import _get from 'lodash/get';
import _property from 'lodash/property';
import SalesOrderPartLineItemReader from './SalesOrderPartLineItem.reader';

const id = _property('id');
const partId = _property('partId');
const binId = _property('binId');
const refPartId = _property('refPartId');
const refId = _property('refId');
const refType = _property('refType');
const stockType = _property('stockType');
const returnQty = returnPartLine => _get(returnPartLine, 'returnQty', 0);
const reason = _property('reason');
const createdTime = _property('createdTime');
const modifiedTime = _property('modifiedTime');
const refundAmount = _property('refundAmount');
const preciseRefundAmount = _property('preciseRefundAmount');
const refundTaxAmount = _property('refundTaxAmount');
const preciseRefundTaxAmount = _property('preciseRefundTaxAmount');
const returnedGoodsType = _property('returnedGoodsType');
const charges = _property('charges');
const temporaryPart = _property('temporaryPart');
const adhocPartInfo = _property('adhocPartInfo');
const invoiceLineId = _property('invoiceLineId');
const taxBreakUp = _property('taxBreakUp');
const taxDetails = _property('taxDetails');
const unusualSale = _property('unusualSale');
const returnAdditional = _property('returnAdditional');
const taxable = _property('taxable');
const listPrice = _property('listPrice');
const printedListPrice = _property('printedListPrice');
const costPrice = _property('costPrice');
const tradePrice = _property('tradePrice');
const sellingPrice = _property('sellingPrice');
const computedSellingPrice = _property('computedSellingPrice');
const initialSellingPrice = _property('initialSellingPrice');
const sourceCodeId = _property('sourceCodeId');
const dealerComment = _property('dealerComment');
const returnAmountPerUnit = _property('returnAmountPerUnit');

export default {
  ...SalesOrderPartLineItemReader,
  id,
  partId,
  binId,
  refPartId,
  refId,
  refType,
  stockType,
  returnQty,
  reason,
  createdTime,
  modifiedTime,
  refundAmount,
  preciseRefundAmount,
  refundTaxAmount,
  preciseRefundTaxAmount,
  returnedGoodsType,
  charges,
  temporaryPart,
  adhocPartInfo,
  invoiceLineId,
  taxBreakUp,
  taxDetails,
  unusualSale,
  returnAdditional,
  taxable,
  listPrice,
  printedListPrice,
  costPrice,
  tradePrice,
  sellingPrice,
  computedSellingPrice,
  initialSellingPrice,
  sourceCodeId,
  dealerComment,
  returnAmountPerUnit,
};
