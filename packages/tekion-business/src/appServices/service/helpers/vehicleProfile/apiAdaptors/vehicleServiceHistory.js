import _map from 'lodash/map';
import _head from 'lodash/head';
import _get from 'lodash/get';
import _find from 'lodash/find';
import _orderBy from 'lodash/orderBy';
import _join from 'lodash/join';
import _reduce from 'lodash/reduce';
import _flatten from 'lodash/flatten';
import _uniq from 'lodash/uniq';
import _isEmpty from 'lodash/isEmpty';
import _size from 'lodash/size';
import _compact from 'lodash/compact';
import _sumBy from 'lodash/sumBy';
import _sortBy from 'lodash/sortBy';
import _isNil from 'lodash/isNil';
import _keyBy from 'lodash/keyBy';
import _flatMap from 'lodash/flatMap';

import PurchaseOrderReader from '@tekion/tekion-base/readers/PurchaseOrder';
import MoneyReader from '@tekion/tekion-base/readers/TMoney';
import UserReader, { EMPLOYEE_DISPLAY_NUMBER_FIELD_NAME } from '@tekion/tekion-base/readers/User';
import { getVendorOptionLabel } from '@tekion/tekion-base/formatters/vendor';
import { tget } from '@tekion/tekion-base/utils/general';
import {
  toMomentWithParserFormat,
  getMomentValueOf,
  convertSecToHours,
  toMoment,
  DATE_TIME_FORMATS,
} from '@tekion/tekion-base/utils/dateUtils';
import { EMPTY_OBJECT, EMPTY_ARRAY, NO_DATA } from '@tekion/tekion-base/app.constants';
import { JOB_TYPES_LABELS, LABOR_RATE_CONFIG } from '@tekion/tekion-base/constants/repairOrder/job';
import { getMilesLabel } from '@tekion/tekion-base/helpers/unit.helper';
import { DATE_TIME_FORMAT } from '@tekion/tekion-conversion-web';

import ROOperationReader from '../../../readers/ROOperation';
import * as JobReader from '../../../readers/Job';
import * as VehicleProfileReader from '../../../readers/vehicleProfile/VehicleProfile';
import ROPartReader from '../../../readers/ROPartAvailability';
import ROReader from '../../../readers/RepairOrder';
import {
  getDealerServiceHistoryJobs,
  getEmployeeDisplayTitle,
  getJobSortCriteria,
  getJobSubletLaborTotal,
  getJobSubletPartsTotal,
  getMilesDisplayName,
  getMilesDisplayString,
  getROAmountBreakup,
  getROTotalAmountFromJobsList,
  getTdpRODetails,
  getSanitizedDynamicTags,
  getServiceHistoryVisibility,
  getLocalizedDateTime,
  getAdaptedPaySplitDetails,
  updateOtherDealerServiceHistoryVisibleStatus,
} from '../general';
import { getStrainedRows } from '../fieldConfig';
import { getOpCodeFromJob } from '../../job';
import { getPOLineNumber } from '../../sublet';
import { getPartsCounterPerson } from '../../partAvailability';
import { getAddedAndResolvedParts } from '../../../../parts/partsROSales/constants/helpers/partsROSales.helper';
import { getOpcodeDisplayValue } from '../../roOperation';
import * as VEHICLE_PROFILE_CONSTANTS from '../../../constants/vehicleProfile/vehicleProfileFields.constants';
import { OEM_SERVICE_TYPES } from '../../../constants/vehicleProfile/general';

export const SUBSCRIPTIONS_KEY = {
  OWNER_CENTER: 'OWNER_CENTER',
  XM_RADIO: 'XM_RADIO',
  DMN: 'DMN',
  ADV_DIAG: 'ADV_DIAG',
  ONSTAR: 'ONSTAR',
  PORSCHE_ROAD_SIDE_ASSISTANCE: 'PORSCHE_ROAD_SIDE_ASSISTANCE',
};

const getLaborInfoObject = laborInfo => ({
  [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_LABOR_OPCODE_FIELD]: VehicleProfileReader.getLaborOpcode(laborInfo),
  [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_OPCODE_DESCRIPTION_FIELD]:
    VehicleProfileReader.getLaborOpcodeDescription(laborInfo),
  [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_LABOR_FLAT_HOURS_FIELD]:
    VehicleProfileReader.getLaborFlatHours(laborInfo),
  [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_LABOR_HOURS_FIELD]: VehicleProfileReader.getLaborHours(laborInfo),
});

const getLaborInfo = getStrainedRows(labor => {
  if (_isNil(labor)) return EMPTY_ARRAY;
  return _map(labor, laborInfo => getLaborInfoObject(laborInfo));
});

const getLaborAndPartInfo = (parts, labor, options) => [
  ..._map(parts, partInfo => ({
    [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_PART_ID_FIELD]: VehicleProfileReader.getPartId(partInfo),
    [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_PART_NAME_FIELD]: VehicleProfileReader.getPartDescription(partInfo),
    [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_PART_QUANTITY_FIELD]: VehicleProfileReader.getPartQuantity(partInfo),
  })),
  ...getLaborInfo(labor, options),
];

const getOEMSubletJobInfo = getStrainedRows(sublet => {
  const subletServiceComponents = VehicleProfileReader.getServiceComponents(sublet);
  const subletSplits = VehicleProfileReader.getSubletSplits(sublet);
  return [
    {
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_SUBLET_CODE_FIELD]: VehicleProfileReader.getSubletCode(sublet),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_SUBLET_WORK_DESCRIPTION_FIELD]:
        VehicleProfileReader.getSubletWorkDescription(sublet),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_MISCELLANOUES_DESCRIPTION_FIELD]: _get(
        _find(subletServiceComponents, { componentTypeCode: 'Miscellaneous' }),
        'description'
      ),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_GOG_DESCRIPTION_FIELD]: _get(
        _find(subletServiceComponents, { componentTypeCode: 'GasOilGrease' }),
        'description'
      ),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_PAINT_DESCRIPTION_FIELD]: _get(
        _find(subletServiceComponents, { componentTypeCode: 'PaintMaterials' }),
        'description'
      ),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_SHOP_SUPPLIES_DESCRIPTION_FIELD]: _get(
        _find(subletServiceComponents, { componentTypeCode: 'ShopSupplies' }),
        'description'
      ),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_FREIGHT_DESCRIPTION_FIELD]: _get(
        _find(subletServiceComponents, { componentTypeCode: 'Freight' }),
        'description'
      ),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_SPLIT_TYPE_CODE_FIELD]: _join(
        _map(subletSplits, VehicleProfileReader.getSubletSplitTypeCode),
        ', '
      ),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_TECHNICIAN_NAME_FIELD]:
        VehicleProfileReader.getSubletTechnicianName(sublet),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_TECHNICIAN_NUMBER_FIELD]:
        VehicleProfileReader.getSubletTechnicianNumber(sublet),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_PRICE_CODE_FIELD]: VehicleProfileReader.getSubletPriceCode(sublet),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_PRICE_DESCRIPTION_FIELD]: _join(
        VehicleProfileReader.getSubletPriceDescription(sublet),
        ', '
      ),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_AUTHORIZATION_NUMBER_FIELD]:
        VehicleProfileReader.getSubletAuthorizationNumber(sublet),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_PART_SPLIT_PERCENTAGE_FIELD]:
        VehicleProfileReader.getSubletSplitPercentage(_head(subletSplits)),
      partInfo: EMPTY_ARRAY,
    },
  ];
});

export const getOEMJobInfo = (jobsList, sublet, options) => [
  ..._map(jobsList, jobInfo => {
    const codesAndComments = VehicleProfileReader.getCodesAndComments(jobInfo);
    return {
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_JOB_FIELD]: VehicleProfileReader.getDmsOperationName(jobInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_JOB_PAY_TYPE_FIELD]: VehicleProfileReader.getJobPayType(jobInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_STANDARD_OPCODE_FIELD]:
        VehicleProfileReader.getDmsOperationId(jobInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_DMS_LABOR_OPCODE_FIELD]:
        VehicleProfileReader.getOperationId(jobInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_DMS_JOB_NUMBER_FIELD]:
        VehicleProfileReader.getDmsJobNumber(jobInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_OPERATION_ID_FIELD]:
        VehicleProfileReader.getDmsOperationId(jobInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_OPERATION_NAME_FIELD]:
        VehicleProfileReader.getDmsOperationName(jobInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_CAUSE_DESCRIPTION_FIELD]:
        VehicleProfileReader.getJobCause(jobInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_COMPLAINT_DESCRIPTION_FIELD]:
        VehicleProfileReader.getJobComplaint(jobInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_CORRECTION_DESCRIPTION_FIELD]:
        VehicleProfileReader.getJobCorrection(jobInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_TECHNICIAN_NOTE_FIELD]:
        VehicleProfileReader.getOemTechnicianNote(jobInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_MISCELLANEOUS_NOTE_FIELD]:
        VehicleProfileReader.getOemMiscNotes(codesAndComments),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_JOB_DENIAL_DESCRIPTION_FIELD]:
        VehicleProfileReader.getOemJobDenialDescription(jobInfo),
      partInfo: getLaborAndPartInfo(
        VehicleProfileReader.getPartList(jobInfo),
        VehicleProfileReader.getLaborHistory(jobInfo),
        options
      ),
    };
  }),
  ...getOEMSubletJobInfo(sublet, options),
];

export const getDealerJobInfo = (jobsList, userList, options, getFormattedCurrency) =>
  _map(jobsList, jobInfo => {
    const {
      id: technicianId,
      familyName: technicianFamilyName,
      givenName: technicianGivenName,
    } = VehicleProfileReader.getTechnician(jobInfo);
    return {
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_JOB_LINE_FIELD]: VehicleProfileReader.getDmsJobNumber(jobInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_OPCODE_FIELD]: VehicleProfileReader.getDmsOperationId(jobInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_OPERATION_DESCRIPTION_FIELD]:
        VehicleProfileReader.getDmsOperationName(jobInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_JOB_CAUSE_FIELD]: VehicleProfileReader.getJobCause(jobInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_JOB_COMPLAINT_FIELD]:
        VehicleProfileReader.getJobComplaint(jobInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_JOB_CORRECTION_FIELD]:
        VehicleProfileReader.getJobCorrection(jobInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_TECHNICIAN_NAME_FIELD]: getEmployeeDisplayTitle({
        userList,
        employeeId: technicianId,
        options: { ...options, showEmployeeNumberOnly: options.showTechnicianEmployeeNumberOnly, isOpenRO: false },
        employeeName: `${technicianGivenName} ${technicianFamilyName}`,
      }),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_JOB_PAY_TYPE_FIELD]:
        VehicleProfileReader.getJobPayType(jobInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_TOTAL_PRICE_FIELD]: getFormattedCurrency(
        VehicleProfileReader.getTotalPrice(jobInfo)
      ),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_JOB_HOURS]: VehicleProfileReader.getJobHours(jobInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_LABOR_SALE_AMOUNT]: getFormattedCurrency(
        VehicleProfileReader.getJobLaborSaleAmount(jobInfo)
      ),
    };
  });

const getThirdPartyOperationInfo = ({ operations, getFormattedCurrency }) =>
  _map(operations, operation => ({
    [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_MISCELLANEOUS_SALE_AMOUNT]: getFormattedCurrency(
      MoneyReader.value(VehicleProfileReader.getMiscSaleAmount(operation))
    ),
    [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_MISCELLANEOUS_COST_AMOUNT]: getFormattedCurrency(
      MoneyReader.value(VehicleProfileReader.getMiscCostAmount(operation))
    ),
    [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_PARTS_COST_AMOUNT]: getFormattedCurrency(
      MoneyReader.value(VehicleProfileReader.getPartCostAmount(operation))
    ),
    [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_LABOR_COST_AMOUNT]: getFormattedCurrency(
      MoneyReader.value(VehicleProfileReader.getLaborCostAmount(operation))
    ),
    [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_TIME_CARD_HOURS]:
      VehicleProfileReader.getTimecardHours(operation),
    [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_SOLD_HOURS]: VehicleProfileReader.getSoldHours(operation),
    [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_ACTUAL_HOURS]: VehicleProfileReader.getActualHours(operation),
    [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_LABOR_SALE_AMOUNT]: getFormattedCurrency(
      MoneyReader.value(VehicleProfileReader.getLaborSaleAmount(operation))
    ),
    [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_PARTS_SALES_AMOUNT]: getFormattedCurrency(
      MoneyReader.value(VehicleProfileReader.getPartSaleAmount(operation))
    ),
  }));

const getThirdPartyJobInfo = ({ jobList, userList, options, getFormattedCurrency }) =>
  _map(jobList, jobInfo => {
    const {
      id: serviceAdvisorId,
      givenName: technicianFirstName,
      familyName: technicianLastName,
    } = VehicleProfileReader.getTechnician(jobInfo);
    return {
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_JOB_LINE_FIELD]: VehicleProfileReader.getDmsJobNumber(jobInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_OPERATION_DESCRIPTION_FIELD]:
        VehicleProfileReader.getDmsOperationId(jobInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_LABOR_TYPE]:
        VehicleProfileReader.getInProgressROPayType(jobInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_JOB_TECHNICIANS_FIELD]: [
        getEmployeeDisplayTitle({
          userList,
          employeeId: serviceAdvisorId,
          options: { ...options, showEmployeeNumberOnly: options?.showAdvisorEmployeeNumberOnly, isOpenRO: false },
          employeeName: _join(_compact([technicianFirstName, technicianLastName]), ' '),
        }),
      ],
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_OPERATIONS_INFO]: getThirdPartyOperationInfo({
        operations: VehicleProfileReader.getLaborHistory(jobInfo),
        getFormattedCurrency,
      }),
    };
  });

const getPartsCounterPersonId = part =>
  ROPartReader.fulfiledBy(part) || ROPartReader.resolvedBy(part) || ROPartReader.claimedBy(part);

const getDealerOperationPartsInfo = (parts, getFormattedCurrency) =>
  _map(getAddedAndResolvedParts(parts), part => {
    const counterPerson = getPartsCounterPerson(part);
    const totalPrice = ROPartReader.totalPrice(part) || ROPartReader.totalSaleAmount(part);
    return {
      ...part,
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_PART]: ROPartReader.resolvedPartName(part) || undefined,
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_QUANTITY]: ROPartReader.approvedQuantity(part) || undefined,
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_UNIT_PRICE]: getFormattedCurrency(
        MoneyReader.value(ROPartReader.resolvedUnitPrice(part))
      ),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_TOTAL_PRICE]: getFormattedCurrency(
        MoneyReader.value(totalPrice)
      ),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_COUNTER_PERSON]: !_isEmpty(counterPerson)
        ? UserReader.name(counterPerson)
        : getPartsCounterPersonId(part),
    };
  });

const getDealerOperationInfo = (operations, laborRates, getFormattedCurrency) =>
  _map(operations, operation => {
    const laborTimeInSeconds = ROOperationReader.laborTimeInSeconds(operation);
    const laborRateDetails =
      _find(laborRates, [LABOR_RATE_CONFIG.value, ROOperationReader.laborRateType(operation)]) ||
      _find(laborRates, [LABOR_RATE_CONFIG.value, ROOperationReader.laborRateId(operation)]);
    return {
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_OPERATION_CODE]: getOpcodeDisplayValue(operation),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_OPERATION_DESCRIPTION]:
        ROOperationReader.opcodeDescription(operation),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_LABOR_HOURS]: laborTimeInSeconds
        ? convertSecToHours(laborTimeInSeconds)
        : undefined,
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_LABOR_RATE]: _get(laborRateDetails, 'displayName'),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_BILL_RATE]: ROOperationReader.billRate(operation),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_BILL_HRS]: ROOperationReader.billHrs(operation),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_LABOR_PRICE]: getFormattedCurrency(
        MoneyReader.value(ROOperationReader.laborAmount(operation))
      ),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_STORY_LINE]: _join(ROOperationReader.storyLines(operation)),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_PARTS_TABLE]: getDealerOperationPartsInfo(
        ROOperationReader.parts(operation),
        getFormattedCurrency
      ),
    };
  });

const getConcatenatedStoryLine = operation => _join(_map(ROOperationReader.storyLines(operation), 'text'), ', ');

export const getAllStoryLineInOperations = operations =>
  _reduce(operations, (acc, operation) => [...acc, getConcatenatedStoryLine(operation)], EMPTY_ARRAY);

export const getConcatenatedStoryLineInOperations = operations =>
  _reduce(operations, (acc, operation) => [...acc, _join(ROOperationReader.storyLines(operation), ', ')], EMPTY_ARRAY);

const getFormattedJobDetails = jobDetails =>
  _flatMap(jobDetails, job => {
    const {
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_OPERATIONS_INFO]: operationLevelData,
      ...restJobDetails
    } = job;
    return _map(operationLevelData, operationInfo => ({
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_OPERATIONS_INFO]: operationLevelData,
      ...operationInfo,
      ...restJobDetails,
    }));
  });

export const getFetchedDealerJobInfo = (
  jobsList,
  roId,
  laborRates,
  mpviConfig,
  dealerId,
  isNewMpviEnabled,
  getFormattedCurrency,
  isCockpitDetailsViewEnabled,
  getFormattedNumber
) => {
  const jobsData = _map(jobsList, jobInfo => {
    const operations = JobReader.getOperations(jobInfo);
    const subletInfos = JobReader.getSubletJobInfos(jobInfo);
    const subletLaborTotal = getJobSubletLaborTotal(subletInfos);
    const subletPartsTotal = getJobSubletPartsTotal(subletInfos);
    const retractedHours = JobReader.getTotalClockedTimeInHours(jobInfo, getFormattedNumber);
    const jobPayType = JobReader.getPayType(jobInfo);
    const jobVoidReason = JobReader.getVoidReason(jobInfo);
    const paySplitTotalPrice = JobReader.getJobAmountByPayType(jobInfo, jobPayType);
    const jobType = JobReader.getType(jobInfo);
    return {
      roId,
      jobType,
      mpviConfig: isNewMpviEnabled ? tget(mpviConfig, ROOperationReader.jobId(jobInfo), {}) : mpviConfig,
      dealerId,
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_JOB_LINE_FIELD]: JobReader.getJobNumber(jobInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_STORY_LINE_FIELD]:
        getConcatenatedStoryLineInOperations(operations),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_JOB_CAUSE_FIELD]: _join(JobReader.getCauses(jobInfo), ', '),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_JOB_CONCERN_FIELD]: JobReader.getConcern(jobInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_CONCERN_TYPE_FIELD]: JobReader.getConcernType(jobInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_JOB_PAY_TYPE_FIELD]: jobPayType,
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_TOTAL_PRICE_FIELD]: getFormattedCurrency(paySplitTotalPrice),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_JOB_HOURS]: convertSecToHours(
        _sumBy(operations, ROOperationReader.laborTimeInSeconds)
      ),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_JOB_TYPE_FIELD]: JOB_TYPES_LABELS[jobType] || undefined,
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_JOB_TECHNICIANS_FIELD]:
        _get(jobInfo, 'technicians') || _compact(_map(JobReader.getTechnicians(jobInfo), 'name')),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_NUMBER_OF_OPERATIONS]: _size(operations),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_SUBLET_VENDOR]:
        _join(
          _map(subletInfos, subletInfo =>
            getVendorOptionLabel({ data: PurchaseOrderReader.vendorDetails(subletInfo) })
          ),
          ', '
        ) || undefined,
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_PO_ID]:
        _join(_map(subletInfos, PurchaseOrderReader.poNumber), ', ') || undefined,
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_PO_LINE_ID]:
        _join(_map(subletInfos, getPOLineNumber), ', ') || undefined,
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_SUBLET_TYPE]:
        _join(_map(subletInfos, PurchaseOrderReader.subletType), ', ') || undefined,
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_SUBLET_LABOR]: subletLaborTotal
        ? getFormattedCurrency(subletLaborTotal)
        : undefined,
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_SUBLET_PARTS]: subletPartsTotal
        ? getFormattedCurrency(subletPartsTotal)
        : undefined,
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_REASON_FOR_VOIDING_JOB]: jobVoidReason || undefined,
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_RETRACTED_ACTUAL_HOURS]: jobVoidReason
        ? retractedHours
        : undefined,
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_OPERATIONS_INFO]: getDealerOperationInfo(
        operations,
        laborRates,
        getFormattedCurrency
      ),
    };
  });
  return jobsData;
};

const getOpenROTechnicianDetails = (openRODetails, userList, options = EMPTY_OBJECT) => {
  const operations = VehicleProfileReader.getOperations(openRODetails);
  return _join(
    _uniq(
      _flatten(
        _map(operations, operation => {
          const technicianDetails = VehicleProfileReader.getTechnicianDetails(openRODetails, operation);
          return _map(technicianDetails, user =>
            getEmployeeDisplayTitle({
              userList,
              employeeId: UserReader.userId(user),
              options: { ...options, showEmployeeNumberOnly: options.showTechnicianEmployeeNumberOnly, isOpenRO: true },
              employeeName: UserReader.displayName(user),
            })
          );
        })
      )
    ),
    ', '
  );
};

const getOpenRODetails = (openROList, userList, options = EMPTY_OBJECT, getFormattedDateAndTime) =>
  _map(openROList, openRODetails => ({
    ...openRODetails,
    [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_RO_ID_FIELD]: VehicleProfileReader.getRONumber(openRODetails),
    roCreatedTime: getLocalizedDateTime({
      formatter: getFormattedDateAndTime,
      value: VehicleProfileReader.getROCreatedDate(openRODetails),
      timezone: 'UTC',
    }),
    [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_SERVICE_ADVISOR_FIELD]: getEmployeeDisplayTitle({
      userList,
      employeeId: VehicleProfileReader.getPrimaryAdvisorId(openRODetails),
      options: { ...options, showEmployeeNumberOnly: options.showAdvisorEmployeeNumberOnly, isOpenRO: true },
      employeeName: UserReader.displayName(VehicleProfileReader.getServiceAdvisorDetails(openRODetails)),
    }),
    [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_MILEAGE_IN_FIELD]: getMilesDisplayString(
      VehicleProfileReader.getMileageIn(openRODetails),
      getMilesLabel()
    ),
    [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_TECHNICIAN_NAME_FIELD]: getOpenROTechnicianDetails(
      openRODetails,
      userList,
      options
    ),
    [VEHICLE_PROFILE_CONSTANTS.JOB_INFO]: [
      {
        [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_OPERATION_DESCRIPTION_FIELD]:
          VehicleProfileReader.getDealerJobDescription(openRODetails),
        [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_OPCODE_FIELD]: getOpCodeFromJob(openRODetails),
        [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_JOB_PAY_TYPE_FIELD]:
          VehicleProfileReader.getInProgressROPayType(openRODetails),
        [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_JOB_HOURS]: convertSecToHours(
          _reduce(
            VehicleProfileReader.getOperations(openRODetails),
            (result, operation) => result + ROOperationReader.billingTimeInSeconds(operation),
            0
          )
        ),
      },
    ],
  }));

export const getSegregatedServiceHistory = (serviceHistoryList, isCCCServiceHistoryEnabled) =>
  _reduce(
    serviceHistoryList,
    (acc, serviceHistory) => {
      if (VehicleProfileReader.getServiceSource(serviceHistory) === VEHICLE_PROFILE_CONSTANTS.SOURCE_TYPE_TDP)
        return { ...acc, dealerServiceHistory: [...acc.dealerServiceHistory, serviceHistory] };
      if (
        isCCCServiceHistoryEnabled &&
        VehicleProfileReader.getDepartmentId(serviceHistory) === VEHICLE_PROFILE_CONSTANTS.DEPARTMENT_CCC
      )
        return { ...acc, thirdPartyServiceHistory: [...acc.thirdPartyServiceHistory, serviceHistory] };
      return { ...acc, oemServiceHistory: [...acc.oemServiceHistory, serviceHistory] };
    },
    { dealerServiceHistory: [], oemServiceHistory: [], thirdPartyServiceHistory: [] }
  );

export const sortServiceHistoryBasedonDate = (serviceHistory, dateTimeReader) =>
  _orderBy(
    serviceHistory,
    serviceHistoryInfo =>
      getMomentValueOf(toMomentWithParserFormat(dateTimeReader(serviceHistoryInfo), DATE_TIME_FORMATS.ISO_8601_DATE)),
    ['desc']
  );

export const getSortedROs = allROs =>
  _orderBy(
    allROs,
    groupedRO => {
      const arcRoDetails = _get(groupedRO, 'arc.repairOrderDetail.ro') || _get(groupedRO, 'arc', EMPTY_OBJECT);
      return (
        ROReader.getClosedTime(arcRoDetails) ||
        getMomentValueOf(
          toMomentWithParserFormat(
            VehicleProfileReader.getServiceOutDate(getTdpRODetails(groupedRO)),
            DATE_TIME_FORMATS.ISO_8601_DATE
          )
        )
      );
    },
    ['desc']
  );

export const getROLevelPayTypeFromServiceInfo = serviceInfo => {
  const payTypesFromJobs = _uniq(
    _compact(_map(VehicleProfileReader.getJobInfo(serviceInfo), VehicleProfileReader.getJobPayType))
  );
  return _isEmpty(payTypesFromJobs)
    ? VehicleProfileReader.getServiceTypeCode(serviceInfo)
    : _join(payTypesFromJobs, ', ');
};

const getServiceCenter = serviceInfo =>
  _join(
    _compact([
      VehicleProfileReader.getServiceCenterCode(serviceInfo),
      VehicleProfileReader.getServiceLocation(serviceInfo),
    ]),
    ', '
  );

const getFirstJobDescription = jobInfo =>
  _get(_head(jobInfo), VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_JOB_FIELD, NO_DATA);

const getSanitizedOEMHistory = (oemServiceHistory, options, getFormattedDateAndTime) =>
  _map(oemServiceHistory, serviceInfo => {
    const jobsList = VehicleProfileReader.getJobInfo(serviceInfo);
    const sublet = VehicleProfileReader.getSubletInfo(serviceInfo);
    const { id: serviceAdvisorId, firstName, lastName } = VehicleProfileReader.getServiceAdvisor(serviceInfo);
    const jobInfo = getOEMJobInfo(jobsList, sublet, options);
    const roCompletionDate = VehicleProfileReader.getServiceOutDate(serviceInfo);
    return {
      [VEHICLE_PROFILE_CONSTANTS.IS_DEALER_SERVICE_HISTORY]: false,
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_RO_ID_FIELD]: VehicleProfileReader.getServiceId(serviceInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_COMPLETION_DATE_FIELD]: getLocalizedDateTime({
        formatter: getFormattedDateAndTime,
        value: roCompletionDate,
      }),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_RO_OPEN_DATE_FIELD]: getLocalizedDateTime({
        formatter: getFormattedDateAndTime,
        value: VehicleProfileReader.getRoOpenDate(serviceInfo),
      }),
      [VEHICLE_PROFILE_CONSTANTS.RO_COMPLETION_DATE_RAW]: roCompletionDate,
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_SERVICE_CENTER_FIELD]: getServiceCenter(serviceInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_SERVICE_LOCATION_FIELD]:
        VehicleProfileReader.getServiceLocationId(serviceInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_DESCRIPTION_FIELD]: getFirstJobDescription(jobInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_JOB_COUNT_FIELD]: _size(jobsList) || NO_DATA,
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_RO_PAY_TYPE_FIELD]: getROLevelPayTypeFromServiceInfo(serviceInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_SERVICE_ADVISOR_ID_FIELD]: serviceAdvisorId,
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_SERVICE_ADVISOR_FIELD]: `${firstName} ${lastName}`,
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_RO_COMMENT_FIELD]:
        VehicleProfileReader.getOemServiceComments(serviceInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_REPAIR_ORDER_INTERNAL_REMARKS_FIELD]:
        VehicleProfileReader.getOemServiceInternalRemarks(serviceInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_DEALER_CODE_FIELD]:
        VehicleProfileReader.getOemDealerCode(serviceInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_DEALER_NAME_FIELD]:
        VehicleProfileReader.getOemDealerName(serviceInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_ODOMETER_IN_FIELD]: getMilesDisplayName(
        VehicleProfileReader.getOdoMeterInValue(serviceInfo) || EMPTY_OBJECT
      ),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_ODOMETER_OUT_FIELD]: getMilesDisplayName(
        VehicleProfileReader.getOdoMeterOutValue(serviceInfo) || EMPTY_OBJECT
      ),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_REPEAT_REPAIR_FLAG_FIELD]:
        VehicleProfileReader.getRepeatRepairFlag(serviceInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_TRANSACTION_TYPE_FIELD]:
        VehicleProfileReader.getOemServiceTransactionType(serviceInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_WARRANTY_CLAIM_NUMBER_FIELD]:
        VehicleProfileReader.getWarrantyClaimNumber(serviceInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_CLAIM_TYPE_FIELD]: VehicleProfileReader.getClaimType(serviceInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_CLAIM_STATUS_FIELD]:
        VehicleProfileReader.getClaimStatus(serviceInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_AUTH_CODE_FIELD]: VehicleProfileReader.getAuthCode(serviceInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_ENTRY_DATE_FIELD]: getLocalizedDateTime({
        formatter: getFormattedDateAndTime,
        value: VehicleProfileReader.getEntryDate(serviceInfo),
      }),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_PROC_DATE_FIELD]: getLocalizedDateTime({
        formatter: getFormattedDateAndTime,
        value: VehicleProfileReader.getProcDate(serviceInfo),
      }),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_POSTING_DATE_FIELD]: getLocalizedDateTime({
        formatter: getFormattedDateAndTime,
        value: VehicleProfileReader.getPostingDate(serviceInfo),
      }),
      [VEHICLE_PROFILE_CONSTANTS.JOB_INFO]: jobInfo,
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_CONDITION_CODE_FIELD]:
        VehicleProfileReader.getServiceConditionCode(serviceInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_CONDITION_DESCRIPTION_FIELD]:
        VehicleProfileReader.getServiceConditionDescription(serviceInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_OPEN_DATE_FIELD]: getLocalizedDateTime({
        formatter: getFormattedDateAndTime,
        value: VehicleProfileReader.getServiceOpenDate(serviceInfo),
      }),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_WARRANTY_CLAIM_TYPE_FIELD]:
        VehicleProfileReader.getWarrantyClaimType(serviceInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_SERVICE_TYPE_FIELD]:
        VehicleProfileReader.getOemServiceType(serviceInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISOTRY_OEM_INVOICE_DATE_FIELD]:
        VehicleProfileReader.getInvoiceDate(serviceInfo),
    };
  });

const SERVICE_TVPE_VS_DATA_MAPPER = {
  [OEM_SERVICE_TYPES.FIXED_PRICE]: serviceInfo =>
    _map(serviceInfo, ({ laborList = EMPTY_ARRAY, partList = EMPTY_ARRAY }) => ({
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_LABOR_TYPE_FIELD]: __('Fixed Price'),
      // Nesting Key
      [VEHICLE_PROFILE_CONSTANTS.JOB_INFO]: [
        ..._map(laborList, laborLine => ({
          [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_LABOR_OPCODE_FIELD]:
            VehicleProfileReader.getLaborOpcode(laborLine),
          [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_LABOR_FLAT_HOURS_FIELD]:
            VehicleProfileReader.getLaborHours(laborLine),
          [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_LABOR_TYPE_FIELD]: __('Labor'),
        })),

        ..._map(partList, partLine => ({
          [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_PART_ID_FIELD]: VehicleProfileReader.getPartId(partLine),
          [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_PART_NAME_FIELD]:
            VehicleProfileReader.getPartDescription(partLine),
          [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_PART_QUANTITY_FIELD]:
            VehicleProfileReader.getPartQuantity(partLine),
          [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_LABOR_TYPE_FIELD]: __('Parts'),
        })),
      ],
    })),
  DEFAULT: (serviceInfo, serviceType) =>
    _map(serviceInfo, data => ({
      isExpanderHidden: true,
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_LABOR_OPCODE_FIELD]: VehicleProfileReader.getLaborOpcode(data),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_LABOR_FLAT_HOURS_FIELD]: VehicleProfileReader.getLaborHours(data),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_PART_ID_FIELD]: VehicleProfileReader.getPartId(data),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_PART_NAME_FIELD]: VehicleProfileReader.getPartDescription(data),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_PART_QUANTITY_FIELD]: VehicleProfileReader.getPartQuantity(data),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_LABOR_TYPE_FIELD]:
        serviceType === OEM_SERVICE_TYPES.LABOR ? __('Labor') : __('Parts'),
    })),
};

const getOEMMaintenanceJobInfo = (laborHistory, partsHistory, fixedHistory) =>
  _flatMap(
    {
      [OEM_SERVICE_TYPES.LABOR]: laborHistory,
      [OEM_SERVICE_TYPES.PARTS]: partsHistory,
      [OEM_SERVICE_TYPES.FIXED_PRICE]: fixedHistory,
    },
    (services, serviceType) => {
      const dataAdaptor = SERVICE_TVPE_VS_DATA_MAPPER[serviceType] ?? SERVICE_TVPE_VS_DATA_MAPPER.DEFAULT;
      return dataAdaptor(services, serviceType);
    }
  );

export const getSanitizedMaintenanceHistory = (oemHistory, getFormattedDateAndTime, isServiceMaintenanceTab) => {
  const serviceMaintenceHistory = _map(oemHistory, maintenanceInfo => {
    const jobHistory = _head(VehicleProfileReader.getJobInfo(maintenanceInfo)) || EMPTY_OBJECT;
    const { serviceLaborHistory, servicePartsHistory, serviceForfaitHistory } = jobHistory;
    return {
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_OPERATION_ID]: VehicleProfileReader.getDmsOperationId(jobHistory),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_COMPLETION_DATE_FIELD]: getLocalizedDateTime({
        formatter: getFormattedDateAndTime,
        value: VehicleProfileReader.getServiceOutDate(maintenanceInfo),
      }),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_ODOMETER_OUT_FIELD]: getMilesDisplayName(
        VehicleProfileReader.getOdoMeterOutValue(maintenanceInfo) || EMPTY_OBJECT
      ),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_CATEGORY_FIELD]:
        VehicleProfileReader.getOemServiceTransactionType(maintenanceInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_CATEGORY_LABEL_FIELD]:
        VehicleProfileReader.getDmsOperationName(jobHistory),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_SYMPTOM_FIELD]: VehicleProfileReader.getSymptom(maintenanceInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_CUSTOMER_COMPLAINT_FIELD]:
        VehicleProfileReader.getOemServiceComments(maintenanceInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_RO_COMMENT_FIELD]:
        VehicleProfileReader.getServiceItemDescription(maintenanceInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM_CUSTOMER_FEEDBACK_FIELD]:
        VehicleProfileReader.getCustomerFeedback(maintenanceInfo),
      [VEHICLE_PROFILE_CONSTANTS.JOB_INFO]: getOEMMaintenanceJobInfo(
        serviceLaborHistory,
        servicePartsHistory,
        serviceForfaitHistory
      ),
    };
  });
  return isServiceMaintenanceTab
    ? {
        [VEHICLE_PROFILE_CONSTANTS.SERVICE_MAINTENANCE_TABLE]: serviceMaintenceHistory,
      }
    : serviceMaintenceHistory;
};

export const getOEMServiceHistory = ({
  oemServiceHistory,
  options,
  getFormattedDateAndTime,
  isServiceMaintenanceEnabled,
}) => ({
  [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_OEM]: isServiceMaintenanceEnabled
    ? getSanitizedMaintenanceHistory(oemServiceHistory, getFormattedDateAndTime) // Specific to RRG
    : getSanitizedOEMHistory(oemServiceHistory, options, getFormattedDateAndTime),
});

const getDealershipName = (tenantDealerDetailsByDealerId, siteId, dealerId) => {
  const currentDealerDetails = tenantDealerDetailsByDealerId?.[dealerId];
  if (!currentDealerDetails) return NO_DATA;
  const dealerName = VehicleProfileReader.getDealershipName(currentDealerDetails);
  if (_isEmpty(currentDealerDetails?.oemSites)) return dealerName;
  const currentSiteName = _get(_find(currentDealerDetails?.oemSites, ['siteId', siteId]), 'name');
  return _join(_compact([dealerName, currentSiteName]), ' - ');
};

const getAdaptedDealerServiceHistory = ({
  sortedROs,
  userList,
  options,
  departmentMap,
  laborRates,
  currentDealerId,
  showOtherDealershipServiceHistory,
  tenantDealerDetails,
  offering,
  locale,
  getFormattedDateAndTime,
  getFormattedCurrency,
}) =>
  _map(sortedROs, serviceInfo => {
    const { repairOrderDetail, migrated: isMigratedRO, ...arcData } = tget(serviceInfo, 'arc', EMPTY_OBJECT);
    const arcRODetails = _get(repairOrderDetail, 'ro', arcData);
    const repairOrderJobs = ROReader.getROJobs(repairOrderDetail);
    const arcJobs = _isEmpty(repairOrderJobs) ? _get(arcData, 'jobs') : repairOrderJobs;
    const repairOrderJobParts = _get(repairOrderDetail, 'jobParts');
    const jobParts = _isEmpty(repairOrderJobParts) ? _get(arcData, 'jobParts') : repairOrderJobParts;
    const jobSummaries = _get(arcData, 'jobSummaries');
    const tdpRODetails = getTdpRODetails(serviceInfo);
    const tdpJobs = VehicleProfileReader.getJobInfo(tdpRODetails);
    const roId = ROReader.getId(arcRODetails).roId || VehicleProfileReader.getROId(tdpRODetails);
    const roNo = ROReader.getRONo(arcRODetails) || VehicleProfileReader.getServiceId(tdpRODetails);
    const serviceAdvisorFromARC = ROReader.getPrimaryServiceAdvisorUser(arcRODetails);
    const techniciansFromARC = ROReader.getTechnicians(arcRODetails).technicianList;
    const {
      id: serviceAdvisorId,
      firstName: serviceAdvisorFirstName,
      lastName: serviceAdvisorLastName,
    } = !_isEmpty(serviceAdvisorFromARC)
      ? {
          id: tget(serviceAdvisorFromARC, EMPLOYEE_DISPLAY_NUMBER_FIELD_NAME),
          firstName: tget(serviceAdvisorFromARC, 'fname'),
          lastName: tget(serviceAdvisorFromARC, 'lname'),
        }
      : VehicleProfileReader.getServiceAdvisor(tdpRODetails);
    const technicians = !_isEmpty(techniciansFromARC)
      ? _map(techniciansFromARC, 'displayName')
      : _map(VehicleProfileReader.getTechnicians(tdpRODetails), 'name');
    const dealerId = ROReader.getDealerId(arcRODetails) || VehicleProfileReader.getDealerId(tdpRODetails);
    const roCompletionDate =
      ROReader.getClosedTime(arcRODetails) || VehicleProfileReader.getServiceOutDate(tdpRODetails);
    const isVisible = getServiceHistoryVisibility(showOtherDealershipServiceHistory, currentDealerId, dealerId);
    const hasTotalPriceBreakdown = !isMigratedRO && !_isEmpty(arcRODetails);
    const roAmountBreakup = hasTotalPriceBreakdown ? getROAmountBreakup(arcRODetails) : EMPTY_OBJECT;
    const roTaxConfigDetails = ROReader.getTaxLabelConfigs(arcRODetails);
    const jobs = getDealerServiceHistoryJobs(tdpJobs, arcJobs, isMigratedRO);
    const tenantDealerDetailsByDealerId = _keyBy(tenantDealerDetails, 'dealerId');
    const siteId = ROReader.getSiteId(arcRODetails);
    const { totalCpAmount, totalIpAmount, totalWpAmount } = getAdaptedPaySplitDetails(arcRODetails);

    return {
      id: roId, // for checkbox in table
      isMigratedRO, // flag to disable checkbox for invoice pdf
      roId,
      laborRates,
      jobs: _sortBy(jobs, getJobSortCriteria),
      jobSummaries,
      jobParts,
      dealerId,
      isVisible,
      [VEHICLE_PROFILE_CONSTANTS.IS_DEALER_SERVICE_HISTORY]: true,
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_RO_ID_FIELD]: roNo,
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_RO_NUMBER_FIELD]: {
        roNo,
        roId,
        dealerId,
        offering,
        siteId,
      },
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_COMPLETION_DATE_FIELD]: getLocalizedDateTime({
        formatter: getFormattedDateAndTime,
        value: roCompletionDate,
      }),
      [VEHICLE_PROFILE_CONSTANTS.RO_COMPLETION_DATE_RAW]: roCompletionDate,
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_SERVICE_ADVISOR_FIELD]: getEmployeeDisplayTitle({
        userList,
        employeeId: serviceAdvisorId,
        options: { ...options, showEmployeeNumberOnly: options.showAdvisorEmployeeNumberOnly, isOpenRO: false },
        employeeName: _join(_compact([serviceAdvisorFirstName, serviceAdvisorLastName]), ' '),
      }),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_MILEAGE_IN_FIELD]: ROReader.getVehicleMileage(arcRODetails)
        ? getMilesDisplayName(
            {
              content: ROReader.getVehicleMileage(arcRODetails),
              unitCode: getMilesLabel(),
            },
            locale
          )
        : getMilesDisplayName(VehicleProfileReader.getOdoMeterInValue(tdpRODetails) || EMPTY_OBJECT, locale),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_DEPARTMENT_FIELD]:
        _get(departmentMap, [ROReader.departmentId(arcRODetails), 'name']) ||
        _get(departmentMap, [VehicleProfileReader.getDepartmentId(tdpRODetails), 'name']),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_RO_TYPE_FIELD]: VehicleProfileReader.getROType(tdpRODetails), // No ARC equivalent
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_CUSTOMER_NAME_FIELD]: ROReader.getCustomerInfo(arcRODetails), // No TDP Equivalent
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_DEALERSHIP_FIELD]: getDealershipName(
        tenantDealerDetailsByDealerId,
        siteId,
        dealerId
      ), // No ARC equivalent
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_SITE_ID]: siteId,
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_RO_TECHNICIANS_FIELD]: _compact(technicians),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_NUMBER_OF_JOBS_FIELD]:
        _size(jobs) || ROReader.getJobCount(arcRODetails),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_RO_TOTAL_FIELD]: {
        hasTotalPriceBreakdown,
        totalAmount: hasTotalPriceBreakdown
          ? tget(roAmountBreakup, 'actualAmount', '0')
          : getROTotalAmountFromJobsList(tdpJobs),
        priceBreakups: roAmountBreakup,
        roTaxConfigDetails,
      },
      [VEHICLE_PROFILE_CONSTANTS.JOB_INFO]: getDealerJobInfo(tdpJobs, userList, options, getFormattedCurrency),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_RO_TAGS_FIELD]: getSanitizedDynamicTags(
        ROReader.getDynamicTags(arcRODetails)
      ), // No TDP Equivalent
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_CP_TOTAL]: { totalAmount: MoneyReader.value(totalCpAmount) }, // No TDP Equivalent
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_W_TOTAL]: { totalAmount: MoneyReader.value(totalWpAmount) }, // No TDP Equivalent
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_I_TOTAL]: { totalAmount: MoneyReader.value(totalIpAmount) }, // No TDP Equivalent
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_OPEN_DATE_FIELD]: getLocalizedDateTime({
        formatter: getFormattedDateAndTime,
        value: ROReader.getRepairOrderCreatedTime(arcRODetails),
      }),
    };
  });

const getAdaptedThirdPartyServiceHistory = ({
  thirdPartyServiceHistory,
  userList,
  options,
  currentDealerId,
  showOtherDealershipServiceHistory,
  getFormattedDateAndTime,
  getFormattedCurrency,
}) =>
  _map(thirdPartyServiceHistory, serviceInfo => {
    const {
      id: serviceAdvisorId,
      firstName: serviceAdvisorFirstName,
      lastName: serviceAdvisorLastName,
    } = VehicleProfileReader.getServiceAdvisor(serviceInfo);
    const dealerId = VehicleProfileReader.getDealerId(serviceInfo);
    return {
      ...serviceInfo,
      isThirdPartyService: true,
      isVisible: getServiceHistoryVisibility(showOtherDealershipServiceHistory, dealerId, currentDealerId),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_RO_NUMBER_FIELD]: {
        roNo: VehicleProfileReader.getServiceId(serviceInfo),
      },
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_SERVICE_ADVISOR_FIELD]: getEmployeeDisplayTitle({
        userList,
        employeeId: serviceAdvisorId,
        options: { ...options, showEmployeeNumberOnly: options?.showAdvisorEmployeeNumberOnly, isOpenRO: false },
        employeeName: _join(_compact([serviceAdvisorFirstName, serviceAdvisorLastName]), ' '),
      }),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_OPEN_DATE_FIELD]: getLocalizedDateTime({
        formatter: getFormattedDateAndTime,
        value: VehicleProfileReader.getServiceInDate(serviceInfo),
      }),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_OPEN_TIME_FIELD]: getLocalizedDateTime({
        formatter: getFormattedDateAndTime,
        value: VehicleProfileReader.getRepairOrderOpenedTime(serviceInfo),
        formatType: DATE_TIME_FORMAT.HOUR_MINUTE,
      }),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_COMPLETION_DATE_FIELD]: getLocalizedDateTime({
        formatter: getFormattedDateAndTime,
        value: VehicleProfileReader.getServiceOutDate(serviceInfo),
      }),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER.SERVICE_HISTORY_DEALER_CLOSE_TIME_FIELD]: getLocalizedDateTime({
        formatter: getFormattedDateAndTime,
        value: VehicleProfileReader.getRepairOrderCompletedTime(serviceInfo),
        formatType: DATE_TIME_FORMAT.HOUR_MINUTE,
      }),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_CUSTOMER_NUMBER]:
        VehicleProfileReader.getCustomerNumber(serviceInfo),
      [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER_RO_TAGS_FIELD]: getSanitizedDynamicTags(),
      [VEHICLE_PROFILE_CONSTANTS.JOB_INFO]: getThirdPartyJobInfo({
        jobList: VehicleProfileReader.getJobInfo(serviceInfo),
        userList,
        options,
        getFormattedCurrency,
      }),
    };
  });

export const getDealerServiceHistory = (
  sortedROs,
  openROList,
  userList,
  options,
  departmentMap,
  { laborRates, currentDealerId, showOtherDealershipServiceHistory, tenantDealerDetails } = EMPTY_OBJECT,
  offering,
  locale,
  getFormattedDateAndTime,
  getFormattedCurrency,
  thirdPartyServiceHistory
) => {
  const dealerServiceHistory = getAdaptedDealerServiceHistory({
    sortedROs,
    userList,
    options,
    departmentMap,
    laborRates,
    currentDealerId,
    showOtherDealershipServiceHistory,
    tenantDealerDetails,
    offering,
    locale,
    getFormattedDateAndTime,
    getFormattedCurrency,
  });
  const sameDealershipServiceHistory = updateOtherDealerServiceHistoryVisibleStatus(
    dealerServiceHistory,
    currentDealerId,
    false
  );
  const adaptedThirdPartyServiceHistory = getAdaptedThirdPartyServiceHistory({
    thirdPartyServiceHistory,
    userList,
    options,
    currentDealerId,
    showOtherDealershipServiceHistory,
    getFormattedDateAndTime,
    getFormattedCurrency,
  });
  return {
    [VEHICLE_PROFILE_CONSTANTS.SERVICE_HISTORY_DEALER]: [
      ...sameDealershipServiceHistory,
      ...adaptedThirdPartyServiceHistory,
    ],
    [VEHICLE_PROFILE_CONSTANTS.OPEN_RO_DETAILS]: getOpenRODetails(
      openROList,
      userList,
      options,
      getFormattedDateAndTime
    ),
  };
};

export const getSortedServiceHistory = (oemServiceHistory, dealerServiceHistory) => {
  const totalServiceHistory = _map([...oemServiceHistory, ...dealerServiceHistory], serviceDetail => {
    const roCompletionDateMoment = toMoment(serviceDetail[VEHICLE_PROFILE_CONSTANTS.RO_COMPLETION_DATE_RAW]);
    return {
      ...serviceDetail,
      [VEHICLE_PROFILE_CONSTANTS.RO_COMPLETION_DATE_RAW]: roCompletionDateMoment?.valueOf(),
    };
  });
  return _orderBy(totalServiceHistory, VEHICLE_PROFILE_CONSTANTS.RO_COMPLETION_DATE_RAW, ['desc']);
};
