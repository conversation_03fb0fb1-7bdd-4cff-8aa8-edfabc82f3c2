export const XM_PHONE_NUMBER_VALUE = '877.GET.XMST (************)';
export const ONSTAR_US_PHONE_NUMBER = '888.ONSTAR1 (************)';
export const ONSTAR_CA_PHONE_NUMBER = '888-4ONST<PERSON> (************)';
export const CONTEXT_ID = 'VEHICLE_PROFILE_FORM';
export const RO_TABLE_NOTES_ACTION = 'RO_TABLE_NOTES_ACTION';
export const JOB_TABLE_NOTES_ACTION = 'JOB_TABLE_NOTES_ACTION';
export const WARRANTY_DEALERSHIP_TABLE_SUBMIT_ACTION = 'WARRANTY_DEALERSHIP_TABLE_SUBMIT_ACTION';
export const WARRANTY_DEALERSHIP_TABLE_DELETE_ACTION = 'WARRANTY_DEALERSHIP_TABLE_DELETE_ACTION';
export const WARRANTY_DEALERSHIP_TABLE_UPDATE_ACTION = 'WARRANTY_DEALERSHIP_TABLE_UPDATE_ACTION';
export const VEHICLE_ID = 'VEHICLE_ID';
export const VEHICLE_TYPE = 'vehicleType';
export const RO_COMPLETION_DATE_RAW = 'roCompletionDateRaw'; // Not a Field - For Mobile
export const IS_DEALER_SERVICE_HISTORY = 'isDealerServiceHistory'; // Not a Field - For Mobile
export const ALERTS_LIST = 'alerts';
export const EXTERNAL_LINKS = 'externalLinks';

export const WARRANTY_BLOCK_CODE = 'warrantyBlockCode';
export const WARRANTY_BLOCK_CAMPAIGN_TYPES = 'warrantyBlockCampaignTypes';
export const WARRANTY_BLOCK_DESCRIPTION = 'warrantyBlockDescription';
export const WARRANTY_BLOCK_EFFECTIVE_DATE = 'warrantyBlockEffectiveDate';
export const WARRANTY_BLOCK_LABOR_OPS = 'warrantyBlockLaborOps';
export const BRANDED_TITLE_NUMBER = 'brandedTitleNumber';
export const BRANDED_TITLE_REPORTING_SOURCE_CODE = 'brandedTitleReportingSourceCode';
export const BRANDED_TITLE_DESCRIPTION = 'brandedTitleDescription';
export const BRANDED_TITLE_REPORTING_STATE = 'brandedTitleReportingState';
export const BRANDED_TITLE_DATE = 'brandedTitleDate';

export const NO_TAGS_OPTION = {
  label: __('No Tags'),
  value: 'NO_TAGS',
};

// Alert type field values
export const WARRANTY_BLOCK_GROUP_CODE = 'Warranty Block';
export const BRANDED_TITLE_GROUP_CODE = 'Branded Title';

// Vehicle details overview
export const VIN = 'vehicleDetailsOverviewVin';
export const FIN = 'vehicleDetailsOverviewFin';
export const VEHICLE_VERSION_SERIES = 'vehicleDetailsOverviewVersionSeries';
export const VEHICLE_STOCK_NUMBER = 'vehicleDetailsOverviewVehicleStockNumber';
export const FLEET_NUMBER = 'vehicleDetailsOverviewFleetNumber';
export const VEHICLE_DETAILS_MANUFACTURING_NUMBER = 'vehicleDetailsOverviewManufacturingNumber';
export const LICENSE = 'vehicleDetailsOverviewLicensePlateNumber';
export const COLOR_ITEM_CODE_FIELD = 'colorItemCode';
export const INTERIOR_COLOR = 'vehicleDetailsOverviewInteriorColor';
export const EXTERIOR_COLOR = 'vehicleDetailsOverviewExteriorColor';
export const FINISH_COLOR = 'vehicleDetailsOverviewFinishColor';
export const TOP_COLOR = 'vehicleDetailsOverviewTopColor';
export const BODY_COLOR_CODE = 'vehicleDetailsOverviewBodyColorCode';

export const IN_SERVICE_DATE = 'vehicleDetailsOverviewInServiceDate';
export const IN_SERVICE_DATE_RAW = 'vehicleDetailsOverviewInServiceDateRaw';
export const IN_SERVICE_ODOMETER = 'vehicleDetailsOverviewInServiceOdometer';
export const SOLD_DATE = 'vehicleDetailsOverviewSoldDate';
export const LAST_SERVICE_DATE = 'vehicleDetailsOverviewLastServiceDate';
export const LAST_SERVICE_DATE_RAW = 'vehicleDetailsOverviewLastServiceDateRaw';
export const LAST_ODOMETER_READING_MILES = 'vehicleDetailsOverviewLastOdometerReadingMiles';
export const LAST_ODOMETER_READING_MILES_RAW = 'vehicleDetailsOverviewLastOdometerReadingMilesRaw'; // Not Table Field - For Mobile
export const CURRENT_ODOMETER_READING = 'vehicleDetailsOverviewCurrentOdometerReading';
export const RFID = 'vehicleDetailsOverviewRfid';
export const YEAR = 'vehicleDetailsOverviewYear';
export const MAKE = 'vehicleDetailsOverviewMake';
export const MODEL = 'vehicleDetailsOverviewModel';
export const VEHICLE_NOTE = 'vehicleDetailsOverviewVehicleNote';
export const TRIM = 'vehicleDetailsOverviewTrim';
export const BRANDING_STATE_CODE = 'vehicleDetailsOverviewBrandingStateCode';
export const BRANDING_TITLE = 'vehicleDetailsOverviewBrandingTitle';
export const BRANDING_STATE_REFERENCE = 'vehicleDetailsOverviewBrandingStateReference';
export const SUB_MODEL_TYPE = 'vehicleDetailsOverviewSubModelType';
export const OVERVIEW_VEHICLE_BODY_CLASS = 'vehicleDetailsOverviewVehicleType';
export const OVERVIEW_VEHICLE_TYPE = 'vehicleDetailsOverviewMercedesVehicleType';
export const MODEL_KEY = 'vehicleDetailsOverviewModelKey';
export const OEM_CODE = 'vehicleDetailsOverviewOemCode';
export const TVV = 'vehicleDetailsOverviewTvv';
export const TAPV = 'vehicleDetailsOverviewTapv';
export const MOT_DATE = 'vehicleDetailsOverviewMotDate';
export const NEXT_MOT_DATE = 'vehicleDetailsOverviewNextMotDate';
export const TAX_DUE_DATE = 'vehicleDetailsOverviewTaxDueDate';
export const NEXT_SERVICE_DATE = 'vehicleDetailsOverviewNextServiceDate';
export const LAST_VISIT_DATE = 'vehicleDetailsOverviewLastVisitDate';
export const POLICY_NUMBER = 'vehicleDetailsOverviewPolicyNumber';
export const ORDER_NUMBER = 'vehicleDetailsOverviewOrderNumber';
export const VEHICLE_KIND = 'vehicleDetailsOverviewVehicleKind';
export const SIGMA_CARD_NUMBER = 'vehicleDetailsOverviewSigmaCardNumber';
export const PAINT_QUALITY = 'vehicleDetailsOverviewPaintQuality';
export const REFRIGERANT_GAS_TYPE = 'vehicleDetailsOverviewRefrigerantGasType';
export const BATTERY_CAPACITY = 'vehicleDetailsOverviewBatteryCapacity';
export const SPARE_WHEEL = 'vehicleDetailsOverviewSpareWheel';

// Vehicle details - Engine details
export const IS_MULTI_ENGINE = 'vehicleDetailsAdditionalIsMultiEngine'; // Not a field, Porsche can have multiple engine
export const ENGINE = 'vehicleDetailsEngineS'; // Table for Porsche
export const ENGINE_CALIBRATION = 'vehicleDetailsEngineCalibration';
// Porsche - Columns for multi engine table
export const ENGINE_ENGINE_TYPE = 'vehicleDetailsEngineType'; // Column for Porsche & Text field for others
export const ENGINE_NUMBER = 'vehicleDetailsEngineNumber';
export const ENGINE_CODE = 'vehicleDetailsEngineCode';
export const ENGINE_CODE_EXTENSION = 'vehicleDetailsEngineCodeExtension';
export const ENGINE_CAPACITY = 'vehicleDetailsEngineCapacity';
export const ENGINE_POWER = 'vehicleDetailsEnginePower';
export const ENGINE_POINT_OF_FITMENT = 'vehicleDetailsEnginePointOfFitment';
export const ENGINE_ASSEMBLY_POSITION = 'vehicleDetailsEngineAssemblyPosition';

// Vehicle details overview for RV/Boat/etc
export const CHASSIS_NUMBER = 'vehicleDetailsOverviewChassisId';
export const HULL_NUMBER = 'vehicleDetailsOverviewHullId';
export const SERIAL_NUMBER = 'vehicleDetailsOverviewSerialId';
export const RV_TYPE = 'vehicleDetailsOverviewType';
export const MANUFACTURER = 'vehicleDetailsOverviewManufacturer';
export const LAST_SERVICE_ENGINE_HOURS = 'vehicleDetailsOverviewLastEngineMeterOutHrs';
export const HYUNDAI_SALE_CODE = 'vehicleDetailsHyundaiSaleCode';

// Vehicle details additional
export const DOOR_COUNT = 'vehicleDetailsAdditionalDoorCount';
export const DRIVE_TYPE = 'vehicleDetailsAdditionalDriveType';
export const BODY_TYPE = 'vehicleDetailsAdditionalBodyType';
export const WEIGHT = 'vehicleDetailsAdditionalWeight';
export const PAINT_COLOR = 'vehicleDetailsAdditionalPaintColor';
export const PAINT_CODE = 'vehicleDetailsAdditionalPaintCode';
export const UPHOLSTRY_CODE = 'vehicleDetailsAdditionalUpholstryCode';
export const UPHOLSTRY_DESCRIPTION = 'vehicleDetailsAdditionalUpholstryDescription';
export const OPTIONS = 'vehicleDetailsAdditionalOptions';
export const BUILD_DATE = 'vehicleDetailsAdditionalBuildDate';
export const BUILD_HOUR = 'vehicleDetailsAdditionalBuildHour';
export const ETA = 'vehicleDetailsAdditionalEta';
export const DRAFTED = 'vehicleDetailsAdditionalDrafted';
export const RETAILER_DEMO = 'vehicleDetailsAdditionalRetailerDemo';
export const SOA_DEMO = 'vehicleDetailsAdditionalSoaDemo';
export const TRANSMISSION = 'vehicleDetailsAdditionalTransmission';
export const TRANSMISSION_CODE = 'vehicleDetailsAdditionalTransmissionCode';
export const VEHICLE_STATUS = 'vehicleDetailsAdditionalVehicleStatus';
export const SUB_CATEGORY = 'vehicleDetailsAdditionalSubcategory';
export const TRANSMISSION_TYPE_CODE = 'vehicleDetailsAdditionalTransmissionTypeCode';
export const ENGINE_DESCRIPTION = 'vehicleDetailsAdditionalEngineDescription';
export const ADDITIONAL_EQUIPMENTS = 'vehicleDetailsAdditionalAdditionalEquipmentS';
export const NEW_ENGINE_NUMBER = 'vehicleDetailsAdditionalNewEngineNumber';
export const GEARBOX_NUMBER = 'vehicleDetailsAdditionalGearboxNumber';
export const NEW_GEARBOX_NUMBER = 'vehicleDetailsAdditionalNewGearboxNumber';
export const BIN_NUMBER = 'vehicleDetailsAdditionalBin';
export const NEW_BIN_NUMBER = 'vehicleDetailsAdditionalNewBinNumber';

export const KEY_CODE_IGNITION = 'vehicleDetailsAdditionalKeyCodeIgnition';
export const KEY_CODE_TRUNK = 'vehicleDetailsAdditionalKeyCodeTrunk';
export const RADIO = 'vehicleDetailsAdditionalRadio';
export const RADIO_CODE = 'vehicleDetailsAdditionalRadioCode';
export const TRUNK_KEYS = 'vehicleDetailsAdditionalTrunkKeys';
export const AXLES_COUNT = 'vehicleDetailsAdditionalAxlesCount';
export const AXLE_CODE = 'vehicleDetailsAdditionalAxleCode';
export const AXLE_RATIO = 'vehicleDetailsAdditionalAxleRatio';
export const CYLINDERS = 'vehicleDetailsAdditionalCylinders';
export const FUEL_TYPE = 'vehicleDetailsAdditionalFuelType';
export const WHEEL_SIZE = 'vehicleDetailsAdditionalWheelSize';
export const TIRE = 'vehicleDetailsAdditionalTire';
export const DELIVERY_DATE = 'vehicleDetailsAdditionalDeliveryDate';
export const SOLD_ORDER = 'vehicleDetailsAdditionalSoldOrder';
export const DELIVERY_TYPE_DESCRIPTION = 'vehicleDetailsAdditionalDeliveryTypeDescription';
export const BUILD_ORDER_ID = 'vehicleDetailsAdditionalBuildOrderNumberId';
export const MODEL_DESCRIPTION = 'vehicleDetailsAdditionalModelDescription';
export const BUILD_PLANT_CODE = 'vehicleDetailsAdditionalBuildPlantCode';
export const VEHICLE_CONDITION_CODE = 'vehicleDetailsAdditionalVehicleConditionCode';
export const MODEL_NUMBER = 'vehicleDetailsAdditionalModelNumber';
export const EMISSION_CERTIFICATE = 'vehicleDetailsAdditionalEmissionsCertificate';
export const BRANDED_VEHICLE = 'vehicleDetailsAdditionalBrandedVehicleYN';
export const FOREIGN_DISTRIBUTION_VEHICLE = 'vehicleDetailsAdditionalForeignDistributionVehicleYN';
export const MANUFACTURER_DATE = 'vehicleDetailsAdditionalManufacturerDate';
export const RETAIL_DATE = 'vehicleDetailsAdditionalRetailDate';
export const PRODUCTION_DATE = 'vehicleDetailsAdditionalProductionDate';
export const REGISTRATION_DATE = 'vehicleDetailsAdditionalRegistrationDate';
export const PDI_DATE = 'vehicleDetailsAdditionalPdiDate';
export const SOLD_TO_FLEET = 'vehicleDetailsAdditionalSoldToFleet';
export const RETAIL_SALES_TYPE = 'vehicleDetailsAdditionalRetailSalesType';
export const SYNC_VERSION = 'vehicleDetailsAdditionalSyncVersion';
export const VHR_ACTIVATED = 'vehicleDetailsAdditionalVhrActivated';
export const MODEM = 'vehicleDetailsAdditionalModem';
export const MARKET_VEHICLE = 'vehicleDetailsAdditionalMarketVehicle';
export const ASPIRATION = 'vehicleDetailsAdditionalAspiration';
export const KEY_WRITE_DATE = 'vehicleDetailsOverviewKeyWriteDate';
export const KEY_READ_DATE = 'vehicleDetailsOverviewKeyReadDate';
export const KEY_READ_ERROR = 'errors';

// Subaru
export const ORIGINAL_MODEL = 'vehicleDetailsAdditionalOriginalModelOptionSpecColor';
export const CHANGE_DATE = 'vehicleDetailsAdditionalChangeDate';
export const CHANGE_USER = 'vehicleDetailsAdditionalChangeUser';
export const CHANGES_TO_MODEL = 'vehicleDetailsAdditionalChangesToModelOptionSpecColor';

// Vehicle Additional Details for Boat/RV
export const BOAT_NAME = 'vehicleDetailsAdditionalBoatName';
export const LENGTH = 'vehicleDetailsAdditionalLength';
export const LAYOUT = 'vehicleDetailsAdditionalLayout';
export const ADDITIONAL_DETAILS_ENGINE_NUMBER = 'vehicleDetailsAdditionalEngineNumber';

// Production Flexibility
export const PRODUCTION_FLEXIBILITY_MODEL_OPTION = 'productionFlexibilityOriginalModelOptionSpecColor';
export const PRODUCTION_FLEXIBILITY_CHANGE_DATE = 'productionFlexibilityChangeDate';
export const PRODUCTION_FLEXIBILITY_CHANGE_USER = 'productionFlexibilityChangeUser';
export const PRODUCTION_FLEXIBILITY_CHANGE_MODEL_OPTION = 'productionFlexibilityChangesToModelOptionSpecColor';

// Capabilities
export const CAPABILITIES_TELEMETICS = 'capabilitiesTelemetics';
export const CAPABILITIES_TELESERVICE = 'capabilitiesTeleservice';
export const CAPABILITIES_TELESERVICE_CONTRACT = 'capabilitiesTeleserviceContract';
export const CAPABILITIES_RTC = 'capabilitiesRtc';

// Telematics Subscription
export const TELEMATICS_SUBSCRIPTION_TABLE = 'TELEMATICS_SUBSCRIPTION_TABLE';
export const WIFI_SUBSCRIPTION = 'telematicsSubscriptionWifiSubscription';
export const COMM_CHECKED_STATUS = 'telematicsSubscriptionCommCheckedStatus';
export const COMM_CHECKED_DATE = 'telematicsSubscriptionCommCheckedDate';
export const DCM_VERSION_NUMBER = 'telematicsSubscriptionDcmVersionNumber';
export const PRODUCT_NAME = 'telematicsSubscriptionProductName';
export const PRODUCT_END_DATE = 'telematicsSubscriptionProductEndDate';
export const TELEMATICS_SUBSCRIPTION_STATUS = 'telematicsSubscriptionStatus';
export const PRODUCT_START_DATE = 'telematicsSubscriptionProductStartDate';

// Associated customers
export const ASSOCIATED_CUSTOMER_TABLE = 'ASSOCIATED_CUSTOMER_TABLE'; // Table Item Key
export const ASSOCIATED_CUSTOMER_PAYLOAD = 'associatedCustomerPayload'; // Not a field. Only required for outgoing payload.
export const ASSOCIATED_CUSTOMER_NAME_FIELD = 'associatedCustomersName';
export const ASSOCIATED_CUSTOMER_PHONE_FIELD = 'associatedCustomersPhoneNumber';
export const ASSOCIATED_CUSTOMER_ORIGINAL_OWNER_NAME_FIELD = 'associatedCustomersOriginalOwnerName';
export const ASSOCIATED_CUSTOMER_FLEET_OWNER_NAME_FIELD = 'associatedCustomersFleetOwnerName';
export const ASSOCIATED_CUSTOMER_EMAIL_FIELD = 'associatedCustomersEmail';
export const ASSOCIATED_CUSTOMER_OWNERSHIP_INFO_FIELD = 'associatedCustomersOwnershipInfo';

// Vehicle health
export const ENGINE_OIL_QUANTITY_TABLE = 'engineOilQuantityTable';
export const REPLACE_ENGINE_OIL_TABLE = 'replaceEngineOilTable';
export const SMARTKEY_BATTERY_TABLE = 'smartkeyBatteryTable';
export const WARNING_LIST_TABLE = 'warningListTable';
export const INSPECTION_HISTORY_TABLE = 'inspectionHistoryTable';

export const VEHICLE_HEALTH_MILEAGE = 'vehicleHealthMileage';
export const VEHICLE_HEALTH_AVG = 'vehicleHealthAverage';
export const VEHICLE_HEALTH_LONG_TERM_AVG = 'vehicleHealthLongTermAverage';
export const VEHICLE_HEALTH_FUEL = 'vehicleHealthFuel';
export const VEHICLE_HEALTH_BATTERY = 'vehicleHealthBattery';
export const VEHICLE_HEALTH_QUANTITY_OF_ENGINE_OIL_DESCRIPTION_FIELD = 'vehicleHealthQuantityOfEngineOilDescription';
export const VEHICLE_HEALTH_QUANTITY_OF_ENGINE_OIL_ICON_FIELD = 'vehicleHealthQuantityOfEngineOilIconImage';
export const VEHICLE_HEALTH_QUANTITY_OF_ENGINE_OIL_LAST_UPDATED_TIME_FIELD =
  'vehicleHealthQuantityOfEngineOilLastUpdatedTime';
export const VEHICLE_HEALTH_QUANTITY_OF_ENGINE_OIL_STATUS_FIELD = 'vehicleHealthQuantityOfEngineOilStatus';
export const VEHICLE_HEALTH_REPLACE_ENGINE_OIL_DESCRIPTION_FIELD = 'vehicleHealthReplaceEngineOilDescription';
export const VEHICLE_HEALTH_REPLACE_ENGINE_OIL_ICON_FIELD = 'vehicleHealthReplaceEngineOilIconImage';
export const VEHICLE_HEALTH_REPLACE_ENGINE_OIL_LAST_UPDATED_TIME_FIELD = 'vehicleHealthReplaceEngineOilLastUpdateTime';
export const VEHICLE_HEALTH_REPLACE_ENGINE_OIL_STATUS_FIELD = 'vehicleHealthReplaceEngineOilStatus';
export const VEHICLE_HEALTH_SMARTKEY_BATTERY_DESCRIPTION_STATUS_FIELD = 'vehicleHealthSmartkeyBatteryDescriptionStatus';
export const VEHICLE_HEALTH_SMARTKEY_BATTERY_ICON_FIELD = 'vehicleHealthSmartkeyBatteryIcon';
export const VEHICLE_HEALTH_SMARTKEY_BATTERY_LAST_UPDATED_TIME_FIELD = 'vehicleHealthSmartkeyBatteryLastUpdateTime';
export const VEHICLE_HEALTH_SMARTKEY_BATTERY_STATUS_FIELD = 'vehicleHealthSmartkeyBatteryStatus';
export const VEHICLE_HEALTH_WARNING_LAST_UPDATED_TIME_FIELD = 'vehicleHealthWarningLastUpdateTime';
export const VEHICLE_HEALTH_WARNING_LIST_FIELD = 'vehicleHealthWarningsList';
export const VEHICLE_HEALTH_DTC = 'vehicleHealthDtc';
export const DTC_CODE = 'dtcCode';
export const DTC_DESC = 'dtcDescription';

// UV Eye Inspections
export const VEHICLE_HEALTH_INSPECTIONS_DESCRIPTION = 'vehicleHealthInspectionsDescription';
export const VEHICLE_HEALTH_INSPECTIONS_SOURCE = 'vehicleHealthInspectionsSource';
export const VEHICLE_HEALTH_INSPECTIONS_URL = 'vehicleHealthInspectionsUrl';
export const VEHICLE_HEALTH_INSPECTIONS_LAST_UPDATED_TIME = 'vehicleHealthInspectionsLastUpdatedTime';
export const VEHICLE_HEALTH_INSPECTIONS_MEDIA = 'vehicleHealthInspectionsMedia';
export const VEHICLE_HEALTH_INSPECTIONS_DESCRIPTION_VALUE = __('$$(Inspection) Report');

// Service history OEM
export const SERVICE_HISTORY_OEM = 'SERVICE_HISTORY_OEM'; // Table Item Key
export const SERVICE_HISTORY_OEM_JOB_TABLE = 'SERVICE_HISTORY_OEM_JOB_TABLE'; // Table Item Key
export const SERVICE_HISTORY_OEM_PART_TABLE = 'SERVICE_HISTORY_OEM_PART_TABLE'; // Table Item Key
export const JOB_INFO = 'jobInfo'; // Nesting key
// Level 1
export const SERVICE_HISTORY_OEM_RO_ID_FIELD = 'serviceHistoryOemRoId';
export const SERVICE_HISTORY_OEM_COMPLETION_DATE_FIELD = 'serviceHistoryOemCompletionDate';
export const SERVICE_HISTORY_OEM_RO_OPEN_DATE_FIELD = 'serviceHistoryOemRoOpenDate';
export const SERVICE_HISTORY_OEM_SERVICE_CENTER_FIELD = 'serviceHistoryOemServiceCenter';
export const SERVICE_HISTORY_OEM_SERVICE_LOCATION_FIELD = 'serviceHistoryOemServiceLocation';
export const SERVICE_HISTORY_OEM_JOB_COUNT_FIELD = 'serviceHistoryOemJobCount';
export const SERVICE_HISTORY_OEM_RO_PAY_TYPE_FIELD = 'serviceHistoryOemPayType';
export const SERVICE_HISTORY_OEM_SERVICE_ADVISOR_ID_FIELD = 'serviceHistoryOemServiceAdvisorId';
export const SERVICE_HISTORY_OEM_SERVICE_ADVISOR_FIELD = 'serviceHistoryOemServiceAdvisorName';
export const SERVICE_HISTORY_OEM_RO_COMMENT_FIELD = 'serviceHistoryOemRepairOrderComment';
export const SERVICE_HISTORY_OEM_REPAIR_ORDER_INTERNAL_REMARKS_FIELD = 'serviceHistoryOemRepairOrderInternalRemarks';
export const SERVICE_HISTORY_OEM_DEALER_CODE_FIELD = 'serviceHistoryOemDealerCode';
export const SERVICE_HISTORY_OEM_DEALER_NAME_FIELD = 'serviceHistoryOemDealerName';
export const SERVICE_HISTORY_OEM_ODOMETER_IN_FIELD = 'serviceHistoryOemOdometerIn';
export const SERVICE_HISTORY_OEM_ODOMETER_OUT_FIELD = 'serviceHistoryOemOdometerOut';
export const SERVICE_HISTORY_OEM_SERVICE_TYPE_FIELD = 'serviceHistoryOemServiceType';
export const SERVICE_HISTORY_OEM_REPEAT_REPAIR_FLAG_FIELD = 'serviceHistoryOemRepeatRepairFlag';
export const SERVICE_HISTORY_OEM_WARRANTY_CLAIM_NUMBER_FIELD = 'serviceHistoryOemWarrantyClaimNumber';
export const SERVICE_HISTORY_OEM_TRANSACTION_TYPE_FIELD = 'serviceHistoryOemTransactionType';
export const SERVICE_HISTORY_OEM_CLAIM_TYPE_FIELD = 'serviceHistoryOemWarrantyClaimType';
export const SERVICE_HISTORY_OEM_CLAIM_STATUS_FIELD = 'serviceHistoryOemClaimStatus';
export const SERVICE_HISTORY_OEM_AUTH_CODE_FIELD = 'serviceHistoryOemAuthCode';
export const SERVICE_HISTORY_OEM_ENTRY_DATE_FIELD = 'serviceHistoryOemEntryDate';
export const SERVICE_HISTORY_OEM_PROC_DATE_FIELD = 'serviceHistoryOemProcDate';
export const SERVICE_HISTORY_OEM_POSTING_DATE_FIELD = 'serviceHistoryOemPostingDate';
export const SERVICE_HISTORY_OEM_DESCRIPTION_FIELD = 'serviceHistoryOemDescription'; // Not a field. extra key for pdf exports
export const SERVICE_HISTORY_OEM_CONDITION_CODE_FIELD = 'serviceHistoryOemConditionCode';
export const SERVICE_HISTORY_OEM_CONDITION_DESCRIPTION_FIELD = 'serviceHistoryOemConditionDescription';
export const SERVICE_HISTORY_OEM_OPEN_DATE_FIELD = 'serviceHistoryOemRoOpenDate';
export const SERVICE_HISTORY_OEM_WARRANTY_CLAIM_TYPE_FIELD = 'serviceHistoryOemWarrantyClaimType';
export const SERVICE_HISOTRY_OEM_INVOICE_DATE_FIELD = 'serviceHistoryOemInvoiceDate';
// RRG Specific
export const SERVICE_HISTORY_OEM_OPERATION_ID = 'serviceHistoryOemOperationId';
export const SERVICE_HISTORY_OEM_CATEGORY_FIELD = 'serviceHistoryOemCategory';
export const SERVICE_HISTORY_OEM_CATEGORY_LABEL_FIELD = 'serviceHistoryOemCategoryLabel';
export const SERVICE_HISTORY_OEM_SYMPTOM_FIELD = 'serviceHistoryOemSymptom';
export const SERVICE_HISTORY_OEM_CUSTOMER_COMPLAINT_FIELD = 'serviceHistoryOemCustomerComplaint';
export const SERVICE_HISTORY_OEM_CUSTOMER_FEEDBACK_FIELD = 'serviceHistoryOemCustomerFeedback';

// Level 2 - JobList
export const SERVICE_HISTORY_OEM_JOB_FIELD = 'serviceHistoryOemJob';
export const SERVICE_HISTORY_OEM_JOB_PAY_TYPE_FIELD = 'serviceHistoryOemJobPayType';
export const SERVICE_HISTORY_OEM_STANDARD_OPCODE_FIELD = 'serviceHistoryOemStandardOpcode';
export const SERVICE_HISTORY_OEM_DMS_LABOR_OPCODE_FIELD = 'serviceHistoryOemDmsLaborOpcode';
export const SERVICE_HISTORY_OEM_DMS_JOB_NUMBER_FIELD = 'serviceHistoryOemDmsJobNumber';
export const SERVICE_HISTORY_OEM_OPERATION_ID_FIELD = 'serviceHistoryOemDmsOperationId';
export const SERVICE_HISTORY_OEM_OPERATION_NAME_FIELD = 'serviceHistoryOemDmsOperationName';
export const SERVICE_HISTORY_OEM_CAUSE_DESCRIPTION_FIELD = 'serviceHistoryOemCauseDescription';
export const SERVICE_HISTORY_OEM_COMPLAINT_DESCRIPTION_FIELD = 'serviceHistoryOemComplaintDescription';
export const SERVICE_HISTORY_OEM_CORRECTION_DESCRIPTION_FIELD = 'serviceHistoryOemCorrectionDescription';
export const SERVICE_HISTORY_OEM_TECHNICIAN_NOTE_FIELD = 'serviceHistoryOemTechnicianNote';
export const SERVICE_HISTORY_OEM_MISCELLANEOUS_NOTE_FIELD = 'serviceHistoryOemMiscellaneousNotes';
export const SERVICE_HISTORY_OEM_JOB_DENIAL_DESCRIPTION_FIELD = 'serviceHistoryOemJobDenialDescription';
// Level 2 - Sublet
export const SERVICE_HISTORY_OEM_SUBLET_CODE_FIELD = 'serviceHistoryOemSubletCode';
export const SERVICE_HISTORY_OEM_SUBLET_WORK_DESCRIPTION_FIELD = 'serviceHistoryOemSubletWorkDescription';
export const SERVICE_HISTORY_OEM_MISCELLANOUES_DESCRIPTION_FIELD = 'serviceHistoryOemMiscellaneousDescription';
export const SERVICE_HISTORY_OEM_GOG_DESCRIPTION_FIELD = 'serviceHistoryOemGogDescription';
export const SERVICE_HISTORY_OEM_PAINT_DESCRIPTION_FIELD = 'serviceHistoryOemPaintDescription';
export const SERVICE_HISTORY_OEM_SHOP_SUPPLIES_DESCRIPTION_FIELD = 'serviceHistoryOemShopSuppliesDescription';
export const SERVICE_HISTORY_OEM_FREIGHT_DESCRIPTION_FIELD = 'serviceHistoryOemFreightDescription';
export const SERVICE_HISTORY_OEM_SPLIT_TYPE_CODE_FIELD = 'serviceHistoryOemSplitsTypeCode';
export const SERVICE_HISTORY_OEM_TECHNICIAN_NAME_FIELD = 'serviceHistoryOemTechnicianName';
export const SERVICE_HISTORY_OEM_TECHNICIAN_NUMBER_FIELD = 'serviceHistoryOemTechnicianNumber';
export const SERVICE_HISTORY_OEM_PRICE_CODE_FIELD = 'serviceHistoryOemPriceCode';
export const SERVICE_HISTORY_OEM_PRICE_DESCRIPTION_FIELD = 'serviceHistoryOemPriceDescription';
export const SERVICE_HISTORY_OEM_AUTHORIZATION_NUMBER_FIELD = 'serviceHistoryOemAuthorizationNumber';
export const SERVICE_HISTORY_OEM_PART_SPLIT_PERCENTAGE_FIELD = 'serviceHistoryOemPartSplitPercentage';
// Level 3 - Parts
export const SERVICE_HISTORY_OEM_PART_ID_FIELD = 'serviceHistoryOemPartId';
export const SERVICE_HISTORY_OEM_PART_NAME_FIELD = 'serviceHistoryOemPartName';
export const SERVICE_HISTORY_OEM_PART_QUANTITY_FIELD = 'serviceHistoryOemPartQuantity';
// Level 3 - Labor
export const SERVICE_HISTORY_OEM_LABOR_OPCODE_FIELD = 'serviceHistoryOemLaborOpcode';
export const SERVICE_HISTORY_OEM_OPCODE_DESCRIPTION_FIELD = 'serviceHistoryOemOpcodeDescription';
export const SERVICE_HISTORY_OEM_LABOR_FLAT_HOURS_FIELD = 'serviceHistoryOemLaborFlatHours';
export const SERVICE_HISTORY_OEM_LABOR_HOURS_FIELD = 'serviceHistoryOemLaborHours';
// RRG Specific
export const SERVICE_HISTORY_OEM_LABOR_TYPE_FIELD = 'serviceHistoryOemLaborType';

// Service history Dealer
export const SERVICE_HISTORY_DEALER = 'SERVICE_HISTORY_DEALER'; // Table Item Key
export const SERVICE_HISTORY_DEALER_JOB_TABLE = 'SERVICE_HISTORY_DEALER_JOB_TABLE'; // Table Item Key
export const SERVICE_HISTORY_DEALER_OPERATION_COMPONENT = 'serviceHistoryDealerOperationDetails'; // Sub-Component Key
export const SERVICE_HISTORY_DEALER_OPERATIONS_INFO = 'SERVICE_HISTORY_OPERATION_INFO'; // Nesting Key
export const SERVICE_HISTORY_DEALER_PARTS_TABLE = 'SERVICE_HISTORY_PARTS_TABLE'; // Table Item Key
export const OPEN_RO_DETAILS = 'openRODetails';
export const SERVICE_HISTORY_DEALER_HEADING = 'SERVICE_HISTORY_DEALER_HEADING';
export const SERVICE_HISTORY_DEALER_RO_SCOPE = 'serviceHistoryDealerTenantLevelServiceHistory';

// Level 1 - RO
export const SERVICE_HISTORY_DEALER_RO_ID_FIELD = 'serviceHistoryDealerRoId';
export const SERVICE_HISTORY_DEALER_RO_NUMBER_FIELD = 'serviceHistoryDealerRoNumber';
export const SERVICE_HISTORY_DEALER_COMPLETION_DATE_FIELD = 'serviceHistoryDealerCompletionDate';
export const SERVICE_HISTORY_DEALER_SERVICE_ADVISOR_FIELD = 'serviceHistoryDealerServiceAdvisor';
export const SERVICE_HISTORY_DEALER_DEALERSHIP_FIELD = 'serviceHistoryDealerDealership';
export const SERVICE_HISTORY_DEALER_DEPARTMENT_FIELD = 'serviceHistoryDealerDepartment';
export const SERVICE_HISTORY_DEALER_RO_TYPE_FIELD = 'serviceHistoryDealerRoType';
export const SERVICE_HISTORY_DEALER_CUSTOMER_NAME_FIELD = 'serviceHistoryDealerCustomerName';
export const SERVICE_HISTORY_DEALER_NUMBER_OF_JOBS_FIELD = 'serviceHistoryDealerNumberOfJobs';
export const SERVICE_HISTORY_DEALER_RO_TECHNICIANS_FIELD = 'serviceHistoryDealerTechnicians';
export const SERVICE_HISTORY_DEALER_RO_TOTAL_FIELD = 'serviceHistoryDealerRoTotal';
export const SERVICE_HISTORY_DEALER_MILEAGE_IN_FIELD = 'serviceHistoryDealerMileage';
export const SERVICE_HISTORY_DEALER_RO_TAGS_FIELD = 'serviceHistoryDealerRoTags';
export const SERVICE_HISTORY_DEALER_OPEN_DATE_FIELD = 'serviceHistoryDealerOpenDate';
export const SERVICE_HISTORY_DEALER_OPEN_TIME_FIELD = 'serviceHistoryDealerOpenTime';
export const SERVICE_HISTORY_DEALER_CLOSE_TIME_FIELD = 'serviceHistoryDealerCloseTime';
export const SERVICE_HISTORY_DEALER_CUSTOMER_NUMBER = 'serviceHistoryDealerCustomerNumber';
export const SERVICE_HISTORY_DEALER_CP_TOTAL = 'serviceHistoryDealerCustomerPayTotal';
export const SERVICE_HISTORY_DEALER_W_TOTAL = 'serviceHistoryDealerWarrantyPayTotal';
export const SERVICE_HISTORY_DEALER_I_TOTAL = 'serviceHistoryDealerInternalPayTotal';
export const SERVICE_HISTORY_DEALER_SITE_ID = 'serviceHistoryDealerSiteId'; // Not a table field

// Level 2 - Job
export const SERVICE_HISTORY_DEALER_JOB_LINE_FIELD = 'serviceHistoryDealerJobLine';
export const SERVICE_HISTORY_DEALER_STORY_LINE_FIELD = 'serviceHistoryDealerStoryLine';
export const SERVICE_HISTORY_DEALER_OPCODE_FIELD = 'serviceHistoryDealerOperationCode';
export const SERVICE_HISTORY_DEALER_OPERATION_DESCRIPTION_FIELD = 'serviceHistoryDealerOperationDescription';
export const SERVICE_HISTORY_DEALER_JOB_CAUSE_FIELD = 'serviceHistoryDealerJobCause';
export const SERVICE_HISTORY_DEALER_JOB_COMPLAINT_FIELD = 'serviceHistoryDealerJobComplaint';
export const SERVICE_HISTORY_DEALER_JOB_CONCERN_FIELD = 'serviceHistoryDealerJobConcern';
export const SERVICE_HISTORY_DEALER_JOB_CORRECTION_FIELD = 'serviceHistoryDealerJobCorrection';
export const SERVICE_HISTORY_DEALER_TECHNICIAN_NAME_FIELD = 'serviceHistoryDealerTechnicianName';
export const SERVICE_HISTORY_DEALER_JOB_PAY_TYPE_FIELD = 'serviceHistoryDealerJobPayType';
export const SERVICE_HISTORY_DEALER_TOTAL_PRICE_FIELD = 'serviceHistoryDealerTotalPrice';
export const SERVICE_HISTORY_DEALER_JOB_HOURS = 'serviceHistoryDealerJobHours';
export const SERVICE_HISTORY_DEALER_CONCERN_TYPE_FIELD = 'serviceHistoryDealerConcernType';
export const SERVICE_HISTORY_DEALER_SUBLET_VENDOR = 'serviceHistoryDealerSubletVendor';
export const SERVICE_HISTORY_DEALER_PO_ID = 'serviceHistoryDealerPoId';
export const SERVICE_HISTORY_DEALER_PO_LINE_ID = 'serviceHistoryDealerPoLineId';
export const SERVICE_HISTORY_DEALER_SUBLET_TYPE = 'serviceHistoryDealerSubletType';
export const SERVICE_HISTORY_DEALER_SUBLET_LABOR = 'serviceHistoryDealerSubletLabor';
export const SERVICE_HISTORY_DEALER_SUBLET_PARTS = 'serviceHistoryDealerSubletParts';
export const SERVICE_HISTORY_DEALER_REASON_FOR_VOIDING_JOB = 'serviceHistoryDealerReasonForVoidingJob';
export const SERVICE_HISTORY_DEALER_RETRACTED_ACTUAL_HOURS = 'serviceHistoryDealerRetractedActualHours';
export const SERVICE_HISTORY_DEALER_JOB_TECHNICIANS_FIELD = 'serviceHistoryDealerTechnician';
export const SERVICE_HISTORY_DEALER_JOB_TYPE_FIELD = 'serviceHistoryDealerJobType';
export const SERVICE_HISTORY_DEALER_NUMBER_OF_OPERATIONS = 'serviceHistoryDealerNumberOfOperations';
export const SERVICE_HISTORY_DEALER_LABOR_SALE_AMOUNT = 'serviceHistoryDealerLaborSaleAmount';
export const SERVICE_HISTORY_DEALER_JOB_NUMBER_FIELD = 'serviceHistoryDealerJobNumber';
export const SERVICE_HISTORY_DEALER_LABOR_TYPE = 'serviceHistoryDealerLaborType';
// Level 3 - Operation
export const SERVICE_HISTORY_DEALER_OPERATION_CODE = 'serviceHistoryDealerOperationCode';
export const SERVICE_HISTORY_DEALER_OPERATION_DESCRIPTION = 'serviceHistoryDealerOperationDescription';
export const SERVICE_HISTORY_DEALER_LABOR_HOURS = 'serviceHistoryDealerLaborHours';
export const SERVICE_HISTORY_DEALER_LABOR_RATE = 'serviceHistoryDealerLaborRate';
export const SERVICE_HISTORY_DEALER_BILL_RATE = 'serviceHistoryDealerBillRate';
export const SERVICE_HISTORY_DEALER_BILL_HRS = 'serviceHistoryDealerBillHrs';
export const SERVICE_HISTORY_DEALER_LABOR_PRICE = 'serviceHistoryDealerLaborPrice';
export const SERVICE_HISTORY_DEALER_STORY_LINE = 'serviceHistoryDealerTechStoryLine';
export const SERVICE_HISTORY_DEALER_PARTS_SALES_AMOUNT = 'serviceHistoryDealerPartsSalesAmount';
export const SERVICE_HISTORY_DEALER_MISCELLANEOUS_SALE_AMOUNT = 'serviceHistoryDealerMiscellaneousSaleAmount';
export const SERVICE_HISTORY_DEALER_MISCELLANEOUS_COST_AMOUNT = 'serviceHistoryDealerMiscellaneousCostAmount';
export const SERVICE_HISTORY_DEALER_PARTS_COST_AMOUNT = 'serviceHistoryDealerPartsCostAmount';
export const SERVICE_HISTORY_DEALER_LABOR_COST_AMOUNT = 'serviceHistoryDealerLaborCostAmount';
export const SERVICE_HISTORY_DEALER_TIME_CARD_HOURS = 'serviceHistoryDealerTimeCardHour';
export const SERVICE_HISTORY_DEALER_SOLD_HOURS = 'serviceHistoryDealerSoldHours';
export const SERVICE_HISTORY_DEALER_ACTUAL_HOURS = 'serviceHistoryDealerActualHours';
// Level 4 - Parts
export const SERVICE_HISTORY_DEALER_PART = 'serviceHistoryDealerPart';
export const SERVICE_HISTORY_DEALER_QUANTITY = 'serviceHistoryDealerQuantity';
export const SERVICE_HISTORY_DEALER_UNIT_PRICE = 'serviceHistoryDealerUnitPrice';
export const SERVICE_HISTORY_DEALER_TOTAL_PRICE = 'serviceHistoryDealerTotalPrice';
export const SERVICE_HISTORY_DEALER_COUNTER_PERSON = 'serviceHistoryDealerCounterPerson';
export const SERVICE_HISTORY_DEALER_PART_FULFILLMENT_NOTES = 'serviceHistoryDealerPartFulfillmentNotes';

// Deferred Recommendation
export const DEFERRED_RECOMMENDATION_TABLE = 'DEFERRED_RECOMMENDATION_TABLE'; // Table Item Key

// Service Maintenance
export const SERVICE_MAINTENANCE_TABLE = 'SERVICE_MAINTENANCE_TABLE'; // Table Item Key
export const SERVICE_MAINTENANCE_OPERATION_ID = 'serviceMaintenanceOperationId';
export const SERVICE_MAINTENANCE_COMPLETION_DATE = 'serviceMaintenanceCompletionDate';
export const SERVICE_MAINTENANCE_ODOMETER_OUT = 'serviceMaintenanceOdometerOut';
export const SERVICE_MAINTENANCE_REPAIR_ORDER_COMMENT = 'serviceMaintenanceRepairOrderComment';
export const SERVICE_MAINTENANCE_CUSTOMER_FEEDBACK = 'serviceMaintenanceCustomerFeedback';
// Level - 2 & 3
export const SERVICE_MAINTENANCE_LABOR_OPCODE_FIELD = 'serviceMaintenanceLaborOpcode';
export const SERVICE_MAINTENANCE_LABOR_FLAT_HOURS_FIELD = 'serviceMaintenanceLaborFlatHours';
export const SERVICE_MAINTENANCE_PART_NAME_FIELD = 'serviceMaintenancePartName';
export const SERVICE_MAINTENANCE_PART_QUANTITY_FIELD = 'serviceMaintenancePartQuantity';
export const SERVICE_MAINTENANCE_LABOR_TYPE_FIELD = 'serviceMaintenanceLaborType';

// Warranties from Dealership
export const WARRANTY_DEALERSHIP_TABLE = 'warrantyDealershipWarrantyDealershipTable'; // Table Item Key
export const WARRANTY_DEALERSHIP_COLUMN_TYPE = 'warrantyDealershipColumnType';
export const WARRANTY_DEALERSHIP_COLUMN_NAME = 'warrantyDealershipColumnName';
export const WARRANTY_DEALERSHIP_COLUMN_STATUS = 'warrantyDealershipColumnStatus';
export const WARRANTY_DEALERSHIP_EXTERNAL_CONTRACT_TYPE = 'warrantyDealershipExternalContractType';
export const WARRANTY_DEALERSHIP_SERVICE_CONTRACT = 'warrantyDealershipServiceContract';
export const WARRANTY_DEALERSHIP_INSURANCE = 'warrantyDealershipInsurance';
export const WARRANTY_DEALERSHIP_TITLE = 'warrantyDealershipTitle';
export const WARRANTY_DEALERSHIP_PROVIDER_NAME = 'warrantyDealershipProviderName';
export const WARRANTY_DEALERSHIP_PROVIDER_TYPE = 'warrantyDealershipProviderType';
export const WARRANTY_DEALERSHIP_START_DATE = 'warrantyDealershipStartDate';
export const WARRANTY_DEALERSHIP_END_DATE = 'warrantyDealershipEndDate';
export const WARRANTY_DEALERSHIP_START_DATE_FORMATTED = 'warrantyDealershipStartDateFormatted';
export const WARRANTY_DEALERSHIP_END_DATE_FORMATTED = 'warrantyDealershipEndDateFormatted';
export const WARRANTY_DEALERSHIP_LIFETIME_PRODUCT = 'warrantyDealershipLifetimeProduct';
export const WARRANTY_DEALERSHIP_START_ODOMETER = 'warrantyDealershipStartOdometer';
export const WARRANTY_DEALERSHIP_END_ODOMETER = 'warrantyDealershipEndOdometer';
export const WARRANTY_DEALERSHIP_DESCRIPTION = 'warrantyDealershipDescription';
export const WARRANTY_DEALERSHIP_DEAL_NUMBER = 'warrantyDealershipDealNumber';
export const WARRANTY_DEALERSHIP_PRICE = 'warrantyDealershipPrice';
export const WARRANTY_DEALERSHIP_PRICE_FORMATTED = 'warrantyDealershipPriceFormatted';
export const WARRANTY_DEALERSHIP_TERMS = 'warrantyDealershipTerms';
export const WARRANTY_DEALERSHIP_STATUS = 'warrantyDealershipStatus';
export const WARRANTY_DEALERSHIP_DEDUCTIBLE = 'warrantyDealershipDeductible';
export const WARRANTY_DEALERSHIP_DEDUCTIBLE_FORMATTED = 'warrantyDealershipDeductibleFormatted';
export const WARRANTY_DEALERSHIP_MILES = 'warrantyDealershipMiles';
export const WARRANTY_DEALERSHIP_RENTAL = 'warrantyDealershipRental';
export const WARRANTY_DEALERSHIP_SOURCE = 'warrantyDealershipSource';
export const WARRANTY_DEALERSHIP_SOURCE_FORMATTED = 'warrantyDealershipSourceFormatted';
export const WARRANTY_DEALERSHIP_POLICY_NUMBER = 'warrantyDealershipPolicyNumber';
export const WARRANTY_DEALERSHIP_CUSTOMER_FIRST_NAME = 'warrantyDealershipCustomerFirstName';
export const WARRANTY_DEALERSHIP_CUSTOMER_LAST_NAME = 'warrantyDealershipCustomerLastName';
export const WARRANTY_DEALERSHIP_CUSTOMER_EMAIL = 'warrantyDealershipCustomerEmail';
export const WARRANTY_DEALERSHIP_CUSTOMER_ADDRESS = 'warrantyDealershipCustomer';
export const WARRANTY_DEALERSHIP_CUSTOMER_CITY = 'warrantyDealershipCustomerCity';
export const WARRANTY_DEALERSHIP_CUSTOMER_STATE_CODE = 'warrantyDealershipCustomerStateCode';
export const WARRANTY_DEALERSHIP_CUSTOMER_POSTAL_CODE = 'warrantyDealershipCustomerPostalCode';
export const WARRANTY_DEALERSHIP_TCA_CONTRACT_STATUS = 'warrantyDealershipContractStatus';
export const WARRANTY_DEALERSHIP_TCA_CONTRACT_NUMBER = 'warrantyDealershipContractNumber';
export const WARRANTY_DEALERSHIP_CONTRACT_ISSUING_DEALER = 'warrantyDealershipIssuingDealer';
export const WARRANTY_DEALERSHIP_CONTRACT_PLAN_DURATION = 'warrantyDealershipPlanDuration';
export const WARRANTY_DEALERSHIP_CONTRACT_PRODUCT_CODE = 'warrantyDealershipProductCode';
export const WARRANTY_DEALERSHIP_CONTRACT_PRODUCT_TYPE = 'warrantyDealershipProductType';
export const WARRANTY_DEALERSHIP_CONTRACT_PRODUCT_VARIANT_DISPLAY_NAME = 'warrantyDealershipProductVariantDisplayName';
export const WARRANTY_DEALERSHIP_CONTRACT_EFFECTIVE_DATE = 'warrantyDealershipEffectiveDate';
export const WARRANTY_DEALERSHIP_CONTRACT_EFFECTIVE_DATE_FORMATTED = 'warrantyDealershipEffectiveDateFormatted';
export const WARRANTY_DEALERSHIP_CONTRACT_EXPIRATION_DATE = 'warrantyDealershipEndDate';
export const WARRANTY_DEALERSHIP_CONTRACT_EFFECTIVE_MILEAGE = 'warrantyDealershipEffectiveMileage';
export const WARRANTY_DEALERSHIP_CONTRACT_EXPIRATION_MILEAGE = 'warrantyDealershipEndOdometer';
export const WARRANTY_DEALERSHIP_CONTRACT_VIN = 'warrantyDealershipVin';
export const WARRANTY_DEALERSHIP_CONTRACT_VEHICLE_YEAR = 'warrantyDealershipVehicleYear';
export const WARRANTY_DEALERSHIP_CONTRACT_VEHICLE_MAKE = 'warrantyDealershipVehicleMake';
export const WARRANTY_DEALERSHIP_CONTRACT_VEHICLE_MODEL = 'warrantyDealershipVehicleModel';
export const WARRANTY_DEALERSHIP_CONTRACT_MAINTENANCE_VISITS = 'warrantyDealershipMaintenanceVisits';
export const WARRANTY_DEALERSHIP_CONTRACT_REMAINING_COUPONS = 'warrantyDealershipRemainingCoupons';
export const WARRANTY_DEALERSHIP_CONTRACT_PAID_CLAIMS = 'warrantyDealershipPaidClaims';
export const WARRANTY_DEALERSHIP_PAID_CLAIMS_ID = 'warrantyDealershipPaidClaimsId';
export const WARRANTY_DEALERSHIP_PAID_CLAIMS_CLAIM_DATE = 'warrantyDealershipPaidClaimsClaimDate';
export const WARRANTY_DEALERSHIP_PAID_CLAIMS_FAILED_COMPONENTS = 'warrantyDealershipPaidClaimsFailedComponents';
export const WARRANTY_DEALERSHIP_PAID_CLAIMS_PAID_DATE = 'warrantyDealershipPaidClaimsPaidDate';

export const EXTERNAL_CONTRACT_TYPE = {
  WARRANTY: 'WARRANTY',
  INSURANCE: 'INSURANCE',
  SERVICE_CONTRACT: 'SERVICE_CONTRACT',
  OTHER: 'OTHER',
};
export const VOID_STATUS = 'VOID';
export const WARRANTY_DEALERSHIP_MANUAL = 'MANUAL';
export const DEAL = 'DEAL';
export const WARRANTY_DEALERSHIP_STATUS_TYPE = {
  ACTIVE: 'ACTIVE',
  EXPIRED: 'EXPIRED',
  UNKNOWN: 'UNKNOWN',
};
export const EXTERNAL_CONTRACT_STATUS_TYPE = {
  ACTIVE: 'ACTIVE',
  EXPIRED: 'EXPIRED',
  UNKNOWN: 'UNKNOWN',
};

export const SOURCE_OPTIONS = {
  DEAL: __('Deal'),
  EXTERNAL_DMS: __('External DMS'),
  OEM: __('OEM'),
  INTEGRATION: __('Integration'),
  MANUAL: __('Manual'),
};

// Warranties from OEM
export const WARRANTY_TABLE = 'WARRANTY_TABLE'; // Table Item Key
export const WARRANTY_CODE_FIELD = 'warrantyWarrantiesWarrantyCode';
export const WARRANTY_DESCRIPTION_FIELD = 'warrantyWarrantiesWarrantyDescription';
export const WARRANTY_COVERAGE_DESCRIPTION_FIELD = 'warrantyWarrantiesCoverageDescription';
export const WARRANTY_VALIDITY_FIELD = 'warrantyWarrantiesValidityStatus';
export const WARRANTY_START_DATE_FIELD = 'warrantyWarrantiesStartDate';
export const WARRANTY_END_DATE_FIELD = 'warrantyWarrantiesEndDate';
export const WARRANTY_START_ODOMETER_FIELD = 'warrantyWarrantiesStartOdo';
export const WARRANTY_END_ODOMETER_FIELD = 'warrantyWarrantiesEndOdo';
export const WARRANTY_DEDUCTIBLE = 'warrantyWarrantiesDeductible';
export const WARRANTY_CANCELLATION_CODE = 'warrantyWarrantiesWarrantyCancellationCodeYN';
export const WARRANTY_CANCELLATION_DATE = 'warrantyWarrantiesWarrantyCancellationDate';
export const WARRANTY_EXPIRATION_DATE_FIELD = 'warrantyWarrantiesExpirationDate';
export const WARRANTY_WARRANTY_FIELD = 'warrantyWarrantiesWarranty';
export const WARRANTY_EXPIRATION_DISTANCE_FIELD = 'warrantyWarrantiesExpirationMilesKilometers';
export const WARRANTY_ROAD_SIDE_ASSISTANCE_FIELD = 'warrantyWarrantiesRoadSideAssistance';
export const WARRANTY_MASTERSHIELD_FIELD = 'warrantyWarrantiesMastershield';
export const WARRANTY_VEHICLE_RESTRICTIONS_FIELD = 'warrantyWarrantiesVehicleRestrictions';
export const WARRANTY_WARRANTY_TRANSFERABLE_FIELD = 'warrantyWarrantiesWarrantyTransferrable';
export const WARRANTY_MONTHS_REMAINING_FIELD = 'warrantyWarrantiesMonthsRemaining';
export const WARRANTY_BUILD_DATE_FIELD = 'warrantyWarrantiesBuildDate';
export const WARRANTY_EXTENDED_WARRANTY_START_MILEAGE = 'warrantyWarrantiesExtendedWarrantyStartMileage';
export const WARRANTY_EXTENDED_WARRANTY_START_DATE = 'warrantyWarrantiesExtendedWarrantyStartDate';
export const WARRANTY_NESTED_TABLE = 'warrantyNestedTable';

// Warranty coverage
export const WARRANTY_COVERAGE_TABLE = 'WARRANTY_COVERAGE_TABLE';
export const WARRANTY_COVERAGE_TYPE = 'warrantyCoverageCoverageType';
export const WARRANTY_COVERAGE_DESCRIPTION = 'warrantyCoverageCoverageDescription';
export const WARRANTY_COVERAGE_ADDITIONAL_INFORMATION = 'warrantyCoverageAdditionalInformation';

// Subaru Equity Shield
export const COVERAGE_SES_TABLE = 'COVERAGE_SES_TABLE';
export const COVERAGE_AGREEMENT_PURCHASE = 'subaruEquityShieldAgreementPurchase';
export const CONTRACT_END_DATE = 'subaruEquityShieldContractEndDate';
export const COVERAGE_CONTRACT_NUMBER = 'subaruEquityShieldContractNumber';
export const CONTRACT_DESC = 'subaruEquityShieldContractDescription';
export const CONTRACT_STATUS = 'subaruEquityShieldContractStatus';
export const WARRANTY_START = 'subaruEquityShieldWarrantyStart';
export const CONTRACT_CANCEL_DATE = 'subaruEquityShieldContractCancelDate';

// Warranty history
export const WARRANTY_HISTORY = 'warrantyHistory'; // Table Item Key
export const WARRANTY_HISTORY_JOB_TABLE = 'warrantyHistoryJobTable'; // Table Item Key
export const WARRANTY_HISTORY_JOB_INFO_FIELD = 'warrantyHistoryJobInfo'; // Nesting Key
// Level 1
export const WARRANTY_HISTORY_RO_FIELD = 'warrantyHistoryRoId';
export const WARRANTY_HISTORY_SERVICE_DATE_FIELD = 'warrantyHistoryServiceDate';
export const WARRANTY_HISTORY_SERVICE_TYPE_FIELD = 'warrantyHistoryServiceType';
export const WARRANTY_HISTORY_DEALER_NAME = 'warrantyHistoryDealerName';
export const WARRANTY_HISTORY_ODOMETER = 'warrantyHistoryOdometer';
export const WARRANTY_HISTORY_COMMENT = 'warrantyHistoryComment';
export const WARRANTY_HISTORY_SERVICE_IN_DATE_FIELD = 'warrantyHistoryServiceInDate';
export const WARRANTY_HISTORY_MILEAGE_IN_FIELD = 'warrantyHistoryMileageIn';
export const WARRANTY_HISTORY_CONDITION_CODE = 'warrantyHistoryConditionCode';
export const WARRANTY_HISTORY_CONDITION_DESCRIPTION = 'warrantyHistoryConditionDescription';
export const WARRANTY_HISTORY_LABOR_OPERATION_IDS_FIELD = 'warrantyHistoryLaborOperationIds';
// Level 2
export const WARRANTY_HISTORY_TRANSACTION_TYPE_FIELD = 'warrantyHistoryTransactionType';
export const WARRANTY_HISTORY_LABOR_OPCODE_FIELD = 'warrantyHistoryLaborOperationCode';
export const WARRANTY_HISTORY_JOB_DESCRIPTION_FIELD = 'warrantyHistoryJobDescription';
export const WARRANTY_HISTORY_PART_NUMBER = 'warrantyHistoryPartNumber';
export const WARRANTY_HISTORY_PART_DESCRIPTION = 'warrantyHistoryPartDescription';
export const WARRANTY_HISTORY_QUANTITY = 'warrantyHistoryQuantity';

// Vehicle Warranty Transfer
export const VEHICLE_WARRANTY_TRANSFER = 'vehicleWarrantyTransfer'; // Table Item Key
export const VEHICLE_WARRANTY_TRANSFER_WARRANTY_TRANSFER_CODE_FIELD = 'vehicleWarrantyTransferWarrantyTransferCode';
export const VEHICLE_WARRANTY_TRANSFER_WARRANTY_TRANSFER_MODEL_YEAR_FIELD =
  'vehicleWarrantyTransferWarrantyTransferModelYear';
export const VEHICLE_WARRANTY_TRANSFER_WARRANTY_TRANSFER_BASIC_COVERAGE_FIELD =
  'vehicleWarrantyTransferWarrantyTransferBasicCoverage';
export const VEHICLE_WARRANTY_TRANSFER_WARRANTY_TRANSFER_POWER_TRAIN_FIELD =
  'vehicleWarrantyTransferWarrantyTransferPowerTrain';
export const VEHICLE_WARRANTY_TRANSFER_WARRANTY_TRANSFER_RUST_PROTECTION_FIELD =
  'vehicleWarrantyTransferWarrantyTransferRustProtection';
export const VEHICLE_WARRANTY_TRANSFER_WARRANTY_TRANSFER_MESSAGE_FIELD =
  'vehicleWarrantyTransferWarrantyTransferMessage';
export const VEHICLE_WARRANTY_TRANSFER_WARRANTY_TRANSFER_DEDUCTIBLE_FIELD =
  'vehicleWarrantyTransferWarrantyTransferDeductible';

// Extended service contract information
export const EXTENDED_WARRANTY = 'EXTENDED_WARRANTY'; // Table Item Key
export const EXTENDED_WARRANTY_COMPANY_NAME_FIELD = 'extendedServiceContractInformationCompanyName';
export const EXTENDED_WARRANTY_COMPANY_PHONE_FIELD = 'extendedServiceContractInformationCompanyPhone';
export const EXTENDED_WARRANTY_POLICY_NUMBER_FIELD = 'extendedServiceContractInformationPolicyNumber';
export const EXTENDED_WARRANTY_TERMS_FIELD = 'extendedServiceContractInformationTerms';
export const EXTENDED_WARRANTY_EFFECTIVE_DATE_FIELD = 'extendedServiceContractInformationEffectiveDate';
export const EXTENDED_WARRANTY_ENROLLMENT_DATE_FIELD = 'extendedServiceContractInformationEnrollmentDate';
export const EXTENDED_WARRANTY_DEDUCTIBLE_FIELD = 'extendedServiceContractInformationDeductible';
export const EXTENDED_WARRANTY_LIMIT_IN_ODO_METER_FIELD = 'extendedServiceContractInformationLimitInOdometer';
export const EXTENDED_WARRANTY_BEGINNING_ODO_METER_FIELD = 'extendedServiceContractInformationStartOdo';
export const EXTENDED_WARRANTY_ENDING_ODO_METER_FIELD = 'extendedServiceContractInformationEndOdo';
export const EXTENDED_WARRANTY_PLAN_TYPE = 'extendedServiceContractInformationPlanType';
export const EXTENDED_WARRANTY_EXTENDED_SERVICE_SECTION_LABEL =
  'extendedServiceContractInformationExtendedServiceSectionLabel';
export const EXTENDED_WARRANTY_SERVICE_CONTRACT_EXPIRATION_NOTE =
  'extendedServiceContractInformationServiceContractExpirationNote';
export const EXTENDED_WARRANTY_PLAN_EXPIRES_DATE = 'extendedServiceContractInformationPlanExpiresDate';
export const EXTENDED_WARRANTY_CONTRACT_TERM = 'extendedServiceContractInformationContractTerm';
export const EXTENDED_WARRANTY_OWNER_NAME = 'extendedServiceContractInformationOwnerName';
export const EXTENDED_WARRANTY_OPTIONS = 'extendedServiceContractInformationOptions';
export const EXTENDED_WARRANTY_CANCELLED_DATE = 'extendedServiceContractInformationCancelledDate';
export const EXTENDED_WARRANTY_CANCELLED_MILEAGE = 'extendedServiceContractInformationCancelledMileage';
export const EXTENDED_WARRANTY_CONTRACT_SUSPENDED_DATE = 'extendedServiceContractInformationContractSuspendedDate';
export const EXTENDED_WARRANTY_CONTRACT_SUSPENDED_INDICATOR =
  'extendedServiceContractInformationContractSuspendedIndicator';
export const EXTENDED_WARRANTY_SERVICE_CONTRACT_ID = 'extendedServiceContractInformationServiceContractId';
export const EXTENDED_WARRANTY_SERVICE_CONTRACT_NOTES = 'extendedServiceContractInformationServiceContractNotes';
export const EXTENDED_WARRANTY_PLAN_EFFECTIVE_MILES = 'extendedServiceContractInformationPlanEffectiveMiles';
export const EXTENDED_WARRANTY_SERVICE_OCPD = 'extendedServiceContractInformationServiceOcpd';
export const EXTENDED_WARRANTY_CLAIMED = 'extendedServiceContractInformationClaimed';
export const EXTENDED_WARRANTY_LAST_PERFORMED_DATE = 'extendedServiceContractInformationLastPerformedDate';
export const EXTENDED_WARRANTY_ALLOWED_DESCRIPTION = 'extendedServiceContractInformationAllowedDescription';
export const EXTENDED_WARRANTY_LAST_PERFORMED_MILES = 'extendedServiceContractInformationLastPerformedMiles';
export const EXTENDED_WARRANTY_ROAD_SIDE_ASSISTANCE = 'extendedServiceContractInformationRoadSideAssistance';
export const EXTENDED_WARRANTY_RENTAL_ALLOWANCE_PER_DAY = 'extendedServiceContractInformationRentalAllowancePerDay';
export const EXTENDED_WARRANTY_RENTAL_ALLOWANCE_PER_VISIT = 'extendedServiceContractInformationRentalAllowancePerVisit';
export const EXTENDED_WARRANTY_TOWING_ALLOWANCE = 'extendedServiceContractInformationTowingAllowance';
export const EXTENDED_WARRANTY_MAXIMUM_OIL_CHANGE_ALLOWANCE =
  'extendedServiceContractInformationMaximumOilChangeAllowance';
export const EXTENDED_WARRANTY_CANCELLATION_FEE = 'extendedServiceContractInformationCancellationFee';
export const EXTENDED_WARRANTY_TRANSFER_FEE = 'extendedServiceContractInformationTransferFee';
export const EXTENDED_WARRANTY_DETAILS_ADDITIONAL_MESSAGE = 'extendedServiceContractInformationAdditionalDetails';
export const EXTENDED_WARRANTY_DETAILS_CENTER_ID = 'extendedServiceContractInformationCenterId';
export const EXTENDED_WARRANTY_DETAILS_VENDOR = 'extendedServiceContractInformationVendor';
export const EXTENDED_WARRANTY_DETAILS_SOLD_BY_ME = 'extendedServiceContractInformationSoldbyme';
export const EXTENDED_WARRANTY_DETAILS_SOLD_BY = 'extendedServiceContractInformationSoldby';
export const EXTENDED_WARRANTY_DETAILS_CUSTOMER_LAST_NAME = 'extendedServiceContractInformationCustomerlastname';
export const EXTENDED_WARRANTY_DETAILS_CUSTOMER_FIRST_NAME = 'extendedServiceContractInformationCustomerfirstname';
export const EXTENDED_WARRANTY_ESP_START_DATE = 'extendedServiceContractInformationEspStartDate';

export const EXTENDED_WARRANTY_CENTER_NAME = 'extendedServiceContractInformationCenterName';
export const EXTENDED_WARRANTY_CENTER_PHONE = 'extendedServiceContractInformationCenterPhone';
export const EXTENDED_WARRANTY_CONTRACT_STATUS = 'extendedServiceContractInformationContractStatus';
export const EXTENDED_WARRANTY_CONTRACT_TRANSFERRED_INDICATOR =
  'extendedServiceContractInformationContractTransferredIndicator';
export const EXTENDED_WARRANTY_CONTRACT_REMAINING = 'extendedServiceContractInformationContractRemaining';

// Exclusion
export const EXCLUSION = 'EXCLUSION'; // Table Item Key
export const EXCLUSION_EFFECTIVE_DATE = 'exclusionsEffectiveDate';
export const EXPIRATION_DATE = 'exclusionsExpirationDate';
export const EXCLUSION_TYPE_DESCRIPTION = 'exclusionsExclusionTypeDescription';
export const EXPIRATION_STATUS = 'exclusionsStatus';

// Added security
export const ADDED_SECURITY = 'ADDED_SECURITY'; // Table Item Key
export const AGREEMENT_PURCHASE = 'addedSecurityAgreementPurchase';
export const AGREEMENT_EXPIRATION = 'addedSecurityAgreementExpiration';
export const COVERAGE_DESCRIPTION = 'addedSecurityCoverageDescription';
export const CONTRACT_NUMBER = 'addedSecurityContractNumber';
export const PLAN_NUMBER = 'addedSecurityPlanNumber';
export const POS_MILEAGE = 'addedSecurityPosMileage';
export const TIRE_PROTECTION = 'addedSecurityTireProtection';
export const STATUS = 'addedSecurityStatus';
export const CONTRACT_TERM_DISTANCE = 'addedSecurityContractTermDistance';
export const CANCEL_DATE = 'addedSecurityCancelDate';
export const DEDUCTIBLE_AMOUNT = 'addedSecurityDeductibleAmount';
export const RENTAL_ALLOWANCE = 'addedSecurityRentalAllowance';
export const TOWING_ALLOWANCE = 'addedSecurityTowingAllowance';
export const COVERAGE_TYPE = 'addedSecurityCoverageType';

// Eligible Contracts
export const ELIGIBILE_CONTRACTS = 'ELIGIBILE_CONTRACTS'; // Table Item Key
export const ELIGIBILE_CONTRACTS_TYPE = 'eligibleContractsType';
export const ELIGIBILE_CONTRACTS_PROGRAM_DESCRIPTION = 'eligibleContractsProgramDescription';
export const ELIGIBILE_CONTRACTS_PROGRAM_CODE = 'eligibleContractsProgramCode';
export const ELIGIBILE_CONTRACTS_WHOLESALE_PRICE = 'eligibleContractsWholesalePrice';
export const ELIGIBILE_CONTRACTS_MSRP = 'eligibleContractsMsrp';
export const ELIGIBILE_CONTRACTS_EXPIRE_AGE = 'eligibleContractsExpireAge';
export const ELIGIBILE_CONTRACTS_EXPIRE_MILEAGE = 'eligibleContractsExpireMileage';
export const ELIGIBILE_CONTRACTS_VENDOR = 'eligibleContractsVendor';
export const ELIGIBILE_CONTRACTS_DEDUCTIBLE = 'eligibleContractsDeductible';

// Insurance
export const INSURANCE_TABLE = 'INSURANCE_TABLE'; // Table Item Key
export const INSURANCE_POLICY_NUMBER_FIELD = 'insurancePolicyNumber';
export const INSURANCE_NAME_FIELD = 'insuranceName';
export const INSURANCE_DESCRIPTION_FIELD = 'insuranceDescription';
export const INSURANCE_DEDUCTIBLE_FIELD = 'insuranceDeductible';
export const INSURANCE_RENTAL_AMOUNT_FIELD = 'insuranceRentalAmount';
export const INSURANCE_START_DATE_FIELD = 'insuranceStartDate';
export const INSURANCE_START_ODO_FIELD = 'insuranceStartOdo';
export const INSURANCE_END_DATE_FIELD = 'insuranceEndDate';
export const INSURANCE_END_ODO_FIELD = 'insuranceEndOdo';

// Service Information
export const SERVICE_INFORMATION_TABLE = 'SERVICE_INFORMATION_TABLE'; // Table Item Key
export const SERVICE_INFORMATION_TYPE = 'serviceInformationType';
export const SERVICE_INFORMATION_NUMBER = 'serviceInformationNumber';
export const SERVICE_INFORMATION_DESCRIPTION = 'serviceInformationDescription';
export const SERVICE_INFORMATION_DATE_POSTED = 'serviceInformationDatePosted';

// Scheduled Maintenenance
export const SCHEDULED_MAINTENANCE_LIST = 'scheduledMaintenanceList';

// Special Service Campaign
export const CAMPAIGNS_TABLE = 'CAMPAIGNS_TABLE'; // Table Item Key
export const CAMPAIGNS_OPCODE_COMPONENT = 'CAMPAIGNS_OPCODE_COMPONENT'; // Nested Table component
export const CAMPAIGN_NUMBER_FIELD = 'specialServiceCampaignNumber';
export const CAMPAIGN_TYPE_FIELD = 'specialServiceCampaignCampaignType';
export const CAMPAIGN_DESCRIPTION_FIELD = 'specialServiceCampaignDescription';
export const CAMPAIGN_COMPLETED_INDICATOR_FIELD = 'specialServiceCampaignCampaignCompletedIndicator';
export const CAMPAIGN_POPUP_MESSAGE_FIELD = 'specialServiceCampaignCampaignPopupMessage';
export const CAMPAIGN_LABOR_OPERATION_FIELD = 'specialServiceCampaignCampaignLaborOperation';
export const CAMPAIGN_LABOR_OPERATION_DESCRIPTION_FIELD = 'specialServiceCampaignCampaignLaborOperationDescription';
export const CAMPAIGN_LABOR_TIME_FIELD = 'specialServiceCampaignCampaignLaborTime';
export const CAMPAIGN_MULTIPLE_CAMPAIGN_POPUP_MESSAGE_FIELD = 'specialServiceCampaignMultipleCampaignPopupMessage';
export const CAMPAIGN_SERVICE_BULLETIN_URL_FIELD = 'specialServiceCampaignServiceBulletinUrl';
export const CAMPAIGN_RECALL_FIELD = 'specialServiceCampaignRecallYNUnknown';
export const CAMPAIGN_DATE_FIELD = 'specialServiceCampaignDate';
export const CAMPAIGN_TERMINATION_MILEAGE_FIELD = 'specialServiceCampaignCampaignTerminationMileage';
export const CAMPAIGN_END_DATE_FIELD = 'specialServiceCampaignCampaignEndDate';

// Level 1
export const CAMPAIGNS_LABOR_OPERATION_ID = 'specialServiceCampaignLaborOperationId';
export const CAMPAIGNS_LABOR_OPERATION_DESCRIPTION = 'specialServiceCampaignLaborOperationDescription';
export const CAMPAIGNS_LABOR_ALLOWANCE_HOURS = 'specialServiceCampaignLaborAllowanceHours';
export const CAMPAIGN_NAME_FIELD = 'specialServiceCampaignName';
export const CAMPAIGN_DAMAGE_CODE_FIELD = 'specialServiceCampaignDamageCode';
export const CAMPAIGN_START_DATE_FIELD = 'specialServiceCampaignCampaignStartDate';

export const CAMPAIGN_PART_TABLE = 'CAMPAIGN_PART_TABLE';
export const CAMPAIGN_PART_ID = 'specialServiceCampaignPartId';
export const CAMPAIGN_PART_DESCRIPTION = 'specialServiceCampaignPartDescription';
export const CAMPAIGN_PART_QUANTITY = 'specialServiceCampaignPartQuantity';
export const CAMPAIGN_PART_TYPE = 'specialServiceCampaignPartType';

// Service Bulletin
export const SERVICE_BULLETINS = 'SERVICE_BULLETINS'; // Table Item Key
export const SERVICE_BULLETIN_REFERENCE_FIELD = 'serviceBulletinsReference';
export const SERVICE_BULLETIN_DESCRIPTION_FIELD = 'serviceBulletinsDescription';
export const SERVICE_BULLETIN_DEFECT_CODE_FIELD = 'serviceBulletinsDefectCode';
export const SERVICE_BULLETIN_MODEL_FIELD = 'serviceBulletinsBulletinModel';
export const SERVICE_BULLETIN_STATUS_FLAG = 'serviceBulletinsStatusFlag';
export const SERVICE_BULLETIN_STATUS_DESCRIPTION = 'serviceBulletinsStatusDescription';
export const SERVICE_BULLETIN_STATUS = 'serviceBulletinsStatus';
export const SERVICE_BULLETIN_REMEDY_STATUS_CODE = 'serviceBulletinsRemedyStatusCode';
export const SERVICE_BULLETIN_REMEDY_STATUS_DESCRIPTION = 'serviceBulletinsRemedyStatusDescription';
export const SERVICE_BULLETIN_STOP_SALES_CODE = 'serviceBulletinsStopSalesCode';
export const SERVICE_BULLETIN_STOP_SALES_FLAG = 'serviceBulletinsStopSalesFlag';
export const SERVICE_BULLETIN_RESERVED_BY_CENTER_ID = 'serviceBulletinsReservedByCenterId';
export const SERVICE_BULLETIN_RESERVED_BY_NAME = 'serviceBulletinsReservedByName';
export const SERVICE_BULLETIN_START_DATE = 'serviceBulletinsStartDate';

// Recalls
export const RECALLS_TABLE = 'RECALLS_TABLE'; // Table Item Key
export const RECALLS_OPCODE_COMPONENT = 'RECALLS_OPCODE_COMPONENT'; // Table Item Key - level 1
export const RECALLS_RECALL_NUMBER = 'recallsRecallNumber';
export const RECALL_NAME_FIELD = 'recallsName';
export const RECALL_LONG_DESCRIPTION_FIELD = 'recallsLongDescription';
export const RECALL_OPENED_ON_FIELD = 'recallsOpenedOn';
export const RECALL_STATUS_FIELD = 'recallsStatus';
export const RECALL_STATUS_CODE = 'recallsStatusCode';
export const RECALL_DAMAGE_CODE = 'recallsDamageCode';
export const RECALL_STATUS_DESCRIPTION = 'recallsStatusDescription';
export const RECALL_REMEDY_STATUS = 'recallsRemedyStatus';
export const RECALL_REMEDY_STATUS_DESCRIPTION = 'recallsRemedyStatusDescription';
export const RECALL_STOP_SALES_CODE = 'recallsStopSalesCode';
export const RECALL_STOP_SALES_FLAG = 'recallsStopSalesFlag';
export const RECALL_RESERVED_BY_CENTER_ID = 'recallsReservedByCenterId';
export const RECALL_RESERVED_BY_NAME = 'recallsReservedByName';
export const RECALLS_RECALL_COMPLETED_DATE = 'recallsRecallCompletedDate';
export const RECALLS_BULLETIN_URL = 'recallsBulletinUrl';
export const RECALLS_DEFECT_CODE = 'recallsDefectCode';
export const RECALLS_RESERVED_BY_ME = 'recallsReservedByMe';
export const RECALLS_RECALL_URL = 'recallUrl';
export const RECALLS_NHTSA_NO = 'nhtsaRecallNo';
export const RECALLS_POPUP_MESSAGE = 'recallsRecallPopupMessage';
export const RECALLS_LABOR_TIME = 'recallsRecallLaborTime';
export const RECALLS_SERVICE_BULLETIN_URL = 'recallsServiceBulletinUrl';
export const RECALLS_CODE = 'recallsCode';
export const RECALLS_SEVERITY = 'recallsSeverity';
export const RECALLS_QUOTATION = 'recallsQuotation';
export const RECALLS_AGREEMENT = 'recallsAgreement';
export const RECALLS_DELIVERY_DATE = 'recallsDeliveryDate';
export const RECALLS_ADD_TO_RO_ACTION = 'recallsAddToRoAction';
export const RECALLS_SEVERITY_CODES = {
  R: 'R',
  B: 'B',
  S: 'S',
};

// Level 1
export const RECALLS_LABOR_OPERATION_ID = 'recallsLaborOperationId';
export const RECALLS_LABOR_OPERATION_DESCRIPTION = 'recallsLaborOperationDescription';
export const RECALLS_LABOR_ALLOWANCE_HOURS = 'recallsLaborAllowanceHours';
export const RECALLS_EXPIRATION_MILEAGE = 'recallsExpirationMileage';
export const RECALLS_EXPIRATION_DATE = 'recallsExpirationDate';
export const RECALLS_TYPE = 'recallsRecallType';

// Recall Modal Fields
export const RECALL_MODAL_NUMBER = 'recallModalNumber';
export const RECALL_MODAL_NHTSA_NO = 'recallModalNhtsaNo';
export const RECALL_MODAL_TYPE = 'recallModalType';
export const RECALL_MODAL_DESCRIPTION = 'recallModalDescription';
export const RECALL_MODAL_DETAILED_DESCRIPTION = 'recallModalDetailedDescription';
export const RECALL_MODAL_CUSTOMER_ACTION = 'recallModalCustomerAction';
export const RECALL_MODAL_RISK = 'recallModalRiskIfNotRepaired';
export const RECALL_MODAL_RECALL_URLS = 'recallModalRecallUrls';

export const RECALL_MODAL_CODE = 'recallModalCode';
export const RECALL_MODAL_NAME = 'recallModalName';
export const RECALL_MODAL_SEVERITY = 'recallModalSeverity';
export const RECALL_MODAL_QUOTATION = 'recallModalQuotation';
export const RECALL_MODAL_AGREEMENT = 'recallModalAgreement';
export const RECALL_MODAL_DELIVERY_DATE = 'recallModalDeliveryDate';

// Due Bills
export const DUE_BILLS_TABLE = 'DUE_BILLS_TABLE'; // Table Item Key
export const DUE_BILL_ISSUE_DATE_FIELD = 'dueBillsIssueDate';
export const DUE_BILL_CODE_FIELD = 'dueBillsCode';
export const DUE_BILL_DESCRIPTION_FIELD = 'dueBillsDescription';
export const DUE_BILL_DISCLOSURE_TYPE_FIELD = 'dueBillsDisclosureType';
export const DUE_BILL_PAY_TYPE_FIELD = 'dueBillsPayType';
export const DUE_BILL_DEALS_SALE_PRICE_FIELD = 'dueBillsSalePrice';
export const DUE_BILL_DEALS_COST_PRICE_FIELD = 'dueBillsCostPrice';
export const DUE_BILL_TAXABLE_FIELD = 'dueBillsTaxable';
export const DUE_BILL_OPCODE_FIELD = 'dueBillsOpcode';
export const DUE_BILL_PART_CODE_FIELD = 'dueBillsPartCode';
export const DUE_BILL_STATUS_FIELD = 'dueBillsStatus';
export const DUE_BILL_RO_ID_FIELD = 'dueBillsRoId';
export const DUE_BILL_RO_COMPLETION_DATE_FIELD = 'dueBillsRoCompletionDate';

// Sales Info (Sales Details)
export const SALES_VEHICLE_TYPE = 'salesInfoSalesDetailsVehicleType';
export const SALES_PERSON = 'salesInfoSalesDetailsSalesPerson';
export const SELLING_DEALER = 'salesInfoSalesDetailsSellingDealer';
export const SELLING_DEALER_CODE = 'salesInfoSalesDetailsSellingDealerCode';
export const SELLING_DEALER_ADDRESS = 'salesInfoSalesDetailsSellingDealerAddress';
export const SELLING_DEALER_CITY = 'salesInfoSalesDetailsSellingDealerCity';
export const SELLING_DEALER_STATE = 'salesInfoSalesDetailsSellingDealerState';
export const SELLING_DEALER_POSTAL_CODE = 'salesInfoSalesDetailsSellingDealerPostalCode';
export const SALES_TYPE = 'salesInfoSalesDetailsSalesType';
export const PDI_REPAIR_ORDER = 'salesInfoSalesDetailsPdiRoNumber';
export const INVOICE_NUMBER = 'salesInfoSalesDetailsInvoiceNumber';
export const MANUFACTURING_NUMBER = 'salesInfoSalesDetailsManufacturingNumber';

// Sales Info - Allocated Party
export const SALES_ALLOCATED_DEALER_CODE = 'salesInfoAllocatedPartyAllocatedDealerCode';
export const SALES_ALLOCATED_DEALER = 'salesInfoAllocatedPartyAllocatedDealer';
export const SALES_ORGANIZATION_ID = 'salesInfoAllocatedPartyOrganizationId';
export const SALES_DEALER_CITY = 'salesInfoAllocatedPartyAllocatedDealerCity';
export const SALES_DEALER_ADDRESS = 'salesInfoAllocatedPartyAllocatedDealerAddress';
export const SALES_POSTAL_CODE = 'salesInfoAllocatedPartyAllocatedDealerPostalCode';
export const SALES_DEALER_STATE = 'salesInfoAllocatedPartyAllocatedDealerState';

// Sales Info - Drafted Party
export const SALES_DRAFTED_DEALER_CODE = 'salesInfoDraftedPartyDraftedDealerCode';
export const SALES_DRAFTED_DEALER = 'salesInfoDraftedPartyDraftedDealer';
export const SALES_DRAFTED_ORGN_ID = 'salesInfoDraftedPartyOrganizationId';
export const SALES_DRAFTED_PARTY_CITY = 'salesInfoDraftedPartyCity';
export const SALES_DRAFTED_PARTY_ADDRESS = 'salesInfoDraftedPartyAddress';
export const SALES_DRAFTED_POSTAL_CODE = 'salesInfoDraftedPartyPostalCode';
export const SALES_DRAFTED_DEALER_STATE = 'salesInfoDraftedPartyDealerState';

// Sales Info (price Details)
export const MSRP = 'salesInfoPriceDetailsMsrp';
export const BASE_RETAIL = 'salesInfoPriceDetailsBaseRetail';
export const BASE_INVOICE = 'salesInfoPriceDetailsBaseInvoice';
export const INVOICE_PRICE = 'salesInfoPriceDetailsInvoicePrice';
export const HOLD_BACK_AMOUNT = 'salesInfoPriceDetailsHoldbackAmount';
export const FREIGHT_COST = 'salesInfoPriceDetailsFreightCost';
export const COMMISSION = 'salesInfoPriceDetailsCommission';

// Sales Info (Financing)
export const PRICE = 'salesInfoFinancingDetailsPrice';
export const PAYMENT_TYPE = 'salesInfoFinancingDetailsPaymentType';
export const DOWN_PAYMENT = 'salesInfoFinancingDetailsDownPayment';
export const TERM = 'salesInfoFinancingDetailsTerms';
export const INTEREST_RATE = 'salesInfoFinancingDetailsInterestRate';
export const MONTHLY_PAYMENT = 'salesInfoFinancingDetailsMonthlyPayment';
export const LENDER_NAME = 'salesInfoFinancingDetailsLender';

// Buyers
export const BUYERS_TABLE = 'buyerDetails';
export const BUYERS_NAME_FIELD = 'buyersName';
export const BUYERS_PHONE_NUMBER_FIELD = 'buyersPhoneNumber';
export const BUYERS_EMAIL_FIELD = 'buyersEmail';

// Subscription
export const ON_STAR = 'subscriptionOnstarOnOff';
export const PHONE_NUMBER = 'subscriptionPhoneNumberUs';
export const PHONE_NUMBER_CA = 'subscriptionPhoneNumberCa';
export const ODO_METER = 'subscriptionOdometer';
export const OIL_LIFE = 'subscriptionOilLife';
export const DATE = 'subscriptionDate';
export const TIRE_PRESSURE = 'subscriptionTyrePressure';
export const XM = 'subscriptionXmOnOff';
export const RADIO_ID = 'subscriptionRadioId';
export const XM_PHONE_NUMBER = 'subscriptionXmPhoneNumber'; // Not in excel. Hardcoded
export const OWNER_CENTER = 'subscriptionOwnerCenterOnOff';
export const DEALER_MAINTENANCE_NOTIFICATION = 'subscriptionDealerMaintenanceNotification'; // Not in excel.
export const ADV_DIAG = 'subscriptionAdvDiagnostics'; // Not in excel.
export const PORSCHE_ROAD_SIDE_ASSISTANCE = 'subscriptionPorscheRoadSideAssistance';

// Symptom code information
export const SYMPTOM_CODE_INFO_TABLE = 'SYMPTOM_CODE_INFO_TABLE';
export const HEADER = 'symptomCodeInformationHeader';
export const SYMPTOM_CODE = 'symptomCodeInformationSymptomCode';
export const SYMPTOM_CODE_DESCRIPTION = 'symptomCodeInformationDescription';
export const SSM_NUMBER = 'symptomCodeInformationSsmNumber';
export const EFFECTIVE_DATE = 'symptomCodeInformationEffectiveDate';
export const SYMPTOM_CODE_TITLE = 'symptomCodeInformationTitle';
export const SYMPTOM_CODE_TEXT = 'symptomCodeInformationText';
export const SYMPTOM_CODE_DETAILS = 'symptomCodeInformationDetails';

// Condition Based Services
export const CONDITION_BASED_SERVICE_TABLE = 'CONDITION_BASED_SERVICE_TABLE';
export const CONDITION_BASED_SERVICE_ID = 'conditionBasedServicesId';
export const CONDITION_BASED_SERVICE_TITLE = 'conditionBasedServicesTitle';
export const CONDITION_BASED_SERVICE_DESCRIPTION = 'conditionBasedServicesDescription';
export const CONDITION_BASED_SERVICE_NAME = 'conditionBasedServicesName';
export const CONDITION_BASED_SERVICE_STATUS = 'conditionBasedServicesStatus';
export const CONDITION_BASED_SERVICE_REMAINING_TIME = 'conditionBasedServicesRemainingTime';
export const CONDITION_BASED_SERVICE_REMAINING_TIME_DUE_NEXT = 'conditionBasedServicesRemainingTimeDueNext';
export const CONDITION_BASED_SERVICE_REMAINING_DISTANCE = 'conditionBasedServicesRemainingDistance';
export const CONDITION_BASED_SERVICE_REMAINING_DISTANCE_DUE_NEXT = 'conditionBasedServicesRemainingDistanceDueNext';
export const CONDITION_BASED_SERVICE_FRU_NUMBER = 'conditionBasedServicesFruNumber';
export const CONDITION_BASED_SERVICE_FRU_QUANTITY = 'conditionBasedServicesFruQuantity';
export const CONDITION_BASED_SERVICE_LINKED_WITH = 'conditionBasedServicesLinkedWith';
export const CONDITION_BASED_SERVICE_DUE_DATE = 'conditionBasedServicesDueDate';
export const CONDITION_BASED_SERVICE_DUE_MILAGE = 'conditionBasedServicesDueMileage';
export const CONDITION_BASED_SERVICE_OPCODE = 'conditionBasedServicesOpcode';
export const CONDITION_BASED_SERVICE_TYPE = 'conditionBasedServicesType';

// Misc Constants
export const SOURCE_TYPE_TDP = 'TDP';

export const DEFAULT_OEM_CODE = 'DEFAULT';

export const GENERIC_OEM_CODE = 'generic';

export const COUNTRY_CODE = 'countryCode';

export const DEPARTMENT_CCC = 'CCC';

export const BUMPER_TO_BUMPER = 'Bumper to Bumper Limited Warranty';

export const TRUNK_KEYS_DEFAULT_VALUE = -1;

export const FIELDS_ELLIPSIS_LENGTH = 150;

export const VEHICLE_TAB_VALUE = {
  VEHICLE_DETAILS: 'VEHICLE_PROFILE_VEHICLE_DETAILS',
  VEHICLE_HEALTH: 'VEHICLE_PROFILE_VEHICLE_HEALTH',
  SERVICE_HISTORY: 'VEHICLE_PROFILE_SERVICE_HISTORY',
  SERVICE_MAINTENANCE: 'VEHICLE_PROFILE_VEHICLE_SERVICE_MAINTENANCE',
  WARRANTY_OEM: 'VEHICLE_PROFILE_WARRANTY_OEM',
  WARRANTY_DEALERSHIP: 'VEHICLE_PROFILE_WARRANTY_DEALERSHIP',
  MAINTENANCE: 'VEHICLE_PROFILE_MAINTENANCE',
  RECALLS: 'VEHICLE_PROFILE_RECALLS',
  DUE_BILLS: 'VEHICLE_PROFILE_DUE_BILLS',
  DEFFERED_RECOMMENDATIONS: 'DEFERRED_RECOMMENDATION_TABLE',
  CAMPAIGNS: 'VEHICLE_PROFILE_CAMPAIGNS',
  TITLE_BLOCK: 'VEHICLE_PROFILE_TITLE_BLOCK',
  SALES_INFO: 'VEHICLE_PROFILE_SALES_INFO',
  SUBSCRIPTION: 'VEHICLE_PROFILE_SUBSCRIPTION',
  SERVICE_BULLETINS: 'VEHICLE_PROFILE_SERVICE_BULLETINS',
  SYMPTOM_CODE_INFORMATION: 'VEHICLE_PROFILE_SYMPTOM_CODE_INFORMATION',
  CONDITION_BASED_SERVICE: 'VEHICLE_PROFILE_CONDITION_BASED_SERVICE',
};

export const VEHICLE_TAB_VALUE_VS_LABEL = {
  [VEHICLE_TAB_VALUE.VEHICLE_DETAILS]: __('Vehicle Details'),
  [VEHICLE_TAB_VALUE.VEHICLE_HEALTH]: __('Vehicle Health'),
  [VEHICLE_TAB_VALUE.SERVICE_HISTORY]: __('Service History'),
  [VEHICLE_TAB_VALUE.SERVICE_MAINTENANCE]: __('Service Maintenance'),
  [VEHICLE_TAB_VALUE.WARRANTY_OEM]: __('OEM Warranties'),
  [VEHICLE_TAB_VALUE.WARRANTY_DEALERSHIP]: __('Dealership Warranties'),
  [VEHICLE_TAB_VALUE.MAINTENANCE]: __('Scheduled Maintenance'),
  [VEHICLE_TAB_VALUE.RECALLS]: __('$$(Recalls)'),
  [VEHICLE_TAB_VALUE.DUE_BILLS]: __('$$(Due Bills)'),
  [VEHICLE_TAB_VALUE.SERVICE_BULLETINS]: __('Service Bulletins'),
  [VEHICLE_TAB_VALUE.CAMPAIGNS]: __('Special Service Campaign'),
  [VEHICLE_TAB_VALUE.SALES_INFO]: __('Sales Info'),
  [VEHICLE_TAB_VALUE.SUBSCRIPTION]: __('Subscription'),
  [VEHICLE_TAB_VALUE.SYMPTOM_CODE_INFORMATION]: __('Symptom Code Info'),
  [VEHICLE_TAB_VALUE.CONDITION_BASED_SERVICE]: __('Condition Based Services'),
};

export const FIELD_CONFIG_FIELD_NAME_KEY = 'fieldName';

export const FIELD_CONSTRAINTS = {
  APPLICABLE: 'R',
  NOT_APPLICABLE: 'NA',
};

export const DEALER_SERVICE_HISTORY = 'dealerServiceHistory';

export const COLUMN_CONFIG_ASSET_TYPES = {
  VEHICLE_PROFILE_SERVICE_HISTORY_OEM: 'VEHICLE_PROFILE_SERVICE_HISTORY_OEM',
  VEHICLE_PROFILE_SERVICE_HISTORY_DEALER: 'VEHICLE_PROFILE_SERVICE_HISTORY_DEALER',
  VEHICLE_PROFILE_SERVICE_HISTORY_DEALER_JOB_TABLE: 'VEHICLE_PROFILE_SERVICE_HISTORY_DEALER_JOB_TABLE',
  VEHICLE_PROFILE_WARRANTY_TABLE: 'VEHICLE_PROFILE_WARRANTY_TABLE',
  VEHICLE_PROFILE_CHILD_WARRANTY_TABLE: 'VEHICLE_PROFILE_CHILD_WARRANTY_TABLE',
  VEHICLE_PROFILE_EXTENDED_WARRANTY: 'VEHICLE_PROFILE_EXTENDED_WARRANTY',
  VEHICLE_PROFILE_SERVICE_HISTORY_OEM_JOB_TABLE: 'VEHICLE_PROFILE_SERVICE_HISTORY_OEM_JOB_TABLE',
  VEHICLE_PROFILE_SERVICE_HISTORY_OEM_PART_TABLE: 'VEHICLE_PROFILE_SERVICE_HISTORY_OEM_PART_TABLE',
  VEHICLE_PROFILE_SERVICE_BULLETINS: 'VEHICLE_PROFILE_SERVICE_BULLETINS',
};

// Asynchronous APIs Is Loading Field IDs
export const ASYNCHRONOUS_API_LOADING_STATUS_FIELDS = {
  VEHICLE_DATA_IS_LOADING: 'VEHICLE_DATA_IS_LOADING',
  RECALLS_IS_LOADING: 'RECALLS_IS_LOADING',
  DEFERRED_RECOMMENDATION_IS_LOADING: 'DEFERRED_RECOMMENDATION_IS_LOADING',
  SELLER_INFO_IS_LOADING: 'SELLER_INFO_IS_LOADING',
  SCHEDULED_MAINTENANCE_LIST_IS_LOADING: 'SCHEDULED_MAINTENANCE_LIST_IS_LOADING',
  DUE_BILLS_IS_LOADING: 'DUE_BILLS_IS_LOADING',
  EXTERNAL_CONTRACT_IS_LOADING: 'EXTERNAL_CONTRACT_IS_LOADING',
};

// Subsection IDs
export const INSPECTION_HISTORY = 'INSPECTION_HISTORY';
export const MULTILINGUAL_WARNING_COLOR = '#FF88001A';

export const FIELDS_SOURCE_EXTERNAL = new Set([
  ALERTS_LIST,
  VIN,
  VEHICLE_VERSION_SERIES,
  COLOR_ITEM_CODE_FIELD,
  INTERIOR_COLOR,
  EXTERIOR_COLOR,
  FINISH_COLOR,
  TOP_COLOR,
  BODY_COLOR_CODE,
  IN_SERVICE_DATE,
  IN_SERVICE_DATE_RAW,
  IN_SERVICE_ODOMETER,
  SOLD_DATE,
  LAST_SERVICE_DATE,
  LAST_SERVICE_DATE_RAW,
  LAST_ODOMETER_READING_MILES,
  LAST_ODOMETER_READING_MILES_RAW,
  CURRENT_ODOMETER_READING,
  VEHICLE_NOTE,
  TRIM,
  BRANDING_STATE_CODE,
  BRANDING_TITLE,
  BRANDING_STATE_REFERENCE,
  SUB_MODEL_TYPE,
  OVERVIEW_VEHICLE_BODY_CLASS,
  MODEL_KEY,
  OEM_CODE,
  CHASSIS_NUMBER,
  HULL_NUMBER,
  SERIAL_NUMBER,
  RV_TYPE,
  MANUFACTURER,
  LAST_SERVICE_ENGINE_HOURS,
  DRIVE_TYPE,
  BODY_TYPE,
  WEIGHT,
  PAINT_COLOR,
  PAINT_CODE,
  UPHOLSTRY_CODE,
  UPHOLSTRY_DESCRIPTION,
  BUILD_HOUR,
  ETA,
  DRAFTED,
  RETAILER_DEMO,
  SOA_DEMO,
  TRANSMISSION,
  TRANSMISSION_CODE,
  ENGINE_NUMBER,
  ENGINE_CODE,
  ENGINE_CODE_EXTENSION,
  ENGINE_CAPACITY,
  ENGINE_POWER,
  ENGINE_POINT_OF_FITMENT,
  ENGINE_ASSEMBLY_POSITION,
  ENGINE,
  ENGINE_CALIBRATION,
  KEY_CODE_IGNITION,
  KEY_CODE_TRUNK,
  IS_MULTI_ENGINE,
  RADIO,
  TRUNK_KEYS,
  AXLE_CODE,
  AXLE_RATIO,
  FUEL_TYPE,
  WHEEL_SIZE,
  TIRE,
  DELIVERY_DATE,
  SOLD_ORDER,
  DELIVERY_TYPE_DESCRIPTION,
  BUILD_ORDER_ID,
  MODEL_DESCRIPTION,
  BUILD_PLANT_CODE,
  VEHICLE_CONDITION_CODE,
  MODEL_NUMBER,
  EMISSION_CERTIFICATE,
  BRANDED_VEHICLE,
  FOREIGN_DISTRIBUTION_VEHICLE,
  MANUFACTURER_DATE,
  RETAIL_DATE,
  PDI_DATE,
  SOLD_TO_FLEET,
  RETAIL_SALES_TYPE,
  SYNC_VERSION,
  VHR_ACTIVATED,
  MODEM,
  MARKET_VEHICLE,
  ASPIRATION,
  KEY_WRITE_DATE,
  KEY_READ_ERROR,
  ORIGINAL_MODEL,
  CHANGE_DATE,
  CHANGE_USER,
  CHANGES_TO_MODEL,
  BOAT_NAME,
  LENGTH,
  LAYOUT,
  ADDITIONAL_DETAILS_ENGINE_NUMBER,
  PRODUCTION_FLEXIBILITY_MODEL_OPTION,
  PRODUCTION_FLEXIBILITY_CHANGE_DATE,
  PRODUCTION_FLEXIBILITY_CHANGE_USER,
  PRODUCTION_FLEXIBILITY_CHANGE_MODEL_OPTION,
  CAPABILITIES_TELEMETICS,
  CAPABILITIES_TELESERVICE,
  CAPABILITIES_TELESERVICE_CONTRACT,
  CAPABILITIES_RTC,
  WIFI_SUBSCRIPTION,
  COMM_CHECKED_DATE,
  DCM_VERSION_NUMBER,
  PRODUCT_NAME,
  PRODUCT_END_DATE,
  TELEMATICS_SUBSCRIPTION_STATUS,
  PRODUCT_START_DATE,
  ASSOCIATED_CUSTOMER_TABLE,
  ENGINE_OIL_QUANTITY_TABLE,
  REPLACE_ENGINE_OIL_TABLE,
  SMARTKEY_BATTERY_TABLE,
  WARNING_LIST_TABLE,
  VEHICLE_HEALTH_MILEAGE,
  VEHICLE_HEALTH_AVG,
  VEHICLE_HEALTH_LONG_TERM_AVG,
  VEHICLE_HEALTH_FUEL,
  VEHICLE_HEALTH_BATTERY,
  VEHICLE_HEALTH_QUANTITY_OF_ENGINE_OIL_DESCRIPTION_FIELD,
  VEHICLE_HEALTH_QUANTITY_OF_ENGINE_OIL_ICON_FIELD,
  VEHICLE_HEALTH_QUANTITY_OF_ENGINE_OIL_LAST_UPDATED_TIME_FIELD,
  VEHICLE_HEALTH_QUANTITY_OF_ENGINE_OIL_STATUS_FIELD,
  VEHICLE_HEALTH_REPLACE_ENGINE_OIL_DESCRIPTION_FIELD,
  VEHICLE_HEALTH_REPLACE_ENGINE_OIL_ICON_FIELD,
  VEHICLE_HEALTH_REPLACE_ENGINE_OIL_LAST_UPDATED_TIME_FIELD,
  VEHICLE_HEALTH_REPLACE_ENGINE_OIL_STATUS_FIELD,
  VEHICLE_HEALTH_SMARTKEY_BATTERY_DESCRIPTION_STATUS_FIELD,
  VEHICLE_HEALTH_SMARTKEY_BATTERY_ICON_FIELD,
  VEHICLE_HEALTH_SMARTKEY_BATTERY_LAST_UPDATED_TIME_FIELD,
  VEHICLE_HEALTH_SMARTKEY_BATTERY_STATUS_FIELD,
  VEHICLE_HEALTH_WARNING_LAST_UPDATED_TIME_FIELD,
  VEHICLE_HEALTH_WARNING_LIST_FIELD,
  SERVICE_HISTORY_OEM,
  SERVICE_HISTORY_OEM_PART_TABLE,
  SERVICE_HISTORY_OEM_JOB_TABLE,
  WARRANTY_TABLE,
  WARRANTY_COVERAGE_TABLE,
  WARRANTY_HISTORY,
  VEHICLE_WARRANTY_TRANSFER,
  EXTENDED_WARRANTY,
  ADDED_SECURITY,
  EXCLUSION,
  ELIGIBILE_CONTRACTS,
  INSURANCE_TABLE,
  SERVICE_INFORMATION_TABLE,
  SCHEDULED_MAINTENANCE_LIST,
  CAMPAIGNS_TABLE,
  SERVICE_BULLETINS,
  RECALLS_TABLE,
  BUYERS_TABLE,
  SYMPTOM_CODE_INFO_TABLE,
  CONDITION_BASED_SERVICE_TABLE,
  SALES_PERSON,
  SALES_VEHICLE_TYPE,
  SELLING_DEALER,
  SELLING_DEALER_CODE,
  SELLING_DEALER_ADDRESS,
  SELLING_DEALER_CITY,
  SELLING_DEALER_STATE,
  SELLING_DEALER_POSTAL_CODE,
  SALES_TYPE,
  PDI_REPAIR_ORDER,
  INVOICE_NUMBER,
  MANUFACTURING_NUMBER,
  SALES_ALLOCATED_DEALER_CODE,
  SALES_ALLOCATED_DEALER,
  SALES_ORGANIZATION_ID,
  SALES_DEALER_CITY,
  SALES_DEALER_ADDRESS,
  SALES_POSTAL_CODE,
  SALES_DEALER_STATE,
  SALES_DRAFTED_DEALER_CODE,
  SALES_DRAFTED_DEALER,
  SALES_DRAFTED_ORGN_ID,
  SALES_DRAFTED_PARTY_CITY,
  SALES_DRAFTED_PARTY_ADDRESS,
  SALES_DRAFTED_POSTAL_CODE,
  SALES_DRAFTED_DEALER_STATE,
  MSRP,
  BASE_RETAIL,
  BASE_INVOICE,
  INVOICE_PRICE,
  HOLD_BACK_AMOUNT,
  FREIGHT_COST,
  COMMISSION,
  PRICE,
  PAYMENT_TYPE,
  DOWN_PAYMENT,
  TERM,
  INTEREST_RATE,
  MONTHLY_PAYMENT,
  LENDER_NAME,
  ON_STAR,
  PHONE_NUMBER,
  PHONE_NUMBER_CA,
  ODO_METER,
  OIL_LIFE,
  DATE,
  TIRE_PRESSURE,
  XM,
  RADIO_ID,
  XM_PHONE_NUMBER,
  OWNER_CENTER,
  DEALER_MAINTENANCE_NOTIFICATION,
  ADV_DIAG,
  PORSCHE_ROAD_SIDE_ASSISTANCE,
  FLEET_NUMBER,
]);
