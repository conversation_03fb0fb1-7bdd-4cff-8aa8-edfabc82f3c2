import * as VEHIC<PERSON>_PROFILE_FIELDS from './vehicleProfileFields.constants';
import * as DEFERRED_RECOMMENDATIONS_FIELDS from '../recommendations';

// These sections and sub sections were added for Vehicle Profle Configurator

const ALERTS = 'alerts';
const EXTERNAL_LINKS = 'externalLinks';
const VEHICLE_DETAILS_OVERVIEW = 'vehicleDetailsOverview';
const VEHICLE_DETAILS_ADDITIONAL = 'vehicleDetailsAdditional';
const VEHICLE_DETAILS_ENGINE = 'vehicleDetailsEngine';
const VEHICLE_HEALTH = 'vehicleHealth';
const CAPABILITIES = 'capabilities';
const TELEMATICS_SUBSCRIPTION = 'telematicsSubscription';
const ASSOCIATED_CUSTOMERS = 'associatedCustomers';
const SERVICE_HISOTRY_OEM = 'serviceHistoryOem';
const SERVICE_HISTORY_DEALER = 'serviceHistoryDealer';
const DEFERRED_RECOMMENDATIONS = 'deferredRecommendations';
const WARRANTY_DEALERSHIP = 'warrantyDealership';
const WARRANTY_WARRANTIES = 'warrantyWarranties';
const WARRANTY_COVERAGE = 'warrantyCoverage';
const WARRANTY_HISTORY = 'warrantyHistory';
const VEHICLE_WARRANTY_TRANSFER = 'vehicleWarrantyTransfer';
const EXTENDED_SERVICE_CONTRACT_INFORMATION = 'extendedServiceContractInformation';
const ADDED_SECURITY = 'addedSecurity';
const EXCLUSIONS = 'exclusions';
const SUBARU_EQUITY_SHIELD = 'subaruEquityShield';
const ELIGIBLE_CONTRACTS = 'eligibleContracts';
const INSURANCE = 'insurance';
const SERVICE_INFORMATION = 'serviceInformation';
const SCHEDULED_MAINTENANCE_LIST = 'scheduledMaintenanceList';
const SPECIAL_SERVICE_CAMPAIGN = 'specialServiceCampaign';
const SERVICE_BULLETINS = 'serviceBulletins';
const RECALLS = 'recalls';
const DUE_BILLS = 'dueBills';
const SALES_INFO_SALES_DETAILS = 'salesInfoSalesDetails';
const SALES_INFO_ALLOCATED_PARTY = 'salesInfoAllocatedParty';
const SALES_INFO_DRAFTED_PARTY = 'salesInfoDraftedParty';
const SALES_INFO_PRICE_DETAILS = 'salesInfoPriceDetails';
const SALES_INFO_FINANCING_DETAILS = 'salesInfoFinancingDetails';
const BUYERS = 'buyers';
const SUBSCRIPTION = 'subscription';
const SYMPTOM_CODE_INFORMATION = 'symptomCodeInformation';
const CONDITION_BASED_SERVICES = 'conditionBasedServices';
const RECALL_MODAL = 'recallModal';
const HEADER = 'header';
const NOT_FOR_VIN_LOOKUP = 'notForVinLookup';
const MARKETING_MESSAGES = 'marketingMessages';
const SEGMENT = 'segment';
const MISC_SECTION = 'MISC_SECTION';

export const VEHICLE_PROFILE_SECTIONS_LABELS = {
  [ALERTS]: __('Alerts'),
  [EXTERNAL_LINKS]: __('External Links'),
  [VEHICLE_DETAILS_OVERVIEW]: __('Vehicle Details - Overview'),
  [VEHICLE_DETAILS_ADDITIONAL]: __('Vehicle Details - Additional'),
  [VEHICLE_DETAILS_ENGINE]: __('Vehicle Details - Engine'),
  [VEHICLE_HEALTH]: __('Vehicle Health'),
  [CAPABILITIES]: __('Capabilities'),
  [TELEMATICS_SUBSCRIPTION]: __('Telematics Subscription'),
  [ASSOCIATED_CUSTOMERS]: __('Associated Customers'),
  [SERVICE_HISOTRY_OEM]: __('Service History - OEM'),
  [SERVICE_HISTORY_DEALER]: __('Service History - $$(Dealer)'),
  [DEFERRED_RECOMMENDATIONS]: __('Deferred Recommendations'),
  [WARRANTY_DEALERSHIP]: __('Warranty - Dealership'),
  [WARRANTY_WARRANTIES]: __('Warranty - Warranties'),
  [WARRANTY_COVERAGE]: __('Warranty - Coverage'),
  [WARRANTY_HISTORY]: __('Warranty - History'),
  [VEHICLE_WARRANTY_TRANSFER]: __('Vehicle Warranty Transfer'),
  [EXTENDED_SERVICE_CONTRACT_INFORMATION]: __('Extended Service Contract Information'),
  [ADDED_SECURITY]: __('Added Security'),
  [EXCLUSIONS]: __('Exclusions'),
  [SUBARU_EQUITY_SHIELD]: __('Subaru Equity Shield'),
  [ELIGIBLE_CONTRACTS]: __('Eligible Contracts'),
  [INSURANCE]: __('Insurance'),
  [SERVICE_INFORMATION]: __('Service Information'),
  [SCHEDULED_MAINTENANCE_LIST]: __('Scheduled Maintenance List'),
  [SPECIAL_SERVICE_CAMPAIGN]: __('Special Service Campaign'),
  [SERVICE_BULLETINS]: __('Service Bulletins'),
  [RECALLS]: __('Recalls'),
  [DUE_BILLS]: __('Due Bills'),
  [SALES_INFO_SALES_DETAILS]: __('Sales Info - Sales Details'),
  [SALES_INFO_ALLOCATED_PARTY]: __('Sales Info - Allocated Party'),
  [SALES_INFO_DRAFTED_PARTY]: __('Sales Info - Drafted Party'),
  [SALES_INFO_PRICE_DETAILS]: __('Sales Info - Price Details'),
  [SALES_INFO_FINANCING_DETAILS]: __('Sales Info - Financing Details'),
  [BUYERS]: __('Buyers'),
  [SUBSCRIPTION]: __('Subscription'),
  [SYMPTOM_CODE_INFORMATION]: __('Symptom Code Information'),
  [CONDITION_BASED_SERVICES]: __('Condition Based Services'),
  [RECALL_MODAL]: __('Recall Modal'),
  [HEADER]: __('Header'),
  [NOT_FOR_VIN_LOOKUP]: __('Not For Vin Lookup'),
  [MARKETING_MESSAGES]: __('Marketing Messages'),
  [SEGMENT]: __('Segment'),
  [MISC_SECTION]: __('Miscellaneous'),
};

// These Fields were added for Vehicle Profile Configurator
// Any new fields should be added in vehicleProfileFields.constants.js

// Vehicle Details Overview
const VEHICLE_DETAILS_OVERVIEW_VEHICLE_DESCRIPTION = 'vehicleDetailsOverviewVehicleDescription';
const VEHICLE_DETAILS_OVERVIEW_LAST_ODOMETER_READING_DATE = 'vehicleDetailsOverviewLastOdometerReadingDate';
const VEHICLE_DETAILS_OVERVIEW_MANUFACTURING_COUNTRY = 'vehicleDetailsOverviewManufacturingCountry';

// Vehicle Details Additional
const VEHICLE_DETAILS_ADDITIONAL_MODEL_YEAR = 'vehicleDetailsAdditionalModelYear';
const VEHICLE_DETAILS_ADDITIONAL_CAR_LINE = 'vehicleDetailsAdditionalCarLine';
const VEHICLE_DETAILS_ADDITIONAL_CHRYSLER_VEHICLE = 'vehicleDetailsAdditionalChryslerVehicle';

// Vehicle Details Engine
const VEHICLE_DETAILS_ENGINE_ENGINE_CALIBRATION = 'vehicleDetailsEngineEngineCalibration';

// Service History
const SERVICE_HISTORY_OEM_DEPARTMENT_TYPE = 'serviceHistoryOemDepartmentType';
const SERVICE_HISTORY_OEM_REPAIR_ORDER_INTERNAL_REMARKS_COMMENT = 'serviceHistoryOemRepairOrderInternalComment';
const SERVICE_HISTORY_OEM_JOB_HISTORY_RECORD = 'serviceHistoryOemJobHistoryRecord';
const SERVICE_HISTORY_OEM_JOB_COMMENT_RECORD = 'serviceHistoryOemJobCommentRecord';
const SERVICE_HISTORY_OEM_PARTS_INFORMATION_RECORD = 'serviceHistoryOemPartsInformationRecord';
const SERVICE_HISTORY_OEM_LABOR_DETAILS_RECORD = 'serviceHistoryOemLaborDetailRecord';

// Deferred Recommendations
const DEFERRED_RECOMMENDATIONS_DEFERRED_ON = 'deferredRecommendationsDeferredOn';

// Warranty History
const WARRANTY_HISTORY_ODOMETER_INDICATOR = 'warrantyHistoryOdometerIndicator';
const WARRANTY_HISTORY_REPAIR_HISTORY = 'warrantyHistoryRepairHistory';

// Special Service Campaign
const SPECIAL_SERVICE_CAMPAIGN_LABOR_TIME = 'specialServiceCampaignLaborTime';

// Service Bulletins
const SERVICE_BULLETINS_SERVICE_BULLETIN_URL = 'serviceBulletinsServiceBulletinUrl';

// Recalls
const RECALLS_RECALL_DOCUMENT = 'recallsRecallDocument';
const RECALLS_BULLETIN_ID = 'recallsBulletinId';
const RECALLS_TECHNICAL_NOTE_NUMBER = 'recallsTechnicalNoteNumber';

// Sales Info Sales Details
const SALES_INFO_SALES_DETAILS_PURCHASE_DATE = 'salesInfoSalesDetailsPurchaseDate';

// Buyers
const BUYERS_ADDRESS = 'buyersAddress';
const BUYERS_CITY = 'buyersCity';
const BUYERS_STATE = 'buyersState';
const BUYERS_POSTAL_CODE = 'buyersPostalCode';

// Header
const HEADER_RECALLS_SEVERITY = 'headerRecallsSeverity';
const HEADER_STOCK_ID = 'headerStockId';
const HEADER_LICENSE_PLATE = 'headerLicensePlate';

export const VEHICLE_PROFILE_FIELDS_LABELS = {
  // Alerts
  [VEHICLE_PROFILE_FIELDS.ALERTS_LIST]: __('Alerts'),

  // Externel Links
  [VEHICLE_PROFILE_FIELDS.EXTERNAL_LINKS]: __('External Links'),

  // Vehicle Details - Overview
  [VEHICLE_PROFILE_FIELDS.CHASSIS_NUMBER]: __('Chassis #'),
  [VEHICLE_PROFILE_FIELDS.SERIAL_NUMBER]: __('Serial #'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_DETAILS_MANUFACTURING_NUMBER]: __('Manufacturing Number'),
  [VEHICLE_PROFILE_FIELDS.MANUFACTURER]: __('Manufacturer'),
  [VEHICLE_PROFILE_FIELDS.LAST_SERVICE_ENGINE_HOURS]: __('Last Engine Meter Out (hrs)'),
  [VEHICLE_PROFILE_FIELDS.RV_TYPE]: __('Type'),
  [VEHICLE_PROFILE_FIELDS.HULL_NUMBER]: __('Hull #'),
  [VEHICLE_PROFILE_FIELDS.VIN]: __('VIN'),
  [VEHICLE_PROFILE_FIELDS.FIN]: __('FIN'),
  [VEHICLE_DETAILS_OVERVIEW_VEHICLE_DESCRIPTION]: __('Vehicle Description'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_VERSION_SERIES]: __('Version / Series'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_STOCK_NUMBER]: __('Vehicle Stock Number'),
  [VEHICLE_PROFILE_FIELDS.LICENSE]: __('License'),
  [VEHICLE_PROFILE_FIELDS.INTERIOR_COLOR]: __('Interior Color'),
  [VEHICLE_PROFILE_FIELDS.EXTERIOR_COLOR]: __('Exterior Color'),
  [VEHICLE_PROFILE_FIELDS.FINISH_COLOR]: __('Finish Color'),
  [VEHICLE_PROFILE_FIELDS.TOP_COLOR]: __('Top Color'),
  [VEHICLE_PROFILE_FIELDS.BODY_COLOR_CODE]: __('Body Color Code'),
  [VEHICLE_PROFILE_FIELDS.IN_SERVICE_DATE]: __('$$(In Service Date)'),
  [VEHICLE_PROFILE_FIELDS.KEY_WRITE_DATE]: __('Key Write Date'),
  [VEHICLE_PROFILE_FIELDS.KEY_READ_DATE]: __('Key Read Date'),
  [VEHICLE_PROFILE_FIELDS.SOLD_DATE]: __('Sold Date'),
  [VEHICLE_PROFILE_FIELDS.IN_SERVICE_ODOMETER]: __('In Service Odometer'),
  [VEHICLE_PROFILE_FIELDS.LAST_SERVICE_DATE]: __('Last Service Date'),
  [VEHICLE_PROFILE_FIELDS.LAST_ODOMETER_READING_MILES]: __('Last Service Odometer'),
  [VEHICLE_DETAILS_OVERVIEW_LAST_ODOMETER_READING_DATE]: __('Last Odometer Reading Date'),
  [VEHICLE_PROFILE_FIELDS.CURRENT_ODOMETER_READING]: __('Miles | Current Odometer'),
  [VEHICLE_PROFILE_FIELDS.RFID]: __('RFID'),
  [VEHICLE_PROFILE_FIELDS.YEAR]: __('Year'),
  [VEHICLE_PROFILE_FIELDS.MAKE]: __('$$(Make)'),
  [VEHICLE_PROFILE_FIELDS.MODEL]: __('Model'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_NOTE]: __('Vehicle Note'),
  [VEHICLE_PROFILE_FIELDS.BRANDING_STATE_CODE]: __('Branding State Code'),
  [VEHICLE_PROFILE_FIELDS.BRANDING_TITLE]: __('Branding Title'),
  [VEHICLE_PROFILE_FIELDS.BRANDING_STATE_REFERENCE]: __('Branding State Reference'),
  [VEHICLE_PROFILE_FIELDS.TRIM]: __('Trim'), // no label needed for vehicle profile
  [VEHICLE_PROFILE_FIELDS.MODEL_KEY]: __('Model Key'),
  [VEHICLE_PROFILE_FIELDS.OEM_CODE]: __('OEM Code'),
  [VEHICLE_PROFILE_FIELDS.SUB_MODEL_TYPE]: __('Sub-Model Type'),
  [VEHICLE_PROFILE_FIELDS.OVERVIEW_VEHICLE_BODY_CLASS]: __('Body Class'),
  [VEHICLE_PROFILE_FIELDS.OVERVIEW_VEHICLE_TYPE]: __('Vehicle Type'),
  [VEHICLE_PROFILE_FIELDS.FLEET_NUMBER]: __('Fleet Number'),
  [VEHICLE_DETAILS_OVERVIEW_MANUFACTURING_COUNTRY]: __('Manufacturing Country'),
  [VEHICLE_PROFILE_FIELDS.TAPV]: __('TAPV'),
  [VEHICLE_PROFILE_FIELDS.TVV]: __('TVV'),
  [VEHICLE_PROFILE_FIELDS.HYUNDAI_SALE_CODE]: __('Hyundai Sale Code (HSC)'),
  [VEHICLE_PROFILE_FIELDS.MOT_DATE]: __('MOT Date'),
  [VEHICLE_PROFILE_FIELDS.NEXT_MOT_DATE]: __('Next MOT Date'),
  [VEHICLE_PROFILE_FIELDS.TAX_DUE_DATE]: __('Tax Due Date[[VEHICLE_PROFILE]]'),
  [VEHICLE_PROFILE_FIELDS.NEXT_SERVICE_DATE]: __('Next Service Date'),
  [VEHICLE_PROFILE_FIELDS.LAST_VISIT_DATE]: __('Last Visit Date'),
  [VEHICLE_PROFILE_FIELDS.POLICY_NUMBER]: __('Policy Number'),
  [VEHICLE_PROFILE_FIELDS.ORDER_NUMBER]: __('Order Number'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_KIND]: __('Vehicle Kind'),
  [VEHICLE_PROFILE_FIELDS.SIGMA_CARD_NUMBER]: __('Sigma Card Number'),
  [VEHICLE_PROFILE_FIELDS.PAINT_QUALITY]: __('Finish Color'),
  [VEHICLE_PROFILE_FIELDS.REFRIGERANT_GAS_TYPE]: __('Refrigerant Gas Type'),
  [VEHICLE_PROFILE_FIELDS.BATTERY_CAPACITY]: __('Battery Capacity'),
  [VEHICLE_PROFILE_FIELDS.SPARE_WHEEL]: __('Spare Wheel'),

  // Vehicle Details - Additional
  [VEHICLE_PROFILE_FIELDS.DOOR_COUNT]: __('No. of Doors'),
  [VEHICLE_PROFILE_FIELDS.DRIVE_TYPE]: __('Drive Type'),
  [VEHICLE_PROFILE_FIELDS.BODY_TYPE]: __('Body Type'),
  [VEHICLE_PROFILE_FIELDS.WEIGHT]: __('Weight'),
  [VEHICLE_PROFILE_FIELDS.PAINT_COLOR]: __('Paint Color'),
  [VEHICLE_PROFILE_FIELDS.PAINT_CODE]: __('Paint Code'),
  [VEHICLE_PROFILE_FIELDS.UPHOLSTRY_CODE]: __('Upholstry Code'),
  [VEHICLE_PROFILE_FIELDS.UPHOLSTRY_DESCRIPTION]: __('Upholstry Description'),
  [VEHICLE_PROFILE_FIELDS.ADDITIONAL_EQUIPMENTS]: __('Additional Equipment(s)'),
  [VEHICLE_PROFILE_FIELDS.OPTIONS]: __('Option(s)'),
  [VEHICLE_PROFILE_FIELDS.BUILD_DATE]: __('Build Date'),
  [VEHICLE_PROFILE_FIELDS.BUILD_HOUR]: __('Build Hour'),
  [VEHICLE_PROFILE_FIELDS.ETA]: __('ETA'),
  [VEHICLE_PROFILE_FIELDS.DRAFTED]: __('Drafted'),
  [VEHICLE_PROFILE_FIELDS.RETAILER_DEMO]: __('Retailer Demo'),
  [VEHICLE_PROFILE_FIELDS.SOA_DEMO]: __('SOA Demo'),
  [VEHICLE_PROFILE_FIELDS.TRANSMISSION_CODE]: __('Transmission Code'),
  [VEHICLE_PROFILE_FIELDS.TRANSMISSION_TYPE_CODE]: __('Transmission Type Code'),
  [VEHICLE_PROFILE_FIELDS.TRANSMISSION]: __('Transmission'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_STATUS]: __('Vehicle Status'),
  [VEHICLE_PROFILE_FIELDS.SUB_CATEGORY]: __('Sub Category'),
  [VEHICLE_PROFILE_FIELDS.RADIO]: __('Radio'),
  [VEHICLE_PROFILE_FIELDS.RADIO_CODE]: __('Radio Code'),
  [VEHICLE_PROFILE_FIELDS.TRUNK_KEYS]: __('Trunk Keys'),
  [VEHICLE_PROFILE_FIELDS.KEY_CODE_IGNITION]: __('Key Code (Ignition)'),
  [VEHICLE_PROFILE_FIELDS.KEY_CODE_TRUNK]: __('Key Code (Trunk)'),
  [VEHICLE_PROFILE_FIELDS.AXLES_COUNT]: __('No. of Axles'),
  [VEHICLE_PROFILE_FIELDS.AXLE_CODE]: __('Axle Code'),
  [VEHICLE_PROFILE_FIELDS.AXLE_RATIO]: __('Axle Ratio'),
  [VEHICLE_PROFILE_FIELDS.CYLINDERS]: __('Cylinders'),
  [VEHICLE_PROFILE_FIELDS.FUEL_TYPE]: __('Fuel Type'),
  [VEHICLE_PROFILE_FIELDS.WHEEL_SIZE]: __('Wheel Size'),
  [VEHICLE_PROFILE_FIELDS.TIRE]: __('Tire'),
  [VEHICLE_PROFILE_FIELDS.DELIVERY_DATE]: __('Delivery Date'),
  [VEHICLE_PROFILE_FIELDS.DELIVERY_TYPE_DESCRIPTION]: __('Delivery Type Description'),
  [VEHICLE_PROFILE_FIELDS.SOLD_ORDER]: __('Sold Order'),
  [VEHICLE_PROFILE_FIELDS.BUILD_ORDER_ID]: __('Build Order Number ID'),
  [VEHICLE_PROFILE_FIELDS.MODEL_DESCRIPTION]: __('Model Description'),
  [VEHICLE_PROFILE_FIELDS.BUILD_PLANT_CODE]: __('Build Plant Code'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_CONDITION_CODE]: __('Vehicle Condition Code'),
  [VEHICLE_PROFILE_FIELDS.MODEL_NUMBER]: __('Model Number'),
  [VEHICLE_DETAILS_ADDITIONAL_MODEL_YEAR]: __('Model Year'),
  [VEHICLE_PROFILE_FIELDS.EMISSION_CERTIFICATE]: __('Emission Certificate'),
  [VEHICLE_PROFILE_FIELDS.BRANDED_VEHICLE]: __('Branded Vehicle'),
  [VEHICLE_PROFILE_FIELDS.FOREIGN_DISTRIBUTION_VEHICLE]: __('Foreign Distribution Vehicle'),
  [VEHICLE_PROFILE_FIELDS.MANUFACTURER_DATE]: __('Manufacturer Date'),
  [VEHICLE_PROFILE_FIELDS.RETAIL_DATE]: __('Retail Date'),
  [VEHICLE_PROFILE_FIELDS.PRODUCTION_DATE]: __('Production Date'),
  [VEHICLE_PROFILE_FIELDS.REGISTRATION_DATE]: __('Registration Date'),
  [VEHICLE_PROFILE_FIELDS.PDI_DATE]: __('$$(PDI) Date'),
  [VEHICLE_PROFILE_FIELDS.SOLD_TO_FLEET]: __('Sold To Fleet'),
  [VEHICLE_PROFILE_FIELDS.RETAIL_SALES_TYPE]: __('Retail Sales Type'),
  [VEHICLE_PROFILE_FIELDS.SYNC_VERSION]: __('Sync Version'),
  [VEHICLE_PROFILE_FIELDS.VHR_ACTIVATED]: __('VHR Activated'),
  [VEHICLE_PROFILE_FIELDS.MODEM]: __('Modem'),
  [VEHICLE_DETAILS_ADDITIONAL_CAR_LINE]: __('Car Line'),
  [VEHICLE_DETAILS_ADDITIONAL_CHRYSLER_VEHICLE]: __('Chrysler Vehicle'),
  [VEHICLE_PROFILE_FIELDS.MARKET_VEHICLE]: __('Market Vehicle'),
  [VEHICLE_PROFILE_FIELDS.LENGTH]: __('Length'),
  [VEHICLE_PROFILE_FIELDS.LAYOUT]: __('Layout'),
  [VEHICLE_PROFILE_FIELDS.BOAT_NAME]: __('Boat Name'),
  [VEHICLE_PROFILE_FIELDS.ENGINE_CALIBRATION]: __('Engine Calibration'),
  [VEHICLE_PROFILE_FIELDS.ENGINE_DESCRIPTION]: __('Engine Description'),
  [VEHICLE_PROFILE_FIELDS.ADDITIONAL_DETAILS_ENGINE_NUMBER]: __('Engine Number'),
  [VEHICLE_PROFILE_FIELDS.ASPIRATION]: __('Aspiration'),
  [VEHICLE_PROFILE_FIELDS.ORIGINAL_MODEL]: __('Original Model - Option-Spec-Color'),
  [VEHICLE_PROFILE_FIELDS.CHANGE_DATE]: __('Change Date'),
  [VEHICLE_PROFILE_FIELDS.CHANGE_USER]: __('Change User'),
  [VEHICLE_PROFILE_FIELDS.CHANGES_TO_MODEL]: __('Changes To Model - Option-Spec-Color'),
  [VEHICLE_PROFILE_FIELDS.NEW_ENGINE_NUMBER]: __('New Engine Number'),
  [VEHICLE_PROFILE_FIELDS.GEARBOX_NUMBER]: __('Gearbox Number'),
  [VEHICLE_PROFILE_FIELDS.NEW_GEARBOX_NUMBER]: __('New Gearbox Number'),
  [VEHICLE_PROFILE_FIELDS.BIN_NUMBER]: __('BIN Number'),
  [VEHICLE_PROFILE_FIELDS.NEW_BIN_NUMBER]: __('New BIN Number'),

  // Production Flexibility
  [VEHICLE_PROFILE_FIELDS.PRODUCTION_FLEXIBILITY_MODEL_OPTION]: __('Original Model - Option-Spec-Color'),
  [VEHICLE_PROFILE_FIELDS.PRODUCTION_FLEXIBILITY_CHANGE_DATE]: __('Change Date'),
  [VEHICLE_PROFILE_FIELDS.PRODUCTION_FLEXIBILITY_CHANGE_USER]: __('Change User'),
  [VEHICLE_PROFILE_FIELDS.PRODUCTION_FLEXIBILITY_CHANGE_MODEL_OPTION]: __('Changes to Model - Option-Spec-Color'),

  // Vehicle Details - Engine
  [VEHICLE_PROFILE_FIELDS.ENGINE]: __('Engine'),
  [VEHICLE_PROFILE_FIELDS.ENGINE_ENGINE_TYPE]: __('Engine Type'),
  [VEHICLE_PROFILE_FIELDS.ENGINE_NUMBER]: __('Number'),
  [VEHICLE_PROFILE_FIELDS.ENGINE_CODE_EXTENSION]: __('Engine Code Extension'),
  [VEHICLE_PROFILE_FIELDS.ENGINE_POWER]: __('Power'),
  [VEHICLE_PROFILE_FIELDS.ENGINE_ASSEMBLY_POSITION]: __('Assembly Position'),
  [VEHICLE_PROFILE_FIELDS.ENGINE_POINT_OF_FITMENT]: __('Point of Fitment'),
  [VEHICLE_PROFILE_FIELDS.ENGINE_CAPACITY]: __('Capacity'),
  [VEHICLE_PROFILE_FIELDS.ENGINE_CODE]: __('Engine Code'),
  [VEHICLE_DETAILS_ENGINE_ENGINE_CALIBRATION]: __('Calibration'),

  // Vehicle Health - Common Column
  [VEHICLE_PROFILE_FIELDS.VEHICLE_HEALTH_MILEAGE]: __('$$(Mileage)'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_HEALTH_AVG]: __('Average'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_HEALTH_LONG_TERM_AVG]: __('Long Term Average'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_HEALTH_FUEL]: __('Fuel'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_HEALTH_BATTERY]: __('Battery'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_HEALTH_INSPECTIONS_DESCRIPTION]: __('Description'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_HEALTH_INSPECTIONS_SOURCE]: __('Source'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_HEALTH_INSPECTIONS_URL]: __('URL'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_HEALTH_INSPECTIONS_LAST_UPDATED_TIME]: __('Last Updated Time'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_HEALTH_INSPECTIONS_MEDIA]: __('Media'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_HEALTH_QUANTITY_OF_ENGINE_OIL_DESCRIPTION_FIELD]: __('Description'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_HEALTH_QUANTITY_OF_ENGINE_OIL_ICON_FIELD]: __('Icon'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_HEALTH_QUANTITY_OF_ENGINE_OIL_LAST_UPDATED_TIME_FIELD]: __('Last Updated Time'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_HEALTH_QUANTITY_OF_ENGINE_OIL_STATUS_FIELD]: __('Status'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_HEALTH_REPLACE_ENGINE_OIL_DESCRIPTION_FIELD]: __('Description'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_HEALTH_REPLACE_ENGINE_OIL_ICON_FIELD]: __('Icon'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_HEALTH_REPLACE_ENGINE_OIL_LAST_UPDATED_TIME_FIELD]: __('Last Updated Time'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_HEALTH_REPLACE_ENGINE_OIL_STATUS_FIELD]: __('Status'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_HEALTH_SMARTKEY_BATTERY_DESCRIPTION_STATUS_FIELD]: __('Description'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_HEALTH_SMARTKEY_BATTERY_LAST_UPDATED_TIME_FIELD]: __('Last Updated Time'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_HEALTH_SMARTKEY_BATTERY_STATUS_FIELD]: __('Status'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_HEALTH_SMARTKEY_BATTERY_ICON_FIELD]: __('Icon'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_HEALTH_WARNING_LAST_UPDATED_TIME_FIELD]: __('Last Updated Time'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_HEALTH_DTC]: __('Dtc'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_HEALTH_WARNING_LIST_FIELD]: __('Description'),

  // Capabilities
  [VEHICLE_PROFILE_FIELDS.CAPABILITIES_TELEMETICS]: __('Telemetics'),
  [VEHICLE_PROFILE_FIELDS.CAPABILITIES_TELESERVICE]: __('Teleservice'),
  [VEHICLE_PROFILE_FIELDS.CAPABILITIES_TELESERVICE_CONTRACT]: __('Teleservice Contract'),
  [VEHICLE_PROFILE_FIELDS.CAPABILITIES_RTC]: __('RTC'),

  // Telematics Subscription
  [VEHICLE_PROFILE_FIELDS.WIFI_SUBSCRIPTION]: __('WIFI Subscription'),
  [VEHICLE_PROFILE_FIELDS.COMM_CHECKED_STATUS]: __('Comm Checked Status'),
  [VEHICLE_PROFILE_FIELDS.COMM_CHECKED_DATE]: __('Comm Checked Date'),
  [VEHICLE_PROFILE_FIELDS.DCM_VERSION_NUMBER]: __('DCM Version Number'),
  [VEHICLE_PROFILE_FIELDS.PRODUCT_NAME]: __('Product Name'),
  [VEHICLE_PROFILE_FIELDS.PRODUCT_END_DATE]: __('Product End Date'),
  [VEHICLE_PROFILE_FIELDS.TELEMATICS_SUBSCRIPTION_STATUS]: __('Status'),
  [VEHICLE_PROFILE_FIELDS.PRODUCT_START_DATE]: __('Product Start Date'),

  // Associated Customers
  [VEHICLE_PROFILE_FIELDS.ASSOCIATED_CUSTOMER_NAME_FIELD]: __('Name'),
  [VEHICLE_PROFILE_FIELDS.ASSOCIATED_CUSTOMER_PHONE_FIELD]: __('Phone Number'),
  [VEHICLE_PROFILE_FIELDS.ASSOCIATED_CUSTOMER_ORIGINAL_OWNER_NAME_FIELD]: __('Original Owner'),
  [VEHICLE_PROFILE_FIELDS.ASSOCIATED_CUSTOMER_FLEET_OWNER_NAME_FIELD]: __('Fleet Owner'),
  [VEHICLE_PROFILE_FIELDS.ASSOCIATED_CUSTOMER_EMAIL_FIELD]: __('Email'),
  [VEHICLE_PROFILE_FIELDS.ASSOCIATED_CUSTOMER_OWNERSHIP_INFO_FIELD]: __('Ownership Info'),

  // Service History - OEM
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM]: __('Service History Oem Table'),

  // Level 1
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_RO_ID_FIELD]: __('$$(RO)#'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_COMPLETION_DATE_FIELD]: __('Completion Date'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_RO_OPEN_DATE_FIELD]: __('RO Open Date'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_SERVICE_CENTER_FIELD]: __('Service Center'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_SERVICE_LOCATION_FIELD]: __('Service Location'),
  [SERVICE_HISTORY_OEM_DEPARTMENT_TYPE]: __('Department Type'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_JOB_COUNT_FIELD]: __('Job Count'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_RO_PAY_TYPE_FIELD]: __('Pay Type'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_SERVICE_ADVISOR_ID_FIELD]: __('$$(Service Advisor) ID'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_SERVICE_ADVISOR_FIELD]: __('$$(Service Advisor)'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_RO_COMMENT_FIELD]: __('$$(Repair Order) Comment'),
  [SERVICE_HISTORY_OEM_REPAIR_ORDER_INTERNAL_REMARKS_COMMENT]: __('$$(Repair Order) Internal Comment'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_REPAIR_ORDER_INTERNAL_REMARKS_FIELD]: __(
    '$$(Repair Order) Internal Remarks'
  ),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_DEALER_CODE_FIELD]: __('$$(Dealer) Code'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_DEALER_NAME_FIELD]: __('$$(Dealer) Name'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_ODOMETER_IN_FIELD]: __('Odometer In'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_ODOMETER_OUT_FIELD]: __('Odometer Out'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_SERVICE_TYPE_FIELD]: __('Service Type'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_REPEAT_REPAIR_FLAG_FIELD]: __('Repeat Repair Flag'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_WARRANTY_CLAIM_NUMBER_FIELD]: __('Warranty Claim Number'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_TRANSACTION_TYPE_FIELD]: __('Transaction Type'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_CLAIM_TYPE_FIELD]: __('Claim Type'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_CLAIM_STATUS_FIELD]: __('Claim Status'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_AUTH_CODE_FIELD]: __('Auth Code'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_ENTRY_DATE_FIELD]: __('Entry Date'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_PROC_DATE_FIELD]: __('Proc Date'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_POSTING_DATE_FIELD]: __('Posting Date'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_DESCRIPTION_FIELD]: __('Description'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_CONDITION_CODE_FIELD]: __('Condition Code'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_CONDITION_DESCRIPTION_FIELD]: __('Condition Description'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_OPEN_DATE_FIELD]: __('Open Date'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_WARRANTY_CLAIM_TYPE_FIELD]: __('Warranty Claim Type'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISOTRY_OEM_INVOICE_DATE_FIELD]: __('Invoice Date'),
  // RRG Specific
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_OPERATION_ID]: __('Operation ID'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_CATEGORY_FIELD]: __('Category'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_CATEGORY_LABEL_FIELD]: __('Label'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_SYMPTOM_FIELD]: __('Symptom'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_CUSTOMER_COMPLAINT_FIELD]: __('Customer Complaint'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_CUSTOMER_FEEDBACK_FIELD]: __('Customer Feedback'),

  // Level 2 - JobList
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_JOB_FIELD]: __('Job'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_JOB_PAY_TYPE_FIELD]: __('Pay Type'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_STANDARD_OPCODE_FIELD]: __('Standard Labor Opcode'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_DMS_LABOR_OPCODE_FIELD]: __('DMS Labor Opcode'),
  [SERVICE_HISTORY_OEM_JOB_HISTORY_RECORD]: __('Job History Record'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_DMS_JOB_NUMBER_FIELD]: __('DMS Job Number'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_OPERATION_ID_FIELD]: __('DMS Operation ID'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_OPERATION_NAME_FIELD]: __('DMS Operation Name'),
  [SERVICE_HISTORY_OEM_JOB_COMMENT_RECORD]: __('Job Comment Record'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_CAUSE_DESCRIPTION_FIELD]: __('Cause Description'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_COMPLAINT_DESCRIPTION_FIELD]: __('Complaint Description'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_CORRECTION_DESCRIPTION_FIELD]: __('Correction Description'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_TECHNICIAN_NOTE_FIELD]: __('Technician Note'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_MISCELLANEOUS_NOTE_FIELD]: __('Miscellaneous Notes'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_JOB_DENIAL_DESCRIPTION_FIELD]: __('Job Denial Description'),

  // Level 2 - Sublet
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_SUBLET_CODE_FIELD]: __('Sublet Code'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_SUBLET_WORK_DESCRIPTION_FIELD]: __('Sublet Work Description'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_MISCELLANOUES_DESCRIPTION_FIELD]: __('Miscellaneous Description'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_GOG_DESCRIPTION_FIELD]: __('GOG Description'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_PAINT_DESCRIPTION_FIELD]: __('Paint Description'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_SHOP_SUPPLIES_DESCRIPTION_FIELD]: __('Shop Supplies Description'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_FREIGHT_DESCRIPTION_FIELD]: __('Freight Description'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_SPLIT_TYPE_CODE_FIELD]: __('Splits Type Code'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_TECHNICIAN_NAME_FIELD]: __('Technician Name'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_TECHNICIAN_NUMBER_FIELD]: __('Technician Number'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_PRICE_CODE_FIELD]: __('Price Code'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_PRICE_DESCRIPTION_FIELD]: __('Price Description'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_AUTHORIZATION_NUMBER_FIELD]: __('Authorization Number'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_PART_SPLIT_PERCENTAGE_FIELD]: __('Part Split Percentage'),

  // Level 3 - Parts
  [SERVICE_HISTORY_OEM_PARTS_INFORMATION_RECORD]: __('Parts Information Record'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_PART_ID_FIELD]: __('Part Id'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_PART_NAME_FIELD]: __('Part Name'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_PART_QUANTITY_FIELD]: __('Part Quantity'),

  // Level 3 - Labor
  [SERVICE_HISTORY_OEM_LABOR_DETAILS_RECORD]: __('Labor Detail Record'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_LABOR_OPCODE_FIELD]: __('Labor Opcode'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_OPCODE_DESCRIPTION_FIELD]: __('Opcode Description'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_LABOR_FLAT_HOURS_FIELD]: __('Labor Flat Hours'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_LABOR_HOURS_FIELD]: __('Labor Hours'),
  // RRG Specific
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_OEM_LABOR_TYPE_FIELD]: __('Type'),

  // Service History - Dealer
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER]: __('Service History Dealer Table'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_RO_SCOPE]: __('Tenant Level Service History'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_RO_NUMBER_FIELD]: __('$$(RO)#'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_COMPLETION_DATE_FIELD]: __('Completion Date'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_SERVICE_ADVISOR_FIELD]: __('$$(Service Advisor)'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_DEALERSHIP_FIELD]: __('Dealership'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_DEPARTMENT_FIELD]: __('Department'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_RO_TYPE_FIELD]: __('$$(RO) Type'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_CUSTOMER_NAME_FIELD]: __('Customer'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_NUMBER_OF_JOBS_FIELD]: __('Number Of Jobs'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_RO_TECHNICIANS_FIELD]: __('Technicians'), // level 1
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_RO_TOTAL_FIELD]: __('Total'), // level 1
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_MILEAGE_IN_FIELD]: __('$$(Mileage)'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_JOB_LINE_FIELD]: __('Job Line'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_JOB_CAUSE_FIELD]: __('Job Cause'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_STORY_LINE_FIELD]: __('Story Line'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_JOB_CONCERN_FIELD]: __('Job Concern'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_JOB_PAY_TYPE_FIELD]: __('Job Pay Type'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_TOTAL_PRICE_FIELD]: __('Total'), // level 2
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_JOB_HOURS]: __('Job Hours'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_CONCERN_TYPE_FIELD]: __('Concern Type'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_SUBLET_VENDOR]: __('Sublet Vendor'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_PO_ID]: __('PO#'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_PO_LINE_ID]: __('PO Line#'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_SUBLET_TYPE]: __('Sublet Type'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_SUBLET_LABOR]: __('Sublet Labor'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_SUBLET_PARTS]: __('Sublet Parts'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_REASON_FOR_VOIDING_JOB]: __('Reason For Voiding Job'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_RETRACTED_ACTUAL_HOURS]: __('Retracted Actual Hours'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_JOB_TECHNICIANS_FIELD]: __('Technicians'), // level 2
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_JOB_TYPE_FIELD]: __('Job Type'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_NUMBER_OF_OPERATIONS]: __('Number Of Operations'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_RO_TAGS_FIELD]: __('$$(RO) Tags'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_OPERATION_COMPONENT]: __('Operation Details'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_OPCODE_FIELD]: __('Opcode'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_OPERATION_DESCRIPTION_FIELD]: __('Operation Description'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_LABOR_HOURS]: __('Labor Hrs'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_LABOR_RATE]: __('Labor Rate'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_BILL_RATE]: __('Bill Rate'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_BILL_HRS]: __('Bill Hrs'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_LABOR_PRICE]: __('Labor Price'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_STORY_LINE]: __('Tech Story'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_LABOR_TYPE]: __('Labor Type'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_CUSTOMER_NUMBER]: __('Customer Number'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_OPEN_DATE_FIELD]: __('Open Date'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_OPEN_TIME_FIELD]: __('Open Time'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_CLOSE_TIME_FIELD]: __('Close Time'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_CP_TOTAL]: __('CP Total'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_W_TOTAL]: __('W Total'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_I_TOTAL]: __('I Total'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_PART]: __('Part'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_QUANTITY]: __('Quantity'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_UNIT_PRICE]: __('Unit Price'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_TOTAL_PRICE]: __('Total Price'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_HISTORY_DEALER_COUNTER_PERSON]: __('Counter Person'),

  // Deferred Recommendations
  [DEFERRED_RECOMMENDATIONS_FIELDS.DEFERRED_RECOMMENDATION_RECOMMNEDATION_FIELD]: __('Recommendation'),
  [DEFERRED_RECOMMENDATIONS_FIELDS.DEFERRED_RECOMMENDATION_RO_ID_FIELD]: __('$$(RO)#'), // common column
  [DEFERRED_RECOMMENDATIONS_FIELDS.DEFERRED_RECOMMENDATION_RECOMMENDED_ON]: __('Recommended By | On'),
  [DEFERRED_RECOMMENDATIONS_FIELDS.DEFERRED_RECOMMENDATION_DEFERRED_BY_FIELD]: __('$$(Deferred) By | On'),
  [DEFERRED_RECOMMENDATIONS_FIELDS.DEFERRED_RECOMMENDATION_CUSTOMER_NAME]: __('Customer'),
  [DEFERRED_RECOMMENDATIONS_DEFERRED_ON]: __('Deferred On'),
  [DEFERRED_RECOMMENDATIONS_FIELDS.DEFERRED_RECOMMENDATION_FOLLOW_UP_TIME]: __('Follow Up Date'),
  [DEFERRED_RECOMMENDATIONS_FIELDS.DEFERRED_RECOMMENDATION_TOTAL_AMOUNT_FIELD]: __('Total'), // common column

  // Service Maintenance
  [VEHICLE_PROFILE_FIELDS.SERVICE_MAINTENANCE_TABLE]: __('Service Maintenance Table'), // Table Item Key
  [VEHICLE_PROFILE_FIELDS.SERVICE_MAINTENANCE_OPERATION_ID]: __('Operation ID'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_MAINTENANCE_COMPLETION_DATE]: __('Completion Date'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_MAINTENANCE_ODOMETER_OUT]: __('Odometer Out'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_MAINTENANCE_REPAIR_ORDER_COMMENT]: __('$$(Repair Order) Comment'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_MAINTENANCE_CUSTOMER_FEEDBACK]: __('Customer Feedback'),
  // Level - 2 & 3
  [VEHICLE_PROFILE_FIELDS.SERVICE_MAINTENANCE_LABOR_OPCODE_FIELD]: __('Labor Opcode'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_MAINTENANCE_LABOR_FLAT_HOURS_FIELD]: __('Labor Flat Hours'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_MAINTENANCE_PART_NAME_FIELD]: __('Part Name'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_MAINTENANCE_PART_QUANTITY_FIELD]: __('Part Quantity'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_MAINTENANCE_LABOR_TYPE_FIELD]: __('Type'),

  // Warranty - Dealership
  [VEHICLE_PROFILE_FIELDS.WARRANTY_DEALERSHIP_COLUMN_NAME]: __('Column Name'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_DEALERSHIP_TABLE]: __('Warranty Dealership Table'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_DEALERSHIP_COLUMN_TYPE]: __('Column Type'),

  // Warranty - Warranties
  [VEHICLE_PROFILE_FIELDS.WARRANTY_CODE_FIELD]: __('Warranty Code'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_DESCRIPTION_FIELD]: __('Warranty Description'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_COVERAGE_DESCRIPTION_FIELD]: __('Coverage Description'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_VALIDITY_FIELD]: __('Validity Status'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_START_DATE_FIELD]: __('Start Date'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_END_DATE_FIELD]: __('End Date'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_START_ODOMETER_FIELD]: __('Start ODO'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_END_ODOMETER_FIELD]: __('End ODO'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_BUILD_DATE_FIELD]: __('Warranty Build Date'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_DEDUCTIBLE]: __('Warranty Deductible'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_CANCELLATION_CODE]: __('Warranty Cancellation Code (Y/N)'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_CANCELLATION_DATE]: __('Warranty Cancellation Date'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_EXPIRATION_DATE_FIELD]: __('Expiration Date'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_WARRANTY_FIELD]: __('Warranty'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_EXPIRATION_DISTANCE_FIELD]: __('Expiration Miles/Kilometers'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_ROAD_SIDE_ASSISTANCE_FIELD]: __('Warranty Road Side Assistance'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_MASTERSHIELD_FIELD]: __('Warranty Mastershield'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_VEHICLE_RESTRICTIONS_FIELD]: __('Warranty Vehicle Restrictions'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_WARRANTY_TRANSFERABLE_FIELD]: __('Warranty Warranty Transferrable'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_MONTHS_REMAINING_FIELD]: __('Warranty Months Remaining'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_EXTENDED_WARRANTY_START_MILEAGE]: __('Extended Warranty Start $$(Mileage)'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_EXTENDED_WARRANTY_START_DATE]: __('Extended Warranty Start Date'),

  // Warranty - Coverage
  [VEHICLE_PROFILE_FIELDS.WARRANTY_COVERAGE_TYPE]: __('Coverage Type'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_COVERAGE_DESCRIPTION]: __('Description'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_COVERAGE_ADDITIONAL_INFORMATION]: __('Additional Information'),

  // Warranty - History
  [VEHICLE_PROFILE_FIELDS.WARRANTY_HISTORY_RO_FIELD]: __('$$(RO)#'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_HISTORY_SERVICE_DATE_FIELD]: __('Service Date'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_HISTORY_SERVICE_IN_DATE_FIELD]: __('Service Date'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_HISTORY_SERVICE_TYPE_FIELD]: __('Service Type'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_HISTORY_DEALER_NAME]: __('$$(Dealer) Name'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_HISTORY_MILEAGE_IN_FIELD]: __('$$(Mileage)'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_HISTORY_ODOMETER]: __('Odometer'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_HISTORY_COMMENT]: __('Comment'),
  [WARRANTY_HISTORY_ODOMETER_INDICATOR]: __('Odometer Indicator'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_HISTORY_PART_NUMBER]: __('Part Number'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_HISTORY_PART_DESCRIPTION]: __('Part Description'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_HISTORY_QUANTITY]: __('Quantity'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_HISTORY_CONDITION_CODE]: __('Condition Code'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_HISTORY_CONDITION_DESCRIPTION]: __('Condition Description'),
  [WARRANTY_HISTORY_REPAIR_HISTORY]: __('Repair History'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_HISTORY_TRANSACTION_TYPE_FIELD]: __('Transaction Type'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_HISTORY_LABOR_OPERATION_IDS_FIELD]: __('Labor Operation Code'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_HISTORY_LABOR_OPCODE_FIELD]: __('Labor Operation Code'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_HISTORY_JOB_DESCRIPTION_FIELD]: __('Job Description'),

  // Vehicle Warranty Transfer
  [VEHICLE_PROFILE_FIELDS.VEHICLE_WARRANTY_TRANSFER_WARRANTY_TRANSFER_CODE_FIELD]: __('Transfer Code'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_WARRANTY_TRANSFER_WARRANTY_TRANSFER_MODEL_YEAR_FIELD]: __('Model Year'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_WARRANTY_TRANSFER_WARRANTY_TRANSFER_BASIC_COVERAGE_FIELD]: __('Basic Coverage'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_WARRANTY_TRANSFER_WARRANTY_TRANSFER_POWER_TRAIN_FIELD]: __('Power Train'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_WARRANTY_TRANSFER_WARRANTY_TRANSFER_RUST_PROTECTION_FIELD]: __('Rust Protection'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_WARRANTY_TRANSFER_WARRANTY_TRANSFER_MESSAGE_FIELD]: __('Message'),
  [VEHICLE_PROFILE_FIELDS.VEHICLE_WARRANTY_TRANSFER_WARRANTY_TRANSFER_DEDUCTIBLE_FIELD]: __('Deductible'),

  // Extended Service Contract Information
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_COMPANY_NAME_FIELD]: __('Company Name'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_COMPANY_PHONE_FIELD]: __('Company Phone'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_POLICY_NUMBER_FIELD]: __('Policy Number'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_TERMS_FIELD]: __('Terms'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_EFFECTIVE_DATE_FIELD]: __('Effective Date'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_ENROLLMENT_DATE_FIELD]: __('Enrollment Date'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_DEDUCTIBLE_FIELD]: __('Deductible'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_LIMIT_IN_ODO_METER_FIELD]: __('Limit In Odometer'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_BEGINNING_ODO_METER_FIELD]: __('Start ODO'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_ENDING_ODO_METER_FIELD]: __('End ODO'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_PLAN_TYPE]: __('Plan Type'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_EXTENDED_SERVICE_SECTION_LABEL]: __('Type'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_SERVICE_CONTRACT_EXPIRATION_NOTE]: __('Service Contract Expiration Note'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_PLAN_EXPIRES_DATE]: __('Plan Expires Date'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_OWNER_NAME]: __('Owner Name'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_OPTIONS]: __('Options'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_CANCELLED_DATE]: __('Cancelled Date'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_CANCELLED_MILEAGE]: __('Cancelled Mileage'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_CONTRACT_SUSPENDED_DATE]: __('Contract Suspended Date'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_CONTRACT_SUSPENDED_INDICATOR]: __('Contract Suspended Indicator'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_SERVICE_CONTRACT_ID]: __('Service Contract ID'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_SERVICE_CONTRACT_NOTES]: __('Service Contract Notes'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_PLAN_EFFECTIVE_MILES]: __('Expiry $$(Mileage)'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_SERVICE_OCPD]: __('Service OCPD'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_CLAIMED]: __('Claimed'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_LAST_PERFORMED_DATE]: __('Last Performed Date'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_ALLOWED_DESCRIPTION]: __('Allowed Description'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_LAST_PERFORMED_MILES]: __('Last performed $$(Mileage)'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_ROAD_SIDE_ASSISTANCE]: __('Road Side Assistance'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_RENTAL_ALLOWANCE_PER_DAY]: __('Rental Allowance Per Day'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_RENTAL_ALLOWANCE_PER_VISIT]: __('Rental Allowance Per Visit'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_TOWING_ALLOWANCE]: __('Towing Allowance'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_MAXIMUM_OIL_CHANGE_ALLOWANCE]: __('Maximum Oil Change Allowance'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_CANCELLATION_FEE]: __('Cancellation Fee'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_TRANSFER_FEE]: __('Transfer Fee'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_DETAILS_VENDOR]: __('Vendor'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_DETAILS_SOLD_BY_ME]: __('Sold By Me'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_DETAILS_SOLD_BY]: __('Sold By'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_DETAILS_CUSTOMER_LAST_NAME]: __('Customer Last Name'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_DETAILS_CUSTOMER_FIRST_NAME]: __('Customer First Name'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_DETAILS_CENTER_ID]: __('Center ID'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_CENTER_NAME]: __('Center Name'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_CENTER_PHONE]: __('Center Phone'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_DETAILS_ADDITIONAL_MESSAGE]: __('Additional Details'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_CONTRACT_TRANSFERRED_INDICATOR]: __('Contract Transferred Indicator'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_CONTRACT_STATUS]: __('Contract Status'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_ESP_START_DATE]: __('Esp Start Date'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_CONTRACT_REMAINING]: __('Contract Remaining'),
  [VEHICLE_PROFILE_FIELDS.EXTENDED_WARRANTY_CONTRACT_TERM]: __('Contract Term'),

  // Added Security
  [VEHICLE_PROFILE_FIELDS.AGREEMENT_PURCHASE]: __('Agreement Purchase'),
  [VEHICLE_PROFILE_FIELDS.AGREEMENT_EXPIRATION]: __('Agreement Expiration'),
  [VEHICLE_PROFILE_FIELDS.COVERAGE_DESCRIPTION]: __('Coverage Description'),
  [VEHICLE_PROFILE_FIELDS.COVERAGE_TYPE]: __('Coverage Type'),
  [VEHICLE_PROFILE_FIELDS.COVERAGE_CONTRACT_NUMBER]: __('Contract Number'),
  [VEHICLE_PROFILE_FIELDS.PLAN_NUMBER]: __('Plan Number'),
  [VEHICLE_PROFILE_FIELDS.POS_MILEAGE]: __('POS Mileage'),
  [VEHICLE_PROFILE_FIELDS.TIRE_PROTECTION]: __('Tire Protection'),
  [VEHICLE_PROFILE_FIELDS.STATUS]: __('Status'),
  [VEHICLE_PROFILE_FIELDS.CONTRACT_TERM_DISTANCE]: __('Contract Term Distance'),
  [VEHICLE_PROFILE_FIELDS.CANCEL_DATE]: __('Cancel Date'),
  [VEHICLE_PROFILE_FIELDS.DEDUCTIBLE_AMOUNT]: __('Deductible Amounts'),
  [VEHICLE_PROFILE_FIELDS.RENTAL_ALLOWANCE]: __('Rental Allowance'),
  [VEHICLE_PROFILE_FIELDS.TOWING_ALLOWANCE]: __('Towing Allowance'),

  // Exclusions
  [VEHICLE_PROFILE_FIELDS.EXCLUSION_EFFECTIVE_DATE]: __('Effective Date'),
  [VEHICLE_PROFILE_FIELDS.EXPIRATION_DATE]: __('Expiration Date'),
  [VEHICLE_PROFILE_FIELDS.EXPIRATION_STATUS]: __('Status'),
  [VEHICLE_PROFILE_FIELDS.EXCLUSION_TYPE_DESCRIPTION]: __('Exclusion Type Description'),

  // Subaru Equity Shield
  [VEHICLE_PROFILE_FIELDS.COVERAGE_AGREEMENT_PURCHASE]: __('Agreement Purchase'),
  [VEHICLE_PROFILE_FIELDS.CONTRACT_END_DATE]: __('Contract End Date'),
  [VEHICLE_PROFILE_FIELDS.CONTRACT_NUMBER]: __('Contract Number'),
  [VEHICLE_PROFILE_FIELDS.CONTRACT_DESC]: __('Contract Description'),
  [VEHICLE_PROFILE_FIELDS.CONTRACT_STATUS]: __('Contract Status'),
  [VEHICLE_PROFILE_FIELDS.WARRANTY_START]: __('Warranty Start'),
  [VEHICLE_PROFILE_FIELDS.CONTRACT_CANCEL_DATE]: __('Contract Cancel Date'),

  // Eligible Contracts
  [VEHICLE_PROFILE_FIELDS.ELIGIBILE_CONTRACTS_TYPE]: __('Type'),
  [VEHICLE_PROFILE_FIELDS.ELIGIBILE_CONTRACTS_PROGRAM_DESCRIPTION]: __('Program Description'),
  [VEHICLE_PROFILE_FIELDS.ELIGIBILE_CONTRACTS_PROGRAM_CODE]: __('Program Code'),
  [VEHICLE_PROFILE_FIELDS.ELIGIBILE_CONTRACTS_WHOLESALE_PRICE]: __('Wholesale Price'),
  [VEHICLE_PROFILE_FIELDS.ELIGIBILE_CONTRACTS_MSRP]: __('MSRP'),
  [VEHICLE_PROFILE_FIELDS.ELIGIBILE_CONTRACTS_EXPIRE_AGE]: __('Expire Age'),
  [VEHICLE_PROFILE_FIELDS.ELIGIBILE_CONTRACTS_EXPIRE_MILEAGE]: __('Expire Mileage'),
  [VEHICLE_PROFILE_FIELDS.ELIGIBILE_CONTRACTS_VENDOR]: __('Vendor'),
  [VEHICLE_PROFILE_FIELDS.ELIGIBILE_CONTRACTS_DEDUCTIBLE]: __('Deductible'),

  // Insurance
  [VEHICLE_PROFILE_FIELDS.INSURANCE_POLICY_NUMBER_FIELD]: __('Policy Number'),
  [VEHICLE_PROFILE_FIELDS.INSURANCE_NAME_FIELD]: __('Name'),
  [VEHICLE_PROFILE_FIELDS.INSURANCE_DESCRIPTION_FIELD]: __('Description'),
  [VEHICLE_PROFILE_FIELDS.INSURANCE_DEDUCTIBLE_FIELD]: __('Deductible'),
  [VEHICLE_PROFILE_FIELDS.INSURANCE_RENTAL_AMOUNT_FIELD]: __('Rental Amount'),
  [VEHICLE_PROFILE_FIELDS.INSURANCE_START_DATE_FIELD]: __('Start Date'),
  [VEHICLE_PROFILE_FIELDS.INSURANCE_START_ODO_FIELD]: __('Start ODO'),
  [VEHICLE_PROFILE_FIELDS.INSURANCE_END_DATE_FIELD]: __('End Date'),
  [VEHICLE_PROFILE_FIELDS.INSURANCE_END_ODO_FIELD]: __('End ODO'),

  // Service Information
  [VEHICLE_PROFILE_FIELDS.SERVICE_INFORMATION_TYPE]: __('Type'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_INFORMATION_NUMBER]: __('Number'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_INFORMATION_DESCRIPTION]: __('Description'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_INFORMATION_DATE_POSTED]: __('Date Posted'),

  // Scheduled Maintenance List
  [VEHICLE_PROFILE_FIELDS.SCHEDULED_MAINTENANCE_LIST]: __('Scheduled Maintenance List'),

  // Special Service Campaign
  [VEHICLE_PROFILE_FIELDS.CAMPAIGN_NUMBER_FIELD]: __('Number'),
  [VEHICLE_PROFILE_FIELDS.CAMPAIGN_NAME_FIELD]: __('Name'),
  [VEHICLE_PROFILE_FIELDS.CAMPAIGN_DESCRIPTION_FIELD]: __('Description'),
  [VEHICLE_PROFILE_FIELDS.CAMPAIGN_DATE_FIELD]: __('Date'),
  [VEHICLE_PROFILE_FIELDS.CAMPAIGN_START_DATE_FIELD]: __('Start Date'),
  [VEHICLE_PROFILE_FIELDS.CAMPAIGN_COMPLETED_INDICATOR_FIELD]: __('Completed Indicator'),
  [VEHICLE_PROFILE_FIELDS.CAMPAIGN_POPUP_MESSAGE_FIELD]: __('Popup Message'),
  [VEHICLE_PROFILE_FIELDS.CAMPAIGN_LABOR_OPERATION_FIELD]: __('Labor Operation'),
  [VEHICLE_PROFILE_FIELDS.CAMPAIGN_LABOR_OPERATION_DESCRIPTION_FIELD]: __('Labor Operation Description'),
  [VEHICLE_PROFILE_FIELDS.CAMPAIGN_LABOR_TIME_FIELD]: __('Labor Time'),
  [VEHICLE_PROFILE_FIELDS.CAMPAIGN_MULTIPLE_CAMPAIGN_POPUP_MESSAGE_FIELD]: __('Multiple Campaign Popup Message'),
  [VEHICLE_PROFILE_FIELDS.CAMPAIGN_DAMAGE_CODE_FIELD]: __('Damage Code'),
  [VEHICLE_PROFILE_FIELDS.CAMPAIGN_SERVICE_BULLETIN_URL_FIELD]: __('Service Bulletin URL'),
  [VEHICLE_PROFILE_FIELDS.CAMPAIGN_RECALL_FIELD]: __('$$(Recall)'),
  [VEHICLE_PROFILE_FIELDS.CAMPAIGN_END_DATE_FIELD]: __('End Date'),
  [VEHICLE_PROFILE_FIELDS.CAMPAIGN_TERMINATION_MILEAGE_FIELD]: __('Campaign Termination Mileage'),
  [VEHICLE_PROFILE_FIELDS.CAMPAIGN_TYPE_FIELD]: __('Type'),
  [VEHICLE_PROFILE_FIELDS.CAMPAIGNS_LABOR_OPERATION_ID]: __('Labor Operation Id'),
  [VEHICLE_PROFILE_FIELDS.CAMPAIGNS_LABOR_OPERATION_DESCRIPTION]: __('Labor Operation Description'),
  [SPECIAL_SERVICE_CAMPAIGN_LABOR_TIME]: __('Labor Time'),
  // Level 2
  [VEHICLE_PROFILE_FIELDS.CAMPAIGN_PART_ID]: __('Part ID'),
  [VEHICLE_PROFILE_FIELDS.CAMPAIGN_PART_TYPE]: __('Part Type'),
  [VEHICLE_PROFILE_FIELDS.CAMPAIGN_PART_QUANTITY]: __('Part Quantity'),
  [VEHICLE_PROFILE_FIELDS.CAMPAIGN_PART_DESCRIPTION]: __('Part Description'),

  // Service Bulletins
  [VEHICLE_PROFILE_FIELDS.SERVICE_BULLETIN_STATUS_FLAG]: __('Status Flag'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_BULLETIN_STATUS_DESCRIPTION]: __('Status Description'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_BULLETIN_STATUS]: __('Status Code'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_BULLETIN_REMEDY_STATUS_CODE]: __('Remedy Status'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_BULLETIN_REMEDY_STATUS_DESCRIPTION]: __('Remedy Status Description'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_BULLETIN_STOP_SALES_CODE]: __('Stop Sales Code'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_BULLETIN_STOP_SALES_FLAG]: __('Stop Sales Flag'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_BULLETIN_RESERVED_BY_CENTER_ID]: __('Reserved By CenterId'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_BULLETIN_RESERVED_BY_NAME]: __('Reserved By Name'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_BULLETIN_START_DATE]: __('Start Date'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_BULLETIN_REFERENCE_FIELD]: __('Reference'),
  [SERVICE_BULLETINS_SERVICE_BULLETIN_URL]: __('Service Bulletin Url'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_BULLETIN_DESCRIPTION_FIELD]: __('Description'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_BULLETIN_DEFECT_CODE_FIELD]: __('Defect Code'),
  [VEHICLE_PROFILE_FIELDS.SERVICE_BULLETIN_MODEL_FIELD]: __('Bulletin Model'),

  // Recalls
  [VEHICLE_PROFILE_FIELDS.RECALLS_RECALL_NUMBER]: __('$$(Recall) Number'),
  [VEHICLE_PROFILE_FIELDS.RECALLS_RESERVED_BY_ME]: __('Reserved By Me'),
  [VEHICLE_PROFILE_FIELDS.RECALL_NAME_FIELD]: __('Name'),
  [VEHICLE_PROFILE_FIELDS.RECALL_OPENED_ON_FIELD]: __('Opened On'),
  [VEHICLE_PROFILE_FIELDS.RECALL_STATUS_FIELD]: __('Status'),
  [VEHICLE_PROFILE_FIELDS.RECALL_STATUS_CODE]: __('Status Code'),
  [VEHICLE_PROFILE_FIELDS.RECALL_STATUS_DESCRIPTION]: __('Status Description'),
  [VEHICLE_PROFILE_FIELDS.RECALLS_EXPIRATION_DATE]: __('Expiration Date'),
  [VEHICLE_PROFILE_FIELDS.RECALLS_EXPIRATION_MILEAGE]: __('Expiration Mileage'),
  [VEHICLE_PROFILE_FIELDS.RECALLS_TYPE]: __('$$(Recall) Type'),
  [VEHICLE_PROFILE_FIELDS.RECALL_REMEDY_STATUS]: __('Remedy Status'),
  [VEHICLE_PROFILE_FIELDS.RECALL_REMEDY_STATUS_DESCRIPTION]: __('Remedy Status Description'),
  [VEHICLE_PROFILE_FIELDS.RECALL_STOP_SALES_CODE]: __('Stop Sales Code'),
  [VEHICLE_PROFILE_FIELDS.RECALL_STOP_SALES_FLAG]: __('Stop Sales Flag'),
  [VEHICLE_PROFILE_FIELDS.RECALL_DAMAGE_CODE]: __('Damage Code'),
  [VEHICLE_PROFILE_FIELDS.RECALL_RESERVED_BY_CENTER_ID]: __('Reserved By CenterId'),
  [VEHICLE_PROFILE_FIELDS.RECALL_RESERVED_BY_NAME]: __('Reserved By Name'),
  [VEHICLE_PROFILE_FIELDS.RECALLS_RECALL_COMPLETED_DATE]: __('$$(Recall) Completed Date'),
  [VEHICLE_PROFILE_FIELDS.RECALLS_LABOR_TIME]: __('Labor Time'),
  [VEHICLE_PROFILE_FIELDS.RECALLS_LABOR_OPERATION_ID]: __('Labor Operation Id'),
  [VEHICLE_PROFILE_FIELDS.RECALLS_LABOR_OPERATION_DESCRIPTION]: __('Labor Operation Description'),
  [VEHICLE_PROFILE_FIELDS.RECALLS_LABOR_ALLOWANCE_HOURS]: __('Labor Allowance Hours'),
  [VEHICLE_PROFILE_FIELDS.RECALLS_POPUP_MESSAGE]: __('Popup Message'),
  [RECALLS_RECALL_DOCUMENT]: __('Recall Document'),
  [VEHICLE_PROFILE_FIELDS.RECALLS_BULLETIN_URL]: __('Bulletin URL'),
  [RECALLS_BULLETIN_ID]: __('Bulletin ID'),
  [VEHICLE_PROFILE_FIELDS.RECALLS_DEFECT_CODE]: __('Defect Code'),
  [RECALLS_TECHNICAL_NOTE_NUMBER]: __('Technical Note Number'),
  [VEHICLE_PROFILE_FIELDS.RECALLS_SEVERITY]: __('Severity'),
  [VEHICLE_PROFILE_FIELDS.RECALLS_QUOTATION]: __('Quotation'),
  [VEHICLE_PROFILE_FIELDS.RECALLS_AGREEMENT]: __('Agreement'),
  [VEHICLE_PROFILE_FIELDS.RECALLS_DELIVERY_DATE]: __('Delivery Date'),
  [VEHICLE_PROFILE_FIELDS.RECALLS_ADD_TO_RO_ACTION]: __('Add to $$(RO) Action'),
  [VEHICLE_PROFILE_FIELDS.RECALLS_CODE]: __('Code'),
  [VEHICLE_PROFILE_FIELDS.RECALLS_SERVICE_BULLETIN_URL]: __('Service Bulletin URL'),

  // Due Bills
  [VEHICLE_PROFILE_FIELDS.DUE_BILL_ISSUE_DATE_FIELD]: __('Issue Date'),
  [VEHICLE_PROFILE_FIELDS.DUE_BILL_CODE_FIELD]: __('Code'),
  [VEHICLE_PROFILE_FIELDS.DUE_BILL_DESCRIPTION_FIELD]: __('Description'),
  [VEHICLE_PROFILE_FIELDS.DUE_BILL_DISCLOSURE_TYPE_FIELD]: __('Disclosure Type'),
  [VEHICLE_PROFILE_FIELDS.DUE_BILL_PAY_TYPE_FIELD]: __('Pay Type'),
  [VEHICLE_PROFILE_FIELDS.DUE_BILL_DEALS_SALE_PRICE_FIELD]: __('Sale Price'),
  [VEHICLE_PROFILE_FIELDS.DUE_BILL_DEALS_COST_PRICE_FIELD]: __('Cost Price'),
  [VEHICLE_PROFILE_FIELDS.DUE_BILL_TAXABLE_FIELD]: __('Taxable'),
  [VEHICLE_PROFILE_FIELDS.DUE_BILL_OPCODE_FIELD]: __('Opcode'),
  [VEHICLE_PROFILE_FIELDS.DUE_BILL_PART_CODE_FIELD]: __('Part Code'),
  [VEHICLE_PROFILE_FIELDS.DUE_BILL_STATUS_FIELD]: __('Completion Status'),
  [VEHICLE_PROFILE_FIELDS.DUE_BILL_RO_ID_FIELD]: __('$$(RO)#'), // common column - twidgets
  [VEHICLE_PROFILE_FIELDS.DUE_BILL_RO_COMPLETION_DATE_FIELD]: __('$$(RO) Completion Date'),

  // Sales Info - Sales Details
  [VEHICLE_PROFILE_FIELDS.SALES_VEHICLE_TYPE]: __('Vehicle Type'),
  [VEHICLE_PROFILE_FIELDS.SALES_PERSON]: __('Sales Person'),
  [VEHICLE_PROFILE_FIELDS.SELLING_DEALER]: __('Selling $$(Dealer)'),
  [VEHICLE_PROFILE_FIELDS.SELLING_DEALER_CODE]: __('Selling $$(Dealer) Code'),
  [VEHICLE_PROFILE_FIELDS.SELLING_DEALER_ADDRESS]: __('Selling $$(Dealer) Address'),
  [VEHICLE_PROFILE_FIELDS.SELLING_DEALER_CITY]: __('Selling $$(Dealer) City'),
  [VEHICLE_PROFILE_FIELDS.SELLING_DEALER_STATE]: __('Selling $$(Dealer) State'),
  [VEHICLE_PROFILE_FIELDS.SELLING_DEALER_POSTAL_CODE]: __('Selling $$(Dealer) Postal Code'),
  [VEHICLE_PROFILE_FIELDS.SALES_TYPE]: __('Sales Type'),
  [VEHICLE_PROFILE_FIELDS.PDI_REPAIR_ORDER]: __('$$(PDI) $$(Repair Order) Number'),
  [VEHICLE_PROFILE_FIELDS.INVOICE_NUMBER]: __('Invoice Number'),
  [SALES_INFO_SALES_DETAILS_PURCHASE_DATE]: __('Purchase Date'),
  [VEHICLE_PROFILE_FIELDS.MANUFACTURING_NUMBER]: __('Manufacturing Number'),

  // Sales Info - Allocated Party
  [VEHICLE_PROFILE_FIELDS.SALES_ALLOCATED_DEALER_CODE]: __('Allocated $$(Dealer) Code'),
  [VEHICLE_PROFILE_FIELDS.SALES_ALLOCATED_DEALER]: __('Allocated $$(Dealer)'),
  [VEHICLE_PROFILE_FIELDS.SALES_ORGANIZATION_ID]: __('Organization ID'),
  [VEHICLE_PROFILE_FIELDS.SALES_DEALER_CITY]: __('Allocated $$(Dealer) City'),
  [VEHICLE_PROFILE_FIELDS.SALES_DEALER_ADDRESS]: __('Allocated $$(Dealer) Address'),
  [VEHICLE_PROFILE_FIELDS.SALES_POSTAL_CODE]: __('Allocated $$(Dealer) Postal Code'),
  [VEHICLE_PROFILE_FIELDS.SALES_DEALER_STATE]: __('Allocated $$(Dealer) State'),

  // Sales Info - Drafted Party
  [VEHICLE_PROFILE_FIELDS.SALES_DRAFTED_DEALER_CODE]: __('Drafted $$(Dealer) Code'),
  [VEHICLE_PROFILE_FIELDS.SALES_DRAFTED_DEALER]: __('Drafted $$(Dealer)'),
  [VEHICLE_PROFILE_FIELDS.SALES_DRAFTED_ORGN_ID]: __('Organization ID'),
  [VEHICLE_PROFILE_FIELDS.SALES_DRAFTED_PARTY_CITY]: __('City'),
  [VEHICLE_PROFILE_FIELDS.SALES_DRAFTED_PARTY_ADDRESS]: __('Address'),
  [VEHICLE_PROFILE_FIELDS.SALES_DRAFTED_POSTAL_CODE]: __('Postal Code'),
  [VEHICLE_PROFILE_FIELDS.SALES_DRAFTED_DEALER_STATE]: __('$$(Dealer) State'),

  // Sales Info - Price Details
  [VEHICLE_PROFILE_FIELDS.MSRP]: __('MSRP'),
  [VEHICLE_PROFILE_FIELDS.BASE_RETAIL]: __('Base Retail'),
  [VEHICLE_PROFILE_FIELDS.BASE_INVOICE]: __('Base Invoice'),
  [VEHICLE_PROFILE_FIELDS.INVOICE_PRICE]: __('Invoice Price'),
  [VEHICLE_PROFILE_FIELDS.HOLD_BACK_AMOUNT]: __('Holdback Amount'),
  [VEHICLE_PROFILE_FIELDS.FREIGHT_COST]: __('Freight Cost'),
  [VEHICLE_PROFILE_FIELDS.COMMISSION]: __('Commission'),

  // Sales Info - Financing Details
  [VEHICLE_PROFILE_FIELDS.PRICE]: __('Price'),
  [VEHICLE_PROFILE_FIELDS.PAYMENT_TYPE]: __('Payment Type'),
  [VEHICLE_PROFILE_FIELDS.DOWN_PAYMENT]: __('Down Payment'),
  [VEHICLE_PROFILE_FIELDS.TERM]: __('Term'),
  [VEHICLE_PROFILE_FIELDS.INTEREST_RATE]: __('Interest Rate'),
  [VEHICLE_PROFILE_FIELDS.MONTHLY_PAYMENT]: __('Monthly Payment'),
  [VEHICLE_PROFILE_FIELDS.LENDER_NAME]: __('Lender'),

  // Buyers
  [VEHICLE_PROFILE_FIELDS.BUYERS_NAME_FIELD]: __('Name'),
  [VEHICLE_PROFILE_FIELDS.BUYERS_PHONE_NUMBER_FIELD]: __('Phone Number'),
  [VEHICLE_PROFILE_FIELDS.BUYERS_EMAIL_FIELD]: __('Email'),
  [BUYERS_ADDRESS]: __('Address'),
  [BUYERS_CITY]: __('City'),
  [BUYERS_STATE]: __('State'),
  [BUYERS_POSTAL_CODE]: __('PostalCode'),

  // Subcription
  [VEHICLE_PROFILE_FIELDS.ON_STAR]: __('Onstar'),
  [VEHICLE_PROFILE_FIELDS.PHONE_NUMBER]: __('Phone Number (US)'),
  [VEHICLE_PROFILE_FIELDS.PHONE_NUMBER_CA]: __('Phone Number (CA)'),
  [VEHICLE_PROFILE_FIELDS.ODO_METER]: __('Odometer'),
  [VEHICLE_PROFILE_FIELDS.OIL_LIFE]: __('Oil Life'),
  [VEHICLE_PROFILE_FIELDS.DATE]: __('Date'),
  [VEHICLE_PROFILE_FIELDS.TIRE_PRESSURE]: __('Tyre Pressure'),
  [VEHICLE_PROFILE_FIELDS.XM]: __('XM'),
  [VEHICLE_PROFILE_FIELDS.RADIO_ID]: __('Radio ID'),
  [VEHICLE_PROFILE_FIELDS.OWNER_CENTER]: __('Owner Center'),
  [VEHICLE_PROFILE_FIELDS.PORSCHE_ROAD_SIDE_ASSISTANCE]: __('Porsche Road Side Assistance'),
  [VEHICLE_PROFILE_FIELDS.XM_PHONE_NUMBER]: __('Phone Number (US)'),
  [VEHICLE_PROFILE_FIELDS.DEALER_MAINTENANCE_NOTIFICATION]: __('$$(Dealer) Maintenance Notification'),
  [VEHICLE_PROFILE_FIELDS.ADV_DIAG]: __('Advanced Diagnostics'),

  // Symptom Code Information
  [VEHICLE_PROFILE_FIELDS.SYMPTOM_CODE]: __('Symptom Code'),
  [VEHICLE_PROFILE_FIELDS.SYMPTOM_CODE_DESCRIPTION]: __('Description'),
  [VEHICLE_PROFILE_FIELDS.SSM_NUMBER]: __('SSM Number'),
  [VEHICLE_PROFILE_FIELDS.EFFECTIVE_DATE]: __('Effective Date'),
  [VEHICLE_PROFILE_FIELDS.SYMPTOM_CODE_TITLE]: __('Title'),
  [VEHICLE_PROFILE_FIELDS.SYMPTOM_CODE_TEXT]: __('Text'),

  // Condition Based Service
  [VEHICLE_PROFILE_FIELDS.CONDITION_BASED_SERVICE_ID]: __('Id'),
  [VEHICLE_PROFILE_FIELDS.CONDITION_BASED_SERVICE_TITLE]: __('Title'),
  [VEHICLE_PROFILE_FIELDS.CONDITION_BASED_SERVICE_TYPE]: __('Type'),
  [VEHICLE_PROFILE_FIELDS.CONDITION_BASED_SERVICE_DESCRIPTION]: __('Description'),
  [VEHICLE_PROFILE_FIELDS.CONDITION_BASED_SERVICE_NAME]: __('Name'),
  [VEHICLE_PROFILE_FIELDS.CONDITION_BASED_SERVICE_STATUS]: __('Status'),
  [VEHICLE_PROFILE_FIELDS.CONDITION_BASED_SERVICE_REMAINING_TIME]: __('Remaining Time'),
  [VEHICLE_PROFILE_FIELDS.CONDITION_BASED_SERVICE_REMAINING_TIME_DUE_NEXT]: __('Remaining Time - Due Next'),
  [VEHICLE_PROFILE_FIELDS.CONDITION_BASED_SERVICE_REMAINING_DISTANCE]: __('Remaining Distance'),
  [VEHICLE_PROFILE_FIELDS.CONDITION_BASED_SERVICE_REMAINING_DISTANCE_DUE_NEXT]: __('Remaining Distance - Due Next'),
  [VEHICLE_PROFILE_FIELDS.CONDITION_BASED_SERVICE_FRU_NUMBER]: __('FRU Number'),
  [VEHICLE_PROFILE_FIELDS.CONDITION_BASED_SERVICE_FRU_QUANTITY]: __('FRU Quantity'),
  [VEHICLE_PROFILE_FIELDS.CONDITION_BASED_SERVICE_LINKED_WITH]: __('Linked With'),
  [VEHICLE_PROFILE_FIELDS.CONDITION_BASED_SERVICE_OPCODE]: __('Opcode'),
  [VEHICLE_PROFILE_FIELDS.CONDITION_BASED_SERVICE_DUE_DATE]: __('Due Date'),

  // Recall Modal
  [VEHICLE_PROFILE_FIELDS.RECALL_MODAL_NUMBER]: __('Number'),
  [VEHICLE_PROFILE_FIELDS.RECALL_MODAL_NHTSA_NO]: __('NHSTA No'),
  [VEHICLE_PROFILE_FIELDS.RECALL_MODAL_TYPE]: __('Type'),
  [VEHICLE_PROFILE_FIELDS.RECALL_MODAL_DESCRIPTION]: __('Description'),
  [VEHICLE_PROFILE_FIELDS.RECALL_MODAL_DETAILED_DESCRIPTION]: __('Detailed Description'),
  [VEHICLE_PROFILE_FIELDS.RECALL_MODAL_CUSTOMER_ACTION]: __('$$(Customer) Action'),
  [VEHICLE_PROFILE_FIELDS.RECALL_MODAL_RISK]: __('Risk If Not Repaired'),
  [VEHICLE_PROFILE_FIELDS.RECALL_MODAL_RECALL_URLS]: __('$$(Recall) URLs'),
  [VEHICLE_PROFILE_FIELDS.RECALL_MODAL_CODE]: __('Code'),
  [VEHICLE_PROFILE_FIELDS.RECALL_MODAL_NAME]: __('Name'),
  [VEHICLE_PROFILE_FIELDS.RECALL_MODAL_SEVERITY]: __('Severity'),
  [VEHICLE_PROFILE_FIELDS.RECALL_MODAL_QUOTATION]: __('Quotation'),
  [VEHICLE_PROFILE_FIELDS.RECALL_MODAL_AGREEMENT]: __('Agreement'),
  [VEHICLE_PROFILE_FIELDS.RECALL_MODAL_DELIVERY_DATE]: __('Delivery Date'),

  // Header
  [HEADER_RECALLS_SEVERITY]: __('Recalls Severity'),
  [HEADER_STOCK_ID]: __('Stock ID'),
  [HEADER_LICENSE_PLATE]: __('License Plate'),
};
