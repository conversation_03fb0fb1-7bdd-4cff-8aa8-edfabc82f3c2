import _isNil from 'lodash/isNil';
import _set from 'lodash/set';

import { CLOUD_ASSET_TYPES } from '@tekion/tekion-base/constants/cloudTypes';
import { CRM_ENTITIES } from '@tekion/tekion-base/constants/crm';
import CRMCustomerReader from '@tekion/tekion-base/readers/CrmCustomer';

import { getDealerSiteAddedUrl } from '@tekion/tekion-base/utils/history';
import { getQueryParamsFromUrl } from '@tekion/tekion-base/utils/general';
import openWindowInNewTab from '@tekion/tekion-base/utils/openWindowInNewTab';

import { CUSTOM_ROUTE_IDS, LEAD_DETAILS_HEADER_KEYS } from '../constants/leads';
import { redirectToLeadDetails, getLeadDetailsRoute } from './routes';

const getContactRedirectionParams = ({
  contact,
  entityId,
  entityType = CRM_ENTITIES.CONTACT,
  navigate,
  location,
  rightContainerPanel = LEAD_DETAILS_HEADER_KEYS.SENTIMENT,
  showEntityList = true,
  useContent,
  useHistoryBack = false,
}) => {
  const contactId = CRMCustomerReader.id(contact);
  const latestLeadId = CRMCustomerReader.latestLead(contact);
  const latestLeadIdWithPermission = CRMCustomerReader.latestLeadWithPermission(contact);
  const currentSite = CRMCustomerReader.currentSite(contact);
  const hasNoAccessToLeadSite = currentSite === false;
  const hasLimitedAccess = latestLeadId && !latestLeadIdWithPermission;
  const queryParams = {
    type: entityType,
    entityId: entityType === CRM_ENTITIES.CONTACT ? contactId : entityId,
    rightContainerPanel,
    showEntityList,
    useContent,
    useHistoryBack,
  };
  const params = {
    navigate,
    location,
    queryParams,
  };

  let leadId;

  if (latestLeadIdWithPermission) {
    leadId = latestLeadIdWithPermission;
  } else if (hasNoAccessToLeadSite) {
    leadId = CUSTOM_ROUTE_IDS.NO_ACCESS;
  } else if (hasLimitedAccess) {
    leadId = CUSTOM_ROUTE_IDS.LIMITED_ACCESS;
  } else {
    _set(params, 'queryParams.rightContainerPanel', LEAD_DETAILS_HEADER_KEYS.SENTIMENT);
    leadId = CUSTOM_ROUTE_IDS.NO_ACTIVITY;
  }
  return {
    leadId,
    params,
  };
};

export const getContactDetailsRoute = args => {
  const { leadId, params } = getContactRedirectionParams(args);
  return getLeadDetailsRoute(leadId, params);
};

const addOpenInSameTabParam = (params, app, openInSameTab) => {
  const shouldOpenInSameTab = _isNil(openInSameTab) ? app === CLOUD_ASSET_TYPES.CRM : openInSameTab;
  return {
    ...params,
    openInSameTab: shouldOpenInSameTab,
  };
};

export const handleContactRedirection = args => {
  const { openInSameTab, app = CLOUD_ASSET_TYPES.CRM, onLeadNavigation, ...rest } = args;
  const { leadId, params } = getContactRedirectionParams(rest);
  const paramsWithOpenInSameTab = addOpenInSameTabParam(params, app, openInSameTab);
  const redirectionFunc = onLeadNavigation || redirectToLeadDetails;
  redirectionFunc(leadId, paramsWithOpenInSameTab);
};

export const handleDealerContactRedirection = args => {
  const { contact, dealerId, siteId } = args;
  const route = getContactDetailsRoute({ contact });
  const { url } = getQueryParamsFromUrl(route);
  const updatedRoute = getDealerSiteAddedUrl({
    url,
    originalUrl: route,
    dealerId,
    tekSiteId: siteId || dealerId,
    workspaceId: dealerId,
  });
  openWindowInNewTab(updatedRoute);
};
