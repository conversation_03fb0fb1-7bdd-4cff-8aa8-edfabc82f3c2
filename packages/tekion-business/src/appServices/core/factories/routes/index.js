import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_BASE_ADMIN,
  <PERSON><PERSON><PERSON><PERSON>DGE_BASE,
  FAQ_BUILDER,
  TAG_MANAGER,
  CORE_CUSTOMER_MANAGEMENT,
  CORE_VENDOR_MANAGEMENT,
  RULE_ENGINE,
  DEALER_CONFIGURATION,
  COMMUNICATION_TEMPLATE,
  REPORTS_MANAGER,
  TEAMS_SETUP,
  TAX_CODES_SETUP,
  REPORT_BUILDER_SETUP,
  PRE_ONBOARDING_QUESTIONNAIRE,
  TAX_SETUP,
  REPORTS,
  EMPLOYEE_ONBOARDING_KEY,
} from '@tekion/tekion-base/constants/appConfigs/coreAppConfigs';
import { EMPLOYEE_HOURS_CONFIG } from '@tekion/tekion-base/constants/appConfigs/employeeHoursAppConfigs';
import { BUSINESS_ACCESS_MANAGER } from '@tekion/tekion-base/constants/appConfigs/businessPortalAppConfigs';

// helpers
import getKnowledgeBaseRoutes from './knowledgeBase/knowledgeBase.routes';
import getFaqBuilderRoutes from './faqBuilder.routes';
import getKnowledgeBaseAdminRoutes from './knowledgeBaseAdmin.routes';
import getTagManagerRoutes from './tagManager.routes';
import customerManagementGenerator from './customerManagement.routes';
import vendorManagementGeneraor from './vendorManagement.routes';
import ruleEngineRoutes from './ruleEngine.routes';
import dealerConfigurationRoutes from './dealerConfiguration.routes';
import templateBuilderRoutes from './templateBuilder.routes';
import reportsManagerRoutes from './reportsManager/reportsManager.routes';
import getTeamsSetupRoutes from './teamsSetup.routes';
import getTaxCodesSetupRoutes from './taxCodesSetup.routes';
import getReportBuilderSetupRoutes from './reportBuilderSetup.routes';
import getPreOnboardingQuestionnaireRoute from './preOnboardingQuestionnaire/preOnboardingQuestionnaire.routes';
import getTaxSetupRoutes from './taxSetup.routes';
import getReportsRoute from './reports.routes';
import getEmployeeOnBoardingRoute from './employeeOnBoarding.routes';
import getEmployeeHoursRoute from './employeeHours.routes';
import { getBusinessAccessManagerRoute } from './businessPortal.routes';

const MODULE_TO_ROUTE_GENERATOR = {
  [KNOWLEDGE_BASE.getKey()]: getKnowledgeBaseRoutes,
  [FAQ_BUILDER.getKey()]: getFaqBuilderRoutes,
  [KNOWLEDGE_BASE_ADMIN.getKey()]: getKnowledgeBaseAdminRoutes,
  [TAG_MANAGER.getKey()]: getTagManagerRoutes,
  [CORE_CUSTOMER_MANAGEMENT.getKey()]: customerManagementGenerator,
  [CORE_VENDOR_MANAGEMENT.getKey()]: vendorManagementGeneraor,
  [RULE_ENGINE.getKey()]: ruleEngineRoutes,
  [DEALER_CONFIGURATION.getKey()]: dealerConfigurationRoutes,
  [COMMUNICATION_TEMPLATE.getKey()]: templateBuilderRoutes,
  [REPORTS_MANAGER.getKey()]: reportsManagerRoutes,
  [TEAMS_SETUP.getKey()]: getTeamsSetupRoutes,
  [TAX_CODES_SETUP.getKey()]: getTaxCodesSetupRoutes,
  [REPORT_BUILDER_SETUP.getKey()]: getReportBuilderSetupRoutes,
  [PRE_ONBOARDING_QUESTIONNAIRE.getKey()]: getPreOnboardingQuestionnaireRoute,
  [TAX_SETUP.getKey()]: getTaxSetupRoutes,
  [REPORTS.getKey()]: getReportsRoute,
  [EMPLOYEE_ONBOARDING_KEY]: getEmployeeOnBoardingRoute,
  [EMPLOYEE_HOURS_CONFIG.getKey()]: getEmployeeHoursRoute,
  [BUSINESS_ACCESS_MANAGER.getKey()]: getBusinessAccessManagerRoute,
};

export default MODULE_TO_ROUTE_GENERATOR;
