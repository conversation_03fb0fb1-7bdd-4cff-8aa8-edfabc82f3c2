/* eslint-disable no-param-reassign */

import _noop from 'lodash/noop';
import _isFunction from 'lodash/isFunction';

import { injectScript, SCRIPT_INJECT_TYPE } from 'tbase/scripts/scriptInjector';

// constant
import { URL_TYPES } from '../../constants/api';
import { EMPTY_OBJECT, EMPTY_STRING } from '../../app.constants';

import { getEnvironmentVariables } from '../../helpers/envHelper';
import TEnvReader from '../../readers/Env';
import UserReader from '../../readers/User';
import httpWorkerClient from '../../services/apiService/httpWorkerClient';
import { ROOT as APP_SERVICE_ROOT } from '../../constants/appServices';
import { DEVELOPMENT } from '../../constants/buildTypes';

const SCRIPT_ID = 'apmRumScript';
const SCRIPT_SRC = 'https://assets.observeinc.com/dist/bundles/apm-rum.umd.min.js';

const isRumEnabled = () =>
  !!(
    TEnvReader.nodeEnv(getEnvironmentVariables()) !== DEVELOPMENT && TEnvReader.rumServerUrl(getEnvironmentVariables())
  );

const getUserContextToSet = userInfo => ({
  id: UserReader.id(userInfo),
  username: UserReader.displayName(userInfo),
  email: UserReader.email(userInfo),
});

const getCustomContextToSet = userInfo => ({
  dealerId: UserReader.dealerId(userInfo),
  tenantName: UserReader.tenantName(userInfo),
  roleId: UserReader.roleId(userInfo),
  persona: UserReader.persona(userInfo),
});

function injectApmRum({ scriptTagId = SCRIPT_ID, pageLoadName, serviceName, userInfo = EMPTY_OBJECT }) {
  const elasticApmConfig = {
    serviceName,
    serverUrl: TEnvReader.rumServerUrl(getEnvironmentVariables()),
    serverUrlPrefix: `?environment=${TEnvReader.buildEnv(getEnvironmentVariables())}&serviceName=${serviceName}`,
    environment: TEnvReader.buildEnv(getEnvironmentVariables()),
    ignoreTransactions: [/pendo/, /walkme/, /openreplay/, /amplitude/, /sentry/],
    apmRequest: ({ method, payload, headers }) => {
      if (!_isFunction(window.Worker)) return true;
      const httpWorkerClientInstance = httpWorkerClient.getInstance();
      httpWorkerClientInstance.sendRequest.bind(httpWorkerClientInstance);
      httpWorkerClientInstance.sendRequest({
        urlType: URL_TYPES.APM,
        baseUrl: TEnvReader.rumServerUrl(getEnvironmentVariables()),
        url: EMPTY_STRING,
        method,
        headers: {
          ...headers,
          Authorization: `Bearer ${TEnvReader.rumApiKey(getEnvironmentVariables())}`,
        },
        data: { payload, __isCompressed__: true },
      });
      return false;
    },
    serviceVersion: TEnvReader.appVersion(getEnvironmentVariables()),
    breakdownMetrics: true,
    logLevel: 'error',
    session: true,
    apiVersion: 2,
    distributedTracingOrigins: ['*'],
    distributedTracingHeaderName: 'X-Observe-Rum-Id',
    propagateTracestate: true,
    transactionSampleRate: 1,
  };

  return injectScript({
    scriptSrc: SCRIPT_SRC,
    scriptTagId,
    injectType: SCRIPT_INJECT_TYPE.SRC,
    onLoad: () => {
      if (!window?.elasticApm) return;
      window.elasticApm.init(elasticApmConfig);
      window.elasticApm.setInitialPageLoadName(pageLoadName);
      window.elasticApm.setUserContext(getUserContextToSet(userInfo));
      window.elasticApm.setCustomContext(getCustomContextToSet(userInfo));
      window.elasticApm.observe('transaction:end', tr => {
        if (tr.type === 'route-change') {
          tr.name = window.location.pathname;
        }
      });
    },
  });
}

function initApmRumScripts({ pageLoadName, serviceName, userInfo }) {
  if (isRumEnabled()) {
    injectApmRum({ pageLoadName, serviceName, userInfo });
  }
}

const initApmRum = () => {
  const userInfo = TEnvReader.userInfo();
  initApmRumScripts({
    pageLoadName: APP_SERVICE_ROOT,
    serviceName: 'ARC_WEB',
    userInfo,
  });
};

function disableApmRumObserver() {
  window?.elasticApm?.observe('transaction:end', _noop);
}

export { initApmRumScripts, disableApmRumObserver, initApmRum };
