import React, { useContext, useRef } from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';
import _noop from 'lodash/noop';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import IconAsBtn from '@tekion/tekion-components/src/atoms/iconAsBtn';
import { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';
import { useTekionConversion } from '@tekion/tekion-conversion-web';

import conversationReader from '@tekion/tekion-widgets/src/organisms/communications/readers/conversation.reader';
import { NDCContext } from '@tekion/tekion-widgets/src/appServices/crm/organisms/ndc/ndcContextProvider';
import useTimer from '@tekion/tekion-widgets/src/organisms/communications/helpers/communications.contactCardTimer';
import CustomerSentiment from '@tekion/tekion-widgets/src/organisms/communications/molecules/customerSentiment';
import { getTimeFormatted } from '@tekion/tekion-widgets/src/organisms/communications/helpers/communications.general';
import { isCustomerConversation } from '@tekion/tekion-widgets/src/organisms/communications/helpers/communications.externalConversations';
import EllipsesToolTip from '@tekion/tekion-widgets/src/organisms/communications/atoms/ellipsesToolTip';
import { getDealershipName } from './helpers/contactCard.helpers';
import UnreadCountBadge from '../../molecules/unreadCountBadge';
import useScrollCardIntoView from './hooks/useScrollCardIntoView';
import useSelectConversation from './hooks/useSelectConversation';
import useRemoveConversation from './hooks/useRemoveConversation';

import s from './contactCard.module.scss';

// Helpers
import { VISIBLE_DEALERSHIP_CHARACTERS } from './constants/contactCard.customerDetails';
import { getConversationUnreadCount, getConversationSentiment } from '../../helpers/communication.converationInfo';
import CustomerInfo from './molecules/customerInfo';
import { getNDCInfoForConversation } from '../../helpers/communication.ndc';
import AiStatus from '../../molecules/aiStatus/AiStatus';

const ContactCard = ({
  onAction,
  conversation,
  isSelected,
  contactListRef,
  selectedDepartment,
  showDealershipName,
  conversationAdditionalInfo,
  isSearchTextPresent,
  onConversationSelect,
  displaySettings,
}) => {
  const { ndcPreferences = EMPTY_OBJECT } = useContext(NDCContext);
  const { isRunning, setIsRunning, currentTime } = useTimer();
  const { id: departmentId = '' } = selectedDepartment;
  const contactCardRef = useRef(null);
  const handleSelectConversation = useSelectConversation({ conversation, onAction });
  const handleRemoveConversation = useRemoveConversation({ departmentId, conversation, onAction });
  const { getFormattedDateAndTime } = useTekionConversion();
  useScrollCardIntoView(contactListRef, contactCardRef, isSelected, isSearchTextPresent);
  const lastMessageTime = conversationReader.lastMessageTime(conversation);
  const unreadCount = getConversationUnreadCount(conversationAdditionalInfo, conversation);
  const time = getTimeFormatted({
    time: lastMessageTime,
    currentTime,
    isRunning,
    setIsRunning,
    getFormattedDateAndTime,
  });
  const shouldCenterAlignRightInfo = !lastMessageTime;
  const lastMessageTimeClassName = cx(s.time, { [s.displayTime]: !unreadCount });
  const contactCardRightInfoClassName = cx(s.right, {
    'flex align-items-center justify-content-end': shouldCenterAlignRightInfo,
  });
  const removeIconClassName = cx(s.removeChatIcon, {
    [s.vCenterAndRightAlign]: !shouldCenterAlignRightInfo,
  });
  const countAndSentimentClassName = cx(s.countAndSentiment, {
    [s.displayCountAndSentiment]: !unreadCount,
  });
  const sentiment = isCustomerConversation(conversation)
    ? getConversationSentiment(conversationAdditionalInfo, conversation)
    : undefined;

  const phoneNumber = conversationReader.originalPhoneNumber(conversation);

  const ndcInfo = getNDCInfoForConversation({
    ndcPreferences,
    phoneNumber,
    conversation,
    displaySettings,
  });
  const handleConversationMouseDown = () => {
    onConversationSelect(handleSelectConversation, isSelected);
  };

  return (
    <div
      role="button"
      tabIndex={0}
      className={cx(s.item, 'cursor-pointer', { [s.selected]: isSelected })}
      onMouseDown={handleConversationMouseDown}
      key={conversationReader.id(conversation)}
      ref={contactCardRef}>
      <div className="flex">
        <div className={s.left}>
          <CustomerInfo conversation={conversation} ndcInfo={ndcInfo} displaySettings={displaySettings} />
          {showDealershipName && (
            <div className="flex">
              <EllipsesToolTip length={VISIBLE_DEALERSHIP_CHARACTERS} className={`${s.contactInfo}`}>
                {getDealershipName(conversation)}
              </EllipsesToolTip>
            </div>
          )}
        </div>
        <div className={contactCardRightInfoClassName}>
          {Boolean(lastMessageTime) && <span className={lastMessageTimeClassName}>{time}</span>}
          <div className={`flex-center justify-content-end ${countAndSentimentClassName}`}>
            {Boolean(unreadCount) && <UnreadCountBadge count={unreadCount} />}
            {sentiment && <CustomerSentiment sentiment={sentiment} className={s.sentimentIcon} />}
          </div>
          {!unreadCount && (
            <IconAsBtn size={SIZES.L} containerClassName={removeIconClassName} onClick={handleRemoveConversation}>
              icon-failed-filled
            </IconAsBtn>
          )}
        </div>
      </div>
      <AiStatus aiAgentName="AI BDC" status="AI_ACTIVE" />
    </div>
  );
};

ContactCard.propTypes = {
  onAction: PropTypes.func,
  isSelected: PropTypes.bool.isRequired,
  selectedDepartment: PropTypes.object,
  conversation: PropTypes.object,
  contactListRef: PropTypes.object,
  showDealershipName: PropTypes.bool,
  conversationAdditionalInfo: PropTypes.object,
  isSearchTextPresent: PropTypes.bool,
  onConversationSelect: PropTypes.func,
  displaySettings: PropTypes.string,
};

ContactCard.defaultProps = {
  conversation: EMPTY_OBJECT,
  selectedDepartment: EMPTY_OBJECT,
  contactListRef: EMPTY_OBJECT,
  onAction: _noop,
  showDealershipName: false,
  conversationAdditionalInfo: EMPTY_OBJECT,
  isSearchTextPresent: false,
  onConversationSelect: _noop,
  displaySettings: undefined,
};

export default ContactCard;
