import _castArray from 'lodash/castArray';
import _find from 'lodash/find';
import _forEach from 'lodash/forEach';
import _get from 'lodash/get';
import _head from 'lodash/head';
import _isEmpty from 'lodash/isEmpty';
import _last from 'lodash/last';
import _noop from 'lodash/noop';
import _size from 'lodash/size';

import { compose } from 'recompose';
import castArrayIfPresent from '@tekion/tekion-base/utils/castArrayIfPresent';
import { getLookupByKeys } from '@tekion/tekion-base/services/lookupService';
import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import esResponseReader from '@tekion/tekion-base/readers/EsResponse';
import OPERATORS from '@tekion/tekion-base/constants/filterOperators';
import Request from '@tekion/tekion-base/builders/request';
import resolver from '@tekion/tekion-base/bulkResolvers';
import { tget } from '@tekion/tekion-base/utils/general';
import getDataFromResponse from '@tekion/tekion-base/utils/getDataFromResponse';
import { RESOURCE_TYPE } from '@tekion/tekion-base/bulkResolvers/constants/resourceType';
import { makeDealerContextQueryParams } from '@tekion/tekion-business/src/appServices/communication/helpers/communications.chatQueryParams';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';

import { DEFAULT_CONVERSATION_PAGE_SIZE } from '@tekion/tekion-widgets/src/organisms/communications/nonEnterpriseCommunication/constants/communication.general';
import { FEATURE_NAME } from '@tekion/tekion-widgets/src/experienceEngine';
import { addSourceInPayload } from '@tekion/tekion-widgets/src/helpers/crm/leads/leadSearch';
import SOURCE from '@tekion/tekion-widgets/src/constants/crm/leads/leadSearch';
import conversationReader from '@tekion/tekion-widgets/src/organisms/communications/readers/conversation.reader';
import { CUSTOMER_TYPES } from '@tekion/tekion-widgets/src/organisms/communications/constants/communications.customerTypes';
import {
  isUnreadMessageFilterSelected,
  getSearchConversationRequestDTO,
} from '@tekion/tekion-widgets/src/organisms/communications/helpers/communications.externalConversations';
import {
  FILTER_TYPE_KEYS,
  SORT_ORDER_KEYS,
} from '@tekion/tekion-widgets/src/organisms/communications/constants/communications.externalFilter';
import { DEPARTMENTS_MAP } from '@tekion/tekion-widgets/src/organisms/communications/constants/communications.departments';
import { getSelectedDepartment } from '@tekion/tekion-widgets/src/organisms/communications/nonEnterpriseCommunication/reducers/selector';
import { COMMUNICATION_STORE_NAME } from '../../constants/communications.general';
import {
  startConversation,
  removeConversation,
  searchConversationByLastConversation,
  associateConversation,
  searchConversationForDepartment,
} from '../services/conversations.services';
import { ignoreConversation } from '../../services/communications.conversation';
import {
  getPageSizeByConversationListSize,
  getConversationsWithInfoFromResponse,
  getConversationIdFilter,
  getConversationCustomerTypeFilter,
  getLeadDetailsForNavigation,
} from './helpers/communication.general';
import actionCreators from './creators/communication.actionCreators';
import { resolveConversation } from '../../helpers/communications.conversations';
import { searchLeads, searchCRMCustomer } from '../services/references.services';
import EXTERNAL_CONVERSATION_ENTITY from '../../resolvers/externalConversationEntity';

import { getSelectedConversationIdFormLS } from '../organisms/externalCommunication/helpers/externalCommunication.localStorage';

import {
  getSelectedTabIdForDepartment,
  getSelectedFilterForDepartment,
  getSelectedSortOrderForDepartment,
} from '../reducers/selectors/secondarySelectors/nonEnterpriseCommunication.external';
import { makeFetchCommunicationPreferencesPayload } from './helpers/nonEnterpriseCommunication.ndc';
import { getConversationFrameRequest } from '../helpers/nonEnterpriseCommunication.conversationsRequest';
import { createSaveConversationsResponseAction } from './creators/externalCommunication/externalCommunication.conversations';
import { fetchConversationSetting } from './externalCommunication/externalCommunication.conversationSetting';
import {
  makeConversationSettingPayload,
  shouldFetchConversationSettingByDealerId,
} from '../helpers/nonEnterpriseCommunication.externalConversationSetting';
import { getConversationSettingsByDealerId } from '../reducers/selectors/externalCommunication/externalCommunication.conversationSetting';
import { createIsErrorInFetchingConversationSettingsByDealerIdAction } from './creators/externalCommunication/externalCommunication.conversationSetting';
import { getDealerOptInSettings } from '../reducers/selectors/nonEnterpriseCommunication.externalMessages';

const getResolvedConversationResponseData = async response => {
  const data = getConversationsWithInfoFromResponse(response) || EMPTY_OBJECT;
  try {
    const resolvedData = await resolver.getResolvedData(undefined, data.hits, {
      entityAdditionalProps: EXTERNAL_CONVERSATION_ENTITY,
    });
    return { ...data, hits: resolvedData };
  } catch (e) {
    return data;
  }
};

export const searchConversation = async (
  customerId,
  phoneNumber,
  customerName,
  department,
  customerType = CUSTOMER_TYPES.CUSTOMER,
  dealerId
) => {
  const request = getSearchConversationRequestDTO({
    department,
    customerId,
    phoneNumber,
    customerName,
    customerType,
  });
  const queryParams = makeDealerContextQueryParams({ dealerId });
  const response = await searchConversationForDepartment(request, queryParams);
  const resolvedData = await getResolvedConversationResponseData(response);
  return resolvedData;
};

const searchConversationById = async ({
  conversationId,
  department,
  selectedTabId,
  isCRMEnabled,
  isCOVEnabled,
  isMultiSiteEnabled,
}) => {
  const request = new Request();
  request.filters = [
    getConversationIdFilter(conversationId),
    getConversationCustomerTypeFilter({
      isCRMEnabled,
      isCOVEnabled,
      department,
      selectedTabId,
      isMultiSiteEnabled,
    }),
  ];
  const response = await searchConversationForDepartment(request);
  const resolvedData = await getResolvedConversationResponseData(response);
  return resolvedData;
};

const manageFetchConversationSettings = ({
  conversationSettingsByDealerId,
  dispatch,
  conversation,
  getFeatureValue,
}) => {
  const dealerId = conversationReader.dealerId(conversation);
  const customerType = conversationReader.customerType(conversation);

  const shouldFetchConversationSetting = shouldFetchConversationSettingByDealerId({
    dealerId,
    conversationSettingsByDealerId,
    getFeatureValue,
    customerType,
  });

  if (!shouldFetchConversationSetting) return;
  const department = conversationReader.department(conversation);
  const conversationSettingPayload = makeConversationSettingPayload({ department, dealerId, customerType });
  dispatch(fetchConversationSetting(conversationSettingPayload, dealerId));
};

export const handleSelectInitialConversation = async ({
  dispatch,
  hits = EMPTY_ARRAY,
  department,
  selectedTabId,
  isCRMEnabled,
  isCOVEnabled,
  isMultiSiteEnabled,
  getFeatureValue,
  conversationSettingsByDealerId,
}) => {
  const currentSelectedConversationId = getSelectedConversationIdFormLS(department, selectedTabId);
  if (!currentSelectedConversationId) {
    return;
  }
  const selectedConversation = _find(hits, { id: currentSelectedConversationId }) || EMPTY_OBJECT;

  if (_isEmpty(selectedConversation)) {
    const response = await searchConversationById({
      conversationId: currentSelectedConversationId,
      department,
      selectedTabId,
      isCRMEnabled,
      isCOVEnabled,
      isMultiSiteEnabled,
    });
    const conversation = _get(response, 'hits.0');
    if (conversation) {
      manageFetchConversationSettings({ conversationSettingsByDealerId, getFeatureValue, dispatch, conversation });
      compose(
        dispatch,
        actionCreators.addConversationAction
      )({
        department,
        conversation,
        shouldSelectConversation: true,
      });
    }
  } else {
    manageFetchConversationSettings({
      conversationSettingsByDealerId,
      getFeatureValue,
      dispatch,
      conversation: selectedConversation,
    });
    compose(
      dispatch,
      actionCreators.selectConversationAction
    )({
      department,
      conversationId: currentSelectedConversationId,
    });
  }
};

export const fetchNDCPreferenceForContacts = ({
  conversations,
  getNDCPreferences,
  department,
  canadaCommunicationOptInPreferencesEnabled,
  dealerOptInSettings,
}) => {
  if (_isEmpty(conversations)) return;
  const fetchCommunicationPreferencesPayload = makeFetchCommunicationPreferencesPayload(conversations);
  getNDCPreferences(
    fetchCommunicationPreferencesPayload,
    department,
    canadaCommunicationOptInPreferencesEnabled,
    undefined,
    dealerOptInSettings
  );
};

export const handleDealerChangeAction = selectedDealers => dispatch => {
  dispatch([actionCreators.setSelecteDealersAction({ selectedDealers })]);
};

export const fetchConversationsForDepartment =
  ({
    department,
    conversationList,
    selectedTabId,
    selectedFilter = FILTER_TYPE_KEYS.ALL,
    selectedSortOrder = SORT_ORDER_KEYS.NEWEST,
    isCRMEnabled,
    isCOVEnabled,
    afterFetchConversationCb = _noop,
    getNDCPreferences = _noop,
    isMultiSiteEnabled,
    canadaCommunicationOptInPreferencesEnabled,
    selectedDealers,
    getFeatureValue,
    shouldResetConversation,
  }) =>
  async (dispatch, getState) => {
    const state = getState();
    dispatch(createIsErrorInFetchingConversationSettingsByDealerIdAction({ isError: false }));
    const conversationSettingsByDealerId = getConversationSettingsByDealerId(state);
    const dealerOptInSettings = getDealerOptInSettings(state);
    try {
      const isCommunicationV2Enabled = getFeatureValue(FEATURE_NAME.COMMUNICATION_V2_ENABLED);
      const lastConversationObject = _last(conversationList);
      const request = getConversationFrameRequest({
        departmentId: department.id,
        lastConversationObject,
        pageSize: compose(getPageSizeByConversationListSize, _size)(conversationList),
        selectedTabId,
        selectedFilter,
        selectedSortOrder,
        isCRMEnabled,
        isCOVEnabled,
        isMultiSiteEnabled,
        selectedDealers,
        useEnumFilters: isCommunicationV2Enabled,
        shouldResetConversation,
      });

      const rawConversations = await searchConversationByLastConversation(request);
      const conversationsResponse = getDataFromResponse(rawConversations);
      dispatch(createSaveConversationsResponseAction({ conversationsResponse, department: department.id }));
      const data = getConversationsWithInfoFromResponse(rawConversations) || EMPTY_OBJECT;
      const resolvedConversationList = await resolver.getResolvedData(undefined, data.hits, {
        entityAdditionalProps: EXTERNAL_CONVERSATION_ENTITY,
      });
      if (getNDCPreferences) {
        fetchNDCPreferenceForContacts({
          conversations: resolvedConversationList,
          getNDCPreferences,
          department: department.id,
          canadaCommunicationOptInPreferencesEnabled,
          dealerOptInSettings,
        });
      }

      if (!lastConversationObject) {
        handleSelectInitialConversation({
          dispatch,
          hits: resolvedConversationList,
          department: department.id,
          selectedTabId,
          isCRMEnabled,
          isCOVEnabled,
          isMultiSiteEnabled,
          getFeatureValue,
          conversationSettingsByDealerId,
        });
      }

      const conversationPayload = {
        department: department.id,
        data: resolvedConversationList || EMPTY_ARRAY,
        count: data.count,
      };

      if (shouldResetConversation) {
        compose(dispatch, actionCreators.createReplaceConversationListAction)(conversationPayload);
      } else {
        compose(dispatch, actionCreators.createSaveConversationListAction)(conversationPayload);
      }
      afterFetchConversationCb();
    } catch (error) {
      toaster(
        TOASTER_TYPE.ERROR,
        __('Failed to load conversations for {{department}}. Please try again later.', {
          department: department?.name,
        })
      );
    }
  };

export const reFetchConversations =
  ({
    department,
    conversationList = EMPTY_ARRAY,
    selectedTabId,
    selectedFilter,
    selectedSortOrder,
    isCRMEnabled,
    isCOVEnabled,
    isMultiSiteEnabled,
    getFeatureValue,
  }) =>
  dispatch => {
    const pageSize = conversationList.length || DEFAULT_CONVERSATION_PAGE_SIZE;
    const isCommunicationV2Enabled = getFeatureValue(FEATURE_NAME.COMMUNICATION_V2_ENABLED);
    const request = getConversationFrameRequest({
      departmentId: department.id,
      pageSize,
      selectedTabId,
      selectedFilter,
      selectedSortOrder,
      isCRMEnabled,
      isCOVEnabled,
      isMultiSiteEnabled,
      useEnumFilters: isCommunicationV2Enabled,
    });

    return searchConversationByLastConversation(request).then(response => {
      const data = getConversationsWithInfoFromResponse(response) || EMPTY_OBJECT;
      const conversationsResponse = getDataFromResponse(response);
      dispatch(createSaveConversationsResponseAction({ conversationsResponse, department: department.id }));
      resolver
        .getResolvedData(undefined, data.hits, { entityAdditionalProps: EXTERNAL_CONVERSATION_ENTITY })
        .then(resolvedConversationList => {
          compose(
            dispatch,
            actionCreators.resetConversationsAction
          )({
            department: department.id,
            data: resolvedConversationList || EMPTY_ARRAY,
            count: data.count,
          });
        })
        .catch(error => {
          toaster(
            TOASTER_TYPE.ERROR,
            __('Failed to load conversations for {{department}}. Please try again later.', {
              department: department?.name,
            })
          );
        });
    });
  };

export const resetOtherDepartments = (selectedDepartment, departments) => dispatch => {
  const actions = [];
  _forEach(departments, department => {
    if (department.id !== selectedDepartment.id) {
      actionCreators.clearReducerAction({
        department: department.id,
      });
    }
  });
  dispatch(actions);
};

export const addConversation = actionCreators.addConversationAction;
export const setConversationById = actionCreators.setConversationByIdAction;
export const setTempConversation = actionCreators.setTempConversationAction;

const getResolvedConversationData = async response => {
  const data = getDataFromResponse(response);
  if (_isEmpty(data)) return EMPTY_OBJECT;
  const { conversation } = data;
  try {
    const resolvedConversation = await resolveConversation(conversation);
    return { ...data, conversation: resolvedConversation };
  } catch (e) {
    return data;
  }
};

export const initiateConversation = async (request = EMPTY_OBJECT, params) => {
  const response = await startConversation(request, params);
  const resolvedData = await getResolvedConversationData(response);
  return resolvedData;
};

export const fetchCustomerInfo =
  ({ conversationId, customerId, department, queryParams }) =>
  dispatch => {
    compose(
      dispatch,
      actionCreators.setCustomerProfileLoadingAction
    )({
      department,
      conversationId,
    });
    return getLookupByKeys(RESOURCE_TYPE.CUSTOMER_MINIMAL, [customerId], queryParams)
      .then(response => {
        compose(
          dispatch,
          actionCreators.setCustomerProfileData
        )({
          department,
          data: _get(response, `${customerId}.data`),
          conversationId,
        });
      })
      .catch(() => {
        compose(
          dispatch,
          actionCreators.setCustomerProfileData
        )({
          department,
          conversationId,
        });
      });
  };

export const fetchLeadInfo =
  ({ conversationId, customerId, department, additional }) =>
  dispatch => {
    compose(
      dispatch,
      actionCreators.setCustomerProfileLoadingAction
    )({
      department,
      conversationId,
    });
    const payload = new Request().addFilter('leadId', OPERATORS.IN, [customerId]);
    const payloadWithSource = addSourceInPayload(payload, SOURCE.COMMUNICATION__TEXT_ICON_CLICK__UI);
    const queryParams = makeDealerContextQueryParams(additional);
    return searchLeads(payloadWithSource, queryParams)
      .then(response => {
        const data = getDataFromResponse(response);
        const list = esResponseReader.hits(data) || EMPTY_ARRAY;
        const leadDetails = _head(list);
        const leadDepartment = tget(leadDetails, 'department', '');

        compose(
          dispatch,
          actionCreators.setCustomerProfileData
        )({
          department,
          data: { leadDepartment, ...leadDetails },
          conversationId,
        });
      })
      .catch(() => {
        compose(
          dispatch,
          actionCreators.setCustomerProfileData
        )({
          department,
          conversationId,
        });
      });
  };

export const fetchCRMCustomer =
  ({ conversationId, customerId, department, featureControls, siteId, additional }) =>
  async dispatch => {
    try {
      compose(
        dispatch,
        actionCreators.setCustomerProfileLoadingAction
      )({
        department,
        conversationId,
      });
      const payload = {
        crmBuyerCustomerId: customerId,
        pageInfo: { start: 0 },
      };
      const queryParams = makeDealerContextQueryParams(additional);
      const isMultiSiteEnabled = featureControls.getIsMultiSiteEnabled();
      const updatedPayload = isMultiSiteEnabled ? { ...payload, siteIds: castArrayIfPresent(siteId) } : payload;
      const { data: crmCustomerInfo } = await searchCRMCustomer(updatedPayload, queryParams);
      if (_isEmpty(crmCustomerInfo)) throw Error(__('customer info cannot be empty'));
      const lead = getLeadDetailsForNavigation(crmCustomerInfo);
      compose(
        dispatch,
        actionCreators.setCustomerProfileData
      )({
        department,
        data: lead,
        conversationId,
      });
    } catch {
      compose(
        dispatch,
        actionCreators.setCustomerProfileData
      )({
        department,
        conversationId,
      });
    }
  };

export const reduceUnreadCount =
  ({ department, conversationId, unreadCount, currentUserId, isMyConversation, selectedFilter, messageIds }) =>
  dispatch => {
    compose(
      dispatch,
      actionCreators.reduceUnreadCountAction
    )({
      department,
      conversationId,
      unreadCount,
      currentUserId,
      isMyConversation,
      messageIds,
    });
    const showOnlyUnreadMessages = isUnreadMessageFilterSelected(selectedFilter);
    if (showOnlyUnreadMessages) {
      handleRemoveConversationSuccess({ department, conversationId, dispatch })();
    }
  };

export const increaseUnreadCount = (params = EMPTY_OBJECT) => {
  const { department, conversationId, isMyConversation } = params;
  return actionCreators.incrementUnreadCountAction({
    department,
    conversationId,
    isMyConversation,
  });
};

export const saveCurrentConversation = (params = EMPTY_OBJECT) => {
  const { department, conversationId } = params;
  return actionCreators.selectConversationAction({
    department,
    conversationId,
  });
};

export const setCustomerPreference =
  ({ department, conversation, customerPreference }) =>
  dispatch => {
    compose(dispatch, actionCreators.setCustomerPreference)(
      department,
      conversationReader.id(conversation),
      customerPreference
    );
  };

export const setParentConversationToReferenceIdMap =
  ({ department, parentConversationId, referenceIdVsConversationMap }) =>
  dispatch => {
    compose(
      dispatch,
      actionCreators.setParentConversationToReferenceIdMap
    )({ department, parentConversationId, referenceIdVsConversationMap });
  };

export const resetSelectedConversation = actionCreators.resetSelectedConversationId;

export const clearTempConversation = actionCreators.clearTempConversationAction;

const handleRemoveConversationSuccess =
  ({ department, conversationId, dispatch }) =>
  () => {
    const {
      removeCustomerConversationAction,
      createRemoveReferenceInfoForConversationAction,
      createRemoveCustomerPreferenceForConversationAction,
    } = actionCreators;
    dispatch([
      removeCustomerConversationAction({ department, conversationId }),
      createRemoveReferenceInfoForConversationAction({ department, conversationId }),
      createRemoveCustomerPreferenceForConversationAction({ department, conversationId }),
    ]);
  };

const handleRemoveConversationFailure = () => toaster('error', __('Unable to delete conversation'));

export const removeCustomerConversation =
  (params = EMPTY_OBJECT) =>
  dispatch => {
    const { department, conversationId, queryParams } = params;
    return removeConversation(conversationId, undefined, queryParams)
      .then(handleRemoveConversationSuccess({ department, conversationId, dispatch }))
      .catch(handleRemoveConversationFailure);
  };

export const ignoreConversationById = (params = EMPTY_OBJECT) => {
  const { conversationId, additional } = params;
  const queryParams = makeDealerContextQueryParams(additional);
  return ignoreConversation(_castArray(conversationId), queryParams);
};

export const removeCustomerConversationAction =
  (params = EMPTY_OBJECT) =>
  dispatch => {
    const { department, conversationId } = params;
    return handleRemoveConversationSuccess({ department, conversationId, dispatch })();
  };

export const associateConversationById = (params = EMPTY_OBJECT) => {
  const { conversationId, customerId, customerType, customerName, queryParams } = params;
  const payload = {
    conversationId,
    customerId,
    customerType,
    customerName,
  };
  return associateConversation(payload, queryParams);
};

export const setExternalFilter = (params = EMPTY_OBJECT) => {
  const { department, selectedFilter } = params;
  return actionCreators.setExternalFilter({ department, selectedFilter });
};

export const setExternalSortOrder = (params = EMPTY_OBJECT) => {
  const { department, selectedSortOrder } = params;
  return actionCreators.setExternalSortOrder({ department, selectedSortOrder });
};

export const removeReferenceInfoForConversations = (params = EMPTY_OBJECT) => {
  const { department } = params;
  return actionCreators.createRemoveReferenceInfoForConversationsAction({ department });
};

export const removeCustomerPreferenceForConversations = (params = EMPTY_OBJECT) => {
  const { department } = params;
  return actionCreators.createRemoveCustomerPreferenceForConversationsAction({ department });
};

export const changeExternalUnreadCount =
  (params = EMPTY_OBJECT) =>
  dispatch => {
    const { departmentId, conversationId, unreadCount } = params;
    const actionsToDispatch = [
      actionCreators.createChangeExternalConversationUnreadCountAction({
        department: departmentId,
        conversationId,
        unreadCount,
      }),
      actionCreators.createChangeExternalDepartmentUnreadCountAction({ department: departmentId, unreadCount }),
    ];
    dispatch(actionsToDispatch);
  };

export const setExternalConversationListTab = (params = EMPTY_OBJECT) => {
  const { departmentId, selectedTabId } = params;
  return actionCreators.createSetExternalConversationListTabAction({
    department: departmentId,
    selectedTabId,
  });
};

export const markMessagesAsNoResponseRequired = ({
  department,
  conversationId,
  messageIds,
  lastMessageIdMarkedAsNoResponse,
  unansweredCount,
}) =>
  actionCreators.createMarkMessagesAsNoResponseRequiredAction({
    department,
    conversationId,
    messageIds,
    lastMessageIdMarkedAsNoResponse,
    unansweredCount,
  });

export const getExternalConversationBatchedActions =
  ({ filterToSet, tabToSet, sortOrderToSet, departmentId }) =>
  (dispatch, getState) => {
    const state = getState();
    const communicationState = state[COMMUNICATION_STORE_NAME];
    const selectedDepartment = getSelectedDepartment(state) || EMPTY_OBJECT;
    const selectedTabId = getSelectedTabIdForDepartment(communicationState, departmentId);
    const externalTabToBeSet = tabToSet || selectedTabId;
    const selectedFilter = getSelectedFilterForDepartment(communicationState, departmentId, externalTabToBeSet);
    const selectedSortOrder = getSelectedSortOrderForDepartment(communicationState, departmentId, externalTabToBeSet);
    const { id: selectedDepartmentId } = selectedDepartment;

    const actionsToDispatch = [];
    if (selectedDepartmentId !== departmentId) {
      actionsToDispatch.push(
        actionCreators.setDepartmentAction({
          department: DEPARTMENTS_MAP[departmentId],
        })
      );
    }

    if (tabToSet && tabToSet !== selectedTabId) {
      actionsToDispatch.push(
        actionCreators.createSetExternalConversationListTabAction({
          department: departmentId,
          selectedTabId: tabToSet,
        })
      );
    }

    if (filterToSet && filterToSet !== selectedFilter) {
      actionsToDispatch.push(
        actionCreators.setExternalFilter({ department: departmentId, selectedFilter: filterToSet })
      );
    }

    if (sortOrderToSet && sortOrderToSet !== selectedSortOrder) {
      actionsToDispatch.push(
        actionCreators.setExternalSortOrder({ department: departmentId, selectedSortOrder: sortOrderToSet })
      );
    }

    if (!_isEmpty(actionsToDispatch)) {
      return dispatch(actionsToDispatch);
    }
  };
