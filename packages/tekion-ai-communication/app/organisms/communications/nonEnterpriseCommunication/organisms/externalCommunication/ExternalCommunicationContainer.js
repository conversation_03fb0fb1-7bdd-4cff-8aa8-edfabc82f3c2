import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { compose } from 'recompose';

import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';

import withActions from '@tekion/tekion-components/src/connectors/withActions';
import { CommunicationEvents } from '@tekion/tekion-base/helpers/eventEmitter';

import withOEMSites from '@tekion/tekion-widgets/src/appServices/crm/hocs/withOEMSites';
import PermissionPlaceHolder from '@tekion/tekion-widgets/src/organisms/communications/atoms/permissionPlaceHolder';
import { COMMUNICATION_STORE_NAME } from '../../../constants/communications.general';
import { COMMUNICATION_EVENT_TYPE } from '@tekion/tekion-widgets/src/organisms/communications/constants/communications.general';
import COMMUNICATION_EVENTS from '@tekion/tekion-widgets/src/organisms/communications/constants/communications.events';
import conversationReader from '@tekion/tekion-widgets/src/organisms/communications/readers/conversation.reader';
import {
  getConversationsCount,
  getSelectedConversation,
  getSelectedConversationId,
  getCustomerInfo,
  getConversationsList,
  getMediaList,
  getMessagesList,
  getMessagesGroupedByReferences,
  getDepartmentPhoneNumbers,
  getCustomerPreferenceByConversationId,
  getSelectedTabId,
  getUnreadCountPerDept,
  getShowMessagesByReference,
  getDataComplianceDetailsFormSelectedConversation,
  getDeceasedData,
} from '@tekion/tekion-widgets/src/organisms/communications/nonEnterpriseCommunication/reducers/selector';
import { withCommunicationSetupConsumer } from '@tekion/tekion-widgets/src/appServices/crm/communicationSetup';

import ExternalCommunication from './ExternalCommunication';

import { getReferenceInfoForSelectedConversationId } from '../../reducers/selectors/nonEnterpriseCommunication.references';
import {
  getSortedPermissibleUserIds,
  getUsersMap,
  getSelectedDealers,
} from '../../reducers/selectors/nonEnterpriseCommunication.container';
import { fetchInternalMessagesFromConversation } from '../../actions/internalMessages.actions';
import {
  fetchConversationsForDepartment,
  handleDealerChangeAction,
  addConversation,
  fetchCustomerInfo,
  fetchLeadInfo,
  fetchCRMCustomer,
  reFetchConversations,
  resetOtherDepartments,
  saveCurrentConversation,
  setCustomerPreference,
  removeCustomerConversation,
} from '../../actions/conversations.actions';
import {
  changeReferenceOfMessages,
  setSelectedReferenceForConversation,
  clearCurrentSelectedReferenceConversation,
} from '../../actions/references.actions';
import {
  sendMessageToConversation,
  fetchConversationMessages,
  removeMessageFromConversation,
  getMessagesByMessage,
  fetchMessagesAndReferencesFromConversation,
  markMessageAsStarred,
  clearMessagesFromConversation,
  retrySendMessageToConversation,
} from '../../actions/messages.actions';
import { fetchUsersForPermissions } from '../../actions/container.actions';
import { fetchConversationSetting } from '../../actions/externalCommunication/externalCommunication.conversationSetting';
import { getReferencesByConversationId } from '../../../helpers/communications.referencesData';
import ACTION_HANDLERS from './externalCommunication.actionHandlers';
import { shouldShowContactListCoverSpinner } from '../../helpers/communication.external';
import { getConversationsResponse } from '../../reducers/selectors/externalCommunication/externalCommunication.conversations';
import { getWorkspaceDetails } from '../../reducers/selectors/externalCommunication/externalCommunication.workspaceDetails';
import {
  selectNotificationConversation,
  shouldShowPermissionPlaceholder,
} from './helpers/externalCommunication.general';
import { INITIAL_STATE } from './constants/externalCommunication.general';

class ExternalCommunicationContainer extends React.Component {
  componentDidMount() {
    CommunicationEvents.on(COMMUNICATION_EVENT_TYPE, this.handleEvents);
    const { openCommunicationConversationContext, onAction } = this.props;

    const {
      conversationId: notificationConversionId,
      communicationType,
      conversation,
    } = openCommunicationConversationContext || EMPTY_OBJECT;
    if (selectNotificationConversation(notificationConversionId, communicationType)) {
      onAction({
        type: COMMUNICATION_EVENTS.SELECT_CONVERSATION,
        payload: conversation,
      });
    }
  }

  componentWillUnmount() {
    CommunicationEvents.removeListener(COMMUNICATION_EVENT_TYPE, this.handleEvents);
  }

  handleEvents = (context = EMPTY_OBJECT) => {
    const { type } = context;
    const { onAction } = this.props;
    if (type) {
      onAction(context);
    }
  };

  render() {
    const { showExternalCommunication, loading, conversationList, displaySettings, communicationSetupConfig } =
      this.props;
    const { isEnterpriseCommunicationEnabled } = displaySettings;
    const showPermissionPlaceholder = shouldShowPermissionPlaceholder(
      showExternalCommunication,
      loading,
      isEnterpriseCommunicationEnabled
    );

    if (showPermissionPlaceholder) {
      return <PermissionPlaceHolder label={__('You do not have permission to access External Chat')} />;
    }

    const showContactListCoverSpinner = shouldShowContactListCoverSpinner(conversationList, loading);

    return (
      <ExternalCommunication
        {...this.props}
        showContactListCoverSpinner={showContactListCoverSpinner}
        getReferencesByConversationId={getReferencesByConversationId}
        communicationSetupConfig={communicationSetupConfig}
      />
    );
  }
}

ExternalCommunicationContainer.propTypes = {
  ...ExternalCommunication.propTypes,
  onAction: PropTypes.func.isRequired,
  selectedDepartment: PropTypes.object,
  showExternalCommunication: PropTypes.bool,
  loading: PropTypes.bool,
  conversationList: PropTypes.array,
  phoneNumbersByDepartment: PropTypes.object,
  messagesList: PropTypes.array,
  messagesByReferences: PropTypes.array,
  conversationsResponse: PropTypes.object,
  displaySettings: PropTypes.object,
  communicationSetupConfig: PropTypes.object,
  dealerOptInSettings: PropTypes.object,
};

ExternalCommunicationContainer.defaultProps = {
  ...ExternalCommunication.defaultProps,
  selectedDepartment: EMPTY_OBJECT,
  showExternalCommunication: false,
  loading: false,
  conversationList: EMPTY_ARRAY,
  phoneNumbersByDepartment: EMPTY_OBJECT,
  messagesList: EMPTY_ARRAY,
  messagesByReferences: EMPTY_ARRAY,
  conversationsResponse: EMPTY_OBJECT,
  displaySettings: EMPTY_OBJECT,
  communicationSetupConfig: EMPTY_OBJECT,
  dealerOptInSettings: EMPTY_OBJECT,
};

const mapStateToProps = (state, props) => {
  const selectedConversation = getSelectedConversation(state);
  const customerInfo = getCustomerInfo(state);
  const conversationList = getConversationsList(state);
  const conversationId = getSelectedConversationId(state);
  const conversationsCount = getConversationsCount(state);
  const communicationState = state[COMMUNICATION_STORE_NAME];
  const unreadCountPerDept = getUnreadCountPerDept(state);
  const mediaList = getMediaList(state);
  const phoneNumbersByDepartment = getDepartmentPhoneNumbers(state);
  const messagesList = getMessagesList(state);
  const messagesByReferences = getMessagesGroupedByReferences(state);
  const customerPreferenceByConversationId = getCustomerPreferenceByConversationId(state);
  const referenceInfo = getReferenceInfoForSelectedConversationId(state);
  const permissibleUserIds = getSortedPermissibleUserIds(state);
  const usersMap = getUsersMap(state);
  const selectedTabId = getSelectedTabId(state);
  const showMessagesByReference = getShowMessagesByReference(state);
  const dataComplianceDetails = getDataComplianceDetailsFormSelectedConversation(state);
  const isDeceased = getDeceasedData(state);
  const dealerId = conversationReader.dealerId(selectedConversation);
  const siteId = conversationReader.siteId(selectedConversation);
  const selectedDealers = getSelectedDealers(state);
  const conversationsResponse = getConversationsResponse(state, props?.selectedDepartment?.id);
  const additional = {
    dealerId,
    siteId,
  };
  const workspaceDetails = getWorkspaceDetails(state);

  return {
    customerInfo,
    conversationList,
    conversationId,
    conversationsCount,
    selectedConversation,
    unreadCountPerDept,
    mediaList,
    phoneNumbersByDepartment,
    communicationState,
    customerPreferenceByConversationId,
    referenceInfo,
    permissibleUserIds,
    usersMap,
    messagesByReferences,
    selectedTabId,
    dataComplianceDetails,
    isDeceased,
    showMessagesByReference,
    messagesList,
    additional,
    selectedDealers,
    conversationsResponse,
    workspaceDetails,
  };
};

export default compose(
  connect(mapStateToProps, {
    fetchConversationsForDepartment,
    handleDealerChangeAction,
    sendMessageToConversation,
    retrySendMessageToConversation,
    fetchConversationMessages,
    fetchMessagesAndReferencesFromConversation,
    addConversation,
    fetchCustomerInfo,
    fetchLeadInfo,
    fetchCRMCustomer,
    markMessageAsStarred,
    reFetchConversations,
    resetOtherDepartments,
    saveCurrentConversation,
    removeMessageFromConversation,
    getMessagesByMessage,
    setCustomerPreference,
    changeReferenceOfMessages,
    setSelectedReferenceForConversation,
    clearCurrentSelectedReferenceConversation,
    fetchInternalMessagesFromConversation,
    removeCustomerConversation,
    clearMessagesFromConversation,
    fetchUsersForPermissions,
    fetchConversationSetting,
  }),
  withActions(INITIAL_STATE, ACTION_HANDLERS),
  withCommunicationSetupConsumer,
  withOEMSites
)(ExternalCommunicationContainer);
