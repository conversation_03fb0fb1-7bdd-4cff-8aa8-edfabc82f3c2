import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { compose } from 'recompose';
import _isEmpty from 'lodash/isEmpty';
import _forEach from 'lodash/forEach';
import _noop from 'lodash/noop';
import _castArray from 'lodash/castArray';

import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import DEALER_PROPERTIES from '@tekion/tekion-base/constants/dealerProperties';
import { TENANT_PROPERTIES } from '@tekion/tekion-base/constants/tenantProperties';
import { WIDGET_ACTIONS } from '@tekion/tekion-components/src/emitters/WidgetEventEmitter';
import withActions from '@tekion/tekion-components/src/connectors/withActions';
import { CommunicationEvents } from '@tekion/tekion-base/helpers/eventEmitter';

import { withTekionConversion } from '@tekion/tekion-conversion-web';
import withCallCommunication from '@tekion/tekion-widgets/src/connectors/withCallCommunication';
import { withExperienceEngineConsumer } from '@tekion/tekion-widgets/src/experienceEngine';
import { withNDC } from '@tekion/tekion-widgets/src/appServices/crm/organisms/ndc';
import communicationWithEvents from '@tekion/tekion-widgets/src/hocs/communicationWithEvents';
import { isLiveChatDpOnAndDealerWorkspace } from '@tekion/tekion-widgets/src/helpers/general';

import {
  COMMUNICATION_EVENT_TYPE,
  OPEN_COMMUNICATION,
} from '@tekion/tekion-widgets/src/organisms/communications/constants/communications.general';
import { getPropertyInSessionStorage } from '@tekion/tekion-widgets/src/organisms/communications/helpers/communications.sessionStorage';
import { COMMUNICATION_SESSION_STORAGE_PROPS } from '@tekion/tekion-widgets/src/organisms/communications/constants/communications.localAndSessionStorage';
import COMMUNICATION_EVENTS from '@tekion/tekion-widgets/src/organisms/communications/constants/communications.events';
import withMediaPreview from '@tekion/tekion-widgets/src/organisms/communications/containers/withMediaPreview';
import withLivePusherHoc from '@tekion/tekion-widgets/src/organisms/communications/nonEnterpriseCommunication/organisms/liveCommunication/livePusher/WithLivePusherHoc';
import {
  getConversationIds,
  getConversationsList,
  getExternalDepartmentSelectedFilter,
  getExternalSortOrder,
  getSelectedConversation,
  getSelectedConversationId,
  getSelectedDepartment,
  getLiveUnreadCount,
  getChatAppName,
  getSelectedTabId,
  getParentConvIdToReferenceConversation,
  getCanadaCommunicationOptInPreferencesEnabled,
} from '@tekion/tekion-widgets/src/organisms/communications/nonEnterpriseCommunication/reducers/selector';
import { getSelectedInternalConversationId } from '@tekion/tekion-widgets/src/organisms/communications/nonEnterpriseCommunication/reducers/selectors/nonEnterpriseCommunication.general';
import COMMUNICATION_TYPES from '@tekion/tekion-widgets/src/organisms/communications/constants/communications.types';
import {
  CONVERSATION_AND_CHAT_STATUS,
  LIVE_ACTION_EVENTS,
  LIVE_COMMUNICATION_EVENTS,
} from '@tekion/tekion-widgets/src/organisms/communications/nonEnterpriseCommunication/organisms/liveCommunication/constants';
import {
  updateChatFilters,
  setActiveConversationId,
  getLiveMessageUnreadCount,
  fetchLiveChatIndicator,
  fetchPusherInstance,
  updateLiveChatUnreadCounter,
  updateRescueNeededCounter,
  updateDepartment,
  updateDepartmentChangeRequest,
  setSearchVisible,
  setChatListCustomStatus,
} from '@tekion/tekion-widgets/src/organisms/communications/nonEnterpriseCommunication/actions/LiveConversations.actions';
import liveCommunicationReducer from '@tekion/tekion-widgets/src/organisms/communications/nonEnterpriseCommunication/reducers/liveCommunication';
import { getLiveChatPusherInstance } from '@tekion/tekion-widgets/src/organisms/AppSkeleton/appSkeleton/skeleton.selector';

import { COMMUNICATION_STORE_NAME } from '../../../constants/communications.general';
import { communicationReducer, communicationInternalReducer } from '../../reducers';
import { getSelectedDealers, getDealerInfoMap } from '../../reducers/selectors/nonEnterpriseCommunication.container';
import NonEnterpriseCommunication from './NonEnterpriseCommunication';
import communicationWithActiveTab from '../../../containers/communicationWithActiveTab';
import withCommunicationDealerProperty from '../../containers/withCommunicationDealerProperty';

import ACTION_HANDLERS from '../../actionHandlers/communication.actionHandlers';
import { sendActionToWidgetPanel } from '../../../helpers/communications.chatWidget';
import { getSelectedCommunicationTypeFromLocalStorage } from './helpers/nonEnterpriseCommunication.localStorage';

import {
  addMessageToConversation,
  clearAndFetchConversationMessages,
  fetchMessagesAndReferencesFromConversation,
  getMessagesByIds,
  getMessagesByMessage,
  markAsStarredMessageAction,
  removeMessageFromConversation,
  updateMessageStatus,
  sendOptInMessage,
  setShowMessagesByReference,
  resetShowMessagesByReference,
} from '../../actions/messages.actions';
import {
  addConversation,
  clearTempConversation,
  fetchConversationsForDepartment,
  increaseUnreadCount,
  reduceUnreadCount,
  reFetchConversations,
  removeCustomerConversationAction,
  resetOtherDepartments,
  resetSelectedConversation,
  saveCurrentConversation,
  setConversationById,
  setCustomerPreference,
  setExternalFilter,
  setExternalSortOrder,
  setTempConversation,
  removeReferenceInfoForConversations,
  removeCustomerPreferenceForConversations,
  changeExternalUnreadCount,
  setExternalConversationListTab,
  setParentConversationToReferenceIdMap,
  markMessagesAsNoResponseRequired,
  getExternalConversationBatchedActions,
  handleDealerChangeAction,
} from '../../actions/conversations.actions';
import {
  getInternalConversations,
  increaseInternalUnreadCount,
  setInternalConversationType,
  setInternalSelectedFilters,
  reduceInternalUnreadCount,
  setConversation,
  setInternalCustomerInfo,
  clearInternalCustomerInfo,
  updateGroupConversation,
  removeInternalConvesationAndMessages,
} from '../../actions/internalConversations.actions';
import {
  addInternalMessageToConversation,
  addStarredUserIdsToMessage,
  fetchInternalMessagesFromConversation,
} from '../../actions/internalMessages.actions';
import {
  setSelectedReferenceForConversation,
  clearCurrentSelectedReferenceConversation,
} from '../../actions/references.actions';
import { createApiBatcherInstance, fetchMetaData, setDepartment } from '../../actions/container.actions';
import { saveResourceById } from '../../actions/lookup.actions';
import { removeMediaFromList, resetMediaList, setMediaList } from '../../actions/transientState.actions';
import {
  fetchAiMetaDataAction,
  reInitializeAiMetaDataAction,
} from '../../actions/nonEnterpriseCommunication.aiMetaData';
import {
  fetchQuickRepliesAction,
  reInitializeQuickReplies as reInitializeQuickRepliesAction,
} from '../../actions/nonEnterpriseCommunication.quickReplies';
import { fetchConversationSetting as fetchExternalConversationSetting } from '../../actions/externalCommunication/externalCommunication.conversationSetting';
import { DEEPLINK_META_DATA_INITIAL_STATE } from './constants/nonEnterpriseCommunication.widgets';

import {
  getInternalConversationsList,
  getInternalCustomerInfo,
  getInternalSelectedConversationType,
  getInternalSelectedFilters,
  getInternalTempConversation,
  getSelectedInternalConversation,
} from '../../reducers/selectors/nonEnterpriseCommunication.internalConversations';
import { getReferenceInfoForSelectedConversationId } from '../../reducers/selectors/nonEnterpriseCommunication.references';
import {
  isQuickReplyEnabled,
  isSmartComposeEnabledForEntity,
  getDealerOptInSettings,
  selectQuickReplies,
} from '../../reducers/selectors/nonEnterpriseCommunication.externalMessages';

import { getDisplaySettings as getExternalCommunicationDisplaySetting } from '../../reducers/selectors/externalCommunication/externalCommunication.displaySettings';
import { getConversationSettingsByDealerId as getExternalConvSettingsByDealer } from '../../reducers/selectors/externalCommunication/externalCommunication.conversationSetting';

import { openCommunication } from '../../helpers/communication.openCommunication';
import GET_COMMUNICATION_TYPE_PROCESS from './helpers/nonEnterpriseCommunication.getCommunicationTypeProcess';

import withResourceConsumer from '../resourceProvider/withResourceConsumer';
import withResourceProvider from '../resourceProvider/withResourceProvider';

import { openTextAndSelectConversation } from './helpers/nonEnterpriseCommunication.openConversation';
import { getExternalCommunicationDepartments } from './helpers/nonEnterpriseCommunication.externaldepartments';
import { getWorkspaceDetails } from '../../reducers/selectors/externalCommunication/externalCommunication.workspaceDetails';

const getInitialState = props => {
  const { hidden = true } = props;
  const INITIAL_STATE = {
    minimized: getPropertyInSessionStorage(COMMUNICATION_SESSION_STORAGE_PROPS.MINIMIZED) || false,
    expanded: getPropertyInSessionStorage(COMMUNICATION_SESSION_STORAGE_PROPS.EXPANDED) || false,
    hidden,
    showCustomerProfile: false,
    loading: false,
    areTopMessagesLoading: false,
    areBottomMessagesLoading: false,
    showConversationLoader: false,
    showMessagesLoader: false,
    selectedCommunicationType: getSelectedCommunicationTypeFromLocalStorage(),
    showUserSearchPanel: false,
    onMessageSentCb: undefined,
    templateId: undefined,
    areNewMessagesAddedToSelectedConversation: false,
    deepLinkWidgetMetaData: DEEPLINK_META_DATA_INITIAL_STATE,
    dealerOptinSetting: undefined,
  };
  return INITIAL_STATE;
};

class NonEnterpriseCommunicationWrapper extends React.Component {
  constructor(props) {
    super(props);
    this.clearTimer = 0;
    const { permissions, createApiBatcherInstance: createApiBatcherInstanceAction, getFeatureValue } = this.props;
    const departments = getExternalCommunicationDepartments(permissions, getFeatureValue);
    if (!_isEmpty(departments)) {
      this.injectExternalReducers(departments);
    }
    this.injectInternalReducer();
    this.injectLiveReducer();
    this.state = {
      departments,
      setInitialConversationContext: false,
    };
    createApiBatcherInstanceAction();
  }

  componentDidMount() {
    const { departments } = this.state;
    const { onAction, hidden, minimized, openCommunicationConversationContext, liveDealerPropertyEnabled } = this.props;
    const {
      showContainerWithoutContext,
      isGlobalIconClicked,
      communicationType,
      conversation = EMPTY_OBJECT,
    } = openCommunicationConversationContext || EMPTY_OBJECT;
    const { dealerId } = conversation;
    this.setCommunicationTypeByDealerProperty();
    onAction({ type: COMMUNICATION_EVENTS.FETCH_METADATA, payload: { departments } });

    CommunicationEvents.on(COMMUNICATION_EVENT_TYPE, this.handleEvents);
    CommunicationEvents.on(OPEN_COMMUNICATION, this.handleOpenCommunication);

    if (dealerId) {
      const selectedDealers = _castArray(dealerId);
      onAction({ type: COMMUNICATION_EVENTS.SELECT_DEALERS, payload: { selectedDealers } });
    }
    if (isLiveChatDpOnAndDealerWorkspace(liveDealerPropertyEnabled)) {
      this.getLiveUnreadMessageAction();
    }
    // on page refresh or opening a new tab where widget opens by data from session storage
    if (!hidden && !minimized) {
      sendActionToWidgetPanel(WIDGET_ACTIONS.SELECTED);
    }

    if (showContainerWithoutContext) {
      CommunicationEvents.emit(COMMUNICATION_EVENT_TYPE, {
        type: COMMUNICATION_EVENTS.SHOW_CONTAINER,
        payload: {
          isGlobalIconClicked,
        },
      });
    }

    if (communicationType) {
      this.setCommunicationType(communicationType);
      return;
    }
    openTextAndSelectConversation(openCommunicationConversationContext);
    this.getInitialSelectedDealers();
  }

  componentWillUnmount() {
    CommunicationEvents.removeListener(COMMUNICATION_EVENT_TYPE, this.handleEvents);
    CommunicationEvents.removeListener(OPEN_COMMUNICATION, this.handleOpenCommunication);
    clearInterval(this.clearTimer);
  }

  getInitialSelectedDealers() {
    const { onAction } = this.props;
    onAction({
      type: COMMUNICATION_EVENTS.SELECT_DEALERS,
      payload: { selectedDealers: EMPTY_ARRAY },
    });
  }

  getLiveUnreadMessageAction = () => {
    const { onAction } = this.props;
    onAction({
      type: LIVE_ACTION_EVENTS.GET_LIVE_CHAT_INDICATOR,
    });
    onAction({
      type: LIVE_ACTION_EVENTS.GET_LIVE_MESSAGE_UNREAD_COUNT,
    });
  };

  injectExternalReducers = departments =>
    _forEach(departments, department => {
      const { id: departmentId } = department;
      const { injectReducer } = this.props;
      injectReducer(`${COMMUNICATION_STORE_NAME}.${departmentId}`, communicationReducer(departmentId));
    });

  injectInternalReducer = () => {
    const { injectReducer } = this.props;
    injectReducer(
      `${COMMUNICATION_STORE_NAME}.${COMMUNICATION_TYPES.INTERNAL}`,
      communicationInternalReducer(COMMUNICATION_TYPES.INTERNAL)
    );
  };

  injectLiveReducer = () => {
    const { injectReducer } = this.props;
    injectReducer(`${COMMUNICATION_STORE_NAME}.${COMMUNICATION_TYPES.LIVE}`, liveCommunicationReducer());
  };

  handleEvents = (context = EMPTY_OBJECT) => {
    const { type } = context;
    const { onAction } = this.props;
    if (type) {
      onAction(context);
    }
  };

  handleOpenCommunication = (context = EMPTY_OBJECT) => {
    openCommunication(context, this.props);
    sendActionToWidgetPanel(WIDGET_ACTIONS.SELECTED);
  };

  setCommunicationType = communicationType => {
    const { onAction } = this.props;
    onAction({
      type: COMMUNICATION_EVENTS.SET_SELECTED_COMMUNICATION_TYPE,
      payload: { communicationType },
    });
  };

  setCommunicationTypeByDealerProperty = () => {
    const {
      internalDealerPropertyEnabled,
      liveDealerPropertyEnabled,
      selectedCommunicationType,
      getDealerPropertyValue,
      externalDealerPropertyEnabled,
    } = this.props;

    const communicationType = GET_COMMUNICATION_TYPE_PROCESS.run({
      internalDealerPropertyEnabled,
      liveDealerPropertyEnabled,
      selectedCommunicationType,
      getDealerPropertyValue,
      externalDealerPropertyEnabled,
    });

    this.setCommunicationType(communicationType);
  };

  setSeenNewMessagesInSelectedConversation = () => {
    const { onAction } = this.props;
    onAction({
      type: COMMUNICATION_EVENTS.SEEN_NEW_MESSAGES_IN_SELECTED_CONVERSATION,
    });
  };

  clearDeepLinkWidgetMetaData = () => {
    const { onAction } = this.props;
    onAction({
      type: COMMUNICATION_EVENTS.SET_DEEPLINK_WIDGET_METADATA,
      payload: DEEPLINK_META_DATA_INITIAL_STATE,
    });
  };

  render() {
    const { departments, setInitialConversationContext } = this.state;
    const { hidden, getDealerPropertyValue, livePusherInstance, onAction, getTenantPropertyValue } = this.props;

    if (hidden) {
      onAction({ type: LIVE_COMMUNICATION_EVENTS.SET_SEARCH_VISIBLE, payload: false });
      onAction({
        type: LIVE_COMMUNICATION_EVENTS.SET_CUSTOM_STATUS,
        payload: { status: CONVERSATION_AND_CHAT_STATUS.IDLE },
      });
      return null;
    }

    const isDseStandalone = getDealerPropertyValue(DEALER_PROPERTIES.DSE_STANDALONE);
    const isLivePusherEnabled = getDealerPropertyValue(DEALER_PROPERTIES.LIVE_CHAT_WEBSOCKET_ENABLE);
    const isTenantInternalCommunicationEnabled = getTenantPropertyValue(
      TENANT_PROPERTIES.INTERNAL_TENANT_COMMUNICATION
    );
    return (
      <NonEnterpriseCommunication
        {...this.props}
        departments={departments}
        setInitialConversationContext={setInitialConversationContext}
        setCommunicationType={this.setCommunicationType}
        setSeenNewMessagesInSelectedConversation={this.setSeenNewMessagesInSelectedConversation}
        isDseStandalone={isDseStandalone}
        isLivePusherEnabled={isLivePusherEnabled}
        livePusherInstance={livePusherInstance}
        isTenantInternalCommunicationEnabled={isTenantInternalCommunicationEnabled}
        clearDeepLinkWidgetMetaData={this.clearDeepLinkWidgetMetaData}
      />
    );
  }
}

NonEnterpriseCommunicationWrapper.propTypes = {
  onAction: PropTypes.func.isRequired,
  permissions: PropTypes.array.isRequired,
  injectReducer: PropTypes.func.isRequired,
  selectedDepartment: PropTypes.object,
  minimized: PropTypes.bool.isRequired,
  hidden: PropTypes.bool.isRequired,
  expanded: PropTypes.bool.isRequired,
  showCustomerProfile: PropTypes.bool.isRequired,
  areTopMessagesLoading: PropTypes.bool.isRequired,
  areBottomMessagesLoading: PropTypes.bool.isRequired,
  showConversationLoader: PropTypes.bool.isRequired,
  showMessagesLoader: PropTypes.bool.isRequired,
  internalDealerPropertyEnabled: PropTypes.bool.isRequired,
  liveDealerPropertyEnabled: PropTypes.bool.isRequired,
  externalDealerPropertyEnabled: PropTypes.bool,
  getDealerPropertyValue: PropTypes.func,
  createApiBatcherInstance: PropTypes.func,
  livePusherInstance: PropTypes.object,
  getTenantPropertyValue: PropTypes.func,
  selectedCommunicationType: PropTypes.string,
  openCommunicationConversationContext: PropTypes.object,
  getFeatureValue: PropTypes.func,
  selectedDealers: PropTypes.array,
};

NonEnterpriseCommunicationWrapper.defaultProps = {
  selectedDepartment: EMPTY_OBJECT,
  getDealerPropertyValue: _noop,
  createApiBatcherInstance: _noop,
  livePusherInstance: null,
  getTenantPropertyValue: _noop,
  selectedCommunicationType: undefined,
  externalDealerPropertyEnabled: false,
  openCommunicationConversationContext: EMPTY_OBJECT,
  getFeatureValue: _noop,
  selectedDealers: EMPTY_ARRAY,
};

const mapStateToProps = state => ({
  communicationState: state[COMMUNICATION_STORE_NAME],
  externalConversationList: getConversationsList(state),
  selectedDepartment: getSelectedDepartment(state),
  selectedDealers: getSelectedDealers(state),
  selectedTabId: getSelectedTabId(state),
  selectedFilter: getExternalDepartmentSelectedFilter(state),
  selectedSortOrder: getExternalSortOrder(state),
  selectedConversation: getSelectedConversation(state),
  conversationId: getSelectedConversationId(state),
  selectedConversationType: getInternalSelectedConversationType(state),
  internalSelectedFilters: getInternalSelectedFilters(state),
  internalConversationList: getInternalConversationsList(state),
  internalCustomerDetail: getInternalCustomerInfo(state),
  selectedInternalConversation: getSelectedInternalConversation(state),
  internalTempConversations: getInternalTempConversation(state),
  conversationIds: getConversationIds(state),
  internalConversationId: getSelectedInternalConversationId(state),
  liveMessagesUnreadCount: getLiveUnreadCount(state),
  referenceInfo: getReferenceInfoForSelectedConversationId(state),
  liveChatAppName: getChatAppName(state),
  livePusherInstance: getLiveChatPusherInstance(state),
  parentConvIdToReferenceConversation: getParentConvIdToReferenceConversation(state),
  dealerInfoMap: getDealerInfoMap(state),
  isEntityEligibleForSmartCompose: isSmartComposeEnabledForEntity(state),
  isQuickReplyEnabled: isQuickReplyEnabled(state),
  quickReplies: selectQuickReplies(state),
  canadaCommunicationOptInPreferencesEnabled: getCanadaCommunicationOptInPreferencesEnabled(state),
  externalCommunicationDisplaySetting: getExternalCommunicationDisplaySetting(state),
  externalConversationSettingsByDealer: getExternalConvSettingsByDealer(state),
  workspaceDetails: getWorkspaceDetails(state),
  dealerOptInSettings: getDealerOptInSettings(state),
});

export default compose(
  React.memo,
  withTekionConversion,
  withResourceProvider,
  withResourceConsumer,
  withCommunicationDealerProperty,
  communicationWithActiveTab,
  connect(mapStateToProps, {
    fetchMetaData,
    setDepartment,
    handleDealerChangeAction,
    reduceUnreadCount,
    increaseInternalUnreadCount,
    setInternalConversationType,
    setInternalSelectedFilters,
    reduceInternalUnreadCount,
    addConversation,
    updateGroupConversation,
    setCustomerPreference,
    updateMessageStatus,
    addMessageToConversation,
    increaseUnreadCount,
    getMessagesByIds,
    addInternalMessageToConversation,
    saveCurrentConversation,
    fetchInternalMessagesFromConversation,
    resetOtherDepartments,
    reFetchConversations,
    getMessagesByMessage,
    clearAndFetchConversationMessages,
    fetchMessagesAndReferencesFromConversation,
    getInternalConversations,
    setInternalCustomerInfo,
    clearInternalCustomerInfo,
    addStarredUserIdsToMessage,
    markAsStarredMessageAction,
    setTempConversation,
    setConversation,
    fetchConversationsForDepartment,
    resetSelectedConversation,
    clearTempConversation,
    saveResourceById,
    createApiBatcherInstance,
    setMediaList,
    removeMediaFromList,
    resetMediaList,
    removeMessageFromConversation,
    removeInternalConvesationAndMessages,
    removeCustomerConversationAction,
    setConversationById,
    setExternalFilter,
    setExternalSortOrder,
    removeReferenceInfoForConversations,
    removeCustomerPreferenceForConversations,
    updateChatFilters,
    setActiveConversationId,
    getLiveMessageUnreadCount,
    fetchLiveChatIndicator,
    fetchPusherInstance,
    updateLiveChatUnreadCounter,
    updateRescueNeededCounter,
    updateDepartment,
    updateDepartmentChangeRequest,
    setSelectedReferenceForConversation,
    setShowMessagesByReference,
    resetShowMessagesByReference,
    changeExternalUnreadCount,
    setExternalConversationListTab,
    setParentConversationToReferenceIdMap,
    markMessagesAsNoResponseRequired,
    sendOptInMessage,
    setSearchVisible,
    setChatListCustomStatus,
    getExternalConversationBatchedActions,
    clearCurrentSelectedReferenceConversation,
    fetchAiMetaDataAction,
    fetchQuickRepliesAction,
    reInitializeQuickRepliesAction,
    reInitializeAiMetaDataAction,
    fetchExternalConversationSetting,
  }),
  withNDC,
  withExperienceEngineConsumer,
  withActions(getInitialState, ACTION_HANDLERS),
  communicationWithEvents,
  withCallCommunication,
  withLivePusherHoc,
  withMediaPreview
)(NonEnterpriseCommunicationWrapper);
