import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { compose } from 'recompose';
import _noop from 'lodash/noop';

import DEALER_PROPERTIES from '@tekion/tekion-base/constants/dealerProperties';

import withPropertyConsumer from '@tekion/tekion-components/src/organisms/propertyProvider/withPropertyConsumer';
import withAsyncReducer from '@tekion/tekion-components/src/connectors/withAsyncReducer';
import { COMMUNICATION_STORE_NAME } from '../../constants/communications.general';
import {
  getExternalCommunicationEnabled,
  getInternalCommunicationEnabled,
  getLiveCommunicationEnabled,
} from '@tekion/tekion-widgets/src/organisms/communications/nonEnterpriseCommunication/reducers/selector';
import { fetchBulkDealerProperty } from '../actions/container.actions';
import communicationContainerReducer from '../reducers/container.reducer';
import { allCommunicationsDisabled } from '../helpers/communication.general';

const withCommunicationDealerProperty = WrappedComponent => {
  class WithCommunicationDealerProperty extends Component {
    componentDidMount() {
      this.getDealerProperty();
    }

    getDealerProperty = () => {
      const { getMultipleDealerPropertyValue, fetchBulkDealerProperty: fetchBulkDealerPropertyActions } = this.props;
      const dealerProperty = getMultipleDealerPropertyValue([
        DEALER_PROPERTIES.EXTERNAL_COMMUNICATION,
        DEALER_PROPERTIES.INTERNAL_COMMUNICATION,
        DEALER_PROPERTIES.LIVE_CHAT_WIDGET_ENABLED,
      ]);
      fetchBulkDealerPropertyActions(dealerProperty);
    };

    render() {
      const { internalDealerPropertyEnabled, externalDealerPropertyEnabled, liveDealerPropertyEnabled } = this.props;
      if (
        allCommunicationsDisabled(
          internalDealerPropertyEnabled,
          externalDealerPropertyEnabled,
          liveDealerPropertyEnabled
        )
      ) {
        return null;
      }
      return <WrappedComponent {...this.props} />;
    }
  }

  const mapStateToProps = state => ({
    externalDealerPropertyEnabled: getExternalCommunicationEnabled(state),
    internalDealerPropertyEnabled: getInternalCommunicationEnabled(state),
    liveDealerPropertyEnabled: getLiveCommunicationEnabled(state),
  });

  WithCommunicationDealerProperty.propTypes = {
    fetchBulkDealerProperty: PropTypes.func,
    getMultipleDealerPropertyValue: PropTypes.func.isRequired,
    internalDealerPropertyEnabled: PropTypes.bool,
    externalDealerPropertyEnabled: PropTypes.bool,
    liveDealerPropertyEnabled: PropTypes.bool,
  };

  WithCommunicationDealerProperty.defaultProps = {
    fetchBulkDealerProperty: _noop,
    internalDealerPropertyEnabled: false,
    externalDealerPropertyEnabled: false,
    liveDealerPropertyEnabled: false,
  };

  return compose(
    connect(mapStateToProps, {
      fetchBulkDealerProperty,
    }),
    withPropertyConsumer,
    withAsyncReducer({
      storeKey: `${COMMUNICATION_STORE_NAME}.containerReducer`,
      reducer: communicationContainerReducer,
    })
  )(WithCommunicationDealerProperty);
};

export default withCommunicationDealerProperty;
