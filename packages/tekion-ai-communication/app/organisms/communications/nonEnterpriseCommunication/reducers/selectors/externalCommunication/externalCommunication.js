import compose from 'recompose/compose';
import _property from 'lodash/property';

import { COMMUNICATION_STORE_NAME } from '../../../../constants/communications.general';

export const getCommunicationReducer = _property(COMMUNICATION_STORE_NAME);

const getContainerReducer = _property('containerReducer');

export const getExternalCommunicationReducer = compose(
  _property('externalCommunication'),
  getContainerReducer,
  getCommunicationReducer
);
