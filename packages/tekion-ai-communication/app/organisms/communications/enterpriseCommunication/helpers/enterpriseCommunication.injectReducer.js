// Lodash
import _forEach from 'lodash/forEach';

// Constants
import { EXTERNAL_CONTAINER_STORE_NAME } from '@tekion/tekion-widgets/src/organisms/communications/enterpriseCommunication/constants/enterpriseCommunication.general';
import { COMMUNICATION_STORE_NAME } from '../../constants/communications.general';

// Reducers
import communicationDepartmentReducer from '../organisms/externalCommunication/reducers/externalCommunication.departmentReducer';

const injectReducerForDepartment = injectReducer => department => {
  const { id: departmentId } = department;
  injectReducer(
    `${COMMUNICATION_STORE_NAME}.${EXTERNAL_CONTAINER_STORE_NAME}.${departmentId}`,
    communicationDepartmentReducer(departmentId)
  );
};

export const injectDepartmentReducers = (departments, injectReducer) =>
  _forEach(departments, injectReducerForDepartment(injectReducer));
