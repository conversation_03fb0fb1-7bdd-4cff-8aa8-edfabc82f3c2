/* eslint-disable import/order */
import React from 'react';
import PropTypes from 'prop-types';
import { compose } from 'recompose';
import { connect } from 'react-redux';

// Lodash
import _isEmpty from 'lodash/isEmpty';

// Constants
import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';

// Containers
import withPropertyConsumer from '@tekion/tekion-components/src/organisms/propertyProvider/withPropertyConsumer';
import withActions from '@tekion/tekion-components/src/connectors/withActions';

// Constants
import { INITIAL_STATE } from './constants/externalCommunication.general';
import ExternalCommunication from './ExternalCommunication';
import PermissionPlaceHolder from '@tekion/tekion-widgets/src/organisms/communications/atoms/permissionPlaceHolder';
import EXTERNAL_COMMUNICATION_ACTION_TYPES from './constants/externalCommunication.actionTypes';
import { EXTERNAL_CONTAINER_STORE_NAME } from '@tekion/tekion-widgets/src/organisms/communications/enterpriseCommunication/constants/enterpriseCommunication.general';
import { COMMUNICATION_STORE_NAME } from '../../../constants/communications.general';

// Helpers
import { getReferencesByConversationId } from './helpers/externalCommunication.references';
import { getPermissibleDepartments } from '@tekion/tekion-widgets/src/organisms/communications/helpers/communications.permissions';

// Action Handlers
import ACTION_HANDLERS from './externalCommunication.actionHandlers';

// Actions
import { createApiBatcherInstance } from './actions/externalCommunication.global';
import { setMediaList, removeMediaFromList, resetMediaList } from './actions/externalCommunication.transient';

// Selectors
import { getMediaList } from './reducers/selectors/externalCommunication.transient';

// Reducers
import transientReducer from './reducers/externalCommunication.transient';
import { getUnreadCountPerDept } from '@tekion/tekion-widgets/src/organisms/communications/enterpriseCommunication/reducers/selectors/enterpriseCommunication.metadata';

class ExternalCommunicationContainer extends React.Component {
  constructor(props) {
    super(props);
    const { showExternalCommunication } = props;
    if (showExternalCommunication) {
      this.injectTransientExternalReducer();
    }
  }

  componentDidMount() {
    this.setInitialDepartment();
    this.createApiBatcherInstanceForDragNDrop();
  }

  setInitialDepartment() {
    const { selectedDepartment, showExternalCommunication, onAction, permissions } = this.props;

    if (_isEmpty(selectedDepartment) && showExternalCommunication) {
      const departments = getPermissibleDepartments(permissions);
      onAction({ type: EXTERNAL_COMMUNICATION_ACTION_TYPES.SET_INITIAL_DEPARTMENT, payload: { departments } });
    }
  }

  injectTransientExternalReducer() {
    const { injectReducer } = this.props;
    injectReducer(`${COMMUNICATION_STORE_NAME}.${EXTERNAL_CONTAINER_STORE_NAME}.transient`, transientReducer);
  }

  createApiBatcherInstanceForDragNDrop() {
    const { createApiBatcherInstance: createApiBatcherInstanceAction } = this.props;
    createApiBatcherInstanceAction();
  }

  render() {
    const { showExternalCommunication, loading } = this.props;
    if (!showExternalCommunication && !loading) {
      return <PermissionPlaceHolder label={__('You do not have permission to access External Chat')} />;
    }

    return <ExternalCommunication {...this.props} getReferencesByConversationId={getReferencesByConversationId} />;
  }
}

ExternalCommunicationContainer.propTypes = {
  ...ExternalCommunication.propTypes,
  showExternalCommunication: PropTypes.bool,
  loading: PropTypes.bool,
  selectedDepartment: PropTypes.object,
  permissions: PropTypes.array,
};

ExternalCommunicationContainer.defaultProps = {
  ...ExternalCommunication.defaultProps,
  showExternalCommunication: false,
  loading: false,
  selectedDepartment: EMPTY_OBJECT,
  permissions: EMPTY_ARRAY,
};

const mapStateToProps = state => ({
  mediaList: getMediaList(state),
  unreadCountPerDept: getUnreadCountPerDept(state),
});

const mapDispatchToProps = {
  setMediaList,
  removeMediaFromList,
  resetMediaList,
  createApiBatcherInstance,
};

export default compose(
  withPropertyConsumer,
  connect(mapStateToProps, mapDispatchToProps),
  withActions(INITIAL_STATE, ACTION_HANDLERS)
)(ExternalCommunicationContainer);
