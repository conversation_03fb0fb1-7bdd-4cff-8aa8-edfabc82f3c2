// Lodash
import _property from 'lodash/property';
import _get from 'lodash/get';

import { createSelector } from 'reselect';

// Constants
import { EXTERNAL_CONTAINER_STORE_NAME } from '@tekion/tekion-widgets/src/organisms/communications/enterpriseCommunication/constants/enterpriseCommunication.general';
import { COMMUNICATION_STORE_NAME } from '../../../../../constants/communications.general';

import { getSelectedConversationId } from './externalCommunication.conversation';

const getTransientReducerState = _property(`${COMMUNICATION_STORE_NAME}.${EXTERNAL_CONTAINER_STORE_NAME}.transient`);

const getMediaListFromConversationId = (conversationId, transientState) =>
  _get(transientState, `${conversationId}.mediaList`);

export const getMediaList = createSelector(
  getSelectedConversationId,
  getTransientReducerState,
  getMediaListFromConversationId
);
