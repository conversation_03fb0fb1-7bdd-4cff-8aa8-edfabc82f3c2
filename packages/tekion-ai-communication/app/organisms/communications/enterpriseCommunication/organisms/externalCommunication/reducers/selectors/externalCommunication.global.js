import { compose } from 'recompose';

// Lodash
import _property from 'lodash/property';

// Constants
import { EXTERNAL_CONTAINER_STORE_NAME } from '@tekion/tekion-widgets/src/organisms/communications/enterpriseCommunication/constants/enterpriseCommunication.general';
import { COMMUNICATION_STORE_NAME } from '../../../../../constants/communications.general';

export const getExternalData = _property(`${COMMUNICATION_STORE_NAME}.${EXTERNAL_CONTAINER_STORE_NAME}`);
const getExternalGlobalData = compose(_property('global'), getExternalData);

export const getSelectedDepartment = compose(_property('selectedDepartment'), getExternalGlobalData);

export const getSelectedDepartmentId = compose(_property('id'), getSelectedDepartment);

export const getMetadataByDealer = compose(_property('dealerMetadata'), getExternalGlobalData);

export const getDealersById = compose(_property('dealersById'), getExternalGlobalData);
