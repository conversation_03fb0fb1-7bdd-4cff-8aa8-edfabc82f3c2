/* eslint-disable import/order */
import React, { Component } from 'react';
import { defaultMemoize } from 'reselect';
import PropTypes from 'prop-types';
import { compose } from 'recompose';
import { connect } from 'react-redux';

// Helpers
import { injectDepartmentReducers } from './helpers/enterpriseCommunication.injectReducer';
import { getPermissibleDepartments } from '@tekion/tekion-widgets/src/organisms/communications/helpers/communications.permissions';
import { openCommunication } from './helpers/openCommunication/enterpriseCommunication.openCommunication';
import { sendActionToWidgetPanel } from '../helpers/communications.chatWidget';

// Constants
import { WIDGET_ACTIONS } from '@tekion/tekion-components/src/emitters/WidgetEventEmitter';
import {
  COMMUNICATION_EVENT_TYPE,
  OPEN_COMMUNICATION,
} from '@tekion/tekion-widgets/src/organisms/communications/constants/communications.general';
import {
  INITIAL_STATE,
  EXTERNAL_CONTAINER_STORE_NAME,
} from '@tekion/tekion-widgets/src/organisms/communications/enterpriseCommunication/constants/enterpriseCommunication.general';
import { COMMUNICATION_STORE_NAME } from '../constants/communications.general';
import ACTION_HANDLERS from './actionHandlers/enterpriseCommunication.actionHandlers';
import { EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import COMMUNICATION_EVENTS from '@tekion/tekion-widgets/src/organisms/communications/constants/communications.events';

// Events
import { CommunicationEvents } from '@tekion/tekion-base/helpers/eventEmitter';

// Components
import EnterpriseCommunication from './EnterpriseCommunication';

// Actions
import { fetchMetadata, fetchCustomer, fetchLeadInfo } from './actions/enterpriseCommunication.metadata';

// External Actions
import {
  setDepartment,
  fetchDealerMetadata,
  getDealerMasterAction,
} from './organisms/externalCommunication/actions/externalCommunication.global';
import {
  setFilter,
  setSortOrder,
  removeReferenceInfoForConversations,
  removeCustomerPreferenceForConversations,
  fetchConversations,
  removeConversation,
  selectConversation,
  saveCustomerPreference,
  reduceUnreadCount,
  saveConversation,
  increaseUnreadCount,
} from './organisms/externalCommunication/actions/externalCommunication.conversation';
import {
  fetchMessagesAndReferencesFromConversation,
  getSurroundingMessagesByMessage,
  fetchConversationMessages,
  sendMessageToConversation,
  retrySendMessageToConversation,
  removeMessageFromConversation,
  addMessageToConversation,
  addStarredUserIdsToMessage,
  updateMessageStatus,
  updateMessages,
} from './organisms/externalCommunication/actions/externalCommunication.messages';
import {
  saveReferenceForConversation,
  clearReferenceForConversation,
} from './organisms/externalCommunication/actions/externalCommunication.references';

// Selectors
import { getCustomerById } from '@tekion/tekion-widgets/src/organisms/communications/enterpriseCommunication/reducers/selectors/enterpriseCommunication.metadata';

// External Selectors
import {
  getSelectedDepartment,
  getMetadataByDealer,
  getDealersById,
  getExternalData,
} from './organisms/externalCommunication/reducers/selectors/externalCommunication.global';
import { getReferenceInfoForSelectedConversationId } from './organisms/externalCommunication/reducers/selectors/externalCommunication.references';
import {
  getMessagesList,
  getMessagesGroupedByReferences,
} from './organisms/externalCommunication/reducers/selectors/externalCommunication.messages';
import {
  getSelectedFilter,
  getSelectedSortOrder,
  getConversationsList,
  getConversationsAdditionalInfoById,
  getSelectedConversation,
  getCustomerPreferenceByConversationId,
  getSelectedTabId,
} from './organisms/externalCommunication/reducers/selectors/externalCommunication.conversation';

// Containers
import withAsyncReducer from '@tekion/tekion-components/src/connectors/withAsyncReducer';
import withActions from '@tekion/tekion-components/src/connectors/withActions';
import withPropertyConsumer from '@tekion/tekion-components/src/organisms/propertyProvider/withPropertyConsumer';
import communicationWithActiveTab from '../containers/communicationWithActiveTab';
import withEnterpriseCommunicationWithEvents from './containers/withEnterpriseCommunicationWithEvents';
import withMediaPreview from '@tekion/tekion-widgets/src/organisms/communications/containers/withMediaPreview';

// Reducers
import communicationMetadataReducer from './reducers';
import globalExternalReducer from './organisms/externalCommunication/reducers/externalCommunication.global';

class EnterpiseCommunicationWrapper extends Component {
  constructor(props) {
    super(props);
    this.injectReducers();
    this.subscribeToCommunicationEvents();
  }

  getPermissibleDepartments = defaultMemoize(getPermissibleDepartments);

  componentDidMount() {
    this.fetchMetadata();
  }

  componentWillUnmount() {
    CommunicationEvents.removeListener(COMMUNICATION_EVENT_TYPE);
    CommunicationEvents.removeListener(OPEN_COMMUNICATION);
  }

  handleOpenCommunication = context => {
    openCommunication(context, this.props);
    sendActionToWidgetPanel(WIDGET_ACTIONS.SELECTED);
  };

  handleEvents = context => {
    const { onAction } = this.props;
    onAction(context);
  };

  injectReducers() {
    this.injectDepartmentReducer();
    this.injectGlobalExternalReducer();
  }

  injectGlobalExternalReducer() {
    const { injectReducer } = this.props;
    injectReducer(`${COMMUNICATION_STORE_NAME}.${EXTERNAL_CONTAINER_STORE_NAME}.global`, globalExternalReducer);
  }

  fetchMetadata() {
    const { onAction, permissions } = this.props;
    const departments = getPermissibleDepartments(permissions);
    onAction({ type: COMMUNICATION_EVENTS.FETCH_METADATA, payload: { departments } });
  }

  subscribeToCommunicationEvents() {
    CommunicationEvents.on(COMMUNICATION_EVENT_TYPE, this.handleEvents);
    CommunicationEvents.on(OPEN_COMMUNICATION, this.handleOpenCommunication);
  }

  injectDepartmentReducer() {
    const { injectReducer, permissions } = this.props;
    const departments = getPermissibleDepartments(permissions);
    injectDepartmentReducers(departments, injectReducer);
  }

  render() {
    const { permissions, hidden } = this.props;
    const departments = this.getPermissibleDepartments(permissions);

    if (hidden) {
      return null;
    }

    return <EnterpriseCommunication {...this.props} departments={departments} />;
  }
}

EnterpiseCommunicationWrapper.propTypes = {
  permissions: PropTypes.array,
  hidden: PropTypes.bool,
  injectReducer: PropTypes.func.isRequired,
  onAction: PropTypes.func.isRequired,
};

EnterpiseCommunicationWrapper.defaultProps = {
  permissions: EMPTY_ARRAY,
  hidden: true,
};

const mapStateToProps = state => ({
  communicationExternalState: getExternalData(state),
  selectedDepartment: getSelectedDepartment(state),
  selectedTabId: getSelectedTabId(state),
  selectedFilter: getSelectedFilter(state),
  selectedSortOrder: getSelectedSortOrder(state),
  conversationList: getConversationsList(state),
  conversationsAdditionalInfoById: getConversationsAdditionalInfoById(state),
  selectedConversation: getSelectedConversation(state),
  dealerMetadata: getMetadataByDealer(state),
  dealersById: getDealersById(state),
  messagesList: getMessagesList(state),
  messagesByReferences: getMessagesGroupedByReferences(state),
  referenceInfo: getReferenceInfoForSelectedConversationId(state),
  customerPreferenceByConversationId: getCustomerPreferenceByConversationId(state),
  customerById: getCustomerById(state),
});

const mapDispatchToProps = {
  fetchMetadata,
  // External
  setDepartment,
  setFilter,
  setSortOrder,
  removeReferenceInfoForConversations,
  removeCustomerPreferenceForConversations,
  fetchConversations,
  removeConversation,
  selectConversation,
  fetchDealerMetadata,
  getDealerMasterAction,
  saveCustomerPreference,
  fetchMessagesAndReferencesFromConversation,
  saveReferenceForConversation,
  clearReferenceForConversation,
  getSurroundingMessagesByMessage,
  fetchConversationMessages,
  sendMessageToConversation,
  retrySendMessageToConversation,
  removeMessageFromConversation,
  fetchCustomer,
  fetchLeadInfo,
  reduceUnreadCount,
  saveConversation,
  addMessageToConversation,
  increaseUnreadCount,
  addStarredUserIdsToMessage,
  updateMessageStatus,
  updateMessages,
};

export default compose(
  withAsyncReducer({
    storeKey: `${COMMUNICATION_STORE_NAME}.metadata`,
    reducer: communicationMetadataReducer,
  }),
  connect(mapStateToProps, mapDispatchToProps),
  withPropertyConsumer,
  communicationWithActiveTab,
  withActions(INITIAL_STATE, ACTION_HANDLERS),
  withEnterpriseCommunicationWithEvents,
  withMediaPreview
)(EnterpiseCommunicationWrapper);
