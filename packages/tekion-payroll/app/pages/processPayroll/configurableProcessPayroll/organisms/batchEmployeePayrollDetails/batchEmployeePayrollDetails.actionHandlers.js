/* eslint-disable import/order */
// Lodash
import _size from 'lodash/size';

// Utils
import getDataFromResponse from 'tbase/utils/getDataFromResponse';
import toastAPIError from 'tbusiness/utils/toastAPIError';
import exposeFunctionsIfTestEnvironment from 'tbase/utils/test/exposeFunctionsIfTestEnvironment';
import { triggerSubmit } from 'tcomponents/pages/formPage/utils/formAction';

// Components
import { toaster, TOASTER_TYPE } from 'tcomponents/organisms/NotificationWrapper';

// Constants
import { EMPTY_OBJECT } from 'tbase/app.constants';
import WARNING_MODAL_ACTION_TYPE from 'constants/payroll.warningModalActionType';
import { ITEM_NAVIGATION_CONTROLLER_ACTION_TYPES } from '../../../molecules/batchEmployeePayrollDetailsHeader';
import FORM_ACTION_TYPES from 'tcomponents/organisms/FormBuilder/constants/actionTypes';
import { BATCH_EMPLOYEE_PAYROLL_DETAILS_FORM_CONTEXT_ID } from './constants/batchEmployeePayrollDetails.general';

// helpers
import { pushFederatedURIToHistory } from 'helpers/federationHelpers';
import redirectToBatchDetails from '../../../helpers/processPayroll.redirectToBatchDetails';
import getBatchEmployeePayrollDetailsRoute from '../../helpers/configurableProcessPayroll.batchEmployeePayrollDetailsRoute';
import { isEmployeeAndBatchEditable } from './helpers/batchEmployeePayrolllDetails.general';
import { shouldShowNegativeBalanceHoursWarning } from './helpers/batchEmployeePayrolllDetails.earnings';
import { isAnyPostTaxDeductionChangedToNonZero } from './helpers/batchEmployeePayrolllDetails.deductions';
import { saveEmployeePayrollDetailsAndInformations } from './hooks/useFetchBatchEmployeePayrollDetails';

// Services
import {
  asyncUpdateEmployeePayrollDetails,
  updateEmployeePayrollDetails,
} from './services/batchEmployeePayrollDetails.general';
import { makeEmployeePayrollDetailsDTO, FIELD_ID } from '../employeePayrollDetailsForm';

// Readers
import EmployeePayrollDetailsSummaryReader from 'readers/payrollV2/EmployeePayrollDetailsSummary';

const handleUpdateEmployeePayrollDetailsFailure = error => {
  toastAPIError(error, __('Failed to save Employee Payroll details.'));
};

const handleUpdateEmployeePayrollDetailsSuccess =
  ({ navigate, batchId, additional, hasBack, setEmployeePayrollDetails, setInformationItems }) =>
  employeePayrollDetails => {
    if (hasBack) {
      toaster(TOASTER_TYPE.SUCCESS, __('Employee payroll details save initiated successfully'));
      redirectToBatchDetails(navigate, batchId);
      return;
    }

    const { navigateToOnSuccess } = additional || EMPTY_OBJECT;

    toaster(TOASTER_TYPE.SUCCESS, __('Employee payroll details saved successfully'));
    if (navigateToOnSuccess) {
      pushFederatedURIToHistory(navigate, navigateToOnSuccess);
      return;
    }
    saveEmployeePayrollDetailsAndInformations(setEmployeePayrollDetails, setInformationItems)(employeePayrollDetails);
  };

const setAsLoaded = setState => () => {
  setState({ loading: false });
};

const getUpdatedEmployeePayrollDetailsService = (additional = EMPTY_OBJECT) => {
  const { isAsyncCall } = additional;
  // This we are doing for one sprint in next sprint we will remove sync call and used async call everywhere thats why we simply putting if condition here
  return isAsyncCall ? asyncUpdateEmployeePayrollDetails : updateEmployeePayrollDetails;
};

const saveEmployeePayrollDetails = ({ getState, setState }) => {
  const {
    navigate,
    batchId,
    employeePayrollDetailsId,
    deductionsById,
    employeePayrollDetails,
    values,
    additional,
    hasBack,
    setEmployeePayrollDetails,
    setInformationItems,
  } = getState();

  const employeePayrollDetailsDTO = makeEmployeePayrollDetailsDTO({
    formValues: values,
    deductionsById,
    employeePayrollDetails,
  });
  setState({
    loading: true,
    showPopup: false,
  });
  const updateEmployeePayrollDetailsService = getUpdatedEmployeePayrollDetailsService(additional);
  return updateEmployeePayrollDetailsService({
    batchId,
    employeePayrollDetailsId,
    employeePayrollDetailsDTO,
  })
    .then(getDataFromResponse)
    .then(
      handleUpdateEmployeePayrollDetailsSuccess({
        navigate,
        batchId,
        additional,
        hasBack,
        setEmployeePayrollDetails,
        setInformationItems,
      })
    )
    .catch(handleUpdateEmployeePayrollDetailsFailure)
    .finally(setAsLoaded(setState));
};

const getShouldShowAddDeductionConfirmation = ({ employeePayrollDetails, values = EMPTY_OBJECT, deductionsById }) => {
  const employeeDeductions = EmployeePayrollDetailsSummaryReader.deductionDetails(employeePayrollDetails);
  const employeeDeductionsFormValue = values[FIELD_ID.DEDUCTIONS];

  return (
    _size(employeeDeductions) !== _size(employeeDeductionsFormValue) ||
    isAnyPostTaxDeductionChangedToNonZero({ employeeDeductions, employeeDeductionsFormValue, deductionsById })
  );
};

const saveEmployeePayrollDetailsBasedOnWarnings = ({
  shouldShowAddDeductionConfirmation,
  isBalanceHoursNegative,
  getState,
  setState,
}) =>
  !(shouldShowAddDeductionConfirmation || isBalanceHoursNegative) && saveEmployeePayrollDetails({ getState, setState });

export const __tests__ = exposeFunctionsIfTestEnvironment({ saveEmployeePayrollDetailsBasedOnWarnings });

const handleEmployeePayrollDetailsSubmit = ({ params = EMPTY_OBJECT, getState, setState }) => {
  const { additional = EMPTY_OBJECT } = params;
  const { hasBack } = additional;
  const { employeePayrollDetails, values = EMPTY_OBJECT, deductionsById } = getState();
  const earningDetails = values[FIELD_ID.EMPLOYEE_EARNING];
  const shouldShowAddDeductionConfirmation = getShouldShowAddDeductionConfirmation({
    employeePayrollDetails,
    values,
    deductionsById,
  });
  const isBalanceHoursNegative = shouldShowNegativeBalanceHoursWarning(earningDetails);

  setState(
    {
      additional,
      hasBack,
      shouldShowAddDeductionConfirmation,
      isBalanceHoursNegative,
      earningDetails,
      showPopup: true,
    },
    () =>
      saveEmployeePayrollDetailsBasedOnWarnings({
        shouldShowAddDeductionConfirmation,
        isBalanceHoursNegative,
        getState,
        setState,
      })
  );
};

const handleItemSelectionChange = ({ getState, params = EMPTY_OBJECT }) => {
  const { employeePayrollDetails, navigate, batchId } = getState();
  const { selectedItem: employeePayrollDetailsId } = params;
  const nextEmployeeDetailsRoute = getBatchEmployeePayrollDetailsRoute(batchId, employeePayrollDetailsId);
  const employeeAndBatchEditable = isEmployeeAndBatchEditable(employeePayrollDetails);
  if (!employeeAndBatchEditable) {
    pushFederatedURIToHistory(navigate, nextEmployeeDetailsRoute);
    return;
  }

  triggerSubmit(BATCH_EMPLOYEE_PAYROLL_DETAILS_FORM_CONTEXT_ID, {
    navigateToOnSuccess: nextEmployeeDetailsRoute,
    isAsyncCall: true,
  });
};

const handleClosePopup = ({ setState, params = EMPTY_OBJECT }) => {
  const { lastSeenTabIndex, prevTabConfigs } = params;
  setState({ showPopup: false, lastSeenTabIndex, prevTabConfigs });
};

const ACTION_HANDLERS = {
  [WARNING_MODAL_ACTION_TYPE.MODAL_SUBMIT]: saveEmployeePayrollDetails,
  [WARNING_MODAL_ACTION_TYPE.CANCEL_WARNING_MODAL]: handleClosePopup,
  [ITEM_NAVIGATION_CONTROLLER_ACTION_TYPES.ITEM_SELECTION_CHANGE]: handleItemSelectionChange,
  [FORM_ACTION_TYPES.ON_FORM_SUBMIT]: handleEmployeePayrollDetailsSubmit,
};

export default ACTION_HANDLERS;
