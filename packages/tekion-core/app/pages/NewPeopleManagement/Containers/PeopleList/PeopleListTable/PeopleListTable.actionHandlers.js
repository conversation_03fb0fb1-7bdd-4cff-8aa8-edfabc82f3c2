import { compose } from 'recompose';
import _get from 'lodash/get';
import _map from 'lodash/map';
import _find from 'lodash/find';
import _uniq from 'lodash/uniq';
import _isEmpty from 'lodash/isEmpty';
import _head from 'lodash/head';
import _noop from 'lodash/noop';
import _delay from 'lodash/delay';
import _has from 'lodash/has';
import _castArray from 'lodash/castArray';
import _omit from 'lodash/omit';
import _size from 'lodash/size';
import _keyBy from 'lodash/keyBy';

import { CORE } from '@tekion/tekion-base/constants/appServices';
import { tget } from '@tekion/tekion-base/utils/general';
import READER from '@tekion/tekion-base/readers/Media';
import { ES_REFETCH_DELAY } from '@tekion/tekion-base/constants/general';
import { getFilters } from '@tekion/tekion-base/helpers/tableGenerator.helpers';
import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import getDataFromResponse from '@tekion/tekion-base/utils/getDataFromResponse';
import { getValueFromLocalStorage, removeValueFromLocalStorage } from '@tekion/tekion-base/utils/localStorage';
import { uploadFiles, getSignedURLs } from '@tekion/tekion-business/src/services/mediaV3';
import { getFirstSignedURLFromMediaList } from '@tekion/tekion-business/src/helpers/mediaV3.helpers';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import { tableGeneratorActionHandler } from '@tekion/tekion-components/src/organisms/TableGenerator';
import { excelExporterForTableGenerator } from '@tekion/tekion-components/src/organisms/excelGenerator/excelGenerator.utils';
import TABLE_ACTION_TYPES from '@tekion/tekion-components/src/organisms/TableManager/constants/actionTypes';
import { DEFAULT_FILTER_GROUP } from '@tekion/tekion-components/src/organisms/filterSection/constants/filterSection.filterGroups';
import { downloadURI } from '@tekion/tekion-components/src/utils/downloadFile';
import {
  handleFilterChange,
  handlePageNumberChange,
  handleSearchChange,
} from '@tekion/tekion-components/src/organisms/TableGenerator/tableGenerator.utils';
import toastErrorMessage from '@tekion/tekion-widgets/src/utils/toastErrorMessage';
import { LOCAL_STORAGE_IDS } from '@tekion/tekion-widgets/src/organisms/notificationCenter/utils/routes/core/core';
import PUSHER_EVENT_TYPE from '@tekion/tekion-business/src/constants/payroll/payroll.pusherEventType';
import makeNotificationConfig from '@tekion/tekion-widgets/src/appServices/payroll/helpers/makeNotificationConfig';
import showNotificationToaster from '@tekion/tekion-widgets/src/molecules/Notification';
import { fetchEmployeeOnboardingDetails } from '@tekion/tekion-business/src/appServices/payroll/services/payrollV2/employee';
import OnboardingDetailsReader from '@tekion/tekion-business/src/appServices/payroll/readers/payrollV2/OnboardingDetails';
import EmployeeOnboardingDetailsReader from '@tekion/tekion-business/src/appServices/payroll/readers/payrollV2/EmployeeOnboardingDetails';

import { handleTableItemsFetch } from 'helpers/peopleManagementAndUserSetup';
import PAYROLL_SOURCE from 'constants/payrollSource';
import { PEOPLE_CREATE_PATH, PEOPLE_EXPORT_PATH } from '../../../Constants/route.constants';
import {
  EXPORT_ACTIONS,
  getColumns,
  BANK_TEMPLATE_COLUMNS,
  BANK_TEMPLATE_COLS_VS_EXPORT_RENDERER,
  COLUMN_KEY_VS_EXPORT_RENDERER,
  activeFilter,
  IMPORT_ACTIONS,
  PRENOTE_FILE_ERROR_MESSAGE,
  GROUP_BY_NONE,
  getEuropeExportRenderers,
  TEMPLATE_MEDIA_ENUMS,
} from './PeopleListTable.constants';

import {
  handleEmployeeStatusChange,
  fetchEmployeeList,
  handleGenerateReport,
  getFileThroughMediaId,
  getFileThroughMediaIdEmployeeData,
  getPrenoteFileForDownload,
  downloadExcelMedia,
  getExcelTemplateMedia,
  fetchEmployeeW4TemplateDetails,
  uploadW4TaxDetailsFileActionHandler,
  syncWithCheck,
  getFileThroughMediaIdBankDetails,
  downloadBankDetailsExcel,
  downloadEmployeeOnboardingErrorsList,
} from '../../../PeopleManagement.action';

import { ACTION_TYPES } from './PeopleListTable.actionTypes';
import {
  getPayLoad,
  getResolvedEmployeeList,
  getResolvedBankTemplateData,
  getFileList,
  areAllFilesXLSX,
  missingEmployeeFilter as filterForMissingEmployee,
  getGroupByPayload,
  getNotificationMessage,
  isNotificationForSuccess,
  getNameForExcelDownload,
} from './PeopleListTable.helpers';
import PEOPLE_LIST_HEADER_ACTION_TYPES from '../PeopleListHeaderActions/PeopleListHeader.actionTypes';
import { isValidDepartmentTypeForTeams } from '../../../../teamsSetupNew/teamsSetup.helpers';
import {
  EMPLOYEE_LIST_FILE_AND_TITLE,
  BANK_DETAILS,
  CRM_FIELDS,
} from '../exports/EmployeeList/constants/employeeList.general';
import { shouldShowCRMFields } from '../../../utils/peopleManagement.utils';
import {
  downloadEmployees,
  resyncEmployeeOnboardingDetails,
  syncEmployeePaymentType,
} from '../../../PeopleManagement.api';
import { giveBulkArcAccessToEmployees } from './peopleListTable.actions';
import { isRRGProgram } from '../../PeopleManagementForm/utils/PeopleManagement.util';
import FEATURE_FLAGS from 'factories/featureFlags/constants/featureFlags';

const handleEmployeeStatus = (listItem = EMPTY_OBJECT, { dispatch, getState }) => {
  compose(dispatch, handleEmployeeStatusChange)(listItem, getState);
};

// const handleEmployeesDeactive = ({ dispatch, setState, getState }) => {
//   compose(
//     dispatch,
//     handleBulkEmployeesDeactive
//   )(setState, getState);
// };

const handleBulkArcAccess = async ({ getState }) => {
  const { selection } = getState();
  if (_size(selection) > 10) {
    toaster(TOASTER_TYPE.INFO, __('Number of selected employees is greater than 10'));
    return;
  }
  const response = await giveBulkArcAccessToEmployees(selection);
  if (response) toaster(TOASTER_TYPE.SUCCESS, __('ARC access given to employees'));
  else toaster(TOASTER_TYPE.ERROR, __('Bulk update failed'));
};

const handleEmployeesAddToTeam = ({ setState, getState }) => {
  const { selection, tableData } = getState();
  if (_isEmpty(selection)) {
    toaster(TOASTER_TYPE.ERROR, __('Please select atleast one employee'));
    return;
  }
  const selectedEmployees = _map(selection, id => _find(tableData, emp => emp.id === id));
  const departmentTypes = _uniq(_map(selectedEmployees, 'dealershipDepartmentName'));
  if (departmentTypes.length > 1) {
    toaster(TOASTER_TYPE.ERROR, __('Employees from multiple departments are selected'));
    return;
  }
  if (!isValidDepartmentTypeForTeams(_head(departmentTypes))) {
    toaster(TOASTER_TYPE.ERROR, __('Employees from accounting department cannot be added to team'));
    return;
  }
  setState({
    showAddToTeamModal: true,
    addToTeamModalData: { selectedMemberIds: selection, departmentType: _head(departmentTypes) },
  });
};

const handleSaveOnboardingDetails = setState => employeeOnboardingDetails => {
  const onboardingDetailsList = EmployeeOnboardingDetailsReader.onboardingDetailsList(employeeOnboardingDetails);
  const onboardingDetailsListById = _keyBy(onboardingDetailsList, OnboardingDetailsReader.id);
  setState({ onboardingDetailsListById });
};

const handleResyncEmployeeOnboardingDetails = setState => {
  setState({ isFetchingOnboardingDetails: true });
  resyncEmployeeOnboardingDetails()
    .then(getDataFromResponse)
    .then(handleSaveOnboardingDetails(setState))
    .catch(err => {
      toastErrorMessage(err, __('Error while resyncing onboarding details'));
    })
    .finally(() => setState({ isFetchingOnboardingDetails: false }));
};

const handleEmployeePaymentTypeSync = () => {
  syncEmployeePaymentType()
    .then(() => toaster(TOASTER_TYPE.SUCCESS, __('Employee payment type sync initiated successfully')))
    .catch(err => {
      toastErrorMessage(err, __('Error while resyncing employee payment type'));
    });
};

const handleFetchEmployeeOnboardingDetails = (setState, getState) => {
  const { payrollApplicable, payrollSource } = getState();

  if (!payrollApplicable || payrollSource !== PAYROLL_SOURCE.CHECKHQ) return;
  setState({ isFetchingOnboardingDetails: true });

  fetchEmployeeOnboardingDetails()
    .then(getDataFromResponse)
    .then(handleSaveOnboardingDetails(setState))
    .catch(err => {
      toastErrorMessage(err, __('Failed to fetch onboarding details'));
    })
    .finally(() => setState({ isFetchingOnboardingDetails: false }));
};
// const flipOneOffCheck = (_, { setState, getState }) => {
//   const { showOneOffCheckModal = false } = getState();
//   setState({ showOneOffCheckModal: !showOneOffCheckModal });
// };

const ROW_ACTION_TO_HANDLER = {
  [ACTION_TYPES.EMPLOYEE_ACTIVATE]: handleEmployeeStatus,
  [ACTION_TYPES.EMPLOYEE_INACTIVATE]: handleEmployeeStatus,
  // [ACTION_TYPES.FLIP_ONE_OFF_CHECK]: flipOneOffCheck,
};

const displayPasscodeModalAction = ({ getState, setState }) => {
  const { tableData } = getState();
  if (_isEmpty(tableData)) {
    toaster(TOASTER_TYPE.WARN, __("Couldn't generate empty list."));
  } else setState({ showPasscodeModal: true });
};

const handleSetDisplayCriticalInfo = (action = EMPTY_OBJECT, { getState, setState }) => {
  const value = _get(action, 'payload');
  setState({ showPasscodeModal: false }, () => handleGenerateReport(value, getState));
};

const handleShowDisplayPasscodeModal = ({ action, getState, setState, exportAction }) => {
  const viewSensitiveInfo = tget(action, 'payload', false);
  setState({ exportAction }, () => {
    if (viewSensitiveInfo) displayPasscodeModalAction({ getState, setState });
    else {
      handleSetDisplayCriticalInfo({ payload: false }, { getState, setState });
    }
  });
};

export const commonMediaUploadHandler = async (action = EMPTY_OBJECT) => {
  const file = action.payload;
  const { type } = action;
  const fileList = getFileList(file);
  const allFiles = areAllFilesXLSX(fileList);
  if (allFiles) {
    const mediaData = await uploadFiles(fileList);
    const mediaId = await getFileThroughMediaId(READER.mediaId(_head(mediaData)), type);
    if (mediaId) downloadExcelMedia(mediaId, type);
  }
};

export const mediaUploadHandlerEmployeeData = async (action = EMPTY_OBJECT) => {
  const file = action.payload;
  const { type } = action;
  const fileList = getFileList(file);
  const allFiles = areAllFilesXLSX(fileList);
  if (allFiles) {
    const mediaData = await uploadFiles(fileList);
    await getFileThroughMediaIdEmployeeData(READER.mediaId(_head(mediaData)), type);
  }
};

const mediaUploadHandlerBankDetails = async (action = EMPTY_OBJECT) => {
  const file = action.payload;
  const { type } = action;
  const fileList = getFileList(file);
  const allFiles = areAllFilesXLSX(fileList);
  if (allFiles) {
    const mediaData = await uploadFiles(fileList);
    await getFileThroughMediaIdBankDetails(READER.mediaId(_head(mediaData)), type);
  }
};

const handleUploadBankDetails = (action = EMPTY_OBJECT, { getState }) => {
  const { featureFlags } = getState();
  const isCheckHqEnabled = featureFlags.getFlag(FEATURE_FLAGS.CHECK_HQ_ENABLED);
  const mediaUploadHandler = isCheckHqEnabled ? mediaUploadHandlerBankDetails : commonMediaUploadHandler;
  mediaUploadHandler(action);
};

const handleImportActionDownlod = ({ getState }) => {
  excelExporterForTableGenerator({
    fileName: BANK_DETAILS,
    payload: EMPTY_OBJECT,
    columns: BANK_TEMPLATE_COLUMNS,
    getState,
    fetchApi: downloadEmployees,
    sheetName: BANK_DETAILS,
    columnKeyVsExportRenderer: BANK_TEMPLATE_COLS_VS_EXPORT_RENDERER,
    listSelector: data => getResolvedBankTemplateData(_get(data, 'hits', EMPTY_ARRAY)),
  });
};

const handlePrenoteFileDownload = async () => {
  const { data: media, success } = await getPrenoteFileForDownload();
  if (!success) return;
  try {
    const mediaDetails = await getSignedURLs(_castArray(media?.mediaId), { attachment: true });
    const url = getFirstSignedURLFromMediaList(mediaDetails);
    downloadURI(url, __('Prenote Details'));
    toaster(TOASTER_TYPE.SUCCESS, __('Prenote File generated successfully'));
  } catch (e) {
    toaster(TOASTER_TYPE.ERROR, PRENOTE_FILE_ERROR_MESSAGE);
  }
};

const handleTableRefresh =
  ({ payrollApplicable, enableConsentConfiguration, itemType }) =>
  (action, { dispatch, setState, getState }) => {
    _delay(
      () => {
        handleFetchEmployeeOnboardingDetails(setState, getState);
        const { payrollSource } = getState();
        handleTableItemsFetch({
          payload: getPayLoad(getState()),
          itemType,
          dispatch,
          getState,
          fetchApi: fetchEmployeeList({ payrollApplicable, enableConsentConfiguration, payrollSource }),
        });
      },
      _has(action, 'addDelay') ? ES_REFETCH_DELAY : 0
    );
  };

const refreshPeopleList =
  ({ payrollApplicable, enableConsentConfiguration, itemType, action, dispatch, setState, getState }) =>
  () =>
    handleTableRefresh({ payrollApplicable, enableConsentConfiguration, itemType })(action, {
      dispatch,
      setState,
      getState,
    });

const handlePayrollBackgroundTaskEvent =
  ({ payrollApplicable, enableConsentConfiguration, itemType }) =>
  (action, { dispatch, setState, getState }) => {
    const { event } = action.payload;
    const notificationMessage = getNotificationMessage(event);
    const isNotificationSuccess = isNotificationForSuccess(event);

    if (isNotificationSuccess) {
      const notificationConfig = makeNotificationConfig({
        notificationMessage,
        onNotificationToasterClose: refreshPeopleList({
          payrollApplicable,
          enableConsentConfiguration,
          itemType,
          action,
          dispatch,
          setState,
          getState,
        }),
      });
      showNotificationToaster(notificationConfig);
    } else {
      toaster(TOASTER_TYPE.ERROR, notificationMessage);
    }
  };

const getTableActionHandlers = (itemType, editPeople, payload, { payrollApplicable, enableConsentConfiguration }) => ({
  ...tableGeneratorActionHandler(itemType, fetchEmployeeList, payload, undefined),
  [TABLE_ACTION_TYPES.TABLE_ITEMS_FETCH]: _noop,
  [TABLE_ACTION_TYPES.TABLE_ITEMS_FETCH_WITH_FILTER_AND_SORT_PREFERENCES]: (
    { payload: params },
    { dispatch, getState, setState }
  ) => {
    const { selectedFilters: prevFilter, pivotById, payrollSource } = getState();
    if (!_isEmpty(pivotById)) return;
    const selectedGroupFilters = tget(params, 'value.filterDetails.selectedGroupFilters', EMPTY_ARRAY);
    let missingEmployeeFilter = EMPTY_OBJECT;
    try {
      const missingEmployeeIds = JSON.parse(getValueFromLocalStorage(LOCAL_STORAGE_IDS.MISSING_EMPLOYEE_IDS));
      missingEmployeeFilter = filterForMissingEmployee(missingEmployeeIds);
    } catch {
      missingEmployeeFilter = EMPTY_OBJECT;
    }
    handleFetchEmployeeOnboardingDetails(setState, getState);
    setState(
      {
        selectedFilterGroup: tget(params, 'value.filterDetails.selectedFilterGroupId', DEFAULT_FILTER_GROUP),
        listFetchInitialized: true,
      },
      () => {
        handleTableItemsFetch({
          payload,
          itemType,
          dispatch,
          getState,
          fetchApi: fetchEmployeeList({ payrollApplicable, enableConsentConfiguration, payrollSource }),
          selectedFilters: [
            ...getFilters(EMPTY_ARRAY, [...(!prevFilter ? activeFilter : prevFilter), ...selectedGroupFilters]),
            missingEmployeeFilter,
          ],
        });
      }
    );

    removeValueFromLocalStorage(LOCAL_STORAGE_IDS.MISSING_EMPLOYEE_IDS);
  },
  [TABLE_ACTION_TYPES.TABLE_ITEMS_SET_FILTER]: (
    { payload: { value, selectedFilterGroup } = EMPTY_OBJECT },
    { dispatch, getState, setState }
  ) => {
    const {
      sortDetails = EMPTY_OBJECT,
      listFetchInitialized = false,
      groupBy = EMPTY_ARRAY,
      payrollSource,
    } = getState();
    if (!listFetchInitialized) return;
    handleFetchEmployeeOnboardingDetails(setState, getState);
    handleFilterChange({
      itemType,
      fetchApi: fetchEmployeeList({ payrollApplicable, enableConsentConfiguration, payrollSource }),
      payload: getPayLoad(getState()),
      dispatch,
      getState,
      selectedFilters: value,
      sortDetails,
      selectedFilterGroup,
      setState,
      groupBy,
    });
  },
  [TABLE_ACTION_TYPES.TABLE_SEARCH]: ({ payload: { value } = EMPTY_OBJECT }, { dispatch, getState, setState }) => {
    const { sortDetails = EMPTY_OBJECT, groupBy = EMPTY_ARRAY, payrollSource } = getState();
    handleFetchEmployeeOnboardingDetails(setState, getState);

    handleSearchChange({
      itemType,
      fetchApi: fetchEmployeeList({ payrollApplicable, enableConsentConfiguration, payrollSource }),
      payload: getPayLoad(getState()),
      dispatch,
      getState,
      searchQuery: value,
      sortDetails,
      groupBy,
    });
  },
  [TABLE_ACTION_TYPES.TABLE_ITEMS_PAGE_UPDATE]: (
    { payload: { value } = EMPTY_OBJECT },
    { dispatch, setState, getState }
  ) => {
    const { payrollSource } = getState();
    handleFetchEmployeeOnboardingDetails(setState, getState);

    handlePageNumberChange({
      itemType,
      fetchApi: fetchEmployeeList({ payrollApplicable, enableConsentConfiguration, payrollSource }),
      payload: getPayLoad(getState()),
      dispatch,
      getState,
      pageNumberValue: value,
      getTableItemsFromState: state => _get(state, [CORE, itemType]),
    });
  },
  [TABLE_ACTION_TYPES.TABLE_ITEM_CLICK]: (action = EMPTY_OBJECT) => {
    const id = _get(action, 'payload.value.original.id');
    if (!id) return false;
    return editPeople(id);
  },
  [TABLE_ACTION_TYPES.TABLE_ITEMS_SORT]: (
    {
      payload: {
        value: { sortTypeMap },
      },
    },
    { setState, getState, dispatch }
  ) => {
    handleFetchEmployeeOnboardingDetails(setState, getState);
    setState({ sortDetails: sortTypeMap }, () => {
      const { payrollSource } = getState();
      handleTableItemsFetch({
        payload,
        itemType,
        dispatch,
        getState,
        fetchApi: fetchEmployeeList({ payrollApplicable, enableConsentConfiguration, payrollSource }),
      });
    });
  },
  [ACTION_TYPES.CREATE_EMPLOYEE]: (action, { getState }) => {
    const { navigate } = getState();
    navigate(PEOPLE_CREATE_PATH);
  },
  [ACTION_TYPES.HANDLE_ROW_ACTION]: (action = EMPTY_OBJECT, { dispatch, setState, getState }) => {
    const { actionType, listItem } = action.payload;
    const handler = ROW_ACTION_TO_HANDLER[actionType];
    handler(listItem, {
      dispatch,
      setState,
      getState,
    });
  },
  [EXPORT_ACTIONS.EXCEL]: (action = EMPTY_OBJECT, { getState, setState }) => {
    const { isNewTeamsSetupEnabled, columns, getFormattedDateAndTime } = getState();
    if (payrollApplicable) {
      handleShowDisplayPasscodeModal({ action, setState, getState, exportAction: EXPORT_ACTIONS.EXCEL });
    } else {
      let columnExportRenderers = shouldShowCRMFields()
        ? COLUMN_KEY_VS_EXPORT_RENDERER
        : _omit(COLUMN_KEY_VS_EXPORT_RENDERER, CRM_FIELDS);
      const isRRG = isRRGProgram();
      columnExportRenderers = isRRG
        ? { ...columnExportRenderers, ...getEuropeExportRenderers(getFormattedDateAndTime) }
        : columnExportRenderers;
      excelExporterForTableGenerator({
        fileName: EMPLOYEE_LIST_FILE_AND_TITLE,
        payload: EMPTY_OBJECT,
        columns: isRRG ? columns : getColumns(undefined, isNewTeamsSetupEnabled, payrollApplicable),
        getState,
        fetchApi: downloadEmployees,
        sheetName: EMPLOYEE_LIST_FILE_AND_TITLE,
        columnKeyVsExportRenderer: columnExportRenderers,
        listSelector: data => getResolvedEmployeeList(_get(data, 'hits', EMPTY_ARRAY), { ...getState() }),
      });
    }
  },
  [EXPORT_ACTIONS.PDF]: (action = EMPTY_OBJECT, { setState, getState }) => {
    handleShowDisplayPasscodeModal({ action, setState, getState, exportAction: EXPORT_ACTIONS.PDF });
  },
  [EXPORT_ACTIONS.PRENOTE_FILE_DOWNLOAD]: () => {
    toaster(TOASTER_TYPE.INFO, __('File is generating'));
    handlePrenoteFileDownload();
  },
  [EXPORT_ACTIONS.BANK_TEMPLATE]: (_, { getState }) => {
    const { featureFlags } = getState();
    const isCheckHqEnabled = featureFlags.getFlag(FEATURE_FLAGS.CHECK_HQ_ENABLED);
    isCheckHqEnabled ? downloadBankDetailsExcel() : handleImportActionDownlod({ getState });
  },
  [EXPORT_ACTIONS.EMPLOYEE_ONBOARDING_ERRORS_LIST]: downloadEmployeeOnboardingErrorsList,
  [EXPORT_ACTIONS.EMPLOYEE_INFO_TEMPLATE]: async (_, { getState }) => {
    const { featureFlags } = getState();
    const isCheckHqEnabled = featureFlags.getFlag(FEATURE_FLAGS.CHECK_HQ_ENABLED);
    const excelName = getNameForExcelDownload(isCheckHqEnabled, payrollApplicable);
    const mediaDetails = await getExcelTemplateMedia(excelName);
    if (!_isEmpty(mediaDetails)) await downloadExcelMedia(mediaDetails, IMPORT_ACTIONS.UPLOAD_EMPLOYEE_DETAILS, false);
  },
  [EXPORT_ACTIONS.DOWNLOAD_W4_TAX_DETAILS_TEMPLATE]: async () => {
    const requestPayload = {
      reportFileName: __('Employee-W4-Details-Template'),
    };
    fetchEmployeeW4TemplateDetails(requestPayload);
  },
  [IMPORT_ACTIONS.UPLOAD_BANK_DETAILS]: handleUploadBankDetails,
  [IMPORT_ACTIONS.UPLOAD_EMPLOYEE_DETAILS]: (action = EMPTY_OBJECT, { getState }) => {
    const { featureFlags } = getState();
    const isCheckHqEnabled = featureFlags.getFlag(FEATURE_FLAGS.CHECK_HQ_ENABLED);
    isCheckHqEnabled ? mediaUploadHandlerEmployeeData(action) : commonMediaUploadHandler(action);
  },
  [IMPORT_ACTIONS.UPLOAD_W4_TAX_DETAILS_FILE]: async (action = EMPTY_OBJECT) => {
    const file = action.payload;
    const fileList = getFileList(file);
    const allFiles = areAllFilesXLSX(fileList);
    if (allFiles) {
      const mediaData = await uploadFiles(fileList);
      const mediaId = READER.mediaId(_head(mediaData));
      if (mediaId) {
        uploadW4TaxDetailsFileActionHandler(mediaId);
      }
    }
  },
  [TABLE_ACTION_TYPES.TABLE_ITEMS_REFRESH]: handleTableRefresh({
    payrollApplicable,
    enableConsentConfiguration,
    itemType,
  }),
  [TABLE_ACTION_TYPES.TABLE_ITEM_SELECT]: ({ payload: { value } }, { getState }) => {
    const { handleTableRowSelect } = getState();
    handleTableRowSelect(value);
  },
  [PEOPLE_LIST_HEADER_ACTION_TYPES.RIGHT_ACTION_SELECTED]: async (action, { dispatch, setState, getState }) => {
    const rightHeaderActionType = _get(action, 'payload.type');
    switch (rightHeaderActionType) {
      // case PEOPLE_LIST_HEADER_ACTION_TYPES.DEACTIVATE_USERS:
      //   handleEmployeesDeactive({ dispatch, getState, setState });
      //   break;
      case PEOPLE_LIST_HEADER_ACTION_TYPES.RESYNC_ONBOARDING_STATUS:
        handleResyncEmployeeOnboardingDetails(setState);
        break;
      case PEOPLE_LIST_HEADER_ACTION_TYPES.EMPLOYEE_PAYMENT_TYPE_SYNC:
        handleEmployeePaymentTypeSync();
        break;
      case PEOPLE_LIST_HEADER_ACTION_TYPES.ADD_TO_TEAM:
        handleEmployeesAddToTeam({ dispatch, getState, setState });
        break;
      case PEOPLE_LIST_HEADER_ACTION_TYPES.BULK_ARC_ACCESS:
        handleBulkArcAccess({ dispatch, getState, setState });
        break;
      default:
    }
  },
  [ACTION_TYPES.CLOSE_ADD_TO_TEAM_MODAL]: (action, { setState }) => {
    setState({ showAddToTeamModal: false });
  },
  [ACTION_TYPES.SET_DISPLAY_CRITICAL_INFO_VALUE]: handleSetDisplayCriticalInfo,
  [ACTION_TYPES.OPEN_PDF_CONFIGURATOR]: (action, { getState }) => {
    const { navigate } = getState();
    navigate(PEOPLE_EXPORT_PATH);
  },
  [ACTION_TYPES.SYNC_WITH_CHECK_HQ]: syncWithCheck,
  [PEOPLE_LIST_HEADER_ACTION_TYPES.GROUP_BY]: (action, { dispatch, getState, setState }) => {
    const { payrollSource } = getState();
    const pivotById = _get(action, 'payload.actionId', '');
    if (!_isEmpty(pivotById)) {
      if (pivotById === GROUP_BY_NONE.id) {
        setState({ pivotById: '', groupBy: EMPTY_ARRAY });
        return;
      }
      const groupBy = getGroupByPayload(pivotById);
      setState({ pivotById, groupBy, listFetchInitialized: true }, () =>
        handleTableItemsFetch({
          payload: getPayLoad(getState()),
          itemType,
          dispatch,
          getState,
          fetchApi: fetchEmployeeList({ payrollApplicable, enableConsentConfiguration, payrollSource }),
        })
      );
    }
  },
  [PUSHER_EVENT_TYPE.PAYROLL_BACKGROUND_TASK]: handlePayrollBackgroundTaskEvent({
    payrollApplicable,
    enableConsentConfiguration,
    itemType,
  }),
});

export default getTableActionHandlers;
