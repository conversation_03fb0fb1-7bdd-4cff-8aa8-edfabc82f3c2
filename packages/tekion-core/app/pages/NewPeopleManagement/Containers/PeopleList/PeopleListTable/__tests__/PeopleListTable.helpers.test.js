import { EMPTY_OBJECT, NO_DATA, SINGLE_SPACE } from '@tekion/tekion-base/app.constants';
import OPERATORS from '@tekion/tekion-base/constants/filterOperators';

import {
  getResolvedBankTemplateData,
  getFilteredColumnsFromConfig,
  resolvePayrollData,
  missingEmployeeFilter,
  getParentGroupedData,
  getDepartmentGroupByData,
  getNameForExcelDownload,
} from '../PeopleListTable.helpers';
import { EMPTY_BANK_DETAILS, TEMPLATE_MEDIA_ENUMS } from '../PeopleListTable.constants';

describe('Testing functions of PeopleListTable.helpers', () => {
  const input = [{}];
  const output = [
    {
      ...EMPTY_BANK_DETAILS,
      uploadStatus: SINGLE_SPACE,
    },
  ];

  const input2 = [
    {
      id: '1223456',
      firstName: 'xyz',
      lastName: '123',
    },
  ];

  const output2 = [
    {
      id: '1223456',
      firstName: 'xyz',
      lastName: '123',
      ...EMPTY_BANK_DETAILS,
      uploadStatus: SINGLE_SPACE,
    },
  ];

  test('Checking getResolvedBankTemplateData function', () => {
    expect(getResolvedBankTemplateData({})).toEqual([]);
    expect(getResolvedBankTemplateData(null)).toEqual([]);
  });
  test('Checking by sending the input', () => {
    expect(getResolvedBankTemplateData(input)).toEqual(output);
  });
  test('Checking by sending the input2', () => {
    expect(getResolvedBankTemplateData(input2)).toEqual(output2);
  });
  test('Validates getFilteredColumnsFromConfig', () => {
    const columns1 = [
      {
        id: 'employeeManagementPayroll_displayNo',
        key: 'displayNo',
        assetType: 'employeeManagementPayroll',
        displayName: 'Employee Number',
        order: 0,
      },
      {
        id: 'employeeManagementPayroll_crmEmail',
        key: 'crmEmail',
        assetType: 'employeeManagementPayroll',
        displayName: 'CRM Email',
        order: 17,
      },
    ];
    const columns2 = [
      {
        id: 'employeeManagementPayroll_displayNo',
        key: 'displayNo',
        assetType: 'employeeManagementPayroll',
        displayName: 'Employee Number',
        order: 0,
      },
    ];
    const columns3 = [
      {
        id: 'employeeManagementPayroll_displayNo',
        key: 'displayNo',
        assetType: 'employeeManagementPayroll',
        displayName: 'Employee Number',
        order: 0,
      },
      {
        id: 'employeeManagementPayroll_ePayrollStatements',
        key: 'ePayrollStatements',
        assetType: 'employeeManagementPayroll',
        displayName: 'Consent - ePayroll Statements',
        order: 1,
      },
    ];
    expect(getFilteredColumnsFromConfig(true)(columns1)).toEqual(columns1);
    expect(getFilteredColumnsFromConfig(false)(columns1)).toEqual(columns2);
    expect(getFilteredColumnsFromConfig(false, true)(columns3)).toEqual(columns3);
    expect(getFilteredColumnsFromConfig(false, false)(columns3)).toEqual(columns2);
  });
});

describe('Testing functions of resolvePayrollData in PeopleListTable.helpers file', () => {
  const tableData = [
    {
      id: '0010458d-c1f6-49d7-a83b-5f36fa955b92',
      dealerId: '945',
      firstName: '190720247',
      lastName: '190720247',
      middleName: null,
      shareW2OnEmployeePortal: true,
      shareW2OnEmployeeEmail: false,
      sharePaystubOnEmployeePortal: true,
      sharePaystubOnEmployeeEmail: false,
      consentExpiryTime: 1684227880925,
    },
  ];

  const output = [
    {
      citizenShip: undefined,
      consentExpiryTime: 1684227880925,
      corporateEmployee: 'No',
      dealerId: '945',
      firstName: '190720247',
      id: '0010458d-c1f6-49d7-a83b-5f36fa955b92',
      jobCodeLabel: undefined,
      lastName: '190720247',
      middleName: null,
      payFreqLabel: undefined,
      payrollEmployee: 'No',
      seasonalEmployee: 'No',
      sharePaystubOnEmployeeEmail: 'No',
      sharePaystubOnEmployeePortal: 'Yes',
      shareW2OnEmployeeEmail: 'No',
      shareW2OnEmployeePortal: 'Yes',
    },
  ];

  test('Checking resolvePayrollData function', () => {
    expect(resolvePayrollData(tableData, null, null)).toEqual(output);
    expect(resolvePayrollData(tableData, [], [])).toEqual(output);
  });
  test('Checking by sending the input', () => {
    expect(resolvePayrollData(null, null, null)).toEqual([]);
  });
});

describe('Testing missingEmployeeFilter function', () => {
  it('should return an object with key, field, operator and values properties when called with a valid missingEmployeeIds array', () => {
    const missingEmployeeIds = [1, 2, 3];
    const result = missingEmployeeFilter(missingEmployeeIds);
    expect(result).toEqual({
      key: 'id',
      field: 'id',
      operator: OPERATORS.IN,
      values: missingEmployeeIds,
    });
  });
  it('should return an object with values as an empty array when called with a undefined missingEmployeeIds array', () => {
    const missingEmployeeIds = undefined;
    const result = missingEmployeeFilter(missingEmployeeIds);
    expect(result).toEqual({
      key: 'id',
      field: 'id',
      operator: OPERATORS.IN,
      values: [],
    });
  });
});

describe('Testing functions of getParentGroupedData in PeopleListTable.helpers file', () => {
  test('Checking getParentGroupedData function with null input', () => {
    const groupByDetails = null;
    expect(getParentGroupedData(groupByDetails, EMPTY_OBJECT)).toEqual([]);
  });
  test('Checking getParentGroupedData function with input', () => {
    const groupByDetails = [
      {
        key: 'jobTitle',
        buckets: [
          { key: '1', docCount: 5 },
          { key: '2', docCount: 10 },
        ],
      },
    ];
    expect(getParentGroupedData(groupByDetails, EMPTY_OBJECT)).toEqual([
      { tagContent: 5, jobTitle: '1', tagId: '1' },
      { tagContent: 10, jobTitle: '2', tagId: '2' },
    ]);
  });

  test('Checking getParentGroupedData function with bad input', () => {
    const groupByDetails = [
      {
        key: 'jobTitle',
        buckets: [],
      },
    ];
    expect(getParentGroupedData(groupByDetails, EMPTY_OBJECT)).toEqual([]);
  });
});

describe('Testing functions of getDepartmentGroupByData in PeopleListTable.helpers file', () => {
  test('Checking getDepartmentGroupByData function with null input', () => {
    expect(getDepartmentGroupByData('', [], {})).toEqual([]);
  });
  test('Checking getDepartmentGroupByData function with input', () => {
    expect(
      getDepartmentGroupByData(
        'department',
        [
          { key: '1', docCount: 5 },
          { key: '2', docCount: 10 },
        ],
        EMPTY_OBJECT
      )
    ).toEqual([
      { tagContent: 5, department: NO_DATA, tagId: '1' },
      { tagContent: 10, department: NO_DATA, tagId: '2' },
    ]);
  });

  test('Checking getDepartmentGroupByData function with bad input', () => {
    const groupByDetails = [
      {
        key: 'department',
        buckets: [],
      },
    ];
    expect(getDepartmentGroupByData(groupByDetails.key, groupByDetails.buckets, EMPTY_OBJECT)).toEqual([]);
  });
});

describe('getNameForExcelDownload', () => {
  test('returns EMPLOYEE_DETAILS_V2 when isCheckHqEnabled is true', () => {
    expect(getNameForExcelDownload(true, false)).toBe(TEMPLATE_MEDIA_ENUMS.EMPLOYEE_DETAILS_V2);
  });

  test('returns PAYROLL_EMPLOYEE_DETAILS when isCheckHqEnabled is false and payrollApplicable is true', () => {
    expect(getNameForExcelDownload(false, true)).toBe(TEMPLATE_MEDIA_ENUMS.PAYROLL_EMPLOYEE_DETAILS);
  });

  test('returns NON_PAYROLL_EMPLOYEE_DETAILS when both isCheckHqEnabled and payrollApplicable are false', () => {
    expect(getNameForExcelDownload(false, false)).toBe(TEMPLATE_MEDIA_ENUMS.NON_PAYROLL_EMPLOYEE_DETAILS);
  });
});
