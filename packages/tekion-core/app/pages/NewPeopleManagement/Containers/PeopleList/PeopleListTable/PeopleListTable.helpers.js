import React from 'react';
import { withProps } from 'recompose';
import compose from 'recompose/compose';

import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import _map from 'lodash/map';
import _find from 'lodash/find';
import _reduce from 'lodash/reduce';
import _filter from 'lodash/filter';
import _includes from 'lodash/includes';
import _castArray from 'lodash/castArray';
import _every from 'lodash/every';
import _initial from 'lodash/initial';
import _noop from 'lodash/noop';
import _identity from 'lodash/identity';
import _property from 'lodash/property';
import _isUndefined from 'lodash/isUndefined';
import _head from 'lodash/head';
import _keys from 'lodash/keys';
import _flatten from 'lodash/flatten';
import _keyBy from 'lodash/keyBy';

import UserReader from '@tekion/tekion-base/readers/User';
import TenvReader from '@tekion/tekion-base/readers/Env';
import { INTERNAL_USER_PERSONAS_MAPPING } from '@tekion/tekion-base/constants/userMimicking.constants';
import { tget } from '@tekion/tekion-base/utils/general';
import { RESOURCE_TYPE } from '@tekion/tekion-base/bulkResolvers';
import { EMPTY_ARRAY, EMPTY_OBJECT, NO_DATA, SINGLE_SPACE } from '@tekion/tekion-base/app.constants';
import standardFieldOptionMapper from '@tekion/tekion-base/utils/optionMappers/standardFieldMapper';
import OPERATORS from '@tekion/tekion-base/constants/filterOperators';
import esGroupReader from '@tekion/tekion-base/readers/EsGroup';
import esBucketReader from '@tekion/tekion-base/readers/EsBucket';
import PUSHER_EVENT_TYPE from '@tekion/tekion-business/src/constants/payroll/payroll.pusherEventType';
import { getUserChannelName } from '@tekion/tekion-base/utils/pusher';

import FILTER_TYPES from '@tekion/tekion-components/src/organisms/filterSection/constants/filterSection.filterTypes';
import DraggerUpload from '@tekion/tekion-components/src/molecules/draggerUpload';
import { DEFAULT_FILTER_GROUP } from '@tekion/tekion-components/src/organisms/filterSection';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import KebabMenu from '@tekion/tekion-components/src/molecules/KebabMenu';
import FontIcon from '@tekion/tekion-components/src/atoms/FontIcon';
import GroupByMenu from '@tekion/tekion-components/src//molecules/groupByMenu';

import NotificationReader from '@tekion/tekion-widgets/src/organisms/notificationCenter/notification.reader';
import JobCodeReader from '@tekion/tekion-business/src/appServices/payroll/readers/payrollV2/JobCode';
import PayScheduleReader from '@tekion/tekion-business/src/appServices/payroll/readers/payrollV2/PaySchedule';
import PAYROLL_SOURCE from 'constants/payrollSource';
import OnboardingDetailsReader from '@tekion/tekion-business/src/appServices/payroll/readers/payrollV2/OnboardingDetails';
import {
  IMPORT_ACTIONS,
  EMPLOYEE_STATUS,
  EMPLOYEE_JOB,
  ITEM_TYPE,
  EMPLOYEE_JOB_CODE,
  EMPLOYEE_PAY_FREQUENCY,
  PAY_FREQ_LABEL,
  JOB_CODE_LABEL,
  DEPARTMENT_VALUE_CHANGE,
  EMPLOYEE_ROLE_ID,
  KEBAB_MENU_ACTIONS,
  EXCEL_FILE_EXTENSION,
  EMPTY_BANK_DETAILS,
  EXPORT_KEBAB_MENU_ACTIONS,
  PAYROLL_EMPLOYEE,
  IMPORT_ACTIONS_VS_LABELS,
  WORK_PHONE,
  CRM_EMAIL,
  CORPORATE_EMPLOYEE,
  SHARE_W2_ON_EMPLOYEE_PORTAL,
  SHARE_W2_ON_EMPLOYEE_EMAIL,
  SHARE_PAYSTUB_ON_EMPLOYEE_PORTAL,
  SHARE_PAYSTUB_ON_EMPLOYEE_EMAIL,
  E_PAYROLL_STATEMENTS,
  KEBAB_MENU_ACTIONS_WITH_CHECK_HQ,
  GROUP_BY_OPTIONS,
  PIVOT_COLUMN_VS_COLUMN_CONFIG,
  RRG_EMPLOYEE_LIST_ITEM_TYPE,
  CHECK_HQ_PAYROLL_ITEM_TYPE,
  PAYROLL_ITEM_TYPE,
  DEPARTMENT_GROUP_BY,
  DEFAULT_NUMBER_OF_ROWS,
  PAYROLL_EMPLOYEE_FILTER_TYPE,
  EMPLOYEE_STATUS_FILTER_TYPE,
  EMPLOYEE_INFO_STATUS_FILTER_TYPE,
  EMPLOYEE_DEPARTMENT_TYPE_FILTER_TYPE,
  TERMINATION_DATE_FILTER_TYPE,
  START_DATE_FILTER_TYPE,
  TEMPLATE_MEDIA_ENUMS,
  EMPLOYEE_ONBOARDING_ERRORS_LIST_ACTION,
} from './PeopleListTable.constants';
import PeopleListHeaderActions from '../PeopleListHeaderActions';
import PEOPLE_LIST_HEADER_ACTION_TYPES from '../PeopleListHeaderActions/PeopleListHeader.actionTypes';
import { JOB_CODE_MAPPER_ID, JOB_TITLE_RESOURCE_PARAMS } from '../../../Constants/general';
import {
  getEmployeeOnboardingErrors,
  getJobTitleLookupByKeys,
  getSortedUniqOptions,
} from '../../../helpers/peopleManagement.helpers';
import {
  getLookupSearchApiCRMEmail,
  getLookupSearchApiWorkPhone,
  getLookupSearchKeysCRMEmail,
  getLookupSearchKeysCRMWorkPhone,
} from './peopleListTable.actions';
import { fetchJobTitleAction } from '../../actions/peopleManagement.actions';
import { getWorkPhoneDetails } from '../../../../../helpers/peopleManagementAndUserSetup';
import { shouldShowCRMFields } from '../../../utils/peopleManagement.utils';
import { CRM_FIELDS } from '../exports/EmployeeList/constants/employeeList.general';
import {
  CITIZENSHIP,
  CITIZENSHIP_LABELS,
  SEASONAL_EMPLOYEE,
} from '../../PeopleManagementForm/FormConstants/payroll.constants';
import PivotTagRenderer from './Components/pivotTagRenderer';
import { isRRGProgram } from '../../PeopleManagementForm/utils/PeopleManagement.util';
import PayrollEventPayloadDataReader from './readers/PayrollEventPayloadData';

const getActionsWithCheckHq = (payrollSource, actions = EMPTY_ARRAY) => {
  if (payrollSource === PAYROLL_SOURCE.CHECKHQ) {
    return [...actions, EMPLOYEE_ONBOARDING_ERRORS_LIST_ACTION];
  }
  return actions;
};

const exportMenuItems = ({ payrollApplicable, prenoteEnabled, payrollSource }) => {
  if (!payrollApplicable) {
    const actions = [EXPORT_KEBAB_MENU_ACTIONS[0]];
    if (!isRRGProgram()) actions.push(EXPORT_KEBAB_MENU_ACTIONS[2]);
    return actions;
  }
  if (!prenoteEnabled) {
    const actions = _initial(EXPORT_KEBAB_MENU_ACTIONS);
    const actionsWithCheckHq = getActionsWithCheckHq(payrollSource, actions);
    return isRRGProgram()
      ? _filter(actionsWithCheckHq, action => action !== EXPORT_KEBAB_MENU_ACTIONS[2])
      : actionsWithCheckHq;
  }
  const actionsWithCheckHq = getActionsWithCheckHq(payrollSource, EXPORT_KEBAB_MENU_ACTIONS);
  return isRRGProgram()
    ? _filter(actionsWithCheckHq, action => action !== EXPORT_KEBAB_MENU_ACTIONS[2])
    : actionsWithCheckHq;
};

const handleAction =
  (onAction = _noop, payload = EMPTY_OBJECT) =>
  actionType => {
    onAction({ type: actionType, payload });
  };

const getKebabMenuItems = payrollSource => {
  if (payrollSource === PAYROLL_SOURCE.CHECKHQ) {
    const userInfo = TenvReader.userInfo();
    const persona = UserReader.persona(userInfo);
    if (persona === INTERNAL_USER_PERSONAS_MAPPING.ISM) return KEBAB_MENU_ACTIONS_WITH_CHECK_HQ;
  }
  return KEBAB_MENU_ACTIONS;
};

export const getSubHeaderProps = (
  onAction,
  isNewTeamsSetupEnabled,
  selection,
  payrollApplicable,
  viewSensitiveInfo,
  prenoteEnabled,
  payrollSource,
  pivotById,
  isEmployeeEditable
) => {
  const subHeaderRightActions = [];
  const isRRG = isRRGProgram();
  if (payrollApplicable) {
    const importActions = isRRG
      ? _filter(IMPORT_ACTIONS, action => action !== IMPORT_ACTIONS.UPLOAD_EMPLOYEE_DETAILS)
      : IMPORT_ACTIONS;
    subHeaderRightActions.push({
      renderer: KebabMenu,
      renderOptions: {
        menuItems: _map(importActions, actionType => ({
          label: (
            <DraggerUpload
              customRequest={handleImportActionUpload(onAction, actionType)}
              className="reset-upload-btn"
              showUploadList={false}
              accept={EXCEL_FILE_EXTENSION}>
              <div>{IMPORT_ACTIONS_VS_LABELS[actionType]}</div>
            </DraggerUpload>
          ),
          value: actionType,
        })),
        onClickAction: _noop,
        triggerElement: <FontIcon>icon-upload</FontIcon>,
        triggerOn: 'hover',
      },
    });
  }
  if (!payrollApplicable && !isRRG) {
    subHeaderRightActions.push({
      renderer: KebabMenu,
      renderOptions: {
        menuItems: [
          {
            label: (
              <DraggerUpload
                customRequest={handleImportActionUpload(onAction, [IMPORT_ACTIONS.UPLOAD_EMPLOYEE_DETAILS])}
                className="reset-upload-btn"
                showUploadList={false}
                accept={EXCEL_FILE_EXTENSION}>
                <div>{IMPORT_ACTIONS_VS_LABELS[IMPORT_ACTIONS.UPLOAD_EMPLOYEE_DETAILS]}</div>
              </DraggerUpload>
            ),
            value: [IMPORT_ACTIONS.UPLOAD_EMPLOYEE_DETAILS],
          },
        ],
        onClickAction: _noop,
        triggerElement: <FontIcon>icon-upload</FontIcon>,
        triggerOn: 'hover',
      },
    });
  }
  subHeaderRightActions.push(
    ...[
      {
        renderer: KebabMenu,
        renderOptions: {
          menuItems: exportMenuItems({ payrollApplicable, prenoteEnabled, payrollSource }),
          onClickAction: handleAction(onAction, viewSensitiveInfo),
          triggerElement: <FontIcon className="m-l-16">icon-download1</FontIcon>,
          triggerOn: 'hover',
        },
      },
      {
        renderer: PeopleListHeaderActions,
        renderOptions: { isNewTeamsSetupEnabled, selection, isEmployeeEditable, payrollSource, payrollApplicable },
        action: PEOPLE_LIST_HEADER_ACTION_TYPES.RIGHT_ACTION_SELECTED,
      },
    ]
  );
  if (isRRG) {
    subHeaderRightActions.push({
      renderer: GroupByMenu,
      renderOptions: {
        groupByOptions: GROUP_BY_OPTIONS,
        shouldShowDefaultOption: false,
        className: 'm-l-16',
        groupByKey: pivotById,
      },
      action: PEOPLE_LIST_HEADER_ACTION_TYPES.GROUP_BY,
    });
  }
  if (payrollApplicable)
    subHeaderRightActions.push({
      renderer: KebabMenu,
      renderOptions: {
        menuItems: getKebabMenuItems(payrollSource),
        onClickAction: handleAction(onAction),
        triggerElement: <FontIcon className="m-l-16">icon-operations</FontIcon>,
        triggerOn: 'hover',
      },
    });
  return {
    subHeaderRightActions,
  };
};

const handleImportActionUpload =
  (onAction = _noop, type) =>
  payload => {
    onAction({ type, payload });
  };

const getLookUpResourceParams = key => ({
  getOptionLabel: _property(key),
  getOptionValue: _property(key),
  getOptionData: _identity,
});

const getResourceParamsForWorkPhone = key => ({
  getOptionLabel: option => getWorkPhoneDetails(option),
  getOptionValue: _property(key),
  getOptionData: _identity,
});

const getCheckHQFilters = (filterTypes, { jobCodes, paySchedules }) => [
  ...filterTypes,
  {
    id: 'jobCodeId',
    name: __('Job Code'),
    type: FILTER_TYPES.MULTI_SELECT,
    additional: {
      options: standardFieldOptionMapper(JOB_CODE_MAPPER_ID, jobCodes),
    },
  },
  {
    id: 'payScheduleId',
    name: __('Pay Frequency'),
    type: FILTER_TYPES.MULTI_SELECT,
    additional: {
      options: standardFieldOptionMapper(undefined, paySchedules),
    },
  },
  PAYROLL_EMPLOYEE_FILTER_TYPE,
];

const getDefaultFilterTypes = (filterTypes, { jobCodeAndEntities, payFrequencies }) => {
  filterTypes.push(
    {
      id: EMPLOYEE_JOB_CODE,
      name: __('Job Code'),
      type: FILTER_TYPES.MULTI_SELECT,
      additional: {
        options: standardFieldOptionMapper(JOB_CODE_MAPPER_ID, jobCodeAndEntities),
      },
    },
    {
      id: EMPLOYEE_PAY_FREQUENCY,
      name: __('Pay Frequency'),
      type: FILTER_TYPES.MULTI_SELECT,
      additional: {
        options: standardFieldOptionMapper(undefined, payFrequencies),
      },
    }
  );

  return [...filterTypes, PAYROLL_EMPLOYEE_FILTER_TYPE];
};

const PAYROLL_SOURCE_VS_FILTER_TYPES = {
  [PAYROLL_SOURCE.CHECKHQ]: getCheckHQFilters,
  DEFAULT: getDefaultFilterTypes,
};

export const getFilterProps = (
  selectedFilters,
  employeeList,
  selectedFilterGroup,
  payrollApplicable,
  jobCodeAndEntities,
  payFrequencies,
  roleList,
  payrollSource,
  jobCodes,
  paySchedules
) => {
  let filterTypes = [
    EMPLOYEE_STATUS_FILTER_TYPE,
    {
      id: EMPLOYEE_ROLE_ID,
      name: __('Role'),
      type: FILTER_TYPES.MULTI_SELECT,
      additional: {
        options: standardFieldOptionMapper(undefined, roleList),
      },
    },
    {
      id: EMPLOYEE_JOB,
      name: __('Job Title'),
      type: FILTER_TYPES.ASYNC_MULTI_SELECT,
      resourceType: RESOURCE_TYPE.EMPLOYEE,
      resourceParams: JOB_TITLE_RESOURCE_PARAMS,
      additional: {
        lookupByKeysApi: getJobTitleLookupByKeys,
        lookUpSearchApi: fetchJobTitleAction,
        getResolvedOptions: getSortedUniqOptions,
      },
    },
    EMPLOYEE_INFO_STATUS_FILTER_TYPE,
    EMPLOYEE_DEPARTMENT_TYPE_FILTER_TYPE,
    TERMINATION_DATE_FILTER_TYPE,
    START_DATE_FILTER_TYPE,
  ];
  if (shouldShowCRMFields()) {
    filterTypes = [
      ...filterTypes,
      {
        id: WORK_PHONE,
        key: WORK_PHONE,
        name: __('Work Phone'),
        type: FILTER_TYPES.ASYNC_MULTI_SELECT,
        resourceType: RESOURCE_TYPE.DEALER_ACCESSIBLE_USER_MINIMAL,
        resourceParams: getResourceParamsForWorkPhone(WORK_PHONE),
        additional: {
          lookUpSearchApi: getLookupSearchApiWorkPhone,
          lookupByKeysApi: getLookupSearchKeysCRMWorkPhone,
        },
      },
      {
        id: CRM_EMAIL,
        key: CRM_EMAIL,
        name: __('CRM Email'),
        type: FILTER_TYPES.ASYNC_MULTI_SELECT,
        resourceType: RESOURCE_TYPE.TENANT_USER_MINIMAL_V2,
        resourceParams: getLookUpResourceParams(CRM_EMAIL),
        additional: {
          lookUpSearchApi: getLookupSearchApiCRMEmail,
          lookupByKeysApi: getLookupSearchKeysCRMEmail,
        },
      },
    ];
  }
  if (payrollApplicable) {
    const funcToGetFilterTypes =
      PAYROLL_SOURCE_VS_FILTER_TYPES[payrollSource] || PAYROLL_SOURCE_VS_FILTER_TYPES.DEFAULT;
    filterTypes = funcToGetFilterTypes(filterTypes, { jobCodeAndEntities, payFrequencies, jobCodes, paySchedules });
  }
  return {
    filterTypes,
    defaultFilterTypes: [EMPLOYEE_STATUS, EMPLOYEE_JOB],
    assetType: ITEM_TYPE,
    appliedFilterGroup: selectedFilterGroup || DEFAULT_FILTER_GROUP,
  };
};

export const getPayLoad = ({ allEmployeeList }) => ({
  allEmployeeList,
});

const getDepartmentOptionMap = departmentFilterOptions =>
  _reduce(
    departmentFilterOptions,
    (acc, department, index) => ({
      ...acc,
      [DEPARTMENT_VALUE_CHANGE[index]]: department,
    }),
    {}
  );

const getDepartmentId = (departmentId, dealershipDepartmentName, departmentFilterOptions) => {
  const filterOptions = departmentFilterOptions[dealershipDepartmentName];
  const option = _find(filterOptions, item => _get(item, 'value') === departmentId);
  return _get(option, 'label');
};

const getGroupByDepartmentId = (departmentId = '', departmentOptionMap = EMPTY_OBJECT) =>
  _get(departmentOptionMap, `${departmentId}.label`, NO_DATA);

const getOnboardingErrorsAndStatus = (onboardingDetailsListById, id) => {
  const employeeOnboardingDetails = onboardingDetailsListById?.[id];
  const onboardStatus = OnboardingDetailsReader.onboardStatus(employeeOnboardingDetails);
  const errorDetails = OnboardingDetailsReader.errorDetails(employeeOnboardingDetails);
  const onboardingErrors = errorDetails ? getEmployeeOnboardingErrors(errorDetails) : undefined;
  return { onboardStatus, onboardingErrors };
};

export const getResolvedEmployeeList = (allEmployeeList, props) => {
  const { payrollApplicable, departmentFilterOptions, roleList, onboardingDetailsListById } = props;
  const returnData = _map(allEmployeeList, employee => {
    const { departmentId, dealershipDepartmentName, roleId, id } = employee;
    const roleObject = _find(roleList, item => _get(item, 'id') === roleId);
    const position = _get(roleObject, 'name');
    const departmentOptionMap = getDepartmentOptionMap(departmentFilterOptions);
    const departmentTypeDisplayName = dealershipDepartmentName;
    const { onboardStatus, onboardingErrors } = getOnboardingErrorsAndStatus(onboardingDetailsListById, id);
    const departmentDisplayName = getDepartmentId(departmentId, dealershipDepartmentName, departmentOptionMap);
    return {
      ...employee,
      position,
      departmentTypeDisplayName,
      departmentDisplayName,
      onboardStatus,
      onboardingErrors,
    };
  });
  return payrollApplicable ? resolvePayrollData(returnData, props) : returnData;
};

export const getResolvedBankTemplateData = allEmployeeList =>
  _map(allEmployeeList, employee => ({
    ...employee,
    ...EMPTY_BANK_DETAILS,
    uploadStatus: SINGLE_SPACE,
  }));

export const handleAddToTeam = (action, dispatch, getState) => {
  const { selection } = getState();
  if (_isEmpty(selection)) {
    toaster(TOASTER_TYPE.ERROR, __('Please select atlease one employee'));
  }
};

const getBooleanLabelBasedOnValue = val => (val ? __('Yes') : __('No'));

const getValueForConsentFields = val => {
  if (_isUndefined(val)) return val;
  return getBooleanLabelBasedOnValue(val);
};

export const resolvePayrollData = (tableData, { payrollSource, ...props }) => {
  if (payrollSource === PAYROLL_SOURCE.CHECKHQ) {
    return resolvePayrollV2Data(tableData, props);
  }
  return resolvePayrollV1Data(tableData, props);
};

const resolveAndAddPayrollV1DataToRow = (jobCodeAndEntities, payFreqOptions) => row => {
  const payFreq = _get(row, EMPLOYEE_PAY_FREQUENCY);
  const freqObject = _find(payFreqOptions, listItem => _get(listItem, 'id') === payFreq);
  const jobCode = _get(row, EMPLOYEE_JOB_CODE);
  const jobCodeObject = _find(jobCodeAndEntities, listItem => _get(listItem, 'id') === jobCode);
  const payrollEmployee = getBooleanLabelBasedOnValue(_get(row, PAYROLL_EMPLOYEE));
  const citizenShip = _get(row, CITIZENSHIP);
  const seasonalEmployee = getBooleanLabelBasedOnValue(_get(row, SEASONAL_EMPLOYEE));
  const corporateEmployee = getBooleanLabelBasedOnValue(_get(row, CORPORATE_EMPLOYEE));
  const shareW2OnEmployeePortal = getValueForConsentFields(_get(row, SHARE_W2_ON_EMPLOYEE_PORTAL));
  const shareW2OnEmployeeEmail = getValueForConsentFields(_get(row, SHARE_W2_ON_EMPLOYEE_EMAIL));
  const sharePaystubOnEmployeePortal = getValueForConsentFields(_get(row, SHARE_PAYSTUB_ON_EMPLOYEE_PORTAL));
  const sharePaystubOnEmployeeEmail = getValueForConsentFields(_get(row, SHARE_PAYSTUB_ON_EMPLOYEE_EMAIL));
  return {
    ...row,
    [PAY_FREQ_LABEL]: _get(freqObject, 'name'),
    [JOB_CODE_LABEL]: _get(jobCodeObject, 'code'),
    payrollEmployee,
    [CITIZENSHIP]: CITIZENSHIP_LABELS[citizenShip],
    seasonalEmployee,
    corporateEmployee,
    shareW2OnEmployeePortal,
    shareW2OnEmployeeEmail,
    sharePaystubOnEmployeePortal,
    sharePaystubOnEmployeeEmail,
  };
};

export const resolvePayrollV1Data = (tableData, { payFrequencies, jobCodeAndEntities }) =>
  _map(tableData, resolveAndAddPayrollV1DataToRow(jobCodeAndEntities, payFrequencies));

const resolveAndAddPayrollV2DataToRow =
  (paySchedulesById = EMPTY_OBJECT, jobCodesById = EMPTY_OBJECT) =>
  row => {
    const payScheduleId = _get(row, 'payScheduleId');
    const paySchedule = paySchedulesById[payScheduleId];
    const jobCodeId = _get(row, 'jobCodeId');
    const jobCode = jobCodesById[jobCodeId];

    const payrollEmployee = getBooleanLabelBasedOnValue(_get(row, PAYROLL_EMPLOYEE));

    return {
      ...row,
      [PAY_FREQ_LABEL]: PayScheduleReader.name(paySchedule),
      [JOB_CODE_LABEL]: JobCodeReader.code(jobCode),
      payrollEmployee,
    };
  };

export const resolvePayrollV2Data = (tableData, { paySchedulesById, jobCodesById }) =>
  _map(tableData, resolveAndAddPayrollV2DataToRow(paySchedulesById, jobCodesById));

export const getSelectedFilters = filters =>
  _map(filters, item => {
    if (item.field)
      return {
        ...item,
        type: item.field,
      };
    return item;
  });

export const getFileList = (fileData = EMPTY_OBJECT) => {
  const file = tget(fileData, 'file', EMPTY_ARRAY);
  const fileList = _castArray(file);
  return fileList;
};

const isFileXLSX = file => {
  const fileType = file?.name;
  return _includes(fileType, EXCEL_FILE_EXTENSION);
};

export const areAllFilesXLSX = fileList => _every(fileList, isFileXLSX);

export const getFilteredColumnsFromConfig =
  (isCRMFieldsVisible, enableConsentConfiguration) =>
  (allColumns = EMPTY_ARRAY) => {
    let columnsToExclude = isCRMFieldsVisible ? EMPTY_ARRAY : CRM_FIELDS;
    if (!enableConsentConfiguration) {
      columnsToExclude = [...columnsToExclude, E_PAYROLL_STATEMENTS];
    }
    return _filter(allColumns, column => !_includes(columnsToExclude, column?.key));
  };

export const missingEmployeeFilter = (missingEmployeeIds = EMPTY_ARRAY) =>
  _isEmpty(missingEmployeeIds)
    ? EMPTY_OBJECT
    : {
        key: 'id',
        field: 'id',
        operator: OPERATORS.IN,
        values: missingEmployeeIds,
      };

export const getPivotColumnConfig = pivotById =>
  _castArray({
    Cell: withProps({ pivotById })(PivotTagRenderer),
    ...(PIVOT_COLUMN_VS_COLUMN_CONFIG[pivotById] || EMPTY_OBJECT),
  });

export const getDepartmentGroupByData = (key, buckets, departmentFilterOptions) => {
  const departmentOptionMap = _keyBy(
    _flatten(
      _reduce(_keys(departmentFilterOptions), (acc, value) => [...acc, departmentFilterOptions[value]], EMPTY_ARRAY)
    ),
    'value'
  );

  return _map(buckets, group => ({
    tagContent: esBucketReader.docCount(group),
    [key]: getGroupByDepartmentId(esBucketReader.key(group), departmentOptionMap),
    tagId: esBucketReader.key(group),
  }));
};

export const getParentGroupedData = (groupByDetails, departmentFilterOptions) => {
  const groupByObject = _head(groupByDetails);
  const key = esGroupReader.key(groupByObject);
  const buckets = esGroupReader.buckets(groupByObject);
  switch (key) {
    case DEPARTMENT_GROUP_BY.id:
      return getDepartmentGroupByData(key, buckets, departmentFilterOptions);

    default:
      return _map(buckets, group => ({
        tagContent: esBucketReader.docCount(group),
        [key]: esBucketReader.key(group),
        tagId: esBucketReader.key(group),
      }));
  }
};

export const getPivotParentTableProps = ({ totalCount = 0, loading = false }) => ({
  rowHeight: 40,
  totalNumberOfEntries: totalCount,
  loading,
  showPagination: false,
  currentPage: 1,
});

export const getGroupByPayload = pivotById =>
  _castArray({
    key: pivotById,
    field: pivotById,
    groupType: 'FIELD',
    rows: DEFAULT_NUMBER_OF_ROWS,
    start: 0,
  });

export const getListItemType = (isCheckHqEnabled = false, isRRG = false) => {
  if (isRRG) return RRG_EMPLOYEE_LIST_ITEM_TYPE;
  if (isCheckHqEnabled) return CHECK_HQ_PAYROLL_ITEM_TYPE;
  return PAYROLL_ITEM_TYPE;
};

export const getPusherEventParams = () => ({
  channelName: getUserChannelName(),
  pusherEventName: PUSHER_EVENT_TYPE.PAYROLL_BACKGROUND_TASK,
});

export const getNotificationMessage = compose(
  PayrollEventPayloadDataReader.bodyOfMessage,
  NotificationReader.payloadData
);

export const isNotificationForSuccess = compose(
  PayrollEventPayloadDataReader.isSuccess,
  NotificationReader.payloadData
);

export const getNameForExcelDownload = (isCheckHqEnabled, payrollApplicable) => {
  if (isCheckHqEnabled) {
    return TEMPLATE_MEDIA_ENUMS.EMPLOYEE_DETAILS_V2;
  }

  if (payrollApplicable) {
    return TEMPLATE_MEDIA_ENUMS.PAYROLL_EMPLOYEE_DETAILS;
  }

  return TEMPLATE_MEDIA_ENUMS.NON_PAYROLL_EMPLOYEE_DETAILS;
};
