/* eslint-disable prefer-destructuring */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { compose } from 'recompose';
import { connect } from 'react-redux';
import { defaultMemoize } from 'reselect';
import _get from 'lodash/get';
import _map from 'lodash/map';
import _head from 'lodash/head';
import _delay from 'lodash/delay';
import _omit from 'lodash/omit';
import _some from 'lodash/some';
import _noop from 'lodash/noop';
import _isEmpty from 'lodash/isEmpty';
import _includes from 'lodash/includes';
import _keyBy from 'lodash/keyBy';

import NOTES_ASSET_TYPES from '@tekion/tekion-base/constants/notesAssetTypes';
import DEALER_PROPERTY_CONSTANTS from '@tekion/tekion-base/constants/dealerProperties';
import { getErrorMessage } from '@tekion/tekion-base/utils/errorUtils';
import { ES_REFETCH_DELAY } from '@tekion/tekion-base/constants/general';
import { CORE } from '@tekion/tekion-base/constants/appServices';
import { STATUS } from '@tekion/tekion-base/constants/status.constants';
import { EMPTY_OBJECT, EMPTY_ARRAY, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { NO, YES } from '@tekion/tekion-base/constants/deal/common';
import getDataFromResponse from '@tekion/tekion-base/utils/getDataFromResponse';

import { DIRECT_DEPOSIT } from '@tekion/tekion-business/src/appServices/payroll/constants/paymentMethods';
import IconAsBtn from '@tekion/tekion-components/atoms/iconAsBtn';
import Button from '@tekion/tekion-components/src/atoms/Button';
import Modal from '@tekion/tekion-components/src/molecules/Modal';
import FieldLabel from '@tekion/tekion-components/src/organisms/FormBuilder/components/fieldLabel';
import Loader from '@tekion/tekion-components/src/molecules/loader';
import Page from '@tekion/tekion-components/src/molecules/pageComponent/PageComponent';
import withFormPageState from '@tekion/tekion-components/src/connectors/withFormPageState';
import withSize from '@tekion/tekion-components/src/hoc/withSize';
import withActions from '@tekion/tekion-components/src/connectors/withActions';
import SaveComponent from '@tekion/tekion-components/src/molecules/SaveComponent';
import Heading from '@tekion/tekion-components/src/atoms/Heading';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import { triggerSubmit } from '@tekion/tekion-components/src/pages/formPage/utils/formAction';
import { PermissionContext } from '@tekion/tekion-components/src/widgets/permissionsHelper';
import { handleFetchNote } from '@tekion/tekion-components/src/actions/notes.actions';
import StatusTabs from '@tekion/tekion-components/src/molecules/statusTabs';
import { withTekionConversion } from '@tekion/tekion-conversion-web';
import { withFeatureFlags, FeatureFlags } from '@tekion/tekion-widgets/src/context/featureFlags';
import { withInformationBanner } from '@tekion/tekion-widgets/src/appServices/payroll/organisms/informationViews';
import toastErrorMessage from '@tekion/tekion-widgets/src/utils/toastErrorMessage';
import OnboardingDetailsReader from '@tekion/tekion-business/src/appServices/payroll/readers/payrollV2/OnboardingDetails';

import withEmployeeAndUserEditEnabled from 'connectors/withEmployeeAndUserEditEnabled';
import { getCoreRoutesWithRoot } from 'helpers/base.helpers';
import { BASE_ROUTE as USER_LIST_BASE_ROUTE } from 'pages/NewUserSetup/Constants/route.constants';
import { isEmailEditable, getUnhiddenRolesList } from 'helpers/peopleManagementAndUserSetup';
import { setNewEOLoaded } from 'pages/base/app.actions';
import { getUserDealersAndRolesByIds, fetchLocalisedRoles } from 'actions/user.actions';
import { fetchDealerOptionsAction } from 'actions/dealer.action';
import { fetchDepartmentFilterOptions } from 'actions/employee.actions';
import PeopleManagementReader from 'readers/PeopleManagement.reader';
import { getDealersById, getLocalisedActiveRoles, getUsersList, getDepartmentOptions } from 'selectors/general';
import { isEmployeeHoursOtDotV3Enabled, isInchcapeProgram } from 'utils/index';
import FEATURE_FLAGS from 'factories/featureFlags/constants/featureFlags';
import { getNonInternalRolesList } from 'helpers/rolesAndPermissions.helpers';

import getPayrollSource from 'helpers/payrollSource';
import PAYROLL_SOURCE from 'constants/payrollSource';
import AuditLogs from './FormComponents/AuditLogs/AuditLogs';
import {
  getSelectedTabId,
  getTabsWithStatus,
  roleMapper,
  getAutofillFormInitialValues,
  getDealerStateCode,
  isFormHavingError,
  getFooterProps,
  shouldUpdateBankDetails,
  shouldShowHourlyWageWarning,
  getContributionsByDeductionId,
  handleFormLoadAndBalanceWarningVisibilityhelper,
  handleSubmitConfirmationModal,
  getAllActiveItems,
  getAdditionalButtonProps,
  displayFlagHourCalculationCheckboxes,
  isOverTimeSectionVisible,
  shouldRenderFooter,
  checkTabSwitchEnabledOnUpdate,
  getOnboardingStatusIcon,
  getOnboardingStatusIconClassname,
  makePayrollEnabledStates,
  getEmployeeWorkingState,
  redirectToEmployeeSetup,
  getDateTimeFieldPlaceholder,
  getWarningMessages,
} from './PeopleManagementForm.helper';
import { PAYMENT_TYPE, PRENOTE_STATUS, BANK_DETAILS } from './FormComponents/BankDetailsForm/bankDetailsForm.constants';

import {
  CONTEXT_ID,
  FIRST_NAME,
  LAST_NAME,
  UPDATE,
  CREATE,
  TABS_HEIGHT,
  getAllTabs,
  ADDRESS_CONTEXT_ID,
  ALL_PAGE,
  ACTION_TYPES,
  CREATE_USER,
  PAID_HOURLY_WAGE,
  LINKED_USER_ID,
  LOGIN_USER_CREATE,
  PASSWORD_USER_CREATE,
  EMAIL_ID,
  INFO_DISPLAY,
  SHOW_CERTIFICATION_NUMBER_FIELD,
  WEEK_START_DAY,
  OT_CONFIGURATION_TYPE,
  OMIT_FIELDS,
  AUTOFILL_FORM_HEIGHT,
  TIMEOUT_SECONDS,
  DEFAULT_TABS,
  FORM_CONFIG_DEPENDENT_KEYS,
  JOB_TITLE,
  PAYROLL_EMPLOYEE,
  FLAG_HOURS,
  FLSA_STATUS,
  PAYROLL_TAXATION_ADDRESS,
  PAYROLL_TAXATION_ADDRESS_CONTEXT_ID,
  BUTTON_LABEL_EOB,
  FORM_CONFIG_SECTION_DEPENDENT_KEYS,
  EMPLOYEE_STATUS,
  CORE_TABS,
  WAGE_DETAILS,
  CANCEL_BUTTON_LABEL_EOB,
  PAYROLL_INFO_FORM_CONTEXT_ID,
  SSN,
  SAVE_ACTIONS,
  EMPLOYEE_ADDRESS,
} from './FormConstants/constants';

import CurrentBalanceWarningModal from './FormComponents/CurrentBalanceWarningModal';
import { STATE_CODE, W4_EMPLOYEE_YEAR } from './FormConstants/taxDetails.constants';
import { createSelectorForFormFields, getFieldsForAutofill } from './FormConstants/PeopleManagement.fields';
import { createSelectorForFormSections, getSectionsForAutofill } from './FormConstants/PeopleManagement.sections';

import { PEOPLE_MANAGEMENT_PATH } from '../../Constants/route.constants';
import {
  fetchPeopleInfoWithOTConfig,
  createPeople,
  fetchServiceTypeOptions,
  fetchLaborRateOptions,
  updatePeople,
  fetchPayrollPayFrequenciesAction,
  fetchEmployeePayrollMetadataAction,
  getOtConfigurationsForDealer,
  getAllStates,
  getPayrollEnabledStates,
  fetchJobCodesAndRelatedEntitiesAction,
  fetchPayrollDeductionsAction,
  fetchPayrollContributionsAction,
  fetchMakeOptions,
  fetchPayrollSettingsAction,
  fetchPayrollSettingsV2Action,
  fetchTimeOffPlansAction,
  fetchSitesByDealerIds,
  fetchPayrollActiveEarnings,
  fetchPayTypeOptions,
  fetchEmployeeMetadataAction,
  fetchPaySchedules,
} from '../../PeopleManagement.action';

import {
  getPayrollDeductions,
  getPayrollJobCodeEntities,
  getPayrollPayFrequencies,
  getServiceTypeOptions,
  getLaborRateOptions,
  getPayrollEeocs,
  getOtConfigurations,
  getStateCodesVsNames,
  getPayrollEnabledStateCodes,
  getAllContributions,
  getMakeOptions,
  getPayrollSettingsForPrenote,
  getTimeOffById,
  getFreqOptions,
  getJobCategories,
  getSitesByDealerIds,
  getRetirementDeductionFederalLimit,
  getPayrollEarnings,
  getPayTypeOptions,
  getSupportedStateCodes,
  getNewEOLoadedFlag,
  getPaySchedules,
} from '../../PeopleManagement.selectors';

import {
  arePayrollPrimaryFieldsEditable,
  shouldDisplayEmployeeSensitiveInfo,
} from '../../permissions/peopleManagement.permissions';
import {
  ACTION_HANDLERS,
  FIELD_ACTION_HANDLERS,
  TAB_ACTION_HANDLERS,
  ADDITIONAL_ACTION_HANDLERS,
} from './PeopleManagement.actionHandlers';

// Utils
import peopleManagementReverseMap from './utils/PeopleManagement.getReverseMap';
import getFormValuesMap from './utils/PeopleManagement.getFormValuesMap';
import getFormInitiValueForCreate from './utils/PeopleManagement.formInitialValuesForCreate';
import {
  DEPOSIT_DETAILS_UPDATED,
  EARNING,
  EMPLOYEE_COMPANY_SETUP_INCOMPLETE_WARNING_INFORMATION_ITEM,
  HOURLY_WAGE_MISSING,
  JOB_CODE,
  NOTIFICATION_LABEL_PROPS,
} from './FormConstants/payroll.constants';
import utils, { displayAdditionalButton, isRRGProgram } from './utils/PeopleManagement.util';

import PeopleManagementFormWithState from './PeopleManagementFormWithState';

import NotesIcon from './FormComponents/EmployeeNotes';

import { getAuditAttributeFormattersMap } from './utils/auditLogsUtils/formatters';
import { getAuditLogsLocalLookup } from './utils/auditLogsUtils/lookup';

import getFormattedOptions from './utils/PeopleManagement.getFormattedOptions';
import { createSelectorForRrgFormSections } from './FormConstants/PeopleManagementRrg.sections';
import PayStubsAndW2ConsentDrawer from './FormComponents/PayStubsAndW2ConsentDrawer';

import styles from './peopleManagementForm.module.scss';

import { EMPLOYEE_INFO_STATUS } from '../PeopleList/PeopleListTable/PeopleListTable.constants';

import NotificationLabel from './FormComponents/PayStubsAndW2ConsentDrawer/components/NotificationLabel';

import { ENABLE_CONSENT_CONFIGURATION, CONSENT_CONFIGURATION } from '../../Constants/general';
import { getErrorsOnUpdateFormMount } from './helpers/peopleManagementForm.errors.helpers';
import { CHECK_HQ_COMPONENT_BY_TAB_ID } from './FormConstants/peopleManagementForm.payrollCheckTabs';
import OnboardingErrorModal from './organisms/onboardingErrorModal';
import W4WithholdingModal from './organisms/w4WithholdingModal';
import EmployeeSetupIncompleteModal from './molecules/employeeSetupIncompleteModal';
import InvalidAddressModal from './molecules/invalidAddressModal';
import { fetchEmployeeOnboardingDetailsById } from '../../PeopleManagement.api';
import {
  getEarnings,
  getWCTypes,
  fetchEarnings,
  fetchWCTypes,
  fetchPayrollMetadata,
  getPayrollMetadata,
} from './organisms/payrollInfoForm';
import { validateEmployeeDetails } from './peopleManagementForm.services';

import ValidationReader from './readers/Validation';
import { getAddressErrorDetails, getValidateEmployeeDetailsRequestDto } from './helpers/peopleManagementForm.general';

const FormWithState = withFormPageState(undefined, ACTION_HANDLERS)(PeopleManagementFormWithState);
const AutoFillFormWithState = withFormPageState()(PeopleManagementFormWithState);

const INITIAL_STATE = {
  dealerInformation: EMPTY_OBJECT,
  currentPage: ALL_PAGE.EMPLOYEE_INFORMATION,
  isLoading: true,
  hideInfo: false,
  showAutoFillConfirmationModal: false,
  autofillData: EMPTY_OBJECT,
  setFormState: _noop,
  formValuesBeforeAutofill: EMPTY_OBJECT,
  showUnhideInfoModal: false,
  bankContextIds: [CONTEXT_ID],
  hideInfoError: false,
  payrollInfoFetched: false,
  showPaymentTypeModal: false,
  tabErrors: DEFAULT_TABS,
  tabsUtilized: DEFAULT_TABS,
  pageOrder: Object.values(_omit(ALL_PAGE, ALL_PAGE.EMPLOYEE_TAX_DETAILS)),
  taxParams: EMPTY_ARRAY,
  isPayrollEmployee: true,
  taxationAddress: undefined,
  addressChanged: true,
  fetchingTaxParams: false,
  isPayrollAddressChanged: false,
  fetchingOnboardingDetails: false,
  onboardStatus: false,
  errorDetails: false,
};

class PeopleManagementForm extends Component {
  getTabsWithStatus = defaultMemoize(getTabsWithStatus);

  getAuditAttributeFormattersMap = defaultMemoize(getAuditAttributeFormattersMap);

  getAuditLogsLocalLookup = defaultMemoize(getAuditLogsLocalLookup);

  getAllTabs = defaultMemoize(getAllTabs);

  getFooterProps = defaultMemoize(getFooterProps);

  constructor(props) {
    super(props);
    this.formRef = React.createRef();
    this.getFormFields = createSelectorForFormFields();
    this.getRrgFormSections = createSelectorForRrgFormSections();
    this.getFormSections = createSelectorForFormSections();
    this.state = {
      initialValues: EMPTY_OBJECT,
      isPostingData: false,
      emailEditable: false,
      mode: CREATE,
      peopleId: _get(props, 'params.peopleId'),
      showModal: false,
      errorOccured: false,
      userInfo: EMPTY_OBJECT,
      isCRMEnabled: false,
      response: EMPTY_OBJECT,
      showSubmitConfirmationModal: false,
      isNewTeamsSetupEnabled: false,
      dealerStateCode: '',
      payrollApplicable: false,
      updateMode: false,
      tabsSwitchable: false,
      employeeDisabledRole: EMPTY_STRING,
      handler: _noop,
      primaryPayrollFieldsEditable: false,
      displayEmployeeSensitiveInfo: false,
      isEmployeeHoursOtDotEnabled: false,
      isCrmNewGtcSetupEnabled: false,
      europeanEOEnabled: false,
      showCurrentBalanceWarning: false,
      isSaveClicked: false,
      showFormCancelConfirmationModal: false,
      loading: false,
      hasTabSwitched: false,
      hasCloseClicked: false,
      isDirty: false,
      inBetweenSave: false,
      isCheckHqEnabled: false,
      isCancelClicked: false,
      initialErrors: EMPTY_OBJECT,
      hasAddressChanged: false,
      isOnboardingComplete: true,
      showW4WithholdingModal: false,
      w4WithholdingDetails: EMPTY_ARRAY,
      hasUserClickedSave: false,
      updateSuggestedAddress: false,
    };
    this.tabOverrideRenderers = {
      [ALL_PAGE.EMPLOYEE_TAX_DETAILS_V2]: this.renderTaxDetailsOverride,
    };
  }

  componentDidMount() {
    const { getDealerPropertyValue, featureFlags, getDateTimeFormatValue } = this.props;
    this.timeoutId = '';
    const isCRMEnabled = getDealerPropertyValue(DEALER_PROPERTY_CONSTANTS.CRM_ENABLED);
    const isNewTeamsSetupEnabled = getDealerPropertyValue(DEALER_PROPERTY_CONSTANTS.NEW_TEAMS_SETUP_ENABLED);
    const payrollApplicable = !!getDealerPropertyValue(DEALER_PROPERTY_CONSTANTS.TEKION_PAYROLL_ENABLED);
    const employeeDisabledRole = getDealerPropertyValue(DEALER_PROPERTY_CONSTANTS.EMPLOYEE_ROLE_EDIT_DISABLED);
    const isCrmNewGtcSetupEnabled = getDealerPropertyValue(DEALER_PROPERTY_CONSTANTS.CRM_NEW_GTC_SETUP_ENABLED);
    const europeanEOEnabled = isRRGProgram();
    const isOTConfigBasedOnTimeAttendance = getDealerPropertyValue(DEALER_PROPERTY_CONSTANTS.IS_INCHCAPE_PROGRAM);
    const makeEmployeeInformationTabFieldsNonMandatory = getDealerPropertyValue(
      DEALER_PROPERTY_CONSTANTS.EMPLOYEE_INFORMATION_NON_MANDATORY_FIELDS_ENABLED
    );
    const europeEOFieldModification = getDealerPropertyValue(
      DEALER_PROPERTY_CONSTANTS.EMPLOYEE_EUROPE_FIELD_MODIFICATIONS
    );
    const isEmployeeHoursOtDotEnabled = isEmployeeHoursOtDotV3Enabled();
    const permissions = this.context;
    const isCheckHqEnabled = featureFlags.getFlag(FEATURE_FLAGS.CHECK_HQ_ENABLED);
    const dateTimeFieldPlaceholder = getDateTimeFieldPlaceholder(getDateTimeFormatValue);
    const isExTekionPayroll = getDealerPropertyValue(DEALER_PROPERTY_CONSTANTS.EX_TEKION_PAYROLL);
    this.setState(
      {
        emailEditable: isEmailEditable(permissions),
        isCRMEnabled,
        isNewTeamsSetupEnabled,
        primaryPayrollFieldsEditable: arePayrollPrimaryFieldsEditable(permissions),
        displayEmployeeSensitiveInfo: shouldDisplayEmployeeSensitiveInfo(permissions),
        payrollApplicable,
        employeeDisabledRole,
        isEmployeeHoursOtDotEnabled,
        isCrmNewGtcSetupEnabled,
        europeanEOEnabled,
        makeEmployeeInformationTabFieldsNonMandatory,
        isOTConfigBasedOnTimeAttendance,
        europeEOFieldModification,
        isCheckHqEnabled,
        dateTimeFieldPlaceholder,
        isExTekionPayroll,
      },
      () => this.init()
    );
  }

  getSaveActionsHandler = saveActionType => {
    const { currentPage } = this.props;
    switch (saveActionType) {
      case SAVE_ACTIONS.SAVE:
        return this.additionalButtonActionHandler;
      case SAVE_ACTIONS.NEXT:
        return this.getPrimaryButtonActionHandlerForPayrollV2(currentPage);
      case SAVE_ACTIONS.TAB_SWITCH:
        return this.handleCancelModalSubmit;
      default:
        return this.additionalButtonActionHandler;
    }
  };

  init = () => {
    const {
      payrollApplicable,
      peopleId,
      isEmployeeHoursOtDotEnabled,
      europeanEOEnabled,
      isOTConfigBasedOnTimeAttendance,
    } = this.state;
    const { onAction, featureFlags } = this.props;

    onAction({
      type: ACTION_TYPES.INITIALIZE_METADATA,
      payload: {
        payrollApplicable,
        peopleId,
        initializeFunction: !peopleId ? this.updateInitValueForCreate : this.updateInitValueForEdit,
        isEmployeeHoursOtDotEnabled,
        europeanEOEnabled,
        isOTConfigBasedOnTimeAttendance,
        payrollSource: getPayrollSource(featureFlags),
      },
    });
  };

  updateInitValueForCreate = () => {
    const { otConfigurations, onAction, dealerInformation, taxParams, getDefaultCountryCode, multiTechRatesEnabled } =
      this.props;
    const { payrollApplicable, employeeDisabledRole } = this.state;
    const dealerStateCode = getDealerStateCode(dealerInformation);
    this.setState({
      initialValues: getFormInitiValueForCreate(
        dealerInformation,
        otConfigurations,
        payrollApplicable,
        dealerStateCode,
        employeeDisabledRole,
        taxParams,
        getDefaultCountryCode,
        multiTechRatesEnabled
      ),
      dealerStateCode,
      isOnboardingComplete: false,
    });
    onAction({
      type: ACTION_TYPES.ON_LOADING_COMPLETE,
    });
  };

  updateInitValueForEdit = () => {
    const { onAction, multiTechRatesEnabled, currentPage, payrollDeductionsId } = this.props;
    const { peopleId, payrollApplicable, europeanEOEnabled, isCheckHqEnabled } = this.state;
    this.setState({ fetchingOnboardingDetails: true });
    Promise.all([fetchPeopleInfoWithOTConfig(peopleId, payrollApplicable, isCheckHqEnabled), this.fetchNote(peopleId)])
      .then(async ([peopleInfo]) => {
        const {
          roleList,
          jobCodesAndRelatedEntities,
          otConfigurations,
          dealerInformation,
          payrollEnabledStateCodes,
          getDefaultCountryCode,
          departmentFilterOptions,
        } = this.props;
        let userInfo = EMPTY_OBJECT;
        const linkedUserId = _get(peopleInfo, LINKED_USER_ID);
        if (linkedUserId) {
          await Promise.all([getUserDealersAndRolesByIds([linkedUserId])])
            .then(([userInfoData]) => {
              userInfo = _head(userInfoData);
            })
            .catch(error => {
              toaster(TOASTER_TYPE.ERROR, getErrorMessage(error));
            });
        }
        const dealerStateCode = getDealerStateCode(dealerInformation);
        const tabsSwitchable = checkTabSwitchEnabledOnUpdate(payrollApplicable, peopleInfo);
        const initialValues = peopleManagementReverseMap({
          apiData: peopleInfo,
          roleList,
          otConfigurations,
          dealerConfig: dealerInformation,
          payrollApplicable,
          payrollEnabledStates: payrollEnabledStateCodes,
          getDefaultCountryCode,
          jobCodeAndEntities: jobCodesAndRelatedEntities,
          dealerStateCode,
          multiTechRatesEnabled,
          europeanEOEnabled,
          payrollDeductionsId,
          departmentFilterOptions,
        });
        this.setState({
          initialValues,
          mode: UPDATE,
          employeeAddress: initialValues[EMPLOYEE_ADDRESS],
          dealerStateCode,
          userInfo,
          updateMode: true,
          tabsSwitchable,
        });
        onAction({
          type: ACTION_TYPES.CHANGE_PAYROLL_TAXATION_ADDRESS,
          payload: {
            updatedValue: initialValues[PAYROLL_TAXATION_ADDRESS],
          },
        });
        if (payrollApplicable && !peopleInfo.createPayroll) {
          this.hideInformation();
          this.addEventListener();
        }
        this.callOnActionPassingActionType(ACTION_TYPES.ON_LOADING_COMPLETE)();
        if (payrollApplicable) {
          const payrollEmployee = initialValues[PAYROLL_EMPLOYEE];
          this.callOnActionPassingActionType(ACTION_TYPES.CHANGE_PAYROLL_EMPLOYEE, payrollEmployee)();
          if (isCheckHqEnabled && payrollEmployee) {
            this.fetchOnboardingDetails();
            this.setState({ wageDetails: initialValues[WAGE_DETAILS] });
          }
        }

        const {
          isCRMEnabled,
          isEmployeeHoursOtDotEnabled,
          isCrmNewGtcSetupEnabled,
          isNewTeamsSetupEnabled,
          displayEmployeeSensitiveInfo,
          isOTConfigBasedOnTimeAttendance,
        } = this.state;
        const { timeAndAttendanceConfig, hideInfo, isPayrollEmployee } = this.props;

        const fields = this.getFields(initialValues, EMPTY_OBJECT, onAction);
        const tabsToValidate = this.getAllTabs(payrollApplicable, isPayrollEmployee);

        if (!europeanEOEnabled) {
          const { initialErrors, updatedTabErrors } = await getErrorsOnUpdateFormMount({
            isOTConfigBasedOnTimeAttendance,
            timeAndAttendanceConfig,
            initialValues,
            isEmployeeHoursOtDotEnabled,
            otConfigurations,
            isCRMEnabled,
            isCrmNewGtcSetupEnabled,
            isNewTeamsSetupEnabled,
            displayEmployeeSensitiveInfo,
            hideInfo,
            payrollApplicable,
            dealerInformation,
            fields,
            tabsToValidate,
            isCheckHqEnabled,
            page: currentPage,
          });
          onAction({
            type: ACTION_TYPES.ADD_ERROR_FOR_TAB,
            payload: updatedTabErrors,
          });
          this.setState({ initialErrors });
        }
      })
      .catch(error => {
        this.setState({ errorOccured: true });
        toaster(TOASTER_TYPE.ERROR, getErrorMessage(error));
        this.callOnActionPassingActionType(ACTION_TYPES.ON_LOADING_COMPLETE)();
      })
      .finally(() => {
        this.setState({ fetchingOnboardingDetails: false });
      });
  };

  fetchNote = async id => {
    const { handleFetchNote: handleFetchNoteAction } = this.props;
    await handleFetchNoteAction(id);
  };

  callOnActionPassingActionType =
    (actionType, payload = EMPTY_OBJECT) =>
    () => {
      const { onAction } = this.props;
      onAction({
        type: actionType,
        payload,
      });
    };

  hideInformation = () => {
    const { hideInfo } = this.props;
    if (!hideInfo) this.handleFlipHideInfo();
  };

  startTimer = () => {
    this.timeoutId = window.setTimeout(this.hideInformation, TIMEOUT_SECONDS);
  };

  resetTimer = () => {
    window.clearTimeout(this.timeoutId);
    this.startTimer();
  };

  addEventListener = () => {
    document.addEventListener('mousemove', this.resetTimer);
    document.addEventListener('mousedown', this.resetTimer);
    this.startTimer();
  };

  scrollToTop = () => {
    if (this.formRef.current) this.formRef.current.scrollTo(0, 0);
  };

  resetLoadAndMoveToNextPage = () => {
    const { onAction, currentPage } = this.props;
    const { tabsSwitchable } = this.state;
    this.setState({ isPostingData: false });
    this.scrollToTop();
    onAction({ type: ACTION_TYPES.ON_NEXT, payload: { currentPage, tabsSwitchable } });
  };

  getSubmissionPage = () => {
    const { payrollApplicable } = this.state;
    const { isPayrollEmployee } = this.props;
    if (payrollApplicable && isPayrollEmployee) return ALL_PAGE.EMPLOYEE_TAX_DETAILS_V2;
    return ALL_PAGE.EMPLOYMENT_DETAILS;
  };

  onNext = (formValues = EMPTY_OBJECT) => {
    const { currentPage, multiTechRatesEnabled } = this.props;
    const {
      initialValues,
      isNewTeamsSetupEnabled,
      updateMode,
      isSaveClicked,
      isCheckHqEnabled,
      dealerStateCode,
      hasTabSwitched,
      hasCloseClicked,
    } = this.state;
    const selectRole = initialValues?.selectRole;
    const earnings = formValues[EARNING];
    if (currentPage === this.getSubmissionPage() || isSaveClicked) {
      if (
        !handleFormLoadAndBalanceWarningVisibilityhelper(
          { earnings, formValues, initialValues },
          this.handleFormLoadAndBalanceWarningVisibility
        )
      ) {
        if (
          handleSubmitConfirmationModal(
            { formValues, isNewTeamsSetupEnabled, updateMode, selectRole },
            this.handleFormLoadAndBalanceWarningVisibility
          )
        )
          this.handleSubmit(formValues);
      }
      return;
    }
    if (
      currentPage === ALL_PAGE.EMPLOYMENT_DETAILS &&
      multiTechRatesEnabled &&
      shouldShowHourlyWageWarning(formValues)
    ) {
      utils.displayWarningMessage(HOURLY_WAGE_MISSING);
    }
    if (isCheckHqEnabled && currentPage === ALL_PAGE.EMPLOYMENT_DETAILS) {
      const wageDetails = formValues[WAGE_DETAILS];
      const employeeWorkingState = getEmployeeWorkingState(formValues, dealerStateCode);
      this.setState({ wageDetails, employeeWorkingState });
    }

    if (isCheckHqEnabled && (hasTabSwitched || hasCloseClicked)) {
      this.handleSubmit(formValues);
      return;
    }
    this.resetLoadAndMoveToNextPage();
  };

  goToPrevPageHandler = () => {
    const { tabsSwitchable } = this.state;
    const { currentPage, onAction } = this.props;
    this.scrollToTop();
    onAction({
      type: ACTION_TYPES.ON_PREV,
      payload: { currentPage, tabsSwitchable },
    });
  };

  handleBankDetailsFormSubmit = () => {
    const { bankContextIds } = this.props;
    if (bankContextIds.length > 1) {
      bankContextIds.forEach(item => {
        if (item !== CONTEXT_ID) triggerSubmit(item);
      });
      return;
    }
    this.triggerFormSubmit();
  };

  handleBankDetailsSubmission = () => {
    this.handleBankDetailsFormSubmit();
  };

  tabsSwitchableHandler = handler => {
    const { currentPage, hideInfoError } = this.props;
    const { isCheckHqEnabled } = this.state;
    this.setState({ handler, isPostingData: true }, () => {
      if (currentPage === ALL_PAGE.EMPLOYEE_BANK_DETAILS) {
        this.setState({ hasUserClickedSave: false });
        this.handleBankDetailsSubmission();
        return;
      }
      if (currentPage !== ALL_PAGE.EMPLOYEE_INFORMATION) {
        if (currentPage === ALL_PAGE.EMPLOYMENT_DETAILS && !hideInfoError) this.hideInformation();
        this.triggerFormSubmit();
        return;
      }
      if (isCheckHqEnabled && currentPage === ALL_PAGE.EMPLOYEE_INFORMATION && !hideInfoError) this.hideInformation();
      triggerSubmit(ADDRESS_CONTEXT_ID);
      triggerSubmit(PAYROLL_TAXATION_ADDRESS_CONTEXT_ID);
      setTimeout(this.triggerFormSubmit);
    });
  };

  onPrev = () => {
    const { tabsSwitchable, isDirty, isCheckHqEnabled } = this.state;
    const { currentPage } = this.props;

    if (isCheckHqEnabled && isDirty && _includes(CORE_TABS, currentPage)) {
      this.setState({ showFormCancelConfirmationModal: true });
      return;
    }

    if (currentPage === ALL_PAGE.EMPLOYEE_INFORMATION) {
      this.navigateToListView();
      return;
    }
    if (tabsSwitchable) {
      this.tabsSwitchableHandler(this.goToPrevPageHandler);
    } else this.goToPrevPageHandler();
  };

  getValidName = peopleInfo => {
    const firstName = PeopleManagementReader.firstName(peopleInfo);
    const lastName = PeopleManagementReader.lastName(peopleInfo);
    if (firstName && lastName) return `${firstName} ${lastName}`;
    if (firstName) return firstName;
    return lastName;
  };

  getHeaderProps = () => ({
    hasBack: true,
    goBackTo: getCoreRoutesWithRoot(PEOPLE_MANAGEMENT_PATH),
    className: styles.pageHeader,
  });

  getHeaderTitle = () => {
    const { peopleId, initialValues } = this.state;
    if (peopleId) {
      const firstName = PeopleManagementReader.firstName(initialValues) || '';
      const lastName = PeopleManagementReader.lastName(initialValues) || '';
      return `${__('Employee')} - ${firstName} ${lastName}`;
    }
    return __('Create Employee');
  };

  linktoUser = () => {
    const { navigate } = this.props;
    const { initialValues } = this.state;
    const linkedUserId = _get(initialValues, LINKED_USER_ID);
    if (!linkedUserId) return;
    navigate(`/${CORE}${USER_LIST_BASE_ROUTE}/edit/${linkedUserId}`);
  };

  handleOnboardingErrorModalOpen = () => {
    this.setState({ showOnboardingErrorModal: true });
  };

  renderHeader = () => {
    const {
      initialValues,
      errorOccured,
      updateMode,
      isCheckHqEnabled,
      companyDefinedAttributes,
      payrollApplicable,
      isW4WithholdingLinkRequired,
    } = this.state;
    const { userList, roleList, navigate, location, payTypeOptions } = this.props;
    const linkedUserId = _get(initialValues, LINKED_USER_ID);
    const linkUserBtnLabel = isInchcapeProgram() ? __('User Details') : __('Link to User');
    const goBackHandler = isCheckHqEnabled && this.onCancel;
    return (
      <Page.Header hasBack className="full-width" goBackHandler={goBackHandler}>
        {!errorOccured && (
          <div className={`full-width ${styles.header}`}>
            <div className="flex align-items-center">
              <Heading size={1}>{this.getHeaderTitle()}</Heading>
              {this.renderOnboardingStatusIcon()}
            </div>
            <div className="flex align-items-center">
              {updateMode && isCheckHqEnabled && isW4WithholdingLinkRequired && (
                <Button
                  view={Button.VIEW.TERTIARY}
                  label={__('W4 Withholding Form')}
                  onClick={() => this.setState({ showW4WithholdingModal: true })}
                />
              )}
              {updateMode && companyDefinedAttributes && (
                <Button
                  view={Button.VIEW.TERTIARY}
                  label={__('Employee Setup')}
                  onClick={() => redirectToEmployeeSetup({ navigate, location })}
                />
              )}
              {updateMode && linkedUserId && initialValues[CREATE_USER] && (
                <Button
                  className={styles.linkUserBtn}
                  view={Button.VIEW.TERTIARY}
                  label={linkUserBtnLabel}
                  onClick={this.linktoUser}
                />
              )}
              {updateMode && (
                <NotesIcon
                  userList={userList}
                  employeeId={PeopleManagementReader.id(initialValues)}
                  className={styles.notesClass}
                  iconClass={styles.notes}
                  employeeName={this.getValidName(initialValues)}
                />
              )}
              {updateMode && (
                <AuditLogs
                  initialValues={initialValues}
                  roleList={roleList}
                  payrollApplicable={payrollApplicable}
                  isCheckHqEnabled={isCheckHqEnabled}
                  payTypeOptions={payTypeOptions}
                />
              )}
            </div>
          </div>
        )}
      </Page.Header>
    );
  };

  actionTabSelect = page => () => {
    const { tabsSwitchable } = this.state;
    const { onAction } = this.props;
    this.scrollToTop();
    onAction({
      type: ACTION_TYPES.TAB_SELECT,
      payload: {
        currentPage: page,
        tabsSwitchable,
      },
    });
  };

  handlePayStubsAndW2ConsentDrawerSubmit = payStubsW2ConsentFormValues => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.HANDLE_PAYSTUBS_W2_CONSENT_DRAWER_SUBMIT,
      payload: payStubsW2ConsentFormValues,
    });
  };

  selectTabOnClick = page => {
    const { tabsSwitchable, isDirty, isCheckHqEnabled } = this.state;
    const { currentPage } = this.props;
    if (isCheckHqEnabled && tabsSwitchable && isDirty && currentPage !== page) {
      this.setState({
        showFormCancelConfirmationModal: true,
        page,
        hasTabSwitched: true,
        inBetweenSave: true,
      });
      return;
    }

    if (
      tabsSwitchable &&
      !(
        currentPage === ALL_PAGE.EMPLOYEE_PAYROLL ||
        currentPage === ALL_PAGE.EMPLOYEE_BANK_DETAILS ||
        currentPage === ALL_PAGE.EMPLOYEE_TAX_DETAILS_V2
      )
    ) {
      if (currentPage === page) return;
      this.tabsSwitchableHandler(this.actionTabSelect(page));
    } else this.actionTabSelect(page)();
  };

  renderTabs = () => {
    const { payrollApplicable, tabsSwitchable, isCheckHqEnabled } = this.state;
    const { currentPage, tabErrors, tabsUtilized, isPayrollEmployee } = this.props;
    const tabs = this.getTabsWithStatus(
      this.getAllTabs(payrollApplicable, isPayrollEmployee, isCheckHqEnabled),
      currentPage,
      tabErrors,
      tabsUtilized,
      tabsSwitchable
    );
    return (
      <StatusTabs
        allTabs={tabs}
        onClick={this.selectTabOnClick}
        selectedTabId={getSelectedTabId(tabs)}
        containerClassName={styles.statusTabs}
        disableTabOnPending={!tabsSwitchable}
      />
    );
  };

  showSuccessToaster = (updateMode, formValues) => {
    const msg = updateMode ? __('updated') : __('created');
    toaster(
      TOASTER_TYPE.SUCCESS,
      __(`Employee '${formValues[FIRST_NAME]} ${formValues[LAST_NAME]}' details has been ${msg} successfully`)
    );
  };

  navigateToListView = () => {
    const { navigate, setNewEOLoaded: setNewEOLoadedAction } = this.props;
    _delay(() => {
      setNewEOLoadedAction();
      navigate(getCoreRoutesWithRoot(PEOPLE_MANAGEMENT_PATH));
    }, ES_REFETCH_DELAY);
  };

  handleNextButtonClick = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_NEXT,
    });
  };

  renderModalContent = () => {
    const { response } = this.state;
    const { email, password } = response;
    return (
      <div style={{ display: 'inline-block' }}>
        <FieldLabel
          labelClassName={styles.titleLabel}
          label={__('New user with the following details has been created:')}
        />
        <FieldLabel label={`${LOGIN_USER_CREATE} ${email}`} />
        <FieldLabel label={`${PASSWORD_USER_CREATE} ${password}`} />
      </div>
    );
  };

  closeModal = () => {
    const { updateMode, response, mode, peopleId, isCheckHqEnabled } = this.state;

    if (!updateMode) {
      if (!response.setPassKey)
        toaster(TOASTER_TYPE.ERROR, __('Unable to Set Parts Usercode. Please Edit and Try Again'));
      if (!response.setPasscode) toaster(TOASTER_TYPE.ERROR, __('Unable to Set Passcode. Please Edit and Try Again'));
    }
    this.setState({ showModal: false });

    if (isCheckHqEnabled && mode === CREATE && !_isEmpty(peopleId)) {
      this.init();
      this.tabsSwitchableHandler(this.resetLoadAndMoveToNextPage);
      return;
    }

    this.navigateToListView();
  };

  handleModal = () => {
    const { showModal } = this.state;
    return (
      <Modal
        visible={showModal}
        title={__('User Created')}
        content={this.renderModalContent()}
        hideSubmit
        onCancel={this.closeModal}
        width={Modal.SIZES.S}
        destroyOnClose
        secondaryBtnText={__('Close')}
      />
    );
  };

  renderSubmitModalContent = () => (
    <div>
      {__(
        'They will no longer be part of any older teams, and teams association will revert to the default for the new role.'
      )}
    </div>
  );

  handleSubmitModalClose = () => {
    this.setState({ showSubmitConfirmationModal: false });
  };

  handleSubmitModalSubmit = () => {
    const { formValues } = this.state;
    this.setState({ isPostingData: true });
    this.handleSubmit(formValues);
    this.setState({ showSubmitConfirmationModal: false });
  };

  renderSubmitConfirmationModal = () => {
    const { showSubmitConfirmationModal, isPostingData } = this.state;
    return (
      <Modal
        visible={showSubmitConfirmationModal}
        title={__('Warning: Teams can potentially be changed')}
        content={this.renderSubmitModalContent()}
        onSubmit={this.handleSubmitModalSubmit}
        onCancel={this.handleSubmitModalClose}
        submitBtnText={__('Submit')}
        width={Modal.SIZES.L}
        destroyOnClose
        loading={isPostingData}
      />
    );
  };

  handleInvalidAddressModalClose = () => {
    this.setState({
      isInvalidAddress: false,
    });
  };

  handleInvalidAddressModalSubmit = () => {
    this.setState({ updateSuggestedAddress: true, isInvalidAddress: false });
  };

  setUpdateSuggestedAddress = updateSuggestedAddress => {
    this.setState({ updateSuggestedAddress });
  };

  renderInvalidAddressModal = () => {
    const { isInvalidAddress, invalidAddressErrorMessage, suggestedAddress } = this.state;
    return (
      <InvalidAddressModal
        isVisible={isInvalidAddress}
        onCancel={this.handleInvalidAddressModalClose}
        onSubmit={this.handleInvalidAddressModalSubmit}
        invalidAddressErrorMessage={invalidAddressErrorMessage}
        suggestedAddress={suggestedAddress}
      />
    );
  };

  handleAddressValidationSuccess = saveActionType => validationDetails => {
    this.setState({ isPostingData: false, loading: false });
    const addressError = ValidationReader.error(validationDetails);
    if (_isEmpty(addressError)) {
      const saveHandler = this.getSaveActionsHandler(saveActionType);
      if (saveHandler) {
        saveHandler();
      }
    } else {
      const { message, suggestedAddress } = getAddressErrorDetails(validationDetails);

      this.setState({
        isInvalidAddress: true,
        showFormCancelConfirmationModal: false,
        invalidAddressErrorMessage: message,
        suggestedAddress,
      });
    }
  };

  handleAddressValidationFailure = e => {
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(e, __('Failed to validate address')));
    this.setState({ isPostingData: false });
  };

  validateAddress = saveActionType => () => {
    const { currentPage } = this.props;
    if (currentPage !== ALL_PAGE.EMPLOYEE_INFORMATION) {
      const saveHandler = this.getSaveActionsHandler(saveActionType);
      if (saveHandler) {
        saveHandler(currentPage);
      }
      return;
    }
    this.setState({ isPostingData: true, loading: true });
    const { employeeAddress } = this.state;
    const validateEmployeeDetailsRequestDto = getValidateEmployeeDetailsRequestDto(employeeAddress);
    validateEmployeeDetails(validateEmployeeDetailsRequestDto)
      .then(this.handleAddressValidationSuccess(saveActionType))
      .catch(this.handleAddressValidationFailure);
  };

  handleCancelModalSubmit = () => {
    const { mode } = this.state;
    const { currentPage } = this.props;
    if (mode === UPDATE) {
      this.setState({ loading: true });
      if (currentPage === ALL_PAGE.EMPLOYEE_PAYROLL) {
        triggerSubmit(PAYROLL_INFO_FORM_CONTEXT_ID);
        return;
      }
      this.handleBeforeSubmit({ inBetweenSaveConfirmationDone: true });
    }
    if (mode === CREATE && currentPage === ALL_PAGE.EMPLOYMENT_DETAILS) {
      this.setState({ isCancelClicked: mode === CREATE }, () => this.handleBeforeSubmit());
    }
  };

  handleCancelModalCancel = () => {
    const { hasTabSwitched, page } = this.state;
    const { onAction } = this.props;
    if (hasTabSwitched) {
      this.handleResetSsnError();
      setTimeout(this.hideInformation);
      this.setState(
        {
          showFormCancelConfirmationModal: false,
          hasTabSwitched: false,
          shouldSetInitialFormValue: true,
          isDirty: false,
        },
        this.actionTabSelect(page)
      );
      onAction({ type: ACTION_TYPES.SET_INITIAL_TAB_ERRORS });
      return;
    }
    this.navigateToListView();
  };

  updateIsDirty = isDirty => {
    this.setState({ isDirty });
  };

  setLoaded = () => {
    this.setState({
      loading: false,
      showFormCancelConfirmationModal: false,
      hasTabSwitched: false,
      hasCloseClicked: false,
    });
  };

  renderFormCancelConfirmationModal = () => {
    const { showFormCancelConfirmationModal, loading } = this.state;
    return (
      <Modal
        visible={showFormCancelConfirmationModal}
        title={__('Employee Data Not Saved')}
        content={__('Do you want to save your changes?')}
        onSubmit={this.validateAddress(SAVE_ACTIONS.TAB_SWITCH)}
        onCancel={this.handleCancelModalCancel}
        loading={loading}
        closable={false}
        maskClosable={false}
        submitBtnText={YES}
        secondaryBtnText={NO}
        width={Modal.SIZES.SM}
        destroyOnClose
      />
    );
  };

  handleModalCancel = () => {
    this.setState({ showOnboardingErrorModal: false });
  };

  handleSaveOnboardingDetails = onboardingDetails => {
    const { setInformationItems } = this.props;
    const onboardStatus = OnboardingDetailsReader.onboardStatus(onboardingDetails);
    const errorDetails = OnboardingDetailsReader.errorDetails(onboardingDetails);
    const companyDefinedAttributes = OnboardingDetailsReader.companyDefinedAttributes(onboardingDetails);
    const employeeCompanySetupIncomplete = OnboardingDetailsReader.employeeCompanySetupIncomplete(onboardingDetails);
    const isW4WithholdingLinkRequired = OnboardingDetailsReader.isW4WithholdingLinkRequired(onboardingDetails);
    const w4WithholdingDetails = OnboardingDetailsReader.w4WithholdingDetails(onboardingDetails);
    const warningMessages = getWarningMessages(employeeCompanySetupIncomplete);
    setInformationItems(warningMessages);

    this.setState({
      onboardStatus,
      errorDetails,
      companyDefinedAttributes,
      employeeCompanySetupIncomplete,
      warningMessages,
      isW4WithholdingLinkRequired,
      w4WithholdingDetails,
    });
  };

  setHasAddressChanged = hasAddressChanged => {
    this.setState({
      hasAddressChanged,
    });
  };

  updateShouldSetInitialFormValue = shouldSetInitialFormValue => {
    this.setState({ shouldSetInitialFormValue });
  };

  onSubmitAutoFillConfirmationModal = () => {
    const {
      autofillData,
      roleList,
      setFormState,
      formValuesBeforeAutofill,
      otConfigurations,
      dealerInformation,
      payrollEnabledStateCodes,
      getDefaultCountryCode,
    } = this.props;
    const { payrollApplicable } = this.state;
    const isPayrollEmployee = !!PeopleManagementReader.payrollEmployee(autofillData);
    setFormState({
      isPayrollEmployee,
      values: {
        ...formValuesBeforeAutofill,
        ..._omit(
          peopleManagementReverseMap({
            apiData: autofillData,
            managerOptions: EMPTY_OBJECT,
            roleList,
            otConfigurations,
            dealerConfig: dealerInformation,
            payrollApplicable,
            payrollEnabledStates: payrollEnabledStateCodes,
            getDefaultCountryCode,
          }),
          payrollApplicable ? [...OMIT_FIELDS, JOB_TITLE] : OMIT_FIELDS
        ),
      },
    });
    this.callOnActionPassingActionType(ACTION_TYPES.FLIP_AUTOFILL_MODAL)();
  };

  getAutofillFields = () => {
    const { dealersById } = this.props;
    return getFieldsForAutofill(dealersById);
  };

  renderAutofillModalContent = () => {
    const { payrollApplicable } = this.state;
    const {
      autofillData,
      roleList,
      otConfigurations,
      dealerInformation,
      payrollEnabledStateCodes,
      getDefaultCountryCode,
    } = this.props;
    const initialValues = getAutofillFormInitialValues(
      autofillData,
      roleList,
      otConfigurations,
      dealerInformation,
      payrollApplicable,
      payrollEnabledStateCodes,
      getDefaultCountryCode
    );
    return (
      <AutoFillFormWithState
        getSections={getSectionsForAutofill}
        getFields={this.getAutofillFields}
        initialValues={initialValues}
        contentHeight={AUTOFILL_FORM_HEIGHT}
        headerComponent={null}
        footerComponent={null}
      />
    );
  };

  renderAutoFillDetailModal = () => {
    const { showAutoFillConfirmationModal } = this.props;
    return (
      <Modal
        visible={showAutoFillConfirmationModal}
        title={__('Autofill Employee Details')}
        content={this.renderAutofillModalContent()}
        onCancel={this.callOnActionPassingActionType(ACTION_TYPES.FLIP_AUTOFILL_MODAL)}
        onSubmit={this.onSubmitAutoFillConfirmationModal}
        width={Modal.SIZES.S}
        submitBtnText={__('Autofill')}
        destroyOnClose
      />
    );
  };

  handleResetSsnError = () => {
    const { onAction } = this.props;
    // Reset SSN error on save when checkHQ is enabled
    onAction({
      type: ACTION_TYPES.SET_SSN_ERROR,
      payload: {
        hasError: false,
      },
    });
  };

  handlePeopleResponse = (formValues, { onboardStatus, errorDetails, ...response }) => {
    const {
      updateMode,
      initialValues,
      inBetweenSave,
      mode,
      isCheckHqEnabled,
      isCancelClicked,
      shouldSwitchNextTabOnSave,
      hasTabSwitched,
      hasCloseClicked,
      handler,
    } = this.state;
    const { createUser } = initialValues;
    const setInfoFlag = _get(formValues, 'setInfoFlag');
    const payrollEmployee = _get(formValues, 'payrollEmployee', false);
    if (payrollEmployee && isCheckHqEnabled) this.setState({ onboardStatus, errorDetails });
    if (updateMode) {
      if (!createUser && formValues[CREATE_USER] && setInfoFlag) {
        this.setState({ showModal: true, response });
        return;
      }
      this.showSuccessToaster(updateMode, formValues);
      if (isCheckHqEnabled && hasTabSwitched) {
        this.setState(
          { hasTabSwitched: false, showFormCancelConfirmationModal: false, loading: false, initialValues: formValues },
          handler
        );
        return;
      }
      if (isCheckHqEnabled && hasCloseClicked) {
        this.setState(
          { hasCloseClicked: false, showFormCancelConfirmationModal: false, loading: false, initialValues: formValues },
          this.navigateToListView
        );
        return;
      }
      if (isCheckHqEnabled && inBetweenSave && shouldSwitchNextTabOnSave) {
        this.setState({ inBetweenSave: false, initialValues: formValues }, () => {
          this.handleResetSsnError();
          this.tabsSwitchableHandler(this.resetLoadAndMoveToNextPage);
        });
        return;
      }
      this.navigateToListView();
      return;
    }

    if (!response.peopleCreated) {
      toaster(TOASTER_TYPE.ERROR, getErrorMessage(response.err, __('Unable to create employee. Please Try Again')));
      this.navigateToListView();
      return;
    }

    const shouldShowPasswordModal = formValues[CREATE_USER] && setInfoFlag && _get(response, 'password');

    if (isCheckHqEnabled && mode === CREATE && payrollEmployee) {
      if (shouldShowPasswordModal) {
        this.setState({ showModal: true, response, peopleId: response?.id });
        return;
      }
      if (isCancelClicked) {
        this.showSuccessToaster(updateMode, formValues);
        this.navigateToListView();
        return;
      }
      this.handleResetSsnError();
      this.setState({ peopleId: response?.id }, () => {
        this.init();
        this.tabsSwitchableHandler(this.resetLoadAndMoveToNextPage);
      });
      return;
    }

    if (shouldShowPasswordModal) {
      this.setState({ showModal: true, response });
      return;
    }

    this.showSuccessToaster(updateMode, formValues);
    this.navigateToListView();
  };

  handleSubmit = formValues => {
    const {
      initialValues,
      isCRMEnabled,
      payrollApplicable,
      updateMode,
      isEmployeeHoursOtDotEnabled,
      europeanEOEnabled,
      isSaveClicked,
      isCrmNewGtcSetupEnabled,
      isCheckHqEnabled,
    } = this.state;
    const {
      payrollDeductions,
      dealerInformation,
      taxParams,
      multiTechRatesEnabled,
      isPayrollAddressChanged = false,
      currentPage,
    } = this.props;
    const { createUser } = initialValues;
    const prenoteStatus = formValues[PRENOTE_STATUS];
    if (payrollApplicable) {
      if (multiTechRatesEnabled && shouldShowHourlyWageWarning(formValues)) {
        utils.displayWarningMessage(HOURLY_WAGE_MISSING);
      }
      if (shouldUpdateBankDetails(formValues[BANK_DETAILS], initialValues[BANK_DETAILS], prenoteStatus)) {
        utils.displayWarningMessage(DEPOSIT_DETAILS_UPDATED);
      }
    }
    if (isPayrollAddressChanged && isSaveClicked) {
      toaster(
        TOASTER_TYPE.WARN,
        __(
          'Employee address has been changed and Tax Details may need an update. Ensure to update the tax details and click Submit button'
        )
      );
      this.setState({ isSaveClicked: false, isPostingData: false });
      return;
    }
    const funcToExec = updateMode ? updatePeople : createPeople;
    const payload = getFormValuesMap(
      formValues,
      dealerInformation,
      isCRMEnabled,
      payrollApplicable,
      payrollDeductions,
      taxParams,
      isEmployeeHoursOtDotEnabled,
      multiTechRatesEnabled,
      europeanEOEnabled,
      isCrmNewGtcSetupEnabled
    );
    const isPayrollEmployee = _get(formValues, PAYROLL_EMPLOYEE);
    const taxDetailsFieldNames = _map(taxParams, 'paramId');
    const areTaxDetailsNotEqual = _some(
      taxDetailsFieldNames,
      fieldName => initialValues[fieldName] !== formValues[fieldName]
    );
    funcToExec({
      payload,
      payrollApplicable,
      isPayrollEmployee,
      createUser,
      multiTechRatesEnabled,
      isSaveClicked,
      areTaxDetailsNotEqual,
      isCheckHqEnabled,
    })
      .then(response => {
        this.setState(
          {
            isPostingData: false,
            isSaveClicked: false,
            isDirty: false,
          },
          this.handlePeopleResponse(formValues, response)
        );
      })
      .catch(e => {
        toaster(TOASTER_TYPE.ERROR, getErrorMessage(e));
        this.setState({
          isPostingData: false,
          isSaveClicked: false,
          hasTabSwitched: false,
          hasCloseClicked: false,
          showFormCancelConfirmationModal: false,
          loading: false,
        });
        if (isCheckHqEnabled && updateMode && currentPage === ALL_PAGE.EMPLOYEE_INFORMATION) {
          toaster(TOASTER_TYPE.ERROR, __('Unable to save data'));
        }
      });
  };

  getSections = (formValues, formErrors) => {
    const {
      initialValues,
      mode,
      isCRMEnabled,
      isNewTeamsSetupEnabled,
      payrollApplicable,
      displayEmployeeSensitiveInfo,
      isEmployeeHoursOtDotEnabled,
      dealerStateCode,
      isCrmNewGtcSetupEnabled,
      europeanEOEnabled,
      isOTConfigBasedOnTimeAttendance,
      europeEOFieldModification,
      isCheckHqEnabled,
      isExTekionPayroll,
    } = this.state;
    const { payrollSettings, dealerInformation, timeAndAttendanceConfig } = this.props;
    const showOverTimeSections = isOverTimeSectionVisible(isOTConfigBasedOnTimeAttendance, timeAndAttendanceConfig);
    const payrollEmployee = formValues[PAYROLL_EMPLOYEE];
    const { hideInfo, otConfigurations, taxParams, prenoteEnabled } = this.props;
    const arcAccess = _get(initialValues, 'createUser');
    const otEnabled = formValues[PAID_HOURLY_WAGE];
    const { currentPage } = this.props;
    const showCertificationNumberField = _get(formValues, SHOW_CERTIFICATION_NUMBER_FIELD);
    const currArcAccess = formValues[CREATE_USER] && !!formValues[EMAIL_ID] && !!formValues[INFO_DISPLAY];
    const weekStartDay = formValues[WEEK_START_DAY];
    const otConfigurationValue = formValues[OT_CONFIGURATION_TYPE];
    const jobCodeNotEmpty = !_isEmpty(formValues[JOB_CODE]);
    const w4EmployeeYear = formValues[W4_EMPLOYEE_YEAR];
    const createPayroll = formValues.createPayroll;
    const isDirectDeposit = formValues[PAYMENT_TYPE] === DIRECT_DEPOSIT.id;
    const employeeStateCode = _get(formValues, STATE_CODE);
    const flagHoursCalculationFlag = displayFlagHourCalculationCheckboxes(_head(_get(formValues, FLAG_HOURS)));
    const flsaStatus = formValues[FLSA_STATUS];
    const enableConsentConfiguration = _get(payrollSettings, ENABLE_CONSENT_CONFIGURATION, false);

    return europeanEOEnabled
      ? this.getRrgFormSections({
          currentPage,
          isCRMEnabled,
          isNewTeamsSetupEnabled,
          isCrmNewGtcSetupEnabled,
          formValues,
          arcAccess,
          mode,
        })
      : this.getFormSections({
          arcAccess,
          currentPage,
          mode,
          otEnabled,
          currArcAccess,
          showCertificationNumberField,
          weekStartDay,
          otConfigurationValue,
          otConfigurations,
          isCRMEnabled,
          isNewTeamsSetupEnabled,
          employeeStateCode,
          formValues,
          jobCodeNotEmpty,
          w4EmployeeYear,
          hideInfo,
          payrollApplicable,
          payrollEmployee,
          createPayroll,
          isDirectDeposit,
          taxParams,
          displayEmployeeSensitiveInfo,
          flagHoursCalculationFlag,
          prenoteEnabled,
          isEmployeeHoursOtDotEnabled,
          flsaStatus,
          dealerStateCode,
          isCrmNewGtcSetupEnabled,
          formErrors,
          enableConsentConfiguration,
          dealerInformation,
          showOverTimeSections,
          europeEOFieldModification,
          isCheckHqEnabled,
          isExTekionPayroll,
        });
  };

  triggerFormSubmit = () => {
    triggerSubmit(CONTEXT_ID);
  };

  handleBankDetailsFormSubmit = async () => {
    const { bankContextIds } = this.props;
    if (bankContextIds.length > 1) {
      bankContextIds.forEach(item => {
        if (item !== CONTEXT_ID) triggerSubmit(item);
      });
      return;
    }
    this.triggerFormSubmit();
  };

  canProceedToNextTab = () => {
    const { fetchingTaxParams } = this.props;
    const { isPostingData } = this.state;

    return !(isPostingData || fetchingTaxParams);
  };

  handleSubmitEmployeeSetupIncompleteModal = () => {
    const { currentPage } = this.props;
    this.setState({ loading: true });
    if (currentPage === ALL_PAGE.EMPLOYEE_PAYROLL) {
      triggerSubmit(PAYROLL_INFO_FORM_CONTEXT_ID);
      return;
    }
    this.setState({ employeeCompanySetupIncomplete: false }, () => this.handleBeforeSubmit());
  };

  handleBeforeSubmit = async (params = EMPTY_OBJECT) => {
    const { inBetweenSaveConfirmationDone = false } = params;
    const { tabsSwitchable, isSaveClicked, isDirty, mode, isCheckHqEnabled, isCancelClicked, hasTabSwitched, page } =
      this.state;
    const { currentPage } = this.props;
    const proceedToNextTab = this.canProceedToNextTab();
    if (
      proceedToNextTab &&
      isCheckHqEnabled &&
      isDirty &&
      mode === UPDATE &&
      _includes(CORE_TABS, currentPage) &&
      !inBetweenSaveConfirmationDone &&
      isCancelClicked
    ) {
      this.setState({ showFormCancelConfirmationModal: true, inBetweenSave: true });
      return;
    }

    if (isCheckHqEnabled && mode === CREATE && currentPage === ALL_PAGE.EMPLOYMENT_DETAILS)
      await this.setState({ isSaveClicked: true });

    if (proceedToNextTab) {
      this.setState({ isPostingData: true }, async () => {
        if (tabsSwitchable && !isSaveClicked) {
          if (hasTabSwitched) {
            this.tabsSwitchableHandler(this.actionTabSelect(page));
            return;
          }
          this.tabsSwitchableHandler(this.resetLoadAndMoveToNextPage);
        } else {
          if (currentPage === ALL_PAGE.EMPLOYEE_BANK_DETAILS) {
            this.setState({ hasUserClickedSave: true });
            this.handleBankDetailsFormSubmit();
            return;
          }
          if (currentPage === ALL_PAGE.EMPLOYEE_INFORMATION) {
            triggerSubmit(ADDRESS_CONTEXT_ID);
            triggerSubmit(PAYROLL_TAXATION_ADDRESS_CONTEXT_ID);
            setTimeout(this.triggerFormSubmit, 50);
            return;
          }
          this.triggerFormSubmit();
        }
      });
    }
  };

  additionalButtonActionHandler = () => {
    this.setState(
      {
        isSaveClicked: true,
        shouldSwitchNextTabOnSave: false,
      },
      () => this.handleBeforeSubmit()
    );
  };

  nextButtonActionHandler = () => {
    this.setState({ shouldSwitchNextTabOnSave: true }, () => this.handleBeforeSubmit());
  };

  saveAndNextButtonActionHandler = () => {
    const { isDirty, mode, initialErrors } = this.state;
    const hasFormErrors = _some(initialErrors, error => isFormHavingError(error));
    this.setState(
      {
        shouldSwitchNextTabOnSave: true,
        inBetweenSave: true,
        isSaveClicked: (isDirty || hasFormErrors) && mode === UPDATE,
      },
      () => this.handleBeforeSubmit()
    );
  };

  setAddress = employeeAddress => {
    this.setState({ employeeAddress });
  };

  onCancel = () => {
    const { isDirty } = this.state;
    if (isDirty) {
      this.setState({ showFormCancelConfirmationModal: true, hasCloseClicked: true });
      return;
    }
    this.navigateToListView();
  };

  getPrimaryButtonActionHandlerForPayrollV2 = currentPage => {
    switch (currentPage) {
      case ALL_PAGE.EMPLOYEE_INFORMATION:
        return this.nextButtonActionHandler;
      case ALL_PAGE.EMPLOYMENT_DETAILS:
        return this.saveAndNextButtonActionHandler;
      default:
        return this.nextButtonActionHandler;
    }
  };

  renderFooter = () => {
    const { currentPage, isPayrollEmployee } = this.props;
    const {
      payrollApplicable,
      updateMode,
      isSaveClicked,
      isPostingData,
      initialValues,
      isCheckHqEnabled,
      hasAddressChanged,
    } = this.state;
    const employeeInfoStatus = initialValues[EMPLOYEE_INFO_STATUS];
    const isPrimaryActionLoading = !this.canProceedToNextTab() && !isSaveClicked;
    const showAdditionalButton =
      employeeInfoStatus === STATUS.COMPLETE
        ? displayAdditionalButton(updateMode, isPayrollEmployee, currentPage, payrollApplicable, isCheckHqEnabled)
        : false;
    if (payrollApplicable && isCheckHqEnabled && isPayrollEmployee)
      return (
        <Page.Footer>
          <SaveComponent
            id={CONTEXT_ID}
            primaryButtonLabel={
              this.getFooterProps(payrollApplicable, isPayrollEmployee, isCheckHqEnabled)[currentPage]
            }
            onPrimaryAction={this.validateAddress(SAVE_ACTIONS.NEXT)}
            onSecondaryAction={this.validateAddress(SAVE_ACTIONS.SAVE)}
            primaryActionLoading={isPrimaryActionLoading || isPostingData}
            additionalButtonPosition={__('left')}
            additionalButtonLabel={getAdditionalButtonProps(CANCEL_BUTTON_LABEL_EOB)[currentPage]}
            showAdditionalButton={showAdditionalButton}
            onAdditionalAction={this.onCancel}
            secondaryActionLoading={isPostingData && !hasAddressChanged}
            secondaryButtonLabel={BUTTON_LABEL_EOB}
            isSecondaryDisabled={hasAddressChanged}
          />
        </Page.Footer>
      );
    return (
      <Page.Footer>
        <SaveComponent
          id={CONTEXT_ID}
          primaryButtonLabel={this.getFooterProps(payrollApplicable, isPayrollEmployee, isCheckHqEnabled)[currentPage]}
          onPrimaryAction={this.handleBeforeSubmit}
          onSecondaryAction={this.onPrev}
          primaryActionLoading={isPrimaryActionLoading}
          primaryActionView={showAdditionalButton ? Button.VIEW.SECONDARY : Button.VIEW.PRIMARY}
          additionalActionView={Button.VIEW.PRIMARY}
          additionalButtonPosition={__('left')}
          additionalButtonLabel={getAdditionalButtonProps(BUTTON_LABEL_EOB)[currentPage]}
          showAdditionalButton={showAdditionalButton}
          onAdditionalAction={this.additionalButtonActionHandler}
          additionalActionLoading={isPostingData}
        />
      </Page.Footer>
    );
  };

  handleFlipHideInfo = () => {
    const { hideInfo, hideInfoError } = this.props;
    if (hideInfo) {
      this.callOnActionPassingActionType(ACTION_TYPES.FLIP_PASSCODE_VERIFICATION_MODAL)();
    } else if (!hideInfoError) {
      this.callOnActionPassingActionType(ACTION_TYPES.FLIP_HIDE_UNHIDE_BUTTON)();
    }
  };

  togglePaystubsAndW2ConsentDrawer = () => {
    this.callOnActionPassingActionType(ACTION_TYPES.TOGGLE_CONSENT_CONFIGURATION_DRAWER)();
  };

  getFields = (formValues, formErrors, onAction) => {
    const {
      mode,
      emailEditable,
      userInfo,
      initialValues,
      payrollApplicable,
      isPostingData,
      tabsSwitchable,
      dealerStateCode,
      employeeDisabledRole,
      primaryPayrollFieldsEditable,
      isEmployeeHoursOtDotEnabled,
      europeanEOEnabled,
      makeEmployeeInformationTabFieldsNonMandatory,
      europeEOFieldModification,
      dateTimeFieldPlaceholder,
      hasUserClickedSave,
    } = this.state;
    const {
      currentPage,
      departmentFilterOptions,
      roleList,
      contentWidth,
      hideInfo,
      jobCodesAndRelatedEntities,
      payrollDeductions,
      payFrequencies,
      showPaymentTypeModal,
      payrollInfoFetched,
      serviceTypeOptions,
      laborRateOptions,
      eeocs,
      otConfigurations,
      stateCodesVsNames,
      taxParams,
      contributionsByDeductionId,
      showUnhideInfoModal,
      makeOptions,
      timeoffPlansByEarningId,
      multiTechRatesEnabled,
      frequencyOptions,
      contributions,
      jobCategories,
      retirementDeductionLimits,
      allEarnings,
      isConsentExpired,
      getCountOfPhoneNumberDigits,
      payTypeOptions,
      payrollSource,
      supportedStateCodes,
      payrollEnabledStatesDetails,
    } = this.props;
    const payrollEmployee = formValues[PAYROLL_EMPLOYEE];

    return this.getFormFields({
      currentPage,
      roleList,
      formValues,
      mode,
      emailEditable,
      departmentFilterOptions,
      userInfo,
      initialValues,
      otConfigurations,
      stateCodesVsNames,
      dealerStateCode,
      contentWidth,
      jobCodesAndRelatedEntities,
      hideInfo,
      handleFlipHideInfo: this.handleFlipHideInfo,
      payrollDeductions,
      formErrors,
      payrollApplicable,
      payrollEmployee,
      isPostingData,
      showPaymentTypeModal,
      callActionTypeFunc: this.callOnActionPassingActionType,
      payFrequencies,
      tabsSwitchable,
      payrollInfoFetched,
      employeeDisabledRole,
      eeocs,
      serviceTypeOptions,
      laborRateOptions,
      onAction,
      primaryPayrollFieldsEditable,
      taxParams,
      contributionsByDeductionId,
      showUnhideInfoModal,
      makeOptions,
      timeoffPlansByEarningId,
      isEmployeeHoursOtDotEnabled,
      multiTechRatesEnabled,
      frequencyOptions,
      contributions,
      jobCategories,
      europeanEOEnabled,
      retirementDeductionLimits,
      makeEmployeeInformationTabFieldsNonMandatory,
      allEarnings,
      togglePaystubsAndW2ConsentDrawer: this.togglePaystubsAndW2ConsentDrawer,
      isConsentExpired,
      getCountOfPhoneNumberDigits,
      payTypeOptions,
      europeEOFieldModification,
      payrollSource,
      supportedStateCodes,
      payrollEnabledStatesDetails,
      employeeId: PeopleManagementReader.passCodeEdit(initialValues),
      dateTimeFieldPlaceholder,
      hasUserClickedSave,
    });
  };

  notifyErrors = errors => {
    const { currentPage, onAction } = this.props;
    const { isPostingData, tabsSwitchable, handler, isSaveClicked, hasTabSwitched, hasCloseClicked } = this.state;
    const ssnError = _get(errors, SSN);
    onAction({
      type: ACTION_TYPES.SET_SSN_ERROR,
      payload: {
        hasError: !!ssnError,
      },
    });
    if (!isPostingData) return false;
    if (
      !tabsSwitchable ||
      (tabsSwitchable && currentPage === this.getSubmissionPage() && handler === this.resetLoadAndMoveToNextPage) ||
      isSaveClicked ||
      hasTabSwitched ||
      hasCloseClicked
    ) {
      const isValid = _some(errors, error => isFormHavingError(error));
      if (isValid) {
        toaster(TOASTER_TYPE.ERROR, __('Please correct form errors'));
        this.setState({ isSaveClicked: false });
      }
      return this.setState({
        isPostingData: false,
        loading: false,
        showFormCancelConfirmationModal: false,
        hasTabSwitched: false,
        hasCloseClicked: false,
      });
    }
    return this.setState({ isPostingData: false, isSaveClicked: false }, handler);
  };

  tabSwitchSubmit = formValues => {
    const { handler, hasTabSwitched, hasCloseClicked } = this.state;
    if (handler === this.resetLoadAndMoveToNextPage || hasTabSwitched || hasCloseClicked) {
      return this.onNext(formValues);
    }
    return this.setState({ isPostingData: false }, handler);
  };

  renderTaxDetailsOverride = () => {
    const { fetchingTaxParams, taxationAddress } = this.props;
    if (_isEmpty(taxationAddress)) return __('Please enter address details first.');

    if (fetchingTaxParams) return <Loader />;

    return null;
  };

  handleFormLoadAndBalanceWarningVisibility = ({
    showCurrentBalanceWarning = false,
    isPostingData = false,
    formValues = EMPTY_OBJECT,
    showSubmitConfirmationModal = false,
  }) => {
    this.setState({ showCurrentBalanceWarning, isPostingData, formValues, showSubmitConfirmationModal });
  };

  hideW4WithholdingModal = () => {
    this.setState({ showW4WithholdingModal: false });
  };

  setTabUtilized = tabUtilized => {
    const { onAction } = this.props;
    const { tabsSwitchable } = this.state;

    onAction({
      type: ACTION_TYPES.SET_TAB_UTILIZED,
      payload: {
        tabUtilized,
        tabsSwitchable,
      },
    });
  };

  setEmployeePayrollTabError = hasError => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ADD_ERROR_FOR_TAB,
      payload: { [ALL_PAGE.EMPLOYEE_PAYROLL]: hasError },
    });
  };

  getFormHeight = ({ contentHeight, getAdjustedContentHeight, isCheckHqEnabled }) =>
    isCheckHqEnabled ? getAdjustedContentHeight(contentHeight) : contentHeight;

  fetchOnboardingDetails() {
    const { peopleId } = this.state;
    if (!peopleId) return Promise.reject();
    return fetchEmployeeOnboardingDetailsById(peopleId)
      .then(getDataFromResponse)
      .then(this.handleSaveOnboardingDetails)
      .catch(err => toastErrorMessage(err, __('Failed to fetch Onboarding Status details')));
  }

  renderOnboardingStatusIcon() {
    const { isPayrollEmployee } = this.props;
    const { onboardStatus, payrollApplicable, isCheckHqEnabled, updateMode } = this.state;
    if (!isPayrollEmployee || !updateMode || !payrollApplicable || !isCheckHqEnabled || !onboardStatus) return null;
    return (
      <IconAsBtn
        className={getOnboardingStatusIconClassname(onboardStatus)}
        onClick={this.handleOnboardingErrorModalOpen}>
        {getOnboardingStatusIcon(onboardStatus)}
      </IconAsBtn>
    );
  }

  renderTabOverride() {
    const { currentPage } = this.props;
    const tabOverrideRenderer = this.tabOverrideRenderers[currentPage] || _noop;

    return tabOverrideRenderer();
  }

  renderOnboardingErrorModal() {
    const { showOnboardingErrorModal, peopleId, errorDetails, onboardStatus } = this.state;
    return (
      <OnboardingErrorModal
        visible={showOnboardingErrorModal}
        onCancel={this.handleModalCancel}
        employeeId={peopleId}
        errorDetails={errorDetails}
        onboardStatus={onboardStatus}
        handleSaveOnboardingDetails={this.handleSaveOnboardingDetails}
      />
    );
  }

  renderAppBody = () => {
    const {
      initialValues,
      isPostingData,
      mode,
      payrollApplicable,
      tabsSwitchable,
      isSaveClicked,
      initialErrors,
      peopleId,
      isCheckHqEnabled,
      wageDetails,
      employeeWorkingState,
      hasAddressChanged,
      page,
      hasTabSwitched,
      shouldSetInitialFormValue,
      hasCloseClicked,
      isOnboardingComplete,
      warningMessages,
      updateSuggestedAddress,
      suggestedAddress,
    } = this.state;
    const {
      contentHeight,
      onAction,
      currentPage,
      setInformationItems,
      getAdjustedContentHeight,
      payrollSettings,
      ...rest
    } = this.props;
    const PayrollTabComponent = CHECK_HQ_COMPONENT_BY_TAB_ID[currentPage];
    if (isCheckHqEnabled && PayrollTabComponent) {
      const fetchOnboardingDetails = this.fetchOnboardingDetails.bind(this);
      return (
        <PayrollTabComponent
          contentHeight={contentHeight - TABS_HEIGHT}
          onAction={onAction}
          navigateToListView={this.navigateToListView}
          employeeId={peopleId}
          setInformationItems={setInformationItems}
          getAdjustedContentHeight={getAdjustedContentHeight}
          wageDetails={wageDetails}
          payrollSettings={payrollSettings}
          employeeWorkingState={employeeWorkingState}
          hasAddressChanged={hasAddressChanged}
          updateIsDirty={this.updateIsDirty}
          currentPage={page}
          onCancel={this.onCancel}
          hasTabSwitched={hasTabSwitched}
          hasCloseClicked={hasCloseClicked}
          setLoaded={this.setLoaded}
          isOnboardingComplete={isOnboardingComplete}
          fetchOnboardingDetails={fetchOnboardingDetails}
          handleNextButtonClick={this.handleNextButtonClick}
          setTabUtilized={this.setTabUtilized}
          setEmployeePayrollTabError={this.setEmployeePayrollTabError}
          warningMessages={warningMessages}
        />
      );
    }
    const submitFunc = tabsSwitchable && !isSaveClicked ? this.tabSwitchSubmit : this.onNext;

    const tabOverride = this.renderTabOverride();

    const tabContentHeight = tabOverride
      ? 0
      : this.getFormHeight({ contentHeight, getAdjustedContentHeight, isCheckHqEnabled }) - TABS_HEIGHT;
    return (
      <>
        {tabOverride}
        <FormWithState
          headerComponent={null}
          initialValues={initialValues}
          initialErrors={initialErrors}
          contextId={CONTEXT_ID}
          footerComponent={null}
          getSections={this.getSections}
          getFields={this.getFields}
          mode={mode}
          onSubmit={submitFunc}
          formConfigDependantKeys={FORM_CONFIG_DEPENDENT_KEYS}
          formSectionDependantKeys={FORM_CONFIG_SECTION_DEPENDENT_KEYS}
          onClose={this.navigateToListView}
          notifyErrors={this.notifyErrors}
          mapFieldToOnChange={FIELD_ACTION_HANDLERS}
          handleAction={onAction}
          contentHeight={tabContentHeight}
          isLoading={isPostingData}
          bodyProps={{ bodyRef: this.formRef }}
          setHasAddressChanged={this.setHasAddressChanged}
          payrollApplicable={payrollApplicable}
          tabsSwitchable={tabsSwitchable}
          currentPage={currentPage}
          updateIsDirty={this.updateIsDirty}
          shouldSetInitialFormValue={shouldSetInitialFormValue}
          updateShouldSetInitialFormValue={this.updateShouldSetInitialFormValue}
          isCheckHqEnabled={isCheckHqEnabled}
          payrollSettings={payrollSettings}
          additionalActionHandlers={ADDITIONAL_ACTION_HANDLERS}
          setAddress={this.setAddress}
          updateSuggestedAddress={updateSuggestedAddress}
          suggestedAddress={suggestedAddress}
          setUpdateSuggestedAddress={this.setUpdateSuggestedAddress}
          {...rest}
        />
      </>
    );
  };

  renderW4WithholdingModal = () => {
    const { showW4WithholdingModal, w4WithholdingDetails } = this.state;
    return (
      <W4WithholdingModal
        showW4WithholdingModal={showW4WithholdingModal}
        hideW4WithholdingModal={this.hideW4WithholdingModal}
        w4WithholdingDetails={w4WithholdingDetails}
      />
    );
  };

  renderCurrentBalanceWarningModal = () => {
    const {
      showCurrentBalanceWarning,
      formValues = EMPTY_OBJECT,
      initialValues: { selectRole },
      isNewTeamsSetupEnabled,
      updateMode,
    } = this.state;
    return (
      <CurrentBalanceWarningModal
        showCurrentBalanceWarning={showCurrentBalanceWarning}
        handleFormLoadAndBalanceWarningVisibility={this.handleFormLoadAndBalanceWarningVisibility}
        handleSubmit={this.handleSubmit}
        formValues={formValues}
        isNewTeamsSetupEnabled={isNewTeamsSetupEnabled}
        updateMode={updateMode}
        selectRole={selectRole}
      />
    );
  };

  render() {
    const {
      errorOccured,
      updateMode,
      isNewTeamsSetupEnabled,
      initialValues,
      isCheckHqEnabled,
      fetchingOnboardingDetails,
      payrollApplicable,
    } = this.state;
    const {
      isLoading,
      contentHeight,
      currentPage,
      payrollSettings,
      consentConfigurationData,
      showPayrollstubsAndW2ConsentDrawer,
      showConsentExpiredBanner,
      isSubmittingConsentData,
      getAdjustedContentHeight,
    } = this.props;
    if (!isLoading && !fetchingOnboardingDetails) {
      const enableConsentConfiguration = _get(payrollSettings, ENABLE_CONSENT_CONFIGURATION, false);
      const isActiveEmployee = _get(initialValues, EMPLOYEE_STATUS, false);
      const defaultConsentConfiguration = _get(payrollSettings, CONSENT_CONFIGURATION, EMPTY_OBJECT);
      return (
        <Page>
          {currentPage === ALL_PAGE.EMPLOYEE_PAYROLL && isActiveEmployee && showConsentExpiredBanner && (
            <NotificationLabel {...NOTIFICATION_LABEL_PROPS} />
          )}
          {this.renderHeader()}
          {!errorOccured && (
            <Page.Body
              style={{ height: this.getFormHeight({ contentHeight, getAdjustedContentHeight, isCheckHqEnabled }) }}>
              {this.renderTabs()}
              {this.renderAppBody()}
            </Page.Body>
          )}
          {shouldRenderFooter({ currentPage, isCheckHqEnabled, errorOccured }) && this.renderFooter()}
          {this.handleModal()}
          {this.renderCurrentBalanceWarningModal()}
          {payrollApplicable && isCheckHqEnabled && this.renderOnboardingErrorModal()}
          {isNewTeamsSetupEnabled && this.renderSubmitConfirmationModal()}
          {isCheckHqEnabled && this.renderFormCancelConfirmationModal()}
          {isCheckHqEnabled && this.renderW4WithholdingModal()}
          {isCheckHqEnabled && this.renderInvalidAddressModal()}
          {!updateMode && this.renderAutoFillDetailModal()}
          {enableConsentConfiguration && showPayrollstubsAndW2ConsentDrawer && (
            <PayStubsAndW2ConsentDrawer
              visible={showPayrollstubsAndW2ConsentDrawer}
              onCancel={this.togglePaystubsAndW2ConsentDrawer}
              onSave={this.handlePayStubsAndW2ConsentDrawerSubmit}
              payStubsW2ConsentData={consentConfigurationData}
              isSubmittingConsentData={isSubmittingConsentData}
              defaultConsentConfiguration={defaultConsentConfiguration}
            />
          )}
        </Page>
      );
    }

    return <Loader />;
  }
}

PeopleManagementForm.contextType = PermissionContext;

PeopleManagementForm.propTypes = {
  currentPage: PropTypes.string.isRequired,
  departmentFilterOptions: PropTypes.object.isRequired,
  isLoading: PropTypes.bool,
  roleList: PropTypes.array.isRequired,
  userList: PropTypes.object.isRequired,
  onAction: PropTypes.func,
  contentHeight: PropTypes.number.isRequired,
  getDealerPropertyValue: PropTypes.func.isRequired,
  autofillData: PropTypes.object,
  setFormState: PropTypes.func,
  formValuesBeforeAutofill: PropTypes.object,
  hideInfo: PropTypes.bool.isRequired,
  showUnhideInfoModal: PropTypes.bool.isRequired,
  bankContextIds: PropTypes.array.isRequired,
  hideInfoError: PropTypes.bool.isRequired,
  contentWidth: PropTypes.number.isRequired,
  payrollDeductions: PropTypes.array,
  allEarnings: PropTypes.array,
  jobCodesAndRelatedEntities: PropTypes.array,
  payrollInfoFetched: PropTypes.bool.isRequired,
  showPaymentTypeModal: PropTypes.bool.isRequired,
  payFrequencies: PropTypes.array.isRequired,
  tabErrors: PropTypes.object.isRequired,
  tabsUtilized: PropTypes.object.isRequired,
  fetchEmployeePayrollMetadataAction: PropTypes.func.isRequired,
  eeocs: PropTypes.array,
  jobCategories: PropTypes.array,
  serviceTypeOptions: PropTypes.array,
  laborRateOptions: PropTypes.array,
  getOtConfigurationsForDealer: PropTypes.func.isRequired,
  otConfigurations: PropTypes.array,
  stateCodesVsNames: PropTypes.object,
  getPayrollEnabledStates: PropTypes.func.isRequired,
  payrollEnabledStateCodes: PropTypes.object,
  getAllStates: PropTypes.func.isRequired,
  dealerInformation: PropTypes.object,
  payrollSettings: PropTypes.object,
  taxParams: PropTypes.array,
  makeOptions: PropTypes.array,
  isPayrollEmployee: PropTypes.bool.isRequired,
  prenoteEnabled: PropTypes.bool.isRequired,
  fetchingTaxParams: PropTypes.bool,
  isConsentExpired: PropTypes.bool,
  showConsentExpiredBanner: PropTypes.bool,
  showPayrollstubsAndW2ConsentDrawer: PropTypes.bool,
  isSubmittingConsentData: PropTypes.bool,
  taxationAddress: PropTypes.object,
  consentConfigurationData: PropTypes.object,
  getCountOfPhoneNumberDigits: PropTypes.func,
  getDefaultCountryCode: PropTypes.func,
  payTypeOptions: PropTypes.array,
  timeAndAttendanceConfig: PropTypes.object,
  featureFlags: PropTypes.instanceOf(FeatureFlags).isRequired,
  setInformationItems: PropTypes.func,
  getAdjustedContentHeight: PropTypes.func,
  payrollDeductionsByName: PropTypes.object,
  payrollDeductionsId: PropTypes.object,
  payrollSource: PropTypes.string,
  supportedStateCodes: PropTypes.array,
  payrollEnabledStatesDetails: PropTypes.object,
  getDateTimeFormatValue: PropTypes.func,
  navigate: PropTypes.func.isRequired,
  location: PropTypes.object.isRequired,
  params: PropTypes.object.isRequired,
};

PeopleManagementForm.defaultProps = {
  isLoading: true,
  onAction: _noop,
  autofillData: EMPTY_OBJECT,
  setFormState: _noop,
  formValuesBeforeAutofill: EMPTY_OBJECT,
  payrollSettings: EMPTY_OBJECT,
  payrollDeductions: EMPTY_ARRAY,
  allEarnings: EMPTY_ARRAY,
  jobCodesAndRelatedEntities: EMPTY_ARRAY,
  eeocs: EMPTY_ARRAY,
  jobCategories: EMPTY_ARRAY,
  serviceTypeOptions: EMPTY_ARRAY,
  laborRateOptions: EMPTY_ARRAY,
  otConfigurations: EMPTY_ARRAY,
  stateCodesVsNames: EMPTY_OBJECT,
  payrollEnabledStateCodes: EMPTY_OBJECT,
  dealerInformation: EMPTY_OBJECT,
  consentConfigurationData: EMPTY_OBJECT,
  taxParams: EMPTY_ARRAY,
  makeOptions: EMPTY_ARRAY,
  fetchingTaxParams: false,
  isConsentExpired: false,
  showConsentExpiredBanner: false,
  showPayrollstubsAndW2ConsentDrawer: false,
  isSubmittingConsentData: false,
  taxationAddress: undefined,
  getCountOfPhoneNumberDigits: _noop,
  getDefaultCountryCode: _noop,
  payTypeOptions: EMPTY_ARRAY,
  timeAndAttendanceConfig: EMPTY_OBJECT,
  setInformationItems: _noop,
  getAdjustedContentHeight: _noop,
  payrollDeductionsByName: EMPTY_OBJECT,
  payrollDeductionsId: EMPTY_OBJECT,
  payrollSource: PAYROLL_SOURCE.TEKION,
  supportedStateCodes: EMPTY_ARRAY,
  payrollEnabledStatesDetails: EMPTY_OBJECT,
  getDateTimeFormatValue: _noop,
};

const mapStateToProps = globalState => {
  const { [CORE]: state } = globalState;
  const payrollSettings = getPayrollSettingsForPrenote(state);
  const payrollEnabledStatesDetails = getPayrollEnabledStateCodes(state);
  const payrollEnabledStates = makePayrollEnabledStates(payrollEnabledStatesDetails);
  const activeContributions = getAllActiveItems(getAllContributions(state));
  const allDeductions = getPayrollDeductions(state);
  const payrollDeductions = getAllActiveItems(allDeductions);
  const allEarnings = getPayrollEarnings(state);
  return {
    departmentFilterOptions: getDepartmentOptions(state),
    roleList: roleMapper(getNonInternalRolesList(getUnhiddenRolesList(getLocalisedActiveRoles(state)))),
    shouldFetchListAPI: getNewEOLoadedFlag(state),
    userList: getUsersList(state),
    dealersById: getDealersById(state),
    jobCodesAndRelatedEntities: getPayrollJobCodeEntities(state),
    allDeductions,
    payrollDeductions,
    payrollDeductionsByName: _keyBy(payrollDeductions, 'name'),
    payrollDeductionsId: _keyBy(payrollDeductions, 'id'),
    allEarnings,
    payFrequencies: getPayrollPayFrequencies(state),
    eeocs: getPayrollEeocs(state),
    serviceTypeOptions: getFormattedOptions(getServiceTypeOptions(state)),
    laborRateOptions: getFormattedOptions(getLaborRateOptions(state)),
    otConfigurations: getOtConfigurations(state),
    stateCodesVsNames: getStateCodesVsNames(state),
    payrollEnabledStateCodes: payrollEnabledStates,
    contributions: activeContributions,
    contributionsByDeductionId: getContributionsByDeductionId(activeContributions),
    makeOptions: getMakeOptions(state),
    payrollSettings,
    prenoteEnabled: !!_get(payrollSettings, 'prenoteEnabled'),
    multiTechRatesEnabled: !!_get(payrollSettings, 'multiTechRatesEnabled'),
    timeoffPlansByEarningId: getTimeOffById(state),
    frequencyOptions: getFreqOptions(state),
    supportedStateCodes: getSupportedStateCodes(state),
    payrollEnabledStatesDetails,
    jobCategories: getJobCategories(state),
    sitesByDealerId: getSitesByDealerIds(state),
    retirementDeductionLimits: getRetirementDeductionFederalLimit(state),
    payTypeOptions: getPayTypeOptions(state),
    allEarningsV2: getEarnings(globalState),
    wcTypes: getWCTypes(globalState),
    paySchedules: getPaySchedules(state),
    payrollMetadata: getPayrollMetadata(globalState),
  };
};

const mapDispatchToProps = {
  setNewEOLoaded,
  handleFetchNote: handleFetchNote(NOTES_ASSET_TYPES.EMPLOYEE),
  fetchPayrollDeductionsAction,
  fetchJobCodesAndRelatedEntitiesAction,
  fetchPayrollPayFrequenciesAction,
  fetchEmployeePayrollMetadataAction,
  fetchServiceTypeOptions,
  fetchLaborRateOptions,
  fetchDepartmentFilterOptions,
  fetchDealerOptionsAction,
  getOtConfigurationsForDealer,
  getAllStates,
  getPayrollEnabledStates,
  fetchLocalisedRoles,
  fetchPayrollContributionsAction,
  fetchMakeOptions,
  fetchPayrollSettingsAction,
  fetchPayrollSettingsV2Action,
  fetchTimeOffPlansAction,
  fetchSitesByDealerIds,
  fetchPayrollActiveEarnings,
  fetchPayTypeOptions,
  fetchEmployeeMetadataAction,
  fetchEarnings,
  fetchWCTypes,
  fetchPaySchedules,
  fetchPayrollMetadata,
};

export default compose(
  withEmployeeAndUserEditEnabled,
  connect(mapStateToProps, mapDispatchToProps),
  withFeatureFlags,
  withActions(INITIAL_STATE, TAB_ACTION_HANDLERS),
  withSize({ hasPageHeader: 1, hasPageFooter: 1 }),
  withTekionConversion,
  withInformationBanner()
)(PeopleManagementForm);
