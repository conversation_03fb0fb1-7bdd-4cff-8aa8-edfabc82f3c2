import { useNavigate } from 'react-router-dom';
import React, { memo, useCallback, useMemo, useEffect, useContext } from 'react';
import PropTypes from 'prop-types';
import compose from 'recompose/compose';

import _pick from 'lodash/pick';
import _size from 'lodash/size';
import _noop from 'lodash/noop';
import _map from 'lodash/map';
import _isNil from 'lodash/isNil';
import _get from 'lodash/get';
import _isFunction from 'lodash/isFunction';
import _identity from 'lodash/identity';

import { EMPTY_ARRAY, EMPTY_OBJECT, NO_DATA } from '@tekion/tekion-base/app.constants';
import { isInchcape } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';

import Page from '@tekion/tekion-components/src/molecules/pageComponent/PageComponent';
import Popover from '@tekion/tekion-components/src/molecules/popover';
import { POPOVER_PLACEMENT } from '@tekion/tekion-components/src/molecules/popover/popover.constants';
import Heading from '@tekion/tekion-components/src/atoms/Heading';
import FontIcon from '@tekion/tekion-components/src/atoms/FontIcon';
import Content from '@tekion/tekion-components/src/atoms/Content';
import { DEFAULT_FILTER_GROUP } from '@tekion/tekion-components/src/organisms/filterSection';
import withCustomSortTable from '@tekion/tekion-components/src/organisms/withCustomSortTable';
import FixedColumnTable from '@tekion/tekion-components/src/molecules/table/FixedColumnTable';
import TableManager, { TABLE_MANAGER_DEFAULT_PROPS } from '@tekion/tekion-widgets/src/organisms/tableManager';
import DropdownSummary from '@tekion/tekion-components/src/molecules/DropdownSummary';
import { useTekionConversion } from '@tekion/tekion-conversion-web';
import {
  DEFAULT_RESULTS_PER_PAGE,
  DEFAULT_RESULTS_PER_PAGE_OPTIONS,
} from '@tekion/tekion-components/src/molecules/table/constants/table.constants';
import Skeleton from '@tekion/tekion-components/src/molecules/skeleton';
import TekionHelmet from '@tekion/tekion-components/src/molecules/TekionHelmet';
import { PropertyControlledComponent } from '@tekion/tekion-components/src/molecules';

import QuickFilters from '@tekion/tekion-widgets/src/organisms/QuickFilters';
import { useSubAppAnalytics } from '@tekion/tekion-widgets/src/utils/analytics/hooks';
import AmountInputFormatContext from '@tekion/tekion-widgets/src/appServices/parts/context/amountInputFormatContext';

import withCoreEnv from 'hoc/coreEnv/withCoreEnv';

import { CORE_PARTS_REPORT_PATH } from 'pages/Reports/constants/departmentTypes';
import WarehouseSingleSelectionRenderer from 'organisms/WarehouseSingleSelectionRenderer';
import { getIsMultiWarehouseEnabled } from 'utils/general';
import { WAREHOUSE_ACTION_TYPE } from 'helpers/warehouse.helper';

import { getPartsAnalyticsAdditionalPayload } from '../partsReport.helpers';
import {
  TABLE_MANAGER_PROPS,
  TABLE_ROW_HEIGHT,
  getModuleStyle,
  SKELETON_TITLE_STYLE,
} from './detailedExtendedValue.constants';
import { getSubHeaderProps, getAggregatedDetailsConfig, getQuickFilterOptions } from './detailedExtendedValue.config';
import { getFilterProps } from './detailedExtendedValue.filters';
import DetailedExtendedValuePushListener from './DetailedExtendedValuePushListener';
import styles from './detailedExtendedValue.module.scss';
import DETAILED_EXTENDED_VALUE_ACTION_TYPE from './detailedExtendedValue.actionTypes';

const SortableTableManager = withCustomSortTable(TableManager);

const SUB_MENU_TITLE_PROPS = {
  titleWrapperClassName: 'p-r-16',
};
function DetailedExtendedValue(props) {
  const navigate = useNavigate();
  const {
    rows,
    columns,
    location,
    onAction,
    totalCount,
    currentPage,
    sortDetails,
    searchQuery,
    contentHeight,
    sourceCodesById,
    selectedFilters,
    columnConfigurator,
    selectedFilterGroup,
    detailedExtendedValueList,
    integratedBrands,
    additional,
    resolvedData,
    isQuickFilterSelection,
    isGeneratingReport, // used for showing the loader while UI is waiting for the notification of Report computation completion from backend
    loading,
    partsGeneralSettings,
    selectedWarehouse,
  } = props;

  useSubAppAnalytics({
    isLoading: loading,
    ...getPartsAnalyticsAdditionalPayload('table'),
  });

  const { getFormattedNumber = _identity } = useTekionConversion();
  const { isLoading: isSettingsLoading = false } = partsGeneralSettings || EMPTY_OBJECT;

  const TABLE_PROPS = {
    ..._pick(props, TABLE_MANAGER_DEFAULT_PROPS),
    totalNumberOfEntries: totalCount,
    currentPage: currentPage + 1,
    resultsPerPage: rows,
    showPagination: _size(detailedExtendedValueList),
    rowHeight: TABLE_ROW_HEIGHT,
    pageSize: rows || DEFAULT_RESULTS_PER_PAGE,
    pageSizeOptions: DEFAULT_RESULTS_PER_PAGE_OPTIONS,
    minRows: 0,
    additional,
    getTdProps: useCallback(
      () => ({
        partsGeneralSettings,
      }),
      [partsGeneralSettings]
    ),
  };

  const moduleStyle = useMemo(() => getModuleStyle(contentHeight), [contentHeight]);

  const lastGeneratedTime = _get(additional, 'lastGeneratedTime');

  const renderAggregatedDetails = useCallback(
    details => {
      const {
        label,
        icon,
        iconInfo,
        popoverContentClassName,
        containerClassName,
        accessor,
        isNestedSummary,
        formatter,
      } = details;
      const { aggregatedDetails = EMPTY_OBJECT } = resolvedData;
      const { isLoading = true, data = EMPTY_OBJECT } = aggregatedDetails;

      if (isNestedSummary) {
        return (
          <div className={`${styles.aggregatedInfoContainer} ${containerClassName} m-l-16 p-l-16`}>
            <DropdownSummary {...details} data={data} submenuTitleProps={SUB_MENU_TITLE_PROPS} />
          </div>
        );
      }

      const rawValue = accessor(data);
      const value = _isFunction(formatter) ? formatter(rawValue) : rawValue;

      return (
        <div className={`${styles.aggregatedInfoContainer} ${containerClassName} m-l-16 p-l-16`}>
          <div className={styles.aggregatedInfo}>
            <Content className={styles.aggregatedDetailLabel}>{`${label}:`}</Content>
            {!_isNil(icon) && (
              <Popover
                placement={POPOVER_PLACEMENT.BOTTOM}
                trigger="hover"
                content={<Content className={popoverContentClassName}>{iconInfo}</Content>}>
                <FontIcon className="m-l-12">icon-info</FontIcon>
              </Popover>
            )}
          </div>
          {isLoading || isSettingsLoading ? (
            <Skeleton paragraph={false} active title={SKELETON_TITLE_STYLE} />
          ) : (
            <Heading className="m-l-4 m-t-4" size={3}>
              {!_isNil(value) ? value : NO_DATA}
            </Heading>
          )}
        </div>
      );
    },
    [resolvedData, isSettingsLoading]
  );

  const renderQuickFilters = useCallback(
    () => (
      <QuickFilters
        onAction={onAction}
        isConfiguratorVisible={false}
        additional={{
          quickFilters: getQuickFilterOptions(resolvedData),
          isQuickFilterSelection,
        }}
        quickFilterButtonGroupProps={{
          filterButtonClassName: styles.extendedQuickFilter,
        }}
      />
    ),
    [resolvedData, onAction, isQuickFilterSelection]
  );

  const AGGREGATED_DETAILS_CONFIG = useMemo(
    () => getAggregatedDetailsConfig({ getFormattedNumber, partsGeneralSettings }),
    [getFormattedNumber, partsGeneralSettings]
  );
  const l3HeaderProps = useMemo(() => ({ render: renderQuickFilters }), [renderQuickFilters]);

  useEffect(() => {
    if (isInchcape()) {
      onAction({
        type: DETAILED_EXTENDED_VALUE_ACTION_TYPE.FETCH_PARTS_GENERAL_SETTING,
      });
    }
  }, [onAction]);

  const handleWarehouseChange = useCallback(
    values => {
      onAction({
        type: WAREHOUSE_ACTION_TYPE.CENTRAL_WAREHOUSE_CHANGE,
        payload: { warehouseId: values },
      });
    },
    [onAction]
  );

  const includeTwoDecimalPlacesInAmountFields = useContext(AmountInputFormatContext);

  return (
    <div className="full-height full-width">
      <Page>
        <TekionHelmet title="Detailed Extended Value" />
        <Page.Header hasBack goBackTo={CORE_PARTS_REPORT_PATH}>
          <div className={`${styles.header} full-width`}>
            <Heading lean size={1}>
              {__('Detailed Extended Value')}
            </Heading>
            <PropertyControlledComponent controllerProperty={getIsMultiWarehouseEnabled()}>
              <WarehouseSingleSelectionRenderer
                onChange={handleWarehouseChange}
                selectedWarehouse={selectedWarehouse}
              />
            </PropertyControlledComponent>
            <PropertyControlledComponent controllerProperty={!getIsMultiWarehouseEnabled()}>
              <div className={styles.rightHeaderSection}>
                {_map(AGGREGATED_DETAILS_CONFIG, renderAggregatedDetails)}
              </div>
            </PropertyControlledComponent>
          </div>
        </Page.Header>
        <Page.Body>
          <PropertyControlledComponent controllerProperty={getIsMultiWarehouseEnabled()}>
            <div className={styles.rightHeaderSection}>{_map(AGGREGATED_DETAILS_CONFIG, renderAggregatedDetails)}</div>
          </PropertyControlledComponent>
          <div className="full-width" style={moduleStyle}>
            <SortableTableManager
              tableComponent={FixedColumnTable}
              columns={[...columns, columnConfigurator]}
              data={detailedExtendedValueList}
              filterProps={getFilterProps({
                sourceCodesById,
                selectedFilterGroup,
                integratedBrands,
                includeTwoDecimalPlacesInAmountFields,
              })}
              navigate={navigate}
              location={location}
              onAction={onAction}
              tableManagerProps={TABLE_MANAGER_PROPS}
              tableProps={TABLE_PROPS}
              sortDetails={sortDetails}
              selectedFilters={selectedFilters}
              subHeaderProps={getSubHeaderProps({ isGeneratingReport, lastGeneratedTime, isSettingsLoading })}
              searchText={searchQuery}
              tableClassName={styles.detailedExtendedValueTable}
              l3HeaderProps={l3HeaderProps}
              partsGeneralSettings={partsGeneralSettings}
              key={selectedWarehouse}
            />
          </div>
        </Page.Body>
      </Page>
      <DetailedExtendedValuePushListener onAction={onAction} isGeneratingReport={isGeneratingReport} />
    </div>
  );
}

DetailedExtendedValue.propTypes = {
  rows: PropTypes.number.isRequired,
  sourceCodesById: PropTypes.object.isRequired,
  sortDetails: PropTypes.object,
  onAction: PropTypes.func,
  selectedFilterGroup: PropTypes.string,
  selectedFilters: PropTypes.array,
  detailedExtendedValueList: PropTypes.array,
  contentHeight: PropTypes.number.isRequired,
  totalCount: PropTypes.number,
  currentPage: PropTypes.number,
  searchQuery: PropTypes.string,
  columns: PropTypes.array,
  columnConfigurator: PropTypes.object,
  integratedBrands: PropTypes.array.isRequired,
  additional: PropTypes.object,
  resolvedData: PropTypes.object,
  partsGeneralSettings: PropTypes.object,
};

DetailedExtendedValue.defaultProps = {
  onAction: _noop,
  selectedFilterGroup: DEFAULT_FILTER_GROUP,
  selectedFilters: EMPTY_ARRAY,
  detailedExtendedValueList: EMPTY_ARRAY,
  sortDetails: EMPTY_OBJECT,
  totalCount: 0,
  currentPage: 0,
  searchQuery: '',
  columns: EMPTY_ARRAY,
  columnConfigurator: EMPTY_OBJECT,
  additional: EMPTY_OBJECT,
  resolvedData: EMPTY_OBJECT,
  partsGeneralSettings: EMPTY_OBJECT,
};

export default compose(withCoreEnv, memo)(DetailedExtendedValue);
