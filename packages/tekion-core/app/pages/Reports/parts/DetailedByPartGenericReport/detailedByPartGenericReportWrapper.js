import { useParams, useNavigate } from 'react-router-dom';
import React, { useCallback, useContext, useMemo } from 'react';
import { connect } from 'react-redux';
import { compose } from 'recompose';

import _get from 'lodash/get';
import _isNil from 'lodash/isNil';
import _keyBy from 'lodash/keyBy';
import _map from 'lodash/map';
import _concat from 'lodash/concat';
import _noop from 'lodash/noop';

import { withTekionConversion } from '@tekion/tekion-conversion-web';
import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import { getQueryParams } from '@tekion/tekion-base/utils/general';
import {
  getShouldSetInitialFilterGroupVariant,
  FILTER_GROUP_VARIANT,
} from '@tekion/tekion-business/src/appServices/parts/helpers/general.helpers';
import { CORE } from '@tekion/tekion-base/constants/appServices';
import TekionHelmet from '@tekion/tekion-components/src/molecules/TekionHelmet';
import DetailedByPartGenericReport from '@tekion/tekion-widgets/src/organisms/DetailedByPartGenericReport/detailedByPartGenericReport';
import withActionHandlers from '@tekion/tekion-components/src/connectors/withActionHandlers';
import FixedColumnTable from '@tekion/tekion-components/src/molecules/table/FixedColumnTable';
import withCheckboxTable from '@tekion/tekion-components/src/organisms/withCheckboxTable/withCheckBoxTable';
import withRouter from '@tekion/tekion-components/src/hoc/withRouter';

import AmountInputFormatContext from '@tekion/tekion-widgets/src/appServices/parts/context/amountInputFormatContext';

import { handleReportDownload } from 'actions/download/download.actions';
import { getSourceCodesByIds, getManufacturerConfig } from 'pages/base/app.selectors';
import withCoreEnv from 'hoc/coreEnv/withCoreEnv';

import { getIsThirdPaneBrowserEnabled, getIsMultiWarehouseEnabled } from 'utils/general';
import { REPORT_TYPES } from '@tekion/tekion-widgets/src/organisms/DetailedByPartGenericReport/detailedByPartGenericReport.constants';
import { CORE_PARTS_REPORT_PATH } from '../../constants/departmentTypes';
import { getFiltersConfig, getReportDefaultFilters } from './configs/filters.config';
import {
  getAdditionalColumns,
  getAdditionalColumnVsKeyRenderer,
  getOrderedColumnsKeys,
} from './configs/columns.config';
import { REPORT_TYPE_TO_REQUEST_BUILDER, REPORT_TYPE_TO_FETCH_API } from './configs/apis.config';
import {
  getTableManagerProps,
  getSearchFieldMetadataByReportType,
  getSubHeaderProps,
  getInAndOutOverviewComponentConfig,
} from './detailedByPartGenericReport.config';
import { getHeaderLabel, reportTypeToLabelMap, DOWNLOAD_ASSET_TYPES } from './detailedByPartGenericReport.constants';
import localActionHandler from './detailedByPartGenericReport.actionHandler';
import { BY_PART_ROUTE_BASE_PATH } from '../ByPartGenericReport/byPartGenericReport.constants';
import withColumnConfiguration from './withColumnConfiguration.hoc';
import { getNormalizerFunction } from './detailedByPartGenericReport.formatter';
import { VARIABLE_DATE_WITH_SELECT_FILTER_TYPE } from '../common/SelectWithDate/SelectWithDate.constants';
import { HEADER_ACTION_TYPES } from './detailedByPartGenericReport.actionTypes';

const CheckboxTable = withCheckboxTable(FixedColumnTable);

function DetailedByPartGenericReportWrapper(props) {
  const params = useParams();
  const navigate = useNavigate();
  const {
    allDealerProperty,
    handleReportDownload: handleReportDownloadService,
    onAction,
    sourceCodeById,
    columnConfigurator,
    columns,
    integratedBrands,
    manufacturerConfig,
    customHeaderDetails = EMPTY_OBJECT,
  } = props;

  const selectedDateChooserFilterTypeRef = React.useRef(VARIABLE_DATE_WITH_SELECT_FILTER_TYPE.TILL_DATE_FILTER);

  const goBackToReport = useCallback(() => {
    navigate(CORE_PARTS_REPORT_PATH);
  }, [navigate]);

  const goBackToByPartReport = useCallback(() => {
    const reportType = _get(params, 'reportType', '');
    navigate(`${CORE_PARTS_REPORT_PATH}/${BY_PART_ROUTE_BASE_PATH}/${reportType}`, {
      replace: true,
    });
  }, [navigate, params]);

  const handleGoBack = useCallback(() => {
    if (_get(params, 'partId', '')) {
      goBackToByPartReport();
      return;
    }
    goBackToReport();
  }, [goBackToByPartReport, goBackToReport, params]);

  const { reportType, partId } = params;
  const requestBuilderFunc = REPORT_TYPE_TO_REQUEST_BUILDER[reportType];
  const detailedByPartFetchApi = REPORT_TYPE_TO_FETCH_API[reportType];
  const reportTypeToLabel = reportTypeToLabelMap[reportType];
  const reportAssetType = DOWNLOAD_ASSET_TYPES[reportType];
  const shouldSetInitialFilters = !getShouldSetInitialFilterGroupVariant(props);
  const queryParams = getQueryParams(props);
  const filterGroupVariant = _get(queryParams, FILTER_GROUP_VARIANT);
  const columnsWithConfigurator = _concat(columns, columnConfigurator);

  const additionalColumns = _isNil(columns) ? getAdditionalColumns(reportType) : _keyBy(columnsWithConfigurator, 'key');
  const orderedColumnsKeys = _isNil(columns)
    ? _get(getOrderedColumnsKeys(), reportType, EMPTY_ARRAY)
    : _map(columnsWithConfigurator, 'key');
  const reportName = getHeaderLabel(reportType);
  const TableComponent = useMemo(
    () => (reportType === REPORT_TYPES.LOST_SALE ? CheckboxTable : FixedColumnTable),
    [reportType]
  );
  const customHeaderAction = React.useCallback(
    params => {
      onAction({
        type: HEADER_ACTION_TYPES.FETCH_IN_AND_OUT_SUMMARY,
        payload: params,
      });
    },
    [onAction]
  );
  const unMarkLostSaleAction = React.useCallback(
    params => {
      onAction({
        type: HEADER_ACTION_TYPES.UNMARK_LOST_SALE,
        payload: _get(params, 'payload', EMPTY_ARRAY),
      });
    },
    [onAction]
  );

  const includeTwoDecimalPlacesInAmountFields = useContext(AmountInputFormatContext);

  return (
    <>
      <TekionHelmet title={`Parts: ${reportName}`} />
      <DetailedByPartGenericReport
        reportType={reportType}
        getOverviewComponents={getInAndOutOverviewComponentConfig}
        customHeaderAction={reportType === REPORT_TYPES.IN_AND_OUT ? customHeaderAction : _noop}
        unMarkLostSaleAction={reportType === REPORT_TYPES.LOST_SALE ? unMarkLostSaleAction : _noop}
        filterProps={getFiltersConfig(
          reportType,
          sourceCodeById,
          shouldSetInitialFilters,
          integratedBrands,
          allDealerProperty,
          {
            defaultFilterValues: getReportDefaultFilters(reportType, filterGroupVariant),
            selectedDateChooserFilterTypeRef,
          },
          manufacturerConfig,
          includeTwoDecimalPlacesInAmountFields
        )}
        additionalColumns={additionalColumns}
        getAdditionalColumnRenderers={getAdditionalColumnVsKeyRenderer(onAction, sourceCodeById)}
        orderedColumnsKeys={orderedColumnsKeys}
        partId={partId}
        requestBuilderFunc={requestBuilderFunc}
        detailedByPartFetchApi={detailedByPartFetchApi}
        getSubHeaderProps={getSubHeaderProps(reportType)}
        reportName={reportName}
        handleGoBack={handleGoBack}
        handleReportDownload={handleReportDownloadService}
        reportTypeToLabel={reportTypeToLabel}
        reportAssetType={reportAssetType}
        tableComponent={TableComponent}
        initialFilters={getReportDefaultFilters(reportType, filterGroupVariant)}
        columnConfigurator={columnConfigurator}
        responseFormatter={getNormalizerFunction(reportType)}
        additional={selectedDateChooserFilterTypeRef}
        additionalTableManagerProps={getTableManagerProps(reportType)}
        customHeaderDetails={customHeaderDetails}
        isThirdPaneEnabled={getIsThirdPaneBrowserEnabled()}
        isMultiWarehouseEnabled={getIsMultiWarehouseEnabled()}
        {...getSearchFieldMetadataByReportType(reportType)}
      />
    </>
  );
}

DetailedByPartGenericReportWrapper.propTypes = {};

DetailedByPartGenericReportWrapper.defaultProps = {};

const mapStateToProps = state => {
  const coreAppState = _get(state, [CORE], EMPTY_ARRAY);
  return {
    sourceCodeById: getSourceCodesByIds(coreAppState),
    manufacturerConfig: getManufacturerConfig(state),
  };
};

export default compose(
  withRouter,
  connect(mapStateToProps, {
    handleReportDownload,
  }),
  withTekionConversion,
  withColumnConfiguration,
  withActionHandlers(localActionHandler),
  withCoreEnv
)(DetailedByPartGenericReportWrapper);
