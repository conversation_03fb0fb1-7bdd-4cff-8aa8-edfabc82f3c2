import React from 'react';

import promiseWrapper from '@tekion/tekion-widgets/src/appServices/parts/utils/promise-wrapper';
import { getRemoteEntryInRuntime } from '@tekion/tekion-widgets/src/helpers/moduleFederation';
import Loader from '@tekion/tekion-components/molecules/loader';
import AdvancedErrorBoundary from '@tekion/tekion-widgets/src/organisms/AdvancedErrorBoundary';

import {
  getIsMultiOemSwitchEnabled,
  getIsPartInventoryAtSiteLevel,
  getIsWIPandDSRAsyncReportDownloadEnabled,
  getIsAverageCostEnabled,
} from 'pages/Reports/parts/common/utils';

// This is used to dynamically fetch a module
// by adding a suspender until the module is being fetched

// This port is only for local development and is pointing to the
// port which parts application is configured to be run
const fetcher = promiseWrapper(getRemoteEntryInRuntime('parts', 'WIPByPart', { port: '3002' }));

function WIPByPart(props) {
  const fetchedModule = fetcher.get();
  const Component = fetchedModule.default;

  return <Component {...props} />;
}

function WIPByPartWrapper(props) {
  return (
    <AdvancedErrorBoundary shouldRenderHeader={false}>
      <React.Suspense fallback={<Loader />}>
        <WIPByPart
          {...props}
          isAverageCostEnabled={getIsAverageCostEnabled()}
          isPartInventoryAtSiteLevel={getIsPartInventoryAtSiteLevel()}
          isMultiSiteEnabled={getIsMultiOemSwitchEnabled()}
          isWIPandDSRAsyncReportDownloadEnabled={getIsWIPandDSRAsyncReportDownloadEnabled()}
        />
      </React.Suspense>
    </AdvancedErrorBoundary>
  );
}

export default React.memo(WIPByPartWrapper);
