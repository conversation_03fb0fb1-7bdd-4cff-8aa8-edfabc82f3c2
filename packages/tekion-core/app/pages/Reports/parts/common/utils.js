import _get from 'lodash/get';
import _sortBy from 'lodash/sortBy';

import CoreEnv from 'utils/coreEnv';
import { tget } from 'tbase/utils/general';
import DEALER_PROPERTY_CONSTANTS from 'tbase/constants/dealerProperties';
import TEnvReader from 'tbase/readers/Env';

import { getDealers } from '../../readers/dealer';

export const getIsMultiOemSwitchEnabled = () =>
  tget(CoreEnv.dealerProps, `${DEALER_PROPERTY_CONSTANTS.MULTI_OEM_SWITCH_ENABLED}.value`, false);

export const getIsPartInventoryShared = () =>
  tget(CoreEnv.dealerProps, `${DEALER_PROPERTY_CONSTANTS.PARTS_INVENTORY_SHARED}.value`, false);

export const getIsWIPandDSRAsyncReportDownloadEnabled = () =>
  tget(CoreEnv.dealerProps, `WIP_DSR_ASYNC_REPORT_DOWNLOAD_ENABLED.value`, false); // constant not added as it is temporary DP

export const getIsPartInventoryAtSiteLevel = () => (getIsMultiOemSwitchEnabled() ? !getIsPartInventoryShared() : false);

export const getIsAverageCostEnabled = () =>
  tget(CoreEnv.dealerProps, `${DEALER_PROPERTY_CONSTANTS.AVERAGE_COST_ENABLED}.value`, false);

// this is to make sure that the current dealership is
// shown as the first option as getDealers doesn't return it that way.
// for the rest their order is maintained.
export const getSortedDealersWithCurrentDealerAsFirst = () => {
  const dealers = getDealers();
  const currentDealerId = _get(TEnvReader.userInfo(), 'dealerId');
  return _sortBy(dealers, dealer => (_get(dealer, 'value') === currentDealerId ? -1 : 0));
};
