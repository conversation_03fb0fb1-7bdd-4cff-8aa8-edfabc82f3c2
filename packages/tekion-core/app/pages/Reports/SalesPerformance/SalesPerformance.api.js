import _get from 'lodash/get';

import {
  DEAL_SERVICE,
  FNI_SERVICE,
  REPORTS_SERVICE,
  SALES_SETTINGS,
  SALES_SETTINGS_SERVICE,
} from '@tekion/tekion-base/constants/deal/endPoint';
import { EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import { cleanESPayload } from '@tekion/tekion-widgets/src/helpers/Sales/esPayload.helpers';

import { Http } from 'services/http';

const API_PATHS = {
  DEALS_LIST: `${REPORTS_SERVICE}/deal/search`,
  GET_DEAL_REPORTS: `${REPORTS_SERVICE}/deal/reports`,
  GET_AGGREGATION_FIELDS: `${REPORTS_SERVICE}/deal/report/agg/fields`,
  GET_FNI_PRODUCTS: `${FNI_SERVICE}/u/v1.0.0/fni/fnIProducts`,
  DEAL_SETUP: `/${SALES_SETTINGS}/`,
  UPDATE_CUSTOM_STATUS: `${DEAL_SERVICE}/u/deal`,
  ENTERPRISE_V2_WORKSPACES: '/enterprise-service/u/v2/workspace',
  GET_BULK_LENDERS: `${SALES_SETTINGS_SERVICE}/v1.0.0/sales/bulkLenders`,
};
export const SALES_PERFORMANCE_PREFERENCE_URL = `${REPORTS_SERVICE}/customreport/listview/sales/preference`;

const getData = ({ data }) => _get(data, 'data', EMPTY_ARRAY);

export default class SalesPerformanceAPI {
  static getDealsList(payload) {
    return Http.post(API_PATHS.DEALS_LIST, payload).then(getData);
  }

  static getDealReports(payload) {
    return Http.post(API_PATHS.GET_DEAL_REPORTS, payload).then(getData);
  }

  static getAggregationFields(payload) {
    return Http.get(API_PATHS.GET_AGGREGATION_FIELDS, payload).then(getData);
  }

  static getDealSetupInfo() {
    return Http.get(API_PATHS.DEAL_SETUP);
  }

  static getFNIProducts() {
    return Http.get(API_PATHS.GET_FNI_PRODUCTS).then(getData);
  }

  static updateCustomStatusInDeal(dealNumber, customStatus) {
    return Http.post(`${API_PATHS.UPDATE_CUSTOM_STATUS}/${dealNumber}/subStatus/`.then(getData), {
      subStatus: customStatus,
    });
  }

  static getEnterpriseV2Workspaces() {
    return Http.get(API_PATHS.ENTERPRISE_V2_WORKSPACES).then(getData);
  }

  static getBulkLenders(dealerIds) {
    return Http.post(API_PATHS.GET_BULK_LENDERS, dealerIds);
  }
}
