import React from 'react';
import { getPrimaryVehicleValue } from '../SalesPerformance.helper';

jest.mock('lodash', () => ({
  _get: jest.fn((obj, path) => obj?.[path]),
  _toString: jest.fn(val => String(val)),
}));

const __ = str => str;
const EMPTY_STRING = '';

describe('getPrimaryVehicleValue', () => {
  const vehicleId = '12345';
  const commonProps = {
    primaryVehicle: { id: vehicleId },
    temp: false,
    dealerId: 'dealer-1',
  };

  it('returns EMPTY_STRING if primaryVehicle is false', () => {
    const result = getPrimaryVehicleValue({ primaryVehicle: false, temp: false }, 'ABC123', 'dealer-1');
    expect(result).toBe(EMPTY_STRING);
  });

  it('returns EMPTY_STRING if temp is true', () => {
    const result = getPrimaryVehicleValue({ ...commonProps, temp: true }, 'Car', 'dealer-1');
    expect(result).toBe(EMPTY_STRING);
  });

  it('returns an <a> element when value exists and dealerId matches', () => {
    const result = getPrimaryVehicleValue(commonProps, 'DEF456', 'dealer-1');
    expect(React.isValidElement(result)).toBe(true);
    expect(result.type).toBe('a');
    expect(result.props.href).toBe(`/vi/vehicle/${vehicleId}`);
    expect(result.props.children).toBe('DEF456');
  });

  it('returns "Build" when value is falsy and dealerId mismatches', () => {
    const result = getPrimaryVehicleValue({ ...commonProps, dealerId: 'other' }, '', 'dealer-1');
    expect(result).toBe('Build');
  });

  it('returns plain value when value exists but dealerId mismatches', () => {
    const result = getPrimaryVehicleValue({ ...commonProps, dealerId: 'other' }, 'DEF456', 'dealer-1');
    expect(result).toBe('DEF456');
  });
});
