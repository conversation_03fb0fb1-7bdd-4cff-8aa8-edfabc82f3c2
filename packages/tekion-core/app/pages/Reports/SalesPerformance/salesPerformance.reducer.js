import { produce } from 'immer';
import { handleActions } from 'redux-actions';
import reduceReducers from 'reduce-reducers';
import _get from 'lodash/get';
import _set from 'lodash/set';
import _keyBy from 'lodash/keyBy';

import { EMPTY_OBJECT } from 'tbase/app.constants';
import salesReducers, { initialState } from 'reducers/sales.reducers';
import { addProductProviderIdToProducts } from './SalesPerformance.helper';
import ACTIONS from './salesPerformance.actionTypes';

const localState = {
  dealSetupInfo: EMPTY_OBJECT,
  columnMetaData: EMPTY_OBJECT,
  metaData: EMPTY_OBJECT,
};

const ACTION_HANDLERS = handleActions(
  {
    [ACTIONS.FETCH_DEAL_SETUP_INFO]: (state, { payload }) =>
      produce(state, draft => {
        const { dealTypeConfigs, customStatuses, lenders } = payload || EMPTY_OBJECT;

        _set(draft, 'dealSetupInfo', {
          ...payload,
          // lendersInfo: _keyBy(lenders, 'id'),
          dealTypeConfigs: dealTypeConfigs || [],
          customStatuses: customStatuses || [],
        });
      }),

    [ACTIONS.SET_BULK_LENDER_INFO]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, 'lendersInfo', {
          lendersInfo: _keyBy(payload, 'id'),
        });
      }),

    [ACTIONS.FETCH_FINANCE_N_INSURANCE_PRODUCTS]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, 'metaData.fniProducts', addProductProviderIdToProducts(payload));
      }),

    [ACTIONS.UPDATE_CUSTOM_STATUS_SUCCESS]: (state, { payload }) =>
      produce(state, draft => {
        const { dealNumber, status } = payload;
        const entityList = _get(draft, 'entityList') || [];
        const targetDeal = (entityList || []).find(({ dealNumber: dealNo }) => dealNo === dealNumber);
        if (targetDeal) _set(targetDeal, 'subStatus', status);
      }),
  },
  localState
);

export default reduceReducers({ ...initialState, ...localState }, salesReducers, ACTION_HANDLERS);
