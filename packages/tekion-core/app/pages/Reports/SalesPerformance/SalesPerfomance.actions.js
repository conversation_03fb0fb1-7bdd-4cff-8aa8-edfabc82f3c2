import _get from 'lodash/get';
import _reduce from 'lodash/reduce';
import _isEmpty from 'lodash/isEmpty';
import _map from 'lodash/map';

import { EMPTY_OBJECT, EMPTY_ARRAY } from 'tbase/app.constants';
import { toaster } from 'tcomponents/organisms/NotificationWrapper';
import { handleBulkFetchNotesV2 } from 'tcomponents/actions/notes.actions';
import { ENTITIES } from '@tekion/tekion-base/bulkResolvers/entities';

import resolver from '@tekion/tekion-base/bulkResolvers';

import { NOTES_ASSET_TYPE } from 'tbase/constants/desking/contracts';
import { createGenericActions } from 'actions/sales.actionCreators';
import { getActivityList } from 'actions/sales.actions';
import { API_STATUS, MESSAGES } from './SalesPerformance.constant';
import SalesPerformanceApi from './SalesPerformance.api';
import ACTIONS from './salesPerformance.actionTypes';

const REPORTS_FETCH_ERROR = __('Failed to Fetch Reports');

const {
  saveReportsList,
  saveDealsAndReports,
  saveAggregationFields,
  fetchTableDataRequest,
  fetchTableDataError,
  saveReportsAndAggregatedReports,
} = createGenericActions;

const getResolvedData = (entityType, rows) => {
  if (_isEmpty(rows)) return Promise.resolve(EMPTY_ARRAY);

  return resolver.getResolvedData(entityType, rows);
};

const getNotes = dealIds => dispatch => {
  if (dealIds.length > 0) {
    dispatch(handleBulkFetchNotesV2(NOTES_ASSET_TYPE.CONTRACTS, dealIds));
  }
};

export const getDealReports = payload => async dispatch => {
  const {
    flatTableReportingRequest: { page, itemsPerPage },
  } = payload || { flatTableReportingRequest: EMPTY_OBJECT };
  dispatch(fetchTableDataRequest());
  try {
    const response = await SalesPerformanceApi.getDealReports(payload);
    const resolvedGroupedRows = await getResolvedData(ENTITIES.SALES_PERF_REPORT_GROUP, response?.hits);

    dispatch(saveReportsList({ response, page, itemsPerPage, resolvedGroupedRows }));
  } catch (error) {
    toaster('error', REPORTS_FETCH_ERROR);
    dispatch(fetchTableDataError());
  }
};

export const getDealReportsBreakDown =
  (reportsPayload, { page, itemsPerPage, ...dealPayload }) =>
  async dispatch => {
    dispatch(fetchTableDataRequest());
    try {
      const promises = [
        SalesPerformanceApi.getDealReports(reportsPayload),
        SalesPerformanceApi.getDealsList(dealPayload),
      ];

      const [reportsList = {}, entityList = {}] = await Promise.all(
        promises.map(p => p.catch(() => toaster('error', REPORTS_FETCH_ERROR)))
      );

      const resolvedDeals = await getResolvedData(ENTITIES.DEALS, entityList?.hits);
      const resolvedReportsList = await getResolvedData(ENTITIES.SALES_PERF_REPORT_GROUP, reportsList?.hits);

      dispatch(
        saveDealsAndReports({
          reportsList,
          entityList: { ...entityList, hits: resolvedDeals },
          page,
          itemsPerPage,
          resolvedReportsList,
        })
      );
      const { dealIds, dealNumbers } = _reduce(
        _get(entityList, 'hits'),
        (acc, deal) => {
          acc.dealIds.push(deal.id);
          acc.dealNumbers.push(deal.dealNumber);
          return acc;
        },
        { dealIds: [], dealNumbers: [] }
      );
      dispatch(getNotes(dealIds));
      dispatch(getActivityList(dealNumbers));
    } catch (error) {
      dispatch(fetchTableDataError());
    }
  };

export const getAggregationFields = () => async dispatch => {
  try {
    const response = await SalesPerformanceApi.getAggregationFields();
    dispatch(saveAggregationFields(response));
  } catch (error) {
    dispatch(saveAggregationFields([]));
  }
};

export const getDealSetupInfo = () => async dispatch => {
  const response = await SalesPerformanceApi.getDealSetupInfo();
  dispatch({
    type: ACTIONS.FETCH_DEAL_SETUP_INFO,
    payload: _get(response, 'data.data') || {},
  });
};

export const getBulkLenders = dealerIds => async dispatch => {
  const response = await SalesPerformanceApi.getBulkLenders(dealerIds);
  dispatch({
    type: ACTIONS.SET_BULK_LENDER_INFO,
    payload: _get(response, 'data.data') || [],
  });
};

export const getFNIProducts = () => async dispatch => {
  const response = await SalesPerformanceApi.getFNIProducts();

  if (!_isEmpty(response)) {
    dispatch({
      type: ACTIONS.FETCH_FINANCE_N_INSURANCE_PRODUCTS,
      payload: response,
    });
  }
};

export const updateCustomStatus = (dealNumber, oldSubStatus, newSubStatus) => async dispatch => {
  dispatch({ type: ACTIONS.UPDATE_CUSTOM_STATUS_SUCCESS, payload: { dealNumber, status: newSubStatus } });
  const response = await SalesPerformanceApi.updateCustomStatusInDeal(dealNumber, newSubStatus);
  if (_get(response, ['data', 'status']) !== API_STATUS.SUCCESS) {
    dispatch({ type: ACTIONS.UPDATE_CUSTOM_STATUS_SUCCESS, payload: { dealNumber, status: oldSubStatus } });
    toaster('error', MESSAGES.UPDATE_CUSTOM_STATUS_FAILURE);
  }
};

export const getDealReportsAndAggregatedReports = (reportsPayload, aggregatedReportsPayload) => async dispatch => {
  const {
    flatTableReportingRequest: { page, itemsPerPage },
  } = reportsPayload || { flatTableReportingRequest: EMPTY_OBJECT };
  dispatch(fetchTableDataRequest());
  try {
    const promises = [
      SalesPerformanceApi.getDealReports(reportsPayload),
      SalesPerformanceApi.getDealReports(aggregatedReportsPayload),
    ];

    const [reportsListResponse = {}, aggregatedReportsList = {}] = await Promise.all(
      promises.map(p => p.catch(() => toaster('error', REPORTS_FETCH_ERROR)))
    );

    const resolvedGroupedRows = await getResolvedData(ENTITIES.SALES_PERF_REPORT_GROUP, reportsListResponse?.hits);
    const aggregatedReports = _map(aggregatedReportsList?.hits || EMPTY_ARRAY, row => ({
      ...row.columns,
      isTotalAggregatedReport: true,
    }));

    dispatch(
      saveReportsAndAggregatedReports({
        reportsListResponse,
        page,
        itemsPerPage,
        resolvedGroupedRows,
        aggregatedReports,
      })
    );
  } catch (error) {
    toaster('error', REPORTS_FETCH_ERROR);
    dispatch(fetchTableDataError());
  }
};
