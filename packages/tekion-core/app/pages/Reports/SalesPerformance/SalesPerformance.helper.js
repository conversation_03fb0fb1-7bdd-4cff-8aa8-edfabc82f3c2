import React from 'react';

import { defaultMemoize } from 'reselect';
import _map from 'lodash/map';
import _values from 'lodash/values';
import _filter from 'lodash/filter';
import _split from 'lodash/split';
import _size from 'lodash/size';
import _get from 'lodash/get';
import _compact from 'lodash/compact';
import _isEmpty from 'lodash/isEmpty';
import _replace from 'lodash/replace';
import _includes from 'lodash/includes';
import _groupBy from 'lodash/groupBy';
import _find from 'lodash/find';
import _flatMap from 'lodash/flatMap';
import _toString from 'lodash/toString';

import { MARKET_CLASS_CATEGORY_OPTIONS } from 'tbase/constants/vehicleInventory/trim';
import { EMPTY_ARRAY, EMPTY_STRING, EMPTY_OBJECT } from 'tbase/app.constants';
import MODULE_ASSET_TYPES from 'tbase/constants/moduleAssetTypes';
import { PERMISSIONS } from 'tbase/constants/permissions';
import { getStatusFilterOptions, getTaxCodeFilterOptions } from 'tbase/utils/sales';
import { RESOURCE_TYPE } from 'tbase/bulkResolvers/constants/resourceType';
import { DEAL_CONFIRMED_FILTER } from 'tbase/constants/deal/common';
import TEnvReader from 'tbase/readers/Env';
import { REASON_FOR_EXEMPT } from 'tbase/constants/deal/customer';
import { getOptionsByKeyValuePair } from 'tbase/marketScan/utils';
import { getPersonasForMatchedRoles } from 'tbase/marketScan/helpers/deal.helpers';
import { SALES_PERSONAS } from 'tbase/utils/userUtils';
import { PERSONAS } from 'tbase/constants/personas';
import { getFormattedLendersList } from 'tbase/utils/sales/lenders.util';
import { WORKSPACE_TYPES } from '@tekion/tekion-business/src/constants/workspace/workspaceTypes';
import { getDealerOptionsForEnterprise } from '@tekion/tekion-base/utils/sales/dealerworkspace';

import {
  USER_LOOKUP_RESOURCE_PARAMS,
  getAllUsersforDealers,
  getSalesUsersLookupParams,
} from 'tbusiness/appServices/service/helpers/user';
import { getAsyncSelectResourceProps } from 'tcomponents/utils/filterUtils';
import FILTER_TYPES from 'tcomponents/organisms/filterSection/constants/filterSection.filterTypes';
import { makeSiteOptions } from '@tekion/tekion-widgets/src/appServices/sales/dealerSiteFilter.helpers';

import PRESET_ID from 'tcomponents/molecules/dateRange/constants/dateRange.presetIds';
import { permissionValidations } from '@tekion/tekion-components/src/widgets/permissionsHelper';
import {
  EQUALS,
  GREATER_THAN,
  GREATER_THAN_EQUAL,
  LESS_THAN,
  LESS_THAN_EQUAL,
  NOT_EQUALS,
  IN,
  NOT_IN,
} from 'tcomponents/organisms/filterSection/constants/filterSection.operators';
import { DEFAULT_FILTER_BEHAVIOR } from 'tcomponents/organisms/filterSection/constants/filterSection.constants';
import { US_STATES } from 'tbase/constants/locations.constants';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';
import { shouldShowV2 } from '@tekion/tekion-business/src/helpers/sales/viFields';

import { COMMON_SALES_INVENTORY_KEYS } from 'constants/reports';
import { DEAL_SALES_LOG_FILTER_TYPES } from '@tekion/tekion-business/src/constants/coreReports';
import {
  ECONTRACT_STATUSES,
  ECONTRACT_STATUS_LABELS,
} from '@tekion/tekion-widgets/src/appServices/sales/constants/credit';
import { arrayFilter } from 'utils/helpers';
import { createFilterTypesFromColumnMetaData } from 'utils/filter';
// import { getPaymentTypesOptions } from 'utils/sales.helpers';
import { getPrimaryVehicleStockNumber, getTradeIns, getDataFromActivityList } from './SalesPerformance.reader';
import DealerWorkspace from './components/DealerWorkspace';

import { getDealers } from '../readers/dealer';
import {
  VEHICLE_DELIVERED_FILTER,
  VEHICLE_CERTIFIED_FILTER,
  CONCIERGE_ACTIVATED,
  KEYS,
  REBATE_TYPE_OPTIONS,
  MIGRATED_OPTIONS,
  BOOLEAN_OPTIONS,
  VEHICLE_CATEGORY_OPTIONS,
  COLUMN_VS_CUSTOM_ACCESSOR,
  COLUMN_ACCESSOR,
  KEYS_VIA_FIELD_NAME,
  CUSTOM_ACCESSOR_VIA_FIELD_NAME,
  GDPR_CONFIGS,
} from './SalesPerformance.constant';

import styles from './SalesPerformance.module.scss';
import { CollapsableGroupHeading } from '../VehicleSales/atoms/enterpriseDealerFilterComponents';

const DELIMITER = '/';

const SALES_PERF_ROUTE_ID = 'default';

const DATE_RANGE = {
  id: DEAL_SALES_LOG_FILTER_TYPES.SOLD_TIME,
  name: __('Contract Date'),
  type: 'DATE',
  additional: {
    defaultPreset: PRESET_ID.MONTH_TO_DATE,
  },
};

const OPERATORS_TO_SUPPORT = [EQUALS, GREATER_THAN, GREATER_THAN_EQUAL, LESS_THAN, LESS_THAN_EQUAL, NOT_EQUALS];

export const NONE_OPTION = {
  label: __('None'),
  value: 'NONE',
};

const STATE_OPTIONS = US_STATES.map(({ code }) => ({
  label: code,
  value: code,
}));

const FILTER_KEYS = [
  KEYS.SOURCE_TYPE,
  KEYS.RECEIVED_DATE,
  KEYS.INTERIOR_COLOR,
  KEYS.EXTERIOR_COLOR,
  KEYS.INVOICE_PRICE,
  ..._values(COMMON_SALES_INVENTORY_KEYS),
  KEYS.CREATED_DATE,
];

const makeOptionsForLenders = (lenders, dealerOptions, enterpriseDealerList, isEnterpriseV2Enabled) => {
  const groupedLenders = _groupBy(lenders, 'dealerId');

  return _compact(
    _map(groupedLenders, (options, dealerId) => {
      const dealer = _find(isEnterpriseV2Enabled ? enterpriseDealerList : dealerOptions, { value: dealerId });

      if (!dealer) return null;
      return {
        label: dealer?.label,
        options: _compact(
          _map(options, ({ displayName, legalName, name, id, osfLender }) => {
            const labelName = displayName || legalName || name;
            if (labelName && id) {
              return {
                label: displayName || legalName || name,
                value: id,
                osfLender,
              };
            }
            return null;
          })
        ),
      };
    })
  );
};

const getOSFLenders = (lenders = {}) => {
  const listOfLenders = _values(lenders) || [];
  return arrayFilter(listOfLenders, 'osfLender', true);
};

export const formatPaymentOptionsFromSalesSetup = paymentOptionsFromSalesSetup =>
  _compact(
    _map(paymentOptionsFromSalesSetup, ({ paymentOptionType: value, displayName: label, enabled }) =>
      enabled ? { value, label } : null
    )
  );

export const getDealerOptions = ({ isEnterpriseV2Enabled, enterpriseV2Workspaces, accessibleDealers }) => {
  const dealerOptions = getDealers();
  if (!isEnterpriseV2Enabled) {
    return dealerOptions;
  }

  return getDealerOptionsForEnterprise({ enterpriseV2Workspaces, accessibleDealers });
};

const getAdditionPayloadForDealer = isEnterpriseV2Enabled => {
  if (isEnterpriseV2Enabled) {
    return {
      component: DealerWorkspace,
      valueKey: 'value',
      isSelectTypeFilter: true,
      dropDownClassName: styles.dealerShipWorkspace,
      id: 'dealers',
      operators: [IN, NOT_IN],
    };
  }
  return EMPTY_OBJECT;
};

export const getEnterpriseDealers = nodes =>
  _flatMap(nodes, node => [
    ...(node.type === WORKSPACE_TYPES.DEALER ? [{ label: node.label, value: node.value }] : []),
    ...(node.children ? getEnterpriseDealers(node.children) : []),
  ]);

// const getSelectedDealerIds = (dealerOptions, tableFilters) => {
//   const { dealerId } = TEnvReader.userInfo();
//   const filter = _find(tableFilters, { type: KEYS.DEALER });
//   if (!filter) {
//     return _filter(dealerOptions, item => item.value === dealerId);
//   }

//   const { operator, values } = filter;

//   if (operator === 'IN') {
//     return _filter(dealerOptions, item => _includes(values, item.value));
//   }
//   if (operator === 'NIN') {
//     return _filter(dealerOptions, item => !_includes(values, item.value));
//   }

//   return dealerOptions;
// };

const getFilterTypes = defaultMemoize(
  (
    filters,
    columnMetaData,
    sourceTypeOptions,
    dealerOemSites,
    isEnterpriseV2Enabled,
    enterpriseV2Workspaces,
    accessibleDealers
  ) => {
    const {
      bodyTypes,
      vehicleModels,
      dealTypes,
      dealCustomStatuses,
      stockTypes,
      vehicleSubTypes,
      paymentTypes,
      locationCodeOptions,
      dealMetadata,
      taxCodes,
      dealSetupInfo,
      makeOptions,
      rolesList,
      tableFilters,
      lendersInfo,
      bodyClass,
    } = filters;
    const extras = {
      ...filters,
      sourceTypeOptions,
    };
    const { dealerId } = TEnvReader.userInfo();
    const rolesForAssignee = _get(dealSetupInfo, 'rolesForAssignee') || {};
    const paymentOptionsFromSalesSetup = formatPaymentOptionsFromSalesSetup(
      _get(dealSetupInfo, 'paymentOptionConfigs')
    );
    const salesPeronas = getPersonasForMatchedRoles(rolesList, rolesForAssignee, PERSONAS.SALES_PERSON);
    const fniManagerPersonas = getPersonasForMatchedRoles(rolesList, rolesForAssignee, PERSONAS.F_nd_I_MANAGER);
    const salesManagerPersonas = getPersonasForMatchedRoles(rolesList, rolesForAssignee, PERSONAS.SALES_MANAGER);
    const closingManagerPersonas = getPersonasForMatchedRoles(rolesList, rolesForAssignee, PERSONAS.CLOSING_MANAGER);
    const dealerOptions = getDealerOptions({ isEnterpriseV2Enabled, enterpriseV2Workspaces, accessibleDealers });
    const additionalPayloadForDealers = getAdditionPayloadForDealer(isEnterpriseV2Enabled);
    const enterpriseDealerList = getEnterpriseDealers(dealerOptions);
    const bodyClassOptions = shouldShowV2() ? bodyClass : MARKET_CLASS_CATEGORY_OPTIONS;

    const filterTypes = [
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.MAKE,
        name: __('Vehicle Makes'),
        type: 'MULTI_SELECT',
        additional: {
          options: makeOptions,
        },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.BODY_TYPE,
        name: __('Body Type'),
        type: 'MULTI_SELECT',
        additional: { options: bodyTypes },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.DISPLAY_MODEL,
        name: __('Model'),
        type: 'MULTI_SELECT',
        additional: { options: vehicleModels },
      },
      ...(DealerPropertyHelper.isRVVehiclesSupported()
        ? [
            {
              id: DEAL_SALES_LOG_FILTER_TYPES.VEHICLE_CATEGORY,
              name: __('Vehicle Category'),
              type: FILTER_TYPES.SINGLE_SELECT,
              additional: {
                options: VEHICLE_CATEGORY_OPTIONS,
              },
            },
          ]
        : []),
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.MIGRATED,
        name: __('Migrated'),
        type: 'SINGLE_SELECT',
        additional: { options: MIGRATED_OPTIONS },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.VEHICLE_TYPE,
        name: __('Stock Type'),
        type: 'MULTI_SELECT',
        additional: { options: stockTypes },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.DEAL_STATUS,
        name: __('Status'),
        type: 'MULTI_SELECT',
        additional: {
          options: getStatusFilterOptions(dealMetadata?.dealStatus),
        },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.VEHICLE_SUB_TYPE,
        name: __('Sub Stock Type'),
        type: 'MULTI_SELECT',
        additional: { options: vehicleSubTypes },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.SALES_PERSON,
        name: __('Sales Person'),
        resourceParams: USER_LOOKUP_RESOURCE_PARAMS,
        type: FILTER_TYPES.ASYNC_MULTI_SELECT,
        resourceType: RESOURCE_TYPE.USER_ID,
        additional: {
          ...getSalesUsersLookupParams(_isEmpty(salesPeronas) ? SALES_PERSONAS : salesPeronas),
          staticOptions: [NONE_OPTION],
        },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.SALES_PERSON_2,
        name: __('Sales Person 2'),
        resourceParams: USER_LOOKUP_RESOURCE_PARAMS,
        type: FILTER_TYPES.ASYNC_MULTI_SELECT,
        resourceType: RESOURCE_TYPE.USER_ID,
        additional: {
          ...getSalesUsersLookupParams(_isEmpty(salesPeronas) ? SALES_PERSONAS : salesPeronas),
          staticOptions: [NONE_OPTION],
        },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.FINANCE_MANAGER,
        name: __('F&I Manager'),
        resourceParams: USER_LOOKUP_RESOURCE_PARAMS,
        type: FILTER_TYPES.ASYNC_MULTI_SELECT,
        resourceType: RESOURCE_TYPE.USER_ID,
        additional: {
          ...getSalesUsersLookupParams(_isEmpty(fniManagerPersonas) ? SALES_PERSONAS : fniManagerPersonas),
          staticOptions: [NONE_OPTION],
        },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.SALES_MANAGER,
        name: __('Sales Manager'),
        resourceParams: USER_LOOKUP_RESOURCE_PARAMS,
        type: FILTER_TYPES.ASYNC_MULTI_SELECT,
        resourceType: RESOURCE_TYPE.USER_ID,
        additional: {
          ...getSalesUsersLookupParams(_isEmpty(salesManagerPersonas) ? SALES_PERSONAS : salesManagerPersonas),
          staticOptions: [NONE_OPTION],
        },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.CLOSING_MANAGER,
        name: __('Closing Manager'),
        resourceParams: USER_LOOKUP_RESOURCE_PARAMS,
        type: FILTER_TYPES.ASYNC_MULTI_SELECT,
        resourceType: RESOURCE_TYPE.USER_ID,
        additional: {
          ...getSalesUsersLookupParams(_isEmpty(closingManagerPersonas) ? SALES_PERSONAS : closingManagerPersonas),
          staticOptions: [NONE_OPTION],
        },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.CREATED_BY,
        name: __('Created By'),
        resourceParams: USER_LOOKUP_RESOURCE_PARAMS,
        type: FILTER_TYPES.ASYNC_MULTI_SELECT,
        resourceType: RESOURCE_TYPE.USER_ID,
        additional: getAllUsersforDealers([dealerId]),
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.DEAL_TYPE,
        name: __('Deal Type'),
        type: 'MULTI_SELECT',
        additional: {
          options: _map(
            _filter(dealTypes, ({ enabled }) => enabled),
            ({ dealType, displayName }) => ({
              value: dealType,
              label: displayName,
            })
          ),
        },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.DEAL_SUB_STATUS,
        name: __('Sub Status'),
        type: 'MULTI_SELECT',
        additional: {
          options: dealCustomStatuses
            .filter(({ enabled }) => enabled)
            .map(({ status }) => ({
              value: status,
              label: status,
            })),
        },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.VEHICLE_DELIVERED,
        name: __('Vehicle Delivered'),
        type: 'SINGLE_SELECT',
        additional: { options: VEHICLE_DELIVERED_FILTER },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.CERTIFIED,
        name: __('Certified'),
        type: 'SINGLE_SELECT',
        additional: { options: VEHICLE_CERTIFIED_FILTER },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.GL_BALANCE_AMT,
        name: __('G/L Balance'),
        type: 'NUMBER',
        additional: {
          operators: OPERATORS_TO_SUPPORT,
        },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.PENDING_DOWNPAYMENT,
        name: __('Pending Down Payment'),
        type: 'NUMBER',
        additional: {
          operators: OPERATORS_TO_SUPPORT,
        },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.TERMS,
        name: __('Terms'),
        type: 'NUMBER',
        additional: {
          operators: OPERATORS_TO_SUPPORT,
        },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.TRADE_COUNT,
        name: __('Trade-in Count'),
        type: 'NUMBER',
        additional: {
          operators: OPERATORS_TO_SUPPORT,
        },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.APPOINTMENTS_SCHEDULED,
        name: __('Appointments Scheduled'),
        type: 'NUMBER',
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.CONTRACTS_IN_TRANSIT_AMT,
        name: __('Contract In Transit'),
        type: 'NUMBER',
        additional: {
          operators: OPERATORS_TO_SUPPORT,
        },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.FLOORING_AMT,
        name: __('Flooring Amount'),
        type: 'NUMBER',
        additional: {
          operators: OPERATORS_TO_SUPPORT,
        },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.FI_CIT,
        name: __('FI CIT'),
        type: 'NUMBER',
        additional: {
          operators: OPERATORS_TO_SUPPORT,
        },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.BODY_CLASS,
        name: __('Body Class'),
        type: 'MULTI_SELECT',
        additional: { options: bodyClassOptions },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.DEALER_LIST,
        name: __('Dealer'),
        type: isEnterpriseV2Enabled ? FILTER_TYPES.CUSTOM : FILTER_TYPES.MULTI_SELECT,
        additional: { options: dealerOptions, ...additionalPayloadForDealers },
      },
      { ...DATE_RANGE },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.FNI_DATE,
        name: __('F&I Date'),
        type: 'DATE',
        additional: {
          defaultPreset: PRESET_ID.MONTH_TO_DATE,
        },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.CLOSED_TIME,
        name: __('Deal Closed Date'),
        type: 'DATE',
        additional: {
          defaultPreset: PRESET_ID.MONTH_TO_DATE,
        },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.BOOKED_TIME,
        name: __('Booked Date'),
        type: 'DATE',
        additional: {
          defaultPreset: PRESET_ID.MONTH_TO_DATE,
        },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.SOLD_DATE,
        name: __('Sold Date'),
        type: 'DATE',
        additional: {
          defaultPreset: PRESET_ID.MONTH_TO_DATE,
        },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.PAYMENT_TYPE,
        name: __('Payment Types'),
        type: 'MULTI_SELECT',
        additional: {
          options: paymentOptionsFromSalesSetup,
          // options: paymentTypes ? getPaymentTypesOptions(paymentTypes) : EMPTY_ARRAY,
        },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.LENDER,
        name: __('Lender'),
        type: 'MULTI_SELECT',
        additional: {
          options: makeOptionsForLenders(
            getFormattedLendersList(lendersInfo),
            dealerOptions,
            enterpriseDealerList,
            isEnterpriseV2Enabled
          ),
          formatOptionLabel: option => (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <span>{option.label}</span>
              {option.osfLender && <span style={{ marginLeft: '8px', fontSize: '12px', color: 'grey' }}>(OSF)</span>}
            </div>
          ),
          components: { GroupHeading: CollapsableGroupHeading },
        },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.LOCATION_CODE,
        name: __('Vehicle Location'),
        type: 'MULTI_SELECT',
        additional: { options: locationCodeOptions },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.FINANCE_RESERVE_PENETRATION,
        name: __('Finance Reserve Penetration'),
        type: FILTER_TYPES.NUMBER,
        additional: { operators: OPERATORS_TO_SUPPORT },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.CONCIERGE_ACTIVATED,
        name: __('Concierge Activated'),
        type: 'SINGLE_SELECT',
        additional: { options: CONCIERGE_ACTIVATED },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.REBATE_AMOUNT,
        name: __('Rebate Amount'),
        type: 'NUMBER',
        additional: {
          operators: OPERATORS_TO_SUPPORT,
        },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.REBATE_TYPE,
        name: __('Rebate Type'),
        type: 'MULTI_SELECT',
        additional: { options: REBATE_TYPE_OPTIONS },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.DEAL_TAX_STATE,
        name: __('Taxable State'),
        type: FILTER_TYPES.ASYNC_MULTI_SELECT,
        resourceType: RESOURCE_TYPE.DEAL_TAX_STATE,
        resourceParams: getAsyncSelectResourceProps(RESOURCE_TYPE.DEAL_TAX_STATE),
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.DEAL_TAX_CITY,
        name: __('Taxable City'),
        type: FILTER_TYPES.ASYNC_MULTI_SELECT,
        resourceType: RESOURCE_TYPE.DEAL_TAX_CITY,
        resourceParams: getAsyncSelectResourceProps(RESOURCE_TYPE.DEAL_TAX_CITY),
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.DEAL_TAX_COUNTY,
        name: __('Taxable County'),
        type: FILTER_TYPES.ASYNC_MULTI_SELECT,
        resourceType: RESOURCE_TYPE.DEAL_TAX_COUNTY,
        resourceParams: getAsyncSelectResourceProps(RESOURCE_TYPE.DEAL_TAX_COUNTY),
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.DEAL_TAX_ZIP,
        name: __('Taxable $$(ZIP Code)'),
        type: FILTER_TYPES.STRING,
        additional: {
          operators: OPERATORS_TO_SUPPORT,
        },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.DEAL_CUSTOMER_COUNTY,
        name: __('Buyer County'),
        type: FILTER_TYPES.ASYNC_MULTI_SELECT,
        resourceType: RESOURCE_TYPE.DEAL_CUSTOMER_COUNTY,
        resourceParams: getAsyncSelectResourceProps(RESOURCE_TYPE.DEAL_CUSTOMER_COUNTY),
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.DEAL_CUSTOMER_CITY,
        name: __('Buyer City'),
        type: FILTER_TYPES.ASYNC_MULTI_SELECT,
        resourceType: RESOURCE_TYPE.DEAL_CUSTOMER_CITY,
        resourceParams: getAsyncSelectResourceProps(RESOURCE_TYPE.DEAL_CUSTOMER_CITY),
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.DEAL_CUSTOMER_STATE,
        name: __('Buyer State'),
        type: FILTER_TYPES.ASYNC_MULTI_SELECT,
        resourceType: RESOURCE_TYPE.DEAL_CUSTOMER_STATE,
        resourceParams: getAsyncSelectResourceProps(RESOURCE_TYPE.DEAL_CUSTOMER_STATE),
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.DEAL_CUSTOMER_ZIP_CODE,
        name: __('Buyer $$(ZIP Code)'),
        type: FILTER_TYPES.STRING,
        additional: {
          operators: OPERATORS_TO_SUPPORT,
        },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.DEAL_TAX_CODE,
        name: __('Tax Code'),
        type: 'MULTI_SELECT',
        additional: { options: getTaxCodeFilterOptions(taxCodes) },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.REASON_FOR_TAX_EXEMPTION,
        name: __('Buyer Tax Exempt Reason'),
        type: 'MULTI_SELECT',
        additional: { options: getOptionsByKeyValuePair(REASON_FOR_EXEMPT) },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.TAX_EXEMPT,
        name: __('Buyer Tax Exempt'),
        type: 'SINGLE_SELECT',
        additional: { options: BOOLEAN_OPTIONS },
      },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.IN_STATE,
        name: __('In State'),
        type: 'SINGLE_SELECT',
        additional: { options: BOOLEAN_OPTIONS },
      },
      ...createFilterTypesFromColumnMetaData(columnMetaData, extras, FILTER_KEYS),
      { ...DEAL_CONFIRMED_FILTER },
      {
        id: DEAL_SALES_LOG_FILTER_TYPES.ECONTRACT_STATUS,
        name: __('Econtract Status'),
        type: 'MULTI_SELECT',
        additional: {
          options: _map(ECONTRACT_STATUSES, statusKey => ({
            value: statusKey,
            label: ECONTRACT_STATUS_LABELS[statusKey],
          })),
        },
      },
    ];

    if (_size(dealerOemSites)) {
      const sites = makeSiteOptions(dealerOemSites);
      filterTypes.push({
        id: DEAL_SALES_LOG_FILTER_TYPES.SOLD_AT_SITE,
        name: __('Sold at'),
        type: FILTER_TYPES.MULTI_SELECT,
        additional: {
          options: sites,
        },
      });

      filterTypes.push({
        id: DEAL_SALES_LOG_FILTER_TYPES.STOCKED_IN_VEHICLE_SITE,
        name: __('Stocked-in at'),
        type: FILTER_TYPES.MULTI_SELECT,
        additional: {
          options: sites,
        },
      });
    }

    return filterTypes;
  }
);

export const DEFAULT_FILTER = [DATE_RANGE.id];

export const getUserDropDownList = userlist =>
  _map(userlist, ({ displayName: label, userID: value }) => ({ label, value }));

export const defaultFilters = multiSiteEnabled =>
  !multiSiteEnabled
    ? [
        {
          operator: 'IN',
          type: DEAL_SALES_LOG_FILTER_TYPES.SOLD_AT_SITE,
          values: ['CURRENT_SITE'],
        },

        {
          operator: 'IN',
          type: DEAL_SALES_LOG_FILTER_TYPES.STOCKED_IN_VEHICLE_SITE,
          values: ['CURRENT_SITE'],
        },
      ]
    : [];
// _slice(_map(getPresetList()[PRESET_ID.MONTH_TO_DATE], getMomentValueOf), 0, 2)
export const getInitialFilters = () => [
  {
    operator: 'BTW',
    type: DATE_RANGE.id,
    values: [],
  },
];

export const getDealTypeVsDisplayName = defaultMemoize(dealTypes =>
  (dealTypes || []).reduce(
    (dealTypesVsDisplayName, { dealType, displayName }) => ({
      ...dealTypesVsDisplayName,
      [dealType]: displayName,
    }),
    {}
  )
);

const appendCustomReportId = reportRouteId =>
  reportRouteId ? `${MODULE_ASSET_TYPES.SALES_PERFORMANCE}_${reportRouteId}` : MODULE_ASSET_TYPES.SALES_PERFORMANCE;

export const getFilterProps = defaultMemoize(
  (
    filters,
    onTableDropDownFilterChange,
    tableFilters,
    selectedFilterGroup,
    reportRouteId,
    dealTypesFromSetup,
    dealCustomStatuses,
    lendersInfo,
    columnMetaData,
    sourceTypeOptions,
    dealMetadata,
    dealerOemSites,
    taxCodes,
    dealSetupInfo,
    rolesList,
    isEnterpriseV2Enabled = false,
    enterpriseV2Workspaces = EMPTY_ARRAY,
    accessibleDealers
  ) => ({
    filterTypes: getFilterTypes(
      {
        ...filters,
        dealTypes: dealTypesFromSetup,
        dealCustomStatuses,
        lendersInfo,
        dealMetadata,
        taxCodes,
        dealSetupInfo,
        rolesList,
        tableFilters,
      },
      columnMetaData,
      sourceTypeOptions,
      dealerOemSites,
      isEnterpriseV2Enabled,
      enterpriseV2Workspaces,
      accessibleDealers
    ),
    defaultFilterTypes: DEFAULT_FILTER,
    onAction: onTableDropDownFilterChange,
    shouldForwardAction: true,
    appliedFilterGroup: selectedFilterGroup,
    assetType: appendCustomReportId(reportRouteId),
    selectedFilters: tableFilters,
    showFilterTrigger: true,
    defaultFilterBehavior: DEFAULT_FILTER_BEHAVIOR.PERSIST,
  })
);

export const getReportRouteId = defaultMemoize(path => {
  const pathArray = _split(path, DELIMITER);
  const reportRouteId = pathArray[pathArray.length - 1];

  return reportRouteId === SALES_PERF_ROUTE_ID ? EMPTY_STRING : reportRouteId;
});

export const getYMMKeyIndex = key => Number(_split(key, '_')[1]) - 1 || 0;

export const getSubStatusOptionsForFlagPicker = defaultMemoize(dealSubStatusList =>
  _map(
    _filter(dealSubStatusList, ({ enabled }) => enabled),
    ({ status, subStatusId, flag, enabled }) => ({
      label: status,
      value: subStatusId,
      flagColor: flag,
      enabled,
    })
  )
);

export const modifyReportsPayload = (flatTableReportingRequest, customReportId, selectedGroupBy) => ({
  flatTableReportingRequest,
  customReportId: customReportId || null,
  groupBys: selectedGroupBy,
});

export const showNoticeOfSalesReportsView = defaultMemoize(
  (permissions, moduleName) =>
    moduleName === MODULE_ASSET_TYPES.DEAL_SALES_LOG &&
    permissionValidations.validatePermissions(permissions, {
      validFor: [PERMISSIONS.CORE.REPORTS.DOWNLOAD_NOTICE_OF_SALES_REPORT],
    })
);

export const updateCustomGourpByKey = defaultMemoize(columnKeyVsField => ({
  ...columnKeyVsField,
  MAKE: DealerPropertyHelper.standardizedMakeSetupEnabled()
    ? {
        ...columnKeyVsField?.MAKE,
        field: DEAL_SALES_LOG_FILTER_TYPES.DISPLAY_MAKE,
        key: KEYS.DISPLAY_MAKE,
      }
    : columnKeyVsField?.MAKE,
}));

export const updateSortDataWithCustomField = defaultMemoize(sort =>
  _map(sort, sortData => {
    if (sortData?.key === KEYS.MAKE) {
      return {
        ...sortData,
        field: makeSortKeyWhenDealerPropertyEnabled({
          isStandardMake: DealerPropertyHelper.standardizedMakeSetupEnabled(),
          fallbackKey: sortData.field,
        }),
      };
    }
    return sortData;
  })
);

export const makeSortKeyWhenDealerPropertyEnabled = defaultMemoize(({ isStandardMake = false, fallbackKey }) =>
  isStandardMake ? DEAL_SALES_LOG_FILTER_TYPES.DISPLAY_MAKE : fallbackKey
);

export const replaceNoneAsEmptyString = filters =>
  _map(filters, ({ values, ...rest }) => {
    const modifledValues = _map(values, item => _replace(item, 'NONE', EMPTY_STRING));
    return {
      ...rest,
      values: modifledValues,
    };
  });

export const addProductProviderIdToProducts = products =>
  (products || []).map(item => {
    const productId = _get(item, 'product.id');
    const providerId = _get(item, 'provider.id');
    return {
      ...item,
      productProviderId: (productId || EMPTY_STRING).concat(providerId || EMPTY_STRING),
    };
  });

export const getAccessor = ({
  key,
  defaultAccessor,
  columnMetaData,
  reportsFilter,
  selectedGroupBy,
  tableFilters,
  activityList,
}) => {
  const filters = [...reportsFilter, ...tableFilters]; // Handle if Salesperson filter is applied
  let accessor = null;
  const fieldName = _get(columnMetaData, [key, 'fieldName']);

  switch (key) {
    case KEYS.DEAL_NUMBER:
      accessor = 'dealNumber';
      break;

    case KEYS.TRADE_YMM:
    case KEYS.TRADE_1_YMM:
    case KEYS.TRADE_2_YMM:
    case KEYS.TRADE_3_YMM:
    case KEYS.TRADE_4_YMM:
    case KEYS.TRADE_5_YMM:
      accessor = getTradeIns(getYMMKeyIndex(key));
      break;

    case KEYS.CUSTOMER_NUMBER: {
      accessor = 'primaryCustomer.displayId';
      break;
    }

    case KEYS.STOCK_ID:
      accessor = getPrimaryVehicleStockNumber;
      break;

    case KEYS.BUYER_FEEDBACK:
      accessor = getDataFromActivityList(activityList, COLUMN_ACCESSOR[key]);
      break;

    case KEYS.CO_BUYER_FEEDBACK:
      accessor = getDataFromActivityList(activityList, COLUMN_ACCESSOR[key]);
      break;

    case KEYS.DEAL_SUB_STATUS: {
      accessor = 'subStatusId';
      break;
    }

    case KEYS.SOURCE_TYPE: {
      accessor = 'primaryVehicle.sourceInfo.source';
      break;
    }

    case KEYS.DEAL_NOTE:
    case KEYS.CUSTOMER:
    case KEYS.TAX_CODE:
      break;

    default: {
      const GDPR_OBJECT = _includes(selectedGroupBy, key) ? null : GDPR_CONFIGS[key];
      if (!_isEmpty(GDPR_OBJECT)) {
        return accessor || defaultAccessor;
      }
      if (_includes(KEYS_VIA_FIELD_NAME, key)) {
        accessor = CUSTOM_ACCESSOR_VIA_FIELD_NAME[key](fieldName);
      } else {
        // Has Custom Accessor ?
        accessor = COLUMN_VS_CUSTOM_ACCESSOR({ filters, activityList, selectedGroupBy, reportsFilter })[key];
      }

      // If Custom accessor is not present get from column metadata
      if (!accessor) {
        accessor = fieldName;
      }
      return accessor || defaultAccessor;
    }
  }
  return accessor || defaultAccessor;
};

export const getPrimaryVehicleValue = (obj, value, currentDealerId) => {
  const { primaryVehicle, temp, dealerId } = obj;

  if (!primaryVehicle) return EMPTY_STRING;
  if (temp) return EMPTY_STRING;

  const renderValue = value || __('Build');
  const isCurrentDealershipDeal = _toString(dealerId) === _toString(currentDealerId);

  if (value && isCurrentDealershipDeal) {
    return (
      <a href={`/vi/vehicle/${_get(primaryVehicle, 'id')}`} target="_blank">
        {renderValue}
      </a>
    );
  }

  return renderValue;
};

export const getModuleName = (defaultReportFlag, customReportId, MODULE_NAME) =>
  defaultReportFlag ? customReportId : MODULE_NAME;
