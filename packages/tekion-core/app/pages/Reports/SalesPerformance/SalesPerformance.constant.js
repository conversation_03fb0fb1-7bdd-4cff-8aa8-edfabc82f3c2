import { defaultMemoize } from 'reselect';

import _get from 'lodash/get';
import _property from 'lodash/property';
import _identity from 'lodash/identity';
import _toUpper from 'lodash/toUpper';
import _round from 'lodash/round';
import _flow from 'lodash/flow';
import _isBoolean from 'lodash/isBoolean';
import _isString from 'lodash/isString';
import _isObject from 'lodash/isObject';
import _includes from 'lodash/includes';
import _noop from 'lodash/noop';
import _isFunction from 'lodash/isFunction';

import MODULE_ASSET_TYPES from 'tbase/constants/moduleAssetTypes';
import { TABLE_TYPES } from '@tekion/tekion-widgets/src/organisms/tableManager';
import { VEHICLE_CATEGORIES } from '@tekion/tekion-base/marketScan/constants/constants';
import { formatDecimalWithoutRounding } from 'tbase/formatters/number';
import { DATE_TIME_FORMAT } from '@tekion/tekion-conversion-web';
import { getDifferenceAsDays, toMoment } from '@tekion/tekion-base/utils/dateUtils';
import { formatCurrencyByLocal } from '@tekion/tekion-base/formatters/formatCurrency';
import { DEAL_STATUS_DISPLAY_MAP_V2 } from 'tbase/constants/deal/status';
import { mapBoolToYesOrNo } from 'tbase/utils/sales';
import { APPLICATION_STATUS } from 'tbase/constants/retail/credit.constants.js';

import AssigneeRenderer from 'molecules/cellRenderer/AssigneeRenderer';
import { getDaysFromDuration } from 'utils/dateHelper';
import {
  fillBlank,
  checkFalsyExceptZero,
  snakeCaseToStartCase,
  fillBlankExceptZero,
  transformObjectValuesToString,
  appendText,
  negativeToZero,
  mapBooleanToString,
  roundOff,
} from 'utils/helpers';
import { formatAsPercentage } from 'utils/sales.helpers';
import { getVehicleAge, getVehicleAgeFromDuration } from 'utils/vehicle';
import { COMMON_SALES_INVENTORY_KEYS } from 'constants/reports';
import { EXPORT_TYPES } from '@tekion/tekion-base/constants/reports.constants';
import { SALES_EXPORT_FILE_TYPES } from '@tekion/tekion-business/src/constants/coreReports';

import * as Reader from './SalesPerformance.reader';
import { getDealerNameById } from '../reports.helper';
import RenderArrayCell from './components/RenderArrayCell';
import FniProductCellRenderer from './components/FniProductCellRenderer';
import { getEnterpriseDealerNameById } from './SalesPerformance.reader';

export const API_STATUS = {
  SUCCESS: 'success',
};

const REBATE_TYPE = {
  REBATE: 'REBATE',
  DEALER_CASH: 'DEALER_CASH',
};

export const REDUCER_NAME = 'salesPerformance';

export const MODULE_NAME = MODULE_ASSET_TYPES.DEAL_SALES_LOG;

export const TABLE_ROW_HEIGHT = 40;

export const TABLE_MANAGER_PROPS = {
  showFilter: true,
  showHeader: true,
  showSubHeader: true,
  showMultiSort: true,
};

export const VEHICLE_DELIVERED_FILTER = [
  {
    label: __('Delivered'),
    value: true,
  },
  {
    label: __('Not Delivered'),
    value: false,
  },
];

export const VEHICLE_CERTIFIED_FILTER = [
  {
    label: __('Certified'),
    value: true,
  },
  {
    label: __('Not Certified'),
    value: false,
  },
];

export const VEHICLE_CATEGORY_OPTIONS = [
  { value: VEHICLE_CATEGORIES.CAR, label: __('Automotive') },
  { value: VEHICLE_CATEGORIES.RV, label: __('RV') },
];

export const CONCIERGE_ACTIVATED = [
  {
    label: __('YES'),
    value: true,
  },
  {
    label: __('NO'),
    value: false,
  },
];

export const REBATE_TYPE_OPTIONS = [
  {
    label: __('Rebate'),
    value: REBATE_TYPE.REBATE,
  },
  {
    label: __('Dealer Cash'),
    value: REBATE_TYPE.DEALER_CASH,
  },
];

export const MIGRATED_OPTIONS = [
  {
    label: __('Migrated'),
    value: true,
  },
  {
    label: __('Not Migrated'),
    value: false,
  },
];

export const DEFAULT_TABLE_PROPS = {
  showPagination: true,
  minRows: 0,
  rowHeight: 40,
  type: TABLE_TYPES.FIXED_COLUMN,
};

export const HEADER_PROPS = {
  label: __('Sales Log | Sales'),
  hasBack: true,
};

export const AGGREGATION_FIELD_TYPES = {
  GROUPBY: 'GROUPBY',
  PROJECTION: 'PROJECTION',
};

const RENDERER_TYPE = {
  TEXT: 'TEXT',
  PRICE: 'PRICE',
  USER: 'USER',
  DATE: 'DATE',
  NUMBER: 'NUMBER',
  BOOLEAN: 'BOOLEAN',
  NONE: 'NONE',
  ARRAY: 'ARRAY',
  EMPLOYEE_DISPLAY_NUMBER: 'EMPLOYEE_DISPLAY_NUMBER',
  PERCENTAGE: 'PERCENTAGE',
};

const Price = [checkFalsyExceptZero, roundOff, formatCurrencyByLocal, fillBlank];
const Text = [snakeCaseToStartCase, fillBlank];
const User = [Reader.getUserName, snakeCaseToStartCase, fillBlank];
const EmployeeDisplayNumber = [Reader.getEmployeeDisplayNumber, fillBlank];

const DateFormat = getFormattedDateAndTime => value => {
  if (_isFunction(getFormattedDateAndTime) && value) {
    return getFormattedDateAndTime({
      value,
      formatType: DATE_TIME_FORMAT.ABBREVIATED_DAY_DATE_ABBREVIATED_MONTH_YEAR,
    });
  }
  return fillBlank();
};
const DateTimeFormat = getFormattedDateAndTime => value => {
  if (_isFunction(getFormattedDateAndTime) && value) {
    return getFormattedDateAndTime({
      value,
      formatType: DATE_TIME_FORMAT.ABBREVIATED_DAY_DATE_ABBREVIATED_MONTH_YEAR_WITH_HOUR_MINUTE,
    });
  }
  return fillBlank();
};

const Numeric = value =>
  checkFalsyExceptZero(value) ? formatDecimalWithoutRounding(value) : fillBlankExceptZero(value);
// For reports duration comes whereas for deals it is calculated based on vehicle entryTime
const VehicleAge = (agingField, getFormattedNumber, dealType) => [
  value => (typeof value === 'object' ? getVehicleAge(value, agingField, dealType) : getVehicleAgeFromDuration(value)),
  negativeToZero,
  getFormattedNumber,
  appendText(__('days')),
];
const roundOfWithPrecision = precision => value => _round(value, precision);
const roundOf = value => (value ? _round(value) : 0);

const QuoteAge = getFormattedNumber => [
  value =>
    typeof value === 'object'
      ? getDifferenceAsDays(toMoment(value.endTime), toMoment(value.startTime))
      : getVehicleAgeFromDuration(value),
  negativeToZero,
  appendText(__('days')),
];

const BOOLEAN_DISPLAY_MAPPER = {
  true: 'Yes',
  false: 'No',
};

const stringifyBool = [
  value => {
    // in reports API everything is string
    if (_isString(value)) return BOOLEAN_DISPLAY_MAPPER[value];
    if (_isBoolean(value)) {
      if (value) {
        return BOOLEAN_DISPLAY_MAPPER.true;
      }
      return BOOLEAN_DISPLAY_MAPPER.false;
    }
    return null;
  },
  fillBlank,
];

const makeRenderer = data => {
  if (_isObject(data)) {
    return data?.displayMake || _flow(Text)(data?.make);
  }
  return data;
};

export const KEYS = {
  DEAL_STATUS: 'DEAL_STATUS',
  LENDER: 'LENDER',
  STOCK_ID: 'stockID',
  PROMISED_DELIVERY_DATE: 'promisedDeliveryDate',
  DEAL_NUMBER: 'dealNumber',
  CREDIT_APPLICATION_STATUS: 'creditApplicationStatus',
  DEAL_SUB_STATUS: 'DEAL_SUB_STATUS',
  QUOTE_AGE: 'QUOTE_AGE',
  VEHICLE: 'vehicle',
  TRIM: 'trim',
  DEAL_COUNT_SUM: 'DEAL_COUNT_SUM',
  UNIT_COUNT: 'UNIT_COUNT',
  SALES_PERSON: 'SALES_PERSON',
  SALES_MANAGER: 'SALES_MANAGER',
  CLOSOING_MANAGER: 'closingManager',
  FINANCE_MANAGER: 'FINANCE_MANAGER',
  CUSTOMER: 'customer',
  CUSTOMER_ADDRESS: 'CUSTOMER_ADDRESS',
  TRADE_YMM: 'tradeYMM',
  TRADE_1_YMM: 'TRADE_1_YMM',
  TRADE_2_YMM: 'TRADE_2_YMM',
  TRADE_3_YMM: 'TRADE_3_YMM',
  TRADE_4_YMM: 'TRADE_4_YMM',
  TRADE_5_YMM: 'TRADE_5_YMM',
  TOTAL_GROSS_PACE: 'TOTAL_GROSS_PACE',
  VEHICLE_AGE: 'vehicleAge',
  ZIP_CODE: 'ZIP_CODE',
  DELIVERY_DONE: 'DELIVERY_DONE',
  CERTIFIED: 'CERTIFIED',
  CONCIERGE_ACTIVATED: 'CONCIERGE_ACTIVATED',
  DEAL_CATEGORY: 'DEAL_CATEGORY',

  FRONT_GROSS_AMT_SUM: 'FRONT_GROSS_AMT_SUM',
  FRONT_GROSS_AMT_AVG: 'FRONT_GROSS_AMT_AVG',
  BACK_GROSS_AMT_SUM: 'BACK_GROSS_AMT_SUM',
  BACK_GROSS_AMT_AVG: 'BACK_GROSS_AMT_AVG',
  TOTAL_GROSS_AMT_SUM: 'TOTAL_GROSS_AMT_SUM',
  TOTAL_GROSS_AMT_AVG: 'TOTAL_GROSS_AMT_AVG',
  VEHICLE_GROSS_SUM: 'VEHICLE_GROSS_SUM',
  VEHICLE_GROSS_AVG: 'VEHICLE_GROSS_AVG',
  BACK_ACC_GROSS_AMT_SUM: 'BACK_ACC_GROSS_AMT_SUM',
  BACK_ACC_GROSS_AMT_AVG: 'BACK_ACC_GROSS_AMT_AVG',
  COMMISSION_GROSS: 'COMMISSION_GROSS',
  FNI_GROSS_AMT_AVG: 'FNI_GROSS_AMT_AVG',
  FNI_GROSS_AMT_SUM: 'FNI_GROSS_AMT_SUM',

  BOOKING_SNAPSHOT_FRONT_GROSS_AMT_SUM: 'BOOKING_SNAPSHOT_FRONT_GROSS_AMT_SUM',
  BOOKING_SNAPSHOT_FRONT_GROSS_AMT_AVG: 'BOOKING_SNAPSHOT_FRONT_GROSS_AMT_AVG',
  BOOKING_SNAPSHOT_BACK_GROSS_AMT_SUM: 'BOOKING_SNAPSHOT_BACK_GROSS_AMT_SUM',
  BOOKING_SNAPSHOT_BACK_GROSS_AMT_AVG: 'BOOKING_SNAPSHOT_BACK_GROSS_AMT_AVG',
  BOOKING_SNAPSHOT_TOTAL_GROSS_AMT_SUM: 'BOOKING_SNAPSHOT_TOTAL_GROSS_AMT_SUM',
  BOOKING_SNAPSHOT_TOTAL_GROSS_AMT_AVG: 'BOOKING_SNAPSHOT_TOTAL_GROSS_AMT_AVG',

  SALE_SNAPSHOT_FRONT_GROSS_AMT_SUM: 'SALE_SNAPSHOT_FRONT_GROSS_AMT_SUM',
  SALE_SNAPSHOT_FRONT_GROSS_AMT_AVG: 'SALE_SNAPSHOT_FRONT_GROSS_AMT_AVG',
  SALE_SNAPSHOT_BACK_GROSS_AMT_SUM: 'SALE_SNAPSHOT_BACK_GROSS_AMT_SUM',
  SALE_SNAPSHOT_BACK_GROSS_AMT_AVG: 'SALE_SNAPSHOT_BACK_GROSS_AMT_AVG',
  SALE_SNAPSHOT_TOTAL_GROSS_AMT_SUM: 'SALE_SNAPSHOT_TOTAL_GROSS_AMT_SUM',
  SALE_SNAPSHOT_TOTAL_GROSS_AMT_AVG: 'SALE_SNAPSHOT_TOTAL_GROSS_AMT_AVG',

  SALES_PERSON_SPIFF_SUM: 'SALES_PERSON_SPIFF_SUM',
  SALES_PERSON_SPIFF_AVG: 'SALES_PERSON_SPIFF_AVG',
  SALES_PERSON_COMMISSION_SUM: 'SALES_PERSON_COMMISSION_SUM',
  SALES_PERSON_COMMISSION_AVG: 'SALES_PERSON_COMMISSION_AVG',
  SALES_PERSON_TOTAL_COMMISSION_SUM: 'SALES_PERSON_TOTAL_COMMISSION_SUM',
  SALES_PERSON_TOTAL_COMMISSION_AVG: 'SALES_PERSON_TOTAL_COMMISSION_AVG',

  SALES_MANAGER_COMMISSION_AVG: 'SALES_MANAGER_COMMISSION_AVG',
  SALES_MANAGER_COMMISSION_SUM: 'SALES_MANAGER_COMMISSION_SUM',
  SALES_MANAGER_SPIFF_AVG: 'SALES_MANAGER_SPIFF_AVG',
  SALES_MANAGER_SPIFF_SUM: 'SALES_MANAGER_SPIFF_SUM',
  SALES_MANAGER_TOTAL_COMMISSION_AVG: 'SALES_MANAGER_TOTAL_COMMISSION_AVG',
  SALES_MANAGER_TOTAL_COMMISSION_SUM: 'SALES_MANAGER_TOTAL_COMMISSION_SUM',

  FINANCE_MANAGER_SPIFF_SUM: 'FINANCE_MANAGER_SPIFF_SUM',
  FINANCE_MANAGER_SPIFF_AVG: 'FINANCE_MANAGER_SPIFF_AVG',
  FINANCE_MANAGER_COMMISSION_SUM: 'FINANCE_MANAGER_COMMISSION_SUM',
  FINANCE_MANAGER_COMMISSION_AVG: 'FINANCE_MANAGER_COMMISSION_AVG',
  FINANCE_MANAGER_TOTAL_COMMISSION_SUM: 'FINANCE_MANAGER_TOTAL_COMMISSION_SUM',
  FINANCE_MANAGER_TOTAL_COMMISSION_AVG: 'FINANCE_MANAGER_TOTAL_COMMISSION_AVG',

  CONTRACT_AGE: 'CONTRACT_AGE',
  CONTRACT_AGE_AVG: 'CONTRACT_AGE_AVG',
  QUOTE_AGE_AVG: 'QUOTE_AGE_AVG',
  DEALER: 'dealerId',
  TAX_EXEMPT: 'TAX_EXEMPT',
  STATE: 'STATE',
  VEHICLE_AGE_AVG: 'VEHICLE_AGE_AVG',
  PENDING_DOWN_PAYMENT_SUM: 'PENDING_DOWN_PAYMENT_SUM',

  OSF: 'OSF',
  DEAL_NOTE: 'DEAL_NOTE',
  RECEIVED_DATE: 'RECEIVED_DATE',
  SOURCE_TYPE: 'SOURCE_TYPE',
  INTERIOR_COLOR: 'INTERIOR_COLOR',
  EXTERIOR_COLOR: 'EXTERIOR_COLOR',
  INVOICE_PRICE: 'INVOICE_PRICE',
  CREATED_DATE: 'CREATED_DATE',

  BUYER_FEEDBACK: 'BUYER_FEEDBACK',
  BUYER_RATING: 'BUYER_RATING',
  CO_BUYER_FEEDBACK: 'CO_BUYER_FEEDBACK',
  CO_BUYER_RATING: 'CO_BUYER_RATING',

  CUSTOMER_PORTAL_LINK_SHARED: 'CUSTOMER_PORTAL_LINK_SHARED',
  CUSTOMER_PORTAL_LINK_OPENS: 'CUSTOMER_PORTAL_LINK_OPENS',

  DOCUMENTS_UPLOADED: 'DOCUMENTS_UPLOADED',
  DOCUMENTS_SIGNED: 'DOCUMENTS_SIGNED',
  VEHICLE_WALKTHROUGH_ADDED: 'VEHICLE_WALKTHROUGH_ADDED',
  VEHICLE_WALKTHROUGH_WATCHED: 'VEHICLE_WALKTHROUGH_WATCHED',
  CREDIT_APPLICATION: 'CREDIT_APPLICATION',
  CREDIT_APPLICATION_STATUS: 'CREDIT_APPLICATION_STATUS',

  DEAL_SHEET_SHARED: 'DEAL_SHEET_SHARED',
  FNI_SHEET_SHARED: 'F&I_SHEET_SHARED',
  ONLINE_SESSIONS: 'ONLINE_SESSIONS',
  ...COMMON_SALES_INVENTORY_KEYS,
  NEW_VEHICLE_COUNT: 'NEW_VEHICLE_COUNT',
  USED_VEHICLE_COUNT: 'USED_VEHICLE_COUNT',
  LEASE_VEHICLE_COUNT: 'LEASE_VEHICLE_COUNT',
  LEASE_VEHICLE_PENETRATION: 'LEASE_VEHICLE_PENETRATION',
  CASH_VEHICLE_COUNT: 'CASH_VEHICLE_COUNT',
  CASH_VEHICLE_PENETRATION: 'CASH_VEHICLE_PENETRATION',
  LOAN_VEHICLE_COUNT: 'LOAN_VEHICLE_COUNT',
  LOAN_VEHICLE_PENETRATION: 'LOAN_VEHICLE_PENETRATION',
  PRODUCT_INDEX_AVG: 'PRODUCT_INDEX_AVG',
  DISCLOSURE_KEY: 'DISCLOSURE_KEY',
  FNI_GROUP_KEY: 'FNI_GROUP_KEY',
  PRESOLD_FNI_GROUP_KEY: 'PRESOLD_FNI_GROUP_KEY',
  TRADEINS: 'TRADEINS',
  TAX_CODE: 'taxCode',
  APR: 'APR',
  BUY_RATE: 'BUY_RATE',
  MARKUP: 'MARKUP',
  BUYER_LAST_FIRST_NAME: 'BUYER_LAST_FIRST_NAME',
  COBUYER_LAST_FIRST_NAME: 'COBUYER_LAST_FIRST_NAME',
  SOLD_AT_SITE_ID: 'soldAtSiteId',
  STOCKED_IN_SITE_ID: 'stockedInAtSiteId',
  TAXABLE_STATE: 'TAXABLE_STATE',
  BUYER_STATE: 'BUYER_STATE',
  DEAL_CONFIRMED: 'DEAL_CONFIRMED',
  REBATE_PROGRAM_NAME: 'REBATE_PROGRAM_NAME',
  REBATE_PROGRAM_CODE: 'REBATE_PROGRAM_CODE',
  REBATE_PROGRAM_NUMBER: 'REBATE_PROGRAM_NUMBER',
  RECIPROCAL_CATEGORY: 'RECIPROCAL_CATEGORY',
  COST_ADJUSTMENT_DESCRIPTION: 'COST_ADJUSTMENT_DESCRIPTION',
  MAKE: 'MAKE',
  DISPLAY_MAKE: 'displayMake',
  CCR_TAX: 'CCR_TAX',
  STATE_TAX_RATE_PCT: 'STATE_TAX_RATE_PCT',
  CITY_TAX_RATE_PCT: 'CITY_TAX_RATE_PCT',
  CITY_TAX_AMOUNT: 'CITY_TAX_AMOUNT',
  COUNTY_TAX_RATE_PCT: 'COUNTY_TAX_RATE_PCT',
  COUNTY_TAX_AMOUNT: 'COUNTY_TAX_AMOUNT',
  MONTHLY_USE_TAX_AMOUNT: 'MONTHLY_USE_TAX_AMOUNT',
  STATE_TAX_AMOUNT: 'STATE_TAX_AMOUNT',
  CCR_TAX_RATE: 'CCR_TAX_RATE',
  FLAT_TAX_AMOUNT: 'FLAT_TAX_AMOUNT',
  DOC_FEE_TAX: 'DOC_FEE_TAX',

  BUYER_FIRST_NAME: 'BUYER_FIRST_NAME',
  BUYER_MIDDLE_NAME: 'BUYER_MIDDLE_NAME',
  BUYER_LAST_NAME: 'BUYER_LAST_NAME',
  COBUYER_FIRST_NAME: 'COBUYER_FIRST_NAME',
  COBUYER_MIDDLE_NAME: 'COBUYER_MIDDLE_NAME',
  COBUYER_LAST_NAME: 'COBUYER_LAST_NAME',
  BUYER_STREET_ADDRESS: 'BUYER_STREET_ADDRESS',
  COBUYER_STREET_ADDRESS: 'COBUYER_STREET_ADDRESS',
  BUYER_CITY: 'BUYER_CITY',
  COBUYER_CITY: 'COBUYER_CITY',
  BUYER_COUNTY: 'BUYER_COUNTY',
  COBUYER_STATE: 'COBUYER_STATE',
  CUSTOMER_NUMBER: 'customerNumber',
  CUSTOMER_EMAIL_ID: 'CUSTOMER_EMAIL_ID',
  COBUYER_NUMBER: 'coBuyerNumber',
  CUSTOMER_WORK_PHONE: 'CUSTOMER_WORK_PHONE',
  CUSTOMER_MOBILE_NUMBER: 'CUSTOMER_MOBILE_NUMBER',
  CUSTOMER_HOME_PHONE: 'CUSTOMER_HOME_PHONE',
  BUYER_ADDRESS_LINE_1: 'BUYER_ADDRESS_LINE_1',
  BUYER_ADDRESS_LINE_2: 'BUYER_ADDRESS_LINE_2',
  TITLE_BUYER: 'TITLE_BUYER',
  TITLE_COBUYER: 'TITLE_COBUYER',
  VEHICLE_DELIVERY_DATE: 'vehicleDeliveryDate',
};

const DATE_TIME_FIELDS = [KEYS.PROMISED_DELIVERY_DATE, KEYS.VEHICLE_DELIVERY_DATE];

export const COLUMN_VS_RENDERER_TYPE = ({ filters, key, fieldName, getFormattedDateAndTime }) => ({
  [RENDERER_TYPE.TEXT]: Text,
  [RENDERER_TYPE.PRICE]: Price,
  [RENDERER_TYPE.PERCENTAGE]: [roundOfWithPrecision(2), formatAsPercentage],
  [RENDERER_TYPE.USER]: User,
  [RENDERER_TYPE.DATE]: _includes(DATE_TIME_FIELDS, fieldName)
    ? DateTimeFormat(getFormattedDateAndTime)
    : DateFormat(getFormattedDateAndTime),
  [RENDERER_TYPE.NUMBER]: Numeric,
  [RENDERER_TYPE.NONE]: [_identity],
  [RENDERER_TYPE.BOOLEAN]: mapBooleanToString,
  [RENDERER_TYPE.ARRAY]: RenderArrayCell(filters, key),
  [RENDERER_TYPE.EMPLOYEE_DISPLAY_NUMBER]: EmployeeDisplayNumber,
});

export const COLUMN_META_VS_CUSTOM_RENDERER = {
  [KEYS.DEAL_CONFIRMED]: mapBoolToYesOrNo,
};

export const KEYS_VIA_FIELD_NAME = [
  KEYS.NEW_VEHICLE_COUNT,
  KEYS.USED_VEHICLE_COUNT,
  KEYS.LEASE_VEHICLE_COUNT,
  KEYS.LEASE_VEHICLE_PENETRATION,
  KEYS.CASH_VEHICLE_COUNT,
  KEYS.CASH_VEHICLE_PENETRATION,
  KEYS.LOAN_VEHICLE_COUNT,
  KEYS.LOAN_VEHICLE_PENETRATION,
  KEYS.PRODUCT_INDEX_AVG,
];

export const CUSTOM_ACCESSOR_VIA_FIELD_NAME = {
  [KEYS.NEW_VEHICLE_COUNT]: Reader.getNewVehicleCountForDeal,
  [KEYS.USED_VEHICLE_COUNT]: Reader.getUsedVehicleCountForDeal,
  [KEYS.LEASE_VEHICLE_COUNT]: Reader.getLeaseVehicleCountForDeal,
  [KEYS.LEASE_VEHICLE_PENETRATION]: Reader.getLeaseVehiclePenetration,
  [KEYS.CASH_VEHICLE_COUNT]: Reader.getCashVehicleCountForDeal,
  [KEYS.CASH_VEHICLE_PENETRATION]: Reader.getCashVehiclePenetrationForDeal,
  [KEYS.LOAN_VEHICLE_COUNT]: Reader.getLoanVehicleCountForDeal,
  [KEYS.LOAN_VEHICLE_PENETRATION]: Reader.getLoanVehiclePenetrationForDeal,
  [KEYS.PRODUCT_INDEX_AVG]: Reader.getProductIndexAvg,
};

export const COLUMN_ACCESSOR = {
  [KEYS.CUSTOMER_PORTAL_LINK_SHARED]: 'customerPortalLinkShared',
  [KEYS.CUSTOMER_PORTAL_LINK_OPENS]: 'portalLinkOpened',
  [KEYS.BUYER_FEEDBACK]: 'buyerFeedback.feedback',
  [KEYS.BUYER_RATING]: 'buyerFeedback.rating',
  [KEYS.CO_BUYER_FEEDBACK]: 'coBuyerFeedback.feedback',
  [KEYS.CO_BUYER_RATING]: 'coBuyerFeedback.rating',
  [KEYS.DOCUMENTS_UPLOADED]: 'documentUploadedCount',
  [KEYS.DOCUMENTS_SIGNED]: 'documentsSigned',
  [KEYS.VEHICLE_WALKTHROUGH_ADDED]: 'vehicleWalkthroughAdded',
  [KEYS.VEHICLE_WALKTHROUGH_WATCHED]: 'vehicleWalkthroughWatched',
  [KEYS.CREDIT_APPLICATION]: 'creditApplication',
  [KEYS.CREDIT_APPLICATION_STATUS]: 'creditApplicationStatus',
  [KEYS.DEAL_SHEET_SHARED]: 'dealSheetShared',
  [KEYS.FNI_SHEET_SHARED]: 'fniMenuSheetShared',
  [KEYS.ONLINE_SESSIONS]: 'onlineSessions',
};

// reportsFilter is groupby filters
export const COLUMN_VS_CUSTOM_ACCESSOR = defaultMemoize(({ activityList, selectedGroupBy, reportsFilter }) => ({
  [KEYS.DEAL_STATUS]: _property('status'),
  [KEYS.LENDER]: _identity,
  [KEYS.QUOTE_AGE]: rowData => Reader.getQuoteAge(rowData),
  [KEYS.VEHICLE]: Reader.getPrimaryVehicleYearMakeModel,
  [KEYS.TRIM]: Reader.getPrimaryVehicleTrim,
  [KEYS.SALES_PERSON]: _property('assignee.salesPersons'),
  [KEYS.SALES_MANAGER]: _property('assignee.salesManager'),
  [KEYS.FINANCE_MANAGER]: _property('assignee.financeManager'),
  [KEYS.CLOSOING_MANAGER]: _property('assignee.closingManager'),
  [KEYS.DEAL_COUNT_SUM]: Reader.getDealCount(reportsFilter, selectedGroupBy),
  [KEYS.UNIT_COUNT]: Reader.getUnitCount(reportsFilter),
  [KEYS.DEAL_CATEGORY]: _property('type'),

  [KEYS.SALES_PERSON_SPIFF_SUM]: Reader.getSalesPersonSpiffCommission(
    reportsFilter,
    ASSIGNEE_COMMISSION_CONFIG.SALES_PERSION,
    selectedGroupBy
  ),
  [KEYS.SALES_PERSON_SPIFF_AVG]: Reader.getSalesPersonSpiffCommission(
    reportsFilter,
    ASSIGNEE_COMMISSION_CONFIG.SALES_PERSION,
    selectedGroupBy
  ),
  [KEYS.SALES_PERSON_COMMISSION_SUM]: Reader.getSalesPersonBaseCommission(
    reportsFilter,
    ASSIGNEE_COMMISSION_CONFIG.SALES_PERSION,
    selectedGroupBy
  ),
  [KEYS.SALES_PERSON_COMMISSION_AVG]: Reader.getSalesPersonBaseCommission(
    reportsFilter,
    ASSIGNEE_COMMISSION_CONFIG.SALES_PERSION,
    selectedGroupBy
  ),
  [KEYS.SALES_PERSON_TOTAL_COMMISSION_SUM]: Reader.getSalesPersonTotalCommission(
    reportsFilter,
    ASSIGNEE_COMMISSION_CONFIG.SALES_PERSION,
    selectedGroupBy
  ),
  [KEYS.SALES_PERSON_TOTAL_COMMISSION_AVG]: Reader.getSalesPersonTotalCommission(
    reportsFilter,
    ASSIGNEE_COMMISSION_CONFIG.SALES_PERSION,
    selectedGroupBy
  ),

  [KEYS.SALES_MANAGER_SPIFF_SUM]: Reader.getSalesPersonSpiffCommission(
    reportsFilter,
    ASSIGNEE_COMMISSION_CONFIG.SALES_MANAGER,
    selectedGroupBy
  ),
  [KEYS.SALES_MANAGER_SPIFF_AVG]: Reader.getSalesPersonSpiffCommission(
    reportsFilter,
    ASSIGNEE_COMMISSION_CONFIG.SALES_MANAGER,
    selectedGroupBy
  ),
  [KEYS.SALES_MANAGER_COMMISSION_SUM]: Reader.getSalesPersonBaseCommission(
    reportsFilter,
    ASSIGNEE_COMMISSION_CONFIG.SALES_MANAGER,
    selectedGroupBy
  ),
  [KEYS.SALES_MANAGER_COMMISSION_AVG]: Reader.getSalesPersonBaseCommission(
    reportsFilter,
    ASSIGNEE_COMMISSION_CONFIG.SALES_MANAGER,
    selectedGroupBy
  ),
  [KEYS.SALES_MANAGER_TOTAL_COMMISSION_SUM]: Reader.getSalesPersonTotalCommission(
    reportsFilter,
    ASSIGNEE_COMMISSION_CONFIG.SALES_MANAGER,
    selectedGroupBy
  ),
  [KEYS.SALES_MANAGER_TOTAL_COMMISSION_AVG]: Reader.getSalesPersonTotalCommission(
    reportsFilter,
    ASSIGNEE_COMMISSION_CONFIG.SALES_MANAGER,
    selectedGroupBy
  ),

  [KEYS.FINANCE_MANAGER_SPIFF_SUM]: Reader.getSalesPersonSpiffCommission(
    reportsFilter,
    ASSIGNEE_COMMISSION_CONFIG.FINANCE_MANAGER,
    selectedGroupBy
  ),
  [KEYS.FINANCE_MANAGER_SPIFF_AVG]: Reader.getSalesPersonSpiffCommission(
    reportsFilter,
    ASSIGNEE_COMMISSION_CONFIG.FINANCE_MANAGER,
    selectedGroupBy
  ),
  [KEYS.FINANCE_MANAGER_COMMISSION_SUM]: Reader.getSalesPersonBaseCommission(
    reportsFilter,
    ASSIGNEE_COMMISSION_CONFIG.FINANCE_MANAGER,
    selectedGroupBy
  ),
  [KEYS.FINANCE_MANAGER_COMMISSION_AVG]: Reader.getSalesPersonBaseCommission(
    reportsFilter,
    ASSIGNEE_COMMISSION_CONFIG.FINANCE_MANAGER,
    selectedGroupBy
  ),
  [KEYS.FINANCE_MANAGER_TOTAL_COMMISSION_SUM]: Reader.getSalesPersonTotalCommission(
    reportsFilter,
    ASSIGNEE_COMMISSION_CONFIG.FINANCE_MANAGER,
    selectedGroupBy
  ),
  [KEYS.FINANCE_MANAGER_TOTAL_COMMISSION_AVG]: Reader.getSalesPersonTotalCommission(
    reportsFilter,
    ASSIGNEE_COMMISSION_CONFIG.FINANCE_MANAGER,
    selectedGroupBy
  ),

  [KEYS.TOTAL_GROSS_PACE]: _property(KEYS.TOTAL_GROSS_PACE),
  [KEYS.VEHICLE_AGE]: Reader.getPrimaryVehicle,
  [KEYS.CUSTOMER_ADDRESS]: Reader.getCustomerAddress,
  [KEYS.ZIP_CODE]: Reader.getCurrentZipCode,
  [KEYS.DELIVERY_DONE]: _property('vehicleDelivered'),
  [KEYS.CERTIFIED]: _property('primaryVehicle.certified'),
  [KEYS.CONCIERGE_ACTIVATED]: _property('conciergeActivated'),
  [KEYS.CONTRACT_AGE]: Reader.getContractAge,
  [KEYS.CONTRACT_AGE_AVG]: Reader.getContractAge,
  [KEYS.DEALER]: _property('dealerId'),
  [KEYS.TAX_EXEMPT]: _property('taxDetails.taxExempt'),
  [KEYS.STATE]: _property('taxDetails.state'),
  [KEYS.TAXABLE_STATE]: _property('taxAndZipCodeDetails.state'),
  [KEYS.BUYER_STATE]: _property('primaryCustomer.currentAddress.state'),
  [KEYS.PENDING_DOWN_PAYMENT_SUM]: _property('grossDetails.pendingDownPaymentAmt'),
  [KEYS.OSF]: _property('termPaymentDetails.osfLender'),

  [KEYS.BUYER_RATING]: Reader.getDataFromActivityList(activityList, COLUMN_ACCESSOR[KEYS.BUYER_RATING]),
  [KEYS.CO_BUYER_RATING]: Reader.getDataFromActivityList(activityList, COLUMN_ACCESSOR[KEYS.CO_BUYER_RATING]),
  [KEYS.CUSTOMER_PORTAL_LINK_SHARED]: Reader.getDataFromActivityList(
    activityList,
    COLUMN_ACCESSOR[KEYS.CUSTOMER_PORTAL_LINK_SHARED]
  ),
  [KEYS.CUSTOMER_PORTAL_LINK_OPENS]: Reader.getDataFromActivityList(
    activityList,
    COLUMN_ACCESSOR[KEYS.CUSTOMER_PORTAL_LINK_OPENS]
  ),
  [KEYS.DOCUMENTS_UPLOADED]: Reader.getDataFromActivityList(activityList, COLUMN_ACCESSOR[KEYS.DOCUMENTS_UPLOADED]),
  [KEYS.VEHICLE_WALKTHROUGH_ADDED]: Reader.getDataFromActivityList(
    activityList,
    COLUMN_ACCESSOR[KEYS.VEHICLE_WALKTHROUGH_ADDED]
  ),
  [KEYS.VEHICLE_WALKTHROUGH_WATCHED]: Reader.getDataFromActivityList(
    activityList,
    COLUMN_ACCESSOR[KEYS.VEHICLE_WALKTHROUGH_WATCHED]
  ),
  [KEYS.CREDIT_APPLICATION]: Reader.getDataFromActivityList(activityList, COLUMN_ACCESSOR[KEYS.CREDIT_APPLICATION]),
  [KEYS.CREDIT_APPLICATION_STATUS]: Reader.getDataFromActivityList(
    activityList,
    COLUMN_ACCESSOR[KEYS.CREDIT_APPLICATION_STATUS]
  ),
  [KEYS.DEAL_SHEET_SHARED]: Reader.getDataFromActivityList(activityList, COLUMN_ACCESSOR[KEYS.DEAL_SHEET_SHARED]),
  [KEYS.FNI_SHEET_SHARED]: Reader.getDataFromActivityList(activityList, COLUMN_ACCESSOR[KEYS.FNI_SHEET_SHARED]),
  [KEYS.ONLINE_SESSIONS]: Reader.getDataFromActivityList(activityList, COLUMN_ACCESSOR[KEYS.ONLINE_SESSIONS]),
  [KEYS.DOCUMENTS_SIGNED]: Reader.getDataFromActivityList(activityList, COLUMN_ACCESSOR[KEYS.DOCUMENTS_SIGNED]),
  [KEYS.FNI_GROUP_KEY]: _property('dealPayment.fnIsKeys'),
  [KEYS.PRESOLD_FNI_GROUP_KEY]: _property('dealPayment.ghostFnIsKeys'),
  [KEYS.APR]: Reader.getAPR,
  [KEYS.BUY_RATE]: Reader.getBuyRate,
  [KEYS.MARKUP]: Reader.getMarkUp,
  [KEYS.BUYER_LAST_FIRST_NAME]: Reader.getPrimaryCustomerLastAndFirstName,
  [KEYS.COBUYER_LAST_FIRST_NAME]: Reader.getSecondaryCustomerLastAndFirstName,
  [KEYS.SOLD_AT_SITE_ID]: _identity,
  [KEYS.STOCKED_IN_SITE_ID]: _identity,
  [KEYS.REBATE_PROGRAM_NAME]: Reader.getRebateProgramNames,
  [KEYS.REBATE_PROGRAM_CODE]: Reader.getRebateProgramCodes,
  [KEYS.REBATE_PROGRAM_NUMBER]: Reader.getRebateProgramNumbers,
  [KEYS.RECIPROCAL_CATEGORY]: Reader.getReciprocalCategory,
  [KEYS.COST_ADJUSTMENT_DESCRIPTION]: Reader.getCostAdjustmentDescriptions,
  [KEYS.MAKE]: 'primaryVehicle',
  [KEYS.CCR_TAX]: Reader.getCCRTax,
  [KEYS.STATE_TAX_RATE_PCT]: Reader.getStateTaxRatePCT,
  [KEYS.CITY_TAX_RATE_PCT]: Reader.getCityTaxRatePCT,
  [KEYS.CITY_TAX_AMOUNT]: Reader.getCityTaxAmount,
  [KEYS.COUNTY_TAX_RATE_PCT]: Reader.getCountyTaxRatePCT,
  [KEYS.COUNTY_TAX_AMOUNT]: Reader.getCountyTaxAmount,
  [KEYS.MONTHLY_USE_TAX_AMOUNT]: Reader.getMonthlyUseTax,
  [KEYS.STATE_TAX_AMOUNT]: Reader.getStateTaxAmount,
  [KEYS.CCR_TAX_RATE]: Reader.getCCRTaxRate,
}));

export const COLUMN_VS_CUSTOM_RENDERER = defaultMemoize(
  ({
    lendersInfo,
    filters,
    dealTypeVsDisplayName,
    fniProductMapById,
    viSettings,
    dealerOemSites,
    selectedGroupBy,
    forBreadCrumb,
    getFormattedNumber,
    isEnterpriseV2Enabled,
    enterpriseV2Workspaces,
    accessibleDealers,
    dealType,
    original,
  }) => ({
    [KEYS.DEAL_STATUS]: value => DEAL_STATUS_DISPLAY_MAP_V2()[_toUpper(value)],
    [KEYS.LENDER]: [Reader.getLenderDisplayName(lendersInfo)],
    [KEYS.QUOTE_AGE]: QuoteAge(getFormattedNumber),
    [KEYS.VEHICLE]: [fillBlank],
    [KEYS.TRIM]: [transformObjectValuesToString, fillBlank],
    [KEYS.SALES_PERSON]: AssigneeRenderer(filters, selectedGroupBy, forBreadCrumb),
    [KEYS.SALES_MANAGER]: AssigneeRenderer(filters, selectedGroupBy, forBreadCrumb),
    [KEYS.FINANCE_MANAGER]: AssigneeRenderer(filters, selectedGroupBy, forBreadCrumb),
    [KEYS.CLOSOING_MANAGER]: AssigneeRenderer(filters, selectedGroupBy, forBreadCrumb),
    [KEYS.FNI_GROUP_KEY]: FniProductCellRenderer(
      Reader.getFniProductDisplayName({ fniProductMapById }),
      filters,
      KEYS.FNI_GROUP_KEY,
      selectedGroupBy,
      original
    ),
    [KEYS.PRESOLD_FNI_GROUP_KEY]: FniProductCellRenderer(
      Reader.getFniProductDisplayName({ fniProductMapById }),
      filters,
      KEYS.PRESOLD_FNI_GROUP_KEY,
      selectedGroupBy,
      original,
      true // disablePreSoldTag = true for ghost F&I products
    ),
    [KEYS.DEAL_COUNT_SUM]: Numeric,
    [KEYS.UNIT_COUNT]: Numeric,
    [KEYS.DEAL_CATEGORY]: [Reader.getDealTypeDisplayName(dealTypeVsDisplayName)],

    [KEYS.SALES_PERSON_SPIFF_SUM]: Price,
    [KEYS.SALES_PERSON_SPIFF_AVG]: Price,
    [KEYS.SALES_PERSON_COMMISSION_SUM]: Price,
    [KEYS.SALES_PERSON_COMMISSION_AVG]: Price,
    [KEYS.SALES_PERSON_TOTAL_COMMISSION_SUM]: Price,
    [KEYS.SALES_PERSON_TOTAL_COMMISSION_AVG]: Price,

    [KEYS.SALES_MANAGER_SPIFF_SUM]: Price,
    [KEYS.SALES_MANAGER_SPIFF_AVG]: Price,
    [KEYS.SALES_MANAGER_COMMISSION_SUM]: Price,
    [KEYS.SALES_MANAGER_COMMISSION_AVG]: Price,
    [KEYS.SALES_MANAGER_TOTAL_COMMISSION_SUM]: Price,
    [KEYS.SALES_MANAGER_TOTAL_COMMISSION_AVG]: Price,

    [KEYS.FINANCE_MANAGER_SPIFF_SUM]: Price,
    [KEYS.FINANCE_MANAGER_SPIFF_AVG]: Price,
    [KEYS.FINANCE_MANAGER_COMMISSION_SUM]: Price,
    [KEYS.FINANCE_MANAGER_COMMISSION_AVG]: Price,
    [KEYS.FINANCE_MANAGER_TOTAL_COMMISSION_SUM]: Price,
    [KEYS.FINANCE_MANAGER_TOTAL_COMMISSION_AVG]: Price,

    [KEYS.FRONT_GROSS_AMT_SUM]: Price,
    [KEYS.FRONT_GROSS_AMT_AVG]: Price,
    [KEYS.BACK_GROSS_AMT_SUM]: Price,
    [KEYS.BACK_GROSS_AMT_AVG]: Price,
    [KEYS.TOTAL_GROSS_AMT_SUM]: Price,
    [KEYS.TOTAL_GROSS_AMT_AVG]: Price,
    [KEYS.VEHICLE_GROSS_SUM]: Price,
    [KEYS.VEHICLE_GROSS_AVG]: Price,
    [KEYS.BACK_ACC_GROSS_AMT_SUM]: Price,
    [KEYS.BACK_ACC_GROSS_AMT_AVG]: Price,
    [KEYS.COMMISSION_GROSS]: Price,
    [KEYS.FNI_GROSS_AMT_AVG]: Price,
    [KEYS.FNI_GROSS_AMT_SUM]: Price,

    [KEYS.BOOKING_SNAPSHOT_FRONT_GROSS_AMT_SUM]: Price,
    [KEYS.BOOKING_SNAPSHOT_FRONT_GROSS_AMT_AVG]: Price,
    [KEYS.BOOKING_SNAPSHOT_BACK_GROSS_AMT_SUM]: Price,
    [KEYS.BOOKING_SNAPSHOT_BACK_GROSS_AMT_AVG]: Price,
    [KEYS.BOOKING_SNAPSHOT_TOTAL_GROSS_AMT_SUM]: Price,
    [KEYS.BOOKING_SNAPSHOT_TOTAL_GROSS_AMT_AVG]: Price,

    [KEYS.SALE_SNAPSHOT_FRONT_GROSS_AMT_SUM]: Price,
    [KEYS.SALE_SNAPSHOT_FRONT_GROSS_AMT_AVG]: Price,
    [KEYS.SALE_SNAPSHOT_BACK_GROSS_AMT_SUM]: Price,
    [KEYS.SALE_SNAPSHOT_BACK_GROSS_AMT_AVG]: Price,
    [KEYS.SALE_SNAPSHOT_TOTAL_GROSS_AMT_SUM]: Price,
    [KEYS.SALE_SNAPSHOT_TOTAL_GROSS_AMT_AVG]: Price,

    [KEYS.TOTAL_GROSS_PACE]: Price,

    [KEYS.CCR_TAX]: Price,
    [KEYS.CITY_TAX_AMOUNT]: Price,
    [KEYS.COUNTY_TAX_AMOUNT]: Price,
    [KEYS.MONTHLY_USE_TAX_AMOUNT]: Price,
    [KEYS.STATE_TAX_AMOUNT]: Price,
    [KEYS.FLAT_TAX_AMOUNT]: Price,
    [KEYS.DOC_FEE_TAX]: Price,

    [KEYS.VEHICLE_AGE]: VehicleAge(_get(viSettings, ['otherSettings', 'agingField']), getFormattedNumber, dealType),
    [KEYS.CUSTOMER_ADDRESS]: [fillBlank],
    [KEYS.ZIP_CODE]: [fillBlank],
    [KEYS.DELIVERY_DONE]: value => (value ? __('Delivered') : fillBlank()),
    [KEYS.CERTIFIED]: value => (value && JSON.parse(value) ? __('Certified') : fillBlank()),
    [KEYS.CONCIERGE_ACTIVATED]: [mapBooleanToString],
    [KEYS.CONTRACT_AGE]: [getDaysFromDuration, getFormattedNumber, appendText(__('days'))],
    [KEYS.CONTRACT_AGE_AVG]: [getDaysFromDuration, getFormattedNumber, appendText(__('days'))],
    [KEYS.DEALER]: isEnterpriseV2Enabled
      ? [getEnterpriseDealerNameById(enterpriseV2Workspaces, accessibleDealers)]
      : [getDealerNameById],
    [KEYS.TAX_EXEMPT]: [mapBooleanToString],
    [KEYS.STATE]: value => value || fillBlank(),
    [KEYS.PENDING_DOWN_PAYMENT_SUM]: Price,
    [KEYS.OSF]: stringifyBool,
    [KEYS.CUSTOMER_PORTAL_LINK_SHARED]: [mapBooleanToString],
    [KEYS.VEHICLE_WALKTHROUGH_ADDED]: [mapBooleanToString],
    [KEYS.VEHICLE_WALKTHROUGH_WATCHED]: [mapBooleanToString],
    [KEYS.CREDIT_APPLICATION]: [mapBooleanToString],
    [KEYS.DEAL_SHEET_SHARED]: [mapBooleanToString],
    [KEYS.FNI_SHEET_SHARED]: [mapBooleanToString],
    [KEYS.PRODUCT_INDEX_AVG]: [fillBlankExceptZero],
    [KEYS.SOLD_AT_SITE_ID]: [Reader.displaySite(dealerOemSites, 'soldAtSiteId'), fillBlank],
    [KEYS.STOCKED_IN_SITE_ID]: [Reader.displaySite(dealerOemSites, 'primaryVehicle.stockedInAtSiteId'), fillBlank],
    [KEYS.TAXABLE_STATE]: value => value || fillBlank(),
    [KEYS.BUYER_STATE]: value => value || fillBlank(),
    [KEYS.REBATE_PROGRAM_NAME]: RenderArrayCell(),
    [KEYS.REBATE_PROGRAM_CODE]: RenderArrayCell(),
    [KEYS.REBATE_PROGRAM_NUMBER]: RenderArrayCell(),
    [KEYS.COST_ADJUSTMENT_DESCRIPTION]: RenderArrayCell(),
    [KEYS.MAKE]: makeRenderer,
  })
);

export const ACCESSOR_KEYS_FOR_REPORTS = {
  [KEYS.VEHICLE_AGE]: KEYS.VEHICLE_AGE_AVG,
  [KEYS.CONTRACT_AGE]: KEYS.CONTRACT_AGE_AVG,
  [KEYS.QUOTE_AGE]: KEYS.QUOTE_AGE_AVG,
};

export const RENDERER_FOR_AGGREGATE_REPORTS = {
  [KEYS.VEHICLE_AGE_AVG]: _flow([roundOf, appendText(__('days'))]),
  [KEYS.CONTRACT_AGE_AVG]: _flow([roundOf, appendText(__('days'))]),
  [KEYS.QUOTE_AGE_AVG]: _flow([roundOf, appendText(__('days'))]),
};

export const MESSAGES = {
  UPDATE_CUSTOM_STATUS_FAILURE: __('Failed to update deal sub status'),
};

export const BUYER_TYPES_DISPLAY_NAMES = {
  BUYER: 'Buyer',
  CO_BUYER: 'Co-Buyer',
};

export const CUTOM_REPORT_OPTION = () => ({
  subMenuId: EXPORT_TYPES.CUSTOM_REPORT,
  subMenuTitle: __('Custom Report'),
  optionLabels: [SALES_EXPORT_FILE_TYPES.NOTICE_OF_SALES_REPORT],
});

export const BOOLEAN_OPTIONS = [
  {
    label: __('Yes'),
    value: true,
  },
  {
    label: __('No'),
    value: false,
  },
];

export const ASSIGNEE_COMMISSION_CONFIG = {
  SALES_PERSION: {
    ASSIGNEE_DETAILS_READER: Reader.getSalesPersonAssigneeDetails,
    SPIFF_AMOUNT_INDIVIDUAL: 'spiffAmt',
    SPIFF_AMOUNT_ALL: 'grossDetails.salesPersonSpiffAmt',
    BASE_COMMISSION_INDIVIDUAL: 'commissionAmt',
    BASE_COMMISSION_ALL: 'grossDetails.salesPersonCommissionAmt',
    TOTAL_COMMISSION_INDIVIDUAL: 'totalAmt',
    TOTAL_COMMISSION_ALL: 'grossDetails.salesPersonTotalCommissionAmt',
    GROUP_BY_KEY: 'SALES_PERSON',
  },
  SALES_MANAGER: {
    ASSIGNEE_DETAILS_READER: Reader.getSalesManagerAssigneeDetails,
    SPIFF_AMOUNT_INDIVIDUAL: 'spiffAmt',
    SPIFF_AMOUNT_ALL: 'grossDetails.salesManagerSpiffAmt',
    BASE_COMMISSION_INDIVIDUAL: 'commissionAmt',
    BASE_COMMISSION_ALL: 'grossDetails.salesManagerCommissionAmt',
    TOTAL_COMMISSION_INDIVIDUAL: 'totalAmt',
    TOTAL_COMMISSION_ALL: 'grossDetails.salesManagerTotalCommissionAmt',
    GROUP_BY_KEY: 'SALES_MANAGER',
  },
  FINANCE_MANAGER: {
    ASSIGNEE_DETAILS_READER: Reader.getFinanceManagerAssigneeDetails,
    SPIFF_AMOUNT_INDIVIDUAL: 'spiffAmt',
    SPIFF_AMOUNT_ALL: 'grossDetails.financeManagerSpiffAmt',
    BASE_COMMISSION_INDIVIDUAL: 'commissionAmt',
    BASE_COMMISSION_ALL: 'grossDetails.financeManagerCommissionAmt',
    TOTAL_COMMISSION_INDIVIDUAL: 'totalAmt',
    TOTAL_COMMISSION_ALL: 'grossDetails.financeManagerTotalCommissionAmt',
    GROUP_BY_KEY: 'FINANCE_MANAGER',
  },
};

export const GDPR_CONFIGS = {
  [KEYS.CUSTOMER_ADDRESS]: {
    reader: '',
    deleteReason: 'primaryCustomer.deleteReason',
    formatter: Reader.getCustomerAddress,
  },
  [KEYS.BUYER_ADDRESS_LINE_1]: {
    reader: 'primaryCustomer.currentAddress.address1',
    deleteReason: 'primaryCustomer.deleteReason',
    formatter: _noop,
  },
  [KEYS.BUYER_ADDRESS_LINE_2]: {
    reader: 'primaryCustomer.currentAddress.address2',
    deleteReason: 'primaryCustomer.deleteReason',
    formatter: _noop,
  },
  [KEYS.BUYER_LAST_FIRST_NAME]: {
    reader: '',
    deleteReason: 'primaryCustomer.deleteReason',
    formatter: Reader.getPrimaryCustomerLastAndFirstName,
  },
  [KEYS.COBUYER_LAST_FIRST_NAME]: {
    reader: '',
    deleteReason: 'secondaryCustomer.deleteReason',
    formatter: Reader.getSecondaryCustomerLastAndFirstName,
  },
  [KEYS.ZIP_CODE]: {
    reader: '',
    deleteReason: 'primaryCustomer.deleteReason',
    formatter: Reader.getCurrentZipCode,
  },
  [KEYS.TITLE_BUYER]: {
    reader: 'titleBuyer',
    deleteReason: 'primaryCustomer.deleteReason',
    formatter: _noop,
  },
  [KEYS.TITLE_COBUYER]: {
    reader: 'titleCoBuyer',
    deleteReason: 'secondaryCustomer.deleteReason',
    formatter: _noop,
  },
  [KEYS.BUYER_FIRST_NAME]: {
    reader: 'primaryCustomer.firstName',
    deleteReason: 'primaryCustomer.deleteReason',
    formatter: _noop,
  },
  [KEYS.BUYER_MIDDLE_NAME]: {
    reader: 'primaryCustomer.middleName',
    deleteReason: 'primaryCustomer.deleteReason',
    formatter: _noop,
  },
  [KEYS.BUYER_LAST_NAME]: {
    reader: 'primaryCustomer.lastName',
    deleteReason: 'primaryCustomer.deleteReason',
    formatter: _noop,
  },
  [KEYS.COBUYER_FIRST_NAME]: {
    reader: 'secondaryCustomer.firstName',
    deleteReason: 'secondaryCustomer.deleteReason',
    formatter: _noop,
  },
  [KEYS.COBUYER_MIDDLE_NAME]: {
    reader: 'secondaryCustomer.middleName',
    deleteReason: 'secondaryCustomer.deleteReason',
    formatter: _noop,
  },
  [KEYS.COBUYER_LAST_NAME]: {
    reader: 'secondaryCustomer.lastName',
    deleteReason: 'secondaryCustomer.deleteReason',
    formatter: _noop,
  },
  [KEYS.BUYER_COUNTY]: {
    reader: 'primaryCustomer.currentAddress.countyName',
    deleteReason: 'primaryCustomer.deleteReason',
    formatter: _noop,
  },
  [KEYS.BUYER_STREET_ADDRESS]: {
    reader: 'primaryCustomer.currentAddress.address1',
    deleteReason: 'primaryCustomer.deleteReason',
    formatter: _noop,
  },
  [KEYS.COBUYER_STREET_ADDRESS]: {
    reader: 'secondaryCustomer.currentAddress.address1',
    deleteReason: 'secondaryCustomer.deleteReason',
    formatter: _noop,
  },
  [KEYS.BUYER_CITY]: {
    reader: 'primaryCustomer.currentAddress.city',
    deleteReason: 'primaryCustomer.deleteReason',
    formatter: _noop,
  },
  [KEYS.COBUYER_CITY]: {
    reader: 'secondaryCustomer.currentAddress.city',
    deleteReason: 'secondaryCustomer.deleteReason',
    formatter: _noop,
  },
  [KEYS.BUYER_STATE]: {
    reader: 'primaryCustomer.currentAddress.state',
    deleteReason: 'primaryCustomer.deleteReason',
    formatter: _noop,
  },
  [KEYS.COBUYER_STATE]: {
    reader: 'secondaryCustomer.currentAddress.state',
    deleteReason: 'secondaryCustomer.deleteReason',
    formatter: _noop,
  },
  [KEYS.CUSTOMER_NUMBER]: {
    reader: 'primaryCustomer.displayId',
    deleteReason: 'primaryCustomer.deleteReason',
    formatter: _noop,
  },
  [KEYS.CUSTOMER_EMAIL_ID]: {
    reader: 'primaryCustomer.email',
    deleteReason: 'primaryCustomer.deleteReason',
    formatter: _noop,
  },
  [KEYS.COBUYER_NUMBER]: {
    reader: 'secondaryCustomer.displayId',
    deleteReason: 'secondaryCustomer.deleteReason',
    formatter: _noop,
  },
  [KEYS.CUSTOMER_WORK_PHONE]: {
    reader: 'primaryCustomer.workPhone',
    deleteReason: 'primaryCustomer.deleteReason',
    formatter: _noop,
  },
  [KEYS.CUSTOMER_HOME_PHONE]: {
    reader: 'primaryCustomer.homePhone',
    deleteReason: 'primaryCustomer.deleteReason',
    formatter: _noop,
  },
  [KEYS.CUSTOMER_MOBILE_NUMBER]: {
    reader: 'primaryCustomer.mobileNo',
    deleteReason: 'primaryCustomer.deleteReason',
    formatter: _noop,
  },
};

export const APPLICATION_STATUS_LABEL = {
  [APPLICATION_STATUS.DRAFT]: __('Draft'),
  [APPLICATION_STATUS.ARCHIVED]: __('Archived'),
  [APPLICATION_STATUS.SUBMITTED]: __('Submitted'),
  [APPLICATION_STATUS.PROCESSED]: __('Processed'),
  [APPLICATION_STATUS.FAILED]: __('Failed'),
  [APPLICATION_STATUS.PENDING]: __('Pending'),
  [APPLICATION_STATUS.CONDITIONED]: __('Conditioned'),
  [APPLICATION_STATUS.APPROVED]: __('Approved'),
  [APPLICATION_STATUS.DECLINED]: __('Declined'),
  [APPLICATION_STATUS.WITHDRAWN]: __('Withdrawn'),
};
