import React, { PureComponent } from 'react';
import withSize from 'tcomponents/hoc/withSize';
import { compose } from 'recompose';
import { connect } from 'react-redux';

import _get from 'lodash/get';
import _noop from 'lodash/noop';

import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import withAsyncReducer from 'tcomponents/connectors/withAsyncReducer';
import PreloadData from 'tcomponents/molecules/preloadData';
import { clearPref } from 'tcomponents/actions/userPreferenceAction';
import withPropertyConsumer from 'tcomponents/organisms/propertyProvider/withPropertyConsumer';
import { DEFAULT_REPORT_ROUTE } from '@tekion/tekion-business/src/constants/coreReports';

import {
  fetchVehicleMakes,
  fetchSalesMetadata,
  fetchVehicleByMakes,
  fetchSalesReportCsv,
  fetchColumnMetaData,
  fetchDealerList,
  updateVehicleSubTypeOptions,
  fetchViMetadata,
  getGeneralFields,
  fetchTaxCodes,
  getNoticeOfSalesReport,
  getVISettings,
  fetchDealMetadata,
  fetchDealerSites,
  getAllRolesInfo,
  getEnterPriseMetaData,
} from 'actions/sales.actions';
import withColConfig from 'organisms/withColConfig';
import { DEAL_NUMBER_SORT } from 'constants/sales.constant';

import withCustomSalesReports from '@tekion/tekion-widgets/src/hocs/withCustomReports/withCustomSalesReports';
import withSalesEnv from '@tekion/tekion-widgets/src/connectors/sales/withSalesEnv';
import withRouter from '@tekion/tekion-components/src/hoc/withRouter';
import customReportsApis from '@tekion/tekion-widgets/src/hocs/withCustomReports/CustomReports.api';
import getDataFromResponse from '@tekion/tekion-base/utils/getDataFromResponse';
import withGroupBy from '../components/withGroupBy';
import reducers from './salesPerformance.reducer';
import { SALES_PERFORMANCE_PREFERENCE_URL } from './SalesPerformance.api';
import {
  getDealReports,
  getAggregationFields,
  getDealReportsBreakDown,
  getDealSetupInfo,
  // updateCustomStatus,
  getFNIProducts,
  getDealReportsAndAggregatedReports,
  getBulkLenders,
} from './SalesPerfomance.actions';
import { getDealTypes, getDealCustomStatuses, getFniProductMapById } from './SalesPerformance.selectors';
import SalesPerformance from './SalesPerformance';
import {
  REDUCER_NAME,
  COLUMN_VS_CUSTOM_RENDERER,
  MODULE_NAME,
  COLUMN_VS_RENDERER_TYPE,
} from './SalesPerformance.constant';
import { COLUMN_CONFIG_BY_KEY } from './SalesPerformance.config';
import {
  getInitialFilters,
  showNoticeOfSalesReportsView,
  getReportRouteId,
  updateCustomGourpByKey,
  getModuleName,
} from './SalesPerformance.helper';

function mapStateToProps(state) {
  const lendersInfo = _get(state, [REDUCER_NAME, 'lendersInfo', 'lendersInfo']) || EMPTY_OBJECT;
  const fniProductMapById = getFniProductMapById(_get(state, REDUCER_NAME));
  const dealerOemSites = _get(state, [REDUCER_NAME, 'dealerSites']);
  const loggedInUserSites = _get(state, 'skeleton.loginData.dealerOemSites') || EMPTY_ARRAY;
  const customRenderer = COLUMN_VS_CUSTOM_RENDERER({
    lendersInfo,
    fniProductMapById,
    dealerOemSites,
    forBreadCrumb: true,
  });
  const commonRenderer = COLUMN_VS_RENDERER_TYPE({});
  const permissions = _get(state, 'permissions.permissions') || EMPTY_ARRAY;
  const isNoticeOfSalesReportVisible = showNoticeOfSalesReportsView(permissions, MODULE_NAME);

  return {
    lendersInfo,
    moduleName: MODULE_NAME,
    customRenderer,
    commonRenderer,
    reports: _get(state, [REDUCER_NAME, 'reports'], EMPTY_ARRAY),
    dealList: _get(state, [REDUCER_NAME, 'entityList'], EMPTY_ARRAY),
    count: _get(state, [REDUCER_NAME, 'count'], 0),
    currentPage: _get(state, [REDUCER_NAME, 'currentPage'], 0),
    groupByOptions: _get(state, [REDUCER_NAME, 'groupByOptions'], EMPTY_ARRAY),
    projections: _get(state, [REDUCER_NAME, 'projections'], EMPTY_ARRAY),
    columnKeyVsField: updateCustomGourpByKey(_get(state, [REDUCER_NAME, 'columnKeyVsField'], EMPTY_OBJECT)),
    vehicles: _get(state, [REDUCER_NAME, 'vehicles'], EMPTY_OBJECT),
    rolesList: _get(state, [REDUCER_NAME, 'rolesInfo'], EMPTY_OBJECT),
    itemsPerPage: _get(state, [REDUCER_NAME, 'itemsPerPage']),
    isFetching: _get(state, [REDUCER_NAME, 'isFetching'], false),
    filters: _get(state, [REDUCER_NAME, 'filters'], EMPTY_ARRAY),
    initialFilters: getInitialFilters(),
    downloadCSV: fetchSalesReportCsv,
    bulkNotes: _get(state, 'core.dealNote', EMPTY_OBJECT),
    columnMetaData: _get(state, [REDUCER_NAME, 'columnMetaData', 'byId'], EMPTY_OBJECT),
    columnMetaDataLoaded: _get(state, [REDUCER_NAME, 'columnMetaData', 'loaded']),
    defaultSort: DEAL_NUMBER_SORT,
    dealTypesFromSetup: getDealTypes(state, REDUCER_NAME),
    dealCustomStatuses: getDealCustomStatuses(state, REDUCER_NAME),
    activityList: _get(state, [REDUCER_NAME, 'activityList'], EMPTY_OBJECT),
    generalFields: _get(state, [REDUCER_NAME, 'generalFields'], EMPTY_ARRAY),
    taxCodes: _get(state, [REDUCER_NAME, 'taxCodes']) || EMPTY_ARRAY,
    fniProductMapById,
    isNoticeOfSalesReportVisible,
    viSettings: _get(state, [REDUCER_NAME, 'viSettings']),
    dealMetadata: _get(state, [REDUCER_NAME, 'dealMetadata']),
    loggedInUserSites,
    dealerOemSites,
    dealSetupInfo: state?.[REDUCER_NAME]?.dealSetupInfo,
    dataMaskingProperties: _get(state, [REDUCER_NAME, 'dataMaskingProperties']) || EMPTY_OBJECT,
    aggregatedReports: _get(state, [REDUCER_NAME, 'aggregatedReports'], EMPTY_ARRAY),
  };
}

const connector = connect(mapStateToProps, {
  getDealReports,
  getAggregationFields,
  getDealReportsBreakDown,
  getDealSetupInfo,
  // updateCustomStatus,
  fetchVehicleMakes,
  fetchSalesMetadata,
  getGeneralFields,
  fetchVehicleByMakes,
  fetchColumnMetaData,
  fetchDealerList,
  updateVehicleSubTypeOptions,
  fetchViMetadata,
  clearPref,
  getFNIProducts,
  fetchTaxCodes,
  getNoticeOfSalesReport,
  getVISettings,
  fetchDealMetadata,
  fetchDealerSites,
  getAllRolesInfo,
  getEnterPriseMetaData,
  getDealReportsAndAggregatedReports,
  getBulkLenders,
});

// https://tekion.atlassian.net/browse/DMS-99694
class SalesPerfContainer extends PureComponent {
  constructor(props) {
    super(props);

    this.state = {
      customReportData: null,
      isCustomReportsFetched: false,
      reportNotFound: false,
      componentReady: false,
    };

    // Component will be created after API data is fetched
    this.Component = null;
  }

  componentDidMount() {
    const { location = EMPTY_OBJECT } = this.props;
    const { pathname } = location;
    const customReportId = getReportRouteId(pathname);

    if (this.isDefaultReport(customReportId)) {
      this.setState({ isCustomReportsFetched: true }, () => {
        this.createComponent();
      });
      return;
    }

    this.fetchCustomReportData(customReportId);
  }

  fetchCustomReportData = customReportId => {
    customReportsApis
      .fetchCustomReport(customReportId)
      .then(response => {
        const customReportData = getDataFromResponse(response);
        this.setState(
          {
            customReportData,
            isCustomReportsFetched: true,
          },
          () => {
            this.createComponent();
          }
        );
      })
      .catch(error => {
        this.setState(
          {
            reportNotFound: true,
            isCustomReportsFetched: true,
          },
          () => {
            this.createComponent();
          }
        );
      });
  };

  createComponent = () => {
    const { location = EMPTY_OBJECT } = this.props;
    const { pathname } = location;
    const customReportId = getReportRouteId(pathname);
    const createCustomReportMode = this.getCreateCustomReportMode();
    const defaultReportFlag = this.getDefaultReportFlag();

    const withColConfigProps = {
      moduleName: getModuleName(defaultReportFlag, customReportId, MODULE_NAME),
      columnConfigByKey: COLUMN_CONFIG_BY_KEY,
      customURL: `${SALES_PERFORMANCE_PREFERENCE_URL}?customReportId=${customReportId}`,
      allowCheckAll: true,
      enableColumnWidthRetention: true,
      shouldVirtualizeList: true,
      preventSavingUserPreferenceColumns: createCustomReportMode,
      preventReadingUserPreferenceColumns: createCustomReportMode,
      rearrangeColumnsAfterSelection: createCustomReportMode,
      openColumnSettingsByDefault: createCustomReportMode,
    };

    const withCustomReportsProps = {
      canCreateAsNewReport: false,
      createCustomReportMode,
      getTableActions: defaultReportFlag ? _noop : null,
    };

    this.Component = compose(
      withColConfig(withColConfigProps),
      withGroupBy({ multiSort: true }),
      withCustomSalesReports(withCustomReportsProps)
    )(SalesPerformance);

    this.setState({ componentReady: true });
  };

  isDefaultReport = customReportId => !customReportId || customReportId === DEFAULT_REPORT_ROUTE;

  getDefaultReportFlag = () => {
    const { customReportData } = this.state;

    return customReportData?.defaultReport === true;
  };

  getCreateCustomReportMode = () => {
    const { location = EMPTY_OBJECT } = this.props;

    return !!location?.state?.createCustomReportMode;
  };

  render() {
    const { Component, props } = this;
    const { getAggregationFields: getAggrFields, ...restOfProps } = props;
    const { customReportData, isCustomReportsFetched, reportNotFound, componentReady } = this.state;

    if (!componentReady) {
      return null;
    }

    const defaultReportFlag = this.getDefaultReportFlag();
    const { location = EMPTY_OBJECT } = this.props;
    const { pathname } = location;
    const customReportId = getReportRouteId(pathname);
    const isDefaultRoute = this.isDefaultReport(customReportId);

    return (
      <PreloadData apiToLoad={getAggrFields}>
        <Component
          {...restOfProps}
          createCustomReportMode={this.getCreateCustomReportMode()}
          defaultReport={defaultReportFlag}
          customReportData={customReportData}
          isCustomReportsFetched={isCustomReportsFetched}
          reportNotFound={reportNotFound}
          isDefaultRoute={isDefaultRoute}
        />
      </PreloadData>
    );
  }
}

export default compose(
  withPropertyConsumer,
  withSalesEnv,
  withAsyncReducer({ storeKey: REDUCER_NAME, reducer: reducers }),
  connector,
  withSize(),
  withRouter
)(SalesPerfContainer);
