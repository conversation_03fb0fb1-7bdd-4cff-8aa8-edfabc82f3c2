/* eslint-disable import/order */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { defaultMemoize } from 'reselect';
import { compose } from 'recompose';
import produce from 'immer';

import _get from 'lodash/get';
import _map from 'lodash/map';
import _isEmpty from 'lodash/isEmpty';
import _find from 'lodash/find';
import _flow from 'lodash/flow';
import _includes from 'lodash/includes';
import _isArray from 'lodash/isArray';
import _property from 'lodash/property';
import _orderBy from 'lodash/orderBy';
import _toString from 'lodash/toString';
import _set from 'lodash/set';
import _size from 'lodash/size';
import _reduce from 'lodash/reduce';
import _values from 'lodash/values';
import _noop from 'lodash/noop';

import DEALER_PROPERTY_CONSTANTS from '@tekion/tekion-base/constants/dealerProperties';
import { siteFilterESPayloadSanitizerForFilters } from '@tekion/tekion-widgets/src/appServices/sales/dealerSiteFilter.helpers';
import WithErrorIcon from '@tekion/tekion-widgets/src/appServices/sales/organisms/withCPRAGDPRErrorIcon/withErrorIcon';
import { withTekionConversion } from '@tekion/tekion-conversion-web';
import { PERMISSIONS } from '@tekion/tekion-base/constants/permissions';
import { PermissionContext, permissionValidations } from '@tekion/tekion-components/src/widgets/permissionsHelper';
import TableManager from '@tekion/tekion-widgets/src/organisms/tableManager';
import DealNotes from './components/DealNotes';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';
import {
  SALES_EXPORT_FILE_TYPES,
  DEAL_SALES_LOG_FILTER_TYPES,
} from '@tekion/tekion-business/src/constants/coreReports';
import { handleFetchUsersToTagInNotes } from '@tekion/tekion-business/src/appServices/sales/utils/sales.utils';
import { getSubStatusId } from '@tekion/tekion-base/marketScan/utils/desking.utils';
import { DEFAULT_PAGE_SIZE } from '@tekion/tekion-base/constants/tableConstants';
import HeaderWithInfo from '@tekion/tekion-components/src/molecules/headerWithInfo';
import { isVehicleFieldIsMasked } from '@tekion/tekion-base/marketScan/utils/deal.util';
import {
  VEHICLE_MASKING_FIELD_LABEL,
  VEHILCE_MASKING_PROPERTIES,
} from '@tekion/tekion-base/marketScan/constants/deal.constants';
import { getDealType } from '@tekion/tekion-base/marketScan/readers/deal.reader';
import { CORE } from '@tekion/tekion-base/constants/appServices';
import { EMPTY_OBJECT, EMPTY_ARRAY, NO_DATA, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { SEARCH_USER_BY_ALL_PERSONAS } from '@tekion/tekion-base/constants/deal/common';
import TableManagerActions from 'tcomponents/organisms/TableManager/constants/actionTypes';
import { getYearMakeModel } from '@tekion/tekion-base/helpers/vehicle.helper';
import { getCustomerName } from '@tekion/tekion-base/helpers/dealCustomer.helper';
import Ellipses from 'tcomponents/atoms/Ellipsis';
import Spinner from 'tcomponents/molecules/SpinnerComponent/Spinner';
import FlagPickerWithRadio from 'tcomponents/molecules/FlagPicker/FlagPickerWithRadio';
import PopoverWithCount from '@tekion/tekion-components/src/organisms/popoverWithCount';
import { POPOVER_PLACEMENT } from '@tekion/tekion-components/src/molecules/popover';
import { REPORTS_PATH } from 'pages/Reports/reports.constants';
import MultipleContentRenderer from 'molecules/cellRenderer/MultipleContentRenderer';
import { createReportPayload, createEntityPayload } from '@tekion/tekion-widgets/src/utils/payload.utils';
import { getSourceTypeOptions } from 'utils/sales.helpers';
import coreEnv from 'utils/coreEnv';
import { FEATURE_NAME, withExperienceEngineConsumer } from '@tekion/tekion-widgets/src/experienceEngine';

import CustomerCell from 'tcomponents/organisms/CustomerCell';
import Content from '@tekion/tekion-components/src/atoms/Content';
import { createCustomNoticeOFSalesReportPayload } from './utils/customNoticeOfSalesReportPayload';
import SalesPerformanceAPI from './SalesPerformance.api';
import {
  getFilterProps,
  getReportRouteId,
  getDealTypeVsDisplayName,
  getSubStatusOptionsForFlagPicker,
  modifyReportsPayload,
  updateSortDataWithCustomField,
  makeSortKeyWhenDealerPropertyEnabled,
  replaceNoneAsEmptyString,
  getAccessor,
  getDealerOptions,
  getEnterpriseDealers,
  getPrimaryVehicleValue,
} from './SalesPerformance.helper';
import { reportsColumnAccessor } from '../reports.helper';
import { getPrimaryVehicle, getValueUsingColMetaData, getDealLookupUsers } from './SalesPerformance.reader';
import styles from '../reports.module.scss';

import {
  TABLE_MANAGER_PROPS,
  DEFAULT_TABLE_PROPS,
  TABLE_ROW_HEIGHT,
  HEADER_PROPS,
  COLUMN_VS_CUSTOM_ACCESSOR,
  COLUMN_VS_CUSTOM_RENDERER,
  KEYS,
  COLUMN_VS_RENDERER_TYPE,
  ACCESSOR_KEYS_FOR_REPORTS,
  RENDERER_FOR_AGGREGATE_REPORTS,
  MODULE_NAME,
  BUYER_TYPES_DISPLAY_NAMES,
  KEYS_VIA_FIELD_NAME,
  CUSTOM_ACCESSOR_VIA_FIELD_NAME,
  CUTOM_REPORT_OPTION,
  COLUMN_META_VS_CUSTOM_RENDERER,
  GDPR_CONFIGS,
  APPLICATION_STATUS_LABEL,
} from './SalesPerformance.constant';
import { getAccessibleDealersForEnterprise, getDealers } from '../readers/dealer';

const { BUYER, CO_BUYER } = BUYER_TYPES_DISPLAY_NAMES;

const sourceType = 'DEAL';
class SalesPerfomance extends PureComponent {
  // state = {
  //   aggregatedReports: EMPTY_ARRAY,
  // };

  state = {
    enterpriseV2Workspaces: EMPTY_ARRAY,
  };

  async componentDidMount() {
    const {
      // fetchDealerList,
      fetchVehicleMakes,
      fetchSalesMetadata,
      fetchColumnMetaData,
      moduleName,
      fetchViMetadata,
      getGeneralFields,
      getFNIProducts,
      fetchTaxCodes,
      getVISettings,
      fetchDealMetadata,
      fetchDealerSites,
      getAllRolesInfo,
      getEnterPriseMetaData,
      getFeatureValue,
      getBulkLenders,
    } = this.props;
    const isEnterpriseV2Enabled = getFeatureValue(FEATURE_NAME.ENTERPRISE_V2_ENABLED);
    this.currentDealerId = _get(coreEnv, 'dealerConfig.id');
    const RVDealerEnabled = DealerPropertyHelper.isRVDealerEnabled();
    const RVVehicleSupported = DealerPropertyHelper.isRVVehiclesSupported();

    Promise.all([
      getAllRolesInfo(),
      fetchVehicleMakes({ RVDealerEnabled, RVVehicleSupported }),
      fetchSalesMetadata(),
      getGeneralFields(),
      this.getSalesSetupInfo(),
      fetchColumnMetaData(moduleName),
      fetchViMetadata(),
      getFNIProducts(),
      fetchTaxCodes(),
      getVISettings(),
      fetchDealMetadata(),
      fetchDealerSites([{ dealerId: this.currentDealerId }]),
    ]);
    let dealerOptions;
    let dealerIds;
    if (isEnterpriseV2Enabled) {
      getEnterPriseMetaData();
      const response = await SalesPerformanceAPI.getEnterpriseV2Workspaces();
      const accessibleDealers = await getAccessibleDealersForEnterprise();
      this.setState({
        enterpriseV2Workspaces: response || EMPTY_ARRAY,
        accessibleDealers: accessibleDealers || EMPTY_ARRAY,
      });
      dealerOptions = getDealerOptions({
        isEnterpriseV2Enabled,
        enterpriseV2Workspaces: response || EMPTY_ARRAY,
        accessibleDealers: accessibleDealers || EMPTY_ARRAY,
      });
      const dealerList = getEnterpriseDealers(dealerOptions);
      dealerIds = _map(dealerList, 'value');
    } else {
      dealerOptions = getDealers();
      dealerIds = _map(dealerOptions, 'value');
    }
    if (!_isEmpty(dealerIds)) {
      await getBulkLenders(dealerIds);
    }
  }

  async componentDidUpdate(prevProps) {
    const { selectedGroupBy, reportsFilter, tableFilters, sortDetails, itemsPerPage } = this.props;
    if (
      prevProps.selectedGroupBy !== selectedGroupBy ||
      reportsFilter !== prevProps.reportsFilter ||
      sortDetails !== prevProps.sortDetails ||
      tableFilters !== prevProps.tableFilters
    ) {
      await this.getTableData(1, itemsPerPage);
    }
  }

  componentWillUnmount() {
    const { clearPref } = this.props;
    /**
     * Since customReport and SalesPerf uses same moduleName in redux store, so need to clear
     */
    clearPref(MODULE_NAME);
  }

  getCustomReportId = () => getReportRouteId(_get(this, 'props.location.pathname'));

  setSitesForFilterPayloads = payload => {
    const { loggedInUserSites, getDealerPropertyValue } = this.props;
    const isMultiOEMEnabled = getDealerPropertyValue(DEALER_PROPERTY_CONSTANTS.MULTI_OEM_SWITCH_ENABLED);
    if (!isMultiOEMEnabled) return payload;

    const { filters } = payload;

    const { stockedInVehicleFilter, soldAtFilter, dealerList } = _reduce(
      filters,
      (acc, filter) => {
        if (filter.field === DEAL_SALES_LOG_FILTER_TYPES.STOCKED_IN_VEHICLE_SITE) {
          return {
            ...acc,
            stockedInVehicleFilter: filter,
          };
        }

        if (filter.field === DEAL_SALES_LOG_FILTER_TYPES.SOLD_AT_SITE) {
          return {
            ...acc,
            soldAtFilter: filter,
          };
        }

        if (filter.field === DEAL_SALES_LOG_FILTER_TYPES.DEALER_LIST) {
          return {
            ...acc,
            dealerList: filter,
          };
        }

        return acc;
      },
      {
        stockedInVehicleFilter: EMPTY_OBJECT,
        dealerList: EMPTY_OBJECT,
        soldAtFilter: EMPTY_OBJECT,
      }
    );

    const siteFieldsToBeIncludedinFilters = [];

    if (!_size(dealerList.values) || _size(soldAtFilter.values)) {
      siteFieldsToBeIncludedinFilters.push(DEAL_SALES_LOG_FILTER_TYPES.SOLD_AT_SITE);
    }
    // if DEAL_SALES_LOG_FILTER_TYPES.STOCKED_IN_VEHICLE_SITE is not in actualPayload, then dont inculde in sanitized payload also
    if (_size(stockedInVehicleFilter.values)) {
      siteFieldsToBeIncludedinFilters.push(DEAL_SALES_LOG_FILTER_TYPES.STOCKED_IN_VEHICLE_SITE);
    }

    if (_size(siteFieldsToBeIncludedinFilters)) {
      return siteFilterESPayloadSanitizerForFilters(payload, loggedInUserSites, siteFieldsToBeIncludedinFilters);
    }

    return payload;
  };

  getModifiedReportsPayload = payload => {
    const { selectedGroupBy } = this.props;
    const reportsPayloadWthSiteFilter = this.setSitesForFilterPayloads(payload);
    return modifyReportsPayload(reportsPayloadWthSiteFilter, this.getCustomReportId(), selectedGroupBy);
  };

  getAggregatedReportsPayload = () => {
    const { columnKeyVsField, projections, tableFilters, reportsFilter, columnMetaData } = this.props;
    return createReportPayload({
      reportsFilter,
      tableFilters: replaceNoneAsEmptyString(tableFilters),
      columnKeyVsField,
      projections,
      columnMetaData,
    });
  };

  // getAggregatedReports = async () => {
  //   const { columnKeyVsField, projections, tableFilters, reportsFilter, columnMetaData } = this.props;

  //   const reportPayload = createReportPayload({
  //     reportsFilter,
  //     tableFilters: replaceNoneAsEmptyString(tableFilters),
  //     columnKeyVsField,
  //     projections,
  //     columnMetaData,
  //   });

  //   const modifiedReportsPayload = this.getModifiedReportsPayload(reportPayload);

  //   const response = await SalesPerformanceAPI.getDealReports(modifiedReportsPayload);

  //   const { hits } = response;
  //   this.setState({
  //     aggregatedReports: _map(hits, row => ({
  //       ...row.columns,
  //       isTotalAggregatedReport: true,
  //     })),
  //   });
  // };

  getData = defaultMemoize((aggregatedReports, reports, vehicles) => [...aggregatedReports, ...reports, ...vehicles]);

  getSalesSetupInfo = () => {
    const { getDealSetupInfo } = this.props;
    getDealSetupInfo();
  };

  getCellValue = defaultMemoize(
    (key, value, original, renderer, columnType, accessor, filters, selectedGroupBy, reportsFilter) => {
      const { renderReportCell, renderTableCell } = this.props;
      const isStandardMake = DealerPropertyHelper.standardizedMakeSetupEnabled();
      // If it is a reports type its object structure is different
      if (original.isSubAggregatedReport || original.isTotalAggregatedReport) {
        // Edge Case incase of groupby salesperson than show deal count avg instead of sum.
        const showAvgDealCount = this.isGroupBySalesPerson() && key === 'DEAL_COUNT_SUM';

        // Edge Case, incase of vehicleAge, and contractAge show vehicleAge avg and contract age avg respectively
        // and also pick respective renderers
        let keyToBeaccessed = ACCESSOR_KEYS_FOR_REPORTS[key] || key;
        if (isStandardMake && key === KEYS.MAKE) {
          keyToBeaccessed = KEYS.DISPLAY_MAKE;
        }
        const reportsRenderer = RENDERER_FOR_AGGREGATE_REPORTS[keyToBeaccessed] || renderer;

        // eslint-disable-next-line no-param-reassign
        value = showAvgDealCount
          ? reportsColumnAccessor('DEAL_COUNT_SALES_PERSON_SUM', original)
          : reportsColumnAccessor(keyToBeaccessed, original);
        return renderReportCell(
          reportsRenderer,
          value,
          keyToBeaccessed,
          original,
          KEYS.DISPLAY_MAKE,
          KEYS.MAKE,
          DEAL_SALES_LOG_FILTER_TYPES.MAKE
        );
      }

      // For FNI Products extra mapping is needed
      if (columnType) {
        // eslint-disable-next-line no-param-reassign
        value = getValueUsingColMetaData(original, columnType, accessor, filters, selectedGroupBy, reportsFilter);
      }
      return renderTableCell(renderer, value, key, original);
    }
  );

  getCell =
    ({
      key,
      bulkNotes,
      columnMetaData,
      sourceTypeOptions,
      fniProductMapById,
      reportsFilter,
      viSettings,
      selectedGroupBy,
      tableFilters,
      getFormattedDateAndTime,
      getFormattedNumber,
      activityList,
      lendersInfo,
      currentDealerId,
    }) =>
    ({ original, value }) => {
      const {
        dealTypesFromSetup,
        dealCustomStatuses,
        taxCodes,
        dealerOemSites,
        dataMaskingProperties,
        getFeatureValue,
      } = this.props;
      const { enterpriseV2Workspaces, accessibleDealers } = this.state;
      const filters = [...reportsFilter, ...tableFilters]; // Handle if Salesperson filter is applied
      let renderer = null;
      let columnType = null;
      const fieldName = _get(columnMetaData, [key, 'fieldName']);
      const dealTypeVsDisplayName = getDealTypeVsDisplayName(dealTypesFromSetup);
      const isEnterpriseV2Enabled = getFeatureValue(FEATURE_NAME.ENTERPRISE_V2_ENABLED);
      const primaryVehicle = getPrimaryVehicle(original);
      if (
        isVehicleFieldIsMasked({
          dataMaskingProperties,
          vehicle: primaryVehicle,
          key,
          isEnterpriseV2Enabled,
          property: VEHILCE_MASKING_PROPERTIES.DEAL_REPORTS_COLUMN,
        })
      ) {
        return this.renderMaskedField();
      }

      switch (key) {
        case KEYS.DEAL_NUMBER: {
          const isCurrentDelearShipDeal = _toString(_get(original, 'dealerId')) === _toString(this.currentDealerId);
          return (
            <DealNotes
              dealNumber={original.dealNumber}
              dealId={original.id}
              bulkNotes={bulkNotes}
              userList={_values(getDealLookupUsers(original))}
              navigate={isCurrentDelearShipDeal}
              reducerKey="core"
              isMultiPinningAllowed
              personas={SEARCH_USER_BY_ALL_PERSONAS}
              fetchUsersToTag={handleFetchUsersToTagInNotes(isEnterpriseV2Enabled)}
              sourceType={sourceType}
              enableMentions
            />
          );
        }

        case KEYS.DEAL_NOTE: {
          const lastNote =
            _get(_orderBy(_get(bulkNotes, `${original.id}.notes`), 'createdTime', 'desc'), '[0].text') || NO_DATA;

          return (
            <Ellipses lines={1} tooltip>
              {lastNote}
            </Ellipses>
          );
        }

        case KEYS.TRADE_YMM:
        case KEYS.TRADE_1_YMM:
        case KEYS.TRADE_2_YMM:
        case KEYS.TRADE_3_YMM:
        case KEYS.TRADE_4_YMM:
        case KEYS.TRADE_5_YMM:
          return (
            <MultipleContentRenderer list={value} accessor={_property('tradeInVehicle')} renderer={getYearMakeModel} />
          );

        case KEYS.CUSTOMER: {
          const { primaryCustomer, secondaryCustomer } = original;
          const buyerDeleteReason = _get(primaryCustomer, 'deleteReason') || null;
          const coBuyerDeleteReason = _get(secondaryCustomer, 'deleteReason') || null;
          const buyerName = getCustomerName(primaryCustomer);
          const coBuyerName = getCustomerName(secondaryCustomer);
          return (
            <CustomerCell
              customer1={buyerName}
              customer2={coBuyerName}
              popoverLabel1={BUYER}
              popoverLabel2={CO_BUYER}
              buyerDeleteReason={buyerDeleteReason}
              coBuyerDeleteReason={coBuyerDeleteReason}
            />
          );
        }

        case KEYS.CUSTOMER_NUMBER: {
          const oldDisplayId = _get(original, 'primaryCustomer.oldDisplayId') || EMPTY_STRING;
          return <HeaderWithInfo heading={value} helpText={oldDisplayId} />;
        }

        case KEYS.STOCK_ID: {
          return getPrimaryVehicleValue(original, value, currentDealerId);
        }

        case KEYS.TRADEINS: {
          if (!_isArray(value)) return NO_DATA;
          const renderItem = item => {
            const tradeInVehicle = _get(item, 'tradeInVehicle');
            if (!tradeInVehicle) return NO_DATA;

            const year = _get(tradeInVehicle, 'year', EMPTY_STRING);
            const displayMake = _get(tradeInVehicle, 'displayMake') || _get(tradeInVehicle, 'make', EMPTY_STRING);
            const displayModel = _get(tradeInVehicle, 'displayModel') || _get(tradeInVehicle, 'model', EMPTY_STRING);
            return _toString(`${year} ${displayMake} ${displayModel}`).trim() || NO_DATA;
          };

          return <PopoverWithCount data={value} renderItem={renderItem} placement={POPOVER_PLACEMENT.BOTTOM_RIGHT} />;
        }

        case KEYS.BUYER_FEEDBACK:
        case KEYS.CO_BUYER_FEEDBACK:
          return (
            <Ellipses length={10} tooltip>
              {value}
            </Ellipses>
          );

        case KEYS.DEAL_SUB_STATUS: {
          const { dealNumber } = original;

          const options = getSubStatusOptionsForFlagPicker(dealCustomStatuses);

          if (!dealNumber) return NO_DATA;
          return (
            <FlagPickerWithRadio
              flagOptions={options}
              selectedValue={getSubStatusId(original?.subStatusId, original?.subStatus, options)}
              showLabel
              readOnly
            />
          );
        }

        case KEYS.SOURCE_TYPE: {
          const selectedSourceType =
            _find(sourceTypeOptions, ({ value: sourceTypeValue }) => sourceTypeValue === value) || EMPTY_OBJECT;
          const displayLabel = _get(selectedSourceType, 'label') || value;
          return displayLabel;
        }

        case KEYS.TAX_CODE: {
          const taxCodeObj = _find(taxCodes, { id: value }) || EMPTY_OBJECT;
          const taxCode = _get(taxCodeObj, 'taxCode') || '-';
          return taxCode;
        }

        case KEYS.CREDIT_APPLICATION_STATUS: {
          return APPLICATION_STATUS_LABEL[original?.creditApplicationStatus];
        }

        default: {
          let accessor = null;
          const GDPR_OBJECT = _includes(selectedGroupBy, key) ? null : GDPR_CONFIGS[key];
          const reader = _get(GDPR_OBJECT, 'reader') || EMPTY_STRING;
          const deleteReason = _get(GDPR_OBJECT, 'deleteReason') || EMPTY_STRING;
          const formatter = _get(GDPR_OBJECT, 'formatter') || _noop;
          if (!_isEmpty(GDPR_OBJECT)) {
            const valueObj = !_isEmpty(reader) ? _get(original, reader) : original;
            const formattedValue = formatter !== _noop ? formatter(valueObj) : valueObj;
            const deleteReasonForCPRAGDPR = _get(original, deleteReason) || null;
            return _isEmpty(deleteReasonForCPRAGDPR) ? (
              formattedValue
            ) : (
              <WithErrorIcon deleteReasonForCPRAGDPR={deleteReasonForCPRAGDPR} />
            );
          }

          if (_includes(KEYS_VIA_FIELD_NAME, key)) {
            accessor = CUSTOM_ACCESSOR_VIA_FIELD_NAME[key](fieldName);
          } else {
            // Has Custom Accessor ?
            accessor = COLUMN_VS_CUSTOM_ACCESSOR({ filters, activityList, selectedGroupBy, reportsFilter })[key];
          }

          // If Custom accessor is not present get from column metadata
          if (!accessor) {
            const fieldType = _get(columnMetaData, [key, 'fieldType']);
            accessor = fieldName;
            columnType = _get(columnMetaData, [key, 'columnType']);
            renderer =
              COLUMN_META_VS_CUSTOM_RENDERER[key] ||
              COLUMN_VS_RENDERER_TYPE({ filters, key, fieldName, getFormattedDateAndTime })[fieldType];
          } else {
            renderer = COLUMN_VS_CUSTOM_RENDERER({
              lendersInfo,
              filters,
              dealTypeVsDisplayName,
              fniProductMapById,
              viSettings,
              dealerOemSites,
              selectedGroupBy,
              getFormattedNumber,
              isEnterpriseV2Enabled,
              enterpriseV2Workspaces,
              accessibleDealers,
              dealType: getDealType(original),
              original,
            })[key];
          }
          renderer = _isArray(renderer) ? _flow(renderer) : renderer;

          return this.getCellValue(
            key,
            value,
            original,
            renderer,
            columnType,
            accessor,
            filters,
            selectedGroupBy,
            reportsFilter
          );
        }
      }
    };

  getCellRenderer = (
    key,
    defaultAccessor,
    bulkNotes,
    columnMetaData,
    sourceTypeOptions,
    fniProductMapById,
    reportsFilter,
    viSettings,
    selectedGroupBy,
    tableFilters,
    getFormattedDateAndTime,
    getFormattedNumber,
    lendersInfo
  ) => {
    let Cell = null;
    const { activityList } = this.props;
    let accessor = null;
    const sortable = !_get(columnMetaData, [key, 'sortDisabled']);

    accessor = getAccessor({
      key,
      defaultAccessor,
      columnMetaData,
      reportsFilter,
      selectedGroupBy,
      tableFilters,
      activityList,
    });

    Cell = this.getCell({
      key,
      bulkNotes,
      columnMetaData,
      sourceTypeOptions,
      fniProductMapById,
      reportsFilter,
      viSettings,
      selectedGroupBy,
      tableFilters,
      getFormattedDateAndTime,
      getFormattedNumber,
      activityList,
      lendersInfo,
      currentDealerId: this.currentDealerId,
    });

    return { Cell, accessor, sortable };
  };

  getColumns = defaultMemoize(
    (
      columns,
      columnConfigurator,
      bulkNotes,
      columnMetaData,
      sourceTypeOptions,
      fniProductMapById,
      reportsFilter,
      viSettings,
      selectedGroupBy,
      tableFilters,
      lendersInfo
    ) => {
      const { isDefaultReport, createCustomReportMode, getFormattedDateAndTime, getFormattedNumber, defaultReport } =
        this.props;
      const columnsWithCellsOverride = columns.map(column => ({
        ...column,
        ...this.getCellRenderer(
          column.key,
          column.accessor,
          bulkNotes,
          columnMetaData,
          sourceTypeOptions,
          fniProductMapById,
          reportsFilter,
          viSettings,
          selectedGroupBy,
          tableFilters,
          getFormattedDateAndTime,
          getFormattedNumber,
          lendersInfo
        ),
      }));

      return [
        ...columnsWithCellsOverride,
        {
          headerClassName: styles.columnConfiguratorHeader,
          ...((isDefaultReport || createCustomReportMode || defaultReport) && columnConfigurator),
        },
      ];
    }
  );

  getSubHeaderProps = (subHeaderProps, isNoticeOfSalesReportVisible) =>
    produce(subHeaderProps, draft => {
      const { subHeaderRightActions: rightActions } = draft;
      const exportActions = _find(rightActions, ({ id }) => id === 'exportActions');
      _set(exportActions, 'renderOptions.onClick', this.download);
      if (isNoticeOfSalesReportVisible) {
        _set(exportActions, 'renderOptions.options', [
          ..._get(exportActions, 'renderOptions.options'),
          CUTOM_REPORT_OPTION(),
        ]);
      }
    });

  isGroupBySalesPerson = () => {
    const { selectedGroupBy } = this.props;
    return _includes(selectedGroupBy, KEYS.SALES_PERSON);
  };

  getSortPayload = () => {
    const { sort, columnMetaData } = this.props;
    const isStandardMake = DealerPropertyHelper.standardizedMakeSetupEnabled();
    return _map(sort, item => {
      const { key, field } = item;
      const sortKey = _get(columnMetaData, [key, 'sortKey']) || key;
      const sortField = _get(columnMetaData, [key, 'sortKey']) || field;
      return {
        ...item,
        key: sortKey,
        field:
          sortKey === KEYS.MAKE
            ? makeSortKeyWhenDealerPropertyEnabled({ isStandardMake, fallbackKey: sortField })
            : sortField,
      };
    });
  };

  getDealsPayload = (page = 1, itemsPerPage, searchText) => {
    const { columnKeyVsField, reportsFilter, tableFilters } = this.props;
    const pageToFetch = page > 0 ? page - 1 : 0;
    return createEntityPayload({
      page: pageToFetch,
      itemsPerPage,
      searchText,
      tableFilters: replaceNoneAsEmptyString(tableFilters),
      reportsFilter,
      columnKeyVsField,
      sort: this.getSortPayload(),
    });
  };

  getReportsPayload = (page = 1, itemsPerPage) => {
    const { columnKeyVsField, projections, tableFilters, columnMetaData, sort } = this.props;

    const pageToFetch = page > 0 ? page - 1 : 0;
    const { selectedGroupBy, reportsFilter } = this.props;
    return createReportPayload({
      page: pageToFetch,
      itemsPerPage,
      groups: selectedGroupBy,
      reportsFilter,
      tableFilters: replaceNoneAsEmptyString(tableFilters),
      columnKeyVsField,
      projections,
      sort: updateSortDataWithCustomField(sort),
      columnMetaData,
      useDisplayMakeForMake: DealerPropertyHelper.standardizedMakeSetupEnabled(),
      groupByMakeKey: KEYS.MAKE,
    });
  };

  // getReports = (page, resultsPerPage) => {
  //   const { getDealReports } = this.props;

  //   const reportsPayload = this.getReportsPayload(page, resultsPerPage);
  //   const modifiedReportsPayloadForSite = this.getModifiedReportsPayload(reportsPayload);

  //   return getDealReports(modifiedReportsPayloadForSite);
  // };

  // Decide if Reports or BreakDown need to be fetched
  getTableData = async (page = 1, resultsPerPage = DEFAULT_PAGE_SIZE) => {
    const { selectedGroupBy, reportsFilter } = this.props;
    const isAvailableFiltersApplied = reportsFilter.length === selectedGroupBy.length;
    if (!_isEmpty(selectedGroupBy) && !isAvailableFiltersApplied) {
      await this.getReportsAndAggregatedReports(page, resultsPerPage);
    } else {
      await this.getReportsBreakdown(page, resultsPerPage);
    }
  };

  getReportsBreakdown = async (page, resultsPerPage) => {
    const { getDealReportsBreakDown } = this.props;
    const dealsPayload = this.getDealsPayload(page, resultsPerPage);

    const modifiedDealspayloadForSite = this.setSitesForFilterPayloads(dealsPayload);

    const reportsPayload = this.getReportsPayload();
    const modifiedReportsPayload = this.getModifiedReportsPayload(reportsPayload);

    await getDealReportsBreakDown(modifiedReportsPayload, modifiedDealspayloadForSite);
  };

  getReportsAndAggregatedReports = async (page, resultsPerPage) => {
    const { getDealReportsAndAggregatedReports } = this.props;

    const reportsPayload = this.getReportsPayload(page, resultsPerPage);
    const modifiedReportsPayloadForSite = this.getModifiedReportsPayload(reportsPayload);

    const aggregatedReportPayload = this.getAggregatedReportsPayload();
    const modifiedAggregatedReportsPayload = this.getModifiedReportsPayload(aggregatedReportPayload);

    await getDealReportsAndAggregatedReports(modifiedReportsPayloadForSite, modifiedAggregatedReportsPayload);
  };

  onAction = action => {
    const { payload, type } = action;
    if (!type) return null;

    const { onAction, customReportAction } = this.props;
    onAction(action);
    customReportAction(action);

    const { value } = payload;
    switch (type) {
      case TableManagerActions.TABLE_ITEMS_PAGE_UPDATE:
        return this.getTableData(value.page, value.resultsPerPage);
      default:
        return null;
    }
  };

  onBackPress = () => {
    const { navigate } = this.props;
    navigate(`/${CORE}/${REPORTS_PATH}/sales`);
  };

  downloadNoticeOfSalesReport = () => {
    const { tableFilters, getNoticeOfSalesReport } = this.props;
    const filterPayload = createCustomNoticeOFSalesReportPayload(tableFilters);
    getNoticeOfSalesReport(filterPayload);
  };

  setFieldForFilter = filters => _map(filters, filter => ({ ...filter, field: filter?.type }));

  download = ({ key }) => {
    const { onDownloadCSV, tableFilters } = this.props;
    switch (key) {
      case SALES_EXPORT_FILE_TYPES.NOTICE_OF_SALES_REPORT:
        this.downloadNoticeOfSalesReport();
        break;
      default: {
        const payload = { filters: this.setFieldForFilter(replaceNoneAsEmptyString(tableFilters)) };
        const tableFiltersWithSite = this.setSitesForFilterPayloads(payload);
        onDownloadCSV({
          key,
          tableFiltersWithSite: tableFiltersWithSite.filters,
          sortDetails: this.getSortPayload(),
        });
        break;
      }
    }
  };

  renderMaskedField = () => <Content>{VEHICLE_MASKING_FIELD_LABEL}</Content>;

  render() {
    const {
      contentHeight,
      columns,
      navigate,
      location,
      columnConfigurator,
      reports,
      dealList,
      count,
      currentPage,
      itemsPerPage,
      isFetching,
      tableFilters,
      selectedFilterGroup,
      headerProps,
      tableActionButton: headerRightActions,
      bulkNotes,
      filters,
      onTableDropDownFilterChange,
      columnMetaData,
      sortDetails,
      isDefaultReport,
      dealTypesFromSetup,
      dealCustomStatuses,
      lendersInfo,
      columnMetaDataLoaded,
      generalFields,
      fniProductMapById,
      resizedProps,
      subHeaderProps,
      isNoticeOfSalesReportVisible,
      reportsFilter,
      viSettings,
      dealMetadata,
      getDealerPropertyValue,
      loggedInUserSites,
      taxCodes,
      dealSetupInfo,
      rolesList,
      selectedGroupBy,
      aggregatedReports,
      getFeatureValue,
    } = this.props;
    const { enterpriseV2Workspaces, accessibleDealers } = this.state;
    /**
     * wait for columnMetaDataLoaded as columnMetaData is used to create filterTypes
     */
    if (!columnMetaDataLoaded) return <Spinner />;
    const tableProps = {
      ...DEFAULT_TABLE_PROPS,
      totalNumberOfEntries: count,
      currentPage,
      loading: isFetching,
      pageSize: itemsPerPage,
      totalNumberOfPages: Math.ceil(count / (itemsPerPage - 1)),
      ...resizedProps,
      displayFirstLast: false,
    };

    const permissions = this.context;
    const hasCreateReportAccess = permissionValidations.validatePermissions(permissions, {
      validFor: [PERMISSIONS.CORE.REPORTS.SALES_REPORT_CREATE],
    });
    const hasEditReportAccess = permissionValidations.validatePermissions(permissions, {
      validFor: [PERMISSIONS.CORE.REPORTS.SALES_REPORT_EDIT],
    });
    // Allow report creation option only if CREATE permission is available!
    let headerRightButton = hasCreateReportAccess ? headerRightActions : null;
    if (!headerRightButton) {
      headerRightButton = hasEditReportAccess && !isDefaultReport ? headerRightActions : null;
    }
    const sourceTypeOptions = getSourceTypeOptions(generalFields);

    const isMultiOEMEnabled = getDealerPropertyValue(DEALER_PROPERTY_CONSTANTS.MULTI_OEM_SWITCH_ENABLED);
    const isEnterpriseV2Enabled = getFeatureValue(FEATURE_NAME.ENTERPRISE_V2_ENABLED);
    const filterProps = getFilterProps(
      filters,
      onTableDropDownFilterChange,
      tableFilters,
      selectedFilterGroup,
      this.getCustomReportId(),
      dealTypesFromSetup,
      dealCustomStatuses,
      lendersInfo,
      columnMetaData,
      sourceTypeOptions,
      dealMetadata,
      isMultiOEMEnabled ? loggedInUserSites : EMPTY_ARRAY,
      taxCodes,
      dealSetupInfo,
      rolesList,
      isEnterpriseV2Enabled,
      enterpriseV2Workspaces,
      accessibleDealers
    );

    const cols = this.getColumns(
      columns,
      columnConfigurator,
      bulkNotes,
      columnMetaData,
      sourceTypeOptions,
      fniProductMapById,
      reportsFilter,
      viSettings,
      selectedGroupBy,
      tableFilters,
      lendersInfo
    );

    return (
      <div className="full-width" style={{ height: contentHeight }} navigate={navigate} location={location}>
        <TableManager
          columns={cols}
          onAction={this.onAction}
          tableManagerProps={TABLE_MANAGER_PROPS}
          tableRowHeight={TABLE_ROW_HEIGHT}
          filterProps={filterProps}
          tableProps={tableProps}
          headerProps={{
            ...HEADER_PROPS,
            ...headerProps,
            headerRightActions: headerRightButton,
            goBackHandler: this.onBackPress,
          }}
          minRows={0}
          data={this.getData(aggregatedReports, reports, dealList)}
          subHeaderProps={this.getSubHeaderProps(subHeaderProps, isNoticeOfSalesReportVisible)}
          sortDetails={sortDetails}
          isMultiSort
          allColumns={cols}
        />
      </div>
    );
  }
}

SalesPerfomance.propTypes = {
  columns: PropTypes.array.isRequired,
  reports: PropTypes.arrayOf(PropTypes.object).isRequired,
  contentHeight: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
  columnConfigurator: PropTypes.object.isRequired,
  // getDealReports: PropTypes.func.isRequired,
  count: PropTypes.number.isRequired,
  currentPage: PropTypes.number.isRequired,
  columnKeyVsField: PropTypes.object.isRequired,
  projections: PropTypes.array.isRequired,
  isFetching: PropTypes.bool.isRequired,
  getDealReportsBreakDown: PropTypes.func.isRequired,
  fetchTaxCodes: PropTypes.func.isRequired,
  getVISettings: PropTypes.func.isRequired,
  bulkNotes: PropTypes.object,
  taxCodes: PropTypes.array.isRequired,
  onDownloadCSV: PropTypes.func.isRequired,
  isNoticeOfSalesReportVisible: PropTypes.bool.isRequired,
  viSettings: PropTypes.object.isRequired,
  getDealReportsAndAggregatedReports: PropTypes.func.isRequired,
  aggregatedReports: PropTypes.array,
  navigate: PropTypes.func.isRequired,
  location: PropTypes.object.isRequired,
  defaultReport: PropTypes.bool,
};

SalesPerfomance.defaultProps = {
  bulkNotes: EMPTY_OBJECT,
  aggregatedReports: EMPTY_ARRAY,
  defaultReport: false,
};

SalesPerfomance.contextType = PermissionContext;

export default compose(withTekionConversion, withExperienceEngineConsumer)(SalesPerfomance);
