// COMMUNICATION
const INTERNAL_TENANT_COMMUNICATION = 'INTERNAL_TENANT_COMMUNICATION';

const EXTERNAL_COMMUNICATION = 'EXTERNAL_COMMUNICATION'; // Property for Customer Communication feature.
const INTERNAL_COMMUNICATION = 'INTERNAL_COMMUNICATION'; // Property for Internal Communication feature.

const COMMUNICATION_ANALYTICS_METRICS = 'COMMUNICATION_ANALYTICS_METRICS'; // Property for Communication Analytics feature.

const COMMUNICATION_V2_ENABLED = 'COMMUNICATION_V2_ENABLED';
const ENTERPRISE_COMMUNICATION_ENABLED = 'ENTERPRISE_COMMUNICATION_ENABLED';
const LIVE_CHAT_WIDGET_ENABLED = 'LIVE_CHAT_WIDGET_ENABLED'; // Property for Live Communication feature.
const LIVE_CHAT_WEBSOCKET_ENABLE = 'LIVE_CHAT_WEBSOCKET_ENABLE'; // property for Enabling or Disabling Live Communication pusher messaging feature.
const LIVE_CHAT_VIDEO_CALL_ENABLED = 'LIVE_CHAT_VIDEO_CALL_ENABLED'; // property for Enabling or Disabling Live Communication Video Calling feature.
const COMMUNICATION_SOUND_NOTIFICATIONS = 'COMMUNICATION_SOUND_NOTIFICATIONS';
const FAQ_PLATFORM_ENABLED = 'FAQ_PLATFORM_ENABLED'; // Property for Faq Builder feature
const SHOW_ROLES_FOR_SUPER_ADMIN = 'SHOW_ROLES_FOR_SUPER_ADMIN'; // Property to only show Roles screen for superAdmin when turned on.
const EXTENDED_CHECK_PAYEE_ADDRESS = 'EXTENDED_CHECK_PAYEE_ADDRESS'; // Property to extended check payee address to another line when turned on.
const CAMPAIGN_MANAGEMENT_DEALER_PROPERTY = 'CAMPAIGN_MANAGEMENT_DEALER_PROPERTY'; // Property to control display of Campaign feature
const CUSTOMER_PROSPECTING_ENABLED = 'CUSTOMER_PROSPECTING_ENABLED';
const RELEASE_ADMIN = 'RELEASE_ADMIN'; // Property to show the release notes tile when on
const INVENTORY_PARAMETERS_ENABLED = 'INVENTORY_PARAMETERS_ENABLED';
const INSPECTIONS_AND_RECOMMENDATIONS_PDF_ENABLED = 'INSPECTIONS_AND_RECOMMENDATIONS_PDF_ENABLED';
const INSPECTION_ADDITIONAL_MEASUREMENTS_ENABLED = 'INSPECTION_ADDITIONAL_MEASUREMENTS_ENABLED';
const IS_OBD_ENABLED = 'IS_OBD_ENABLED';
const VIS_PDF_ENABLED = 'VIS_PDF_ENABLED';
const NEW_DSE_ENABLED = 'NEW_DSE_ENABLED';
const WEB_CHECKIN_ENABLED = 'WEB_CHECKIN_ENABLED';
const DEALERS_ALLOWED_TO_SEARCH_IN = 'DEALERS_ALLOWED_TO_SEARCH_IN';
const SCHEDULING_AUDIT_LOGS_ENABLED = 'SCHEDULING_AUDIT_LOGS_ENABLED';
const SCHEDULING_BY_HOURS_ENABLED = 'SCHEDULING_BY_HOURS_ENABLED';
const ENTERPRISE_BDC_ENABLED = 'ENTERPRISE_BDC_ENABLED';
const AUTO_DISPATCH_FEATURE_ENABLED = 'AUTO_DISPATCH_FEATURE_ENABLED';
const AUTO_PROMISE_TIME_ENABLED = 'AUTO_PROMISE_TIME_ENABLED';
const NEW_PROMISE_TIME_SERVICE_ENABLED = 'NEW_PROMISE_TIME_SERVICE_ENABLED';
const KEY_DROP_OFF_ENABLED = 'KEY_DROP_OFF_ENABLED';
const CONSUMER_PORTAL_ENABLED = 'CONSUMER_PORTAL_ENABLED';
const CCPA_ENABLED = 'CCPA_ENABLED';
const SERVICE_MENU_EDITABLE = 'SERVICE_MENU_EDITABLE';
const NEW_CUSTOMER_CHANGE_DISPLAY_NUMBER_CONTROL = 'NEW_CUSTOMER_CHANGE_DISPLAY_NUMBER_CONTROL';
const NEW_VENDOR_CHANGE_DISPLAY_NUMBER_CONTROL = 'NEW_VENDOR_CHANGE_DISPLAY_NUMBER_CONTROL';
const AUTO_RIM_PART_EXIT_PROCESS = 'AUTO_RIM_PART_EXIT_PROCESS';
const KNOWLEDGE_BASE_ADMIN = 'KNOWLEDGE_BASE_ADMIN';
const SERVICE_NOW_ENABLED = 'SERVICE_NOW_ENABLED';
const EMPLOYEE_LEADER_BOARD_OBJECTIVES = 'EMPLOYEE_LEADER_BOARD_OBJECTIVES';
const NEW_IMAGE_UPLOADER_ENABLED_IN_SERVICE_MODULE = 'NEW_IMAGE_UPLOADER_ENABLED_IN_SERVICE_MODULE';
const NEW_MPVI_ENABLED = 'NEW_MPVI_ENABLED';
const NEW_MOBILE_CHECK_IN_ENABLED = 'NEW_MOBILE_CHECK_IN_ENABLED';
const QUOTE_SERVICE_ENABLED = 'QUOTE_SERVICE_ENABLED';
const RO_LIST_AUTO_REFRESH_DISABLED = 'RO_LIST_AUTO_REFRESH_DISABLED';
const RO_NEW_ADD_JOB_VIEW_ENABLED = 'RO_NEW_ADD_JOB_VIEW_ENABLED';
const DEALER_LOGOS_ENABLED = 'DEALER_LOGOS_ENABLED';
const OEM_DETAILS_ENABLED = 'OEM_DETAILS_ENABLED';
const TAP_SUPPORT_CHAT = 'TAP_SUPPORT_CHAT';
const RO_DASHBOARD_V2_DISABLED = 'RO_DASHBOARD_V2_DISABLED';
const SERVICE_CATALOG_BASE_ENABLED = 'SERVICE_CATALOG_BASE_ENABLED';
const SERVICE_CATALOG_PRO_ENABLED = 'SERVICE_CATALOG_PRO_ENABLED';
const SERVICE_CATALOG_PRO_V2_ENABLED = 'SERVICE_CATALOG_PRO_V2_ENABLED';
const EMPLOYEE_ROLE_EDIT_DISABLED = 'EMPLOYEE_ROLE_EDIT_DISABLED';
const DOWNLOAD_CLAIMS_ENABLED = 'DOWNLOAD_CLAIMS_ENABLED';
const CMS_DOB_STR_ENABLED = 'CMS_DOB_STR_ENABLED';
const OBD_ENABLED = 'OBD_ENABLED';
const OBD_DATA_SOURCE_PRIORITY_SETTINGS_ENABLED = 'OBD_DATA_SOURCE_PRIORITY_SETTINGS_ENABLED';
const BMW_AIR_KSD_ENABLED = 'BMW_AIR_KSD_ENABLED';
const EMPLOYEE_SCHEDULER_ENABLED = 'EMPLOYEE_SCHEDULER_ENABLED';
const EMPLOYEE_SCHEDULER_AVAILABILITY_ENABLED = 'EMPLOYEE_SCHEDULER_AVAILABILITY_ENABLED';
const SERVICE_MENU_PRO_ENABLED = 'SERVICE_MENU_PRO_ENABLED';
const KEYLOUNGE_WALKIN_ENABLED = 'KEYLOUNGE_WALKIN_ENABLED';
const AUTO_PARTS_PRICING_ENABLED = 'AUTO_PARTS_PRICING_ENABLED';
const SERVICE_SCREENS_PARTS_SEARCH_ENABLED = 'SERVICE_SCREENS_PARTS_SEARCH_ENABLED';
const DEALERTIRE_ENABLED = 'DEALERTIRE_ENABLED';
const SESSION_RECORDER_PROVIDER = 'SESSION_RECORDER_PROVIDER';
const SESSION_RECORDING_FETCH_CAPTURE_DISABLED = 'SESSION_RECORDING_FETCH_CAPTURE_DISABLED';
const SUBLET_PO_MULTIPLE_RO = 'SUBLET_PO_MULTIPLE_RO';
const SERVICE_HISTORY_BULK_PDF_DOWNLOAD_ENABLED = 'SERVICE_HISTORY_BULK_PDF_DOWNLOAD_ENABLED';
const FCA_DEFAULT_MARKUP_API = 'FCA_DEFAULT_MARKUP_API';
const FORD_NEW_PDR_CODE_ENABLED = 'FORD_NEW_PDR_CODE_ENABLED';
const INSPECTION_LABOR_OPCODES_REVIEW_ENABLED = 'INSPECTION_LABOR_OPCODES_REVIEW_ENABLED';
const TEKION_PAY_ENABLED = 'TEKION_PAY_ENABLED';
const TEKION_PAY_SERVER_SIDE_ENABLED = 'TEKION_PAY_SERVER_SIDE_ENABLED';
const TPAY_PAYMENT_LINK_ENABLED = 'TPAY_PAYMENT_LINK_ENABLED';
const TPAY_DASHBOARDS_ENABLED = 'TPAY_DASHBOARDS_ENABLED';
const ENABLE_MULTI_SESSION_TABS = 'ENABLE_MULTI_SESSION_TABS';
const RV_DEALER_ENABLED = 'RV_DEALER_ENABLED';
const WEB_CHECKIN_NEW_ADD_JOB_VIEW_ENABLED = 'WEB_CHECKIN_NEW_ADD_JOB_VIEW_ENABLED';
const SALES_LEAD_MANUAL_STAGE_MOVEMENT_ENABLED = 'SALES_LEAD_MANUAL_STAGE_MOVEMENT_ENABLED';
const DEFERRED_RECALL_REPORT_ENABLED = 'DEFERRED_RECALL_REPORT_ENABLED';
const CASH_ON_DELIVERY_ENABLED = 'CASH_ON_DELIVERY_ENABLED';
const LOANER_ONTRAC_ENABLED = 'LOANER_ONTRAC_ENABLED';
const TEKION_WALLET_ENABLED = 'TEKION_WALLET_ENABLED';
const CONFIGURABLE_PAYMENT_MODES = 'CONFIGURABLE_PAYMENT_MODES';
const CASHIERING_PROCUREMENT_CARD_ENABLED = 'CASHIERING_PROCUREMENT_CARD_ENABLED';
const INTERAC_REFUND_ENABLED = 'INTERAC_REFUND_ENABLED';
const TRANSACTION_LIMIT_ON_ALL_SOURCES_ENABLED = 'TRANSACTION_LIMIT_ON_ALL_SOURCES_ENABLED';
const REFUND_APPROVAL_ENABLED = 'REFUND_APPROVAL_ENABLED';
const CASHIERING_DEMO_APP_ENABLED = 'CASHIERING_DEMO_APP_ENABLED';
const DAY_COLLECTION_ENTERPRISE_V2_ENABLED = 'DAY_COLLECTION_ENTERPRISE_V2_ENABLED';

const DISPLAY_CUSTOMER_ID_IN_APPOINTMENT_PDF_ENABLED = 'DISPLAY_CUSTOMER_ID_IN_APPOINTMENT_PDF_ENABLED';
const ENABLE_TAX_BREAKUP = 'ENABLE_TAX_BREAKUP';
const TAX_GROUP_ENABLED = 'TAX_GROUP_ENABLED';
const TEKION_CREDIT_COMPLIANCE = 'TEKION_CREDIT_COMPLIANCE';
const DEALER_TRACK_DFE_CREDIT_APPLICATION_ENABLED = 'DEALER_TRACK_DFE_CREDIT_APPLICATION_ENABLED';
const CUDL_DFE_CREDIT_APPLICATION_ENABLED = 'CUDL_DFE_CREDIT_APPLICATION_ENABLED';
const IS_LEASE_NOT_SUPPORTED = 'IS_LEASE_NOT_SUPPORTED';
const VEHICLE_TYPE_CONFIGURATION_ENABLED = 'VEHICLE_TYPE_CONFIGURATION_ENABLED';
const STANDARDIZED_MAKE_SETUP_ENABLED = 'STANDARDIZED_MAKE_SETUP_ENABLED';
const IS_ML_DASHBOARD_ENABLED = 'IS_ML_DASHBOARD_ENABLED';
const NEW_DOWNLOAD_CENTRE_ENABLED = 'NEW_DOWNLOAD_CENTRE_ENABLED';
const LEASE_COMPARISION_ENABLED = 'LEASE_COMPARISION_ENABLED';
const FLEETS = 'FLEETS';
const CAMPAIGN_COLLECTION_V3_ENABLED = 'CAMPAIGN_COLLECTION_V3_ENABLED';
const PERPETUAL_CAMPAIGNS_ENABLED = 'PERPETUAL_CAMPAIGNS_ENABLED';
const CENTRAL_COMMUNICATION_OPTIN_PREFERENCES_ENABLED = 'CENTRAL_COMMUNICATION_OPTIN_PREFERENCES_ENABLED';
const WEB_NOTIFICATION_METRICS_ENABLED = 'WEB_NOTIFICATION_METRICS_ENABLED';
const DEALER_CONFIG_GENERAL_DETAILS_FIELD_MODIFICATION_ENABLED =
  'DEALER_CONFIG_GENERAL_DETAILS_FIELD_MODIFICATION_ENABLED';

const PRINT_METRICS_ENABLED = 'PRINT_METRICS_ENABLED';
const OEM_REWARDS_DISABLED = 'OEM_REWARDS_DISABLED';
const ENABLE_DEALS_DASHBOARD = 'ENABLE_DEALS_DASHBOARD';
const DEALER_CONFIG_LOCALE_SETTING_ENABLED = 'DEALER_CONFIG_LOCALE_SETTING_ENABLED';

const TRAINING_USER_SENTIMENT_ENABLED = 'TRAINING_USER_SENTIMENT_ENABLED';
const CMS_INCLUDE_ADDRESS_IN_DUPLICATE_PREVENTION = 'CMS_INCLUDE_ADDRESS_IN_DUPLICATE_PREVENTION';

const TCA_EXTERNAL_CONTRACTS_ENABLED = 'TCA_EXTERNAL_CONTRACTS_ENABLED';

// --------------- Service Specific dealer properties START -------------- //

const SERVICE_V3_ENABLED = 'SERVICE_V3_ENABLED';
const CENTRAL_VEHICLE_ASSOCIATION_ENABLED = 'CENTRAL_VEHICLE_ASSOCIATION_ENABLED';
const CHARGE_CUSTOMER_COLOR_CODING_ENABLED = 'CHARGE_CUSTOMER_COLOR_CODING_ENABLED';
const VEHICLE_ATTRIBUTE_STANDARDISATION_ENABLED = 'VEHICLE_ATTRIBUTE_STANDARDISATION_ENABLED';

// --------------- Service Specific dealer properties END -------------- //
// --------------- Parts Specific dealer properties START -------------- //
const MY_PRICELINK_INTEGRATION_ENABLED = 'MY_PRICELINK_INTEGRATION_ENABLED';
const FULFILMENT_EDIT_LOCK = 'FULFILMENT_EDIT_LOCK';
const AUTO_CLOSE_SALE_ORDER = 'AUTO_CLOSE_SALE_ORDER';
const MPL_PRICING_DEFAULT_ENABLED = 'MPL_PRICING_DEFAULT_ENABLED';
const MPL_PRICING_EXPORT_ENABLED = 'MPL_PRICING_EXPORT_ENABLED';
const MPL_PRICING_QUOTE_ENABLED = 'MPL_PRICING_QUOTE_ENABLED';
const OEM_INTEGRATONS_ENABLED = 'OEM_INTEGRATONS_ENABLED';
const PARTS_MASTER_OEM_SOURCE = 'PARTS_MASTER_OEM_SOURCE'; // Property to get OEMs/Brands integrated for dealership
const REQUIRE_PASSCODE = 'REQUIRE_PASSCODE';
const CORE_MANAGEMENT_ENABLED = 'CORE_MANAGEMENT_ENABLED';
const CORE_INVENTORY_SITE_LEVEL_ENABLED = 'CORE_INVENTORY_SITE_LEVEL_ENABLED';
const BIN_SHELF_DRAWER_FEATURE_ENABLED = 'BIN_SHELF_DRAWER_FEATURE_ENABLED';
const PARTS_REMSTAR_ENABLED = 'PARTS_REMSTAR_ENABLED';
const INVENTORY_IN_OUT_SALES_ENABLED = 'INVENTORY_IN_OUT_SALES_ENABLED';
const AUTO_REPLACEMENT_FEATURE_ENABLED = 'AUTO_REPLACEMENT_FEATURE_ENABLED';
const BASELINE_PAGE_IN_PDFS_ENABLED = 'BASELINE_PAGE_IN_PDFS_ENABLED';
const PARTS_MULTI_OEM_ENABLED = 'PARTS_MULTI_OEM_ENABLED'; // Property to get DealerCodes for brands. [Not added from Integration side yet]
const PARTS_SEARCH_V2_ENABLED = 'PARTS_SEARCH_V2_ENABLED';
const PARTS_INVENTORY_SHARED = 'PARTS_INVENTORY_SHARED';
const OPSTRAX_DELIVERY_PART_ENABLED = 'OPSTRAX_DELIVERY_PART_ENABLED';
const PARTS_STOCK_ORDER_CREATION_DISABLED = 'PARTS_STOCK_ORDER_CREATION_DISABLED';
const AUTO_FILL_CHARGE_CUSTOMER = 'AUTO_FILL_CHARGE_CUSTOMER';
const CUSTOMERS_SEARCH_V2_ENABLED = 'CUSTOMERS_SEARCH_V2_ENABLED';
const SALESORDER_MULTIFACET_SEARCH_ENABLED = 'SALESORDER_MULTIFACET_SEARCH_ENABLED';
const PART_CROSS_SITE_DOCUMENT_ACCESS_ENABLED_FROM_MAIN_SITE = 'PART_CROSS_SITE_DOCUMENT_ACCESS_ENABLED_FROM_MAIN_SITE';
const FCA_CONTROL_NUMBER_ENABLED = 'FCA_CONTROL_NUMBER_ENABLED';
const VIEW_PARTS_ON_SO_LIST_ENABLED = 'VIEW_PARTS_ON_SO_LIST_ENABLED';
const PARTS_AI_SEARCH_ENABLED = 'PARTS_AI_SEARCH_ENABLED';
const PARTS_POS_ENABLED = 'PARTS_POS_ENABLED';
const SHIPMENT_RECEIVING_ENABLED = 'SHIPMENT_RECEIVING_ENABLED';
const ENABLE_SO_PRICE_OVERRIDE = 'ENABLE_SO_PRICE_OVERRIDE';
const BRINGOZ_DELIVERY_PART_ENABLED = 'BRINGOZ_DELIVERY_PART_ENABLED';
const ELITE_EXTRA_DELIVERY_PART_ENABLED = 'ELITE_EXTRA_DELIVERY_PART_ENABLED';
const OEM_STOCK_ORDER_COMPUTE_SERVICE_ENABLED = 'OEM_STOCK_ORDER_COMPUTE_SERVICE_ENABLED';
const MAX_PARTS_PRICE_V2_ENABLED = 'MAX_PARTS_PRICE_V2_ENABLED';
const SOURCE_CODE_PART_PREFIX_ENABLED = 'SOURCE_CODE_PART_PREFIX_ENABLED';
const PARTS_SALES_ORDER_LIST_V2_ENABLED = 'PARTS_SALES_ORDER_LIST_V2_ENABLED';
const PARTS_SALES_ORDER_SYNC_LINE_FLOW_ENABLED = 'PARTS_SALES_ORDER_SYNC_LINE_FLOW_ENABLED';
const ETKA_CATALOGUE_IMPORT_ENABLED = 'ETKA_CATALOGUE_IMPORT_ENABLED';
const PARTS_RECEIVING_IN_MODAL_ENABLED = 'PARTS_RECEIVING_IN_MODAL_ENABLED';
const PARTS_FOUNDATION_ENABLED = 'PARTS_FOUNDATION_ENABLED';
const SOR_V2_ENABLED = 'SOR_V2_ENABLED';
const NEW_PHYSICAL_INVENTORY_ENABLED = 'NEW_PHYSICAL_INVENTORY_ENABLED';
const NEW_PARTS_INVENTORY_SERVICE_ENABLED = 'NEW_PARTS_INVENTORY_SERVICE_ENABLED';
const PARTS_SALES_ORDER_V3_ENABLED = 'PARTS_SALES_ORDER_V3_ENABLED';
const BIN_SPOT_CHECK_ENABLED = 'BIN_SPOT_CHECK_ENABLED';
const PO_AUTOMATION_ENABLED = 'PO_AUTOMATION_ENABLED';
const PO_PART_LINE_ENABLED = 'PO_PART_LINE_ENABLED';
const CARQUEST_ENABLED = 'CARQUEST_ENABLED';
const SUPERSESSION_RECEIVING_ENABLED = 'SUPERSESSION_RECEIVING_ENABLED';
const SO_PDF_CONVERT_RICH_TEXT_TO_STRING_ENABLED = 'SO_PDF_CONVERT_RICH_TEXT_TO_STRING_ENABLED';
const STOCK_VALUATION_REPORT_ENABLED = 'STOCK_VALUATION_REPORT_ENABLED';
const PREPAID_SALES_ENABLED = 'PREPAID_SALES_ENABLED';
const CORE_INVENTORY_ENABLED = 'CORE_INVENTORY_ENABLED';
const PARTS_NEW_PHASE_IN_OUT_ENABLED = 'PARTS_NEW_PHASE_IN_OUT_ENABLED';
const REMSTAR_API_INTEGRATION_ENABLED = 'REMSTAR_API_INTEGRATION_ENABLED';
const SO_DIRECT_INVOICING_ENABLED = 'SO_DIRECT_INVOICING_ENABLED';
const WAREHOUSE_MANAGEMENT_V2_ENABLED = 'WAREHOUSE_MANAGEMENT_V2_ENABLED';
const PARTS_INVOICES_AND_RECONCILIATION_ENABLED = 'PARTS_INVOICES_AND_RECONCILIATION_ENABLED';
const INVOICE_RECONCILIATION_ENABLED = 'INVOICE_RECONCILIATION_ENABLED';
const NEW_STOCK_ORDER_CALCULATION_SERVICE = 'NEW_STOCK_ORDER_CALCULATION_SERVICE';
const VIEW_PARTS_PERFORMANCE_REPORT_ENABLED = 'VIEW_PARTS_PERFORMANCE_REPORT_ENABLED';
const ACTIVITY_LOGS_ENABLED = 'ACTIVITY_LOGS_ENABLED';
const INTER_DEALERSHIP_PO_ENABLED = 'INTER_DEALERSHIP_PO_ENABLED';
const INVENTORY_COST_INFLATION = 'INVENTORY_COST_INFLATION';
const SO_PERIODIC_INVOICE_ENABLED = 'SO_PERIODIC_INVOICE_ENABLED';
const PARTS_PO_APPROVAL_FLOW_ENABLED = 'PARTS_PO_APPROVAL_FLOW_ENABLED';
const ON_HAND_ADJUSTMENT_APPROVAL_FLOW_ENABLED = 'ON_HAND_ADJUSTMENT_APPROVAL_FLOW_ENABLED';
const SPECIAL_ORDER_PRICE_CODE_ENABLED = 'SPECIAL_ORDER_PRICE_CODE_ENABLED';
const PART_ESTIMATION_ENABLED = 'PART_ESTIMATION_ENABLED';
const OH_ADJUSTMENT_ACCOUNT_POSTING_ENABLED = 'OH_ADJUSTMENT_ACCOUNT_POSTING_ENABLED';
const USED_CORE_RETURN_CREDIT_PO_ENABLED = 'USED_CORE_RETURN_PO_ENABLED';
const NEW_RECEIVING_API_ENABLED = 'NEW_RECEIVING_API_ENABLED';
const INVENTORY_TURNOVER_REPORT_ENABLED = 'INVENTORY_TURNOVER_REPORT_ENABLED';
const PART_IMPREST_STOCK_FEATURE_ENABLED = 'PART_IMPREST_STOCK_FEATURE_ENABLED';
const NEW_DETAILED_SALES_REPORT_ENABLED = 'NEW_DETAILED_SALES_REPORT_ENABLED';
const SO_PDF_HTML_PREVIEW_ENABLED = 'SO_PDF_HTML_PREVIEW_ENABLED';
const PARTS_PDF_BACKPRINT_ENABLED = 'PARTS_PDF_BACKPRINT_ENABLED';
const PARTS_TRANSACTION_HISTORY_V2_ENABLED = 'PARTS_TRANSACTION_HISTORY_V2_ENABLED';
const PARTS_DOCUMENT_SERVICE_INTEGRATION_ENABLED = 'PARTS_DOCUMENT_SERVICE_INTEGRATION_ENABLED';
const PARTS_SALES_ORDER_BULK_CREATE_RETURN_ENABLED = 'PARTS_SALES_ORDER_BULK_CREATE_RETURN_ENABLED';
const IS_STOCK_ORDER_BY_BIN_ENABLED = 'IS_STOCK_ORDER_BY_BIN_ENABLED';
const PARTS_SALES_ORDER_ROWS_SETTINGS_ENABLED = 'PARTS_SALES_ORDER_ROWS_SETTINGS_ENABLED';
const IS_AFTERMARKET_PARTS_ENABLED = 'IS_AFTERMARKET_PARTS_ENABLED';
const PARTS_TEMPLATIZED_PDF_GENERATION_ENABLED = 'PARTS_TEMPLATIZED_PDF_GENERATION_ENABLED';
const PARTS_MULTI_WAREHOUSE_ENABLED = 'PARTS_MULTI_WAREHOUSE_ENABLED';
const PARTS_TAX_CODE_CUSTOMER_TAX_EXEMPT_ENABLED = 'PARTS_TAX_CODE_CUSTOMER_TAX_EXEMPT_ENABLED';
const IS_PHYSICAL_INVENTORY_SCANNING_ENABLED = 'IS_PHYSICAL_INVENTORY_SCANNING_ENABLED';
const PARTS_MONITORING_DASHBOARD_ENABLED = 'PARTS_MONITORING_DASHBOARD_ENABLED';
const IS_NEW_PREFERRED_PART_LIST_ENABLED = 'IS_NEW_PREFERRED_PART_LIST_ENABLED';
const BULK_SO_CREATION = 'BULK_SO_CREATION';
const PARTS_AUTO_INVOICE_ON_FILL_ENABLED = 'PARTS_AUTO_INVOICE_ON_FILL_ENABLED';

// ---- Reports ---- //
const NCM_REPORT_ENABLED = 'NCM_REPORT_ENABLED';
const MPVI_USAGE_REPORT_ENABLED = 'MPVI_USAGE_REPORT_ENABLED';
const ENABLE_FEE_DETAILS_IN_SA_REPORT = 'ENABLE_FEE_DETAILS_IN_SA_REPORT';
const SERVICE_ADVISOR_REPORT_V2_ENABLED = 'SERVICE_ADVISOR_REPORT_V2_ENABLED';
const TECHNICIAN_RECOMMENDATION_REPORT = 'TECHNICIAN_RECOMMENDATION_REPORT';
const WIP_ON_DATE_FILTER_ENABLED = 'WIP_ON_DATE_ENABLED';
const MIGRATED_TO_NEW_ADVISOR_PERFORMANCE = 'MIGRATED_TO_NEW_ADVISOR_PERFORMANCE';
const PAYROLL_LOCAL_TAX_ENABLED = 'PAYROLL_LOCAL_TAX_ENABLED';
const PAYROLL_WA_LTI_REPORT_ENABLED = 'PAYROLL_WA_LTI_REPORT_ENABLED';
const CHARGE_CUSTOMER_REPORT_ENABLED = 'CHARGE_CUSTOMER_REPORT_ENABLED';
const LOST_TIME_TRACKING_ENABLED = 'LOST_TIME_TRACKING_ENABLED';
const RO_SALES_PNA_HIDE_LIST_PRICE_COLUMN = 'RO_SALES_PNA_HIDE_LIST_PRICE_COLUMN';
const NEW_PAD_REPORT_ENABLED = 'NEW_PAD_REPORT_ENABLED';
const CORE_CANNED_REPORTS_CHARGE_CUSTOMER_ENABLED = 'CORE_CANNED_REPORTS_CHARGE_CUSTOMER_ENABLED';
const CORE_CANNED_REPORTS_NEW_CUSTOMER_CREATED_ENABLED = 'CORE_CANNED_REPORTS_NEW_CUSTOMER_CREATED_ENABLED';
const CORE_CANNED_REPORTS_PERMISSION_BY_ROLE_ENABLED = 'CORE_CANNED_REPORTS_PERMISSION_BY_ROLE_ENABLED';
const CORE_CANNED_REPORTS_PERMISSION_BY_USER_ENABLED = 'CORE_CANNED_REPORTS_PERMISSION_BY_USER_ENABLED';
const CORE_CANNED_REPORTS_USERS_BY_ROLE_ENABLED = 'CORE_CANNED_REPORTS_USERS_BY_ROLE_ENABLED';
const PARTS_TAX_CODE_ENABLED = 'PARTS_TAX_CODE_ENABLED';
const SERVICE_VISIBILITY_DASHBOARD_V2 = 'SERVICE_VISIBILITY_DASHBOARD_V2';
const TECH_PERFORMANCE_V2_ENABLED = 'TECH_PERFORMANCE_V2_ENABLED';
const PARTS_ANOMALY_ENABLED = 'PARTS_ANOMALY_ENABLED';
const PRICE_DIFFERENCE_ENABLED = 'PRICE_DIFFERENCE_ENABLED';
const PARTS_EPC_ENABLED = 'PARTS_EPC_ENABLED';
const PART_FRACTIONAL_SALE_ENABLED = 'PART_FRACTIONAL_SALE_ENABLED';
const NEW_BULK_INVENTORY_ENABLED = 'NEW_BULK_INVENTORY_ENABLED';
const PARTS_TRADE_BY_BIN = 'PARTS_TRADE_BY_BIN';
const MULTI_INVOICE_FOR_INTERNAL_SO_ENABLED = 'MULTI_INVOICE_FOR_INTERNAL_SO_ENABLED';

// --------------- Parts Specific dealer properties END -------------- //

// --- VI --- //
const VI_RE_POSTING = 'VI_RE_POSTING';
const INVENTORY_MINIMISED_INDEX = 'INVENTORY_MINIMISED_INDEX';
const VI_OPTIONS_LOOKUP = 'VI_OPTIONS_LOOKUP';

// ---- CRM ---- //
const CRM_ENABLED = 'CRM_ENABLED';
const CUSTOMER_REWARDS = 'CUSTOMER_REWARDS';
const CUSTOMER_UNMAPPED_VALUE_ENABLED = 'CUSTOMER_UNMAPPED_VALUE_ENABLED';
const OEM_ENABLED = 'OEM_ENABLED';
const CRM_LEGACY_ASSIGNEES_ENABLED = 'CRM_LEGACY_ASSIGNEES_ENABLED';
const CRM_LITE_FOR_DRP = 'CRM_LITE_FOR_DRP';
const CRM_SERVICE_PARTS_LEADS_ENABLED = 'CRM_SERVICE_PARTS_LEADS_ENABLED';
const CRM_BULK_MERGE_ENABLED = 'CRM_BULK_MERGE_ENABLED';
const CRM_PLANNER_ENABLED = 'CRM_PLANNER_ENABLED';
const CRM_COMMUNICATION_HUB_V2 = 'CRM_COMMUNICATION_HUB_V2';
const CRM_CUSTOMER_ONEVIEW_ENABLED = 'CRM_CUSTOMER_ONEVIEW_ENABLED';
const API_LAYER_ENABLED = 'API_LAYER_ENABLED';
const LEAD_CONVERSION_SCORE_ENABLED = 'LEAD_CONVERSION_SCORE_ENABLED';
const LEAD_ASSIGNMENT_V2_ENABLED = 'LEAD_ASSIGNMENT_V2_ENABLED';
const NDC_SET_UP_ENABLED = 'NDC_SET_UP_ENABLED';
const CRM_ZULTYS_ENABLED = 'CRM_ZULTYS_ENABLED';
const ADVANCED_APPOINTMENT_ENABLED = 'ADVANCED_APPOINTMENT_ENABLED';
const ML_BASED_LEAD_CONTACT_LINKING_ENABLED = 'ML_BASED_LEAD_CONTACT_LINKING_ENABLED';
const COMM_SETUP_DELETE_DOMAIN_DISABLED = 'COMM_SETUP_DELETE_DOMAIN_DISABLED';
const SINGLE_NUMBER_GTC_ENABLED = 'SINGLE_NUMBER_GTC_ENABLED';
const COV_REFERENCING_ENABLED = 'COV_REFERENCING_ENABLED';
const CRM_MULTI_OEM_SWITCH_ENABLED = 'CRM_MULTI_OEM_SWITCH_ENABLED';
const AI_COMMUNICATIONS_ENABLED = 'AI_COMMUNICATIONS_ENABLED';
const CRM_CUSTOMER_LEVEL_VALUE_ENABLED = 'CRM_CUSTOMER_LEVEL_VALUE_ENABLED';
const LEAD_DEAL_SYNC_V2 = 'LEAD_DEAL_SYNC_V2';
const CRM_BUYER_COBUYER_SWAP_ENABLED = 'CRM_BUYER_COBUYER_SWAP_ENABLED';
const LEAD_INFO_V2_ENABLED = 'LEAD_INFO_V2_ENABLED';

// ---- Template Builder ---- //
const TEMPLATE_STORE_ENABLED = 'TEMPLATE_STORE_ENABLED';
const TEKION_BRANDING_DISABLED = 'TEKION_BRANDING_DISABLED';

// --- Service template builder --- //
const COMMUNICATION_TEMPLATE_BUILDER_ENABLED = 'COMMUNICATION_TEMPLATE_BUILDER_ENABLED';

// --- Employee Hours --- //
const CLOCKINV3_ENABLED = 'CLOCKINV3_ENABLED';
const EMPLOYEE_OT_CONFIG_STATE_LEVEL = 'EMPLOYEE_OT_CONFIG_STATE_LEVEL'; // moving OtConfiguration from dealer master to dealer State level
const PRODUCT_FEEDBACK_DASHBOARD_ENABLED = 'PRODUCT_FEEDBACK_DASHBOARD_ENABLED';
const NPS_DASHBOARD_ENABLED = 'NPS_DASHBOARD_ENABLED';
const NPS_SETUP_ENABLED = 'NPS_SETUP_ENABLED';

// ---- CORE ---- //
const VEHICLE_RECOMMENDATION_ENABLED = 'BUDGET_BASED_RECOMMENDATION';
const PERMISSION_ROLE_APPROVAL = 'PERMISSION_ROLE_APPROVAL';
const CREATE_CUSTOM_ROLE_DISABLED = 'CREATE_CUSTOM_ROLE_DISABLED';
const CONFIGURE_APPROVERS_DISABLED = 'CONFIGURE_APPROVERS_DISABLED';
const CENTRALISED_ACCOUNTING = 'CENTRALISED_ACCOUNTING'; // Property to determine whether he is centralised workspace parent dealer or not
const ACCESS_SETTINGS_ENABLED = 'ACCESS_SETTINGS_ENABLED';
const ACCESS_SETTINGS_V2_ENABLED = 'ACCESS_SETTINGS_V2_ENABLED';
const CMS_FIELD_SEARCH_ENABLED = 'CMS_FIELD_SEARCH_ENABLED'; // Property to show Customer Management searchable fields
const WORKFLOW_ENGINE_ENABLED_FOR_DEALER = 'WORKFLOW_ENGINE_ENABLED_FOR_DEALER'; // Property to enable workflow-engine
const PROCESS_AUTOMATION_ENABLED = 'PROCESS_AUTOMATION_ENABLED'; // Property to enable process automation
const RULEENGINE_ENABLED_FOR_SERVICE = 'RULEENGINE_ENABLED_FOR_SERVICE';
const ENABLE_TASK_MANAGER_TAB = 'ENABLE_TASK_MANAGER_TAB'; // Property to enbale process-automation for deal module
const GLBA_COMPLIANCE = 'GLBA_COMPLIANCE';
const NEW_CMS_GLOBAL_SEARCH_ENABLED = 'NEW_CMS_GLOBAL_SEARCH_ENABLED';
const GLOBAL_SEARCH_WITH_TAGS_ENABLED = 'GLOBAL_SEARCH_WITH_TAGS_ENABLED';
const GLOBAL_SEARCH_METRICS_ENABLED = 'GLOBAL_SEARCH_METRICS_ENABLED';
const CMS_DUPLICATE_PREVENTION_ENABLED = 'CMS_DUPLICATE_PREVENTION_ENABLED';
const CMS_NEW_CUSTOMER_LINKING_ENABLED = 'CMS_NEW_CUSTOMER_LINKING_ENABLED';
const APPROVAL_FLOW_ENABLED = 'APPROVAL_FLOW_ENABLED';
const IDLE_TIMEOUT_ENABLED = 'IDLE_TIMEOUT_ENABLED';
const OPTIN_OPTOUT_SETTINGS_ENABLED = 'CENTRAL_COMMUNICATION_OPTIN_PREFERENCES_ENABLED';
const CAPENCY_VERIFICATION_REQUIRED = 'CAPENCY_VERIFICATION_REQUIRED';
const VENDOR_AUDIT_ENABLED = 'VENDOR_AUDIT_ENABLED';
const CMS_KEEP_MERGE_INFO_OUTSIDE_CUSTOMER = 'CMS_KEEP_MERGE_INFO_OUTSIDE_CUSTOMER';
const KEEP_VEHICLE_INFO_OUTSIDE_CUSTOMER_ENABLED = 'CMS_KEEP_VEHICLE_INFO_OUTSIDE_CUSTOMER';
const DRS_REPORTING_ENABLED = 'DRS_REPORTING_ENABLED';
const SYSTEM_ALERTS_ENABLED = 'SYSTEM_ALERTS_ENABLED';
const INCHCAPE_VENDOR_MANAGEMENT_FIELDS_ENABLED = 'INCHCAPE_VENDOR_MANAGEMENT_FIELDS_ENABLED';
const CORE_FOUNDATION_ENABLED = 'CORE_FOUNDATION_ENABLED';
const EMPLOYEE_SSN_VALIDATION_DISABLED = 'EMPLOYEE_SSN_VALIDATION_DISABLED';
const GET_ADDRESS_DOT_IO_ENABLED = 'GET_ADDRESS_DOT_IO_ENABLED';
const AEC_USER_SETUP_ENABLED = 'AEC_USER_SETUP_ENABLED';
const CMS_RELATIONSHIP_MANAGEMENT_ENABLED = 'CMS_RELATIONSHIP_MANAGEMENT_ENABLED';
const CMS_CONFIGURABLE_PAYMENT_MODES_ENABLED = 'CMS_CONFIGURABLE_PAYMENT_MODES_ENABLED';
const CMS_SHOW_ADDRESS_FIELDS_IN_MERGE = 'CMS_SHOW_ADDRESS_FIELDS_IN_MERGE';
const CMS_ENTERPRISE_NOTES_ENABLED = 'CMS_ENTERPRISE_NOTES_ENABLED';
const WALKME_SCRIPT_IDENTIFIER = 'WALKME_SCRIPT_IDENTIFIER';

// ---- Accounting ---- //
const IS_BANK_ADMIN_ENABLED = 'IS_BANK_ADMIN_ENABLED';
const ACCOUNTING_FORM8300 = 'ACCOUNTING_FORM8300';
const ACCOUNTING_FORM1099 = 'ACCOUNTING_FORM1099';
const ACCOUNTING_TAX_CODES = 'ACCOUNTING_TAX_CODES';
const ACCOUNTING_SALES_TAX_REPORT = 'ACCOUNTING_SALES_TAX_REPORT';
const ACCOUNTING_VI_USE_TAX_REGIME = 'ACCOUNTING_VI_USE_TAX_REGIME';
const ACCOUNTING_COUNT_LOGIC_TYPE = 'ACCOUNTING_COUNT_LOGIC_TYPE';
const ACCOUNTING_NEW_PDF_FRAMEWORK_ENABLED = 'ACCOUNTING_NEW_PDF_FRAMEWORK_ENABLED';
const ACCOUNTING_MULTI_CONTROL_ENABLED = 'ACCOUNTING_MULTI_CONTROL_ENABLED';
const ACCOUNTING_PRIOR_PERIOD_ENABLED = 'ACCOUNTING_PRIOR_PERIOD_ENABLED';
const ACCOUNTING_AP_OTHER_PAYABLES = 'ACCOUNTING_AP_OTHER_PAYABLES';
const ACCOUNTING_FORM1099_ELECTRONIC_FILING = 'ACCOUNTING_FORM1099_ELECTRONIC_FILING';
const OCR_INVOICING_ENABLED = 'OCR_INVOICING_ENABLED';
const ACCOUNTING_AUTO_POSTING_RBNI = 'ACCOUNTING_AUTO_POSTING_RBNI';
const ACCOUNTING_SOLUTIOS_ENABLED = 'ACCOUNTING_SOLUTIOS_ENABLED';
const ACCOUNTING_JOURNAL_ENTRY_VIRTUALIZATION_ENABLED = 'ACCOUNTING_JOURNAL_ENTRY_VIRTUALIZATION_ENABLED';
const ACCOUNTING_SUB_APPLICATION = 'ACCOUNTING_SUB_APPLICATION';
const BULK_SUPPORT_FOR_INVOICE_PDF_AR_STATEMENT = 'BULK_SUPPORT_FOR_INVOICE_PDF_AR_STATEMENT';
const CUSTOMER_STATEMENT_GENERATION_THROUGH_BRS = 'CUSTOMER_STATEMENT_GENERATION_THROUGH_BRS';
const ACCOUNTING_INCHCAPE_DEALER = 'ACCOUNTING_INCHCAPE_DEALER';
const ACCOUNTING_INTER_PREFIX = 'ACCOUNTING_INTER_PREFIX';

// ---- DESKING ---- //
const SUMBIT_ECONTRACT = 'E_CONTRACTING';
const ZERO_CONTACT_SALES = 'ZERO_CONTACT_SALES';
const NEW_ZCS_ENABLED = 'NEW_ZCS_ENABLED';
const SALES_DRS_FNIMENU = 'SALES_DRS_FNI_MENU';
const SALES_DRS_EDOCS = 'SALES_DRS_E_DOCS';
const DEALS_FNIMENU = 'F_AND_I_MENU';
const FNI_AUTO_UPDATE_ENABLED = 'FNI_AUTO_UPDATE_ENABLED';
const CONCIERGE_FNI_ENABLED = 'CONCIERGE_FNI_ENABLED';
const NEW_DEAL_CREATE_API_ENABLED = 'NEW_DESKING_API_ENABLED';
const MULTI_VEHICLE_DESKING = 'MULTI_VEHICLE_DESKING';
const ACTIVATE_GHOST_PRODUCT = 'ACTIVATE_GHOST_PRODUCT';
const SHOW_ALL_LENDER_PROGRAMS = 'SHOW_ALL_LENDER_PROGRAMS';
const E_SIGN_ENABLE_FOR_R1 = 'E_SIGN_ENABLE_FOR_R1';
const E_SIGN_CAPABILITY = 'E_SIGN_CAPABILITY';
const ENABLE_ENHANCED_DOCUMENTS_TAB = 'ENABLE_ENHANCED_DOCUMENTS_TAB';
const FNI_SPP_LENDER = 'FNI_SPP_LENDER';
const IS_PNI_REGISTERED = 'IS_PNI_REGISTERED';
const DEALS_FOUNDATION_ENABLED = 'DEALS_FOUNDATION_ENABLED';
const COMPLETION_CERTIFICATE_ENABLED = 'COMPLETION_CERTIFICATE_ENABLED';
const VIN_LOOKUP_DISABLED = 'VIN_LOOKUP_DISABLED';
const PLATFORM_AUDIT_LOGS_FOR_SALES = 'PLATFORM_AUDIT_LOGS_FOR_SALES';
const IS_AUTO_ADJUST_DEAL_POSTINGS_ENABLED = 'IS_AUTO_ADJUST_DEAL_POSTINGS_ENABLED';
const DOCUMENTS_TAB_PERFORMANCE_IMPROVEMENTS = 'DOCUMENTS_TAB_PERFORMANCE_IMPROVEMENTS';
const DIGITAL_SIGNING_PLATFORM = 'DIGITAL_SIGNING_PLATFORM';
const IS_HONDA_PROGRAM = 'IS_HONDA_PROGRAM';
const IS_AEC_PROGRAM = 'IS_AEC_PROGRAM';
const NEW_TRADEIN_ENABLED = 'NEW_TRADEIN_ENABLED';
const DEALER_STATUS_USAGE_ENABLED = 'DEALER_STATUS_USAGE_ENABLED';
const REGISTERATION_NO_AND_ZIP_CODE_SEARCH_ENABLED = 'REGISTERATION_NO_AND_ZIP_CODE_SEARCH_ENABLED';
const AEC_PLATFROM_DEAL_PUSH_ENABLED = 'AEC_PLATFROM_DEAL_PUSH_ENABLED';
const ENABLE_DOCS_VISIBILITY_ACROSS_MODULES = 'ENABLE_DOCS_VISIBILITY_ACROSS_MODULES';
const ENHANCED_VI_DOCS = 'ENHANCED_VI_DOCS';

// --- DSE Standalone Estimates --- //
const DSE_VIRTUAL_CLIPBOARD_ENABLED = 'DSE_VIRTUAL_CLIPBOARD_ENABLED';
const TOYOTA_TST_INTEGRATION_ENABLED = 'TOYOTA_TST_INTEGRATION_ENABLED';

// ---- Offerings ---- //
const DSE_STANDALONE = 'DSE_STANDALONE';
const ARC_ENABLED = 'ARC';

// --- Leaderboard --- //
const LEADERBOARD_ENABLED = 'LEADERBOARD_ENABLED';

// ---- New teams setup ---- //
const NEW_TEAMS_SETUP_ENABLED = 'NEW_TEAMS_SETUP_ENABLED';

// --- PDF Configurator --- //
const PDF_CONFIGURATOR_UI_ENABLED = 'PDF_CONFIGURATOR_UI_ENABLED';
const PDF_CONFIGURATOR_ENABLED = 'PDF_CONFIGURATOR_ENABLED';
const CHECKIN_PDF_UNIFIED_TNC_ENABLED = 'CHECKIN_PDF_UNIFIED_TNC_ENABLED';
const APPOINTMENT_PDF_CONFIGURATOR_ENABLED = 'APPOINTMENT_PDF_CONFIGURATOR_ENABLED';

// ---- Payroll Service ---- //
const TEKION_PAYROLL_ENABLED = 'TEKION_PAYROLL_ENABLED';
const EMPLOYEE_HOURS_APP_DISABLED = 'EMPLOYEE_HOURS_APP_DISABLED';
const CHECK_HQ_ENABLED = 'CHECK_HQ_ENABLED';

// ------- DRP USER ------- //
const DRP_ENABLED = 'DRP_ENABLED';

// --- Multi Oem Site Switch --- //
const MULTI_OEM_SWITCH_ENABLED = 'MULTI_OEM_SWITCH_ENABLED';
const ACCOUNTING_SITE_OVERRIDE_ENABLED = 'ACCOUNTING_SITE_OVERRIDE_ENABLED';

// ------- Service Menu Presented Enabled ------- //
const GM_SERVICE_MENU_PRESENTED_ENABLED = 'GM_SERVICE_MENU_PRESENTED_ENABLED';

// --- Send employee time punch to payroll switch --- //
const THIRD_PARTY_PAYROLL_ENABLED = 'THIRD_PARTY_PAYROLL_ENABLED';

// --- Oem User Management Enabled --- //
const OEM_USER_FCA_ENABLED = 'OEM_USER_FCA_ENABLED';

// --- Tekion Calc Engine Enabled --- //
const CALC_ENGINE_ENABLED = 'CALC_ENGINE_ENABLED';
const DEAL_STATUS_CONFIG_ENABLED = 'DEAL_STATUS_CONFIG_ENABLED';
const DEAL_STATUS_RULES_CONFIG_ENABLED = 'DEAL_STATUS_RULES_CONFIG_ENABLED';
const GALAXY_DEAL_COMPARE_ENABLED = 'GALAXY_DEAL_COMPARE_ENABLED';
const VEHICLE_CONFIGURATOR_ENABLED = 'VEHICLE_CONFIGURATOR_ENABLED';
const GALAXY_ENABLED = 'GALAXY_ENABLED';

// --- Scan Managment --- //
const ARC_SCANNING = 'ARC_SCANNING';
const SCAN_MANAGEMENT_V2_ENABLED = 'SCAN_MANAGEMENT_V2_ENABLED';
const SALES_SCAN_MANAGEMENT_V2_ENABLED = 'SALES_SCAN_MANAGEMENT_V2_ENABLED';

// --- Printer Setup --- //
const PRINTER_SETUP_V2_ENABLED = 'PRINTER_SETUP_V2_ENABLED';

const SEND_REAL_TIME_PUNCHES_ENABLED = 'SEND_REAL_TIME_PUNCHES_ENABLED';
const SESSION_RECORDING_DISABLED = 'SESSION_RECORDING_DISABLED';

// ------- Standard State code Mandatory Enabled ------- //
const STANDARD_STATE_CODE_ENABLED = 'STANDARD_STATE_CODE_ENABLED';

// ------- Support Portal Enabled ------- //
const SUPPORT_PORTAL_DISABLED = 'SUPPORT_PORTAL_DISABLED';

// ------- Asset Tracking Enabled ------- //
const VEHICLE_TRACKING_ENABLED = 'VEHICLE_TRACKING_ENABLED';

// ------- Tekion Store Enabled ------- //
const TEKION_STORE_ENABLED = 'TEKION_STORE_ENABLED';

// ------- Connected Displays Enabled ------- //
const CONNECTED_DISPLAYS_ENABLED = 'CONNECTED_DISPLAYS_ENABLED';

// ------ Express Mode ------------- //
const EXPRESS_MODE_ENABLED = 'EXPRESS_MODE_ENABLED';

// ------ RO Approval System ------------- //
const RO_APPROVAL_SYSTEM_ENABLED = 'RO_APPROVAL_SYSTEM_ENABLED';

// ------ SPG Override------------- //
const SPG_SINGLE_PRICE_MODE_ENABLED = 'SPG_SINGLE_PRICE_MODE_ENABLED';
// ------ New Tax Regime Enabled ------------- //
const NEW_TAX_REGIME_ENABLED = 'NEW_TAX_REGIME_ENABLED';

const INTERNAL_APPS_HIDDEN = 'INTERNAL_APPS_HIDDEN';

// ------ Scheduling settings v2 ------------- //
const SCHEDULING_SETTINGS_CAPACITY_ENABLED = 'SCHEDULING_SETTINGS_CAPACITY_ENABLED';

const NEW_APP_SKELETON_ENABLED = 'NEW_APP_SKELETON_ENABLED'; // A flag used to decide between old and new app skeleton components

// --------- ANALYTICS ----------- //
const ANALYTICS_DEALER = 'ANALYTICS_DEALER';
// A flag indicates is forecast data is corrupted for dealer
const ANALYTICS_IS_SERVICE_FORECAST_DATA_CORRUPTED = 'ANALYTICS_IS_SERVICE_FORECAST_DATA_CORRUPTED';
// Flag indicates whether enterprise dashboard is enabled for the user or not
const ANALYTICS_WEB_ENTERPRISE_DASHBOARD_ENABLED = 'ANALYTICS_WEB_ENTERPRISE_DASHBOARD_ENABLED';

// Flag to create or view clickstream projects
const ANALYTICS_CLICKSTREAM_ENABLED = 'ANALYTICS_CLICKSTREAM_ENABLED';

// Flag to check internal dealer
const ANALYTICS_INTERNAL_DEALER = 'ANALYTICS_INTERNAL_DEALER';

// ------- Analytics Fs Excel Mapper ------- //
const ANALYTICS_FS_EXCEL_MAPPING_ENABLED = 'ANALYTICS_FS_EXCEL_MAPPING_ENABLED';
const ANALYTICS_DASHBOARD_BUILDER_ENABLED = 'ANALYTICS_DASHBOARD_BUILDER_ENABLED';

// flag indicates to show Vehicle Inventory Reconditioning
const ANALYTICS_INVENTORY_RECONDITIONING_ENABLED = 'ANALYTICS_INVENTORY_RECONDITIONING_ENABLED';

// till which month forecast data is corrupted for dealer
const ANALYTICS_CORRUPTED_SERVICE_FORECAST_DATA_TIMESTAMP = 'ANALYTICS_CORRUPTED_SERVICE_FORECAST_DATA_TIMESTAMP';

// ------- Dealer Statistics ------- //
const ANALYTICS_DEALER_STATS_ENABLED = 'ANALYTICS_DEALER_STATS_ENABLED';

// ------- Usage Report ------- //
const ANALYTICS_USAGE_REPORT_ENABLED = 'ANALYTICS_USAGE_REPORT_ENABLED';

// ------- Sales Consultant Split ------- //
const ANALYTICS_SALES_CONSULTANT_ENABLED = 'ANALYTICS_SALES_CONSULTANT_ENABLED';

// ------- Enable New Sales Dashboard ------ //
const ANALYTICS_SALES_REVAMP_DASHBOARD_ENABLED = 'ANALYTICS_SALES_REVAMP_DASHBOARD_ENABLED';

// ------- Enable Service V2 Dashboard ------ //
const SERVICE_DASHBOARD_V2 = 'SERVICE_DASHBOARD_V2';

// ------- Feature Management ------- //
const INTERNAL_FEATURE_MANAGEMENT_ENABLED = 'INTERNAL_FEATURE_MANAGEMENT_ENABLED';

// ------ Lyft Flex Rides ------------- //
const FLEX_RIDE_ENABLED = 'FLEX_RIDE_ENABLED';

// ------ Refund as AP Check ------------- //
const AP_CHECK_REFUND_ENABLED = 'AP_CHECK_REFUND_ENABLED';

// ------ UV Eye ------------- //
const UV_EYE_INTEGRATION_ENABLED = 'UV_EYE_INTEGRATION_ENABLED';

// ------------- TELEMATICS OBD --------------- //
const TELEMATIC_OBD_ENABLED = 'TELEMATIC_OBD_ENABLED';

const VARIABLE_LABOR_PAY_RATE_ADJUSTMENTS_ENABLED = 'VARIABLE_LABOR_PAY_RATE_ADJUSTMENTS_ENABLED';

// ------ WIP Reports V2 ------ //
const WIP_REPORT_V2_ENABLED = 'WIP_REPORT_V2_ENABLED';

const CUSTOMER_AUDIT_ENABLED = 'CUSTOMER_AUDIT_ENABLED';

// -------- Double Optin Enable ------- //
const DOUBLE_OPT_IN_SETUP_ENABLED = 'DOUBLE_OPT_IN_SETUP_ENABLED';

// -------- GM Rewards ------- //
const GM_REWARDS_V2_ENABLED = 'GM_REWARDS_V2_ENABLED';

// -------- Customer 360 ------- //
const CUSTOMER_VALUE_ENABLED = 'CUSTOMER_VALUE_ENABLED';
const HIDE_CUSTOMER_360_OTHER_TABS_CMS_ONLY = 'HIDE_CUSTOMER_360_OTHER_TABS_CMS_ONLY';

// -------- Form Configurator ------- //
const NEW_DEAL_PREVIEW_WORKFLOW = 'NEW_DEAL_PREVIEW_WORKFLOW';

const FIXEDOPS_V2_ENABLED = 'FIXEDOPS_V2_ENABLED';

// -------- Car Bravo ----------- //
const CAR_BRAVO_ENABLED = 'CAR_BRAVO_ENABLED';

const CMS_MERGE_V2 = 'CMS_MERGE_V2';

// ------ Vehicle Diagnostic Report ------//
const VEHICLE_DIAGNOSTIC_REPORT_PDF_ENABLED = 'VEHICLE_DIAGNOSTIC_REPORT_PDF_ENABLED';
// --------- Override Tax Percentage Enable ------- //
const PARTS_SALES_ORDER_TAX_OVERRIDE_ENABLED = 'PARTS_SALES_ORDER_TAX_OVERRIDE_ENABLED';

// ---- CW VIN ---- //
const MULTIPLE_VEHICLE_TYPE_ENABLED = 'MULTIPLE_VEHICLE_TYPE_ENABLED';

// -------- MULTIPLE_RO_SERVICE_ADVISOR_ENABLED --------------------------------
const MULTIPLE_RO_SERVICE_ADVISOR_ENABLED = 'MULTIPLE_RO_SERVICE_ADVISOR_ENABLED';

// -------- SERVICE_DATE_CONFIGURATOR_ENABLED --------------------------------
const SERVICE_DATE_CONFIGURATOR_ENABLED = 'SERVICE_DATE_CONFIGURATOR_ENABLED';

// ------ Multilingual ------//
export const MULTILINGUAL_ENABLED = 'MULTILINGUAL_ENABLED';
const MULTILINGUAL_TRACKER_ENABLED = 'MULTILINGUAL_TRACKER_ENABLED';

// --------- Fee Setup -------------- //
const CAMPING_WORLD_ENABLED = 'CAMPING_WORLD_ENABLED';
const FEE_GRID_CONFIG_ENABLED = 'FEE_GRID_CONFIG_ENABLED';

// ------- Service type override ------- //
export const SERVICE_TYPE_OVERRIDE_ENABLED = 'SERVICE_TYPE_OVERRIDE_ENABLED';
const FORCED_PART_SELECTION_ENABLED = 'FORCED_PART_SELECTION_ENABLED';

// ------- Consumer Scheduling v2 Configuration ------- //
const CONSUMER_SCHEDULING_V2_ENABLED = 'CONSUMER_SCHEDULING_V2_ENABLED';

// ------- employee hours settings ------- //
export const EMPLOYEE_HOURS_OT_DOT_V3 = 'EMPLOYEE_HOURS_OT_DOT_V3';

// ------- Service aggregation scheduling settings enabled ------- //
export const SERVICE_AGGREGATION_SCHEDULING_SETTINGS_ENABLED = 'SERVICE_AGGREGATION_SCHEDULING_SETTINGS_ENABLED';
// ------ OEM Sync ------------- //
const OEM_RETRIEVAL_ENABLED = 'OEM_RETRIEVAL_ENABLED';

const SERVICE_MAINTENANCE_ENABLED = 'SERVICE_MAINTENANCE_ENABLED';

const VEHICLE_MODEL_AND_BODY_TYPE_FILTER_ENABLED = 'VEHICLE_MODEL_AND_BODY_TYPE_FILTER_ENABLED';
const CTC_DISABLED = 'CTC_DISABLED';

const EXPRESS_INSPECTION_ONLY = 'EXPRESS_INSPECTION_ONLY';

const PROCESS_CLAIM_FOR_WARRANTY_SPLIT = 'PROCESS_CLAIM_FOR_WARRANTY_SPLIT';

const PROCESS_CLAIM_ON_JOB_COMPLETION = 'PROCESS_CLAIM_ON_JOB_COMPLETION';

// ------ RO Job Scheduling Feature ------ //
const SCHEDULING_RO_JOBS_ENABLED = 'SCHEDULING_RO_JOBS_ENABLED';

// ------ RO List Expanded View ------ //
const RO_LIST_EXPANDED_VIEW_ENABLED = 'RO_LIST_EXPANDED_VIEW_ENABLED';

const OPEN_RO_MIGRATION_ENABLED = 'OPEN_RO_MIGRATION_ENABLED';
// ------ Fulfilment SOR Details new PDF ------ //
const PARTS_SOR_DETAILS_PDF_NEW_ENABLED = 'PARTS_SOR_DETAILS_PDF_NEW_ENABLED';

const EXTERNAL_COMPANY_PDF_ENABLED = 'EXTERNAL_COMPANY_PDF_ENABLED';
const CUSTOMER_EAVS2_VALIDATION_ENABLED = 'CUSTOMER_EAVS2_VALIDATION_ENABLED';

// ------ RO Warranty Posting -------- //
const RRG_WARRANTY_POSTING_ENABLED = 'RRG_WARRANTY_POSTING_ENABLED';

// ------ RO Sync -------- //
const REPUSH_RO_ENABLED = 'REPUSH_RO_ENABLED';

// ------ SERVICE CRM ------------- //
const SERVICE_CRM_ENABLED = 'SERVICE_CRM_ENABLED';

// ------ INTERNAL APPS > COMMUNICATION_SETTINGS -------------
const INTERNAL_COMMUNICATION_SETTINGS_ENABLED = 'INTERNAL_COMMUNICATION_SETTINGS_ENABLED';

// ----- MPI - Multiple Opcodes Enhancement ------ //
export const MPI_MULTI_CODE_ENABLED = 'MPI_MULTI_CODE_ENABLED';

// ----- CUSTOM RO NUMBER ----------- //
export const CUSTOM_RO_NUMBER_ENABLED = 'CUSTOM_RO_NUMBER_ENABLED';

// ------ RO STATUS ------------- //
const MULTI_TECHNICIAN_AUTO_FLAGGING_ENABLED = 'MULTI_TECH_AUTO_FLAGGING';

const DEROGATION_MANAGEMENT_ENABLED = 'DEROGATION_MANAGEMENT_ENABLED';
const GUARANTEE_MANAGEMENT_ENABLED = 'GUARANTEE_MANAGEMENT_ENABLED';
const CLAIM_WORK_POOL_ENABLED = 'CLAIM_WORK_POOL_ENABLED';
const CORE_NPS_ENABLED = 'CORE_NPS_ENABLED';

const ENHANCE_VIN_DECODING = 'ENHANCE_VIN_DECODING';

const CRM_CUSTOM_WIDGETS_ENABLED = 'CRM_CUSTOM_WIDGETS_ENABLED';
const CRM_STANDALONE_ENABLED = 'CRM_STANDALONE_ENABLED';
const CMS_VEHICLE_OWNERSHIP_ENABLED = 'CMS_VEHICLE_OWNERSHIP_ENABLED';
const CUSTOMER_360_COUPONS_ENABLED = 'CUSTOMER_360_COUPONS_ENABLED';

const CHECKIN_CONFIGURATOR_ENABLED = 'CHECKIN_CONFIGURATOR_ENABLED';
const CRM_NEW_GTC_SETUP_ENABLED = 'CRM_NEW_GTC_SETUP_ENABLED';
const CMS_AUDIT_REPORT_ENABLED = 'CMS_AUDIT_REPORT_ENABLED';

const GDPR_ENABLED = 'GDPR_ENABLED';
const CPRA_ENABLED = 'CPRA_ENABLED';
const AUTO_ADD_OPCODE_RULES_ENABLED = 'AUTO_ADD_OPCODE_RULES_ENABLED';

// --------------  DFE -------------------//

const DFE_ENABLED = 'DFE_ENABLED';
const NEW_DFE_ENABLED = 'NEW_DFE_ENABLED';

// --------------   Health Check ---------------- //
const HEALTH_CHECK_ENABLED = 'HEALTH_CHECK_ENABLED';

// -------------- Appointments ---------------- //
const APPOINTMENT_PARTS_FLOW_ENABLED = 'APPOINTMENT_PARTS_FLOW_ENABLED';
const SERVICE_APPOINTMENT_PROMISE_TIME_ENABLED = 'SERVICE_APPOINTMENT_PROMISE_TIME_ENABLED';
const SERVICE_APPOINTMENT_QUOTE_SEARCH_ENABLED = 'SERVICE_APPOINTMENT_QUOTE_SEARCH_ENABLED';

const SERVICE_WORKLOAD_PLANNER_ENABLED = 'SERVICE_WORKLOAD_PLANNER_ENABLED';
const SERVICE_MULTIPLE_TRANSPORTATION_FLOW_ENABLED = 'SERVICE_MULTIPLE_TRANSPORTATION_FLOW_ENABLED';

const EUROPEAN_ROLES_AND_PERMISSIONS_ENABLED = 'EUROPEAN_ROLES_AND_PERMISSIONS_ENABLED';
const EUROPEAN_FIELD_SETUP_ENABLED = 'EUROPEAN_FIELD_SETUP_ENABLED';
const FIELD_SETUP_CODE_COLUMN_DISABLED = 'FIELD_SETUP_CODE_COLUMN_DISABLED';

const MIGRATION_DASHBOARD_ENABLED = 'MIGRATION_DASHBOARD_ENABLED';
const EUROPEAN_EMPLOYEE_ONBOARDING_ENABLED = 'EUROPEAN_EMPLOYEE_ONBOARDING_ENABLED';
const OPECODE_INTEGRATION_ENABLED = 'OPECODE_INTEGRATION_ENABLED';

const E_SIGN_ENABLED = 'E-SIGNING_ENABLED';

// -------------- Employee Objectives ---------------- //

const EUROPEAN_ANNUAL_SALES_OBJECTIVE_ENABLED = 'EUROPEAN_ANNUAL_SALES_OBJECTIVE_ENABLED';

// --------------   Profit Loss View ---------------- //
const PROFIT_LOSS_VIEW_ENABLED = 'PROFIT_LOSS_VIEW_ENABLED';

// -------------- GM SAVI ----------- //
const SAVI_GM_ENABLED = 'SAVI_GM_ENABLED';

// -------------- Hunter Lane ----------- //
const HUNTER_LANE_ENABLED = 'HUNTER_LANE_ENABLED';

// ------------ RO_JOB_TAGS_V2 ------- //
const RO_JOB_TAGS_V2_ENABLED = 'RO_JOB_TAGS_V2_ENABLED';

// ------------ RO_UNASSIGNED_SKILL_TAGS ------- //
const UNASSIGNED_SKILL_TAGS_FILTER_ENABLED = 'UNASSIGNED_SKILL_TAGS_FILTER_ENABLED';

// ------------ RO_MILEAGE_IN_FILTER ------- //
const RO_MILEAGE_IN_FILTER_DISABLED = 'RO_MILEAGE_IN_FILTER_DISABLED';

const AURORA_ENABLED = 'aurora_enabled';
// --------------   Tax Codes ---------------- //
const NEW_TAX_SERVICE_ENABLED = 'NEW_TAX_SERVICE_ENABLED';
const USE_NEW_TAX_CODES_SETUP = 'USE_NEW_TAX_CODES_SETUP';
const PRODUCT_GROUP_TAX_CODE_ENABLED = 'PRODUCT_GROUP_TAX_CODE_ENABLED';

// -------------- RO Details Customer Details Update ---------------- //
const SHOULD_UPDATE_CUSTOMER_INFO_IN_RO_DETAILS = 'SHOULD_UPDATE_CUSTOMER_INFO_IN_RO_DETAILS';

// ------------- Amount Rounf off -------- //
const AMOUNT_ROUND_OFF_ENABLED = 'AMOUNT_ROUND_OFF_ENABLED';

// --------------   List view defaults configuration ---------------- //

const LIST_VIEW_DEFAULTS_CONFIGURATION_ENABLED = 'LIST_VIEW_DEFAULTS_CONFIGURATION_ENABLED';

const ROLES_AND_RIGHTS_ENABLED = 'ROLES_AND_RIGHTS_ENABLED';

// ------- RO List Async Media Upload --------- //
const MOBILE_ASYNC_UPLOAD_ENABLED = 'MOBILE_ASYNC_UPLOAD_ENABLED';

// --------------   Polly Restricted States ---------------- //

const POLLY_INTEGRATION_ENABLED = 'POLLY_INTEGRATION_ENABLED';

const VEHICLE_SYNDICATION_ENABLED = 'VEHICLE_SYNDICATION_ENABLED';
const VEHICLE_PRECONFIGURED_OPTIONS_ENABLED = 'VEHICLE_PRECONFIGURED_OPTIONS_ENABLED';
const FEED_SYNDICATION_ENABLED = 'FEED_SYNDICATION_ENABLED';

const AUTO_IPACKET_ENABLED = 'AUTO_IPACKET_ENABLED';

// -------------- Enabling RO Bulk Action Screen View ---------------- //

// --------------   Vehicle Group   ---------------- //

const VEHICLE_GROUP_ENABLED = 'VEHICLE_GROUP_ENABLED';
const EUR_CMS_ENABLED = 'EUR_CMS_ENABLED';

const CONNECTED_CAR_DEVICE_ENABLED = 'CONNECTED_CAR_DEVICE_ENABLED';

// -------------- RO Settings KPI Total Sales ---------------- //
const RO_LIST_KPI_SALES_V3 = 'RO_LIST_KPI_SALES_V3';

// -------------- User Mimicking Access Setup for lower envs ---------------- //
const MIMICKING_ACCESS_SETUP_LE_ENABLED = 'MIMICKING_ACCESS_SETUP_LE_ENABLED';

// -------------- RO Details Aggregation ---------------- //
const RO_DETAILS_AGGREGATION_ENABLED = 'RO_DETAILS_AGGREGATION_ENABLED';

// -------------- Multiple OEM Opcode -------------- //
const MULTIPLE_OEM_OPCODE_ENABLED = 'MULTIPLE_OEM_OPCODE_ENABLED';

const CMS_VIEW_ASSOCIATED_COMPANIES = 'CMS_VIEW_ASSOCIATED_COMPANIES';

// -------------- Invoice Confirmation Screen ---------------- //
const INVOICE_CONFIRMATION_ENABLED = 'INVOICE_CONFIRMATION_ENABLED';
const ALLOW_IF_OFFSET_ACCOUNTS_IN_POSTING = 'ALLOW_IF_OFFSET_ACCOUNTS_IN_POSTING';

// -------------- Report Builder ---------------- //
const DYNAMIC_REPORT_BUILDER_ENABLED = 'DYNAMIC_REPORT_BUILDER_ENABLED';
const INTERNAL_MODULES_AND_DATA_SOURCES_ENABLED = 'INTERNAL_MODULES_AND_DATA_SOURCES_ENABLED';
const VMS_FETCH_VENDOR_CATEGORIES = 'VMS_FETCH_VENDOR_CATEGORIES';

const EMPLOYEE_INFORMATION_NON_MANDATORY_FIELDS_ENABLED = 'EMPLOYEE_INFORMATION_NON_MANDATORY_FIELDS_ENABLED';
const OPCODE_EXCEL_V2_ENABLED = 'OPCODE_EXCEL_V2_ENABLED';

const LABOR_RATE_CONFIG_ENABLED = 'LABOR_RATE_CONFIG_ENABLED';

const USER_EMPLOYEE_CREATION_DISABLED = 'USER_EMPLOYEE_CREATION_DISABLED';

const TRIM_ATTRIBUTE_RESTRICTION_ENABLED = 'TRIM_ATTRIBUTE_RESTRICTION_ENABLED';

const ENABLE_AUTO_CLOCK_OUT_EMPLOYEE_SCHEDULE = 'ENABLE_AUTO_CLOCK_OUT_EMPLOYEE_SCHEDULE';

const ENABLE_AUTO_GENERATED_CLOCK = 'ENABLE_AUTO_GENERATED_CLOCK';

const PARTS_CUSTOMER_DATA_DELETION_ENABLED = 'PARTS_CUSTOMER_DATA_DELETION_ENABLED';

const SERVICE_FOUNDATION_ENABLED = 'SERVICE_FOUNDATION_ENABLED';

// Program-specific properties
const HERCULES_PROGRAM_ENABLED = 'HERCULES_PROGRAM_ENABLED';
const FULFILMENT_PART_AUTO_RESOLVING_ENABLED = 'FULFILMENT_PART_AUTO_RESOLVING_ENABLED';

const DEALER_DETAILS_DEPARTMENT_ADDRESS_DISABLED = 'DEALER_DETAILS_DEPARTMENT_ADDRESS_DISABLED';
const QUOTE_SETUP_ENABLED = 'QUOTE_SETUP_ENABLED';

const COMMERCIAL_SALES_SETUP_ENABLED = 'COMMERCIAL_SALES_SETUP_ENABLED';

// -------------- Program Status ---------------- //
const INCHCAPE_ENABLED = 'INCHCAPE_ENABLED';

// --------- FORM BUILDER ----------- //
const FORM_BUILDER = 'FORM_BUILDER';

// -------------- Coupon Management Auto Add Coupon Criteria To Job View ---------------- //
const AUTO_ADD_COUPON_ENABLED = 'AUTO_ADD_COUPON_ENABLED';

const INCHCAPE_CMS_FIELDS_UPGRADATION_ENABLED = 'INCHCAPE_CMS_FIELDS_UPGRADATION_ENABLED';

// ------------------------------------- Return Part on RO --------------------------------- //
const RETURN_PART_ON_RO_ENABLED = 'RETURN_PART_ON_RO_ENABLED';
const DISABLE_SEND_UPDATED_PARTS_ALWAYS = 'DISABLE_SEND_UPDATED_PARTS_ALWAYS';

const PARTS_THIRD_PANE_BROWSER_ENABLED = 'PARTS_THIRD_PANE_BROWSER_ENABLED';

// ------------------------------------- Enabling FNI V2 --------------------------------- //
const FNIMENU_V2_ENABLED = 'FNIMENU_V2_ENABLED';

const CLOCKED_TIME_DRAWER_ENABLED = 'CLOCKED_TIME_DRAWER_ENABLED';

// ---------- Dealer Onboarding ---------- //
const PRE_ONBOARDING_QUESTIONNAIRE_ENABLED = 'PRE_ONBOARDING_QUESTIONNAIRE_ENABLED';
const CMS_MULTI_ENTITY_CONSENT_MANAGEMENT_ENABLED = 'CMS_MULTI_ENTITY_CONSENT_MANAGEMENT_ENABLED';

const JOB_CLOCK_IN_OUT_REASON_ENABLED = 'JOB_CLOCK_IN_OUT_REASON_ENABLED';
const EMPLOYEE_DELETE_PII_INFO_ENABLED = 'EMPLOYEE_DELETE_PII_INFO_ENABLED';
const CMS_CL_APPROVAL_FLOW_ENABLED = 'CMS_CL_APPROVAL_FLOW_ENABLED';

// ------------------------------------- Paytypes Setup --------------------------------- //
const PAYTYPE_CREATION_LIMIT_ENABLED = 'PAYTYPE_CREATION_LIMIT_ENABLED';
const CMS_VERBIAGE_PREFERENCES_DIFFERENTIATION_ENABLED = 'CMS_VERBIAGE_PREFERENCES_DIFFERENTIATION_ENABLED';

const TEKION_SMART_SUGGEST_PARTS_ENABLED = 'TEKION_SMART_SUGGEST_PARTS_ENABLED';
const CRM_MULTI_DOMAIN_ENABLED = 'CRM_MULTI_DOMAIN_ENABLED';
const VI_ARC_LITE_ENABLED = 'VI_ARC_LITE_ENABLED';

const DEALER_LEVEL_SETUPS_ENABLED = 'DEALER_LEVEL_SETUPS_ENABLED';

const AEC_PROGRAM_ENABLED_APPS = 'AEC_PROGRAM_ENABLED_APPS';

const APPOINTMENT_HOURS_COLUMN_ENABLED = 'APPOINTMENT_HOURS_COLUMN_ENABLED';

const CLOCK_TIME_DISABLED = 'CLOCK_TIME_DISABLED';

const ADD_SALES_DEPARTMENT_AND_EXCEPTIONS_WITH_MAKE_ENABLED = 'ADD_SALES_DEPARTMENT_AND_EXCEPTIONS_WITH_MAKE_ENABLED';
const DEFERRED_SERVICE_REPORT_V2_ENABLED = 'DEFERRED_SERVICE_REPORT_V2_ENABLED';

const TECH_PERFORMANCE_REPORT_V2_ENABLED = 'TECH_PERFORMANCE_REPORT_V2_ENABLED';
const RO_AGE_REPORT_ENABLED = 'RO_AGE_REPORT_ENABLED';

const GMOSS_SCHEDULING_TYPE_ENABLED = 'GMOSS_SCHEDULING_TYPE_ENABLED';

const IS_ORDER_MANAGEMENT_SYSTEM_V2_ENABLED = 'IS_ORDER_MANAGEMENT_SYSTEM_V2_ENABLED';

const INDICATOR_MENU_CONFIG = 'INDICATOR_MENU_CONFIG';

const DEFAULT_ARC_UI_ROUTE_ID = 'DEFAULT_ARC_UI_ROUTE_ID';
const PAYMENT_APPROVAL_ENABLED = 'PAYMENT_APPROVAL_ENABLED';
const ENTERPRISE_V2_ENABLED = 'ENTERPRISE_V2_ENABLED';
const ENTERPRISE_V2_DETAILS = 'ENTERPRISE_V2_DETAILS';
const ENTERPRISE_BU_ENABLED = 'ENTERPRISE_BU_ENABLED';
const APP_SKELETON_THEME = 'APP_SKELETON_THEME';

const PPR_PRINT_ENHANCEMENT_ENABLED = 'PPR_PRINT_ENHANCEMENT_ENABLED';
const HOLD_AUTOMATION_ENABLED = 'HOLD_AUTOMATION_ENABLED';

// Service Menu Setup
const SERVICE_MENU_SETUP_AUDIT_LOGS_ENABLED = 'SERVICE_MENU_SETUP_AUDIT_LOGS_ENABLED';

const E_SIGN_SERVICE_INTEGRATION_ENABLED = 'E_SIGN_SERVICE_INTEGRATION_ENABLED';

const UNIVERSAL_SEARCH_ENABLED = 'UNIVERSAL_SEARCH_ENABLED';

const E_SIGN_PLATFORM_ENABLED = 'E_SIGN_PLATFORM_ENABLED';

// Oem Agent Feature Flags

const ENABLE_BPM_AND_VAT = 'ENABLE_BPM_AND_VAT';
const SHOW_MONTHLY_PAYMENT = 'SHOW_MONTHLY_PAYMENT';
const QUOTE_PRIMARY_ACTION = 'QUOTE_PRIMARY_ACTION';
const MODIFY_QUOTE_LEAD = 'MODIFY_QUOTE_LEAD';
const SHOW_ADDITIONAL_ACCESSORIES_SEPARATELY = 'SHOW_ADDITIONAL_ACCESSORIES_SEPARATELY';
const SHOW_TAX_RATES_DROPDOWN = 'SHOW_TAX_RATES_DROPDOWN';
const QUOTE_CONTRACT_TYPE = 'QUOTE_CONTRACT_TYPE';
const SHOW_UPFITTINGS_FROM_LIBRARY = 'SHOW_UPFITTINGS_FROM_LIBRARY';
const ENABLE_FAVORITES = 'ENABLE_FAVORITES';
const ENABLE_TEMPLATES = 'ENABLE_TEMPLATES';
const ENABLE_CATALOG_REORDER = 'ENABLE_CATALOG_REORDER';
const ENABLE_QUOTE_ASSIGNEE = 'ENABLE_QUOTE_ASSIGNEE';
const ENABLE_QUOTE_CHANGE_ASSIGNEE = 'ENABLE_QUOTE_CHANGE_ASSIGNEE';
const ENABLE_QUOTE_BULK_SELECTION = 'ENABLE_QUOTE_BULK_SELECTION';
const TRADE_IN_TYPE = 'TRADE_IN_TYPE';
const TAXES_AND_FEES_TYPE = 'TAXES_AND_FEES_TYPE';
const QUOTE__SHOW_TRADE_IN_DESC_IN_CONFIG_CARD = 'QUOTE__SHOW_TRADE_IN_DESC_IN_CONFIG_CARD';
const QUOTE__SHOW_ADDITIONAL_INFO_DESC_IN_CONFIG_CARD = 'QUOTE__SHOW_ADDITIONAL_INFO_DESC_IN_CONFIG_CARD';
const QUOTE__SHOW_TRADE_IN = 'QUOTE__SHOW_TRADE_IN';
const NON_CVC_ENABLED = 'NON_CVC_ENABLED';
const ENABLE_EMAIL_CUSTOMIZATION = 'ENABLE_EMAIL_CUSTOMIZATION';
const ENABLE_LEAD_EXPORT = 'ENABLE_LEAD_EXPORT';
const QUOTE__SHOW_DISCLOSURE_TYPE_COLUMN_SERVICE_PLANS = 'QUOTE__SHOW_DISCLOSURE_TYPE_COLUMN_SERVICE_PLANS';
const QUOTE__ENABLE_MARKET_ADJUSTMENT_FOR_ADD_ONS = 'QUOTE__ENABLE_MARKET_ADJUSTMENT_FOR_ADD_ONS';
const QUOTE__ENABLE_VEHICLE_BUILDABILITY_VERIFICATION = 'QUOTE__ENABLE_VEHICLE_BUILDABILITY_VERIFICATION';
const QUOTE__EDIT_VEHICLE_SPECIFICATION = 'QUOTE__EDIT_VEHICLE_SPECIFICATION';
const QUOTE__ENABLE_VSO_INQUIRY = 'QUOTE__ENABLE_VSO_INQUIRY';
const ENABLE_SHIP_THRU = 'ENABLE_SHIP_THRU';
const QUOTE__ENABLE_REMARKS = 'QUOTE__ENABLE_REMARKS';
const QUOTE__INCENTIVE_TYPE = 'QUOTE__INCENTIVE_TYPE';
const QUOTE__ENABLE_SALES_CODE_SELECTOR = 'QUOTE__ENABLE_SALES_CODE_SELECTOR';
const QUOTE__ENABLE_MULTI_LANGUAGE_PDF_DOWNLOAD = 'QUOTE__ENABLE_MULTI_LANGUAGE_PDF_DOWNLOAD';
const QUOTE__ANALYTICS_TAG_ENABLED = 'QUOTE__ANALYTICS_TAG_ENABLED';
const QUOTE__ENABLE_STOCK_AND_RETAIL_ORDER_TYPES = 'QUOTE__ENABLE_STOCK_AND_RETAIL_ORDER_TYPES';

const CLICKSTREAM_FORD_PROJECT_KEY = 'CLICKSTREAM_FORD_PROJECT_KEY';
const CLICKSTREAM_FORD_API_KEY = 'CLICKSTREAM_FORD_API_KEY';

// OMS Feature Flags
const OMS__PLACE_ORDER_TYPE = 'OMS__PLACE_ORDER_TYPE';
const OMS__ENABLE_VEHICLE_BUILDABILITY_VERIFICATION = 'OMS__ENABLE_VEHICLE_BUILDABILITY_VERIFICATION';
const OMS__ENABLE_SALES_CODE_SELECTOR = 'OMS__ENABLE_SALES_CODE_SELECTOR';
const OMS__ANALYTICS_TAG_ENABLED = 'OMS__ANALYTICS_TAG_ENABLED';
const OMS__ENABLE_STOCK_AND_RETAIL_ORDER_TYPES = 'OMS__ENABLE_STOCK_AND_RETAIL_ORDER_TYPES';
const OMS__DROP_SHIP_TYPE = 'OMS__DROP_SHIP_TYPE';
const DIGITAL_ADOPTION_PLATFORM_PROVIDER = 'DIGITAL_ADOPTION_PLATFORM_PROVIDER';
const DIGITAL_ADOPTION_PLATFORM_PROVIDER_PROJECT_KEY = 'DIGITAL_ADOPTION_PLATFORM_PROVIDER_PROJECT_KEY';
const UNIVERSAL_GUEST_PROFILE_ENABLED = 'UNIVERSAL_GUEST_PROFILE_ENABLED';
const BE_PERMISSION_LABELS_ENABLED = 'BE_PERMISSION_LABELS_ENABLED';
const LOGIN_OVERRIDE_PERMISSION = 'LOGIN_OVERRIDE_PERMISSION';
const PART_RETURN_SCANNING_ENABLED = 'PART_RETURN_SCANNING_ENABLED';

const PARTS_SNAP_ON_PRO_OEM_CODE = 'PARTS_SNAP_ON_PRO_OEM_CODE';

const APPROVAL_WORKFLOW_FOR_CREDIT_LIMIT = 'APPROVAL_WORKFLOW_FOR_CREDIT_LIMIT';

const PARTS_SALES_ORDER_APPROVAL_FLOW_ENABLED = 'PARTS_SALES_ORDER_APPROVAL_FLOW_ENABLED';

const REMOVE_USER_ROLE_ASSIGN_EMPLOYEE_VALIDATION = 'REMOVE_USER_ROLE_ASSIGN_EMPLOYEE_VALIDATION';

const FEATURE_NAME = {
  BE_PERMISSION_LABELS_ENABLED,
  DIGITAL_ADOPTION_PLATFORM_PROVIDER,
  DIGITAL_ADOPTION_PLATFORM_PROVIDER_PROJECT_KEY,
  INTERNAL_TENANT_COMMUNICATION,
  EXTERNAL_COMPANY_PDF_ENABLED,
  ARC_SCANNING,
  SCAN_MANAGEMENT_V2_ENABLED,
  SALES_SCAN_MANAGEMENT_V2_ENABLED,
  MY_PRICELINK_INTEGRATION_ENABLED,
  FULFILMENT_EDIT_LOCK,
  AUTO_CLOSE_SALE_ORDER,
  MPL_PRICING_DEFAULT_ENABLED,
  MPL_PRICING_EXPORT_ENABLED,
  MPL_PRICING_QUOTE_ENABLED,
  OEM_INTEGRATONS_ENABLED,
  PARTS_REMSTAR_ENABLED,
  INVENTORY_IN_OUT_SALES_ENABLED,
  AUTO_REPLACEMENT_FEATURE_ENABLED,
  BASELINE_PAGE_IN_PDFS_ENABLED,
  REQUIRE_PASSCODE,
  CORE_MANAGEMENT_ENABLED,
  NCM_REPORT_ENABLED,
  NEW_CUSTOMER_CHANGE_DISPLAY_NUMBER_CONTROL,
  NEW_VENDOR_CHANGE_DISPLAY_NUMBER_CONTROL,
  RELEASE_ADMIN,
  EXTERNAL_COMMUNICATION,
  INTERNAL_COMMUNICATION,
  COMMUNICATION_ANALYTICS_METRICS,
  LIVE_CHAT_WIDGET_ENABLED,
  FAQ_PLATFORM_ENABLED,
  SHOW_ROLES_FOR_SUPER_ADMIN,
  EXTENDED_CHECK_PAYEE_ADDRESS,
  PARTS_MASTER_OEM_SOURCE,
  CAMPAIGN_MANAGEMENT_DEALER_PROPERTY,
  INSPECTIONS_AND_RECOMMENDATIONS_PDF_ENABLED,
  INSPECTION_ADDITIONAL_MEASUREMENTS_ENABLED,
  PDF_CONFIGURATOR_ENABLED,
  CHECKIN_PDF_UNIFIED_TNC_ENABLED,
  IS_OBD_ENABLED,
  VIS_PDF_ENABLED,
  NEW_DSE_ENABLED,
  WEB_CHECKIN_ENABLED,
  DEALERS_ALLOWED_TO_SEARCH_IN,
  SCHEDULING_AUDIT_LOGS_ENABLED,
  SCHEDULING_BY_HOURS_ENABLED,
  ENTERPRISE_BDC_ENABLED,
  AUTO_DISPATCH_FEATURE_ENABLED,
  KEY_DROP_OFF_ENABLED,
  CONSUMER_PORTAL_ENABLED,
  CCPA_ENABLED,
  SERVICE_MENU_EDITABLE,
  CRM_ENABLED,
  CRM_PLANNER_ENABLED,
  TEMPLATE_STORE_ENABLED,
  VEHICLE_RECOMMENDATION_ENABLED,
  SUMBIT_ECONTRACT,
  ZERO_CONTACT_SALES,
  SALES_DRS_FNIMENU,
  SALES_DRS_EDOCS,
  DEALS_FNIMENU,
  FNI_AUTO_UPDATE_ENABLED,
  CONCIERGE_FNI_ENABLED,
  NEW_DEAL_CREATE_API_ENABLED,
  ACTIVATE_GHOST_PRODUCT,
  MPVI_USAGE_REPORT_ENABLED,
  EMPLOYEE_OT_CONFIG_STATE_LEVEL,
  CLOCKINV3_ENABLED,
  AUTO_RIM_PART_EXIT_PROCESS,
  DSE_VIRTUAL_CLIPBOARD_ENABLED,
  TOYOTA_TST_INTEGRATION_ENABLED,
  DSE_STANDALONE,
  BIN_SHELF_DRAWER_FEATURE_ENABLED,
  IS_BANK_ADMIN_ENABLED,
  KNOWLEDGE_BASE_ADMIN,
  SERVICE_NOW_ENABLED,
  PERMISSION_ROLE_APPROVAL,
  CREATE_CUSTOM_ROLE_DISABLED,
  CONFIGURE_APPROVERS_DISABLED,
  ENABLE_FEE_DETAILS_IN_SA_REPORT,
  PRODUCT_FEEDBACK_DASHBOARD_ENABLED,
  PRINT_METRICS_ENABLED,
  NPS_DASHBOARD_ENABLED,
  NPS_SETUP_ENABLED,
  EMPLOYEE_LEADER_BOARD_OBJECTIVES,
  NEW_IMAGE_UPLOADER_ENABLED_IN_SERVICE_MODULE,
  LEADERBOARD_ENABLED,
  ARC_ENABLED,
  NEW_MPVI_ENABLED,
  CENTRALISED_ACCOUNTING,
  NEW_MOBILE_CHECK_IN_ENABLED,
  CUSTOMER_REWARDS,
  CRM_LEGACY_ASSIGNEES_ENABLED,
  CRM_LITE_FOR_DRP,
  CRM_SERVICE_PARTS_LEADS_ENABLED,
  NEW_TEAMS_SETUP_ENABLED,
  PDF_CONFIGURATOR_UI_ENABLED,
  TEKION_PAYROLL_ENABLED,
  DRP_ENABLED,
  QUOTE_SERVICE_ENABLED,
  RO_LIST_AUTO_REFRESH_DISABLED,
  MULTI_OEM_SWITCH_ENABLED,
  ACCOUNTING_SITE_OVERRIDE_ENABLED,
  GM_SERVICE_MENU_PRESENTED_ENABLED,
  THIRD_PARTY_PAYROLL_ENABLED,
  RO_NEW_ADD_JOB_VIEW_ENABLED,
  DEALER_LOGOS_ENABLED,
  OEM_DETAILS_ENABLED,
  TAP_SUPPORT_CHAT,
  OEM_ENABLED,
  PARTS_MULTI_OEM_ENABLED,
  SO_PDF_HTML_PREVIEW_ENABLED,
  PARTS_SEARCH_V2_ENABLED,
  PARTS_INVENTORY_SHARED,
  PARTS_PDF_BACKPRINT_ENABLED,
  PARTS_DOCUMENT_SERVICE_INTEGRATION_ENABLED,
  OEM_USER_FCA_ENABLED,
  SERVICE_CATALOG_BASE_ENABLED,
  SERVICE_CATALOG_PRO_ENABLED,
  SERVICE_CATALOG_PRO_V2_ENABLED,
  CALC_ENGINE_ENABLED,
  GALAXY_ENABLED,
  DEAL_STATUS_CONFIG_ENABLED,
  DEAL_STATUS_RULES_CONFIG_ENABLED,
  GALAXY_DEAL_COMPARE_ENABLED,
  VEHICLE_CONFIGURATOR_ENABLED,
  EMPLOYEE_ROLE_EDIT_DISABLED,
  DOWNLOAD_CLAIMS_ENABLED,
  OPSTRAX_DELIVERY_PART_ENABLED,
  CMS_DOB_STR_ENABLED,
  COMMUNICATION_TEMPLATE_BUILDER_ENABLED,
  OBD_ENABLED,
  OBD_DATA_SOURCE_PRIORITY_SETTINGS_ENABLED,
  BMW_AIR_KSD_ENABLED,
  MULTI_VEHICLE_DESKING,
  SEND_REAL_TIME_PUNCHES_ENABLED,
  SESSION_RECORDING_DISABLED,
  ACCOUNTING_FORM8300,
  ACCOUNTING_FORM1099,
  ACCOUNTING_AP_OTHER_PAYABLES,
  ACCOUNTING_TAX_CODES,
  ACCOUNTING_SALES_TAX_REPORT,
  ACCOUNTING_VI_USE_TAX_REGIME,
  ACCOUNTING_COUNT_LOGIC_TYPE,
  ACCOUNTING_JOURNAL_ENTRY_VIRTUALIZATION_ENABLED,
  OCR_INVOICING_ENABLED,
  PARTS_STOCK_ORDER_CREATION_DISABLED,
  AUTO_PROMISE_TIME_ENABLED,
  AUTO_FILL_CHARGE_CUSTOMER,
  EMPLOYEE_SCHEDULER_ENABLED,
  EMPLOYEE_SCHEDULER_AVAILABILITY_ENABLED,
  CUSTOMERS_SEARCH_V2_ENABLED,
  SALESORDER_MULTIFACET_SEARCH_ENABLED,
  PART_CROSS_SITE_DOCUMENT_ACCESS_ENABLED_FROM_MAIN_SITE,
  SERVICE_MENU_PRO_ENABLED,
  STANDARD_STATE_CODE_ENABLED,
  KEYLOUNGE_WALKIN_ENABLED,
  AUTO_PARTS_PRICING_ENABLED,
  SERVICE_SCREENS_PARTS_SEARCH_ENABLED,
  SUPPORT_PORTAL_DISABLED,
  VEHICLE_TRACKING_ENABLED,
  SERVICE_ADVISOR_REPORT_V2_ENABLED,
  DEALERTIRE_ENABLED,
  EXPRESS_MODE_ENABLED,
  RO_APPROVAL_SYSTEM_ENABLED,
  CONNECTED_DISPLAYS_ENABLED,
  FCA_CONTROL_NUMBER_ENABLED,
  ACCESS_SETTINGS_ENABLED,
  ACCESS_SETTINGS_V2_ENABLED,
  SESSION_RECORDER_PROVIDER,
  SESSION_RECORDING_FETCH_CAPTURE_DISABLED,
  SPG_SINGLE_PRICE_MODE_ENABLED,
  FLEX_RIDE_ENABLED,
  AP_CHECK_REFUND_ENABLED,
  UV_EYE_INTEGRATION_ENABLED,
  TELEMATIC_OBD_ENABLED,
  SUBLET_PO_MULTIPLE_RO,
  SUBLET_RECIEVING_AND_REISSUE_IGNORE_ENABLED: 'SUBLET_RECIEVING_AND_REISSUE_IGNORE_ENABLED',
  NEW_TAX_REGIME_ENABLED,
  CMS_FIELD_SEARCH_ENABLED,
  INTERNAL_APPS_HIDDEN,
  VIEW_PARTS_ON_SO_LIST_ENABLED,
  SCHEDULING_SETTINGS_CAPACITY_ENABLED,
  NEW_APP_SKELETON_ENABLED,
  ANALYTICS_CORRUPTED_SERVICE_FORECAST_DATA_TIMESTAMP,
  ANALYTICS_IS_SERVICE_FORECAST_DATA_CORRUPTED,
  ANALYTICS_INVENTORY_RECONDITIONING_ENABLED,
  ANALYTICS_DASHBOARD_BUILDER_ENABLED,
  SERVICE_HISTORY_BULK_PDF_DOWNLOAD_ENABLED,
  FCA_DEFAULT_MARKUP_API,
  FORD_NEW_PDR_CODE_ENABLED,
  INSPECTION_LABOR_OPCODES_REVIEW_ENABLED,
  WORKFLOW_ENGINE_ENABLED_FOR_DEALER,
  PROCESS_AUTOMATION_ENABLED,
  RULEENGINE_ENABLED_FOR_SERVICE,
  TEKION_PAY_ENABLED,
  TEKION_PAY_SERVER_SIDE_ENABLED,
  TPAY_PAYMENT_LINK_ENABLED,
  TPAY_DASHBOARDS_ENABLED,
  PARTS_POS_ENABLED,
  SHIPMENT_RECEIVING_ENABLED,
  E_SIGN_ENABLE_FOR_R1,
  COMPLETION_CERTIFICATE_ENABLED,
  OEM_STOCK_ORDER_COMPUTE_SERVICE_ENABLED,
  MAX_PARTS_PRICE_V2_ENABLED,
  PARTS_AI_SEARCH_ENABLED,
  VI_RE_POSTING,
  MODULA_INTEGRATION_ENABLED: 'MODULA_INTEGRATION_ENABLED',
  NEW_ZCS_ENABLED,
  VEHICLE_DIAGNOSTIC_REPORT_PDF_ENABLED,
  ACCOUNTING_NEW_PDF_FRAMEWORK_ENABLED,
  SHOW_ALL_LENDER_PROGRAMS,
  VARIABLE_LABOR_PAY_RATE_ADJUSTMENTS_ENABLED,
  WIP_REPORT_V2_ENABLED,
  E_SIGN_CAPABILITY,
  FNI_SPP_LENDER,
  GLBA_COMPLIANCE,
  DYNAMIC_REPORT_BUILDER_ENABLED,
  CUSTOMER_AUDIT_ENABLED,
  ENABLE_MULTI_SESSION_TABS,
  GM_REWARDS_V2_ENABLED,
  CUSTOMER_VALUE_ENABLED,
  HIDE_CUSTOMER_360_OTHER_TABS_CMS_ONLY,
  ENABLE_SO_PRICE_OVERRIDE,
  DOUBLE_OPT_IN_SETUP_ENABLED,
  NEW_DEAL_PREVIEW_WORKFLOW,
  FIXEDOPS_V2_ENABLED,
  RV_DEALER_ENABLED,
  BRINGOZ_DELIVERY_PART_ENABLED,
  CMS_MERGE_V2,
  DISPLAY_CUSTOMER_ID_IN_APPOINTMENT_PDF_ENABLED,
  WEB_CHECKIN_NEW_ADD_JOB_VIEW_ENABLED,
  SOURCE_CODE_PART_PREFIX_ENABLED,
  ANALYTICS_DEALER,
  ANALYTICS_WEB_ENTERPRISE_DASHBOARD_ENABLED,
  ANALYTICS_FS_EXCEL_MAPPING_ENABLED,
  ANALYTICS_DEALER_STATS_ENABLED,
  ANALYTICS_CLICKSTREAM_ENABLED,
  ANALYTICS_USAGE_REPORT_ENABLED,
  INCHCAPE_ENABLED,
  ANALYTICS_INTERNAL_DEALER,
  INTERNAL_FEATURE_MANAGEMENT_ENABLED,
  ENABLE_TAX_BREAKUP,
  TAX_GROUP_ENABLED,
  IS_LEASE_NOT_SUPPORTED,
  VEHICLE_TYPE_CONFIGURATION_ENABLED,
  TEKION_CREDIT_COMPLIANCE,
  CRM_BULK_MERGE_ENABLED,
  TECHNICIAN_RECOMMENDATION_REPORT,
  SERVICE_VISIBILITY_DASHBOARD_V2,
  MULTIPLE_VEHICLE_TYPE_ENABLED,
  PARTS_SALES_ORDER_TAX_OVERRIDE_ENABLED,
  MULTILINGUAL_ENABLED,
  MULTILINGUAL_TRACKER_ENABLED,
  ACCOUNTING_MULTI_CONTROL_ENABLED,
  ACCOUNTING_PRIOR_PERIOD_ENABLED,
  SALES_LEAD_MANUAL_STAGE_MOVEMENT_ENABLED,
  SERVICE_TYPE_OVERRIDE_ENABLED,
  CONSUMER_SCHEDULING_V2_ENABLED,
  EMPLOYEE_HOURS_OT_DOT_V3,
  SERVICE_AGGREGATION_SCHEDULING_SETTINGS_ENABLED,
  PARTS_SALES_ORDER_LIST_V2_ENABLED,
  CUSTOMER_CANCELLATION: 'CUSTOMER_CANCELLATION',
  CAMPING_WORLD_ENABLED,
  OEM_RETRIEVAL_ENABLED,
  SERVICE_MAINTENANCE_ENABLED,
  STANDARDIZED_MAKE_SETUP_ENABLED,
  PARTS_SALES_ORDER_SYNC_LINE_FLOW_ENABLED,
  PARTS_RECEIVING_IN_MODAL_ENABLED,
  FORCED_PART_SELECTION_ENABLED,
  NEW_CMS_GLOBAL_SEARCH_ENABLED,
  GLOBAL_SEARCH_WITH_TAGS_ENABLED,
  CMS_DUPLICATE_PREVENTION_ENABLED,
  TEKION_STORE_ENABLED,
  EMPLOYEE_HOURS_APP_DISABLED,
  VEHICLE_MODEL_AND_BODY_TYPE_FILTER_ENABLED,
  DEFERRED_RECALL_REPORT_ENABLED,
  CAR_BRAVO_ENABLED,
  ETKA_CATALOGUE_IMPORT_ENABLED,
  TEKION_BRANDING_DISABLED,
  CTC_DISABLED,
  EXPRESS_INSPECTION_ONLY,
  PROCESS_CLAIM_FOR_WARRANTY_SPLIT,
  PROCESS_CLAIM_ON_JOB_COMPLETION,
  PARTS_FOUNDATION_ENABLED,
  CASH_ON_DELIVERY_ENABLED,
  SCHEDULING_RO_JOBS_ENABLED,
  CRM_COMMUNICATION_HUB_V2,
  OPEN_RO_MIGRATION_ENABLED,
  CRM_CUSTOMER_ONEVIEW_ENABLED,
  LIVE_CHAT_WEBSOCKET_ENABLE,
  PARTS_SOR_DETAILS_PDF_NEW_ENABLED,
  WIP_ON_DATE_FILTER_ENABLED,
  DEALS_FOUNDATION_ENABLED,
  CUSTOMER_EAVS2_VALIDATION_ENABLED,
  REPUSH_RO_ENABLED,
  RRG_WARRANTY_POSTING_ENABLED,
  ELITE_EXTRA_DELIVERY_PART_ENABLED,
  SERVICE_CRM_ENABLED,
  API_LAYER_ENABLED,
  CMS_NEW_CUSTOMER_LINKING_ENABLED,
  INVENTORY_PARAMETERS_ENABLED,
  LOANER_ONTRAC_ENABLED,
  TEKION_WALLET_ENABLED,
  INTERNAL_COMMUNICATION_SETTINGS_ENABLED,
  NEGATIVE_OH_ENABLED: 'NEGATIVE_OH_ENABLED',
  PARTS_GROUPMENT_ENABLED: 'PARTS_GROUPMENT_ENABLED',
  MPI_MULTI_CODE_ENABLED,
  CLAIM_WORK_POOL_ENABLED,
  DEROGATION_MANAGEMENT_ENABLED,
  GUARANTEE_MANAGEMENT_ENABLED,
  RO_LIST_EXPANDED_VIEW_ENABLED,
  CORE_NPS_ENABLED,
  ENHANCE_VIN_DECODING,
  CRM_CUSTOM_WIDGETS_ENABLED,
  IS_ML_DASHBOARD_ENABLED,
  NEW_DOWNLOAD_CENTRE_ENABLED,
  APPROVAL_FLOW_ENABLED,
  MIGRATED_TO_NEW_ADVISOR_PERFORMANCE,
  CRM_STANDALONE_ENABLED,
  TIRE_STORAGE_ENABLED: 'TIRE_STORAGE_ENABLED',
  CMS_VEHICLE_OWNERSHIP_ENABLED,
  PAYROLL_LOCAL_TAX_ENABLED,
  PAYROLL_WA_LTI_REPORT_ENABLED,
  SOR_V2_ENABLED,
  CUSTOMER_PROSPECTING_ENABLED,
  CHECKIN_CONFIGURATOR_ENABLED,
  CUSTOMER_360_COUPONS_ENABLED,
  CRM_NEW_GTC_SETUP_ENABLED,
  LEAD_CONVERSION_SCORE_ENABLED,
  CMS_AUDIT_REPORT_ENABLED,
  PRINTER_SETUP_V2_ENABLED,
  NEW_PARTS_INVENTORY_SERVICE_ENABLED,
  HEALTH_CHECK_ENABLED,
  NEW_PHYSICAL_INVENTORY_ENABLED,
  CUSTOM_RO_NUMBER_ENABLED,
  MULTIPLE_RO_SERVICE_ADVISOR_ENABLED,
  PARTS_SALES_ORDER_V3_ENABLED,
  BIN_SPOT_CHECK_ENABLED,
  LEAD_ASSIGNMENT_V2_ENABLED,
  VIN_LOOKUP_DISABLED,
  APPOINTMENT_PARTS_FLOW_ENABLED,
  SERVICE_APPOINTMENT_PROMISE_TIME_ENABLED,
  SERVICE_APPOINTMENT_QUOTE_SEARCH_ENABLED,
  DFE_ENABLED,
  NEW_DFE_ENABLED,
  GDPR_ENABLED,
  CPRA_ENABLED,
  MIGRATION_DASHBOARD_ENABLED,
  PLATFORM_AUDIT_LOGS_FOR_SALES,
  PO_AUTOMATION_ENABLED,
  PO_PART_LINE_ENABLED,
  OPECODE_INTEGRATION_ENABLED,
  EUROPEAN_ROLES_AND_PERMISSIONS_ENABLED,
  EUROPEAN_FIELD_SETUP_ENABLED,
  FIELD_SETUP_CODE_COLUMN_DISABLED,
  E_SIGN_ENABLED,
  PROFIT_LOSS_VIEW_ENABLED,
  PICKLIST_V2_ENABLED: 'PICKLIST_V2_ENABLED',
  PICKLIST_V2_ENHANCEMENTS_ENABLED: 'PICKLIST_V2_ENHANCEMENTS_ENABLED',
  MOBILE_PICKLIST_ENABLED: 'MOBILE_PICKLIST_ENABLED',
  CHARGE_CUSTOMER_REPORT_ENABLED,
  CORE_INVENTORY_SITE_LEVEL_ENABLED,
  IDLE_TIMEOUT_ENABLED,
  LOST_TIME_TRACKING_ENABLED,
  RO_SALES_PNA_HIDE_LIST_PRICE_COLUMN,
  NDC_SET_UP_ENABLED,
  DEALER_TRACK_DFE_CREDIT_APPLICATION_ENABLED,
  CUDL_DFE_CREDIT_APPLICATION_ENABLED,
  CARQUEST_ENABLED,
  LEASE_COMPARISION_ENABLED,
  FLEETS,
  USER_EMPLOYEE_MODIFICATION_DISABLED: 'USER_EMPLOYEE_MODIFICATION_DISABLED',
  USER_EMPLOYEE_CREATION_DISABLED,
  SAVI_GM_ENABLED,
  EUROPEAN_EMPLOYEE_ONBOARDING_ENABLED,
  RO_DASHBOARD_V2_DISABLED,
  HUNTER_LANE_ENABLED,
  RO_JOB_TAGS_V2_ENABLED,
  UNASSIGNED_SKILL_TAGS_FILTER_ENABLED,
  RO_MILEAGE_IN_FILTER_DISABLED,
  SUPERSESSION_RECEIVING_ENABLED,
  SO_PDF_CONVERT_RICH_TEXT_TO_STRING_ENABLED,
  OPTIN_OPTOUT_SETTINGS_ENABLED,
  STOCK_VALUATION_REPORT_ENABLED,
  AURORA_ENABLED,
  NEW_TAX_SERVICE_ENABLED,
  CAPENCY_VERIFICATION_REQUIRED,
  LIST_VIEW_DEFAULTS_CONFIGURATION_ENABLED,
  MOBILE_ASYNC_UPLOAD_ENABLED,
  ACCOUNTING_FORM1099_ELECTRONIC_FILING,
  ROLES_AND_RIGHTS_ENABLED,
  POLLY_INTEGRATION_ENABLED,
  SHOULD_UPDATE_CUSTOMER_INFO_IN_RO_DETAILS,
  USE_NEW_TAX_CODES_SETUP,
  VEHICLE_SYNDICATION_ENABLED,
  VEHICLE_PRECONFIGURED_OPTIONS_ENABLED,
  APPOINTMENT_PDF_CONFIGURATOR_ENABLED,
  AUTO_IPACKET_ENABLED,
  CRM_ZULTYS_ENABLED,
  PREPAID_SALES_ENABLED,
  CORE_INVENTORY_ENABLED,
  NEW_GM_CLAIM_FORM_ENABLED: 'NEW_GM_CLAIM_FORM_ENABLED',
  BMW_CANADA_CLAIM_FORM_ENABLED: 'BMW_CANADA_CLAIM_FORM_ENABLED',
  PARTS_NEW_PHASE_IN_OUT_ENABLED,
  REMSTAR_API_INTEGRATION_ENABLED,
  VEHICLE_GROUP_ENABLED,
  ADVANCED_APPOINTMENT_ENABLED,
  COV_REFERENCING_ENABLED,
  EUR_CMS_ENABLED,
  ANALYTICS_SALES_CONSULTANT_ENABLED,
  SO_DIRECT_INVOICING_ENABLED,
  COMMUNICATION_SOUND_NOTIFICATIONS,
  WEB_NOTIFICATION_METRICS_ENABLED,
  EUROPEAN_ANNUAL_SALES_OBJECTIVE_ENABLED,
  ENABLE_ENHANCED_DOCUMENTS_TAB,
  WAREHOUSE_MANAGEMENT_V2_ENABLED,
  AMOUNT_ROUND_OFF_ENABLED,
  CAMPAIGN_COLLECTION_V3_ENABLED,
  CONNECTED_CAR_DEVICE_ENABLED,
  VENDOR_AUDIT_ENABLED,
  PARTS_INVOICES_AND_RECONCILIATION_ENABLED,
  INVOICE_RECONCILIATION_ENABLED,
  NEW_STOCK_ORDER_CALCULATION_SERVICE,
  VIEW_PARTS_PERFORMANCE_REPORT_ENABLED,
  ACTIVITY_LOGS_ENABLED,
  RO_LIST_KPI_SALES_V3,
  CENTRAL_COMMUNICATION_OPTIN_PREFERENCES_ENABLED,
  DEALER_CONFIG_GENERAL_DETAILS_FIELD_MODIFICATION_ENABLED,
  GLOBAL_SEARCH_METRICS_ENABLED,
  CMS_KEEP_MERGE_INFO_OUTSIDE_CUSTOMER,
  KEEP_VEHICLE_INFO_OUTSIDE_CUSTOMER_ENABLED,
  MIMICKING_ACCESS_SETUP_LE_ENABLED,
  DRS_REPORTING_ENABLED,
  FEE_GRID_CONFIG_ENABLED,
  INTER_DEALERSHIP_PO_ENABLED,
  SYSTEM_ALERTS_ENABLED,
  ACCOUNTING_AUTO_POSTING_RBNI,
  RO_DETAILS_AGGREGATION_ENABLED,
  MULTIPLE_OEM_OPCODE_ENABLED,
  ENABLE_TASK_MANAGER_TAB,
  INVENTORY_COST_INFLATION,
  ENABLED_ANALYTICS_PROVIDER: 'ENABLED_ANALYTICS_PROVIDER',
  ANALYTICS_CUSTOM_SAMPLE_RATE: 'ANALYTICS_CUSTOM_SAMPLE_RATE',
  CMS_VIEW_ASSOCIATED_COMPANIES,
  SO_PERIODIC_INVOICE_ENABLED,
  INVOICE_CONFIRMATION_ENABLED,
  ANALYTICS_SALES_REVAMP_DASHBOARD_ENABLED,
  IS_AUTO_ADJUST_DEAL_POSTINGS_ENABLED,
  INTERNAL_MODULES_AND_DATA_SOURCES_ENABLED,
  DOCUMENTS_TAB_PERFORMANCE_IMPROVEMENTS,
  VMS_FETCH_VENDOR_CATEGORIES,
  EXTERNAL_DMS_ENABLED: 'EXTERNAL_DMS_ENABLED',
  INCHCAPE_VENDOR_MANAGEMENT_FIELDS_ENABLED,
  ALLOW_IF_OFFSET_ACCOUNTS_IN_POSTING,
  EMPLOYEE_INFORMATION_NON_MANDATORY_FIELDS_ENABLED,
  CORE_FOUNDATION_ENABLED,
  NEW_ETA_INFORMATION_TO_CCC_SERVICE: 'NEW_ETA_INFORMATION_TO_CCC_SERVICE',
  DIGITAL_SIGNING_PLATFORM,
  OEM_REWARDS_DISABLED,
  LABOR_RATE_CONFIG_ENABLED,
  ML_BASED_LEAD_CONTACT_LINKING_ENABLED,
  COMM_SETUP_DELETE_DOMAIN_DISABLED,
  SINGLE_NUMBER_GTC_ENABLED,
  EMPLOYEE_SSN_VALIDATION_DISABLED,
  PRODUCT_GROUP_TAX_CODE_ENABLED,
  OPCODE_EXCEL_V2_ENABLED,
  SERVICE_DASHBOARD_V2,
  TRIM_ATTRIBUTE_RESTRICTION_ENABLED,
  ENABLE_AUTO_CLOCK_OUT_EMPLOYEE_SCHEDULE,
  ENABLE_AUTO_GENERATED_CLOCK,
  ACCOUNTING_SOLUTIOS_ENABLED,
  PARTS_CUSTOMER_DATA_DELETION_ENABLED,
  ENABLE_DEALS_DASHBOARD,
  HERCULES_PROGRAM_ENABLED,
  NEW_PAD_REPORT_ENABLED,
  FEED_SYNDICATION_ENABLED,
  DEALER_DETAILS_DEPARTMENT_ADDRESS_DISABLED,
  CORE_CANNED_REPORTS_CHARGE_CUSTOMER_ENABLED,
  CORE_CANNED_REPORTS_NEW_CUSTOMER_CREATED_ENABLED,
  CORE_CANNED_REPORTS_PERMISSION_BY_ROLE_ENABLED,
  CORE_CANNED_REPORTS_PERMISSION_BY_USER_ENABLED,
  CORE_CANNED_REPORTS_USERS_BY_ROLE_ENABLED,
  BULK_SUPPORT_FOR_INVOICE_PDF_AR_STATEMENT,
  CUSTOMER_STATEMENT_GENERATION_THROUGH_BRS,
  FULFILMENT_PART_AUTO_RESOLVING_ENABLED,
  FORM_BUILDER,
  AVERAGE_COST_ENABLED: 'INVENTORY_PART_AVERAGE_COST_ENABLED',
  SERVICE_FOUNDATION_ENABLED,
  AUTO_ADD_COUPON_ENABLED,
  NEW_PROMISE_TIME_SERVICE_ENABLED,
  GET_ADDRESS_DOT_IO_ENABLED,
  INCHCAPE_CMS_FIELDS_UPGRADATION_ENABLED,
  PARTS_PO_APPROVAL_FLOW_ENABLED,
  ON_HAND_ADJUSTMENT_APPROVAL_FLOW_ENABLED,
  INVENTORY_MINIMISED_INDEX,
  ACCOUNTING_SUB_APPLICATION,
  CUSTOMER_UNMAPPED_VALUE_ENABLED,
  IS_HONDA_PROGRAM,
  DEALER_STATUS_USAGE_ENABLED,
  IS_AEC_PROGRAM,
  CONFIGURABLE_PAYMENT_MODES,
  MULTI_TECHNICIAN_AUTO_FLAGGING_ENABLED,
  TRAINING_USER_SENTIMENT_ENABLED,
  CMS_INCLUDE_ADDRESS_IN_DUPLICATE_PREVENTION,
  TCA_EXTERNAL_CONTRACTS_ENABLED,
  LEAD_DEAL_SYNC_V2,
  RETURN_PART_ON_RO_ENABLED,
  PERPETUAL_CAMPAIGNS_ENABLED,
  PARTS_THIRD_PANE_BROWSER_ENABLED,
  DISABLE_SEND_UPDATED_PARTS_ALWAYS,
  IS_PNI_REGISTERED,
  SPECIAL_ORDER_PRICE_CODE_ENABLED,
  IS_RRG_PROGRAM: 'IS_RRG_PROGRAM',
  IS_INCHCAPE_PROGRAM: 'IS_INCHCAPE_PROGRAM',
  SERVICE_WORKLOAD_PLANNER_ENABLED,
  SERVICE_MULTIPLE_TRANSPORTATION_FLOW_ENABLED,
  FNIMENU_V2_ENABLED,
  CLOCKED_TIME_DRAWER_ENABLED,
  PART_ESTIMATION_ENABLED,
  PRE_ONBOARDING_QUESTIONNAIRE_ENABLED,
  CASHIERING_PROCUREMENT_CARD_ENABLED,
  DOMAIN_COMMUNICATION_SETUP_ENABLED: 'DOMAIN_COMMUNICATION_SETUP_ENABLED',
  CMS_MULTI_ENTITY_CONSENT_MANAGEMENT_ENABLED,
  JOB_CLOCK_IN_OUT_REASON_ENABLED,
  EMPLOYEE_DELETE_PII_INFO_ENABLED,
  AEC_USER_SETUP_ENABLED,
  SERVICE_V3_ENABLED,
  CENTRAL_VEHICLE_ASSOCIATION_ENABLED,
  CMS_CL_APPROVAL_FLOW_ENABLED,
  CRM_MULTI_OEM_SWITCH_ENABLED,
  PAYTYPE_CREATION_LIMIT_ENABLED,
  OH_ADJUSTMENT_ACCOUNT_POSTING_ENABLED,
  CMS_VERBIAGE_PREFERENCES_DIFFERENTIATION_ENABLED,
  TEKION_SMART_SUGGEST_PARTS_ENABLED,
  CRM_MULTI_DOMAIN_ENABLED,
  VI_ARC_LITE_ENABLED,
  USED_CORE_RETURN_CREDIT_PO_ENABLED,
  DEALER_LEVEL_SETUPS_ENABLED,
  AI_COMMUNICATIONS_ENABLED,
  NEW_RECEIVING_API_ENABLED,
  LIVE_CHAT_VIDEO_CALL_ENABLED,
  CHARGE_CUSTOMER_COLOR_CODING_ENABLED,
  SERVICE_DATE_CONFIGURATOR_ENABLED,
  AEC_PROGRAM_ENABLED_APPS,
  PARTS_TAX_CODE_ENABLED,
  CMS_RELATIONSHIP_MANAGEMENT_ENABLED,
  NEW_TRADEIN_ENABLED,
  APPOINTMENT_HOURS_COLUMN_ENABLED,
  CLOCK_TIME_DISABLED,
  AUTO_ADD_OPCODE_RULES_ENABLED,
  ADD_SALES_DEPARTMENT_AND_EXCEPTIONS_WITH_MAKE_ENABLED,
  REGISTERATION_NO_AND_ZIP_CODE_SEARCH_ENABLED,
  INVENTORY_TURNOVER_REPORT_ENABLED,
  DEFERRED_SERVICE_REPORT_V2_ENABLED,
  TECH_PERFORMANCE_REPORT_V2_ENABLED,
  CMS_CONFIGURABLE_PAYMENT_MODES_ENABLED,
  CMS_SHOW_ADDRESS_FIELDS_IN_MERGE,
  RO_AGE_REPORT_ENABLED,
  GMOSS_SCHEDULING_TYPE_ENABLED,
  PART_IMPREST_STOCK_FEATURE_ENABLED,
  ENABLE_DOCS_VISIBILITY_ACROSS_MODULES,
  CRM_CUSTOMER_LEVEL_VALUE_ENABLED,
  TECH_PERFORMANCE_V2_ENABLED,
  ACCOUNTING_INCHCAPE_DEALER,
  CHECK_HQ_ENABLED,
  DEALER_CONFIG_LOCALE_SETTING_ENABLED,
  QUOTE_SETUP_ENABLED,
  COMMERCIAL_SALES_SETUP_ENABLED,
  NEW_DETAILED_SALES_REPORT_ENABLED,
  IS_ORDER_MANAGEMENT_SYSTEM_V2_ENABLED,
  INDICATOR_MENU_CONFIG,
  REQUEST_ID_ENABLED: 'REQUEST_ID_ENABLED',
  LABEL_CONFIGURATOR_ENABLED: 'LABEL_CONFIGURATOR_ENABLED',
  CREDIT_SALE_ENABLED: 'CREDIT_SALE_ENABLED',
  PARTS_ASYNC_PRINT_ENABLED: 'PARTS_ASYNC_PRINT_ENABLED',
  LABEL_CONFIGURATOR_DISABLED: 'LABEL_CONFIGURATOR_DISABLED',
  PARTS_RO_SALES_PART_AGGREGATE_SEARCH_ENABLED: 'PARTS_RO_SALES_PART_AGGREGATE_SEARCH_ENABLED',
  PARTS_ANOMALY_ENABLED,
  PRICE_DIFFERENCE_ENABLED,
  PAYMENT_APPROVAL_ENABLED,
  PART_FRACTIONAL_SALE_ENABLED,
  NEW_BULK_INVENTORY_ENABLED,
  INTERAC_REFUND_ENABLED,
  ENTERPRISE_V2_ENABLED,
  ENTERPRISE_V2_DETAILS,
  ENTERPRISE_BU_ENABLED,
  APP_SKELETON_THEME,
  VEHICLE_ATTRIBUTE_STANDARDISATION_ENABLED,
  DEFAULT_ARC_UI_ROUTE_ID,
  PPR_PRINT_ENHANCEMENT_ENABLED,
  PARTS_TRANSACTION_HISTORY_V2_ENABLED,
  TRANSACTION_LIMIT_ON_ALL_SOURCES_ENABLED,
  ENHANCED_VI_DOCS,
  PARTS_SALES_ORDER_BULK_CREATE_RETURN_ENABLED,
  IS_STOCK_ORDER_BY_BIN_ENABLED,
  PARTS_SALES_ORDER_ROWS_SETTINGS_ENABLED,
  IS_AFTERMARKET_PARTS_ENABLED,
  HOLD_AUTOMATION_ENABLED,
  SERVICE_MENU_SETUP_AUDIT_LOGS_ENABLED,
  UNIVERSAL_SEARCH_ENABLED,
  E_SIGN_SERVICE_INTEGRATION_ENABLED,
  CMS_ENTERPRISE_NOTES_ENABLED,
  REFUND_APPROVAL_ENABLED,
  E_SIGN_PLATFORM_ENABLED,
  CASHIERING_DEMO_APP_ENABLED,
  SHOW_MONTHLY_PAYMENT,
  QUOTE_PRIMARY_ACTION,
  MODIFY_QUOTE_LEAD,
  SHOW_ADDITIONAL_ACCESSORIES_SEPARATELY,
  SHOW_TAX_RATES_DROPDOWN,
  QUOTE_CONTRACT_TYPE,
  SHOW_UPFITTINGS_FROM_LIBRARY,
  ENABLE_FAVORITES,
  ENABLE_TEMPLATES,
  ENABLE_CATALOG_REORDER,
  ENABLE_QUOTE_ASSIGNEE,
  ENABLE_QUOTE_CHANGE_ASSIGNEE,
  ENABLE_QUOTE_BULK_SELECTION,
  TRADE_IN_TYPE,
  TAXES_AND_FEES_TYPE,
  QUOTE__SHOW_TRADE_IN_DESC_IN_CONFIG_CARD,
  QUOTE__SHOW_ADDITIONAL_INFO_DESC_IN_CONFIG_CARD,
  QUOTE__SHOW_TRADE_IN,
  ENABLE_BPM_AND_VAT,
  NON_CVC_ENABLED,
  ENABLE_EMAIL_CUSTOMIZATION,
  ENABLE_LEAD_EXPORT,
  QUOTE__SHOW_DISCLOSURE_TYPE_COLUMN_SERVICE_PLANS,
  QUOTE__ENABLE_MARKET_ADJUSTMENT_FOR_ADD_ONS,
  QUOTE__ENABLE_VEHICLE_BUILDABILITY_VERIFICATION,
  QUOTE__EDIT_VEHICLE_SPECIFICATION,
  QUOTE__ENABLE_VSO_INQUIRY,
  ENABLE_SHIP_THRU,
  QUOTE__ENABLE_REMARKS,
  QUOTE__INCENTIVE_TYPE,
  QUOTE__ENABLE_SALES_CODE_SELECTOR,
  QUOTE__ENABLE_MULTI_LANGUAGE_PDF_DOWNLOAD,
  QUOTE__ANALYTICS_TAG_ENABLED,
  QUOTE__ENABLE_STOCK_AND_RETAIL_ORDER_TYPES,
  CLICKSTREAM_FORD_PROJECT_KEY,
  CLICKSTREAM_FORD_API_KEY,
  OMS__PLACE_ORDER_TYPE,
  OMS__ENABLE_VEHICLE_BUILDABILITY_VERIFICATION,
  OMS__ENABLE_SALES_CODE_SELECTOR,
  OMS__ANALYTICS_TAG_ENABLED,
  OMS__ENABLE_STOCK_AND_RETAIL_ORDER_TYPES,
  PARTS_TEMPLATIZED_PDF_GENERATION_ENABLED,
  OMS__DROP_SHIP_TYPE,
  WALKME_SCRIPT_IDENTIFIER,
  CRM_BUYER_COBUYER_SWAP_ENABLED,
  UNIVERSAL_GUEST_PROFILE_ENABLED,
  PARTS_EPC_ENABLED,
  PARTS_TRADE_BY_BIN,
  MULTI_INVOICE_FOR_INTERNAL_SO_ENABLED,
  LOGIN_OVERRIDE_PERMISSION,
  PARTS_MULTI_WAREHOUSE_ENABLED,
  PARTS_TAX_CODE_CUSTOMER_TAX_EXEMPT_ENABLED,
  ACCOUNTING_INTER_PREFIX,
  PART_RETURN_SCANNING_ENABLED,
  COMMUNICATION_V2_ENABLED,
  ENTERPRISE_COMMUNICATION_ENABLED,
  VI_OPTIONS_LOOKUP,
  DAY_COLLECTION_ENTERPRISE_V2_ENABLED,
  IS_PHYSICAL_INVENTORY_SCANNING_ENABLED,
  AEC_PLATFROM_DEAL_PUSH_ENABLED,
  LEAD_INFO_V2_ENABLED,
  PARTS_SNAP_ON_PRO_OEM_CODE,
  PARTS_MONITORING_DASHBOARD_ENABLED,
  APPROVAL_WORKFLOW_FOR_CREDIT_LIMIT,
  PARTS_SALES_ORDER_APPROVAL_FLOW_ENABLED,
  IS_NEW_PREFERRED_PART_LIST_ENABLED,
  BULK_SO_CREATION,
  RO_SALES_HEADER_REVAMP_ENABLED: 'RO_SALES_HEADER_REVAMP_ENABLED',
  REMOVE_USER_ROLE_ASSIGN_EMPLOYEE_VALIDATION,
  PARTS_AUTO_INVOICE_ON_FILL_ENABLED,
};

export default FEATURE_NAME;
