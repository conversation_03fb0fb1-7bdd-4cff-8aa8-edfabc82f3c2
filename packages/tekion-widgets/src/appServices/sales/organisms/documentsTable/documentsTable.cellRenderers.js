/* eslint-disable react/prop-types */
import React from 'react';
import cx from 'classnames';
import _map from 'lodash/map';
import _includes from 'lodash/includes';
import _get from 'lodash/get';

import { EMPTY_OBJECT, EMPTY_STRING, NO_DATA } from '@tekion/tekion-base/app.constants';
import {
  UPLOADED_DOCUMENTS_MENU_ACTIONS,
  UPLOADED_DOCUMENTS_ACTION_KEYS,
} from '@tekion/tekion-business/src/constants/sales/documents';
import openWindowInNewTab from '@tekion/tekion-base/utils/openWindowInNewTab';
import commonRoutes from '@tekion/tekion-base/constants/commonRoutes';
import getRoute from '@tekion/tekion-base/factories/routeFactory';
import Label from '@tekion/tekion-components/src/atoms/Label';
import Content from '@tekion/tekion-components/src/atoms/Content';
import FontIcon, { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';
import Tooltip from '@tekion/tekion-components/src/atoms/tooltip';
import InfoBadge from '@tekion/tekion-components/src/molecules/infoBadge';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import { tget } from '@tekion/tekion-base/utils/general';
import Popover, { POPOVER_PLACEMENT, POPOVER_TRIGGER } from '@tekion/tekion-components/src/molecules/popover';
import { getFormattedDateTime, DATE_TIME_FORMATS, getUnix } from '@tekion/tekion-base/utils/dateUtils';
import DealerPropertyHelper from '@tekion/tekion-components/src/helpers/sales/dealerPropertyHelper';
import COLORS from 'tstyles/exports.scss';

import KebabMenuWithActions from './components/kebabMenuWithActions';
import {
  DOCUMENT_SOURCES,
  SOURCE_LABELS,
  OVERLAY_STYLE,
  WARNING_ICON_DOCUMENT_STATUS,
  DOCUMENT_STATUS,
  DOCUMENT_MESSAGES,
  PLACEHOLDER_NOT_APPLICABLE_POPOVER,
  PLACEHOLDER_NOT_APPLICABLE_POPOVER_WITHOUT_TASK_MANAGER,
} from './documentsTable.constants';
import {
  getValidKebabMenuActions,
  getRowActionsMenuDisablePopoverMessages,
  getTooltipMessage,
  shouldDisableDocument,
} from './documentsTable.utils';
import styles from './documentsTable.module.scss';

export const renderWarningPopoverContent = message => <div className={styles.warningTextContainer}>{message}</div>;

const getExpirationWarningIcon = ({ expiryTimeStamp, documentStatus }) => {
  const currentTimeStamp = getUnix();
  const expiryDate = getFormattedDateTime(expiryTimeStamp, DATE_TIME_FORMATS.DATE_MONTH_YEAR);
  let documentMessage;
  switch (documentStatus) {
    case DOCUMENT_STATUS.ABOUT_TO_EXPIRE: {
      if (expiryTimeStamp > currentTimeStamp)
        documentMessage = DOCUMENT_MESSAGES[DOCUMENT_STATUS.ABOUT_TO_EXPIRE]?.(expiryDate);
      else documentMessage = DOCUMENT_MESSAGES[DOCUMENT_STATUS.EXPIRED_BUT_NOT_DELETED]?.(expiryDate);
      break;
    }

    case DOCUMENT_STATUS.ON_HOLD: {
      documentMessage = DOCUMENT_MESSAGES[DOCUMENT_STATUS.ON_HOLD]?.(expiryDate);
      break;
    }
    default:
      documentMessage = EMPTY_STRING;
  }

  return (
    <Popover
      trigger={POPOVER_TRIGGER.HOVER}
      placement={POPOVER_PLACEMENT.BOTTOM_RIGHT}
      content={renderWarningPopoverContent(documentMessage)}
      overlayStyle={OVERLAY_STYLE}
      >
      <FontIcon className={styles.cautionIcon} color={COLORS.carrotOrange} size={SIZES.MD}>
        icon-caution2
      </FontIcon>
    </Popover>
  );
};

export const getNumberOfPagesCell = ({
  pages,
  showEmptyCell,
  piiCheckboxChecked,
  expiryTimeStamp,
  documentStatus,
  archived,
  isVIDocumentUploaded,
  documentPermissions,
  documentCellContainerClassName,
}) => {
  const showWarningIcon = piiCheckboxChecked && _includes(WARNING_ICON_DOCUMENT_STATUS, documentStatus);
  const showPreviewIcon = (() => !showEmptyCell || archived || isVIDocumentUploaded === 'true')();
  const tooltipTitle = getTooltipMessage({ documentPermissions, showEmptyCell, archived, piiCheckboxChecked });
  const isDocumentDisabled = shouldDisableDocument(documentPermissions, piiCheckboxChecked);
  const icon = isDocumentDisabled ? 'icon-hide' : 'icon-eye-outline';

  return (
    <div className={cx('d-flex justify-content-between', documentCellContainerClassName)}>
      <PropertyControlledComponent controllerProperty={showPreviewIcon} fallback={<Content>{NO_DATA}</Content>}>
        <div className={styles.noOfPagesCell}>
          <div className="d-flex">
            <PropertyControlledComponent controllerProperty={!showEmptyCell}>
              <Label>{__('{{numOfPages}} Pages', { numOfPages: pages })}</Label>
            </PropertyControlledComponent>
            <Tooltip title={tooltipTitle}>
              <FontIcon
                size={SIZES.L}
                className={cx('marginL8', {
                  'cursor-pointer': !isDocumentDisabled,
                })}
                disabled={isDocumentDisabled}>
                {icon}
              </FontIcon>
            </Tooltip>
          </div>
        </div>
      </PropertyControlledComponent>
      <PropertyControlledComponent controllerProperty={showWarningIcon}>
        {getExpirationWarningIcon({ expiryTimeStamp, documentStatus })}
      </PropertyControlledComponent>
    </div>
  );
};

export const getActionMenuCell = (disabledDocumentIds, onUploadClick) => ({
  onClick,
  rowData,
  mediaId,
  name,
  sourceDocumentId,
  source,
  documentPermissions,
}) => {
  const rowActionsDisabledPopoverMessages = getRowActionsMenuDisablePopoverMessages(rowData, documentPermissions);
  const actions = _map(getValidKebabMenuActions(UPLOADED_DOCUMENTS_MENU_ACTIONS), action => {
    if (_includes(disabledDocumentIds, sourceDocumentId) || source !== DOCUMENT_SOURCES.STOCK) {
      return {
        ...action,
        disabled: true,
      };
    }
    return {
      ...action,
      ...tget(rowActionsDisabledPopoverMessages, [UPLOADED_DOCUMENTS_ACTION_KEYS[action.key]], EMPTY_OBJECT),
    };
  });

  const handleActionMenuClick = item => {
    if (item?.key === UPLOADED_DOCUMENTS_ACTION_KEYS.REPLACE_UPLOADED_DOCUMENT) {
      onUploadClick(sourceDocumentId);
    } else {
      onClick({
        type: item?.key,
        mediaId,
        fileName: name,
        sourceDocumentId,
        shouldShowModal: false,
      });
    }
  };

  return <KebabMenuWithActions actions={actions} onClick={handleActionMenuClick} />;
};

export const partitionHeader = ({ label }) => (
  <Label className={`d-flex align-items-center ${styles.partitionHeader} `} size="S">
    {label}
  </Label>
);

export const getDocumentNameCell = ({ original, value }) => {
  if (original?.notApplicable && !original?.archived) {
    return (
      <div className={styles.documentName}>
        <Content>{value}</Content>
        <Popover
          content={
            <div className={styles.naPopoverInfo}>
              {DealerPropertyHelper.isTaskManagerTabEnabled()
                ? PLACEHOLDER_NOT_APPLICABLE_POPOVER
                : PLACEHOLDER_NOT_APPLICABLE_POPOVER_WITHOUT_TASK_MANAGER}
            </div>
          }
          placement={POPOVER_PLACEMENT.TOP}
          trigger={POPOVER_TRIGGER.HOVER}>
          <div className={styles.naContainer}>{__('NA')}</div>
        </Popover>
      </div>
    );
  }
  if (original?.archived) {
    return (
      <div className={styles.documentName}>
        <Content>{value}</Content>
        <div className={styles.archiveBadge}>{__('Removed')}</div>
      </div>
    );
  }
  if (original?.source === DOCUMENT_SOURCES.STOCK && _get(original, 'mediaId')) {
    return (
      <div className={styles.documentName}>
        <Content>{value}</Content>
        <div className={styles.uploadedBadge}>{__('Uploaded')}</div>
      </div>
    );
  }
  return <Content>{value}</Content>;
};

export const getDocumentSourceCell = ({ original, value }) => {
  const formattedValue = SOURCE_LABELS[value];
  if (original?.source === DOCUMENT_SOURCES.STOCK) return formattedValue;
  const route = getRoute(commonRoutes.DEAL, original?.assetId);
  const InfoContent = (
    <div className={`d-flex justify-content-center align-items-center ${styles.dealPopover}`}>
      <Content className={styles.sourcePopover}>{__(`Deal# {{dealId}}`, { dealId: original.assetId })}</Content>
      <FontIcon onClick={() => openWindowInNewTab(route)} size={SIZES.S} className="marginL8 cursor-pointer">
        icon-redirect
      </FontIcon>
    </div>
  );
  return (
    <div className="d-flex">
      <Label>{formattedValue}</Label>
      <InfoBadge infoIconSize="s" infoBadgeClassName="m-l-8" helpText={InfoContent} />
    </div>
  );
};
