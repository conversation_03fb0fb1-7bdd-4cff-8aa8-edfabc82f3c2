import React from 'react';
import cx from 'classnames';
import PropTypes from 'prop-types';

import _noop from 'lodash/noop';

import { EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import Heading from '@tekion/tekion-components/src/atoms/Heading';
import InfoBadge from '@tekion/tekion-components/src/molecules/infoBadge';
import Switch from '@tekion/tekion-components/src/molecules/Switch';

const TableHeader = ({ styles, onArchiveToggle, totalArchivedDocs, documentHeaderContainerClassName }) => (
  <div className={cx(styles.tableHeaderContent, 'd-flex', 'align-items-center', documentHeaderContainerClassName)}>
    <div className="full-width d-flex justify-content-between">
      <div className="d-flex align-items-center">
        <Heading size={2} className={styles.heading}>
          {__('Vehicle Documents')}
        </Heading>
        <InfoBadge
          infoIconSize="L"
          infoBadgeClassName="m-l-8"
          helpText={__('This section provides a comprehensive view of all your vehicle-related documents.')}
        />
      </div>
      <div className="d-flex align-items-center">
        <div className="m-r-8">{__('Show Removed Documents ({{totalArchivedDocs}})', { totalArchivedDocs })}</div>
        <Switch size="small" onChange={onArchiveToggle} />
      </div>
    </div>
  </div>
);

TableHeader.propTypes = {
  styles: PropTypes.object.isRequired,
  onArchiveToggle: PropTypes.func,
  totalArchivedDocs: PropTypes.number,
  documentHeaderContainerClassName: PropTypes.string,
};

TableHeader.defaultProps = {
  onArchiveToggle: _noop,
  totalArchivedDocs: 0,
  documentHeaderContainerClassName: EMPTY_STRING,
};

export default React.memo(TableHeader);
