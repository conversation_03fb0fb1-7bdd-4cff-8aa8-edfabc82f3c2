import React, { useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';

import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _noop from 'lodash/noop';

import { EMPTY_ARRAY, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import Button from '@tekion/tekion-components/src/atoms/Button';
import Divider from '@tekion/tekion-components/src/atoms/Divider';
import FontIcon, { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';
import Popover, { POPOVER_TRIGGER, POPOVER_PLACEMENT } from '@tekion/tekion-components/src/molecules/popover';
import ExpandableSearch from '@tekion/tekion-components/src/organisms/ExpandableSearch';
import Tooltip from '@tekion/tekion-components/src/atoms/tooltip/Tooltip';

import DocumentsViewSwitch from './documentsViewSwitch';
import { ACTION_MENU_ITEMS, BULK_ACTION_MENU_SECTIONS } from '../../documentsTable.constants';
import { getBulkActionMenuConfig } from '../../documentsTable.config';
import { getDisabledAndPopoverInfoForActions } from '../../documentsTable.utils';
import ActionMenuWithSections from '../../../../../../organisms/actionMenuWithSections/ActionMenuWithSections';

const DocumentsRightHeader = ({
  styles,
  onSearch,
  onDocumentViewChange,
  selectedView,
  selectedFormIds,
  onAction,
  visibilityConfig,
  onUpload,
  onAddPlaceHolder,
  documents,
  documentPermissions,
  documentHeaderContainerClassName,
}) => {
  const handleActionSelect = useCallback(
    type => {
      onAction({
        type,
        payload: {
          isBulkOperation: true,
        },
      });
    },
    [onAction]
  );

  const handlePrint = useCallback(() => {
    onAction({
      type: ACTION_MENU_ITEMS.PRINT,
    });
  }, [onAction]);

  const handleOnUploadClick = useCallback(() => {
    onUpload();
  }, [onUpload]);

  const bulkMenuItemConfig = useMemo(
    () =>
      getBulkActionMenuConfig({
        actionsDisabledAndPopoverMessages: getDisabledAndPopoverInfoForActions({
          selectedFormIds,
          formsToBeShown: documents,
          documentPermissions,
        }),
      }),
    [selectedFormIds, documents, documentPermissions]
  );

  const addPlaceHolderTooltipMessage = useMemo(
    () =>
      !_get(documentPermissions, 'ADD_PLACEHOLDER') ? __('You are not authorized to add placeholder') : EMPTY_STRING,
    [documentPermissions]
  );

  return (
    <div className={cx(styles.actionsContainer, documentHeaderContainerClassName)}>
      <div className="p-r-20">
        <div className="d-flex flex-row align-items-center">
          <ExpandableSearch className={styles.expandableSearch} onChange={onSearch} />
          <Divider type="vertical" />
          <DocumentsViewSwitch key="DOCUMENTS_VIEW" onChange={onDocumentViewChange} selected={selectedView} />

          <Divider type="vertical" className="m-16" />
          <Popover
            content={!visibilityConfig.shouldEdit && <div className="p-8">{__('Bulk Upload Documents')}</div>}
            trigger={POPOVER_TRIGGER.HOVER}
            placement={POPOVER_PLACEMENT.TOP}
          >
            <Button
              className={cx(styles.addButton, 'marginR8')}
              onClick={handleOnUploadClick}
              view={Button.VIEW.ICON}
              disabled={!visibilityConfig.shouldEdit}
            >
              <FontIcon size={SIZES.S}>icon-upload</FontIcon>
            </Button>
          </Popover>
          <Tooltip title={addPlaceHolderTooltipMessage}>
            <Button
              className={cx(styles.addButton, 'marginR8')}
              view={Button.VIEW.ICON}
              onClick={onAddPlaceHolder(true)}
              disabled={!_get(documentPermissions, 'ADD_PLACEHOLDER') || !visibilityConfig.shouldEdit}
            >
              <FontIcon size={SIZES.S}>icon-add</FontIcon>
            </Button>
          </Tooltip>

          <ActionMenuWithSections
            sections={BULK_ACTION_MENU_SECTIONS}
            onAction={handleActionSelect}
            menuItemsConfig={bulkMenuItemConfig}
            triggerElement={
              <Popover trigger="hover">
                <Button className="d-flex align-items-center m-x-16" disabled={_isEmpty(selectedFormIds)}>
                  {__('Actions')}
                  <FontIcon size={SIZES.S} className="marginL8">
                    icon-chevron-down
                  </FontIcon>
                </Button>
              </Popover>
            }
            trigger="click"
            placement="bottomRight"
            disabled={_isEmpty(selectedFormIds)}
          />
          <Button
            onClick={handlePrint}
            view={Button.VIEW.SECONDARY}
            className="m-8"
            disabled={_isEmpty(selectedFormIds)}
          >
            {__('Print')}
          </Button>
        </div>
      </div>
    </div>
  );
};

DocumentsRightHeader.propTypes = {
  onDocumentViewChange: PropTypes.func,
  selectedView: PropTypes.string,
  selectedFormIds: PropTypes.array,
  onAction: PropTypes.func.isRequired,
  visibilityConfig: PropTypes.bool.isRequired,
  onUpload: PropTypes.func.isRequired,
  onAddPlaceHolder: PropTypes.func.isRequired,
  documentHeaderContainerClassName: PropTypes.string,
};

DocumentsRightHeader.defaultProps = {
  onDocumentViewChange: _noop,
  selectedView: EMPTY_STRING,
  selectedFormIds: EMPTY_ARRAY,
  documentHeaderContainerClassName: EMPTY_STRING,
};

export default React.memo(DocumentsRightHeader);
