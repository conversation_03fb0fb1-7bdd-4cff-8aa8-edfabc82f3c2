import React, { useState, useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import _map from 'lodash/map';
import _find from 'lodash/find';
import _noop from 'lodash/noop';
import _isEmpty from 'lodash/isEmpty';
import _includes from 'lodash/includes';
import _head from 'lodash/head';
import _get from 'lodash/get';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { tget } from '@tekion/tekion-base/utils/general';
import { getSignedURLs } from '@tekion/tekion-business/src/services/mediaV3/mediaV3.services';
import { SUPPORTED_ROLES } from '@tekion/tekion-business/src/constants/sales/documents';
import { Content, FontIcon, Heading } from '@tekion/tekion-components/src/atoms';
import { useTekionConversion } from '@tekion/tekion-conversion-web';
import Loader from '@tekion/tekion-components/src/molecules/loader';
import Modal from '@tekion/tekion-components/src/molecules/Modal';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import Portal from '@tekion/tekion-components/src/molecules/Portal';
import SortableTable from '@tekion/tekion-components/src/organisms/SortableTableWithPartition';
import StickyBanner from '@tekion/tekion-components/src/molecules/stickyBanner';
import COLORS from '@tekion/tekion-styles-next/scss/exports.scss';

import VersionHistoryAPI from '../../../../organisms/vi/services/versionHistory.api';
import DocumentUploadModalWithoutLazyLoad from './DocumentUploadModalWithoutLazyLoad';
import Attachment from '../../../../molecules/Attachment';
import DocumentsPreview from '../../../../organisms/DocumentPreviewDrawer';
import { ACCEPTED_FILE_EXTENSIONS } from '../../../../organisms/DocumentUploadModal/documentUploadModal.constants';
import { DOCUMENT_SOURCES, DOCUMENTS_VIEW_CONFIGURATIONS, PERMISSION_MAP } from './documentsTable.constants';
import { getColumnConfig } from './documentsTable.config';
import {
  getDocumentsCategoryPartitions,
  getBulkUploadModalViewConfigurations,
  getShowCurrentVersion,
} from './documentsTable.helper';
import styles from './documentsTable.module.scss';
import TableHeader from './components/TableHeader';
import DocumentsRightHeader from './components/documentsRightHeader/DocumentsRightHeader';
import useAddPlaceholder from './useAddPlaceholder';
import AddPlaceholder from '../../../../organisms/addPlaceholder/AddPlaceholder';

const DocumentsTable = ({
  loading,
  documents,
  customFormCategories,
  selectedItems,
  onAction,
  selectedView,
  visibilityConfig,
  onDocumentViewChange,
  selectedFormIds,
  showConfirmationModal,
  documentIdToBeDeleted,
  onConfirmationCancel,
  onConfirm,
  confirmationLoading,
  onTableItemSelect,
  onColumnSort,
  onRowUpdate,
  hideRowDrag,
  onSearch,
  showUploadScreen,
  onUploadBackIconClick,
  formCategories,
  documentsToBeMapped,
  onDocumentsSave,
  remainders,
  sortableContClassName,
  disabledDocumentIds,
  preSelectedDocumentIdToMap,
  onAddPlaceHolder,
  placeHolders,
  documentPermissions,
  filePickerRef,
  onUploadClick,
  onUploadDocuments,
  files,
  onArchiveToggle,
  totalArchivedDocs,
  onPrint,
  documentHeaderContainerClassName,
  documentCellContainerClassName,
}) => {
  const [showDocumentPreview, setShowDocumentPreview] = useState(false);
  const [selectedPreviewDocument, setSelectedPreviewDocument] = useState(EMPTY_OBJECT);
  const [showAddPlaceHolderModal, setShowAddPlaceHolderModal] = useState(false);
  const [versionHistory, setVersionHistory] = useState(EMPTY_ARRAY);

  const { getFormattedDateAndTime } = useTekionConversion();

  const additionalTableProps = useMemo(
    () => getDocumentsCategoryPartitions(customFormCategories, selectedView, documents),
    [selectedView, customFormCategories, documents]
  );

  const handleActionMenuClick = useCallback(
    ({ type, ...rest }) => {
      onAction({
        type,
        payload: rest,
      });
    },
    [onAction]
  );

  const getVersionMediaID = async selectedDoc => {
    const versionHistoryPayload = {
      assetId: selectedDoc?.assetId,
      sourceDocumentId: selectedDoc?.sourceDocumentId,
    };
    const versionHistoryData = await VersionHistoryAPI.fetchVersionHistory(versionHistoryPayload);
    setVersionHistory(versionHistoryData);
    return _head(versionHistoryData)?.mediaId;
  };

  const handleViewDocument = useCallback(
    async sourceDocumentId => {
      const selectedDoc = _find(documentsToBeMapped.allDocuments, { sourceDocumentId });
      if (!selectedDoc?.archived && _includes(disabledDocumentIds, sourceDocumentId)) return;
      const versionMediaId = await getVersionMediaID(selectedDoc);
      const mediaId = selectedDoc?.mediaId || versionMediaId;
      if (mediaId) {
        setShowDocumentPreview(true);
        const data = await getSignedURLs([mediaId]);
        setSelectedPreviewDocument({
          previewUrl: tget(data, [0, 'normal', 'url'], ''),
          document: selectedDoc,
        });
      }
    },
    [setShowDocumentPreview, documentsToBeMapped, disabledDocumentIds]
  );

  const handleSwitchDocument = useCallback(
    ({ sourceDocumentId }) => {
      handleViewDocument(sourceDocumentId);
    },
    [handleViewDocument]
  );

  const handleViewSelectedVersion = useCallback(
    async version => {
      setShowDocumentPreview(true);
      try {
        const data = await getSignedURLs([version?.mediaId]);
        setSelectedPreviewDocument({
          previewUrl: tget(data, [0, 'normal', 'url'], ''),
          document: selectedPreviewDocument?.document,
        });
      } catch {
        setSelectedPreviewDocument(EMPTY_OBJECT);
      }
    },
    [setShowDocumentPreview, selectedPreviewDocument]
  );

  const handleClosePreview = useCallback(() => {
    setSelectedPreviewDocument(EMPTY_OBJECT);
    setShowDocumentPreview(false);
  }, [setShowDocumentPreview, setSelectedPreviewDocument]);

  const columnConfig = useMemo(
    () =>
      getColumnConfig({
        handleActionMenuClick,
        handleViewDocument,
        customFormCategories,
        disabledDocumentIds,
        getFormattedDateAndTime,
        documentPermissions,
        onUploadClick,
        documentCellContainerClassName,
      }),
    [
      handleActionMenuClick,
      handleViewDocument,
      customFormCategories,
      disabledDocumentIds,
      getFormattedDateAndTime,
      documentPermissions,
      onUploadClick,
      documentCellContainerClassName,
    ]
  );

  const handleAddPlaceHolderModalVisibility = shouldShow => () => {
    setShowAddPlaceHolderModal(shouldShow);
  };

  const modalContent = useMemo(() => {
    if (documentIdToBeDeleted) {
      return <Content>{__('Are you sure you want to remove the placeholder(s)?')}</Content>;
    }
    const selectedDocuments = _map(selectedFormIds, formId => _find(documents, { sourceDocumentId: formId }));
    return (
      <div>
        <div className={styles.cautionBanner}>
          <FontIcon> icon-caution </FontIcon>
          <Content colorVariant={Content.COLOR_VARIANTS.GREY} className="m-l-16">
            {__('Are you sure you want to remove the placeholder(s)?')}
          </Content>
        </div>
        <Heading className="m-t-16" size={4}>
          {__('Selected Documents')}{' '}
        </Heading>
        <div className="m-t-8">
          {_map(selectedDocuments, document => (
            <div key={document.name} className={styles.selectedDocuments}>
              <Content>{document.name}</Content>
              <PropertyControlledComponent controllerProperty={document.source !== DOCUMENT_SOURCES.STOCK}>
                <Content className="d-flex" colorVariant={Content.COLOR_VARIANTS.GREY}>
                  <FontIcon className="m-r-8" color={COLORS.carrotOrange}>
                    icon-caution
                  </FontIcon>
                  {__('Cannot Delete Document not Generated in VI')}
                </Content>
              </PropertyControlledComponent>
            </div>
          ))}
        </div>
      </div>
    );
  }, [documentIdToBeDeleted, documents, selectedFormIds]);

  const isBulkCheckboxVisible = useMemo(() => _isEmpty(documents), [documents]);

  const handleSubmit = useCallback(
    () => onConfirm({ isBulkOperation: !documentIdToBeDeleted }),
    [onConfirm, documentIdToBeDeleted]
  );

  const { placeholderOptions, isAnyPlaceholderAlreadyAdded } = useAddPlaceholder({
    documents: placeHolders,
    allDocuments: documents,
  });

  const handleOnAddPlaceHolder = useMemo(
    () => selectedPlaceHolder => {
      setShowAddPlaceHolderModal(false);
      onAddPlaceHolder(selectedPlaceHolder);
    },
    [onAddPlaceHolder]
  );

  const documentVersionHistoryConfig = useMemo(
    () => ({
      versionHistory,
      onSelectVersion: handleViewSelectedVersion,
    }),
    [versionHistory, handleViewSelectedVersion]
  );

  const handleRowClick = useMemo(
    () => rowData => {
      if (
        !_get(rowData, ['original', 'containsPii']) ||
        _get(documentPermissions, PERMISSION_MAP.VIEW_MANAGE_SENSITIVE_DOCUMENT)
      ) {
        const sourceDocumentId = tget(rowData, ['original', 'sourceDocumentId'], EMPTY_STRING);
        handleViewDocument(sourceDocumentId);
      }
    },
    [handleViewDocument, documentPermissions]
  );

  const showCurrentVersion = useMemo(() => getShowCurrentVersion(selectedPreviewDocument), [selectedPreviewDocument]);

  return (
    <div className={styles.tableContainer}>
      <DocumentsRightHeader
        selectedFormIds={selectedFormIds}
        onDocumentViewChange={onDocumentViewChange}
        selectedView={selectedView}
        onAction={onAction}
        visibilityConfig={visibilityConfig}
        styles={styles}
        onSearch={onSearch}
        onUpload={onUploadDocuments}
        onAddPlaceHolder={handleAddPlaceHolderModalVisibility}
        documents={documents}
        documentPermissions={documentPermissions}
        documentHeaderContainerClassName={documentHeaderContainerClassName}
      />

      <div className="m-24">
        <TableHeader
          styles={styles}
          visibilityConfig={visibilityConfig}
          onUpload={onUploadDocuments}
          onArchiveToggle={onArchiveToggle}
          totalArchivedDocs={totalArchivedDocs}
          documentHeaderContainerClassName={documentHeaderContainerClassName}
        />
        <PropertyControlledComponent controllerProperty={!_isEmpty(remainders)}>
          <div className={styles.bannerContainer}>
            <StickyBanner
              showDismiss={false}
              shouldAllowOwnBannerType
              reminders={remainders}
              bannerStyle={styles.bannerStyle}
            />
          </div>
        </PropertyControlledComponent>

        <Modal
          width={Modal.SIZES.MD}
          visible={showConfirmationModal}
          title={__('Remove Placeholder(s)')}
          content={modalContent}
          submitBtnText={__('Remove')}
          onCancel={onConfirmationCancel}
          onSubmit={handleSubmit}
          loading={confirmationLoading}
        />
        <div className={styles.sortableContainer}>
          {loading ? (
            <Loader className="full-width full-height align-center" />
          ) : (
            <SortableTable
              withCheckbox
              data={documents}
              selection={selectedItems}
              disabled={disabledDocumentIds}
              columns={columnConfig}
              onSelect={onTableItemSelect}
              columnCheckBoxClassName="p-0"
              noPartitionSelector
              noHeadSelector={isBulkCheckboxVisible}
              keyField="sourceDocumentId"
              sortableAccessor="sortableAccessor"
              onColumnSort={onColumnSort}
              onRowUpdate={onRowUpdate}
              hideRowDrag={hideRowDrag}
              sortableContClassName={sortableContClassName}
              onRowClick={handleRowClick}
              {...additionalTableProps}
            />
          )}
        </div>
      </div>
      <PropertyControlledComponent controllerProperty={showUploadScreen}>
        <Portal className="zIndex3">
          <DocumentUploadModalWithoutLazyLoad
            onClose={onUploadBackIconClick}
            formCategories={formCategories}
            requestedDocuments={documentsToBeMapped.requestedDocuments}
            onSave={onDocumentsSave}
            preSelectedDocumentIdToMap={preSelectedDocumentIdToMap}
            viewConfigurations={getBulkUploadModalViewConfigurations(documentPermissions)}
            preselectedFilesToUpload={files}
          />
        </Portal>
      </PropertyControlledComponent>
      <DocumentsPreview
        previewURL={selectedPreviewDocument?.previewUrl}
        selectedDocument={selectedPreviewDocument?.document}
        closePreviewDrawer={handleClosePreview}
        openPreview={showDocumentPreview}
        documentsList={documentsToBeMapped.allDocuments}
        onDocumentSwitch={handleSwitchDocument}
        viewConfigurations={DOCUMENTS_VIEW_CONFIGURATIONS.preview}
        pdfAnnotationProps={DOCUMENTS_VIEW_CONFIGURATIONS.pdf}
        documentVersionHistoryConfig={documentVersionHistoryConfig}
        onPrint={onPrint}
        showCurrentVersion={showCurrentVersion}
      />
      <AddPlaceholder
        visible={showAddPlaceHolderModal}
        onSubmit={handleOnAddPlaceHolder}
        documents={placeHolders}
        onCancel={handleAddPlaceHolderModalVisibility(false)}
        title={__('Add Placeholder')}
        userRole={SUPPORTED_ROLES.DEALER}
        label={__('Select PlaceHolder')}
        placeholderOptions={placeholderOptions}
        isAnyPlaceholderAlreadyAdded={isAnyPlaceholderAlreadyAdded}
        infoMessage={isAnyPlaceholderAlreadyAdded && __('Disabled items are already added')}
        hideSpecifications
      />
      <Attachment
        ref={filePickerRef}
        multiple
        accept={ACCEPTED_FILE_EXTENSIONS}
        onSelectFiles={onUploadDocuments}
        className={styles.attachment}
      />
    </div>
  );
};

DocumentsTable.propTypes = {
  documents: PropTypes.array,
  columns: PropTypes.object,
  customFormCategories: PropTypes.object,
  onFormSelection: PropTypes.func,
  selectedItems: PropTypes.array.isRequired,
  onAction: PropTypes.func.isRequired,
  loading: PropTypes.bool.isRequired,
  selectedView: PropTypes.string.isRequired,
  visibilityConfig: PropTypes.bool.isRequired,
  onActionClick: PropTypes.bool.isRequired,
  selectedFormIds: PropTypes.array,
  onDocumentViewChange: PropTypes.func,
  showConfirmationModal: PropTypes.bool,
  documentIdToBeDeleted: PropTypes.func,
  onConfirmationCancel: PropTypes.func,
  onConfirm: PropTypes.func,
  confirmationLoading: PropTypes.bool,
  onTableItemSelect: PropTypes.func,
  onColumnSort: PropTypes.func,
  onRowUpdate: PropTypes.func,
  hideRowDrag: PropTypes.bool,
  onSearch: PropTypes.func,
  showUploadScreen: PropTypes.bool,
  onUploadBackIconClick: PropTypes.func,
  formCategories: PropTypes.array,
  documentsToBeMapped: PropTypes.object,
  onDocumentsSave: PropTypes.func,
  remainders: PropTypes.array,
  sortableContClassName: PropTypes.string,
  disabledDocumentIds: PropTypes.array,
  preSelectedDocumentIdToMap: PropTypes.string,
  documentPermissions: PropTypes.object.isRequired,
  files: PropTypes.array,
  onUploadDocuments: PropTypes.func,
  onUploadClick: PropTypes.func,
  filePickerRef: PropTypes.object,
  onArchiveToggle: PropTypes.func,
  totalArchivedDocs: PropTypes.number,
  onPrint: PropTypes.func,
  documentHeaderContainerClassName: PropTypes.string,
  documentCellContainerClassName: PropTypes.string,
};

DocumentsTable.defaultProps = {
  documents: EMPTY_ARRAY,
  columns: EMPTY_OBJECT,
  customFormCategories: EMPTY_OBJECT,
  onFormSelection: _noop,
  onDocumentViewChange: _noop,
  showConfirmationModal: false,
  documentIdToBeDeleted: EMPTY_STRING,
  onConfirmationCancel: _noop,
  onConfirm: _noop,
  confirmationLoading: false,
  onTableItemSelect: _noop,
  onColumnSort: _noop,
  onRowUpdate: _noop,
  hideRowDrag: false,
  onSearch: _noop,
  selectedFormIds: EMPTY_ARRAY,
  showUploadScreen: false,
  onUploadBackIconClick: _noop,
  formCategories: EMPTY_ARRAY,
  documentsToBeMapped: EMPTY_OBJECT,
  onDocumentsSave: _noop,
  remainders: EMPTY_ARRAY,
  sortableContClassName: EMPTY_STRING,
  disabledDocumentIds: EMPTY_ARRAY,
  preSelectedDocumentIdToMap: EMPTY_STRING,
  files: EMPTY_ARRAY,
  onUploadDocuments: _noop,
  onUploadClick: _noop,
  filePickerRef: EMPTY_OBJECT,
  onArchiveToggle: _noop,
  totalArchivedDocs: 0,
  onPrint: _noop,
  documentHeaderContainerClassName: EMPTY_STRING,
  documentCellContainerClassName: EMPTY_STRING,
};

export default React.memo(DocumentsTable);
