import { mapProps } from 'recompose';
import { defaultMemoize } from 'reselect';

import _get from 'lodash/get';

import IconWithText from '@tekion/tekion-components/src/atoms/IconWithText';
import { tget } from '@tekion/tekion-base/utils/general';
import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import {
  getNumberOfPagesCell,
  getActionMenuCell,
  getDocumentSourceCell,
  getDocumentNameCell,
} from './documentsTable.cellRenderers';
import NextActionCell from './components/NextActionCell';
import { getCategoryCellValue } from './documentsTable.helper';
import { TABLE_COLUMNS, ACTION_MENU_ITEMS } from './documentsTable.constants';
import { isDocumentUploadedAgainstPlaceholder } from './documentsTable.utils';
import LastActivity from '../../../../organisms/LastActivity/LastActivity';

export const getColumnConfig = ({
  handleActionMenuClick,
  customFormCategories,
  disabledDocumentIds,
  getFormattedDateAndTime,
  documentPermissions,
  onUploadClick,
  documentCellContainerClassName,
}) => [
  {
    id: TABLE_COLUMNS.NAME,
    Header: __('Name'),
    accessor: 'name',
    width: 400,
    sortable: true,
    Cell: getDocumentNameCell,
  },
  {
    id: TABLE_COLUMNS.NEXT_ACTION,
    Header: __('NextAction'),
    accessor: 'nextAction',
    width: 200,
    Cell: mapProps(({ original }) => ({
      showNextAction: !_get(original, 'mediaId'),
      icon: 'icon-upload',
      label: __('Upload'),
      onClick: onUploadClick,
      preSelectedDocumentIdToMap: original?.sourceDocumentId,
      isDisabled: original?.isDisabled,
    }))(NextActionCell),
  },
  {
    id: TABLE_COLUMNS.SOURCE,
    Header: __('Source'),
    accessor: 'source',
    width: 180,
    Cell: getDocumentSourceCell,
  },
  {
    id: TABLE_COLUMNS.CATEGORY,
    Header: __('Category'),
    accessor: 'category',
    width: 180,
    Cell: getCategoryCellValue(customFormCategories),
  },
  {
    id: TABLE_COLUMNS.DOCUMENT,
    Header: __('Document'),
    accessor: 'noOfPages',
    width: 250,

    Cell: mapProps(({ original }) => ({
      pages: original?.noOfPages,
      showEmptyCell: !isDocumentUploadedAgainstPlaceholder(original),
      documentStatus: tget(original, ['additionalAttributes', 'dormantAccountStatus']),
      piiCheckboxChecked: original?.containsPii,
      expiryTimeStamp: original?.expiryTimestamp,
      archived: original?.archived,
      isVIDocumentUploaded: tget(original, 'additionalAttributes.isVIDocumentUploaded'),
      documentPermissions,
      documentCellContainerClassName,
    }))(getNumberOfPagesCell),
  },
  {
    id: TABLE_COLUMNS.LAST_ACTIVITY,
    Header: __('Last Activity'),
    accessor: 'lastActivity',
    width: 320,
    Cell: mapProps(({ original }) => ({
      formActivityLog: _get(original, 'formActivityLog'),
      getFormattedDateAndTime,
    }))(LastActivity),
  },
  {
    id: TABLE_COLUMNS.MENU,
    Header: '',
    width: 40,
    Cell: mapProps(({ original }) => ({
      onClick: handleActionMenuClick,
      rowData: original,
      documentPermissions,
      ...original,
    }))(getActionMenuCell(disabledDocumentIds, onUploadClick)),
  },
];

const REMOVE_PLACEHOLDER = {
  key: ACTION_MENU_ITEMS.REMOVE_PLACEHOLDER,
  Component: IconWithText,
  renderOptions: {
    icon: 'icon-close',
    textContent: __('Remove Placeholder(s)'),
  },
};

const REMOVE_UPLOADED_DOCUMENT = {
  key: ACTION_MENU_ITEMS.REMOVE_UPLOADED_DOCUMENT,
  Component: IconWithText,
  renderOptions: {
    icon: 'icon-close',
    textContent: __('Remove Uploaded Document(s)'),
  },
};

const DOWNLOAD = {
  key: ACTION_MENU_ITEMS.DOWNLOAD,
  Component: IconWithText,
  renderOptions: {
    icon: 'icon-download1',
    textContent: __('Download'),
  },
};

const MERGE_AND_DOWNLOAD = {
  key: ACTION_MENU_ITEMS.MERGE_AND_DOWNLOAD,
  Component: IconWithText,
  renderOptions: {
    icon: 'icon-download1',
    textContent: __('Merge & Download'),
  },
};

export const getBulkActionMenuConfig = defaultMemoize(({ actionsDisabledAndPopoverMessages }) => ({
  [ACTION_MENU_ITEMS.REMOVE_PLACEHOLDER]: {
    ...REMOVE_PLACEHOLDER,
    ...tget(actionsDisabledAndPopoverMessages, ACTION_MENU_ITEMS.REMOVE_PLACEHOLDER, EMPTY_OBJECT),
  },
  [ACTION_MENU_ITEMS.REMOVE_UPLOADED_DOCUMENT]: {
    ...REMOVE_UPLOADED_DOCUMENT,
    ...tget(actionsDisabledAndPopoverMessages, ACTION_MENU_ITEMS.REMOVE_UPLOADED_DOCUMENT, EMPTY_OBJECT),
  },
  [ACTION_MENU_ITEMS.DOWNLOAD]: {
    ...DOWNLOAD,
    ...tget(actionsDisabledAndPopoverMessages, ACTION_MENU_ITEMS.DOWNLOAD, EMPTY_OBJECT),
  },
  [ACTION_MENU_ITEMS.MERGE_AND_DOWNLOAD]: {
    ...MERGE_AND_DOWNLOAD,
    ...tget(actionsDisabledAndPopoverMessages, ACTION_MENU_ITEMS.MERGE_AND_DOWNLOAD, EMPTY_OBJECT),
  },
}));
