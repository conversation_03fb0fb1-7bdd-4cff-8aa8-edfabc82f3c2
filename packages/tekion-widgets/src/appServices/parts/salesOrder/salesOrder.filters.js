import produce from 'immer';

import _set from 'lodash/set';
import _get from 'lodash/get';
import _keys from 'lodash/keys';
import _compact from 'lodash/compact';
import _without from 'lodash/without';
import _property from 'lodash/property';
import _uniq from 'lodash/uniq';
import _map from 'lodash/map';

import { getStartEpoch } from '@tekion/tekion-base/utils/dateUtils';
import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import CashieringSettingsReader from '@tekion/tekion-base/readers/CashieringSettings';
import PaymentModeConfigReader from '@tekion/tekion-base/readers/CashieringSettings/paymentModesConfig';
import { COLLECT_PAYMENT_TYPES_CONFIG } from '@tekion/tekion-widgets/src/organisms/TransactionList/Transaction.constants';
import {
  IN,
  GREATER_THAN,
  LESS_THAN,
  EQUALS,
  BTW,
} from '@tekion/tekion-components/src/organisms/filterSection/constants/filterSection.operators';
import FILTER_TYPES from '@tekion/tekion-components/src/organisms/filterSection/constants/filterSection.filterTypes';
import { getDealerSitesFilter } from '@tekion/tekion-business/src/appServices/parts/helpers/dealerSiteFilter.helpers';
import OPERATORS from '@tekion/tekion-base/constants/filterOperators';
import { getFilterOptions, addOptionsToAdditional } from '@tekion/tekion-base/formatters/filterDataFormatter';
import { PART_SALES_ORDER_ASSET_TYPE } from '@tekion/tekion-base/constants/parts/general';
import {
  PAID,
  UNPAID,
  PARTIALLY_PAID,
  INTERNAL,
  RETAIL,
  WHOLESALE,
  DRAFT,
  INVOICED,
  DELIVERED,
  VOIDED,
  PARTIALLY_DELIVERED,
  CLOSED,
  REOPENED,
} from '@tekion/tekion-base/constants/parts/salesOrderFilters';

import { PARTS_PERSONA_FILTER_CONFIG } from '../helpers/filterConfigs';
import { DELIVERY_METHOD_OPTIONS } from '../constants/deliveryMethods';

import { getSaleTypeOptions } from './salesOrder.helpers';

import { getPaymentTypeFilterOptions } from '../utils/filterUtils';
import { createFilter } from '../helpers/filters';

export const SALES_ORDER_LIST_VIEW_FILTER_KEY = {
  CREATED_TIME: 'createdTime',
  SALE_TYPE: 'saleSubTypeId',
  PAYMENT_STATUS: 'paymentStatus',
  SALE_STATUS: 'status',
  ON_ORDER: 'onOrdered',
  RETURN: 'returnDetails',
  SOR_REQUEST: 'sorRequestIds',
  COUNTER_PERSON: 'userId',
  SOR_RECEIVED: 'purchaseOrderHoldExists',
  ON_HOLD: 'onHold',
  ACTION_PENDING: 'pendingAction',
  ETA: 'estimatedArrivalTime',
  UNPAID_SO: 'unpaidSO',
  OPEN_QUOTES: 'openQuotes',
  SITE_ID: 'siteId',
  DEPARTMENT: 'departmentId',
  PREPAID_ORDERS: 'prepaidOrderExists',
  PREPAID_HOLD: 'prepaidHoldExists',
  AUTO_FILL: 'autoFill',
  ROUTE_NUMBER: 'routeNumber',
};

const PAYMENT_STATUS_FILTER_TYPES = {
  [PAID.id]: PAID,
  [UNPAID.id]: UNPAID,
  [PARTIALLY_PAID.id]: PARTIALLY_PAID,
};

const SALE_TYPE_FILTER_TYPES = {
  [INTERNAL.id]: INTERNAL,
  [RETAIL.id]: RETAIL,
  [WHOLESALE.id]: WHOLESALE,
};

export const SALE_STATUS_FILTER_TYPES = {
  [DRAFT.id]: DRAFT,
  [INVOICED.id]: INVOICED,
  [DELIVERED.id]: DELIVERED,
  [VOIDED.id]: VOIDED,
  [PARTIALLY_DELIVERED.id]: PARTIALLY_DELIVERED,
  [CLOSED.id]: CLOSED,
  [REOPENED.id]: REOPENED,
};

export const SO_DATE_FILTER = {
  id: SALES_ORDER_LIST_VIEW_FILTER_KEY.CREATED_TIME,
  name: __('Date'),
  type: FILTER_TYPES.DATE_WITHOUT_PRESET,
};

const SALE_TYPE_FILTER = {
  id: SALES_ORDER_LIST_VIEW_FILTER_KEY.SALE_TYPE,
  name: __('Sale Type'),
  type: FILTER_TYPES.MULTI_SELECT,
  additional: {
    options: getFilterOptions('SALE_TYPE_FILTER', SALE_TYPE_FILTER_TYPES),
  },
};

const PAYMENT_STATUS_FILTER = {
  id: SALES_ORDER_LIST_VIEW_FILTER_KEY.PAYMENT_STATUS,
  name: __('Payment Status'),
  type: FILTER_TYPES.MULTI_SELECT,
  additional: {},
};

export const SALE_STATUS_FILTER = {
  id: SALES_ORDER_LIST_VIEW_FILTER_KEY.SALE_STATUS,
  name: __('Sale Status'),
  type: FILTER_TYPES.MULTI_SELECT,
  additional: {},
};

export const getUserFilter = title => ({
  ...PARTS_PERSONA_FILTER_CONFIG,
  name: __('Counter Person'),
  ...(title && { name: title }),
});

export const getDepartmentFilter = departments => ({
  id: SALES_ORDER_LIST_VIEW_FILTER_KEY.DEPARTMENT,
  type: FILTER_TYPES.MULTI_SELECT,
  name: __('Department'),
  additional: {
    options: [
      { label: __('Default'), value: null },
      ...getFilterOptions('DEPARTMENT', departments, {
        label: option => option.label,
        value: option => option.value,
      }),
    ],
  },
});

const DEFAULT_FILTER_TYPES = [SO_DATE_FILTER.id];

// TODO: Need to change to exist/not-exist filter once backend starts supporting
const ON_ORDER_QUANTITY = {
  id: SALES_ORDER_LIST_VIEW_FILTER_KEY.ON_ORDER,
  name: __('On Order'),
  type: 'SELECT',
  additional: {
    operators: [IN],
    options: [
      {
        label: __('True'),
        value: true,
      },
      {
        label: __('False'),
        value: false,
      },
    ],
  },
};

const returnReferences = {
  id: SALES_ORDER_LIST_VIEW_FILTER_KEY.RETURN,
  name: __('Return'),
  additional: {
    checkFieldExistence: true,
  },
};

const sorAutoFilledReferences = {
  id: SALES_ORDER_LIST_VIEW_FILTER_KEY.AUTO_FILL,
  name: __('SOR auto fill'),
  additional: {
    checkFieldExistence: true,
  },
};

const sorRequestReferences = {
  id: SALES_ORDER_LIST_VIEW_FILTER_KEY.SOR_REQUEST,
  name: __('SOR Request'),
  additional: {
    checkFieldExistence: true,
  },
};

const ACTION_PENDING_FILTER = {
  id: SALES_ORDER_LIST_VIEW_FILTER_KEY.ACTION_PENDING,
  name: __('Action Pending'),
  additional: {
    checkFieldExistence: true,
  },
};

const ETA_FILTER = {
  id: SALES_ORDER_LIST_VIEW_FILTER_KEY.ETA,
  name: __('ETA'),
  type: FILTER_TYPES.DATE_WITHOUT_PRESET,
};

const ON_HOLD_FILTER = {
  id: SALES_ORDER_LIST_VIEW_FILTER_KEY.ON_HOLD,
  name: __('On Hold'),
  additional: {
    checkFieldExistence: true,
  },
};

const PREPAID_ORDER_FILTER = {
  id: SALES_ORDER_LIST_VIEW_FILTER_KEY.PREPAID_ORDERS,
  name: __('Prepaid'),
  additional: {
    checkFieldExistence: true,
  },
};

const SOR_RECEIVED_FILTER = {
  id: SALES_ORDER_LIST_VIEW_FILTER_KEY.SOR_RECEIVED,
  name: __('SOR Received'),
  additional: {
    checkFieldExistence: true,
  },
};

const CHARGE_FILTER = {
  id: 'customer.chargeCustomer',
  name: __('Is Charge'),
  type: FILTER_TYPES.MULTI_SELECT,
  additional: {
    options: [
      {
        value: true,
        label: __('Yes'),
      },
      {
        value: false,
        label: __('No'),
      },
    ],
  },
};

const GROSS_PROFIT_FILTER = {
  id: 'summary.totalGrossProfitPercentage',
  name: __('Gross Profit %'),
  type: FILTER_TYPES.NUMBER_INPUT_WITH_RANGE,
  additional: {
    operators: [LESS_THAN, GREATER_THAN, BTW, EQUALS],
  },
};

export const getAmountDueFilter = includeTwoDecimalPlacesInAmountFields => ({
  id: 'invoice.customerPay.dueAmount.amount',
  name: __('Payment Due'),
  type: FILTER_TYPES.AMOUNT_DOLLAR_RANGE,
  additional: {
    operators: [LESS_THAN, GREATER_THAN, BTW, EQUALS],
    enforcePrecision: includeTwoDecimalPlacesInAmountFields,
  },
});

export const getAmountPaidFilter = includeTwoDecimalPlacesInAmountFields => ({
  id: 'invoice.customerPay.paidAmount.amount',
  name: __('Amount Paid'),
  type: FILTER_TYPES.AMOUNT_DOLLAR_RANGE,
  additional: {
    operators: [LESS_THAN, GREATER_THAN, BTW, EQUALS],
    enforcePrecision: includeTwoDecimalPlacesInAmountFields,
  },
});

const DELIVERY_METHOD_FILTER = {
  id: 'deliveryMethod',
  name: __('Delivery Method'),
  type: FILTER_TYPES.MULTI_SELECT,
  additional: {
    options: getFilterOptions('DELIVERY_METHOD', DELIVERY_METHOD_OPTIONS, {
      label: _property('label'),
      value: _property('value'),
    }),
  },
};

const paymentStatusTypeOptions = getFilterOptions('PAYMENT_STATUS_FILTER', PAYMENT_STATUS_FILTER_TYPES);
const saleStatusOptions = getFilterOptions('SALE_STATUS_FILTER', SALE_STATUS_FILTER_TYPES);

export const getSaleTypeFilter = () => SALE_TYPE_FILTER;

export const getSaleTypeFilterWithCustomSaleTypes = ({ partsGeneralSettings, withTags, forceEnableOptions }) => {
  const saleTypeOptionsWithCustomSaleTypes = getSaleTypeOptions({
    partsGeneralSettings,
    withTags: withTags ?? false,
    forceEnable: forceEnableOptions ?? false,
  });

  return produce(SALE_TYPE_FILTER, draft => {
    _set(draft, 'additional.options', saleTypeOptionsWithCustomSaleTypes);
  });
};

const getTenderTypeFilter = isPCardEnabled => ({
  id: 'invoice.paymentDetails.paymentMode',
  name: __('Payment Type'),
  type: FILTER_TYPES.MULTI_SELECT,
  additional: {
    options: getFilterOptions('PAYMENT_TYPE', getPaymentTypeFilterOptions(isPCardEnabled), {
      label: _property('label'),
      value: _property('id'),
    }),
  },
});

const getPaymentTypeFilter = paymentModeConfigs => ({
  id: 'invoice.paymentDetails.paymentModeName',
  name: __('Payment Type'),
  type: FILTER_TYPES.MULTI_SELECT,
  additional: {
    options: getFilterOptions('PAYMENT_TYPE', paymentModeConfigs, {
      label: _property('paymentMode'),
      value: _property('id'),
    }),
  },
});

const getTenderTypeFilterForConfigurablePaymentModes = allTenderTypes => ({
  id: 'invoice.paymentDetails.paymentMode',
  name: __('Tender Type'),
  type: FILTER_TYPES.MULTI_SELECT,
  additional: {
    options: getFilterOptions('TENDER_TYPE', allTenderTypes, {
      label: _property('label'),
      value: _property('id'),
    }),
  },
});

const getRouteNumberFilter = routeOptions => ({
  id: 'shippingAddress.routeNumber',
  name: __('Route Number'),
  type: FILTER_TYPES.VIRTUALIZED_MULTI_SELECT,
  additional: {
    options: routeOptions,
  },
});

const SALE_TYPE_FILTER_WITH_VALUES = {
  type: SALES_ORDER_LIST_VIEW_FILTER_KEY.SALE_STATUS,
  operator: OPERATORS.IN,
  values: _keys(SALE_STATUS_FILTER_TYPES),
};

const makeFilterTypes = ({
  isCrossSiteDocumentAccessAllowed,
  isMultiOemSwitchEnabled,
  partsEnv,
  isPrepaidSalesEnabled,
  isSalesOrderV3Enabled,
  isPCardEnabled,
  isPaymentModesConfigEnabled,
  routeOptions,
  includeTwoDecimalPlacesInAmountFields,
}) => {
  const paymentStatusFilter = addOptionsToAdditional(PAYMENT_STATUS_FILTER, paymentStatusTypeOptions);

  const saleTypeFilter = getSaleTypeFilterWithCustomSaleTypes({
    partsGeneralSettings: _get(partsEnv, 'partsSettings'),
  });

  const saleStatusFilter = addOptionsToAdditional(SALE_STATUS_FILTER, saleStatusOptions);

  const siteIdFilter = isCrossSiteDocumentAccessAllowed ? getDealerSitesFilter(isMultiOemSwitchEnabled) : null;

  const prepaidOrderFilter = isPrepaidSalesEnabled ? PREPAID_ORDER_FILTER : null;
  const etaFilter = isSalesOrderV3Enabled ? ETA_FILTER : null;
  const paymentModeConfigs = CashieringSettingsReader.paymentModeConfiguration(partsEnv.cashieringSettings);
  const allTenderTypes = _map(_uniq(_map(paymentModeConfigs, PaymentModeConfigReader.tenderType)), tenderType => ({
    id: tenderType,
    label: COLLECT_PAYMENT_TYPES_CONFIG[tenderType]?.label || tenderType,
  }));

  const paymentTypeFilter = getPaymentTypeFilter(paymentModeConfigs);
  const tenderTypeFilter = isPaymentModesConfigEnabled
    ? getTenderTypeFilterForConfigurablePaymentModes(allTenderTypes)
    : getTenderTypeFilter(isPCardEnabled);

  const routeFilter = getRouteNumberFilter(routeOptions);

  return _compact([
    siteIdFilter,
    saleTypeFilter,
    paymentStatusFilter,
    saleStatusFilter,
    SO_DATE_FILTER,
    ON_ORDER_QUANTITY,
    returnReferences,
    sorRequestReferences,
    SOR_RECEIVED_FILTER,
    ACTION_PENDING_FILTER,
    etaFilter,
    ON_HOLD_FILTER,
    CHARGE_FILTER,
    GROSS_PROFIT_FILTER,
    getAmountDueFilter(includeTwoDecimalPlacesInAmountFields),
    getAmountPaidFilter(includeTwoDecimalPlacesInAmountFields),
    DELIVERY_METHOD_FILTER,
    prepaidOrderFilter,
    tenderTypeFilter,
    ...(isPaymentModesConfigEnabled ? [paymentTypeFilter] : EMPTY_ARRAY),
    sorAutoFilledReferences,
    routeFilter,
  ]);
};

export const getSalesOrderFilterProps = ({
  selectedFilterGroup,
  shouldSetInitialSelectedFilters,
  isCrossSiteDocumentAccessAllowed,
  isMultiOemSwitchEnabled,
  isPrepaidSalesEnabled,
  isSalesOrderV3Enabled,
  partsEnv,
  departments,
  isPCardEnabled,
  isPaymentModesConfigEnabled,
  routeOptions,
  includeTwoDecimalPlacesInAmountFields,
}) => {
  const filterTypes = makeFilterTypes({
    isCrossSiteDocumentAccessAllowed,
    isMultiOemSwitchEnabled,
    isPrepaidSalesEnabled,
    isSalesOrderV3Enabled,
    partsEnv,
    isPCardEnabled,
    isPaymentModesConfigEnabled,
    routeOptions,
    includeTwoDecimalPlacesInAmountFields,
  });

  return {
    filterTypes: [...filterTypes, getUserFilter(), getDepartmentFilter(departments)],
    defaultFilterTypes: DEFAULT_FILTER_TYPES,
    appliedFilterGroup: selectedFilterGroup,
    assetType: PART_SALES_ORDER_ASSET_TYPE,
    showDefaultFiltersInPopover: true,
    showAddFilter: false,
    shouldSetInitialSelectedFilters,
  };
};

export const DEFAULT_STATUS_FILTER_VALUES = _keys(SALE_STATUS_FILTER_TYPES);

export const SOR_RECEIVED_QUICK_FILTER = createFilter(
  SALES_ORDER_LIST_VIEW_FILTER_KEY.SOR_RECEIVED,
  SALES_ORDER_LIST_VIEW_FILTER_KEY.SOR_RECEIVED,
  OPERATORS.EXISTS,
  []
);
export const ACTION_PENDING_QUICK_FILTER = createFilter(
  SALES_ORDER_LIST_VIEW_FILTER_KEY.ACTION_PENDING,
  SALES_ORDER_LIST_VIEW_FILTER_KEY.ACTION_PENDING,
  OPERATORS.EXISTS,
  []
);

export const ETA_QUICK_FILTER = createFilter(
  SALES_ORDER_LIST_VIEW_FILTER_KEY.ETA,
  SALES_ORDER_LIST_VIEW_FILTER_KEY.ETA,
  OPERATORS.BTW,
  [1, getStartEpoch('day', Date.now())]
);

export const SOR_REQUESTED_QUICK_FILTER = createFilter(
  SALES_ORDER_LIST_VIEW_FILTER_KEY.SOR_REQUEST,
  SALES_ORDER_LIST_VIEW_FILTER_KEY.SOR_REQUEST,
  OPERATORS.EXISTS,
  []
);
export const UNPAID_SO_QUICK_FILTER = createFilter(
  SALES_ORDER_LIST_VIEW_FILTER_KEY.UNPAID_SO,
  SALES_ORDER_LIST_VIEW_FILTER_KEY.UNPAID_SO,
  OPERATORS.IN,
  [true]
);
export const ON_ORDER_QUICK_FILTER = createFilter(
  SALES_ORDER_LIST_VIEW_FILTER_KEY.ON_ORDER,
  SALES_ORDER_LIST_VIEW_FILTER_KEY.ON_ORDER,
  OPERATORS.IN,
  [true]
);
export const ON_HOLD_QUICK_FILTER = createFilter(
  SALES_ORDER_LIST_VIEW_FILTER_KEY.ON_HOLD,
  SALES_ORDER_LIST_VIEW_FILTER_KEY.ON_HOLD,
  OPERATORS.EXISTS,
  []
);
export const AUTOFILL_QUICK_FILTER = createFilter(
  SALES_ORDER_LIST_VIEW_FILTER_KEY.AUTO_FILL,
  SALES_ORDER_LIST_VIEW_FILTER_KEY.AUTO_FILL,
  OPERATORS.EXISTS,
  []
);
export const OPEN_QUOTES_QUICK_FILTER = createFilter(
  SALES_ORDER_LIST_VIEW_FILTER_KEY.OPEN_QUOTES,
  SALES_ORDER_LIST_VIEW_FILTER_KEY.OPEN_QUOTES,
  OPERATORS.IN,
  [true]
);

export const PREPAID_ORDERS_QUICK_FILTER = createFilter(
  SALES_ORDER_LIST_VIEW_FILTER_KEY.PREPAID_ORDERS,
  SALES_ORDER_LIST_VIEW_FILTER_KEY.PREPAID_ORDERS,
  OPERATORS.EXISTS,
  []
);

export const getSORReceivedAndPrepaidHoldFilterValue = ({ operator = OPERATORS.EXISTS } = EMPTY_OBJECT) => ({
  operator: OPERATORS.BOOL,
  groupType: 'FIELD',
  key: SALES_ORDER_LIST_VIEW_FILTER_KEY.SOR_RECEIVED,
  field: SALES_ORDER_LIST_VIEW_FILTER_KEY.SOR_RECEIVED,
  orFilters: [
    {
      key: SALES_ORDER_LIST_VIEW_FILTER_KEY.SOR_RECEIVED,
      field: SALES_ORDER_LIST_VIEW_FILTER_KEY.SOR_RECEIVED,
      operator: OPERATORS.IN,
      values: [operator === OPERATORS.EXISTS],
      groupType: 'FIELD',
    },
    {
      key: SALES_ORDER_LIST_VIEW_FILTER_KEY.PREPAID_HOLD,
      field: SALES_ORDER_LIST_VIEW_FILTER_KEY.PREPAID_HOLD,
      operator: OPERATORS.IN,
      values: [operator === OPERATORS.EXISTS],
      groupType: 'FIELD',
    },
  ],
});

const UNPAID_FILTER_PAYMENT_STATUS_VALUES = [PARTIALLY_PAID.id, UNPAID.id];
const UNPAID_FILTER_SALE_STATUS_VALUES = [INVOICED.id, PARTIALLY_DELIVERED.id, DELIVERED.id, REOPENED.id];

export const getQuickFilterPayload = ({ isPrepaidSalesEnabled }) => [
  {
    key: SALES_ORDER_LIST_VIEW_FILTER_KEY.SOR_RECEIVED,
    filters: [
      isPrepaidSalesEnabled
        ? getSORReceivedAndPrepaidHoldFilterValue({ operator: OPERATORS.EXISTS })
        : {
            key: SALES_ORDER_LIST_VIEW_FILTER_KEY.SOR_RECEIVED,
            field: SALES_ORDER_LIST_VIEW_FILTER_KEY.SOR_RECEIVED,
            operator: OPERATORS.IN,
            values: [true],
            groupType: 'FIELD',
          },
    ],
  },
  {
    key: SALES_ORDER_LIST_VIEW_FILTER_KEY.ACTION_PENDING,
    filters: [
      {
        key: SALES_ORDER_LIST_VIEW_FILTER_KEY.ACTION_PENDING,
        field: SALES_ORDER_LIST_VIEW_FILTER_KEY.ACTION_PENDING,
        operator: OPERATORS.IN,
        values: [true],
        groupType: 'FIELD',
      },
    ],
  },
  {
    key: SALES_ORDER_LIST_VIEW_FILTER_KEY.ETA,
    filters: [
      {
        key: SALES_ORDER_LIST_VIEW_FILTER_KEY.ETA,
        field: SALES_ORDER_LIST_VIEW_FILTER_KEY.ETA,
        operator: OPERATORS.BTW,
        values: [1, getStartEpoch('day', Date.now())],
        groupType: 'FIELD',
      },
    ],
  },
  {
    key: SALES_ORDER_LIST_VIEW_FILTER_KEY.SOR_REQUEST,
    filters: [
      {
        key: SALES_ORDER_LIST_VIEW_FILTER_KEY.SOR_REQUEST,
        field: SALES_ORDER_LIST_VIEW_FILTER_KEY.SOR_REQUEST,
        operator: OPERATORS.EXISTS,
        values: [],
        groupType: 'FIELD',
      },
    ],
  },
  {
    key: SALES_ORDER_LIST_VIEW_FILTER_KEY.UNPAID_SO,
    filters: [
      {
        key: SALES_ORDER_LIST_VIEW_FILTER_KEY.PAYMENT_STATUS,
        field: SALES_ORDER_LIST_VIEW_FILTER_KEY.PAYMENT_STATUS,
        operator: OPERATORS.IN,
        values: UNPAID_FILTER_PAYMENT_STATUS_VALUES,
        groupType: 'FIELD',
      },
      {
        key: SALES_ORDER_LIST_VIEW_FILTER_KEY.SALE_STATUS,
        field: SALES_ORDER_LIST_VIEW_FILTER_KEY.SALE_STATUS,
        operator: OPERATORS.IN,
        values: UNPAID_FILTER_SALE_STATUS_VALUES,
        groupType: 'FIELD',
      },
    ],
  },
  {
    key: SALES_ORDER_LIST_VIEW_FILTER_KEY.ON_ORDER,
    filters: [
      {
        key: SALES_ORDER_LIST_VIEW_FILTER_KEY.ON_ORDER,
        field: SALES_ORDER_LIST_VIEW_FILTER_KEY.ON_ORDER,
        operator: OPERATORS.IN,
        values: [true],
        groupType: 'FIELD',
      },
    ],
  },
  {
    key: SALES_ORDER_LIST_VIEW_FILTER_KEY.ON_HOLD,
    filters: [
      {
        key: SALES_ORDER_LIST_VIEW_FILTER_KEY.ON_HOLD,
        field: SALES_ORDER_LIST_VIEW_FILTER_KEY.ON_HOLD,
        operator: OPERATORS.IN,
        values: [true],
        groupType: 'FIELD',
      },
    ],
  },
  {
    key: SALES_ORDER_LIST_VIEW_FILTER_KEY.OPEN_QUOTES,
    filters: [
      {
        key: SALES_ORDER_LIST_VIEW_FILTER_KEY.SALE_STATUS,
        field: SALES_ORDER_LIST_VIEW_FILTER_KEY.SALE_STATUS,
        operator: OPERATORS.IN,
        values: [DRAFT.id],
        groupType: 'FIELD',
      },
    ],
  },
  {
    key: SALES_ORDER_LIST_VIEW_FILTER_KEY.AUTO_FILL,
    filters: [
      {
        key: SALES_ORDER_LIST_VIEW_FILTER_KEY.AUTO_FILL,
        field: SALES_ORDER_LIST_VIEW_FILTER_KEY.AUTO_FILL,
        operator: OPERATORS.IN,
        values: [true],
        groupType: 'FIELD',
      },
    ],
  },
  ...(isPrepaidSalesEnabled
    ? [
        {
          key: SALES_ORDER_LIST_VIEW_FILTER_KEY.PREPAID_ORDERS,
          filters: [
            {
              key: SALES_ORDER_LIST_VIEW_FILTER_KEY.PREPAID_ORDERS,
              field: SALES_ORDER_LIST_VIEW_FILTER_KEY.PREPAID_ORDERS,
              operator: OPERATORS.IN,
              values: [true],
              groupType: 'FIELD',
            },
          ],
        },
      ]
    : EMPTY_ARRAY),
];

export const STATUS_VALUES_FOR_QUICK_FILTER = 'statusValuesForQuickFilter';

const SOR_RECEIVED_QUICK_FILTER_VALUE = {
  displayName: __('SOR Received'),
  id: SALES_ORDER_LIST_VIEW_FILTER_KEY.SOR_RECEIVED,
  fixed: true,
  isConfigurable: false,
  checked: true,
  value: {
    ...SOR_RECEIVED_QUICK_FILTER,
    type: SALES_ORDER_LIST_VIEW_FILTER_KEY.SOR_RECEIVED,
  },
  selectedFilters: [SALE_TYPE_FILTER_WITH_VALUES],
  getCount: quickFiltersCount => _get(quickFiltersCount, SALES_ORDER_LIST_VIEW_FILTER_KEY.SOR_RECEIVED, 0),
  helpText: __('SO on which SOR or Stock Order Reserve parts have arrived and are on hold.'),
};

const ACTION_PENDING_QUICK_FILTER_VALUE = {
  displayName: __('Action Pending'),
  id: SALES_ORDER_LIST_VIEW_FILTER_KEY.ACTION_PENDING,
  isConfigurable: true,
  checked: true,
  value: {
    ...ACTION_PENDING_QUICK_FILTER,
    type: SALES_ORDER_LIST_VIEW_FILTER_KEY.ACTION_PENDING,
  },
  selectedFilters: [
    { ...SALE_TYPE_FILTER_WITH_VALUES, values: _without(SALE_TYPE_FILTER_WITH_VALUES.values, VOIDED.id) },
    { ...SALE_TYPE_FILTER_WITH_VALUES, values: _without(SALE_TYPE_FILTER_WITH_VALUES.values, VOIDED.id) },
  ],
  getCount: quickFiltersCount => _get(quickFiltersCount, SALES_ORDER_LIST_VIEW_FILTER_KEY.ACTION_PENDING, 0),
  helpText: __('SO on which parts are resolved but they’re not filled or ordered or put on hold yet.'),
};

const ETA_QUICK_FILTER_VALUE = {
  displayName: __('ETA Breached'),
  id: SALES_ORDER_LIST_VIEW_FILTER_KEY.ETA,
  isConfigurable: true,
  checked: false,
  value: {
    ...ETA_QUICK_FILTER,
    type: SALES_ORDER_LIST_VIEW_FILTER_KEY.ETA,
  },
  selectedFilters: [SALE_TYPE_FILTER_WITH_VALUES],
  getCount: quickFiltersCount => _get(quickFiltersCount, SALES_ORDER_LIST_VIEW_FILTER_KEY.ETA, 0),
  helpText: __('This will give the list of SO where ETA has been breached for part(s)'),
};

const SOR_REQUESTED_QUICK_FILTER_VALUE = {
  displayName: __('SOR Requested'),
  id: SALES_ORDER_LIST_VIEW_FILTER_KEY.SOR_REQUEST,
  isConfigurable: true,
  checked: true,
  value: {
    ...SOR_REQUESTED_QUICK_FILTER,
    type: SALES_ORDER_LIST_VIEW_FILTER_KEY.SOR_REQUEST,
  },
  selectedFilters: [SALE_TYPE_FILTER_WITH_VALUES],
  getCount: quickFiltersCount => _get(quickFiltersCount, SALES_ORDER_LIST_VIEW_FILTER_KEY.SOR_REQUEST, 0),
  helpText: __('SO for which SOR has been raised but order not placed yet.'),
};

const PREPAID_ORDERS_QUICK_FILTER_VALUE = {
  displayName: __('Prepaid Orders'),
  id: SALES_ORDER_LIST_VIEW_FILTER_KEY.PREPAID_ORDERS,
  isConfigurable: true,
  checked: false,
  value: {
    ...PREPAID_ORDERS_QUICK_FILTER,
    type: SALES_ORDER_LIST_VIEW_FILTER_KEY.PREPAID_ORDERS,
  },
  selectedFilters: [SALE_TYPE_FILTER_WITH_VALUES],
  getCount: quickFiltersCount => _get(quickFiltersCount, SALES_ORDER_LIST_VIEW_FILTER_KEY.PREPAID_ORDERS, 0),
  helpText: __('SO on which parts are prepaid'),
};

const UNPAID_SO_QUICK_FILTER_VALUE = {
  displayName: __('Unpaid SO'),
  id: SALES_ORDER_LIST_VIEW_FILTER_KEY.UNPAID_SO,
  isConfigurable: true,
  checked: true,
  value: [
    {
      type: SALES_ORDER_LIST_VIEW_FILTER_KEY.PAYMENT_STATUS,
      operator: OPERATORS.IN,
      values: UNPAID_FILTER_PAYMENT_STATUS_VALUES,
    },
    {
      type: SALES_ORDER_LIST_VIEW_FILTER_KEY.SALE_STATUS,
      operator: OPERATORS.IN,
      values: UNPAID_FILTER_SALE_STATUS_VALUES,
    },
  ],
  selectedFilters: [
    {
      type: SALES_ORDER_LIST_VIEW_FILTER_KEY.PAYMENT_STATUS,
      operator: OPERATORS.IN,
      values: UNPAID_FILTER_PAYMENT_STATUS_VALUES,
    },
    {
      type: SALES_ORDER_LIST_VIEW_FILTER_KEY.SALE_STATUS,
      operator: OPERATORS.IN,
      values: UNPAID_FILTER_SALE_STATUS_VALUES,
    },
  ],
  getCount: quickFiltersCount => _get(quickFiltersCount, SALES_ORDER_LIST_VIEW_FILTER_KEY.UNPAID_SO, 0),
  helpText: __('SO and Credit Memo on which payment or refund is pending.'),
};

const ON_ORDER_QUICK_FILTER_VALUE = {
  displayName: __('On Order'),
  id: SALES_ORDER_LIST_VIEW_FILTER_KEY.ON_ORDER,
  isConfigurable: true,
  checked: true,
  value: {
    ...ON_ORDER_QUICK_FILTER,
    type: SALES_ORDER_LIST_VIEW_FILTER_KEY.ON_ORDER,
  },
  selectedFilters: [SALE_TYPE_FILTER_WITH_VALUES],
  getCount: quickFiltersCount => _get(quickFiltersCount, SALES_ORDER_LIST_VIEW_FILTER_KEY.ON_ORDER, 0),
  helpText: __('SO for which parts order has been placed.'),
};

const ON_HOLD_QUICK_FILTER_VALUE = {
  displayName: __('On Hold'),
  id: SALES_ORDER_LIST_VIEW_FILTER_KEY.ON_HOLD,
  isConfigurable: true,
  checked: true,
  value: {
    ...ON_HOLD_QUICK_FILTER,
    type: SALES_ORDER_LIST_VIEW_FILTER_KEY.ON_HOLD,
  },
  selectedFilters: [SALE_TYPE_FILTER_WITH_VALUES],
  getCount: quickFiltersCount => _get(quickFiltersCount, SALES_ORDER_LIST_VIEW_FILTER_KEY.ON_HOLD, 0),
  helpText: __('SO on which parts are being held either from inventory or from SOR.'),
};

const AUTO_FILL_QUICK_FILTER_VALUE = {
  displayName: __('SOR AutoFill'),
  id: SALES_ORDER_LIST_VIEW_FILTER_KEY.AUTO_FILL,
  isConfigurable: true,
  checked: false,
  value: {
    ...AUTOFILL_QUICK_FILTER,
    type: SALES_ORDER_LIST_VIEW_FILTER_KEY.AUTO_FILL,
  },
  selectedFilters: [SALE_TYPE_FILTER_WITH_VALUES],
  getCount: quickFiltersCount => _get(quickFiltersCount, SALES_ORDER_LIST_VIEW_FILTER_KEY.AUTO_FILL, 0),
  helpText: __('SO on which SOR has been autofill either partially or fully.'),
};

const OPEN_QUOTES_QUICK_FILTER_VALUE = {
  displayName: __('Open Quotes'),
  id: SALES_ORDER_LIST_VIEW_FILTER_KEY.OPEN_QUOTES,
  isConfigurable: true,
  checked: false,
  value: {
    ...OPEN_QUOTES_QUICK_FILTER,
    type: SALES_ORDER_LIST_VIEW_FILTER_KEY.OPEN_QUOTES,
  },
  selectedFilters: [
    {
      type: SALES_ORDER_LIST_VIEW_FILTER_KEY.SALE_STATUS,
      operator: OPERATORS.IN,
      values: [DRAFT.id],
    },
  ],
  getCount: quickFiltersCount => _get(quickFiltersCount, SALES_ORDER_LIST_VIEW_FILTER_KEY.OPEN_QUOTES, 0),
  helpText: __('These are the open SO and Credit Sale quotes. '),
};

export const QUICK_FILTERS_TO_REMOVE = {
  UNPAID_SO_QUICK_FILTER_VALUE,
  OPEN_QUOTES_QUICK_FILTER_VALUE,
};

export const getQuickFiltersToConvert = ({ isPrepaidSalesEnabled }) => ({
  ON_HOLD_QUICK_FILTER_VALUE,
  ACTION_PENDING_QUICK_FILTER_VALUE,
  SOR_RECEIVED_QUICK_FILTER_VALUE,
  AUTO_FILL_QUICK_FILTER_VALUE,
  ...(isPrepaidSalesEnabled ? { PREPAID_ORDERS_QUICK_FILTER_VALUE } : EMPTY_ARRAY),
});

export const getQuickFilters = ({ isPrepaidSalesEnabled, isSalesOrderV3Enabled }) => [
  SOR_RECEIVED_QUICK_FILTER_VALUE,
  ...(isPrepaidSalesEnabled ? [PREPAID_ORDERS_QUICK_FILTER_VALUE] : EMPTY_ARRAY),
  ACTION_PENDING_QUICK_FILTER_VALUE,
  SOR_REQUESTED_QUICK_FILTER_VALUE,
  UNPAID_SO_QUICK_FILTER_VALUE,
  ON_ORDER_QUICK_FILTER_VALUE,
  ON_HOLD_QUICK_FILTER_VALUE,
  AUTO_FILL_QUICK_FILTER_VALUE,
  OPEN_QUOTES_QUICK_FILTER_VALUE,
  ...(isSalesOrderV3Enabled ? [ETA_QUICK_FILTER_VALUE] : EMPTY_ARRAY),
];
