import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import _map from 'lodash/map';
import _noop from 'lodash/noop';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';

import { arrayMove } from 'react-sortable-hoc';

// Constants
import { EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import Content from '@tekion/tekion-components/src/atoms/Content';
import TABLE_ACTION_TYPES from '@tekion/tekion-components/src/molecules/tableInputField/constants/TableInputField.actionTypes';

import { NESTING_PATH_SEPERATOR } from '../../constants/general';

class InputTableContent extends PureComponent {
  renderDataRow = parentPath => (rowData, index) => {
    const {
      columns,
      getTdProps,
      onAction,
      errors,
      getRowId,
      InputTableCellComponent,
      InputTableRowComponent,
      additionalCellProps,
    } = this.props;
    const currentPath = [...parentPath, index].join(NESTING_PATH_SEPERATOR);
    const rowError = _get(errors, [...parentPath, index].join('.children.')); // TODO Vidit Temp change to unblock,
    const rowKey = getRowId(rowData) || index;
    const children = _get(rowData, 'children', EMPTY_ARRAY);
    return (
      <>
        <InputTableRowComponent
          key={rowKey}
          rowIndex={index}
          path={currentPath}
          rowData={rowData}
          columns={columns}
          getTdProps={getTdProps}
          onAction={onAction}
          errors={rowError}
          InputTableCellComponent={InputTableCellComponent}
          additionalCellProps={additionalCellProps}
        />
        {_map(children, this.renderDataRow(currentPath))}
      </>
    );
  };

  handleSortEnd = ({ oldIndex, newIndex }) => {
    if (oldIndex === newIndex) return;

    const { onAction, data } = this.props;
    const updatedRows = arrayMove(data, oldIndex, newIndex);
    onAction({
      type: TABLE_ACTION_TYPES.TABLE_ROWS_ORDER_CHANGE,
      payload: {
        updatedRows,
      },
    });
  };

  render() {
    const { data, EmptyPlaceholder, loading, LoadingComponent } = this.props;
    if (loading) {
      // TODO bhavika to add her new table loader here
      return <LoadingComponent loading />;
    }
    if (_isEmpty(data)) {
      // TODO grid compliant empty placeholder
      return (
        <EmptyPlaceholder>
          <Content>{__('No Rows found')}</Content>
        </EmptyPlaceholder>
      );
    }
    return _map(data, this.renderDataRow(''));
  }
}

InputTableContent.propTypes = {
  data: PropTypes.array,
  columns: PropTypes.array,
  getTdProps: PropTypes.func,
  onAction: PropTypes.func,
  errors: PropTypes.array,
  loading: PropTypes.bool,
  EmptyPlaceholder: PropTypes.func,
  getRowId: PropTypes.func,
  InputTableCellComponent: PropTypes.element.isRequired,
  InputTableRowComponent: PropTypes.element.isRequired,
};

InputTableContent.defaultProps = {
  data: EMPTY_ARRAY,
  columns: EMPTY_ARRAY,
  errors: EMPTY_ARRAY,
  getTdProps: _noop,
  onAction: _noop,
  loading: false,
  EmptyPlaceholder: _noop,
  getRowId: _noop,
};

export default InputTableContent;
