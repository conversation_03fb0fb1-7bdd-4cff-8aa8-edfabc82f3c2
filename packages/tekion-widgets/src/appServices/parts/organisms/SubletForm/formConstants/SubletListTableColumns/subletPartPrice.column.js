import React from 'react';
import _get from 'lodash/get';

import { ColumnConfig, CellConfig } from '@tekion/tekion-components/src//molecules/tableInputField';
import { getCellError } from '@tekion/tekion-components/src//molecules/tableInputField/utils/general';
import makeCellRenderer from '@tekion/tekion-components/src/molecules/tableInputField/containers/makeCellRenderer';
import { TAX_DIVISION_TYPE } from '@tekion/tekion-business/src/appServices/parts/constants/tax';

import DecimalInputCellRenderer from '../../../../components/CellRenderers/DecimalInputCellRenderer';
import withTriggerChangeOnBlur from '../../../../../../hocs/parts/withTriggerChangeOnBlur';

import BulkUpdateTaxCheckbox from '../../components/BulkUpdateTaxCheckBox';
import { shouldSubletRowDisabled } from '../../subletJob.helper';
import { SUBLET_TABLE_COLUMNS } from '../subletForm.constants';
import { isViewMode as getIsViewMode } from '../../subletForm.utils';
import styles from '../../SubletForm.module.scss';

const WIDTH = 150;
const DecimalInputComponent = makeCellRenderer(withTriggerChangeOnBlur(DecimalInputCellRenderer));

const getSubletPriceProps = props => {
  const isLastRow = _get(props, 'original.isLastRow', false);
  const value = _get(props, 'original.partAmount', 0);
  const mode = _get(props, 'tdProps.rest.additional.extra.mode');

  return {
    className: 'text-right-align',
    error: isLastRow ? null : getCellError(props),
    min: 0,
    value,
    enforcePrecision: false,
    disabled: shouldSubletRowDisabled(props) || getIsViewMode(mode),
  };
};

const setGetValueFromOnChangePrice = (nestingPath, value) => value || 0;

const getPartPriceColumn = ({ onAction, taxDetails, taxableSubletColumnIds, isViewMode }) =>
  new ColumnConfig(SUBLET_TABLE_COLUMNS.PART_AMOUNT)
    .setAccessor(SUBLET_TABLE_COLUMNS.PART_AMOUNT)
    .setWidth(WIDTH)
    .addCellConfig(new CellConfig().setComponent(DecimalInputComponent))
    .setWidth(190)
    .setTitle(props => (
      <BulkUpdateTaxCheckbox
        {...props}
        onAction={onAction}
        taxDetails={taxDetails}
        taxDivisionType={TAX_DIVISION_TYPE.PART}
        taxableSubletColumnIds={taxableSubletColumnIds}
        isViewMode={isViewMode}
        label={__('Parts Price')}
      />
    ))
    .setMapCellPropsToComponentProps(getSubletPriceProps)
    .setGetValueFromOnChange(setGetValueFromOnChangePrice)
    .setHeaderClassName(`text-right-align ${styles.headerCell}`);

export default getPartPriceColumn;
