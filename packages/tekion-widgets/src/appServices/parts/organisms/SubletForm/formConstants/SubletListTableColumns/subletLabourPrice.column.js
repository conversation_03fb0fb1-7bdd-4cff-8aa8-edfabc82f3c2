import React from 'react';
import _get from 'lodash/get';

import { ColumnConfig, CellConfig } from '@tekion/tekion-components/src/molecules/tableInputField';
import { getCellError } from '@tekion/tekion-components/src/molecules/tableInputField/utils/general';
import makeCellRenderer from '@tekion/tekion-components/src/molecules/tableInputField/containers/makeCellRenderer';
import { TAX_DIVISION_TYPE } from '@tekion/tekion-business/src/appServices/parts/constants/tax';

import DecimalInputCellRenderer from '../../../../components/CellRenderers/DecimalInputCellRenderer';
import withTriggerChangeOnBlur from '../../../../../../hocs/parts/withTriggerChangeOnBlur';

import { shouldSubletRowDisabled } from '../../subletJob.helper';
import { SUBLET_TABLE_COLUMNS } from '../subletForm.constants';
import { isViewMode as getIsViewMode } from '../../subletForm.utils';
import BulkUpdateTaxCheckbox from '../../components/BulkUpdateTaxCheckBox';
import styles from '../../SubletForm.module.scss';

const DecimalInputComponent = makeCellRenderer(withTriggerChangeOnBlur(DecimalInputCellRenderer));

const getSubletPriceProps = props => {
  const isLastRow = _get(props, 'original.isLastRow', false);
  const value = _get(props, 'original.laborAmount', 0);
  const mode = _get(props, 'tdProps.rest.additional.extra.mode');

  return {
    className: 'text-right-align',
    error: isLastRow ? null : getCellError(props),
    min: 0,
    value,
    enforcePrecision: false,
    disabled: shouldSubletRowDisabled(props) || getIsViewMode(mode),
  };
};

const setGetValueFromOnChangePrice = (nestingPath, value) => value || 0;

const getLaborPriceColumn = ({ onAction, taxDetails, taxableSubletColumnIds, isViewMode }) =>
  new ColumnConfig(SUBLET_TABLE_COLUMNS.LABOR_AMOUNT)
    .setAccessor(SUBLET_TABLE_COLUMNS.LABOR_AMOUNT)
    .setWidth(200)
    .addCellConfig(new CellConfig().setComponent(DecimalInputComponent))
    .setWidth(200)
    .setTitle(props => (
      <BulkUpdateTaxCheckbox
        {...props}
        onAction={onAction}
        taxDivisionType={TAX_DIVISION_TYPE.LABOR}
        taxDetails={taxDetails}
        taxableSubletColumnIds={taxableSubletColumnIds}
        isViewMode={isViewMode}
        label={__('Labor Price')}
      />
    ))
    .setMapCellPropsToComponentProps(getSubletPriceProps)
    .setGetValueFromOnChange(setGetValueFromOnChangePrice)
    .setHeaderClassName(`text-right-align ${styles.headerCell}`);

export default getLaborPriceColumn;
