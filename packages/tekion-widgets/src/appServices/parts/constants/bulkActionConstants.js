import { ASSET_TYPE } from '@tekion/tekion-components/src/organisms/PdfViewerPartsV2/PdfViewer.constants';

export const BULK_ACTION_DOCUMENTS = {
  INVOICE: 'INVOICE', // comprises all salesOrders, invoices and returns
  QUOTE: 'QUOTE',
  PICKLIST: 'PICKLIST',
  CREDIT_MEMO: 'CREDIT_MEMO',
  SOR: 'SOR',
  SALES_ORDER: 'SALES_ORDER',
};

export const BULK_ACTION_STATUS = {
  FAILED: 'FAILED',
  SUCCESS: 'SUCCESS',
  PENDING: 'PENDING',
  IN_PROGRESS: 'IN_PROGRESS',
  POST_PROCESSING: 'POST_PROCESSING',
};

export const BULK_ACTIONS_REQUEST_TYPES = {
  PRINT: 'PRINT',
  PICKLIST: 'PICKLIST',
  EMAIL: 'EMAIL',
  DOWNLOAD: 'DOWNLOAD',
  LABEL_PRINT: 'LABEL_PRINT',
  SOR_MOVE_TO_OH: 'SOR_MOVE_TO_OH',
  SALES_ORDER_RETURN_CREATE: 'SALES_ORDER_RETURN_CREATE',
  SALES_ORDER_PAYMENT: 'SALES_ORDER_PAYMENT',
  SALES_ORDER_CREATION: 'SALES_ORDER_CREATION',
  SALES_ORDER_AUTO_INVOICE: 'SALES_ORDER_AUTO_INVOICE',
};

export const FORMATTED_BULK_ACTION_REQUEST = {
  [BULK_ACTIONS_REQUEST_TYPES.PRINT]: 'printed',
  [BULK_ACTIONS_REQUEST_TYPES.PICKLIST]: 'printed',
  [BULK_ACTIONS_REQUEST_TYPES.EMAIL]: 'emailed',
  [BULK_ACTIONS_REQUEST_TYPES.DOWNLOAD]: 'downloaded',
  [BULK_ACTIONS_REQUEST_TYPES.LABEL_PRINT]: 'printed',
  [BULK_ACTIONS_REQUEST_TYPES.SOR_MOVE_TO_OH]: 'moved to on hand',
  [BULK_ACTIONS_REQUEST_TYPES.SALES_ORDER_RETURN_CREATE]: 'created',
  [BULK_ACTIONS_REQUEST_TYPES.SALES_ORDER_PAYMENT]: 'refunded',
  [BULK_ACTIONS_REQUEST_TYPES.SALES_ORDER_CREATION]: 'created',
  [BULK_ACTIONS_REQUEST_TYPES.SALES_ORDER_AUTO_INVOICE]: 'generated',
};

export const ASSET_TYPE_VS_BULK_ACTION_DOCUMENTS = {
  [ASSET_TYPE.SALES_ORDER]: BULK_ACTION_DOCUMENTS.INVOICE,
  [ASSET_TYPE.MULTI_INVOICE]: BULK_ACTION_DOCUMENTS.INVOICE,
  [ASSET_TYPE.MULTI_INVOICE_RETURN]: BULK_ACTION_DOCUMENTS.INVOICE,
  [ASSET_TYPE.SALES_ORDER_RETURN]: BULK_ACTION_DOCUMENTS.INVOICE,
  [ASSET_TYPE.SALES_ORDER_QUOTE]: BULK_ACTION_DOCUMENTS.QUOTE,
  [ASSET_TYPE.CREDIT_MEMO]: BULK_ACTION_DOCUMENTS.CREDIT_MEMO,
};

export const BULK_ACTION_REQUEST_TYPE_VS_FORMATTED_DOCUMENT_NAME = {
  [BULK_ACTIONS_REQUEST_TYPES.PRINT]: {
    [BULK_ACTION_DOCUMENTS.INVOICE]: __('invoices'),
    [BULK_ACTION_DOCUMENTS.QUOTE]: __('quotes'),
    [BULK_ACTION_DOCUMENTS.CREDIT_MEMO]: __('credit memos'),
  },
  [BULK_ACTIONS_REQUEST_TYPES.PICKLIST]: {
    [BULK_ACTION_DOCUMENTS.PICKLIST]: __('picklists'),
  },
  [BULK_ACTIONS_REQUEST_TYPES.DOWNLOAD]: {
    [BULK_ACTION_DOCUMENTS.INVOICE]: __('invoices'),
    [BULK_ACTION_DOCUMENTS.CREDIT_MEMO]: __('credit memos'),
    [BULK_ACTION_DOCUMENTS.QUOTE]: __('quotes'),
  },
  [BULK_ACTIONS_REQUEST_TYPES.LABEL_PRINT]: {
    [BULK_ACTION_DOCUMENTS.PICKLIST]: __('part labels'),
  },
  [BULK_ACTIONS_REQUEST_TYPES.SOR_MOVE_TO_OH]: {
    [BULK_ACTION_DOCUMENTS.SOR]: __("SOR's parts"),
  },
  [BULK_ACTIONS_REQUEST_TYPES.SALES_ORDER_CREATION]: {
    [BULK_ACTION_DOCUMENTS.SALES_ORDER]: __('sales orders'),
  },
  [BULK_ACTIONS_REQUEST_TYPES.SALES_ORDER_AUTO_INVOICE]: {
    [BULK_ACTION_DOCUMENTS.INVOICE]: __('invoices'),
  },
};

export const BULK_ACTION_DOCUMENT_VS_LABEL = {
  [BULK_ACTION_DOCUMENTS.INVOICE]: 'Invoices',
  [BULK_ACTION_DOCUMENTS.QUOTE]: 'Quotes',
  [BULK_ACTION_DOCUMENTS.PICKLIST]: 'Picklists',
  [BULK_ACTION_DOCUMENTS.CREDIT_MEMO]: 'Credit Memos',
  [BULK_ACTION_DOCUMENTS.SALES_ORDER]: 'sales orders',
};

export const BULK_ACTIONS_REQUEST_VS_LABEL = {
  [BULK_ACTIONS_REQUEST_TYPES.PRINT]: 'print',
  [BULK_ACTIONS_REQUEST_TYPES.PICKLIST]: 'print',
  [BULK_ACTIONS_REQUEST_TYPES.EMAIL]: 'email',
  [BULK_ACTIONS_REQUEST_TYPES.DOWNLOAD]: 'download',
  [BULK_ACTIONS_REQUEST_TYPES.LABEL_PRINT]: 'part label print',
  [BULK_ACTIONS_REQUEST_TYPES.SOR_MOVE_TO_OH]: 'move to on hand',
  [BULK_ACTIONS_REQUEST_TYPES.SALES_ORDER_CREATION]: 'creation',
};

export const BULK_ACTION_DOCUMENT_VS_DOWNLOAD_FOLDER_LABEL = {
  [BULK_ACTION_DOCUMENTS.INVOICE]: 'SO_INVOICE',
  [BULK_ACTION_DOCUMENTS.QUOTE]: 'QUOTE',
  [BULK_ACTION_DOCUMENTS.PICKLIST]: 'SO_PICKLIST',
  [BULK_ACTION_DOCUMENTS.CREDIT_MEMO]: 'CM_INVOICE',
  [BULK_ACTION_DOCUMENTS.SALES_ORDER]: 'SALES_ORDER',
};

export const ASSET_TYPE_VS_LABEL = {
  [ASSET_TYPE.SALES_ORDER]: __('Sales Order'),
  [ASSET_TYPE.SOR]: __('SOR'),
};

export const BULK_ACTIONS_ROUTE_SEARCH_PARAMS = {
  BULK_REQUEST_ID: 'bulkRequestId',
  REQUEST_TYPE: 'requestType',
  ASSET_TYPE: 'assetType',
};
