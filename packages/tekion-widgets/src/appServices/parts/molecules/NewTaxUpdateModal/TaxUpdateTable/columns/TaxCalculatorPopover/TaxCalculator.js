import React, { useCallback, memo } from 'react';
import PropTypes from 'prop-types';

import _round from 'lodash/round';
import _divide from 'lodash/divide';
import _multiply from 'lodash/multiply';
import _noop from 'lodash/noop';
import _isNil from 'lodash/isNil';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import Content from '@tekion/tekion-components/src/atoms/Content/Content';
import Button from '@tekion/tekion-components/src/atoms/Button';
import Heading from '@tekion/tekion-components/src/atoms/Heading';

import styles from './taxCalculator.module.scss';
import CurrencyInput from '../../../../../components/CellRenderers/CurrencyInput';

const ADD_ON_BEFORE_LABEL = '$';

const PopoverContentRenderer = props => {
  const {
    isHideFooter,
    hideSubmit,
    tertiaryButtonLabel,
    primaryButtonLabel,
    tertiaryButtonProp,
    primaryDisabled,
    onSubmit,
    onCancel,
    hideCancel,
    primaryLoading,
    taxRate,
    taxAmount,
    totalAmount,
    setTaxRate,
    setTaxAmount,
  } = props;

  const handleChange = useCallback(
    updatedValue => {
      setTaxAmount(updatedValue);
      if (totalAmount > 0) {
        if (updatedValue === 0 || _isNil(updatedValue)) {
          setTaxRate(0);
          return;
        }
        const updatedTaxRate = _round(_multiply(_divide(updatedValue, totalAmount), 100), 2);
        setTaxRate(updatedTaxRate);
      }
    },
    [totalAmount, setTaxAmount, setTaxRate]
  );

  const handleSubmit = useCallback(() => {
    onSubmit(taxRate);
  }, [taxRate, onSubmit]);

  return (
    <div className={styles.container}>
      <Heading className={styles.heading}>{__('Calculate Tax %')}</Heading>
      <div className={styles.bodyContainer}>
        <Content>{__('Tax Amount')}</Content>
        <CurrencyInput
          addonBefore={ADD_ON_BEFORE_LABEL}
          shouldDisabledStepper
          wrapperClassName={styles.inputField}
          className={styles.inputField}
          value={taxAmount}
          onChange={handleChange}
          triggerChangeOnBlur={false}
          shouldDisableStepper
          precision={2}
          enforcePrecision={false}
        />
        {_isNil(taxRate) ? (
          <Content>{__('Tax Percentage -')}</Content>
        ) : (
          <Content>{__('Tax Percentage {{taxRate}}%', { taxRate })}</Content>
        )}
      </div>

      {!isHideFooter && (
        <div className={styles.footerWrapper}>
          <div>
            {!hideCancel && (
              <Button onClick={onCancel} view={Button.VIEW.SECONDARY} {...tertiaryButtonProp}>
                {tertiaryButtonLabel}
              </Button>
            )}
            {!hideSubmit && (
              <Button
                view={Button.VIEW.PRIMARY}
                onClick={handleSubmit}
                className={styles.primaryBtn}
                loading={primaryLoading}
                disabled={primaryDisabled}>
                {primaryButtonLabel}
              </Button>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

PopoverContentRenderer.propTypes = {
  isHideFooter: PropTypes.bool,
  hideSubmit: PropTypes.bool,
  tertiaryButtonLabel: PropTypes.string,
  primaryButtonLabel: PropTypes.string,
  tertiaryButtonProp: PropTypes.object,
  primaryDisabled: PropTypes.bool,
  onSubmit: PropTypes.func,
  onCancel: PropTypes.func,
  hideCancel: PropTypes.bool,
  primaryLoading: PropTypes.bool,
  taxRate: PropTypes.number,
  totalAmount: PropTypes.number,
  taxAmount: PropTypes.number,
  setTaxRate: PropTypes.func,
  setTaxAmount: PropTypes.func,
};

PopoverContentRenderer.defaultProps = {
  isHideFooter: false,
  hideSubmit: false,
  tertiaryButtonLabel: __('Cancel'),
  primaryButtonLabel: __('Apply'),
  tertiaryButtonProp: EMPTY_OBJECT,
  primaryDisabled: false,
  onSubmit: _noop,
  onCancel: _noop,
  hideCancel: false,
  primaryLoading: false,
  taxRate: null,
  totalAmount: 0,
  taxAmount: null,
  setTaxRate: _noop,
  setTaxAmount: _noop,
};

export default memo(PopoverContentRenderer);
