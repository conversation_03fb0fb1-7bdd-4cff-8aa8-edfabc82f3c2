import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';

import _toNumber from 'lodash/toNumber';

import { getFormattedCurrencyWithSymbol } from '@tekion/tekion-business/src/appServices/parts/utils/currency.utils';

import FORM_BUILDER_ACTION_TYPES from '@tekion/tekion-components/src/organisms/FormBuilder/constants/actionTypes';
import Content from '@tekion/tekion-components/src/atoms/Content';
import { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';

import IconAsBtn from '@tekion/tekion-components/src/atoms/iconAsBtn';
import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import CurrencyInput from '../../components/CellRenderers/CurrencyInput';
import styles from './editablePriceBreakUpSummary.module.scss';
import FlatTaxInfoWithPopOver from './FlatTaxInfoWithPopOver';

export const EDITABLE_SUMMARY_ACTION_TYPES = {
  ON_EDIT_TAX_AMOUNT_ICON_CLICK: 'ON_EDIT_TAX_AMOUNT_ICON_CLICK',
};

const EditablePriceBreakUpSection = React.memo(props => {
  const {
    label,
    value,
    sectionClassName,
    canEditFlatTax,
    onAction,
    id,
    taxType,
    defaultTaxRate,
    isViewMode,
    taxBreakUpByTaxType,
    taxDivisionType,
    withTaxDivisionSummary,
    taxDetailsByTaxType,
  } = props;

  const handleChange = useCallback(
    flatTaxAmount => {
      onAction({
        type: FORM_BUILDER_ACTION_TYPES.ON_FIELD_CHANGE,
        payload: {
          taxDivisionType,
          taxType,
          flatTaxAmount: _toNumber(flatTaxAmount) || 0,
          id,
        },
      });
    },
    [onAction, id, taxType, taxDivisionType]
  );

  const onEditTaxIconClick = useCallback(() => {
    onAction({
      type: EDITABLE_SUMMARY_ACTION_TYPES.ON_EDIT_TAX_AMOUNT_ICON_CLICK,
      payload: {
        taxDivisionType,
        id,
        taxBreakUpByTaxType,
        taxDetailsByTaxType,
        taxType,
      },
    });
  }, [taxType, onAction, id, taxDivisionType, taxBreakUpByTaxType, taxDetailsByTaxType]);

  const isInEditMode = !isViewMode;

  return (
    <div
      className={classNames(`${sectionClassName} ${styles.flatTaxContainer}`, {
        [styles.priceTextValueContainer]: isInEditMode,
        'm-b-4': isInEditMode,
        'm-b-8 m-t-8': withTaxDivisionSummary,
      })}>
      <FlatTaxInfoWithPopOver defaultTaxRate={defaultTaxRate}>
        <Content className={styles.flatTaxAmountLabel}>{label}</Content>
      </FlatTaxInfoWithPopOver>
      <div
        className={classNames(styles.priceSectionValue, 'flex', {
          [styles.editableTaxCell]: isInEditMode && !withTaxDivisionSummary,
          [styles.flatTaxAmount]: withTaxDivisionSummary,
        })}
        id={label}>
        {withTaxDivisionSummary ? (
          <Content className={styles.flatTaxAmountLabel}>{getFormattedCurrencyWithSymbol(value)}</Content>
        ) : (
          <CurrencyInput
            id={styles.editableCurrency}
            className={styles.flatTaxContainer}
            onChange={handleChange}
            disabled={!canEditFlatTax || withTaxDivisionSummary}
            value={value}
            min={0}
            enforcePrecision={false}
          />
        )}
        {withTaxDivisionSummary && canEditFlatTax ? (
          <IconAsBtn className={styles.editIcon} size={SIZES.MD_S} onClick={onEditTaxIconClick}>
            icon-edit
          </IconAsBtn>
        ) : null}
      </div>
    </div>
  );
});

EditablePriceBreakUpSection.propTypes = {
  label: PropTypes.string,
  value: PropTypes.string,
  sectionClassName: PropTypes.string,
  canEditFlatTax: PropTypes.bool,
  onAction: PropTypes.func.isRequired,
  taxType: PropTypes.object.isRequired,
  id: PropTypes.string.isRequired,
  defaultTaxRate: PropTypes.number,
  isViewMode: PropTypes.bool,
  taxBreakUpByTaxType: PropTypes.object,
  taxDivisionType: PropTypes.string,
  taxDetailsByTaxType: PropTypes.object,
  withTaxDivisionSummary: PropTypes.bool,
};

EditablePriceBreakUpSection.defaultProps = {
  label: '',
  value: '',
  sectionClassName: '',
  canEditFlatTax: false,
  defaultTaxRate: 0,
  isViewMode: false,
  taxBreakUpByTaxType: EMPTY_OBJECT,
  taxDivisionType: '',
  taxDetailsByTaxType: EMPTY_OBJECT,
  withTaxDivisionSummary: false,
};

export default React.memo(EditablePriceBreakUpSection);
