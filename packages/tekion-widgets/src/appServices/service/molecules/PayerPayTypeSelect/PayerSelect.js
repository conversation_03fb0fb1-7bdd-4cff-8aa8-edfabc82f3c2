import React, { useState, useMemo, useCallback, useEffect } from 'react';
import cx from 'classnames';
import PropTypes from 'prop-types';

import _noop from 'lodash/noop';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';

import Search from '@tekion/tekion-components/molecules/Search';
import { NativeSelect, components } from '@tekion/tekion-components/src/molecules/advancedSelect';
import Content from '@tekion/tekion-components/src/atoms/Content';
import Button from '@tekion/tekion-components/src/atoms/Button';
import Ellipsis from '@tekion/tekion-components/atoms/Ellipsis';
import FieldLabel from '@tekion/tekion-components/organisms/FormBuilder/components/fieldLabel';
import Error from '@tekion/tekion-components/organisms/FormBuilder/components/error';
import withFetchAsyncResource from '@tekion/tekion-components/src/molecules/advancedSelect/containers/withAsyncResource/withFetchAsyncResource/index';
import withAsyncSelect from '@tekion/tekion-components/src/molecules/advancedSelect/containers/withAsyncSelect';
import { SingleValueControl } from '@tekion/tekion-components/src/molecules/advancedSelect/components';
import { useDebounceCallback } from '@tekion/tekion-base/customHooks/useDebounce';

import { RESOURCE_TYPE } from '@tekion/tekion-base/bulkResolvers';

import { getPayerNameAndPayType } from '@tekion/tekion-business/src/appServices/service/helpers/payerPayType.helpers';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';

import castArrayIfPresent from '@tekion/tekion-base/utils/castArrayIfPresent';
import {
  DEFAULT_FILTER_VALUE,
  PRIMARY_PAYER_SELECT_ID,
  PAYER_TYPE_FILTER_ID,
  PAYER_SEARCH_ID,
  STYLE_OVERRIDES,
  SINGLE_VALUE_ELLIPSIS_LENGTH,
} from './PayerPayTypeSelect.constants';
import {
  PAYER_TYPE_OPTIONS,
  getDefaultAndAssociatedPayersFilterPayload,
  isAssociatedPayer,
  isDefaultPayer,
} from './PayerPayTypeSelect.utils';
import { usePayerSearch } from './hooks/usePayerSearch';
import { usePayerTypeFilter } from './hooks/usePayerTypeFilter';
import { usePrimaryPayerSelect } from './hooks/usePrimaryPayerSelect';
import { PAYER_OPTIONS_ELLIPSIS_LENGTH } from '../../constants/payer.constants';

import styles from './PayerPayTypeSelect.module.scss';

const AdvanceAsyncSelect = withFetchAsyncResource(withAsyncSelect(NativeSelect));

const PayerSelect = props => {
  const {
    value: primaryPayerId,
    onAction,
    id,
    payerPayTypeConfiguration,
    defaultSubPayType,
    payTypeConfigurations,
    containerClassName,
    shouldShowFieldLabel,
    menuPosition,
    menuPlacement,
    isDisabled,
    isClearable,
    error,
    fieldClassName,
    showManageSplitsButton,
    handleManagePayerOnClick,
    subPayType,
    customerId,
    filterOption,
  } = props;

  const [menuIsOpen, setMenuIsOpen] = useState(false);
  const [refreshOptions, setRefreshOptions] = useState(false);
  const [searchQuery, setSearchQuery] = useState(EMPTY_STRING);
  const [defaultAndAssociatedPayersFilterPayload, setDefaultAndAssociatedPayersFilterPayload] = useState(EMPTY_OBJECT);

  const debouncedSetRefreshOptions = useDebounceCallback(setRefreshOptions, 400);

  useEffect(() => {
    if (_isNil(subPayType)) {
      debouncedSetRefreshOptions(!_isNil(subPayType));
    } else {
      debouncedSetRefreshOptions(!_isNil(subPayType));
      setDefaultAndAssociatedPayersFilterPayload(
        getDefaultAndAssociatedPayersFilterPayload(payTypeConfigurations, subPayType, customerId)
      );
    }
  }, [customerId, payTypeConfigurations, subPayType, debouncedSetRefreshOptions]);

  const getMenuPortalTarget = useCallback(() => document.getElementById(id), [id]);

  const { handleSearch, handleDeleteForSearch, handleMouseTouchEventForSearch, handleKeyDown } = usePayerSearch(
    setSearchQuery,
    debouncedSetRefreshOptions
  );

  const {
    handlePrimaryPayerChange,
    handleOnLoadOptionsSuccess,
    handleClickForPrimaryPayer,
    handleBlurForPrimaryPayer,
  } = usePrimaryPayerSelect({
    onAction,
    id,
    payerPayTypeConfiguration,
    defaultSubPayType,
    setMenuIsOpen,
    setRefreshOptions,
    primaryPayerId,
    shouldSendJobPayType: true,
    subPayType,
  });

  const {
    filterQuery,
    filterIsOpen,
    payerTypeFilterPayload,
    handlePayerTypeFilterChange,
    handlePayerTypeFilterClick,
    handlePayerTypeFilterBlur,
    handleMenuListBlur,
  } = usePayerTypeFilter(setMenuIsOpen, debouncedSetRefreshOptions, id, defaultAndAssociatedPayersFilterPayload);

  const renderPayerTag = useCallback(
    label => <Content className={cx(styles.paytypeClass, styles.tagClass)}>{label}</Content>,
    []
  );

  const getPayerTag = useCallback(
    (optionData, customerId) => {
      const isPayerAssociated = isAssociatedPayer(optionData, payTypeConfigurations, subPayType);
      const isPayerDefault = isDefaultPayer(optionData, payTypeConfigurations, subPayType, customerId);
      if (isPayerDefault) return renderPayerTag(__('Default'));
      if (customerId === optionData?.id) return renderPayerTag(__('$$(RO) Billing'));
      if (isPayerAssociated) return renderPayerTag(__('Associated'));
      return renderPayerTag(__('Other'));
    },
    [subPayType, payTypeConfigurations, renderPayerTag]
  );

  const getPayerLabel = useCallback(
    (option, ellipsisLength) => {
      const { optionData } = option;
      if (_isEmpty(optionData)) {
        return null;
      }

      const { payerName } = getPayerNameAndPayType({ optionData });
      return (
        <div className={styles.payerOptions}>
          <Content className={styles.payerNameClass}>
            <Ellipsis tooltip length={ellipsisLength}>
              {payerName}
            </Ellipsis>
          </Content>
          {getPayerTag(optionData, customerId)}
        </div>
      );
    },
    [getPayerTag, customerId]
  );

  const getFormattedOptionLabel = useCallback(
    option => getPayerLabel(option, PAYER_OPTIONS_ELLIPSIS_LENGTH),
    [getPayerLabel]
  );

  const getCustomMenuList = useCallback(
    menuProps => (
      <>
        <div className={styles.selectSearchFilterContainer} onBlur={handleMenuListBlur}>
          <NativeSelect
            options={PAYER_TYPE_OPTIONS}
            onChange={handlePayerTypeFilterChange}
            value={filterQuery}
            defaultValue={DEFAULT_FILTER_VALUE}
            inputId={PAYER_TYPE_FILTER_ID}
            onMenuOpen={handlePayerTypeFilterClick}
            onBlur={handlePayerTypeFilterBlur}
            menuIsOpen={filterIsOpen}
            className={styles.selectFilterContainer}
          />
          <Search
            placeholder={__('Search')}
            className={styles.selectSearchContainer}
            value={searchQuery}
            id={PAYER_SEARCH_ID}
            onChange={handleSearch}
            onMouseDown={handleMouseTouchEventForSearch}
            onTouchEnd={handleMouseTouchEventForSearch}
            onKeyUp={handleDeleteForSearch}
            onKeyDown={handleKeyDown}
            autoFocus
          />
        </div>
        <components.MenuList {...menuProps} />
      </>
    ),
    [
      filterIsOpen,
      filterQuery,
      handleDeleteForSearch,
      handleMenuListBlur,
      handleMouseTouchEventForSearch,
      handlePayerTypeFilterBlur,
      handlePayerTypeFilterChange,
      handlePayerTypeFilterClick,
      handleSearch,
      searchQuery,
      handleKeyDown,
    ]
  );

  const getSingleValue = useCallback(
    singleValueProps => {
      const { data } = singleValueProps || EMPTY_OBJECT;
      const payerLabel = getPayerLabel(data, SINGLE_VALUE_ELLIPSIS_LENGTH);
      if (_isEmpty(payerLabel)) return null;
      return (
        <SingleValueControl {...singleValueProps} className={styles.singleValueContainer}>
          {payerLabel}
        </SingleValueControl>
      );
    },
    [getPayerLabel]
  );

  const overriddenComponents = useMemo(
    () => ({ MenuList: getCustomMenuList, SingleValue: getSingleValue }),
    [getCustomMenuList, getSingleValue]
  );

  const sanitizedValue = useMemo(() => primaryPayerId, [primaryPayerId]);

  const renderSelect = useCallback(
    () => (
      <AdvanceAsyncSelect
        menuPortalTarget={getMenuPortalTarget()}
        menuPosition={menuPosition}
        menuPlacement={menuPlacement}
        value={castArrayIfPresent(sanitizedValue)}
        onChange={handlePrimaryPayerChange}
        components={overriddenComponents}
        resourceType={RESOURCE_TYPE.CUSTOMER}
        shouldFetchOnValueChange
        refreshOptions={refreshOptions}
        onLoadOptionsSuccess={handleOnLoadOptionsSuccess}
        filters={payerTypeFilterPayload}
        id={id}
        menuIsOpen={menuIsOpen}
        onMenuOpen={handleClickForPrimaryPayer}
        onBlur={handleBlurForPrimaryPayer}
        className={containerClassName}
        formatOptionLabel={getFormattedOptionLabel}
        inputValue={searchQuery}
        styles={STYLE_OVERRIDES}
        isDisabled={isDisabled}
        isClearable={isClearable}
        isSearchable={false}
        filterOption={filterOption}
      />
    ),
    [
      containerClassName,
      getFormattedOptionLabel,
      handleBlurForPrimaryPayer,
      handleClickForPrimaryPayer,
      handleOnLoadOptionsSuccess,
      handlePrimaryPayerChange,
      menuIsOpen,
      overriddenComponents,
      payerTypeFilterPayload,
      sanitizedValue,
      refreshOptions,
      searchQuery,
      menuPosition,
      menuPlacement,
      getMenuPortalTarget,
      isDisabled,
      isClearable,
      id,
      filterOption,
    ]
  );

  if (!shouldShowFieldLabel) {
    return renderSelect();
  }

  return (
    <div className={fieldClassName}>
      <FieldLabel {...props} />
      {renderSelect()}
      {showManageSplitsButton && (
        <Button
          id="managePayers"
          view="tertiary"
          className={styles.manageSplitsButton}
          onClick={handleManagePayerOnClick}>
          {__('Manage Splits')}
        </Button>
      )}
      {error && <Error error={error} />}
    </div>
  );
};

PayerSelect.propTypes = {
  onAction: PropTypes.func,
  id: PropTypes.string,
  payerPayTypeConfiguration: PropTypes.object,
  defaultSubPayType: PropTypes.string,
  value: PropTypes.string,
  payTypeConfigurations: PropTypes.array,
  containerClassName: PropTypes.string,
  shouldShowFieldLabel: PropTypes.bool,
  menuPosition: PropTypes.string,
  menuPlacement: PropTypes.string,
  isClearable: PropTypes.bool,
  isDisabled: PropTypes.bool,
  label: PropTypes.string,
  error: PropTypes.string,
  fieldClassName: PropTypes.string,
  showManageSplitsButton: PropTypes.bool,
  handleManagePayerOnClick: PropTypes.func,
  subPayType: PropTypes.string,
  customerId: PropTypes.string,
};

PayerSelect.defaultProps = {
  onAction: _noop,
  id: PRIMARY_PAYER_SELECT_ID,
  payerPayTypeConfiguration: EMPTY_OBJECT,
  defaultSubPayType: EMPTY_STRING,
  value: EMPTY_STRING,
  payTypeConfigurations: EMPTY_ARRAY,
  containerClassName: EMPTY_STRING,
  shouldShowFieldLabel: false,
  menuPosition: EMPTY_STRING,
  menuPlacement: undefined,
  isClearable: false,
  isDisabled: false,
  label: __('Primary Payer'),
  error: EMPTY_STRING,
  fieldClassName: EMPTY_STRING,
  showManageSplitsButton: false,
  handleManagePayerOnClick: _noop,
  subPayType: null,
  customerId: EMPTY_STRING,
};

export default React.memo(PayerSelect);
