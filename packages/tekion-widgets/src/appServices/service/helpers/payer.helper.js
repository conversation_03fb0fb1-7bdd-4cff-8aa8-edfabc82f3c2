import { tget } from '@tekion/tekion-base/utils/general';
// eslint-disable-next-line
import ServiceEnv from '@tekion/tekion-widgets/src/appServices/service/utils/serviceEnv';
import { SERVICE_RESTRICTED_SETTINGS_ID } from '@tekion/tekion-base/constants/roSettings.constants';
import { RO_CLOSE_LEVELS } from '@tekion/tekion-business/src/appServices/service/constants/payer.constants';

export const getPostingLevelFromServiceSettings = (shouldAddFallback = true) => {
  const fallback = shouldAddFallback ? RO_CLOSE_LEVELS.BASE_PAY_TYPE : null;
  return tget(ServiceEnv, ['settings', SERVICE_RESTRICTED_SETTINGS_ID, 'doAccountPostingBasedOn'], fallback);
};
