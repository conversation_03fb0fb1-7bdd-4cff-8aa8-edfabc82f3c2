import { tget } from '@tekion/tekion-base/utils/general';

import {
  SERVICE_MODULE_REDUCER_KEY,
  SERVICE_RO_BASE_REDUCER_KEY,
  SERVICE_BOOTSTRAP_REDUCER_KEY,
} from '../constants/appModuleInitializer.constants';

const WARRANTY_POSTING_LIST_REDUCER_KEY = 'RO_WARRANTY_POSTINGS';
const REPAIR_ORDER_DETAILS_REDUCER_KEY = 'repairOrderDetails';
const RO_LIST_OVERLAY_REDUCER_KEY = 'RO_LIST_OVERLAY';
const DAY_COLLECTION_REDUCER_KEY = 'dayCollection';
const ESTIMATE_REDUCER_KEY = 'estimates';
const ESTIMATE_LIST_REDUCER_KEY = 'estimatesList';
const INVOICE_REDUCER_KEY = 'invoices';
const COLLECTION_HISTORY_REDUCER_KEY = 'collectionHistory';
const LABOR_PRICING_LIST_REDUCER_KEY = 'LABOR_PRICING';
const MPVI_FORMS_REDUCER_KEY = 'RO_MPVI_FORMS';
const OPCODE_LIST_REDUCER_KEY = 'OPCODE_LIST';
const OPCODE_DETAIL_REDUCER_KEY = 'OPCODE_MANAGEMENT_FORM';
const CONFIGURATOR_LIST_REDUCER_KEY = 'PDF_CONFIGURATOR';
const PRA_LIST_REDUCER_KEY = 'PRA';
const QUOTE_LIST_REDUCER_KEY = 'QUOTES_LIST';
const QUOTE_DETAILS_REDUCER_KEY = 'quoteDetails';
const FLAG_HOURS_REDUCER_KEY = 'FLAG_HOURS';
const RO_DASHBOARD_REDUCER_KEY = 'RO_DASHBOARD';
const RO_WARRANTY_CLAIM_LIST_REDUCER_KEY = 'RO_WARRANTY_CLAIMS';
const SERVICE_MENU_SETTINGS_REDUCER_KEY = 'serviceMenuSettings';
const RO_LIST_REDUCER_KEY = 'RO';
const SERVICE_MENU_REDUCER_KEY = 'serviceMenu';
const RO_NOTES_REDUCER_KEY = 'notes';
const LABOR_DETAIL_REDUCER_KEY = 'laborDetail';
const LABOR_PRICING_REDUCER_KEY = 'laborPricing';
const SERVICE_CRM_REDUCE_KEY = 'serviceCrm';
const SERVICE_CRM_LIST_REDUCE_KEY = 'serviceCrmList';
const INVOICE_CONFIRMATION_REDUCER_KEY = 'invoiceConfirmation';
const PRE_RO_DETAILS_REDUCER_KEY = 'preRODetails';
const ASYNC_QUEUE_REDUCER_KEY = 'asyncQueue';

const getReducerWithNamespace = reducerKey =>
  `${SERVICE_MODULE_REDUCER_KEY}.${SERVICE_RO_BASE_REDUCER_KEY}.${reducerKey}`;

export const getWarrantyPostingReducerKey = () => getReducerWithNamespace(WARRANTY_POSTING_LIST_REDUCER_KEY);
export const getRepairOrderDetailsReducerKey = () => getReducerWithNamespace(REPAIR_ORDER_DETAILS_REDUCER_KEY);
export const getROListOverlayReducerKey = () => getReducerWithNamespace(RO_LIST_OVERLAY_REDUCER_KEY);
export const getDayCollectionReducerKey = () => getReducerWithNamespace(DAY_COLLECTION_REDUCER_KEY);
export const getEstimateReducerKey = () => getReducerWithNamespace(ESTIMATE_REDUCER_KEY);
export const getEstimateListReducerKey = () => getReducerWithNamespace(ESTIMATE_LIST_REDUCER_KEY);
export const getInvoiceReducerKey = () => getReducerWithNamespace(INVOICE_REDUCER_KEY);
export const getCollectionHistoryReducerKey = () => getReducerWithNamespace(COLLECTION_HISTORY_REDUCER_KEY);
export const getLaborPricingListReducerKey = () => getReducerWithNamespace(LABOR_PRICING_LIST_REDUCER_KEY);
export const getMpviFormReducerKey = () => getReducerWithNamespace(MPVI_FORMS_REDUCER_KEY);
export const getOpcodeListReducerKey = () => getReducerWithNamespace(OPCODE_LIST_REDUCER_KEY);
export const getOpcodeDetailReducerKey = () => getReducerWithNamespace(OPCODE_DETAIL_REDUCER_KEY);
export const getConfiguratorListReducerKey = () => getReducerWithNamespace(CONFIGURATOR_LIST_REDUCER_KEY);
export const getPRAListReducerKey = () => getReducerWithNamespace(PRA_LIST_REDUCER_KEY);
export const getQuoteListReducerKey = () => getReducerWithNamespace(QUOTE_LIST_REDUCER_KEY);
export const getQuoteDetailsReducerKey = () => getReducerWithNamespace(QUOTE_DETAILS_REDUCER_KEY);
export const getFlagHoursReducerKey = () => getReducerWithNamespace(FLAG_HOURS_REDUCER_KEY);
export const getDashboardReducerKey = () => getReducerWithNamespace(RO_DASHBOARD_REDUCER_KEY);
export const getWarrantyClaimListReducerKey = () => getReducerWithNamespace(RO_WARRANTY_CLAIM_LIST_REDUCER_KEY);
export const getServiceMenuSettingsReducerKey = () => getReducerWithNamespace(SERVICE_MENU_SETTINGS_REDUCER_KEY);
export const getROListReducerKey = () => getReducerWithNamespace(RO_LIST_REDUCER_KEY);
export const getROBootstrapReducerKey = () => `${SERVICE_MODULE_REDUCER_KEY}.${SERVICE_BOOTSTRAP_REDUCER_KEY}`;
export const getServiceMenuReducerKey = () => getReducerWithNamespace(SERVICE_MENU_REDUCER_KEY);
export const getRONotesReducerKey = () => getReducerWithNamespace(RO_NOTES_REDUCER_KEY);
export const getLaborDetailReducerKey = () => getReducerWithNamespace(LABOR_DETAIL_REDUCER_KEY);
export const getLaborPricingReducerKey = () => getReducerWithNamespace(LABOR_PRICING_REDUCER_KEY);
export const getServiceCRMReducerKey = () => getReducerWithNamespace(SERVICE_CRM_REDUCE_KEY);
export const getServiceCRMListReducerKey = () => getReducerWithNamespace(SERVICE_CRM_LIST_REDUCE_KEY);
export const getInvoiceConfirmationReducerKey = () => getReducerWithNamespace(INVOICE_CONFIRMATION_REDUCER_KEY);
export const getAsyncQueueReducerKey = () => getReducerWithNamespace(ASYNC_QUEUE_REDUCER_KEY);

export const getWarrantyPostingState = state => tget(state, getWarrantyPostingReducerKey());
export const getRepairOrderDetailsState = state => tget(state, getRepairOrderDetailsReducerKey());
export const getROListOverlayState = state => tget(state, getROListOverlayReducerKey());
export const getDayCollectionState = state => tget(state, getDayCollectionReducerKey());
export const getEstimateState = state => tget(state, getEstimateReducerKey());
export const getEstimateListState = state => tget(state, getEstimateListReducerKey());
export const getInvoiceState = state => tget(state, getInvoiceReducerKey());
export const getCollectionHistoryState = state => tget(state, getCollectionHistoryReducerKey());
export const getlaborPricingListState = state => tget(state, getLaborPricingListReducerKey());
export const getMpviFormState = state => tget(state, getMpviFormReducerKey());
export const getOpcodeListState = state => tget(state, getOpcodeListReducerKey());
export const getOpcodeDetailState = state => tget(state, getOpcodeDetailReducerKey());
export const getConfiguratorListState = state => tget(state, getConfiguratorListReducerKey());
export const getPRAListState = state => tget(state, getPRAListReducerKey());
export const getQuoteListState = state => tget(state, getQuoteListReducerKey());
export const getFlagHoursState = state => tget(state, getFlagHoursReducerKey());
export const getDashboardState = state => tget(state, getDashboardReducerKey());
export const getWarrantyClaimListState = state => tget(state, getWarrantyClaimListReducerKey());
export const getServiceMenuSettingsState = state => tget(state, getServiceMenuSettingsReducerKey());
export const getROListState = state => tget(state, getROListReducerKey());
export const getROBootstrapState = state => tget(state, getROBootstrapReducerKey());
export const getServiceMenuState = state => tget(state, getServiceMenuReducerKey());
export const getRONotesState = state => tget(state, getRONotesReducerKey());
export const getLaborDetailsState = state => tget(state, getLaborDetailReducerKey());
export const getLaborPricingState = state => tget(state, getLaborPricingReducerKey());
// export const getQuote
export const getPreRODetailsReducerKey = () => getReducerWithNamespace(PRE_RO_DETAILS_REDUCER_KEY);
