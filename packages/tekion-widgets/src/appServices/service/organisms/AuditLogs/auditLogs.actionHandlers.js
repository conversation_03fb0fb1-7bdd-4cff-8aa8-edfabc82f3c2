import _get from 'lodash/get';
import _head from 'lodash/head';
import _reduce from 'lodash/reduce';
import _isEmpty from 'lodash/isEmpty';
import _isPlainObject from 'lodash/isPlainObject';
import _isFunction from 'lodash/isFunction';

import { tget } from '@tekion/tekion-base/utils/general';
import { getFilterPayload as getSelectedFilters } from '@tekion/tekion-base/helpers/filter.helper';
import { getUpdatedPartitionedLists } from '@tekion/tekion-components/src/molecules/PartitionedList';
import { EMPTY_OBJECT, EMPTY_ARRAY, EMPTY_ROW } from '@tekion/tekion-base/app.constants';

import { resolveAuditLogs } from './bulkResolver';
import {
  isLastPage,
  groupAuditsByDay,
  getInitFilterProps,
  getSanitizedAuditLogs,
  getAuditsFetchPayload,
  getAuditLogsDatePointers,
  getFilterTypesByAssetType,
  getStateToSetOnScroll,
  getUpdatedLookupData,
  getLookupWithSystemUser,
} from './helpers';
import {
  STATE_VALUES,
  ERROR_MESSAGES,
  SCROLL_FETCH_LOADER_POSITION,
  AUDIT_LOGS_ACTION_TYPES as ACTION_TYPES,
} from './auditLogs.constants';

const setResolvedData = (setState, getState) => (_, __, newLookupData) => {
  const { lookupData } = getState();
  setState({ lookupData: getUpdatedLookupData(newLookupData, lookupData) });
};

const getDefaultStateToSetPostFetch = ({ nextToken, ...restState }) => ({
  ...restState,
  nextScrollDownToken: nextToken,
});

const handleFetchAuditLogs = (param = EMPTY_OBJECT, { getState, setState }) => {
  const {
    fetchPayload = EMPTY_OBJECT,
    stateToSetPreFetch = EMPTY_OBJECT,
    getStateToSetPostFetch = getDefaultStateToSetPostFetch,
  } = param;

  const { getAuditLogs } = getState();
  if (getAuditLogs) {
    getAuditLogs().then(logs => {
      setState({
        auditLogs: logs,
      });
    });
    return;
  }

  setState({ showLoader: true, auditLogs: EMPTY_ARRAY, ...stateToSetPreFetch }, () => {
    const { isScrollTop } = fetchPayload;
    const { auditLogs, currentTab, tabsMap, filterProps, assetIds, fetchAuditLogs, lookupResolver, ...restState } =
      getState();
    if (_isEmpty(currentTab)) {
      setState(STATE_VALUES.LOADER_OFF);
      return;
    }
    const assetData = tget(tabsMap, currentTab, EMPTY_OBJECT);
    const selectedFilters = _get(filterProps, 'selectedFilters');
    fetchAuditLogs(
      getAuditsFetchPayload({
        ...restState,
        ...assetData,
        filters: getSelectedFilters(selectedFilters),
        ...fetchPayload,
        assetIds,
      }),
      assetIds
    )
      .then(({ nextToken, resultList, queryExecutionId }) => {
        const resolver = _isFunction(lookupResolver) ? lookupResolver : resolveAuditLogs;
        return resolver(resultList, setResolvedData(setState, getState)).then(() => {
          setState({
            ...STATE_VALUES.LOADER_OFF,
            queryExecutionId,
            ...getStateToSetPostFetch({
              nextToken,
              noAuditsMessage: undefined,
              ...getAuditLogsDatePointers(resultList),
              auditLogs: getUpdatedPartitionedLists(auditLogs, {
                groupItemsBy: groupAuditsByDay,
                itemsToAppend: getSanitizedAuditLogs(resultList, _get(assetData, 'label')),
                isAppendTop: isScrollTop,
              }),
            }),
          });
        });
      })
      .catch(() => {
        setState({ ...STATE_VALUES.LOADER_OFF, noAuditsMessage: ERROR_MESSAGES.AUDIT_FETCH });
      });
  });
};

const handleTabInit = (selectedTab, getState, setState) => {
  if (!selectedTab) return;
  const { tabsAudits = EMPTY_OBJECT, tabsMap, fetchAuditBeginningTimestamp } = getState();
  const filterTypes = getFilterTypesByAssetType(tabsMap, selectedTab);
  const filterProps = getInitFilterProps(filterTypes);
  const assetData = tget(tabsMap, selectedTab, EMPTY_OBJECT);
  const { id: assetType, assetId } = assetData;
  fetchAuditBeginningTimestamp(assetType, assetId).then(auditBeginningTimestamp =>
    setState({ auditBeginningTimestamp })
  );
  handleFetchAuditLogs(
    {
      stateToSetPreFetch: {
        filterProps,
        currentTab: selectedTab,
        nextScrollUpToken: null,
        searchKeywords: EMPTY_ARRAY,
        tabsAudits: { ...tabsAudits, [selectedTab]: EMPTY_OBJECT },
      },
    },
    { getState, setState }
  );
};

const handleInit = ({ payload = EMPTY_OBJECT }, { setState, getState }) => {
  const { tabs, initialLocalLookup } = getState();
  if (_isEmpty(tabs)) return;
  const tabsMap = _reduce(tabs, (accTabs, tab = EMPTY_OBJECT) => ({ ...accTabs, [tab.id]: tab }), EMPTY_OBJECT);
  setState(
    {
      tabsMap,
      ...payload,
      noAuditsMessage: null,
      auditLogs: EMPTY_ARRAY,
      showOldValues: true,
      lookupData: getLookupWithSystemUser(initialLocalLookup),
    },
    () => {
      const defaultAsset = _get(_head(tabs), 'id');
      handleTabInit(defaultAsset, getState, setState);
    }
  );
};

const handleDateChange = ({ payload = EMPTY_OBJECT }, { setState, getState }) => {
  const { value } = payload;
  if (!value) return;
  const getStateToSetPostFetch = ({ nextToken, ...restStateToSet }) => ({
    ...restStateToSet,
    nextScrollUpToken: undefined,
    nextScrollDownToken: nextToken,
  });
  handleFetchAuditLogs({ getStateToSetPostFetch, fetchPayload: { endTimestamp: value } }, { getState, setState });
};

const handleSearch = ({ payload = EMPTY_OBJECT }, { getState, setState }) => {
  const { value } = payload;
  handleFetchAuditLogs(
    { stateToSetPreFetch: { searchKeywords: value, ...STATE_VALUES.RESET_PAGINATION } },
    { getState, setState }
  );
};

const handleScroll = ({ payload }, { getState, setState }) => {
  const { isScrollTop } = payload || EMPTY_OBJECT;
  const { nextScrollUpToken, nextScrollDownToken, auditLogs } = getState();
  if (isLastPage(isScrollTop, nextScrollUpToken, nextScrollDownToken)) return;

  const { TOP, BOTTOM } = SCROLL_FETCH_LOADER_POSITION;
  const getStateToSetPostFetch = ({ endTimestamp, startTimestamp, nextToken, ...restStateToSet }) => ({
    ...restStateToSet,
    ...getStateToSetOnScroll(isScrollTop, { endTimestamp, startTimestamp, nextToken }),
  });
  handleFetchAuditLogs(
    {
      getStateToSetPostFetch,
      fetchPayload: { isScrollTop },
      stateToSetPreFetch: { auditLogs, loaderPosition: isScrollTop ? TOP : BOTTOM },
    },
    { getState, setState }
  );
};

const handleRefresh = (_, { getState, setState }) => {
  const { tabsMap, currentTab } = getState();
  handleFetchAuditLogs(
    {
      stateToSetPreFetch: {
        searchKeywords: EMPTY_ARRAY,
        filterProps: getInitFilterProps(getFilterTypesByAssetType(tabsMap, currentTab)),
        ...STATE_VALUES.RESET_PAGINATION,
      },
    },
    { getState, setState }
  );
};

const handleTabChange = ({ payload = EMPTY_OBJECT }, { getState, setState }) => {
  const { value } = payload;
  if (!value) return;

  const {
    tabsAudits = EMPTY_OBJECT,
    currentTab,
    filterProps,
    searchKeywords,
    userClickedFilters,
    auditLogs,
    nextScrollUpToken,
    nextScrollDownToken,
    auditBeginningTimestamp,
  } = getState();

  const tabAudit = tabsAudits[value];
  const currTabState = {
    filterProps,
    searchKeywords,
    userClickedFilters,
    auditLogs,
    nextScrollUpToken,
    nextScrollDownToken,
    auditBeginningTimestamp,
  };

  if (_isPlainObject(tabAudit)) {
    setState({
      ...tabAudit,
      currentTab: value,
      tabsAudits: { ...tabsAudits, [value]: EMPTY_OBJECT, [currentTab]: currTabState },
    });
    return;
  }
  setState({ tabsAudits: { [currentTab]: currTabState } }, () => {
    handleTabInit(value, getState, setState);
  });
};

const handleUserClickedFilterApply = ({ payload }, { getState, setState }) => {
  const { value } = payload;
  const { tabsMap, currentTab } = getState();
  handleFetchAuditLogs(
    {
      stateToSetPreFetch: {
        userClickedFilters: value,
        searchKeywords: EMPTY_ARRAY,
        nextScrollUpToken: null,
        filterProps: getInitFilterProps(getFilterTypesByAssetType(tabsMap, currentTab)),
      },
    },
    { getState, setState }
  );
};

const handleClearFilterByPath = (_, { getState, setState }) =>
  setState({ userClickedFilters: null }, () => handleRefresh(undefined, { getState, setState }));

const handleToggleShowOldValues = (_, { setState }) =>
  setState(prevState => ({ showOldValues: !prevState.showOldValues }));

const handleUpdateAuditScrollHeight = ({ payload }, { setState }) =>
  setState({ auditBodyContentHeight: payload.value });

const handleAuditFilterApply = ({ payload = EMPTY_OBJECT }, { getState, setState }) => {
  const { value } = payload;
  const { filterProps } = getState();
  const newSelectedFilters = _isEmpty(value) ? EMPTY_ROW : value;
  handleFetchAuditLogs(
    {
      stateToSetPreFetch: {
        ...STATE_VALUES.RESET_PAGINATION,
        filterProps: {
          ...filterProps,
          selectedFilters: newSelectedFilters,
        },
      },
    },
    { getState, setState }
  );
};

const ACTION_HANDLERS = {
  [ACTION_TYPES.INIT]: handleInit,
  [ACTION_TYPES.ON_SCROLL]: handleScroll,
  [ACTION_TYPES.REFRESH_LOGS]: handleRefresh,
  [ACTION_TYPES.ON_CHANGE_SEARCH]: handleSearch,
  [ACTION_TYPES.ON_TAB_CHANGE]: handleTabChange,
  [ACTION_TYPES.ON_DATE_CHANGE]: handleDateChange,
  [ACTION_TYPES.ON_CLEAR_PATH_FILTER]: handleClearFilterByPath,
  [ACTION_TYPES.ON_USER_CLICK_FILTER_CHANGE]: handleUserClickedFilterApply,
  [ACTION_TYPES.TOGGLE_SHOW_OLD_VALUES]: handleToggleShowOldValues,
  [ACTION_TYPES.UPDATE_SCROLL_HEIGHT]: handleUpdateAuditScrollHeight,
  [ACTION_TYPES.ON_AUDIT_FILTER_APPLY]: handleAuditFilterApply,
};

export default ACTION_HANDLERS;
