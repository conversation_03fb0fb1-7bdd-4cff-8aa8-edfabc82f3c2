import React from 'react';
import PropTypes from 'prop-types';

// Styles
import s from './dealerLogo.module.scss';

const DealerLogo = props => {
  const { imgUrl, className } = props;
  return <img src={imgUrl} alt="" className={`${s.logo} ${className} absolute checkDealerLogo`} />;
};

DealerLogo.propTypes = {
  imgUrl: PropTypes.string,
  className: PropTypes.string,
};

DealerLogo.defaultProps = {
  imgUrl: '',
  className: '',
};

export default DealerLogo;
