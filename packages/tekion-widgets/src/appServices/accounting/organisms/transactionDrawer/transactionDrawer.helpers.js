/* eslint-disable import/order */

// lodash
import _keyBy from 'lodash/keyBy';

// Readers
import setupFieldReader from '@tekion/tekion-base/readers/SetupField';

// Components
import ReversingJournalEntry from './reversingJournalEntry';
import AdjustingJournalEntry from './adjustingJournalEntry';
import JournalEntryView from './journalEntryView';
import JournalEntryAdjustDate from './adjustDateJournalEntry';
import PriorPeriodDateAdjustmentJournalEntry from './organisms/priorPeriodDateAdjustmentJournalEntry';
import ViewConsolidatedInterFranchiseJournalEntry from './organisms/viewConsolidatedInterFranchiseJournalEntry';
import ReversingConsolidatedIFJournalEntry from './organisms/reversingConsolidatedIFJournalEntry';
import AdjustAccDateConsolidatedIFJournalEntry from './organisms/adjustAccDateConsolidatedIFJournalEntry';
import AdjustingConsolidatedIFJournalEntry from './organisms/adjustingConsolidatedIFJournalEntry';

// helpers
import getRoute from '@tekion/tekion-business/src/factories/route/getRoute';

// Constants
import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import JOURNAL_ENTRY_TYPES from '../../constants/journalEntryTypes';
import { ACCOUNTING } from '@tekion/tekion-base/constants/appServices';
import { JOURNAL_ENTRIES } from '@tekion/tekion-base/constants/appConfigs/accountingAppConfigs';
import JOURNAL_ENTRY_MODES from '@tekion/tekion-business/src/appServices/accounting/factories/routes/journalEntries/journalEntries.modes';

const JOURNAL_ENTRY_TYPE_VS_COMPONENT = {
  [JOURNAL_ENTRY_TYPES.REVERSING]: ReversingJournalEntry,
  [JOURNAL_ENTRY_TYPES.ADJUSTING]: AdjustingJournalEntry,
  [JOURNAL_ENTRY_TYPES.VIEW]: JournalEntryView,
  [JOURNAL_ENTRY_TYPES.ADJUST_DATE]: JournalEntryAdjustDate,
  [JOURNAL_ENTRY_TYPES.ADJUST_DATE_TO_PRIOR_PERIOD]: PriorPeriodDateAdjustmentJournalEntry,
  [JOURNAL_ENTRY_TYPES.CONSOLIDATED_VIEW]: ViewConsolidatedInterFranchiseJournalEntry,
  [JOURNAL_ENTRY_TYPES.REVERSING_CONSOLIDATED_IF]: ReversingConsolidatedIFJournalEntry,
  [JOURNAL_ENTRY_TYPES.ADJUST_ACC_DATE_CONSOLIDATED_IF]: AdjustAccDateConsolidatedIFJournalEntry,
  [JOURNAL_ENTRY_TYPES.ADJUSTING_CONSOLIDATED_IF]: AdjustingConsolidatedIFJournalEntry,
};

export const getComponent = (journalEntryType, showMigratedJEConfirmationModal = false) => {
  const Component = JOURNAL_ENTRY_TYPE_VS_COMPONENT[journalEntryType];
  if (Component && !showMigratedJEConfirmationModal) return Component;

  return JournalEntryView;
};

export const getTransactionDealerInfo = (transactionDealerId, dealersById = EMPTY_OBJECT, currentDealerInfo) =>
  dealersById[transactionDealerId] || currentDealerInfo;

export const getJournalEntryPdfExportUrl = (transactionId, transactionDealerId) =>
  getRoute(ACCOUNTING, JOURNAL_ENTRIES.getKey(), {
    mode: JOURNAL_ENTRY_MODES.PRINT,
    dealerId: transactionDealerId,
    transactionId,
    isExternal: true,
    isRootApp: true,
  });

export const getTransactionDealerMetadata = ({
  allAccountsMapByDealerId = EMPTY_OBJECT,
  accountsMapByDealerId = EMPTY_OBJECT,
  journalsMapByDealerId = EMPTY_OBJECT,
  accountingSettingsByDealerId = EMPTY_OBJECT,
  setupFieldsByDealerId = EMPTY_OBJECT,
  accountingSettingsForCurrentDealer,
  setupFieldsForCurrentDealer,
  accountsMapForCurrentDealer,
  allAccountsMapForCurrentDealer,
  journalsMapForCurrentDealer,
  transactionDealerId,
}) => {
  if (transactionDealerId) {
    const setupFieldsForTransactionDealer = setupFieldsByDealerId[transactionDealerId];
    const setupFieldsById = _keyBy(setupFieldsForTransactionDealer, setupFieldReader.id);
    return {
      allAccountsMap: allAccountsMapByDealerId[transactionDealerId],
      accountsMap: accountsMapByDealerId[transactionDealerId],
      journalsMap: journalsMapByDealerId[transactionDealerId],
      accountingSettings: accountingSettingsByDealerId[transactionDealerId],
      setupFields: setupFieldsById,
    };
  }
  return {
    accountingSettings: accountingSettingsForCurrentDealer,
    setupFields: setupFieldsForCurrentDealer,
    accountsMap: accountsMapForCurrentDealer,
    allAccountsMap: allAccountsMapForCurrentDealer,
    journalsMap: journalsMapForCurrentDealer,
  };
};

export const checkIfOtherDealerTransactionActionsAreAllowed = (
  transactionDealerId,
  currentDealerId,
  isEnterpriseV2Enabled
) => !transactionDealerId || transactionDealerId === currentDealerId || isEnterpriseV2Enabled;
