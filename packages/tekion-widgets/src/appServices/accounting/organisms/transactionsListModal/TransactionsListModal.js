/* eslint-disable import/order */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { defaultMemoize } from 'reselect';
import { compose } from 'recompose';

// lodash
import _size from 'lodash/size';
import _get from 'lodash/get';
import _noop from 'lodash/noop';

// Containers
import withActions from '@tekion/tekion-components/src/connectors/withActions';
import { withTekionConversion } from '@tekion/tekion-conversion-web';

// components
import Modal from '@tekion/tekion-components/src/molecules/Modal';
import BaseTable from '@tekion/tekion-components/src/molecules/table/BaseTable';
import FontIcon, { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';

// readers
import transactionReader from '@tekion/tekion-base/readers/Transaction';

// utils
import { getEditJERedirectionRoute } from '@tekion/tekion-business/src/appServices/accounting/helpers/journalEntryRoutes';
import openWindowInNewTab from '@tekion/tekion-base/utils/openWindowInNewTab';

// constants
import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import MODES from '@tekion/tekion-business/src/constants/routeModes';
import Colors from 'tstyles/exports.scss';
import ACTION_HANDLERS from './transactionsListModal.actionHandlers';
import FEATURE_FLAGS from '@tekion/tekion-business/src/appServices/accounting/constants/featureFlags';

// helpers
import getColumns from './transactionsListModal.columns';

// context
import { EMPTY_FEATURE_FLAGS, FeatureFlags } from '../../../../context/featureFlags';

// Style
import styles from './transactionsListModal.module.scss';

class TransactionsListModal extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      isSaving: false,
    };
  }

  getColumns = defaultMemoize(getColumns);

  getTdProps = () => {
    const { onAction } = this.props;

    const tdProps = {
      onAction,
    };
    return tdProps;
  };

  handleRowClick = value => {
    const { routeMode, featureFlags } = this.props;
    const selectedTransaction = _get(value, 'row._original');
    const transactionId = transactionReader.id(selectedTransaction);
    const dealerId = transactionReader.dealerId(selectedTransaction);
    const isTransactionSecured = transactionReader.secured(selectedTransaction);
    const isEnterpriseV2Enabled = featureFlags.getFlag(FEATURE_FLAGS.IS_ENTERPRISE_V2_ENABLED);
    if (!isTransactionSecured) {
      const url = getEditJERedirectionRoute({
        mode: routeMode,
        transactionId,
        isExternal: true,
        dealerId,
        isEnterpriseV2Enabled,
      });
      openWindowInNewTab(url);
    }
  };

  renderDuplicateTransactionsMessage = () => {
    const { selectedRefText, transactions } = this.props;
    const duplicateReferencesCount = _size(transactions);
    return (
      <div>
        <div className="flex align-items-center">
          <FontIcon color={Colors.mordantRed} size={SIZES.S} className={styles.duplicateCountIcon}>
            icon-caution
          </FontIcon>
          <div className={styles.duplicateCountMessage}>
            {__('{{duplicateReferencesCount}} duplicate reference(s) found', {
              duplicateReferencesCount,
            })}
          </div>
        </div>
        <div className={styles.selectedReference}>
          {__('The following journal entries have already been posted with Reference {{selectedRefText}}', {
            selectedRefText,
          })}
        </div>
      </div>
    );
  };

  setAsSavingComplete = () => {
    this.setState({
      isSaving: false,
    });
  };

  handleModalSubmit = () => {
    const { onSubmit, onCancel } = this.props;
    this.setState({
      isSaving: true,
    });
    const submitPromise = onSubmit();
    if (submitPromise instanceof Promise) {
      submitPromise.then(onCancel).finally(this.setAsSavingComplete);
    }
  };

  renderDuplicateTransactions = () => {
    const { transactions, journalsMap, getFormattedDateAndTime } = this.props;
    const transactionsCount = _size(transactions);
    const columns = this.getColumns(journalsMap, getFormattedDateAndTime);
    return (
      <>
        {this.renderDuplicateTransactionsMessage()}
        <BaseTable
          className={styles.duplicateTransactionsTable}
          columns={columns}
          data={transactions}
          pageSize={transactionsCount}
          showPagination={false}
          minRows={transactionsCount}
          onRowClick={this.handleRowClick}
          sortable={false}
          getTdProps={this.getTdProps}
        />
      </>
    );
  };

  render() {
    const { visible, onCancel, hideFooter } = this.props;
    const { isSaving } = this.state;
    return (
      <Modal
        hideFooter={hideFooter}
        onCancel={onCancel}
        onSubmit={this.handleModalSubmit}
        visible={visible}
        title={__('Duplicates Found')}
        width={Modal.SIZES.XL}
        submitBtnText={__('Ignore and Submit')}
        loading={isSaving}
        destroyOnClose
        afterClose={this.setAsSavingComplete}>
        {this.renderDuplicateTransactions()}
      </Modal>
    );
  }
}

TransactionsListModal.propTypes = {
  visible: PropTypes.bool,
  transactions: PropTypes.array,
  journalsMap: PropTypes.object,
  selectedRefText: PropTypes.string,
  onCancel: PropTypes.func,
  onSubmit: PropTypes.func,
  onAction: PropTypes.func,
  hideFooter: PropTypes.bool,
  getFormattedDateAndTime: PropTypes.func,
  routeMode: PropTypes.string,
  featureFlags: PropTypes.instanceOf(FeatureFlags),
};

TransactionsListModal.defaultProps = {
  visible: false,
  transactions: EMPTY_ARRAY,
  journalsMap: EMPTY_OBJECT,
  selectedRefText: undefined,
  onCancel: _noop,
  onSubmit: _noop,
  onAction: _noop,
  hideFooter: false,
  getFormattedDateAndTime: _noop,
  routeMode: MODES.EDIT,
  featureFlags: EMPTY_FEATURE_FLAGS,
};

export default compose(withActions(EMPTY_OBJECT, ACTION_HANDLERS), withTekionConversion)(TransactionsListModal);
