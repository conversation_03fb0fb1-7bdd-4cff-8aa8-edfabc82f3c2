/* eslint-disable no-restricted-imports */
/* eslint-disable import/order */
import React, { createRef } from 'react';
import PropTypes from 'prop-types';
import { compose, withProps } from 'recompose';
import { defaultMemoize } from 'reselect';
import { connect } from 'react-redux';

// Lodash
import _noop from 'lodash/noop';
import _get from 'lodash/get';

// Containers
import withSetupFields from '../../../../containers/withSetupFields';
import {
  JOURNAL_ENTRY_ACTION_TYPES,
  PostingContext,
  withTransactionRestrictions,
  InvalidControlOverridePopup,
  CreditLimitOverridePopup,
  checkIfMultiControlInputDrawerIsVisible,
  withGrossAmount,
} from '../../../transactionPostingForm';
import withSize from 'tcomponents/hoc/withSize';
import withPropertyConsumer from '@tekion/tekion-components/src/organisms/propertyProvider/withPropertyConsumer';

// Components
import ConfigurableEditTransactionForm from './organisms/configurableEditTransactionForm/ConfigurableEditTransactionForm';
import AttachmentsUploadInProgressModal from '../../../../molecules/attachmentsUploadInProgressModal';
import RecurringScheduleUnsavedChangesModal from '../../../../molecules/recurringScheduleUnsavedChangesDialog';
import TransactionsListModal from '../../../transactionsListModal';
import Modal from '@tekion/tekion-components/src/molecules/Modal';
import RestrictedAsset from '@tekion/tekion-components/src/molecules/restrictedAsset/index';
import ShortcutActionList from 'tcomponents/molecules/shortcutActionList';
import { triggerSubmit } from 'tcomponents/pages/formPage/utils/formAction';

// Utils

// Constants
import MODES from '@tekion/tekion-business/src/constants/routeModes';
import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { GENERAL } from '@tekion/tekion-business/src/appServices/accounting/constants/transactionTypes';
import { BODY_PROPS } from '../../constants/configurableTransaction.general';
import FIELD_IDS from '../../constants/configurableTransaction.fieldIds';
import ACTION_TYPES from '../../constants/configurableTransaction.actionTypes';
import { SHORTCUT_ACTION_TYPE } from '../../constants/configurableTransaction.shortcutActions';
import SHORT_CUT_CONFIGS from './constants/configurableEditTransaction.shortcutConfig';
import { EMPTY_FEATURE_FLAGS, FeatureFlags } from '../../../../../../context/featureFlags';

// Helpers
import FORM_ACTION_HANDLERS from './helpers/configurableEditTransaction.formActionHandlers';
import {
  makeEditJERequestDTO,
  defaultGlPostingNormalizer,
  defaultPostingNormalizer,
} from './helpers/configurableEditTransaction.request';
import {
  getUpdatedFooterProps,
  getSubmitDataWithFlowType,
  getUpdatedBannerProps,
} from './helpers/configurableEditTransaction.general';
import { getUpdatedFields } from './helpers/configurableEditTransaction.getUpdatedFields';
import makeTransactionProps from './helpers/configurableEditTransaction.makeTransactionProps';
class ConfigurableEditTransaction extends React.Component {
  constructor(props) {
    super(props);
    this.transactionBodyRef = createRef();

    this.shortcutActionHandlersMap = {
      [SHORTCUT_ACTION_TYPE.SUBMIT_JE]: this.handleTriggerSubmit,
      [SHORTCUT_ACTION_TYPE.SAVE_DRAFT_JE]: this.handleSaveAsDraft,
    };
  }

  getUpdatedFooterProps = defaultMemoize(getUpdatedFooterProps);

  getUpdatedFields = defaultMemoize(getUpdatedFields);

  getUpdatedBannerProps = defaultMemoize(getUpdatedBannerProps);

  getSubmitData = shouldOverrideCreditLimit => {
    const {
      values,
      fields,
      accountsMap,
      transaction,
      postings,
      transactionType,
      glPostingNormalizer,
      shouldUpdateCountAdjusted,
      isAdvanceTemplate,
      taxCodeList,
      initialFormValues,
      targetIFJournalByDealerId,
      makeEditTransactionSubmitData,
      setupFieldsByDealerId,
      accountsMapByDealerId,
      isDefaultJournalValidationRequired,
      postingNormalizer,
      journalsMapByDealerId,
      featureFlags,
      getDealerPropertyValue,
      transactionMetadata,
      shouldOverrideInvalidControl,
    } = this.props;
    const submitData = makeEditTransactionSubmitData({
      initialFormValues,
      formValues: values,
      setupFields: fields,
      accountsMap,
      initialPostings: postings,
      initialTransaction: transaction,
      transactionType,
      glPostingNormalizer,
      shouldUpdateCountAdjusted,
      isAdvanceTemplate,
      taxCodeList,
      targetIFJournalByDealerId,
      setupFieldsByDealerId,
      accountsMapByDealerId,
      isDefaultJournalValidationRequired,
      postingNormalizer,
      journalsMapByDealerId,
      featureFlags,
      getDealerPropertyValue,
      shouldOverrideCreditLimit,
      transactionMetadata,
      shouldOverrideInvalidControl,
    });
    return {
      data: submitData,
    };
  };

  handleSubmit = action => {
    const { onAction, showNonZeroBalanceError, values, checkIfTransactionIsUnbalanced, shouldValidateBalance } =
      this.props;
    this.shouldOverrideCreditLimit = _get(action, 'payload.shouldOverrideCreditLimit', false);
    if (checkIfTransactionIsUnbalanced(values) && shouldValidateBalance) {
      showNonZeroBalanceError(true);
      return;
    }
    const additional = _get(action, 'payload.additional', EMPTY_OBJECT);
    const { data: submitData } = this.getSubmitData(this.shouldOverrideCreditLimit);
    onAction({
      type: action.type,
      payload: {
        submitData,
        additional,
      },
    });
  };

  handleAction = action => {
    const { handleAction, journalsMap, values, onAction } = this.props;
    const { type } = action;
    const handler = FORM_ACTION_HANDLERS[type];
    const getSubmitDataWithCreditLimitOverride = () => this.getSubmitData(this.shouldOverrideCreditLimit);
    if (handler) {
      return handler({
        action,
        journalsMap,
        values,
        onAction,
      });
    }
    return handleAction(action, this.handleSubmit, getSubmitDataWithCreditLimitOverride);
  };

  handleSaveAsDraft = () => {
    const { handleAction } = this.props;
    return handleAction({ type: JOURNAL_ENTRY_ACTION_TYPES.SAVE_AS_DRAFT });
  };

  handleCloseTransactionsListModal = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.CLOSE_TRANSACTION_LIST,
    });
  };

  handleTransactionsListModalSubmit = () => {
    const { onAction } = this.props;
    const { data: submitData } = this.getSubmitData(this.shouldOverrideCreditLimit);
    onAction({
      type: ACTION_TYPES.IGNORE_DUPLICATE_REFERENCES_AND_SUBMIT,
      payload: { submitData },
    });
  };

  handleApprovalConfirmationModalSubmit = () => {
    const { onAction, transaction } = this.props;
    const { data: submitData } = this.getSubmitData(this.shouldOverrideCreditLimit);
    const updatedSubmitData = getSubmitDataWithFlowType(submitData, transaction);
    onAction({
      type: ACTION_TYPES.SUBMIT_FOR_APPROVAL,
      payload: { submitData: updatedSubmitData },
    });
  };

  handleCloseApprovalConfirmationModal = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.CLOSE_APPROVAL_MODAL,
    });
  };

  handleTriggerSubmit = () => {
    const { contextId } = this.props;
    triggerSubmit(contextId);
  };

  handleShortcutAction = ({ type }) => {
    const shortcutActionHandler = this.shortcutActionHandlersMap[type] || _noop;
    shortcutActionHandler();
  };

  renderRecurringScheduleUnsavedChangesModal() {
    const {
      showRecurringScheduleUnsavedChangesModal,
      isSubmittedAsDraft,
      onSecondarySubmitRecurringScheduleUnsavedModal,
      onCloseRecurringScheduleUnsavedChangesModal,
      onPrimarySubmitRecurringScheduleUnsavedModal,
      restrictedTemplateWithViewPermission,
    } = this.props;
    const submitBtnText = restrictedTemplateWithViewPermission ? __('Cancel') : undefined;
    const { data: submitData } = this.getSubmitData(this.shouldOverrideCreditLimit);
    const onSecondarySubmit = onSecondarySubmitRecurringScheduleUnsavedModal(submitData);
    return (
      <RecurringScheduleUnsavedChangesModal
        isVisible={showRecurringScheduleUnsavedChangesModal}
        isDraft={isSubmittedAsDraft}
        onSecondarySubmit={onSecondarySubmit}
        onCancel={onCloseRecurringScheduleUnsavedChangesModal}
        onSubmit={onPrimarySubmitRecurringScheduleUnsavedModal}
        submitBtnText={submitBtnText}
      />
    );
  }

  renderApprovalConfirmationModal() {
    const { showApprovalConfirmationModal, isSubmittingForApproval } = this.props;
    return (
      <Modal
        title={__('Confirmation')}
        submitBtnText={__('Submit for Approval')}
        content={__('This transaction requires an approval.')}
        onCancel={this.handleCloseApprovalConfirmationModal}
        onSubmit={this.handleApprovalConfirmationModalSubmit}
        visible={showApprovalConfirmationModal}
        secondaryBtnText={__('Cancel')}
        width={500}
        loading={isSubmittingForApproval}
        destroyOnClose
      />
    );
  }

  renderTransactionsWithSelectedReference = () => {
    const {
      transactionsWithSelectedReference,
      showDuplicateTransactions,
      showDuplicateTransactionsFooter,
      values,
      journalsMap,
      centralisedEnabledApps,
      featureFlags,
    } = this.props;
    const refText = values[FIELD_IDS.REFERENCE_TEXT];
    const routeMode = MODES.VIEW;
    return (
      <TransactionsListModal
        transactions={transactionsWithSelectedReference}
        visible={showDuplicateTransactions}
        journalsMap={journalsMap}
        selectedRefText={refText}
        onCancel={this.handleCloseTransactionsListModal}
        onSubmit={this.handleTransactionsListModalSubmit}
        hideFooter={!showDuplicateTransactionsFooter}
        routeMode={routeMode}
        centralisedEnabledApps={centralisedEnabledApps}
        featureFlags={featureFlags}
      />
    );
  };

  renderShortcutActions() {
    return <ShortcutActionList shortcutActionsConfig={SHORT_CUT_CONFIGS} onAction={this.handleShortcutAction} />;
  }

  renderControlOverrideModal() {
    const { showControlOverrideConfirmation } = this.props;
    return (
      <InvalidControlOverridePopup
        onAction={this.handleAction}
        showControlOverrideConfirmation={showControlOverrideConfirmation}
      />
    );
  }

  renderCreditLimitOverrideModal() {
    const { showCreditLimitOverridePopup } = this.props;
    return (
      <CreditLimitOverridePopup
        onAction={this.handleAction}
        showCreditLimitOverridePopup={showCreditLimitOverridePopup}
      />
    );
  }

  render() {
    const {
      headerProps,
      headerComponent,
      footerProps,
      onClose,
      values,
      errors,
      formFields,
      sections,
      footerComponent,
      contextId,
      transactionInfo,
      isCheckSupported,
      dealerInfo,
      hideElectronicSignature,
      isSubmissionInProgress,
      uploadingAttachmentsCountWhileSubmission,
      showBalanceErrorPopUp,
      useKeyBoardShortcuts,
      balanceErrorPopupContent,
      showGrossAmount,
      shouldSplitGrossAmount,
      grossAmountDetails,
      fetchingGrossDetails,
      isTransactionDetailSecured,
      contentHeight,
      headerHeight,
      bannerProps,
      isMultiControlInputDrawerVisible,
      inaccessibleDealerIds,
      shouldShowTransactionRestrictionBanner,
      shouldDisableSubmitForInAccessibleDealers,
    } = this.props;

    if (isTransactionDetailSecured)
      return <RestrictedAsset headerComponent={headerComponent} bodyHeight={contentHeight} headerProps={headerProps} />;

    const attachmentsList = values[FIELD_IDS.ATTACHMENTS];
    const updatedFooterProps = this.getUpdatedFooterProps(
      footerProps,
      this.handleSaveAsDraft,
      values,
      inaccessibleDealerIds,
      shouldDisableSubmitForInAccessibleDealers
    );

    const updatedBannerProps = this.getUpdatedBannerProps(
      bannerProps,
      inaccessibleDealerIds,
      values,
      shouldShowTransactionRestrictionBanner
    );

    const updatedFields = this.getUpdatedFields(
      formFields,
      showGrossAmount,
      shouldSplitGrossAmount,
      grossAmountDetails,
      fetchingGrossDetails,
      this.transactionBodyRef,
      inaccessibleDealerIds
    );

    return (
      <>
        <PostingContext.Provider value={values[FIELD_IDS.POSTINGS]}>
          <ConfigurableEditTransactionForm
            headerComponent={headerComponent}
            headerProps={headerProps}
            bodyProps={BODY_PROPS}
            sections={sections}
            fields={updatedFields}
            values={values}
            errors={errors}
            footerProps={updatedFooterProps}
            footerComponent={footerComponent}
            onClose={onClose}
            onAction={this.handleAction}
            contextId={contextId}
            shouldRemoveErrorOnSubmission
            transactionInfo={transactionInfo}
            isCheckSupported={isCheckSupported}
            dealerInfo={dealerInfo}
            hideElectronicSignature={hideElectronicSignature}
            isSubmissionInProgress={isSubmissionInProgress}
            showBalanceErrorPopUp={showBalanceErrorPopUp}
            useKeyBoardShortcuts={useKeyBoardShortcuts}
            balanceErrorPopupContent={balanceErrorPopupContent}
            headerHeight={headerHeight}
            bannerProps={updatedBannerProps}
            transactionBodyRef={this.transactionBodyRef}
            contentHeight={contentHeight}
          />
        </PostingContext.Provider>
        <AttachmentsUploadInProgressModal
          uploadingAttachmentsCountWhileSubmission={uploadingAttachmentsCountWhileSubmission}
          onAction={this.handleAction}
          attachmentsList={attachmentsList}
        />
        {this.renderRecurringScheduleUnsavedChangesModal()}
        {this.renderTransactionsWithSelectedReference()}
        {this.renderApprovalConfirmationModal()}
        {this.renderControlOverrideModal()}
        {this.renderCreditLimitOverrideModal()}
        {!isMultiControlInputDrawerVisible && this.renderShortcutActions()}
      </>
    );
  }
}

ConfigurableEditTransaction.propTypes = {
  journalsMap: PropTypes.object,
  headerProps: PropTypes.object,
  headerComponent: PropTypes.oneOfType([PropTypes.func, PropTypes.node]),
  footerComponent: PropTypes.oneOfType([PropTypes.func, PropTypes.node]),
  footerProps: PropTypes.object,
  onClose: PropTypes.func.isRequired,
  fields: PropTypes.object, // These are setup fields
  accountsMap: PropTypes.object,
  transaction: PropTypes.object,
  postings: PropTypes.array,
  onAction: PropTypes.func,
  contextId: PropTypes.string.isRequired,
  formFields: PropTypes.object,
  sections: PropTypes.array,
  errors: PropTypes.object,
  values: PropTypes.object,
  handleAction: PropTypes.func,
  transactionInfo: PropTypes.object,
  isCheckSupported: PropTypes.bool,
  dealerInfo: PropTypes.object,
  hideElectronicSignature: PropTypes.bool,
  isSubmissionInProgress: PropTypes.bool,
  uploadingAttachmentsCountWhileSubmission: PropTypes.number,
  showNonZeroBalanceError: PropTypes.func.isRequired,
  showBalanceErrorPopUp: PropTypes.bool,
  transactionType: PropTypes.string,
  checkIfTransactionIsUnbalanced: PropTypes.func.isRequired,
  useKeyBoardShortcuts: PropTypes.bool,
  glPostingNormalizer: PropTypes.func,
  showRecurringScheduleUnsavedChangesModal: PropTypes.bool,
  isSubmittedAsDraft: PropTypes.bool,
  onSecondarySubmitRecurringScheduleUnsavedModal: PropTypes.func,
  onPrimarySubmitRecurringScheduleUnsavedModal: PropTypes.func,
  onCloseRecurringScheduleUnsavedChangesModal: PropTypes.func,
  restrictedTemplateWithViewPermission: PropTypes.bool,
  balanceErrorPopupContent: PropTypes.string,
  shouldUpdateCountAdjusted: PropTypes.bool,
  transactionsWithSelectedReference: PropTypes.array,
  showDuplicateTransactions: PropTypes.bool,
  showDuplicateTransactionsFooter: PropTypes.bool,
  showGrossAmount: PropTypes.bool,
  shouldSplitGrossAmount: PropTypes.bool,
  fetchingGrossDetails: PropTypes.bool,
  grossAmountDetails: PropTypes.object,
  isAdvanceTemplate: PropTypes.bool,
  taxCodeList: PropTypes.array,
  showApprovalConfirmationModal: PropTypes.bool,
  isSubmittingForApproval: PropTypes.bool,
  isTransactionDetailSecured: PropTypes.bool,
  contentHeight: PropTypes.number,
  makeEditTransactionSubmitData: PropTypes.func,
  initialFormValues: PropTypes.object,
  targetIFJournalByDealerId: PropTypes.object,
  showControlOverrideConfirmation: PropTypes.bool,
  setupFieldsByDealerId: PropTypes.object,
  accountsMapByDealerId: PropTypes.object,
  isDefaultJournalValidationRequired: PropTypes.bool,
  headerHeight: PropTypes.number,
  postingNormalizer: PropTypes.func,
  journalsMapByDealerId: PropTypes.object,
  featureFlags: PropTypes.instanceOf(FeatureFlags),
  bannerProps: PropTypes.object,
  getDealerPropertyValue: PropTypes.func.isRequired,
  showCreditLimitOverridePopup: PropTypes.bool,
  transactionMetadata: PropTypes.object,
  inaccessibleDealerIds: PropTypes.array,
  shouldDisableSubmitForInAccessibleDealers: PropTypes.bool,
  centralisedEnabledApps: PropTypes.array,
  shouldOverrideInvalidControl: PropTypes.bool,
};

ConfigurableEditTransaction.defaultProps = {
  journalsMap: EMPTY_OBJECT,
  headerProps: EMPTY_OBJECT,
  headerComponent: null,
  footerComponent: undefined,
  footerProps: EMPTY_OBJECT,
  fields: EMPTY_OBJECT,
  accountsMap: EMPTY_OBJECT,
  transaction: EMPTY_OBJECT,
  postings: EMPTY_ARRAY,
  onAction: _noop,
  formFields: EMPTY_OBJECT,
  sections: EMPTY_ARRAY,
  errors: EMPTY_OBJECT,
  values: EMPTY_OBJECT,
  handleAction: _noop,
  transactionInfo: EMPTY_OBJECT,
  isCheckSupported: false,
  dealerInfo: EMPTY_OBJECT,
  hideElectronicSignature: false,
  isSubmissionInProgress: false,
  uploadingAttachmentsCountWhileSubmission: 0,
  showBalanceErrorPopUp: false,
  transactionType: GENERAL,
  useKeyBoardShortcuts: false,
  glPostingNormalizer: defaultGlPostingNormalizer,
  showRecurringScheduleUnsavedChangesModal: false,
  isSubmittedAsDraft: false,
  onSecondarySubmitRecurringScheduleUnsavedModal: _noop,
  onPrimarySubmitRecurringScheduleUnsavedModal: _noop,
  onCloseRecurringScheduleUnsavedChangesModal: _noop,
  restrictedTemplateWithViewPermission: false,
  balanceErrorPopupContent: undefined,
  shouldUpdateCountAdjusted: false,
  transactionsWithSelectedReference: EMPTY_ARRAY,
  showDuplicateTransactions: false,
  showDuplicateTransactionsFooter: false,
  showGrossAmount: false,
  shouldSplitGrossAmount: false,
  grossAmountDetails: EMPTY_OBJECT,
  fetchingGrossDetails: false,
  isAdvanceTemplate: false,
  taxCodeList: EMPTY_ARRAY,
  showApprovalConfirmationModal: false,
  isSubmittingForApproval: false,
  isTransactionDetailSecured: false,
  contentHeight: 0,
  makeEditTransactionSubmitData: makeEditJERequestDTO,
  initialFormValues: EMPTY_OBJECT,
  targetIFJournalByDealerId: EMPTY_OBJECT,
  showControlOverrideConfirmation: false,
  setupFieldsByDealerId: EMPTY_OBJECT,
  accountsMapByDealerId: EMPTY_OBJECT,
  isDefaultJournalValidationRequired: false,
  headerHeight: 0,
  postingNormalizer: defaultPostingNormalizer,
  journalsMapByDealerId: EMPTY_OBJECT,
  featureFlags: EMPTY_FEATURE_FLAGS,
  bannerProps: {
    show: false,
  },
  showCreditLimitOverridePopup: false,
  transactionMetadata: undefined,
  inaccessibleDealerIds: EMPTY_ARRAY,
  shouldDisableSubmitForInAccessibleDealers: true,
  centralisedEnabledApps: EMPTY_ARRAY,
  shouldOverrideInvalidControl: false,
};

const mapStateToProps = state => ({
  isMultiControlInputDrawerVisible: checkIfMultiControlInputDrawerIsVisible(state),
});

export default compose(
  withSize({ hasPageHeader: true, hasPageFooter: true }),
  withSetupFields,
  withPropertyConsumer,
  withProps(makeTransactionProps),
  withTransactionRestrictions,
  withGrossAmount,
  connect(mapStateToProps)
)(ConfigurableEditTransaction);
