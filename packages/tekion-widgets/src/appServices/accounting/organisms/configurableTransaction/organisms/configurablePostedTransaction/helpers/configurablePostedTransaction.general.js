/* eslint-disable import/order */
// Constants
import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { BODY_PROPS } from '../../../constants/configurableTransaction.general';
import TRANSACTION_FIELD_ID from '../../../constants/configurableTransaction.fieldIds';

// Readers
import dealerInfoReader from '@tekion/tekion-base/readers/DealerInfo';

export const makeBodyProps = (isNavigationAllowed, navigationProps, featureFlags, transaction, showKebabMenu) => ({
  ...BODY_PROPS,
  isNavigationAllowed,
  navigationProps,
  featureFlags,
  transaction,
  showKebabMenu,
});

export const makeInitialState = (values = EMPTY_OBJECT) => {
  const { [TRANSACTION_FIELD_ID.POSTINGS]: initialPostings } = values;
  return {
    formPostings: initialPostings,
    fetchingScheduleBalance: true,
  };
};

export const getValuesWithFormPostings = (values = EMPTY_OBJECT, formPostings) => {
  const valuesWithFormPostings = {
    ...values,
    [TRANSACTION_FIELD_ID.POSTINGS]: formPostings,
  };
  return valuesWithFormPostings;
};

export const getSelectedDealerId = (values = EMPTY_OBJECT, dealerInfo) => {
  const selectedDealerId = values[TRANSACTION_FIELD_ID.FRANCHISE] || dealerInfoReader.id(dealerInfo);
  return selectedDealerId;
};
