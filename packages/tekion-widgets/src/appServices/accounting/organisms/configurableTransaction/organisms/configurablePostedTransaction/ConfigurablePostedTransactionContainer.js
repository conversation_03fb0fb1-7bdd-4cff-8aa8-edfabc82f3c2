/* eslint-disable import/order */
import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Constants
import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { ACTION_HANDLER_OPTIONS } from './constants/configurablePostedTransaction.general';

// Containers
import withActions from '@tekion/tekion-components/src/connectors/withActions';

// Components
import ConfigurablePostedTransaction from './ConfigurablePostedTransaction';

// Service
import fetchScheduleBalance from '../../../../services/fetchScheduleBalance';

// Helpers
import ACTION_HANDLERS from './helpers/configurablePostedTransaction.actionHandlers';
import { makeInitialState } from './helpers/configurablePostedTransaction.general';

class ConfigurablePostedTransactionContainer extends Component {
  constructor(props) {
    super(props);
    const { values } = this.props;
    const initialState = makeInitialState(values);

    this.component = withActions(initialState, ACTION_HANDLERS, ACTION_HANDLER_OPTIONS)(ConfigurablePostedTransaction);
  }

  render() {
    const ConfigurablePostedTransactionComponent = this.component;
    return <ConfigurablePostedTransactionComponent {...this.props} />;
  }
}

ConfigurablePostedTransactionContainer.propTypes = {
  values: PropTypes.object,
  fetchScheduleBalance: PropTypes.func,
};

ConfigurablePostedTransactionContainer.defaultProps = {
  values: EMPTY_OBJECT,
  fetchScheduleBalance,
};

export default ConfigurablePostedTransactionContainer;
