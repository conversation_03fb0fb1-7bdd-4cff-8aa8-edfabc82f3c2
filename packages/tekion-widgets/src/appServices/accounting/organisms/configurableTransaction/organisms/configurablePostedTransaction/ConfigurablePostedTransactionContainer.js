/* eslint-disable import/order */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { defaultMemoize } from 'reselect';

// Constants
import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { ACTION_HANDLER_OPTIONS } from './constants/configurablePostedTransaction.general';

// Containers
import withActions from '@tekion/tekion-components/src/connectors/withActions';

// Components
import ConfigurablePostedTransaction from './ConfigurablePostedTransaction';

// Service
import fetchScheduleBalance from '../../../../services/fetchScheduleBalance';
import fetchScheduleBalanceForWorkspaceDealer from '../../../../services/fetchScheduleBalanceForWorkspaceDealer';

// Helpers
import ACTION_HANDLERS from './helpers/configurablePostedTransaction.actionHandlers';
import { makeInitialState, getSelectedDealerId } from './helpers/configurablePostedTransaction.general';
import { isCentralisedJEEnabled as checkIsCentralisedJEEnabled } from '@tekion/tekion-business/src/appServices/accounting/helpers/journalEntryRoutes';

class ConfigurablePostedTransactionContainer extends Component {
  checkIsCentralisedJEEnabled = defaultMemoize(checkIsCentralisedJEEnabled);

  getSelectedDealerId = defaultMemoize(getSelectedDealerId);

  constructor(props) {
    super(props);
    const { values } = this.props;
    const initialState = makeInitialState(values);

    this.component = withActions(initialState, ACTION_HANDLERS, ACTION_HANDLER_OPTIONS)(ConfigurablePostedTransaction);
  }

  getScheduleBalance = request => {
    const { centralisedEnabledApps, values, dealerInfo } = this.props;
    const isCentralisedJEEnabled = this.checkIsCentralisedJEEnabled(centralisedEnabledApps);
    const dealerId = this.getSelectedDealerId(values, dealerInfo);
    return isCentralisedJEEnabled
      ? fetchScheduleBalanceForWorkspaceDealer({ dealerId, request })
      : fetchScheduleBalance(request);
  };

  render() {
    const ConfigurablePostedTransactionComponent = this.component;
    return <ConfigurablePostedTransactionComponent {...this.props} fetchScheduleBalance={this.getScheduleBalance} />;
  }
}

ConfigurablePostedTransactionContainer.propTypes = {
  values: PropTypes.object,
  centralisedEnabledApps: PropTypes.object,
  dealerInfo: PropTypes.object,
};

ConfigurablePostedTransactionContainer.defaultProps = {
  values: EMPTY_OBJECT,
  centralisedEnabledApps: EMPTY_OBJECT,
  dealerInfo: EMPTY_OBJECT,
};

export default ConfigurablePostedTransactionContainer;
