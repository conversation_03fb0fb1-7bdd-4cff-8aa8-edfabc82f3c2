/* eslint-disable import/order */
import React from 'react';
import { defaultMemoize } from 'reselect';
import PropTypes from 'prop-types';
import { compose, withProps } from 'recompose';

// Lodash
import _noop from 'lodash/noop';
import _isEmpty from 'lodash/isEmpty';

// Builders
import ConfigurableEntityMetadata from '@tekion/tekion-business/src/builders/ConfigurableEntityMetadata';

// Helpers
import makeSections from './helpers/configurablePostedTransaction.sections';
import makeFields from './helpers/configurablePostedTransaction.fields';
import { getUpdatedFields } from './helpers/configurablePostedTransaction.overrideFields';
import { getShortCutConfig } from './helpers/configurablePostedTransaction.shortcutActions';
import { makeBodyProps, getValuesWithFormPostings } from './helpers/configurablePostedTransaction.general';
import { shouldDisableRedirectionForChildDealer } from '@tekion/tekion-business/src/appServices/accounting/helpers/journalEntry/journalEntry.general';

// Constants
import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import TRANSACTION_FIELD_ID from '../../constants/configurableTransaction.fieldIds';
import CONFIGURABLE_FORM_FIELD_VALUE_GETTERS_BY_TRANSACTION_TYPE from '../../constants/configurableTransaction.formFieldValueGetters';
import { CONTEXT_ID } from './constants/configurablePostedTransaction.general';
import { SHORTCUT_ACTION_TYPE } from './constants/configurablePostedTransaction.shortcutActions';
import ACTION_TYPES from './constants/configurablePostedTransaction.actionTypes';

// Components
import Loader from '@tekion/tekion-components/src/molecules/loader';
import FormPage from '@tekion/tekion-components/pages/formPage';
import AttachmentsUploadInProgressModal from '../../../../molecules/attachmentsUploadInProgressModal';
import RestrictedAsset from '@tekion/tekion-components/src/molecules/restrictedAsset/index';
import ShortcutActionList from '@tekion/tekion-components/src/molecules/shortcutActionList';
import { triggerSubmit } from '@tekion/tekion-components/pages/formPage/utils/formAction';
import ConfigurableTransactionPostingBody from '../configurableTransactionPostingBody';

// Containers
import { withGrossAmount } from '../../../transactionPostingForm';
import { withFeatureFlags, EMPTY_FEATURE_FLAGS } from '../../../../../../context/featureFlags';
import withPropertyConsumer from '@tekion/tekion-components/src/organisms/propertyProvider/withPropertyConsumer';
import withPostingNotesCount from '../../../transactionPostingForm/containers/withPostingNotesCount';

// Utils
import getIsMultiTabSessionsEnabled from '@tekion/tekion-components/src/utils/getIsMultiTabSessionsEnabled';

// Readers
import dealerInfoReader from '@tekion/tekion-base/readers/DealerInfo';

class ConfigurablePostedTransaction extends React.Component {
  makeSections = defaultMemoize(makeSections);

  makeFields = defaultMemoize(makeFields);

  getUpdatedFields = defaultMemoize(getUpdatedFields);

  getShortCutConfig = defaultMemoize(getShortCutConfig);

  makeBodyProps = defaultMemoize(makeBodyProps);

  getValuesWithFormPostings = defaultMemoize(getValuesWithFormPostings);

  componentDidMount() {
    this.fetchScheduleBalance();
  }

  getSections() {
    const { entityMetadata, values } = this.props;
    const showAutoReversalField = values[TRANSACTION_FIELD_ID.AUTO_REVERSING];
    const sections = this.makeSections(
      entityMetadata.getEntityConfig(),
      entityMetadata.getDetailViewConfig(),
      showAutoReversalField
    );
    return sections;
  }

  getFields() {
    const {
      entityMetadata,
      journalsMap,
      programFieldMap,
      dealerInfo,
      dealerInfoByDealerId,
      setupFields,
      accountsMap,
      transactionType,
      accountingSettings,
      shouldEnableEditing,
      featureFlags,
      allAccountsMapByDealerId,
      journalsMapByDealerId,
      values = EMPTY_OBJECT,
      shouldSupportNotes,
      postingNotesCountMap,
      onNotesCountUpdate,
      transaction,
      childDealerIds,
      getDealerPropertyValue,
      hideReferenceRedirectionIcon,
    } = this.props;
    const selectedDealerId =
      values[TRANSACTION_FIELD_ID.FRANCHISE] ||
      values[TRANSACTION_FIELD_ID.ESTABLISHMENT] ||
      dealerInfoReader.id(dealerInfo);
    const isMultiTabSessionsEnabled = getIsMultiTabSessionsEnabled(getDealerPropertyValue);
    const shouldHideReferenceRedirectionIcon =
      hideReferenceRedirectionIcon ||
      shouldDisableRedirectionForChildDealer(childDealerIds, selectedDealerId, isMultiTabSessionsEnabled);
    const fields = this.makeFields(
      entityMetadata.getEntityConfig(),
      entityMetadata.getDetailViewConfig(),
      journalsMap,
      programFieldMap,
      dealerInfo,
      setupFields,
      accountsMap,
      transactionType,
      accountingSettings,
      shouldEnableEditing,
      undefined,
      featureFlags,
      allAccountsMapByDealerId,
      journalsMapByDealerId,
      selectedDealerId,
      dealerInfoByDealerId,
      shouldSupportNotes,
      postingNotesCountMap,
      onNotesCountUpdate,
      undefined,
      transaction,
      shouldHideReferenceRedirectionIcon
    );
    return fields;
  }

  getBodyProps() {
    const { isNavigationAllowed, navigationProps, featureFlags, transaction, showKebabMenu } = this.props;
    return this.makeBodyProps(isNavigationAllowed, navigationProps, featureFlags, transaction, showKebabMenu);
  }

  handlePostedJEShortcutAction = () => {
    const { contextId } = this.props;
    triggerSubmit(contextId);
  };

  handleApprovalJEShortcutAction = () => {
    const { openApprovalRequestDrawer } = this.props;
    openApprovalRequestDrawer();
  };

  // eslint-disable-next-line react/sort-comp
  shortcutActionHandlersMap = {
    [SHORTCUT_ACTION_TYPE.SHOW_REVIEW_APPROVAL_DRAWER]: this.handleApprovalJEShortcutAction,
    [SHORTCUT_ACTION_TYPE.SUBMIT_JE]: this.handlePostedJEShortcutAction,
  };

  handleShortcutAction = ({ type }) => {
    const shortcutActionHandler = this.shortcutActionHandlersMap[type] || _noop;
    shortcutActionHandler();
  };

  fetchScheduleBalance() {
    const { onAction } = this.props;
    onAction({ type: ACTION_TYPES.FETCH_SCHEDULE_BALANCE });
  }

  renderUploadProgressModal() {
    const { values, onAction, uploadingAttachmentsCountWhileSubmission } = this.props;
    const attachmentsList = values[TRANSACTION_FIELD_ID.ATTACHMENTS];
    return (
      <AttachmentsUploadInProgressModal
        uploadingAttachmentsCountWhileSubmission={uploadingAttachmentsCountWhileSubmission}
        onAction={onAction}
        attachmentsList={attachmentsList}
      />
    );
  }

  renderShortcutAction() {
    const { approvalDetails, shouldDisableSubmitAction, isApprovalRequestDrawerVisible } = this.props;
    const shortCutConfig = this.getShortCutConfig(
      approvalDetails,
      shouldDisableSubmitAction,
      isApprovalRequestDrawerVisible
    );
    return <ShortcutActionList shortcutActionsConfig={shortCutConfig} onAction={this.handleShortcutAction} />;
  }

  render() {
    const {
      headerComponent,
      headerProps,
      footerProps,
      onClose,
      onAction,
      values,
      errors,
      footerComponent,
      contentHeight,
      showGrossAmount,
      shouldSplitGrossAmount,
      grossAmountDetails,
      fetchingGrossDetails,
      isTransactionDetailSecured,
      formFields,
      contextId,
      shouldSupportNotes,
      postingNotesCountMap,
      onNotesCountUpdate,
      centralisedEnabledApps,
      featureFlags,
      formSections,
      shouldRemoveEditPostingLinesAction,
      fetchingScheduleBalance,
      formPostings,
    } = this.props;

    if (fetchingScheduleBalance) {
      return <Loader />;
    }

    if (isTransactionDetailSecured)
      return <RestrictedAsset headerComponent={headerComponent} bodyHeight={contentHeight} headerProps={headerProps} />;

    const initialFields = _isEmpty(formFields) ? this.getFields() : formFields;

    const updatedFields = this.getUpdatedFields(
      initialFields,
      showGrossAmount,
      shouldSplitGrossAmount,
      grossAmountDetails,
      fetchingGrossDetails,
      shouldSupportNotes,
      postingNotesCountMap,
      onNotesCountUpdate,
      centralisedEnabledApps,
      featureFlags,
      shouldRemoveEditPostingLinesAction
    );

    const sections = _isEmpty(formSections) ? this.getSections() : formSections;

    const valuesWithFormPostings = this.getValuesWithFormPostings(values, formPostings);

    return (
      <>
        <FormPage
          contextId={contextId}
          headerComponent={headerComponent}
          headerProps={headerProps}
          bodyComponent={ConfigurableTransactionPostingBody}
          onAction={onAction}
          bodyProps={this.getBodyProps()}
          sections={sections}
          fields={updatedFields}
          onClose={onClose}
          footerProps={footerProps}
          values={valuesWithFormPostings}
          errors={errors}
          footerComponent={footerComponent}
          contentHeight={contentHeight}
        />
        {this.renderUploadProgressModal()}
        {this.renderShortcutAction()}
      </>
    );
  }
}

ConfigurablePostedTransaction.propTypes = {
  entityMetadata: PropTypes.instanceOf(ConfigurableEntityMetadata).isRequired,
  journalsMap: PropTypes.object,
  headerProps: PropTypes.object,
  headerComponent: PropTypes.elementType,
  footerProps: PropTypes.object,
  onClose: PropTypes.func,
  values: PropTypes.object.isRequired,
  errors: PropTypes.object,
  programFieldMap: PropTypes.object,
  dealerInfo: PropTypes.object,
  dealerInfoByDealerId: PropTypes.object,
  setupFields: PropTypes.object,
  accountsMap: PropTypes.object,
  footerComponent: PropTypes.oneOfType([PropTypes.func, PropTypes.node]),
  onAction: PropTypes.func,
  transactionType: PropTypes.string,
  accountingSettings: PropTypes.object,
  shouldEnableEditing: PropTypes.bool,
  contentHeight: PropTypes.number.isRequired,
  uploadingAttachmentsCountWhileSubmission: PropTypes.number,
  showGrossAmount: PropTypes.bool,
  shouldSplitGrossAmount: PropTypes.bool,
  fetchingGrossDetails: PropTypes.bool,
  grossAmountDetails: PropTypes.object,
  isTransactionDetailSecured: PropTypes.bool,
  shouldDisableSubmitAction: PropTypes.bool,
  isApprovalRequestDrawerVisible: PropTypes.bool,
  approvalDetails: PropTypes.object,
  openApprovalRequestDrawer: PropTypes.func,
  formFields: PropTypes.object,
  contextId: PropTypes.string,
  featureFlags: PropTypes.array,
  allAccountsMapByDealerId: PropTypes.object,
  journalsMapByDealerId: PropTypes.object,
  shouldSupportNotes: PropTypes.bool,
  onNotesCountUpdate: PropTypes.func,
  postingNotesCountMap: PropTypes.object,
  centralisedEnabledApps: PropTypes.object,
  formSections: PropTypes.array,
  isNavigationAllowed: PropTypes.bool,
  navigationProps: PropTypes.object,
  shouldRemoveEditPostingLinesAction: PropTypes.bool,
  transaction: PropTypes.object,
  getDealerPropertyValue: PropTypes.func,
  childDealerIds: PropTypes.array,
  showKebabMenu: PropTypes.bool,
  hideReferenceRedirectionIcon: PropTypes.bool,
  fetchingScheduleBalance: PropTypes.bool,
  formPostings: PropTypes.array,
};

ConfigurablePostedTransaction.defaultProps = {
  journalsMap: EMPTY_OBJECT,
  headerProps: EMPTY_OBJECT,
  headerComponent: null,
  footerProps: EMPTY_OBJECT,
  programFieldMap: EMPTY_OBJECT,
  dealerInfo: EMPTY_OBJECT,
  dealerInfoByDealerId: EMPTY_OBJECT,
  setupFields: EMPTY_OBJECT,
  accountsMap: EMPTY_OBJECT,
  footerComponent: null,
  onAction: _noop,
  transactionType: undefined,
  accountingSettings: EMPTY_OBJECT,
  shouldEnableEditing: true,
  errors: EMPTY_OBJECT,
  onClose: _noop,
  uploadingAttachmentsCountWhileSubmission: 0,
  showGrossAmount: false,
  shouldSplitGrossAmount: false,
  grossAmountDetails: EMPTY_OBJECT,
  fetchingGrossDetails: false,
  isTransactionDetailSecured: false,
  shouldDisableSubmitAction: false,
  isApprovalRequestDrawerVisible: false,
  approvalDetails: EMPTY_OBJECT,
  openApprovalRequestDrawer: _noop,
  formFields: EMPTY_OBJECT,
  contextId: CONTEXT_ID,
  featureFlags: EMPTY_FEATURE_FLAGS,
  allAccountsMapByDealerId: EMPTY_OBJECT,
  journalsMapByDealerId: EMPTY_OBJECT,
  shouldSupportNotes: false,
  onNotesCountUpdate: _noop,
  postingNotesCountMap: EMPTY_OBJECT,
  centralisedEnabledApps: EMPTY_OBJECT,
  formSections: EMPTY_ARRAY,
  isNavigationAllowed: false,
  navigationProps: EMPTY_OBJECT,
  shouldRemoveEditPostingLinesAction: false,
  getDealerPropertyValue: _noop,
  childDealerIds: EMPTY_ARRAY,
  transaction: EMPTY_OBJECT,
  showKebabMenu: false,
  hideReferenceRedirectionIcon: false,
  fetchingScheduleBalance: true,
  formPostings: EMPTY_ARRAY,
};
export default compose(
  withFeatureFlags,
  withPostingNotesCount,
  withProps({ fieldValueGettersByTxType: CONFIGURABLE_FORM_FIELD_VALUE_GETTERS_BY_TRANSACTION_TYPE }),
  withGrossAmount,
  withPropertyConsumer
)(ConfigurablePostedTransaction);
