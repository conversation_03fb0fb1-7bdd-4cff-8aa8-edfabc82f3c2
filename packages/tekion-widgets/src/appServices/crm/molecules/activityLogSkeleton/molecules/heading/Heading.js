import React from 'react';
import PropTypes from 'prop-types';
import _times from 'lodash/times';

import { renderSkeletonItem } from './helpers/heading.component';
import { DEFAULT_SKELETON_ITEMS_COUNT } from './constants/heading.general';

import styles from './heading.module.scss';

const Heading = props => {
  const { skeletonItemsCount } = props;
  return (
    <div className={`flex p-b-12 m-b-20 ${styles.headingContainer}`}>
      {_times(skeletonItemsCount, renderSkeletonItem)}
    </div>
  );
};

Heading.propTypes = {
  skeletonItemsCount: PropTypes.number,
};

Heading.defaultProps = {
  skeletonItemsCount: DEFAULT_SKELETON_ITEMS_COUNT,
};

export default Heading;
