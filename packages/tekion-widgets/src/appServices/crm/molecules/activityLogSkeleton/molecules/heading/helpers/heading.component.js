import React from 'react';

import Skeleton from '@tekion/tekion-components/src/molecules/skeleton';

import { PARAGRAPH_PROPS } from '../constants/heading.general';

import styles from '../heading.module.scss';

export const renderSkeletonItem = skeletonId => (
  <div key={skeletonId} className={styles.skeletonItem}>
    <Skeleton active title={false} paragraph={PARAGRAPH_PROPS} className="m-b-0" />
  </div>
);
