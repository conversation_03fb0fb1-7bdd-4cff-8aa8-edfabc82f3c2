import React from 'react';
import PropTypes from 'prop-types';

import Header from './molecules/header';
import InfoSection from './molecules/infoSection';

const EntityInformationSkeleton = props => {
  const { className } = props;
  return (
    <div className={`p-16 ${className}`}>
      <Header />
      <InfoSection rowCount={1} />
      <InfoSection rowCount={6} />
    </div>
  );
};

EntityInformationSkeleton.propTypes = {
  className: PropTypes.string,
};

EntityInformationSkeleton.defaultProps = {
  className: '',
};

export default EntityInformationSkeleton;
