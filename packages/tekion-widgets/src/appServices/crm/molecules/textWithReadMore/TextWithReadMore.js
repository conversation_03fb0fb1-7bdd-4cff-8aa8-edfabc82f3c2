import React, { useCallback } from 'react';
import PropTypes from 'prop-types';

import _size from 'lodash/size';
import useToggle from '@tekion/tekion-base/customHooks/useToggle';
import { getDisplayText, getBtnLabel } from './helpers/textWithReadMore.general';
import { MIN_TEXT_LENGTH } from './constants/textWithReadMore.general';

import styles from './textWithReadMore.module.scss';

const TextWithReadMore = ({ heading, text, minLength, className }) => {
  const [showFullText, toggleShowFullText] = useToggle(false);

  const displayText = getDisplayText(text, showFullText, minLength);
  const btnLabel = getBtnLabel(displayText, minLength);

  const isShowLabelRequired = _size(text) > minLength;

  const onToggle = useCallback(() => {
    toggleShowFullText();
  }, [toggleShowFullText]);

  const renderButton = () =>
    isShowLabelRequired && (
      <span
        onClick={onToggle}
        className={`${styles.readMore} cursor-pointer`}
        role="button"
        tabIndex="0"
        aria-label={btnLabel}
      >
        {btnLabel}{' '}
      </span>
    );

  return (
    <div className={`${className} ${styles.paragraph} mt-3 overflow-hidden`}>
      <span className={styles.heading}>{heading}: </span>
      <span className={styles.description}>{displayText}</span>
      {renderButton()}
    </div>
  );
};

TextWithReadMore.propTypes = {
  text: PropTypes.string.isRequired,
  heading: PropTypes.string.isRequired,
  minLength: PropTypes.number,
  className: PropTypes.string,
};

TextWithReadMore.defaultProps = {
  minLength: MIN_TEXT_LENGTH,
  className: '',
};

export default React.memo(TextWithReadMore);
