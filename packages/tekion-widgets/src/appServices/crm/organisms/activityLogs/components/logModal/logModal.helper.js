import React from 'react';
import _map from 'lodash/map';
import _isEmpty from 'lodash/isEmpty';

import { getToday, getCurrentTime } from '@tekion/tekion-base/utils/dateUtils';
import { EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { CALL_STATUSES } from '@tekion/tekion-widgets/src/organisms/communications/constants/communications.messages';
import { getContactWithPrefix } from '@tekion/tekion-widgets/src/appServices/crm/molecules/coordinateSelector/helpers/coordinateSelector.helpers';
import ENTITY_TYPE from '@tekion/tekion-base/constants/entityTypes';
import LeadReader from '@tekion/tekion-base/readers/lead.reader';
import castArrayIfPresent from '@tekion/tekion-base/utils/castArrayIfPresent';
import { getAllContactNumbersOfLeads } from '@tekion/tekion-business/src/helpers/crm/lead.helpers';
import { ENTITIES } from '@tekion/tekion-base/bulkResolvers/entities';
import Resolver from '@tekion/tekion-base/bulkResolvers';
import LeadsAPI from '@tekion/tekion-base/services/crm/leads.api';

import NDCCoordinateMask from '../../../../molecules/ndcCoordinateMask';
import { ACTION_TYPES } from '../../activityLogs.constants';
import { FIELD_IDS, TEXT_TYPES, CALL_TYPES, CALL_STATUS_VS_NOTES_PREFIX } from './logModal.constants';

import styles from './logModal.module.scss';
import { DEPARTMENTS, makeCommunicationPreferencePayload } from '../../../ndc';

import { getNDCInfoForLead } from '../../../ndc/ndc.helper';

const getInitialNote = logType =>
  logType === ACTION_TYPES.LOG_CALL
    ? CALL_STATUS_VS_NOTES_PREFIX[CALL_STATUSES.CALLED_AND_LEFT_VOICEMAIL]
    : EMPTY_STRING;

export const getInitialLogInfo = logType => ({
  [FIELD_IDS.TO]: EMPTY_STRING,
  [FIELD_IDS.DATE]: getToday(),
  [FIELD_IDS.TIME]: getCurrentTime(),
  [FIELD_IDS.BODY]: getInitialNote(logType),
  [FIELD_IDS.TEXT_TYPE]: TEXT_TYPES.OUTBOUND,
  [FIELD_IDS.CALL_TYPE]: CALL_TYPES.OUTBOUND,
  [FIELD_IDS.CALL_STATUS]: CALL_STATUSES.CALLED_AND_LEFT_VOICEMAIL,
});

export const transformAPIDataToFormData = log => log;

export const updateLogModalCoordinateOptions = (log, leadDetails, ndcPreferences = EMPTY_OBJECT) => {
  const { options } = log;
  const tempOptions = _map(options, ({ value, label, countryCode, key }) => {
    const ndcInfo = getNDCInfoForLead(ndcPreferences, value, leadDetails);

    if (!_isEmpty(ndcInfo)) {
      return {
        value,
        label: (
          <div className={`d-flex align-items-center ${styles.option}`}>
            <NDCCoordinateMask
              {...ndcInfo}
              label={value}
              labelWithSufix={label}
              labelSuffix={key}
              countryCode={countryCode}
            />
          </div>
        ),
        countryCode,
      };
    }

    return {
      label: getContactWithPrefix(label, countryCode),
      value,
      countryCode,
    };
  });

  return {
    ...log,
    options: tempOptions,
  };
};

export const makeFetchCommunicationPreferencePayload = leadDetails => {
  const coordinates = getAllContactNumbersOfLeads(leadDetails);
  const entityId = LeadReader.leadId(leadDetails);
  const communicationPreference = makeCommunicationPreferencePayload({
    coordinates,
    entityType: ENTITY_TYPE.LEAD,
    entityId,
    department: DEPARTMENTS.COMMUNICATION_RETAIL,
  });
  const communicationPreferences = castArrayIfPresent(communicationPreference);

  return { requests: communicationPreferences };
};

export const getResolvedLeadDetails = async ({
  shouldFetchLeadData = false,
  leadDetails = EMPTY_OBJECT,
  params = EMPTY_OBJECT,
}) => {
  if (shouldFetchLeadData) {
    const { leadId } = leadDetails;
    const lead = await LeadsAPI.getLead(leadId, params);
    const resolvedDetails = await Resolver.getResolvedData(ENTITIES.LEADS, [lead]);
    return resolvedDetails;
  }

  const resolvedDetails = await Resolver.getResolvedData(ENTITIES.LEADS, [leadDetails]);
  return resolvedDetails;
};
