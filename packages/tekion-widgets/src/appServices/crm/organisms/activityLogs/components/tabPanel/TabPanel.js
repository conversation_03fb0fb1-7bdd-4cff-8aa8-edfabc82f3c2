import React, { PureComponent, createRef } from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';
import _filter from 'lodash/filter';
import _map from 'lodash/map';
import _noop from 'lodash/noop';
import _sortBy from 'lodash/sortBy';
import _includes from 'lodash/includes';
import _stubTrue from 'lodash/stubTrue';

// Components
import { EMPTY_STRING, EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';

import isLeadDetailsEnhancementEnabled from '@tekion/tekion-business/src/appServices/crm/helpers/isLeadDetailsEnhancementEnabled';

import Button from '@tekion/tekion-components/src/atoms/Button';
import Tag from '@tekion/tekion-components/src/atoms/Tag';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import FontIcon, { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';
import Tabs from '@tekion/tekion-components/src/molecules/Tabs/Tabs';
import COLORS from '@tekion/tekion-styles-next/scss/exports.scss';
import DealerPropertyHelper from '@tekion/tekion-components/src/helpers/sales/dealerPropertyHelper';
import { withTekionConversion } from '@tekion/tekion-conversion-web';

import {
  ACTION_TYPES_CONFIG,
  ACTION_KEYS,
  ACTIVITY_CONTAINER_CLASSNAME,
  ACTION_TYPES,
  ACTIVITY_TYPES_WITHOUT_COUNT,
} from '../../activityLogs.constants';
import TabBody from './TabBody';
import ConfigReader from '../../activityLogsConfig.reader';
import { showActivityCount, getActivitiesCountByTab } from '../../activityLogs.helper';
import { TAB_VS_ALLOW_RIGHT_ACTIONS_FUNC } from '../../helpers/activityLogs.tabConfig.helpers';
import styles from '../../activityLogs.module.scss';
import { getValidRightActions } from '../../helpers/activityLogs.actionConfig';
import { ActivityLogBodySkeleton } from '../../../../molecules/activityLogSkeleton';

class TabPanel extends PureComponent {
  constructor(props) {
    super(props);
    this.leftActionsContainerRef = createRef(null);
    this.rightActionsContainerRef = createRef(null);
  }

  onClickLeftAction = action => {
    const { onClickLeftAction } = this.props;
    onClickLeftAction(action);
  };

  onClickRightAction = type => {
    const { onAction, filter } = this.props;
    const actions = {
      type,
      payload: {
        filter,
      },
    };
    onAction(actions);
  };

  onAction = action => {
    const { onAction } = this.props;
    const { filter } = this.props;
    const { type, payload } = action;
    const actions = {
      type,
      payload: {
        filter,
        data: payload?.response,
      },
    };
    onAction(actions);
  };

  getLeftActions = () => {
    const { activeTab, filter, config = EMPTY_OBJECT, activityTabsCount, getFormattedNumber } = this.props;

    const leftActions = ConfigReader.leftActions(config, activeTab);

    return (
      <div ref={this.leftActionsContainerRef} className="d-flex">
        {_map(leftActions, ({ key, value }) => (
          <Tag
            key={key}
            className={cx('cursor-pointer m-r-16', styles.tag, { selectedItem: filter === key })}
            onClick={() => this.onClickLeftAction(key)}
            contentClassName={cx('text-center', styles.content, { [styles.contentSelected]: filter === key })}>
            {showActivityCount(activeTab, key, value, activityTabsCount, getFormattedNumber)}
          </Tag>
        ))}
      </div>
    );
  };

  getRightActions = shouldShowRightActionText => {
    const {
      activeTab,
      leadDetails,
      config = EMPTY_OBJECT,
      filterPreferences,
      updateFilterPreferences,
      onAction,
      getDealerPropertyValue,
      isTestDriveCrmSetupEnabled,
      settings,
      isDealOpen,
      isCTCDisabled,
    } = this.props;
    const allowRightActionsGetter = TAB_VS_ALLOW_RIGHT_ACTIONS_FUNC[activeTab] || _stubTrue;
    const allowRightActions = allowRightActionsGetter(leadDetails, { isDealOpen, getDealerPropertyValue });
    const rightActions = ConfigReader.rightActions(config, activeTab);
    const validRightActions = getValidRightActions(rightActions, leadDetails);
    const shouldDisplayDropdown = key => {
      if (isCTCDisabled && ACTION_TYPES.CALL === key) {
        return false;
      }
      return true;
    };

    if (!allowRightActions) return null;

    return (
      <div ref={this.rightActionsContainerRef} className={`d-flex ${styles.communicationsLog}`}>
        {_map(validRightActions, ({ key, value, Component, onFontIconClickHandler = _noop }) => (
          <Component
            shouldDisplayDropdown={shouldDisplayDropdown(key)}
            key={key}
            filterPreferences={filterPreferences}
            updateFilterPreferences={updateFilterPreferences}
            className="d-flex m-x-8 cursor-pointer align-items-center"
            role="presentation"
            onClick={({ key: filterKey }) =>
              this.onClickRightAction(key === ACTION_TYPES.LOG_COMMUNICATIONS ? filterKey : key)
            }
            leadDetails={leadDetails}
            onClickRightAction={this.onClickRightAction}
            render={({ isDisabled } = EMPTY_OBJECT) => (
              <>
                <FontIcon
                  onClick={onFontIconClickHandler(isCTCDisabled, this.onClickRightAction, key, leadDetails)}
                  disabled={isDisabled}
                  className="m-x-8 cursor-pointer"
                  size={SIZES.M}>
                  {ACTION_TYPES_CONFIG[key]?.icon}
                </FontIcon>
                <PropertyControlledComponent controllerProperty={shouldShowRightActionText}>
                  <Button
                    className={cx('is-borderless', styles.rightActionContent, {
                      [styles.rightActionContentDisabled]: isDisabled,
                    })}
                    view={Button.VIEW.TERTIARY}>
                    {value}
                  </Button>
                </PropertyControlledComponent>
              </>
            )}
            onAction={onAction}
            getDealerPropertyValue={getDealerPropertyValue}
            isTestDriveCrmSetupEnabled={isTestDriveCrmSetupEnabled}
            settings={settings}
          />
        ))}
      </div>
    );
  };

  renderComponent = () => {
    const { activeTab, config: tabConfig } = this.props;
    const { renderer: Component, renderOptions } = tabConfig[activeTab] || EMPTY_OBJECT;
    return Component ? <Component {...renderOptions(this.props)} /> : null;
  };

  renderTabBody = key => {
    const {
      actions,
      leadDetails,
      activityLogs,
      create,
      refresh,
      users,
      noActivityConfig,
      pastLeadsData,
      viewType,
      filter,
      isCustomerOneViewEnabled,
      createBulkActivityLogsPayload,
      createBulkActivityLogsPayloadV2,
      hasMoreActivity,
      isTestDriveCrmSetupEnabled,
      getDealerPropertyValue,
      settings,
      activeTab,
      config: tabConfig,
      dealerInfo,
      communicationOptinPreference,
      AISummaryContainerViewType,
      hasAISummaryFeedbackSubmitted,
      updateFilterPreferences,
      setHasMoreActivities,
      setActivityLogsGetterFns,
      paymentOptionConfigs,
      isCTCDisabled,
    } = this.props;

    const { wrapperClassname, hideDefaultScrollableList } = tabConfig[activeTab] || EMPTY_OBJECT;
    return (
      <TabBody
        isCTCDisabled={isCTCDisabled}
        actions={actions}
        leadDetails={leadDetails}
        activityLogs={activityLogs}
        leftActions={this.getLeftActions}
        rightActions={this.getRightActions}
        leftActionsContainerRef={this.leftActionsContainerRef}
        rightActionsContainerRef={this.rightActionsContainerRef}
        render={this.renderComponent}
        create={create}
        refresh={refresh}
        onAction={this.onAction}
        users={users}
        noActivityConfig={noActivityConfig}
        pastLeadsData={pastLeadsData}
        tabKey={key}
        viewType={viewType}
        filter={filter}
        isCustomerOneViewEnabled={isCustomerOneViewEnabled}
        createBulkActivityLogsPayload={createBulkActivityLogsPayload}
        createBulkActivityLogsPayloadV2={createBulkActivityLogsPayloadV2}
        hasMoreActivity={hasMoreActivity}
        isTestDriveCrmSetupEnabled={isTestDriveCrmSetupEnabled}
        getDealerPropertyValue={getDealerPropertyValue}
        settings={settings}
        wrapperClassname={wrapperClassname}
        hideDefaultScrollableList={hideDefaultScrollableList}
        dealerInfo={dealerInfo}
        communicationOptinPreference={communicationOptinPreference}
        AISummaryContainerViewType={AISummaryContainerViewType}
        hasAISummaryFeedbackSubmitted={hasAISummaryFeedbackSubmitted}
        updateFilterPreferences={updateFilterPreferences}
        setHasMoreActivities={setHasMoreActivities}
        setActivityLogsGetterFns={setActivityLogsGetterFns}
        paymentOptionConfigs={paymentOptionConfigs}
      />
    );
  };

  renderTabName = ({ name, key }) => {
    const { activityTabsCount, notRespondedTabs, activeTab, getFormattedNumber } = this.props;

    let activityLogsCount = getActivitiesCountByTab(key, activityTabsCount, getFormattedNumber);
    if (_includes(ACTIVITY_TYPES_WITHOUT_COUNT, key)) {
      activityLogsCount = EMPTY_STRING;
    }

    return (
      <div className="d-flex align-items-center">
        <div
          className={cx(
            styles.activityTab,
            {
              [styles.activeTab]: key === activeTab,
            },
            { [styles.inactiveTab]: key !== activeTab }
          )}>
          {name} {activityLogsCount}
        </div>
        <PropertyControlledComponent controllerProperty={_includes(notRespondedTabs, key)}>
          <FontIcon className={cx('m-l-4', styles.unreadIcon)} size={FontIcon.SIZES.XXS}>
            icon-circle-filled
          </FontIcon>
        </PropertyControlledComponent>
      </div>
    );
  };

  renderTabPanes = () => {
    const { tabs = EMPTY_ARRAY, hasScrollReachedBottom, getDealerPropertyValue } = this.props;
    const ACTIVITY_LOGS_TABS = _sortBy(_filter(tabs, 'visible'), ['sequence']);
    return ACTIVITY_LOGS_TABS.map(tab => {
      const { isFetching, panelId, activeTab } = this.props;
      const isTabActive = activeTab === tab.key;

      const shouldConsiderParentScroll = isLeadDetailsEnhancementEnabled(getDealerPropertyValue);
      return (
        <Tabs.TabPane tab={this.renderTabName(tab)} key={tab.key}>
          <div
            className={cx(
              styles.tabsPane,
              `overflow-hidden d-flex flex-column relative ${ACTIVITY_CONTAINER_CLASSNAME}`,
              { [styles.tabsPaneV2]: shouldConsiderParentScroll && !hasScrollReachedBottom }
            )}
            id={`${panelId}_${tab.key}`}>
            {isFetching || !isTabActive ? <ActivityLogBodySkeleton /> : this.renderTabBody(tab.key)}
          </div>
        </Tabs.TabPane>
      );
    });
  };

  render() {
    const { activeTab, onTabChange, tabsConfigurator, tabClassName } = this.props;
    return (
      <div className={`full-width overflow-hidden d-flex ${styles.tabsWrapper} ${tabClassName}`}>
        <Tabs
          className={cx('full-width d-flex flex-column', styles.leadActivityTabs)}
          activeKey={activeTab}
          tabBarGutter={0}
          onChange={onTabChange}
          tabBarStyle={{ background: COLORS.white }}
          tabBarExtraContent={tabsConfigurator()}>
          {this.renderTabPanes()}
        </Tabs>
      </div>
    );
  }
}

TabPanel.propTypes = {
  actions: PropTypes.object.isRequired,
  activeTab: PropTypes.string,
  activityLogs: PropTypes.object.isRequired,
  activityTabsCount: PropTypes.object.isRequired,
  filter: PropTypes.string,
  leadDetails: PropTypes.object.isRequired,
  onTabChange: PropTypes.func,
  onAction: PropTypes.func,
  isFetching: PropTypes.bool,
  refresh: PropTypes.func,
  create: PropTypes.func,
  bottomSpacing: PropTypes.string,
  onClickLeftAction: PropTypes.func,
  config: PropTypes.object,
  panelId: PropTypes.string.isRequired,
  tabs: PropTypes.array.isRequired,
  unreadTabs: PropTypes.array.isRequired,
  users: PropTypes.object.isRequired,
  noActivityConfig: PropTypes.shape({
    image: PropTypes.string,
    text: PropTypes.string,
  }),
  viewType: PropTypes.string.isRequired,
  pastLeadsData: PropTypes.array,
  isCustomerOneViewEnabled: PropTypes.bool,
  createBulkActivityLogsPayload: PropTypes.func,
  createBulkActivityLogsPayloadV2: PropTypes.func,
  hasMoreActivity: PropTypes.bool,
  filterPreferences: PropTypes.array,
  updateFilterPreferences: PropTypes.func,
  isTestDriveCrmSetupEnabled: PropTypes.bool,
  getDealerPropertyValue: PropTypes.func,
  settings: PropTypes.object,
  isDealOpen: PropTypes.object,
  dealerInfo: PropTypes.object,
  communicationOptinPreference: PropTypes.object,
  tabClassName: PropTypes.string,
  hasScrollReachedBottom: PropTypes.bool,
  AISummaryContainerViewType: PropTypes.string,
  hasAISummaryFeedbackSubmitted: PropTypes.bool,
  setHasMoreActivities: PropTypes.func,
  setActivityLogsGetterFns: PropTypes.func,
  paymentOptionConfigs: PropTypes.array,
  isCTCDisabled: PropTypes.bool,
};

TabPanel.defaultProps = {
  activeTab: EMPTY_STRING,
  isFetching: false,
  filter: ACTION_KEYS.ALL,
  onTabChange: _noop,
  onAction: _noop,
  create: _noop,
  refresh: _noop,
  onClickLeftAction: _noop,
  bottomSpacing: EMPTY_STRING,
  config: EMPTY_OBJECT,
  noActivityConfig: EMPTY_OBJECT,
  pastLeadsData: EMPTY_ARRAY,
  isCustomerOneViewEnabled: false,
  createBulkActivityLogsPayload: _noop,
  createBulkActivityLogsPayloadV2: _noop,
  hasMoreActivity: false,
  filterPreferences: EMPTY_ARRAY,
  updateFilterPreferences: _noop,
  isTestDriveCrmSetupEnabled: false,
  getDealerPropertyValue: _noop,
  settings: EMPTY_OBJECT,
  isDealOpen: false,
  dealerInfo: EMPTY_OBJECT,
  communicationOptinPreference: EMPTY_OBJECT,
  tabClassName: '',
  hasScrollReachedBottom: false,
  AISummaryContainerViewType: undefined,
  hasAISummaryFeedbackSubmitted: false,
  setHasMoreActivities: _noop,
  setActivityLogsGetterFns: _noop,
  paymentOptionConfigs: EMPTY_ARRAY,
  isCTCDisabled: false,
};

export default withTekionConversion(TabPanel);
