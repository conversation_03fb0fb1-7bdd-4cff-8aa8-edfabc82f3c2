import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import _isEmpty from 'lodash/isEmpty';
import _noop from 'lodash/noop';
import _head from 'lodash/head';
import { defaultMemoize } from 'reselect';
import { compose } from 'recompose';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import LeadReader from '@tekion/tekion-base/readers/lead.reader';
import TasksAPI from '@tekion/tekion-base/services/crm/tasks.api';
import { getErrorMessage } from '@tekion/tekion-base/utils/errorUtils';
import { tget } from '@tekion/tekion-base/utils/general';

import Button from '@tekion/tekion-components/src/atoms/Button';
import { MODAL_TYPE, showGlobalModal } from '@tekion/tekion-components/src/emitters/ModalEventEmitter';
import Modal from '@tekion/tekion-components/src/molecules/Modal';
import withPropertyConsumer from '@tekion/tekion-components/src/organisms/propertyProvider/withPropertyConsumer';
import DEALER_PROPERTIES from '@tekion/tekion-base/constants/dealerProperties';
import { PropertyControlledComponent } from '@tekion/tekion-components/src/molecules';
import Loader from '@tekion/tekion-components/src/molecules/loader';
import { triggerSubmit } from '@tekion/tekion-components/src/pages/formPage/utils/formAction';
import isEUDealer from '@tekion/tekion-business/src/appServices/crm/helpers/isEUDealer';
import { getErrorMessageForCrm } from '@tekion/tekion-business/src/appServices/crm/utils/getErrorMessageForCrm';
import TEnvReader from '@tekion/tekion-base/readers/Env';
import getDataFromResponse from '@tekion/tekion-base/utils/getDataFromResponse';
import { TOASTER_TYPE, toaster } from '@tekion/tekion-components/src/organisms/NotificationWrapper';

import { makeBaseQueryParams } from '@tekion/tekion-business/src/appServices/communication/helpers/communicatoins.makeQueryParams';
import TaskCompletionList from '../../../../molecules/taskCompletionList/TasksCompletionList';
import { getOverdueTasksRequest } from '../../../../helpers/overDueTasks';
import { showError } from '../../../../utils/toaster';
import { DEPARTMENTS, NDCHelper } from '../../../ndc';
import { ACTION_TYPES, ACTIVITY_LOGS_TYPES_VS_TITLE } from '../../activityLogs.constants';
import LogForm from './components/logForm';
import { LOG_FORM_CONTEXT_ID, LOG_TYPES_VS_TASK_ACTIONS } from './logModal.constants';
import { getPayload } from './logModal.factory';
import * as Helpers from './logModal.helper';

import { getSelectedTasks } from '../../../../helpers/communicationOutcome.helpers';
import { LOG_MODAL_TEST_CONFIG } from './constants/logModal.dataTestConfig';
import withCommunicationOptinPrefererence from '../../../../hocs/withCommunicationOptinPrefererence';
import { getOptinSettings } from '../../../../helpers/communications/communication.optinSettings';
import ACTIVITY_LOG_VS_COMMUNICATION_TYPE from './constants/logModal.activityLogVsCommunicationType';
import { fetchCentralSetupFields } from '../../../../services/centralSetupService';
import { CRM_MODAL_ENTITY } from '../../../../constants/crmModalEntity';
import MiniLeadDetails from '../../../../molecules/MiniLeadDetails';
import { FEATURE_NAME, withExperienceEngineConsumer } from '../../../../../../experienceEngine';

import styles from './logModal.module.scss';

const INITIAL_STATE = {
  errors: EMPTY_OBJECT,
  log: EMPTY_OBJECT,
  onSubmitCb: _noop,
  logType: EMPTY_STRING,
  tasks: EMPTY_ARRAY,
  selectedTasks: EMPTY_ARRAY,
  loading: true,
  submitLoading: false,
  showTasksList: true,
  leadDetails: EMPTY_OBJECT,
  areContactsLoading: false,
  communicationOptinPreference: EMPTY_OBJECT,
  sourceDetails: EMPTY_OBJECT,
  fields: EMPTY_ARRAY,
  customerPhoneNumber: undefined,
};

class LogModal extends PureComponent {
  getOptinSettings = defaultMemoize(getOptinSettings);

  constructor(props) {
    super(props);
    this.state = INITIAL_STATE;
    LogModal.ref = this;
  }

  componentDidMount() {
    this.init();
  }

  setContactsLoading = loading => {
    this.setState({
      areContactsLoading: loading,
    });
  };

  setLoading = loading => {
    this.setState({
      loading,
    });
  };

  getLogInfoFromProps = async ({ logInfo, leadDetails, getDealerPropertyValue, communicationOptinPreference }) => {
    const dealerId = LeadReader.dealerId(leadDetails);
    const siteId = LeadReader.siteId(leadDetails);
    const params = makeBaseQueryParams(dealerId, siteId);
    const ndcOptinSetUp = this.getOptinSettings(communicationOptinPreference);
    if (logInfo) {
      if (getDealerPropertyValue(DEALER_PROPERTIES.NDC_SET_UP_ENABLED)) {
        this.setContactsLoading(true);

        const fetchCommunicationPreferencesPayload = Helpers.makeFetchCommunicationPreferencePayload(leadDetails);
        const { ndcPreferences } = await NDCHelper.fetchNDCPreferences({
          ndcOptinSetUp,
          getDealerPropertyValue,
          fetchCommunicationPreferencesPayload,
          department: DEPARTMENTS.COMMUNICATION_RETAIL,
          params,
        });
        this.setContactsLoading(false);
        return {
          logInfoFromProps: Helpers.updateLogModalCoordinateOptions(
            Helpers.transformAPIDataToFormData(logInfo),
            leadDetails,
            ndcPreferences
          ),
          ndcPreferences,
        };
      }
      return {
        logInfoFromProps: Helpers.updateLogModalCoordinateOptions(
          Helpers.transformAPIDataToFormData(logInfo),
          leadDetails
        ),
      };
    }
    return EMPTY_OBJECT;
  };

  async getCentralSetupFields() {
    try {
      const response = await fetchCentralSetupFields(CRM_MODAL_ENTITY.LOG_MODAL);
      const fields = getDataFromResponse(response);
      this.setState({
        fields,
      });
    } catch (error) {
      toaster(TOASTER_TYPE.ERROR, getErrorMessageForCrm(error, __('Error fetching log modal fields.')));
    }
  }

  async getTasksAndCentralSetupFields() {
    this.setState({ loading: true });
    const { getFeatureValue } = this.props;
    const isEnterpriseCommunicationEnabled = getFeatureValue(FEATURE_NAME.ENTERPRISE_COMMUNICATION_ENABLED);

    const promises = [this.getTasks()];
    if (isEnterpriseCommunicationEnabled) {
      promises.push(this.getCentralSetupFields());
    }
    await Promise.allSettled(promises);
    this.setState({ loading: false });
  }

  init = async () => {
    const { eventData, getDealerPropertyValue, communicationOptinPreference } = this.props;

    const {
      logInfo = EMPTY_OBJECT,
      showTasksList = true,
      leadDetails,
      onSubmitCb = _noop,
      customerPhoneNumber,
      sourceDetails,
    } = eventData;
    const { logType } = logInfo;
    const communicationType = ACTIVITY_LOG_VS_COMMUNICATION_TYPE[logType];

    const { logInfoFromProps = EMPTY_OBJECT, ndcPreferences = EMPTY_OBJECT } = await this.getLogInfoFromProps({
      logInfo,
      leadDetails,
      getDealerPropertyValue,
      communicationOptinPreference,
      communicationType,
    });

    this.setState(
      {
        log: { ...Helpers.getInitialLogInfo(logType), ...logInfoFromProps },
        logType,
        onSubmitCb,
        loading: true,
        showTasksList,
        leadDetails,
        ndcPreferences,
        customerPhoneNumber,
        sourceDetails,
      },
      this.getTasksAndCentralSetupFields
    );
  };

  getTasks = async () => {
    const { logType, leadDetails } = this.state;
    const { getDealerPropertyValue } = this.props;
    const isDealerEU = isEUDealer(getDealerPropertyValue);
    const { id } = TEnvReader.userInfo();
    const dealerId = LeadReader.dealerId(leadDetails);
    const siteId = LeadReader.siteId(leadDetails);
    const params = makeBaseQueryParams(dealerId, siteId);
    const response = await TasksAPI.getTasks(
      getOverdueTasksRequest({
        leadId: LeadReader.leadId(leadDetails),
        taskType: LOG_TYPES_VS_TASK_ACTIONS[logType],
        isDealerEU,
      }),
      params
    );
    const tasks = tget(response, 'hits', EMPTY_ARRAY);
    const selectedTasks = getSelectedTasks(tasks, id);
    this.setState({
      tasks,
      loading: false,
      selectedTasks,
    });
  };

  hide = () => {
    const { onModalClose } = this.props;
    onModalClose();
  };

  getState = () => ({ ...this.state });

  setParentState = stateToSet => this.setState(stateToSet);

  openTaskModal = leadDetails => {
    showGlobalModal({
      modalType: MODAL_TYPE.TASK_MODAL,
      payload: {
        taskInfo: {
          entityId: LeadReader.leadId(leadDetails),
          assignedTo: LeadReader.primarySalesPersonUserId(leadDetails),
          department: LeadReader.department(leadDetails),
        },
        title: __('Create Follow-up Task'),
      },
    });
  };

  handleSubmit = props => {
    const { submitLoading } = this.state;
    if (submitLoading) return;

    this.setState({
      submitLoading: true,
    });
    this.handleSubmitCb(props);
  };

  saveAndCreateFollowupTask = () => {
    this.handleSubmit({ openTaskModal: true });
  };

  handleModalSubmit = props => {
    triggerSubmit(LOG_FORM_CONTEXT_ID, props);
  };

  onSubmit = (_formtype, props = EMPTY_OBJECT) => {
    this.handleSubmit(props);
  };

  handleSubmitCb = async ({ openTaskModal }) => {
    const { leadDetails } = this.state;
    const leadId = LeadReader.leadId(leadDetails);
    const dealerId = LeadReader.dealerId(leadDetails);
    const siteId = LeadReader.siteId(leadDetails);
    const params = makeBaseQueryParams(dealerId, siteId);
    const { onSubmitCb, log, logType, selectedTasks } = this.state;
    const taskIds = this.shouldShowTaskList() ? selectedTasks : EMPTY_ARRAY;
    onSubmitCb(getPayload({ leadId, log, logType, taskIds }), logType, params);
    let shouldOpenTaskModal = openTaskModal;
    if (!openTaskModal && !_isEmpty(taskIds)) {
      try {
        shouldOpenTaskModal = await TasksAPI.checkFollowUpConfig(leadId);
      } catch (err) {
        const errorMessage = getErrorMessage(err, __('Something went wrong'));
        showError(errorMessage);
      }
    }
    this.hide();
    if (shouldOpenTaskModal) this.openTaskModal(leadDetails);
  };

  renderFooterRightSection = () => {
    const { log, submitLoading } = this.state;
    return (
      <div className="d-inline-flex">
        <Button
          type={Button.VIEW.SECONDARY}
          onClick={this.hide}
          className="m-r-16"
          data-test={LOG_MODAL_TEST_CONFIG.MODAL.SECONDARY_BUTTON}>
          {__('Cancel')}
        </Button>
        <Button
          type={Button.VIEW.SECONDARY}
          onClick={this.saveAndCreateFollowupTask}
          disabled={!log.to || !log.body || submitLoading}
          data-test={LOG_MODAL_TEST_CONFIG.MODAL.ADDITIONAL_BUTTON}>
          {__('Log and Create Followup Task')}
        </Button>
      </div>
    );
  };

  handleTaskSelect = values => {
    this.setState({
      selectedTasks: values,
    });
  };

  shouldShowTaskList = () => {
    const { log, showTasksList } = this.state;
    if (!showTasksList) return false;
    if (log.logType === ACTION_TYPES.LOG_TEXT) return log.textType === ACTION_TYPES.SENT;
    if (log.logType === ACTION_TYPES.LOG_CALL) return log.callType === ACTION_TYPES.OUTBOUND;
    return true;
  };

  render() {
    const {
      log,
      errors,
      logType,
      tasks,
      selectedTasks,
      loading,
      submitLoading,
      fields,
      customerPhoneNumber,
      sourceDetails,
      leadDetails,
    } = this.state;
    const { body } = log;
    const { getDealerPropertyValue, visible } = this.props;

    return (
      <Modal
        onCancel={this.hide}
        width={Modal.SIZES.L}
        title={ACTIVITY_LOGS_TYPES_VS_TITLE[logType]?.headerTitle}
        renderFooterRightSection={this.renderFooterRightSection}
        submitBtnText={__('Log')}
        visible={visible}
        hideCancel
        onSubmit={this.handleModalSubmit}
        loading={submitLoading}
        dataTestConfig={LOG_MODAL_TEST_CONFIG.MODAL}>
        <div className={`${styles.logBody} overflow-y-auto`}>
          <MiniLeadDetails
            fields={fields}
            className="m-b-32"
            sourceDetails={sourceDetails}
            leadDetails={leadDetails}
            customerPhoneNumber={customerPhoneNumber}
          />
          <PropertyControlledComponent controllerProperty={!loading} fallback={<Loader />}>
            <LogForm
              getDealerPropertyValue={getDealerPropertyValue}
              cancel={this.hide}
              createFollowupTask={this.createFollowupTask}
              errors={errors}
              getState={this.getState}
              isDisabledLog={!body}
              onSubmit={this.onSubmit}
              setParentState={this.setParentState}
              values={log}
              logType={logType}
            />
            <PropertyControlledComponent controllerProperty={this.shouldShowTaskList()}>
              <div className="m-t-24">
                <TaskCompletionList selectedValues={selectedTasks} onSelect={this.handleTaskSelect} data={tasks} />
              </div>
            </PropertyControlledComponent>
          </PropertyControlledComponent>
        </div>
      </Modal>
    );
  }
}

LogModal.propTypes = {
  eventData: PropTypes.object,
  onModalClose: PropTypes.func,
  getDealerPropertyValue: PropTypes.func,
  visible: PropTypes.bool,
  getFeatureValue: PropTypes.func,
};

LogModal.defaultProps = {
  eventData: EMPTY_OBJECT,
  onModalClose: _noop,
  getDealerPropertyValue: _noop,
  visible: false,
  getFeatureValue: _noop,
};
export default compose(
  withPropertyConsumer,
  withCommunicationOptinPrefererence(true),
  withExperienceEngineConsumer
)(LogModal);
