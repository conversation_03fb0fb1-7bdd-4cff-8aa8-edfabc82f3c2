import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { compose, branch } from 'recompose';
import { defaultMemoize } from 'reselect';
import { bindActionCreators } from 'redux';

import { EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import { SERVICES } from '@tekion/tekion-base/constants/crm/services';
import MODULE_ASSET_TYPES from '@tekion/tekion-base/constants/moduleAssetTypes';
import { getBaseUrl } from '@tekion/tekion-base/helpers/crm/baseUrls';
import withActionHandlers from '@tekion/tekion-components/src/connectors/withActionHandlers';
import withAsyncReducer from '@tekion/tekion-components/src/connectors/withAsyncReducer';
import withRouter from '@tekion/tekion-components/src/hoc/withRouter';

import withAiBdcUser from '../../../../hocs/withAiBdcUser';
import withFilterPreference from '../../../../hocs/withFilterPreference';
import withPreference from '../../../../hocs/withPreference';
import withPusherEventHandler from '../../../../hocs/withPusherEventHandler';
import { AI_SUMMARY_PREFERENCE_ASSET_TYPES } from '../../constants/AISummaryContainerViewTypes';
import ActivityLogs from './ActivityLogs';
import { ACTIVITY_LOGS_REDUCER_NAME } from './activityLogs.constants';
import ActivityLogsReducer from './activityLogs.reducer';
import ActivityLogSkeleton from '../../molecules/activityLogSkeleton';

// Actions
import * as activityLogsActions from './activityLogs.actions';
import ACTION_HANDLERS from './actionHandlers';

// Selectors
import * as activityLogsSelector from './activityLogs.selectors';

import { makeCompoundAssetKey } from './activityLogs.helper';
// Activity Log Pusher Hoc
import VehicleTransactionModal from '../vehicleTransactionsModal';
import { withNDC } from '../ndc';

import styles from './activityLogs.module.scss';

const getActivityLogsActions = defaultMemoize(dispatch => bindActionCreators({ ...activityLogsActions }, dispatch));
class ActivityLogsContainer extends React.PureComponent {
  getComponent = defaultMemoize(department => {
    if (!department) return null;

    const compoundAssetKey = makeCompoundAssetKey(MODULE_ASSET_TYPES.LEAD_ACTIVITY_TABS, department);
    const path = `${getBaseUrl(SERVICES.SETUP)}/list-view/${compoundAssetKey}`;

    return withPreference(compoundAssetKey, EMPTY_ARRAY, path, ActivityLogSkeleton)(ActivityLogs);
  });

  render() {
    const spinnerContainerClass = `full-width full-height ${styles.spinner}`;
    const { department } = this.props;
    const Component = this.getComponent(department);
    return (
      <>
        {!department ? (
          <ActivityLogSkeleton />
        ) : (
          <Component {...this.props} spinnerWrapperClass={spinnerContainerClass} />
        )}
        <VehicleTransactionModal />
      </>
    );
  }
}

ActivityLogsContainer.propTypes = {
  department: PropTypes.string.isRequired,
};

const mapStateToProps = state => ({
  activityLogs: activityLogsSelector.getActivityLogsSelector(state),
  activityTabsCount: activityLogsSelector.getActivityTabsCount(state),
  activeTab: activityLogsSelector.getActiveTab(state),
  users: activityLogsSelector.getUsers(state),
  notRespondedTabs: activityLogsSelector.getNotRespondedTabs(state),
  pastLeadsData: activityLogsSelector.getPastLeadsData(state),
  hasMoreActivity: activityLogsSelector.getHasMoreActivity(state),
  isFetching: activityLogsSelector.getIsFetching(state),
});

const mapDispatchToProps = dispatch => ({
  actions: getActivityLogsActions(dispatch),
});

export default compose(
  withAsyncReducer({
    storeKey: ACTIVITY_LOGS_REDUCER_NAME,
    reducer: ActivityLogsReducer,
  }),
  withNDC,
  withActionHandlers(ACTION_HANDLERS),
  connect(mapStateToProps, mapDispatchToProps),
  withRouter,
  withPusherEventHandler,
  branch(
    props => props.isCustomerOneViewEnabled,
    withFilterPreference(
      [
        AI_SUMMARY_PREFERENCE_ASSET_TYPES.CRM_LEAD_SNAPSHOT_VIEW,
        AI_SUMMARY_PREFERENCE_ASSET_TYPES.CRM_SUMMARY_FEEDBACK_STATUS,
      ],
      ActivityLogSkeleton
    )
  ),
  withAiBdcUser
)(ActivityLogsContainer);
