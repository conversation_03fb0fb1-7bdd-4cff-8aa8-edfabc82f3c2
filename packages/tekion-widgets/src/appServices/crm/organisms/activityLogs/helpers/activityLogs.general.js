import _get from 'lodash/get';
import _includes from 'lodash/includes';
import _isNil from 'lodash/isNil';
import _map from 'lodash/map';

import { isInchcapeEnabled } from '@tekion/tekion-business/src/appServices/crm/helpers/isInchcapeEnabled';
import LeadsAPI, { getLeadActivityByActivityIdAndActivityType } from '@tekion/tekion-base/services/crm/leads.api';
import isStringEmpty from '@tekion/tekion-base/utils/isStringEmpty';
import isLeadDetailsEnhancementEnabled from '@tekion/tekion-business/src/appServices/crm/helpers/isLeadDetailsEnhancementEnabled';
import { ENTITIES } from '@tekion/tekion-base/bulkResolvers/entities';
import Resolver from '@tekion/tekion-base/bulkResolvers';
import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import { getAttachmentsMedia } from '../../../actions/mediaActions';
import {
  AI_SUMMARY_FEEDBACK_FILTER_CONFIG,
  AI_SUMMARY_CONTAINER_VIEW_FILTER_CONFIG,
} from '../../../constants/AISummaryContainerViewTypes';
import showAISummary from '../../../helpers/showAISummary';
import { VIEW_FILTER_TYPES } from '../activityLogs.constants';
import { activityPusherReader } from '../activityLogs.reader';
import { VALID_TABS_FOR_AI_SUMMARY } from '../constants/activityLogs.general';
import { getFilterValueFromPreference } from './communicationView';

export const getActivityLogsFromPusher = async pusherEvent => {
  const eventData = activityPusherReader.payload(pusherEvent);
  const activityActionType = _get(eventData, 'activityActionType');
  const activity = _get(eventData, 'activity');

  // Fallback Code, If we get the old activity info.
  if (!isStringEmpty(activity)) {
    const parsedActivityInfo = JSON.parse(activity);
    return { activityActionType, activity: parsedActivityInfo };
  }

  const activityId = _get(eventData, 'activityId');
  const activityType = _get(eventData, 'activityType');

  const activityInfo = await getLeadActivityByActivityIdAndActivityType(activityId, activityType);

  return { activityActionType, activity: activityInfo };
};

export const getRespondedStatusFromPusher = pusherEvent => {
  const eventData = activityPusherReader.payload(pusherEvent);
  const shouldFetchRespondedStatus = _get(eventData, 'responseFlagUpdateNeeded');

  return { shouldFetchRespondedStatus };
};

export const getQuoteLabel = getDealerPropertyValue =>
  isInchcapeEnabled(getDealerPropertyValue) ? __('Offers') : __('Quotes');

export const getAISummaryFilterPreferences = filterPreferences => {
  const getSummaryFilterPreferences = config => getFilterValueFromPreference(filterPreferences, config);

  const [AISummaryContainerViewType, hasAISummaryFeedbackSubmitted] = _map(
    [AI_SUMMARY_CONTAINER_VIEW_FILTER_CONFIG, AI_SUMMARY_FEEDBACK_FILTER_CONFIG],
    getSummaryFilterPreferences
  );

  return { AISummaryContainerViewType, hasAISummaryFeedbackSubmitted };
};

export const getStepsPanelComponent = (getDealerPropertyValue, InfiniteScrollContainer, StepsPanel = 'div') => {
  if (isLeadDetailsEnhancementEnabled(getDealerPropertyValue)) return StepsPanel;
  return InfiniteScrollContainer;
};

export const shouldShowActivityLogsLoader = (hasMore, isFetching, leadDetailsEnhancementEnabled) =>
  hasMore || (isFetching && leadDetailsEnhancementEnabled);

export const isChronologicalView = viewType => viewType === VIEW_FILTER_TYPES.CHRONOLOGICAL_VIEW;

export const isLeadView = viewType => viewType === VIEW_FILTER_TYPES.LEAD_VIEW;

export const shouldShowAISummary = params => {
  const { activeTab, getDealerPropertyValue, leadDetails } = params;

  const isValidTab = _isNil(activeTab) || _includes(VALID_TABS_FOR_AI_SUMMARY, activeTab);

  const showSummary = showAISummary({ leadDetails, getDealerPropertyValue });

  return isValidTab && showSummary;
};

export const fetchAttachments = async mediaStore => {
  const attachmentList = await getAttachmentsMedia(mediaStore);
  return attachmentList;
};

export const getResolvedLeadDetails = async ({
  shouldFetchLeadData = false,
  leadDetails = EMPTY_OBJECT,
  params = EMPTY_OBJECT,
}) => {
  if (shouldFetchLeadData) {
    const { leadId } = leadDetails;
    const lead = await LeadsAPI.getLead(leadId, params);
    const resolvedDetails = await Resolver.getResolvedData(ENTITIES.LEADS, [lead]);
    return resolvedDetails;
  }

  const resolvedDetails = await Resolver.getResolvedData(ENTITIES.LEADS, [leadDetails]);
  return resolvedDetails;
};
