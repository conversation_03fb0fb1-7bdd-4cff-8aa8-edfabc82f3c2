import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { defaultMemoize } from 'reselect';

import _debounce from 'lodash/debounce';
import _find from 'lodash/find';
import _includes from 'lodash/includes';
import _isEmpty from 'lodash/isEmpty';
import _noop from 'lodash/noop';
import _get from 'lodash/get';
import _size from 'lodash/size';

import { EMPTY_OBJECT, EMPTY_ARRAY, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import LeadReader from '@tekion/tekion-base/readers/lead.reader';
import { tget } from '@tekion/tekion-base/utils/general';
import isStringEmpty from '@tekion/tekion-base/utils/isStringEmpty';
import { isRRGEnabled } from '@tekion/tekion-business/src/appServices/crm/helpers/isRRGEnabled';
import SetupReader from '@tekion/tekion-business/src/appServices/crm/readers/setup.reader';
import { PermissionContext } from '@tekion/tekion-components/src/widgets/permissionsHelper';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';

import { NoLongerOwnsEmitter, NO_LONGER_OWNS_EVENTS } from '../noLongerOwns';

import CallDetails from '../callDetails';
import renderUserOrCampaignData from '../userRender/renderUserOrCampaignData';
import VehicleContractModal from '../vehicleContractModal';
import TabPanel from './components/tabPanel/TabPanel';
import {
  ACTION_KEYS,
  ACTION_TYPES,
  ACTIVITY_TYPES,
  PUSHER_ACTION_TYPES,
  ACTIVITY_LOGS_TABS,
  PUSHER_EVENT_NAME,
  COMMUNICATION_FILTER_TYPES,
  DEFAULT_ACTIVITY_PAGE_NO,
  DEFAULT_PAGE_NO,
  VIEW_FILTER_TYPES,
} from './activityLogs.constants';
import { getActivitiesCountPayload, createBulkActivityLogsPayload } from './activityLogs.factory';
import {
  getActiveTab,
  getFilterByActiveTab,
  shouldShowUnrespondedMark,
  getLeadChannelAndEventName,
  getLeadsPayload,
  getPageInfo,
  isLeadViewSupported,
  getBulkActivityPayloadV2,
  isMultiViewSupported,
  isValidTab,
} from './activityLogs.helper';
import {
  getTabConfigurations,
  getActivityLogConfigList,
  getLeftActions,
  getAllowedTabsForTabsConfigurator,
  patchTabRightActionsConfig,
} from './activityLogs.config';
import { getNoActivityConfig } from './activityLogs.noActivityConfig';
import { shouldFetchDataByTab } from './constraints/activityLogs.general';
import { getQueryParamValue } from '../../utils';
import { CRM_ENTITIES } from '../../constants/common';
import { canShowCreditApplication } from '../../helpers/creditApplication';
import withMetadataContext from '../../hocs/withMetadata/WithMetadataContext';
import ActivityLogsContext from './activityLogs.context';
import ActivityTabsConfigurator from './components/activityTabsConfigurator';
import styles from './activityLogs.module.scss';
import { ACTION_TYPES as ACTIVITY_LOGS_ACTION_TYPES } from './activityLogs.actionTypes';
import ViewTypeDropdown from './components/viewTypeDropdown';
import { getTabCountV2Payload } from './helpers/activityLogs.activityCount';
import {
  getAISummaryFilterPreferences,
  getActivityLogsFromPusher,
  isChronologicalView,
  shouldShowAISummary,
} from './helpers/activityLogs.general';
import { addSelectedTabQueryParam, removeSelectedTabQueryParam } from './helpers/activityLogs.tabQueryParams';
import ActivityLogSkeleton from '../../molecules/activityLogSkeleton';

class ActivityLogs extends PureComponent {
  static create(props) {
    if (ActivityLogs.ref) {
      ActivityLogs.ref.create(props);
    }
  }

  static refresh(props, params) {
    if (ActivityLogs.ref) {
      ActivityLogs.ref.refresh(props, params);
    }
  }

  static reset() {
    if (ActivityLogs.ref) {
      ActivityLogs.ref.reset();
    }
  }

  static resetActivityConfig() {
    if (ActivityLogs.ref) ActivityLogs.ref.setTabConfig();
  }

  getAllowedTabsMemoized = defaultMemoize(getAllowedTabsForTabsConfigurator);

  getContextValue = defaultMemoize((entityId, shouldHighlightActivity, getNDCPreferences) => ({
    highlightedActivityId: entityId && shouldHighlightActivity ? entityId : null,
    getPageNo: this.getPageNo,
    setPageNo: this.setPageNo,
    getNDCPreferences,
  }));

  constructor(props) {
    super(props);
    props.subscribePusher(this); // Don't Delete this, it is used to subscribe to the pusher
    // removing hightlighting of activity as discussed as part of DMS-84144

    this.state = {
      filter: ACTION_KEYS.TODAY,
      focusNoteEditor: false,
      viewType: VIEW_FILTER_TYPES.CHRONOLOGICAL_VIEW,
      pageNo: DEFAULT_PAGE_NO,
      contractMetaData: EMPTY_OBJECT,
      showContractModal: false,
    };
    ActivityLogs.ref = this;
    this.fetchActivitysOnCreateAction = _debounce(options => this.refresh(EMPTY_OBJECT, options), 2000);
    this.getNoActivityConfig = defaultMemoize(getNoActivityConfig);
    this.noteEditorRef = React.createRef(null);
  }

  componentDidMount() {
    this.init();
    this.addEventListeners();
    this.fetchAISummaryDetails();
  }

  componentDidUpdate(prevProps) {
    const { props } = this;
    const { actions, subscribeEvents, unSubscribeEvents, type, selectedTab: tab } = props;
    const { config } = this.state;
    const prevLeadId = LeadReader.leadId(prevProps.leadDetails);
    const leadId = LeadReader.leadId(props.leadDetails);
    const entityType = type || getQueryParamValue(props.location, 'type');
    const prevPreferences = _get(prevProps, 'preferences');
    const preferences = _get(props, 'preferences');
    const selectedTab = tab || getQueryParamValue(props.location, 'selectedTab');
    let { activeTab, filter } = getActiveTab(entityType, preferences, config, { selectedTab });
    const prevMatchLeadId = _get(prevProps, 'params.id');
    const matchLeadId = _get(props, 'params.id');
    if (prevMatchLeadId !== matchLeadId) {
      actions.setCurrentLeadId({ leadId: matchLeadId });
      unSubscribeEvents([getLeadChannelAndEventName(prevMatchLeadId, PUSHER_EVENT_NAME)]);
    }
    if (prevLeadId !== leadId) {
      this.handleLeadChange(activeTab, { filter });
      this.resetViewType();
      subscribeEvents([getLeadChannelAndEventName(leadId, PUSHER_EVENT_NAME)]);
    } else if (prevPreferences !== preferences) {
      this.setTabConfig(filter);
      if (_size(prevPreferences) !== 0) {
        activeTab = getActiveTab(null, preferences, config, { selectedTab })?.activeTab;
        filter = getActiveTab(null, preferences, config, { selectedTab })?.filter;
      }
      this.handleTabChange(activeTab, { filter });
    } else if (!_isEmpty(props.entityId) && prevProps.entityId !== props.entityId && selectedTab) {
      this.handleTabChange(activeTab, { filter });
    }
  }

  componentWillUnmount() {
    const { actions } = this.props;
    actions.resetActivityLogsData();
    this.removeEventListeners();
  }

  init = () => {
    const { actions, location, preferences, params } = this.props;
    const selectedTab = getQueryParamValue(location, 'selectedTab') || params.selectedTab;
    const action = getQueryParamValue(location, 'action') || params.action || '';
    const entityType = getQueryParamValue(location, 'type');
    const { activeTab, filter } = getActiveTab(entityType, preferences, EMPTY_OBJECT, { selectedTab });
    const { activeTab: currentTab, subscribeEvents } = this.props;
    const currentLeadId = tget(params, 'id');
    this.setTabConfig(filter);
    actions.setCurrentLeadId({ leadId: currentLeadId });
    subscribeEvents([getLeadChannelAndEventName(currentLeadId, PUSHER_EVENT_NAME)]);
    if (action === ACTION_TYPES.OPEN_TAB && !isStringEmpty(selectedTab)) {
      this.fetchActivityLogsOnMount(selectedTab);
    } else if (activeTab !== currentTab) {
      // TODO: On Tab Change should not be triggered from here.
      this.handleTabChange(activeTab, { filter });
    } else {
      this.refresh();
    }
  };

  addEventListeners = () => {
    NoLongerOwnsEmitter.on(NO_LONGER_OWNS_EVENTS.NO_LONGER_OWNS_SUCCESS, this.handleNoLongerOwnsSuccess);
  };

  handleNoLongerOwnsSuccess = () => {
    this.refresh(EMPTY_OBJECT, { loader: false });
  };

  removeEventListeners = () => {
    const { actions, unSubscribeEvents, leadDetails } = this.props;
    NoLongerOwnsEmitter.removeListener(NO_LONGER_OWNS_EVENTS.NO_LONGER_OWNS_SUCCESS, this.handleNoLongerOwnsSuccess);
    const leadId = LeadReader.leadId(leadDetails);
    actions.setCurrentLeadId(EMPTY_OBJECT);
    unSubscribeEvents([getLeadChannelAndEventName(leadId, PUSHER_EVENT_NAME)]);
  };

  fetchActivityLogsOnMount = async tab => {
    const { actions } = this.props;
    const filter = getFilterByActiveTab(tab, this.config, getLeftActions, {});
    this.setState({ filter }, async () => {
      await actions.setActiveTab(tab);
      this.refresh();
    });
  };

  setPageNo = pageNo => {
    this.setState({ pageNo });
  };

  getPageNo = () => {
    const { pageNo } = this.state;
    return pageNo;
  };

  handleLeadChange = async (activeTab, extra) => {
    const { actions, params, setHasMoreActivities, setActivityLogsGetterFns } = this.props;
    const payload = activeTab ? { activeTab } : EMPTY_OBJECT;
    const leadId = _get(params, 'id');
    actions.resetActivityLogsData({ ...payload, currentLeadId: leadId });
    this.fetchAISummaryDetails();
    setHasMoreActivities(false);
    setActivityLogsGetterFns(EMPTY_ARRAY);
    const filter = getFilterByActiveTab(activeTab, this.config, getLeftActions, extra);
    this.setTabConfig(filter);
    this.setState({ filter, pageNo: DEFAULT_PAGE_NO }, this.refresh);
  };

  setTabConfig = filter => {
    const {
      leadDetails,
      liveChatCount,
      settings,
      preferences,
      isCustomerOneViewEnabled,
      getDealerPropertyValue,
      isCTCDisabled,
    } = this.props;
    this.config = getTabConfigurations(
      leadDetails,
      {
        showCreditApplication: canShowCreditApplication(settings),
        liveChatCount,
        tabFilter: filter,
        isCustomerOneViewEnabled,
        getDealerPropertyValue,
        isCTCDisabled,
      },
      preferences
    );
    this.setState({ config: this.config });
  };

  patchTabConfig = filter => {
    const { leadDetails, activeTab } = this.props;
    const { config } = this.state;
    this.config = patchTabRightActionsConfig(config, leadDetails, activeTab, filter);
    this.setState({ config: this.config });
  };

  create = async payload => {
    const { actions } = this.props;
    const { createLeadActivity } = actions;
    await createLeadActivity(payload);
  };

  handleTabChange = async (newActiveTab, extra) => {
    const { actions, activeTab, preferences, navigate, location, params, updateRouteUrl } = this.props;
    const leadId = _get(params, 'id');
    const { filter } = this.state;
    const { onTabChangeCb = _noop } = extra || EMPTY_OBJECT;
    const newFilter = getFilterByActiveTab(newActiveTab, this.config, getLeftActions, extra);
    if (activeTab !== newActiveTab || filter !== newFilter) {
      await actions.setActiveTab(newActiveTab);
      this.setState(
        {
          filter: newFilter,
          pageNo: DEFAULT_PAGE_NO,
        },
        () => {
          const { filter: updatedFilter } = this.state;
          this.patchTabConfig(updatedFilter);
          onTabChangeCb();
          this.refresh();
        }
      );
    }
    if (isValidTab(newActiveTab, preferences)) {
      addSelectedTabQueryParam(newActiveTab, { navigate, location, updateRouteUrl }, leadId);
    } else {
      removeSelectedTabQueryParam({ navigate, location, updateRouteUrl }, leadId);
    }
  };

  onTabChange = tab => {
    const onTabChangeCb = () =>
      this.setState({
        focusNoteEditor: tab === ACTIVITY_TYPES.NOTE,
      });
    this.handleTabChange(tab, { onTabChangeCb });
  };

  onViewTypeStateChange = () => {
    this.fetchAISummaryDetails();
    this.refresh();
  };

  onViewTypeChange = viewType => {
    this.setState({ viewType, pageNo: DEFAULT_PAGE_NO }, this.onViewTypeStateChange);
  };

  resetViewType = () => {
    this.setState({ viewType: VIEW_FILTER_TYPES.CHRONOLOGICAL_VIEW });
  };

  refresh = async (props, params) => {
    const { loader = true, onlyCountRequired } = params || EMPTY_OBJECT;
    const { filter, viewType, pageNo } = this.state;
    const {
      actions,
      activeTab,
      leadDetails,
      pastLeadsData,
      isCustomerOneViewEnabled,
      params: routeParams,
      getNDCPreferences,
    } = this.props;

    const chronologicalView = isChronologicalView(viewType);
    const crmBuyerCustomerId = LeadReader.crmBuyerCustomerId(leadDetails);
    const buyerCustomerId = LeadReader.buyerCustomerId(leadDetails);

    if (loader) actions.setIsFetching({ isFetching: true, activeTab });
    const leadId = LeadReader.leadId(leadDetails);
    /*
      Getting currently Viewing LeadId from URL and comparing it with props leadId,
      before making an API call, to avoid fetching activity logs for the leadId
      which we get from pusher activity. This is a trail & error approach for now.
      This issue occurs after the changing the Pusher channel from user channel
      to lead channel
    */
    const currentlyViewingLeadId = tget(routeParams, 'id');
    if (leadId && leadId === currentlyViewingLeadId) {
      const { getActivitiesCount, getBulkActivityLogs, getUsersFromIds, getBulkActivityLogsV2, getLeads } = actions;
      const shouldMakeActivityLogsV2Call = isLeadViewSupported(activeTab) && isCustomerOneViewEnabled;
      const tabCountV2ChronologicalViewPayload = getTabCountV2Payload(
        COMMUNICATION_FILTER_TYPES.GENERAL_COMMUNICATION,
        leadDetails
      );
      const tabCountV2LeadViewPayload = getTabCountV2Payload(VIEW_FILTER_TYPES.LEAD_VIEW, leadDetails);
      const promisesArray = [
        getActivitiesCount({
          payload: getActivitiesCountPayload(leadId),
          isCustomerOneViewEnabled,
          activitiesV2ChronologicalViewALLCountPayload: tabCountV2ChronologicalViewPayload,
          activitiesV2LeadViewCountPayload: tabCountV2LeadViewPayload,
          chronologicalView,
          leadId,
        }),
      ];
      if (!onlyCountRequired && shouldFetchDataByTab(activeTab, leadDetails)) {
        if (shouldMakeActivityLogsV2Call) {
          promisesArray.push(
            getBulkActivityLogsV2(
              {
                leadId,
                ...getBulkActivityPayloadV2(filter, viewType, activeTab),
                crmBuyerCustomerId,
                buyerCustomerId,
                pageInfo: getPageInfo({
                  leadId:
                    !chronologicalView ||
                    _includes(
                      [
                        ACTIVITY_TYPES.NOTE,
                        ACTIVITY_TYPES.UPCOMING,
                        ACTIVITY_TYPES.CREDIT_APPLICATION,
                        ACTIVITY_TYPES.QUOTE,
                      ],
                      activeTab
                    )
                      ? EMPTY_STRING
                      : leadId,
                }),
              },
              props,
              {
                viewType,
                leadId,
                getNDCPreferences,
              }
            )
          );
          if (_isEmpty(pastLeadsData)) {
            promisesArray.push(getLeads(getLeadsPayload(crmBuyerCustomerId)));
          }
        } else {
          const pageNumber = pageNo ? DEFAULT_PAGE_NO : DEFAULT_ACTIVITY_PAGE_NO;
          this.setPageNo(pageNumber);
          promisesArray.push(
            getBulkActivityLogs(this.makeBulkActivityLogsPayload(DEFAULT_ACTIVITY_PAGE_NO), {
              activeTab,
              getNDCPreferences,
              leadId,
            })
          );
        }
      }
      await Promise.all(promisesArray);
      if (!onlyCountRequired) {
        if (_includes([ACTIVITY_TYPES.NOTE, ACTIVITY_TYPES.ALL], activeTab)) {
          const { activityLogs } = this.props;
          await getUsersFromIds(activityLogs);
        }
      }
      if (loader) actions.setIsFetching({ isFetching: false });
    }
  };

  fetchAISummaryDetails = () => {
    const { actions, getDealerPropertyValue, leadDetails, activeTab } = this.props;
    const leadId = LeadReader.leadId(leadDetails);
    const shouldFetchAISummary = shouldShowAISummary({ activeTab, getDealerPropertyValue, leadDetails });
    if (shouldFetchAISummary) {
      actions.fetchAISummaryDetails({ leadId });
    }
  };

  showContractModal = payload => {
    const { data } = payload || EMPTY_OBJECT;
    this.setState({ showContractModal: true, contractMetaData: data });
  };

  hideContractModal = () => {
    this.setState({ showContractModal: false });
  };

  showTranscriptModal = async payload => {
    const { messageId } = payload?.data;
    this.setState({ showTranscriptModal: true, communicationMessageId: messageId });
  };

  hideTranscriptModal = () => {
    this.setState({ showTranscriptModal: false, communicationMessageId: null });
  };

  onActivityLogActions = action => {
    const { type, payload } = action;
    const { parentOnAction } = this.props;
    switch (type) {
      case ACTION_TYPES.REFRESH_CURRENT_ACTIVITY: {
        this.refresh();
        break;
      }
      case ACTION_TYPES.SHOW_TRANSCRIPT_MODAL: {
        this.showTranscriptModal(payload);
        break;
      }
      case ACTION_TYPES.SHOW_OWNERSHIP_CONTRACT_MODAL: {
        this.showContractModal(payload);
        break;
      }
      default:
        parentOnAction(action);
    }
  };

  makeBulkActivityLogsPayloadV2 = pageNo => {
    const { activeTab, leadDetails } = this.props;
    const { filter, viewType } = this.state;
    const crmBuyerCustomerId = LeadReader.crmBuyerCustomerId(leadDetails);
    const buyerCustomerId = LeadReader.buyerCustomerId(leadDetails);
    const leadId = LeadReader.leadId(leadDetails);
    const {
      '@type': type,
      activityTypes,
      activeTab: activeTabFromPayload,
      filter: filterFromPayload,
      additionalActivitiesFilter,
    } = getBulkActivityPayloadV2(filter, viewType, activeTab);
    return {
      '@type': type,
      activityTypes,
      activeTab: activeTabFromPayload,
      filter: filterFromPayload,
      additionalActivitiesFilter,
      leadId,
      crmBuyerCustomerId,
      buyerCustomerId,
      pageInfo: getPageInfo({ leadId, pageNo }),
    };
  };

  makeBulkActivityLogsPayload = pageNo => {
    const { activeTab, leadDetails } = this.props;
    const { filter, config } = this.state;
    return createBulkActivityLogsPayload({ activeTab, leadDetails, pageNo, filter, config });
  };

  componentDidReceiveEvent = async event => {
    const { leadDetails, onAction, params } = this.props;
    const { activityActionType, activity } = await getActivityLogsFromPusher(event);
    const activityDataType = tget(activity, ['activityData', '@type']);
    const leadId = tget(activity, 'leadId');

    /*
      Getting currently Viewing LeadId from URL and comparing it with props leadId,
      before making an API call, to avoid fetching activity logs for the leadId
      which we get from pusher activity. This is a trail & error approach for now.
      This issue occurs after the changing the Pusher channel from user channel
      to lead channel
    */
    const currentlyViewingLeadId = _get(params, 'id');
    if (leadId === LeadReader.leadId(leadDetails) && leadId === currentlyViewingLeadId) {
      this.getActivitesBasedOnEventType({ activityDataType, activityActionType, activity });
      if (shouldShowUnrespondedMark(activityActionType, activity)) {
        onAction({
          type: ACTIVITY_LOGS_ACTION_TYPES.HANDLE_UPDATE_NOT_RESPONDED_TABS,
          payload: { notRespondedTabs: [ACTIVITY_TYPES.COMMUNICATIONS], appendPreviousNotRespondedTabs: true },
        });
      }
    }
  };

  getActivitesBasedOnEventType = async ({ activityDataType, activityActionType, activity }) => {
    const {
      activeTab,
      actions: { updateActivity, deleteActivity },
      onAction,
    } = this.props;
    const { activites } = _find(ACTIVITY_LOGS_TABS, { key: activeTab });
    const iscurrentActivity = _includes(activites, activityDataType) || activeTab === ACTIVITY_TYPES.ALL;
    if (activityActionType === PUSHER_ACTION_TYPES.CREATE) {
      this.fetchActivitysOnCreateAction({ loader: false, onlyCountRequired: !iscurrentActivity });
      onAction({
        type: ACTIVITY_LOGS_ACTION_TYPES.HANDLE_ACTIVITY_LOG_PUSHER_CREATE,
        payload: { activity },
      });
    } else if (iscurrentActivity && activityActionType === PUSHER_ACTION_TYPES.UPDATE) {
      onAction({
        type: ACTIVITY_LOGS_ACTION_TYPES.HANDLE_ACTIVITY_LOG_PUSHER_UPDATE,
        payload: { activeTab, activity },
      });
    } else if (iscurrentActivity && activityActionType === PUSHER_ACTION_TYPES.DELETE) {
      // Todo: Remove it once backend changes the Pusher event for Task Dismissed to task update
      if (activityDataType !== CRM_ENTITIES.TASK) {
        this.refresh(EMPTY_OBJECT, { loader: false, onlyCountRequired: true });
        deleteActivity({ activeTab, activity });
      } else updateActivity(activity);
    }
  };

  reset = () => {
    const { actions, navigate, location, preferences, params } = this.props;
    const { config } = this.state;
    const entityType = getQueryParamValue(location, 'type');
    const selectedTab = getQueryParamValue(location, 'selectedTab');
    const { activeTab, filter } = getActiveTab(entityType, preferences, config, { selectedTab });
    const payload = activeTab ? { activeTab } : EMPTY_OBJECT;
    const leadId = tget(params, 'id');
    this.setState({ filter }, actions.resetActivityLogsData({ ...payload, currentLeadId: leadId }));
  };

  onClickLeftAction = action => {
    this.patchTabConfig(action);
    this.setState({ filter: action }, this.refresh);
    this.setPageNo(DEFAULT_PAGE_NO);
  };

  renderTabsConfigurator = () => {
    const { preferences, isCustomerOneViewEnabled, updateUserPreferences, linkedLeadsCount, activeTab } = this.props;
    const { config, viewType } = this.state;
    if (_isEmpty(config) || _isEmpty(preferences)) return <></>;
    return (
      <div className="d-flex flex-row">
        <div className={styles.tabsBarRightDivider}></div>
        <div className={`d-flex justify-center align-items-center p-x-8 ${styles.tabsRightActionsContainer}`}>
          <PropertyControlledComponent
            controllerProperty={linkedLeadsCount?.buyerTotalLeads > 1 && isMultiViewSupported(activeTab)}>
            <ViewTypeDropdown
              viewType={viewType}
              isCustomerOneViewEnabled={isCustomerOneViewEnabled}
              onViewTypeChange={this.onViewTypeChange}
              iconClassName="p-r-8"
            />
          </PropertyControlledComponent>
          <ActivityTabsConfigurator
            tabs={this.getAllowedTabsMemoized(config, preferences)}
            shouldAddParentClass={false}
            onAction={updateUserPreferences}
          />
        </div>
      </div>
    );
  };

  render() {
    const {
      actions,
      activityLogs,
      activityTabsCount,
      leadDetails,
      users,
      preferences,
      notRespondedTabs,
      pastLeadsData,
      isCustomerOneViewEnabled,
      hasMoreActivity,
      isFetching,
      getNDCPreferences,
      filterPreferences,
      updateFilterPreferences,
      getDealerPropertyValue,
      settings,
      isFetchingLead,
      isDealOpen,
      dealerInfo,
      communicationOptinPreference,
      tabClassName,
      aiBdcUser,
      hasScrollReachedBottom,
      setHasMoreActivities,
      setActivityLogsGetterFns,
      paymentOptionConfigs,
      updateRouteUrl,
      isCTCDisabled,
    } = this.props;

    const isRRG = isRRGEnabled(getDealerPropertyValue);

    const isTestDriveCrmSetupEnabled = SetupReader.advancedTestDrive(settings);

    const {
      filter,
      focusNoteEditor,
      viewType,
      showContractModal,
      contractMetaData,
      showTranscriptModal,
      communicationMessageId,
    } = this.state;

    const { AISummaryContainerViewType, hasAISummaryFeedbackSubmitted } =
      getAISummaryFilterPreferences(filterPreferences);

    const { activeTab, panelId, location } = this.props;
    const entityId = getQueryParamValue(location, 'entityId');
    const contextValue = this.getContextValue(entityId, false, getNDCPreferences);

    return (
      <div>
        {_isEmpty(preferences) || isFetchingLead ? (
          <ActivityLogSkeleton />
        ) : (
          <ActivityLogsContext.Provider value={contextValue}>
            <TabPanel
              isCTCDisabled={isCTCDisabled}
              tabClassName={tabClassName}
              config={this.config}
              tabs={getActivityLogConfigList(this.config)}
              activeTab={activeTab}
              isFetching={isFetching}
              activityLogs={activityLogs}
              actions={actions}
              activityTabsCount={activityTabsCount}
              filter={filter}
              leadDetails={leadDetails}
              onTabChange={this.onTabChange}
              onAction={this.onActivityLogActions}
              panelId={panelId}
              create={this.create}
              onClickLeftAction={this.onClickLeftAction}
              notRespondedTabs={notRespondedTabs}
              users={users}
              noActivityConfig={this.getNoActivityConfig(activeTab, filter, {
                leadDetails,
              })}
              tabsConfigurator={this.renderTabsConfigurator}
              focusNoteEditor={focusNoteEditor}
              viewType={viewType}
              pastLeadsData={pastLeadsData}
              isCustomerOneViewEnabled={isCustomerOneViewEnabled}
              createBulkActivityLogsPayload={this.makeBulkActivityLogsPayload}
              createBulkActivityLogsPayloadV2={this.makeBulkActivityLogsPayloadV2}
              hasMoreActivity={hasMoreActivity}
              filterPreferences={filterPreferences}
              updateFilterPreferences={updateFilterPreferences}
              getDealerPropertyValue={getDealerPropertyValue}
              isTestDriveCrmSetupEnabled={isTestDriveCrmSetupEnabled}
              settings={settings}
              isDealOpen={isDealOpen}
              dealerInfo={dealerInfo}
              communicationOptinPreference={communicationOptinPreference}
              hasScrollReachedBottom={hasScrollReachedBottom}
              AISummaryContainerViewType={AISummaryContainerViewType}
              hasAISummaryFeedbackSubmitted={hasAISummaryFeedbackSubmitted}
              setHasMoreActivities={setHasMoreActivities}
              setActivityLogsGetterFns={setActivityLogsGetterFns}
              paymentOptionConfigs={paymentOptionConfigs}
            />
          </ActivityLogsContext.Provider>
        )}
        <PropertyControlledComponent controllerProperty={isRRG}>
          <VehicleContractModal
            visible={showContractModal}
            metaData={contractMetaData}
            onModalClose={this.hideContractModal}
          />
        </PropertyControlledComponent>
        {showTranscriptModal && (
          <CallDetails
            messageId={communicationMessageId}
            renderUserOrCampaignData={renderUserOrCampaignData}
            aiBdcUser={aiBdcUser}
            onCloseDrawer={this.hideTranscriptModal}
            containerClassName={styles.callDetailsDrawerContainer}
          />
        )}
      </div>
    );
  }
}

ActivityLogs.propTypes = {
  activeTab: PropTypes.string.isRequired,
  actions: PropTypes.object.isRequired,
  activityLogs: PropTypes.object.isRequired,
  activityTabsCount: PropTypes.object.isRequired,
  isDealOpen: PropTypes.bool,
  isFetchingLead: PropTypes.bool,
  leadDetails: PropTypes.object.isRequired,
  liveChatCount: PropTypes.number.isRequired,
  notRespondedTabs: PropTypes.array,
  onAction: PropTypes.func.isRequired,
  panelId: PropTypes.string.isRequired,
  parentOnAction: PropTypes.func,
  settings: PropTypes.object.isRequired,
  subscribePusher: PropTypes.func.isRequired,
  subscribeEvents: PropTypes.func.isRequired,
  unSubscribeEvents: PropTypes.func.isRequired,
  users: PropTypes.object.isRequired,
  pastLeadsData: PropTypes.array,
  isCustomerOneViewEnabled: PropTypes.bool,
  entityId: PropTypes.string,
  preferences: PropTypes.array,
  params: PropTypes.object.isRequired,
  hasMoreActivity: PropTypes.bool,
  updateUserPreferences: PropTypes.array,
  isFetching: PropTypes.bool,
  filterPreferences: PropTypes.array,
  getDealerPropertyValue: PropTypes.func,
  updateFilterPreferences: PropTypes.func,
  linkedLeadsCount: PropTypes.object,
  dealerInfo: PropTypes.object,
  communicationOptinPreference: PropTypes.object,
  aiBdcUser: PropTypes.object,
  hasScrollReachedBottom: PropTypes.bool,
  setHasMoreActivities: PropTypes.func,
  setActivityLogsGetterFns: PropTypes.func,
  paymentOptionConfigs: PropTypes.array,
  updateRouteUrl: PropTypes.bool,
  isCTCDisabled: PropTypes.bool,
};

ActivityLogs.defaultProps = {
  isDealOpen: false,
  isFetchingLead: false,
  notRespondedTabs: EMPTY_ARRAY,
  parentOnAction: _noop,
  pastLeadsData: EMPTY_ARRAY,
  isCustomerOneViewEnabled: false,
  entityId: EMPTY_STRING,
  preferences: EMPTY_ARRAY,
  hasMoreActivity: false,
  updateUserPreferences: EMPTY_ARRAY,
  isFetching: false,
  filterPreferences: EMPTY_ARRAY,
  getDealerPropertyValue: _noop,
  updateFilterPreferences: _noop,
  linkedLeadsCount: EMPTY_OBJECT,
  dealerInfo: EMPTY_OBJECT,
  communicationOptinPreference: EMPTY_OBJECT,
  aiBdcUser: EMPTY_OBJECT,
  hasScrollReachedBottom: false,
  setHasMoreActivities: _noop,
  setActivityLogsGetterFns: _noop,
  paymentOptionConfigs: EMPTY_ARRAY,
  updateRouteUrl: true,
  isCTCDisabled: false,
};

ActivityLogs.contextType = PermissionContext;

export default withMetadataContext(ActivityLogs);
