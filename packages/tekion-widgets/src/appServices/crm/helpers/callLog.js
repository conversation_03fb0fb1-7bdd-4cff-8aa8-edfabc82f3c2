import _isEmpty from 'lodash/isEmpty';
import _head from 'lodash/head';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { ACTIONS } from '@tekion/tekion-base/constants/crm/tasks';
import LeadsAPI from '@tekion/tekion-base/services/crm/leads.api';
import { MODAL_TYPE, showGlobalModal } from '@tekion/tekion-components/src/emitters/ModalEventEmitter';

import { makeBaseQueryParams } from '@tekion/tekion-business/src/appServices/communication/helpers/communicatoins.makeQueryParams';
import { showSuccess, showError } from '../utils/toaster';
import TaskReader from '../readers/task.reader';
import { ACTIVITY_LOG_TYPES } from '../organisms/activityLogs/activityLogs.constants';
import { getLogModalValues, getLeadName } from './lead';
import { getResolvedLeadDetails } from '../organisms/activityLogs/helpers/activityLogs.general';

const getLogInfo = (leadDetails, getFormattedPhoneNumber) => {
  const { options, to, toCountryCode } = getLogModalValues(
    leadDetails,
    ACTIVITY_LOG_TYPES.LOG_CALL,
    getFormattedPhoneNumber
  );
  const leadName = getLeadName(leadDetails);
  return { to, toCountryCode, leadName, logType: ACTIVITY_LOG_TYPES.LOG_CALL, options };
};

const onLogCommunicationSubmitCb = task => async payload => {
  try {
    const requestPayload = !_isEmpty(task)
      ? { ...(payload || EMPTY_OBJECT), taskIds: [TaskReader.taskId(task)] }
      : payload;
    const res = await LeadsAPI.logCommunication(requestPayload);
    if (res) {
      showSuccess(__('Task Completed'));
    } else {
      showError(__('Failed to log task'));
    }
  } catch (error) {
    showError(__('Failed to log task'));
  }
};

const getEventEmitterParams = async ({
  leadDetails,
  task,
  showTasksList,
  getFormattedPhoneNumber,
  additional = EMPTY_OBJECT,
  shouldFetchLeadData = false,
}) => {
  const { dealerId, siteId } = additional;
  const params = makeBaseQueryParams(dealerId, siteId);
  const resolvedLeadDetailsResponse = await getResolvedLeadDetails({ leadDetails, shouldFetchLeadData, params });
  const resolvedLeadDetails = _head(resolvedLeadDetailsResponse);
  const logInfo = getLogInfo(resolvedLeadDetails, getFormattedPhoneNumber);
  const onSubmitCb = onLogCommunicationSubmitCb(task);

  return { logInfo, onSubmitCb, showTasksList, leadDetails: resolvedLeadDetails, additional, shouldFetchLeadData };
};

export const onClickLog = async ({
  leadDetails,
  task,
  showTasksList = true,
  getFormattedPhoneNumber,
  additional,
  shouldFetchLeadData = false,
}) => {
  const eventEmitterParams = await getEventEmitterParams({
    leadDetails,
    task,
    showTasksList,
    getFormattedPhoneNumber,
    additional,
    shouldFetchLeadData,
  });
  showGlobalModal({
    modalType: MODAL_TYPE.LOG_MODAL,
    payload: eventEmitterParams,
  });
};

export const isLogTaskAllowed = (action, isCTCDisabled) => {
  if (action === ACTIONS.CALL) {
    return isCTCDisabled;
  }
  return false;
};
