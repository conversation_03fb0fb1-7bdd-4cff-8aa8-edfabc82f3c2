import _isEmpty from 'lodash/isEmpty';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { ACTIONS } from '@tekion/tekion-base/constants/crm/tasks';
import LeadsAPI from '@tekion/tekion-base/services/crm/leads.api';
import { MODAL_TYPE, showGlobalModal } from '@tekion/tekion-components/src/emitters/ModalEventEmitter';

import { showSuccess, showError } from '../utils/toaster';
import TaskReader from '../readers/task.reader';
import { ACTIVITY_LOG_TYPES } from '../organisms/activityLogs/activityLogs.constants';
import { getLogModalValues, getLeadName } from './lead';

const getLogInfo = (leadDetails, getFormattedPhoneNumber) => {
  const { options, to, toCountryCode } = getLogModalValues(
    leadDetails,
    ACTIVITY_LOG_TYPES.LOG_CALL,
    getFormattedPhoneNumber
  );
  const leadName = getLeadName(leadDetails);
  return { to, toCountryCode, leadName, logType: ACTIVITY_LOG_TYPES.LOG_CALL, options };
};

const onLogCommunicationSubmitCb = task => async payload => {
  try {
    const requestPayload = !_isEmpty(task)
      ? { ...(payload || EMPTY_OBJECT), taskIds: [TaskReader.taskId(task)] }
      : payload;
    const res = await LeadsAPI.logCommunication(requestPayload);
    if (res) {
      showSuccess(__('Task Completed'));
    } else {
      showError(__('Failed to log task'));
    }
  } catch (error) {
    showError(__('Failed to log task'));
  }
};

const getEventEmitterParams = ({
  leadDetails,
  task,
  showTasksList,
  getFormattedPhoneNumber,
  additional,
  shouldFetchLeadData,
}) => {
  const logInfo = getLogInfo(leadDetails, getFormattedPhoneNumber);
  const onSubmitCb = onLogCommunicationSubmitCb(task);

  return { logInfo, onSubmitCb, showTasksList, leadDetails, additional, shouldFetchLeadData };
};

export const onClickLog = async ({
  leadDetails,
  task,
  showTasksList = true,
  getFormattedPhoneNumber,
  additional,
  shouldFetchLeadData = false,
}) => {
  const eventEmitterParams = getEventEmitterParams({
    leadDetails,
    task,
    showTasksList,
    getFormattedPhoneNumber,
    additional,
    shouldFetchLeadData,
  });
  showGlobalModal({
    modalType: MODAL_TYPE.LOG_MODAL,
    payload: eventEmitterParams,
  });
};

export const isLogTaskAllowed = (action, isCTCDisabled) => {
  if (action === ACTIONS.CALL) {
    return isCTCDisabled;
  }
  return false;
};
