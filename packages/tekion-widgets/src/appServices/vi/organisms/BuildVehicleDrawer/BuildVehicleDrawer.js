import React, { use<PERSON>allback, useEffect, memo, useMemo } from 'react';
import Proptypes from 'prop-types';
import _noop from 'lodash/noop';
import _filter from 'lodash/filter';
import _includes from 'lodash/includes';

import Drawer from '@tekion/tekion-components/src/molecules/drawer';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import { Heading } from '@tekion/tekion-components/src/atoms';
import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import { ACTION_TYPES, EVENTS, MODULE, TAB_ENUMS, VALIDATOR_TYPE } from './buildVehicleDrawer.constants';
import buildVehicleDrawerEvents from './buildVehicleDrawer.events';
import VehicleDetails from './VehicleDetails';

import styles from './buildVehicleDrawer.module.scss';

/**
 * BuildVehicleDrawer component.
 *
 * This component is responsible for displaying a drawer interface
 * for building a vehicle. It accepts various props to customize its behavior
 * and display.
 *
 * @component
 * @param {Object} props - The props object.
 * @param {string} props.module - The module name based on this Field in the form will be shown. Required.
 * @param {function} props.onSubmitCb - The callback function to be called upon submission. Required.
 * @param {function} props.onCancelCb - The callback function to be called upon cancel.
 * @param {Object} [props.data] - The data object that contains pre-existing data for the vehicle build (Edit Mode). Optional.
 * @param {React.ReactNode} [props.headerTitle] - A React node to customize the header title of the drawer. Optional.
 * @param {Array} props.permissions - An array of permissions required to interact with the component. Required.
 * @param {Array} props.tabsToHide - An array of permissions required to interact with the component. Required.
 * @param {Object} props.salesSetup - Sales Setup Object
 * @param {Object} props.formFieldOverrides - Form Field Overrides
 * @param {boolean} props.allFieldsDisabledByDefault - Disable all fields by default
 * @param {string | number} props.dealerId - To Fetch Dealer Specific Configurations
 *
 */

const BuildVehicleDrawer = ({ onAction, visibility, headerTitle, tabsToHide, ...restProps }) => {
  const BODY_STYLE = {
    height: '100%',
    padding: 0,
  };

  const handleAction = useCallback(
    type => payload =>
      onAction({
        type,
        payload,
      }),
    [onAction]
  );

  const showDrawer = useCallback(
    options => handleAction(ACTION_TYPES.SHOW_BUILD_VEHICLE_DRAWER)(options),
    [handleAction]
  );
  const hideDrawer = useCallback(() => handleAction(ACTION_TYPES.HIDE_BUILD_VEHICLE_DRAWER)(), [handleAction]);

  useEffect(() => {
    buildVehicleDrawerEvents.on(EVENTS.SHOW_BUILD_VEHICLE_DRAWER, showDrawer);
    buildVehicleDrawerEvents.on(EVENTS.HIDE_BUILD_VEHICLE_DRAWER, hideDrawer);
    return () => {
      buildVehicleDrawerEvents.removeAllListeners();
    };
  }, [showDrawer, hideDrawer]);

  const viLiteTabs = useMemo(() => {
    const tabsKeys = Object.keys(TAB_ENUMS);
    return _filter(tabsKeys, key => !_includes(tabsToHide, key));
  }, [tabsToHide]);

  return (
    <Drawer
      className={styles.drawer}
      bodyStyle={BODY_STYLE}
      zIndex={1}
      width="80%"
      title={headerTitle}
      onClose={hideDrawer}
      destroyOnClose
      visible={visibility}>
      <PropertyControlledComponent controllerProperty={visibility}>
        <VehicleDetails {...restProps} hideDrawer={hideDrawer} viLiteTabs={viLiteTabs} lite />
      </PropertyControlledComponent>
    </Drawer>
  );
};

BuildVehicleDrawer.propTypes = {
  module: Proptypes.string,
  onSubmitCb: Proptypes.func,
  data: Proptypes.object,
  headerTitle: Proptypes.node,
  permissions: Proptypes.array,
  tabsToHide: Proptypes.array,
  salesSetup: Proptypes.object,
  formFieldOverrides: Proptypes.shape({
    disabled: Proptypes.bool,
    hidden: Proptypes.bool,
    required: Proptypes.bool,
    options: Proptypes.array,
    validators: Proptypes.array,
    component: Proptypes.node,
    validatorType: Proptypes.oneOf(VALIDATOR_TYPE),
  }),
  allFieldsDisabledByDefault: Proptypes.bool,
  dealerId: Proptypes.oneOfType([Proptypes.string, Proptypes.number]),
};

BuildVehicleDrawer.defaultProps = {
  module: MODULE.VI,
  onSubmitCb: _noop,
  data: EMPTY_OBJECT,
  headerTitle: <Heading size={2}>{__('Build Vehicle')}</Heading>,
  permissions: EMPTY_ARRAY,
  tabsToHide: EMPTY_ARRAY,
  salesSetup: EMPTY_OBJECT,
  formFieldOverrides: EMPTY_OBJECT,
  allFieldsDisabledByDefault: false,
  dealerId: null,
};

export default memo(BuildVehicleDrawer);
