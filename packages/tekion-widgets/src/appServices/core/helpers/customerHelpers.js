import _omit from 'lodash/omit';
import _concat from 'lodash/concat';
import _isEmpty from 'lodash/isEmpty';
import _head from 'lodash/head';
import _castArray from 'lodash/castArray';
import _map from 'lodash/map';
import _find from 'lodash/find';
import _filter from 'lodash/filter';
import _isNumber from 'lodash/isNumber';
import _size from 'lodash/size';
import _get from 'lodash/get';
import _includes from 'lodash/includes';
import _reduce from 'lodash/reduce';
import _values from 'lodash/values';
import _findIndex from 'lodash/findIndex';
import _toUpper from 'lodash/toUpper';
import _uniq from 'lodash/uniq';
import _compact from 'lodash/compact';
import _identity from 'lodash/identity';
import _noop from 'lodash/noop';
import _pick from 'lodash/pick';
import _join from 'lodash/join';
import _isBoolean from 'lodash/isBoolean';

import { maskData, tget } from '@tekion/tekion-base/utils/general';
import { getUnix, toMoment } from '@tekion/tekion-base/utils/dateUtils';
import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { getFormattedPhoneNum, getPhoneNo } from '@tekion/tekion-base/utils/phoneNumberInputUtils';
import updateElementAtIndex from '@tekion/tekion-base/utils/updateElementAtIndex';
import { getLookupByKeys } from '@tekion/tekion-base/services/lookupService';
import { RESOURCE_TYPE } from '@tekion/tekion-base/bulkResolvers/constants/resourceType';
import { PHONE_TYPES } from '@tekion/tekion-base/constants/customer.constants';
import customerReader from '@tekion/tekion-base/readers/Customer';
import { getFilteredPhones } from '@tekion/tekion-base/helpers/customer.helper';
import LOCALES from '@tekion/tekion-base/constants/locales';

import { phoneNumberInput } from '@tekion/tekion-conversion-web';

import { PhoneNumberValue } from '../../../fieldRenderers/phoneNumberInputField';
import {
  PREFERRED_CONTACT_TYPE_MAP,
  CUSTOMER_TYPES,
  CONTACT_FIELDS,
  SEARCH_CONTACT,
  PREFERRED_COMMUNICATION_TYPES,
  PREFERRED_COMMUNICATION,
  DEFAULT_COMMUNICATION_PREFERENCES,
  FIELDS_TO_OMIT_ON_SAVE,
  SIREN_LENGTH,
  DEFAULT_RRG_COUNTRY,
  FIELDS_TO_OMIT_FOR_INDIVIDUAL_RRG_CUSTOMER,
  COMPANY_DETAILS_FIELDS,
  FIELD_IDS_TO_FILTER_LIST_FOR_INCHCAPE,
  FIELDS_TO_OMIT_FOR_BUSINESS_RRG_CUSTOMER,
} from '../constants/customerCommonConstants';
import { getValueForStaticList } from '../components/OemLoyalty/oemLoyalty.columns';

const { VERIFICATION_STATUS } = phoneNumberInput;

export const getCastArrayValuesFromApi = data => {
  if (_isNumber(data)) return _castArray(data);
  if (!data) return data;
  return _castArray(data);
};

const getDataWithLastRow = (data = EMPTY_ARRAY) => _concat(data, [{ isLastRow: true }]);

const getAdditionalDetailsInLabelValueFormat = (data, formatter = _identity) =>
  _map(data, (item, key) => ({ label: getCastArrayValuesFromApi(key), value: formatter(item) }));

const getPhoneForForm = defaultPhoneCode => phoneDetail => ({
  phone: getFormattedPhoneNum(customerReader.number(phoneDetail)),
  phoneCode: _get(phoneDetail, 'extension') || defaultPhoneCode,
  capencyVerified: _get(phoneDetail, 'capencyVerified'),
});

export const getBasicDetailsUpgradedFields = (initialValues, isInchcapeEnabled, rrgProgram) => {
  if (!(isInchcapeEnabled || rrgProgram)) return EMPTY_OBJECT;
  const reciprocalTrading = customerReader.reciprocalTrading(initialValues) || EMPTY_OBJECT;
  const legacyDMSNumber = customerReader.legacyDMSNumber(initialValues) || EMPTY_STRING;
  const isMotabilityCustomer = customerReader.motabilityCustomer(initialValues) || false;
  const { vendorId = EMPTY_STRING } = reciprocalTrading;
  let values = {
    legacyDMSNumber,
    isMotabilityCustomer,
  };
  if (vendorId) {
    values = {
      ...values,
      reciprocalTrading: true,
      reciprocalAccount: _castArray(vendorId),
    };
  }

  return values;
};

export const getInitialValuesForBasicDetails = (
  initialValues,
  defaultPhoneCode,
  oemOptionsSize,
  hasViewCustomerSensitiveInfo,
  languages = EMPTY_ARRAY,
  dealerLanguage = LOCALES.EN_US,
  rrgProgram = false,
  basicDetailsUpgradedFieldsEnabled = false
) => {
  const contact = customerReader.phones(initialValues) || EMPTY_ARRAY;
  const socialSecurityNumber = customerReader.socialSecurityNumber(initialValues) || EMPTY_STRING;
  const fax = customerReader.fax(initialValues);
  const loyaltyInfoByOEMs = _map(tget(initialValues, 'loyaltyInfoByOEMs', EMPTY_ARRAY), item => ({
    ...item,
    oemId: getCastArrayValuesFromApi(tget(item, 'oemId')),
  }));
  const customerRewardsInfos = _map(tget(initialValues, 'customerRewardsInfos', EMPTY_ARRAY), item => ({
    ...item,
    oem: getCastArrayValuesFromApi(tget(item, 'oem')),
    status: _toUpper(tget(item, 'status')),
  }));
  const preferredLanguage = customerReader.preferredLanguage(initialValues) || dealerLanguage;
  const showNameOnDigitalDisplay = customerReader.showNameOnDigitalDisplay(initialValues);
  const dateOfBirth = customerReader.dateOfBirth(initialValues);
  const subPayType = customerReader.subPayTypeFromConfig(initialValues);
  const inheritPayTypeFromClientCategory = customerReader.inheritPayTypeFromClientCategory(initialValues);
  return {
    ...(rrgProgram
      ? {
          siret: getCastArrayValuesFromApi(customerReader.siret(initialValues)),
          shortName: customerReader.shortName(initialValues),
          businessNameId: getCastArrayValuesFromApi(customerReader.siret(initialValues)),
          ...(customerReader.nafActivity(initialValues) || EMPTY_OBJECT),
          typology: customerReader.typology(initialValues),
          countryOfRegistration: customerReader.countryOfRegistration(initialValues) || DEFAULT_RRG_COUNTRY,
          additionalEmails: getAdditionalDetailsInLabelValueFormat(customerReader.additionalEmails(initialValues)),
          additionalPhones: getAdditionalDetailsInLabelValueFormat(
            customerReader.additionalPhones(initialValues),
            getPhoneForForm(defaultPhoneCode)
          ),
          insuranceCode: customerReader.insuranceCode(initialValues),
          insuranceActivity: customerReader.insuranceActivity(initialValues),
          multiInsurance: !!customerReader.multiInsurance(initialValues),
          insurancePlatform: customerReader.insurancePlatform(initialValues),
          clientReferenceCodeEnabled: customerReader.clientReferenceCodeEnabled(initialValues),
          dateOfBirth: dateOfBirth ? toMoment(customerReader.dateOfBirth(initialValues)) : null,
          invoicingSiret: customerReader.invoicingSiret(initialValues),
          jobTitle: customerReader.jobTitle(initialValues),
          stopCommunication: tget(initialValues, 'stopCommunication', false),
          companyCreationDate: tget(initialValues, 'companyCreationDate'),
        }
      : EMPTY_OBJECT),
    clientCategory: customerReader.clientCategory(initialValues),
    ...getBasicDetailsUpgradedFields(initialValues, basicDetailsUpgradedFieldsEnabled, rrgProgram),
    customerType: customerReader.customerType(initialValues) ? CUSTOMER_TYPES.BUSINESS : CUSTOMER_TYPES.INDIVIDUAL,
    businessName: customerReader.companyName(initialValues) || customerReader.businessName(initialValues),
    parentCompany: customerReader.parentCompany(initialValues),
    externalId: customerReader.externalId(initialValues),
    vip: !!customerReader.vip(initialValues),
    firstName: customerReader.firstName(initialValues),
    alternateFirstName: customerReader.alternateFirstName(initialValues),
    alternateLastName: customerReader.alternateLastName(initialValues),
    alternateName: customerReader.alternateName(initialValues),
    lastName: customerReader.lastName(initialValues),
    ...getValuesForNumbersInDefaultContact(contact, defaultPhoneCode),
    email: tget(initialValues, 'email'),
    alternateEmail: customerReader.alternateEmail(initialValues),
    fax: !_isEmpty(fax) ? _head(fax) : null,
    preferredCommunicationMode: getPreferredCommunicationMode(tget(initialValues, 'preferredContactType')),
    communicationPreferences: tget(initialValues, 'communicationPreferences', DEFAULT_COMMUNICATION_PREFERENCES),
    socialSecurityNumber: !hasViewCustomerSensitiveInfo ? maskData(socialSecurityNumber) : socialSecurityNumber,
    salutation: customerReader.salutation(initialValues),
    suffix: customerReader.suffix(initialValues),
    middleName: customerReader.middleName(initialValues),
    phones: getFilteredPhones(contact),
    loyaltyInfoByOEMs:
      _size(loyaltyInfoByOEMs) === oemOptionsSize ? loyaltyInfoByOEMs : getDataWithLastRow(loyaltyInfoByOEMs),
    customerRewardsInfos: !_isEmpty(customerRewardsInfos)
      ? customerRewardsInfos
      : getDataWithLastRow(customerRewardsInfos),
    preferredLanguage: _includes(_map(languages, 'value'), preferredLanguage) ? preferredLanguage : null,
    showNameOnDigitalDisplay,
    subType: customerReader.subType(initialValues),
    businessWebsite: customerReader.businessWebsite(initialValues),
    businessClassification: customerReader.businessClassification(initialValues),
    industry: customerReader.industry(initialValues),
    businessType: customerReader.businessType(initialValues),
    // handling a special case where BE is sending subpaytype when inheritPayTypeFromClientCategory is also true
    // UI will fetch the current default subpaytype and show it on Screen
    subPayType: inheritPayTypeFromClientCategory ? null : subPayType,
  };
};

const getUpdatedPhonesValue = (type, prevIndexes, updatedPhone, typeNo, typeDigit, typeObject) => {
  let updatedPhones = updatedPhone;
  const prevMobileIndex = tget(prevIndexes, type);
  const objectToUpdate = {
    number: getPhoneNo(typeNo),
    phoneType: typeDigit,
    isPrimary: !!tget(typeObject, 'checked'),
    extension: tget(typeObject, 'phoneCode'),
    capencyVerified: _get(typeObject, 'capencyVerified'),
  };
  if (prevMobileIndex === -1) {
    updatedPhones = _concat(updatedPhones, objectToUpdate);
  } else {
    updatedPhones = updateElementAtIndex(updatedPhones, objectToUpdate, prevMobileIndex);
  }
  return updatedPhones;
};

const getPhonesValueFromDefaultContact = (prevIndexes, prevPhones, values) => {
  const { mobile = EMPTY_OBJECT, phone = EMPTY_OBJECT, work = EMPTY_OBJECT } = values;
  let updatedPhones = prevPhones;
  const mobileNo = tget(mobile, 'phone');
  const phoneNo = tget(phone, 'phone');
  const workNo = tget(work, 'phone');
  if (mobileNo || tget(prevIndexes, 'mobile') !== -1) {
    updatedPhones = getUpdatedPhonesValue('mobile', prevIndexes, updatedPhones, mobileNo, PHONE_TYPES.MOBILE, mobile);
  }
  if (phoneNo || tget(prevIndexes, 'phone') !== -1) {
    updatedPhones = getUpdatedPhonesValue('phone', prevIndexes, updatedPhones, phoneNo, PHONE_TYPES.HOME, phone);
  }
  if (workNo || tget(prevIndexes, 'work') !== -1) {
    updatedPhones = getUpdatedPhonesValue('work', prevIndexes, updatedPhones, workNo, PHONE_TYPES.WORK, work);
  }
  return updatedPhones;
};

export const getSelectedInputValue = field => (_isEmpty(field) ? null : _head(field));

export const getContactFromPhones = phones =>
  _map(phones, phone => ({
    ..._omit(phone, 'phoneType'),
    type: tget(phone, 'phoneType'),
  }));

const getPreferredContactTypeForBackend = values => {
  const preferredCommunicationMode = tget(values, 'preferredCommunicationMode');
  if (!preferredCommunicationMode) return null;
  return PREFERRED_CONTACT_TYPE_MAP[preferredCommunicationMode];
};

export const getPrimaryPhone = phones => {
  if (_isEmpty(phones)) return null;
  const preferredPhoneObject = _find(phones, phone => tget(phone, 'isPrimary'));
  if (!_isEmpty(preferredPhoneObject)) return tget(preferredPhoneObject, 'number');
  return tget(_head(phones), 'number');
};

const getLoyaltyInfoForBackend = loyaltyInfoByOEMs =>
  _map(getValueForStaticList(loyaltyInfoByOEMs), item => _omit(item, 'isLastRow'));

const getAdditonalPhoneFormatterForBackend = data => ({
  number: getPhoneNo(_get(data, 'phone')),
  extension: _get(data, 'phoneCode'),
  capencyVerified: _get(data, 'capencyVerified'),
});

const getAdditionalDetailsForBackend = (data, formatter = _identity) =>
  _reduce(
    data,
    (acc, item) => ({
      ...acc,
      [getSelectedInputValue(item?.label)]: formatter(item?.value),
    }),
    EMPTY_OBJECT
  );

export const getFormValueMapForBasicDetails = (
  initialValues = EMPTY_OBJECT,
  values = EMPTY_OBJECT,
  rrgProgram = false,
  basicDetailsUpgradedFieldsEnabled = false
) => {
  const {
    customerType,
    fax,
    mobile,
    phone,
    work,
    preferredContactType,
    loyaltyInfoByOEMs,
    prevIndexes,
    salutation,
    suffix,
    siret,
    additionalEmails,
    additionalPhones,
    ...rest
  } = values;
  const prevPhones = tget(initialValues, 'phones', EMPTY_ARRAY);
  const phones = getPhonesValueFromDefaultContact(prevIndexes, prevPhones, values);
  let valuesToSend = initialValues;
  const isBusinessCustomer = customerType === CUSTOMER_TYPES.BUSINESS;
  if (isBusinessCustomer) {
    valuesToSend = {
      ...valuesToSend,
      customerType: 1,
    };
  } else {
    valuesToSend = {
      ...valuesToSend,
      customerType: 0,
    };
  }
  let returnData = {
    ..._omit(valuesToSend, ['preferredCommunicationMode', 'customerRewardsInfos']),
    ..._omit({ ...rest }, [...FIELDS_TO_OMIT_ON_SAVE]),
    additionalEmails: getAdditionalDetailsForBackend(additionalEmails),
    additionalPhones: getAdditionalDetailsForBackend(additionalPhones, getAdditonalPhoneFormatterForBackend),
    salutation,
    suffix,
    fax: fax ? _castArray(fax) : null,
    phones,
    contact: getContactFromPhones(phones),
    preferredContactType: getPreferredContactTypeForBackend(values),
    primaryPhone: getPrimaryPhone(phones),
    companyName: tget(values, 'businessName'),
    loyaltyInfoByOEMs: getLoyaltyInfoForBackend(loyaltyInfoByOEMs),
    companyDetails: _pick(values, COMPANY_DETAILS_FIELDS),
  };

  if (rrgProgram || basicDetailsUpgradedFieldsEnabled) {
    const setup = tget(returnData, 'setup', EMPTY_OBJECT);
    const service = tget(setup, 'service', EMPTY_OBJECT);
    returnData = {
      ...returnData,
      setup: {
        ...setup,
        service: { ...service, coreReturnEnabled: true },
      },
    };
  }

  if (rrgProgram) {
    if (isBusinessCustomer) {
      returnData = {
        ..._omit(returnData, FIELDS_TO_OMIT_FOR_BUSINESS_RRG_CUSTOMER),
        siret: getSelectedInputValue(siret),
        nafActivity: {
          nafActivity: _get(rest, 'nafActivity'),
          nafLabel: _get(rest, 'nafLabel'),
        },
        insuranceInfo: {
          insuranceCode: rest?.insuranceCode,
          insuranceActivity: rest?.insuranceActivity,
          multiInsurance: rest?.multiInsurance,
          insurancePlatform: rest?.insurancePlatform || null,
        },
      };
    } else {
      const setup = tget(returnData, 'setup', EMPTY_OBJECT);
      const sale = tget(setup, 'sale', EMPTY_OBJECT);
      const dateOfBirth = tget(values, 'dateOfBirth');
      returnData = {
        ..._omit(returnData, FIELDS_TO_OMIT_FOR_INDIVIDUAL_RRG_CUSTOMER),
        setup: {
          ...setup,
          sale: { ...sale, dateOfBirth: dateOfBirth ? getUnix(dateOfBirth) : null },
        },
        insuranceInfo: {
          insurancePlatform: rest?.insurancePlatform || null,
        },
      };
    }
  }

  if (basicDetailsUpgradedFieldsEnabled || rrgProgram) {
    const reciprocalAccount = _head(tget(values, 'reciprocalAccount', EMPTY_ARRAY)) || EMPTY_STRING;
    returnData = _omit(returnData, ['reciprocalAccount', 'reciprocalTrading']);
    if (reciprocalAccount) {
      returnData = {
        ...returnData,
        reciprocalTrading: {
          vendorId: reciprocalAccount,
        },
      };
    }
  }
  return returnData;
};

const setNumberValues = (values, prefCommValue) => {
  let formValues = values;
  const { mobile, work, phone } = formValues;
  const array = _concat(mobile, work, phone);
  const ind = _findIndex(array, 'phone');
  if (prefCommValue === PREFERRED_COMMUNICATION_TYPES.CALL || prefCommValue === PREFERRED_COMMUNICATION_TYPES.SMS) {
    const checkedObject = _find(array, item => _get(item, 'checked'));
    if (_isEmpty(checkedObject)) {
      if (ind !== -1) {
        if (ind === 0) formValues = { ...formValues, mobile: { ...mobile, checked: true } };
        else if (ind === 1) formValues = { ...formValues, work: { ...work, checked: true } };
        else formValues = { ...formValues, phone: { ...phone, checked: true } };
      } else {
        formValues = { ...formValues, mobile: { phone: '', checked: true, phoneCode: mobile.phoneCode || '+1' } };
      }
    }
  } else if (ind === -1)
    formValues = { ...formValues, mobile: { phone: '', checked: false, phoneCode: mobile.phoneCode || '+1' } };
  return formValues;
};

const getPreferredCommunicationMode = data => {
  if (!_isNumber(data)) return data;
  if (data === 3) return PREFERRED_COMMUNICATION_TYPES.POSTAL;
  if (data === 0) return PREFERRED_COMMUNICATION_TYPES.CALL;
  if (data === 1) return PREFERRED_COMMUNICATION_TYPES.EMAIL;
  return PREFERRED_COMMUNICATION_TYPES.SMS;
};

const getPreferredContactType = (mobile, work, phone) => {
  if (tget(mobile, 'checked')) return PHONE_TYPES.MOBILE;
  if (tget(phone, 'checked')) return PHONE_TYPES.HOME;
  if (tget(work, 'checked')) return PHONE_TYPES.WORK;
  return -1;
};

const getValuesForNumbersInDefaultContact = (contact, defaultPhoneCode) => {
  const mobileContact = getValueFromContact(contact, PHONE_TYPES.MOBILE, defaultPhoneCode);
  const phoneContact = getValueFromContact(contact, PHONE_TYPES.HOME, defaultPhoneCode);
  const workContact = getValueFromContact(contact, PHONE_TYPES.WORK, defaultPhoneCode);
  const mobile = tget(mobileContact, 'contact');
  const phone = tget(phoneContact, 'contact');
  const work = tget(workContact, 'contact');
  return {
    prevIndexes: {
      mobile: tget(mobileContact, 'prevIndex'),
      phone: tget(phoneContact, 'prevIndex'),
      work: tget(workContact, 'prevIndex'),
    },
    mobile,
    phone,
    work,
    preferredContactType: getPreferredContactType(mobile, work, phone),
  };
};

const getValueFromContact = (contacts, type, defaultPhoneCode) => {
  const contactsOfType = _filter(contacts, contact => tget(contact, 'phoneType') === type);
  if (_isEmpty(contactsOfType))
    return { contact: { phone: null, checked: false, phoneCode: defaultPhoneCode }, prevIndex: -1 };
  const preferredContact = _filter(contactsOfType, contact => tget(contact, 'isPrimary'));
  if (!_isEmpty(preferredContact)) {
    const firstNumber = _head(preferredContact);
    return {
      contact: {
        phone: getFormattedPhoneNum(tget(firstNumber, 'number')),
        checked: true,
        phoneCode: _get(firstNumber, 'extension') || defaultPhoneCode,
        capencyVerified: _get(firstNumber, 'capencyVerified'),
        dndStatus: !!_get(firstNumber, 'dndStatus'),
      },
      prevIndex: _findIndex(contacts, _head(preferredContact)),
    };
  }
  const remainingContacts = _filter(contactsOfType, contact => !!tget(contact, 'number'));
  const contact = _isEmpty(remainingContacts) ? _head(contactsOfType) : _head(remainingContacts);
  return {
    contact: {
      phone: getFormattedPhoneNum(tget(contact, 'number')),
      checked: false,
      phoneCode: _get(contact, 'extension') || defaultPhoneCode,
      capencyVerified: _get(contact, 'capencyVerified'),
      dndStatus: !!_get(contact, 'dndStatus'),
    },
    prevIndex: _findIndex(contacts, contact),
  };
};

const getValueMapForPointOfContact = (value, defaultPhoneCode) => {
  const title = getCastArrayValuesFromApi(_get(value, 'salutation'));
  const suffix = getCastArrayValuesFromApi(_get(value, 'suffix'));
  const preferredCommunicationMode = _get(value, 'preferredContactType');
  const firstName = _get(value, 'firstName');
  const lastName = _get(value, 'lastName');
  const phones = _get(value, 'phones');
  const email = _get(value, 'email');
  const fax = _get(value, 'fax');
  return {
    title,
    suffix,
    preferredCommunicationMode: getPreferredCommunicationMode(preferredCommunicationMode),
    firstName,
    lastName,
    email,
    fax: fax ? _head(fax) : null,
    ...getValuesForNumbersInDefaultContact(phones, defaultPhoneCode),
  };
};

export const handlePointOfContactFieldChange = async (
  action = EMPTY_OBJECT,
  { getState, setState },
  callBackFunc = _noop
) => {
  const { payload } = action;
  const { id, value } = payload;
  const { values, defaultPhoneCode } = getState();
  const prevValue = _get(values, `${id}`);
  const prevChecked = _get(prevValue, 'checked');
  const currValue = _get(value, 'phone');
  const currChecked = _get(value, 'checked');
  let formValues = { ...values };
  if (_includes(CONTACT_FIELDS, id)) {
    if (!prevChecked && currChecked) {
      const returnValues = _reduce(
        CONTACT_FIELDS,
        (acc, field) => {
          const fieldValue = formValues[field];
          const phone = _get(fieldValue, 'phone');
          if (id === field && phone && phone !== EMPTY_STRING) {
            return {
              ...acc,
              [id]: {
                ...fieldValue,
                checked: true,
              },
            };
          }
          return {
            ...acc,
            [field]: {
              ...fieldValue,
              checked: false,
            },
          };
        },
        formValues
      );
      setState({ values: returnValues }, () => callBackFunc({ setState, getState, params: payload }));
      return;
    }
    const setValue = {
      ...value,
      phone: currValue,
    };
    formValues = {
      ...formValues,
      [id]: !currValue ? { ...setValue, checked: false } : { ...setValue },
      [`prev${id}`]: currValue,
    };
    formValues = setNumberValues(formValues, _get(formValues, PREFERRED_COMMUNICATION));
  } else if (id === SEARCH_CONTACT) {
    const customer = await getLookupByKeys(RESOURCE_TYPE.CUSTOMER, value);
    const customerData = _get(_head(_values(customer)), 'data');
    formValues = {
      ...values,
      ...getValueMapForPointOfContact(customerData, defaultPhoneCode),
      [id]: value,
    };
  } else if (id === PREFERRED_COMMUNICATION) {
    formValues = setNumberValues(values, value);
    formValues = { ...formValues, [id]: value };
  } else {
    formValues = { ...formValues, [id]: value };
  }
  setState({ values: formValues }, () => callBackFunc({ setState, getState, params: payload }));
};

export const getSingleSelectField = (value = EMPTY_ARRAY, options = EMPTY_ARRAY) => {
  const currValue = _head(value);
  const outputLabel = _find(options, option => option.value === currValue);
  const returnValue = _isEmpty(outputLabel) ? null : outputLabel.label;
  return returnValue;
};

export const getUniqIds = (items = EMPTY_ARRAY, field = '') => _uniq(_compact(_map(items, field)));

export const getSirenFromSiret = siret => (siret || '').slice(0, SIREN_LENGTH);

export const getRemainingSiretAfterSiren = siret => (siret || '').slice(SIREN_LENGTH);

export const getSelectedThreeDigitCountryCode = (countries, extension) =>
  _get(
    _find(countries, country => country?.extensionCode === extension.slice(1)),
    'threeDigitCountryCode',
    'FRA'
  );

export const getPhoneNumberValue = (value, defaultPhoneCode) => {
  const phoneNumber = tget(value, 'phone');
  const phoneNumberValue = new PhoneNumberValue(tget(value, 'phoneCode', defaultPhoneCode)).setPhoneNumber(
    getPhoneNo(phoneNumber)
  );
  const capencyVerified = _get(value, 'capencyVerified');
  if (phoneNumber && _isBoolean(capencyVerified)) {
    phoneNumberValue.setVerificationStatus(
      capencyVerified ? VERIFICATION_STATUS.VERIFIED : VERIFICATION_STATUS.NOT_VERIFIED
    );
  }
  return phoneNumberValue;
};

export const getMultiSelectOptionLabels = (value, options) =>
  _reduce(options, (acc, option) => (_includes(value, option?.value) ? [...acc, option?.label] : acc), EMPTY_ARRAY);

export const getMultiSelectConcatenatedLabel = (value, options) =>
  _join(getMultiSelectOptionLabels(value, options), ', ');

export const getCapencyVerifiedValue = verificationStatus => {
  if (verificationStatus === VERIFICATION_STATUS.VERIFIED) return true;
  if (verificationStatus === VERIFICATION_STATUS.NOT_VERIFIED || verificationStatus === VERIFICATION_STATUS.FAILED)
    return false;
  return null;
};

export const getFilteredColumnsForInchcapeReportGeneration = columns =>
  _filter(columns, column => !_includes(FIELD_IDS_TO_FILTER_LIST_FOR_INCHCAPE, column?.key));

export const isNonFrenchCustomer = (country = EMPTY_STRING) => country !== DEFAULT_RRG_COUNTRY;
