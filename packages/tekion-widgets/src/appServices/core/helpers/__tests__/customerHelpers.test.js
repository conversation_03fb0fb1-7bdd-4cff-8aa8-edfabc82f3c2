import mockDataFactory from '@tekion/tekion-mock-data/src/factory';
import {
  getSingleSelectField,
  getInitialValuesForBasicDetails,
  getFormValueMapForBasicDetails,
  getUniqIds,
  getSirenFromSiret,
  getRemainingSiretAfterSiren,
  getSelectedInputValue,
  getSelectedThreeDigitCountryCode,
  getMultiSelectConcatenatedLabel,
  getMultiSelectOptionLabels,
  getBasicDetailsUpgradedFields,
  getCapencyVerifiedValue,
  getFilteredColumnsForInchcapeReportGeneration,
  isNonFrenchCustomer,
} from '../customerHelpers';

const CUSTOMER_INPUT_1 = mockDataFactory.get('/customer/basicDetails/initialValues/input2');
const CUSTOMER_INPUT_2 = mockDataFactory.get('/customer/basicDetails/initialValues/input3');
const CUSTOMER_INPUT_3 = mockDataFactory.get('/customer/basicDetails/initialValues/input1');
const FORMVALUES_INPUT_1 = mockDataFactory.get('/customer/basicDetails/formValues/input1');
const FORMVALUES_INPUT_2 = mockDataFactory.get('/customer/basicDetails/formValues/input2');
const FORMVALUES_INPUT_3 = mockDataFactory.get('/customer/basicDetails/formValues/input3');
const FORMVALUES_INPUT_4 = mockDataFactory.get('/customer/basicDetails/formValues/input4');
const FORMVALUES_OUTPUT_1 = mockDataFactory.get('/customer/basicDetails/formValues/output1');
const FORMVALUES_OUTPUT_2 = mockDataFactory.get('/customer/basicDetails/formValues/output2');
const FORMVALUES_OUTPUT_3 = mockDataFactory.get('/customer/basicDetails/formValues/output3');

describe('testing customerHelpers', () => {
  it('get Single Select label for Non Edit Form', () => {
    expect(getSingleSelectField()).toBeNull();
    expect(
      getSingleSelectField(
        ['abcd'],
        [
          { label: 'efgh', value: 'abcd' },
          { label: 'aeiou', value: 'bcde' },
        ]
      )
    ).toEqual('efgh');
    expect(
      getSingleSelectField(
        ['bcde'],
        [
          { label: 'efgh', value: 'abcd' },
          { label: 'aeiou', value: 'bcde' },
        ]
      )
    ).toEqual('aeiou');
    expect(
      getSingleSelectField(
        ['abc'],
        [
          { label: 'efgh', value: 'abcd' },
          { label: 'aeiou', value: 'bcde' },
        ]
      )
    ).toBeNull();
    expect(
      getSingleSelectField('abcd', [
        { label: 'efgh', value: 'abcd' },
        { label: 'aeiou', value: 'bcde' },
      ])
    ).toBeNull();
  });

  it('get initial values for basic details form', () => {
    expect(getInitialValuesForBasicDetails(CUSTOMER_INPUT_1)).toEqual({
      customerType: 'Business',
      email: undefined,
      externalId: undefined,
      businessName: 'Kashmi',
      parentCompany: null,
      vip: false,
      firstName: 'Kashmi',
      alternateFirstName: null,
      alternateLastName: null,
      alternateName: null,
      lastName: null,
      prevIndexes: {
        mobile: 0,
        phone: -1,
        work: -1,
      },
      mobile: {
        phone: '(000) 000 - 00',
        checked: true,
        phoneCode: '+1',
        capencyVerified: undefined,
        dndStatus: false,
      },
      phone: {
        phone: null,
        checked: false,
        phoneCode: undefined,
      },
      work: {
        phone: null,
        checked: false,
        phoneCode: undefined,
      },
      preferredContactType: 0,
      alternateEmail: null,
      fax: null,
      preferredCommunicationMode: 'CALL',
      communicationPreferences: {
        email: {
          serviceUpdates: true,
          marketing: true,
        },
        call: {
          serviceUpdates: true,
          marketing: true,
        },
        text: {
          serviceUpdates: true,
          marketing: true,
        },
        postalMail: {
          serviceUpdates: true,
          marketing: true,
        },
        source: null,
      },
      socialSecurityNumber: '',
      subType: undefined,
      salutation: null,
      clientCategory: undefined,
      suffix: null,
      middleName: null,
      phones: [
        {
          phoneType: 0,
          number: '00000000',
          countryCode: null,
          extension: '+1',
          isPrimary: true,
          optOutOfMarketingCalls: false,
        },
      ],
      loyaltyInfoByOEMs: [
        {
          isLastRow: true,
        },
      ],
      customerRewardsInfos: [
        {
          isLastRow: true,
        },
      ],
      preferredLanguage: null,
      showNameOnDigitalDisplay: null,
      businessWebsite: undefined,
      businessClassification: undefined,
      industry: undefined,
      businessType: null,
      subPayType: 'C1',
    });
    expect(
      getInitialValuesForBasicDetails(CUSTOMER_INPUT_1, undefined, undefined, true, [
        { label: 'USA - English', value: 'en_US' },
      ])
    ).toEqual({
      customerType: 'Business',
      email: undefined,
      externalId: undefined,
      businessName: 'Kashmi',
      parentCompany: null,
      vip: false,
      firstName: 'Kashmi',
      alternateFirstName: null,
      alternateLastName: null,
      alternateName: null,
      lastName: null,
      prevIndexes: {
        mobile: 0,
        phone: -1,
        work: -1,
      },
      mobile: {
        phone: '(000) 000 - 00',
        checked: true,
        phoneCode: '+1',
        capencyVerified: undefined,
        dndStatus: false,
      },
      phone: {
        phone: null,
        checked: false,
        phoneCode: undefined,
      },
      work: {
        phone: null,
        checked: false,
        phoneCode: undefined,
      },
      preferredContactType: 0,
      alternateEmail: null,
      fax: null,
      preferredCommunicationMode: 'CALL',
      communicationPreferences: {
        email: {
          serviceUpdates: true,
          marketing: true,
        },
        call: {
          serviceUpdates: true,
          marketing: true,
        },
        text: {
          serviceUpdates: true,
          marketing: true,
        },
        postalMail: {
          serviceUpdates: true,
          marketing: true,
        },
        source: null,
      },
      socialSecurityNumber: '',
      subType: undefined,
      salutation: null,
      suffix: null,
      clientCategory: undefined,
      middleName: null,
      phones: [
        {
          phoneType: 0,
          number: '00000000',
          countryCode: null,
          extension: '+1',
          isPrimary: true,
          optOutOfMarketingCalls: false,
        },
      ],
      loyaltyInfoByOEMs: [
        {
          isLastRow: true,
        },
      ],
      customerRewardsInfos: [
        {
          isLastRow: true,
        },
      ],
      preferredLanguage: 'en_US',
      showNameOnDigitalDisplay: null,
      businessWebsite: undefined,
      businessClassification: undefined,
      industry: undefined,
      businessType: null,
      subPayType: 'C1',
    });
    expect(getInitialValuesForBasicDetails(CUSTOMER_INPUT_2)).toEqual({
      customerType: 'Individual',
      email: undefined,
      externalId: undefined,
      businessName: null,
      parentCompany: null,
      vip: false,
      firstName: 'Dennis',
      alternateFirstName: null,
      alternateLastName: null,
      alternateName: null,
      lastName: 'Ng',
      prevIndexes: {
        mobile: 0,
        phone: 1,
        work: -1,
      },
      mobile: {
        phone: '(288) 087 - 75',
        checked: true,
        phoneCode: undefined,
        dndStatus: false,
      },
      phone: {
        phone: '(317) 317 - 07',
        checked: false,
        phoneCode: undefined,
        dndStatus: false,
      },
      work: {
        phone: null,
        checked: false,
      },
      preferredContactType: 0,
      alternateEmail: null,
      clientCategory: '2586236c-0b58-405d-b413-174338ea3c2b',
      fax: null,
      preferredCommunicationMode: 'CALL',
      communicationPreferences: {
        email: {
          serviceUpdates: true,
          marketing: true,
        },
        call: {
          serviceUpdates: true,
          marketing: true,
        },
        text: {
          serviceUpdates: true,
          marketing: true,
        },
        postalMail: {
          serviceUpdates: true,
          marketing: true,
        },
        source: null,
      },
      socialSecurityNumber: '',
      subType: undefined,
      salutation: null,
      suffix: null,
      middleName: null,
      phones: [
        {
          phoneType: 0,
          number: '28808775',
          countryCode: '1',
          extension: '',
          isPrimary: true,
          optOutOfMarketingCalls: null,
        },
        {
          phoneType: 1,
          number: '********',
          countryCode: '1',
          extension: '',
          isPrimary: false,
          optOutOfMarketingCalls: null,
        },
      ],
      loyaltyInfoByOEMs: [
        {
          isLastRow: true,
        },
      ],
      customerRewardsInfos: [
        {
          isLastRow: true,
        },
      ],
      preferredLanguage: null,
      showNameOnDigitalDisplay: null,
      industry: 'ACCOUNTANTS',
      businessClassification: ['BROKER'],
      businessWebsite: 'www.abcd.com',
      businessType: null,
    });
    expect(
      getInitialValuesForBasicDetails(
        CUSTOMER_INPUT_3,
        undefined,
        undefined,
        true,
        [{ label: 'USA - English', value: 'en_US' }, undefined, true],
        undefined,
        true,
        true
      )
    ).toEqual({
      customerType: 'Business',
      email: undefined,
      shortName: 'P.S.A',
      externalId: undefined,
      businessName: 'SOS Associates',
      clientReferenceCodeEnabled: true,
      parentCompany: null,
      vip: false,
      alternateFirstName: null,
      alternateLastName: null,
      alternateName: null,
      firstName: undefined,
      lastName: undefined,
      prevIndexes: {
        mobile: 0,
        phone: 1,
        work: -1,
      },
      mobile: {
        capencyVerified: undefined,
        checked: true,
        phone: '(288) 087 - 75',
        phoneCode: undefined,
        dndStatus: true,
      },
      phone: {
        phone: '(317) 317 - 07',
        phoneCode: undefined,
        capencyVerified: undefined,
        checked: false,
        dndStatus: true,
      },
      work: {
        phone: null,
        checked: false,
        phoneCode: undefined,
      },
      preferredContactType: 0,
      alternateEmail: null,
      fax: null,
      preferredCommunicationMode: 'POSTAL',
      communicationPreferences: {
        email: {
          serviceUpdates: true,
          marketing: true,
        },
        call: {
          serviceUpdates: true,
          marketing: true,
        },
        text: {
          serviceUpdates: true,
          marketing: true,
        },
        postalMail: {
          serviceUpdates: true,
          marketing: true,
        },
        source: null,
      },
      socialSecurityNumber: '',
      stopCommunication: false,
      subType: undefined,
      salutation: null,
      suffix: null,
      middleName: undefined,
      phones: [
        {
          phoneType: 0,
          countryCode: '1',
          extension: '',
          isPrimary: true,
          number: '28808775',
          optOutOfMarketingCalls: null,
          dndStatus: true,
        },
        {
          countryCode: '1',
          extension: '',
          isPrimary: false,
          number: '********',
          optOutOfMarketingCalls: null,
          phoneType: 1,
          dndStatus: true,
        },
      ],
      loyaltyInfoByOEMs: [
        {
          isLastRow: true,
        },
      ],
      customerRewardsInfos: [
        {
          isLastRow: true,
        },
      ],
      preferredLanguage: 'en_US',
      showNameOnDigitalDisplay: null,
      typology: 'RLT',
      additionalEmails: [],
      additionalPhones: [],
      businessType: 'abc',
      clientCategory: undefined,
      countryOfRegistration: 'FR',
      insuranceActivity: '3105a319-4cd2-4a60-97e2-fa2b46e9952d',
      insuranceCode: '3105a319-4cd2-4a60-97e2-fa2b45e9952d',
      insurancePlatform: '9f3a7994-01af-4db1-9e23-070c19eeb4f8',
      multiInsurance: true,
      nafActivity: 'acdb',
      nafActivityLabel: 'abcd',
      siret: ['**************'],
      businessWebsite: undefined,
      businessClassification: undefined,
      industry: undefined,
      reciprocalTrading: true,
      reciprocalAccount: ['123456'],
      businessNameId: ['**************'],
      invoicingSiret: '1234',
      jobTitle: '12341',
      dateOfBirth: null,
      legacyDMSNumber: '',
    });
  });

  it('Get uniq ids for lookup call', () => {
    expect(getUniqIds([], 'roId')).toEqual([]);
    expect(getUniqIds([{ roId: '1212' }, { roId: '1341' }, { roId: '1341' }, {}], 'roId')).toEqual(['1212', '1341']);
  });

  it('Get siren from siret', () => {
    expect(getSirenFromSiret(null)).toEqual('');
    expect(getSirenFromSiret('1234')).toEqual('1234');
    expect(getSirenFromSiret('*********0')).toEqual('*********');
  });

  it('Get remaining siret after siren', () => {
    expect(getRemainingSiretAfterSiren(null)).toEqual('');
    expect(getRemainingSiretAfterSiren('1234')).toEqual('');
    expect(getRemainingSiretAfterSiren('*********0123')).toEqual('0123');
  });

  it('Get value for array select value', () => {
    expect(getSelectedInputValue()).toBeNull();
    expect(getSelectedInputValue(['1235'])).toEqual('1235');
    expect(getSelectedInputValue([])).toBeNull();
  });

  it('Get formValue map for basic details', () => {
    expect(getFormValueMapForBasicDetails(FORMVALUES_INPUT_1, FORMVALUES_INPUT_2, true, true)).toEqual(
      FORMVALUES_OUTPUT_1
    );
    expect(getFormValueMapForBasicDetails(FORMVALUES_INPUT_3, FORMVALUES_INPUT_4, true, true)).toEqual(
      FORMVALUES_OUTPUT_2
    );
    expect(getFormValueMapForBasicDetails(FORMVALUES_INPUT_3, FORMVALUES_INPUT_4, false, false)).toEqual(
      FORMVALUES_OUTPUT_3
    );
    expect(getFormValueMapForBasicDetails(FORMVALUES_INPUT_3, FORMVALUES_INPUT_4, true, false)).toEqual(
      FORMVALUES_OUTPUT_2
    );
  });

  const OPTIONS_FOR_MULTI_SELECT = [
    { label: 'Accountants', value: 'ACCOUNTANTS' },
    { label: 'Industrialists', value: 'INDUSTRIALISTS' },
    { label: 'Engineer', value: 'ENGINEER' },
  ];

  const MULTI_SELECT_INPUT1 = ['ENGINEER', 'DOCTOR'];
  const MULTI_SELECT_INPUT2 = ['INDUSTRIALISTS', 'ACCOUNTANTS'];

  it('Get label for multi select options', () => {
    expect(getMultiSelectConcatenatedLabel()).toEqual('');
    expect(getMultiSelectConcatenatedLabel(MULTI_SELECT_INPUT1, OPTIONS_FOR_MULTI_SELECT)).toEqual('Engineer');
    expect(getMultiSelectConcatenatedLabel(MULTI_SELECT_INPUT2, OPTIONS_FOR_MULTI_SELECT)).toEqual(
      'Accountants, Industrialists'
    );
  });

  it('Get option labels array from set of values', () => {
    expect(getMultiSelectOptionLabels()).toEqual([]);
    expect(getMultiSelectOptionLabels(MULTI_SELECT_INPUT1, OPTIONS_FOR_MULTI_SELECT)).toEqual(['Engineer']);
    expect(getMultiSelectOptionLabels(MULTI_SELECT_INPUT2, OPTIONS_FOR_MULTI_SELECT)).toEqual([
      'Accountants',
      'Industrialists',
    ]);
  });

  it('Validate getBasicDetailsUpgradedFields', () => {
    // Test with null initialValues
    expect(getBasicDetailsUpgradedFields(null, true, true)).toEqual({
      legacyDMSNumber: '',
      isMotabilityCustomer: false,
    });

    // Test with reciprocalTrading.vendorId present
    expect(getBasicDetailsUpgradedFields({ reciprocalTrading: { vendorId: '1234' } }, true, true)).toEqual({
      reciprocalTrading: true,
      reciprocalAccount: ['1234'],
      legacyDMSNumber: '',
      isMotabilityCustomer: false,
    });

    // Test with isInchcapeEnabled=false but rrgProgram=true
    expect(getBasicDetailsUpgradedFields({ reciprocalTrading: { vendorId: '1234' } }, false, true)).toEqual({
      reciprocalTrading: true,
      reciprocalAccount: ['1234'],
      legacyDMSNumber: '',
      isMotabilityCustomer: false,
    });

    // Test with isInchcapeEnabled=true but rrgProgram=false
    expect(getBasicDetailsUpgradedFields({ reciprocalTrading: { vendorId: '1234' } }, true, false)).toEqual({
      reciprocalTrading: true,
      reciprocalAccount: ['1234'],
      legacyDMSNumber: '',
      isMotabilityCustomer: false,
    });

    // Test with both flags false - should return empty object
    expect(getBasicDetailsUpgradedFields({ reciprocalTrading: { vendorId: '1234' } }, false, false)).toEqual({});

    // Test with null reciprocalTrading
    expect(getBasicDetailsUpgradedFields({ reciprocalTrading: null }, true, true)).toEqual({
      legacyDMSNumber: '',
      isMotabilityCustomer: false,
    });

    // Test with isMotabilityCustomer=true
    expect(getBasicDetailsUpgradedFields({ isMotabilityCustomer: true }, true, true)).toEqual({
      legacyDMSNumber: '',
      isMotabilityCustomer: true,
    });

    // Test with both isMotabilityCustomer and reciprocalTrading
    expect(
      getBasicDetailsUpgradedFields(
        {
          isMotabilityCustomer: true,
          reciprocalTrading: { vendorId: '5678' },
        },
        true,
        true
      )
    ).toEqual({
      reciprocalTrading: true,
      reciprocalAccount: ['5678'],
      legacyDMSNumber: '',
      isMotabilityCustomer: true,
    });
  });

  it('get verified values for capency', () => {
    expect(getCapencyVerifiedValue()).toBeNull();
    expect(getCapencyVerifiedValue('VERIFIED')).toEqual(true);
    expect(getCapencyVerifiedValue('NOT_VERIFIED')).toEqual(false);
    expect(getCapencyVerifiedValue('FAILED')).toEqual(false);
  });

  it('get filtered columns for inchcape program', () => {
    const SALES_TAX_COLUMN = { key: 'salesTaxInfo', displayName: 'Sales Tax exempted' };
    expect(getFilteredColumnsForInchcapeReportGeneration()).toEqual([]);
    expect(
      getFilteredColumnsForInchcapeReportGeneration([
        { key: 'mobilePhone', displayName: 'Mobile Phone' },
        SALES_TAX_COLUMN,
      ])
    ).toEqual([SALES_TAX_COLUMN]);
    expect(getFilteredColumnsForInchcapeReportGeneration([SALES_TAX_COLUMN])).toEqual([SALES_TAX_COLUMN]);
  });
});

describe('Test cases for getting phone number options', () => {
  it('should validate getOptionsForPhoneNumber function', () => {
    expect(getSelectedThreeDigitCountryCode([{ extensionCode: '90', threeDigitCountryCode: 'BOL' }], '+90')).toEqual(
      'BOL'
    );
    expect(getSelectedThreeDigitCountryCode([{ extensionCode: '80', threeDigitCountryCode: 'BOL' }], '+90')).toEqual(
      'FRA'
    );
    expect(getSelectedThreeDigitCountryCode([{ extensionCode: '90' }], '+90')).toEqual('FRA');
  });
});

describe('Test cases to find if Customer is not from France', () => {
  it('Should validate isNonFrenchCustomer function', () => {
    expect(isNonFrenchCustomer('FR')).toEqual(false);
    expect(isNonFrenchCustomer('')).toEqual(true);
    expect(isNonFrenchCustomer('FJ')).toEqual(true);
    expect(isNonFrenchCustomer()).toEqual(true);
  });
});
