import React from 'react';
import PropTypes from 'prop-types';
import update from 'immutability-helper';
import { defaultMemoize } from 'reselect';

import _noop from 'lodash/noop';
import _map from 'lodash/map';
import _set from 'lodash/set';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import Button from '@tekion/tekion-components/src/atoms/Button';
import Content from '@tekion/tekion-components/src/atoms/Content';
import Table from '@tekion/tekion-components/src/molecules/table/BaseTable';
import actionTypes from '@tekion/tekion-components/src/organisms/FormBuilder/constants/actionTypes';

export const tableFieldActionTypes = {
  ADD_SUBROW: 'addSubrow',
  REMOVE_SUBROW: 'removeSubrow',
  REMOVE_ROW: 'removeRow',
};

const DefaultCell = {
  renderer: ({ value }) => <Content>{value}</Content>,
  renderOptions: EMPTY_OBJECT,
};

class TableField extends React.PureComponent {
  getColumnData = defaultMemoize((columns, error, value) => {
    const { renderRemoveAction } = this.props;
    const adaptedColumns = _map(columns, (column) => {
      const Cell = column.Cell || DefaultCell;
      return {
        ...column,
        Cell: this.renderCell(Cell),
      };
    });

    if (renderRemoveAction) {
      return [...adaptedColumns, this.renderRemoveAction(value)];
    }
    return adaptedColumns;
  });

  addNewRow = (nestingPath, value) => {
    const { value: inputValue, onAction, id } = this.props; //eslint-disable-line
    const absolutePath = `${nestingPath.join('.nestedRow.')}.nestedRow`;
    const path = _set({}, absolutePath, { $push: [value] });
    const newData = update(inputValue, path);
    onAction({
      type: actionTypes.ON_FIELD_CHANGE,
      payload: {
        id,
        value: newData,
      },
    });
  };

  removeRow = index => () => {
    const { id, value, onAction } = this.props;
    this.lastRemovedIndex = index;
    const newData = [...value.slice(0, index), ...value.slice(index + 1, value.length)];
    onAction({
      type: actionTypes.ON_FIELD_CHANGE,
      payload: {
        id,
        value: newData,
      },
    });
  };

  clearRow = index => () => {
    const {
      id, value, error, onAction,
    } = this.props;
    const newData = [...value];
    const newErrors = [...error];

    newData[index] = {};
    newErrors[index] = {};

    onAction({
      type: actionTypes.ON_FIELD_CHANGE,
      payload: {
        id,
        value: newData,
      },
    });

    onAction({
      type: actionTypes.VALIDATION_SUCCESS,
      payload: {
        errors: newErrors,
      },
    });
  };

  removeSubrow = (nestingPath) => {
    const { id, value, onAction } = this.props;
    const newData = [...value];
    newData[nestingPath[0]].nestedRow.splice(nestingPath[1], 1);

    onAction({
      type: 'ON_REMOVE_ROW',
      payload: {
        id,
        value: nestingPath[1],
      },
    });

    onAction({
      type: actionTypes.ON_FIELD_CHANGE,
      payload: {
        id,
        value: newData,
      },
    });
  };

  handleCellAction = nestingPath => ({ type, payload: { id: cellId, value, additional } = {} }) => {
    const {
      props: { onAction, value: inputValue, id },
    } = this;
    if(type == actionTypes.ON_FIELD_BLUR) return;
    if (type === tableFieldActionTypes.ADD_SUBROW) {
      return this.addNewRow(nestingPath, value);
    }
    if (type === tableFieldActionTypes.REMOVE_SUBROW) {
      return this.removeSubrow(nestingPath);
    }
    if (type === tableFieldActionTypes.REMOVE_ROW) {
      return this.removeRow(nestingPath[0]);
    }
    const pathToUpdate = nestingPath.join('.nestedRow.');
    const updatedValue = { [cellId]: value };
    const newData = update(inputValue, _set({}, pathToUpdate, { $merge: updatedValue }));
    onAction({
      type: actionTypes.ON_FIELD_CHANGE,
      payload: {
        id,
        value: newData,
        additional: {
          ...additional,
          pathToUpdate,
          updatedValue,
          nestingPath,
        },
      },
    });
    return _noop;
  };

  renderCell = ({ renderer, renderOptions }) => (props) => {
    const Component = renderer;
    const { value, error, getFieldRendererOverrides = _noop } = this.props;
    const valueToPass = _get(value, props.nestingPath.join('.nestedRow.'), EMPTY_OBJECT);
    const errorToPass = _get(error, props.nestingPath.join('.nestedRow.'), EMPTY_OBJECT);
    const additionalProps = getFieldRendererOverrides(valueToPass, _get(props, 'column.id', ''), this.props, props);
    return (
      <Component
        {...props}
        {...renderOptions}
        key={this.lastRemovedIndex <= props.index ? props.index + 1 : props.index}
        value={valueToPass[props.column.id]}
        error={errorToPass[props.column.id]}
        onAction={this.handleCellAction(props.nestingPath)}
        {...additionalProps}
      />
    );
  };

  renderRemoveAction = (values) => {
    const { showAlwaysTrash, disabled } = this.props;
    return {
      Header: '',
      Cell: (row) => {
        const { index } = row;
        const isLastIndex = values.length - 1 === row.index;
        return isLastIndex && !showAlwaysTrash ? (
          <Button disabled={disabled || _isEmpty(row.original)} onClick={this.clearRow(index)} view="tertiary">
            {__('Clear')}
          </Button>
        ) : (
          <Button
            disabled={disabled || (!showAlwaysTrash && isLastIndex)}
            view="icon"
            onClick={this.removeRow(index)}
            icon="icon-trash"
          />
        );
      },
      width: 65,
      sortable: false,
    };
  };

  render() {
    const {
      columns, value, rowHeight, defaultExpanded, expanded, tableClassName, error, getTdProps,
    } = this.props;
    const columnData = this.getColumnData(columns, error, value);
    return (
      <div className={`${tableClassName} full-width`}>
        <Table
          expanded={expanded}
          defaultExpanded={defaultExpanded}
          subRowsKey="nestedRow"
          columns={columnData}
          data={value}
          rowHeight={rowHeight}
          showPagination={false}
          minRows={1}
          pageSize={value.length + 1}
          collapseOnDataChange={false}
          getTdProps={getTdProps}
        />
      </div>
    );
  }
}
TableField.propTypes = {
  onAddRow: PropTypes.func,
  addRowActionTitle: PropTypes.string,
  headerComponent: PropTypes.bool,
  columns: PropTypes.array,
  value: PropTypes.array,
  showAlwaysTrash: PropTypes.bool,
  onAction: PropTypes.func.isRequired,
  renderRemoveAction: PropTypes.bool,
  disabled: PropTypes.bool,
};

TableField.defaultProps = {
  onAddRow: _noop,
  addRowActionTitle: 'Add New',
  headerComponent: false,
  columns: [],
  value: [],
  showAlwaysTrash: false,
  renderRemoveAction: false,
  disabled: false,
};

export default TableField;
