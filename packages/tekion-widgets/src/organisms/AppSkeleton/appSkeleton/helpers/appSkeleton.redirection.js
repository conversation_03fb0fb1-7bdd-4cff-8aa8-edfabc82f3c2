/* eslint-disable import/order */
// Lodash
import _isEmpty from 'lodash/isEmpty';
import _replace from 'lodash/replace';
import _startsWith from 'lodash/startsWith';

// Utils
import Process from '@tekion/tekion-base/utils/Process';
import { isWorkspaceParent } from '../../../../utils/workspace';

// Constants
import { ACCOUNTING } from '@tekion/tekion-base/constants/appServices';
import { APP_MODULE_CONFIG } from '../config';
import ACCOUNTING_CONFIGS from '@tekion/tekion-base/constants/appConfigs/accountingAppConfigs';
import CENTRAL_APPS_BY_NON_CENTRAL_APP from '../constants/appSkeleton.centralAppsByNonCentralApps';

// Helpers
import {
  getBestMatchedAppConfigForPath,
  findAppConfigForAppKey,
} from '../../../../helpers/appSkeletonRouteValidators/appSkeleton.config.helper';
import {
  getCentralisedAppConfigsList,
  isCentralAppEnabled,
  isNonCentralAppEnabled,
} from './appSkeleton.accountingAppConfig';

const isCentralAppConfig = appConfig => !!appConfig?.nonCentralApp;

const isNonCentralReplicaAppConfig = appConfig => !!CENTRAL_APPS_BY_NON_CENTRAL_APP[appConfig?.key];

const redirectToNonCentralApp = (appConfig, navigate) => {
  const nonCentralAppKey = appConfig?.nonCentralApp;
  const nonCentralAppConfig = findAppConfigForAppKey(ACCOUNTING_CONFIGS, nonCentralAppKey);
  const nonCentralAppRootPath = nonCentralAppConfig?.path;
  navigate(`/${ACCOUNTING}/${nonCentralAppRootPath}`, { replace: true });
};

const redirectToCentralApp = (appConfig, navigate, location) => {
  const centralAppKey = CENTRAL_APPS_BY_NON_CENTRAL_APP[appConfig?.key];
  const centralAppConfig = findAppConfigForAppKey(ACCOUNTING_CONFIGS, centralAppKey);
  const centralAppRootPath = centralAppConfig?.path;
  const searchQuery = location?.search;
  navigate(`/${ACCOUNTING}/${centralAppRootPath}${searchQuery}`, { replace: true });
};

const handleNonAccountingMicroService = (next, params) => {
  const { appConfig, path } = params;
  // for non accounting app, do nothing
  if (appConfig?.module !== ACCOUNTING && !_startsWith(path, '/accounting')) {
    return;
  }
  next(params);
};

const handleEmptyAppConfig = (next, params) => {
  const { appConfig } = params;
  if (_isEmpty(appConfig)) {
    return;
  }
  next(params);
};

const handleParentDealerRedirection = (next, params) => {
  const { appConfig, loginData, navigate, location, centralisedEnabledApps } = params;
  const centralisedAppConfigsList = getCentralisedAppConfigsList(centralisedEnabledApps, ACCOUNTING_CONFIGS);
  if (isWorkspaceParent(loginData)) {
    // if an encountered app is a central app and the central app is not enabled, redirect to the noncentral app
    if (isCentralAppConfig(appConfig) && !isCentralAppEnabled(appConfig, centralisedAppConfigsList)) {
      return redirectToNonCentralApp(appConfig, navigate);
    }
    // if an encountered app is a noncentral app and the noncentral app is not enabled, redirect to the central app.
    if (isNonCentralReplicaAppConfig(appConfig) && !isNonCentralAppEnabled(appConfig, centralisedAppConfigsList)) {
      return redirectToCentralApp(appConfig, navigate, location);
    }
  }
  return next(params);
};

const handleChildDealerOrNonCentralDealerRedirection = (next, params) => {
  const { appConfig, loginData, navigate } = params;
  // if an encountered app is a central app, redirect to noncentral app
  if (!isWorkspaceParent(loginData) && isCentralAppConfig(appConfig)) {
    return redirectToNonCentralApp(appConfig, navigate);
  }
  return next(params);
};

const handleJERedirection = (next, params) => {
  const { path, navigate } = params;

  if (_startsWith(path, '/accounting/centralised/journalEntry') || _startsWith(path, '/accounting/journalEntry')) {
    const nonCentralJEPath = _replace(path, 'centralised/journalEntry', 'journalEntry');
    return navigate(nonCentralJEPath, { replace: true });
  }

  return next(params);
};

const REDIRECTION_PROCESS = new Process()
  .addHandler(handleNonAccountingMicroService)
  .addHandler(handleJERedirection)
  .addHandler(handleEmptyAppConfig)
  .addHandler(handleParentDealerRedirection)
  .addHandler(handleChildDealerOrNonCentralDealerRedirection);

export const handleRedirection = ({ location, loginData, navigate, centralisedEnabledApps }) => {
  // find app config
  const path = location?.pathname;
  const appConfig = getBestMatchedAppConfigForPath(path, APP_MODULE_CONFIG);
  return REDIRECTION_PROCESS.run({ appConfig, loginData, navigate, location, centralisedEnabledApps, path });
};
