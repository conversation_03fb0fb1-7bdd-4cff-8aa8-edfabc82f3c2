/* eslint-disable import/order */
// Lodash
import _split from 'lodash/split';

// Constants
import ACCOUNTING_SOURCE_TYPES from '../sourceTypes/accounting';
import DEFAULT_MODES from '@tekion/tekion-business/src/constants/routeModes';
import { ACCOUNTING } from '@tekion/tekion-base/constants/appServices';
import { JOURNAL_ENTRIES } from '@tekion/tekion-base/constants/appConfigs/accountingAppConfigs';

// Factories
import getRoute from '@tekion/tekion-business/src/factories/route';

// Utils
import getIsInchcapeDealer from '@tekion/tekion-business/src/utils/getIsInchcapeDealer';

const ACCOUNTING_BASE_URL = '/accounting';

const getAccountingSchedulesRoute = scheduleId => {
  if (!scheduleId) return;
  // eslint-disable-next-line consistent-return
  return `${ACCOUNTING_BASE_URL}/schedules/${scheduleId}/controlBook`;
};

const getArInvoicesRoute = otherReceivableSetupId => {
  if (!otherReceivableSetupId) return `${ACCOUNTING_BASE_URL}/customerInvoice/accountsReceivable`;
  return `${ACCOUNTING_BASE_URL}/customerInvoice/otherReceivables/${otherReceivableSetupId}`;
};

const getArAgingRoute = otherReceivableSetupId => {
  if (!otherReceivableSetupId) return `${ACCOUNTING_BASE_URL}/aragingReport/list`;
  return `${ACCOUNTING_BASE_URL}/aragingReport/otherReceivables/${otherReceivableSetupId}`;
};
const getAccountingConsolidatedAndBatchPaymentsRoute = () => `${ACCOUNTING_BASE_URL}/invoice/list`;
const getAccountingCentralInvoicesRoute = () => `${ACCOUNTING_BASE_URL}/centralInvoicing/list`;

const getApAgingRoute = () => `${ACCOUNTING_BASE_URL}/agingReport/list`;

const getAccountingVendorInvoiceRoute = () => `${ACCOUNTING_BASE_URL}/invoice/list`;

const getAccountingJournalEntriesRoute = postingSourceId => {
  const [transactionId, dealerId, isEnterpriseV2Enabled] = _split(postingSourceId, ' ');

  return getRoute(ACCOUNTING, JOURNAL_ENTRIES.getKey(), {
    dealerId,
    transactionId,
    mode: DEFAULT_MODES.VIEW,
    isEnterpriseV2Enabled: isEnterpriseV2Enabled === 'true' && getIsInchcapeDealer(),
  });
};

const ACCOUNTING_NOTES_ROUTES = {
  [ACCOUNTING_SOURCE_TYPES.SCHEDULES]: getAccountingSchedulesRoute,
  [ACCOUNTING_SOURCE_TYPES.CONSOLIDATED_PAYMENTS]: getAccountingConsolidatedAndBatchPaymentsRoute,
  [ACCOUNTING_SOURCE_TYPES.BATCH_PAYMENTS]: getAccountingConsolidatedAndBatchPaymentsRoute,
  [ACCOUNTING_SOURCE_TYPES.CENTRAL_INVOICING]: getAccountingCentralInvoicesRoute,
  [ACCOUNTING_SOURCE_TYPES.AR_INVOICES]: getArInvoicesRoute,
  [ACCOUNTING_SOURCE_TYPES.AR_AGING]: getArAgingRoute,
  [ACCOUNTING_SOURCE_TYPES.AP_AGING]: getApAgingRoute,
  [ACCOUNTING_SOURCE_TYPES.VENDOR_INVOICES]: getAccountingVendorInvoiceRoute,
  [ACCOUNTING_SOURCE_TYPES.JOURNAL_ENTRIES]: getAccountingJournalEntriesRoute,
};

export default ACCOUNTING_NOTES_ROUTES;
