import _pick from 'lodash/pick';

import { STATUS } from '@tekion/tekion-base/constants/statuses';
import { COLOR_MAP } from '@tekion/tekion-base/constants/statusColors';
import CONTENT_TYPE from '@tekion/tekion-base/constants/ExtensionVsMIME';

import { STATE_VS_COLOR } from '@tekion/tekion-components/src/molecules/CellRenderers/statusRenderer/StatusRenderer';

export const ACTION_TYPES = {
  FETCH_APPROVAL_REQUEST: 'FETCH_APPROVAL_REQUEST',
  CLOSE_APPROVAL_REQUEST: 'CLOSE_APPROVAL_REQUEST',
  APPROVE_APPROVAL_REQUEST: 'APPROVE_APPROVAL_REQUEST',
  REJECT_APPRVOAL_REQUEST: 'REJECT_APPRVOAL_REQUEST',
  WITHDRAW_APPROVAL_REQUEST: 'WITHDRAW_APPROVAL_REQUEST',
  SHOW_COMPARE_RIGHTS_MODAL: 'SHOW_COMPARE_RIGHTS_MODAL',
  RESET_STATE: 'RESET_STATE',
  RESUBMIT_APPROVAL_REQUEST: 'RESUBMIT_APPROVAL_REQUEST',
  SEND_REMINDER: 'SEND_REMINDER',
  ROLEID_DATA: 'ROLEID_DATA',
  SHOW_DELEGATE_FORM: 'SHOW_DELEGATE_FORM',
  CLOSE_DELEGATE_FORM: 'CLOSE_DELEGATE_FORM',
  SHOW_SEND_BACK_FORM: 'SHOW_SEND_BACK_FORM',
  CLOSE_SEND_BACK_FORM: 'CLOSE_SEND_BACK_FORM',
  ADD_MEDIA_TO_NOTES: 'ADD_MEDIA_TO_NOTES',
  SHOW_MEDIA_PREVIEW: 'SHOW_MEDIA_PREVIEW',
  CLOSE_MEDIA_PREVIEW: 'CLOSE_MEDIA_PREVIEW',
  CLOSE_RESUBMIT_MODAL: 'CLOSE_RESUBMIT_MODAL',
  EDIT_RETURNED_REQUEST: 'EDIT_RETURNED_REQUEST',
  CLOSE_SELF_APPROVE_INFO_MODAL: 'CLOSE_SELF_APPROVE_INFO_MODAL',
};

export const APPROVER_TYPES = {
  USER: 'USER',
  ROLE: 'ROLE',
};

export const NOTE_FIELD_ID = 'note';

export const REQUEST_AGE = 'createdTime';

export const BUSSINESS_PROCESS_IDS = {
  TEST: 'TEST',
  PAYMENTS: 'Payments',
  PO_SUBMISSION: 'Parts_9876',
  ROLES_AND_RIGHTS: 'rolesAndRights',
  CUSTOMER_CREDIT_LIMIT: 'Customer_Credit_Limit',
  PARTS_INVENTORY_ON_HAND_ADJUSTMENT: 'PARTS_INVENTORY_ON_HAND_ADJUSTMENT',
  MANUAL_JOURNAL_ENTRY: 'Manual_Journal_Entry',
  VENDOR_INVOICE: 'Vendor_Invoices',
  SERVICE: 'Service',
  CASHIERING: {
    DEALS_REFUND: 'DEALS_REFUND',
    SERVICE_REFUND: 'SERVICE_REFUND',
    PARTS_REFUND: 'PARTS_REFUND',
  },
  VEHICLE_TRANSFER: 'VEHICLE_TRANSFER',
  CUSTOMER_APPROVALS: {
    DEFAULT: 'Customer Approvals',
    TRADEIN_APPROVAL: 'TRADEIN_APPROVAL',
    REBATES_APPROVAL: 'REBATES_APPROVAL',
  },
  DEAL_RECAP: 'DEAL_RECAP',
  SALES_ORDER_BUSINESS_APPROVAL_PROCESS: 'PART_SALES_ORDER_FLOW',
};

export const POSSIBLE_REQUEST_STATUS = [
  STATUS.PENDING,
  STATUS.APPROVED,
  STATUS.REJECTED,
  STATUS.WITHDRAWN,
  STATUS.DECLINED,
  STATUS.COMPLETED,
  STATUS.EXPIRED,
  STATUS.RETURNED,
  STATUS.VOID,
];

export const STATUS_COLOR_MAP = {
  ..._pick(STATE_VS_COLOR, POSSIBLE_REQUEST_STATUS),
  [STATUS.PENDING]: COLOR_MAP.ORANGE,
  [STATUS.WITHDRAWN]: COLOR_MAP.GREY,
  [STATUS.RETURNED]: COLOR_MAP.GREY,
};

export const RULE_MATCHING_V2 = true;

export const STATUS_VS_MESSAGE = {
  [STATUS.APPROVED]: __('Approved Successfully'),
  [STATUS.WITHDRAWN]: __('Request is withdrawn'),
  [STATUS.REJECTED]: __('Request is declined'),
  [STATUS.EXPIRED]: __('Request is Expired'),
};

export const BUSINESS_PROCESS_IDS_WITH_FOOTER_ACTIONS = [];

export const BUSINESS_PROCESS_IDS_WITHOUT_VALUE_CHANGE = [
  BUSSINESS_PROCESS_IDS.CUSTOMER_CREDIT_LIMIT,
  BUSSINESS_PROCESS_IDS.PO_SUBMISSION,
];

export const SEND_BACK_OPTIONS = [
  {
    id: 'SEND_BACK',
    name: __('Send back'),
  },
];

export const DEFAULT_ATTACHMENT_TYPES = [CONTENT_TYPE.pdf, CONTENT_TYPE.jpg, CONTENT_TYPE.jpeg];

export const DEFAULT_FOOTER_OBJECT = {
  showPrimaryButton: false,
  showSecondaryButton: false,
};

export const SITE = 'SITE';
