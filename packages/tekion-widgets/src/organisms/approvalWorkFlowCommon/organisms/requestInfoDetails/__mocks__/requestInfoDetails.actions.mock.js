export const MOCK_DEAL_OBJECT = {
  deal: {
    lastModifiedBy: '595c5993-7300-48a1-828e-64c5582d6746',
    createdBy: '595c5993-7300-48a1-828e-64c5582d6746',
    deleted: false,
    createdTime: 1740569335962,
    modifiedTime: 1742462085116,
    id: '67befaf81d3df3293b67e8b5',
    dealNumber: '111895',
    uuid: '08cb33fc-39d3-49ac-aead-b569e9dc712f',
    userId: '595c5993-7300-48a1-828e-64c5582d6746',
    dealerId: '2065',
    tenantId: 'honda1',
    type: 'PERSONAL',
    collectFees: true,
    collectTaxes: true,
    collectTaxRetail: true,
    collectTaxLease: true,
    pricesVatInclusive: true,
    overrideFees: true,
    vehicles: [
      {
        createdTime: null,
        modifiedTime: null,
        deleted: false,
        vin: '19UDE4H29RA003259',
        orderNumber: null,
        id: '66dff9b8d118cb68b252463c',
        marketScanVehicleId: null,
        dealVehicleId: 'e8487dbb-8677-4e9a-b5df-5052a949102c',
        styleDetailList: null,
        dealerId: '49',
        primaryVehicle: true,
        rfIdTag: null,
        stockID: 'AI1341',
        year: 2024,
        make: 'Acura',
        driveTrain: null,
        makeId: 'acura',
        displayMake: 'Acura',
        model: 'Integra',
        range: null,
        segment: null,
        displayModel: 'Integra',
        modelCode: 'DE4H2RJW',
        modelType: 'Pikachu',
        mfrModelCode: 'DE4H2RJW',
        titleInfo: null,
        modelDescription: null,
        exteriorColor: 'Maroon',
        interiorColor: 'Mint green',
        interiorColorCode: null,
        exteriorColorCode: null,
        manufacturingColorCode: null,
        languages: null,
        tradeInVehicleId: null,
        previousStockID: null,
        customFields: {},
        status: 'STOCKED_IN',
        tempTag: null,
        sourceInfo: {
          source: 'OEM',
          sourceName: 'SALES_AUTOMATION',
        },
        vehicleType: 'NEW',
        vehicleSubType: '',
        driveType: 'Bilbo Baggins',
        unladenWeight: null,
        unladenWeightUnit: '',
        grossWeight: null,
        grossWeightUnit: '',
        grossVehicleWeightRating: null,
        mileage: 4047,
        mileageType: 'MILES',
        mileageStatus: null,
        pricingDetails: {
          sellingPrice: 14387,
          finalCost: 600,
          invoicePrice: 15809,
          retailPrice: 58871,
          deskingSellingPrice: null,
          retailPriceVatAmount: null,
          profit: 0,
          totalAdjustments: 600,
          commPrice: null,
          msrp: 14387,
          costOfSale: null,
          vatRate: null,
          vatAmount: null,
          holdBackAmount: 0,
          viHoldBackAmount: null,
          glBalance: 0,
          reconditioningCost: null,
          loanSellingPrice: 14387,
          leaseSellingPrice: 14387,
          flooringAmt: null,
          baseRetail: 31800,
          adjustedRetailPrice: null,
          wholesalePrice: null,
          internetPrice: 90427,
          commissionPrice: null,
          employeePrice: null,
          supplierPrice: null,
          freightInPrice: null,
          takePrice: null,
          airTaxPrice: null,
          maxResidualizationMSRP: null,
          configuredOptionsMsrp: null,
          configuredOptionsInvoice: null,
          interiorInvoicePrice: null,
          interiorMSRP: null,
          exteriorInvoicePrice: null,
          exteriorMSRP: null,
          baseInvoice: null,
          totalCost: null,
          marketPrice: null,
          buyingPrice: null,
          realReconditioningCost: null,
          licensePlatePrice: null,
          preparationPrice: null,
          buyingPriceExcludingVat: null,
          originalLoanSellingPrice: null,
          originalLeaseSellingPrice: null,
          gasGuzzlerTax: null,
          packageDiscounts: null,
          taxCode: null,
          taxCodeId: null,
          productTaxClassification: null,
          vehicleVatableAmount: null,
          exteriorColorPrice: null,
          interiorColorPrice: null,
          retailVatAmount: null,
          baseRetailVatAmount: null,
          pricingTaxes: null,
          vatInclusive: true,
          colorUpCharge: null,
          discount: null,
          discountVatAmount: null,
        },
        costAdjustments: [
          {
            lastModifiedBy: '492',
            createdBy: '492',
            deleted: false,
            createdTime: *************,
            modifiedTime: *************,
            id: 'e1488871-1df6-4df2-8d50-6ea04b971edc',
            description: 'Bank Fee',
            grossType: 'FRONT',
            amount: 500,
            userId: null,
            manuallyAdded: false,
            manuallyUpdated: false,
            reconditioning: false,
            costOfSaleAccountId: null,
            departmentId: null,
            posted: false,
          },
          {
            lastModifiedBy: '492',
            createdBy: '492',
            deleted: false,
            createdTime: *************,
            modifiedTime: *************,
            id: 'c373b524-617c-47b3-a4c5-3b576475155e',
            description: 'Definition Fee',
            grossType: 'BACK',
            amount: 100,
            userId: null,
            manuallyAdded: false,
            manuallyUpdated: false,
            reconditioning: false,
            costOfSaleAccountId: null,
            departmentId: null,
            posted: false,
          },
        ],
        licensePlateNumber: null,
        roDetails: [],
        licensePlateExpirationDate: 0,
        trimDetails: {
          oem: 'honda',
          brand: 'honda',
          actualBrand: 'honda',
          mapped: true,
          driveType: 'Bilbo Baggins',
          engineCylinders: 0,
          bodyType: '',
          axleCount: 0,
          bodyClass: 'Car',
          maximumSpeedMeasure: {
            unitCode: 'MPH',
          },
        },
        five64FuelType: null,
        five64BodyClass: null,
        vehicleAdditionalDetails: null,
        temp: false,
        certified: false,
        dealerCertified: false,
        certificationTier: null,
        bodyType: '',
        glAccountNumber: null,
        glAccountId: '49_110',
        glAccountNumberAndName: '110 - SALES COMPENSATION',
        entryTime: *************,
        stockedInTime: *************,
        leadId: null,
        vehicleSoldTime: null,
        viOptions: [],
        options: [],
        mileageForResidualCalculation: 0,
        inServiceDate: null,
        serviceVehicleId: null,
        locationCode: null,
        vinLookupResolved: false,
        lastEightVin: null,
        lastSixVin: null,
        lastFourVin: null,
        qualifiedForNewVehiclePrograms: false,
        auctionCar: false,
        demoVehicleIndicator: false,
        fleetVehicleIndicator: false,
        discounts: null,
        offers: null,
        exteriorColorDetail: {},
        selectedDiscounts: null,
        dealConfirmationTime: null,
        amtFinancedDuringPreviousBuy: null,
        monthlyPaymentDuringPreviousBuy: null,
        sellingPriceDuringPreviousBuy: null,
        stockedInAtSiteId: null,
        soldAtSiteId: null,
        vehicleSubStatus: null,
        parts: [],
        accessories: [],
        inFactoryWarranty: null,
        styleId: '439773',
        chromeSerializedValue: null,
        newTyreCount: 5,
        previousStatus: null,
        vehicleDisplayImage: null,
        bestStyleName: null,
        agStatusCode: null,
        agStatus: null,
        productionTime: null,
        vgStatusCode: null,
        demoCode: null,
        additionalOEMInfo: null,
        locationDetails: null,
        demoDescription: null,
        seriesCode: null,
        seriesName: null,
        estimatedDeliveryDate: null,
        actualDeliveryDate: null,
        stopDeliveryIndicator: null,
        campaignCodes: null,
        preownedRegistration: null,
        valuationsData: null,
        features: null,
        invoiceDate: null,
        purchaseNumber: null,
        orderStatus: null,
        mada: null,
        madc: null,
        purchaseOrderDate: null,
        exteriorColorLocalization: null,
        interiorColorLocalization: null,
        modelLocalization: null,
        bodyTypeLocalization: null,
        provisionValue: null,
        fiscalHorsePower: null,
        firstRegistrationDate: null,
        previousUsage: null,
        isFirstHandOwner: null,
        optionsRetailPrice: null,
        transformationPrice: null,
        tradeInCommitment: null,
        termMileage: null,
        baseValue: null,
        dateOfFirstAvailability: null,
        emissionValue: null,
        vehiclePriceIncludingOptions: null,
        genre: null,
        reformeB: false,
        isVAT: null,
        vehicleTechnicalIdentifier: null,
        customerExpectedDate: null,
        deliveryDealer: null,
        batteryIndentificationNumber: null,
        priceListNumber: null,
        configUrl: null,
        warrantyDescription: null,
        vehicleCategory: null,
        buildVehicle: null,
        typeVariantVersion: null,
        stockOriginSource: null,
        wltpCo2: null,
        professionalUsage: false,
        isVATQualifying: null,
        thirdPartyProviders: null,
        originCountryCode: null,
        purchaseSource: null,
        certificationStatus: null,
        tempVIN: null,
        foreignRegistration: false,
        policeNumberInfo: null,
        weightType: null,
        useType: 'Personal',
        totalCostAdjustmentsAmount: null,
        totalROCostsAmount: null,
        vehicleKind: null,
        wheelPlan: null,
        isImported: null,
        nedcCo2: null,
        numberOfPreviousOwners: null,
        programId: null,
        hasSingleStyleId: false,
      },
    ],
    hasDuplicate: true,
    parentId: '111722',
    readOnly: false,
    customers: [
      {
        type: 'BUYER',
        customerId: '68a2d739-3ac0-49c5-82bd-3d872ca3f093',
        crmCustomerId: '573aad5d-1663-442f-86d0-1e30a37ccdea',
        buyerTypeNum: 0,
        sameAsDeal: false,
        firstName: 'Joseph',
        incorporationTime: 0,
        lastName: 'Bak',
        mobileNo: '**********',
        workPhone: '**********',
        homePhone: '**********',
        email: '<EMAIL>',
        address: [
          {
            cmsAddressId: 'aff56e3c-c73d-4763-8276-f545d7973183',
            address1: '81 CHESTNUT AV',
            residentType: 'OWNS_OUTRIGHT',
            addressType: 'CURRENT',
            currentAddress: true,
            city: 'Northfield',
            state: 'CA',
            zipCode: '44067',
            country: 'US',
            countyName: 'US',
            inCity: false,
            periodOfResidence: 48,
            periodOfResidenceDurationType: 'MONTH',
            moreThanTwoYears: false,
            capencyVerification: false,
          },
          {
            cmsAddressId: '05e8415e-53cb-4ad4-b755-13bbd01bdc0e',
            address1: '434',
            addressType: 'BILLING',
            currentAddress: false,
            state: 'CA',
            zipCode: '90703',
            country: 'US',
            inCity: false,
            periodOfResidenceDurationType: 'YEAR',
            moreThanTwoYears: false,
          },
          {
            cmsAddressId: 'ed0923d7-f0dd-4451-96e0-00b9de9b79c3',
            address1: '434',
            addressType: 'GARAGE',
            currentAddress: false,
            state: 'CA',
            zipCode: '90703',
            inCity: false,
            periodOfResidenceDurationType: 'YEAR',
            moreThanTwoYears: false,
          },
        ],
        secondTaxRate: 0,
        creditScore: 0,
        externalCustomerId: '15521',
        temp: false,
        birthDate: 689904000000,
        dob: {
          year: 1991,
          month: 11,
          dayOfTheMonth: 12,
        },
        licenseDetails: {},
        salutation: 'MR',
        employmentDetails: [
          {
            status: 'FULL_TIME',
            currentEmployment: true,
            occupation: null,
            employeeId: null,
            employerName: 'Test',
            address: {
              currentAddress: false,
              inCity: false,
              moreThanTwoYears: false,
            },
            phoneNum: '**********',
            emailId: null,
            employmentType: null,
            grossIncome: 5000,
            grossIncomeInterval: 'MONTHLY',
            incomeInterval: 'MONTHLY',
            periodOfEmployment: 48,
            cudlEmployee: null,
            periodOfEmploymentDurationType: 'MONTH',
            cmsEmployeeId: '7e6db81a-e85b-4272-a7ee-f163ef0f4613',
            employerType: null,
            moreThanTwoYears: false,
          },
        ],
        driverDetails: {
          buyerTypeNum: 0,
          sameAsDeal: false,
          firstName: '',
          middleName: '',
          lastName: '',
          mobileNo: '',
          email: '',
          address: [
            {
              address1: '',
              currentAddress: false,
              city: '',
              state: '',
              zipCode: '',
              country: '',
              countyName: '',
              inCity: false,
              moreThanTwoYears: false,
            },
          ],
          secondTaxRate: 0,
          temp: false,
          guarantor: false,
          yearsInBusiness: 0,
          deleted: false,
          driverType: 'OTHER',
          dayOfCollection: 0,
          natureOfOperation: 'LB',
        },
        customerMedia: [],
        guarantor: false,
        displayId: '15521',
        communicationPreferences: {
          optOutFromEmail: true,
          optOutFromMobile: true,
          optOutFromSms: true,
        },
        preferredCommunicationMode: 'CALL',
        accountantPhone: '',
        principalOwners: [
          {
            buyerTypeNum: 0,
            sameAsDeal: false,
            mobileNo: '',
            workPhone: '',
            homePhone: '',
            secondTaxRate: 0,
            temp: false,
            guarantor: false,
            yearsInBusiness: 0,
            deleted: false,
            dayOfCollection: 0,
            natureOfOperation: 'LB',
            numberOfDependants: 0,
            totalMonthsAsOperator: 0,
            percentageOwnerShip: 0,
            bankruptcyDeclared: false,
            experiencedRepossession: false,
            privacyIndicator: false,
          },
        ],
        otherIncomeDetails: {
          otherIncomeDescription: null,
          income: null,
          interval: null,
          incomeSource: null,
          childAlimonyAmount: null,
        },
        referenceDetails: [],
        businessPhone: '',
        marketId: '34',
        yearsInBusiness: 0,
        customerTaxInfo: {
          salesTaxInfo: [],
        },
        contacts: [
          {
            countryCode: null,
            extension: '+1',
            isPrimary: null,
            number: '**********',
            type: 0,
            optOutOfMarketingCalls: null,
          },
          {
            countryCode: null,
            extension: '+1',
            isPrimary: null,
            number: '**********',
            type: 1,
            optOutOfMarketingCalls: null,
          },
          {
            countryCode: null,
            extension: '+1',
            isPrimary: null,
            number: '**********',
            type: 2,
            optOutOfMarketingCalls: null,
          },
        ],
        deleted: false,
        gender: 'Male',
        driverType: 'OTHER',
        garagingAddressType: 'OTHER',
        billingAddressType: 'OTHER',
        dayOfCollection: 0,
        natureOfOperation: 'LB',
      },
      {
        type: 'CO_BUYER',
        customerId: '68a2d739-3ac0-49c5-82bd-3d872ca3f093',
        crmCustomerId: '573aad5d-1663-442f-86d0-1e30a37ccdea',
        buyerTypeNum: 0,
        sameAsDeal: false,
        firstName: 'CoJoseph',
        incorporationTime: 0,
        lastName: 'CoBak',
        mobileNo: '**********',
        workPhone: '**********',
        homePhone: '**********',
        email: '<EMAIL>',
        secondTaxRate: 0,
        creditScore: 0,
        externalCustomerId: '15521',
        temp: false,
        birthDate: 689904000000,
        customerMedia: [],
        guarantor: false,
        displayId: '15521',
        deleted: false,
        gender: 'Male',
        driverType: 'OTHER',
        garagingAddressType: 'OTHER',
        billingAddressType: 'OTHER',
        dayOfCollection: 0,
        natureOfOperation: 'LB',
      },
    ],
    dealVehiclePaymentInfos: [
      {
        dealVehicleId: 'e8487dbb-8677-4e9a-b5df-5052a949102c',
        dealPayments: [
          {
            lastModifiedBy: '492',
            createdBy: '492',
            deleted: false,
            createdTime: 1742884778580,
            modifiedTime: 1742884778580,
            id: 'db8fd13f-ecb8-45ac-a493-54a74d719b6b',
            termPaymentDetails: [
              {
                lastModifiedBy: '492',
                createdBy: '492',
                deleted: false,
                createdTime: 1742884778580,
                modifiedTime: 1742884778580,
                id: '1e594613-6f37-41aa-b3b4-6f304c695267',
                preQualificationStatus: 'NOT_INITIATED',
                downPayment: 1000,
                downPaymentPercentage: null,
                downPaymentPct: 6.95,
                downPaymentPctType: 'SELLING_PRICE',
                calcBasedOnDownPmtPct: true,
                outOfPocketCash: 1000,
                tradeEquityCashBack: null,
                securityDepositOverridden: false,
                securityDeposits: null,
                paymentInfo: null,
                securityDepositV2: null,
                securityDepositId: null,
                securityDepositType: null,
                securityDeposit: 0,
                emiAmount: 576.44,
                onePayBaseEmiWithoutTaxPerTerm: null,
                onePayGstTaxPerTerm: null,
                onePayHstTaxPerTerm: null,
                onePayPstTaxPerTerm: null,
                onePayQstTaxPerTerm: null,
                onePayEmiPerTerm: null,
                onePayMonthlyPaymentOnlyCapped: null,
                monthlyPaymentOnlyCapped: null,
                onePayPayment: null,
                baseEmiAmount: 541.97,
                amountFinanced: 13274.31,
                loanToValueRatio: 92.27,
                selected: true,
                osfLender: false,
                lender: 'CVB',
                lenderId: '6205025fe3590f965ceb5167',
                lenderCode: null,
                lienFilingCode: null,
                apr: {
                  buyRate: 4,
                  effectiveRate: 0,
                  buyRateOverridden: true,
                  apr: 4,
                  moneyFactor: 4,
                  financeReserve: 0,
                  pctOfCapCost: null,
                  financeReserveOverridden: false,
                  aprCode: null,
                  markUp: 0,
                  markUpOverridden: false,
                  participation: 100,
                  reserveMethod: 0,
                  rateType: 0,
                  lenderRateType: 0,
                  manuallyUpdated: false,
                  alternateRateAdjustments: null,
                  rateOfInterest: null,
                  originalAPR: 4,
                  originalBuyRate: 4,
                  originalMarkup: 0,
                  originalRateType: 0,
                  useStandardProgram: false,
                  programType: 'CUSTOM',
                  blendedRateApplied: false,
                },
                residual: {
                  baseValue: null,
                  manuallyUpdated: false,
                  residualOverridden: false,
                  basePercentage: 0,
                  adjustedValue: null,
                  adjustedPercentage: 0,
                  totalValue: null,
                  balloonPaymentValue: null,
                  oneTimeBalloonPayment: null,
                  totalPercentage: 0,
                  residualName: null,
                  demoMilesPenaltyMiles: 0,
                  demoMilesRate: null,
                  demoMilesResidualAdjustment: null,
                  demoMilesResidualAdjustmentPct: null,
                  residualDisplayType: 'PERCENTAGE',
                  adjustedMsrp: null,
                  residualizableOptionsValue: null,
                  crvMRM: null,
                  lowOdometerAdjustment: null,
                  additionalResidualAmount: null,
                  residualEnhancement: null,
                  provisionValue: null,
                  residualBasis: null,
                },
                yearlyMiles: {
                  baseValue: 0,
                  additionalValue: 0,
                  demoMileageNA: false,
                  actualPenaltyPerMile: 0,
                  penaltyPerMile: 0,
                  initialOdometerMileageRate: null,
                  initialOdometerMileageRateOverridden: false,
                  actualPenaltyPerMileOverridden: false,
                  penaltyPerMileOverridden: false,
                  totalValue: 12000,
                  totalPenalty: null,
                  removeDemoMileageFromAnnualMileage: false,
                  totalMilesHidden: 12000,
                  yearlyMilesUpdated: null,
                  reasonForChange: null,
                },
                rebates: [
                  {
                    lastModifiedBy: '492',
                    createdBy: '492',
                    deleted: false,
                    createdTime: 1742884778554,
                    modifiedTime: 1742884778554,
                    id: 'e8c87fee-0de1-4260-b3b1-86dfb8d31d57',
                    parentId: null,
                    name: 'Acura Graduate Offer',
                    languages: null,
                    rebateId: 'e8c87fee-0de1-4260-b3b1-86dfb8d31d57',
                    valueType: 0,
                    cashPercent: '0',
                    programNumber: 'AP-W60',
                    manuallyUpdated: true,
                    programCode: 'COLG',
                    programName: null,
                    dealerCash: 0,
                    dealerApprovalStatus: null,
                    autoAppliedRebate: true,
                    rebateType: 'REBATE',
                    taxType: null,
                    rebateAmount: 500,
                    custom: false,
                    certificateNumber: null,
                    manuallyEdited: false,
                    firstMonthWaiver: false,
                    paymentTypeCode: 0,
                    applicablePaymentTypes: ['10', '11'],
                    terms: [
                      {
                        startTerm: null,
                        endTerm: null,
                      },
                    ],
                    gmCardDetails: null,
                    rate: null,
                    rateReduction: null,
                    descriptionText: null,
                    vatInfo: {
                      taxCode: null,
                      vatPercent: null,
                      vatAmount: null,
                      taxId: null,
                      vatInclusive: true,
                    },
                    rebateSelections: null,
                    rebateOperator: null,
                    rebateProvider: 'CMS',
                    isCcrTaxApplicable: null,
                    code: 'AP-W60',
                    rebateCategory: null,
                    collectTaxesForEvTaxCredit: true,
                    customerNumber: null,
                    vendorNumber: null,
                    validityStartDate: null,
                    validityEndDate: null,
                    financeCompany: null,
                    make: null,
                    status: null,
                    rebateIdKey: 'e8c87fee-0de1-4260-b3b1-86dfb8d31d57__',
                    personalisedRebate: false,
                  },
                  {
                    lastModifiedBy: '492',
                    createdBy: '492',
                    deleted: false,
                    createdTime: 1742884778554,
                    modifiedTime: 1742884778554,
                    id: 'b1536f72-8a67-4112-9b26-7f0c1f50bd94',
                    parentId: null,
                    name: 'Military Appreciation',
                    languages: null,
                    rebateId: 'b1536f72-8a67-4112-9b26-7f0c1f50bd94',
                    valueType: 0,
                    cashPercent: '0',
                    programNumber: 'AP-W59',
                    manuallyUpdated: true,
                    programCode: 'MAPR',
                    programName: null,
                    dealerCash: 0,
                    dealerApprovalStatus: null,
                    autoAppliedRebate: true,
                    rebateType: 'REBATE',
                    taxType: null,
                    rebateAmount: 750,
                    custom: false,
                    certificateNumber: null,
                    manuallyEdited: false,
                    firstMonthWaiver: false,
                    paymentTypeCode: 0,
                    applicablePaymentTypes: ['10', '11'],
                    terms: [
                      {
                        startTerm: null,
                        endTerm: null,
                      },
                    ],
                    gmCardDetails: null,
                    rate: null,
                    rateReduction: null,
                    descriptionText: null,
                    vatInfo: {
                      taxCode: null,
                      vatPercent: null,
                      vatAmount: null,
                      taxId: null,
                      vatInclusive: true,
                    },
                    rebateSelections: null,
                    rebateOperator: null,
                    rebateProvider: 'CMS',
                    isCcrTaxApplicable: null,
                    code: 'AP-W59',
                    rebateCategory: null,
                    collectTaxesForEvTaxCredit: true,
                    customerNumber: null,
                    vendorNumber: null,
                    validityStartDate: null,
                    validityEndDate: null,
                    financeCompany: null,
                    make: null,
                    status: null,
                    rebateIdKey: 'b1536f72-8a67-4112-9b26-7f0c1f50bd94__',
                    personalisedRebate: false,
                  },
                ],
                firstPaymentWaiverManuallyOveridden: false,
                securityDepositRateAdjustment: 0,
                totalAcquisitionCost: null,
                totalRebateAmt: null,
                bankFee: null,
                inceptionFee: null,
                programId: null,
                programNumber: null,
                capCostReductionfactors: {
                  downpaymentFromCash: null,
                  downpaymentFromEquity: null,
                  downpaymentFromRebate: null,
                  downpaymentFromTax: null,
                  tradeInTaxableCCRBase: null,
                  tradeInCcrTaxAmount: null,
                  downPaymentFromCashTotalAmount: null,
                  downPaymentFromCashToDriveOff: null,
                },
                workingCashTableData: null,
                driveOffReductionFactors: {
                  driveOffFromRebates: null,
                  driveOffFromTradeIn: null,
                  negativeEquityCoveredFromCash: null,
                  negativeEquityCoveredFromRebate: null,
                },
                totalTaxAmt: 827.31,
                generic: false,
                cashDeficiency: null,
                imbalanceAmount: 0,
                deferredPayment1: {
                  payment: 1000,
                  paymentDate: 0,
                },
                deferredPayment2: null,
                deferredPayment3: null,
                deferredPayment4: null,
                totalPayable: 1000,
                daysToFirstPayment: 0,
                monthlyPaymentBeforeTax: 541.97,
                taxes: {
                  capCostReductionTaxRate: 0,
                  stateTaxRate: 0,
                  cityTaxRate: 0,
                  taxRate: 6.25,
                  manuallyUpdated: false,
                  salesTaxCapturedInDriveOff: false,
                  inventoryTaxCapturedInDriveOff: false,
                  salesTax: 827.31,
                  propertyTax: null,
                  docStampTax: null,
                  downpaymentTax: null,
                  downpaymentTaxCappedAmt: null,
                  downpaymentTaxCapped: false,
                  inventoryTax: null,
                  downpaymentTaxInFees: null,
                  fairFaxCountyTaxCappedAmt: null,
                  fairFaxCountyTaxUpfrontAmt: null,
                  fairFaxCountyTax: null,
                  otherTaxInFees: 0,
                  otherTaxInCap: 0,
                  financeCharge: null,
                  useTax: 0,
                  customFeeTaxInCap: 0,
                  customFeeTaxInFees: 0,
                  tradeTaxExempt: 0,
                  garagedStateSalesTax: null,
                  serviceContractTax: 0,
                  countyTaxRate: 0,
                  countyTax: 0,
                  cityTax: 0,
                  outOfStateLocalBusinessTax: null,
                  outOfStateLocalBusinessAftermarketTax: null,
                  outOfStateStateBusinessTax: null,
                  localBusinessTax: null,
                  localBusinessTaxCapped: null,
                  localBusinessTaxUpfront: null,
                  localBusinessAftermarketTax: null,
                  localBusinessAftermarketTaxCapped: null,
                  localBusinessAftermarketTaxUpfront: null,
                  stateBusinessTax: null,
                  acquisitionFeeTax: null,
                  stateBusinessTaxCapped: null,
                  stateBusinessTaxUpfront: null,
                  taxBreakdownUpfrontTotal: 0,
                  taxBreakdownCappedTotal: 0,
                  localAftermarketTax: null,
                  localAftermarketTaxCapped: null,
                  localAftermarketTaxUpfront: null,
                  localTax: null,
                  localTaxCapped: null,
                  localTaxUpfront: null,
                  singleArticleTax: null,
                  singleArticleTaxCapped: null,
                  singleArticleTaxUpfront: null,
                  businessTax: null,
                  rtaTax: null,
                  outOfStateLocalBusinessTaxCapped: null,
                  outOfStateLocalBusinessAftermarketTaxCapped: null,
                  outOfStateStateBusinessTaxCapped: null,
                  outOfStateLocalBusinessTaxUpfront: null,
                  outOfStateLocalBusinessAftermarketTaxUpfront: null,
                  outOfStateStateBusinessTaxUpfront: null,
                  stateTax: 827.31,
                  stateTaxUpfront: null,
                  stateTaxCapped: null,
                  gstTax: null,
                  gstTaxMutOnly: null,
                  pstTaxMutOnly: null,
                  pstTax: null,
                  qstTax: null,
                  hstTax: null,
                  totalGstOrHstTax: null,
                  totalPstTax: null,
                  insuranceTax: null,
                  catTax: null,
                  catTaxCapped: null,
                  catTaxUpfront: null,
                  bandoTaxCapped: null,
                  bandoTaxUpfront: null,
                  bandoTax: null,
                  totalTax: 827.31,
                  monthlyUseTax: null,
                  leaseTax: null,
                  municipalTax: null,
                  spdsTax: null,
                  ccrTax: null,
                  cappedCostTax: null,
                  motorVehicleTax: null,
                  privilegeTax: null,
                  commonTax: null,
                  warrantyTax: null,
                  sellingPriceSalesTax: null,
                  luxuryTax: null,
                  lenderTaxExemptFeeTax: null,
                  localFnITax: null,
                  businessFnITax: null,
                  stateFnITax: null,
                  docFeeTax: null,
                  bpolTax: null,
                  stateExciseTax: null,
                  totalFnIProductTax: null,
                  otherFnIProductTax: null,
                  wholesaleTaxUpfront: null,
                  wholesaleTaxCapped: null,
                  taxBreakups: [],
                  taxDetails: [
                    {
                      taxType: 'STATE_TAX',
                      displayName: 'State Tax',
                      upfront: null,
                      amount: 827.31,
                      childTax: true,
                      parentType: 'SALES_TAX',
                      display: false,
                      percentage: 6.25,
                      taxableBase: 13236.96,
                      productId: null,
                    },
                    {
                      taxType: 'SALES_TAX',
                      displayName: 'Sales Tax',
                      upfront: null,
                      amount: 827.31,
                      childTax: false,
                      parentType: null,
                      display: true,
                      percentage: 6.25,
                      taxableBase: 13236.96,
                      productId: null,
                    },
                  ],
                  taxBreakDown: null,
                  fniTaxBreakup: null,
                  feeTaxBreakupDetails: null,
                  vitTaxRate: null,
                  fniTaxBreakupBasedOnDisclosure: null,
                  districtTax: 0,
                },
                vendorTaxes: null,
                dealFees: [
                  {
                    lastModifiedBy: '492',
                    createdBy: '492',
                    deleted: false,
                    createdTime: *************,
                    modifiedTime: *************,
                    id: 'ea35fe2a-5f17-4738-bb58-acad45db6b68',
                    overriden: false,
                    name: 'TireFee',
                    languages: {
                      documentDefault: 'en_US',
                      locale: null,
                      keyValueMapList: null,
                    },
                    manuallyUpdated: false,
                    amount: 50,
                    feeCode: 'TT4',
                    upfront: true,
                    taxable: false,
                    taxUpFront: false,
                    marketScanAmt: null,
                    manuallyAdded: false,
                    internalFeeCode: 'TIRE_FEE',
                    marketScan: false,
                    source: 'SALES_SETUP',
                    feeType: 'DMV',
                    standardAmount: null,
                    markup: null,
                    manuallyEdited: false,
                    baseAmount: null,
                    pricingType: null,
                    feeCalcType: null,
                    taxConfigs: [
                      {
                        taxRegimeType: 'SALES_TAX',
                        taxable: true,
                      },
                    ],
                    isCommissionable: null,
                    vatInfo: {
                      taxCode: null,
                      vatPercent: null,
                      vatAmount: null,
                      taxId: null,
                      vatInclusive: true,
                    },
                    outOfPocketFee: null,
                    productClassification: null,
                    accountingGroup: null,
                    vendorNumber: null,
                    costPrice: null,
                    waiveAcquisitionFee: null,
                    vendorId: null,
                    costVatTaxInfo: {
                      taxCode: null,
                      vatPercent: null,
                      vatAmount: null,
                      taxId: null,
                      vatInclusive: true,
                    },
                    costVatPrice: null,
                    impactOnEmi: null,
                    netProfit: 50,
                    netSale: 50,
                    transactionType: null,
                  },
                  {
                    lastModifiedBy: '492',
                    createdBy: '492',
                    deleted: false,
                    createdTime: *************,
                    modifiedTime: *************,
                    id: '88cd4a33-ec51-4312-b008-a75523ce74fc',
                    overriden: false,
                    name: 'DuplicateFees1LN',
                    languages: {
                      documentDefault: 'en_US',
                      locale: null,
                      keyValueMapList: null,
                    },
                    manuallyUpdated: false,
                    amount: 100,
                    feeCode: 'TitleFees57LN',
                    upfront: true,
                    taxable: true,
                    taxUpFront: false,
                    marketScanAmt: null,
                    manuallyAdded: false,
                    internalFeeCode: 'TEMPORARY_TAG_FEE',
                    marketScan: false,
                    source: 'SALES_SETUP',
                    feeType: 'DMV',
                    standardAmount: null,
                    markup: null,
                    manuallyEdited: false,
                    baseAmount: null,
                    pricingType: null,
                    feeCalcType: null,
                    taxConfigs: [
                      {
                        taxRegimeType: 'SALES_TAX',
                        taxable: true,
                      },
                    ],
                    isCommissionable: null,
                    vatInfo: {
                      taxCode: null,
                      vatPercent: null,
                      vatAmount: null,
                      taxId: null,
                      vatInclusive: true,
                    },
                    outOfPocketFee: null,
                    productClassification: null,
                    accountingGroup: null,
                    vendorNumber: null,
                    costPrice: null,
                    waiveAcquisitionFee: null,
                    vendorId: null,
                    costVatTaxInfo: {
                      taxCode: null,
                      vatPercent: null,
                      vatAmount: null,
                      taxId: null,
                      vatInclusive: true,
                    },
                    costVatPrice: null,
                    impactOnEmi: null,
                    netProfit: 100,
                    netSale: 100,
                    transactionType: null,
                  },
                  {
                    lastModifiedBy: '492',
                    createdBy: '492',
                    deleted: false,
                    createdTime: *************,
                    modifiedTime: *************,
                    id: 'a5f4e8d3-2e97-42e6-86d9-aa13d929c1c7',
                    overriden: false,
                    name: 'Title Certificate Fee',
                    languages: null,
                    manuallyUpdated: false,
                    amount: 75,
                    feeCode: 'Title Fee',
                    upfront: true,
                    taxable: false,
                    taxUpFront: false,
                    marketScanAmt: null,
                    manuallyAdded: false,
                    internalFeeCode: 'TITLE_FEE',
                    marketScan: false,
                    source: 'GALAXY',
                    feeType: 'DMV',
                    standardAmount: 75,
                    markup: null,
                    manuallyEdited: false,
                    baseAmount: null,
                    pricingType: null,
                    feeCalcType: null,
                    taxConfigs: null,
                    isCommissionable: null,
                    vatInfo: null,
                    outOfPocketFee: null,
                    productClassification: null,
                    accountingGroup: null,
                    vendorNumber: null,
                    costPrice: null,
                    waiveAcquisitionFee: null,
                    vendorId: null,
                    costVatTaxInfo: null,
                    costVatPrice: null,
                    impactOnEmi: null,
                    netProfit: 75,
                    netSale: 75,
                    transactionType: null,
                  },
                  {
                    lastModifiedBy: '492',
                    createdBy: '492',
                    deleted: false,
                    createdTime: *************,
                    modifiedTime: *************,
                    id: '0aeb8807-57aa-4931-95e1-fe0784ec0e44',
                    overriden: false,
                    name: 'Registration Fee',
                    languages: null,
                    manuallyUpdated: false,
                    amount: 60,
                    feeCode: 'Registration Fee',
                    upfront: true,
                    taxable: false,
                    taxUpFront: false,
                    marketScanAmt: null,
                    manuallyAdded: false,
                    internalFeeCode: 'REGISTRATION_CHARGES',
                    marketScan: false,
                    source: 'GALAXY',
                    feeType: 'DMV',
                    standardAmount: 60,
                    markup: null,
                    manuallyEdited: false,
                    baseAmount: null,
                    pricingType: null,
                    feeCalcType: null,
                    taxConfigs: null,
                    isCommissionable: null,
                    vatInfo: null,
                    outOfPocketFee: null,
                    productClassification: null,
                    accountingGroup: null,
                    vendorNumber: null,
                    costPrice: null,
                    waiveAcquisitionFee: null,
                    vendorId: null,
                    costVatTaxInfo: null,
                    costVatPrice: null,
                    impactOnEmi: null,
                    netProfit: 60,
                    netSale: 60,
                    transactionType: null,
                  },
                  {
                    lastModifiedBy: '492',
                    createdBy: '492',
                    deleted: false,
                    createdTime: *************,
                    modifiedTime: *************,
                    id: '20f63564-70d6-41f0-95c1-586e3dcd2a64',
                    overriden: false,
                    name: 'Plate Transfer Fee',
                    languages: null,
                    manuallyUpdated: false,
                    amount: 25,
                    feeCode: 'Plate Transfer Fee',
                    upfront: true,
                    taxable: false,
                    taxUpFront: false,
                    marketScanAmt: null,
                    manuallyAdded: false,
                    internalFeeCode: 'PLATE_TRANSFER_FEE',
                    marketScan: false,
                    source: 'GALAXY',
                    feeType: 'DMV',
                    standardAmount: 25,
                    markup: null,
                    manuallyEdited: false,
                    baseAmount: null,
                    pricingType: null,
                    feeCalcType: null,
                    taxConfigs: null,
                    isCommissionable: null,
                    vatInfo: null,
                    outOfPocketFee: null,
                    productClassification: null,
                    accountingGroup: null,
                    vendorNumber: null,
                    costPrice: null,
                    waiveAcquisitionFee: null,
                    vendorId: null,
                    costVatTaxInfo: null,
                    costVatPrice: null,
                    impactOnEmi: null,
                    netProfit: 25,
                    netSale: 25,
                    transactionType: null,
                  },
                ],
                manuallyDeletedFees: null,
                firstPaymentWaived: true,
                firstPaymentWaivedAmount: 0,
                firstPaymentWaivedMaxAmount: null,
                registrationnFee: null,
                stateFee: null,
                titleFee: null,
                wasteTireFee: null,
                serviceContractNotIncludedInSalesTax: false,
                bandOFee: null,
                lienFee: null,
                totalCapReduced: 0,
                paidReserveIsFlatFee: false,
                maxRateMarkup: null,
                msLenderCode: null,
                paidReserveIsCapCostParticipation: false,
                collectRegFee: false,
                downPaymentIndex: 0,
                singleScheduledPayment: null,
                taxWaivedPayments: null,
                programFlags: null,
                taxCode: null,
                taxZipCode: '02186',
                taxAddress: null,
                taxCity: null,
                taxCounty: 'NORFOLK',
                taxExemptAmount: null,
                totalRebateAmount: 1250,
                totalDealerCashAmount: 0,
                rolledOverCashDeficiency: null,
                totalMonthlyPayment: 13834.56,
                financeCharge: 560.25,
                dueFromCustomerMainInvoice: null,
                dueFromCustomer: null,
                fordFlexDecreasePct: null,
                fordFlexFirstPayment: null,
                fordFlexFirstTerm: null,
                fordFlexLastPayment: null,
                rebatesBreakup: null,
                proRataAdjustment: null,
                applyCanadaLuxuryTax: false,
                programExpirationDate: null,
                paymentBreakups: {
                  driveOff: [],
                  outOfPocketCash: [
                    {
                      total: 1000,
                      type: 'CUSTOMER_CASH',
                      breakupItemList: [],
                    },
                  ],
                  grossCapCost: [],
                  netCapCost: [],
                  luxuryTax: null,
                },
                discount: null,
                hasStandardData: false,
                downPaymentWithoutCCRTax: null,
                totalAmountPayable: null,
                optionalFinalPayment: null,
                optionToPurchaseFee: null,
                totalDeposit: null,
                quoteDetails: null,
                infoMessages: null,
                paymentTax: null,
                upfrontPayments: null,
                vcciFields: {
                  baseVehicleCashPrice: null,
                  totalDownPaymentAmount: null,
                  aggregateCashPrice: null,
                  cashValueOfLeasedGoodsAmount: null,
                  valueGivenByYouAmount: null,
                  valueReceivedByYouAmount: null,
                  costOfBorrowingAmount: null,
                  taxableBaseMonthlyPaymentAmount: null,
                  taxableTotalOfBaseMonthlyPaymentsAmount: null,
                  implicitFinanceChargesAmount: null,
                  totalCostOfTerm: null,
                  totalCostOfLease: null,
                  leaseCapitalizedAmount: null,
                  feeSubtotal: null,
                  upfrontCashDownPaymentAmount: null,
                  upfrontMfgRebateAmount: null,
                  canadaAcrPercentage: null,
                  upfrontNetTradeAmount: null,
                  rebateApplicableIfCashDeal: null,
                  financeSubtotalTypeOther: null,
                  adjustedBalanceForCreditRateCalculation: null,
                  totalAmountPayable: null,
                  totalVehicleCashPrice: null,
                  taxableSellingPrice: null,
                  totalAmountDueAtSigning: null,
                  aggregateEmiAmount: null,
                  cashDownPaymentAmount: null,
                  lenderNetCapCost: null,
                  totalSellingPrice: null,
                },
                financingProductCode: null,
                contractInTransitAmount: null,
                totalUnroundedBaseMonthlyPayment: null,
                unroundedBaseMonthlyPayment: null,
                multipleSecurityDepositProgramList: null,
                securityDepositWaiverDetails: null,
                deposit: null,
                dueAtSigning: 1000,
                remainingDueAtSigning: 1000,
                previousResponseAdjustmentData: {
                  demoMilesAdjustment: null,
                  additionalMileageAdjustment: null,
                  lowOdometerAdjustment: null,
                  baseResidualValue: null,
                  baseResidualPercentage: 0,
                  additionalResidualAmount: null,
                },
                priorTradeBalance: 0,
                ruleWarningMap: {},
                programOverridden: false,
                subventionCostValueAbsolute: null,
                subventionCostValuePercentage: null,
                subventionCostOverridden: false,
                dueFromCustomerWithoutPaidCustomerCash: null,
                categoryBasedTotalRebates: null,
                taxStrategy: null,
                taxOutOfState: true,
                rebatesVerified: false,
              },
              {
                lastModifiedBy: '492',
                createdBy: '492',
                deleted: false,
                createdTime: 1742884778580,
                modifiedTime: 1742884778580,
                id: 'ddf50496-2384-4c3b-856e-6e43baaf1a94',
                preQualificationStatus: 'NOT_INITIATED',
                downPayment: 2000,
                downPaymentPercentage: null,
                downPaymentPct: 13.9,
                downPaymentPctType: 'SELLING_PRICE',
                calcBasedOnDownPmtPct: true,
                outOfPocketCash: 2000,
                tradeEquityCashBack: null,
                securityDepositOverridden: false,
                securityDeposits: null,
                paymentInfo: null,
                securityDepositV2: null,
                securityDepositId: null,
                securityDepositType: null,
                securityDeposit: 0,
                emiAmount: 533.01,
                onePayBaseEmiWithoutTaxPerTerm: null,
                onePayGstTaxPerTerm: null,
                onePayHstTaxPerTerm: null,
                onePayPstTaxPerTerm: null,
                onePayQstTaxPerTerm: null,
                onePayEmiPerTerm: null,
                onePayMonthlyPaymentOnlyCapped: null,
                monthlyPaymentOnlyCapped: null,
                onePayPayment: null,
                baseEmiAmount: 498.54,
                amountFinanced: 12274.31,
                loanToValueRatio: 85.32,
                selected: false,
                osfLender: false,
                lender: 'CVB',
                lenderId: '6205025fe3590f965ceb5167',
                lenderCode: null,
                lienFilingCode: null,
                apr: {
                  buyRate: 4,
                  effectiveRate: 0,
                  buyRateOverridden: true,
                  apr: 4,
                  moneyFactor: 4,
                  financeReserve: 0,
                  pctOfCapCost: null,
                  financeReserveOverridden: false,
                  aprCode: null,
                  markUp: 0,
                  markUpOverridden: false,
                  participation: 100,
                  reserveMethod: 0,
                  rateType: 0,
                  lenderRateType: 0,
                  manuallyUpdated: false,
                  alternateRateAdjustments: null,
                  rateOfInterest: null,
                  originalAPR: 4,
                  originalBuyRate: 4,
                  originalMarkup: 0,
                  originalRateType: 0,
                  useStandardProgram: false,
                  programType: 'CUSTOM',
                  blendedRateApplied: false,
                },
                residual: {
                  baseValue: null,
                  manuallyUpdated: false,
                  residualOverridden: false,
                  basePercentage: 0,
                  adjustedValue: null,
                  adjustedPercentage: 0,
                  totalValue: null,
                  balloonPaymentValue: null,
                  oneTimeBalloonPayment: null,
                  totalPercentage: 0,
                  residualName: null,
                  demoMilesPenaltyMiles: 0,
                  demoMilesRate: null,
                  demoMilesResidualAdjustment: null,
                  demoMilesResidualAdjustmentPct: null,
                  residualDisplayType: 'PERCENTAGE',
                  adjustedMsrp: null,
                  residualizableOptionsValue: null,
                  crvMRM: null,
                  lowOdometerAdjustment: null,
                  additionalResidualAmount: null,
                  residualEnhancement: null,
                  provisionValue: null,
                  residualBasis: null,
                },
                yearlyMiles: {
                  baseValue: 0,
                  additionalValue: 0,
                  demoMileageNA: false,
                  actualPenaltyPerMile: 0,
                  penaltyPerMile: 0,
                  initialOdometerMileageRate: null,
                  initialOdometerMileageRateOverridden: false,
                  actualPenaltyPerMileOverridden: false,
                  penaltyPerMileOverridden: false,
                  totalValue: 12000,
                  totalPenalty: null,
                  removeDemoMileageFromAnnualMileage: false,
                  totalMilesHidden: 12000,
                  yearlyMilesUpdated: null,
                  reasonForChange: null,
                },
                rebates: [
                  {
                    lastModifiedBy: '492',
                    createdBy: '492',
                    deleted: false,
                    createdTime: 1742884778556,
                    modifiedTime: 1742884778556,
                    id: 'e8c87fee-0de1-4260-b3b1-86dfb8d31d57',
                    parentId: null,
                    name: 'Acura Graduate Offer',
                    languages: null,
                    rebateId: 'e8c87fee-0de1-4260-b3b1-86dfb8d31d57',
                    valueType: 0,
                    cashPercent: '0',
                    programNumber: 'AP-W60',
                    manuallyUpdated: true,
                    programCode: 'COLG',
                    programName: null,
                    dealerCash: 0,
                    dealerApprovalStatus: null,
                    autoAppliedRebate: true,
                    rebateType: 'REBATE',
                    taxType: null,
                    rebateAmount: 500,
                    custom: false,
                    certificateNumber: null,
                    manuallyEdited: false,
                    firstMonthWaiver: false,
                    paymentTypeCode: 0,
                    applicablePaymentTypes: ['10', '11'],
                    terms: [
                      {
                        startTerm: null,
                        endTerm: null,
                      },
                    ],
                    gmCardDetails: null,
                    rate: null,
                    rateReduction: null,
                    descriptionText: null,
                    vatInfo: {
                      taxCode: null,
                      vatPercent: null,
                      vatAmount: null,
                      taxId: null,
                      vatInclusive: true,
                    },
                    rebateSelections: null,
                    rebateOperator: null,
                    rebateProvider: 'CMS',
                    isCcrTaxApplicable: null,
                    code: 'AP-W60',
                    rebateCategory: null,
                    collectTaxesForEvTaxCredit: true,
                    customerNumber: null,
                    vendorNumber: null,
                    validityStartDate: null,
                    validityEndDate: null,
                    financeCompany: null,
                    make: null,
                    status: null,
                    rebateIdKey: 'e8c87fee-0de1-4260-b3b1-86dfb8d31d57__',
                    personalisedRebate: false,
                  },
                  {
                    lastModifiedBy: '492',
                    createdBy: '492',
                    deleted: false,
                    createdTime: 1742884778556,
                    modifiedTime: 1742884778556,
                    id: 'b1536f72-8a67-4112-9b26-7f0c1f50bd94',
                    parentId: null,
                    name: 'Military Appreciation',
                    languages: null,
                    rebateId: 'b1536f72-8a67-4112-9b26-7f0c1f50bd94',
                    valueType: 0,
                    cashPercent: '0',
                    programNumber: 'AP-W59',
                    manuallyUpdated: true,
                    programCode: 'MAPR',
                    programName: null,
                    dealerCash: 0,
                    dealerApprovalStatus: null,
                    autoAppliedRebate: true,
                    rebateType: 'REBATE',
                    taxType: null,
                    rebateAmount: 750,
                    custom: false,
                    certificateNumber: null,
                    manuallyEdited: false,
                    firstMonthWaiver: false,
                    paymentTypeCode: 0,
                    applicablePaymentTypes: ['10', '11'],
                    terms: [
                      {
                        startTerm: null,
                        endTerm: null,
                      },
                    ],
                    gmCardDetails: null,
                    rate: null,
                    rateReduction: null,
                    descriptionText: null,
                    vatInfo: {
                      taxCode: null,
                      vatPercent: null,
                      vatAmount: null,
                      taxId: null,
                      vatInclusive: true,
                    },
                    rebateSelections: null,
                    rebateOperator: null,
                    rebateProvider: 'CMS',
                    isCcrTaxApplicable: null,
                    code: 'AP-W59',
                    rebateCategory: null,
                    collectTaxesForEvTaxCredit: true,
                    customerNumber: null,
                    vendorNumber: null,
                    validityStartDate: null,
                    validityEndDate: null,
                    financeCompany: null,
                    make: null,
                    status: null,
                    rebateIdKey: 'b1536f72-8a67-4112-9b26-7f0c1f50bd94__',
                    personalisedRebate: false,
                  },
                ],
                firstPaymentWaiverManuallyOveridden: false,
                securityDepositRateAdjustment: 0,
                totalAcquisitionCost: null,
                totalRebateAmt: null,
                bankFee: null,
                inceptionFee: null,
                programId: null,
                programNumber: null,
                capCostReductionfactors: {
                  downpaymentFromCash: null,
                  downpaymentFromEquity: null,
                  downpaymentFromRebate: null,
                  downpaymentFromTax: null,
                  tradeInTaxableCCRBase: null,
                  tradeInCcrTaxAmount: null,
                  downPaymentFromCashTotalAmount: null,
                  downPaymentFromCashToDriveOff: null,
                },
                workingCashTableData: null,
                driveOffReductionFactors: {
                  driveOffFromRebates: null,
                  driveOffFromTradeIn: null,
                  negativeEquityCoveredFromCash: null,
                  negativeEquityCoveredFromRebate: null,
                },
                totalTaxAmt: 827.31,
                generic: false,
                cashDeficiency: null,
                imbalanceAmount: 0,
                deferredPayment1: {
                  payment: 2000,
                  paymentDate: 0,
                },
                deferredPayment2: null,
                deferredPayment3: null,
                deferredPayment4: null,
                totalPayable: null,
                daysToFirstPayment: 0,
                monthlyPaymentBeforeTax: 498.54,
                taxes: {
                  capCostReductionTaxRate: 0,
                  stateTaxRate: 0,
                  cityTaxRate: 0,
                  taxRate: 6.25,
                  manuallyUpdated: false,
                  salesTaxCapturedInDriveOff: false,
                  inventoryTaxCapturedInDriveOff: false,
                  salesTax: 827.31,
                  propertyTax: null,
                  docStampTax: null,
                  downpaymentTax: null,
                  downpaymentTaxCappedAmt: null,
                  downpaymentTaxCapped: false,
                  inventoryTax: null,
                  downpaymentTaxInFees: null,
                  fairFaxCountyTaxCappedAmt: null,
                  fairFaxCountyTaxUpfrontAmt: null,
                  fairFaxCountyTax: null,
                  otherTaxInFees: 0,
                  otherTaxInCap: 0,
                  financeCharge: null,
                  useTax: 0,
                  customFeeTaxInCap: 0,
                  customFeeTaxInFees: 0,
                  tradeTaxExempt: 0,
                  garagedStateSalesTax: null,
                  serviceContractTax: 0,
                  countyTaxRate: 0,
                  countyTax: 0,
                  cityTax: 0,
                  outOfStateLocalBusinessTax: null,
                  outOfStateLocalBusinessAftermarketTax: null,
                  outOfStateStateBusinessTax: null,
                  localBusinessTax: null,
                  localBusinessTaxCapped: null,
                  localBusinessTaxUpfront: null,
                  localBusinessAftermarketTax: null,
                  localBusinessAftermarketTaxCapped: null,
                  localBusinessAftermarketTaxUpfront: null,
                  stateBusinessTax: null,
                  acquisitionFeeTax: null,
                  stateBusinessTaxCapped: null,
                  stateBusinessTaxUpfront: null,
                  taxBreakdownUpfrontTotal: 0,
                  taxBreakdownCappedTotal: 0,
                  localAftermarketTax: null,
                  localAftermarketTaxCapped: null,
                  localAftermarketTaxUpfront: null,
                  localTax: null,
                  localTaxCapped: null,
                  localTaxUpfront: null,
                  singleArticleTax: null,
                  singleArticleTaxCapped: null,
                  singleArticleTaxUpfront: null,
                  businessTax: null,
                  rtaTax: null,
                  outOfStateLocalBusinessTaxCapped: null,
                  outOfStateLocalBusinessAftermarketTaxCapped: null,
                  outOfStateStateBusinessTaxCapped: null,
                  outOfStateLocalBusinessTaxUpfront: null,
                  outOfStateLocalBusinessAftermarketTaxUpfront: null,
                  outOfStateStateBusinessTaxUpfront: null,
                  stateTax: 827.31,
                  stateTaxUpfront: null,
                  stateTaxCapped: null,
                  gstTax: null,
                  gstTaxMutOnly: null,
                  pstTaxMutOnly: null,
                  pstTax: null,
                  qstTax: null,
                  hstTax: null,
                  totalGstOrHstTax: null,
                  totalPstTax: null,
                  insuranceTax: null,
                  catTax: null,
                  catTaxCapped: null,
                  catTaxUpfront: null,
                  bandoTaxCapped: null,
                  bandoTaxUpfront: null,
                  bandoTax: null,
                  totalTax: 827.31,
                  monthlyUseTax: null,
                  leaseTax: null,
                  municipalTax: null,
                  spdsTax: null,
                  ccrTax: null,
                  cappedCostTax: null,
                  motorVehicleTax: null,
                  privilegeTax: null,
                  commonTax: null,
                  warrantyTax: null,
                  sellingPriceSalesTax: null,
                  luxuryTax: null,
                  lenderTaxExemptFeeTax: null,
                  localFnITax: null,
                  businessFnITax: null,
                  stateFnITax: null,
                  docFeeTax: null,
                  bpolTax: null,
                  stateExciseTax: null,
                  totalFnIProductTax: null,
                  otherFnIProductTax: null,
                  wholesaleTaxUpfront: null,
                  wholesaleTaxCapped: null,
                  taxBreakups: [],
                  taxDetails: [
                    {
                      taxType: 'STATE_TAX',
                      displayName: 'State Tax',
                      upfront: null,
                      amount: 827.31,
                      childTax: true,
                      parentType: 'SALES_TAX',
                      display: false,
                      percentage: 6.25,
                      taxableBase: 13236.96,
                      productId: null,
                    },
                    {
                      taxType: 'SALES_TAX',
                      displayName: 'Sales Tax',
                      upfront: null,
                      amount: 827.31,
                      childTax: false,
                      parentType: null,
                      display: true,
                      percentage: 6.25,
                      taxableBase: 13236.96,
                      productId: null,
                    },
                  ],
                  taxBreakDown: null,
                  fniTaxBreakup: null,
                  feeTaxBreakupDetails: null,
                  vitTaxRate: null,
                  fniTaxBreakupBasedOnDisclosure: null,
                  districtTax: 0,
                },
                vendorTaxes: null,
                dealFees: [
                  {
                    lastModifiedBy: '492',
                    createdBy: '492',
                    deleted: false,
                    createdTime: *************,
                    modifiedTime: *************,
                    id: 'ea35fe2a-5f17-4738-bb58-acad45db6b68',
                    overriden: false,
                    name: 'TireFee',
                    languages: {
                      documentDefault: 'en_US',
                      locale: null,
                      keyValueMapList: null,
                    },
                    manuallyUpdated: false,
                    amount: 50,
                    feeCode: 'TT4',
                    upfront: true,
                    taxable: false,
                    taxUpFront: false,
                    marketScanAmt: null,
                    manuallyAdded: false,
                    internalFeeCode: 'TIRE_FEE',
                    marketScan: false,
                    source: 'SALES_SETUP',
                    feeType: 'DMV',
                    standardAmount: null,
                    markup: null,
                    manuallyEdited: false,
                    baseAmount: null,
                    pricingType: null,
                    feeCalcType: null,
                    taxConfigs: [
                      {
                        taxRegimeType: 'SALES_TAX',
                        taxable: true,
                      },
                    ],
                    isCommissionable: null,
                    vatInfo: {
                      taxCode: null,
                      vatPercent: null,
                      vatAmount: null,
                      taxId: null,
                      vatInclusive: true,
                    },
                    outOfPocketFee: null,
                    productClassification: null,
                    accountingGroup: null,
                    vendorNumber: null,
                    costPrice: null,
                    waiveAcquisitionFee: null,
                    vendorId: null,
                    costVatTaxInfo: {
                      taxCode: null,
                      vatPercent: null,
                      vatAmount: null,
                      taxId: null,
                      vatInclusive: true,
                    },
                    costVatPrice: null,
                    impactOnEmi: null,
                    netProfit: 50,
                    netSale: 50,
                    transactionType: null,
                  },
                  {
                    lastModifiedBy: '492',
                    createdBy: '492',
                    deleted: false,
                    createdTime: *************,
                    modifiedTime: *************,
                    id: '88cd4a33-ec51-4312-b008-a75523ce74fc',
                    overriden: false,
                    name: 'DuplicateFees1LN',
                    languages: {
                      documentDefault: 'en_US',
                      locale: null,
                      keyValueMapList: null,
                    },
                    manuallyUpdated: false,
                    amount: 100,
                    feeCode: 'TitleFees57LN',
                    upfront: true,
                    taxable: true,
                    taxUpFront: false,
                    marketScanAmt: null,
                    manuallyAdded: false,
                    internalFeeCode: 'TEMPORARY_TAG_FEE',
                    marketScan: false,
                    source: 'SALES_SETUP',
                    feeType: 'DMV',
                    standardAmount: null,
                    markup: null,
                    manuallyEdited: false,
                    baseAmount: null,
                    pricingType: null,
                    feeCalcType: null,
                    taxConfigs: [
                      {
                        taxRegimeType: 'SALES_TAX',
                        taxable: true,
                      },
                    ],
                    isCommissionable: null,
                    vatInfo: {
                      taxCode: null,
                      vatPercent: null,
                      vatAmount: null,
                      taxId: null,
                      vatInclusive: true,
                    },
                    outOfPocketFee: null,
                    productClassification: null,
                    accountingGroup: null,
                    vendorNumber: null,
                    costPrice: null,
                    waiveAcquisitionFee: null,
                    vendorId: null,
                    costVatTaxInfo: {
                      taxCode: null,
                      vatPercent: null,
                      vatAmount: null,
                      taxId: null,
                      vatInclusive: true,
                    },
                    costVatPrice: null,
                    impactOnEmi: null,
                    netProfit: 100,
                    netSale: 100,
                    transactionType: null,
                  },
                  {
                    lastModifiedBy: '492',
                    createdBy: '492',
                    deleted: false,
                    createdTime: *************,
                    modifiedTime: *************,
                    id: 'f5ffc041-a693-4953-a969-5557b33527b9',
                    overriden: false,
                    name: 'Title Certificate Fee',
                    languages: null,
                    manuallyUpdated: false,
                    amount: 75,
                    feeCode: 'Title Fee',
                    upfront: true,
                    taxable: false,
                    taxUpFront: false,
                    marketScanAmt: null,
                    manuallyAdded: false,
                    internalFeeCode: 'TITLE_FEE',
                    marketScan: false,
                    source: 'GALAXY',
                    feeType: 'DMV',
                    standardAmount: 75,
                    markup: null,
                    manuallyEdited: false,
                    baseAmount: null,
                    pricingType: null,
                    feeCalcType: null,
                    taxConfigs: null,
                    isCommissionable: null,
                    vatInfo: null,
                    outOfPocketFee: null,
                    productClassification: null,
                    accountingGroup: null,
                    vendorNumber: null,
                    costPrice: null,
                    waiveAcquisitionFee: null,
                    vendorId: null,
                    costVatTaxInfo: null,
                    costVatPrice: null,
                    impactOnEmi: null,
                    netProfit: 75,
                    netSale: 75,
                    transactionType: null,
                  },
                  {
                    lastModifiedBy: '492',
                    createdBy: '492',
                    deleted: false,
                    createdTime: *************,
                    modifiedTime: *************,
                    id: '459a3c88-fae8-45d1-b4dc-99486f3e1a18',
                    overriden: false,
                    name: 'Registration Fee',
                    languages: null,
                    manuallyUpdated: false,
                    amount: 60,
                    feeCode: 'Registration Fee',
                    upfront: true,
                    taxable: false,
                    taxUpFront: false,
                    marketScanAmt: null,
                    manuallyAdded: false,
                    internalFeeCode: 'REGISTRATION_CHARGES',
                    marketScan: false,
                    source: 'GALAXY',
                    feeType: 'DMV',
                    standardAmount: 60,
                    markup: null,
                    manuallyEdited: false,
                    baseAmount: null,
                    pricingType: null,
                    feeCalcType: null,
                    taxConfigs: null,
                    isCommissionable: null,
                    vatInfo: null,
                    outOfPocketFee: null,
                    productClassification: null,
                    accountingGroup: null,
                    vendorNumber: null,
                    costPrice: null,
                    waiveAcquisitionFee: null,
                    vendorId: null,
                    costVatTaxInfo: null,
                    costVatPrice: null,
                    impactOnEmi: null,
                    netProfit: 60,
                    netSale: 60,
                    transactionType: null,
                  },
                  {
                    lastModifiedBy: '492',
                    createdBy: '492',
                    deleted: false,
                    createdTime: *************,
                    modifiedTime: *************,
                    id: 'e45bfa1a-5451-40ef-96dc-5f059539a71b',
                    overriden: false,
                    name: 'Plate Transfer Fee',
                    languages: null,
                    manuallyUpdated: false,
                    amount: 25,
                    feeCode: 'Plate Transfer Fee',
                    upfront: true,
                    taxable: false,
                    taxUpFront: false,
                    marketScanAmt: null,
                    manuallyAdded: false,
                    internalFeeCode: 'PLATE_TRANSFER_FEE',
                    marketScan: false,
                    source: 'GALAXY',
                    feeType: 'DMV',
                    standardAmount: 25,
                    markup: null,
                    manuallyEdited: false,
                    baseAmount: null,
                    pricingType: null,
                    feeCalcType: null,
                    taxConfigs: null,
                    isCommissionable: null,
                    vatInfo: null,
                    outOfPocketFee: null,
                    productClassification: null,
                    accountingGroup: null,
                    vendorNumber: null,
                    costPrice: null,
                    waiveAcquisitionFee: null,
                    vendorId: null,
                    costVatTaxInfo: null,
                    costVatPrice: null,
                    impactOnEmi: null,
                    netProfit: 25,
                    netSale: 25,
                    transactionType: null,
                  },
                ],
                manuallyDeletedFees: null,
                firstPaymentWaived: true,
                firstPaymentWaivedAmount: 0,
                firstPaymentWaivedMaxAmount: null,
                registrationnFee: null,
                stateFee: null,
                titleFee: null,
                wasteTireFee: null,
                serviceContractNotIncludedInSalesTax: false,
                bandOFee: null,
                lienFee: null,
                totalCapReduced: 0,
                paidReserveIsFlatFee: false,
                maxRateMarkup: null,
                msLenderCode: null,
                paidReserveIsCapCostParticipation: false,
                collectRegFee: false,
                downPaymentIndex: 0,
                singleScheduledPayment: null,
                taxWaivedPayments: null,
                programFlags: null,
                taxCode: null,
                taxZipCode: null,
                taxAddress: null,
                taxCity: null,
                taxCounty: null,
                taxExemptAmount: null,
                totalRebateAmount: null,
                totalDealerCashAmount: null,
                rolledOverCashDeficiency: null,
                totalMonthlyPayment: null,
                financeCharge: 517.93,
                dueFromCustomerMainInvoice: null,
                dueFromCustomer: null,
                fordFlexDecreasePct: null,
                fordFlexFirstPayment: null,
                fordFlexFirstTerm: null,
                fordFlexLastPayment: null,
                rebatesBreakup: null,
                proRataAdjustment: null,
                applyCanadaLuxuryTax: false,
                programExpirationDate: null,
                paymentBreakups: {
                  driveOff: [],
                  outOfPocketCash: [
                    {
                      total: 2000,
                      type: 'CUSTOMER_CASH',
                      breakupItemList: [],
                    },
                  ],
                  grossCapCost: [],
                  netCapCost: [],
                  luxuryTax: null,
                },
                discount: null,
                hasStandardData: false,
                downPaymentWithoutCCRTax: null,
                totalAmountPayable: null,
                optionalFinalPayment: null,
                optionToPurchaseFee: null,
                totalDeposit: null,
                quoteDetails: null,
                infoMessages: null,
                paymentTax: null,
                upfrontPayments: null,
                vcciFields: {
                  baseVehicleCashPrice: null,
                  totalDownPaymentAmount: null,
                  aggregateCashPrice: null,
                  cashValueOfLeasedGoodsAmount: null,
                  valueGivenByYouAmount: null,
                  valueReceivedByYouAmount: null,
                  costOfBorrowingAmount: null,
                  taxableBaseMonthlyPaymentAmount: null,
                  taxableTotalOfBaseMonthlyPaymentsAmount: null,
                  implicitFinanceChargesAmount: null,
                  totalCostOfTerm: null,
                  totalCostOfLease: null,
                  leaseCapitalizedAmount: null,
                  feeSubtotal: null,
                  upfrontCashDownPaymentAmount: null,
                  upfrontMfgRebateAmount: null,
                  canadaAcrPercentage: null,
                  upfrontNetTradeAmount: null,
                  rebateApplicableIfCashDeal: null,
                  financeSubtotalTypeOther: null,
                  adjustedBalanceForCreditRateCalculation: null,
                  totalAmountPayable: null,
                  totalVehicleCashPrice: null,
                  taxableSellingPrice: null,
                  totalAmountDueAtSigning: null,
                  aggregateEmiAmount: null,
                  cashDownPaymentAmount: null,
                  lenderNetCapCost: null,
                  totalSellingPrice: null,
                },
                financingProductCode: null,
                contractInTransitAmount: null,
                totalUnroundedBaseMonthlyPayment: null,
                unroundedBaseMonthlyPayment: null,
                multipleSecurityDepositProgramList: null,
                securityDepositWaiverDetails: null,
                deposit: null,
                dueAtSigning: 2000,
                remainingDueAtSigning: 2000,
                previousResponseAdjustmentData: {
                  demoMilesAdjustment: null,
                  additionalMileageAdjustment: null,
                  lowOdometerAdjustment: null,
                  baseResidualValue: null,
                  baseResidualPercentage: 0,
                  additionalResidualAmount: null,
                },
                priorTradeBalance: 0,
                ruleWarningMap: {},
                programOverridden: false,
                subventionCostValueAbsolute: null,
                subventionCostValuePercentage: null,
                subventionCostOverridden: false,
                dueFromCustomerWithoutPaidCustomerCash: null,
                categoryBasedTotalRebates: null,
                taxStrategy: null,
                taxOutOfState: false,
                rebatesVerified: false,
              },
              {
                lastModifiedBy: '492',
                createdBy: '492',
                deleted: false,
                createdTime: 1742884778580,
                modifiedTime: 1742884778580,
                id: 'b8ae08a1-4051-44bc-9f4a-5c2e6e2a4f60',
                preQualificationStatus: 'NOT_INITIATED',
                downPayment: 500,
                downPaymentPercentage: null,
                downPaymentPct: 3.48,
                downPaymentPctType: 'SELLING_PRICE',
                calcBasedOnDownPmtPct: true,
                outOfPocketCash: 500,
                tradeEquityCashBack: null,
                securityDepositOverridden: false,
                securityDeposits: null,
                paymentInfo: null,
                securityDepositV2: null,
                securityDepositId: null,
                securityDepositType: null,
                securityDeposit: 0,
                emiAmount: 598.15,
                onePayBaseEmiWithoutTaxPerTerm: null,
                onePayGstTaxPerTerm: null,
                onePayHstTaxPerTerm: null,
                onePayPstTaxPerTerm: null,
                onePayQstTaxPerTerm: null,
                onePayEmiPerTerm: null,
                onePayMonthlyPaymentOnlyCapped: null,
                monthlyPaymentOnlyCapped: null,
                onePayPayment: null,
                baseEmiAmount: 563.68,
                amountFinanced: 13774.31,
                loanToValueRatio: 95.74,
                selected: false,
                osfLender: false,
                lender: 'CVB',
                lenderId: '6205025fe3590f965ceb5167',
                lenderCode: null,
                lienFilingCode: null,
                apr: {
                  buyRate: 4,
                  effectiveRate: 0,
                  buyRateOverridden: true,
                  apr: 4,
                  moneyFactor: 4,
                  financeReserve: 0,
                  pctOfCapCost: null,
                  financeReserveOverridden: false,
                  aprCode: null,
                  markUp: 0,
                  markUpOverridden: false,
                  participation: 100,
                  reserveMethod: 0,
                  rateType: 0,
                  lenderRateType: 0,
                  manuallyUpdated: false,
                  alternateRateAdjustments: null,
                  rateOfInterest: null,
                  originalAPR: 4,
                  originalBuyRate: 4,
                  originalMarkup: 0,
                  originalRateType: 0,
                  useStandardProgram: false,
                  programType: 'CUSTOM',
                  blendedRateApplied: false,
                },
                residual: {
                  baseValue: null,
                  manuallyUpdated: false,
                  residualOverridden: false,
                  basePercentage: 0,
                  adjustedValue: null,
                  adjustedPercentage: 0,
                  totalValue: null,
                  balloonPaymentValue: null,
                  oneTimeBalloonPayment: null,
                  totalPercentage: 0,
                  residualName: null,
                  demoMilesPenaltyMiles: 0,
                  demoMilesRate: null,
                  demoMilesResidualAdjustment: null,
                  demoMilesResidualAdjustmentPct: null,
                  residualDisplayType: 'PERCENTAGE',
                  adjustedMsrp: null,
                  residualizableOptionsValue: null,
                  crvMRM: null,
                  lowOdometerAdjustment: null,
                  additionalResidualAmount: null,
                  residualEnhancement: null,
                  provisionValue: null,
                  residualBasis: null,
                },
                yearlyMiles: {
                  baseValue: 0,
                  additionalValue: 0,
                  demoMileageNA: false,
                  actualPenaltyPerMile: 0,
                  penaltyPerMile: 0,
                  initialOdometerMileageRate: null,
                  initialOdometerMileageRateOverridden: false,
                  actualPenaltyPerMileOverridden: false,
                  penaltyPerMileOverridden: false,
                  totalValue: 12000,
                  totalPenalty: null,
                  removeDemoMileageFromAnnualMileage: false,
                  totalMilesHidden: 12000,
                  yearlyMilesUpdated: null,
                  reasonForChange: null,
                },
                rebates: [
                  {
                    lastModifiedBy: '492',
                    createdBy: '492',
                    deleted: false,
                    createdTime: 1742884778557,
                    modifiedTime: 1742884778557,
                    id: 'e8c87fee-0de1-4260-b3b1-86dfb8d31d57',
                    parentId: null,
                    name: 'Acura Graduate Offer',
                    languages: null,
                    rebateId: 'e8c87fee-0de1-4260-b3b1-86dfb8d31d57',
                    valueType: 0,
                    cashPercent: '0',
                    programNumber: 'AP-W60',
                    manuallyUpdated: true,
                    programCode: 'COLG',
                    programName: null,
                    dealerCash: 0,
                    dealerApprovalStatus: null,
                    autoAppliedRebate: true,
                    rebateType: 'REBATE',
                    taxType: null,
                    rebateAmount: 500,
                    custom: false,
                    certificateNumber: null,
                    manuallyEdited: false,
                    firstMonthWaiver: false,
                    paymentTypeCode: 0,
                    applicablePaymentTypes: ['10', '11'],
                    terms: [
                      {
                        startTerm: null,
                        endTerm: null,
                      },
                    ],
                    gmCardDetails: null,
                    rate: null,
                    rateReduction: null,
                    descriptionText: null,
                    vatInfo: {
                      taxCode: null,
                      vatPercent: null,
                      vatAmount: null,
                      taxId: null,
                      vatInclusive: true,
                    },
                    rebateSelections: null,
                    rebateOperator: null,
                    rebateProvider: 'CMS',
                    isCcrTaxApplicable: null,
                    code: 'AP-W60',
                    rebateCategory: null,
                    collectTaxesForEvTaxCredit: true,
                    customerNumber: null,
                    vendorNumber: null,
                    validityStartDate: null,
                    validityEndDate: null,
                    financeCompany: null,
                    make: null,
                    status: null,
                    rebateIdKey: 'e8c87fee-0de1-4260-b3b1-86dfb8d31d57__',
                    personalisedRebate: false,
                  },
                  {
                    lastModifiedBy: '492',
                    createdBy: '492',
                    deleted: false,
                    createdTime: 1742884778558,
                    modifiedTime: 1742884778558,
                    id: 'b1536f72-8a67-4112-9b26-7f0c1f50bd94',
                    parentId: null,
                    name: 'Military Appreciation',
                    languages: null,
                    rebateId: 'b1536f72-8a67-4112-9b26-7f0c1f50bd94',
                    valueType: 0,
                    cashPercent: '0',
                    programNumber: 'AP-W59',
                    manuallyUpdated: true,
                    programCode: 'MAPR',
                    programName: null,
                    dealerCash: 0,
                    dealerApprovalStatus: null,
                    autoAppliedRebate: true,
                    rebateType: 'REBATE',
                    taxType: null,
                    rebateAmount: 750,
                    custom: false,
                    certificateNumber: null,
                    manuallyEdited: false,
                    firstMonthWaiver: false,
                    paymentTypeCode: 0,
                    applicablePaymentTypes: ['10', '11'],
                    terms: [
                      {
                        startTerm: null,
                        endTerm: null,
                      },
                    ],
                    gmCardDetails: null,
                    rate: null,
                    rateReduction: null,
                    descriptionText: null,
                    vatInfo: {
                      taxCode: null,
                      vatPercent: null,
                      vatAmount: null,
                      taxId: null,
                      vatInclusive: true,
                    },
                    rebateSelections: null,
                    rebateOperator: null,
                    rebateProvider: 'CMS',
                    isCcrTaxApplicable: null,
                    code: 'AP-W59',
                    rebateCategory: null,
                    collectTaxesForEvTaxCredit: true,
                    customerNumber: null,
                    vendorNumber: null,
                    validityStartDate: null,
                    validityEndDate: null,
                    financeCompany: null,
                    make: null,
                    status: null,
                    rebateIdKey: 'b1536f72-8a67-4112-9b26-7f0c1f50bd94__',
                    personalisedRebate: false,
                  },
                ],
                firstPaymentWaiverManuallyOveridden: false,
                securityDepositRateAdjustment: 0,
                totalAcquisitionCost: null,
                totalRebateAmt: null,
                bankFee: null,
                inceptionFee: null,
                programId: null,
                programNumber: null,
                capCostReductionfactors: {
                  downpaymentFromCash: null,
                  downpaymentFromEquity: null,
                  downpaymentFromRebate: null,
                  downpaymentFromTax: null,
                  tradeInTaxableCCRBase: null,
                  tradeInCcrTaxAmount: null,
                  downPaymentFromCashTotalAmount: null,
                  downPaymentFromCashToDriveOff: null,
                },
                workingCashTableData: null,
                driveOffReductionFactors: {
                  driveOffFromRebates: null,
                  driveOffFromTradeIn: null,
                  negativeEquityCoveredFromCash: null,
                  negativeEquityCoveredFromRebate: null,
                },
                totalTaxAmt: 827.31,
                generic: false,
                cashDeficiency: null,
                imbalanceAmount: 0,
                deferredPayment1: {
                  payment: 500,
                  paymentDate: 0,
                },
                deferredPayment2: null,
                deferredPayment3: null,
                deferredPayment4: null,
                totalPayable: null,
                daysToFirstPayment: 0,
                monthlyPaymentBeforeTax: 563.68,
                taxes: {
                  capCostReductionTaxRate: 0,
                  stateTaxRate: 0,
                  cityTaxRate: 0,
                  taxRate: 6.25,
                  manuallyUpdated: false,
                  salesTaxCapturedInDriveOff: false,
                  inventoryTaxCapturedInDriveOff: false,
                  salesTax: 827.31,
                  propertyTax: null,
                  docStampTax: null,
                  downpaymentTax: null,
                  downpaymentTaxCappedAmt: null,
                  downpaymentTaxCapped: false,
                  inventoryTax: null,
                  downpaymentTaxInFees: null,
                  fairFaxCountyTaxCappedAmt: null,
                  fairFaxCountyTaxUpfrontAmt: null,
                  fairFaxCountyTax: null,
                  otherTaxInFees: 0,
                  otherTaxInCap: 0,
                  financeCharge: null,
                  useTax: 0,
                  customFeeTaxInCap: 0,
                  customFeeTaxInFees: 0,
                  tradeTaxExempt: 0,
                  garagedStateSalesTax: null,
                  serviceContractTax: 0,
                  countyTaxRate: 0,
                  countyTax: 0,
                  cityTax: 0,
                  outOfStateLocalBusinessTax: null,
                  outOfStateLocalBusinessAftermarketTax: null,
                  outOfStateStateBusinessTax: null,
                  localBusinessTax: null,
                  localBusinessTaxCapped: null,
                  localBusinessTaxUpfront: null,
                  localBusinessAftermarketTax: null,
                  localBusinessAftermarketTaxCapped: null,
                  localBusinessAftermarketTaxUpfront: null,
                  stateBusinessTax: null,
                  acquisitionFeeTax: null,
                  stateBusinessTaxCapped: null,
                  stateBusinessTaxUpfront: null,
                  taxBreakdownUpfrontTotal: 0,
                  taxBreakdownCappedTotal: 0,
                  localAftermarketTax: null,
                  localAftermarketTaxCapped: null,
                  localAftermarketTaxUpfront: null,
                  localTax: null,
                  localTaxCapped: null,
                  localTaxUpfront: null,
                  singleArticleTax: null,
                  singleArticleTaxCapped: null,
                  singleArticleTaxUpfront: null,
                  businessTax: null,
                  rtaTax: null,
                  outOfStateLocalBusinessTaxCapped: null,
                  outOfStateLocalBusinessAftermarketTaxCapped: null,
                  outOfStateStateBusinessTaxCapped: null,
                  outOfStateLocalBusinessTaxUpfront: null,
                  outOfStateLocalBusinessAftermarketTaxUpfront: null,
                  outOfStateStateBusinessTaxUpfront: null,
                  stateTax: 827.31,
                  stateTaxUpfront: null,
                  stateTaxCapped: null,
                  gstTax: null,
                  gstTaxMutOnly: null,
                  pstTaxMutOnly: null,
                  pstTax: null,
                  qstTax: null,
                  hstTax: null,
                  totalGstOrHstTax: null,
                  totalPstTax: null,
                  insuranceTax: null,
                  catTax: null,
                  catTaxCapped: null,
                  catTaxUpfront: null,
                  bandoTaxCapped: null,
                  bandoTaxUpfront: null,
                  bandoTax: null,
                  totalTax: 827.31,
                  monthlyUseTax: null,
                  leaseTax: null,
                  municipalTax: null,
                  spdsTax: null,
                  ccrTax: null,
                  cappedCostTax: null,
                  motorVehicleTax: null,
                  privilegeTax: null,
                  commonTax: null,
                  warrantyTax: null,
                  sellingPriceSalesTax: null,
                  luxuryTax: null,
                  lenderTaxExemptFeeTax: null,
                  localFnITax: null,
                  businessFnITax: null,
                  stateFnITax: null,
                  docFeeTax: null,
                  bpolTax: null,
                  stateExciseTax: null,
                  totalFnIProductTax: null,
                  otherFnIProductTax: null,
                  wholesaleTaxUpfront: null,
                  wholesaleTaxCapped: null,
                  taxBreakups: [],
                  taxDetails: [
                    {
                      taxType: 'STATE_TAX',
                      displayName: 'State Tax',
                      upfront: null,
                      amount: 827.31,
                      childTax: true,
                      parentType: 'SALES_TAX',
                      display: false,
                      percentage: 6.25,
                      taxableBase: 13236.96,
                      productId: null,
                    },
                    {
                      taxType: 'SALES_TAX',
                      displayName: 'Sales Tax',
                      upfront: null,
                      amount: 827.31,
                      childTax: false,
                      parentType: null,
                      display: true,
                      percentage: 6.25,
                      taxableBase: 13236.96,
                      productId: null,
                    },
                  ],
                  taxBreakDown: null,
                  fniTaxBreakup: null,
                  feeTaxBreakupDetails: null,
                  vitTaxRate: null,
                  fniTaxBreakupBasedOnDisclosure: null,
                  districtTax: 0,
                },
                vendorTaxes: null,
                dealFees: [
                  {
                    lastModifiedBy: '492',
                    createdBy: '492',
                    deleted: false,
                    createdTime: *************,
                    modifiedTime: *************,
                    id: 'ea35fe2a-5f17-4738-bb58-acad45db6b68',
                    overriden: false,
                    name: 'TireFee',
                    languages: {
                      documentDefault: 'en_US',
                      locale: null,
                      keyValueMapList: null,
                    },
                    manuallyUpdated: false,
                    amount: 50,
                    feeCode: 'TT4',
                    upfront: true,
                    taxable: false,
                    taxUpFront: false,
                    marketScanAmt: null,
                    manuallyAdded: false,
                    internalFeeCode: 'TIRE_FEE',
                    marketScan: false,
                    source: 'SALES_SETUP',
                    feeType: 'DMV',
                    standardAmount: null,
                    markup: null,
                    manuallyEdited: false,
                    baseAmount: null,
                    pricingType: null,
                    feeCalcType: null,
                    taxConfigs: [
                      {
                        taxRegimeType: 'SALES_TAX',
                        taxable: true,
                      },
                    ],
                    isCommissionable: null,
                    vatInfo: {
                      taxCode: null,
                      vatPercent: null,
                      vatAmount: null,
                      taxId: null,
                      vatInclusive: true,
                    },
                    outOfPocketFee: null,
                    productClassification: null,
                    accountingGroup: null,
                    vendorNumber: null,
                    costPrice: null,
                    waiveAcquisitionFee: null,
                    vendorId: null,
                    costVatTaxInfo: {
                      taxCode: null,
                      vatPercent: null,
                      vatAmount: null,
                      taxId: null,
                      vatInclusive: true,
                    },
                    costVatPrice: null,
                    impactOnEmi: null,
                    netProfit: 50,
                    netSale: 50,
                    transactionType: null,
                  },
                  {
                    lastModifiedBy: '492',
                    createdBy: '492',
                    deleted: false,
                    createdTime: *************,
                    modifiedTime: *************,
                    id: '88cd4a33-ec51-4312-b008-a75523ce74fc',
                    overriden: false,
                    name: 'DuplicateFees1LN',
                    languages: {
                      documentDefault: 'en_US',
                      locale: null,
                      keyValueMapList: null,
                    },
                    manuallyUpdated: false,
                    amount: 100,
                    feeCode: 'TitleFees57LN',
                    upfront: true,
                    taxable: true,
                    taxUpFront: false,
                    marketScanAmt: null,
                    manuallyAdded: false,
                    internalFeeCode: 'TEMPORARY_TAG_FEE',
                    marketScan: false,
                    source: 'SALES_SETUP',
                    feeType: 'DMV',
                    standardAmount: null,
                    markup: null,
                    manuallyEdited: false,
                    baseAmount: null,
                    pricingType: null,
                    feeCalcType: null,
                    taxConfigs: [
                      {
                        taxRegimeType: 'SALES_TAX',
                        taxable: true,
                      },
                    ],
                    isCommissionable: null,
                    vatInfo: {
                      taxCode: null,
                      vatPercent: null,
                      vatAmount: null,
                      taxId: null,
                      vatInclusive: true,
                    },
                    outOfPocketFee: null,
                    productClassification: null,
                    accountingGroup: null,
                    vendorNumber: null,
                    costPrice: null,
                    waiveAcquisitionFee: null,
                    vendorId: null,
                    costVatTaxInfo: {
                      taxCode: null,
                      vatPercent: null,
                      vatAmount: null,
                      taxId: null,
                      vatInclusive: true,
                    },
                    costVatPrice: null,
                    impactOnEmi: null,
                    netProfit: 100,
                    netSale: 100,
                    transactionType: null,
                  },
                  {
                    lastModifiedBy: '492',
                    createdBy: '492',
                    deleted: false,
                    createdTime: *************,
                    modifiedTime: *************,
                    id: 'b2c0d1f7-3bc0-4871-bd66-46ed045881ff',
                    overriden: false,
                    name: 'Title Certificate Fee',
                    languages: null,
                    manuallyUpdated: false,
                    amount: 75,
                    feeCode: 'Title Fee',
                    upfront: true,
                    taxable: false,
                    taxUpFront: false,
                    marketScanAmt: null,
                    manuallyAdded: false,
                    internalFeeCode: 'TITLE_FEE',
                    marketScan: false,
                    source: 'GALAXY',
                    feeType: 'DMV',
                    standardAmount: 75,
                    markup: null,
                    manuallyEdited: false,
                    baseAmount: null,
                    pricingType: null,
                    feeCalcType: null,
                    taxConfigs: null,
                    isCommissionable: null,
                    vatInfo: null,
                    outOfPocketFee: null,
                    productClassification: null,
                    accountingGroup: null,
                    vendorNumber: null,
                    costPrice: null,
                    waiveAcquisitionFee: null,
                    vendorId: null,
                    costVatTaxInfo: null,
                    costVatPrice: null,
                    impactOnEmi: null,
                    netProfit: 75,
                    netSale: 75,
                    transactionType: null,
                  },
                  {
                    lastModifiedBy: '492',
                    createdBy: '492',
                    deleted: false,
                    createdTime: *************,
                    modifiedTime: *************,
                    id: '2e671f37-6c36-4062-9038-3fd96544cf6b',
                    overriden: false,
                    name: 'Registration Fee',
                    languages: null,
                    manuallyUpdated: false,
                    amount: 60,
                    feeCode: 'Registration Fee',
                    upfront: true,
                    taxable: false,
                    taxUpFront: false,
                    marketScanAmt: null,
                    manuallyAdded: false,
                    internalFeeCode: 'REGISTRATION_CHARGES',
                    marketScan: false,
                    source: 'GALAXY',
                    feeType: 'DMV',
                    standardAmount: 60,
                    markup: null,
                    manuallyEdited: false,
                    baseAmount: null,
                    pricingType: null,
                    feeCalcType: null,
                    taxConfigs: null,
                    isCommissionable: null,
                    vatInfo: null,
                    outOfPocketFee: null,
                    productClassification: null,
                    accountingGroup: null,
                    vendorNumber: null,
                    costPrice: null,
                    waiveAcquisitionFee: null,
                    vendorId: null,
                    costVatTaxInfo: null,
                    costVatPrice: null,
                    impactOnEmi: null,
                    netProfit: 60,
                    netSale: 60,
                    transactionType: null,
                  },
                  {
                    lastModifiedBy: '492',
                    createdBy: '492',
                    deleted: false,
                    createdTime: *************,
                    modifiedTime: *************,
                    id: 'e8cd1942-f565-4310-99d3-8eb4f1b34a35',
                    overriden: false,
                    name: 'Plate Transfer Fee',
                    languages: null,
                    manuallyUpdated: false,
                    amount: 25,
                    feeCode: 'Plate Transfer Fee',
                    upfront: true,
                    taxable: false,
                    taxUpFront: false,
                    marketScanAmt: null,
                    manuallyAdded: false,
                    internalFeeCode: 'PLATE_TRANSFER_FEE',
                    marketScan: false,
                    source: 'GALAXY',
                    feeType: 'DMV',
                    standardAmount: 25,
                    markup: null,
                    manuallyEdited: false,
                    baseAmount: null,
                    pricingType: null,
                    feeCalcType: null,
                    taxConfigs: null,
                    isCommissionable: null,
                    vatInfo: null,
                    outOfPocketFee: null,
                    productClassification: null,
                    accountingGroup: null,
                    vendorNumber: null,
                    costPrice: null,
                    waiveAcquisitionFee: null,
                    vendorId: null,
                    costVatTaxInfo: null,
                    costVatPrice: null,
                    impactOnEmi: null,
                    netProfit: 25,
                    netSale: 25,
                    transactionType: null,
                  },
                ],
                manuallyDeletedFees: null,
                firstPaymentWaived: true,
                firstPaymentWaivedAmount: 0,
                firstPaymentWaivedMaxAmount: null,
                registrationnFee: null,
                stateFee: null,
                titleFee: null,
                wasteTireFee: null,
                serviceContractNotIncludedInSalesTax: false,
                bandOFee: null,
                lienFee: null,
                totalCapReduced: 0,
                paidReserveIsFlatFee: false,
                maxRateMarkup: null,
                msLenderCode: null,
                paidReserveIsCapCostParticipation: false,
                collectRegFee: false,
                downPaymentIndex: 0,
                singleScheduledPayment: null,
                taxWaivedPayments: null,
                programFlags: null,
                taxCode: null,
                taxZipCode: null,
                taxAddress: null,
                taxCity: null,
                taxCounty: null,
                taxExemptAmount: null,
                totalRebateAmount: null,
                totalDealerCashAmount: null,
                rolledOverCashDeficiency: null,
                totalMonthlyPayment: null,
                financeCharge: 581.29,
                dueFromCustomerMainInvoice: null,
                dueFromCustomer: null,
                fordFlexDecreasePct: null,
                fordFlexFirstPayment: null,
                fordFlexFirstTerm: null,
                fordFlexLastPayment: null,
                rebatesBreakup: null,
                proRataAdjustment: null,
                applyCanadaLuxuryTax: false,
                programExpirationDate: null,
                paymentBreakups: {
                  driveOff: [],
                  outOfPocketCash: [
                    {
                      total: 500,
                      type: 'CUSTOMER_CASH',
                      breakupItemList: [],
                    },
                  ],
                  grossCapCost: [],
                  netCapCost: [],
                  luxuryTax: null,
                },
                discount: null,
                hasStandardData: false,
                downPaymentWithoutCCRTax: null,
                totalAmountPayable: null,
                optionalFinalPayment: null,
                optionToPurchaseFee: null,
                totalDeposit: null,
                quoteDetails: null,
                infoMessages: null,
                paymentTax: null,
                upfrontPayments: null,
                vcciFields: {
                  baseVehicleCashPrice: null,
                  totalDownPaymentAmount: null,
                  aggregateCashPrice: null,
                  cashValueOfLeasedGoodsAmount: null,
                  valueGivenByYouAmount: null,
                  valueReceivedByYouAmount: null,
                  costOfBorrowingAmount: null,
                  taxableBaseMonthlyPaymentAmount: null,
                  taxableTotalOfBaseMonthlyPaymentsAmount: null,
                  implicitFinanceChargesAmount: null,
                  totalCostOfTerm: null,
                  totalCostOfLease: null,
                  leaseCapitalizedAmount: null,
                  feeSubtotal: null,
                  upfrontCashDownPaymentAmount: null,
                  upfrontMfgRebateAmount: null,
                  canadaAcrPercentage: null,
                  upfrontNetTradeAmount: null,
                  rebateApplicableIfCashDeal: null,
                  financeSubtotalTypeOther: null,
                  adjustedBalanceForCreditRateCalculation: null,
                  totalAmountPayable: null,
                  totalVehicleCashPrice: null,
                  taxableSellingPrice: null,
                  totalAmountDueAtSigning: null,
                  aggregateEmiAmount: null,
                  cashDownPaymentAmount: null,
                  lenderNetCapCost: null,
                  totalSellingPrice: null,
                },
                financingProductCode: null,
                contractInTransitAmount: null,
                totalUnroundedBaseMonthlyPayment: null,
                unroundedBaseMonthlyPayment: null,
                multipleSecurityDepositProgramList: null,
                securityDepositWaiverDetails: null,
                deposit: null,
                dueAtSigning: 500,
                remainingDueAtSigning: 500,
                previousResponseAdjustmentData: {
                  demoMilesAdjustment: null,
                  additionalMileageAdjustment: null,
                  lowOdometerAdjustment: null,
                  baseResidualValue: null,
                  baseResidualPercentage: 0,
                  additionalResidualAmount: null,
                },
                priorTradeBalance: 0,
                ruleWarningMap: {},
                programOverridden: false,
                subventionCostValueAbsolute: null,
                subventionCostValuePercentage: null,
                subventionCostOverridden: false,
                dueFromCustomerWithoutPaidCustomerCash: null,
                categoryBasedTotalRebates: null,
                taxStrategy: null,
                taxOutOfState: false,
                rebatesVerified: false,
              },
            ],
            programManuallyOverridden: false,
            selectedLender: 'CVB',
            selectedTier: null,
            selectedLenderId: '6205025fe3590f965ceb5167',
            paymentOption: {
              paymentType: 'LOAN',
              financingCode: null,
              paymentSubType: null,
              paymentFrequency: 'MONTHLY',
              value: 24,
              apr: null,
              rateType: null,
              useStandardProgram: false,
              programType: null,
              programId: null,
              yearlyMiles: 12000,
              lenderId: null,
              securityDeposit: null,
              downPayment: null,
              residualValue: null,
              residualDisplayType: null,
              fnIs: [],
              rebates: [],
              accessories: [],
              fees: [],
              daysToFirstPayment: null,
              useDefaultFees: true,
              useDefaultAccessories: true,
              useDefaultFNIs: true,
              creditScore: null,
              rank: 1,
              osfLender: false,
              useDefaultApr: true,
              columnSource: null,
              dealColumnTag: null,
              aprDetails: null,
              yearlyMilesDetails: null,
              residualDetails: null,
            },
            selectedLenderCode: null,
            accessories: [],
            fnIs: [],
            fnIsCount: 0,
            insuranceCount: 0,
            totalFnisPriceAmt: null,
            uniqueId: null,
            decisionUpdatedBy: null,
            columnSource: null,
            previousLenderId: null,
            selectedPackageId: null,
            leaseProgramAvailabilityStatus: null,
            programDesc: null,
            sppPaymentOption: null,
            optionContractValidityDate: null,
            financingProduct: null,
            contractNumber: null,
            preQualificationStatus: 'NOT_INITIATED',
            financeContractNumber: null,
            lenderChanged: false,
            exceptionFromCalc: null,
            ruleWarningMap: {},
            noOfInstallments: null,
            selectedByCustomer: false,
            financeContractStatusDto: null,
            securityDepositWaiverReason: null,
            osfLenderDetails: null,
            conciergeColumn: false,
            preferredLender: false,
            dealColumnTag: null,
            decisionDealPaymentId: null,
            contraSettlementsSupported: false,
            showMonthlyPaymentFlagMap: null,
            selectedLenderFetchCmsPrograms: false,
            fnIRatingColumnId: 'db8fd13f-ecb8-45ac-a493-54a74d719b6b',
          },
        ],
        selected: true,
      },
    ],
    status: 'FINANCE_AND_INSURANCE',
    previousStatus: 'QUOTE',
    activity: 'DEAL_CREATED',
    assignee: {
      salesPersons: [''],
      salesManager: [''],
      financeManager: [''],
      backOfficeClerk: [''],
      closingManager: [''],
      bdcUser: [''],
      agents: [''],
      intermediaries: [''],
      eadvisors: [''],
      salesPerson1: '',
      salesPerson2: '',
    },
    leadType: 'INTERNET',
    leadSource: 'CRM_UI',
    paymentTemplate: {
      paymentOptions: [
        {
          paymentType: 'LOAN',
          financingCode: null,
          paymentSubType: null,
          paymentFrequency: null,
          value: 24,
          apr: null,
          rateType: null,
          useStandardProgram: null,
          programType: null,
          programId: null,
          yearlyMiles: null,
          lenderId: null,
          securityDeposit: null,
          downPayment: null,
          residualValue: null,
          residualDisplayType: null,
          fnIs: null,
          rebates: null,
          accessories: null,
          fees: null,
          daysToFirstPayment: null,
          useDefaultFees: true,
          useDefaultAccessories: true,
          useDefaultFNIs: true,
          creditScore: null,
          rank: 1,
          osfLender: false,
          useDefaultApr: true,
          columnSource: null,
          dealColumnTag: null,
          aprDetails: null,
          yearlyMilesDetails: null,
          residualDetails: null,
        },
        {
          paymentType: 'LEASE',
          financingCode: null,
          paymentSubType: null,
          paymentFrequency: null,
          value: 36,
          apr: null,
          rateType: null,
          useStandardProgram: null,
          programType: null,
          programId: null,
          yearlyMiles: null,
          lenderId: null,
          securityDeposit: null,
          downPayment: null,
          residualValue: null,
          residualDisplayType: null,
          fnIs: null,
          rebates: null,
          accessories: null,
          fees: null,
          daysToFirstPayment: null,
          useDefaultFees: true,
          useDefaultAccessories: true,
          useDefaultFNIs: true,
          creditScore: null,
          rank: 1,
          osfLender: false,
          useDefaultApr: true,
          columnSource: null,
          dealColumnTag: null,
          aprDetails: null,
          yearlyMilesDetails: null,
          residualDetails: null,
        },
        {
          paymentType: 'CASH',
          financingCode: null,
          paymentSubType: null,
          paymentFrequency: null,
          value: 0,
          apr: null,
          rateType: null,
          useStandardProgram: null,
          programType: null,
          programId: null,
          yearlyMiles: null,
          lenderId: null,
          securityDeposit: null,
          downPayment: null,
          residualValue: null,
          residualDisplayType: null,
          fnIs: null,
          rebates: null,
          accessories: null,
          fees: null,
          daysToFirstPayment: null,
          useDefaultFees: true,
          useDefaultAccessories: true,
          useDefaultFNIs: true,
          creditScore: null,
          rank: 1,
          osfLender: false,
          useDefaultApr: true,
          columnSource: null,
          dealColumnTag: null,
          aprDetails: null,
          yearlyMilesDetails: null,
          residualDetails: null,
        },
        {
          paymentType: 'ONE_TIME_LEASE',
          financingCode: null,
          paymentSubType: null,
          paymentFrequency: null,
          value: 36,
          apr: null,
          rateType: null,
          useStandardProgram: null,
          programType: null,
          programId: null,
          yearlyMiles: null,
          lenderId: null,
          securityDeposit: null,
          downPayment: null,
          residualValue: null,
          residualDisplayType: null,
          fnIs: null,
          rebates: null,
          accessories: null,
          fees: null,
          daysToFirstPayment: null,
          useDefaultFees: true,
          useDefaultAccessories: true,
          useDefaultFNIs: true,
          creditScore: null,
          rank: 1,
          osfLender: false,
          useDefaultApr: true,
          columnSource: null,
          dealColumnTag: null,
          aprDetails: null,
          yearlyMilesDetails: null,
          residualDetails: null,
        },
        {
          paymentType: 'BALLOON',
          financingCode: null,
          paymentSubType: null,
          paymentFrequency: null,
          value: 24,
          apr: null,
          rateType: null,
          useStandardProgram: null,
          programType: null,
          programId: null,
          yearlyMiles: null,
          lenderId: null,
          securityDeposit: null,
          downPayment: null,
          residualValue: null,
          residualDisplayType: null,
          fnIs: null,
          rebates: null,
          accessories: null,
          fees: null,
          daysToFirstPayment: null,
          useDefaultFees: true,
          useDefaultAccessories: true,
          useDefaultFNIs: true,
          creditScore: null,
          rank: 1,
          osfLender: false,
          useDefaultApr: true,
          columnSource: null,
          dealColumnTag: null,
          aprDetails: null,
          yearlyMilesDetails: null,
          residualDetails: null,
        },
      ],
    },
    lastModifiedByUser: '595c5993-7300-48a1-828e-64c5582d6746',
    lastActivityDate: 1742462085116,
    updateLastActivityDate: true,
    tradeIns: [
      {
        lastModifiedBy: '595c5993-7300-48a1-828e-64c5582d6746',
        createdBy: '595c5993-7300-48a1-828e-64c5582d6746',
        deleted: false,
        createdTime: *************,
        modifiedTime: *************,
        id: '44a2fe42-8c1f-429e-8588-e0bd529f5652',
        tradeAllowance: 24850,
        tradePayOff: 1000,
        actualCashValue: 24850,
        tradeInVehicle: {
          createdTime: null,
          modifiedTime: null,
          deleted: false,
          vin: '1J4FY19S3VP480928',
          orderNumber: null,
          id: null,
          marketScanVehicleId: null,
          dealVehicleId: null,
          styleDetailList: null,
          dealerId: null,
          primaryVehicle: false,
          rfIdTag: null,
          stockID: null,
          year: 2019,
          make: 'Tesla',
          driveTrain: null,
          makeId: null,
          displayMake: null,
          model: 'Model 3',
          range: null,
          segment: null,
          displayModel: null,
          modelCode: null,
          modelType: null,
          mfrModelCode: null,
          titleInfo: null,
          modelDescription: null,
          exteriorColor: null,
          interiorColor: null,
          interiorColorCode: null,
          exteriorColorCode: null,
          manufacturingColorCode: null,
          languages: null,
          tradeInVehicleId: 'd-00051',
          previousStockID: null,
          customFields: {},
          status: null,
          tempTag: null,
          sourceInfo: null,
          vehicleType: 'USED',
          vehicleSubType: null,
          driveType: null,
          unladenWeight: null,
          unladenWeightUnit: null,
          grossWeight: null,
          grossWeightUnit: null,
          grossVehicleWeightRating: null,
          mileage: 2234,
          mileageType: null,
          mileageStatus: null,
          pricingDetails: null,
          costAdjustments: null,
          licensePlateNumber: null,
          roDetails: null,
          licensePlateExpirationDate: null,
          trimDetails: {
            trim: 'Sedan 4D Long Range Electric',
            engineCylinders: 0,
            axleCount: 0,
          },
          five64FuelType: null,
          five64BodyClass: null,
          vehicleAdditionalDetails: null,
          temp: false,
          certified: false,
          dealerCertified: false,
          certificationTier: null,
          bodyType: null,
          glAccountNumber: null,
          glAccountId: null,
          glAccountNumberAndName: null,
          entryTime: null,
          stockedInTime: null,
          leadId: null,
          vehicleSoldTime: null,
          viOptions: null,
          options: [],
          mileageForResidualCalculation: 0,
          inServiceDate: null,
          serviceVehicleId: null,
          locationCode: null,
          vinLookupResolved: false,
          lastEightVin: null,
          lastSixVin: null,
          lastFourVin: null,
          qualifiedForNewVehiclePrograms: false,
          auctionCar: false,
          demoVehicleIndicator: null,
          fleetVehicleIndicator: null,
          discounts: null,
          offers: null,
          exteriorColorDetail: null,
          selectedDiscounts: null,
          dealConfirmationTime: null,
          amtFinancedDuringPreviousBuy: null,
          monthlyPaymentDuringPreviousBuy: null,
          sellingPriceDuringPreviousBuy: null,
          stockedInAtSiteId: null,
          soldAtSiteId: null,
          vehicleSubStatus: null,
          parts: null,
          accessories: null,
          inFactoryWarranty: null,
          styleId: null,
          chromeSerializedValue: null,
          newTyreCount: null,
          previousStatus: null,
          vehicleDisplayImage: null,
          bestStyleName: null,
          agStatusCode: null,
          agStatus: null,
          productionTime: null,
          vgStatusCode: null,
          demoCode: null,
          additionalOEMInfo: null,
          locationDetails: null,
          demoDescription: null,
          seriesCode: null,
          seriesName: null,
          estimatedDeliveryDate: null,
          actualDeliveryDate: null,
          stopDeliveryIndicator: null,
          campaignCodes: null,
          preownedRegistration: null,
          valuationsData: null,
          features: null,
          invoiceDate: null,
          purchaseNumber: null,
          orderStatus: null,
          mada: null,
          madc: null,
          purchaseOrderDate: null,
          exteriorColorLocalization: null,
          interiorColorLocalization: null,
          modelLocalization: null,
          bodyTypeLocalization: null,
          provisionValue: null,
          fiscalHorsePower: null,
          firstRegistrationDate: null,
          previousUsage: null,
          isFirstHandOwner: null,
          optionsRetailPrice: null,
          transformationPrice: null,
          tradeInCommitment: null,
          termMileage: null,
          baseValue: null,
          dateOfFirstAvailability: null,
          emissionValue: null,
          vehiclePriceIncludingOptions: null,
          genre: null,
          reformeB: false,
          isVAT: null,
          vehicleTechnicalIdentifier: null,
          customerExpectedDate: null,
          deliveryDealer: null,
          batteryIndentificationNumber: null,
          priceListNumber: null,
          configUrl: null,
          warrantyDescription: null,
          vehicleCategory: null,
          buildVehicle: null,
          typeVariantVersion: null,
          stockOriginSource: null,
          wltpCo2: null,
          professionalUsage: false,
          isVATQualifying: null,
          thirdPartyProviders: null,
          originCountryCode: null,
          purchaseSource: null,
          certificationStatus: null,
          tempVIN: null,
          foreignRegistration: false,
          policeNumberInfo: null,
          weightType: null,
          useType: 'Personal',
          totalCostAdjustmentsAmount: null,
          totalROCostsAmount: null,
          vehicleKind: null,
          wheelPlan: null,
          isImported: null,
          nedcCo2: null,
          numberOfPreviousOwners: null,
          programId: null,
          hasSingleStyleId: false,
        },
        totalReconditioningCost: 0,
        customer: {
          buyerTypeNum: 0,
          sameAsDeal: false,
          mobileNo: '',
          secondTaxRate: 0,
          temp: false,
          guarantor: false,
          yearsInBusiness: 0,
          deleted: false,
          dayOfCollection: 0,
          natureOfOperation: 'LB',
        },
        dealerApprovalStatus: 'APPROVED',
        lienHolderDetails: {
          lastModifiedBy: '595c5993-7300-48a1-828e-64c5582d6746',
          createdBy: '595c5993-7300-48a1-828e-64c5582d6746',
          deleted: false,
          createdTime: *************,
          modifiedTime: *************,
          id: null,
          lienHolder: null,
          lienHolderAddress: null,
          lienHolderPhone: '',
          dealerId: null,
          tenantId: null,
          dealNumber: null,
          dailyPerDiem: null,
          lienAmount: 1000,
          loanNumber: null,
          expirationDate: null,
          lienHolderAccountNumber: null,
          lienAgreementNumber: null,
          lienAgreementType: null,
          contraSettlementsSupported: false,
          hpSettlementMediaId: null,
        },
        valuation: {
          provider: 'Dealer',
          valuationTime: 0,
          option: [],
          trim: 'Sedan 4D Long Range Electric',
          mileage: 2234,
          bodyStyle: 'Sedan 4D Long Range Electric',
        },
        vehiclePrimeConversion: false,
        requireIportalAndHpiCall: false,
        groundedVehicleLease: false,
        source: 'CONSUMER',
        tradeAllowanceUpdated: false,
        tradePayoffUpdated: false,
      },
    ],
    workingCashConfig: {
      negativeTradePayOffs: [
        {
          payOfftype: 'EXCESS_DOWNPAYMENT',
          enabled: false,
        },
        {
          payOfftype: 'EXCESS_REBATES',
          enabled: false,
        },
      ],
      leaseWorkingCashConfigs: [
        {
          leaseWorkingCashType: 'USE_TRADE_IN_FOR_CCR',
          enabled: true,
        },
        {
          leaseWorkingCashType: 'USE_REBATES_IN_FOR_CCR',
          enabled: true,
        },
        {
          leaseWorkingCashType: 'USE_DOWNPAYMENT_FOR_CCR',
          enabled: true,
        },
        {
          leaseWorkingCashType: 'CASH_ALWAYS_COVERS_DRIVEOFF',
          enabled: true,
        },
      ],
    },
    migrated: false,
    contractAge: {
      startTime: 1740645066710,
      endTime: 0,
    },
    quoteAge: {
      startTime: 1740569335962,
      endTime: 1740645066710,
      age: 0,
    },
    lastCalculatorUsed: 'GALAXY',
    leaseMaturityDate: 1834646400000,
    program: 'ACURA_NEW',
    calculationRequired: false,
    createdPostInvoiceDp: false,
    updatedFromConcierge: true,
    helmEventSent: true,
    lastPaymentUpdateTime: 0,
    reservationDeal: false,
    crmDetailsUpdated: [],
    showFIErrorMessageToster: false,
    paused: false,
    econtractPackageStatus: 'NOT_SUBMITTED',
  },
};

export const MOCK_APPROVAL_DATA = {};
