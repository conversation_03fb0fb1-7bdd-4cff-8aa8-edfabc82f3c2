/* eslint-disable import/order */
// Lodash

// Constants
import { ACCOUNTING } from '@tekion/tekion-base/constants/appServices';
import ROUTE_MODES from '@tekion/tekion-business/src/constants/routeModes';
import { JOURNAL_ENTRIES, VENDOR_INVOICES } from '@tekion/tekion-base/constants/appConfigs/accountingAppConfigs';
import { APPROVAL_WORKSPACE } from '@tekion/tekion-base/constants/appConfigs/coreAppConfigs';

// Helpers
import approvalRequestReader from '../../readers/approvalRequest.reader';
import getRoute from '@tekion/tekion-business/src/factories/route';

export const getVendorInvoiceDetailUrl = approvalRequest => {
  const dealerId = approvalRequestReader.dealerId(approvalRequest);
  const invoiceId = approvalRequestReader.requestIdentifier(approvalRequest);

  const route = getRoute(ACCOUNTING, VENDOR_INVOICES.getKey(), {
    mode: ROUTE_MODES.VIEW,
    invoiceId,
    dealerId,
    pageSource: APPROVAL_WORKSPACE.getKey(),
  });

  return route;
};

export const getManualJournalEntryUrl = approvalRequest => {
  // TODO: if BE handle this for all JE's then it can be removed from UI
  const requestIdentifier = approvalRequestReader.requestIdentifier(approvalRequest);
  const dealerId = approvalRequestReader.dealerId(approvalRequest);
  const route = getRoute(ACCOUNTING, JOURNAL_ENTRIES.getKey(), {
    mode: ROUTE_MODES.VIEW,
    transactionId: requestIdentifier,
    dealerId,
  });
  return route;
};
