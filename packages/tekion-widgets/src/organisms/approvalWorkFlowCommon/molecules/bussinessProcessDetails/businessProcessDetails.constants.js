import { getVendorInvoiceDetailUrl, getManualJournalEntryUrl } from './businessProcessDetails.helpers';
import { BUSSINESS_PROCESS_IDS } from '../../constants/approvalWorkspace.general.constants';

export const BUSINESS_PROCESS_VS_LABEL = {
  PARTS_INVENTORY_ON_HAND_ADJUSTMENT: __('Parts Inventory On-Hand Adjustment'),
  PO_SUBMISSION: __('PO Submission'),
  VEHICLE_TRANSFER: __('Vehicle Transfer'),
  TRADEIN_APPROVAL: __('Customer Trade-In Approval'),
  REBATES_APPROVAL: __('Customer Rebates Approval'),
};

export const BUSINESS_PROCESS_VS_URL = {
  // please add the url here to update businessprocess vs function to create your url
  [BUSSINESS_PROCESS_IDS.VENDOR_INVOICE]: getVendorInvoiceDetailUrl,
  [BUSSINESS_PROCESS_IDS.MANUAL_JOURNAL_ENTRY]: getManualJournalEntryUrl,
};
