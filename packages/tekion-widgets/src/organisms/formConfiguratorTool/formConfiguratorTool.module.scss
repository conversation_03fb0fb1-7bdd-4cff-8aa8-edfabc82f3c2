@import "tstyles/component.scss";

.leftPaneWrapper {
  min-width: 20%;
  @include full-height;
  border-right: 1px solid $platinum;
  max-width: 35rem;
}

.rightPaneWrapper {
  min-width: 20%;
  @include full-height;
  border-right: 1px solid $platinum;
  max-width: 35rem;
}

.bodyWrapper {
  @include flex();
  @include full-width;
}

.middleContainer {
  flex-grow: 1;
  border-right: 1px solid $platinum;
  padding: 2rem;
}

.wrapper {
  @include full-width;
  @include full-height;
}

.headerActionsWrapper {
  @include flex();
}

.headerAction {
  @include flex($align-items: center);
  margin-right: 2rem;

  &:last-of-type {
    margin-right: 0rem;
  }
}

.headerActionText {
  margin-left: 1rem;
  color: $denim;
}

.formInfo {
  @include flex();
}

.headerWrapper {
  @include full-width;
  @include flex($justify-content: space-between, $align-items: center);
}

.statusItem {
  width: fit-content;
  margin-left: 2rem;
}

.footerToggle {
  margin-left: auto;
  margin-right: auto;
}

.button-container {
  position: relative;
}

.footerToggleWithAction {
  flex-grow: 1;
  @include flex($justify-content: flex-end, $align-items: center);
}

.additionalFooterButton {
  right: 1.6rem;
}

.setupScreenWrapper {
  @include full-width;
  @include full-height;
}

.setupFormWrapper {
  @include full-height;
  overflow-y: scroll;
  @include flex();
}

.documentWrapper {
  width: 50%;
}

.formWrapper {
  width: 50%;
  padding-top: 1.6rem;
}

.savePromptTitle {
  font-weight: bold;
  margin-bottom: 0;
}

.validateForm div[class*="fieldLabel_label"] {
  white-space: unset;
}

.leftPaneWrapperViewOnly {
  opacity: 0.5;
  cursor: not-allowed;
  & > div {
    pointer-events: none;
  }
}

.viewOnlyModeHeader {
  background-color: rgba($color: $carrotOrange, $alpha: 0.4);
  border-radius: 2rem;
  border: 0.1rem solid $carrotOrange;
  font-size: $font-size-small;
  padding: 0.6rem 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 1rem;
}

.infoIconClassName {
  color: $dodgerBlueLight;
  margin-right: 1.2rem;
}

.iconWithTextWrapper {
  padding: 1.2rem 2.2rem 1.2rem 1.6rem;
  background-color: $aliceBlue;
  border-radius: 0.4rem;
  color: $atomic;
}

.inputBoxErrorClass {
  top: auto;
}

.headerKebabMenu {
  margin-left: 2rem;
  align-self: center;
}

.headerActionsButtonIcon {
  color: $accentText;
  margin-right: 0.8rem;
}

.headerActionsButtonTextStyle {
  color: $accentText;
}

.headerActionsButton {
  display: flex;
  align-items: center;
}

.headerActionsButtonDisabled {
  color: $black;
  opacity: 0.4;
}
