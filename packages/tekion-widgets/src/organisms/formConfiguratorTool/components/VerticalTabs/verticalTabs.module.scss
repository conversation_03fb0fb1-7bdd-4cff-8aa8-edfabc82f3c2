@import "tstyles/component.scss";

.setupWrapper {
  @include full-width;
  @include full-height;
}

.verticalTabsPage {
  background-color: $white !important;
  height: 100%;
  display: flex;
}

:global([class*="verticalTabsPage"] .ant-tabs-nav-wrap) {
  background: $white;
  padding: 2rem 2rem 0 0;
}

:global([class*="verticalTabsPage"] .ant-tabs-ink-bar) {
  left: 0;
  top: 1.2rem !important;
  bottom: 1.2rem !important;
  height: 2.8rem !important;
  width: 0.4rem;
  margin-left: -0.1rem;
  border-radius: 0.4rem;
  background-color: $denim !important;
}

:global([class*="verticalTabsPage"] .ant-tabs-tab) {
  font-size: 1.6rem;
  line-height: 2rem;
  letter-spacing: 0.03rem;
  width: 18rem;
  text-align: left;
  background: $white;
  border-left: $border;
}

:global([class*="verticalTabsPage"] .ant-tabs-nav .ant-tabs-tab) {
  padding: 1.2rem 2.4rem;
  margin: 0;
}

:global([class*="verticalTabsPage"] .ant-tabs-nav .ant-tabs-tab-active) {
  color: $denim;
  border-left: 0.4rem solid $denim;
  border-radius: 0.2rem;
}
