import React from 'react';
import cx from 'classnames';

import PropTypes from 'prop-types';

import Tabs from '@tekion/tekion-components/src/molecules/Tabs/ScrollSpy';

import styles from './verticalTabs.module.scss';

const TAB_POSITION = 'left';

const VerticalTabs = ({ className, tabs, ...rest }) => (
  <div className={styles.setupWrapper}>
    <Tabs tabPosition={TAB_POSITION} className={className || cx(styles.verticalTabsPage, 'text', 'content')} {...rest}>
      {tabs}
    </Tabs>
  </div>
);

VerticalTabs.propTypes = {
  children: PropTypes.element,
};

VerticalTabs.defaultProps = {
  children: null,
};

VerticalTabs.TabPane = Tabs.TabPane;

export default VerticalTabs;
