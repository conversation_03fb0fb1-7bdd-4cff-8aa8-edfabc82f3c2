import styles from './textFormatting.module.scss';

export const TEXT_FORMATTING_FIELDS = {
  FONT_FAMILY: 'font',
  FONT_SIZE: 'size',
  FONT_COLOR: 'color',
  FONT_STYLE: 'fontStyles',
  TEXT_ADJUSTMENTS: 'textAdjustments',
  TEXT_ALIGNMENT: 'textAlignment',
  TEXT_COMBING: 'textCombing',
  HEADER_WITH_INFO_POPOVER: 'HEADER_WITH_INFO_POPOVER',
  HEADER_WITH_TEXT_COMBING_POPOVER: 'HEADER_WITH_TEXT_COMBING_POPOVER',
  ORIENTATION: 'orientation',
  FIELD_VISIBILITY: 'visibility',
  CHARACTER_LIMIT: 'maxLength',
  LIMIT_OPTIONS: 'limitOption',
};

export const TEXT_ADJUSMENT_TYPES = {
  AUTO_SIZE: 'AUTO_SIZE',
  MULTI_LINE: 'MULTI_LINE',
};

export const TEXT_ALIGNMENT_TYPES = {
  LEFT_ALIGNED: 'LEFT_ALIGNED',
  CENTER_ALIGNED: 'CENTER_ALIGNED',
  RIGHT_ALIGNED: 'RIGHT_ALIGNED',
};

export const LIMIT_TYPES = {
  LIMIT: 'LIMIT',
  COMB: 'COMB',
};

export const FONT_STYLE_OPTIONS = [
  { label: 'Bold', value: 'bold', checkboxItemClassName: styles.bold },
  { label: 'Italics', value: 'italics', checkboxItemClassName: styles.italics },
  { label: 'Underline', value: 'underline', checkboxItemClassName: styles.underline },
  { label: 'Strike Through', value: 'strikeThrough', checkboxItemClassName: styles.strikeThrough },
];

export const TEXT_ADJUSMENT_OPTIONS = [
  {
    label: __('Auto Size'),
    value: TEXT_ADJUSMENT_TYPES.AUTO_SIZE,
    helpText: __('Font size will shrink / increase based on the text length and annotation size'),
  },
  {
    label: __('Multi-Line'),
    value: TEXT_ADJUSMENT_TYPES.MULTI_LINE,
    helpText: __('Allow text overflow to next line'),
  },
];

export const ORIENTATION_TYPES = {
  ZERO: 0,
  DEGREE_90: 90,
  DEGREE_180: 180,
  DEGREE_270: 270,
};

export const ORIENTATION_OPTIONS = [
  {
    label: __('0 degrees'),
    value: ORIENTATION_TYPES.ZERO,
  },
  {
    label: __('90 degrees'),
    value: ORIENTATION_TYPES.DEGREE_90,
  },
  {
    label: __('180 degrees'),
    value: ORIENTATION_TYPES.DEGREE_180,
  },
  {
    label: __('270 degree'),
    value: ORIENTATION_TYPES.DEGREE_270,
  },
];

export const TEXT_ALIGNMENT_OPTIONS = [
  { label: __('Left'), value: TEXT_ALIGNMENT_TYPES.LEFT_ALIGNED },
  { label: __('Center'), value: TEXT_ALIGNMENT_TYPES.CENTER_ALIGNED },
  { label: __('Right'), value: TEXT_ALIGNMENT_TYPES.RIGHT_ALIGNED },
];

export const LIMIT_OPTIONS = [
  { label: __('Limit'), value: LIMIT_TYPES.LIMIT },
  { label: __('Text Comb'), value: LIMIT_TYPES.COMB },
];

export const LIMIT_OPTIONS_LITE = [{ label: __('Limit'), value: LIMIT_TYPES.LIMIT }];

export const TEXT_FORMATTING_DESCRIPTION = __("Define Configuration Field's Font Size, Alignments & Adjustments");

export const FONT_FAMILY_TYPES = {
  COURIER: 'COURIER',
  HELVETICA: 'HELVETICA',
};

export const FONT_FAMILY_TYPES_DISPLAY_NAME = {
  [FONT_FAMILY_TYPES.COURIER]: __('Courier'),
  [FONT_FAMILY_TYPES.HELVETICA]: __('Helvetica'),
};

export const TEXT_FORMATTING_FONT_SIZES = [6, 8, 9, 10, 12, 14, 18];
