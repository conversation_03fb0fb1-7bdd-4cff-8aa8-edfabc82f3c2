import React, { useCallback } from 'react';
import PropTypes from 'prop-types';

import _noop from 'lodash/noop';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import FormPage from '@tekion/tekion-components/src/organisms/FormBuilder';
import actionTypes from '@tekion/tekion-components/src/organisms/FormBuilder/constants/actionTypes';
import { FORM_PRINTER_TYPES } from '@tekion/tekion-base/constants/formConfigurator/printers';
import { getFormFields, getFormSections } from './TextFormatting.config';
function TextFormatting(props) {
  const { updateFormValues, values, modalType, configuratorMetaData, formPrinterType, isFcLiteModeEnabled } = props;

  const onTextFormattingChange = useCallback(
    ({ payload: { id, value }, type }) => {
      if (type === actionTypes.ON_FIELD_CHANGE) {
        updateFormValues({ id, value });
      }
    },
    [updateFormValues]
  );

  return (
    <div className="full-width">
      <FormPage
        sections={getFormSections(modalType, isFcLiteModeEnabled)}
        fields={getFormFields(configuratorMetaData, formPrinterType, values, isFcLiteModeEnabled)}
        values={values}
        headerComponent={null}
        footerComponent={null}
        onAction={onTextFormattingChange}
        className="full-width"
      />
    </div>
  );
}

TextFormatting.propTypes = {
  values: PropTypes.object,
  configuratorMetaData: PropTypes.object,
  updateFormValues: PropTypes.func,
  modalType: PropTypes.string.isRequired,
  formPrinterType: PropTypes.string,
  isFcLiteModeEnabled: PropTypes.bool,
};

TextFormatting.defaultProps = {
  values: EMPTY_OBJECT,
  configuratorMetaData: EMPTY_OBJECT,
  updateFormValues: _noop,
  formPrinterType: FORM_PRINTER_TYPES.IMPACT,
  isFcLiteModeEnabled: false,
};

export default TextFormatting;
