import cx from 'classnames';

import _includes from 'lodash/includes';
import _map from 'lodash/map';
import _get from 'lodash/get';

import { EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';

import RadioGroupInput from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/radio';

import NumberInput from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/numberInputField';
import TextInput from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/textInput';
import Select from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/select';
import { formatPhone } from '@tekion/tekion-base/formatters/number';
import CheckboxGroupRenderer from '../fieldRenderers/checkboxGroup';
import TextCombingPopover from './components/TextCombingPopover';
import LimitCombSelect from './components/limitCombSelect';
import {
  TEXT_ADJUSMENT_OPTIONS,
  TEXT_ALIGNMENT_OPTIONS,
  TEXT_FORMATTING_FONT_SIZES,
  FONT_STYLE_OPTIONS,
  TEXT_FORMATTING_FIELDS as FORM_FIELDS,
  ORIENTATION_OPTIONS,
  TEXT_ADJUSMENT_TYPES,
  LIMIT_OPTIONS,
  LIMIT_TYPES,
  LIMIT_OPTIONS_LITE,
} from './TextFormatting.constants';
import {
  ANNOTATION_MODAL_TYPES,
  DEFAULT_TEXT_COLOR,
  PRINTER_TYPE_VS_FONTS_OPTIONS,
} from '../../FormConfiguratorTool.constants';

import styles from './textFormatting.module.scss';
import HeaderWithInfoPopover from '../HeaderWithInfoPopover';

const getOptionsForRotation = formFieldVisibilityOptions => {
  const options = _map(formFieldVisibilityOptions, option => ({
    label: __(`${option.displayName}`),
    value: option.value,
  }));
  return options;
};

const getOptionsForFontSizes = () => {
  const options = _map(TEXT_FORMATTING_FONT_SIZES, value => ({ label: value, value }));
  return options;
};

export const FORM_FIELD_FORMATTERS = {
  [FORM_FIELDS.PHONE_NUMBER]: formatPhone,
};

const getTextStyleFields = modalType => {
  const showTextDecorationFields = _includes(
    [
      ANNOTATION_MODAL_TYPES.GLOBAL_FIELD,
      ANNOTATION_MODAL_TYPES.CUSTOM_FIELD,
      ANNOTATION_MODAL_TYPES.CONFIGURATION_FIELD,
    ],
    modalType
  );

  if (showTextDecorationFields) {
    return [
      {
        columns: [FORM_FIELDS.FONT_FAMILY, FORM_FIELDS.FONT_SIZE],
      },
      {
        columns: [FORM_FIELDS.FONT_COLOR],
      },
      {
        columns: [FORM_FIELDS.TEXT_ADJUSTMENTS, FORM_FIELDS.TEXT_ALIGNMENT],
      },
    ];
  }
  return [
    {
      columns: [FORM_FIELDS.FONT_FAMILY, FORM_FIELDS.FONT_SIZE],
    },
    {
      columns: [FORM_FIELDS.TEXT_ADJUSTMENTS, FORM_FIELDS.TEXT_ALIGNMENT],
    },
  ];
};

export const getFormSections = (modalType, isFcLiteModeEnabled) => [
  {
    className: styles.sectionWrapper,
    header: { label: __('Text Formatting'), className: styles.formSectionHeader, size: 3 },
    subHeader: {
      label: __('Define Configuration Field’s Font Size, Alignments & Adjustments'),
      className: styles.formSectionSubHeader,
    },
    rows: [],
  },
  {
    className: styles.sectionWrapper,
    header: {
      label: __('Text Styles'),
      size: 5,
    },
    rows: getTextStyleFields(modalType),
  },
  {
    className: styles.sectionWrapper,
    rows: isFcLiteModeEnabled
      ? [
          {
            columns: [FORM_FIELDS.LIMIT_OPTIONS, FORM_FIELDS.CHARACTER_LIMIT],
          },
        ]
      : [
          {
            columns: [FORM_FIELDS.LIMIT_OPTIONS],
          },
          {
            columns: [FORM_FIELDS.CHARACTER_LIMIT, FORM_FIELDS.TEXT_COMBING],
          },
        ],
  },
  ...(isFcLiteModeEnabled
    ? EMPTY_ARRAY
    : [
        {
          className: styles.sectionWrapper,
          rows: [
            {
              columns: [FORM_FIELDS.FIELD_VISIBILITY, FORM_FIELDS.ORIENTATION],
            },
          ],
        },
      ]),
];

export const getFormFields = (configuratorMetaData, printerType, formValues, isFcLiteModeEnabled) => ({
  [FORM_FIELDS.FONT_FAMILY]: {
    renderer: Select,
    renderOptions: {
      label: __('Font'),
      options: PRINTER_TYPE_VS_FONTS_OPTIONS[printerType],
    },
  },
  [FORM_FIELDS.FONT_SIZE]: {
    renderer: Select,
    renderOptions: {
      label: __('Font Size'),
      options: getOptionsForFontSizes(),
      disabled: _includes(_get(formValues, 'textAdjustments'), TEXT_ADJUSMENT_TYPES.AUTO_SIZE),
    },
  },
  [FORM_FIELDS.FONT_COLOR]: {
    renderer: TextInput,
    renderOptions: {
      label: __('Font Color'),
      placeholder: DEFAULT_TEXT_COLOR,
      fieldClassName: styles.halfField,
      disabled: true,
    },
  },
  [FORM_FIELDS.FONT_STYLE]: {
    renderer: CheckboxGroupRenderer,
    renderOptions: {
      label: __('Font Style'),
      options: FONT_STYLE_OPTIONS,
      fieldClassName: cx(styles.checkboxGroupWrapper, styles.textFormattingField),
    },
  },
  [FORM_FIELDS.TEXT_ADJUSTMENTS]: {
    renderer: CheckboxGroupRenderer,
    renderOptions: {
      label: __('Text Adjustments'),
      options: TEXT_ADJUSMENT_OPTIONS,
      fieldClassName: cx(styles.checkboxGroupWrapper, styles.textFormattingField),
    },
  },
  [FORM_FIELDS.TEXT_ALIGNMENT]: {
    renderer: RadioGroupInput,
    renderOptions: {
      label: __('Text Alignments'),
      radios: TEXT_ALIGNMENT_OPTIONS,
    },
  },
  [FORM_FIELDS.HEADER_WITH_INFO_POPOVER]: {
    renderer: HeaderWithInfoPopover,
    renderOptions: {
      headingSize: 4,
      headerLabel: __('Text Combing'),
      helpText: __('Combing content goes here'),
    },
  },
  [FORM_FIELDS.CHARACTER_LIMIT]: {
    renderer: NumberInput,
    renderOptions: {
      label: __('Limit'),
      fieldClassName: styles.halfField,
      shouldDisabledStepper: true,
      disabled: _get(formValues, 'limitOption') === LIMIT_TYPES.COMB || !_get(formValues, 'limitOption'),
    },
  },
  [FORM_FIELDS.LIMIT_OPTIONS]: {
    renderer: LimitCombSelect,
    renderOptions: {
      radios: isFcLiteModeEnabled ? LIMIT_OPTIONS_LITE : LIMIT_OPTIONS,
      headingSize: 1.4,
      headerLabel: __(isFcLiteModeEnabled ? 'Limit Properties' : 'Limit and Comb Properties'),
      helpText: __(
        'Text Comb and limit restrict total number of characters used. Text comb additionally separates the annotation box for each character'
      ),
      renderCustomHelpText: TextCombingPopover,
      isRadio: !isFcLiteModeEnabled,
    },
  },
  [FORM_FIELDS.HEADER_WITH_TEXT_COMBING_POPOVER]: {
    renderer: HeaderWithInfoPopover,
    renderOptions: {
      headingSize: 1.4,
      headerLabel: __('Limit and Comb Properties'),
      helpText: __(
        'Text Comb and limit restrict total number of characters used. Text comb additionally separates the annotation box for each character'
      ),
      renderCustomHelpText: TextCombingPopover,
    },
  },
  [FORM_FIELDS.TEXT_COMBING]: {
    renderer: NumberInput,
    renderOptions: {
      label: __('No. of Comb Characters'),
      size: 6,
      min: 0,
      precision: 0,
      triggerChangeOnBlur: false,
      shouldDisabledStepper: true,
      disabled: _get(formValues, 'limitOption') === LIMIT_TYPES.LIMIT || !_get(formValues, 'limitOption'),
    },
  },
  [FORM_FIELDS.FIELD_VISIBILITY]: {
    renderer: Select,
    renderOptions: {
      label: __('Visibility'),
      options: getOptionsForRotation(_get(configuratorMetaData, 'formFieldVisibility')),
    },
  },
  [FORM_FIELDS.ORIENTATION]: {
    renderer: Select,
    renderOptions: {
      label: __('Orientation'),
      options: ORIENTATION_OPTIONS,
    },
  },
});
