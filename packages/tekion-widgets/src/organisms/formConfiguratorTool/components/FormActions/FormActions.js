import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';

// Lodash
import _noop from 'lodash/noop';

// Constants
import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

// Components
import Button from '@tekion/tekion-components/src/atoms/Button';
import Content from '@tekion/tekion-components/src/atoms/Content';
import FontIcon, { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';
import Tooltip, { TOOLTIP_PLACEMENT } from '@tekion/tekion-components/src/atoms/tooltip';
import AlignFieldsPopover from '../AlignFieldsPopover';
import MatchFieldPopover from '../MatchFieldPopover';

// Styles
import styles from './formActions.module.scss';

// Utils
import { allowGroupSelection, multipleFieldsSelected } from '../../FormConfiguratorTool.utils';

const FormActions = ({
  onAlign,
  onMatch,
  annotatedFields,
  selectedFields,
  toggleGroupSelectedAnnotations,
  isAnnotationFieldType,
  onClickEditProperties,
  disableEditProperies,
  hideEditPropertiesButton,
  disableBulkDelete,
  onPrintAlignClick,
  hidePrinterAlignment,
  canPrintAlign,
  deleteFieldsInBulk,
  onSingleAnnotationCopy,
  groupSelectedAnnotations,
  disableFieldActions,
  onDeleteAnnotation,
  isFcLiteModeEnabled,
  bulkDuplicateFields,
}) => {
  const onDelete = () => {
    if (isAnnotationFieldType) {
      onDeleteAnnotation();
    } else {
      deleteFieldsInBulk();
    }
  };

  const onCopy = () => {
    if (isAnnotationFieldType) {
      onSingleAnnotationCopy();
    } else {
      bulkDuplicateFields();
    }
  };

  const deleteButtonDisabled = useMemo(() => {
    if (isAnnotationFieldType) {
      return disableFieldActions;
    }

    return !multipleFieldsSelected(annotatedFields, selectedFields) || disableBulkDelete;
  }, [isAnnotationFieldType, disableFieldActions, annotatedFields, selectedFields, disableBulkDelete]);

  return (
    <div
      className={cx(
        'd-flex',
        {
          'justify-content-center': !isAnnotationFieldType,
        },
        styles.formActionsContainer
      )}>
      {!hideEditPropertiesButton && (
        <>
          <Button
            view={Button.VIEW.TERTIARY}
            onClick={onClickEditProperties}
            className={cx('d-flex align-items-center', styles.editPropertiesButton)}
            disabledWrapperClassname="d-flex"
            disabled={disableEditProperies}>
            <FontIcon
              size={SIZES.S}
              className={cx('m-r-8', styles.editPropertiesIcon, {
                [styles.disabled]: disableEditProperies,
              })}>
              icon-edit
            </FontIcon>
            <Content
              className={cx(styles.propertiesTextStyle, {
                [styles.disabled]: disableEditProperies,
              })}>
              {__('Edit Properties')}
            </Content>
          </Button>
          <div className="m-r-4"></div>
        </>
      )}
      <AlignFieldsPopover onClick={onAlign} canAlign={multipleFieldsSelected(annotatedFields, selectedFields)} />
      <div className="m-r-4"></div>
      <MatchFieldPopover onClick={onMatch} canMatch={multipleFieldsSelected(annotatedFields, selectedFields)} />
      <div className={styles.verticalSeparator}></div>
      <Tooltip placement={TOOLTIP_PLACEMENT.TOP} title={__(groupSelectedAnnotations ? 'Ungroup' : 'Group')}>
        <Button
          view={Button.VIEW.TERTIARY}
          disabledWrapperClassname="d-flex"
          onClick={toggleGroupSelectedAnnotations}
          disabled={!allowGroupSelection(annotatedFields, selectedFields)}
          className={cx(styles.groupButton, { [styles.groupButtonActive]: groupSelectedAnnotations })}>
          <FontIcon
            size={SIZES.MD}
            className={cx(styles.groupIcon, {
              [styles.disabled]: !allowGroupSelection(annotatedFields, selectedFields),
            })}>
            icon-group
          </FontIcon>
        </Button>
      </Tooltip>
      {!isFcLiteModeEnabled && (
        <>
          <Tooltip placement={TOOLTIP_PLACEMENT.TOP} title={__('Duplicate')}>
            <Button
              view={Button.VIEW.TERTIARY}
              disabledWrapperClassname="d-flex"
              onClick={onCopy}
              disabled={
                !isAnnotationFieldType ? !multipleFieldsSelected(annotatedFields, selectedFields) : disableFieldActions
              }
              className={styles.groupButton}>
              <FontIcon
                size={SIZES.MD}
                className={cx(styles.groupIcon, {
                  [styles.disabled]: !isAnnotationFieldType
                    ? !multipleFieldsSelected(annotatedFields, selectedFields)
                    : disableFieldActions,
                })}>
                icon-copy2
              </FontIcon>
            </Button>
          </Tooltip>

          <Tooltip
            placement={TOOLTIP_PLACEMENT.TOP}
            title={__(isAnnotationFieldType ? 'Delete' : 'Bulk Delete Fields')}>
            <Button
              view={Button.VIEW.TERTIARY}
              disabledWrapperClassname="d-flex"
              onClick={onDelete}
              disabled={deleteButtonDisabled}
              className={styles.groupButton}>
              <FontIcon
                size={SIZES.MD}
                className={cx(styles.groupIcon, {
                  [styles.disabled]: deleteButtonDisabled,
                })}>
                icon-trash
              </FontIcon>
            </Button>
          </Tooltip>

          {!hidePrinterAlignment && (
            <Tooltip placement={TOOLTIP_PLACEMENT.TOP} title={__('Print Alignment')}>
              <Button
                view={Button.VIEW.TERTIARY}
                disabledWrapperClassname="d-flex"
                onClick={onPrintAlignClick}
                disabled={!canPrintAlign}
                className={styles.groupButton}>
                <FontIcon
                  size={SIZES.MD}
                  className={cx(styles.groupIcon, {
                    [styles.disabled]: !canPrintAlign,
                  })}>
                  icon-print-failed
                </FontIcon>
              </Button>
            </Tooltip>
          )}
        </>
      )}
    </div>
  );
};

FormActions.propTypes = {
  onAlign: PropTypes.func,
  onMatch: PropTypes.func,
  annotatedFields: PropTypes.object,
  selectedFields: PropTypes.array,
  toggleGroupSelectedAnnotations: PropTypes.func,
  setShowTooltip: PropTypes.func,
  onClickEditProperties: PropTypes.func,
  disableEditProperies: PropTypes.bool,
  hideEditPropertiesButton: PropTypes.bool,
  deleteFieldsInBulk: PropTypes.func,
  canPrintAlign: PropTypes.bool,
  onPrintAlignClick: PropTypes.func,
  hidePrinterAlignment: PropTypes.bool,
  disableBulkDelete: PropTypes.bool,
  isAnnotationFieldType: PropTypes.bool,
  onSingleAnnotationCopy: PropTypes.func,
  groupSelectedAnnotations: PropTypes.bool,
  disableFieldActions: PropTypes.bool,
  onDeleteAnnotation: PropTypes.func,
  bulkDuplicateFields: PropTypes.func,
};

FormActions.defaultProps = {
  onAlign: _noop,
  onMatch: _noop,
  annotatedFields: EMPTY_OBJECT,
  selectedFields: EMPTY_ARRAY,
  toggleGroupSelectedAnnotations: _noop,
  setShowTooltip: _noop,
  onClickEditProperties: _noop,
  disableEditProperies: false,
  hideEditPropertiesButton: false,
  deleteFieldsInBulk: _noop,
  canPrintAlign: false,
  onPrintAlignClick: _noop,
  hidePrinterAlignment: false,
  disableBulkDelete: false,
  isAnnotationFieldType: false,
  onSingleAnnotationCopy: _noop,
  groupSelectedAnnotations: false,
  disableFieldActions: false,
  onDeleteAnnotation: _noop,
  bulkDuplicateFields: _noop,
};

export default FormActions;
