@import "tstyles/component.scss";

.formActionsContainer {
  padding: 0.4rem 0.8rem;
  height: 4rem;
  border-bottom: 0.1rem solid $platinum;
}

.verticalSeparator {
  border-left: 0.1rem solid $platinum;
  margin: 0 0.4rem;
}

.disabled {
  color: $black;
  opacity: 0.4;
}

.editPropertiesIcon {
  color: $black;
}

.propertiesTextStyle {
  font-weight: normal;
}

.editPropertiesButton {
  &:hover {
    .editPropertiesIcon,
    .propertiesTextStyle {
      color: $azure;
    }
  }
}

.propertiesTextStyle {
  font-weight: normal;
  &:hover {
    color: $tertiary-button-hover-text;
  }
}

.tooltipStyle {
  background-color: $white;
}

.groupIcon {
  color: $black;
}

.groupButton {
  &:hover {
    .groupIcon {
      color: $azure;
    }
  }
}

.groupButtonActive {
  background: $marbleDayLight;
  border-radius: 0.4rem;
  .groupIcon {
    color: $azure;
  }
}
