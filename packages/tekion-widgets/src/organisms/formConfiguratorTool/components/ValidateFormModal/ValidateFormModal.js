import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import _get from 'lodash/get';
import _map from 'lodash/map';
import _isEmpty from 'lodash/isEmpty';
import _filter from 'lodash/filter';
import _castArray from 'lodash/castArray';
import produce from 'immer';
import _noop from 'lodash/noop';
import _debounce from 'lodash/debounce';

import { EMPTY_OBJECT, EMPTY_ARRAY, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { PAGER, ZOOM_OUT, ZOOM_IN } from '@tekion/tekion-base/constants/pspdfkit';
import { DATE_TIME_FORMATS } from '@tekion/tekion-base/utils/dateUtils';
import { capitalizeFirstLetters } from '@tekion/tekion-base/formatters/string';

import Select from '@tekion/tekion-components/src/molecules/Select';
import Radio from '@tekion/tekion-components/src/molecules/Radio';
import TextInput from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/textInput';
import Input from '@tekion/tekion-components/src/molecules/Input';
import actionTypes from '@tekion/tekion-components/src/organisms/FormBuilder/constants/actionTypes';
import Heading from '@tekion/tekion-components/src/atoms/Heading';
import FormBuilder from '@tekion/tekion-components/src/organisms/FormBuilder';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import PdfAnnotations from '@tekion/tekion-components/src/organisms/PdfAnnotations';
import { uuid } from '@tekion/tekion-components/src/utils';
import Drawer, { PLACEMENTS } from '@tekion/tekion-components/src/molecules/drawer';
import IconAsBtn from '@tekion/tekion-components/src/atoms/iconAsBtn';
import Button from '@tekion/tekion-components/src/atoms/Button';
import ButtonWithIconAndLabel from '@tekion/tekion-widgets/src/molecules/buttonWithIconAndLabel';
import Content from '@tekion/tekion-components/src/atoms/Content';

import noPDFData from 'assets/images/noPDFData.svg';

import { getArrayLength } from '../../utils';

import { getDealerNameOptions, getPdfKeyOfOptionByValue } from '../../FormConfiguratorTool.utils';
import {
  FIELD_TYPES_VS_RENDERERS,
  FIELD_TYPES_REQUIRING_ROOT_PDF_KEY_CHANGE,
  FIELD_TYPE_VALUES,
} from '../../FormConfiguratorTool.constants';
import {
  VALIDATE_PREVIEW_BY,
  VALIDATE_PREVIEW_BY_LABELS,
  VALIDATE_PREVIEW_BY_OPTIONS,
} from './validateFormModal.constants';
import styles from './validateFormModal.module.scss';
import { getValidateFormInputRows } from '../../utils/validateForm.utils';
import { getFormSectionsForPreview } from '../../../../utils/formsPreview';

const BODY_STYLE = {
  padding: 0,
};

class ValidateFormModal extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      formValues: {},
      formErrors: {},
      focusedField: '',
      highlightFormField: '',
      validatePreviewId: '',
      validatePreviewBy: VALIDATE_PREVIEW_BY.DEAL,
      pdfInstance: {},
    };
  }

  componentDidMount() {
    this.setState({
      requestId: uuid(),
    });
  }

  changeValidatePreviewId = e => this.setState({ validatePreviewId: _get(e, 'target.value') });

  onFormValuesChange = ({ type, payload = {} }) => {
    const { id: key, value } = payload;
    if (type === actionTypes.ON_FIELD_CHANGE) {
      this.setState(prevState =>
        produce(prevState, draftState => {
          draftState.formValues = { ...draftState.formValues, [key]: value };
        })
      );
      this.setFocusedField({ key });
    } else if (type === actionTypes.VALIDATION_SUCCESS) {
      this.setState({
        formErrors: payload.errors,
      });
    }
  };

  setFocusedField = ({ key }) => {
    this.setState({ focusedField: key });
  };

  onFieldFocus = formFieldName => {
    this.setState({ highlightFormField: formFieldName });
  };

  getCustomPropsBasedOnFieldType = field => {
    const { fieldType, options, displayName, filedName } = field;
    const { highlightFormField } = this.state;

    const customFieldOptions = !_isEmpty(options)
      ? _map(options, ({ display, value }) => ({
          label: capitalizeFirstLetters(display),
          value,
        }))
      : EMPTY_ARRAY;
    switch (fieldType) {
      case FIELD_TYPE_VALUES.MULTI_SELECT:
        return {
          mode: 'multiple',
          options: customFieldOptions,
          fieldClassName: highlightFormField === filedName ? styles.highlightClass : styles.multiSelectStyle,
          fieldLabelClassName: highlightFormField === filedName ? styles.label : '',
        };
      case FIELD_TYPE_VALUES.CHECKBOX:
        return {
          options: customFieldOptions,
          label: getArrayLength(customFieldOptions) > 1 ? displayName : undefined,
          fieldClassName:
            highlightFormField === filedName
              ? `${styles.highlightClass} ${styles.checkBoxStyle}`
              : styles.checkBoxStyle,
          labelClassName: highlightFormField === filedName ? styles.label : '',
        };
      case FIELD_TYPE_VALUES.SINGLE_SELECT:
        return {
          options: customFieldOptions,
          allowClear: true,
          fieldClassName: highlightFormField === filedName ? styles.highlightClass : '',
          fieldLabelClassName: highlightFormField === filedName ? styles.label : '',
        };
      case FIELD_TYPE_VALUES.DATE:
        return {
          format: DATE_TIME_FORMATS.NUMERIC_DATE,
          fieldClassName: highlightFormField === filedName ? styles.highlightClass : '',
          fieldLabelClassName: highlightFormField === filedName ? styles.label : '',
        };
      case FIELD_TYPE_VALUES.FREE_TEXT:
        return {
          fieldClassName: highlightFormField === filedName ? styles.highlightClass : '',
          labelClassName: highlightFormField === filedName ? styles.label : '',
        };
      case FIELD_TYPE_VALUES.PRICE:
        return {
          fieldClassName: highlightFormField === filedName ? styles.highlightClass : '',
          labelClassName: highlightFormField === filedName ? styles.label : '',
        };
      case FIELD_TYPE_VALUES.TEXT_AREA:
        return {
          fieldClassName: `flex-grow-1 ${highlightFormField === filedName ? styles.highlightClass : ''}`,
          labelClassName: highlightFormField === filedName ? styles.label : '',
        };
      default:
        return EMPTY_OBJECT;
    }
  };

  getRowsForFormSections = groupedFields => _map(groupedFields, twoFields => ({ columns: twoFields }));

  getFieldIdsForFormSections = fieldsInSection => _map(fieldsInSection, ({ filedName }) => filedName);

  generateSectionAndConfigForForm = fields => {
    const { fieldDisplaySections } = this.props;
    const formConfig = {};
    let formSections = [];
    const customFieldsForSelectedForm = _filter(fields, field => field.userInput === true);

    if (!_isEmpty(customFieldsForSelectedForm)) {
      formSections = getFormSectionsForPreview({
        fieldDisplaySections,
        customAnnotationFields: customFieldsForSelectedForm,
        formInputRowStyles: styles,
        formSectionsClassName: 'is-paddingless',
        getValidFormInputRowGroups: getValidateFormInputRows,
        getRowsForFormSections: this.getRowsForFormSections,
        getFieldIdsForFormSections: this.getFieldIdsForFormSections,
      });
      customFieldsForSelectedForm.forEach(field => {
        const { fieldType, filedName, displayName, maxLen } = field;
        if (filedName) {
          if (maxLen !== 0) {
            const charLimitHelpText = `Only ${maxLen} characters allowed.`;
            formConfig[filedName] = {
              renderer: FIELD_TYPES_VS_RENDERERS[fieldType] || TextInput,
              renderOptions: {
                label: displayName,
                size: 6,
                ...this.getCustomPropsBasedOnFieldType(field),
                maxLength: maxLen,
                helpText: __(charLimitHelpText),
                helpTextClassName: 'p-3',
              },
            };
          } else {
            formConfig[filedName] = {
              renderer: FIELD_TYPES_VS_RENDERERS[fieldType] || TextInput,
              renderOptions: {
                label: displayName,
                size: 6,
                ...this.getCustomPropsBasedOnFieldType(field),
              },
            };
          }
        }
      });
    }
    return { formConfig, formSections };
  };

  handleSubmit = () => {
    const { validatePreviewId, validatePreviewBy, requestId, formValues } = this.state;
    const { onApplyAndValidate, getFormDetailsPayload, fields, dealersListforPreview } = this.props;
    const payload = getFormDetailsPayload();

    const fieldsWithValues = _map(fields, field => {
      const { fieldType, filedName, options } = field;
      const values = _get(formValues, filedName, EMPTY_ARRAY);
      const fieldWithValues = { ...field, values: _castArray(values) };
      if (FIELD_TYPES_REQUIRING_ROOT_PDF_KEY_CHANGE.includes(fieldType)) {
        fieldWithValues.pdfKey = getPdfKeyOfOptionByValue(options, values);
      }
      return fieldWithValues;
    });

    onApplyAndValidate({
      validatePreviewId,
      validatePreviewBy,
      fields: fieldsWithValues,
      payload,
      requestId,
      dealersListforPreview,
    });
  };

  resetValidatePreview = () => this.setState({ validatePreviewId: '', validatePreviewBy: VALIDATE_PREVIEW_BY.DEAL });

  updateValidatePreviewBy = e => this.setState({ validatePreviewBy: e.target.value, validatePreviewId: '' });

  renderValidatePreviewBy = () => {
    const { selectedDealer, isGlobalForm } = this.props;
    const { validatePreviewBy } = this.state;
    return (
      <PropertyControlledComponent controllerProperty={!isGlobalForm || selectedDealer}>
        <Heading size={4} className="m-t-16 m-b-16">
          {__('Validate By')}
        </Heading>
        <Radio
          buttonStyle="outline"
          onChange={this.updateValidatePreviewBy}
          radios={VALIDATE_PREVIEW_BY_OPTIONS}
          value={validatePreviewBy}
          radioClassName="m-r-24"
        />
      </PropertyControlledComponent>
    );
  };

  updateEnv = e => {
    const { onSelectEnv } = this.props;
    onSelectEnv(e);
    this.resetValidatePreview();
  };

  renderEnvSelection = () => {
    const { envsForSelection, envForPreview, isGlobalForm } = this.props;
    return (
      <PropertyControlledComponent controllerProperty={isGlobalForm}>
        <Heading size={4} className="m-b-16">
          {__('Environment')}
        </Heading>
        <Radio
          buttonStyle="outline"
          onChange={this.updateEnv}
          radios={envsForSelection}
          value={envForPreview}
          radioClassName="m-r-24"
        />
      </PropertyControlledComponent>
    );
  };

  searchDealer = searchText => {
    const { onSearchDealer } = this.props;
    onSearchDealer(searchText);
  };

  debouncedSearchDealer = _debounce(this.searchDealer, 300); // eslint-disable-line react/sort-comp

  updateSelectedDealer = selectedDealer => {
    const { onSelectDealer } = this.props;
    onSelectDealer(selectedDealer);
    this.resetValidatePreview();
  };

  renderDealerSelection = () => {
    const { dealersListforPreview, selectedDealer, envForPreview, isGlobalForm } = this.props;
    return (
      <PropertyControlledComponent controllerProperty={isGlobalForm && envForPreview}>
        <Heading size={4} className="m-t-16 m-b-16">
          {__('Dealer')}
        </Heading>
        <Select
          options={getDealerNameOptions(dealersListforPreview)}
          value={selectedDealer}
          showSearch
          onSearch={this.debouncedSearchDealer}
          containerClassName="full-width"
          className="full-width"
          onSelect={this.updateSelectedDealer}
        />
      </PropertyControlledComponent>
    );
  };

  renderValidatePreviewIdAndFormFields = () => {
    const { fields, isGlobalForm, envForPreview, selectedDealer, isDisableFormFields } = this.props;
    const { formValues, formErrors, validatePreviewId, validatePreviewBy } = this.state;
    const { formConfig, formSections } = this.generateSectionAndConfigForForm(fields);
    return (
      <PropertyControlledComponent
        controllerProperty={!isGlobalForm || (isGlobalForm && envForPreview && selectedDealer)}>
        <Heading className="m-t-16 m-b-16" size={4}>
          {VALIDATE_PREVIEW_BY_LABELS[validatePreviewBy]}
        </Heading>
        <Input className={styles.dealNumberInput} value={validatePreviewId} onChange={this.changeValidatePreviewId} />
        <Heading size={4} className="m-t-16 m-b-16">
          {__('Form Fields')}
        </Heading>
        <div className={isDisableFormFields ? styles.viewOnly : styles.formSection}>
          <FormBuilder
            sections={formSections}
            fields={formConfig}
            onAction={this.onFormValuesChange}
            values={formValues}
            errors={formErrors}
          />
        </div>
      </PropertyControlledComponent>
    );
  };

  renderValidateButton = () => {
    const { validatePreviewId } = this.state;
    return (
      <Button
        view={Button.VIEW.SECONDARY}
        className={styles.validateButton}
        disabled={!validatePreviewId}
        onClick={this.handleSubmit}>
        {__('Validate')}
      </Button>
    );
  };

  getInstance = pdfInstance => {
    this.setState({
      pdfInstance,
    });
  };

  renderLeftContent = () => (
    <div className={styles.leftContainer}>
      <div className={styles.leftBody}>
        {this.renderEnvSelection()}
        {this.renderDealerSelection()}
        {this.renderValidatePreviewBy()}
        {this.renderValidatePreviewIdAndFormFields()}
      </div>
      {this.renderValidateButton()}
    </div>
  );

  renderRightContent = (previewUrls = EMPTY_OBJECT) => {
    const { formName } = this.props;
    const { focusedField, requestId } = this.state;
    const targetUrl = previewUrls[requestId];
    const additionalToolbarItems = [{ type: ZOOM_IN }, { type: ZOOM_OUT }, { type: PAGER }];
    const originalFileName = formName?.split('.')?.[0];

    return (
      <React.Fragment>
        <PropertyControlledComponent controllerProperty={!targetUrl}>
          <Content className="m-y-16 m-l-32">{originalFileName}</Content>
          <div className={styles.noPDFDataContainer}>
            <div className="full-height full-width d-flex flex-column justify-content-center align-items-center">
              <img src={noPDFData} alt={__('No Pdf DAta')} />
              <Heading size={3} className="m-b-4">
                {__('Enter Deal#/Lead ID to Preview')}
              </Heading>
              <Content className={styles.contentLabel}>
                {__('Validate form against deal data to preview and/or print.')}
              </Content>
            </div>
          </div>
        </PropertyControlledComponent>
        <PropertyControlledComponent controllerProperty={targetUrl}>
          <Content className="m-y-16 m-l-32">{originalFileName}</Content>
          <PdfAnnotations
            documentUrl={targetUrl}
            isSignEnabled={false}
            hasToolbar={false}
            pdfContainerClassName={styles.pdfkit}
            containerClassName={styles.pdfkitContainer}
            focusedField={focusedField}
            onFormFieldFocus={this.onFieldFocus}
            additionalToolbarItems={additionalToolbarItems}
            config={{
              toolbarPlacement: 'BOTTOM',
            }}
            getInstance={this.getInstance}
          />
        </PropertyControlledComponent>
      </React.Fragment>
    );
  };

  renderHeader = () => {
    const { toggleModal } = this.props;
    return (
      <div className={`p-y-16 p-x-24 ${styles.headerContainer}`}>
        <Heading size={1}>{__('Test Print')}</Heading>
        <IconAsBtn containerClassName={`flex-center ${styles.closeIcon}`} onClick={toggleModal}>
          icon-close
        </IconAsBtn>
      </div>
    );
  };

  renderBody = () => {
    const { previewUrls, className } = this.props;
    return (
      <div className={classNames('d-flex', styles.validateFormModalBodyContainer, className)}>
        {this.renderLeftContent()}
        <div className={styles.modalContentSeperator}></div>
        <div className={styles.rightContainer}>{this.renderRightContent(previewUrls)}</div>
      </div>
    );
  };

  handlePrint = () => {
    const { pdfInstance } = this.state;
    pdfInstance.print();
  };

  renderFooter = () => {
    const { toggleModal, previewUrls = EMPTY_OBJECT } = this.props;
    const { requestId } = this.state;
    const targetUrl = previewUrls[requestId];
    return (
      <div className={`p-y-16 p-x-24 ${styles.footerContainer}`}>
        <Button view={Button.VIEW.SECONDARY} className="m-r-24" onClick={toggleModal}>
          {__('Cancel')}
        </Button>

        <ButtonWithIconAndLabel
          icon="icon-printer2"
          label={__('Print')}
          className={styles.printButton}
          view={Button.VIEW.PRIMARY}
          disabled={!targetUrl}
          onClick={this.handlePrint}
        />
      </div>
    );
  };

  render() {
    const { showModal, toggleModal, previewUrls, className } = this.props;

    return (
      <Drawer
        placement={PLACEMENTS.RIGHT}
        closable={false}
        width={1200}
        visible={showModal}
        onClose={toggleModal}
        className={styles.drawer}
        destroyOnClose>
        {this.renderHeader()}
        {this.renderBody()}
        {this.renderFooter()}
      </Drawer>
    );
  }
}

ValidateFormModal.propTypes = {
  toggleModal: PropTypes.func.isRequired,
  onApplyAndValidate: PropTypes.func.isRequired,
  showModal: PropTypes.bool,
  previewUrls: PropTypes.object,
  getFormDetailsPayload: PropTypes.func.isRequired,
  fieldDisplaySections: PropTypes.array,
  className: PropTypes.string,
  fields: PropTypes.array,
  onSelectEnv: PropTypes.func,
  dealersListforPreview: PropTypes.array,
  envsForSelection: PropTypes.array,
  selectedDealer: PropTypes.string,
  onSearchDealer: PropTypes.func,
  onSelectDealer: PropTypes.func,
  envForPreview: PropTypes.string,
  isGlobalForm: PropTypes.bool,
  isDisableFormFields: PropTypes.bool,
};

ValidateFormModal.defaultProps = {
  showModal: false,
  previewUrls: EMPTY_OBJECT,
  fieldDisplaySections: EMPTY_ARRAY,
  fields: EMPTY_ARRAY,
  onSelectEnv: _noop,
  dealersListforPreview: EMPTY_ARRAY,
  envsForSelection: EMPTY_ARRAY,
  selectedDealer: EMPTY_STRING,
  className: EMPTY_STRING,
  onSearchDealer: _noop,
  onSelectDealer: _noop,
  envForPreview: EMPTY_STRING,
  isGlobalForm: false,
  isDisableFormFields: false,
};

export default ValidateFormModal;
