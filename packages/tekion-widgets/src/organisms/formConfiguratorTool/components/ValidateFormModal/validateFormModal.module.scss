@use "tstyles/variables.scss";
@use "tstyles/mixins/layout.scss";
@use "tstyles/colors.scss";
@import "tstyles/component.scss";

$skeleton-height: 6.4rem;
$close-icon-size: 3.2rem;

.validateFormModalBodyContainer {
  height: calc(100% - 12.8rem);
  .leftContainer {
    overflow: auto;
    width: 45%;
    padding: 1.6rem 2.4rem 0 2.4rem;
    .leftBody {
      height: calc(100% - 5.5rem);
      overflow: auto;
    }
  }

  .modalContentSeperator {
    width: 0.1rem;
    background-color: $platinum;
    @include full-height;
  }

  .rightContainer {
    @include full-height;
    width: 55%;
    background: rgb(246 247 248);
  }
}

.dealNumberInput {
  width: 31.8rem;
}

.multiSelectStyle{
  height: auto;
}

.pdfkitContainer {
  height: calc(100% - 5rem);
  width: 100%;
  position: relative;
  @include flex($flex-flow: column);
  margin-left: 3rem;
}

.pdfkit {
  width: 90%;
}

.highlightClass{
  border: solid 0.5rem $lightCaribbeanGreen;
  left: 0; 
  top: 0;
  width: 100%;
  height: 100%;
}

.label{
  background: $lightCaribbeanGreen;
  width: 100%;
  margin: 0;
  padding-bottom: 0.1rem;
}

.viewOnly {
  opacity: 0.5;
  pointer-events: none;
}

.checkBoxStyle {
  label {
    word-break: break-all;
  }
}

.validateButton {
  width: 100%;
}

.formSection {
  overflow: auto;
  height: calc(100% - 26.8rem);
}

.contentLabel {
  font-size: 1.5rem;
}

.drawer {
  :global(.ant-drawer-content-wrapper) {
    top: $skeleton-height;
  }

  :global(.ant-drawer-body) {
    padding: 0;
  }

  :global(.ant-drawer-wrapper-body) {
    height: calc(100% - #{$skeleton-height}) !important;
    overflow: hidden !important;
  }
  :global(.ant-drawer-body) {
    height: 100%;
  }


  .headerContainer {
    @include layout.flex($align-items: center, $justify-content: space-between);
    border-bottom: variables.$border;
    background-color: colors.$glitter;
  }

  .closeIcon {
    border-radius: 50%;
    width: $close-icon-size;
    height: $close-icon-size;
    color: colors.$atomic;
    &:hover {
      background: colors.$lightGray;
    }
  }

  .footerContainer {
    @include layout.flex($align-items: center, $justify-content: flex-end);
    border-top: variables.$border;
    background-color: colors.$white;
    height: 6.4rem;

  .printButton {
    align-items: center;
    }
  }

}

.noPDFDataContainer {
  margin: 0 3rem;
  height: calc(100% - 5rem);
  background-color: $white;
}

