import React, { useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import _get from 'lodash/get';
import Heading from '@tekion/tekion-components/src/atoms/Heading';
import FormBuilder from '@tekion/tekion-components/src/organisms/FormBuilder';
import BaseTable from '@tekion/tekion-components/src/molecules/table/BaseTable';
import Button from '@tekion/tekion-components/src/atoms/Button';
import Select from '@tekion/tekion-components/src/molecules/Select';
import { defaultMemoize } from 'reselect';
import actionTypes from '@tekion/tekion-components/src/organisms/FormBuilder/constants/actionTypes';
import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import _noop from 'lodash/noop';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import {
  COLUMNS,
  FORM_FIELDS,
  CONDITIONAL_TABLE_HEADERS_KEYS,
  FIELD_DEFAULT_VALUES,
} from './FieldPropertiesGeneralForm.constants';
import { getFields, getSections } from './FieldPropertiesGeneralForm.config';
import styles from './fieldPropertiesGeneralForm.module.scss';
import { checkIsCustomField, showGlobalFieldDefaultValues } from '../FieldPropertiesForm/FieldPropertiesForm.utils';
import { getTimeStampAnnotationOptions, isSignatureAnnotation } from '../../FormConfiguratorTool.utils';

export default function FieldPropertiesGeneralForm(props) {
  const {
    values,
    allGlobalFields,
    updateFormValues,
    selectedField,
    globalFields,
    supportedFieldTypes,
    fieldProperties,
    isFormsLibrary,
    editType,
    annotatedFields,
    isFcLiteModeEnabled,
    config,
    hasViewOnlyPermission,
    isGlobalFieldByDefault,
    hideActiveFieldToggle,
    disableEditForFCLiteMode,
  } = props;

  const handleAction = useCallback(
    ({ payload: { id, value }, type }) => {
      if (type === actionTypes.ON_FIELD_CHANGE) {
        updateFormValues({ id, value });
      }
    },
    [updateFormValues]
  );

  const handleConditionalValuesChange = conditionalValues => {
    handleAction({
      payload: {
        id: FORM_FIELDS.FIELD_CONDITIONAL_VALUE,
        value: conditionalValues,
      },
      type: actionTypes.ON_FIELD_CHANGE,
    });
  };

  const selectedFieldType = _get(values, `${[FORM_FIELDS.FIELD_TYPE]}[0]`, null);
  const defaultValueTypeSelected = values[FORM_FIELDS.FIELD_DEFAULT_VALUE_PICKER];
  const showCustomValueField = showGlobalFieldDefaultValues(
    defaultValueTypeSelected,
    FIELD_DEFAULT_VALUES.CUSTOM_VALUE
  );

  const showConditionalValueTable = showGlobalFieldDefaultValues(
    defaultValueTypeSelected,
    FIELD_DEFAULT_VALUES.CONDITIONAL_VALUES
  );

  const showTimeStampSelectionField = isSignatureAnnotation(fieldProperties);
  const timeStampAnnotationsOptions = useMemo(
    () => getTimeStampAnnotationOptions(annotatedFields, fieldProperties),
    [annotatedFields, fieldProperties]
  );

  const updatedFormValues = {
    ...values,
    [FORM_FIELDS.CUSTOM_FIELD]: checkIsCustomField(fieldProperties),
    [FORM_FIELDS.SHOW_ON_FORM]: _get(fieldProperties, 'active', false),
  };

  return (
    <div>
      <div>
        <Heading bold size={3}>
          {__('General')}
        </Heading>
        <p>{__('Define Field Type & Default States')}</p>
      </div>

      <div>
        <FormBuilder
          className={styles.formContainer}
          values={updatedFormValues}
          fields={getFields({
            globalFields,
            selectedField,
            selectedFieldType,
            supportedFieldTypes,
            isFormsLibrary,
            editType,
            timeStampAnnotationsOptions,
            hasViewOnlyPermission,
            fieldProperties,
            disableEditForFCLiteMode,
          })}
          sections={getSections({
            showCustomValueField,
            showTimeStampSelectionField,
            isFcLiteModeEnabled,
            config,
            isGlobalFieldByDefault,
            hideActiveFieldToggle,
          })}
          onAction={handleAction}
        />
      </div>

      <PropertyControlledComponent controllerProperty={showConditionalValueTable}>
        <ConditionalValueTable
          allGlobalFields={allGlobalFields}
          conditionalValues={values.conditionalValues || [EMPTY_OBJECT]}
          updateConditionalValues={handleConditionalValuesChange}
        />
      </PropertyControlledComponent>
    </div>
  );
}

const ConditionalValueTable = props => {
  const { conditionalValues, allGlobalFields, updateConditionalValues } = props;

  const CONDITIONAL_TABLE_COLUMN_KEYS = [
    COLUMNS.COLUMN_OPERATOR,
    COLUMNS.COLUMN_GLOBAL_FIELD,
    COLUMNS.COLUMN_DELETE_ACTION,
  ];

  const handleGlobalFieldSelectChange = (index, value) => {
    const _defaultValues = [...conditionalValues];
    _defaultValues[index] = {
      ...conditionalValues[index],
      [COLUMNS.COLUMN_GLOBAL_FIELD]: value,
    };
    updateConditionalValues(_defaultValues);
  };

  const handleDeleteRow = index => {
    const _defaultValues = [...conditionalValues];
    _defaultValues.splice(index, 1);
    updateConditionalValues(_defaultValues);
  };

  const handleAddRow = () => {
    const _defaultValues = [...conditionalValues];
    _defaultValues.push({});
    updateConditionalValues(_defaultValues);
  };

  const getCellComponent = defaultMemoize(({ key, value, index }) => {
    let Cell = null;

    switch (key) {
      case COLUMNS.COLUMN_OPERATOR:
        Cell = <div>{index === 0 ? __('Show') : __('Else Show')}</div>;
        break;
      case COLUMNS.COLUMN_GLOBAL_FIELD:
        Cell = (
          <Select
            className="full-width"
            containerClassName="full-width"
            placeholder={__('Select Type')}
            onChange={(...args) => handleGlobalFieldSelectChange(index, ...args)}
            options={allGlobalFields}
            value={value}
            disabled={false}
          />
        );
        break;
      case COLUMNS.COLUMN_DELETE_ACTION:
        Cell = <Button view="icon" icon="icon-trash" onClick={() => handleDeleteRow(index)} />;
        break;
      default:
        Cell = <div>NOT_AVAILABLE</div>;
    }
    return Cell;
  });

  const getColumns = () => {
    const columnData = CONDITIONAL_TABLE_COLUMN_KEYS.map(key => ({
      ...CONDITIONAL_TABLE_COLUMN_KEYS[key],
      key,
      accessor: key,
      Header: CONDITIONAL_TABLE_HEADERS_KEYS[key],
      Cell: ({ original: rowData, value, index }) =>
        getCellComponent({
          key,
          value,
          index,
          rowData,
        }),
    }));

    return columnData;
  };

  return (
    <div>
      <BaseTable showPagination={false} columns={getColumns()} data={conditionalValues} minRows={1} rowHeight={40} />
      <Button view="tertiary" onClick={handleAddRow}>
        {__('+ Add Condition')}
      </Button>
    </div>
  );
};

ConditionalValueTable.propTypes = {
  conditionalValues: PropTypes.object,
  allGlobalFields: PropTypes.array,
  updateConditionalValues: PropTypes.func,
};

ConditionalValueTable.defaultProps = {
  conditionalValues: EMPTY_OBJECT,
  allGlobalFields: EMPTY_ARRAY,
  updateConditionalValues: _noop,
};

FieldPropertiesGeneralForm.propTypes = {
  values: PropTypes.object,
  allGlobalFields: PropTypes.array,
  globalFields: PropTypes.array,
  updateFormValues: PropTypes.func,
  selectedField: PropTypes.object,
  supportedFieldTypes: PropTypes.array,
  annotatedFields: PropTypes.object,
  fieldProperties: PropTypes.object,
};

FieldPropertiesGeneralForm.defaultProps = {
  values: EMPTY_OBJECT,
  allGlobalFields: EMPTY_ARRAY,
  globalFields: EMPTY_ARRAY,
  updateFormValues: _noop,
  selectedField: null,
  supportedFieldTypes: EMPTY_ARRAY,
  annotatedFields: EMPTY_OBJECT,
  fieldProperties: EMPTY_OBJECT,
};
