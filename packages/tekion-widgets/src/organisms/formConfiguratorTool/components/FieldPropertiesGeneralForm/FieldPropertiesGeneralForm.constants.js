export const FORM_FIELDS = {
  FIELD_NAME: 'name',
  FIELD_TYPE: 'type',
  FIELD_VARIATION: 'variation',
  FIELD_DESCRIPTION: 'description',
  FIELD_PREFIX: 'prefix',
  FIELD_SUFFIX: 'suffix',
  FIELD_DEFAULT_VALUE_PICKER: 'defaultValueType',
  FIELD_CUSTOM_VALUE: 'customValues',
  FIELD_CONDITIONAL_VALUE: 'conditionalValues',
  HEADER_WITH_INFO_POPOVER: 'headerWithInfoPopover',
  TIME_STAMP: 'timeStampKey',
  SHOW_ON_FORM: 'showOnForm',
  CUSTOM_FIELD: 'customField',
};

export const COLUMNS = {
  COLUMN_OPERATOR: 'operator',
  COLUMN_GLOBAL_FIELD: 'globalField',
  COLUMN_DELETE_ACTION: 'deleteAction',
};

export const CONDITIONAL_TABLE_HEADERS_KEYS = {
  [COLUMNS.COLUMN_OPERATOR]: __('Operator'),
  [COLUMNS.COLUMN_GLOBAL_FIELD]: __('Global Field'),
  [COLUMNS.COLUMN_DELETE_ACTION]: __('Delete'),
};

export const FIELD_NAME_TYPES = [
  {
    label: __('Stock Type'),
    value: 'STOCK_TYPE',
  },
  {
    label: __('Amount Type'),
    value: 'AMOUNT_TYPE',
  },
];

export const GLOBAL_FIELD_OPTIONS = [
  {
    label: __('Customer Type'),
    value: 'CUSTOMER_TYPE',
  },
  {
    label: __('Full Paid'),
    value: 'FULL_PAID',
  },
];

export const FIELD_DEFAULT_VALUES = {
  BLANK: 'BLANK',
  CUSTOM_VALUE: 'CUSTOM_VALUE',
  CONDITIONAL_VALUES: 'CONDITIONAL_VALUES',
  NONE: 'NONE',
};

export const FIELD_DEFAULT_VALUES_TYPES = [
  {
    label: __('Blank'),
    value: FIELD_DEFAULT_VALUES.BLANK,
  },
  {
    label: __('Custom Value'),
    value: FIELD_DEFAULT_VALUES.CUSTOM_VALUE,
  },
  {
    label: __('Conditional Values'),
    value: FIELD_DEFAULT_VALUES.CONDITIONAL_VALUES,
  },
  {
    label: __('No default'),
    value: FIELD_DEFAULT_VALUES.NONE,
  },
];
