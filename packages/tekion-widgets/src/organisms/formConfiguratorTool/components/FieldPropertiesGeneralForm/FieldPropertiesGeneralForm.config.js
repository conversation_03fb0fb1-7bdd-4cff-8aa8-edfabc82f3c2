import _map from 'lodash/map';
import _get from 'lodash/get';
import _forEach from 'lodash/forEach';
import _filter from 'lodash/filter';
import _isEmpty from 'lodash/isEmpty';
import _includes from 'lodash/includes';
import { defaultMemoize } from 'reselect';

import { EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';

import Select from '@tekion/tekion-components/organisms/FormBuilder/fieldRenderers/select';
import SelectInput from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/SelectInput';
import TextInput from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/textInput';
import SwitchWithInfoField from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/switchWithInfoField';
import { SWITCH_PLACEMENT } from '@tekion/tekion-components/src/molecules/switchWithInfo';

import { FORM_FIELDS, FIELD_DEFAULT_VALUES_TYPES } from './FieldPropertiesGeneralForm.constants';
import HeaderWithInfoPopover from '../HeaderWithInfoPopover';
import styles from './fieldPropertiesGeneralForm.module.scss';
import { checkIfGlobalField } from '../FieldPropertiesForm/FieldPropertiesForm.utils';
import {
  fieldsSearchFilter,
  getSupportedFieldTypeOptions,
  disableFieldBasedOnPermission,
} from '../../FormConfiguratorTool.utils';
import { CollapsibleGroupSelect } from '../FieldRenderer';
import { FIELD_TYPE_OPTIONS, FIELD_TYPE_VALUES, FIELD_VARIATION_OPTIONS } from '../../FormConfiguratorTool.constants';

export const getSections = ({
  showCustomValueField,
  showTimeStampSelectionField,
  isFcLiteModeEnabled,
  isGlobalFieldByDefault,
  hideActiveFieldToggle,
}) => {
  const defaultValuesFields = [FORM_FIELDS.FIELD_DEFAULT_VALUE_PICKER];

  if (showCustomValueField) {
    defaultValuesFields.push(FORM_FIELDS.FIELD_CUSTOM_VALUE);
  }

  const customFieldToggle = isGlobalFieldByDefault ? [FORM_FIELDS.CUSTOM_FIELD] : [];
  const showOnFormToggle = !hideActiveFieldToggle ? [FORM_FIELDS.SHOW_ON_FORM] : [];

  return [
    {
      header: {
        label: __('Field Configurations'),
        size: 5,
      },
      fieldIds: [
        FORM_FIELDS.SHOW_ON_FORM,
        FORM_FIELDS.CUSTOM_FIELD,
        FORM_FIELDS.FIELD_NAME,
        FORM_FIELDS.FIELD_TYPE,
        FORM_FIELDS.FIELD_VARIATION,
        FORM_FIELDS.FIELD_DESCRIPTION,
        FORM_FIELDS.TIME_STAMP,
      ],
      rows: [
        {
          columns: [...showOnFormToggle, ...customFieldToggle],
        },
        {
          columns: [FORM_FIELDS.FIELD_NAME, FORM_FIELDS.FIELD_TYPE],
        },
        {
          columns: [FORM_FIELDS.FIELD_VARIATION, FORM_FIELDS.FIELD_DESCRIPTION],
        },
        {
          columns: showTimeStampSelectionField ? [FORM_FIELDS.TIME_STAMP] : [],
        },
      ],
    },
    ...(isFcLiteModeEnabled
      ? EMPTY_ARRAY
      : [
          {
            fieldIds: [FORM_FIELDS.FIELD_PREFIX, FORM_FIELDS.FIELD_SUFFIX],
            rows: [
              {
                columns: [FORM_FIELDS.HEADER_WITH_INFO_POPOVER],
              },
              {
                columns: [FORM_FIELDS.FIELD_PREFIX, FORM_FIELDS.FIELD_SUFFIX],
              },
            ],
          },
        ]),
    {
      header: {
        label: __('Default Value'),
        size: 5,
      },
      fieldIds: defaultValuesFields,
      rows: [
        {
          columns: defaultValuesFields,
        },
      ],
    },
  ];
};

const getGlobalFieldValues = defaultMemoize((globalFields, searchQueryValue) => {
  const globalFieldsToDisplay = [];
  _forEach(globalFields, fieldSections => {
    const newFieldSection = { ...fieldSections };
    newFieldSection.globalAnnotationList = _filter(newFieldSection?.globalAnnotationList, value =>
      fieldsSearchFilter(_get(value, 'fieldName'), searchQueryValue)
    );
    if (!_isEmpty(newFieldSection.globalAnnotationList)) {
      globalFieldsToDisplay.push(newFieldSection);
    }
  });
  return globalFieldsToDisplay;
});

const getGroupedOptions = globalFields =>
  _map(getGlobalFieldValues(globalFields, ''), globalFieldCategory => ({
    id: _get(globalFieldCategory, 'categoryDisplayName'),
    label: _get(globalFieldCategory, 'categoryDisplayName'),
    options: _map(_get(globalFieldCategory, 'globalAnnotationList'), globalField => ({
      label: _get(globalField, 'fieldAnnotation'),
      value: _get(globalField, 'fieldAnnotation'),
    })),
  }));

const styleOverrides = {
  valueContainer: base => ({
    ...base,
    padding: '0 0 0 1.2rem',
  }),
};

// static fields as for now, can be dynamic later or change as per requirements
export const getFields = ({
  globalFields,
  selectedField,
  selectedFieldType,
  supportedFieldTypes,
  isFormsLibrary,
  editType,
  timeStampAnnotationsOptions = [],
  hasViewOnlyPermission,
  disableEditForFCLiteMode,
}) => {
  const isDisableBasedOnPermission = disableFieldBasedOnPermission({
    isFormsLibrary,
    editType,
  });
  return {
    [FORM_FIELDS.FIELD_NAME]: {
      renderer: CollapsibleGroupSelect,
      renderOptions: {
        label: __('Field Name'),
        placeholder: __('Select Field Name'),
        filterOption: ({ value }, searchText) => fieldsSearchFilter(value, searchText),
        groupedOptions: getGroupedOptions(globalFields),
        isMulti: false,
        styleOverrides,
        isDisabled: isDisableBasedOnPermission || disableEditForFCLiteMode,
      },
    },
    [FORM_FIELDS.FIELD_TYPE]: {
      renderer: SelectInput,
      renderOptions: {
        label: __('Field Type'),
        placeholder: __('Select Field Type'),
        options: getSupportedFieldTypeOptions(FIELD_TYPE_OPTIONS, supportedFieldTypes),
        isDisabled: isDisableBasedOnPermission || checkIfGlobalField(selectedField) || disableEditForFCLiteMode,
      },
    },
    [FORM_FIELDS.FIELD_VARIATION]: {
      renderer: SelectInput,
      renderOptions: {
        label: __('Field Formatting'),
        placeholder: __('Select Field Formatting'),
        options: FIELD_VARIATION_OPTIONS[selectedFieldType],
        isDisabled: hasViewOnlyPermission || selectedFieldType === FIELD_TYPE_VALUES.SIGNATURE,
      },
    },
    [FORM_FIELDS.TIME_STAMP]: {
      renderer: Select,
      renderOptions: {
        label: __('Timestamp'),
        placeholder: __('Select timestamp annotation'),
        allowClear: true,
        fieldClassName: styles.halfField,
        options: timeStampAnnotationsOptions,
      },
    },
    [FORM_FIELDS.FIELD_DEFAULT_VALUE_PICKER]: {
      renderer: SelectInput,
      renderOptions: {
        label: __('When Default state value is empty, Show'),
        placeholder: __('Select'),
        options: FIELD_DEFAULT_VALUES_TYPES,
        isDisabled:
          isDisableBasedOnPermission ||
          _includes([FIELD_TYPE_VALUES.SIGNATURE, FIELD_TYPE_VALUES.TIMESTAMP], selectedFieldType),
      },
    },
    [FORM_FIELDS.FIELD_DESCRIPTION]: {
      renderer: TextInput,
      renderOptions: {
        label: __('Field Description'),
        placeholder: __('Tell User about Field.'),
        disabled: hasViewOnlyPermission || checkIfGlobalField(selectedField),
      },
    },
    [FORM_FIELDS.FIELD_PREFIX]: {
      renderer: TextInput,
      renderOptions: {
        label: __('Prefix'),
        placeholder: __('Enter'),
        disabled: isDisableBasedOnPermission,
      },
    },
    [FORM_FIELDS.HEADER_WITH_INFO_POPOVER]: {
      renderer: HeaderWithInfoPopover,
      renderOptions: {
        headingSize: 5,
        headerLabel: 'Affix',
        helpText: 'Text About Affix comes here',
      },
    },
    [FORM_FIELDS.FIELD_SUFFIX]: {
      renderer: TextInput,
      renderOptions: {
        label: __('Suffix'),
        placeholder: __('Enter'),
        disabled: isDisableBasedOnPermission,
      },
    },
    [FORM_FIELDS.FIELD_CUSTOM_VALUE]: {
      renderer: TextInput,
      renderOptions: {
        label: __('Custom Value'),
        placeholder: __('Enter'),
      },
    },
    [FORM_FIELDS.SHOW_ON_FORM]: {
      renderer: SwitchWithInfoField,
      renderOptions: {
        label: __('Show on Form'),
        switchPlacement: SWITCH_PLACEMENT.START,
        switchSize: 'small',
        labelClassName: styles.labelClassName,
        switchWithInfoClassName: styles.switchWithInfoClassName,
        disabled: hasViewOnlyPermission,
      },
    },
    [FORM_FIELDS.CUSTOM_FIELD]: {
      renderer: SwitchWithInfoField,
      renderOptions: {
        label: __('Custom Field'),
        switchPlacement: SWITCH_PLACEMENT.START,
        switchSize: 'small',
        labelClassName: styles.labelClassName,
        switchWithInfoClassName: styles.switchWithInfoClassName,
        disabled: disableFieldBasedOnPermission({
          disableActions: hasViewOnlyPermission,
          isFormsLibrary,
          editType,
        }),
      },
    },
  };
};
