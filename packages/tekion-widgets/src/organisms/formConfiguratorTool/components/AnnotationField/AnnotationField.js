import React, { useCallback, useMemo, useState, useRef } from 'react';
import cx from 'classnames';
import PropTypes from 'prop-types';
import _noop from 'lodash/noop';
import _get from 'lodash/get';
import _round from 'lodash/round';
import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import Tooltip, { TOOLTIP_PLACEMENT } from '@tekion/tekion-components/src/atoms/tooltip';
import Content from '@tekion/tekion-components/src/atoms/Content';
import Heading from '@tekion/tekion-components/src/atoms/Heading';
import FontIcon, { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import COLORS from 'tstyles/exports.scss';
import ResizeAndDrag from '../ResizeAndDrag';
import FormActions from '../FormActions';
import styles from './annotationField.module.scss';
import useKeypress from '../../hooks/useKeyboard';
import { KEYBOARD_KEY_CODES, FIELD_TYPES, DEFAULT_SCALE } from '../../FormConfiguratorTool.constants';
import {
  getPixelInNumber,
  showBottomReference,
  showLeftReference,
  showRightReference,
  showTopReference,
} from '../../FormConfiguratorTool.utils';
import configReader from '../../readers/config.reader';
import DivWithoutPropagation from '../../../../atoms/DivWithoutPropagation';

// corner cursor styles for react-rnd, whatever passed will overrride default one completely
const RESIZE_CURSOR_STYLE = {
  top: {
    width: '100%',
    height: '10px',
    top: '-5px',
    left: '5px',
    cursor: 'row-resize',
  },
  right: {
    width: '10px',
    height: '100%',
    top: '5px',
    right: '-5px',
    cursor: 'col-resize',
  },
  bottom: {
    width: '100%',
    height: '10px',
    bottom: '-5px',
    left: '5px',
    cursor: 'row-resize',
  },
  left: {
    width: '10px',
    height: '100%',
    top: '5px',
    left: '-5px',
    cursor: 'col-resize',
  },
  topRight: {
    width: '20px',
    height: '20px',
    position: 'absolute',
    right: '-15px',
    top: '-15px',
    cursor: 'ne-resize',
  },
  bottomRight: {
    width: '20px',
    height: '20px',
    position: 'absolute',
    right: '-15px',
    bottom: '-15px',
    cursor: 'se-resize',
  },
  bottomLeft: {
    width: '20px',
    height: '20px',
    position: 'absolute',
    left: '-15px',
    bottom: '-15px',
    cursor: 'sw-resize',
  },
  topLeft: {
    width: '20px',
    height: '20px',
    position: 'absolute',
    left: '-15px',
    top: '-15px',
    cursor: 'nw-resize',
  },
};

const INITIAL_REFERENCE_LINE_STATE = { show: false, gap: 0 };
const RESIZING_OPTIONS = {
  top: true,
  right: true,
  bottom: true,
  left: true,
  topRight: true,
  bottomRight: true,
  bottomLeft: true,
  topLeft: true,
};

const AnnotationField = React.memo(
  ({
    field,
    formScale,
    selectedTooltipField,
    selected,
    setShowTooltip,
    isAnchor,
    onChange,
    onClick,
    onDuplicate,
    onDelete,
    showFieldProperties,
    annotatedFields,
    disableDragging,
    enableResizing,
    resizeContainerStyle,
    isInvalid,
    isViewOnlyMode,
    config,
    disableFieldActions,
    isFcLiteModeEnabled,
    selectedFields,
    updateSelectedFields,
    toggleGroupSelectedAnnotations,
    groupSelectedAnnotations,
  }) => {
    const { x, y, width, height, fieldAnnotation: label, fieldType, description, order, id } = field;
    const [isDragging, setIsDragging] = useState(false);
    const [dragPosition, setDragPosition] = useState({ x, y, width, height });
    const [topReferenceLine, setTopReferenceLine] = useState(INITIAL_REFERENCE_LINE_STATE);
    const [bottomReferenceLine, setBottomReferenceLine] = useState(INITIAL_REFERENCE_LINE_STATE);
    const [leftReferenceLine, setLeftReferenceLine] = useState(INITIAL_REFERENCE_LINE_STATE);
    const [rightReferenceLine, setRightReferenceLine] = useState(INITIAL_REFERENCE_LINE_STATE);
    const timer = useRef();

    useKeypress(KEYBOARD_KEY_CODES.ESCAPE, () => setShowTooltip(null));

    const onDrag = useCallback(
      (event, d) => {
        setIsDragging(true);
        setDragPosition({ ...dragPosition, x: d.x, y: d.y });
        setTopReferenceLine(showTopReference(annotatedFields, { ...dragPosition, x: d.x, y: d.y }, id));
        setRightReferenceLine(showRightReference(annotatedFields, { ...dragPosition, x: d.x, y: d.y }, id));
        setBottomReferenceLine(showBottomReference(annotatedFields, { ...dragPosition, x: d.x, y: d.y }, id));
        setLeftReferenceLine(showLeftReference(annotatedFields, { ...dragPosition, x: d.x, y: d.y }, id));
        setShowTooltip(null);
      },
      [dragPosition, setShowTooltip, annotatedFields, id]
    );

    const onDragStop = useCallback(
      (e, d) => {
        setDragPosition({ ...dragPosition, x: d.x, y: d.y });
        setTimeout(() => {
          setIsDragging(false);
        }, 10);
        if (isDragging) onChange(field, { x: d.x, y: d.y });
      },
      [field, onChange, isDragging, setIsDragging, dragPosition]
    );

    const onResizeStop = useCallback(
      (e, direction, ref, delta, resizedPosition) => {
        onChange(field, {
          width: getPixelInNumber(ref.style.width),
          height: getPixelInNumber(ref.style.height),
          actualWidth: _round(getPixelInNumber(ref.style.width) * (DEFAULT_SCALE / formScale), 2),
          actualHeight: _round(getPixelInNumber(ref.style.height) * (DEFAULT_SCALE / formScale), 2),
          ...resizedPosition,
        });
        setDragPosition({
          width: getPixelInNumber(ref.style.width),
          height: getPixelInNumber(ref.style.height),
          ...resizedPosition,
        });
        setShowTooltip(null);
      },
      [field, onChange, setShowTooltip, formScale]
    );

    const onResize = useCallback((e, direction, ref, delta, resizedPosition) => {
      setDragPosition({
        width: getPixelInNumber(ref.style.width),
        height: getPixelInNumber(ref.style.height),
        ...resizedPosition,
      });
    }, []);

    const onSingleClick = useCallback(
      isShiftPressed => {
        if (isShiftPressed) onClick(field, isShiftPressed);
        else setShowTooltip(id);
      },
      [field, id, onClick, setShowTooltip]
    );

    const onFieldClick = useCallback(
      event => {
        if (isDragging) return;
        clearTimeout(timer.current);

        if (event.detail === 1) {
          timer.current = setTimeout(onSingleClick, 200, event?.shiftKey);
        } else if (
          event.detail === 2 &&
          !(fieldType === FIELD_TYPES.GLOBAL_FIELD && configReader.disableGlobalAnnotationPropertiesClick(config))
        ) {
          onPropertiesClick();
        }
        event?.stopPropagation(); // eslint-disable-line no-unused-expressions
      },
      [onSingleClick, onPropertiesClick, isDragging, config, fieldType]
    );

    const onDuplicateClick = useCallback(() => onDuplicate(field), [field, onDuplicate]);
    const onDeleteClick = useCallback(() => onDelete(id), [id, onDelete]);
    const onPropertiesClick = useCallback(() => {
      showFieldProperties(field);
      setShowTooltip(null);
    }, [field, showFieldProperties, setShowTooltip]);

    const renderTooltipContent = useMemo(
      () => (
        <div className={styles.fieldContainer}>
          <div className={styles.fieldInfoContainer}>
            <Content colorVariant={Content.COLOR_VARIANTS.GREY}>
              {label}
              {description ? `: ${description}` : ''}
            </Content>
          </div>
          <DivWithoutPropagation>
            <FormActions
              onAlign={updateSelectedFields}
              onMatch={updateSelectedFields}
              annotatedFields={annotatedFields}
              selectedFields={selectedFields}
              toggleGroupSelectedAnnotations={toggleGroupSelectedAnnotations}
              onClickEditProperties={onPropertiesClick}
              hideEditPropertiesButton={
                fieldType === FIELD_TYPES.GLOBAL_FIELD && configReader.disableGlobalAnnotationPropertiesClick(config)
              }
              isAnnotationFieldType
              onDeleteAnnotation={onDeleteClick}
              onSingleAnnotationCopy={onDuplicateClick}
              hidePrinterAlignment
              isFcLiteModeEnabled={isFcLiteModeEnabled}
              groupSelectedAnnotations={groupSelectedAnnotations}
              disableFieldActions={disableFieldActions}
            />
          </DivWithoutPropagation>
        </div>
      ),
      [description, label, onDuplicateClick, onDeleteClick, onPropertiesClick, isViewOnlyMode, selectedFields]
    );

    const renderContent = () => {
      if (fieldType === FIELD_TYPES.GLOBAL_FIELD || fieldType === FIELD_TYPES.CUSTOM_FIELD) {
        return (
          <Content className={styles.fieldLabel}>
            {order}.{label}
          </Content>
        );
      }
      // update configuration view
      return (
        <Content className={styles.fieldLabel}>
          {order}.{label}
        </Content>
      );
    };

    return (
      <>
        <Tooltip
          trigger="click"
          title={renderTooltipContent}
          visible={selectedTooltipField === id}
          placement={TOOLTIP_PLACEMENT.TOP}
          overlayClassName={styles.tooltipStyle}
          overlayInnerStyle={{ padding: 0 }}
          getPopupContainer={triggerNode => triggerNode.parentNode}>
          <ResizeAndDrag
            size={{ width, height }}
            position={{ x, y }}
            onDrag={onDrag}
            onDragStop={onDragStop}
            resizeHandleStyles={RESIZE_CURSOR_STYLE}
            onResize={onResize}
            onResizeStop={onResizeStop}
            disableDragging={disableDragging}
            enableResizing={enableResizing ? RESIZING_OPTIONS : false}
            className={cx('d-flex', styles.default, {
              [styles.customFieldStyle]: !selected && fieldType === FIELD_TYPES.CUSTOM_FIELD,
              [styles.configFieldStyle]: !selected && fieldType === FIELD_TYPES.CONFIGURATION_FIELD,
              [styles.selected]: selected,
              [styles.anchorStyle]: isAnchor,
              [styles.invalid]: isInvalid,
            })}
            onClick={onFieldClick}
            style={{ zIndex: isDragging ? 999 : 'auto', ...resizeContainerStyle }}
            data-testid="annotationContainer"
            bounds="parent" // parent classname added in pdfAnnotator for box bounds
          >
            {renderContent()}
            {isInvalid && (
              <div className={styles.invalidAlert}>
                <Tooltip trigger="hover" title={__('Field’s formula expression is invalid')} placement="top">
                  <FontIcon size={SIZES.MD} color={COLORS.red} className={styles.invalidIcon} data-testid="icon-error">
                    icon-alert1
                  </FontIcon>
                </Tooltip>
              </div>
            )}
          </ResizeAndDrag>
        </Tooltip>
        <PropertyControlledComponent controllerProperty={isDragging}>
          {/* top dragging reference line */}
          <div
            className={cx(styles.horizontalReferenceLine, {
              [styles.hidden]: !_get(topReferenceLine, 'show'),
              [styles.referenceLineAlignStyle]: _get(topReferenceLine, 'gap') === 0,
            })}
            style={{ top: _get(dragPosition, 'y', y) + _get(topReferenceLine, 'gap') }}
          />
          {/* right dragging reference line */}
          <div
            className={cx(styles.verticalReferenceLine, {
              [styles.hidden]: !_get(rightReferenceLine, 'show'),
              [styles.referenceLineAlignStyle]: _get(rightReferenceLine, 'gap') === 0,
            })}
            style={{
              left:
                _get(dragPosition, 'x', x) + _get(dragPosition, 'width', width) - _get(rightReferenceLine, 'gap') - 1,
            }}
          />
          {/* bottom dragging reference line */}
          <div
            className={cx(styles.horizontalReferenceLine, {
              [styles.hidden]: !_get(bottomReferenceLine, 'show'),
              [styles.referenceLineAlignStyle]: _get(bottomReferenceLine, 'gap') === 0,
            })}
            style={{
              top:
                _get(dragPosition, 'y', y) +
                _get(dragPosition, 'height', height) -
                _get(bottomReferenceLine, 'gap') -
                1,
            }}
          />
          {/* left dragging reference line */}
          <div
            className={cx(styles.verticalReferenceLine, {
              [styles.hidden]: !_get(leftReferenceLine, 'show'),
              [styles.referenceLineAlignStyle]: _get(leftReferenceLine, 'gap') === 0,
            })}
            style={{ left: _get(dragPosition, 'x', x) + _get(leftReferenceLine, 'gap') }}
          />
        </PropertyControlledComponent>
      </>
    );
  }
);

AnnotationField.propTypes = {
  field: PropTypes.object.isRequired,
  selected: PropTypes.bool,
  isAnchor: PropTypes.bool,
  onChange: PropTypes.func,
  onClick: PropTypes.func,
  onDuplicate: PropTypes.func,
  onDelete: PropTypes.func,
  showFieldProperties: PropTypes.func,
  setShowTooltip: PropTypes.func,
  selectedTooltipField: PropTypes.string,
  annotatedFields: PropTypes.object,
  disableDragging: PropTypes.bool,
  enableResizing: PropTypes.bool,
  resizeContainerStyle: PropTypes.object,
  formScale: PropTypes.number,
  isViewOnlyMode: PropTypes.bool,
  config: PropTypes.object,
  disableFieldActions: PropTypes.bool,
  selectedFields: PropTypes.array,
  isFcLiteModeEnabled: PropTypes.bool,
  updateSelectedFields: PropTypes.func,
  toggleGroupSelectedAnnotations: PropTypes.func,
};

AnnotationField.defaultProps = {
  selected: false,
  isAnchor: false,
  onChange: _noop,
  onClick: _noop,
  onDuplicate: _noop,
  onDelete: _noop,
  showFieldProperties: _noop,
  setShowTooltip: _noop,
  selectedTooltipField: '',
  annotatedFields: EMPTY_OBJECT,
  disableDragging: false,
  enableResizing: true,
  resizeContainerStyle: EMPTY_OBJECT,
  formScale: 1,
  isViewOnlyMode: false,
  config: EMPTY_OBJECT,
  disableFieldActions: false,
  selectedFields: EMPTY_ARRAY,
  isFcLiteModeEnabled: false,
  updateSelectedFields: _noop,
  toggleGroupSelectedAnnotations: _noop,
};

export default AnnotationField;
