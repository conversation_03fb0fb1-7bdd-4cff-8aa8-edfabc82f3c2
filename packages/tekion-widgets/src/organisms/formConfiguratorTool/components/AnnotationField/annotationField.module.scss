@import "tstyles/component.scss";

.default {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 0.1rem solid $verditer;
  background: $cosmicLatte;
  overflow: hidden;
  border-radius: 0.4rem;
  position: relative;
}

.selected {
  border: 0.1rem solid $mayaBlue;
  background: $aliceBlue;
}

.anchorStyle {
  border: 0.1rem solid $azure;
  background: $aliceBlue;
}

.tooltipStyle {
  max-width: none;

  :global(.ant-tooltip-inner) {
    background-color: $white;
    padding: 0;
    color: initial;
  }
  :global(.ant-tooltip-arrow) {
    border-top-color: $white;
  }
}

.heading {
  color: $white;
}

.fieldInfoContainer {
  height: 3.2rem;
  background-color: $lightGray;
  padding: 0.8rem;
}

.fieldContainer {
  width: max-content;
}

.fieldLabel {
  font-size: $font-size-small;
  font-family: $font-medium;
  letter-spacing: 0.3px;
  line-height: $line-height-small;
  word-wrap: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.customFieldStyle {
  border: 0.1rem solid $purpleHepatica;
  background: $contrail;
}

.invalid {
  border: 0.1rem solid $red;
  overflow: unset;
}
.invalidAlert {
  position: absolute;
  top: -1.25rem;
  left: -0.75rem;
}
.invalidIcon {
  padding: 0.5rem;
}

.configFieldStyle {
  border: 0.1rem solid $energyYellow;
  background: $ginFizz;
}

.horizontalReferenceLine {
  width: 100%;
  position: absolute;
  height: 0.1rem;
  background-color: $azure;
  z-index: 999;
}

.verticalReferenceLine {
  height: 100%;
  position: absolute;
  width: 0.1rem;
  top: 0;
  background-color: $azure;
  z-index: 999;
}

.referenceLineAlignStyle {
  background-color: $paoloVeroneseGreen;
}

.hidden {
  display: none;
}
