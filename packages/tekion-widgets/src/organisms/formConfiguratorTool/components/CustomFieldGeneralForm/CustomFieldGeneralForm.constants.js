export const FORM_FIELDS = {
  FIELD_NAME: 'name',
  FIELD_TYPE: 'type',
  FIELD_DISPLAY_NAME: 'displayName',
  FIELD_VARIATION: 'variation',
  FIELD_DESCRIPTION: 'description',
  TIME_STAMP: 'timeStampKey',
  FIELD_DEFAULT_STATE: 'fieldDefaultState',
  FIELD_READ_ONLY_VALUE: 'fieldReadOnlyValue',
  FIELD_USER_INPUT_VALUE: 'fieldUserInputValue',
  FIELD_IS_MANDATORY_FIELD: 'fieldIsMandatoryField',
  IS_SELECT_MANDATORY: 'isSelectMandatory',
  FIELD_HEADER_WITH_CHECKBOX: 'fieldHeaderWithCheckbox',
  FIELD_CUSTOM_OPTION_TABLE: 'customOptions',
  HAVE_DEFAULT_OPTIONS: 'haveDefaultOptions',
  HEADER_WITH_INFO_POPOVER: 'headerWithInfoPopover',
  FIELD_PREFIX: 'prefix',
  FIELD_SUFFIX: 'suffix',
  LANGUAGES: 'languages',
  SHOW_ON_FORM: 'showOnForm',
  CUSTOM_FIELD: 'customField',
};

export const FIELD_DEFAULT_STATE_VALUES = {
  READ_ONLY: 'READ_ONLY',
  USER_INPUT: 'USER_INPUT',
  MANDATORY_USER_SELECT: 'MANDATORY_USER_SELECT',
};

export const CHECKBOX_DEFAULT_STATE_OPTIONS = [
  {
    label: __('Read Only'),
    value: FIELD_DEFAULT_STATE_VALUES.READ_ONLY,
  },
  {
    label: __('User Input'),
    value: FIELD_DEFAULT_STATE_VALUES.USER_INPUT,
  },
];

export const FIELD_DEFAULT_STATE_OPTIONS = [
  {
    label: __('Read Only'),
    value: FIELD_DEFAULT_STATE_VALUES.READ_ONLY,
  },
  {
    label: __('User Input'),
    value: FIELD_DEFAULT_STATE_VALUES.USER_INPUT,
  },
  {
    label: __('Mandatory User Input'),
    value: FIELD_DEFAULT_STATE_VALUES.MANDATORY_USER_SELECT,
  },
];

export const DEFAULT_AFFIX_ROWS = [
  {
    columns: [FORM_FIELDS.HEADER_WITH_INFO_POPOVER],
  },
  {
    columns: [FORM_FIELDS.FIELD_PREFIX, FORM_FIELDS.FIELD_SUFFIX],
  },
];
