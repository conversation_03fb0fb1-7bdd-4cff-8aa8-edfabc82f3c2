import React, { useCallback, useState, useMemo } from 'react';
import PropTypes from 'prop-types';
import _get from 'lodash/get';
import _noop from 'lodash/noop';

import { EMPTY_OBJECT, EMPTY_ARRAY, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import Heading from '@tekion/tekion-components/src/atoms/Heading';
import FormBuilder from '@tekion/tekion-components/src/organisms/FormBuilder';
import actionTypes from '@tekion/tekion-components/src/organisms/FormBuilder/constants/actionTypes';
import MULTI_LINGUAL_FORM_ACTION_HANDLERS from '@tekion/tekion-components/src/hoc/withMultiLingualForm/MultiLingualForm.actionHandlers';
import { MULTI_LINGUAL_FORM_ACTION_TYPES } from '@tekion/tekion-components/src/hoc/withMultiLingualForm/MultiLingualForm.constants';
import withMultiLingualForm from '@tekion/tekion-components/src/hoc/withMultiLingualForm';

import withMultiLingualInfo from '../../../../hocs/withMultiLingualInfo';
import { getFields, getSections } from './CustomFieldGeneralForm.config';
import { FORM_FIELDS } from './CustomFieldGeneralForm.constants';
import styles from './customFieldGeneralForm.module.scss';
import { getTimeStampAnnotationOptions, isSignatureAnnotation } from '../../FormConfiguratorTool.utils';
import { checkIsCustomField } from '../FieldPropertiesForm/FieldPropertiesForm.utils';

const MultilingualForm = withMultiLingualForm(FormBuilder);
function CustomFieldGeneralForm(props) {
  const {
    values,
    updateFormValues,
    fieldProperties,
    annotatedFieldsAsOptions,
    annotatedCustomFields,
    supportedFieldTypes,
    customFieldGeneralFormColumns,
    customFieldRenderOptions,
    languageOptions,
    currentLanguageId,
    handleMultiLingualFieldChange,
    isFormsLibrary,
    editType,
    annotatedFields,
    isFcLiteModeEnabled,
    disableEditForFCLiteMode,
    hasViewOnlyPermission,
    isGlobalFieldByDefault,
    hideActiveFieldToggle,
  } = props;

  const [multiProps, setMultiProps] = useState({});

  const getState = useCallback(() => ({ ...values, ...multiProps }), [values, multiProps]);
  const setStateLocalState = useCallback(stateToSet => {
    setMultiProps(stateToSet);
  }, []);

  const handleAction = action => {
    const { type, payload = {} } = action;
    const { id, value, language } = payload;

    if (type === actionTypes.ON_FIELD_CHANGE) {
      updateFormValues({ id, value });
    } else if (type === MULTI_LINGUAL_FORM_ACTION_TYPES.ON_MULTI_LINGUAL_FIELD_CHANGE) {
      handleMultiLingualFieldChange({ id, value, language });
    } else if (MULTI_LINGUAL_FORM_ACTION_HANDLERS[type]) {
      MULTI_LINGUAL_FORM_ACTION_HANDLERS[type](action, {
        setState: setStateLocalState,
        getState,
      });
    }
  };

  const handleCustomOptionsUpdate = value => {
    handleAction({
      payload: {
        id: FORM_FIELDS.FIELD_CUSTOM_OPTION_TABLE,
        value,
      },
      type: actionTypes.ON_FIELD_CHANGE,
    });
  };

  const onHaveDefaultOptionsCheckChange = event => {
    const { checked } = event.target;
    handleAction({
      payload: {
        id: FORM_FIELDS.HAVE_DEFAULT_OPTIONS,
        value: checked,
      },
      type: actionTypes.ON_FIELD_CHANGE,
    });
  };

  const selectedFieldType = _get(values, `${[FORM_FIELDS.FIELD_TYPE]}[0]`, null);
  const fieldDefaultStateValue = _get(values, FORM_FIELDS.FIELD_DEFAULT_STATE, null);
  const haveDefaultOptions = _get(values, FORM_FIELDS.HAVE_DEFAULT_OPTIONS, null);
  const customOptionValues = _get(values, FORM_FIELDS.FIELD_CUSTOM_OPTION_TABLE, []);
  const haveJexlEnabled = _get(fieldProperties, 'formula.preference', null);
  const showTimeStampSelectionField = useMemo(() => isSignatureAnnotation(fieldProperties), [fieldProperties]);
  const timeStampAnnotationsOptions = useMemo(
    () => getTimeStampAnnotationOptions(annotatedFields, fieldProperties),
    [annotatedFields, fieldProperties]
  );

  const updatedFormValues = {
    ...values,
    [FORM_FIELDS.CUSTOM_FIELD]: checkIsCustomField(fieldProperties),
    [FORM_FIELDS.SHOW_ON_FORM]: _get(fieldProperties, 'active', false),
  };

  return (
    <div>
      <div>
        <Heading bold size={3}>
          {__('General')}
        </Heading>
        <p>{__('Define Field Type & Default States')}</p>
      </div>

      <div>
        <MultilingualForm
          className={styles.formContainer}
          values={updatedFormValues}
          fields={getFields({
            selectedFieldType,
            fieldDefaultStateValue,
            customOptionValues,
            handleCustomOptionsUpdate,
            haveDefaultOptions,
            onHaveDefaultOptionsCheckChange,
            annotatedFieldsAsOptions,
            annotatedCustomFields,
            haveJexlEnabled,
            supportedFieldTypes,
            customFieldGeneralFormColumns,
            customFieldRenderOptions,
            isFormsLibrary,
            editType,
            timeStampAnnotationsOptions,
            disableEditForFCLiteMode,
            hasViewOnlyPermission,
            isGlobalFieldByDefault,
            hideActiveFieldToggle,
          })}
          sections={getSections({
            selectedFieldType,
            customFieldGeneralFormColumns,
            showTimeStampSelectionField,
            isFcLiteModeEnabled,
            isGlobalFieldByDefault,
            hideActiveFieldToggle,
          })}
          onAction={handleAction}
          multiLingualFieldDetails={multiProps?.multiLingualFieldDetails}
          multiLingualFormProps={{
            ...multiProps?.multiLingualFormProps,
            languageOptions,
            currentLanguageId,
          }}
        />
      </div>
    </div>
  );
}

CustomFieldGeneralForm.propTypes = {
  values: PropTypes.object,
  annotatedFieldsAsOptions: PropTypes.array,
  annotatedCustomFields: PropTypes.array,
  updateFormValues: PropTypes.func,
  fieldProperties: PropTypes.object,
  supportedFieldTypes: PropTypes.array,
  customFieldGeneralFormColumns: PropTypes.object,
  customFieldRenderOptions: PropTypes.object,
  languageOptions: PropTypes.array,
  currentLanguageId: PropTypes.string,
  handleMultiLingualFieldChange: PropTypes.func,
  isFormsLibrary: PropTypes.bool,
  annotatedFields: PropTypes.object,
};

CustomFieldGeneralForm.defaultProps = {
  values: EMPTY_OBJECT,
  annotatedFieldsAsOptions: EMPTY_ARRAY,
  annotatedCustomFields: EMPTY_ARRAY,
  updateFormValues: _noop,
  fieldProperties: EMPTY_OBJECT,
  supportedFieldTypes: EMPTY_OBJECT,
  customFieldGeneralFormColumns: EMPTY_OBJECT,
  customFieldRenderOptions: EMPTY_OBJECT,
  languageOptions: EMPTY_ARRAY,
  currentLanguageId: EMPTY_STRING,
  handleMultiLingualFieldChange: _noop,
  isFormsLibrary: false,
  annotatedFields: EMPTY_OBJECT,
};

export default withMultiLingualInfo(CustomFieldGeneralForm);
