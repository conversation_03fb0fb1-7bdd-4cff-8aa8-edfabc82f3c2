import _includes from 'lodash/includes';
import cx from 'classnames';
import _get from 'lodash/get';
import _tail from 'lodash/tail';
import _filter from 'lodash/filter';

import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import Select from '@tekion/tekion-components/organisms/FormBuilder/fieldRenderers/select';
import SelectInput from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/SelectInput';
import TextInput from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/textInput';
import RadioGroupInput from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/radio';
import Checkbox from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/checkbox';
import SwitchWithInfoField from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/switchWithInfoField';
import { SWITCH_PLACEMENT } from '@tekion/tekion-components/src/molecules/switchWithInfo';
import CustomOptionsTable from './components/CustomFieldGeneralFormTable';
import HeaderWithCheckbox from './components/HeaderWithCheckbox/HeaderWithCheckbox';
import HeaderWithInfoPopover from '../HeaderWithInfoPopover';
import {
  FORM_FIELDS,
  FIELD_DEFAULT_STATE_OPTIONS,
  FIELD_DEFAULT_STATE_VALUES,
  CHECKBOX_DEFAULT_STATE_OPTIONS,
  DEFAULT_AFFIX_ROWS,
} from './CustomFieldGeneralForm.constants';
import {
  FIELD_TYPES_VS_RENDERERS,
  FIELD_TYPE_OPTIONS,
  FIELD_TYPE_VALUES,
  FIELD_VARIATION_OPTIONS,
} from '../../FormConfiguratorTool.constants';
import styles from './customFieldGeneralForm.module.scss';
import { getSupportedFieldTypeOptions, disableFieldBasedOnPermission } from '../../FormConfiguratorTool.utils';

const INPUT_SECTIONS = [
  FIELD_TYPE_VALUES.FREE_TEXT,
  FIELD_TYPE_VALUES.TEXT_AREA,
  FIELD_TYPE_VALUES.DATE,
  FIELD_TYPE_VALUES.PRICE,
  FIELD_TYPE_VALUES.CHECKBOX,
  FIELD_TYPE_VALUES.PHONE,
];

const getInputSection = customFieldGeneralFormColumns => {
  const showIsMandatoryField = _get(customFieldGeneralFormColumns, 'fieldStates.showIsMandatoryField', true);
  const showReadOnlyField = _get(customFieldGeneralFormColumns, 'fieldStates.showReadOnlyField', true);
  const defaultStateFields = showReadOnlyField
    ? [FORM_FIELDS.FIELD_READ_ONLY_VALUE, FORM_FIELDS.FIELD_USER_INPUT_VALUE]
    : [FORM_FIELDS.FIELD_USER_INPUT_VALUE];
  const fieldStatesRows = [
    {
      columns: [FORM_FIELDS.FIELD_DEFAULT_STATE],
    },
    {
      columns: defaultStateFields,
    },
  ];

  if (showIsMandatoryField) {
    fieldStatesRows.push({
      columns: [FORM_FIELDS.FIELD_IS_MANDATORY_FIELD],
    });
  }

  return [
    {
      header: {
        label: __('Field States'),
        size: 5,
      },
      fieldIds: [
        FORM_FIELDS.FIELD_DEFAULT_STATE,
        FORM_FIELDS.FIELD_READ_ONLY_VALUE,
        FORM_FIELDS.FIELD_USER_INPUT_VALUE,
        FORM_FIELDS.FIELD_IS_MANDATORY_FIELD,
      ],
      rows: fieldStatesRows,
    },
  ];
};

const getCustomOptionSection = () => [
  {
    header: {
      label: __('Field Values'),
      size: 5,
    },
    fieldIds: [
      FORM_FIELDS.IS_SELECT_MANDATORY,
      FORM_FIELDS.HAVE_DEFAULT_OPTIONS,
      FORM_FIELDS.FIELD_CUSTOM_OPTION_TABLE,
    ],
    rows: [
      {
        columns: [FORM_FIELDS.IS_SELECT_MANDATORY, FORM_FIELDS.HAVE_DEFAULT_OPTIONS],
        className: 'd-flex justify-content-end',
      },
      {
        columns: [FORM_FIELDS.FIELD_CUSTOM_OPTION_TABLE],
      },
    ],
  },
];

const getCustomFieldRows = ({ showTimeStampSelectionField, customFieldToggle, showOnFormToggle }) => [
  {
    columns: [...showOnFormToggle, ...customFieldToggle],
  },
  {
    columns: [FORM_FIELDS.FIELD_NAME, FORM_FIELDS.FIELD_TYPE],
  },
  {
    columns: [FORM_FIELDS.FIELD_DISPLAY_NAME, FORM_FIELDS.FIELD_VARIATION],
  },
  {
    columns: [FORM_FIELDS.FIELD_DESCRIPTION, ...(showTimeStampSelectionField ? [FORM_FIELDS.TIME_STAMP] : [])],
  },
];

export const getSections = ({
  selectedFieldType,
  customFieldGeneralFormColumns,
  showTimeStampSelectionField,
  isFcLiteModeEnabled,
  isGlobalFieldByDefault,
  hideActiveFieldToggle,
}) => {
  const customFieldToggle = isGlobalFieldByDefault ? [FORM_FIELDS.CUSTOM_FIELD] : [];
  const showOnFormToggle = !hideActiveFieldToggle ? [FORM_FIELDS.SHOW_ON_FORM] : [];

  const fieldConfigurationsRows =
    _get(customFieldGeneralFormColumns, 'fieldConfigurationsRows') ||
    getCustomFieldRows({ showTimeStampSelectionField, customFieldToggle, showOnFormToggle });

  const affixRows = _get(customFieldGeneralFormColumns, 'affixRows') || DEFAULT_AFFIX_ROWS;

  const sections = [
    {
      header: {
        label: __('Field Configurations'),
        size: 5,
      },
      fieldIds: [
        FORM_FIELDS.SHOW_ON_FORM,
        FORM_FIELDS.CUSTOM_FIELD,
        FORM_FIELDS.FIELD_NAME,
        FORM_FIELDS.FIELD_TYPE,
        FORM_FIELDS.FIELD_VARIATION,
        FORM_FIELDS.FIELD_DESCRIPTION,
      ],
      rows: fieldConfigurationsRows,
    },
    ...(isFcLiteModeEnabled
      ? EMPTY_ARRAY
      : [
          {
            fieldIds: [FORM_FIELDS.FIELD_PREFIX, FORM_FIELDS.FIELD_SUFFIX],
            rows: affixRows,
          },
        ]),
  ];

  if (_includes(INPUT_SECTIONS, selectedFieldType)) {
    const section = getInputSection(customFieldGeneralFormColumns);
    sections.push(...section);
  }

  if (selectedFieldType === FIELD_TYPE_VALUES.SINGLE_SELECT || selectedFieldType === FIELD_TYPE_VALUES.MULTI_SELECT) {
    const section = getCustomOptionSection();
    sections.push(...section);
    sections.push({
      header: {
        label: __('Field States'),
        size: 5,
      },
      fieldIds: [FORM_FIELDS.FIELD_IS_MANDATORY_FIELD],
      rows: [
        {
          columns: [FORM_FIELDS.FIELD_IS_MANDATORY_FIELD],
        },
      ],
    });
  }

  return sections;
};

const getDefaultStateOptions = showReadOnlyField => {
  if (!showReadOnlyField) return _tail(FIELD_DEFAULT_STATE_OPTIONS);
  return FIELD_DEFAULT_STATE_OPTIONS;
};

// static fields as for now, can be dynamic later or change as per requirements
export const getFields = ({
  selectedFieldType,
  fieldDefaultStateValue,
  customOptionValues,
  handleCustomOptionsUpdate,
  haveDefaultOptions,
  onHaveDefaultOptionsCheckChange,
  annotatedFieldsAsOptions,
  annotatedCustomFields,
  haveJexlEnabled,
  supportedFieldTypes,
  customFieldGeneralFormColumns,
  customFieldRenderOptions,
  isFormsLibrary,
  editType,
  timeStampAnnotationsOptions = [],
  disableEditForFCLiteMode,
  hasViewOnlyPermission,
}) => {
  const inputTypes = {
    ...FIELD_TYPES_VS_RENDERERS,
    [FIELD_TYPE_VALUES.SIGNATURE]: TextInput,
    [FIELD_TYPE_VALUES.CHECKBOX]: TextInput,
  };

  const userInputFieldLabel =
    selectedFieldType === FIELD_TYPE_VALUES.CHECKBOX ? __('Print Value') : __('User Input Default Value');

  const showReadOnlyField = _get(customFieldGeneralFormColumns, 'fieldStates.showReadOnlyField', true);
  const defaultStateOptions =
    selectedFieldType === FIELD_TYPE_VALUES.CHECKBOX
      ? CHECKBOX_DEFAULT_STATE_OPTIONS
      : getDefaultStateOptions(showReadOnlyField);
  const isDisableBasedOnPermission = disableFieldBasedOnPermission({
    isFormsLibrary,
    editType,
  });

  return {
    [FORM_FIELDS.FIELD_NAME]: {
      renderer: TextInput,
      renderOptions: {
        required: true,
        label: __('Field Name'),
        placeholder: __('Enter Field Name'),
        ..._get(customFieldRenderOptions, [FORM_FIELDS.FIELD_NAME], EMPTY_OBJECT),
        disabled: isDisableBasedOnPermission || disableEditForFCLiteMode,
      },
    },
    [FORM_FIELDS.FIELD_TYPE]: {
      renderer: SelectInput,
      renderOptions: {
        required: true,
        label: __('Field Type'),
        placeholder: __('Select Field Type'),
        options: getSupportedFieldTypeOptions(
          _filter(FIELD_TYPE_OPTIONS, ({ value }) => value !== FIELD_TYPE_VALUES.TIMESTAMP),
          supportedFieldTypes
        ),
        ..._get(customFieldRenderOptions, [FORM_FIELDS.FIELD_TYPE], EMPTY_OBJECT),
        isDisabled: isDisableBasedOnPermission || disableEditForFCLiteMode,
      },
    },
    [FORM_FIELDS.FIELD_VARIATION]: {
      renderer: SelectInput,
      renderOptions: {
        label: __('Field Formatting'),
        placeholder: __('Select Field Formatting'),
        options: FIELD_VARIATION_OPTIONS[selectedFieldType],
        required: true,
        ..._get(customFieldRenderOptions, [FORM_FIELDS.FIELD_VARIATION], EMPTY_OBJECT),
        isDisabled:
          isDisableBasedOnPermission ||
          [
            FIELD_TYPE_VALUES.MULTI_SELECT,
            FIELD_TYPE_VALUES.SINGLE_SELECT,
            FIELD_TYPE_VALUES.CHECKBOX,
            FIELD_TYPE_VALUES.SIGNATURE,
          ].includes(selectedFieldType),
      },
    },
    [FORM_FIELDS.FIELD_DISPLAY_NAME]: {
      renderer: TextInput,
      renderOptions: {
        label: __('Display Name'),
        placeholder: __('Field Display Name'),
        ..._get(customFieldRenderOptions, [FORM_FIELDS.FIELD_DISPLAY_NAME], EMPTY_OBJECT),
        isMultiLingual: !isFormsLibrary,
      },
    },
    [FORM_FIELDS.FIELD_DESCRIPTION]: {
      renderer: TextInput,
      renderOptions: {
        label: __('Field Description'),
        placeholder: __('Tell User about Field.'),
        ..._get(customFieldRenderOptions, [FORM_FIELDS.FIELD_DESCRIPTION], EMPTY_OBJECT),
        disabled: isDisableBasedOnPermission,
        fieldClassName: styles.halfField,
      },
    },
    [FORM_FIELDS.TIME_STAMP]: {
      renderer: Select,
      renderOptions: {
        label: __('Timestamp'),
        placeholder: __('Select timestamp annotation'),
        allowClear: true,
        fieldClassName: styles.halfField,
        options: timeStampAnnotationsOptions,
      },
    },
    [FORM_FIELDS.FIELD_READ_ONLY_VALUE]: {
      renderer: inputTypes[selectedFieldType],
      renderOptions: {
        label: __('Read Only Default Value'),
        placeholder: __('Enter Default Value'),
        required: true,
        disabled:
          isDisableBasedOnPermission ||
          fieldDefaultStateValue === FIELD_DEFAULT_STATE_VALUES.USER_INPUT ||
          fieldDefaultStateValue === FIELD_DEFAULT_STATE_VALUES.MANDATORY_USER_SELECT,
        ..._get(customFieldRenderOptions, [FORM_FIELDS.FIELD_READ_ONLY_VALUE], EMPTY_OBJECT),
      },
    },
    [FORM_FIELDS.FIELD_USER_INPUT_VALUE]: {
      renderer: inputTypes[selectedFieldType],
      renderOptions: {
        label: userInputFieldLabel,
        placeholder: __(''),
        required:
          selectedFieldType === FIELD_TYPE_VALUES.CHECKBOX &&
          fieldDefaultStateValue === FIELD_DEFAULT_STATE_VALUES.USER_INPUT,
        disabled:
          isDisableBasedOnPermission ||
          fieldDefaultStateValue === FIELD_DEFAULT_STATE_VALUES.READ_ONLY ||
          haveJexlEnabled ||
          fieldDefaultStateValue === FIELD_DEFAULT_STATE_VALUES.MANDATORY_USER_SELECT,
        ..._get(customFieldRenderOptions, [FORM_FIELDS.FIELD_USER_INPUT_VALUE], EMPTY_OBJECT),
      },
    },
    [FORM_FIELDS.FIELD_DEFAULT_STATE]: {
      renderer: RadioGroupInput,
      renderOptions: {
        label: __('Default State'),
        radios: defaultStateOptions,
        disabled: isDisableBasedOnPermission,
        ..._get(customFieldRenderOptions, [FORM_FIELDS.FIELD_DEFAULT_STATE], EMPTY_OBJECT),
      },
    },
    [FORM_FIELDS.FIELD_IS_MANDATORY_FIELD]: {
      renderer: Checkbox,
      renderOptions: {
        checkboxLabel: __('Display Default Value as N/A'),
        disabled:
          isDisableBasedOnPermission || fieldDefaultStateValue === FIELD_DEFAULT_STATE_VALUES.MANDATORY_USER_SELECT,
        ..._get(customFieldRenderOptions, [FORM_FIELDS.FIELD_IS_MANDATORY_FIELD], EMPTY_OBJECT),
      },
    },
    [FORM_FIELDS.HAVE_DEFAULT_OPTIONS]: {
      renderer: Checkbox,
      renderOptions: {
        checkboxLabel: __('Keep Default Options'),
        fieldClassName: cx('flex-grow-0 flex-shrik-1 mb-1', styles.autoFlexBasis),
        ..._get(customFieldRenderOptions, [FORM_FIELDS.HAVE_DEFAULT_OPTIONS], EMPTY_OBJECT),
      },
    },
    [FORM_FIELDS.IS_SELECT_MANDATORY]: {
      renderer: Checkbox,
      renderOptions: {
        checkboxLabel: __('Mandatory User Input'),
        fieldClassName: cx('flex-grow-0 flex-shrik-1 mb-1', styles.autoFlexBasis),
        ..._get(customFieldRenderOptions, [FORM_FIELDS.IS_SELECT_MANDATORY], EMPTY_OBJECT),
      },
    },
    [FORM_FIELDS.FIELD_CUSTOM_OPTION_TABLE]: {
      renderer: CustomOptionsTable,
      renderOptions: {
        values: customOptionValues,
        updateValues: handleCustomOptionsUpdate,
        isMultiSelect: selectedFieldType === FIELD_TYPE_VALUES.MULTI_SELECT,
        haveDefaultOptions,
        annotatedFieldsAsOptions,
        annotatedCustomFields,
        ..._get(customFieldRenderOptions, [FORM_FIELDS.FIELD_CUSTOM_OPTION_TABLE], EMPTY_OBJECT),
      },
    },
    [FORM_FIELDS.FIELD_HEADER_WITH_CHECKBOX]: {
      renderer: HeaderWithCheckbox,
      renderOptions: {
        headerText: __('Field Values'),
        checkBoxLabel: __('Keep Default Options'),
        checked: haveDefaultOptions,
        onCheckBoxChange: onHaveDefaultOptionsCheckChange,
        ..._get(customFieldRenderOptions, [FORM_FIELDS.FIELD_HEADER_WITH_CHECKBOX], EMPTY_OBJECT),
      },
    },
    [FORM_FIELDS.FIELD_PREFIX]: {
      renderer: TextInput,
      renderOptions: {
        label: __('Prefix'),
        placeholder: __('Enter'),
        ..._get(customFieldRenderOptions, [FORM_FIELDS.FIELD_PREFIX], EMPTY_OBJECT),
        disabled: isDisableBasedOnPermission,
      },
    },
    [FORM_FIELDS.HEADER_WITH_INFO_POPOVER]: {
      renderer: HeaderWithInfoPopover,
      renderOptions: {
        headingSize: 5,
        headerLabel: 'Affix',
        helpText: 'Text About Affix comes here',
        ..._get(customFieldRenderOptions, [FORM_FIELDS.HEADER_WITH_INFO_POPOVER], EMPTY_OBJECT),
      },
    },
    [FORM_FIELDS.FIELD_SUFFIX]: {
      renderer: TextInput,
      renderOptions: {
        label: __('Suffix'),
        placeholder: __('Enter'),
        ..._get(customFieldRenderOptions, [FORM_FIELDS.FIELD_SUFFIX], EMPTY_OBJECT),
        disabled: isDisableBasedOnPermission,
      },
    },
    [FORM_FIELDS.SHOW_ON_FORM]: {
      renderer: SwitchWithInfoField,
      renderOptions: {
        label: __('Show on Form'),
        switchPlacement: SWITCH_PLACEMENT.START,
        switchSize: 'small',
        labelClassName: styles.labelClassName,
        switchWithInfoClassName: styles.switchWithInfoClassName,
        disabled: hasViewOnlyPermission,
      },
    },
    [FORM_FIELDS.CUSTOM_FIELD]: {
      renderer: SwitchWithInfoField,
      renderOptions: {
        label: __('Custom Field'),
        switchPlacement: SWITCH_PLACEMENT.START,
        switchSize: 'small',
        labelClassName: styles.labelClassName,
        switchWithInfoClassName: styles.switchWithInfoClassName,
        disabled: disableFieldBasedOnPermission({
          disableActions: hasViewOnlyPermission,
          isFormsLibrary,
          editType,
        }),
      },
    },
  };
};
