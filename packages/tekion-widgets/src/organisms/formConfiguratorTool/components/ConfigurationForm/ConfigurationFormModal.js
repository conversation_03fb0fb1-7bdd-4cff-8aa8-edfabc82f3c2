import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { compose } from 'recompose';

import _noop from 'lodash/noop';
import _get from 'lodash/get';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import withActionHandlers from '@tekion/tekion-components/src/connectors/withActionHandlers';
import Modal from '@tekion/tekion-components/src/molecules/Modal';
import ACTION_TYPES from './ConfigurationForm.actionTypes';
import ConfigurationForm from './ConfigurationForm';
import FORM_ACTION_HANDLERS from './ConfigurationForm.actionHandlers';
import { shouldActionDisabled, getConfigFieldDisplayTitle } from './ConfigurationForm.utils';
import styles from './configFormModal.module.scss';

const BODY_STYLE = {
  height: '60rem',
  paddingTop: '0rem',
  paddingBottom: '0rem',
};

class ConfigurationFormModal extends PureComponent {
  handleNewFormSubmit = async () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_CONFIGURATION_FORM_SUBMIT,
    });
  };

  handleToggleActiveField = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_CONFIGURATION_FIELD_ACTIVE,
    });
  };

  renderHeader = () => {
    const { selectedField, configurationValue, isEdit } = this.props;
    return (
      <div className={styles.headerWrapper}>
        {getConfigFieldDisplayTitle(selectedField, configurationValue, isEdit)}
      </div>
    );
  };

  render() {
    const { configurationValue, errors, formScale, pageDimensions } = this.props;
    const { showModal, toggleModal, isEdit, isFormSubmitInProgress, hasViewOnlyPermission } = this.props;

    return (
      <Modal
        onCancel={toggleModal}
        onSubmit={this.handleNewFormSubmit}
        secondaryBtnText={hasViewOnlyPermission ? __('Close') : __('Cancel')}
        submitBtnText={isEdit ? __('Update') : __('Save')}
        title={this.renderHeader()}
        visible={showModal}
        width={Modal.SIZES.L}
        bodyStyle={BODY_STYLE}
        destroyOnClose
        hideSubmit={hasViewOnlyPermission}
        okButtonProps={{
          disabled: shouldActionDisabled(configurationValue, errors, formScale, pageDimensions),
          loading: isFormSubmitInProgress,
        }}>
        <ConfigurationForm {...this.props} />
      </Modal>
    );
  }
}

ConfigurationFormModal.propTypes = {
  toggleModal: PropTypes.func.isRequired,
  isFormSubmitInProgress: PropTypes.bool,
  showModal: PropTypes.bool,
  formValues: PropTypes.object,
  configurationValue: PropTypes.object,
  commonProperties: PropTypes.object,
  errors: PropTypes.object,
  onAction: PropTypes.func,
  isEdit: PropTypes.bool,
  selectedField: PropTypes.object,
  formScale: PropTypes.number.isRequired,
  pageDimensions: PropTypes.object,
};

ConfigurationFormModal.defaultProps = {
  showModal: false,
  formValues: EMPTY_OBJECT,
  configurationValue: EMPTY_OBJECT,
  commonProperties: EMPTY_OBJECT,
  errors: EMPTY_OBJECT,
  isFormSubmitInProgress: false,
  onAction: _noop,
  isEdit: false,
  selectedField: EMPTY_OBJECT,
  pageDimensions: EMPTY_OBJECT,
};

export default compose(withActionHandlers(FORM_ACTION_HANDLERS))(ConfigurationFormModal);
