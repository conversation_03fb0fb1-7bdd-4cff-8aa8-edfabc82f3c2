// Lodash
import _filter from 'lodash/filter';
import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import GeneralConfig from './components/GeneralConfig';
import TextFormatting from '../TextFormatting';
import Dimensions from '../Dimensions';
import Affix from '../Affix';
import Offsets from '../Offsets/Offsets';
import { ANNOTATION_MODAL_TYPES } from '../../FormConfiguratorTool.constants';
import { CONFIG_KEYS, CONFIGURATION_FORM_SECTIONS, CONFIGURATION_FORM_TABS } from './ConfigurationForm.constants';

export const CONFIGURATION_FORM_TAB_KEY_VS_RENDERER = {
  [CONFIGURATION_FORM_TABS.GENERAL_CONFIG]: GeneralConfig,
  [CONFIGURATION_FORM_TABS.TEXT_FORMATTING]: TextFormatting,
  [CONFIGURATION_FORM_TABS.OFFSETS]: Offsets,
  [CONFIGURATION_FORM_TABS.DIMENSIONS]: Dimensions,
  [CONFIGURATION_FORM_TABS.AFFIX]: Affix,
};

const pickGeneralTabProps = props => {
  const {
    metaData,
    fields,
    annotatedFields,
    isEdit,
    productsList,
    configuratorMetaData,
    isGlobalForm,
    configurationValue,
    hasViewOnlyPermission,
    hideActiveFieldToggle,
  } = props;
  return {
    metaData,
    fields,
    annotatedFields,
    isEdit,
    productsList,
    configuratorMetaData,
    isGlobalForm,
    configurationValue,
    hasViewOnlyPermission,
    hideActiveFieldToggle,
  };
};

const pickTextFormattingTabProps = props => {
  const { configuratorMetaData, formPrinterType } = props;
  return {
    modalType: ANNOTATION_MODAL_TYPES.GLOBAL_FIELD,
    configuratorMetaData,
    formPrinterType,
  };
};

const pickOffsetTabProps = () => EMPTY_OBJECT;

const pickDimensionsTabProps = () => EMPTY_OBJECT;

const pickAffixTabProps = () => EMPTY_OBJECT;

export const getAdditionalProps = {
  [CONFIGURATION_FORM_TABS.GENERAL_CONFIG]: pickGeneralTabProps,
  [CONFIGURATION_FORM_TABS.TEXT_FORMATTING]: pickTextFormattingTabProps,
  [CONFIGURATION_FORM_TABS.OFFSETS]: pickOffsetTabProps,
  [CONFIGURATION_FORM_TABS.DIMENSIONS]: pickDimensionsTabProps,
  [CONFIGURATION_FORM_TABS.AFFIX]: pickAffixTabProps,
};

export const getConfigurationFormSections = isFcLiteModeEnabled => {
  if (isFcLiteModeEnabled) {
    return _filter(CONFIGURATION_FORM_SECTIONS, section => section !== CONFIG_KEYS.AFFIX);
  }

  return CONFIGURATION_FORM_SECTIONS;
};
