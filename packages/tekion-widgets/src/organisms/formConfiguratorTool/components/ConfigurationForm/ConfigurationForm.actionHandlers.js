import produce from 'immer';
import _set from 'lodash/set';
import _get from 'lodash/get';
import _filter from 'lodash/filter';
import _find from 'lodash/find';
import _head from 'lodash/head';
import _includes from 'lodash/includes';
import _round from 'lodash/round';
import _unset from 'lodash/unset';

import FORM_ACTION_TYPES from '@tekion/tekion-components/src/organisms/FormBuilder/constants/actionTypes';
import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import ACTION_TYPES from './ConfigurationForm.actionTypes';
import { GENERAL_CONFIG_FIELDS, CONFIG_KEYS } from './ConfigurationForm.constants';
import { getUpdatedFieldsTargettingSettingValues } from './ConfigurationForm.utils';
import {
  DEFAULT_FIELD_HEIGHT,
  DEFAULT_FIELD_WIDTH,
  DEFAULT_SCALE,
  LASER_FONT_VALUES,
  IMPACT_FONT_VALUES,
} from '../../FormConfiguratorTool.constants';
import {
  getAnnotationCoordinatesFromTop,
  getDefaultFieldTextFormatting,
  getOffsetsAndPositions,
  getDefaultFieldGeneral,
} from '../../FormConfiguratorTool.utils';
import { DEFAULT_FIELD_KEYS } from './components/GeneralConfig/GeneralConfig.constants';
import { LIMIT_TYPES } from '../TextFormatting/TextFormatting.constants';

const handlePageMount = (action, { setState, getState }) => {
  const { selectedConfig = {}, commonProperties, formPrinterType, formScale, pageDimensions } = getState();
  const { textFormat = {}, offsets = {}, ...rest } = selectedConfig || {};
  const dimensions = {
    width: _get(rest, 'actualWidth', DEFAULT_FIELD_WIDTH),
    height: _get(rest, 'actualHeight', DEFAULT_FIELD_HEIGHT),
  };
  let textFormatting;
  if (selectedConfig && selectedConfig.textFormat) {
    if (formPrinterType === 'LASER') {
      if (_includes(LASER_FONT_VALUES, selectedConfig.textFormat.font)) {
        textFormatting = selectedConfig.textFormat;
      } else {
        textFormatting = { ...getDefaultFieldTextFormatting(formPrinterType) };
      }
    }
    if (formPrinterType === 'IMPACT') {
      if (_includes(IMPACT_FONT_VALUES, selectedConfig.textFormat.font)) {
        textFormatting = selectedConfig.textFormat;
      } else {
        textFormatting = { ...getDefaultFieldTextFormatting(formPrinterType) };
      }
    }
  } else {
    textFormatting = { ...getDefaultFieldTextFormatting(formPrinterType), ...textFormat };
  }

  const common = {
    textFormat: textFormatting,
    general: {
      ...getDefaultFieldGeneral(),
      default: DEFAULT_FIELD_KEYS.BLANK,
    },
    ...commonProperties,
    offsets: { ...offsets },
  };
  setState({
    configurationValue: {
      ...(selectedConfig || EMPTY_OBJECT),
      common,
      textFormat: textFormatting,
      offsets: getOffsetsAndPositions(selectedConfig, formScale, pageDimensions),
      dimensions,
    },
  });
};

const handleFieldChange = (action, { getState, setState }) => {
  const { payload } = action;
  const { configurationValue } = getState();
  const { category, id, value, isEdit } = payload;
  if (category) {
    setState(
      produce(draft => {
        if (!isEdit) {
          if (category === CONFIG_KEYS.TEXT_FORMATTING || category === CONFIG_KEYS.OFFSETS) {
            _set(draft, `configurationValue.${category}.${id}`, value);
            _set(draft, `configurationValue.common.${category}.${id}`, value);
            if (id === 'limitOption') {
              const selectedValue = value;
              switch (selectedValue) {
                case LIMIT_TYPES.LIMIT:
                  _set(draft, `configurationValue.textFormat.textCombing`, null);
                  break;
                case LIMIT_TYPES.COMB:
                  _set(draft, `configurationValue.textFormat.maxLength`, null);
                  break;
                default:
                  _set(draft, `configurationValue.textFormat.textCombing`, null);
              }
            }
          }
        } else if (isEdit) {
          if (category === CONFIG_KEYS.TEXT_FORMATTING || category === CONFIG_KEYS.OFFSETS) {
            _set(draft, `configurationValue.${category}.${id}`, value);
            if (id === 'limitOption') {
              const selectedValue = value;
              switch (selectedValue) {
                case LIMIT_TYPES.LIMIT:
                  _set(draft, `configurationValue.textFormat.textCombing`, null);
                  break;
                case LIMIT_TYPES.COMB:
                  _set(draft, `configurationValue.textFormat.maxLength`, null);
                  break;
                default:
                  _set(draft, `configurationValue.textFormat.textCombing`, null);
              }
            }
          }
        }
      })
    );

    if (category === CONFIG_KEYS.GENERAL_CONFIG) {
      setState(
        produce(draft => {
          _set(draft, `configurationValue.common.${category}.${id}`, value);
        })
      );

      if (id === GENERAL_CONFIG_FIELDS.ROW_FIELD) {
        const existingFields = _get(configurationValue, `common.${category}.field`);
        const deletedFields = _filter(existingFields, field => !_includes(value, field));
        const newFields = _filter(value, val => !_find(existingFields, existingVal => val === existingVal));
        const existingValues = _get(configurationValue, `common.${category}.fieldTargets`) || [];

        let _values;
        if (newFields.length > 0) {
          const newTargettingValues = getUpdatedFieldsTargettingSettingValues(newFields);
          _values = [...existingValues, ...newTargettingValues];
        } else {
          const deleteVals = _filter(existingValues, val => val.fieldsTargettingConfigField !== _head(deletedFields));
          _values = [...deleteVals];
        }
        setState(
          produce(draft => {
            _set(
              draft,
              `configurationValue.common.${category}.${GENERAL_CONFIG_FIELDS.FIELDS_TARGETTING_RULES}`,
              _values
            );
          })
        );
      }
    }

    if (category === CONFIG_KEYS.DIMENSIONS || category === CONFIG_KEYS.AFFIX) {
      setState(
        produce(draft => {
          _set(draft, `configurationValue.${category}.${id}`, value);
        })
      );
    }

    if (id === GENERAL_CONFIG_FIELDS.SHOW_ON_FORM) {
      handleToggleActiveField(action, { getState, setState });
    }

    return;
  }

  setState({
    hasFormChanged: true,
    configurationValue: {
      ...configurationValue,
      [payload.id]: payload.value,
    },
  });
};

const handleFormSubmit = (action, { getState }) => {
  const { configurationValue, onSubmit, formScale, pageDimensions } = getState();
  const scale = formScale / DEFAULT_SCALE;
  onSubmit(
    produce(configurationValue, draft => {
      _set(draft, 'width', _round(_get(configurationValue, 'dimensions.width') * scale, 2));
      _set(draft, `height`, _round(_get(configurationValue, 'dimensions.height') * scale, 2));
      _set(draft, 'actualWidth', _get(configurationValue, 'dimensions.width'));
      _set(draft, `actualHeight`, _get(configurationValue, 'dimensions.height'));
      _set(draft, `active`, _get(configurationValue, 'active', false));

      const { x, y } = getAnnotationCoordinatesFromTop(
        _get(configurationValue, 'offsets.left'),
        _get(configurationValue, 'offsets.top'),
        _get(configurationValue, 'page'),
        pageDimensions,
        formScale
      );

      _set(draft, 'x', x);
      _set(draft, 'y', y);
      _unset(draft, 'offsets.top');
      _unset(draft, 'offsets.left');
      _unset(draft, 'dimensions');
    })
  );
};

const handleToggleActiveField = (_, { setState, getState }) => {
  const { configurationValue } = getState();
  setState({
    configurationValue: {
      ...configurationValue,
      active: !_get(configurationValue, 'active', false),
    },
  });
};

const ACTION_HANDLERS = {
  [FORM_ACTION_TYPES.ON_FIELD_CHANGE]: handleFieldChange,
  [ACTION_TYPES.ON_CONFIGURATION_FORM_MOUNT]: handlePageMount,
  [ACTION_TYPES.ON_CONFIGURATION_FORM_SUBMIT]: handleFormSubmit,
  [ACTION_TYPES.ON_CONFIGURATION_FIELD_ACTIVE]: handleToggleActiveField,
};

export default ACTION_HANDLERS;
