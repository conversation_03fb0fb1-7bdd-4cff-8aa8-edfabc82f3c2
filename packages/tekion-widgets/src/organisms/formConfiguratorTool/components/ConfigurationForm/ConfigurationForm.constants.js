import _omit from 'lodash/omit';
import _values from 'lodash/values';

// import { CONFIG_FIELD_TYPES } from './components/FieldsTargetting/FieldsTargetting.constant'; //check

const CONFIG_FIELD_TYPES = {
  ACCESSORY: 'accConfig',
  FNI: 'fniConfig',
  FEE: 'feeConfig',
  REBATE: 'rebateConfig',
  CASHIERING: 'paymentConfig',
  COMMISSION: 'formCommissionConfig',
  OPTION: 'option',
};

export const CONFIG_KEYS = {
  GENERAL_CONFIG: 'general',
  TEXT_FORMATTING: 'textFormat',
  OFFSETS: 'offsets',
  RULES: 'rules',
  FORMULA: 'formula',
  DIMENSIONS: 'dimensions',
  AFFIX: 'affix',
};

export const GENERAL_CONFIG_FIELDS = {
  CONFIG_NAME: 'configName',
  ROWS: 'numberOfRows',
  ROW_KEY: 'key',
  ROW_FIELD: 'field',
  ROW_VALUES: 'value',
  FIELDS_TARGETTING_RULES: 'fieldTargets',
  DELIMITER: 'delimiter',
  DEFAULT: 'default',
  CUSTOM_VALUE: 'customValue',
  SHOW_ON_FORM: 'showOnForm',
};

export const REQUIRED_FORM_FIELDS = [
  GENERAL_CONFIG_FIELDS.CONFIG_NAME,
  GENERAL_CONFIG_FIELDS.ROW_KEY,
  GENERAL_CONFIG_FIELDS.ROW_FIELD,
  GENERAL_CONFIG_FIELDS.ROW_VALUES,
];

export const REQUIRED_FORM_NUMERICAL_FIELDS = [GENERAL_CONFIG_FIELDS.ROWS];

export const LEFT_PANE_SECTIONS = [
  { id: CONFIG_KEYS.GENERAL_CONFIG, label: __('General Configurations') },
  {
    id: CONFIG_KEYS.TEXT_FORMATTING,
    label: __('Text Formatting'),
  },
  {
    id: CONFIG_KEYS.OFFSETS,
    label: __('Offsets'),
  },
];

export const CONFIGURATION_FORM_TABS = {
  GENERAL_CONFIG: CONFIG_KEYS.GENERAL_CONFIG,
  TEXT_FORMATTING: CONFIG_KEYS.TEXT_FORMATTING,
  OFFSETS: CONFIG_KEYS.OFFSETS,
  DIMENSIONS: CONFIG_KEYS.DIMENSIONS,
  AFFIX: CONFIG_KEYS.AFFIX,
};

export const CONFIGURATION_FORM_TAB_NAMES = {
  [CONFIGURATION_FORM_TABS.GENERAL_CONFIG]: __('General'),
  [CONFIGURATION_FORM_TABS.TEXT_FORMATTING]: __('Text Formatting'),
  [CONFIGURATION_FORM_TABS.OFFSETS]: __('Offsets'),
  [CONFIGURATION_FORM_TABS.DIMENSIONS]: __('Dimensions'),
  [CONFIGURATION_FORM_TABS.AFFIX]: __('Affix'),
};

export const CONFIGURATION_FORM_SECTIONS = [
  CONFIG_KEYS.GENERAL_CONFIG,
  CONFIG_KEYS.TEXT_FORMATTING,
  CONFIG_KEYS.OFFSETS,
  CONFIG_KEYS.DIMENSIONS,
  CONFIG_KEYS.AFFIX,
];

export const TARGETING_TYPES = {
  INCLUDE: 'include',
  EXCLUDE: 'exclude',
};

export const TYPES = {
  DISCLOSURE_TYPES: 'disclosureType',
  // specific to fniConfig
  PRODUCTS: 'products',
  PRODUCT_COVERAGE_TYPE: 'productCoverageType',
  // specific to feeConfig
  FEE_TYPES: 'feeType',
  // specific to accConfig
  ACCESSORY_TYPES: 'partsOrServices',
  PAY_TYPES: 'paidBy',
  // specific to rebateConfig
  REBATE_TYPES: 'rebateType',
  // specific to paymentConfig
  PAYMENT_TYPES: 'paymentType',
  // specific to commissionConfig
  COMMISSION_PLAN: 'commissionPlan',
  ASSIGNEE_TYPE: 'assigneeType',
  FNI_PRODUCTS: 'productsList',
  ACCESSORY_CODE: 'accessoryCode',
};

export const TYPES_DISPLAY_NAME = {
  [TYPES.DISCLOSURE_TYPES]: __('Disclosure Type'),
  [TYPES.PRODUCTS]: __('Products'),
  [TYPES.PRODUCT_COVERAGE_TYPE]: __('Coverage Type'),
  [TYPES.FEE_TYPES]: __('Fee Type'),
  [TYPES.ACCESSORY_TYPES]: __('Accessory Type'),
  [TYPES.PAY_TYPES]: __('Pay Type[[cashieringPayType]]'),
  [TYPES.REBATE_TYPES]: __('Rebate Type'),
  [TYPES.PAYMENT_TYPES]: __('Payment Type'),
  [TYPES.COMMISSION_PLAN]: __('Commission Plan'),
  [TYPES.ASSIGNEE_TYPE]: __('Assignee Type'),
  [TYPES.FNI_PRODUCTS]: __('F&I products'),
  [TYPES.ACCESSORY_CODE]: __('Accessory'),
};

export const TARGETING_DISPLAY_NAME = {
  include: __('IN'),
  exclude: __('EX'),
};

export const FIELD_SETTINGS_VS_TYPES = {
  [CONFIG_FIELD_TYPES.ACCESSORY]: [
    TYPES.DISCLOSURE_TYPES,
    TYPES.ACCESSORY_TYPES,
    TYPES.PAY_TYPES,
    TYPES.ACCESSORY_CODE,
  ],
  [CONFIG_FIELD_TYPES.FNI]: [TYPES.DISCLOSURE_TYPES, TYPES.PRODUCTS, TYPES.PRODUCT_COVERAGE_TYPE],
  [CONFIG_FIELD_TYPES.FEE]: [TYPES.FEE_TYPES],
  [CONFIG_FIELD_TYPES.REBATE]: [TYPES.REBATE_TYPES],
  [CONFIG_FIELD_TYPES.CASHIERING]: [TYPES.PAYMENT_TYPES],
  [CONFIG_FIELD_TYPES.COMMISSION]: [
    TYPES.COMMISSION_PLAN,
    TYPES.ASSIGNEE_TYPE,
    TYPES.FNI_PRODUCTS,
    TYPES.DISCLOSURE_TYPES,
  ],
};

export const TYPES_VS_KEYS = {
  [TYPES.DISCLOSURE_TYPES]: {
    include: 'includedDisclosureTypes',
    exclude: 'excludedDisclosureTypes',
  },
  [TYPES.FEE_TYPES]: {
    include: 'includeFeeTypes',
    exclude: 'excludeFeeTypes',
  },
  [TYPES.PRODUCTS]: {
    include: 'includeFniProducts',
    exclude: 'excludeFniProducts',
  },
  [TYPES.ACCESSORY_TYPES]: {
    include: TYPES.ACCESSORY_TYPES,
  },
  [TYPES.PAY_TYPES]: {
    include: TYPES.PAY_TYPES,
  },
  [TYPES.REBATE_TYPES]: {
    include: TYPES.REBATE_TYPES,
  },
  [TYPES.PAYMENT_TYPES]: {
    include: 'includePaymentTypes',
  },
  [TYPES.COMMISSION_PLAN]: {
    include: TYPES.COMMISSION_PLAN,
  },
  [TYPES.ASSIGNEE_TYPE]: {
    include: TYPES.ASSIGNEE_TYPE,
  },
  [TYPES.FNI_PRODUCTS]: {
    include: TYPES.PRODUCTS,
  },
  [TYPES.PRODUCT_COVERAGE_TYPE]: {
    include: 'productCoverageType',
  },
  [TYPES.ACCESSORY_CODE]: {
    include: 'includedAccessoryCode',
    exclude: 'excludedAccessoryCode',
  },
};

export const TARGETING_OPTIONS = [
  {
    label: __('Include'),
    value: TARGETING_TYPES.INCLUDE,
  },
  {
    label: __('Exclude'),
    value: TARGETING_TYPES.EXCLUDE,
  },
];

export const PRICE_FIELDS = [
  CONFIG_FIELD_TYPES.ACCESSORY,
  CONFIG_FIELD_TYPES.FNI,
  CONFIG_FIELD_TYPES.FEE,
  CONFIG_FIELD_TYPES.REBATE,
  CONFIG_FIELD_TYPES.CASHIERING,
];

export const UPFRONT_FIELDS = [CONFIG_FIELD_TYPES.FNI, CONFIG_FIELD_TYPES.FEE, CONFIG_FIELD_TYPES.ACCESSORY];

export const TAXABLE_FIELDS = [CONFIG_FIELD_TYPES.ACCESSORY, CONFIG_FIELD_TYPES.FNI, CONFIG_FIELD_TYPES.FEE];

export const RESIDUALISED_FIELDS = [CONFIG_FIELD_TYPES.ACCESSORY];

export const CERTIFIED_FIELDS = [CONFIG_FIELD_TYPES.REBATE];

export const ONLY_INCLUSIVE_TYPE = _values(
  _omit(TYPES, ['PRODUCTS', 'DISCLOSURE_TYPES', 'FEE_TYPES', 'ACCESSORY_CODE'])
);

export const SINGLE_SELECT_TYPES = [
  TYPES.ACCESSORY_TYPES,
  TYPES.PAY_TYPES,
  TYPES.REBATE_TYPES,
  TYPES.PRODUCT_COVERAGE_TYPE,
];

export const TYPES_VS_OPTIONS_KEY_MAP = {
  [TYPES.DISCLOSURE_TYPES]: 'disclosureTypes',
  [TYPES.FEE_TYPES]: 'feeTypes',
  [TYPES.ACCESSORY_TYPES]: 'accessoryTypes',
  [TYPES.PAY_TYPES]: 'payTypes',
  [TYPES.REBATE_TYPES]: 'rebateTypes',
  [TYPES.PAYMENT_TYPES]: 'paymentModes',
  [TYPES.COMMISSION_PLAN]: 'commissionPlan',
  [TYPES.ASSIGNEE_TYPE]: 'assigneeTypes',
  [TYPES.ACCESSORY_CODE]: 'accessoryCode',
};
