import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';

import cx from 'classnames';
import _get from 'lodash/get';
import _noop from 'lodash/noop';
import _map from 'lodash/map';
import _filter from 'lodash/filter';
import _size from 'lodash/size';

import actionTypes from '@tekion/tekion-components/src/organisms/FormBuilder/constants/actionTypes';
import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import Tab from '@tekion/tekion-components/src/molecules/Tabs/ScrollSpy';
import Divider from '@tekion/tekion-components/src/atoms/Divider';

import ACTION_TYPES from './ConfigurationForm.actionTypes';
import VerticalTabs from '../VerticalTabs';
import { ANNOTATION_MODAL_TYPES } from '../../FormConfiguratorTool.constants';
import { CONFIGURATION_FORM_SECTIONS, CONFIGURATION_FORM_TAB_NAMES, CONFIG_KEYS } from './ConfigurationForm.constants';

import {
  CONFIGURATION_FORM_TAB_KEY_VS_RENDERER,
  getAdditionalProps,
  getConfigurationFormSections,
} from './ConfigurationForm.helpers';
import styles from '../FieldPropertiesForm/fieldPropertiesModal.module.scss';

class ConfigurationForm extends PureComponent {
  componentDidMount() {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_CONFIGURATION_FORM_MOUNT,
    });
  }

  updateFormValues = (category, isEdit) => payload => {
    const { onAction } = this.props;
    onAction({
      type: actionTypes.ON_FIELD_CHANGE,
      payload: { ...payload, category, isEdit },
    });
  };

  getSections = () => {
    const { isFcLiteModeEnabled } = this.props;

    return getConfigurationFormSections(isFcLiteModeEnabled);
  };

  renderComponent = tabKey => {
    const { configurationValue, isEdit, hasViewOnlyPermission, disableEditForFCLiteMode, isFcLiteModeEnabled } =
      this.props;
    const Component = CONFIGURATION_FORM_TAB_KEY_VS_RENDERER[tabKey];

    return (
      <div className={cx({ [styles.disabledTab]: hasViewOnlyPermission })} id={tabKey}>
        <Component
          sectionName={tabKey}
          values={
            tabKey === CONFIG_KEYS.GENERAL_CONFIG
              ? _get(configurationValue, `common.${tabKey}`)
              : _get(configurationValue, `${tabKey}`)
          }
          updateFormValues={this.updateFormValues(tabKey, isEdit)}
          formType={ANNOTATION_MODAL_TYPES.CONFIGURATION_FIELD}
          isFcLiteModeEnabled={isFcLiteModeEnabled}
          disableEditForFCLiteMode={disableEditForFCLiteMode}
          {...getAdditionalProps[tabKey](this.props)}
        />
      </div>
    );
  };

  renderScrollableContent = () => (
    <div>
      {_map(this.getSections(), (sectionKey, idx) => (
        <>
          {this.renderComponent(sectionKey)}
          {idx < _size(this.getSections()) - 1 && <Divider className="m-t-0" />}
        </>
      ))}
    </div>
  );

  renderTabPanes = () =>
    _map(this.getSections(), section => <Tab.TabPane tab={CONFIGURATION_FORM_TAB_NAMES[section]} key={section} />);

  render() {
    return (
      <VerticalTabs
        navigate={_noop}
        tabs={this.renderTabPanes()}
        contentContainerClassName={styles.scrollableContainer}
        render={this.renderScrollableContent}>
        {this.renderTabPanes()}
      </VerticalTabs>
    );
  }
}

ConfigurationForm.propTypes = {
  configurationValue: PropTypes.object,
  onAction: PropTypes.func,
  isEdit: PropTypes.bool,
  isFcLiteModeEnabled: PropTypes.bool,
};

ConfigurationForm.defaultProps = {
  configurationValue: EMPTY_OBJECT,
  onAction: _noop,
  isEdit: false,
  isFcLiteModeEnabled: false,
};

export default ConfigurationForm;
