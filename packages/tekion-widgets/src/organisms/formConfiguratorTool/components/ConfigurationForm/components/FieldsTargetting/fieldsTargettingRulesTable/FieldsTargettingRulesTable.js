import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { defaultMemoize } from 'reselect';
import _get from 'lodash/get';
import _map from 'lodash/map';
import _includes from 'lodash/includes';
import { EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';

import Popover from '@tekion/tekion-components/src/molecules/popover';
import Content from '@tekion/tekion-components/src/atoms/Content';
import Select from '@tekion/tekion-components/src/molecules/Select';
import BaseTable from '@tekion/tekion-components/src/molecules/table/BaseTable';
import actionTypes from '@tekion/tekion-components/src/organisms/FormBuilder/constants/actionTypes';
import { PRODUCT_COVERAGE_TYPE_OPTIONS } from '@tekion/tekion-base/constants/deal/fnis';

import { FIELD_TYPE_VALUES } from '../../../../../FormConfiguratorTool.constants';
import { EMPTY_PLACEHOLDERS } from '../../../../../constants/pages';

import {
  TYPES,
  TYPES_VS_OPTIONS_KEY_MAP,
  TYPES_DISPLAY_NAME,
  ONLY_INCLUSIVE_TYPE,
  SINGLE_SELECT_TYPES,
} from '../../../ConfigurationForm.constants';
import ConditionValueRender from './components/ConditionValueRender';
import {
  MANDATORY_RULES_COLUMN_KEYS,
  MANDATORY_RULES_STATIC_COLUMN_DATA,
  KEYS,
} from './FieldsTargettingRulesTable.tableConfig';

import { OPERATORS_VS_LABEL, OPERATORS, LIBRARY_UNSUPPORTED_TYPES } from '../FieldsTargetting.constant';
import { getOptionsByValue } from '../FieldsTargetting.utils';

import styles from '../FieldsTargetting.module.scss';

const { NOT_AVAILABLE } = EMPTY_PLACEHOLDERS;

class FieldsTargettingRulesTable extends PureComponent {
  // eslint-disable-next-line react/sort-comp
  getProductOptions = defaultMemoize(productsList =>
    (productsList || EMPTY_ARRAY).map(({ id, displayName }) => ({
      label: displayName,
      value: id,
    }))
  );

  onSelectChange = defaultMemoize((index, key) => async value => {
    const { updateCondition, ruleIndex } = this.props;
    await updateCondition(ruleIndex)({
      index,
      key,
      value,
    });
  });

  onValuesChange = (index, key) => value => {
    const { ruleIndex, updateCondition } = this.props;
    updateCondition(ruleIndex)({
      index,
      key,
      value,
    });
  };

  onValuesChangeAction =
    (index, key) =>
    ({ payload: { value }, type }) => {
      const { ruleIndex, updateCondition } = this.props;
      if (type === actionTypes.ON_FIELD_CHANGE) {
        updateCondition(ruleIndex)({
          index,
          key,
          value,
        });
      }
    };

  getOptionsBasedOnType = type => {
    const { productsList, metaData } = this.props;
    switch (type) {
      case TYPES.PRODUCTS:
      case TYPES.FNI_PRODUCTS:
        return this.getProductOptions(productsList);
      case TYPES.PRODUCT_COVERAGE_TYPE:
        return PRODUCT_COVERAGE_TYPE_OPTIONS;
      case TYPES.ACCESSORY_CODE: {
        const dueBills = _get(metaData, 'dueBills');
        return _map(dueBills, dueBill => {
          const { code: value, name } = dueBill;
          return {
            label: `${value} - ${name}`,
            value,
          };
        });
      }
      default:
        return getOptionsByValue(_get(metaData, TYPES_VS_OPTIONS_KEY_MAP[type]) || EMPTY_ARRAY);
    }
  };

  getOperators = () =>
    _map([OPERATORS.INCLUDE, OPERATORS.EXCLUDE], operator => ({
      label: OPERATORS_VS_LABEL[operator],
      value: operator,
    }));

  getValuesOptions = rowData => {
    const { fieldsTargettingConditionType } = rowData;
    return this.getOptionsBasedOnType(fieldsTargettingConditionType);
  };

  getFieldType = type =>
    _includes(SINGLE_SELECT_TYPES, type) ? FIELD_TYPE_VALUES.SINGLE_SELECT : FIELD_TYPE_VALUES.MULTI_SELECT;

  getConditionalValueRenderer = ({ key, value, index, rowData }, disabled = false) => {
    const { operator, fieldsTargettingConditionType } = rowData;
    return (
      <ConditionValueRender
        columnKey={key}
        index={index}
        value={value}
        onChange={this.onValuesChange(index, key, rowData)}
        onAction={this.onValuesChangeAction(index, key, rowData)}
        fieldType={this.getFieldType(fieldsTargettingConditionType)}
        options={this.getValuesOptions(rowData)}
        className={styles.condtionValueRenderers}
        disabled={disabled}
        isDateRange={operator === OPERATORS.BTW && fieldsTargettingConditionType === FIELD_TYPE_VALUES.DATE}
      />
    );
  };

  getOptionsCellComponent = ({ key, value, index, rowData }) => {
    const { isGlobalForm, disableEditForFCLiteMode } = this.props;
    const { fieldsTargettingConditionType } = rowData;
    if (isGlobalForm && _includes(LIBRARY_UNSUPPORTED_TYPES, fieldsTargettingConditionType)) {
      return (
        <Popover
          placement="top"
          trigger="hover"
          mouseEnterDelay={0.5}
          content={
            <div className="d-flex flex-column align-items-center px-4 py-2">
              <Content size={5}>{__('Value can be selected in dealership view.')}</Content>
            </div>
          }>
          {this.getConditionalValueRenderer({ key, value, index, rowData }, true)}
        </Popover>
      );
    }
    return this.getConditionalValueRenderer({ key, value, index, rowData }, disableEditForFCLiteMode);
  };

  getCellComponent = ({ key, value, index, rowData }) => {
    const { disableEditForFCLiteMode } = this.props;
    const { fieldsTargettingConditionType } = rowData;
    let Cell = null;
    switch (key) {
      case KEYS.FIELDS_TARGETTING_CONDITION_TYPE:
        Cell = <div>{TYPES_DISPLAY_NAME[value]}</div>;
        break;
      case KEYS.OPERATOR:
        Cell = (
          <Select
            className="full-width"
            containerClassName="full-width"
            placeholder={__('Select Type')}
            onChange={this.onSelectChange(index, key, rowData)}
            options={this.getOperators()}
            value={value}
            disabled={_includes(ONLY_INCLUSIVE_TYPE, fieldsTargettingConditionType) || disableEditForFCLiteMode}
          />
        );
        break;
      case KEYS.OPTIONS:
        Cell = this.getOptionsCellComponent({ key, value, index, rowData });
        break;
      default:
        Cell = <div>{NOT_AVAILABLE}</div>;
    }
    return Cell;
  };

  getColumns = () => {
    const columnData = MANDATORY_RULES_COLUMN_KEYS.map(key => ({
      ...MANDATORY_RULES_STATIC_COLUMN_DATA[key],
      key,
      accessor: key,
      Cell: ({ original: rowData, value, index }) =>
        this.getCellComponent({
          key,
          value,
          index,
          rowData,
        }),
    }));

    return columnData;
  };

  getTableRowProps = () => ({
    style: {
      padding: '1rem',
    },
  });

  render() {
    const { fieldsTargettingConditions } = this.props;
    return (
      <BaseTable
        className={styles.baseTable}
        showPagination={false}
        columns={this.getColumns()}
        data={fieldsTargettingConditions}
        minRows={1}
        rowHeight="auto"
        getTrProps={this.getTableRowProps}
      />
    );
  }
}

FieldsTargettingRulesTable.propTypes = {
  ruleIndex: PropTypes.number.isRequired,
  updateCondition: PropTypes.func.isRequired,
  fieldsTargettingConditions: PropTypes.array.isRequired,
  productsList: PropTypes.array.isRequired,
  metaData: PropTypes.array.isRequired,
  isGlobalForm: PropTypes.bool,
};

FieldsTargettingRulesTable.defaultProps = {
  isGlobalForm: false,
};

export default FieldsTargettingRulesTable;
