@import "tstyles/component.scss";

.rowFieldsWrapper {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 2.4rem;
}

.rowFields {
  width: 20rem;
}

.checkboxGroupWrapper {
  :global(.ant-row) {
    @include flex();
  }
}

.fieldLabel {
  height: 2.4rem;
  height: 21px !important;
  display: flex;
}

.name {
  margin-bottom: 2rem;
  width: 50%;
}

.customInput {
  width: 50%;
}

.customSelect {
  width: 100%;
}

.customSelectWrapper {
  margin-right: 2.4rem;
  width: 50%;
}

.switchWithInfoClassName {
  flex: 1 1;
}

.labelClassName {
  margin-left: 0.8rem;

  font-family: $font-regular;
  font-size: $font-size-normal;
}
