import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';
import _get from 'lodash/get';
import _map from 'lodash/map';
import _values from 'lodash/values';
import _noop from 'lodash/noop';

import Heading from '@tekion/tekion-components/src/atoms/Heading';
import TextInput from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/textInput';
import NumberInputField from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/numberInputField';
import MultiSelect from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/MultiSelectField';
import SwitchWithInfoField from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/switchWithInfoField';
import { SWITCH_PLACEMENT } from '@tekion/tekion-components/src/molecules/switchWithInfo';

import Content from '@tekion/tekion-components/src/atoms/Content';
import actionTypes from '@tekion/tekion-components/src/organisms/FormBuilder/constants/actionTypes';
import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import Select from '@tekion/tekion-components/src/molecules/Select';

import FieldsTargetting from '../FieldsTargetting';
import { GENERAL_CONFIG_FIELDS } from '../../ConfigurationForm.constants';
import {
  CONDITION_PARAMETER_TYPE,
  CONFIG_FIELD_TYPES_DISPLAY_NAME,
  CONFIG_FIELD_TYPES,
  CONFIG_ANNOTATION_DELIMITER,
} from '../FieldsTargetting/FieldsTargetting.constant';
import { getConfigurationValues } from '../../ConfigurationForm.utils';
import { DEFAULT_FIELD_KEYS, DEFAULT_FIELD_OPTIONS, DEFAULT_VALUE_EMPTY_DESCRIPTION } from './GeneralConfig.constants';

import styles from './generalConfig.module.scss';

function GeneralConfig(props) {
  const {
    updateFormValues,
    values,
    metaData,
    productsList,
    configuratorMetaData,
    isGlobalForm,
    isEdit,
    configurationValue,
    hasViewOnlyPermission,
    isFcLiteModeEnabled,
    disableEditForFCLiteMode,
    hideActiveFieldToggle,
  } = props;

  const disableEditForFCLite = isFcLiteModeEnabled && disableEditForFCLiteMode;

  const onGeneralConfigChange = useCallback(
    ({ payload: { id, value }, type }) => {
      if (type === actionTypes.ON_FIELD_CHANGE) {
        updateFormValues({ id, value });
      }
    },
    [updateFormValues]
  );

  const onFieldsTargettingChange = useCallback(
    value => {
      updateFormValues({ id: GENERAL_CONFIG_FIELDS.FIELDS_TARGETTING_RULES, value });
    },
    [updateFormValues]
  );

  const getOptionsForFieldConfigTypes = () => {
    const configFieldTypesList = _values(CONFIG_FIELD_TYPES);
    const options = _map(configFieldTypesList, value => {
      const label = CONFIG_FIELD_TYPES_DISPLAY_NAME[value];
      return {
        label,
        value,
        conditionParameterType: CONDITION_PARAMETER_TYPE.CONFIGURATION_FIELD,
      };
    });
    return options;
  };

  const onDefaultValueTypeChange = value =>
    onGeneralConfigChange({
      type: actionTypes.ON_FIELD_CHANGE,
      payload: {
        id: GENERAL_CONFIG_FIELDS.DEFAULT,
        value,
      },
    });

  const onCustomValueChange = ({ payload: { id, value }, type }) =>
    onGeneralConfigChange({
      type,
      payload: {
        id,
        value: value === '' ? null : value,
      },
    });

  const onDelimiterChange = value =>
    onGeneralConfigChange({
      type: actionTypes.ON_FIELD_CHANGE,
      payload: {
        id: GENERAL_CONFIG_FIELDS.DELIMITER,
        value,
      },
    });

  const renderDefaultValue = () => {
    const defaultValueOption = [DEFAULT_FIELD_KEYS.BLANK, DEFAULT_FIELD_KEYS.NA].includes(values?.default)
      ? values?.default
      : DEFAULT_FIELD_KEYS.CUSTOM_VALUE;
    const defaultValue = values?.default ? defaultValueOption : values?.default;

    return (
      <div className="m-b-24">
        <Heading className="m-t-32 m-b-8" size={5}>
          {__('Default Value')}
        </Heading>
        <div className="d-flex m-t-16 align-items-center">
          <div className={styles.customSelectWrapper}>
            <Content size={5} className="m-b-8">
              {DEFAULT_VALUE_EMPTY_DESCRIPTION}
            </Content>
            <Select
              className={styles.customSelect}
              options={DEFAULT_FIELD_OPTIONS}
              value={defaultValue}
              id={GENERAL_CONFIG_FIELDS.DEFAULT}
              onChange={onDefaultValueTypeChange}
            />
          </div>
          {defaultValueOption === DEFAULT_FIELD_KEYS.CUSTOM_VALUE && (
            <TextInput
              required
              containerClassName={styles.customInput}
              onAction={onCustomValueChange}
              label={__('Custom Value')}
              value={values?.default}
              id={GENERAL_CONFIG_FIELDS.DEFAULT}
            />
          )}
        </div>
      </div>
    );
  };

  const renderRowDetailFields = () => (
    <div>
      <Heading className="m-b-8" size={5}>
        {__('Rows')}
      </Heading>
      <div className={styles.rowFieldsWrapper}>
        <NumberInputField
          label={__('No. of Rows')}
          value={_get(values, GENERAL_CONFIG_FIELDS.ROWS)}
          shouldDisabledStepper
          min={1}
          max={100}
          precision={0}
          onAction={onGeneralConfigChange}
          id={GENERAL_CONFIG_FIELDS.ROWS}
          disabled={disableEditForFCLite}
        />
        <TextInput
          onAction={onGeneralConfigChange}
          label="Key"
          value={_get(values, GENERAL_CONFIG_FIELDS.ROW_KEY)}
          id={GENERAL_CONFIG_FIELDS.ROW_KEY}
          disabled={disableEditForFCLite}
        />
        <MultiSelect
          fieldLabelClassName={styles.fieldLabel}
          placeholder={__('Field')}
          label="Field"
          onAction={onGeneralConfigChange}
          options={getOptionsForFieldConfigTypes()}
          value={_get(values, GENERAL_CONFIG_FIELDS.ROW_FIELD)}
          id={GENERAL_CONFIG_FIELDS.ROW_FIELD}
          disabled={disableEditForFCLite}
        />

        <MultiSelect
          required
          fieldLabelClassName={styles.fieldLabel}
          placeholder={__('Select Values')}
          label={__('Value')}
          onAction={onGeneralConfigChange}
          options={getConfigurationValues(configuratorMetaData)}
          value={_get(values, GENERAL_CONFIG_FIELDS.ROW_VALUES)}
          id={GENERAL_CONFIG_FIELDS.ROW_VALUES}
          isWithSelectAll
          disabled={disableEditForFCLite}
        />
      </div>
    </div>
  );

  return (
    <div className="full-width">
      <div className="m-b-4">
        <Heading size={3} className="mb-2">
          {__('General')}
        </Heading>
        <Content colorVariant={Content.COLOR_VARIANTS.GREY} className="m-b-16">
          {__('Define field type & default states')}
        </Content>

        {isEdit && (
          <>
            <Heading className="m-b-8 m-t-16" size={5}>
              {__('Field Configurations')}
            </Heading>
            {!hideActiveFieldToggle && (
              <SwitchWithInfoField
                {...{
                  value: _get(configurationValue, 'active', false),
                  label: __('Show on Form'),
                  switchPlacement: SWITCH_PLACEMENT.START,
                  switchSize: 'small',
                  disabled: hasViewOnlyPermission,
                  labelClassName: styles.labelClassName,
                  onAction: onGeneralConfigChange,
                  id: GENERAL_CONFIG_FIELDS.SHOW_ON_FORM,
                }}
              />
            )}
          </>
        )}

        <div className={cx(styles.rowFieldsWrapper, 'm-b-24')}>
          <TextInput
            required
            onAction={onGeneralConfigChange}
            label={__('Configuration Name')}
            value={_get(values, GENERAL_CONFIG_FIELDS.CONFIG_NAME)}
            id={GENERAL_CONFIG_FIELDS.CONFIG_NAME}
            disabled={disableEditForFCLite}
          />

          <Select
            className={styles.customSelect}
            options={CONFIG_ANNOTATION_DELIMITER}
            label={__('Delimiter')}
            onChange={onDelimiterChange}
            id={GENERAL_CONFIG_FIELDS.DELIMITER}
            value={_get(values, GENERAL_CONFIG_FIELDS.DELIMITER)}
            disabled={disableEditForFCLiteMode}
          />
        </div>
      </div>

      {renderRowDetailFields()}
      <FieldsTargetting
        fieldTargets={_get(values, GENERAL_CONFIG_FIELDS.FIELDS_TARGETTING_RULES)}
        updateFieldsTargettingValues={onFieldsTargettingChange}
        metaData={metaData}
        productsList={productsList}
        isGlobalForm={isGlobalForm}
        disableEditForFCLiteMode={disableEditForFCLite}
      />
      {renderDefaultValue()}
    </div>
  );
}

GeneralConfig.propTypes = {
  values: PropTypes.object,
  metaData: PropTypes.object,
  updateFormValues: PropTypes.func,
  productsList: PropTypes.array,
  configuratorMetaData: PropTypes.object,
  isGlobalForm: PropTypes.bool,
};

GeneralConfig.defaultProps = {
  values: EMPTY_OBJECT,
  metaData: EMPTY_OBJECT,
  updateFormValues: _noop,
  productsList: EMPTY_ARRAY,
  configuratorMetaData: EMPTY_OBJECT,
  isGlobalForm: false,
};

export default GeneralConfig;
