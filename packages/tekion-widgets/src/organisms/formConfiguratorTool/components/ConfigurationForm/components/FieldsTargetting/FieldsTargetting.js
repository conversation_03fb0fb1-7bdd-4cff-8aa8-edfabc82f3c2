import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import produce from 'immer';
import _map from 'lodash/map';
import _set from 'lodash/set';
import _includes from 'lodash/includes';

import SelectInput from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/SelectInput';
import Heading from '@tekion/tekion-components/src/atoms/Heading';
import Checkbox from '@tekion/tekion-components/src/atoms/checkbox';
import Collapse from '@tekion/tekion-components/src/molecules/Collapse';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import actionTypes from '@tekion/tekion-components/src/organisms/FormBuilder/constants/actionTypes';

import FieldsTargettingRulesTable from './fieldsTargettingRulesTable';
import * as FieldsTargettingRulesUtils from './FieldsTargetting.utils';
import {
  CONFIG_FIELD_TYPES_DISPLAY_NAME,
  DROPDOWN_OPTIONS,
  TAXABLE_FIELDS,
  RESIDUALISED_FIELDS,
  CERTIFIED_FIELDS,
  UPFRONT_FIELDS,
  PREFERENCES_FIELDS,
  PRICE_IGNORED_FIELDS,
} from './FieldsTargetting.constant';

import styles from './FieldsTargetting.module.scss';

class FieldsTargetting extends PureComponent {
  onUpdateCondition =
    ruleIndex =>
    ({ index, key, value }) => {
      const { fieldTargets, updateFieldsTargettingValues } = this.props;
      const updatedRules = produce(fieldTargets, draft => {
        draft[ruleIndex] = FieldsTargettingRulesUtils.updateCondition(fieldTargets[ruleIndex], index, key, value);
      });
      updateFieldsTargettingValues(updatedRules);
    };

  onGeneralConfigChange =
    ruleIndex =>
    ({ payload: { id, value }, type }) => {
      if (type === actionTypes.ON_FIELD_CHANGE) {
        const { updateFieldsTargettingValues } = this.props;
        const { fieldTargets } = this.props;
        const updatedRules = produce(fieldTargets, draft => {
          _set(draft[ruleIndex], id, value);
        });
        updateFieldsTargettingValues(updatedRules);
      }
    };

  onPriceCheckHandler = index => event => {
    this.onGeneralConfigChange(index)({
      payload: {
        id: 'priceAllowed',
        value: event.target.checked,
      },
      type: actionTypes.ON_FIELD_CHANGE,
    });
  };

  renderPanels = () => {
    const { fieldTargets, productsList, metaData, fields, fieldKeysList, isGlobalForm, disableEditForFCLiteMode } =
      this.props;
    return _map(fieldTargets, (rule, index) => {
      const configFieldType = rule?.fieldsTargettingConfigField;
      return {
        header: (
          <div className="d-flex justify-content-between full-width">
            <Heading size={5} className="m-t-24 m-b-16">
              {__(CONFIG_FIELD_TYPES_DISPLAY_NAME[rule?.fieldsTargettingConfigField])}
            </Heading>
          </div>
        ),
        headerClass: styles.collapseHeader,
        arrowClass: styles.arrowIcon,
        key: index,
        style: index > 0 ? { borderTop: 'none' } : { borderTop: '0.1rem solid #d4d5d6' },
        body: (
          <>
            <Heading size={5} className="m-t-8 m-b-16">
              {__('Targeting')}
            </Heading>
            <FieldsTargettingRulesTable
              fieldsTargettingConditions={rule?.fieldsTargettingConditions}
              updateCondition={this.onUpdateCondition}
              ruleIndex={index}
              productsList={productsList}
              metaData={metaData}
              fields={fields}
              fieldKeysList={fieldKeysList}
              isGlobalForm={isGlobalForm}
              disableEditForFCLiteMode={disableEditForFCLiteMode}
            />
            <PropertyControlledComponent controllerProperty={_includes(PREFERENCES_FIELDS, configFieldType)}>
              <Heading size={5} className="m-t-32 m-b-16">
                {__('Preferences')}
              </Heading>

              <div className={styles.preferences}>
                <PropertyControlledComponent controllerProperty={_includes(TAXABLE_FIELDS, configFieldType)}>
                  <SelectInput
                    className={styles.option}
                    id="taxable"
                    onAction={this.onGeneralConfigChange(index)}
                    label={__('Taxable')}
                    placeholder={__('Select')}
                    options={DROPDOWN_OPTIONS}
                    value={rule?.taxable}
                    isDisabled={disableEditForFCLiteMode}
                  />
                </PropertyControlledComponent>
                <PropertyControlledComponent controllerProperty={_includes(RESIDUALISED_FIELDS, configFieldType)}>
                  <SelectInput
                    className={styles.option}
                    id="residualised"
                    onAction={this.onGeneralConfigChange(index)}
                    label={__('Residualised')}
                    placeholder={__('Select')}
                    options={DROPDOWN_OPTIONS}
                    value={rule?.residualised}
                    isDisabled={disableEditForFCLiteMode}
                  />
                </PropertyControlledComponent>
                <PropertyControlledComponent controllerProperty={_includes(CERTIFIED_FIELDS, configFieldType)}>
                  <SelectInput
                    className={styles.option}
                    id="certified"
                    onAction={this.onGeneralConfigChange(index)}
                    label={__('Certified')}
                    placeholder={__('Select')}
                    options={DROPDOWN_OPTIONS}
                    value={rule?.certified}
                    isDisabled={disableEditForFCLiteMode}
                  />
                </PropertyControlledComponent>
                <PropertyControlledComponent controllerProperty={_includes(UPFRONT_FIELDS, configFieldType)}>
                  <SelectInput
                    className={styles.option}
                    id="upfront"
                    onAction={this.onGeneralConfigChange(index)}
                    label={__('Upfront')}
                    placeholder={__('Select')}
                    options={DROPDOWN_OPTIONS}
                    value={rule?.upfront}
                    isDisabled={disableEditForFCLiteMode}
                  />
                </PropertyControlledComponent>
              </div>
            </PropertyControlledComponent>
            <PropertyControlledComponent controllerProperty={!_includes(PRICE_IGNORED_FIELDS, configFieldType)}>
              <Checkbox
                label={__('Price')}
                onChange={this.onPriceCheckHandler(index)}
                value={_includes(PRICE_IGNORED_FIELDS, configFieldType) ? false : rule?.priceAllowed}
                disabled={disableEditForFCLiteMode}
              />
            </PropertyControlledComponent>
          </>
        ),
      };
    });
  };

  render() {
    return (
      <>
        <Heading size={5} className="m-t-32 m-b-16">
          {__('Fields Targetting')}
        </Heading>
        <Collapse panels={this.renderPanels()} />
      </>
    );
  }
}

FieldsTargetting.propTypes = {
  fieldTargets: PropTypes.object.isRequired,
  updateFieldsTargettingValues: PropTypes.func.isRequired,
  fieldKeysList: PropTypes.array.isRequired,
  productsList: PropTypes.array.isRequired,
  metaData: PropTypes.array.isRequired,
  fields: PropTypes.array.isRequired,
  isGlobalForm: PropTypes.bool.isRequired,
};

FieldsTargetting.defaultProps = {};

export default FieldsTargetting;
