import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';

import cx from 'classnames';
import _get from 'lodash/get';
import _noop from 'lodash/noop';
import _map from 'lodash/map';
import _size from 'lodash/size';

import actionTypes from '@tekion/tekion-components/src/organisms/FormBuilder/constants/actionTypes';
import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import Tab from '@tekion/tekion-components/src/molecules/Tabs/ScrollSpy';
import Divider from '@tekion/tekion-components/src/atoms/Divider';

import ACTION_TYPES from './CommonPropertiesForm.actionTypes';
import VerticalTabs from '../VerticalTabs';
import { ANNOTATION_MODAL_TYPES } from '../../FormConfiguratorTool.constants';
import { FIELD_FORM_TAB_NAMES } from './CommonPropertiesForm.constants';
import { getFieldTabKeyRenderer, getAdditionalProps } from './CommonPropertiesForm.helpers';
import styles from './commonPropertiesModal.module.scss';
import configReader from '../../readers/config.reader';
import { getSupportedFieldTabs } from './CommonPropertiesForm.utils';

class CommonPropertiesForm extends PureComponent {
  componentDidMount() {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_FIELD_FORM_MOUNT,
    });
  }

  updateFormValues = category => payload => {
    const { onAction } = this.props;
    onAction({
      type: actionTypes.ON_FIELD_CHANGE,
      payload: { ...payload, category },
    });
  };

  getSections = () => {
    const { config } = this.props;
    const supportedBulkPropertiesUpdate = configReader.supportedBulkPropertiesUpdate(config);

    return getSupportedFieldTabs(supportedBulkPropertiesUpdate);
  };

  renderComponent = tabKey => {
    const { fieldProperties, hasViewOnlyPermission, isFcLiteModeEnabled } = this.props;
    const Component = getFieldTabKeyRenderer[tabKey];
    return (
      <div className={cx({ [styles.disabledTab]: hasViewOnlyPermission })} id={tabKey}>
        <Component
          sectionName={tabKey}
          values={_get(fieldProperties, tabKey)}
          updateFormValues={this.updateFormValues(tabKey)}
          formType={ANNOTATION_MODAL_TYPES.CONFIGURATION_FIELD}
          isFcLiteModeEnabled={isFcLiteModeEnabled}
          {...getAdditionalProps[tabKey](this.props)}
        />
      </div>
    );
  };

  renderTabPanes = () => {
    const sections = this.getSections();
    return _map(sections, key => <Tab.TabPane tab={FIELD_FORM_TAB_NAMES[key]} key={key} />);
  };

  renderScrollableContent = () => {
    const sections = this.getSections();

    return (
      <div>
        {_map(sections, (sectionKey, idx) => (
          <>
            {this.renderComponent(sectionKey)}
            {idx < _size(sections) - 1 && <Divider className="m-t-0" />}
          </>
        ))}
      </div>
    );
  };

  render() {
    return (
      <VerticalTabs
        navigate={_noop}
        tabs={this.renderTabPanes()}
        contentContainerClassName={styles.scrollableContainer}
        render={this.renderScrollableContent}
      />
    );
  }
}

CommonPropertiesForm.propTypes = {
  fieldProperties: PropTypes.object,
  config: PropTypes.object,
  onAction: PropTypes.func,
};

CommonPropertiesForm.defaultProps = {
  fieldProperties: EMPTY_OBJECT,
  config: EMPTY_OBJECT,
  onAction: _noop,
};

export default CommonPropertiesForm;
