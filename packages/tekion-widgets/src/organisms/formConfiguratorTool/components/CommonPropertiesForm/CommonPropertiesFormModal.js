import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { compose } from 'recompose';

import _noop from 'lodash/noop';
import _size from 'lodash/size';

import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import withActionHandlers from '@tekion/tekion-components/src/connectors/withActionHandlers';
import Modal from '@tekion/tekion-components/src/molecules/Modal';

import ACTION_TYPES from './CommonPropertiesForm.actionTypes';
import FieldPropertiesForm from './CommonPropertiesForm';
import FORM_ACTION_HANDLERS from './CommonPropertiesForm.actionHandlers';
import { disableCommonPropertiesSubmit } from './CommonPropertiesForm.utils';

const BODY_STYLE = {
  height: '60rem',
  paddingTop: '0rem',
  paddingBottom: '0rem',
};

class CommonPropertiesFormModal extends PureComponent {
  handleNewFormSubmit = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_FIELD_FORM_SUBMIT,
    });
  };

  render() {
    const { showModal, toggleModal, fieldProperties, hasViewOnlyPermission, selectedFields } = this.props;
    return (
      <Modal
        onCancel={toggleModal}
        onSubmit={this.handleNewFormSubmit}
        secondaryBtnText={hasViewOnlyPermission ? __('Close') : __('Cancel')}
        submitBtnText={__('Save')}
        title={__('Field Properties: {{selectedFieldsCount}} selected', {
          selectedFieldsCount: _size(selectedFields),
        })}
        visible={showModal}
        width={Modal.SIZES.XL}
        bodyStyle={BODY_STYLE}
        hideSubmit={hasViewOnlyPermission}
        destroyOnClose
        okButtonProps={{
          disabled: disableCommonPropertiesSubmit(fieldProperties),
        }}>
        <FieldPropertiesForm {...this.props} />
      </Modal>
    );
  }
}

CommonPropertiesFormModal.propTypes = {
  toggleModal: PropTypes.func.isRequired,
  showModal: PropTypes.bool,
  fieldProperties: PropTypes.object,
  onAction: PropTypes.func,
  selectedFields: PropTypes.array,
  hasViewOnlyPermission: PropTypes.bool,
};

CommonPropertiesFormModal.defaultProps = {
  showModal: false,
  fieldProperties: EMPTY_OBJECT,
  onAction: _noop,
  selectedFields: EMPTY_ARRAY,
  hasViewOnlyPermission: false,
};

export default compose(withActionHandlers(FORM_ACTION_HANDLERS))(CommonPropertiesFormModal);
