@import "tstyles/component.scss";

.active {
  color: $denim;
}

.labelContainer {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0.8rem;
  border-radius: 0.4rem;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.labelContainerHover {
  &:hover {
    background-color: $aliceBlue;
    & > div {
      color: $denim;
    }
  }
}

.labelContainerActive {
  @extend .labelContainer;
  background-color: $aliceBlue;
  color: $denim;
}

.popoverContent {
  padding: 0.4rem;
  display: flex;
}

.marginRight {
  margin-right: 1.2rem;
}

.buttonClass {
  font-family: $font-regular;
  font-weight: normal;
  margin-bottom: 0.8rem;
}

.disabled {
  color: $black;
  opacity: 0.4;
}

.matchIcon {
  color: $black;
}

.matchIconButton {
  &:hover {
    .matchIcon {
      color: $azure;
    }
  }
}
