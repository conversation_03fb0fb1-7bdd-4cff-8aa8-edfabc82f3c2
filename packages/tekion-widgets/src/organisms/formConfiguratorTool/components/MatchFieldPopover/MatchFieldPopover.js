import React from 'react';
import cx from 'classnames';
import PropTypes from 'prop-types';
import _noop from 'lodash/noop';
import _map from 'lodash/map';
import Content from '@tekion/tekion-components/src/atoms/Content';
import Button from '@tekion/tekion-components/src/atoms/Button';
import Popover, { POPOVER_PLACEMENT, POPOVER_TRIGGER } from '@tekion/tekion-components/src/molecules/popover';
import FontIcon, { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';
import Tooltip, { TOOLTIP_PLACEMENT } from '@tekion/tekion-components/src/atoms/tooltip';

import styles from './matchFieldPopover.module.scss';
import { MATCH_FORM_FIELDS } from '../../FormConfiguratorTool.constants';

const MatchFieldPopover = ({ containerClass, onClick, canMatch }) => {
  const [popoverVisible, setPopoverVisible] = React.useState(false);
  const togglePopOver = () => setPopoverVisible(!popoverVisible);

  const renderPopoverContent = React.useMemo(
    () => (
      <div className={styles.popoverContent} data-testid="align-fields-content">
        {_map(MATCH_FORM_FIELDS, matchType => (
          <Tooltip placement={TOOLTIP_PLACEMENT.TOP} title={matchType.label}>
            <Button
              onClick={() => onClick(matchType.type)}
              className={cx('m-r-4', styles.matchIconButton)}
              view={Button.VIEW.TERTIARY}
              key={matchType.type}>
              <FontIcon size={SIZES.MD} className={styles.matchIcon}>
                {matchType.icon}
              </FontIcon>
            </Button>
          </Tooltip>
        ))}
      </div>
    ),
    [onClick]
  );

  const renderDisableContent = React.useMemo(
    () => (
      <div className="d-flex flex-column align-items-center px-4 py-2" data-testid="match-fields-disabled-content">
        <Content size={5}>{__('Select atleast 2 fields')}</Content>
        <Content size={5}>{__('to Match Sizes')}</Content>
      </div>
    ),
    []
  );

  const renderInfoPopover = React.useMemo(
    () => (
      <Popover
        placement="top"
        trigger="hover"
        mouseEnterDelay={0.5}
        content={
          <div className="d-flex flex-column align-items-center px-4 py-2">
            <Content size={5}>{__('Fields dimensions will be matched')}</Content>
            <Content size={5}>{__('based on the first selected field')}</Content>
          </div>
        }>
        <FontIcon size={SIZES.S} className="ml-4">
          icon-info
        </FontIcon>
      </Popover>
    ),
    []
  );

  return (
    <Popover
      placement={canMatch ? POPOVER_PLACEMENT.BOTTOM : POPOVER_PLACEMENT.TOP}
      trigger={canMatch ? POPOVER_TRIGGER.CLICK : POPOVER_TRIGGER.HOVER}
      mouseEnterDelay={0.5}
      content={canMatch ? renderPopoverContent : null}
      visible={popoverVisible}
      onVisibleChange={togglePopOver}>
      <Tooltip
        placement={TOOLTIP_PLACEMENT.TOP}
        title={!canMatch ? __('Select at least 2 fields to enable action') : ''}>
        <div
          className={cx(styles.labelContainer, {
            [styles.labelContainerHover]: canMatch,
            [styles.labelContainerActive]: popoverVisible && canMatch,
          })}
          role="button"
          tabIndex={0}>
          <FontIcon size={SIZES.S} className={cx({ [styles.active]: popoverVisible, [styles.disabled]: !canMatch })}>
            icon-match-width-height
          </FontIcon>
          <Content size={5} className={cx('m-x-8', { [styles.active]: popoverVisible, [styles.disabled]: !canMatch })}>
            {__('Match size')}
          </Content>
          <FontIcon size={SIZES.S} className={cx({ [styles.active]: popoverVisible, [styles.disabled]: !canMatch })}>
            {popoverVisible && canMatch ? 'icon-chevron-up' : 'icon-chevron-down'}
          </FontIcon>
        </div>
      </Tooltip>
    </Popover>
  );
};

MatchFieldPopover.propTypes = {
  containerClass: PropTypes.string,
  onClick: PropTypes.func,
  canMatch: PropTypes.bool,
};

MatchFieldPopover.defaultProps = {
  containerClass: '',
  onClick: _noop,
  canMatch: false,
};

export default MatchFieldPopover;
