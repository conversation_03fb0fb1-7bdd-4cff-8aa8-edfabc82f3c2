export const OFFSET_DESCRIPTION = __('Define offset positions');

export const OFFSET_FIELDS = {
  OFFSET_X: 'xOffset',
  OFFSET_Y: 'yOffset',
};

// TOP and LEFT map to (x, y) of a field
export const POSITION_FIELDS = {
  TOP: 'top',
  LEFT: 'left',
};

export const OFFSET_UNIT = 'mm';

const SIGNS = {
  POSITIVE: 'POSITIVE',
  NEGATIVE: 'NEGATIVE',
};

export const OFFSET_X_RADIOS = [
  {
    label: __('Left'),
    value: SIGNS.NEGATIVE,
  },
  {
    label: __('Right'),
    value: SIGNS.POSITIVE,
  },
];

export const OFFSET_Y_RADIOS = [
  {
    label: __('Up'),
    value: SIGNS.POSITIVE,
  },
  {
    label: __('Down'),
    value: SIGNS.NEGATIVE,
  },
];
