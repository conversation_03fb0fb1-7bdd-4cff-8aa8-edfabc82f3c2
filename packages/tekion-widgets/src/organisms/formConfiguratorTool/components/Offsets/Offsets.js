import React, { useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';

import _noop from 'lodash/noop';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import actionTypes from '@tekion/tekion-components/src/organisms/FormBuilder/constants/actionTypes';
import FormPage from '@tekion/tekion-components/src/organisms/FormBuilder';

import { getFormFields, getFormSections } from './Offsets.config';

import { getFormValues, updateValueForOnChangeHandler } from './offsets.helpers';

function Offsets(props) {
  const { values, updateFormValues } = props;

  const displayValues = useMemo(() => getFormValues(values), [values]);

  const onOffsetChange = useCallback(
    ({ payload: { id, value }, type }) => {
      if (type === actionTypes.ON_FIELD_CHANGE) {
        updateFormValues({
          id,
          value: updateValueForOnChangeHandler({ id, value }),
        });
      }
    },
    [updateFormValues]
  );

  return (
    <div className="full-width">
      <FormPage
        sections={getFormSections()}
        fields={getFormFields()}
        values={displayValues}
        headerComponent={null}
        footerComponent={null}
        onAction={onOffsetChange}
        className="full-width"
      />
    </div>
  );
}

Offsets.propTypes = {
  values: PropTypes.object,
  updateFormValues: PropTypes.func,
};

Offsets.defaultProps = {
  values: EMPTY_OBJECT,
  updateFormValues: _noop,
};

export default Offsets;
