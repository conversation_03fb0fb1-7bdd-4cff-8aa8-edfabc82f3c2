import NumberInput from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/numberInputField';
import { formatPhone } from '@tekion/tekion-base/formatters/number';

import NumberInputWithSignToggle from '../../../../molecules/NumberInputWithSignToggle';

import {
  OFFSET_FIELDS as FORM_FIELDS,
  OFFSET_UNIT,
  OFFSET_X_RADIOS,
  OFFSET_Y_RADIOS,
  POSITION_FIELDS,
} from './Offsets.constants';

import styles from './offsets.module.scss';

export const FORM_FIELD_FORMATTERS = {
  [FORM_FIELDS.PHONE_NUMBER]: formatPhone,
};

export const getFormSections = () => [
  {
    className: styles.sectionWrapper,
    header: { label: __('Position (in mm)'), className: styles.formSectionHeader, size: 3 },
    subHeader: {
      label: __('Define field coordinates.'),
      className: styles.formSectionSubHeader,
    },
    rows: [
      {
        columns: [POSITION_FIELDS.TOP, POSITION_FIELDS.LEFT],
      },
    ],
  },
  {
    className: styles.sectionWrapper,
    header: { label: __('Print Offset (in mm)'), className: styles.formSectionHeader, size: 3 },
    subHeader: {
      label: __('Update relative print position.'),
      className: styles.formSectionSubHeader,
    },
    rows: [
      {
        columns: [FORM_FIELDS.OFFSET_X, FORM_FIELDS.OFFSET_Y],
      },
    ],
  },
];

export const getFormFields = () => ({
  [FORM_FIELDS.OFFSET_X]: {
    renderer: NumberInputWithSignToggle,
    renderOptions: {
      addonAfter: OFFSET_UNIT,
      radios: OFFSET_X_RADIOS,
      parser: value => {
        const parsed = value.replace(/[^\d.]/g, '').replace(/^(\d*\.\d*).*$/, '$1');
        return parsed;
      },
    },
    precision: 0,
  },
  [FORM_FIELDS.OFFSET_Y]: {
    renderer: NumberInputWithSignToggle,
    renderOptions: {
      addonAfter: OFFSET_UNIT,
      radios: OFFSET_Y_RADIOS,
      parser: value => {
        const parsed = value.replace(/[^\d.]/g, '').replace(/^(\d*\.\d*).*$/, '$1');
        return parsed;
      },
      precision: 0,
    },
  },
  [POSITION_FIELDS.TOP]: {
    renderer: NumberInput,
    renderOptions: {
      label: __('From Top'),
      size: 6,
      min: 0,
      precision: 0,
      triggerChangeOnBlur: false,
      shouldDisabledStepper: true,
      addonAfter: OFFSET_UNIT,
    },
  },
  [POSITION_FIELDS.LEFT]: {
    renderer: NumberInput,
    renderOptions: {
      label: __('From Left'),
      size: 6,
      min: 0,
      precision: 0,
      triggerChangeOnBlur: false,
      shouldDisabledStepper: true,
      addonAfter: OFFSET_UNIT,
    },
  },
});
