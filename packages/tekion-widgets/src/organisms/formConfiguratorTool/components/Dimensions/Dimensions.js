import React, { useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';

import _noop from 'lodash/noop';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import actionTypes from '@tekion/tekion-components/src/organisms/FormBuilder/constants/actionTypes';
import FormPage from '@tekion/tekion-components/src/organisms/FormBuilder';

import { getFormFields, getFormSections } from './Dimensions.config';

import { getFormValues, updateValueForOnChangeHandler } from './dimensions.helpers';

function Dimensions(props) {
  const { values, updateFormValues } = props;

  const displayValues = useMemo(() => getFormValues(values), [values]);

  const onDimensionsChange = useCallback(
    ({ payload: { id, value }, type }) => {
      if (type === actionTypes.ON_FIELD_CHANGE) {
        updateFormValues({
          id,
          value: updateValueForOnChangeHandler({ id, value }),
        });
      }
    },
    [updateFormValues]
  );

  return (
    <div className="full-width">
      <FormPage
        sections={getFormSections()}
        fields={getFormFields()}
        values={displayValues}
        headerComponent={null}
        footerComponent={null}
        onAction={onDimensionsChange}
        className="full-width"
      />
    </div>
  );
}

Dimensions.propTypes = {
  values: PropTypes.object,
  updateFormValues: PropTypes.func,
};

Dimensions.defaultProps = {
  values: EMPTY_OBJECT,
  updateFormValues: _noop,
};

export default Dimensions;
