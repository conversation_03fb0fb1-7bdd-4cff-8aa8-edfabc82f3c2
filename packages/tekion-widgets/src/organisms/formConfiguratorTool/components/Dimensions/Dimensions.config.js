import NumberInput from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/numberInputField';

import { DIMENSIONS_DESCRIPTION, DIMENSIONS_UNIT, DIMENSIONS_FIELDS as FORM_FIELDS } from './Dimensions.constants';

import styles from './dimensions.module.scss';

export const getFormSections = () => [
  {
    className: styles.sectionWrapper,
    header: { label: __('Dimensions (in mm)'), className: styles.formSectionHeader, size: 3 },
    subHeader: {
      label: DIMENSIONS_DESCRIPTION,
      className: styles.formSectionSubHeader,
    },
    rows: [],
  },
  {
    className: styles.sectionWrapper,
    rows: [
      {
        columns: [FORM_FIELDS.WIDTH, FORM_FIELDS.HEIGHT],
      },
    ],
  },
];

export const getFormFields = () => ({
  [FORM_FIELDS.WIDTH]: {
    renderer: NumberInput,
    renderOptions: {
      label: __('Width'),
      size: 6,
      min: 0,
      precision: 2,
      triggerChangeOnBlur: false,
      shouldDisabledStepper: true,
      addonAfter: DIMENSIONS_UNIT,
    },
  },
  [FORM_FIELDS.HEIGHT]: {
    renderer: NumberInput,
    renderOptions: {
      label: __('Height'),
      size: 6,
      min: 0,
      precision: 2,
      triggerChangeOnBlur: false,
      shouldDisabledStepper: true,
      addonAfter: DIMENSIONS_UNIT,
    },
  },
});
