import React, { useCallback, useEffect, useMemo, useState, useRef } from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';
import _map from 'lodash/map';
import _includes from 'lodash/includes';
import _keys from 'lodash/keys';
import _noop from 'lodash/noop';
import _head from 'lodash/head';
import _get from 'lodash/get';
import FontIcon, { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';
import _replace from 'lodash/replace';
import _castArray from 'lodash/castArray';
import _omit from 'lodash/omit';
import _size from 'lodash/size';
import _isEmpty from 'lodash/isEmpty';
import _throttle from 'lodash/throttle';
import _debounce from 'lodash/debounce';
import DEALER_PROPERTY_CONSTANTS from '@tekion/tekion-base/constants/dealerProperties';
import Upload from '@tekion/tekion-components/src/molecules/uploader';
import Modal from '@tekion/tekion-components/src/molecules/Modal';
import ConfirmationDialog from '@tekion/tekion-components/src/molecules/confirmationDialog';
import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import Heading from '@tekion/tekion-components/src/atoms/Heading';
import PDFRenderer from '@tekion/tekion-components/src/organisms/PDFRenderer';
import Content from '@tekion/tekion-components/src/atoms/Content';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import DraggerUpload from '@tekion/tekion-components/src/molecules/draggerUpload';
import Popover from '@tekion/tekion-components/src/molecules/popover';
import Spinner from '@tekion/tekion-components/src/molecules/SpinnerComponent/Spinner';
import KebabMenu from '@tekion/tekion-components/src/molecules/KebabMenu';
import COLORS from '@tekion/tekion-styles-next/scss/exports.scss';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import SelectInput from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/SelectInput';
import UPLOAD_IMAGE from '../../assets/images/upload.svg';
import PrintAlignModal from '../updateModal/UpdateModalMappingFields';
import PrintAlignModalContent from '../PrintAlignModal';
import PDFToolbar from '../pdfToolbar';
import FormActions from '../FormActions';

import styles from './pdfAnnotator.module.scss';
import ACTION_TYPES from '../../FormConfiguratorTool.actionTypes';
import AnnotationField from '../AnnotationField';
import PdfFormSelectOption from '../pdfFormSelectOption';
import {
  DEFAULT_FIELD_HEIGHT,
  DEFAULT_FIELD_WIDTH,
  ERRORS,
  PAGE_MARGINS,
  PAGE_BORDERS,
} from '../../FormConfiguratorTool.constants';
import { PTS_TO_INCH_CONSTANT } from '../../constants/constants';
import {
  allowGroupSelection,
  getPageNumber,
  isSupportedFileName,
  getPdfHeight,
  getSelectedFieldsInRectangle,
  disableFieldBasedOnPermission,
  getFieldsAlongWithTimestampAnnotation,
  allowPropertiesUpdate,
} from '../../FormConfiguratorTool.utils';
import GroupAnnotationField from '../GroupAnnotationField';
import ConfigReader from '../../readers/config.reader';

const PDFAnnotator = ({
  onAction,
  selectedFields,
  totalPages,
  annotatedFields,
  canPerformUndo,
  canPerformRedo,
  addMedia,
  removeMedia,
  mediaList,
  isUploading,
  onFieldClick,
  formInfo,
  isEditing,
  pdfLoaded,
  selectedTooltipField,
  formScale,
  isReplaced,
  pageDimensions,
  groupSelectedAnnotations,
  hasViewOnlyPermission,
  fieldsAffectedAsArray,
  config,
  showDeleteFieldsBulkModal,
  renderBannerComponent,
  pdfLibraryFormsList,
  getDealerPropertyValue,
  isFormsLibrary,
  editType,
  hasEditGlobalFieldsPermission,
  isFcLiteModeEnabled,
}) => {
  const formURL = useMemo(() => _get(formInfo, 'url', null), [formInfo]);
  const formName = useMemo(() => _get(formInfo, 'name', null), [formInfo]);
  const [showRestoreConfirmation, setShowRestoreConfirmation] = useState(false);
  const [disableRestore, setDisableRestore] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const pageDimensionsRef = useRef({});
  const [pdfInfoVisible, setPDFInfoVisible] = useState(false);
  const pdfInfo = useRef({});
  const isMouseMoving = useRef(false);
  const canvasRef = useRef(null);
  const [mouseRectStartCoordinates, setMouseRectStartCoordinates] = useState({ startX: 0, startY: 0 });

  const printAlignModalRef = useRef(null);

  const hidePrintAlignModal = () => printAlignModalRef.current.hide();

  const savePrintAlignment = alignedAnnotatedFields => {
    onAction({
      type: ACTION_TYPES.ON_PRINT_ALIGNMENT_SAVE_CLICK,
      payload: { alignedAnnotatedFields },
    });
  };

  useEffect(() => {
    const scrollTop = document.querySelector('#formContainer')?.scrollTop % getPdfHeight(pageDimensions);
    const currPage = getPageNumber(scrollTop, pageDimensions, totalPages) || 1;
    setCurrentPage(currPage);
    if (pageDimensions[currPage]) {
      const { view } = pageDimensions[currPage];
      const widthInPts = Math.abs(view[0] - view[2]);
      const heightInPts = Math.abs(view[1] - view[3]);
      const widthInInches = Number(widthInPts * PTS_TO_INCH_CONSTANT).toFixed(2);
      const heightInInches = Number(heightInPts * PTS_TO_INCH_CONSTANT).toFixed(2);
      pdfInfo.current = {
        orientation:
          pageDimensions[currPage].width > pageDimensions[currPage].height ? __('Landscape') : __('Portrait'),
        size: `${widthInInches} x ${heightInInches} inches`,
      };
    }
  }, [totalPages, currentPage, pageDimensions]);
  useEffect(() => {
    let formInfoAfterReplace;
    if (!isEditing) {
      if (isReplaced) {
        formInfoAfterReplace = _get(mediaList, '[1]');
      } else formInfoAfterReplace = _get(mediaList, '[0]');
    } else formInfoAfterReplace = _get(mediaList, '[0]');
    // TODO: medialist keeps changing, add a condition to only update initially and at the end
    onAction({
      type: ACTION_TYPES.SET_FORM_INFO,
      payload: {
        formInfo: formInfoAfterReplace || formInfo,
        restoreFormInfo: _get(mediaList, '[0]'),
      },
    });
  }, [mediaList, onAction, formInfo, isEditing, isReplaced]);

  function onPageLoad(info) {
    const { height, pageNumber, width, view } = info;
    const pdfHeight = height || Math.abs(view[1] - view[3]) * formScale;
    pageDimensionsRef.current = { ...pageDimensionsRef.current, [pageNumber]: { width, height: pdfHeight, view } };

    if (_size(pageDimensionsRef.current) === totalPages) {
      onAction({
        type: ACTION_TYPES.UPDATE_PDF_PAGE_DIMENSIONS,
        payload: {
          pageDimensions: pageDimensionsRef.current,
        },
      });
    }
  }

  function onDocumentLoadSuccess(pdf) {
    const { numPages } = pdf;
    pageDimensionsRef.current = {};
    onAction({
      type: ACTION_TYPES.UPDATE_TOTAL_PAGES,
      payload: { totalPages: numPages },
    });
  }
  const onChange = useCallback(
    (field, newPosition) => {
      onAction({
        type: ACTION_TYPES.UPDATE_FIELD,
        payload: { field, newPosition },
      });
    },
    [onAction]
  );

  const onClick = useCallback(
    (field, isShiftPressed) => {
      if (isShiftPressed) {
        onAction({
          type: ACTION_TYPES.ON_FIELD_SELECT,
          payload: { field },
        });
      }
    },
    [onAction]
  );

  const updateSelectedFields = useCallback(
    (updateType, values = null) =>
      onAction({
        type: ACTION_TYPES.UPDATE_SELECTED_FIELDS,
        payload: { updateType, values },
      }),
    [onAction]
  );

  const onUndoClick = () => {
    onAction({ type: ACTION_TYPES.UNDO_LAST_ACTION });
  };
  const onRedoClick = () => {
    onAction({ type: ACTION_TYPES.REDO_LAST_ACTION });
  };

  const onZoomIn = useCallback(() => {
    onAction({
      type: ACTION_TYPES.UPDATE_FORM_SCALE,
      payload: { increaseBy: 0.1 },
    });
  }, [onAction]);

  const onZoomOut = useCallback(() => {
    onAction({
      type: ACTION_TYPES.UPDATE_FORM_SCALE,
      payload: { increaseBy: -0.1 },
    });
  }, [onAction]);

  const toggleGroupSelectedAnnotations = useCallback(() => {
    onAction({
      type: ACTION_TYPES.TOGGLE_GROUP_ANNOTAIONS,
    });
  }, [onAction]);

  const showCommonPropertiesModal = useCallback(() => {
    onAction({
      type: ACTION_TYPES.SHOW_COMMON_PROPERTIES_MODAL,
    });
  }, [onAction]);

  const showDeleteFieldsInBulkModal = useCallback(() => {
    onAction({
      type: ACTION_TYPES.SHOW_DELETE_FIELDS_BULK_MODAL,
    });
  }, [onAction]);

  const hideDeleteFieldsInBulkModal = useCallback(() => {
    onAction({
      type: ACTION_TYPES.HIDE_DELETE_FIELDS_BULK_MODAL,
    });
  }, [onAction]);

  const deleteFieldsBulk = useCallback(() => {
    onAction({
      type: ACTION_TYPES.DELETE_FIELDS_BULK,
      payload: { selectedFields: getFieldsAlongWithTimestampAnnotation(selectedFields, annotatedFields) },
    });
  }, [onAction, selectedFields, annotatedFields]);

  const onPrintAlignClick = () => {
    printAlignModalRef.current.show({
      component: PrintAlignModalContent,
    });
  };

  const bulkDuplicateFields = () => {
    onAction({
      type: ACTION_TYPES.BULK_DUPLICATE_FIELDS,
      payload: { selectedFields },
    });
  };

  const renderFormActions = () => (
    <FormActions
      onAlign={updateSelectedFields}
      onMatch={updateSelectedFields}
      annotatedFields={annotatedFields}
      selectedFields={selectedFields}
      toggleGroupSelectedAnnotations={toggleGroupSelectedAnnotations}
      onClickEditProperties={showCommonPropertiesModal}
      disableEditProperies={!allowPropertiesUpdate(annotatedFields, selectedFields)}
      deleteFieldsInBulk={showDeleteFieldsInBulkModal}
      canPrintAlign={hasViewOnlyPermission ? false : pdfLoaded}
      onPrintAlignClick={onPrintAlignClick}
      hidePrinterAlignment={ConfigReader.hidePrinterAlignment(config)}
      disableBulkDelete={!isFormsLibrary && (hasViewOnlyPermission || !hasEditGlobalFieldsPermission)}
      isFcLiteModeEnabled={isFcLiteModeEnabled}
      groupSelectedAnnotations={groupSelectedAnnotations}
      bulkDuplicateFields={bulkDuplicateFields}
    />
  );

  const onDuplicate = useCallback(
    selectedField => {
      onAction({
        type: ACTION_TYPES.DUPLICATE_SELECTED_FIELD,
        payload: { selectedField },
      });
    },
    [onAction]
  );

  const onDelete = useCallback(
    selectedField => {
      const selectedFields = getFieldsAlongWithTimestampAnnotation([selectedField], annotatedFields);
      onAction({
        type: _size(selectedField) > 1 ? ACTION_TYPES.DELETE_FIELDS_BULK : ACTION_TYPES.DELETE_FIELD_FROM_FORM,
        payload: { selectedFields },
      });
    },
    [onAction, annotatedFields]
  );

  const handleShowProperties = useCallback(
    selectedField => {
      onFieldClick(selectedField);
    },
    [onFieldClick]
  );

  const setShowFieldTooltip = useCallback(
    fieldId => {
      onAction({
        type: ACTION_TYPES.SHOW_FIELD_TOOLTIP,
        payload: { fieldId },
      });
    },
    [onAction]
  );

  const showPlainPDFViewModal = useCallback(() => {
    onAction({
      type: ACTION_TYPES.SHOW_PLAIN_PDF_VIEW_MODAL,
    });
  }, [onAction]);

  const disableFieldActions = useMemo(
    () =>
      disableFieldBasedOnPermission({
        isFormsLibrary,
        editType,
      }),
    [isFormsLibrary, editType]
  );

  const renderAnnotations = isViewOnlyMode => {
    let fieldsToAnnotate = annotatedFields;
    if (groupSelectedAnnotations && allowGroupSelection(annotatedFields, selectedFields)) {
      fieldsToAnnotate = _omit(annotatedFields, selectedFields);
    }
    return _map(_keys(fieldsToAnnotate), fieldId => {
      const field = annotatedFields[fieldId];
      return (
        <AnnotationField
          key={field.id}
          field={field}
          selected={_includes(selectedFields, field.id)}
          isAnchor={_head(selectedFields) === field.id}
          onChange={onChange}
          formScale={formScale}
          onClick={onClick}
          onDuplicate={onDuplicate}
          onDelete={onDelete}
          showFieldProperties={handleShowProperties}
          selectedTooltipField={selectedTooltipField}
          setShowTooltip={setShowFieldTooltip}
          annotatedFields={annotatedFields}
          disableDragging={isViewOnlyMode}
          enableResizing={!isViewOnlyMode}
          isInvalid={fieldsAffectedAsArray && fieldsAffectedAsArray.includes(`f${field.order}`)}
          isViewOnlyMode={isViewOnlyMode}
          config={config}
          disableFieldActions={disableFieldActions}
          isFcLiteModeEnabled={isFcLiteModeEnabled}
          selectedFields={selectedFields}
          updateSelectedFields={updateSelectedFields}
          toggleGroupSelectedAnnotations={toggleGroupSelectedAnnotations}
          groupSelectedAnnotations={groupSelectedAnnotations}
        />
      );
    });
  };

  const renderGroupAnnotaion = isViewOnlyMode => (
    <GroupAnnotationField
      annotatedFields={annotatedFields}
      selectedFields={selectedFields}
      onFieldClick={onClick}
      onChange={updateSelectedFields}
      isViewOnlyMode={isViewOnlyMode}
    />
  );

  const renderForm = () => (
    <PDFRenderer
      file={formURL}
      contentHeight={0}
      onDocumentLoadSuccess={onDocumentLoadSuccess}
      onPageLoadSuccess={onPageLoad}
      showRuler={false}
      renderHeader={_noop}
      zoomScale={formScale}
      style={{ height: '100%', width: '100%' }}
      showPageInfo={false}
      pdfRendererClass="pdfContainer" // classname needed for bounds of annotationField
      pageClassName={styles.pageWithBorder}
      bodyClassName={styles.pdfBodyClassName}
    />
  );

  const uploadFile = async (selectedFile = EMPTY_OBJECT) => {
    const file = _get(selectedFile, 'file', EMPTY_ARRAY);
    const fileList = _castArray(file);
    const pdfName = _replace(_get(file, 'name'), '.pdf', '');
    if (isSupportedFileName(pdfName)) {
      try {
        await addMedia(fileList);
      } catch {
        toaster(TOASTER_TYPE.ERROR, ERRORS.UPLOADING_FORM);
      }
    } else {
      toaster(TOASTER_TYPE.ERROR, ERRORS.INVALID_FILE_NAME);
    }
  };

  const handlePDFLibraryFormSelection = ({ type, payload }) => {
    const { option: selectedPDF } = payload;
    if (type === 'ON_CHANGE') {
      onAction({ type: ACTION_TYPES.SELECT_PDF_LIBRARY_FORM, payload: { selectedPDF } });
    }
  };

  const handlePDFLibrarySearch = searchText => {
    onAction({ type: ACTION_TYPES.SEARCH_PDF_LIBRARY_FORMS, payload: { searchText } });
  };

  const debouncedSearchPDFLIbrary = _debounce(handlePDFLibrarySearch, 1000);

  const renderUploadView = () => {
    const isNewDealPreviewWorkflow = getDealerPropertyValue(DEALER_PROPERTY_CONSTANTS.NEW_DEAL_PREVIEW_WORKFLOW);
    return (
      <div className={styles.fileSelectionContainer}>
        <DraggerUpload
          customRequest={uploadFile}
          showUploadList={false}
          className={cx(styles.uploadContainer, { [styles.uploadContainerWithPDFSearch]: isNewDealPreviewWorkflow })}
          accept=".pdf">
          <img src={UPLOAD_IMAGE} alt={__('Upload')} />
          <div className={styles.textContainer}>
            <Content className="mt-2">{__('Drag and drop your form here')}</Content>
            <Content>
              {__('or click to ')}
              <Heading size={5} className={styles.browseText}>
                {__('browse')}
              </Heading>
            </Content>
          </div>
        </DraggerUpload>
        <PropertyControlledComponent controllerProperty={isNewDealPreviewWorkflow}>
          <div className={styles.fullFlex}>
            <Content className={styles.dividerWrapper}>
              <span className={styles.dividerLine}>
                <hr />
              </span>{' '}
              {__('OR')}
              <span className={styles.dividerLine}>
                <hr />
              </span>
            </Content>
            <div className={styles.searchPdfKey}>
              <SelectInput
                showSearch
                showArrow={false}
                placeholder={__('Search PDF key...')}
                onAction={handlePDFLibraryFormSelection}
                onInputChange={debouncedSearchPDFLIbrary}
                shouldFetchOnValueChange
                options={pdfLibraryFormsList}
                isAsyncInputClearable={false}
                components={{ Option: PdfFormSelectOption }}
              />
            </div>
          </div>
        </PropertyControlledComponent>
      </div>
    );
  };

  const renderReplacePdfView = () => (
    <Upload customRequest={uploadFile} showUploadList={false} accept=".pdf" disabled={isReplaced || disableRestore}>
      <div className={isReplaced || disableRestore ? styles.disabledKey : ''}>{__('Replace PDF')}</div>
    </Upload>
  );

  const onDrop = event => {
    const selectedField = event.dataTransfer.getData('text/plain');
    const rect = document.querySelector('.react-pdf__Document').getBoundingClientRect();
    const coordinates = {
      x: event.pageX - rect.left - DEFAULT_FIELD_WIDTH / 2,
      y: event.pageY - rect.top - DEFAULT_FIELD_HEIGHT / 2,
    };
    onAction({
      type: ACTION_TYPES.ADD_FIELD_TO_FORM,
      payload: {
        selectedField: {
          ...JSON.parse(selectedField),
          x: coordinates.x,
          y: coordinates.y,
          page: getPageNumber(coordinates.y, pageDimensions, totalPages),
        },
      },
    });
  };
  const onDragOver = e => e?.preventDefault();

  const throttledPdfScroll = _throttle(e => {
    const scrollTop = _get(e, 'target.scrollTop', 0) % getPdfHeight(pageDimensions);
    setCurrentPage(getPageNumber(scrollTop, pageDimensions, totalPages) || 1);
  }, 100);

  const onPDFScroll = e => {
    e.persist();
    throttledPdfScroll(e);
  };

  const renderPageInfo = () => <Content className="mt-4 mb-2 px-4">{`Page ${currentPage} of ${totalPages}`}</Content>;
  const onMouseDown = event => {
    isMouseMoving.current = true;
    const rect = document.querySelector('.react-pdf__Document').getBoundingClientRect();
    setMouseRectStartCoordinates({
      startX: event.pageX - rect.left,
      startY: event.pageY - rect.top,
    });
  };
  const onMouseUp = event => {
    if (isMouseMoving.current) {
      isMouseMoving.current = false;
      const rect = document.querySelector('.react-pdf__Document').getBoundingClientRect();
      const selectedFieldsInRectangle = getSelectedFieldsInRectangle(annotatedFields, {
        ...mouseRectStartCoordinates,
        endX: event.pageX - rect.left,
        endY: event.pageY - rect.top,
      });
      onAction({
        type: ACTION_TYPES.ON_MULTIPLE_FIELD_SELECT,
        payload: { selectedFields: selectedFieldsInRectangle },
      });
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      ctx.clearRect(0, 0, canvas.width, canvas.height); // clear canvas
      setMouseRectStartCoordinates({ startX: 0, startY: 0 });
    }
  };
  const onMouseMove = event => {
    const rect = document.querySelector('.react-pdf__Document').getBoundingClientRect();
    const mousex = parseInt(event.pageX - rect.left, 10);
    const mousey = parseInt(event.pageY - rect.top, 10);
    if (isMouseMoving.current) {
      const { startX, startY } = mouseRectStartCoordinates;
      const width = mousex - startX;
      const height = mousey - startY;
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      ctx.translate(0.5, 0.5);
      ctx.clearRect(0, 0, canvas.width, canvas.height); // clear canvas
      ctx.beginPath();
      ctx.rect(startX, startY, width, height);
      ctx.fillStyle = 'rgb(200, 227, 255, 0.3)';
      ctx.fill();
      ctx.lineWidth = 1;
      ctx.strokeStyle = COLORS.dodgerBlue;
      ctx.stroke();
      ctx.closePath();
      ctx.setTransform(1, 0, 0, 1, 0, 0);
    }
  };

  const renderPDFView = () => (
    <>
      {renderBannerComponent()}
      {renderFileInfo()}
      <div className={`d-flex full-height ${styles.pdfViewScroll}`} onScroll={onPDFScroll} id="formContainer">
        <div className="full-height m-auto">
          <div>
            <div className={cx(styles.pdfContainer, { [styles.border]: pdfLoaded })}>
              <div className={`overflow-hidden ${styles.relative}`} onDrop={onDrop} onDragOver={onDragOver}>
                {renderForm()}
                <PropertyControlledComponent controllerProperty={_size(pageDimensions) === totalPages}>
                  <canvas
                    className={styles.canvasStyle}
                    width={_get(pageDimensions[1], `width`) || 0}
                    height={getPdfHeight(pageDimensions) + (totalPages - 1) * PAGE_MARGINS + totalPages * PAGE_BORDERS}
                    style={{ zIndex: isMouseMoving.current ? 100000 : 'auto' }}
                    ref={canvasRef}
                    onMouseDown={onMouseDown}
                    onMouseMove={onMouseMove}
                    onMouseUp={onMouseUp}
                  />
                </PropertyControlledComponent>
                <PropertyControlledComponent controllerProperty={pdfLoaded}>
                  <PropertyControlledComponent
                    controllerProperty={
                      groupSelectedAnnotations && allowGroupSelection(annotatedFields, selectedFields)
                    }>
                    {renderGroupAnnotaion(hasViewOnlyPermission)}
                  </PropertyControlledComponent>
                  {renderAnnotations(hasViewOnlyPermission)}
                </PropertyControlledComponent>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );

  const renderPdfToolbar = () => (
    <div className={styles.pdfToolbarContainer}>
      <PDFToolbar
        onUndo={onUndoClick}
        onRedo={onRedoClick}
        canPerformUndo={canPerformUndo}
        canPerformRedo={canPerformRedo}
        onZoomIn={onZoomIn}
        onZoomOut={onZoomOut}
        formScale={formScale}
        currentPage={currentPage}
        totalPages={totalPages}
        pageDimensions={pageDimensions}
      />
    </div>
  );

  const getMenuItems = () => [
    ...(!isFcLiteModeEnabled
      ? [
          {
            key: 'replace',
            label: __('Replace PDF'),
            renderer: renderReplacePdfView,
            disabled: isReplaced || disableRestore,
          },
          {
            key: 'restore',
            label: __('Restore PDF'),
            disabled: !isReplaced || disableRestore,
          },
        ]
      : []),
    {
      key: 'plainView',
      label: __('View Plain PDF'),
    },
  ];
  const onClickRowAction = key => {
    if (key === 'restore') {
      setShowRestoreConfirmation(true);
    }
    if (key === 'plainView') {
      showPlainPDFViewModal();
    }
  };

  const handleSubmitRestore = () => {
    removeMedia(mediaList);
    onAction({
      type: ACTION_TYPES.RESTORE_PDF,
    });
    setShowRestoreConfirmation(false);
    setDisableRestore(true);
  };

  const handleCancelRestore = () => {
    setShowRestoreConfirmation(false);
  };
  const renderFileInfo = () => {
    const hidePDFActions = ConfigReader.hidePDFActions(config);
    return (
      <div className={`d-flex justify-content-between px-4 py-1 ${styles.pageInfo}`}>
        <div className="d-flex">
          <Content>{formName}</Content>
          <Popover
            content={
              <div className={styles.pdfInfoContentWrapper}>
                <Content className="bold">{__('PDF Info')}</Content>
                <ul className={styles.pdfInfoList}>
                  <li>
                    <span className={styles.pdfInfoLabel}>{__('Orientation:')}</span>
                    <span className={styles.pdfInfoValue}>{pdfInfo?.current?.orientation}</span>
                  </li>
                  <li>
                    <span className={styles.pdfInfoLabel}>{__('Size:')}</span>
                    <span className={styles.pdfInfoValue}>{pdfInfo?.current?.size}</span>
                  </li>
                </ul>
              </div>
            }
            visible={pdfInfoVisible}
            onVisibleChange={value => setPDFInfoVisible(value)}
            trigger="hover"
            placement="bottomLeft"
            arrowPointAtCenter>
            <div className={cx({ [styles.highlightClassName]: pdfInfoVisible })}>
              <FontIcon className="ml-4" size={SIZES.MD_S}>
                icon-info
              </FontIcon>
            </div>
          </Popover>
        </div>
        <PropertyControlledComponent controllerProperty={!hasViewOnlyPermission && !hidePDFActions}>
          <KebabMenu
            onClickAction={onClickRowAction}
            menuItems={getMenuItems()}
            triggerElement={<span className="icon-overflow pointer" />}
          />
        </PropertyControlledComponent>
        <ConfirmationDialog
          title={__('Restore PDF')}
          isVisible={showRestoreConfirmation}
          width={Modal.SIZES.SM}
          destroyOnClose
          onCancel={handleCancelRestore}
          onSubmit={handleSubmitRestore}
          secondaryBtnText={__('Cancel')}
          submitBtnText={__('Confirm Restore')}
          content={__(
            'Restore pdf action will remove the current pdf and restore it to the last form pdf, do you wish to proceed ?'
          )}
        />
        <ConfirmationDialog
          title={__('Delete Fields')}
          isVisible={showDeleteFieldsBulkModal}
          width={Modal.SIZES.SM}
          destroyOnClose
          onCancel={hideDeleteFieldsInBulkModal}
          onSubmit={deleteFieldsBulk}
          secondaryBtnText={__('Cancel')}
          submitBtnText={__('Delete')}
          content={__('Are you sure you want to delete {{deleteFieldsCount}} fields?', {
            deleteFieldsCount: _size(getFieldsAlongWithTimestampAnnotation(selectedFields, annotatedFields)),
          })}
          okButtonProps={{ className: styles.deleteButtonBackgroundColor }}
        />
      </div>
    );
  };

  if (isUploading) {
    return (
      <div className={styles.container}>
        <div className={`full-width full-height d-flex justify-content-center align-items-center ${styles.container}`}>
          <Spinner />
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      {renderFormActions()}
      {formURL ? renderPDFView() : renderUploadView()}
      {renderPdfToolbar()}
      <PrintAlignModal
        ref={printAlignModalRef}
        childComponentProps={{
          formInfo,
          formScale,
          annotatedFields,
          onCloseModal: hidePrintAlignModal,
          onSaveModal: savePrintAlignment,
        }}
      />
    </div>
  );
};

PDFAnnotator.propTypes = {
  mediaList: PropTypes.array.isRequired,
  formScale: PropTypes.number.isRequired,
  onAction: PropTypes.func,
  addMedia: PropTypes.func,
  removeMedia: PropTypes.func,
  selectedFields: PropTypes.array,
  totalPages: PropTypes.number,
  annotatedFields: PropTypes.object,
  formInfo: PropTypes.object,
  canPerformUndo: PropTypes.bool,
  canPerformRedo: PropTypes.bool,
  isUploading: PropTypes.bool,
  onFieldClick: PropTypes.func,
  isEditing: PropTypes.bool,
  pdfLoaded: PropTypes.bool,
  selectedTooltipField: PropTypes.string,
  isReplaced: PropTypes.bool,
  groupSelectedAnnotations: PropTypes.bool,
  hasViewOnlyPermission: PropTypes.bool,
  pageDimensions: PropTypes.object,
  fieldsAffectedAsArray: PropTypes.object,
  config: PropTypes.object,
  showDeleteFieldsBulkModal: PropTypes.bool,
  renderBannerComponent: PropTypes.func,
  pdfLibraryFormsList: PropTypes.array,
  getDealerPropertyValue: PropTypes.func,
  isFcLiteModeEnabled: PropTypes.bool,
};

PDFAnnotator.defaultProps = {
  onAction: _noop,
  addMedia: _noop,
  removeMedia: _noop,
  selectedFields: EMPTY_ARRAY,
  totalPages: 0,
  annotatedFields: EMPTY_OBJECT,
  pageDimensions: EMPTY_OBJECT,
  formInfo: EMPTY_OBJECT,
  canPerformUndo: false,
  canPerformRedo: false,
  isUploading: false,
  onFieldClick: _noop,
  isEditing: false,
  pdfLoaded: false,
  selectedTooltipField: null,
  isReplaced: false,
  groupSelectedAnnotations: false,
  hasViewOnlyPermission: false,
  fieldsAffectedAsArray: EMPTY_ARRAY,
  config: EMPTY_OBJECT,
  showDeleteFieldsBulkModal: false,
  renderBannerComponent: _noop,
  pdfLibraryFormsList: EMPTY_ARRAY,
  getDealerPropertyValue: _noop,
  isFcLiteModeEnabled: false,
};

export default React.memo(PDFAnnotator);
