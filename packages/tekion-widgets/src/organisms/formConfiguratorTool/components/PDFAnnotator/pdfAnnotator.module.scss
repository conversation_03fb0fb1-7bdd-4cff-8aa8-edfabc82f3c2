@import "tstyles/component.scss";

.container {
  flex-grow: 1;
  border-right: 0.1rem solid $platinum;
  padding-bottom: 10rem;
  @include full-height;
  overflow-y: hidden;
  position: relative;
}

.pdfContainer {
  @include full-height;
  overflow-y: auto;
  position: relative;
}

.relative {
  position: relative;
}

.fileSelectionContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.uploadContainer {
  flex: 1;
  :global .ant-upload-drag {
    border: none;
    background-color: $white;
  }
}

.uploadContainerWithPDFSearch {
  :global(.ant-upload-drag-container) {
    vertical-align: bottom !important;
  }
}

.uploadImg {
  margin-top: 4rem;
  height: 4.8rem;
  width: auto;
}

.browseText {
  display: inline;
  color: $denim;
  font-weight: bold;
}

.searchPdfKey {
  width: 60%;
  margin: 1% 20%;
}

.dividerLine {
  width: 20%;
}

.dividerWrapper {
  font-size: 2rem;
  font-style: italic;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  padding-bottom: 16px;
}

.pageInfo {
  background: $lightGray;
}

.pageWithBorder {
  border: 0.1rem solid $platinum;
  margin-bottom: 1rem;
}

.pdfBodyClassName {
  overflow-x: hidden;
  overflow-y: hidden;
}

.pdfViewScroll {
  overflow-x: scroll;
}

.disabledKey {
  color: $ashGray;
  cursor: not-allowed;
}

.canvasStyle {
  position: absolute;
  top: 0;
  left: 0;
}

.pdfInfoContentWrapper {
  padding: 1rem;
}

.pdfInfoList {
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.pdfInfoList li {
  display: flex;
}

.pdfInfoLabel {
  margin-right: 0.4rem;
  color: $atomic;
}

.pdfInfoValue {
  color: $black;
}

.highlightClassName {
  color: $dodgerBlueLight;
}

.deleteButtonBackgroundColor {
  background-color: $mordantRed;
  border-color: $mordantRed;
  &:hover {
    background-color: $mordantRed;
    border-color: $mordantRed;
  }
}

.fullFlex {
  flex: 1;
}

.pdfToolbarContainer {
  @include flex($justify-content: center);
  bottom: 0.8rem;
  width: 100%;
  position: absolute;
}
