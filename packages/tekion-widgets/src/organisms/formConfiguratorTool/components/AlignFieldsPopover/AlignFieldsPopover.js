import React from 'react';
import cx from 'classnames';
import PropTypes from 'prop-types';
import _noop from 'lodash/noop';
import _map from 'lodash/map';
import Content from '@tekion/tekion-components/src/atoms/Content';
import Button from '@tekion/tekion-components/src/atoms/Button';
import Popover, { POPOVER_PLACEMENT, POPOVER_TRIGGER } from '@tekion/tekion-components/src/molecules/popover';
import FontIcon, { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';
import Tooltip, { TOOLTIP_PLACEMENT } from '@tekion/tekion-components/src/atoms/tooltip';

import styles from './alignFieldsPopover.module.scss';
import { ALIGN_FORM_FIELDS } from '../../FormConfiguratorTool.constants';

const AlignFieldsPopover = ({ containerClass, onClick, canAlign }) => {
  const [popoverVisible, setPopoverVisible] = React.useState(false);
  const togglePopOver = () => setPopoverVisible(!popoverVisible);

  const renderPopoverContent = React.useMemo(
    () => (
      <div className={styles.popoverContent} data-testid="align-fields-content">
        {_map(ALIGN_FORM_FIELDS, alignment => (
          <Tooltip placement={TOOLTIP_PLACEMENT.TOP} title={alignment.label}>
            <Button
              onClick={() => onClick(alignment.type)}
              className={cx('m-r-4', styles.alignIconButton)}
              view={Button.VIEW.TERTIARY}
              key={alignment.type}>
              <FontIcon size={SIZES.MD} className={styles.alignIcon}>
                {alignment.icon}
              </FontIcon>
            </Button>
          </Tooltip>
        ))}
      </div>
    ),
    [onClick]
  );

  return (
    <Popover
      placement={canAlign ? POPOVER_PLACEMENT.BOTTOM : POPOVER_PLACEMENT.TOP}
      trigger={canAlign ? POPOVER_TRIGGER.CLICK : POPOVER_TRIGGER.HOVER}
      mouseEnterDelay={0.5}
      content={canAlign ? renderPopoverContent : null}
      visible={popoverVisible}
      onVisibleChange={togglePopOver}>
      <Tooltip
        placement={TOOLTIP_PLACEMENT.TOP}
        title={!canAlign ? __('Select at least 2 fields to enable action') : ''}>
        <div
          className={cx(styles.labelContainer, {
            [styles.labelContainerHover]: canAlign,
            [styles.labelContainerActive]: popoverVisible && canAlign,
          })}
          role="button"
          tabIndex={0}>
          <FontIcon size={SIZES.S} className={cx({ [styles.active]: popoverVisible, [styles.disabled]: !canAlign })}>
            icon-align-left
          </FontIcon>
          <Content size={5} className={cx('m-x-8', { [styles.active]: popoverVisible, [styles.disabled]: !canAlign })}>
            {__('Align')}
          </Content>
          <FontIcon size={SIZES.S} className={cx({ [styles.active]: popoverVisible, [styles.disabled]: !canAlign })}>
            {popoverVisible && canAlign ? 'icon-chevron-up' : 'icon-chevron-down'}
          </FontIcon>
        </div>
      </Tooltip>
    </Popover>
  );
};

AlignFieldsPopover.propTypes = {
  containerClass: PropTypes.string,
  onClick: PropTypes.func,
  canAlign: PropTypes.bool,
};

AlignFieldsPopover.defaultProps = {
  containerClass: '',
  onClick: _noop,
  canAlign: false,
};

export default AlignFieldsPopover;
