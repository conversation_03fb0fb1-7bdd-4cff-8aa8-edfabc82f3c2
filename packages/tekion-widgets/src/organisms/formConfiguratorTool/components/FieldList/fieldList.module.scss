@import "tstyles/component.scss";

$search-bar-height: 3.2rem;

.fieldsPaneWrapper {
  @include full-width;
  @include full-height;
  @include flex($justify-content: flex-start);

  flex-direction: column;
  position: relative;
}

.searchBar {
  @include full-width;
  input {
    border: 0.1rem solid $ashGray;
  }
}

.header_actions {
  @include flex($align-items: center);
  margin-top: -0.5rem;
}

.fields_header {
  @include flex($align-items: center);
}

.fields_body {
  padding: 1rem 2rem;
}

.page_section {
  padding-bottom: 2rem;
}

.field_info {
  @include flex($align-items: center);
}

.field_wrapper {
  @include flex($align-items: center, $justify-content: space-between);
  border-bottom: 1px solid $lightGray;
  margin-left: -0.8rem;
  padding: 0.8rem;
}

.header_kebab_wrapper {
  transform: rotate(90deg);
}

.fieldTypeIcon {
  margin-right: 1rem;
}

.noFieldsWrapper {
  height: 100%;
  width: 100%;
  flex-grow: 1;
  @include flex($justify-content: center, $align-items: center);

  .noFieldsTextContainer {
    @include flex($align-items: center);
    flex-direction: column;
  }
}

.arrowIcon {
  height: 3.2rem !important;
  padding: 0.8rem 0 !important;
}
.tabText {
  font-size: 1.4rem !important;
}

.searchBarWrapper {
  height: $search-bar-height;
  margin: 2rem;
  flex: 1;

  :global([class*="input_input"]) {
    border-radius: 0.4rem;
  }

  :global(.ant-input-affix-wrapper) {
    line-height: 1;
  }
}

.searchBox {
  width: 100% !important;
}

.textInput {
  @include full-width;
}

.tabBar {
  display: flex;
  flex-direction: column;
  :global(.ant-tabs-tab) {
    padding: 1rem !important;
  }
  :global {
    .ant-tabs-content {
      height: 100%;
    }
  }
}

.collapseHeaderWrapper {
  margin: 1rem !important;
}

.headerItems {
  :global(.ant-collapse-header) {
    margin: 1rem !important;
  }
  :global(.ant-collapse-arrow) {
    left: 0rem !important;
  }
}

.tabText {
  font-family: inherit;
}

.collaper_wrapper {
  width: 100%;
  flex-grow: 1;
  height: 0;
  overflow: scroll;
  :global(.ant-collapse-borderless > .ant-collapse-item) {
    border-top: none !important;
  }
}

.collapseContent {
  :global(.ant-collapse-content-box) {
    padding-left: 3.4rem !important;
    padding-right: 1.2rem !important;
  }
}

.reorderInputFieldsKebabMenu {
  margin-left: 1rem;
  &:hover {
    color: $tertiary-button-hover-text;
    background-color: $aliceBlue;
  }
}

.selectedField {
  @extend .field_wrapper;
  background: $aliceBlue;

  border-radius: 0.4rem;
}

.selectedFieldText {
  color: $azure;
}
