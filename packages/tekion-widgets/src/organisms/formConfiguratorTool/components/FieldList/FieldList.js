import React, { Component } from 'react';
import { defaultMemoize } from 'reselect';
import PropTypes from 'prop-types';
import cx from 'classnames';

import _debounce from 'lodash/debounce';
import _map from 'lodash/map';
import _get from 'lodash/get';
import _includes from 'lodash/includes';
import _intersection from 'lodash/intersection';
import _size from 'lodash/size';
import _curry from 'lodash/curry';
import _noop from 'lodash/noop';
import _filter from 'lodash/filter';
import _forEach from 'lodash/forEach';
import _flatMap from 'lodash/flatMap';
import _every from 'lodash/every';
import _split from 'lodash/split';
import _join from 'lodash/join';
import _keys from 'lodash/keys';

import { EMPTY_STRING, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import Heading from '@tekion/tekion-components/src/atoms/Heading';
import Button from '@tekion/tekion-components/src/atoms/Button';
import Checkbox from '@tekion/tekion-components/src/atoms/checkbox';
import Content from '@tekion/tekion-components/src/atoms/Content';
import FontIcon, { SIZES as FONT_SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import Tabs from '@tekion/tekion-components/src/molecules/Tabs';
import { Collapse } from '@tekion/tekion-components/src/molecules/Collapse';
import SearchInput from '@tekion/tekion-components/src/molecules/searchInput';
import Ellipsis from '@tekion/tekion-components/src/atoms/Ellipsis';
import KebabMenu from '@tekion/tekion-components/src/molecules/KebabMenu';
import KebabMenuWithIcons from '../kebabMenuWithIcons';
import fieldsEmptyState from '../../assets/images/Fields_empty_state.png';

import ACTION_TYPES from '../../FormConfiguratorTool.actionTypes';
import styles from './fieldList.module.scss';
import {
  getAnnotationsByPage,
  getFieldsAndConfigsTabsUid,
  getAllTabsKeys,
  getAnnotationsByType,
  disableFieldBasedOnPermission,
} from '../../FormConfiguratorTool.utils';
import {
  FIELD_MENU_ITEMS,
  FIELD_MENU_ITEMS_TYPES,
  FIELDS_AND_CONFIGS_TABS,
  FIELDS_AND_CONFIGS_TABS_TITLE,
  FIELDS_AND_CONFIGS_STYLE_CONFIG,
  FIELD_TYPES,
} from '../../FormConfiguratorTool.constants';
import { getKebabMenuOptions } from './helpers/fieldList.helpers';

class FieldList extends Component {
  static propTypes = {
    onAction: PropTypes.func,
    selectedFields: PropTypes.array,
    annotatedFields: PropTypes.array,
    onSearch: PropTypes.func,
    showFieldTypeICons: PropTypes.bool,
    onExpandFields: PropTypes.func,
    enableFormsPreview: PropTypes.bool,
  };

  static defaultProps = {
    onAction: _noop,
    onSearch: _noop,
    selectedFields: EMPTY_ARRAY,
    annotatedFields: EMPTY_ARRAY,
    showFieldTypeICons: false,
    onExpandFields: _noop,
    enableFormsPreview: false,
  };

  getAnnotatedFields = defaultMemoize((annotatedFields, searchQueryValue) => {
    const annotatedFieldsToDisplay = [];
    _forEach(annotatedFields, fieldSections => {
      const newFieldSection = Object.assign({}, fieldSections);
      newFieldSection.values = _filter(newFieldSection?.values, value =>
        _every(_split(searchQueryValue, ' '), query => _includes(value?.fieldName?.toLowerCase(), query?.toLowerCase()))
      );
      if (newFieldSection?.values?.length) {
        annotatedFieldsToDisplay.push(newFieldSection);
      }
    });
    return annotatedFieldsToDisplay;
  });

  handleFieldActionClick = _curry((selectedField, type) => {
    const { onAction } = this.props;
    if (type === FIELD_MENU_ITEMS_TYPES.DUPLICATE) {
      onAction({
        type: ACTION_TYPES.DUPLICATE_SELECTED_FIELD,
        payload: { selectedField },
      });
    } else if (type === FIELD_MENU_ITEMS_TYPES.DELETE) {
      const { id } = selectedField;
      onAction({
        type: ACTION_TYPES.DELETE_FIELD_FROM_FORM,
        payload: { selectedField: id },
      });
    } else if (type === FIELD_MENU_ITEMS_TYPES.PROPERTIES) {
      onAction({
        type: ACTION_TYPES.ON_FIELD_PROPERTIES_CLICK,
        payload: { selectedField },
      });
    }
  });

  constructor(props) {
    super(props);
    this.state = { searchQueryValue: EMPTY_STRING };
    this.handleSearchWithDebounce = _debounce(props.onSearch, 500);
  }

  renderFieldActions = field => {
    const { hasViewOnlyPermission, isFormsLibrary, editType, isFcLiteModeEnabled } = this.props;

    const validOptions = getKebabMenuOptions({
      hasViewOnlyPermission,
      isFormsLibrary,
      editType,
      isFcLiteModeEnabled,
    });

    return (
      <div className={styles.header_kebab_wrapper}>
        <KebabMenuWithIcons
          icon="icon-overflow"
          placement="bottomLeft"
          options={validOptions}
          onClick={this.handleFieldActionClick(field)}
        />
      </div>
    );
  };

  renderFieldSerial = order => {
    const { showFieldTypeICons } = this.props;
    // render icon based on fieldType
    if (showFieldTypeICons) {
      return (
        <FontIcon size="M" className={styles.fieldTypeIcon}>
          icon-detail-view
        </FontIcon>
      );
    }
    return `${order}. `;
  };

  renderField = _curry((selectedChildIds, field) => {
    const { onAction, hasViewOnlyPermission } = this.props;
    const { id, fieldAnnotation: label, order, fieldType } = field;
    const onFieldSelect = () => onAction({ type: ACTION_TYPES.ON_TOGGLE_SINGLE_ANNOTATED_FIELD, payload: { id } });

    const selected = _includes(selectedChildIds, id);

    return (
      <div className={cx(styles.field_wrapper, { [styles.selectedField]: selected })}>
        <div className={styles.field_info}>
          <Checkbox disabled={hasViewOnlyPermission} value={_includes(selectedChildIds, id)} onClick={onFieldSelect} />
          <Content className={cx('m-l-16 d-flex', { [styles.selectedFieldText]: selected })} size={5}>
            <Ellipsis ellipsisLength={10} lines={1} tooltip>
              {this.renderFieldSerial(order)} {__(label)} &nbsp;
              <PropertyControlledComponent controllerProperty={fieldType === FIELD_TYPES.CUSTOM_FIELD}>
                {__('(custom)')}
              </PropertyControlledComponent>
            </Ellipsis>
          </Content>
        </div>
        {this.renderFieldActions(field)}
      </div>
    );
  });

  renderPageSectionHeaderField = ({ page, allChildIds, selectedChildIds }) => {
    const { onAction, hasViewOnlyPermission } = this.props;
    const areSomeSelected = _size(selectedChildIds) > 0;
    const areAllChildrenSelected = areSomeSelected && _size(selectedChildIds) === _size(allChildIds);
    const onPageSelect = event => {
      onAction({
        type: ACTION_TYPES.ON_TOGGLE_BULK_ANNOTATED_FIELDS,
        payload: { ids: allChildIds },
      });
      event.stopPropagation();
    };
    return (
      <div className="flex align-items-center m-t-8 m-l-24">
        <Checkbox
          disabled={hasViewOnlyPermission}
          value={areAllChildrenSelected}
          indeterminate={!areAllChildrenSelected && areSomeSelected}
          onClick={onPageSelect}
        />
        <Content className="m-l-16" size={4} colorVariant={Content.COLOR_VARIANTS.GREY}>
          {page}
        </Content>
      </div>
    );
  };

  // this index should not be used, as it may affect search. Same issue with page number index
  renderPageSection = ({ page, values }, index) => {
    const { selectedFields: selectedAnnotatedFields } = this.props;
    const allChildIds = _map(values, value => _get(value, 'id'));
    const selectedChildIds = _intersection(selectedAnnotatedFields, allChildIds);
    return (
      <Collapse.Panel
        panelKey={index}
        header={this.renderPageSectionHeaderField({ page, allChildIds, selectedChildIds })}
        headerClass={styles.collapseHeader}
        className={styles.collapseContent}>
        <div className={styles.form_body_wrapper}>{_map(values, this.renderField(selectedChildIds))}</div>
      </Collapse.Panel>
    );
  };

  renderExpandIcon = ({ isActive }) => (
    <span>
      <FontIcon size={FONT_SIZES.M}>{isActive ? 'icon-caret-down-filled' : 'icon-caret-right-filled'}</FontIcon>
    </span>
  );

  renderFieldCollapser = annotatedFieldsToDisplay => {
    const keysForExpandingFields = _keys(annotatedFieldsToDisplay);
    const totalAnnotatedFields = _size(_flatMap(annotatedFieldsToDisplay, page => page?.values));
    if (totalAnnotatedFields === 0) {
      return this.renderImageWithDesc();
    }
    return (
      <div className={styles.collaper_wrapper}>
        <Collapse
          bordered={false}
          defaultActiveKey={keysForExpandingFields}
          key={_join(keysForExpandingFields)}
          collapseHeaderClassName={styles.collapseHeaderWrapper}
          arrowClass={styles.arrowIcon}
          expandIcon={this.renderExpandIcon}
          className={styles.headerItems}>
          {_map(annotatedFieldsToDisplay, this.renderPageSection)}
        </Collapse>
      </div>
    );
  };

  handleSearch = searchData => {
    this.setState({ searchQueryValue: searchData });
    this.handleSearchWithDebounce(searchData);
  };

  clearSearchText = () => this.setState({ searchQueryValue: EMPTY_STRING });

  renderSearchBar = () => {
    const { searchQueryValue } = this.state;
    return (
      <div className={styles.searchBarWrapper}>
        <SearchInput
          className={styles.searchBar}
          inputContainerClassName={styles.searchBar}
          textInputClass={styles.textInput}
          onSearch={() => this.handleSearch(searchQueryValue)}
          value={searchQueryValue}
          onChange={this.handleSearch}
          allowClear
        />
      </div>
    );
  };

  renderHeader = () => {
    const { onExpandFields, showReorderFieldsModal, hasViewOnlyPermission, enableFormsPreview } = this.props;
    return (
      <div className={styles.fields_header}>
        {this.renderSearchBar()}
        {!hasViewOnlyPermission && (
          <Button view={Button.VIEW.ICON} icon="icon-expand" className={styles.expand} onClick={onExpandFields} />
        )}
        <KebabMenu
          className={styles.reorderInputFieldsKebabMenu}
          onClickAction={showReorderFieldsModal}
          menuItems={[
            {
              label: __('Organise Fields'),
              key: 'ORGANIZE_FIELDS',
              disabled: hasViewOnlyPermission || !enableFormsPreview,
            },
          ]}
          triggerElement={
            <span className={cx('icon-overflow pointer p-t-4 p-r-8 p-b-4 p-l-8', styles.header_kebab_wrapper)} />
          }
        />
      </div>
    );
  };

  renderImageWithDesc() {
    return (
      <div className={styles.noFieldsWrapper}>
        <div className={styles.noFieldsTextContainer}>
          <img src={fieldsEmptyState} alt={__('No fields added')} />
          <div>{__('Annotated form fields')}</div>
          <div>{__('will appear here')}</div>
        </div>
      </div>
    );
  }

  renderTabContent = tab => {
    const { annotatedFields: annotatedFieldsObject } = this.props;
    const { searchQueryValue } = this.state;
    const { globalAnnotations, configurationAnnotations, customAnnotations } =
      getAnnotationsByType(annotatedFieldsObject);
    const annotatedFields = getAnnotationsByPage({ ...globalAnnotations, ...customAnnotations });
    const configAnnotatedFields = getAnnotationsByPage(configurationAnnotations);
    const annotatedFieldsToDisplay = this.getAnnotatedFields(annotatedFields, searchQueryValue);
    const configAnnotatedFieldsToDisplay = this.getAnnotatedFields(configAnnotatedFields, searchQueryValue);

    switch (tab) {
      case FIELDS_AND_CONFIGS_TABS.GLOBAL_FIELDS:
        return (
          <div className={styles.fieldsPaneWrapper}>
            {this.renderHeader()}
            {this.renderFieldCollapser(annotatedFieldsToDisplay)}
          </div>
        );
      case FIELDS_AND_CONFIGS_TABS.CONFIGURATIONS:
        return (
          <div className={styles.fieldsPaneWrapper}>
            {this.renderHeader()}
            {this.renderFieldCollapser(configAnnotatedFieldsToDisplay)}
          </div>
        );
      default:
        return null;
    }
  };

  renderTabs = () => (
    <Tabs
      defaultActiveKey={FIELDS_AND_CONFIGS_TABS.CONFIGURATIONS}
      tabBarStyle={FIELDS_AND_CONFIGS_STYLE_CONFIG}
      keys={getAllTabsKeys()}
      onChange={this.clearSearchText}
      className={`full-height ${styles.tabBar}`}>
      {Object.keys(FIELDS_AND_CONFIGS_TABS).map(tab => (
        <Tabs.TabPane
          tab={
            <Heading size={5} className={styles.tabText}>
              {FIELDS_AND_CONFIGS_TABS_TITLE[tab]}
            </Heading>
          }
          key={getFieldsAndConfigsTabsUid(tab)}>
          {this.renderTabContent(tab)}
        </Tabs.TabPane>
      ))}
    </Tabs>
  );

  render() {
    return this.renderTabs();
  }
}

export default FieldList;
