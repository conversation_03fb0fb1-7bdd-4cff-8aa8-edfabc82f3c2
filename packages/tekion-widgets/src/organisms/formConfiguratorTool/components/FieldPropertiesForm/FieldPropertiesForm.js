import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';

import cx from 'classnames';
import _get from 'lodash/get';
import _noop from 'lodash/noop';
import _map from 'lodash/map';
import _size from 'lodash/size';

import actionTypes from '@tekion/tekion-components/src/organisms/FormBuilder/constants/actionTypes';
import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import Tab from '@tekion/tekion-components/src/molecules/Tabs/ScrollSpy';
import Divider from '@tekion/tekion-components/src/atoms/Divider';

import ACTION_TYPES from './FieldPropertiesForm.actionTypes';
import VerticalTabs from '../VerticalTabs';
import { ANNOTATION_MODAL_TYPES, FIELD_TYPES } from '../../FormConfiguratorTool.constants';
import {
  getFieldTabKey<PERSON>enderer,
  getAdditionalProps,
  getTabPaneProps,
  getTabHeadings,
} from './FieldPropertiesForm.helpers';
import { getFieldTabs } from './FieldPropertiesForm.utils';
import styles from './fieldPropertiesModal.module.scss';
import ConfigReader from '../../readers/config.reader';

class FieldPropertiesForm extends PureComponent {
  componentDidMount() {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_FIELD_FORM_MOUNT,
    });
  }

  getSections = () => {
    const { fieldProperties, tabConfigurations, hideTabs, config, isFcLiteModeEnabled } = this.props;
    const fieldType = _get(fieldProperties, 'fieldType') || FIELD_TYPES.CUSTOM_FIELD;
    const supportedGlobalFieldTabs = ConfigReader.supportedGlobalFieldTabs(config);
    const supportedCustomFieldTabs = ConfigReader.supportedCustomFieldTabs(config);
    return (
      tabConfigurations[fieldType] ||
      getFieldTabs({
        fieldProperties,
        hideTabs,
        supportedGlobalFieldTabs,
        supportedCustomFieldTabs,
        isFcLiteModeEnabled,
      })
    );
  };

  updateFormValues = category => payload => {
    const { onAction } = this.props;
    onAction({
      type: actionTypes.ON_FIELD_CHANGE,
      payload: { ...payload, category },
    });
  };

  handleMultiLingualFieldChange = payload => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_LANGUAGE_CHANGE,
      payload: { ...payload },
    });
  };

  renderComponent = tabKey => {
    const { fieldProperties, hasViewOnlyPermission, isFormsLibrary, isFcLiteModeEnabled, disableEditForFCLiteMode } =
      this.props;
    const Component = getFieldTabKeyRenderer(fieldProperties)[tabKey];
    return (
      <div className={cx({ [styles.disabledTab]: hasViewOnlyPermission })} id={tabKey}>
        <Component
          sectionName={tabKey}
          values={_get(fieldProperties, tabKey)}
          updateFormValues={this.updateFormValues(tabKey)}
          formType={ANNOTATION_MODAL_TYPES.CONFIGURATION_FIELD}
          {...getAdditionalProps[tabKey](this.props)}
          handleMultiLingualFieldChange={this.handleMultiLingualFieldChange}
          isFormsLibrary={isFormsLibrary}
          isFcLiteModeEnabled={isFcLiteModeEnabled}
          disableEditForFCLiteMode={disableEditForFCLiteMode}
        />
      </div>
    );
  };

  renderScrollableContent = () => {
    const sections = this.getSections();

    return (
      <div>
        {_map(sections, (sectionKey, idx) => (
          <>
            {this.renderComponent(sectionKey)}
            {idx < _size(sections) - 1 && <Divider className="m-t-0" />}
          </>
        ))}
      </div>
    );
  };

  renderTabPanes = () => {
    const { fieldProperties, errorTabs } = this.props;

    const sections = this.getSections();

    const tabPaneProps = getTabPaneProps(fieldProperties);
    const tabHeading = getTabHeadings(fieldProperties, errorTabs);

    return _map(sections, key => <Tab.TabPane tab={tabHeading[key]} key={key} {...tabPaneProps} />);
  };

  render() {
    return (
      <VerticalTabs
        navigate={_noop}
        tabs={this.renderTabPanes()}
        contentContainerClassName={styles.scrollableContainer}
        render={this.renderScrollableContent}
      />
    );
  }
}

FieldPropertiesForm.propTypes = {
  fieldProperties: PropTypes.object,
  tabConfigurations: PropTypes.object,
  hideTabs: PropTypes.object,
  hasViewOnlyPermission: PropTypes.bool,
  onAction: PropTypes.func,
  config: PropTypes.object,
  isFormsLibrary: PropTypes.bool,
  errorTabs: PropTypes.object,
  isFcLiteModeEnabled: PropTypes.bool,
};

FieldPropertiesForm.defaultProps = {
  fieldProperties: EMPTY_OBJECT,
  tabConfigurations: EMPTY_OBJECT,
  hideTabs: EMPTY_ARRAY,
  hasViewOnlyPermission: false,
  onAction: _noop,
  config: EMPTY_OBJECT,
  isFormsLibrary: false,
  errorTabs: EMPTY_OBJECT,
  isFcLiteModeEnabled: false,
};

export default FieldPropertiesForm;
