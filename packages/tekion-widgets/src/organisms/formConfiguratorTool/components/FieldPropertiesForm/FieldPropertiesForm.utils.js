import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _find from 'lodash/find';
import _size from 'lodash/size';
import _values from 'lodash/values';
import _compact from 'lodash/compact';
import _some from 'lodash/some';
import _head from 'lodash/head';
import _map from 'lodash/map';
import _includes from 'lodash/includes';
import _set from 'lodash/set';
import _filter from 'lodash/filter';

import { EMPTY_OBJECT, EMPTY_STRING, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import { FORM_PRINTER_TYPES } from '@tekion/tekion-base/constants/formConfigurator/printers';
import { FIELD_FORM_TAB_SECTIONS, CONFIG_KEYS, REQUIRED_CUSTOM_FORM_FIELDS } from './FieldPropertiesForm.constants';
import {
  FIELD_TYPES,
  FIELD_TYPE_VALUES,
  DEFAULT_SCALE,
  LASER_FONT_VALUES,
  IMPACT_FONT_VALUES,
} from '../../FormConfiguratorTool.constants';
import {
  areCoordinatesValid,
  getDefaultFieldTextFormatting,
  getFieldDisplayName,
  isValidDimension,
} from '../../FormConfiguratorTool.utils';
import { FORM_FIELDS, FIELD_DEFAULT_STATE_VALUES } from '../CustomFieldGeneralForm/CustomFieldGeneralForm.constants';
import { CONFIG_FIELD_TYPES } from '../Rules/Conditions/Conditions.constant';

export const shouldActionDisabled = ({ fieldProperties = {}, errors, pageDimensions, formScale, isGlobalForm }) => {
  const errorTabs = {
    [CONFIG_KEYS.GENERAL_CONFIG]: false,
    [CONFIG_KEYS.TEXT_FORMATTING]: false,
    [CONFIG_KEYS.OFFSETS]: false,
    [CONFIG_KEYS.DIMENSIONS]: false,
    [CONFIG_KEYS.RULES]: false,
    [CONFIG_KEYS.FORMULA]: false,
  };
  const generalConfig = _get(fieldProperties, CONFIG_KEYS.GENERAL_CONFIG, EMPTY_OBJECT);

  if (!isValidDimension(fieldProperties, pageDimensions, formScale)) {
    _set(errorTabs, CONFIG_KEYS.DIMENSIONS, true);
  }

  const scale = formScale / DEFAULT_SCALE;
  if (
    !areCoordinatesValid(
      {
        top: _get(fieldProperties, 'offsets.top') * scale,
        left: _get(fieldProperties, 'offsets.left') * scale,
        height: _get(fieldProperties, 'dimensions.height') * scale,
        width: _get(fieldProperties, 'dimensions.width') * scale,
      },
      pageDimensions
    )
  ) {
    _set(errorTabs, CONFIG_KEYS.DIMENSIONS, true);
    _set(errorTabs, CONFIG_KEYS.OFFSETS, true);
  }
  if (checkIsCustomField(fieldProperties)) {
    const requiredFields = [...REQUIRED_CUSTOM_FORM_FIELDS];
    const fieldType = _get(fieldProperties, 'general.type[0]', null);
    const fieldDefaultStateValue = _get(fieldProperties, 'general.fieldDefaultState', null);
    if (fieldType === FIELD_TYPE_VALUES.CHECKBOX && fieldDefaultStateValue === FIELD_DEFAULT_STATE_VALUES.USER_INPUT) {
      requiredFields.push(FORM_FIELDS.FIELD_USER_INPUT_VALUE);
    }
    const isGeneralInValid =
      !_isEmpty(_find(requiredFields, field => !generalConfig[field] || _isEmpty(generalConfig[field]))) ||
      _size(_compact(_values(errors)));
    const formulas = _get(fieldProperties, 'formula.formulas', EMPTY_ARRAY);
    let formulasInValid = false;
    _some(formulas, formula => {
      const exp = _get(formula, 'expression', EMPTY_STRING);
      if (_isEmpty(exp)) {
        formulasInValid = true;
        return true;
      }

      const conditions = _get(formula, 'conditions', EMPTY_ARRAY);
      let haveFailedCondition = false;
      _some(conditions, condition => {
        const operator = _get(condition, 'operator', EMPTY_STRING);
        const targeting = _get(condition, 'fieldsTargettingConditions', EMPTY_STRING);
        const values = _get(condition, 'values', EMPTY_STRING);
        if (_isEmpty(operator) || _isEmpty(targeting) || (!isGlobalForm && _isEmpty(values))) {
          haveFailedCondition = true;
          return true;
        }
        return false;
      });

      if (haveFailedCondition) {
        formulasInValid = true;
        return true;
      }

      const isValid = _get(formula, 'isValid', true);
      formulasInValid = !isValid;
      if (formulasInValid) {
        return true;
      }
      return false;
    });

    const isRulesInValid = _some(_get(fieldProperties, 'rules.conditions'), condition => {
      if (
        !_isEmpty(condition.conditionParameter) &&
        _includes(
          [CONFIG_FIELD_TYPES.TRADE_IN, CONFIG_FIELD_TYPES.STOCK_TYPE],
          _get(condition, 'conditionParameter.value')
        )
      ) {
        if (
          _isEmpty(condition.fieldsTargettingConditions) &&
          !_isEmpty(condition.operator) &&
          !_isEmpty(condition.values)
        ) {
          return false;
        }
      }

      if (
        !_isEmpty(condition.conditionParameter) &&
        _get(condition, 'conditionParameter.value') == CONFIG_FIELD_TYPES.OPTION
      ) {
        if (
          _isEmpty(condition.fieldsTargettingConditions) &&
          _isEmpty(condition.values) &&
          !_isEmpty(condition.operator)
        ) {
          return false;
        }
      }

      if (
        _isEmpty(condition.fieldsTargettingConditions) ||
        (!isGlobalForm && _isEmpty(condition.values)) ||
        _isEmpty(condition.operator) ||
        _isEmpty(condition.conditionParameter)
      ) {
        return true;
      }
    });

    _set(errorTabs, CONFIG_KEYS.GENERAL_CONFIG, isGeneralInValid);
    _set(errorTabs, CONFIG_KEYS.FORMULA, formulasInValid);
    _set(errorTabs, CONFIG_KEYS.RULES, isRulesInValid);
  }
  return errorTabs;
};

export const getFieldTabs = ({
  fieldProperties,
  hideTabs,
  supportedGlobalFieldTabs,
  supportedCustomFieldTabs,
  isFcLiteModeEnabled,
}) => {
  const fieldType = _get(fieldProperties, 'fieldType') || FIELD_TYPES.CUSTOM_FIELD;
  let tabs = FIELD_FORM_TAB_SECTIONS[fieldType];
  if (fieldType === FIELD_TYPES.CUSTOM_FIELD) {
    if (!_isEmpty(supportedCustomFieldTabs)) {
      tabs = supportedCustomFieldTabs;
    }

    if (isFcLiteModeEnabled) {
      tabs = _filter(tabs, tab => tab !== CONFIG_KEYS.RULES && tab !== CONFIG_KEYS.FORMULA);
    }

    return _compact(_map(tabs, tab => (hideTabs.includes(tab) ? null : tab)));
  }
  if (!_isEmpty(supportedGlobalFieldTabs)) {
    tabs = supportedGlobalFieldTabs;
  }
  return tabs;
};

export const checkIsCustomField = fieldProperties => {
  const fieldType = _get(fieldProperties, 'fieldType') || FIELD_TYPES.CUSTOM_FIELD;
  return fieldType === FIELD_TYPES.CUSTOM_FIELD;
};

export const checkIsConfigField = fieldProperties => {
  const fieldType = _get(fieldProperties, 'fieldType') || FIELD_TYPES.CONFIGURATION_FIELD;
  return fieldType === FIELD_TYPES.CONFIGURATION_FIELD;
};

export const checkIfGlobalField = fieldProperties => _get(fieldProperties, 'fieldType') === FIELD_TYPES.GLOBAL_FIELD;

export const getFieldDisplayTitle = (selectedField, fieldProperties, isEdit) => {
  if (!isEdit) return __('New Custom Field');
  const order = _get(fieldProperties, 'order') || EMPTY_STRING;
  const fieldLabel = `${order ? `${order}. ` : EMPTY_STRING}${getFieldDisplayName(selectedField)} - `;
  return `${fieldLabel}${__('Field Properties')}`;
};

export const getFieldNameValue = selectedField => {
  if (_get(selectedField, 'fieldType') === FIELD_TYPES.GLOBAL_FIELD) {
    return {
      label: _get(selectedField, 'fieldAnnotation'),
      value: _get(selectedField, 'fieldAnnotation'),
    };
  }
  return _get(selectedField, 'fieldName');
};

export const isSingleMultiSelectCustomField = fieldProperties => {
  const fieldType = _head(_get(fieldProperties, 'general.type'));
  return (
    checkIsCustomField(fieldProperties) &&
    (fieldType === FIELD_TYPE_VALUES.MULTI_SELECT || fieldType === FIELD_TYPE_VALUES.SINGLE_SELECT)
  );
};

export const isSelectMandatory = selectedField => {
  if (isSingleMultiSelectCustomField(selectedField)) {
    if (_get(selectedField, 'general.fieldDefaultState') === FIELD_DEFAULT_STATE_VALUES.MANDATORY_USER_SELECT)
      return { [FORM_FIELDS.IS_SELECT_MANDATORY]: true };
    return { [FORM_FIELDS.IS_SELECT_MANDATORY]: false };
  }
  return {};
};

export const getTextFormattingWithPrinterType = (formPrinterType, selectedField, textFormat) => {
  let textFormatting = null;
  if (selectedField) {
    if (formPrinterType === FORM_PRINTER_TYPES.LASER) {
      if (_includes(LASER_FONT_VALUES, selectedField.textFormat.font)) {
        textFormatting = selectedField.textFormat;
      } else {
        textFormatting = { ...getDefaultFieldTextFormatting(formPrinterType) };
      }
    }
    if (formPrinterType === FORM_PRINTER_TYPES.IMPACT) {
      if (_includes(IMPACT_FONT_VALUES, selectedField.textFormat.font)) {
        textFormatting = selectedField.textFormat;
      } else {
        textFormatting = { ...getDefaultFieldTextFormatting(formPrinterType) };
      }
    }
  } else {
    textFormatting = { ...getDefaultFieldTextFormatting(formPrinterType), ...textFormat };
  }
  return textFormatting;
};

export const showGlobalFieldDefaultValues = (defaultValue, defaultValueType) =>
  defaultValue && defaultValue.length > 0 && defaultValue[0] === defaultValueType;
