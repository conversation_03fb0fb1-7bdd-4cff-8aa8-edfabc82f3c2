import produce from 'immer';

import _set from 'lodash/set';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _forEach from 'lodash/forEach';
import _keys from 'lodash/keys';
import _head from 'lodash/head';
import _round from 'lodash/round';
import _castArray from 'lodash/castArray';
import _unset from 'lodash/unset';
import _map from 'lodash/map';

import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import FORM_ACTION_TYPES from '@tekion/tekion-components/src/organisms/FormBuilder/constants/actionTypes';
import ACTION_TYPES from './FieldPropertiesForm.actionTypes';
import {
  checkIsCustomField,
  getFieldNameValue,
  getTextFormattingWithPrinterType,
  isSelectMandatory,
  isSingleMultiSelectCustomField,
} from './FieldPropertiesForm.utils';
import {
  FIELD_TYPES,
  DEFAULT_FIELD_STATE_CUSTOM_GENERAL,
  DEFAULT_SCALE,
  DEFAULT_FIELD_HEIGHT,
  DEFAULT_FIELD_WIDTH,
  FIELD_TYPE_VALUES,
  FIELD_DEFAULT_VARIATIONS,
  DEFAULT_FIELD_VALUES,
} from '../../FormConfiguratorTool.constants';

import { FORM_FIELDS } from '../FieldPropertiesGeneralForm/FieldPropertiesGeneralForm.constants';
import {
  getAnnotationCoordinatesFromTop,
  getGlobalFieldFromName,
  getOffsetsAndPositions,
  isSignatureAnnotation,
} from '../../FormConfiguratorTool.utils';
import {
  FIELD_DEFAULT_STATE_VALUES,
  FORM_FIELDS as CUSTOM_FORM_FIELDS,
} from '../CustomFieldGeneralForm/CustomFieldGeneralForm.constants';
import FormFormulaManager from '../../FormFormulaManager';

import { ACTION_OPTIONS_KEYS } from '../Rules/Rules.constants';
import { LIMIT_TYPES } from '../TextFormatting/TextFormatting.constants';
import { CONFIG_KEYS } from './FieldPropertiesForm.constants';
import FormConfiguratorToolAPI from '../../FormConfiguratorTool.api';

const handlePageMount = (action, { setState, getState }) => {
  const { selectedField = {}, fieldProperties, formPrinterType, formScale, pageDimensions } = getState();
  const { textFormat = {}, general = {}, rules = {}, ...rest } = selectedField || {};
  const genealDefaults = checkIsCustomField(fieldProperties) ? DEFAULT_FIELD_STATE_CUSTOM_GENERAL : {};
  const dimensions = {
    width: _get(rest, 'actualWidth', DEFAULT_FIELD_WIDTH),
    height: _get(rest, 'actualHeight', DEFAULT_FIELD_HEIGHT),
  };
  const textFormatting = getTextFormattingWithPrinterType(formPrinterType, selectedField, textFormat);

  setState({
    fieldProperties: {
      active: true,
      ...rest,
      textFormat: textFormatting,
      general: {
        ...genealDefaults,
        ...general,
        name: getFieldNameValue(selectedField),
        ...isSelectMandatory(selectedField),
      },
      offsets: getOffsetsAndPositions(selectedField, formScale, pageDimensions),
      dimensions,
      rules: { action: ACTION_OPTIONS_KEYS.NA, ...rules },
    },
  });
};

const handleFieldChange = (action, { getState, setState }) => {
  const { payload } = action;
  const { fieldProperties, globalFields } = getState();
  const { category, id, value } = payload;
  if (category) {
    setState(
      produce(draft => {
        _set(draft, `fieldProperties.${category}.${id}`, value);
        if (id === FORM_FIELDS.FIELD_TYPE && _get(fieldProperties, 'fieldType') === FIELD_TYPES.CUSTOM_FIELD) {
          _set(draft, `fieldProperties.general.fieldUserInputValue`, '');
          _set(draft, `fieldProperties.general.fieldReadOnlyValue`, DEFAULT_FIELD_VALUES[value]);
        }
        if (id === FORM_FIELDS.FIELD_NAME && !isSignatureAnnotation(fieldProperties))
          _set(draft, `fieldProperties.general.timeStampKey`, null);
        if (id === FORM_FIELDS.FIELD_NAME && _get(fieldProperties, 'fieldType') === FIELD_TYPES.GLOBAL_FIELD) {
          const selectedField = getGlobalFieldFromName(globalFields, _get(value, 'value'));
          if (!_isEmpty(selectedField)) {
            _set(draft, `fieldProperties.description`, _get(selectedField, 'description'));
            _set(draft, `fieldProperties.fieldAnnotation`, _get(selectedField, 'fieldAnnotation'));
            _set(draft, `fieldProperties.fieldName`, _get(selectedField, 'fieldName'));
            _set(draft, `fieldProperties.general.description`, _get(selectedField, 'description'));
            _set(
              draft,
              `fieldProperties.general.type`,
              _castArray(_get(selectedField, 'type') || FIELD_TYPE_VALUES.FREE_TEXT)
            );
            _set(
              draft,
              `fieldProperties.general.variation`,
              FIELD_DEFAULT_VARIATIONS[_get(selectedField, 'type') || FIELD_TYPE_VALUES.FREE_TEXT]
            );
          }
        }
        if (id === 'limitOption') {
          const selectedValue = value;
          switch (selectedValue) {
            case LIMIT_TYPES.LIMIT:
              _set(draft, `fieldProperties.textFormat.textCombing`, null);
              break;
            case LIMIT_TYPES.COMB:
              _set(draft, `fieldProperties.textFormat.maxLength`, null);
              break;
            default:
              _set(draft, `fieldProperties.textFormat.textCombing`, null);
          }
        }

        if (id === 'type' && checkIsCustomField(fieldProperties)) {
          const selectedValue = _head(value);

          _set(draft, 'fieldProperties.general.customOptions', []);
          switch (selectedValue) {
            case FIELD_TYPE_VALUES.FREE_TEXT:
              _set(draft, `fieldProperties.general.variation`, FIELD_DEFAULT_VARIATIONS.FREE_TEXT);
              break;
            case FIELD_TYPE_VALUES.PRICE:
              _set(draft, `fieldProperties.general.variation`, FIELD_DEFAULT_VARIATIONS.PRICE);
              break;
            case FIELD_TYPE_VALUES.DATE:
              _set(draft, `fieldProperties.general.variation`, FIELD_DEFAULT_VARIATIONS.DATE);
              break;
            case FIELD_TYPE_VALUES.PHONE:
              _set(draft, `fieldProperties.general.variation`, FIELD_DEFAULT_VARIATIONS.PHONE);
              break;
            case FIELD_TYPE_VALUES.TEXT_AREA:
              _set(draft, `fieldProperties.general.variation`, FIELD_DEFAULT_VARIATIONS.TEXT_AREA);
              break;
            default:
              _set(draft, `fieldProperties.general.variation`, []);
          }
          _set(draft, `fieldProperties.general.fieldDefaultState`, FIELD_DEFAULT_STATE_VALUES.USER_INPUT);
        }

        if (id === CUSTOM_FORM_FIELDS.IS_SELECT_MANDATORY && isSingleMultiSelectCustomField(fieldProperties)) {
          _set(
            draft,
            `fieldProperties.general.fieldDefaultState`,
            value ? FIELD_DEFAULT_STATE_VALUES.MANDATORY_USER_SELECT : FIELD_DEFAULT_STATE_VALUES.USER_INPUT
          );
        }

        if (id === 'preference' && checkIsCustomField(fieldProperties)) {
          _set(draft, `fieldProperties.general.fieldUserInputValue`, '');
          _set(draft, `fieldProperties.general.fieldDefaultState`, FIELD_DEFAULT_STATE_VALUES.READ_ONLY);
        }

        if (category === CONFIG_KEYS.GENERAL_CONFIG && id === 'type' && checkIsCustomField(fieldProperties)) {
          _set(draft, `fieldProperties.formula.formulas`, []);
          _set(draft, `fieldProperties.formula.preference`, null);
        }

        if (id === FORM_FIELDS.SHOW_ON_FORM) {
          handleToggleActiveField(action, { getState, setState });
        }

        if (id === FORM_FIELDS.CUSTOM_FIELD) {
          handleToggleGlobalField(action, { getState, setState });
        }
      })
    );
    return;
  }

  setState({
    hasFormChanged: true,
    fieldProperties: {
      ...fieldProperties,
      [payload.id]: payload.value,
    },
  });
};

const handleFormSubmit = (action, { getState }) => {
  const { fieldProperties, onSubmit, formScale, selectedField, pageDimensions } = getState();
  const scale = formScale / DEFAULT_SCALE;
  onSubmit(
    produce(fieldProperties, draft => {
      if (_get(fieldProperties, 'fieldType') === FIELD_TYPES.CUSTOM_FIELD) {
        _set(draft, 'fieldAnnotation', _get(fieldProperties, 'general.name'));
        _set(draft, `fieldName`, _get(fieldProperties, 'general.name'));
        const formulas = _get(fieldProperties, 'formula.formulas', []);
        const dependents = [];
        _forEach(formulas, formula => {
          const formulaDependents = _keys(_get(formula, 'formulaMappings', {}));
          dependents.push(...formulaDependents);
        });
        FormFormulaManager.updateDependents(`f${selectedField.order}`, dependents);
      }
      if (_get(fieldProperties, 'fieldType') === FIELD_TYPES.GLOBAL_FIELD) {
        _set(draft, `general.name`, _castArray(_get(fieldProperties, 'general.name.value')));
      }

      if (isSingleMultiSelectCustomField(fieldProperties)) {
        if (_get(fieldProperties, `general.${CUSTOM_FORM_FIELDS.IS_SELECT_MANDATORY}`)) {
          _set(draft, 'general.fieldDefaultState', FIELD_DEFAULT_STATE_VALUES.MANDATORY_USER_SELECT);
        } else {
          _set(draft, 'general.fieldDefaultState', FIELD_DEFAULT_STATE_VALUES.USER_INPUT);
        }
        _unset(draft, `general.${CUSTOM_FORM_FIELDS.IS_SELECT_MANDATORY}`);
      }

      _set(draft, 'width', _round(_get(fieldProperties, 'dimensions.width') * scale, 2));
      _set(draft, `height`, _round(_get(fieldProperties, 'dimensions.height') * scale, 2));

      const { x, y } = _get(fieldProperties, 'page')
        ? getAnnotationCoordinatesFromTop(
            _get(fieldProperties, 'offsets.left'),
            _get(fieldProperties, 'offsets.top'),
            _get(fieldProperties, 'page'),
            pageDimensions,
            formScale
          )
        : { x: _get(fieldProperties, 'offsets.left'), y: _get(fieldProperties, 'offsets.top') };

      _set(draft, 'x', x);
      _set(draft, 'y', y);
      _unset(draft, 'offsets.top');
      _unset(draft, 'offsets.left');
      _set(draft, 'actualWidth', _get(fieldProperties, 'dimensions.width'));
      _set(draft, `actualHeight`, _get(fieldProperties, 'dimensions.height'));
      _set(draft, `active`, _get(fieldProperties, 'active', false));
      _unset(draft, 'dimensions');
    })
  );
};

const handleToggleGlobalField = (action, { getState, setState }) => {
  const { fieldProperties, selectedField } = getState();
  const newFieldType = checkIsCustomField(fieldProperties) ? FIELD_TYPES.GLOBAL_FIELD : FIELD_TYPES.CUSTOM_FIELD;

  if (newFieldType === FIELD_TYPES.GLOBAL_FIELD) {
    setState({
      fieldProperties: {
        ...fieldProperties,
        general: {
          ...fieldProperties.general,
          ...selectedField.general,
          name: getFieldNameValue(selectedField),
        },
        fieldType: newFieldType,
      },
    });
  } else {
    setState({
      fieldProperties: {
        ...fieldProperties,
        general: {
          ...fieldProperties.general,
          name: _get(fieldProperties, 'general.name.value'),
          fieldDefaultState: FIELD_DEFAULT_STATE_VALUES.USER_INPUT,
        },
        fieldType: newFieldType,
      },
    });
  }
};

const handleToggleActiveField = (_, { getState, setState }) => {
  const { fieldProperties } = getState();
  setState({
    fieldProperties: {
      ...fieldProperties,
      active: !_get(fieldProperties, 'active', false),
    },
  });
};

const handleOnFormulaSearch = async action => {
  const {
    payload: { query },
  } = action;
  const resp = await FormConfiguratorToolAPI.searchFormula(query);
  const list = _get(resp, 'rawResponse.data', []);
  const data = _map(list, formula => ({
    value: formula.formulaText,
    label: formula.formulaName,
  }));
  return data;
};

const setStateForFormulaTab = ({ getState, setState }, stateChanges) => {
  const { fieldProperties } = getState();
  setState({
    fieldProperties: {
      ...fieldProperties,
      [CONFIG_KEYS.FORMULA]: {
        ...fieldProperties[CONFIG_KEYS.FORMULA],
        ...stateChanges,
      },
    },
  });
};

const getStateForFormulaTab = ({ getState }) => {
  const { fieldProperties } = getState();
  return fieldProperties[CONFIG_KEYS.FORMULA];
};

const handleOnSaveFormulaClick = (_, stateHelpers) => {
  setStateForFormulaTab(stateHelpers, { showSaveFormulaPrompt: true });
};

const handleOnDeleteFormulaClick = (action, stateHelpers) => {
  const {
    payload: { formulaToDelete },
  } = action;
  setStateForFormulaTab(stateHelpers, { showDeleteFormulaPrompt: true, formulaToDelete });
};

const handleOnSelectSavedFormula = (action, stateHelpers) => {
  const {
    payload: { selectedSavedFormula },
  } = action;
  setStateForFormulaTab(stateHelpers, { selectedSavedFormula });
};

const handleOnFormulaMenuClose = (action, stateHelpers) => {
  const {
    payload: { refreshOptions },
  } = action;
  setStateForFormulaTab(stateHelpers, { refreshOptions });
};

const handleOnHideSaveFormulaPrompt = (_, stateHelpers) => {
  setStateForFormulaTab(stateHelpers, { showSaveFormulaPrompt: false });
};

const handleOnHideDeleteFormulaPrompt = (_, stateHelpers) => {
  setStateForFormulaTab(stateHelpers, { showDeleteFormulaPrompt: false, formulaToDelete: null });
};

const handleOnDeleteFormulaPromptSubmit = async (_, stateHelpers) => {
  const { formulaToDelete } = getStateForFormulaTab(stateHelpers);
  await FormConfiguratorToolAPI.deleteFormula(formulaToDelete);
  setStateForFormulaTab(stateHelpers, { showDeleteFormulaPrompt: false, formulaToDelete: null, refreshOptions: true });
  toaster(TOASTER_TYPE.SUCCESS, __('Formula is deleted successfully'));
};

const handleOnSaveFormulaPromptSubmit = async (action, stateHelpers) => {
  const {
    payload: { formulaText },
  } = action;
  const { saveFormulaName } = getStateForFormulaTab(stateHelpers);
  const resp = await FormConfiguratorToolAPI.saveFormula({ formulaName: saveFormulaName, formulaText });
  const haveError = !_isEmpty(_get(resp, 'error', null));
  if (haveError) {
    setStateForFormulaTab(stateHelpers, { saveFormulaError: __('Please enter a unique formula name') });
  } else {
    setStateForFormulaTab(stateHelpers, {
      showSaveFormulaPrompt: false,
      saveFormulaName: '',
      saveFormulaError: null,
      refreshOptions: true,
    });
    toaster(TOASTER_TYPE.SUCCESS, __('Formula is saved successfully'));
  }
};

const handleOnSaveFormulaNameChange = (action, stateHelpers) => {
  const {
    payload: { saveFormulaName },
  } = action;
  setStateForFormulaTab(stateHelpers, { saveFormulaName, saveFormulaError: null });
};

const handleMultiLingualFieldChange = (action, { setState }) => {
  const { payload } = action;
  const { language, id, value } = payload;
  setState(prevState =>
    produce(prevState, draftState => {
      _set(draftState.fieldProperties.general, ['languages', 'locale', language, id], value);
    })
  );
};

const ACTION_HANDLERS = {
  [FORM_ACTION_TYPES.ON_FIELD_CHANGE]: handleFieldChange,
  [ACTION_TYPES.ON_FIELD_FORM_MOUNT]: handlePageMount,
  [ACTION_TYPES.ON_FIELD_FORM_SUBMIT]: handleFormSubmit,
  [ACTION_TYPES.ON_TOGGLE_GLOBAL_FIELD]: handleToggleGlobalField,
  [ACTION_TYPES.ON_TOGGLE_ACTIVE_FIELD]: handleToggleActiveField,

  [ACTION_TYPES.ON_FORMULA_SEARCH]: handleOnFormulaSearch,

  [ACTION_TYPES.ON_SAVE_FORMULA_CLICK]: handleOnSaveFormulaClick,
  [ACTION_TYPES.ON_DELETE_FORMULA_CLICK]: handleOnDeleteFormulaClick,
  [ACTION_TYPES.ON_SELECT_SAVED_FORMULA]: handleOnSelectSavedFormula,
  [ACTION_TYPES.ON_FORMULA_MENU_CLOSE]: handleOnFormulaMenuClose,
  [ACTION_TYPES.ON_HIDE_SAVE_FORMULA_PROMPT]: handleOnHideSaveFormulaPrompt,
  [ACTION_TYPES.ON_HIDE_DELETE_FORMULA_PROMPT]: handleOnHideDeleteFormulaPrompt,
  [ACTION_TYPES.ON_SAVE_FORMULA_PROMPT_SUBMIT]: handleOnSaveFormulaPromptSubmit,
  [ACTION_TYPES.ON_DELETE_FORMULA_PROMPT_SUBMIT]: handleOnDeleteFormulaPromptSubmit,

  [ACTION_TYPES.ON_SAVE_FORMULA_NAME_CHANGE]: handleOnSaveFormulaNameChange,
  [ACTION_TYPES.ON_LANGUAGE_CHANGE]: handleMultiLingualFieldChange,
};

export default ACTION_HANDLERS;
