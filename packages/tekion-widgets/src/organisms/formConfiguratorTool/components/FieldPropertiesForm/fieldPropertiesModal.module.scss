@import "tstyles/component.scss";

.headerWrapper {
  @include flex($align-items: center, $justify-content: space-between);
}

.headerActionWrapper {
  display: flex;
}

.switchLabel {
  color: $black;
  margin-right: 1.6rem;
}

.disabledTab {
  opacity: 0.5;
  cursor: not-allowed;
  & > div {
    pointer-events: none;
  }
}

.tooltipMainStyle > div > div:first-child {
  border-top-color: $white;
}

.tooltipMainStyle > div > div:last-child {
  background-color: $white;
  padding: 1.5rem;
}

.tooltipContent {
  background-color: $white;
  color: $black;
}

.tooltipContent > p {
  line-height: 1.7rem;
  margin-bottom: 0;
  margin-top: 0.2rem;
}

.tooltipContentHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tooltipTitle {
  font-weight: bold;
}

.tooltipCross {
  cursor: pointer;
}

.tooltipActions {
  display: flex;
  margin-top: 0.5rem;
  justify-content: flex-end;
}

.scrollableContainer {
  scroll-behavior: smooth;
  width: 100%;
  padding: 2.4rem 0 0 2.4rem;
  border-left: $border;
}
