import React from 'react';
import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import _values from 'lodash/values';
import _map from 'lodash/map';
import _filter from 'lodash/filter';
import _orderBy from 'lodash/orderBy';
import _get from 'lodash/get';
import _reduce from 'lodash/reduce';

import Popover from '@tekion/tekion-components/src/molecules/popover';
import Content from '@tekion/tekion-components/src/atoms/Content';
import FontIcon from '@tekion/tekion-components/src/atoms/FontIcon';
import GeneralConfig from '../FieldPropertiesGeneralForm';
import CustomFieldGeneralForm from '../CustomFieldGeneralForm';
import TextFormatting from '../TextFormatting';
import Offsets from '../Offsets/Offsets';
import Rules from '../Rules/Rules';
import Formula from '../Formula/Formula';
import Dimensions from '../Dimensions';

import { FIELD_FORM_TABS, FIELD_FORM_TAB_NAMES } from './FieldPropertiesForm.constants';
import { ANNOTATION_MODAL_TYPES, FIELD_TYPES } from '../../FormConfiguratorTool.constants';
import { checkIsCustomField } from './FieldPropertiesForm.utils';
import { FIELD_DEFAULT_STATE_VALUES, FORM_FIELDS } from '../CustomFieldGeneralForm/CustomFieldGeneralForm.constants';
import ConfigReader from '../../readers/config.reader';
import { isTimestampAnnotation } from '../../FormConfiguratorTool.utils';

export const getFieldTabKeyRenderer = fieldProperties => ({
  [FIELD_FORM_TABS.GENERAL_CONFIG]: checkIsCustomField(fieldProperties) ? CustomFieldGeneralForm : GeneralConfig,
  [FIELD_FORM_TABS.TEXT_FORMATTING]: TextFormatting,
  [FIELD_FORM_TABS.OFFSETS]: Offsets,
  [FIELD_FORM_TABS.DIMENSIONS]: Dimensions,
  [FIELD_FORM_TABS.RULES]: Rules,
  [FIELD_FORM_TABS.FORMULA]: Formula,
});

const pickGeneralTabProps = props => {
  const {
    fieldProperties,
    metaData,
    fields,
    globalFields,
    productsList,
    selectedField,
    annotatedFields,
    allGlobalFields,
    config,
    isGlobalForm,
    editType,
    onAction,
  } = props;

  const applicableFields = _filter(
    _values(annotatedFields),
    field => field.fieldType !== FIELD_TYPES.CONFIGURATION_FIELD
  );

  const annotatedCustomFields = _filter(
    _values(annotatedFields),
    field => field.fieldType !== FIELD_TYPES.CONFIGURATION_FIELD && field.fieldType !== FIELD_TYPES.GLOBAL_FIELD
  );

  const allFields = _map(_orderBy(applicableFields, ['fieldType', 'fieldName']), field => ({
    label: _get(field, 'fieldAnnotation'),
    value: _get(field, 'fieldAnnotation'),
    conditionParameterType: _get(field, 'fieldType'),
    fieldType: _get(field, 'type'),
  }));

  const allCustomFields = _map(_orderBy(annotatedCustomFields, ['fieldType', 'fieldName']), field => ({
    label: _get(field, 'fieldName'),
    value: _get(field, 'fieldName'),
  }));

  const isGlobalFieldByDefault = allowChangeToCustomField(selectedField);
  const hideActiveFieldToggle = _get(ConfigReader.customRenderOptions(config), ['hideActiveFieldToggle'], false);

  return {
    metaData,
    annotatedFieldsAsOptions: allFields,
    annotatedCustomFields: allCustomFields,
    allGlobalFields,
    fields,
    selectedField,
    globalFields,
    productsList,
    annotatedFields,
    fieldProperties,
    supportedFieldTypes: ConfigReader.supportedFieldTypes(config),
    customFieldGeneralFormColumns: ConfigReader.customFieldGeneralFormColumns(config),
    customFieldRenderOptions: _get(ConfigReader.customRenderOptions(config), 'fieldOptions', EMPTY_OBJECT),
    isGlobalForm,
    editType,
    isGlobalFieldByDefault,
    hideActiveFieldToggle,
    onAction,
  };
};

const pickTextFormattingTabProps = props => {
  const { configuratorMetaData, formPrinterType } = props;
  return {
    modalType: ANNOTATION_MODAL_TYPES.GLOBAL_FIELD,
    configuratorMetaData,
    formPrinterType,
  };
};

const pickOffsetTabProps = ({ pageDimensions, fieldProperties, formScale } = EMPTY_OBJECT) => {
  const currentPage = _get(fieldProperties, 'page');
  return {
    pageDimensions,
    currentPage,
    formScale,
  };
};
const pickDimensionsTabProps = ({ pageDimensions, fieldProperties, formScale } = EMPTY_OBJECT) => {
  const currentPage = _get(fieldProperties, 'page');
  return {
    pageDimensions,
    currentPage,
    formScale,
  };
};

const pickFormulaTabProps = props => {
  const {
    onAction,
    metaData,
    fieldProperties,
    fields,
    configFields,
    annotatedFields,
    productsList,
    selectedField,
    allGlobalFields,
    reducerKey,
    isGlobalForm,
    siteOptions,
    stateListOptions,
    paymentOptions,
    editType,
  } = props;
  return {
    metaData,
    fields,
    annotatedFields,
    productsList,
    selectedField,
    allGlobalFields,
    reducerKey,
    fieldProperties,
    onAction,
    configFields,
    isGlobalForm,
    siteOptions,
    stateListOptions,
    paymentOptions,
    editType,
  };
};

export const getAdditionalProps = {
  [FIELD_FORM_TABS.GENERAL_CONFIG]: pickGeneralTabProps,
  [FIELD_FORM_TABS.TEXT_FORMATTING]: pickTextFormattingTabProps,
  [FIELD_FORM_TABS.OFFSETS]: pickOffsetTabProps,
  [FIELD_FORM_TABS.DIMENSIONS]: pickDimensionsTabProps,
  [FIELD_FORM_TABS.RULES]: pickGeneralTabProps,
  [FIELD_FORM_TABS.FORMULA]: pickFormulaTabProps,
};

export const getTabPaneProps = values => ({
  [FIELD_FORM_TABS.GENERAL_CONFIG]: {},
  [FIELD_FORM_TABS.TEXT_FORMATTING]: {},
  [FIELD_FORM_TABS.OFFSETS]: {},
  [FIELD_FORM_TABS.DIMENSIONS]: {},
  [FIELD_FORM_TABS.RULES]: {},
  [FIELD_FORM_TABS.FORMULA]: {
    disabled:
      _get(values, `general.${FORM_FIELDS.FIELD_DEFAULT_STATE}`) === FIELD_DEFAULT_STATE_VALUES.MANDATORY_USER_SELECT,
  },
});

const addAlertIcon = (value, showAlertIcon) => {
  if (!showAlertIcon) return value;
  return (
    <div className="d-flex align-items-center">
      {value}{' '}
      <FontIcon className="m-l-8" size="MD" color="red">
        icon-alert
      </FontIcon>
    </div>
  );
};

const handleAlertIcon = (fieldTabs, errorTabs) =>
  _reduce(
    fieldTabs,
    (result, value, key) => ({
      ...result,
      [key]: addAlertIcon(value, _get(errorTabs, key)),
    }),
    {}
  );

export const getTabHeadings = (values, errorTabs) => {
  const fieldTabs = FIELD_FORM_TAB_NAMES;
  if (_get(values, `general.${FORM_FIELDS.FIELD_DEFAULT_STATE}`) === FIELD_DEFAULT_STATE_VALUES.MANDATORY_USER_SELECT) {
    return {
      ...handleAlertIcon(fieldTabs, errorTabs),
      [FIELD_FORM_TABS.FORMULA]: (
        <Popover
          placement="top"
          trigger="hover"
          mouseEnterDelay={0.5}
          content={
            <div className="d-flex flex-column align-items-center px-4 py-2">
              <Content size={5}>{__('Unavailable for Mandatory User Input Value')}</Content>
            </div>
          }>
          {addAlertIcon(fieldTabs[FIELD_FORM_TABS.FORMULA], _get(errorTabs, fieldTabs.FORMULA))}
        </Popover>
      ),
    };
  }
  return handleAlertIcon(fieldTabs, errorTabs);
};

export const allowChangeToCustomField = annotation =>
  !checkIsCustomField(annotation) && !isTimestampAnnotation(annotation);
