import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';

import _noop from 'lodash/noop';
import _get from 'lodash/get';
import _values from 'lodash/values';
import _filter from 'lodash/filter';
import _size from 'lodash/size';
import _includes from 'lodash/includes';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';

import Modal from '@tekion/tekion-components/src/molecules/Modal';
import Button from '@tekion/tekion-components/src/atoms/Button';
import FontIcon, { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';
import { toaster } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import withCustomActionHandlers from '../../withCustomActionHandlers';
import ACTION_TYPES from './FieldPropertiesForm.actionTypes';
import FieldPropertiesForm from './FieldPropertiesForm';
import FORM_ACTION_HANDLERS from './FieldPropertiesForm.actionHandlers';
import { shouldActionDisabled, checkIsCustomField, getFieldDisplayTitle } from './FieldPropertiesForm.utils';
import styles from './fieldPropertiesModal.module.scss';
import { FIELD_TYPES } from '../../FormConfiguratorTool.constants';
import ConfigReader from '../../readers/config.reader';

const BODY_STYLE = {
  height: '60rem',
  paddingTop: '0rem',
  paddingBottom: '0rem',
};

class FieldPropertiesFormModal extends PureComponent {
  state = {
    showTooltip: false,
    errorTabs: {},
  };

  handleNewFormSubmit = () => {
    const { onAction, fieldProperties, errors, formScale, pageDimensions, isGlobalForm } = this.props;
    const { hasViewOnlyPermission } = this.props;

    const errorTabs = shouldActionDisabled({ fieldProperties, errors, formScale, pageDimensions, isGlobalForm });
    this.setState({
      errorTabs,
    });

    if (_includes(_values(errorTabs), true)) {
      toaster('error', __('Please enter mandatory fields to proceed'));
      return;
    }

    if (!hasViewOnlyPermission && this.haveSimilarCustomFields()) {
      this.showTooltip();
    } else {
      onAction({
        type: ACTION_TYPES.ON_FIELD_FORM_SUBMIT,
      });
    }
  };

  confirmUpdateFields = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_FIELD_FORM_SUBMIT,
    });
    this.hideTooltip();
  };

  haveSimilarCustomFields = () => {
    const { fieldProperties, annotatedFields, selectedField } = this.props;
    const selectedFieldId = _get(selectedField, 'id', EMPTY_STRING);
    const isCustomField = checkIsCustomField(fieldProperties);
    if (isCustomField) {
      const name = _get(fieldProperties, 'general.name', null);
      return (
        _size(
          _filter(
            _values(annotatedFields),
            field =>
              field.fieldType === FIELD_TYPES.CUSTOM_FIELD && field.fieldName === name && field.id !== selectedFieldId
          )
        ) > 0
      );
    }
    return false;
  };

  handleToggleGlobalField = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_TOGGLE_GLOBAL_FIELD,
    });
  };

  handleToggleActiveField = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_TOGGLE_ACTIVE_FIELD,
    });
  };

  renderHeader = () => {
    const { fieldProperties, selectedField } = this.props;
    const { isEdit } = this.props;
    const title = getFieldDisplayTitle(selectedField, fieldProperties, isEdit);
    return <div className={styles.headerWrapper}>{title}</div>;
  };

  showTooltip = () => this.setState({ showTooltip: true });

  hideTooltip = () => this.setState({ showTooltip: false });

  renderTooltip = () => {
    const { config } = this.props;
    const customToolTipConfig = _get(ConfigReader.customRenderOptions(config), 'updateCTAToolTipConfig', EMPTY_OBJECT);
    const customToolTipMessage = _get(customToolTipConfig, 'toolTipMessage');
    const customToolTipTitle = _get(customToolTipConfig, 'title');
    const contentClassName = _get(customToolTipConfig, 'contentClassName');

    return (
      <div className={styles.tooltipContent}>
        <div className={styles.tooltipContentHeader}>
          <div className={styles.tooltipTitle}>{customToolTipTitle || __('Update Fields')}</div>
          <FontIcon className={styles.tooltipCross} size={SIZES.S} onClick={this.hideTooltip}>
            icon-cross
          </FontIcon>
        </div>
        <p className={contentClassName}>
          {customToolTipMessage || __('All duplicate fields will also be modified. Are you sure you want to update?')}
        </p>
        <div className={styles.tooltipActions}>
          <Button view="tertiary" onClick={this.hideTooltip}>
            {__('Cancel')}
          </Button>
          <Button view="tertiary" onClick={this.confirmUpdateFields}>
            {__('Confirm')}
          </Button>
        </div>
      </div>
    );
  };

  closeModal = () => {
    const { toggleModal } = this.props;
    this.hideTooltip();
    toggleModal();
    this.setState({
      errorTabs: EMPTY_ARRAY,
    });
  };

  render() {
    const { fieldProperties, errors, formScale, pageDimensions, config, isGlobalForm } = this.props;
    const { showModal, isEdit, isFormSubmitInProgress, hasViewOnlyPermission } = this.props;
    const { showTooltip, errorTabs } = this.state;
    const tooltipContainerClass = _get(ConfigReader.customRenderOptions(config), [
      'updateCTAToolTipConfig',
      'containerClassName',
    ]);

    return (
      <Modal
        onCancel={this.closeModal}
        onSubmit={this.handleNewFormSubmit}
        secondaryBtnText={hasViewOnlyPermission ? __('Close') : __('Cancel')}
        submitBtnText={isEdit ? __('Update') : __('Save')}
        title={this.renderHeader()}
        visible={showModal}
        width={Modal.SIZES.L}
        bodyStyle={BODY_STYLE}
        hideSubmit={hasViewOnlyPermission}
        destroyOnClose
        okButtonProps={{
          loading: isFormSubmitInProgress,
        }}
        primaryBtnTooltipProps={{
          visible: hasViewOnlyPermission ? false : showTooltip,
          overlayClassName: cx(styles.tooltipMainStyle, tooltipContainerClass),
          overlay: hasViewOnlyPermission ? null : this.renderTooltip,
        }}>
        <FieldPropertiesForm {...this.props} errorTabs={errorTabs} />
      </Modal>
    );
  }
}

FieldPropertiesFormModal.propTypes = {
  toggleModal: PropTypes.func.isRequired,
  isFormSubmitInProgress: PropTypes.bool,
  showModal: PropTypes.bool,
  hasViewOnlyPermission: PropTypes.bool,
  formValues: PropTypes.object,
  fieldProperties: PropTypes.object,
  annotatedFields: PropTypes.object,
  selectedField: PropTypes.object,
  errors: PropTypes.object,
  onAction: PropTypes.func,
  isEdit: PropTypes.bool,
  pageDimensions: PropTypes.object.isRequired,
  formScale: PropTypes.number.isRequired,
  config: PropTypes.object,
  isGlobalForm: PropTypes.bool,
};

FieldPropertiesFormModal.defaultProps = {
  showModal: false,
  formValues: EMPTY_OBJECT,
  annotatedFields: EMPTY_OBJECT,
  hasViewOnlyPermission: true,
  fieldProperties: EMPTY_OBJECT,
  selectedField: EMPTY_OBJECT,
  errors: EMPTY_OBJECT,
  isFormSubmitInProgress: false,
  onAction: _noop,
  isEdit: false,
  config: EMPTY_OBJECT,
  isGlobalForm: false,
};

export default withCustomActionHandlers(FORM_ACTION_HANDLERS)(FieldPropertiesFormModal);
