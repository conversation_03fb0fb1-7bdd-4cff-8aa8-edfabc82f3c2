import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';

import _get from 'lodash/get';
import _noop from 'lodash/noop';

import Heading from '@tekion/tekion-components/src/atoms/Heading';
import Button from '@tekion/tekion-components/src/atoms/Button';
import Select from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/select';
import Content from '@tekion/tekion-components/src/atoms/Content';
import actionTypes from '@tekion/tekion-components/src/organisms/FormBuilder/constants/actionTypes';
import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';

import Conditions from './Conditions';
import * as ConditionsUtils from './Conditions/Conditions.utils';
import { disableFieldBasedOnPermission } from '../../FormConfiguratorTool.utils';

import { RULES_DESCRIPTION, RULES_CONFIG_FIELDS, ACTION_OPTIONS } from './Rules.constants';
import styles from './rules.module.scss';

function Rules(props) {
  const {
    updateFormValues,
    values,
    metaData,
    fields,
    productsList,
    annotatedFields,
    globalFields,
    annotatedFieldsAsOptions,
    isGlobalForm,
    isFormsLibrary,
    editType,
  } = props;

  const onRulesConfigChange = useCallback(
    ({ payload: { id, value }, type }) => {
      if (type === actionTypes.ON_FIELD_CHANGE) {
        updateFormValues({ id, value });
      }
    },
    [updateFormValues]
  );

  const onRuleConditionsChange = useCallback(
    value => {
      updateFormValues({ id: RULES_CONFIG_FIELDS.CONDTIONS, value });
    },
    [updateFormValues]
  );

  const onAddCondition = useCallback(() => {
    const updatedRules = ConditionsUtils.addNewRuleCondition(_get(values, RULES_CONFIG_FIELDS.CONDTIONS, []));
    updateFormValues({ id: RULES_CONFIG_FIELDS.CONDTIONS, value: updatedRules });
  }, [updateFormValues, values]);

  const renderDefaultValue = () => (
    <div className="m-b-24">
      <Heading className="m-t-16 m-b-4" size={5}>
        {__('Action')}
      </Heading>
      <Select
        placeholder={__('Actions')}
        onAction={onRulesConfigChange}
        options={ACTION_OPTIONS}
        value={_get(values, RULES_CONFIG_FIELDS.ACTION)}
        id={RULES_CONFIG_FIELDS.ACTION}
        className={styles.rowFields}
      />
    </div>
  );

  return (
    <div
      className={cx('full-width', {
        [styles.rulesViewOnly]: disableFieldBasedOnPermission({
          isFormsLibrary,
          editType,
        }),
      })}>
      <Heading className="m-b-4" size={3}>
        {__('Rules')}
      </Heading>
      <Content size={5} className="m-b-32">
        {RULES_DESCRIPTION}
      </Content>
      <Conditions
        fieldTargets={_get(values, RULES_CONFIG_FIELDS.CONDTIONS, [])}
        updateFieldsTargettingValues={onRuleConditionsChange}
        metaData={metaData}
        productsList={productsList}
        fields={fields}
        annotatedFields={annotatedFields}
        annotatedFieldsAsOptions={annotatedFieldsAsOptions}
        globalFields={globalFields}
        isGlobalForm={isGlobalForm}
      />
      <Button view={Button.VIEW.TERTIARY} className="marginT16" onClick={onAddCondition}>
        {__('+ Add Condition')}
      </Button>
      {renderDefaultValue()}
    </div>
  );
}

Rules.propTypes = {
  values: PropTypes.object,
  metaData: PropTypes.object,
  fields: PropTypes.array,
  productsList: PropTypes.array,
  updateFormValues: PropTypes.func,
  annotatedFields: PropTypes.object,
  globalFields: PropTypes.array,
  annotatedFieldsAsOptions: PropTypes.array,
  isGlobalForm: PropTypes.bool,
};

Rules.defaultProps = {
  values: EMPTY_OBJECT,
  metaData: EMPTY_OBJECT,
  fields: EMPTY_ARRAY,
  updateFormValues: _noop,
  productsList: EMPTY_ARRAY,
  annotatedFields: EMPTY_OBJECT,
  globalFields: EMPTY_ARRAY,
  annotatedFieldsAsOptions: EMPTY_ARRAY,
  isGlobalForm: false,
};

export default Rules;
