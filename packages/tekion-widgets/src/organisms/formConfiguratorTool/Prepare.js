import React, { Component } from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';
import _noop from 'lodash/noop';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';

import { EMPTY_OBJECT, EMPTY_ARRAY, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { PRINTER_TYPES } from '@tekion/tekion-base/constants/formConfigurator/printers';
import { getPaymentTypeOptions } from '@tekion/tekion-business/src/helpers/sales/paymentType.helper';
import Spinner from '@tekion/tekion-components/src/molecules/SpinnerComponent/Spinner';
import { PropertyControlledComponent } from '@tekion/tekion-components/src/molecules';

import UpdateModalMappingFields from './components/updateModal/UpdateModalMappingFields';
import PDFAnnotator from './components/PDFAnnotator';
import PlainPDFViewModal from './components/plainPDFViewModal/PlainPDFViewModal';
import ACTION_TYPES from './FormConfiguratorTool.actionTypes';
import FieldsSetupPane from './components/FieldsSetupPane';
import FieldList from './components/FieldList/FieldList';
import ConfigurationFormModal from './components/ConfigurationForm';
import FieldPropertiesFormModal from './components/FieldPropertiesForm';
import styles from './formConfiguratorTool.module.scss';
import MappingListModal from './components/MappingListModal/MappingListModal';
import CommonPropertiesFormModal from './components/CommonPropertiesForm';
import ReorderFieldsModal from './components/reorderFieldsModal';
import ConfigReader from './readers/config.reader';
import { disableFieldBasedOnPermission } from './FormConfiguratorTool.utils';

class Prepare extends Component {
  componentDidMount() {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_PREPARE_SCREEN_MOUNT,
    });

    window.addEventListener('beforeunload', this.handleOnRefresh);
  }

  componentWillUnmount() {
    const { removeMedia, mediaList } = this.props;
    removeMedia(mediaList);
    window.removeEventListener('beforeunload', this.handleOnRefresh);
  }

  /* eslint-disable no-param-reassign */
  handleOnRefresh = event => {
    event.preventDefault();
    event.returnValue = '';
    return '';
  };
  /* eslint-enable no-param-reassign */

  handleGobalFieldSearch = searchText => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.GLOBAL_FIELD_SEARCH,
      payload: {
        searchText,
      },
    });
  };

  handleAddConfigurationClick = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_ADD_CONFIGURATION_CLICK,
    });
  };

  hideModal = () => this.updateModal.hide();

  handleExpandFieldsClick = () => {
    this.updateModal.show({
      component: MappingListModal,
    });
  };

  handleAddCustomFieldClick = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_ADD_CUSTOM_FIELD_CLICK,
    });
  };

  handleCloseConfigurationClick = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_CLOSE_CONFIGURATION_MODAL,
    });
  };

  handleFieldClick = selectedField => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_FIELD_PROPERTIES_CLICK,
      payload: { selectedField },
    });
  };

  handleCloseFieldFormModalClick = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_CLOSE_FIELD_MODAL,
    });
  };

  hideCommonPropertiesModal = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.HIDE_COMMON_PROPERTIES_MODAL,
    });
  };

  handleSelectedFieldSearch = searchText => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_ANNOTATED_FIELD_SEARCH,
      payload: {
        searchText,
      },
    });
  };

  renderCenterContainer = () => <PDFAnnotator {...this.props} onFieldClick={this.handleFieldClick} />;

  toggleConfigurationModal = (field = '') => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_TOGGLE_CONFIGURATION_FORM_MODAL,
      payload: { field },
    });
  };

  handleConfigurationFormSubmit = configurationValue => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_CONFIGURATION_FIELD_FORM_SUBMIT,
      payload: { configurationValue },
    });
  };

  handleFieldFormSubmit = fieldPropertiesValue => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_FIELD_FORM_SUBMIT,
      payload: { fieldPropertiesValue },
    });
  };

  handleCommonFieldFormSubmit = fieldPropertiesValue => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.SUBMIT_COMMON_PROPERTIES,
      payload: { fieldPropertiesValue },
    });
  };

  hidePlainPDFViewModal = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.HIDE_PLAIN_PDF_VIEW_MODAL,
    });
  };

  hidePlainPDFViewModal = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.HIDE_PLAIN_PDF_VIEW_MODAL,
    });
  };

  showReorderFieldsModal = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.SHOW_REORDER_FIELDS_MODAL,
    });
  };

  hideReorderFieldsModal = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.HIDE_REORDER_FIELDS_MODAL,
    });
  };

  handleReorderFormSubmit = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_REORDER_FORM_SUBMIT,
    });
  };

  renderModals = () => {
    const {
      isConfigurationFormModalVisible,
      isFieldFormModalVisible,
      metaData,
      productsList,
      fields,
      formInfo,
      allGlobalFields,
      annotatedFields,
      configuratorMetaData,
      selectedField,
      isFieldEdit,
      isConfigEdit,
      globalFields,
      selectedConfig,
      commonPropertiesForConfigs,
      formPrinterType,
      formScale,
      isCommonPropertiesModalVisible,
      selectedFields,
      pageDimensions,
      hasViewOnlyPermission,
      tabConfigurations,
      hideTabs,
      fieldPropertiesFormActionHandlers,
      reducerKey,
      configFields,
      config,
      isFormsLibrary,
      isGlobalForm,
      siteOptions,
      stateListOptions,
      paymentOptionConfigs,
      editType,
      isFcLiteModeEnabled,
      disableEditForFCLiteMode,
    } = this.props;

    const commonProperties = _get(commonPropertiesForConfigs, selectedConfig.configId, EMPTY_OBJECT);

    return (
      <>
        <ConfigurationFormModal
          showModal={isConfigurationFormModalVisible}
          toggleModal={this.handleCloseConfigurationClick}
          onSubmit={this.handleConfigurationFormSubmit}
          metaData={metaData}
          productsList={productsList}
          fields={fields}
          annotatedFields={annotatedFields}
          configuratorMetaData={configuratorMetaData}
          isEdit={isConfigEdit}
          selectedConfig={selectedConfig}
          commonProperties={commonProperties}
          formPrinterType={formPrinterType}
          formScale={formScale}
          pageDimensions={pageDimensions}
          hasViewOnlyPermission={hasViewOnlyPermission}
          isGlobalForm={isGlobalForm}
          isFcLiteModeEnabled={isFcLiteModeEnabled}
          disableEditForFCLiteMode={disableEditForFCLiteMode}
        />
        <FieldPropertiesFormModal
          allGlobalFields={allGlobalFields}
          globalFields={globalFields}
          showModal={isFieldFormModalVisible}
          toggleModal={this.handleCloseFieldFormModalClick}
          onSubmit={this.handleFieldFormSubmit}
          metaData={metaData}
          productsList={productsList}
          fields={fields}
          configFields={configFields}
          annotatedFields={annotatedFields}
          configuratorMetaData={configuratorMetaData}
          selectedField={selectedField}
          isEdit={isFieldEdit}
          formPrinterType={formPrinterType}
          formScale={formScale}
          pageDimensions={pageDimensions}
          hasViewOnlyPermission={hasViewOnlyPermission}
          customActionHandlers={fieldPropertiesFormActionHandlers}
          tabConfigurations={tabConfigurations}
          hideTabs={hideTabs}
          reducerKey={reducerKey}
          config={config}
          isFormsLibrary={isFormsLibrary}
          isGlobalForm={isGlobalForm}
          siteOptions={siteOptions}
          stateListOptions={stateListOptions}
          paymentOptions={getPaymentTypeOptions(paymentOptionConfigs)}
          editType={editType}
          isFcLiteModeEnabled={isFcLiteModeEnabled}
          disableEditForFCLiteMode={disableEditForFCLiteMode}
        />
        <CommonPropertiesFormModal
          showModal={isCommonPropertiesModalVisible}
          toggleModal={this.hideCommonPropertiesModal}
          onSubmit={this.handleCommonFieldFormSubmit}
          annotatedFields={annotatedFields}
          selectedFields={selectedFields}
          formScale={formScale}
          formPrinterType={formPrinterType}
          configuratorMetaData={configuratorMetaData}
          hasViewOnlyPermission={hasViewOnlyPermission}
          config={config}
          isFcLiteModeEnabled={isFcLiteModeEnabled}
          disableEditForFCLiteMode={disableEditForFCLiteMode}
        />
      </>
    );
  };

  renderLeftPane = () => {
    const {
      isConfigurationFormModalVisible,
      isFieldFormModalVisible,
      metaData,
      productsList,
      fields,
      formInfo,
      allGlobalFields,
      annotatedFields,
      configuratorMetaData,
      selectedField,
      isFieldEdit,
      isConfigEdit,
      globalFields,
      selectedConfig,
      commonPropertiesForConfigs,
      formPrinterType,
      formScale,
      isCommonPropertiesModalVisible,
      selectedFields,
      pageDimensions,
      hasViewOnlyPermission,
      tabConfigurations,
      hideTabs,
      fieldPropertiesFormActionHandlers,
      reducerKey,
      configFields,
      config,
      isFormsLibrary,
      isGlobalForm,
      siteOptions,
      stateListOptions,
      paymentOptionConfigs,
      editType,
    } = this.props;

    const havePDF = formInfo && !_isEmpty(formInfo.url);
    const commonProperties = _get(commonPropertiesForConfigs, selectedConfig.configId, EMPTY_OBJECT);

    return (
      <div
        className={cx(styles.leftPaneWrapper, {
          [styles.leftPaneWrapperViewOnly]: disableFieldBasedOnPermission({
            disableActions: hasViewOnlyPermission,
            isFormsLibrary,
            editType,
          }),
        })}>
        <FieldsSetupPane
          {...this.props}
          onSearch={this.handleGobalFieldSearch}
          onAddConfiguration={this.handleAddConfigurationClick}
          onAddCustomField={this.handleAddCustomFieldClick}
          disableActions={!havePDF}
        />
        {this.renderModals()}
      </div>
    );
  };

  getUpdateModalRef = ref => {
    this.updateModal = ref;
  };

  renderRightPane = () => {
    const {
      annotatedFields,
      onAction,
      commonPropertiesForConfigs,
      hasViewOnlyPermission,
      showReorderFieldsModal: reorderFieldsModalVisible,
      isDragging,
      fieldToBeSorted,
      sourceSection,
      targetSection,
      userInputFieldsList,
      isFormsLibrary,
    } = this.props;
    return (
      <div className={styles.rightPaneWrapper}>
        <FieldList
          {...this.props}
          onSearch={this.handleSelectedFieldSearch}
          showFieldTypeICons={false}
          onExpandFields={this.handleExpandFieldsClick}
          showReorderFieldsModal={this.showReorderFieldsModal}
        />
        <UpdateModalMappingFields
          ref={this.getUpdateModalRef}
          childComponentProps={{
            annotatedFields,
            hasViewOnlyPermission,
            onAction,
            onCloseModal: this.hideModal,
            commonPropertiesForConfigs,
          }}
        />
        <ReorderFieldsModal
          visible={reorderFieldsModalVisible}
          closeModal={this.hideReorderFieldsModal}
          onAction={onAction}
          isDragging={isDragging}
          fieldToBeSorted={fieldToBeSorted}
          sourceSection={sourceSection}
          targetSection={targetSection}
          userInputFieldsList={userInputFieldsList}
          onSubmit={this.handleReorderFormSubmit}
          isFormsLibrary={isFormsLibrary}
        />
      </div>
    );
  };

  render() {
    const {
      isLoadingPrepare,
      showPlainPDFViewModal,
      onAction,
      plainPDFPageDimensions,
      plainPDFTotalPages,
      formInfo,
      config,
      isFcLiteModeEnabled,
    } = this.props;
    const hideAnnotatedFieldsRightPane = ConfigReader.hideAnnotatedFieldsRightPane(config);

    if (isLoadingPrepare) {
      return (
        <div className="d-flex justify-content-center align-items-center full-height full-width">
          <Spinner />
        </div>
      );
    }

    return (
      <div className={styles.bodyWrapper}>
        {!isFcLiteModeEnabled && this.renderLeftPane()}
        {isFcLiteModeEnabled && this.renderModals()}
        {this.renderCenterContainer()}
        <PropertyControlledComponent controllerProperty={!hideAnnotatedFieldsRightPane}>
          {this.renderRightPane()}
        </PropertyControlledComponent>
        <PlainPDFViewModal
          showPlainPDFViewModal={showPlainPDFViewModal}
          onClose={this.hidePlainPDFViewModal}
          onAction={onAction}
          pageDimensions={plainPDFPageDimensions}
          totalPages={plainPDFTotalPages}
          formInfo={formInfo}
        />
      </div>
    );
  }
}

Prepare.propTypes = {
  isLoadingPrepare: PropTypes.bool,
  onAction: PropTypes.func,
  isConfigurationFormModalVisible: PropTypes.func,
  isFieldFormModalVisible: PropTypes.func,
  metaData: PropTypes.object,
  fields: PropTypes.object,
  annotatedFields: PropTypes.object,
  configuratorMetaData: PropTypes.object,
  selectedField: PropTypes.object,
  selectedConfig: PropTypes.object,
  isFieldEdit: PropTypes.bool,
  isConfigEdit: PropTypes.bool,
  allGlobalFields: PropTypes.array,
  productsList: PropTypes.array,
  formInfo: PropTypes.object,
  globalFields: PropTypes.array,
  commonPropertiesForConfigs: PropTypes.object,
  removeMedia: PropTypes.func,
  mediaList: PropTypes.array,
  formPrinterType: PropTypes.string,
  formScale: PropTypes.number.isRequired,
  isCommonPropertiesModalVisible: PropTypes.bool,
  selectedFields: PropTypes.array,
  pageDimensions: PropTypes.object,
  showPlainPDFViewModal: PropTypes.bool,
  fieldPropertiesFormActionHandlers: PropTypes.object,
  plainPDFPageDimensions: PropTypes.object,
  plainPDFTotalPages: PropTypes.number,
  showReorderFieldsModal: PropTypes.bool,
  isDragging: PropTypes.bool,
  fieldToBeSorted: PropTypes.object,
  sourceSection: PropTypes.string,
  targetSection: PropTypes.string,
  userInputFieldsList: PropTypes.array,
  config: PropTypes.object,
  isFormsLibrary: PropTypes.bool,
  isGlobalForm: PropTypes.bool,
  hasViewOnlyPermission: PropTypes.bool,
  tabConfigurations: PropTypes.object,
  hideTabs: PropTypes.array,
  reducerKey: PropTypes.string,
  configFields: PropTypes.array,
  siteOptions: PropTypes.array,
  stateListOptions: PropTypes.array,
  paymentOptionConfigs: PropTypes.array,
  isFcLiteModeEnabled: PropTypes.bool,
};

Prepare.defaultProps = {
  isLoadingPrepare: false,
  onAction: _noop,
  isConfigurationFormModalVisible: false,
  isFieldFormModalVisible: false,
  metaData: EMPTY_OBJECT,
  fields: EMPTY_OBJECT,
  annotatedFields: EMPTY_OBJECT,
  configuratorMetaData: EMPTY_OBJECT,
  selectedField: EMPTY_OBJECT,
  selectedConfig: EMPTY_OBJECT,
  isFieldEdit: PropTypes.bool,
  isConfigEdit: PropTypes.bool,
  allGlobalFields: EMPTY_ARRAY,
  productsList: EMPTY_ARRAY,
  globalFields: EMPTY_ARRAY,
  formInfo: EMPTY_OBJECT,
  commonPropertiesForConfigs: EMPTY_OBJECT,
  pageDimensions: EMPTY_OBJECT,
  removeMedia: _noop,
  mediaList: EMPTY_ARRAY,
  formPrinterType: PRINTER_TYPES.IMPACT,
  isCommonPropertiesModalVisible: false,
  selectedFields: EMPTY_ARRAY,
  showPlainPDFViewModal: false,
  fieldPropertiesFormActionHandlers: EMPTY_OBJECT,
  plainPDFPageDimensions: EMPTY_OBJECT,
  plainPDFTotalPages: 0,
  showReorderFieldsModal: false,
  isDragging: false,
  fieldToBeSorted: EMPTY_OBJECT,
  sourceSection: EMPTY_STRING,
  targetSection: EMPTY_STRING,
  userInputFieldsList: EMPTY_ARRAY,
  config: EMPTY_OBJECT,
  isFormsLibrary: false,
  isGlobalForm: false,
  hasViewOnlyPermission: false,
  tabConfigurations: EMPTY_OBJECT,
  hideTabs: EMPTY_ARRAY,
  reducerKey: '',
  configFields: EMPTY_ARRAY,
  siteOptions: EMPTY_ARRAY,
  stateListOptions: EMPTY_ARRAY,
  paymentOptionConfigs: EMPTY_ARRAY,
  isFcLiteModeEnabled: false,
};

export default Prepare;
