import _includes from 'lodash/includes';
import _without from 'lodash/without';
import _concat from 'lodash/concat';
import _head from 'lodash/head';
import _slice from 'lodash/slice';
import _forEach from 'lodash/forEach';
import _values from 'lodash/values';
import _map from 'lodash/map';
import _keys from 'lodash/keys';
import _sortBy from 'lodash/sortBy';
import _reduce from 'lodash/reduce';
import _set from 'lodash/set';
import _castArray from 'lodash/castArray';
import _get from 'lodash/get';
import _size from 'lodash/size';
import _join from 'lodash/join';
import _reject from 'lodash/reject';
import _isEmpty from 'lodash/isEmpty';
import _split from 'lodash/split';
import _filter from 'lodash/filter';
import _find from 'lodash/find';
import _range from 'lodash/range';
import _flattenDeep from 'lodash/flattenDeep';
import _flatten from 'lodash/flatten';
import _cloneDeep from 'lodash/cloneDeep';
import _toNumber from 'lodash/toNumber';
import _toUpper from 'lodash/toUpper';
import _isNull from 'lodash/isNull';
import _toString from 'lodash/toString';
import _round from 'lodash/round';
import _every from 'lodash/every';
import _uniqBy from 'lodash/uniqBy';
import _uniq from 'lodash/uniq';
import _some from 'lodash/some';
import _unset from 'lodash/unset';
import _isString from 'lodash/isString';
import _isNil from 'lodash/isNil';
import _has from 'lodash/has';
import _trim from 'lodash/trim';
import _max from 'lodash/max';
import _findLastIndex from 'lodash/findLastIndex';
import _replace from 'lodash/replace';
import _endsWith from 'lodash/endsWith';
import _pick from 'lodash/pick';
import _compact from 'lodash/compact';
import _isNaN from 'lodash/isNaN';
import _divide from 'lodash/divide';
import _multiply from 'lodash/multiply';
import produce from 'immer';
import {
  toMoment,
  getTimeStamp,
  isValidDate,
  moment,
  getUnix,
  startOfDay,
  getCurrentTime,
  endOfDay,
  nextday,
} from '@tekion/tekion-base/utils/dateUtils';
import { EMPTY_ARRAY, EMPTY_STRING, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import FILTER_OPERATORS from '@tekion/tekion-base/constants/filterOperators';
import { uuid } from '@tekion/tekion-components/src/utils';
import * as FormUtils from '@tekion/tekion-base/utils/formConfigurator/utils';
import { FORM_PRINTER_TYPES } from '@tekion/tekion-base/constants/formConfigurator/printers';
import { DATE_TIME_FORMAT } from '@tekion/tekion-conversion-web';
import { SIGNATURE_TYPES } from '@tekion/tekion-base/constants/formConfigurator/constants';
import { FIELD_DEFAULT_STATE_VALUES } from './components/CustomFieldGeneralForm/CustomFieldGeneralForm.constants';
import { getUsageTargetParamsFromPayload } from '../AddForm/AddForm.utils';
import {
  ALIGN_FORM_FIELD_TYPES,
  DEFAULT_ANNOTATION_FIELD_LAYOUT,
  FIELD_TYPES,
  MATCH_FORM_FIELD_TYPES,
  FIELDS_AND_CONFIGS_TABS,
  DEFAULT_FIELD_STATE_GENERAL,
  DEFAULT_TEXT_COLOR,
  DEFAULT_FIELD_STATE_TEXTFORMAT,
  DEFAULT_FIELD_STATE_OFFSET,
  LASER_FONT_VALUES,
  IMPACT_FONT_VALUES,
  PAGE_MARGINS,
  DEFAULT_SCALE,
  DEFAULT_FIELD_HEIGHT,
  UPDATE_GROUPED_FIELDS,
  MOVE_SELECTED_FIELDS,
  KEYBOARD_KEY_CODES,
  FIELD_DEFAULT_VARIATIONS,
  FIELD_TYPE_VALUES,
  DEFAULT_FIELD_WIDTH,
  PAGE_BORDERS,
  DEFAULT_PDF_NAME,
  EDIT_TYPE_PERMISSIONS,
  DELTA_CONSTANT,
  PTS_TO_MM_CONSTANT,
} from './FormConfiguratorTool.constants';
import { FORM_FIELD, MODULE_TARGETING_DEFAULT_OPTIONS } from '../AddForm/AddForm.constants';
import {
  CONFIG_FIELD_TYPES_DISPLAY_NAME,
  OPERATORS,
  TYPES,
  CONFIG_FIELD_TYPES,
} from './components/Rules/Conditions/Conditions.constant';
import {
  FIELD_DEFAULT_VALUES,
  FORM_FIELDS,
} from './components/FieldPropertiesGeneralForm/FieldPropertiesGeneralForm.constants';
import { DROPDOWN_OPTION_VALUES } from './components/ConfigurationForm/components/FieldsTargetting/FieldsTargetting.constant';
import { DEFAULT_FIELD_KEYS } from './components/ConfigurationForm/components/GeneralConfig/GeneralConfig.constants';
import { ACTION_OPTIONS_KEYS } from './components/Rules/Rules.constants';

import { CONFIG_KEYS } from './components/FieldPropertiesForm/FieldPropertiesForm.constants';
import FormFormulaManager from './FormFormulaManager';
import { LIMIT_TYPES } from './components/TextFormatting/TextFormatting.constants';
import configReader from './readers/config.reader';

const REQUIRED_HASH_ARRAY_LENGTH = 2;

// assumption: ALL values(non-empty) are either present in entities or not
export const checkAndToggle = (entities = EMPTY_ARRAY, values) =>
  (_includes(entities, _head(values)) ? _without : _concat)(entities, ...values);

export const getListOfProducts = products =>
  (products || EMPTY_ARRAY).map(({ product, provider }) => {
    const { id: productId, displayName } = product;
    const { id: providerId } = provider;
    return {
      id: `${providerId}_&_${productId}`,
      productId,
      providerId,
      displayName,
    };
  });

const getPDFMetadataForForm = (formValue, pdfMetadata) => {
  if (_get(formValue, 'pdfLibraryPdfKey')) {
    return {
      [FORM_FIELD.LICENSED_BY]: _get(pdfMetadata, FORM_FIELD.LICENSED_BY),
      [FORM_FIELD.LICENSED_PREFERED_NAME]: _get(pdfMetadata, FORM_FIELD.LICENSED_PREFERED_NAME),
      [FORM_FIELD.LICENSE_KEY]: _get(pdfMetadata, FORM_FIELD.LICENSE_KEY),
      [FORM_FIELD.LICENSED_FOR]: _get(pdfMetadata, FORM_FIELD.LICENSED_FOR),
      [FORM_FIELD.PRINTER]: _get(pdfMetadata, FORM_FIELD.PRINTER),
    };
  }
  return {};
};

export const getFormSetupDetailsFromPayload = (formValue = EMPTY_OBJECT, pdfMetadata) => ({
  ...formValue,
  [FORM_FIELD.TARGETED_MODULES]: _isEmpty(formValue[FORM_FIELD.TARGETED_MODULES])
    ? MODULE_TARGETING_DEFAULT_OPTIONS
    : formValue[FORM_FIELD.TARGETED_MODULES],
  deskingUsageTargetParams: _get(formValue, FORM_FIELD.USAGE_TARGET_PARAMS, EMPTY_ARRAY),
  [FORM_FIELD.USAGE_TARGET_PARAMS]: getUsageTargetParamsFromPayload(formValue),
  [FORM_FIELD.EFFECTIVE_DATE]: toMoment(formValue[FORM_FIELD.EFFECTIVE_DATE]),
  [FORM_FIELD.EXPIRY_DATE]: toMoment(formValue[FORM_FIELD.EXPIRY_DATE]),
  [FORM_FIELD.FORM_CATEGORY]: [formValue[FORM_FIELD.FORM_CATEGORY]],
  ...getPDFMetadataForForm(formValue, pdfMetadata),
});

export const getPixelInNumber = value => {
  if (typeof value === 'string') {
    if (value.includes('px')) {
      return Number(value.replace('px', ''));
    }
    return Number(value);
  }
  return value;
};

const getTopOfSelectedFields = (annotatedFields, selectedFields) => {
  let top = _get(annotatedFields, `${_head(selectedFields)}.y`);
  _forEach(selectedFields, id => {
    if (top > getPixelInNumber(_get(annotatedFields, `${id}.y`))) {
      top = getPixelInNumber(_get(annotatedFields, `${id}.y`));
    }
  });
  return _round(top, 2);
};

const getBottomOfSelectedFields = (annotatedFields, selectedFields) => {
  let bottom =
    getPixelInNumber(_get(annotatedFields, `${_head(selectedFields)}.y`)) +
    getPixelInNumber(_get(annotatedFields, `${_head(selectedFields)}.height`));
  _forEach(selectedFields, id => {
    if (
      bottom <
      getPixelInNumber(_get(annotatedFields, `${id}.y`)) + getPixelInNumber(_get(annotatedFields, `${id}.height`))
    ) {
      bottom =
        getPixelInNumber(_get(annotatedFields, `${id}.y`)) + getPixelInNumber(_get(annotatedFields, `${id}.height`));
    }
  });
  return _round(bottom, 2);
};

const getLeftOfSelectedFields = (annotatedFields, selectedFields) => {
  let left = _get(annotatedFields, `${_head(selectedFields)}.x`);
  _forEach(selectedFields, id => {
    if (left > getPixelInNumber(_get(annotatedFields, `${id}.x`))) {
      left = _get(annotatedFields, `${id}.x`);
    }
  });
  return _round(left, 2);
};

const getRightOfSelectedFields = (annotatedFields, selectedFields) => {
  let right =
    getPixelInNumber(_get(annotatedFields, `${_head(selectedFields)}.x`)) +
    getPixelInNumber(_get(annotatedFields, `${_head(selectedFields)}.width`));
  _forEach(selectedFields, id => {
    if (
      right <
      getPixelInNumber(_get(annotatedFields, `${id}.x`)) + getPixelInNumber(_get(annotatedFields, `${id}.width`))
    ) {
      right =
        getPixelInNumber(_get(annotatedFields, `${id}.x`)) + getPixelInNumber(_get(annotatedFields, `${id}.width`));
    }
  });
  return _round(right, 2);
};

const getHorizontalCenterOfSelectedFields = (annotatedFields, selectedFields) => {
  const leftOfSelectedFields = getLeftOfSelectedFields(annotatedFields, selectedFields);
  const rightOfSelectedFields = getRightOfSelectedFields(annotatedFields, selectedFields);
  return (leftOfSelectedFields + rightOfSelectedFields) / 2;
};

const getVerticalCenterOfSelectedFields = (annotatedFields, selectedFields) => {
  const topOfSelectedFields = getTopOfSelectedFields(annotatedFields, selectedFields);
  const bottomOfSelectedFields = getBottomOfSelectedFields(annotatedFields, selectedFields);
  return (topOfSelectedFields + bottomOfSelectedFields) / 2;
};

export const getPositionInfoOfSelectedFields = (annotatedFields, selectedFields) => ({
  top: getTopOfSelectedFields(annotatedFields, selectedFields),
  bottom: getBottomOfSelectedFields(annotatedFields, selectedFields),
  right: getRightOfSelectedFields(annotatedFields, selectedFields),
  left: getLeftOfSelectedFields(annotatedFields, selectedFields),
});

// TODO: Remove getPixelInNumber() here instead convert while storing in state
const alignSelectedFields = (annotatedFields, selectedFields, updateType, pageDimensions, totalPages) => {
  const newFields = { ...annotatedFields };
  const leftOfSelectedFields = getLeftOfSelectedFields(annotatedFields, selectedFields);
  const toptOfSelectedFields = getTopOfSelectedFields(annotatedFields, selectedFields);
  const rightOfSelectedFields = getRightOfSelectedFields(annotatedFields, selectedFields);
  const bottomOfSelectedFields = getBottomOfSelectedFields(annotatedFields, selectedFields);
  const verticalCenterOfSelectedFields = getVerticalCenterOfSelectedFields(annotatedFields, selectedFields);
  const horizontalCenterOfSelectedFields = getHorizontalCenterOfSelectedFields(annotatedFields, selectedFields);
  _forEach(selectedFields, id => {
    const field = newFields[id];
    switch (updateType) {
      case ALIGN_FORM_FIELD_TYPES.ALIGN_LEFT:
        newFields[id] = { ...field, x: leftOfSelectedFields };
        break;

      case ALIGN_FORM_FIELD_TYPES.ALIGN_TOP:
        newFields[id] = { ...field, y: toptOfSelectedFields };
        break;

      case ALIGN_FORM_FIELD_TYPES.ALIGN_RIGHT:
        newFields[id] = {
          ...field,
          x: rightOfSelectedFields - getPixelInNumber(field.width),
        };
        break;

      case ALIGN_FORM_FIELD_TYPES.ALIGN_BOTTOM:
        newFields[id] = {
          ...field,
          y: bottomOfSelectedFields - getPixelInNumber(field.height),
        };
        break;

      case ALIGN_FORM_FIELD_TYPES.ALIGN_HORIZONTALLY:
        newFields[id] = {
          ...field,
          y: verticalCenterOfSelectedFields - getPixelInNumber(field.height) / 2,
        };
        break;

      case ALIGN_FORM_FIELD_TYPES.ALIGN_VERTICALLY:
        newFields[id] = {
          ...field,
          x: horizontalCenterOfSelectedFields - getPixelInNumber(field.width) / 2,
        };
        break;

      default:
        newFields[id] = field;
        break;
    }

    newFields[id].page = getPageNumber(newFields[id].y, pageDimensions, totalPages);
  });
  return newFields;
};

const matchSelectedFields = (annotatedFields, selectedFields, updateType, pageDimensions, totalPages) => {
  const anchor = annotatedFields[_head(selectedFields)];
  const adjustFields = _slice(selectedFields, 1);
  const newFields = { ...annotatedFields };
  _forEach(adjustFields, id => {
    const field = newFields[id];
    switch (updateType) {
      // Matchings
      case MATCH_FORM_FIELD_TYPES.MATCH_HEIGHT:
        newFields[id] = { ...field, height: anchor.height, actualHeight: anchor.actualHeight };
        break;

      case MATCH_FORM_FIELD_TYPES.MATCH_WIDTH:
        newFields[id] = { ...field, width: anchor.width, actualWidth: anchor.actualWidth };
        break;

      case MATCH_FORM_FIELD_TYPES.MATCH_WIDTH_HEIGHT:
        newFields[id] = {
          ...field,
          width: anchor.width,
          height: anchor.height,
          actualWidth: anchor.actualWidth,
          actualHeight: anchor.actualHeight,
        };
        break;

      default:
        newFields[id] = field;
        break;
    }
    newFields[id].page = getPageNumber(newFields[id].y, pageDimensions, totalPages);
  });
  return newFields;
};

export const getHeightTillPage = (pageDimensions, tillPage) => {
  let height = 0;
  let page = tillPage;
  while (page > 0) {
    height += _get(pageDimensions[page], 'height') || 0;
    page -= 1;
  }
  return height;
};

export const getSafeYPosition = (y, height, pageDimensions) => {
  const targetPage = getPageNumber(y, pageDimensions);

  const pageEnd = getHeightTillPage(pageDimensions, targetPage) + PAGE_MARGINS * (targetPage - 1);

  const yWithHeight = y + height;
  const max = pageEnd + PAGE_MARGINS / 2;

  if (pageEnd < yWithHeight) {
    if (y >= max) {
      return pageEnd + PAGE_MARGINS;
    }
    return pageEnd - height;
  }
  return y;
};

export const updateGroupedFields = ({ annotatedFields, selectedFields, pageDimensions, updateValues }) => {
  const newFields = _cloneDeep(annotatedFields);
  const { dx, dy, y, height } = updateValues;
  let changeInY = dy;
  const safeY = getSafeYPosition(y, height, pageDimensions);
  changeInY -= y - safeY;
  _forEach(selectedFields, id => {
    _set(newFields, `${id}.x`, _round(_get(newFields, `${id}.x`) + dx, 2));
    _set(newFields, `${id}.y`, _round(_get(newFields, `${id}.y`) + changeInY, 2));
    _set(newFields, `${id}.page`, getPageNumber(safeY, pageDimensions) || 1);
  });
  return newFields;
};

export const moveSelectedFields = ({ annotatedFields, selectedFields, updateValues, pageDimensions }) => {
  const newFields = { ...annotatedFields };
  const { x, y, keyCode } = updateValues;
  const { top, bottom, left, right } = getPositionInfoOfSelectedFields(annotatedFields, selectedFields);
  let isValidX = false;
  let isValidY = false;
  const safeYForAllFields = getSafeYPosition(top + y, bottom - top, pageDimensions);
  const pageNumber = _get(newFields, `${_head(selectedFields)}.page`);
  const { width: pageWidth, height: pageHeight } = pageDimensions[pageNumber];
  if (left + x > 0 && right + x < pageWidth) isValidX = true;
  if (right + x > pageWidth && keyCode === KEYBOARD_KEY_CODES.ARROW_LEFT) isValidX = true;
  if (safeYForAllFields === top + y) isValidY = true;
  if (bottom + y > pageHeight && keyCode === KEYBOARD_KEY_CODES.ARROW_UP) isValidY = true;
  _forEach(selectedFields, id => {
    const newField = { ..._get(newFields, id) };
    if (isValidX) _set(newField, `x`, _round(_get(newField, `x`) + x, 1));
    if (isValidY) _set(newField, `y`, _round(_get(newField, `y`) + y, 1));
    _set(newFields, id, newField);
  });
  return newFields;
};

export const updateFieldProperties = (
  annotatedFields,
  selectedFields,
  updateType,
  pageDimensions,
  totalPages,
  updateValues
) => {
  if (updateType === MOVE_SELECTED_FIELDS) {
    return moveSelectedFields({ annotatedFields, selectedFields, updateValues, pageDimensions });
  }
  if (updateType === UPDATE_GROUPED_FIELDS) {
    return updateGroupedFields({ annotatedFields, selectedFields, pageDimensions, updateValues });
  }
  if (_includes(_values(MATCH_FORM_FIELD_TYPES), updateType)) {
    return matchSelectedFields(annotatedFields, selectedFields, updateType, pageDimensions, totalPages);
  }
  return alignSelectedFields(annotatedFields, selectedFields, updateType, pageDimensions, totalPages);
};

const updatedFieldValueByScale = (value, prevScale, newFormScale) => _round((value / prevScale) * newFormScale, 2);

export const updateAnnotationsByScale = (prevScale, newFormScale, annotatedFields) => {
  const updatedFields = _cloneDeep(annotatedFields);
  _forEach(_keys(updatedFields), fieldId => {
    const field = updatedFields[fieldId];
    _set(updatedFields, `${fieldId}.width`, updatedFieldValueByScale(_get(field, 'width'), prevScale, newFormScale));
    _set(updatedFields, `${fieldId}.height`, updatedFieldValueByScale(_get(field, 'height'), prevScale, newFormScale));
    _set(updatedFields, `${fieldId}.x`, updatedFieldValueByScale(_get(field, 'x'), prevScale, newFormScale));
    _set(
      updatedFields,
      `${fieldId}.y`,
      updatedFieldValueByScale(_get(field, 'y') - (_get(field, 'page') - 1) * PAGE_MARGINS, prevScale, newFormScale) +
        (_get(field, 'page') - 1) * PAGE_MARGINS
    );
  });
  return updatedFields;
};

export const updateAnnotationsByPrinterType = (formPrinterType, annotatedFields) => {
  const updatedFields = _cloneDeep(annotatedFields);
  _forEach(_keys(updatedFields), fieldId => {
    const font =
      formPrinterType === FORM_PRINTER_TYPES.LASER ? LASER_FONT_VALUES.HELVETICA_BOLD : IMPACT_FONT_VALUES.COURIER;
    _set(updatedFields, `${fieldId}.textFormat.font`, font);
  });
  return updatedFields;
};

export const getAnnotationsByPage = annotatedList => {
  const annotatedListObjByPage = {};
  _forEach(_sortBy(_values(annotatedList), ['order']), (annotation = {}) => {
    if (annotatedListObjByPage[`page_${annotation.page}`]) {
      annotatedListObjByPage[`page_${annotation.page}`].page = `Page ${annotation.page}`;
      annotatedListObjByPage[`page_${annotation.page}`].values = [
        ...annotatedListObjByPage[`page_${annotation.page}`].values,
        annotation,
      ];
    } else {
      annotatedListObjByPage[`page_${annotation.page}`] = {};
      annotatedListObjByPage[`page_${annotation.page}`].page = `Page ${annotation.page}`;
      annotatedListObjByPage[`page_${annotation.page}`].values = [annotation];
    }
  });
  return _sortBy(
    _map(_keys(annotatedListObjByPage), page => annotatedListObjByPage[page]),
    ['page']
  );
};

export const getParamsFromUrlHash = hash => {
  const params = hash?.split('#');
  if (params?.length > 1) {
    // valid hash
    const list = params[1].split('&'); // hash list
    const obj = {};
    list.forEach(element => {
      const arr = element.split('=');
      if (arr.length === REQUIRED_HASH_ARRAY_LENGTH) {
        const key = arr[0]; // key
        // eslint-disable-next-line prefer-destructuring
        obj[key] = arr[1]; // value
      }
    });
    return obj;
  }
  return {};
};

const getRelativeY = (y, page, pageDimensions, height) =>
  getHeightTillPage(pageDimensions, page) -
  (getPixelInNumber(y) + getPixelInNumber(height)) +
  (page - 1) * PAGE_MARGINS;

export const getAnnotationsByType = annotatedFields => {
  const annotationsByType = {
    globalAnnotations: {},
    configurationAnnotations: {},
    customAnnotations: {},
  };
  return _reduce(
    _values(annotatedFields),
    (acc, curr) => {
      if (curr.fieldType === FIELD_TYPES.GLOBAL_FIELD) annotationsByType.globalAnnotations[curr.id] = curr;
      else if (curr.fieldType === FIELD_TYPES.CONFIGURATION_FIELD)
        annotationsByType.configurationAnnotations[curr.id] = curr;
      else annotationsByType.customAnnotations[curr.id] = curr;
      return acc;
    },
    annotationsByType
  );
};

const getConditionalValuesAsMap = conditionalValues => {
  const applicableValues = _reject(conditionalValues, _isEmpty);

  return _map(applicableValues, (value, index) => ({
    conditionalValueOperator: index === 0 ? 'SHOW' : 'ELSE_SHOW',
    conditionalFieldValue: _get(value, 'globalField'),
  }));
};

const getTextFormatPayload = textFormat => ({
  fontType: _get(textFormat, 'font', EMPTY_STRING),
  fontSize: _get(textFormat, 'size', DEFAULT_FIELD_STATE_TEXTFORMAT.size),
  textColor: _get(textFormat, 'color', DEFAULT_TEXT_COLOR),
  textAlignment: _get(textFormat, 'textAlignment', EMPTY_STRING),
  textAdjustments: _get(textFormat, 'textAdjustments', EMPTY_ARRAY),
  textCombing: _get(textFormat, 'textCombing', null),
  visibility: _get(textFormat, 'visibility', DEFAULT_FIELD_STATE_TEXTFORMAT.visibility),
  orientation: _get(textFormat, 'orientation', DEFAULT_FIELD_STATE_TEXTFORMAT.orientation),
  maxLength: _get(textFormat, 'maxLength', null),
  limitOption: _get(textFormat, 'limitOption', null),
});

const getOffsetPayload = (offsets, formScale, previousOffsets) => {
  const scale = DEFAULT_SCALE / formScale;

  const previousXOffset = _get(previousOffsets, 'xOffset');
  const previousYOffset = _get(previousOffsets, 'yOffset');

  return {
    xOffset: getValueBasedOnDelta(
      _round(_get(offsets, 'xOffset', DEFAULT_FIELD_STATE_OFFSET.xOffset) * scale, 2),
      previousXOffset
    ),
    yOffset: getValueBasedOnDelta(
      _round(_get(offsets, 'yOffset', DEFAULT_FIELD_STATE_OFFSET.yOffset) * scale, 2),
      previousYOffset
    ),
  };
};

const getAffixPayload = affixPayload => ({
  prefix: _get(affixPayload, 'prefix', EMPTY_STRING),
  suffix: _get(affixPayload, 'suffix', EMPTY_STRING),
});

export const getSetupRequiredFields = () => {
  const REQUIRED_FORM_FIELDS = [
    FORM_FIELD.FORM_NAME,
    FORM_FIELD.PRINTER,
    FORM_FIELD.EFFECTIVE_DATE,
    FORM_FIELD.EXPIRY_DATE,
    FORM_FIELD.DEPARTMENT,
    FORM_FIELD.TARGETED_MODULES,
  ];

  return REQUIRED_FORM_FIELDS;
};

const getCommonAnnotationPropertiesPayload = (annotation, pageDimensions, formScale) => {
  const page = _get(annotation, 'page', 1);
  return {
    annotationKey: `${_get(annotation, 'fieldAnnotation', null)}`,
    order: _get(annotation, 'order', null),
    annotationId: _get(annotation, 'annotationId', null),
    active: _get(annotation, 'active', false),
    pageNumber: page,
    ...calculateAnnotationFieldPositions({ annotation, pageDimensions, formScale }),
  };
};

const getCommonAnnotationPayload = (annotation, pageDimensions, formScale) => ({
  ...getCommonAnnotationPropertiesPayload(annotation, pageDimensions, formScale),
  timeStampKey: _get(annotation, 'general.timeStampKey', null),
  fieldType: _head(_get(annotation, 'general.type', null)),
  fieldVariation: _head(_get(annotation, 'general.variation', null)),
  fieldDescription: _get(annotation, 'general.description', null),
  textFormat: getTextFormatPayload(_get(annotation, 'textFormat', EMPTY_OBJECT)),
  offsets: getOffsetPayload(
    _get(annotation, 'offsets', EMPTY_OBJECT),
    formScale,
    _get(annotation, 'general.offsets', EMPTY_OBJECT)
  ),
});

const getDefaultValueTypes = annotation => {
  const defaultValues = {
    defaultStateCustomValue: null,
    defaultStateConditionalValues: null,
    defaultOption: null,
  };
  const valueType = _head(_get(annotation, 'general.defaultValueType', null));
  if (valueType === FIELD_DEFAULT_VALUES.CUSTOM_VALUE) {
    _set(defaultValues, 'defaultStateCustomValue', _get(annotation, 'general.customValues', null));
  } else if (valueType === FIELD_DEFAULT_VALUES.CONDITIONAL_VALUES) {
    _set(
      defaultValues,
      'defaultStateConditionalValues',
      getConditionalValuesAsMap(_get(annotation, 'general.conditionalValues', []))
    );
  } else if (valueType === FIELD_DEFAULT_VALUES.NONE) {
    _set(defaultValues, 'defaultOption', valueType);
  }
  return defaultValues;
};

const getGlobalAnnotationsPayload = (annotations, pageDimensions, formScale) =>
  _map(_values(annotations), annotation => ({
    ...getCommonAnnotationPayload(annotation, pageDimensions, formScale),
    annotationKey: `${_get(annotation, 'fieldAnnotation', null)}`,
    fieldName: _get(annotation, 'fieldName', ''),
    prefix: _get(annotation, 'general.prefix', null),
    suffix: _get(annotation, 'general.suffix', null),
    ...getDefaultValueTypes(annotation),
  }));

const getConfigMetaDataForConfigurationPayload = (
  { general, textFormat, offsets, width, height, affix },
  configFields,
  configId,
  formScale
) => ({
  configName: _get(general, 'configName', EMPTY_STRING),
  prefixKey: _get(general, 'key', EMPTY_STRING),
  rowcount: _get(general, 'numberOfRows', EMPTY_STRING),
  valuesDisplayed: _get(general, 'value', EMPTY_ARRAY),
  width: width || DEFAULT_FIELD_WIDTH,
  height: height || DEFAULT_FIELD_HEIGHT,
  prefix: _get(affix, 'prefix', EMPTY_STRING),
  suffix: _get(affix, 'suffix', EMPTY_STRING),

  textFormat: getTextFormatPayload(textFormat),
  offsets: getOffsetPayload(offsets, formScale, _get(general, 'offsets', EMPTY_OBJECT)),
  defaultValue: _get(general, 'default', null),
  configSequence: getConfigurationStartOrder(configFields, configId),
  delimiter: _get(general, 'delimiter', DEFAULT_FIELD_STATE_GENERAL.delimiter),
});

const getFormProducts = productList =>
  _map(productList, productId => {
    const split = _split(productId, '_&_');
    return {
      id: productId,
      providerId: split[0],
      productId: split[1],
    };
  });

const getFniConfigProducts = productList =>
  _map(productList, productId => {
    const split = _split(productId, '_&_');
    return {
      providerId: split[0],
      productId: split[1],
    };
  });

const disclosureTypes = (configType, operator) =>
  _head(configType.fieldsTargettingConditions).operator === operator &&
  _head(configType.fieldsTargettingConditions).options;

const formCommDisclosureTypes = (configType, operator) =>
  configType.fieldsTargettingConditions[3].operator === operator && configType.fieldsTargettingConditions[3].options;

const getConfigurationStartOrder = (configFields, configId) => {
  const configField = _find(configFields, field => field.configId === configId);
  return _get(_head(_get(configField, 'globalAnnotationList')), 'order');
};

const getCappedForConfig = capped => {
  if (capped) {
    switch (capped) {
      case DROPDOWN_OPTION_VALUES.YES:
        return false;
      case DROPDOWN_OPTION_VALUES.NO:
        return true;
      case DROPDOWN_OPTION_VALUES.BOTH:
        return false;
      default:
        return null;
    }
  }
  return null;
};

const getUpfrontForConfig = (capped, all) => {
  let dropDownVal = null;
  if (capped && !all) {
    dropDownVal = DROPDOWN_OPTION_VALUES.NO;
  } else if (!capped && all) {
    dropDownVal = DROPDOWN_OPTION_VALUES.BOTH;
  } else if (!_isNull(capped) && !capped && !all) {
    dropDownVal = DROPDOWN_OPTION_VALUES.YES;
  }
  return dropDownVal;
};

const getAllConfigFlag = capped => {
  if (capped) {
    switch (capped) {
      case DROPDOWN_OPTION_VALUES.YES:
        return false;
      case DROPDOWN_OPTION_VALUES.NO:
        return false;
      case DROPDOWN_OPTION_VALUES.BOTH:
        return true;
      default:
        return null;
    }
  }
  return null;
};

const getCerifiedForConfig = certified => {
  if (certified) {
    switch (certified) {
      case DROPDOWN_OPTION_VALUES.YES:
        return true;
      case DROPDOWN_OPTION_VALUES.NO:
        return false;
      case DROPDOWN_OPTION_VALUES.BOTH:
        return null;
      default:
        return null;
    }
  }
  return null;
};

const getCerifiedForConfigUpdate = certified => {
  let dropDownVal;
  if (certified) {
    dropDownVal = DROPDOWN_OPTION_VALUES.YES;
  } else if (_isNull(certified)) {
    dropDownVal = DROPDOWN_OPTION_VALUES.BOTH;
  } else if (!_isNull(certified) && !certified) {
    dropDownVal = DROPDOWN_OPTION_VALUES.NO;
  }
  return dropDownVal;
};

const getConfigDataForPayload = general => {
  const configTypeData = configType =>
    _head(_filter(general.fieldTargets, fieldTarget => fieldTarget.fieldsTargettingConfigField === configType));

  const fniConfig = configTypeData('fniConfig') || null;
  const accConfig = configTypeData('accConfig') || null;
  const rebateConfig = configTypeData('rebateConfig') || null;
  const feeConfig = configTypeData('feeConfig') || null;
  const paymentConfig = configTypeData('paymentConfig') || null;
  const formCommissionConfig = configTypeData('formCommissionConfig') || null;
  const fniConfigProducts = _get(fniConfig, 'fieldsTargettingConditions[1].options', null);
  const formCommissionProducts = _get(formCommissionConfig, 'fieldsTargettingConditions[2].options', null);
  const feeIncludeFeeTypes =
    (configTypeData('feeConfig') &&
      feeConfig.fieldsTargettingConditions[0].operator === OPERATORS.INCLUDE &&
      feeConfig.fieldsTargettingConditions[0].options) ||
    null;
  const feeExcludeFeeTypes =
    (configTypeData('feeConfig') &&
      feeConfig.fieldsTargettingConditions[0].operator === OPERATORS.EXCLUDE &&
      feeConfig.fieldsTargettingConditions[0].options) ||
    null;
  const fniConfigData =
    (configTypeData('fniConfig') && {
      includedDisclosureTypes: disclosureTypes(fniConfig, OPERATORS.INCLUDE) || null,
      excludedDisclosureTypes: disclosureTypes(fniConfig, OPERATORS.EXCLUDE) || null,
      includeProductIds: null,
      excludeProductIds: null,
      includePlanCodes: null,
      excludePlanCodes: null,
      capped: getCappedForConfig(_head(_get(fniConfig, 'upfront', EMPTY_ARRAY))),
      allFni: getAllConfigFlag(_head(_get(fniConfig, 'upfront', EMPTY_ARRAY))),
      rows: _get(general, 'numberOfRows', null),
      key: _get(general, 'key', null),
      blankAllowed: _get(general, 'default', null) === DEFAULT_FIELD_KEYS.BLANK,
      productProviderMap: null,
      includeFniProducts:
        (fniConfig.fieldsTargettingConditions[1].operator === OPERATORS.INCLUDE &&
          getFniConfigProducts(fniConfigProducts)) ||
        null,
      excludeFniProducts:
        (fniConfig.fieldsTargettingConditions[1].operator === OPERATORS.EXCLUDE &&
          getFniConfigProducts(fniConfigProducts)) ||
        null,
      productCoverageType:
        (!_isEmpty(_get(fniConfig, 'fieldsTargettingConditions[2].options', null)) &&
          _get(fniConfig, 'fieldsTargettingConditions[2].options', null)) ||
        null,
      taxable: null,
      taxableType: _head(_get(fniConfig, 'taxable', EMPTY_ARRAY)) || null,
      priceAllowed: _get(fniConfig, 'priceAllowed', false),
    }) ||
    null;
  const accConfigData =
    (configTypeData('accConfig') && {
      rows: _get(general, 'numberOfRows', null),
      key: _get(general, 'key', null),
      includedDisclosureTypes: disclosureTypes(accConfig, OPERATORS.INCLUDE) || null,
      excludedDisclosureTypes: disclosureTypes(accConfig, OPERATORS.EXCLUDE) || null,
      partsOrServices:
        (!_isEmpty(_get(accConfig, 'fieldsTargettingConditions[1].options', null)) &&
          _get(accConfig, 'fieldsTargettingConditions[1].options', null)) ||
        null,
      paidBy:
        (!_isEmpty(_get(accConfig, 'fieldsTargettingConditions[2].options', null)) &&
          _get(accConfig, 'fieldsTargettingConditions[2].options', null)) ||
        null,
      includedAccessoryCode:
        _get(accConfig, 'fieldsTargettingConditions[3].operator') === OPERATORS.INCLUDE
          ? _get(accConfig, 'fieldsTargettingConditions[3].options', null)
          : null,
      excludedAccessoryCode:
        _get(accConfig, 'fieldsTargettingConditions[3].operator') === OPERATORS.EXCLUDE
          ? _get(accConfig, 'fieldsTargettingConditions[3].options', null)
          : null,
      capped: getCappedForConfig(_head(_get(accConfig, 'upfront', EMPTY_ARRAY))),
      allAccessory: getAllConfigFlag(_head(_get(accConfig, 'upfront', EMPTY_ARRAY))),
      taxableType: _head(_get(accConfig, 'taxable', EMPTY_ARRAY)) || null,
      residualize: _head(_get(accConfig, 'residualised', EMPTY_ARRAY)) || null,
      priceAllowed: _get(accConfig, 'priceAllowed', false),
    }) ||
    null;
  const feeConfigData =
    (configTypeData('feeConfig') && {
      rows: _get(general, 'numberOfRows', null),
      key: _get(general, 'key', null),
      includeFeeTypes: feeIncludeFeeTypes,
      excludeFeeTypes: feeExcludeFeeTypes,
      capped: getCappedForConfig(_head(_get(feeConfig, 'upfront', EMPTY_ARRAY))),
      allFee: getAllConfigFlag(_head(_get(feeConfig, 'upfront', EMPTY_ARRAY))),
      taxableType: _head(_get(feeConfig, 'taxable', EMPTY_ARRAY)) || null,
      priceAllowed: _get(feeConfig, 'priceAllowed', false),
    }) ||
    null;
  const paymentConfigData =
    (configTypeData('paymentConfig') && {
      rows: _get(general, 'numberOfRows', null),
      key: _get(general, 'key', null),
      includePaymentTypes: _get(paymentConfig, 'fieldsTargettingConditions[0].options', null),
      priceAllowed: _get(paymentConfig, 'priceAllowed', false),
    }) ||
    null;
  const rebateConfigData =
    (configTypeData('rebateConfig') && {
      rows: _get(general, 'numberOfRows', null),
      key: _get(general, 'key', null),
      blankAllowed: _get(general, 'default', null) === DEFAULT_FIELD_KEYS.BLANK,
      rebateType: _toUpper(_get(rebateConfig, 'fieldsTargettingConditions[0].options', null)),
      certified: getCerifiedForConfig(_head(_get(rebateConfig, 'certified', EMPTY_ARRAY))),
      priceAllowed: _get(rebateConfig, 'priceAllowed', false),
    }) ||
    null;
  const formCommissionConfigData =
    (configTypeData('formCommissionConfig') && {
      rows: _get(general, 'numberOfRows', null),
      key: _get(general, 'key', null),
      commissionPlan: _get(formCommissionConfig, 'fieldsTargettingConditions[0].options', null),
      assigneeType: _get(formCommissionConfig, 'fieldsTargettingConditions[1].options', null),
      includedDisclosureTypes: formCommDisclosureTypes(formCommissionConfig, OPERATORS.INCLUDE) || null,
      excludedDisclosureTypes: formCommDisclosureTypes(formCommissionConfig, OPERATORS.EXCLUDE) || null,
      products: getFormProducts(formCommissionProducts) || null,
    }) ||
    null;

  const optionData =
    (configTypeData('option') && {
      rows: _get(general, 'numberOfRows', null),
      key: _get(general, 'key', null),
    }) ||
    null;

  return {
    id: null,
    accConfig: accConfigData,
    option: optionData,
    fniConfig: fniConfigData,
    feeConfig: feeConfigData,
    rebateConfig: rebateConfigData,
    paymentConfig: paymentConfigData,
    mergeAllConfig: true,
    formCommissionConfig: formCommissionConfigData,
  };
};

const getConfigAnnotationPayload = (annotations, pageDimensions, formScale) =>
  _map(_values(annotations), annotation => ({
    fieldName: _get(annotation, 'fieldName', ''),
    ...getCommonAnnotationPropertiesPayload(annotation, pageDimensions, formScale),
    annotationKey: `${_get(annotation, 'fieldAnnotation', null)}`,
    textFormat: getTextFormatPayload(_get(annotation, 'textFormat', EMPTY_OBJECT)),
    offsets: getOffsetPayload(
      _get(annotation, 'offsets', EMPTY_OBJECT),
      formScale,
      _get(annotation, 'general.offsets', EMPTY_OBJECT)
    ),
    ...getAffixPayload(_get(annotation, 'affix', EMPTY_OBJECT)),
  }));

const getConfigurationsAnnotationsPayload = (annotations, pageDimensions, configData, formScale) => {
  const configFields = _get(configData, 'configFields', EMPTY_ARRAY);
  const commonPropertiesForConfigs = _get(configData, 'commonPropertiesForConfigs', EMPTY_OBJECT);
  const configurationsAnnotations = _map(configFields, configField => {
    const commonPropertiesForThisConfig = _get(commonPropertiesForConfigs, configField.configId, EMPTY_OBJECT);
    const annotationsForThisConfig = _filter(annotations, annotation => annotation.configId === configField.configId);
    return {
      configurationId: _get(configData, 'configurationId', null),
      configurationMetadata: getConfigMetaDataForConfigurationPayload(
        commonPropertiesForThisConfig,
        configFields,
        configField.configId,
        formScale
      ),
      config: getConfigDataForPayload(_get(commonPropertiesForThisConfig, 'general', EMPTY_OBJECT)),
      configurationAnnotationList: getConfigAnnotationPayload(annotationsForThisConfig, pageDimensions, formScale),
    };
  });
  return configurationsAnnotations;
};

const getFormulaComponentList = formulas =>
  _map(formulas, formula => {
    const conditions = _map(formula.conditions, condition => ({
      operator: condition.operator,
      values: _isEmpty(_get(condition, 'values')) ? [] : _get(condition, 'values'),
      parameters: condition.fieldsTargettingConditions,
      displayKeys: condition.displayKeys,
    }));
    return {
      conditions,
      formulaExpression: _get(formula, 'expression', EMPTY_STRING),
      fieldMapping: _get(formula, 'formulaMappings', EMPTY_OBJECT),
      formulaType: _get(formula, 'formulaType', EMPTY_STRING),
    };
  });

const getFniProducts = condition =>
  _map(_get(condition, 'values', []), productId => {
    const split = _split(productId, '_&_');
    return {
      providerId: split[0],
      productId: split[1],
    };
  });

const parseValues = condition => {
  const values = _get(condition, 'values');
  const { fieldsTargettingConditions: type } = condition;
  if (type === FIELD_TYPE_VALUES.FREE_TEXT || type === FIELD_TYPE_VALUES.TEXT_AREA) {
    return _map(_split(values, ','), _trim);
  }
  return values ? _castArray(values) : null;
};

const getMandatoryRulesConditions = (conditions, isGlobalForm) => {
  const filledConditions = _filter(
    conditions,
    condition =>
      (!_isEmpty(condition.fieldsTargettingConditions) &&
        (!_isEmpty(condition.values) || isGlobalForm) &&
        !_isEmpty(condition.operator) &&
        !_isEmpty(condition.conditionParameter)) ||
      ((_get(condition, 'conditionParameter.label') ===
        CONFIG_FIELD_TYPES_DISPLAY_NAME[CONFIG_FIELD_TYPES.STOCK_TYPE] ||
        _get(condition, 'conditionParameter.label') === CONFIG_FIELD_TYPES_DISPLAY_NAME[CONFIG_FIELD_TYPES.TRADE_IN]) &&
        !_isEmpty(condition.values) &&
        !_isEmpty(condition.operator) &&
        !_isEmpty(condition.conditionParameter))
  );
  return _map(filledConditions, condition => {
    const type = _get(condition, 'fieldsTargettingConditions', EMPTY_STRING);

    const values = parseValues(condition);
    return {
      fniProducts: type === TYPES.PRODUCT || type === TYPES.FNI_PRODUCT ? getFniProducts(condition) : null,
      operator: _get(condition, 'operator', null),
      values: type === TYPES.PRODUCT || type === TYPES.FNI_PRODUCT ? null : values,
      mandatoryConditionFieldType: _get(condition, 'fieldsTargettingConditions', EMPTY_STRING),
      conditionParameter: {
        conditionParameterType: _get(condition, 'conditionParameter.conditionParameterType', EMPTY_STRING),
        name: _get(condition, 'conditionParameter.value', EMPTY_STRING),
      },
    };
  });
};

const getDefaultStateForCustomField = annotation => {
  const defaultStateType = _get(annotation, 'general.fieldDefaultState', null);
  if (_head(_get(annotation, `general.${FORM_FIELDS.FIELD_TYPE}`)) === FIELD_TYPE_VALUES.SIGNATURE) {
    return { defaultStateType: FIELD_DEFAULT_STATE_VALUES.READ_ONLY, isRequired: false };
  }
  if (defaultStateType === FIELD_DEFAULT_STATE_VALUES.MANDATORY_USER_SELECT) {
    return { defaultStateType: FIELD_DEFAULT_STATE_VALUES.USER_INPUT, isRequired: true };
  }
  return { defaultStateType, isRequired: false };
};

const getDisplayOrderForUserInputCustomField = annotation => {
  const fieldDefaultState = _get(annotation, 'general.fieldDefaultState', null);
  if (
    fieldDefaultState === FIELD_DEFAULT_STATE_VALUES.MANDATORY_USER_SELECT ||
    fieldDefaultState === FIELD_DEFAULT_STATE_VALUES.USER_INPUT
  ) {
    return {
      displayOrder: _get(annotation, 'displayOrder', 0),
      fieldDisplaySectionId: _get(annotation, 'fieldDisplaySectionId', EMPTY_STRING),
    };
  }
  return {
    displayOrder: 0,
    fieldDisplaySectionId: EMPTY_STRING,
  };
};

const getCustomAnnotationsPayload = (
  annotations,
  pageDimensions,
  formScale,
  hideTabs = [],
  getDateTimeFormatValue,
  isGlobalForm
) =>
  _map(_values(annotations), annotation => {
    const fieldUserInputValue = _get(annotation, 'general.fieldUserInputValue', null);
    const fieldType = _head(_get(annotation, 'general.type', [EMPTY_STRING]));
    let userInputDefaultValue;
    let displayValueOptions;
    if (fieldType === FIELD_TYPE_VALUES.CHECKBOX) {
      userInputDefaultValue = null;
      displayValueOptions = [
        {
          key: null,
          displayKey: _head(_castArray(_get(annotation, 'general.name', null))),
          value: fieldUserInputValue,
          globalAnnotationKey: `${_head(_castArray(_get(annotation, 'general.name', null)))}`,
          defaultValue: null,
        },
      ];
    } else if (
      fieldType === FIELD_TYPE_VALUES.DATE &&
      toMoment(fieldUserInputValue) &&
      isValidDate(fieldUserInputValue)
    ) {
      userInputDefaultValue = toMoment(fieldUserInputValue).format(getDateTimeFormatValue(DATE_TIME_FORMAT.BASE));
      displayValueOptions = _get(annotation, 'general.customOptions', null);
    } else if (fieldType === FIELD_TYPE_VALUES.PRICE) {
      userInputDefaultValue = _toString(fieldUserInputValue);
    } else {
      userInputDefaultValue = fieldUserInputValue;
      displayValueOptions = _get(annotation, 'general.customOptions', null);
    }
    let readOnlyDefaultValue = _get(annotation, 'general.fieldReadOnlyValue', null);
    if (fieldType === FIELD_TYPE_VALUES.DATE && toMoment(readOnlyDefaultValue) && isValidDate(readOnlyDefaultValue)) {
      readOnlyDefaultValue = toMoment(readOnlyDefaultValue).format(getDateTimeFormatValue(DATE_TIME_FORMAT.BASE));
    } else if (fieldType === FIELD_TYPE_VALUES.PRICE) {
      readOnlyDefaultValue = _toString(readOnlyDefaultValue);
    }
    const result = {
      ...getCommonAnnotationPayload(annotation, pageDimensions, formScale),
      displayName: _get(annotation, 'general.displayName', null),
      languages: _get(annotation, 'general.languages', null),
      fieldName: _head(_castArray(_get(annotation, 'general.name', null))),
      displayValueOptions,
      haveDefaultOptions: _get(annotation, 'general.haveDefaultOptions', null),
      ...getDefaultStateForCustomField(annotation),
      readOnlyDefaultValue,
      userInputDefaultValue,
      isMandatoryField: _get(annotation, 'general.fieldIsMandatoryField', null),
      prefix: _get(annotation, 'general.prefix', null),
      suffix: _get(annotation, 'general.suffix', null),
      ...getDisplayOrderForUserInputCustomField(annotation),
    };

    if (!hideTabs.includes(CONFIG_KEYS.FORMULA)) {
      result.formula = {
        formulaComponentsList: getFormulaComponentList(_get(annotation, 'formula.formulas', null)),
        jexl: _get(annotation, 'formula.jexlScript', null),
        preference: _get(annotation, 'formula.preference', null),
      };
    }

    if (!hideTabs.includes(CONFIG_KEYS.RULES)) {
      result.rules = {
        ruleName: EMPTY_STRING,
        annotationToMandate: `${_get(annotation, 'general.name', null)}`,
        action: null,
        addNAIfNotMandatory: _get(annotation, 'rules.action') === ACTION_OPTIONS_KEYS.NA,
        mandatoryConditions: getMandatoryRulesConditions(_get(annotation, 'rules.conditions', {}), isGlobalForm),
      };
    }
    return result;
  });

export const getAllAnnotationPayload = (
  { annotatedFields, pageDimensions, configData, formScale, hideTabs = [], isGlobalForm = false },
  getDateTimeFormatValue
) => {
  const annotationsPayload = {};
  const annotationsByType = getAnnotationsByType(annotatedFields);
  _set(
    annotationsPayload,
    'globalAnnotations',
    getGlobalAnnotationsPayload(annotationsByType.globalAnnotations, pageDimensions, formScale)
  );
  _set(
    annotationsPayload,
    'configurationsAnnotations',
    getConfigurationsAnnotationsPayload(
      annotationsByType.configurationAnnotations,
      pageDimensions,
      configData,
      formScale
    )
  );
  _set(
    annotationsPayload,
    'customAnnotations',
    getCustomAnnotationsPayload(
      annotationsByType.customAnnotations,
      pageDimensions,
      formScale,
      hideTabs,
      getDateTimeFormatValue,
      isGlobalForm
    )
  );
  return annotationsPayload;
};

export const getFieldDisplayName = fieldProperties =>
  _head(_castArray(_get(fieldProperties, 'general.name') || _get(fieldProperties, 'fieldName'))) || EMPTY_STRING;

export const getFieldsWithDateValuesInTimestamp = valuesObjWithFieldType =>
  valuesObjWithFieldType.map(field => {
    const value = _head(_get(field, 'values'));
    if (value && field.fieldType === FIELD_TYPE_VALUES.DATE && toMoment(value) && isValidDate(value))
      return { ...field, values: [Number(getTimeStamp(value))] };
    if (value && field.fieldType === FIELD_TYPE_VALUES.PRICE) return { ...field, values: [_toString(value)] };
    return field;
  });
export const getFieldPositions = (annotatedFields, pageDimensions, totalPages, formScrollTop, formScale) => {
  let fieldPositionY = formScrollTop || 0;
  const delta = 5;
  const sortedFields = _sortBy(_values(annotatedFields), 'y');

  for (let i = 0; i < _size(sortedFields); i += 1) {
    const field = sortedFields[i];
    if (!field.x) {
      if (
        fieldPositionY + delta >= getPixelInNumber(field.y) &&
        fieldPositionY < getPixelInNumber(field.y) + getPixelInNumber(field.height)
      ) {
        fieldPositionY = getPixelInNumber(field.y) + getPixelInNumber(field.height);
      } else {
        break;
      }
    }
  }

  return {
    ...DEFAULT_ANNOTATION_FIELD_LAYOUT,
    width: DEFAULT_FIELD_WIDTH * formScale,
    height: DEFAULT_FIELD_HEIGHT * formScale,
    y: (fieldPositionY === 0 ? fieldPositionY : fieldPositionY + delta) % getPdfHeight(pageDimensions),
    page: getPageNumber(fieldPositionY === 0 ? fieldPositionY : fieldPositionY + delta, pageDimensions) || 1,
  };
};

export const getFieldsAndConfigsTabsUid = tab => `FIELDS_AND_CONFIGS_${tab}`;

export const getAllTabsKeys = () => _map(_values(FIELDS_AND_CONFIGS_TABS), tab => getFieldsAndConfigsTabsUid(tab));

const getCustomFieldFormulas = (formula, order) => {
  if (_isNil(formula)) return null;
  const { formulaComponentsList = [], jexl: jexlScript = '', preference = null } = formula || {};
  const formulas = _map(formulaComponentsList, formulaComponent => {
    const { conditions, formulaExpression: expression, fieldMapping, formulaType } = formulaComponent || {};
    return {
      conditions: _map(conditions, ({ parameters, displayKeys, ...rest }) => ({
        ...rest,
        fieldsTargettingConditions: parameters,
        displayKeys,
      })),
      expression,
      formulaMappings: fieldMapping,
      formulaType,
    };
  });

  const dependents = [];
  _forEach(formulas, _formula => {
    const formulaDependents = _keys(_get(_formula, 'formulaMappings', {}));
    dependents.push(...formulaDependents);
  });
  FormFormulaManager.updateDependents(`f${order}`, dependents);

  return {
    formulas,
    jexlScript,
    defineJexl: !!jexlScript,
    preference,
  };
};

const reParseValues = ({ condition } = EMPTY_OBJECT) => {
  const { mandatoryConditionFieldType, operator } = condition;
  let values = _get(condition, 'values');
  if (
    mandatoryConditionFieldType === FIELD_TYPE_VALUES.FREE_TEXT ||
    mandatoryConditionFieldType === FIELD_TYPE_VALUES.TEXT_AREA
  ) {
    values = _join(values, ', ');
    return {
      values: _castArray(values),
    };
  }
  if (mandatoryConditionFieldType === FIELD_TYPE_VALUES.DATE) {
    const dateDMYs = _get(condition, 'values');
    if (operator === OPERATORS.BTW) {
      values = [Number(dateDMYs[0]), Number(dateDMYs[1])];
    } else {
      values = [Number(dateDMYs[0])];
    }
  }
  if (mandatoryConditionFieldType === TYPES.PRODUCT || mandatoryConditionFieldType === TYPES.FNI_PRODUCT) {
    const fniProducts = _get(condition, 'fniProducts');
    values = _map(fniProducts, ({ productId, providerId }) => `${providerId}_&_${productId}`);
  }

  return { values };
};

export const getCustomFieldRules = (rules, productsList) => {
  if (_isNil(rules)) return null;
  const { mandatoryConditions } = rules;
  const conditions = _map(mandatoryConditions, condition => {
    const { conditionParameter, mandatoryConditionFieldType, values, dateDMYs, ...rest } = condition;
    const parsedValues = reParseValues({ condition, productsList });

    const { conditionParameterType: parameterType, name: parameterName } = conditionParameter;
    const parameterLabel = CONFIG_FIELD_TYPES_DISPLAY_NAME[parameterName];
    const fieldType = TYPES[mandatoryConditionFieldType];

    return {
      ...rest,
      ...parsedValues,
      fieldsTargettingConditions: mandatoryConditionFieldType,
      conditionParameter: {
        conditionParameterType: parameterType,
        label: parameterLabel || parameterName,
        value: parameterName,
        fieldType,
      },
    };
  });
  let action;
  if (_get(rules, 'addNAIfNotMandatory', EMPTY_STRING) === true) {
    action = ACTION_OPTIONS_KEYS.NA;
  } else action = ACTION_OPTIONS_KEYS.BLANK;
  return {
    action,
    conditions,
  };
};

const getTextFormattingOptions = textFormat => ({
  textFormat: {
    font: _get(textFormat, 'fontType'),
    size: _get(textFormat, 'fontSize'),
    color: _get(textFormat, 'textColor'),
    textAlignment: _get(textFormat, 'textAlignment'),
    textAdjustments: _get(textFormat, 'textAdjustments'),
    textCombing: _get(textFormat, 'textCombing'),
    orientation: _get(textFormat, 'orientation'),
    visibility: _get(textFormat, 'visibility'),
    maxLength: _get(textFormat, 'maxLength') === 0 ? null : _get(textFormat, 'maxLength'),
    limitOption:
      !_get(textFormat, 'limitOption') && _get(textFormat, 'textCombing') > 0
        ? LIMIT_TYPES.COMB
        : _get(textFormat, 'limitOption', null),
  },
});

const getOffsets = (offsets, formScale) => {
  const scale = formScale / DEFAULT_SCALE;
  return {
    offsets: {
      xOffset: _round(_get(offsets, 'xOffset', 0) * scale, 2),
      yOffset: _round(_get(offsets, 'yOffset', 0) * scale, 2),
    },
  };
};

const getDefaultValueType = annotation => {
  const defaultStateCustomValue = _get(annotation, 'defaultStateCustomValue');
  const defaultStateConditionalValues = _get(annotation, 'defaultStateConditionalValues');
  const defaultOption = _get(annotation, 'defaultOption');
  if (defaultStateCustomValue) return [FIELD_DEFAULT_VALUES.CUSTOM_VALUE];
  if (defaultStateConditionalValues) return [FIELD_DEFAULT_VALUES.CONDITIONAL_VALUES];
  if (defaultOption) return [FIELD_DEFAULT_VALUES.NONE];
  return [FIELD_DEFAULT_VALUES.BLANK];
};

const getDefaultCustomValue = annotation => {
  const defaultStateCustomValue = _get(annotation, 'defaultStateCustomValue');
  if (defaultStateCustomValue) return defaultStateCustomValue;
  return null;
};

const getDefaultConditionalvalue = annotation => {
  const defaultStateConditionalValues = _get(annotation, 'defaultStateConditionalValues');
  if (defaultStateConditionalValues)
    return _map(defaultStateConditionalValues, conditionalValue => ({
      globalField: conditionalValue.conditionalFieldValue,
    }));
  return null;
};

const getAbsoluteY = (pageNumber, pageDimensions, lowerLeftY, height, scale) => {
  const scaledHeight = (lowerLeftY + height) * scale;
  const _y = getHeightTillPage(pageDimensions, pageNumber) - scaledHeight;
  const marginAdjustments = (pageNumber - 1) * PAGE_MARGINS;
  return _round(_y + marginAdjustments, 2);
};

const getCommonAnnotationInfo = (annotation, pageDimensions, fieldType, formScale) => {
  const {
    annotationId = null,
    fieldName,
    fieldDescription,
    annotationKey,
    pageNumber,
    order,
    active,
    lowerLeftX,
    lowerLeftY,
    height,
    width,
    fieldType: generalFieldType,
    fieldVariation,
    timeStampKey,
  } = annotation;
  const scale = formScale / DEFAULT_SCALE;
  const generalInfo = {};
  _set(generalInfo, 'fieldName', fieldName);
  _set(generalInfo, 'description', fieldDescription);
  _set(generalInfo, 'fieldAnnotation', annotationKey);
  _set(generalInfo, 'page', pageNumber);
  _set(generalInfo, 'order', order);
  _set(generalInfo, 'annotationId', annotationId);
  _set(generalInfo, 'active', Boolean(active));
  _set(generalInfo, 'x', _round(lowerLeftX * scale, 2));
  _set(generalInfo, 'y', getAbsoluteY(pageNumber, pageDimensions, lowerLeftY, height, scale));
  _set(generalInfo, 'height', _round(height * scale, 2));
  _set(generalInfo, 'width', _round(width * scale, 2));
  _set(generalInfo, 'actualHeight', height);
  _set(generalInfo, 'actualWidth', width);
  _set(generalInfo, 'fieldType', fieldType);
  _set(generalInfo, 'general.name', [annotationKey]);
  _set(generalInfo, 'general.type', [generalFieldType]);
  _set(generalInfo, 'general.variation', [fieldVariation]);
  _set(generalInfo, 'general.description', fieldDescription);
  _set(generalInfo, 'general.timeStampKey', timeStampKey);
  _set(generalInfo, 'general.lowerLeftX', lowerLeftX);
  _set(generalInfo, 'general.lowerLeftY', lowerLeftY);
  return generalInfo;
};

const getGeneralAnnotationInfo = (annotation, pageDimensions, fieldType, formScale) => {
  const { prefix, suffix, offsets } = annotation;
  const generalAnnotationInfo = getCommonAnnotationInfo(annotation, pageDimensions, fieldType, formScale);
  _set(generalAnnotationInfo, 'general.prefix', prefix);
  _set(generalAnnotationInfo, 'general.suffix', suffix);
  _set(generalAnnotationInfo, 'general.defaultValueType', getDefaultValueType(annotation));
  _set(generalAnnotationInfo, 'general.customValues', getDefaultCustomValue(annotation));
  _set(generalAnnotationInfo, 'general.conditionalValues', getDefaultConditionalvalue(annotation));
  _set(generalAnnotationInfo, 'general.offsets', offsets);
  return generalAnnotationInfo;
};

const getDefaultCustomValueFromResponse = (annotation, key) => {
  if (
    _get(annotation, 'fieldType') === FIELD_TYPE_VALUES.DATE &&
    toMoment(_get(annotation, key)) &&
    isValidDate(_get(annotation, key))
  ) {
    return moment.utc(_get(annotation, key));
  }
  return _get(annotation, key);
};

const getDefaultStateFromResponse = annotation => {
  const isRequired = _get(annotation, 'isRequired');
  const defaultState = _get(annotation, 'defaultStateType');
  if (isRequired && defaultState === FIELD_DEFAULT_STATE_VALUES.USER_INPUT) {
    return FIELD_DEFAULT_STATE_VALUES.MANDATORY_USER_SELECT;
  }
  return defaultState;
};

const getCustomAnnotationInfo = (annotation, pageDimensions, fieldType, formScale) => {
  const customAnnotationInfo = getCommonAnnotationInfo(annotation, pageDimensions, fieldType, formScale);
  _set(customAnnotationInfo, 'general.name', _get(annotation, 'fieldName'));
  _set(customAnnotationInfo, 'general.customOptions', _get(annotation, 'displayValueOptions', []));
  _set(customAnnotationInfo, 'general.haveDefaultOptions', _get(annotation, 'haveDefaultOptions', false));
  _set(customAnnotationInfo, 'general.displayName', _get(annotation, 'displayName', ''));
  _set(customAnnotationInfo, 'general.languages', _get(annotation, 'languages', null));

  _set(customAnnotationInfo, 'general.fieldDefaultState', getDefaultStateFromResponse(annotation));
  _set(
    customAnnotationInfo,
    'general.fieldReadOnlyValue',
    getDefaultCustomValueFromResponse(annotation, 'readOnlyDefaultValue')
  );
  if (_get(annotation, 'fieldType') === FIELD_TYPE_VALUES.CHECKBOX && _size(annotation.displayValueOptions) > 0) {
    const userInputDefaultVal = _get(_head(annotation.displayValueOptions), 'value', '');
    _set(customAnnotationInfo, 'general.fieldUserInputValue', userInputDefaultVal);
  } else {
    _set(
      customAnnotationInfo,
      'general.fieldUserInputValue',
      getDefaultCustomValueFromResponse(annotation, 'userInputDefaultValue')
    );
  }
  _set(customAnnotationInfo, 'general.fieldIsMandatoryField', _get(annotation, 'isMandatoryField'));
  _set(customAnnotationInfo, 'general.prefix', _get(annotation, 'prefix'));
  _set(customAnnotationInfo, 'general.suffix', _get(annotation, 'suffix'));
  _set(customAnnotationInfo, 'displayOrder', _get(annotation, 'displayOrder', 0));
  _set(customAnnotationInfo, 'fieldDisplaySectionId', _get(annotation, 'fieldDisplaySectionId', EMPTY_STRING));
  return customAnnotationInfo;
};

const getDefaultDelimiterValue = delimiterVal => {
  if (!delimiterVal) return DEFAULT_FIELD_STATE_GENERAL.delimiter;
  return delimiterVal;
};

const getConfigurationsAnnotationsInfo = (config, configMetaData) => {
  const { accConfig, fniConfig, feeConfig, rebateConfig, paymentConfig, formCommissionConfig } = config;
  const configInfo = {};
  const id = uuid();
  const field = _filter(_keys(config), key => config[key] !== null && key !== 'mergeAllConfig');
  const numOfRows = _toString(configMetaData.rowcount);

  _set(configInfo, 'general.id', id);
  _set(configInfo, 'general.configName', configMetaData.configName);
  _set(configInfo, 'general.numberOfRows', numOfRows);
  _set(configInfo, 'general.field', field);
  _set(configInfo, 'general.value', configMetaData.valuesDisplayed);
  _set(configInfo, 'general.key', configMetaData.prefixKey);
  _set(configInfo, 'general.default', configMetaData.defaultValue);
  _set(configInfo, 'general.delimiter', getDefaultDelimiterValue(configMetaData.delimiter));

  const fieldTargets = [];
  field.forEach(fieldValue => {
    fieldTargets.push({ fieldsTargettingConfigField: fieldValue });
  });

  if (_includes(field, 'accConfig')) {
    const accConfigData = fieldTargets.find(fieldTarget => fieldTarget.fieldsTargettingConfigField === 'accConfig');
    _set(accConfigData, 'taxable', _split(accConfig.taxableType, ','));
    _set(accConfigData, 'priceAllowed', accConfig.priceAllowed);
    _set(accConfigData, 'residualised', _split(accConfig.residualize, ','));
    _set(accConfigData, 'upfront', _split(getUpfrontForConfig(accConfig.capped, accConfig.allAccessory), ','));
    _set(accConfigData, 'fieldsTargettingConditions[0].fieldsTargettingConditionType', 'disclosureType');
    if (accConfig.includedDisclosureTypes) {
      _set(accConfigData, 'fieldsTargettingConditions[0].operator', OPERATORS.INCLUDE);
      _set(accConfigData, 'fieldsTargettingConditions[0].options', accConfig.includedDisclosureTypes);
    } else if (accConfig.excludedDisclosureTypes) {
      _set(accConfigData, 'fieldsTargettingConditions[0].operator', OPERATORS.EXCLUDE);
      _set(accConfigData, 'fieldsTargettingConditions[0].options', accConfig.excludedDisclosureTypes);
    }
    _set(accConfigData, 'fieldsTargettingConditions[1].fieldsTargettingConditionType', 'partsOrServices');
    _set(accConfigData, 'fieldsTargettingConditions[1].operator', OPERATORS.INCLUDE);
    _set(accConfigData, 'fieldsTargettingConditions[1].options', accConfig.partsOrServices);
    _set(accConfigData, 'fieldsTargettingConditions[2].fieldsTargettingConditionType', 'paidBy');
    _set(accConfigData, 'fieldsTargettingConditions[2].operator', OPERATORS.INCLUDE);
    _set(accConfigData, 'fieldsTargettingConditions[2].options', accConfig.paidBy);

    _set(accConfigData, 'fieldsTargettingConditions[3].fieldsTargettingConditionType', 'accessoryCode');
    if (accConfig.includedAccessoryCode) {
      _set(accConfigData, 'fieldsTargettingConditions[3].operator', OPERATORS.INCLUDE);
      _set(accConfigData, 'fieldsTargettingConditions[3].options', accConfig.includedAccessoryCode);
    }
    if (accConfig.excludedAccessoryCode) {
      _set(accConfigData, 'fieldsTargettingConditions[3].operator', OPERATORS.EXCLUDE);
      _set(accConfigData, 'fieldsTargettingConditions[3].options', accConfig.excludedAccessoryCode);
    }
  }

  if (_includes(field, 'fniConfig')) {
    const fniConfigData = fieldTargets.find(fieldTarget => fieldTarget.fieldsTargettingConfigField === 'fniConfig');
    _set(fniConfigData, 'taxable', _split(fniConfig.taxableType, ','));
    _set(fniConfigData, 'priceAllowed', fniConfig.priceAllowed);
    _set(fniConfigData, 'upfront', _split(getUpfrontForConfig(fniConfig.capped, fniConfig.allFni), ','));

    _set(fniConfigData, 'fieldsTargettingConditions[0].fieldsTargettingConditionType', 'disclosureType');
    if (fniConfig.includedDisclosureTypes) {
      _set(fniConfigData, 'fieldsTargettingConditions[0].operator', OPERATORS.INCLUDE);
      _set(fniConfigData, 'fieldsTargettingConditions[0].options', fniConfig.includedDisclosureTypes);
    } else if (fniConfig.excludedDisclosureTypes) {
      _set(fniConfigData, 'fieldsTargettingConditions[0].operator', OPERATORS.EXCLUDE);
      _set(fniConfigData, 'fieldsTargettingConditions[0].options', fniConfig.excludedDisclosureTypes);
    }
    _set(fniConfigData, 'fieldsTargettingConditions[1].fieldsTargettingConditionType', 'products');
    if (fniConfig.includeFniProducts) {
      _set(fniConfigData, 'fieldsTargettingConditions[1].operator', OPERATORS.INCLUDE);
      _set(
        fniConfigData,
        'fieldsTargettingConditions[1].options',
        _map(fniConfig.includeFniProducts, ({ productId, providerId }) => `${providerId}_&_${productId}`)
      );
    }
    if (fniConfig.excludeFniProducts) {
      _set(fniConfigData, 'fieldsTargettingConditions[1].operator', OPERATORS.EXCLUDE);
      _set(
        fniConfigData,
        'fieldsTargettingConditions[1].options',
        _map(fniConfig.excludeFniProducts, ({ productId, providerId }) => `${providerId}_&_${productId}`)
      );
    }
    _set(fniConfigData, 'fieldsTargettingConditions[2].fieldsTargettingConditionType', 'productCoverageType');
    _set(fniConfigData, 'fieldsTargettingConditions[2].operator', OPERATORS.INCLUDE);
    if (fniConfig.productCoverageType) {
      _set(fniConfigData, 'fieldsTargettingConditions[2].options', fniConfig.productCoverageType);
    }
  }

  if (_includes(field, 'feeConfig')) {
    const feeConfigData = fieldTargets.find(fieldTarget => fieldTarget.fieldsTargettingConfigField === 'feeConfig');
    _set(feeConfigData, 'taxable', _split(feeConfig.taxableType, ','));
    _set(feeConfigData, 'priceAllowed', feeConfig.priceAllowed);
    _set(feeConfigData, 'upfront', _split(getUpfrontForConfig(feeConfig.capped, feeConfig.allFee), ','));
    _set(feeConfigData, 'fieldsTargettingConditions[0].fieldsTargettingConditionType', 'feeType');
    if (feeConfig.includeFeeTypes) {
      _set(feeConfigData, 'fieldsTargettingConditions[0].operator', OPERATORS.INCLUDE);
      _set(feeConfigData, 'fieldsTargettingConditions[0].options', feeConfig.includeFeeTypes);
    } else if (feeConfig.excludeFeeTypes) {
      _set(feeConfigData, 'fieldsTargettingConditions[0].operator', OPERATORS.EXCLUDE);
      _set(feeConfigData, 'fieldsTargettingConditions[0].options', feeConfig.excludeFeeTypes);
    }
  }
  if (_includes(field, 'paymentConfig')) {
    const paymentConfigData = fieldTargets.find(
      fieldTarget => fieldTarget.fieldsTargettingConfigField === 'paymentConfig'
    );
    _set(paymentConfigData, 'priceAllowed', paymentConfig.priceAllowed);
    _set(paymentConfigData, 'fieldsTargettingConditions[0].fieldsTargettingConditionType', 'paymentType');
    if (paymentConfig.includePaymentTypes) {
      _set(paymentConfigData, 'fieldsTargettingConditions[0].operator', OPERATORS.INCLUDE);
      _set(paymentConfigData, 'fieldsTargettingConditions[0].options', paymentConfig.includePaymentTypes);
    } else if (paymentConfig.excludePaymentTypes) {
      _set(paymentConfigData, 'fieldsTargettingConditions[0].operator', OPERATORS.EXCLUDE);
      _set(paymentConfigData, 'fieldsTargettingConditions[0].options', paymentConfig.excludePaymentTypes);
    }
  }
  if (_includes(field, 'rebateConfig')) {
    const rebateConfigData = fieldTargets.find(
      fieldTarget => fieldTarget.fieldsTargettingConfigField === 'rebateConfig'
    );
    _set(rebateConfigData, 'priceAllowed', rebateConfig.priceAllowed);
    _set(rebateConfigData, 'certified', _split(getCerifiedForConfigUpdate(rebateConfig.certified), ','));
    _set(rebateConfigData, 'fieldsTargettingConditions[0].fieldsTargettingConditionType', 'rebateType');
    _set(rebateConfigData, 'fieldsTargettingConditions[0].operator', OPERATORS.INCLUDE);
    _set(rebateConfigData, 'fieldsTargettingConditions[0].options', rebateConfig.rebateType);
  }

  if (_includes(field, 'formCommissionConfig')) {
    const formCommissionConfigData = fieldTargets.find(
      fieldTarget => fieldTarget.fieldsTargettingConfigField === 'formCommissionConfig'
    );
    _set(formCommissionConfigData, 'fieldsTargettingConditions[0].fieldsTargettingConditionType', 'commissionPlan');
    _set(formCommissionConfigData, 'fieldsTargettingConditions[0].operator', OPERATORS.INCLUDE);
    _set(formCommissionConfigData, 'fieldsTargettingConditions[0].options', formCommissionConfig.commissionPlan);
    _set(formCommissionConfigData, 'fieldsTargettingConditions[1].fieldsTargettingConditionType', 'assigneeType');
    _set(formCommissionConfigData, 'fieldsTargettingConditions[1].options', formCommissionConfig.assigneeType);
    _set(formCommissionConfigData, 'fieldsTargettingConditions[1].operator', OPERATORS.INCLUDE);

    if (formCommissionConfig.products) {
      _set(formCommissionConfigData, 'fieldsTargettingConditions[2].operator', OPERATORS.INCLUDE);
      _set(formCommissionConfigData, 'fieldsTargettingConditions[2].fieldsTargettingConditionType', 'productsList');
      _set(
        formCommissionConfigData,
        'fieldsTargettingConditions[2].options',
        _map(formCommissionConfig.products, ({ productId, providerId }) => `${providerId}_${productId}`)
      );
    }
    _set(formCommissionConfigData, 'fieldsTargettingConditions[3].fieldsTargettingConditionType', 'disclosureType');

    if (formCommissionConfig.includedDisclosureTypes) {
      _set(formCommissionConfigData, 'fieldsTargettingConditions[3].operator', OPERATORS.INCLUDE);

      _set(
        formCommissionConfigData,
        'fieldsTargettingConditions[3].options',
        formCommissionConfig.includedDisclosureTypes
      );
    } else if (formCommissionConfig.excludedDisclosureTypes) {
      _set(formCommissionConfigData, 'fieldsTargettingConditions[3].operator', OPERATORS.EXCLUDE);

      _set(
        formCommissionConfigData,
        'fieldsTargettingConditions[3].options',
        formCommissionConfig.excludedDisclosureTypes
      );
    }
  }
  _set(configInfo, 'general.fieldTargets', fieldTargets);
  return configInfo;
};

export const getAnnotationDetailsFromResponse = (response = {}, pageDimensions, totalPages, formScale) => {
  const annotatedFields = {};
  let formFieldsCount = 0;
  const { globalAnnotations = [], configurationsAnnotations = [], customAnnotations = [] } = response;

  const configFields = [];
  const commonPropertiesForConfigs = {};

  _forEach(globalAnnotations, globalAnnotation => {
    const id = uuid();
    const { order, textFormat, offsets } = globalAnnotation;
    const annotation = {
      id,
      ...getGeneralAnnotationInfo(globalAnnotation, pageDimensions, FIELD_TYPES.GLOBAL_FIELD, formScale),
      ...getTextFormattingOptions(textFormat),
      ...getOffsets(offsets, formScale),
    };

    annotatedFields[id] = annotation;
    if (order > formFieldsCount) formFieldsCount = order;
  });
  if (!_isEmpty(configurationsAnnotations)) {
    _forEach(configurationsAnnotations, configurationsAnnotationValue => {
      const { textFormat, offsets, width, height } = configurationsAnnotationValue.configurationMetadata;
      const configId = uuid();
      const configFieldsGeneral = getConfigurationsAnnotationsInfo(
        configurationsAnnotationValue.config,
        configurationsAnnotationValue.configurationMetadata
      );
      const commonProperties = {
        ...configFieldsGeneral,
        ...getTextFormattingOptions(textFormat),
        ...getOffsets(offsets, formScale),
        affix: getAffixPayload(configurationsAnnotationValue.configurationMetadata),
        width: width || DEFAULT_FIELD_WIDTH,
        height: height || DEFAULT_FIELD_HEIGHT,
      };
      commonPropertiesForConfigs[configId] = commonProperties;

      const configurationMetadata = _get(configurationsAnnotationValue, 'configurationMetadata');
      const rowcount = _get(configurationMetadata, 'rowcount', 0);
      const numOfRows = _range(rowcount);
      const globalAnnotationList = _map(_get(configurationMetadata, 'valuesDisplayed'), (valueText, index) => {
        const configKey = _get(configurationMetadata, 'prefixKey');
        const globalAnnotationListField = _map(numOfRows, row => {
          const globalAnnotation = {
            configId,
            fieldAnnotation: `${configKey + valueText}_${row + 1}`,
            fieldName: `${configKey + valueText}_${row + 1}`,
            description: 'config field',
            fieldType: FIELD_TYPES.CONFIGURATION_FIELD,
            general: {
              valueType: valueText,
              prefix: getFormattedPrefix(
                _get(configFieldsGeneral, 'general.prefix', EMPTY_STRING),
                index * rowcount + _toNumber(row) + 1
              ),
              suffix: _get(configFieldsGeneral, 'general.suffix', EMPTY_STRING),
            },
            affix: {
              prefix: getFormattedPrefix(
                _get(commonProperties, 'affix.prefix', EMPTY_STRING),
                index * rowcount + _toNumber(row) + 1
              ),
              suffix: _get(commonProperties, 'affix.suffix', EMPTY_STRING),
            },
          };
          return globalAnnotation;
        });
        return globalAnnotationListField;
      });
      configFields.push({
        categoryDisplayName: _get(configurationMetadata, 'configName'),
        globalAnnotationList: _flattenDeep(globalAnnotationList),
        configId,
        configurationId: _get(configurationMetadata, 'configurationId', null),
      });
      _forEach(configurationsAnnotationValue.configurationAnnotationList, configurationsAnnotation => {
        const id = uuid();
        const {
          order,
          textFormat: textFormatForConfigAnnotation,
          offsets: offsetsForConfigAnnotation,
          prefix,
          suffix,
        } = configurationsAnnotation;
        const textformatFallback = textFormatForConfigAnnotation || textFormat;
        const offsetFallback = offsetsForConfigAnnotation || offsets;
        const annotation = {
          id,
          configId,
          ...getGeneralAnnotationInfo(
            configurationsAnnotation,
            pageDimensions,
            FIELD_TYPES.CONFIGURATION_FIELD,
            formScale
          ),
          ...getTextFormattingOptions(textformatFallback),
          ...getOffsets(offsetFallback, formScale),
          affix: { prefix, suffix },
        };
        annotatedFields[id] = annotation;
        if (order >= formFieldsCount) {
          formFieldsCount = order;
        }
      });
    });
  }

  _forEach(customAnnotations, customAnnotation => {
    const id = uuid();
    const { order, textFormat, offsets, formula, rules } = customAnnotation;
    const annotation = {
      id,
      ...getCustomAnnotationInfo(customAnnotation, pageDimensions, FIELD_TYPES.CUSTOM_FIELD, formScale),
      ...getTextFormattingOptions(textFormat),
      ...getOffsets(offsets, formScale),
      formula: getCustomFieldFormulas(formula, order),
      rules: getCustomFieldRules(rules),
    };

    annotatedFields[id] = annotation;
    if (order > formFieldsCount) formFieldsCount = order;
  });

  return { annotatedFields, formFieldsCount, configFields, commonPropertiesForConfigs };
};

export const isDuplicateConfigField = (field, annotatedFieldsOrder) =>
  field.fieldType === FIELD_TYPES.CONFIGURATION_FIELD &&
  annotatedFieldsOrder &&
  annotatedFieldsOrder.includes(field.order);

export const isConfigField = field => field.fieldType === FIELD_TYPES.CONFIGURATION_FIELD;

export const getGeneralDefaultProperties = () => DEFAULT_FIELD_STATE_GENERAL;
export const getGeneralProperties = selectedField => {
  const { fieldAnnotation, description, type = FIELD_TYPE_VALUES.FREE_TEXT } = selectedField;

  return {
    ...getGeneralDefaultProperties(),
    name: [fieldAnnotation],
    description,
    [FORM_FIELDS.FIELD_TYPE]: [type],
    [FORM_FIELDS.FIELD_VARIATION]: FIELD_DEFAULT_VARIATIONS[type],
  };
};

export const getConfigFieldsFromResponse = annotations => {
  const configurationAnnotations = _get(annotations, 'configurationsAnnotations');
  const configFields = _map(configurationAnnotations, (configurationAnnotation, index) => {
    const configurationMetadata = _get(configurationAnnotation, 'configurationMetadata');
    const numOfRows = _range(_get(configurationMetadata, 'rowcount'));
    const configSequence = _range(_get(configurationMetadata, 'configSequence')) || [2, 9][index];
    // const configSequence = [2, 9][index] - 1;
    const globalAnnotationList = _map(_get(configurationMetadata, 'valuesDisplayed'), valueText => {
      const configKey = _get(configurationMetadata, 'prefixKey');
      const globalAnnotationListField = _map(numOfRows, row => {
        const id = uuid();
        const globalAnnotation = {
          id,
          fieldAnnotation: `${configKey + valueText}_${row + 1}`,
          fieldName: `${configKey + valueText}_${row + 1}`,
          description: 'config field',
          fieldType: FIELD_TYPES.CONFIGURATION_FIELD,
          order: configSequence + 1 + row,
          ...DEFAULT_ANNOTATION_FIELD_LAYOUT,
        };
        return globalAnnotation;
      });
      return globalAnnotationListField;
    });
    return {
      categoryDisplayName: _get(configurationMetadata, 'configName'),
      globalAnnotationList: _flattenDeep(globalAnnotationList),
    };
  });
  return configFields;
};
export const getGlobalFieldFromName = (globalFields, name) => {
  const globalFieldsArray = _flatten(
    _map(globalFields, globalFieldObj => _get(globalFieldObj, 'globalAnnotationList', []))
  );
  return _find(globalFieldsArray, globalField => _get(globalField, 'fieldAnnotation') === _head(_castArray(name)));
};

export const getDefaultFieldTextFormatting = printerType => ({
  ...DEFAULT_FIELD_STATE_TEXTFORMAT,
  font: printerType === FORM_PRINTER_TYPES.LASER ? LASER_FONT_VALUES.HELVETICA_BOLD : IMPACT_FONT_VALUES.COURIER,
  textAdjustments: printerType === FORM_PRINTER_TYPES.IMPACT ? [] : ['AUTO_SIZE'],
});

const MAX_GAP = 5;
export const showTopReference = (annotatedFields, selectedAnnotation, id) => {
  let show = false;
  let gap = 0;
  _forEach(_values(annotatedFields), annotatedField => {
    const selectedAnnotationY = _get(selectedAnnotation, 'y');
    const annotatedFieldY = _get(annotatedField, 'y');
    const annotatedFieldHeight = _get(annotatedField, 'height');

    if (_get(annotatedField, 'id') !== id) {
      if (Math.abs(selectedAnnotationY - annotatedFieldY) < MAX_GAP) {
        show = true;
        gap = annotatedFieldY - selectedAnnotationY;
      } else if (Math.abs(selectedAnnotationY - (annotatedFieldY + annotatedFieldHeight)) < MAX_GAP) {
        show = true;
        gap = annotatedFieldY + annotatedFieldHeight - selectedAnnotationY;
      }
    }
  });
  return { show, gap: _round(gap) };
};

export const showBottomReference = (annotatedFields, selectedAnnotation, id) => {
  let show = false;
  let gap = 0;
  _forEach(_values(annotatedFields), annotatedField => {
    const selectedAnnotationY = _get(selectedAnnotation, 'y');
    const selectedAnnotationHeight = _get(selectedAnnotation, 'height');
    const annotatedFieldY = _get(annotatedField, 'y');
    const annotatedFieldHeight = _get(annotatedField, 'height');
    if (_get(annotatedField, 'id') !== id) {
      if (Math.abs(selectedAnnotationY + selectedAnnotationHeight - annotatedFieldY) < MAX_GAP) {
        show = true;
        gap = selectedAnnotationY + selectedAnnotationHeight - annotatedFieldY;
      } else if (
        Math.abs(selectedAnnotationY + selectedAnnotationHeight - (annotatedFieldY + annotatedFieldHeight)) < MAX_GAP
      ) {
        show = true;
        gap = selectedAnnotationY + selectedAnnotationHeight - (annotatedFieldY + annotatedFieldHeight);
      }
    }
  });
  return { show, gap: _round(gap) };
};

export const showLeftReference = (annotatedFields, selectedAnnotation, id) => {
  let show = false;
  let gap = 0;
  _forEach(_values(annotatedFields), annotatedField => {
    const selectedAnnotationX = _get(selectedAnnotation, 'x');
    const annotatedFieldX = _get(annotatedField, 'x');
    const annotatedFieldWidth = _get(annotatedField, 'width');

    if (_get(annotatedField, 'id') !== id) {
      if (Math.abs(selectedAnnotationX - annotatedFieldX) < MAX_GAP) {
        show = true;
        gap = annotatedFieldX - selectedAnnotationX;
      } else if (Math.abs(selectedAnnotationX - (annotatedFieldX + annotatedFieldWidth)) < MAX_GAP) {
        show = true;
        gap = annotatedFieldX + annotatedFieldWidth - selectedAnnotationX;
      }
    }
  });
  return { show, gap: _round(gap) };
};

export const showRightReference = (annotatedFields, selectedAnnotation, id) => {
  let show = false;
  let gap = 0;
  _forEach(_values(annotatedFields), annotatedField => {
    const selectedAnnotationX = _get(selectedAnnotation, 'x');
    const selectedAnnotationWidth = _get(selectedAnnotation, 'width');
    const annotatedFieldX = _get(annotatedField, 'x');
    const annotatedFieldWidth = _get(annotatedField, 'width');
    if (_get(annotatedField, 'id') !== id) {
      if (Math.abs(selectedAnnotationX + selectedAnnotationWidth - annotatedFieldX) < MAX_GAP) {
        show = true;
        gap = selectedAnnotationX + selectedAnnotationWidth - annotatedFieldX;
      } else if (
        Math.abs(selectedAnnotationX + selectedAnnotationWidth - (annotatedFieldX + annotatedFieldWidth)) < MAX_GAP
      ) {
        show = true;
        gap = selectedAnnotationX + selectedAnnotationWidth - (annotatedFieldX + annotatedFieldWidth);
      }
    }
  });

  return { show, gap: _round(gap) };
};

export const getPdfKeyOfOptionByValue = (options, valueSelected) => {
  const selectedOption = (options || EMPTY_ARRAY).find(({ value }) => value === valueSelected) || {};
  return _get(selectedOption, 'pdfKey');
};

export const fieldsSearchFilter = (value, searchText) =>
  _every(_split(searchText, ' '), query => _includes(value?.toLowerCase(), query?.toLowerCase()));

export const { isSupportedFileName } = FormUtils;

export const multipleFieldsSelected = (annotatedFields, selectedFields) =>
  _size(_filter(selectedFields, selectedField => _get(annotatedFields, selectedField))) > 1;

export const checkForFieldsInSinglePage = (annotatedFields, selectedFields) => {
  const uniqFieldsByPage = _uniqBy(
    _map(
      _filter(selectedFields, selectedField => _get(annotatedFields, selectedField)),
      selectedField => _get(annotatedFields, selectedField)
    ),
    'page'
  );
  return _size(uniqFieldsByPage) === 1;
};

export const allowGroupSelection = (annotatedFields, selectedFields) =>
  multipleFieldsSelected(annotatedFields, selectedFields) &&
  checkForFieldsInSinglePage(annotatedFields, selectedFields);

export const isConfigSelected = (annotatedFields, selectedFields) => {
  const selectedAnnotatedFields = _map(selectedFields, selectedField => _get(annotatedFields, selectedField));
  return _some(
    selectedAnnotatedFields,
    selectedAnnotatedField => _get(selectedAnnotatedField, 'fieldType') === FIELD_TYPES.CONFIGURATION_FIELD
  );
};

export const allowPropertiesUpdate = (annotatedFields, selectedFields) =>
  multipleFieldsSelected(annotatedFields, selectedFields);

export const moveSelectedFieldsBy = keyCode => {
  const CHANGE = 3;
  if (keyCode === KEYBOARD_KEY_CODES.ARROW_DOWN) {
    return { x: 0, y: CHANGE, keyCode };
  }
  if (keyCode === KEYBOARD_KEY_CODES.ARROW_UP) {
    return { x: 0, y: -CHANGE, keyCode };
  }
  if (keyCode === KEYBOARD_KEY_CODES.ARROW_LEFT) {
    return { x: -CHANGE, y: 0, keyCode };
  }
  if (keyCode === KEYBOARD_KEY_CODES.ARROW_RIGHT) {
    return { x: CHANGE, y: 0, keyCode };
  }
  return { x: 0, y: 0 };
};

export const updateCommonProperties = (annotatedFields, selectedFields, commonProperties) => {
  const newFields = { ...annotatedFields };
  _forEach(selectedFields, id => {
    const newField = { ..._get(newFields, id), ..._get(commonProperties, 'dimensions') };
    _set(newField, `textFormat`, { ..._get(newField, 'textFormat'), ..._get(commonProperties, 'textFormat') });
    _set(newFields, id, newField);
  });
  return newFields;
};

export const { getPageNumber, getPdfHeight } = FormUtils;
export const getScaledPageDimensions = (pageDimensions, formScale, increaseBy) =>
  _reduce(
    _keys(pageDimensions),
    (dimensions, page) => ({
      ...dimensions,
      [page]: {
        ...pageDimensions[page],
        height: _round(
          (_get(pageDimensions, `${page}.height`) / _round(formScale, 1)) * _round(formScale + increaseBy, 1),
          1
        ),
        width: _round(
          (_get(pageDimensions, `${page}.width`) / _round(formScale, 1)) * _round(formScale + increaseBy, 1),
          1
        ),
      },
    }),
    {}
  );

const collide = (rect1, rect2) =>
  rect1.x < rect2.x + rect2.width &&
  rect1.x + rect1.width > rect2.x &&
  rect1.y < rect2.y + rect2.height &&
  rect1.height + rect1.y > rect2.y;

export const getSelectedFieldsInRectangle = (annotatedFields, { startX, startY, endX, endY }) =>
  _map(
    _filter(_values(annotatedFields), annotatedField =>
      collide(annotatedField, {
        x: Math.min(startX, endX),
        y: Math.min(startY, endY),
        width: Math.abs(endX - startX),
        height: Math.abs(endY - startY),
      })
    ),
    annotatedField => _get(annotatedField, 'id')
  );

export const getConfigAnnotationList = (configId, configGeneralProperties, commonProperties) => {
  const numberOfRows = _get(configGeneralProperties, 'numberOfRows', 0);
  const numOfRowsList = _range(numberOfRows);

  const annotationList = _map(_get(configGeneralProperties, 'value'), (valueText, index) => {
    const annotationListFields = _map(numOfRowsList, row => {
      const annotationField = {
        configId,
        fieldAnnotation: `${configGeneralProperties.key + valueText}_${row + 1}`,
        fieldName: `${configGeneralProperties.key + valueText}_${row + 1}`,
        description: 'This is a configuration Field.',
        fieldType: FIELD_TYPES.CONFIGURATION_FIELD,
        offsets: _get(commonProperties, 'offsets'),
        textFormat: _get(commonProperties, 'textFormat'),
        general: {
          valueType: valueText,
        },
        affix: {
          prefix: getFormattedPrefix(
            _get(commonProperties, 'affix.prefix', EMPTY_STRING),
            index * numberOfRows + row + 1
          ),
          suffix: _get(commonProperties, 'affix.suffix', EMPTY_STRING),
        },
      };
      return annotationField;
    });
    return annotationListFields;
  });

  return annotationList;
};

export const removeUnwantedConfigAnnotations = (annotatedFields, configId, configAnnotationList) => {
  const updatedAnnotatedFields = { ...annotatedFields };
  const annotatedConfigFields = _map(
    _filter(
      _values(annotatedFields),
      annotatedField => annotatedField.configId && configId === annotatedField.configId
    ),
    field => ({
      id: _get(field, 'id'),
      fieldAnnotation: _get(field, 'fieldAnnotation'),
    })
  );

  const newConfigAnnotationKeys = _map(_flatten(configAnnotationList), configAnnotation =>
    _get(configAnnotation, 'fieldAnnotation')
  );

  _forEach(annotatedConfigFields, configAnnotation => {
    if (!_includes(newConfigAnnotationKeys, _get(configAnnotation, 'fieldAnnotation'))) {
      _unset(updatedAnnotatedFields, _get(configAnnotation, 'id'));
    }
  });

  return updatedAnnotatedFields;
};
export const getFormattedPrefix = (prefix, index) => {
  if (_isEmpty(prefix)) return EMPTY_STRING;

  const prefixString = _toString(prefix);
  const lastIndexOfSemiColon = _findLastIndex(prefixString, ch => ch === ';');
  if (lastIndexOfSemiColon >= 0) {
    return prefixString
      .substring(0, lastIndexOfSemiColon)
      .concat(_toString(index))
      .concat(prefixString.substring(lastIndexOfSemiColon));
  }
  return prefixString;
};

export const isValidDimension = ({ dimensions = EMPTY_OBJECT, x, y, page }, pageDimensions, formScale) => {
  const { width, height } = dimensions;
  const currentPageMaxY = getHeightTillPage(pageDimensions, page) + PAGE_MARGINS * (page - 1) + PAGE_BORDERS / 2;
  if (
    _isString(width) ||
    _isNil(width) ||
    width <= 0 ||
    x + width * formScale > _get(pageDimensions, `${page}.width`) + PAGE_BORDERS
  ) {
    return false;
  }
  if (_isString(height) || _isNil(height) || height <= 0 || y + height * formScale > currentPageMaxY) {
    return false;
  }
  return true;
};

export const areCoordinatesValid = ({ top, left, width = 0, height = 0 }, pageDimensions) => {
  const [maxWidth, maxHeight] = [
    _max(_map(_values(pageDimensions), 'width')),
    _max(_map(_values(pageDimensions), 'height')),
  ];
  if (top + height > maxHeight || left + width > maxWidth) return false;
  return true;
};

export const shouldDisableSaveAsNewAction = ({ saveAsNewName, saveAsNewReasonForPublish }) =>
  _size(saveAsNewName) < 4 || _isEmpty(saveAsNewReasonForPublish);

// returns the coordinates of an annotation in its page
const getAnnotationCoordinates = (annotation, pageDimensions, formScale) => {
  const scale = DEFAULT_SCALE / formScale;
  const page = _get(annotation, 'page', 1);
  return {
    x: _round(getPixelInNumber(_get(annotation, 'x', 0)) * scale, 2),
    y: _round(
      (_get(annotation, 'y', 0) -
        (getHeightTillPage(pageDimensions, page - 1) + (page - 1) * PAGE_MARGINS + (page - 1) * PAGE_BORDERS)) *
        scale,
      2
    ),
  };
};

export const getAnnotationWithRelativePosition = (annotation, pageDimensions, formScale) => {
  const scale = DEFAULT_SCALE / formScale;
  return {
    ...annotation,
    width: _round(getPixelInNumber(_get(annotation, 'width', 0)) * scale, 2),
    height: _round(getPixelInNumber(_get(annotation, 'height')) * scale, 2),
    ...getAnnotationCoordinates(annotation, pageDimensions, formScale),
  };
};

export const getSelectedFieldsString = (
  annotatedFields,
  selectedFields,
  configFields,
  commonPropertiesForConfigs,
  pageDimensions,
  formScale
) => {
  const selectedAnnotatedFields = _reduce(
    _uniq(selectedFields),
    (acc, curr) => {
      const currentAnnotation = getAnnotationWithRelativePosition(
        _get(annotatedFields, curr),
        pageDimensions,
        formScale
      );
      let selectedAnnotations = {
        ...acc,
        annotations: [..._get(acc, 'annotations'), currentAnnotation],
      };
      if (isConfigField(currentAnnotation)) {
        const configAnnotation = _find(
          _get(acc, 'configurations'),
          config => _get(config, 'configData.configId') === _get(currentAnnotation, 'configId')
        );
        if (!configAnnotation) {
          selectedAnnotations = {
            ...selectedAnnotations,
            configurations: [
              ..._get(acc, 'configurations'),
              {
                configData: _find(
                  configFields,
                  config => _get(config, 'configId') === _get(currentAnnotation, 'configId')
                ),
                commonProperties: _get(commonPropertiesForConfigs, _get(currentAnnotation, 'configId')),
              },
            ],
          };
        }
      }
      return selectedAnnotations;
    },
    {
      annotations: [],
      configurations: [],
    }
  );

  return JSON.stringify(selectedAnnotatedFields);
};

const getAnnotation = (annotation, id, order, pageDimensions, formScrollTop, formScale) => {
  const updatedAnnotation = { ...annotation, id, order };
  const currentPage = getPageNumber(formScrollTop, pageDimensions);
  const scale = formScale / DEFAULT_SCALE;
  _set(updatedAnnotation, 'height', _round(_get(annotation, 'height') * scale, 2));
  _set(updatedAnnotation, 'width', _round(_get(annotation, 'width') * scale, 2));
  _set(updatedAnnotation, 'page', currentPage);
  _set(updatedAnnotation, 'x', _round(_get(annotation, 'x') * scale, 2));
  if (updatedAnnotation.x > _get(pageDimensions, `${currentPage}.width`)) {
    _set(updatedAnnotation, 'x', 0);
    _set(updatedAnnotation, 'y', 0);
  }

  let yFromPageTop = _round(_get(annotation, 'y') * scale, 2);
  if (yFromPageTop > _get(pageDimensions, `${currentPage}.height`)) {
    yFromPageTop = 0;
    _set(updatedAnnotation, 'x', 0);
  }
  _set(
    updatedAnnotation,
    'y',
    getHeightTillPage(pageDimensions, currentPage - 1) +
      (currentPage - 1) * PAGE_MARGINS +
      currentPage * PAGE_BORDERS +
      yFromPageTop
  );

  if (updatedAnnotation.y > getHeightTillPage(pageDimensions, currentPage)) {
    _set(
      updatedAnnotation,
      'y',
      getHeightTillPage(pageDimensions, currentPage - 1) + (currentPage - 1) * PAGE_MARGINS + currentPage * PAGE_BORDERS
    );
  }
  return resetTimestampKey(updatedAnnotation);
};

export const getUpdatedAnnotatedFields = ({
  annotatedFields,
  selectedAnnotations,
  annotationsCount,
  pageDimensions,
  formScrollTop,
  formScale,
}) => {
  let updatedAnnotatedFields = { ...annotatedFields };
  let formFieldsCount = annotationsCount;
  const { annotations, configurations: configAnnotations } = selectedAnnotations;
  updatedAnnotatedFields = _reduce(
    annotations,
    (acc, curr) => {
      const id = uuid();
      formFieldsCount += 1;
      return {
        ...acc,
        [id]: getAnnotation(curr, id, formFieldsCount, pageDimensions, formScrollTop, formScale),
      };
    },
    updatedAnnotatedFields
  );

  const configurations = {
    configFields: [],
    commonPropertiesForConfigs: {},
  };
  _forEach(configAnnotations, ({ configData, commonProperties }) => {
    _set(configurations, 'configFields', _concat(_get(configurations, 'configFields'), configData));
    _set(configurations, `commonPropertiesForConfigs.${_get(configData, 'configId')}`, commonProperties);
  });

  return {
    formFieldsCount,
    updatedAnnotatedFields,
    ...configurations,
  };
};

export const isValidAnnotationFields = selectedAnnotatedFields =>
  _has(selectedAnnotatedFields, 'annotations') && _has(selectedAnnotatedFields, 'configurations');

// returns the offsets and top(y), left(x) positions of the field for the offsets tab in modal
export const getOffsetsAndPositions = (annotationField, formScale, pageDimensions) => {
  const { offsets = {} } = annotationField || {};
  const { x: left, y: top } = getAnnotationCoordinates(annotationField, pageDimensions, formScale);
  return {
    ...offsets,
    top,
    left,
  };
};

export const getAnnotationCoordinatesFromTop = (x, yFromPageTop, page, pageDimensions, formScale) => {
  const scale = formScale / DEFAULT_SCALE;
  return {
    x: _round(x * scale, 2),
    y:
      getHeightTillPage(pageDimensions, page - 1) +
      (page - 1) * PAGE_MARGINS +
      (page - 1) * PAGE_BORDERS +
      yFromPageTop * scale,
  };
};

export const getNewAnnotationDetails = (
  fieldProperties,
  annotatedFields,
  pageDimensions,
  totalPages,
  formScrollTop,
  formScale
) => {
  if (_get(fieldProperties, 'x') && _get(fieldProperties, 'y')) {
    const currentPageNumber = getPageNumber(formScrollTop, pageDimensions);
    return {
      width: DEFAULT_FIELD_WIDTH * formScale,
      height: DEFAULT_FIELD_HEIGHT * formScale,
      page: currentPageNumber,
      ...fieldProperties,
      ...getAnnotationCoordinatesFromTop(
        _get(fieldProperties, 'x'),
        _get(fieldProperties, 'y'),
        currentPageNumber,
        pageDimensions,
        formScale
      ),
    };
  }
  const { x, y, ...rest } = getFieldPositions(annotatedFields, pageDimensions, totalPages, formScrollTop, formScale);
  return { ...rest, ...fieldProperties, x, y };
};

export const getGlobalFieldsToDisplay = globalFields =>
  _sortBy(
    _map(globalFields, field => ({
      ...field,
      globalAnnotationList: _sortBy(field.globalAnnotationList, o => o.fieldName),
    })),
    o => o.categoryDisplayName
  );

export const getSupportedGlobalFields = (globalFields, supportedGlobalFieldCategories) => {
  let supportedGlobalFields = {};
  if (!_isEmpty(supportedGlobalFieldCategories)) {
    _forEach(supportedGlobalFieldCategories, globalFieldCategory => {
      const category = _get(globalFieldCategory, 'category');
      const supportedAnnotations = _get(globalFieldCategory, 'annotations');
      const fieldCategory = _get(globalFields, category);
      supportedGlobalFields[category] = {
        categoryDisplayName: _get(fieldCategory, 'categoryDisplayName'),
        globalAnnotationList: _filter(_get(fieldCategory, 'globalAnnotationList'), annotation =>
          _includes(supportedAnnotations, _get(annotation, 'fieldAnnotation'))
        ),
      };
    });
  } else {
    supportedGlobalFields = globalFields;
  }
  return _values(supportedGlobalFields);
};

export const getSupportedFieldTypeOptions = (fieldTypeOptions, supportedFieldTypes) => {
  if (_isEmpty(supportedFieldTypes)) return fieldTypeOptions;
  return _filter(fieldTypeOptions, fieldTypeOption => _includes(supportedFieldTypes, _get(fieldTypeOption, 'value')));
};

export const getUpdatedFormulatAffectedFields = (annotatedFields, selectedFields, fieldsAffectedByFieldAction) => {
  const fieldsAffected = _cloneDeep(fieldsAffectedByFieldAction);
  _forEach(selectedFields, selectedField => {
    const annotationField = _get(annotatedFields, selectedField);
    const dependentsOnThis = FormFormulaManager.getFieldsDependentOnField(`f${annotationField.order}`);
    FormFormulaManager.removeField(`f${annotationField.order}`);
    fieldsAffected[`f${annotationField.order}`] = dependentsOnThis;
  });
  return {
    fieldsAffectedByFieldAction: fieldsAffected,
    fieldsAffectedAsArray: _uniq(_flatten(_map(_keys(fieldsAffected), key => _values(fieldsAffected[key])))),
  };
};

// Moves the field to 0,0 of page 1 if the field is beyond its page.
export const getFieldsWithUpdatedPosition = (annotatedFields, totalPages, pageDimensions) => {
  const updatedFields = _cloneDeep(annotatedFields);
  _forEach(_keys(updatedFields), fieldId => {
    const field = updatedFields[fieldId];
    if (
      field.page > totalPages ||
      field.y >
        getHeightTillPage(pageDimensions, field.page) + (field.page - 1) * PAGE_MARGINS + field.page * PAGE_BORDERS ||
      field.x > _get(pageDimensions, `${field.page}.width`)
    ) {
      _set(updatedFields, `${fieldId}.x`, 0);
      _set(updatedFields, `${fieldId}.y`, 0);
      _set(updatedFields, `${fieldId}.page`, 1);
    }
  });
  return updatedFields;
};

export const getSaveAnnotationsPayload = (
  {
    annotatedFields,
    commonPropertiesForConfigs,
    configFields,
    formInfo,
    formPrinterType,
    formScale,
    globalFormName,
    hideTabs,
    mediaId,
    pageDimensions,
    sectionDetails,
    totalPages,
  },
  getDateTimeFormatValue
) => ({
  mediaId,
  formDisplayKey: globalFormName,
  originalFileName: _replace(_get(formInfo, 'name', DEFAULT_PDF_NAME), '.pdf', ''),
  formPrinterType,
  ...getAllAnnotationPayload(
    {
      annotatedFields,
      pageDimensions,
      formScale,
      configData: {
        configFields,
        commonPropertiesForConfigs,
      },
      hideTabs,
    },
    getDateTimeFormatValue
  ),
  effectiveDate: getUnix(startOfDay(getCurrentTime())),
  expiryDate: getUnix(endOfDay(nextday())),
  fieldDisplaySections: sectionDetails,
  pageCount: totalPages,
  pdfLibraryPdfKey: _get(formInfo, 'pdfLibraryPdfKey') || null,
});

export const getFormattedFormId = formId =>
  _endsWith(_toUpper(formId), '_TMP') ? _toString(formId).substring(0, _size(formId) - _size('_TMP')) : formId;

export const getInputLanguageOptions = (languages, currentLanguageId, id, value) =>
  produce(languages, draft => {
    _set(draft, ['locale', currentLanguageId, id], value);
  });

export const getDealerNameOptions = dealers =>
  _map(dealers, ({ dealerName, id }) => ({
    label: dealerName,
    value: id,
  }));

export const getDealerSearchPayload = (searchText, env) => ({
  searchText,
  filters: [
    {
      field: 'env',
      operator: FILTER_OPERATORS.IN,
      values: [env],
    },
  ],
  pageInfo: {
    start: 0,
    rows: 20,
    current: 1,
  },
  sort: [],
});

export const getFormListPayload = (searchText = '', page, itemsPerPage, sort, selectedFilters) => {
  const pageInfo = {
    start: page * itemsPerPage,
    rows: itemsPerPage,
  };

  return {
    searchText,
    pageInfo,
    sort,
    filters: selectedFilters,
  };
};

export const disableFieldBasedOnPermission = ({ disableActions = false, isFormsLibrary, editType }) => {
  if (isFormsLibrary || editType === EDIT_TYPE_PERMISSIONS.GLOBAL_EDIT) return disableActions;
  if (
    editType === EDIT_TYPE_PERMISSIONS.TEKION_RESTRICTED_EDIT ||
    editType === EDIT_TYPE_PERMISSIONS.DP_RESTRICTED_EDIT
  ) {
    return true;
  }
  return true;
};

export const getDefaultFieldGeneral = () => ({
  delimiter: DEFAULT_FIELD_STATE_GENERAL.delimiter,
});

export const uniqueConfigFields = configFields => _uniqBy(configFields, 'configId');

export const getSelectedFieldProperties = (selectedField, order) => {
  const fieldType = _get(selectedField, 'type');
  const fieldAnnotation = _get(selectedField, 'fieldAnnotation');
  return {
    ...selectedField,
    fieldAnnotation: fieldType === FIELD_TYPE_VALUES.TIMESTAMP ? `${fieldAnnotation}_${order}` : fieldAnnotation,
  };
};

export const isTimestampAnnotation = annotation =>
  _head(_castArray(_get(annotation, 'general.type'))) === FIELD_TYPE_VALUES.TIMESTAMP;

export const isSignatureAnnotation = annotation =>
  _some(
    _values(SIGNATURE_TYPES),
    signatureType =>
      _some(_castArray(_get(annotation, 'general.name')), sign => _includes(sign, signatureType)) ||
      _some(_castArray(_get(annotation, 'general.name.value')), sign => _includes(sign, signatureType))
  ) && !isTimestampAnnotation(annotation);

export const getSignatureAnnotationsList = annotations =>
  _filter(_values(annotations), annotation => isSignatureAnnotation(annotation));

const getTimestampAnnotationValue = annotation => `${_get(annotation, 'fieldAnnotation')}_${_get(annotation, 'order')}`;

const isTimestampAnnotationUsedAlready = (signatureAnnotations, timeStampAnnotation, selectedField) =>
  !_some(
    signatureAnnotations,
    signatureAnnotation =>
      _get(signatureAnnotation, 'general.timeStampKey') === getTimestampAnnotationValue(timeStampAnnotation)
  ) || _get(selectedField, 'general.timeStampKey') === getTimestampAnnotationValue(timeStampAnnotation);

const getFilteredSignatureAnnotations = (annotations, selectedField) =>
  _filter(annotations, annotation => _get(annotation, 'id') !== _get(selectedField, 'id'));

export const getTimeStampAnnotationsList = (annotations, selectedField = {}) => {
  const signatureAnnotations = getFilteredSignatureAnnotations(getSignatureAnnotationsList(annotations), selectedField);
  const timestampAnnotations = _filter(_values(annotations), annotation => isTimestampAnnotation(annotation));
  return _sortBy(
    _filter(timestampAnnotations, timeStampAnnotation =>
      isTimestampAnnotationUsedAlready(signatureAnnotations, timeStampAnnotation, selectedField)
    ),
    ['order']
  );
};

export const getTimeStampAnnotationOptions = (annotations, selectedField) =>
  _map(getTimeStampAnnotationsList(annotations, selectedField), annotation => ({
    label: `${_get(annotation, 'order')}. ${_get(annotation, 'fieldName')}`,
    value: getTimestampAnnotationValue(annotation),
  }));

export const resetTimestampKey = annotation =>
  produce(annotation, draft => {
    _set(draft, 'general.timeStampKey', null);
  });

const isTimestampAnnotationForSignature = (timestampKey, annotation) =>
  _head(_castArray(_get(annotation, 'general.type'))) === FIELD_TYPE_VALUES.TIMESTAMP &&
  timestampKey === getTimestampAnnotationValue(annotation);

export const getTimestampAnnotationFromSignature = (signatureAnnotation, annotations) => {
  const timestampKey = _get(signatureAnnotation, 'general.timeStampKey');
  let signAndTimestampAnnotations = [signatureAnnotation];
  if (timestampKey)
    signAndTimestampAnnotations = _concat(
      signAndTimestampAnnotations,
      _find(_values(annotations), annotation => isTimestampAnnotationForSignature(timestampKey, annotation))
    );
  return _compact(signAndTimestampAnnotations);
};

export const getIdsFromAnnotations = annotations => _map(annotations, annotation => _get(annotation, 'id'));

export const getFieldsAlongWithTimestampAnnotation = (selecetdFields, annotations) => {
  const selectedAnnotations = _values(_pick(annotations, selecetdFields));
  return _uniq(
    _reduce(
      selectedAnnotations,
      (acc, curr) => [...acc, ...getIdsFromAnnotations(getTimestampAnnotationFromSignature(curr, annotations))],
      []
    )
  );
};

export const showFormPrintertypeModal = ({ creatingForm, formPrinterType, formInfo, config }) =>
  creatingForm &&
  _isEmpty(configReader.formAnnotationDetails(config)) &&
  !_get(formInfo, 'pdfLibraryPdfKey') &&
  !formPrinterType;

export const getValueBasedOnDelta = (updatedValue, previousValue = 0) => {
  const delta = Math.abs(updatedValue - previousValue);

  if (delta > DELTA_CONSTANT) {
    return updatedValue;
  }

  return previousValue;
};

export const calculateAnnotationFieldPositions = ({ annotation, pageDimensions, formScale }) => {
  const page = _get(annotation, 'page', 1);
  const y = _get(annotation, 'y', 0);
  const _height = _get(annotation, 'height', 0);
  const scale = DEFAULT_SCALE / formScale;

  const previousLowerLeftX = _get(annotation, 'general.lowerLeftX');
  const previousLowerLeftY = _get(annotation, 'general.lowerLeftY');
  const previousHeight = _get(annotation, 'actualHeight');
  const previousWidth = _get(annotation, 'actualWidth');

  const calculatedLowerLeftX = _round(getPixelInNumber(_get(annotation, 'x', 0)) * scale, 2);
  const calculatedLowerLeftY = _round(getRelativeY(y, page, pageDimensions, _height) * scale, 2);
  const calculatedWidth = _round(getPixelInNumber(_get(annotation, 'width', 0)) * scale, 2);
  const calculatedHeight = _round(getPixelInNumber(_height) * scale, 2);

  return {
    width: getValueBasedOnDelta(calculatedWidth, previousWidth),
    height: getValueBasedOnDelta(calculatedHeight, previousHeight),
    lowerLeftX: getValueBasedOnDelta(calculatedLowerLeftX, previousLowerLeftX),
    lowerLeftY: getValueBasedOnDelta(calculatedLowerLeftY, previousLowerLeftY),
  };
};

export const pxToMm = pixels => {
  // Handle null, undefined, and NaN inputs
  if (_isNil(pixels) || _isNaN(_toNumber(pixels))) {
    return null;
  }

  const numericPixels = _toNumber(pixels);
  const calculatedValue = numericPixels * PTS_TO_MM_CONSTANT;

  return _round(calculatedValue, 2);
};

export const mmToPx = mm => {
  // Handle null, undefined, and NaN inputs
  if (_isNil(mm) || _isNaN(_toNumber(mm))) {
    return null;
  }

  const numericMm = _toNumber(mm);
  const calculatedValue = numericMm / PTS_TO_MM_CONSTANT;

  return calculatedValue;
};
