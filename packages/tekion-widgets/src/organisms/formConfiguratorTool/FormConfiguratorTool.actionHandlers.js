import _get from 'lodash/get';
import _map from 'lodash/map';
import _findIndex from 'lodash/findIndex';
import _flatten from 'lodash/flatten';
import _isEmpty from 'lodash/isEmpty';
import _values from 'lodash/values';
import _forEach from 'lodash/forEach';
import _replace from 'lodash/replace';
import _keys from 'lodash/keys';
import _flattenDeep from 'lodash/flattenDeep';
import _unset from 'lodash/unset';
import _endsWith from 'lodash/endsWith';
import _round from 'lodash/round';
import _size from 'lodash/size';
import _uniq from 'lodash/uniq';
import _compact from 'lodash/compact';
import _trim from 'lodash/trim';
import _set from 'lodash/set';
import _filter from 'lodash/filter';
import _concat from 'lodash/concat';
import _slice from 'lodash/slice';
import _omit from 'lodash/omit';
import _reduce from 'lodash/reduce';
import _toNumber from 'lodash/toNumber';
import _isNumber from 'lodash/isNumber';
import _last from 'lodash/last';
import _split from 'lodash/split';
import { arrayMove } from 'react-sortable-hoc';
import _pick from 'lodash/pick';
import _has from 'lodash/has';
import _noop from 'lodash/noop';

import GlobalAnalytics from '@tekion/tekion-base/utils/GlobalAnalytics';
import { EMPTY_OBJECT, EMPTY_ARRAY, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { FORM_SOURCE } from '@tekion/tekion-base/constants/deal/formSetup';
import { setValueInSessionStorage } from '@tekion/tekion-base/utils/sessionStorage';
import {
  addIdToDealerInformation,
  getDefaultSearchPayload,
  getFormInfoFromSelectedPdf,
  getFormPrinterType,
} from '@tekion/tekion-business/src/utils/formConfigurator/formConfigurator.utils';
import { getIsVisibilityAcrossModulesEnabled } from '@tekion/tekion-business/src/readers/core/FormSetup.reader';
import { uuid } from '@tekion/tekion-components/src/utils';
import { downloadURI } from '@tekion/tekion-components/src/utils/downloadFile';
import { getSignedURLs } from '@tekion/tekion-business/src/services/mediaV3';
import { toaster, TOASTER_TYPE, dismissToast } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import { nextday, getCurrentTime, getUnix, endOfDay, startOfDay } from '@tekion/tekion-base/utils/dateUtils';
import { DEFAULT_PAGE_SIZE } from '@tekion/tekion-base/constants/tableConstants';
import { MESSAGES, TOASTER_STATUS } from './constants/pages';

import { getUsageTargetParamsFromPayload } from '../AddForm/AddForm.utils';
import { FORM_FIELD } from '../AddForm/AddForm.constants';
import ROUTES from './constants/routes';
import { getUserInputFieldsList } from '../../utils/formsPreview';
import { FORM_STATE } from './constants/formConfigurator';
import * as ValidateFormUtils from './utils/validateForm.utils';
import { getAPIError } from '../../appServices/sales/readers/error.reader';
import ACTION_TYPES from './FormConfiguratorTool.actionTypes';
import FormAnnotationManager, {
  AddField,
  DeleteFields,
  PasteAnnotations,
  UpdateField,
  UpdateSelectedFields,
} from './FormAnnotationManager';
import {
  FIELD_TYPES,
  PAGE_TYPES,
  DEFAULT_PDF_NAME,
  DEFAULT_FIELD_STATE_OFFSET,
  DEFAULT_SCALE,
  DEFAULT_FIELD_WIDTH,
  DEFAULT_FIELD_HEIGHT,
  DEFAULT_FIELD_STATE_AFFIX,
  CHARACTER_LIMIT_FOR_COMMENTS,
} from './FormConfiguratorTool.constants';
import formConfiguratorToolAPI from './FormConfiguratorTool.api';
import {
  getAnnotationDetailsFromResponse,
  getAllAnnotationPayload,
  getFieldDisplayName,
  getFieldPositions,
  getFormSetupDetailsFromPayload,
  getListOfProducts,
  getGeneralProperties,
  getDefaultFieldTextFormatting,
  updateAnnotationsByScale,
  isSupportedFileName,
  updateCommonProperties,
  getScaledPageDimensions,
  getConfigAnnotationList,
  removeUnwantedConfigAnnotations,
  getFieldsWithDateValuesInTimestamp,
  getSelectedFieldsString,
  getUpdatedAnnotatedFields,
  updateAnnotationsByPrinterType,
  getNewAnnotationDetails,
  getGlobalFieldsToDisplay,
  getSupportedGlobalFields,
  getUpdatedFormulatAffectedFields,
  getFieldsWithUpdatedPosition,
  getSaveAnnotationsPayload,
  getDealerSearchPayload,
  getFormListPayload,
  uniqueConfigFields,
  resetTimestampKey,
  showFormPrintertypeModal,
} from './FormConfiguratorTool.utils';

import { checkIsConfigField, checkIsCustomField } from './components/FieldPropertiesForm/FieldPropertiesForm.utils';
import { getNewFormPayload } from './utils/FormConfigurator.utils';
import FormFormulaManager from './FormFormulaManager';
import { FIELD_DEFAULT_STATE_VALUES } from './components/CustomFieldGeneralForm/CustomFieldGeneralForm.constants';
import ConfigReader from './readers/config.reader';
import { getDealerCountryCode } from './utils/dealer.utils';

const handlePageMount = async (action, { getState, setState }) => {
  GlobalAnalytics.sendSubAppLoadingEvent({ subAppName: 'FormConfiguratorTool' });
  const {
    payload: { pageType },
  } = action;
  const {
    initializeApis = _noop,
    getFNIProducts = _noop,
    fetchDueBills = _noop,
    getViSettings = _noop,
    fetchStateListFromCountryCode = _noop,
    fetchDealerSites = _noop,
    fetchGlobalMetaData = _noop,
  } = getState();

  initializeApis();
  setState({ pageType });

  const dealerCountryCode = getDealerCountryCode();
  await Promise.all([
    fetchDueBills(),
    getFNIProducts(),
    getViSettings(),
    fetchDealerSites(),
    fetchStateListFromCountryCode(dealerCountryCode),
    fetchGlobalMetaData(),
  ]);
  const { dueBills, fniOptions } = getState();
  const formMetaDataResponse = _get(await formConfiguratorToolAPI.getFormsMetaData(), 'response');

  if (!_isEmpty(formMetaDataResponse) || !_isEmpty(dueBills) || !_isEmpty(fniOptions)) {
    setState({
      metaData: {
        ...formMetaDataResponse,
        dueBills,
      },
      productsList: getListOfProducts(fniOptions),
    });
  }
  setState({ isLoading: false });
  GlobalAnalytics.sendSubAppLoadedEvent({ subAppName: 'FormConfiguratorTool' });
};

const handleFetchDocumentTypes = async (action, { getState }) => {
  const { initializeAddForm, isDealerTrackEnabled } = getState();
  await initializeAddForm(isDealerTrackEnabled);
};

const fetchDocumentSetupSettings = async () => {
  const formSetupSettings = await formConfiguratorToolAPI.fetchDocumentSetupSetings();
  const visibilityAcrossModulesEnabled = getIsVisibilityAcrossModulesEnabled(formSetupSettings);
  return visibilityAcrossModulesEnabled;
};

const handleSetupScreenMount = async (action, { setState, getState }) => {
  GlobalAnalytics.sendSubAppInitializingEvent({ subAppName: 'FormConfiguratorToolSetupScreen' });
  const { setupDataAvailable } = getState();
  if (setupDataAvailable) return;

  const { initializeAddForm, fetchSalesSetupInfo, isDealerTrackEnabled } = getState();

  setState({ isLoadingSetup: true });

  await initializeAddForm(isDealerTrackEnabled);
  const visibilityAcrossModulesEnabled = await fetchDocumentSetupSettings();
  if (fetchSalesSetupInfo) {
    fetchSalesSetupInfo();
  }

  const { params, pageType, showFormsPortal, showEnterpriseView } = getState();
  const formId = _get(params, 'formId');
  try {
    const formSetupDetailsResponse = await formConfiguratorToolAPI.getFormSetupDetails(
      formId,
      showFormsPortal,
      showEnterpriseView
    );
    const {
      response: { hits },
    } = formSetupDetailsResponse;

    const formSetupDetails = _get(hits, '[0]', null);

    // const digiCertData = _get(formSetupDetails, 'digicertStakeHolderEncryption');
    const digiCertEncryptionEnabled = _get(formSetupDetails, 'digiCertEncryptionEnabled');
    // const defaultCheckedDigiCertForm = !_isEmpty(digiCertData);

    let formPrinterType = EMPTY_STRING;

    if (!_isEmpty(formId)) {
      const responseObject = await formConfiguratorToolAPI.getFormDetails(formId);
      const formsResponse = _get(responseObject, 'response');
      formPrinterType = _get(formsResponse, 'formPrinterType', EMPTY_STRING);

      let formInfo = null;
      let formDisplayKey = '';
      let mediaId = '';
      const { response: labels } = await formConfiguratorToolAPI.getFormLabels();

      if (labels) {
        setState({
          allLabels: _get(labels, 'allLabels') || EMPTY_ARRAY,
          recentLabels: _get(labels, 'recentLabels') || EMPTY_ARRAY,
        });
      } else {
        toaster(TOASTER_STATUS.ERROR, MESSAGES.FAILED_TO_FETCH_LABELS);
      }

      if (!_isEmpty(formsResponse)) {
        mediaId = _get(formsResponse, 'mediaId');
        const originalFileName = _get(formsResponse, 'originalFileName');
        formDisplayKey = _get(formsResponse, 'formDisplayKey');
        formInfo = {
          name: `${originalFileName}.pdf`,
          formDisplayKey,
        };
        if (mediaId) {
          const signedUrlResponse = await getSignedURLs([mediaId]);
          const url = _get(signedUrlResponse, '0.normal.url');
          formInfo.mediaId = mediaId;
          formInfo.url = url;
        }
      }

      if (pageType === PAGE_TYPES.SETUP) {
        setState({
          mediaId,
          globalFormName: formDisplayKey || DEFAULT_PDF_NAME,
          formDetails: formsResponse,
          dealerPdfKey: _get(formsResponse, 'dealerPdfKey'),
          formInfo,
          formId,
          formPrinterType,
        });
      }
    }

    let pdfMetadata = EMPTY_OBJECT;
    const { pdfLibraryPdfKey } = formSetupDetails;
    if (pdfLibraryPdfKey) {
      pdfMetadata = await formConfiguratorToolAPI.getPDFMetadataFromPDFLibrary(pdfLibraryPdfKey);
    }

    if (pageType === PAGE_TYPES.SETUP) {
      setState({
        formValues: {
          ...(formSetupDetails ? getFormSetupDetailsFromPayload(formSetupDetails, pdfMetadata) : {}),
          digiCertEncryptionEnabled,
        },
        setupDataAvailable: true,
        isEditing: _get(formSetupDetails, 'formState', null) === 'PUBLISHED',
        isCreating: _get(formSetupDetails, 'formState', null) !== 'PUBLISHED',
        showSaveAsNew: _endsWith(formId, '_TMP'),
        formPrinterType,
        visibilityAcrossModulesEnabled,
        isPublishedForm: _get(formSetupDetails, 'department') && _get(formSetupDetails, 'printerType'),
        // digiCertFormEnabled: defaultCheckedDigiCertForm,
      });
    }
  } catch {
    toaster(TOASTER_TYPE.ERROR, __('Error loading setup details'));
  } finally {
    setState({ isLoadingSetup: false });
    GlobalAnalytics.sendSubAppIntializedEvent({ subAppName: 'FormConfiguratorToolSetupScreen' });
  }
};

const handleAgreementScreenMount = (_, { setState }) => {
  setState({
    globalFormName: __('New Form Agreement'),
  });
};

const handlePrepareScreenMount = async (action, { getState, setState }) => {
  GlobalAnalytics.sendSubAppInitializingEvent({ subAppName: 'FormConfiguratorToolPrepareScreen' });
  setState({ isLoadingPrepare: true });
  resetFormInfo(setState);
  const { params, fetchSalesSetupInfo, config, showFormsPortal, showEnterpriseView } = getState();
  const supportedGlobalFieldCategories = ConfigReader.supportedGlobalFieldCategories(config);
  const formId = _get(params, 'formId');

  if (fetchSalesSetupInfo) {
    fetchSalesSetupInfo();
  }
  const promises = [formConfiguratorToolAPI.getConfiguratorMetadata(), formConfiguratorToolAPI.fetchGlobalFields()];

  if (!_isEmpty(formId)) {
    promises.push(
      formConfiguratorToolAPI.getFormDetails(formId),
      formConfiguratorToolAPI.getFormSetupDetails(formId, showFormsPortal, showEnterpriseView)
    );
  }
  const responses = _map(await Promise.all(promises), responseObject => _get(responseObject, 'response'));
  const [configuratorMetaData, globalFieldsResponse, formsResponse, formSetupDetailsResponse] = responses;

  let formInfo = null;
  let formDisplayKey = '';
  const selectedFormInfo = ConfigReader.selectedFormInfo(config);
  let formAnnotationDetails = formsResponse;
  if (!_isEmpty(ConfigReader.formAnnotationDetails(config)))
    formAnnotationDetails = ConfigReader.formAnnotationDetails(config);

  if (!_isEmpty(formAnnotationDetails) || selectedFormInfo) {
    let info = null;
    if (!_isEmpty(formAnnotationDetails)) info = formAnnotationDetails;
    else info = selectedFormInfo;
    const mediaId = _get(info, 'mediaId');
    const originalFileName = _get(info, 'originalFileName');
    formDisplayKey = _get(info, 'formDisplayKey');
    formInfo = {
      name: `${originalFileName}.pdf`,
      formDisplayKey,
    };
    if (mediaId) {
      const signedUrlResponse = await getSignedURLs([mediaId]);
      const url = _get(signedUrlResponse, '0.normal.url');
      formInfo.mediaId = mediaId;
      formInfo.url = url;
    }
  } else if (_isEmpty(formAnnotationDetails) && formId && _isEmpty(selectedFormInfo)) {
    toaster(TOASTER_TYPE.ERROR, __('Unable to load PDF'));
  }

  const _globalFields = getSupportedGlobalFields(
    _get(globalFieldsResponse, 'globalFields'),
    supportedGlobalFieldCategories
  );
  const globalFields = getGlobalFieldsToDisplay(_globalFields);

  const globalFieldsAsOptions = _flatten(
    _map(globalFields, globalFieldCategory =>
      _map(globalFieldCategory.globalAnnotationList, field => ({
        label: field.fieldAnnotation,
        value: field.fieldAnnotation,
        type: field.type || 'FREE_TEXT',
        id: field.id,
        isGlobalField: true,
      }))
    )
  );

  const formSetupDetails = _get(formSetupDetailsResponse, 'hits[0]', EMPTY_OBJECT);

  let pdfMetadata = EMPTY_OBJECT;
  const { pdfLibraryPdfKey } = formSetupDetails;
  if (pdfLibraryPdfKey) {
    pdfMetadata = await formConfiguratorToolAPI.getPDFMetadataFromPDFLibrary(pdfLibraryPdfKey);
  }

  if (!_isEmpty(responses)) {
    setState({
      globalFields,
      allGlobalFields: globalFieldsAsOptions,
      globalFormName: formDisplayKey || DEFAULT_PDF_NAME,
      formState: _get(formAnnotationDetails, 'formState', FORM_STATE.DRAFT),
      isEdit: true,
      isLoading: false,
      formId,
      formDetails: formAnnotationDetails,
      formInfo,
      configuratorMetaData,
      isEditing: Boolean(formId),
      isCreating: Boolean(!formId),
      pdfLoaded: false,
      formPrinterType: _get(formAnnotationDetails, 'formPrinterType'),
      sectionDetails: _get(formAnnotationDetails, 'fieldDisplaySections', EMPTY_ARRAY),
      enableFormsPreview: false,
      hideFormPrinterTypeSelection: !!_get(formSetupDetails, 'pdfLibraryPdfKey'),
      formValues: !_isEmpty(formSetupDetails) ? getFormSetupDetailsFromPayload(formSetupDetails, pdfMetadata) : {},
      isPublishedForm: _get(formSetupDetails, 'department') && _get(formSetupDetails, 'printerType'),
    });
  }
  setState(prevState => ({ isLoadingPrepare: false, restoreFormInfoForEditForm: prevState.formInfo }));
  GlobalAnalytics.sendSubAppIntializedEvent({ subAppName: 'FormConfiguratorToolPrepareScreen' });
};

const handleFormNameChange = async (action, { setState }) => {
  const {
    payload: { value },
  } = action;

  if (isSupportedFileName(value)) {
    setState({
      globalFormName: _trim(value),
      isValidFormName: true,
      isEditingFormName: false,
    });
  } else {
    setState({ isValidFormName: false });
  }
};

const handleGlobalFieldSearch = (action, { setState }) => {
  const {
    payload: { searchText },
  } = action;
  setState({ gloablFieldSearchText: searchText });
};

const handleAnnotatedFieldSearch = (action, { setState }) => {
  const {
    payload: { searchText },
  } = action;
  setState({ gloablFieldSearchText: searchText });
};

const handleToggleSingleAnnotatedField = (action, { setState }) => {
  const {
    payload: { id },
  } = action;
  setState({
    selectedFields: FormAnnotationManager.onBulkSelectFields([id]),
  });
  updateUserActions(setState);
};

const handleToggleBulkAnnotatedFields = (action, { setState }) => {
  const {
    payload: { ids = [] },
  } = action;
  setState({
    selectedFields: FormAnnotationManager.onBulkSelectFields(ids),
  });
  updateUserActions(setState);
};

const handlePrepareSetupScreenToggle = (action, { getState, setState }) => {
  const {
    payload: { formId },
  } = action;
  const { pageType, navigate } = getState();
  const nextPage = pageType === PAGE_TYPES.PREPARE ? PAGE_TYPES.SETUP : PAGE_TYPES.PREPARE;
  if (nextPage === PAGE_TYPES.SETUP) {
    navigate(`${ROUTES.FORM_CONFIGURATOR_TOOL}/${formId}`);
  }
  window.location.hash = `#pageType=${nextPage}`;
  setState({ pageType: nextPage });
};

const navigateToFormsList = (action, { getState }) => {
  const { navigate } = getState();
  navigate(ROUTES.FORM_CONFIGURATOR);
};

const handleSubmitFormSetup = async (action, { setState, getState }) => {
  const { payload: actionPayload = {} } = action;
  const { submitAsNew, dynamicUsageCategoryVsRules } = actionPayload;
  const {
    formValues,
    usageTargetParams,
    fniOptions,
    navigate,
    dealerPdfKey,
    mediaId,
    globalFormName,
    saveAsNewName,
    makes,
    saveAsNewReasonForPublish,
    saveWithReasonForPublish,
    dueBills,
    formPrinterType,
    econtractForm,
    moduleVisibilityRules,
    metaData,
  } = getState();

  setState({ isFormSubmitInProgress: true });

  if (submitAsNew) {
    if (!_isEmpty(saveAsNewName) && _size(saveAsNewName) >= 4 && isSupportedFileName(saveAsNewName)) {
      setState({ isValidFormName: true });
    } else {
      setState({ isValidFormName: false, isFormSubmitInProgress: false });
      return;
    }
  }

  if (submitAsNew && _size(saveAsNewReasonForPublish) > CHARACTER_LIMIT_FOR_COMMENTS) {
    setState({ saveAsNewReasonForPublishErrorMessage: __('Character limit exceeded'), isFormSubmitInProgress: false });
    return;
  }

  if (!submitAsNew && _size(saveWithReasonForPublish) > CHARACTER_LIMIT_FOR_COMMENTS) {
    setState({ saveWithReasonForPublishErrorMessage: __('Character limit exceeded'), isFormSubmitInProgress: false });
    return;
  }

  try {
    const payload = {
      mediaId,
      formDisplayKey: globalFormName,
      formSetupDetails: {
        ...getNewFormPayload({
          formValues,
          usageTargetParams,
          fnis: fniOptions,
          dueBills,
          makes,
          econtractForm,
          moduleVisibilityRules,
          metaData,
          dynamicUsageCategoryVsRules,
        }),
        formPrinterType,
      },
    };

    if (submitAsNew) {
      payload.formDisplayKey = _trim(saveAsNewName);
      payload.saveAsNew = true;
      payload.comment = saveAsNewReasonForPublish;
      payload.formSetupDetails.formSource = FORM_SOURCE.FORM_PRO;
    } else {
      payload.comment = saveWithReasonForPublish;
    }

    const response = await formConfiguratorToolAPI.saveForm(dealerPdfKey, payload);
    if (!_isEmpty(response)) {
      navigate(ROUTES.FORM_CONFIGURATOR);
    } else {
      toaster(TOASTER_TYPE.ERROR, __('Error saving form details'));
    }
  } catch {
    toaster(TOASTER_TYPE.ERROR, __('Error saving form details'));
  }
  setState({ isSaveConfirmationModalVisible: false, isFormSubmitInProgress: false });
};

const handleAcceptAgreement = (action, { setState }) => {
  const {
    payload: { nextPage },
  } = action;
  window.location.hash = `#pageType=${nextPage}`;
  setState({ pageType: nextPage });
};

const handleSaveAnnotations = async (action, { getState, setState }) => {
  const {
    annotatedFields,
    formInfo,
    pageDimensions,
    globalFormName,
    formPrinterType,
    configFields,
    commonPropertiesForConfigs,
    formId,
    formScale,
    hideTabs,
    sectionDetails,
    config,
    getDateTimeFormatValue,
    totalPages,
  } = getState();
  const {
    payload: { mediaId, callback },
  } = action;

  const payload = getSaveAnnotationsPayload(
    {
      annotatedFields,
      commonPropertiesForConfigs,
      configFields,
      formInfo,
      formPrinterType,
      formScale,
      globalFormName,
      hideTabs,
      mediaId,
      pageDimensions,
      sectionDetails,
      totalPages,
    },
    getDateTimeFormatValue
  );

  const onNextClick = ConfigReader.onNextClick(config);
  if (onNextClick) {
    onNextClick(payload);
    return;
  }

  setState({ isSavingAnnotations: true });

  try {
    let response;
    if (formId) {
      const { response: updateResponse } = await formConfiguratorToolAPI.updateAnnotations(formId, payload);
      response = updateResponse;
    } else {
      const { response: saveResponse } = await formConfiguratorToolAPI.saveAnnotations(payload);
      response = saveResponse;
    }
    if (response) {
      setState({
        mediaId: _get(response, 'mediaId'),
        dealerPdfKey: _get(response, 'dealerPdfKey'),
        formDisplayKey: _get(response, 'formDisplayKey'),
        saveAnnotationsResponse: response,
      });
      callback(_get(response, 'dealerPdfKey'));
    } else {
      toaster(TOASTER_TYPE.ERROR, __('Error saving annotation details'));
    }
  } catch {
    toaster(TOASTER_TYPE.ERROR, __('Error saving annotation details'));
  } finally {
    setState({ isSavingAnnotations: false });
  }
};

const setFormInfo = (action, { setState, getState }) => {
  const {
    payload: { formInfo, restoreFormInfo },
  } = action;
  const { isReplaced, isEditing } = getState();
  setState(prevState => ({
    formInfo,
    globalFormName:
      isReplaced || isEditing
        ? prevState.globalFormName
        : _get(formInfo, 'formDisplayKey') || _replace(_get(formInfo, 'name', DEFAULT_PDF_NAME), '.pdf', ''),
    isValidFormName: true,
    restoreFormInfoForCreateForm: restoreFormInfo,
  }));
};

const addFieldToForm = (action, { setState, getState }) => {
  const {
    payload: { selectedField },
  } = action;
  const {
    pageDimensions,
    annotatedFields: annotatedFieldsState,
    totalPages,
    formPrinterType,
    formScale,
    commonPropertiesForConfigs,
  } = getState();
  const formScrollTop = document.querySelector('#formContainer').scrollTop;
  const scale = formScale / DEFAULT_SCALE;
  const configId = _get(selectedField, 'configId', EMPTY_STRING);
  const commonPropertiesForThisConfig = _get(commonPropertiesForConfigs, configId, EMPTY_STRING);
  const newField = {
    ...getFieldPositions(annotatedFieldsState, pageDimensions, totalPages, formScrollTop, formScale),
    width: _round(_get(commonPropertiesForThisConfig, 'width', DEFAULT_FIELD_WIDTH) * scale, 2),
    height: _round(_get(commonPropertiesForThisConfig, 'height', DEFAULT_FIELD_HEIGHT) * scale, 2),
    actualWidth: _get(commonPropertiesForThisConfig, 'width', DEFAULT_FIELD_WIDTH),
    actualHeight: _get(commonPropertiesForThisConfig, 'height', DEFAULT_FIELD_HEIGHT),
    ...selectedField,
    id: uuid(),
    fieldType: selectedField.fieldType || FIELD_TYPES.GLOBAL_FIELD,
    // FOR save API
    general:
      selectedField.fieldType === FIELD_TYPES.CONFIGURATION_FIELD
        ? { ..._get(commonPropertiesForThisConfig, 'general', EMPTY_OBJECT), ...selectedField.general } || null
        : selectedField.general || getGeneralProperties(selectedField),
    textFormat: selectedField.textFormat || getDefaultFieldTextFormatting(formPrinterType),
    offsets: selectedField.offsets || DEFAULT_FIELD_STATE_OFFSET,
    order: selectedField.order || FormAnnotationManager.getAnnotationsCount() + 1,
    affix: selectedField.affix || DEFAULT_FIELD_STATE_AFFIX,
    active: true,
  };
  FormFormulaManager.addField(`f${newField.order}`);
  // FormFormulaManager.printDependencyTree();
  FormAnnotationManager.increaseAnnotationsCount();
  setState({ ...FormAnnotationManager.execute(new AddField(newField, formScale)) });
  updateUserActions(setState);
};

const deleteFieldFromForm = (action, { getState, setState }) => {
  const {
    payload: { selectedFields: selectedField },
  } = action;
  const { formScale, fieldsAffectedByFieldAction = {}, annotatedFields } = getState();
  const annotationField = _get(annotatedFields, selectedField);
  const dependentsOnThis = FormFormulaManager.getFieldsDependentOnField(`f${annotationField.order}`);
  FormFormulaManager.removeField(`f${annotationField.order}`);
  fieldsAffectedByFieldAction[`f${annotationField.order}`] = dependentsOnThis;
  setState({
    ...FormAnnotationManager.execute(new DeleteFields([selectedField], formScale)),
    selectedFields: FormAnnotationManager.removeFieldFromSelectedList(selectedField),

    // map of array of fields which are affected on active/inactive or deletion
    fieldsAffectedByFieldAction,

    // array of unique fields for easy access to affected fields
    fieldsAffectedAsArray: _uniq(
      _flatten(_map(_keys(fieldsAffectedByFieldAction), key => _values(fieldsAffectedByFieldAction[key])))
    ),
  });
  updateUserActions(setState);
};

const duplicateSelectedField = (action, { setState, getState }) => {
  const {
    payload: { selectedField },
  } = action;
  const { formScale } = getState();
  const id = uuid();
  const { x = 0, y = 0 } = selectedField;
  const newField = {
    ...resetTimestampKey(selectedField),
    x: x + 10,
    y: y + 10,
    id,
    order: FormAnnotationManager.getAnnotationsCount() + 1,
  };
  FormFormulaManager.addField(`f${newField.order}`);
  FormAnnotationManager.increaseAnnotationsCount();
  setState({ ...FormAnnotationManager.execute(new AddField(newField, formScale)) });
  updateUserActions(setState);
};

const duplicateMultipleFields = (action, { getState, setState }) => {
  const {
    payload: { selectedFields },
  } = action;
  const { annotatedFields } = getState();

  const selectedAnnotationFields = _values(_pick(annotatedFields, selectedFields));

  _forEach(selectedAnnotationFields, selectedField => {
    duplicateSelectedField({ payload: { selectedField } }, { getState, setState });
  });
};

const onFormFieldSelect = (action, { getState, setState }) => {
  const {
    payload: { field },
  } = action;
  const { hasViewOnlyPermission } = getState();
  if (!hasViewOnlyPermission) {
    setState({ selectedFields: FormAnnotationManager.onSelectField(field) });
    updateUserActions(setState);
  }
};

const onMultipleSelect = (action, { setState }) => {
  const {
    payload: { selectedFields },
  } = action;
  setState({ selectedFields: FormAnnotationManager.onMultipleSelect(selectedFields) });
  updateUserActions(setState);
};

const updateTotalPages = (action, { setState }) => {
  const {
    payload: { totalPages },
  } = action;

  setState(prevState => ({
    totalPages: FormAnnotationManager.setTotalPages(totalPages),
    pageDimensions: {},
    isReplaced: !!prevState.totalPages,
  }));
};

const updateField = (action, { setState, getState }) => {
  const {
    payload: { field, newPosition },
  } = action;
  const { pageDimensions, formScale } = getState();
  setState({
    ...FormAnnotationManager.execute(new UpdateField(field, newPosition, pageDimensions, formScale)),
  });
  updateUserActions(setState);
};

const updateSelectedFields = (action, { setState, getState }) => {
  const {
    payload: { updateType, values },
  } = action;
  const { formScale, pageDimensions } = getState();
  setState({
    ...FormAnnotationManager.execute(new UpdateSelectedFields(updateType, pageDimensions, formScale, values)),
  });
  updateUserActions(setState);
};

const clearSelctedFields = (_, { setState }) => {
  setState({ selectedFields: FormAnnotationManager.clearSelectedFields(), groupSelectedAnnotations: false });
  updateUserActions(setState);
};

const undoLastAction = (_, { getState, setState }) => {
  const { formScale } = getState();
  setState({ ...FormAnnotationManager.undo(formScale) });
  updateUserActions(setState);
};

const redoLastAction = (_, { setState, getState }) => {
  const { formScale } = getState();
  setState({ ...FormAnnotationManager.redo(formScale) });
  updateUserActions(setState);
};

const updateUserActions = setState => {
  setState({
    canPerformUndo: FormAnnotationManager.getUndoStack().length > 0,
    canPerformRedo: FormAnnotationManager.getRedoStack().length > 0,
    canAlignOrMatch: FormAnnotationManager.getSelectedFields().length > 1,
  });
};

const handleAddCongfigurationClick = (action, { setState }) => {
  setState({ isConfigurationFormModalVisible: true, isConfigEdit: false });
};

const handleCloseConfigurationModal = (action, { setState }) => {
  setState({ isConfigurationFormModalVisible: false, isConfigEdit: false, selectedConfig: EMPTY_OBJECT });
};

const handleCloseValidateFormModalClick = (action, { setState }) => {
  setState({ isValidateFormModalVisible: false });
};
const onValidateFormClick = async (action, { setState, getState }) => {
  const {
    formDetails,
    annotatedFields,
    formInfo,
    pageDimensions,
    globalFormName,
    formPrinterType,
    configFields,
    commonPropertiesForConfigs,
    formScale,
    hideTabs,
    sectionDetails,
    getDateTimeFormatValue,
  } = getState();
  const {
    payload: { mediaId },
  } = action;
  const payload = {
    mediaId,
    formDisplayKey: globalFormName,
    originalFileName: _replace(_get(formInfo, 'name', DEFAULT_PDF_NAME), '.pdf', ''),
    formPrinterType,
    ...getAllAnnotationPayload(
      {
        annotatedFields,
        pageDimensions,
        formScale,
        configData: {
          configFields,
          commonPropertiesForConfigs,
        },
        hideTabs,
      },
      getDateTimeFormatValue
    ),
    effectiveDate: getUnix(startOfDay(getCurrentTime())),
    expiryDate: getUnix(endOfDay(nextday())),
    fieldDisplaySections: sectionDetails,
  };
  try {
    const formIdForValidateForm = _get(formDetails, 'dealerPdfKey', null);
    const { response } = await formConfiguratorToolAPI.newPreviewFields(payload);
    if (!_isEmpty(response)) {
      setState({
        isValidateFormModalVisible: true,
        formIdForValidateForm,
        formKey: _get(formDetails, 'dealerPdfKey', null),
        configs: !_isEmpty(response.configs)
          ? ValidateFormUtils.getParsedConfigDataFromFormDetails(response.configs)
          : ValidateFormUtils.addNewConfiguration([]),
        fieldsForValidateForm: !_isEmpty(response.fields)
          ? ValidateFormUtils.addIndexAsIdToFields(response.fields)
          : ValidateFormUtils.addNewRow([]),
        labels: response.labels || EMPTY_ARRAY,
        formDetailsForValidateForm: response,
      });
    }
  } catch {
    toaster(TOASTER_TYPE.ERROR, __('Error in generating preview fields.'));
  }
};
const handleShowSaveConfirmationModal = (action, { setState }) => {
  setState({ isSaveConfirmationModalVisible: true });
};

const handleShowSaveAsNewConfirmationModal = (action, { setState }) => {
  setState({ isSaveAsNewConfirmationModalVisible: true });
};

const handleHideSaveConfirmationModal = (action, { setState }) => {
  setState({ isSaveConfirmationModalVisible: false });
};

const handleHideSaveAsNewConfirmationModal = (action, { setState }) => {
  setState({ isSaveAsNewConfirmationModalVisible: false });
};

const showExitEditorModal = (_, { setState }) => {
  setState({ showExitEditorModal: true });
};

const hideExitEditorModal = (_, { setState }) => {
  setState({ showExitEditorModal: false });
};

const handleConfigurationFormSubmit = (action, { setState, getState }) => {
  const {
    payload: { configurationValue: _configurationValue },
  } = action;

  const { annotatedFields, configFields, commonPropertiesForConfigs } = getState();

  const commonPropertiesForThisConfig = { ..._get(_configurationValue, 'common', EMPTY_OBJECT) };

  const configurationValue = { ..._configurationValue };
  _unset(configurationValue, 'common');

  let configData;
  const id = _get(configurationValue, 'id');
  let configId = _get(configurationValue, 'configId');

  if (id) {
    const updatedField = {
      ...annotatedFields[id],
      ...configurationValue,
    };

    const targetConfigFieldIndex = _findIndex(configFields, field => field.configId === configId);
    const commonProperties = commonPropertiesForConfigs[configId];
    const generalPropertiesForThisConfig = _get(commonPropertiesForThisConfig, 'general', EMPTY_OBJECT);
    const configAnnotationList = getConfigAnnotationList(configId, generalPropertiesForThisConfig, commonProperties);
    let updatedAnnotationFields = { ...annotatedFields };
    _set(updatedAnnotationFields, id, updatedField);
    updatedAnnotationFields = removeUnwantedConfigAnnotations(updatedAnnotationFields, configId, configAnnotationList);

    configData = {
      ...configFields[targetConfigFieldIndex],
      categoryDisplayName: _get(commonPropertiesForThisConfig, 'general.configName'),
      globalAnnotationList: _flattenDeep(configAnnotationList),
    };

    configFields[targetConfigFieldIndex] = configData;

    setState(prevState => ({
      annotatedFields: FormAnnotationManager.setAnnotationFields(updatedAnnotationFields),
      commonPropertiesForConfigs: {
        ...prevState.commonPropertiesForConfigs,
        [configId]: commonPropertiesForThisConfig,
      },
      configFields,
    }));
  } else {
    configId = uuid();
    const generalPropertiesForThisConfig = _get(commonPropertiesForThisConfig, 'general', EMPTY_OBJECT);
    const configAnnotationList = getConfigAnnotationList(configId, generalPropertiesForThisConfig, configurationValue);

    _set(commonPropertiesForThisConfig, 'width', _get(_configurationValue, 'dimensions.width', DEFAULT_FIELD_WIDTH));
    _set(commonPropertiesForThisConfig, 'height', _get(_configurationValue, 'dimensions.height', DEFAULT_FIELD_HEIGHT));
    _set(commonPropertiesForThisConfig, 'affix.prefix', _get(_configurationValue, 'affix.prefix', EMPTY_STRING));
    _set(commonPropertiesForThisConfig, 'affix.suffix', _get(_configurationValue, 'affix.suffix', EMPTY_STRING));

    configData = {
      configId,
      categoryDisplayName: generalPropertiesForThisConfig.configName,
      globalAnnotationList: _flattenDeep(configAnnotationList),
    };
    setState(prevState => ({
      configFields: prevState.configFields ? [...prevState.configFields, configData] : [configData],
      commonPropertiesForConfigs: {
        ...prevState.commonPropertiesForConfigs,
        [configId]: commonPropertiesForThisConfig,
      },
    }));
  }
  setState({
    isConfigurationFormModalVisible: false,
    isConfigEdit: false,
    selectedConfig: EMPTY_OBJECT,
  });
};
const updateGlobalFormName = (action, { setState }) => {
  const {
    payload: { globalFormName },
  } = action;
  setState({ globalFormName });
};

const handleSaveAsNewFormNameChange = (action, { setState }) => {
  const {
    payload: { saveAsNewName },
  } = action;
  setState({ saveAsNewName });
};

const handleFormSetupCommentChange = (action, { setState }) => {
  const {
    payload: { saveWithReasonForPublish, saveAsNewReasonForPublish, isSaveAsNewForm },
  } = action;
  if (isSaveAsNewForm) {
    setState({ saveAsNewReasonForPublish });
    if (!_isEmpty(saveAsNewReasonForPublish) && _size(saveAsNewReasonForPublish) <= CHARACTER_LIMIT_FOR_COMMENTS) {
      setState({ saveAsNewReasonForPublishErrorMessage: EMPTY_STRING });
    }
  } else {
    setState({ saveWithReasonForPublish });
    if (!_isEmpty(saveWithReasonForPublish) && _size(saveWithReasonForPublish) <= CHARACTER_LIMIT_FOR_COMMENTS) {
      setState({ saveWithReasonForPublishErrorMessage: EMPTY_STRING });
    }
  }
};

const handleAddCustomFieldClick = (action, { setState }) => {
  setState({ isFieldFormModalVisible: true, isFieldEdit: false, selectedField: null });
};

const handleFieldPropertiesClick = (action, { setState }) => {
  const {
    payload: { selectedField },
  } = action;
  if (checkIsConfigField(selectedField)) {
    setState({ isConfigurationFormModalVisible: true, selectedConfig: selectedField, isConfigEdit: true });
  } else {
    setState(prevState => ({
      isFieldFormModalVisible: true,
      selectedField,
      isFieldEdit: true,
      annotatedFields: { ...prevState.annotatedFields, [selectedField.id]: selectedField },
    }));
  }
};

const handleCloseFieldFormModal = (action, { setState }) => {
  setState({ isFieldFormModalVisible: false, selectedField: null, isFieldEdit: false });
};

const showCommonPropertiesModal = (_, { setState }) => {
  setState({ isCommonPropertiesModalVisible: true });
};

const hideCommonPropertiesModal = (_, { setState }) => {
  setState({ isCommonPropertiesModalVisible: false });
};

const saveCommonProperties = (action, { getState, setState }) => {
  const {
    payload: { fieldPropertiesValue },
  } = action;
  const { annotatedFields, selectedFields } = getState();
  setState({
    isCommonPropertiesModalVisible: false,
    annotatedFields: FormAnnotationManager.setAnnotationFields(
      updateCommonProperties(annotatedFields, selectedFields, fieldPropertiesValue)
    ),
  });
};

const updatePageDimensions = (action, { setState, getState }) => {
  const {
    payload: { pageDimensions },
  } = action;

  const {
    totalPages,
    formDetails,
    isCreating,
    formScale,
    isReplaced,
    annotatedFields: fieldsAddedBeforeReplace,
    config,
    formInfo,
  } = getState();
  const {
    annotatedFields = {},
    formFieldsCount = 0,
    commonPropertiesForConfigs,
    configFields,
  } = getAnnotationDetailsFromResponse(formDetails, pageDimensions, totalPages, formScale) || {};

  _forEach(_values(annotatedFields), _field => {
    const formulas = _get(_field, 'formula.formulas', []);
    const dependents = [];
    _forEach(formulas, formula => {
      const formulaDependents = _keys(_get(formula, 'formulaMappings', {}));
      dependents.push(...formulaDependents);
    });
    FormFormulaManager.addField(`f${_field.order}`, dependents);
  });

  const fieldsAffectedByFieldAction = {};
  _forEach(_values(annotatedFields), _field => {
    if (!_field.active) {
      const dependentsOnThis = FormFormulaManager.getFieldsDependentOnField(`f${_field.order}`);
      FormFormulaManager.removeField(`f${_field.order}`);
      fieldsAffectedByFieldAction[`f${_field.order}`] = dependentsOnThis;
    }
  });

  let allAnnotatedFields = annotatedFields;
  if (isReplaced) {
    allAnnotatedFields = fieldsAddedBeforeReplace;
  }

  const updatedFields = getFieldsWithUpdatedPosition(allAnnotatedFields, totalPages, pageDimensions);
  let formfieldcount;
  if (isReplaced) {
    formfieldcount = FormAnnotationManager.getAnnotationsCount();
  } else {
    formfieldcount = formFieldsCount;
  }

  const formPrinterType = ConfigReader.formPrinterType(config) || _get(formDetails, 'formPrinterType');
  const isCreateFormModalOpen = showFormPrintertypeModal({
    creatingForm: isCreating,
    formPrinterType,
    formInfo,
    config,
  });

  setState(prevState => ({
    pdfLoaded: true,
    pageDimensions,
    annotatedFields: FormAnnotationManager.setAnnotationFields(updatedFields),
    formFieldsCount: FormAnnotationManager.setAnnotationsCount(formfieldcount),
    isCreateFormModalOpen,
    formPrinterType,
    configFields: isReplaced ? prevState.configFields : configFields,
    commonPropertiesForConfigs: isReplaced ? prevState.commonPropertiesForConfigs : commonPropertiesForConfigs,
    fieldsAffectedByFieldAction,
    fieldsAffectedAsArray: _uniq(
      _flatten(_map(_keys(fieldsAffectedByFieldAction), key => _values(fieldsAffectedByFieldAction[key])))
    ),
    enableFormsPreview: true,
  }));
};

const handleFieldFormSubmit = (action, { setState, getState }) => {
  const {
    payload: { fieldPropertiesValue },
  } = action;
  const { annotatedFields, fieldsAffectedByFieldAction = {} } = getState();
  const _fieldsAffectedByFieldAction = { ...fieldsAffectedByFieldAction };
  let id = _get(fieldPropertiesValue, 'id');
  let fieldOrder;
  let selectedField;
  if (id) {
    const updatedField = {
      ...annotatedFields[id],
      ...fieldPropertiesValue,
      fieldName: getFieldDisplayName(fieldPropertiesValue) || _get(annotatedFields[id], 'fieldName'),
    };
    setState({ annotatedFields: FormAnnotationManager.updateField(updatedField) });
    const { order } = updatedField;
    fieldOrder = order;
    selectedField = updatedField;
  } else {
    id = uuid(); // make api, get id
    const { pageDimensions, annotatedFields: annotatedFieldsState, totalPages, formScale } = getState();
    const formScrollTop = document.querySelector('#formContainer').scrollTop;
    const newField = {
      id,
      fieldType: FIELD_TYPES.CUSTOM_FIELD,
      ...getNewAnnotationDetails(
        fieldPropertiesValue,
        annotatedFieldsState,
        pageDimensions,
        totalPages,
        formScrollTop,
        formScale
      ),
      order: FormAnnotationManager.getAnnotationsCount() + 1,
      fieldName: getFieldDisplayName(fieldPropertiesValue) || 'Custom Field',
      fieldAnnotation: getFieldDisplayName(fieldPropertiesValue) || 'Custom Field',
      description: _get(fieldPropertiesValue, 'general.description'),
    };
    FormAnnotationManager.increaseAnnotationsCount();
    setState({ ...FormAnnotationManager.execute(new AddField(newField, formScale)) });
    const formulas = _get(fieldPropertiesValue, 'formula.formulas', []);
    const dependents = [];
    _forEach(formulas, _formula => {
      const formulaDependents = _keys(_get(_formula, 'formulaMappings', {}));
      dependents.push(...formulaDependents);
    });
    FormFormulaManager.addField(`f${newField.order}`, dependents);
    const { order } = newField;
    fieldOrder = order;
    selectedField = newField;
  }

  const isCustom = checkIsCustomField(fieldPropertiesValue);
  if (isCustom) {
    const name = _get(fieldPropertiesValue, 'general.name', null);
    const fields = _filter(
      _values(annotatedFields),
      field => field.fieldType === FIELD_TYPES.CUSTOM_FIELD && field.fieldName === name && field.id !== id
    );
    if (_size(fields) > 0) {
      let updatedAnnotatedFields = annotatedFields;
      _forEach(fields, field => {
        const _id = _get(field, 'id');
        const fieldValue = {
          ...updatedAnnotatedFields[_id],
          general: {
            ...fieldPropertiesValue.general,
            timeStampKey: _get(field, 'general.timeStampKey', null),
          },
          rules: fieldPropertiesValue.rules,
          formula: fieldPropertiesValue.formula,
        };
        updatedAnnotatedFields = FormAnnotationManager.updateField(fieldValue);
      });
      setState({ annotatedFields: updatedAnnotatedFields });
    }
  }

  const active = _get(fieldPropertiesValue, 'active', false);
  if (!active) {
    // if field is not active, we will remove the field from our graph but mark the dependents as affected
    const dependentsOnThis = FormFormulaManager.getFieldsDependentOnField(`f${selectedField.order}`);
    FormFormulaManager.removeField(`f${selectedField.order}`);
    _fieldsAffectedByFieldAction[`f${selectedField.order}`] = dependentsOnThis;
  } else if (!FormFormulaManager.hasField(`f${fieldOrder}`)) {
    const formulas = _get(fieldPropertiesValue, 'formula.formulas', []);
    const dependents = [];
    _forEach(formulas, _formula => {
      const formulaDependents = _keys(_get(_formula, 'formulaMappings', {}));
      dependents.push(...formulaDependents);
    });
    FormFormulaManager.addField(`f${fieldOrder}`, dependents);
    _fieldsAffectedByFieldAction[`f${selectedField.order}`]?.map(field =>
      FormFormulaManager.addEdge(field, `f${fieldOrder}`)
    );
    _fieldsAffectedByFieldAction[`f${selectedField.order}`] = null;
  }

  const fieldUsedIndex = _compact(
    _map(_keys(_fieldsAffectedByFieldAction), key =>
      _values(_fieldsAffectedByFieldAction[key]).includes(`f${fieldOrder}`) ? key : null
    )
  );

  if (fieldUsedIndex.length > 0) {
    const formulas = _get(fieldPropertiesValue, 'formula.formulas', []);
    let isValid = true;
    _forEach(formulas, formula => {
      isValid = isValid && formula.isValid;
    });
    if (isValid) {
      _forEach(fieldUsedIndex, index => {
        const _index = _fieldsAffectedByFieldAction[index].indexOf(`f${fieldOrder}`);
        _fieldsAffectedByFieldAction[index].splice(_index, 1);
      });
    }
  }
  setState({
    isFieldFormModalVisible: false,
    isFieldEdit: false,
    fieldsAffectedByFieldAction: _fieldsAffectedByFieldAction,
    fieldsAffectedAsArray: _uniq(
      _flatten(_map(_keys(_fieldsAffectedByFieldAction), key => _values(_fieldsAffectedByFieldAction[key])))
    ),
  });
};

const handleCreateNewForm = (action, { getState, setState }) => {
  const {
    payload: { formData },
  } = action;
  const { annotatedFields } = getState();
  const formPrinterType = _get(formData, 'printerType');
  const updatedAnnotatedFields = updateAnnotationsByPrinterType(formPrinterType, annotatedFields);

  setState({
    formPrinterType,
    isCreateFormModalOpen: false,
    annotatedFields: FormAnnotationManager.setAnnotationFields(updatedAnnotatedFields),
  });
};

const resetPDFLayoutInfo = (_, { setState }) => {
  setState({ totalPages: 0, pageDimensions: {} });
};

export const resetFormInfo = setState => {
  FormAnnotationManager.resetAnnotationManager();
  setState({
    globalFormName: DEFAULT_PDF_NAME,
    formInfo: {},
    pdfLoaded: false,
    annotatedFields: {},
    selectedFields: [],
    totalPages: 0,
    pageDimensions: {},
    formFieldsCount: 0,
  });
  updateUserActions(setState);
};

const restorePDF = (action, { getState, setState }) => {
  const {
    isEditing,
    restoreFormInfoForCreateForm,
    restoreFormInfoForEditForm,
    annotatedFields,
    totalPages,
    pageDimensions,
  } = getState();

  const updatedFields = getFieldsWithUpdatedPosition(annotatedFields, totalPages, pageDimensions);

  setState({
    formInfo: !isEditing ? restoreFormInfoForCreateForm : restoreFormInfoForEditForm,
    annotatedFields: FormAnnotationManager.setAnnotationFields(updatedFields),
  });
};

const onCancel = (action, { getState, setState }) => {
  const { navigate } = getState();
  resetFormInfo(setState);
  navigate(ROUTES.FORM_CONFIGURATOR);
};

const showFieldTooltip = (action, { setState }) => {
  const {
    payload: { fieldId },
  } = action;
  setState({
    selectedTooltipField: fieldId,
  });
};

const handleEditFormName = (action, { setState }) => {
  const {
    payload: { isEditingFormName },
  } = action;
  setState({
    isEditingFormName,
    isValidFormName: true,
  });
};

const updateFormScale = (action, { getState, setState }) => {
  const {
    payload: { increaseBy },
  } = action;
  const { setFormScale, formScale, annotatedFields, pageDimensions } = getState();
  const updatedAnnotatedFields = updateAnnotationsByScale(
    _round(formScale, 1),
    _round(formScale + increaseBy, 1),
    annotatedFields
  );
  setFormScale(_round(formScale + increaseBy, 1));
  setState({
    annotatedFields: FormAnnotationManager.setAnnotationFields(updatedAnnotatedFields),
    pageDimensions: getScaledPageDimensions(pageDimensions, formScale, increaseBy),
  });
};

const toggleGroupAnnotation = (_, { getState, setState }) => {
  const { groupSelectedAnnotations } = getState();
  setState({
    groupSelectedAnnotations: !groupSelectedAnnotations,
  });
};
const onPrinterButtonClick = (action, { setState, getState }) => {
  const { formPrinterType } = getState();

  setState({
    isCreateFormModalOpen: true,
    formPrinterType,
  });
};

const onCloseCreateFormClick = (action, { setState }) => {
  setState({ isCreateFormModalOpen: false });
};
const showPlainPDFViewModal = (_, { setState }) => {
  setState({ showPlainPDFViewModal: true });
};

const hidePlainPDFViewModal = (_, { setState }) => {
  setState({ showPlainPDFViewModal: false });
};

const updateTotalPlainPDFPages = (action, { setState }) => {
  const {
    payload: { totalPages },
  } = action;

  setState({
    plainPDFTotalPages: FormAnnotationManager.setTotalPages(totalPages),
    plainPDFPageDimensions: {},
  });
};

const updatePlainPDFPageDimensions = (action, { setState }) => {
  const {
    payload: { pageDimensions },
  } = action;

  setState({
    plainPDFPageDimensions: pageDimensions,
  });
};

const onApplyAndValidate = async (action, { getState, setState }) => {
  const {
    payload: { fields, validatePreviewId: assetId, validatePreviewBy: assetType, requestId },
  } = action;
  const {
    formDetailsForValidateForm,
    annotatedFields,
    formInfo,
    pageDimensions,
    globalFormName,
    formPrinterType,
    configFields,
    commonPropertiesForConfigs,
    formScale,
    hideTabs,
    sectionDetails,
    params,
    formSetupDetails = {},
    getDateTimeFormatValue,
    showFormsPortal,
    showEnterpriseView,
  } = getState();
  setValueInSessionStorage(_get(formInfo, 'name'), true);
  const fieldsWithValues = getFieldsWithDateValuesInTimestamp(fields);
  const formId = _get(params, 'formId');
  const mediaId = _get(formInfo, 'mediaId', EMPTY_STRING);
  if (_isEmpty(formSetupDetails)) {
    if (!_isEmpty(formId)) {
      try {
        const formSetupDetailsResponse = await formConfiguratorToolAPI.getFormSetupDetails(
          formId,
          showFormsPortal,
          showEnterpriseView
        );
        const {
          response: { hits },
        } = formSetupDetailsResponse;
        setState({
          formSetupDetails: _pick(hits[0], 'mandatorySigningType'),
        });
      } catch {
        toaster(TOASTER_TYPE.ERROR, __('Error in fetching signature type.'));
      }
    }
  }

  const payload = {
    previewFormAnnotationRequest: {
      mediaId,
      formDisplayKey: globalFormName,
      originalFileName: _replace(_get(formInfo, 'name', DEFAULT_PDF_NAME), '.pdf', ''),
      formPrinterType,
      ...getAllAnnotationPayload(
        {
          annotatedFields,
          pageDimensions,
          formScale,
          configData: {
            configFields,
            commonPropertiesForConfigs,
          },
          hideTabs,
        },
        getDateTimeFormatValue
      ),
      effectiveDate: getUnix(startOfDay(getCurrentTime())),
      expiryDate: getUnix(endOfDay(nextday())),
    },
    formDetails: { ...formDetailsForValidateForm, fields: fieldsWithValues },
    fieldDisplaySections: sectionDetails,
    mandatorySigningType: _get(getState(), 'formSetupDetails.mandatorySigningType', null),
    requestId,
    assetType,
    assetId,
  };

  const { error } = await formConfiguratorToolAPI.newPreviewUrl(payload);
  if (!_isEmpty(error)) {
    const errorMsg = getAPIError(error);
    if (errorMsg === undefined) {
      toaster(TOASTER_TYPE.ERROR, __('Error in previewing the form.'));
    } else toaster(TOASTER_TYPE.ERROR, errorMsg);
  }
};

const copyAnnotationsToClipboard = async (_, { getState }) => {
  const { annotatedFields, selectedFields, configFields, commonPropertiesForConfigs, formScale, pageDimensions } =
    getState();
  dismissToast();
  const selectedFieldsString = getSelectedFieldsString(
    annotatedFields,
    selectedFields,
    configFields,
    commonPropertiesForConfigs,
    pageDimensions,
    formScale
  );
  try {
    await navigator.clipboard.writeText(selectedFieldsString);
    toaster(TOASTER_TYPE.SUCCESS, __('Annotations copied.'));
  } catch {
    toaster(TOASTER_TYPE.ERROR, __('Error copying annotations.'));
  }
};

const pasteAnnotationsFromClipboard = async (action, { getState, setState }) => {
  const {
    payload: { selectedAnnotations },
  } = action;
  const { annotatedFields, pageDimensions, configFields, commonPropertiesForConfigs, formScale } = getState();
  try {
    const annotationsCount = FormAnnotationManager.getAnnotationsCount();
    const formScrollTop = document.querySelector('#formContainer').scrollTop;

    const oldState = {
      annotatedFields,
      configFields,
      commonPropertiesForConfigs,
    };
    const {
      updatedAnnotatedFields,
      configFields: copiedConfigFields,
      commonPropertiesForConfigs: copiedCommonPropertiesForConfigs,
      formFieldsCount: newFormFieldsCount,
    } = getUpdatedAnnotatedFields({
      annotatedFields,
      selectedAnnotations,
      annotationsCount,
      pageDimensions,
      formScrollTop,
      formScale,
    });
    const newState = {
      annotatedFields: updatedAnnotatedFields,
      configFields: uniqueConfigFields([...configFields, ...copiedConfigFields]),
      commonPropertiesForConfigs: { ...commonPropertiesForConfigs, ...copiedCommonPropertiesForConfigs },
      formFieldsCount: newFormFieldsCount,
    };
    setState({
      ...FormAnnotationManager.execute(new PasteAnnotations(oldState, newState, formScale)),
    });
    updateUserActions(setState);
    toaster(TOASTER_TYPE.SUCCESS, __('Annotations pasted.'));
  } catch {
    toaster(TOASTER_TYPE.ERROR, __('Error pasting annotations.'));
  }
};

const showReorderFieldsModal = (_, { getState, setState }) => {
  const { annotatedFields, sectionDetails } = getState();

  let fieldsList = _filter(annotatedFields, ({ fieldType, general }) => {
    const fieldDefaultState = _get(general, 'fieldDefaultState', EMPTY_STRING);
    return (
      fieldType === FIELD_TYPES.CUSTOM_FIELD &&
      (fieldDefaultState === FIELD_DEFAULT_STATE_VALUES.USER_INPUT ||
        fieldDefaultState === FIELD_DEFAULT_STATE_VALUES.MANDATORY_USER_SELECT)
    );
  });

  const fieldsListNameVsId = _reduce(
    fieldsList,
    (nameVsIdConfig, { id, general }) => {
      const fieldName = _get(general, 'name', EMPTY_STRING);
      if (_has(nameVsIdConfig, fieldName)) {
        nameVsIdConfig[fieldName].push(id);
        return nameVsIdConfig;
      }
      return {
        ...nameVsIdConfig,
        [fieldName]: [id],
      };
    },
    {}
  );

  fieldsList = _filter(
    _map(fieldsList, field => {
      const fieldName = _get(field, 'general.name', EMPTY_STRING);
      if (_isEmpty(fieldsListNameVsId[fieldName])) {
        return EMPTY_OBJECT;
      }
      if (_size(fieldsListNameVsId[fieldName]) > 1) {
        const duplicateFieldsIds = fieldsListNameVsId[fieldName];
        fieldsListNameVsId[fieldName] = EMPTY_ARRAY;
        return {
          ...field,
          duplicateFieldsIds,
        };
      }
      return field;
    }),
    field => !_isEmpty(field)
  );

  if (!_isEmpty(sectionDetails)) {
    setState({
      showReorderFieldsModal: true,
      userInputFieldsList: getUserInputFieldsList(sectionDetails, fieldsList),
    });
  } else {
    const defaultSection = { id: uuid(), default: true, name: 'Default Section', displayOrder: 1 };
    setState({
      showReorderFieldsModal: true,
      userInputFieldsList: [
        {
          ...defaultSection,
          fields: fieldsList,
        },
      ],
      sectionDetails: [defaultSection],
    });
  }
};

const hideReorderFieldsModal = (_, { setState }) => {
  setState({ showReorderFieldsModal: false });
};

const handleReorderFormSubmit = (_, { getState, setState }) => {
  const { userInputFieldsList, annotatedFields } = getState();
  let updatedAnnotatedFields = annotatedFields;
  _forEach(userInputFieldsList, section => {
    const { id: sectionId, fields } = section;
    _forEach(fields, (field, index) => {
      const { id: fieldId, duplicateFieldsIds } = field;
      updatedAnnotatedFields = FormAnnotationManager.updateField({
        ...annotatedFields[fieldId],
        ...field,
        displayOrder: index + 1,
        fieldDisplaySectionId: sectionId,
      });
      _forEach(duplicateFieldsIds, duplicateFieldId => {
        updatedAnnotatedFields = FormAnnotationManager.updateField({
          ...annotatedFields[duplicateFieldId],
          displayOrder: index + 1,
          fieldDisplaySectionId: sectionId,
        });
      });
    });
  });

  setState({
    showReorderFieldsModal: false,
    annotatedFields: updatedAnnotatedFields,
    sectionDetails: _map(userInputFieldsList, (section, index) => ({
      ..._omit(section, 'fields'),
      displayOrder: index + 1,
    })),
  });
};

const setSourceSectionForReordering = (action, { setState }) => {
  const {
    payload: { sourceSection, fieldToBeSorted },
  } = action;
  setState({
    isDragging: true,
    sourceSection,
    targetSection: sourceSection,
    fieldToBeSorted,
  });
};

const sortUserInputField = (action, { getState, setState }) => {
  const {
    payload: { oldIndex, newIndex },
  } = action;
  const { fieldToBeSorted, sourceSection, targetSection, userInputFieldsList } = getState();

  if (!_isEmpty(targetSection) && sourceSection !== targetSection) {
    setState({
      userInputFieldsList: _map(userInputFieldsList, userInputField => {
        if (userInputField.id === sourceSection) {
          return {
            ...userInputField,
            fields: _concat(_slice(userInputField.fields, 0, oldIndex), _slice(userInputField.fields, oldIndex + 1)),
          };
        }
        if (userInputField.id === targetSection) {
          return {
            ...userInputField,
            fields: [...userInputField.fields, fieldToBeSorted],
          };
        }
        return userInputField;
      }),
    });
  } else if (!_isEmpty(targetSection) && sourceSection === targetSection) {
    setState({
      userInputFieldsList: _map(userInputFieldsList, userInputField =>
        userInputField.id === sourceSection
          ? { ...userInputField, fields: arrayMove(userInputField.fields, oldIndex, newIndex) }
          : userInputField
      ),
    });
  }
  setState({
    isDragging: false,
    fieldToBeSorted: EMPTY_OBJECT,
    sourceSection: EMPTY_STRING,
    targetSection: EMPTY_STRING,
  });
};

const onMouseEnterInSection = (action, { setState }) => {
  const {
    payload: { sectionKey },
  } = action;
  setState({ targetSection: sectionKey });
};

const onMouseLeaveSection = (_, { setState }) => {
  setState({ targetSection: EMPTY_STRING });
};

const onSectionNameChange = (action, { getState, setState }) => {
  const {
    payload: { sectionKey, value, languageOptions },
  } = action;

  const { userInputFieldsList } = getState();
  setState({
    userInputFieldsList: _map(userInputFieldsList, userInputField =>
      userInputField.id === sectionKey ? { ...userInputField, name: value, languages: languageOptions } : userInputField
    ),
  });
};

const moveSectionUp = (action, { getState, setState }) => {
  const {
    payload: { sectionKey },
  } = action;
  const { userInputFieldsList } = getState();
  const sectionIndex = _findIndex(userInputFieldsList, userInputField => userInputField.id === sectionKey);
  setState({ userInputFieldsList: arrayMove(userInputFieldsList, sectionIndex, sectionIndex - 1) });
};

const moveSectionDown = (action, { getState, setState }) => {
  const {
    payload: { sectionKey },
  } = action;
  const { userInputFieldsList } = getState();
  const sectionIndex = _findIndex(userInputFieldsList, userInputField => userInputField.id === sectionKey);
  setState({ userInputFieldsList: arrayMove(userInputFieldsList, sectionIndex, sectionIndex + 1) });
};

const deleteSection = (action, { getState, setState }) => {
  const {
    payload: { sectionToBeDeleted, sectionBuffer },
  } = action;
  const { userInputFieldsList } = getState();
  const sectionToBeDeletedIndex = _findIndex(
    userInputFieldsList,
    userInputField => userInputField.id === sectionToBeDeleted
  );
  if (!_isEmpty(sectionBuffer)) {
    const bufferSectionIndex = _findIndex(userInputFieldsList, userInputField => userInputField.id === sectionBuffer);
    userInputFieldsList[bufferSectionIndex].fields = [
      ...userInputFieldsList[bufferSectionIndex].fields,
      ...userInputFieldsList[sectionToBeDeletedIndex].fields,
    ];
  }
  setState({
    userInputFieldsList: _concat(
      _slice(userInputFieldsList, 0, sectionToBeDeletedIndex),
      _slice(userInputFieldsList, sectionToBeDeletedIndex + 1)
    ),
  });
};

const addSection = (_, { getState, setState }) => {
  const { userInputFieldsList } = getState();
  const newSectionKey = uuid();

  const newSectionNumber = _reduce(
    userInputFieldsList,
    (maxSectionNumber, { name }) => {
      const thisSectionNumber = _toNumber(_last(_split(name, ' ')));
      if (_isNumber(thisSectionNumber) && maxSectionNumber < thisSectionNumber) {
        return thisSectionNumber;
      }
      return maxSectionNumber;
    },
    0
  );

  setState({
    userInputFieldsList: [
      ...userInputFieldsList,
      { id: newSectionKey, name: `Section ${newSectionNumber + 1}`, fields: [], default: false },
    ],
  });
};

const showAuditLogs = (action, { setState, getState }) => {
  const { formDetails } = getState();
  setState({
    showAuditLogs: true,
    assetId: _get(formDetails, 'dealerPdfKey', null),
  });
};

const toggleAuditLogs = (action, { setState }) => {
  setState({
    showAuditLogs: false,
  });
};

const showDeleteFieldsBulkModal = (_, { setState }) => {
  setState({ showDeleteFieldsBulkModal: true });
};

const hideDeleteFieldsBulkModal = (_, { setState }) => {
  setState({ showDeleteFieldsBulkModal: false });
};

const deleteFieldsBulk = (action, { getState, setState }) => {
  const {
    payload: { selectedFields },
  } = action;
  const { formScale, fieldsAffectedByFieldAction: fieldsAffected, annotatedFields } = getState();
  const { fieldsAffectedByFieldAction, fieldsAffectedAsArray } = getUpdatedFormulatAffectedFields(
    annotatedFields,
    selectedFields,
    fieldsAffected
  );
  setState(
    {
      ...FormAnnotationManager.execute(new DeleteFields(selectedFields, formScale)),
      selectedFields: FormAnnotationManager.clearSelectedFields(),
      showDeleteFieldsBulkModal: false,
      fieldsAffectedByFieldAction,
      fieldsAffectedAsArray,
    },
    () => {
      toaster(
        TOASTER_TYPE.SUCCESS,
        __('{{deleteFieldsCount}} fields deleted', { deleteFieldsCount: _size(selectedFields) }),
        {},
        __('Fields Deleted!')
      );
    }
  );
  updateUserActions(setState);
};

const handleOnPrintAlignmentSaveClick = (action, { setState }) => {
  const {
    payload: { alignedAnnotatedFields },
  } = action;

  setState({ annotatedFields: alignedAnnotatedFields });
};

const handleSaveAndPublish = async (_, { getState, setState }) => {
  setState({ isSavingFormDetails: true });

  const {
    navigate,
    pageType,
    annotatedFields,
    formInfo,
    pageDimensions,
    formPrinterType,
    configFields,
    commonPropertiesForConfigs,
    formScale,
    hideTabs,
    sectionDetails,
    fniOptions,
    dueBills,
    makes,
    getDateTimeFormatValue,
    showFormsPortal,
    showEnterpriseView,
  } = getState();

  let { formId, formSetupDetails, globalFormName, formDetails } = getState();

  let mediaId = _get(formInfo, 'mediaId', EMPTY_STRING);

  try {
    if (pageType === PAGE_TYPES.PREPARE) {
      const payload = getSaveAnnotationsPayload(
        {
          annotatedFields,
          commonPropertiesForConfigs,
          configFields,
          formInfo,
          formPrinterType,
          formScale,
          globalFormName,
          hideTabs,
          mediaId,
          pageDimensions,
          sectionDetails,
        },
        getDateTimeFormatValue
      );
      const { response } = await formConfiguratorToolAPI.updateAnnotations(formId, payload);

      formId = _get(response, 'dealerPdfKey');
      const formSetupDetailsResponse = await formConfiguratorToolAPI.getFormSetupDetails(
        formId,
        showFormsPortal,
        showEnterpriseView
      );
      formSetupDetails = _get(formSetupDetailsResponse, 'response.hits[0]', EMPTY_OBJECT);

      const { response: formsResponse } = await formConfiguratorToolAPI.getFormDetails(formId);
      globalFormName = _get(formsResponse, 'formDisplayKey', DEFAULT_PDF_NAME);
      mediaId = _get(formsResponse, 'mediaId', EMPTY_STRING);
      formDetails = formsResponse;
    }

    const formValues = formSetupDetails ? getFormSetupDetailsFromPayload(formSetupDetails) : EMPTY_OBJECT;
    const dealerPdfKey = _get(formDetails, 'dealerPdfKey', null);
    const payload = {
      mediaId,
      formDisplayKey: globalFormName,
      formSetupDetails: {
        ...getNewFormPayload({
          formValues,
          usageTargetParams: getUsageTargetParamsFromPayload(formValues),
          fnis: fniOptions,
          dueBills,
          makes,
          moduleVisibilityRules: _get(formValues, FORM_FIELD.MODULE_VISIBILITY_RULES, EMPTY_ARRAY),
        }),
        formPrinterType,
      },
      comment: 'comment',
    };
    await formConfiguratorToolAPI.saveForm(dealerPdfKey, payload);
  } catch {
    toaster(TOASTER_TYPE.ERROR, __('Error saving form details'));
  }

  setState({ isSavingFormDetails: false });
  navigate(ROUTES.FORM_CONFIGURATOR);
};

const onSelectEnvForPreview = async (action, { getState, setState }) => {
  const {
    payload: { env },
  } = action;
  setState(
    {
      envForPreview: env,
      dealersListforPreview: [],
      selectedDealer: null,
    },
    () => {
      onSearchDealerForPreview(
        {
          payload: {
            searchText: '',
          },
        },
        { getState, setState }
      );
    }
  );
};

const onSelectDealerForPreview = async (action, { setState }) => {
  const {
    payload: { selectedDealer },
  } = action;

  setState({ selectedDealer });
};

const onSearchDealerForPreview = async (action, { setState, getState }) => {
  const {
    payload: { searchText },
  } = action;
  const { config, envForPreview } = getState();
  try {
    const { fetchDealers } = ConfigReader.validateFormProps(config);
    const dealersListforPreview = await fetchDealers(getDealerSearchPayload(searchText, envForPreview));
    const updatedDealersList = addIdToDealerInformation(dealersListforPreview);
    setState({ dealersListforPreview: _get(updatedDealersList, 'hits') });
  } catch {
    toaster(TOASTER_TYPE.ERROR, __('Error fetching dealers.'));
  }
};

// in below function, need to chnage the payload after gettiong actual API, As of now using forms configurator api which is used to display table
const fetchFormsList = async ({ setState, getState, searchForms }) => {
  setState({ loading: true });
  const { searchText, currentPage, pageLength } = getState();
  const payload = getFormListPayload(searchText, 0, pageLength || DEFAULT_PAGE_SIZE, EMPTY_ARRAY, EMPTY_ARRAY);
  const response = await formConfiguratorToolAPI.searchFormList(payload);
  if (response) {
    const { count, hits } = response;
    const pageNumber = count > 0 && !searchForms ? currentPage : 1;
    setState({
      currentPage: pageNumber,
      count,
      formsList: hits,
      // resolvedUsers,
    });
  }
  setState({ loading: false });
};
const handleFiledChange = ({ payload }, { getState, setState }) => {
  const searchText = encodeURIComponent(payload);
  setState({ searchText, filterData: {}, selectedFilters: EMPTY_ARRAY }, () =>
    fetchFormsList({ getState, setState, searchForms: true })
  );
};

const searchPDFLibraryForms = async (action, { setState }) => {
  const {
    payload: { searchText },
  } = action;

  const response = await formConfiguratorToolAPI.fetchPdfFormsList(getDefaultSearchPayload(searchText));
  if (response) {
    const { hits } = response;
    setState({
      pdfLibraryFormsList: hits,
    });
  }
};

const onSelectPDFLibraryForm = async (action, { setState }) => {
  const {
    payload: { selectedPDF },
  } = action;
  const { mediaId } = selectedPDF;
  const signedUrlResponse = await getSignedURLs([mediaId]);
  const url = _get(signedUrlResponse, '0.normal.url');

  setState({
    formInfo: getFormInfoFromSelectedPdf(selectedPDF, url),
    hideFormPrinterTypeSelection: true,
    isCreateFormModalOpen: false,
    formPrinterType: getFormPrinterType(selectedPDF),
  });
};

const showFormSettingsSidebar = (_, { setState }) => {
  setState({ showFormSettingsSidebar: true });
};

const hideFormSettingsSidebar = (_, { setState }) => {
  setState({ showFormSettingsSidebar: false });
};

const onFormSettingsUpdate = (action, { setState }) => {
  const { payload: updatedFormSettingsState = EMPTY_OBJECT } = action;

  const formName = _get(updatedFormSettingsState, 'formValues.formName');

  setState({ ...updatedFormSettingsState, globalFormName: formName });

  hideFormSettingsSidebar(action, { setState });
};

const onFormSingleSave = async (action, { setState, getState }) => {
  const {
    annotatedFields,
    formInfo,
    pageDimensions,
    globalFormName,
    formPrinterType,
    configFields,
    commonPropertiesForConfigs,
    formId,
    formScale,
    hideTabs,
    sectionDetails,
    getDateTimeFormatValue,
    totalPages,
    formValues,
    usageTargetParams,
    fniOptions,
    navigate,
    dealerPdfKey,
    // mediaId,
    makes,
    saveWithReasonForPublish,
    dueBills,
    econtractForm,
    moduleVisibilityRules,
    metaData,
    isFcLiteModeEnabled,
  } = getState();
  const {
    payload: { mediaId, dynamicUsageCategoryVsRules },
  } = action;

  // Use formName from formValues if in lite mode and globalFormName is disabled, otherwise use globalFormName
  const effectiveFormName =
    isFcLiteModeEnabled && _get(formValues, 'formName') ? _get(formValues, 'formName') : globalFormName;

  const annotationsPayload = getSaveAnnotationsPayload(
    {
      annotatedFields,
      commonPropertiesForConfigs,
      configFields,
      formInfo,
      formPrinterType,
      formScale,
      globalFormName: effectiveFormName,
      hideTabs,
      mediaId,
      pageDimensions,
      sectionDetails,
      totalPages,
    },
    getDateTimeFormatValue
  );

  const formSettingsPayload = {
    mediaId,
    formDisplayKey: effectiveFormName,
    formSetupDetails: {
      ...getNewFormPayload({
        formValues,
        usageTargetParams,
        fnis: fniOptions,
        dueBills,
        makes,
        econtractForm,
        moduleVisibilityRules,
        metaData,
        dynamicUsageCategoryVsRules,
      }),
      formPrinterType,
    },
    comment: saveWithReasonForPublish,
  };

  setState({ isFormSubmitInProgress: true });

  const payload = {
    updateRequest: annotationsPayload,
    saveRequest: formSettingsPayload,
  };

  try {
    const response = await formConfiguratorToolAPI.singleStepSaveForm(formId, payload);
    if (!_isEmpty(response)) {
      navigate(ROUTES.FORM_CONFIGURATOR);
    } else {
      toaster(TOASTER_TYPE.ERROR, __('Error saving form details'));
    }
  } catch {
    toaster(TOASTER_TYPE.ERROR, __('Error saving form details'));
  }

  setState({ isSaveConfirmationModalVisible: false, isFormSubmitInProgress: false });
};

const onDownloadFormPdf = async action => {
  const {
    payload: { formKey },
  } = action;

  try {
    if (formKey) {
      const signedUrlResponse = await formConfiguratorToolAPI.downloadFormPdf(formKey);
      if (!signedUrlResponse) throw new Error('error');
      downloadURI(signedUrlResponse);
    }
  } catch (err) {
    toaster(TOASTER_TYPE.ERROR, __('Failed to download'));
  }
};

const ACTION_HANDLERS = {
  [ACTION_TYPES.ON_PAGE_MOUNT]: handlePageMount,
  [ACTION_TYPES.ON_AGREEMENT_SCREEN_MOUNT]: handleAgreementScreenMount,
  [ACTION_TYPES.ON_PREPARE_SCREEN_MOUNT]: handlePrepareScreenMount,
  [ACTION_TYPES.ON_SETUP_SCREEN_MOUNT]: handleSetupScreenMount,
  [ACTION_TYPES.GET_DOCUMENT_TYPE_LIST]: handleFetchDocumentTypes,
  [ACTION_TYPES.HANDLE_FORM_NAME_CHANGE]: handleFormNameChange,
  [ACTION_TYPES.GLOBAL_FIELD_SEARCH]: handleGlobalFieldSearch,
  [ACTION_TYPES.ON_ANNOTATED_FIELD_SEARCH]: handleAnnotatedFieldSearch,
  [ACTION_TYPES.ON_TOGGLE_SINGLE_ANNOTATED_FIELD]: handleToggleSingleAnnotatedField,
  [ACTION_TYPES.ON_TOGGLE_BULK_ANNOTATED_FIELDS]: handleToggleBulkAnnotatedFields,
  [ACTION_TYPES.TOGGLE_PREPARE_SETUP_SCREEN]: handlePrepareSetupScreenToggle,
  [ACTION_TYPES.NAVIGATE_BACK_TO_FORMS_LIST]: navigateToFormsList,
  [ACTION_TYPES.HANDLE_SUBMIT_FORM_SETUP]: handleSubmitFormSetup,
  [ACTION_TYPES.ON_SAVE_ANNOTATIONS]: handleSaveAnnotations,
  [ACTION_TYPES.ON_ACCEPT_AGREEMENT]: handleAcceptAgreement,
  [ACTION_TYPES.ADD_FIELD_TO_FORM]: addFieldToForm,
  [ACTION_TYPES.ON_FIELD_PROPERTIES_CLICK]: handleFieldPropertiesClick,
  [ACTION_TYPES.DELETE_FIELD_FROM_FORM]: deleteFieldFromForm,
  [ACTION_TYPES.DUPLICATE_SELECTED_FIELD]: duplicateSelectedField,
  [ACTION_TYPES.BULK_DUPLICATE_FIELDS]: duplicateMultipleFields,
  [ACTION_TYPES.ON_FIELD_SELECT]: onFormFieldSelect,
  [ACTION_TYPES.ON_MULTIPLE_FIELD_SELECT]: onMultipleSelect,
  [ACTION_TYPES.UPDATE_TOTAL_PAGES]: updateTotalPages,
  [ACTION_TYPES.UPDATE_PDF_PAGE_DIMENSIONS]: updatePageDimensions,
  [ACTION_TYPES.UPDATE_FIELD]: updateField,
  [ACTION_TYPES.UPDATE_SELECTED_FIELDS]: updateSelectedFields,
  [ACTION_TYPES.CLEAR_SELECTED_FIELDS]: clearSelctedFields,
  [ACTION_TYPES.UNDO_LAST_ACTION]: undoLastAction,
  [ACTION_TYPES.REDO_LAST_ACTION]: redoLastAction,
  [ACTION_TYPES.ON_ADD_CONFIGURATION_CLICK]: handleAddCongfigurationClick,
  [ACTION_TYPES.ON_CLOSE_CONFIGURATION_MODAL]: handleCloseConfigurationModal,
  [ACTION_TYPES.ON_CONFIGURATION_FIELD_FORM_SUBMIT]: handleConfigurationFormSubmit,
  [ACTION_TYPES.ON_ADD_CUSTOM_FIELD_CLICK]: handleAddCustomFieldClick,
  [ACTION_TYPES.ON_CLOSE_FIELD_MODAL]: handleCloseFieldFormModal,
  [ACTION_TYPES.ON_FIELD_FORM_SUBMIT]: handleFieldFormSubmit,
  [ACTION_TYPES.UPDATE_GLOBAL_FORM_NAME]: updateGlobalFormName,
  [ACTION_TYPES.SET_FORM_INFO]: setFormInfo,
  [ACTION_TYPES.ON_CLOSE_VALIDATE_FORM_MODAL]: handleCloseValidateFormModalClick,
  [ACTION_TYPES.ON_VALIDATE_FORM_MODAL_CLICK]: onValidateFormClick,
  [ACTION_TYPES.SHOW_SAVE_CONFIRMATION_MODAL]: handleShowSaveConfirmationModal,
  [ACTION_TYPES.SHOW_SAVE_AS_NEW_CONFIRMATION_MODAL]: handleShowSaveAsNewConfirmationModal,
  [ACTION_TYPES.HIDE_SAVE_CONFIRMATION_MODAL]: handleHideSaveConfirmationModal,
  [ACTION_TYPES.HIDE_SAVE_AS_NEW_CONFIRMATION_MODAL]: handleHideSaveAsNewConfirmationModal,
  [ACTION_TYPES.ON_SAVE_AS_NEW_FORM_NAME_CHANGE]: handleSaveAsNewFormNameChange,
  [ACTION_TYPES.ON_FORM_SETUP_COMMENT_CHANGE]: handleFormSetupCommentChange,
  [ACTION_TYPES.ON_SUBMIT_CREATE_FORM]: handleCreateNewForm,
  [ACTION_TYPES.RESET_PDF_LAYOUT_INFO]: resetPDFLayoutInfo,
  [ACTION_TYPES.ON_CANCEL]: onCancel,
  [ACTION_TYPES.SHOW_FIELD_TOOLTIP]: showFieldTooltip,
  [ACTION_TYPES.SHOW_EXIT_EDITOR_MODAL]: showExitEditorModal,
  [ACTION_TYPES.HIDE_EXIT_EDITOR_MODAL]: hideExitEditorModal,
  [ACTION_TYPES.EDIT_FORM_NAME]: handleEditFormName,
  [ACTION_TYPES.UPDATE_FORM_SCALE]: updateFormScale,
  [ACTION_TYPES.RESTORE_PDF]: restorePDF,
  [ACTION_TYPES.TOGGLE_GROUP_ANNOTAIONS]: toggleGroupAnnotation,
  [ACTION_TYPES.SHOW_COMMON_PROPERTIES_MODAL]: showCommonPropertiesModal,
  [ACTION_TYPES.HIDE_COMMON_PROPERTIES_MODAL]: hideCommonPropertiesModal,
  [ACTION_TYPES.SUBMIT_COMMON_PROPERTIES]: saveCommonProperties,
  [ACTION_TYPES.ON_PRINTER_BUTTON_CLICK]: onPrinterButtonClick,
  [ACTION_TYPES.ON_CLOSE_BUTTON_CLICK]: onCloseCreateFormClick,
  [ACTION_TYPES.SHOW_PLAIN_PDF_VIEW_MODAL]: showPlainPDFViewModal,
  [ACTION_TYPES.HIDE_PLAIN_PDF_VIEW_MODAL]: hidePlainPDFViewModal,
  [ACTION_TYPES.UPDATE_TOTAL_PLAIN_PDF_PAGES]: updateTotalPlainPDFPages,
  [ACTION_TYPES.UPDATE_PLAIN_PDF_PAGE_DIMENSIONS]: updatePlainPDFPageDimensions,
  [ACTION_TYPES.ON_APPLY_AND_VALIDATE]: onApplyAndValidate,
  [ACTION_TYPES.COPY_ANNOTATION_FIELDS]: copyAnnotationsToClipboard,
  [ACTION_TYPES.PASTE_ANNOTATION_FIELDS]: pasteAnnotationsFromClipboard,
  [ACTION_TYPES.SHOW_REORDER_FIELDS_MODAL]: showReorderFieldsModal,
  [ACTION_TYPES.HIDE_REORDER_FIELDS_MODAL]: hideReorderFieldsModal,
  [ACTION_TYPES.SET_SOURCE_SECTION_FOR_REORDERING]: setSourceSectionForReordering,
  [ACTION_TYPES.SORT_USER_INPUT_FIELD]: sortUserInputField,
  [ACTION_TYPES.ON_MOUSE_ENTER_IN_SECTION]: onMouseEnterInSection,
  [ACTION_TYPES.ON_MOUSE_LEAVE_SECTION]: onMouseLeaveSection,
  [ACTION_TYPES.ON_SECTION_NAME_CHANGE]: onSectionNameChange,
  [ACTION_TYPES.MOVE_SECTION_UP]: moveSectionUp,
  [ACTION_TYPES.MOVE_SECTION_DOWN]: moveSectionDown,
  [ACTION_TYPES.DELETE_SECTION]: deleteSection,
  [ACTION_TYPES.ADD_SECTION]: addSection,
  [ACTION_TYPES.ON_REORDER_FORM_SUBMIT]: handleReorderFormSubmit,
  [ACTION_TYPES.SHOW_AUDIT_LOGS_DRAWER]: showAuditLogs,
  [ACTION_TYPES.TOGGLE_AUDIT_LOGS]: toggleAuditLogs,

  [ACTION_TYPES.SHOW_DELETE_FIELDS_BULK_MODAL]: showDeleteFieldsBulkModal,
  [ACTION_TYPES.HIDE_DELETE_FIELDS_BULK_MODAL]: hideDeleteFieldsBulkModal,
  [ACTION_TYPES.DELETE_FIELDS_BULK]: deleteFieldsBulk,

  [ACTION_TYPES.ON_PRINT_ALIGNMENT_SAVE_CLICK]: handleOnPrintAlignmentSaveClick,
  [ACTION_TYPES.ON_SAVE_AND_PUBLISH]: handleSaveAndPublish,
  [ACTION_TYPES.SELECT_ENV_FOR_PREVIEW]: onSelectEnvForPreview,
  [ACTION_TYPES.SELECT_DEALER_FOR_PREVIEW]: onSelectDealerForPreview,
  [ACTION_TYPES.SEARCH_DEALER_FOR_PREVIEW]: onSearchDealerForPreview,
  [ACTION_TYPES.ON_FIELD_CHANGE]: handleFiledChange,
  [ACTION_TYPES.SEARCH_PDF_LIBRARY_FORMS]: searchPDFLibraryForms,
  [ACTION_TYPES.SELECT_PDF_LIBRARY_FORM]: onSelectPDFLibraryForm,

  [ACTION_TYPES.SHOW_FORM_SETTINGS_SIDEBAR]: showFormSettingsSidebar,
  [ACTION_TYPES.HIDE_FORM_SETTINGS_SIDEBAR]: hideFormSettingsSidebar,
  [ACTION_TYPES.ON_FORM_SUBMIT]: onFormSettingsUpdate,
  [ACTION_TYPES.ON_FORM_SINGLE_SAVE]: onFormSingleSave,

  [ACTION_TYPES.ON_DOWNLOAD_FORM_PDF]: onDownloadFormPdf,
};

export default ACTION_HANDLERS;
