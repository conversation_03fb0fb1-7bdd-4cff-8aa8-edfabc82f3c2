import { EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import {
  AUDIT_ID,
  AUDIT_ID_FORMS_LIBRARY_FORM,
  AUDIT_ID_FORM_ANNOTATION,
  AUDIT_ID_FORMS_LIBRARY_FORM_SETUP,
  AUDIT_ID_FORM_ACTION,
  AUDIT_ID_FORMS_IN_PACKAGE,
} from '@tekion/tekion-base/constants/formConfigurator/constants';
import Colors from 'tstyles/exports.scss';
import TextInput from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/textInput/withKeyPressInput';
import Select from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/select';
import CheckBox from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/checkboxGroup';
import DateInput from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/datePicker';
import FILTER_TYPES from '@tekion/tekion-components/src/organisms/filterSection/constants/filterSection.filterTypes';

import * as FORM_CONSTANTS from '@tekion/tekion-base/constants/formConfigurator/constants';
import TextArea from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/textArea';
import { FORM_PRINTER_TYPES } from '@tekion/tekion-base/constants/formConfigurator/printers';
import PriceInput from '../../fieldRenderers/currencyInputField';
import { FIELD_DEFAULT_VALUES } from './components/FieldPropertiesGeneralForm/FieldPropertiesGeneralForm.constants';
import { ORIENTATION_TYPES } from './components/TextFormatting/TextFormatting.constants';
import {
  FORM_FIELDS as CUSTOM_FORM_FIELDS,
  FIELD_DEFAULT_STATE_VALUES,
} from './components/CustomFieldGeneralForm/CustomFieldGeneralForm.constants';

export const { PAGE_BORDERS, PAGE_MARGINS } = FORM_CONSTANTS;
export const DEFAULT_FORM_PAGE_WIDTH = 1728;
export const { DEFAULT_FORM_SCALE } = FORM_CONSTANTS;
export const MIN_SCALE = 1;
export const MAX_SCALE = 2.5;
export const ALIGN_FORM_FIELD_TYPES = {
  ALIGN_LEFT: 'ALIGN_LEFT',
  ALIGN_TOP: 'ALIGN_TOP',
  ALIGN_HORIZONTALLY: 'ALIGN_HORIZONTALLY',
  ALIGN_VERTICALLY: 'ALIGN_VERTICALLY',
  ALIGN_RIGHT: 'ALIGN_RIGHT',
  ALIGN_BOTTOM: 'ALIGN_BOTTOM',
};

export const ALIGN_FORM_FIELD_LABELS = {
  ALIGN_LEFT: __('Align Left'),
  ALIGN_TOP: __('Align Top'),
  ALIGN_HORIZONTALLY: __('Align Horizontally'),
  ALIGN_VERTICALLY: __('Align Vertically'),
  ALIGN_RIGHT: __('Align Right'),
  ALIGN_BOTTOM: __('Align Bottom'),
};

export const ALIGN_FORM_FIELDS = [
  {
    type: ALIGN_FORM_FIELD_TYPES.ALIGN_LEFT,
    label: ALIGN_FORM_FIELD_LABELS.ALIGN_LEFT,
    icon: 'icon-align-left',
  },
  {
    type: ALIGN_FORM_FIELD_TYPES.ALIGN_TOP,
    label: ALIGN_FORM_FIELD_LABELS.ALIGN_TOP,
    icon: 'icon-align-top',
  },
  {
    type: ALIGN_FORM_FIELD_TYPES.ALIGN_HORIZONTALLY,
    label: ALIGN_FORM_FIELD_LABELS.ALIGN_HORIZONTALLY,
    icon: 'icon-align-horizontally',
  },
  {
    type: ALIGN_FORM_FIELD_TYPES.ALIGN_VERTICALLY,
    label: ALIGN_FORM_FIELD_LABELS.ALIGN_VERTICALLY,
    icon: 'icon-align-vertically',
  },
  {
    type: ALIGN_FORM_FIELD_TYPES.ALIGN_RIGHT,
    label: ALIGN_FORM_FIELD_LABELS.ALIGN_RIGHT,
    icon: 'icon-align-right',
  },
  {
    type: ALIGN_FORM_FIELD_TYPES.ALIGN_BOTTOM,
    label: ALIGN_FORM_FIELD_LABELS.ALIGN_BOTTOM,
    icon: 'icon-align-bottom',
  },
];

export const MATCH_FORM_FIELD_TYPES = {
  MATCH_HEIGHT: 'MATCH_HEIGHT',
  MATCH_WIDTH: 'MATCH_WIDTH',
  MATCH_WIDTH_HEIGHT: 'MATCH_WIDTH_HEIGHT',
};

export const MATCH_FORM_FIELD_LABELS = {
  MATCH_HEIGHT: __('Match Height'),
  MATCH_WIDTH: __('Match Width'),
  MATCH_WIDTH_HEIGHT: __('Match Height & Width'),
};

export const UPDATE_GROUPED_FIELDS = 'UPDATE_GROUPED_FIELDS';
export const MOVE_SELECTED_FIELDS = 'MOVE_SELECTED_FIELDS';

export const MATCH_FORM_FIELDS = [
  {
    type: MATCH_FORM_FIELD_TYPES.MATCH_HEIGHT,
    label: MATCH_FORM_FIELD_LABELS.MATCH_HEIGHT,
    icon: 'icon-match-height',
  },
  {
    type: MATCH_FORM_FIELD_TYPES.MATCH_WIDTH,
    label: MATCH_FORM_FIELD_LABELS.MATCH_WIDTH,
    icon: 'icon-match-width',
  },
  {
    type: MATCH_FORM_FIELD_TYPES.MATCH_WIDTH_HEIGHT,
    label: MATCH_FORM_FIELD_LABELS.MATCH_WIDTH_HEIGHT,
    icon: 'icon-match-width-height',
  },
];

export const KEYBOARD_KEY_CODES = {
  ESCAPE: 'Escape',
  Y: 89,
  Z: 90,
  C: 67,
  V: 86,
  ARROW_LEFT: 'ArrowLeft',
  ARROW_RIGHT: 'ArrowRight',
  ARROW_UP: 'ArrowUp',
  ARROW_DOWN: 'ArrowDown',
  CONTROL_KEY: 'Control',
};

export const ARROW_KEYS = [
  KEYBOARD_KEY_CODES.ARROW_UP,
  KEYBOARD_KEY_CODES.ARROW_RIGHT,
  KEYBOARD_KEY_CODES.ARROW_DOWN,
  KEYBOARD_KEY_CODES.ARROW_LEFT,
];

export const DEFAULT_FIELD_HEIGHT = 12.69;
export const DEFAULT_FIELD_WIDTH = 80;

export const DEFAULT_ANNOTATION_FIELD_LAYOUT = {
  x: 0,
  y: 0,
  width: DEFAULT_FIELD_WIDTH,
  height: DEFAULT_FIELD_HEIGHT,
  actualWidth: DEFAULT_FIELD_WIDTH,
  actualHeight: DEFAULT_FIELD_HEIGHT,
  page: 1,
};

export const FIELD_TYPES = {
  GLOBAL_FIELD: 'GLOBAL_FIELD',
  CUSTOM_FIELD: 'CUSTOM_FIELD',
  CONFIGURATION_FIELD: 'CONFIGURATION_FIELD',
};

export const ANNOTATION_MODAL_TYPES = {
  GLOBAL_FIELD: 'GLOBAL_FIELD',
  CUSTOM_FIELD: 'CUSTOM_FIELD',
  CONFIGURATION_FIELD: 'CONFIGURATION_FIELD',
};

export const FORM_OPTIONS = [
  {
    key: 'delete',
    label: __('Delete PDF'),
  },
];
export const PAGE_TYPES = {
  AGREEMENT: 'AGREEMENT',
  PREPARE: 'PREPARE',
  SETUP: 'SETUP',
};

export const FIELD_MENU_ITEMS_TYPES = {
  PROPERTIES: 'properties',
  DUPLICATE: 'duplicate',
  DELETE: 'delete',
};

export const FIELD_MENU_ITEMS = [
  {
    label: __('Properties'),
    key: FIELD_MENU_ITEMS_TYPES.PROPERTIES,
    icon: 'icon-preferences',
  },
  {
    label: __('Duplicate'),
    key: FIELD_MENU_ITEMS_TYPES.DUPLICATE,
    icon: 'icon-copy',
  },
  {
    label: __('Delete'),
    key: FIELD_MENU_ITEMS_TYPES.DELETE,
    icon: 'icon-trash',
  },
];

export const CONFIG_FIELD_MENU_ITEMS = [
  {
    label: __('Properties'),
    key: FIELD_MENU_ITEMS_TYPES.PROPERTIES,
    icon: 'icon-preferences',
  },
  {
    label: __('Delete'),
    key: FIELD_MENU_ITEMS_TYPES.DELETE,
    icon: 'icon-trash',
  },
];

export const FIELDS_AND_CONFIGS_TABS = {
  GLOBAL_FIELDS: 'GLOBAL_FIELDS',
  CONFIGURATIONS: 'CONFIGURATIONS',
};

export const FIELDS_AND_CONFIGS_TABS_TITLE = {
  [FIELDS_AND_CONFIGS_TABS.GLOBAL_FIELDS]: __('Fields'),
  [FIELDS_AND_CONFIGS_TABS.CONFIGURATIONS]: __('Grouped Fields'),
};

export const FIELDS_AND_CONFIGS_STYLE_CONFIG = {
  background: Colors.white,
  height: 35,
};
export const FORM_RENDERED_SCALE = 1.5;
export const DEFAULT_SCALE = 1;
export const ANNOTATION_SCALE_RENDER = FORM_RENDERED_SCALE / DEFAULT_SCALE;
export const ANNOTATION_SCALE_PAYLOAD = DEFAULT_SCALE / FORM_RENDERED_SCALE;

export const DEFAULT_TEXT_COLOR = '#000000';
export const DEFAULT_FIELD_STATE_GENERAL = {
  name: ['STOCK_TYPE'],
  type: ['FREE_TEXT'],
  variation: ['UPPER_CASE'],
  description: 'New vehicle information.',
  prefix: 'prefix',
  suffix: 'suffix',
  defaultValueType: [FIELD_DEFAULT_VALUES.NONE],
  customValues: '',
  conditionalValues: [EMPTY_OBJECT],
  delimiter: ',',
};
export const DEFAULT_FIELD_STATE_CUSTOM_GENERAL = {
  [CUSTOM_FORM_FIELDS.FIELD_DEFAULT_STATE]: FIELD_DEFAULT_STATE_VALUES.USER_INPUT,
};
export const DEFAULT_FIELD_STATE_TEXTFORMAT = {
  // maxLength: 4,
  font: 'COURIER',
  size: 10,
  color: DEFAULT_TEXT_COLOR,
  textAlignment: 'LEFT_ALIGNED',
  fontStyles: ['ITALIC', 'BOLD'],
  textAdjustments: ['AUTO_SIZE'],
  visibility: 4, // VISIBLE
  orientation: ORIENTATION_TYPES.ZERO,
};
export const DEFAULT_FIELD_STATE_OFFSET = {
  xOffset: 0,
  yOffset: 0,
};

export const DEFAULT_PDF_NAME = 'Untitled 1';
export const PRINTER_TYPES_OPTIONS = [
  {
    label: __('Impact'),
    value: FORM_PRINTER_TYPES.IMPACT,
  },
  {
    label: __('Laser'),
    value: FORM_PRINTER_TYPES.LASER,
  },
];

export const IMPACT_FONT_VALUES = {
  COURIER: 'COURIER',
  COURIER_BOLD: 'COURIER_BOLD',
  COURIER_OBLIQUE: 'COURIER_OBLIQUE',
  COURIER_BOLD_OBLIQUE: 'COURIER_BOLDOBLIQUE',
};
export const LASER_FONT_VALUES = {
  HELVETICA: 'HELVETICA',
  HELVETICA_BOLD: 'HELVETICA_BOLD',
  HELVETICA_OBLIQUE: 'HELVETICA_OBLIQUE',
  HELVETICA_BOLD_OBLIQUE: 'HELVETICA_BOLDOBLIQUE',
};
export const PRINTER_TYPE_VS_FONTS_OPTIONS = {
  [FORM_PRINTER_TYPES.IMPACT]: [
    {
      label: __('Courier'),
      value: IMPACT_FONT_VALUES.COURIER,
    },
    {
      label: __('Courier Bold'),
      value: IMPACT_FONT_VALUES.COURIER_BOLD,
    },
    {
      label: __('Courier Oblique'),
      value: IMPACT_FONT_VALUES.COURIER_OBLIQUE,
    },
    {
      label: __('Courier Bold Oblique'),
      value: IMPACT_FONT_VALUES.COURIER_BOLD_OBLIQUE,
    },
  ],
  [FORM_PRINTER_TYPES.LASER]: [
    {
      label: __('Helvetica'),
      value: LASER_FONT_VALUES.HELVETICA,
    },
    {
      label: __('Helvetica Bold'),
      value: LASER_FONT_VALUES.HELVETICA_BOLD,
    },
    {
      label: __('Helvetica Oblique'),
      value: LASER_FONT_VALUES.HELVETICA_OBLIQUE,
    },
    {
      label: __('Helvetica Bold Oblique'),
      value: LASER_FONT_VALUES.HELVETICA_BOLD_OBLIQUE,
    },
  ],
};

export const FIELDS_AND_CONFIGS_STYLE_CONFIG_MAPPINGLIST = {
  background: Colors.white,
  height: 40,
};

export const FIELD_TYPE_VALUES = {
  FREE_TEXT: 'FREE_TEXT',
  SINGLE_SELECT: 'SINGLE_SELECT',
  MULTI_SELECT: 'MULTI_SELECT',
  DATE: 'DATE',
  PRICE: 'PRICE',
  CHECKBOX: 'CHECKBOX',
  PHONE: 'PHONE',
  TEXT_AREA: 'TEXT_AREA',
  SIGNATURE: 'SIGNATURE',
  TIMESTAMP: 'TIMESTAMP',
};

export const FIELD_TYPES_VS_RENDERERS = {
  [FIELD_TYPE_VALUES.SINGLE_SELECT]: Select,
  [FIELD_TYPE_VALUES.MULTI_SELECT]: Select,
  [FIELD_TYPE_VALUES.FREE_TEXT]: TextInput,
  [FIELD_TYPE_VALUES.SIGNATURE]: TextInput,
  [FIELD_TYPE_VALUES.PHONE]: TextInput,
  [FIELD_TYPE_VALUES.DATE]: DateInput,
  [FIELD_TYPE_VALUES.PRICE]: PriceInput,
  [FIELD_TYPE_VALUES.CHECKBOX]: CheckBox,
  [FIELD_TYPE_VALUES.TEXT_AREA]: TextArea,
};

export const FIELD_TYPES_REQUIRING_ROOT_PDF_KEY_CHANGE = [FIELD_TYPE_VALUES.SINGLE_SELECT];
export const { REGEX_FOR_VALID_FORMNAME } = FORM_CONSTANTS;

export const { ERRORS } = FORM_CONSTANTS;

// update once backend confirms the values
export const GLOBAL_FIELD_INFO_KEYS = {
  FIELD_KEY: 'fieldAnnotation',
  APPLICATION_DISPLAY_NAME: 'applicationDisplayName',
  APPLICATION_SECTION: 'applicationSection',
  FIELD_EXAMPLE: 'fieldExample',
};

export const GLOBAL_FIELD_INFO_LABELS = {
  [GLOBAL_FIELD_INFO_KEYS.FIELD_KEY]: __('Field Key'),
  [GLOBAL_FIELD_INFO_KEYS.APPLICATION_DISPLAY_NAME]: __('Application Display Name'),
  [GLOBAL_FIELD_INFO_KEYS.APPLICATION_SECTION]: __('Application Section'),
  [GLOBAL_FIELD_INFO_KEYS.FIELD_EXAMPLE]: __('Field Ex'),
};

export const FIELD_TYPE_OPTIONS = [
  {
    label: __('Free Text'),
    value: FIELD_TYPE_VALUES.FREE_TEXT,
  },
  {
    label: __('Single Select'),
    value: FIELD_TYPE_VALUES.SINGLE_SELECT,
  },
  {
    label: __('Multi Select'),
    value: FIELD_TYPE_VALUES.MULTI_SELECT,
  },
  {
    label: __('Date'),
    value: FIELD_TYPE_VALUES.DATE,
  },
  {
    label: __('Price'),
    value: FIELD_TYPE_VALUES.PRICE,
  },
  {
    label: __('Checkbox'),
    value: FIELD_TYPE_VALUES.CHECKBOX,
  },
  {
    label: __('Phone'),
    value: FIELD_TYPE_VALUES.PHONE,
  },
  {
    label: __('Text Area'),
    value: FIELD_TYPE_VALUES.TEXT_AREA,
  },
  {
    label: __('Signature'),
    value: FIELD_TYPE_VALUES.SIGNATURE,
  },
  {
    label: __('Timestamp'),
    value: FIELD_TYPE_VALUES.TIMESTAMP,
  },
];

export const FREE_TEXT_VARIATION_VALUES = {
  LOWER_CASE: 'LOWER_CASE',
  UPPER_CASE: 'UPPER_CASE',
  TITLE_CASE: 'TITLE_CASE',
  SENTENCE_CASE: 'SENTENCE_CASE',
};

export const PRICE_VARIATION_VALUES = {
  WITHOUT_BREAK: '###',
  WITH_DECIMAL: '###.00',
  WITH_BREAK: '###, ###',
  WITH_BREAK_AND_DECIMAL: '###, ###.00',
};

export const DATE_VARIATION_VALUES = {
  ABBR_MONTH_DATE_YEAR: 'MM/dd/yy',
  ABBR_DATE_MONTH_YEAR: 'dd/MM/yy',
  DATE_MONTH_YEAR: 'dd/MM/yyyy',
  MONTH_DATE_YEAR: 'MM/dd/yyyy',
  DETAILED: 'MMM dd, yyyy',
};

export const PHONE_VARIATION_VALUES = {
  HYPHENATED: 'xxx-xxx-xxxx',
  WITH_BRACKETS_AND_TWO_HYPHENS: '(xxx)-xxx-xxxx',
  WITH_BRACKETS_AND_HYPHEN: '(xxx)xxx-xxxx',
  WITH_PLUS_AND_TWO_HYPHENS: '+xxx-xxx-xxxx',
  DEFAULT: 'xxxxxxxxxx',
  WITH_PLUS_SPACE_AND_HYPHEN: '+x xxx xxx-xxxx',
  WITH_PLUS_AND_HYPHEN: '+x (xxx) xxx-xxxx',
};

const NO_FIELD_VARIATION_OPTION = {
  label: __('None'),
  value: 'NONE',
};

const TEXT_FIELD_VARIATION_OPTIONS = [
  {
    label: __('Lower Case'),
    value: FREE_TEXT_VARIATION_VALUES.LOWER_CASE,
  },
  {
    label: __('Upper Case'),
    value: FREE_TEXT_VARIATION_VALUES.UPPER_CASE,
  },
  {
    label: __('Title Case'),
    value: FREE_TEXT_VARIATION_VALUES.TITLE_CASE,
  },
  {
    label: __('Sentence Case'),
    value: FREE_TEXT_VARIATION_VALUES.SENTENCE_CASE,
  },
  NO_FIELD_VARIATION_OPTION,
];

export const FIELD_VARIATION_OPTIONS = {
  [FIELD_TYPE_VALUES.FREE_TEXT]: TEXT_FIELD_VARIATION_OPTIONS,
  [FIELD_TYPE_VALUES.TEXT_AREA]: TEXT_FIELD_VARIATION_OPTIONS,
  [FIELD_TYPE_VALUES.DATE]: [
    {
      label: __('MM/DD/YY'),
      value: DATE_VARIATION_VALUES.ABBR_MONTH_DATE_YEAR,
    },
    {
      label: __('DD/MM/YY'),
      value: DATE_VARIATION_VALUES.ABBR_DATE_MONTH_YEAR,
    },
    {
      label: __('DD/MM/YYYY'),
      value: DATE_VARIATION_VALUES.DATE_MONTH_YEAR,
    },
    {
      label: __('MM/DD/YYYY'),
      value: DATE_VARIATION_VALUES.MONTH_DATE_YEAR,
    },
    {
      label: __('Detailed (Ex: (Jan 21st, 2021))'),
      value: DATE_VARIATION_VALUES.DETAILED,
    },
    NO_FIELD_VARIATION_OPTION,
  ],
  [FIELD_TYPE_VALUES.PRICE]: [
    {
      label: __('$$$'),
      value: PRICE_VARIATION_VALUES.WITHOUT_BREAK,
    },
    {
      label: __('$$$.$$'),
      value: PRICE_VARIATION_VALUES.WITH_DECIMAL,
    },
    {
      label: __('$$$, $$$'),
      value: PRICE_VARIATION_VALUES.WITH_BREAK,
    },
    {
      label: __('$$$, $$$.$$'),
      value: PRICE_VARIATION_VALUES.WITH_BREAK_AND_DECIMAL,
    },
    NO_FIELD_VARIATION_OPTION,
  ],
  [FIELD_TYPE_VALUES.PHONE]: [
    {
      label: __('xxx-xxx-xxxx'),
      value: PHONE_VARIATION_VALUES.HYPHENATED,
    },
    {
      label: __('(xxx)-xxx-xxxx'),
      value: PHONE_VARIATION_VALUES.WITH_BRACKETS_AND_TWO_HYPHENS,
    },
    {
      label: __('(xxx)xxx-xxxx'),
      value: PHONE_VARIATION_VALUES.WITH_BRACKETS_AND_HYPHEN,
    },
    {
      label: __('+xxx-xxx-xxxx'),
      value: PHONE_VARIATION_VALUES.WITH_PLUS_AND_TWO_HYPHENS,
    },
    {
      label: __('xxxxxxxxxx'),
      value: PHONE_VARIATION_VALUES.DEFAULT,
    },
    {
      label: __('+x xxx xxx-xxxx'),
      value: PHONE_VARIATION_VALUES.WITH_PLUS_SPACE_AND_HYPHEN,
    },
    {
      label: __('+x (xxx) xxx-xxxx'),
      value: PHONE_VARIATION_VALUES.WITH_PLUS_AND_HYPHEN,
    },
    NO_FIELD_VARIATION_OPTION,
  ],
};

const TEXT_FIELD_DEFAULT_VARIATIONS = [FREE_TEXT_VARIATION_VALUES.UPPER_CASE];

export const FIELD_DEFAULT_VARIATIONS = {
  FREE_TEXT: TEXT_FIELD_DEFAULT_VARIATIONS,
  TEXT_AREA: TEXT_FIELD_DEFAULT_VARIATIONS,
  PRICE: [PRICE_VARIATION_VALUES.WITH_BREAK_AND_DECIMAL],
  DATE: [DATE_VARIATION_VALUES.MONTH_DATE_YEAR],
  PHONE: [PHONE_VARIATION_VALUES.HYPHENATED],
};

export const DEFAULT_FIELD_STATE_AFFIX = {
  prefix: EMPTY_STRING,
  suffix: EMPTY_STRING,
};

export const CHARACTER_LIMIT_FOR_COMMENTS = 500;

export const FORMS_AUDITS_FILTER_TYPES = {
  filterTypes: [
    {
      id: 'fontType',
      name: __('Font Type'),
      type: FILTER_TYPES.MULTI_SELECT,
    },
    {
      id: 'size',
      name: __('Font Size'),
      type: FILTER_TYPES.MULTI_SELECT,
    },
    {
      id: 'textAlignment',
      name: __('Text Alignment'),
      type: FILTER_TYPES.MULTI_SELECT,
    },
    {
      id: 'printerType',
      name: __('Printer Type'),
      type: FILTER_TYPES.MULTI_SELECT,
    },
    {
      id: 'fieldType',
      name: __('Field Type'),
      type: FILTER_TYPES.MULTI_SELECT,
    },
  ],
};

export const DEFAULT_FIELD_VALUES = {
  [FIELD_TYPE_VALUES.FREE_TEXT]: EMPTY_STRING,
  [FIELD_TYPE_VALUES.SINGLE_SELECT]: EMPTY_STRING,
  [FIELD_TYPE_VALUES.MULTI_SELECT]: EMPTY_STRING,
  [FIELD_TYPE_VALUES.DATE]: EMPTY_STRING,
  [FIELD_TYPE_VALUES.PRICE]: 0,
  [FIELD_TYPE_VALUES.CHECKBOX]: EMPTY_STRING,
  [FIELD_TYPE_VALUES.PHONE]: EMPTY_STRING,
  [FIELD_TYPE_VALUES.TEXT_AREA]: EMPTY_STRING,
  [FIELD_TYPE_VALUES.SIGNATURE]: EMPTY_STRING,
};

export const MULTI_LINGUAL_FIELD_IDS = {
  DISPLAY_NAME: 'displayName',
  SECTION_NAME: 'displayName',
};

export const SAVE_AS_MODAL_ADDITIONAL_PROPS = {
  saveFormModalTitle: __('Save'),
  saveCommentLabel: __('Reason for Save'),
  saveAsModalSubmitBtnText: __('Save'),
  saveAsModalDescription: __('Your changes will not be applied to the form if you don’t save them'),
};

export const EDIT_TYPE_PERMISSIONS = {
  GLOBAL_EDIT: 'GLOBAL_EDIT',
  TEKION_RESTRICTED_EDIT: 'TEKION_RESTRICTED_EDIT',
  DP_RESTRICTED_EDIT: 'DP_RESTRICTED_EDIT',
  NO_EDIT: 'NO_EDIT',
};

export const DEFAULT_PAGES = [PAGE_TYPES.PREPARE, PAGE_TYPES.SETUP];

export const DELTA_CONSTANT = 0.03;

export const ASSET_TYPES_LIST = [
  AUDIT_ID,
  AUDIT_ID_FORMS_LIBRARY_FORM,
  AUDIT_ID_FORM_ANNOTATION,
  AUDIT_ID_FORMS_LIBRARY_FORM_SETUP,
  AUDIT_ID_FORM_ACTION,
  AUDIT_ID_FORMS_IN_PACKAGE,
];

export const PTS_TO_MM_CONSTANT = 0.352778;
