import React, { Component } from 'react';
import cx from 'classnames';
import { defaultMemoize } from 'reselect';
import PropTypes from 'prop-types';

import _noop from 'lodash/noop';
import _map from 'lodash/map';
import _keys from 'lodash/keys';
import _get from 'lodash/get';
import _size from 'lodash/size';
import _forEach from 'lodash/forEach';
import _findIndex from 'lodash/findIndex';
import _filter from 'lodash/filter';
import _throttle from 'lodash/throttle';
import _includes from 'lodash/includes';
import _uniqBy from 'lodash/uniqBy';
import _isEmpty from 'lodash/isEmpty';
import _head from 'lodash/head';
import _keyBy from 'lodash/keyBy';

import { tget } from '@tekion/tekion-base/utils/general';
import { EMPTY_STRING, EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import {
  AUDIT_ID,
  AUDITS_LABEL,
  AUDIT_FALLBACK_CONFIG,
  FORM_STATE_CONFIG_MAP,
  FORM_STATE,
} from '@tekion/tekion-base/constants/formConfigurator/constants';
import { FORM_PRINTER_VS_PRINTER_TYPES } from '@tekion/tekion-base/constants/formConfigurator/printers';
import { DEFAULT_OPTIONS } from '@tekion/tekion-components/src/hoc/withSize';
import PageHeader from '@tekion/tekion-components/src/molecules/pageComponent/PageHeader';
import Spinner from '@tekion/tekion-components/src/molecules/SpinnerComponent/Spinner';
import Button from '@tekion/tekion-components/src/atoms/Button';
import FontIcon, { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';
import Content from '@tekion/tekion-components/src/atoms/Content';
import TextArea from '@tekion/tekion-components/src/atoms/TextArea';
import Input from '@tekion/tekion-components/src/molecules/Input';
import IconWithText from '@tekion/tekion-components/src/atoms/IconWithText';
import StatusItem from '@tekion/tekion-components/src/atoms/StatusItem';
import SaveComponent from '@tekion/tekion-components/src/molecules/SaveComponent';
import Modal from '@tekion/tekion-components/src/molecules/Modal';
import ConfirmationDialog from '@tekion/tekion-components/src/molecules/confirmationDialog';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import KebabMenu from '@tekion/tekion-components/src/molecules/KebabMenu';
import {
  getFormsProAuditFormatters,
  getAuditFilters,
} from '@tekion/tekion-business/src/utils/formConfigurator/formConfigurator.utils';

import AuditLogs from '../../appServices/service/organisms/AuditLogs';
import ACTION_TYPES from './FormConfiguratorTool.actionTypes';
import HeaderInputToggleCell from './components/HeaderInputToggleCell';
import FooterStepIndicator from './components/FooterStepIndicator/FooterStepIndicator';
import {
  KEYBOARD_KEY_CODES,
  PAGE_TYPES,
  ERRORS,
  MOVE_SELECTED_FIELDS,
  ARROW_KEYS,
  FORMS_AUDITS_FILTER_TYPES,
  SAVE_AS_MODAL_ADDITIONAL_PROPS,
  DEFAULT_PAGES,
  ASSET_TYPES_LIST,
} from './FormConfiguratorTool.constants';
import { PAGE_COMPONENT_MAP } from './constants/formsConfiguratorTool';
import {
  checkForFieldsInSinglePage,
  getParamsFromUrlHash,
  getSetupRequiredFields,
  isValidAnnotationFields,
  moveSelectedFieldsBy,
  shouldDisableSaveAsNewAction,
  disableFieldBasedOnPermission,
} from './FormConfiguratorTool.utils';
import ValidateFormModal from './components/ValidateFormModal/ValidateFormModal';
import FormConfiguratorContext, { Consumer } from './formConfiguratorTool.context';
import { makeLicenseProviderKeys, shouldActionDisabled } from './utils/FormConfigurator.utils';
import FormFormulaManager from './FormFormulaManager';
import CreateForm from './components/CreateForm/CreateFormModal';
import AddFormLiteDrawer from '../addFormLiteDrawer';
import ConfigReader from './readers/config.reader';
import { getCustomRequiredFields, getDynamicUsageCategoryVsRulesOptions } from '../AddForm/AddForm.utils';
import formConfiguratorToolAPI from './FormConfiguratorTool.api';

import styles from './formConfiguratorTool.module.scss';

const EXIT_EDITOR_BODY_STYLE = { height: '20rem' };

class FormConfiguratorTool extends Component {
  static contextType = FormConfiguratorContext;

  constructor(props) {
    super(props);
    FormFormulaManager.removeAllFields();
  }

  async componentDidMount() {
    const { onAction, isGlobalForm } = this.props;
    const pageType = this.getPageType();
    onAction({
      type: ACTION_TYPES.ON_PAGE_MOUNT,
      payload: { pageType, isGlobalForm },
    });
    document.addEventListener('keydown', this.handleKeydown);
    window.addEventListener('paste', this.onAnnotationsPaste);
  }

  componentDidUpdate() {
    const { onAction, pageType: previousPageType } = this.props;
    const pageType = this.getPageType();
    if (previousPageType !== pageType) {
      onAction({
        type: ACTION_TYPES.ON_PAGE_TYPE_CHANGE,
        payload: { pageType },
      });
    }
  }

  componentWillUnmount() {
    document.removeEventListener('keydown', this.handleKeydown);
    window.removeEventListener('paste', this.onAnnotationsPaste);
  }

  getMenuItems = () => [
    {
      key: 'auditLogs',
      label: __('Audit Logs'),
    },
    { key: 'download', label: __('Download') },
  ];

  downloadFormPdf = () => {
    const { onAction, formId: formKey } = this.props;
    onAction({
      type: ACTION_TYPES.ON_DOWNLOAD_FORM_PDF,
      payload: {
        formKey,
      },
    });
  };

  onClickRowAction = key => {
    if (key === 'auditLogs') {
      this.showAuditLogsDrawer();
    }

    if (key === 'download') {
      this.downloadFormPdf();
    }
  };

  getPageType() {
    const { location, config, pages, isFcLiteModeEnabled } = this.props;

    if (isFcLiteModeEnabled) {
      window.location.hash = ``;
      return PAGE_TYPES.PREPARE;
    }

    const hash = tget(location, 'hash');
    const hashParams = getParamsFromUrlHash(hash);
    if (PAGE_TYPES[hashParams?.pageType]) return PAGE_TYPES[hashParams?.pageType];

    const firstPage = _head(pages) || PAGE_TYPES.PREPARE;

    if (!ConfigReader.hidePageTypeInformation(config)) {
      window.location.hash = `#pageType=${firstPage}`;
    }

    return PAGE_TYPES[hashParams?.pageType] || firstPage;
  }

  showAuditLogsDrawer = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.SHOW_AUDIT_LOGS_DRAWER,
    });
  };

  toggleAuditLogs = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.TOGGLE_AUDIT_LOGS,
    });
  };

  handleCloseValidateFormModalClick = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_CLOSE_VALIDATE_FORM_MODAL,
    });
  };

  onValidateFormClick = () => {
    const { onAction, formInfo } = this.props;
    const { resetPreviewUrls } = this.context;
    if (resetPreviewUrls) resetPreviewUrls();
    onAction({
      type: ACTION_TYPES.ON_VALIDATE_FORM_MODAL_CLICK,
      payload: {
        mediaId: _get(formInfo, 'mediaId', EMPTY_STRING),
      },
    });
  };

  onPreviewClick = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.SHOW_PREVIEW_MODEL,
    });
  };

  onPrinterButtonClick = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_PRINTER_BUTTON_CLICK,
    });
  };

  onFormSettingsButtonClick = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.SHOW_FORM_SETTINGS_SIDEBAR,
    });
  };

  renderFooterPageToggle = () => {
    const { pageType, config, pages } = this.props;
    const hideAdditionalFooterDetail = ConfigReader.hideAdditionalFooterDetail(config);
    if (hideAdditionalFooterDetail) return null;
    return (
      <div className={styles.footerToggleWithAction}>
        <FooterStepIndicator pages={pages} pageType={pageType} />
      </div>
    );
  };

  // eslint-disable-next-line react/sort-comp
  moveSelectedFields = _throttle(key => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.UPDATE_SELECTED_FIELDS,
      payload: { updateType: MOVE_SELECTED_FIELDS, values: moveSelectedFieldsBy(key) },
    });
  }, 250);

  onAnnotationsPaste = event => {
    const { onAction } = this.props;
    const selectedFieldsString = event.clipboardData?.getData('text/plain');
    try {
      const selectedAnnotations = JSON.parse(selectedFieldsString);
      if (isValidAnnotationFields(selectedAnnotations)) {
        event.preventDefault();
        if (_get(event, 'target.tagName') !== 'INPUT') {
          onAction({ type: ACTION_TYPES.PASTE_ANNOTATION_FIELDS, payload: { selectedAnnotations } });
        }
      }
    } catch {}
  };

  handleKeydown = async event => {
    const { onAction, annotatedFields, selectedFields } = this.props;
    const isShiftClicked = event.shiftKey === true;
    const isCtrlClicked = event.ctrlKey === true || event.metaKey === true;
    if (event.key === KEYBOARD_KEY_CODES.ESCAPE) {
      onAction({ type: ACTION_TYPES.CLEAR_SELECTED_FIELDS });
    } else if (event.keyCode === KEYBOARD_KEY_CODES.Z && !isShiftClicked && isCtrlClicked) {
      // Undo on command+z
      onAction({ type: ACTION_TYPES.UNDO_LAST_ACTION });
    } else if (event.keyCode === KEYBOARD_KEY_CODES.Z && isShiftClicked && isCtrlClicked) {
      // Redo on command+shift+z
      onAction({ type: ACTION_TYPES.REDO_LAST_ACTION });
    } else if (_includes(ARROW_KEYS, event.key)) {
      if (checkForFieldsInSinglePage(annotatedFields, selectedFields)) {
        this.moveSelectedFields(event.key);
      }
    } else if (event.keyCode === KEYBOARD_KEY_CODES.C && !isShiftClicked && isCtrlClicked) {
      if (_size(selectedFields) > 0 && _isEmpty(window.getSelection().toString())) {
        event.preventDefault();
        onAction({ type: ACTION_TYPES.COPY_ANNOTATION_FIELDS });
      }
    }
  };

  renderFooterPageToggleWithAddtionalButton = () => {
    const { pageType, pages } = this.props;
    return (
      <div className={styles.footerToggleWithAction}>
        <FooterStepIndicator pages={pages} pageType={pageType} />
        <Button view={Button.VIEW.SECONDARY} className={styles.additionalFooterButton} onClick={this.onCancel}>
          {__('Cancel')}
        </Button>
      </div>
    );
  };

  acceptAgreement = () => {
    const { onAction, pageType, pages } = this.props;
    const currentPage = _findIndex(pages, page => page === pageType);
    const nextPage = _get(pages, [currentPage + 1], pageType);

    onAction({
      type: ACTION_TYPES.ON_ACCEPT_AGREEMENT,
      payload: { nextPage },
    });
  };

  saveAnnotations = () => {
    const { onAction, formInfo } = this.props;
    // @todo handle how to call new API
    onAction({
      type: ACTION_TYPES.ON_SAVE_ANNOTATIONS,
      payload: {
        mediaId: _get(formInfo, 'mediaId', EMPTY_STRING),
        callback: this.togglePrepareSetupScreen,
      },
    });
  };

  onCancel = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_CANCEL,
    });
  };

  togglePrepareSetupScreen = formId => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.TOGGLE_PREPARE_SETUP_SCREEN,
      payload: {
        formId,
      },
    });

    onAction({
      type: ACTION_TYPES.RESET_PDF_LAYOUT_INFO,
    });
  };

  onBackClick = () => {
    const { pageType, isLoadingSetup } = this.props;

    if (pageType === PAGE_TYPES.SETUP) {
      if (isLoadingSetup) return;
      this.togglePrepareSetupScreen();
    } else {
      this.onPrepareCancelClick();
    }
  };

  submitFormSetup = () => {
    const { onAction, isFcLiteModeEnabled } = this.props;

    if (isFcLiteModeEnabled) {
      this.saveFcLiteConfig();
      return;
    }

    onAction({
      type: ACTION_TYPES.HANDLE_SUBMIT_FORM_SETUP,
      payload: {
        dynamicUsageCategoryVsRules: this.getDynamicOptionsForTargeting(),
      },
    });
  };

  submitAsNewFormSetup = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.HANDLE_SUBMIT_FORM_SETUP,
      payload: {
        submitAsNew: true,
        dynamicUsageCategoryVsRules: this.getDynamicOptionsForTargeting(),
      },
    });
  };

  showSaveConfirmationModal = () => {
    const { onAction } = this.props;

    // @todo handle how to show the pick dealerships modal & new APIs
    onAction({
      type: ACTION_TYPES.SHOW_SAVE_CONFIRMATION_MODAL,
    });
  };

  showSaveAsNewConfirmationModal = () => {
    const { onAction } = this.props;

    onAction({
      type: ACTION_TYPES.SHOW_SAVE_AS_NEW_CONFIRMATION_MODAL,
    });
  };

  getAnnotationsfields = annotatedFieldsObj =>
    _map(_keys(annotatedFieldsObj), fieldId => {
      const field = annotatedFieldsObj[fieldId];
      return field;
    });

  handleAssetRequest = assetId =>
    _map(ASSET_TYPES_LIST, assetType => ({
      assetType,
      assetId,
    }));

  getAuditTabs = (filters, assetId) => [
    {
      id: AUDIT_ID,
      label: AUDITS_LABEL,
      filterTypes: getAuditFilters(filters),
      requests: this.handleAssetRequest(assetId),
    },
  ];

  onPrepareCancelClick = () => {
    const { formInfo, isCreating, isLoadingPrepare, canPerformUndo, config } = this.props;
    const onCancelClick = ConfigReader.onCancelClick(config);
    if (isLoadingPrepare) return;
    if (onCancelClick) {
      onCancelClick();
      return;
    }
    let showExitEditorModal = false;
    if (isCreating) {
      showExitEditorModal = Boolean(_get(formInfo, 'mediaId', EMPTY_STRING));
    } else {
      showExitEditorModal = Boolean(_get(formInfo, 'mediaId', EMPTY_STRING)) && canPerformUndo;
    }
    if (showExitEditorModal) {
      this.showExitEditorModal();
    } else {
      this.onCancel();
    }
  };

  saveFcLiteConfig = () => {
    const { onAction, formInfo } = this.props;

    onAction({
      type: ACTION_TYPES.ON_FORM_SINGLE_SAVE,
      payload: {
        dynamicUsageCategoryVsRules: this.getDynamicOptionsForTargeting(),
        mediaId: _get(formInfo, 'mediaId', EMPTY_STRING),
      },
    });
  };

  renderFooter = () => {
    const {
      pageType,
      pageDimensions,
      totalPages,
      isSavingAnnotations = false,
      hasViewOnlyPermission,
      config,
      pdfLoaded,
      formPrinterType,
      isFcLiteModeEnabled,
    } = this.props;
    const prepareSaveButtonProps = ConfigReader.prepareSaveButtonProps(config) || EMPTY_OBJECT;
    const prepareCancelButtonProps = ConfigReader.prepareCancelButtonProps(config) || EMPTY_OBJECT;

    if (hasViewOnlyPermission) {
      return (
        <SaveComponent
          onSecondaryAction={this.onBackClick}
          primaryActionLoading={false}
          secondaryButtonLabel={__('Close')}
          showPrimaryButton={false}
          showSecondaryButton
        />
      );
    }

    if (isFcLiteModeEnabled) {
      return (
        <SaveComponent
          onPrimaryAction={this.showSaveConfirmationModal}
          onSecondaryAction={this.onCancel}
          primaryActionLoading={false}
          primaryButtonLabel={__('Save')}
          secondaryButtonLabel={__('Cancel')}
          showPrimaryButton
          showSecondaryButton
        />
      );
    }

    if (pageType === PAGE_TYPES.AGREEMENT) {
      return (
        <SaveComponent
          renderAdditionalFooterDetail={this.renderFooterPageToggle}
          onPrimaryAction={this.acceptAgreement}
          onSecondaryAction={this.onCancel}
          onTertiaryAction={this.onCancel}
          primaryButtonLabel={__('Accept')}
          secondaryButtonLabel={__('Reject')}
          showSecondaryButton
          primaryActionLoading={false}
          showTertiaryButton={false}
          isPrimaryDisabled={false}
          {...prepareSaveButtonProps}
          {...prepareCancelButtonProps}
        />
      );
    }

    if (pageType === PAGE_TYPES.PREPARE) {
      return (
        <SaveComponent
          renderAdditionalFooterDetail={this.renderFooterPageToggle}
          onPrimaryAction={this.saveAnnotations}
          onSecondaryAction={this.onPrepareCancelClick}
          onTertiaryAction={this.onBackClick}
          primaryActionLoading={false}
          primaryButtonLabel={__('Next')}
          secondaryButtonLabel={__('Cancel')}
          tertiaryButtonLabel={__('Back')}
          showSecondaryButton
          showTertiaryButton={false} // show based on some flag
          isPrimaryDisabled={!(_size(pageDimensions) === totalPages) || isSavingAnnotations || !pdfLoaded}
          {...prepareSaveButtonProps}
          {...prepareCancelButtonProps}
        />
      );
    }

    const { formValues, errors, showSaveAsNew, isLoadingSetup, isSaveAsNewDisabled, econtractForm, isSaveDisabled } =
      this.props;
    const isInvalid = shouldActionDisabled(
      formValues,
      errors,
      getCustomRequiredFields({
        formValues,
        econtractForm,
        requiredFormFields: getSetupRequiredFields(),
      })
    );

    const { printerType } = formValues;
    const isPrinterTypeInValid = !_includes(FORM_PRINTER_VS_PRINTER_TYPES[formPrinterType], printerType);

    return (
      <SaveComponent
        renderAdditionalFooterDetail={this.renderFooterPageToggleWithAddtionalButton}
        onPrimaryAction={this.showSaveConfirmationModal}
        onSecondaryAction={this.showSaveAsNewConfirmationModal}
        onTertiaryAction={this.togglePrepareSetupScreen}
        primaryActionLoading={false}
        primaryButtonLabel={__('Save')}
        secondaryButtonLabel={__('Save as New')}
        tertiaryButtonLabel={__('Back')}
        showSecondaryButton={showSaveAsNew}
        showTertiaryButton={!isLoadingSetup}
        isPrimaryDisabled={isInvalid || isSaveDisabled || isPrinterTypeInValid}
        isSecondaryDisabled={isInvalid || isSaveAsNewDisabled || isPrinterTypeInValid}
      />
    );
  };

  // Check when to render
  renderHeaderActions = () => {
    const {
      enableFormsPreview,
      config,
      hideFormPrinterTypeSelection,
      isFormsLibrary,
      editType,
      isFcLiteModeEnabled,
      hasViewOnlyPermission,
    } = this.props;
    const hideAuditLogs = ConfigReader.hideAuditLogs(config);
    return (
      <div className={styles.headerActionsWrapper}>
        <PropertyControlledComponent controllerProperty={!hideFormPrinterTypeSelection && !isFcLiteModeEnabled}>
          <Button
            view={Button.VIEW.TERTIARY}
            onClick={this.onPrinterButtonClick}
            disabled={disableFieldBasedOnPermission({
              isFormsLibrary,
              editType,
            })}
            className={styles.headerActionsButton}>
            <FontIcon
              size={SIZES.MD_S}
              className={cx('m-r-8', styles.headerActionsButtonIcon, {
                [styles.headerActionsButtonDisabled]: disableFieldBasedOnPermission({
                  isFormsLibrary,
                  editType,
                }),
              })}>
              icon-printer2
            </FontIcon>
            <Content
              className={cx('m-r-8', styles.headerActionsButtonTextStyle, {
                [styles.headerActionsButtonDisabled]: disableFieldBasedOnPermission({
                  isFormsLibrary,
                  editType,
                }),
              })}>
              {__('Printer Type')}
            </Content>
          </Button>
        </PropertyControlledComponent>

        {isFcLiteModeEnabled && (
          <Button
            view={Button.VIEW.TERTIARY}
            onClick={this.onFormSettingsButtonClick}
            className={styles.headerActionsButton}>
            <FontIcon size={SIZES.MD_S} className={styles.headerActionsButtonIcon}>
              icon-settings
            </FontIcon>
            <Content className={styles.headerActionsButtonTextStyle}>{__('Form Settings')}</Content>
          </Button>
        )}
        <Button
          view={Button.VIEW.TERTIARY}
          onClick={this.onValidateFormClick}
          disabled={!enableFormsPreview}
          className={styles.headerActionsButton}>
          <FontIcon
            size={SIZES.MD_S}
            className={cx('m-r-8', styles.headerActionsButtonIcon, {
              [styles.headerActionsButtonDisabled]: !enableFormsPreview,
            })}>
            icon-printer
          </FontIcon>
          <Content
            className={cx('m-r-8', styles.headerActionsButtonTextStyle, {
              [styles.headerActionsButtonDisabled]: !enableFormsPreview,
            })}>
            {__('Test Print')}
          </Content>
        </Button>
        <PropertyControlledComponent controllerProperty={!hideAuditLogs}>
          <KebabMenu
            className={styles.headerKebabMenu}
            onClickAction={this.onClickRowAction}
            menuItems={this.getMenuItems()}
            triggerElement={<span className="icon-overflow pointer" />}
          />
        </PropertyControlledComponent>
      </div>
    );
  };

  handleGlobalFormNameChange = value => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.HANDLE_FORM_NAME_CHANGE,
      payload: { value },
    });
  };

  handleEditFormName = isEditingFormName => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.EDIT_FORM_NAME,
      payload: { isEditingFormName },
    });
  };

  renderHeader = () => {
    const {
      globalFormName,
      formState,
      formInfo,
      isValidFormName,
      isEditingFormName,
      pageType,
      hasViewOnlyPermission,
      isFormsLibrary,
      isFcLiteModeEnabled,
    } = this.props;
    const isPrepareScreen = pageType === PAGE_TYPES.PREPARE;
    const formURL = _get(formInfo, 'url', null);
    const isPDFuploaded = !formURL;
    const status = FORM_STATE_CONFIG_MAP[formState];
    const isAgreementPage = pageType === PAGE_TYPES.AGREEMENT;
    const canEdit = hasViewOnlyPermission || isAgreementPage || isFcLiteModeEnabled;

    return (
      <PageHeader hasBack goBackHandler={this.onBackClick}>
        <div className={styles.headerWrapper}>
          <div className={styles.formInfo}>
            <HeaderInputToggleCell
              value={globalFormName}
              onChange={this.handleGlobalFormNameChange}
              disabled={isPDFuploaded}
              disableEditing={canEdit}
              isValidFormName={isValidFormName}
              isEditingFormName={isEditingFormName}
              handleEditFormName={this.handleEditFormName}
            />
            {!isAgreementPage && !isFcLiteModeEnabled && (
              <StatusItem label={status.label} color={status.color} statusClass={styles.statusItem} />
            )}
            <PropertyControlledComponent controllerProperty={hasViewOnlyPermission}>
              <div className={styles.viewOnlyModeHeader}>{__('View Only Mode')}</div>
            </PropertyControlledComponent>
          </div>
          <PropertyControlledComponent controllerProperty={isPrepareScreen}>
            {this.renderHeaderActions()}
          </PropertyControlledComponent>
        </div>
      </PageHeader>
    );
  };

  getDynamicOptionsForTargeting = () => {
    const {
      fniOptions,
      dueBills,
      fuelTypes,
      lenderTypes,
      dealTypeConfigs,
      vehicleTypes,
      customStatus,
      siteOptions,
      models,
      trimsList,
      makes,
      tekionMakes,
      stateListOptions,
      paymentOptionConfigs,
      dealStatus,
      tradeInCount,
    } = this.props;
    return getDynamicUsageCategoryVsRulesOptions({
      fnIs: fniOptions,
      dueBills,
      states: stateListOptions,
      makes,
      tekionMakes,
      models,
      fuelTypes,
      lenderTypes,
      dealTypeConfigs,
      vehicleTypes,
      customStatus,
      siteOptions,
      trimsList,
      paymentOptionConfigs,
      dealStatus,
      tradeInCount,
    });
  };

  renderBody = () => {
    const { contentHeight, pageType, config } = this.props;
    const ComponentToRender = PAGE_COMPONENT_MAP[pageType];
    const additionalProps =
      pageType === PAGE_TYPES.PREPARE
        ? EMPTY_OBJECT
        : { dynamicUsageCategoryVsRules: this.getDynamicOptionsForTargeting() };
    const hideHeader = ConfigReader.hideHeader(config);
    const customHeaderHeight = ConfigReader.customHeaderHeight(config);
    let height = contentHeight;
    if (hideHeader) height += DEFAULT_OPTIONS.headerHeight;
    if (customHeaderHeight) height -= customHeaderHeight;
    return (
      <div style={{ height }} className={styles.bodyWrapper}>
        <ComponentToRender {...this.props} {...additionalProps} />
      </div>
    );
  };

  handleOnCommentChange = event => {
    const { value } = event.target;
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_FORM_SETUP_COMMENT_CHANGE,
      payload: {
        saveWithReasonForPublish: value,
        isSaveAsNewForm: false,
      },
    });
  };

  renderSavePromptContent = ({
    globalFormName,
    saveWithReasonForPublish,
    saveWithReasonForPublishErrorMessage,
    isGlobalForm,
    saveCommentLabel,
    saveAsModalDescription,
  }) => (
    <div>
      <div>
        <p className={styles.savePromptTitle}>{`${__('Want to save your changes to')} ${globalFormName}?`}</p>
        <PropertyControlledComponent controllerProperty={!isGlobalForm}>
          <p>{saveAsModalDescription}</p>
        </PropertyControlledComponent>
      </div>
      <TextArea
        rows={3}
        label={saveCommentLabel}
        placeholder={__('Type reason..')}
        required
        value={saveWithReasonForPublish}
        onChange={this.handleOnCommentChange}
        containerClassName="p-t-8 p-b-24"
        error={saveWithReasonForPublishErrorMessage}
        errorClass={styles.inputBoxErrorClass}
      />
      <IconWithText
        textContent={__('The previous version can be accessed in the Version History.')}
        icon="icon-information-filled"
        iconClassName={styles.infoIconClassName}
        iconSize={SIZES.S}
        className={styles.iconWithTextWrapper}
      />
    </div>
  );

  handleOnFormNameChange = event => {
    const { value } = event.target;
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_SAVE_AS_NEW_FORM_NAME_CHANGE,
      payload: {
        saveAsNewName: value,
      },
    });
  };

  handleSaveAsNewCommentChange = event => {
    const { value } = event.target;
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_FORM_SETUP_COMMENT_CHANGE,
      payload: {
        saveAsNewReasonForPublish: value,
        isSaveAsNewForm: true,
      },
    });
  };

  getSaveFormDetailsPayload = () => {
    const { configs, fieldsForValidateForm, labels } = this.props;
    const configsForPayload = _map(configs, config => {
      const parsedData = {
        mergeAllConfig: config.mergeAllConfig,
      };
      _forEach(config.groupedFieldConfigs || [], ({ configFieldType, id, ...rest }) => {
        if (configFieldType) {
          parsedData[configFieldType] = rest;
        }
      });
      return parsedData;
    });
    let fieldsForPayload = _filter(
      _forEach(fieldsForValidateForm || []),
      ({ pdfKey, displayName }) => pdfKey || displayName
    );
    fieldsForPayload = _map(fieldsForPayload, ({ options, ...rest }) => ({
      ...rest,
      filedName: _get(rest, 'pdfKey'),
      options: _filter(options || [], ({ pdfKey, display }) => pdfKey || display),
    }));
    return {
      ...this.formDetails,
      configs: configsForPayload,
      fields: fieldsForPayload,
      labels,
    };
  };

  renderSaveAsNewPromptContent = () => {
    const {
      saveAsNewName,
      isValidFormName,
      saveAsNewReasonForPublish,
      saveAsNewReasonForPublishErrorMessage,
      saveCommentLabel,
    } = this.props;
    let errorMessage = '';
    if (!isValidFormName) {
      errorMessage = ERRORS.INVALID_FILE_NAME;
    }
    return (
      <>
        <Input
          label={__('Form Name')}
          required
          value={saveAsNewName}
          onChange={this.handleOnFormNameChange}
          errorInPopover={!isValidFormName}
          error={errorMessage}
        />
        <Input
          label={saveCommentLabel}
          placeholder={__('Type reason..')}
          required
          value={saveAsNewReasonForPublish}
          onChange={this.handleSaveAsNewCommentChange}
          containerClassName="p-t-24"
          error={saveAsNewReasonForPublishErrorMessage}
        />
      </>
    );
  };

  hideSaveModal = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.HIDE_SAVE_CONFIRMATION_MODAL,
    });
  };

  hideSaveAsNewModal = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.HIDE_SAVE_AS_NEW_CONFIRMATION_MODAL,
    });
  };

  showExitEditorModal = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.SHOW_EXIT_EDITOR_MODAL,
    });
  };

  hideExitEditorModal = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.HIDE_EXIT_EDITOR_MODAL,
    });
  };

  onSaveAsDraftClick = () => {
    const { onAction, formInfo, isSavingAnnotations } = this.props;
    if (isSavingAnnotations) return;
    onAction({
      type: ACTION_TYPES.ON_SAVE_ANNOTATIONS,
      payload: {
        mediaId: _get(formInfo, 'mediaId', EMPTY_STRING),
        callback: this.onSaveAsDraftSuccess,
      },
    });
  };

  onSaveAsDraftSuccess = formId => {
    const { onAction } = this.props;
    if (formId)
      onAction({
        type: ACTION_TYPES.ON_CANCEL,
      });
  };

  handleCreateFormSubmit = formData => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_SUBMIT_CREATE_FORM,
      payload: { formData },
    });
  };

  onApplyAndValidate = ({ fields, validatePreviewId, validatePreviewBy, requestId, dealersListforPreview }) => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_APPLY_AND_VALIDATE,
      payload: { fields, validatePreviewId, validatePreviewBy, requestId, dealersListforPreview },
    });
  };

  onSelectEnv = e => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.SELECT_ENV_FOR_PREVIEW,
      payload: { env: e.target.value },
    });
  };

  onSelectDealer = selectedDealer => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.SELECT_DEALER_FOR_PREVIEW,
      payload: { selectedDealer },
    });
  };

  onSearchDealer = searchText => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.SEARCH_DEALER_FOR_PREVIEW,
      payload: { searchText },
    });
  };

  renderValidateFormModal = () => {
    const {
      isValidateFormModalVisible,
      formIdForValidateForm,
      formDetailsForValidateForm,
      formInfo,
      config,
      envForPreview,
      selectedDealer,
      dealersListforPreview,
      isGlobalForm,
      isFormsLibrary,
      hasViewOnlyPermission,
    } = this.props;
    const customAnnotations = _uniqBy(formDetailsForValidateForm?.fields, 'filedName');
    const fieldDisplaySections = formDetailsForValidateForm?.fieldDisplaySections;
    const validateFormProps = ConfigReader.validateFormProps(config);
    return (
      <Consumer>
        {({ previewUrls }) => (
          <PropertyControlledComponent controllerProperty={isValidateFormModalVisible}>
            <ValidateFormModal
              showModal={isValidateFormModalVisible}
              toggleModal={this.handleCloseValidateFormModalClick}
              fields={customAnnotations || EMPTY_ARRAY}
              formId={formIdForValidateForm}
              getFormDetailsPayload={this.getSaveFormDetailsPayload}
              previewUrls={previewUrls}
              className={styles.validateForm}
              onApplyAndValidate={this.onApplyAndValidate}
              fieldDisplaySections={fieldDisplaySections}
              formName={_get(formInfo, 'name')}
              onSelectEnv={this.onSelectEnv}
              envForPreview={envForPreview}
              selectedDealer={selectedDealer}
              onSelectDealer={this.onSelectDealer}
              onSearchDealer={this.onSearchDealer}
              dealersListforPreview={dealersListforPreview}
              isGlobalForm={isGlobalForm}
              isDisableFormFields={!isFormsLibrary && hasViewOnlyPermission}
              {...validateFormProps}
            />
          </PropertyControlledComponent>
        )}
      </Consumer>
    );
  };

  renderPrinterModal = () => {
    const { isCreateFormModalOpen, formPrinterType, onAction, config } = this.props;
    return (
      <CreateForm
        isCreateFormModalOpen={isCreateFormModalOpen}
        handleCreateFormSubmit={this.handleCreateFormSubmit}
        formPrinterType={formPrinterType}
        onAction={onAction}
        config={config}
      />
    );
  };

  renderEditorExitFooterLeftSection = () => (
    <Button view={Button.VIEW.TERTIARY} onClick={this.hideExitEditorModal}>
      {__('Cancel')}
    </Button>
  );

  renderEditorExitFooterRightSection = () => (
    <Button view={Button.VIEW.SECONDARY} onClick={this.onCancel}>
      {__('Don’t Save & Exit')}
    </Button>
  );

  renderConfirmationDialogs = () => {
    const {
      globalFormName,
      isSaveConfirmationModalVisible,
      isSaveAsNewConfirmationModalVisible,
      showExitEditorModal,
      saveWithReasonForPublish,
      saveWithReasonForPublishErrorMessage,
      saveAsNewName,
      saveAsNewReasonForPublish,
      isGlobalForm,
      saveFormModalTitle,
      saveAsNewFormModalTitle,
      modalSubmitBtnText,
      saveCommentLabel,
      isFormSubmitInProgress,
      saveAsNewReasonForPublishErrorMessage,
      saveAsModalSubmitBtnText,
      saveAsModalDescription,
      isPublishedForm,
    } = this.props;
    const SavePromptContent = this.renderSavePromptContent;
    const SaveAsNewPromptContent = this.renderSaveAsNewPromptContent;
    let saveAsModalAdditionalProps = {};
    if (isPublishedForm) {
      saveAsModalAdditionalProps = SAVE_AS_MODAL_ADDITIONAL_PROPS;
    }
    return (
      <>
        <ConfirmationDialog
          title={_get(saveAsModalAdditionalProps, 'saveFormModalTitle', saveFormModalTitle)}
          isVisible={isSaveConfirmationModalVisible}
          width={Modal.SIZES.SM}
          destroyOnClose
          onCancel={this.hideSaveModal}
          onSubmit={this.submitFormSetup}
          secondaryBtnText={__('Cancel')}
          submitBtnText={_get(saveAsModalAdditionalProps, 'saveAsModalSubmitBtnText', saveAsModalSubmitBtnText)}
          content={
            <SavePromptContent
              globalFormName={globalFormName}
              saveWithReasonForPublish={saveWithReasonForPublish}
              saveWithReasonForPublishErrorMessage={saveWithReasonForPublishErrorMessage}
              isGlobalForm={isGlobalForm}
              saveCommentLabel={saveCommentLabel}
              saveAsModalDescription={saveAsModalDescription}
              {...saveAsModalAdditionalProps}
            />
          }
          okButtonProps={{ disabled: _isEmpty(saveWithReasonForPublish), loading: isFormSubmitInProgress }}
        />
        <ConfirmationDialog
          title={saveAsNewFormModalTitle}
          isVisible={isSaveAsNewConfirmationModalVisible}
          width={Modal.SIZES.SM}
          destroyOnClose
          onCancel={this.hideSaveAsNewModal}
          onSubmit={this.submitAsNewFormSetup}
          secondaryBtnText={__('Cancel')}
          submitBtnText={modalSubmitBtnText}
          content={
            <SaveAsNewPromptContent
              globalFormName={globalFormName}
              saveAsNewReasonForPublish={saveAsNewReasonForPublish}
              saveAsNewReasonForPublishErrorMessage={saveAsNewReasonForPublishErrorMessage}
              isGlobalForm={isGlobalForm}
              saveCommentLabel={saveCommentLabel}
            />
          }
          okButtonProps={{
            disabled: shouldDisableSaveAsNewAction({ saveAsNewName, saveAsNewReasonForPublish }),
            loading: isFormSubmitInProgress,
          }}
        />
        <ConfirmationDialog
          title={__('Exit Editor')}
          isVisible={showExitEditorModal}
          width={Modal.SIZES.MD}
          destroyOnClose
          onCancel={this.hideExitEditorModal}
          hideCancel
          onSubmit={this.onSaveAsDraftClick}
          submitBtnText={__('Save & Exit')}
          content={
            <Content className="mb-8">
              {__('Are you sure to exit the editor? Save form as draft or delete form to go back.')}
            </Content>
          }
          bodyStyle={EXIT_EDITOR_BODY_STYLE}
          renderFooterLeftSection={this.renderEditorExitFooterLeftSection}
          renderFooterRightSection={this.renderEditorExitFooterRightSection}
        />
      </>
    );
  };

  clearSelectedFieldTooltip = () => {
    const { onAction, pageType } = this.props;
    if (pageType === PAGE_TYPES.PREPARE)
      onAction({
        type: ACTION_TYPES.SHOW_FIELD_TOOLTIP,
        payload: { fieldId: null },
      });
  };

  renderAuditLogs = () => {
    const { showAuditLogs, assetId } = this.props;
    return (
      <AuditLogs
        visible={showAuditLogs}
        onClose={this.toggleAuditLogs}
        tabs={this.getAuditTabs(FORMS_AUDITS_FILTER_TYPES, assetId)}
        attributeVsFormatter={getFormsProAuditFormatters()}
        customDefaultAuditTypeConfig={AUDIT_FALLBACK_CONFIG}
        fetchAuditLogs={formConfiguratorToolAPI.fetchAuditLogs}
      />
    );
  };

  onCloseFormSettingsSidebar = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.HIDE_FORM_SETTINGS_SIDEBAR,
    });
  };

  getCategories = defaultMemoize(metadata => _keyBy(_get(metadata, 'category'), 'key'));

  onUpdateFormSettings = payload => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_FORM_SUBMIT,
      payload,
    });
  };

  renderFormSettingsDrawer = () => {
    const { showFormSettingsSidebar, legalProviders, metaData, isFCLiteViewOnly, disableEditForFCLiteMode } =
      this.props;

    if (!showFormSettingsSidebar) return null;

    const categories = this.getCategories(metaData);

    return (
      <AddFormLiteDrawer
        {...this.props}
        updateForm={_noop}
        categories={categories}
        hasFormConfiguratorView={isFCLiteViewOnly}
        toggleModal={this.onCloseFormSettingsSidebar}
        showModal={showFormSettingsSidebar}
        isFCLiteEditFlow={!disableEditForFCLiteMode}
        isFCLiteViewFlow={isFCLiteViewOnly}
        onUpdateFormSettings={this.onUpdateFormSettings}
      />
    );
  };

  render() {
    const { isLoading, config, isFcLiteModeEnabled } = this.props;
    const hideHeader = ConfigReader.hideHeader(config);
    if (isLoading) return <Spinner className="d-flex justify-content-center align-items-center full-height" />;
    return (
      <div
        className="d-flex flex-column full-height"
        id="fieldsMappingListScreen"
        role="button"
        tabIndex={0}
        onClick={this.clearSelectedFieldTooltip}>
        <PropertyControlledComponent controllerProperty={!hideHeader}>
          {this.renderHeader()}
        </PropertyControlledComponent>
        {this.renderBody()}
        {this.renderFooter()}
        {this.renderConfirmationDialogs()}
        {this.renderValidateFormModal()}
        {this.renderPrinterModal()}
        {isFcLiteModeEnabled && this.renderFormSettingsDrawer()}
        {this.renderAuditLogs()}
      </div>
    );
  }
}

FormConfiguratorTool.propTypes = {
  contentHeight: PropTypes.number,
  onAction: PropTypes.func,
  globalFormName: PropTypes.string,
  pageType: PropTypes.string,
  isLoading: PropTypes.bool,
  isValidateFormModalVisible: PropTypes.func,
  annotatedFields: PropTypes.object,
  formId: PropTypes.string,
  saveAsNewName: PropTypes.string,
  isSaveConfirmationModalVisible: PropTypes.bool,
  isSaveAsNewConfirmationModalVisible: PropTypes.bool,
  formState: PropTypes.string,
  formInfo: PropTypes.object,
  formValues: PropTypes.object,
  errors: PropTypes.object,
  isCreating: PropTypes.bool,
  showExitEditorModal: PropTypes.bool,
  canPerformUndo: PropTypes.bool,
  isValidFormName: PropTypes.bool,
  isEditingFormName: PropTypes.bool,
  configs: PropTypes.array,
  formKey: PropTypes.string,
  formDetails: PropTypes.object,
  formIdForValidateForm: PropTypes.string,
  fieldsForValidateForm: PropTypes.array,
  labels: PropTypes.array,
  formDetailsForValidateForm: PropTypes.object,
  selectedFields: PropTypes.array,
  isSavingAnnotations: PropTypes.bool,
  isLoadingPrepare: PropTypes.bool,
  isLoadingSetup: PropTypes.bool,
  showSaveAsNew: PropTypes.bool,
  pageDimensions: PropTypes.object,
  totalPages: PropTypes.number,
  saveAsNewReasonForPublish: PropTypes.string,
  saveWithReasonForPublish: PropTypes.string,
  saveAsNewReasonForPublishErrorMessage: PropTypes.string,
  saveWithReasonForPublishErrorMessage: PropTypes.string,
  isCreateFormModalOpen: PropTypes.bool,
  formPrinterType: PropTypes.string,
  isGlobalForm: PropTypes.bool,
  saveFormModalTitle: PropTypes.string,
  saveAsNewFormModalTitle: PropTypes.string,
  modalSubmitBtnText: PropTypes.string,
  saveCommentLabel: PropTypes.string,
  enableFormsPreview: PropTypes.bool,
  showAuditLogs: PropTypes.bool,
  assetId: PropTypes.string,
  config: PropTypes.object,
  isSaveAsNewDisabled: PropTypes.bool,
  pdfLoaded: PropTypes.bool,
  isFormSubmitInProgress: PropTypes.bool,
  econtractForm: PropTypes.bool,
  envForPreview: PropTypes.string,
  selectedDealer: PropTypes.string,
  dealersListforPreview: PropTypes.array,
  formCategories: PropTypes.array,
  hasViewOnlyPermission: PropTypes.bool,
  hideFormPrinterTypeSelection: PropTypes.bool,
  fniOptions: PropTypes.array,
  dueBills: PropTypes.array,
  fuelTypes: PropTypes.array,
  lenderTypes: PropTypes.array,
  dealTypeConfigs: PropTypes.array,
  vehicleTypes: PropTypes.array,
  customStatus: PropTypes.array,
  siteOptions: PropTypes.array,
  models: PropTypes.array,
  trimsList: PropTypes.array,
  makes: PropTypes.array,
  tekionMakes: PropTypes.array,
  stateListOptions: PropTypes.array,
  paymentOptionConfigs: PropTypes.array,
  dealStatus: PropTypes.array,
  saveAsModalSubmitBtnText: PropTypes.string,
  saveAsModalDescription: PropTypes.string,
  isSaveDisabled: PropTypes.bool,
  tradeInCount: PropTypes.number,
  pages: PropTypes.array,
  location: PropTypes.object.isRequired,
  isFcLiteModeEnabled: PropTypes.bool,
  showFormSettingsSidebar: PropTypes.bool,
};

FormConfiguratorTool.defaultProps = {
  contentHeight: 0,
  onAction: _noop,
  globalFormName: EMPTY_STRING,
  pageType: PAGE_TYPES.AGREEMENT,
  isLoading: false,
  isValidateFormModalVisible: false,
  annotatedFields: EMPTY_OBJECT,
  formId: EMPTY_STRING,
  saveAsNewName: EMPTY_STRING,
  isSaveConfirmationModalVisible: false,
  isSaveAsNewConfirmationModalVisible: false,
  formState: FORM_STATE.DRAFT,
  formInfo: EMPTY_OBJECT,
  formValues: EMPTY_OBJECT,
  errors: EMPTY_OBJECT,
  isCreating: false,
  showExitEditorModal: false,
  canPerformUndo: false,
  isValidFormName: true,
  isEditingFormName: false,
  configs: EMPTY_ARRAY,
  formKey: EMPTY_STRING,
  formDetails: EMPTY_OBJECT,
  formIdForValidateForm: EMPTY_STRING,
  fieldsForValidateForm: EMPTY_ARRAY,
  labels: EMPTY_ARRAY,
  formDetailsForValidateForm: EMPTY_OBJECT,
  selectedFields: EMPTY_ARRAY,
  isSavingAnnotations: false,
  isLoadingPrepare: false,
  isLoadingSetup: false,
  showSaveAsNew: false,
  pageDimensions: EMPTY_OBJECT,
  totalPages: 0,
  saveAsNewReasonForPublish: EMPTY_STRING,
  saveWithReasonForPublish: EMPTY_STRING,
  saveAsNewReasonForPublishErrorMessage: EMPTY_STRING,
  saveWithReasonForPublishErrorMessage: EMPTY_STRING,
  isCreateFormModalOpen: false,
  formPrinterType: EMPTY_STRING,
  isGlobalForm: false,
  saveFormModalTitle: __('Save & Publish'),
  saveAsNewFormModalTitle: __('Save & Publish as New'),
  modalSubmitBtnText: __('Publish'),
  saveCommentLabel: __('Reason for Publish'),
  enableFormsPreview: false,
  showAuditLogs: false,
  assetId: EMPTY_STRING,
  config: EMPTY_OBJECT,
  isSaveAsNewDisabled: false,
  pdfLoaded: false,
  isFormSubmitInProgress: false,
  econtractForm: false,
  selectedDealer: EMPTY_STRING,
  envForPreview: EMPTY_STRING,
  dealersListforPreview: EMPTY_ARRAY,
  formCategories: EMPTY_ARRAY,
  hasViewOnlyPermission: false,
  hideFormPrinterTypeSelection: false,
  fniOptions: EMPTY_ARRAY,
  dueBills: EMPTY_ARRAY,
  fuelTypes: EMPTY_ARRAY,
  lenderTypes: EMPTY_ARRAY,
  dealTypeConfigs: EMPTY_ARRAY,
  vehicleTypes: EMPTY_ARRAY,
  customStatus: EMPTY_ARRAY,
  siteOptions: EMPTY_ARRAY,
  models: EMPTY_ARRAY,
  trimsList: EMPTY_ARRAY,
  makes: EMPTY_ARRAY,
  tekionMakes: EMPTY_ARRAY,
  stateListOptions: EMPTY_ARRAY,
  paymentOptionConfigs: EMPTY_ARRAY,
  dealStatus: EMPTY_ARRAY,
  saveAsModalSubmitBtnText: __('Publish'),
  saveAsModalDescription: __('Your changes will not be published if you don’t save them'),
  isSaveDisabled: false,
  tradeInCount: 2,
  pages: DEFAULT_PAGES,
  isFcLiteModeEnabled: false,
  showFormSettingsSidebar: false,
};

export default React.memo(FormConfiguratorTool);
