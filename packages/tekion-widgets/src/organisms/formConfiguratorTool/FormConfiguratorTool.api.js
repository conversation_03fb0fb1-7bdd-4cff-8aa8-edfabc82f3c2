import { ESIGN_SERVICE } from '@tekion/tekion-base/constants/deal/endPoint';
import _get from 'lodash/get';
import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { URL_TYPES } from '@tekion/tekion-base/constants/api';
import Http from '@tekion/tekion-base/services/apiService/httpClient';
import { tget } from '@tekion/tekion-base/utils/general';
import { TOASTER_TYPE, toaster } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import { getResponse } from './utils';
import { ERROR_MESSAGES } from './constants/formConfigurator';
import { errorCallBack, successCallBack } from './helpers/http.helpers';

const ENDPOINT = {
  PRINTER_SERVICE: 'print/u/',
};

const API_PATHS = {
  GET_FORM_LABELS: `${ENDPOINT.PRINTER_SERVICE}forms/details/labels`,
  NEW_PREVIEW_FIELDS: `/${ENDPOINT.PRINTER_SERVICE}form/getFormDetails`,
  NEW_PREVIEW_URL: `/${ENDPOINT.PRINTER_SERVICE}form/preview`,
  ADD_NEW_FORM: `${ENDPOINT.PRINTER_SERVICE}newForm`,
  SAVE_FORMULA: `${ENDPOINT.PRINTER_SERVICE}formula-text/`,
  DELETE_FORMULA: `${ENDPOINT.PRINTER_SERVICE}formula-text/`,
  SEARCH_FORMULA: `${ENDPOINT.PRINTER_SERVICE}formula-text/search`,
  GLOBAL_FIELDS: `forms/core/u/configurator/global-fields`,
  GET_FORMS_METADATA: `${ESIGN_SERVICE}/formMetaData`,
  GET_FORM_SETUP_DETAILS: `${ENDPOINT.PRINTER_SERVICE}form/search`,
  GET_FORM_PORTAL_SETUP_DETAILS: `${ENDPOINT.PRINTER_SERVICE}forms-portal/search`,
  GET_CONFIGURATOR_METADATA: `${ENDPOINT.PRINTER_SERVICE}form-configurator/format/metadata`,
  GET_FORM_ANNOTATION_DETAILS: `${ENDPOINT.PRINTER_SERVICE}form-configurator/form`,
  SAVE_FORM: `${ENDPOINT.PRINTER_SERVICE}form-configurator/form`,
  SAVE_ANNOTATIONS: `/${ENDPOINT.PRINTER_SERVICE}form-configurator/form/annotations`,
  DELETE_FORM: `${ENDPOINT.PRINTER_SERVICE}form-configurator/form`,
  FORM_LOCK_ACQUIRE: `${ENDPOINT.PRINTER_SERVICE}form/lock/acquire`,
  FORM_LOCK_FORCE_ACQUIRE: `${ENDPOINT.PRINTER_SERVICE}form/lock/forceAcquire`,
  RELEASE_FORM_LOCK: `${ENDPOINT.PRINTER_SERVICE}form/lock/release`,
  SEARCH_FORM_LIST: `${ENDPOINT.PRINTER_SERVICE}form/search`,
  SEARCH_PDF_FORMS_LIST: `${ENDPOINT.PRINTER_SERVICE}pdf-library/pdf-library-metadata/search`,
  GET_PDF_LIBRARY_METADATA: `${ENDPOINT.PRINTER_SERVICE}pdf-library/pdf-library-metadata`,
  GET_PDF_METADATA_MULTIPLE_FORMS: `${ENDPOINT.PRINTER_SERVICE}pdf-library/pdf-library-metadata-for-given-keys`,
  FETCH_DOCUMENTS_SETUP_SETTINGS: `${ENDPOINT.PRINTER_SERVICE}formConfig/fetch/settings`,
  AUDIT_LOGS: `${ENDPOINT.PRINTER_SERVICE}form-configurator/audit/logs`,
  DOWNLOAD_FORM_PDF: `${ENDPOINT.PRINTER_SERVICE}downloadFormPdf`,
};

export default class FormConfiguratorToolAPI {
  static getFormsMetaData() {
    return Http.get(URL_TYPES.CDMS, API_PATHS.GET_FORMS_METADATA).then(successCallBack, errorCallBack);
  }

  static fetchGlobalFields() {
    return Http.get(URL_TYPES.CDMS, API_PATHS.GLOBAL_FIELDS).then(successCallBack, errorCallBack);
  }

  static getFormDetails(id) {
    return Http.get(URL_TYPES.CDMS, `${API_PATHS.GET_FORM_ANNOTATION_DETAILS}/${id}/annotations`).then(
      successCallBack,
      errorCallBack
    );
  }

  static saveForm(id, payload) {
    return Http.put(URL_TYPES.CDMS, `${API_PATHS.SAVE_FORM}/${id}`, payload)
      .then(successCallBack, errorCallBack)
      .then(getResponse);
  }

  static singleStepSaveForm(id, payload) {
    return Http.put(URL_TYPES.CDMS, `${API_PATHS.SAVE_FORM}/${id}/update-and-save`, payload)
      .then(successCallBack, errorCallBack)
      .then(getResponse);
  }

  static getFormSetupDetails(formKey, showFormsPortal, showEnterpriseView) {
    const endpoint =
      showFormsPortal || showEnterpriseView
        ? API_PATHS.GET_FORM_PORTAL_SETUP_DETAILS
        : API_PATHS.GET_FORM_SETUP_DETAILS;
    return Http.post(URL_TYPES.CDMS, endpoint, {
      searchText: '',
      filters: [
        {
          field: 'deleted',
          values: [false],
        },
        {
          field: 'formKey',
          values: [formKey],
        },
      ],
      pageInfo: {},
      sort: [],
    }).then(successCallBack, errorCallBack);
  }

  static saveAnnotations(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.SAVE_ANNOTATIONS, payload).then(successCallBack, errorCallBack);
  }

  static updateAnnotations(id, payload) {
    return Http.put(URL_TYPES.CDMS, `${API_PATHS.SAVE_ANNOTATIONS}/${id}`, payload).then(
      successCallBack,
      errorCallBack
    );
  }

  static getConfiguratorMetadata() {
    return Http.get(URL_TYPES.CDMS, `${API_PATHS.GET_CONFIGURATOR_METADATA}`).then(successCallBack, errorCallBack);
  }

  static getFormLabels() {
    return Http.get(URL_TYPES.CDMS, API_PATHS.GET_FORM_LABELS).then(successCallBack, errorCallBack);
  }

  static newPreviewFields(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.NEW_PREVIEW_FIELDS, payload).then(successCallBack, errorCallBack);
  }

  static newPreviewUrl(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.NEW_PREVIEW_URL, payload).then(successCallBack, errorCallBack);
  }

  static addNewForm(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.ADD_NEW_FORM, payload)
      .then(successCallBack, errorCallBack)
      .then(getResponse)
      .catch(error => {
        const message = _get(error, 'data.errorDetails.debugMessage');
        if (message) toaster(TOASTER_TYPE.ERROR, message);
        else toaster(TOASTER_TYPE.ERROR, ERROR_MESSAGES.DEFAULT);
        return EMPTY_OBJECT;
      });
  }

  static saveFormula(formulaData) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.SAVE_FORMULA, formulaData).then(successCallBack, errorCallBack);
  }

  static deleteFormula(formulaName) {
    return Http.delete(URL_TYPES.CDMS, `${API_PATHS.DELETE_FORMULA}${formulaName}`).then(
      successCallBack,
      errorCallBack
    );
  }

  static searchFormula(searchString) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.SEARCH_FORMULA, { searchString, limit: 20 }).then(
      successCallBack,
      errorCallBack
    );
  }

  static acquireFormEditAccess(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.FORM_LOCK_ACQUIRE, payload)
      .then(successCallBack, errorCallBack)
      .then(getResponse);
  }

  static forceAcquireFormAccess(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.FORM_LOCK_FORCE_ACQUIRE, payload)
      .then(successCallBack, errorCallBack)
      .then(getResponse);
  }

  static releaseFormEditAccessLock(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.RELEASE_FORM_LOCK, payload)
      .then(successCallBack, errorCallBack)
      .then(getResponse);
  }

  // need to change api once we get PDF library API
  static searchFormList(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.SEARCH_FORM_LIST, payload)
      .then(successCallBack, errorCallBack)
      .then(getResponse);
  }

  static fetchPdfFormsList(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.SEARCH_PDF_FORMS_LIST, payload)
      .then(successCallBack, errorCallBack)
      .then(getResponse);
  }

  static getPDFMetadataFromPDFLibrary(pdfKey) {
    return Http.get(URL_TYPES.CDMS, `${API_PATHS.GET_PDF_LIBRARY_METADATA}/${pdfKey}`)
      .then(successCallBack, errorCallBack)
      .then(getResponse);
  }

  static getMultiplePDFMetadataFromPDFLibrary(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.GET_PDF_METADATA_MULTIPLE_FORMS, payload)
      .then(successCallBack, errorCallBack)
      .then(getResponse);
  }

  static fetchDocumentSetupSetings() {
    return Http.get(URL_TYPES.CDMS, API_PATHS.FETCH_DOCUMENTS_SETUP_SETTINGS)
      .then(successCallBack, errorCallBack)
      .then(getResponse);
  }

  static fetchAuditLogs(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.AUDIT_LOGS, payload).then(res => {
      const auditData = _get(res, 'data.formsLibraryEnabled')
        ? tget(res, 'data.libraryAuditResponse', EMPTY_OBJECT)
        : tget(res, 'data.configuratorAuditResponse', EMPTY_OBJECT);
      const resultList = _get(auditData, 'resultList') || _get(auditData, 'data');
      const { nextToken, queryExecutionId } = auditData;
      return { nextToken, resultList, queryExecutionId };
    });
  }

  static downloadFormPdf(formKey) {
    return Http.get(URL_TYPES.CDMS, `${API_PATHS.DOWNLOAD_FORM_PDF}/${formKey}`)
      .then(successCallBack, errorCallBack)
      .then(getResponse);
  }
}
