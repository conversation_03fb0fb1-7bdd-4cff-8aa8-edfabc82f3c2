import { mapProps } from 'recompose';

import _get from 'lodash/get';
import _isEqual from 'lodash/isEqual';

import { ENTITY_REFERENCE_TYPES_TO_LABEL } from '@tekion/tekion-base/constants/parts/entityReferenceTypes';
import { NO_DATA } from '@tekion/tekion-base/app.constants';

import ButtonRenderer from '@tekion/tekion-components/src/molecules/CellRenderers/buttonRenderer';
import Button from '@tekion/tekion-components/src/atoms/Button';
import DefaultRenderer from '@tekion/tekion-components/src/molecules/CellRenderers/DefaultContent';
import { GENERIC_PART_REPORT_COLUMN_KEYS } from '@tekion/tekion-components/src/constants/parts/reports.constants';
import ShowMoreItemsCellRenderer from '@tekion/tekion-components/src/molecules/CellRenderers/ShowMoreItemsCell';
import { FRACTIONAL_SALE_NUMBER_DISPLAY_OPTIONS } from '@tekion/tekion-business/src/constants/fractionalSales.constants';
import NumberDisplayCellRenderer from '../../../molecules/tableInputFieldCells/NumberDisplayCellRenderer';

import DateRenderer from '../../../cellRenderers/dateRenderer';
import PartNameRendererWithExternalLinkIcon from '../../../appServices/parts/molecules/PartNameRendererWithExternalLinkIcon';

import { getBrandLabel, getReferenceTypeServiceMode } from '../helpers/detailedByPartGenericReport.helpers';
import { LOCAL_ACTIONS } from '../detailedByPartGenericReport.actionTypes';
import { REPORT_TYPES } from '../detailedByPartGenericReport.constants';

const getPartNameRenderer = ({ reportType, isThirdPaneEnabled, isMultiWarehouseEnabled }) => {
  if (_isEqual(reportType, REPORT_TYPES.LOST_SALE)) {
    return mapProps(props => ({
      value: _get(props, 'rowData.partId.data.partNumber'),
      partId: _get(props, 'rowData.partId.id'),
      warehouseId: _get(props, 'rowData.warehouseId'),
      isMultiWarehouseEnabled,
      isThirdPaneEnabled,
    }))(PartNameRendererWithExternalLinkIcon);
  }

  return DefaultRenderer;
};

const NumberDisplayCellRendererWrapper = mapProps(props => ({
  ...props,
  value: props?.data,
  options: FRACTIONAL_SALE_NUMBER_DISPLAY_OPTIONS,
}))(NumberDisplayCellRenderer);

export const getColumnVsKeyRenderer = ({ onAction, reportType, isThirdPaneEnabled, isMultiWarehouseEnabled }) => ({
  [GENERIC_PART_REPORT_COLUMN_KEYS.REFERENCE_NUMBER]: mapProps(props => ({
    ...props,
    onAction,
    label: props.data || NO_DATA,
    view: Button.VIEW.TERTIARY,
    actionType: LOCAL_ACTIONS.REDIRECT_REFERENCE_NUMBER,
    value: props.rowData,
    original: { ...props.rowData, isThirdPaneEnabled },
  }))(ButtonRenderer),
  [GENERIC_PART_REPORT_COLUMN_KEYS.REFERENCE_TYPE]: mapProps(props => ({
    ...props,
    data: getReferenceTypeServiceMode(props, reportType),
  }))(DefaultRenderer),
  [GENERIC_PART_REPORT_COLUMN_KEYS.CREATED_DATE]: DateRenderer,
  [GENERIC_PART_REPORT_COLUMN_KEYS.CREATED_BY]: DefaultRenderer,
  [GENERIC_PART_REPORT_COLUMN_KEYS.COST_PRICE]: DefaultRenderer,
  [GENERIC_PART_REPORT_COLUMN_KEYS.TOTAL_COST]: DefaultRenderer,
  [GENERIC_PART_REPORT_COLUMN_KEYS.EXTENDED_COST_PRICE]: DefaultRenderer,
  [GENERIC_PART_REPORT_COLUMN_KEYS.EXTENDED_SELLING_PRICE]: DefaultRenderer,
  [GENERIC_PART_REPORT_COLUMN_KEYS.GROSS_PROFIT]: DefaultRenderer,
  [GENERIC_PART_REPORT_COLUMN_KEYS.GROSS_PROFIT_PERCENT]: DefaultRenderer,
  [GENERIC_PART_REPORT_COLUMN_KEYS.LIST_PRICE]: DefaultRenderer,
  [GENERIC_PART_REPORT_COLUMN_KEYS.PART_NAME]: getPartNameRenderer({
    reportType,
    isThirdPaneEnabled,
    isMultiWarehouseEnabled,
  }),
  [GENERIC_PART_REPORT_COLUMN_KEYS.MODIFIED_TIME]: DateRenderer,
  [GENERIC_PART_REPORT_COLUMN_KEYS.MANUFACTURER]: mapProps(props => ({ ...props, data: getBrandLabel(props) }))(
    DefaultRenderer
  ),
  [GENERIC_PART_REPORT_COLUMN_KEYS.SOURCE_CODE]: DefaultRenderer,
  [GENERIC_PART_REPORT_COLUMN_KEYS.GROUP]: DefaultRenderer,
  [GENERIC_PART_REPORT_COLUMN_KEYS.BINS]: ShowMoreItemsCellRenderer,
  [GENERIC_PART_REPORT_COLUMN_KEYS.ON_HAND_QUANTITY]: NumberDisplayCellRendererWrapper,
  [GENERIC_PART_REPORT_COLUMN_KEYS.ON_HOLD_QUANTITY]: NumberDisplayCellRendererWrapper,
  [GENERIC_PART_REPORT_COLUMN_KEYS.CORE_COST]: DefaultRenderer,
});
