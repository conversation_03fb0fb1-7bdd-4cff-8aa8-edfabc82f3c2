import React, { PureComponent } from 'react';
import { defaultMemoize } from 'reselect';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { compose } from 'recompose';
import _filter from 'lodash/filter';
import _isNil from 'lodash/isNil';
import _noop from 'lodash/noop';
import _isFunction from 'lodash/isFunction';
import _get from 'lodash/get';
import cx from 'classnames';
import _isEmpty from 'lodash/isEmpty';
import _includes from 'lodash/includes';
import _identity from 'lodash/identity';

import { withTekionConversion } from '@tekion/tekion-conversion-web';

import { EMPTY_OBJECT, EMPTY_ARRAY, EMPTY_STRING, NO_DATA } from '@tekion/tekion-base/app.constants';
import UserReader from '@tekion/tekion-base/readers/User';
import { PARTS } from '@tekion/tekion-base/constants/appServices';

import withPropertyConsumer from '@tekion/tekion-components/src/organisms/propertyProvider/withPropertyConsumer';
import Page from '@tekion/tekion-components/src/molecules/pageComponent/PageComponent';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import Skeleton from '@tekion/tekion-components/src/molecules/skeleton';
import {
  getItems,
  getFilterValue,
  getPageNumber,
  getAppliedSortDetails,
} from '@tekion/tekion-components/src/reducers/tableItemReducer';
import {
  getTableItemDetailsByFilterValue,
  getSearchAndFilterDetails,
} from '@tekion/tekion-components/src/utils/tableItems';
import Heading from '@tekion/tekion-components/src/atoms/Heading';
import { createDbRequestPayload } from '@tekion/tekion-components/src/helpers/reportHelper/dbRequestBuilder.helper';
// Intended - MFed
// eslint-disable-next-line no-restricted-imports
import { useSubAppAnalytics } from '@tekion/tekion-widgets/src/utils/analytics/hooks';
import Loader from '@tekion/tekion-components/molecules/loader';
import { moduleFederationLazy } from '../../appServices/parts/utils/moduleFederationLazy';

import { getReportsAnalyticsPayloadWithTags } from '../../utils/analytics/helpers';
import {
  TABLE_ROW_HEIGHT,
  TABLE_MIN_ROWS,
  ITEM_TYPE,
  REPORT_TYPES,
  REPORT_TYPE_TO_DISPLAY_SEARCH,
} from './detailedByPartGenericReport.constants';
import TableSummaryRenderer, { FIELD_TYPE } from '../../molecules/TableSummaryRenderer';
import {
  TABLE_MANAGER_PROPS,
  getModuleStyle,
  getColumns,
  getTableManagerProps,
} from './detailedByPartGenericReport.config';
import { LOCAL_ACTIONS } from './detailedByPartGenericReport.actionTypes';
import { getColumnVsKeyRenderer } from './configs/columns.renderers';
import withProps from './withProps.hoc';
import {
  getAggregatedTotalCost,
  getAggregatedTotalQty,
  getAggregatedGrossProfit,
  getTotalCostPrice,
  getTotalListPrice,
  getAggregatedCoreValue,
  getAdditionalReportData,
  getUniqueCores,
  getTotalCores,
  getTotalCoreOnHandValue,
  getDisabledReportData,
} from './helpers/detailedByPartGenericReport.selectors';
import { shouldDisplayPartName } from './helpers/detailedByPartGenericReport.helpers';
import CoreTransaction from '../PartsCoreTransactionWrapper';
import {
  getSubHeaderTableManagerProps,
  getTableProps,
  getTableMangerColumns,
  TABLE_TYPES,
  TABLE_TYPES_VS_COMPONENT,
} from './detailedByPartGenericReport.utils';

import { getPrintModalPreference } from '../AppSkeleton/appSkeleton/skeleton.selector';

import styles from './detailedByPartGenericReport.module.scss';

const LostSaleDrawer = moduleFederationLazy({
  port: '3001',
  moduleName: 'parts',
  exposedModuleFederationComponentName: 'LostSaleDrawer',
});

const getRowClassName = (state, rowInfo) => {
  const disabled = _get(rowInfo, ['original', 'unmarked'], false);
  if (disabled) return styles.disabledRow;
  return EMPTY_STRING;
};
class DetailedByPartGenericReport extends PureComponent {
  getFilterProps = defaultMemoize((filterProps, selectedFilterGroup) => ({ ...filterProps, selectedFilterGroup }));

  componentDidMount() {
    const { onAction } = this.props;
    onAction({
      type: LOCAL_ACTIONS.RESOLVE_PART_NAME,
    });
  }

  handleGoBack = () => {
    const { onAction, handleGoBack } = this.props;
    if (_isFunction(handleGoBack)) {
      onAction({
        type: LOCAL_ACTIONS.RESET_STATE,
      });
      handleGoBack();
    }
  };

  renderHeader() {
    const {
      partNameToDisplay,
      navigate,
      location,
      reportName,
      reportType,
      aggregatedTotalCost = 0,
      aggregatedTotalQty,
      aggregatedGrossProfit = 0,
      WarehouseDropdownComponent,
      warehouseDropdownProps,
      showWarehouseDropdown,
      headerClassName,
    } = this.props;
    const headerLabel = shouldDisplayPartName(partNameToDisplay, reportType)
      ? __(`${reportName} : ${partNameToDisplay}`)
      : __(`${reportName}`);

    return (
      <Page.Header hasBack onBack={this.handleGoBack} navigate={navigate} location={location}>
        <Heading className={cx('flex flex-grow-1', { [headerClassName]: showWarehouseDropdown })}>
          {headerLabel}
        </Heading>
        <PropertyControlledComponent controllerProperty={showWarehouseDropdown}>
          <WarehouseDropdownComponent {...warehouseDropdownProps} />
        </PropertyControlledComponent>
        <PropertyControlledComponent controllerProperty={reportType === REPORT_TYPES.DETAILED_WIP_BY_PART}>
          <div className="flex">
            <TableSummaryRenderer label={__('Total Value')} value={aggregatedTotalCost} type={FIELD_TYPE.PRICE} />
            <TableSummaryRenderer
              label={__('Total Qty WIP')}
              value={aggregatedTotalQty ?? 0}
              type={FIELD_TYPE.NUMBER}
            />
            <TableSummaryRenderer label={__('Gross Profit')} value={aggregatedGrossProfit} type={FIELD_TYPE.PRICE} />
          </div>
        </PropertyControlledComponent>
      </Page.Header>
    );
  }

  renderCustomHeader = () => {
    const { getOverviewComponents, customHeaderDetails = EMPTY_OBJECT, reportType } = this.props;
    // Note: isLoading is only for rendering Custom Header please do not consume
    const { isLoading = true, data = EMPTY_OBJECT } = customHeaderDetails || EMPTY_OBJECT;

    const { netInAndOutCostPrice = EMPTY_OBJECT, netInAndOutSellingPrice = EMPTY_OBJECT } =
      !isLoading && _includes(REPORT_TYPE_TO_DISPLAY_SEARCH, reportType) ? getOverviewComponents(data) : EMPTY_OBJECT;

    if (isLoading && _includes(REPORT_TYPE_TO_DISPLAY_SEARCH, reportType)) {
      return (
        <div className="full-width flex m-b-2 align-items-center justify-content-end">
          <div className={styles.customHeaderSkeleton}>
            <Skeleton loading active paragraph={false} />
          </div>
        </div>
      );
    }

    return (
      <PropertyControlledComponent controllerProperty={_includes(REPORT_TYPE_TO_DISPLAY_SEARCH, reportType)}>
        <PropertyControlledComponent
          controllerProperty={!_isEmpty(netInAndOutCostPrice) && !_isEmpty(netInAndOutSellingPrice)}>
          <div className={cx('full-width flex m-b-2 align-items-center justify-content-end')}>
            <PropertyControlledComponent controllerProperty={!_isEmpty(netInAndOutCostPrice)}>
              {this.renderComponentFromConfig(netInAndOutCostPrice)}
            </PropertyControlledComponent>
            <PropertyControlledComponent controllerProperty={!_isEmpty(netInAndOutSellingPrice)}>
              {this.renderComponentFromConfig(netInAndOutSellingPrice)}
            </PropertyControlledComponent>
          </div>
        </PropertyControlledComponent>
      </PropertyControlledComponent>
    );
  };

  renderComponentFromConfig = (config = EMPTY_OBJECT) => {
    if (_isEmpty(config)) return null;
    const Component = _get(config, 'renderer');
    const props = _get(config, 'renderOptions');
    if (!Component) return null;
    return <Component {...props} />;
  };

  closeLostSaleDrawer = () => {
    const { onAction } = this.props;
    onAction({ type: LOCAL_ACTIONS.CLOSE_LOST_SALE_DRAWER });
  };

  submitLostSaleDrawer = values => {
    const { onAction } = this.props;
    onAction({ type: LOCAL_ACTIONS.SUBMIT_LOST_SALE_DRAWER, payload: { values } });
  };

  render() {
    const { props } = this;
    const {
      contentHeight,
      onAction,
      navigate,
      location,
      filterProps,
      getSubHeaderProps,
      rows,
      currentPage,
      totalCount,
      getHeaderProps,
      additionalColumns,
      additionalColumnRenderers: _additionalColumnRenderers,
      orderedColumnsKeys,
      additionalTableManagerProps,
      columnConfigurator,
      tableType,
      sortDetails,
      selectedFilterGroup,
      selectedFilters,
      tableData,
      reportType,
      params,
      searchField,
      searchText,
      defaultSearchField,
      searchFieldsOptions,
      tableClassName,
      isThirdPaneEnabled,
      getFormattedCurrency,
      getFormattedPercentage,
      showLostSaleModal,
      lostSaleDrawerParams,
      shouldAllowDisabledRowsToCheck,
      getAdditionalColumnRenderers,
      selectedWarehouse,
      isMultiWarehouseEnabled,
    } = this.props;
    const additionalColumnRenderers = _isFunction(getAdditionalColumnRenderers)
      ? getAdditionalColumnRenderers(onAction)
      : _additionalColumnRenderers;
    const columnKeyVSRenderer = {
      ...getColumnVsKeyRenderer({ onAction, reportType, isThirdPaneEnabled, isMultiWarehouseEnabled }),
      ...additionalColumnRenderers,
    };
    const columns = getColumns(
      additionalColumns,
      orderedColumnsKeys,
      getFormattedCurrency,
      getFormattedPercentage,
      reportType
    );
    const hasQueryParams = _get(params, 'partId');
    const createdTime = _filter(selectedFilters, { type: 'createdTime' });
    const isUsedCoreInventoryReport = reportType === REPORT_TYPES.USED_CORE_INVENTORY;

    const className = cx('part-generic-report', reportType, tableClassName);
    const DetailedByPartGenericTable = TABLE_TYPES_VS_COMPONENT[tableType];
    const subHeaderProps = getSubHeaderProps(props);

    const tableProps = {
      ...getTableProps({
        ...this.props,
        tableRowHeight: TABLE_ROW_HEIGHT,
        minRows: TABLE_MIN_ROWS,
        className,
        shouldAllowDisabledRowsToCheck,
        getRowClassName,
      }),
      getTrProps: (_, rowInfo) => {
        if (reportType !== REPORT_TYPES.LOST_SALE) return EMPTY_OBJECT;

        const unmarked = _get(rowInfo, 'original.unmarked');
        const unmarkedByUserDetails = _get(rowInfo, 'original.unmarkedBy');
        const unmarkedByUserName = UserReader.name(unmarkedByUserDetails);

        return {
          title: unmarked ? __(`Unmarked by ${unmarkedByUserName}`) || NO_DATA : EMPTY_STRING,
        };
      },
    };

    const additionalProps = isMultiWarehouseEnabled ? { key: selectedWarehouse } : EMPTY_OBJECT;

    return (
      <Page>
        {this.renderHeader()}
        <Page.Body>
          {this.renderCustomHeader()}
          <div className="full-width" style={getModuleStyle(contentHeight, reportType)}>
            <DetailedByPartGenericTable
              className={cx('part-generic-report', reportType, tableClassName)}
              {...this.props}
              navigate={navigate}
              location={location}
              columns={getTableMangerColumns(columns, columnKeyVSRenderer)}
              columnKeyVSRenderer={columnKeyVSRenderer}
              tableManagerProps={getTableManagerProps(TABLE_MANAGER_PROPS, additionalTableManagerProps)}
              tableRowHeight={TABLE_ROW_HEIGHT}
              minRows={TABLE_MIN_ROWS}
              onAction={onAction}
              filterProps={this.getFilterProps(filterProps, selectedFilterGroup)}
              subHeaderProps={getSubHeaderTableManagerProps(rows, currentPage, totalCount, subHeaderProps)}
              headerProps={getHeaderProps(props)}
              fixedPagination
              tableType={tableType}
              sortDetails={sortDetails}
              columnConfigurator={columnConfigurator}
              searchableFieldsOptions={searchFieldsOptions}
              searchText={searchText}
              searchField={searchField || defaultSearchField}
              tableProps={tableProps}
              data={tableData}
              {...additionalProps}
            />
          </div>
        </Page.Body>
        <PropertyControlledComponent controllerProperty={!_isNil(hasQueryParams) && isUsedCoreInventoryReport}>
          <CoreTransaction params={params} createdTime={createdTime} defaultNavigateBackBehaviour />
        </PropertyControlledComponent>
        <PropertyControlledComponent controllerProperty={reportType === REPORT_TYPES.LOST_SALE}>
          <React.Suspense fallback={<Loader />}>
            <LostSaleDrawer
              {...lostSaleDrawerParams}
              isVisible={showLostSaleModal}
              onAction={onAction}
              onClose={this.closeLostSaleDrawer}
              onSubmit={this.submitLostSaleDrawer}
              areActionsDisabled
            />
          </React.Suspense>
        </PropertyControlledComponent>
      </Page>
    );
  }
}

DetailedByPartGenericReport.propTypes = {
  onAction: PropTypes.func.isRequired,
  tableData: PropTypes.array,
  contentHeight: PropTypes.number,
  partNameToDisplay: PropTypes.string,
  type: PropTypes.string,
  requestBuilderFunc: PropTypes.func,
  getReportPdfDetails: PropTypes.func,
  handleGoBack: PropTypes.func,
  reportName: PropTypes.string.isRequired,
  additionalColumns: PropTypes.object,
  additionalColumnRenderers: PropTypes.object,
  orderedColumnsKeys: PropTypes.array,
  filterProps: PropTypes.object,
  getSubHeaderProps: PropTypes.func,
  getHeaderProps: PropTypes.func,
  pageSize: PropTypes.number,
  additionalTableManagerProps: PropTypes.object,
  columnConfigurator: PropTypes.object,
  sortDetails: PropTypes.object,
  pdfVariants: PropTypes.string,
  documentType: PropTypes.string,
  selectedFilters: PropTypes.array,
  reportType: PropTypes.string.isRequired,
  tableClassName: PropTypes.string,
  tableType: PropTypes.string,
  loading: PropTypes.bool,
  getFormattedNumber: PropTypes.func,
  getFormattedPercentage: PropTypes.func,
  getFormattedCurrency: PropTypes.func.isRequired,
  itemType: PropTypes.string,
  showLostSaleModal: PropTypes.bool,
  selection: PropTypes.array,
  lostSaleDrawerParams: PropTypes.object,
  disabled: PropTypes.bool,
  shouldAllowDisabledRowsToCheck: PropTypes.bool,
  getAdditionalColumnRenderers: PropTypes.func,
  navigate: PropTypes.func.isRequired,
  location: PropTypes.object.isRequired,
  params: PropTypes.object.isRequired,
  warehouseDropdownProps: PropTypes.object,
  showWarehouseDropdown: PropTypes.bool,
  headerClassName: PropTypes.string,
};

DetailedByPartGenericReport.defaultProps = {
  tableData: EMPTY_ARRAY,
  contentHeight: 0,
  partNameToDisplay: '',
  type: '',
  requestBuilderFunc: createDbRequestPayload,
  getReportPdfDetails: _noop,
  handleGoBack: _noop,
  additionalColumns: EMPTY_OBJECT,
  additionalColumnRenderers: EMPTY_OBJECT,
  orderedColumnsKeys: EMPTY_ARRAY,
  filterProps: EMPTY_OBJECT,
  getSubHeaderProps: _noop,
  getHeaderProps: _noop,
  pageSize: 10,
  additionalTableManagerProps: EMPTY_OBJECT,
  columnConfigurator: EMPTY_OBJECT,
  sortDetails: EMPTY_OBJECT,
  pdfVariants: '',
  documentType: '',
  selectedFilters: EMPTY_ARRAY,
  tableClassName: '',
  tableType: TABLE_TYPES.TABLE_MANAGER,
  loading: false,
  getFormattedNumber: _identity,
  getFormattedPercentage: _identity,
  itemType: ITEM_TYPE,
  showLostSaleModal: false,
  selection: EMPTY_ARRAY,
  lostSaleDrawerParams: EMPTY_OBJECT,
  disabled: false,
  shouldAllowDisabledRowsToCheck: false,
  getAdditionalColumnRenderers: null,
  showWarehouseDropdown: false,
  warehouseDropdownProps: EMPTY_OBJECT,
  headerClassName: EMPTY_STRING,
};

const mapStateToProps = (state, props) => {
  const itemTypeValue = _get(props, 'itemType', ITEM_TYPE);
  const tableManagerState = _get(state, `${itemTypeValue}.tableItems`, EMPTY_OBJECT);
  const filterValue = getFilterValue(tableManagerState);

  return {
    showPrintOptionForUser: getPrintModalPreference(state),
    currentPage: getPageNumber(tableManagerState),
    tableData: getItems(tableManagerState, filterValue),
    ...getTableItemDetailsByFilterValue(tableManagerState, filterValue),
    ...getSearchAndFilterDetails(tableManagerState, filterValue),
    aggregatedTotalQty: getAggregatedTotalQty(tableManagerState),
    sortDetails: getAppliedSortDetails(tableManagerState),
    aggregatedTotalCost: getAggregatedTotalCost(tableManagerState),
    aggregatedGrossProfit: getAggregatedGrossProfit(tableManagerState),
    totalCostPrice: getTotalCostPrice(tableManagerState),
    totalListPrice: getTotalListPrice(tableManagerState),
    aggregatedCoreValue: getAggregatedCoreValue(tableManagerState),
    additionalReportData: getAdditionalReportData(tableManagerState),
    uniqueCores: getUniqueCores(tableManagerState),
    totalCores: getTotalCores(tableManagerState),
    totalCoreOnHandValue: getTotalCoreOnHandValue(tableManagerState),
    disabled: getDisabledReportData(tableManagerState) || EMPTY_ARRAY,
  };
};

const mapDispatchToProps = (_, props) => {
  const { handleReportDownload } = props;
  return {
    handleReportDownload,
  };
};

function DetailedByPartGenericReportWithAnalytics(props) {
  const { loading: isLoading } = props;

  useSubAppAnalytics({ isLoading, ...getReportsAnalyticsPayloadWithTags(PARTS) });

  return <DetailedByPartGenericReport {...props} />;
}

export default compose(
  withPropertyConsumer,
  withTekionConversion,
  connect(mapStateToProps, mapDispatchToProps),
  withProps
)(DetailedByPartGenericReportWithAnalytics);
