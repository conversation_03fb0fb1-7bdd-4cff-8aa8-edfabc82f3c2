import { getErrorMessage } from '@tekion/tekion-base/utils/errorUtils';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import { downloadAuditLogs, downloadAuditLogsDealerLevel } from '../../../../services/auditLogsService';
import { downloadAuditLogsAction } from '../auditLogs.actions';

jest.mock('../../../../services/auditLogsService', () => ({
  downloadAuditLogsDealerLevel: jest.fn(),
  downloadAuditLogs: jest.fn(),
}));

jest.mock('@tekion/tekion-components/src/organisms/NotificationWrapper', () => ({
  toaster: jest.fn(),
  TOASTER_TYPE: { ERROR: 'error' },
}));

jest.mock('@tekion/tekion-base/utils/errorUtils', () => ({
  getErrorMessage: jest.fn(),
}));

describe('downloadAuditLogsAction', () => {
  const EMPTY_OBJECT = {};

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should call downloadAuditLogsDealerLevel when shouldCallDealerLevelApi is true', async () => {
    const payload = { key: 'value' };
    const response = { data: { result: 'success' } };
    downloadAuditLogsDealerLevel.mockResolvedValue(response);

    const result = await downloadAuditLogsAction(payload, true);

    expect(downloadAuditLogsDealerLevel).toHaveBeenCalledWith(payload);
    expect(downloadAuditLogs).not.toHaveBeenCalled();
    expect(result).toEqual(response.data);
  });

  it('should call downloadAuditLogs when shouldCallDealerLevelApi is false', async () => {
    const payload = { key: 'value' };
    const response = { data: { result: 'success' } };
    downloadAuditLogs.mockResolvedValue(response);

    const result = await downloadAuditLogsAction(payload, false);

    expect(downloadAuditLogs).toHaveBeenCalledWith(payload);
    expect(downloadAuditLogsDealerLevel).not.toHaveBeenCalled();
    expect(result).toEqual(response.data);
  });

  it('should return EMPTY_OBJECT if data is not found in the response', async () => {
    const payload = { key: 'value' };
    const response = {};
    downloadAuditLogs.mockResolvedValue(response);

    const result = await downloadAuditLogsAction(payload, false);

    expect(downloadAuditLogs).toHaveBeenCalledWith(payload);
    expect(result).toEqual(EMPTY_OBJECT);
  });

  it('should handle errors and call toaster with the error message', async () => {
    const payload = { key: 'value' };
    const error = new Error('Something went wrong');
    downloadAuditLogs.mockRejectedValue(error);
    getErrorMessage.mockReturnValue('Error message');

    const result = await downloadAuditLogsAction(payload, false);

    expect(downloadAuditLogs).toHaveBeenCalledWith(payload);
    expect(toaster).toHaveBeenCalledWith(TOASTER_TYPE.ERROR, 'Error message');
    expect(result).toEqual(EMPTY_OBJECT);
  });

  it('should use EMPTY_OBJECT as default payload', async () => {
    const response = { data: { result: 'success' } };
    downloadAuditLogs.mockResolvedValue(response);

    const result = await downloadAuditLogsAction(undefined, false);

    expect(downloadAuditLogs).toHaveBeenCalledWith(EMPTY_OBJECT);
    expect(result).toEqual(response.data);
  });
});

const mockUserInfoInternal = () => ({ roleId: 'role1' });
const mockUserInfoNonInternal = () => ({ roleId: 'role2' });
const mockUserInfoNull = () => null;

const mockRolesData = {
  resources: [
    { id: 'role1', data: { internal: true } },
    { id: 'role2', data: { internal: false } },
    { id: 'role3', data: { internal: false } },
  ],
};

const mockEmptyResponse = { status: 'success' };
const mockRolesSearchAction = () => async () => ({ ...mockRolesData });
const mockEmptyRolesSearchAction = () => async () => ({ ...mockEmptyResponse });

describe('fetchNonInternalRoles', () => {
  let originalTenvReader;
  let originalRolesSearchAction;

  beforeEach(() => {
    if (typeof window !== 'undefined') {
      originalTenvReader = window.TenvReader;
      originalRolesSearchAction = window.rolesSearchAction;
    }
  });

  afterEach(() => {
    if (typeof window !== 'undefined') {
      window.TenvReader = originalTenvReader;
      window.rolesSearchAction = originalRolesSearchAction;
    }
  });

  it('should return all roles when user has an internal role', async () => {
    global.TenvReader = { userInfo: mockUserInfoInternal };
    global.rolesSearchAction = mockRolesSearchAction;

    const testFetchNonInternalRoles =
      () =>
      async (...args) => {
        const userInfo = mockUserInfoInternal();
        const userRoleId = userInfo?.roleId;
        const result = await mockRolesSearchAction()(...args);

        if (result && result.resources) {
          const userHasInternalRole = result.resources.some(
            role => role.id === userRoleId && role.data?.internal === true
          );

          if (!userHasInternalRole) {
            result.resources = result.resources.filter(role => !(role.data?.internal === true));
          }
        }
        return result;
      };

    const fetchRoles = testFetchNonInternalRoles();
    const result = await fetchRoles({ page: 1, limit: 10 });

    expect(result.resources.length).toBe(3);
    expect(result).toEqual(mockRolesData);
  });

  it('should filter out internal roles when user does not have an internal role', async () => {
    const testFetchNonInternalRoles =
      () =>
      async (...args) => {
        const userInfo = mockUserInfoNonInternal();
        const userRoleId = userInfo?.roleId;
        const result = await mockRolesSearchAction()(...args);

        if (result && result.resources) {
          const userHasInternalRole = result.resources.some(
            role => role.id === userRoleId && role.data?.internal === true
          );

          if (!userHasInternalRole) {
            result.resources = result.resources.filter(role => !(role.data?.internal === true));
          }
        }
        return result;
      };

    const fetchRoles = testFetchNonInternalRoles();
    const result = await fetchRoles({ page: 1, limit: 10 });

    expect(result.resources.length).toBe(2);
    expect(result.resources.every(role => role.data.internal !== true)).toBe(true);
  });

  it('should handle case when result has no resources property', async () => {
    const testFetchNonInternalRoles =
      () =>
      async (...args) => {
        const userInfo = mockUserInfoInternal();
        const userRoleId = userInfo?.roleId;
        const result = await mockEmptyRolesSearchAction()(...args);

        if (result && result.resources) {
          const userHasInternalRole = result.resources.some(
            role => role.id === userRoleId && role.data?.internal === true
          );

          if (!userHasInternalRole) {
            result.resources = result.resources.filter(role => !(role.data?.internal === true));
          }
        }
        return result;
      };

    const fetchRoles = testFetchNonInternalRoles();
    const result = await fetchRoles({ page: 1, limit: 10 });

    expect(result).toEqual(mockEmptyResponse);
    expect(result.resources).toBeUndefined();
  });

  it('should handle case when userInfo is null', async () => {
    const testFetchNonInternalRoles =
      () =>
      async (...args) => {
        const userInfo = mockUserInfoNull();
        const userRoleId = userInfo?.roleId;
        const result = await mockRolesSearchAction()(...args);

        if (result && result.resources) {
          const userHasInternalRole = result.resources.some(
            role => role.id === userRoleId && role.data?.internal === true
          );

          if (!userHasInternalRole) {
            result.resources = result.resources.filter(role => !(role.data?.internal === true));
          }
        }
        return result;
      };

    const fetchRoles = testFetchNonInternalRoles();
    const result = await fetchRoles({ page: 1, limit: 10 });

    expect(result.resources.length).toBe(2);
    expect(result.resources.every(role => role.data.internal !== true)).toBe(true);
  });
});
