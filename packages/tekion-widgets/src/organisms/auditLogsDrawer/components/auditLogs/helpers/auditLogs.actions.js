import _get from 'lodash/get';
import _filter from 'lodash/filter';
import _some from 'lodash/some';

import { tget } from '@tekion/tekion-base/utils/general';
import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import TenvReader from '@tekion/tekion-base/readers/Env';
import { rolesSearchAction } from '@tekion/tekion-business/src/actions/rolesActions';
import { getFileDetailsForDownload } from '@tekion/tekion-base/services/exportService';
import { getErrorMessage } from '@tekion/tekion-base/utils/errorUtils';
import getDataFromResponse from '@tekion/tekion-base/utils/getDataFromResponse';
import { TOASTER_TYPE, toaster } from '@tekion/tekion-components/src/organisms/NotificationWrapper';

import {
  downloadAuditLogs,
  downloadAuditLogsDealerLevel,
  fetchClickHouseEnabled,
} from '../../../services/auditLogsService';

export const exportMediaTaskAction = async (exportTaskId = '') => {
  try {
    const fileDownloadResponse = await getFileDetailsForDownload(exportTaskId);
    const data = getDataFromResponse(fileDownloadResponse);
    return data;
  } catch (error) {
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(error));
    return EMPTY_OBJECT;
  }
};

export const downloadAuditLogsAction = async (payload = EMPTY_OBJECT, shouldCallDealerLevelApi) => {
  try {
    const fileDownloadResponse = shouldCallDealerLevelApi
      ? await downloadAuditLogsDealerLevel(payload)
      : await downloadAuditLogs(payload);
    return tget(fileDownloadResponse, 'data', EMPTY_OBJECT);
  } catch (error) {
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(error));
    return EMPTY_OBJECT;
  }
};

export const fetchClickHouseEnabledForAssets = async (payload = EMPTY_OBJECT) => {
  try {
    const response = await fetchClickHouseEnabled(payload);
    const enabled = tget(response, 'data.enabled', false);
    const assetsWithDealerLevelDownload = tget(response, 'data.assetsWithDealerLevelDownload', EMPTY_ARRAY);
    return { enabled, assetsWithDealerLevelDownload };
  } catch {
    return false;
  }
};

export const fetchNonInternalRoles = async (...args) => {
  try {
    const userInfo = TenvReader.userInfo();
    const userRoleId = userInfo?.roleId;
    const result = await rolesSearchAction()(...args);
    if (result?.resources) {
      const userHasInternalRole = _some(
        result.resources,
        role => _get(role, 'id') === userRoleId && _get(role, 'data.internal')
      );
      if (!userHasInternalRole) {
        const filteredResources = _filter(result.resources, role => !_get(role, 'data.internal'));
        return { ...result, resources: filteredResources };
      }
    }
    return result;
  } catch (error) {
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(error));
    return EMPTY_OBJECT;
  }
};
