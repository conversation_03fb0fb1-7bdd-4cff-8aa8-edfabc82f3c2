import _constant from 'lodash/constant';
import _values from 'lodash/values';

import userReader from '@tekion/tekion-base/readers/User';
import FILTER_TYPES from '@tekion/tekion-components/src/organisms/filterSection/constants/filterSection.filterTypes';
import { filterOption } from '@tekion/tekion-business/src/helpers/workspace/workspace.helper';
import { fetchAccessibleWorkspaceAction } from '@tekion/tekion-business/src/actions/workspace.actions';

import { BTW_INCLUSIVE } from '@tekion/tekion-components/src/organisms/filterSection/constants/filterSection.operators';
import { RESOURCE_TYPE } from '@tekion/tekion-base/bulkResolvers';
import { fetchNonInternalRoles } from '../helpers/auditLogs.actions';
import { AUDIT_TYPE_OPTIONS } from '../../activityLogV2/components/ActivityLog/activityLog.constants';

const getUserLabel = data => userReader.name(data?.data);

export const FILTER_IDS = {
  AUDIT_DATE: 'createdTime',
  USER: 'userId',
  ROLE: 'roleId',
  DEALER: 'dealerId',
  AUDIT_TYPE: 'auditType',
  DESTINATION_DEALER: 'destinationDealerId',
};

export const BASIC_FILTER_IDS = new Set(_values(FILTER_IDS));

export const DATE_RANGE_FILTER = {
  id: FILTER_IDS.AUDIT_DATE,
  name: __('Date Range'),
  type: FILTER_TYPES.DATE,
  additional: { operators: [BTW_INCLUSIVE] },
};

export const USER_FILTER = {
  id: FILTER_IDS.USER,
  name: __('User'),
  type: FILTER_TYPES.ASYNC_MULTI_SELECT,
  resourceType: RESOURCE_TYPE.TENANT_USER_MINIMAL_V2,
  additional: {
    getOptionLabel: getUserLabel,
  },
};

export const ROLE_FILTER = {
  id: FILTER_IDS.ROLE,
  name: __('Role'),
  type: FILTER_TYPES.ASYNC_MULTI_SELECT,
  resourceType: 'ROLE_MINIMAL',
  additional: {
    resourceType: 'ROLE_MINIMAL',
    getOptionLabel: getUserLabel,
    lookUpSearchApi: fetchNonInternalRoles,
    lookupByKeysApi: _constant(Promise.resolve([])),
  },
};

export const DEALER_FILTER = {
  id: FILTER_IDS.DEALER,
  name: __('Change Initiated From'),
  type: FILTER_TYPES.ASYNC_MULTI_SELECT,
  resourceType: RESOURCE_TYPE.WORKSPACE_CONTEXTUAL_HIERARCHY,
  additional: {
    resourceType: RESOURCE_TYPE.WORKSPACE_CONTEXTUAL_HIERARCHY,
    lookUpSearchApi: fetchAccessibleWorkspaceAction,
    lookupByKeysApi: _constant(Promise.resolve([])),
    filterOption,
  },
};

export const DESTINATION_DEALER_FILTER = {
  id: FILTER_IDS.DESTINATION_DEALER,
  name: __('Impacted Dealerships'),
  type: FILTER_TYPES.ASYNC_MULTI_SELECT,
  resourceType: RESOURCE_TYPE.WORKSPACE_CONTEXTUAL_HIERARCHY,
  additional: {
    resourceType: RESOURCE_TYPE.WORKSPACE_CONTEXTUAL_HIERARCHY,
    lookUpSearchApi: fetchAccessibleWorkspaceAction,
    lookupByKeysApi: _constant(Promise.resolve([])),
    filterOption,
  },
};

export const AUDIT_TYPE_FILTER = {
  id: FILTER_IDS.AUDIT_TYPE,
  name: __('Change Type'),
  type: FILTER_TYPES.MULTI_SELECT,
  additional: {
    options: AUDIT_TYPE_OPTIONS,
  },
};

export const DEFAULT_FILTER_TYPES = [FILTER_IDS.AUDIT_DATE];
