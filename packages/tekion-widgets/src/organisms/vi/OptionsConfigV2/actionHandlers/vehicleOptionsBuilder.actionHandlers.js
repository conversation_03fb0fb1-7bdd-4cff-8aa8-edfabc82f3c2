import produce from 'immer';
import _find from 'lodash/find';
import _isEmpty from 'lodash/isEmpty';
import _filter from 'lodash/filter';
import _pull from 'lodash/pull';
import _includes from 'lodash/includes';
import _forEach from 'lodash/forEach';
import _isNil from 'lodash/isNil';
import _set from 'lodash/set';
import _get from 'lodash/get';
import _head from 'lodash/head';
import _parseInt from 'lodash/parseInt';
import _omit from 'lodash/omit';
import _noop from 'lodash/noop';

import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import { VEHICLE_TYPES } from '@tekion/tekion-base/constants/vehicleInventory/vehicleTypes';
import { tget } from '@tekion/tekion-base/utils/general';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/organisms/NotificationWrapper';

import {
  KEYS,
  VEHICLE_OPTION_BUILDER_ACTIONS_TYPES,
  OPTION_SOURCES,
  PRICE_UPDATE_TYPE,
  HEADER_TITLE_TYPES,
  PACKS_KEYS_TO_UPDATE,
  VEHICLE_OPTION_CATEGORY,
} from '../constants/vehicleOptionsBuilder.constants';
import PROGRAM_CONFIG from '../constants/programConfig';
import { ADD_OPTIONS, UPDATE_OPTIONS, DELETE_OPTIONS } from '../../Options/options.actionTypes';
import {
  calculateVehiclePrices,
  getFilteredSelectedOptionsCode,
  getFilteredSelectedPacksCode,
  getManualPacksData,
  getOptionsIdListByPackId,
  getPricingDetailsData,
  getPricingDetailsDataForManuallyAddedOptions,
  shouldUpdatePricingOnOptionDelete,
  validateOption,
} from '../helpers/vehicleOptionsBuilder.helpers';
import { CONSUMER_FRIENDLY_TITLE_TO_CATEGORY_ENUM } from '../components/AddVehicleOption/addVehicleOption.constants';
import VehicleOptionsApi from '../services/optionsValidationApi.api';
import { getValidateOptionsPayload } from '../components/AddVehicleOption/addVehicleOption.helpers';

const getModifiedOption = (option, value) =>
  produce(option, draftState => {
    _forEach(PACKS_KEYS_TO_UPDATE, key => {
      const path = key?.split('.') || EMPTY_ARRAY;
      const val = _get(value, key);
      _set(draftState, [...path], val);
    });
  });

const addUpdatePacksOptions = (value, options, actionType, onOptionosAndPartsAction) => {
  const packOptions = _filter(options, option => option[KEYS.PACKAGE_CODE] === value[KEYS.PACKAGE_CODE]);
  _forEach(packOptions, option => {
    const modifiedOption = getModifiedOption(option, value);
    onOptionosAndPartsAction(UPDATE_OPTIONS, [{ item: modifiedOption }]);
  });
  if (!_isNil(value?.[KEYS.OPTION_CODE])) onOptionosAndPartsAction(actionType, [{ item: value }]);
};

const addorUpdateOptions = (value, options, actionType, onOptionosAndPartsAction) => {
  if (
    !PROGRAM_CONFIG.shouldBuildVehicleFromChromeConstruct() &&
    value?.[KEYS.CONSUMER_FRIENDLY_HEADER_NAME] === HEADER_TITLE_TYPES.PACKS
  ) {
    addUpdatePacksOptions(value, options, actionType, onOptionosAndPartsAction);
  } else {
    onOptionosAndPartsAction(actionType, [{ item: value }]);
  }
};

const addOrRemoveOption = async (payload, dealerId) => {
  try {
    const optionsFromResponse = await VehicleOptionsApi.validateOptions(payload, dealerId);
    return tget(optionsFromResponse, 'data.data', EMPTY_ARRAY);
  } catch {
    toaster(TOASTER_TYPE.ERROR, __('Failed To Enable Option'));
    return null;
  }
};

export const fetchRetailPriceExclVAT = async payload => {
  try {
    const response = await VehicleOptionsApi.fetchRetailPriceExclVAT(payload);
    const retailPriceExclVatDetails = _head(response?.data?.data);
    if (!_isEmpty(retailPriceExclVatDetails?.vatCalculationException)) {
      toaster(TOASTER_TYPE.ERROR, __('Failed to fetch retail price excluding VAT'));
      return EMPTY_OBJECT;
    }
    return retailPriceExclVatDetails;
  } catch {
    toaster(TOASTER_TYPE.ERROR, __('Failed to fetch retail price excluding VAT'));
    return EMPTY_OBJECT;
  }
};

const updateVehiclePrices = ({
  getState,
  updatedConfiguredOptionsMsrp,
  updatedRetailPrice,
  retailPriceExcludingVat,
}) => {
  const { updateVehicleDetails, vehicleDetails, onVehiclePriceUpdate = _noop } = getState();
  const vehicleType = _get(vehicleDetails, 'vehicleType');
  updateVehicleDetails([
    { key: 'pricingDetails.configuredOptionsMsrp', value: updatedConfiguredOptionsMsrp },
    ...(vehicleType === VEHICLE_TYPES.NEW
      ? [
          {
            key: 'pricingDetails.retailPrice',
            value: updatedRetailPrice,
          },
          {
            key: 'pricingDetails.retailPriceExcludingVat',
            value: _get(retailPriceExcludingVat, 'priceVatDetail.vatExclusiveAmount'),
          },
        ]
      : []),
  ]);
  onVehiclePriceUpdate();
};

export const shouldUpdatePricingOnOptionAdd = ({ option, allOptions, getState, setState }) => {
  const { manualPacksData: initialManualPacksData } = getState();
  const isPack = option[KEYS.CATEGORY] === VEHICLE_OPTION_CATEGORY.PACK;
  let shouldUpdatePricing = true;
  let priceToUpdate = tget(option, KEYS.PRICE_INCLUDING_VAT, 0);

  if (!isPack) return { shouldUpdatePricing, priceToUpdate };

  let manualPacksData = initialManualPacksData || getManualPacksData(allOptions);
  const packageCode = option[KEYS.PACKAGE_CODE];
  const currentPackagePrice = tget(option, KEYS.PACKAGE_PRICE, 0);
  const previousPackagePrice = _get(manualPacksData, packageCode);

  if (_isNil(previousPackagePrice)) {
    manualPacksData = { ...manualPacksData, [packageCode]: currentPackagePrice };
    setState({ manualPacksData });
    priceToUpdate = currentPackagePrice;
  } else if (previousPackagePrice !== currentPackagePrice) {
    priceToUpdate = currentPackagePrice - previousPackagePrice;
    manualPacksData = { ...manualPacksData, [packageCode]: priceToUpdate };
    setState({ manualPacksData });
  } else {
    shouldUpdatePricing = false;
  }

  return { shouldUpdatePricing, priceToUpdate };
};

export const setManualPackDataOnPackDelete = ({ option, getState, setState }) => {
  const { manualPacksData } = getState();
  const updatedManualPacksData = _omit(manualPacksData, option[KEYS.PACKAGE_CODE]);
  setState({ manualPacksData: updatedManualPacksData });
};

const ACTION_HANDLERS = {
  [VEHICLE_OPTION_BUILDER_ACTIONS_TYPES.OPTION_ITEM_CHECKBOX_CLICK]: async (
    { payload = EMPTY_OBJECT },
    { getState, setState }
  ) => {
    const {
      value,
      addOrRemoveChromeOption,
      setForceUpdatePrices,
      setLoading,
      allOptionsData,
      setCurrentSelectedOptionCodes,
    } = payload;

    const { onOptionosAndPartsAction, dealerId } = getState();

    if (PROGRAM_CONFIG.shouldBuildVehicleFromChromeConstruct()) {
      setLoading(true);
      const { invoicePrice, retailPrice, id } = value || EMPTY_OBJECT;
      if (value[KEYS.SOURCE] === OPTION_SOURCES.CHROME_CONSTRUCT) {
        await addOrRemoveChromeOption({ optionId: id });
        setForceUpdatePrices({ type: PRICE_UPDATE_TYPE.CHROME_PRICE_UPDATE, status: true });
      } else {
        onOptionosAndPartsAction(DELETE_OPTIONS, [{ item: value }]);
        setForceUpdatePrices({
          type: PRICE_UPDATE_TYPE.MANUAL_PRICE_UPDATE,
          invoiceChange: -1 * invoicePrice,
          retailChange: -1 * retailPrice,
          status: true,
        });
      }
    } else {
      setLoading(true);
      const { newValue } = getFilteredSelectedOptionsCode(allOptionsData, value);
      const payloadForAPI = getValidateOptionsPayload(
        allOptionsData.options,
        newValue,
        allOptionsData.selectedOptionCodes
      );
      const response = await addOrRemoveOption(payloadForAPI, dealerId);
      const totalOptionsRetailPrice = await validateOption({
        allOptionsData,
        response,
        setCurrentSelectedOptionCodes,
      });
      setState({ totalOptionsRetailPrice });
      setLoading(false);
    }
  },
  [VEHICLE_OPTION_BUILDER_ACTIONS_TYPES.OPTION_ITEM_EDIT]: async (
    { payload = EMPTY_OBJECT },
    { getState, setState }
  ) => {
    const { setForceUpdatePrices, value, options, selectedOptionCodes, setCurrentSelectedOptionCodes, vehicleDetails } =
      payload;
    const { onOptionosAndPartsAction, priceUpdateForDIOEnabled } = getState();

    const { invoicePrice, retailPrice, dealerInstalledOption } = value || EMPTY_OBJECT;
    const targetOptions = _find(options, { uuid: value?.uuid });

    if (!_isEmpty(targetOptions)) {
      addorUpdateOptions(value, options, UPDATE_OPTIONS, onOptionosAndPartsAction);
      if (!PROGRAM_CONFIG.shouldBuildVehicleFromChromeConstruct()) {
        const priceIncludingVat = tget(value, KEYS.PRICE_INCLUDING_VAT, 0);
        const prevPriceIncludingVat = tget(targetOptions, KEYS.PRICE_INCLUDING_VAT, 0);
        const prevPrices = {
          retailPrice: _parseInt(_get(vehicleDetails, 'pricingDetails.retailPrice')) - prevPriceIncludingVat,
          configuredOptionsMsrp:
            _parseInt(_get(vehicleDetails, 'pricingDetails.configuredOptionsMsrp')) - prevPriceIncludingVat,
        };

        const oldId = targetOptions[KEYS.ID];
        const id = value[KEYS.ID];
        if (oldId !== id) {
          const updatedSelectedOptionCodes = _pull(selectedOptionCodes, oldId);
          setCurrentSelectedOptionCodes([...updatedSelectedOptionCodes, id]);
        }

        const pricingData = getPricingDetailsDataForManuallyAddedOptions({
          payload,
          getState,
          priceIncludingVat,
          isAdding: true,
          prevPrices,
        });
        const prices = await calculateVehiclePrices({ ...pricingData, fetchRetailPriceExclVAT });
        updateVehiclePrices({ getState, ...prices });
      } else {
        const retailChange =
          dealerInstalledOption && !priceUpdateForDIOEnabled ? 0 : retailPrice - targetOptions.retailPrice;
        setForceUpdatePrices({
          type: PRICE_UPDATE_TYPE.MANUAL_PRICE_UPDATE,
          invoiceChange: invoicePrice - targetOptions.invoicePrice,
          retailChange,
          configuredOptionsMsrpChange: retailPrice - targetOptions.retailPrice,
          status: true,
        });
      }
    } else {
      addorUpdateOptions(value, options, ADD_OPTIONS, onOptionosAndPartsAction);

      if (!PROGRAM_CONFIG.shouldBuildVehicleFromChromeConstruct()) {
        const id = value[KEYS.ID];
        setCurrentSelectedOptionCodes([...selectedOptionCodes, id]);

        const { shouldUpdatePricing, priceToUpdate } = shouldUpdatePricingOnOptionAdd({
          option: value,
          allOptions: options,
          getState,
          setState,
        });
        if (shouldUpdatePricing) {
          const pricingData = getPricingDetailsDataForManuallyAddedOptions({
            payload,
            getState,
            priceIncludingVat: priceToUpdate,
            isAdding: true,
          });
          const prices = await calculateVehiclePrices({ ...pricingData, fetchRetailPriceExclVAT });
          updateVehiclePrices({ getState, ...prices });
        }
      } else {
        const isManualDealerAddOnOption =
          value?.source === OPTION_SOURCES.PRE_CONFIGURED && value?.metadata?.manualAddOn;
        const retailChange =
          isManualDealerAddOnOption || (dealerInstalledOption && !priceUpdateForDIOEnabled) ? 0 : retailPrice;
        setForceUpdatePrices({
          type: PRICE_UPDATE_TYPE.MANUAL_PRICE_UPDATE,
          invoiceChange: invoicePrice,
          retailChange,
          configuredOptionsMsrpChange: isManualDealerAddOnOption ? 0 : retailPrice,
          status: true,
        });
      }
    }
  },
  [VEHICLE_OPTION_BUILDER_ACTIONS_TYPES.OPTION_ITEM_DELETE]: async (
    { payload = EMPTY_OBJECT },
    { getState, setState }
  ) => {
    const { setForceUpdatePrices, value, setCurrentSelectedOptionCodes, selectedOptionCodes, options } = payload;
    const { onOptionosAndPartsAction, priceUpdateForDIOEnabled } = getState();

    const { invoicePrice, retailPrice, dealerInstalledOption } = value || EMPTY_OBJECT;
    const isManualDealerAddOnOption = value?.source === OPTION_SOURCES.PRE_CONFIGURED;
    const retailChange =
      isManualDealerAddOnOption || (dealerInstalledOption && !priceUpdateForDIOEnabled) ? 0 : retailPrice;
    onOptionosAndPartsAction(DELETE_OPTIONS, [{ item: value }]);

    if (!PROGRAM_CONFIG.shouldBuildVehicleFromChromeConstruct()) {
      const isPackOption = value[KEYS.CATEGORY] === VEHICLE_OPTION_CATEGORY.PACK;
      const priceToUpdate = isPackOption
        ? tget(value, KEYS.PACKAGE_PRICE, 0)
        : tget(value, KEYS.PRICE_INCLUDING_VAT, 0);

      const id = value[KEYS.ID];
      const filteredOptionCodeList = _filter(selectedOptionCodes, optionCode => optionCode !== id);
      setCurrentSelectedOptionCodes([...filteredOptionCodeList]);

      const shouldUpdatePricing = shouldUpdatePricingOnOptionDelete(value, options);
      if (shouldUpdatePricing) {
        const pricingData = getPricingDetailsDataForManuallyAddedOptions({
          payload,
          getState,
          priceIncludingVat: priceToUpdate,
          isAdding: false,
        });
        const prices = await calculateVehiclePrices({ ...pricingData, fetchRetailPriceExclVAT });
        updateVehiclePrices({ getState, ...prices });
        if (isPackOption) setManualPackDataOnPackDelete({ option: value, getState, setState });
      }
      return;
    }

    setForceUpdatePrices({
      type: PRICE_UPDATE_TYPE.MANUAL_PRICE_UPDATE,
      invoiceChange: -1 * invoicePrice,
      retailChange: -1 * retailChange,
      configuredOptionsMsrpChange: isManualDealerAddOnOption ? 0 : -1 * retailPrice,
      status: true,
    });
  },
  [VEHICLE_OPTION_BUILDER_ACTIONS_TYPES.OPTION_ITEM_GROUP_CHANGE]: ({ payload = EMPTY_OBJECT }, { getState }) => {
    const { optionIds, groupId, allOptionsData } = payload;
    const { onOptionosAndPartsAction } = getState();

    optionIds.forEach(optionId => {
      const targetOption = allOptionsData?.options?.find(el => el[KEYS.OPTION_CODE] === optionId);
      if (targetOption) {
        onOptionosAndPartsAction(UPDATE_OPTIONS, [
          {
            item: {
              ...targetOption,
              [KEYS.CONSUMER_FRIENDLY_HEADER_NAME]: groupId,
              [KEYS.IS_GROUP_MANUALLY_CREATED]: true,
              ...(PROGRAM_CONFIG.shouldShowEUV2Options()
                ? { [KEYS.CATEGORY]: CONSUMER_FRIENDLY_TITLE_TO_CATEGORY_ENUM[groupId] }
                : EMPTY_OBJECT),
            },
          },
        ]);
      }
    });
  },
  [VEHICLE_OPTION_BUILDER_ACTIONS_TYPES.PACK_ITEM_CHECKBOX_CLICK]: async (
    { payload = EMPTY_OBJECT },
    { setState, getState }
  ) => {
    const { value, allOptionsData, setCurrentSelectedOptionCodes, setLoading } = payload;
    const { dealerId } = getState();
    const { selectedPacks, newValue } = getFilteredSelectedPacksCode(allOptionsData, value);

    if (PROGRAM_CONFIG.shouldShowEUV2Options()) {
      setLoading(true);
      const payloadForAPI = getValidateOptionsPayload(
        allOptionsData.options,
        newValue,
        allOptionsData.selectedOptionCodes
      );
      const response = await addOrRemoveOption(payloadForAPI, dealerId);
      const totalOptionsRetailPrice = await validateOption({
        allOptionsData,
        response,
        setCurrentSelectedOptionCodes,
      });
      setState({ totalOptionsRetailPrice });
      setLoading(false);
    } else {
      setCurrentSelectedOptionCodes([...selectedPacks]);
    }
  },
  [VEHICLE_OPTION_BUILDER_ACTIONS_TYPES.PACK_ITEM_DELETE]: async (
    { payload = EMPTY_OBJECT },
    { getState, setState }
  ) => {
    const { value: selectedPack, setCurrentSelectedOptionCodes, allOptionsData } = payload;
    const { options, selectedOptionCodes } = allOptionsData;
    const optionsIdListForPack = getOptionsIdListByPackId(options, selectedPack);
    const filteredOptionCodeList = _filter(selectedOptionCodes, id => !_includes(optionsIdListForPack, id));
    const priceToUpdate = tget(selectedPack, KEYS.PACKAGE_PRICE, 0);
    const pricingData = getPricingDetailsDataForManuallyAddedOptions({
      payload,
      getState,
      priceIncludingVat: priceToUpdate,
      isAdding: false,
    });
    const prices = await calculateVehiclePrices({ ...pricingData, fetchRetailPriceExclVAT });
    updateVehiclePrices({ getState, ...prices });
    setCurrentSelectedOptionCodes([...filteredOptionCodeList]);
    setManualPackDataOnPackDelete({ selectedPack, getState, setState });
  },
  [VEHICLE_OPTION_BUILDER_ACTIONS_TYPES.UPDATE_PRICE_ON_SAVE]: async ({ payload = EMPTY_OBJECT }, { getState }) => {
    const pricingData = getPricingDetailsData(payload, getState);
    const prices = await calculateVehiclePrices({ ...pricingData, fetchRetailPriceExclVAT });

    updateVehiclePrices({ getState, ...prices });
  },
};

export default ACTION_HANDLERS;
