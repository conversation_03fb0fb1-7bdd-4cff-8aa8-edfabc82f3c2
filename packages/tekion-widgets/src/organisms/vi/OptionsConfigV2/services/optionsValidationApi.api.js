import Http from '@tekion/tekion-base/services/apiService/httpClient';
import { URL_TYPES } from '@tekion/tekion-base/constants/api';
import { EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { stringInterpolate } from '@tekion/tekion-base/formatters/string';

const API_PATHS = {
  VALIDATE_OPTIONS: 'vi/u/v1.0.0/options/validate{dealerIdParam}',
  FETCH_RETAIL_PRICE_EXCLUDING_VAT: '/api-vi/u/external/galaxy/calculate/RETAIL_PRICE_EXCLUDING_VAT',
};

export default class VehicleOptionsApi {
  static async validateOptions(payload = EMPTY_OBJECT, dealerIdParam = EMPTY_STRING) {
    return Http.post(URL_TYPES.CDMS, stringInterpolate(API_PATHS.VALIDATE_OPTIONS, { dealerIdParam }), payload);
  }

  static fetchRetailPriceExclVAT(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.FETCH_RETAIL_PRICE_EXCLUDING_VAT, payload);
  }
}
