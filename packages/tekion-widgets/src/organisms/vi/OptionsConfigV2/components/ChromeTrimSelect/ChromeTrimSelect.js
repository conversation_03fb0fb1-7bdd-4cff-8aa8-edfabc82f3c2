import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { compose } from 'recompose';
import _noop from 'lodash/noop';
import _get from 'lodash/get';
import _isEqual from 'lodash/isEqual';
import _some from 'lodash/some';
import _toString from 'lodash/toString';
import _isObject from 'lodash/isObject';
import _isEmpty from 'lodash/isEmpty';

import { EMPTY_STRING, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import { formatCurrencyWithMoneyFormat } from '@tekion/tekion-base/marketScan/utils';
import usePrevious from '@tekion/tekion-base/customHooks/usePrevious';
import { DEAL_TYPES } from '@tekion/tekion-base/constants/deal/type';
import SelectWithGroups from '@tekion/tekion-components/src/molecules/selectWithGroups';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import Tooltip, { TOOLTIP_PLACEMENT } from '@tekion/tekion-components/src/atoms/tooltip';
import FontIcon from '@tekion/tekion-components/src/atoms/FontIcon';
import withActionHandlers from '@tekion/tekion-components/src/connectors/withActionHandlers';

import colors from 'tstyles/component.scss';

import { getStylesByGroups } from './chromeTrimSelect.helper';
import ChromeApi from '../../services/chromeConfigOptions.api';
import { getTrimSelectToasterMessage } from '../../../../../helpers/vehicleInventory/vehicleOptions.helpers';

import { ACTION_TYPES } from './chromeTrimSelect.constants';
import ACTION_HANDLERS from './chromeTrimSelect.actionHandlers';

import styles from './ChromeTrimSelect.module.scss';

const headerOption = { label: 'Trim Name', labelSufix: 'Total Retail', optionClassName: styles.optionClassName };

const ChromeTrimSelect = props => {
  const {
    year,
    make,
    model,
    updateVehicleDetails,
    value,
    disableVehicleConfigurator,
    showNextButton,
    dealType,
    viewOnly,
    removeDefaultOptions,
    isVehicleUsed,
    onAction,
    isEditMode,
    moduleType,
    shouldHideInvoiceTooltip,
  } = props;
  const prevProps = usePrevious({ year, make, model, isVehicleUsed });

  const orderAvailability = DEAL_TYPES.FLEET === dealType ? 'Fleet' : 'Retail';
  const [styleOptions, setStylesOptions] = useState(EMPTY_ARRAY);
  const [isLoading, setisLoading] = useState(false);
  const [disabled, setDisables] = useState(true);
  const [selectedStyleId, setSelectedStyleId] = useState(null);
  const [isLoadingOption, setIsLoadingOption] = useState(false);

  const getOptionsByStyle = useCallback(
    styleId =>
      onAction({
        type: ACTION_TYPES.GET_OPTION_BY_STYLE_ID,
        payload: {
          styleId,
          orderAvailability,
          removeDefaultOptions,
          isVehicleUsed,
          updateVehicleDetails,
          setSelectedStyleId,
          setIsLoadingOption,
          moduleType,
        },
      }),
    [updateVehicleDetails, orderAvailability, isVehicleUsed, onAction, removeDefaultOptions]
  );

  const suffixBuilder = useCallback(
    (baseMsrp, baseInvoice, shouldHideInvoiceTooltip = false) => (
      <span className="d-flex flex-row justify-content-between align-items-center">
        {formatCurrencyWithMoneyFormat(baseMsrp)} &nbsp;
        {!shouldHideInvoiceTooltip && (
          <Tooltip
            placement={TOOLTIP_PLACEMENT.TOP}
            autoAdjustOverflow
            title={`Invoice Price: ${formatCurrencyWithMoneyFormat(baseInvoice)}`}
            overlayClassName={styles.smallTooltip}>
            <FontIcon color={colors.black}>icon-info</FontIcon>
          </Tooltip>
        )}
      </span>
    ),
    []
  );

  useEffect(() => {
    const fetchOptionData = async () => {
      const styleIdFromVinLookup = !_isObject(value) && value ? _toString(value) : null;
      const isStyleIdPresent = _some(styleOptions, { value: styleIdFromVinLookup });

      if (isStyleIdPresent && !isEditMode && !_isEqual(value, selectedStyleId)) {
        await getOptionsByStyle(value);
      } else if (styleOptions.length && !isStyleIdPresent && !isEditMode && styleIdFromVinLookup) {
        toaster(TOASTER_TYPE.INFO, getTrimSelectToasterMessage(moduleType));
      }
    };
    fetchOptionData();
  }, [value, isEditMode, styleOptions, getOptionsByStyle]);

  useEffect(() => {
    const getStyles = async () => {
      setStylesOptions([]);
      setisLoading(true);
      try {
        const rawResponse = await ChromeApi.fetchStyles({
          model,
          year,
          make,
          orderAvailability,
          isVehicleUsed,
        });

        const stylesByModel = getStylesByGroups(
          _get(rawResponse, 'data.style', []),
          isVehicleUsed ? _noop : suffixBuilder,
          shouldHideInvoiceTooltip
        );

        if (stylesByModel.length) {
          setStylesOptions(stylesByModel);
        } else {
          disableVehicleConfigurator();
        }
      } catch (error) {
        toaster(TOASTER_TYPE.ERROR, __('Trim details could not be fetched'));
      } finally {
        setisLoading(false);
      }
    };

    if (!model && !_isEmpty(styleOptions)) setStylesOptions([]);

    if (year && make && model && !_isEqual({ year, make, model, isVehicleUsed }, prevProps)) {
      getStyles();
      setDisables(false);
    } else {
      setDisables(false);
    }
  }, [year, make, model, prevProps, disableVehicleConfigurator, orderAvailability, isVehicleUsed, suffixBuilder]);

  const handleOnStyleIdChange = useCallback(
    (...params) =>
      onAction({
        type: ACTION_TYPES.ON_STYLE_ID_CHANGE,
        payload: {
          params,
          getOptionsByStyle,
          showNextButton,
        },
      }),
    [getOptionsByStyle, showNextButton, onAction]
  );

  return (
    <SelectWithGroups
      {...props}
      value={`${value}`}
      onAction={_noop}
      onSelect={handleOnStyleIdChange}
      options={styleOptions}
      disabled={disabled || viewOnly || isLoadingOption}
      headerOption={headerOption}
      loading={isLoading}
    />
  );
};

ChromeTrimSelect.propTypes = {
  viewOnly: PropTypes.bool,
  year: PropTypes.any,
  make: PropTypes.string,
  model: PropTypes.string,
  value: PropTypes.any,
  dealType: PropTypes.any,
  updateVehicleDetails: PropTypes.func,
  disableVehicleConfigurator: PropTypes.func,
  showNextButton: PropTypes.func,
  removeDefaultOptions: PropTypes.bool,
  isVehicleUsed: PropTypes.bool,
  onAction: PropTypes.func,
  isEditMode: PropTypes.bool,
  moduleType: PropTypes.string,
  shouldHideInvoiceTooltip: PropTypes.bool,
};

ChromeTrimSelect.defaultProps = {
  viewOnly: false,
  year: EMPTY_STRING,
  make: EMPTY_STRING,
  model: EMPTY_STRING,
  value: EMPTY_STRING,
  dealType: EMPTY_STRING,
  updateVehicleDetails: _noop,
  disableVehicleConfigurator: _noop,
  showNextButton: _noop,
  removeDefaultOptions: false,
  isVehicleUsed: false,
  onAction: _noop,
  isEditMode: false,
  moduleType: EMPTY_STRING,
  shouldHideInvoiceTooltip: false,
};

export default compose(withActionHandlers(ACTION_HANDLERS), React.memo)(ChromeTrimSelect);
