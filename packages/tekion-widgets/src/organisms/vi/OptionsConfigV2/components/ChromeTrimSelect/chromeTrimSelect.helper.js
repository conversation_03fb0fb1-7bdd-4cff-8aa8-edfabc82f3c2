import _sortBy from 'lodash/sortBy';
import _map from 'lodash/map';

import { EMPTY_OBJECT, EMPTY_ARRAY, NO_DATA } from '@tekion/tekion-base/app.constants';

import { getStyleUserFriendlyText } from '../../helpers/chromeConfigOptions.helper';

export const getStylesByGroups = (
  data = EMPTY_ARRAY,
  suffixBuilder = () => EMPTY_OBJECT,
  shouldHideInvoiceTooltip = false
) =>
  _sortBy(
    _map(data, trim => {
      const { styleId, baseMsrp, baseInvoice, trimName } = trim;
      return {
        label: getStyleUserFriendlyText(trim),
        value: `${styleId}`,
        group: trimName || NO_DATA,
        labelSufix: suffixBuilder(baseMsrp, baseInvoice, shouldHideInvoiceTooltip),
      };
    }),
    'trimName'
  ) || EMPTY_ARRAY;
