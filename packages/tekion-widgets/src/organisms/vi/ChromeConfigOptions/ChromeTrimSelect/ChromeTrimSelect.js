import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import _get from 'lodash/get';
import _noop from 'lodash/noop';
import _isEqual from 'lodash/isEqual';
import _isEmpty from 'lodash/isEmpty';
import _some from 'lodash/some';
import _toString from 'lodash/toString';
import _isObject from 'lodash/isObject';

import SelectWithGroups from '@tekion/tekion-components/src/molecules/selectWithGroups';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import Tooltip from '@tekion/tekion-components/src/atoms/tooltip';
import FontIcon from '@tekion/tekion-components/src/atoms/FontIcon';
import { EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { formatCurrencyWithMoneyFormat } from '@tekion/tekion-base/marketScan/utils';
import usePrevious from '@tekion/tekion-base/customHooks/usePrevious';
import { DEAL_TYPES } from '@tekion/tekion-base/constants/deal/type';
import colors from 'tstyles/component.scss';

import { getStylesByGroups } from './ChromeTrimSelect.helper';
import ChromeApi from '../ChromeConfigOptions.api';
import {
  EMPTY_CHROME_DATA,
  VEHICLE_UPDATE_TYPES,
  CHROME_SELECTED_STATES,
} from '../VehicleOptionsBuilder/VehicleOptionsBuilder.constants';
import {
  getFormattedChromeData,
  getObjectsForvehicleUpdate,
} from '../VehicleOptionsBuilder/VehicleOptionsBuilder.helper';
import { optionsPricesLogics } from '../ChromeConfigOptions.helper';
import { getTrimSelectToasterMessage } from '../../../../helpers/vehicleInventory/vehicleOptions.helpers';

import styles from './ChromeTrimSelect.module.scss';

const suffixBuilder = (baseMsrp, baseInvoice, shouldHideInvoiceTooltip = false) => (
  <span className="d-flex flex-row justify-content-between align-items-center">
    {formatCurrencyWithMoneyFormat(baseMsrp)} &nbsp;
    {!shouldHideInvoiceTooltip && (
      <Tooltip
        placement="top"
        autoAdjustOverflow
        title={`Invoice Price: ${formatCurrencyWithMoneyFormat(baseInvoice)}`}
        overlayClassName={styles.smallTooltip}>
        <FontIcon color={colors.black}>icon-info</FontIcon>
      </Tooltip>
    )}
  </span>
);

const ChromeTrimSelect = props => {
  const {
    year,
    make,
    model,
    updateVehicleDetails,
    value,
    disableVehicleConfigurator,
    showNextButton,
    dealType,
    viewOnly,
    removeDefaultOptions,
    isVehicleUsed,
    isEditMode,
    moduleType,
    shouldHideInvoiceTooltip,
  } = props;

  const prevProps = usePrevious({ year, make, model, isVehicleUsed });
  const orderAvailability = DEAL_TYPES.FLEET === dealType ? 'Fleet' : 'Retail';
  const [styleOptions, setStylesOptions] = useState([]);
  const [disabled, setDisables] = useState(true);
  const [isLoading, setisLoading] = useState(false);
  const [selectedStyleId, setSelectedStyleId] = useState(null);
  const [isLoadingOption, setIsLoadingOption] = useState(false);

  const getOptionsByStyle = useCallback(
    async styleId => {
      try {
        setSelectedStyleId(styleId);
        setIsLoadingOption(true);
        const rawResponse = await ChromeApi.fetchOptionsByStyle({
          style: styleId,
          orderAvailability,
          isVehicleUsed,
        });
        const sanitizedResponse = removeDefaultOptions ? optionsPricesLogics(rawResponse) : rawResponse;

        const data = _get(sanitizedResponse, 'data', {});
        const formattedChromeData = getFormattedChromeData(data, CHROME_SELECTED_STATES, moduleType);
        updateVehicleDetails([
          ...getObjectsForvehicleUpdate(
            VEHICLE_UPDATE_TYPES.STYLEID_SERIALVALUE_TO_VEHICLE,
            formattedChromeData,
            {},
            moduleType
          ),
          ...getObjectsForvehicleUpdate(
            VEHICLE_UPDATE_TYPES.CHROME_VEHICLE_TO_VEHICLE,
            formattedChromeData,
            {},
            moduleType
          ),
          ...getObjectsForvehicleUpdate(
            VEHICLE_UPDATE_TYPES.PRICES_TO_VEHICLE,
            isVehicleUsed ? { pricing: {} } : formattedChromeData,
            {},
            moduleType
          ),
          ...getObjectsForvehicleUpdate(VEHICLE_UPDATE_TYPES.OPTIONS_TO_VEHICLE, formattedChromeData, {}, moduleType),
        ]);
      } catch (err) {
        setSelectedStyleId(null);
        updateVehicleDetails([
          ...getObjectsForvehicleUpdate(
            VEHICLE_UPDATE_TYPES.STYLEID_SERIALVALUE_TO_VEHICLE,
            EMPTY_CHROME_DATA,
            {},
            moduleType
          ),
          ...getObjectsForvehicleUpdate(
            VEHICLE_UPDATE_TYPES.CHROME_VEHICLE_TO_VEHICLE,
            EMPTY_CHROME_DATA,
            {},
            moduleType
          ),
          ...getObjectsForvehicleUpdate(VEHICLE_UPDATE_TYPES.PRICES_TO_VEHICLE, EMPTY_CHROME_DATA, {}, moduleType),
          ...getObjectsForvehicleUpdate(VEHICLE_UPDATE_TYPES.OPTIONS_TO_VEHICLE, EMPTY_CHROME_DATA, {}, moduleType),
        ]);
        toaster(TOASTER_TYPE.ERROR, __('Failed to fetch vehicle information'));
      } finally {
        setIsLoadingOption(false);
      }
    },
    [updateVehicleDetails, orderAvailability, isVehicleUsed]
  );

  useEffect(() => {
    const fetchOptionData = async () => {
      const styleIdFromVinLookup = !_isObject(value) && value ? _toString(value) : null;
      const isStyleIdPresent = _some(styleOptions, { value: styleIdFromVinLookup });
      if (isStyleIdPresent && !isEditMode && !_isEqual(value, selectedStyleId)) {
        await getOptionsByStyle(value);
      } else if (styleOptions.length && !isStyleIdPresent && !isEditMode && styleIdFromVinLookup) {
        toaster(TOASTER_TYPE.INFO, getTrimSelectToasterMessage(moduleType));
      }
    };

    fetchOptionData();
  }, [value, isEditMode, styleOptions, getOptionsByStyle]);

  useEffect(() => {
    const getStyles = async () => {
      setStylesOptions([]);
      setisLoading(true);

      try {
        const rawResponse = await ChromeApi.fetchStyles({
          model,
          year,
          make,
          orderAvailability,
          isVehicleUsed,
        });

        const stylesByModel = getStylesByGroups(
          _get(rawResponse, 'data.style', []),
          isVehicleUsed ? _noop : suffixBuilder,
          shouldHideInvoiceTooltip
        );

        if (stylesByModel.length) {
          setStylesOptions(stylesByModel);
        } else {
          disableVehicleConfigurator();
        }
      } catch (error) {
        toaster(TOASTER_TYPE.ERROR, __('Trim details could not be fetched'));
      } finally {
        setisLoading(false);
      }
    };

    if (!model && !_isEmpty(styleOptions)) setStylesOptions([]);

    if (year && make && model && !_isEqual({ year, make, model, isVehicleUsed }, prevProps)) {
      getStyles();
      setDisables(false);
    } else {
      setDisables(false);
    }
  }, [year, make, model, prevProps, disableVehicleConfigurator, orderAvailability, isVehicleUsed]);

  const onActionHandler = (...params) => {
    const styleId = params?.[0];
    getOptionsByStyle(styleId);
    showNextButton();
  };

  return (
    <SelectWithGroups
      {...props}
      value={`${value}`}
      onAction={_noop}
      onSelect={onActionHandler}
      options={styleOptions}
      disabled={disabled || viewOnly || isLoadingOption}
      headerOption={{ label: 'Trim Name', labelSufix: 'Total Retail', optionClassName: styles.optionClassName }}
      loading={isLoading}
    />
  );
};

ChromeTrimSelect.propTypes = {
  viewOnly: PropTypes.bool,
  year: PropTypes.any,
  make: PropTypes.string,
  model: PropTypes.string,
  value: PropTypes.any,
  dealType: PropTypes.any,
  updateVehicleDetails: PropTypes.func,
  disableVehicleConfigurator: PropTypes.func,
  showNextButton: PropTypes.func,
  removeDefaultOptions: PropTypes.bool,
  isVehicleUsed: PropTypes.bool,
  isEditMode: PropTypes.bool,
  moduleType: PropTypes.string,
  shouldHideInvoiceTooltip: PropTypes.bool,
};

ChromeTrimSelect.defaultProps = {
  viewOnly: false,
  year: EMPTY_STRING,
  make: EMPTY_STRING,
  model: EMPTY_STRING,
  value: EMPTY_STRING,
  dealType: EMPTY_STRING,
  updateVehicleDetails: _noop,
  disableVehicleConfigurator: _noop,
  showNextButton: _noop,
  removeDefaultOptions: false,
  isVehicleUsed: false,
  isEditMode: false,
  moduleType: EMPTY_STRING,
  shouldHideInvoiceTooltip: false,
};

export default ChromeTrimSelect;
