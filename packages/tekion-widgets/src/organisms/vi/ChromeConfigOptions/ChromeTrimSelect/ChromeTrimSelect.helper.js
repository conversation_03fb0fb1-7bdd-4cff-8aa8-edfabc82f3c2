import _sortBy from 'lodash/sortBy';
import _map from 'lodash/map';

import { getStyleUserFriendlyText } from '../ChromeConfigOptions.helper';

export const getStylesByGroups = (data = [], suffixBuilder = () => {}, shouldHideInvoiceTooltip = false) =>
  _sortBy(
    _map(data, trim => {
      const { styleId, baseMsrp, baseInvoice, trimName } = trim;
      return {
        label: getStyleUserFriendlyText(trim),
        value: `${styleId}`,
        group: trimName || '-',
        labelSufix: suffixBuilder(baseMsrp, baseInvoice, shouldHideInvoiceTooltip),
      };
    }),
    'trimName'
  ) || [];
