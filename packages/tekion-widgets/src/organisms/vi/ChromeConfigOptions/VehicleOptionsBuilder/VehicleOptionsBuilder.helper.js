import { defaultMemoize } from 'reselect';
import _get from 'lodash/get';
import _uniqBy from 'lodash/uniqBy';
import _map from 'lodash/map';

import { addPrices, arraySumBasedOnKey, formatCurrencyWithMoneyFormat } from '@tekion/tekion-base/marketScan/utils';
import { EMPTY_OBJECT, EMPTY_ARRAY, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { MARKET_CLASS_CATEGORY_OPTIONS } from '@tekion/tekion-base/constants/vehicleInventory/trim';
import { formatPrice, tget } from '@tekion/tekion-base/utils/general';
import MODULE from '@tekion/tekion-base/constants/appServices';
import { uuid } from '@tekion/tekion-components/src/utils';
import { getTransferBalancePrice } from '@tekion/tekion-business/src/appServices/vehicleInventory/helpers/vehiclePricing';
import DealerPropertyHelper from '@tekion/tekion-components/src/helpers/sales/dealerPropertyHelper';

import {
  KEYS,
  OPTION_SOURCES,
  API_KEYS_TO_CONVERT,
  VEHICLE_DETAILS_KEYS,
  DEFAULT_OPTION_VALUES,
  CHROME_OPTIONS_CONFLICT_STATUSES,
  CHROME_SELECTED_STATES,
  VEHICLE_UPDATE_TYPES,
  AIR_TAX_PRICE_OPTION_CODE,
  VEHICLE_PRICE_TYPE,
} from './VehicleOptionsBuilder.constants';
import { getStyleUserFriendlyText } from '../ChromeConfigOptions.helper';
import {
  getExteriorColorData,
  getInteriorColorData,
} from '../../../../helpers/vehicleInventory/vehicleOptions.helpers';

const {
  MANUFACTURER_MODEL_CODE,
  TRIM_DRIVE_TYPE,
  TRIM_BODY_TYPE,
  TRIM_BODY_CLASS,
  TRIM_NAME,
  BASE_INVOICE,
  BASE_RETAIL,
  RETAIL_PRICE,
  INVOICE_PRICE,
  MSRP,
  SELLING_PRICE,
  MRM,
  FREIGHT_IN_PRICE,
  AIR_TAX_PRICE,
  OPTIONS_MSRP,
  OPTIONS_INVOICE,
  EXTERIOR_COLOR,
  TRANSFER_BALANCE,
  EXTERIOR_COLOR_CODE,
  INTERIOR_COLOR,
  INTERIOR_COLOR_CODE,
} = VEHICLE_DETAILS_KEYS;

const getDescriptionText = descriptions => descriptions?.reduce((acc, curr) => `${acc}${curr?.description || ''} `, '');

// convert data from chrome construct to deal keys to support for exisiting options from vinlookup or deal
const mapApiFieldsToDealKeys = (data = EMPTY_ARRAY) =>
  data.map(option => ({
    ...option,
    [KEYS.ID]: option[KEYS.ID] || option[KEYS.OPTION_CODE] || option[API_KEYS_TO_CONVERT.OPTION_CODE],
    [KEYS.HEADER_TITLE]: option[KEYS.HEADER_TITLE] || option[API_KEYS_TO_CONVERT.HEADER_TITLE] || '-',
    [KEYS.OPTION_CODE]: option[KEYS.OPTION_CODE] || option[API_KEYS_TO_CONVERT.OPTION_CODE],
    [KEYS.INVOICE_PRICE]: option[KEYS.INVOICE_PRICE] || option[API_KEYS_TO_CONVERT.INVOICE_PRICE],
    [KEYS.RETAIL_PRICE]: option[KEYS.RETAIL_PRICE] || option[API_KEYS_TO_CONVERT.RETAIL_PRICE],
    [KEYS.DESCRIPTION]: (option[KEYS.DESCRIPTION] || getDescriptionText(option.descriptions || EMPTY_ARRAY)).replace(
      /(<([^>]+)>)/gi,
      ''
    ),
    [KEYS.SOURCE]: option[KEYS.SOURCE] || OPTION_SOURCES.CHROME_CONSTRUCT,
  }));

const mergeOptions = defaultMemoize(
  (chromeOptions = EMPTY_ARRAY, allOptions = EMPTY_ARRAY, selectionStates = EMPTY_ARRAY) => {
    // add missing data for all options & default title as uncategorized
    const formattedData =
      chromeOptions
        ?.filter(option => option[KEYS.SOURCE] === OPTION_SOURCES.CHROME_CONSTRUCT)
        ?.map((option, index) => ({
          ...option,
          [KEYS.ID]: option[KEYS.ID] || option[KEYS.OPTION_CODE],
          [KEYS.HEADER_TITLE]: option[KEYS.HEADER_TITLE] || DEFAULT_OPTION_VALUES.HEADER_TITLE,
          index,
        })) || [];

    // add missing data for manual options
    const formattedManulOptions =
      allOptions
        ?.filter(option => option[KEYS.SOURCE] !== OPTION_SOURCES.CHROME_CONSTRUCT)
        ?.map(option => ({
          ...option,
          [KEYS.ID]: option[KEYS.ID] || option[KEYS.OPTION_CODE],
          [KEYS.HEADER_TITLE]: option[KEYS.HEADER_TITLE] || DEFAULT_OPTION_VALUES.HEADER_TITLE,
          [KEYS.UUID]: option[KEYS.UUID] || uuid(),
          [KEYS.CHROME_SELECTION_STATE]: option[KEYS.CHROME_SELECTION_STATE] || DEFAULT_OPTION_VALUES.SELECTION_STATE,
        })) || [];

    // merge options ** DON NOT CHANGE THE BELOW ORDER
    const merged = _uniqBy([...formattedData, ...formattedManulOptions], KEYS.ID);

    // filter on selection state
    return selectionStates?.length
      ? merged?.filter(option => selectionStates.includes(option?.[KEYS.CHROME_SELECTION_STATE]))
      : merged;
  }
);

export const getOptionsBySelectionStates = defaultMemoize(
  (formattedOptions = EMPTY_ARRAY, selectionStates = EMPTY_ARRAY) =>
    formattedOptions?.filter(option => selectionStates.includes(option?.[KEYS.CHROME_SELECTION_STATE]))
);

export const getOptionCodesBySelectionStates = defaultMemoize(
  (formattedOptions = EMPTY_ARRAY, selectionStates = EMPTY_ARRAY) =>
    getOptionsBySelectionStates(formattedOptions, selectionStates).map(option => option[KEYS.OPTION_CODE])
);

export const getOptionsById = (options = EMPTY_ARRAY, ids = EMPTY_ARRAY) =>
  options.filter(option => ids.includes(option[KEYS.OPTION_CODE]));

const getOptionsPricingDetails = (options = EMPTY_ARRAY) => ({
  manualInvoice: arraySumBasedOnKey(options, 0, KEYS.INVOICE_PRICE),
  manualRetail: arraySumBasedOnKey(options, 0, KEYS.RETAIL_PRICE),
});

const getAirTaxPriceForChromeOption = (options = []) => {
  const airTaxOption = getOptionsById(options, [AIR_TAX_PRICE_OPTION_CODE]);
  return airTaxOption && airTaxOption.length ? airTaxOption[0]?.retailPrice : 0;
};

// convert options to dealkeys and extract other chrome info to vehicle obj format
export const getFormattedChromeData = (data = EMPTY_OBJECT, selectionStates = EMPTY_ARRAY, moduleType) => {
  const configuration = _get(data, 'configuration', EMPTY_OBJECT);
  const chromeSerializedValue = _get(configuration, ['style', 'configurationState', 'serializedValue'], EMPTY_STRING);
  const options = mapApiFieldsToDealKeys(_get(configuration, 'options', EMPTY_ARRAY));

  const { exteriorColor, exteriorColorCode } = getExteriorColorData(options);
  const { interiorColor, interiorColorCode } = getInteriorColorData(options);

  const { status, conflictResolvingChromeOptionCodes = EMPTY_ARRAY } = data;
  const isOptionsConflict = CHROME_OPTIONS_CONFLICT_STATUSES.includes(status);

  const {
    isFullyConfigured = false,
    configuredTotalMsrp = 0,
    configuredTotalInvoice = 0,
    configuredOptionsMsrp = 0,
    configuredOptionsInvoice = 0,
    style = EMPTY_OBJECT,
  } = configuration || EMPTY_OBJECT;
  const selectedStyleName = getStyleUserFriendlyText(style || EMPTY_OBJECT);
  const {
    baseMsrp = 0,
    baseInvoice = 0,
    destination = 0,
    styleId = EMPTY_STRING,
    manufacturerModelCode: mfrModelCode = EMPTY_STRING,
    consumerFriendlyDrivetrain: driveType = EMPTY_STRING,
    consumerFriendlyBodyType: bodyType = EMPTY_STRING,
    marketClassName: bodyClass = EMPTY_STRING,
    trimName = EMPTY_STRING,
  } = style || EMPTY_OBJECT;
  const chromeAirTaxPrice = getAirTaxPriceForChromeOption(options);

  return {
    options: selectionStates.length
      ? options.filter(option => selectionStates.includes(option?.[KEYS.CHROME_SELECTION_STATE]))
      : options,
    pricing: {
      configuredOptionsMsrp,
      configuredOptionsInvoice,

      // keys similar to vehicle
      baseInvoice,
      baseRetail: baseMsrp,
      retailPrice: configuredTotalMsrp,
      msrp: configuredTotalMsrp,
      invoicePrice: configuredTotalInvoice,
      freightInPrice: destination,
      [AIR_TAX_PRICE]: chromeAirTaxPrice,
    },
    styleId,
    chromeSerializedValue,
    selectedStyleName,
    isFullyConfigured,
    isOptionsConflict,
    conflictResolvingChromeOptionCodes,
    chromeOptionsCount: options.length,
    mfrModelCode,
    trimDetails: {
      driveType,
      bodyType,
      bodyClass,
      trimName,
    },
    exteriorColor,
    ...(moduleType === MODULE.VI
      ? // NOTE: PM-6777 is done for VI only, once this is done from deals team
        // We have to remove this VI module specific check
        {
          exteriorColorCode,
          interiorColor,
          interiorColorCode,
        }
      : {}),
  };
};

export const mergeOptionsData = defaultMemoize(
  (chromeData = EMPTY_OBJECT, vehicleDetails = EMPTY_OBJECT, selectionStates = EMPTY_ARRAY) => {
    const { options: allOptions = EMPTY_ARRAY } = vehicleDetails;
    const { options: chromeOnlyOptions } = chromeData;

    const manuallyAddedOptions =
      allOptions?.filter(option => option[KEYS.SOURCE] !== OPTION_SOURCES.CHROME_CONSTRUCT) || EMPTY_ARRAY;
    const allMergedOptions = mergeOptions(chromeOnlyOptions, allOptions, selectionStates);

    const nonGroupedOptions =
      allOptions?.filter(
        option => !option[KEYS.HEADER_TITLE] || option[KEYS.HEADER_TITLE] === DEFAULT_OPTION_VALUES.HEADER_TITLE
      ) || EMPTY_ARRAY;
    const selectedOptionCodes = getOptionCodesBySelectionStates(allMergedOptions, CHROME_SELECTED_STATES);

    return {
      ...chromeData,

      options: allMergedOptions,
      selectedOptionCodes,
      nonChromeOptionsCount: manuallyAddedOptions.length,
      nonGroupedOptionsCount: nonGroupedOptions.length,
    };
  }
);

export const getMergedChromePrices = defaultMemoize((chromeData = EMPTY_OBJECT, vehicleDetails = EMPTY_OBJECT) => {
  const { options: allOptions = EMPTY_ARRAY, pricingDetails: vehiclePrices = EMPTY_OBJECT } = vehicleDetails;
  const { pricing: chromePrices = EMPTY_OBJECT } = chromeData;

  const manuallyAddedOptions =
    allOptions?.filter(
      option => option[KEYS.SOURCE] !== OPTION_SOURCES.CHROME_CONSTRUCT && option?.metadata?.manualAddOn
    ) || EMPTY_ARRAY;

  const { manualInvoice, manualRetail } = getOptionsPricingDetails(manuallyAddedOptions);
  return {
    pricing: {
      freightInPrice: chromePrices?.freightInPrice || vehiclePrices?.freightInPrice,
      baseInvoice: chromePrices?.baseInvoice || vehiclePrices?.baseInvoice,
      baseRetail: chromePrices?.baseRetail || vehiclePrices?.baseRetail,
      [AIR_TAX_PRICE]: chromePrices?.[AIR_TAX_PRICE] || vehiclePrices?.[AIR_TAX_PRICE],

      retailPrice: addPrices([chromePrices?.retailPrice, manualRetail]),
      invoicePrice: addPrices([chromePrices?.invoicePrice, manualInvoice]),
      msrp: addPrices([chromePrices?.msrp, manualRetail]),
      configuredOptionsMsrp: addPrices([chromePrices?.configuredOptionsMsrp, manualRetail]),
      configuredOptionsInvoice: addPrices([chromePrices?.configuredOptionsInvoice, manualInvoice]),
    },
  };
});

export const getUpdatedVehcielPrices = defaultMemoize(
  (vehicleDetails = EMPTY_OBJECT, invoiceChange, retailChange, configuredOptionsMsrpChange) => {
    const { pricingDetails: vehiclePrices = EMPTY_OBJECT } = vehicleDetails;

    return {
      pricing: {
        ...vehiclePrices,
        retailPrice: addPrices([vehiclePrices?.retailPrice, retailChange]),
        configuredOptionsMsrp: addPrices([vehiclePrices?.configuredOptionsMsrp, configuredOptionsMsrpChange]),
        configuredOptionsInvoice: addPrices([vehiclePrices?.configuredOptionsInvoice, invoiceChange]),
      },
    };
  }
);

export const getObjectsForvehicleUpdate = (type, data, salesSetupInfo, moduleType) => {
  const priceType = _get(salesSetupInfo, 'dealConfig.priceType');
  const isFormatterRequired = !DealerPropertyHelper.isViV2Enabled();
  if (type === VEHICLE_UPDATE_TYPES.STYLEID_SERIALVALUE_TO_VEHICLE) {
    return [
      { key: KEYS.CHROME_SERIALIZED_VALUE, value: data.chromeSerializedValue },
      { key: KEYS.CHROME_STYLE_ID, value: data.styleId },
    ];
  }
  if (type === VEHICLE_UPDATE_TYPES.CHROME_VEHICLE_TO_VEHICLE) {
    return [
      { key: MANUFACTURER_MODEL_CODE, value: data.mfrModelCode },
      { key: TRIM_DRIVE_TYPE, value: data.trimDetails.driveType },
      { key: TRIM_BODY_TYPE, value: data.trimDetails.bodyType },
      { key: TRIM_BODY_CLASS, value: data.trimDetails.bodyClass, options: MARKET_CLASS_CATEGORY_OPTIONS },
      { key: TRIM_NAME, value: data.trimDetails.trimName },
      { key: EXTERIOR_COLOR, value: data.exteriorColor },
      ...(moduleType === MODULE.VI
        ? // NOTE: PM-6777 is done for VI only, once this is done from deals team
          // We have to remove this VI module specific check
          [
            { key: EXTERIOR_COLOR_CODE, value: data.exteriorColorCode },
            { key: INTERIOR_COLOR, value: data.interiorColor },
            { key: INTERIOR_COLOR_CODE, value: data.interiorColorCode },
          ]
        : []),
    ];
  }
  if (type === VEHICLE_UPDATE_TYPES.PRICES_TO_VEHICLE) {
    return [
      // used in build vehicle
      { key: BASE_INVOICE, value: data.pricing.baseInvoice },
      { key: BASE_RETAIL, value: data.pricing.baseRetail },
      { key: RETAIL_PRICE, value: data.pricing.retailPrice },
      { key: INVOICE_PRICE, value: data.pricing.invoicePrice },
      { key: MSRP, value: data.pricing.msrp },
      {
        key: SELLING_PRICE,
        value: priceType === VEHICLE_PRICE_TYPE.MSRP ? data.pricing.msrp : data.pricing.retailPrice,
      },
      { key: MRM, value: null },
      { key: FREIGHT_IN_PRICE, value: data.pricing.freightInPrice },
      { key: AIR_TAX_PRICE, value: data.pricing[AIR_TAX_PRICE] },
      { key: OPTIONS_MSRP, value: data.pricing.configuredOptionsMsrp },
      {
        key: OPTIONS_INVOICE,
        value: data.pricing.configuredOptionsInvoice,
      },

      // used in VI and edit vehicel
      {
        key: `pricingDetails.${OPTIONS_MSRP}`,
        value: data.pricing.configuredOptionsMsrp,
        formatters: isFormatterRequired ? [formatPrice] : [],
      },
      {
        key: `pricingDetails.${OPTIONS_INVOICE}`,
        value: data.pricing.configuredOptionsInvoice,
        formatters: [formatPrice],
      },
      {
        key: `pricingDetails.${BASE_INVOICE}`,
        value: data.pricing.baseInvoice,
        formatters: isFormatterRequired ? [formatPrice] : [],
      },
      {
        key: `pricingDetails.${BASE_RETAIL}`,
        value: data.pricing.baseRetail,
        formatters: isFormatterRequired ? [formatPrice] : [],
      },
      {
        key: `pricingDetails.${RETAIL_PRICE}`,
        value: data.pricing.retailPrice,
        formatters: isFormatterRequired ? [formatPrice] : [],
      },
      {
        key: `pricingDetails.${INVOICE_PRICE}`,
        value: data.pricing.invoicePrice,
        formatters: isFormatterRequired ? [formatPrice] : [],
      },
      { key: `pricingDetails.${MSRP}`, value: data.pricing.msrp, formatters: isFormatterRequired ? [formatPrice] : [] },
      {
        key: `pricingDetails.${SELLING_PRICE}`,
        value: data.pricing.msrp,
        formatters: isFormatterRequired ? [formatPrice] : [],
      },
      { key: `pricingDetails.${MRM}`, value: null, formatters: isFormatterRequired ? [formatPrice] : [] },
      {
        key: `pricingDetails.${FREIGHT_IN_PRICE}`,
        value: data.pricing.freightInPrice,
        formatters: isFormatterRequired ? [formatPrice] : [],
      },
      {
        key: `pricingDetails.${AIR_TAX_PRICE}`,
        value: data.pricing[AIR_TAX_PRICE],
        formatters: isFormatterRequired ? [formatPrice] : [],
      },
      {
        key: `pricingDetails.${TRANSFER_BALANCE}`,
        value: getTransferBalancePrice(data?.pricing?.msrp),
        formatters: isFormatterRequired ? [formatPrice] : [],
      },
    ];
  }
  if (type === VEHICLE_UPDATE_TYPES.OPTIONS_TO_VEHICLE) {
    return [{ key: KEYS.OPTIONS, value: data.options }];
  }

  return [];
};

export const getProductClassificationOptions = defaultMemoize(productClassificationResponse =>
  _map(productClassificationResponse, item => ({
    label: item?.classificationName,
    value: item?.id,
  }))
);

export const getHitsFromResponse = defaultMemoize(response => tget(response, 'data.data', EMPTY_ARRAY));

export const isManualDealerAddOnOption = option =>
  option[KEYS.SOURCE] === OPTION_SOURCES.PRE_CONFIGURED && option?.metadata?.manualAddOn;

export const getPriceSummaryColumns = shouldHideInvoicePrice => [
  {
    Header: __('Description'),
    accessor: KEYS.DESCRIPTION,
    id: KEYS.DESCRIPTION,
    minWidth: 150,
    Cell: ({ value }) => value,
  },
  ...(!shouldHideInvoicePrice
    ? [
        {
          Header: __('Invoice'),
          accessor: KEYS.INVOICE_PRICE,
          id: KEYS.INVOICE_PRICE,
          minWidth: 100,
          Cell: ({ value }) => formatCurrencyWithMoneyFormat(value),
        },
      ]
    : []),
  {
    Header: __('Retail'),
    accessor: KEYS.RETAIL_PRICE,
    id: KEYS.RETAIL_PRICE,
    minWidth: 100,
    Cell: ({ value }) => formatCurrencyWithMoneyFormat(value),
  },
];

export const getOptionsBreakDownColumns = shouldHideInvoicePrice => [
  {
    Header: __('Name'),
    accessor: 'name',
    id: 'name',
    minWidth: 150,
    Cell: ({ value }) => value,
  },
  ...(!shouldHideInvoicePrice
    ? [
        {
          Header: __('Invoice'),
          accessor: 'invoice',
          id: 'invoice',
          minWidth: 100,
          Cell: ({ value }) => formatCurrencyWithMoneyFormat(value),
        },
      ]
    : []),
  {
    Header: __('Retail'),
    accessor: 'retail',
    id: 'retail',
    minWidth: 100,
    Cell: ({ value }) => formatCurrencyWithMoneyFormat(value),
  },
];
