import {
  getFormattedChromeData,
  mergeOptionsData,
  getUpdatedVehcielPrices,
  getPriceSummaryColumns,
  getOptionsBreakDownColumns,
} from '../VehicleOptionsBuilder.helper';
import { EMPTY_CHROME_DATA, KEYS } from '../VehicleOptionsBuilder.constants';
import {
  sampleChromeData,
  sampleVehicleDetails,
  chromeDataWithDealKeys,
} from '../__mocks__/VehicleOptionsBuilder.mock';

describe('Test VehicleOptionsBuilder helpers', () => {
  it('test getFormattedChromeData with empty inputs', () => {
    const { nonChromeOptionsCount, nonGroupedOptionsCount, ...rest } = EMPTY_CHROME_DATA;
    expect(getFormattedChromeData({}, [])).toMatchObject(rest);
  });

  it('test getFormattedChromeData - 1', () => {
    expect(getFormattedChromeData(sampleChromeData, [])).toMatchObject(chromeDataWithDealKeys);
  });

  it('test mergeOptionsData with empty inputs', () => {
    expect(mergeOptionsData(sampleChromeData, {}, [])).toMatchObject(sampleChromeData);
  });

  it('test getUpdatedVehcielPrices empty inputs', () => {
    expect(getUpdatedVehcielPrices(sampleVehicleDetails, 0, 0, 0).pricing).toEqual(sampleVehicleDetails.pricingDetails);
  });
});

describe('Test getPriceSummaryColumns', () => {
  it('should return columns with invoice price when shouldHideInvoicePrice is false', () => {
    const columns = getPriceSummaryColumns(false);

    expect(columns).toHaveLength(3);
    expect(columns[0].accessor).toBe(KEYS.DESCRIPTION);
    expect(columns[1].accessor).toBe(KEYS.INVOICE_PRICE);
    expect(columns[2].accessor).toBe(KEYS.RETAIL_PRICE);

    expect(columns[0].Cell({ value: 'Test Description' })).toBe('Test Description');
    expect(columns[1].Cell({ value: 100 })).toBe('$100.00');
    expect(columns[2].Cell({ value: 200 })).toBe('$200.00');
  });

  it('should return columns without invoice price when shouldHideInvoicePrice is true', () => {
    const columns = getPriceSummaryColumns(true);

    expect(columns).toHaveLength(2);
    expect(columns[0].accessor).toBe(KEYS.DESCRIPTION);
    expect(columns[1].accessor).toBe(KEYS.RETAIL_PRICE);

    expect(columns[0].Cell({ value: 'Test Description' })).toBe('Test Description');
    expect(columns[1].Cell({ value: 200 })).toBe('$200.00');
  });
});

describe('Test getOptionsBreakDownColumns', () => {
  it('should return columns with invoice price when shouldHideInvoicePrice is false', () => {
    const columns = getOptionsBreakDownColumns(false);

    expect(columns).toHaveLength(3);
    expect(columns[0].accessor).toBe('name');
    expect(columns[1].accessor).toBe('invoice');
    expect(columns[2].accessor).toBe('retail');

    expect(columns[0].Cell({ value: 'Test Option' })).toBe('Test Option');
    expect(columns[1].Cell({ value: 100 })).toBe('$100.00');
    expect(columns[2].Cell({ value: 200 })).toBe('$200.00');
  });

  it('should return columns without invoice price when shouldHideInvoicePrice is true', () => {
    const columns = getOptionsBreakDownColumns(true);

    expect(columns).toHaveLength(2);
    expect(columns[0].accessor).toBe('name');
    expect(columns[1].accessor).toBe('retail');

    expect(columns[0].Cell({ value: 'Test Option' })).toBe('Test Option');
    expect(columns[1].Cell({ value: 200 })).toBe('$200.00');
  });
});