import React, { useMemo, useState, useEffect, useCallback } from 'react';
import { compose } from 'recompose';
import PropTypes from 'prop-types';
import _map from 'lodash/map';
import _find from 'lodash/find';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import _difference from 'lodash/difference';
import _isEqual from 'lodash/isEqual';
import _noop from 'lodash/noop';
import _forEach from 'lodash/forEach';
import _size from 'lodash/size';
import _last from 'lodash/last';
import _groupBy from 'lodash/groupBy';
import _sortBy from 'lodash/sortBy';
import _filter from 'lodash/filter';

import classNames from 'classnames';
import { defaultMemoize } from 'reselect';
import { Divider } from 'antd';
import TableManager, { TABLE_TYPES } from '@tekion/tekion-components/src/organisms/TableManager';
import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { formatCurrencyWithMoneyFormat } from '@tekion/tekion-base/marketScan/utils';
import { PREFERENCE_ASSET_TYPE } from '@tekion/tekion-base/marketScan/constants/pages';
import Content from '@tekion/tekion-components/src/atoms/Content/Content';
import Button from '@tekion/tekion-components/src/atoms/Button';
import FontIcon from '@tekion/tekion-components/src/atoms/FontIcon';
import InfoWithTable from '@tekion/tekion-components/src/molecules/InfoWithTable';
import withUserPreferenceColumn from '@tekion/tekion-components/src/connectors/withUserPreferenceColumn';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import { TABLE_ACTIONS } from '@tekion/tekion-components/src/organisms/SortableTableWithPartition';
import DealerPropertyHelper from '@tekion/tekion-components/src/helpers/sales/dealerPropertyHelper';

import COLORS from '@tekion/tekion-styles-next/scss/exports.scss';
import {
  KEYS,
  OPTION_SOURCES,
  EMPTY_CHROME_DATA,
  VEHICLE_UPDATE_TYPES,
  CHROME_SELECTED_STATES,
  CUSTOM_TABLE_ACTIONS,
  HEADER_TITLE_TYPES,
  PRIMARY_OPTION_HEADERS,
  CUSTOMER_SENSITIVE_COLUMNS,
  CHROME_OPTIONS_SELECTION_STATE,
} from './VehicleOptionsBuilder.constants';
import {
  getOptionsById,
  getOptionsBySelectionStates,
  mergeOptionsData,
  getFormattedChromeData,
  getObjectsForvehicleUpdate,
  getMergedChromePrices,
  getUpdatedVehcielPrices,
  isManualDealerAddOnOption,
  getPriceSummaryColumns,
  getOptionsBreakDownColumns,
} from './VehicleOptionsBuilder.helper';
import AddVehicleOption from '../AddVehicleOption/AddVehicleOption';
import OptionsConflictModal from '../OptionsConflictModal/OptionsConflictModal';
import OptionGroupSelect from '../OptionGroupSelect/OptionGroupSelect';
import { getCellComponent } from './VehicleOptionsBuilder.formConfig';
import EditDeletePopover from '../../../../appServices/sales/molecules/editDeletePopover';
import { ADD_BULK_OPTIONS, ADD_OPTIONS, UPDATE_OPTIONS, DELETE_OPTIONS } from '../../Options/options.actionTypes';
import ChromeApi from '../ChromeConfigOptions.api';
import SalesDealerPropertyHelper from '../../../../appServices/sales/helpers/salesDealerPropertyHelper';
import CheckboxWithTooltip from '../../../../appServices/sales/molecules/CheckboxWihTooltip';

import styles from './VehicleOptionsBuilder.module.scss';

import { optionsPricesLogics } from '../ChromeConfigOptions.helper';

const TABLE_MANAGER_PROPS = {
  showSearch: false,
  showFilter: false,
  showSubHeader: false,
  rowHeight: 30,
  selectWidth: 40,
};

/** Component start */
const VehicleOptionsBuilder = ({
  columnConfigurator,
  isVehicleConfiguratorEnabled,
  vehicleDetails,
  onOptionosAndPartsAction,
  updateVehicleDetails,
  showSelectedOptions,
  containerClass,
  amountContainerClass,
  columns: tableColumns,
  priceUpdateForDIOEnabled,
  showManualRefresh,
  onManualRefresh,
  hidePreconfiguredOptions,
  removeDefaultOptions,
  dioCheckBoxProps,
  showCustomerSensitiveInfo,
  isViewOnly,
  isVehicleUsed,
  salesSetupInfo,
  showPriceSummary,
  moduleType,
  getTdProps,
  shouldHideInvoicePrice,
}) => {
  const { options, chromeSerializedValue, styleId, pricesManuallyUpdated } = vehicleDetails;

  const [loading, setLoading] = useState(false);
  const [showSelected, setShowSelected] = useState(showSelectedOptions);
  const [primaryOptionHeaders] = useState(PRIMARY_OPTION_HEADERS);

  const [chromeData, setChromeData] = useState(EMPTY_CHROME_DATA);
  const [allOptionsData, setAllOptionsData] = useState(EMPTY_CHROME_DATA);

  const [forceUpdatePrices, setForceUpdatePrices] = useState({ status: false });

  // Manually edit options & madal states
  const [addOrEditOptionModal, setAddOrEditOptionModal] = useState({ isOpen: false, option: {} });

  // conflict modal and state
  const [conflictModal, setConflictModal] = useState({ isOpen: false, options: [] });

  // create group modal
  const [createEditGroupModal, setCreateEditGroupModal] = useState({
    isOpen: false,
    selectedHeader: '',
    selectedOptions: [],
  });

  const updateOptionsConfiguration = useCallback(
    data => {
      const formattedChromeData = getFormattedChromeData(data, [], moduleType);

      // set if any conflict data
      setConflictModal({
        isOpen: !!formattedChromeData.conflictResolvingChromeOptionCodes.length,
        options: getOptionsById(formattedChromeData.options, formattedChromeData.conflictResolvingChromeOptionCodes),
      });

      // update in parent component to save under vehicleDetails
      updateVehicleDetails([
        ...getObjectsForvehicleUpdate(
          VEHICLE_UPDATE_TYPES.STYLEID_SERIALVALUE_TO_VEHICLE,
          formattedChromeData,
          salesSetupInfo,
          moduleType
        ),
        ...getObjectsForvehicleUpdate(
          VEHICLE_UPDATE_TYPES.CHROME_VEHICLE_TO_VEHICLE,
          formattedChromeData,
          salesSetupInfo,
          moduleType
        ),
      ]);

      setChromeData({ ...formattedChromeData });
    },
    [updateVehicleDetails]
  );

  const getOptionsBySeralizedValue = useCallback(async () => {
    setLoading(true);
    try {
      const rawResponse = await ChromeApi.fetchOptionBySerializedValue({
        serializedValue: chromeSerializedValue,
        isVehicleUsed,
      });
      const sanitizedResponse = removeDefaultOptions ? optionsPricesLogics(rawResponse) : rawResponse;
      const data = _get(sanitizedResponse, 'data', {});
      updateOptionsConfiguration(data);
    } catch (err) {
      setChromeData(EMPTY_CHROME_DATA);
    }
    setLoading(false);
  }, [chromeSerializedValue, updateOptionsConfiguration, removeDefaultOptions, isVehicleUsed]);

  const addOrRemoveChromeOption = async ({ optionId }) => {
    try {
      const rawResponse = await ChromeApi.addOrRemoveOption({
        serializedValue: chromeSerializedValue,
        optionId,
        isVehicleUsed,
      });
      const sanitizedResponse = removeDefaultOptions ? optionsPricesLogics(rawResponse) : rawResponse;

      const data = _get(sanitizedResponse, 'data', {});
      updateOptionsConfiguration(data);
    } catch (err) {
      const errText = __(_get(err, 'data.Message', ''));
      toaster(TOASTER_TYPE.ERROR, `${__('Unable to Edit Option')}: ${errText}`);
    }
  };

  // fetch options headers on component mount
  // useEffect(() => {
  //   async function fetchHeaderList() {
  //     const rawResponse = await ChromeApi.getPrimaryOptionHeaders();
  //     const headerList = tget(rawResponse, 'data.primaryOptionHeaders', EMPTY_ARRAY);
  //     setPrimaryOptionHeaders(getSelectOptions(headerList, false));
  //   }
  //   fetchHeaderList();
  // }, []);

  useEffect(() => {
    if (isVehicleConfiguratorEnabled && chromeSerializedValue && !conflictModal.options.length) {
      getOptionsBySeralizedValue();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [chromeSerializedValue, isVehicleConfiguratorEnabled, getOptionsBySeralizedValue, isVehicleUsed]);

  // clear chrome data on vehicle configurator turn off
  useEffect(() => {
    if (!isVehicleConfiguratorEnabled && !_isEqual(chromeData, EMPTY_CHROME_DATA)) {
      setChromeData(EMPTY_CHROME_DATA);
    } else if (!styleId && !chromeSerializedValue && !_isEqual(chromeData, EMPTY_CHROME_DATA)) {
      setChromeData(EMPTY_CHROME_DATA);
    }
  }, [isVehicleConfiguratorEnabled, styleId, chromeSerializedValue, chromeData]);

  useEffect(() => {
    const mergedOptionsData = mergeOptionsData(chromeData, vehicleDetails, showSelected ? CHROME_SELECTED_STATES : []);
    const mergedSelectedOptionsData = getOptionsBySelectionStates(mergedOptionsData?.options, CHROME_SELECTED_STATES);

    if (!_isEqual(vehicleDetails.options, mergedSelectedOptionsData)) {
      onOptionosAndPartsAction(ADD_BULK_OPTIONS, [{ item: mergedSelectedOptionsData }]);
    }
    setAllOptionsData({ ...mergedOptionsData });
  }, [vehicleDetails, chromeData, showSelected, onOptionosAndPartsAction, updateVehicleDetails]);

  // update prices on any table actions
  useEffect(() => {
    if (forceUpdatePrices.status) {
      const { type, invoiceChange, retailChange, configuredOptionsMsrpChange } = forceUpdatePrices;
      if (type === 'chromeUpdate') {
        const mergedPricesData = getMergedChromePrices(chromeData, vehicleDetails, CHROME_SELECTED_STATES);
        updateVehicleDetails([
          { key: 'pricesManuallyUpdated', value: false },
          ...getObjectsForvehicleUpdate(
            VEHICLE_UPDATE_TYPES.PRICES_TO_VEHICLE,
            mergedPricesData,
            salesSetupInfo,
            moduleType
          ),
        ]);
      }
      if (type === 'manualUpdate') {
        const mergedPricesData = getUpdatedVehcielPrices(
          vehicleDetails,
          invoiceChange,
          retailChange,
          configuredOptionsMsrpChange ?? retailChange
        );
        updateVehicleDetails([
          { key: 'pricesManuallyUpdated', value: false },
          ...getObjectsForvehicleUpdate(
            VEHICLE_UPDATE_TYPES.PRICES_TO_VEHICLE,
            mergedPricesData,
            salesSetupInfo,
            moduleType
          ),
        ]);
      }
      setForceUpdatePrices({ status: false });
    }
  }, [chromeData, vehicleDetails, updateVehicleDetails, forceUpdatePrices]);

  const isRowClickable = rowInfo => {
    const rowId = _get(rowInfo, 'original.id');
    // check if not header
    if (rowId !== undefined) {
      // check if not disabled
      if (!rowsToBeDisabled.includes(rowId)) {
        return true;
      }
    }

    return false;
  };

  const tableRowActions = async action => {
    const { type, payload } = action;

    if (type === TABLE_ACTIONS.TABLE_ITEM_CLICK) {
      if (!isRowClickable(payload?.value || {}) || loading) return;

      const { original } = payload?.value || {};
      const { id, [KEYS.SOURCE]: source, invoicePrice, retailPrice } = original || {};
      const isManualDealerAddOn = isManualDealerAddOnOption(original);

      if (source === OPTION_SOURCES.CHROME_CONSTRUCT) {
        setLoading(true);
        await addOrRemoveChromeOption({ optionId: id });
        setForceUpdatePrices({ type: 'chromeUpdate', status: true });
      } else if (original[KEYS.SOURCE] !== OPTION_SOURCES.PRE_CONFIGURED || isManualDealerAddOn) {
        onOptionosAndPartsAction(DELETE_OPTIONS, [{ item: original }]);
        setForceUpdatePrices({
          type: 'manualUpdate',
          invoiceChange: isManualDealerAddOn ? 0 : -1 * invoicePrice,
          retailChange: isManualDealerAddOn ? 0 : -1 * retailPrice,
          status: true,
        });
      }
    }
    if (type === TABLE_ACTIONS.TABLE_ITEM_SELECT) {
      if (loading) return;
      const { value } = payload;
      const added = _difference(value, allOptionsData.selectedOptionCodes);
      const removed = _difference(allOptionsData.selectedOptionCodes, value);
      const ids = [...added, ...removed];

      if (ids && ids.length && !_isEqual(allOptionsData.selectedOptionCodes, ids)) {
        const original = allOptionsData?.options?.find(option => option[KEYS.ID] === ids[0]);
        const { invoicePrice, retailPrice } = original || {};
        const isManualDealerAddOn = isManualDealerAddOnOption(original);

        if (original[KEYS.SOURCE] === OPTION_SOURCES.CHROME_CONSTRUCT) {
          setLoading(true);
          await addOrRemoveChromeOption({ optionId: ids[0] });
          setForceUpdatePrices({ type: 'chromeUpdate', status: true });
        } else if (original[KEYS.SOURCE] !== OPTION_SOURCES.PRE_CONFIGURED || isManualDealerAddOn) {
          onOptionosAndPartsAction(DELETE_OPTIONS, [{ item: original }]);
          setForceUpdatePrices({
            type: 'manualUpdate',
            invoiceChange: isManualDealerAddOn ? 0 : -1 * invoicePrice,
            retailChange: isManualDealerAddOn ? 0 : -1 * retailPrice,
            status: true,
          });
        }
      }
    }
    if (type === CUSTOM_TABLE_ACTIONS.TABLE_ITEM_DELETE) {
      const { original } = payload?.value || {};
      const { invoicePrice, retailPrice, dealerInstalledOption } = original || {};
      const isManualDealerAddOn = isManualDealerAddOnOption(original);
      const retailChange =
        isManualDealerAddOn || (dealerInstalledOption && !priceUpdateForDIOEnabled) ? 0 : retailPrice;
      onOptionosAndPartsAction(DELETE_OPTIONS, [{ item: original }]);
      setForceUpdatePrices({
        type: 'manualUpdate',
        invoiceChange: isManualDealerAddOn ? 0 : -1 * invoicePrice,
        retailChange: -1 * retailChange,
        configuredOptionsMsrpChange: isManualDealerAddOn ? 0 : -1 * retailPrice,
        status: true,
      });
    }
    if (type === CUSTOM_TABLE_ACTIONS.TABLE_ITEM_EDIT) {
      const { original } = payload?.value || {};
      const { invoicePrice, retailPrice, dealerInstalledOption } = original || {};
      const targetOptions = _find(options, { uuid: original?.uuid });
      if (!_isEmpty(targetOptions)) {
        const retailChange =
          dealerInstalledOption && !priceUpdateForDIOEnabled ? 0 : retailPrice - targetOptions.retailPrice;
        onOptionosAndPartsAction(UPDATE_OPTIONS, [{ item: original }]);
        setForceUpdatePrices({
          type: 'manualUpdate',
          invoiceChange: invoicePrice - targetOptions.invoicePrice,
          retailChange,
          configuredOptionsMsrpChange: retailPrice - targetOptions.retailPrice,
          status: true,
        });
      } else {
        const isManualDealerAddOn = isManualDealerAddOnOption(original);
        const retailChange =
          isManualDealerAddOn || (dealerInstalledOption && !priceUpdateForDIOEnabled) ? 0 : retailPrice;
        onOptionosAndPartsAction(ADD_OPTIONS, [{ item: original }]);
        setForceUpdatePrices({
          type: 'manualUpdate',
          invoiceChange: invoicePrice,
          retailChange,
          configuredOptionsMsrpChange: isManualDealerAddOn ? 0 : retailPrice,
          status: true,
        });
        toaster(TOASTER_TYPE.SUCCESS, __('Option added successfully'));
      }
    }
    if (type === CUSTOM_TABLE_ACTIONS.TABLE_ITEM_GROUP_CHANGE) {
      const { optionIds, groupId } = payload || {};
      optionIds.forEach(optionId => {
        const targetOption = allOptionsData?.options?.find(el => el[KEYS.OPTION_CODE] === optionId);
        if (targetOption) {
          onOptionosAndPartsAction(UPDATE_OPTIONS, [
            { item: { ...targetOption, [KEYS.HEADER_TITLE]: groupId, [KEYS.IS_GROUP_MANUALLY_CREATED]: true } },
          ]);
        }
      });
    }
  };

  const getColumns = defaultMemoize(columns => {
    const columnsWithRenderer = _map(columns, column => ({
      ...column,
      Cell: ({ original, value }) => getCellComponent(column.key, original, value),
    }));
    const visibleColumns =
      showCustomerSensitiveInfo && !shouldHideInvoicePrice
        ? columnsWithRenderer
        : _filter(columnsWithRenderer, ({ key }) => !CUSTOMER_SENSITIVE_COLUMNS.has(key));
    return [
      ...visibleColumns,
      {
        headerClassName: styles.columnConfiguratorHeader,
        ...columnConfigurator,
        // Disabling Kebab options for ARC lite users DMS-150041
        Cell:
          DealerPropertyHelper.isViArcLiteEnabled() || DealerPropertyHelper.isAECProgram()
            ? _noop()
            : ({ original }) => {
                const isChromeConstruct = original?.[KEYS.SOURCE] === OPTION_SOURCES.CHROME_CONSTRUCT;
                const isPreConfigured = original?.[KEYS.SOURCE] === OPTION_SOURCES.PRE_CONFIGURED;
                const isJustHeader = original?.justHeader;

                if (isManualDealerAddOnOption(original)) {
                  return (
                    <EditDeletePopover
                      data={[
                        {
                          name: __('Delete'),
                          onClick: () => {
                            tableRowActions({
                              type: CUSTOM_TABLE_ACTIONS.TABLE_ITEM_DELETE,
                              payload: { value: { original } },
                            });
                          },
                          menuItemClassName: 'justify-content-start',
                        },
                      ]}
                    />
                  );
                }

                if (!isChromeConstruct && !isPreConfigured && !isJustHeader) {
                  return (
                    <EditDeletePopover
                      data={[
                        {
                          name: __('Edit Option'),
                          onClick: () => {
                            setAddOrEditOptionModal({ isOpen: true, option: original });
                          },
                          menuItemClassName: 'justify-content-start',
                        },
                        {
                          name: __('Edit Group'),
                          onClick: () => {
                            setCreateEditGroupModal({
                              isOpen: true,
                              selectedHeader: original?.[KEYS.HEADER_TITLE],
                              selectedOptions: [],
                            });
                          },
                          menuItemClassName: 'justify-content-start',
                        },
                        {
                          name: __('Delete'),
                          onClick: () => {
                            tableRowActions({
                              type: CUSTOM_TABLE_ACTIONS.TABLE_ITEM_DELETE,
                              payload: { value: { original } },
                            });
                          },
                          menuItemClassName: 'justify-content-start',
                        },
                      ]}
                    />
                  );
                }
                return null;
              },
        width: 50,
      },
    ];
  });

  const resolveConflictOption = selectedOption => {
    tableRowActions({ type: TABLE_ACTIONS.TABLE_ITEM_SELECT, payload: { value: selectedOption } });
  };

  const changeOptionsGroupHandler = (optionIds = [], groupId = '') => {
    tableRowActions({ type: CUSTOM_TABLE_ACTIONS.TABLE_ITEM_GROUP_CHANGE, payload: { optionIds, groupId } });
  };
  const showAllOptions = () => setShowSelected(false);
  const showOptionAddModal = () => setAddOrEditOptionModal({ isOpen: true, option: {} });
  const hideOptionConflictModal = () => setConflictModal({ isOpen: false, options: [] });
  const hideAddOrEditOptionModal = () => setAddOrEditOptionModal({ isOpen: false, option: {} });
  const hideOptionGroupSelectModal = () =>
    setCreateEditGroupModal({ isOpen: false, selectedHeader: '', selectedOptions: [] });
  const addOrEditOptionHandler = original => {
    tableRowActions({ type: CUSTOM_TABLE_ACTIONS.TABLE_ITEM_EDIT, payload: { value: { original } } });
  };

  const { formatData, rowsToBeDisabled } = useMemo(() => {
    const unGroupedData = _groupBy(allOptionsData?.options, KEYS.HEADER_TITLE) || {};
    // sorting packages if present
    if (unGroupedData?.[HEADER_TITLE_TYPES.PACKAGE]?.length) {
      unGroupedData[HEADER_TITLE_TYPES.PACKAGE] = _sortBy(unGroupedData[HEADER_TITLE_TYPES.PACKAGE], [
        KEYS.PACKAGE_NAME,
      ]);
    }
    const groupedData = Object.values(unGroupedData).reduce((acc, val) => [...acc, ...val], []);

    // filter preconfigured options
    const displayData = [];
    _forEach(groupedData, item => {
      if (item?.source === OPTION_SOURCES.PRE_CONFIGURED && hidePreconfiguredOptions) {
        return;
      }
      const last = _last(displayData)?.[KEYS.HEADER_TITLE];
      if (item[KEYS.HEADER_TITLE] !== last) {
        displayData.push({ justHeader: true, [tableColumns?.[0]?.key]: item[KEYS.HEADER_TITLE] });
      }
      displayData.push({ ...item, avoidSelectedBgColor: true });
    });

    const newRowsToBeDisabled = displayData
      .filter(
        rowData =>
          rowData.selectionState === CHROME_OPTIONS_SELECTION_STATE.INCLUDED ||
          // need to check here
          (rowData.source === OPTION_SOURCES.PRE_CONFIGURED && !rowData?.metadata?.manualAddOn)
      )
      .map(rowData => rowData.id);

    return {
      formatData: displayData,
      rowsToBeDisabled: newRowsToBeDisabled,
    };
  }, [allOptionsData?.options, tableColumns, hidePreconfiguredOptions]);

  const getTableRowProps = (state, rowInfo) => {
    const justHeader = _get(rowInfo, 'original.justHeader') || false;
    if (justHeader) {
      return {
        style: {
          backgroundColor: COLORS.polarGreen,
          fontWeight: 'bold',
        },
      };
    }

    if ((_get(state, 'disabled') || []).includes(_get(rowInfo, 'original.id'))) {
      return {
        onClick: _noop,
        style:
          _get(rowInfo, 'original.source') === OPTION_SOURCES.PRE_CONFIGURED
            ? EMPTY_OBJECT
            : { backgroundColor: COLORS.glitter, color: COLORS.cometGray },
      };
    }

    return {};
  };

  const calculateInvoiceAndRetailOptions = useMemo(() => {
    const freightPrice = vehicleDetails?.pricingDetails?.freightInPrice;
    const airTax = _find(vehicleDetails.options, ({ chromeOptionCode }) => chromeOptionCode === '-EXTAX');
    const items = [];
    let totalInvoice = 0;
    let totalMsrp = 0;
    if (freightPrice && removeDefaultOptions) {
      items.push({
        name: __('Freight Price'),
        invoice: -freightPrice,
        retail: -freightPrice,
      });
      totalInvoice += -freightPrice;
      totalMsrp += -freightPrice;
    }

    if (airTax && removeDefaultOptions) {
      items.push({
        name: __('Air Tax'),
        invoice: -airTax?.invoicePrice,
        retail: -airTax?.retailPrice,
      });
      totalInvoice += -airTax?.invoicePrice || 0;
      totalMsrp += -airTax?.retailPrice || 0;
    }

    _forEach(vehicleDetails.options, option => {
      if (option?.invoicePrice || option?.retailPrice) {
        if (option?.chromeOptionCode !== '-EXTAX') {
          items.push({
            name: option?.description,
            invoice: option?.invoicePrice,
            retail: option?.retailPrice,
          });
          totalInvoice += option?.invoicePrice || 0;
          totalMsrp += option?.retailPrice || 0;
        }
      }
    });

    return { items, totalInvoiceOptions: totalInvoice, totalMsrpOptions: totalMsrp };
  }, [vehicleDetails, removeDefaultOptions]);

  const getTotalOptions = useMemo(() => {
    const { items } = calculateInvoiceAndRetailOptions;
    const columns = getOptionsBreakDownColumns(shouldHideInvoicePrice);
    return (
      <div className={classNames('d-flex flex-row')}>
        {__('Total Price')}
        {items?.length && removeDefaultOptions ? (
          <div className="m-l-8 m-t-4">
            <InfoWithTable tableData={items} columns={columns} placement="top" />
          </div>
        ) : null}
      </div>
    );
  }, [calculateInvoiceAndRetailOptions, removeDefaultOptions, shouldHideInvoicePrice]);

  const getAirTax = useMemo(() => {
    const airTax = _find(vehicleDetails.options, ({ chromeOptionCode }) => chromeOptionCode === '-EXTAX');
    return airTax;
  }, [vehicleDetails.options]);

  const isManualRefreshVisible = useMemo(
    () =>
      showManualRefresh &&
      (DealerPropertyHelper.isVehicleOptionsEnabled() || SalesDealerPropertyHelper.isVehicleProfileEnabled()),
    [showManualRefresh]
  );

  return (
    <div
      className={classNames(
        'd-flex flex-column justify-content-between align-items-center',
        styles.containerClass,
        containerClass
      )}>
      <div className="d-flex flex-row justify-content-between align-items-center m-t-12 m-b-12 full-width">
        <span>{allOptionsData.selectedStyleName}</span>
        {/* Disabling Below Actions for ARC lite users DMS-150041 */}
        <PropertyControlledComponent
          controllerProperty={!DealerPropertyHelper.isViArcLiteEnabled() && !DealerPropertyHelper.isAECProgram()}>
          <span className="d-flex flex-row justify-content-between align-items-center">
            <PropertyControlledComponent controllerProperty={allOptionsData.isFullyConfigured}>
              {__('Fully Configured')} &nbsp; <FontIcon className={styles.iconStyle}>icon-circle-check-active</FontIcon>
            </PropertyControlledComponent>
            <PropertyControlledComponent
              controllerProperty={!!(isVehicleConfiguratorEnabled && chromeData?.options?.length && showSelected)}>
              <Button view={Button.VIEW.TERTIARY} onClick={showAllOptions}>
                {__('Configure Options')}
              </Button>
            </PropertyControlledComponent>
            <PropertyControlledComponent controllerProperty={!!allOptionsData.nonGroupedOptionsCount && !isViewOnly}>
              <Button
                view={Button.VIEW.TERTIARY}
                onClick={() => setCreateEditGroupModal({ isOpen: true, selectedHeader: '', selectedOptions: [] })}>
                {__('Create Group')}
              </Button>
            </PropertyControlledComponent>
            <PropertyControlledComponent controllerProperty={isManualRefreshVisible}>
              <Button view={Button.VIEW.TERTIARY} onClick={onManualRefresh}>
                {__('Refresh')}
              </Button>
            </PropertyControlledComponent>
            <PropertyControlledComponent controllerProperty={!isViewOnly}>
              <Button view={Button.VIEW.TERTIARY} onClick={showOptionAddModal}>
                {__('Add Option')}
              </Button>
            </PropertyControlledComponent>
          </span>
        </PropertyControlledComponent>
      </div>
      <TableManager
        onAction={showSelected ? _noop : tableRowActions}
        tableManagerProps={TABLE_MANAGER_PROPS}
        tableProps={{
          selection: allOptionsData.selectedOptionCodes,
          showPagination: false,
          pageSize: _size(formatData),
          minRows: _size(formatData),
          showRowHoverColor: false,
          type: !showSelected ? TABLE_TYPES.WITH_CHECKBOX : TABLE_TYPES.FIXED_COLUMN,
          totalNumberOfEntries: _size(formatData),
          overrideDefaultExpanderClick: true,
          pivotIconClassName: styles.pivotIconClassName,
          noHeadSelector: true,
          getTrProps: getTableRowProps,
          disabled: rowsToBeDisabled,
          loading,
          SelectInputComponent: CheckboxWithTooltip,
          getTdProps,
        }}
        columns={getColumns(tableColumns)}
        data={formatData}
        noHeadSelector
      />
      <PropertyControlledComponent controllerProperty={showCustomerSensitiveInfo && showPriceSummary}>
        <div
          className={classNames(
            'd-flex flex-row justify-content-between align-items-center',
            styles.amountContainer,
            amountContainerClass
          )}>
          <div className={styles.amountBox}>
            <div className={styles.price}>
              {formatCurrencyWithMoneyFormat(vehicleDetails?.pricingDetails?.baseRetail)}
            </div>
            <div>{__('Base Retail')}</div>
          </div>
          <Divider type="vertical" className={classNames('m-l-16 m-r-16', styles.divider)} />
          <div className={styles.amountBox}>
            <div className={styles.price}>
              {formatCurrencyWithMoneyFormat(vehicleDetails?.pricingDetails?.configuredOptionsMsrp)}
            </div>
            <div>{__('Option Retail')}</div>
          </div>
          <Divider type="vertical" className={classNames('m-l-16 m-r-16', styles.divider)} />
          <div className={styles.amountBox}>
            <div className={classNames('d-flex flex-row justify-content-between align-items-center', styles.price)}>
              {formatCurrencyWithMoneyFormat(vehicleDetails?.pricingDetails?.retailPrice)} &nbsp; &nbsp;
              <InfoWithTable
                name={__('Price Summary')}
                manuallyEditedIndicator={pricesManuallyUpdated}
                tableData={[
                  {
                    [KEYS.DESCRIPTION]: __('Base Price'),
                    [KEYS.INVOICE_PRICE]: vehicleDetails?.pricingDetails?.baseInvoice,
                    [KEYS.RETAIL_PRICE]: vehicleDetails?.pricingDetails?.baseRetail,
                  },
                  {
                    [KEYS.DESCRIPTION]: __('Dest. Charge'),
                    [KEYS.INVOICE_PRICE]: vehicleDetails?.pricingDetails?.freightInPrice,
                    [KEYS.RETAIL_PRICE]: vehicleDetails?.pricingDetails?.freightInPrice,
                  },
                  {
                    [KEYS.DESCRIPTION]: __('Air Tax'),
                    [KEYS.INVOICE_PRICE]: getAirTax?.invoicePrice,
                    [KEYS.RETAIL_PRICE]: getAirTax?.retailPrice,
                  },
                  {
                    [KEYS.DESCRIPTION]: getTotalOptions,
                    [KEYS.INVOICE_PRICE]: calculateInvoiceAndRetailOptions?.totalInvoiceOptions,
                    [KEYS.RETAIL_PRICE]: calculateInvoiceAndRetailOptions?.totalMsrpOptions,
                  },
                  {
                    [KEYS.DESCRIPTION]: <Content className={styles.totalPrice}>{__('Total Price')}</Content>,
                    [KEYS.INVOICE_PRICE]: vehicleDetails?.pricingDetails?.invoicePrice,
                    [KEYS.RETAIL_PRICE]: vehicleDetails?.pricingDetails?.retailPrice,
                  },
                ]}
                columns={getPriceSummaryColumns(shouldHideInvoicePrice)}
                placement="top"
              />
            </div>
            <div>{__('Total Retail')}</div>
          </div>
        </div>
      </PropertyControlledComponent>

      <PropertyControlledComponent controllerProperty={addOrEditOptionModal.isOpen}>
        <AddVehicleOption
          option={addOrEditOptionModal.option}
          hideModal={hideAddOrEditOptionModal}
          primaryOptionHeaders={primaryOptionHeaders}
          addOption={addOrEditOptionHandler}
          selectedOptions={allOptionsData?.options}
          dioCheckBoxProps={dioCheckBoxProps}
          isViewOnly={isViewOnly}
          shouldHideInvoicePrice={shouldHideInvoicePrice}
        />
      </PropertyControlledComponent>
      <PropertyControlledComponent controllerProperty={conflictModal.isOpen}>
        <OptionsConflictModal
          options={conflictModal.options}
          hideModal={hideOptionConflictModal}
          selectOption={option => resolveConflictOption(option)}
        />
      </PropertyControlledComponent>
      <PropertyControlledComponent controllerProperty={createEditGroupModal.isOpen}>
        <OptionGroupSelect
          formattedData={allOptionsData?.options}
          hideModal={hideOptionGroupSelectModal}
          primaryOptionHeaders={primaryOptionHeaders}
          addOrEditGroup={changeOptionsGroupHandler}
          selectedHeader={createEditGroupModal.selectedHeader}
          selectedOptions={createEditGroupModal.selectedOptions}
        />
      </PropertyControlledComponent>
    </div>
  );
};

VehicleOptionsBuilder.propTypes = {
  columnConfigurator: PropTypes.object,
  isVehicleConfiguratorEnabled: PropTypes.bool,
  updateVehicleDetails: PropTypes.func,
  onOptionosAndPartsAction: PropTypes.func,
  vehicleDetails: PropTypes.shape({
    options: PropTypes.array,
    chromeSerializedValue: PropTypes.string,
    styleId: PropTypes.string,
    pricingDetails: PropTypes.shape({
      baseRetail: PropTypes.any,
      baseInvoice: PropTypes.any,
      retailPrice: PropTypes.any,
      invoicePrice: PropTypes.any,
      msrp: PropTypes.any,
      freightInPrice: PropTypes.any,
    }),
  }),
  showSelectedOptions: PropTypes.bool,
  containerClass: PropTypes.string,
  amountContainerClass: PropTypes.string,
  columns: PropTypes.array,
  priceUpdateForDIOEnabled: PropTypes.bool,
  showManualRefresh: PropTypes.bool,
  onManualRefresh: PropTypes.func,
  hidePreconfiguredOptions: PropTypes.bool,
  removeDefaultOptions: PropTypes.bool,
  dioCheckBoxProps: PropTypes.object,
  showCustomerSensitiveInfo: PropTypes.bool,
  showPriceSummary: PropTypes.bool,
  isViewOnly: PropTypes.bool,
  isVehicleUsed: PropTypes.bool,
  salesSetupInfo: PropTypes.object,
  moduleType: PropTypes.string,
  getTdProps: PropTypes.func,
  shouldHideInvoicePrice: PropTypes.bool,
};

VehicleOptionsBuilder.defaultProps = {
  columnConfigurator: EMPTY_OBJECT,
  isVehicleConfiguratorEnabled: false,
  updateVehicleDetails: _noop,
  onOptionosAndPartsAction: _noop,
  vehicleDetails: {
    options: EMPTY_ARRAY,
    chromeSerializedValue: EMPTY_STRING,
    styleId: EMPTY_STRING,
    pricingDetails: {
      baseRetail: undefined,
      baseInvoice: undefined,
      retailPrice: undefined,
      invoicePrice: undefined,
      msrp: undefined,
      freightInPrice: undefined,
    },
  },
  showSelectedOptions: false,
  containerClass: EMPTY_STRING,
  amountContainerClass: EMPTY_STRING,
  columns: EMPTY_ARRAY,
  priceUpdateForDIOEnabled: false,
  showManualRefresh: false,
  onManualRefresh: _noop,
  hidePreconfiguredOptions: false,
  removeDefaultOptions: false,
  dioCheckBoxProps: EMPTY_OBJECT,
  showCustomerSensitiveInfo: true,
  showPriceSummary: true,
  isViewOnly: false,
  isVehicleUsed: false,
  salesSetupInfo: EMPTY_OBJECT,
  moduleType: EMPTY_STRING,
  getTdProps: undefined,
  shouldHideInvoicePrice: false,
};

export default compose(
  withUserPreferenceColumn(
    PREFERENCE_ASSET_TYPE.CHROME_VEHICLE_BUILDER_OPTIONS,
    EMPTY_ARRAY,
    {},
    true,
    false,
    EMPTY_OBJECT,
    '',
    false,
    undefined,
    true,
    undefined,
    { overlayClassName: styles.columnConfigPopover }
  )
)(VehicleOptionsBuilder);
