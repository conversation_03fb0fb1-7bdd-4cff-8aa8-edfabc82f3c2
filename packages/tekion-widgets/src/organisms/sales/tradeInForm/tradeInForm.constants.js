import _toUpper from 'lodash/toUpper';

import { getCurrencySymbol } from '@tekion/tekion-base/formatters/formatCurrency';
import { getTimeStamp } from '@tekion/tekion-base/utils/dateUtils';

import { PAYMENT_TYPES } from '@tekion/tekion-base/marketScan/constants/desking.constants';
import { DIVISION_TYPES } from '@tekion/tekion-base/marketScan/constants/tradeInValuation.constants';

export const KEYS = {
  TRADE_ALLOWANCE: 'tradeAllowance',
  TRADE_PAY_OFF: 'tradePayOff',
  ACTUAL_CASH_VALUE: 'actualCashValue',
  RETAINED_EQUITY: 'retainedEquity',
  BOOK_VALUE_PRICE: 'bookValuePrice',
  TRADE_IN_VEHICLE: 'tradeInVehicle',
  GROUNDED_VEHICLE_LEASE: 'groundedVehicleLease',
  PRIOR_LEASE_TYPE: 'priorLeaseType',
  OVER_ESTIMATION: 'overEstimation',
  TRADE_IN_VALUE_WITH_TAX: 'tradeInValueWithTax',
  INCLUDING_VAT: 'tradeInVehicle.isVATQualifying',
  APPRAISAL: 'appraisal',
  SERVICE_VEHICLE_ID: 'tradeInVehicle.serviceVehicleId',
  VIN: 'tradeInVehicle.vin',
  YEAR: 'tradeInVehicle.year',
  MAKE: 'tradeInVehicle.make',
  TEKION_MAKE_ID: 'tradeInVehicle.makeId',
  ENGINE_SIZE: 'tradeInVehicle.trimDetails.engineSize',
  DRIVE_TYPE: 'tradeInVehicle.trimDetails.driveType',
  TEKION_DISPLAY_MAKE: 'tradeInVehicle.displayMake',
  MODEL: 'tradeInVehicle.model',
  SALES_DESTINATION: 'salesDestination',
  DISPLAY_MODEL: 'tradeInVehicle.displayModel',
  BRAND: 'tradeInVehicle.trimDetails.brand',
  RV_TYPE: 'tradeInVehicle.vehicleAdditionalDetails.rvType',
  BEST_STYLE_NAME: 'tradeInVehicle.bestStyleName',
  MODEL_DESCRIPTION: 'tradeInVehicle.modelDescription',
  VIN_RESOLVED: 'tradeInVehicle.vinLookupResolved',
  EXTERIOR_COLOR: 'tradeInVehicle.exteriorColor',
  TRIM: 'tradeInVehicle.trim',
  TRIM_DETAILS: 'tradeInVehicle.trimDetails',
  BODY_CLASS: 'tradeInVehicle.trimDetails.bodyClass',
  TRANSMISSION_CONTROL_TYPE: 'tradeInVehicle.trimDetails.transmissionControlType',
  BODY_TYPE: 'tradeInVehicle.bodyType',
  VEHICLE_SUBTYPE: 'tradeInVehicle.vehicleSubType',
  LOCATION_CODE: 'tradeInVehicle.locationCode',
  TITLE_NUMBER: 'tradeInVehicle.titleInfo.titleNumber',
  TITLE_STATE: 'tradeInVehicle.titleInfo.titleState',
  TITLE_TYPE: 'tradeInVehicle.titleInfo.titleType',
  TITLE_ISSUE_DATE: 'tradeInVehicle.titleInfo.titleIssueDate',
  INTERIOR_COLOR: 'tradeInVehicle.interiorColor',
  MILEAGE: 'tradeInVehicle.mileage',
  MILEAGE_TYPE: 'tradeInVehicle.mileageType',
  LICENSE_PLATE_NUMBER: 'tradeInVehicle.licensePlateNumber',
  LICENSE_PLATE_EXPIRATION_DATE: 'tradeInVehicle.licensePlateExpirationDate',
  TOTAL_RECONDITION_COST: 'totalReconditioningCost',
  CUSTOMER_TYPE: 'ownerType',
  GST_CCR_TAX: 'tradeInTaxSettings.isGstCcrTaxApplicable',
  PST_CCR_TAX: 'tradeInTaxSettings.isPstCcrTaxApplicable',
  GST_EXEMPTION: 'tradeInExemptionSettings.hasGstExemption',
  PST_EXEMPTION: 'tradeInExemptionSettings.hasPstExemption',
  TRADE_IN_TAX_SETTINGS: 'tradeInTaxSettings',
  TRADE_IN_EXEMPTION_SETTINGS: 'tradeInExemptionSettings',
  FIRST_NAME: 'customer.firstName',
  MIDDLE_NAME: 'customer.middleName',
  LAST_NAME: 'customer.lastName',
  SSN: 'customer.ssn',
  ADDRESS_1: 'customer.address.0.address1',
  ADDRESS_2: 'customer.address.0.address2',
  ADDRESS_3: 'customer.address.0.address3',
  ADDRESS_4: 'customer.address.0.address4',
  ADDRESS_TYPE: 'customer.address.0.addressType',
  CITY: 'customer.address.0.city',
  STATE: 'customer.address.0.state',
  ZIPCODE: 'customer.address.0.zipCode',
  COUNTY: 'customer.address.0.countyName',
  COUNTRY: 'customer.address.0.country',
  CO_BUYER: 'customer.coBuyerFullName',
  LIEN_HOLDER: 'lienHolderDetails.id',
  CONTRA_SETTLEMENTS_SUPPORTED_LIENHOLDER: 'lienHolderDetails.contraSettlementsSupported',
  LIEN_HOLDER_NAME: 'lienHolderDetails.lienHolder',
  DAILY_PER_DIEM: 'lienHolderDetails.dailyPerDiem',
  LIEN_AMOUNT: 'lienHolderDetails.lienAmount',
  LOAN_NUMBER: 'lienHolderDetails.loanNumber',
  EXPIRATION_DATE: 'lienHolderDetails.expirationDate',
  LIEN_HOLDER_ACCOUNT_NUMBER: 'lienHolderDetails.lienHolderAccountNumber',
  SERVICE_OR_SAFETY_INSPECTION: 'reconditionCosts.serviceORSafetyInspectionCost',
  DETAIL: 'reconditionCosts.detailCost',
  PAINT_AND_BODY: 'reconditionCosts.paintNBodyCost',
  TIRES: 'reconditionCosts.tiresCost',
  ADD_POPULAR_COLOR: 'reconditionCosts.addPopularColorCost',
  WIND_SHIELD: 'reconditionCosts.windShieldCost',
  FRESHEN_INTERIOR: 'reconditionCosts.freshenInteriorCost',
  MILES: 'reconditionCosts.milesCost',
  OWNERSHIP_TYPE: 'ownerShipType',
  STOCK_NUMBER: 'tradeInVehicle.stockID',
  IS_EXISTING_STOCK_ID: 'tradeInVehicle.isExistingStockId',
  IS_EXISTING_REG_NUMBER: 'tradeInVehicle.isDuplicateRegistrationNumber',
  CO_BUYER_FIRST_NAME: 'coBuyer.firstName',
  CO_BUYER_MIDDLE_NAME: 'coBuyer.middleName',
  CO_BUYER_LASTNAME: 'coBuyer.lastName',
  CO_BUYER_LICENSE: 'coBuyer.licenseDetails.licenseNumber',
  BUYER_LICENSE_NUMBER: 'customer.licenseDetails.licenseNumber',
  PHONE_NUMBER: 'customer.mobileNo',
  MANDATORY_FIELDS_SECTION: 'lienHolderDetails.mandatoryFieldsSection',
  LIENHOLDER_ADDRESS: 'lienHolderDetails.lienHolderAddress.address1',
  LIENHOLDER_ADDRESS2: 'lienHolderDetails.lienHolderAddress.address2',
  LIENHOLDER_ADDRESS3: 'lienHolderDetails.lienHolderAddress.address3',
  LIENHOLDER_ADDRES4: 'lienHolderDetails.lienHolderAddress.address4',
  LIENHOLDER_PHONE: 'lienHolderDetails.lienHolderPhone',
  LIENHOLDER_CITY: 'lienHolderDetails.lienHolderAddress.city',
  LIENHOLDER_STATE: 'lienHolderDetails.lienHolderAddress.state',
  LIENHOLDER_ZIPCODE: 'lienHolderDetails.lienHolderAddress.zipCode',
  LIENHOLDER_COUNTRY: 'lienHolderDetails.lienHolderAddress.country',
  LICENSE_ISSUED_STATE: 'licenseIssuedState',
  DIVISION: 'division',
  BODY_STYLE: 'valuation.extVehicleId',
  VEHICLE_OPTIONS: 'valuation.option',
  VEHICLE_CONDITION: 'valuation.condition',
  PRICE_LIST: 'valuation.priceList',
  VALUATION: 'valuation', // deprecated
  VALUATIONS: 'valuations',
  VALUATION_MODEL: 'valuation.model',
  AUTOPOPULATED_FIELDS: 'autoPopulatedFields',
  IS_IN_VALID_VIN: 'tradeInVehicle.isInValidVin',
  CUSTOM_FIELDS: 'tradeInVehicle.customFields',
  INSERVICE_DATE: 'tradeInVehicle.inServiceDate',
  TAKE_PRICE: 'tradeInVehicle.pricingDetails.takePrice',
  LIST_PRICE: 'tradeInVehicle.pricingDetails.msrp',
  SPECIAL_WEB_PRICE: 'tradeInVehicle.pricingDetails.internetPrice',
  DEALER_APPROVAL_STATUS: 'dealerApprovalStatus',
  EMAIL: 'customer.email',
  INVOICE_DATE: 'tradeInVehicle.invoiceDate',
  DATE_OF_BIRTH: 'customer.dob',
  PRIME_CONVERSION: 'vehiclePrimeConversion',
  FUEL_TYPE: 'tradeInVehicle.trimDetails.fuelType',
  VEHICLE_KIND: 'tradeInVehicle.genre',
  FIRST_REGISTRATION_DATE: 'tradeInVehicle.firstRegistrationDate',
  FOREIGN_REGISTRATION: 'tradeInVehicle.foreignRegistration',
  ORIGIN: 'tradeInVehicle.sourceInfo.source',
  PREVIOUS_USAGE: 'tradeInVehicle.previousUsage',
  FIRST_OWNER: 'tradeInVehicle.isFirstHandOwner',
  HORSE_POWER: 'tradeInVehicle.trimDetails.horsePower',
  VERSION: 'tradeInVehicle.trimDetails.trim',
  VEHICLE_CATEGORY: 'tradeInVehicle.vehicleCategory',
  TRADE_PAY_OFF_QUOTE: 'tradePayoffQuote',
  NET_TRADE_IN_VALUE: 'netTradeInValue',
  LIENHOLDER_AGREEMENT_TYPE: 'lienHolderDetails.lienAgreementType',
  LIENHOLDER_AGREEMENT_NUMBER: 'lienHolderDetails.lienAgreementNumber',
  NUMBER_OF_PREVIOUS_OWNERS: 'numberOfPreviousOwners',
  PURCHASE_SOURCE: 'tradeInVehicle.purchaseSource',
  DOORS: 'tradeInVehicle.trimDetails.bodyDoorCount',
  INTERIOR_UPHOLSTERY: 'tradeInVehicle.trimDetails.seatMaterial',
  PRODUCT_TAX_CLASSIFICATION: 'tradeInVehicle.pricingDetails.productTaxClassification',
  VAT_VALUE: 'tradeInVehicle.pricingDetails.vatAmount',
  THIRD_PARTY_PROVIDERS: 'tradeInVehicle.thirdPartyProviders',
  STYLE_ID: 'tradeInVehicle.styleId',
  CAP_ID: 'tradeInVehicle.thirdPartyProviders.0.id',
  LIENHOLDER_ADDRESS_DETAILS: 'LIENHOLDER_ADDRESS_DETAILS',
  MEDIA_DETAILS: 'tradeInMediaDetails',
  DAMAGE_DETAILS: 'damageMediaDetails',
  VEHICLE_PREFIX: 'tradeInVehicle.vehiclePrefix',
};

export const MANDATORY_FIELDS_FOR_LIEN_SECTION = [
  KEYS.LIENHOLDER_AGREEMENT_NUMBER,
  KEYS.LIEN_HOLDER,
  KEYS.LIENHOLDER_AGREEMENT_TYPE,
  KEYS.EXPIRATION_DATE,
];
export const DEALER_APPROVAL_STATUS_ENUM = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
};

export const MULTI_LINGUAL_KEY_PATHS = {
  EXTERIOR_COLOR: ['tradeInVehicle', 'exteriorColor'],
  INTERIOR_COLOR: ['tradeInVehicle', 'interiorColor'],
  TRANSMISSION_CONTROL_TYPE: ['tradeInVehicle', 'transmissionControlType'],
};

export const VALUATION_KEYS = {
  BODY_STYLE: 'extVehicleId',
  VEHICLE_OPTIONS: 'option',
  VEHICLE_CONDITION: 'condition',
  PRICE_LIST: 'priceList',
  VALUATION_MODEL: 'model',
  GRADE: 'grade',
  REGION: 'region',
  COLOR: 'color',
  DATE: 'date',
  TRIM: 'trim',
  KBB_ICO: 'KBB_ICO',
  VEHICLE_DETAILS: 'vehicleDetails',
  LICENSE_PLATE_NUMBER: 'licensePlateNumber',
  MILEAGE: 'mileage',
  ZIP_CODE: 'zipCode',
  VEHICLE_OPTIONS_BUTTON: 'vehicleOptionsButton',
  VEHICLE_OPTIONS_BB_ICO: 'vehicleOptions',
  VEHICLE_CONDITIONS: 'vehicleConditions',
  VEHICLE_CONDITION_BUTTON: 'vehicleConditionButton',
  VIN: 'vin',
  CONTACT_DETAILS: 'contactDetails',
  FIRST_NAME: 'firstName',
  LAST_NAME: 'lastName',
  PHONE_NUMBER: 'phoneNo',
  EMAIL_ADDRESS: 'emailAddress',
};

export const FIELDS_TO_BE_NOT_SHOWN = [KEYS.ACTUAL_CASH_VALUE];

export const VIN_LOOKUP_KEYS = [
  KEYS.YEAR,
  KEYS.MAKE,
  KEYS.TEKION_MAKE_ID,
  KEYS.MODEL,
  KEYS.DISPLAY_MODEL,
  KEYS.BEST_STYLE_NAME,
  KEYS.TRIM,
  KEYS.TRIM_DETAILS,
  KEYS.VIN_RESOLVED,
  KEYS.EXTERIOR_COLOR,
  KEYS.INTERIOR_COLOR,
  KEYS.TEKION_DISPLAY_MAKE,
];
export const DEPENDANT_CUSTOMER_KEYS = [
  KEYS.FIRST_NAME,
  KEYS.MIDDLE_NAME,
  KEYS.LAST_NAME,
  KEYS.ADDRESS_1,
  KEYS.CITY,
  KEYS.STATE,
  KEYS.COUNTY,
  KEYS.ZIPCODE,
  KEYS.CO_BUYER_FIRST_NAME,
  KEYS.CO_BUYER_MIDDLE_NAME,
  KEYS.CO_BUYER_LASTNAME,
  KEYS.CO_BUYER_LICENSE,
  KEYS.BUYER_LICENSE_NUMBER,
];

export const LIEN_HOLDER_ADDRESS_KEYS = {
  [KEYS.LIENHOLDER_ADDRESS]: 'line1',
  [KEYS.LIENHOLDER_CITY]: 'city',
  [KEYS.LIENHOLDER_STATE]: 'state',
  [KEYS.LIENHOLDER_ZIPCODE]: 'zipCode',
  [KEYS.LIENHOLDER_ADDRES4]: 'address4',
  [KEYS.LIENHOLDER_ADDRESS3]: 'address3',
  [KEYS.LIENHOLDER_ADDRESS2]: 'address2',
  [KEYS.LIENHOLDER_COUNTRY]: 'country',
};

export const LIEN_HOLDER_OPTIONS_ADDRESS_KEYS = {
  [KEYS.LIENHOLDER_ADDRESS]: 'address1',
  [KEYS.LIENHOLDER_CITY]: 'city',
  [KEYS.LIENHOLDER_STATE]: 'state',
  [KEYS.LIENHOLDER_ZIPCODE]: 'zipCode',
  [KEYS.LIENHOLDER_ADDRES4]: 'address4',
  [KEYS.LIENHOLDER_ADDRESS3]: 'address3',
  [KEYS.LIENHOLDER_ADDRESS2]: 'address2',
  [KEYS.LIENHOLDER_COUNTRY]: 'country',
};

export const LIEN_PAY_OFF_HOLDER_OPTIONS_ADDRESS_KEYS = {
  [KEYS.LIENHOLDER_ADDRESS]: 'address',
  [KEYS.LIENHOLDER_CITY]: 'city',
  [KEYS.LIENHOLDER_STATE]: 'state',
  [KEYS.LIENHOLDER_ZIPCODE]: 'zipCode',
  [KEYS.LIENHOLDER_ADDRES4]: 'address4',
  [KEYS.LIENHOLDER_ADDRESS3]: 'address3',
  [KEYS.LIENHOLDER_ADDRESS2]: 'address2',
  [KEYS.LIENHOLDER_COUNTRY]: 'country',
};

export const ADDRESS_KEYS = {
  [KEYS.ADDRESS_1]: 'line1',
  [KEYS.ADDRESS_2]: 'line2',
  [KEYS.ADDRESS_3]: 'line3',
  [KEYS.ADDRESS_4]: 'line4',
  [KEYS.CITY]: 'city',
  [KEYS.STATE]: 'state',
  [KEYS.COUNTY]: 'countyName',
  [KEYS.COUNTRY]: 'country',
  [KEYS.ZIPCODE]: 'zipCode',
  [KEYS.ADDRESS_TYPE]: 'addressType',
};

export const ADDRESS_KEYS_VS_LOCATION_KEYS = {
  [KEYS.ADDRESS_1]: 'line1',
  [KEYS.ADDRESS_2]: 'line2',
  [KEYS.ADDRESS_3]: 'line3',
  [KEYS.ADDRESS_4]: 'line4',
  [KEYS.CITY]: 'city',
  [KEYS.STATE]: 'state',
  [KEYS.COUNTY]: 'county',
  [KEYS.COUNTRY]: 'country',
  [KEYS.ZIPCODE]: 'zipCode',
  [KEYS.ADDRESS_TYPE]: 'addressType',
};

export const TRIM_MODAL_KEYS = [KEYS.TRIM, KEYS.TRIM_DETAILS];
export const VIN_FIELD_MAX_LENGTH = 17;
export const CURRENCY_SYMBOL = getCurrencySymbol();

export const OWNERSHIP_TYPE = [
  {
    label: __('Loaned/Owned'),
    value: PAYMENT_TYPES.LOAN,
  },
  {
    label: __('Leased'),
    value: PAYMENT_TYPES.LEASE,
  },
];

export const OWNERSHIP_TYPE_WITH_DIVISION_OPTIONS = [
  {
    label: __('Loaned/Owned with Division 1'),
    value: `${PAYMENT_TYPES.LOAN}_${DIVISION_TYPES.CAPPED_TAX_CREDIT}`,
  },
  {
    label: __('Loaned/Owned with Division 2'),
    value: `${PAYMENT_TYPES.LOAN}_${DIVISION_TYPES.FULL_TAX_CREDIT}`,
  },
  {
    label: __('Leased'),
    value: PAYMENT_TYPES.LEASE,
  },
];

export const OWNER_TYPES = {
  BUYER: 'BUYER',
  CO_BUYER: 'CO_BUYER',
  OTHER: 'OTHER',
  BOTH: 'BOTH',
};

export const FIRST_OWNER_OPTIONS = [
  { label: __('Yes'), value: true },
  { label: __('No'), value: false },
];

export const OWNER_OPTIONS = [
  {
    label: __('Both'),
    value: OWNER_TYPES.BOTH,
  },
  {
    label: __('Buyer'),
    value: OWNER_TYPES.BUYER,
  },
  {
    label: __('Co Buyer'),
    value: OWNER_TYPES.CO_BUYER,
  },
  {
    label: __('Other'),
    value: OWNER_TYPES.OTHER,
  },
];

export const PRIOR_LEASE_TYPE = {
  CREDIT: 'CREDIT',
  BALANCE: 'BALANCE',
};

export const PRIOR_LEASE_SELECT_OPTIONS = [
  {
    label: __('Balance'),
    value: PRIOR_LEASE_TYPE.BALANCE,
  },
  {
    label: __('Credit'),
    value: PRIOR_LEASE_TYPE.CREDIT,
  },
];

export const OWNER_OPTIONS_WITHOUT_COBUYER = [
  {
    label: __('Buyer'),
    value: OWNER_TYPES.BUYER,
  },
  {
    label: __('Other'),
    value: OWNER_TYPES.OTHER,
  },
];

export const KEYS_THAT_REQUIRE_MS_CALL = [
  KEYS.TRADE_ALLOWANCE,
  KEYS.ACTUAL_CASH_VALUE,
  KEYS.TRADE_PAY_OFF,
  KEYS.RETAINED_EQUITY,
  KEYS.OWNERSHIP_TYPE,
  KEYS.BOOK_VALUE_PRICE,
];

export const FORM_FIELD_FORMATTERS = {
  [KEYS.LICENSE_ISSUED_STATE]: _toUpper,
  [KEYS.STOCK_NUMBER]: _toUpper,
  [KEYS.VIN]: _toUpper,
  [KEYS.LICENSE_PLATE_EXPIRATION_DATE]: value => (!value ? null : getTimeStamp(value)),
  [KEYS.EXPIRATION_DATE]: value => (!value ? null : getTimeStamp(value)),
};

export const VEHICLE_VALUATION_TRIM_KEYS = {
  EXT_VEHICLE_ID: 'extVehicleId',
  YEAR: 'year',
  MAKE: 'make',
  MODEL: 'model',
  TRIM: 'trim',
  BODY_STYLE: 'bodyStyle',
  ENGINE: 'engine',
  MSRP: 'msrp',
  TRANSMISSION: 'transmission',
  DRIVE: 'drive',
  MILEAGE: 'mileage',
  OPTION: 'option',
  CONDITION: 'condition',
  INSERVICE_DATE: 'inServiceDate',
};

export const APPRAISAL_KEYS = {
  TRADE_ALLOWANCE: 'value',
  TRADE_PAY_OFF: 'tradePayOff',
  INTERIOR_COLOR: 'interiorColor',
  EXTERIOR_COLOR: 'exteriorColor',
  LICENSE_PLATE_EXPIRATION_DATE: 'licensePlateExpirationDate',
  LICENSE_PLATE_NUMBER: 'licensePlateNumber',
  TRANSMISSION_CONTROL_TYPE: 'transmissionControlType',
  MILEAGE: 'mileage',
  LIEN_HOLDER_NAME: 'lienHolderName',
  TITLE_NUMBER: 'titleNumber',
  TITLE_STATE: 'titleState',
  TITLE_TYPE: 'titleType',
  APPRAISED_TIME: 'appraisalTime',
  APPRAISER_FIRST_NAME: 'appraiser.firstName',
  APPRAISER_LAST_NAME: 'appraiser.lastName',
  LINE_HOLDER_PHONE_NUMBER: 'lienHolderPhone',
  LIEN_HOLDER_ADDRESS: 'lienHolderAddress',
};

export const VEHICLE_DETAILS_VALUES = {
  PLATE: 'PLATE',
  VIN: 'VIN',
};

export const VEHICLE_DETAILS_OPTIONS = [
  {
    label: __('VIN'),
    value: VEHICLE_DETAILS_VALUES.VIN,
  },
  {
    label: __('Plate'),
    value: VEHICLE_DETAILS_VALUES.PLATE,
  },
];

export const AGREEMENT_TYPE_OPTIONS = [
  { value: '1', label: __('Hire Purchase') },
  { value: '2', label: __('Conditional Sale') },
  { value: '3', label: __('Credit Sale') },
  { value: '4', label: __('Lease') },
  { value: '5', label: __('Personal Loan') },
  { value: '6', label: __('Miscellaneous') },
  { value: '7', label: __('Contract Hire') },
  { value: '8', label: __('Bill of Sale') },
  { value: '9', label: __('Personal Contract Plan') },
  { value: 'G', label: __('Unit Stocking') },
  { value: 'H', label: __('Demonstration Stocking.') },
  { value: 'A', label: __('Security Registrations') },
];

export const VAT_QUALIFYING_OPTIONS = [
  {
    value: true,
    label: __('Yes'),
  },
  {
    value: false,
    label: __('No'),
  },
];

export const VAT_QUALIFYING_OPTIONS_FOR_MANDATORY_FORM = [
  {
    value: 'true',
    label: __('Yes'),
  },
  {
    value: 'false',
    label: __('No'),
  },
];

export const TRADEIN_TABS_KEYS = {
  BASIC_DETAILS: 'basicDetails',
  OWNERSHIP: 'ownership',
  LIEN_HOLDER: 'lienHolder',
  CUSTOM_FIELDS: 'customFields',
  MEDIA_DETAILS: 'tradeInMediaDetails',
  DAMAGE_DETAILS: 'damageMediaDetails',
  VALUATION: 'valuations',
  TRADEIN_SETTINGS: 'tradeInSettings',
};

export const DEFAULT_SECTIONS = [
  TRADEIN_TABS_KEYS.BASIC_DETAILS,
  TRADEIN_TABS_KEYS.OWNERSHIP,
  TRADEIN_TABS_KEYS.LIEN_HOLDER,
  TRADEIN_TABS_KEYS.CUSTOM_FIELDS,
  TRADEIN_TABS_KEYS.MEDIA_DETAILS,
  TRADEIN_TABS_KEYS.DAMAGE_DETAILS,
  TRADEIN_TABS_KEYS.VALUATION,
];

export const NO_TRADE_PAY_OFF_MESSAGE = __('Enter Finance Settlement to enable');

export const TRANSMISSION_CONTROL_TYPE_OPTIONS = [
  {
    label: __('Automatic'),
    value: 'Automatic',
  },
  {
    label: __('Manual'),
    value: 'Manual',
  },
];
