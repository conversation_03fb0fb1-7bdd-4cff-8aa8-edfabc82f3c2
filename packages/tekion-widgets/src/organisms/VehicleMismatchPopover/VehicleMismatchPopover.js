import React, { useMemo, useCallback } from 'react';
import PropTypes from 'prop-types';
import _noop from 'lodash/noop';
import _size from 'lodash/size';
import _partition from 'lodash/partition';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { tget } from '@tekion/tekion-base/utils/general';
import openWindowInNewTab from '@tekion/tekion-base/utils/openWindowInNewTab';
import IconWithPopover from '@tekion/tekion-components/src/molecules/IconWithPopover/IconWithPopover';
import Button from '@tekion/tekion-components/src/atoms/Button';
import { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon/Icon.constants';
import Heading from '@tekion/tekion-components/src/atoms/Heading';
import Content from '@tekion/tekion-components/src/atoms/Content';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import DealerPropertyHelper from '@tekion/tekion-components/src/helpers/sales/dealerPropertyHelper';
import {
  getSpecificAlertsForVehicle,
  hasOptionsMismatch,
  getAllVehicleAlerts,
  hasVehicleAlert,
  getOEMAlertList,
} from '@tekion/tekion-business/src/appServices/vehicleInventory/helpers/vehicleAlerts';
import { ALERTS } from '@tekion/tekion-business/src/appServices/vehicleInventory/constants/vehicleAlerts';

import DivWithoutPropagation from '../../atoms/DivWithoutPropagation';
import { isAECOnlyProgram } from '../../utils/vi/program.utils';
import { isAecAndArcLiteProgram as isArcPlusAecProgram } from '../../utils/program.utils';

import VehicleMismatchDescription from './VehicleMismatchDescription';
import AlertList from './components/AlertList';

import styles from './vehicleMismatchPopover.module.scss';

const VehicleMismatchPopover = ({ vehicleAlerts, vehicleId, size, redirectToVehicleDetails, vehicle }) => {
  const alertOptions = useMemo(
    () =>
      DealerPropertyHelper.isViV2Enabled()
        ? getAllVehicleAlerts(vehicleId, vehicleAlerts)
        : getSpecificAlertsForVehicle(vehicleId, vehicleAlerts, [ALERTS.OPTION_MISMATCH, ALERTS.OEM_ALERT]),
    [vehicleId, vehicleAlerts]
  );

  const isVehicleAlertPresent = useMemo(
    () =>
      DealerPropertyHelper.isViV2Enabled()
        ? hasVehicleAlert(vehicleId, vehicleAlerts)
        : hasOptionsMismatch(vehicleId, vehicleAlerts, ALERTS.OPTION_MISMATCH),
    [vehicleId, vehicleAlerts]
  );

  const [oemAlerts, otherAlerts] = useMemo(
    () => _partition(alertOptions, alert => alert.key === ALERTS.OEM_ALERT),
    [alertOptions]
  );

  const otherAlertsCount = useMemo(() => _size(otherAlerts), [otherAlerts]);

  const oemAlertList = useMemo(() => getOEMAlertList(oemAlerts), [oemAlerts]);

  const oemAlertListCount = useMemo(() => _size(oemAlertList), [oemAlertList]);

  const handleRedirectToVIDetails = useCallback(
    e => {
      e.stopPropagation();
      redirectToVehicleDetails(vehicleId, vehicle);
    },
    [vehicleId, vehicle]
  );

  const {
    text: linkText,
    url: linkUrl,
    description: linkDescription,
  } = useMemo(() => tget(oemAlerts, '0.additionDetails.hyperLinkDetails', EMPTY_OBJECT), [oemAlerts]);

  const handleRedirect = useCallback(
    e => {
      e.stopPropagation();
      openWindowInNewTab(linkUrl);
    },
    [linkUrl]
  );

  return (
    <PropertyControlledComponent controllerProperty={isVehicleAlertPresent}>
      <IconWithPopover
        iconClass={`inline m-l-4 align-self-center ${styles.warningIcon}`}
        icon="icon-caution-empty"
        size={size}
        content={
          <DivWithoutPropagation className={styles.popOverLayout}>
            <PropertyControlledComponent controllerProperty={otherAlertsCount > 0}>
              <div className={styles.renderButtonsContainer}>
                <Heading size={5} className={styles.heading}>
                  {otherAlertsCount} {otherAlertsCount > 1 ? __('Alerts') : __('Alert')}
                </Heading>
                <PropertyControlledComponent controllerProperty={!isAECOnlyProgram}>
                  <Button view={Button.VIEW.TERTIARY} onClick={handleRedirectToVIDetails}>
                    {__('View Details')}
                  </Button>
                </PropertyControlledComponent>
              </div>
              <VehicleMismatchDescription issues={otherAlerts} />
            </PropertyControlledComponent>
            <PropertyControlledComponent controllerProperty={isArcPlusAecProgram() && oemAlertListCount > 0}>
              <AlertList heading={__('Vehicle OEM Alerts')} alerts={oemAlertList} />
              <PropertyControlledComponent controllerProperty={linkUrl}>
                <Content inline className="m-t-12 m-r-4">
                  {linkDescription}:
                </Content>
                <Button view={Button.VIEW.TERTIARY} onClick={handleRedirect} highlightOnHover={false}>
                  {linkText}
                </Button>
              </PropertyControlledComponent>
            </PropertyControlledComponent>
          </DivWithoutPropagation>
        }
      />
    </PropertyControlledComponent>
  );
};

VehicleMismatchPopover.propTypes = {
  vehicleAlerts: PropTypes.object.isRequired,
  vehicleId: PropTypes.string.isRequired,
  size: PropTypes.string,
  redirectToVehicleDetails: PropTypes.func,
  vehicle: PropTypes.object,
};

VehicleMismatchPopover.defaultProps = {
  size: SIZES.S,
  redirectToVehicleDetails: _noop,
  vehicle: EMPTY_OBJECT,
};

export default React.memo(VehicleMismatchPopover);
