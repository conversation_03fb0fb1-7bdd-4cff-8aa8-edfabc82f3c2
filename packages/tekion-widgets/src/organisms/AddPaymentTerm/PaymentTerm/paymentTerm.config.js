// reselect
import { defaultMemoize } from 'reselect';
import cx from 'classnames';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { isRRG } from '@tekion/tekion-base/utils/dealerProgramUtils';
import { tget } from '@tekion/tekion-base/utils/general';
import { getOptionsByKeyValuePair } from '@tekion/tekion-base/marketScan/utils';

// renderer
import Radio from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/radio';
import Select from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/select';
import { filterRows } from '@tekion/tekion-components/src/organisms/FormBuilder/utils/general';
import TextInput from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/textInput';
import Heading from '@tekion/tekion-components/src/atoms/Heading';

import { isRequiredRule, isNumber } from '@tekion/tekion-base/utils/formValidators';
import { tableValidator } from '@tekion/tekion-components/src/utils/tableFieldValidators';

// cell renderers
import { SerialNumberRenderer, TextRendererWithRightLabel } from './cellRenderers';
import {
  dayRangeNumberValidator,
  dueDateValidator,
  cutOffDateValidator,
  getDueDateLabel,
  isRequired,
  checkIfPreviousCellHasErrors,
  uniqueValidator,
} from './utils';

import { getDueDateOptions, getPaymentModeOptions, isDueDateDisabled } from './paymentTerm.helpers';

// constants
import {
  PAYMENT_MODE_TYPES,
  FIELD_IDS,
  DUE_MONTH_OPTIONS,
  PAYMENT_DESCRIPTION_FIELD,
  PAYMENT_DATE_OPTIONS_FOR_RRG,
  NO_OF_DAYS_INPUT_STYLE,
} from './paymentTerm.constants';

import TableField from '../../TableField';

// styles
import styles from './paymentTerm.module.scss';

const filterPaymentInfo = {
  noOfDays: ({ paymentMode }) => paymentMode === PAYMENT_MODE_TYPES.DUE_IN_DAYS,
  data: ({ paymentMode }) => paymentMode === PAYMENT_MODE_TYPES.MONTHLY_DISTRIBUTION,
  [FIELD_IDS.IS_FDM]: ({ isRRGProgram }) => isRRGProgram,
  [FIELD_IDS.DUE_MONTH]: ({ paymentMode }) => paymentMode === PAYMENT_MODE_TYPES.MONTH_FOLLOWING,
  [FIELD_IDS.DUE_DATE]: ({ paymentMode }) => paymentMode === PAYMENT_MODE_TYPES.MONTH_FOLLOWING,
  [FIELD_IDS.PAYMENT_DETAILS]: ({ isRRGProgram }) => isRRGProgram,
  [FIELD_IDS.PAYMENT_DATE_SELECTION_HEADING]: ({ isRRGProgram }) => isRRGProgram,
  [FIELD_IDS.PAYMENT_TYPE]: ({ isRRGProgram }) => isRRGProgram,
  [FIELD_IDS.NO_OF_DAYS_AFTER_MONTH_END]: ({ isRRGProgram, fdm, paymentMode }) =>
    isRRGProgram && fdm === PAYMENT_DATE_OPTIONS_FOR_RRG.FDM && paymentMode === PAYMENT_MODE_TYPES.DUE_IN_DAYS,
};

const getHeadingField = (children = '', className = '', size = 3) => ({
  renderer: Heading,
  renderOptions: {
    children,
    size,
    className,
  },
});

// sections for payment term
export const paymentTermSection = defaultMemoize((sectionClass = '', formValues, isInchcapeEnabled) => {
  const paymentMode = tget(formValues, FIELD_IDS.PAYMENT_MODE, PAYMENT_MODE_TYPES.DUE_IN_DAYS);
  const fdm = tget(formValues, FIELD_IDS.IS_FDM);
  return [
    {
      className: cx(sectionClass, styles.formFirstSection),
      rows: [
        {
          columns: filterRows([FIELD_IDS.PAYMENT_DETAILS], { isRRGProgram: isRRG() }, filterPaymentInfo),
        },
        {
          columns: [FIELD_IDS.PAYMENT_TERM_NAME, FIELD_IDS.PAYMENT_DESCRIPTION],
        },
        {
          columns: filterRows([FIELD_IDS.PAYMENT_DATE_SELECTION_HEADING], { isRRGProgram: isRRG() }, filterPaymentInfo),
        },
        {
          columns: filterRows([FIELD_IDS.IS_FDM], { isRRGProgram: isRRG() }, filterPaymentInfo),
        },
        {
          columns: filterRows([FIELD_IDS.PAYMENT_TYPE], { isRRGProgram: isRRG() }, filterPaymentInfo),
        },
        {
          columns: [FIELD_IDS.PAYMENT_MODE],
        },
      ],
    },
    {
      className: fdm !== PAYMENT_DATE_OPTIONS_FOR_RRG.FDM ? cx(sectionClass, styles.noOfDaysSection) : sectionClass,
      rows: [
        {
          columns: filterRows(
            [FIELD_IDS.NO_OF_DAYS, FIELD_IDS.SET_DATES_TABLE, FIELD_IDS.NO_OF_DAYS_AFTER_MONTH_END],
            { paymentMode, isRRGProgram: isRRG(), fdm },
            filterPaymentInfo
          ),
        },
      ],
    },
    ...(isInchcapeEnabled
      ? [
          {
            className: sectionClass,
            rows: [
              {
                columns: filterRows([FIELD_IDS.DUE_MONTH, FIELD_IDS.DUE_DATE], { paymentMode }, filterPaymentInfo),
              },
            ],
          },
        ]
      : []),
  ];
});

export const paymentTermConfig = ({ values, uniqueValues = [], disableName, isInchcapeEnabled, disabled }) => {
  const paymentTerm = values[FIELD_IDS.PAYMENT_MODE];
  const dueMonth = values[FIELD_IDS.DUE_MONTH];

  const NO_OF_DAYS_RENDER_OPTIONS = {
    label: __('Number of Days after invoice date'),
    required: true,
    validators: [isRequiredRule, isNumber],
    inputStyle: disabled ? EMPTY_OBJECT : NO_OF_DAYS_INPUT_STYLE,
  };

  return {
    [FIELD_IDS.PAYMENT_TERM_NAME]: {
      renderer: TextInput,
      accessor: FIELD_IDS.PAYMENT_TERM_NAME,
      renderOptions: {
        label: __('Payment Term Name'),
        required: true,
        validators: [isRequiredRule, uniqueValidator(uniqueValues)],
        disabled: disableName,
      },
    },
    [FIELD_IDS.PAYMENT_MODE]: {
      renderer: Radio,
      accessor: FIELD_IDS.PAYMENT_MODE,
      renderOptions: {
        id: FIELD_IDS.PAYMENT_MODE,
        radios: getPaymentModeOptions(isInchcapeEnabled),
        required: true,
        disabled,
      },
    },
    [FIELD_IDS.PAYMENT_DESCRIPTION]: PAYMENT_DESCRIPTION_FIELD,
    [FIELD_IDS.DUE_MONTH]: {
      renderer: Select,
      renderOptions: {
        id: FIELD_IDS.DUE_MONTH,
        size: 6,
        label: __('Due Month'),
        required: true,
        options: DUE_MONTH_OPTIONS,
        validators: [isRequiredRule],
        disabled,
      },
    },
    [FIELD_IDS.DUE_DATE]: {
      renderer: Select,
      renderOptions: {
        id: FIELD_IDS.DUE_DATE,
        label: __('Due Date'),
        size: 6,
        required: true,
        options: getDueDateOptions({ paymentTerm, dueMonth }),
        validators: [isRequiredRule],
        disabled: isDueDateDisabled({ paymentTerm, dueMonth }),
      },
    },
    [FIELD_IDS.IS_FDM]: {
      renderer: Radio,
      renderOptions: {
        id: FIELD_IDS.IS_FDM,
        radios: getOptionsByKeyValuePair(PAYMENT_DATE_OPTIONS_FOR_RRG),
        validators: [isRequiredRule],
        disabled,
      },
    },
    [FIELD_IDS.NO_OF_DAYS]: {
      renderer: isRRG() ? TextInput : TextRendererWithRightLabel,
      accessor: FIELD_IDS.NO_OF_DAYS,
      renderOptions: isRRG()
        ? {
            ...NO_OF_DAYS_RENDER_OPTIONS,
            triggerChangeOnBlur: true,
          }
        : {
            ...NO_OF_DAYS_RENDER_OPTIONS,
            label: __('Number of Days'),
            rightLabel: __('days after invoice date'),
          },
    },
    [FIELD_IDS.NO_OF_DAYS_AFTER_MONTH_END]: {
      id: FIELD_IDS.NO_OF_DAYS_AFTER_MONTH_END,
      renderer: TextInput,
      renderOptions: {
        label: __('Number of days after the end of the month'),
        validators: [isNumber],
        triggerChangeOnBlur: true,
        disabled,
      },
    },
    [FIELD_IDS.PAYMENT_DETAILS]: getHeadingField(__('Payment Details'), styles.sectionLabel),
    [FIELD_IDS.PAYMENT_DATE_SELECTION_HEADING]: getHeadingField(__('Payment Date Selection'), styles.sectionLabel),
    [FIELD_IDS.PAYMENT_TYPE]: getHeadingField(__('Payment Type'), styles.sectionLabel),
    [FIELD_IDS.SET_DATES_TABLE]: {
      renderer: TableField,
      renderOptions: {
        rowHeight: 40,
        headerComponent: true,
        renderRemoveAction: true,
        expanded: true,
        disabled,
        validators: [
          tableValidator({
            [FIELD_IDS.CUT_OFF_DATE]: [isRequired, isNumber, dayRangeNumberValidator, cutOffDateValidator],
            [FIELD_IDS.DUE_DATE]: [isRequired, isNumber, dayRangeNumberValidator, dueDateValidator],
          }),
        ],
        getFieldRendererOverrides: checkIfPreviousCellHasErrors,
        columns: [
          {
            Header: __('S No.'),
            accessor: FIELD_IDS.SERIAL_NUMBER,
            width: 60,
            Cell: {
              renderer: SerialNumberRenderer,
              renderOptions: {
                id: FIELD_IDS.SERIAL_NUMBER,
                disabled,
              },
            },
          },
          {
            Header: __('Cut-off Date of Current Month'),
            accessor: FIELD_IDS.CUT_OFF_DATE,
            sortable: false,
            Cell: {
              renderer: TextRendererWithRightLabel,
              renderOptions: {
                id: FIELD_IDS.CUT_OFF_DATE,
                errorInPopover: true,
                inputStyle: {
                  width: 90,
                },
                disabled,
              },
            },
          },
          {
            Header: __('Due Date'),
            accessor: FIELD_IDS.DUE_DATE,
            sortable: false,
            Cell: {
              renderer: TextRendererWithRightLabel,
              renderOptions: {
                id: FIELD_IDS.DUE_DATE,
                errorInPopover: true,
                isDynamicLabel: true,
                rightLabel: getDueDateLabel(values, FIELD_IDS.SET_DATES_TABLE, FIELD_IDS.DUE_DATE),
                inputStyle: {
                  width: 90,
                },
                rightLabelStyle: {
                  paddingLeft: 16,
                },
                disabled,
              },
            },
          },
        ],
      },
    },
  };
};
