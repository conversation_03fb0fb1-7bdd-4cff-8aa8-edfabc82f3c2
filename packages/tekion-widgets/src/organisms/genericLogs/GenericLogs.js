import React from 'react';
import PropTypes from 'prop-types';

// Lodash
import _size from 'lodash/size';
import _map from 'lodash/map';
import _noop from 'lodash/noop';

// Constants
import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

// Components
import RotatingLoader from '@tekion/tekion-components/src/atoms/rotatingLoader';
import Content from '@tekion/tekion-components/src/atoms/Content';
import Button from '@tekion/tekion-components/src/atoms/Button';

// Local Components
import LogItem from './molecules/logItem';
import DefaultLogsHeader from './molecules/defaultLogsHeader';

// Assets
import logsLoadFailed from './assets/logsLoadFailed.svg';
import noActivityFound from './assets/noActivityFound.svg';

// Styles
import styles from './genericLogs.module.scss';

const renderHeader = ({
  lastUpdatedTime,
  onRefresh,
  getFormattedDateAndTime,
  headerLabel,
  customHeaderRenderer: CustomHeaderRenderer,
}) => (
  <CustomHeaderRenderer
    headerLabel={headerLabel}
    lastUpdatedTime={lastUpdatedTime}
    onRefresh={onRefresh}
    getFormattedDateAndTime={getFormattedDateAndTime}
  />
);

const renderLoadingContent = loadingMessage => (
  <div className={styles.loaderContainer}>
    <RotatingLoader />
    <Content className={styles.loaderContent}>{loadingMessage}</Content>
  </div>
);

const renderErrorContent = (remount, errorMessage) => (
  <div className={styles.errorContainer}>
    <img src={logsLoadFailed} alt="" />
    <Content className={styles.content}>
      <div className={styles.errorContent}>{errorMessage}</div>
    </Content>
    <Button className="m-t-20 m-b-20" view={Button.VIEW.PRIMARY} onClick={remount}>
      {__('Retry')}
    </Button>
  </div>
);

const renderLogItem =
  ({ logsSize, logIcon, customRightSectionRenderer, separatorClass }) =>
  (logData, index) => {
    const shouldShowSeparator = index !== logsSize - 1;
    return (
      <LogItem
        key={index}
        logData={logData}
        shouldShowSeparator={shouldShowSeparator}
        logIcon={logIcon}
        customRightSectionRenderer={customRightSectionRenderer}
        separatorClass={separatorClass}
      />
    );
  };

const renderLogs = ({ logs, logIcon, customRightSectionRenderer, separatorClass, logsSize }) => (
  <div className={`overflow-y-auto p-t-16 p-b-16 ${styles.logContainer}`}>
    {_map(logs, renderLogItem({ logsSize, logIcon, customRightSectionRenderer, separatorClass }))}
  </div>
);

const renderEmptyLogContent = emptyMessage => (
  <div className={styles.emptyLogContainer}>
    <img src={noActivityFound} alt="" />
    <Content className={styles.emptyLogContent}>{emptyMessage}</Content>
  </div>
);

const renderContent = ({ logs, logIcon, emptyMessage, customRightSectionRenderer, separatorClass }) => {
  const logsSize = _size(logs);
  if (logsSize) {
    return renderLogs({ logs, logIcon, customRightSectionRenderer, separatorClass, logsSize });
  }
  return renderEmptyLogContent(emptyMessage);
};

function GenericLogs(props) {
  const {
    lastUpdatedTime,
    onRefresh,
    isLoading,
    isError,
    remount,
    logs,
    getFormattedDateAndTime,
    headerLabel,
    logIcon,
    loadingMessage,
    errorMessage,
    emptyMessage,
    customRightSectionRenderer,
    customHeaderRenderer,
    separatorClass,
  } = props;

  return (
    <div className={styles.container}>
      {renderHeader({ lastUpdatedTime, onRefresh, getFormattedDateAndTime, headerLabel, customHeaderRenderer })}
      <div className={styles.separator} />
      {isLoading && renderLoadingContent(loadingMessage)}
      {isError && renderErrorContent(remount, errorMessage)}
      {!isLoading &&
        !isError &&
        renderContent({ logs, logIcon, emptyMessage, customRightSectionRenderer, separatorClass })}
    </div>
  );
}

GenericLogs.propTypes = {
  lastUpdatedTime: PropTypes.object,
  onRefresh: PropTypes.func,
  isLoading: PropTypes.bool,
  isError: PropTypes.bool,
  remount: PropTypes.func,
  logs: PropTypes.arrayOf(
    PropTypes.shape({
      logMessage: PropTypes.string,
      formattedAmount: PropTypes.string,
      createdByUserName: PropTypes.string,
      formattedDate: PropTypes.string,
      formattedTime: PropTypes.string,
    })
  ),
  getFormattedDateAndTime: PropTypes.func,
  headerLabel: PropTypes.string,
  logIcon: PropTypes.string,
  loadingMessage: PropTypes.string,
  errorMessage: PropTypes.string,
  emptyMessage: PropTypes.string,
  customRightSectionRenderer: PropTypes.func,
  customHeaderRenderer: PropTypes.func,
  separatorClass: PropTypes.string,
};

GenericLogs.defaultProps = {
  lastUpdatedTime: EMPTY_OBJECT,
  onRefresh: _noop,
  isLoading: true,
  isError: false,
  remount: _noop,
  logs: EMPTY_ARRAY,
  getFormattedDateAndTime: _noop,
  headerLabel: __('Logs'),
  logIcon: 'icon-history',
  loadingMessage: __('Loading, Please Wait!'),
  errorMessage: __('Failed to load!'),
  emptyMessage: __('No logs found!'),
  customRightSectionRenderer: undefined,
  customHeaderRenderer: DefaultLogsHeader,
  separatorClass: undefined,
};

export default GenericLogs;
