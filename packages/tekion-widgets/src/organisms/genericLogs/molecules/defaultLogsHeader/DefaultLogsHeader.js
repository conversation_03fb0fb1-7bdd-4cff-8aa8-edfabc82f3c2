import React, { useMemo } from 'react';
import PropTypes from 'prop-types';

// Lodash
import _noop from 'lodash/noop';

// Constants
import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

// Components
import Heading from '@tekion/tekion-components/src/atoms/Heading';
import IconAsBtn from '@tekion/tekion-components/src/atoms/iconAsBtn';
import FontIcon from '@tekion/tekion-components/src/atoms/FontIcon';

// Helpers
import { getLastUpdatedDateAndTime } from './helpers/defaultLogsHeader.general';

// Styles
import styles from './defaultLogsHeader.module.scss';

const DefaultHeaderRenderer = props => {
  const { headerLabel, lastUpdatedTime, onRefresh, getFormattedDateAndTime } = props;

  const lastUpdatedDateAndTime = useMemo(
    () => getLastUpdatedDateAndTime(lastUpdatedTime, getFormattedDateAndTime),
    [lastUpdatedTime, getFormattedDateAndTime]
  );
  const { date, time } = lastUpdatedDateAndTime;

  return (
    <Heading size={4} className={`p-t-8 p-b-8 ${styles.header}`}>
      <div className={`p-0 ${styles.leftSectionHeader}`}>
        <FontIcon>icon-history</FontIcon>
        {headerLabel}
      </div>
      <div className={styles.rightSectionHeader}>
        <div className={styles.dateAndTime}>{__('Last Updated: {{date}} • {{time}}', { date, time })}</div>
        <IconAsBtn onClick={onRefresh} className={styles.refreshIcon}>
          icon-refresh2
        </IconAsBtn>
      </div>
    </Heading>
  );
};

DefaultHeaderRenderer.propTypes = {
  lastUpdatedTime: PropTypes.object,
  onRefresh: PropTypes.func,
  headerLabel: PropTypes.string,
  getFormattedDateAndTime: PropTypes.func,
};

DefaultHeaderRenderer.defaultProps = {
  lastUpdatedTime: EMPTY_OBJECT,
  onRefresh: _noop,
  headerLabel: __('Logs'),
  getFormattedDateAndTime: _noop,
};

export default DefaultHeaderRenderer;
