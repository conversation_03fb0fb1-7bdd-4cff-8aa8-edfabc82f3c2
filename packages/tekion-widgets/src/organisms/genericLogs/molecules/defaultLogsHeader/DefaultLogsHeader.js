import React from 'react';
import PropTypes from 'prop-types';

// Lodash
import _noop from 'lodash/noop';

// Components
import Heading from '@tekion/tekion-components/src/atoms/Heading';
import IconAsBtn from '@tekion/tekion-components/src/atoms/iconAsBtn';
import FontIcon from '@tekion/tekion-components/src/atoms/FontIcon';

// Styles
import styles from './defaultLogsHeader.module.scss';

const DefaultHeaderRenderer = props => {
  const { headerLabel, onRefresh } = props;

  return (
    <Heading size={4} className={`p-t-8 p-b-8 ${styles.header}`}>
      <div className={`p-0 ${styles.leftSectionHeader}`}>
        <FontIcon>icon-history</FontIcon>
        {headerLabel}
      </div>
      <div className={styles.rightSectionHeader}>
        <IconAsBtn onClick={onRefresh} className={styles.refreshIcon}>
          icon-refresh2
        </IconAsBtn>
      </div>
    </Heading>
  );
};

DefaultHeaderRenderer.propTypes = {
  headerLabel: PropTypes.string,
  onRefresh: PropTypes.func,
};

DefaultHeaderRenderer.defaultProps = {
  headerLabel: __('Logs'),
  onRefresh: _noop,
};

export default DefaultHeaderRenderer;
