@use "tstyles/colors.scss";
@use "tstyles/mixins/layout.scss";
@use "tstyles/mixins/typography.scss";
@use "tstyles/variables.scss";

.container {
  @include layout.flex($align-items: normal);
  gap: 1.6rem;
}

.leftSection {
  width: 1.6rem;
  padding: 0 0 0.2rem;
  @include layout.flex($flex-flow: column, $align-items: center);
}

.separator {
  height: 5rem;
  width: 0.05rem;
  background: colors.$platinum;
}

.rightSection {
  @include layout.flex($flex-flow: column nowrap);
  gap: 0.8rem;
  width: 35.2rem;
  height: 6.5rem;
}

.logMessage {
  height: 1.7rem;
  @include typography.heading5($font-family: variables.$font-regular, $color: colors.$gunMetal);
}
