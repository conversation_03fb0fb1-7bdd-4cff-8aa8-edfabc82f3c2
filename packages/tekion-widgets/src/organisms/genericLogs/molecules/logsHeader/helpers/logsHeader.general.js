// Constant
import { DATE_TIME_FORMAT } from '@tekion/tekion-conversion-web';

export const getLastUpdatedDateAndTime = (lastUpdatedTime, getFormattedDateAndTime) => {
  const date = getFormattedDateAndTime({
    value: lastUpdatedTime,
    formatType: DATE_TIME_FORMAT.ABBREVIATED_BASE,
  });
  const time = getFormattedDateAndTime({
    value: lastUpdatedTime,
    formatType: DATE_TIME_FORMAT.HOUR_MINUTE,
  });
  return { date, time };
};
