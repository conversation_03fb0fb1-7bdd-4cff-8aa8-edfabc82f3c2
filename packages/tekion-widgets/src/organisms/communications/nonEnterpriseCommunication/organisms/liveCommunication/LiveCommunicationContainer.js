import React, { useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';
import { compose } from 'recompose';
import { connect } from 'react-redux';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _find from 'lodash/find';
import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import TEnvReader from '@tekion/tekion-base/readers/Env';
import DEALER_PROPERTIES from '@tekion/tekion-base/constants/dealerProperties';
import withPropertyConsumer from '@tekion/tekion-components/src/organisms/propertyProvider/withPropertyConsumer';
import withActions from '@tekion/tekion-components/src/connectors/withActions';
import { permissionValidations } from '@tekion/tekion-components/src/widgets/permissionsHelper';
import { PERMISSIONS } from '@tekion/tekion-base/constants/permissions';
import _noop from 'lodash';
import useInterval from './hooks/useInterval';
import withDealerMaster from '../../../../../hocs/dealer/withDealerMaster';
import PermissionPlaceHolder from '../../../atoms/permissionPlaceHolder';
import {
  fetchChatsList,
  fetchScreenMessage,
  fetchSendMessages,
  setActiveConversationId,
  getSortByContactList,
  updateChatStatusByType,
  updateDepartmentStatusByType,
  updateDepartmentStatusByRequest,
  fetchCannedMessages,
  fetchVehicleDetails,
  fetchContactList,
  fetchDealerSpecificFilters,
  fetchTenantUsers,
  handleLeadFormUpdate,
  setCountBySelectedConversationId,
  setSearchConversations,
  setSearchVisible,
  setSharableMediaFiles,
  setMediaUploading,
  setMarkUnreadRead,
  updateChatFilters,
  setFilterType,
  setThresholdSettings,
  setChatListPending,
  removeUserStatus,
  setUserSentMessage,
  fetchTemplates,
  setMediaList,
  fetchMessageList,
  setFilterSection,
  setUserTyping,
  setDepartmentConverstions,
  updateDepartmentStatusByExpired,
  updateDepartmentStatusByAccepted,
  setChatListCustomStatus,
  handleLeadAndAppointmentSubmit,
  handleUpcomingAppointments,
  fetchLeadDetails,
  checkIsDealerMultiWebsite,
  createVirtualMeetingRoom,
  joinVirtualMeetingRoom,
  endVideoCall,
  fetchDealerSetings,
  setActiveVideoCallRoomId,
} from '../../actions/LiveConversations.actions';
import conversationReader from './readers/conversationList.reader';
import LiveCommunication from './LiveCommunication';
import {
  LIVE_ACTION_EVENTS,
  CHAT_INITIAL_STATE,
  LIVE_COMMUNICATION_EVENTS,
  REQUEST_TYPE,
  CONVERSATION_POLLING_TIMER,
  MESSAGES_POLLING_TIMER,
  FILTER_BY,
  CONVERSATION_AND_CHAT_STATUS,
  INSIGHTS_POLLING_TIMER,
  INSIGHTS_LOADING_STATUS,
  CUSTOMER,
} from './constants';
import ACTION_HANDLERS from './LiveCommunication.actionHandlers';
import {
  getLiveSelectedConversationId,
  getConversationLoadingStatus,
  getChatScreenLoadingStatus,
  getConversationListIds,
  getLiveConversations,
  getCannedMessages,
  getCountBySelectedConversationId,
  getSharableMediaFiles,
  getChatListFilters,
  getChatListFilterType,
  getExternalEventLoadingStatus,
  getConversationBySelectedId,
  getLiveMediaList,
  getMessageFromTime,
  getDepartmentConversations,
  getIsVideoCallVisible,
} from '../../reducers/selector';
import LivePusher from './livePusher/LivePusher';
import withVirtualMeeting from '../../../../VirtualMeeting/hoc/withVirtualMeeting';

const userInfo = TEnvReader.userInfo();

const LiveCommunicationContainer = props => {
  const {
    onAction,
    conversations,
    departmentConversations,
    selectedConversationId,
    conversationStatus,
    chatScreenLoadingStatus,
    count,
    countBySelectedConversationId,
    filterType,
    activeFilterPageNo,
    allFilterPageNo,
    filters,
    permissions,
    showLiveCommunication,
    messageFromTimeById,
    isLivePusherEnabled,
    getDealerPropertyValue,
    dealerMaster,
    livePusherInstance,
    ...restProps
  } = props;

  const isCrmEnabled = getDealerPropertyValue(DEALER_PROPERTIES.CRM_ENABLED) || false;
  const selectedConversationChat = _find(
    conversations,
    conversation => _get(conversation, 'chatId') === selectedConversationId
  );
  const isAgentMentioned = useMemo(
    () => conversationReader.unreadMentions(selectedConversationChat),
    [conversations, selectedConversationId]
  );

  const getCustomerId = () => {
    const customerId = conversationReader.customerId(selectedConversationChat);
    if (customerId) return customerId;

    const senderType = conversationReader.senderInfoType(selectedConversationChat);
    const receiverType = conversationReader.receiverInfoType(selectedConversationChat);
    const senderId = conversationReader.senderInfoId(selectedConversationChat);
    const receiverId = conversationReader.receiverInfoId(selectedConversationChat);
    let id;

    if (senderType === CUSTOMER) {
      id = senderId;
    }
    if (receiverType === CUSTOMER) {
      id = receiverId;
    }
    return id;
  };

  useInterval(() => {
    if (!_isEmpty(conversations) && !isLivePusherEnabled) {
      onAction({
        type: LIVE_ACTION_EVENTS.GET_SCREEN_MESSAGES,
        payload: {
          id: selectedConversationId,
          time: messageFromTimeById || Date.now(),
          count: _get(countBySelectedConversationId, `${selectedConversationId}.count`) || count,
          department: conversationReader.chatTag(selectedConversationChat),
          isCrmEnabled,
        },
      });
    }
  }, MESSAGES_POLLING_TIMER);

  useInterval(() => {
    onAction({
      type: LIVE_ACTION_EVENTS.GET_CHATS_LIST,
      payload: {
        requestType: REQUEST_TYPE.POOLING,
        pageNo: filterType === FILTER_BY.ACTIVE_CHATS ? activeFilterPageNo : allFilterPageNo,
      },
    });
    if (isAgentMentioned) {
      onAction({
        type: LIVE_ACTION_EVENTS.UPDATE_IS_UNREAD_MENTIONS,
        payload: {
          chatId: selectedConversationId,
          markTagRead: true,
        },
      });
    }
  }, CONVERSATION_POLLING_TIMER);

  useInterval(() => {
    if (!_isEmpty(conversations)) {
      const id = getCustomerId();
      if (id)
        onAction({
          type: LIVE_ACTION_EVENTS.GET_INSIGHTS_DATA,
          payload: {
            id,
            selectedConversationId,
          },
        });
    }
  }, INSIGHTS_POLLING_TIMER);

  useEffect(() => {
    onAction({
      type: LIVE_COMMUNICATION_EVENTS.SET_PROGRAM,
      payload: { isHondaProgram: getDealerPropertyValue(DEALER_PROPERTIES.IS_HONDA_PROGRAM) },
    });
  }, [onAction, getDealerPropertyValue]);

  useEffect(() => {
    onAction({ type: LIVE_COMMUNICATION_EVENTS.CHECK_IS_DEALER_MULTI_WEBSITE });
  }, [onAction]);

  useEffect(() => {
    const id = getCustomerId();
    if (!_isEmpty(selectedConversationId) && id) {
      onAction({
        type: LIVE_ACTION_EVENTS.GET_INSIGHTS_DATA,
        payload: {
          id,
          loadingState: INSIGHTS_LOADING_STATUS.FETCHING_NEW_CHAT,
          selectedConversationId,
        },
      });
      if (isLivePusherEnabled) {
        onAction({
          type: LIVE_ACTION_EVENTS.GET_SCREEN_MESSAGES,
          payload: {
            id: selectedConversationId,
            time: messageFromTimeById || Date.now(),
            count: _get(countBySelectedConversationId, `${selectedConversationId}.count`) || count,
            newConversationSelected: true,
            department: conversationReader.chatTag(selectedConversationChat),
            isCrmEnabled,
          },
        });
      }
    }
  }, [selectedConversationId, messageFromTimeById]);

  useEffect(() => {
    if (!_isEmpty(selectedConversationId)) {
      onAction({
        type: LIVE_COMMUNICATION_EVENTS.GET_DEALER_SPECIFIC_FILTERS,
        payload: {
          selectedConversationId,
        },
      });
    }
  }, [onAction, selectedConversationId]);

  useEffect(() => {
    onAction({ type: LIVE_COMMUNICATION_EVENTS.GET_THRESHOLD_SETTINGS });
    onAction({ type: LIVE_COMMUNICATION_EVENTS.GET_DEALER_SETTINGS });
    onAction({ type: LIVE_COMMUNICATION_EVENTS.GET_DEPARTMENT_CONVERSATIONS });
    onAction({ type: LIVE_COMMUNICATION_EVENTS.CHECK_IS_DEALER_MULTI_WEBSITE });
  }, []);

  const isPermittedToSendMessage = useMemo(
    () =>
      permissionValidations.validatePermissions(permissions, {
        validFor: [PERMISSIONS.LIVE_CHAT.LIVE_CHAT_COMMUNICATION.SEND_CUSTOMER_MESSAGE],
      }),
    []
  );

  const isPermittedToSendInternalMessage = useMemo(
    () =>
      permissionValidations.validatePermissions(permissions, {
        validFor: [PERMISSIONS.LIVE_CHAT.LIVE_CHAT_COMMUNICATION.SEND_INTERNAL_MESSAGE],
      }),
    []
  );
  const isPermittedToEnterChat = useMemo(
    () =>
      permissionValidations.validatePermissions(permissions, {
        validFor: [PERMISSIONS.LIVE_CHAT.LIVE_CHAT_COMMUNICATION.OVERRIDE_EXISTING_AGENT],
      }),
    []
  );

  if (!showLiveCommunication) {
    return <PermissionPlaceHolder label={__('You do not have permission to access Live Chat')} />;
  }
  return (
    <LivePusher isPusherEnabled={isLivePusherEnabled} livePusherInstance={livePusherInstance}>
      <LiveCommunication
        {...restProps}
        selectedConversationId={selectedConversationId}
        conversations={conversations}
        departmentConversations={departmentConversations}
        conversationStatus={conversationStatus}
        chatScreenLoadingStatus={chatScreenLoadingStatus}
        userInfo={userInfo}
        count={count}
        countBySelectedConversationId={countBySelectedConversationId}
        onAction={onAction}
        filters={filters}
        filterType={filterType}
        activeFilterPageNo={activeFilterPageNo}
        allFilterPageNo={allFilterPageNo}
        isPermittedToSendMessage={isPermittedToSendMessage}
        isPermittedToSendInternalMessage={isPermittedToSendInternalMessage}
        isPermittedToEnterChat={isPermittedToEnterChat}
        isCrmEnabled={isCrmEnabled}
        dealerLogos={_get(dealerMaster, 'dealerLogos')}
        dealerName={_get(dealerMaster, 'dealerName')}
      />
    </LivePusher>
  );
};

const mapStateToProps = state => ({
  conversations: getLiveConversations(state),
  departmentConversations: getDepartmentConversations(state),
  selectedConversationId: getLiveSelectedConversationId(state),
  conversationStatus: getConversationLoadingStatus(state),
  chatScreenLoadingStatus: getChatScreenLoadingStatus(state),
  conversationListIds: getConversationListIds(state),
  cannedMessages: getCannedMessages(state),
  countBySelectedConversationId: getCountBySelectedConversationId(state),
  filters: getChatListFilters(state),
  filterType: getChatListFilterType(state),
  sharableMediaFileList: getSharableMediaFiles(state),
  externalEventLoadingStatus: getExternalEventLoadingStatus(state),
  selectedConversation: getConversationBySelectedId(state),
  liveMediaList: getLiveMediaList(state),
  messageFromTimeById: getMessageFromTime(state),
  isVideoCallVisible: getIsVideoCallVisible(state),
});

LiveCommunicationContainer.propTypes = {
  selectedConversationId: PropTypes.oneOf([PropTypes.number, PropTypes.string]).isRequired,
  conversations: PropTypes.arrayOf(PropTypes.object).isRequired,
  dealerMaster: PropTypes.object.isRequired,
  departmentConversations: PropTypes.array,
  isLoading: PropTypes.bool,
  onAction: PropTypes.func,
  conversationStatus: PropTypes.string,
  chatScreenLoadingStatus: PropTypes.string,
  count: PropTypes.number.isRequired,
  countBySelectedConversationId: PropTypes.object,
  isPermittedToSendMessage: PropTypes.bool,
  isPermittedToSendInternalMessage: PropTypes.bool,
  isPermittedToEnterChat: PropTypes.bool,
  bottomChatsLoading: PropTypes.bool,
  externalEventLoadingStatus: PropTypes.bool,
  hasMoreConversations: PropTypes.bool,
  bottomConversationLoading: PropTypes.bool,
  isFiltersLoading: PropTypes.bool,
  activeFilterPageNo: PropTypes.number,
  allFilterPageNo: PropTypes.number,
  sort: PropTypes.array.isRequired,
  filters: PropTypes.object.isRequired,
  filterType: PropTypes.string.isRequired,
  isLivePusherEnabled: PropTypes.bool.isRequired,
  permissions: PropTypes.array,
  showLiveCommunication: PropTypes.bool,
  messageFromTimeById: PropTypes.string,
  getDealerPropertyValue: PropTypes.func.isRequired,
};

LiveCommunicationContainer.defaultProps = {
  isLoading: false,
  externalEventLoadingStatus: false,
  isPermittedToSendMessage: false,
  isPermittedToSendInternalMessage: false,
  isPermittedToEnterChat: false,
  bottomChatsLoading: false,
  countBySelectedConversationId: EMPTY_OBJECT,
  hasMoreConversations: false,
  bottomConversationLoading: false,
  isFiltersLoading: false,
  activeFilterPageNo: 1,
  allFilterPageNo: 1,
  chatScreenLoadingStatus: CONVERSATION_AND_CHAT_STATUS.IDLE,
  conversationStatus: CONVERSATION_AND_CHAT_STATUS.IDLE,
  onAction: _noop,
  permissions: EMPTY_ARRAY,
  messageFromTimeById: EMPTY_STRING,
  showLiveCommunication: false,
  departmentConversations: EMPTY_ARRAY,
};

export default compose(
  React.memo,
  withVirtualMeeting,
  withDealerMaster,
  withPropertyConsumer,
  connect(mapStateToProps, {
    fetchChatsList,
    fetchScreenMessage,
    fetchSendMessages,
    setActiveConversationId,
    getSortByContactList,
    updateChatStatusByType,
    updateDepartmentStatusByType,
    updateDepartmentStatusByRequest,
    fetchCannedMessages,
    fetchVehicleDetails,
    fetchContactList,
    fetchDealerSpecificFilters,
    fetchTenantUsers,
    setCountBySelectedConversationId,
    setSearchConversations,
    setSearchVisible,
    handleLeadFormUpdate,
    setSharableMediaFiles,
    setMediaUploading,
    setMarkUnreadRead,
    updateChatFilters,
    setFilterType,
    setThresholdSettings,
    setDepartmentConverstions,
    setChatListPending,
    removeUserStatus,
    setUserSentMessage,
    fetchTemplates,
    setMediaList,
    fetchMessageList,
    setFilterSection,
    setUserTyping,
    updateDepartmentStatusByExpired,
    updateDepartmentStatusByAccepted,
    setChatListCustomStatus,
    handleLeadAndAppointmentSubmit,
    handleUpcomingAppointments,
    fetchLeadDetails,
    checkIsDealerMultiWebsite,
    createVirtualMeetingRoom,
    joinVirtualMeetingRoom,
    endVideoCall,
    fetchDealerSetings,
    setActiveVideoCallRoomId,
  }),
  withActions(CHAT_INITIAL_STATE, ACTION_HANDLERS)
)(LiveCommunicationContainer);
