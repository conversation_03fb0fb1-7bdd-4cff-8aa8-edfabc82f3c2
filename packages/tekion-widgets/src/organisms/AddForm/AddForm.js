import React, { PureComponent } from 'react';
import cx from 'classnames';
import PropTypes from 'prop-types';
import _noop from 'lodash/noop';
import _get from 'lodash/get';
import _set from 'lodash/set';
import _curry from 'lodash/curry';
import _includes from 'lodash/includes';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { FORM_SOURCE } from '@tekion/tekion-base/constants/deal/formSetup';
import { FORM_PRINTER_TYPES } from '@tekion/tekion-base/constants/formConfigurator/printers';
import DEALER_PROPERTY_CONSTANTS from '@tekion/tekion-base/constants/dealerProperties';
import TEnvReader from '@tekion/tekion-base/readers/Env';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import Heading from '@tekion/tekion-components/src/atoms/Heading';
import FormPage from '@tekion/tekion-components/src/organisms/FormBuilder';
import { isInchcape } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
import FORM_ACTION_TYPES from '@tekion/tekion-components/src/organisms/FormBuilder/constants/actionTypes';

import DigiCertEncryption from '../digiCertEncryption';
import UsageTargetting from './components/usageTargetting';
import AddFormPdfRenderer from './components/addFormPdfRenderer';
import EContractFormDetails from './components/eContractFormDetails';
import VisibilityTargetting from './components/visibilityTargetting';
import WorkflowTargetting from './components/workflowTargetting';
import LabelsCell from './LabelsCell';
import { getFormSections, getFormFields } from './AddForm.config';
import { FORM_FIELD, DIGICERT_HELPER_TEXT, DIGICERT_ENCRYPTION_TYPES, FORM_FIELD_LABELS } from './AddForm.constants';
import ACTION_TYPES from './AddForm.actionTypes';
import { disableFormSetupField, getUpdatedLabels, getWorkflowTargetingData } from './AddForm.utils';
import { mmToPx, pxToMm } from '../formConfiguratorTool/FormConfiguratorTool.utils';

import OptionsGroupHeading from '../../molecules/optionsGroupHeading';
import styles from './AddForm.module.scss';

class AddForm extends PureComponent {
  constructor(props) {
    super(props);
    const { allLabels = EMPTY_ARRAY, recentLabels = EMPTY_ARRAY } = props;
    this.state = {
      allLabels,
      recentLabels,
    };
  }

  componentDidMount() {
    const { onAction, dynamicUsageCategoryVsRules } = this.props;
    onAction({
      type: ACTION_TYPES.ON_FORM_MOUNT,
      payload: {
        dynamicUsageCategoryVsRules,
      },
    });
  }

  addUsageTargetParam = module => () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ADD_USAGE_TARGET_PARAMS,
      payload: { module },
    });
  };

  // eslint-disable-next-line react/sort-comp
  deleteUsageTargetParam = _curry((module, index) => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.DELETE_USAGE_TARGET_PARAMS,
      payload: { value: index, module },
    });
  });

  updateUsageTargetParams = _curry((module, { index, key, value, label }) => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.UPDATE_USAGE_TARGET_PARAMS,
      payload: { index, key, value, module, label },
    });
  });

  onLabelChange = value => {
    const { onAction } = this.props;
    const { recentLabels, allLabels } = this.state;
    const { newRecentLabels, newAllLabels } = getUpdatedLabels({
      allLabels,
      recentLabels,
      newLabels: value,
    });
    this.setState({ allLabels: newAllLabels, recentLabels: newRecentLabels });
    onAction({
      type: ACTION_TYPES.ON_LABEL_CHANGE,
      payload: value,
    });
  };

  onSelectGroupLabelChange = value => {
    const { onAction } = this.props;
    onAction({
      type: FORM_ACTION_TYPES.ON_FIELD_CHANGE,
      payload: {
        id: FORM_FIELD.STATES,
        value,
      },
    });
  };

  renderGroupLabel = props => {
    const { formValues } = this.props;
    return (
      <OptionsGroupHeading
        {...props}
        selectedOptions={_get(formValues, FORM_FIELD.STATES, EMPTY_ARRAY)}
        onGroupSelect={this.onSelectGroupLabelChange}
      />
    );
  };

  onHandleChange = ({ payload: { id, value }, type }) => {
    const { onAction } = this.props;
    if (type === FORM_ACTION_TYPES.ON_FIELD_CHANGE) {
      let updatedValue = value;

      switch (id) {
        case FORM_FIELD.OFFSET_X:
        case FORM_FIELD.OFFSET_Y:
          updatedValue = mmToPx(value);
          break;

        default:
          break;
      }

      onAction({
        payload: { id, value: updatedValue },
        type,
      });
    } else onAction({ payload: { id, value }, type });
  };

  getMmBasedFormValues = () => {
    const { formValues } = this.props;

    const updatedValues = { ...formValues };

    _set(updatedValues, FORM_FIELD.OFFSET_X, pxToMm(formValues[FORM_FIELD.OFFSET_X]));

    _set(updatedValues, FORM_FIELD.OFFSET_Y, pxToMm(formValues[FORM_FIELD.OFFSET_Y]));

    return updatedValues;
  };

  render() {
    const {
      formValues,
      errors,
      dynamicUsageCategoryVsRules,
      usageTargetParams,
      warnings,
      formWrapperClassName,
      documentWrapperClassName,
      formHeight,
      isEditing,
      isCreating,
      mediaId,
      formNameValue,
      showRequiredFields,
      requiredFields,
      formPrinterType,
      isGlobalForm,
      isFormsLibrary,
      hasEditGlobalFieldsPermission,
      hasFormConfiguratorView: isViewOnlyMode,
      econtractForm,
      activePanelKey,
      printerTypes,
      docTypes,
      vehicleTypes,
      categories,
      formCategories,
      isDealerTrackEnabled,
      licensedByKeys,
      onAction,
      moduleVisibilityRules,
      hasAddVisibilityTargettingPermission,
      getDealerPropertyValue,
      disableCalcEngine,
      hasEContractingEditPermission,
      visibilityAcrossModulesEnabled,
      editType,
      workflowTargettingData,
      globalMetaData,
      refreshFormTag,
      tradeInCount,
      isFCLiteEditFlow,
      isFCLiteViewFlow,
    } = this.props;
    const { recentLabels, allLabels } = this.state;
    const { expiryDate, effectiveDate, formCategory } = formValues;
    const formScanKey = _get(formValues, FORM_FIELD.FORM_KEY);
    if (formNameValue) {
      _set(formValues, 'formName', formNameValue);
    }
    const formSource = _get(formValues, 'formSource', FORM_SOURCE.FORM_PRO);
    const targettedModules = _get(formValues, FORM_FIELD.TARGETED_MODULES, EMPTY_ARRAY);
    const isFleetEnabled = getDealerPropertyValue(DEALER_PROPERTY_CONSTANTS.FLEETS);
    const digiCertEncryptionEnabled = _get(formValues, 'digiCertEncryptionEnabled');
    const isDigiCertDPValue = getDealerPropertyValue(DEALER_PROPERTY_CONSTANTS.DIGICERT_SIGNATURE_ENCRYPTION_TYPE);
    const isDigiCertDPEnabled = _includes(DIGICERT_ENCRYPTION_TYPES, isDigiCertDPValue);
    const userInfo = TEnvReader.userInfo();
    const userPersona = _get(userInfo, 'persona');
    const isPDFLibraryForm = !!_get(formValues, 'pdfLibraryPdfKey');

    const { workflowTargettedModules, workflowTargetted } = getWorkflowTargetingData(
      _get(formValues, FORM_FIELD.WORK_FLOW_TARGETING_RULES_MAP)
    );

    const updatedFormValues = this.getMmBasedFormValues(formValues);

    return (
      <>
        <div
          className={cx(styles.leftSideContent, formWrapperClassName, {
            [styles.formDisabled]: isViewOnlyMode,
          })}>
          <FormPage
            sections={getFormSections({
              isFleetEnabled,
              isFormsLibrary,
              isGlobalForm,
              userPersona,
              isDealerTrackEnabled,
              isFCLiteEditFlow,
              isFCLiteViewFlow,
            })}
            fields={getFormFields({
              ...updatedFormValues,
              printerTypes,
              formPrinterType,
              expiryDate,
              effectiveDate,
              formCategories,
              formCategory,
              licensedByKeys,
              isEditing,
              isCreating,
              formNameValue,
              isGlobalForm,
              requiredFields: showRequiredFields ? requiredFields : EMPTY_ARRAY,
              docTypes,
              disableCalcEngine,
              hasEContractingEditPermission,
              isPDFLibraryForm,
              isFormsLibrary,
              editType,
              globalMetaData,
              renderGroupLabel: this.renderGroupLabel,
              refreshFormTag,
            })}
            values={updatedFormValues}
            headerComponent={null}
            footerComponent={null}
            onAction={this.onHandleChange}
            errors={errors}
            warnings={warnings}
          />
          {!isFCLiteEditFlow && !isFCLiteViewFlow && (
            <EContractFormDetails
              econtractForm={econtractForm}
              activePanelKey={activePanelKey}
              onAction={onAction}
              formValues={updatedFormValues}
              errors={errors}
              warnings={warnings}
              disableForm={!isGlobalForm && !hasEditGlobalFieldsPermission}
            />
          )}

          <div className="m-x-24 m-t-24 m-b-24">
            <Heading size={3}>{FORM_FIELD_LABELS[FORM_FIELD.FORM_LABELS]}</Heading>
            <LabelsCell
              labels={_get(updatedFormValues, 'labels')}
              recentLabels={recentLabels}
              allLabels={allLabels}
              onLabelChange={this.onLabelChange}
            />
          </div>
          <UsageTargetting
            targettedModules={targettedModules}
            onAction={onAction}
            usageTargetParams={usageTargetParams}
            addUsageTargetParam={this.addUsageTargetParam}
            deleteUsageTargetParam={this.deleteUsageTargetParam}
            updateUsageTargetParams={this.updateUsageTargetParams}
            vehicleTypes={vehicleTypes}
            dynamicUsageCategoryVsRules={dynamicUsageCategoryVsRules}
            categories={categories}
            isGlobalForm={isGlobalForm}
            tradeInCount={tradeInCount}
          />
          <VisibilityTargetting
            moduleVisibilityRules={moduleVisibilityRules}
            onAction={onAction}
            hasAddVisibilityTargettingPermission={hasAddVisibilityTargettingPermission}
            targettedModules={targettedModules}
            isFormsLibrary={isFormsLibrary}
            visibilityAcrossModulesEnabled={visibilityAcrossModulesEnabled}
            tradeInCount={tradeInCount}
          />
          <PropertyControlledComponent controllerProperty={isDigiCertDPEnabled || isFormsLibrary}>
            <div className={styles.digiCertContainer}>
              <DigiCertEncryption
                onAction={onAction}
                digiCertFormEnabled={digiCertEncryptionEnabled}
                helperText={DIGICERT_HELPER_TEXT}
                disabled={isGlobalForm}
              />
            </div>
          </PropertyControlledComponent>

          <PropertyControlledComponent controllerProperty={!isFormsLibrary && isInchcape()}>
            <WorkflowTargetting
              workflowTargettingData={workflowTargettingData}
              workflowTargettedModules={workflowTargettedModules}
              workflowTargetted={workflowTargetted}
              onAction={onAction}
            />
          </PropertyControlledComponent>
        </div>

        <div className={cx(styles.pdfContent, documentWrapperClassName)}>
          <AddFormPdfRenderer
            formScanKey={formScanKey}
            formHeight={formHeight}
            isEditing={isEditing}
            isCreating={isCreating}
            mediaId={mediaId}
          />
        </div>
      </>
    );
  }
}

AddForm.propTypes = {
  printerTypes: PropTypes.array,
  docTypes: PropTypes.array,
  vehicleTypes: PropTypes.array.isRequired,
  formCategories: PropTypes.array.isRequired,
  licensedByKeys: PropTypes.object,
  formValues: PropTypes.object,
  errors: PropTypes.object,
  warnings: PropTypes.object,
  onAction: PropTypes.func,
  dynamicUsageCategoryVsRules: PropTypes.object,
  categories: PropTypes.object,
  usageTargetParams: PropTypes.object,
  formHeight: PropTypes.number,
  formWrapperClassName: PropTypes.number,
  documentWrapperClassName: PropTypes.number,
  isEditing: PropTypes.bool,
  isCreating: PropTypes.bool,
  mediaId: PropTypes.string,
  formNameValue: PropTypes.string,
  formPrinterType: PropTypes.string,
  showRequiredFields: PropTypes.bool,
  requiredFields: PropTypes.array,
  isGlobalForm: PropTypes.bool,
  isFormsLibrary: PropTypes.bool,
  hasEditGlobalFieldsPermission: PropTypes.bool,
  recentLabels: PropTypes.array,
  allLabels: PropTypes.array,
  hasFormConfiguratorView: PropTypes.bool,
  econtractForm: PropTypes.bool,
  activePanelKey: PropTypes.array,
  moduleVisibilityRules: PropTypes.object,
  hasAddVisibilityTargettingPermission: PropTypes.bool,
  getDealerPropertyValue: PropTypes.func,
  isDealerTrackEnabled: PropTypes.bool,
  disableCalcEngine: PropTypes.bool,
  hasEContractingEditPermission: PropTypes.bool,
  visibilityAcrossModulesEnabled: PropTypes.bool,
  globalMetaData: PropTypes.object,
  editType: PropTypes.string,
  workflowTargettingData: PropTypes.array,
  refreshFormTag: PropTypes.func,
  tradeInCount: PropTypes.number.isRequired,
};

AddForm.defaultProps = {
  printerTypes: EMPTY_ARRAY,
  docTypes: EMPTY_ARRAY,
  licensedByKeys: EMPTY_OBJECT,
  formValues: EMPTY_OBJECT,
  errors: EMPTY_OBJECT,
  warnings: EMPTY_OBJECT,
  dynamicUsageCategoryVsRules: EMPTY_OBJECT,
  categories: EMPTY_OBJECT,
  usageTargetParams: EMPTY_OBJECT,
  onAction: _noop,
  formHeight: 500,
  formWrapperClassName: EMPTY_STRING,
  documentWrapperClassName: EMPTY_STRING,
  isEditing: false,
  isCreating: false,
  mediaId: EMPTY_STRING,
  formNameValue: EMPTY_STRING,
  showRequiredFields: false,
  requiredFields: EMPTY_ARRAY,
  formPrinterType: FORM_PRINTER_TYPES.IMPACT,
  isGlobalForm: false,
  isFormsLibrary: false,
  hasEditGlobalFieldsPermission: false,
  recentLabels: EMPTY_ARRAY,
  allLabels: EMPTY_ARRAY,
  hasFormConfiguratorView: true,
  econtractForm: false,
  activePanelKey: EMPTY_ARRAY,
  moduleVisibilityRules: EMPTY_OBJECT,
  hasAddVisibilityTargettingPermission: false,
  getDealerPropertyValue: _noop,
  isDealerTrackEnabled: false,
  disableCalcEngine: false,
  hasEContractingEditPermission: false,
  visibilityAcrossModulesEnabled: false,
  globalMetaData: EMPTY_OBJECT,
  editType: EMPTY_STRING,
  workflowTargettingData: EMPTY_ARRAY,
  refreshFormTag: _noop,
};

export default AddForm;
