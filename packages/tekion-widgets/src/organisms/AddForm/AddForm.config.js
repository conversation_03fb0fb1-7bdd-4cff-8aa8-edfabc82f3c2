import React from 'react';
import _map from 'lodash/map';
import _filter from 'lodash/filter';
import _get from 'lodash/get';
import _includes from 'lodash/includes';
import _values from 'lodash/values';
import _set from 'lodash/set';
import { defaultMemoize } from 'reselect';

import { EMPTY_STRING, EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import {
  DATE_TIME_FORMATS,
  addDays,
  getToday,
  isAfter,
  isBefore,
  subtractDays,
} from '@tekion/tekion-base/utils/dateUtils';
import standardFieldOptionMapper from '@tekion/tekion-base/utils/optionMappers/standardFieldMapper';
import {
  PRINTER_TYPES_LABEL,
  PRINTER_TYPES,
  FORM_PRINTER_VS_PRINTER_TYPES,
} from '@tekion/tekion-base/constants/formConfigurator/printers';
import {
  CALCULATION_ENGINE_OPTIONS,
  E_CONTRACTING,
  LICENSED_FOR_OPTIONS,
  MODULE_TARGETTING_TYPES,
  ENTERPRISE_OPTIONS,
} from '@tekion/tekion-base/constants/formConfigurator/constants';
import { INTERNAL_USER_PERSONAS_MAPPING } from '@tekion/tekion-base/constants/userMimicking.constants';
import IconAsBtn from '@tekion/tekion-components/src/atoms/iconAsBtn';
import CheckBox from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/checkbox';
import TextInput from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/textInput';
import Select from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/select';
import SelectInput from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/SelectInput';
import MultiSelectField from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/MultiSelectField';
import COLORS from '@tekion/tekion-styles-next/scss/exports.scss';
import NumberInputWithSignToggle from '../../molecules/NumberInputWithSignToggle';

import InputDatePickerField from '../../fieldRenderers/inputDatePickerField';
import NumberInputField from '../../fieldRenderers/numberInputField';
import { shouldBeBefore, shouldBeAfter } from '../../appServices/sales/utils/formValidator';
import {
  AGENCY_LIST_OPTIONS,
  E_CONTRACTING_FORM_FIELD,
  E_CONTRACT_FORM_TYPES,
  E_CONTRACT_FORM_TYPE_OPTIONS,
  PAYMENT_TYPE_OPTIONS,
  STATES,
  VAULTING_PARTNERS,
  VAULTING_PARTNER_OPTIONS,
} from '../../constants/eContractForm';
import DatePickerWrapper from './components/datePickerWrapper';
import {
  PRICE_TYPE_OPTIONS,
  FORM_FIELD,
  DEPARTMENTS_OPTIONS,
  SIGNING_TYPE_OPTIONS,
  USAGE_CATEGORY_VS_MODULE,
  FORM_FIELD_LABELS,
  OFFSET_X_RADIOS,
  OFFSET_Y_RADIOS,
  SIGNS,
} from './AddForm.constants';
import {
  disableFormSetupField,
  isFieldRequired,
  formattedSelectFormsList,
  disableFormSetupBasedOnField,
  formattedStatesListResponse,
} from './AddForm.utils';
import styles from './AddForm.module.scss';

const getPrinterTypesOptions = (printerTypes, formPrinterType) =>
  _filter(
    _filter(
      _map(printerTypes, printerType => ({
        label: PRINTER_TYPES_LABEL[printerType],
        value: printerType,
      })),
      'label'
    ),
    formPrinter => _includes(FORM_PRINTER_VS_PRINTER_TYPES[formPrinterType], _get(formPrinter, 'value'))
  );

const getFormCategoryRenderOptions = (
  formCategories,
  formCategory,
  hasEContractingEditPermission,
  isFormsLibrary,
  editType
) => {
  const validFormCategories = _map(
    _filter(formCategories, category => category.enabled),
    category => ({
      label: category.displayName,
      value: category.formCategory,
      isDisabled: !hasEContractingEditPermission && category.formCategory === E_CONTRACTING,
    })
  );

  const renderOptions = {
    options: validFormCategories,
    label: FORM_FIELD_LABELS[FORM_FIELD.FORM_CATEGORY],
    className: styles.formCategory,
    helpText: __('The category defines the deal jacket where the form would be added'),
    infoContentClassName: styles.iconContent,
    infoIconClassName: styles.infoIcon,
  };
  const category = _filter(formCategories, fCategory => formCategory === fCategory.formCategory)[0];
  if (category) {
    renderOptions.defaultValue = category.displayName;
  }
  _set(renderOptions, 'disabled', disableFormSetupField(isFormsLibrary, editType));

  return renderOptions;
};

export const getFormSections = ({
  isFleetEnabled,
  isFormsLibrary,
  isGlobalForm,
  userPersona,
  isDealerTrackEnabled,
  isFCLiteEditFlow,
  isFCLiteViewFlow,
}) => {
  if (!isFCLiteEditFlow) {
    return [
      {
        className: `full-width ${styles.formContainer}`,
        header: { label: __('Form Identification'), size: 3 },
        rows: [
          {
            columns: [FORM_FIELD.FORM_NAME],
          },
          {
            columns: isGlobalForm ? [FORM_FIELD.FORM_TAG] : EMPTY_ARRAY,
          },
          {
            columns: [FORM_FIELD.CODE, FORM_FIELD.FORM_CATEGORY, FORM_FIELD.EFFECTIVE_DATE],
          },
          {
            columns: [FORM_FIELD.EXPIRY_DATE, FORM_FIELD.DEPARTMENT, FORM_FIELD.DOCUMENT_NAME],
          },
          {
            columns: [FORM_FIELD.DOCUMENT_ID, FORM_FIELD.FORM_NUMBER, FORM_FIELD.FORM_REVISION_DATE],
          },

          {
            columns: [FORM_FIELD.PRICE_TYPE, FORM_FIELD.COST],
          },
          {
            columns: [
              ...(isFleetEnabled || isFormsLibrary ? [FORM_FIELD.FLEET_ELIGIBILITY] : EMPTY_ARRAY),
              ...(isDealerTrackEnabled ? [FORM_FIELD.DOCUMENT_TYPE] : EMPTY_ARRAY),
            ],
          },
          {
            columns: [FORM_FIELD.STATES, FORM_FIELD.OEMS],
          },
          {
            columns: [FORM_FIELD.LENDER, FORM_FIELD.STATE_DEALER_ASSOCIATIONS],
          },
          {
            columns: [FORM_FIELD.FNI_PRODUCT_PROVIDER, FORM_FIELD.FORM_TYPES],
          },
        ],
      },
      {
        className: `full-width ${styles.formContainer}`,
        header: { label: __('Forms Printing'), size: 3 },
        rows: [
          {
            columns: [FORM_FIELD.PRINTER, FORM_FIELD.PRINT_SEQUENCE, FORM_FIELD.NUMBER_OF_COPIES],
          },
          {
            columns: [
              FORM_FIELD.DUPLEX,
              ...(!isFormsLibrary && userPersona === INTERNAL_USER_PERSONAS_MAPPING.FORMS
                ? [FORM_FIELD.CALCULATION_ENGINE]
                : EMPTY_ARRAY),
            ],
          },
        ],
      },
      {
        className: `full-width ${styles.formContainer}`,
        header: { label: __('Print Offset (in mm)'), size: 3 },
        subHeader: { label: __('Update relative print position.'), className: styles.subHeaderLabel },
        rows: [
          {
            columns: [FORM_FIELD.OFFSET_X, FORM_FIELD.OFFSET_Y],
          },
        ],
      },
      {
        className: `full-width ${styles.formContainer}`,
        header: { label: __('Form License Fields'), size: 3 },
        rows: [
          {
            columns: [FORM_FIELD.LICENSED_BY, FORM_FIELD.LICENSED_PREFERED_NAME, FORM_FIELD.LICENSE_KEY],
          },
          {
            columns: [FORM_FIELD.LICENSED_FOR, ...(isFormsLibrary ? [FORM_FIELD.ENTERPRISE] : EMPTY_ARRAY)],
          },
        ],
      },
      {
        className: `full-width ${styles.formContainer}`,
        header: { label: __('Forms Signing'), size: 3 },
        rows: [
          {
            columns: [FORM_FIELD.MANDATORY_SIGNATURE_TYPE],
          },
        ],
      },
    ];
  } else {
    return [
      {
        className: `full-width ${styles.formContainer}`,
        header: { label: __('General'), size: 3 },
        rows: [
          {
            columns: [FORM_FIELD.FORM_NAME],
          },
          {
            columns: [FORM_FIELD.FORM_CATEGORY, FORM_FIELD.EFFECTIVE_DATE],
          },
          {
            columns: [FORM_FIELD.EXPIRY_DATE, FORM_FIELD.DEPARTMENT],
          },
          {
            columns: [FORM_FIELD.FORM_REVISION_DATE],
          },
          {
            columns: [FORM_FIELD.STATES, FORM_FIELD.OEMS],
          },
          {
            columns: [FORM_FIELD.LENDER, FORM_FIELD.STATE_DEALER_ASSOCIATIONS],
          },
          {
            columns: [FORM_FIELD.FNI_PRODUCT_PROVIDER, ...(!isFCLiteViewFlow ? [FORM_FIELD.FORM_TYPES] : EMPTY_ARRAY)],
          },
        ],
      },
      {
        className: `full-width ${styles.formContainer}`,
        header: { label: __('Print Offset (in mm)'), size: 3 },
        subHeader: { label: __('Update relative print position.'), className: styles.subHeaderLabel },
        rows: [
          {
            columns: [FORM_FIELD.OFFSET_X, FORM_FIELD.OFFSET_Y],
          },
        ],
      },
      {
        className: `full-width ${styles.formContainer}`,
        header: { label: __('Forms Printing'), size: 3 },
        rows: [
          {
            columns: [FORM_FIELD.PRINTER, FORM_FIELD.PRINT_SEQUENCE, FORM_FIELD.NUMBER_OF_COPIES],
          },
          {
            columns: [FORM_FIELD.DUPLEX],
          },
        ],
      },
      {
        className: `full-width ${styles.formContainer}`,
        header: { label: __('Form License Fields'), size: 3 },
        rows: [
          {
            columns: [FORM_FIELD.LICENSED_BY, FORM_FIELD.LICENSED_PREFERED_NAME, FORM_FIELD.LICENSE_KEY],
          },
          {
            columns: [FORM_FIELD.LICENSED_FOR],
          },
        ],
      },
      {
        className: `full-width ${styles.formContainer}`,
        header: { label: __('Forms Signing'), size: 3 },
        rows: [
          {
            columns: [FORM_FIELD.MANDATORY_SIGNATURE_TYPE],
          },
        ],
      },
    ];
  }
};

export const getFormFields = ({
  printerTypes,
  printerType,
  formPrinterType,
  effectiveDate,
  expiryDate,
  formCategories,
  formCategory,
  licensedByKeys,
  formNameValue,
  requiredFields,
  isGlobalForm,
  docTypes,
  disableCalcEngine,
  hasEContractingEditPermission,
  isPDFLibraryForm,
  isFormsLibrary,
  editType,
  globalMetaData,
  renderGroupLabel,
  refreshFormTag,
}) => {
  const disableField = disableFormSetupField(isFormsLibrary, editType);
  return {
    [FORM_FIELD.FORM_NAME]: {
      renderer: TextInput,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.FORM_NAME],
        disabled: isFormsLibrary && !!formNameValue,
        required: requiredFields.includes(FORM_FIELD.FORM_NAME),
      },
    },
    [FORM_FIELD.CODE]: {
      renderer: TextInput,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.CODE],
        disabled: isFormsLibrary && disableField,
      },
    },
    [FORM_FIELD.PRINTER]: {
      renderer: Select,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.PRINTER],
        options: getPrinterTypesOptions(printerTypes, formPrinterType),
        required: requiredFields.includes(FORM_FIELD.PRINTER),
        disabled: disableField || isPDFLibraryForm,
      },
    },
    [FORM_FIELD.EFFECTIVE_DATE]: {
      renderer: InputDatePickerField,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.EFFECTIVE_DATE],
        dateFormat: DATE_TIME_FORMATS.ABBREVIATED_NUMERIC_DATE,
        validators: [shouldBeBefore(expiryDate)],
        required: requiredFields.includes(FORM_FIELD.EFFECTIVE_DATE),
        disabled: disableField,
      },
    },
    [FORM_FIELD.EXPIRY_DATE]: {
      renderer: InputDatePickerField,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.EXPIRY_DATE],
        dateFormat: DATE_TIME_FORMATS.ABBREVIATED_NUMERIC_DATE,
        disabledDate: current => current < effectiveDate,
        validators: [shouldBeAfter(effectiveDate)],
        required: requiredFields.includes(FORM_FIELD.EXPIRY_DATE),
        disabled: disableField,
      },
    },
    [FORM_FIELD.OFFSET_X]: {
      renderer: NumberInputWithSignToggle,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.OFFSET_X],
        addonAfter: 'mm',
        radios: OFFSET_X_RADIOS,
        parser: value => {
          const parsed = value.replace(/[^\d.]/g, '').replace(/^(\d*\.\d*).*$/, '$1');
          return parsed;
        },
      },
    },
    [FORM_FIELD.OFFSET_Y]: {
      renderer: NumberInputWithSignToggle,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.OFFSET_Y],
        addonAfter: 'mm',
        radios: OFFSET_Y_RADIOS,
        parser: value => {
          const parsed = value.replace(/[^\d.]/g, '').replace(/^(\d*\.\d*).*$/, '$1');
          return parsed;
        },
      },
    },
    [FORM_FIELD.DEPARTMENT]: {
      renderer: Select,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.DEPARTMENT],
        options: DEPARTMENTS_OPTIONS,
        required: requiredFields.includes(FORM_FIELD.DEPARTMENT),
      },
    },
    [FORM_FIELD.LICENSED_BY]: {
      renderer: Select,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.LICENSED_BY],
        placeholder: __('Select License'),
        options: standardFieldOptionMapper('FORM_LICENSE_PROVIDER', _values(licensedByKeys)),
        disabled: disableField || isPDFLibraryForm,
      },
    },

    [FORM_FIELD.LICENSED_PREFERED_NAME]: {
      renderer: TextInput,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.LICENSED_PREFERED_NAME],
        disabled: disableField || isPDFLibraryForm,
      },
    },

    [FORM_FIELD.LICENSE_KEY]: {
      renderer: TextInput,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.LICENSE_KEY],
        disabled: disableField || isPDFLibraryForm,
      },
    },

    [FORM_FIELD.LICENSED_FOR]: {
      renderer: MultiSelectField,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.LICENSED_FOR],
        options: LICENSED_FOR_OPTIONS,
        disabled: disableField || isPDFLibraryForm,
      },
    },

    [FORM_FIELD.DUPLEX]: {
      renderer: CheckBox,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.DUPLEX],
        disabled: _includes([PRINTER_TYPES.DOT_MATRIX, PRINTER_TYPES.LABEL], printerType),
      },
    },
    [FORM_FIELD.NUMBER_OF_COPIES]: {
      renderer: NumberInputField,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.NUMBER_OF_COPIES],
        min: 0,
        precision: 0,
      },
    },
    [FORM_FIELD.MANDATORY_SIGNATURE_TYPE]: {
      renderer: Select,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.MANDATORY_SIGNATURE_TYPE],
        allowClear: true,
        options: SIGNING_TYPE_OPTIONS,
        disabled: disableFormSetupBasedOnField({
          field: FORM_FIELD.MANDATORY_SIGNATURE_TYPE,
          disableField,
          isFormsLibrary,
          editType,
        }),
      },
    },
    [FORM_FIELD.CALCULATION_ENGINE]: {
      renderer: Select,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.CALCULATION_ENGINE],
        allowClear: true,
        options: CALCULATION_ENGINE_OPTIONS,
        disabled: disableField || disableCalcEngine,
        showAuxiliaryError: false,
      },
    },
    [FORM_FIELD.DOCUMENT_TYPE]: {
      renderer: Select,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.DOCUMENT_TYPE],
        allowClear: true,
        helpText: __('Required for DealerTrack eSigning'),
        infoContentClassName: styles.docTypeHelpText,
        labelClassName: styles.docTypeLabel,
        options: formattedSelectFormsList(docTypes),
        containerClassName: styles.docTypeContainer,
      },
    },
    [FORM_FIELD.FLEET_ELIGIBILITY]: {
      renderer: CheckBox,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.FLEET_ELIGIBILITY],
        helpText: __('These are forms expected to be tracked at fleet level'),
        infoContentClassName: styles.iconContent,
        infoIconClassName: styles.infoIcon,
        disabled: isGlobalForm,
      },
    },
    [FORM_FIELD.PRINT_SEQUENCE]: {
      renderer: NumberInputField,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.PRINT_SEQUENCE],
        min: 0,
        triggerChangeOnBlur: false,
      },
    },
    [FORM_FIELD.FORM_CATEGORY]: {
      renderer: SelectInput,
      renderOptions: getFormCategoryRenderOptions(
        formCategories,
        formCategory,
        hasEContractingEditPermission,
        isFormsLibrary,
        editType
      ),
    },

    [FORM_FIELD.PRICE_TYPE]: {
      renderer: Select,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.PRICE_TYPE],
        placeholder: __('Select Price Type'),
        options: PRICE_TYPE_OPTIONS,
        disabled: disableField,
      },
    },
    [FORM_FIELD.COST]: {
      renderer: NumberInputField,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.COST],
        disabled: disableField,
      },
    },
    [FORM_FIELD.FORM_REVISION_DATE]: {
      renderer: DatePickerWrapper,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.FORM_REVISION_DATE],
        disabled: disableField,
      },
    },
    [FORM_FIELD.FORM_NUMBER]: {
      renderer: TextInput,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.FORM_NUMBER],
        disabled: disableField,
      },
    },
    [FORM_FIELD.DOCUMENT_NAME]: {
      renderer: TextInput,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.DOCUMENT_NAME],
        disabled: disableField,
      },
    },
    [FORM_FIELD.DOCUMENT_ID]: {
      renderer: TextInput,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.DOCUMENT_ID],
        disabled: disableField,
      },
    },
    [FORM_FIELD.STATES]: {
      renderer: MultiSelectField,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.STATES],
        options: formattedStatesListResponse(
          _get(globalMetaData, FORM_FIELD.COUNTRIES),
          _get(globalMetaData, FORM_FIELD.STATES)
        ),
        styles: base => ({
          ...base,
          ':not(:last-child)': {
            borderBottom: `0.1rem solid ${COLORS.platinum}`,
          },
        }),
        additionalOverrideProps: { overrideWithBaseSelectStyles: true },
        dropDownClassName: styles.dropDownClassName,
        formatGroupLabel: renderGroupLabel,
        disabled: disableField,
      },
    },
    [FORM_FIELD.OEMS]: {
      renderer: MultiSelectField,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.OEMS],
        options: _get(globalMetaData, FORM_FIELD.OEMS, EMPTY_ARRAY),
        isWithSelectAll: true,
        disabled: disableField,
      },
    },
    [FORM_FIELD.LENDER]: {
      renderer: MultiSelectField,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.LENDER],
        options: _get(globalMetaData, FORM_FIELD.LENDER, EMPTY_ARRAY),
        isWithSelectAll: true,
        disabled: disableField,
      },
    },
    [FORM_FIELD.STATE_DEALER_ASSOCIATIONS]: {
      renderer: MultiSelectField,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.STATE_DEALER_ASSOCIATIONS],
        options: _get(globalMetaData, FORM_FIELD.STATE_DEALER_ASSOCIATIONS, EMPTY_ARRAY),
        isWithSelectAll: true,
        disabled: disableField,
      },
    },
    [FORM_FIELD.FNI_PRODUCT_PROVIDER]: {
      renderer: MultiSelectField,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.FNI_PRODUCT_PROVIDER],
        options: _get(globalMetaData, FORM_FIELD.FNI_PRODUCT_PROVIDER, EMPTY_ARRAY),
        isWithSelectAll: true,
        disabled: disableField,
      },
    },
    [FORM_FIELD.FORM_TYPES]: {
      renderer: MultiSelectField,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.FORM_TYPES],
        options: _get(globalMetaData, FORM_FIELD.FORM_TYPES, EMPTY_ARRAY),
        isWithSelectAll: true,
        disabled: disableField,
      },
    },
    [FORM_FIELD.FORM_TAG]: {
      renderer: TextInput,
      renderOptions: {
        label: FORM_FIELD_LABELS[FORM_FIELD.FORM_TAG],
        addonBefore: <IconAsBtn onClick={refreshFormTag}>icon-refresh</IconAsBtn>,
        placeholder: '',
        readOnly: true,
      },
    },
    ...getAdditionalFormFields({ isFormsLibrary }),
  };
};

export const getAdditionalFormFields = ({ isFormsLibrary }) => {
  if (isFormsLibrary) {
    return {
      [FORM_FIELD.ENTERPRISE]: {
        renderer: MultiSelectField,
        renderOptions: {
          label: __('Enterprise'),
          options: ENTERPRISE_OPTIONS,
        },
      },
    };
  }
  return EMPTY_OBJECT;
};

export const eContractingFormSections = [
  {
    rows: [
      {
        columns: [E_CONTRACTING_FORM_FIELD.TYPE],
      },
      {
        columns: [E_CONTRACTING_FORM_FIELD.VAULTING_REQUIRED],
      },
      {
        columns: [E_CONTRACTING_FORM_FIELD.VAULTING_PARTNER, E_CONTRACTING_FORM_FIELD.AGENCY],
      },
      {
        columns: [E_CONTRACTING_FORM_FIELD.FORM_REVISION_DATE, E_CONTRACTING_FORM_FIELD.STATE],
      },
      {
        columns: [E_CONTRACTING_FORM_FIELD.PAYMENT_TYPE],
      },
    ],
  },
];

export const getEContractingFormFields = defaultMemoize(({ econtractForm, formValues }) => {
  const today = getToday();
  const type = _get(formValues, E_CONTRACTING_FORM_FIELD.TYPE, EMPTY_STRING);
  const isVaultingRequired = _get(formValues, E_CONTRACTING_FORM_FIELD.VAULTING_REQUIRED, false);
  const vaultingPartner = _get(formValues, E_CONTRACTING_FORM_FIELD.VAULTING_PARTNER, EMPTY_STRING);
  const dateRangeStart = subtractDays(90, today);
  const dateRangeEnd = addDays(90, today);
  const isFieldDisabled = type !== E_CONTRACT_FORM_TYPES.CONTRACT_FORM || !isVaultingRequired;

  return {
    [E_CONTRACTING_FORM_FIELD.TYPE]: {
      renderer: Select,
      renderOptions: {
        label: __('Type of eContract Form'),
        options: E_CONTRACT_FORM_TYPE_OPTIONS,
        required: isFieldRequired(
          E_CONTRACTING_FORM_FIELD.TYPE,
          type,
          isVaultingRequired,
          vaultingPartner,
          econtractForm
        ),
        fieldClassName: styles.selectFieldClassName,
      },
    },
    [E_CONTRACTING_FORM_FIELD.VAULTING_REQUIRED]: {
      renderer: CheckBox,
      renderOptions: {
        label: __('Vaulting Required'),
        disabled: type !== E_CONTRACT_FORM_TYPES.CONTRACT_FORM,
      },
    },
    [E_CONTRACTING_FORM_FIELD.VAULTING_PARTNER]: {
      renderer: Select,
      renderOptions: {
        label: __('Vaulting Partner'),
        options: VAULTING_PARTNER_OPTIONS,
        disabled: isFieldDisabled,
        required: isFieldRequired(
          E_CONTRACTING_FORM_FIELD.VAULTING_PARTNER,
          type,
          isVaultingRequired,
          vaultingPartner,
          econtractForm
        ),
      },
    },
    [E_CONTRACTING_FORM_FIELD.AGENCY]: {
      renderer: MultiSelectField,
      renderOptions: {
        label: __('Agency'),
        options: AGENCY_LIST_OPTIONS,
        required: isFieldRequired(
          E_CONTRACTING_FORM_FIELD.AGENCY,
          type,
          isVaultingRequired,
          vaultingPartner,
          econtractForm
        ),
        disabled: isFieldDisabled || vaultingPartner !== VAULTING_PARTNERS.ODE,
      },
    },
    [E_CONTRACTING_FORM_FIELD.FORM_REVISION_DATE]: {
      renderer: InputDatePickerField,
      renderOptions: {
        label: __('Form Revision Date'),
        dateFormat: DATE_TIME_FORMATS.ABBREVIATED_NUMERIC_DATE,
        disabled: isFieldDisabled,
        disabledDate: date => isBefore(dateRangeStart, date) || isAfter(dateRangeEnd, date),
        required: isFieldRequired(
          E_CONTRACTING_FORM_FIELD.FORM_REVISION_DATE,
          type,
          isVaultingRequired,
          vaultingPartner,
          econtractForm
        ),
      },
    },
    [E_CONTRACTING_FORM_FIELD.STATE]: {
      renderer: MultiSelectField,
      renderOptions: {
        label: __('State'),
        options: STATES,
        disabled: isFieldDisabled,
        required: isFieldRequired(
          E_CONTRACTING_FORM_FIELD.STATE,
          type,
          isVaultingRequired,
          vaultingPartner,
          econtractForm
        ),
      },
    },
    [E_CONTRACTING_FORM_FIELD.PAYMENT_TYPE]: {
      renderer: MultiSelectField,
      renderOptions: {
        label: __('Payment Type'),
        options: PAYMENT_TYPE_OPTIONS,
        disabled: isFieldDisabled,
        required: isFieldRequired(
          E_CONTRACTING_FORM_FIELD.PAYMENT_TYPE,
          type,
          isVaultingRequired,
          vaultingPartner,
          econtractForm
        ),
        fieldClassName: styles.selectFieldClassName,
      },
    },
  };
});

export const MODULE_TARGETTING_CONFIG = {
  [MODULE_TARGETTING_TYPES.CRM]: {
    key: `${MODULE_TARGETTING_TYPES.CRM}_TARGETTING`,
    title: FORM_FIELD_LABELS[FORM_FIELD.CRM_USAGE_TARGET_PARAMS],
    usageCategories: USAGE_CATEGORY_VS_MODULE[MODULE_TARGETTING_TYPES.CRM],
  },
  [MODULE_TARGETTING_TYPES.DESKING]: {
    key: `${MODULE_TARGETTING_TYPES.DESKING}_TARGETTING`,
    title: FORM_FIELD_LABELS[FORM_FIELD.USAGE_TARGET_PARAMS],
    usageCategories: USAGE_CATEGORY_VS_MODULE[MODULE_TARGETTING_TYPES.DESKING],
  },
  [MODULE_TARGETTING_TYPES.DIGITAL_RETAILING]: {
    key: `${MODULE_TARGETTING_TYPES.DIGITAL_RETAILING}_TARGETTING`,
    title: FORM_FIELD_LABELS[FORM_FIELD.DIGITAL_RETAILING_USAGE_TARGET_PARAMS],
    usageCategories: USAGE_CATEGORY_VS_MODULE[MODULE_TARGETTING_TYPES.DIGITAL_RETAILING],
  },
  [MODULE_TARGETTING_TYPES.ACCOUNTING]: {
    key: `${MODULE_TARGETTING_TYPES.ACCOUNTING}_TARGETTING`,
    title: FORM_FIELD_LABELS[FORM_FIELD.ACCOUNTING_USAGE_TARGET_PARAMS],
    usageCategories: USAGE_CATEGORY_VS_MODULE[MODULE_TARGETTING_TYPES.ACCOUNTING],
  },
};
