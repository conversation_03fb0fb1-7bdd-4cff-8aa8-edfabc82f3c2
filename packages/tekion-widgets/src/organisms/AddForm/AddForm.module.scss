@import "tstyles/component.scss";
@import "tcomponents/organisms/FormBuilder/components/fieldLayout/fieldLayout.module.scss";

.leftSideContent {
  overflow-y: auto;
  width: 50%;
  margin-top: 4rem;
  padding: 0 4rem;
}
.pdfContent {
  width: 50%;
  border-left: 1px solid $platinum;
  padding: 0.8rem;
}

.formLabel {
  display: inline-block;
  padding: 0 1.2rem;
  background: $glitter;
  border-radius: 1rem;
  font-weight: 600;
}

.addFormModalStyles {
  margin: 10rem 0rem;
}

.documentWrapper {
  width: 50%;
  padding: 2rem 0rem;
}

.formWrapper {
  width: 50%;
  padding-top: 1.6rem;
  padding: 2rem;
}

.addFormComponentWrapper {
  @include flex();
  @include full-width;
  height: 70vh;
}

.tooltipMainStyle > div > div:first-child {
  border-top-color: $white;
}

.tooltipMainStyle > div > div:last-child {
  background-color: $white;
  padding: 1.5rem;
}

.tooltipContent {
  background-color: $white;
  color: $black;
}

.tooltipContent > p {
  line-height: 1.7rem;
  margin-bottom: 0;
  margin-top: 0.2rem;
}

.tooltipContentHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tooltipTitle {
  font-weight: bold;
}

.tooltipCross {
  cursor: pointer;
}

.tooltipActions {
  display: flex;
  margin-top: 0.5rem;
  justify-content: flex-end;
}

.formDisabled {
  opacity: 0.5;
  cursor: not-allowed;
  & > div {
    pointer-events: none;
  }
}

.licensedBy {
  flex: 0 1 auto;
  width: 31rem;
  margin-bottom: 2.4rem;
  margin-left: 1rem;
}

.sellingPriceUsageRuleContainer {
  display: flex;
  justify-content: space-between;
}

.sellingPriceInput {
  width: 48%;
}

.selectContainer {
  border: 0.1rem solid $ashGray;
  @include full-width;
}

.copyFormName {
  margin-bottom: 1rem;
}

.collapseBodyClassName {
  padding: 0;
  border-left: 0;
  margin-left: 0;
}

.collapseArrowClassName {
  padding: 1.6rem 1rem 1.6rem 0 !important;
}

.collapseHeaderClassName {
  padding: 0.8rem 0 !important;
}

.addTargettingButton {
  margin-top: 1.6rem;
}

.labelChip {
  width: fit-content;
  background: $glitter;
  border-radius: 1.2rem;
  padding: 0 0.8rem;
}

.moreLabelsCount {
  color: $denim;
  font-weight: 600;
}

.moreLabelsPopover {
  padding: 0.8rem;
  width: 15rem;
  max-height: 25rem;
  overflow: scroll;
}

.addRule {
  margin-bottom: 15rem;
}

.disabledClick {
  pointer-events: none;
}

.selectFieldClassName {
  flex-grow: 0.5;
  margin-right: $form-field-right-spacing !important;
}

.rulesTableClassName {
  z-index: unset;
}

.iconContent {
  padding: 0 1rem;
}

.infoIcon {
  margin-left: -1.5rem;
  font-size: $font-normal;
}

.digiCertContainer {
  padding: 2.4rem;
}

.docTypeHelpText {
  padding: 0.2rem 0.8rem;
}

.docTypeLabel {
  margin-right: 15.6rem;
}

.docTypeContainer {
  width: 29.7rem;
}

.formCategory {
  :first-child {
    border-width: 0.01rem;
  }
}


.container {
  .anchorContainer {
    width: 24rem;
  }
}

.formContainer {
  max-width: 100%;
}
.savePromptTitle {
  font-weight: bold;
  margin-bottom: 0;
}

.inputBoxErrorClass {
  top: auto;
}

.infoIconClassName {
  color: $dodgerBlueLight;
  margin-right: 1.2rem;
}

.iconWithTextWrapper {
  padding: 1.2rem 2.2rem 1.2rem 1.6rem;
  background-color: $aliceBlue;
  border-radius: 0.4rem;
  color: $atomic;
}

.dropDownClassName {
  margin-top: 0.8rem;
  width: 29.6rem;
  height: 3.2rem;
  max-width: 100%;
}

.subHeaderLabel {
  font-family: "Proxima-Nova-Regular";
  margin-top: -1.0rem;
}
