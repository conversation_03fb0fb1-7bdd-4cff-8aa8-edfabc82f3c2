import _omit from 'lodash/omit';
import _values from 'lodash/values';
import {
  MANDATORY_SIGNATURE_TYPES,
  MANDATORY_SIGNATURE_TYPES_VS_LABELS,
} from '@tekion/tekion-base/constants/signature';
import {
  <PERSON>GE_CATEGORY as FORM_SETUP_USAGE_CATEGORY,
  USAGE_RULES as FORM_SETUP_USAGE_RULES,
  USAGE_CATEGORY_LABEL as FORM_SETUP_USAGE_CATEGORY_LABEL,
  USAGE_RULES_LABEL as FORM_SETUP_USAGE_RULES_LABEL,
  USAGE_CATEGORY_TREE as FORM_SETUP_USAGE_CATEGORY_TREE,
  USAGE_CATEGORIES_ONLY_FOR_PLACEHOLDERS,
} from '@tekion/tekion-base/constants/deal/formSetup';
import {
  USAGE_CATEGORY as FORM_CONFIGURATOR_USAGE_CATEGORY,
  USAGE_RULES as FORM_CONFIGURATOR_USAGE_RULES,
  USAGE_CATEGORY_LABEL as FORM_CONFIGURATOR_USAGE_CATEGORY_LABEL,
  USAGE_RULES_LABEL as FORM_CONFIGURATOR_USAGE_RULES_LABEL,
  USAGE_CATEGORY_TREE as FORM_CONFIGURATOR_USAGE_CATEGORY_TREE,
  MODULE_TARGETTING_TYPES,
} from '@tekion/tekion-base/constants/formConfigurator/constants';

export const FORM_FIELD = {
  FORM_ID: 'id',
  FORM_NAME: 'formName',
  USAGE_TARGET_PARAMS: 'usageTargetParams',
  CRM_USAGE_TARGET_PARAMS: 'crmUsageTargetParams',
  DIGITAL_RETAILING_USAGE_TARGET_PARAMS: 'digitalRetailingUsageTargetParams',
  ACCOUNTING_USAGE_TARGET_PARAMS: 'accountingUsageTargetParams',
  PROVIDER_RULE_PARAMS: 'providerRuleParams',
  ACC_RULE_PARAMS: 'accRuleParams',
  CERTIFIED_RULE_PARAM: 'certifiedRuleParam',
  OPERATOR: 'operator',
  CUSTOMTER_TYPE: 'customerType',
  MAKES: 'makes',
  USAGE_RULES: 'usageRules',
  USAGE_CATEGORY: 'usageCategory',
  EFFECTIVE_DATE: 'effectiveDate',
  EXPIRY_DATE: 'expiryDate',
  FORM_KEY: 'formKey',
  CODE: 'code',
  PRODUCT_ID: 'productId',
  PROVIDER_ID: 'providerId',
  OFFSET_X: 'offsetX',
  OFFSET_Y: 'offsetY',
  DEPARTMENT: 'department',
  USAGE_DESCRIPTION: 'usageRuleDescription',
  ACCESSORY_CODE: 'accCode',
  DUPLEX: 'duplex',
  WET_SIGNATURE: 'wetSignature',
  MANDATORY_SIGNATURE_TYPE: 'mandatorySigningType',
  NUMBER_OF_COPIES: 'numberOfCopies',
  FROM_PEN: 'fromPen',
  PRINT_SEQUENCE: 'printSequence',
  FORM_CATEGORY: 'formCategory',
  FORM_LABELS: 'labels',
  PRICE_TYPE: 'priceType',
  COST: 'cost',
  FORM_NUMBER: 'formNumber',
  FORM_REVISION_DATE: 'formRevisionDate',
  DOCUMENT_NAME: 'externalDocumentName',
  DOCUMENT_ID: 'externalDocumentId',
  TARGETED_MODULES: 'targetedModules',
  MODULE_VISIBILITY_RULES: 'moduleVisibilityRules',
  FLEET_ELIGIBILITY: 'eligibleForFleet',
  CALCULATION_ENGINE: 'calculationEngine',
  DOCUMENT_TYPE: 'docType',
  LICENSED_BY: 'licensedBy',
  LICENSED_PREFERED_NAME: 'licensorPreferredFormName',
  LICENSE_KEY: 'licenseKey',
  LICENSED_FOR: 'licensedFor',
  PRINTER: 'printerType',
  USAGE_CATEGORY_LABEL: 'usageCategoryLabel',
  TARGETED_MODULES_LABEL: 'targetedModulesLabel',
  WORK_FLOW_TARGETING_RULES_MAP: 'moduleWorkflowTargetingRulesMap',
  COUNTRIES: 'countries',
  STATES: 'states',
  OEMS: 'oems',
  LENDER: 'lenders',
  STATE_DEALER_ASSOCIATIONS: 'stateDealerAssociations',
  FNI_PRODUCT_PROVIDER: 'fniProducts',
  FORM_TYPES: 'formTypes',
  FORM_TAG: 'formTag',
  ENTERPRISE: 'enterprise',
  OEM_DETAILS: 'oemDetails',
};

export const FORM_FIELD_LABELS = {
  [FORM_FIELD.FORM_ID]: 'ID',
  [FORM_FIELD.FORM_NAME]: __('Form Name'),
  [FORM_FIELD.USAGE_TARGET_PARAMS]: __('Deals Targetting'),
  [FORM_FIELD.CRM_USAGE_TARGET_PARAMS]: __('CRM Targetting'),
  [FORM_FIELD.DIGITAL_RETAILING_USAGE_TARGET_PARAMS]: __('Digital Retailing Targetting'),
  [FORM_FIELD.ACCOUNTING_USAGE_TARGET_PARAMS]: __('Accounting Targetting'),
  [FORM_FIELD.EFFECTIVE_DATE]: __('Effective Date'),
  [FORM_FIELD.EXPIRY_DATE]: __('Expiry Date'),
  [FORM_FIELD.FORM_KEY]: __('Form Key'),
  [FORM_FIELD.CODE]: __('Code'),
  [FORM_FIELD.OFFSET_X]: __('Print Offset (Left / Right)'),
  [FORM_FIELD.OFFSET_Y]: __('Print Offset ( Up / Down )'),
  [FORM_FIELD.DEPARTMENT]: __('Department'),
  [FORM_FIELD.DUPLEX]: __('Double Sided Print'),
  [FORM_FIELD.MANDATORY_SIGNATURE_TYPE]: __('Eligible Signing Method'),
  [FORM_FIELD.NUMBER_OF_COPIES]: __('Number of Copies'),
  [FORM_FIELD.PRINT_SEQUENCE]: __('Print Order'),
  [FORM_FIELD.FORM_CATEGORY]: __('Category'),
  [FORM_FIELD.FORM_LABELS]: __('Labels'),
  [FORM_FIELD.PRICE_TYPE]: __('Price Type'),
  [FORM_FIELD.COST]: __('Cost'),
  [FORM_FIELD.FORM_NUMBER]: __('Form Number'),
  [FORM_FIELD.FORM_REVISION_DATE]: __('Form Revision Date'),
  [FORM_FIELD.DOCUMENT_NAME]: __('Document Name'),
  [FORM_FIELD.DOCUMENT_ID]: __('Document ID'),
  [FORM_FIELD.FLEET_ELIGIBILITY]: __('Eligible for Fleet'),
  [FORM_FIELD.CALCULATION_ENGINE]: __('Calculation Engine'),
  [FORM_FIELD.DOCUMENT_TYPE]: __('Document Type'),
  [FORM_FIELD.LICENSED_BY]: __('Licensed By'),
  [FORM_FIELD.LICENSED_PREFERED_NAME]: __('Form License Name'),
  [FORM_FIELD.LICENSE_KEY]: __(' License Key'),
  [FORM_FIELD.LICENSED_FOR]: __(' Licensed For'),
  [FORM_FIELD.PRINTER]: __('Document Format'),
  [FORM_FIELD.TARGETED_MODULES_LABEL]: __('Targeted Module(s)'),
  [FORM_FIELD.WORK_FLOW_TARGETING_RULES_MAP]: __('Worflow Targeting'),
  [FORM_FIELD.COUNTRIES]: __('Countries'),
  [FORM_FIELD.STATES]: __('States'),
  [FORM_FIELD.OEMS]: __('OEM'),
  [FORM_FIELD.LENDER]: __('Lender / Captive'),
  [FORM_FIELD.STATE_DEALER_ASSOCIATIONS]: __('State Dealer Association'),
  [FORM_FIELD.FNI_PRODUCT_PROVIDER]: __('F&I Product Provider'),
  [FORM_FIELD.FORM_TYPES]: __('Form Type'),
  [FORM_FIELD.FORM_TAG]: __('Form Id'),
};

export const PDF_LIBRARY_FIELDS = [
  FORM_FIELD.LICENSED_BY,
  FORM_FIELD.LICENSED_PREFERED_NAME,
  FORM_FIELD.LICENSE_KEY,
  FORM_FIELD.LICENSED_FOR,
  FORM_FIELD.PRINTER,
];

export const REQUIRED_FORM_FIELDS = [
  FORM_FIELD.FORM_NAME,
  FORM_FIELD.PRINTER,
  FORM_FIELD.EFFECTIVE_DATE,
  FORM_FIELD.EXPIRY_DATE,
  FORM_FIELD.DEPARTMENT,
  FORM_FIELD.TARGETED_MODULES,
];

export const USAGE_RULES = {
  ...FORM_SETUP_USAGE_RULES,
  ...FORM_CONFIGURATOR_USAGE_RULES,
};

export const USAGE_CATEGORY = {
  ...FORM_SETUP_USAGE_CATEGORY,
  ...FORM_CONFIGURATOR_USAGE_CATEGORY,
};

export const USAGE_CATEGORY_LABEL = {
  ...FORM_SETUP_USAGE_CATEGORY_LABEL,
  ...FORM_CONFIGURATOR_USAGE_CATEGORY_LABEL,
};

export const USAGE_RULES_LABEL = {
  ...FORM_SETUP_USAGE_RULES_LABEL,
  ...FORM_CONFIGURATOR_USAGE_RULES_LABEL,
};

export const USAGE_CATEGORY_TREE = {
  ...FORM_SETUP_USAGE_CATEGORY_TREE,
  ...FORM_CONFIGURATOR_USAGE_CATEGORY_TREE,
};

export const DEPARTMENTS = {
  ACCOUNTING: 'ACCOUNTING',
  PARTS: 'PARTS',
  SALES: 'SALES',
  SERVICES: 'SERVICES',
};

export const DEPARTMENTS_DISPLAY = {
  [DEPARTMENTS.ACCOUNTING]: __('Accounting'),
  [DEPARTMENTS.PARTS]: __('Parts'),
  [DEPARTMENTS.SALES]: __('Sales'),
  [DEPARTMENTS.SERVICES]: __('Services'),
};

export const DEPARTMENTS_OPTIONS = [
  { label: DEPARTMENTS_DISPLAY[DEPARTMENTS.ACCOUNTING], value: DEPARTMENTS.ACCOUNTING },
  { label: DEPARTMENTS_DISPLAY[DEPARTMENTS.PARTS], value: DEPARTMENTS.PARTS },
  { label: DEPARTMENTS_DISPLAY[DEPARTMENTS.SALES], value: DEPARTMENTS.SALES },
  { label: DEPARTMENTS_DISPLAY[DEPARTMENTS.SERVICES], value: DEPARTMENTS.SERVICES },
];

export const DYNAMIC_USAGE_CATEGORIES = [
  USAGE_CATEGORY.FNI,
  USAGE_CATEGORY.DUE_BILLS,
  USAGE_CATEGORY.STATE,
  USAGE_CATEGORY.MAKE,
  USAGE_CATEGORY.VEHICLE_FUEL_TYPE,
  USAGE_CATEGORY.LENDER,
];
export const SELECT_TAG_CATEGORIES = [USAGE_CATEGORY.VEHICLE_YEAR];

export const SELECT_TAG_TYPES = {
  [USAGE_CATEGORY.VEHICLE_YEAR]: Number.parseInt,
};

export const NO_RULE_USAGE_CATEGORIES = [USAGE_CATEGORY.NONE, USAGE_CATEGORY.ALWAYS];

export const ERROR_MESSAGES = {
  [FORM_FIELD.PRINT_SEQUENCE]: __('This sequence number is associated with another form'),
  [FORM_FIELD.CALCULATION_ENGINE]: __('Selected calculation engine is not supported'),
};

export const DEALER_CERTIFIED_STATUS = [
  {
    label: __('Dealer Certified'),
    value: 'DEALER_CERTIFIED',
  },
  {
    label: __('Dealer Not Certified'),
    value: 'DEALER_NOT_CERTIFIED',
  },
];

export const OEM_CERTIFIED_STATUS = [
  {
    label: __('OEM Certified'),
    value: 'OEM_CERTIFIED',
  },
  {
    label: __('OEM Not Certified'),
    value: 'OEM_NOT_CERTIFIED',
  },
];

const SALESPERSONS = {
  SALESPERSON_1: 'SALESPERSON_1',
  SALESPERSON_2: 'SALESPERSON_2',
};

export const SALESPERSON_OPTIONS = [
  {
    label: __('Salesperson 1'),
    value: SALESPERSONS.SALESPERSON_1,
  },
  {
    label: __('Salesperson 2'),
    value: SALESPERSONS.SALESPERSON_2,
  },
];

export const PRICE_TYPE_OPTIONS = [
  { label: __('Deal level'), value: 'DEAL_LEVEL' },
  { label: __('Form level'), value: 'FORM_LEVEL' },
];

export const ERRORS = {
  SAME_FILE_NAME: __('File Name already exists'),
  INVALID_FILE_NAME: __(
    'Please rename the file. A file name can only contain alphabets, numbers, -, _ and cannot end with tmp, v1, copy etc.'
  ),
  UPDATING_FORM_NAME: __('Error updating form name.'),
};

export const REGEX_FOR_VALID_FORMNAME =
  /^(?!.*(tmp|tmp_|tmp-|v1|v1_|v1-|copy|copy_|copy-|copy\d+-|copy\d+_|copy\d+)$)[a-zA-Z0-9_-\s]+$/i;

export const SIGNING_TYPE_OPTIONS = [
  {
    label: MANDATORY_SIGNATURE_TYPES_VS_LABELS[MANDATORY_SIGNATURE_TYPES.WET_SIGN],
    value: MANDATORY_SIGNATURE_TYPES.WET_SIGN,
  },
  {
    label: MANDATORY_SIGNATURE_TYPES_VS_LABELS[MANDATORY_SIGNATURE_TYPES.E_SIGN],
    value: MANDATORY_SIGNATURE_TYPES.E_SIGN,
  },
];

export const USAGE_TARGETTING_KEY = 'USAGE_TARGETTING_KEY';
export const E_CONTRACT_FORM_PANEL_KEY = 'E_CONTRACT_FORM_PANEL_KEY';

export const MODULE_TARGETING_DEFAULT_OPTIONS = [MODULE_TARGETTING_TYPES.DESKING];

export const USAGE_CATEGORY_VS_MODULE = {
  [MODULE_TARGETTING_TYPES.CRM]: [
    USAGE_CATEGORY.CO_BUYER,
    USAGE_CATEGORY.TRADE_INS,
    USAGE_CATEGORY.TRADE_IN_PAY_OFF,
    USAGE_CATEGORY.MODEL,
    USAGE_CATEGORY.MAKE,
    USAGE_CATEGORY.DEAL_TYPE,
    USAGE_CATEGORY.VEHICLE_YEAR,
    USAGE_CATEGORY.ALWAYS,
    USAGE_CATEGORY.CUSTOMER_TYPE,
    USAGE_CATEGORY.SERVICE_INCLUSIVE_COVERAGE_TYPE,
    USAGE_CATEGORY.APPOINTMENT_TYPE,
    USAGE_CATEGORY.VEHICLE,
  ],
  [MODULE_TARGETTING_TYPES.DESKING]: _values(
    _omit(USAGE_CATEGORY, ['APPOINTMENT_TYPE', ...USAGE_CATEGORIES_ONLY_FOR_PLACEHOLDERS])
  ),
  [MODULE_TARGETTING_TYPES.DIGITAL_RETAILING]: _values(
    _omit(USAGE_CATEGORY, ['APPOINTMENT_TYPE', 'FORM_TYPE', 'DEAL_STATUS'])
  ),
  [MODULE_TARGETTING_TYPES.ACCOUNTING]: [USAGE_CATEGORY.FORM_TYPE],
};

export const SELECT_OPTION_ACTIONS = {
  SELECT_OPTION: 'select-option',
  DESELECT_OPTION: 'deselect-option',
};

export const DIGICERT_HELPER_TEXT = __(
  'Tekion eSigning solution will be used by default (toggle disabled). To use Digicert encryption, enable the toggle'
);

export const DIGICERT_ENCRYPTION_TYPES = ['QES', 'ADES'];

const SIGNS = {
  POSITIVE: 'POSITIVE',
  NEGATIVE: 'NEGATIVE',
};

export const OFFSET_X_RADIOS = [
  {
    label: __('Left'),
    value: SIGNS.NEGATIVE,
  },
  {
    label: __('Right'),
    value: SIGNS.POSITIVE,
  },
];

export const OFFSET_Y_RADIOS = [
  {
    label: __('Up'),
    value: SIGNS.POSITIVE,
  },
  {
    label: __('Down'),
    value: SIGNS.NEGATIVE,
  },
];
