import React, { useEffect, useRef, useImperativeHandle, forwardRef } from 'react';
import PropTypes from 'prop-types';

import Tracker from '@openreplay/tracker';
import trackerAssist from '@openreplay/tracker-assist';

import _get from 'lodash/get';
import _find from 'lodash/find';
import _isFunction from 'lodash/isFunction';
import _isNil from 'lodash/isNil';
import _noop from 'lodash/noop';

import UserReader from '@tekion/tekion-base/readers/User';
import useFetchData from '@tekion/tekion-base/customHooks/useFetchData';

import { fetchGlobalPropertiesForThirdParty } from '@tekion/tekion-business/src/services/globalPropertyService';
import { isGlobalPropertyEnabled } from '@tekion/tekion-business/src/helpers/globalPropertyHelpers';
import { THIRD_PARTY_PROPERTIES } from '@tekion/tekion-business/src/constants/globalPropertyConstants';

import variables from '@tekion/tekion-styles-next/scss/exports.scss';

import TEnvReader from '@tekion/tekion-base/readers/Env';
import { getEnvironmentVariables } from '@tekion/tekion-base/helpers/envHelper';
import { sendCustomEvent } from './openreplay.utils';
import OpenreplayShortcut from './OpenreplayShortcut';

const isOpenReplayEnvVariableSet = () =>
  !(
    _isNil(TEnvReader.openReplayProjectKey(getEnvironmentVariables())) ||
    _isNil(TEnvReader.openReplayProjectId(getEnvironmentVariables())) ||
    _isNil(TEnvReader.openReplayOrgUrl(getEnvironmentVariables()))
  );

// TODO move to imperative ref
const getSessionInfo = () => {
  const openreplayHostFunc = _get(window, '__OPENREPLAY__.app.getHost');
  const openreplaySessionIDFunc = _get(window, '__OPENREPLAY__.app.getSessionID');
  if (_isFunction(openreplaySessionIDFunc) && _isFunction(openreplayHostFunc)) {
    return {
      sessionId: window.__OPENREPLAY__.app.getSessionID(),
      ssessionUrl: `${window.__OPENREPLAY__.app.getHost()}/${TEnvReader.openReplayProjectId(
        getEnvironmentVariables()
      )}/session/${window.__OPENREPLAY__.app.getSessionID()}`,
    };
  }
  return {};
};

const updateLiveAssistStatus = ({ status, email, name, query } = {}) => {
  const func = _get(window, 'TAP_CHAT.updateLiveAssistStatus');
  if (_isFunction(func)) {
    try {
      const { sessionId, sessionUrl } = getSessionInfo();
      window.TAP_CHAT.updateLiveAssistStatus({
        sessionId,
        sessionUrl,
        status,
        email,
        name,
        query,
      });
    } catch (error) {
      console.error('Error updating live assist status ', error);
    }
  }
};

const getUserId = sessionData =>
  `${UserReader.name(sessionData)}_${UserReader.tenantName(sessionData)}__${UserReader.dealerId(sessionData)}`;

const getTrackingInfo = sessionData => ({
  Username: UserReader.name(sessionData),
  DealerId: UserReader.dealerId(sessionData),
  TenantName: UserReader.tenantName(sessionData),
  SiteId: _get(sessionData, 'tekSiteId'),
  Persona: UserReader.persona(sessionData),
  Email: UserReader.email(sessionData),
  DealerName: _get(
    _find(
      _get(sessionData, 'dealer'),
      dealerInfo => UserReader.dealerId(dealerInfo) === UserReader.dealerId(sessionData)
    ),
    'dealerDisplayName'
  ),
  UserId: UserReader.userId(sessionData),
});

const CONFIRM_REMOTE_CONTROL_PROMPT_STYLE = {
  color: variables.jetBlack,
  backgroundColor: variables.white,
  opacity: 1,
  width: '50rem',
};
const CONFIRM_BUTTON_OPTIONS = {
  innerHTML: __('Allow'),
  style: {
    color: variables.white,
    backgroundColor: variables.denim,
    border: '0.1rem solid',
    borderColor: variables.denim,
    borderRadius: '0.2rem',
    height: '3.2rem',
    padding: '0 1.6rem',
    width: 'unset',
    fontSize: '1.4rem',
    fontWeight: 'bold',
  },
};
const DECLINE_BUTTON_OPTIONS = {
  innerHTML: __('Deny'),
  style: {
    color: variables.jetBlack,
    backgroundColor: variables.white,
    border: '0.1rem solid',
    borderColor: variables.osloGray,
    borderRadius: '0.2rem',
    height: '3.2rem',
    padding: '0 1.6rem',
    width: 'unset',
    fontSize: '1.4rem',
    fontWeight: 'bold',
  },
};

function OpenReplay(props, ref) {
  const { sessionData, axiosInstance, openReplayProperties } = props;
  const thisRef = useRef({});
  const [dataResponse] = useFetchData(fetchGlobalPropertiesForThirdParty, []);
  const isOpenReplayEnabledForDealer = _get(openReplayProperties, 'ENABLED', true);
  const isCapturePayloadEnabledForDealer = _get(openReplayProperties, 'CAPTURE_PAYLOAD', false);
  const getCurrentSessionDetails = () => {
    const currentOpenReplayAppInstance = _get(thisRef.current, 'tracker.app');
    if (_isFunction(currentOpenReplayAppInstance?.getHost) && _isFunction(currentOpenReplayAppInstance?.getSessionID)) {
      return {
        host: currentOpenReplayAppInstance.getHost(),
        sessionId: currentOpenReplayAppInstance.getSessionID(),
      };
    }
    return null;
  };
  useImperativeHandle(ref, () => ({
    getCurrentSessionUrl: () => {
      const currentSessionDetails = getCurrentSessionDetails();
      if (_isNil(currentSessionDetails)) return null;

      const { host, sessionId } = currentSessionDetails;
      return `${host}/${TEnvReader.openReplayProjectId(getEnvironmentVariables())}/session/${sessionId}`;
    },
    getCurrentLiveSessionUrl: () => {
      const currentSessionDetails = getCurrentSessionDetails();
      if (_isNil(currentSessionDetails)) return null;

      const { host, sessionId } = currentSessionDetails;
      return `${host}/${TEnvReader.openReplayProjectId(getEnvironmentVariables())}/assist/${sessionId}`;
    },
  }));

  useEffect(() => {
    if (isOpenReplayEnvVariableSet()) {
      const { data: globalPropertiesForThirdParty, isLoading, hasError } = dataResponse;
      const isOpenreplayEnabled = isGlobalPropertyEnabled(
        globalPropertiesForThirdParty,
        THIRD_PARTY_PROPERTIES.OPEN_REPLAY
      );
      const isOpenreplayFetchCaptureEnabled = isGlobalPropertyEnabled(
        globalPropertiesForThirdParty,
        THIRD_PARTY_PROPERTIES.OPEN_REPLAY_FETCH_CAPTURE
      );
      if (!isOpenreplayEnabled || !isOpenReplayEnabledForDealer || isLoading || hasError) {
        return _noop;
      }
      if (!thisRef.current?.tracker) {
        thisRef.current.tracker = new Tracker({
          projectKey: TEnvReader.openReplayProjectKey(getEnvironmentVariables()),
          __DISABLE_SECURE_MODE: TEnvReader.nodeEnv(getEnvironmentVariables()) === 'development',
          ingestPoint: `${TEnvReader.openReplayOrgUrl(getEnvironmentVariables())}ingest`,
          capturePerformance: false,
          resetTabOnWindowOpen: true,
          userID: getUserId(sessionData),
          captureIFrames: false,
          defaultInputMode: 0,
          disableCanvas: true,
          disableClickMaps: true,
          disableStringDict: true,
          network: {
            failuresOnly: false,
            ignoreHeaders: ['tekion-api-token'],
            capturePayload: isOpenreplayFetchCaptureEnabled && isCapturePayloadEnabledForDealer,
          },
        });
        thisRef.current.tracker.use(
          trackerAssist({
            controlConfirm: {
              text: __('Do you want to give Remote Control to Tekion Support Agent?'),
              style: CONFIRM_REMOTE_CONTROL_PROMPT_STYLE,
              confirmBtn: CONFIRM_BUTTON_OPTIONS,
              declineBtn: DECLINE_BUTTON_OPTIONS,
            },
            callConfirm: __('Do you want to accept the call from Tekion Support Agent?'),
            onAgentConnect: ({ email, name, query } = {}) => {
              updateLiveAssistStatus({ status: 'CONNECTED', email, name, query });
              return () => updateLiveAssistStatus({ status: 'DISCONNECTED', email, name, query });
            },
            onRemoteControlStart: () => {
              sendCustomEvent('Remote Control Started');
              return () => sendCustomEvent('Remote Control Ended');
            },
          })
        );
      }

      const { tracker } = thisRef.current;
      tracker.start({
        userID: getUserId(sessionData),
        metadata: getTrackingInfo(sessionData),
      });
    }

    const currentRefValue = thisRef.current;

    return () => {
      if (isOpenReplayEnvVariableSet()) {
        const { tracker } = currentRefValue;
        if (_isFunction(tracker.stop)) {
          tracker.stop();
        }
      }
    };
  }, [sessionData, dataResponse, axiosInstance, isCapturePayloadEnabledForDealer, isOpenReplayEnabledForDealer]);

  return <OpenreplayShortcut />;
}

OpenReplay.propTypes = {
  axiosInstance: PropTypes.object,
  sessionData: PropTypes.object,
};

OpenReplay.defaultProps = {
  axiosInstance: null,
  sessionData: null,
};

export default forwardRef(OpenReplay);
