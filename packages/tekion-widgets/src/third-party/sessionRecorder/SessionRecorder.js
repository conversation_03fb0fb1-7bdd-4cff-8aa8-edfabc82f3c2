import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';

import _isNil from 'lodash/isNil';

import TEnvReader from '@tekion/tekion-base/readers/Env';
import UserReader from '@tekion/tekion-base/readers/User';
import DEALER_PROPERTIES from '@tekion/tekion-base/constants/dealerProperties';
import { currentRecorder } from '@tekion/tekion-base/third-party/open-replay/refs';

import withPropertyConsumer from '@tekion/tekion-components/src/organisms/propertyProvider/withPropertyConsumer';

import OpenReplay from './OpenReplay';

export const SCRIPT_NODE_ID = 'session-recorder-snippet';
export { currentRecorder };

const SESSION_RECORDER_PROVIDER = {
  OPEN_REPLAY: 'OPEN_REPLAY',
};

const SESSION_RECORDER_PROVIDER_VS_COMPONENT = {
  [SESSION_RECORDER_PROVIDER.OPEN_REPLAY]: OpenReplay,
};

class SessionRecorder extends PureComponent {
  static propTypes = {
    getDealerPropertyValue: PropTypes.string.isRequired,
  };

  state = {
    userSessionCaptureEnabled: false,
    userInfo: TEnvReader.userInfo(),
  };

  componentDidMount() {
    this.setUpSessionRenderer();
  }

  setUpSessionRenderer() {
    const { getDealerPropertyValue } = this.props;
    const isDealerSessionRecordingDisabled =
      getDealerPropertyValue(DEALER_PROPERTIES.SESSION_RECORDING_DISABLED) ?? false;
    this.setState({ userSessionCaptureEnabled: !isDealerSessionRecordingDisabled });
  }

  renderSessionRecorder = () => {
    const { getDealerPropertyValue, axiosInstance } = this.props;
    const { userInfo } = this.state;
    const userIdentifier = `${UserReader.dealerId(userInfo)}_${UserReader.id(userInfo)}`;
    const sessionRecorderProvider =
      getDealerPropertyValue(DEALER_PROPERTIES.SESSION_RECORDER_PROVIDER) ?? SESSION_RECORDER_PROVIDER.OPEN_REPLAY;
    const SessionRecorderComponent = SESSION_RECORDER_PROVIDER_VS_COMPONENT[sessionRecorderProvider];
    if (_isNil(SessionRecorderComponent)) {
      return null;
    }
    const openReplayProperties = getDealerPropertyValue(DEALER_PROPERTIES.OPEN_REPLAY_PROPERTIES);
    return (
      <SessionRecorderComponent
        sessionData={userInfo}
        sessionId={userIdentifier}
        scriptNodeId={SCRIPT_NODE_ID}
        axiosInstance={axiosInstance}
        getDealerPropertyValue={getDealerPropertyValue}
        ref={currentRecorder}
        openReplayProperties={openReplayProperties}
      />
    );
  };

  render() {
    const { userSessionCaptureEnabled } = this.state;
    if (!userSessionCaptureEnabled) return null;

    return this.renderSessionRecorder();
  }
}

export default React.memo(withPropertyConsumer(SessionRecorder));
