// Lodash
import _isEmpty from 'lodash/isEmpty';
import _intersectionBy from 'lodash/intersectionBy';

import { EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';

// Helpers
import { findBestMatchedAppConfigsForPath, checkPathException } from '../appSkeleton.config.helper';

const appConfigUniqueIdentifier = config => {
  const appKey = config?.app;
  const overriddenModule = config?.override || config?.moduleOverride;
  const appModuleName = overriddenModule || config?.module;
  return `${appKey}__${appModuleName}`;
};

export const getIsValidPathAndBestMatchExists = ({ path, appModuleConfig, permissibleAppConfigs }) => {
  const { bestMatchLength, bestMatchedAppConfigs } = findBestMatchedAppConfigsForPath(path, appModuleConfig);
  if (bestMatchLength > 0) {
    const validAppsForPath = _intersectionBy(bestMatchedAppConfigs, permissibleAppConfigs, appConfigUniqueIdentifier);
    const isValid = !_isEmpty(validAppsForPath);
    return { bestMatchExists: true, isValid, bestMatchedAppConfigs };
  }
  return { bestMatchExists: false, isValid: false, bestMatchedAppConfigs: EMPTY_ARRAY };
};

const appModulePathValidationHandler = (next, params) => {
  const {
    path,
    appModuleConfig,
    getDealerPropertyValue,
    dealerStatus,
    isNewAppSkeletonConfigEnabled,
    permissibleAppConfigs,
  } = params;

  // todo cleanup
  if (path.includes('journalEntry')) return next(params);
  const { bestMatchExists, isValid, bestMatchedAppConfigs } = getIsValidPathAndBestMatchExists({
    path,
    appModuleConfig,
    permissibleAppConfigs,
  });

  return bestMatchExists
    ? isValid ||
        (!isNewAppSkeletonConfigEnabled &&
          checkPathException(bestMatchedAppConfigs, dealerStatus, getDealerPropertyValue))
    : next(params);
};

export default appModulePathValidationHandler;
