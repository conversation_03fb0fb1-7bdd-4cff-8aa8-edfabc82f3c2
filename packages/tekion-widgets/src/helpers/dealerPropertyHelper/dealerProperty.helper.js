import _noop from 'lodash/noop';

import DEALER_PROPERTIES from '@tekion/tekion-base/constants/dealerProperties';
import { isInchcape } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
const {
  VEHICLE_RECOMMENDATION_ENABLED,
  SUMBIT_ECONTRACT,
  DEALS_FNIMENU,
  ZERO_CONTACT_SALES,
  ENABLE_TASK_MANAGER_TAB,
  SALES_DRS_FNIMENU,
  SALES_DRS_EDOCS,
  ACTIVATE_GHOST_PRODUCT,
  NEW_DEAL_CREATE_API_ENABLED,
  CRM_ENABLED,
  CRM_LEGACY_ASSIGNEES_ENABLED,
  MULTI_OEM_SWITCH_ENABLED,
  CALC_ENGINE_ENABLED,
  GALAXY_ENABLED,
  COM_LR_ENABLED,
  DEAL_STATUS_CONFIG_ENABLED,
  DEAL_STATUS_RULES_CONFIG_ENABLED,
  GALAXY_DEAL_COMPARE_ENABLED,
  LIVE_GALAXY_DEAL_COMPARE_ENABLED,
  VEH<PERSON><PERSON>_CONFIGURATOR_ENABLED,
  CMS_DOB_STR_ENABLED,
  MULTI_VEHICLE_DESKING,
  SALES_SCAN_MANAGEMENT_V2_ENABLED,
  SCAN_MANAGEMENT_V2_ENABLED,
  LIVE_CHAT_WIDGET_ENABLED,
  VI_RE_POSTING,
  NEW_ZCS_ENABLED,
  OEM_ENABLED,
  CRM_SERVICE_PARTS_LEADS_ENABLED,
  SHOW_ALL_LENDER_PROGRAMS,
  E_SIGN_CAPABILITY,
  FNI_SPP_LENDER,
  E_SIGN_ENABLE_FOR_R1,
  DEAL_MODIFIED_COMPARISON_ENABLED,
  COMPLETION_CERTIFICATE_ENABLED,
  RV_DEALER_ENABLED,
  VEHICLE_TYPE_CONFIGURATION_ENABLED,
  ENABLE_TAX_BREAKUP,
  TEKION_CREDIT_COMPLIANCE,
  REASSIGN_CONTRACT_ENABLED,
  CRM_BULK_MERGE_ENABLED,
  TAX_GROUP_ENABLED,
  IS_LEASE_NOT_SUPPORTED,
  COMMERCIAL_SALES_SETUP_ENABLED,
  MULTILINGUAL_ENABLED,
  SALES_LEAD_MANUAL_STAGE_MOVEMENT_ENABLED,
  STANDARDIZED_MAKE_SETUP_ENABLED,
  CTC_DISABLED,
  CRM_CUSTOMER_ONEVIEW_ENABLED,
  CAMPING_WORLD_ENABLED,
  CAMPAIGN_MANAGEMENT_DEALER_PROPERTY,
  TEKION_PAY_ENABLED,
  ENHANCE_VIN_DECODING,
  DSE_STANDALONE,
  CRM_CUSTOM_WIDGETS_ENABLED,
  DEALS_FOUNDATION_ENABLED,
  TPAY_PAYMENT_LINK_ENABLED,
  CRM_STANDALONE_ENABLED,
  LEAD_CONVERSION_SCORE_ENABLED,
  CHECKIN_CONFIGURATOR_ENABLED,
  LEAD_ASSIGNMENT_V2_ENABLED,
  VIN_LOOKUP_DISABLED,
  MIGRATION_DASHBOARD_ENABLED,
  PLATFORM_AUDIT_LOGS_FOR_SALES,
  CRM_NEW_GTC_SETUP_ENABLED,
  CAMPAIGN_ONLY_DOMAIN_ENABLED,
  DOMAIN_COMMUNICATION_SETUP_ENABLED,
  COMM_SETUP_DELETE_DOMAIN_DISABLED,
  SINGLE_NUMBER_GTC_ENABLED,
  NEW_DEAL_PREVIEW_WORKFLOW,
  NDC_SET_UP_ENABLED,
  BACKEND_DRIVEN_CREDIT_APP,
  CREDIT_REPORTS_ASYNC,
  ROUTEONE_ECON_OLD_FLOW,
  ROUTEONE_ECON_BIFROST_SIGNING_ONLY,
  ROUTEONE_ECON_BIFROST_SUBMISSION_AND_SIGNING,
  ROUTEONE_ECON_BIFROST_VALIDATION_AND_SIGNING,
  DEALER_TRACK_DFE_CREDIT_APPLICATION_ENABLED,
  LEASE_COMPARISION_ENABLED,
  CUSTOMER_EAVS2_VALIDATION_ENABLED,
  POLLY_INTEGRATION_ENABLED,
  VEHICLE_SYNDICATION_ENABLED,
  VEHICLE_PRECONFIGURED_OPTIONS_ENABLED,
  CRM_ZULTYS_ENABLED,
  CUSTOMER_VALUE_ENABLED,
  CUSTOMER_360_COUPONS_ENABLED,
  VEHICLE_TRACKING_ENABLED,
  ENABLE_ENHANCED_DOCUMENTS_TAB,
  US_UPLOAD_DOC_SYNC_CRM,
  CAPENCY_VERIFICATION_REQUIRED,
  CENTRAL_COMMUNICATION_OPTIN_PREFERENCES_ENABLED,
  FLEETS,
  DOCUMENTS_TAB_PERFORMANCE_IMPROVEMENTS,
  IS_AUTO_ADJUST_DEAL_POSTINGS_ENABLED,
  DIGITAL_SIGNING_PLATFORM,
  ENABLE_PARALLEL_SIGNING,
  ML_BASED_LEAD_CONTACT_LINKING_ENABLED,
  OEM_REWARDS_DISABLED,
  ENABLE_DEALS_DASHBOARD,
  FEED_SYNDICATION_ENABLED,
  ACCOUNTING_FORM8300,
  INVENTORY_MINIMISED_INDEX,
  CUSTOMER_UNMAPPED_VALUE_ENABLED,
  IS_HONDA_PROGRAM,
  IS_HONDA_CPO_PROGRAM,
  IS_HONDA_ZDX_PROGRAM,
  IS_HONDA_MARCH,
  IS_AEC_PROGRAM,
  IS_ARC_AEC_PROGRAM,
  IS_ARC_LITE_ENABLED,
  EXTERNAL_DMS_ENABLED,
  EXTERNAL_DMS_V2_ENABLED,
  TEKION_PAY_SERVER_SIDE_ENABLED,
  FNIMENU_V2_ENABLED,
  IS_PNI_REGISTERED,
  COV_REFERENCING_ENABLED,
  FNI_AUTO_UPDATE_ENABLED,
  ENABLE_BASE_PAYMENT_ACKNOWLEDGEMENT,
  CONCIERGE_FNI_ENABLED,
  CONCIERGE_PRO,
  DRS_CASHIERING_IFRAME,
  CRM_MULTI_OEM_SWITCH_ENABLED,
  GET_ADDRESS_DOT_IO_ENABLED,
  VI_ARC_LITE_ENABLED,
  IS_RRG_PROGRAM,
  IS_INCHCAPE_PROGRAM,
  AI_COMMUNICATIONS_ENABLED,
  CRM_MULTI_DOMAIN_ENABLED,
  NEW_TRADEIN_ENABLED,
  CRM_CUSTOMER_LEVEL_VALUE_ENABLED,
  LEAD_DEAL_SYNC_V2,
  API_LAYER_ENABLED,
  LIVE_CHAT_WEBSOCKET_ENABLE,
  IS_CO_BUYER_SIGNATURE_ENABLED_IN_FNI_MENU_PDF,
  CONFIGURABLE_PAYMENT_MODES,
  ENABLE_DOCS_VISIBILITY_ACROSS_MODULES,
  ENABLE_DYNAMIC_VEHICLE_FORM,
  DIGICERT_SIGNATURE_ENCRYPTION_TYPE,
  VI_V2_ENABLED,
  SERVICE_V3_ENABLED,
  RETAIL_SHARE_VIA_VIRTUAL_MEETING_ENABLED,
  IS_DR2_ENABLED,
  ADVANCED_APPOINTMENT_ENABLED,
  IS_VEHICLE_V2_CERTIFICATION_ENABLED,
  OPTIONS_V2_ENABLED,
  ENFORCE_MANDATORY_FIELDS,
  NON_CVC_ENABLED,
  CONTACT_CONNECTIONS_ENABLED,
  IS_DR_2,
  NEW_DRS_SETUP_V2,
  PAYER_PAYTYPE_SETUP_ENABLED,
  LEXIS_NEXIS_COMPLIANCE_ENABLED,
  VI_VEHICLE_TRANSFER_V2_ENABLED,
  DEALERTRACK_FINANCIAL_SERVICES_ENABLED,
  IS_ASBURY_PROGRAM,
  IS_MSCAN_BACKEND,
  DESKING_TDP_INTEGRATION_ENABLED,
  SUPPORT_PRIOR_LEASE_CREDIT_ENABLED,
  TRANSACTION_LIMIT_ON_ALL_SOURCES_ENABLED,
  ENHANCED_VI_DOCS,
  PREVIEW_FIRST_PDF_GENERATION,
  CASHIERING_INVOICE_LEVEL,
  CMS_DUPLICATE_PREVENTION_ENABLED,
  DEAL_CONTACTS_ENABLED,
  HIDE_TRADE_IN_TAX_EXEMPT,
  TRADE_IN_MEDIA_SUPPORT_ENABLED,
  CASHIERING_DEMO_APP_ENABLED,
  NEW_VEHICLE_SALES_ENABLED,
  BUILD_VEHICLE_V2_ENABLED,
  PDF_OPTIMIZATION_ENABLED,
  FNI_PDF_V2,
  CUSTOMER_APPROVALS_ENABLED,
  IS_NEW_VEHICLE_VARIANT_ENABLED,
  UNIVERSAL_GUEST_PROFILE_ENABLED,
  DEALS_VIRTUAL_MEETING_ENABLED,
  NEW_VARIANT_SELECTION_FOR_CALC_ENABLED,
  FORMS_PORTAL_ENABLED,
  USER_BASED_PRINTER_STATUS_ENABLED,
  IS_ACURA_ALL_VIN_ENABLED,
  ACCOUNTING_INTER_PREFIX,
} = DEALER_PROPERTIES;

class DealerPropertyHelper {
  constructor() {
    this.getDealerPropertyValue = _noop;
  }

  initPropertyGetter(value = _noop) {
    this.getDealerPropertyValue = value;
  }

  // Sales DP's
  isBudgetBasedRecommendationsEnabled() {
    const enabled = this.getDealerPropertyValue(VEHICLE_RECOMMENDATION_ENABLED);
    return enabled;
  }

  isGalaxyLiveDealCompareEnabled() {
    const enabled = this.getDealerPropertyValue(LIVE_GALAXY_DEAL_COMPARE_ENABLED);
    return enabled;
  }

  isComLrEnabled() {
    const enabled = this.getDealerPropertyValue(COM_LR_ENABLED);
    return enabled;
  }

  isDealStatusConfigEnabled() {
    const enabled = this.getDealerPropertyValue(DEAL_STATUS_CONFIG_ENABLED) || isInchcape();
    return enabled;
  }

  // Retail DP's
  isCalcEngineEnabled() {
    const enabled = this.getDealerPropertyValue(CALC_ENGINE_ENABLED);
    return enabled;
  }

  // Common DP's
  isGalaxyEnabled() {
    const enabled = this.getDealerPropertyValue(GALAXY_ENABLED);
    return enabled;
  }

  // FNI DP's
  isFnIEnabled() {
    const enabled = this.getDealerPropertyValue(DEALS_FNIMENU);
    return enabled;
  }

  isPniEnabled() {
    const enabled = this.getDealerPropertyValue(IS_PNI_REGISTERED);
    return enabled;
  }

  isDealStatusRulesConfigEnabled() {
    const enabled = this.getDealerPropertyValue(DEAL_STATUS_RULES_CONFIG_ENABLED) || isInchcape();
    return enabled;
  }

  isVehicleConfiguratorEnabled() {
    const enabled = this.getDealerPropertyValue(VEHICLE_CONFIGURATOR_ENABLED);
    return enabled;
  }

  isEContractEnabled() {
    const enabled = this.getDealerPropertyValue(SUMBIT_ECONTRACT);
    return enabled;
  }

  iszeroContactSalesEnabled() {
    const enabled = this.getDealerPropertyValue(ZERO_CONTACT_SALES);
    return enabled;
  }

  isTaskManagerTabEnabled() {
    const enabled = this.getDealerPropertyValue(ENABLE_TASK_MANAGER_TAB);
    return enabled;
  }

  isFnIConciergeEnabled() {
    const enabled = this.getDealerPropertyValue(SALES_DRS_FNIMENU);
    return enabled;
  }

  isCheckinConfiguratorEnabled() {
    const enabled = this.getDealerPropertyValue(CHECKIN_CONFIGURATOR_ENABLED);
    return enabled;
  }

  isDocumentConciergeEnabled() {
    const enabled = this.getDealerPropertyValue(SALES_DRS_EDOCS);
    return enabled;
  }

  isGhostProductEnabled() {
    const enabled = this.getDealerPropertyValue(ACTIVATE_GHOST_PRODUCT);
    return enabled;
  }

  // enableNewAPICheck is an exclusive check to implement New Market Scan from backend API on few places only rather then on all places
  // if UI code wants to go through this flag needs to pass enableNewAPICheck as true
  isCreateDealWithNewAPIEnabled(enableNewAPICheck) {
    if (!enableNewAPICheck) {
      return false;
    }
    const enabled = this.getDealerPropertyValue(NEW_DEAL_CREATE_API_ENABLED);
    return enabled;
  }

  isCRMEnabled() {
    const enabled = this.getDealerPropertyValue(CRM_ENABLED);
    return enabled;
  }

  isCRMEnabledV2() {
    const enabled = this.isCRMEnabled();
    return enabled;
  }

  isMultiOEMEnabled() {
    const enabled = this.getDealerPropertyValue(MULTI_OEM_SWITCH_ENABLED);
    return enabled;
  }

  isVehicleTrackingEnabled() {
    const enabled = this.getDealerPropertyValue(VEHICLE_TRACKING_ENABLED);
    return enabled;
  }

  isCMSDOBStringEnabled() {
    const enabled = this.getDealerPropertyValue(CMS_DOB_STR_ENABLED);
    return enabled;
  }

  isMultiVehicleDeskingEnabled() {
    const enabled = this.getDealerPropertyValue(MULTI_VEHICLE_DESKING);
    return enabled;
  }

  isScanManagementV2Enabled() {
    const isSalesScanV2Enabled = this.getDealerPropertyValue(SALES_SCAN_MANAGEMENT_V2_ENABLED);
    const isCoreScanV2Enabled = this.getDealerPropertyValue(SCAN_MANAGEMENT_V2_ENABLED);
    return isSalesScanV2Enabled && isCoreScanV2Enabled;
  }

  isLiveChatEnabled() {
    const enabled = this.getDealerPropertyValue(LIVE_CHAT_WIDGET_ENABLED);
    return enabled;
  }

  isLiveChatWebsocketEnabled = () => this.getDealerPropertyValue(LIVE_CHAT_WEBSOCKET_ENABLE);

  isVehicleRePostingEnabled() {
    const enabled = this.getDealerPropertyValue(VI_RE_POSTING);
    return enabled;
  }

  isNewZCSEnabled() {
    const enabled = this.getDealerPropertyValue(NEW_ZCS_ENABLED);
    return enabled;
  }

  getDealerSupportedOEMs() {
    const OEMs = this.getDealerPropertyValue(OEM_ENABLED);
    return OEMs;
  }

  isCRMServiceAndPartsEnabled() {
    const enabled = this.getDealerPropertyValue(CRM_SERVICE_PARTS_LEADS_ENABLED);
    return enabled;
  }

  isESignCapabilityEnabled() {
    const enabled = this.getDealerPropertyValue(E_SIGN_CAPABILITY);
    return enabled;
  }

  isFniSppLenderEnabled() {
    const enabled = this.getDealerPropertyValue(FNI_SPP_LENDER);
    return enabled;
  }

  isESignEnableForR1() {
    const enabled = this.getDealerPropertyValue(E_SIGN_ENABLE_FOR_R1);
    return enabled;
  }

  isDealModifiedComparisionEnabled() {
    const enabled = this.getDealerPropertyValue(DEAL_MODIFIED_COMPARISON_ENABLED);
    return enabled;
  }

  isCompletionCertificateEnabled = () => this.getDealerPropertyValue(COMPLETION_CERTIFICATE_ENABLED);

  showAllLenderPrograms() {
    const enabled = this.getDealerPropertyValue(SHOW_ALL_LENDER_PROGRAMS);
    return enabled;
  }

  isRVDealerEnabled() {
    const enabled = this.getDealerPropertyValue(RV_DEALER_ENABLED);
    return enabled;
  }

  isVinLookupDisabled() {
    const enabled = this.getDealerPropertyValue(VIN_LOOKUP_DISABLED);
    return enabled;
  }

  isMultiLingualEnabled() {
    const enabled = this.getDealerPropertyValue(MULTILINGUAL_ENABLED);
    return enabled;
  }

  isTaxBreakUpEnabled() {
    const enabled = this.getDealerPropertyValue(ENABLE_TAX_BREAKUP);
    return enabled;
  }

  isTekionCreditComplianceEnabled() {
    const enabled = this.getDealerPropertyValue(TEKION_CREDIT_COMPLIANCE);
    return enabled;
  }

  isReassignContractEnabled() {
    const enabled = this.getDealerPropertyValue(REASSIGN_CONTRACT_ENABLED);
    return enabled;
  }

  isCRMBulkMergeEnabled = () => {
    const enabled = this.getDealerPropertyValue(CRM_BULK_MERGE_ENABLED);
    return enabled;
  };

  isTaxGroupEnabled = () => {
    const enabled = this.getDealerPropertyValue(TAX_GROUP_ENABLED);
    return enabled;
  };

  isLeaseNotSupported = () => {
    const enabled = this.getDealerPropertyValue(IS_LEASE_NOT_SUPPORTED);
    return enabled;
  };

  isCommercialSalesSetupEnabled() {
    const enabled = this.getDealerPropertyValue(COMMERCIAL_SALES_SETUP_ENABLED);
    return enabled;
  }

  standardizedMakeSetupEnabled = () => this.getDealerPropertyValue(STANDARDIZED_MAKE_SETUP_ENABLED);

  isPlatFormAuditLogsForSalesEnabled() {
    const enabled = this.getDealerPropertyValue(PLATFORM_AUDIT_LOGS_FOR_SALES);
    return enabled;
  }

  isCTCDisabled = () => {
    const enabled = this.getDealerPropertyValue(CTC_DISABLED);
    return enabled;
  };

  isCampaignManagementDealerPropertyEnabled = () => this.getDealerPropertyValue(CAMPAIGN_MANAGEMENT_DEALER_PROPERTY);

  crmCustOneViewEnabled = () => this.getDealerPropertyValue(CRM_CUSTOMER_ONEVIEW_ENABLED);

  isCustomerUnmappedValueEnabled = () => this.getDealerPropertyValue(CUSTOMER_UNMAPPED_VALUE_ENABLED);

  isCampingWorldEnabled = () => this.getDealerPropertyValue(CAMPING_WORLD_ENABLED);

  isEnhancedVINDecodingEnabled = () => this.getDealerPropertyValue(ENHANCE_VIN_DECODING);

  isTekionPayEnabled = () => this.getDealerPropertyValue(TEKION_PAY_ENABLED);

  isDealsFoundationEnabled = () => this.getDealerPropertyValue(DEALS_FOUNDATION_ENABLED);

  isDSEStandAloneEnabled = () => this.getDealerPropertyValue(DSE_STANDALONE);

  isTekionPaymentLinkEnabled = () => this.getDealerPropertyValue(TPAY_PAYMENT_LINK_ENABLED);

  isCustomWidgetEnabled = () => this.getDealerPropertyValue(CRM_CUSTOM_WIDGETS_ENABLED);

  isStandAloneCRMEnabled = () => this.getDealerPropertyValue(CRM_STANDALONE_ENABLED);

  isLeadConversionScoreEnabled = () => this.getDealerPropertyValue(LEAD_CONVERSION_SCORE_ENABLED);

  isLeadAssignmentEnabled = () => this.getDealerPropertyValue(LEAD_ASSIGNMENT_V2_ENABLED);

  isMigrationDashboardEnabled = () => this.getDealerPropertyValue(MIGRATION_DASHBOARD_ENABLED);

  isCRMNewGTCSetupEnabled = () => this.getDealerPropertyValue(CRM_NEW_GTC_SETUP_ENABLED);

  isCampaignOnlyDomainEnabled = () => this.getDealerPropertyValue(CAMPAIGN_ONLY_DOMAIN_ENABLED);

  isCommunicationSetupEnabled = () => this.getDealerPropertyValue(DOMAIN_COMMUNICATION_SETUP_ENABLED);

  isCRMCommunicationSetupDeleteDomainDisabled = () => this.getDealerPropertyValue(COMM_SETUP_DELETE_DOMAIN_DISABLED);

  isSingleGTCNumberEnabled = () => this.getDealerPropertyValue(SINGLE_NUMBER_GTC_ENABLED);

  isNewDealPreviewWorkflow = () => this.getDealerPropertyValue(NEW_DEAL_PREVIEW_WORKFLOW);

  isNDCSetUpEnabled = () => this.getDealerPropertyValue(NDC_SET_UP_ENABLED);

  isBackendDrivenCreditApplicationFlow = () => this.getDealerPropertyValue(BACKEND_DRIVEN_CREDIT_APP);

  isAsyncCreditReportsEnabled = () => this.getDealerPropertyValue(CREDIT_REPORTS_ASYNC);

  isRouteOneOldFlow = () => this.getDealerPropertyValue(ROUTEONE_ECON_OLD_FLOW);

  isRouteOneSigningOnly = () => this.getDealerPropertyValue(ROUTEONE_ECON_BIFROST_SIGNING_ONLY);

  isRouteOneSigningAndSubmission = () => this.getDealerPropertyValue(ROUTEONE_ECON_BIFROST_SUBMISSION_AND_SIGNING);

  isRouteOneValidationAndSigning = () => this.getDealerPropertyValue(ROUTEONE_ECON_BIFROST_VALIDATION_AND_SIGNING);

  isDealerTrackDfeCreditApplicationEnabled = () =>
    this.getDealerPropertyValue(DEALER_TRACK_DFE_CREDIT_APPLICATION_ENABLED);

  isLeaseComparisionEnabled = () => this.getDealerPropertyValue(LEASE_COMPARISION_ENABLED);

  isCustomerEAVSEnabled = () => this.getDealerPropertyValue(CUSTOMER_EAVS2_VALIDATION_ENABLED);

  isFleetsEnabled = () => this.getDealerPropertyValue(FLEETS);

  isPollyIntegrationEnabled = () => this.getDealerPropertyValue(POLLY_INTEGRATION_ENABLED);

  isVehicleLevelSyndicationEnabled = () => this.getDealerPropertyValue(VEHICLE_SYNDICATION_ENABLED);

  isVehicleOptionsEnabled = () => this.getDealerPropertyValue(VEHICLE_PRECONFIGURED_OPTIONS_ENABLED);

  isCRMZultysEnabled = () => this.getDealerPropertyValue(CRM_ZULTYS_ENABLED);

  isCustomerValueEnabled = () => this.getDealerPropertyValue(CUSTOMER_VALUE_ENABLED);

  isCustomer360CouponsEnabled = () => this.getDealerPropertyValue(CUSTOMER_360_COUPONS_ENABLED);

  isEnhancedDocumentsTabEnabled = () => this.getDealerPropertyValue(ENABLE_ENHANCED_DOCUMENTS_TAB);

  isUSUploadDocSyncCrm = () => this.getDealerPropertyValue(US_UPLOAD_DOC_SYNC_CRM);

  isCapencyVerificationRequired = () => this.getDealerPropertyValue(CAPENCY_VERIFICATION_REQUIRED);

  isCentralCommunicationOptionPreferenceEnabled = () =>
    this.getDealerPropertyValue(CENTRAL_COMMUNICATION_OPTIN_PREFERENCES_ENABLED);

  isRVVehiclesSupported = () => this.getDealerPropertyValue(VEHICLE_TYPE_CONFIGURATION_ENABLED);

  isAutoAdjustDealPostingsEnabled = () => this.getDealerPropertyValue(IS_AUTO_ADJUST_DEAL_POSTINGS_ENABLED);

  isDocumentsTabPerformanceImprovementsEnabled = () =>
    this.getDealerPropertyValue(DOCUMENTS_TAB_PERFORMANCE_IMPROVEMENTS);

  isDigitalSigningPlatformEnabled = () => this.getDealerPropertyValue(DIGITAL_SIGNING_PLATFORM);

  isParallelSigningEnabled = () => this.getDealerPropertyValue(ENABLE_PARALLEL_SIGNING);

  isMlContactLeadEnabled = () => this.getDealerPropertyValue(ML_BASED_LEAD_CONTACT_LINKING_ENABLED);

  isOemRewardsDisabled = () => this.getDealerPropertyValue(OEM_REWARDS_DISABLED);

  isDealsDashboardEnabled = () => this.getDealerPropertyValue(ENABLE_DEALS_DASHBOARD);

  isCashieringForm8300Applicable = () => this.getDealerPropertyValue(ACCOUNTING_FORM8300);

  isFeedSyndicationEnabled = () => this.getDealerPropertyValue(FEED_SYNDICATION_ENABLED);

  isInventoryIndexMinimised = () => this.getDealerPropertyValue(INVENTORY_MINIMISED_INDEX);

  isExternalDmsEnabled = () => this.getDealerPropertyValue(EXTERNAL_DMS_ENABLED);

  isExternalDmsV2Enabled = () => this.getDealerPropertyValue(EXTERNAL_DMS_V2_ENABLED);

  isHondaProgramEnabled = () => this.getDealerPropertyValue(IS_HONDA_PROGRAM);

  isHondaCPOProgramEnabled = () => this.getDealerPropertyValue(IS_HONDA_CPO_PROGRAM);

  isHondaZDXProgramEnabled = () => this.getDealerPropertyValue(IS_HONDA_ZDX_PROGRAM);

  isAcuraAllVinEnabled = () => this.getDealerPropertyValue(IS_ACURA_ALL_VIN_ENABLED);

  isHondaMarchProgramEnabled = () => this.getDealerPropertyValue(IS_HONDA_MARCH);

  isAECProgram = () => this.getDealerPropertyValue(IS_AEC_PROGRAM);

  isArcAECProgram = () => this.getDealerPropertyValue(IS_ARC_AEC_PROGRAM);

  isArcLiteProgramDPEnabled = () => this.getDealerPropertyValue(IS_ARC_LITE_ENABLED);

  isTpayTerminalServerSideEnabled = () => this.getDealerPropertyValue(TEKION_PAY_SERVER_SIDE_ENABLED);

  isFniMenuV2Enabled = () => this.getDealerPropertyValue(FNIMENU_V2_ENABLED);

  isCOVReferencingEnabled = () => this.getDealerPropertyValue(COV_REFERENCING_ENABLED);

  isAutoUpdateEnabled = () => this.getDealerPropertyValue(FNI_AUTO_UPDATE_ENABLED);

  isBasePaymentAcknowledgement = () => this.getDealerPropertyValue(ENABLE_BASE_PAYMENT_ACKNOWLEDGEMENT);

  isConciergeFniEnabled = () => this.getDealerPropertyValue(CONCIERGE_FNI_ENABLED);

  isConciergeProEnabled = () => this.getDealerPropertyValue(CONCIERGE_PRO);

  isDrsCashieringIframeEnabled = () => this.getDealerPropertyValue(DRS_CASHIERING_IFRAME);

  isCrmMultiOEMSwitchEnabled = () => this.getDealerPropertyValue(CRM_MULTI_OEM_SWITCH_ENABLED);

  getGetAddressIoEnabled = () => this.getDealerPropertyValue(GET_ADDRESS_DOT_IO_ENABLED);

  isViArcLiteEnabled = () => this.getDealerPropertyValue(VI_ARC_LITE_ENABLED);

  isCapencyOrGetAddIoEnabled = () =>
    this.getDealerPropertyValue(GET_ADDRESS_DOT_IO_ENABLED) ||
    this.getDealerPropertyValue(CAPENCY_VERIFICATION_REQUIRED);

  isInchcapeEnabled = () => this.getDealerPropertyValue(IS_INCHCAPE_PROGRAM);

  // Deprecated. please use Sub Application ID
  isRRGEnabled = () => this.getDealerPropertyValue(IS_RRG_PROGRAM);

  isAiCommunicationEnabled = () => this.getDealerPropertyValue(AI_COMMUNICATIONS_ENABLED);

  isLegacyAssigneesEnabled = () => this.getDealerPropertyValue(CRM_LEGACY_ASSIGNEES_ENABLED);

  isCrmMultiDomainEnabled = () => this.getDealerPropertyValue(CRM_MULTI_DOMAIN_ENABLED);

  isNewTradeInEnabled = () => this.getDealerPropertyValue(NEW_TRADEIN_ENABLED);

  isCrmCustomerLevelValueEnabled = () => this.getDealerPropertyValue(CRM_CUSTOMER_LEVEL_VALUE_ENABLED);

  isDealLeadSyncV2Enabled = () => this.getDealerPropertyValue(LEAD_DEAL_SYNC_V2);

  isAPILayerEnabled = () => this.getDealerPropertyValue(API_LAYER_ENABLED);

  isCoBuyerSignatureEnabledInFniMenuPdf = () =>
    this.getDealerPropertyValue(IS_CO_BUYER_SIGNATURE_ENABLED_IN_FNI_MENU_PDF);

  isPaymentModeConfigEnabled = () => this.getDealerPropertyValue(CONFIGURABLE_PAYMENT_MODES);

  isVisibilityAcrossModuleEnabled = () => this.getDealerPropertyValue(ENABLE_DOCS_VISIBILITY_ACROSS_MODULES);

  isDynamicVehicleFormEnabled = () => this.getDealerPropertyValue(ENABLE_DYNAMIC_VEHICLE_FORM);

  isDigicertSignatureEncryptionEnabled = () => this.getDealerPropertyValue(DIGICERT_SIGNATURE_ENCRYPTION_TYPE);

  isViV2Enabled = () => this.getDealerPropertyValue(VI_V2_ENABLED);

  isServiceV3Enabled = () => this.getDealerPropertyValue(SERVICE_V3_ENABLED);

  isRetailShareViaVMEnabled = () => this.getDealerPropertyValue(RETAIL_SHARE_VIA_VIRTUAL_MEETING_ENABLED);

  isDR2Enabled = () => this.getDealerPropertyValue(IS_DR2_ENABLED);

  isAdvancedAppointmentEnabled = () => this.getDealerPropertyValue(ADVANCED_APPOINTMENT_ENABLED);

  isV2CertificationEnabled = () => this.getDealerPropertyValue(IS_VEHICLE_V2_CERTIFICATION_ENABLED);

  isOptionsV2Enabled = () => this.getDealerPropertyValue(OPTIONS_V2_ENABLED);

  isEnforceMandatoryFieldsEnabled = () => this.getDealerPropertyValue(ENFORCE_MANDATORY_FIELDS);

  isNonCVCEnabled = () => this.getDealerPropertyValue(NON_CVC_ENABLED);

  isContactConnectionsEnabled = () => this.getDealerPropertyValue(CONTACT_CONNECTIONS_ENABLED);

  isDR2FlowEnabled = () => this.getDealerPropertyValue(IS_DR_2);

  isNewDR2FlowEnabled = () => this.getDealerPropertyValue(NEW_DRS_SETUP_V2);

  isPayerPayTypeSetupEnabled = () =>
    this.isServiceV3Enabled() && this.getDealerPropertyValue(PAYER_PAYTYPE_SETUP_ENABLED);

  isLNComplianceEnabled = () => this.getDealerPropertyValue(LEXIS_NEXIS_COMPLIANCE_ENABLED);

  isVehicleTransferV2Enabled = () => this.getDealerPropertyValue(VI_VEHICLE_TRANSFER_V2_ENABLED);

  isDTFinancialServicesEnabled = () => this.getDealerPropertyValue(DEALERTRACK_FINANCIAL_SERVICES_ENABLED);

  isAsburyProgramEnabled = () => this.getDealerPropertyValue(IS_ASBURY_PROGRAM);

  isTransactionLimitOnAllSourcesEnabled = () => this.getDealerPropertyValue(TRANSACTION_LIMIT_ON_ALL_SOURCES_ENABLED);

  isMScanBackendEnabled = () => this.getDealerPropertyValue(IS_MSCAN_BACKEND);

  isEnhancedVIDocsEnabled = () => this.getDealerPropertyValue(ENHANCED_VI_DOCS);

  isDeskingTDPIntegrationEnabled = () => this.getDealerPropertyValue(DESKING_TDP_INTEGRATION_ENABLED);

  isPreviewPdfEnabled = () => this.getDealerPropertyValue(PREVIEW_FIRST_PDF_GENERATION);

  isCashieringInvoiceLevelEnabled = () => this.getDealerPropertyValue(CASHIERING_INVOICE_LEVEL);

  isCustomerDuplicatePreventionEnabled = () => false; // this.getDealerPropertyValue(CMS_DUPLICATE_PREVENTION_ENABLED);

  isSupportPriorLeaseCreditEnabled = () => this.getDealerPropertyValue(SUPPORT_PRIOR_LEASE_CREDIT_ENABLED);

  isDealContactsEnabled = () => this.getDealerPropertyValue(DEAL_CONTACTS_ENABLED);

  hideTradeInTaxExempt = () => this.getDealerPropertyValue(HIDE_TRADE_IN_TAX_EXEMPT);

  isTradeInMediaSupportEnabled = () => this.getDealerPropertyValue(TRADE_IN_MEDIA_SUPPORT_ENABLED);

  isCashieringDemoAppEnabled = () => this.getDealerPropertyValue(CASHIERING_DEMO_APP_ENABLED);

  isNewVehicleSalesEnabled = () => this.getDealerPropertyValue(NEW_VEHICLE_SALES_ENABLED);

  isBuildVehicleV2Enabled = () => this.getDealerPropertyValue(BUILD_VEHICLE_V2_ENABLED);

  isPdfOptimizationEnabled = () => this.getDealerPropertyValue(PDF_OPTIMIZATION_ENABLED);

  isFniMenuPdfV2Enabled = () => this.getDealerPropertyValue(FNI_PDF_V2);

  isNewVehicleVariantEnabled = () => this.getDealerPropertyValue(IS_NEW_VEHICLE_VARIANT_ENABLED);

  isCustomerApprovalsEnabled = () => this.getDealerPropertyValue(CUSTOMER_APPROVALS_ENABLED);

  isUniversalGuestProfileEnabled = () => this.getDealerPropertyValue(UNIVERSAL_GUEST_PROFILE_ENABLED);

  // CRM DP's
  isManualStageMovementAllowed() {
    return this.getDealerPropertyValue(SALES_LEAD_MANUAL_STAGE_MOVEMENT_ENABLED);
  }

  isDealsVirtualMeetingEnabled = () => this.getDealerPropertyValue(DEALS_VIRTUAL_MEETING_ENABLED);

  isNewVariantSelectionForCalcEnabled = () => this.getDealerPropertyValue(NEW_VARIANT_SELECTION_FOR_CALC_ENABLED);

  isFormsPortalEnabled = () => this.getDealerPropertyValue(FORMS_PORTAL_ENABLED);

  isRealTimePrintStatusV2Enabled = () => this.getDealerPropertyValue(USER_BASED_PRINTER_STATUS_ENABLED);

  isAccountingInterPrefixEnabled = () => this.getDealerPropertyValue(ACCOUNTING_INTER_PREFIX);
}

const dealerPropertyHelper = new DealerPropertyHelper();

export default dealerPropertyHelper;
