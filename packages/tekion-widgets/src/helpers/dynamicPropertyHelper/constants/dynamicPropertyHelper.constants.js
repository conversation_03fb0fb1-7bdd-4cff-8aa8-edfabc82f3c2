const DATA_TYPES = {
  STRING: 'STRING',
  <PERSON><PERSON><PERSON><PERSON><PERSON>: 'BOOLEAN',
  INTEGER: 'INTEGER',
  LONG: 'LONG',
};

const DYNAMIC_PROPERTIES = {
  VI_V2_SETUP_FLOW_ENABLED: 'VI_V2_SETUP_FLOW_ENABLED',
  MAP_FINANCE_CHARGE_IN_MSCAN: 'MAP_FINANCE_CHARGE_IN_MSCAN',
  WEBHOOK_EVENT_TO_APC: 'WEBHOOK_EVENT_TO_APC',
  ENABLE_REPORT_VEHICLE_DELIVERY: 'ENABLE_REPORT_VEHICLE_DELIVERY',
  ACCOUNTING_GROUP_ENABLED: 'ACCOUNTING_GROUP_ENABLED',
  ENABLE_ASSIGNEE_FOR_CLOSED_BOOKED_UNWOUND: 'ENABLE_ASSIGNEE_FOR_CLOSED_BOOKED_UNWOUND',
  RESTRICT_CRM_EVENT_FLOW: 'RESTRICT_CRM_EVENT_FLOW',
  BPOL_TAX_SAME_AS_BNO_TAX: 'BPOL_TAX_SAME_AS_BNO_TAX',
};

export const DYNAMIC_PROPERTIES_PAYLOAD = [
  {
    key: DYNAMIC_PROPERTIES.VI_V2_SETUP_FLOW_ENABLED,
    dataType: DATA_TYPES.BOOLEAN,
  },
  {
    key: DYNAMIC_PROPERTIES.MAP_FINANCE_CHARGE_IN_MSCAN,
    dataType: DATA_TYPES.BOOLEAN,
  },
  {
    key: DYNAMIC_PROPERTIES.WEBHOOK_EVENT_TO_APC,
    dataType: DATA_TYPES.BOOLEAN,
  },
  {
    key: DYNAMIC_PROPERTIES.ENABLE_REPORT_VEHICLE_DELIVERY,
    dataType: DATA_TYPES.BOOLEAN,
  },
  {
    key: DYNAMIC_PROPERTIES.ACCOUNTING_GROUP_ENABLED,
    dataType: DATA_TYPES.BOOLEAN,
  },
  {
    key: DYNAMIC_PROPERTIES.ENABLE_ASSIGNEE_FOR_CLOSED_BOOKED_UNWOUND,
    dataType: DATA_TYPES.BOOLEAN,
  },
  {
    key: DYNAMIC_PROPERTIES.RESTRICT_CRM_EVENT_FLOW,
    dataType: DATA_TYPES.BOOLEAN,
  },
  {
    key: DYNAMIC_PROPERTIES.BPOL_TAX_SAME_AS_BNO_TAX,
    dataType: DATA_TYPES.BOOLEAN,
  },
];

export default DYNAMIC_PROPERTIES;
