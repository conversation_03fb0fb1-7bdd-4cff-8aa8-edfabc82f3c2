import _get from 'lodash/get';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import DYNAMIC_PROPERTIES from './constants/dynamicPropertyHelper.constants';

class DynamicPropertyHelper {
  constructor() {
    this.dynamicPropertyValue = EMPTY_OBJECT;
  }

  initPropertyValues(dynamicPropertyValue) {
    this.dynamicPropertyValue = dynamicPropertyValue;
  }

  isVIV2Enabled = () => _get(this.dynamicPropertyValue, DYNAMIC_PROPERTIES.VI_V2_SETUP_FLOW_ENABLED);

  isMapFinanceChargeInMScanResponse = () =>
    _get(this.dynamicPropertyValue, DYNAMIC_PROPERTIES.MAP_FINANCE_CHARGE_IN_MSCAN) || false;

  isWebhookToApcEnabled = () => _get(this.dynamicPropertyValue, DYNAMIC_PROPERTIES.WEBHOOK_EVENT_TO_APC);

  isNewRDRFlowEnabled = () => _get(this.dynamicPropertyValue, DYNAMIC_PROPERTIES.ENABLE_REPORT_VEHICLE_DELIVERY);

  isAccountingGroupEnabled = () => _get(this.dynamicPropertyValue, DYNAMIC_PROPERTIES.ACCOUNTING_GROUP_ENABLED);

  isDisableAssigneeForClosedBookedUnwound = () =>
    _get(this.dynamicPropertyValue, DYNAMIC_PROPERTIES.ENABLE_ASSIGNEE_FOR_CLOSED_BOOKED_UNWOUND);

  isRestrictCRMEventFlowEnabled = () =>
    _get(this.dynamicPropertyValue, DYNAMIC_PROPERTIES.RESTRICT_CRM_EVENT_FLOW) || false;

  // TEMP DYNAMIC PROPERTY to be removed later
  isBPOLTaxSameAsBNOTax = () => _get(this.dynamicPropertyValue, DYNAMIC_PROPERTIES.BPOL_TAX_SAME_AS_BNO_TAX);
}

const dynamicPropertyHelper = new DynamicPropertyHelper();

export default dynamicPropertyHelper;
