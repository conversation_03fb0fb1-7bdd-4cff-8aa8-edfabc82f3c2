import React, { PureComponent } from 'react';
import cx from 'classnames';
import PropTypes from 'prop-types';
import { defaultMemoize } from 'reselect';
import { compose } from 'recompose';
import _map from 'lodash/map';
import _get from 'lodash/get';
import _size from 'lodash/size';
import _head from 'lodash/head';
import _noop from 'lodash/noop';
import _isEmpty from 'lodash/isEmpty';

import { EMPTY_OBJECT, EMPTY_STRING, NO_DATA } from 'tbase/app.constants';
import { RO_STATUS_LABEL_MAP } from 'tbase/constants/repairOrder/general';
import { SIZES } from 'tcomponents/atoms/FontIcon';
import { tget } from 'tbase/utils/general';
import BaseTable from 'tcomponents/molecules/table/BaseTable';
import { StatusItem } from 'tcomponents/atoms';
import Heading from 'tcomponents/atoms/Heading';
import Button from 'tcomponents/atoms/Button';
import Content from 'tcomponents/atoms/Content';
import PropertyControlledComponent from 'tcomponents/molecules/PropertyControlledComponent';
import IconWithPopover from 'tcomponents/molecules/IconWithPopover';
import EditDeletePopover from 'twidgets/molecules/EditDeletePopover/EditDeletePopover';
import { DATE_TIME_FORMAT, withTekionConversion } from '@tekion/tekion-conversion-web';

import { TABLE_CONFIG } from 'appConfig';
import PROGRAM_CONFIG from 'constants/programConfig';
import { APPROVAL_REQUEST_TYPE } from 'constants/constants';
import { APPROVAL_STATUS, APPROVAL_STATUS_VS_LABEL, STATUS_VS_COLOR } from 'constants/config';
import { isRVDealerEnabled, fetchMigratedRO } from 'helpers/vehicle.helper';
import { shouldLockVehicle } from 'helpers/common';
import Card from 'molecules/Card';
import RODescription from 'molecules/RODetailsPopover/RODescription';
import PopoverCell from 'molecules/PopoverCell';
import ApprovalCellRenderer from 'molecules/cellRenderers/approvalCellRenderer';
import { showConfirmationModal } from 'organisms/ConfirmationModal';
import RoHistoryModal, { showRoHistoryModal } from 'organisms/RoHistoryModal';
import { hasApproveCostPermission } from 'permissions/inventory.permissions';
import CostApprovalAPI from 'services/costApproval';
import { redirect } from 'utils';

import { PREDEFINED_SECTIONS, PREDEFINED_SECTIONS_KEYS } from 'pages/VehicleDetailsV2/VehicleDetails.constant';

import RoPayerDetails from './Components/RoPayerDetails';
import { getColumns } from './serviceDetails.config';
import { TABLE_COLUMNS, TYPE_VS_ENUM_MAP } from './serviceDetails.constants';
import {
  getROTableData,
  isRoStatusClosedOrInvoiced,
  getROHistoryTableData,
  shouldShowRoHistoryModal,
} from './ServiceDetails.utils';

import styles from './ServiceDetailsTable.module.scss';

const cardTitle = PREDEFINED_SECTIONS[PREDEFINED_SECTIONS_KEYS.SERVICE_DETAILS]?.title;

class ServiceDetailsTable extends PureComponent {
  getColumns = defaultMemoize(isCostApprovalEnabled =>
    _map(getColumns(isCostApprovalEnabled), column => ({
      ...column,
      Cell: this.getCellRenderers(column.accessor),
    }))
  );

  renderRODetails = operations => (
    <div className="p-4">
      {_map(operations, operation => (
        <RODescription
          opcode={_get(operation, 'opcode')}
          description={_get(operation, 'opcodeDescription')}
          status={_get(operation, 'status')}
        />
      ))}
    </div>
  );

  onDeleteRow = async ({ original: rowData }) => {
    showConfirmationModal(
      {
        title: __('Deletion of Draft RO'),
        message: __('Once the draft RO is deleted, it cannot be restored. Are you sure you want to proceed?'),
        item: rowData,
      },
      async item => {
        try {
          const { getApprovalRos, vehicle } = this.props;
          const { id: vId } = vehicle || EMPTY_OBJECT;
          const { id, isLegacyData } = item?.extraRowDetails || EMPTY_OBJECT;
          await CostApprovalAPI.delete({
            id,
            legacyData: isLegacyData,
            requestType: APPROVAL_REQUEST_TYPE.RO,
          });
          await getApprovalRos(vId);
        } catch (err) {
          err.toaster(__('Failed To Delete RO'));
        }
      }
    );
  };

  onEditRow = async ({ original: rowData }) => {
    const { getApprovalRos, vehicle, editSingleJobRO } = this.props;
    const { id: vId } = vehicle || EMPTY_OBJECT;
    const onSuccessCb = async () => {
      await getApprovalRos(vId);
    };
    editSingleJobRO({ extraDetails: rowData?.extraRowDetails, onSuccessCb });
  };

  renderOpCodesContent = row => {
    const type = _get(row, [TABLE_COLUMNS.TYPE]);
    if (type === 'PO') {
      return NO_DATA;
    }
    const opCodes = _get(row, [TABLE_COLUMNS.OP_CODES]);
    const roLabel = _get(_head(opCodes), 'opcode');
    const length = _size(opCodes);

    return (
      <PopoverCell
        label={roLabel}
        length={length}
        className={styles.popoverChild}
        overlayClassName={styles.popoverLayout}
        content={this.renderRODetails(opCodes)}
      />
    );
  };

  renderAmount = ({ original: row }) => {
    const { roPayerDetails, status } = row;
    const amount = tget(row, [TABLE_COLUMNS.AMOUNT], NO_DATA);
    const shouldShowIcon =
      isRoStatusClosedOrInvoiced(status) &&
      !_isEmpty(roPayerDetails) &&
      PROGRAM_CONFIG.shouldShowIconInROAndPODetails();
    return (
      <>
        <Content>{amount}</Content>
        <PropertyControlledComponent controllerProperty={shouldShowIcon}>
          <IconWithPopover
            icon="icon-info"
            size={SIZES.MD_S}
            iconClass="m-l-4"
            content={<RoPayerDetails roPayerDetails={roPayerDetails} />}
          />
        </PropertyControlledComponent>
      </>
    );
  };

  getRouteLink = (type, id) => TYPE_VS_ENUM_MAP[type](id);

  updateVehicle = async (response, isApprove) => {
    const { getApprovalRos, vehicle, refetchVehicleDetails, currentLanguageId } = this.props;
    const { id: vId } = vehicle || EMPTY_OBJECT;
    await getApprovalRos(vId, response);
    if (isApprove) await refetchVehicleDetails(vId, null, currentLanguageId);
  };

  getCellRenderers = key => {
    let Cell = null;
    switch (key) {
      case TABLE_COLUMNS.RO_PO_NUMBER:
        Cell = ({ original }) => {
          const { number, id, type } = original;
          return !isRVDealerEnabled() ? (
            <Button view="link" onClick={() => redirect(this.getRouteLink(type, id))}>
              {number}
            </Button>
          ) : (
            <Content>{number}</Content>
          );
        };
        break;
      case TABLE_COLUMNS.OP_CODES:
        Cell = ({ original }) => this.renderOpCodesContent(original);
        break;
      case TABLE_COLUMNS.AMOUNT:
        Cell = this.renderAmount;
        break;
      case TABLE_COLUMNS.STATUS:
        Cell = ({ original }) => {
          const { isCostApprovalEnabled } = this.props;
          const { status } = original?.extraRowDetails || EMPTY_OBJECT;
          if (status === APPROVAL_STATUS.APPROVED || !PROGRAM_CONFIG.shouldShowCostApproval() || !isCostApprovalEnabled)
            return <Content>{RO_STATUS_LABEL_MAP[_get(original, 'status')] || NO_DATA}</Content>;

          return status ? (
            <StatusItem label={APPROVAL_STATUS_VS_LABEL[status]} color={STATUS_VS_COLOR[status]} />
          ) : (
            NO_DATA
          );
        };
        break;

      case TABLE_COLUMNS.CLOSED_DATE:
        Cell = ({ original }) => {
          const value = _get(original, [TABLE_COLUMNS.CLOSED_DATE]);
          const { getFormattedDateAndTime } = this.props;
          return value
            ? getFormattedDateAndTime({ value, formatType: DATE_TIME_FORMAT.DATE_ABBREVIATED_MONTH_YEAR })
            : NO_DATA;
        };
        break;
      case TABLE_COLUMNS.NEXT_ACTION:
        Cell = ({ original }) => {
          const { vehicle } = this.props;
          const { id: vId, status: vehicleStatus } = vehicle || EMPTY_OBJECT;
          const { id, status } = original?.extraRowDetails || EMPTY_OBJECT;
          return (
            <PropertyControlledComponent
              controllerProperty={status === APPROVAL_STATUS.PENDING && hasApproveCostPermission()}
              fallback={NO_DATA}
            >
              <ApprovalCellRenderer
                requestType={APPROVAL_REQUEST_TYPE.RO}
                onAction={this.updateVehicle}
                value={id}
                disabled={shouldLockVehicle(vehicleStatus)}
              />
            </PropertyControlledComponent>
          );
        };
        break;
      case TABLE_COLUMNS.MENU:
        Cell = ({ original, index }) => {
          const { vehicle } = this.props;
          const { status: vehicleStatus } = vehicle || EMPTY_OBJECT;
          const { status } = original?.extraRowDetails || EMPTY_OBJECT;
          return (
            <PropertyControlledComponent controllerProperty={status === APPROVAL_STATUS.PENDING} fallback={<></>}>
              <EditDeletePopover
                onEdit={this.onEditRow}
                onDelete={this.onDeleteRow}
                data={{
                  original,
                  index,
                }}
                disabled={shouldLockVehicle(vehicleStatus)}
              />
            </PropertyControlledComponent>
          );
        };
        break;
      default:
        Cell = null;
    }
    return Cell;
  };

  fetchMigratedRO = async () => {
    const { vehicle } = this.props;
    const { vin } = vehicle;
    const data = await fetchMigratedRO(vin);
    const roTableData = _map(data, getROTableData);
    this.showRoHistoryModal(roTableData);
  };

  showRoHistoryModal = data => {
    showRoHistoryModal({
      title: __('RO History'),
      message: PROGRAM_CONFIG.shouldShowTransferPage()
        ? __('The list of RO’s associated with the vehicle before migration/transfer')
        : __(`List of RO's associated with the vehicle before migration`),
      tableData: data,
    });
  };

  handleROHistoryClick = () => {
    if (PROGRAM_CONFIG.shouldShowTransferPage()) {
      const { roHistoryDetails } = this.props;
      const roTableData = _map(roHistoryDetails, getROHistoryTableData);
      this.showRoHistoryModal(roTableData);
    } else {
      this.fetchMigratedRO();
    }
  };

  renderExtraHeader = () => {
    const { roHistoryDetails } = this.props;
    return (
      <PropertyControlledComponent controllerProperty={shouldShowRoHistoryModal(roHistoryDetails)}>
        <Button view={Button.VIEW.TERTIARY} onClick={this.handleROHistoryClick} highlightOnHover={false}>
          {__('RO History')}
        </Button>
      </PropertyControlledComponent>
    );
  };

  renderCardTitle = () => {
    const { predefinedSections, predefinedSectionKeys } = this.props;
    return <Heading size={2}>{predefinedSections[predefinedSectionKeys.SERVICE_DETAILS]?.title}</Heading>;
  };

  renderROTable = (className = EMPTY_STRING) => {
    const { data, isCostApprovalEnabled } = this.props;
    return (
      <BaseTable
        data={data}
        columns={this.getColumns(isCostApprovalEnabled)}
        showPagination={false}
        pageSize={data.length}
        rowHeight={TABLE_CONFIG.ROW_HEIGHT}
        minRows={data.length}
        className={className}
      />
    );
  };

  renderRoPoTable = () => {
    const { predefinedSectionKeys, isReadOnly } = this.props;
    if (PROGRAM_CONFIG.shouldShowCostApproval()) {
      return (
        <>
          <Heading size={3} className="m-y-16">
            <div className="d-flex justify-content-between">
              <div>{cardTitle}</div>
              <div>{this.renderExtraHeader()}</div>
            </div>
          </Heading>
          {this.renderROTable()}
        </>
      );
    }
    return (
      <Card
        id={predefinedSectionKeys.SERVICE_DETAILS}
        title={this.renderCardTitle()}
        extra={this.renderExtraHeader()}
        isReadOnly={isReadOnly}>
        {this.renderROTable()}
      </Card>
    );
  };

  render() {
    const { showTableOnly, tableClassName, isReadOnly, roHistoryDetails } = this.props;
    const containerClassName = cx(tableClassName, { 'pointer-events-none': isReadOnly });
    return (
      <PropertyControlledComponent
        controllerProperty={!showTableOnly}
        fallback={this.renderROTable(containerClassName)}>
        {this.renderRoPoTable()}

        <PropertyControlledComponent controllerProperty={shouldShowRoHistoryModal(roHistoryDetails)}>
          <RoHistoryModal />
        </PropertyControlledComponent>
      </PropertyControlledComponent>
    );
  }
}

ServiceDetailsTable.propTypes = {
  data: PropTypes.array.isRequired,
  vehicle: PropTypes.object.isRequired,
  getFormattedDateAndTime: PropTypes.func,
  predefinedSections: PropTypes.object,
  predefinedSectionKeys: PropTypes.object,
  tableClassName: PropTypes.string,
  showTableOnly: PropTypes.bool,
  isCostApprovalEnabled: PropTypes.bool.isRequired,
  getApprovalRos: PropTypes.func,
  editSingleJobRO: PropTypes.func.isRequired,
  refetchVehicleDetails: PropTypes.func,
  currentLanguageId: PropTypes.string,
  isReadOnly: PropTypes.bool,
};

ServiceDetailsTable.defaultProps = {
  getFormattedDateAndTime: _noop,
  predefinedSections: EMPTY_OBJECT,
  predefinedSectionKeys: EMPTY_OBJECT,
  tableClassName: EMPTY_STRING,
  showTableOnly: false,
  getApprovalRos: _noop,
  refetchVehicleDetails: _noop,
  currentLanguageId: EMPTY_STRING,
  isReadOnly: false,
};

export default compose(withTekionConversion)(ServiceDetailsTable);
