import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import _map from 'lodash/map';
import _sumBy from 'lodash/sumBy';
import _filter from 'lodash/filter';
import _includes from 'lodash/includes';
import _isEqual from 'lodash/isEqual';
import _reduce from 'lodash/reduce';
import _get from 'lodash/get';
import _noop from 'lodash/noop';

import { RO_STATUS_VALUES } from 'tbase/constants/repairOrder/roStatus';
import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from 'tbase/app.constants';
import VehicleReader from 'tbusiness/appServices/vehicleInventory/readers/vehicle';

import PROGRAM_CONFIG from 'constants/programConfig';
import { APPROVAL_STATUS } from 'constants/config';
import { APPROVAL_REQUEST_TYPE } from 'constants/constants';
import VIEvents, { EVENT_TYPES } from 'event';
import { isRVDealerEnabled, isVehicleInCurrentLoggedInSite } from 'helpers/vehicle.helper';
import RepairOrderAPIs from 'services/repairOrder';
import CostApprovalAPI from 'services/costApproval';

import ServiceDetailsTable from './ServiceDetailsTable';
import { getROTableData, getPOTableData, getExtraRowDetails } from './ServiceDetails.utils';

const DEFAULT_STATE = {
  roTableData: EMPTY_ARRAY,
};

class ServiceDetails extends PureComponent {
  constructor(props) {
    super(props);
    this.state = DEFAULT_STATE;
  }

  componentDidMount() {
    this.init();
    VIEvents.on(EVENT_TYPES.REFETCH_RO_APPROVAL_DATA, this.getApprovalRos);
  }

  componentDidUpdate(prevProps) {
    const { roDetails } = this.props;
    if (!_isEqual(roDetails, prevProps.roDetails)) {
      this.init();
    }
  }

  componentWillUnmount() {
    VIEvents.removeListener(EVENT_TYPES.REFETCH_RO_APPROVAL_DATA, this.getApprovalRos);
  }

  getROV3Details = () => {
    const { vehicle } = this.props;
    const { id } = vehicle || EMPTY_OBJECT;
    if (id) return RepairOrderAPIs.fetchROV3Details(id);
    return EMPTY_ARRAY;
  };

  getApprovalRos = async (vehicleId = EMPTY_STRING, onSuccessResponse = EMPTY_OBJECT) => {
    const { fetchCostSummary } = this.props;
    try {
      const response = await CostApprovalAPI.getAll({
        vehicleId,
        requestType: APPROVAL_REQUEST_TYPE.RO,
      });
      const data = _get(response, 'data', EMPTY_ARRAY);
      const roTableData = _map(data, ro => {
        const roDetails = _get(ro, 'roDetails');
        const extraRowDetails = getExtraRowDetails(ro);
        if (_get(onSuccessResponse, 'data.status') === APPROVAL_STATUS.APPROVED) {
          fetchCostSummary();
        }

        return getROTableData(roDetails, undefined, extraRowDetails);
      });
      this.setState({ roTableData });
    } catch (err) {
      err.toaster(__('Failed To fetch RO Details'));
    }
  };

  init = async () => {
    const { roDetails, vehicle, form } = this.props;
    const shouldShowROData = isVehicleInCurrentLoggedInSite(vehicle);
    if (!shouldShowROData) {
      return;
    }
    if (PROGRAM_CONFIG.shouldShowCostApproval()) {
      const vehicleId = VehicleReader.id(vehicle);
      await this.getApprovalRos(vehicleId);
      return;
    }
    try {
      let roTableData = [];
      const isRVDealer = isRVDealerEnabled();
      const vin = VehicleReader.vin(vehicle);
      if (isRVDealer) {
        const stockNumber = VehicleReader.stockId(vehicle);
        if (vin || stockNumber) {
          const response = await RepairOrderAPIs.fetchRODetailsRV({
            vin,
            stockNumber,
          });
          roTableData = _map(response, data => getROTableData(data, isRVDealer));
          form.change(
            'pricingDetails.workOrders',
            _sumBy(
              _filter(response, data => data?.status === RO_STATUS_VALUES.CLOSED),
              'internalRoTotal'
            )
          );
          form.change(
            'pricingDetails.workOrderEstimate',
            _sumBy(
              _filter(response, data => _includes([RO_STATUS_VALUES.IN_PROGRESS], data?.status)),
              'internalRoTotal'
            )
          );
        }
      } else {
        let roData;
        if (PROGRAM_CONFIG.shouldUseServiceV3()) {
          const {
            data: { ongoingRoDetails },
          } = await this.getROV3Details();
          roData = ongoingRoDetails;
        } else {
          const { data } = await RepairOrderAPIs.fetchRODetails(_map(roDetails, ro => ro?.roId));
          roData = data;
        }

        roTableData = _reduce(
          roData,
          (filteredROData, ro) => {
            if (!ro?.metadata?.isTransferred) filteredROData.push(getROTableData(ro));
            return filteredROData;
          },
          []
        );
      }
      this.setState({ roTableData });
    } catch (err) {
      err.toaster(__('Failed To fetch RO Details'));
    }
  };

  render() {
    const { roTableData } = this.state;
    const { poData, vehicle, roDetails, editSingleJobRO, isCostApprovalEnabled, isReadOnly, ...rest } = this.props;
    const poTableData = _map(poData, getPOTableData);
    const shouldShowROnPOData = isVehicleInCurrentLoggedInSite(vehicle);
    const roHistoryDetails = _filter(roDetails, ro => ro?.metadata?.isTransferred);

    return (
      <ServiceDetailsTable
        data={shouldShowROnPOData ? [...roTableData, ...poTableData] : []}
        vehicle={vehicle}
        getApprovalRos={this.getApprovalRos}
        editSingleJobRO={editSingleJobRO}
        isCostApprovalEnabled={isCostApprovalEnabled}
        roHistoryDetails={roHistoryDetails}
        isReadOnly={isReadOnly}
        {...rest}
      />
    );
  }
}

ServiceDetails.propTypes = {
  roDetails: PropTypes.array.isRequired,
  poData: PropTypes.object.isRequired,
  vehicle: PropTypes.object.isRequired,
  editSingleJobRO: PropTypes.func.isRequired,
  isCostApprovalEnabled: PropTypes.bool.isRequired,
  fetchCostSummary: PropTypes.func,
  isReadOnly: PropTypes.bool,
};

ServiceDetails.defaultProps = {
  fetchCostSummary: _noop,
  isReadOnly: false,
};

export default ServiceDetails;
