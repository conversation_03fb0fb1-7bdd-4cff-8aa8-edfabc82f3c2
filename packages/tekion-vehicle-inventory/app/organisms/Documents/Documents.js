import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';

import { EMPTY_OBJECT } from 'tbase/app.constants';
import { tget } from 'tbase/utils/general';
import DocumentsTable from 'twidgets/appServices/sales/organisms/documentsTable';
import { DOCUMENT_VIEW } from 'twidgets/appServices/sales/organisms/documentsTable/documentsTable.constants';
import {
  hasViDocsAddPlaceHolderPermission,
  hasViDocsRemoveUploadedDocumentPermission,
  hasViDocsRemovePlaceHolderPermission,
  hasViDocsAdhocUploadPermission,
  hasViewAndManageSensitiveDocumentPermission,
} from 'permissions/inventory.permissions';

import { DOCUMENTS_ACTIONS } from './documents.constants';
import {
  mapFormCategories,
  getFormsToBeMapped,
  getEditableDocuments,
  getDisabledDocumentIds,
} from './documents.helpers';
import styles from './documents.module.scss';

const VehicleDocuments = ({
  loading,
  documents,
  tableView,
  selectedFormIds,
  onAction,
  showUploadScreen,
  customFormCategories,
  searchResults,
  searchText,
  visibilityConfig,
  showConfirmationModal,
  documentIdToBeDeleted,
  confirmationLoading,
  alerts,
  preSelectedDocumentIdToMap,
  placeHolders,
  invoiceData,
  isInitDocumentsFetched,
  files,
  totalArchivedDocs,
  isReadOnlyDetails,
}) => {
  useEffect(() => {
    if (!isInitDocumentsFetched) onAction({ type: DOCUMENTS_ACTIONS.INIT });

    if (isInitDocumentsFetched && invoiceData) {
      onAction({ type: DOCUMENTS_ACTIONS.REFETCH_DECUMENTS });
    }
  }, [invoiceData]);

  const handleActionClick = useCallback(
    type => payload => {
      onAction({
        type,
        payload,
      });
      return 'success';
    },
    [onAction]
  );

  const categories = useMemo(() => mapFormCategories(customFormCategories), [customFormCategories]);

  const formsToBeMapped = useMemo(() => getFormsToBeMapped(documents), [documents]);

  const disableDrag = useMemo(
    () => tableView === DOCUMENT_VIEW.CATEGORY || isReadOnlyDetails,
    [tableView, isReadOnlyDetails]
  );

  const rows = useMemo(() => getEditableDocuments(searchText ? searchResults : documents), [
    documents,
    searchText,
    searchResults,
  ]);

  const disabledDocumentIds = useMemo(() => getDisabledDocumentIds(rows), [rows]);

  const filePickerRef = useRef(null);

  const handleUploadDocuments = useCallback(
    (event = EMPTY_OBJECT) => {
      const { files: uploadedFiles } = tget(event, 'target', EMPTY_OBJECT);
      onAction({
        type: DOCUMENTS_ACTIONS.HIDE_SHOW_UPLOAD,
        payload: { shouldShowModal: true, fileList: uploadedFiles, preSelectedDocumentIdToMap },
      });
    },
    [onAction, preSelectedDocumentIdToMap]
  );

  const handleUploadClick = useCallback(
    sourceDocumentId => {
      const response = filePickerRef?.current?.openFilePicker();
      if (!response) return;
      onAction({
        type: DOCUMENTS_ACTIONS.SET_UPLOAD_PRESELECTED_FORM_ID,
        payload: { sourceDocumentId },
      });
    },
    [onAction, filePickerRef]
  );

  const handleTableItemSelect = useCallback(
    payload => {
      onAction({
        type: DOCUMENTS_ACTIONS.SELECT,
        payload: { selectedDocuments: payload, disabledDocumentIds },
      });
    },
    [onAction, disabledDocumentIds]
  );

  const handleArchiveToggle = useCallback(
    archiveToggle => {
      onAction({
        type: DOCUMENTS_ACTIONS.ARCHIVE_TOGGLE,
        payload: { archiveToggle },
      });
    },
    [onAction]
  );

  const documentPermissions = {
    ADD_PLACEHOLDER: hasViDocsAddPlaceHolderPermission(),
    ADHOC_UPLOAD: hasViDocsAdhocUploadPermission(),
    REMOVE_PLACEHOLDER: hasViDocsRemovePlaceHolderPermission(),
    REMOVE_UPLOADED_DOCUMENT: hasViDocsRemoveUploadedDocumentPermission(),
    VIEW_MANAGE_SENSITIVE_DOCUMENT: hasViewAndManageSensitiveDocumentPermission(),
  };

  return (
    <div className={classNames('full-height', styles.documentsTabBody)}>
      <DocumentsTable
        documents={rows}
        disabledDocumentIds={disabledDocumentIds}
        customFormCategories={customFormCategories}
        selectedItems={selectedFormIds}
        selectedView={tableView}
        onAction={onAction}
        loading={loading}
        visibilityConfig={visibilityConfig}
        showUploadScreen={showUploadScreen}
        onActionClick={handleActionClick}
        onDocumentViewChange={handleActionClick(DOCUMENTS_ACTIONS.VIEW_CHANGE)}
        selectedFormIds={selectedFormIds}
        showConfirmationModal={showConfirmationModal}
        documentIdToBeDeleted={documentIdToBeDeleted}
        onConfirmationCancel={handleActionClick(DOCUMENTS_ACTIONS.HIDE_CONFIRMATION_MODAL)}
        onConfirm={handleActionClick(DOCUMENTS_ACTIONS.CONFIRM_DELETE)}
        confirmationLoading={confirmationLoading}
        onTableItemSelect={handleTableItemSelect}
        onColumnSort={handleActionClick(DOCUMENTS_ACTIONS.DRAG_AND_DROP)}
        onRowUpdate={handleActionClick(DOCUMENTS_ACTIONS.DRAG_AND_DROP)}
        hideRowDrag={disableDrag}
        onShowUploadScreen={handleActionClick(DOCUMENTS_ACTIONS.HIDE_SHOW_UPLOAD)}
        onSearch={handleActionClick(DOCUMENTS_ACTIONS.SEARCH)}
        onUploadBackIconClick={handleActionClick(DOCUMENTS_ACTIONS.HIDE_SHOW_UPLOAD)}
        formCategories={categories}
        documentsToBeMapped={formsToBeMapped}
        onDocumentsSave={handleActionClick(DOCUMENTS_ACTIONS.UPLOAD_DOCUMENT)}
        onPrint={handleActionClick(DOCUMENTS_ACTIONS.PRINT)}
        remainders={alerts}
        sortableContClassName={classNames(styles.sortableTableContainer, {
          [styles.readOnlyTableContainer]: isReadOnlyDetails,
        })}
        preSelectedDocumentIdToMap={preSelectedDocumentIdToMap}
        onAddPlaceHolder={handleActionClick(DOCUMENTS_ACTIONS.ADD_PLACEHOLDER)}
        placeHolders={placeHolders}
        documentPermissions={documentPermissions}
        onUploadClick={handleUploadClick}
        filePickerRef={filePickerRef}
        onUploadDocuments={handleUploadDocuments}
        files={files}
        onArchiveToggle={handleArchiveToggle}
        totalArchivedDocs={totalArchivedDocs}
        documentHeaderContainerClassName={isReadOnlyDetails ? 'pointer-events-none' : ''}
        documentCellContainerClassName={isReadOnlyDetails ? 'pointer-events-auto' : ''}
      />
    </div>
  );
};

VehicleDocuments.propTypes = {
  loading: PropTypes.bool.isRequired,
  documents: PropTypes.array.isRequired,
  tableView: PropTypes.bool.isRequired,
  selectedFormIds: PropTypes.array.isRequired,
  onAction: PropTypes.func.isRequired,
  showUploadScreen: PropTypes.bool.isRequired,
  customFormCategories: PropTypes.array.isRequired,
  searchResults: PropTypes.array.isRequired,
  searchText: PropTypes.string.isRequired,
  visibilityConfig: PropTypes.object.isRequired,
  showConfirmationModal: PropTypes.bool.isRequired,
  documentIdToBeDeleted: PropTypes.any.isRequired,
  confirmationLoading: PropTypes.bool.isRequired,
  alerts: PropTypes.array.isRequired,
  preSelectedDocumentIdToMap: PropTypes.string.isRequired,
  placeHolders: PropTypes.array.isRequired,
  invoiceData: PropTypes.array.isRequired,
  isInitDocumentsFetched: PropTypes.bool.isRequired,
  isReadOnlyDetails: PropTypes.bool.isRequired,
};

export default React.memo(VehicleDocuments);
