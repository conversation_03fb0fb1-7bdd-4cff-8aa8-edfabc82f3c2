import React from 'react';
import cx from 'classnames';
import PropTypes from 'prop-types';

import Ellipsis from 'tcomponents/atoms/Ellipsis';
import Content from 'tcomponents/atoms/Content';
import PropertyControlledComponent from 'tcomponents/molecules/PropertyControlledComponent';
import { PURCHASE_DETAILS_STATUS } from 'twidgets/constants/vi.constants';

import styles from './roDetailsPopover.module.scss';

const RODescription = ({ opcode, description, status, note }) => {
  const isVoided = status === PURCHASE_DETAILS_STATUS.VOIDED;

  return (
    <Ellipsis lines={2}>
      <div className="d-flex flex-row mb-2 mt-2">
        <Content className={cx(styles.content, { [styles.strikeThrough]: isVoided })}>{opcode}</Content>
        <Content
          className={cx(styles.descriptionContent, {
            [styles.strikeThrough]: isVoided,
          })}
          colorVariant={Content.COLOR_VARIANTS.GREY}>
          {description}
        </Content>
      </div>
      <PropertyControlledComponent controllerProperty={note}>
        <Content className={cx(styles.note, { [styles.strikeThrough]: isVoided })}>{note}</Content>
      </PropertyControlledComponent>
    </Ellipsis>
  );
};

RODescription.propTypes = {
  opcode: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  status: PropTypes.string.isRequired,
  note: PropTypes.string,
};

RODescription.defaultProps = {
  note: undefined,
};

export default RODescription;
