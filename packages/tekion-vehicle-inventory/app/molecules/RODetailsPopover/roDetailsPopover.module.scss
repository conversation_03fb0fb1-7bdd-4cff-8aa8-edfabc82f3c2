@import "tstyles/component.scss";

:export {
  completed: $parisGreen;
  inProgress: $carrotOrange;
  notStarted: $platinum;
}

.content {
  &::after {
    content: "-";
    margin: 0 0.5rem;
  }
  white-space: nowrap;
}

.descriptionContent {
  max-width: 18rem;
  word-break: break-word;
}

.disabledLink {
  pointer-events: none;
}

.icon {
  color: $atomic;
}

.roLink {
  &:hover > div {
    color: $denim;
  }
}

.strikeThrough {
  text-decoration: line-through;
}

.note{
  font-style: italic;
  margin-bottom: 0.4rem;
}