import React, { memo } from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';

import { EMPTY_STRING } from 'tbase/app.constants';
import TCard from 'tcomponents/molecules/Card';

import styles from './card.module.scss';

const Card = ({ children, title, className, isReadOnly, ...rest }) => (
  <TCard
    title={title}
    bordered={false}
    className={cx(styles.container, className, { 'pointer-events-none opacity65': isReadOnly })}
    {...rest}>
    {children}
  </TCard>
);

Card.propTypes = {
  children: PropTypes.object.isRequired,
  title: PropTypes.string,
  className: PropTypes.string,
  isReadOnly: PropTypes.bool,
};

Card.defaultProps = {
  title: EMPTY_STRING,
  className: EMPTY_STRING,
  isReadOnly: false,
};

export default memo(Card);
