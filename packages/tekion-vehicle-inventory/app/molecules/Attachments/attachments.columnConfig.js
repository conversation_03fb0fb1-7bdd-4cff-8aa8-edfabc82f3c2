import cx from 'classnames';
import { mapProps } from 'recompose';

import _get from 'lodash/get';

import { DateCellRenderer } from 'tcomponents/molecules/tableInputField/cellRenderers';
import KebabMenu from 'tcomponents/molecules/KebabMenu';
import { DATE_TIME_FORMAT } from '@tekion/tekion-conversion-web';

import FileName from './components/FileName';
import { COLUMN_IDS, getRowActionsConfig } from './attachments.config';
import { getCreatedTime } from './attachments.helpers';

import styles from './Attachments.module.scss';

export const getColumnConfig = (getPresignedURLList, triggerElement, handleKebabMenuAction, isReadOnly) => [
  {
    id: COLUMN_IDS.FILE_NAME,
    Header: __('File Name'),
    Cell: mapProps(({ original }) => ({
      file: original,
      getPresignedURLList,
    }))(FileName),
    sortable: false,
    minWidth: 240,
  },
  {
    id: COLUMN_IDS.UPLOADED_ON,
    Header: __('Uploaded on'),
    Cell: mapProps(({ original }) => ({
      data: getCreatedTime(original),
      format: DATE_TIME_FORMAT.DATE_ABBREVIATED_MONTH_YEAR_WITH_HOUR_MINUTE,
    }))(DateCellRenderer),
    minWidth: 180,
    sortable: false,
  },
  {
    id: COLUMN_IDS.MENU,
    Cell: mapProps(({ original }) => ({
      menuItems: getRowActionsConfig(_get(original, ['mediaList', 0, 'sensitive'])),
      triggerElement,
      rowInfo: original,
      onClickAction: handleKebabMenuAction,
      disabled: isReadOnly,
      className: cx({ [styles.cursorDisabled]: isReadOnly }),
    }))(KebabMenu),
    width: 40,
    sortable: false,
    resizable: false,
  },
];
