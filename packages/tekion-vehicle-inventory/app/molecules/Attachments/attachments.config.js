import { IconWithText } from 'tcomponents/atoms';

import { hasAttachmentsEdit, hasViewAndManageSensitiveDocumentPermission } from 'permissions/inventory.permissions';

import styles from './Attachments.module.scss';
import { ROW_ACTION_KEYS } from './attachments.constants';

export const COLUMN_IDS = {
  FILE_NAME: 'fileName',
  UPLOADED_ON: 'createdTime',
  MENU: 'menu',
};

export const getRowActionsConfig = isSensitive => [
  ...(isSensitive
    ? [
        {
          key: ROW_ACTION_KEYS.UNMARK_ATTACHMENT_SENSITIVE,
          renderer: IconWithText,
          icon: 'icon-preview',
          textContent: __('Unmark as Sensitive'),
          className: styles.kebabMenuItems,
          disabled: !hasAttachmentsEdit() || !hasViewAndManageSensitiveDocumentPermission(),
        },
      ]
    : [
        {
          key: ROW_ACTION_KEYS.MARK_ATTACHMENT_SENSITIVE,
          renderer: IconWithText,
          icon: 'icon-hide',
          textContent: __('Mark as Sensitive'),
          className: styles.kebabMenuItems,
          disabled: !hasAttachmentsEdit() || !hasViewAndManageSensitiveDocumentPermission(),
        },
      ]),
  {
    key: ROW_ACTION_KEYS.DELETE_ATTACHMENT,
    renderer: IconWithText,
    icon: 'icon-trash',
    textContent: __('Delete'),
    disabled: (isSensitive && !hasViewAndManageSensitiveDocumentPermission()) || !hasAttachmentsEdit(),
    className: styles.kebabMenuItems,
  },
];
