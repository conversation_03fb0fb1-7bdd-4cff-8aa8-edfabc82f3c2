@import "tstyles/component.scss";

.generalAttachmentTable {
  max-height: 39rem;
}

.tableContainer {
  :global(.rt-tbody) {
    height: 16rem;
    overflow-y: auto;
  }
  margin-top: 2.4rem;
  .fileURL {
    color: $black;
    @include is-underlined();
  }
  .row {
    padding: 1.2rem 0rem !important ;
  }
  }

.statusColumn {
  @include flex($justify-content: space-between);
  @include full-width;
  .statusContent {
    @include flex();
    white-space: normal;
    .loader {
      height: 1.6rem;
      width: 1.6rem;
    }
    .padding {
      padding-left: 0.8rem;
    }
    .uploadConfirmation {
      color: $parisGreen;
    }
    .uploadFailed {
      color: $mordantRed;
    }
  }
  .deleteButton {
    margin-right: 1.6rem;
  }
}

.contentContainer {
  display: flex;
  gap: 2rem;
}

.tooltip{
  :global(.ant-tooltip-inner) {
    background-color: $riverBedGray;
    min-width: 45rem;
  }
}

.kebabMenu {
  cursor: pointer;
  &:hover {
    color: $denim;
  }
  transform: rotate(90deg);
}

.kebabMenuItems {
  color: inherit;
}

.cursorDisabled {
  cursor: not-allowed;
}