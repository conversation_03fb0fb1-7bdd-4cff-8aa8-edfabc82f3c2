import React, { PureComponent } from 'react';
import cx from 'classnames';
import PropTypes from 'prop-types';
import _concat from 'lodash/concat';
import _noop from 'lodash/noop';

import { EMPTY_ARRAY, EMPTY_STRING } from 'tbase/app.constants';
import { tget } from 'tbase/utils/general';
import Heading from 'tcomponents/atoms/Heading';
import PropertyControlledComponent from 'tcomponents/molecules/PropertyControlledComponent';
import Tooltip from 'tcomponents/atoms/tooltip';
import BaseTable from 'tcomponents/molecules/table/BaseTable';
import withMediaUploader from 'twidgets/connectors/withMediaUploader';

import { TABLE_CONFIG } from 'appConfig';
import { showAddAttachmentDrawer } from 'event';
import { DragAndDropContainer, Attachment } from 'molecules';
import { ATTACHMENT } from 'constants/constants';
import PROGRAM_CONFIG from 'constants/programConfig';

import { ROW_ACTION_KEYS } from './attachments.constants';
import { getAttachmentLists } from './attachments.helpers';
import UploadAttachmentDrawer from './components/uploadAttachmentDrawer';
import { getColumnConfig } from './attachments.columnConfig';

import styles from './Attachments.module.scss';

const triggerElement = <span className={`icon-overflow ${styles.kebabMenu}`} />;

class Attachments extends PureComponent {
  static propTypes = {
    onUploadFiles: PropTypes.func,
    onDeleteFiles: PropTypes.func,
    onModifySensitivity: PropTypes.func,
    getPresignedURLList: PropTypes.func,
    showHeading: PropTypes.bool,
    attachments: PropTypes.array,
    uploadMediaList: PropTypes.func,
    isReadOnly: PropTypes.bool,
  };

  static defaultProps = {
    onUploadFiles: _noop,
    onDeleteFiles: _noop,
    onModifySensitivity: _noop,
    getPresignedURLList: _noop,
    showHeading: true,
    attachments: EMPTY_ARRAY,
    uploadMediaList: _noop,
    isReadOnly: false,
  };

  constructor(props) {
    super(props);
    this.state = {
      selectedFiles: EMPTY_ARRAY,
    };
    const { getPresignedURLList, isReadOnly } = this.props;
    this.columnsData = getColumnConfig(getPresignedURLList, triggerElement, this.handleKebabMenuAction, isReadOnly);
  }

  handleKebabMenuAction = (key, rowInfo) => {
    switch (key) {
      case ROW_ACTION_KEYS.DELETE_ATTACHMENT:
        this.onDelete(rowInfo);
        break;
      case ROW_ACTION_KEYS.MARK_ATTACHMENT_SENSITIVE:
      case ROW_ACTION_KEYS.UNMARK_ATTACHMENT_SENSITIVE:
        this.handleModifySensitivity(rowInfo);
        break;
      default:
        break;
    }
  };

  onGotFilesToUpload = files => {
    showAddAttachmentDrawer({ files });
  };

  onDelete = fileToDelete => {
    const { onDeleteFiles } = this.props;
    const mediaFile = tget(fileToDelete, 'mediaFile', EMPTY_STRING);
    onDeleteFiles(mediaFile);
  };

  handleModifySensitivity = row => {
    const { onModifySensitivity } = this.props;
    const mediaFile = tget(row, 'mediaFile', EMPTY_STRING);
    onModifySensitivity(mediaFile);
  };

  render() {
    const { selectedFiles } = this.state;
    const { showHeading, attachments = EMPTY_ARRAY, uploadMediaList, onUploadFiles, isReadOnly } = this.props;
    const attachmentList = getAttachmentLists(attachments);
    const tableData = _concat(selectedFiles, attachmentList);

    // NOTE: Have to Disable DnD when already uploading
    return (
      <>
        <Tooltip title={isReadOnly ? __("You don't have permission to upload documents.") : EMPTY_STRING}>
          <div className={cx({ [styles.cursorDisabled]: isReadOnly })}>
            <div className={cx({ disableScreen: isReadOnly })}>
              <DragAndDropContainer onDrop={this.onGotFilesToUpload}>
                <div className={styles.generalAttachmentTable}>
                  <PropertyControlledComponent controllerProperty={!PROGRAM_CONFIG.shouldHideUploadMedia()}>
                    <Attachment
                      title={ATTACHMENT.TITLE}
                      message={ATTACHMENT.MESSAGE}
                      onSelection={this.onGotFilesToUpload}
                      multiple
                    />
                  </PropertyControlledComponent>
                  {showHeading && (
                    <Heading size={3} className="m-t-16">
                      {__('Attachments')}
                    </Heading>
                  )}
                </div>
              </DragAndDropContainer>
            </div>
          </div>
        </Tooltip>
        <div className={styles.tableContainer}>
          <BaseTable
            data={tableData}
            columns={this.columnsData}
            showPagination={false}
            pageSize={tableData.length}
            rowHeight={TABLE_CONFIG.ROW_HEIGHT}
            minRows={tableData.length}
          />
        </div>
        <UploadAttachmentDrawer uploadMediaList={uploadMediaList} onUploadFiles={onUploadFiles} />
      </>
    );
  }
}

export default withMediaUploader(Attachments);
