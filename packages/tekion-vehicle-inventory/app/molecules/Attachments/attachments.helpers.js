import _get from 'lodash/get';
import _reduce from 'lodash/reduce';
import _isEmpty from 'lodash/isEmpty';
import _map from 'lodash/map';
import _head from 'lodash/head';

import { tget } from 'tbase/utils/general';
import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from 'tbase/app.constants';
import FileReader from 'tbase/utils/File.reader';

import { MAX_FILE_SIZE } from 'constants/constants';

import { FILE_STATUS_CONFIG } from './attachments.constants';

export const getStatus = file => {
  const { mediaFile } = file || EMPTY_OBJECT;
  const fileSize = file && FileReader.getFileSize(file);
  if (fileSize >= MAX_FILE_SIZE) return FILE_STATUS_CONFIG.WARNING;
  if (!mediaFile) return FILE_STATUS_CONFIG.PROGRESS;
  return FILE_STATUS_CONFIG.UPLOADED;
};

export const getCreatedTime = file => {
  const { mediaList } = file || EMPTY_OBJECT;
  return _get(mediaList, '0.createdTime');
};

export const getAttachmentLists = attachments =>
  _reduce(
    attachments,
    (acc, attachment) => {
      const mediaList = tget(attachment, 'mediaList', EMPTY_ARRAY);
      if (_isEmpty(mediaList)) return acc;
      const updatedMediaList = _map(mediaList, media => {
        const { mediaId, mediaFile, url, type, name, fileName } = media || EMPTY_OBJECT;
        const fileNameOrDefault = name || fileName || mediaId;
        return {
          mediaFile: mediaFile || mediaId,
          url,
          name: fileNameOrDefault,
          type,
          mediaList: [media],
        };
      });
      return [...acc, ...updatedMediaList];
    },
    []
  );

export const getFileName = (file, mediaList) =>
  _get(file, 'name') || _get(_head(mediaList), 'fileName') || EMPTY_STRING;
