import SOURCE, { SOURCE_ENUM } from 'tbase/constants/vehicleInventory/sourceType';
import { VEHICLE_TYPES } from 'tbase/constants/vehicleInventory/vehicleTypes';
import STATUS from 'tbase/constants/purchaseOrder/status';
import { PURCHASE_DETAILS_STATUS } from 'twidgets/constants/vi.constants';

export const VEHICLE_TYPE = {
  NEW: __('New'),
  USED: __('Used'),
  SPECIAL: __('Special'),
  DEMO: __('Demo / Service'),
};

export const INVENTORY_STATUS = {
  ALL: 'All',
  DRAFT: 'DRAFT',
  IN_TRANSIT: 'IN_TRANSIT',
  FLOAT: 'FLOAT',
  STOCKED_IN: 'STOCKED_IN',
  ON_HOLD: 'ON_HOLD',
  ON_ORDER: 'ON_ORDER',
  SOLD: 'SOLD',
  RETIRED: 'RETIRED',
  RECEIVED: 'RECEIVED',
  INVOICE_RECEIVED: 'INVOICE_RECEIVED',
  RESERVED: 'RESERVED',
  CANCELLED: 'CANCELLED',
  TRANSFERRED: 'TRANSFERRED',
  TRANSFERRED_OUT: 'TRANSFERRED_OUT',
};

export const TAB_ENUMS = {
  GENERAL: 'GENERAL',
  PRICING: 'PRICING',
  OPTIONS_PARTS: 'OPTIONS_PARTS',
  DAMAGES: 'DAMAGES',
  MEDIA: 'MEDIA',
  DEALS: 'DEALS',
  VALUATION: 'VALUATION',
  LEADS: 'LEADS',
  DOCUMENTS: 'DOCUMENTS',
  MARKETING: 'MARKETING',
};

export const SETUP_TAB_ENUMS = {
  STOCK_TYPE: 'STOCK_TYPE',
  GENERAL: 'GENERAL',
  PRICING: 'PRICING',
  ACCOUNT_SETUP: 'ACCOUNT_SETUP',
  STOCK_RULES: 'STOCK_RULES',
  DEALER_ADD_ONS: 'DEALER_ADD_ONS',
  OPTIONS: 'OPTIONS',
  OTHERS: 'OTHERS',
  MOBILE_VI: 'MOBILE_VI',
  INVENTORY_FEED: 'INVENTORY_FEED',
};

export const INVENTORY_STOCK_RULES = {
  MAKES: 'MAKES',
  ROLLBACK: 'ROLLBACK',
  USED: 'USED',
};

export const TRADE_OWNERSHIP_TYPES = {
  LOAN: __('Loan'),
  LEASE: __('Lease'),
};

export const TRANSFER_VEHICLE_LABEL = {
  TRANSFERRED: __('Transfer'),
};

export const CONFIGURATION_DISPLAY_LABEL = {
  STOCK_TYPE: VEHICLE_TYPE,
  TRADE_OWNERSHIP_TYPE: TRADE_OWNERSHIP_TYPES,
  TRANSFERRED: TRANSFER_VEHICLE_LABEL,
};

export { SOURCE };

export const CONTROL_NUMBERS = {
  STOCK_NUM_OF_VEHICLE: 'STOCK_NUM_OF_VEHICLE',
  LAST_6_VIN_DIGITS: 'LAST_6_VIN_DIGITS',
  LAST_8_VIN_DIGITS: 'LAST_8_VIN_DIGITS',
  PURCHASE_CATEGORY: 'PURCHASE_CATEGORY',
  MAKE: 'MAKE',
  MODEL: 'MODEL',
  VARIANT: 'VARIANT',
  VEHICLE: 'VEHICLE',
  FULL_VIN: 'FULL_VIN',
  REGISTRATION_NUMBER: 'REGISTRATION_NUMBER',
  VENDOR: 'VENDOR',
  CUSTOMER: 'CUSTOMER',
};

export const CONTROL_TAG_LABEL = {
  [CONTROL_NUMBERS.MAKE]: __('Make'),
  [CONTROL_NUMBERS.MODEL]: __('Model'),
  [CONTROL_NUMBERS.VARIANT]: __('Variant'),
  [CONTROL_NUMBERS.VEHICLE]: __('Stock# of Vehicle'),
  [CONTROL_NUMBERS.FULL_VIN]: __('VIN Number'),
  [CONTROL_NUMBERS.REGISTRATION_NUMBER]: __('Registration Number'),
  [CONTROL_NUMBERS.VENDOR]: __('Vendor ID'),
  [CONTROL_NUMBERS.CUSTOMER]: __('Customer ID'),
};

export const DAMAGE_STATUS = {
  OPEN: 'OPEN',
  IN_PROGRESS: 'IN_PROGRESS',
  SERVICED: 'SERVICED',
};

export const DAMAGE_STATUS_LABEL = {
  [DAMAGE_STATUS.OPEN]: __('Open'),
  [DAMAGE_STATUS.IN_PROGRESS]: __('In Progress'),
  [DAMAGE_STATUS.SERVICED]: __('Serviced'),
};

export const PURCHASE_ORDER_LABEL = {
  [STATUS.APPROVAL_PENDING]: __('Approval Pending'),
  [STATUS.APPROVED]: __('Approved'),
  [STATUS.CANCELLED]: __('Cancelled'),
  [STATUS.CLOSED]: __('Closed'),
  [STATUS.DECLINED]: __('Declined'),
  [STATUS.DRAFT]: __('Draft'),
  [STATUS.INVOICED]: __('Closed'), // Complete transaction is done, and it is closed for the PO user
  [STATUS.PARTIALLY_INVOICED]: __('Partially Invoiced'),
  [STATUS.SUBMITTED]: __('Submitted'),
  [STATUS.PARTIALLY_RECEIVED]: __('Partially Received'),
  [STATUS.RECEIVED]: __('Received'),
  [STATUS.REISSUED]: __('Re-Issued'),
  [STATUS.VOIDED]: __('Voided'),
  [STATUS.PRE_INVOICED]: __('Pre Invoiced'),
};

export const KPI_FILTERS = {
  ALL_VEHICLES_WITH_RO: 'ALL_VEHICLES_WITH_RO',
  NEW_VEHICLES_WITH_RO: 'NEW_VEHICLES_WITH_RO',
  USED_VEHICLES_WITH_RO: 'USED_VEHICLES_WITH_RO',
  NEW_VEHICLES_WITH_RECALLS: 'NEW_VEHICLES_WITH_RECALLS',
  USED_VEHICLES_WITH_RECALLS: 'USED_VEHICLES_WITH_RECALLS',
  NEW_VEHICLES_WITH_PDI: 'NEW_VEHICLES_WITH_PDI',
  USED_VEHICLES_WITH_INSPECTION: 'USED_VEHICLES_WITH_INSPECTION',
  VEHICLES_WITH_ZERO_GL: 'VEHICLES_WITH_ZERO_GL',
  VEHICLES_WITH_ADJUSTED_PRICES: 'VEHICLES_WITH_ADJUSTED_PRICES',
  VEHICLES_WITH_OPTION_MISMATCH: 'VEHICLES_WITH_OPTION_MISMATCH',
  VEHICLES_WITH_MULTI_STYLE_ID_MISMATCH: 'VEHICLES_WITH_MULTI_STYLE_ID_MISMATCH',
  VEHICLES_WITH_OEM_ALERTS: 'VEHICLES_WITH_OEM_ALERTS',
};

export const AMOUNT_TYPE = {
  FLAT_AMOUNT: 'FLAT_AMOUNT',
  RETAIL_PRICE_PERCENTAGE: 'RETAIL_PRICE_PERCENTAGE',
  INVOICE_PRICE_PERCENTAGE: 'INVOICE_PRICE_PERCENTAGE',
  MSRP_PERCENTAGE: 'MSRP_PERCENTAGE',
  HOLDBACK_PERCENTAGE: 'HOLDBACK_PERCENTAGE',
  WHOLESALE_FINANCE_RESERVE_PERCENTAGE: 'WHOLESALE_FINANCE_RESERVE_PERCENTAGE',
  PORT_HOLDBACK: 'PORT_HOLDBACK_PERCENTAGE',
  VANGUARD: 'VANGUARD_PERCENTAGE',
  TINT_NITRO: 'TINT_NITRO_PERCENTAGE',
  FLOORPLANASSISTANCE_PERCENTAGE: 'FLOORPLANASSISTANCE_PERCENTAGE',
};

export const STOCK_RULE_ENUMS = {
  AUTO_INCREMENTING_NUMBERS: 'AUTO_INCREMENTING_NUMBERS',
  AUTO_INCREMENTING_ALPHABETS: 'AUTO_INCREMENTING_ALPHABETS',
  BODY_CLASS: 'BODY_CLASS',
  DEAL_VEHICLE_STOCK_NUMBER: 'DEAL_VEHICLE_STOCK_NUMBER',
  DEAL_VEHICLE_STOCK_NUMBER_WITH_AUTO_INCREMENT: 'DEAL_VEHICLE_STOCK_NUMBER_WITH_AUTO_INCREMENT',
  TRADE_IN_VEHICLE_STOCK_NUMBER_WITH_AUTO_INCREMENT: 'TRADE_IN_VEHICLE_STOCK_NUMBER_WITH_AUTO_INCREMENT',
  DEAL_VEHICLE_VIN: 'DEAL_VEHICLE_VIN',
  DEALER_CODE: 'DEALER_CODE',
  LETTERS: 'LETTERS',
  MAKE: 'MAKE',
  MODEL: 'MODEL',
  SOURCE: 'SOURCE',
  STOCK_TYPE: 'STOCK_TYPE',
  STOCK_SUBTYPE: 'STOCK_SUBTYPE',
  TRADE_OWNERSHIP_TYPE: 'TRADE_OWNERSHIP_TYPE',
  VIN: 'VIN',
  YEAR: 'YEAR',
  TRANSFER_VEHICLES_WITH_LETTERS: 'TRANSFER_VEHICLES_WITH_LETTERS',
};

export const MANDATORY_STOCK_RULE_TYPES = [STOCK_RULE_ENUMS.DEALER_CODE];

export const FORMAT_OPTIONS_TO_VALUES = {
  THREE_DIGITS: 3,
  FOUR_DIGITS: 4,
  FIVE_DIGITS: 5,
  SIX_DIGITS: 6,
  EIGHT_DIGITS: 8,
};

export const TRADE_IN_VEHICLE_SOURCE = [
  SOURCE_ENUM.TRADE_IN_1,
  SOURCE_ENUM.TRADE_IN_2,
  SOURCE_ENUM.TRADE_IN_3,
  SOURCE_ENUM.TRADE_IN_4,
  SOURCE_ENUM.TRADE_IN_5,
];

export const STORE_TYPE_VALUES = {
  AUTOMOTIVE: 'AUTOMOTIVE',
  RV: 'RV',
  AUTOMOBILE: 'AUTOMOBILE',
};

export const VEHICLE_ORDER_STATUS = {
  IN_PRODUCTION: 'IN_PRODUCTION',
  READY_TO_SHIP: 'READY_TO_SHIP',
  OUT_FOR_DELIVERY: 'OUT_FOR_DELIVERY',
  IN_STORAGE_CENTER: 'IN_STORAGE_CENTER',
  OUT_FOR_DEALERSHIP: 'OUT_FOR_DEALERSHIP',
  ARRIVED_AT_DEALERSHIP: 'ARRIVED_AT_DEALERSHIP',
  READY_FOR_HANDOVER: 'READY_FOR_HANDOVER',
  DELIVERY_TO_CUSTOMER: 'DELIVERY_TO_CUSTOMER',
};

export const VEHICLE_ORDER_STATUS_CONFIG = {
  [VEHICLE_ORDER_STATUS.IN_PRODUCTION]: { label: __('In Production') },
  [VEHICLE_ORDER_STATUS.READY_TO_SHIP]: { label: __('Ready to Ship') },
  [VEHICLE_ORDER_STATUS.OUT_FOR_DELIVERY]: { label: __('Out for Delivery') },
  [VEHICLE_ORDER_STATUS.IN_STORAGE_CENTER]: { label: __('In Storage Center') },
  [VEHICLE_ORDER_STATUS.OUT_FOR_DEALERSHIP]: { label: __('Out For Dealership') },
  [VEHICLE_ORDER_STATUS.ARRIVED_AT_DEALERSHIP]: { label: __('Arrived at Dealership') },
  [VEHICLE_ORDER_STATUS.READY_FOR_HANDOVER]: { label: __('Ready for Handover') },
  [VEHICLE_ORDER_STATUS.DELIVERY_TO_CUSTOMER]: { label: __('Delivery to Customer') },
};

export const BATTERY_PURCHASE_TYPE = {
  BUY: 'BUY',
  LEASE: 'LEASE',
};

export const BATTERY_PURCHASE_TYPE_CONFIG = {
  [BATTERY_PURCHASE_TYPE.BUY]: { label: __('Buy'), value: BATTERY_PURCHASE_TYPE.BUY },
  [BATTERY_PURCHASE_TYPE.LEASE]: { label: __('Lease'), value: BATTERY_PURCHASE_TYPE.LEASE },
};

export const INVOICE_TYPE = {
  SHELL_INVOICE: 'SHELL_INVOICE',
  SHELL_CREDIT_NOTE: 'SHELL_CREDIT_NOTE',
  BATTERY_INVOICE: 'BATTERY_INVOICE',
  BATTERY_CREDIT_NOTE: 'BATTERY_CREDIT_NOTE',
};

export const INVOICE_TYPE_CONFIG = {
  [INVOICE_TYPE.SHELL_INVOICE]: { label: __('Shell Invoice'), value: INVOICE_TYPE.SHELL_INVOICE },
  [INVOICE_TYPE.SHELL_CREDIT_NOTE]: { label: __('Shell Credit Note'), value: INVOICE_TYPE.SHELL_CREDIT_NOTE },
  [INVOICE_TYPE.BATTERY_INVOICE]: { label: __('Battery Invoice'), value: INVOICE_TYPE.BATTERY_INVOICE },
  [INVOICE_TYPE.BATTERY_CREDIT_NOTE]: { label: __('Battery Credit Note'), value: INVOICE_TYPE.BATTERY_CREDIT_NOTE },
};

export const ORDER_TYPE = {
  PO: 'PO',
  RO: 'RO',
};

export const CUSTOMER_SENSITIVE_FIELDS = {
  AGE: 'AGE',
  RECEIVED_DATE: 'RECEIVED_DATE',
  WHOLESALE_OVERVIEW: 'WHOLESALE_OVERVIEW',
  DISCOUNTS: 'DISCOUNTS',
  OFFERS: 'OFFERS',
  ADDITIONAL_BODY: 'ADDITIONAL_BODY',
  HARD_PACK: 'HARD_PACK',
  TRANSACTIONS: 'TRANSACTIONS',
  OPTIONS: 'OPTIONS',
  HOLDBACK_AMOUNT: 'HOLDBACK_AMOUNT',
  glAccount: 'GL_ACCOUNT',
  glBalance: 'GL_BALANCE',
  INVOICE_PRICE: 'INVOICE_PRICE',
  BASE_INVOICE: 'BASE_INVOICE',
};

export const BULK_ACTION_TYPE = {
  RECEIVED_FLAG: 'RECEIVED_FLAG',
};

export const VI_SETUP_VEHICLE_TYPES = {
  AUTOMOBILE: 'AUTOMOBILE',
  RV: 'RV',
};

export const OUTBOUND_SYNDICATION_STATUS = {
  SYNDICATION_READY: 'SYNDICATION_READY',
  MISSING_INFORMATION: 'MISSING_INFORMATION',
  NOT_QUALIFIED: 'NOT_QUALIFIED',
};

export const OUTBOUND_SYNDICATION_PARTNER_STATUS = {
  PUBLISHED: 'PUBLISHED',
  NOT_PUBLISHED: 'NOT_PUBLISHED',
  INACTIVE: 'INACTIVE',
  QUOTA_AVAILABLE: 'QUOTA_AVAILABLE',
  QUOTA_FULL: 'QUOTA_FULL',
};

export const INVOICE_SOURCE = {
  CUSTOMER: 'CUSTOMER',
  VENDOR: 'VENDOR',
};

export const INVOICE_SOURCE_TYPE = {
  INDIVIDUAL: 'INDIVIDUAL',
  BUSINESS: 'BUSINESS',
};

export const INVOICE_SOURCE_TYPE_VS_LABEL = {
  [INVOICE_SOURCE_TYPE.INDIVIDUAL]: __('Individual'),
  [INVOICE_SOURCE_TYPE.BUSINESS]: __('Business'),
};

export const DELETABLE_PURCHASE_DETAILS_STATUS = [PURCHASE_DETAILS_STATUS.CREATED, PURCHASE_DETAILS_STATUS.ERROR];

export const OPTION_CONFIGURATIONS = {
  STANDARD_EQUIPMENT: 'STANDARD_EQUIPMENT',
  PACKS_AND_OPTIONS: 'PACKS_AND_OPTIONS',
  ACCESSORIES: 'ACCESSORIES',
};

export const COST_NATURE = {
  OFF_SITE_COST: 'OFF_SITE_COST',
  BUYBACK_EXCESS_MILEAGE: 'BUYBACK_EXCESS_MILEAGE',
  BUYBACK_REFIT: 'BUYBACK_REFIT',
  BUYBACK_PROVISION: 'BUYBACK_PROVISION',
  BUYBACK_EARLY_RETURN: 'BUYBACK_EARLY_RETURN',
};

export const BUYBACK_COSTS = [
  COST_NATURE.BUYBACK_EARLY_RETURN,
  COST_NATURE.BUYBACK_EXCESS_MILEAGE,
  COST_NATURE.BUYBACK_PROVISION,
  COST_NATURE.BUYBACK_REFIT,
];

// Origin Values used to filter based on stock type and preselect on the vehicle in RRG
export const DEFAULT_ORIGIN_VALUES = {
  VN: 'VN',
  VS: 'VS',
  VD: 'VD',
};

export const RRG_ORIGIN_TO_STOCK_TYPE_LIST = {
  [VEHICLE_TYPES.NEW]: [DEFAULT_ORIGIN_VALUES.VN],
  [VEHICLE_TYPES.DEMO]: [DEFAULT_ORIGIN_VALUES.VS, DEFAULT_ORIGIN_VALUES.VD],
};

export const DURATION_UNIT = {
  MONTHS: 'months',
  DAYS: 'days',
};
