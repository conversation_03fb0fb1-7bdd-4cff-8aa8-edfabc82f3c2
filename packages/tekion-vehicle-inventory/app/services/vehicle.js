import { URL_TYPES } from 'tbase/constants/api';
import { DEAL_SERVICE } from 'tbase/constants/deal/endPoint';
import MODULE_ASSET_TYPES from 'tbase/constants/moduleAssetTypes';
import { stringInterpolate } from 'tbase/formatters/string';
import { isInchcapeOrRRG } from 'tbase/utils/sales/dealerProgram.utils';

import DealerManager from 'utils/dealerManager';
import Http from 'services/http';

const API_PATHS = {
  GET_VEHICLES: '/api-vi/u/v1.0.0/inventory/v2/search',
  VIN_VALIDATE: 'api-vi/u/v1.0.0/vin-validate',
  GET_VEHICLES_TENANT: '/api-vi/u/v1.0.0/inventory/tenant/v2/search?langParam={langParam}',
  GET_VEHICLES_ENTERPRISE_V2: '/api-vi/u/enterprise/inventory/search?langParam={langParam}',
  EXPORT_PDF: '/exports/pdf-v2',
  PRINT: '/pms/u/v3/print',
  POST_TO_ACCOUNTING: '/api-vi/u/v1.0.0/inventory/{vid}/posting?update={update}&repost={repost}',
  BULK_POST_TO_ACCOUNTING: '/api-vi/u/v1.0.0/inventory/bulk/posting',
  GET_VEHICLE: '/api-vi/u/v1.0.0/inventory/{vehicleId}?langParam={langParam}',
  UPDATE_VEHICLE: '/api-vi/u/v1.0.0/inventory/{vehicleId}?langParam={langParam}',
  UPDATE_VEHICLEV2: '/api-vi/u/v2/inventory/{vehicleId}?langParam={langParam}',
  BULK_UPDATE: '/api-vi/u/v1.0.0/inventories/patch',
  UPDATE_VEHICLE_SUB_STATUS: 'api-vi/u/v1.0.0/inventory/{vid}/sub-status/{subStatusId}',
  DELETE_VEHICLES: '/api-vi/u/v1.0.0/vi',
  LABEL_PRINT: `${DEAL_SERVICE}/u/deal/label/dealJacket/print`,
  SEARCH_DEAL_TENANT: `${DEAL_SERVICE}/u/deal/report/search`,
  FETCH_ASSOCIATED_ADDITIONAL_COSTS: '/vi-setup/u/vi/additionalCosts',
  FETCH_TENANT_INVENTORY_DETAILS: '/api-vi/u/v1.0.0/inventory/tenant/{vehicleId}?dealerId={dealerId}',
  TRANSFER_VEHICLE: '/api-vi/u/v1.0.0/inventory/transfer',
  SET_PASSWORD: '/vi-setup/u/vi/INVENTORY_TRANSFER_PASSCODE/change',
  VALIDATE_PASSWORD: '/vi-setup/u/vi/INVENTORY_TRANSFER_PASSCODE/validate',
  UPDATE_LOCATION: '/api-vi/u/v1.0.0/update/location',
  MARK_MARKETABLE: '/api-vi/u/v1.0.0/bulk-update-marketable',
  BULK_STATUS_UPDATE: '/api-vi/u/v1.0.0/inventory/bulk-update/field',
  BULK_UPDATE_NOTES: '/api-vi/u/v1.0.0/notes',
  PRICING_CONFIG: 'api-vi/u/v1.0.0/inventory/{vehicleId}/price-update-restrict?enable={isEnabled}',
  FIELD_UPDATE_CONFIG: 'api-vi/u/v1.0.0/inventory/{vehicleId}/enable/AUTO-FIELD-UPDATE?enable={isEnabled}',
  OEM_SYNC_TOGGLE: 'api-vi/u/v1.0.0/inventory/{vehicleId}/enable/OEM-SYNC?enable={isEnabled}',
  FETCH_SETUP_OPTIONS: 'api-vi/u/v1.0.0/fetch-pre-configured-options',
  INVENTORY_UPDATE_FIELD: '/api-vi/u/v1.0.0/inventory/update/field',
  FETCH_CUSTOMER_VIEW_ASSETS: '/api-vi/u/v1.0.0/inventory/customer-view-assets',
  FETCH_VEHICLE_ALERTS: '/api-vi/u/v1.0.0/vehicle/details/fetch',
  UPDATE_VEHICLE_ALERTS: '/api-vi/u/v1.0.0/vehicle/details/update',
  RESERVED_TIMER: 'api-vi/u/aec/vehicle-reserve/timer',
  VALIDATE_INVOICE_NUMBER: '/api-vi/u/v1.0.0/inventory/validate/invoiceNumber',
  VOID_PURCHASE_INVOICE: 'vi/u/v1.0.0/inventory/{vehicleId}/purchase-details/void',
  FETCH_INVOICES_BY_VEHICLE_ID: '/api-vi/u/v1.0.0/inventory/{vehicleId}/purchase-details',
  FETCH_INVOICES_WITH_ERROR_BY_VEHICLE_ID: '/api-vi/u/v1.0.0/inventory/{vehicleId}/purchase-details?status=ERROR',
  FETCH_INVENTORY_MAPPED_GL_ACCOUNTS: '/api-vi/u/v1.0.0/inventory/vehicles/glAccounts',
  VALIDATE_PURCHASE_DETAILS: '/api-vi/u/v1.0.0/inventories/validate/purchase-details',
};

export default class VehicleAPIs extends DealerManager {
  static fetchVehicles(payload) {
    const queryParams = {
      dealerId: this.getDealerId(),
    };
    return Http.post(URL_TYPES.CDMS, API_PATHS.GET_VEHICLES, payload, queryParams);
  }

  static fetchVehicleVins(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.VIN_VALIDATE, payload);
  }

  static fetchVehiclesTenant(payload, langParam) {
    return Http.post(URL_TYPES.CDMS, stringInterpolate(API_PATHS.GET_VEHICLES_TENANT, { langParam }), payload);
  }

  static fetchVehiclesEnterpriseV2(payload, langParam) {
    return Http.post(URL_TYPES.CDMS, stringInterpolate(API_PATHS.GET_VEHICLES_ENTERPRISE_V2, { langParam }), payload);
  }

  static printDocuments(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.PRINT, payload);
  }

  static exportPdf(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.EXPORT_PDF, payload);
  }

  static postToAccounting(vid, payload, update = true, repost = false) {
    return Http.post(URL_TYPES.CDMS, stringInterpolate(API_PATHS.POST_TO_ACCOUNTING, { update, vid, repost }), payload);
  }

  static bulkPostToAccounting(payload, update = true) {
    return Http.post(URL_TYPES.CDMS, stringInterpolate(API_PATHS.BULK_POST_TO_ACCOUNTING, { update }), payload);
  }

  static bulkUpdateVehicles(payload) {
    return Http.put(URL_TYPES.CDMS, stringInterpolate(API_PATHS.BULK_UPDATE), payload);
  }

  static updateVehicle(vehicleId, vehicle, langParam) {
    return Http.put(
      URL_TYPES.CDMS,
      stringInterpolate(isInchcapeOrRRG() ? API_PATHS.UPDATE_VEHICLEV2 : API_PATHS.UPDATE_VEHICLE, {
        vehicleId,
        langParam,
      }),
      vehicle
    );
  }

  // Update and get vehicle details endpoint are same. Just method are different
  static getVehicleDetails(vehicleId, langParam) {
    return Http.get(URL_TYPES.CDMS, stringInterpolate(API_PATHS.GET_VEHICLE, { vehicleId, langParam }));
  }

  static getTenantLevelVehicleDetails(vehicleId, dealerId) {
    return Http.get(
      URL_TYPES.CDMS,
      stringInterpolate(API_PATHS.FETCH_TENANT_INVENTORY_DETAILS, { vehicleId, dealerId })
    );
  }

  static updateVehicleSubStatus(vid, subStatusId) {
    return Http.put(URL_TYPES.CDMS, stringInterpolate(API_PATHS.UPDATE_VEHICLE_SUB_STATUS, { vid, subStatusId }));
  }

  static deleteVehicles(payload) {
    return Http.delete(URL_TYPES.CDMS, API_PATHS.DELETE_VEHICLES, payload);
  }

  static printVehicleInfo(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.LABEL_PRINT, payload);
  }

  static getActiveTenantDeals(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.SEARCH_DEAL_TENANT, payload);
  }

  static fetchAdditionalCost(payload, langParam) {
    const queryParams = {
      langParam,
      dealerId: this.getDealerId(),
    };
    return Http.post(URL_TYPES.CDMS, API_PATHS.FETCH_ASSOCIATED_ADDITIONAL_COSTS, payload, queryParams);
  }

  static transferVehicle(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.TRANSFER_VEHICLE, payload);
  }

  static setTransferVehiclePassworld(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.SET_PASSWORD, payload);
  }

  static validateUserPin(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.VALIDATE_PASSWORD, payload);
  }

  static updateLocation(payload) {
    return Http.put(URL_TYPES.CDMS, API_PATHS.UPDATE_LOCATION, payload);
  }

  static inventoryUpdateField(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.INVENTORY_UPDATE_FIELD, payload);
  }

  static markMarketable(payload) {
    return Http.put(URL_TYPES.CDMS, API_PATHS.MARK_MARKETABLE, payload);
  }

  static markRecommended(payload) {
    return Http.put(URL_TYPES.CDMS, API_PATHS.BULK_UPDATE, payload);
  }

  static bulkStatusUpdate(payload) {
    return Http.put(URL_TYPES.CDMS, API_PATHS.BULK_STATUS_UPDATE, payload);
  }

  static bulkUpdateNotes(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.BULK_UPDATE_NOTES, payload);
  }

  static fetchSetupOptions(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.FETCH_SETUP_OPTIONS, payload);
  }

  static fetchPriceConfig(vehicleId, isEnabled) {
    return Http.put(URL_TYPES.CDMS, stringInterpolate(API_PATHS.PRICING_CONFIG, { vehicleId, isEnabled }));
  }

  static fetchFieldUpdateConfig(vehicleId, isEnabled) {
    return Http.put(URL_TYPES.CDMS, stringInterpolate(API_PATHS.FIELD_UPDATE_CONFIG, { vehicleId, isEnabled }));
  }

  static toggleOEMSync(vehicleId, isEnabled) {
    return Http.put(URL_TYPES.CDMS, stringInterpolate(API_PATHS.OEM_SYNC_TOGGLE, { vehicleId, isEnabled }));
  }

  static fetchCustomerViewAssets() {
    return Http.get(URL_TYPES.CDMS, API_PATHS.FETCH_CUSTOMER_VIEW_ASSETS);
  }

  static getVehicleAlerts = vehicleIds => {
    const queryParams = {
      dealerId: this.getDealerId(),
    };
    return Http.post(URL_TYPES.CDMS, API_PATHS.FETCH_VEHICLE_ALERTS, vehicleIds, queryParams);
  };

  static updateVehicleAlerts = payload => Http.put(URL_TYPES.CDMS, API_PATHS.UPDATE_VEHICLE_ALERTS, payload);

  static getReservedVehicleTimer(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.RESERVED_TIMER, payload);
  }

  static validateInvoiceNumber(vendorId, invoiceNumber) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.VALIDATE_INVOICE_NUMBER, { vendorId, invoiceNumber });
  }

  static voidPurchaseInvoice(vehicleId, payload) {
    const queryParams = { vehicleId };
    return Http.post(URL_TYPES.CDMS, stringInterpolate(API_PATHS.VOID_PURCHASE_INVOICE, queryParams), payload);
  }

  static fetchVendorInvoices(vehicleId) {
    return Http.get(URL_TYPES.CDMS, stringInterpolate(API_PATHS.FETCH_INVOICES_BY_VEHICLE_ID, { vehicleId }));
  }

  static fetchVendorInvoicesWithError(vehicleId) {
    return Http.get(
      URL_TYPES.CDMS,
      stringInterpolate(API_PATHS.FETCH_INVOICES_WITH_ERROR_BY_VEHICLE_ID, { vehicleId })
    );
  }

  static fetchInventoryMappedGlAccounts(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.FETCH_INVENTORY_MAPPED_GL_ACCOUNTS, payload);
  }

  static validatePurchaseDetails(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.VALIDATE_PURCHASE_DETAILS, payload);
  }
}

export const GENERATE_FETCH_COLUMNS_API_PATH = store => `/vi-setup/u/column/VI?store=${store}`;

export const GENERATE_FETCH_COST_COLUMNS_API_PATH = store =>
  `/vi-setup/u/column/${MODULE_ASSET_TYPES.VI_COSTS}?store=${store}`;
