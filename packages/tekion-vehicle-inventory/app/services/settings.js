import { stringInterpolate } from 'tbase/formatters/string';

import { URL_TYPES } from 'tbase/constants/api';
import Http from 'services/http';

import { ASSET_SUB_TYPES } from 'constants/settings';
import { getVehicleStoreType } from 'helpers/common';
import DealerManager from 'utils/dealerManager';

const API_PATHS = {
  GET_SETTINGS: '/vi-setup/u/vi',
  GET_CUSTOM_FIELDS: '/settings/u/customField/VI',
  FETCH_GENERAL_CUSTOM_FIELDS: `settings/u/customField/VI/subType/${ASSET_SUB_TYPES.CUSTOM_FIELDS_GENERAL}`,
  FETCH_PRICING_CUSTOM_FIELDS: `settings/u/customField/VI/subType/${ASSET_SUB_TYPES.CUSTOM_FIELDS_PRICING}`,
  SAVE_INVENTORY_SETTINGS: '/vi-setup/u/vi/all?langParam={langParam}',
  ADD_CUSTOM_FIELD: '/settings/u/customField/VI',
  CUSTOM_FIELD_OPERATION_COMMON: '/settings/u/customField/VI/{id}',
  CUSTOM_FIELD_OPERATION_EDIT: '/vi-setup/u/field/VI/{id}',
  BULK_UPDATE_CUSTOM_FIELD: '/vi-setup/u/field/VI/bulk',
  GET_USER_ROLES: '/settings/u/admin/roles',
  RULE_COUNTS_OPERATION: '/vi-setup/u/vi/rule-counts',
  FETCH_INCREMENT: '/vi-setup/u/vi/rule-counts/current',
  FETCH_SETTINGS_FIELDS: '/vi-setup/u/field/VI/subType/{assetSubType}?store={store}',
  TEST_ADDITIONAL_COST: '/vi-setup/u/vi/additionalCost/applicability',
  GET_DUE_BILLS: '/retail/sales/u/sales/settings/v1.0.0/sales/duebills?locale={langParam}',
  GET_WARRANTY_TYPES: '/vi-setup/u/vi/fetch-warranty-types',
  FETCH_PACKS_CONFIGURATION_TYPE: '/vi-setup/u/packs/param',
};
export default class SettingAPIs extends DealerManager {
  // SETTINGS ACTIONS
  static getInventorySettings(langParam) {
    const queryParams = {
      langParam,
      dealerId: this.getDealerId(),
    };
    return Http.get(URL_TYPES.CDMS, API_PATHS.GET_SETTINGS, queryParams);
  }

  static getCustomFields(payload) {
    return Http.get(URL_TYPES.CDMS, API_PATHS.GET_CUSTOM_FIELDS, payload);
  }

  static getGeneralCustomFields(store) {
    return Http.get(URL_TYPES.CDMS, API_PATHS.FETCH_GENERAL_CUSTOM_FIELDS, { store });
  }

  static getPricingCustomFields(store) {
    return Http.get(URL_TYPES.CDMS, API_PATHS.FETCH_PRICING_CUSTOM_FIELDS, { store });
  }

  static saveInventorySettings(payload, langParam) {
    return Http.post(URL_TYPES.CDMS, stringInterpolate(API_PATHS.SAVE_INVENTORY_SETTINGS, { langParam }), payload);
  }

  static addCustomField(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.ADD_CUSTOM_FIELD, payload);
  }

  static editCustomField(id, payload) {
    return Http.put(URL_TYPES.CDMS, stringInterpolate(API_PATHS.CUSTOM_FIELD_OPERATION_EDIT, { id }), payload);
  }

  static deleteCustomField(id, payload) {
    return Http.delete(URL_TYPES.CDMS, stringInterpolate(API_PATHS.CUSTOM_FIELD_OPERATION_COMMON, { id }), payload);
  }

  static getCustomField(id, payload) {
    return Http.get(URL_TYPES.CDMS, stringInterpolate(API_PATHS.CUSTOM_FIELD_OPERATION_COMMON, { id }), payload);
  }

  static bulkUpdateCustomFields(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.BULK_UPDATE_CUSTOM_FIELD, payload);
  }

  static getUserRoles(payload) {
    return Http.get(URL_TYPES.CDMS, API_PATHS.GET_USER_ROLES, payload);
  }

  static fetchRuleCounts(payload) {
    return Http.get(URL_TYPES.CDMS, API_PATHS.RULE_COUNTS_OPERATION, payload);
  }

  static updateRuleCounts(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.RULE_COUNTS_OPERATION, payload);
  }

  static fetchIncrement(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.FETCH_INCREMENT, payload);
  }

  // PRICING ACTIONS

  static testAdditionalCost(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.TEST_ADDITIONAL_COST, payload);
  }

  // FETCH FIELDS

  static fetchFields(assetSubType, payload) {
    return Http.get(
      URL_TYPES.CDMS,
      stringInterpolate(API_PATHS.FETCH_SETTINGS_FIELDS, { assetSubType, store: getVehicleStoreType() }),
      payload
    );
  }

  static getDueBills(langParam) {
    return Http.get(URL_TYPES.CDMS, stringInterpolate(API_PATHS.GET_DUE_BILLS, { langParam }));
  }

  static getWarrantyTypes() {
    return Http.get(URL_TYPES.CDMS, API_PATHS.GET_WARRANTY_TYPES);
  }

  static fetchPacksConfigurationType() {
    return Http.get(URL_TYPES.CDMS, API_PATHS.FETCH_PACKS_CONFIGURATION_TYPE);
  }
}
