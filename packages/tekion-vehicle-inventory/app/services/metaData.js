import { EMPTY_OBJECT } from 'tbase/app.constants';
import { stringInterpolate } from 'tbase/formatters/string';
import { URL_TYPES } from 'tbase/constants/api';

import DealerManager from 'utils/dealerManager';
import Http from 'services/http';

const API_PATHS = {
  FETCH_TRIM_LIST: 'api-vi/u/v1.0.0/distinct/trim',
  FETCH_BASE_METADATA: '/api-vi/u/v1.0.0/metadata',
  FETCH_MODELS_BY_MAKE: '/api-vi/u/tdp/make/media',
  FETCH_DEFAULT_PDI: '/lookup/ids',
  CHECK_OEM_AUTH: '/oem_credential/u/checkValidity',
  FETCH_BODY_STYLE: '/api-vi/u/tdp/bodystyle',
  FETCH_FUEL_TYPE: '/api-vi/u/tdp/fueltype',
  FETCH_JOURNAL_ACCOUNT: '/acc-setups/u/journal/allActive',
  FETCH_DISTRIBUTED_GL_ACCOUNT: '/api-vi/u/v1.0.0/accounts-and-keys',
  FETCH_DISTRIBUTED_GL_ACCOUNT_BULK: '/api-vi/u/v1.0.0/accounts-and-keys-bulk',
  FETCH_ALL_APPLICABLE_ROLES: '/settings/u/admin/roles',
  FETCH_GL_ACCOUNT: '/accounting/u/glAccount/all/active',
  FETCH_DEALER_MASTER: '/api-core/u/dealer-master',
  GET_SETTINGS_METADATA: '/vi-setup/u/vi/metadata',
  FETCH_ALL_MAKES: '/api-vi/u/tdp/makes/v2',
  FETCH_STATES_BY_COUNTRY_CODE: 'settings/u/global/states/{countryCode}',
  FETCH_INTER_COMPANY_DEALERSHIPS: 'accounting/u/interDealership/setup/all/offsetAccounts',
  FETCH_ACCOUNTING_SETUPS: '/api-vi/u/v1.0.0/accountingSettings',
  FETCH_TENANT_DEALER_DETAILS: 'settings/u/dealer/v2/dealers',
  GET_HARDPACK_METADATA: '/api-vi/u/v1.0.0/inventory/hardpacks/metadata',
  FETCH_LANGUAGES: '/api-vi/u/tdp/markets/country-and-language',
  // isBasic for getting unique models by ignoring bestStyleName
  FETCH_PAGINATED_MODELS_BY_MAKE: '/vi/u/tdp/v2/make/media?isBasic=true',
  FETCH_MODEL_CODES_BY_MAKE: '/api-vi/u/v1/vi-integration/mfr-codes',
};

export default class MetadataAPIs extends DealerManager {
  static fetchTrimList(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.FETCH_TRIM_LIST, payload);
  }

  static fetchBaseMetaData(payload = EMPTY_OBJECT) {
    const queryParams = {
      dealerId: this.getDealerId(),
    };
    return Http.get(URL_TYPES.CDMS, API_PATHS.FETCH_BASE_METADATA, { ...payload, ...queryParams });
  }

  static fetchModelsByMakes(payload, standardMakes = true) {
    const queryParams = {
      dealerId: this.getDealerId(),
    };
    if (standardMakes) {
      queryParams.tekionMake = true;
    }
    return Http.post(URL_TYPES.CDMS, API_PATHS.FETCH_MODELS_BY_MAKE, payload, queryParams);
  }

  static fetchOpcodes(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.FETCH_DEFAULT_PDI, payload);
  }

  static checkOEMAuth(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.CHECK_OEM_AUTH, payload);
  }

  static fetchbodystyle(payload) {
    return Http.get(URL_TYPES.CDMS, API_PATHS.FETCH_BODY_STYLE, payload);
  }

  static fetchFuelType(payload) {
    return Http.get(URL_TYPES.CDMS, API_PATHS.FETCH_FUEL_TYPE, payload);
  }

  static fetchJournalAccount(payload) {
    return Http.get(URL_TYPES.CDMS, API_PATHS.FETCH_JOURNAL_ACCOUNT, payload);
  }

  static fetchDistributedGLAccount() {
    return Http.get(URL_TYPES.CDMS, API_PATHS.FETCH_DISTRIBUTED_GL_ACCOUNT);
  }

  static fetchDistributedGLAccountBulk(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.FETCH_DISTRIBUTED_GL_ACCOUNT_BULK, payload);
  }

  static fetchAllApplicableRoles(payload) {
    return Http.get(URL_TYPES.CDMS, API_PATHS.FETCH_ALL_APPLICABLE_ROLES, payload);
  }

  static fetchGlAccounts(payload) {
    return Http.get(URL_TYPES.CDMS, API_PATHS.FETCH_GL_ACCOUNT, payload);
  }

  static fetchDealerMaster(payload) {
    return Http.get(URL_TYPES.CDMS, API_PATHS.FETCH_DEALER_MASTER, payload);
  }

  static getSettingsMetaData(payload = EMPTY_OBJECT) {
    const queryParams = {
      dealerId: this.getDealerId(),
    };
    return Http.get(URL_TYPES.CDMS, API_PATHS.GET_SETTINGS_METADATA, { ...payload, ...queryParams });
  }

  static fetchAllMakes({ rvVehiclesEnabled = false, standardMakes = false, vehicleCategory }) {
    const dealerId = this.getDealerId();
    return Http.get(URL_TYPES.CDMS, API_PATHS.FETCH_ALL_MAKES, {
      unsupportedMakes: true,
      ...(rvVehiclesEnabled && { includeRV: 'RV' }),
      ...(standardMakes && { tekionMake: true }),
      databaseType: vehicleCategory,
      ...(!!dealerId && {
        dealerId,
      }),
    });
  }

  static fetchStatesByCountryCode(countryCode) {
    return Http.get(URL_TYPES.CDMS, stringInterpolate(API_PATHS.FETCH_STATES_BY_COUNTRY_CODE, { countryCode }));
  }

  static fetchInterCompanyDealerships(payload) {
    return Http.get(URL_TYPES.CDMS, API_PATHS.FETCH_INTER_COMPANY_DEALERSHIPS, payload);
  }

  static fetchAccountingSetups(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.FETCH_ACCOUNTING_SETUPS, payload);
  }

  static fetchTenantDealerDetails() {
    return Http.get(URL_TYPES.CDMS, API_PATHS.FETCH_TENANT_DEALER_DETAILS);
  }

  static getHardpackMetadata(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.GET_HARDPACK_METADATA, payload);
  }

  static fetchLanguages() {
    return Http.get(URL_TYPES.CDMS, API_PATHS.FETCH_LANGUAGES);
  }

  static fetchPaginatedModels(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.FETCH_PAGINATED_MODELS_BY_MAKE, payload);
  }

  static fetchModelCodesByMakes(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.FETCH_MODEL_CODES_BY_MAKE, payload);
  }
}
