@import "assets/scss/partials/index.scss";
/* this is the "root" in "root em." */
html {
  font-size: 62.5%; /* Now 10px = 1rem! */
}

body {
  color: #161616 !important;
  font-family: $font-regular !important;
  font-size: 14px; /* px fallback */
  font-size: $font-size-normal !important; /* default font-size for document */
  line-height: 1.5; /* a nice line-height */
  overflow: hidden;
}

// https://css-tricks.com/snippets/css/turn-off-number-input-spinners/
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  margin: 0;
}

.margin16 {
  margin: 1.6rem;
}

.padding1 {
  padding: 1rem;
}

.padding16 {
  padding: 1.6rem;
}

.marginL8 {
  margin-left: 0.8rem;
}

.marginR4 {
  margin-right: 0.4rem;
}
.marginR8 {
  margin-right: 0.8rem;
}

.marginL12 {
  margin-left: 1.2rem;
}

.marginL16 {
  margin-left: 1.6rem;
}

.marginR16 {
  margin-right: 1.6rem;
}

.marginR24 {
  margin-right: 2.4rem;
}

.marginL24 {
  margin-left: 2.4rem;
}

.marginL32 {
  margin-left: 3.2rem;
}

.marginT12 {
  margin-top: 1.2rem;
}

.marginR12 {
  margin-right: 1.2rem;
}

.marginT24 {
  margin-top: 2.4rem;
}

.marginB12 {
  margin-bottom: 1.2rem;
}

.marginB16 {
  margin-bottom: 1.6rem;
}

.marginB24 {
  margin-bottom: 2.4rem;
}

.paddingL12 {
  padding-left: 1.2rem;
}

.paddingL16 {
  padding-left: 1.6rem;
}

.paddingR24 {
  padding-right: 2.4rem;
}

.paddingR8 {
  padding-right: 0.8rem;
}

.paddingT16 {
  padding-top: 1.6rem;
}

.paddingT8 {
  padding-top: 0.8rem;
}

.paddingT32 {
  padding-top: 3.2rem;
}

.marginR32 {
  margin-right: 3.2rem;
}

.marginL32 {
  margin-left: 3.2rem;
}

.marginT8 {
  margin-top: 0.8rem;
}

.marginB8 {
  margin-bottom: 0.8rem;
}

.marginB4 {
  margin-bottom: 0.4rem;
}

.marginT16 {
  margin-top: 1.6rem;
}

.marginT32 {
  margin-top: 3.2rem;
}

.marginT50 {
  margin-top: 5rem;
}

.marginV16 {
  margin: 1.6rem 0;
}

.maxContent {
  width: max-content;
}

.overflowHidden {
  overflow: hidden;
}

.zIndex1 {
  z-index: 1;
}

.zIndex3 {
  z-index: 3;
}

.zIndex10 {
  z-index: 10;
}

.zIndex10-imp {
  z-index: 10 !important;
}

.paddingL8 {
  padding-left: 0.8rem;
}

.borderLeft1 {
  border-left: $border-base-width $border-base-type $platinum;
}

.underline {
  text-decoration: underline;
}

.disableScreen {
  pointer-events: none;
  opacity: 0.5;
}

.disableClick {
  pointer-events: none;
}

.overflow-hidden-imp {
  overflow: hidden !important;
}

.overflow-x-hidden-imp {
  overflow-x: hidden !important;
}

.iconRotate {
  transform: rotate(90deg);
}

.maxContent {
  width: max-content;
}

.ashGrayFont {
  color: $ashGray;
}

.strikedOut {
  text-decoration: line-through;
}

.opacity65 {
  opacity: 0.65;
}

.opacity50 {
  opacity: 0.5;
}

.pointer-events-none {
  pointer-events: none;
}

.pointer-events-auto {
  pointer-events: auto;
}
