/* eslint-disable react/sort-comp */
import React, { PureComponent } from 'react';
import produce from 'immer';
import Proptype from 'prop-types';
import { defaultMemoize } from 'reselect';
import cx from 'classnames';
import queryString from 'query-string';

import _forEach from 'lodash/forEach';
import _get from 'lodash/get';
import _head from 'lodash/head';
import _includes from 'lodash/includes';
import _isEmpty from 'lodash/isEmpty';
import _isEqual from 'lodash/isEqual';
import _last from 'lodash/last';
import _map from 'lodash/map';
import _noop from 'lodash/noop';
import _omit from 'lodash/omit';
import _partial from 'lodash/partial';
import _set from 'lodash/set';
import _size from 'lodash/size';
import _some from 'lodash/some';
import _reject from 'lodash/reject';
import _filter from 'lodash/filter';
import _reduce from 'lodash/reduce';

import { EMPTY_OBJECT, EMPTY_ARRAY, EMPTY_STRING } from 'tbase/app.constants';
import NOTES_ASSET_TYPES from 'tbase/constants/notesAssetTypes';
import TEnvReader from 'tbase/readers/Env';
import { getErrorMessage } from 'tbase/utils/errorUtils';
import { tget } from 'tbase/utils/general';
import { isInchcape, isInchcapeOrRRG } from 'tbase/utils/sales/dealerProgram.utils';
import GlobalAnalytics from 'tbase/utils/GlobalAnalytics';
import VehicleReader from 'tbusiness/appServices/vehicleInventory/readers/vehicle';
import { VEHICLE_TYPES } from 'tbase/constants/vehicleInventory/vehicleTypes';
import { SOURCE_ENUM } from 'tbase/constants/vehicleInventory/sourceType';
import Button from 'tcomponents/atoms/Button';
import Content from 'tcomponents/atoms/Content';
import Label from 'tcomponents/atoms/Label';
import Tooltip, { TOOLTIP_PLACEMENT } from 'tcomponents/atoms/tooltip';
import FontIcon, { SIZES } from 'tcomponents/atoms/FontIcon';
import Loader from 'tcomponents/molecules/loader';
import Modal from 'tcomponents/molecules/Modal';
import Switch from 'tcomponents/molecules/Switch';
import Page from 'tcomponents/molecules/pageComponent/PageComponent';
import Tabs from 'tcomponents/molecules/Tabs/Tabs';
import AuditLogs from 'tcomponents/organisms/AuditLogs/AuditLogs';
import { ASSET_TYPES } from 'tcomponents/organisms/AuditLogs/AuditLogs/AuditLogs.constants';
import { toaster, TOASTER_TYPE } from 'tcomponents/organisms/NotificationWrapper';
import DealerPropertyHelper from 'tcomponents/helpers/sales/dealerPropertyHelper';
import PropertyControlledComponent from 'tcomponents/molecules/PropertyControlledComponent';
import { getScaleAdjustedSize } from 'tcomponents/utils/general';
import { triggerSubmit } from 'tcomponents/pages/formPage/utils/formAction';
import { FEATURE_NAME } from 'twidgets/experienceEngine';
import { hasVehicleAlertByType } from 'tbusiness/appServices/vehicleInventory/helpers/vehicleAlerts';
import { ALERTS } from 'tbusiness/appServices/vehicleInventory/constants/vehicleAlerts';
import { getVehicleDetailsRoute } from 'tbusiness/appServices/vehicleInventory/helpers/routes';
import SalesDealerPropertyHelper from 'twidgets/appServices/sales/helpers/salesDealerPropertyHelper';

import { LAYOUT } from 'appConfig';
import { createRo } from 'actions/repairOrder';
import { TAB_ENUMS, VEHICLE_TYPE } from 'constants/constantEnum';
import {
  VEHICLE_STATUS,
  MESSAGES,
  NO_INVOICE_MESSAGE,
  INVOICE_PREVIEW_TOOLTIP_TIMER,
  LABELS,
  VEHICLE_ACTIONS,
  ES_SYNC_DELAY,
  PAGES,
} from 'constants/constants';
import PROGRAM_CONFIG from 'constants/programConfig';
import { hidePortal, refetchRoApprovalData } from 'event';
import { getRenderInfoModalData } from 'factories/vehicle';
import { getVehicleStoreType, isVehicleStockTypeRestrictedForPurchaseInvoice } from 'helpers/common';
import { getNotesModalProps } from 'helpers/notes';
import { handleVehicleDetailsBackNavigation } from 'helpers/route';
import {
  getTrimDetails,
  isVehicleTypeNew,
  shouldPostToAccounting,
  checkIfVINHasSoldVehicle,
  isRVDealerEnabled,
  shouldReadOnlyDetails,
  shoulRenderInfoModal,
  getDetailsPageStickyBannerData,
  isStandardMakeEnabled,
  isVehicleStatusSold,
} from 'helpers/vehicle.helper';
import JobModal from 'molecules/JobModal';
import NotesModal from 'molecules/NotesModal';
import TabHeader from 'molecules/TabHeader/TabHeader';
import CarRecord from 'organisms/CarRecord';
import ConfirmationModal, { showConfirmationModal } from 'organisms/ConfirmationModal';
import FeatureSheetDrawer from 'organisms/FeatureSheetDrawer';
import OptionSheetDrawer from 'organisms/OptionSheetDrawer';
import Portal from 'organisms/Portal';
import PostToAccountingModal, { showPostToAccountingModal } from 'organisms/PostToAccountingModal';
import VehicleAlertDrawer from 'organisms/VehicleAlertDrawer';
import StockCardButton from 'molecules/StockCardButton';
import InvoiceReader from 'readers/invoice';
import SettingsReader from 'readers/settings';
import VehicleAPI from 'services/vehicle';
import { getSingleROPayload } from 'pages/VehicleInventoryList/VehicleInventoryList.reader';
import { getSingleROPayloadForApproval } from 'pages/VehicleInventoryList/VehicleInventoryList.factory';
import { hasViewInvoice } from 'permissions/inventory.permissions';
import PermissionHelper from 'permissions/permissions.helper';
import { transformObjectValuesToString, omitKeys } from 'utils';
import DealerManager from 'utils/dealerManager';
import { parsePriceFieldValue } from 'utils/fieldFormatter';
import { getAppSentryPayload } from 'utils/commonUtils';
import { getIdFromKey, shouldGenerateStockIdOnSave } from 'helpers/vehicleDetails.helper';

import ACTION_TYPES from './actionTypes';
import Header from './Components/Header';
import Footer from './Components/Footer';
import VINConfirmationModal, { showVINConfirmationModal } from './Components/VINConfirmationModal';
import { getOpenDamages } from './Components/Damages/damages.reader';
import PreviewContainer from './Components/InvoicePreview/invoicePreviewContainer';
import VehicleDetailsAPI from './vehicleDetails.api';
import {
  SHARE_MENU_KEYS,
  EXCULDED_PRICING_DETAILS_KEYS_ON_SAVE,
  VEHICLE_DETAILS_TOP_TAB_BAR_STYLE,
  DISABLE_CTA_ON_FIELDS_WITH_ERROR,
  VALIDATION_MESSAGE_MAP,
  VI_ERROR_CODES,
} from './VehicleDetails.constant';
import {
  checkIfPostingDisabled,
  isPurchaseInvoiceApplicable,
  isPurchaseInvoiceAvailableToSave,
  isPostingAllowedFromPurchaseInvoice,
  shouldShowTabs,
  isPurchaseInvoiceAvailableToUpdate,
  getTabsFormCount,
  shouldShowValuationTab,
} from './vehicleDetails.constraints';
import { formatCustomFields, getTargetingPayload, getVehicleDetails } from './VehicleDetails.factory';
import {
  isValidStockTypeForDraftStatusSave,
  getGlAccountOptions,
  isVehicleSubTypeRequired,
  checkValidVinLength,
  getFormInitialState,
  fetch3dModels,
  validateTabErrors,
  isUploadInProgress,
  getIsPrimaryCtaSaveAsDraft,
  getInvoiceDataToSaveOrPost,
  getProductTaxClassificationOptions,
  getDefaultProductTaxClassificationOption,
  isValidMarketingContent,
  getEnumConfigForAuditLogs,
  getSegregatedValidationIssues,
} from './VehicleDetails.helpers';
import VehicleDetailsReader from './VehicleDetails.reader';
import { TAB_KEY_VS_TAB_RENDERER, getVehicleDetailsTabs } from './VehicleDetails.tabConfig';
import { getGlAccountByAccountId, getGlAccountByNumber } from './vehicleDetails.utils';

import styles from './vehicleDetails.module.scss';
import { FORM_FIELD_KEYS } from './Components/Marketing/Components/MarketingInfoForm/marketingInfoForm.constants';

const validatedFormTabsCount = React.createRef(0);

class VehicleDetails extends PureComponent {
  static propTypes = {
    actions: Proptype.object,
    appraisalDetails: Proptype.object,
    contentHeight: Proptype.number,
    dealerConfig: Proptype.object,
    vehicleDetails: Proptype.object,
    vehicleDetailsInStore: Proptype.object,
    isEditMode: Proptype.bool,
    bodyTypeMapping: Proptype.array.isRequired,
    valuationDetails: Proptype.object.isRequired,
    providerList: Proptype.array.isRequired,
    vehicleTypes: Proptype.array,
    isVehicleSubTypeMandatory: Proptype.object,
    policeRecordData: Proptype.object,
    customerViewProps: Proptype.object,
    vehicleDetailsForm: Proptype.object,
    onAction: Proptype.func,
    isPostingToAccounting: Proptype.bool,
    fields: Proptype.array,
    currentLanguageId: Proptype.string,
    marketingInfo: Proptype.object,
    isOptionSetChanged: Proptype.bool,
    vendorInvoiceData: Proptype.array,
    costSummary: Proptype.array,
    isCostApprovalEnabled: Proptype.bool,
    lite: Proptype.bool,
    viLiteTabs: Proptype.array,
    freezeUpdateForValidateVehicleSave: Proptype.bool,
    navigate: Proptype.func.isRequired,
    location: Proptype.object.isRequired,
    params: Proptype.object.isRequired,
    permissions: Proptype.array,
    getDealerPropertyValue: Proptype.func,
    enableStockNumberGenerationOnSave: Proptype.bool,
    formFieldOverrides: Proptype.object,
    allFieldsDisabledByDefault: Proptype.bool,
    dealerID: Proptype.oneOfType([Proptype.string, Proptype.number]),
  };

  static defaultProps = {
    actions: EMPTY_OBJECT,
    appraisalDetails: EMPTY_OBJECT,
    contentHeight: LAYOUT.PAGE_HEADER,
    dealerConfig: EMPTY_OBJECT,
    vehicleDetails: EMPTY_OBJECT,
    vehicleDetailsInStore: EMPTY_OBJECT,
    isEditMode: false,
    vehicleTypes: EMPTY_ARRAY,
    isVehicleSubTypeMandatory: EMPTY_OBJECT,
    policeRecordData: EMPTY_OBJECT,
    customerViewProps: EMPTY_OBJECT,
    vehicleDetailsForm: EMPTY_OBJECT,
    onAction: _noop,
    isPostingToAccounting: false,
    fields: EMPTY_ARRAY,
    currentLanguageId: '',
    marketingInfo: EMPTY_OBJECT,
    isOptionSetChanged: false,
    vendorInvoiceData: EMPTY_ARRAY,
    costSummary: EMPTY_ARRAY,
    isCostApprovalEnabled: false,
    lite: false,
    viLiteTabs: EMPTY_ARRAY,
    freezeUpdateForValidateVehicleSave: false,
    permissions: EMPTY_ARRAY,
    getDealerPropertyValue: _noop,
    enableStockNumberGenerationOnSave: false,
    formFieldOverrides: EMPTY_OBJECT,
    allFieldsDisabledByDefault: false,
    dealerID: null,
  };

  considerLookupFailureForVehicleSave = false;

  constructor(props) {
    super(props);
    const {
      location,
      isEditMode,
      getFeatureValue,
      lite,
      permissions,
      getDealerPropertyValue,
      actions,
      dealerId: dealerIdFromProps,
    } = props;

    if (lite) {
      const { dealerId: currentDealerId } = TEnvReader.userInfo();
      if (dealerIdFromProps) DealerManager.initializeDealerId(dealerIdFromProps ?? currentDealerId);
      PermissionHelper.init(permissions);
      DealerPropertyHelper.initPropertyGetter(getDealerPropertyValue);
      SalesDealerPropertyHelper.initPropertyGetter(getDealerPropertyValue);
      if (DealerPropertyHelper.isMultiOEMEnabled()) {
        actions.fetchSitesByDealerId();
      }
    }
    const { dealerId } = this.getQueryParams(location);
    this.enterpriseV2Enabled = !!getFeatureValue(FEATURE_NAME.ENTERPRISE_V2_ENABLED);
    this.dealerID = dealerId;
    this.isTertiaryButtonClicked = false;
    this.state = {
      initialState: !_isEmpty(props.vehicleDetails) ? props.vehicleDetails : getFormInitialState(),
      showTabExtraContent: true,
      showTabExtraContentPrintContent: false,
      isFetching: isEditMode,
      vehicleID: tget(props, 'pathParams.id'),
      save: false,
      doPosting: false,
      postingModel: false,
      defaultActiveKey: TAB_ENUMS.GENERAL,
      currentActiveTabKey: TAB_ENUMS.GENERAL,
      isVehicleAlertBannerVisible: false,
      showPriceDetailsSummaryDrawer: false,
      isCTADisabled: false,
      tabs: this.getTabs(props),
      tabsFormCount: this.getTabsFormCount(props),
    };

    this.errors = {};
    this.shouldShowRevampedCTA = PROGRAM_CONFIG.shouldEnableRevampedCTABehaviour();
  }

  getEnumConfigForAuditLogs = defaultMemoize(getEnumConfigForAuditLogs);

  componentDidMount() {
    GlobalAnalytics.sendSubAppInitializingEvent();
    this.init();
  }

  static getDerivedStateFromProps(props, state) {
    const initialState = {
      ...props.vehicleDetails,
      pricingDetails: _get(props.vehicleDetails, 'pricingDetails') || EMPTY_OBJECT,
    };
    if (!_isEqual(initialState, state.initialState)) {
      return {
        initialState,
      };
    }
    return {
      showTabExtraContent: true,
    };
  }

  getTabs(props) {
    const { lite, viLiteTabs } = props;
    let tabs = getVehicleDetailsTabs(shouldShowTabs(props));
    if (lite) {
      tabs = _filter(tabs, tab => _includes(viLiteTabs, tab.key));
    }
    return tabs;
  }

  getTabsFormCount(props) {
    const { lite, viLiteTabs } = props;
    return getTabsFormCount(lite, viLiteTabs);
  }

  componentDidUpdate(prevProps) {
    const { lite, viLiteTabs } = this.props;

    if (
      prevProps.lite !== lite ||
      prevProps.viLiteTabs !== viLiteTabs ||
      shouldShowValuationTab(prevProps) !== shouldShowValuationTab(this.props)
    ) {
      this.setState({
        tabs: this.getTabs(this.props),
        tabsFormCount: this.getTabsFormCount(this.props),
      });
    }
  }

  componentWillUnmount() {
    const { actions, lite } = this.props;
    actions.resetVehicleState();
    if (lite) DealerManager.clearDealerId();
  }

  init = async () => {
    const { isEditMode, actions, currentLanguageId, onAction, lite, vehicleDetailsFromProps } = this.props;
    const { vehicleID } = this.state;

    if (!lite) fetch3dModels();

    let vDetails;
    if (isEditMode) {
      if (lite) {
        actions.saveInventoryDetails(vehicleDetailsFromProps);
        vDetails = vehicleDetailsFromProps;
      } else {
        this.filters = [
          {
            field: 'id',
            values: [vehicleID],
          },
        ];
        vDetails = await actions.getInventoryDetails(vehicleID, this.dealerID, currentLanguageId);
      }
      onAction({ type: ACTION_TYPES.SET_INITIAL_VEHICLE_DETAILS, payload: { vDetails } });
      this.setState({ isFetching: false });

      if (isInchcape()) actions.fetchVehicleStockTypeHistory(vehicleID);
    } else {
      // TODO: this won't be needed.Need to remove
      actions.resetVehicleState();
    }

    const { vehicleDetails } = this.props;
    if (!vehicleDetails.accountPosted) {
      this.setState({ postingModel: true });
    }
    const { vin, vehicleType } = vehicleDetails;
    this.fetchSectionsAndFields(vDetails);

    const actionsArray = [
      actions.fetchInventorySettings({ currentLanguageId }),
      actions.getBaseMetaData(),
      actions.fetchCustomerSensitiveAssets(),
    ];
    if (vin && isVehicleTypeNew(vehicleType)) {
      actionsArray.push(actions.getInvoicesByVinNumber(vin));
    }
    if (lite) {
      actionsArray.push(actions.fetchInventoryUserSettings(currentLanguageId));

      const rvVehiclesEnabled = isRVDealerEnabled();
      const standardMakes = isStandardMakeEnabled();

      actions
        .getAllMakes({ rvVehiclesEnabled, standardMakes })
        .then(data => actions.fetchModelsByMakes({ makes: data, standardMakes }))
        .catch(err => toaster(TOASTER_TYPE.ERROR, getErrorMessage(err, __('Failed to Fetch Make and Model'))));
    }

    if (isInchcapeOrRRG()) onAction({ type: ACTION_TYPES.FETCH_ALL_COUNTRIES });

    Promise.all(actionsArray).then(([viSettingData]) => {
      const glAccountList = tget(this, 'props.glAccountList', []);
      const glAccountOptions = getGlAccountOptions(viSettingData, glAccountList);
      this.setState({ vin, viSettingData, glAccountOptions }, () => {
        GlobalAnalytics.sendSubAppIntializedEvent(getAppSentryPayload(PAGES.VEHICLE_DETAILS));
      });
    });
  };

  setIsUploadStillInProgress = value => {
    this.setState({ isUploadStillInProgress: value });
    this.setIsCTADisabled(value);
  };

  setIsCTADisabled = value => {
    this.setState({ isCTADisabled: value });
  };

  setLookupFailureForVehicleSave = value => {
    this.considerLookupFailureForVehicleSave = value;
  };

  fetchSectionsAndFields = (params = EMPTY_OBJECT) => {
    const { actions, vehicleDetails, vehicleTypes, isEditMode, module, lite } = this.props;
    const payload = getTargetingPayload({ ...vehicleDetails, ...params }, { vehicleTypes });
    this.validateTabsForm({ shouldSubmit: false });
    Promise.allSettled([
      actions.fetchAllSections(payload, getVehicleStoreType(lite)),
      actions.fetchAllFields(payload, getVehicleStoreType(lite), isEditMode, module),
    ]).then(() => this.validateTabsForm({ shouldSubmit: false, shouldCompareWithPreviousValidation: true }));
  };

  getQueryParams = defaultMemoize(location => {
    const dealerId = _get(queryString.parse(location?.search), 'dealerId');

    return {
      dealerId,
    };
  });

  fetchPoliceRecordData = vDetails => {
    const { onAction } = this.props;
    onAction({ type: ACTION_TYPES.FETCH_POLICE_RECORD_DATA, payload: { vDetails } });
  };

  fetchStockIdOnError = additional => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.REFETCH_STOCK_ID_ON_ERROR,
      payload: { additional },
    });
  };

  fetchDeclarationStatus = async vehicleId => {
    const { onAction } = this.props;
    onAction({ type: ACTION_TYPES.FETCH_DECLARATION_STATUS, payload: { vehicleId } });
  };

  fetchVendorInvoicesData = async vehicleId => {
    const { actions } = this.props;
    try {
      await actions.fetchVendorInvoicesData(vehicleId);
    } catch (e) {
      toaster(TOASTER_TYPE.ERROR, __('Failed to fetch Vendor Invoices'));
    }
  };

  onCancelAction = async () => {
    showConfirmationModal(
      {
        title: __('Cancel'),
        message: __('All your unsaved changes will be lost. Do you want to proceed?'),
      },
      this.goBackHandler
    );
  };

  getVehicleDetails(values) {
    const { vehicleDetailsInStore, applicableAdditionalCost, vehicleDetails, isEditMode, defaultGlAccount, metaData } =
      this.props;
    const params = {
      vehicleDetailsInStore,
      applicableAdditionalCost,
      vehicleDetails,
      isEditMode,
      defaultGlAccount,
      isSaveAsDraftClicked: this.isSaveAsDraftClicked(values),
      defaultProductTaxClassification: getDefaultProductTaxClassificationOption(
        tget(metaData, 'taxDetailsMetadata.taxClassificationDetails', EMPTY_ARRAY)
      ),
    };

    return getVehicleDetails({ values, params });
  }

  onSaveUpdateAction = async (values, additional) => {
    const { vehicleDetailsInStore, actions, lite, onSubmitCb, navigate, isOptionSetChanged } = this.props;
    const { save } = this.state;
    const vehicleDetailToSave = this.getVehicleDetails(values);
    const damagesList = _get(vehicleDetailsInStore, 'damages.damagesList.data') || EMPTY_ARRAY;
    const { pricingDetails } = vehicleDetailToSave;
    const modifiedPricingDetails = _omit(pricingDetails, EXCULDED_PRICING_DETAILS_KEYS_ON_SAVE);
    const saveReadyPricingFields = parsePriceFieldValue(modifiedPricingDetails);

    const { additionalCosts = EMPTY_ARRAY } = saveReadyPricingFields;

    const updatedAdditionalCosts = _map(additionalCosts, additionalCost => omitKeys(additionalCost, ['isEdited']));

    const updatedValues = {
      ...vehicleDetailToSave,
      pricingDetails: { ...saveReadyPricingFields, additionalCosts: updatedAdditionalCosts },
    };

    if (isOptionSetChanged) {
      const optionId = await actions.updateOptions(values?.optionId);
      if (optionId) updatedValues.optionId = optionId;
    }

    if (lite) return onSubmitCb({ vehicleDetails: updatedValues, allOptions: vehicleDetailsInStore?.allOptions });

    const vehicle = await this.updateVehicle(values, updatedValues, damagesList, additional);
    const vehicleId = _get(vehicle, 'id');

    this.setIsCTADisabled(false);

    if (vehicle && save) {
      actions.resetVehicleState();
      // TODO: Unify the CTA behaviors across programs - PM-30115
      // US/CA needs to redirect back to Listing page post Save, EU needs to stay on details page as per ENG-71532,
      if (this.shouldShowRevampedCTA) {
        navigate(
          getVehicleDetailsRoute({
            vehicleId,
          }),
          {
            replace: true,
          }
        );
      } else {
        setTimeout(this.goBackHandler, 1000);
      }
    }
  };

  shouldPostAccounting = () => {
    const { vehicleDetails, isEditMode } = this.props;
    this.setState({ postingModel: true });
    return isEditMode && shouldPostToAccounting(vehicleDetails);
  };

  updateVehicleDamages = async (damagesList, vehicleId) => {
    const { vehicleID } = this.state;
    const { actions, isEditMode, vehicleDetailsForm } = this.props;
    const vid = isEditMode ? vehicleID : vehicleId;
    const vin = _get(vehicleDetailsForm, 'vin');

    if (_size(damagesList) > 0) {
      const filteredDamagedList = _map(damagesList, damage => ({
        ...((VehicleDetailsReader.isDamageNew(damage) ? _omit(damage, ['id']) : damage) || EMPTY_OBJECT),
        vin,
      }));

      if (PROGRAM_CONFIG.shouldShowCostApproval())
        await actions
          .saveApprovalDamagesBulk(filteredDamagedList, vid)
          .then(() => this.fetchCostSummaryAndUpdateAdditionalCost());
      else await actions.saveDamagesBulk(filteredDamagedList, vid);
    }
  };

  setAdditionalCost = async () => {
    const { onAction } = this.props;
    await onAction({ type: ACTION_TYPES.SET_ADDITIONAL_COST });
  };

  getGLBalance = id => VehicleDetailsAPI.getGlBalance(id).then(({ data }) => _get(data, 'glBalance'));

  isSaveAsDraftClicked = values => {
    const { isEditMode } = this.props;
    const { vehicleType } = values;
    return !isEditMode && isValidStockTypeForDraftStatusSave(vehicleType) && this.isTertiaryButtonClicked;
  };

  updateVendorInvoices = async (vehicleID, isUpdate) => {
    const { actions, vendorInvoiceData } = this.props;
    const isInvoiceAdded = await actions.updateVendorInvoice(vehicleID, _last(vendorInvoiceData), isUpdate);
    return isInvoiceAdded;
  };

  updateMarketingInfo = async (vehicleObj, vehicleId) => {
    const { actions, vehicleDetailsInStore, currentLanguageId, marketingInfo } = this.props;
    const originalMarketingInfo = tget(vehicleDetailsInStore, 'originalMarketingInfo', EMPTY_OBJECT);
    let updatedVehicle = vehicleObj;
    if (PROGRAM_CONFIG.shouldShowMarketingSection() && !_isEqual(originalMarketingInfo, marketingInfo)) {
      const sanitizedMarketingInfo = produce(marketingInfo, draft => {
        draft[FORM_FIELD_KEYS.KEY_SELLING_POINTS] = _reject(draft[FORM_FIELD_KEYS.KEY_SELLING_POINTS], value =>
          _isEmpty(value)
        );
      });

      if (isValidMarketingContent(sanitizedMarketingInfo)) {
        await actions.postMarketingInfo(vehicleId, sanitizedMarketingInfo);
        updatedVehicle = await actions.getInventoryDetails(vehicleId, this.dealerID, currentLanguageId);
      }
    }
    return updatedVehicle;
  };

  updateVehicle = async (values, vehicleDetailToSave, damagesList, additional) => {
    const { actions, vehicleDetailsInStore, currentLanguageId, isEditMode, vendorInvoiceData, isOptionSetChanged } =
      this.props;
    const { save, doPosting } = this.state;
    let vehicleObj;
    let triedAddingInvoice = false;
    const id = _get(vehicleDetailsInStore, 'general.id');

    const accountPosted = _get(vehicleObj, 'accountPosted');
    const invoiceAvailableToUpdate = isPurchaseInvoiceAvailableToUpdate(vendorInvoiceData, accountPosted);
    const invoiceAvailableToSave = isPurchaseInvoiceAvailableToSave(vendorInvoiceData);

    const invoiceData = getInvoiceDataToSaveOrPost(vendorInvoiceData);

    if (id && isEditMode) {
      vehicleObj = this.shouldShowRevampedCTA
        ? await actions.updateVehicle(id, vehicleDetailToSave, invoiceData, doPosting, additional)
        : await actions.updateVehicle(id, vehicleDetailToSave, save, currentLanguageId, additional);

      // To remove the multiple style id alert from the vehicle
      const hasMultiStyleIdMismatchAlert = hasVehicleAlertByType(vehicleObj, ALERTS.MULTI_STYLE_ID_MISMATCH);
      if (hasMultiStyleIdMismatchAlert) {
        await actions.removeMultiStyleAlert(id);
      }

      const errorCode = tget(vehicleObj, 'data.errorDetails.errorCode');
      if (errorCode === VI_ERROR_CODES.STOCK_ID_ERROR_CODE) {
        this.fetchStockIdOnError(additional);
        return false;
      }

      if (!vehicleObj) return vehicleObj;
    } else {
      if (
        !this.shouldShowRevampedCTA &&
        !this.isSaveAsDraftClicked(vehicleDetailToSave) &&
        !isPostingAllowedFromPurchaseInvoice(vendorInvoiceData)
      ) {
        toaster(TOASTER_TYPE.ERROR, __('Purchase Invoice is required for saving & stocking the vehicle'));
        return false;
      }
      vehicleObj = await actions.createVehicle(vehicleDetailToSave, _head(invoiceData), doPosting);
      const errorCode = tget(vehicleObj, 'data.errorDetails.errorCode');
      if (errorCode === VI_ERROR_CODES.STOCK_ID_ERROR_CODE) {
        this.fetchStockIdOnError(additional);
        return false;
      }

      if (!vehicleObj) return vehicleObj;

      if (
        !this.shouldShowRevampedCTA &&
        VEHICLE_TYPE[_get(vehicleObj, 'vehicleType')] !== VEHICLE_TYPE.SPECIAL &&
        !this.isSaveAsDraftClicked(vehicleDetailToSave)
      ) {
        let isInvoiceAdded = true;
        if (isPurchaseInvoiceApplicable()) {
          triedAddingInvoice = true;
          isInvoiceAdded = await this.updateVendorInvoices(vehicleObj.id);
        }
        if (isInvoiceAdded) {
          try {
            await VehicleAPI.postToAccounting(vehicleObj.id, vehicleObj, false);
          } catch (error) {
            toaster(TOASTER_TYPE.ERROR, getErrorMessage(error, __('Failed to post to accounting')));
          }
        }
      }
    }

    if (this.shouldShowRevampedCTA) {
      if (isEditMode) vehicleObj = _get(vehicleObj, 'updatedVehicle');
      else vehicleObj = _get(vehicleObj, 'createdVehicle');
    }

    const vehicleId = _get(vehicleObj, 'id');
    if (this.shouldShowRevampedCTA && isEditMode) {
      this.fetchVendorInvoicesData(vehicleId);
    }

    if (
      !this.shouldShowRevampedCTA &&
      !triedAddingInvoice &&
      isPurchaseInvoiceApplicable() &&
      (invoiceAvailableToUpdate || invoiceAvailableToSave)
    ) {
      triedAddingInvoice = true;
      await this.updateVendorInvoices(vehicleId, invoiceAvailableToUpdate);
      // Refetch the Vehicle Details as updateVendorInvoices might have updated vehicle data object
      if (!save) await actions.getInventoryDetails(vehicleId, this.dealerID, currentLanguageId);
    }

    this.updateVehicleDamages(damagesList, vehicleId);
    setTimeout(() => {
      this.fetchPoliceRecordData(vehicleObj);
      if (isEditMode && triedAddingInvoice) {
        this.fetchCostSummary();
      }
      if (PROGRAM_CONFIG.shouldShowVehicleSyndicationList()) {
        actions.getSyndicationDetail(vehicleId);
      }
    }, ES_SYNC_DELAY);

    vehicleObj = this.updateMarketingInfo(vehicleObj, vehicleId);
    return vehicleObj;
  };

  saveValuationDetails = async () => {
    const { onAction } = this.props;
    await onAction({ type: ACTION_TYPES.SAVE_VALUATION_DETAILS });
  };

  renderInformationModal = (values, message = EMPTY_STRING, additional) => {
    showConfirmationModal(
      {
        title: __('Information'),
        message: this.renderInfoModalContent(message),
        width: Modal.SIZES.SM,
        submitBtnText: __('Confirm'),
        hideCancel: true,
        hideCloseIcon: true,
        onCancel: _partial(this.setIsCTADisabled, false),
      },
      () => this.handleInformationModalConfirm(values, additional)
    );
  };

  handleInformationModalConfirm = (values, additional) => {
    this.onSaveUpdateAction(this.updateVehicleObjectForServer(values), additional);
  };

  renderInfoModalContent = (message = EMPTY_STRING) => (
    <>
      <div className={cx('d-flex align-items-center', styles.infoModal)}>
        <FontIcon size={SIZES.MD}>icon-info</FontIcon>
        <div className={styles.infoContent}>{message}</div>
      </div>
      <Content>{__(`Click 'Confirm' to proceed.`)}</Content>
    </>
  );

  onSubmit = async additional => {
    const {
      isEditMode,
      vehicleDetailsInStore,
      bodyTypeMapping,
      vehicleTypes,
      isVehicleSubTypeMandatory,
      vehicleDetailsForm,
      onAction,
      lite,
      enableStockNumberGenerationOnSave,
    } = this.props;

    const { postingModel: postingModelInState } = this.state;
    const postingModel = additional?.postingModel ?? postingModelInState;

    const { vehicleType, vin } = vehicleDetailsForm;
    const id = _get(vehicleDetailsInStore, 'general.id');

    const saveAction = async () => {
      const updatedVehicleDetails = { ...vehicleDetailsForm };
      if (shouldGenerateStockIdOnSave({ enableStockNumberGenerationOnSave, values: vehicleDetailsForm, isEditMode })) {
        const generatedStockID = await onAction({ type: ACTION_TYPES.GENERATE_STOCK_ID_ON_SAVE });
        if (generatedStockID) {
          updatedVehicleDetails.stockID = generatedStockID;
        } else {
          this.setIsCTADisabled(false);
          return;
        }
      }

      this.saveValuationDetails();
      if (this.isTertiaryButtonClicked) {
        this.onDraftSave(updatedVehicleDetails, additional);
        return;
      }

      if (!lite) {
        if (!this.shouldShowRevampedCTA && postingModel && this.shouldPostAccounting()) {
          this.postToAccounting(this.updateVehicleObjectForServer(updatedVehicleDetails));
          this.setIsCTADisabled(false);
          return;
        }
        if (!id) {
          await this.setAdditionalCost(updatedVehicleDetails, bodyTypeMapping);
        } else {
          const glBalance = await this.getGLBalance(id);
          onAction({ type: ACTION_TYPES.UPDATE_GL_BALANCE, payload: { glBalance } });
        }
        const isValidated = await this.handleVehicleUpdateValidation(additional);

        if (isValidated) return;
      }

      this.onSaveUpdateAction(this.updateVehicleObjectForServer(updatedVehicleDetails), additional);
    };

    if (!isVehicleSubTypeRequired(vehicleDetailsForm, vehicleTypes, isVehicleSubTypeMandatory)) {
      return;
    }

    if (_isEmpty(vehicleType || _get(vehicleDetailsInStore, 'general.vehicleType'))) {
      toaster('error', MESSAGES.INVALID_VEHICLE_TYPE_WARNING);
      return;
    }

    this.setIsCTADisabled(true);
    if (!isEditMode && (await checkIfVINHasSoldVehicle(vin))) {
      if (VEHICLE_TYPE[vehicleType] === VEHICLE_TYPE.NEW) {
        showConfirmationModal(
          {
            title: LABELS.WARNING,
            message: MESSAGES.SOLD_VEHICLE_WARNING,
            submitBtnText: LABELS.CONFIRM,
            onCancel: _partial(this.setIsCTADisabled, false),
          },
          saveAction
        );
        return;
      }
    }
    saveAction();
  };

  handleValidateVehicleSaveErrorAndInfoModal = async additional => {
    const { vehicleDetailsForm, actions, onAction, freezeUpdateForValidateVehicleSave } = this.props;
    await onAction({
      type: ACTION_TYPES.HANDLE_ERROR_AND_INFO_MODAL_FOR_VALIDATE_VEHICLE_SAVE,
      payload: {
        actions,
        vehicleDetailsForm,
        additional,
        renderInformationModal: (values, message, additional) =>
          this.renderInformationModal(values, message, additional),
        setIsCTADisabled: value => this.setIsCTADisabled(value),
      },
    });
    return freezeUpdateForValidateVehicleSave;
  };

  handleVehicleUpdateValidation = async additional => {
    const { vehicleDetailsForm, vehicleDetails, vehicleDetailsInStore, isEditMode } = this.props;

    const vehicleDetailToSave = this.getVehicleDetails(vehicleDetailsForm);

    const { rawVehicleData } = vehicleDetailsInStore;
    const infoModalData = getRenderInfoModalData(vehicleDetailToSave, rawVehicleData);
    const canRenderInfoModal = shoulRenderInfoModal(infoModalData);

    const { accountPosted } = vehicleDetails;

    const vehicleType = VehicleDetailsReader.getVehicleType(vehicleDetails);
    const status = _get(vehicleDetailToSave, 'status');
    const source = _get(vehicleDetailToSave, 'sourceInfo.source');
    const isValidDealToShowModal =
      vehicleType !== VEHICLE_TYPES.SPECIAL &&
      status !== VEHICLE_STATUS.SOLD &&
      !_includes(source, SOURCE_ENUM.TRADE_IN);

    if (PROGRAM_CONFIG.shouldValidateVehicleOnSave() && isEditMode) {
      await this.handleValidateVehicleSaveErrorAndInfoModal(additional);
      const { freezeUpdateForValidateVehicleSave } = this.props;
      return freezeUpdateForValidateVehicleSave;
    }

    if (accountPosted && isValidDealToShowModal && canRenderInfoModal) {
      const messageToBeDisplayed = VALIDATION_MESSAGE_MAP.DEFAULT;
      this.renderInformationModal(vehicleDetailsForm, messageToBeDisplayed, additional);
      return true;
    }
    return false;
  };

  // update payload for create and update vehicle object
  updateVehicleObjectForServer(values) {
    const { vehicleDetailsInStore, lite } = this.props;
    const customFields = formatCustomFields(_get(values, 'customFields', {}) || {});
    // TODO: update validation logic
    if (!lite && !values.vin) {
      toaster('error', __('Please fill mandatory details'));
    }

    return {
      ...values,
      pricingDetails: {
        ...values.pricingDetails,
        ...vehicleDetailsInStore.pricing,
      },
      options: vehicleDetailsInStore.optionParts.options,
      parts: vehicleDetailsInStore.optionParts.parts,
      accessories: vehicleDetailsInStore?.optionParts?.accessories,
      customFields,
    };
  }

  previewInvoice = () => {
    const { invoices } = this.props;
    if (!_isEmpty(invoices)) {
      const defaultInvoice = _head(invoices);
      const gmPrintContent = InvoiceReader.gmPrintContent(defaultInvoice);
      if (_isEmpty(gmPrintContent)) {
        toaster('error', __('No Invoice To Preview'));
      }
      this.setState({
        showTabExtraContent: _isEmpty(gmPrintContent),
        showTabExtraContentPrintContent: !_isEmpty(gmPrintContent),
      });
    } else {
      this.setState({
        showTabExtraContent: true,
        showTabExtraContentPrintContent: false,
      });
    }
  };

  handelCancelAction = () => {
    this.setState({
      showTabExtraContentPrintContent: false,
      showTabExtraContent: true,
    });
  };

  previewInvoiceMouseEnter = () => {
    this.setState({ shouldShowNoInvoiceToolTip: true }, () => {
      const { invoicePreviewTooltipTimer } = this;
      if (invoicePreviewTooltipTimer) {
        clearTimeout(invoicePreviewTooltipTimer);
      }
      this.invoicePreviewTooltipTimer = setTimeout(() => {
        this.setState({ shouldShowNoInvoiceToolTip: false });
      }, INVOICE_PREVIEW_TOOLTIP_TIMER);
    });
  };

  onTabChange = activeKey => {
    this.setState({
      showTabExtraContent: _includes(['GENERAL', 'PRICING', 'OPTIONS_PARTS'], activeKey),
      showTabExtraContentPrintContent: false,
      currentActiveTabKey: activeKey,
    });
  };

  postToAccounting = values => {
    const { currentLanguageId, onAction } = this.props;
    const vehicleDetailsToSave = this.getVehicleDetails(values);
    const pricingDetails = VehicleReader.pricingDetails(values);
    const saveReadyPricingFields = parsePriceFieldValue(pricingDetails);

    const params = {
      vehicleObject: { ...vehicleDetailsToSave, pricingDetails: saveReadyPricingFields },
      langParam: currentLanguageId,
    };

    if (PROGRAM_CONFIG.shouldShowPurchaseInvoice()) {
      onAction({
        type: ACTION_TYPES.POST_TO_ACCOUNTING,
        payload: {
          ...params,
          onSuccessCb: this.onPostedToAccounting,
        },
      });
      return;
    }

    showPostToAccountingModal(params);
  };

  onPostedToAccounting = () => {
    const { vehicleDetailsInStore } = this.props;
    const damagesList = _get(vehicleDetailsInStore, 'damages.damagesList.data') || EMPTY_ARRAY;
    this.updateVehicleDamages(damagesList);
    setTimeout(this.goBackHandler, 1000);
  };

  shouldShowPrintInvoice = () => {
    const { currentActiveTabKey } = this.state;
    const { vehicleDetails } = this.props;
    const vehicleType = VehicleDetailsReader.getVehicleType(vehicleDetails);
    return (
      isVehicleTypeNew(vehicleType) &&
      (_isEmpty(currentActiveTabKey) || _includes(['GENERAL', 'PRICING', 'OPTIONS_PARTS'], currentActiveTabKey)) &&
      hasViewInvoice() &&
      !isInchcapeOrRRG()
    );
  };

  getVehicleActions = () => {
    const showAuditLogs = () => {
      this.auditLogs.openAuditLogs();
    };
    return _map(VEHICLE_ACTIONS, action => {
      const { id } = action;
      switch (id) {
        case 'auditLogs':
          return { ...action, onClick: showAuditLogs };
        default:
      }
      return action;
    });
  };

  handleSaveAction = isTertiaryButtonClicked => {
    const { isEditMode } = this.props;
    this.isTertiaryButtonClicked = isTertiaryButtonClicked;
    const stateToUpdate = {};
    if (this.shouldShowRevampedCTA) {
      stateToUpdate.doPosting = !this.isTertiaryButtonClicked;
      stateToUpdate.save = !isEditMode;
    } else stateToUpdate.save = true;
    this.setState(stateToUpdate, () => {
      this.validateTabsForm({ caller: this.handleSaveAction, callerParams: isTertiaryButtonClicked });
    });
  };

  handleUpdateAction = () => {
    this.setState({ doPosting: false, save: false, postingModel: false }, () => {
      this.validateTabsForm({ postingModel: false, caller: this.handleUpdateAction });
    });
  };

  onDraftSave = async (values, additional) => {
    const { vehicleType } = values;
    if (isValidStockTypeForDraftStatusSave(vehicleType)) {
      _set(values, 'status', VEHICLE_STATUS.DRAFT);
    }
    if (this.isSaveAsDraftClicked(values)) await this.setAdditionalCost(values);
    this.onSaveUpdateAction(this.updateVehicleObjectForServer(values), additional);
  };

  goBackHandler = () => {
    const { navigate, location } = this.props;
    handleVehicleDetailsBackNavigation({
      navigate,
      location,
    });
  };

  shouldShowTertiaryButton = () => {
    const { isEditMode, vehicleDetailsForm } = this.props;
    const { vehicleType } = vehicleDetailsForm;

    const saveAsDraft = isValidStockTypeForDraftStatusSave(vehicleType) && !isEditMode;
    if (this.shouldShowRevampedCTA) return saveAsDraft;

    if (getIsPrimaryCtaSaveAsDraft({ isEditMode, vehicleType })) return false;

    return this.shouldPostAccounting() || saveAsDraft;
  };

  policeRecordSuccessCb = (successResponse, shouldFetchDropdownParameters = false) => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.POLICE_RECORD_SUCCESS_CALLBACK,
      payload: { successResponse, shouldFetchDropdownParameters },
    });
  };

  fetchCostSummary = () => {
    const { onAction } = this.props;
    const { vehicleID } = this.state;
    onAction({
      type: ACTION_TYPES.FETCH_COST_SUMMARY,
      payload: {
        vehicleID,
      },
    });
  };

  fetchCostSummaryAndUpdateAdditionalCost = async () => {
    const { onAction, actions, currentLanguageId } = this.props;
    const { vehicleID } = this.state;
    onAction({
      type: ACTION_TYPES.FETCH_COST_SUMMARY,
      payload: {
        vehicleID,
      },
    });

    const vehicleDetails = await actions.getInventoryDetails(vehicleID, this.dealerID, currentLanguageId, false);
    actions.setMiscellaneousCost(tget(vehicleDetails, 'pricingDetails.additionalCosts'));
  };

  onPurchaseInformationClose = shouldRefetchVehicleDetails => {
    const { currentLanguageId, actions, vendorInvoiceData } = this.props;
    const { vehicleID } = this.state;
    actions.setVendorInvoiceDataOriginalToState(vendorInvoiceData);
    if (shouldRefetchVehicleDetails) {
      toaster(TOASTER_TYPE.INFO, __('Fetching Vehicle and Cost Details'));
      actions.getInventoryDetails(vehicleID, this.dealerID, currentLanguageId);
      this.fetchCostSummary();
    }
    hidePortal();
  };

  fetchAllHardPacksList = async () => {
    const { onAction } = this.props;
    const data = await onAction({ type: ACTION_TYPES.FETCH_ALL_HARDPACKS });
    return data;
  };

  validateTabsForm = (additional = EMPTY_OBJECT) => {
    validatedFormTabsCount.current = 0;
    const { tabs } = this.state;

    const contextIds = _reduce(
      tabs,
      (validContextIds, tab) => {
        if (tab.contextId) validContextIds.push(tab.contextId);
        return validContextIds;
      },
      []
    );
    _forEach(contextIds, contextId => triggerSubmit(contextId, additional));
  };

  onValidatedCb = ({ isValidationSuccessful, tabKey, errors: validationIssues, additional }) => {
    const { vehicleDetails, formFieldOverrides, fields } = this.props;
    const { tabsFormCount } = this.state;
    if (additional?.reset) {
      this.errors = {};
      this.setState({ errors: this.errors });
      this.setIsCTADisabled(false);
      return;
    }

    const { errors: formErrors } = getSegregatedValidationIssues(validationIssues, formFieldOverrides, fields);
    this.errors[tabKey] = validateTabErrors(formErrors);
    if (isInchcapeOrRRG()) {
      const { fields } = this.props;
      const shouldDisable = _some(DISABLE_CTA_ON_FIELDS_WITH_ERROR, key => _get(formErrors, getIdFromKey(fields, key)));
      this.setIsCTADisabled(shouldDisable);
    }
    this.setState({ errors: { ...this.errors } });

    if (
      (isValidationSuccessful || (isInchcape() && isVehicleStatusSold(vehicleDetails?.status))) &&
      tget(additional, 'shouldSubmit', true)
    ) {
      validatedFormTabsCount.current += 1;
      if (validatedFormTabsCount.current === tabsFormCount) {
        this.errors = {};
        this.setState({ errors: this.errors });
        this.validateConditionsToSaveVehicle(additional);
      }
    }
  };

  createRo = async (payload, onSuccessCb = _noop) => {
    const createRoResponse = isInchcapeOrRRG() ? await createRo(payload, false) : await createRo(payload);
    const { vehicleID } = this.state;
    const { currentLanguageId, isCostApprovalEnabled } = this.props;
    if (createRoResponse?.status === 'success') {
      onSuccessCb(vehicleID, this.dealerID, currentLanguageId);
    }
    this.jobModal.close();
    if (isCostApprovalEnabled) await refetchRoApprovalData(vehicleID);
  };

  getDamages = () => {
    const { actions, vehicleDetails } = this.props;
    const vehicleId = _get(vehicleDetails, 'id');

    if (vehicleId) {
      actions.getDamages(vehicleId);
    }
  };

  createSingleJobRO = ({ extraDetails = EMPTY_OBJECT }) => {
    const { settings, displayModelSource, vehicleDetails, damages, actions } = this.props;
    const serviceSettings = _get(settings, 'serviceSettings') || EMPTY_OBJECT;
    this.jobModal.show(
      { ...vehicleDetails, vehicleDamages: getOpenDamages(damages) },
      { serviceSettings, ...extraDetails },
      async (data, { recalls, pdis, techId, assignee, notes, salesPersonId, payerEstimatesConfig }) => {
        const params = [
          data,
          {
            recalls,
            pdis,
            techId,
            assignee,
            notes,
            salesPersonId,
            payerEstimatesConfig,
          },
          displayModelSource,
        ];

        const serviceObject = PROGRAM_CONFIG.shouldShowCostApproval()
          ? getSingleROPayloadForApproval(...params)
          : getSingleROPayload(...params);

        await this.createRo(serviceObject, actions.getInventoryDetails);
        this.getDamages();
      }
    );
  };

  editSingleJobRO = ({ extraDetails = EMPTY_OBJECT, onSuccessCb = _noop }) => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.EDIT_SINGLE_JOB_RO,
      payload: { extraDetails, onSuccessCb, jobModalRef: this.jobModal },
    });
  };

  getTabProps(tab) {
    const {
      vehicleDetails,
      dealerConfig,
      isEditMode,
      actions,
      customFields,
      allGlAccount,
      associatedGlAccounts,
      defaultGlAccount,
      policeRecordData,
      customerViewProps: customerViewPropsFromState,
      currentLanguageId,
      glAccountList,
      costSummary,
      declarationStatus,
      customerViewToggle,
      providersWithValuation,
      vehicleDetailsForm,
      customFormCategories,
      countryOptions,
      isCostApprovalEnabled,
      vendorInvoiceData,
      lite,
      metaData,
      vehicleStockTypeHistory,
      enterpriseV2Workspaces,
      isVehicleConfiguratorEnabled,
      enableUsedVehiclesPricingReset,
      displayModelSource,
      autocheckReportEnabled,
      removeDefaultOptions,
      enableStockNumberGenerationOnSave,
      formFieldOverrides,
      allFieldsDisabledByDefault,
    } = this.props;
    const {
      vin,
      viSettingData,
      glAccountOptions,
      currentSelectedGlAccount,
      vehicleGlAccountId,
      vehicleID,
      showPriceDetailsSummaryDrawer,
      errors,
    } = this.state;
    const formCurrentState = vehicleDetailsForm;
    const { vehicleType, status } = formCurrentState;
    const vehicleid = isEditMode ? vehicleID : undefined;
    const customerViewProps = { ...customerViewPropsFromState, customerViewToggle };
    const visibilityConfig = {
      shouldEdit: isEditMode,
    };
    const fieldsWithError = _get(errors, [tab, 'fieldsWithError']);
    const taxClassificationDetails = tget(metaData, 'taxDetailsMetadata.taxClassificationDetails', EMPTY_ARRAY);
    const taxRates = tget(metaData, 'taxDetailsMetadata.taxRates', EMPTY_OBJECT);
    const isInvoiceAdditionRestricted = isVehicleStockTypeRestrictedForPurchaseInvoice(
      vehicleType,
      status,
      vehicleStockTypeHistory
    );

    switch (tab) {
      case TAB_ENUMS.GENERAL:
        return {
          selectedVehicle: vehicleDetails,
          dealerConfig,
          isEditMode,
          enterpriseV2Enabled: this.enterpriseV2Enabled,
          enterpriseV2Workspaces,
          vin: vin || _get(formCurrentState, 'vin'),
          getRecallsByVin: actions.getRecallsByVin,
          fetchPoliceRecordParameters: actions.fetchPoliceRecordParameters,
          updateRecallsInVehicleDetails: actions.updateRecallsInVehicleDetails,
          customFields,
          getGlAccountFromSettings: actions.getGlAccountFromSettings,
          optionsLookupEnable: SettingsReader.getOptionsLookupEnable(viSettingData),
          setLookupFailureForVehicleSave: this.setLookupFailureForVehicleSave,
          fetchSetupFields: this.fetchSectionsAndFields,
          fetchPoliceRecordData: this.fetchPoliceRecordData,
          policeRecordData,
          policeRecordSuccessCb: this.policeRecordSuccessCb,
          customerViewProps,
          fetchVendorInvoicesData: this.fetchVendorInvoicesData,
          addOrUpdateVendorInvoice: actions.addOrUpdateVendorInvoice,
          onPurchaseInformationClose: this.onPurchaseInformationClose,
          fetchAllHardPacksList: this.fetchAllHardPacksList,
          validateTabsForm: this.validateTabsForm,
          declarationStatus,
          fetchDeclarationStatus: this.fetchDeclarationStatus,
          onValidatedCb: this.onValidatedCb,
          fieldsWithError,
          countryOptions,
          createSingleJobRO: this.createSingleJobRO,
          deleteVIPurchaseInvoice: actions.deleteVIPurchaseInvoice,
          setVendorInvoiceDataToState: actions.setVendorInvoiceDataToState,
          setVendorInvoiceDataOriginalToState: actions.setVendorInvoiceDataOriginalToState,
          setVehiclePurchaseStatus: actions.setPurchaseStatus,
          lite,
          isInvoiceAdditionRestricted,
          isVehicleConfiguratorEnabled,
          enableUsedVehiclesPricingReset,
          displayModelSource,
          removeDefaultOptions,
          enableStockNumberGenerationOnSave,
          formFieldOverrides,
          allFieldsDisabledByDefault,
        };

      case TAB_ENUMS.PRICING:
        return {
          vehicleID: vehicleid,
          pricingDetails: vehicleDetails.pricingDetails,
          isEditMode,
          vin: vin || _get(formCurrentState, 'vin'),
          glAccountList: glAccountOptions,
          currentSelectedGlAccount,
          vehicleGlAccountId,
          allGlAccount,
          getGlAccountByNumber: _partial(getGlAccountByNumber, glAccountList),
          getGlAccountById: _partial(getGlAccountByAccountId, glAccountList),
          associatedGlAccounts,
          defaultGlAccount,
          disablePricingEdits: SettingsReader.getDisablePricingEditSettingsEnable(viSettingData),
          customerViewProps,
          currentLanguageId,
          fetchAllHardPacksList: this.fetchAllHardPacksList,
          costSummary,
          fetchCostSummary: this.fetchCostSummary,
          onValidatedCb: this.onValidatedCb,
          createSingleJobRO: this.createSingleJobRO,
          fieldsWithError,
          lite,
          displayModelSource,
          autocheckReportEnabled,
          removeDefaultOptions,
          formFieldOverrides,
          allFieldsDisabledByDefault,
        };

      case TAB_ENUMS.VALUATION:
        return {
          providersWithValuation,
          displayModelSource,
        };

      case TAB_ENUMS.DAMAGES:
        return {
          vehicleDetails,
          fetchCostSummary: this.fetchCostSummaryAndUpdateAdditionalCost,
          editSingleJobRO: this.editSingleJobRO,
          isEditMode,
          isCostApprovalEnabled,
          fetchAllHardPacksList: this.fetchAllHardPacksList,
          isVIMode: true,
          refetchVehicleDetails: actions.getInventoryDetails,
          currentLanguageId,
        };

      case TAB_ENUMS.DEALS:
        return {
          vehicleID,
        };

      case TAB_ENUMS.MEDIA:
        return {
          setIsUploadStillInProgress: this.setIsUploadStillInProgress,
        };

      case TAB_ENUMS.LEADS:
        return {
          vehicleDetails,
          displayModelSource,
        };
      case TAB_ENUMS.DOCUMENTS:
        return {
          vehicleDetails,
          customFormCategories,
          visibilityConfig,
        };
      case TAB_ENUMS.MARKETING:
        return {
          vehicleDetails,
          onValidatedCb: this.onValidatedCb,
          vehicleId: vehicleid,
        };
      case TAB_ENUMS.OPTIONS_PARTS:
        return {
          isEditMode,
          enablePriceUpdateForDIO: SettingsReader.enablePriceUpdateForDIO(viSettingData),
          customerViewProps,
          selectedVehicleType: vehicleType,
          showPriceDetailsSummaryDrawer,
          togglePriceDetailsSummaryDrawer: this.togglePriceDetailsSummaryDrawer,
          vendorInvoiceData,
          isConfigureOptionsDisabled: isInchcape() && isVehicleStatusSold(vehicleDetails.status),
          productTaxClassificationOptions: getProductTaxClassificationOptions(taxClassificationDetails, taxRates),
          defaultProductTaxClassification: getDefaultProductTaxClassificationOption(taxClassificationDetails),
          validateTabsForm: this.validateTabsForm,
          isVehicleConfiguratorEnabled,
          enableUsedVehiclesPricingReset,
          displayModelSource,
          removeDefaultOptions,
        };

      default:
        return EMPTY_OBJECT;
    }
  }

  validateConditionsToSaveVehicle = additional => {
    const { vehicleDetailsInStore, vehicleDetailsForm, lite } = this.props;
    const isMediaUploadInProgress = isUploadInProgress(vehicleDetailsInStore);
    if (isMediaUploadInProgress) {
      showConfirmationModal({
        title: __('Media Upload.'),
        message: __('Please wait. Media upload is in progress.'),
      });
      return;
    }

    if (!lite) {
      const vin = VehicleReader.vin(vehicleDetailsForm);
      if (!(checkValidVinLength(vin) && !this.considerLookupFailureForVehicleSave)) {
        showVINConfirmationModal({}, () => this.onSubmit(additional));
        return;
      }
    }
    this.onSubmit(additional);
  };

  isPostAccountingDisabled = () => {
    const { isEditMode, policeRecordData, vendorInvoiceData, vehicleDetailsForm, vehicleStockTypeHistory } = this.props;
    return checkIfPostingDisabled({
      vehicleDetailsForm,
      isEditMode,
      policeRecordData,
      vendorInvoiceData,
      enterpriseV2Enabled: this.enterpriseV2Enabled,
      vehicleStockTypeHistory,
    });
  };

  oemSwitchHandler = active => {
    const { onAction } = this.props;
    const { vehicleID } = this.state;
    onAction({ type: ACTION_TYPES.TOGGLE_OEM_SWITCH, payload: { active, vehicleID } });
  };

  customerViewToggleHandler = value => {
    const { onAction } = this.props;
    onAction({ type: ACTION_TYPES.TOGGLE_CUSTOMER_VIEW, payload: { value } });
  };

  toggleOptionSheetDrawer = () => {
    const { onAction } = this.props;
    onAction({ type: ACTION_TYPES.TOGGLE_OPTION_SHEET_DRAWER });
  };

  toggleFeatureSheetDrawer = () => {
    const { onAction } = this.props;
    onAction({ type: ACTION_TYPES.TOGGLE_FEATURE_SHEET_DRAWER });
  };

  shareMenuHandler = key => {
    switch (key) {
      case SHARE_MENU_KEYS.OPTION_SHEET:
        this.toggleOptionSheetDrawer();
        break;
      case SHARE_MENU_KEYS.FEATURE_SHEET:
        this.toggleFeatureSheetDrawer();
        break;
      default:
        break;
    }
  };

  onOptionSheetPDFGenerate =
    pdfActionsData =>
    ({ mediaUrl }) => {
      const { onAction } = this.props;
      onAction({ type: ACTION_TYPES.OPTION_SHEET_PDF_GENERATE, payload: { pdfActionsData, mediaUrl } });
    };

  onFeatureSheetPDFGenerate =
    pdfActionsData =>
    ({ mediaId, mediaUrl }) => {
      const { onAction } = this.props;
      const { vehicleID } = this.state;
      onAction({
        type: ACTION_TYPES.FEATURE_SHEET_PDF_GENERATE,
        payload: { vehicleID, pdfActionsData, mediaId, mediaUrl },
      });
    };

  printInvoiceButton = () => {
    const { shouldShowNoInvoiceToolTip } = this.state;
    const { invoices } = this.props;
    const disableButton = _isEmpty(invoices);

    return (
      <Tooltip
        placement={TOOLTIP_PLACEMENT.BOTTOM}
        title={disableButton ? NO_INVOICE_MESSAGE : ''}
        visible={shouldShowNoInvoiceToolTip && disableButton}>
        <Button
          className={styles.printInvoiceButton}
          onClick={this.previewInvoice}
          disabled={disableButton}
          onMouseEnter={this.previewInvoiceMouseEnter}>
          {__('Invoice Preview')}
        </Button>
      </Tooltip>
    );
  };

  extraItemsList = () => {
    const { showTabExtraContent } = this.state;
    const { vehicleDetails, isEditMode, customerViewToggle } = this.props;
    return [
      {
        type: 'oemSwitch',
        renderer: this.renderOEMSyncSwitch(),
        shouldShow:
          !PROGRAM_CONFIG.shouldHideSyndicateToOEM() && DealerPropertyHelper.isVehicleLevelSyndicationEnabled(),
      },
      {
        type: 'printInvoice',
        renderer: this.printInvoiceButton(),
        shouldShow: this.shouldShowPrintInvoice(showTabExtraContent),
      },
      {
        type: 'stockCard',
        renderer: <StockCardButton vehicleDetails={vehicleDetails} customerViewToggle={customerViewToggle} />,
        shouldShow: isEditMode && PROGRAM_CONFIG.shouldShowStockCard(),
      },
    ];
  };

  renderTabExtraContent = () => {
    const { lite } = this.props;
    return (
      <PropertyControlledComponent controllerProperty={!lite}>
        <div className={styles.navigationTabExtraContent}>
          {_map(this.extraItemsList(), item => item.shouldShow && item.renderer)}
        </div>
      </PropertyControlledComponent>
    );
  };

  renderOEMSyncSwitch = () => {
    const { vehicleDetails, isEditMode, isToggleLoading } = this.props;
    return (
      <div className="d-flex align-items-center m-r-16">
        <Switch
          checked={isEditMode && vehicleDetails.eligibleForOEMSyndication}
          onChange={this.oemSwitchHandler}
          checkedChildren={<FontIcon size={SIZES.XS}>icon-tick1</FontIcon>}
          unCheckedChildren={<FontIcon size={SIZES.XS}>icon-cross</FontIcon>}
          loading={isToggleLoading}
          disabled={!isEditMode}
        />
        <Label className="m-l-8">{__('Syndicate to OEM')}</Label>
      </div>
    );
  };

  renderTabBody = tab => {
    const { vehicleDetails, isEditMode, enterpriseV2Workspaces, lite } = this.props;
    const TabBodyComponent = TAB_KEY_VS_TAB_RENDERER[tab];
    const tabProps = this.getTabProps(tab);
    const isReadOnlyDetails = shouldReadOnlyDetails({
      vehicleDetails,
      isEditMode,
      enterpriseV2Enabled: this.enterpriseV2Enabled,
      enterpriseV2Workspaces,
      lite,
    });
    const tabsWithCustomReadOnlyStyle = [TAB_ENUMS.GENERAL, TAB_ENUMS.DOCUMENTS];

    if (TabBodyComponent) {
      return (
        <div
          className={cx('full-width full-height', {
            [styles.viewOnly]: isReadOnlyDetails && !tabsWithCustomReadOnlyStyle.includes(tab),
            'pointer-events-none': isReadOnlyDetails && tab === TAB_ENUMS.GENERAL,
          })}>
          <TabBodyComponent isReadOnlyDetails={isReadOnlyDetails} {...tabProps} />
        </div>
      );
    }
    return null;
  };

  renderTabHeader = tabConfig => {
    const { errors } = this.state;
    const { key, name } = tabConfig || EMPTY_OBJECT;
    const hasError = _get(errors, [key, 'hasError']);
    return <TabHeader tabName={name} hasError={hasError} />;
  };

  getTabBodyHeight = () => {
    const { isEditMode, enterpriseV2Workspaces, vehicleDetails, contentHeight } = this.props;
    const { isVehicleAlertBannerVisible } = this.state;
    const { shouldShow: isStickyBannerPresent } = getDetailsPageStickyBannerData({
      isEditMode,
      vehicleDetails,
      enterpriseV2Enabled: this.enterpriseV2Enabled,
      enterpriseV2Workspaces,
    });

    let tabBodyHeight = contentHeight;
    if (PROGRAM_CONFIG.shouldHideVehicleDetailsFooter()) tabBodyHeight += getScaleAdjustedSize(64);
    if (isVehicleAlertBannerVisible) tabBodyHeight -= getScaleAdjustedSize(48);
    if (isStickyBannerPresent) tabBodyHeight -= getScaleAdjustedSize(48);
    return tabBodyHeight;
  };

  renderTabPanes = () => {
    const tabBodyHeight = this.getTabBodyHeight();
    const { tabs } = this.state;

    return _map(tabs, tab => (
      <Tabs.TabPane tab={this.renderTabHeader(tab)} key={tab.key} forceRender={tab.forceRender}>
        <div style={{ height: tabBodyHeight }} className={cx('overflow-auto', tab.className)}>
          {this.renderTabBody(tab.key)}
        </div>
      </Tabs.TabPane>
    ));
  };

  setCurrentActiveTabKey = tabKey => this.setState({ currentActiveTabKey: tabKey });

  setVehicleAlertBannerVisibility = flag => this.setState({ isVehicleAlertBannerVisible: flag });

  togglePriceDetailsSummaryDrawer = flag => this.setState({ showPriceDetailsSummaryDrawer: flag });

  render() {
    const {
      isEditMode,
      vehicleDetailsInStore,
      notes,
      invoices,
      vehicleDetails,
      vehicleDetailsForm,
      displayModelSource,
      currentLanguageId,
      customerViewProps: customerViewPropsFromState,
      customerViewToggle,
      isOptionSheetDrawerVisible,
      isFeatureSheetDrawerVisible,
      userSettings,
      actions,
      vehicleTypes,
      isPostingToAccounting,
      enterpriseV2Workspaces,
      costSummary,
      lite,
      hideDrawer,
      module,
      metaData,
      vehicleStockTypeHistory,
      fields,
    } = this.props;
    const {
      initialState = EMPTY_OBJECT,
      formCurrentState = EMPTY_OBJECT,
      showTabExtraContentPrintContent,
      vin,
      isFetching,
      vehicleID,
      defaultActiveKey,
      currentActiveTabKey,
      isVehicleAlertBannerVisible,
      isCTADisabled,
      isUploadStillInProgress,
    } = this.state;
    const { trimDetails = EMPTY_OBJECT } = initialState;
    const updatedInitialState = initialState;
    const trimsToDisplay = isEditMode ? transformObjectValuesToString(getTrimDetails(trimDetails)) : '';
    const vehicleid = isEditMode ? vehicleID : undefined;
    const formInitialStateWithTrimDetails = {
      ...updatedInitialState,
      trim: trimsToDisplay,
      ...formCurrentState,
    };
    const currentUser = TEnvReader.userInfo();
    const vehicleNotes = notes[vehicleid] || EMPTY_ARRAY;
    const customerViewProps = { ...customerViewPropsFromState, customerViewToggle };
    const roSettings = tget(userSettings, 'roSettings', EMPTY_OBJECT);
    const { shouldShow: isStickyBannerPresent, bannerMessage } = getDetailsPageStickyBannerData({
      isEditMode,
      vehicleDetails,
      enterpriseV2Enabled: this.enterpriseV2Enabled,
      enterpriseV2Workspaces,
    });

    const enumConfigForAuditLogs = this.getEnumConfigForAuditLogs(metaData, fields);

    return (
      <PropertyControlledComponent controllerProperty={!isFetching} fallback={<Loader />}>
        <Page id="vehicleDetails" className="d-flex flex-column">
          <>
            <PropertyControlledComponent controllerProperty={PROGRAM_CONFIG.shouldShowVehicleAlertDrawer()}>
              <VehicleAlertDrawer
                vehicleID={vehicleID}
                containerClassName={styles.optionsTableContainer}
                optionMismatchTableClassName={styles.optionsMismatchTable}
                actions={actions}
                setCurrentActiveTabKey={this.setCurrentActiveTabKey}
                isVehicleAlertBannerVisible={isVehicleAlertBannerVisible}
                setVehicleAlertBannerVisibility={this.setVehicleAlertBannerVisibility}
                vehicleDetailsForm={vehicleDetailsForm}
              />
            </PropertyControlledComponent>
            <PropertyControlledComponent controllerProperty={isStickyBannerPresent}>
              <div className={cx('full-width d-flex align-items-center', styles.bannerContainer)}>
                <FontIcon size={SIZES.L} className={styles.importIcon}>
                  icon-info
                </FontIcon>
                <div>{bannerMessage}</div>
              </div>
            </PropertyControlledComponent>
            <PropertyControlledComponent controllerProperty={!lite}>
              <Header
                assetId={vehicleid}
                isEditMode={isEditMode}
                auditLogsRef={this.auditLogs}
                getVehicleDetails={values => this.getVehicleDetails(values)}
                displayModelSource={displayModelSource}
                currentLanguageId={currentLanguageId}
                customerViewProps={customerViewProps}
                customerViewToggleHandler={this.customerViewToggleHandler}
                shareMenuHandler={this.shareMenuHandler}
                vehicleDetailsInStore={vehicleDetailsInStore}
                vehicleTypes={vehicleTypes}
                fetchPoliceRecordData={this.fetchPoliceRecordData}
                fetchStockIdOnError={this.fetchStockIdOnError}
                handleUpdateAction={this.handleUpdateAction}
                costSummary={costSummary}
                module={module}
                refetchVehicleDetails={actions.getInventoryDetails}
              />
            </PropertyControlledComponent>
            <Page.Body className={styles.vehicleDetailsContainer}>
              <Tabs
                defaultActiveKey={defaultActiveKey}
                size="small"
                tabBarGutter={0}
                activeKey={currentActiveTabKey}
                onChange={this.onTabChange}
                tabBarExtraContent={this.renderTabExtraContent()}
                tabBarStyle={VEHICLE_DETAILS_TOP_TAB_BAR_STYLE}>
                {this.renderTabPanes()}
              </Tabs>
              <PropertyControlledComponent controllerProperty={showTabExtraContentPrintContent && !isRVDealerEnabled()}>
                <PreviewContainer
                  handelCancelAction={this.handelCancelAction}
                  vin={vin}
                  invoices={invoices}
                  showTabExtraContentPrintContent={showTabExtraContentPrintContent}
                />
              </PropertyControlledComponent>
            </Page.Body>
            <PropertyControlledComponent controllerProperty={!PROGRAM_CONFIG.shouldHideVehicleDetailsFooter()}>
              <Footer
                vehicleDetails={vehicleDetails}
                vehicleDetailsForm={vehicleDetailsForm}
                isEditMode={isEditMode}
                isCTADisabled={isCTADisabled}
                currentActiveTabKey={currentActiveTabKey}
                customerViewProps={customerViewProps}
                togglePriceDetailsSummaryDrawer={this.togglePriceDetailsSummaryDrawer}
                shouldPostAccounting={this.shouldPostAccounting()}
                handleSaveAction={this.handleSaveAction}
                handleUpdateAction={this.handleUpdateAction}
                onCancelAction={this.onCancelAction}
                showTertiaryButton={this.shouldShowTertiaryButton()}
                isTertiaryButtonClicked={this.isTertiaryButtonClicked}
                isPostAccountingDisabled={this.isPostAccountingDisabled()}
                isPostingToAccounting={isPostingToAccounting}
                isSavingVehicleDetails={vehicleDetailsInStore?.isSaving}
                isUpdatingVehicleDetails={vehicleDetailsInStore?.isUpdating}
                isUploadStillInProgress={isUploadStillInProgress}
                enterpriseV2Enabled={this.enterpriseV2Enabled}
                enterpriseV2Workspaces={enterpriseV2Workspaces}
                lite={lite}
                hideDrawer={hideDrawer}
                vehicleStockTypeHistory={vehicleStockTypeHistory}
              />
            </PropertyControlledComponent>
            <Portal className="zIndex10" />
            <AuditLogs
              ref={ref => {
                this.auditLogs = ref;
              }}
              type={ASSET_TYPES.VI}
              id={vehicleID}
              containerClassName="zIndex10-imp"
              enumConfig={enumConfigForAuditLogs}
            />
            <JobModal
              ref={ref => {
                this.jobModal = ref;
              }}
              roSettings={roSettings}
            />
            <NotesModal
              {...getNotesModalProps({ vehicleDetails: formInitialStateWithTrimDetails, displayModelSource })}
              assetId={vehicleid}
              assetType={NOTES_ASSET_TYPES.VEHICLE}
              currentUser={currentUser}
              notes={vehicleNotes}
            />
            <PostToAccountingModal
              onSubmit={this.onPostedToAccounting}
              enterpriseV2Enabled={this.enterpriseV2Enabled}
            />
            <VINConfirmationModal />
            <ConfirmationModal />
            <PropertyControlledComponent controllerProperty={PROGRAM_CONFIG.shouldShowPoliceRecord()}>
              <CarRecord />
            </PropertyControlledComponent>
            {/* <PropertyControlledComponent controllerProperty={PROGRAM_CONFIG.shouldShowCidDeclaration()}>
              <DeclarationRecord />
            </PropertyControlledComponent> */}
            <PropertyControlledComponent controllerProperty={PROGRAM_CONFIG.shouldShowOptionSheet()}>
              <OptionSheetDrawer
                vehicleDetails={vehicleDetails}
                visible={isOptionSheetDrawerVisible}
                onClose={this.toggleOptionSheetDrawer}
                onActionComplete={this.onOptionSheetPDFGenerate}
              />
            </PropertyControlledComponent>
            <PropertyControlledComponent controllerProperty={PROGRAM_CONFIG.shouldShowFeatureSheet()}>
              <FeatureSheetDrawer
                vehicleDetails={vehicleDetails}
                visible={isFeatureSheetDrawerVisible}
                onClose={this.toggleFeatureSheetDrawer}
                onActionComplete={this.onFeatureSheetPDFGenerate}
              />
            </PropertyControlledComponent>
          </>
        </Page>
      </PropertyControlledComponent>
    );
  }
}

export default VehicleDetails;
