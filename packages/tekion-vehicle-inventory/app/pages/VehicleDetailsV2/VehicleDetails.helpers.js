import React from 'react';
import { compose } from 'recompose';
import { defaultMemoize } from 'reselect';

import _every from 'lodash/every';
import _filter from 'lodash/filter';
import _find from 'lodash/find';
import _forEach from 'lodash/forEach';
import _get from 'lodash/get';
import _has from 'lodash/has';
import _includes from 'lodash/includes';
import _isEmpty from 'lodash/isEmpty';
import _isFunction from 'lodash/isFunction';
import _isNaN from 'lodash/isNaN';
import _keyBy from 'lodash/keyBy';
import _keys from 'lodash/keys';
import _map from 'lodash/map';
import _omit from 'lodash/omit';
import _reduce from 'lodash/reduce';
import _size from 'lodash/size';
import _sortBy from 'lodash/sortBy';
import _isUndefined from 'lodash/isUndefined';
import _castArray from 'lodash/castArray';
import _head from 'lodash/head';
import _some from 'lodash/some';
import _flatten from 'lodash/flatten';
import _last from 'lodash/last';
import _dropRight from 'lodash/dropRight';
import _compact from 'lodash/compact';
import _join from 'lodash/join';
import _findLast from 'lodash/findLast';
import _pick from 'lodash/pick';

import { DATE_TIME_FORMAT } from '@tekion/tekion-conversion-web';
import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from 'tbase/app.constants';
import { MILEAGE_STATUS_LABELS } from 'tbase/constants/vehicleInventory/vehicle';
import { VEHICLE_TYPES } from 'tbase/constants/vehicleInventory/vehicleTypes';
import { VIS_PRICING } from 'tbase/constants/vehicleInventory/visPrice';
import { FIELD_ADD_ON_DESCRIPTION_KEYS } from 'tbase/constants/vehicleInventory/viFormFields';
import exposeFunctionsIfTestEnvironment from 'tbase/utils/test/exposeFunctionsIfTestEnvironment';
import { tget } from 'tbase/utils/general';
import { getErrorMessage } from 'tbase/utils/errorUtils';
import { isRequiredRule } from 'tbase/utils/formValidators';
import { isRRG, isInchcapeOrRRG, isInchcape } from 'tbase/utils/sales/dealerProgram.utils';
import isNotEmpty from 'tbase/utils/isNotEmpty';
import { toaster } from 'tcomponents/organisms/NotificationWrapper';
import OPERATORS from 'tbase/constants/filterOperators';
import Request from 'tbase/builders/request';
import VehicleReader from 'tbusiness/appServices/vehicleInventory/readers/vehicle';
import {
  FIELD_ADD_ON_DESCRIPTION_KEYS_CONFIG,
  FIELD_TYPE_VS_DESCRIPTION_KEYS,
  FIELD_TYPES_KEY,
  PURCHASE_DETAILS_STATUS,
} from 'twidgets/constants/vi.constants';
import { GENERAL_FIELD_KEYS, PRICING_FIELD_KEYS } from 'twidgets/appServices/sales/config/viFormFields';
import { VALIDATOR_TYPE } from 'twidgets/appServices/vi/organisms/BuildVehicleDrawer';

import {
  BODY_TYPE_URLS,
  FULL_WIDTH,
  MESSAGES,
  PLACE_HOLDERS,
  VALID_VIN_LENGTH,
  VEHICLE_STATUS,
  COST_TYPES,
  RESTRICTED_STATUS_FOR_INVOICE_ADDITION,
} from 'constants/constants';
import PROGRAM_CONFIG from 'constants/programConfig';
import { checkIfInspectionJobDone, hasPendingRO } from 'helpers/repairOrder';
import { ASSET_SUB_TYPES } from 'constants/settings';
import { isVehicleStatusSold } from 'helpers/vehicle.helper';
import { hasVINEdit } from 'permissions/inventory.permissions';
import DealerConfigReader from 'readers/dealerConfig';
import {
  getFormattedCustomFieldValue,
  getFormattedDateValue,
  getFormattedMultiSelectValue,
  getPriceValueWithDecimals,
  parseMultiSelectValue,
  parsePriceFieldValue,
} from 'utils/fieldFormatter';
import AdditionalFieldInfo from 'molecules/AdditionalFieldInfo';
import { getFieldOptions } from 'utils';
import { isDiscountOnMSRPRequiredRule } from 'utils/validations';

import { MEDIA_TYPE } from './Components/Pictures/media.enums';
import { CustomFieldsComponent } from './VehicleDetails.customFormConfig';
import {
  VEHICLE_DETAIL_FORM_STORE_KEY,
  VEHICLE_ERROR_CODE_VS_DISPLAY_MESSAGE_CONFIG,
  KEYS_TO_OMIT_FOR_ACCOUNT_UPDATE_VALIDATION_PAYLOAD,
  VALIDATION_MESSAGE_MAP,
  VALIDATE_SAVE_VALIDATION_KEYS,
  MULTI_STYLE_ID_TRIM_FIELDS,
} from './VehicleDetails.constant';
import VehicleDetailsReader from './VehicleDetails.reader';

import styles from './vehicleDetails.module.scss';

export const getFormInitialState = () => ({
  trimDetails: {
    trim: EMPTY_STRING,
    driveType: EMPTY_STRING,
    aspiration: EMPTY_STRING,
    engineDescription: EMPTY_STRING,
    engineSize: null,
    engineCylinders: null,
    fuelType: EMPTY_STRING,
    fuelDeliveryType: EMPTY_STRING,
    transmissionControlType: EMPTY_STRING,
    bodyType: '',
  },
  vehicleType: EMPTY_STRING,
  vehicleSubType: EMPTY_STRING,
  certified: false,
  mileageType: DealerConfigReader.getDealerMilageMeasure(),
  vehicleMedia: {
    [MEDIA_TYPE.NORMAL_IMAGES]: EMPTY_ARRAY,
    [MEDIA_TYPE.THREE_SIXTY_IMAGES]: EMPTY_ARRAY,
  },
  options: EMPTY_ARRAY,
  parts: EMPTY_ARRAY,
  pricingDetails: {
    additionalCosts: EMPTY_ARRAY,
  },
});

export const getVISPricing = data => {
  const visPricing = _reduce(
    data,
    (acc, priceType = {}) => {
      const priceSourceCode = _get(priceType, 'priceSourceCode') || {};
      if (_has(VIS_PRICING, [priceSourceCode])) {
        const priceFieldName = _get(VIS_PRICING, [priceSourceCode]);
        const chargeAmount = _get(priceType, 'chargeAmount.content');
        return { ...acc, [priceFieldName]: chargeAmount };
      }
      return acc;
    },
    {}
  );

  return { ...visPricing, retailPrice: visPricing.msrp };
};

export const checkIfVINEnabled = formState => {
  const status = _get(formState, 'status');
  // If vehicle is sold, VIN should not be editable
  if (isVehicleStatusSold(status)) {
    return false;
  }

  const glBalance = _get(formState, 'pricingDetails.glBalance');
  // Can edit VIN if GL Balance is 0
  if (!glBalance) {
    return true;
  }

  return hasVINEdit();
};

export const checkValidVinLength = vin => _size(vin) === VALID_VIN_LENGTH;

export const isValidStockTypeForDraftStatusSave = vehicleType =>
  _includes([VEHICLE_TYPES.NEW, VEHICLE_TYPES.USED, VEHICLE_TYPES.DEMO], vehicleType);

const getFieldsWithError = errors =>
  _reduce(
    errors,
    (acc, error, fieldId) => {
      if (!_isEmpty(error)) acc.push(fieldId);
      return acc;
    },
    []
  );

export const validateTabErrors = errors => {
  if (!_isEmpty(errors)) {
    const fieldsWithError = getFieldsWithError(errors);
    return { hasError: !_isEmpty(fieldsWithError), fieldsWithError };
  }
  return { hasError: false, fieldsWithError: EMPTY_ARRAY };
};

export const confirmStockTypeChange = (selectedVehicle, changedVehicleType) => {
  const selectedVehicleType = tget(selectedVehicle, 'vehicleType', EMPTY_STRING);
  return (
    ((selectedVehicleType === VEHICLE_TYPES.USED && changedVehicleType === VEHICLE_TYPES.NEW) ||
      (selectedVehicleType === VEHICLE_TYPES.NEW && changedVehicleType === VEHICLE_TYPES.USED)) &&
    checkIfInspectionJobDone(selectedVehicle) &&
    hasPendingRO(selectedVehicle)
  );
};

export const getGlAccountOptions = (viSettingData, glAccountList) => {
  const glAccounts = new Set();
  const accSetUpRule = tget(viSettingData, 'accountSetting.accountSetupRules', []);
  _forEach(accSetUpRule, rule => {
    const mappings = tget(rule, 'mappings', []);
    _forEach(mappings, mapping => {
      const accountId = _get(mapping, 'accountId');
      if (!_isEmpty(accountId)) {
        glAccounts.add(accountId);
      }
    });
  });
  const glAccFromSettings = Array.from(glAccounts);
  return _filter(glAccountList, account => _includes(glAccFromSettings, account?.value));
};

export const isVehicleSubTypeRequired = (values, vehicleTypes, isVehicleSubTypeMandatory) => {
  const { vehicleSubType, vehicleType } = values;
  const subTypesByVehicleType = _keyBy(vehicleTypes, 'type');
  if (
    isVehicleSubTypeMandatory?.enable &&
    _isEmpty(vehicleSubType) &&
    _size(subTypesByVehicleType?.[vehicleType]?.subTypes)
  ) {
    toaster('error', MESSAGES.SELECT_STOCK_SUB_TYPE);
    return false;
  }
  return true;
};

export const getValueFromOptions = (options, formattedValue) =>
  !_isEmpty(_find(options, { value: formattedValue })) ? formattedValue : EMPTY_STRING;

export const updateVehicleForm = defaultMemoize((updateVehicleObject, fields) => {
  _forEach(fields, ({ value, key, formatters, options }) => {
    const formattedValue = !_isEmpty(formatters) ? compose(...formatters)(value) : value;
    const selectedValue = !_isEmpty(options) ? getValueFromOptions(options, formattedValue) : formattedValue;
    const path = [VEHICLE_DETAIL_FORM_STORE_KEY, key].join('.');
    updateVehicleObject({ [path]: selectedValue });
  });
});

export const getDealType = () => 'Retail';

export const getFormattedPricingDetails = pricingDetails =>
  _reduce(
    _keys(pricingDetails),
    (acc, val) => {
      if (!_isNaN(pricingDetails?.[val])) {
        return { ...acc, [val]: getPriceValueWithDecimals(pricingDetails?.[val]) };
      }
      return { ...acc, [val]: pricingDetails?.[val] };
    },
    EMPTY_OBJECT
  );

export const filterAdditionalCostDataForProgram = hardpacks =>
  PROGRAM_CONFIG.shouldShowRevampedHardpackSetup()
    ? _filter(hardpacks, item => item.costType !== COST_TYPES.VEHICLE_INVOICE_ITEM)
    : hardpacks;

export const haveValidFields = (fields = EMPTY_ARRAY, allFields = EMPTY_ARRAY) =>
  !_isEmpty(_filter(allFields, field => _includes(fields, field.id)));

export const getFilteredSection = (sectionName = EMPTY_STRING, sectionData = EMPTY_ARRAY, allFields) => {
  const filteredSections = _filter(
    sectionData,
    section => section.assetSubType === sectionName && haveValidFields(section.fields, allFields) && section.visibility
  );
  return _sortBy(filteredSections, 'order');
};

const CACHE_NAME = '3d-model';

export const fetch3dModels = async () => {
  caches.open(CACHE_NAME).then(cache => {
    cache.keys().then(cachedRequests => {
      const urls = cachedRequests.map(request => request.url).filter(url => url);
      // Checks if url is already cached else caches it.
      Object.keys(BODY_TYPE_URLS).forEach(bodyType => {
        if (!_includes(urls, BODY_TYPE_URLS[bodyType])) {
          cache.add(BODY_TYPE_URLS[bodyType]);
        }
      });
    });
  });
};

export const getKeyFromId = (formFields, id) => _get(_find(formFields, { id }), 'key');

export const getCustomProps = ({ key, fieldType, systemDefined }) => {
  switch (fieldType) {
    case FIELD_TYPES_KEY.DATE: {
      if (!systemDefined)
        return {
          format: DATE_TIME_FORMAT.BASE,
          placeholder: PLACE_HOLDERS.DATE,
          customFormatter: getFormattedDateValue,
        };
      return EMPTY_OBJECT;
    }
    case FIELD_TYPES_KEY.SINGLE_SELECT:
      return { placeholder: PLACE_HOLDERS.SELECT };
    case FIELD_TYPES_KEY.MULTI_SELECT: {
      if (!systemDefined)
        return {
          customFormatter: getFormattedMultiSelectValue,
          mode: 'multiple',
          parse: parseMultiSelectValue,
          placeholder: PLACE_HOLDERS.SELECT,
          defaultValue: EMPTY_ARRAY,
        };
      return EMPTY_OBJECT;
    }
    case FIELD_TYPES_KEY.PRICE_OR_COST:
      return {
        name: `customPricingFields.${key}`,
        customFieldKey: key,
        enforcePrecision: false,
      };
    default:
      return EMPTY_OBJECT;
  }
};

const combineValidators = (validators = EMPTY_ARRAY, mandatory) => {
  if (mandatory) return [...validators, isRequiredRule];
  return validators;
};

const getOptions = ({ options = EMPTY_ARRAY, key = EMPTY_STRING, systemDefined = true }) =>
  _every(options, option => !_isUndefined(option.label) && !_isUndefined(option.value))
    ? options
    : getFieldOptions(options, key, systemDefined);

export const checkFieldForSyndicationMandatory = ({ syndicationMandatoryFieldsSetup, vehicleStockType, key }) => {
  const mandatoryFieldsForSyndication = tget(syndicationMandatoryFieldsSetup, vehicleStockType, EMPTY_ARRAY);
  const isMandatorySyndicationField = _some(mandatoryFieldsForSyndication, { key });
  return isMandatorySyndicationField;
};

const getClassNamesForDecription = ({ key }) => {
  const { componentKey, classNameKey } = FIELD_ADD_ON_DESCRIPTION_KEYS_CONFIG[key] || EMPTY_OBJECT;
  const className =
    key === FIELD_ADD_ON_DESCRIPTION_KEYS.DESCRIPTION ? styles.addOnFloatRight : 'd-flex justify-content-between';
  return { componentKey, classNameKey, className };
};

export const isFieldDisabled = (formField, vehicleStatus) => {
  const { formEditable, disabled, readOnly } = formField || EMPTY_OBJECT;
  return !formEditable || disabled || readOnly || (isInchcape() && isVehicleStatusSold(vehicleStatus));
};

export const getValidators = ({
  validators = [],
  mandatory,
  fieldKey,
  enableStockNumberGenerationOnSave,
  isEditMode,
}) => {
  if (fieldKey === GENERAL_FIELD_KEYS.PRODUCTION && enableStockNumberGenerationOnSave && !isEditMode) return validators;
  if (fieldKey === PRICING_FIELD_KEYS.DISCOUNT_ON_MSRP && mandatory) return [isDiscountOnMSRPRequiredRule];
  return combineValidators(validators, mandatory);
};

const getIsFieldRequired = ({ mandatory, fieldKey, enableStockNumberGenerationOnSave, isEditMode }) => {
  if (fieldKey === GENERAL_FIELD_KEYS.PRODUCTION && enableStockNumberGenerationOnSave && !isEditMode) return false;
  return mandatory;
};

export function formFieldReader({
  fields = EMPTY_OBJECT,
  field = EMPTY_OBJECT,
  vehicleStockType = EMPTY_STRING,
  syndicationMandatoryFieldsSetup = EMPTY_OBJECT,
  vehicleStatus,
  enableStockNumberGenerationOnSave,
  isEditMode,
  formFieldOverrides = EMPTY_OBJECT,
  allFieldsDisabledByDefault,
}) {
  const formField = _omit(fields, ['hidden', 'description']);

  const overriddenField = tget(formFieldOverrides, formField?.key, EMPTY_OBJECT);
  if (overriddenField?.hidden) return null;

  const { id = EMPTY_STRING, mandatory = false } = fields;
  const style = _get(formField, 'style') || EMPTY_ARRAY;
  const label = _get(formField, 'label');
  const fieldLabel = _isFunction(label) ? label() : label;
  const description = _get(fields, 'description');
  const fieldDisabled = isFieldDisabled(formField, vehicleStatus) || allFieldsDisabledByDefault;
  let shouldDisplaySyndication = false;

  const updatedField = {
    ...formField,
    id,
    component: tget(formField, 'component', CustomFieldsComponent(field)),
    options: getOptions(_pick(formField, ['options', 'key', 'systemDefined'])),
    disabled: fieldDisabled,
    isDisabled: fieldDisabled,
    validators: getValidators({
      validators: formField.validators,
      mandatory: mandatory || overriddenField?.required,
      fieldKey: formField?.key,
      enableStockNumberGenerationOnSave,
      isEditMode,
    }),
    required: getIsFieldRequired({
      mandatory: mandatory || overriddenField?.required,
      fieldKey: formField?.key,
      enableStockNumberGenerationOnSave,
      isEditMode,
    }),
    style: { ...style, ...FULL_WIDTH },
    label: fieldLabel,
    ...overriddenField,
  };

  const fieldAddOnDescriptionKey = tget(fields, 'fieldAddOnDescriptionKey', EMPTY_STRING);
  const { componentKey, classNameKey, className } = getClassNamesForDecription({ key: fieldAddOnDescriptionKey });

  if (PROGRAM_CONFIG.shouldShowVehicleSyndicationList()) {
    shouldDisplaySyndication = checkFieldForSyndicationMandatory({
      syndicationMandatoryFieldsSetup,
      vehicleStockType,
      key: formField?.key,
    });
  }
  return {
    ...updatedField,
    [componentKey]: (
      <AdditionalFieldInfo shouldDisplaySyndication={shouldDisplaySyndication} description={description} />
    ),
    [classNameKey]: className,
  };
}

export function customFieldReader(
  fields = EMPTY_OBJECT,
  field = EMPTY_OBJECT,
  vehicleStatus,
  allFieldsDisabledByDefault
) {
  const formField = _omit(fields, ['hidden', 'description']);
  const description = _get(fields, 'description');
  const fieldDisabled = isFieldDisabled(formField, vehicleStatus) || allFieldsDisabledByDefault;
  const { id = EMPTY_STRING, mandatory = false, name = EMPTY_STRING, key } = field;
  const addOn = {
    label: name,
    name: `customFields.${key}`,
    parse: value => [{ value }],
    style: FULL_WIDTH,
    customFormatter: getFormattedCustomFieldValue,
    ...getCustomProps(field),
  };
  const { fieldType } = formField;
  const dateFormat = { format: DATE_TIME_FORMAT.BASE };

  const { componentKey, classNameKey, className } = getClassNamesForDecription({
    key: FIELD_TYPE_VS_DESCRIPTION_KEYS[fieldType],
  });

  return {
    ...formField,
    ...(!_get(formField, 'systemDefined') ? addOn : EMPTY_OBJECT),
    id,
    ...(fieldType === FIELD_TYPES_KEY.DATE && dateFormat),
    component: !_isEmpty(formField) ? formField.component : CustomFieldsComponent(field),
    options: getOptions(_pick(formField, ['options', 'key', 'systemDefined'])),
    disabled: fieldDisabled,
    isDisabled: fieldDisabled,
    validators: combineValidators(formField.validators, mandatory),
    required: mandatory,
    ...(name !== 'Trim' && { required: mandatory }),
    [componentKey]: <AdditionalFieldInfo description={description} />,
    [classNameKey]: className,
  };
}

const getSectionFields = sections => _flatten(_map(sections, 'fields'));

export const getFilteredFields = (fields, sections) => {
  const sectionFields = getSectionFields(sections);
  return _filter(fields, field => _includes(sectionFields, field.id));
};

export const getMileageStatusOptions = () =>
  _map(_keys(MILEAGE_STATUS_LABELS), value => ({ label: MILEAGE_STATUS_LABELS[value], value }));

export const getCastValue = value => _head(_castArray(value));

export const getCustomKey = key => `customFields.${key}`;

export const getAvailableFields = (sections, fields) => {
  const sectionFields = getSectionFields(sections);
  return _filter(fields, field => _includes(sectionFields, field.id));
};

export const getMultilingualAccessor = (valuePath, language) => {
  const fieldValueId = _last(valuePath);
  const pathPrefix = _dropRight(valuePath);
  return [...pathPrefix, 'languages', 'locale', language, fieldValueId].join('.');
};

export const getCountriesOptions = countries =>
  _map(countries, country => {
    const label = `${_get(country, 'name', EMPTY_STRING)} (${_get(country, 'twoDigitCountryCode', EMPTY_STRING)})`;
    const value = _get(country, 'threeDigitCountryCode', EMPTY_STRING);
    return {
      label,
      value,
      countryName: _get(country, 'name', EMPTY_STRING),
      twoDigitCountryCode: _get(country, 'twoDigitCountryCode', EMPTY_STRING),
    };
  });

export const getVehicleSyndicationPayload = vehicleId =>
  new Request().addFilter('id', OPERATORS.IN, _castArray(vehicleId));

export const getVIErrorMessage = (error, vehicleData, defaultMessage = __('Something went wrong.')) => {
  const errorCode = _get(error, 'data.errorDetails.errorCode');
  const displayMessage =
    VEHICLE_ERROR_CODE_VS_DISPLAY_MESSAGE_CONFIG[errorCode]?.(vehicleData) || getErrorMessage(error, defaultMessage);
  return displayMessage;
};

export const getVehicleAccountUpdateValidationPayload = vehicleDetailsForm =>
  _omit(vehicleDetailsForm, KEYS_TO_OMIT_FOR_ACCOUNT_UPDATE_VALIDATION_PAYLOAD);

export const getAllValidationMessage = async ({ validateVehicleSave, vehicleDetailsForm }) => {
  const { stockTypeChangePossible, stockTypeChangeErrorMessages, validateSaveResponse } =
    await vehicleStockTypeChangeValidation({
      validateVehicleSave,
      vehicleDetailsForm,
    });

  if (!stockTypeChangePossible && !_isEmpty(stockTypeChangeErrorMessages)) {
    return {
      stockTypeChangeErrorMessages,
    };
  }

  const validatedMessageList = _reduce(
    validateSaveResponse,
    (acc, curr, key) => {
      const validationMessage = VALIDATION_MESSAGE_MAP[key] || VALIDATION_MESSAGE_MAP.DEFAULT;
      const messageToDisplay = _get(curr, 'isValid', false) ? validationMessage : EMPTY_STRING;
      return acc.add(messageToDisplay);
    },
    new Set()
  );

  return { messageToBeDisplayed: _join(_compact([...validatedMessageList]), ', ') };
};

export const isSaveAndUpdateDisabledForVehicle = ({ vehicleDetails, isCTADisabled }) => {
  const vehicleStatus = VehicleReader.status(vehicleDetails);
  return (
    isCTADisabled ||
    vehicleStatus === VEHICLE_STATUS.TENTATIVE ||
    (isInchcapeOrRRG() && _includes(RESTRICTED_STATUS_FOR_INVOICE_ADDITION.COMMON_RESTRICTED_STATUSES, vehicleStatus))
  );
};

export const getIsPrimaryCtaSaveAsDraft = ({ isEditMode, vehicleType }) =>
  isRRG() && !isEditMode && vehicleType === VEHICLE_TYPES.USED;

export const isUploadInProgress = vehicleDetails => {
  const vehicleMedia = VehicleDetailsReader.getVehicleMedia(vehicleDetails);
  const keys = Object.keys(vehicleMedia);
  let uploadInProgress = false;

  if (PROGRAM_CONFIG.shouldShowRevampedMediaTab())
    uploadInProgress = _some(vehicleMedia, ({ mediaList }) => _some(mediaList, media => media.isUploading));
  else {
    keys.forEach(key => {
      const mediaList = vehicleMedia[key] || [];
      mediaList.forEach(media => {
        if (media.isUploading) {
          uploadInProgress = true;
        }
      });
    });
  }

  return uploadInProgress;
};

export const getLastPostedInvoice = invoiceData =>
  _findLast(invoiceData, ({ accountingStatus }) => accountingStatus === PURCHASE_DETAILS_STATUS.POSTED);

export const getInvoiceNoListText = invoiceList => _join(invoiceList);

export const getInvoiceDataToSaveOrPost = vendorInvoiceData => {
  const filteredInvoices = _filter(
    vendorInvoiceData,
    invoice =>
      _includes([PURCHASE_DETAILS_STATUS.CREATED, PURCHASE_DETAILS_STATUS.ERROR], invoice?.accountingStatus) ||
      !invoice?.accountingStatus
  );
  return _map(filteredInvoices, invoice => {
    if (!invoice?.accountingStatus) {
      return _omit(invoice, 'id');
    }
    return invoice;
  });
};

export const getTaxClassificationData = (taxDetailsMetadata, taxRates) => {
  const { taxCode, taxCodeId: taxId } = _find(taxDetailsMetadata, { default: true }) || EMPTY_OBJECT;
  const { taxRate: vatPercent } = _get(taxRates, [taxCode, 0], EMPTY_OBJECT);

  return { taxCode, taxId, vatPercent };
};

export const addVehicleDetailsToVIStore = cb => (state, ownProps) => cb(state, ownProps);

export const addDefaultProductTaxClassificationToOptions = state => {
  const { taxClassificationDetails, taxRates } = tget(state, 'bootstrap.metaData.taxDetailsMetadata', EMPTY_OBJECT);
  const taxDetails = getTaxClassificationData(taxClassificationDetails, taxRates);
  const allOptions = tget(state, 'vehicleDetails.allOptions', EMPTY_ARRAY);
  return _map(allOptions, option => ({
    ...option,
    optionVatInfo: { ...(option.optionVatInfo || EMPTY_OBJECT), ...taxDetails },
  }));
};

export const getEnumConfigForTaxClassification = metaData => {
  const taxClassificationDetails = _get(metaData, 'taxDetailsMetadata.taxClassificationDetails');
  return _reduce(
    taxClassificationDetails,
    (enumConfig, taxDetail) => {
      const { id, classificationName } = taxDetail || EMPTY_OBJECT;
      return { ...enumConfig, ...(id ? { [id]: classificationName } : {}) };
    },
    {}
  );
};

export const getEnumConfigForAttributeDisplay = allFields =>
  _reduce(
    allFields,
    (acc, field) => {
      const { assetSubType, key, name } = field || EMPTY_OBJECT;
      const isCustomField =
        assetSubType === ASSET_SUB_TYPES.CUSTOM_FIELDS_GENERAL ||
        assetSubType === ASSET_SUB_TYPES.CUSTOM_FIELDS_PRICING;
      return isCustomField ? { ...acc, [key]: name } : acc;
    },
    {}
  );

export const getEnumConfigForAuditLogs = (metaData, allFields) => ({
  productTaxClassification: getEnumConfigForTaxClassification(metaData),
  attributeDisplay: getEnumConfigForAttributeDisplay(allFields),
});

export const getNonSelectedItems = (allItems, selectedItems, key) =>
  _filter(
    allItems,
    item => !_some(selectedItems, selectedItem => (selectedItem[key] || selectedItem) === (item[key] || item))
  );

export const getProductTaxClassificationOptions = (productTax, taxRates = EMPTY_OBJECT) =>
  _map(productTax, item => {
    const { classificationName, id, taxCode, taxCodeId, default: defaultOption } = item || EMPTY_OBJECT;
    const taxRate = _get(taxRates, [taxCode, 0, 'taxRate']);
    return {
      label: classificationName,
      value: id,
      option: {
        taxCode,
        taxCodeId,
        defaultOption,
        taxRate,
      },
    };
  });

export const getDefaultProductTaxClassificationOption = productTax => _find(productTax, { default: true });

export const isValidMarketingContent = marketingContent => _some(marketingContent, isNotEmpty);

export const vehicleStockTypeChangeValidation = async ({ validateVehicleSave, vehicleDetailsForm }) => {
  const payload = getVehicleAccountUpdateValidationPayload(vehicleDetailsForm);
  const validateSavePayload = parsePriceFieldValue(payload);
  const response = await validateVehicleSave(validateSavePayload);
  const stockTypeChangePossible = tget(
    response,
    ['data', VALIDATE_SAVE_VALIDATION_KEYS.STOCK_TYPE_CHANGE_VALIDATION_PASSED, 'isValid'],
    false
  );
  const stockTypeChangeFailureMessages = tget(
    response,
    ['data', VALIDATE_SAVE_VALIDATION_KEYS.STOCK_TYPE_CHANGE_VALIDATION_PASSED, 'comments'],
    EMPTY_ARRAY
  );
  return {
    stockTypeChangePossible,
    stockTypeChangeErrorMessages: stockTypeChangeFailureMessages,
    validateSaveResponse: response?.data,
  };
};

export const getUpdatedVinDetailFields = (vinDetails, hasMultipleStyleId) =>
  hasMultipleStyleId
    ? {
        ...vinDetails,
        ..._reduce(
          MULTI_STYLE_ID_TRIM_FIELDS,
          (acc, trimField) => {
            acc[trimField] = null;
            return acc;
          },
          {}
        ),
      }
    : vinDetails;

export const getOverriddenField = (initialFields, overrides) => {
  const overriddenField = { ...initialFields };
  _forEach(
    overrides,
    (fieldConfig, key) => {
      if (!overriddenField[key]) overriddenField[key] = fieldConfig;
      else overriddenField[key] = { ...overriddenField[key], ...fieldConfig };
    },
    {}
  );
  return overriddenField;
};

export const getSegregatedValidationIssues = (validationIssues, overrides, fields) => {
  const warnings = {};
  const errors = { ...validationIssues };
  _forEach(validationIssues, (error, id) => {
    const key = getKeyFromId(fields, id);
    if (overrides[key] && overrides[key].validatorType === VALIDATOR_TYPE.WARNING) {
      warnings[id] = error;
      delete errors[id];
    }
  });
  return { warnings, errors };
};

export const __tests__ = exposeFunctionsIfTestEnvironment({
  combineValidators,
  getIsFieldRequired,
});
