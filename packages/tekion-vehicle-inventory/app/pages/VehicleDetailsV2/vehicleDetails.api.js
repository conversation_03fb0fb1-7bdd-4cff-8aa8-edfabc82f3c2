import { stringInterpolate } from 'tbase/formatters/string';
import { URL_TYPES } from 'tbase/constants/api';
import { MODULE } from 'twidgets/appServices/vi/organisms/BuildVehicleDrawer';

import DealerManager from 'utils/dealerManager';
import Http from 'services/http';

const API_PATHS = {
  GET_VIS_PRICE: '/api-vi/u/v1.0.0/oem/pricing?vin={vin}',
  GET_GL_BALANCE: '/api-vi/u/v1.0.0/inventory/{vehicleId}/glBalance',
  FETCH_ALL_HARD_PACKS: '/vi-setup/u/vi/additionalCosts/all?langParam={langParam}',
  FETCH_OPTIONS_BY_MODEL_CODE: '/api-vi/u/v1.0.0/options/{modelCode}',
  FETCH_INVOICES_BY_VIN: '/api-vi/u/v1.0.0/invoices/{vin}',
  FETCH_RECALLS_BY_VIN: '/api-vi/u/v1.0.0/recalls/{vin}',
  PRINT_LABEL: '/sales/core/u/deal/label/vi/print',
  FETCH_VEHICLE_POSTINGS: '/api-vi/u/v1.0.0/inventory/{vehicleId}/getPostings',
  FETCH_MAPPED_GL_ACCOUNTS: '/api-vi/u/v1.0.0/inventory/glAccounts',
  CREATE_VEHICLE: '/api-vi/u/v1.0.0/inventory',
  FETCH_ALL_SECTIONS: '/vi-setup/u/vi/sections/fetch-target',
  FETCH_ALL_FIELDS: '/vi-setup/u/vi/sf/field/target',
  FETCH_INVOICES_BY_VEHICLE_ID: '/api-vi/u/v1.0.0/inventory/{vehicleId}/purchase-details',
  DELETE_INVOICE_BY_INVOICE_ID: '/api-vi/u/v1.0.0/inventory/purchase-details/{invoiceId}',
  UPSERT_INVOICES_BY_VEHICLE_ID: '/api-vi/u/v1.0.0/inventory/{vehicleId}/purchase-details',
  FETCH_COST_SUMMARY: '/api-vi/u/v1.0.0/inventory/{vehicleId}/costs',
  PATCH_VEHICLE_UPDATE: '/api-vi/u/v1.0.0/inventory/{vehicleId}/patch',
  ALL_COUNTRIES: '/api-core/u/global-settings/country/all',
  VALIDATE_VEHICLE_SAVE: '/vi/u/v1.0.0/inventory/validate-save',
  FETCH_RETAIL_PRICE_EXCLUDING_VAT: '/api-vi/u/external/galaxy/calculate/RETAIL_PRICE_EXCLUDING_VAT',
  FETCH_BASE_RETAIL_EXCLUDING_VAT: '/api-vi/u/external/galaxy/calculate/BASE_RETAIL_EXCLUDING_VAT',
  UPDATE_INCOME_DETAILS_PREFERENCE: '/api-vi/u/v1.0.0/inventory/{vehicleId}/costs/income-details',
  CREATE_VEHICLE_WITH_INVOICE: '/api-vi/u/v1.0.0/inventory/vehicle-and-purchase-details',
  UPDATE_VEHICLE_WITH_INVOICE: '/api-vi/u/v1.0.0/inventory/{vehicleId}/vehicle-and-purchase-details',
  CREATE_ALL_OPTIONS: '/api-vi/u/v1.0.0/inventory/options',
  ALL_OPTIONS: '/api-vi/u/v1.0.0/inventory/{optionId}/options',
  GET_SETTINGS: '/vi-setup/u/vi',
  STOCK_TYPE_CHANGE_HISTORY: '/vi/u/v1.0.0/inventory/{vehicleId}/stock-type-change-history',
};

export default class VehicleDetailsAPI extends DealerManager {
  static fetchVisPrice(vin) {
    return Http.get(URL_TYPES.CDMS, stringInterpolate(API_PATHS.GET_VIS_PRICE, { vin }));
  }

  static getGlBalance(vehicleId) {
    return Http.get(URL_TYPES.CDMS, stringInterpolate(API_PATHS.GET_GL_BALANCE, { vehicleId }));
  }

  static fetchAllHardPacks(payload, langParam) {
    return Http.post(URL_TYPES.CDMS, stringInterpolate(API_PATHS.FETCH_ALL_HARD_PACKS, { langParam }), payload);
  }

  static fetchOptions(modelCode) {
    return Http.get(URL_TYPES.CDMS, stringInterpolate(API_PATHS.FETCH_OPTIONS_BY_MODEL_CODE, { modelCode })).then(
      ({ data }) => data
    );
  }

  static fetchInvoicesByVIN(vin) {
    return Http.get(URL_TYPES.CDMS, stringInterpolate(API_PATHS.FETCH_INVOICES_BY_VIN, { vin }));
  }

  static fetchRecallsByVIN(vin) {
    const queryParams = { dealerId: this.getDealerId() };
    return Http.get(URL_TYPES.CDMS, stringInterpolate(API_PATHS.FETCH_RECALLS_BY_VIN, { vin }), queryParams);
  }

  static doLabelPrint(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.PRINT_LABEL, payload);
  }

  static fetchVehiclePostings(vehicleId) {
    return Http.get(URL_TYPES.CDMS, stringInterpolate(API_PATHS.FETCH_VEHICLE_POSTINGS, { vehicleId }));
  }

  static fetchMappedGlAccounts(payload) {
    const queryParams = { dealerId: this.getDealerId() };
    return Http.post(URL_TYPES.CDMS, API_PATHS.FETCH_MAPPED_GL_ACCOUNTS, payload, queryParams);
  }

  static createVehicle(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.CREATE_VEHICLE, payload);
  }

  static fetchAllSections(payload, assetType, vehicleStoreType) {
    const queryParams = {
      assetType,
      vehicleType: vehicleStoreType,
      dealerId: this.getDealerId(),
    };
    return Http.post(URL_TYPES.CDMS, API_PATHS.FETCH_ALL_SECTIONS, payload, queryParams);
  }

  static fetchAllFields(payload, assetType, vehicleStoreType, module = MODULE.VI) {
    const queryParams = {
      assetType,
      vehicleType: vehicleStoreType,
      module,
      dealerId: this.getDealerId(),
    };
    return Http.post(URL_TYPES.CDMS, API_PATHS.FETCH_ALL_FIELDS, payload, queryParams);
  }

  static fetchVendorInvoices(vehicleId) {
    return Http.get(URL_TYPES.CDMS, stringInterpolate(API_PATHS.FETCH_INVOICES_BY_VEHICLE_ID, { vehicleId }));
  }

  static deleteVIPurchaseInvoice(invoiceId) {
    return Http.delete(URL_TYPES.CDMS, stringInterpolate(API_PATHS.DELETE_INVOICE_BY_INVOICE_ID, { invoiceId }));
  }

  static createVendorInvoice(vehicleId, payload) {
    return Http.post(
      URL_TYPES.CDMS,
      stringInterpolate(API_PATHS.UPSERT_INVOICES_BY_VEHICLE_ID, { vehicleId }),
      payload
    );
  }

  static updateVendorInvoice(vehicleId, payload) {
    return Http.put(URL_TYPES.CDMS, stringInterpolate(API_PATHS.UPSERT_INVOICES_BY_VEHICLE_ID, { vehicleId }), payload);
  }

  static fetchCostSummary(vehicleId, params) {
    return Http.get(URL_TYPES.CDMS, stringInterpolate(API_PATHS.FETCH_COST_SUMMARY, { vehicleId }), params);
  }

  static patchUpdateVehicle(vehicleId, payload) {
    return Http.put(URL_TYPES.CDMS, stringInterpolate(API_PATHS.PATCH_VEHICLE_UPDATE, { vehicleId }), payload);
  }

  static getAllCountries() {
    return Http.get(URL_TYPES.CDMS, API_PATHS.ALL_COUNTRIES);
  }

  static validateVehicleSave(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.VALIDATE_VEHICLE_SAVE, payload);
  }

  static fetchRetailPriceExclVAT(payload) {
    const queryParams = { dealerId: this.getDealerId() };
    return Http.post(URL_TYPES.CDMS, API_PATHS.FETCH_RETAIL_PRICE_EXCLUDING_VAT, payload, queryParams);
  }

  static fetchBaseRetailExclVAT(payload) {
    const queryParams = { dealerId: this.getDealerId() };
    return Http.post(URL_TYPES.CDMS, API_PATHS.FETCH_BASE_RETAIL_EXCLUDING_VAT, payload, queryParams);
  }

  static updateIncomeDetailsPreference(payload, vehicleId) {
    return Http.post(
      URL_TYPES.CDMS,
      stringInterpolate(API_PATHS.UPDATE_INCOME_DETAILS_PREFERENCE, { vehicleId }),
      payload
    );
  }

  static createVehicleWithInvoice(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.CREATE_VEHICLE_WITH_INVOICE, payload);
  }

  static updateVehicleWithInvoice(vehicleId, payload) {
    return Http.post(URL_TYPES.CDMS, stringInterpolate(API_PATHS.UPDATE_VEHICLE_WITH_INVOICE, { vehicleId }), payload);
  }

  static postAllOptions(payload) {
    const queryParams = { dealerId: this.getDealerId() };
    return Http.post(URL_TYPES.CDMS, API_PATHS.CREATE_ALL_OPTIONS, payload, queryParams);
  }

  static fetchAllOptions(optionId) {
    const queryParams = {
      dealerId: this.getDealerId(),
    };
    return Http.get(URL_TYPES.CDMS, stringInterpolate(API_PATHS.ALL_OPTIONS, { optionId }), queryParams);
  }

  static updateAllOptions(optionId, payload) {
    const queryParams = { dealerId: this.getDealerId() };
    return Http.put(URL_TYPES.CDMS, stringInterpolate(API_PATHS.ALL_OPTIONS, { optionId }), payload, queryParams);
  }

  static getInventorySettings(langParam) {
    const queryParams = {
      langParam,
      dealerId: this.getDealerId(),
    };
    return Http.get(URL_TYPES.CDMS, API_PATHS.GET_SETTINGS, queryParams);
  }

  static fetchVehicleStockTypeHistory(vehicleId) {
    return Http.get(URL_TYPES.CDMS, stringInterpolate(API_PATHS.STOCK_TYPE_CHANGE_HISTORY, { vehicleId }));
  }
}
