import _constant from 'lodash/constant';

import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import { BROWSER_ASSET_TYPE } from 'tbase/helpers/media.helper';
import VEHICLE_MEDIA_ASSET_TYPES from 'tbusiness/appServices/crm/constants/vehicleAssetType';
import { GENERAL_FIELD_KEYS } from 'twidgets/appServices/sales/config/viFormFields';
import COLORS from 'tstyles/exports.scss';

import { VEHICLE_STATUS } from 'constants/constants';
import { TAB_ENUMS } from 'constants/constantEnum';
import PROGRAM_CONFIG from 'constants/programConfig';
import { getTitle as getSourceDealInfoTitle } from 'organisms/SourceDealInfo/sourceDealInfo.helpers';

export const DEBOUNCE_DELAY = 500;
export const DEBOUNCE_OPTIONS = { leading: true, trailing: false };

export const VEHICLE_STATUS_FOR_DRAFT_SAVE = [VEHICLE_STATUS.DRAFT, VEHICLE_STATUS.IN_TRANSIT];

export const INITIAL_STATE = {
  showTabExtraContent: true,
  showTabExtraContentPrintContent: false,
  providersWithValuation: EMPTY_ARRAY,
  isFetching: false,
  save: false,
  postingModel: false,
  isToggleLoading: false,
  customerViewToggle: false,
  isOptionSheetDrawerVisible: false,
  declarationStatus: EMPTY_OBJECT,
  costSummary: EMPTY_OBJECT,
  freezeUpdateForValidateVehicleSave: false,
};

export const VEHICLE_DETAILS_TOP_TAB_BAR_STYLE = {
  display: 'flex',
  alignItems: 'center',
  position: 'relative',
  borderBottom: `0.1rem solid ${COLORS.platinum}`,
  height: '6rem',
};

export const SHARE_MENU_KEYS = {
  OTHER_SHEETS: 'OTHER_SHEETS',
  OPTION_SHEET: 'OPTION_SHEET',
  FEATURE_SHEET: 'FEATURE_SHEET',
};

export const SHARE_MENU_CONFIG = [
  {
    key: SHARE_MENU_KEYS.OPTION_SHEET,
    label: __('Option Sheet'),
    shouldRender: PROGRAM_CONFIG.shouldShowOptionSheet,
  },
  {
    key: SHARE_MENU_KEYS.FEATURE_SHEET,
    label: __('Feature Sheet'),
    shouldRender: PROGRAM_CONFIG.shouldShowFeatureSheet,
  },
];

export const VEHICLE_DETAIL_FORM_STORE_KEY = 'vehicleDetailForm';

export const PREDEFINED_SECTIONS_KEYS = {
  // General Tab Sections
  OVERVIEW: 'OVERVIEW',
  VEHICLE_TYPE: 'VEHICLE_TYPE',
  VEHICLE_IDENTIFICATION: 'VEHICLE_IDENTIFICATION',
  VEHICLE_QUALIFICATION: 'VEHICLE_QUALIFICATION',
  PURCHASE_DETAILS: 'PURCHASE_DETAILS',
  POLICE_NUMBER: 'POLICE_NUMBER',
  CID_DECLARATION: 'CID_DECLARATION',
  SOURCE_DEAL_INFO: 'SOURCE_DEAL_INFO',
  OEM: 'OEM',
  SERVICE_DETAILS: 'SERVICE_DETAILS',
  APPLICABLE_WARRANTIES: 'APPLICABLE_WARRANTIES',
  GENERAL_ATTACHMENT: 'GENERAL_ATTACHMENT',
  VEHICLE_HISTORY: 'VEHICLE_HISTORY',

  // Pricing Tab Sections
  MARGIN_SUMMARY: 'MARGIN_SUMMARY',
  GL_ACCOUNT: 'GL_ACCOUNT',
  DISCOUNTS: 'DISCOUNTS',
  OFFERS: 'OFFERS',
  ADDITIONAL_BODY: 'ADDITIONAL_BODY',
  HARD_PACK: 'HARD_PACK',
  TRANSACTIONS: 'TRANSACTIONS',
  REPORTS: 'REPORTS',

  // Marketing Tab Sections
  SYNDICATION: 'SYNDICATION',
  MARKETING_INFO: 'MARKETING_INFO',
};

export const PREDEFINED_SECTIONS = {
  [PREDEFINED_SECTIONS_KEYS.OVERVIEW]: { id: PREDEFINED_SECTIONS_KEYS.OVERVIEW, title: __('Overview') },
  [PREDEFINED_SECTIONS_KEYS.VEHICLE_IDENTIFICATION]: {
    id: PREDEFINED_SECTIONS_KEYS.VEHICLE_IDENTIFICATION,
    title: __('Vehicle Identification'),
  },
  [PREDEFINED_SECTIONS_KEYS.VEHICLE_HISTORY]: {
    id: PREDEFINED_SECTIONS_KEYS.VEHICLE_HISTORY,
    title: __('Vehicle History'),
  },
  [PREDEFINED_SECTIONS_KEYS.VEHICLE_TYPE]: { id: PREDEFINED_SECTIONS_KEYS.VEHICLE_TYPE, title: __('Vehicle Type') },
  [PREDEFINED_SECTIONS_KEYS.VEHICLE_QUALIFICATION]: {
    id: PREDEFINED_SECTIONS_KEYS.VEHICLE_QUALIFICATION,
    title: __('Vehicle Qualification'),
  },
  [PREDEFINED_SECTIONS_KEYS.PURCHASE_DETAILS]: {
    id: PREDEFINED_SECTIONS_KEYS.PURCHASE_DETAILS,
    title: __('Purchase Details'),
  },
  [PREDEFINED_SECTIONS_KEYS.POLICE_NUMBER]: { id: PREDEFINED_SECTIONS_KEYS.POLICE_NUMBER, title: __('Police Number') },
  [PREDEFINED_SECTIONS_KEYS.CID_DECLARATION]: {
    id: PREDEFINED_SECTIONS_KEYS.CID_DECLARATION,
    title: __('CID'),
  },
  [PREDEFINED_SECTIONS_KEYS.SOURCE_DEAL_INFO]: {
    id: PREDEFINED_SECTIONS_KEYS.SOURCE_DEAL_INFO,
    title: getSourceDealInfoTitle,
  },
  [PREDEFINED_SECTIONS_KEYS.OEM]: { id: PREDEFINED_SECTIONS_KEYS.OEM, title: __('OEM') },
  [PREDEFINED_SECTIONS_KEYS.SERVICE_DETAILS]: {
    id: PREDEFINED_SECTIONS_KEYS.SERVICE_DETAILS,
    title: __('RO & PO Details'),
  },
  [PREDEFINED_SECTIONS_KEYS.APPLICABLE_WARRANTIES]: {
    id: PREDEFINED_SECTIONS_KEYS.APPLICABLE_WARRANTIES,
    title: __('Applicable Warranties'),
  },
  [PREDEFINED_SECTIONS_KEYS.GENERAL_ATTACHMENT]: {
    id: PREDEFINED_SECTIONS_KEYS.GENERAL_ATTACHMENT,
    title: __('Attachments'),
  },

  [PREDEFINED_SECTIONS_KEYS.MARGIN_SUMMARY]: {
    id: PREDEFINED_SECTIONS_KEYS.MARGIN_SUMMARY,
    title: __('Margin Summary'),
  },
  [PREDEFINED_SECTIONS_KEYS.GL_ACCOUNT]: { id: PREDEFINED_SECTIONS_KEYS.GL_ACCOUNT, title: __('G/L Account(s)') },
  [PREDEFINED_SECTIONS_KEYS.DISCOUNTS]: { id: PREDEFINED_SECTIONS_KEYS.DISCOUNTS, title: __('Discounts') },
  [PREDEFINED_SECTIONS_KEYS.OFFERS]: { id: PREDEFINED_SECTIONS_KEYS.OFFERS, title: __('Offers') },
  [PREDEFINED_SECTIONS_KEYS.ADDITIONAL_BODY]: {
    id: PREDEFINED_SECTIONS_KEYS.ADDITIONAL_BODY,
    title: __('Additional Body'),
  },
  [PREDEFINED_SECTIONS_KEYS.HARD_PACK]: { id: PREDEFINED_SECTIONS_KEYS.HARD_PACK, title: __('Hard Pack') },
  [PREDEFINED_SECTIONS_KEYS.TRANSACTIONS]: { id: PREDEFINED_SECTIONS_KEYS.TRANSACTIONS, title: __('Transactions') },
  [PREDEFINED_SECTIONS_KEYS.REPORTS]: {
    id: PREDEFINED_SECTIONS_KEYS.REPORTS,
    title: __('Reports'),
  },
  [PREDEFINED_SECTIONS_KEYS.SYNDICATION]: { id: PREDEFINED_SECTIONS_KEYS.SYNDICATION, title: __('Syndication') },
  [PREDEFINED_SECTIONS_KEYS.MARKETING_INFO]: {
    id: PREDEFINED_SECTIONS_KEYS.MARKETING_INFO,
    title: __('Marketing Info'),
  },
};

export const FORM_CONTEXT_ID = {
  GENERAL_FORM_CONTEXT_ID: 'GENERAL_FORM_CONTEXT_ID',
  PRICING_FORM_CONTEXT_ID: 'PRICING_FORM_CONTEXT_ID',
  MARKETING_FORM_CONTEXT_ID: 'MARKETING_FORM_CONTEXT_ID',
};

export const EXCULDED_PRICING_DETAILS_KEYS_ON_SAVE = ['transferBalance'];

export const VI_ERROR_CODES = {
  UNSOLD_VEHICLE_ERROR_CODE: 'VI136',
  MAKE_MANDATORY_ERROR_CODE: 'VI206',
  STOCK_ID_ERROR_CODE: 'VI102',
  VEHICLE_RECORD_VERSIONING_ERROR_CODE: 'VI224',
};

// create a function only inside this config, for all constants as well.
export const VEHICLE_ERROR_CODE_VS_DISPLAY_MESSAGE_CONFIG = {
  [VI_ERROR_CODES.UNSOLD_VEHICLE_ERROR_CODE]: vehicleData =>
    __('Unsold vehicle exists for given VIN: {{vin}}', { vin: vehicleData?.vin }),
  [VI_ERROR_CODES.MAKE_MANDATORY_ERROR_CODE]: _constant(__('"Make" is mandatory for creating a vehicle record')),
  [VI_ERROR_CODES.STOCK_ID_ERROR_CODE]: _constant(__('Stock # already exists')),
  [VI_ERROR_CODES.VEHICLE_RECORD_VERSIONING_ERROR_CODE]: _constant(
    __('You are trying to update an outdated vehicle record, please refresh to fetch the latest vehicle record.')
  ),
};

export const DISABLE_CTA_ON_FIELDS_WITH_ERROR = [GENERAL_FIELD_KEYS.LICENSE_PLATE_NUMBER, GENERAL_FIELD_KEYS.VIN];

export const KEYS_TO_OMIT_FOR_ACCOUNT_UPDATE_VALIDATION_PAYLOAD = ['accountId'];

export const VALIDATE_SAVE_VALIDATION_KEYS = {
  GL_ACCOUNT_MAPPING_UPDATED: 'GL_ACCOUNT_MAPPING_UPDATED',
  REGISTRATION_NUMBER_CHANGED: 'REGISTRATION_NUMBER_CHANGED',
  STOCK_TYPE_CHANGE_VALIDATION_PASSED: 'STOCK_TYPE_CHANGE_VALIDATION_PASSED',
};

export const VALIDATION_MESSAGE_MAP = {
  DEFAULT: __('The updated fields will impact the journal entries.'),
};

export const MEDIA_TYPE = {
  ...VEHICLE_MEDIA_ASSET_TYPES,
  STAMPYT_NORMAL_IMAGES: 'stampyt_images',
  STAMPYT_THREE_SIXTY_IMAGES: 'stampyt_videos_panorama',
};

export const MEDIA_CATEGORY_VS_TYPE = {
  [MEDIA_TYPE.NORMAL_IMAGES]: BROWSER_ASSET_TYPE.image,
  [MEDIA_TYPE.NORMAL_VIDEOS]: BROWSER_ASSET_TYPE.video,
};

export const MEDIA_SOURCE = {
  DECODE: 'DECODE',
};

export const REDUCER_NAME = 'vehicleDetails';

export const VI_LITE_TABS = [TAB_ENUMS.GENERAL, TAB_ENUMS.PRICING, TAB_ENUMS.OPTIONS_PARTS];

export const MULTI_STYLE_ID_TRIM_FIELDS = ['mfrModelCode', 'styleId', 'driveType', 'bodyType', 'trim'];
