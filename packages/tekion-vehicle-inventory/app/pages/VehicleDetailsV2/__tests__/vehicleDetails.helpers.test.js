import { EMPTY_OBJECT } from 'tbase/app.constants';
import { isRequiredRule } from 'tbase/utils/formValidators';
import { isInchcape, isInchcapeOrRRG } from 'tbase/utils/sales/dealerProgram.utils';
import { FIELD_TYPES_KEY } from 'twidgets/constants/vi.constants';
import { GENERAL_FIELD_KEYS, PRICING_FIELD_KEYS } from 'twidgets/appServices/sales/config/viFormFields';

import { ASSET_SUB_TYPES } from 'constants/settings';
import { isVehicleStatusSold } from 'helpers/vehicle.helper';
import { isDiscountOnMSRPRequiredRule } from 'utils/validations';

import { VALIDATION_MESSAGE_MAP } from '../VehicleDetails.constant';
import {
  getCountriesOptions,
  validateTabErrors,
  getAllValidationMessage,
  getVehicleAccountUpdateValidationPayload,
  isSaveAndUpdateDisabledForVehicle,
  getEnumConfigForTaxClassification,
  isFieldDisabled,
  getProductTaxClassificationOptions,
  getDefaultProductTaxClassificationOption,
  isValidMarketingContent,
  vehicleStockTypeChangeValidation,
  getCustomProps,
  getEnumConfigForAttributeDisplay,
  getEnumConfigForAuditLogs,
  getUpdatedVinDetailFields,
  getValidators,
  getOverriddenField,
  getSegregatedValidationIssues,
  __tests__,
} from '../VehicleDetails.helpers';
import {
  vehicleDetailsForm,
  stockTypeChangeValidationResponse,
  validateSaveAPIResponseWithStockTypeChangeAllowed,
  validateSaveAPIResponseWithStockTypeChangeNotAllowed,
  stockTypeChangeValidationResponseWithoutComments,
  stockTypeChangeErrors,
} from '../__mocks__/VehicleDetails.helpers.mock';

const { combineValidators, getIsFieldRequired } = __tests__;

jest.mock('tbase/utils/sales/dealerProgram.utils', () => ({
  isInchcape: jest.fn(),
  isInchcapeOrRRG: jest.fn(),
}));

jest.mock('helpers/vehicle.helper', () => ({
  isVehicleStatusSold: jest.fn(),
}));

describe('Test getCountriesOptions helper', () => {
  it('should return an empty array when input data is null', () => {
    const data = null;
    const result = getCountriesOptions(data);
    expect(result).toEqual([]);
  });

  it('should convert data into label-value pairs', () => {
    const data = [
      {
        name: 'Afghanistan',
        extensionCode: '93',
        twoDigitCountryCode: 'AF',
        threeDigitCountryCode: 'AFG',
      },
      {
        name: 'Andorra',
        extensionCode: '376',
        twoDigitCountryCode: 'AD',
        threeDigitCountryCode: 'AND',
      },
      {
        name: 'United Arab Emirates',
        extensionCode: '971',
        twoDigitCountryCode: 'AE',
        threeDigitCountryCode: 'ARE',
      },
    ];
    const result = getCountriesOptions(data);
    expect(result).toEqual([
      {
        label: 'Afghanistan (AF)',
        value: 'AFG',
        countryName: 'Afghanistan',
        twoDigitCountryCode: 'AF',
      },
      {
        label: 'Andorra (AD)',
        value: 'AND',
        countryName: 'Andorra',
        twoDigitCountryCode: 'AD',
      },
      {
        label: 'United Arab Emirates (AE)',
        value: 'ARE',
        countryName: 'United Arab Emirates',
        twoDigitCountryCode: 'AE',
      },
    ]);
  });
});

describe('Test validateTabErrors helper', () => {
  it('should return no errors when errors object is empty', () => {
    const errors = {};
    const result = validateTabErrors(errors);
    expect(result).toEqual({ hasError: false, fieldsWithError: [] });
  });

  it('should correctly identify errors and return their IDs', () => {
    const errors = { A: 'Error', B: 'Error', C: undefined, D: null, E: '' };
    const result = validateTabErrors(errors);
    expect(result).toEqual({ hasError: true, fieldsWithError: ['A', 'B'] });
  });
});

describe('Test getVehicleAccountUpdateValidationPayload helper', () => {
  it('should remove accountId from payload if there in the payload', () => {
    const payload = {
      accountId: 'test123',
      id: 'test387',
      status: 'SOLD',
    };
    const expectedResult = {
      id: 'test387',
      status: 'SOLD',
    };
    const result = getVehicleAccountUpdateValidationPayload(payload);
    expect(result).toEqual(expectedResult);
  });

  it('should return empty object if payload is empty', () => {
    const payload = {};
    const expectedResult = {};
    const result = getVehicleAccountUpdateValidationPayload(payload);
    expect(result).toEqual(expectedResult);
  });
});

describe('Test getAllValidationMessage helper', () => {
  it('should return stock type change error messages when stock type change is not allowed', async () => {
    const mockValidateVehicleSaveWithComments = jest.fn(() =>
      Promise.resolve(validateSaveAPIResponseWithStockTypeChangeNotAllowed)
    );

    const result = await getAllValidationMessage({
      validateVehicleSave: mockValidateVehicleSaveWithComments,
      vehicleDetailsForm,
    });

    expect(result).toEqual({
      stockTypeChangeErrorMessages: stockTypeChangeErrors,
    });
  });

  it('should return default validation message when validation keys are valid', async () => {
    const mockValidateVehicleSaveWithOutComments = jest.fn(() =>
      Promise.resolve(validateSaveAPIResponseWithStockTypeChangeAllowed)
    );

    const result = await getAllValidationMessage({
      validateVehicleSave: mockValidateVehicleSaveWithOutComments,
      vehicleDetailsForm,
    });

    expect(result).toEqual({
      messageToBeDisplayed: VALIDATION_MESSAGE_MAP.DEFAULT,
    });
  });
});

describe('Test isSaveAndUpdateDisabledForVehicle', () => {
  it('should returns true when isCTADisabled is true', () => {
    const params = {
      vehicleDetails: {
        status: 'TENTATIVE',
      },
      isCTADisabled: true,
    };
    const result = isSaveAndUpdateDisabledForVehicle(params);
    expect(result).toBe(true);
  });

  it('should returns true when vehicleStatus is VEHICLE_STATUS.TENTATIVE', () => {
    const params = {
      vehicleDetails: {
        status: 'TENTATIVE',
      },
      isCTADisabled: false,
    };
    const result = isSaveAndUpdateDisabledForVehicle(params);
    expect(result).toBe(true);
  });

  it('should returns false when isCTADisabled is false and vehicleStatus is not VEHICLE_STATUS.TENTATIVE', () => {
    const params = {
      vehicleDetails: {
        status: 'DRAFT',
      },
      isCTADisabled: false,
    };
    isInchcapeOrRRG.mockReturnValue(true);
    const result = isSaveAndUpdateDisabledForVehicle(params);
    expect(result).toBe(false);
  });
});

describe('getEnumConfigForTaxClassification', () => {
  it('should return an empty object if taxClassificationDetails is not present', () => {
    const metaData = {};
    const result = getEnumConfigForTaxClassification(metaData);
    expect(result).toEqual({});
  });

  it('should return a mapped object for given tax classification details', () => {
    const metaData = {
      taxDetailsMetadata: {
        taxClassificationDetails: [
          { id: '1', classificationName: 'Classification A' },
          { id: '2', classificationName: 'Classification B' },
        ],
      },
    };

    const expected = {
      1: 'Classification A',
      2: 'Classification B',
    };

    const result = getEnumConfigForTaxClassification(metaData);
    expect(result).toEqual(expected);
  });

  it('should handle empty objects within taxClassificationDetails', () => {
    const metaData = {
      taxDetailsMetadata: {
        taxClassificationDetails: [
          { id: '1', classificationName: 'Classification A' },
          {}, // empty object, should be ignored
          { id: '2', classificationName: 'Classification B' },
        ],
      },
    };

    const expected = {
      1: 'Classification A',
      2: 'Classification B',
    };

    const result = getEnumConfigForTaxClassification(metaData);
    expect(result).toEqual(expected);
  });

  it('should handle missing id or classificationName fields', () => {
    const metaData = {
      taxDetailsMetadata: {
        taxClassificationDetails: [
          { id: '1' }, // missing classificationName
          { classificationName: 'Classification B' }, // missing id
        ],
      },
    };

    const expected = {
      1: undefined, // missing classificationName should return undefined
    };

    const result = getEnumConfigForTaxClassification(metaData);
    expect(result).toEqual(expected);
  });
});

describe('Test isFieldDisabled', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return true when formEditable is false', () => {
    const formField = { formEditable: false, disabled: false, readOnly: false };
    const result = isFieldDisabled(formField, 'DRAFT');
    expect(result).toBe(true);
  });

  it('should return true when disabled is true', () => {
    const formField = { formEditable: true, disabled: true, readOnly: false };
    const result = isFieldDisabled(formField, 'DRAFT');
    expect(result).toBe(true);
  });

  it('should return true when readOnly is true', () => {
    const formField = { formEditable: true, disabled: false, readOnly: true };
    const result = isFieldDisabled(formField, 'SOLD');
    expect(result).toBe(true);
  });

  it('should return true when isInchcape is true and vehicle status is sold', () => {
    const formField = { formEditable: true, disabled: false, readOnly: false };
    const vehicleStatus = 'SOLD';

    isInchcape.mockReturnValue(true);
    isVehicleStatusSold.mockReturnValue(true);

    const result = isFieldDisabled(formField, vehicleStatus);
    expect(result).toBe(true);
    expect(isInchcape).toHaveBeenCalled();
    expect(isVehicleStatusSold).toHaveBeenCalledWith(vehicleStatus);
  });

  it('should return false when all conditions are false', () => {
    const formField = { formEditable: true, disabled: false, readOnly: false };
    const vehicleStatus = 'DRAFT';

    isInchcape.mockReturnValue(false);
    isVehicleStatusSold.mockReturnValue(false);

    const result = isFieldDisabled(formField, vehicleStatus);
    expect(result).toBe(false);
    expect(isInchcape).toHaveBeenCalled();
  });

  it('should return true when formField is empty ', () => {
    const result = isFieldDisabled(undefined, 'DRAFT');
    expect(result).toBe(true); // As EMPTY_OBJECT is defaulted so !formEditable is true
  });

  it('should return false when isInchcape is true but vehicle status is not sold', () => {
    const formField = { formEditable: true, disabled: false, readOnly: false };
    const vehicleStatus = 'STOCKED_IN';

    isInchcape.mockReturnValue(true);
    isVehicleStatusSold.mockReturnValue(false);

    const result = isFieldDisabled(formField, vehicleStatus);
    expect(result).toBe(false);
    expect(isInchcape).toHaveBeenCalled();
    expect(isVehicleStatusSold).toHaveBeenCalledWith(vehicleStatus);
  });
});

describe('Test getProductTaxClassificationOptions', () => {
  it('should maps productTax data to the expected format without taxRate', () => {
    const productTaxData = [
      {
        id: '64d9da1ca23dcc69a02b01df',
        classificationName: 'test-shivam',
        taxCode: 'TESTVAT',
        taxCodeId: '64cb76d079b0362d51c953c3',
        active: true,
        default: false,
      },
    ];

    const mappedData = getProductTaxClassificationOptions(productTaxData);
    expect(mappedData).toEqual([
      {
        label: 'test-shivam',
        value: '64d9da1ca23dcc69a02b01df',
        option: {
          taxCode: 'TESTVAT',
          taxCodeId: '64cb76d079b0362d51c953c3',
          defaultOption: false,
        },
      },
    ]);
  });

  it('should maps productTax data to the expected format with taxRate', () => {
    const productTaxData = [
      {
        id: '64d9da1ca23dcc69a02b01df',
        classificationName: 'test-shivam',
        taxCode: 'TESTVAT',
        taxCodeId: '64cb76d079b0362d51c953c3',
        active: true,
        default: false,
      },
    ];

    const taxRate = {
      TESTVAT: [
        {
          taxRate: 100,
        },
      ],
    };

    const mappedData = getProductTaxClassificationOptions(productTaxData, taxRate);
    expect(mappedData).toEqual([
      {
        label: 'test-shivam',
        value: '64d9da1ca23dcc69a02b01df',
        option: {
          taxCode: 'TESTVAT',
          taxCodeId: '64cb76d079b0362d51c953c3',
          defaultOption: false,
          taxRate: 100,
        },
      },
    ]);
  });

  it('should returns an empty array when passed an empty input', () => {
    const emptyInput = [];
    const mappedData = getProductTaxClassificationOptions(emptyInput);
    expect(mappedData).toEqual([]);
  });
});

describe('Test getDefaultProductTaxClassificationOption', () => {
  it('should return the default product tax classification option whose default is true', () => {
    const productTax = [
      {
        id: '64d9da1ca23dcc69a02b01df',
        classificationName: 'test-shivam',
        taxCode: 'TESTVAT',
        taxCodeId: '64cb76d079b0362d51c953c3',
        active: true,
        default: false,
      },
      {
        id: '64d9da1ca23dcc69a02b01df',
        classificationName: 'test-abc',
        taxCode: 'TESTVAT2',
        taxCodeId: '64cb76d079b0362d51c953c3',
        active: true,
        default: true,
      },
    ];
    const result = getDefaultProductTaxClassificationOption(productTax);
    expect(result).toEqual({
      id: '64d9da1ca23dcc69a02b01df',
      classificationName: 'test-abc',
      taxCode: 'TESTVAT2',
      taxCodeId: '64cb76d079b0362d51c953c3',
      active: true,
      default: true,
    });
  });
});

describe('Test isValidMarketingContent', () => {
  it('should return false if all properties in the object are null or empty', () => {
    const marketingContent = {
      headline: null,
      description: null,
      keySellingPoints: [],
    };
    expect(isValidMarketingContent(marketingContent)).toBe(false);
  });

  it('should return true if at least one property has a valid value', () => {
    const marketingContent = {
      headline: 'Great Product!',
      description: null,
      keySellingPoints: [],
    };
    expect(isValidMarketingContent(marketingContent)).toBe(true);
  });

  it('should return true if a non-empty array is provided as a property', () => {
    const marketingContent = {
      headline: null,
      description: null,
      keySellingPoints: ['Unique feature'],
    };
    expect(isValidMarketingContent(marketingContent)).toBe(true);
  });

  it('should return false for an empty object', () => {
    const marketingContent = {};
    expect(isValidMarketingContent(marketingContent)).toBe(false);
  });

  it('should return false if the object contains only empty strings or null values', () => {
    const marketingContent = {
      headline: '',
      description: null,
      keySellingPoints: [],
    };
    expect(isValidMarketingContent(marketingContent)).toBe(false);
  });

  it('should return true if an object contains a mix of valid and invalid properties', () => {
    const marketingContent = {
      headline: '',
      description: 'Amazing features!',
      keySellingPoints: [],
    };
    expect(isValidMarketingContent(marketingContent)).toBe(true);
  });
});

describe('Test vehicleStockTypeChangeValidation helper', () => {
  it('should return why stock type change is not allowed', async () => {
    const mockValidateVehicleSaveWithComments = jest.fn(() =>
      Promise.resolve(validateSaveAPIResponseWithStockTypeChangeNotAllowed)
    );

    const result = await vehicleStockTypeChangeValidation({
      validateVehicleSave: mockValidateVehicleSaveWithComments,
      vehicleDetailsForm,
    });

    expect(result).toEqual(stockTypeChangeValidationResponse);
  });

  it('should return stock type change is allowed', async () => {
    const mockValidateVehicleSaveWithOutComments = jest.fn(() =>
      Promise.resolve(validateSaveAPIResponseWithStockTypeChangeAllowed)
    );

    const result = await vehicleStockTypeChangeValidation({
      validateVehicleSave: mockValidateVehicleSaveWithOutComments,
      vehicleDetailsForm,
    });

    expect(result).toEqual(stockTypeChangeValidationResponseWithoutComments);
  });
});

describe('Test getCustomProps', () => {
  it('should return custom props for DATE field type when systemDefined is false', () => {
    const result = getCustomProps({ key: 'dateField', fieldType: FIELD_TYPES_KEY.DATE, systemDefined: false });
    expect(result).toEqual({
      format: 'BASE#',
      placeholder: 'Select Date',
      customFormatter: expect.any(Function),
    });
  });

  it('should return empty object for DATE field type when systemDefined is true', () => {
    const result = getCustomProps({ key: 'dateField', fieldType: FIELD_TYPES_KEY.DATE, systemDefined: true });
    expect(result).toEqual(EMPTY_OBJECT);
  });

  it('should return placeholder for SINGLE_SELECT field type', () => {
    const result = getCustomProps({
      key: 'singleSelectField',
      fieldType: FIELD_TYPES_KEY.SINGLE_SELECT,
      systemDefined: false,
    });
    expect(result).toEqual({ placeholder: 'Select' });
  });

  it('should return custom props for MULTI_SELECT field type when systemDefined is false', () => {
    const result = getCustomProps({
      key: 'multiSelectField',
      fieldType: FIELD_TYPES_KEY.MULTI_SELECT,
      systemDefined: false,
    });
    expect(result).toEqual({
      customFormatter: expect.any(Function),
      mode: 'multiple',
      parse: expect.any(Function),
      placeholder: 'Select',
      defaultValue: [],
    });
  });

  it('should return empty object for MULTI_SELECT field type when systemDefined is true', () => {
    const result = getCustomProps({
      key: 'multiSelectField',
      fieldType: FIELD_TYPES_KEY.MULTI_SELECT,
      systemDefined: true,
    });
    expect(result).toEqual(EMPTY_OBJECT);
  });

  it('should return custom props for PRICE_OR_COST field type', () => {
    const result = getCustomProps({
      key: 'CUSTOM_PRICE_FIELD',
      fieldType: FIELD_TYPES_KEY.PRICE_OR_COST,
      systemDefined: false,
    });
    expect(result).toEqual({
      name: 'customPricingFields.CUSTOM_PRICE_FIELD',
      customFieldKey: 'CUSTOM_PRICE_FIELD',
      enforcePrecision: false,
    });
  });

  it('should return empty object for unknown field type', () => {
    const result = getCustomProps({ key: 'unknownField', fieldType: 'UNKNOWN_TYPE', systemDefined: false });
    expect(result).toEqual(EMPTY_OBJECT);
  });
});

describe('Test getEnumConfigForAttributeDisplay helper', () => {
  it('should return an empty object when fields do not contain custom fields', () => {
    const fields = [
      { assetSubType: ASSET_SUB_TYPES.GENERAL, key: 'field1', name: 'Field 1' },
      { assetSubType: ASSET_SUB_TYPES.PRICING, key: 'field2', name: 'Field 2' },
    ];
    const result = getEnumConfigForAttributeDisplay(fields);
    expect(result).toEqual({});
  });

  it('should correctly map custom fields to the expected format', () => {
    const fields = [
      { assetSubType: ASSET_SUB_TYPES.CUSTOM_FIELDS_GENERAL, key: 'customField1', name: 'Custom Field 1' },
      { assetSubType: ASSET_SUB_TYPES.CUSTOM_FIELDS_PRICING, key: 'customField2', name: 'Custom Field 2' },
    ];
    const result = getEnumConfigForAttributeDisplay(fields);
    expect(result).toEqual({
      customField1: 'Custom Field 1',
      customField2: 'Custom Field 2',
    });
  });

  it('should only include custom fields in the result', () => {
    const fields = [
      { assetSubType: ASSET_SUB_TYPES.CUSTOM_FIELDS_GENERAL, key: 'customField1', name: 'Custom Field 1' },
      { assetSubType: ASSET_SUB_TYPES.GENERAL, key: 'field1', name: 'Field 1' },
    ];
    const result = getEnumConfigForAttributeDisplay(fields);
    expect(result).toEqual({
      customField1: 'Custom Field 1',
    });
  });

  it('should return an empty object when no fields are provided', () => {
    const result = getEnumConfigForAttributeDisplay([]);
    expect(result).toEqual({});
  });
});

describe('Test getEnumConfigForAuditLogs helper', () => {
  it('should return an object with empty attributeDisplay when metaData is empty', () => {
    const result = getEnumConfigForAuditLogs({}, []);
    expect(result).toEqual({
      productTaxClassification: {},
      attributeDisplay: {},
    });
  });

  it('should correctly map productTaxClassification and attributeDisplay when valid data is provided', () => {
    const metaData = {
      taxDetailsMetadata: {
        taxClassificationDetails: [
          { id: '1', classificationName: 'Classification A' },
          { id: '2', classificationName: 'Classification B' },
        ],
      },
    };
    const fields = [
      { assetSubType: ASSET_SUB_TYPES.CUSTOM_FIELDS_GENERAL, key: 'customField1', name: 'Custom Field 1' },
    ];

    const result = getEnumConfigForAuditLogs(metaData, fields);
    expect(result).toEqual({
      productTaxClassification: {
        1: 'Classification A',
        2: 'Classification B',
      },
      attributeDisplay: {
        customField1: 'Custom Field 1',
      },
    });
  });
});

describe('Test getUpdatedVinDetailFields', () => {
  it('should add fields from MULTI_STYLE_ID_TRIM_FIELDS as null when hasMultipleStyleId is true', () => {
    const vinDetails = { field1: 'value1', field2: 'value2' };
    const hasMultipleStyleId = true;

    const result = getUpdatedVinDetailFields(vinDetails, hasMultipleStyleId);

    const expected = {
      field1: 'value1',
      field2: 'value2',
      mfrModelCode: null,
      styleId: null,
      driveType: null,
      bodyType: null,
      trim: null,
    };

    expect(result).toEqual(expected);
  });

  it('should return vinDetails unchanged when hasMultipleStyleId is false', () => {
    const vinDetails = { field1: 'value1', field2: 'value2' };
    const hasMultipleStyleId = false;

    const result = getUpdatedVinDetailFields(vinDetails, hasMultipleStyleId);
    expect(result).toEqual({
      field1: 'value1',
      field2: 'value2',
    });
  });

  it('should add fields from MULTI_STYLE_ID_TRIM_FIELDS as null when vinDetails is empty and hasMultipleStyleId is true', () => {
    const vinDetails = {};
    const hasMultipleStyleId = true;

    const result = getUpdatedVinDetailFields(vinDetails, hasMultipleStyleId);

    const expected = {
      mfrModelCode: null,
      styleId: null,
      driveType: null,
      bodyType: null,
      trim: null,
    };

    expect(result).toEqual(expected);
  });

  it('should return empty vinDetails when vinDetails is empty and hasMultipleStyleId is false', () => {
    const vinDetails = {};
    const hasMultipleStyleId = false;

    const result = getUpdatedVinDetailFields(vinDetails, hasMultipleStyleId);
    expect(result).toEqual({});
  });

  it('should not add any fields when MULTI_STYLE_ID_TRIM_FIELDS is non-empty but hasMultipleStyleId is false', () => {
    const vinDetails = { field1: 'value1' };
    const hasMultipleStyleId = false;

    const result = getUpdatedVinDetailFields(vinDetails, hasMultipleStyleId);
    expect(result).toEqual({
      field1: 'value1',
    });
  });
});

describe('Test getValidators helper', () => {
  it('should return original validators for PRODUCTION field when stock number generation is enabled and not in edit mode', () => {
    const validator1 = jest.fn();
    const validator2 = jest.fn();
    const validators = [validator1, validator2];
    const params = {
      validators,
      mandatory: true,
      fieldKey: GENERAL_FIELD_KEYS.PRODUCTION,
      enableStockNumberGenerationOnSave: true, // Toggle ON
      isEditMode: false,
    };

    const result = getValidators(params);

    // Should not add isRequiredRule
    expect(result).toEqual(validators);
  });

  it('should call combineValidators for PRODUCTION field when stock number generation is disabled', () => {
    const customValidator = jest.fn();
    const validators = [customValidator];
    const params = {
      validators,
      mandatory: true,
      fieldKey: GENERAL_FIELD_KEYS.PRODUCTION,
      enableStockNumberGenerationOnSave: false, // Toggle OFF
      isEditMode: false,
    };

    const result = getValidators(params);
    expect(result).toEqual([...validators, isRequiredRule]);
  });

  it('should call combineValidators for PRODUCTION field when in edit mode', () => {
    const customValidator = jest.fn();
    const validators = [customValidator];
    const params = {
      validators,
      mandatory: true,
      fieldKey: GENERAL_FIELD_KEYS.PRODUCTION,
      enableStockNumberGenerationOnSave: true, // Toggle ON
      isEditMode: true,
    };

    const result = getValidators(params);
    expect(result).toEqual([...validators, isRequiredRule]);
  });

  it('should return isDiscountOnMSRPRequiredRule for DISCOUNT_ON_MSRP field when mandatory', () => {
    const customValidator = jest.fn();
    const validators = [customValidator];
    const params = {
      validators,
      mandatory: true,
      fieldKey: PRICING_FIELD_KEYS.DISCOUNT_ON_MSRP,
      enableStockNumberGenerationOnSave: false,
      isEditMode: false,
    };

    const result = getValidators(params);
    expect(result).toEqual([isDiscountOnMSRPRequiredRule]);
  });

  it('should call combineValidators for fields other than PRODUCTION and DISCOUNT_ON_MSRP', () => {
    const customValidator = jest.fn();
    const validators = [customValidator];
    const params = {
      validators,
      mandatory: true,
      fieldKey: 'MAKE',
      enableStockNumberGenerationOnSave: false,
      isEditMode: false,
    };

    const result = getValidators(params);
    expect(result).toEqual([...validators, isRequiredRule]);
  });

  it('should handle empty validators array', () => {
    const params = {
      validators: [],
      mandatory: true,
      fieldKey: 'TRIM',
      enableStockNumberGenerationOnSave: false,
      isEditMode: false,
    };

    const result = getValidators(params);
    expect(result).toEqual([isRequiredRule]);
  });
});

describe('Test combineValidators helper', () => {
  it('should add isRequiredRule to validators when mandatory is true', () => {
    const validator1 = jest.fn();
    const validator2 = jest.fn();
    const validators = [validator1, validator2];
    const mandatory = true;

    const result = combineValidators(validators, mandatory);

    expect(result).toEqual([...validators, isRequiredRule]);
    expect(result.length).toBe(validators.length + 1);
    expect(result[2]).toBe(isRequiredRule);
  });

  it('should return original validators when mandatory is false', () => {
    const validator1 = jest.fn();
    const validator2 = jest.fn();
    const validators = [validator1, validator2];
    const mandatory = false;

    const result = combineValidators(validators, mandatory);

    expect(result).toEqual(validators);
    expect(result.length).toBe(validators.length);
  });

  it('should handle empty validators array when mandatory is true', () => {
    const validators = [];
    const mandatory = true;

    const result = combineValidators(validators, mandatory);

    expect(result).toEqual([isRequiredRule]);
    expect(result.length).toBe(1);
  });

  it('should handle empty validators array when mandatory is false', () => {
    const validators = [];
    const mandatory = false;

    const result = combineValidators(validators, mandatory);

    expect(result).toEqual([]);
    expect(result.length).toBe(0);
  });

  it('should use default empty array when validators is undefined', () => {
    const mandatory = true;

    const result = combineValidators(undefined, mandatory);

    expect(result).toEqual([isRequiredRule]);
    expect(result.length).toBe(1);
  });
});

describe('Test getIsFieldRequired helper', () => {
  it('should return false when field is PRODUCTION and stock number generation is enabled and not in edit mode', () => {
    const params = {
      mandatory: true,
      fieldKey: 'PRODUCTION',
      enableStockNumberGenerationOnSave: true,
      isEditMode: false,
    };
    const result = getIsFieldRequired(params);
    expect(result).toEqual(false);
  });

  it('should return actual mandatory when field is PRODUCTION and stock number generation is disabled', () => {
    const params = {
      mandatory: true,
      fieldKey: 'PRODUCTION',
      enableStockNumberGenerationOnSave: false,
      isEditMode: false,
    };
    const result = getIsFieldRequired(params);
    expect(result).toEqual(params.mandatory);
  });

  it('should return actual mandatory when field is PRODUCTION and in edit mode', () => {
    const params = {
      mandatory: true,
      fieldKey: 'PRODUCTION',
      enableStockNumberGenerationOnSave: true,
      isEditMode: true,
    };
    const result = getIsFieldRequired(params);
    expect(result).toEqual(params.mandatory);
  });

  it('should return actual mandatory when field is other than PRODUCTION ', () => {
    const params = {
      mandatory: true,
      fieldKey: 'MAKE',
      enableStockNumberGenerationOnSave: false,
      isEditMode: false,
    };
    const result = getIsFieldRequired(params);
    expect(result).toEqual(params.mandatory);
  });
});

describe('Test getOverriddenField', () => {
  it('should return a copy of initialFields when overrides is empty', () => {
    const initialFields = {
      VIN: { required: true, disabled: false },
      STOCK_NUMBER: { required: false, options: [] },
    };
    const overrides = {};

    const result = getOverriddenField(initialFields, overrides);

    expect(result).toEqual(initialFields);
    expect(result).not.toBe(initialFields); // Should be a new object
  });

  it('should add new fields from overrides when they do not exist in initialFields', () => {
    const initialFields = {
      VIN: { required: true },
    };
    const overrides = {
      STOCK_NUMBER: { options: ['Option1', 'Option2'] },
      LICENSE_PLATE_NUMBER: { validators: ['required'] },
    };

    const result = getOverriddenField(initialFields, overrides);

    expect(result).toEqual({
      VIN: { required: true },
      STOCK_NUMBER: { options: ['Option1', 'Option2'] },
      LICENSE_PLATE_NUMBER: { validators: ['required'] },
    });
  });

  it('should merge existing fields with overrides when they exist in both objects', () => {
    const initialFields = {
      VIN: { required: true, validators: ['required'] },
      STOCK_NUMBER: { options: ['AUTO-001', 'AUTO-002'] },
    };
    const overrides = {
      VIN: { disabled: true, hidden: false },
      STOCK_NUMBER: { required: true, validators: ['required', 'minLength'] },
    };

    const result = getOverriddenField(initialFields, overrides);

    expect(result).toEqual({
      VIN: {
        required: true,
        validators: ['required'],
        disabled: true,
        hidden: false,
      },
      STOCK_NUMBER: {
        options: ['AUTO-001', 'AUTO-002'],
        required: true,
        validators: ['required', 'minLength'],
      },
    });
  });

  it('should handle mixed scenario with new fields and overridden fields', () => {
    const initialFields = {
      VIN: { required: false },
      STOCK_NUMBER: { options: ['A', 'B'], required: true },
    };
    const overrides = {
      STOCK_NUMBER: { disabled: true, hidden: true },
      LICENSE_PLATE_NUMBER: { validators: ['maxLength'] },
    };

    const result = getOverriddenField(initialFields, overrides);

    expect(result).toEqual({
      VIN: { required: false },
      STOCK_NUMBER: {
        options: ['A', 'B'],
        required: true,
        disabled: true,
        hidden: true,
      },
      LICENSE_PLATE_NUMBER: { validators: ['maxLength'] },
    });
  });

  it('should handle empty initialFields', () => {
    const initialFields = {};
    const overrides = {
      VIN: { required: true, disabled: false },
      STOCK_NUMBER: { options: ['AUTO-001'], validators: ['required'] },
    };

    const result = getOverriddenField(initialFields, overrides);

    expect(result).toEqual({
      VIN: { required: true, disabled: false },
      STOCK_NUMBER: { options: ['AUTO-001'], validators: ['required'] },
    });
  });

  it('should handle complex field configurations with all properties', () => {
    const initialFields = {
      VIN: {
        required: true,
        disabled: false,
        options: [],
        validators: ['required'],
        hidden: false,
      },
    };
    const overrides = {
      VIN: {
        disabled: true,
        options: ['VIN-001'],
        validators: ['required', 'vinFormat'],
        hidden: true,
      },
      LICENSE_PLATE_NUMBER: {
        required: false,
        disabled: false,
        options: ['ABC-123', 'DEF-456', 'GHI-789'],
        validators: ['plateFormat'],
        hidden: false,
      },
    };

    const result = getOverriddenField(initialFields, overrides);

    expect(result).toEqual({
      VIN: {
        required: true,
        disabled: true,
        options: ['VIN-001'],
        validators: ['required', 'vinFormat'],
        hidden: true,
      },
      LICENSE_PLATE_NUMBER: {
        required: false,
        disabled: false,
        options: ['ABC-123', 'DEF-456', 'GHI-789'],
        validators: ['plateFormat'],
        hidden: false,
      },
    });
  });

  it('should not mutate the original initialFields object', () => {
    const initialFields = {
      VIN: { required: true, disabled: false },
    };
    const originalInitialFields = JSON.parse(JSON.stringify(initialFields));
    const overrides = {
      VIN: { disabled: true, hidden: true },
      STOCK_NUMBER: { options: ['AUTO-001'] },
    };

    getOverriddenField(initialFields, overrides);

    expect(initialFields).toEqual(originalInitialFields);
  });
});

describe('Test getSegregatedValidationIssues', () => {
  const mockFields = [
    { id: 'field1', key: 'VIN' },
    { id: 'field2', key: 'STOCK_NUMBER' },
    { id: 'field3', key: 'LICENSE_PLATE_NUMBER' },
  ];

  it('should return empty warnings and original errors when no overrides are provided', () => {
    const validationIssues = {
      field1: 'VIN is required',
      field2: 'Stock number is invalid',
    };
    const overrides = {};

    const result = getSegregatedValidationIssues(validationIssues, overrides, mockFields);

    expect(result).toEqual({
      warnings: {},
      errors: {
        field1: 'VIN is required',
        field2: 'Stock number is invalid',
      },
    });
  });

  it('should segregate errors into warnings when override has WARNING validator type', () => {
    const validationIssues = {
      field1: 'VIN is required',
      field2: 'Stock number is invalid',
      field3: 'License plate format is incorrect',
    };
    const overrides = {
      VIN: { validatorType: 'WARNING' },
      LICENSE_PLATE_NUMBER: { validatorType: 'WARNING' },
    };

    const result = getSegregatedValidationIssues(validationIssues, overrides, mockFields);

    expect(result).toEqual({
      warnings: {
        field1: 'VIN is required',
        field3: 'License plate format is incorrect',
      },
      errors: {
        field2: 'Stock number is invalid',
      },
    });
  });

  it('should keep errors as errors when override has ERROR validator type', () => {
    const validationIssues = {
      field1: 'VIN is required',
      field2: 'Stock number is invalid',
    };
    const overrides = {
      VIN: { validatorType: 'ERROR' },
      STOCK_NUMBER: { validatorType: 'ERROR' },
    };

    const result = getSegregatedValidationIssues(validationIssues, overrides, mockFields);

    expect(result).toEqual({
      warnings: {},
      errors: {
        field1: 'VIN is required',
        field2: 'Stock number is invalid',
      },
    });
  });

  it('should handle mixed validator types correctly', () => {
    const validationIssues = {
      field1: 'VIN is required',
      field2: 'Stock number is invalid',
      field3: 'License plate format is incorrect',
    };
    const overrides = {
      VIN: { validatorType: 'WARNING' },
      STOCK_NUMBER: { validatorType: 'ERROR' },
      LICENSE_PLATE_NUMBER: { validatorType: 'WARNING' },
    };

    const result = getSegregatedValidationIssues(validationIssues, overrides, mockFields);

    expect(result).toEqual({
      warnings: {
        field1: 'VIN is required',
        field3: 'License plate format is incorrect',
      },
      errors: {
        field2: 'Stock number is invalid',
      },
    });
  });

  it('should handle empty validationIssues object', () => {
    const validationIssues = {};
    const overrides = {
      VIN: { validatorType: 'WARNING' },
    };

    const result = getSegregatedValidationIssues(validationIssues, overrides, mockFields);

    expect(result).toEqual({
      warnings: {},
      errors: {},
    });
  });

  it('should handle fields that do not exist in the fields array', () => {
    const validationIssues = {
      field1: 'VIN is required',
      nonExistentField: 'Some error',
    };
    const overrides = {
      VIN: { validatorType: 'WARNING' },
      NON_EXISTENT_KEY: { validatorType: 'WARNING' },
    };

    const result = getSegregatedValidationIssues(validationIssues, overrides, mockFields);

    expect(result).toEqual({
      warnings: {
        field1: 'VIN is required',
      },
      errors: {
        nonExistentField: 'Some error',
      },
    });
  });

  it('should handle overrides without validatorType property', () => {
    const validationIssues = {
      field1: 'VIN is required',
      field2: 'Stock number is invalid',
    };
    const overrides = {
      VIN: { someOtherProperty: 'value' },
      STOCK_NUMBER: { validatorType: 'WARNING' },
    };

    const result = getSegregatedValidationIssues(validationIssues, overrides, mockFields);

    expect(result).toEqual({
      warnings: {
        field2: 'Stock number is invalid',
      },
      errors: {
        field1: 'VIN is required',
      },
    });
  });

  it('should not mutate the original validationIssues object', () => {
    const validationIssues = {
      field1: 'VIN is required',
      field2: 'Stock number is invalid',
    };
    const originalErrors = { ...validationIssues };
    const overrides = {
      VIN: { validatorType: 'WARNING' },
    };

    getSegregatedValidationIssues(validationIssues, overrides, mockFields);

    expect(validationIssues).toEqual(originalErrors);
  });
});
