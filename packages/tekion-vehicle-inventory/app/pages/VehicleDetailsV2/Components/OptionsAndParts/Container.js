import React, { useCallback, useMemo } from 'react';
import { connect } from 'react-redux';
import { compose } from 'recompose';
import { bindActionCreators } from 'redux';
import cx from 'classnames';
import _isEmpty from 'lodash/isEmpty';

import { EMPTY_OBJECT, EMPTY_ARRAY } from 'tbase/app.constants';
import { tget } from 'tbase/utils/general';
import { VEHICLE_TYPES } from 'tbase/constants/vehicleInventory/vehicleTypes';
import MODULE from 'tbase/constants/appServices';
import withActions from 'tcomponents/connectors/withActions';
import VehicleOptionsBuilder from 'twidgets/organisms/vi/OptionsResolver/VehicleOptionsBuilder';
import {
  ADD_BULK_OPTIONS,
  ADD_OPTIONS,
  DELETE_OPTIONS,
  UPDATE_OPTIONS,
} from 'twidgets/organisms/vi/Options/options.actionTypes';
import { getFormattedPricingDetailsWithConfiguredOptions } from 'twidgets/organisms/vi/OptionsConfigV2/helpers/chromeConfigOptions.helper';

import { CUSTOMER_SENSITIVE_FIELDS } from 'constants/constantEnum';
import DealerManager from 'utils/dealerManager';
import { shouldShowCustomerSensitiveInfo } from 'helpers/common';
import { hasPriceSummaryViewAccess } from 'helpers/vehicle.helper';
import { getOptionsBuilderTableTdProps } from 'helpers/vehicleDetails.helper';
import PROGRAM_CONFIG from 'constants/programConfig';
import { hasInventoryAllCostPricesViewPermission } from 'permissions/inventory.permissions';

import { VEHICLE_DETAIL_FORM_STORE_KEY } from '../../VehicleDetails.constant';
import { addVehicleDetailsToVIStore } from '../../VehicleDetails.helpers';
import { updateVehicleObject } from '../../VehicleDetails.actions';
import { addOptions, updateOptions, deleteOptions, addBulkOptions } from './options.actions';
import { refreshOptions } from './options.helpers';
import { ACTION_TYPES, DIO_CHECKBOX_PARAMS } from './options.constants';
import ACTION_HANDLERS from './options.actionHandlers';

import styles from './OptionsAndParts.module.scss';

const Container = ({
  onAction,
  isVehicleConfiguratorEnabled,
  optionsActions,
  isEditMode,
  options,
  enablePriceUpdateForDIO,
  removeDefaultOptions,
  customerViewProps = EMPTY_OBJECT,
  formValues,
  enableUsedVehiclesPricingReset,
  selectedVehicleType,
  showPriceDetailsSummaryDrawer,
  togglePriceDetailsSummaryDrawer,
  isReadOnlyDetails,
  allOptions,
  isConfigureOptionsDisabled,
  productTaxClassificationOptions,
  defaultProductTaxClassification,
  validateTabsForm,
}) => {
  const updateFieldState = useCallback((fields, updatedOptions) => {
    onAction({ type: ACTION_TYPES.ON_UPDATE_FIELD_STATE, payload: { fields, updatedOptions } });
  }, []);

  const onOptionosAndPartsAction = useCallback((type, [payload]) => {
    const { item } = payload || EMPTY_OBJECT;
    switch (type) {
      case UPDATE_OPTIONS:
        optionsActions.updateOptions({ item });
        break;
      case ADD_OPTIONS:
        optionsActions.addOptions({ item });
        break;
      case DELETE_OPTIONS:
        optionsActions.deleteOptions({ item });
        break;
      case ADD_BULK_OPTIONS:
        optionsActions.addBulkOptions(item);
        break;
      default:
        break;
    }
  }, []);

  const {
    styleId,
    chromeSerializedValue,
    pricingDetails,
    pricesManuallyUpdated,
    thirdPartyProviders,
    licensePlateNumber,
    eurotaxModel,
    id,
    vin,
    vehicleKind,
    vehicleType,
  } = formValues;

  const vehicleConfiguratorFormValue = useMemo(
    () => tget(formValues, 'vehicleConfiguratorFormValue', isVehicleConfiguratorEnabled),
    [isVehicleConfiguratorEnabled, formValues]
  );

  const formattedPricingDetails = useMemo(
    () => getFormattedPricingDetailsWithConfiguredOptions(pricingDetails, options),
    [pricingDetails, options]
  );

  const onManualRefresh = async () => {
    const { fieldsToUpdate, newOptions } = await refreshOptions(formValues, options, removeDefaultOptions);
    onOptionosAndPartsAction(ADD_BULK_OPTIONS, [{ item: newOptions }]);
    updateFieldState(fieldsToUpdate, newOptions);
  };

  const showCustomerSensitiveInfo = useMemo(
    () => shouldShowCustomerSensitiveInfo({ fieldKey: CUSTOMER_SENSITIVE_FIELDS.OPTIONS, ...customerViewProps }),
    [customerViewProps]
  );

  const isVehicleUsed = useMemo(
    () => selectedVehicleType === VEHICLE_TYPES.USED && enableUsedVehiclesPricingReset,
    [selectedVehicleType, enableUsedVehiclesPricingReset]
  );

  const shouldShowOptionsAsSelected = useMemo(
    () =>
      PROGRAM_CONFIG.shouldBuildVehicleWithChromeConstruct()
        ? isEditMode || !chromeSerializedValue
        : !_isEmpty(options),
    [chromeSerializedValue, isEditMode]
  );

  const showPriceSummary = useMemo(() => hasPriceSummaryViewAccess(), []);

  const shouldHideInvoicePrice = useMemo(() => !hasInventoryAllCostPricesViewPermission(), []);

  const vehiclePriceUpdateHandler = useCallback(() => validateTabsForm({ shouldSubmit: false }), []);

  return (
    <div className={styles.options}>
      <VehicleOptionsBuilder
        isVehicleConfiguratorEnabled={vehicleConfiguratorFormValue}
        vehicleDetails={{
          options,
          styleId,
          chromeSerializedValue,
          pricingDetails: formattedPricingDetails,
          pricesManuallyUpdated,
          thirdPartyProviders,
          licensePlateNumber,
          eurotaxModel,
          vehicleId: id,
          vin,
          vehicleKind,
          vehicleType,
        }}
        onOptionosAndPartsAction={onOptionosAndPartsAction}
        updateVehicleDetails={updateFieldState}
        containerClass={cx(styles.containerClass, { 'p-b-24': !showPriceSummary })}
        amountContainerClass={styles.amountContainerClass}
        showSelectedOptions={shouldShowOptionsAsSelected}
        tableClassName={styles.tableStyles}
        priceUpdateForDIOEnabled={enablePriceUpdateForDIO?.enable}
        onManualRefresh={onManualRefresh}
        showManualRefresh
        removeDefaultOptions={removeDefaultOptions}
        dioCheckBoxProps={DIO_CHECKBOX_PARAMS}
        showCustomerSensitiveInfo={showCustomerSensitiveInfo}
        showPriceSummary={showPriceSummary}
        isVehicleUsed={isVehicleUsed}
        showPriceDetailsSummaryDrawer={showPriceDetailsSummaryDrawer}
        togglePriceDetailsSummaryDrawer={togglePriceDetailsSummaryDrawer}
        isReadOnlyDetails={isReadOnlyDetails}
        allOptions={allOptions}
        isConfigureOptionsDisabled={isConfigureOptionsDisabled}
        productTaxClassificationOptions={productTaxClassificationOptions}
        defaultProductTaxClassification={defaultProductTaxClassification}
        onVehiclePriceUpdate={vehiclePriceUpdateHandler}
        moduleType={MODULE.VI}
        getTdProps={getOptionsBuilderTableTdProps}
        shouldHideInvoicePrice={shouldHideInvoicePrice}
        dealerId={DealerManager.getDealerId()}
      />
    </div>
  );
};

const mapStateToProps = ({ vi: state }) => ({
  options: tget(state, 'vehicleDetails.optionParts.options', EMPTY_ARRAY),
  formValues: tget(state, ['vehicleDetails', VEHICLE_DETAIL_FORM_STORE_KEY], EMPTY_OBJECT),
  allOptions: tget(state, 'vehicleDetails.allOptions', EMPTY_ARRAY),
});

const mapDispatchToProps = dispatch => ({
  optionsActions: bindActionCreators(
    { addOptions, updateOptions, deleteOptions, addBulkOptions, updateVehicleObject },
    dispatch
  ),
});

export default compose(
  connect(addVehicleDetailsToVIStore(mapStateToProps), mapDispatchToProps),
  withActions(EMPTY_OBJECT, ACTION_HANDLERS),
  React.memo
)(Container);
