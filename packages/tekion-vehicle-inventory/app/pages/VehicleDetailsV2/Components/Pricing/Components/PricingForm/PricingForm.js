import React, { PureComponent } from 'react';
import { defaultMemoize } from 'reselect';
import PropTypes from 'prop-types';
import _get from 'lodash/get';
import _includes from 'lodash/includes';
import _isEqual from 'lodash/isEqual';
import _noop from 'lodash/noop';
import _reduce from 'lodash/reduce';
import _isNil from 'lodash/isNil';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from 'tbase/app.constants';
import { tget } from '@tekion/tekion-base/utils/general';
import { isRRG } from 'tbase/utils/dealerProgramUtils';
import { VEHICLE_STATUS } from 'tbase/constants/vehicleInventory/vehicleStatus';
import VehicleReader from 'tbusiness/appServices/vehicleInventory/readers/vehicle';
import dealerPropertyHelper from 'tcomponents/helpers/sales/dealerPropertyHelper';
import Info from 'tcomponents/molecules/Info';
import { FormWithSubmission } from 'tcomponents/pages/formPage';
import { PRICING_FIELD_KEYS, PRICING_FORM_FIELDS_V2_CONFIG } from 'twidgets/appServices/sales/config/viFormFields';

import { ASSET_SUB_TYPES } from 'constants/settings';
import { appendOptionsToFields } from 'constants/utils';
import { hasDetailedPricingEdit, hasVehicleEditDates } from 'permissions/inventory.permissions';
import Discount from 'molecules/formFields/Discount';

import { getFormFields } from './PricingForm.formConfig';
import { getValues } from './PricingForm.helpers';
import { ACTION_TYPES } from './pricingForm.constants';
import DescriptionSwitch from './Components/DescriptionSwitch';
import MarketPriceLabel from './Components/MarketPriceLabel';
import { FORM_CONTEXT_ID } from '../../../../VehicleDetails.constant';
import { getFormSectionWithSectionData } from '../../../../VehicleDetails.sections';
import {
  customFieldReader,
  formFieldReader,
  getOverriddenField,
  getProductTaxClassificationOptions,
  getSegregatedValidationIssues,
} from '../../../../VehicleDetails.helpers';
import { CustomFieldsComponent } from '../../../../VehicleDetails.customFormConfig';

class PricingForm extends PureComponent {
  // Memoized method to avoid recomputation of validation issues on every render
  getSegregatedValidationIssuesMemoized = defaultMemoize(getSegregatedValidationIssues);

  componentDidMount() {
    this.init();
  }

  componentDidUpdate(prevProps) {
    const {
      fields,
      shouldDisableInvoiceDate,
      associatedGlAccounts,
      defaultGlAccount,
      disablePricingEdits,
      customerViewProps,
      vehicleStockType,
    } = this.props;
    if (
      !_isEqual(prevProps.fields, fields) ||
      !_isEqual(prevProps.shouldDisableInvoiceDate, shouldDisableInvoiceDate) ||
      !_isEqual(prevProps.associatedGlAccounts, associatedGlAccounts) ||
      !_isEqual(prevProps.defaultGlAccount, defaultGlAccount) ||
      !_isEqual(prevProps.disablePricingEdits, disablePricingEdits) ||
      !_isEqual(prevProps.customerViewProps, customerViewProps) ||
      !_isEqual(prevProps.vehicleStockType, vehicleStockType)
    ) {
      this.init();
    }
  }

  prepareGlAccountOption = data => {
    const { getGLAccounts } = this.props;
    return getGLAccounts(data, 'id');
  };

  getTargetGlAccountNumber = () => {
    const { defaultGlAccount } = this.props;
    return _get(defaultGlAccount, 'id');
  };

  handleFetchMarketPrice = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.FETCH_MARKET_PRICE,
    });
  };

  init = () => {
    const {
      fields,
      associatedGlAccounts,
      disablePricingEdits,
      onAction,
      metaData,
      formState,
      vehicleStockType,
      syndicationMandatoryFieldsSetup,
      vehicleID,
      vehicleStatus,
      formFieldOverrides,
      allFieldsDisabledByDefault,
    } = this.props;

    const shouldOverrideDisable = dealerPropertyHelper.isViArcLiteEnabled() || !hasDetailedPricingEdit();

    const overriddenFields = getOverriddenField(PRICING_FORM_FIELDS_V2_CONFIG, formFieldOverrides);

    const pricingFields = _reduce(
      fields,
      (acc, field) => {
        const priceField = {
          ...field,
          ...tget(overriddenFields, field?.key, EMPTY_OBJECT),
        };

        switch (field.key) {
          case PRICING_FIELD_KEYS.PRODUCT_TAX_CLASSIFICATION:
            priceField.options = getProductTaxClassificationOptions(
              tget(metaData, 'taxDetailsMetadata.taxClassificationDetails', EMPTY_ARRAY)
            );
            break;

          case PRICING_FIELD_KEYS.GL_BALANCE:
            priceField.disabled = true;
            break;

          case PRICING_FIELD_KEYS.INVOICE_DATE:
            // do not remove might be used in future
            // if (shouldDisableInvoiceDate) {
            //   priceField.disabled = shouldDisableInvoiceDate;
            // }
            priceField.disabled = !hasVehicleEditDates();
            break;

          case PRICING_FIELD_KEYS.MSRP:
          case PRICING_FIELD_KEYS.INVOICE_PRICE:
            {
              const status = VehicleReader.status(formState);
              if (_includes([VEHICLE_STATUS.STOCKED_IN], status)) {
                priceField.disabled = disablePricingEdits;
              }
              const { adjustedMsrp, adjustedInvoicePrice } = tget(formState, 'pricingDetails', EMPTY_OBJECT);
              if (
                (field?.key === PRICING_FIELD_KEYS.MSRP && Math.abs(adjustedMsrp) > 0) ||
                (field?.key === PRICING_FIELD_KEYS.INVOICE_PRICE && Math.abs(adjustedInvoicePrice) > 0)
              ) {
                priceField.suffix = (
                  <Info helpText={__('Adjusted Price')} tooltipClass="align-center p-12" className="m-r-12" />
                );
              }
            }
            break;

          case PRICING_FIELD_KEYS.MARKET_PRICE:
            if (isRRG() && !_isNil(vehicleID)) {
              priceField.label = (
                <MarketPriceLabel priceField={priceField} handleFetchMarketPrice={this.handleFetchMarketPrice} />
              );
            }
            break;

          case PRICING_FIELD_KEYS.DISCOUNT_ON_MSRP:
            priceField.component = Discount;
            break;
          default:
            break;
        }

        if (shouldOverrideDisable) {
          priceField.disabled = true;
          priceField.isDisabled = true;
        }

        if (priceField.assetSubType === ASSET_SUB_TYPES.CUSTOM_FIELDS_PRICING) {
          priceField.component = CustomFieldsComponent({ type: field.fieldType });
          const processedField = customFieldReader(priceField, field, vehicleStatus, allFieldsDisabledByDefault);
          return [...acc, processedField];
        }

        const formField = formFieldReader({
          fields: priceField,
          field,
          vehicleStockType,
          syndicationMandatoryFieldsSetup,
          vehicleStatus,
          formFieldOverrides,
          allFieldsDisabledByDefault,
        });

        if (_isNil(formField)) return acc;

        if (field.key === PRICING_FIELD_KEYS.GL_ACCOUNT) {
          const processedField = appendOptionsToFields(
            formField,
            this.prepareGlAccountOption(associatedGlAccounts),
            this.getTargetGlAccountNumber()
          );
          return [...acc, processedField];
        }

        if (isRRG() && field.key === PRICING_FIELD_KEYS.MARKET_PRICE && !_isNil(vehicleID)) {
          const processedField = { ...formField, required: false };
          return [...acc, processedField];
        }

        return [...acc, formField];
      },
      []
    );

    onAction({
      type: ACTION_TYPES.INIT_FORM_FIELDS,
      payload: {
        formFields: getFormFields({ fields: pricingFields }),
      },
    });
  };

  render() {
    const {
      vehicleID,
      sectionDetails,
      formState,
      fields,
      onAction,
      errors,
      formFieldsConfig,
      associatedGlAccounts,
      customerViewProps,
      formFieldOverrides,
    } = this.props;

    const sections = getFormSectionWithSectionData({ sectionDetails, formFieldsConfig, customerViewProps });
    const values = getValues(formState, fields, formFieldsConfig, this.getTargetGlAccountNumber, associatedGlAccounts);

    const { warnings, errors: formErrors } = this.getSegregatedValidationIssuesMemoized(
      errors,
      formFieldOverrides,
      fields
    );

    return (
      <React.Fragment>
        <DescriptionSwitch vehicleID={vehicleID} />
        <FormWithSubmission
          contextId={FORM_CONTEXT_ID.PRICING_FORM_CONTEXT_ID}
          values={values}
          fields={formFieldsConfig}
          sections={sections}
          onAction={onAction}
          errors={formErrors}
          warnings={warnings}
          className="full-width"
          shouldRemoveErrorOnSubmission
        />
      </React.Fragment>
    );
  }
}

PricingForm.propTypes = {
  associatedGlAccounts: PropTypes.array,
  customerViewProps: PropTypes.object,
  defaultGlAccount: PropTypes.object,
  disablePricingEdits: PropTypes.bool.isRequired,
  errors: PropTypes.object,
  fields: PropTypes.array,
  formFieldsConfig: PropTypes.object,
  formState: PropTypes.object,
  getGLAccounts: PropTypes.func,
  metaData: PropTypes.object,
  onAction: PropTypes.func.isRequired,
  sectionDetails: PropTypes.array,
  shouldDisableInvoiceDate: PropTypes.bool,
  vehicleID: PropTypes.string.isRequired,
  vehicleStockType: PropTypes.string,
  syndicationMandatoryFieldsSetup: PropTypes.object,
  vehicleStatus: PropTypes.string,
  formFieldOverrides: PropTypes.object,
  allFieldsDisabledByDefault: PropTypes.bool,
};

PricingForm.defaultProps = {
  associatedGlAccounts: EMPTY_ARRAY,
  customerViewProps: EMPTY_OBJECT,
  defaultGlAccount: EMPTY_OBJECT,
  errors: EMPTY_OBJECT,
  fields: EMPTY_ARRAY,
  formFieldsConfig: EMPTY_OBJECT,
  formState: EMPTY_OBJECT,
  getGLAccounts: _noop,
  metaData: EMPTY_OBJECT,
  sectionDetails: EMPTY_ARRAY,
  shouldDisableInvoiceDate: false,
  vehicleStockType: EMPTY_STRING,
  syndicationMandatoryFieldsSetup: EMPTY_OBJECT,
  vehicleStatus: EMPTY_STRING,
  formFieldOverrides: EMPTY_OBJECT,
  allFieldsDisabledByDefault: false,
};

export default PricingForm;
