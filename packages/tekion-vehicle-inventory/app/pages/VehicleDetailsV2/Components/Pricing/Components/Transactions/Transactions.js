import React, { Component } from 'react';
import Proptypes from 'prop-types';
import _noop from 'lodash/noop';
import _size from 'lodash/size';
import _map from 'lodash/map';
import _get from 'lodash/get';
import _partial from 'lodash/partial';

import { EMPTY_ARRAY, EMPTY_STRING } from 'tbase/app.constants';
import Button from 'tcomponents/atoms/Button';
import Collapse from 'tcomponents/molecules/Collapse';
import BaseTable from 'tcomponents/molecules/table/BaseTable';
import { withTekionConversion } from '@tekion/tekion-conversion-web';

import { TABLE_CONFIG } from 'appConfig';
import { NO_ROWS } from 'constants/constants';
import { getJournalEntriesRoute } from 'helpers/route';
import { redirect } from 'utils';

import { PREDEFINED_SECTIONS, PREDEFINED_SECTIONS_KEYS } from '../../../../VehicleDetails.constant';
import { transactionTableColumnConfig } from './transaction.columns';

import styles from '../../pricing.module.scss';

const renderCollapse = (subTitle, transactionListData = [], columns) => {
  if (transactionListData.length) {
    return (
      <div className="full-width">
        <BaseTable
          showPagination={false}
          columns={columns}
          data={transactionListData}
          minRows={TABLE_CONFIG.MIN_ROWS}
          onRowClick={_noop}
          pageSize={_size(transactionListData)}
          rowHeight={TABLE_CONFIG.ROW_HEIGHT}
        />
      </div>
    );
  }
  return <div>{NO_ROWS}</div>;
};

class Transactions extends Component {
  redirectToJournalEntry = (transactionId, dealerId) => {
    const redirectRoute = getJournalEntriesRoute(transactionId, dealerId);
    redirect(redirectRoute);
  };

  render() {
    const { transactionListData, subTitle, getGLAccount, getFormattedDateAndTime } = this.props;
    const TRANSACTION_TABLE_COLUMNS = transactionTableColumnConfig(getFormattedDateAndTime);
    const columns = _map(TRANSACTION_TABLE_COLUMNS, column => {
      switch (column.accessor) {
        case 'glAccountId':
          return {
            ...column,
            Cell: ({ value }) => {
              const { data } = getGLAccount(value);
              return data ? `${_get(data, 'accountNumber')} -  ${_get(data, 'accountName')}` : value;
            },
          };
        case 'account':
          return {
            ...column,
            Cell: data => {
              const { original } = data;
              const { transactionNumber, transactionId, dealerId } = original;
              return (
                <Button view="link" onClick={_partial(this.redirectToJournalEntry, transactionId, dealerId)}>
                  {transactionNumber}
                </Button>
              );
            },
          };

        default:
          return column;
      }
    });
    return (
      <div className={styles.transactionContainer}>
        <Collapse
          panels={[
            {
              header: PREDEFINED_SECTIONS[PREDEFINED_SECTIONS_KEYS.TRANSACTIONS]?.title,
              body: renderCollapse(subTitle, transactionListData, columns),
              key: 1,
            },
          ]}
        />
      </div>
    );
  }
}

Transactions.propTypes = {
  transactionListData: Proptypes.array,
  subTitle: Proptypes.string,
  getFormattedDateAndTime: Proptypes.func.isRequired,
};

Transactions.defaultProps = {
  transactionListData: EMPTY_ARRAY,
  subTitle: EMPTY_STRING,
};

export default withTekionConversion(Transactions);
