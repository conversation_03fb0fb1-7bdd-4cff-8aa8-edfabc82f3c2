import React, { useEffect, useMemo, useCallback } from 'react';
import cx from 'classnames';
import PropTypes from 'prop-types';
import _find from 'lodash/find';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _noop from 'lodash/noop';
import _result from 'lodash/result';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from 'tbase/app.constants';
import { Heading } from 'tcomponents/atoms';
import PropertyControlledComponent from 'tcomponents/molecules/PropertyControlledComponent';

import PROGRAM_CONFIG from 'constants/programConfig';
import { isRVDealerEnabled } from 'helpers/vehicle.helper';
import Card from 'molecules/Card';
import Discounts from 'molecules/Discounts';
import Offers from 'molecules/Offers';

import SideNavigation from '../SideNavigation';
import { PREDEFINED_SECTIONS, PREDEFINED_SECTIONS_KEYS } from '../../VehicleDetails.constant';
import AdditionalBodyParts from './Components/AdditionalBodyParts';
import AdditionalCost from './Components/AdditionalCost/AdditionalCost';
import CostAndMarginSummary from './Components/CostAndMarginSummary';
import GLAccount from './Components/GLAccount';
import PricingForm from './Components/PricingForm';
import RightPanel from './Components/RightPanel';
import { transactionMockTitle } from './Components/Transactions/MockData';
import Transactions from './Components/Transactions/Transactions';
import { ACTION_TYPES, PRICING_SECTION_ID } from './pricing.constants';
import {
  getCustomSections,
  getIsVehicleValuationVisible,
  getPricingSectionContainer,
  getSectionViewConfig,
  shouldDisableInvoiceDate,
} from './pricing.helpers';

import styles from './pricing.module.scss';

const Pricing = props => {
  const {
    actions,
    additionalCosts,
    allAdditionalCosts,
    allGlAccount,
    associatedGlAccounts,
    autocheckReportEnabled,
    bodyTypeMapping,
    centralisedEnabledApps,
    costSummary,
    createSingleJobRO,
    customerViewProps,
    dealerMasterData,
    defaultGlAccount,
    disablePricingEdits,
    discounts,
    displayModelSource,
    fetchCostSummary,
    fields,
    fieldsWithError,
    form,
    getGlAccountById,
    getGlAccountByNumber,
    glAccountList,
    invoices,
    isEditMode,
    offers,
    onAction,
    onValidatedCb,
    priceUpdated,
    pricing,
    providerDetails,
    sections,
    transactionList,
    valuationDetails,
    vehicleDetails,
    vehicleID,
    vehicleTypes,
    viSettings,
    syndicationMandatoryFieldsSetup,
    fetchAllHardPacksList,
    pricingFieldValues,
    rawVehicleDetails,
    vendorInvoiceData,
    formState,
    isReadOnlyDetails,
    lite,
    optionsInStore,
    removeDefaultOptions,
    formFieldOverrides,
    allFieldsDisabledByDefault,
  } = props;

  const {
    deleteAdditionalBodyPart,
    updateAdditionalBodyPart,
    setAdditionalBodyParts,
    addAdditionalCost,
    clearAdditionalCost,
    deleteAdditionalCost,
    updateAdditionalCost,
    getAdditionalCostsForVehicle,
    fetchAllHardPacks,
  } = actions || EMPTY_OBJECT;

  const { additionalBodyParts: additionalBodyPartsList } = pricing || EMPTY_OBJECT;

  useEffect(() => {
    onAction({ type: ACTION_TYPES.ON_INIT });
  }, [rawVehicleDetails]);

  useEffect(() => {
    if (!priceUpdated && !_isEmpty(invoices)) {
      onAction({ type: ACTION_TYPES.ON_UPDATE_PRICING_FIELDS });
    }
  }, [onAction, priceUpdated, invoices]);

  const getGLAccount = useCallback(
    id => _find(glAccountList, ({ value }) => value === id) || EMPTY_OBJECT,
    [glAccountList]
  );

  const onDeleteMiscellaneousCost = useCallback(
    ({ data = EMPTY_OBJECT, onSuccessCb }) => {
      onAction({
        type: ACTION_TYPES.ON_DELETE_MISCELLANEOUSCOST,
        payload: { data, onSuccessCb },
      });
    },
    [onAction]
  );

  const renderSectionTitle = useCallback(
    sectionKey => <Heading size={2}>{_result(PREDEFINED_SECTIONS, [sectionKey, 'title'])}</Heading>,
    []
  );

  const sectionViewConfig = useMemo(() => getSectionViewConfig(customerViewProps), [customerViewProps]);

  const disableInvoiceDate = useMemo(() => shouldDisableInvoiceDate(invoices), [invoices]);

  const isVehicleValuationVisible = useMemo(
    () =>
      getIsVehicleValuationVisible({
        vehicleDetails: _get(form.getState(), 'values'),
        providerDetails,
      }),
    [form, providerDetails]
  );

  const customSections = useMemo(
    () =>
      lite
        ? EMPTY_ARRAY
        : getCustomSections({
            sectionViewConfig,
            isVehicleValuationVisible,
            autocheckReportEnabled,
          }),
    [sectionViewConfig, isVehicleValuationVisible, autocheckReportEnabled, lite]
  );

  return (
    <div className="d-flex full-height">
      <SideNavigation
        systemSections={sections}
        customSectionConfig={customSections}
        fieldsWithError={fieldsWithError}
        getContainer={getPricingSectionContainer}
        offsetTop={20}
      />

      <div
        id={PRICING_SECTION_ID}
        className={cx('overflow-y-auto full-height', styles.rightBodyContainer, {
          [styles.readOnlyContainer]: isReadOnlyDetails,
        })}>
        <div className={`p-24 ${styles.pricingSectionsContainer}`}>
          <PropertyControlledComponent controllerProperty={PROGRAM_CONFIG.shouldShowCostTable() && !lite}>
            <Card
              id={PREDEFINED_SECTIONS_KEYS.MARGIN_SUMMARY}
              title={renderSectionTitle(PREDEFINED_SECTIONS_KEYS.MARGIN_SUMMARY)}>
              <CostAndMarginSummary
                isEditMode={isEditMode}
                costSummary={costSummary}
                additionalCosts={additionalCosts}
                vehicleID={vehicleID}
                fetchCostSummary={fetchCostSummary}
                onDelete={onDeleteMiscellaneousCost}
                viSettings={viSettings}
                vehicleDetails={vehicleDetails}
                createSingleJobRO={createSingleJobRO}
                displayModelSource={displayModelSource}
                fetchAllHardPacksList={fetchAllHardPacksList}
                actions={actions}
                fields={fields}
                pricingFieldsValues={pricingFieldValues}
              />
            </Card>
            <Card
              id={PREDEFINED_SECTIONS_KEYS.GL_ACCOUNT}
              title={renderSectionTitle(PREDEFINED_SECTIONS_KEYS.GL_ACCOUNT)}>
              <GLAccount costSummary={costSummary} allGlAccount={allGlAccount} vehicleDetails={vehicleDetails} />
            </Card>
          </PropertyControlledComponent>

          <PricingForm
            isEditMode={isEditMode}
            disablePricingEdits={disablePricingEdits}
            form={form}
            fields={fields}
            shouldDisableInvoiceDate={disableInvoiceDate}
            glAccountList={glAccountList}
            defaultGlAccount={defaultGlAccount}
            associatedGlAccounts={associatedGlAccounts}
            dealerMasterData={dealerMasterData}
            vehicleID={vehicleID}
            viSettings={viSettings}
            customerViewProps={customerViewProps}
            sectionDetails={sections}
            onValidatedCb={onValidatedCb}
            vehicleStockType={formState?.vehicleType}
            syndicationMandatoryFieldsSetup={syndicationMandatoryFieldsSetup}
            vendorInvoiceData={vendorInvoiceData}
            rawVehicleDetails={rawVehicleDetails}
            vehicleStatus={formState?.status}
            optionsInStore={optionsInStore}
            removeDefaultOptions={removeDefaultOptions}
            formFieldOverrides={formFieldOverrides}
            allFieldsDisabledByDefault={allFieldsDisabledByDefault}
          />

          <PropertyControlledComponent controllerProperty={!lite}>
            <PropertyControlledComponent controllerProperty={_get(sectionViewConfig, 'discounts')}>
              <Card
                id={PREDEFINED_SECTIONS_KEYS.DISCOUNTS}
                title={renderSectionTitle(PREDEFINED_SECTIONS_KEYS.DISCOUNTS)}>
                <Discounts discounts={discounts} />
              </Card>
            </PropertyControlledComponent>

            <PropertyControlledComponent controllerProperty={_get(sectionViewConfig, 'offers')}>
              <Card id={PREDEFINED_SECTIONS_KEYS.OFFERS} title={renderSectionTitle(PREDEFINED_SECTIONS_KEYS.OFFERS)}>
                <Offers offers={offers} />
              </Card>
            </PropertyControlledComponent>

            <PropertyControlledComponent controllerProperty={_get(sectionViewConfig, 'additionalBody')}>
              <Card
                id={PREDEFINED_SECTIONS_KEYS.ADDITIONAL_BODY}
                title={renderSectionTitle(PREDEFINED_SECTIONS_KEYS.ADDITIONAL_BODY)}>
                <AdditionalBodyParts
                  deleteAdditionalBodyPart={deleteAdditionalBodyPart}
                  updateAdditionalBodyPart={updateAdditionalBodyPart}
                  additionalBodyPartsList={additionalBodyPartsList}
                  setAdditionalBodyParts={setAdditionalBodyParts}
                  vehicleStatus={formState?.status}
                />
              </Card>
            </PropertyControlledComponent>

            <PropertyControlledComponent
              controllerProperty={_get(sectionViewConfig, 'hardPack') && !PROGRAM_CONFIG.shouldShowCostTable()}>
              <Card
                id={PREDEFINED_SECTIONS_KEYS.HARD_PACK}
                title={renderSectionTitle(PREDEFINED_SECTIONS_KEYS.HARD_PACK)}>
                <AdditionalCost
                  allAdditionalCosts={allAdditionalCosts}
                  additionalCostList={additionalCosts || EMPTY_ARRAY}
                  addAdditionalCost={addAdditionalCost}
                  clearAdditionalCost={clearAdditionalCost}
                  fetchAllHardPacks={fetchAllHardPacks}
                  deleteAdditionalCost={deleteAdditionalCost}
                  form={form}
                  updateAdditionalCost={updateAdditionalCost}
                  allGlAccount={allGlAccount}
                  fetchAdditionalCost={getAdditionalCostsForVehicle}
                  bodyTypeMapping={bodyTypeMapping}
                  getGlAccountById={getGlAccountById}
                  getGlAccountByNumber={getGlAccountByNumber}
                  vehicleTypes={vehicleTypes}
                  displayModelSource={displayModelSource}
                />
              </Card>
            </PropertyControlledComponent>

            <PropertyControlledComponent controllerProperty={_get(sectionViewConfig, 'transactions')}>
              <Card id={PREDEFINED_SECTIONS_KEYS.TRANSACTIONS}>
                <Transactions
                  subTitle={transactionMockTitle}
                  transactionListData={transactionList}
                  getGLAccount={getGLAccount}
                  glAccountList={glAccountList}
                  centralisedEnabledApps={centralisedEnabledApps}
                />
              </Card>
            </PropertyControlledComponent>

            <PropertyControlledComponent
              controllerProperty={!isRVDealerEnabled() && (isVehicleValuationVisible || autocheckReportEnabled)}>
              <Card id={PREDEFINED_SECTIONS_KEYS.REPORTS}>
                <RightPanel
                  form={form}
                  providerDetails={providerDetails}
                  isVehicleValuationVisible={isVehicleValuationVisible}
                  autocheckReportEnabled={autocheckReportEnabled}
                  valuationDetails={valuationDetails}
                />
              </Card>
            </PropertyControlledComponent>
          </PropertyControlledComponent>
        </div>
      </div>
    </div>
  );
};

Pricing.propTypes = {
  actions: PropTypes.object,
  additionalCosts: PropTypes.array,
  allAdditionalCosts: PropTypes.array,
  allGlAccount: PropTypes.object,
  associatedGlAccounts: PropTypes.array,
  autocheckReportEnabled: PropTypes.bool,
  bodyTypeMapping: PropTypes.array,
  centralisedEnabledApps: PropTypes.array,
  costSummary: PropTypes.object,
  createSingleJobRO: PropTypes.func,
  customerViewProps: PropTypes.object,
  dealerMasterData: PropTypes.object,
  defaultGlAccount: PropTypes.object,
  disablePricingEdits: PropTypes.bool,
  discounts: PropTypes.array,
  displayModelSource: PropTypes.string,
  fetchCostSummary: PropTypes.func,
  fields: PropTypes.array.isRequired,
  fieldsWithError: PropTypes.array,
  form: PropTypes.object,
  getGlAccountById: PropTypes.func,
  getGlAccountByNumber: PropTypes.func,
  glAccountList: PropTypes.array,
  invoices: PropTypes.array,
  isEditMode: PropTypes.bool.isRequired,
  offers: PropTypes.array,
  onAction: PropTypes.func.isRequired,
  onValidatedCb: PropTypes.func,
  priceUpdated: PropTypes.bool,
  pricing: PropTypes.object,
  providerDetails: PropTypes.array,
  sections: PropTypes.array,
  transactionList: PropTypes.array,
  valuationDetails: PropTypes.object,
  vehicleDetails: PropTypes.object,
  vehicleID: PropTypes.string.isRequired,
  vehicleTypes: PropTypes.array,
  viSettings: PropTypes.object,
  syndicationMandatoryFieldsSetup: PropTypes.object,
  fetchAllHardPacksList: PropTypes.func,
  pricingFieldValues: PropTypes.object,
  rawVehicleDetails: PropTypes.object,
  vendorInvoiceData: PropTypes.array,
  formState: PropTypes.object,
  isReadOnlyDetails: PropTypes.bool,
  optionsInStore: PropTypes.array,
  removeDefaultOptions: PropTypes.bool,
  formFieldOverrides: PropTypes.object,
  allFieldsDisabledByDefault: PropTypes.bool,
};

Pricing.defaultProps = {
  actions: EMPTY_OBJECT,
  additionalCosts: EMPTY_ARRAY,
  allAdditionalCosts: EMPTY_ARRAY,
  allGlAccount: EMPTY_OBJECT,
  associatedGlAccounts: EMPTY_ARRAY,
  autocheckReportEnabled: false,
  bodyTypeMapping: EMPTY_ARRAY,
  centralisedEnabledApps: EMPTY_ARRAY,
  costSummary: EMPTY_OBJECT,
  createSingleJobRO: _noop,
  customerViewProps: EMPTY_OBJECT,
  dealerMasterData: EMPTY_OBJECT,
  defaultGlAccount: EMPTY_OBJECT,
  disablePricingEdits: false,
  discounts: EMPTY_ARRAY,
  displayModelSource: EMPTY_STRING,
  fetchCostSummary: _noop,
  fieldsWithError: EMPTY_ARRAY,
  form: EMPTY_OBJECT,
  getGlAccountById: _noop,
  getGlAccountByNumber: _noop,
  glAccountList: EMPTY_ARRAY,
  invoices: EMPTY_ARRAY,
  offers: EMPTY_ARRAY,
  onValidatedCb: _noop,
  priceUpdated: false,
  pricing: EMPTY_OBJECT,
  providerDetails: EMPTY_ARRAY,
  sections: EMPTY_ARRAY,
  transactionList: EMPTY_ARRAY,
  valuationDetails: EMPTY_OBJECT,
  vehicleDetails: EMPTY_OBJECT,
  vehicleTypes: EMPTY_ARRAY,
  viSettings: EMPTY_OBJECT,
  syndicationMandatoryFieldsSetup: EMPTY_OBJECT,
  fetchAllHardPacksList: _noop,
  pricingFieldValues: EMPTY_OBJECT,
  rawVehicleDetails: EMPTY_OBJECT,
  vendorInvoiceData: EMPTY_ARRAY,
  formState: EMPTY_OBJECT,
  isReadOnlyDetails: false,
  optionsInStore: EMPTY_ARRAY,
  removeDefaultOptions: false,
  formFieldOverrides: EMPTY_OBJECT,
  allFieldsDisabledByDefault: false,
};

export default Pricing;
