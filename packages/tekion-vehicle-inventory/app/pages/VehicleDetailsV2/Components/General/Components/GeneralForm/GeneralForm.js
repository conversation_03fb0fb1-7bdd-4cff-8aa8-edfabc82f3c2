/* eslint-disable no-case-declarations */
import React, { PureComponent } from 'react';
import classnames from 'classnames';
import PropTypes from 'prop-types';
import _castArray from 'lodash/castArray';
import _concat from 'lodash/concat';
import _get from 'lodash/get';
import _head from 'lodash/head';
import _includes from 'lodash/includes';
import _isEmpty from 'lodash/isEmpty';
import _isEqual from 'lodash/isEqual';
import _map from 'lodash/map';
import _noop from 'lodash/noop';
import _find from 'lodash/find';
import _filter from 'lodash/filter';
import _size from 'lodash/size';
import _range from 'lodash/range';
import _split from 'lodash/split';
import _partial from 'lodash/partial';
import _result from 'lodash/result';
import _set from 'lodash/set';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING, NO_DATA } from 'tbase/app.constants';
import { MILEAGE_STATUS } from 'tbase/constants/vehicleInventory/vehicle';
import {
  BODY_CLASS_ENUM_LABEL_MAP,
  ORIGINAL_TYPE_CODE_ENUM_LABEL_MAP,
  SALE_TYPE_ENUM_LABEL_MAP,
  SEAT_MATERIAL_ENUM_LABEL_MAP,
} from 'tbase/constants/vehicleInventory/fieldOptions';
import { capitalizeFirstLetterOnly } from 'tbase/formatters/string';
import { getDaysOnLot, getDaysTosell, getVehicleModel } from 'tbase/helpers/vehicle.helper';
import { getCurrentYearValue } from 'tbase/utils/dateUtils';
import { nonZeroRule } from 'tbase/utils/formValidators';
import { tget } from 'tbase/utils/general';
import { getYearOptions, getSelectOptions } from 'tbase/utils/sales';
import { getCapacityLabel, getEmissionLabel } from '@tekion/tekion-base/utils/vi/localizationUtils';
import { VEHICLE_TYPES } from 'tbase/constants/vehicleInventory/vehicleTypes';
import { isRRG } from 'tbase/utils/sales/dealerProgram.utils';
import MODULE from 'tbase/constants/appServices';
import VehicleReader from 'tbusiness/appServices/vehicleInventory/readers/vehicle';
import { MILEAGE_TYPES } from 'tbusiness/appServices/vehicleInventory/constants/vehicle';
import Content from 'tcomponents/atoms/Content';
import FontIcon, { SIZES } from 'tcomponents/atoms/FontIcon';
import Label from 'tcomponents/atoms/Label';
import Tooltip, { TOOLTIP_PLACEMENT } from 'tcomponents/atoms/tooltip';
import IconAsBtn from 'tcomponents/atoms/iconAsBtn';
import dealerPropertyHelper from 'tcomponents/helpers/sales/dealerPropertyHelper';
import PropertyControlledComponent from 'tcomponents/molecules/PropertyControlledComponent';
import ConfirmationDialog from 'tcomponents/molecules/confirmationDialog';
import Popover, { POPOVER_TRIGGER } from 'tcomponents/molecules/popover';
import { CreatableSelect } from 'tcomponents/organisms/FormBuilder/fieldRenderers';
import { FormWithSubmission } from 'tcomponents/pages/formPage';
import ChromeApi from 'twidgets/organisms/vi/OptionsResolver/chromeConfigOptions.api';
import VinDecodeModal from 'twidgets/appServices/sales/organisms/VinDecodeModal';
import { GENERAL_FIELD_KEYS, GENERAL_FORM_FIELDS_V2_CONFIG } from 'twidgets/appServices/sales/config/viFormFields';
import { getLabelFromKey } from 'twidgets/appServices/sales/helpers/viFormFields';
import { FIELD_TYPES_KEY } from 'twidgets/constants/vi.constants';
import { withTekionConversion } from '@tekion/tekion-conversion-web';
import ModelSelectRenderer, { ASYNC_MODEL_RENDERER_TYPE } from 'twidgets/organisms/vi/ModelSelectRenderer';
import SelectTrimModal from 'twidgets/appServices/vi/organisms/SelectTrimModal';

import lengthLabelUtil from 'utils/lengthLabelUtil';
import mileageLabelUtil from 'utils/mileageLabelUtil';
import weightLabelUtil from 'utils/weightLabelUtil';
import speedLabelUtil from 'utils/speedLabelUtil';
import { BOOLEAN_OPTIONS, MESSAGES } from 'constants/constants';
import PROGRAM_CONFIG, { isARCBaseProgram } from 'constants/programConfig';
import {
  checkIfVehicleConfiguratorEnabled,
  getUserAccessibleSitesList,
  getOptionsWithCreatedOption,
  getCustomSelectOptions,
  getFuelTypeOptions,
  getFieldOptionsByName,
  getOptionsWithSelectedValue,
} from 'helpers/common';
import { getLastUpdatedUser } from 'helpers/user';
import {
  getAllBrands,
  getBoolFieldOptions,
  getIsGroupAgingFieldDisabled,
  getModifiedOptionsList,
  getSourceOptions,
  isMultiLingualEnabled,
  isRVDealerEnabled,
  isStandardMakeEnabled,
} from 'helpers/vehicle.helper';
import { getDisplayModelSourceLabel } from 'helpers/vehicleDetails.helper';
import { showConfirmationModal } from 'organisms/ConfirmationModal';
import {
  hasLabelPrintPermission,
  hasStockIdEdit,
  hasVehicleEditDates,
  hasYMMEdit,
  hasInventoryAllCostPricesViewPermission,
} from 'permissions/inventory.permissions';

import { CustomFieldsComponent } from '../../../../VehicleDetails.customFormConfig';
import { getFormSectionWithSectionData } from '../../../../VehicleDetails.sections';
import { FORM_CONTEXT_ID } from '../../../../VehicleDetails.constant';
import { getPicturesCount } from '../../../../../VehicleInventoryList/VehicleInventoryList.helpers';
import {
  checkIfVINEnabled,
  customFieldReader,
  formFieldReader,
  getDealType,
  getFormInitialState,
  getMileageStatusOptions,
  updateVehicleForm,
} from '../../../../VehicleDetails.helpers';
import TrimFieldWrapper from '../TrimFieldWrapper';
import TrackingFieldWrapper from '../TrackingFieldWrapper';
import TrimModal from '../TrimModal';
import { getFormFields } from './GeneralForm.formConfig';
import GENERAL_FORM_ACTION_TYPES from './generalForm.actionTypes';
import { getModifiedTrimOptions, getMultiLingualFieldValue, getValues } from './generalForm.helpers';
import ModelOption from './Components/ModelOption';
import VariantOption from './Components/VariantOption';
import ConfirmVinDecodePopover from './Components/ConfirmVinDecodePopover';

import styles from '../../general.module.scss';
import withHelperText from './Components/withHelperText';

class GeneralForm extends PureComponent {
  static propTypes = {
    form: PropTypes.object,
    fields: PropTypes.array,
    isEditMode: PropTypes.bool,
    onPrint: PropTypes.func,
    actions: PropTypes.object,
    optionsLookupEnable: PropTypes.bool,
    customerViewProps: PropTypes.object,
    enableUsedVehiclesPricingReset: PropTypes.bool,
    onAction: PropTypes.func.isRequired,
    formData: PropTypes.object.isRequired,
    displayModelSource: PropTypes.string,
    dealerMasterData: PropTypes.object,
    metaData: PropTypes.object,
    formKeys: PropTypes.object,
    trimOptions: PropTypes.array,
    options: PropTypes.array,
    styleId: PropTypes.string,
    getModelsList: PropTypes.func,
    getUserInfoById: PropTypes.func,
    countryOptions: PropTypes.array,
    fuelTypeOptions: PropTypes.array,
    vehicleStockType: PropTypes.string,
    syndicationMandatoryFieldsSetup: PropTypes.object,
    vinDecodeModalVisible: PropTypes.bool,
    localeFromStore: PropTypes.object.isRequired,
    isVinDecodeEnabled: PropTypes.bool.isRequired,
    getMeasureUnitLabel: PropTypes.func,
    vinDecodeConfirmPopoverVisible: PropTypes.bool,
    vehicleStatus: PropTypes.string,
    isVinDecodeLoading: PropTypes.bool,
    removeDefaultOptions: PropTypes.bool,
    enableStockNumberGenerationOnSave: PropTypes.bool,
  };

  static defaultProps = {
    form: EMPTY_OBJECT,
    fields: EMPTY_ARRAY,
    isEditMode: true,
    onPrint: _noop,
    actions: EMPTY_OBJECT,
    optionsLookupEnable: false,
    customerViewProps: EMPTY_OBJECT,
    enableUsedVehiclesPricingReset: false,
    displayModelSource: EMPTY_STRING,
    dealerMasterData: EMPTY_OBJECT,
    metaData: EMPTY_OBJECT,
    formKeys: EMPTY_OBJECT,
    trimOptions: EMPTY_ARRAY,
    options: EMPTY_ARRAY,
    styleId: EMPTY_STRING,
    getModelsList: _noop,
    getUserInfoById: _noop,
    countryOptions: EMPTY_ARRAY,
    fuelTypeOptions: EMPTY_ARRAY,
    vehicleStockType: EMPTY_STRING,
    syndicationMandatoryFieldsSetup: EMPTY_OBJECT,
    vinDecodeModalVisible: false,
    getMeasureUnitLabel: _noop,
    vinDecodeConfirmPopoverVisible: false,
    vehicleStatus: EMPTY_STRING,
    isVinDecodeLoading: false,
    removeDefaultOptions: false,
    enableStockNumberGenerationOnSave: false,
  };

  constructor(props) {
    super(props);
    this.isVINEditEnabled = false;
    this.isRegEditEnabled = false;
    this.modelCode = EMPTY_STRING;
    this.contextForFormLevelValidation = React.createRef();
  }

  componentDidMount() {
    this.init();
  }

  componentDidUpdate(prevProps) {
    const {
      errors,
      fields,
      formKeys,
      optionsLookupEnable,
      options,
      styleId,
      metaData,
      customerViewProps,
      trimOptions,
      vinDecodeConfirmPopoverVisible,
      isVinDecodeLoading,
    } = this.props;

    if (!_isEqual(prevProps.errors, errors)) {
      _set(this.contextForFormLevelValidation, 'current.errors', errors);
    }

    if (
      !_isEqual(prevProps.fields, fields) ||
      !_isEqual(prevProps.formKeys, formKeys) ||
      !_isEqual(prevProps.options, options) ||
      !_isEqual(prevProps.styleId, styleId) ||
      !_isEqual(prevProps.optionsLookupEnable, optionsLookupEnable) ||
      !_isEqual(prevProps.metaData?.locationCode, metaData?.locationCode) ||
      !_isEqual(prevProps.metaData?.makes, metaData?.makes) ||
      !_isEqual(prevProps.customerViewProps, customerViewProps) ||
      !_isEqual(prevProps.trimOptions, trimOptions) ||
      !_isEqual(prevProps.vinDecodeConfirmPopoverVisible, vinDecodeConfirmPopoverVisible) ||
      !_isEqual(prevProps.isVinDecodeLoading, isVinDecodeLoading)
    ) {
      this.init();
    }
  }

  hideReceivedDateChangeModal = () => {
    const { onAction } = this.props;
    onAction({
      type: GENERAL_FORM_ACTION_TYPES.HANDLE_HIDE_RECIEVED_DATE_CHANGE_MODAL,
    });
  };

  changeReceivedDate = (date, fieldName) => {
    const { onAction } = this.props;
    onAction({
      type: GENERAL_FORM_ACTION_TYPES.HANDLE_CHANGE_RECEIVED_DATE,
      payload: {
        date,
        fieldName,
      },
    });
  };

  handleEditIconClick = async ({ field, label }) => {
    showConfirmationModal(
      {
        title: __('Edit {{label}}', { label }),
        submitBtnText: __('Edit'),
        message: (
          <div className="d-flex">
            <FontIcon className={styles.warning}>icon-exclamation</FontIcon>
            <Content>
              {__(
                'Edited {{label}} will be saved only at the inventory and it has to be changed manually in Accounting, Deals & attached ROs.',
                { label }
              )}
            </Content>
          </div>
        ),
      },
      () => {
        if (GENERAL_FIELD_KEYS.VIN === field) this.isVINEditEnabled = true;
        else this.isRegEditEnabled = true;

        this.init();
        this.updatePartialFieldStatus(field, 'disabled', false);
      }
    );
  };

  updatePartialFieldStatus = (key, property, value) => {
    const { onAction } = this.props;
    onAction({
      type: GENERAL_FORM_ACTION_TYPES.UPDATE_FORM_FIELDS,
      payload: {
        key,
        property,
        value,
      },
    });
  };

  updateFieldStatus = (formFields, generalFields) => {
    const { onAction } = this.props;
    onAction({
      type: GENERAL_FORM_ACTION_TYPES.INIT_FORM_FIELDS,
      payload: {
        formFields,
        generalFields,
      },
    });
  };

  getEditIconPopOverContent = ({ isEditable, isEditInProgress }) => {
    if (!isEditable)
      return (
        <div className={styles.vinEditIconPopoverContent}>
          {__('Not editable. The vehicle might not have Zero GL balance, or the vehicle is sold')}
        </div>
      );

    return (
      <PropertyControlledComponent controllerProperty={isEditInProgress}>
        <div className={styles.vinEditIconPopoverContent}>{__('Already in edit mode')}</div>
      </PropertyControlledComponent>
    );
  };

  renderEditIconWithPopover = ({ field, isEditable = true, label }) => {
    const isEditInProgress = GENERAL_FIELD_KEYS.VIN === field ? this.isVINEditEnabled : this.isRegEditEnabled;

    return (
      <Popover
        trigger={POPOVER_TRIGGER.HOVER}
        content={this.getEditIconPopOverContent({ isEditable, isEditInProgress })}
        overlayClassName={styles.vinEditPopoverContainer}>
        <IconAsBtn
          className={classnames({ [styles.iconHover]: isEditable && !isEditInProgress })}
          disabled={!isEditable || isEditInProgress}
          onClick={_partial(this.handleEditIconClick, { field, label })}>
          icon-edit
        </IconAsBtn>
      </Popover>
    );
  };

  handleVehicleEditIconClick = () => {
    const { onAction } = this.props;
    onAction({
      type: GENERAL_FORM_ACTION_TYPES.HANDLE_VIN_MODAL_OPEN,
    });
  };

  handleRefreshStockId = () => {
    const { onAction } = this.props;
    onAction({ type: GENERAL_FORM_ACTION_TYPES.REFRESH_STOCK_ID });
  };

  handleMOTRefreshIconClick = () => {
    const { onAction } = this.props;
    onAction({
      type: GENERAL_FORM_ACTION_TYPES.HANDLE_MOT_EXPIRY_REFRESH_CLICK,
    });
  };

  renderEditIcon = (isEnabled = true) => (
    <IconAsBtn
      className={classnames({ [styles.iconHover]: isEnabled })}
      disabled={!isEnabled}
      onClick={this.handleVehicleEditIconClick}>
      icon-edit
    </IconAsBtn>
  );

  getModelChangeConfirmationContent = () => (
    <div className="d-flex">
      <FontIcon className={styles.warning}>icon-exclamation</FontIcon>
      <Content>{MESSAGES.MODEL_CODE_CHANGE_CONFIRMATION}</Content>
    </div>
  );

  onCancelModelCodeChange = () => {
    const { onAction } = this.props;
    onAction({
      type: GENERAL_FORM_ACTION_TYPES.HANDLE_CANCEL_MODEL_CODE_CHANGE,
    });
  };

  renderModelCodeChangeModal = () => {
    const { showModelChangeModal } = this.props;
    return (
      <ConfirmationDialog
        title={__('Edit Model Code')}
        isVisible={showModelChangeModal}
        content={this.getModelChangeConfirmationContent()}
        onCancel={this.onCancelModelCodeChange}
        onSubmit={this.handleModelCodeChange}
        submitBtnText={__('Edit')}
        secondaryBtnText={__('Cancel')}
      />
    );
  };

  handleModelCodeChange = () => {
    const { onAction } = this.props;
    onAction({
      type: GENERAL_FORM_ACTION_TYPES.HANDLE_MODEL_CODE_CHANGE,
    });
  };

  getFormState = () => {
    const { form } = this.props;
    return tget(form.getState(), 'values', EMPTY_OBJECT);
  };

  getExteriorColorPopoverContent = (hexCode, baseColor) => (
    <div className={styles.exteriorColorPopoverContent}>
      <div className="d-flex flex-row">
        <Label>{__('Hexcode:')}</Label>
        <Content className="m-l-24">{hexCode}</Content>
      </div>
      <div className="d-flex flex-row">
        <Label>{__('Base Color:')}</Label>
        <Content className={styles.baseColor}>{baseColor}</Content>
      </div>
    </div>
  );

  renderExteriorColorPopover = () => {
    const hexCode = VehicleReader.extColorHexCode(this.getFormState());
    const baseColor = VehicleReader.extBaseColor(this.getFormState());
    return (
      <Popover trigger={POPOVER_TRIGGER.HOVER} content={this.getExteriorColorPopoverContent(hexCode, baseColor)}>
        <FontIcon size={SIZES.MD} className="cursor-pointer" color={hexCode}>
          {hexCode === '-' ? 'icon-circle-outline' : 'icon-circle-filled'}
        </FontIcon>
      </Popover>
    );
  };

  setVehicleConfiguratorValue = enabled => {
    const { form } = this.props;
    form.change('vehicleConfiguratorFormValue', enabled);
    this.init();
  };

  disableVehicleConfigurator = () => {
    this.setVehicleConfiguratorValue(false);
  };

  getVehicleConfiguratorEnabled = () => {
    const { isEditMode, isVehicleConfiguratorEnabled, options, styleId } = this.props;
    const vehicleConfiguratorFormValue = tget(
      this.getFormState(),
      'vehicleConfiguratorFormValue',
      isVehicleConfiguratorEnabled
    );
    return checkIfVehicleConfiguratorEnabled(
      isVehicleConfiguratorEnabled,
      vehicleConfiguratorFormValue,
      options,
      styleId,
      isEditMode
    );
  };

  resetTrimDetails = () => {
    const { form } = this.props;
    const vehicleConfiguratorEnabled = this.getVehicleConfiguratorEnabled();
    if (vehicleConfiguratorEnabled) {
      form.change('trimDetails', _get(getFormInitialState(), 'trimDetails'));
    }
  };

  updateVehicleDetails = formFields => {
    const { actions } = this.props;
    const optionsField = _find(formFields, { key: 'options' });
    const filteredFields = _filter(formFields, field => !_includes(['options'], field?.key));
    if (_size(optionsField)) {
      actions.addBulkOptions(optionsField?.value);
    }
    updateVehicleForm(actions.updateVehicleObject, filteredFields);
  };

  handlePopulateGroupAgingFields = () => {
    const { onAction } = this.props;
    onAction({ type: GENERAL_FORM_ACTION_TYPES.HANDLE_POPULATE_GROUP_AGING_FIELDS });
  };

  init = () => {
    const {
      fields,
      isEditMode,
      onPrint,
      form,
      metaData,
      getModelsList,
      formData = EMPTY_OBJECT,
      allMakes,
      displayModelSource,
      onAction,
      getUserInfoById,
      countryOptions,
      trimOptions,
      fuelTypeOptions,
      enableUsedVehiclesPricingReset,
      vehicleStockType,
      syndicationMandatoryFieldsSetup,
      getMeasureUnitLabel,
      vinDecodeConfirmPopoverVisible,
      isVinDecodeEnabled,
      lite,
      vehicleStatus,
      isVinDecodeLoading,
      removeDefaultOptions,
      enableStockNumberGenerationOnSave,
    } = this.props;

    const { selectedModel, selectedMake, selectedYear, source, mileageStatus, selectedBrand } = formData;
    const dealerSiteOptions = _get(metaData, 'dealerSiteOptions') || EMPTY_ARRAY;

    const stateOptions = _get(metaData, 'stateOptions') || EMPTY_ARRAY;
    const modelList = tget(metaData, 'modelList', EMPTY_ARRAY);

    const standardMakes = isStandardMakeEnabled();
    let allMakeOptions = tget(metaData, 'makes', EMPTY_ARRAY);
    if (selectedMake && !_includes(allMakes, selectedMake)) {
      allMakeOptions = _concat({ label: capitalizeFirstLetterOnly(selectedMake), value: selectedMake }, allMakeOptions);
    }

    const bodyTypes = tget(metaData, 'bodyTypes', EMPTY_ARRAY);

    const brandList = getModifiedOptionsList(
      getAllBrands(modelList, [selectedMake], selectedYear, standardMakes),
      selectedBrand
    );
    const modelOptions = getModifiedOptionsList(
      getModelsList({
        makes: [selectedMake],
        selectedYear,
        selectedBrand,
        displayModelSource,
        standardMakes,
      }),
      selectedModel
    );

    this.checkStylesPresentForSelectedYMM();

    const originalModelValue = getVehicleModel({
      models: tget(metaData, 'modelList', EMPTY_ARRAY),
      year: selectedYear,
      make: selectedMake,
      selectedModel,
    });

    const handleUnitChange = value => {
      form.change('wheelBase.unitCode', value);
    };

    const formState = this.getFormState();

    const generalFields = _map(fields, field => {
      const formField = tget(GENERAL_FORM_FIELDS_V2_CONFIG, field?.key, EMPTY_OBJECT);
      const label = getLabelFromKey(field?.key) || field?.name;
      let generalField = { ...field, label, ...formField };

      let mileageStatusDefaultValue = mileageStatus;
      const emissionLabel = getEmissionLabel(getMeasureUnitLabel);

      if (!_isEmpty(formField)) {
        switch (generalField.key) {
          case GENERAL_FIELD_KEYS.VIN:
            if (isRRG() && isVinDecodeEnabled) {
              generalField.component = ConfirmVinDecodePopover;
              generalField.popoverVisible = vinDecodeConfirmPopoverVisible;
            }
            if (lite) {
              generalField.mandatory = false;
            }
            if (!isEditMode || lite) {
              break;
            }
            // eslint-disable-next-line no-case-declarations
            const isVINEditable = checkIfVINEnabled(form);
            if (!this.isVINEditEnabled) generalField.disabled = true;
            if (!dealerPropertyHelper.isViArcLiteEnabled()) {
              generalField.addonAfter = this.renderEditIconWithPopover({
                field: generalField.key,
                isEditable: isVINEditable,
                label: generalField.label,
              });
            }
            break;
          case GENERAL_FIELD_KEYS.PRODUCTION:
            const shouldDisablePrintIcon = !hasLabelPrintPermission();
            const isStockFieldDisabled = !hasStockIdEdit() || dealerPropertyHelper.isViArcLiteEnabled();
            if (isARCBaseProgram()) {
              generalField.addonAfter = (
                <Tooltip
                  title={shouldDisablePrintIcon ? MESSAGES.LABEL_PRINT_MESSAGE : EMPTY_STRING}
                  placement={TOOLTIP_PLACEMENT.TOP_RIGHT}
                  arrowPointAtCenter>
                  <IconAsBtn
                    onClick={onPrint}
                    disabled={dealerPropertyHelper.isViArcLiteEnabled() || shouldDisablePrintIcon}>
                    icon-printer
                  </IconAsBtn>
                </Tooltip>
              );
            }
            generalField.addonBefore = (
              <IconAsBtn onClick={this.handleRefreshStockId} disabled={isStockFieldDisabled}>
                icon-refresh
              </IconAsBtn>
            );
            generalField.disabled = isStockFieldDisabled;
            break;
          case GENERAL_FIELD_KEYS.WHEELBASE:
            const value = _get(this.getFormState(), 'wheelBase.unitCode');
            const defaulValue = lengthLabelUtil.getInchUnitLabel();
            if (!value && defaulValue) handleUnitChange(defaulValue);
            generalField.addonBefore = <Content>{value || defaulValue || NO_DATA}</Content>;
            break;
          case GENERAL_FIELD_KEYS.SERVICE_INTERVAL_MILEAGE:
            generalField.addonBefore = mileageLabelUtil.getMileageUnitLabel();
            break;
          case GENERAL_FIELD_KEYS.MILEAGE:
            if (generalField.mandatory) {
              generalField.validators = [...generalField.validators, nonZeroRule];
            }
            generalField.addonBefore = __('{{mileageLabel}}', {
              mileageLabel: mileageLabelUtil.getMileageUnitLabel(),
            });
            break;
          case GENERAL_FIELD_KEYS.MILEAGE_STATUS:
            if (!isEditMode && _isEmpty(mileageStatusDefaultValue)) {
              mileageStatusDefaultValue = MILEAGE_STATUS.ACTUAL;
            }
            generalField.defaultValue = mileageStatusDefaultValue;
            generalField.validation = undefined;
            generalField.options = getMileageStatusOptions();
            break;
          case GENERAL_FIELD_KEYS.SOURCE: {
            const selectedSource = !source ? EMPTY_ARRAY : _castArray(source);
            generalField.options = getSourceOptions(
              _castArray(field),
              selectedSource,
              false,
              vehicleStockType,
              isRRG()
            );
            break;
          }
          case GENERAL_FIELD_KEYS.LICENSE_PLATE_NUMBER: {
            const isRegDecodeEnabledProgram = PROGRAM_CONFIG.shouldDecodeRegistrationNumber();
            if (lite) break;
            if (isRegDecodeEnabledProgram && isEditMode) {
              // eslint-disable-next-line no-case-declarations
              if (!this.isRegEditEnabled) generalField.disabled = true;

              generalField.addonAfter = this.renderEditIconWithPopover({
                field: generalField.key,
                label: _result(generalField, 'label'),
              });
            }

            break;
          }
          case GENERAL_FIELD_KEYS.PICTURES_COUNT:
            generalField.defaultValue = getPicturesCount;
            break;
          case GENERAL_FIELD_KEYS.VAT_UV_ONLY:
          case GENERAL_FIELD_KEYS.RECOMMENDED:
          case GENERAL_FIELD_KEYS.TELEMATICS_SERVICE_FLAG:
          case GENERAL_FIELD_KEYS.CURRENT_MODEL:
          case GENERAL_FIELD_KEYS.EXCLUSIVE_PRODUCT:
          case GENERAL_FIELD_KEYS.DEALER_TRADE:
          case GENERAL_FIELD_KEYS.PREOWNED_REGISTERED:
          case GENERAL_FIELD_KEYS.STOP_DELIVERY_INDICATOR:
            generalField.options = getBoolFieldOptions();
            break;
          case GENERAL_FIELD_KEYS.PREVIOUS_USE:
            const selectedPreviousUse = _get(formState, 'certificationGroup.previousUse');
            generalField.options = getOptionsWithSelectedValue(field?.options, selectedPreviousUse);
            break;
          case GENERAL_FIELD_KEYS.LOCATION_CODE:
            const locationCode = _get(formState, 'locationCode');
            generalField.options = getOptionsWithSelectedValue(field?.options, locationCode);
            break;
          case GENERAL_FIELD_KEYS.YEAR:
            generalField.options = _map(getYearOptions(1920, +getCurrentYearValue() + 3), option => ({
              ...option,
              label: option?.value,
            }));

            generalField.disabled = !hasYMMEdit();

            break;
          case GENERAL_FIELD_KEYS.MAKE:
            if (!standardMakes) {
              generalField = {
                ...generalField,
                component: CreatableSelect,
                shouldConvertValueToArray: true,
                createOptionPosition: 'first',
                isClearable: true,
                disabled: !hasYMMEdit(),
                options: allMakeOptions,
                onCreateOption: this.onCreateNewOption(generalField.id),
                parse: val => (val ? _head(val) : val),
              };
            } else {
              generalField = {
                ...generalField,
                showSearch: true,
                allowClear: true,
                disabled: !hasYMMEdit(),
                options: allMakeOptions,
              };
            }

            break;
          case GENERAL_FIELD_KEYS.LAST_MODIFIED_USER:
            const getUserDisplayName = val => {
              const { value: newVal } = getLastUpdatedUser(val, getUserInfoById) || {};
              return newVal;
            };
            generalField = {
              ...generalField,
              format: getUserDisplayName,
            };
            break;
          case GENERAL_FIELD_KEYS.BRAND:
            generalField.options = brandList;
            generalField.onCreateOption = this.onCreateNewOption(generalField.id);
            break;
          case GENERAL_FIELD_KEYS.TAGS:
            generalField.defaultValue = VehicleReader.tags(this.getFormState());
            generalField.component = TrackingFieldWrapper;
            generalField.form = form;
            break;
          case GENERAL_FIELD_KEYS.MODEL:
            const bestStyleName = VehicleReader.bestStyleName(this.getFormState());
            const displayModel = VehicleReader.displayModel(this.getFormState());
            generalField.name = getDisplayModelSourceLabel(displayModelSource, bestStyleName, displayModel);
            generalField.disabled = !hasYMMEdit();
            generalField.options = modelOptions;
            generalField.onCreateOption = this.onCreateNewOption(generalField.id);

            if (PROGRAM_CONFIG.shouldFilterModelsBasedOnYearRange() && !PROGRAM_CONFIG.shouldShowAsyncModelSelect()) {
              generalField.components = { Option: ModelOption };
            }

            if (PROGRAM_CONFIG.shouldShowAsyncModelSelect()) {
              generalField.component = ModelSelectRenderer;
              generalField.allMakes = allMakes;
              generalField.additional = {
                makes: [selectedMake],
                selectedYear,
                selectedBrand,
                displayModelSource,
                standardMakes,
                rendererType: ASYNC_MODEL_RENDERER_TYPE.CREATABLE_SINGLE_SELECT,
              };
            }

            break;
          case GENERAL_FIELD_KEYS.RECEIVED_DATE:
            generalField.disabled = !hasVehicleEditDates();
            break;
          case GENERAL_FIELD_KEYS.EXTERIOR_COLOR:
            generalField.addonAfter = !isEditMode ? null : this.renderExteriorColorPopover();
            generalField.disabled = isEditMode && !VehicleReader.exteriorColorEditable(this.getFormState());
            break;
          case GENERAL_FIELD_KEYS.TITLE_STATE:
            generalField.options = _map(stateOptions, state => ({ ...state, label: _get(state, 'value') }));
            break;
          case GENERAL_FIELD_KEYS.STOCKED_IN_AT_SITE_ID:
          case GENERAL_FIELD_KEYS.SOLD_AT_SITE_ID:
            const siteId = _get(this.getFormState(), [generalField.name]);
            generalField.options = getUserAccessibleSitesList(dealerSiteOptions, siteId);
            break;
          case GENERAL_FIELD_KEYS.TRIM: {
            const vehicleConfiguratorEnabled = this.getVehicleConfiguratorEnabled();
            generalField.component = TrimFieldWrapper;
            generalField.isVinDecodeLoading = isVinDecodeLoading;
            if (vehicleConfiguratorEnabled) {
              const dealTypeValue = getDealType();
              generalField.year = selectedYear;
              generalField.model = originalModelValue || selectedModel;
              generalField.make = selectedMake;
              generalField.className = `p-y-12 ${styles.trimField}`;
              generalField.disableVehicleConfigurator = this.disableVehicleConfigurator;
              generalField.updateVehicleDetails = this.updateVehicleDetails;
              generalField.vehicleConfiguratorEnabled = true;
              generalField.dealType = dealTypeValue || 'Retail';
              generalField.isVehicleUsed = vehicleStockType === VEHICLE_TYPES.USED && enableUsedVehiclesPricingReset;
              generalField.isEditMode = isEditMode;
              generalField.moduleType = MODULE.VI;
              generalField.removeDefaultOptions = removeDefaultOptions;
              generalField.shouldHideInvoiceTooltip = !hasInventoryAllCostPricesViewPermission();
            }

            break;
          }
          case GENERAL_FIELD_KEYS.HITCH_WEIGHT:
          case GENERAL_FIELD_KEYS.UNLADEN_WEIGHT:
          case GENERAL_FIELD_KEYS.GROSS_VEHICLE_WEIGHT:
            generalField.addonAfter = __('{{weightMeasure}}', {
              weightMeasure: weightLabelUtil.getWeightMeasureUnitLabel(),
            });
            break;
          case GENERAL_FIELD_KEYS.DAYS_TO_SELL:
            form.change('vehicleAdditionalDetails.daysToSell', getDaysTosell(this.getFormState()));
            break;
          case GENERAL_FIELD_KEYS.DAYS_ON_LOT:
            form.change('vehicleAdditionalDetails.daysOnLot', getDaysOnLot(this.getFormState()));
            break;
          case GENERAL_FIELD_KEYS.FUEL_TYPE_TRIM:
            const fuelType = _get(this.getFormState(), 'trimDetails.fuelType');
            generalField.options = getFuelTypeOptions(getCustomSelectOptions(fuelTypeOptions, fuelType));
            break;
          case GENERAL_FIELD_KEYS.VEHICLE:
            generalField.addonAfter = this.renderEditIcon();
            break;
          case GENERAL_FIELD_KEYS.TRIM_CODE:
            if (PROGRAM_CONFIG.shouldShowCustomTrim()) {
              const selectedTrim = _get(this.getFormState(), 'trimDetails.trim');

              const options = selectedTrim
                ? getModifiedTrimOptions(trimOptions?.data, selectedTrim)
                : trimOptions?.data;

              const helperText =
                trimOptions?.fromRegDecode &&
                _size(options) > 1 &&
                !selectedTrim &&
                __('Please select the correct variant from the provided options.');

              generalField = {
                ...generalField,
                options,
                label: __('Variant'),
                component: withHelperText(CreatableSelect),
                shouldConvertValueToArray: true,
                helperText,
                createOptionPosition: 'first',
                isClearable: true,
                parse: val => (val ? _head(val) : val),
                onCreateOption: this.onCreateNewOption(generalField.id),
                components: { Option: VariantOption },
                isLoading: trimOptions?.isFetching,
              };
            }
            break;
          case GENERAL_FIELD_KEYS.BODY_CLASS:
            const selectedBodyClass = _get(this.getFormState(), 'trimDetails.bodyClass');
            const options = isRVDealerEnabled()
              ? generalField.options
              : getFieldOptionsByName(field?.options, BODY_CLASS_ENUM_LABEL_MAP);
            generalField.options = selectedBodyClass
              ? getOptionsWithCreatedOption(options, selectedBodyClass)
              : options;
            break;
          case GENERAL_FIELD_KEYS.SALE_TYPE:
            getFieldOptionsByName(field?.options, SALE_TYPE_ENUM_LABEL_MAP);
            break;
          case GENERAL_FIELD_KEYS.ORIGINAL_TYPE_CODE:
            getFieldOptionsByName(field?.options, ORIGINAL_TYPE_CODE_ENUM_LABEL_MAP);
            break;
          case GENERAL_FIELD_KEYS.SEAT_MATERIAL:
            getFieldOptionsByName(field?.options, SEAT_MATERIAL_ENUM_LABEL_MAP);
            break;
          case GENERAL_FIELD_KEYS.MOT_EXPIRY_DATE:
            generalField.label = (
              <div className="d-flex align-items-center">
                <Content>{__('MOT Expiry Date')}</Content>
                <FontIcon className="m-l-4 cursor-pointer" onClick={this.handleMOTRefreshIconClick}>
                  icon-refresh2
                </FontIcon>
              </div>
            );
            generalField.labelClassName = 'd-flex';
            break;
          case GENERAL_FIELD_KEYS.IMPORTED_FROM:
            generalField.options = countryOptions;
            generalField.disabled = !tget(this.getFormState(), 'isImported', false);
            break;
          case GENERAL_FIELD_KEYS.SEATING_CAPACITY:
            generalField.options = getSelectOptions(_range(2, 16));
            break;
          case GENERAL_FIELD_KEYS.MAX_SPEED_OF_VEHICLE:
            generalField.addonBefore = speedLabelUtil.getSpeedMeasureUnitLabel();
            break;
          case GENERAL_FIELD_KEYS.BODY_TYPE_TRIM:
            const selectedBodyType = _get(this.getFormState(), 'trimDetails.bodyType');
            generalField.options = selectedBodyType
              ? getOptionsWithCreatedOption(bodyTypes, selectedBodyType)
              : bodyTypes;
            generalField.onCreateOption = this.onCreateNewOption(generalField.id);
            break;
          case GENERAL_FIELD_KEYS.WLTP_CO2:
            generalField.addonAfter = emissionLabel;
            break;
          case GENERAL_FIELD_KEYS.NEDC_CO2:
            generalField.addonAfter = emissionLabel;
            break;
          case GENERAL_FIELD_KEYS.FUEL_TANK_CAPACITY:
            generalField.addonAfter = getCapacityLabel(getMeasureUnitLabel);
            break;
          case GENERAL_FIELD_KEYS.MILEAGE_TYPE_INFO:
            if (!isEditMode) {
              form.change('vehicleMileageType', MILEAGE_TYPES.NON_VERIFIED);
            }
            break;
          case GENERAL_FIELD_KEYS.LENGTH_IN_FEET:
          case GENERAL_FIELD_KEYS.GARAGE_LENGTH:
            generalField.addonAfter = lengthLabelUtil.getFootUnitLabel();
            break;
          case GENERAL_FIELD_KEYS.LENGTH_IN_INCHES:
          case GENERAL_FIELD_KEYS.LENGTH:
          case GENERAL_FIELD_KEYS.HEIGHT:
          case GENERAL_FIELD_KEYS.WIDTH:
            generalField.addonAfter = lengthLabelUtil.getInchUnitLabel();
            break;
          case GENERAL_FIELD_KEYS.GROUP_RECEIVED_DATE:
          case GENERAL_FIELD_KEYS.GROUP_STOCKED_IN_DATE:
          case GENERAL_FIELD_KEYS.GROUP_SOLD_DATE:
            this.handlePopulateGroupAgingFields();
            if (generalField.key !== GENERAL_FIELD_KEYS.GROUP_SOLD_DATE) {
              generalField.disabled = getIsGroupAgingFieldDisabled(this.getFormState());
            }
            break;
          default:
            break;
        }

        if (dealerPropertyHelper.isViArcLiteEnabled()) {
          generalField.disabled = true;
          generalField.isDisabled = true;
        }
        if (generalField.isMultiLingual && isMultiLingualEnabled()) {
          const { label: fieldName, name } = generalField || EMPTY_OBJECT;
          generalField.multiLingualLabel = fieldName;
          generalField.multilingualId = generalField.id;
          generalField.onAction = onAction;
          generalField.getMultiLingualFieldValue = getMultiLingualFieldValue;
          generalField.valuePath = _split(name, '.');
          generalField.className = styles.multiLingualInput;
        } else {
          generalField.isMultiLingual = false;
        }
        return formFieldReader({
          fields: generalField,
          field,
          vehicleStockType,
          syndicationMandatoryFieldsSetup,
          vehicleStatus,
          enableStockNumberGenerationOnSave,
          isEditMode,
        });
      }
      generalField = { ...field, component: CustomFieldsComponent({ type: field.fieldType }) };
      if (field?.fieldType === FIELD_TYPES_KEY.BOOLEAN) generalField.options = BOOLEAN_OPTIONS;
      return customFieldReader(generalField, field, vehicleStatus);
    });
    const formFields = getFormFields({ fields: generalFields });

    this.updateFieldStatus(formFields, generalFields);
  };

  checkStylesPresentForSelectedYMM = async () => {
    const { options, isVehicleConfiguratorEnabled } = this.props;
    const vehicleConfiguratorFormValue = tget(
      this.getFormState(),
      'vehicleConfiguratorFormValue',
      isVehicleConfiguratorEnabled
    );
    if (isVehicleConfiguratorEnabled && !vehicleConfiguratorFormValue && !options?.length) {
      const { formData } = this.props;
      const dealTypeValue = getDealType();
      const { selectedModel, selectedMake, selectedYear } = formData || EMPTY_OBJECT;

      const rawResponse = await ChromeApi.fetchStyles({
        model: selectedModel,
        year: selectedYear,
        make: selectedMake,
        orderAvailability: dealTypeValue || 'Retail',
      });
      if (!_isEmpty(tget(rawResponse, 'data.style', EMPTY_OBJECT))) {
        this.setVehicleConfiguratorValue(true);
      }
    }
  };

  onCreateNewOption = id => value => {
    const { onAction } = this.props;
    onAction({
      type: GENERAL_FORM_ACTION_TYPES.HANDLE_ADD_OPTION,
      payload: { id, value },
    });
  };

  handleVINDecodeModalClose = () => {
    const { onAction } = this.props;
    onAction({
      type: GENERAL_FORM_ACTION_TYPES.HANDLE_VIN_MODAL_CLOSE,
    });
  };

  handleDecodeSubmit = value => {
    const { onAction } = this.props;
    onAction({
      type: GENERAL_FORM_ACTION_TYPES.HANDLE_DECODE_SUBMIT,
      payload: { vinDetails: value },
    });
  };

  renderReceivedDataChangeModal = () => {
    const { selectedReceivedDate, showReceivedDateChangeModal } = this.props;
    return (
      <ConfirmationDialog
        title={__('Confirm Date Change')}
        isVisible={showReceivedDateChangeModal}
        content={MESSAGES.RECEIVED_DATE}
        onCancel={this.hideReceivedDateChangeModal}
        onSubmit={() => {
          this.changeReceivedDate(selectedReceivedDate, 'entryTime');
          this.hideReceivedDateChangeModal();
        }}
        submitBtnText={__('Confirm')}
        secondaryBtnText={__('Cancel')}
      />
    );
  };

  onTrimChange = value => {
    const { onAction } = this.props;
    onAction({
      type: GENERAL_FORM_ACTION_TYPES.HANDLE_TRIM_SELECT_CHANGE,
      payload: { value },
    });
  };

  render() {
    const {
      form,
      sectionDetails,
      onAction,
      errors,
      formFieldsConfig,
      fields,
      displayModelSource,
      vinDecodeModalVisible,
      localeFromStore,
      dealerMasterData,
      customerViewProps,
      countryOptions,
      multiLingualFieldDetails,
      multiLingualFormProps,
      languageOptions,
      currentLanguageId,
      isVinDecodeEnabled,
      payloadForRegistrationDecode,
      isVinDecodeLoading,
      isEditMode,
    } = this.props;

    const formState = this.getFormState();

    const sections = getFormSectionWithSectionData({
      sectionDetails,
      formFieldsConfig,
      customerViewProps,
      countryOptions,
    });

    const values = getValues({
      formValues: formState,
      formFields: fields,
      formFieldsConfig,
      vehicleConfiguratorFormValue: this.getVehicleConfiguratorEnabled(),
      displayModelSource,
      dealerMasterData,
    });

    const updatedMultiLingualFormProps = {
      ...multiLingualFormProps,
      languageOptions,
      currentLanguageId,
    };

    return (
      <>
        <FormWithSubmission
          contextId={FORM_CONTEXT_ID.GENERAL_FORM_CONTEXT_ID}
          values={values}
          fields={formFieldsConfig}
          sections={sections}
          onAction={onAction}
          errors={errors}
          multiLingualFieldDetails={multiLingualFieldDetails}
          multiLingualFormProps={updatedMultiLingualFormProps}
          shouldRemoveErrorOnSubmission
          contextForFormLevelValidation={this.contextForFormLevelValidation}
        />
        {this.renderModelCodeChangeModal()}
        {this.renderReceivedDataChangeModal()}
        <TrimModal form={form} />
        <SelectTrimModal
          onSubmit={this.onTrimChange}
          vehicleData={formState}
          displayModelSource={displayModelSource}
          isEditMode={isEditMode}
          isVinDecodeLoading={isVinDecodeLoading}
        />
        <VinDecodeModal
          isVisible={vinDecodeModalVisible}
          payloadData={payloadForRegistrationDecode}
          onClose={this.handleVINDecodeModalClose}
          onSubmit={this.handleDecodeSubmit}
          locale={localeFromStore?.language}
        />
      </>
    );
  }
}

export default withTekionConversion(GeneralForm);
