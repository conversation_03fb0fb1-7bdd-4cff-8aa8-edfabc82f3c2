import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { defaultMemoize } from 'reselect';
import _noop from 'lodash/noop';

import { EMPTY_ARRAY } from 'tbase/app.constants';

import { getMediaListFromAttachment } from 'helpers/media';
import Attachments from 'molecules/Attachments/Attachments';

import styles from '../general.module.scss';

class GeneralAttachment extends PureComponent {
  static propTypes = {
    addAttachedFiles: PropTypes.func,
    removeAttachedFiles: PropTypes.func,
    modifyAttachmentSensitivity: PropTypes.func,
    attachments: PropTypes.array,
    isReadOnly: PropTypes.bool,
  };

  static defaultProps = {
    addAttachedFiles: _noop,
    removeAttachedFiles: _noop,
    modifyAttachmentSensitivity: _noop,
    attachments: EMPTY_ARRAY,
    isReadOnly: false,
  };

  getMediaListFromAttachment = defaultMemoize(getMediaListFromAttachment);

  onUploadFiles = files => {
    const { addAttachedFiles } = this.props;
    addAttachedFiles(files);
  };

  onDeleteFiles = mediaFile => {
    const { removeAttachedFiles } = this.props;
    removeAttachedFiles(mediaFile);
  };

  handleModifySensitivity = mediaFile => {
    const { modifyAttachmentSensitivity } = this.props;
    modifyAttachmentSensitivity(mediaFile);
  };

  render() {
    const { attachments, isReadOnly } = this.props;
    const modifiedAttachments = this.getMediaListFromAttachment(attachments);

    return (
      <div className={styles.generalAttachmentTable}>
        <Attachments
          onUploadFiles={this.onUploadFiles}
          onDeleteFiles={this.onDeleteFiles}
          onModifySensitivity={this.handleModifySensitivity}
          attachments={modifiedAttachments}
          showHeading={false}
          isReadOnly={isReadOnly}
        />
      </div>
    );
  }
}

export default GeneralAttachment;
