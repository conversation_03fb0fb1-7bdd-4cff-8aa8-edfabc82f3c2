import { createAction } from 'redux-actions';
import _concat from 'lodash/concat';
import _filter from 'lodash/filter';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _map from 'lodash/map';
import _reject from 'lodash/reject';
import _find from 'lodash/find';
import _head from 'lodash/head';

import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import { getErrorMessage } from 'tbase/utils/errorUtils';
import { tget } from 'tbase/utils/general';
import * as VIServiceAPI from 'tbase/services/sales/vehicleInventoryService';
import getDataFromResponse from 'tbase/utils/getDataFromResponse';
import { toaster, TOASTER_TYPE } from 'tcomponents/organisms/NotificationWrapper';
import Modal from 'tcomponents/molecules/Modal';

import { MESSAGES } from 'constants/constants';
import PROGRAM_CONFIG from 'constants/programConfig';
import DealerManager from 'utils/dealerManager';
import { isCVDVinDecodingSource, getAttachmentWithToggledSensitivity } from 'helpers/common';
import { getVIState } from 'helpers/moduleFederation';
import { isMultiLingualEnabled } from 'helpers/vehicle.helper';
import VehicleService from 'services/vehicle';
import MetadataAPIs from 'services/metaData';
import VendorAPIs from 'services/vendor';
import { showConfirmationModal } from 'organisms/ConfirmationModal';

import GeneralAPI from './General.api';
import { generalActions as ACTIONS } from './general.actionTypes';

const setAttachedFilesDataAction = createAction(ACTIONS.VI_SET_ATTACHED_FILES_DATA);
const setVehicleHistory = createAction(ACTIONS.FETCH_VEHICLE_HISTORY);

export function generateStockId(payload = {}, errorMessage) {
  return VIServiceAPI.getStockId(payload, DealerManager.getDealerId())
    .then(getDataFromResponse)
    .catch(() => {
      toaster(TOASTER_TYPE.ERROR, errorMessage || MESSAGES.FAILED_STOCK_ID_GENERATION);
      return false;
    });
}

export function fetchVINDetails(VIN = '', langParam, shouldUpdateRedux = true) {
  return dispatch => {
    dispatch({ type: ACTIONS.FETCH_VIN_DETAILS_REQUEST });
    if (PROGRAM_CONFIG.shouldFetchVINLookupDetailsV2()) {
      return GeneralAPI.fetchVINLookupDetailsV2({
        vin: VIN,
        locales: [langParam],
      }).then(({ data }) => data);
    }
    if (isMultiLingualEnabled()) {
      return GeneralAPI.fetchVINLookupDetailsMultiLingual(VIN, langParam, isCVDVinDecodingSource())
        .then(({ data }) => {
          if (shouldUpdateRedux) {
            dispatch({ type: ACTIONS.FETCH_VIN_DETAILS_SUCCESS, payload: data });
          }
          return data;
        })
        .catch(() => {
          dispatch({ type: ACTIONS.FETCH_VIN_DETAILS_FAILURE });
          return false;
        });
    }
    return GeneralAPI.fetchVINLookupDetails(VIN, langParam, isCVDVinDecodingSource())
      .then(({ data }) => {
        if (shouldUpdateRedux) {
          dispatch({ type: ACTIONS.FETCH_VIN_DETAILS_SUCCESS, payload: data });
        }
        return data;
      })
      .catch(() => {
        dispatch({ type: ACTIONS.FETCH_VIN_DETAILS_FAILURE });
        return false;
      });
  };
}

export function fetchTotalCount(filters = {}) {
  return async () => {
    const params = { filters };
    const {
      data: { count },
    } = await VehicleService.fetchVehicles(params);
    return count;
  };
}

export const addAttachedFiles = payload => async (dispatch, getState) => {
  const state = getVIState(getState());
  const alreadyAttachedFiles = tget(state, 'vehicleDetails.attachments', EMPTY_ARRAY);
  const newFiles = _filter(payload, file => !_isEmpty(_get(file, 'mediaList')));
  const attachments = _concat(newFiles, alreadyAttachedFiles);
  dispatch(setAttachedFilesDataAction(attachments));
};

export const removeAttachedFiles = mediaFileToDelete => async (dispatch, getState) => {
  const state = getVIState(getState());
  const alreadyAttachedFiles = tget(state, 'vehicleDetails.attachments', EMPTY_ARRAY);
  const uploadedFileList = _map(alreadyAttachedFiles, item => {
    const mediaList = tget(item, 'mediaList', EMPTY_ARRAY);
    return {
      ...item,
      mediaList: _reject(
        mediaList,
        ({ mediaFile, mediaId }) => mediaFile === mediaFileToDelete || mediaId === mediaFileToDelete
      ),
    };
  });
  const attachments = _filter(uploadedFileList, ({ mediaList }) => !_isEmpty(mediaList));
  dispatch(setAttachedFilesDataAction(attachments));
};

export function fetchHardpackMetadata(payload) {
  return async () => {
    try {
      const response = await MetadataAPIs.getHardpackMetadata(payload);
      return response?.data || EMPTY_OBJECT;
    } catch (error) {
      return EMPTY_OBJECT;
    }
  };
}

export function fetchVendorCategoryCodes(payload) {
  return async () => {
    try {
      const response = await VendorAPIs.getVendorsSetupList(payload);
      return response?.data?.category || EMPTY_ARRAY;
    } catch (error) {
      return EMPTY_ARRAY;
    }
  };
}

export const fetchVehicleDetailsOnRegDecode =
  ({ registrationPlateNumber, capId, langParam }) =>
  async () => {
    try {
      toaster(TOASTER_TYPE.INFO, MESSAGES.REG_DECODE_FETCHING);
      const response = await GeneralAPI.fetchVINLookupDetailsV2({
        registrationPlateNumber,
        capId,
        locales: [langParam],
      });
      if (_isEmpty(response?.data)) {
        toaster(TOASTER_TYPE.INFO, MESSAGES.REG_DECODE_NO_RESPONSE);
      } else {
        toaster(TOASTER_TYPE.SUCCESS, MESSAGES.REG_DECODE_SUCCESS);
      }
      return _get(response, 'data.0', EMPTY_OBJECT);
    } catch (error) {
      toaster(TOASTER_TYPE.ERROR, getErrorMessage(error, MESSAGES.REG_DECODE_FAILURE));
      return EMPTY_OBJECT;
    }
  };

export const fetchVehicleDetailsByStyleId = payload => async () => {
  try {
    toaster(TOASTER_TYPE.INFO, MESSAGES.REG_DECODE_FETCHING);
    const response = await GeneralAPI.fetchVehicleDetailsByStyleId(payload);
    if (_isEmpty(response?.data)) {
      toaster(TOASTER_TYPE.INFO, MESSAGES.REG_DECODE_NO_RESPONSE);
    } else {
      toaster(TOASTER_TYPE.SUCCESS, MESSAGES.REG_DECODE_SUCCESS);
    }
    return _get(response, 'data', EMPTY_OBJECT);
  } catch (error) {
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(error, MESSAGES.REG_DECODE_FAILURE));
    return EMPTY_OBJECT;
  }
};

export const fetchTrimOptions =
  ({ make, modelId, vehicleCategory }) =>
  async () => {
    try {
      const payload = {
        vehicleCategory,
        divisionName: make,
        modelId,
      };
      if (!vehicleCategory) {
        toaster(TOASTER_TYPE.INFO, MESSAGES.INVALID_VEHICLE_CATEGORY);
        return EMPTY_ARRAY;
      }
      const response = await GeneralAPI.fetchTrimOptions(payload);
      return _get(response, 'data.style', EMPTY_ARRAY);
    } catch (error) {
      return EMPTY_ARRAY;
    }
  };

export const fetchVatCodeForVendor = payload => async () => {
  try {
    const response = await GeneralAPI.fetchVatCode(payload);
    return _get(response, 'data.VAT_CODE.data', EMPTY_OBJECT);
  } catch (error) {
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(error, MESSAGES.FETCH_VAT_CODE_FAILURE));
    return EMPTY_OBJECT;
  }
};

export const fetchVIVehicleHistory = registrationPlateNumber => async dispatch => {
  try {
    const HPIResponse = await GeneralAPI.fetchVIVehicleHistoryReports(registrationPlateNumber);
    const reponseData = HPIResponse?.data;
    dispatch(setVehicleHistory(reponseData));
  } catch (error) {
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(error, MESSAGES.VEHICLE_HISTORY_FAILURE));
    dispatch({ type: ACTIONS.VEHICLE_HISTORY_FAILURE, payload: error });
  }
};

export const fetchMOTExpiryDate = payload => async () => {
  try {
    const response = await GeneralAPI.fetchMOTExpiryRefresh(payload);
    return response?.data;
  } catch (error) {
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(error, MESSAGES.MOT_REFRESH_FAILURE));
    return null;
  }
};

export const fetchExternalLookupData = payload => async () => {
  try {
    const response = await GeneralAPI.fetchExternalLookupData(payload);
    return response?.data;
  } catch (error) {
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(error));
    return null;
  }
};

export const fetchStockTypeChangeCostOverview = (newStockType, vehicleId) => async () => {
  try {
    const response = await GeneralAPI.fetchStockTypeChangeCostOverview(newStockType, vehicleId);
    return response?.data;
  } catch (error) {
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(error, MESSAGES.FETCH_STOCK_TYPE_CHANGE_COST_SUMMARY_FAILURE));
    return null;
  }
};

export const modifyAttachmentSensitivity = mediaFileId => async (dispatch, getState) => {
  const state = getVIState(getState());
  const alreadyAttachedFiles = tget(state, 'vehicleDetails.attachments', EMPTY_ARRAY);
  const selectedMedia = _find(alreadyAttachedFiles, item => {
    const mediaList = tget(item, 'mediaList', EMPTY_ARRAY);
    return _find(mediaList, ({ mediaFile, mediaId }) => (mediaFile ?? mediaId) === mediaFileId);
  });

  const updateSensitiveStatus = () => {
    const updatedFiles = getAttachmentWithToggledSensitivity(alreadyAttachedFiles, mediaFileId);
    dispatch(setAttachedFilesDataAction(updatedFiles));
  };

  if (!_get(_head(_get(selectedMedia, 'mediaList')), 'sensitive')) {
    showConfirmationModal(
      {
        title: __('Mark as Sensitive'),
        message: __('Are you sure you want to mark this as a document containing sensitive information?'),
        submitBtnText: __('Confirm'),
        width: Modal.SIZES.SM,
      },
      updateSensitiveStatus
    );
  } else {
    updateSensitiveStatus();
  }
};
