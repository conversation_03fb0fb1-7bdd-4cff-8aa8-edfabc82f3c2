import { stringInterpolate } from 'tbase/formatters/string';

import { URL_TYPES } from 'tbase/constants/api';

import DealerManager from 'utils/dealerManager';
import Http from 'services/http';

const API_PATHS = {
  FETCH_VIN_LOOKUP_DETAILS_MULTILINGUAL: '/api-vi/u/v1.0.0/vinlookup/ml/{vin}',
  FETCH_VIN_LOOKUP_DETAILS: '/api-vi/u/v1.0.0/vinlookup/{vin}',
  FETCH_VIN_LOOKUP_DETAILS_MULTILINGUAL_CVD: '/api-vi/u/v1.0.0/vinlookup/ml/{vin}',
  FETCH_VIN_LOOKUP_DETAILS_CVD: '/api-vi/u/v1.0.0/vinlookup/{vin}',
  FETCH_VIN_LOOKUP_V2: 'vi/u/v1.0.0/v2/vinlookup',
  <PERSON><PERSON>CH_DETAILS_BY_STYLE_ID: '/vi/u/v1.0.0/v2/variant-lookup',
  FETCH_TRIM_OPTIONS: '/api-vi/u/v1.0.0/model/trims',
  FETCH_VAT_CODE: 'api-vi/u/v1.0.0/inventory/tax-rule-engine/evaluate-input',
  FETCH_VI_VEHICLE_HISTORY: '/api-vi/u/v1.0.0/vehicle-history/{registrationNumber}',
  FETCH_MOT_REFRESH: 'api-vi/u/v1/dvla/mot-expiry/refresh',
  FETCH_EXTERNAL_LOOKUP: 'api-vi/u/v1.0.0/external/lookup',
  // add api-vi later
  FETCH_STOCK_TYPE_CHANGE_COST_OVERVIEW: 'vi/u/v1.0.0/inventory/{vehicleId}/costs/stock-type-change-overview',
};

export default class GeneralAPI extends DealerManager {
  static fetchVINLookupDetails(vin, langParam, isCVD) {
    const apiPath = isCVD ? API_PATHS.FETCH_VIN_LOOKUP_DETAILS_CVD : API_PATHS.FETCH_VIN_LOOKUP_DETAILS;
    const queryParams = {
      langParam,
      dealerId: this.getDealerId(),
    };
    if (isCVD) {
      queryParams.dataSource = 'CVD';
    }
    return Http.get(URL_TYPES.CDMS, stringInterpolate(apiPath, { vin }), queryParams);
  }

  static fetchVINLookupDetailsMultiLingual(vin, langParam, isCVD) {
    const apiPath = isCVD
      ? API_PATHS.FETCH_VIN_LOOKUP_DETAILS_MULTILINGUAL_CVD
      : API_PATHS.FETCH_VIN_LOOKUP_DETAILS_MULTILINGUAL;
    const queryParams = {
      langParam,
      dealerId: this.getDealerId(),
    };
    if (isCVD) {
      queryParams.dataSource = 'CVD';
    }
    return Http.post(URL_TYPES.CDMS, stringInterpolate(apiPath, { vin }), [langParam], queryParams);
  }

  static fetchVINLookupDetailsV2(payload) {
    const queryParams = {
      dealerId: this.getDealerId(),
    };
    return Http.post(URL_TYPES.CDMS, API_PATHS.FETCH_VIN_LOOKUP_V2, payload, queryParams);
  }

  static fetchVehicleDetailsByStyleId(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.FETCH_DETAILS_BY_STYLE_ID, payload);
  }

  static fetchTrimOptions(payload) {
    const queryParams = {
      dealerId: this.getDealerId(),
    };
    return Http.post(URL_TYPES.CDMS, API_PATHS.FETCH_TRIM_OPTIONS, payload, queryParams);
  }

  static fetchVatCode(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.FETCH_VAT_CODE, payload);
  }

  static fetchMOTExpiryRefresh(payload) {
    return Http.post(URL_TYPES.CDMS, API_PATHS.FETCH_MOT_REFRESH, payload);
  }

  static fetchVIVehicleHistoryReports(registrationNumber) {
    const queryParams = {
      dealerId: this.getDealerId(),
    };
    return Http.get(
      URL_TYPES.CDMS,
      stringInterpolate(API_PATHS.FETCH_VI_VEHICLE_HISTORY, { registrationNumber }),
      queryParams
    );
  }

  static fetchExternalLookupData(registrationNumber) {
    const payload = { registrationNumber, providers: ['DVLA', 'CVD'] };
    const queryParams = {
      dealerId: this.getDealerId(),
    };
    return Http.post(URL_TYPES.CDMS, API_PATHS.FETCH_EXTERNAL_LOOKUP, payload, queryParams);
  }

  static fetchStockTypeChangeCostOverview(newStockType, vehicleId) {
    const payload = { updatedStockType: newStockType };
    return Http.post(
      URL_TYPES.CDMS,
      stringInterpolate(API_PATHS.FETCH_STOCK_TYPE_CHANGE_COST_OVERVIEW, { vehicleId }),
      payload
    );
  }
}
