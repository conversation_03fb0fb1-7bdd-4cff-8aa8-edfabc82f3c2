/* eslint-disable consistent-return */
/*
  eslint-disable react/sort-comp
*/
import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import cx from 'classnames';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _noop from 'lodash/noop';
import _result from 'lodash/result';
import _set from 'lodash/set';
import PropTypes from 'prop-types';
import _last from 'lodash/last';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from 'tbase/app.constants';
import { VEHICLE_TYPES } from 'tbase/constants/vehicleInventory/vehicleTypes';
import { PURCHASE_STATUS_LIST } from 'tbase/constants/vehicleInventory/vehicle';
import { tget } from 'tbase/utils/general';
import { isInchcapeOrRRG, isInchcape } from 'tbase/utils/sales/dealerProgram.utils';
import { Heading } from 'tcomponents/atoms';
import { PropertyControlledComponent } from 'tcomponents/molecules';
import StickyBanner, { BANNER_TYPE } from 'tcomponents/molecules/stickyBanner';
import ConfirmationDialog from 'tcomponents/molecules/confirmationDialog';
import Content from 'tcomponents/atoms/Content';
import Button from 'tcomponents/atoms/Button';
import Tooltip from 'tcomponents/atoms/tooltip';
import HPIVehicleHistory from 'twidgets/organisms/HPIVehicleHistory';

import { MESSAGES } from 'constants/constants';
import PROGRAM_CONFIG from 'constants/programConfig';
import {
  getVehicleAge,
  getFirstNonVoidedInvoice,
  areExistingInvoicesVoided,
  areExistingInvoicesAndConsignmentVoided,
} from 'helpers/vehicle.helper';
import { getDisplayModelSourceValue } from 'helpers/vehicleDetails.helper';
import ApplicableWarranties from 'molecules/ApplicableWarranties';
import Card from 'molecules/Card';
import AddNewInvoice from 'organisms/PurchaseInformation/components/AddNewInvoice';
import {
  getPayloadForNewInvoice,
  getPayloadFromExistingInvoice,
} from 'organisms/PurchaseInformation/purchaseInformation.factory';
import { INVOICE_TYPE } from 'organisms/PurchaseInformation/purchaseInformation.constants';
import SourceDealInfo from 'organisms/SourceDealInfo';
import ServiceDetails from 'organisms/ServiceDetails';
import SettingsReader from 'readers/settings';
import { hasAccessViPurchaseInvoicePermission } from 'permissions/inventory.permissions';

import { PREDEFINED_SECTIONS, PREDEFINED_SECTIONS_KEYS } from '../../VehicleDetails.constant';
import { getOpenDamages } from '../Damages/damages.reader';
import ApplicableWarrantiesV2 from './Components/ApplicableWarrantiesV2';
import CidDeclaration from './Components/CidDeclaration';
import PoliceNumber from './Components/PoliceNumber';
import PurchaseDetails from './Components/PurchaseDetails';
// import VehicleQualification from './Components/VehicleQualification';
import GeneralAttachment from './Components/GeneralAttachment';
import OEM from './Components/OEM';
import Overview from './Components/Overview';
import VehicleType from './Components/VehicleType';
import { GENERAL_ACTION_TYPES, GENERAL_SECTION_ID } from './general.constant';
import GeneralForm from './Components/GeneralForm';
import { getCustomSections, getGeneralSectionContainer, getStickerBannerReminders } from './General.helpers';
import SideNavigation from '../SideNavigation';

import styles from './general.module.scss';
import PurchaseStatus from './Components/PurchaseStatus';

const General = ({
  actions,
  recallDetails,
  dealerConfig,
  selectedVehicleType,
  customerViewProps,
  displayModelSource,
  userSettings,
  selectedVehicle,
  damages,
  vehicleTypes,
  certificationStatusOptions,
  form,
  currentSeletedVehicleType,
  fetchVendorInvoicesData,
  // vinDecodeVehicles,
  isEditMode,
  vendorInvoiceData,
  fields,
  sections,
  fetchPoliceRecordData,
  policeRecordData,
  policeRecordSuccessCb,
  declarationStatus,
  vehiclePOs,
  fetchDeclarationStatus,
  attachments,
  showStockTypeChangeModal,
  updatedVehicle,
  onAction,
  formState,
  fetchSetupFields,
  sourceInfo,
  getUserInfoById,
  optionsLookupEnable,
  onValidatedCb,
  fieldsWithError,
  countryOptions,
  createSingleJobRO,
  isVehicleConfiguratorEnabled,
  enableUsedVehiclesPricingReset,
  shouldHideReminders,
  validateTabsForm,
  vehicleHistory,
  fetchPoliceRecordParameters,
  isReadOnlyDetails,
  lite,
  showAddInvoiceModal,
  enterpriseV2Enabled,
  enterpriseV2Workspaces,
  isInvoiceAdditionRestricted,
  isVinDecodeLoading,
  vinValidateAcrossWorkSpace,
  removeDefaultOptions,
  enableStockNumberGenerationOnSave,
  formFieldOverrides,
  allFieldsDisabledByDefault,
}) => {
  const isVinLookupInProgressRef = useRef(false);

  // const enteredVin = _get(formState, 'vin');
  const { addAttachedFiles, removeAttachedFiles, modifyAttachmentSensitivity } = actions;
  const { oemDetails, warranties, mileageStatus } = selectedVehicle;
  const vehicleDetailsForm = formState;
  const {
    id,
    pricingDetails,
    certified,
    dealerCertified,
    make,
    model,
    displayModel,
    bestStyleName,
    year,
    certificationStatus,
    status,
    locationCode,
    stockedInAtSiteId,
    vehicleType: vehicleStockType,
    firstRegistrationDate,
    vehicleSubStatus,
    isVATQualifying,
  } = vehicleDetailsForm;
  const source = _get(vehicleDetailsForm, 'sourceInfo.source');
  const brand = _get(vehicleDetailsForm, 'trimDetails.brand');
  const trim = _get(vehicleDetailsForm, 'trimDetails.trim');
  const isImported = _get(vehicleDetailsForm, 'isImported');
  const warrantiesList = tget(vehicleDetailsForm, 'warranties', EMPTY_ARRAY);
  const bodyType = _get(vehicleDetailsForm, 'trimDetails.bodyType');
  const fuelType = _get(vehicleDetailsForm, 'trimDetails.fuelType');
  const accountPosted = _get(vehicleDetailsForm, 'accountPosted');
  const registrationNumber = _get(vehicleDetailsForm, 'licensePlateNumber');

  useEffect(() => {
    onAction({ type: GENERAL_ACTION_TYPES.ON_INIT });
  }, []);

  const setIsVinLookupInProgressRef = useCallback(value => {
    _set(isVinLookupInProgressRef, 'current', value);
  }, []);

  const onToggleCertificationStatus = selections => {
    onAction({ type: GENERAL_ACTION_TYPES.TOGGLE_CERTIFICATION_STATUS, payload: { value: selections } });
  };

  const certificationStatusProps = {
    certificationStatus,
    certificationStatusOptions,
    onToggleCertificationStatus,
  };

  const vehicleSubType = _get(vehicleDetailsForm, 'vehicleSubType') ?? _get(selectedVehicle, 'general.vehicleSubType');
  const isVehicleSubTypeMandatory = SettingsReader.isVehicleSubTypeMandatory(userSettings);
  const vehicle = { ...(selectedVehicle || EMPTY_OBJECT), vehicleDamages: getOpenDamages(damages) };
  const roDetails = _get(selectedVehicle, 'roDetails') || EMPTY_ARRAY;
  const poData = isEditMode ? vehiclePOs[id] || EMPTY_ARRAY : EMPTY_ARRAY;

  const agingField = SettingsReader.agingField(userSettings);
  const age = getVehicleAge(selectedVehicle, agingField);

  const vehicleType = useMemo(
    () => currentSeletedVehicleType ?? selectedVehicleType,
    [selectedVehicleType, currentSeletedVehicleType]
  );

  const handleVehicleTypeChange = modifiedVehicle => {
    onAction({
      type: GENERAL_ACTION_TYPES.VEHICLE_TYPE_CHANGE,
      payload: {
        updatedVehicle: modifiedVehicle,
        setIsVinLookupInProgressRef,
        isVehicleConfiguratorEnabled,
        enableUsedVehiclesPricingReset,
        vehicleDetailsForm,
      },
    });
  };

  const handleOnSubmitVehicleTypeChange = modifiedVehicle => {
    onAction({
      type: GENERAL_ACTION_TYPES.VEHICLE_TYPE_CHANGE_ON_SUMBIT,
      payload: {
        updatedVehicle: modifiedVehicle,
        setIsVinLookupInProgressRef,
        isVehicleConfiguratorEnabled,
        enableUsedVehiclesPricingReset,
      },
    });
  };

  const onVehicleTypeSubCategoryChange = vehicleTypeSubCategory => {
    onAction({
      type: GENERAL_ACTION_TYPES.VEHICLE_TYPE_SUB_CATEGORY_CHANGE,
      payload: { vehicleTypeSubCategory, isVinLookupInProgressRef },
    });
  };

  const handleToggleCertified = key => event => {
    onAction({
      type: GENERAL_ACTION_TYPES.TOGGLE_CERTIFIED,
      payload: { key, event },
    });
  };

  const handleVatQualifyingChange = value => {
    onAction({
      type: GENERAL_ACTION_TYPES.VAT_QUALIFYING_CHANGE,
      payload: value,
    });
  };

  // const handleVinDecode = () =>
  //   onAction({
  //     type: GENERAL_ACTION_TYPES.VIN_DECODE,
  //   });

  const handleVinDecodeSave = vinDetails =>
    onAction({
      type: GENERAL_ACTION_TYPES.VIN_DECODE_SAVE,
      payload: { vinDetails },
    });

  const handlePurchaseInformationPortal = useCallback(
    tabIndex => {
      onAction({
        type: GENERAL_ACTION_TYPES.OPEN_PURCHASE_MODAL,
        payload: { vendorInvoiceData, initialTabIndex: tabIndex },
      });
    },
    [onAction, vendorInvoiceData]
  );

  const handleAddPurchaseInvoiceClick = useCallback(() => {
    const shouldShowAddInvoiceModal =
      areExistingInvoicesVoided(vendorInvoiceData) && !areExistingInvoicesAndConsignmentVoided(vendorInvoiceData);
    if (_isEmpty(vendorInvoiceData) || !shouldShowAddInvoiceModal) {
      onAction({ type: GENERAL_ACTION_TYPES.SHOW_ADD_INVOICE_MODAL });
    } else {
      const firstNonVoidedInvoice = getFirstNonVoidedInvoice(vendorInvoiceData);
      const updatedAdditionalPayload = getPayloadFromExistingInvoice(firstNonVoidedInvoice);
      const payload = getPayloadForNewInvoice(INVOICE_TYPE.INVOICE, updatedAdditionalPayload);
      onAction({
        type: GENERAL_ACTION_TYPES.OPEN_PURCHASE_MODAL,
        payload: { addNewInvoicePayload: payload, vendorInvoiceData },
      });
    }
  }, [onAction, vendorInvoiceData]);

  const handleAddInvoiceHideModal = useCallback(() => {
    onAction({ type: GENERAL_ACTION_TYPES.HIDE_ADD_INVOICE_MODAL });
  }, [onAction]);

  const handleAddInvoiceModalSubmit = useCallback(
    (invoiceDataPayload, selectedInvoiceType) => {
      onAction({
        type: GENERAL_ACTION_TYPES.OPEN_PURCHASE_MODAL,
        payload: { addNewInvoicePayload: invoiceDataPayload, selectedInvoiceType },
      });
    },
    [onAction]
  );

  const handleStockTypeModalClose = () => {
    onAction({
      type: GENERAL_ACTION_TYPES.CLOSE_STOCK_TYPE_MODAL,
    });
  };

  const handleVinFieldChange = (vin, vinValidateData) => {
    onAction({ type: GENERAL_ACTION_TYPES.VIN_CHANGE, payload: { vin, vinValidateData } });
  };

  const getStockID = payload => onAction({ type: GENERAL_ACTION_TYPES.GET_STOCK_ID, payload });

  const handleLabelPrint = () => {
    onAction({ type: GENERAL_ACTION_TYPES.LABEL_PRINT });
  };

  const isVehicleTypeUsed = useMemo(() => {
    if (!isEditMode && vehicleType === VEHICLE_TYPES.USED) return true;
    return vehicleType === VEHICLE_TYPES.USED && selectedVehicleType === VEHICLE_TYPES.USED;
  }, [isEditMode, vehicleType, selectedVehicleType]);

  const shouldRenderPoliceRecord = useMemo(
    () => (PROGRAM_CONFIG.shouldShowPoliceRecord() ? isVehicleTypeUsed : false),
    [isVehicleTypeUsed]
  );

  const shouldRenderCidDeclaration = useMemo(
    () => (PROGRAM_CONFIG.shouldShowCidDeclaration() ? isVehicleTypeUsed : false),
    [isVehicleTypeUsed]
  );

  const shouldRenderOEM = !_isEmpty(oemDetails);

  const customSections = useMemo(
    () =>
      getCustomSections(
        {
          shouldRenderPoliceRecord,
          shouldRenderCidDeclaration,
          sourceInfo,
          shouldRenderOEM,
        },
        lite
      ),
    [shouldRenderPoliceRecord, shouldRenderCidDeclaration, sourceInfo, shouldRenderOEM, lite]
  );

  const selectedDisplayModelSource = useMemo(
    () => getDisplayModelSourceValue(displayModelSource, bestStyleName, displayModel, model),
    [displayModelSource, bestStyleName, displayModel, model]
  );

  const renderStockTypeChangeConfirmationModal = () => (
    <ConfirmationDialog
      title={__('Confirm Stock Type Change')}
      isVisible={showStockTypeChangeModal}
      content={MESSAGES.STOCK_TYPE_CHANGE_MESSAGE}
      onCancel={handleStockTypeModalClose}
      onSubmit={() => {
        handleOnSubmitVehicleTypeChange(updatedVehicle);
        handleStockTypeModalClose();
      }}
      submitBtnText={__('Confirm')}
      secondaryBtnText={__('Cancel')}
    />
  );

  const renderAddNewInvoiceModal = useCallback(
    () => (
      <AddNewInvoice
        isVisible={showAddInvoiceModal}
        hideModal={handleAddInvoiceHideModal}
        handleSubmit={handleAddInvoiceModalSubmit}
        invoiceData={vendorInvoiceData}
        areExistingInvoicesVoided={areExistingInvoicesVoided(vendorInvoiceData)}
        areExistingInvoicesAndConsignmentVoided={areExistingInvoicesAndConsignmentVoided(vendorInvoiceData)}
        vehicleStockType={vehicleStockType}
      />
    ),
    [handleAddInvoiceHideModal, handleAddInvoiceModalSubmit, showAddInvoiceModal, vendorInvoiceData, vehicleStockType]
  );

  const renderSectionTitle = useCallback(
    sectionKey => <Heading size={2}>{_result(PREDEFINED_SECTIONS, [sectionKey, 'title'])}</Heading>,
    []
  );

  const isTransferredVehicle = useMemo(
    () => tget(vehicleDetailsForm, 'inventoryTransferDetails.metadata.isTransferred', false),
    [vehicleDetailsForm]
  );

  const handlePurchaseStatusChange = useCallback(
    value => {
      onAction({ type: GENERAL_ACTION_TYPES.ON_PURCHASE_STATUS_CHANGE, payload: { value } });
    },
    [onAction]
  );

  const addInvoiceTooltipTitle = useMemo(
    () =>
      !hasAccessViPurchaseInvoicePermission()
        ? __("You're not authorized to add/view purchase invoice(s)")
        : EMPTY_STRING,
    []
  );

  const renderPurchaseDetailSectionTitle = useCallback(
    sectionKey => {
      const isNotStarted = _isEmpty(vendorInvoiceData);
      const vehicleSourceDealNo = vehicleDetailsForm?.sourceInfo?.sourceDealNumber;
      const disabled =
        isTransferredVehicle ||
        isInvoiceAdditionRestricted ||
        !hasAccessViPurchaseInvoicePermission() ||
        (!isNotStarted && !accountPosted) ||
        (!isNotStarted && !_last(vendorInvoiceData)?.accountingStatus) ||
        !!vehicleSourceDealNo;

      const purchaseStatus = tget(vehicleDetailsForm, 'purchaseStatus', PURCHASE_STATUS_LIST.TO_START);
      return (
        <div className="flex justify-content-between">
          <div className="flex align-items-center m-r-16">
            <Heading size={2} className="m-r-12">
              {_result(PREDEFINED_SECTIONS, [sectionKey, 'title'])}
            </Heading>
            <PurchaseStatus
              purchaseStatus={purchaseStatus}
              handlePurchaseStatusChange={handlePurchaseStatusChange}
              isTransferredVehicle={isTransferredVehicle}
              vehicleStatus={status}
              vehicleDetailsForm={vehicleDetailsForm}
            />
          </div>
          <Tooltip title={addInvoiceTooltipTitle} placement="topRight">
            <Button onClick={handleAddPurchaseInvoiceClick} view={Button.VIEW.SECONDARY} disabled={disabled}>
              {__('Add')}
            </Button>
          </Tooltip>
        </div>
      );
    },
    [
      isTransferredVehicle,
      status,
      vendorInvoiceData,
      handleAddPurchaseInvoiceClick,
      vehicleDetailsForm,
      addInvoiceTooltipTitle,
      accountPosted,
      isInvoiceAdditionRestricted,
    ]
  );

  const transferSource = useMemo(
    () => tget(vehicleDetailsForm, 'inventoryTransferDetails.sourceDealer', false),
    [vehicleDetailsForm]
  );

  const reminders = useMemo(
    () =>
      isTransferredVehicle ? EMPTY_ARRAY : getStickerBannerReminders({ vendorInvoiceData, accountPosted, isEditMode }),
    [vendorInvoiceData, accountPosted, isEditMode, isTransferredVehicle]
  );

  const handleDismissReminders = useCallback(() => {
    onAction({ type: GENERAL_ACTION_TYPES.DISMISS_REMINDERS });
  }, [onAction]);

  const InactiveSectionPlaceHolder = (
    <Content className="text-center">
      {__('This section becomes active only after the vehicle record is created.')}
    </Content>
  );

  const shouldShowVATQualifying = useMemo(() => lite && isInchcape(), [lite]);

  return (
    <div className="d-flex full-height">
      <SideNavigation
        systemSections={sections}
        customSectionConfig={customSections}
        fieldsWithError={fieldsWithError}
        getContainer={getGeneralSectionContainer}
        offsetTop={20}
        className={cx({
          opacity50: isReadOnlyDetails,
        })}
      />

      <div className={`d-flex flex-column full-height ${styles.rightBodyContainer}`}>
        <PropertyControlledComponent controllerProperty={!_isEmpty(reminders) && !shouldHideReminders}>
          <StickyBanner
            reminders={reminders}
            bannerType={BANNER_TYPE.INFO}
            bannerStyle={styles.stickyBannerStyle}
            onClose={handleDismissReminders}
            shouldAutoAdjustWidthOfLeftBanner
          />
        </PropertyControlledComponent>
        <div
          id={GENERAL_SECTION_ID}
          className={cx('overflow-y-auto full-height', {
            [styles.readOnlyContainer]: isReadOnlyDetails,
          })}>
          <div className={cx('p-24', styles.generalSectionsContainer)}>
            <PropertyControlledComponent controllerProperty={!lite}>
              <Card
                id={PREDEFINED_SECTIONS_KEYS.OVERVIEW}
                title={renderSectionTitle(PREDEFINED_SECTIONS_KEYS.OVERVIEW)}
                isReadOnly={isReadOnlyDetails}>
                <Overview
                  actions={actions}
                  recalls={recallDetails}
                  titleBlock={dealerConfig.titleBlock}
                  age={age}
                  pricingDetails={pricingDetails}
                  vehicleType={vehicleType}
                  selectedVehicle={vehicle}
                  displayModelSource={displayModelSource}
                  customerViewProps={customerViewProps}
                  createSingleJobRO={createSingleJobRO}
                />
              </Card>
            </PropertyControlledComponent>

            <Card
              id={PREDEFINED_SECTIONS_KEYS.VEHICLE_TYPE}
              title={renderSectionTitle(PREDEFINED_SECTIONS_KEYS.VEHICLE_TYPE)}
              isReadOnly={isReadOnlyDetails}>
              <VehicleType
                selectedVehicleType={vehicleType}
                selectedVehicleSubType={vehicleSubType}
                certified={certified}
                dealerCertified={dealerCertified}
                onVehicleTypeChange={handleVehicleTypeChange}
                onVehicleTypeSubCategoryChange={onVehicleTypeSubCategoryChange}
                onToggleCertified={handleToggleCertified}
                options={vehicleTypes}
                isVehicleSubTypeMandatory={isVehicleSubTypeMandatory}
                certificationStatusProps={certificationStatusProps}
                isEditMode={isEditMode}
                shouldShowVATQualifying={shouldShowVATQualifying}
                onVatQualifyingChange={handleVatQualifyingChange}
                isVATQualifying={isVATQualifying}
                vehicleStatus={status}
              />
            </Card>
            {/* <PropertyControlledComponent controllerProperty={PROGRAM_CONFIG.shouldShowVehicleQualification()}>
              <Card
                id={PREDEFINED_SECTIONS_KEYS.VEHICLE_QUALIFICATION}
                title={renderSectionTitle(PREDEFINED_SECTIONS_KEYS.VEHICLE_QUALIFICATION)}
              >
                <VehicleQualification
                  vin={enteredVin}
                  displayModelSource={displayModelSource}
                  updateFieldState={handleUpdateFieldState}
                  onVinDecode={handleVinDecode}
                  vinDecodeVehicles={vinDecodeVehicles}
                  onVinDecodeSave={handleVinDecodeSave}
                />
              </Card>
            </PropertyControlledComponent> */}

            <GeneralForm
              vehicleStockType={vehicleStockType}
              sectionDetails={sections}
              fields={fields}
              onVinFieldChange={handleVinFieldChange}
              formData={{
                selectedMake: make,
                selectedModel: selectedDisplayModelSource,
                selectedYear: year,
                selectedBrand: brand,
                source,
                mileageStatus,
              }}
              formKeys={[
                locationCode,
                make,
                selectedDisplayModelSource,
                source,
                stockedInAtSiteId,
                currentSeletedVehicleType,
                mileageStatus,
                year,
                brand,
                trim,
                isImported,
                bodyType,
                firstRegistrationDate,
                fuelType,
              ]}
              isEditMode={isEditMode}
              enterpriseV2Enabled={enterpriseV2Enabled}
              enterpriseV2Workspaces={enterpriseV2Workspaces}
              getUserInfoById={getUserInfoById}
              fetchSetupFields={fetchSetupFields}
              optionsLookupEnable={optionsLookupEnable}
              getStockID={getStockID}
              onPrint={handleLabelPrint}
              displayModelSource={displayModelSource}
              customerViewProps={customerViewProps}
              onValidatedCb={onValidatedCb}
              countryOptions={countryOptions}
              onVinDecodeSave={handleVinDecodeSave}
              enableUsedVehiclesPricingReset={enableUsedVehiclesPricingReset}
              isVehicleConfiguratorEnabled={isVehicleConfiguratorEnabled}
              validateTabsForm={validateTabsForm}
              lite={lite}
              vehicleStatus={status}
              isVinDecodeLoading={isVinDecodeLoading}
              vinValidateAcrossWorkSpace={vinValidateAcrossWorkSpace}
              removeDefaultOptions={removeDefaultOptions}
              enableStockNumberGenerationOnSave={enableStockNumberGenerationOnSave}
              formFieldOverrides={formFieldOverrides}
              allFieldsDisabledByDefault={allFieldsDisabledByDefault}
              isReadOnly={isReadOnlyDetails}
            />

            <PropertyControlledComponent controllerProperty={!lite}>
              <PropertyControlledComponent controllerProperty={PROGRAM_CONFIG.shouldShowPurchaseInvoice()}>
                <Card
                  id={PREDEFINED_SECTIONS_KEYS.PURCHASE_DETAILS}
                  title={renderPurchaseDetailSectionTitle(PREDEFINED_SECTIONS_KEYS.PURCHASE_DETAILS)}
                  isReadOnly={isReadOnlyDetails}>
                  <PurchaseDetails
                    vehicleId={id}
                    isEditMode={isEditMode}
                    fetchVendorInvoicesData={fetchVendorInvoicesData}
                    openPortal={handlePurchaseInformationPortal}
                    invoiceData={vendorInvoiceData}
                    vehicleStatus={status}
                    isTransferredVehicle={isTransferredVehicle}
                    transferSource={transferSource}
                  />
                </Card>
              </PropertyControlledComponent>

              <PropertyControlledComponent controllerProperty={shouldRenderPoliceRecord}>
                <PoliceNumber
                  isEditMode={isEditMode}
                  vehicleDetails={vehicleDetailsForm}
                  fetchPoliceRecordData={fetchPoliceRecordData}
                  policeRecordData={policeRecordData}
                  policeRecordSuccessCb={policeRecordSuccessCb}
                  countryOptions={countryOptions}
                  fetchPoliceRecordParameters={fetchPoliceRecordParameters}
                />
              </PropertyControlledComponent>

              <PropertyControlledComponent controllerProperty={shouldRenderCidDeclaration}>
                <Card
                  id={PREDEFINED_SECTIONS_KEYS.CID_DECLARATION}
                  title={renderSectionTitle(PREDEFINED_SECTIONS_KEYS.CID_DECLARATION)}
                  isReadOnly={isReadOnlyDetails}>
                  <CidDeclaration
                    isEditMode={isEditMode}
                    vehicleId={id}
                    vehicleStatus={status}
                    vehicleSubStatus={vehicleSubStatus}
                    declarationStatus={declarationStatus}
                    fetchDeclarationStatus={fetchDeclarationStatus}
                  />
                </Card>
              </PropertyControlledComponent>

              <PropertyControlledComponent
                controllerProperty={!isInchcapeOrRRG() && !_isEmpty(sourceInfo?.sourceDealNumber)}>
                <Card
                  id={PREDEFINED_SECTIONS_KEYS.SOURCE_DEAL_INFO}
                  title={renderSectionTitle(PREDEFINED_SECTIONS_KEYS.SOURCE_DEAL_INFO)}
                  isReadOnly={isReadOnlyDetails}>
                  <SourceDealInfo />
                </Card>
              </PropertyControlledComponent>

              <PropertyControlledComponent controllerProperty={shouldRenderOEM}>
                <Card
                  id={PREDEFINED_SECTIONS_KEYS.OEM}
                  title={renderSectionTitle(PREDEFINED_SECTIONS_KEYS.OEM)}
                  isReadOnly={isReadOnlyDetails}>
                  <OEM oemdetails={oemDetails} />
                </Card>
              </PropertyControlledComponent>

              <PropertyControlledComponent
                controllerProperty={
                  !PROGRAM_CONFIG.shouldHideRoPoDetails() && !PROGRAM_CONFIG.shouldShowCostApproval()
                }>
                <ServiceDetails
                  roDetails={roDetails}
                  poData={poData}
                  vehicle={vehicleDetailsForm}
                  form={form}
                  predefinedSections={PREDEFINED_SECTIONS}
                  predefinedSectionKeys={PREDEFINED_SECTIONS_KEYS}
                  isReadOnly={isReadOnlyDetails}
                />
              </PropertyControlledComponent>

              <Card
                id={PREDEFINED_SECTIONS_KEYS.APPLICABLE_WARRANTIES}
                title={renderSectionTitle(PREDEFINED_SECTIONS_KEYS.APPLICABLE_WARRANTIES)}
                isReadOnly={isReadOnlyDetails}>
                <PropertyControlledComponent
                  controllerProperty={!PROGRAM_CONFIG.shouldShowWarrantyConfiguration()}
                  fallback={<ApplicableWarrantiesV2 form={form} warrantiesList={warrantiesList} />}>
                  <ApplicableWarranties warranties={warranties || EMPTY_ARRAY} />
                </PropertyControlledComponent>
              </Card>

              <PropertyControlledComponent controllerProperty={PROGRAM_CONFIG.shouldShowVehicleHistory()}>
                <Card
                  id={PREDEFINED_SECTIONS_KEYS.VEHICLE_HISTORY}
                  title={renderSectionTitle(PREDEFINED_SECTIONS_KEYS.VEHICLE_HISTORY)}
                  isReadOnly={isReadOnlyDetails}>
                  <PropertyControlledComponent controllerProperty={isEditMode} fallback={InactiveSectionPlaceHolder}>
                    <HPIVehicleHistory
                      onVehicleDetailsAction={onAction}
                      registrationNumber={registrationNumber}
                      tradeInId={registrationNumber}
                      isVIMode
                      vehicleHistory={vehicleHistory}
                    />
                  </PropertyControlledComponent>
                </Card>
              </PropertyControlledComponent>

              <PropertyControlledComponent
                controllerProperty={
                  !PROGRAM_CONFIG.shouldHideUploadMedia() && !PROGRAM_CONFIG.shouldShowDocumentsSection()
                }>
                <Card
                  id={PREDEFINED_SECTIONS_KEYS.GENERAL_ATTACHMENT}
                  title={renderSectionTitle(PREDEFINED_SECTIONS_KEYS.GENERAL_ATTACHMENT)}>
                  <GeneralAttachment
                    addAttachedFiles={addAttachedFiles}
                    removeAttachedFiles={removeAttachedFiles}
                    modifyAttachmentSensitivity={modifyAttachmentSensitivity}
                    attachments={attachments}
                    isReadOnly={isReadOnlyDetails}
                  />
                </Card>
              </PropertyControlledComponent>
            </PropertyControlledComponent>
            {renderStockTypeChangeConfirmationModal()}
            {renderAddNewInvoiceModal()}
          </div>
        </div>
      </div>
    </div>
  );
};

General.propTypes = {
  actions: PropTypes.object,
  dealerConfig: PropTypes.object,
  selectedVehicle: PropTypes.object,
  fetchSetupFields: PropTypes.func,
  form: PropTypes.object,
  isEditMode: PropTypes.bool,
  attachments: PropTypes.array,
  fields: PropTypes.array,
  vehicleTypes: PropTypes.array,
  selectedVehicleType: PropTypes.string,
  recallDetails: PropTypes.array,
  vehiclePOs: PropTypes.object,
  optionsLookupEnable: PropTypes.bool,
  userSettings: PropTypes.object,
  fetchPoliceRecordData: PropTypes.func,
  policeRecordData: PropTypes.object,
  policeRecordSuccessCb: PropTypes.func,
  customerViewProps: PropTypes.object,
  fetchVendorInvoicesData: PropTypes.func,
  vendorInvoiceData: PropTypes.array,
  certificationStatusOptions: PropTypes.array,
  declarationStatus: PropTypes.object,
  fetchDeclarationStatus: PropTypes.func,
  isVehicleConfiguratorEnabled: PropTypes.bool,
  enableUsedVehiclesPricingReset: PropTypes.bool,
  createSingleJobRO: PropTypes.func,
  shouldHideReminders: PropTypes.bool,
  fieldsWithError: PropTypes.array,
  validateTabsForm: PropTypes.func,
  vehicleHistory: PropTypes.array,
  fetchPoliceRecordParameters: PropTypes.func,
  isReadOnlyDetails: PropTypes.bool.isRequired,
  showAddInvoiceModal: PropTypes.bool,
  enterpriseV2Enabled: PropTypes.bool,
  isInvoiceAdditionRestricted: PropTypes.bool.isRequired,
  isVinDecodeLoading: PropTypes.bool,
  removeDefaultOptions: PropTypes.bool,
  enableStockNumberGenerationOnSave: PropTypes.bool,
  formFieldOverrides: PropTypes.object,
  allFieldsDisabledByDefault: PropTypes.bool,
};

General.defaultProps = {
  actions: EMPTY_OBJECT,
  dealerConfig: EMPTY_ARRAY,
  selectedVehicle: EMPTY_ARRAY,
  fetchSetupFields: _noop,
  form: EMPTY_OBJECT,
  isEditMode: false,
  attachments: EMPTY_ARRAY,
  fields: EMPTY_ARRAY,
  vehicleTypes: EMPTY_ARRAY,
  selectedVehicleType: '',
  recallDetails: EMPTY_ARRAY,
  vehiclePOs: EMPTY_OBJECT,
  optionsLookupEnable: false,
  userSettings: EMPTY_OBJECT,
  fetchPoliceRecordData: _noop,
  policeRecordData: EMPTY_OBJECT,
  policeRecordSuccessCb: _noop,
  customerViewProps: EMPTY_OBJECT,
  fetchVendorInvoicesData: _noop,
  vendorInvoiceData: EMPTY_ARRAY,
  certificationStatusOptions: EMPTY_ARRAY,
  declarationStatus: EMPTY_OBJECT,
  fetchDeclarationStatus: _noop,
  isVehicleConfiguratorEnabled: false,
  enableUsedVehiclesPricingReset: false,
  createSingleJobRO: _noop,
  shouldHideReminders: false,
  fieldsWithError: EMPTY_ARRAY,
  validateTabsForm: _noop,
  vehicleHistory: EMPTY_ARRAY,
  fetchPoliceRecordParameters: _noop,
  showAddInvoiceModal: false,
  enterpriseV2Enabled: false,
  isVinDecodeLoading: false,
  removeDefaultOptions: false,
  enableStockNumberGenerationOnSave: false,
  formFieldOverrides: EMPTY_OBJECT,
  allFieldsDisabledByDefault: false,
};

export default General;
