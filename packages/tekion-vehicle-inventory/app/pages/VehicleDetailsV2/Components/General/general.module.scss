@use "tstyles/component.scss";
@use "styles/mixins.scss" as viMixins;

.overview {
  height: 11.8rem;
  border: 0.05rem solid component.$platinum;
  @include component.flex();
  > div.block {
    position: relative;
    text-align: center;
    flex: 1 1 0;
    @include component.flex(column nowrap, center, center);
  }
  > div.block:not(:last-child)::after {
    content: "";
    position: absolute;
    right: -2px;
    top: 25%;
    width: 1px;
    height: 50%;
    background-color: component.$platinum;
  }
  .block .stats {
    font-size: 3.6rem;
    line-height: 4.3rem;
    display: flex;
    align-self: flex-start;
  }
  .block {
    @include component.flex(column nowrap);
    font-weight: bold;
    letter-spacing: 0.1rem;
    font-size: component.$font-size-large;
    > div.statsType {
      @include component.flex();
      align-items: center;
      letter-spacing: 0;
      > span.statsText {
        margin-right: 3rem;
      }
    }
  }
}

div.badgeGap {
  padding-right: 2rem;
  padding-top: 0.02rem;
}

.vehicleType {
  @include component.flex();
}

.checkbox {
  margin-top: 2.5rem;

  :global(.ant-checkbox + span) {
    padding-right: 0;
  }
}

.iconPopoverClassName {
  :global(.ant-tooltip-inner) {
    background-color: component.$white;
    color: component.$black;
    min-width: 9rem;
    padding-left: 2.2rem;
  }

  :global(.ant-tooltip-arrow) {
    border-top-color: component.$white;
  }
}

:export {
  onSelectColor: component.$denim;
  defaultColor: component.$gunMetal;
}

.generalAttachmentTable {
  .tableContainer {
    margin-top: 2.4rem;
    .statusColumn {
      @include component.flex(row nowrap, space-between);
      .statusContent {
        @include component.flex();
      }
    }
  }
}

// print preview styles override

.generalContainer .printPreviewContainer {
  width: 44%;
  @include component.full-height;
  font-size: component.$font-size-medium;
}

.generalContainer .priceTabRightSideContainer {
  display: flex;
}

.roBadgeOrange {
  background-color: component.$carrotOrange !important;
}

.roBadgeRed {
  background-color: component.$mordantRed !important;
}

.vinEditPopoverContainer {
  max-width: 18rem;

  .vinEditIconPopoverContent {
    padding: 0.8rem 1.6rem 1rem 1.6rem;
  }
}

.iconHover {
  &:hover {
    color: blue;
  }
}

.warning {
  @include component.size(2.4);
  @include component.color(white);
  padding: 0.4rem 0.2rem;
  margin-right: 1.2rem;
  background-color: component.$gold;
}

.exteriorColorPopoverContent {
  padding: 1.2rem;

  .baseColor {
    margin-left: 1.1rem;
  }
}

.requiredLabel {
  &::after {
    content: "*";
    font-size: component.$font-size-large;
    color: component.$error-color;
  }
}

.trimField {
  @include component.full-height;
  @include component.is-paddingless;
  > div {
    @include component.full-height;
  }
}

.disabled {
  pointer-events: none;
  opacity: 0.5;
}

.rightBodyContainer {
  width: calc(100% - 30rem);
  background-color: component.$glitter;
}

.generalSectionsContainer {
  @include component.flex(column);
  gap: 2.4rem;
  :global(.ant-card-head) {
    @include viMixins.stickyCardHeader($zIndex: 2);
  }
}

.stickyBannerStyle {
  border-top: none;
}

.stockTypePopoverInfo {
  padding: 0.5rem 0.7rem;
  border-radius: 8rem;
}

.readOnlyContainer {
  pointer-events: auto;
}

.multiLingualInput {
  :global(.ant-input-affix-wrapper .ant-input-suffix) {
    z-index: 1 !important;
  }
}