/* eslint-disable consistent-return */
/*
  eslint-disable react/sort-comp
*/
import React, { PureComponent } from 'react';
import cx from 'classnames';
import PropTypes from 'prop-types';
import produce from 'immer';
import _debounce from 'lodash/debounce';
import _filter from 'lodash/filter';
import _forEach from 'lodash/forEach';
import _get from 'lodash/get';
import _isArray from 'lodash/isArray';
import _isEmpty from 'lodash/isEmpty';
import _isString from 'lodash/isString';
import _noop from 'lodash/noop';
import _size from 'lodash/size';
import _toUpper from 'lodash/toUpper';
import _trim from 'lodash/trim';
import _some from 'lodash/some';
import _find from 'lodash/find';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from 'tbase/app.constants';
import { getErrorMessage } from 'tbase/utils/errorUtils';
import { VEHICLE_TYPES } from 'tbase/constants/vehicleInventory/vehicleTypes';
import { checkIfVINContainsInvalidCharacters, getDisplayModalText } from 'tbase/helpers/vehicle.helper';
import isStringEmpty from 'tbase/utils/isStringEmpty';
import { tget } from 'tbase/utils/general';
import { isInchcapeOrRRG, isRRG } from 'tbase/utils/sales/dealerProgram.utils';
import VehicleReader from 'tbusiness/appServices/vehicleInventory/readers/vehicle';
import ConfirmationDialog from 'tcomponents/molecules/confirmationDialog';
import PropertyControlledComponent from 'tcomponents/molecules/PropertyControlledComponent';
import { toaster, TOASTER_TYPE } from 'tcomponents/organisms/NotificationWrapper';
import dealerPropertyHelper from 'tcomponents/helpers/sales/dealerPropertyHelper';
import withMultiLingualInfo from 'twidgets/hocs/withMultiLingualInfo';

import { VEHICLE_TYPE } from 'constants/constantEnum';
import { MESSAGES, BODY_TYPE } from 'constants/constants';
import { GENERAL_FORM_FIELDS } from 'constants/formFields';
import PROGRAM_CONFIG from 'constants/programConfig';
import { showPortal, hidePortal } from 'event';
import { getAdditionalCostAPIPayload } from 'factories/vehicle';
import { isCVDVinDecodingSource } from 'helpers/common';
import { getVehicleAge, isRVDealerEnabled, getSourceOptions } from 'helpers/vehicle.helper';
import { getDisplayModelSourceValue } from 'helpers/vehicleDetails.helper';
import ApplicableWarranties from 'molecules/ApplicableWarranties';
import PurchaseInformation from 'organisms/PurchaseInformation';
import SourceDealInfo from 'organisms/SourceDealInfo';
import { getOpenDamages } from 'pages/VehicleDetails/Components/Damages/damages.reader';
import { hasStockIdEdit } from 'permissions/inventory.permissions';
import SettingsReader from 'readers/settings';
import { parsePriceFieldValue } from 'utils/fieldFormatter';
import { checkIfVinExists } from 'utils/validations';

import ApplicableWarrantiesV2 from './Components/ApplicableWarrantiesV2';
import PoliceNumber from './Components/PoliceNumber';
import PurchaseDetails from './Components/PurchaseDetails/PurchaseDetails';
import CidDeclaration from './Components/CidDeclaration/CidDeclaration';
import VehicleQualification from './Components/VehicleQualification';
import ServiceDetails from './Components/ServiceDetails';
import { generateStockId } from './general.actions';
import { DEFAULT_VEHICLE_SOURCE_BASED_ON_VEHICLE_TYPE, FUEL_TYPE } from './general.constant';
import { createPrintPayload, resetPricingDetails, RESET_CHROME_OPTIONS_PRICING_DETAILS } from './general.reader';
import GeneralAttachment from './GeneralAttachment';
import GeneralForm from './GeneralForm';
import OEM from './OEM';
import Overview from './Overview';
import VehicleType from './VehicleType';
import { showVINConfirmationModal } from '../VINConfirmationModal';
import VehicleDetailsAPI from '../../vehicleDetails.api';
import {
  getVISPricing,
  confirmStockTypeChange,
  filterAdditionalCostDataForProgram,
} from '../../VehicleDetails.helpers';
import { getRecallsList } from '../../VehicleDetails.reader';
import { parseValuesFromVin } from '../../VehicleDetails.factory';

import styles from './general.module.scss';

class General extends PureComponent {
  static propTypes = {
    actions: PropTypes.object,
    dealerConfig: PropTypes.object,
    selectedVehicle: PropTypes.object,
    fetchSetupFields: PropTypes.func,
    form: PropTypes.object,
    isEditMode: PropTypes.bool,
    attachments: PropTypes.array,
    fields: PropTypes.array,
    vehicleTypes: PropTypes.array,
    selectedVehicleType: PropTypes.string,
    getRecallsByVin: PropTypes.func.isRequired,
    updateRecallsInVehicleDetails: PropTypes.func.isRequired,
    customFields: PropTypes.array,
    recallDetails: PropTypes.array,
    setLookupFailureForVehicleSave: PropTypes.func,
    vehiclePOs: PropTypes.object,
    optionsLookupEnable: PropTypes.bool,
    userSettings: PropTypes.object,
    fetchPoliceRecordData: PropTypes.func,
    policeRecordData: PropTypes.object,
    policeRecordSuccessCb: PropTypes.func,
    customerViewProps: PropTypes.object,
    fetchVendorInvoicesData: PropTypes.func,
    vendorInvoiceData: PropTypes.array,
    certificationStatusOptions: PropTypes.array,
    declarationStatus: PropTypes.object,
    fetchDeclarationStatus: PropTypes.func,
    isVehicleConfiguratorEnabled: PropTypes.bool,
    enableUsedVehiclesPricingReset: PropTypes.bool,
    enterpriseV2Enabled: PropTypes.bool,
    enableStockNumberGenerationOnSave: PropTypes.bool,
    isReadOnlyDetails: PropTypes.bool,
  };

  static defaultProps = {
    actions: EMPTY_OBJECT,
    dealerConfig: EMPTY_ARRAY,
    selectedVehicle: EMPTY_ARRAY,
    fetchSetupFields: _noop,
    form: EMPTY_OBJECT,
    isEditMode: false,
    attachments: EMPTY_ARRAY,
    fields: EMPTY_ARRAY,
    vehicleTypes: EMPTY_ARRAY,
    selectedVehicleType: '',
    customFields: EMPTY_ARRAY,
    recallDetails: EMPTY_ARRAY,
    vehiclePOs: EMPTY_OBJECT,
    setLookupFailureForVehicleSave: _noop,
    optionsLookupEnable: false,
    userSettings: EMPTY_OBJECT,
    fetchPoliceRecordData: _noop,
    policeRecordData: EMPTY_OBJECT,
    policeRecordSuccessCb: _noop,
    customerViewProps: EMPTY_OBJECT,
    fetchVendorInvoicesData: _noop,
    vendorInvoiceData: EMPTY_ARRAY,
    certificationStatusOptions: EMPTY_ARRAY,
    declarationStatus: EMPTY_OBJECT,
    fetchDeclarationStatus: _noop,
    isVehicleConfiguratorEnabled: false,
    enableUsedVehiclesPricingReset: false,
    enterpriseV2Enabled: false,
    enableStockNumberGenerationOnSave: false,
    isReadOnlyDetails: false,
  };

  constructor(props) {
    super(props);
    this.state = {
      fieldStatus: {
        vin: {
          onBlurCb: event => this.onVinFieldChange(event),
        },
      },
      showStockTypeChangeModal: false,
      updatedVehicle: EMPTY_OBJECT,
    };
    this.isVinLookupInProgressRef = React.createRef(false);
    this.debouncedAPICallOnVehicleTypeChange = _debounce(this.handleAPICallsOnVehicleTypeChange, 400);
  }

  componentDidMount() {
    const { form } = this.props;
    this.setState(
      produce(draft => {
        draft.fieldStatus.stockID = { disabled: !hasStockIdEdit() || dealerPropertyHelper.isViArcLiteEnabled() };
      }),
      () => {
        this.vin = VehicleReader.vin(_get(form.getState(), 'values'));
      }
    );
  }

  componentDidUpdate(props) {
    const { selectedVehicleType, form } = this.props;
    if (selectedVehicleType !== props.selectedVehicleType) {
      // this.onVehicleTypeChange({ value: selectedVehicleType });
      form.change('vehicleType', selectedVehicleType);
    }
  }

  onVinFieldChangeRRG = async event => {
    const { isMandatoryPopupDispatched } = this;
    const { form } = this.props;
    this.setState({ vinDecodeVehicles: EMPTY_ARRAY });
    const vin = event.target.value;
    const vehicle = _get(form.getState(), 'values', EMPTY_OBJECT);
    const vehicleType = VehicleReader.vehicleType(vehicle);
    const formState = _get(form.getState(), 'values', EMPTY_OBJECT);
    this.handleVinValidationFailureCases({ vin, formState });

    if (this.vin === vin) {
      return;
    }
    this.vin = vin;
    if (!vehicleType && !isMandatoryPopupDispatched) {
      toaster('info', MESSAGES.VEHICLE_TYPE_MANDATORY);
      this.isMandatoryPopupDispatched = true;
    }
  };

  onVinDecode = async () => {
    const { vin } = this;
    const { vinValidateAcrossWorkSpace } = this.props;
    if (!(await checkIfVinExists({ vin, vinValidateAcrossWorkSpace }))) {
      const vinDetails = await this.getVinDetails(vin, false);
      this.setState({ vinDecodeVehicles: vinDetails });
      return vinDetails;
    }
  };

  onVinDecodeSave = async vinDetails => {
    const { vin } = this;
    const { form } = this.props;
    this.setDefaultAdditionalCosts(vinDetails);
    this.setRecalls(vin);
    this.addBulkOptions(vinDetails?.options);
    const existingStockId = _get(form.getState(), 'values.stockID');
    if (!_isEmpty(vinDetails) && _isEmpty(existingStockId)) {
      await this.getStockID();
    }
  };

  onVinFieldChange = async event => {
    const rvVehiclesEnabled = isRVDealerEnabled();
    if (rvVehiclesEnabled) {
      return;
    }
    if (PROGRAM_CONFIG.shouldDecodeVin()) return this.onVinFieldChangeRRG(event);
    const { form, isEditMode, actions, currentLanguageId, vinValidateAcrossWorkSpace } = this.props;
    const vin = event.target.value;
    const vehicle = _get(form.getState(), 'values', {});
    const vehicleType = VehicleReader.vehicleType(vehicle);

    if (this.vin === vin) {
      return;
    }
    if (isEditMode) {
      const vinDetails = await actions.fetchVINDetails(vin, currentLanguageId, false);
      const shouldCallDependentAPIs = this.handleVinValidationFailureCases({ vin, formState: vehicle, vinDetails });
      if (!shouldCallDependentAPIs) {
        return;
      }
    }
    this.vin = vin;
    if (!vehicleType) {
      const { isMandatoryPopupDispatched } = this;
      if (!isMandatoryPopupDispatched) {
        toaster('info', MESSAGES.VEHICLE_TYPE_MANDATORY);
        this.isMandatoryPopupDispatched = true;
      }
      return;
    }
    if (!(await checkIfVinExists({ vin, vinValidateAcrossWorkSpace }))) {
      this.updateFieldState({ modelCode: EMPTY_STRING }); // TODO: have to remove this line.
      const vinDetails = await this.getVinDetails(vin);
      this.setState({ options: vinDetails?.options });
      if (_isEmpty(vinDetails) || isEditMode) {
        return;
      }

      this.setRecalls(vin);
      this.addBulkOptions(vinDetails?.options);
    } else {
      this.resetFields(true);
    }

    await this.getGlAccountFromSettings();
    this.getVISPrice(vin);
  };

  setRecalls = async vin => {
    const { getRecallsByVin, updateRecallsInVehicleDetails } = this.props;
    return getRecallsByVin(vin).then(data => {
      const payload = getRecallsList(_get(data, 'recalls'));
      updateRecallsInVehicleDetails(payload);
      return true;
    });
  };

  addBulkOptions = options => {
    const { actions, optionsLookupEnable, form } = this.props;
    const modelCode = tget(form.getState(), 'values.modelCode', EMPTY_STRING);
    if (!(optionsLookupEnable && modelCode)) {
      actions.addBulkOptions(options);
    }
  };

  resetFields = valid => {
    const { form } = this.props;
    this.updateFieldState({
      year: EMPTY_STRING,
      make: EMPTY_STRING,
      displayModel: EMPTY_STRING,
      trim: EMPTY_STRING,
      stockID: EMPTY_STRING,
      trimDetails: EMPTY_OBJECT,
      model: EMPTY_STRING,
      displayMake: EMPTY_STRING,
    });
    if (!valid) this.toggleFieldForInput(true, 'year', 'make', 'displayModel', 'model', 'displayMake');
    form.mutators.setFieldTouched('stockID', false);
  };

  getVISPrice = vin => {
    const { form, actions, isEditMode } = this.props;
    const formState = _get(form.getState(), 'values', {}) || EMPTY_OBJECT;

    // If Enhanced vin decoding is enabled, we won't call the OEM api again for pricing fields
    if (isCVDVinDecodingSource()) {
      return;
    }

    if (isStringEmpty(_trim(vin))) {
      actions.updateHeaderDetails(formState);
      return;
    }
    const { vehicleType } = formState;
    if (VEHICLE_TYPE[vehicleType] === VEHICLE_TYPE.NEW && !isEditMode) {
      VehicleDetailsAPI.fetchVisPrice(vin).then(({ data }) => {
        const pricingDetails = getVISPricing(data);
        actions.updateHeaderDetails({ ...formState, pricingDetails });
        form.change('pricingDetails', pricingDetails);
      });
    } else {
      actions.updateHeaderDetails(formState);
      form.change('pricingDetails', { ...formState.pricingDetails, ...resetPricingDetails() });
    }
  };

  handleVinLookupFailureConfirmation = () => {
    const { setLookupFailureForVehicleSave } = this.props;
    setLookupFailureForVehicleSave(false);
  };

  // returns whether should call vin-lookup dependent API's.
  handleVinValidationFailureCases = ({ vin, vinDetails = EMPTY_OBJECT, formState }) => {
    const { form, setLookupFailureForVehicleSave, isEditMode } = this.props;
    if (!vinDetails || checkIfVINContainsInvalidCharacters(vin) || !VehicleReader.validVin(vinDetails)) {
      setLookupFailureForVehicleSave(true);
      form.mutators.setFieldTouched('stockID', true);
      showVINConfirmationModal({ values: formState }, this.handleVinLookupFailureConfirmation);
      if (!vinDetails) {
        // as vin lookup failed no need for calling other dependent APIs(stock Id, glAccount, etc.)
        return false;
      }
    } else {
      setLookupFailureForVehicleSave(false);
    }
    return !isEditMode;
  };

  getVinDetails = async (vin, showError = true) => {
    const { actions, form, displayModelSource, currentLanguageId } = this.props;
    const formState = _get(form.getState(), 'values', EMPTY_OBJECT);
    const vinDetails = await actions.fetchVINDetails(vin, currentLanguageId);
    if (showError) {
      const shouldCallDependentAPIs = this.handleVinValidationFailureCases({ vin, vinDetails, formState });
      if (!shouldCallDependentAPIs) {
        return;
      }
    }
    const vehicle = parseValuesFromVin(vinDetails, displayModelSource);
    this.updateFieldState(vehicle);
    const existingStockId = _get(form.getState(), 'values.stockID');
    if (!_isEmpty(vinDetails) && _isEmpty(existingStockId) && PROGRAM_CONFIG.shouldGenerateStockId()) {
      await this.getStockID();
    }
    if (PROGRAM_CONFIG.shouldFetchAdditionalCosts()) this.setDefaultAdditionalCosts(vinDetails);
    return vinDetails || EMPTY_OBJECT;
  };

  getMappedBodyType = bodyType => {
    const formatExistingBodyType = (formatedBodyType, type) => {
      let existingBodyType = 'SEDAN';
      formatedBodyType.forEach(formatedType => {
        if (BODY_TYPE[type].toString().toUpperCase().indexOf(_toUpper(formatedType)) > -1) {
          existingBodyType = type;
        }
      });
      return existingBodyType;
    };
    let mappedBodyType = 'SEDAN';
    Object.keys(BODY_TYPE).forEach(type => {
      if (_isArray(BODY_TYPE[type])) {
        BODY_TYPE[type].forEach(truck => {
          if (truck.toUpperCase().indexOf(_toUpper(bodyType)) > -1) {
            mappedBodyType = type;
          } else {
            const formatedBodyType = bodyType.split(' ');
            mappedBodyType = formatExistingBodyType(formatedBodyType, type);
          }
        });
      } else if (_isString(BODY_TYPE[type])) {
        if (BODY_TYPE[type].toUpperCase().indexOf(_toUpper(bodyType)) > -1) {
          mappedBodyType = type;
        } else {
          const formatedBodyType = bodyType.split(' ');
          mappedBodyType = formatExistingBodyType(formatedBodyType, type);
        }
      }
    });
    return mappedBodyType;
  };

  setDefaultAdditionalCosts = async vinDetails => {
    if (vinDetails) {
      const { actions, bodyTypeMapping, vehicleTypes, form, displayModelSource, currentLanguageId } = this.props;
      const vehicleDetails = getAdditionalCostAPIPayload(
        tget(form.getState(), 'values', EMPTY_OBJECT),
        { bodyTypeMapping, vehicleTypes },
        displayModelSource
      );
      const { data } = await actions.getAdditionalCostsForVehicle({ vehicleDetails }, currentLanguageId);
      if (data) {
        const filteredData = filterAdditionalCostDataForProgram(data);
        actions.clearAdditionalCost();
        _forEach(filteredData, item => {
          actions.addAdditionalCost({ item });
        });
      }
    }
  };

  getStockID = async params => {
    const { form, displayModelSource, enableStockNumberGenerationOnSave } = this.props;
    const { isManualRefresh = false } = params || EMPTY_OBJECT;

    if (enableStockNumberGenerationOnSave && !isManualRefresh) return;

    const formValues = tget(form.getState(), 'values', EMPTY_OBJECT);
    const {
      vehicleType,
      vin,
      year,
      vehicleSubType,
      make,
      displayMake,
      makeId,
      trimDetails,
      sourceInfo,
      stockedInAtSiteId,
      mfrModelCode,
    } = formValues;
    const { bodyClass } = trimDetails || EMPTY_OBJECT;
    const { source } = sourceInfo || EMPTY_OBJECT;
    if (vehicleType && vin) {
      const stockIdPayload = {
        vehicleInfo: {
          vin,
          year,
          make,
          displayMake,
          makeId,
          model: getDisplayModalText(formValues, displayModelSource),
          bodyClass,
          stockType: vehicleType,
          source,
          stockSubType: vehicleSubType,
          stockedInAtSiteId,
          mfrModelCode,
        },
      };
      const response = await generateStockId(stockIdPayload);
      if (response === '') {
        toaster(TOASTER_TYPE.ERROR, MESSAGES.NO_STOCK_ID_RULE_FOUND);
      } else if (response) {
        this.setState(
          prevState => {
            const stockIDFieldStatus = _get(prevState, 'fieldStatus.stockID');
            return {
              fieldStatus: { ...prevState.fieldStatus, stockID: { ...stockIDFieldStatus, customError: undefined } },
            };
          },
          () => {
            this.updateFieldState({ stockID: response || EMPTY_STRING });
          }
        );
      }
    } else if (!vehicleType) toaster(TOASTER_TYPE.INFO, MESSAGES.SELECT_VEHICLE_TYPE);
  };

  allowSoldVehicleToInventory = async vin => {
    const { vinValidateAcrossWorkSpace } = this.props;
    if (vin) {
      this.isVinLookupInProgressRef.current = true;
      try {
        await this.getGlAccountFromSettings();
        if (!(await checkIfVinExists({ vin, vinValidateAcrossWorkSpace }))) {
          const vinDetails = await this.getVinDetails(vin);
          this.addBulkOptions(vinDetails?.options);
        } else this.resetFields(true);
      } catch (error) {
        toaster(TOASTER_TYPE.ERROR, getErrorMessage(error));
      } finally {
        this.isVinLookupInProgressRef.current = false;
      }
    }
  };

  hideStockTypeChangeModal = () => {
    this.setState({
      showStockTypeChangeModal: false,
      updatedVehicle: EMPTY_OBJECT,
    });
  };

  renderStockTypeChangeConfirmationModal = () => {
    const { showStockTypeChangeModal, updatedVehicle } = this.state;
    return (
      <ConfirmationDialog
        title={__('Confirm Stock Type Change')}
        isVisible={showStockTypeChangeModal}
        content={MESSAGES.STOCK_TYPE_CHANGE_MESSAGE}
        onCancel={this.hideStockTypeChangeModal}
        onSubmit={() => {
          this.onVehicleTypeChange(updatedVehicle);
          this.hideStockTypeChangeModal();
        }}
        submitBtnText={__('Confirm')}
        secondaryBtnText={__('Cancel')}
      />
    );
  };

  confirmStockTypeChange = updatedVehicle => {
    const changedVehicleType = tget(updatedVehicle, 'value', EMPTY_STRING);
    const { isEditMode, selectedVehicle } = this.props;
    if (isEditMode && confirmStockTypeChange(selectedVehicle, changedVehicleType)) {
      this.setState({
        showStockTypeChangeModal: true,
        updatedVehicle,
      });
    } else {
      this.onVehicleTypeChange(updatedVehicle);
    }
  };

  onVehicleTypeChange = payload => {
    const vehicleType = _get(payload, 'value');
    const { fields, form, selectedVehicle, enableUsedVehiclesPricingReset, isVehicleConfiguratorEnabled } = this.props;
    const formValues = _get(form.getState(), 'values') || {};
    const vin = _get(formValues, 'vin') || selectedVehicle.vin;
    form.batch(() => {
      _forEach(
        _filter(GENERAL_FORM_FIELDS, ({ category }) => category === VEHICLE_TYPE[vehicleType]),
        ({ name }) => {
          form.change(`${name}`, selectedVehicle[name]);
        }
      );

      // TODO: batch these changes
      form.change('vehicleType', vehicleType);
      form.change('vehicleSubType', undefined);
      form.change('certified', false);
      form.change('dealerCertified', false);

      const defaultSource = _get(DEFAULT_VEHICLE_SOURCE_BASED_ON_VEHICLE_TYPE, [VEHICLE_TYPE[vehicleType]]);

      const sourceOptions = _get(_find(fields, { key: 'SOURCE' }), 'options');

      const isDefaultSourceValuePresent = _some(sourceOptions, { name: defaultSource });

      if (isDefaultSourceValuePresent) {
        form.change('sourceInfo.source', defaultSource);
      } else {
        form.change('sourceInfo.source', undefined);
      }
    });

    if (enableUsedVehiclesPricingReset && isVehicleConfiguratorEnabled) this.resetChromeOptionPricingFields();

    this.setState(
      produce(draft => {
        draft.currentSeletedVehicleType = vehicleType;
      }),
      () => this.debouncedAPICallOnVehicleTypeChange(vin)
    );
  };

  resetChromeOptionPricingFields = () => {
    const { form } = this.props;
    const formState = tget(form.getState(), 'values', {}) || EMPTY_OBJECT;
    form.change('pricingDetails', { ...formState.pricingDetails, ...RESET_CHROME_OPTIONS_PRICING_DETAILS });
    form.change('trimDetails', EMPTY_OBJECT);
    form.change('trim', EMPTY_STRING);
    form.change('styleId', EMPTY_STRING);
    form.change('chromeSerializedValue', EMPTY_STRING);
  };

  handleAPICallsOnVehicleTypeChange = async vin => {
    const { isEditMode, form, actions, fetchSetupFields } = this.props;
    const formCurrentState = _get(form.getState(), 'values') || {};
    actions.updateHeaderDetails(formCurrentState);
    fetchSetupFields(formCurrentState);
    if (isEditMode) {
      const existingStockId = _get(form.getState(), 'values.stockID');
      if (_isEmpty(existingStockId)) {
        await this.getStockID();
      }
    } else {
      this.allowSoldVehicleToInventory(vin);
      this.getVISPrice(vin);
    }
  };

  onVehicleTypeSubCategoryChange = async vehicleTypeSubCategory => {
    const { form, isEditMode, fetchSetupFields } = this.props;
    const vinDetails = _get(form.getState(), 'values', {});
    const { vin } = vinDetails;
    form.change('vehicleSubType', vehicleTypeSubCategory, '');
    if (vin) {
      if (!isEditMode) {
        await this.getGlAccountFromSettings();
        this.setDefaultAdditionalCosts(vinDetails);
        // TODO : field Action Handlers
      }
      const existingStockId = _get(form.getState(), 'values.stockID');
      if (_isEmpty(existingStockId) && !this.isVinLookupInProgressRef.current) {
        await this.getStockID();
      }
    }
    const formState = _get(form.getState(), 'values', {});
    fetchSetupFields(formState);
  };

  onToggleCertified = key => event => {
    const { form } = this.props;
    form.change(key, event?.target?.checked);
  };

  onToggleCertificationStatus = selections => {
    const { form } = this.props;
    const selectedValue = tget(selections, [_size(selections) - 1], null);
    form.change('certificationStatus', selectedValue);
  };

  toggleFieldForInput = (isDisabled, ...fields) => {
    this.setState(
      produce(draft => {
        fields.forEach(field => {
          draft.fieldStatus[field] = {
            disabled: isDisabled,
          };
        });
      })
    );
  };

  updateFieldStatus = (fieldName, status) =>
    this.setState(prevState => ({ fieldStatus: { ...prevState.fieldStatus, [fieldName]: status } }));

  updateFieldState = fields => {
    const { form } = this.props;
    const formBatchPromise = new Promise((resolve, reject) => {
      try {
        form.batch(() => {
          Object.keys(fields).forEach(field => {
            form.change(field, fields[field]);
          });
        });
        resolve(true);
      } catch (error) {
        reject(error);
      }
    });
    formBatchPromise.then(() => {
      // updateVehicleState({ formCurrentState: _get(form.getState(), 'values') });
    });
  };

  removeFuelTypeFromFields = (fields = EMPTY_ARRAY) => {
    const newFields = fields
      .map(field => {
        if (field.key === FUEL_TYPE) {
          return undefined;
        }
        return field;
      })
      .filter(elem => elem !== undefined);
    return newFields;
  };

  onLabelPrint = () => {
    const { form, actions, selectedVehicleType, displayModelSource } = this.props;
    const { currentSeletedVehicleType } = this.state;
    const vehicleDetailsForm = _get(form.getState(), 'values', {});
    const payload = createPrintPayload(vehicleDetailsForm, displayModelSource);
    const vehicleType = selectedVehicleType || currentSeletedVehicleType;
    if (payload && vehicleType) {
      actions.doLabelPrint(payload);
    } else {
      toaster(TOASTER_TYPE.INFO, MESSAGES.MANDATORY_FIELDS_FOR_PRINT_WARNING);
    }
  };

  appendCustomFieldsToGeneral = (fields = [], customFields = []) => [...fields, ...customFields];

  shouldShowCertCheckbox = (assetSubTypeNoneFields, vehicleType) =>
    !_isEmpty(assetSubTypeNoneFields) && vehicleType === _toUpper(VEHICLE_TYPE.USED);

  refreshStockId = () => {
    const { form, selectedVehicle } = this.props;
    const vin = _get(form.getState(), 'values', {}).vin || selectedVehicle.vin;
    if (!_isEmpty(vin)) {
      this.getStockID({ isManualRefresh: true });
    }
  };

  getGlAccountFromSettings = () => {
    const { getGlAccountFromSettings, form } = this.props;
    const vehicleDetailsForm = _get(form.getState(), 'values', {});
    const pricingDetails = _get(vehicleDetailsForm, 'pricingDetails') || {};
    const parsedPricingDetails = parsePriceFieldValue(pricingDetails);
    return getGlAccountFromSettings({ ...vehicleDetailsForm, pricingDetails: parsedPricingDetails }).then(data => {
      const accountId = _get(data, 'accountInfo.accountId');
      form.change('accountId', accountId);
      return true;
    });
  };

  openPurchaseInformationPortal = () => {
    const { fields, vendorInvoiceData, addOrUpdateVendorInvoice, actions, form, fetchAllHardPacksList } = this.props;
    const vehicleDetailsForm = _get(form.getState(), 'values', {});
    showPortal({
      component: PurchaseInformation,
      componentProps: {
        onHide: hidePortal,
        sources: getSourceOptions(fields),
        invoiceData: vendorInvoiceData,
        addOrUpdateVendorInvoice,
        fetchHardpackMetadata: actions.fetchHardpackMetadata,
        fetchAllHardPacksList,
        vehicleStatus: vehicleDetailsForm?.status,
        accountPosted: vehicleDetailsForm?.accountPosted,
      },
    });
  };

  shouldRenderCidDeclaration = () => {
    const { isEditMode, selectedVehicleType } = this.props;
    const { currentSeletedVehicleType = selectedVehicleType } = this.state;
    return (
      isEditMode &&
      isRRG() &&
      currentSeletedVehicleType === VEHICLE_TYPES.USED &&
      selectedVehicleType === VEHICLE_TYPES.USED
    );
  };

  render() {
    const {
      actions,
      damages,
      dealerConfig,
      selectedVehicle,
      isEditMode,
      enterpriseV2Enabled,
      attachments,
      vehicleTypes,
      selectedVehicleType,
      form,
      customFields,
      recallDetails,
      vehiclePOs,
      optionsLookupEnable,
      fetchSetupFields,
      userSettings,
      displayModelSource,
      getUserInfoById,
      fetchPoliceRecordData,
      policeRecordData,
      policeRecordSuccessCb,
      customerViewProps,
      fetchVendorInvoicesData,
      vendorInvoiceData,
      certificationStatusOptions,
      declarationStatus,
      fetchDeclarationStatus,
      enterpriseV2Workspaces,
      vinValidateAcrossWorkSpace,
      enableStockNumberGenerationOnSave,
      isReadOnlyDetails,
    } = this.props;
    let { fields } = this.props;
    const formState = form.getState();
    const enteredVin = _get(formState, 'values.vin');
    const { addAttachedFiles, removeAttachedFiles, modifyAttachmentSensitivity } = actions;
    const { oemDetails, warranties, mileageStatus } = selectedVehicle;
    const {
      fieldStatus,
      currentSeletedVehicleType = selectedVehicleType,
      options = [],
      vinDecodeVehicles,
    } = this.state;
    const vehicleDetailsForm = _get(form.getState(), 'values', {});
    const {
      id,
      pricingDetails,
      certified,
      dealerCertified,
      locationCode,
      stockedInAtSiteId,
      make,
      model,
      displayModel,
      bestStyleName,
      year,
      certificationStatus,
      status,
    } = vehicleDetailsForm;
    const source = _get(vehicleDetailsForm, 'sourceInfo.source');
    const brand = _get(vehicleDetailsForm, 'trimDetails.brand');
    const warrantiesList = tget(vehicleDetailsForm, 'warranties', EMPTY_ARRAY);
    let { vehicleSubType } = vehicleDetailsForm;
    fields = this.removeFuelTypeFromFields(fields);
    fields = this.appendCustomFieldsToGeneral(fields, customFields);
    if (!vehicleSubType) {
      vehicleSubType = _get(selectedVehicle, 'general.vehicleSubType');
    }
    const agingField = SettingsReader.agingField(userSettings);
    const isVehicleSubTypeMandatory = SettingsReader.isVehicleSubTypeMandatory(userSettings);
    const age = getVehicleAge(selectedVehicle, agingField);
    const vehicle = { ...(selectedVehicle || EMPTY_OBJECT), vehicleDamages: getOpenDamages(damages) };
    const roDetails = _get(selectedVehicle, 'roDetails') || EMPTY_ARRAY;
    const poData = isEditMode ? vehiclePOs[id] || EMPTY_ARRAY : EMPTY_ARRAY;
    const selectedDisplayModelSource = getDisplayModelSourceValue(
      displayModelSource,
      bestStyleName,
      displayModel,
      model
    );
    const certificationStatusProps = {
      certificationStatus,
      certificationStatusOptions,
      onToggleCertificationStatus: this.onToggleCertificationStatus,
    };

    return (
      <div className={styles.generalContainer}>
        <div className={cx({ [styles.disabled]: isReadOnlyDetails })}>
          <Overview
            actions={actions}
            recalls={recallDetails}
            titleBlock={dealerConfig.titleBlock}
            age={age}
            pricingDetails={pricingDetails}
            vehicleType={currentSeletedVehicleType}
            selectedVehicle={vehicle}
            userSettings={userSettings}
            displayModelSource={displayModelSource}
            customerViewProps={customerViewProps}
          />
          <VehicleType
            selectedVehicleType={currentSeletedVehicleType}
            selectedVehicleSubType={vehicleSubType}
            certified={certified}
            dealerCertified={dealerCertified}
            onVehicleTypeChange={this.confirmStockTypeChange}
            onVehicleTypeSubCategoryChange={this.onVehicleTypeSubCategoryChange}
            onToggleCertified={this.onToggleCertified}
            options={vehicleTypes}
            isVehicleSubTypeMandatory={isVehicleSubTypeMandatory}
            certificationStatusProps={certificationStatusProps}
          />
          <PropertyControlledComponent controllerProperty={PROGRAM_CONFIG.shouldShowVehicleQualification()}>
            <VehicleQualification
              vin={enteredVin}
              displayModelSource={displayModelSource}
              updateFieldState={this.updateFieldState}
              onVinDecode={this.onVinDecode}
              vinDecodeVehicles={vinDecodeVehicles}
              onVinDecodeSave={this.onVinDecodeSave}
            />
          </PropertyControlledComponent>
          <GeneralForm
            actions={actions}
            form={form}
            dealerDefinedFormInput={dealerConfig.dealerDefinedFormData}
            isEditMode={isEditMode}
            enterpriseV2Enabled={enterpriseV2Enabled}
            enterpriseV2Workspaces={enterpriseV2Workspaces}
            fieldStatus={fieldStatus}
            fields={fields}
            onPrint={this.onLabelPrint}
            refreshStockId={this.refreshStockId}
            getGlAccountFromSettings={this.getGlAccountFromSettings}
            selectedVehicleType={currentSeletedVehicleType}
            formData={{
              selectedMake: make,
              selectedModel: selectedDisplayModelSource,
              selectedYear: year,
              selectedBrand: brand,
              source,
              mileageStatus,
            }}
            formKeys={[
              locationCode,
              make,
              selectedDisplayModelSource,
              source,
              stockedInAtSiteId,
              currentSeletedVehicleType,
              mileageStatus,
              year,
              brand,
            ]}
            // Needed because we are creating dynamic options for below 2 of them and have to re initialize form on the basis of value present,
            options={options}
            //
            getUserInfoById={getUserInfoById}
            updateFieldStatus={this.updateFieldStatus}
            optionsLookupEnable={optionsLookupEnable}
            fetchSetupFields={fetchSetupFields}
            getStockID={this.getStockID}
            displayModelSource={displayModelSource}
            customerViewProps={customerViewProps}
            vehicleId={id}
            vinValidateAcrossWorkSpace={vinValidateAcrossWorkSpace}
            enableStockNumberGenerationOnSave={enableStockNumberGenerationOnSave}
          />
          <PropertyControlledComponent controllerProperty={PROGRAM_CONFIG.shouldShowPurchaseInvoice()}>
            <PurchaseDetails
              vehicleId={id}
              isEditMode={isEditMode}
              fetchVendorInvoicesData={fetchVendorInvoicesData}
              openPortal={this.openPurchaseInformationPortal}
              invoiceData={vendorInvoiceData}
              vehicleStatus={vehicleDetailsForm?.status}
              // cannot destruct as valiable fields is already declared above
              // eslint-disable-next-line react/destructuring-assignment
              sources={getSourceOptions(this.props.fields)}
            />
          </PropertyControlledComponent>
          <PropertyControlledComponent
            controllerProperty={
              isEditMode &&
              PROGRAM_CONFIG.shouldShowPoliceRecord() &&
              currentSeletedVehicleType === VEHICLE_TYPES.USED &&
              selectedVehicleType === VEHICLE_TYPES.USED
            }>
            <PoliceNumber
              vehicleDetails={vehicleDetailsForm}
              fetchPoliceRecordData={fetchPoliceRecordData}
              policeRecordData={policeRecordData}
              policeRecordSuccessCb={policeRecordSuccessCb}
            />
          </PropertyControlledComponent>
          <PropertyControlledComponent controllerProperty={this.shouldRenderCidDeclaration()}>
            <CidDeclaration
              vehicleId={id}
              vehicleStatus={status}
              declarationStatus={declarationStatus}
              fetchDeclarationStatus={fetchDeclarationStatus}
            />
          </PropertyControlledComponent>
          <PropertyControlledComponent controllerProperty={!isInchcapeOrRRG()}>
            <SourceDealInfo />
          </PropertyControlledComponent>
          <OEM oemdetails={oemDetails} />
          <PropertyControlledComponent controllerProperty={!PROGRAM_CONFIG.shouldHideRoPoDetails()}>
            <ServiceDetails roDetails={roDetails} poData={poData} vehicle={vehicleDetailsForm} form={form} />
          </PropertyControlledComponent>
          <PropertyControlledComponent
            controllerProperty={!PROGRAM_CONFIG.shouldShowWarrantyConfiguration()}
            fallback={<ApplicableWarrantiesV2 form={form} warrantiesList={warrantiesList} />}>
            <ApplicableWarranties
              shouldShowTitle
              warranties={warranties || EMPTY_ARRAY}
              className={styles.generalTable}
            />
          </PropertyControlledComponent>
        </div>
        <PropertyControlledComponent controllerProperty={!PROGRAM_CONFIG.shouldHideUploadMedia()}>
          <GeneralAttachment
            addAttachedFiles={addAttachedFiles}
            removeAttachedFiles={removeAttachedFiles}
            attachments={attachments}
            modifyAttachmentSensitivity={modifyAttachmentSensitivity}
            isReadOnly={isReadOnlyDetails}
          />
        </PropertyControlledComponent>
        {this.renderStockTypeChangeConfirmationModal()}
      </div>
    );
  }
}

export default withMultiLingualInfo(General);
