import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { defaultMemoize } from 'reselect';
import _noop from 'lodash/noop';

import { EMPTY_ARRAY } from 'tbase/app.constants';
import Heading from 'tcomponents/atoms/Heading';

import { getMediaListFromAttachment } from 'helpers/media';
import Attachments from 'molecules/Attachments/Attachments';

import styles from './general.module.scss';

class GeneralAttachment extends PureComponent {
  static propTypes = {
    addAttachedFiles: PropTypes.func,
    removeAttachedFiles: PropTypes.func,
    attachments: PropTypes.array,
    modifyAttachmentSensitivity: PropTypes.func,
    isReadOnly: PropTypes.bool,
  };

  static defaultProps = {
    addAttachedFiles: _noop,
    removeAttachedFiles: _noop,
    attachments: EMPTY_ARRAY,
    modifyAttachmentSensitivity: _noop,
    isReadOnly: false,
  };

  getMediaListFromAttachment = defaultMemoize(getMediaListFromAttachment);

  onUploadFiles = files => {
    const { addAttachedFiles } = this.props;
    addAttachedFiles(files);
  };

  onDeleteFiles = mediaFile => {
    const { removeAttachedFiles } = this.props;
    removeAttachedFiles(mediaFile);
  };

  handleModifySensitivity = mediaFileId => {
    const { modifyAttachmentSensitivity } = this.props;
    modifyAttachmentSensitivity(mediaFileId);
  };

  render() {
    const { attachments, isReadOnly } = this.props;
    const modifiedAttachments = this.getMediaListFromAttachment(attachments);

    return (
      <React.Fragment>
        <Heading size={3}>{__('Attachments')}</Heading>
        <div className={styles.generalAttachmentTable}>
          <Attachments
            onUploadFiles={this.onUploadFiles}
            onDeleteFiles={this.onDeleteFiles}
            onModifySensitivity={this.handleModifySensitivity}
            attachments={modifiedAttachments}
            showHeading={false}
            isReadOnly={isReadOnly}
          />
        </div>
      </React.Fragment>
    );
  }
}

export default GeneralAttachment;
