/* eslint-disable no-case-declarations */
import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { compose } from 'recompose';
import PropTypes from 'prop-types';
import classnames from 'classnames';

import _castArray from 'lodash/castArray';
import _concat from 'lodash/concat';
import _filter from 'lodash/filter';
import _find from 'lodash/find';
import _get from 'lodash/get';
import _head from 'lodash/head';
import _includes from 'lodash/includes';
import _isEmpty from 'lodash/isEmpty';
import _isEqual from 'lodash/isEqual';
import _isObject from 'lodash/isObject';
import _map from 'lodash/map';
import _noop from 'lodash/noop';
import _reject from 'lodash/reject';
import _size from 'lodash/size';
import _values from 'lodash/values';
import _has from 'lodash/has';
import _trim from 'lodash/trim';
import _some from 'lodash/some';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from 'tbase/app.constants';
import { MILEAGE_STATUS } from 'tbase/constants/vehicleInventory/vehicle';
import { VEHICLE_STATUS } from 'tbase/constants/vehicleInventory/vehicleStatus';
import { BEST_STYLE_NAME } from 'tbase/constants/retail/salesSetup.constants';
import { getDaysOnLot, getDaysTosell, getVehicleModel } from 'tbase/helpers/vehicle.helper';
import { capitalizeFirstLetters, capitalizeFirstLetterOnly } from 'tbase/formatters/string';
import ESReader from 'tbase/readers/EsResponse';
import { getEndOfToday, getMomentValueOf, getCurrentYearValue } from 'tbase/utils/dateUtils';
import { isInchcape, isRRG } from 'tbase/utils/sales/dealerProgram.utils';
import { getErrorMessage } from 'tbase/utils/errorUtils';
import { tget } from 'tbase/utils/general';
import isStringEmpty from 'tbase/utils/isStringEmpty';
import { getYearOptions } from 'tbase/utils/sales';
import { VEHICLE_TYPES } from 'tbase/constants/vehicleInventory/vehicleTypes';
import VehicleReader from 'tbusiness/appServices/vehicleInventory/readers/vehicle';
import { shouldShowEnterpriseAge } from 'tbusiness/appServices/vehicleInventory/helpers/vehicle';
import Button from 'tcomponents/atoms/Button';
import Content from 'tcomponents/atoms/Content';
import FontIcon, { SIZES } from 'tcomponents/atoms/FontIcon';
import Tooltip, { TOOLTIP_PLACEMENT } from 'tcomponents/atoms/tooltip';
import Heading from 'tcomponents/atoms/Heading';
import Icon from 'tcomponents/atoms/iconAsBtn';
import Label from 'tcomponents/atoms/Label';
import Switch, { LABEL_POSITIONS } from 'tcomponents/organisms/FormBuilder/fieldRenderers/switch';
import withActionHandlers from 'tcomponents/connectors/withActionHandlers';
import ConfirmationDialog from 'tcomponents/molecules/confirmationDialog';
import Popover from 'tcomponents/molecules/popover/TPopover';
import PropertyControlledComponent from 'tcomponents/molecules/PropertyControlledComponent';
import { SelectWrapper } from 'tcomponents/molecules/finalFormAntdWrappers';
import ACTION_TYPES from 'tcomponents/organisms/FormBuilder/constants/actionTypes';
import { toaster, TOASTER_TYPE } from 'tcomponents/organisms/NotificationWrapper';
import dealerPropertyHelper from 'tcomponents/helpers/sales/dealerPropertyHelper';
import { GENERAL_FIELD_KEYS } from 'twidgets/appServices/sales/config/viFormFields';
import { ERRORS } from 'twidgets/constants/vi.constants';
import ChromeApi from 'twidgets/organisms/vi/OptionsResolver/chromeConfigOptions.api';
import withMultiLingualInfo from 'twidgets/hocs/withMultiLingualInfo';

import { MESSAGES, VEHICLE_KEYS, TEKION_MAKE_DISPLAY_NAME, TEKION_MAKE_ID } from 'constants/constants';
import { GENERAL_FORM_FIELDS } from 'constants/formFields';
import PROGRAM_CONFIG from 'constants/programConfig';
import { getUserAccessibleSitesList, checkIfVehicleConfiguratorEnabled, getYesNoOptions } from 'helpers/common';
import {
  getMileageStatusOptions,
  getSourceOptions,
  getTrimDetails,
  getModifiedOptionsList,
  getAllBrands,
  isRVDealerEnabled,
  isMultiLingualEnabled,
  isStandardMakeEnabled,
  getIsGroupAgingFieldDisabled,
} from 'helpers/vehicle.helper';
import { getDisplayModelSourceLabel } from 'helpers/vehicleDetails.helper';
import { getLastUpdatedUser } from 'helpers/user';
import withMetaData from 'hocs/withMetaData';
import { ComponentWrapper } from 'organisms/ReactFinalFormWrapper';
import TrimModal from 'organisms/TrimModal';
import { formFieldReader, customFieldReader } from 'pages/Settings/settings.reader';
import { showConfirmationModal } from 'organisms/ConfirmationModal';
import MultiLingualFieldWindowWrapper from 'organisms/MultiLingualForm/MultiLingualFieldWindowWrapper';
import {
  hasYMMEdit,
  hasVehicleEditDates,
  hasStockIdEdit,
  hasLabelPrintPermission,
  hasInventoryAllCostPricesViewPermission,
} from 'permissions/inventory.permissions';
import EventReader from 'readers/event';
import VehicleService from 'services/vehicle';
import { transformObjectValuesToString, getFieldOptions } from 'utils';
import renderFormData from 'utils/formBuilder';
import mileageLabelUtil from 'utils/mileageLabelUtil';
import weightLabelUtil from 'utils/weightLabelUtil';
import { checkIfVinExists, validateNonZeroNumberField, composeValidators } from 'utils/validations';

import { BATTERY_PURCHASE_TYPE_CONFIG, INVOICE_TYPE_CONFIG } from 'constants/constantEnum';
import { getVehicleConfiguratorEnabled } from 'selectors/common';

import { getPicturesCount } from '../../../VehicleInventoryList/VehicleInventoryList.helpers';
import ACTION_HANDLERS from './GeneralForm.actionHandlers';
import { CustomFieldsComponent } from './GeneralFormCustomFields';
import VehicleDetailsAPI from '../../vehicleDetails.api';
import { checkIfVINEnabled, updateVehicleForm, getDealType, getFormInitialState } from '../../VehicleDetails.helpers';
import { searchOrderNumberPayload } from '../../VehicleDetails.factory';

import styles from './general.module.scss';

class GeneralForm extends PureComponent {
  static propTypes = {
    form: PropTypes.object,
    fieldStatus: PropTypes.object,
    fields: PropTypes.array,
    isEditMode: PropTypes.bool,
    onPrint: PropTypes.func,
    refreshStockId: PropTypes.func,
    updateFieldStatus: PropTypes.func,
    actions: PropTypes.object,
    optionsLookupEnable: PropTypes.bool,
    fetchSetupFields: PropTypes.func,
    customerViewProps: PropTypes.object,
    enableUsedVehiclesPricingReset: PropTypes.bool,
    selectedVehicleType: PropTypes.string,
    enterpriseV2Enabled: PropTypes.bool,
    removeDefaultOptions: PropTypes.bool,
    enableStockNumberGenerationOnSave: PropTypes.bool,
  };

  static defaultProps = {
    form: EMPTY_OBJECT,
    fieldStatus: EMPTY_OBJECT,
    fields: EMPTY_ARRAY,
    isEditMode: true,
    onPrint: _noop,
    refreshStockId: _noop,
    updateFieldStatus: _noop,
    actions: EMPTY_OBJECT,
    optionsLookupEnable: false,
    fetchSetupFields: _noop,
    customerViewProps: EMPTY_OBJECT,
    enableUsedVehiclesPricingReset: false,
    selectedVehicleType: VEHICLE_TYPES.NEW,
    enterpriseV2Enabled: false,
    removeDefaultOptions: false,
    enableStockNumberGenerationOnSave: false,
  };

  constructor(props) {
    super(props);
    this.isVINEditEnabled = false;
    this.state = {
      fields: props.fields,
      initialValues: EMPTY_OBJECT,
      showModelChangeModal: false,
      showReceivedDateChangeModal: false,
      defaultValueApplied: EMPTY_OBJECT,
    };
    this.modelCode = EMPTY_STRING;
    this.selectedReceivedDate = EMPTY_STRING;
  }

  componentDidMount() {
    this.init();
  }

  componentDidUpdate(prevProps) {
    const { fields, formKeys, optionsLookupEnable, options, styleId, metaData, customerViewProps } = this.props;
    if (
      !_isEqual(prevProps.fields, fields) ||
      !_isEqual(prevProps.formKeys, formKeys) ||
      !_isEqual(prevProps.options, options) ||
      !_isEqual(prevProps.styleId, styleId) ||
      !_isEqual(prevProps.optionsLookupEnable, optionsLookupEnable) ||
      !_isEqual(prevProps.metaData?.locationCode, metaData?.locationCode) ||
      !_isEqual(prevProps.metaData?.makes, metaData?.makes) ||
      !_isEqual(prevProps.customerViewProps, customerViewProps)
    ) {
      this.init();
    }
  }

  onFieldChange = async (val, field) => {
    const { actions, form, updateFieldStatus, fieldStatus } = this.props;
    const { initialValues } = this.state;
    const { stockID } = initialValues;
    const { stockID: stockNumberFieldStatus = EMPTY_OBJECT } = fieldStatus || EMPTY_OBJECT;
    form.change(field.name, val);
    this.updateVehicleState();
    if (stockID !== val) {
      const filters = [
        {
          field: 'stockID',
          values: [val],
        },
      ];
      const duplicate = await actions.fetchTotalCount(filters);
      const customError = duplicate ? ERRORS.DUPLICATE_STOCK_ID : undefined;
      updateFieldStatus('stockID', { ...stockNumberFieldStatus, customError });
    }
  };

  onFieldFocus = (val, field) => {
    const { initialValues } = this.state;
    if (!_get(initialValues, field.name)) {
      this.setState({ initialValues: { ...initialValues, [field.name]: val } });
    }
  };

  handleSourceChange = value => {
    const { form, fetchSetupFields, getGlAccountFromSettings, isEditMode } = this.props;
    form.change('sourceInfo.source', value);
    const formState = this.getFormState();
    fetchSetupFields(formState);
    if (!isEditMode) {
      getGlAccountFromSettings();
    }
  };

  onVinChangeCb = async value => {
    const { updateFieldStatus, fieldStatus, enterpriseV2Workspaces, enterpriseV2Enabled, vinValidateAcrossWorkSpace } =
      this.props;
    const { vin: vinFieldStatus = EMPTY_OBJECT } = fieldStatus || EMPTY_OBJECT;
    const error = await checkIfVinExists({
      vin: value,
      enterpriseV2Workspaces,
      enterpriseV2Enabled,
      vinValidateAcrossWorkSpace,
    });
    updateFieldStatus('vin', { ...vinFieldStatus, customError: error || undefined });
  };

  updateVehicleState = () => {
    const { form, actions } = this.props;
    const formCurrentState = _get(form.getState(), 'values');
    actions.updateHeaderDetails(formCurrentState);
  };

  hideReceivedDateChangeModal = () => {
    this.setState({
      showReceivedDateChangeModal: false,
    });
  };

  renderReceivedDataChangeModal = () => {
    const { showReceivedDateChangeModal } = this.state;
    return (
      <ConfirmationDialog
        title={__('Confirm Date Change')}
        isVisible={showReceivedDateChangeModal}
        content={MESSAGES.RECEIVED_DATE}
        onCancel={this.hideReceivedDateChangeModal}
        onSubmit={() => {
          this.changeReceivedDate(this.selectedReceivedDate, 'entryTime');
          this.hideReceivedDateChangeModal();
        }}
        submitBtnText={__('Confirm')}
        secondaryBtnText={__('Cancel')}
      />
    );
  };

  onReceivedDateChange = (date, field) => {
    if (date > getEndOfToday()) {
      this.setState({
        showReceivedDateChangeModal: true,
      });
      this.selectedReceivedDate = date;
      return;
    }
    this.changeReceivedDate(date, field?.name);
  };

  changeReceivedDate = (date, fieldName) => {
    const { form } = this.props;
    const formattedDate = date ? getMomentValueOf(date) : EMPTY_STRING;
    form.change(fieldName, formattedDate);
    this.updateVehicleState();
    this.updateGroupReceivedDate(formattedDate);
  };

  updateGroupReceivedDate = receivedDate => {
    const { form, enterpriseV2Enabled } = this.props;
    const formState = this.getFormState();
    const showEnterpriseAge = shouldShowEnterpriseAge({
      enterpriseV2Enabled,
    });
    const isGroupReceivedDateDisabled = getIsGroupAgingFieldDisabled(formState);
    if (showEnterpriseAge && !isGroupReceivedDateDisabled) {
      form.change('groupReceivedDate', receivedDate);
    }
  };

  handleVINEditIconClick = async () => {
    showConfirmationModal(
      {
        title: __('Edit VIN #'),
        submitBtnText: __('Edit'),
        message: (
          <div className="d-flex">
            <FontIcon className={styles.warning}>icon-exclamation</FontIcon>
            <Content>
              {__(
                'Edited VIN # will be saved only at the inventory and it has to be changed manually in Accounting, Deals & attached ROs.'
              )}
            </Content>
          </div>
        ),
      },
      () => {
        this.isVINEditEnabled = true;
        const { updateFieldStatus, fieldStatus } = this.props;
        const { vin: vinFieldStatus = EMPTY_OBJECT } = fieldStatus || EMPTY_OBJECT;
        updateFieldStatus('vin', { ...vinFieldStatus, disabled: false });
        this.init();
      }
    );
  };

  getVINEditIconPopOverContent = isVinEditEnabled => {
    if (!isVinEditEnabled) {
      return (
        <div className={styles.vinEditIconPopoverContent}>
          {__('Not editable. The vehicle might not have Zero GL balance, or the vehicle is sold')}
        </div>
      );
    }
    return (
      <PropertyControlledComponent controllerProperty={this.isVINEditEnabled}>
        <div className={styles.vinEditIconPopoverContent}>{__('Already in edit mode')}</div>
      </PropertyControlledComponent>
    );
  };

  renderVINEditIcon = isVINEditable => (
    <Popover
      placement="top"
      trigger="hover"
      content={this.getVINEditIconPopOverContent(isVINEditable)}
      overlayClassName={styles.vinEditPopoverContainer}>
      <Icon
        className={classnames({ [styles.iconHover]: isVINEditable && !this.isVINEditEnabled })}
        disabled={!isVINEditable || this.isVINEditEnabled}
        onClick={isVINEditable && !this.isVINEditEnabled ? this.handleVINEditIconClick : _noop}>
        icon-edit
      </Icon>
    </Popover>
  );

  getModelChangeConfirmationContent = () => (
    <div className="d-flex">
      <FontIcon className={styles.warning}>icon-exclamation</FontIcon>
      <Content>{MESSAGES.MODEL_CODE_CHANGE_CONFIRMATION}</Content>
    </div>
  );

  onCancelModelCodeChange = () => {
    const { form } = this.props;
    form.change('modelCode', this.modelCode);
    this.hideModelCodeChangeModal();
  };

  hideModelCodeChangeModal = () => {
    this.setState({ showModelChangeModal: false });
  };

  showModelCodeChangeModal = e => {
    if (this.modelCode !== e.target.value) {
      this.setState({ showModelChangeModal: true });
    }
  };

  renderModelCodeChangeModal = () => {
    const { showModelChangeModal } = this.state;
    return (
      <ConfirmationDialog
        title={__('Edit Model Code')}
        isVisible={showModelChangeModal}
        content={this.getModelChangeConfirmationContent()}
        onCancel={this.onCancelModelCodeChange}
        onSubmit={this.handleModelCodeChange}
        submitBtnText={__('Edit')}
        secondaryBtnText={__('Cancel')}
      />
    );
  };

  handleModelCodeChange = async () => {
    const { actions } = this.props;
    const value = VehicleReader.modelCode(this.getFormState());
    this.hideModelCodeChangeModal();
    if (isStringEmpty(value)) {
      this.modelCode = value;
      actions.addBulkOptions(EMPTY_ARRAY);
      return;
    }
    try {
      const options = await VehicleDetailsAPI.fetchOptions(value);
      actions.addBulkOptions(options || EMPTY_ARRAY);
    } catch (err) {
      toaster(TOASTER_TYPE.ERROR, getErrorMessage(err, __('Options Lookup using Model Code failed')));
    }
    this.modelCode = value;
  };

  getFormState = () => {
    const { form } = this.props;
    return tget(form.getState(), 'values', EMPTY_OBJECT);
  };

  isDirtyFormField = field => {
    const { form } = this.props;
    return _has(tget(form.getState(), 'dirtyFields', EMPTY_OBJECT), field);
  };

  updateFormWithDefaultValue = (defaultValue, fieldKey, selectedValue, defaultValueEnabled) => {
    const { form } = this.props;
    const { defaultValueApplied } = this.state;
    if (!defaultValueApplied[fieldKey] && !selectedValue && defaultValueEnabled) {
      this.setState(
        {
          defaultValueApplied: {
            ...defaultValueApplied,
            [fieldKey]: true,
          },
        },
        () => form.change(fieldKey, _head(defaultValue))
      );
    }
  };

  getExteriorColorPopoverContent = (hexCode, baseColor) => (
    <div className={styles.exteriorColorPopoverContent}>
      <div className="d-flex flex-row">
        <Label>{__('Hexcode:')}</Label>
        <Content className="m-l-24">{hexCode}</Content>
      </div>
      <div className="d-flex flex-row">
        <Label>{__('Base Color:')}</Label>
        <Content className={styles.baseColor}>{baseColor}</Content>
      </div>
    </div>
  );

  renderExteriorColorPopover = () => {
    const hexCode = VehicleReader.extColorHexCode(this.getFormState());
    const baseColor = VehicleReader.extBaseColor(this.getFormState());
    return (
      <Popover placement="top" trigger="hover" content={this.getExteriorColorPopoverContent(hexCode, baseColor)}>
        <FontIcon size={SIZES.MD} className="cursor-pointer" color={hexCode}>
          {hexCode === '-' ? 'icon-circle-outline' : 'icon-circle-filled'}
        </FontIcon>
      </Popover>
    );
  };

  setVehicleConfiguratorValue = enabled => {
    const { form } = this.props;
    form.change('vehicleConfiguratorFormValue', enabled);
    this.init();
  };

  updateVehicleDetails = formFields => {
    const { form, actions } = this.props;
    const optionsField = _find(formFields, { key: 'options' });
    const filteredFields = _filter(formFields, field => !_includes(['options'], field?.key));
    if (_size(optionsField)) {
      actions.addBulkOptions(optionsField?.value);
    }
    updateVehicleForm(form, filteredFields);
  };

  disableVehicleConfigurator = () => {
    this.setVehicleConfiguratorValue(false);
  };

  onOrderNumberChange = async value => {
    const { updateFieldStatus, fieldStatus, vehicleId } = this.props;
    const { orderNumber: orderNumberStatus = EMPTY_OBJECT } = fieldStatus || EMPTY_OBJECT;
    if (!_isEmpty(value)) {
      const payload = searchOrderNumberPayload(value);
      const { data } = await VehicleService.fetchVehicles(payload);
      const count = ESReader.count(data);
      const hits = ESReader.hits(data);
      const checkIfVehicleIdIsPresent = _some(hits, { id: vehicleId });
      const error = !checkIfVehicleIdIsPresent && count > 0 ? ERRORS.DUPLICATE_ORDER_NUMBER : undefined;
      updateFieldStatus('orderNumber', { ...orderNumberStatus, customError: error });
    } else {
      updateFieldStatus('orderNumber', { ...orderNumberStatus, customError: undefined });
    }
  };

  multiLingualFieldChange = field => value => {
    const targetValue = _isObject(value) ? EventReader.targetValue(value) : value;
    const { form, currentLanguageId } = this.props;
    const nestingPath = field?.nestingPath;
    const id = field?.multilingualId;
    form.change(field?.name, targetValue);
    if (nestingPath) {
      form.change(`${nestingPath}.languages.locale.${currentLanguageId}.${id}`, targetValue);
    } else {
      form.change(`languages.locale.${currentLanguageId}.${id}`, targetValue);
    }
  };

  getVehicleConfiguratorEnabled = () => {
    const { isEditMode, isVehicleConfiguratorEnabled, options, styleId } = this.props;
    const vehicleConfiguratorFormValue = tget(
      this.getFormState(),
      'vehicleConfiguratorFormValue',
      isVehicleConfiguratorEnabled
    );
    return checkIfVehicleConfiguratorEnabled(
      isVehicleConfiguratorEnabled,
      vehicleConfiguratorFormValue,
      options,
      styleId,
      isEditMode
    );
  };

  resetTrimDetails = () => {
    const { form } = this.props;
    const vehicleConfiguratorEnabled = this.getVehicleConfiguratorEnabled();
    if (vehicleConfiguratorEnabled) {
      form.change('trimDetails', _get(getFormInitialState(), 'trimDetails'));
      form.change('styleId', EMPTY_STRING);
      form.change('chromeSerializedValue', EMPTY_STRING);
    }
  };

  resetModel = () => {
    const { form, displayModelSource } = this.props;
    form.change('displayModel', EMPTY_STRING);
    form.change('model', EMPTY_STRING);
    if (displayModelSource === BEST_STYLE_NAME) form.change('bestStyleName', EMPTY_STRING);
  };

  handlePopulateGroupAgingFields = () => {
    const { form, enterpriseV2Enabled } = this.props;
    const formState = this.getFormState();
    const showEnterpriseAge = shouldShowEnterpriseAge({
      enterpriseV2Enabled,
    });
    if (!showEnterpriseAge) return;

    const { stockedInTime, entryTime, vehicleSoldTime, groupReceivedDate, groupStockedInTime, groupSoldTime } =
      formState || EMPTY_OBJECT;
    if (!groupReceivedDate) form.change('groupReceivedDate', entryTime);
    if (!groupStockedInTime) form.change('groupStockedInTime', stockedInTime);
    if (!groupSoldTime) form.change('groupSoldTime', vehicleSoldTime);
  };

  init = () => {
    const {
      fields,
      isEditMode,
      onPrint,
      form,
      metaData,
      getModelsList,
      refreshStockId,
      optionsLookupEnable,
      getGlAccountFromSettings,
      getStockID,
      formData = EMPTY_OBJECT,
      originalMakes,
      allMakes,
      displayModelSource,
      onAction,
      getUserInfoById,
      customerViewProps,
      enableUsedVehiclesPricingReset,
      selectedVehicleType,
      removeDefaultOptions,
      enableStockNumberGenerationOnSave,
    } = this.props;
    const formState = this.getFormState();
    const { selectedModel, selectedMake, selectedYear, source, mileageStatus, selectedBrand } = formData;
    const dealerSiteOptions = _get(metaData, 'dealerSiteOptions') || EMPTY_ARRAY;
    const dealerData = _get(metaData, 'dealerMasterData', {}) || {};
    const stateOptions = _get(metaData, 'stateOptions') || EMPTY_ARRAY;
    const modelList = tget(metaData, 'modelList', EMPTY_ARRAY);

    const standardMakes = isStandardMakeEnabled();
    let allMakeOptions = tget(metaData, 'makes', EMPTY_ARRAY);
    if (selectedMake && !_includes(allMakes, selectedMake)) {
      allMakeOptions = _concat({ label: capitalizeFirstLetterOnly(selectedMake), value: selectedMake }, allMakeOptions);
    }

    const brandList = getModifiedOptionsList(
      getAllBrands(modelList, [selectedMake], selectedYear, standardMakes),
      selectedBrand
    );
    const modelOptions = getModifiedOptionsList(
      getModelsList({
        makes: [selectedMake],
        selectedYear,
        selectedBrand,
        displayModelSource,
        standardMakes,
      }),
      selectedModel
    );
    this.checkStylesPresentForSelectedYMM();

    const originalModelValue = getVehicleModel({
      models: tget(metaData, 'modelList', EMPTY_ARRAY),
      year: selectedYear,
      make: selectedMake,
      selectedModel,
    });

    const generalFields = _map(fields, field => {
      const formField = GENERAL_FORM_FIELDS.find(item => item.key === field.key) || {};
      let generalField = { ...field, ...formField };
      let mileageStatusDefaultValue = mileageStatus;

      const { defaultValue, defaultValueEnabled } = generalField;
      delete generalField.defaultValue;

      if (!_isEmpty(formField)) {
        switch (generalField.key) {
          case 'VIN':
            generalField.onChangeCb = e => this.onVinChangeCb(e.target.value);
            if (!isEditMode) {
              break;
            }
            // eslint-disable-next-line no-case-declarations
            const isVINEditable = checkIfVINEnabled(form);
            generalField.disabled = true;
            if (!dealerPropertyHelper.isViArcLiteEnabled()) {
              generalField.addonAfter = this.renderVINEditIcon(isVINEditable);
            }
            break;
          case 'PRODUCTION':
            // TODO - replace both addOnAfter and addOnBefore with IconAsBtn
            const shouldDisablePrintIcon = !hasLabelPrintPermission();
            const isStockFieldDisabled = !hasStockIdEdit() || dealerPropertyHelper.isViArcLiteEnabled();
            generalField.addonAfter = (
              <Tooltip
                title={shouldDisablePrintIcon ? MESSAGES.LABEL_PRINT_MESSAGE : EMPTY_STRING}
                placement={TOOLTIP_PLACEMENT.TOP_RIGHT}
                arrowPointAtCenter>
                <Icon onClick={onPrint} disabled={dealerPropertyHelper.isViArcLiteEnabled() || shouldDisablePrintIcon}>
                  icon-printer
                </Icon>
              </Tooltip>
            );
            generalField.addonBefore = (
              <Button
                onClick={refreshStockId}
                view={Button.VIEW.ICON}
                icon="icon-refresh"
                disabled={isStockFieldDisabled}
              />
            );
            generalField.onChange = e => this.onFieldChange(e.target.value, generalField);
            generalField.onFocus = e => this.onFieldFocus(e.target.value, generalField);
            break;
          case 'MILEAGE':
            if (generalField.mandatory) {
              generalField.validation = composeValidators(generalField.validation, validateNonZeroNumberField);
            }
            generalField.onChangeCb = this.updateVehicleState;
            generalField.addonBefore = __('{{mileageLabel}}', {
              mileageLabel: mileageLabelUtil.getMileageUnitLabel(),
            });
            break;
          // ToDo:  If user select ‘'Pro VO’' create a warn to ask user if want to transfert the vehicle to PRO VO
          // case 'SALES_DESTINATION':
          //   break;
          case 'MILEAGE_STATUS':
            if (!isEditMode && _isEmpty(mileageStatusDefaultValue)) {
              mileageStatusDefaultValue = MILEAGE_STATUS.ACTUAL;
            }
            generalField.defaultValue = mileageStatusDefaultValue;
            generalField.onChange = value => {
              form.change('mileageStatus', value);
            };
            generalField.validation = undefined;
            generalField.options = getMileageStatusOptions();
            break;
          case 'SOURCE': {
            const selectedSource = !source ? EMPTY_ARRAY : _castArray(source);
            generalField.options = getSourceOptions(_castArray(field), selectedSource);
            generalField.onChange = this.handleSourceChange;
            let sourceLabel = generalField.label;
            // TODO: change this once expereince solution is developed
            if (isRRG()) sourceLabel = __('Origin');
            if (isInchcape()) sourceLabel = __('Purchase Source');
            generalField.label = sourceLabel;
            break;
          }
          case 'LICENSE_PLATE_NUMBER': {
            let licensePlateNumber = generalField.label;
            if (isRRG()) licensePlateNumber = __('Registration Number');
            generalField.label = licensePlateNumber;
            break;
          }
          case 'PICTURES_COUNT':
            generalField.defaultValue = getPicturesCount;
            break;
          case 'BODY_CLASS':
            generalField.onChange = value => {
              form.change(VEHICLE_KEYS.BODY_CLASS, value);
              if (isRVDealerEnabled()) {
                const rvDetails = tget(metaData, 'rvDetails', EMPTY_OBJECT);
                const bodyClassObj = rvDetails?.[value];
                form.change(VEHICLE_KEYS.ORIGINAL_TYPE_CODE, bodyClassObj?.originalTypeCode);
                form.change(VEHICLE_KEYS.RV_TYPES, bodyClassObj?.rvType);
              }
              if (!isEditMode) {
                getGlAccountFromSettings();
              }
            };
            break;
          case 'MARKETABLE':
          case GENERAL_FIELD_KEYS.READY_TO_GO:
          case 'DOUBLE_OF_KEYS':
          case 'VEHICLE_ASSIGNED':
          case 'BILLS':
          case 'FIRST_HAND_OWNER':
          case 'REGISTRATION_CARD_PAYABLE':
          case 'BONUS_ELGIBLE':
          case 'VAT_UV_ONLY':
            generalField.options = _map(field.options, option => ({
              ...option,
              value: _get(option, 'name') === 'Yes',
            }));
            break;
          case 'RECOMMENDED':
            generalField.options = getYesNoOptions(field?.options);
            break;
          case 'LOCATION_CODE':
            generalField.options = getFieldOptions(field?.options);
            break;
          case 'YEAR':
            generalField.options = _map(getYearOptions(1920, +getCurrentYearValue() + 3), option => ({
              ...option,
              name: option?.value,
            }));

            generalField.disabled = !hasYMMEdit();
            generalField.onChange = value => {
              form.change(VEHICLE_KEYS.YEAR, value);
              this.resetModel();
              this.resetTrimDetails();
              this.updateVehicleState();
            };

            this.updateFormWithDefaultValue(defaultValue, VEHICLE_KEYS.YEAR, selectedYear, defaultValueEnabled);

            break;
          case 'MAKE':
            if (!standardMakes) {
              generalField = {
                ...generalField,
                component: ComponentWrapper,
                renderer: 'CreatableSelect',
                format: val => (val ? _castArray(val) : val),
                errorClassName: 'relative',
                parse: val => (val ? _head(val) : val),
                createOptionPosition: 'first',
                isClearable: true,
                isDisabled: !hasYMMEdit(),
                options: allMakeOptions,
                onCreateOption: this.onCreateNewOption('make'),
                onAction: this.onOptionChange('make'),
              };
            } else {
              generalField = {
                ...generalField,
                component: SelectWrapper,
                showSearch: true,
                filterOption: true,
                optionFilterProp: 'children',
                allowClear: true,
                disabled: !hasYMMEdit(),
                options: allMakeOptions,
                onChange: value => {
                  form.change('make', value);
                  const filteredMake = _find(originalMakes, { tekionMakeId: value });
                  form.change('displayMake', _get(filteredMake, TEKION_MAKE_DISPLAY_NAME));
                  form.change('makeId', _get(filteredMake, TEKION_MAKE_ID));
                  this.resetModel();
                  this.resetTrimDetails();
                },
              };
            }

            this.updateFormWithDefaultValue(defaultValue, VEHICLE_KEYS.MAKE, selectedMake, defaultValueEnabled);

            break;
          case 'LAST_MODIFIED_USER':
            const getUserDisplayName = val => {
              const { value } = getLastUpdatedUser(val, getUserInfoById) || {};
              return value;
            };
            generalField = {
              ...generalField,
              format: getUserDisplayName,
            };
            break;
          case 'BRAND':
            generalField.options = brandList;
            generalField.onCreateOption = this.onCreateNewOption('trimDetails.brand');
            generalField.onAction = this.onOptionChange('trimDetails.brand');
            break;
          case 'TAGS':
            generalField.defaultValue = VehicleReader.tags(this.getFormState());
            break;
          case 'MODEL':
            const bestStyleName = VehicleReader.bestStyleName(this.getFormState());
            const displayModel = VehicleReader.displayModel(this.getFormState());
            generalField.name = getDisplayModelSourceLabel(displayModelSource, bestStyleName, displayModel);
            generalField.isDisabled = !hasYMMEdit();
            generalField.options = modelOptions;
            generalField.onCreateOption = this.onCreateNewOption('displayModel');
            generalField.onAction = this.onOptionChange('displayModel');
            break;
          case 'TELEMATICS_SERVICE_FLAG':
            generalField.options = _map(field.options, option => ({
              ...option,
              value: _get(option, 'name') === 'Yes',
            }));
            break;
          case 'PREVIOUS_USE':
            generalField.options = _map(field.options, option => ({
              ...option,
              label: capitalizeFirstLetters(option.name),
            }));
            break;
          case 'RECEIVED_DATE':
            generalField.onChange = date => this.onReceivedDateChange(date, generalField);
            generalField.disabled = !hasVehicleEditDates();
            break;
          case 'MODEL_CODE':
            const status = VehicleReader.status(formState);
            const isAccountPosted = VehicleReader.accountPosted(formState);
            this.modelCode = VehicleReader.modelCode(formState);
            if (
              !isAccountPosted &&
              !_includes([VEHICLE_STATUS.STOCKED_IN, VEHICLE_STATUS.SOLD], status) &&
              optionsLookupEnable
            ) {
              generalField.onBlurCb = this.showModelCodeChangeModal;
            }
            break;
          case 'EXTERIOR_COLOR':
            generalField.addonAfter = !isEditMode ? null : this.renderExteriorColorPopover();
            generalField.disabled = isEditMode && !VehicleReader.exteriorColorEditable(this.getFormState());
            break;
          case GENERAL_FIELD_KEYS.ORDER_NUMBER:
            generalField.onBlurCb = e => this.onOrderNumberChange(e.target.value);
            break;
          case GENERAL_FIELD_KEYS.TITLE_TYPE:
            generalField.options = _map(_get(field, 'options'), ({ name, ...rest }) => ({
              ...rest,
              name,
              label: capitalizeFirstLetters(name),
            }));
            break;
          case GENERAL_FIELD_KEYS.TITLE_STATE:
            generalField.options = _map(stateOptions, state => ({ ...state, name: _get(state, 'value') }));
            break;
          case GENERAL_FIELD_KEYS.STOCKED_IN_AT_SITE_ID:
          case GENERAL_FIELD_KEYS.SOLD_AT_SITE_ID:
            const siteId = _get(this.getFormState(), [generalField.name]);
            generalField.options = getUserAccessibleSitesList(dealerSiteOptions, siteId);
            if (!isEditMode && generalField.key === GENERAL_FIELD_KEYS.STOCKED_IN_AT_SITE_ID) {
              generalField.onChange = value => {
                form.change(VEHICLE_KEYS.STOCKED_IN_AT_SITE_ID, value);
                getGlAccountFromSettings();
                getStockID();
              };
            }
            break;
          case GENERAL_FIELD_KEYS.TRIM_CODE:
            generalField.onChange = event => {
              form.change('trimDetails.trim', event?.target?.value); // trim code update
              const { trimDetails } = _get(form.getState(), 'values'); // Trim details modal update
              const trimsToDisplay = transformObjectValuesToString(getTrimDetails(trimDetails));
              form.change('trim', trimsToDisplay);
              this.updateVehicleState(); // updating header
            };
            break;
          case GENERAL_FIELD_KEYS.TRIM: {
            const vehicleConfiguratorEnabled = this.getVehicleConfiguratorEnabled();
            if (vehicleConfiguratorEnabled) {
              const dealTypeValue = getDealType();
              generalField = {
                component: ComponentWrapper,
                name: 'styleId',
                year: selectedYear,
                model: originalModelValue || selectedModel,
                make: selectedMake,
                label: __('Trim'),
                renderer: 'ChromeTrimSelect',
                className: `p-y-12 ${styles.trimField}`,
                disableVehicleConfigurator: this.disableVehicleConfigurator,
                updateVehicleDetails: this.updateVehicleDetails,
                dealType: dealTypeValue || 'Retail',
                isVehicleUsed: selectedVehicleType === VEHICLE_TYPES.USED && enableUsedVehiclesPricingReset,
                isEditMode,
                removeDefaultOptions,
                shouldHideInvoiceTooltip: !hasInventoryAllCostPricesViewPermission(),
              };
            }
            break;
          }
          case GENERAL_FIELD_KEYS.HITCH_WEIGHT:
          case GENERAL_FIELD_KEYS.UNLADEN_WEIGHT:
          case GENERAL_FIELD_KEYS.GROSS_VEHICLE_WEIGHT:
            generalField.addonAfter = __('{{weightMeasure}}', {
              weightMeasure: weightLabelUtil.getWeightMeasureUnitLabel(),
            });
            break;
          case GENERAL_FIELD_KEYS.CURRENT_MODEL:
            generalField.options = getYesNoOptions(field?.options);
            break;
          case GENERAL_FIELD_KEYS.EXCLUSIVE_PRODUCT:
            generalField.options = getYesNoOptions(field?.options);
            break;
          case GENERAL_FIELD_KEYS.DEALER_TRADE:
            generalField.options = getYesNoOptions(field?.options);
            break;
          case GENERAL_FIELD_KEYS.PREOWNED_REGISTERED:
            generalField.options = getYesNoOptions(field?.options);
            break;
          case GENERAL_FIELD_KEYS.STOP_DELIVERY_INDICATOR:
            generalField.options = getYesNoOptions(field?.options);
            break;
          case GENERAL_FIELD_KEYS.DAYS_TO_SELL:
            form.change('vehicleAdditionalDetails.daysToSell', getDaysTosell(this.getFormState()));
            break;
          case GENERAL_FIELD_KEYS.DAYS_ON_LOT:
            form.change('vehicleAdditionalDetails.daysOnLot', getDaysOnLot(this.getFormState()));
            break;
          case 'BATTERY_PURCHASE_TYPE':
            generalField.options = _values(BATTERY_PURCHASE_TYPE_CONFIG);
            break;
          case 'INVOICE_TYPE':
            generalField.options = _values(INVOICE_TYPE_CONFIG);
            break;
          case 'ASSIGNED_VEHICLE_DETAILS':
            generalField.onAction = this.onOptionChange('assignedVehicleDetails');
            break;
          case GENERAL_FIELD_KEYS.PROVIDER_ORDER_STATUS:
            generalField.label = field?.name;
            break;
          case GENERAL_FIELD_KEYS.GROUP_RECEIVED_DATE:
          case GENERAL_FIELD_KEYS.GROUP_STOCKED_IN_DATE:
          case GENERAL_FIELD_KEYS.GROUP_SOLD_DATE:
            this.handlePopulateGroupAgingFields();
            if (generalField.key !== GENERAL_FIELD_KEYS.GROUP_SOLD_DATE) {
              generalField.disabled = getIsGroupAgingFieldDisabled(formState);
            }
            break;
          default:
            break;
        }
        if (dealerPropertyHelper.isViArcLiteEnabled()) {
          generalField.disabled = true;
          generalField.isDisabled = true;
        }
        if (generalField.isMultiLingual && isMultiLingualEnabled()) {
          const fieldName = _get(field, 'name');
          generalField.multiLingualLabel = fieldName;
          generalField.onAction = onAction;
          generalField.getMultiLingualFieldValue = this.getMultiLingualFieldValue;
          generalField.onChange = this.multiLingualFieldChange(generalField);
        } else {
          generalField.isMultiLingual = false;
        }
        return formFieldReader(generalField, field, { enableStockNumberGenerationOnSave, isEditMode });
      }
      generalField = { ...field, component: CustomFieldsComponent({ type: field.fieldType }) };
      return customFieldReader(generalField, field);
    });

    this.setState({
      fields: _reject(
        generalFields,
        field =>
          field?.shouldShow &&
          !field?.shouldShow({
            vehicleStatus: VehicleReader.status(this.getFormState()),
            stockType: VehicleReader.vehicleType(this.getFormState()),
            dealerData,
            fieldKey: field?.key,
            ...customerViewProps,
          })
      ),
    });
  };

  getMultiLingualFieldValue = ({ values, nestingPath, fieldId, localeId }) => {
    if (nestingPath) {
      return tget(values, [nestingPath, 'languages', 'locale', localeId, fieldId], EMPTY_STRING);
    }
    return tget(values, ['languages', 'locale', localeId, fieldId], EMPTY_STRING);
  };

  onOptionChange =
    field =>
    ({ payload, type }) => {
      if (type === ACTION_TYPES.ON_FIELD_CHANGE) {
        const { form, displayModelSource } = this.props;
        const { value, option } = payload;
        form.change(field, _head(value));
        if (field === 'displayModel') {
          form.change('model', _head(value));
          if (displayModelSource === BEST_STYLE_NAME) {
            form.change('bestStyleName', _head(value));
          }
        }

        if (field === 'make' && !isStandardMakeEnabled()) {
          const label = tget(option, 'label', _head(value));
          form.change('displayMake', label);
          this.resetModel();
        }

        if (field === 'make' || field === 'displayModel') {
          this.resetTrimDetails();
          this.updateVehicleState();
        }
      }
    };

  checkStylesPresentForSelectedYMM = async () => {
    const { options, isVehicleConfiguratorEnabled } = this.props;
    const vehicleConfiguratorFormValue = tget(
      this.getFormState(),
      'vehicleConfiguratorFormValue',
      isVehicleConfiguratorEnabled
    );
    if (isVehicleConfiguratorEnabled && !vehicleConfiguratorFormValue && !options?.length) {
      const { formData } = this.props;
      const dealTypeValue = getDealType();
      const { selectedModel, selectedMake, selectedYear, selectedVehicleType, enableUsedVehiclesPricingReset } =
        formData || EMPTY_OBJECT;

      const rawResponse = await ChromeApi.fetchStyles({
        model: selectedModel,
        year: selectedYear,
        make: selectedMake,
        orderAvailability: dealTypeValue || 'Retail',
        isVehicleUsed: selectedVehicleType === VEHICLE_TYPES.USED && enableUsedVehiclesPricingReset,
      });
      if (!_isEmpty(tget(rawResponse, 'data.style', EMPTY_OBJECT))) {
        this.setVehicleConfiguratorValue(true);
      }
    }
  };

  onCreateNewOption = field => option => {
    this.onOptionChange(field)({ payload: { value: _castArray(_trim(option)) }, type: ACTION_TYPES.ON_FIELD_CHANGE });
  };

  renderMultiLingualWindow = () => {
    const { multiLingualFieldDetails, multiLingualFormProps, onAction, languageOptions, currentLanguageId } =
      this.props;
    const { fields } = this.state;

    return (
      <MultiLingualFieldWindowWrapper
        multiLingualFieldDetails={multiLingualFieldDetails}
        multiLingualFormProps={{
          ...multiLingualFormProps,
          languageOptions,
          currentLanguageId,
        }}
        onAction={onAction}
        fields={fields}
        values={this.getFormState()}
      />
    );
  };

  handleTestDriveChange = ({ payload }) => {
    const { form } = this.props;
    form.change('inTestDrivePool', payload.value);
  };

  renderTestDrivePoolSwitch = () => {
    const { form } = this.props;
    const { inTestDrivePool } = _get(form.getState(), 'values');
    return (
      <PropertyControlledComponent controllerProperty={PROGRAM_CONFIG.shouldRenderTestDrivePoolSwitch()}>
        <Switch
          value={inTestDrivePool}
          onAction={this.handleTestDriveChange}
          fieldClassName="m-b-20"
          labelPosition={LABEL_POSITIONS.BEFORE}
          label={__('Add To Test Drive Pool ')}
        />
      </PropertyControlledComponent>
    );
  };

  render() {
    const { fieldStatus, form } = this.props;
    const { fields } = this.state;
    const vehicleConfiguratorEnabled = this.getVehicleConfiguratorEnabled();

    const filterFields = {
      TRIM_CODE: vehicleConfiguratorEnabled,
    };

    return (
      <div className={styles.generalSection}>
        <Heading size={3} className="m-y-16">
          {__('General')}
        </Heading>
        {this.renderTestDrivePoolSwitch()}
        <div className={styles.generalFormContent}>
          {renderFormData(
            fields,
            fieldStatus,
            {
              form,
              vehicleConfiguratorEnabled,
            },
            filterFields
          )}
        </div>
        {this.renderModelCodeChangeModal()}
        {this.renderReceivedDataChangeModal()}
        {this.renderMultiLingualWindow()}
        <TrimModal form={form} />
      </div>
    );
  }
}

function mapStateToProps({ vi: state }) {
  return {
    isVehicleConfiguratorEnabled: getVehicleConfiguratorEnabled(state),
    styleId: tget(state, 'vehicleDetails.general.styleId', EMPTY_STRING),
    originalMakes: tget(state, 'settings.metaData.originalAllMakes', EMPTY_OBJECT),
    allMakes: tget(state, 'settings.metaData.allMakes', EMPTY_ARRAY),
    enableUsedVehiclesPricingReset: _get(state, 'inventory.salesSetup.enableUsedVehiclesPricingReset'),
    removeDefaultOptions: tget(state, 'inventory.salesSetup.removeDefaultOptions', false),
  };
}

export default compose(
  connect(mapStateToProps),
  withMetaData,
  withActionHandlers(ACTION_HANDLERS, EMPTY_OBJECT),
  withMultiLingualInfo
)(GeneralForm);
