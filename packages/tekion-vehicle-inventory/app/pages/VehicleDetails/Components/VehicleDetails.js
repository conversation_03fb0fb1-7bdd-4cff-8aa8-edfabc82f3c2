/* eslint-disable react/sort-comp */
import React, { PureComponent } from 'react';
import Proptype from 'prop-types';
import { Form } from 'react-final-form';
import arrayMutators from 'final-form-arrays';
import { compose } from 'recompose';
import { defaultMemoize } from 'reselect';
import setFieldTouched from 'final-form-set-field-touched';
import cx from 'classnames';
import queryString from 'query-string';

import _castArray from 'lodash/castArray';
import _find from 'lodash/find';
import _get from 'lodash/get';
import _head from 'lodash/head';
import _includes from 'lodash/includes';
import _isArray from 'lodash/isArray';
import _isEmpty from 'lodash/isEmpty';
import _isEqual from 'lodash/isEqual';
import _isNil from 'lodash/isNil';
import _keyBy from 'lodash/keyBy';
import _last from 'lodash/last';
import _map from 'lodash/map';
import _noop from 'lodash/noop';
import _omit from 'lodash/omit';
import _omitBy from 'lodash/omitBy';
import _partial from 'lodash/partial';
import _set from 'lodash/set';
import _size from 'lodash/size';
import _values from 'lodash/values';

import { EMPTY_OBJECT, EMPTY_ARRAY, EMPTY_STRING } from 'tbase/app.constants';
import { ACTIONS as PDF_ACTIONS } from 'tbase/constants/pdfActions';
import NOTES_ASSET_TYPES from 'tbase/constants/notesAssetTypes';
import { SALES_PRINTING_MODULE_NAMES } from 'tbase/constants/pdf.constants';
import { PRINTER_MESSAGES } from 'tbase/constants/printer.constants';
import { VEHICLE_TYPES } from 'tbase/constants/vehicleInventory/vehicleTypes';
import { SOURCE_ENUM } from 'tbase/constants/vehicleInventory/sourceType';
import { getDisplayModalText } from 'tbase/helpers/vehicle.helper';
import TEnvReader from 'tbase/readers/Env';
import { printFileV2 } from 'tbase/services/printerService';
import { getErrorMessage } from 'tbase/utils/errorUtils';
import { formMutator } from 'tbase/utils/formHelper';
import { tget } from 'tbase/utils/general';
import { isRRG, isInchcapeOrRRG } from 'tbase/utils/sales/dealerProgram.utils';
import GlobalAnalytics from 'tbase/utils/GlobalAnalytics';
import VehicleReader from 'tbusiness/appServices/vehicleInventory/readers/vehicle';
import Button from 'tcomponents/atoms/Button';
import Content from 'tcomponents/atoms/Content';
import Label from 'tcomponents/atoms/Label';
import Tooltip, { TOOLTIP_PLACEMENT } from 'tcomponents/atoms/tooltip';
import FontIcon, { SIZES } from 'tcomponents/atoms/FontIcon';
import Spinner from 'tcomponents/molecules/SpinnerComponent';
import Modal from 'tcomponents/molecules/Modal';
import Switch from 'tcomponents/molecules/Switch';
import Page from 'tcomponents/molecules/pageComponent/PageComponent';
import SaveComponent from 'tcomponents/molecules/SaveComponent';
import Tabs from 'tcomponents/molecules/Tabs/Tabs';
import withSize from 'tcomponents/hoc/withSize';
import AuditLogs from 'tcomponents/organisms/AuditLogs/AuditLogs';
import { ASSET_TYPES } from 'tcomponents/organisms/AuditLogs/AuditLogs/AuditLogs.constants';
import { toaster, TOASTER_TYPE } from 'tcomponents/organisms/NotificationWrapper';
import DealerPropertyHelper from 'tcomponents/helpers/sales/dealerPropertyHelper';
import PropertyControlledComponent from 'tcomponents/molecules/PropertyControlledComponent';
import { getScaleAdjustedSize } from 'tcomponents/utils/general';
import StockCardButton from 'molecules/StockCardButton';
import withMultiLingualInfo from 'twidgets/hocs/withMultiLingualInfo';
import { FEATURE_NAME, withExperienceEngineConsumer } from 'twidgets/experienceEngine';

import { LAYOUT } from 'appConfig';
import { TAB_ENUMS, VEHICLE_TYPE } from 'constants/constantEnum';
import {
  VEHICLE_STATUS,
  BODY_TYPE_URLS,
  MESSAGES,
  NO_INVOICE_MESSAGE,
  INVOICE_PREVIEW_TOOLTIP_TIMER,
  PRICE_FORMAT_KEYS,
  LABELS,
  VEHICLE_ACTIONS,
  ES_SYNC_DELAY,
  INCOMING_CAR_RECORD_STATUS,
  DECLARATION_STATUS,
  PAGES,
} from 'constants/constants';
import PROGRAM_CONFIG from 'constants/programConfig';
import { getAdditionalCostAPIPayload, getRenderInfoModalData } from 'factories/vehicle';
import { getVehicleStoreType, isCRMEnabled } from 'helpers/common';
import { getNotesModalProps } from 'helpers/notes';
import { getPrintPayload } from 'helpers/print';
import { handleVehicleDetailsBackNavigation } from 'helpers/route';
import {
  getTrimDetails,
  isVehicleTypeNew,
  hasValuation,
  shouldFetchValuationDetails,
  checkValuationSavedForProviders,
  hasAppraisals,
  shouldPostToAccounting,
  checkIfVINHasSoldVehicle,
  isRVDealerEnabled,
  shouldReadOnlyDetails,
  handleStartDealForVehicles,
  isLoggedInDealerVehicle,
  isVehicleEligibleForStartDeal,
  shoulRenderInfoModal,
  getDetailsPageStickyBannerData,
} from 'helpers/vehicle.helper';
import { shouldGenerateStockIdOnSave } from 'helpers/vehicleDetails.helper';
import NotesModal from 'molecules/NotesModal';
import TabBody from 'molecules/TabBody/TabBody';
import TabHeader from 'molecules/TabHeader/TabHeader';
import CarRecord from 'organisms/CarRecord';
import DeclarationRecord from 'organisms/DeclarationRecord';
import ConfirmationModal, { showConfirmationModal } from 'organisms/ConfirmationModal';
import OptionSheetDrawer from 'organisms/OptionSheetDrawer';
import Portal from 'organisms/Portal';
import PostToAccountingModal, { showPostToAccountingModal } from 'organisms/PostToAccountingModal';
import VehicleAlertDrawer from 'organisms/VehicleAlertDrawer';
import OptionsPriceSummaryFooter from 'organisms/OptionsPriceSummaryFooter';
import InvoiceReader from 'readers/invoice';
import SettingsReader from 'readers/settings';
import PoliceRecordReader from 'readers/policeRecord';
import VehicleAPI from 'services/vehicle';
import PermissionHelper from 'permissions/permissions.helper';
import { hasViewInvoice, hasVehiclePostingPermission } from 'permissions/inventory.permissions';
import { transformObjectValuesToString, omitKeys } from 'utils';
import { getPriceFieldsFormat, parsePriceFieldValue } from 'utils/fieldFormatter';
import { getAppSentryPayload } from 'utils/commonUtils';

import Header from './Header';
import ValuationAPI from './Valuation/Valuation.api';
import VINConfirmationModal, { showVINConfirmationModal } from './VINConfirmationModal';
import { generateStockId } from './General/general.actions';
import VehicleDetailsAPI from '../vehicleDetails.api';
import { SHARE_MENU_KEYS, EXCULDED_PRICING_DETAILS_KEYS_ON_SAVE } from '../VehicleDetails.constant';
import {
  getSaveAppraisalPayload,
  getSaveValuationPayload,
  getTargetingPayload,
  parseOptionsData,
  sanitizeVehicleDetailsPayload,
} from '../VehicleDetails.factory';
import {
  validateTabErrors,
  isValidStockTypeForDraftStatusSave,
  getGlAccountOptions,
  isVehicleSubTypeRequired,
  checkValidVinLength,
  getFormInitialState,
  isPurchaseInvoiceApplicable,
  isPurchaseInvoiceAvailableToSave,
  isPostingAllowedFromPurchaseInvoice,
  filterAdditionalCostDataForProgram,
} from '../VehicleDetails.helpers';
import VehicleDetailsReader from '../VehicleDetails.reader';
import {
  TAB_KEY_VS_TAB_RENDERER,
  getVehicleDetailsTabConfig,
  getVehicleDetailsTabs,
} from '../VehicleDetails.tabConfig';
import PreviewContainer from './InvoicePreview/invoicePreviewContainer';

import styles from './VehicleDetails.module.scss';

const MUTATORS = {
  ...arrayMutators,
  formMutator,
  setFieldTouched,
};

const headerHeight = LAYOUT.PAGE_HEADER + LAYOUT.TAB_HEIGHT + LAYOUT.MARGIN;

const CACHE_NAME = '3d-model';
class VehicleDetails extends PureComponent {
  static propTypes = {
    actions: Proptype.object,
    appraisalDetails: Proptype.object,
    contentHeight: Proptype.number,
    dealerConfig: Proptype.object,
    vehicleDetails: Proptype.object,
    vehicleDetailsInStore: Proptype.object,
    isEditMode: Proptype.bool,
    bodyTypeMapping: Proptype.array.isRequired,
    valuationDetails: Proptype.object.isRequired,
    providerList: Proptype.array.isRequired,
    vehicleTypes: Proptype.array,
    isVehicleSubTypeMandatory: Proptype.object,
    policeRecordData: Proptype.object,
    customerViewProps: Proptype.object,
    navigate: Proptype.func.isRequired,
    location: Proptype.object.isRequired,
    params: Proptype.object.isRequired,
    enableStockNumberGenerationOnSave: Proptype.bool,
  };

  static defaultProps = {
    actions: EMPTY_OBJECT,
    appraisalDetails: EMPTY_OBJECT,
    contentHeight: LAYOUT.PAGE_HEADER,
    dealerConfig: EMPTY_OBJECT,
    vehicleDetails: EMPTY_OBJECT,
    vehicleDetailsInStore: EMPTY_OBJECT,
    isEditMode: false,
    vehicleTypes: EMPTY_ARRAY,
    isVehicleSubTypeMandatory: EMPTY_OBJECT,
    policeRecordData: EMPTY_OBJECT,
    customerViewProps: EMPTY_OBJECT,
    enableStockNumberGenerationOnSave: false,
  };

  validate = _noop;

  openDialog = _noop;

  considerLookupFailureForVehicleSave = false;

  constructor(props) {
    super(props);
    const { location, isEditMode, getFeatureValue } = props;
    const { dealerId } = this.getQueryParams(location);
    this.enterpriseV2Enabled = !!getFeatureValue(FEATURE_NAME.ENTERPRISE_V2_ENABLED);
    this.dealerID = dealerId;
    this.isTertiaryButtonClicked = false;
    this.isUpdateButtonClicked = false;
    this.state = {
      initialState: !_isEmpty(props.vehicleDetails) ? props.vehicleDetails : getFormInitialState(),
      showTabExtraContent: true,
      showTabExtraContentPrintContent: false,
      providersWithValuation: EMPTY_ARRAY,
      isFetching: isEditMode,
      vehicleID: tget(props, 'pathParams.id'),
      save: false,
      postingModel: false,
      isToggleLoading: false,
      customerViewToggle: false,
      isOptionSheetDrawerVisible: false,
      declarationStatus: EMPTY_OBJECT,
      costSummary: EMPTY_OBJECT,
      defaultActiveKey: TAB_ENUMS.GENERAL,
      currentActiveTabKey: TAB_ENUMS.GENERAL,
      isVehicleAlertBannerVisible: false,
      showPriceDetailsSummaryDrawer: false,
    };
    this.pricingTabRef = React.createRef();
  }

  componentDidMount() {
    GlobalAnalytics.sendSubAppInitializingEvent();
    this.init();
  }

  static getDerivedStateFromProps(props, state) {
    const initialState = {
      ...props.vehicleDetails,
      pricingDetails: _get(props.vehicleDetails, 'pricingDetails') || EMPTY_OBJECT,
    };
    if (!_isEqual(initialState, state.initialState)) {
      return {
        initialState,
      };
    }
    return {
      showTabExtraContent: true,
    };
  }

  componentWillUnmount() {
    const { actions } = this.props;
    actions.resetVehicleState();
  }

  setLookupFailureForVehicleSave = value => {
    this.considerLookupFailureForVehicleSave = value;
  };

  fetchSetupFields = (params = EMPTY_OBJECT) => {
    const { actions, vehicleDetails, vehicleTypes } = this.props;
    const payload = getTargetingPayload({ ...vehicleDetails, ...params }, { vehicleTypes });
    actions.getSetupFields(payload, getVehicleStoreType());
  };

  getQueryParams = defaultMemoize(location => {
    const dealerId = _get(queryString.parse(location?.search), 'dealerId');

    return {
      dealerId,
    };
  });

  init = async () => {
    const { isEditMode, actions, currentLanguageId } = this.props;
    const { vehicleID } = this.state;
    this.fetch3dModels();
    if (isEditMode) {
      this.filters = [
        {
          field: 'id',
          values: [vehicleID],
        },
      ];
      const vDetails = await actions.getInventoryDetails(vehicleID, this.dealerID, currentLanguageId);
      this.setInitialVehicleDetails(vDetails);

      this.getVehicleReservedTimerData();

      this.setState({
        isFetching: false,
      });
    } else {
      // TODO: this won't be needed.Need to remove
      actions.resetVehicleState();
    }
    const { vehicleDetails } = this.props;
    if (!vehicleDetails.accountPosted) {
      this.setState({ postingModel: true });
    }
    const { vin, vehicleType } = vehicleDetails;
    this.fetchSetupFields();

    const actionsArray = [
      actions.fetchInventorySettings({ currentLanguageId }),
      actions.getBaseMetaData(),
      actions.fetchCustomerSensitiveAssets(),
    ];
    if (vin && isVehicleTypeNew(vehicleType)) {
      actionsArray.push(actions.getInvoicesByVinNumber(vin));
    }
    Promise.all(actionsArray).then(([viSettingData]) => {
      const glAccountList = tget(this, 'props.glAccountList', []);
      const glAccountOptions = getGlAccountOptions(viSettingData, glAccountList);
      this.setState({ vin, viSettingData, glAccountOptions }, () => {
        GlobalAnalytics.sendSubAppIntializedEvent(getAppSentryPayload(PAGES.VEHICLE_DETAILS));
      });
    });
  };

  fetch3dModels = async () => {
    caches.open(CACHE_NAME).then(cache => {
      cache.keys().then(cachedRequests => {
        const urls = cachedRequests.map(request => request.url).filter(url => url);
        // Checks if url is already cached else caches it.
        Object.keys(BODY_TYPE_URLS).forEach(bodyType => {
          if (!_includes(urls, BODY_TYPE_URLS[bodyType])) {
            cache.add(BODY_TYPE_URLS[bodyType]);
          }
        });
      });
    });
  };

  setInitialVehicleDetails = vDetails => {
    if (_isEmpty(_get(vDetails, 'vin'))) {
      toaster('error', MESSAGES.VEHICLE_DETAILS_NOT_AVAILABLE);
      return;
    }
    const { actions, isEditMode, providerList } = this.props;
    const { thumbnailIcon, pricingDetails, id, discounts, offers } = vDetails;
    const { additionalBodyParts } = pricingDetails || EMPTY_OBJECT;
    actions.getDamages(vDetails.id);
    actions.setVehicleState(VehicleDetailsReader.getVehicleDetails(vDetails));
    actions.setInitialVehicleMediaData(VehicleDetailsReader.getFormattedVehicleMedia(vDetails));
    actions.setThumbnailIcon(thumbnailIcon);
    actions.setDiscounts(discounts);
    actions.setOffers(offers);
    actions.setAdditionalBodyParts(additionalBodyParts || EMPTY_ARRAY);
    actions.getGlAccountFromSettings(vDetails);
    actions.setHeaderDetails(vDetails);
    if (
      shouldFetchValuationDetails({
        isEditMode,
        vehicleDetails: vDetails,
        providerList,
      })
    ) {
      this.setValuationDetails(id);
    }
    this.getPOs(id);
  };

  fetchPoliceRecordData = vDetails => {
    const { actions } = this.props;
    if (PROGRAM_CONFIG.shouldShowPoliceRecord() && VehicleReader.vehicleType(vDetails) === VEHICLE_TYPES.USED) {
      actions
        .fetchPoliceRecordData(vDetails.id)
        .then(({ data }) => {
          const registerId = PoliceRecordReader.registerId(data);
          if (!_isEmpty(registerId)) actions.fetchPoliceRecordParameters(registerId);
        })
        .catch(() => {
          toaster(TOASTER_TYPE.ERROR, __('Police Record Fetch Failed.'));
        });
    }
  };

  fetchDeclarationStatus = async vehicleId => {
    const { actions } = this.props;
    const declarationStatusResponse = await actions.getDeclarationStatus(vehicleId);
    this.setState({ declarationStatus: tget(declarationStatusResponse, 'data', EMPTY_OBJECT) });
  };

  fetchVendorInvoicesData = async vehicleId => {
    const { actions } = this.props;
    try {
      await actions.fetchVendorInvoicesData(vehicleId);
    } catch (e) {
      toaster(TOASTER_TYPE.ERROR, __('Failed to fetch Vendor Invoices'));
    }
  };

  getPOs = vehicleId => {
    const { actions, enterpriseV2Workspaces } = this.props;
    actions.getVehiclePOsByScopeId({
      vehicleIds: _castArray(vehicleId),
      enterpriseV2Enabled: this.enterpriseV2Enabled,
      enterpriseV2Workspaces,
    });
  };

  setValuationDetails = async id => {
    const { actions } = this.props;
    const [valuationRawData, appraisalRawData] = await Promise.all([
      ValuationAPI.getSavedValuationDetails(id),
      ValuationAPI.getSavedAppraisalDetails(id),
    ]);
    const valuationDetails = _get(valuationRawData, 'data.valuationMap') || EMPTY_OBJECT;
    const appraisalDetails = _get(appraisalRawData, 'data.appraisals') || EMPTY_OBJECT;
    actions.setValuationDetails(valuationDetails);
    actions.setAppraisalDetails(appraisalDetails);
    const providersWithValuation = checkValuationSavedForProviders(valuationDetails);
    this.setState({ providersWithValuation });
  };

  onCancelAction = async () => {
    showConfirmationModal(
      {
        title: __('Cancel'),
        message: __('All your unsaved changes will be lost. Do you want to proceed?'),
      },
      this.goBackHandler
    );
  };

  formatCustomFields = (customFields = {}) => {
    const customFieldObject = {};
    const filteredCustomFields = _omitBy(customFields, _isNil);
    const customFieldsEntries = Object.entries(filteredCustomFields);
    const formatedCustomFieldsEntries = customFieldsEntries.map(fields => {
      const fieldsCopy = [...fields];
      const currentValue = fieldsCopy[fieldsCopy.length - 1];
      if (!_isArray(currentValue)) {
        const formatedCurrentValue = { value: currentValue };
        fieldsCopy[1] = formatedCurrentValue;
      }
      return fieldsCopy;
    });
    formatedCustomFieldsEntries.map(field => {
      if (!_isArray(field[field.length - 1])) {
        customFieldObject[_head(field)] = [field[field.length - 1]];
      } else {
        customFieldObject[_head(field)] = field[field.length - 1];
      }
      return field;
    });
    return customFieldObject;
  };

  getVehicleDetails(values) {
    const { vehicleDetailsInStore, applicableAdditionalCost, vehicleDetails, isEditMode, defaultGlAccount } =
      this.props;
    const initialCustomFields = _get(values, 'customFields', {}) || {};
    const vehicleMedia = VehicleDetailsReader.getFormattedVehicleMediaForServer(vehicleDetailsInStore);
    const attachments = _get(vehicleDetailsInStore, 'attachments');
    let vehicleStatus = _get(vehicleDetailsInStore, 'general.status', VEHICLE_STATUS.RECEIVED);
    if (this.isSaveAsDraftClicked(values)) {
      vehicleStatus = VEHICLE_STATUS.DRAFT;
    } else if (vehicleStatus === VEHICLE_STATUS.FLOAT) {
      vehicleStatus = VEHICLE_STATUS.FLOAT;
    } else if (vehicleStatus === VEHICLE_STATUS.SOLD) {
      vehicleStatus = VEHICLE_STATUS.SOLD;
    } else if (vehicleStatus === VEHICLE_STATUS.RECEIVED) {
      vehicleStatus = _get(vehicleDetails, 'accountPosted') ? VEHICLE_STATUS.STOCKED_IN : vehicleStatus;
    }
    const defaultGLAccountId = _get(defaultGlAccount, 'id');
    const recallDetails =
      _get(vehicleDetailsInStore, 'totalRecalls.recallDetails') ||
      _get(vehicleDetailsInStore, 'general.recallDetails') ||
      [];
    const additionalCost = _get(vehicleDetailsInStore, 'pricing.additionalCosts') || [];
    const additionalPricing = _get(values, 'pricingDetails') || {};
    const newPricingDetails = {
      ...additionalPricing,
      additionalCosts: Array.from(new Set([...additionalCost, ...applicableAdditionalCost])),
    };
    const certified = VehicleReader.certified(values) || false;
    const dealerCertified = VehicleReader.dealerCertified(values) || false;
    const customFields = initialCustomFields || {};
    const customFieldObject = this.formatCustomFields(customFields);
    const generalDetails = _get(vehicleDetailsInStore, 'general', {}) || {};
    const vinLookupResolved = !isEditMode
      ? _get(vehicleDetailsInStore, 'vinDetails.data.trimStatus') === 'SUCCESS'
      : _get(vehicleDetailsInStore, 'general.vinLookupResolved');
    const thumbnailIcon = _get(vehicleDetailsInStore, 'thumbnailIcon');
    const vehicleType = values.vehicleType || _get(vehicleDetailsInStore, 'general.vehicleType');
    const cpo = VEHICLE_TYPE[vehicleType] === VEHICLE_TYPE.USED ? { certified, dealerCertified } : {};
    const vehicleSubStatus = tget(vehicleDetailsInStore, 'vehicleSubStatus', null);
    const bestStyleName = tget(values, 'bestStyleName', null);
    const displayModel = tget(values, 'displayModel', null);
    const exteriorColorDetail = {
      baseColor:
        _get(vehicleDetails, 'exteriorColorDetail.baseColor') ||
        _get(vehicleDetailsInStore?.vinDetails?.data, 'exteriorColorDetail.baseColor') ||
        null,
      hexCode:
        _get(vehicleDetails, 'exteriorColorDetail.hexCode') ||
        _get(vehicleDetailsInStore?.vinDetails?.data, 'exteriorColorDetail.hexCode') ||
        null,
      group: _get(values, 'exteriorColorDetail.group'),
      type: _get(values, 'exteriorColorDetail.type'),
    };
    const interiorColorDetail = {
      baseColor:
        _get(vehicleDetails, 'interiorColorDetail.baseColor') ||
        _get(vehicleDetailsInStore?.vinDetails?.data, 'interiorColorDetail.baseColor') ||
        null,
      hexCode:
        _get(vehicleDetails, 'interiorColorDetail.hexCode') ||
        _get(vehicleDetailsInStore?.vinDetails?.data, 'interiorColorDetail.hexCode') ||
        null,
    };
    const options = parseOptionsData(values?.options);
    const vehicleDetailToSave = {
      ...generalDetails,
      ...values,
      vehicleType,
      ...cpo,
      vehicleMedia,
      attachments: !_isEmpty(attachments) ? [...attachments] : EMPTY_ARRAY,
      status: vehicleStatus,
      recallDetails,
      pricingDetails: newPricingDetails,
      customFields: customFieldObject,
      vinLookupResolved,
      thumbnailIcon,
      accountId: _get(values, 'accountId') || defaultGLAccountId,
      vehicleSubStatus,
      options,
      bestStyleName,
      displayModel,
      exteriorColorDetail,
      interiorColorDetail,
    };

    return sanitizeVehicleDetailsPayload(vehicleDetailToSave);
  }

  onSaveUpdateAction = async values => {
    const { vehicleDetailsInStore, actions } = this.props;
    const { save } = this.state;
    const vehicleDetailToSave = this.getVehicleDetails(values);
    const damagesList = _get(vehicleDetailsInStore, 'damages.damagesList.data') || EMPTY_ARRAY;
    const { pricingDetails } = vehicleDetailToSave;
    const modifiedPricingDetails = _omit(pricingDetails, EXCULDED_PRICING_DETAILS_KEYS_ON_SAVE);
    const saveReadyPricingFields = parsePriceFieldValue(modifiedPricingDetails);
    const { additionalCosts = EMPTY_ARRAY } = saveReadyPricingFields;

    const updatedAdditionalCosts = _map(additionalCosts, additionalCost => omitKeys(additionalCost, ['isEdited']));

    const vehicle = await this.updateVehicle(
      values,
      {
        ...vehicleDetailToSave,
        pricingDetails: { ...saveReadyPricingFields, additionalCosts: updatedAdditionalCosts },
      },
      damagesList
    );

    if (vehicle && save) {
      actions.resetVehicleState();
      // need timeout for vehicle update to happen
      setTimeout(this.goBackHandler, 1000);
    }
  };

  shouldPostAccounting = () => {
    const { vehicleDetails, isEditMode } = this.props;
    return isEditMode && shouldPostToAccounting(vehicleDetails);
  };

  updateVehicleDamages = async (damagesList, vehicleId, vin) => {
    const { vehicleID } = this.state;
    const { actions, isEditMode } = this.props;
    const vid = isEditMode ? vehicleID : vehicleId;

    if (_size(damagesList) > 0) {
      const filteredDamagedList = _map(damagesList, damage => {
        const updatedDamage =
          (VehicleDetailsReader.isDamageNew(damage) ? _omit(damage, ['id']) : damage) || EMPTY_OBJECT;
        if (!_isNil(vin)) {
          return {
            ...updatedDamage,
            vin,
          };
        }
        return updatedDamage;
      });
      await actions.saveDamagesBulk(filteredDamagedList, vid);
    }
  };

  fetchAdditonalCostData = async formValues => {
    const { actions, bodyTypeMapping, vehicleTypes, displayModelSource, currentLanguageId } = this.props;
    const vehicleDetails = getAdditionalCostAPIPayload(
      formValues,
      { bodyTypeMapping, vehicleTypes },
      displayModelSource
    );
    const { data } = await actions.getAdditionalCostsForVehicle({ vehicleDetails }, currentLanguageId);
    return data;
  };

  setAdditionalCost = async formValues => {
    const data = await this.fetchAdditonalCostData(formValues);
    const { actions } = this.props;
    await actions.setAdditionalCost(filterAdditionalCostDataForProgram(data));
  };

  getGLBalance = id => VehicleDetailsAPI.getGlBalance(id).then(({ data }) => _get(data, 'glBalance'));

  isSaveAsDraftClicked = values => {
    const { isEditMode } = this.props;
    const { vehicleType } = values;
    return !isEditMode && isValidStockTypeForDraftStatusSave(vehicleType) && this.isTertiaryButtonClicked;
  };

  updateVendorInvoices = async vehicleID => {
    const { actions, vendorInvoiceData } = this.props;
    const isInvoiceAdded = await actions.updateVendorInvoice(vehicleID, _last(vendorInvoiceData));
    return isInvoiceAdded;
  };

  updateVehicle = async (values, vehicleDetailToSave, damagesList) => {
    const { actions, vehicleDetailsInStore, currentLanguageId, isEditMode, vendorInvoiceData } = this.props;
    const { save } = this.state;
    let vehicleObj;
    let triedAddingInvoice = false;
    const id = _get(vehicleDetailsInStore, 'general.id');
    if (id && isEditMode) {
      vehicleObj = await actions.updateVehicle(id, vehicleDetailToSave, save, currentLanguageId);
    } else {
      if (!this.isSaveAsDraftClicked(vehicleDetailToSave) && !isPostingAllowedFromPurchaseInvoice(vendorInvoiceData)) {
        toaster(TOASTER_TYPE.ERROR, __('Purchase Invoice is required for saving & stocking the vehicle'));
        return false;
      }
      vehicleObj = await actions.createVehicle(vehicleDetailToSave);
      if (
        VEHICLE_TYPE[_get(vehicleObj, 'vehicleType')] !== VEHICLE_TYPE.SPECIAL &&
        !this.isSaveAsDraftClicked(vehicleDetailToSave)
      ) {
        let isInvoiceAdded = true;
        if (isPurchaseInvoiceApplicable()) {
          triedAddingInvoice = true;
          isInvoiceAdded = await this.updateVendorInvoices(vehicleObj.id);
        }
        if (isInvoiceAdded) {
          try {
            await VehicleAPI.postToAccounting(vehicleObj.id, vehicleObj, false);
          } catch (error) {
            toaster(TOASTER_TYPE.ERROR, getErrorMessage(error, __('Failed to post to accounting')));
          }
        }
      }
    }

    if (!triedAddingInvoice && isPurchaseInvoiceApplicable() && isPurchaseInvoiceAvailableToSave(vendorInvoiceData)) {
      triedAddingInvoice = true;
      await this.updateVendorInvoices(vehicleObj.id);
    }

    this.updateVehicleDamages(damagesList, _get(vehicleObj, 'id'), _get(vehicleObj, 'vin'));
    setTimeout(() => {
      this.fetchPoliceRecordData(vehicleObj);
      if (isEditMode && triedAddingInvoice) this.fetchCostSummary();
    }, ES_SYNC_DELAY);

    return vehicleObj;
  };

  saveValuationDetails = async values => {
    const { isEditMode, providerList, appraisalDetails, valuationDetails } = this.props;
    if (
      hasValuation({
        isEditMode,
        vehicleDetails: values,
        providerList,
        valuationDetails,
      })
    ) {
      await ValuationAPI.saveValuationDetails(VehicleReader.id(values), getSaveValuationPayload(valuationDetails));
    }
    if (
      hasAppraisals({
        isEditMode,
        vehicleDetails: values,
        providerList,
        appraisalDetails,
      })
    ) {
      await ValuationAPI.saveAppraisalDetails(VehicleReader.id(values), getSaveAppraisalPayload(appraisalDetails));
    }
  };

  handleInformationModalConfirm = values => {
    this.onSaveUpdateAction(this.updateVehicleObjectForServer(values));
  };

  renderInfoModalContent = () => (
    <>
      <div className={cx('d-flex align-items-center', styles.infoModal)}>
        <FontIcon size={SIZES.MD}>icon-info</FontIcon>
        <div className={styles.infoContent}>{__('The updated fields will impact the journal entries.')}</div>
      </div>
      <Content>{__(`Click 'Confirm' to proceed.`)}</Content>
    </>
  );

  renderInformationModal = values => {
    showConfirmationModal(
      {
        title: __('Information'),
        message: this.renderInfoModalContent(),
        width: Modal.SIZES.SM,
        submitBtnText: __('Confirm'),
        hideCancel: true,
        hideCloseIcon: true,
      },
      () => this.handleInformationModalConfirm(values)
    );
  };

  generateStockIdOnSave = async form => {
    const { displayModelSource } = this.props;

    const formValues = tget(form.getState(), 'values', EMPTY_OBJECT);
    const {
      vehicleType,
      vin,
      year,
      vehicleSubType,
      make,
      displayMake,
      makeId,
      trimDetails,
      sourceInfo,
      stockedInAtSiteId,
      mfrModelCode,
    } = formValues;
    const { bodyClass } = trimDetails || EMPTY_OBJECT;
    const { source } = sourceInfo || EMPTY_OBJECT;

    if (!vin) return null;
    if (!vehicleType) {
      toaster(TOASTER_TYPE.INFO, MESSAGES.SELECT_VEHICLE_TYPE);
      return null;
    }

    const stockIdPayload = {
      vehicleInfo: {
        vin,
        year,
        make,
        displayMake,
        makeId,
        model: getDisplayModalText(formValues, displayModelSource),
        bodyClass,
        stockType: vehicleType,
        source,
        stockSubType: vehicleSubType,
        stockedInAtSiteId,
        mfrModelCode,
      },
    };
    toaster(TOASTER_TYPE.INFO, __('Generating Stock Number'));
    const response = await generateStockId(
      stockIdPayload,
      __('Stock Number could not be generated, please try again.')
    );
    if (response === '') toaster(TOASTER_TYPE.ERROR, MESSAGES.NO_STOCK_ID_RULE_FOUND);
    else if (response) form.change('stockID', response);

    return response;
  };

  onSubmit = async (values, form) => {
    const {
      isEditMode,
      vehicleDetailsInStore,
      bodyTypeMapping,
      vehicleTypes,
      isVehicleSubTypeMandatory,
      vehicleDetails,
      enableStockNumberGenerationOnSave,
    } = this.props;
    const vehicleDetailToSave = this.getVehicleDetails(values);
    const { postingModel } = this.state;
    const { vehicleType, vin } = values;
    const { accountPosted } = vehicleDetails;
    const id = _get(vehicleDetailsInStore, 'general.id');
    const { rawVehicleData } = vehicleDetailsInStore;
    const infoModalData = getRenderInfoModalData(vehicleDetailToSave, rawVehicleData);
    const canRenderInfoModal = shoulRenderInfoModal(infoModalData);
    const saveAction = async () => {
      const updatedValues = { ...values };
      if (shouldGenerateStockIdOnSave({ enableStockNumberGenerationOnSave, values, isEditMode })) {
        const generatedStockID = await this.generateStockIdOnSave(form);
        if (generatedStockID) {
          updatedValues.stockID = generatedStockID;
        } else return;
      }

      this.saveValuationDetails(updatedValues);
      if (this.isTertiaryButtonClicked) {
        this.onDraftSave(updatedValues);
        return;
      }
      if (this.isUpdateButtonClicked) {
        this.onDraftSave(updatedValues);
        return;
      }

      if (postingModel && this.shouldPostAccounting()) {
        this.postToAccounting(this.updateVehicleObjectForServer(updatedValues));
        return;
      }
      if (!id) {
        await this.setAdditionalCost(updatedValues, bodyTypeMapping);
      } else {
        const glBalance = await this.getGLBalance(id);
        const updatedGLBalance = glBalance || _get(updatedValues, 'pricingDetails.glBalance');
        _set(updatedValues, ['pricingDetails', 'glBalance'], updatedGLBalance);
      }

      const status = _get(vehicleDetailToSave, 'status');
      const source = _get(vehicleDetailToSave, 'sourceInfo.source');
      const isValidDealToShowModal =
        vehicleType !== VEHICLE_TYPES.SPECIAL &&
        status !== VEHICLE_STATUS.SOLD &&
        !_includes(source, SOURCE_ENUM.TRADE_IN);
      if (accountPosted && isValidDealToShowModal && canRenderInfoModal) {
        this.renderInformationModal(updatedValues);
        return;
      }
      this.onSaveUpdateAction(this.updateVehicleObjectForServer(updatedValues));
    };

    if (!isVehicleSubTypeRequired(values, vehicleTypes, isVehicleSubTypeMandatory)) {
      return;
    }

    if (_isEmpty(vehicleType || _get(vehicleDetailsInStore, 'general.vehicleType'))) {
      toaster('error', MESSAGES.INVALID_VEHICLE_TYPE_WARNING);
      return;
    }

    if (!isEditMode && (await checkIfVINHasSoldVehicle(vin))) {
      if (VEHICLE_TYPE[vehicleType] === VEHICLE_TYPE.NEW) {
        showConfirmationModal(
          {
            title: LABELS.WARNING,
            message: MESSAGES.SOLD_VEHICLE_WARNING,
            submitBtnText: LABELS.CONFIRM,
          },
          saveAction
        );
        return;
      }
    }
    saveAction();
  };

  // update payload for create and update vehicle object
  updateVehicleObjectForServer(values) {
    const { vehicleDetailsInStore } = this.props;
    const customFields = this.formatCustomFields(_get(values, 'customFields', {}) || {});
    // TODO: update validation logic
    if (!values.vin) {
      toaster('error', __('Please fill mandatory details'));
    }

    return {
      ...values,
      pricingDetails: {
        ...values.pricingDetails,
        ...vehicleDetailsInStore.pricing,
      },
      options: vehicleDetailsInStore.optionParts.options,
      parts: vehicleDetailsInStore.optionParts.parts,
      accessories: vehicleDetailsInStore?.optionParts?.accessories,
      customFields,
    };
  }

  previewInvoice = () => {
    const { invoices } = this.props;
    if (!_isEmpty(invoices)) {
      const defaultInvoice = _head(invoices);
      const gmPrintContent = InvoiceReader.gmPrintContent(defaultInvoice);
      if (_isEmpty(gmPrintContent)) {
        toaster('error', __('No Invoice To Preview'));
      }
      this.setState({
        showTabExtraContent: _isEmpty(gmPrintContent),
        showTabExtraContentPrintContent: !_isEmpty(gmPrintContent),
      });
    } else {
      this.setState({
        showTabExtraContent: true,
        showTabExtraContentPrintContent: false,
      });
    }
  };

  handelCancelAction = () => {
    this.setState({
      showTabExtraContentPrintContent: false,
      showTabExtraContent: true,
    });
  };

  shouldDisableInvoicePreview = () => {
    const { invoices } = this.props;
    return _isEmpty(invoices);
  };

  previewInvoiceMouseEnter = () => {
    this.setState({ shouldShowNoInvoiceToolTip: true }, () => {
      const { invoicePreviewTooltipTimer } = this;
      if (invoicePreviewTooltipTimer) {
        clearTimeout(invoicePreviewTooltipTimer);
      }
      this.invoicePreviewTooltipTimer = setTimeout(() => {
        this.setState({ shouldShowNoInvoiceToolTip: false });
      }, INVOICE_PREVIEW_TOOLTIP_TIMER);
    });
  };

  printInvoiceButton = () => {
    const { shouldShowNoInvoiceToolTip } = this.state;
    const disableButton = this.shouldDisableInvoicePreview();

    return (
      <Tooltip
        placement={TOOLTIP_PLACEMENT.BOTTOM}
        title={disableButton ? NO_INVOICE_MESSAGE : ''}
        visible={shouldShowNoInvoiceToolTip && disableButton}>
        <Button
          className={styles.printInvoiceButton}
          onClick={this.previewInvoice}
          disabled={disableButton}
          onMouseEnter={this.previewInvoiceMouseEnter}>
          {__('Invoice Preview')}
        </Button>
      </Tooltip>
    );
  };

  /**
   * This function check if the vehicle is sold.
   * Start deal option is only shown for non-sold vehicles
   *
   * @returns {boolean} Whether to show start deal option  or not
   */
  shouldShowStartDeal = () => {
    const { vehicleDetailsInStore, isEditMode } = this.props;
    return (
      isEditMode &&
      isLoggedInDealerVehicle(vehicleDetailsInStore.rawVehicleData) &&
      isVehicleEligibleForStartDeal(VehicleReader.status(vehicleDetailsInStore.rawVehicleData))
    );
  };

  /**
   * This function renders the extra items on right side of vehicleDetails navigations bar
   *
   * @param {array} renderItemList List of items to render
   */
  navigationTabExtraContent = renderItemList => (
    <div className={styles.navigationTabExtraContent}>
      {_map(renderItemList, item => item.shouldShow && item.renderer)}
    </div>
  );

  startDealForVehicle = () => {
    const { vehicleDetailsInStore } = this.props;
    handleStartDealForVehicles([vehicleDetailsInStore.rawVehicleData]);
  };

  onTabChange = activeKey => {
    this.setState({
      showTabExtraContent: _includes(['GENERAL', 'PRICING', 'OPTIONS_PARTS'], activeKey),
      showTabExtraContentPrintContent: false,
      currentActiveTabKey: activeKey,
    });
  };

  postToAccounting = values => {
    const { currentLanguageId } = this.props;
    const vehicleDetailsToSave = this.getVehicleDetails(values);
    const pricingDetails = VehicleReader.pricingDetails(values);
    const saveReadyPricingFields = parsePriceFieldValue(pricingDetails);
    showPostToAccountingModal({
      vehicleObject: { ...vehicleDetailsToSave, pricingDetails: saveReadyPricingFields },
      langParam: currentLanguageId,
    });
  };

  onPostedToAccounting = () => {
    const { vehicleDetailsInStore } = this.props;
    const damagesList = _get(vehicleDetailsInStore, 'damages.damagesList.data') || EMPTY_ARRAY;

    this.updateVehicleDamages(damagesList);
    setTimeout(this.goBackHandler, 1000);
  };

  getCustomFieldsData = (customField = []) => {
    const customFields = {};
    for (let i = 0; i < customField.length; i += 1) {
      const label = customField[i].label || _get(customField, [i, 'name'], '');
      const options = _get(customField, [i, 'options'], []);
      if (!_isEmpty(options)) {
        const customFieldsObject = _head(options) || {};
        const customFieldsValue = _get(customFieldsObject, 'name', '') || '';
        customFields[label] = customFieldsValue;
      }
    }
    return customFields;
  };

  getGlAccountByAccountId = glAccountId => {
    const { glAccountList } = this.props;
    return _find(_values(glAccountList), account => account?.value === glAccountId);
  };

  getGlAccountByNumber = glAccountNumber => {
    const { glAccountList } = this.props;
    return Object.values(glAccountList).find(account => account.label === glAccountNumber);
  };

  formatPriceFields = (formState, form) => {
    const pricingDetails = _get(formState, 'pricingDetails');
    const upDatedPriceDetails = {};
    if (!_isEmpty(pricingDetails)) {
      Object.values(PRICE_FORMAT_KEYS).forEach(priceFieldName => {
        if (pricingDetails[priceFieldName]) {
          const currentActiveState = _get(form.getState(), 'active');
          if (_isEmpty(currentActiveState)) {
            form.change(`pricingDetails.${priceFieldName}`, getPriceFieldsFormat(pricingDetails[priceFieldName]));
          }
          upDatedPriceDetails[priceFieldName] = getPriceFieldsFormat(pricingDetails[priceFieldName]);
        }
      });
    }
    return { ...formState, pricingDetails: upDatedPriceDetails };
  };

  shouldShowPrintInvoice = () => {
    const { currentActiveTabKey } = this.state;
    const { vehicleDetails } = this.props;
    const vehicleType = VehicleDetailsReader.getVehicleType(vehicleDetails);
    return (
      isVehicleTypeNew(vehicleType) &&
      (_isEmpty(currentActiveTabKey) || _includes(['GENERAL', 'PRICING', 'OPTIONS_PARTS'], currentActiveTabKey)) &&
      hasViewInvoice()
    );
  };

  getVehicleActions = () => {
    const showAuditLogs = () => {
      this.auditLogs.openAuditLogs();
    };
    return _map(VEHICLE_ACTIONS, action => {
      const { id } = action;
      switch (id) {
        case 'auditLogs':
          return { ...action, onClick: showAuditLogs };
        default:
      }
      return action;
    });
  };

  handleSaveAction = (isTertiaryButtonClicked, handleSubmit) => {
    this.isTertiaryButtonClicked = isTertiaryButtonClicked;
    this.setState({ save: true, postingModel: !isTertiaryButtonClicked && this.shouldPostAccounting() }, () => {
      handleSubmit();
    });
  };

  handleUpdateAction = (isUpdateButtonClicked, handleSubmit) => {
    this.isUpdateButtonClicked = isUpdateButtonClicked;
    this.setState({ save: false, postingModel: false }, () => {
      handleSubmit();
    });
  };

  onDraftSave = async values => {
    const { vehicleType } = values;
    if (isValidStockTypeForDraftStatusSave(vehicleType)) {
      _set(values, 'status', VEHICLE_STATUS.DRAFT);
    }
    if (this.isSaveAsDraftClicked(values)) await this.setAdditionalCost(values);
    this.onSaveUpdateAction(this.updateVehicleObjectForServer(values));
  };

  goBackHandler = () => {
    const { navigate, location } = this.props;
    handleVehicleDetailsBackNavigation({
      navigate,
      location,
    });
  };

  shouldShowTertiaryButton = form => {
    const { isEditMode } = this.props;
    const { vehicleType } = _get(form.getState(), 'values') || EMPTY_OBJECT;

    const saveAsDraft = isValidStockTypeForDraftStatusSave(vehicleType) && !isEditMode;
    return this.shouldPostAccounting() || saveAsDraft;
  };

  getTertiaryButtonLabel = form => {
    const { isEditMode } = this.props;
    const { vehicleType } = _get(form.getState(), 'values') || EMPTY_OBJECT;
    return isValidStockTypeForDraftStatusSave(vehicleType) && !isEditMode ? __('Save as Draft') : __('Save');
  };

  policeRecordSuccessCb = (successResponse, shouldFetchDropdownParameters = false) => {
    const { actions } = this.props;
    const registerId = PoliceRecordReader.registerId(successResponse);
    if (shouldFetchDropdownParameters && !_isEmpty(registerId)) actions.fetchPoliceRecordParameters(registerId);
    actions.setPoliceRecordData(successResponse);
  };

  fetchCostSummary = async () => {
    const { actions } = this.props;
    const { vehicleID } = this.state;
    const costSummary = await actions.fetchCostSummary(vehicleID);
    this.setState({ costSummary });
  };

  fetchAllHardPacksList = async form => {
    const { bodyTypeMapping, vehicleTypes, displayModelSource, currentLanguageId, actions } = this.props;
    const formValues = tget(form.getState(), 'values', EMPTY_OBJECT);
    const response = await actions.fetchAllHardPacksList({
      formValues,
      bodyTypeMapping,
      vehicleTypes,
      displayModelSource,
      currentLanguageId,
    });
    return response;
  };

  getTabProps(tab, form) {
    const {
      vehicleDetails,
      dealerConfig,
      isEditMode,
      actions,
      customFields,
      allGlAccount,
      associatedGlAccounts,
      defaultGlAccount,
      policeRecordData,
      customerViewProps: customerViewPropsFromState,
      currentLanguageId,
      customFormCategories,
      enterpriseV2Workspaces,
      enableStockNumberGenerationOnSave,
    } = this.props;
    const {
      vin,
      viSettingData,
      glAccountOptions,
      currentSelectedGlAccount,
      vehicleGlAccountId,
      providersWithValuation,
      vehicleID,
      customerViewToggle,
      declarationStatus,
      costSummary,
      showPriceDetailsSummaryDrawer,
    } = this.state;

    const formCurrentState = _get(form.getState(), 'values') || EMPTY_OBJECT;
    const { vehicleType } = formCurrentState;
    const vehicleid = isEditMode ? vehicleID : undefined;
    const customerViewProps = { ...customerViewPropsFromState, customerViewToggle };

    switch (tab) {
      case TAB_ENUMS.GENERAL:
        return {
          selectedVehicle: vehicleDetails,
          dealerConfig,
          isEditMode,
          enterpriseV2Enabled: this.enterpriseV2Enabled,
          enterpriseV2Workspaces,
          vin: vin || _get(formCurrentState, 'vin'),
          form,
          getRecallsByVin: actions.getRecallsByVin,
          updateRecallsInVehicleDetails: actions.updateRecallsInVehicleDetails,
          customFields,
          getGlAccountFromSettings: actions.getGlAccountFromSettings,
          optionsLookupEnable: SettingsReader.getOptionsLookupEnable(viSettingData),
          setLookupFailureForVehicleSave: this.setLookupFailureForVehicleSave,
          fetchSetupFields: this.fetchSetupFields,
          fetchPoliceRecordData: this.fetchPoliceRecordData,
          policeRecordData,
          policeRecordSuccessCb: this.policeRecordSuccessCb,
          customerViewProps,
          fetchVendorInvoicesData: this.fetchVendorInvoicesData,
          addOrUpdateVendorInvoice: actions.addOrUpdateVendorInvoice,
          fetchAllHardPacksList: _partial(this.fetchAllHardPacksList, form),
          declarationStatus,
          fetchDeclarationStatus: this.fetchDeclarationStatus,
          enableStockNumberGenerationOnSave,
        };

      case TAB_ENUMS.PRICING:
        return {
          vehicleID: vehicleid,
          ref: this.pricingTabRef,
          pricingDetails: vehicleDetails.pricingDetails,
          form,
          isEditMode,
          vin: vin || _get(formCurrentState, 'vin'),
          glAccountList: glAccountOptions,
          currentSelectedGlAccount,
          vehicleGlAccountId,
          allGlAccount,
          formatPriceFields: this.formatPriceFields,
          getGlAccountByNumber: this.getGlAccountByAccountNumber,
          getGlAccountById: this.getGlAccountByAccountId,
          associatedGlAccounts,
          defaultGlAccount,
          disablePricingEdits: SettingsReader.getDisablePricingEditSettingsEnable(viSettingData),
          customerViewProps,
          currentLanguageId,
          fetchAllHardPacksList: _partial(this.fetchAllHardPacksList, form),
          costSummary,
          fetchCostSummary: this.fetchCostSummary,
        };

      case TAB_ENUMS.VALUATION:
        return {
          form,
          providersWithValuation,
        };

      case TAB_ENUMS.DAMAGES:
        return {
          vehicleDetails,
          form,
        };

      case TAB_ENUMS.DEALS:
        return {
          vehicleID,
        };
      case TAB_ENUMS.LEADS:
        return {
          vehicleDetails,
        };
      case TAB_ENUMS.DOCUMENTS:
        return {
          vehicleDetails,
          customFormCategories,
          isEditMode,
        };

      case TAB_ENUMS.OPTIONS_PARTS:
        return {
          isEditMode,
          form,
          enablePriceUpdateForDIO: SettingsReader.enablePriceUpdateForDIO(viSettingData),
          customerViewProps,
          selectedVehicleType: vehicleType,
          showPriceDetailsSummaryDrawer,
          togglePriceDetailsSummaryDrawer: this.togglePriceDetailsSummaryDrawer,
        };

      default:
        return EMPTY_OBJECT;
    }
  }

  shouldShowValuationTab = form => {
    const { appraisalDetails, providerList, valuationDetails, isEditMode } = this.props;
    return (
      hasValuation({
        isEditMode,
        vehicleDetails: _get(form.getState(), 'values'),
        providerList,
        valuationDetails,
      }) ||
      hasAppraisals({
        isEditMode,
        vehicleDetails: _get(form.getState(), 'values'),
        providerList,
        appraisalDetails,
      })
    );
  };

  canEditTab = (tab, { form }) => {
    const editConditions = {
      [TAB_ENUMS.VALUATION]: this.shouldShowValuationTab(form),
    };
    return editConditions[tab];
  };

  shouldShowTabs = (form, tabConfig) => {
    const config = _keyBy(tabConfig, 'key');
    const { isEditMode } = this.props;
    return {
      [TAB_ENUMS.VALUATION]: config?.VALUATION?.showTab && this.canEditTab('VALUATION', { form }),
      [TAB_ENUMS.PRICING]: config?.PRICING?.showTab && PermissionHelper.can(config?.PRICING?.validFor, 'OR'),
      [TAB_ENUMS.GENERAL]: config?.GENERAL?.showTab,
      [TAB_ENUMS.OPTIONS_PARTS]: config?.OPTIONS_PARTS?.showTab,
      [TAB_ENUMS.DAMAGES]: config?.DAMAGES?.showTab && !DealerPropertyHelper.isRVDealerEnabled(),
      [TAB_ENUMS.MEDIA]: config?.MEDIA?.showTab,
      [TAB_ENUMS.LEADS]: config?.LEADS?.showTab && isCRMEnabled() && isEditMode,
      [TAB_ENUMS.DEALS]: config?.DEALS?.showTab && isEditMode,
      [TAB_ENUMS.DOCUMENTS]: config?.DOCUMENTS?.showTab,
    };
  };

  getShouldReadOnlyDetails = () => {
    const { isEditMode, enterpriseV2Workspaces, vehicleDetails } = this.props;
    return shouldReadOnlyDetails({
      vehicleDetails,
      isEditMode,
      enterpriseV2Enabled: this.enterpriseV2Enabled,
      enterpriseV2Workspaces,
    });
  };

  renderTabBody = (tab, form) => {
    const TabBodyComponent = TAB_KEY_VS_TAB_RENDERER[tab];
    const tabProps = this.getTabProps(tab, form);
    const isReadOnlyDetails = this.getShouldReadOnlyDetails();
    if (TabBodyComponent) {
      return (
        <div
          className={cx('full-width full-height', {
            [styles.viewOnly]: isReadOnlyDetails && tab !== TAB_ENUMS.GENERAL,
          })}>
          <TabBodyComponent isReadOnlyDetails={isReadOnlyDetails} {...tabProps} />
        </div>
      );
    }
    return null;
  };

  renderTabHeader = (tabConfig, tabsWithError) => {
    const hasError = tabConfig?.errors && tabConfig?.touched && _get(tabsWithError, [tabConfig?.name, 'hasError']);

    return <TabHeader tabName={tabConfig?.name} hasError={hasError} />;
  };

  getTabBodyHeight = () => {
    const { isEditMode, enterpriseV2Workspaces, vehicleDetails, contentHeight } = this.props;
    const { isVehicleAlertBannerVisible } = this.state;
    const { shouldShow: isStickyBannerPresent } = getDetailsPageStickyBannerData({
      isEditMode,
      vehicleDetails,
      enterpriseV2Enabled: this.enterpriseV2Enabled,
      enterpriseV2Workspaces,
    });

    let tabBodyHeight = contentHeight;
    if (PROGRAM_CONFIG.shouldHideVehicleDetailsFooter()) tabBodyHeight += getScaleAdjustedSize(64);
    if (isVehicleAlertBannerVisible) tabBodyHeight -= getScaleAdjustedSize(48);
    if (isStickyBannerPresent) tabBodyHeight -= getScaleAdjustedSize(48);
    return tabBodyHeight;
  };

  renderTabPanes = (errors, touched, form) => {
    const tabs = getVehicleDetailsTabs(this.shouldShowTabs(form, getVehicleDetailsTabConfig()));
    const tabsWithError = validateTabErrors({ errors, touched });
    const tabBodyHeight = this.getTabBodyHeight();

    return _map(tabs, tab => (
      <Tabs.TabPane tab={this.renderTabHeader(tab, tabsWithError)} key={tab.key} forceRender={tab.forceRender}>
        <TabBody height={tab.height(tabBodyHeight)} className={cx(styles.tabBody, tab.className)}>
          {this.renderTabBody(tab.key, form)}
        </TabBody>
      </Tabs.TabPane>
    ));
  };

  renderLoader = () => <Spinner className="full-width full-height flex-center absolute" />;

  validateConditionsToSaveVehicle = (values, form) => {
    const { vehicleDetailsInStore } = this.props;
    const isMediaUploadInProgress = VehicleDetailsReader.isUploadInProgress(vehicleDetailsInStore);
    if (isMediaUploadInProgress) {
      showConfirmationModal({
        title: __('Media Upload.'),
        message: __('Please wait. Media upload is in progress.'),
      });
      return;
    }

    const vin = VehicleReader.vin(values);
    if (!(checkValidVinLength(vin) && !this.considerLookupFailureForVehicleSave)) {
      showVINConfirmationModal({ values }, this.onSubmit);
      return;
    }

    this.onSubmit(values, form);
  };

  oemSwitchHandler = async active => {
    const { actions } = this.props;
    const { vehicleID } = this.state;
    this.setState({ isToggleLoading: true });
    await actions.toggleOEMSyndication({ vehicleId: vehicleID, isEnabled: active });
    this.setState({ isToggleLoading: false });
  };

  renderOEMSyncSwitch = () => {
    const { vehicleDetails, isEditMode } = this.props;
    const { isToggleLoading } = this.state;
    return (
      <div className="d-flex align-items-center m-r-16">
        <Switch
          checked={isEditMode && vehicleDetails.eligibleForOEMSyndication}
          onChange={this.oemSwitchHandler}
          checkedChildren={<FontIcon size={SIZES.XS}>icon-tick1</FontIcon>}
          unCheckedChildren={<FontIcon size={SIZES.XS}>icon-cross</FontIcon>}
          loading={isToggleLoading}
          disabled={!isEditMode}
        />
        <Label className="m-l-8">{__('Syndicate to OEM')}</Label>
      </div>
    );
  };

  isPostAccountingDisabled = form => {
    const { isEditMode, policeRecordData, vendorInvoiceData } = this.props;
    const vehicleDetails = tget(form.getState(), 'values', EMPTY_OBJECT);
    const isPostAccountingApplicable = isEditMode && shouldPostToAccounting(vehicleDetails);

    const vehiclePostingPermissionDisabled =
      (!isEditMode || isPostAccountingApplicable) && !hasVehiclePostingPermission();

    if (isInchcapeOrRRG() && isPostAccountingApplicable) {
      if (vehiclePostingPermissionDisabled)
        return { disabled: true, disabledMessage: MESSAGES.VEHICLE_POSTING_PERMISSION_DISABLED };
      const { declarationStatus } = this.state;
      let disabled = false;
      if (isRRG() && VehicleReader.vehicleType(vehicleDetails) === VEHICLE_TYPES.USED) {
        const incomingRecordStatus = PoliceRecordReader.incomingStatus(policeRecordData);
        const { purchaseStatus } = declarationStatus || EMPTY_OBJECT;
        disabled =
          incomingRecordStatus !== INCOMING_CAR_RECORD_STATUS.VALIDATED ||
          purchaseStatus !== DECLARATION_STATUS.SUBMITTED;
      }
      if (isPurchaseInvoiceApplicable()) {
        const firstInvoice = _head(vendorInvoiceData);
        disabled = disabled || _isEmpty(vendorInvoiceData) || !firstInvoice.accountingStatus;
      }
      return { disabled, disabledMessage: EMPTY_STRING };
    }
    const notLoggedInDealer =
      this.enterpriseV2Enabled && isPostAccountingApplicable && !isLoggedInDealerVehicle(vehicleDetails);
    if (notLoggedInDealer) return { disabled: true, disabledMessage: MESSAGES.DIFFERENT_DEALER_VEHICLE_INFO };
    if (vehiclePostingPermissionDisabled)
      return { disabled: true, disabledMessage: MESSAGES.VEHICLE_POSTING_PERMISSION_DISABLED };
    return { disabled: false, disabledMessage: EMPTY_STRING };
  };

  customerViewToggleHandler = value => {
    this.setState({ customerViewToggle: value });
  };

  toggleOptionSheetDrawer = () => {
    this.setState(({ isOptionSheetDrawerVisible }) => ({ isOptionSheetDrawerVisible: !isOptionSheetDrawerVisible }));
  };

  shareMenuHandler = key => {
    switch (key) {
      case SHARE_MENU_KEYS.OPTION_SHEET:
        this.toggleOptionSheetDrawer();
        break;
      default:
        break;
    }
  };

  onOptionSheetPDFGenerate =
    pdfActionsData =>
    async ({ mediaUrl }) => {
      const { action, payload } = pdfActionsData || EMPTY_OBJECT;
      switch (action) {
        case PDF_ACTIONS.PRINT:
          try {
            const reqPayload = getPrintPayload({
              signedUrl: mediaUrl,
              options: payload,
              module: SALES_PRINTING_MODULE_NAMES.OPTION_SHEET,
            });
            await printFileV2(reqPayload);
            toaster(TOASTER_TYPE.INFO, PRINTER_MESSAGES.PRINT_JOB_TRIGGER_SUCCESS);
          } catch {
            toaster(TOASTER_TYPE.ERROR, PRINTER_MESSAGES.PRINT_JOB_TRIGGER_FAILURE);
          }
          break;

        default:
          break;
      }
    };

  setCurrentActiveTabKey = tabKey => this.setState({ currentActiveTabKey: tabKey });

  setVehicleAlertBannerVisibility = flag => this.setState({ isVehicleAlertBannerVisible: flag });

  getVehicleReservedTimerData = () => {
    const { actions, vehicleDetails } = this.props;
    actions.getReservedVehicleTimer(_castArray(vehicleDetails));
  };

  togglePriceDetailsSummaryDrawer = flag => this.setState({ showPriceDetailsSummaryDrawer: flag });

  renderPriceDetailsSummary = form => () => {
    if (!PROGRAM_CONFIG.shouldShowPriceSummaryFooter()) return null;

    const { customerViewProps: customerViewPropsFromState } = this.props;
    const { customerViewToggle, currentActiveTabKey } = this.state;
    const formValues = tget(form.getState(), 'values', EMPTY_OBJECT);
    const customerViewProps = { ...customerViewPropsFromState, customerViewToggle };

    return (
      <OptionsPriceSummaryFooter
        customerViewProps={customerViewProps}
        vehicleDetailsForm={formValues}
        currentActiveTabKey={currentActiveTabKey}
        togglePriceDetailsSummaryDrawer={this.togglePriceDetailsSummaryDrawer}
      />
    );
  };

  render() {
    const {
      isEditMode,
      vehicleDetailsInStore,
      notes,
      invoices,
      vehicleDetails,
      displayModelSource,
      currentLanguageId,
      customerViewProps: customerViewPropsFromState,
      actions,
      enterpriseV2Workspaces,
    } = this.props;
    const {
      initialState = EMPTY_OBJECT,
      showTabExtraContent,
      formCurrentState = EMPTY_OBJECT,
      showTabExtraContentPrintContent,
      vin,
      isFetching,
      vehicleID,
      customerViewToggle,
      isOptionSheetDrawerVisible,
      isVehicleAlertBannerVisible,
      defaultActiveKey,
      currentActiveTabKey,
    } = this.state;
    const { trimDetails = EMPTY_OBJECT } = initialState;
    const updatedInitialState = initialState;
    const trimsToDisplay = isEditMode ? transformObjectValuesToString(getTrimDetails(trimDetails)) : '';
    const printInvoiceButton = this.printInvoiceButton();
    const oemSyncSwitch = this.renderOEMSyncSwitch();
    const vehicleid = isEditMode ? vehicleID : undefined;
    const formInitialStateWithTrimDetails = {
      ...updatedInitialState,
      trim: trimsToDisplay,
      ...formCurrentState,
    };
    const currentUser = TEnvReader.userInfo();
    const vehicleNotes = notes[vehicleid] || EMPTY_ARRAY;
    const extraItemsList = [
      {
        type: 'oemSwitch',
        renderer: oemSyncSwitch,
        shouldShow:
          !PROGRAM_CONFIG.shouldHideSyndicateToOEM() && DealerPropertyHelper.isVehicleLevelSyndicationEnabled(),
      },
      {
        type: 'printInvoice',
        renderer: printInvoiceButton,
        shouldShow: this.shouldShowPrintInvoice(showTabExtraContent),
      },
      {
        type: 'stockCard',
        renderer: <StockCardButton vehicleDetails={vehicleDetails} customerViewToggle={customerViewToggle}/>,
        shouldShow: isEditMode && PROGRAM_CONFIG.shouldShowStockCard(),
      },
    ];
    const customerViewProps = { ...customerViewPropsFromState, customerViewToggle };
    const { shouldShow: isStickyBannerPresent, bannerMessage } = getDetailsPageStickyBannerData({
      isEditMode,
      vehicleDetails,
      enterpriseV2Enabled: this.enterpriseV2Enabled,
      enterpriseV2Workspaces,
    });

    return (
      <React.Fragment>
        <PropertyControlledComponent controllerProperty={isFetching}>{this.renderLoader()}</PropertyControlledComponent>
        <PropertyControlledComponent controllerProperty={!isFetching}>
          <Page id="vehicleDetails" className="d-flex flex-column">
            <Form
              onSubmit={this.validateConditionsToSaveVehicle}
              initialValues={formInitialStateWithTrimDetails}
              keepDirtyOnReinitialize
              mutators={MUTATORS}
              render={({ handleSubmit, form, errors, touched }) => (
                <>
                  <PropertyControlledComponent controllerProperty={PROGRAM_CONFIG.shouldShowVehicleAlertDrawer()}>
                    <VehicleAlertDrawer
                      vehicleID={vehicleID}
                      containerClassName={styles.optionsTableContainer}
                      optionMismatchTableClassName={styles.optionsMismatchTable}
                      actions={actions}
                      setCurrentActiveTabKey={this.setCurrentActiveTabKey}
                      isVehicleAlertBannerVisible={isVehicleAlertBannerVisible}
                      setVehicleAlertBannerVisibility={this.setVehicleAlertBannerVisibility}
                    />
                  </PropertyControlledComponent>
                  <PropertyControlledComponent controllerProperty={isStickyBannerPresent}>
                    <div className={cx('full-width d-flex align-items-center', styles.bannerContainer)}>
                      <FontIcon size={SIZES.L} className={styles.importIcon}>
                        icon-info
                      </FontIcon>
                      <div>{bannerMessage}</div>
                    </div>
                  </PropertyControlledComponent>
                  <Header
                    form={form}
                    assetId={vehicleid}
                    isEditMode={isEditMode}
                    auditLogsRef={this.auditLogs}
                    getVehicleDetails={values => this.getVehicleDetails(values)}
                    displayModelSource={displayModelSource}
                    currentLanguageId={currentLanguageId}
                    dealProps={{
                      startDealForVehicle: this.startDealForVehicle,
                      shouldShowStartDeal: this.shouldShowStartDeal,
                    }}
                    customerViewProps={customerViewProps}
                    customerViewToggleHandler={this.customerViewToggleHandler}
                    shareMenuHandler={this.shareMenuHandler}
                    vehicleDetailsInStore={vehicleDetailsInStore}
                  />
                  <Page.Body className={styles.vehicleDetailsContainer}>
                    <Tabs
                      defaultActiveKey={defaultActiveKey}
                      size="small"
                      tabBarGutter={0}
                      activeKey={currentActiveTabKey}
                      onChange={this.onTabChange}
                      tabBarExtraContent={this.navigationTabExtraContent(extraItemsList)}
                      tabBarStyle={{
                        backgroundColor: '#F4F5F6',
                        display: 'flex',
                        alignItems: 'center',
                        position: 'relative',
                      }}>
                      {this.renderTabPanes(errors, touched, form)}
                      {/* will be implemented in future do not delete */}
                      {/* <Tabs.TabPane key="7" tab={__('Activity')}>
                    <TabBody height={contentHeight}>
                      <Activity vehicle={vehicleDetails} />
                    </TabBody>
                  </Tabs.TabPane> */}
                    </Tabs>
                    <PropertyControlledComponent
                      controllerProperty={showTabExtraContentPrintContent && !isRVDealerEnabled()}>
                      <PreviewContainer
                        handelCancelAction={this.handelCancelAction}
                        vin={vin}
                        form={form}
                        invoices={invoices}
                        showTabExtraContentPrintContent={showTabExtraContentPrintContent}
                      />
                    </PropertyControlledComponent>
                  </Page.Body>
                  <PropertyControlledComponent controllerProperty={!PROGRAM_CONFIG.shouldHideVehicleDetailsFooter()}>
                    <Page.Footer>
                      <div
                        className={cx({
                          [styles.viewOnly]: this.getShouldReadOnlyDetails(),
                        })}>
                        <SaveComponent
                          primaryButtonLabel={this.shouldPostAccounting() ? __('Ready To Post') : undefined}
                          primaryButtonToolTipTipClassName={styles.footerPrimaryButtonTooltip}
                          isPrimaryDisabled={this.isPostAccountingDisabled(form)?.disabled || false}
                          primaryButtonToolTipTitle={
                            this.isPostAccountingDisabled(form)?.disabledMessage || EMPTY_STRING
                          }
                          tertiaryButtonLabel={this.getTertiaryButtonLabel(form)}
                          additionalButtonLabel={__('Update')}
                          onPrimaryAction={() => {
                            this.handleSaveAction(false, handleSubmit);
                          }}
                          onSecondaryAction={this.onCancelAction}
                          onTertiaryAction={() => {
                            this.handleSaveAction(true, handleSubmit);
                          }}
                          onAdditionalAction={() => {
                            this.handleUpdateAction(false, handleSubmit);
                          }}
                          primaryActionLoading={!this.isTertiaryButtonClicked && vehicleDetailsInStore.isSaving}
                          tertiaryActionLoading={this.isTertiaryButtonClicked && vehicleDetailsInStore.isSaving}
                          additionalActionLoading={!this.isUpdateButtonClicked && vehicleDetailsInStore.isUpdating}
                          showAdditionalButton={isEditMode}
                          showTertiaryButton={this.shouldShowTertiaryButton(form)}
                          renderAdditionalFooterDetail={this.renderPriceDetailsSummary(form)}
                        />
                      </div>
                    </Page.Footer>
                  </PropertyControlledComponent>
                  <Portal className="zIndex10" />
                  <AuditLogs
                    ref={ref => {
                      this.auditLogs = ref;
                    }}
                    type={ASSET_TYPES.VI}
                    id={vehicleID}
                  />
                  <NotesModal
                    {...getNotesModalProps({ vehicleDetails: formInitialStateWithTrimDetails, displayModelSource })}
                    assetId={vehicleid}
                    assetType={NOTES_ASSET_TYPES.VEHICLE}
                    currentUser={currentUser}
                    notes={vehicleNotes}
                  />
                  <PostToAccountingModal
                    onSubmit={this.onPostedToAccounting}
                    enterpriseV2Enabled={this.enterpriseV2Enabled}
                  />
                  <VINConfirmationModal />
                  <ConfirmationModal />
                  <PropertyControlledComponent controllerProperty={PROGRAM_CONFIG.shouldShowPoliceRecord()}>
                    <CarRecord />
                  </PropertyControlledComponent>
                  <PropertyControlledComponent controllerProperty={isRRG()}>
                    <DeclarationRecord />
                  </PropertyControlledComponent>
                  <PropertyControlledComponent controllerProperty={PROGRAM_CONFIG.shouldShowOptionSheet()}>
                    <OptionSheetDrawer
                      vehicleDetails={vehicleDetails}
                      visible={isOptionSheetDrawerVisible}
                      onClose={this.toggleOptionSheetDrawer}
                      onActionComplete={this.onOptionSheetPDFGenerate}
                    />
                  </PropertyControlledComponent>
                </>
              )}
            />
          </Page>
        </PropertyControlledComponent>
      </React.Fragment>
    );
  }
}

export default compose(
  withMultiLingualInfo,
  withExperienceEngineConsumer,
  withSize({ hasPageFooter: 1, hasPageHeader: 1, headerHeight })
)(VehicleDetails);
