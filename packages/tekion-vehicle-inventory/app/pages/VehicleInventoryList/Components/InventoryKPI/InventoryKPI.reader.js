import _get from 'lodash/get';
import _noop from 'lodash/noop';
import _sum from 'lodash/sum';

import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import { VEHICLE_TYPES } from 'tbase/constants/vehicleInventory/vehicleTypes';
import { humanize } from 'tbase/utils/dateUtils';
import { tget } from 'tbase/utils/general';
import { isAecAndArcLiteProgram as isArcPlusAecProgram } from 'twidgets/utils/program.utils';

import { KPI_TYPE, FILTER_TYPES } from './InventoryKPI.helper';

export const getTotalCount = data => {
  const { newVehicle, usedVehicle } = data || EMPTY_OBJECT;
  const newVehicleCount = _get(newVehicle, 'openCount', 0);
  const usedVehicleCount = _get(usedVehicle, 'openCount', 0);

  return newVehicleCount + usedVehicleCount;
};

export const getAlertCount = data => {
  const {
    vehicleWithAjustedPrice,
    vehicleWithZeroGLBalance,
    vehicleWithOptionMisMatch,
    vehicleWithMultiStyleConflict,
    vehicleWithOemAlerts,
  } = data || EMPTY_OBJECT;
  const zeroGLVehicleCount = tget(vehicleWithZeroGLBalance, 'openCount', 0);
  const adjustedPriceVehicleCount = tget(vehicleWithAjustedPrice, 'openCount', 0);
  const vehicleWithOptionMisMatchCount = tget(vehicleWithOptionMisMatch, 'openCount', 0);
  const vehicleWithMultiStyleIdMisMatchCount = tget(vehicleWithMultiStyleConflict, 'openCount', 0);
  const vehicleWithOemAlertsCount = tget(vehicleWithOemAlerts, 'openCount', 0);
  return _sum([
    zeroGLVehicleCount,
    adjustedPriceVehicleCount,
    vehicleWithOptionMisMatchCount,
    vehicleWithMultiStyleIdMisMatchCount,
    vehicleWithOemAlertsCount,
  ]);
};

export const getMenuContent = (data, onClick = _noop, ref, selectedFilter) => {
  const {
    newVehicle,
    usedVehicle,
    allVehicle,
    vehicleWithAjustedPrice,
    vehicleWithZeroGLBalance,
    vehicleWithOptionMisMatch,
    vehicleWithMultiStyleConflict,
    vehicleWithOemAlerts,
  } = data || EMPTY_OBJECT;
  const newVehicleCount = _get(newVehicle, 'openCount', 0);
  const usedVehicleCount = _get(usedVehicle, 'openCount', 0);
  const allVehicleCount = _get(allVehicle, 'openCount', 0);

  const roKPI = [
    {
      label: __('New Vehicles'),
      selected: selectedFilter === VEHICLE_TYPES.NEW,
      onClick,
      data: { ref, type: VEHICLE_TYPES.NEW },
      value: newVehicleCount,
      enableClick: true,
    },
    {
      label: __('Used Vehicles'),
      selected: selectedFilter === VEHICLE_TYPES.USED,
      data: { ref, type: VEHICLE_TYPES.USED },
      onClick,
      value: usedVehicleCount,
      enableClick: true,
    },
  ];
  const openROKPI = [
    ...roKPI,
    {
      label: __('All Vehicles'),
      selected: selectedFilter === VEHICLE_TYPES.ALL,
      data: { ref, type: VEHICLE_TYPES.ALL },
      onClick,
      value: allVehicleCount,
      enableClick: true,
    },
  ];

  const zeroGLVehicleCount = _get(vehicleWithZeroGLBalance, 'openCount', 0);
  const adjustedPriceVehicleCount = _get(vehicleWithAjustedPrice, 'openCount', 0);
  const vehicleWithOptionMisMatchCount = tget(vehicleWithOptionMisMatch, 'openCount', 0);
  const vehicleWithMultiStyleIdMisMatchCount = tget(vehicleWithMultiStyleConflict, 'openCount', 0);
  const vehicleWithOemAlertsCount = tget(vehicleWithOemAlerts, 'openCount', 0);
  const alertKPI = [
    {
      label: __('Vehicles with Zero G/L Balance'),
      selected: selectedFilter === FILTER_TYPES.ZERO_GL_BALANCE,
      onClick,
      data: { ref, type: FILTER_TYPES.ZERO_GL_BALANCE },
      value: zeroGLVehicleCount,
      enableClick: true,
      labelClassName: 'flex-auto-grow-shrink',
    },
    {
      label: __('Vehicles with Adjusted Prices'),
      selected: selectedFilter === FILTER_TYPES.ADJUSTED_PRICES,
      onClick,
      data: { ref, type: FILTER_TYPES.ADJUSTED_PRICES },
      value: adjustedPriceVehicleCount,
      enableClick: true,
    },
    {
      label: __('Vehicles with Option Mismatch'),
      selected: selectedFilter === FILTER_TYPES.OPTION_MISMATCH,
      onClick,
      data: { ref, type: FILTER_TYPES.OPTION_MISMATCH },
      value: vehicleWithOptionMisMatchCount,
      enableClick: true,
      labelClassName: 'flex-auto-grow-shrink',
    },
    {
      label: __('Vehicles with Multiple Trims'),
      selected: selectedFilter === FILTER_TYPES.MULTI_STYLE_ID_MISMATCH,
      onClick,
      data: { ref, type: FILTER_TYPES.MULTI_STYLE_ID_MISMATCH },
      value: vehicleWithMultiStyleIdMisMatchCount,
      enableClick: true,
      labelClassName: 'flex-auto-grow-shrink',
    },
  ];

  if (isArcPlusAecProgram()) {
    alertKPI.push({
      label: __('Vehicles with OEM Alerts'),
      selected: selectedFilter === FILTER_TYPES.OEM_ALERTS,
      onClick,
      data: { ref, type: FILTER_TYPES.OEM_ALERTS },
      value: vehicleWithOemAlertsCount,
      enableClick: true,
      labelClassName: 'flex-auto-grow-shrink',
    });
  }

  switch (ref) {
    case KPI_TYPE.RO:
      return openROKPI;
    case KPI_TYPE.RECALLS:
    case KPI_TYPE.PDI:
      return roKPI;
    case KPI_TYPE.ALERT:
      return alertKPI;
    default:
  }
  return EMPTY_ARRAY;
};

export const getAverageROCloseTime = data => {
  const { ro } = data || EMPTY_OBJECT;
  const roAverageCloseTime = _get(ro, 'averageCloseTime') || 0;

  return roAverageCloseTime ? humanize(roAverageCloseTime, 'ms', true) : '-';
};
