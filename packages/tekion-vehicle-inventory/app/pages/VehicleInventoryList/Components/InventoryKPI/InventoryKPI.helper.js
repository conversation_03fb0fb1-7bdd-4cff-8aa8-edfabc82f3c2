import _castArray from 'lodash/castArray';
import _filter from 'lodash/filter';
import _isEqual from 'lodash/isEqual';
import _values from 'lodash/values';

import { EMPTY_ARRAY, EMPTY_STRING } from 'tbase/app.constants';
import Request from 'tbase/builders/request';
import OPERATORS from 'tbase/constants/filterOperators';
import { RO_STATUS_VALUES } from 'tbase/constants/repairOrder/roStatus';
import { STATUS } from 'tbase/constants/statuses';
import { VEHICLE_TYPES } from 'tbase/constants/vehicleInventory/vehicleTypes';
import { VEHICLE_STATUS } from 'tbase/constants/vehicleInventory/vehicleStatus';
import { ALERTS } from 'tbusiness/appServices/vehicleInventory/constants/vehicleAlerts';

import { KPI_FILTERS } from 'constants/constantEnum';

const getKPIFilter = val =>
  new Request().addFilter('kpi', OPERATORS.IN, _castArray(val), undefined, undefined, { type: 'kpi' }).filters;

const RO_STATUS = _filter(_values(RO_STATUS_VALUES), value => !_isEqual(value, STATUS.CLOSED));

const PDI_STATUS = [STATUS.OPEN, STATUS.IN_PROGRESS, STATUS.VOID_INITIATED, STATUS.VOIDED];

const RECALL_STATUS = [STATUS.CLOSED];

const DEFAULT_STATUS = [
  VEHICLE_STATUS.STOCKED_IN,
  VEHICLE_STATUS.DRAFT,
  VEHICLE_STATUS.FLOAT,
  VEHICLE_STATUS.RECEIVED,
  VEHICLE_STATUS.IN_TRANSIT,
  VEHICLE_STATUS.INVOICE_RECEIVED,
  VEHICLE_STATUS.IN_PRODUCTION,
  VEHICLE_STATUS.ON_ORDER,
  VEHICLE_STATUS.RETIRED,
  VEHICLE_STATUS.ON_HOLD,
  VEHICLE_STATUS.RESERVED,
  VEHICLE_STATUS.TRANSFERRED,
  VEHICLE_STATUS.TRANSFERRED_OUT,
  VEHICLE_STATUS.TRANSFERRED_IN,
  VEHICLE_STATUS.CANCELLED,
];

const ZERO_GL_BALANCE_STATUS = [VEHICLE_STATUS.STOCKED_IN];

const getStatusFilter = status => new Request().addFilter('status', OPERATORS.IN, status || DEFAULT_STATUS).filters;

const getROFilter = () => new Request().addFilter('roDetails.roStatus', OPERATORS.IN, RO_STATUS).filters;

const getPDIFilter = () =>
  new Request()
    .addFilter('roDetails.pdiJobDetail.jobStatus', OPERATORS.NIN, PDI_STATUS)
    .addFilter('pdiDetail.applicable', OPERATORS.IN, _castArray(true)).filters;

const getUVIFilter = () =>
  new Request()
    .addFilter('roDetails.uviJobDetail.jobStatus', OPERATORS.NIN, PDI_STATUS)
    .addFilter('uviDetail.applicable', OPERATORS.IN, _castArray(true)).filters;

const getRecallFilter = () =>
  new Request()
    .addFilter('recallDetails', OPERATORS.EXISTS, EMPTY_ARRAY, undefined, undefined, undefined, {
      skipValueCheck: true,
    })
    .addFilter('recallDetails.manufacturerRecallStatus', OPERATORS.NIN, RECALL_STATUS).filters;

const getDeletedFilter = () => new Request().addFilter('deleted', OPERATORS.IN, _castArray(false)).filters;

const getNonSpecialVehiclesFilter = () =>
  new Request().addFilter('vehicleType', OPERATORS.NIN, _castArray(VEHICLE_TYPES.SPECIAL)).filters;

const getZeroGLBalanceFilter = () =>
  new Request().addFilter('pricingDetails.glBalance', OPERATORS.LTE, _castArray(0)).filters;

const getAdjustedPricesFilter = () => {
  const { filters } = new Request()
    .addFilter('pricingDetails.adjustedInvoicePrice', OPERATORS.EXISTS, _castArray(true))
    .addFilter('pricingDetails.adjustedMsrp', OPERATORS.EXISTS, _castArray(true));
  return {
    operator: OPERATORS.BOOL,
    orFilters: filters,
  };
};

const getOptionMismatchFilter = () =>
  new Request().addFilter('alerts.key', OPERATORS.IN, _castArray(ALERTS.OPTION_MISMATCH)).filters;

const getMultiStyleIdMismatchFilter = () =>
  new Request().addFilter('alerts.key', OPERATORS.IN, _castArray(ALERTS.MULTI_STYLE_ID_MISMATCH)).filters;

const getVehicleTypeFilter = vehicleType =>
  new Request().addFilter('vehicleType', OPERATORS.IN, _castArray(vehicleType)).filters;

const getVehicleTypeAndStatusFilter = vehicleType => [...getVehicleTypeFilter(vehicleType), ...getStatusFilter()];

export const OPEN_RO_FILTER = {
  NEW: [...getVehicleTypeAndStatusFilter(VEHICLE_TYPES.NEW), ...getROFilter()],
  USED: [...getVehicleTypeAndStatusFilter(VEHICLE_TYPES.USED), ...getROFilter()],
  ALL: [...getStatusFilter(), ...getROFilter()],
};

export const OPEN_RECALL_FILTER = {
  NEW: [...getVehicleTypeAndStatusFilter(VEHICLE_TYPES.NEW), ...getRecallFilter()],
  USED: [...getVehicleTypeAndStatusFilter(VEHICLE_TYPES.USED), ...getRecallFilter()],
};

export const OPEN_PDI_FILTER = {
  NEW: [...getVehicleTypeAndStatusFilter(VEHICLE_TYPES.NEW), ...getPDIFilter()],
  USED: [...getVehicleTypeAndStatusFilter(VEHICLE_TYPES.USED), ...getUVIFilter()],
};

export const VEHICLE_WITH_ZERO_GL_FILTER = [
  ...getStatusFilter(ZERO_GL_BALANCE_STATUS),
  ...getDeletedFilter(),
  ...getZeroGLBalanceFilter(),
  ...getNonSpecialVehiclesFilter(),
];

export const VEHICLE_WITH_ADJUSTED_PRICES_FILTER = [...getDeletedFilter(), getAdjustedPricesFilter()];

export const VEHICLES_WITH_OPTION_MISMATCH_FILTER = [...getOptionMismatchFilter()];

export const VEHICLES_WITH_MULTI_STYLE_ID_MISMATCH_FILTER = [...getMultiStyleIdMismatchFilter()];

export const KPI_TYPE = {
  ALERT: 'alert',
  PDI: 'pdi',
  RO: 'ro',
  RECALLS: 'recalls',
};

export const kpiFilter = {
  [KPI_TYPE.RO]: {
    NEW: getKPIFilter(KPI_FILTERS.NEW_VEHICLES_WITH_RO),
    USED: getKPIFilter(KPI_FILTERS.USED_VEHICLES_WITH_RO),
    ALL: getKPIFilter(KPI_FILTERS.ALL_VEHICLES_WITH_RO),
  },
  [KPI_TYPE.RECALLS]: {
    NEW: getKPIFilter(KPI_FILTERS.NEW_VEHICLES_WITH_RECALLS),
    USED: getKPIFilter(KPI_FILTERS.USED_VEHICLES_WITH_RECALLS),
  },
  [KPI_TYPE.PDI]: {
    NEW: getKPIFilter(KPI_FILTERS.NEW_VEHICLES_WITH_PDI),
    USED: getKPIFilter(KPI_FILTERS.USED_VEHICLES_WITH_INSPECTION),
  },
  [KPI_TYPE.ALERT]: {
    ZERO_GL_BALANCE: getKPIFilter(KPI_FILTERS.VEHICLES_WITH_ZERO_GL),
    ADJUSTED_PRICES: getKPIFilter(KPI_FILTERS.VEHICLES_WITH_ADJUSTED_PRICES),
    OPTION_MISMATCH: getKPIFilter(KPI_FILTERS.VEHICLES_WITH_OPTION_MISMATCH),
    MULTI_STYLE_ID_MISMATCH: getKPIFilter(KPI_FILTERS.VEHICLES_WITH_MULTI_STYLE_ID_MISMATCH),
  },
};

export const KPI_FILTER_VALUES = {
  [KPI_FILTERS.NEW_VEHICLES_WITH_RO]: OPEN_RO_FILTER.NEW,
  [KPI_FILTERS.USED_VEHICLES_WITH_RO]: OPEN_RO_FILTER.USED,
  [KPI_FILTERS.ALL_VEHICLES_WITH_RO]: OPEN_RO_FILTER.ALL,
  [KPI_FILTERS.NEW_VEHICLES_WITH_RECALLS]: OPEN_RECALL_FILTER.NEW,
  [KPI_FILTERS.USED_VEHICLES_WITH_RECALLS]: OPEN_RECALL_FILTER.USED,
  [KPI_FILTERS.NEW_VEHICLES_WITH_PDI]: OPEN_PDI_FILTER.NEW,
  [KPI_FILTERS.USED_VEHICLES_WITH_INSPECTION]: OPEN_PDI_FILTER.USED,
  [KPI_FILTERS.VEHICLES_WITH_ZERO_GL]: VEHICLE_WITH_ZERO_GL_FILTER,
  [KPI_FILTERS.VEHICLES_WITH_ADJUSTED_PRICES]: VEHICLE_WITH_ADJUSTED_PRICES_FILTER,
  [KPI_FILTERS.VEHICLES_WITH_OPTION_MISMATCH]: VEHICLES_WITH_OPTION_MISMATCH_FILTER,
  [KPI_FILTERS.VEHICLES_WITH_MULTI_STYLE_ID_MISMATCH]: VEHICLES_WITH_MULTI_STYLE_ID_MISMATCH_FILTER,
};

export const FILTER_TYPES = {
  ZERO_GL_BALANCE: 'ZERO_GL_BALANCE',
  ADJUSTED_PRICES: 'ADJUSTED_PRICES',
  OPTION_MISMATCH: 'OPTION_MISMATCH',
  MULTI_STYLE_ID_MISMATCH: 'MULTI_STYLE_ID_MISMATCH',
  OEM_ALERTS: 'OEM_ALERTS',
};

export const KPI_FILTER_TO_REF_N_TYPE_MAP = {
  [KPI_FILTERS.NEW_VEHICLES_WITH_RO]: {
    ref: KPI_TYPE.RO,
    type: VEHICLE_TYPES.NEW,
  },
  [KPI_FILTERS.USED_VEHICLES_WITH_RO]: {
    ref: KPI_TYPE.RO,
    type: VEHICLE_TYPES.USED,
  },
  [KPI_FILTERS.ALL_VEHICLES_WITH_RO]: {
    ref: KPI_TYPE.RO,
    type: VEHICLE_TYPES.ALL,
  },
  [KPI_FILTERS.NEW_VEHICLES_WITH_RECALLS]: {
    ref: KPI_TYPE.RECALLS,
    type: VEHICLE_TYPES.NEW,
  },
  [KPI_FILTERS.USED_VEHICLES_WITH_RECALLS]: {
    ref: KPI_TYPE.RECALLS,
    type: VEHICLE_TYPES.USED,
  },
  [KPI_FILTERS.NEW_VEHICLES_WITH_PDI]: {
    ref: KPI_TYPE.PDI,
    type: VEHICLE_TYPES.NEW,
  },
  [KPI_FILTERS.USED_VEHICLES_WITH_INSPECTION]: {
    ref: KPI_TYPE.PDI,
    type: VEHICLE_TYPES.USED,
  },
  [KPI_FILTERS.VEHICLES_WITH_ZERO_GL]: {
    ref: KPI_TYPE.ALERT,
    type: FILTER_TYPES.ZERO_GL_BALANCE,
  },
  [KPI_FILTERS.VEHICLES_WITH_ADJUSTED_PRICES]: {
    ref: KPI_TYPE.ALERT,
    type: FILTER_TYPES.ADJUSTED_PRICES,
  },
  [KPI_FILTERS.VEHICLES_WITH_OPTION_MISMATCH]: {
    ref: KPI_TYPE.ALERT,
    type: FILTER_TYPES.OPTION_MISMATCH,
  },
  [KPI_FILTERS.VEHICLES_WITH_MULTI_STYLE_ID_MISMATCH]: {
    ref: KPI_TYPE.ALERT,
    type: FILTER_TYPES.MULTI_STYLE_ID_MISMATCH,
  },
};
