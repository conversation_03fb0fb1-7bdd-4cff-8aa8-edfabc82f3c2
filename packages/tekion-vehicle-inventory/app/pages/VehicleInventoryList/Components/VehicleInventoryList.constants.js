import { FILTER_TYPE } from 'tbase/constants/sales/vehicleAgeFilter';
import { isInchcapeOrRRG } from 'tbase/utils/sales/dealerProgram.utils';
import VEHICLE_MEDIA_ASSET_TYPES from 'tbusiness/appServices/crm/constants/vehicleAssetType';
import colors from 'tstyles/exports.scss';

import { INVENTORY_STATUS, KPI_FILTERS } from 'constants/constantEnum';
import { VEHICLE_STATUS } from 'constants/constants';

import COLUMN_IDS from '../VehicleInventoryList.columnIds';
import COLUMN_IDS_RV from '../VehicleInventoryList.columnIds_RV';

export {
  FILTER_TYPE,
  VEHICLE_AGE_PAYLOAD,
  VEHICLE_AGE_OPERATOR_PAYLOAD,
  VEHICLE_ENTERPRISE_AGE_PAYLOAD,
  VEHICLE_ENTERPRISE_AGE_OPERATOR_PAYLOAD,
} from 'tbase/constants/sales/vehicleAgeFilter';

export const DEFAULT_FILTER = [
  {
    type: 'status',
    operator: 'IN',
    values: [INVENTORY_STATUS.STOCKED_IN],
  },
];

export const INVENTORY_BULK_ACTIONS = {
  CREATE_RO: 'create',
  ATTACH_DOCUMENT: 'attach',
  LABEL_PRINT: 'print',
  DELETE: 'delete',
  POSTING: 'posting',
  START_DEAL: 'startDeal',
  MARKETABLE: 'marketable',
  RECOMMENDED: 'recommended',
  CHANGE_STATUS: 'changeStatus',
  MARK_AS_SOLD: 'markAsSold',
  MARK_AS_HOLD: 'markAsHold',
  ADD_TO_TEST_DRIVE_POOL: 'addToTestDrivePool',
  MARK_AS_DRAFT: 'markAsDraft',
  MARK_AS_IN_TRANSIT: 'markAsInTransit',
  EDIT_PRICE: 'editPrice',
};

export const RO_STATUS = {
  CLOSED: 'CLOSED',
};

export const NON_COMPATIBLE_KPI_FILTERS = ['status', 'vehicleType', 'kpi'];

export const CPO_FILTER_OPTIONS = [
  {
    label: __('Certified'),
    value: true,
  },
  {
    label: __('Not Certified'),
    value: false,
  },
];

export const KPI_FILTER_OPTIONS = [
  {
    label: __('All Vehicles with RO'),
    value: KPI_FILTERS.ALL_VEHICLES_WITH_RO,
  },
  {
    label: __('New Vehicles with RO'),
    value: KPI_FILTERS.NEW_VEHICLES_WITH_RO,
  },
  {
    label: __('Used Vehicles with RO'),
    value: KPI_FILTERS.USED_VEHICLES_WITH_RO,
  },
  {
    label: __('New Vehicles with Recalls'),
    value: KPI_FILTERS.NEW_VEHICLES_WITH_RECALLS,
  },
  {
    label: __('Used Vehicles with Recalls'),
    value: KPI_FILTERS.USED_VEHICLES_WITH_RECALLS,
  },
  {
    label: __('New Vehicles with PDI'),
    value: KPI_FILTERS.NEW_VEHICLES_WITH_PDI,
  },
  {
    label: __('Used Vehicles with inspection'),
    value: KPI_FILTERS.USED_VEHICLES_WITH_INSPECTION,
  },
  {
    label: __('Vehicles with Zero G/L Balance'),
    value: KPI_FILTERS.VEHICLES_WITH_ZERO_GL,
  },
  {
    label: __('Vehicles with Adjusted Prices'),
    value: KPI_FILTERS.VEHICLES_WITH_ADJUSTED_PRICES,
  },
  {
    label: __('Vehicles with Option Mismatch'),
    value: KPI_FILTERS.VEHICLES_WITH_OPTION_MISMATCH,
  },
  {
    label: __('Vehicles with Multiple Trims'),
    value: KPI_FILTERS.VEHICLES_WITH_MULTI_STYLE_ID_MISMATCH,
  },
  {
    label: __('Vehicles with OEM Alerts'),
    value: KPI_FILTERS.VEHICLES_WITH_OEM_ALERTS,
  },
];

export const TRANSMISSION_CONTROL_TYPE_OPTIONS = [
  {
    label: __('Automatic'),
    value: 'Automatic',
  },
  {
    label: __('Manual'),
    value: 'Manual',
  },
];

export const EXPORT_TYPES = {
  PDF: 'PDF',
  EXCEL: 'EXCEL',
};

export const EXPORT_FILE_TYPES = {
  EXPORT_AS_PDF: __('Export as Pdf'),
  EXPORT_AS_EXCEL: __('Export as Excel'),
};

export const TABLE_MANAGER_PROPS = {
  showFilter: true,
  showSubHeader: true,
  showSearch: false,
  showMultiSort: true,
  showL3Header: true,
  showPersonaPreferenceSave: true,
};

export const FILTER_KEYS = {
  DEALER_ID: 'dealerId',
  STATUS: 'status',
  MAKE: 'make',
  DISPLAY_MODEL: 'displayModel',
};

export const VEHICLE_IMAGE_SIZE = 24;

export const MAX_SELECTED_VEHICLES_PERMITTED = 50;

export const INDICATOR_COLUMN_PROPS = {
  fixed: 'left',
  resizable: false,
  sortable: false,
  width: 50,
};

export const RENDERER_TYPE = {
  TEXT: 'TEXT',
  PRICE: 'PRICE',
  USER: 'USER',
  DATE: 'DATE',
  NUMBER: 'NUMBER',
  NONE: 'NONE',
  BOOLEAN: 'BOOLEAN',
};

export const VEHICLE_TRANSFER_INFO_MAP = {
  TRANSFERED_TO: __('Transferred to'),
  TRANSFERED_FROM: __('Transferred from'),
  TRANSFERED_ON: __('Transferred on'),
  TRANSFERED_BY: __('Transferred by'),
  INVOICE_PRICE: __('Original Invoice price'),
  MARKED_UP_AMOUNT: __('Marked up amount'),
};

export const DAYS_ON_LOT_OPERATOR_PAYLOAD = {
  [FILTER_TYPE.EQUALS]: 'EQUAL_FILTER_DAYS_ON_LOT_SCRIPT',
  [FILTER_TYPE.GREATER_THAN]: 'GREATER_THAN_FILTER_DAYS_ON_LOT_SCRIPT',
  [FILTER_TYPE.GREATER_THAN_EQUAL]: 'GREATER_THAN_EQUAL_FILTER_DAYS_ON_LOT_SCRIPT',
  [FILTER_TYPE.LESS_THAN]: 'LESS_THAN_FILTER_DAYS_ON_LOT_SCRIPT',
  [FILTER_TYPE.LESS_THAN_EQUAL]: 'LESS_THAN_EQUAL_FILTER_DAYS_ON_LOT_SCRIPT',
};

export const DAYS_TO_SELL_OPERATOR_PAYLOAD = {
  [FILTER_TYPE.EQUALS]: 'EQUAL_FILTER_DAYS_TO_SELL_SCRIPT',
  [FILTER_TYPE.GREATER_THAN]: 'GREATER_THAN_FILTER_DAYS_TO_SELL_SCRIPT',
  [FILTER_TYPE.GREATER_THAN_EQUAL]: 'GREATER_THAN_EQUAL_FILTER_DAYS_TO_SELL_SCRIPT',
  [FILTER_TYPE.LESS_THAN]: 'LESS_THAN_FILTER_DAYS_TO_SELL_SCRIPT',
  [FILTER_TYPE.LESS_THAN_EQUAL]: 'LESS_THAN_EQUAL_FILTER_DAYS_TO_SELL_SCRIPT',
};

export const ALLOWED_IMAGE_TYPE = [
  VEHICLE_MEDIA_ASSET_TYPES.THREE_SIXTY_IMAGES,
  VEHICLE_MEDIA_ASSET_TYPES.NORMAL_IMAGES,
];

export const VIEW_VALUES = {
  LIST: 'LIST',
  CARD: 'CARD',
};

export const VIEW_OPTIONS = [
  { value: VIEW_VALUES.LIST, additional: { icon: 'icon-list-view' } },
  { value: VIEW_VALUES.CARD, additional: { icon: 'icon-thumbnail-view' } },
];

export const DEFAULT_CARD_VIEW_PAGE_SIZE = 10;
export const DEFAULT_PAGINATION = 50;

export const VEHICLE_STATUS_COLOR_MAP = {
  [VEHICLE_STATUS.STOCKED_IN]: colors.solidGreen,
};

export const ADVANCE_SEARCH_OPTIONS = [
  { value: 'all', label: __('All') },
  { value: COLUMN_IDS.VIN, label: __('VIN') },
  { value: COLUMN_IDS.STOCK_ID, label: __('Stock#') },
  { value: COLUMN_IDS.YEAR, label: __('Year') },
  { value: COLUMN_IDS.MAKE, label: __('Make') },
  { value: COLUMN_IDS.MODEL, label: __('Model') },
  { value: COLUMN_IDS.LICENSE_PLATE_NUMBER, label: __('Registration#'), isValid: isInchcapeOrRRG },
  { value: 'purchaseDetailsInfo.invoiceNumbers', label: __('Invoice Number'), isValid: isInchcapeOrRRG },
  { value: COLUMN_IDS.TRIM, label: () => (isInchcapeOrRRG() ? __('Variant') : __('Trim')) },
];

export const FILTER_GROUP_ACTION_TYPE = {
  CREATE: 'CREATE',
  DELETE: 'DELETE',
  UPDATE: 'UPDATE',
};

export const FILTER_ASSET_TYPE = 'VEHICLE_INVENTORY';

export const KEYS_TO_PICK_FOR_QUICK_FILTER = [
  'default',
  'filters',
  'groupName',
  'nonDeletable',
  'nonRenamable',
  'selected',
  'selectedQuickFilter',
  'order',
  'locked',
];

export const INVENTORY_ALL_COST_VIEW_EXPORT_PAYLOAD = [
  COLUMN_IDS.INVOICE_PRICE,
  COLUMN_IDS.BASE_INVOICE_PRICE,
  COLUMN_IDS.WHOLESALE_FINANCE_RESERVE,
  COLUMN_IDS.BUYING_PRICE,
  COLUMN_IDS.FLOORING_AMOUNT,
  COLUMN_IDS_RV.FLOORING_ORIGINAL_AMOUNT,
  COLUMN_IDS_RV.OUTSTANDING_FLOOR_PLAN,
];
