import React, { useState, PureComponent, useMemo } from 'react';
import { PropTypes } from 'prop-types';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _map from 'lodash/map';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from 'tbase/app.constants';
import { getOpenRecall } from 'tbase/helpers/vehicle.helper';
import { isInchcapeOrRRG } from 'tbase/utils/sales/dealerProgram.utils';
import Button from 'tcomponents/atoms/Button';
import Heading from 'tcomponents/atoms/Heading';
import Popover from 'tcomponents/molecules/popover/TPopover';
import PropertyControlledComponent from 'tcomponents/molecules/PropertyControlledComponent';
import Tooltip, { TOOLTIP_PLACEMENT } from 'tcomponents/atoms/tooltip';
import { isAECOnlyProgram } from 'twidgets/utils/vi/program.utils';

import Badge from 'molecules/Badge/Badge';

import { MESSAGES } from 'constants/constants';
import { checkForOpenInspectionJobs, shouldShowOpcodes } from 'helpers/repairOrder';
import RODetailsPopover, { RoDescription } from 'molecules/RODetailsPopover';
import { shouldLockVehicle } from 'helpers/common';

import { getOpcode, disableCreateRO } from 'helpers/vehicle.helper';
import { getAllPDIs } from '../../VehicleInventoryList.reader';
import styles from './VinDetails.module.scss';

class RODetails extends PureComponent {
  onCreateRO = event => {
    event.stopPropagation();
    const { createROCB, setVisible } = this.props;
    createROCB();
    setVisible(false);
  };

  renderRecall = openRecalls => {
    if (!openRecalls.length) return null;
    return (
      <React.Fragment>
        <Heading size={5}>{__('Recalls')}</Heading>
        {_map(openRecalls, ({ manufacturerRecallNumber, recallDescription, note }) => (
          <RoDescription opcode={manufacturerRecallNumber} description={recallDescription} note={note} />
        ))}
      </React.Fragment>
    );
  };

  renderPDIs = pdis => {
    if (!pdis.length) return null;
    return (
      <React.Fragment>
        <Heading size={5}>{__('Opcodes')}</Heading>
        {_map(pdis, ({ opcodeDetails }) => (
          <RoDescription opcode={getOpcode({ opcodeDetails })} description={_get(opcodeDetails, 'description')} />
        ))}
      </React.Fragment>
    );
  };

  render() {
    const { roDetails = EMPTY_ARRAY, vInfo, isFetching, setFetching } = this.props;
    const openRecalls = getOpenRecall(vInfo);
    const pdis = getAllPDIs(vInfo);
    // const status = _get(vInfo, 'status');
    const isCreateRODisabled = disableCreateRO(vInfo);
    const createRODisabledMessage = shouldLockVehicle(vInfo?.status) ? MESSAGES.RO_CREATION_DENIED_INFO : EMPTY_STRING;

    return (
      <div className={styles.popoverLayout}>
        <PropertyControlledComponent controllerProperty={shouldShowOpcodes(vInfo)}>
          <PropertyControlledComponent
            controllerProperty={!isAECOnlyProgram()}
            fallback={this.renderRecall(openRecalls)}>
            <div className="d-flex justify-content-between align-items-center">
              <Heading size={4}>{__('Open Repair Orders')}</Heading>
              <Tooltip placement={TOOLTIP_PLACEMENT.RIGHT} title={createRODisabledMessage}>
                <Button
                  view={Button.VIEW.TERTIARY}
                  highlightOnHover={false}
                  onClick={this.onCreateRO}
                  disabled={isCreateRODisabled}
                  className={isCreateRODisabled ? styles.disabledCreateRo : ''}>
                  {__('Create RO')}
                </Button>
              </Tooltip>
            </div>
            {this.renderRecall(openRecalls)}
            <PropertyControlledComponent controllerProperty={isInchcapeOrRRG() || checkForOpenInspectionJobs(vInfo)}>
              {this.renderPDIs(pdis)}
            </PropertyControlledComponent>

            {!_isEmpty(roDetails) && <div className={styles.separator} />}
          </PropertyControlledComponent>
        </PropertyControlledComponent>
        {!_isEmpty(roDetails) && (
          <RODetailsPopover
            roDetails={roDetails}
            heading={__('Ongoing Repair Orders')}
            showOnlyInProgressRO
            vehicleInfo={vInfo}
            isFetching={isFetching}
            setFetching={setFetching}
          />
        )}
      </div>
    );
  }
}

const OpenRO = ({ roDetails, badgeStyle, vInfo, createROCB }) => {
  const [isFetching, setFetching] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const roLabel = useMemo(() => (isAECOnlyProgram() ? 'R' : 'RO'), []);
  const getRODetails = (
    <RODetails
      createROCB={createROCB}
      roDetails={(roDetails || []).map(({ roId, roNumber }) => ({ roId, roNumber }))}
      vInfo={vInfo}
      setVisible={setIsVisible}
      isFetching={isFetching}
      setFetching={setFetching}
    />
  );

  return (
    <Popover
      placement="rightBottom"
      trigger="hover"
      content={getRODetails}
      visible={isVisible}
      onVisibleChange={visible => setIsVisible(visible)}>
      <div className={styles.roBadgeContainer}>
        <Badge content={roLabel} className={badgeStyle} />
      </div>
    </Popover>
  );
};

OpenRO.propTypes = {
  roDetails: PropTypes.array.isRequired,
  badgeStyle: PropTypes.object,
  vInfo: PropTypes.object,
  createROCB: PropTypes.func.isRequired,
};

OpenRO.defaultProps = {
  badgeStyle: EMPTY_OBJECT,
  vInfo: EMPTY_OBJECT,
};

export default OpenRO;
