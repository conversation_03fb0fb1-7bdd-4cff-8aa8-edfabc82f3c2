import _filter from 'lodash/filter';
import _map from 'lodash/map';
import _size from 'lodash/size';
import _sortBy from 'lodash/sortBy';

import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import { RESOURCE_TYPE } from 'tbase/bulkResolvers/constants/resourceType';
import { YES_OR_NO_OPTIONS } from 'tbase/constants/deal/common';
import { capitalize } from 'tbase/formatters/string';
import { shouldShowEnterpriseAge } from 'tbusiness/appServices/vehicleInventory/helpers/vehicle';
import { getVehicleStatuses } from 'tbusiness/appServices/vehicleInventory/helpers/vehicleStatus';
import { getVehicleKindOptions } from 'tbusiness/appServices/vehicleInventory/helpers/vehicleOption';
import dealerPropertyHelper from 'tcomponents/helpers/sales/dealerPropertyHelper';
import { getAsyncSelectResourceProps } from 'tcomponents/utils/filterUtils';
import { getSelectOptions, getYearOptions } from 'tbase/utils/sales';
import { isInchcape, isInchcapeOrRRG, isRRG } from 'tbase/utils/sales/dealerProgram.utils';
import { VEHICLE_INVENTORY_LIST_FILTER_IDS } from 'tbusiness/appServices/vehicleInventory/constants/filters';
import FILTER_TYPES from 'tcomponents/organisms/filterSection/constants/filterSection.filterTypes';
import {
  EQUALS,
  GREATER_THAN,
  GREATER_THAN_EQUAL,
  LESS_THAN,
  LESS_THAN_EQUAL,
  NOT_EQUALS,
  BTW,
  IN,
  NOT_IN,
} from 'tcomponents/organisms/filterSection/constants/filterSection.operators';
import {
  mapCustomFieldsToFilters,
  mapCustomPricingFieldsToFilters,
  getCustomFieldOperators,
} from 'twidgets/appServices/sales/helpers/customField.helper';
import {
  FORM_FIELD_LABEL_CONFIG,
  GENERAL_FIELD_KEYS,
  PRICING_FIELD_KEYS,
} from 'twidgets/appServices/sales/config/viFormFields';
import { CUSTOM_FIELD_TYPES } from 'twidgets/appServices/sales/constants/constants';
import OptionCodeSelectRenderer from 'twidgets/organisms/vi/OptionCodeSelectRenderer';
import { getMfrModelCodesOptions } from 'twidgets/helpers/vehicleInventory/vehicleMetaData.helpers';
import { isAecOrArcPlusAecProgram } from 'twidgets/utils/vi/program.utils';

import { getModelFilter } from 'factories/model';
import { VEHICLE_TYPE } from 'constants/constantEnum';
import { SALES_DESTINATION_ENUM_LABEL_MAP } from 'constants/constants';
import PROGRAM_CONFIG, { isARCBaseProgram } from 'constants/programConfig';
import {
  getUserAccessibleSitesList,
  getBodyClassOptions,
  getFuelTypeOptions,
  makeOptionFromValue,
} from 'helpers/common';
import {
  getSubStatusOptionsForFlagPicker,
  getSourceOptions,
  isAutomotiveEnabled,
  getBoolFieldOptions,
  showCountryDependentFields,
} from 'helpers/vehicle.helper';
import { CollapsableGroupHeading, WorkspaceOption } from 'molecules/enterpriseDealerFilterComponents';
import { hasAccountingCostView, hasInventoryAllCostPricesViewPermission } from 'permissions/inventory.permissions';
import { getFieldOptions } from 'utils';

import styles from 'styles/index.module.scss';

import { CPO_FILTER_OPTIONS, KPI_FILTER_OPTIONS } from './Components/VehicleInventoryList.constants';
import { getConditionBasedFilter, getCertificationStatusFilterOptions } from './VehicleInventoryList.helpers';

const getConditionBasedFilters = ({ stockSubtypes, customStatuses, locationField, certificationStatuses }) => [
  {
    id: 'pricingDetails.glBalance',
    name: __('G/L Balance'),
    type: FILTER_TYPES.AMOUNT_DOLLAR_RANGE,
    permission: () => hasAccountingCostView() && !dealerPropertyHelper.isViArcLiteEnabled(),
    additional: {
      enforcePrecision: false,
    },
    visible: true,
  },
  {
    id: 'stopDeliveryIndicator',
    name: __('Stop Delivery Indicator'),
    type: FILTER_TYPES.SINGLE_SELECT,
    additional: {
      options: YES_OR_NO_OPTIONS,
    },
    key: 'STOP_DELIVERY_INDICATOR',
    permission: showCountryDependentFields,
    visible: true,
  },
  {
    id: VEHICLE_INVENTORY_LIST_FILTER_IDS.KPI,
    name: __('KPI'),
    type: FILTER_TYPES.SINGLE_SELECT,
    additional: {
      options: KPI_FILTER_OPTIONS,
    },
    permission: () => !dealerPropertyHelper.isViArcLiteEnabled(),
    visible: true,
  },
  {
    id: VEHICLE_INVENTORY_LIST_FILTER_IDS.RECEIVED_AT_DEALERSHIP,
    name: __('Received at dealership'),
    type: FILTER_TYPES.SINGLE_SELECT,
    additional: {
      options: YES_OR_NO_OPTIONS,
    },
    permission: () => !dealerPropertyHelper.isViArcLiteEnabled() && !isInchcape(),
    visible: true,
  },
  {
    id: VEHICLE_INVENTORY_LIST_FILTER_IDS.VEHICLE_SUB_TYPE,
    name: __('Stock SubType'),
    type: FILTER_TYPES.MULTI_SELECT,
    additional: {
      options: stockSubtypes,
    },
    permission: () => !dealerPropertyHelper.isViArcLiteEnabled(),
    visible: true,
  },
  {
    id: VEHICLE_INVENTORY_LIST_FILTER_IDS.SUB_STATS,
    name: __('Sub Status'),
    type: FILTER_TYPES.MULTI_SELECT,
    additional: {
      options: getSubStatusOptionsForFlagPicker(customStatuses),
    },
    permission: () => !dealerPropertyHelper.isViArcLiteEnabled(),
    visible: true,
  },
  {
    id: VEHICLE_INVENTORY_LIST_FILTER_IDS.TRANSFERED,
    name: __('Transfered'),
    type: FILTER_TYPES.SINGLE_SELECT,
    additional: {
      options: getBoolFieldOptions(),
    },
    permission: () => !dealerPropertyHelper.isViArcLiteEnabled(),
    visible: true,
  },
  {
    id: VEHICLE_INVENTORY_LIST_FILTER_IDS.LOCATION_CODE,
    name: __('Vehicle Location'),
    type: FILTER_TYPES.MULTI_SELECT,
    additional: {
      options: getFieldOptions(locationField?.options),
    },
    permission: () => !dealerPropertyHelper.isViArcLiteEnabled(),
    visible: true,
  },
  {
    id: VEHICLE_INVENTORY_LIST_FILTER_IDS.CUSTOM_ORDER_NUMBER,
    name: __('Custom Order'),
    type: FILTER_TYPES.SINGLE_SELECT,
    additional: {
      options: YES_OR_NO_OPTIONS,
    },
    permission: dealerPropertyHelper.isViArcLiteEnabled,
    visible: true,
  },
  {
    id: VEHICLE_INVENTORY_LIST_FILTER_IDS.DEALER_CERTIFIED,
    name: __('Dealer Certified'),
    type: FILTER_TYPES.SINGLE_SELECT,
    additional: {
      options: CPO_FILTER_OPTIONS,
    },
    permission: () => !PROGRAM_CONFIG.shouldShowCertificationStatusV2(),
    visible: true,
  },
  {
    id: VEHICLE_INVENTORY_LIST_FILTER_IDS.CERTIFIED,
    name: __('Certified Pre-Owned'),
    type: FILTER_TYPES.SINGLE_SELECT,
    additional: {
      options: CPO_FILTER_OPTIONS,
    },
    permission: () => !PROGRAM_CONFIG.shouldShowCertificationStatusV2(),
    visible: true,
  },
  {
    id: VEHICLE_INVENTORY_LIST_FILTER_IDS.CERTIFICATION_STATUS,
    name: __('Certification Status'),
    type: FILTER_TYPES.MULTI_SELECT,
    additional: {
      options: getCertificationStatusFilterOptions(certificationStatuses),
    },
    permission: () => PROGRAM_CONFIG.shouldShowCertificationStatusV2(),
    visible: true,
  },
  {
    id: VEHICLE_INVENTORY_LIST_FILTER_IDS.INVOICES_WITH_ERROR,
    name: __('Invoices with Error'),
    type: FILTER_TYPES.NUMBER,
    permission: PROGRAM_CONFIG.shouldShowPurchaseInvoice,
    visible: true,
  },
];

const getVehicleTypeOptions = vehicleTypes =>
  _map(vehicleTypes, ({ type }) => ({ label: VEHICLE_TYPE[type], value: type }));

const getFilterLabel = () => {
  if (isRRG()) {
    return {
      [VEHICLE_INVENTORY_LIST_FILTER_IDS.SOURCE]: __('Origin'),
    };
  }
  if (isInchcape()) {
    return {
      [VEHICLE_INVENTORY_LIST_FILTER_IDS.SOURCE]: __('Purchase Source'),
    };
  }
  return {
    [VEHICLE_INVENTORY_LIST_FILTER_IDS.SOURCE]: __('Source'),
  };
};

export const getCommonFilters = ({
  makes,
  vehicleTypeOptions,
  statusActionsMapping,
  models,
  displayModelSource,
  selectedMakes,
}) => [
  {
    id: VEHICLE_INVENTORY_LIST_FILTER_IDS.YEAR,
    name: __('Year'),
    type: FILTER_TYPES.MULTI_SELECT,
    additional: {
      options: getYearOptions(),
    },
    visible: true,
  },
  {
    id: VEHICLE_INVENTORY_LIST_FILTER_IDS.MAKE,
    name: __('Make'),
    type: FILTER_TYPES.MULTI_SELECT,
    additional: {
      options: makes,
    },
    visible: true,
  },
  getModelFilter({
    models,
    displayModelSource,
    selectedMakes,
    type: FILTER_TYPES.MULTI_SELECT,
  }),
  {
    id: VEHICLE_INVENTORY_LIST_FILTER_IDS.VEHICLE_TYPE,
    name: __('Stock Type'),
    type: FILTER_TYPES.MULTI_SELECT,
    additional: {
      options: vehicleTypeOptions,
    },
    visible: true,
  },
  {
    id: VEHICLE_INVENTORY_LIST_FILTER_IDS.STATUS,
    name: __('Status'),
    type: FILTER_TYPES.MULTI_SELECT,
    additional: {
      options: getVehicleStatuses(statusActionsMapping),
    },
    visible: true,
  },
];

const getFilterTypes = (
  customFields,
  customPricingFields,
  isMultiOemSwitchEnabled,
  isRVVehicleEnabled,
  bodyStyles,
  stockSubtypes,
  models,
  metaData,
  customStatuses,
  sourceField,
  dealerFilterOptions,
  locationField,
  dealerMasterData,
  vehicleTypes,
  certificationStatuses,
  viActionsConfig,
  displayModelSource,
  selectedMakes,
  enterpriseV2Enabled,
  allModelCodes,
  selectedModels
) => {
  const {
    interiorColors,
    exteriorColors,
    makes,
    trimList,
    dealerSiteOptions,
    bodyClass,
    fuelTypes,
    certificationTiers,
  } = metaData;
  const { statusActionsMapping } = viActionsConfig || EMPTY_OBJECT;
  const vehicleTypeOptions = getVehicleTypeOptions(vehicleTypes);
  const filterLabelConfig = getFilterLabel();
  const sites = getUserAccessibleSitesList(dealerSiteOptions);

  const isRRGEnabled = isRRG();
  const isInchcapeOrRRGEnabled = isInchcapeOrRRG();
  const isARCEnabled = isARCBaseProgram();
  const isAutomotiveEnable = isAutomotiveEnabled();
  // Todo: isAsburyProgramEnabled to be replaced with isEnterpriseEnabled.
  const isAsburyEnabled = dealerPropertyHelper.isAsburyProgramEnabled();

  const isInventoryCostPriceViewEnabled = hasInventoryAllCostPricesViewPermission();

  const listFilters = _sortBy(
    [
      ...getConditionBasedFilter(
        getConditionBasedFilters({ stockSubtypes, customStatuses, locationField, certificationStatuses }),
        {
          dealerData: dealerMasterData,
        }
      ),
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.BODY_CLASS,
        name: __('Body Class'),
        type: FILTER_TYPES.MULTI_SELECT,
        additional: {
          options: isRVVehicleEnabled ? getSelectOptions(bodyClass) : getBodyClassOptions(),
        },
        visible: true,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.INTERIOR_COLOR,
        name: __('Interior Color'),
        type: FILTER_TYPES.ASYNC_MULTI_SELECT,
        resourceType: RESOURCE_TYPE.VEHICLE_INTERIOR_COLOR,
        resourceParams: getAsyncSelectResourceProps(RESOURCE_TYPE.VEHICLE_INTERIOR_COLOR),
        additional: {
          options: getSelectOptions(interiorColors),
        },
        visible: isARCEnabled,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.EXTERIOR_COLOR,
        name: __('Exterior Color'),
        type: FILTER_TYPES.ASYNC_MULTI_SELECT,
        resourceType: RESOURCE_TYPE.VEHICLE_EXTERIOR_COLOR,
        resourceParams: getAsyncSelectResourceProps(RESOURCE_TYPE.VEHICLE_EXTERIOR_COLOR),
        additional: {
          options: getSelectOptions(exteriorColors),
        },
        visible: true,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.DISCOUNT_EXPIRY_DATE,
        name: __('Discount Expiry Date'),
        type: FILTER_TYPES.DATE,
        visible: true,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.VEHICLE_AGE,
        name: __('Vehicle Age'),
        type: FILTER_TYPES.NUMBER_INPUT_WITH_RANGE,
        additional: {
          operators: [EQUALS, GREATER_THAN, GREATER_THAN_EQUAL, LESS_THAN, LESS_THAN_EQUAL, BTW],
        },
        visible: true,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.ENTERPRISE_AGE,
        name: __('Enterprise Age'),
        type: FILTER_TYPES.NUMBER_INPUT_WITH_RANGE,
        additional: {
          operators: [EQUALS, GREATER_THAN, GREATER_THAN_EQUAL, LESS_THAN, LESS_THAN_EQUAL, BTW],
        },
        visible: shouldShowEnterpriseAge({ enterpriseV2Enabled }),
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.MILAGE,
        name: __('Mileage'),
        type: FILTER_TYPES.NUMBER,
        visible: true,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.TRIM,
        name: FORM_FIELD_LABEL_CONFIG[GENERAL_FIELD_KEYS.TRIM_CODE](),
        type: FILTER_TYPES.MULTI_SELECT,
        additional: {
          options: getSelectOptions(trimList, false),
        },
        visible: true,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.RETAIL_PRICE,
        name: FORM_FIELD_LABEL_CONFIG[PRICING_FIELD_KEYS.RETAIL_PRICE](),
        type: FILTER_TYPES.AMOUNT_DOLLAR_RANGE,
        additional: {
          enforcePrecision: false,
        },
        visible: true,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.RECEIVED_DATE,
        name: __('Received Date'),
        type: FILTER_TYPES.DATE,
        visible: true,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.STOCKED_IN_DATE,
        name: __('Stocked In Date'),
        type: FILTER_TYPES.DATE,
        visible: true,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.SOLD_DATE,
        name: __('Sold Date'),
        type: FILTER_TYPES.DATE,
        visible: true,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.CREATE_TIME,
        name: __('Creation Date'),
        type: FILTER_TYPES.DATE,
        visible: true,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.MODIFIED_TIME,
        name: __('Last Modified Date'),
        type: FILTER_TYPES.DATE,
        visible: true,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.EXPITY_DATE,
        name: __('Offers Expiry Date'),
        type: FILTER_TYPES.DATE,
        visible: true,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.EXTERIOR_HEX_CODE,
        name: __('Exterior Hex Color'),
        type: FILTER_TYPES.ASYNC_MULTI_SELECT,
        resourceType: RESOURCE_TYPE.VEHICLE_EXTERIOR_COLOR_HEXCODE,
        resourceParams: getAsyncSelectResourceProps(RESOURCE_TYPE.VEHICLE_EXTERIOR_COLOR_HEXCODE),
        visible: true,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.SOURCE,
        name: filterLabelConfig[VEHICLE_INVENTORY_LIST_FILTER_IDS.SOURCE],
        type: FILTER_TYPES.MULTI_SELECT,
        additional: {
          options: getSourceOptions([sourceField], EMPTY_ARRAY, true),
        },
        visible: true,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.FLOORING_AMOUNT,
        name: __('Flooring Amount'),
        type: FILTER_TYPES.AMOUNT_DOLLAR_RANGE,
        additional: {
          operators: [EQUALS, GREATER_THAN, GREATER_THAN_EQUAL, LESS_THAN, LESS_THAN_EQUAL, NOT_EQUALS],
          enforcePrecision: false,
        },
        visible: isInventoryCostPriceViewEnabled,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.MARKETABLE,
        name: __('Marketable'),
        type: FILTER_TYPES.SINGLE_SELECT,
        additional: {
          options: getBoolFieldOptions(),
        },
        visible: true,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.IN_TEST_DRIVE_POOL,
        name: __('In Test Drive Pool'),
        type: FILTER_TYPES.SINGLE_SELECT,
        additional: {
          options: getBoolFieldOptions(),
        },
        visible: isInchcapeOrRRGEnabled,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.BODY_DOOR_COUNT,
        name: __('Body Door Count'),
        type: FILTER_TYPES.NUMBER,
        visible: isRRGEnabled,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.FIRST_HAND_OWNER,
        name: __('First Hand Owner'),
        type: FILTER_TYPES.SINGLE_SELECT,
        additional: {
          options: YES_OR_NO_OPTIONS,
        },
        visible: isRRGEnabled,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.OEM_BONUS,
        name: __('OEM Bonus'),
        type: FILTER_TYPES.SINGLE_SELECT,
        additional: {
          options: YES_OR_NO_OPTIONS,
        },
        visible: isRRGEnabled,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.FUEL_TYPE,
        name: __('Fuel Type'),
        type: FILTER_TYPES.SINGLE_SELECT,
        additional: {
          options: getFuelTypeOptions(_map(fuelTypes, 'value')),
        },
        visible: isInchcapeOrRRGEnabled,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.SALES_DESTINATION,
        name: __('Sales Destination'),
        type: FILTER_TYPES.SINGLE_SELECT,
        additional: {
          options: makeOptionFromValue(SALES_DESTINATION_ENUM_LABEL_MAP),
        },
        visible: isRRGEnabled,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.VEHICLE_KIND,
        name: __('Vehicle Type'),
        type: FILTER_TYPES.SINGLE_SELECT,
        additional: {
          options: getVehicleKindOptions(),
        },
        visible: isInchcapeOrRRGEnabled,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.IS_BONUS_ELIGIBLE,
        name: __('Eligibility to bonus on conversion'),
        type: FILTER_TYPES.SINGLE_SELECT,
        additional: {
          options: YES_OR_NO_OPTIONS,
        },
        visible: isRRGEnabled,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.FISCAL_HORSE_POWER,
        name: __('Fiscal Horse Power'),
        type: FILTER_TYPES.NUMBER,
        visible: isRRGEnabled,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.PROVIDER_ORDER_STATUS,
        name: __('OEM Order Status'),
        type: FILTER_TYPES.ASYNC_MULTI_SELECT,
        resourceType: RESOURCE_TYPE.VEHICLE_PROVIDER_ORDER_STATUS,
        resourceParams: getAsyncSelectResourceProps(RESOURCE_TYPE.VEHICLE_PROVIDER_ORDER_STATUS),
        visible: isRRGEnabled,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.ORDER_NUMBER,
        name: __('Order Number'),
        type: FILTER_TYPES.STRING,
        additional: {
          operators: getCustomFieldOperators(CUSTOM_FIELD_TYPES.FREE_TEXT),
        },
        visible: isRRGEnabled,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.IS_VAT_QUALIFYING,
        name: __('VAT Qualifying'),
        type: FILTER_TYPES.SINGLE_SELECT,
        additional: {
          options: YES_OR_NO_OPTIONS,
        },
        visible: isInchcapeOrRRGEnabled,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.INCLUSIVE_PRICE,
        name: FORM_FIELD_LABEL_CONFIG[PRICING_FIELD_KEYS.INCLUSIVE_PRICE],
        type: FILTER_TYPES.AMOUNT_DOLLAR_RANGE,
        additional: {
          enforcePrecision: false,
        },
        visible: true,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.OPTION_CODE,
        name: __('Option Code'),
        type: FILTER_TYPES.CUSTOM,
        additional: {
          component: OptionCodeSelectRenderer,
          valueKey: 'value',
          operators: [IN, NOT_IN],
          dropDownClassName: styles.filterDropDownClassName,
        },
        visible: true,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.MFR_MODEL_CODE,
        name: __('Manufacturer Model Code'),
        type: FILTER_TYPES.VIRTUALIZED_MULTI_SELECT,
        additional: {
          options: getMfrModelCodesOptions(allModelCodes, selectedMakes, selectedModels),
        },
        visible: true,
      },

      // NOTE: These are common filters, and are part of global serach VI page filter list
      ...getCommonFilters({
        makes,
        vehicleTypeOptions,
        statusActionsMapping,
        models,
        displayModelSource,
        selectedMakes,
      }),

      // RVVFilters
      {
        id: 'locationDetails.locationStatus',
        name: __('Location Status'),
        type: FILTER_TYPES.MULTI_SELECT,
        additional: {
          options: getSelectOptions(metaData?.locationStatus),
        },
        visible: isRVVehicleEnabled,
      },
      {
        id: 'recommended',
        name: __('Recommended'),
        type: FILTER_TYPES.SINGLE_SELECT,
        additional: {
          options: getBoolFieldOptions(),
        },
        visible: isRVVehicleEnabled,
      },
      {
        id: 'locationDetails.glLocation',
        name: __('G/L Location'),
        type: FILTER_TYPES.MULTI_SELECT,
        additional: {
          options: getSelectOptions(metaData?.glLocation),
        },
        visible: isRVVehicleEnabled,
      },
      {
        id: 'vehicleAdditionalDetails.originalTypeCode',
        name: __('Original Type Code'),
        type: FILTER_TYPES.MULTI_SELECT,
        additional: {
          options: getSelectOptions(metaData?.originalTypeCode, false),
        },
        visible: isRVVehicleEnabled,
      },
      {
        id: 'daysOnLot',
        name: __('Days On Lot'),
        type: FILTER_TYPES.NUMBER,
        visible: isRVVehicleEnabled,
      },
      {
        id: 'daysToSell',
        name: __('Days To Sell'),
        type: FILTER_TYPES.NUMBER,
        visible: isRVVehicleEnabled,
      },
      {
        id: 'vehicleAdditionalDetails.ageGroup',
        name: __('Age Group'),
        type: FILTER_TYPES.MULTI_SELECT,
        additional: {
          options: getSelectOptions(metaData?.ageGroup, false),
        },
        visible: isRVVehicleEnabled,
      },
      {
        id: 'saleType',
        name: __('Sale Type'),
        type: FILTER_TYPES.MULTI_SELECT,
        additional: {
          options: getSelectOptions(metaData?.saleType),
        },
        visible: isRVVehicleEnabled,
      },
      {
        id: 'locationDetails.plantLocation',
        name: __('Plant Location'),
        type: FILTER_TYPES.MULTI_SELECT,
        additional: {
          options: getSelectOptions(metaData?.plantLocation, true, [capitalize]),
        },
        visible: isRVVehicleEnabled,
      },
      {
        id: 'currentModel',
        name: __('Current Model'),
        type: FILTER_TYPES.SINGLE_SELECT,
        additional: {
          options: YES_OR_NO_OPTIONS,
        },
        visible: isRVVehicleEnabled,
      },
      {
        id: 'exclusiveProduct',
        name: __('Exclusive Product'),
        type: FILTER_TYPES.SINGLE_SELECT,
        additional: {
          options: YES_OR_NO_OPTIONS,
        },
        visible: isRVVehicleEnabled,
      },
      {
        id: 'trimDetails.floorPlan',
        name: __('Floor Plan'),
        type: FILTER_TYPES.MULTI_SELECT,
        additional: {
          options: getSelectOptions(metaData?.floorPlan),
        },
        visible: isRVVehicleEnabled,
      },
      {
        id: 'vehicleAdditionalDetails.floorPlanGroup',
        name: __('Floor Plan Group'),
        type: FILTER_TYPES.MULTI_SELECT,
        additional: {
          options: getSelectOptions(metaData?.floorPlanGroup),
        },
        visible: isRVVehicleEnabled,
      },
      {
        id: 'estimatedDeliveryDate',
        name: __('Delivery Date'),
        type: FILTER_TYPES.DATE,
        visible: isRVVehicleEnabled,
      },

      {
        id: 'trimDetails.brand',
        name: __('Brand'),
        type: FILTER_TYPES.MULTI_SELECT,
        additional: {
          options: _sortBy(getSelectOptions(metaData?.brand), 'label'),
        },
        visible: isRVVehicleEnabled,
      },
      {
        id: 'vehicleAdditionalDetails.rvType',
        name: __('RV Type'),
        type: FILTER_TYPES.MULTI_SELECT,
        additional: {
          options: getSelectOptions(metaData?.rvType),
        },
        visible: isRVVehicleEnabled,
      },

      // multiOEM filters
      {
        id: 'stockedInAtSiteId',
        name: __('Stocked-in at'),
        type: FILTER_TYPES.MULTI_SELECT,
        additional: {
          options: sites,
        },
        visible: isMultiOemSwitchEnabled,
      },
      {
        id: 'soldAtSiteId',
        name: __('Sold at'),
        type: FILTER_TYPES.MULTI_SELECT,
        additional: {
          options: sites,
        },
        visible: isMultiOemSwitchEnabled,
      },

      // automotiveFilters
      {
        id: 'trimDetails.bodyType',
        name: __('Body Type'),
        type: FILTER_TYPES.MULTI_SELECT,
        additional: {
          options: bodyStyles,
        },
        visible: isAutomotiveEnable,
      },

      // asburyFilters
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.WHOLESALE_FINANCE_RESERVE,
        name: __('Wholesale Finance Reserve'),
        type: FILTER_TYPES.AMOUNT_DOLLAR_RANGE,
        additional: {
          operators: [EQUALS, GREATER_THAN, GREATER_THAN_EQUAL, LESS_THAN, LESS_THAN_EQUAL, NOT_EQUALS],
          enforcePrecision: false,
        },
        visible: isAsburyEnabled && isInventoryCostPriceViewEnabled,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.PORT_HOLDBACK,
        name: __('Port Holdback'),
        type: FILTER_TYPES.AMOUNT_DOLLAR_RANGE,
        additional: {
          operators: [EQUALS, GREATER_THAN, GREATER_THAN_EQUAL, LESS_THAN, LESS_THAN_EQUAL, NOT_EQUALS],
          enforcePrecision: false,
        },
        visible: isAsburyEnabled,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.VANGUARD,
        name: __('Vanguard'),
        type: FILTER_TYPES.AMOUNT_DOLLAR_RANGE,
        additional: {
          operators: [EQUALS, GREATER_THAN, GREATER_THAN_EQUAL, LESS_THAN, LESS_THAN_EQUAL, NOT_EQUALS],
          enforcePrecision: false,
        },
        visible: isAsburyEnabled,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.TINT_NITRO,
        name: __('Tint Nitro'),
        type: FILTER_TYPES.AMOUNT_DOLLAR_RANGE,
        additional: {
          operators: [EQUALS, GREATER_THAN, GREATER_THAN_EQUAL, LESS_THAN, LESS_THAN_EQUAL, NOT_EQUALS],
          enforcePrecision: false,
        },
        visible: isAsburyEnabled,
      },
      {
        id: VEHICLE_INVENTORY_LIST_FILTER_IDS.CERTIFICATION_TIER,
        name: __('Certification Tier'),
        type: FILTER_TYPES.ASYNC_MULTI_SELECT,
        resourceType: RESOURCE_TYPE.VEHICLE_CERTIFICATION_TIER,
        resourceParams: getAsyncSelectResourceProps(RESOURCE_TYPE.VEHICLE_CERTIFICATION_TIER),
        additional: {
          options: getSelectOptions(certificationTiers),
        },
        visible: isAecOrArcPlusAecProgram(),
      },

      // Todo: Will need to be added later
      // {
      //   id: 'uvStockNumber',
      //   name: __('N° police'),
      //   type: FILTER_TYPES.STRING,
      // },
      ...mapCustomFieldsToFilters(customFields),
      ...mapCustomPricingFieldsToFilters(customPricingFields),
    ],
    ['name']
  );

  const applicableFilters = _filter(listFilters, 'visible');

  const dealerFilter = {
    id: 'dealerId',
    name: __('Dealer'),
    type: FILTER_TYPES.MULTI_SELECT,
    isDisabled: _size(dealerFilterOptions) <= 1,
    additional: {
      options: dealerFilterOptions,
      ...(enterpriseV2Enabled
        ? { components: { GroupHeading: CollapsableGroupHeading, Option: WorkspaceOption } }
        : { showSelectAll: true }),
    },
  };
  return [...applicableFilters, dealerFilter];
};

export default getFilterTypes;
