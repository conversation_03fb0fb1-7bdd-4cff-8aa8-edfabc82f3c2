import { defaultMemoize } from 'reselect';

import _castArray from 'lodash/castArray';
import _compact from 'lodash/compact';
import _concat from 'lodash/concat';
import _includes from 'lodash/includes';
import _isEmpty from 'lodash/isEmpty';
import _uniq from 'lodash/uniq';
import _filter from 'lodash/filter';
import _find from 'lodash/find';
import _get from 'lodash/get';
import _groupBy from 'lodash/groupBy';
import _head from 'lodash/head';
import _isObject from 'lodash/isObject';
import _isNil from 'lodash/isNil';
import _map from 'lodash/map';
import _max from 'lodash/max';
import _keys from 'lodash/keys';
import _keyBy from 'lodash/keyBy';
import _omit from 'lodash/omit';
import _reduce from 'lodash/reduce';
import _size from 'lodash/size';
import _sortBy from 'lodash/sortBy';
import _toNumber from 'lodash/toNumber';
import _toUpper from 'lodash/toUpper';
import _values from 'lodash/values';
import _every from 'lodash/every';
import _mapValues from 'lodash/mapValues';
import _toString from 'lodash/toString';
import _isEqual from 'lodash/isEqual';
import _some from 'lodash/some';
import _set from 'lodash/set';
import _join from 'lodash/join';
import _upperFirst from 'lodash/upperFirst';
import _toLower from 'lodash/toLower';

import { EMPTY_ARRAY, EMPTY_STRING, EMPTY_OBJECT, NO_DATA } from 'tbase/app.constants';
import Request from 'tbase/builders/request';
import OPERATORS from 'tbase/constants/filterOperators';
import { MILEAGE_STATUS_LABELS } from 'tbase/constants/vehicleInventory/vehicle';
import { VEHICLE_TYPES } from 'tbase/constants/vehicleInventory/vehicleTypes';
import { VEHICLE_STATUS } from 'tbase/constants/vehicleInventory/vehicleStatusV2';
import {
  DISPLAY_MODEL,
  BEST_STYLE_NAME,
  DISPLAY_MODEL_KEY_VS_VALUE,
} from 'tbase/constants/retail/salesSetup.constants';
import { capitalizeFirstLetterOnly, capitalizeFirstLetters } from 'tbase/formatters/string';
import { isCurrentSiteEntity } from 'tbase/helpers/dealer.helper';
import { getSelectedWorkspaceIdFromLS } from 'tbase/helpers/user.helper';
import ESReader from 'tbase/readers/EsResponse';
import TEnvReader from 'tbase/readers/Env';
import getCurrentSiteIdFromLS from 'tbase/utils/getCurrentSiteIdFromLS';
import { getSelectOptions, getYearOptions } from 'tbase/utils/sales';
import { isInchcape, isInchcapeOrRRG } from 'tbase/utils/sales/dealerProgram.utils';
import { getDisplayMakeText, getDisplayModalText, getVehicleAgeInDays } from 'tbase/helpers/vehicle.helper';
import { tget } from 'tbase/utils/general';
import { addDays, addMonths, getCurrentTime, getDifferenceAsDays } from 'tbase/utils/dateUtils';
import openWindowInNewTab from 'tbase/utils/openWindowInNewTab';
import VehicleReader from 'tbusiness/appServices/vehicleInventory/readers/vehicle';
import {
  getRestrictedDetailActions,
  getRestrictedListActions,
  getRestrictedDestinationStatuses,
  isSelectedVehicleAllowedToStartDeal,
} from 'tbusiness/appServices/vehicleInventory/helpers/vehicleStatus';
import { getVehicleDetailsRoute as getVehicleDetailsRouteFromFactory } from 'tbusiness/appServices/vehicleInventory/helpers/routes';
import { getVehicleEnterpriseAge } from 'tbusiness/appServices/vehicleInventory/helpers/vehicle';
import { BODY_TYPE_LABELS } from 'tbusiness/appServices/vehicleInventory/constants/bodyTypeOption';
import { WORKSPACE_TYPES } from 'tbusiness/constants/workspace/workspaceTypes';
import DealerPropertyHelper from 'tcomponents/helpers/sales/dealerPropertyHelper';
import {
  getFirstNonVoidedInvoice,
  getLastNonVoidedInvoice,
} from 'twidgets/helpers/vehicleInventory/vehicleInvoice.helpers';
import { PURCHASE_DETAILS_STATUS } from 'twidgets/constants/vi.constants';

import { DURATION_UNIT, RRG_ORIGIN_TO_STOCK_TYPE_LIST } from 'constants/constantEnum';
import {
  TEKION_MAKE_ID,
  RV_TYPES,
  VEHICLE_CATEGORY,
  COUNTRY_VS_FIELDS,
  RV_TRIM_FIELDS,
  CUSTOM_ORDER,
  APPLICABLE_VEHICLE_STATUSES_FOR_PRICE_EDITOR,
  VEHICLE_DESTINATION_STATUS_TO_ACTIONS,
  RESTRICTED_BULK_ACTION_MESSAGES,
  ROW_ACTIONS,
  CARD_VIEW_MEDIA_TYPES,
} from 'constants/constants';
import { getActionMap, ROW_ACTION_TO_VEHICLE_ACTION_MAP } from 'constants/config';
import PROGRAM_CONFIG from 'constants/programConfig';
import { showConfirmationModal } from 'organisms/ConfirmationModal';
import { INVOICE_TYPE } from 'organisms/PurchaseInformation/purchaseInformation.constants';
import {
  hasUsedVehicleValuationView,
  hasInventoryEdit,
  hasDetailedPricingView,
  hasDetailedPricingEdit,
} from 'permissions/inventory.permissions';
import {
  isSoldVehiclesAllowed,
  isReservedVehiclesAllowed,
  isOnHoldVehiclesAllowed,
} from 'permissions/desking.permissions';
import VehicleService from 'services/vehicle';
import VehicleAssetTrackingAPIs from 'services/assetTracking';
import RepairOrderAPIs from 'services/repairOrder';
import { getArrayLength } from 'utils/commonUtils';

import { DEAL_STATUS } from 'tbase/marketScan/constants/deal.constants';
import { isMainSite, checkIfUserHasSiteAccess, shouldLockVehicle, getWorkspaceNameFromId } from './common';
import { getCreateDealRoute } from './route';

export { getFirstNonVoidedInvoice, getLastNonVoidedInvoice };

export const getVehicleAge = (vehicle, agingField) =>
  Math.max(0, Math.floor(getVehicleAgeInDays(vehicle, { field: agingField })));

export const getModifiedOptionsList = (options, selectionOption, formatters) => {
  const allOptions = _compact([selectionOption, ...options]);
  return getSelectOptions(_uniq(allOptions), !_isEmpty(formatters), formatters);
};

export const getMileageStatusOptions = () =>
  _map(_keys(MILEAGE_STATUS_LABELS), value => ({ name: MILEAGE_STATUS_LABELS[value], value }));

export const getTrimDetails = trimDetails => _omit(trimDetails, ['oem', 'brand', 'actualBrand', 'mapped']);

export const isVehicleStatusSold = status => VEHICLE_STATUS[status] === VEHICLE_STATUS.SOLD;

export const getSourceOptions = (
  customFields,
  selectedSources = EMPTY_ARRAY,
  disabled = false,
  vehicleStockType,
  shouldFilterOnStockType = false
) => {
  const sourceOptions = _get(
    _find(customFields, field => field?.key === 'SOURCE'),
    'options'
  );
  const Sources = disabled
    ? sourceOptions
    : _filter(
        sourceOptions,
        option => _get(option, 'details.enable') || _includes(selectedSources, _get(option, 'name'))
      );

  const filteredSources = shouldFilterOnStockType
    ? _filter(Sources, source =>
        vehicleStockType !== VEHICLE_TYPES.USED
          ? _includes(RRG_ORIGIN_TO_STOCK_TYPE_LIST[vehicleStockType], _get(source, 'name'))
          : !(
              _includes(RRG_ORIGIN_TO_STOCK_TYPE_LIST[VEHICLE_TYPES.NEW], _get(source, 'name')) ||
              _includes(RRG_ORIGIN_TO_STOCK_TYPE_LIST[VEHICLE_TYPES.DEMO], _get(source, 'name'))
            )
      )
    : Sources;

  return _map(filteredSources, option => ({
    name: _get(option, 'name'),
    label: _get(option, 'details.displayName') || _get(option, 'details.displayKey'),
    value: option?.name,
    isEnabledInSetup: _get(option, 'details.enable'),
  }));
};

export const getBoolFieldOptions = () => [
  { label: __('Yes'), value: true },
  { label: __('No'), value: false },
];

export const isVehicleTypeNew = vehicleType => VEHICLE_TYPES[vehicleType] === VEHICLE_TYPES.NEW;

export const isVehicleTypeUsed = vehicleType => VEHICLE_TYPES[vehicleType] === VEHICLE_TYPES.USED;

export const hasValuation = ({ isEditMode, vehicleDetails, providerList, valuationDetails }) => {
  const status = VehicleReader.status(vehicleDetails);
  const checkForSoldVehicle =
    isVehicleStatusSold(status) && !_find(_values(valuationDetails), valuationDetail => !_isEmpty(valuationDetail));
  return shouldFetchValuationDetails({ isEditMode, vehicleDetails, providerList }) && !checkForSoldVehicle;
};

export const shouldFetchValuationDetails = ({ isEditMode, vehicleDetails, providerList }) => {
  const vehicleType = VehicleReader.vehicleType(vehicleDetails);
  return isEditMode && isVehicleTypeUsed(vehicleType) && _size(providerList) > 0 && hasUsedVehicleValuationView();
};

export const checkValuationSavedForProviders = valuationDetails => {
  const providers = _keys(valuationDetails);
  return _filter(providers, provider => !_isEmpty(valuationDetails[provider]));
};

export const hasAppraisals = ({ isEditMode, vehicleDetails, providerList, appraisalDetails }) => {
  const status = VehicleReader.status(vehicleDetails);
  const checkForSoldVehicle =
    isVehicleStatusSold(status) && !_find(_values(appraisalDetails), appraisal => !_isEmpty(appraisal));
  return shouldFetchValuationDetails({ isEditMode, vehicleDetails, providerList }) && !checkForSoldVehicle;
};

export const shouldPostToAccounting = vehicle => {
  const accountPosted = VehicleReader.accountPosted(vehicle);
  const vehicleType = VehicleReader.vehicleType(vehicle);

  return !accountPosted && VEHICLE_TYPES[vehicleType] !== VEHICLE_TYPES.SPECIAL;
};

export const checkIfVINHasSoldVehicle = async vin => {
  const requestDTO = new Request()
    .addFilter('vin', OPERATORS.IN, _castArray(vin))
    .addFilter('status', OPERATORS.IN, _castArray(VEHICLE_STATUS.SOLD));
  const { data } = await VehicleService.fetchVehicles(requestDTO);

  return ESReader.count(data);
};

export const getStockedInSiteFallback = () => {
  const currentSiteId = getCurrentSiteIdFromLS();
  if (isMainSite(currentSiteId)) {
    return undefined;
  }

  return currentSiteId;
};

export const isVehicleInCurrentLoggedInSite = vehicle =>
  !(DealerPropertyHelper.isMultiOEMEnabled() && !isCurrentSiteEntity(VehicleReader.stockedInAtSiteId(vehicle)));

export const getSubStatusOptionsForFlagPicker = vehicleSubStatusList =>
  _map(vehicleSubStatusList, ({ status, key, flag, enabled }) => ({
    label: status,
    value: key,
    flagColor: flag,
    enabled,
  }));

export const getMappedBodyType = (bodyType, bodyTypeMapping) => {
  const mappedBodyType = bodyType || EMPTY_STRING;
  const matched = _find(bodyTypeMapping, { type: _toUpper(mappedBodyType) });
  return matched || mappedBodyType;
};

export const getBodyTypeOptions = bodyTypes =>
  _map(bodyTypes, bodyType => ({ label: BODY_TYPE_LABELS[bodyType] || bodyType, value: bodyType }));

export const shouldRepostToAccounting = ({ vehicle }) =>
  DealerPropertyHelper.isVehicleRePostingEnabled() &&
  isLoggedInDealerVehicle(vehicle) &&
  _includes([VEHICLE_STATUS.SOLD, VEHICLE_STATUS.STOCKED_IN, VEHICLE_STATUS.RESERVED], VehicleReader.status(vehicle));

export const showConfirmationDialog = (title, message, onConfirmFn) => {
  showConfirmationModal(
    {
      title,
      message,
      submitBtnText: __('Confirm'),
    },
    onConfirmFn
  );
};

export const isRVDealerEnabled = () => DealerPropertyHelper.isRVDealerEnabled() || false;

export const isAutomotiveEnabled = () => !isRVDealerEnabled();

export const getModelAsSelectOptions = (allModels, displayModelSource = DISPLAY_MODEL) => {
  const models = _reduce(
    allModels,
    (result, value) => {
      const data = _get(value, ['data']) || EMPTY_ARRAY;
      return [
        ...result,
        ..._reduce(
          data,
          (acc, { model, displayModel, bestStyleName }) =>
            displayModelSource === BEST_STYLE_NAME
              ? [...acc, model, displayModel, bestStyleName]
              : [...acc, model, displayModel],
          EMPTY_ARRAY
        ),
      ];
    },
    EMPTY_ARRAY
  );
  return _sortBy(getSelectOptions(_uniq(_compact(models)), false), 'label');
};

export const getCondition = (standardMakes, makes, data, make) =>
  standardMakes
    ? _isEmpty(makes) || _includes(makes, _get(_head(data), TEKION_MAKE_ID))
    : _isEmpty(makes) || _includes(makes, make);

export const getAllBrands = (allModels, makes = [], selectedYear = EMPTY_STRING, standardMakes = false) => {
  const brands = _reduce(
    allModels,
    (result, value, make) => {
      const data = _get(value, ['data']) || EMPTY_ARRAY;
      const condition = getCondition(standardMakes, makes, data, make);
      if (condition) {
        return [
          ...result,
          ..._reduce(
            data,
            (acc, { year, mappingModels }) => {
              if (!_isEmpty(year) && selectedYear && _toNumber(selectedYear) !== year) {
                return acc;
              }
              return [...acc, mappingModels];
            },
            EMPTY_ARRAY
          ),
        ];
      }
      return result;
    },
    EMPTY_ARRAY
  );
  return _sortBy(_uniq(_compact(brands)));
};

export const isLoggedInDealerVehicle = vehicle => {
  const { dealerId } = TEnvReader.userInfo();
  const dealerID = VehicleReader.dealerId(vehicle);
  return dealerId === dealerID;
};

export const sharedInventoryCheck = vehicle => !isLoggedInDealerVehicle(vehicle) && !isRVDealerEnabled();

export const isLoggedInOrChildWorkspaceVehicle = (vehicle, enterpriseV2Workspaces) => {
  const currentWorkspaceId = _get(TEnvReader.userInfo(), 'dealerId') || getSelectedWorkspaceIdFromLS();
  const dealerID = VehicleReader.dealerId(vehicle);
  const allWorkspaceIds = getAllChildWorkspaceIds(currentWorkspaceId, enterpriseV2Workspaces);
  return _includes(allWorkspaceIds, dealerID);
};

export const sharedEnterpriseV2InventoryCheck = (vehicle, enterpriseV2Workspaces) =>
  !isLoggedInOrChildWorkspaceVehicle(vehicle, enterpriseV2Workspaces);

/**
 * Determines if a vehicle with the given status is eligible for a start deal.
 * This function checks user permissions to determine eligibility based on the vehicle status.
 *
 * @param {string} vehicleStatus - The current status of the vehicle.
 * @returns {boolean} - True if the vehicle is eligible for a start deal, false otherwise.
 *
 */
export const isVehicleEligibleForStartDeal = vehicleStatus => {
  switch (vehicleStatus) {
    case VEHICLE_STATUS.SOLD:
      return isSoldVehiclesAllowed();
    case VEHICLE_STATUS.RESERVED:
      return isReservedVehiclesAllowed();
    case VEHICLE_STATUS.ON_HOLD:
      return isOnHoldVehiclesAllowed();
    default:
      return true;
  }
};

/**
 * This function is used to check whether to disable the click on vehicle details or not.
 * @param {object} vehicle vehicle for which to check if we want to disable or not
 * @param {boolean} isEditMode check wheater we are creating new vehicle or it is already existing vehicle
 */
export const shouldReadOnlyDetails = ({
  vehicleDetails,
  isEditMode,
  enterpriseV2Enabled,
  enterpriseV2Workspaces,
  lite,
}) => {
  if (lite) {
    return isEditMode && !hasInventoryEdit();
  }

  const isValidLoggedInWorkspaceVehicle = enterpriseV2Enabled
    ? isLoggedInOrChildWorkspaceVehicle(vehicleDetails, enterpriseV2Workspaces)
    : isLoggedInDealerVehicle(vehicleDetails);

  return (
    isEditMode &&
    ((DealerPropertyHelper.isMultiOEMEnabled() &&
      !_isNil(VehicleReader.stockedInAtSiteId(vehicleDetails)) &&
      !checkIfUserHasSiteAccess(VehicleReader.stockedInAtSiteId(vehicleDetails))) ||
      !isValidLoggedInWorkspaceVehicle ||
      !hasInventoryEdit())
  );
};

/**
 * In this function we have three conditions with which we decide to disable vehicle details and make it read only
 * 1. If dealer is multioem then there is the check if the user has access to current logged in site or not
 * 2. If other dealers vehicle is loaded in the logged in dealer then user cannot edit the other dealer vehicle from logged in dealer
 * 3. If the user doesn't have inventory edit permission
 */

/**
 * This function is called when starting a deal from VI listing or details page
 * @param {array} selectedVehicles Vehicle(s) for which to start the deal
 */
export const handleStartDealForVehicles = selectedVehicles => {
  const dealsRoute = getCreateDealRoute();

  const eligibleVehicles = _filter(selectedVehicles, vehicle =>
    isVehicleEligibleForStartDeal(VehicleReader.status(vehicle))
  );
  const combinedVehicleIds = encodeURIComponent(JSON.stringify(_map(eligibleVehicles, 'id')));

  openWindowInNewTab(`${dealsRoute}?vehicleIDList=${combinedVehicleIds}`);
};

export const isCustomOrder = ({ vehicle }) => {
  const stockCurrentSource = VehicleReader.stockCurrentSource(vehicle);
  return stockCurrentSource === CUSTOM_ORDER;
};

export const shouldDisableStartDeal = selectedVehicles => {
  const vehicleStatuses = _map(selectedVehicles, vehicle => VehicleReader.status(vehicle));
  const isEligible = _every(vehicleStatuses, isVehicleEligibleForStartDeal);
  const isVehicleCustomOrder = _some(selectedVehicles, vehicle => isCustomOrder({ vehicle }));
  return !isEligible || isVehicleCustomOrder;
};
/**
 * A vehicle can be transferred only if:
 * 1) It has deals only in "Quote" status or no deals at all
 * 2) It has non-zero Invoice value
 * 3) GL account setup is done for the dealer
 * 3) IR account setup is done for the dealer
 * @param {array} activeDeals Current active deals for vehicle
 * @returns {Object} Error check and message to be shown
 */
export const validateTransferEligibility = ({
  activeDeals,
  vehicle,
  dealerAccountingSetup,
  interCompanyDealerships,
}) => {
  let showError = false;
  let message = EMPTY_STRING;
  const invoice = VehicleReader.invoicePrice(vehicle);
  const vehicleDealerId = VehicleReader.dealerId(vehicle);
  const interFranchiseSetup = tget(dealerAccountingSetup, [vehicleDealerId, 'interFranchiseSetup'], EMPTY_OBJECT);

  if (_size(activeDeals) === 0) showError = false;

  if (!invoice && !(invoice === 0)) {
    showError = true;
    message = 'errorMessageInvoice';
  } else if (_isEmpty(_get(interCompanyDealerships, vehicleDealerId))) {
    showError = true;
    message = 'errorMessageIFID';
  } else if (!interFranchiseSetup?.markUpGlAccountId) {
    showError = true;
    message = 'errorMessageMarkupID';
  } else if (!_every(activeDeals, deal => deal?.status === DEAL_STATUS.QUOTE)) {
    showError = true;
    message = 'errorMessageDeals';
  }

  return { showError, message };
};

export const getVehicleCategory = type => {
  if (isRVDealerEnabled() && (type === RV_TYPES?.MOTORIZED?.value || type === RV_TYPES?.TOWABLE?.value)) {
    return VEHICLE_CATEGORY.RV;
  }
  return VEHICLE_CATEGORY.AUTOMOTIVE;
};

export const shouldShowRoTag = vehicle => isVehicleInCurrentLoggedInSite(vehicle) || !isLoggedInDealerVehicle(vehicle);

export const isMultiLingualEnabled = () => DealerPropertyHelper.isMultiLingualEnabled() || false;

export const getMakesList = makeData => {
  const data = _isObject(makeData?.[0]) ? _map(makeData, TEKION_MAKE_ID) : makeData;
  return data;
};

export const isStandardMakeEnabled = () => DealerPropertyHelper.standardizedMakeSetupEnabled() || false;

export const showCountryDependentFields = ({ dealerData, fieldKey } = EMPTY_OBJECT) => {
  const countryCode = dealerData?.dealerCountryCode;
  return COUNTRY_VS_FIELDS?.[countryCode]?.[fieldKey] || false;
};

export const getModifiedTrimData = trimData => (!isRVDealerEnabled() ? _omit(trimData, RV_TRIM_FIELDS) : trimData);

export const getDealerDetailsFromIds = (dealerIds, dealerDetails) =>
  _map(
    _filter(dealerDetails, ({ dealerId }) => _includes(dealerIds, dealerId)),
    ({ dealerId, displayName, state, country, city }) => ({
      dealerId,
      displayName,
      state,
      country,
      city,
    })
  );

const keyDealerById = defaultMemoize(dealers => _keyBy(dealers, 'dealerId'));

export const getDealerNamesByIds = dealerIds => {
  const userInfo = TEnvReader.userInfo();
  const dealers = _get(userInfo, 'dealer', EMPTY_ARRAY);
  const dealersById = keyDealerById(dealers);
  return _map(dealerIds, dealerId => _get(dealersById, [dealerId, 'dealerDisplayName']));
};

export const showDisabledArea = (isLocked, enterpriseView) => isLocked && !enterpriseView;

export const makeOptionsForDropdown = allMakes =>
  _map(allMakes, ({ tekionMakeId, tekionMakeDisplayName }) => ({
    label: tekionMakeDisplayName,
    value: tekionMakeId,
  }));

export const getApplicableAdditionalCosts = (data, additionalCosts) => {
  if (_isEmpty(additionalCosts)) {
    return data;
  }
  return additionalCosts;
};

export const renderVehicleDetails = () =>
  !DealerPropertyHelper.isDSEStandAloneEnabled() && DealerPropertyHelper.isDealsFoundationEnabled();

// Options coming from the Source are mapped to create labels and show in Source Column

export const getSourceTypeOptions = defaultMemoize((sourceTypeData, onlyActive = true) => {
  let sourceTypeOptions = EMPTY_ARRAY;
  if (!_isEmpty(sourceTypeData)) {
    const options = _get(sourceTypeData, 'options') || EMPTY_ARRAY;
    const activeOptions = onlyActive
      ? _filter(options, option => {
          const details = _get(option, 'details') || EMPTY_OBJECT;
          const enable = _get(details, 'enable') || false;
          return enable;
        }) || EMPTY_ARRAY
      : options;
    if (!_isEmpty(activeOptions)) {
      sourceTypeOptions = _map(activeOptions, activeOption => {
        const name = _get(activeOption, 'name') || EMPTY_STRING;
        const details = _get(activeOption, 'details') || EMPTY_OBJECT;
        const displayName = _get(details, 'displayName') || EMPTY_STRING;
        return { label: displayName, value: name };
      });
    }
  }
  return sourceTypeOptions;
});

// Todo: Currently we are using dealerData?.currency and once we get the specific key for RRG dealer to determine wether to show or not to show the field based on vehicle type

export const shouldShowForDcUv = ({ stockType }) =>
  VEHICLE_TYPES[stockType] === VEHICLE_TYPES.USED || VEHICLE_TYPES.DEMO;

export const shouldShowForDcNew = ({ stockType }) =>
  VEHICLE_TYPES[stockType] === VEHICLE_TYPES.NEW || VEHICLE_TYPES.DEMO;

export const shouldShowForDemoCar = ({ stockType }) => VEHICLE_TYPES[stockType] === VEHICLE_TYPES.DEMO;

export const shouldShowForUsedCar = ({ stockType }) => VEHICLE_TYPES[stockType] === VEHICLE_TYPES.USED;

export const shouldShowForNewVehicle = ({ stockType }) => VEHICLE_TYPES[stockType] === VEHICLE_TYPES.NEW;

export const getOpcode = ({ opcodeDetails }) => _get(opcodeDetails, 'opcode') || _get(opcodeDetails, 'displayValue');

export const fetchUnAssignedTags = async () => {
  const payload = {
    type: 'getTag',
    data: 'un-assigned',
  };
  const { data } = await VehicleAssetTrackingAPIs.fetchUnAssignedTags(payload);
  return data;
};

export const getAllMakesOptions = ({ allMakes, originalAllMakes }) => {
  if (isStandardMakeEnabled()) {
    return makeOptionsForDropdown(originalAllMakes);
  }
  return getSelectOptions(allMakes, true, [capitalizeFirstLetters]);
};

export const fetchMigratedRO = async vin => {
  try {
    const { data } = await RepairOrderAPIs.fetchMigratedRO(vin);
    return data;
  } catch (error) {
    return error;
  }
};

export const getYearTargetingOptions = () =>
  _mapValues(getYearOptions(), ({ label, value }) => ({ label, value: _toString(value) }));

export const shoulRenderInfoModal = ({ modified, original }) => !_isEqual(modified, original);

export const shouldDisableEditPriceBulkAction = selectedVehicles => {
  const vehicleStatuses = _map(selectedVehicles, vehicle => VehicleReader.status(vehicle));
  const isNonApplicableStatusSelected = _some(
    vehicleStatuses,
    status => !_includes(APPLICABLE_VEHICLE_STATUSES_FOR_PRICE_EDITOR, status)
  );
  return isNonApplicableStatusSelected;
};

const getDestinationStatuesToActions = (rowActionsConfigByKey, destinationStatues, isDetailPage) =>
  _compact(
    _map(destinationStatues, vehicleStatus => {
      if (vehicleStatus === VEHICLE_STATUS.VOID && !isDetailPage) {
        return null;
      }
      return tget(rowActionsConfigByKey, VEHICLE_DESTINATION_STATUS_TO_ACTIONS[vehicleStatus], EMPTY_OBJECT);
    })
  );

export const getAllRestrictedActionsOnVehicleOnPopover = (
  { vehicle, viActionsConfig, rowActionsConfig },
  isDetailPage = true
) => {
  const rowActionsConfigByKey = _keyBy(rowActionsConfig, 'key');
  const destinationStatues = getRestrictedDestinationStatuses(vehicle, viActionsConfig);
  const pageSpecificActions = isDetailPage
    ? getRestrictedDetailActions(vehicle, viActionsConfig)
    : getRestrictedListActions(vehicle, viActionsConfig);

  const isLoggedInDealerVehicleFlag = isLoggedInDealerVehicle(vehicle);

  const ACTIONS_MAP = getActionMap(isLoggedInDealerVehicleFlag);

  return _concat(
    _map(pageSpecificActions, action => tget(rowActionsConfigByKey, ACTIONS_MAP[action], EMPTY_OBJECT)),
    getDestinationStatuesToActions(rowActionsConfigByKey, destinationStatues, isDetailPage)
  );
};

export const getRestrictedBulkActionItems = ({
  selectedVehicles,
  isRVDealer,
  availableListViewBulkActions,
  getBulkActionItems,
  getInventoryBulkActions,
  enterpriseV2Enabled,
}) => {
  const bulkActionItems = getBulkActionItems({
    selectedVehicles,
    isRVDealer,
    enterpriseV2Enabled,
  });
  if (!isInchcapeOrRRG()) {
    return bulkActionItems;
  }
  const baseBulkActionItems = _keyBy(bulkActionItems, 'value');
  return _compact(
    _map(availableListViewBulkActions, (value, key) => {
      const { forbiddenStatuses } = value;
      if (!_isEmpty(forbiddenStatuses)) {
        const originalBulkAction = _get(baseBulkActionItems, getInventoryBulkActions[key]);
        _set(originalBulkAction, 'disabled', !isSelectedVehicleAllowedToStartDeal(selectedVehicles, forbiddenStatuses));
        _set(originalBulkAction, 'disabledMessage', _get(RESTRICTED_BULK_ACTION_MESSAGES, key, EMPTY_STRING));
        return originalBulkAction;
      }
      return _get(baseBulkActionItems, getInventoryBulkActions[key]);
    })
  );
};

export const disableCreateRO = vehicleInfo =>
  !isLoggedInDealerVehicle(vehicleInfo) || !hasInventoryEdit() || shouldLockVehicle(vehicleInfo?.status);

export const isPurchaseInvoiceDraft = ({ accountingStatus }) =>
  _isNil(accountingStatus) || accountingStatus === PURCHASE_DETAILS_STATUS.CREATED;

export const getThumbnailImage = ({ thumbnailIcon, iconDetail, placeHolderImage }) => {
  const url = tget(thumbnailIcon, 'url', _get(iconDetail, 'thumbnailUrl'));
  if (url) {
    try {
      return url.indexOf('chromedata') > 0 && url.indexOf('http://') === 0 ? url.replace('http://', 'https://') : url;
    } catch (e) {
      return placeHolderImage;
    }
  }
  return placeHolderImage;
};

export const getGdis = vehicle => {
  const { vehicleDetails } = vehicle;
  const status = VehicleReader.status(vehicleDetails);
  const { vehicleSoldTime, groupStockedInTime } = vehicleDetails;
  switch (status) {
    case VEHICLE_STATUS.SOLD:
    case VEHICLE_STATUS.TRANSFERRED_OUT:
      if (!vehicleSoldTime || !groupStockedInTime) {
        return null;
      }
      return getDifferenceAsDays(vehicleSoldTime, groupStockedInTime);
    case VEHICLE_STATUS.DRAFT:
    case VEHICLE_STATUS.TENTATIVE:
    case VEHICLE_STATUS.CANCELLED:
      return null;
    default:
      if (!groupStockedInTime) {
        return null;
      }
      return getDifferenceAsDays(getCurrentTime(), groupStockedInTime);
  }
};

export const getDetailsPageStickyBannerData = ({
  isEditMode,
  vehicleDetails,
  enterpriseV2Enabled,
  enterpriseV2Workspaces,
}) => {
  if (isCustomOrder({ vehicle: vehicleDetails })) {
    return { shouldShow: true, bannerMessage: __('This vehicle is custom ordered') };
  }

  if (isEditMode && enterpriseV2Enabled && !isLoggedInDealerVehicle(vehicleDetails)) {
    const dealerId = VehicleReader.dealerId(vehicleDetails);
    const dealerName = getWorkspaceNameFromId(dealerId, enterpriseV2Workspaces);
    return { shouldShow: true, bannerMessage: __('This vehicle belongs to “{{dealerName}}”', { dealerName }) };
  }

  return { shouldShow: false };
};

const DEALER_FILTER_WORKSPACES_ORDER = [
  WORKSPACE_TYPES.CORPORATE,
  WORKSPACE_TYPES.DEALER_GROUP,
  WORKSPACE_TYPES.DEALER,
];

export const getEnterpriseV2DealerFilterOptions = ({
  enterpriseV2Metadata,
  enterpriseV2Workspaces,
  workspaceDetails,
}) => {
  const { accessibleDealers } = enterpriseV2Metadata || EMPTY_OBJECT;
  const workspacesDataById = _keyBy(enterpriseV2Workspaces, 'workspaceId');
  const accessibleDealersData = _map(accessibleDealers, id => tget(workspacesDataById, id, EMPTY_OBJECT));
  const accessibleDealersDataByWorkspaceType = _groupBy(accessibleDealersData, 'workspaceType');

  return _reduce(
    DEALER_FILTER_WORKSPACES_ORDER,
    (acc, workspaceType) => {
      const workspaces = accessibleDealersDataByWorkspaceType[workspaceType];
      if (_isEmpty(workspaces)) return acc;

      const options = _map(workspaces, workspace => {
        const { workspaceId, name, parentWorkspaceId } = workspace || EMPTY_OBJECT;
        return {
          id: workspaceId,
          label: name,
          value: workspaceId,
          parentWorkspaceId,
          ...(workspaceType === WORKSPACE_TYPES.DEALER && {
            parentWorkspaceName: tget(workspacesDataById, [parentWorkspaceId, 'name'], EMPTY_STRING),
          }),
        };
      });
      const sortedOptions = _sortBy(options, ['parentWorkspaceName', 'label']);
      const workspaceLabel = tget(workspaceDetails, ['workspaceName', workspaceType], NO_DATA);
      acc.push({
        id: workspaceType,
        label: `${workspaceLabel} (${_size(sortedOptions)})`,
        options: sortedOptions,
      });
      return acc;
    },
    []
  );
};

export const getAllChildWorkspaceIds = (workspaceId, enterpriseV2Workspaces) => {
  const workspacesById = _keyBy(enterpriseV2Workspaces, 'workspaceId');

  const getChildWorkspaces = id => {
    const workspace = workspacesById[id];
    if (!workspace) return EMPTY_ARRAY;

    // Only proceed if the workspace type is one of the specified types
    if (!_includes(DEALER_FILTER_WORKSPACES_ORDER, workspace?.workspaceType)) return EMPTY_ARRAY;

    const childWorkspaceIds = tget(workspace, 'childWorkspaceIds', EMPTY_ARRAY);
    // Recursively get all child workspaceIds and accumulate them
    const allChildWorkspaceIds = _reduce(
      childWorkspaceIds,
      (acc, childId) => _concat(acc, getChildWorkspaces(childId)),
      []
    );

    return [id, ...allChildWorkspaceIds];
  };

  return getChildWorkspaces(workspaceId);
};

// This implementation has been moved out to this to helper to get the vehicle details page route based on DP checks and vehicle id
// TODO:remove dealer property
export const getVehicleDetailsRoute = ({ vehicle = EMPTY_OBJECT, isExternal }) => {
  const { dealerId, id } = vehicle;
  const route = getVehicleDetailsRouteFromFactory({ vehicleId: id, isExternal });
  if (DealerPropertyHelper.isRVDealerEnabled() && !isLoggedInDealerVehicle(vehicle)) {
    return `${route}?dealerId=${dealerId}`;
  }
  return route;
};

/**
 * This function checks whether to show transfer vehicle action in vehicle listing
 */
export const validateTransferVehicleCondition = ({ enterpriseV2Enabled, workspaces }) => {
  const { dealer } = TEnvReader.userInfo();
  const numberOfDealers = enterpriseV2Enabled ? getArrayLength(workspaces) : getArrayLength(dealer);
  return numberOfDealers > 1 && !(DealerPropertyHelper.isMultiOEMEnabled() && isMainSite(getCurrentSiteIdFromLS()));
};

export const validateRequestVehicleTransferCondition = ({ enterpriseV2Enabled, workspaces }) => {
  const { dealer } = TEnvReader.userInfo();
  const numberOfDealers = enterpriseV2Enabled ? getArrayLength(workspaces) : getArrayLength(dealer);
  return (
    numberOfDealers > 1 &&
    PROGRAM_CONFIG.shouldShowTransferPage(enterpriseV2Enabled) &&
    !(DealerPropertyHelper.isMultiOEMEnabled() && isMainSite(getCurrentSiteIdFromLS()))
  );
};

export const getAllEnterpriseWorkspaceIds = enterpriseV2Workspaces =>
  _reduce(
    enterpriseV2Workspaces,
    (acc, workspace) => {
      const { workspaceId, workspaceType } = workspace || EMPTY_OBJECT;
      if (!_includes(DEALER_FILTER_WORKSPACES_ORDER, workspaceType)) return acc;
      return [...acc, workspaceId];
    },
    []
  );

export const shouldShowStartDeal = ({ vehicle = EMPTY_OBJECT, isEditMode = true, viActionsConfig }) => {
  if (isInchcapeOrRRG())
    return (
      isEditMode &&
      isLoggedInDealerVehicle(vehicle) &&
      _includes(
        getRestrictedDetailActions(vehicle, viActionsConfig),
        ROW_ACTION_TO_VEHICLE_ACTION_MAP[ROW_ACTIONS.START_DEAL]
      )
    );
  return isEditMode && isLoggedInDealerVehicle(vehicle) && isVehicleEligibleForStartDeal(VehicleReader.status(vehicle));
};

export const getCardViewMediaList = vehicle => {
  const vehicleMedia = VehicleReader.vehicleMedia(vehicle);
  return _reduce(
    CARD_VIEW_MEDIA_TYPES,
    (acc, type) => {
      const mediaDataForType = _find(vehicleMedia, { type });
      const mediaListForType = tget(mediaDataForType, 'mediaList', EMPTY_ARRAY);
      return [...acc, ...mediaListForType];
    },
    []
  );
};

export const areExistingInvoicesVoided = invoiceData =>
  _every(
    invoiceData,
    ({ accountingStatus, type }) =>
      accountingStatus === PURCHASE_DETAILS_STATUS.VOIDED || type === INVOICE_TYPE.CONSIGNMENT
  );

export const areExistingInvoicesAndConsignmentVoided = invoiceData =>
  _every(invoiceData, { accountingStatus: PURCHASE_DETAILS_STATUS.VOIDED });

export const hasPriceSummaryViewAccess = () => hasDetailedPricingView() || hasDetailedPricingEdit();

export const getUpdatedAdoptionDate = ({ consignmentDateConfig, selectedMake, consignmentDate, defaultDate }) => {
  const normalizedSelectedMake = _toLower(selectedMake);
  const durationToUpdate = _get(
    _find(consignmentDateConfig, config => {
      const normalizedMakes = _map(config.values, _toLower);
      return _includes(normalizedMakes, normalizedSelectedMake);
    }),
    'data'
  );

  if (!durationToUpdate) return defaultDate;

  const addDuration = durationToUpdate.unit === DURATION_UNIT.MONTHS ? addMonths : addDays;
  return addDuration(durationToUpdate.value, consignmentDate);
};

export const vehicleEnterpriseAgeFormatter = ({ vehicleDetails, agingField }) => {
  const enterpriseAge = getVehicleEnterpriseAge(vehicleDetails, agingField);
  return __('{{numOfDays}} Days', { numOfDays: _max([0, enterpriseAge]) });
};

export const getIsGroupAgingFieldDisabled = vehicleDetails => {
  if (isInchcape()) return false;
  const { status, transfered } = vehicleDetails || EMPTY_OBJECT;
  return transfered || status === VEHICLE_STATUS.TRANSFERRED_OUT;
};

export const getFormattedYearMakeModelDisplay = (vehicle, displayModelSource = DISPLAY_MODEL) => {
  if (_isEmpty(vehicle)) return EMPTY_STRING;
  const sanitizedModel = tget(
    vehicle,
    DISPLAY_MODEL_KEY_VS_VALUE[displayModelSource],
    VehicleReader.displayModel(vehicle) || VehicleReader.model(vehicle)
  );
  let sanitizedMake = getDisplayMakeText(vehicle);
  if (!isStandardMakeEnabled()) {
    sanitizedMake = capitalizeFirstLetterOnly(sanitizedMake);
  }
  const ymmGrouped = _compact([_toString(VehicleReader.year(vehicle)), sanitizedMake, _upperFirst(sanitizedModel)]);
  return _join(ymmGrouped, ' ');
};

export const getFormattedYearMakeBrandModel = (vehicle, displayModelSource = DISPLAY_MODEL, isRVDealer = false) => {
  if (_isEmpty(vehicle)) return EMPTY_STRING;
  if (!isRVDealer) return getFormattedYearMakeModelDisplay(vehicle, displayModelSource);
  const sanitizedModel = getDisplayModalText(vehicle, displayModelSource);
  let sanitizedMake = getDisplayMakeText(vehicle);
  if (!isStandardMakeEnabled()) {
    sanitizedMake = capitalizeFirstLetterOnly(sanitizedMake);
  }
  const yearMakeBrandModelGrouped = _compact([
    _toString(VehicleReader.year(vehicle)),
    sanitizedMake,
    _get(vehicle, 'trimDetails.brand'),
    _upperFirst(sanitizedModel),
  ]);
  return _join(yearMakeBrandModelGrouped, ' ');
};

export const getScopeIds = (enterpriseV2Enabled, enterpriseV2Workspaces) => {
  if (enterpriseV2Enabled) {
    const scopeIds = _reduce(
      enterpriseV2Workspaces,
      (acc, workspace) => {
        const { workspaceId, workspaceType, tenantId } = workspace || EMPTY_OBJECT;
        if (!_includes(DEALER_FILTER_WORKSPACES_ORDER, workspaceType)) return acc;
        return [...acc, { dealerId: workspaceId, tenantId }];
      },
      []
    );
    return { scopeIds };
  }

  const { dealer, tenantName } = TEnvReader.userInfo();

  const scopeIds = _map(dealer, dealerItem => ({
    dealerId: dealerItem?.dealerId,
    tenantId: tenantName,
  }));

  return { scopeIds };
};

export const isPoLinkDisabled = poDealerId => {
  const { dealerId } = TEnvReader.userInfo();
  return dealerId !== poDealerId;
};
