import _every from 'lodash/every';
import _filter from 'lodash/filter';
import _find from 'lodash/find';
import _get from 'lodash/get';
import _head from 'lodash/head';
import _includes from 'lodash/includes';
import _isEmpty from 'lodash/isEmpty';
import _keyBy from 'lodash/keyBy';
import _map from 'lodash/map';
import _reject from 'lodash/reject';
import _reduce from 'lodash/reduce';
import _set from 'lodash/set';
import _toUpper from 'lodash/toUpper';
import _pick from 'lodash/pick';
import _values from 'lodash/values';
import _compact from 'lodash/compact';
import _uniqBy from 'lodash/uniqBy';
import _some from 'lodash/some';
import _split from 'lodash/split';
import _last from 'lodash/last';
import _join from 'lodash/join';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING, NO_DATA } from 'tbase/app.constants';
import { VEHICLE_TYPES, VEHICLE_TYPES_TO_LABEL } from 'tbase/constants/vehicleInventory/vehicleTypes';
import { formatCurrency } from 'tbase/formatters/formatCurrency';
import { getSelectedDealerSites } from 'tbase/helpers/oemSite';
import TEnvReader from 'tbase/readers/Env';
import { getFormattedDateTime, DATE_TIME_FORMATS } from 'tbase/utils/dateUtils';
import { tget } from 'tbase/utils/general';
import dealerInfoReader from 'tbase/readers/DealerInfo';
import { VEHICLE_STATUS } from 'tbase/constants/vehicleInventory/vehicleStatus';
import UserReader from 'tbase/readers/User';
import { VEHICLE_STATUS_TO_LABEL } from 'tbase/constants/vehicleInventory/vehicleStatusV2';
import { isInchcape, isInchcapeOrRRG, isRRG } from 'tbase/utils/sales/dealerProgram.utils';
import { getSelectOptions } from 'tbase/utils/sales';
import { FUEL_TYPE_LABELS } from 'tbusiness/appServices/vehicleInventory/constants/fuelTypeOption';
import { BODY_TYPE_LABELS } from 'tbusiness/appServices/vehicleInventory/constants/bodyTypeOption';
import DealerPropertyHelper from 'tcomponents/helpers/sales/dealerPropertyHelper';
import { GENERAL_FIELD_KEYS } from 'twidgets/appServices/sales/config/viFormFields';
import { OPTION_SOURCES } from 'twidgets/organisms/vi/OptionsConfigV2/constants/vehicleOptionsBuilder.constants';
import SalesDealerPropertyHelper from 'twidgets/appServices/sales/helpers/salesDealerPropertyHelper';
import { DATE_TIME_FORMAT } from '@tekion/tekion-conversion-web';

import { MARKET_CLASS_CATEGORY_OPTIONS } from 'constants/commonConfig';
import { PACKS_CONFIGURATION_CONFIG } from 'constants/config';
import {
  INVENTORY_LOG_FIELD_TYPE,
  INVENTORY_LOG_SORT_SCRIPT,
  INVENTORY_LOG_TYPES,
  PAYTYPE,
  USER_INFO_KEYS,
  LOCKED_VEHICLE_STATUS,
  RESTRICTED_STATUS_FOR_INVOICE_ADDITION,
} from 'constants/constants';
import {
  CONTROL_NUMBERS,
  CONTROL_TAG_LABEL,
  CUSTOMER_SENSITIVE_FIELDS,
  STORE_TYPE_VALUES,
  TRADE_OWNERSHIP_TYPES,
  VEHICLE_ORDER_STATUS_CONFIG,
} from 'constants/constantEnum';
import PROGRAM_CONFIG from 'constants/programConfig';

export const formattedDate = (column, fieldName, getFormattedDateAndTime) => {
  const value = _get(column, fieldName);

  if (!value) return NO_DATA;

  return getFormattedDateAndTime
    ? getFormattedDateAndTime({ value, formatType: DATE_TIME_FORMAT.DATE_ABBREVIATED_MONTH_YEAR })
    : getFormattedDateTime(value, DATE_TIME_FORMATS.ABBREVIATED_MONTH_DATE_YEAR);
};

export const formattedCurrency = (column, fieldName) => {
  const value = _get(column, fieldName);
  return value ? formatCurrency(value) : NO_DATA;
};

export const getDealerAddressDetails = (dealerMasterData, params) => {
  const dealerContact = dealerInfoReader.dealerAddress(dealerMasterData);
  const dealerAddress = _head(dealerContact);
  return !_isEmpty(params) ? _pick(dealerAddress, [...params]) : dealerAddress;
};

export const extractResponseFromGroupedData = response => {
  const groups = _get(response, 'data.groups') || EMPTY_ARRAY;
  if (!_isEmpty(groups)) {
    const activeData = _get(_head(groups), 'buckets') || EMPTY_ARRAY;
    return _reduce(activeData, (acc, { key, docCount }) => ({ ...acc, [_toUpper(key)]: docCount }), EMPTY_OBJECT);
  }
  return EMPTY_OBJECT;
};

export const getPermittedActions = actions => _filter(actions, action => _every(action?.validFor, item => item()));

export const getConditionalRendering = (
  renderConditions,
  { vehicle, actionConfig, enterpriseV2Enabled, workspaces, enterpriseV2Workspaces }
) =>
  _every(renderConditions, condition =>
    condition({ vehicle, actionConfig, enterpriseV2Enabled, workspaces, enterpriseV2Workspaces })
  );

export const getSiteNameFromSiteId = (siteId, siteOptions) => _get(_find(siteOptions, { value: siteId }), 'label');

export const removeMainSite = dealerSiteOptions => _reject(dealerSiteOptions, 'additional.mainSite');

export const siteIdsArrayToSiteNames = (valuesArray, dealerSiteOptions) =>
  _map(valuesArray, val => getSiteNameFromSiteId(val, dealerSiteOptions));

export const isMainSite = siteId => _includes(siteId, '-1');

export const getUserAccessibleSiteIds = () => {
  const { dealerId, dealerOemSites } = TEnvReader.userInfo();
  const userAccessibleSites = getSelectedDealerSites(dealerId, dealerOemSites);
  return _map(userAccessibleSites, 'siteId');
};

export const getUserAccessibleSitesList = (dealerSites, siteId) => {
  const subSites = _reject(dealerSites, 'additional.mainSite');
  const siteIdArray = getUserAccessibleSiteIds();
  const siteOptions = _filter(subSites, ({ value }) => _includes([...siteIdArray, siteId], value));
  return _map(siteOptions, option => ({ ...option, name: _get(option, 'value') }));
};

export const checkIfUserHasSiteAccess = siteId => _includes([...getUserAccessibleSiteIds()], siteId);

export const sourceIdToSourceName = (sourceId, sources) =>
  tget(_find(sources, { value: sourceId }), 'label', EMPTY_STRING);

export const sourceIdsToSourceNames = (sourceIds, sources) =>
  _map(sourceIds, key => sourceIdToSourceName(key, sources));

export const addContentType = allImages =>
  _map(allImages, image => _set(image, 'contentType', image?.file?.type || image?.type));

export const getJournalNumber = (journalId, journalAccounts) => {
  const journalAccount = _find(_values(journalAccounts), { id: journalId });
  return __('{{account}} - {{name}}', { account: journalAccount?.journalNumber, name: journalAccount?.journalName });
};

export const checkIfVehicleConfiguratorEnabled = (
  isVehicleConfiguratorEnabled,
  vehicleConfiguratorFormValue,
  options,
  styleId,
  isEditMode
) => {
  if (isVehicleConfiguratorEnabled && vehicleConfiguratorFormValue && (!isEditMode || !_isEmpty(styleId))) {
    return true;
  }
  return false;
};

export const shouldShowEstimatedDeliveryDate = ({ vehicleStatus }) => {
  if (
    vehicleStatus === VEHICLE_STATUS.IN_TRANSIT ||
    vehicleStatus === VEHICLE_STATUS.DRAFT ||
    vehicleStatus === VEHICLE_STATUS.NEW
  ) {
    return true;
  }
  return false;
};

export const isVlsEnabled = setupInfo => tget(setupInfo, 'salesSetup.isVLSEnabled', false);

export const getActiveUsersOfLoggedInDealer = users => {
  const activeUsers = _filter(users, user => UserReader.isActive(user));

  const userAccessibleDealer = _filter(activeUsers, user => hasUserAccessToDealerVsSite(user));

  return userAccessibleDealer;
};

const hasUserAccessToDealerVsSite = user => {
  const isMultiOemEnabled = DealerPropertyHelper.isMultiOEMEnabled();
  const loggedInDealerId = getCurrentUserInfo(USER_INFO_KEYS.DEALER_ID);
  const dealerAccess = _find(_get(user, 'dealerOemSites'), dealer => dealer?.dealerId === loggedInDealerId);

  if (isMultiOemEnabled) {
    return hasSiteAccess(dealerAccess, isMultiOemEnabled);
  }
  return hasDealerAccess(user);
};

const hasDealerAccess = user => {
  const loggedInDealerId = getCurrentUserInfo(USER_INFO_KEYS.DEALER_ID);
  const dealerAccess = !_isEmpty(
    _find(UserReader.dealerAndRoles(user), dealer => dealer?.dealerId === loggedInDealerId)
  );
  return dealerAccess;
};

const hasSiteAccess = (dealerAccess, isMultiOemEnabled) => {
  const loggedInSiteId = getCurrentUserInfo(USER_INFO_KEYS.TEKSITE_ID);
  const siteAccess = !_isEmpty(_find(_get(dealerAccess, 'oemSites'), site => site?.siteId === loggedInSiteId));

  return isMultiOemEnabled && siteAccess;
};

const getCurrentUserInfo = key => {
  const appStateHeaders = TEnvReader.userInfo();
  return _get(appStateHeaders, key);
};

export const getDealerNameFromId = (dealerID, dealers) => {
  const dealer = _find(dealers, { dealerId: dealerID });
  return dealer?.dealerDisplayName;
};

export const getWorkspaceNameFromId = (workspaceId, enterpriseV2Workspaces) => {
  const workspacesById = _keyBy(enterpriseV2Workspaces, 'workspaceId');
  return tget(workspacesById, [workspaceId, 'name'], EMPTY_STRING);
};

export const getVehicleStoreType = (isBuildVehicle = false) => {
  if (isBuildVehicle) return STORE_TYPE_VALUES.AUTOMOBILE;
  if (DealerPropertyHelper.isRVDealerEnabled()) return STORE_TYPE_VALUES.RV;
  return PROGRAM_CONFIG.shouldShowV2() ? STORE_TYPE_VALUES.AUTOMOBILE : STORE_TYPE_VALUES.AUTOMOTIVE;
};

export const isCVDVinDecodingSource = () => DealerPropertyHelper.isEnhancedVINDecodingEnabled();

export const getYesNoOptions = options =>
  _map(options, option => ({
    ...option,
    value: _get(option, 'name') === 'Yes',
  }));

export const combineAllModels = combinedModelResponse =>
  _reduce(
    _map(combinedModelResponse, response => response?.data),
    (acc, models) => ({ ...acc, ...models }),
    EMPTY_OBJECT
  );

export const getSortWithCustomPricingPayload = sortPayload =>
  _map(sortPayload, sort => {
    const { field, key, order } = sort || EMPTY_OBJECT;
    const splitFieldPath = _split(field, '.');
    const nestedPath = _head(splitFieldPath);

    if (nestedPath !== 'customPricingFields') return sort;

    return {
      key,
      order,
      nestedPath,
      field: 'customPricingFields.value',
      nestedFieldSort: true,
      filtersForNestedSort: [{ field: 'customPricingFields.fieldName', values: [key] }],
    };
  });

export const getModifiedSort = payload => {
  const sortPayload = _map(payload, sortData => {
    switch (sortData?.key) {
      case INVENTORY_LOG_TYPES.VEHICLE_AGE:
        return {
          order: sortData?.order,
          tekScriptSort: {
            scriptSortType: 'NUMBER',
            tekScript: {
              idOrCode: INVENTORY_LOG_SORT_SCRIPT.VEHICLE_AGE_SORT_SCRIPT,
              params: {},
            },
          },
        };
      case INVENTORY_LOG_TYPES.ENTERPRISE_AGE:
        return {
          order: sortData?.order,
          field: INVENTORY_LOG_FIELD_TYPE.ENTERPRISE_AGE,
          tekScriptSort: {
            scriptSortType: 'NUMBER',
            tekScript: {
              idOrCode: INVENTORY_LOG_SORT_SCRIPT.ENTERPRISE_AGE_SORT_SCRIPT,
              params: {},
            },
          },
        };
      case INVENTORY_LOG_TYPES.DAYS_ON_LOT:
        return {
          order: sortData?.order,
          field: INVENTORY_LOG_FIELD_TYPE.DAYS_ON_LOT,
          tekScriptSort: {
            scriptSortType: 'NUMBER',
            tekScript: {
              idOrCode: INVENTORY_LOG_SORT_SCRIPT.DAYS_ON_LOT_SORT_SCRIPT,
              params: {},
            },
          },
        };
      case INVENTORY_LOG_TYPES.DAYS_TO_SELL:
        return {
          order: sortData?.order,
          field: INVENTORY_LOG_FIELD_TYPE.DAYS_TO_SELL,
          tekScriptSort: {
            scriptSortType: 'NUMBER',
            tekScript: {
              idOrCode: INVENTORY_LOG_SORT_SCRIPT.DAYS_TO_SELL_SORT_SCRIPT,
              params: {},
            },
          },
        };

      case INVENTORY_LOG_TYPES.VAT_QUALIFYING:
        return {
          order: sortData?.order,
          field: INVENTORY_LOG_FIELD_TYPE.VAT_QUALIFYING,
          tekScriptSort: {
            scriptSortType: 'NUMBER',
            tekScript: {
              idOrCode: INVENTORY_LOG_SORT_SCRIPT.VAT_QUALIFYING_SORT_SCRIPT,
              params: EMPTY_OBJECT,
            },
          },
        };
      default:
        return sortData;
    }
  });
  return getSortWithCustomPricingPayload(sortPayload);
};

export const getOrderStatusOptions = orderStatus =>
  _map(orderStatus, val => ({ label: VEHICLE_ORDER_STATUS_CONFIG[val]?.label, value: val }));

export const getVehicleTypeLabel = vehicleType => tget(VEHICLE_TYPES_TO_LABEL, vehicleType, EMPTY_STRING);

export const getVehicleTypeLabelsList = vehicleTypes => _map(vehicleTypes, getVehicleTypeLabel);

export const getBodyClassLabel = bodyClass =>
  tget(_find(getBodyClassOptions(), { value: bodyClass }), 'label', EMPTY_STRING);

export const getBodyClassLabelsList = bodyClasses => _map(bodyClasses, getBodyClassLabel);

export const getTradeOwnershipTypeLabel = type => tget(TRADE_OWNERSHIP_TYPES, type, EMPTY_STRING);

export const getCustomSelectOptions = (options, customOption) => {
  const listOfOptions = options || EMPTY_ARRAY;
  if (!_includes(listOfOptions, customOption)) {
    return _compact([customOption, ...listOfOptions]);
  }
  return options;
};

export const shouldShowCustomerSensitiveInfo = ({
  fieldKey,
  isCustomerViewEnabled = false,
  customerViewToggle = false,
  customerViewAssets = EMPTY_ARRAY,
} = EMPTY_OBJECT) => {
  // TODO: Remove once EU PM validates the Toggle Impact
  if (!PROGRAM_CONFIG.shouldShowEnableCustomerViewToggle()) return true;

  const key = tget(CUSTOMER_SENSITIVE_FIELDS, fieldKey, fieldKey);
  if (!_includes(customerViewAssets, key)) return true;

  if (isCustomerViewEnabled) return !customerViewToggle;

  return true;
};

export const isCRMEnabled = () => DealerPropertyHelper.isCRMEnabled();

export const getSourceName = () => {
  if (isRRG()) return __('Origin');
  if (isInchcape()) return __('Purchase Source');
  return __('Source');
};

export const getHardpackMetadataPayload = () => ({
  status: ['ACTIVE'],
  taxCodeTypes: ['SUB'],
});

export const getControlTags = controlTags =>
  _map(controlTags, controlTag => {
    let label = CONTROL_TAG_LABEL[controlTag];

    if (controlTag === CONTROL_NUMBERS.PURCHASE_CATEGORY) label = getSourceName();

    return {
      label,
      value: controlTag,
    };
  });

export const getBodyClassOptions = () => _filter(MARKET_CLASS_CATEGORY_OPTIONS, item => item.shouldShow());

export const getFuelTypeSelectOptions = fuelTypes =>
  _map(fuelTypes, fuelType => {
    const label = FUEL_TYPE_LABELS[fuelType] || fuelType;
    return { label, value: fuelType };
  });

export const getFieldOptionsByName = (fieldOptions, labelConfig) =>
  _map(fieldOptions, fieldOption => {
    const { name } = fieldOption;
    const label = labelConfig[name] || name;
    return { label, value: name };
  });

export const getFuelTypeOptions = fuelTypes => {
  if (isInchcapeOrRRG()) {
    return getFuelTypeSelectOptions(fuelTypes);
  }
  return getSelectOptions(fuelTypes, false);
};

export const getRoJobReader = (roDetails = EMPTY_ARRAY) =>
  _reduce(
    roDetails,
    (acc, data = EMPTY_OBJECT) => {
      const { recallJobDetails = EMPTY_ARRAY, pdiJobDetail = EMPTY_ARRAY, uviJobDetail = EMPTY_ARRAY } = data;
      return {
        ...acc,
        recallJobDetails: [...acc.recallJobDetails, ...recallJobDetails],
        pdiJobDetail: [...acc.pdiJobDetail, ...pdiJobDetail],
        uviJobDetail: [...acc.uviJobDetail, ...uviJobDetail],
      };
    },
    { recallJobDetails: EMPTY_ARRAY, pdiJobDetail: EMPTY_ARRAY, uviJobDetail: EMPTY_ARRAY }
  );

export const getPDIDetails = vehicle => {
  const { vehiclePDI = EMPTY_ARRAY } = vehicle;

  return _reduce(
    vehiclePDI,
    (pdiDetails, pdi) => {
      if (pdi?.selected) {
        pdiDetails.push({
          pdiCode: pdi.opCode,
          opCode: pdi.opCode,
          payType: _get(pdi, 'opcodeDetails.defaultPayType') || PAYTYPE.INTERNAL,
        });
      }
      return pdiDetails;
    },
    []
  );
};

export const getOptionsWithCreatedOption = (options = EMPTY_ARRAY, newOption) =>
  _uniqBy(
    [{ label: isInchcapeOrRRG() ? BODY_TYPE_LABELS[newOption] || newOption : newOption, value: newOption }, ...options],
    'value'
  );

export const shouldLockVehicle = status =>
  _includes(LOCKED_VEHICLE_STATUS, status) && PROGRAM_CONFIG.shouldLockVehicles();

export const getReservedTimerPayload = vehiclesList => {
  const ids = _map(_filter(vehiclesList, { status: VEHICLE_STATUS.RESERVED }), 'vin');
  return {
    queryBy: GENERAL_FIELD_KEYS.VIN,
    ids,
  };
};

export const shouldShowTimer = (vehicleTimerList, vin, status) =>
  status === VEHICLE_STATUS.RESERVED && _some(vehicleTimerList, { vin });

export const getOptionsWithRest = (options, { labelKey, valueKey }) =>
  _map(options, option => ({
    label: tget(option, [labelKey], option),
    value: tget(option, [valueKey], option),
    ...(option || EMPTY_OBJECT),
  }));

export const makeOptionFromValue = labelMap => _map(labelMap, (value, key) => ({ label: value, value: key }));

export const getLeafNodesList = data =>
  _reduce(
    data,
    (acc, value) => {
      const { children } = value || EMPTY_OBJECT;
      if (!_isEmpty(children)) return [...acc, ...children];
      return [...acc, value];
    },
    []
  );

export const getParameterOptions = (currentSelectedOptions, allOptions, isSourceFilterEnabled = true) => {
  if (isInchcape() && isSourceFilterEnabled) {
    return _filter(allOptions, option => option.isEnabledInSetup || _includes(currentSelectedOptions, option.name));
  }
  return allOptions;
};

export const getNonSelectedItems = (allItems, selectedItems, key) =>
  _filter(
    allItems,
    item => !_some(selectedItems, selectedItem => (selectedItem[key] || selectedItem) === (item[key] || item))
  );

export const getPacksConfigurationLabelConfig = () =>
  PROGRAM_CONFIG.shouldShowRevampedHardpackSetup()
    ? PACKS_CONFIGURATION_CONFIG.COST_CONFIGURATION
    : PACKS_CONFIGURATION_CONFIG.HARD_PACK;

export const isVehicleStockTypeRestrictedForPurchaseInvoice = (vehicleType, status, stockTypeHistory) => {
  const { oldStatus } = _head(stockTypeHistory) || EMPTY_OBJECT;
  const { newStatus } = _last(stockTypeHistory) || EMPTY_OBJECT;
  const stockTypeTransitions = [
    { oldStatus: VEHICLE_TYPES.NEW, newStatus: VEHICLE_TYPES.USED },
    { oldStatus: VEHICLE_TYPES.NEW, newStatus: VEHICLE_TYPES.SPECIAL },
  ];

  const isPreviousStockTypeNew = _some(stockTypeTransitions, { oldStatus, newStatus });

  return _includes(
    vehicleType !== VEHICLE_TYPES.NEW && !isPreviousStockTypeNew
      ? RESTRICTED_STATUS_FOR_INVOICE_ADDITION.ALL_RESTRICTED_STATUSES
      : RESTRICTED_STATUS_FOR_INVOICE_ADDITION.COMMON_RESTRICTED_STATUSES,
    status
  );
};

export const getAdditionalErrorMessages = ({ invalidVins, dealer, enterpriseV2Workspaces, enterpriseV2Enabled }) =>
  _reduce(
    invalidVins,
    (acc, vehicle) => {
      const vin = tget(vehicle, 'vin', EMPTY_STRING);
      const dealersPresent = tget(vehicle, 'dealersPresent', EMPTY_ARRAY);
      const dealerNames = _map(dealersPresent, dealerResponse => {
        const dealerId = tget(dealerResponse, 'dealerId', EMPTY_STRING);
        return enterpriseV2Enabled
          ? getWorkspaceNameFromId(dealerId, enterpriseV2Workspaces)
          : getDealerNameFromId(dealerId, dealer);
      });

      const dealerStatus = _join(
        _map(dealersPresent, dealerResponse => {
          const status = tget(dealerResponse, 'status', EMPTY_STRING);
          return VEHICLE_STATUS_TO_LABEL[status] || status;
        }),
        ', '
      );

      const dealerNamesString = _join(
        _filter(dealerNames, name => name !== EMPTY_STRING),
        ', '
      );

      if (dealerNamesString) {
        return {
          ...acc,
          [vin]: {
            message: __('Vehicle already exists in {{dealerStatus}} status in {{dealerNames}}', {
              dealerStatus: dealerStatus,
              dealerNames: dealerNamesString,
            }),
            dealerStatus: dealerStatus,
            dealerNames: dealerNamesString,
          },
        };
      }
      return acc;
    },
    EMPTY_OBJECT
  );

export const getVinPayload = (vehicles, newStatus) => ({
  vinsToValidate: _map(vehicles, ({ vin, status, id }) => ({
    vin,
    currentStatus: status,
    updatedStatus: newStatus,
    vehicleId: id,
  })),
});

export const getAttachmentWithToggledSensitivity = (attachments, mediaFileId) =>
  _map(attachments, attachment => {
    const media = _get(attachment, 'mediaList[0]');
    const shouldToggle = (_get(media, 'mediaFile') ?? _get(media, 'mediaId')) === mediaFileId;
    return {
      ...attachment,
      mediaList: [
        {
          ...media,
          sensitive: shouldToggle ? !_get(media, 'sensitive') : _get(media, 'sensitive'),
        },
      ],
    };
  });

export const getSiteOptionsByDealerId = (enterpriseV2Enabled, workspaces) => {
  if (enterpriseV2Enabled) {
    return _reduce(
      workspaces,
      (siteOptionsByDealerId, workspace = EMPTY_OBJECT) => {
        const { userAccess, workspaceId } = workspace;

        return {
          ...siteOptionsByDealerId,
          [workspaceId]: _reduce(
            userAccess,
            (siteOptions, { name: label, workspaceId: value }) => {
              if (!isMainSite(value)) siteOptions.push({ label, value });
              return siteOptions;
            },
            []
          ),
        };
      },
      {}
    );
  }

  const { dealerOemSites } = TEnvReader.userInfo();
  return _reduce(
    dealerOemSites,
    (siteOptionsByDealerId, siteDetails = EMPTY_OBJECT) => {
      const { oemSites, dealerId } = siteDetails;

      return {
        ...siteOptionsByDealerId,
        [dealerId]: _reduce(
          oemSites,
          (siteOptions, { name: label, siteId: value, mainSite }) => {
            if (!mainSite) siteOptions.push({ label, value });
            return siteOptions;
          },
          []
        ),
      };
    },
    {}
  );
};

export const getSiteNameByDealerAndSiteId = (dealerId, siteId, enterpriseV2Enabled, workspaces) => {
  if (enterpriseV2Enabled) {
    const dealer = _find(workspaces, { workspaceId: dealerId });
    return tget(_find(dealer?.userAccess, { workspaceId: siteId }), 'name', siteId);
  }

  const { dealerOemSites } = TEnvReader.userInfo();
  const dealer = _find(dealerOemSites, { dealerId });
  return tget(_find(dealer?.oemSites, { siteId }), 'name', siteId);
};

export const getFinalOptions = (configuredOptions, options) => {
  if (!SalesDealerPropertyHelper.isVehicleProfileEnabled()) {
    return configuredOptions;
  }

  const manualAddOnOptions = _filter(
    options,
    option =>
      option.source === OPTION_SOURCES.PRE_CONFIGURED &&
      option?.metadata?.manualAddOn &&
      !_some(configuredOptions, { optionCode: option.optionCode })
  );

  return [...configuredOptions, ...manualAddOnOptions];
};

export const getOptionsWithSelectedValue = (options, selectedValue) => {
  const fieldOptions = _every(options, option => !_isEmpty(option.label) && !_isEmpty(option.value))
    ? options
    : _map(options, option => ({ label: option.name || option.label, value: option.name || option.value }));

  if (!selectedValue) return fieldOptions;
  const existsInOptions = _some(fieldOptions, option => option.value === selectedValue);
  if (existsInOptions) return fieldOptions;

  return [{ label: selectedValue, value: selectedValue }, ...fieldOptions];
};
