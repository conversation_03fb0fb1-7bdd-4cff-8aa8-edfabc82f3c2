import * as programChecks from 'tbase/utils/sales/dealerProgram.utils';
import { VEHICLE_TYPES } from 'tbase/constants/vehicleInventory/vehicleTypes';
import { VEHICLE_STATUS } from 'tbase/constants/vehicleInventory/vehicleStatusV2';
import TEnvReader from 'tbase/readers/Env';
import SalesDealerPropertyHelper from 'twidgets/appServices/sales/helpers/salesDealerPropertyHelper';
import { OPTION_SOURCES } from 'twidgets/organisms/vi/ChromeConfigOptions/VehicleOptionsBuilder/VehicleOptionsBuilder.constants';

import { PACKS_CONFIGURATION_CONFIG } from 'constants/config';
import { INVENTORY_LOG_FIELD_TYPE, INVENTORY_LOG_SORT_SCRIPT, INVENTORY_LOG_TYPES } from 'constants/constants';
import PROGRAM_CONFIG from 'constants/programConfig';

import {
  getOptionsWithRest,
  getParameterOptions,
  shouldShowTimer,
  shouldShowCustomerSensitiveInfo,
  getNonSelectedItems,
  getJournalNumber,
  getModifiedSort,
  getPacksConfigurationLabelConfig,
  getSortWithCustomPricingPayload,
  isVehicleStockTypeRestrictedForPurchaseInvoice,
  getSiteOptionsByDealerId,
  getSiteNameByDealerAndSiteId,
  getFinalOptions,
  getOptionsWithSelectedValue,
} from '../common';

const isInchcape = jest.spyOn(programChecks, 'isInchcape');

jest.mock('constants/programConfig', () => ({
  ...jest.requireActual('constants/programConfig'),
  shouldShowEnableCustomerViewToggle: jest.fn(),
  shouldShowRevampedHardpackSetup: jest.fn(),
}));

describe('shouldShowTimer function', () => {
  const VEHICLE_STATUS = {
    RESERVED: 'RESERVED',
  };

  const vehicleTimerList = [
    {
      vin: '3G1TA5AF0CL161457',
    },
  ];

  it('returns false when vehicleTimerList is empty', () => {
    const result = shouldShowTimer([], '3G1TA5AF0CL161457', VEHICLE_STATUS.RESERVED);
    expect(result).toBe(false);
  });

  it('returns false when vin is not found in vehicleTimerList', () => {
    const result = shouldShowTimer(vehicleTimerList, '5H1TB5AF0CL999999', VEHICLE_STATUS.RESERVED);
    expect(result).toBe(false);
  });

  it('returns false when status is not VEHICLE_STATUS.RESERVED', () => {
    const result = shouldShowTimer(vehicleTimerList, '3G1TA5AF0CL161457', 'DRAFT');
    expect(result).toBe(false);
  });

  it('returns true when vin is found in vehicleTimerList and status is VEHICLE_STATUS.RESERVED', () => {
    const result = shouldShowTimer(vehicleTimerList, '3G1TA5AF0CL161457', VEHICLE_STATUS.RESERVED);
    expect(result).toBe(true);
  });
});

describe('getOptionsWithRest', () => {
  const options = [
    { id: 1, name: 'Option 1' },
    { id: 2, name: 'Option 2' },
    { id: 3, name: 'Option 3' },
  ];

  it('returns an empty array when options array is empty', () => {
    expect(getOptionsWithRest([], { labelKey: 'name', valueKey: 'id' })).toEqual([]);
  });

  it('extracts label and value using provided keys', () => {
    const transformedOptions = getOptionsWithRest(options, { labelKey: 'name', valueKey: 'id' });
    expect(transformedOptions).toEqual([
      { label: 'Option 1', value: 1, id: 1, name: 'Option 1' },
      { label: 'Option 2', value: 2, id: 2, name: 'Option 2' },
      { label: 'Option 3', value: 3, id: 3, name: 'Option 3' },
    ]);
  });

  it('preserves additional properties of options', () => {
    const optionsWithAdditionalProps = [
      { id: 1, name: 'Option 1', extra: 'additional' },
      { id: 2, name: 'Option 2', extra: 'additional' },
    ];
    const transformedOptions = getOptionsWithRest(optionsWithAdditionalProps, { labelKey: 'name', valueKey: 'id' });
    expect(transformedOptions).toEqual([
      { label: 'Option 1', value: 1, id: 1, name: 'Option 1', extra: 'additional' },
      { label: 'Option 2', value: 2, id: 2, name: 'Option 2', extra: 'additional' },
    ]);
  });
});

describe('Test getParameterOptions', () => {
  let isInchcape;
  const currentSelectedOptions = ['PART_EXCHANGE', 'MOTABILITY'];
  const allOptions = [
    {
      name: 'PART_EXCHANGE',
      label: 'Part Exchange',
      value: 'PART_EXCHANGE',
      isEnabledInSetup: true,
    },
    {
      name: 'MOTABILITY',
      label: 'Motability',
      value: 'MOTABILITY',
      isEnabledInSetup: false,
    },
    {
      name: 'FLEET',
      label: 'Fleet',
      value: 'FLEET',
      isEnabledInSetup: true,
    },
    {
      name: 'BUY_BACK',
      label: 'Buy Back',
      value: 'BUY_BACK',
      isEnabledInSetup: true,
    },
    {
      name: 'OEM',
      label: 'OEM',
      value: 'OEM',
      isEnabledInSetup: true,
    },
    {
      name: 'CUSTOM_SOURCE_1',
      label: 'Custom Sourcee',
      value: 'CUSTOM_SOURCE_1',
      isEnabledInSetup: false,
    },
    {
      name: 'CUSTOM_SOURCE_2',
      label: 'Checkinggg',
      value: 'CUSTOM_SOURCE_2',
      isEnabledInSetup: true,
    },
    {
      name: 'CUSTOM_SOURCE_3',
      label: 'Custom Source 3',
      value: 'CUSTOM_SOURCE_3',
      isEnabledInSetup: false,
    },
    {
      name: 'CUSTOM_SOURCE_4',
      label: 'Custom Source 4',
      value: 'CUSTOM_SOURCE_4',
      isEnabledInSetup: false,
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    isInchcape = jest.spyOn(programChecks, 'isInchcape');
  });

  it('should filter options based on isEnabledInSetup and currentSelectedOptions when isInchcape is true and isSourceFilterEnabled is true', () => {
    isInchcape.mockReturnValue(true);

    const result = getParameterOptions(currentSelectedOptions, allOptions, true);

    expect(result).toEqual([
      {
        name: 'PART_EXCHANGE',
        label: 'Part Exchange',
        value: 'PART_EXCHANGE',
        isEnabledInSetup: true,
      },
      {
        name: 'MOTABILITY',
        label: 'Motability',
        value: 'MOTABILITY',
        isEnabledInSetup: false,
      },
      {
        name: 'FLEET',
        label: 'Fleet',
        value: 'FLEET',
        isEnabledInSetup: true,
      },
      {
        name: 'BUY_BACK',
        label: 'Buy Back',
        value: 'BUY_BACK',
        isEnabledInSetup: true,
      },
      { name: 'OEM', label: 'OEM', value: 'OEM', isEnabledInSetup: true },
      {
        name: 'CUSTOM_SOURCE_2',
        label: 'Checkinggg',
        value: 'CUSTOM_SOURCE_2',
        isEnabledInSetup: true,
      },
    ]);
  });

  it('should return all options when isInchcape is false, regardless of isSourceFilterEnabled', () => {
    isInchcape.mockReturnValue(false);

    const result = getParameterOptions(currentSelectedOptions, allOptions, true);

    expect(result).toEqual(allOptions);
  });

  it('should return all options when isSourceFilterEnabled is false even if isInchcape is true', () => {
    isInchcape.mockReturnValue(true);

    const result = getParameterOptions(currentSelectedOptions, allOptions, false);

    expect(result).toEqual(allOptions);
  });

  it('should return all options when isInchcape is false and isSourceFilterEnabled is false', () => {
    isInchcape.mockReturnValue(false);

    const result = getParameterOptions(currentSelectedOptions, allOptions, false);

    expect(result).toEqual(allOptions);
  });
});

describe('Test shouldShowCustomerSensitiveInfo helper', () => {
  const mockCustomerViewProps = {
    isCustomerViewEnabled: false,
    customerViewAssets: ['AGE', 'INVOICE_PRICE', 'ADDITIONAL_BODY', 'HARD_PACK', 'INVOICE_DATE', 'OPTIONS'],
    customerViewToggle: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return true if shouldShowEnableCustomerViewToggle programConfig is false', () => {
    PROGRAM_CONFIG.shouldShowEnableCustomerViewToggle.mockReturnValue(false);

    const result = shouldShowCustomerSensitiveInfo({ fieldKey: 'OPTIONS', ...mockCustomerViewProps });

    expect(result).toBe(true);
    expect(PROGRAM_CONFIG.shouldShowEnableCustomerViewToggle).toHaveBeenCalled();
  });

  it('should return true if fieldKey is not in customerViewAssets', () => {
    PROGRAM_CONFIG.shouldShowEnableCustomerViewToggle.mockReturnValue(true);

    const result = shouldShowCustomerSensitiveInfo({ fieldKey: 'NON_CUSTOMER_VIEW_FIELD', ...mockCustomerViewProps });

    expect(result).toBe(true);
    expect(PROGRAM_CONFIG.shouldShowEnableCustomerViewToggle).toHaveBeenCalled();
  });

  it('should return opposite of customerViewToggle if isCustomerViewEnabled is true and fieldKey is in customerViewAssets', () => {
    PROGRAM_CONFIG.shouldShowEnableCustomerViewToggle.mockReturnValue(true);

    const result = shouldShowCustomerSensitiveInfo({
      fieldKey: 'OPTIONS',
      ...mockCustomerViewProps,
      isCustomerViewEnabled: true,
      customerViewToggle: true,
    });

    expect(result).toBe(false);
    expect(PROGRAM_CONFIG.shouldShowEnableCustomerViewToggle).toHaveBeenCalled();
  });

  it('should return true if isCustomerViewEnabled is false and fieldKey is in customerViewAssets', () => {
    PROGRAM_CONFIG.shouldShowEnableCustomerViewToggle.mockReturnValue(true);

    const result = shouldShowCustomerSensitiveInfo({ fieldKey: 'OPTIONS', ...mockCustomerViewProps });

    expect(result).toBe(true);
    expect(PROGRAM_CONFIG.shouldShowEnableCustomerViewToggle).toHaveBeenCalled();
  });

  it('should use default values when no arguments are provided', () => {
    PROGRAM_CONFIG.shouldShowEnableCustomerViewToggle.mockReturnValue(true);

    const result = shouldShowCustomerSensitiveInfo();

    expect(result).toBe(true);
    expect(PROGRAM_CONFIG.shouldShowEnableCustomerViewToggle).toHaveBeenCalled();
  });
});

describe('getNonSelectedItems', () => {
  it('should return items that are not selected', () => {
    const allItems = [
      { id: 1, name: 'Item 1' },
      { id: 2, name: 'Item 2' },
      { id: 3, name: 'Item 3' },
    ];

    const selectedItems = [
      { id: 1, name: 'Item 1' },
      { id: 3, name: 'Item 3' },
    ];

    const key = 'id';

    const result = getNonSelectedItems(allItems, selectedItems, key);

    expect(result).toEqual([{ id: 2, name: 'Item 2' }]);
  });

  it('should return all items if none are selected', () => {
    const allItems = [
      { id: 1, name: 'Item 1' },
      { id: 2, name: 'Item 2' },
      { id: 3, name: 'Item 3' },
    ];

    const selectedItems = [];

    const key = 'id';

    const result = getNonSelectedItems(allItems, selectedItems, key);

    expect(result).toEqual(allItems);
  });

  it('should return an empty array if all items are selected', () => {
    const allItems = [
      { id: 1, name: 'Item 1' },
      { id: 2, name: 'Item 2' },
      { id: 3, name: 'Item 3' },
    ];

    const selectedItems = [
      { id: 1, name: 'Item 1' },
      { id: 2, name: 'Item 2' },
      { id: 3, name: 'Item 3' },
    ];

    const key = 'id';

    const result = getNonSelectedItems(allItems, selectedItems, key);

    expect(result).toEqual([]);
  });

  it('should handle selected items without the key field', () => {
    const allItems = [
      { id: 1, name: 'Item 1' },
      { id: 2, name: 'Item 2' },
      { id: 3, name: 'Item 3' },
    ];

    const selectedItems = [{ name: 'Item 1' }]; // no 'id' field

    const key = 'id';

    const result = getNonSelectedItems(allItems, selectedItems, key);

    expect(result).toEqual([
      { id: 1, name: 'Item 1' },
      { id: 2, name: 'Item 2' },
      { id: 3, name: 'Item 3' },
    ]);
  });

  it('should handle allItems with objects and selectedItems with primitive values', () => {
    const allItems = [{ name: 1 }, { name: 2 }, { name: 3 }];
    const selectedItems = [2];

    const key = 'name';

    const result = getNonSelectedItems(allItems, selectedItems, key);

    expect(result).toEqual([{ name: 1 }, { name: 3 }]);
  });

  it('should handle both allItems and selectedItems with primitive values', () => {
    const allItems = [1, 2, 3];
    const selectedItems = [2];

    const key = undefined;

    const result = getNonSelectedItems(allItems, selectedItems, key);

    expect(result).toEqual([1, 3]);
  });
});

describe('Test getJournalNumber', () => {
  const journalAccounts = {
    10: {
      id: '6351_10',
      journalNumber: '10',
      journalName: 'Derby-Payment',
      // ... other properties ...
    },
    11: {
      id: '6351_11',
      journalNumber: '11',
      journalName: 'Another Journal',
      // ... other properties ...
    },
  };

  it('should return formatted journal number and name for a valid journalId', () => {
    const result = getJournalNumber('6351_10', journalAccounts);
    expect(result).toBe('10 - Derby-Payment');
  });

  it('should return formatted journal number and name for another valid journalId', () => {
    const result = getJournalNumber('6351_11', journalAccounts);
    expect(result).toBe('11 - Another Journal');
  });

  it('should handle edge scenarios', () => {
    const result1 = getJournalNumber('invalid_id', journalAccounts);
    expect(result1).toBe(' - ');

    const result2 = getJournalNumber('6351_10', {});
    expect(result2).toBe(' - ');

    const result3 = getJournalNumber('6351_10');
    expect(result3).toBe(' - ');
  });
});

describe('Test getModifiedSort', () => {
  it('should return modified sort payload for VEHICLE_AGE', () => {
    const payload = [{ key: INVENTORY_LOG_TYPES.VEHICLE_AGE, order: 'ASC' }];
    const result = getModifiedSort(payload);
    expect(result).toEqual([
      {
        order: 'ASC',
        tekScriptSort: {
          scriptSortType: 'NUMBER',
          tekScript: {
            idOrCode: INVENTORY_LOG_SORT_SCRIPT.VEHICLE_AGE_SORT_SCRIPT,
            params: {},
          },
        },
      },
    ]);
  });

  it('should return modified sort payload for ENTERPRISE_AGE', () => {
    const payload = [{ key: INVENTORY_LOG_TYPES.ENTERPRISE_AGE, order: 'DESC' }];
    const result = getModifiedSort(payload);
    expect(result).toEqual([
      {
        order: 'DESC',
        field: INVENTORY_LOG_FIELD_TYPE.ENTERPRISE_AGE,
        tekScriptSort: {
          scriptSortType: 'NUMBER',
          tekScript: {
            idOrCode: INVENTORY_LOG_SORT_SCRIPT.ENTERPRISE_AGE_SORT_SCRIPT,
            params: {},
          },
        },
      },
    ]);
  });

  it('should return modified sort payload for DAYS_ON_LOT', () => {
    const payload = [{ key: INVENTORY_LOG_TYPES.DAYS_ON_LOT, order: 'ASC' }];
    const result = getModifiedSort(payload);
    expect(result).toEqual([
      {
        order: 'ASC',
        field: INVENTORY_LOG_FIELD_TYPE.DAYS_ON_LOT,
        tekScriptSort: {
          scriptSortType: 'NUMBER',
          tekScript: {
            idOrCode: INVENTORY_LOG_SORT_SCRIPT.DAYS_ON_LOT_SORT_SCRIPT,
            params: {},
          },
        },
      },
    ]);
  });

  it('should return modified sort payload for DAYS_TO_SELL', () => {
    const payload = [{ key: INVENTORY_LOG_TYPES.DAYS_TO_SELL, order: 'DESC' }];
    const result = getModifiedSort(payload);
    expect(result).toEqual([
      {
        order: 'DESC',
        field: INVENTORY_LOG_FIELD_TYPE.DAYS_TO_SELL,
        tekScriptSort: {
          scriptSortType: 'NUMBER',
          tekScript: {
            idOrCode: INVENTORY_LOG_SORT_SCRIPT.DAYS_TO_SELL_SORT_SCRIPT,
            params: {},
          },
        },
      },
    ]);
  });

  it('should return modified sort payload for VAT_QUALIFYING', () => {
    const payload = [{ key: INVENTORY_LOG_TYPES.VAT_QUALIFYING, order: 'ASC' }];
    const result = getModifiedSort(payload);
    expect(result).toEqual([
      {
        order: 'ASC',
        field: INVENTORY_LOG_FIELD_TYPE.VAT_QUALIFYING,
        tekScriptSort: {
          scriptSortType: 'NUMBER',
          tekScript: {
            idOrCode: INVENTORY_LOG_SORT_SCRIPT.VAT_QUALIFYING_SORT_SCRIPT,
            params: {},
          },
        },
      },
    ]);
  });

  it('should return original sort data for other keys', () => {
    const payload = [{ key: 'VIN', order: 'ASC' }];
    const result = getModifiedSort(payload);
    expect(result).toEqual(payload);
  });

  it('should handle multiple sort keys', () => {
    const payload = [
      { key: INVENTORY_LOG_TYPES.VEHICLE_AGE, order: 'ASC' },
      { key: INVENTORY_LOG_TYPES.DAYS_ON_LOT, order: 'DESC' },
    ];
    const result = getModifiedSort(payload);
    expect(result).toEqual([
      {
        order: 'ASC',
        tekScriptSort: {
          scriptSortType: 'NUMBER',
          tekScript: {
            idOrCode: INVENTORY_LOG_SORT_SCRIPT.VEHICLE_AGE_SORT_SCRIPT,
            params: {},
          },
        },
      },
      {
        order: 'DESC',
        field: INVENTORY_LOG_FIELD_TYPE.DAYS_ON_LOT,
        tekScriptSort: {
          scriptSortType: 'NUMBER',
          tekScript: {
            idOrCode: INVENTORY_LOG_SORT_SCRIPT.DAYS_ON_LOT_SORT_SCRIPT,
            params: {},
          },
        },
      },
    ]);
  });
});

describe('Test getPacksConfigurationLabelConfig', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return COST_CONFIGURATION when shouldShowRevampedHardpackSetup is true', () => {
    PROGRAM_CONFIG.shouldShowRevampedHardpackSetup.mockReturnValue(true);
    const result = getPacksConfigurationLabelConfig();
    expect(result).toBe(PACKS_CONFIGURATION_CONFIG.COST_CONFIGURATION);
  });

  it('should return HARD_PACK when shouldShowRevampedHardpackSetup is false', () => {
    PROGRAM_CONFIG.shouldShowRevampedHardpackSetup.mockReturnValue(false);
    const result = getPacksConfigurationLabelConfig();
    expect(result).toBe(PACKS_CONFIGURATION_CONFIG.HARD_PACK);
  });
});

describe('Test getSortWithCustomPricingPayload', () => {
  it('should return the original sort when field is not customPricingFields', () => {
    const sortPayload = [{ field: 'pricingDetails.retailPrice', key: 'retailPrice', order: 'ASC' }];
    const result = getSortWithCustomPricingPayload(sortPayload);
    expect(result).toEqual(sortPayload);
  });

  it('should modify the sort for customPricingFields', () => {
    const sortPayload = [
      { field: 'pricingDetails.retailPrice', key: 'retailPrice', order: 'ASC' },
      {
        field: 'customPricingFields.PRICING_CUSTOM_FIELD_1_AUTOMOBILE.value',
        key: 'PRICING_CUSTOM_FIELD_1_AUTOMOBILE',
        order: 'ASC',
      },
    ];
    const expectedResult = [
      { field: 'pricingDetails.retailPrice', key: 'retailPrice', order: 'ASC' },
      {
        key: 'PRICING_CUSTOM_FIELD_1_AUTOMOBILE',
        order: 'ASC',
        nestedPath: 'customPricingFields',
        field: 'customPricingFields.value',
        nestedFieldSort: true,
        filtersForNestedSort: [
          { field: 'customPricingFields.fieldName', values: ['PRICING_CUSTOM_FIELD_1_AUTOMOBILE'] },
        ],
      },
    ];
    const result = getSortWithCustomPricingPayload(sortPayload);
    expect(result).toEqual(expectedResult);
  });

  it('should return an empty array when sortPayload is empty', () => {
    const result = getSortWithCustomPricingPayload([]);
    expect(result).toEqual([]);
  });
});

describe('Test isVehicleStockTypeRestrictedForPurchaseInvoice', () => {
  it('should return true if the status is restricted for a vehicle type not NEW and no valid stock type transition', () => {
    const vehicleType = VEHICLE_TYPES.USED;
    const status = VEHICLE_STATUS.INVOICED;
    const stockTypeHistory = [{ oldStatus: 'USED', newStatus: 'SPECIAL' }];

    const result = isVehicleStockTypeRestrictedForPurchaseInvoice(vehicleType, status, stockTypeHistory);

    expect(result).toBe(true);
  });

  it('should return false if the vehicle type is NEW and the status is not restricted', () => {
    const vehicleType = VEHICLE_TYPES.NEW;
    const status = VEHICLE_STATUS.STOCKED_IN;
    const stockTypeHistory = [{ oldStatus: 'NEW', newStatus: 'SPECIAL' }];

    const result = isVehicleStockTypeRestrictedForPurchaseInvoice(vehicleType, status, stockTypeHistory);

    expect(result).toBe(false);
  });

  it('should return true if the stock type transitioned from NEW to USED and status is restricted', () => {
    const vehicleType = VEHICLE_TYPES.USED;
    const status = VEHICLE_STATUS.VOID;
    const stockTypeHistory = [{ oldStatus: 'NEW', newStatus: 'USED' }];

    const result = isVehicleStockTypeRestrictedForPurchaseInvoice(vehicleType, status, stockTypeHistory);

    expect(result).toBe(true);
  });

  it('should return false if there is no stockTypeHistory and the vehicle type is not NEW', () => {
    const vehicleType = VEHICLE_TYPES.USED;
    const status = VEHICLE_STATUS.DRAFT;
    const stockTypeHistory = [];

    const result = isVehicleStockTypeRestrictedForPurchaseInvoice(vehicleType, status, stockTypeHistory);

    expect(result).toBe(false);
  });
});

describe('Test getSiteOptionsByDealerId', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return site options grouped by dealerId when enterpriseV2Enabled is true', () => {
    const workspaces = [
      {
        workspaceId: 'dealer1',
        userAccess: [
          { name: 'Site A', workspaceId: 'siteA' },
          { name: 'Main Site', workspaceId: '-1_mainSite' },
        ],
      },
      {
        workspaceId: 'dealer2',
        userAccess: [
          { name: 'Site B', workspaceId: 'siteB' },
          { name: 'Site C', workspaceId: 'siteC' },
        ],
      },
    ];

    const result = getSiteOptionsByDealerId(true, workspaces);

    expect(result).toEqual({
      dealer1: [{ label: 'Site A', value: 'siteA' }],
      dealer2: [
        { label: 'Site B', value: 'siteB' },
        { label: 'Site C', value: 'siteC' },
      ],
    });
  });

  it('should return site options grouped by dealerId when enterpriseV2Enabled is false', () => {
    jest.spyOn(TEnvReader, 'userInfo').mockImplementation(() => ({
      dealerOemSites: [
        {
          dealerId: 'dealer1',
          oemSites: [
            { name: 'OEM Site A', siteId: 'oemSiteA', mainSite: false },
            { name: 'OEM Main Site', siteId: 'mainSite', mainSite: true },
          ],
        },
        {
          dealerId: 'dealer2',
          oemSites: [
            { name: 'OEM Site B', siteId: 'oemSiteB', mainSite: false },
            { name: 'OEM Site C', siteId: 'oemSiteC', mainSite: false },
          ],
        },
      ],
    }));

    const result = getSiteOptionsByDealerId(false);

    expect(result).toEqual({
      dealer1: [{ label: 'OEM Site A', value: 'oemSiteA' }], // Excludes mainSite
      dealer2: [
        { label: 'OEM Site B', value: 'oemSiteB' },
        { label: 'OEM Site C', value: 'oemSiteC' },
      ],
    });
  });

  it('should return an empty object when there are no workspaces and enterpriseV2Enabled is true', () => {
    const result = getSiteOptionsByDealerId(true, []);
    expect(result).toEqual({});
  });

  it('should return an empty object when there are no dealerOemSites and enterpriseV2Enabled is false', () => {
    jest.spyOn(TEnvReader, 'userInfo').mockImplementation(() => ({
      dealerOemSites: [],
    }));

    const result = getSiteOptionsByDealerId(false);
    expect(result).toEqual({});
  });

  it('should return an empty object when all sites are main sites', () => {
    const workspaces = [
      {
        workspaceId: 'dealer1',
        userAccess: [{ name: 'Main Site', workspaceId: '-1_mainSite' }],
      },
    ];

    const result = getSiteOptionsByDealerId(true, workspaces);

    expect(result).toEqual({ dealer1: [] });
  });

  it('should return an empty object when dealerOemSites contain only main sites', () => {
    jest.spyOn(TEnvReader, 'userInfo').mockImplementation(() => ({
      dealerOemSites: [
        {
          dealerId: 'dealer1',
          oemSites: [{ name: 'OEM Main Site', siteId: 'mainSite', mainSite: true }],
        },
      ],
    }));

    const result = getSiteOptionsByDealerId(false);

    expect(result).toEqual({ dealer1: [] });
  });
});

describe('Test getSiteNameByDealerAndSiteId', () => {
  const workspaces = [
    {
      workspaceId: 'dealer1',
      userAccess: [
        { workspaceId: 'site1', name: 'Site One' },
        { workspaceId: 'site2', name: 'Site Two' },
      ],
    },
  ];

  const dealerOemSites = [
    {
      dealerId: 'dealer1',
      oemSites: [
        { siteId: 'site1', name: 'OEM Site One' },
        { siteId: 'site2', name: 'OEM Site Two' },
      ],
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return the site name when enterpriseV2Enabled is true', () => {
    const result = getSiteNameByDealerAndSiteId('dealer1', 'site1', true, workspaces);
    expect(result).toBe('Site One');
  });

  it('should return siteId when site is not found in workspaces', () => {
    const result = getSiteNameByDealerAndSiteId('dealer1', 'site3', true, workspaces);
    expect(result).toBe('site3');
  });

  it('should return the site name when enterpriseV2Enabled is false', () => {
    jest.spyOn(TEnvReader, 'userInfo').mockImplementation(() => ({
      dealerOemSites,
    }));

    const result = getSiteNameByDealerAndSiteId('dealer1', 'site2', false, []);
    expect(result).toBe('OEM Site Two');
  });

  it('should return siteId when site is not found in dealerOemSites', () => {
    jest.spyOn(TEnvReader, 'userInfo').mockImplementation(() => ({
      dealerOemSites,
    }));

    const result = getSiteNameByDealerAndSiteId('dealer1', 'site3', false, []);
    expect(result).toBe('site3');
  });

  it('should return siteId if dealer is not found', () => {
    jest.spyOn(TEnvReader, 'userInfo').mockImplementation(() => ({
      dealerOemSites,
    }));

    const result = getSiteNameByDealerAndSiteId('dealer2', 'site1', false, []);
    expect(result).toBe('site1');
  });
});

describe('Test getFinalOptions', () => {
  let isVehicleProfileEnabledSpy;
  beforeEach(() => {
    isVehicleProfileEnabledSpy = jest.spyOn(SalesDealerPropertyHelper, 'isVehicleProfileEnabled');
  });

  it('returns configuredOptions if vehicle profile is not enabled', () => {
    isVehicleProfileEnabledSpy.mockReturnValue(false);

    const configuredOptions = [{ optionCode: 'A1' }];
    const options = [{ optionCode: 'B2', source: OPTION_SOURCES.PRE_CONFIGURED, metadata: { manualAddOn: true } }];

    const result = getFinalOptions(configuredOptions, options);
    expect(result).toEqual(configuredOptions);
  });

  it('adds manualAddOn options not already in configuredOptions', () => {
    isVehicleProfileEnabledSpy.mockReturnValue(true);

    const configuredOptions = [{ optionCode: 'A1' }];
    const options = [
      { optionCode: 'B2', source: OPTION_SOURCES.PRE_CONFIGURED, metadata: { manualAddOn: true } },
      { optionCode: 'C3', source: OPTION_SOURCES.PRE_CONFIGURED, metadata: { manualAddOn: false } },
      { optionCode: 'A1', source: OPTION_SOURCES.PRE_CONFIGURED, metadata: { manualAddOn: true } },
      { optionCode: 'D4', source: 'OTHER_SOURCE', metadata: { manualAddOn: true } },
    ];

    const result = getFinalOptions(configuredOptions, options);

    expect(result).toEqual([
      { optionCode: 'A1' },
      { optionCode: 'B2', source: OPTION_SOURCES.PRE_CONFIGURED, metadata: { manualAddOn: true } },
    ]);
  });

  it('returns original configuredOptions if no valid manual add-ons exist', () => {
    isVehicleProfileEnabledSpy.mockReturnValue(true);

    const configuredOptions = [{ optionCode: 'A1' }];
    const options = [
      { optionCode: 'B2', source: 'OTHER_SOURCE', metadata: { manualAddOn: true } },
      { optionCode: 'C3', source: OPTION_SOURCES.PRE_CONFIGURED, metadata: { manualAddOn: false } },
    ];

    const result = getFinalOptions(configuredOptions, options);
    expect(result).toEqual(configuredOptions);
  });

  it('handles options with missing metadata', () => {
    isVehicleProfileEnabledSpy.mockReturnValue(true);

    const configuredOptions = [];
    const options = [
      { optionCode: 'B2', source: OPTION_SOURCES.PRE_CONFIGURED },
      { optionCode: 'C3', source: OPTION_SOURCES.PRE_CONFIGURED, metadata: {} },
    ];

    const result = getFinalOptions(configuredOptions, options);
    expect(result).toEqual([]);
  });
});

describe('Test getOptionsWithSelectedValue', () => {
  it('should return original options when selectedValue is empty', () => {
    const options = [
      { label: 'Option 1', value: '1' },
      { label: 'Option 2', value: '2' },
    ];
    const result = getOptionsWithSelectedValue(options, '');
    expect(result).toEqual(options);
  });

  it('should return original options when selectedValue is null', () => {
    const options = [
      { label: 'Option 1', value: '1' },
      { label: 'Option 2', value: '2' },
    ];
    const result = getOptionsWithSelectedValue(options, null);
    expect(result).toEqual(options);
  });

  it('should return original options when selectedValue is undefined', () => {
    const options = [
      { label: 'Option 1', value: '1' },
      { label: 'Option 2', value: '2' },
    ];
    const result = getOptionsWithSelectedValue(options, undefined);
    expect(result).toEqual(options);
  });

  it('should prepend selectedValue to options when it does not exist', () => {
    const options = [
      { label: 'Option 1', value: '1' },
      { label: 'Option 2', value: '2' },
    ];
    const result = getOptionsWithSelectedValue(options, '3');
    expect(result).toEqual([
      { label: '3', value: '3' },
      { label: 'Option 1', value: '1' },
      { label: 'Option 2', value: '2' },
    ]);
  });

  it('should not append selectedValue when it already exists in options', () => {
    const options = [
      { label: 'Option 1', value: '1' },
      { label: 'Option 2', value: '2' },
    ];
    const result = getOptionsWithSelectedValue(options, '2');
    expect(result).toEqual(options);
  });
});
