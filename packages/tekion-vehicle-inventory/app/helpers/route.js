import { EMPTY_OBJECT } from 'tbase/app.constants';
import { SERVICES, PARTS, ACCOUNTING, SALES, CORE } from 'tbase/constants/appServices';
import { JOURNAL_ENTRIES } from 'tbase/constants/appConfigs/accountingAppConfigs';
import { CORE_CUSTOMER_MANAGEMENT, CORE_VENDOR_MANAGEMENT } from 'tbase/constants/appConfigs/coreAppConfigs';
import { DEALS } from 'tbase/constants/appConfigs/salesAppConfigs';
import { tget } from 'tbase/utils/general';
import openWindowInNewTab from 'tbase/utils/openWindowInNewTab';
import getRoute from 'tbusiness/factories/route';
import {
  redirectToPoliceRecord,
  redirectToVehicleInventoryList,
  redirectToVehiclePriceEditor,
  redirectToVehicleSyndicationList,
} from 'tbusiness/appServices/vehicleInventory/helpers/routes';
import MODES from 'tbusiness/constants/routeModes';
import { CUSTOMER_MANAGEMENT_SUBTYPES } from 'tbusiness/appServices/core/factories/routes/customerManagement.routes';
import PART_ENTITY_TYPES from 'tbusiness/appServices/parts/constants/partEntitytypes';

export const getRORoute = id =>
  getRoute(SERVICES, 'repair_order', {
    mode: MODES.EDIT,
    assetId: id,
    isExternal: true,
  });

export const getSubletPurchaseOrderRoute = id =>
  getRoute(PARTS, PART_ENTITY_TYPES.PURCHASE_ORDER, {
    mode: MODES.VIEW,
    entityDetails: {
      id,
      orderType: 'SUBLET',
    },
    isExternal: true,
  });

export const getJournalEntriesRoute = (transactionId, dealerId) =>
  getRoute(ACCOUNTING, JOURNAL_ENTRIES.getKey(), {
    mode: MODES.VIEW,
    transactionId,
    isExternal: true,
    dealerId,
  });

export const getDealRoute = dealNumber =>
  getRoute(SALES, DEALS.getKey(), {
    mode: MODES.VIEW,
    dealNumber,
    isExternal: true,
  });

export const redirectToDealRoute = dealNumber => {
  const route = getDealRoute(dealNumber);
  openWindowInNewTab(route);
};

export const getCreateDealRoute = () =>
  getRoute(SALES, DEALS.getKey(), {
    mode: MODES.CREATE,
    isExternal: true,
  });

export const getViewVendorRoute = vendorId =>
  getRoute(CORE, CORE_VENDOR_MANAGEMENT.getKey(), {
    mode: MODES.VIEW,
    vendorId,
    isExternal: true,
  });

export const getCustomerDetailedViewRoute = customerId =>
  getRoute(CORE, CORE_CUSTOMER_MANAGEMENT.getKey(), {
    mode: MODES.VIEW,
    entityType: CUSTOMER_MANAGEMENT_SUBTYPES.CUSTOMERS,
    entityId: customerId,
    isExternal: true,
  });

export const redirectToCustomerDetailedViewRoute = customerId => {
  const route = getCustomerDetailedViewRoute(customerId);
  openWindowInNewTab(route);
};

export const handleVehicleDetailsBackNavigation = ({ navigate, location }) => {
  const historyState = tget(location, 'state', EMPTY_OBJECT);
  const {
    redirectedFromPoliceRecord = false,
    redirectedFromPriceEditor = false,
    redirectedFromSyndicationList = false,
  } = historyState;
  if (redirectedFromPoliceRecord) {
    redirectToPoliceRecord({
      navigate,
    });
    return;
  }
  if (redirectedFromPriceEditor) {
    redirectToVehiclePriceEditor({
      navigate,
    });
    return;
  }
  if (redirectedFromSyndicationList) {
    redirectToVehicleSyndicationList({
      navigate,
    });
    return;
  }
  redirectToVehicleInventoryList({
    navigate,
  });
};
