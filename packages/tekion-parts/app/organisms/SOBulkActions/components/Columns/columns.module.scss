@import 'tstyles/component.scss';


.tableCheckBoxHeaderCell {
    :global(#headerCellContainer) {
      width: auto;
      height: auto;
      &:focus-within {
        @include focus-outline-base;
        z-index: 1;
      }
      justify-content: center;
    }
    /* fix for the SO v3 invoice tab, print invoice modal
       checkbox header alignment issue,  Table v2 separator class by default adding width is 0.8rem.
       But the checkbox col width is very small so the checkbox element is moving left side.
  
       solution: overriding the separator with to fit-content
       jira => https://tekion.atlassian.net/browse/DMS-110045
    */
    div:last-child {
      width: fit-content;
    }
  }
  
  .tableCheckboxRowCell {
    padding: 0rem;
  }

  .checkBoxCell {
    @include flex-center;
    @include full-width;
    @include full-height;
  }