import { useReducer } from 'react';

import _filter from 'lodash/filter';
import _get from 'lodash/get';
import _head from 'lodash/head';
import _includes from 'lodash/includes';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';
import _keys from 'lodash/keys';
import _map from 'lodash/map';
import _orderBy from 'lodash/orderBy';
import _size from 'lodash/size';
import _toLower from 'lodash/toLower';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';

import { TABLE_ACTION_TYPES } from '@tekion/tekion-widgets/src/organisms/tableManager';

import { BULK_ACTION_COLUMN_KEYS } from 'organisms/SOBulkActions/constants/bulkActionColumns.constants';

import { GENERATE_INVOICE_ACTION, INVOICE_GENERATION_STATUS } from '../generateInvoice.constants';
import {
  getSectionVisibilityKey,
  getAllIdsByGenerationStatus,
  getHeaderIdByGenerationStatus,
  getCheckboxIds,
  getValuesWithHeadingRows,
} from '../generateInvoice.helpers';

const initialState = {
  searchQuery: EMPTY_STRING,
  selectedCheckboxIds: EMPTY_ARRAY,
  sortDetails: EMPTY_OBJECT,
  values: EMPTY_ARRAY,
  isInvoiceToBeGeneratedSectionVisible: true,
  isInvoiceAlreadyGeneratedSectionVisible: true,
  isInvoiceCannotBeGeneratedSectionVisible: true,
  isAllToBeGeneratedRowsChecked: false,
};

const setCheckboxActionHandler = (state, action) => {
  const { values } = state;
  const { selectedRowIds = EMPTY_ARRAY, isAllToBeGeneratedRowsChecked = false } = _get(action, 'payload', EMPTY_OBJECT);

  if (_size(values) === _size(selectedRowIds) || _size(selectedRowIds) === 0) {
    return {
      ...state,
      selectedCheckboxIds: selectedRowIds,
    };
  }

  const toBeGeneratedIds = getAllIdsByGenerationStatus(values, INVOICE_GENERATION_STATUS.TO_BE_GENERATED);
  const toBeGeneratedHeaderId = getHeaderIdByGenerationStatus(values, INVOICE_GENERATION_STATUS.TO_BE_GENERATED);

  const selectedToBeGeneratedIds = _filter(selectedRowIds, id =>
    _includes([...toBeGeneratedIds, toBeGeneratedHeaderId], id),
  );

  const { isAllRowsChecked: newToBeGeneratedIsAllRowsChecked, selectedCheckboxIds: newToBeGeneratedSelectedIds } =
    getCheckboxIds({
      lastIsAllRowsChecked: isAllToBeGeneratedRowsChecked,
      selectedCheckboxIds: selectedToBeGeneratedIds,
      headerCheckboxId: toBeGeneratedHeaderId,
      totalCheckboxIds: [...toBeGeneratedIds, toBeGeneratedHeaderId],
    });

  return {
    ...state,
    selectedCheckboxIds: newToBeGeneratedSelectedIds,
    isAllToBeGeneratedRowsChecked: newToBeGeneratedIsAllRowsChecked,
  };
};

const generateInvoiceReducer = (state, action) => {
  const { type, payload } = action;

  switch (type) {
    case GENERATE_INVOICE_ACTION.SET_SELECTED_ROWS:
      return setCheckboxActionHandler(state, action);

    case GENERATE_INVOICE_ACTION.SET_TABLE_DATA: {
      const values = _get(payload, 'values', EMPTY_ARRAY);

      // Auto-select all "to be generated" invoices by default
      const toBeGeneratedIds = getAllIdsByGenerationStatus(values, INVOICE_GENERATION_STATUS.TO_BE_GENERATED);
      const toBeGeneratedHeaderId = getHeaderIdByGenerationStatus(values, INVOICE_GENERATION_STATUS.TO_BE_GENERATED);
      const initialSelectedIds = [...toBeGeneratedIds, ...(toBeGeneratedHeaderId ? [toBeGeneratedHeaderId] : [])];

      return {
        ...state,
        values,
        selectedCheckboxIds: initialSelectedIds,
        isAllToBeGeneratedRowsChecked: !_isEmpty(toBeGeneratedIds),
      };
    }

    case GENERATE_INVOICE_ACTION.SET_SEARCH_QUERY:
      return {
        ...state,
        searchQuery: _get(payload, 'searchQuery', EMPTY_STRING),
      };

    case GENERATE_INVOICE_ACTION.TOGGLE_SECTION_VISIBILITY: {
      const { sectionKey } = payload;
      const visibilityKey = getSectionVisibilityKey(sectionKey);
      const { values } = state;

      if (_isNil(sectionKey) || !visibilityKey) {
        return state;
      }

      const newVisibility = !state[visibilityKey];

      return {
        ...state,
        [visibilityKey]: newVisibility,
        values: _map(values, item => {
          if (item?.isHeading && _get(item, ['invoiceGenerationStatus']) === sectionKey) {
            return { ...item, isSectionVisible: newVisibility };
          }
          return item;
        }),
      };
    }

    case TABLE_ACTION_TYPES.TABLE_ITEMS_SORT: {
      const sortDetails = _get(action, ['payload', 'value', 'sortTypeMap'], EMPTY_OBJECT);
      const { values } = state;
      const columnKey = _head(_keys(sortDetails));
      const sortOrder = _get(sortDetails, columnKey);
      const valuesWithoutHeading = _filter(values, item => !item.isHeading);

      if (columnKey === BULK_ACTION_COLUMN_KEYS.CREATED_BY) {
        return {
          ...state,
          sortDetails,
          values: getValuesWithHeadingRows(
            _orderBy(valuesWithoutHeading, [item => item?.name || item?.id], _toLower(sortOrder)),
          ),
        };
      }

      return {
        ...state,
        sortDetails,
        values: getValuesWithHeadingRows(_orderBy(valuesWithoutHeading, columnKey, _toLower(sortOrder))),
      };
    }

    case TABLE_ACTION_TYPES.SET_SORT_DETAILS:
      return {
        ...state,
        sortDetails: _get(payload, 'sortDetails', EMPTY_OBJECT),
      };

    default:
      return state;
  }
};

const useGenerateInvoiceState = () => useReducer(generateInvoiceReducer, initialState);

export default useGenerateInvoiceState;
