export const GENERATE_INVOICE_ACTION = {
  SET_SELECTED_ROWS: 'SET_SELECTED_ROWS',
  SET_TABLE_DATA: 'SET_TABLE_DATA',
  SET_SEARCH_QUERY: 'SET_SEARCH_QUERY',
  TOGGLE_SECTION_VISIBILITY: 'TOGGLE_SECTION_VISIBILITY',
};

export const HEADING_TITLE = __('Bulk Generate Invoices');

export const SUB_HEADING_TEXT = __(
  'Only filled or partially filled SOs can be invoiced. Closed, Voided, and Reopened SOs are not eligible. Invoice Quantity parts are not allowed.',
);

export const GROUPING_CONFIG = {
  groupByKey: 'invoiceGenerationStatus',
  groupOrder: ['toBeGenerated', 'alreadyGenerated', 'cannotBeGenerated'],
};

export const INVOICE_GENERATION_STATUS = {
  TO_BE_GENERATED: 'toBeGenerated',
  ALREADY_GENERATED: 'alreadyGenerated',
  CANNOT_BE_GENERATED: 'cannotBeGenerated',
};

export const INVOICE_GENERATION_STATUS_LABELS = {
  [INVOICE_GENERATION_STATUS.TO_BE_GENERATED]: __('Invoices to be generated'),
  [INVOICE_GENERATION_STATUS.ALREADY_GENERATED]: __('Invoice already generated'),
  [INVOICE_GENERATION_STATUS.CANNOT_BE_GENERATED]: __("Invoices can't be generated"),
};

export const SECTION_VISIBILITY_KEYS = {
  INVOICE_TO_BE_GENERATED: 'isInvoiceToBeGeneratedSectionVisible',
  INVOICE_ALREADY_GENERATED: 'isInvoiceAlreadyGeneratedSectionVisible',
  INVOICE_CANNOT_BE_GENERATED: 'isInvoiceCannotBeGeneratedSectionVisible',
};
