import React, { memo, useMemo, useEffect, useCallback, useState } from 'react';

import _compact from 'lodash/compact';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _map from 'lodash/map';
import _max from 'lodash/max';
import _noop from 'lodash/noop';
import _reduce from 'lodash/reduce';
import _size from 'lodash/size';
import _uniq from 'lodash/uniq';

import cx from 'classnames';
import PropTypes from 'prop-types';
import { mapProps } from 'recompose';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { formatErrorMessage } from '@tekion/tekion-base/utils/general';

import SalesOrderReader from '@tekion/tekion-business/src/appServices/parts/salesOrder/readers/SalesOrder.reader';

import { Button, Content, Heading, HelperText } from '@tekion/tekion-components/src/atoms';
import { HELPER_TEXT_TYPES } from '@tekion/tekion-components/src/atoms/HelperText';
import useTableCheckbox from '@tekion/tekion-components/src/organisms/inputTable2/hooks/useTableCheckbox';
import { TOASTER_TYPE, toaster } from '@tekion/tekion-components/src/organisms/NotificationWrapper';

import InputTable from '@tekion/tekion-widgets/src/appServices/parts/components/TableV2';
import InputTableCell from '@tekion/tekion-widgets/src/appServices/parts/components/TableV2/components/inputTableCell';

import { resolveUsers } from 'exports/SalesOrder/SalesOrder.actions';

import { useBulkActionMetadata, useBulkActionRequest } from 'queries/bulk-actions';

import TableLoader from '../../../pages/PartsSalesOrderV2/PartsSalesOrderDetail/PartsSalesOrderDetailBody/PartsSalesOrderDetailForm/components/TableLoader';
import BulkActionWrapper from '../components/BulkActionWrapper';
import InputTableRow from '../components/Rows/InputTableRow';
import { BULK_ACTION_COLUMN_KEYS } from '../constants/bulkActionColumns.constants';
import { BULK_ACTION_ASSETS, BULK_ACTION_ASSETS_VS_PRINT_ASSETS } from '../constants/bulkActions.constants';

import COLUMNS from './generateInvoice.config';
import { GENERATE_INVOICE_ACTION, HEADING_TITLE, SUB_HEADING_TEXT } from './generateInvoice.constants';
import {
  filterSalesOrdersForInvoiceGeneration,
  getBulkGenerateInvoicePayload,
  getInvoicesData,
  getValuesWithHeadingRows,
  getVisibleSectionValues,
} from './generateInvoice.helpers';
import styles from './generateInvoice.module.scss';
import useGenerateInvoiceState from './hooks/useGenerateInvoiceState';

const GenerateInvoice = props => {
  const { salesOrders, closeBulkActionModal } = props;

  const filteredSalesOrders = useMemo(() => filterSalesOrdersForInvoiceGeneration(salesOrders), [salesOrders]);

  const { data: originalTableData = EMPTY_ARRAY, isFetching } = useBulkActionMetadata(
    {
      ids: _map(filteredSalesOrders, so => SalesOrderReader.id(so)),
      assetTypes: BULK_ACTION_ASSETS_VS_PRINT_ASSETS[BULK_ACTION_ASSETS.INVOICE],
      printInfoRequired: false,
    },
    {
      select: rawData => rawData,
    },
  );

  const [state, setState] = useGenerateInvoiceState();
  const [userDetailsById, setUserDetailsById] = useState(EMPTY_OBJECT);

  const {
    selectedCheckboxIds,
    sortDetails,
    values,
    isInvoiceToBeGeneratedSectionVisible,
    isInvoiceAlreadyGeneratedSectionVisible,
    isInvoiceCannotBeGeneratedSectionVisible,
    isAllToBeGeneratedRowsChecked,
  } = state;

  const { mutate: triggerBulkAction, isLoading: isBulkActionInProgress } = useBulkActionRequest({
    onSuccess: () => {
      toaster(TOASTER_TYPE.SUCCESS, __('Bulk invoice generation request initiated.'));
      closeBulkActionModal();
    },
    onError: error => {
      const errorDetails = _get(error, 'data.errorDetails', EMPTY_OBJECT);
      const { displayMessage = EMPTY_STRING, debugMessage = EMPTY_STRING } = errorDetails;
      const formattedDisplayMessage = formatErrorMessage(displayMessage);
      const errorMessage =
        formattedDisplayMessage || debugMessage || __('Failed to generate invoices. Please try again.');

      toaster(TOASTER_TYPE.ERROR, errorMessage);
    },
  });

  const onCheckboxSelect = useCallback(
    currentSelectedRowIds => {
      setState({
        type: GENERATE_INVOICE_ACTION.SET_SELECTED_ROWS,
        payload: {
          selectedRowIds: currentSelectedRowIds,
          isAllToBeGeneratedRowsChecked,
        },
      });
    },
    [setState, isAllToBeGeneratedRowsChecked],
  );

  const { context } = useTableCheckbox({
    selectedRowIds: selectedCheckboxIds,
    onSelect: onCheckboxSelect,
    data: getVisibleSectionValues(
      values,
      isInvoiceToBeGeneratedSectionVisible,
      isInvoiceAlreadyGeneratedSectionVisible,
      isInvoiceCannotBeGeneratedSectionVisible,
    ),
  });

  useEffect(() => {
    if (!_isEmpty(originalTableData)) {
      const invoicesData = getInvoicesData(originalTableData, filteredSalesOrders);
      const valuesWithHeadingRows = getValuesWithHeadingRows(invoicesData);
      setState({
        type: GENERATE_INVOICE_ACTION.SET_TABLE_DATA,
        payload: { values: valuesWithHeadingRows },
      });
    }
  }, [originalTableData, filteredSalesOrders, setState]);

  useEffect(() => {
    const resolveUserDetails = async () => {
      if (!_isEmpty(originalTableData)) {
        try {
          const userIds = _compact(_uniq(_map(originalTableData, item => _get(item, 'salesOrder.userId'))));

          if (!_isEmpty(userIds)) {
            const resolvedUsers = await resolveUsers(userIds);
            setUserDetailsById(resolvedUsers);
          }
        } catch (error) {
          // Silent fail for user resolution
        }
      }
    };

    resolveUserDetails();
  }, [originalTableData]);

  const handleHideSection = useCallback(
    ({ headingValue }) => {
      setState({
        type: GENERATE_INVOICE_ACTION.TOGGLE_SECTION_VISIBILITY,
        payload: { sectionKey: headingValue },
      });
    },
    [setState],
  );

  const handleGenerateInvoices = useCallback(() => {
    if (_isEmpty(selectedCheckboxIds)) {
      toaster(TOASTER_TYPE.ERROR, __('Please select at least one invoice to generate'));
      return;
    }

    const selectedInvoices = _reduce(
      selectedCheckboxIds,
      (acc, id) => {
        const invoice = values.find(item => item.id === id && !item.isHeading);
        if (invoice) {
          acc.push(invoice);
        }
        return acc;
      },
      [],
    );

    if (_isEmpty(selectedInvoices)) {
      toaster(TOASTER_TYPE.ERROR, __('No valid invoices selected for generation'));
      return;
    }

    const bulkActionPayload = getBulkGenerateInvoicePayload({
      selectedInvoices,
      salesOrders: filteredSalesOrders,
    });

    triggerBulkAction(bulkActionPayload);
  }, [selectedCheckboxIds, values, filteredSalesOrders, triggerBulkAction]);

  if (_isEmpty(filteredSalesOrders)) {
    return (
      <BulkActionWrapper title={HEADING_TITLE}>
        <Content className="text-center p-4">
          <Heading size={4} className="m-b-2">
            {__('No Eligible Sales Orders')}
          </Heading>
          <HelperText type={HELPER_TEXT_TYPES.ASSISTIVE}>
            {__('Selected sales orders are not eligible for invoice generation.')}
          </HelperText>
        </Content>
      </BulkActionWrapper>
    );
  }

  return (
    <BulkActionWrapper title={HEADING_TITLE}>
      <div className={cx(styles.headerSection)}>
        <div className="m-2">
          <Heading size={4} className="m-b-4">
            {__(`Selected Sales Orders(${_size(filteredSalesOrders)})`)}
          </Heading>
          <HelperText type={HELPER_TEXT_TYPES.ASSISTIVE}>{SUB_HEADING_TEXT}</HelperText>
        </div>
        <div className={styles.rightHeaderSection}>
          <Button
            view={Button.VIEW.PRIMARY}
            onClick={handleGenerateInvoices}
            className={cx(styles.rightHeaderItems, styles.generateButton)}
            disabled={_isEmpty(selectedCheckboxIds) || isBulkActionInProgress}
            loading={isBulkActionInProgress}
          >
            {__('Generate All')}
          </Button>
        </div>
      </div>
      <div className={cx(styles.body)}>
        <useTableCheckbox.context.Provider value={context}>
          {isFetching ? (
            <TableLoader
              columns={COLUMNS}
              rows={_max([_size(filteredSalesOrders), 5])}
              rowClassName={styles.tableRow}
              className={styles.partListContainer}
            />
          ) : (
            <InputTable
              columns={COLUMNS}
              onAction={setState}
              data={getVisibleSectionValues(
                values,
                isInvoiceToBeGeneratedSectionVisible,
                isInvoiceAlreadyGeneratedSectionVisible,
                isInvoiceCannotBeGeneratedSectionVisible,
              )}
              sortDetails={sortDetails}
              InputTableRowComponent={mapProps(prop => ({
                ...prop,
                spanColumnId: BULK_ACTION_COLUMN_KEYS.INVOICE_NO,
                additionalCellProps: { handleHideSection },
              }))(InputTableRow)}
              InputTableCellComponent={mapProps(prop => ({
                ...prop,
                className: styles.tableCell,
                userDetailsById,
              }))(InputTableCell)}
            />
          )}
        </useTableCheckbox.context.Provider>
      </div>
    </BulkActionWrapper>
  );
};

GenerateInvoice.propTypes = {
  salesOrders: PropTypes.array,
  closeBulkActionModal: PropTypes.func,
};

GenerateInvoice.defaultProps = {
  salesOrders: EMPTY_ARRAY,
  closeBulkActionModal: _noop,
};

export default memo(GenerateInvoice);
