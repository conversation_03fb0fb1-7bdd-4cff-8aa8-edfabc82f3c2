import _filter from 'lodash/filter';
import _find from 'lodash/find';
import _get from 'lodash/get';
import _includes from 'lodash/includes';
import _isEmpty from 'lodash/isEmpty';
import _map from 'lodash/map';
import _reduce from 'lodash/reduce';
import _size from 'lodash/size';
import _some from 'lodash/some';
import _toString from 'lodash/toString';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { getCustomerIdWithName } from '@tekion/tekion-base/formatters/customer';
import { uuid } from '@tekion/tekion-base/utils/general';

import SalesOrderReader from '@tekion/tekion-business/src/appServices/parts/salesOrder/readers/SalesOrder.reader';
import SalesOrderFeeLineItemReader from '@tekion/tekion-business/src/appServices/parts/salesOrder/readers/SalesOrderFeeLineItem.reader';

import SalesOrderDomain from '../../../pages/PartsSalesOrderV2/domain/SalesOrder.domain';
import SalesOrderInvoiceDomain from '../../../pages/PartsSalesOrderV2/domain/SalesOrderInvoice.domain';
import { BULK_ACTION_COLUMN_KEYS } from '../constants/bulkActionColumns.constants';
import { BULK_ACTIONS_REQUEST_TYPES, BULK_ACTION_ASSETS } from '../constants/bulkActions.constants';

import { GROUPING_CONFIG, INVOICE_GENERATION_STATUS, SECTION_VISIBILITY_KEYS } from './generateInvoice.constants';

export const filterSalesOrdersForInvoiceGeneration = salesOrders =>
  _filter(salesOrders, salesOrder => SalesOrderDomain.isSalesOrder(salesOrder));

const isEligibleToInvoice = (salesOrder, salesOrderInvoiceList) => {
  const partSaleDetails = SalesOrderReader.partSaleDetails(salesOrder);
  const assetCharges = SalesOrderReader.assetCharges(salesOrder);
  const partReturn = SalesOrderReader.partReturn(salesOrder);

  const hasUnInvoicedParts = _some(partSaleDetails, partLine => {
    const isPartInvoiced = SalesOrderInvoiceDomain.getIsPartRequestInvoiced(salesOrderInvoiceList, partLine);
    return !isPartInvoiced;
  });

  const hasUnInvoicedFees = _some(assetCharges, assetCharge => {
    const feeId = SalesOrderFeeLineItemReader.id(assetCharge);
    const isFeeInvoiced = SalesOrderInvoiceDomain.isSoLevelFeeInvoiced(salesOrderInvoiceList, feeId);
    return !isFeeInvoiced;
  });

  const hasUnInvoicedPartReturn = _some(partReturn, partLine => {
    const isPartInvoiced = SalesOrderInvoiceDomain.getIsPartRequestInvoiced(salesOrderInvoiceList, partLine);
    return !isPartInvoiced;
  });

  return hasUnInvoicedParts || hasUnInvoicedFees || hasUnInvoicedPartReturn;
};

const getInvoiceGenerationStatus = (salesOrder, salesOrderInvoiceList) => {
  const isClosed = SalesOrderDomain.isClosed(salesOrder);
  const isVoided = SalesOrderDomain.isVoided(salesOrder);
  const isReopened = SalesOrderDomain.isReopened(salesOrder);
  const isDraft = SalesOrderDomain.isDraft(salesOrder);

  if (isClosed || isVoided || isReopened || isDraft) {
    return INVOICE_GENERATION_STATUS.CANNOT_BE_GENERATED;
  }

  if (isEligibleToInvoice(salesOrder, salesOrderInvoiceList)) {
    return INVOICE_GENERATION_STATUS.TO_BE_GENERATED;
  }

  return INVOICE_GENERATION_STATUS.ALREADY_GENERATED;
};

export const getInvoicesData = (metaData, _salesOrders) =>
  _map(metaData, metaRecord => {
    const salesOrder = _get(metaRecord, 'salesOrder', EMPTY_OBJECT);
    const salesOrderInvoiceList = _get(metaRecord, 'salesOrderInvoiceList', EMPTY_ARRAY);

    const salesOrderId = SalesOrderReader.id(salesOrder);
    const orderNo = SalesOrderReader.orderNo(salesOrder);
    const customer = SalesOrderReader.customer(salesOrder);
    const customerName = getCustomerIdWithName(customer);
    const createdTime = SalesOrderReader.createdTime(salesOrder);
    const saleAmount = SalesOrderReader.saleAmount(salesOrder);
    const status = SalesOrderReader.status(salesOrder);

    const invoiceGenerationStatus = getInvoiceGenerationStatus(salesOrder, salesOrderInvoiceList);

    return {
      id: salesOrderId,
      [BULK_ACTION_COLUMN_KEYS.INVOICE_NO]: orderNo,
      [BULK_ACTION_COLUMN_KEYS.CUSTOMER_NAME]: customerName,
      [BULK_ACTION_COLUMN_KEYS.CREATED_BY]: _get(salesOrder, 'createdBy.name', EMPTY_STRING),
      [BULK_ACTION_COLUMN_KEYS.CREATED_TIME]: createdTime,
      [BULK_ACTION_COLUMN_KEYS.SALE_AMOUNT]: _get(saleAmount, 'amount', 0),
      [BULK_ACTION_COLUMN_KEYS.ROUTE]: _get(customer, 'routeNumber', EMPTY_STRING),
      [BULK_ACTION_COLUMN_KEYS.INVOICE_STATUS]: status,
      invoiceGenerationStatus,
      salesOrder,
      salesOrderInvoiceList,
      metaRecord,
    };
  });

export const getValuesWithHeadingRows = (tableData = EMPTY_ARRAY) => {
  if (_isEmpty(tableData)) return EMPTY_ARRAY;

  const { groupOrder, groupByKey } = GROUPING_CONFIG;
  const res = _reduce(
    groupOrder,
    (acc, val, index) => {
      const tableRows = _filter(tableData, row => _get(row, [groupByKey]) === val);
      const headingRow = {
        id: _toString(index),
        isHeading: true,
        [groupByKey]: val,
        rowGroupSize: _size(tableRows),
        isSectionVisible: true,
      };

      return [...acc, headingRow, ...tableRows];
    },
    EMPTY_ARRAY,
  );

  return res;
};

export const getVisibleSectionValues = (
  values,
  isInvoiceToBeGeneratedSectionVisible,
  isInvoiceAlreadyGeneratedSectionVisible,
  isInvoiceCannotBeGeneratedSectionVisible,
) =>
  _filter(values, item => {
    const { invoiceGenerationStatus, isHeading } = item;

    if (isHeading) {
      return true;
    }

    switch (invoiceGenerationStatus) {
      case INVOICE_GENERATION_STATUS.TO_BE_GENERATED:
        return isInvoiceToBeGeneratedSectionVisible;
      case INVOICE_GENERATION_STATUS.ALREADY_GENERATED:
        return isInvoiceAlreadyGeneratedSectionVisible;
      case INVOICE_GENERATION_STATUS.CANNOT_BE_GENERATED:
        return isInvoiceCannotBeGeneratedSectionVisible;
      default:
        return true;
    }
  });

export const getBulkGenerateInvoicePayload = ({ selectedInvoices, salesOrders: _salesOrders }) => ({
  requestId: uuid(),
  requestType: BULK_ACTIONS_REQUEST_TYPES.SALES_ORDER_AUTO_INVOICE,
  assetType: BULK_ACTION_ASSETS.INVOICE,
  parentAssetType: BULK_ACTION_ASSETS.SALES_ORDER,
  extra: {
    departmentId: EMPTY_STRING,
    recipients: EMPTY_ARRAY,
  },
  requestList: _reduce(
    selectedInvoices,
    (acc, invoice) => [
      ...acc,
      {
        assetId: invoice.id,
        parentAssetId: invoice.id,
        requestDetails: EMPTY_OBJECT,
      },
    ],
    EMPTY_ARRAY,
  ),
});

export const getSectionVisibilityKey = invoiceGenerationStatus => {
  switch (invoiceGenerationStatus) {
    case INVOICE_GENERATION_STATUS.TO_BE_GENERATED:
      return SECTION_VISIBILITY_KEYS.INVOICE_TO_BE_GENERATED;
    case INVOICE_GENERATION_STATUS.ALREADY_GENERATED:
      return SECTION_VISIBILITY_KEYS.INVOICE_ALREADY_GENERATED;
    case INVOICE_GENERATION_STATUS.CANNOT_BE_GENERATED:
      return SECTION_VISIBILITY_KEYS.INVOICE_CANNOT_BE_GENERATED;
    default:
      return null;
  }
};

export const getAllIdsByGenerationStatus = (data, generationStatus) =>
  _map(
    _filter(data, item => !item?.isHeading && _get(item, 'invoiceGenerationStatus') === generationStatus),
    'id',
  );

export const getHeaderIdByGenerationStatus = (data, generationStatus) =>
  _get(
    _find(data, item => item.isHeading && _get(item, 'invoiceGenerationStatus') === generationStatus),
    'id',
  );

export const getCheckboxIds = ({ lastIsAllRowsChecked, selectedCheckboxIds, headerCheckboxId, totalCheckboxIds }) => {
  if (_isEmpty(selectedCheckboxIds) || _size(selectedCheckboxIds) === _size(totalCheckboxIds)) {
    return {
      isAllRowsChecked: lastIsAllRowsChecked,
      selectedCheckboxIds,
    };
  }

  const isHeaderChecked = _includes(selectedCheckboxIds, headerCheckboxId);

  if (lastIsAllRowsChecked === isHeaderChecked) {
    const totalNonHeaderIds = _filter(totalCheckboxIds, id => id !== headerCheckboxId);
    const selectedNonHeaderIds = _filter(selectedCheckboxIds, id => id !== headerCheckboxId);
    const isAllNonHeaderRowsChecked = _reduce(
      totalNonHeaderIds,
      (acc, nonHeaderId) => acc && _includes(selectedCheckboxIds, nonHeaderId),
      true,
    );
    return {
      isAllRowsChecked: isAllNonHeaderRowsChecked,
      selectedCheckboxIds: [...selectedNonHeaderIds, ...(isAllNonHeaderRowsChecked ? [headerCheckboxId] : [])],
    };
  }

  return isHeaderChecked
    ? { isAllRowsChecked: true, selectedCheckboxIds: totalCheckboxIds }
    : { isAllRowsChecked: false, selectedCheckboxIds: [] };
};
