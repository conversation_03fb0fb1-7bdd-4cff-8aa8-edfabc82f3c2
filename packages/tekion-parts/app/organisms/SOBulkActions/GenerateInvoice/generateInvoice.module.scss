@import 'tstyles/component.scss';

.body {
  @include flex($align-items: flex-start, $justify-content: center);
  @include full-height;
  padding-top: 1.5rem;
}

.headerSection  {
  @include flex($align-items: center, $justify-content: space-between);

}

.rightHeaderSection {
  @include flex($align-items: center);
}

.tableCheckBoxHeaderCell {
  @include flex-center;

}

.tableHeaderCell {
  @include flex-center;

  height: 4rem;
}

.tableRowCell {
  @include flex($align-items: center);
}

.tableRowCellLeftAligned {
  @include flex($align-items: center, $justify-content: flex-start);
}

.rightHeaderItems {
  padding: 0;
  margin: 0rem 1rem;
}

.generateButton {
  min-width: 14rem !important;
  width: 14rem !important;
  text-align: center;

  :global(.ant-btn-loading-icon) {
    margin-right: 0 !important;
  }
}

.tableCell {
  padding: 0rem;
  height: 3.6rem;
  @include flex-center;
}

.tableRow {
  min-height: 3.6rem;
}

.partListContainer {
  @include full-width;
}
