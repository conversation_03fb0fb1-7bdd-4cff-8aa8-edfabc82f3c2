import React from 'react';

import _property from 'lodash/property';

import PropTypes from 'prop-types';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import CellConfig from '@tekion/tekion-widgets/src/appServices/parts/components/TableV2/builders/CellConfig';
import ColumnConfig from '@tekion/tekion-widgets/src/appServices/parts/components/TableV2/builders/ColumnConfig';

import CreatedDate from '../components/Cells/CreatedDate';
import TextRenderer from '../components/Cells/TextRenderer';
import { CheckboxCell } from '../components/Columns/Checkbox';
import { BULK_ACTION_COLUMN_KEYS } from '../constants/bulkActionColumns.constants';

import CreatedByRenderer from './components/CreatedByRenderer';
import GrossSaleAmountRenderer from './components/GrossSaleAmountRenderer';
import InvoiceNoWithHeading from './components/InvoiceNoWithHeading';
import InvoiceStatusRenderer from './components/InvoiceStatusRenderer';
import RouteRenderer from './components/RouteRenderer';
import {
  GROUPING_CONFIG,
  INVOICE_GENERATION_STATUS_LABELS,
  INVOICE_GENERATION_STATUS,
} from './generateInvoice.constants';
import styles from './generateInvoice.module.scss';

const GenerateInvoiceCheckboxCell = props => {
  const { rowData } = props;
  const { isHeading = false, invoiceGenerationStatus } = rowData;

  if (isHeading) {
    return null;
  }

  const isDisabled = invoiceGenerationStatus !== INVOICE_GENERATION_STATUS.TO_BE_GENERATED;

  return (
    <div className={styles.tableCheckBoxCell}>
      <CheckboxCell {...props} disabledFromProps={isDisabled} />
    </div>
  );
};

GenerateInvoiceCheckboxCell.propTypes = {
  rowData: PropTypes.object,
};

GenerateInvoiceCheckboxCell.defaultProps = {
  rowData: EMPTY_OBJECT,
};

const COLUMNS = [
  new ColumnConfig(BULK_ACTION_COLUMN_KEYS.CHECKBOX)
    .setHeader('')
    .setMinWidth(72)
    .setHeaderClassName(styles.tableCheckBoxHeaderCell)
    .addCellConfig(new CellConfig().setComponent(GenerateInvoiceCheckboxCell)),

  new ColumnConfig(BULK_ACTION_COLUMN_KEYS.INVOICE_NO)
    .setAccessor(_property(BULK_ACTION_COLUMN_KEYS.INVOICE_NO))
    .setHeader(__('Invoice#'))
    .setMinWidth(164)
    .setHeaderClassName(styles.tableHeaderCell)
    .setSortable(true)
    .addCellConfig(new CellConfig().setComponent(InvoiceNoWithHeading)),

  new ColumnConfig(BULK_ACTION_COLUMN_KEYS.CUSTOMER_NAME)
    .setAccessor(_property(BULK_ACTION_COLUMN_KEYS.CUSTOMER_NAME))
    .setHeader(__('Customer'))
    .setCanOccupyRestSpace()
    .setHeaderClassName(styles.tableHeaderCell)
    .setCellClassName(styles.tableRowCellLeftAligned)
    .setSortable(true)
    .addCellConfig(new CellConfig().setComponent(TextRenderer)),

  new ColumnConfig(BULK_ACTION_COLUMN_KEYS.CREATED_BY)
    .setAccessor(_property(BULK_ACTION_COLUMN_KEYS.CREATED_BY))
    .setHeader(__('Created By'))
    .setMinWidth(250)
    .setHeaderClassName(styles.tableHeaderCell)
    .setCellClassName(styles.tableRowCell)
    .setSortable(true)
    .addCellConfig(new CellConfig().setComponent(CreatedByRenderer)),

  new ColumnConfig(BULK_ACTION_COLUMN_KEYS.CREATED_TIME)
    .setAccessor(_property(BULK_ACTION_COLUMN_KEYS.CREATED_TIME))
    .setHeader(__('Created Time'))
    .setMinWidth(198)
    .setHeaderClassName(styles.tableHeaderCell)
    .setCellClassName(styles.tableRowCell)
    .setSortable(true)
    .addCellConfig(new CellConfig().setComponent(CreatedDate)),

  new ColumnConfig(BULK_ACTION_COLUMN_KEYS.SALE_AMOUNT)
    .setAccessor(_property(BULK_ACTION_COLUMN_KEYS.SALE_AMOUNT))
    .setHeader(__('Gross Sale Amount'))
    .setMinWidth(164)
    .setHeaderClassName(styles.tableHeaderCell)
    .setCellClassName(styles.tableRowCell)
    .setSortable(true)
    .addCellConfig(new CellConfig().setComponent(GrossSaleAmountRenderer)),

  new ColumnConfig(BULK_ACTION_COLUMN_KEYS.ROUTE)
    .setAccessor(_property(BULK_ACTION_COLUMN_KEYS.ROUTE))
    .setHeader(__('Route'))
    .setMinWidth(196)
    .setHeaderClassName(styles.tableHeaderCell)
    .setCellClassName(styles.tableRowCell)
    .setSortable(true)
    .addCellConfig(new CellConfig().setComponent(RouteRenderer)),

  new ColumnConfig(BULK_ACTION_COLUMN_KEYS.INVOICE_STATUS)
    .setAccessor(_property(BULK_ACTION_COLUMN_KEYS.INVOICE_STATUS))
    .setHeader(__('Invoice Status'))
    .setMinWidth(198)
    .setHeaderClassName(styles.tableHeaderCell)
    .setCellClassName(styles.tableRowCell)
    .addCellConfig(new CellConfig().setComponent(InvoiceStatusRenderer)),
];

COLUMNS.groupingConfig = GROUPING_CONFIG;
COLUMNS.groupingLabels = INVOICE_GENERATION_STATUS_LABELS;

export default COLUMNS;
