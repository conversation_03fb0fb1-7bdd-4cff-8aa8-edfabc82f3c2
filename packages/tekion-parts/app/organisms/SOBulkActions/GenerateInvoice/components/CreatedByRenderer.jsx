import React, { memo } from 'react';

import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';

import { NO_DATA, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import userReader from '@tekion/tekion-base/readers/User';

import Skeleton from '@tekion/tekion-components/src/molecules/skeleton';

import { BULK_ACTION_COLUMN_KEYS } from '../../constants/bulkActionColumns.constants';

const getUserNameToDisplay = userDetails => {
  if (_isEmpty(userDetails)) {
    return <Skeleton paragraph={false} active />;
  }

  return userReader.name(userDetails) || NO_DATA;
};

const CreatedByRenderer = props => {
  const rowData = _get(props, 'rowData', EMPTY_OBJECT);
  const userDetailsById = _get(props, 'userDetailsById', EMPTY_OBJECT);

  const createdByUserDetails = _get(rowData, BULK_ACTION_COLUMN_KEYS.CREATED_BY);

  if (!_isEmpty(createdByUserDetails)) {
    return getUserNameToDisplay(createdByUserDetails);
  }

  const userId = _get(rowData, 'salesOrder.userId');
  if (userId && userDetailsById[userId]) {
    return getUserNameToDisplay(userDetailsById[userId]);
  }

  return NO_DATA;
};

export default memo(CreatedByRenderer);
