import { memo } from 'react';

import _get from 'lodash/get';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import { getFormattedCurrencyPrecisionWithSymbol } from '@tekion/tekion-business/src/appServices/parts/utils/currency.utils';

import { useTekionConversion } from '@tekion/tekion-conversion-web';

import { BULK_ACTION_COLUMN_KEYS } from '../../constants/bulkActionColumns.constants';

const GrossSaleAmountRenderer = props => {
  const { getFormattedCurrency } = useTekionConversion() || EMPTY_OBJECT;
  const rowData = _get(props, 'rowData', EMPTY_OBJECT);

  const saleAmount = _get(rowData, BULK_ACTION_COLUMN_KEYS.SALE_AMOUNT);

  if (!saleAmount) {
    return null;
  }

  return getFormattedCurrencyPrecisionWithSymbol(saleAmount, getFormattedCurrency);
};

export default memo(GrossSaleAmountRenderer);
