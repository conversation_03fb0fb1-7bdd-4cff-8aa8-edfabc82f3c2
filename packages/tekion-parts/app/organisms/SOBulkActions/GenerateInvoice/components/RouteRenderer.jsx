import _get from 'lodash/get';

import { EMPTY_OBJECT, NO_DATA } from '@tekion/tekion-base/app.constants';

import { BULK_ACTION_COLUMN_KEYS } from '../../constants/bulkActionColumns.constants';

const RouteRenderer = props => {
  const rowData = _get(props, 'rowData', EMPTY_OBJECT);

  const route = _get(rowData, BULK_ACTION_COLUMN_KEYS.ROUTE);

  return route || NO_DATA;
};

export default RouteRenderer;
