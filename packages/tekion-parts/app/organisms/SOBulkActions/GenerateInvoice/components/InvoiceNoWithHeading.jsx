import React, { useCallback, memo } from 'react';

import _get from 'lodash/get';

import cx from 'classnames';
import PropTypes from 'prop-types';

import { EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';

import { Content } from '@tekion/tekion-components/src/atoms';
import IconAsBtn from '@tekion/tekion-components/src/atoms/iconAsBtn';

import TextRenderer from '@tekion/tekion-widgets/src/appServices/parts/components/TableV2/cellRenderers/TextDisplayCellRenderer';

import { CheckboxCell } from '../../components/Columns/Checkbox';
import { INVOICE_GENERATION_STATUS_LABELS, INVOICE_GENERATION_STATUS } from '../generateInvoice.constants';

import styles from './InvoiceNoWithHeading.module.scss';

const InvoiceNoWithHeading = props => {
  const { rowData, value, additionalCellProps } = props;
  const { isHeading = false, rowGroupSize = 0, isSectionVisible = true } = rowData;
  const { handleHideSection } = additionalCellProps || {};
  const invoiceGenerationStatus = _get(rowData, 'invoiceGenerationStatus');

  const onClick = useCallback(() => {
    if (handleHideSection) {
      handleHideSection({
        headingValue: invoiceGenerationStatus,
        isVisible: isSectionVisible,
      });
    }
  }, [handleHideSection, invoiceGenerationStatus, isSectionVisible]);

  if (isHeading) {
    const sectionLabel = INVOICE_GENERATION_STATUS_LABELS[invoiceGenerationStatus] || 'Unknown Section';
    const showCheckbox = invoiceGenerationStatus === INVOICE_GENERATION_STATUS.TO_BE_GENERATED;

    return (
      <div className={cx(styles.subHeadingContainer)}>
        <div className="d-flex align-items-center">
          {showCheckbox && rowGroupSize > 0 ? (
            <CheckboxCell rowData={rowData} className={styles.subHeaderCheckboxCell} disabledFromProps={false} />
          ) : (
            <div className={styles.subHeaderCheckboxCell} />
          )}
          <Content highlight>{`${sectionLabel} (${rowGroupSize})`}</Content>
        </div>
        <div>
          <IconAsBtn onClick={onClick}>{isSectionVisible ? 'icon-caret-down' : 'icon-caret-right'}</IconAsBtn>
        </div>
      </div>
    );
  }

  return <TextRenderer value={value} />;
};

InvoiceNoWithHeading.propTypes = {
  additionalCellProps: PropTypes.object,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  rowData: PropTypes.object,
};

InvoiceNoWithHeading.defaultProps = {
  additionalCellProps: EMPTY_OBJECT,
  value: EMPTY_STRING,
  rowData: EMPTY_OBJECT,
};

export default memo(InvoiceNoWithHeading);
