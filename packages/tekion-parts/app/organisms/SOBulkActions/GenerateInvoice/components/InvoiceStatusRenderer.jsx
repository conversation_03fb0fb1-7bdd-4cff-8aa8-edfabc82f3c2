import React, { memo } from 'react';

import _get from 'lodash/get';

import PropTypes from 'prop-types';

import { EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';

import StatusItem from '@tekion/tekion-components/src/atoms/StatusItem';

import { SALES_STATUS_VS_STATUS_ITEM } from '@tekion/tekion-widgets/src/appServices/parts/salesOrder/salesOrderStatusItem';

const InvoiceStatusRenderer = ({ rowData, value }) => {
  const { isHeading = false } = rowData;

  if (isHeading) return null;

  const statusItemProps = _get(SALES_STATUS_VS_STATUS_ITEM, value, EMPTY_OBJECT);

  return <StatusItem {...statusItemProps} />;
};

InvoiceStatusRenderer.propTypes = {
  value: PropTypes.string,
  rowData: PropTypes.object,
};

InvoiceStatusRenderer.defaultProps = {
  value: EMPTY_STRING,
  rowData: EMPTY_OBJECT,
};

export default memo(InvoiceStatusRenderer);
