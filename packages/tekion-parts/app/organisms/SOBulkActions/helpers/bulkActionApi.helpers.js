import _filter from 'lodash/filter';
import _find from 'lodash/find';
import _get from 'lodash/get';
import _head from 'lodash/head';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';
import _isString from 'lodash/isString';
import _map from 'lodash/map';
import _reduce from 'lodash/reduce';
import _split from 'lodash/split';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { SALES_STATUS } from '@tekion/tekion-base/constants/parts/sales';
import { getCustomerIdWithName } from '@tekion/tekion-base/formatters/customer';

import SalesOrderReader from '@tekion/tekion-business/src/appServices/parts/salesOrder/readers/SalesOrder.reader';
import SalesOrderListViewItemReader from '@tekion/tekion-business/src/appServices/parts/salesOrder/readers/SalesOrderListViewItem.reader';
import { SALES_ORDER_TYPE } from '@tekion/tekion-business/src/appServices/parts/salesOrder/salesOrder.constants';

import { ASSET_TYPE } from '@tekion/tekion-components/src/organisms/PdfViewerPartsV2/PdfViewer.constants';

import { BULK_ACTION_COLUMN_KEYS } from '../constants/bulkActionColumns.constants';
import { BULK_ACTION_ASSETS } from '../constants/bulkActions.constants';
import SalesOrderBulkPrintReader from '../readers/SalesOrderBulkPrint.reader';

const getInvoice = (salesOrderInvoiceList, invoiceId) =>
  _find(salesOrderInvoiceList, invoice => invoice.id === invoiceId);

const getAssetType = salesOrder => {
  if (
    SalesOrderReader.orderType(salesOrder) === SALES_ORDER_TYPE.CREDIT_MEMO ||
    SalesOrderReader.orderType(salesOrder) === SALES_ORDER_TYPE.CREDIT_SALE
  ) {
    return ASSET_TYPE.CREDIT_MEMO;
  }

  if (SalesOrderReader.status(salesOrder) === SALES_STATUS.DRAFT) return ASSET_TYPE.SALES_ORDER_QUOTE;

  return ASSET_TYPE.SALES_ORDER;
};

const parseSalesOrderData = (salesOrder, { createdBy, customerName }) => ({
  id: `${SalesOrderReader.id(salesOrder)}${0}${ASSET_TYPE.SALES_ORDER}`,
  [BULK_ACTION_COLUMN_KEYS.ROUTE]: _get(SalesOrderReader.shippingAddress(salesOrder), 'routeNumber'),
  [BULK_ACTION_COLUMN_KEYS.CREATED_BY]: createdBy,
  [BULK_ACTION_COLUMN_KEYS.CUSTOMER_NAME]: customerName,
  [BULK_ACTION_COLUMN_KEYS.INVOICE_NO]: SalesOrderReader.orderNo(salesOrder),
  [BULK_ACTION_COLUMN_KEYS.CREATED_TIME]: SalesOrderReader.createdTime(salesOrder),
  [BULK_ACTION_COLUMN_KEYS.PRINT_STATUS]: SalesOrderBulkPrintReader.printed(salesOrder),
  [BULK_ACTION_COLUMN_KEYS.SALE_AMOUNT]: SalesOrderReader.saleAmount(salesOrder),
  [BULK_ACTION_COLUMN_KEYS.INVOICE_STATUS]: SalesOrderReader.status(salesOrder),
  [BULK_ACTION_COLUMN_KEYS.MODIFIED_TIME]: SalesOrderReader.modifiedTime(salesOrder),
  salesOrderId: SalesOrderReader.id(salesOrder),
  salesOrderNo: SalesOrderReader.orderNo(salesOrder),
  assetType: getAssetType(salesOrder),
  assetId: SalesOrderReader.id(salesOrder),
  departmentId: SalesOrderListViewItemReader.departmentId(salesOrder),
});

const parseReturnInvoiceData = (
  returnInvoice,
  salesOrder,
  salesOrderInvoiceList,
  index,
  { createdBy, customerName },
) => {
  const returnAssetType = _isNil(SalesOrderBulkPrintReader.invoiceId(returnInvoice))
    ? ASSET_TYPE.SALES_ORDER_RETURN
    : ASSET_TYPE.MULTI_INVOICE_RETURN;
  return {
    id: `${SalesOrderBulkPrintReader.id(returnInvoice)}${index}${returnAssetType}`,
    [BULK_ACTION_COLUMN_KEYS.ROUTE]: _get(SalesOrderReader.shippingAddress(salesOrder), 'routeNumber'),
    [BULK_ACTION_COLUMN_KEYS.CREATED_BY]: createdBy,
    [BULK_ACTION_COLUMN_KEYS.CUSTOMER_NAME]: customerName,
    [BULK_ACTION_COLUMN_KEYS.INVOICE_NO]: SalesOrderBulkPrintReader.returnNumber(returnInvoice),
    [BULK_ACTION_COLUMN_KEYS.CREATED_TIME]:
      SalesOrderBulkPrintReader.createdTime(returnInvoice) || SalesOrderReader.createdTime(salesOrder),
    [BULK_ACTION_COLUMN_KEYS.PRINT_STATUS]: SalesOrderBulkPrintReader.printed(returnInvoice),
    [BULK_ACTION_COLUMN_KEYS.SALE_AMOUNT]:
      SalesOrderBulkPrintReader.returnAmount(returnInvoice) || SalesOrderReader.saleAmount(salesOrder),
    [BULK_ACTION_COLUMN_KEYS.INVOICE_STATUS]: SalesOrderBulkPrintReader.status(returnInvoice),
    [BULK_ACTION_COLUMN_KEYS.MODIFIED_TIME]: _isNil(SalesOrderBulkPrintReader.invoiceId(returnInvoice))
      ? SalesOrderReader.modifiedTime(salesOrder)
      : _get(getInvoice(salesOrderInvoiceList, SalesOrderBulkPrintReader.invoiceId(returnInvoice)), 'id'),
    salesOrderId: SalesOrderReader.id(salesOrder),
    salesOrderNo: SalesOrderReader.orderNo(salesOrder),
    assetType: _isNil(SalesOrderBulkPrintReader.invoiceId(returnInvoice))
      ? ASSET_TYPE.SALES_ORDER_RETURN
      : ASSET_TYPE.MULTI_INVOICE_RETURN,
    assetId: SalesOrderBulkPrintReader.id(returnInvoice),
    departmentId: SalesOrderListViewItemReader.departmentId(salesOrder),
  };
};

const parseMultiInvoiceData = (invoice, salesOrder, index, { createdBy, customerName }) => ({
  id: `${SalesOrderBulkPrintReader.id(invoice)}${index}${ASSET_TYPE.MULTI_INVOICE}`,
  [BULK_ACTION_COLUMN_KEYS.ROUTE]: _get(SalesOrderReader.shippingAddress(salesOrder), 'routeNumber'),
  [BULK_ACTION_COLUMN_KEYS.CREATED_BY]: createdBy,
  [BULK_ACTION_COLUMN_KEYS.CUSTOMER_NAME]: customerName,
  [BULK_ACTION_COLUMN_KEYS.INVOICE_NO]: SalesOrderBulkPrintReader.invoiceNumber(invoice),
  [BULK_ACTION_COLUMN_KEYS.CREATED_TIME]:
    SalesOrderBulkPrintReader.createdTime(invoice) || SalesOrderReader.createdTime(salesOrder),
  [BULK_ACTION_COLUMN_KEYS.MODIFIED_TIME]: SalesOrderBulkPrintReader.modifiedTime(invoice),
  [BULK_ACTION_COLUMN_KEYS.PRINT_STATUS]: SalesOrderBulkPrintReader.printed(invoice),
  [BULK_ACTION_COLUMN_KEYS.SALE_AMOUNT]:
    SalesOrderBulkPrintReader.invoiceAmount(invoice) || SalesOrderReader.saleAmount(salesOrder),
  [BULK_ACTION_COLUMN_KEYS.INVOICE_STATUS]: SalesOrderBulkPrintReader.status(invoice),
  salesOrderId: SalesOrderReader.id(salesOrder),
  salesOrderNo: SalesOrderReader.orderNo(salesOrder),
  assetType: ASSET_TYPE.MULTI_INVOICE,
  assetId: SalesOrderBulkPrintReader.id(invoice),
  departmentId: SalesOrderListViewItemReader.departmentId(salesOrder),
});

const getNonVoidedInvoices = (invoices = EMPTY_ARRAY) =>
  _filter(invoices, inv => SalesOrderBulkPrintReader.status(inv) !== SALES_STATUS.VOIDED);

const parseDataByDocument = ({
  salesOrder = EMPTY_OBJECT,
  salesOrderInvoiceList = EMPTY_ARRAY,
  salesOrderReturnList = EMPTY_ARRAY,
}) => {
  const customerData = SalesOrderReader.customer(salesOrder);
  const customerName = getCustomerIdWithName(customerData);
  const createdBy = {
    name: SalesOrderReader.partCounterPersonName(salesOrder),
    id: SalesOrderReader.userId(salesOrder),
  };

  const multiInvoiceParsedInfo = _map(getNonVoidedInvoices(salesOrderInvoiceList), (invoice, index) =>
    parseMultiInvoiceData(invoice, salesOrder, index, { createdBy, customerName }),
  );

  const returnInvoiceParsedData = _map(getNonVoidedInvoices(salesOrderReturnList), (returnInvoice, index) =>
    parseReturnInvoiceData(returnInvoice, salesOrder, salesOrderInvoiceList, index, {
      createdBy,
      customerName,
    }),
  );

  const salesOrderParsedData = parseSalesOrderData(salesOrder, {
    createdBy,
    customerName,
  });

  if (!_isEmpty(salesOrderInvoiceList) || !_isEmpty(salesOrderReturnList))
    return [
      ...(_isEmpty(salesOrderInvoiceList) ? EMPTY_ARRAY : multiInvoiceParsedInfo),
      ...(_isEmpty(salesOrderReturnList) ? EMPTY_ARRAY : returnInvoiceParsedData),
    ];

  return [salesOrderParsedData];
};

export const parseMetaData = (rawData, bulkPrintDocumentType) => {
  if (bulkPrintDocumentType === BULK_ACTION_ASSETS.INVOICE) {
    return _reduce(
      rawData,
      (acc, { salesOrder, salesOrderInvoiceList }) => {
        if (_isEmpty(salesOrderInvoiceList)) return [...acc, ...parseDataByDocument({ salesOrder })];
        return [...acc, ...parseDataByDocument({ salesOrder, salesOrderInvoiceList })];
      },
      EMPTY_ARRAY,
    );
  }

  if (bulkPrintDocumentType === BULK_ACTION_ASSETS.CREDIT_MEMO) {
    return _reduce(
      rawData,
      (acc, { salesOrder, salesOrderReturnList }) => {
        const isCreditMemo =
          SalesOrderReader.orderType(salesOrder) === SALES_ORDER_TYPE.CREDIT_MEMO ||
          SalesOrderReader.orderType(salesOrder) === SALES_ORDER_TYPE.CREDIT_SALE;
        if (isCreditMemo) return [...acc, ...parseDataByDocument({ salesOrder })];
        return [...acc, ...parseDataByDocument({ salesOrder, salesOrderReturnList })];
      },
      EMPTY_ARRAY,
    );
  }

  return _reduce(rawData, (acc, { salesOrder }) => [...acc, ...parseDataByDocument({ salesOrder })], []);
};

const matchInvoicesToSalesOrder = (salesOrder, salesOrderInvoiceList) => {
  const salesOrderNo = SalesOrderReader.orderNo(salesOrder);
  const matchingInvoices = _filter(salesOrderInvoiceList, invoice => {
    const invoiceNumber = SalesOrderBulkPrintReader.invoiceNumber(invoice);
    if (!invoiceNumber) return false;

    const invoiceParts = _split(invoiceNumber, '-');
    const invoiceSalesOrderNo = _head(invoiceParts);

    return invoiceSalesOrderNo === salesOrderNo;
  });

  return {
    ...salesOrder,
    salesOrderInvoiceList: matchingInvoices,
  };
};

export const parseMetaDataForGenerateInvoice = rawData =>
  _reduce(
    rawData,
    (acc, { salesOrder, salesOrderInvoiceList }) => {
      const salesOrderWithInvoices = matchInvoicesToSalesOrder(salesOrder, salesOrderInvoiceList || EMPTY_ARRAY);

      if (_isEmpty(salesOrderWithInvoices.salesOrderInvoiceList)) {
        return [...acc, ...parseDataByDocument({ salesOrder: salesOrderWithInvoices })];
      }
      return [
        ...acc,
        ...parseDataByDocument({
          salesOrder: salesOrderWithInvoices,
          salesOrderInvoiceList: salesOrderWithInvoices.salesOrderInvoiceList,
        }),
      ];
    },
    EMPTY_ARRAY,
  );

const parseRequestLine = data => ({
  id: SalesOrderBulkPrintReader.id(data),
  parentAssetId: _get(data, 'parentAssetId', EMPTY_STRING),
  assetId: _get(data, 'assetId', EMPTY_STRING),
  status: _get(data, 'status', EMPTY_STRING),
  [BULK_ACTION_COLUMN_KEYS.PRINT_STATUS]:
    _get(data, ['responseDetails', 'printStatus']) ||
    _get(data, ['responseDetails', 'documentGenerationStatus']) ||
    _get(data, 'status') ||
    EMPTY_STRING,
});

export const parseRequestData = data => {
  const requestList = _get(data, 'requestList', EMPTY_ARRAY);

  return {
    status: _get(data, 'status', EMPTY_STRING),
    requestType: _get(data, 'requestType', EMPTY_STRING),
    bulkPrintDocumentType: _get(data, 'assetType', EMPTY_STRING),
    requestLineData: _map(requestList, req => parseRequestLine(req)),
  };
};

export const formatErrorMessage = errorMessage => {
  if (!_isString(errorMessage)) {
    return EMPTY_STRING;
  }

  const formattedMessage = _head(_split(errorMessage, '-'));

  return formattedMessage || EMPTY_STRING;
};
