import { EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';

export const BULK_ACTION_ASSETS = {
  INVOICE: 'INVOICE', // comprises all salesOrders, invoices and returns
  QUOTE: 'QUOTE',
  PICKLIST: 'PICKLIST',
  CREDIT_MEMO: 'CREDIT_MEMO',
  SALES_ORDER: 'SALES_ORDER', // comprises salesOrder only, currently used for SALES_ORDER_CREATION
};

// to be used in metadata query
export const BULK_ACTION_ASSETS_VS_PRINT_ASSETS = {
  [BULK_ACTION_ASSETS.INVOICE]: ['SALES_ORDER_INVOICE'],
  [BULK_ACTION_ASSETS.QUOTE]: EMPTY_ARRAY,
  [BULK_ACTION_ASSETS.CREDIT_MEMO]: ['CREDIT_MEMO'],
};

export const PRINT_PICKLIST_PAYLOAD = {
  MODULE_TYPE: 'PARTS',
  DOCUMENT_TYPE: 'SO_PICKLIST',
  KEY_TO_SORT: 'primaryBin',
  COPY_TYPE: ['DEALER'],
};

export const BULK_ACTIONS_REQUEST_TYPES = {
  PRINT: 'PRINT',
  PICKLIST: 'PICKLIST',
  EMAIL: 'EMAIL',
  DOWNLOAD: 'DOWNLOAD',
  LABEL_PRINT: 'LABEL_PRINT',
  SOR_MOVE_TO_OH: 'SOR_MOVE_TO_OH',
  SALES_ORDER_RETURN_CREATE: 'SALES_ORDER_RETURN_CREATE',
  SALES_ORDER_PAYMENT: 'SALES_ORDER_PAYMENT',
  SALES_ORDER_CREATION: 'SALES_ORDER_CREATION',
  SALES_ORDER_AUTO_INVOICE: 'SALES_ORDER_AUTO_INVOICE',
};

export const BULK_ACTIONS_STATUS = {
  FAILED: 'FAILED',
  SUCCESS: 'SUCCESS',
  PENDING: 'PENDING',
  IN_PROGRESS: 'IN_PROGRESS',
  POST_PROCESSING: 'POST_PROCESSING',
};
