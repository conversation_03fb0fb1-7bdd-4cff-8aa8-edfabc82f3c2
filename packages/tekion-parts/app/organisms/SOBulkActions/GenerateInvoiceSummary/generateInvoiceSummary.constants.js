import COLORS from '@tekion/tekion-styles-next/scss/exports.scss';
export const BULK_STATUS_VS_INVOICE_STATUS_CONFIG = {
  SUCCESS: {
    icon: 'icon-passed',
    color: COLORS.statusSuccess,
    label: __('Completed'),
  },
  FAILED: {
    icon: 'icon-failed',
    color: COLORS.destructivetext,
    label: __('Failed'),
  },
  IN_PROGRESS: {
    icon: 'icon-clocked-in',
    color: COLORS.dodgerBlue,
    label: __('Generating'),
  },
  PENDING: {
    icon: 'icon-clocked-in',
    color: COLORS.destructivetext,
    label: __('Pending'),
  },
};
