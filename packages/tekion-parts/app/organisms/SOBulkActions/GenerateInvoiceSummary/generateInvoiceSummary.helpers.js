import _find from 'lodash/find';
import _isEmpty from 'lodash/isEmpty';
import _map from 'lodash/map';
import _toString from 'lodash/toString';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';

export const getSummaryTableData = (metaData = EMPTY_ARRAY, requestData = EMPTY_OBJECT) => {
  const { requestLineData = EMPTY_ARRAY, ...rest } = requestData;

  if (_isEmpty(metaData) || _isEmpty(requestLineData)) {
    return { rows: EMPTY_ARRAY };
  }

  const rows = _map(requestLineData, request => {
    const { assetId = EMPTY_STRING } = request;

    const metaDataItem = _find(metaData, item => _toString(item?.assetId) === _toString(assetId));
    return {
      ...metaDataItem,
      ...request,
    };
  });

  return {
    ...rest,
    rows,
  };
};
