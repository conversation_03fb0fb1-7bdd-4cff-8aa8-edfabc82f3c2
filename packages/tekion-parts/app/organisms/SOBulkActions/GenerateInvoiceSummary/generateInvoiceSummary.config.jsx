import React from 'react';

import CreatedBy from '../components/Cells/CreatedBy';
import CreatedDate from '../components/Cells/CreatedDate';
import SaleAmount from '../components/Cells/SaleAmount';
import TextRenderer from '../components/Cells/TextRenderer';
import { BULK_ACTION_COLUMN_KEYS } from '../constants/bulkActionColumns.constants';
import { BULK_ACTION_ASSETS } from '../constants/bulkActions.constants';

import InvoiceStatusRenderer from './components/InvoiceStatusRenderer';

export const BULK_ASSET_VS_TITLE = {
  [BULK_ACTION_ASSETS.INVOICE]: __('Generate Invoice Summary'),
  [BULK_ACTION_ASSETS.QUOTE]: __('Generate Quotes Summary'),
  [BULK_ACTION_ASSETS.CREDIT_MEMO]: __('Generate Credit Memos Summary'),
};

const INVOICE_NO = {
  Header: __('SO Number'),
  accessor: BULK_ACTION_COLUMN_KEYS.INVOICE_NO,
  key: BULK_ACTION_COLUMN_KEYS.INVOICE_NO,
  minWidth: 120,
  sortable: true,
  Cell: TextRenderer,
};

const ROUTE = {
  Header: __('Route'),
  accessor: BULK_ACTION_COLUMN_KEYS.ROUTE,
  key: BULK_ACTION_COLUMN_KEYS.ROUTE,
  minWidth: 164,
  sortable: true,
  Cell: TextRenderer,
};

const CUSTOMER = {
  Header: __('Customer Name'),
  accessor: BULK_ACTION_COLUMN_KEYS.CUSTOMER_NAME,
  key: BULK_ACTION_COLUMN_KEYS.CUSTOMER_NAME,
  minWidth: 164,
  sortable: true,
  Cell: TextRenderer,
};

const CREATED_BY = {
  Header: __('Created By'),
  accessor: BULK_ACTION_COLUMN_KEYS.CREATED_BY,
  key: BULK_ACTION_COLUMN_KEYS.CREATED_BY,
  minWidth: 154,
  sortable: true,
  Cell: ({ value }) => <CreatedBy value={value} />,
};

const CREATION_DATE = {
  Header: __('Creation Date'),
  accessor: BULK_ACTION_COLUMN_KEYS.CREATED_TIME,
  key: BULK_ACTION_COLUMN_KEYS.CREATED_TIME,
  minWidth: 164,
  sortable: true,
  Cell: ({ value }) => <CreatedDate value={value} />,
};

const INVOICE_STATUS = {
  Header: __('Invoice Status'),
  accessor: BULK_ACTION_COLUMN_KEYS.INVOICE_STATUS,
  key: BULK_ACTION_COLUMN_KEYS.INVOICE_STATUS,
  minWidth: 128,
  Cell: ({ original }) => <InvoiceStatusRenderer rowData={original} />,
};

const SALE_AMOUNT = {
  Header: __('Sale Amount'),
  accessor: BULK_ACTION_COLUMN_KEYS.SALE_AMOUNT,
  key: BULK_ACTION_COLUMN_KEYS.SALE_AMOUNT,
  minWidth: 144,
  Cell: ({ value }) => <SaleAmount value={value} />,
};

export default [INVOICE_NO, CUSTOMER, CREATED_BY, CREATION_DATE, ROUTE, SALE_AMOUNT, INVOICE_STATUS];
