import React, { memo, useEffect } from 'react';

import _filter from 'lodash/filter';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _map from 'lodash/map';
import _size from 'lodash/size';
import _toString from 'lodash/toString';

import cx from 'classnames';
import PropTypes from 'prop-types';

import { EMPTY_ARRAY, EMPTY_STRING } from '@tekion/tekion-base/app.constants';

import { Content } from '@tekion/tekion-components/src/atoms';
import Loader from '@tekion/tekion-components/src/molecules/loader';
import { FixedColumnTable } from '@tekion/tekion-components/src/molecules/table';
import withCustomSortTable from '@tekion/tekion-components/src/organisms/withCustomSortTable';

import TableManager from '@tekion/tekion-widgets/src/organisms/tableManager';

import { useBulkActionMetadata, useBulkActionFetchRequestData } from 'queries/bulk-actions';

import BulkActionWrapper from '../components/BulkActionWrapper';
import {
  BULK_ACTION_ASSETS,
  BULK_ACTION_ASSETS_VS_PRINT_ASSETS,
  BULK_ACTIONS_STATUS,
} from '../constants/bulkActions.constants';
import { parseMetaDataForGenerateInvoice, parseRequestData } from '../helpers/bulkActionApi.helpers';

import FailedInvoiceBanner from './components/FailedInvoiceBanner';
import COLUMNS, { BULK_ASSET_VS_TITLE } from './generateInvoiceSummary.config';
import { getSummaryTableData } from './generateInvoiceSummary.helpers';
import useGenerateInvoiceSummaryState, {
  GENERATE_INVOICE_SUMMARY_ACTIONS,
} from './hooks/useGenerateInvoiceSummaryState';

const SortableTableManager = withCustomSortTable(TableManager);

const GenerateInvoiceSummary = props => {
  const { bulkRequestId, bulkActionAssetType, lastUpdatedAt } = props;

  const { data: invoiceStatusData, isFetching: isRequestDataFetching } = useBulkActionFetchRequestData(
    bulkRequestId,
    lastUpdatedAt,
    {
      select: rawData => parseRequestData(rawData),
    },
  );

  const { data: tableData, isFetching: isTableDataFetching } = useBulkActionMetadata(
    {
      ids: _map(invoiceStatusData?.requestLineData, 'parentAssetId', EMPTY_STRING),
      assetTypes:
        BULK_ACTION_ASSETS_VS_PRINT_ASSETS[bulkActionAssetType] ||
        BULK_ACTION_ASSETS_VS_PRINT_ASSETS[BULK_ACTION_ASSETS.INVOICE],
      printInfoRequired: false, // Invoice generation doesn't need print info
    },
    {
      enabled: !isRequestDataFetching,
      select: rawData => {
        const parsedMetaData = parseMetaDataForGenerateInvoice(rawData);
        const combinedTableDataWithInvoiceStatus = getSummaryTableData(parsedMetaData, invoiceStatusData);
        return combinedTableDataWithInvoiceStatus;
      },
    },
  );

  const [state, setState] = useGenerateInvoiceSummaryState();

  useEffect(
    () =>
      setState({
        type: GENERATE_INVOICE_SUMMARY_ACTIONS.SET_TABLE_DATA,
        payload: {
          tableData,
        },
      }),
    [tableData, setState],
  );

  const { sortDetails, tableData: originalTableData } = state;

  const rows = _get(originalTableData, 'rows', EMPTY_ARRAY);

  // Calculate failed invoices count
  const failedInvoicesCount = _size(_filter(rows, ({ status }) => status === BULK_ACTIONS_STATUS.FAILED));

  if (_isEmpty(_toString(bulkRequestId))) {
    return (
      <BulkActionWrapper title={BULK_ASSET_VS_TITLE[bulkActionAssetType]}>
        <Content className={cx('d-flex bold justify-content-center m-t-24')}>
          {__('Selected items are not applicable for this action. Please select relevant items.')}
        </Content>
      </BulkActionWrapper>
    );
  }

  return (
    <BulkActionWrapper title={BULK_ASSET_VS_TITLE[bulkActionAssetType]}>
      {isTableDataFetching || isRequestDataFetching ? (
        <Loader />
      ) : (
        <SortableTableManager
          tableComponent={FixedColumnTable}
          columns={COLUMNS}
          sortDetails={sortDetails}
          onAction={setState}
          l3HeaderProps={{
            render: args => <FailedInvoiceBanner {...args} />,
            failedInvoicesCount,
          }}
          data={rows}
          isMultiSort={false}
          tableManagerProps={{ showL3Header: true }}
        />
      )}
    </BulkActionWrapper>
  );
};

GenerateInvoiceSummary.propTypes = {
  lastUpdatedAt: PropTypes.number,
  bulkRequestId: PropTypes.string,
  bulkActionAssetType: PropTypes.string,
};

GenerateInvoiceSummary.defaultProps = {
  lastUpdatedAt: 0,
  bulkRequestId: EMPTY_STRING,
  bulkActionAssetType: BULK_ACTION_ASSETS.INVOICE,
};

export default memo(GenerateInvoiceSummary);
