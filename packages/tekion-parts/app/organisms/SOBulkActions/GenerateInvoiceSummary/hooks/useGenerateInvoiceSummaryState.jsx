import { useReducer } from 'react';

import _get from 'lodash/get';
import _head from 'lodash/head';
import _keys from 'lodash/keys';
import _orderBy from 'lodash/orderBy';
import _toLower from 'lodash/toLower';

import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import { TABLE_ACTION_TYPES } from '@tekion/tekion-widgets/src/organisms/tableManager';

import { BULK_ACTION_COLUMN_KEYS } from '../../constants/bulkActionColumns.constants';

export const GENERATE_INVOICE_SUMMARY_ACTIONS = {
  SET_TABLE_DATA: 'SET_TABLE_DATA',
  SET_SORT_DETAILS: 'SET_SORT_DETAILS',
};

const INITIAL_STATE = {
  tableData: EMPTY_OBJECT,
  sortDetails: EMPTY_OBJECT,
  regenerateBannerDisabled: true,
  failedRequestLineIds: EMPTY_ARRAY,
};

const reducer = (state, action) => {
  const type = _get(action, 'type');

  switch (type) {
    case GENERATE_INVOICE_SUMMARY_ACTIONS.SET_TABLE_DATA: {
      const tableData = _get(action, ['payload', 'tableData'], EMPTY_OBJECT);
      const failedRequestLineIds = _get(tableData, 'failedRequestLineIds', EMPTY_ARRAY);
      const regenerateBannerDisabled = _get(tableData, 'regenerateBannerDisabled', true);

      return {
        ...state,
        tableData,
        failedRequestLineIds,
        regenerateBannerDisabled,
      };
    }

    case GENERATE_INVOICE_SUMMARY_ACTIONS.SET_SORT_DETAILS: {
      const sortDetails = _get(action, ['payload', 'sortDetails'], EMPTY_OBJECT);
      return {
        ...state,
        sortDetails,
      };
    }

    case TABLE_ACTION_TYPES.TABLE_ITEMS_SORT: {
      const sortTypeMap = _get(action, ['payload', 'value', 'sortTypeMap']);
      const { tableData = EMPTY_OBJECT } = state;
      const { rows = EMPTY_ARRAY } = tableData;
      const columnKey = _head(_keys(sortTypeMap));
      const sortOrder = _get(sortTypeMap, columnKey);

      if (columnKey === BULK_ACTION_COLUMN_KEYS.CREATED_BY) {
        return {
          ...state,
          sortDetails: sortTypeMap,
          tableData: {
            ...tableData,
            rows: _orderBy(rows, [item => item?.name || item?.id], _toLower(sortOrder)),
          },
        };
      }

      if (columnKey === BULK_ACTION_COLUMN_KEYS.INVOICE_STATUS) {
        return {
          ...state,
          sortDetails: sortTypeMap,
          tableData: {
            ...tableData,
            rows: _orderBy(rows, 'status', _toLower(sortOrder)),
          },
        };
      }

      return {
        ...state,
        sortDetails: sortTypeMap,
        tableData: {
          ...tableData,
          rows: _orderBy(rows, columnKey, _toLower(sortOrder)),
        },
      };
    }

    default:
      return state;
  }
};

const useGenerateInvoiceSummaryState = () => useReducer(reducer, INITIAL_STATE);

export default useGenerateInvoiceSummaryState;
