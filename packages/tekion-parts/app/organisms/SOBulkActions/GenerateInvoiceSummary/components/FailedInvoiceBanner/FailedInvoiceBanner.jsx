import React, { memo } from 'react';

import cx from 'classnames';
import PropTypes from 'prop-types';

import { Content } from '@tekion/tekion-components/src/atoms';
import Icon from '@tekion/tekion-components/src/atoms/FontIcon';

import styles from './failedInvoiceBanner.module.scss';

const FailedInvoiceBanner = props => {
  const { failedInvoicesCount } = props;

  if (!failedInvoicesCount || failedInvoicesCount === 0) return null;

  return (
    <div className={cx(styles.failedInvoiceContainer, 'd-flex align-items-center')}>
      <div className={cx('d-flex align-items-center')}>
        <Icon>icon-info</Icon>
        <Content className={cx('p-l-20')}>
          {__("{{count}} Invoice(s) couldn't be generated", { count: failedInvoicesCount })}
        </Content>
      </div>
    </div>
  );
};

FailedInvoiceBanner.propTypes = {
  failedInvoicesCount: PropTypes.number,
};

FailedInvoiceBanner.defaultProps = {
  failedInvoicesCount: 0,
};

export default memo(FailedInvoiceBanner);
