import React, { memo } from 'react';

import _get from 'lodash/get';

import cx from 'classnames';
import PropTypes from 'prop-types';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import { Content } from '@tekion/tekion-components/src/atoms';
import Icon, { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';

import COLORS from '@tekion/tekion-styles-next/scss/exports.scss';

import { BULK_STATUS_VS_INVOICE_STATUS_CONFIG } from '../../generateInvoiceSummary.constants';

const InvoiceStatusRenderer = props => {
  const { rowData } = props;

  const status = _get(rowData, 'status');

  const statusConfig = BULK_STATUS_VS_INVOICE_STATUS_CONFIG[status] || {
    icon: 'icon-clocked-in',
    color: COLORS.ashGray,
    label: status || __('Unknown'),
  };

  return (
    <div className={cx('d-flex align-items-center')}>
      <Icon className="p-r-8" color={statusConfig.color} size={SIZES.MD_S}>
        {statusConfig.icon}
      </Icon>
      <Content>{statusConfig.label}</Content>
    </div>
  );
};

InvoiceStatusRenderer.propTypes = {
  rowData: PropTypes.object,
};

InvoiceStatusRenderer.defaultProps = {
  rowData: EMPTY_OBJECT,
};

export default memo(InvoiceStatusRenderer);
