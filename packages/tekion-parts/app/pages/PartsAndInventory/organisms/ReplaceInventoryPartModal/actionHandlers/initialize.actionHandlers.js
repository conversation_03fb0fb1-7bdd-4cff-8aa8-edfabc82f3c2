import _get from 'lodash/get';
import _head from 'lodash/head';
import _isFunction from 'lodash/isFunction';
import _isNil from 'lodash/isNil';
import _last from 'lodash/last';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { getErrorMessage } from '@tekion/tekion-base/utils/errorUtils';

import { fetchInventoryPartMetaDataByPartIds } from '@tekion/tekion-business/src/appServices/parts/partsAndInventory/actions/inventoryPart.actions';
import PartBinMappingDomain from '@tekion/tekion-business/src/appServices/parts/partsAndInventory/domain/PartBinMapping.domain';
import inventoryPartReader from '@tekion/tekion-business/src/appServices/parts/partsAndInventory/readers/inventoryPart.reader';

import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';

import { DEFAULT_PART_BRAND_CODE } from 'constants/brand.constants';

import generalSettingsReader from 'readers/generalSettings.reader';

import { getPartCommentDisplayValue } from 'shared/PartsInventory/helpers';

import partsEnv from 'utils/partsEnv';

import { PART_REPLACEMENT_PAGE_SOURCE_TYPE } from '../../../domain/PartSupersessions.domain';
import { REPLACE_PART_FORM_KEYS } from '../constants/form.constants';

import { setPartDetailsToForm } from './common.actionHandlers';

export const INITIALIZE_ACTION_TYPES = {
  INIT_FORM: 'INIT_FORM',
};

const getPartSettingsInitialFormValues = () => {
  const partsSettings = _get(partsEnv, 'partsSettings');
  const allowTransferSourceCode = generalSettingsReader.transferSourceCode(partsSettings);
  const allowTransferBins = generalSettingsReader.transferBins(partsSettings);
  const allowTransferOnHand = generalSettingsReader.transferOnHand(partsSettings);
  const allowTransferCustomizedPriceOverride = generalSettingsReader.transferCustomizedPriceOverride(partsSettings);
  const allowTransferPartLevelComments = generalSettingsReader.transferPartLevelComments(partsSettings);
  return {
    [REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_ON_HAND]: allowTransferOnHand,
    [REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_BINS]: allowTransferBins,
    [REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_CUSTOMIZED_PRICE_OVERRIDE]: allowTransferCustomizedPriceOverride,
    [REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_PART_LEVEL_COMMENTS]: allowTransferPartLevelComments,
    [REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_SOURCE_CODE]: allowTransferSourceCode,
  };
};

const initializeFormWithInventorySourceType = ({ getState, setState }) => {
  const { currentInventoryPartDetails } = getState() || EMPTY_OBJECT;
  const partSettingFormValues = getPartSettingsInitialFormValues();
  const {
    [REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_BINS]: allowTransferBins,
    [REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_SOURCE_CODE]: allowTransferSourceCode,
  } = partSettingFormValues;
  const sourceCodeId = inventoryPartReader.sourceCodeId(currentInventoryPartDetails);
  const partBinMappings = inventoryPartReader.partBinMappings(currentInventoryPartDetails);
  const primaryBinId = PartBinMappingDomain.getPrimaryBinId(partBinMappings);
  setState({
    isLoading: false,
    isFormDisabled: false,
    values: {
      ...partSettingFormValues,
      ...(allowTransferSourceCode && {
        [REPLACE_PART_FORM_KEYS.SOURCE_CODE_ID]: _isNil(sourceCodeId) ? null : [sourceCodeId],
      }),
      ...(allowTransferBins && {
        [REPLACE_PART_FORM_KEYS.PRIMARY_BIN_ID]: _isNil(primaryBinId) ? null : [primaryBinId],
      }),
      [REPLACE_PART_FORM_KEYS.BRAND_CODE]: DEFAULT_PART_BRAND_CODE,
      [REPLACE_PART_FORM_KEYS.PART_COMMENTS]: getPartCommentDisplayValue(currentInventoryPartDetails),
    },
  });
};

const initializeFormWithSupersessionSourceType = async ({ getState, setState }) => {
  const { newPartId, oldPartId } = getState();
  try {
    const partList = await fetchInventoryPartMetaDataByPartIds([oldPartId, newPartId]); // sushma to check with jay , how to get isAvailable in master fields and decide on what to use
    const currentInventoryPartDetails = _head(partList);
    const selectedPartDetails = _last(partList);
    const partSettingFormValues = getPartSettingsInitialFormValues();

    setState(
      {
        currentInventoryPartDetails,
        isLoading: false,
        isSubmitting: false,
        values: partSettingFormValues,
      },
      () => {
        setPartDetailsToForm({
          getState,
          setState,
          params: { selectedPartDetails },
        });
      },
    );
  } catch (error) {
    setState({ isLoading: false });
    toaster(
      TOASTER_TYPE.ERROR,
      getErrorMessage(error, __('Failed to initialize replace part, Please try again after sometime')),
    );
  }
};

const PAGE_SOURCE_TYPE_VS_FORM_INITIALIZER = {
  [PART_REPLACEMENT_PAGE_SOURCE_TYPE.INVENTORY]: initializeFormWithInventorySourceType,
  [PART_REPLACEMENT_PAGE_SOURCE_TYPE.PART_SUPERSESSION]: initializeFormWithSupersessionSourceType,
};

const handleModalInit = async ({ getState, setState, params }) => {
  setState({ isLoading: true, isFormDisabled: true });
  const { pageSourceType } = getState() || EMPTY_OBJECT;
  const initializerFunction = PAGE_SOURCE_TYPE_VS_FORM_INITIALIZER[pageSourceType];
  if (_isFunction(initializerFunction)) {
    initializerFunction({ getState, setState, params });
    return;
  }
  setState({
    isLoading: false,
    isFormDisabled: false,
    values: {
      ...getPartSettingsInitialFormValues(),
    },
    errors: EMPTY_OBJECT,
  });
};

export const INITIALIZE_ACTION_HANDLERS = {
  [INITIALIZE_ACTION_TYPES.INIT_FORM]: handleModalInit,
};
