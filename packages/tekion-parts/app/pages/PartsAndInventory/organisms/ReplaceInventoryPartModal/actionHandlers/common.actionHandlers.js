import _get from 'lodash/get';
import _head from 'lodash/head';
import _isNil from 'lodash/isNil';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import SourceCodeReader from '@tekion/tekion-base/readers/PartsSourceCode';

import InventoryPartDomain from '@tekion/tekion-business/src/appServices/parts/partsAndInventory/domain/InventoryPart.domain';
import inventoryPartReader from '@tekion/tekion-business/src/appServices/parts/partsAndInventory/readers/inventoryPart.reader';

import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';

import { DEFAULT_PART_BRAND_CODE } from 'constants/brand.constants';

import { checkForDuplicatePartAction } from 'shared/PartsInventory/helperActions';

import { REPLACE_PART_FORM_KEYS, FORM_MESSAGES } from '../constants/form.constants';
import { getPartFormValues } from '../helpers/form.helpers';
import { getShouldConsiderSourceCodePartPrefix } from '../helpers/general.helpers';

const handlePartDuplicationAfterSettingResolvedPartDetails = async ({ getState, setState }) => {
  const { values, sourceCodeDetailsById, selectedPartDetails, pageSourceType } = getState();
  const {
    [REPLACE_PART_FORM_KEYS.PART_NUMBER]: partNumber,
    [REPLACE_PART_FORM_KEYS.SOURCE_CODE_ID]: sourceCodeId,
    [REPLACE_PART_FORM_KEYS.BRAND_CODE]: brandCode,
    [REPLACE_PART_FORM_KEYS.PART]: partId,
  } = values;
  const selectedSourceCodeId = _head(sourceCodeId);
  const partPrefix = SourceCodeReader.partPrefix(_get(sourceCodeDetailsById, selectedSourceCodeId));

  // check if part duplication needs to be checked or not with source code prefix
  const shouldCheckForPartDuplication = getShouldConsiderSourceCodePartPrefix({
    pageSourceType,
    partNumber,
    sourceCodeId: selectedSourceCodeId,
    partPrefix,
    selectedPartDetails,
  });

  // If resolved part number is same as sanitized number then no need to check for duplication
  if (!shouldCheckForPartDuplication) {
    setState({
      isFormDisabled: false,
      isLoading: false,
    });
    return;
  }

  const isDuplicatePart = await duplicatePartCheckHandler({
    getState,
    setState,
    params: {
      brandCode,
      partNumber,
      sourceCodeId: selectedSourceCodeId,
      sourceCodeDetailsById,
      currentSelectedPartId: _head(partId),
    },
  });

  if (!isDuplicatePart) {
    setState({
      isFormDisabled: false,
      isLoading: false,
    });
  }
};

const setInventoryPartDetailsToForm = ({ getState, setState, params }) => {
  const { values, currentInventoryPartDetails } = getState();
  const selectedPartDetails = params?.selectedPartDetails;
  const partFormValues = getPartFormValues({ ...params, values, currentInventoryPartDetails });
  setState({
    selectedPartDetails,
    errors: EMPTY_OBJECT,
    isFormDisabled: false,
    isLoading: false,
    values: {
      ...values,
      ...partFormValues,
    },
  });
};

const setMasterPartDetailsToForm = ({ getState, setState, params }) => {
  const { values, currentInventoryPartDetails } = getState();
  const selectedPartDetails = params?.selectedPartDetails;
  const partFormValues = getPartFormValues({ ...params, values, currentInventoryPartDetails });

  setState(
    {
      selectedPartDetails,
      errors: EMPTY_OBJECT,
      values: {
        ...values,
        ...partFormValues,
      },
    },
    () => {
      handlePartDuplicationAfterSettingResolvedPartDetails({ getState, setState });
    },
  );
};

const setNonMasterPartDetailsToForm = ({ getState, setState, params }) => {
  const { values, currentInventoryPartDetails } = getState();
  const newPartFieldOption = params?.newPartFieldOption;
  const selectedPartDetails = params?.selectedPartDetails;
  const partFormValues = getPartFormValues({ ...params, values, currentInventoryPartDetails });
  setState(
    {
      selectedPartDetails,
      errors: EMPTY_OBJECT,
      values: {
        ...values,
        ...partFormValues,
        [REPLACE_PART_FORM_KEYS.PART]: [newPartFieldOption?.value],
        [REPLACE_PART_FORM_KEYS.PART_NUMBER]: newPartFieldOption?.value,
        [REPLACE_PART_FORM_KEYS.BRAND_CODE]: DEFAULT_PART_BRAND_CODE,
      },
    },
    () => {
      handlePartDuplicationAfterSettingResolvedPartDetails({ getState, setState });
    },
  );
};

export const setPartDetailsToForm = ({ setState, getState, params }) => {
  const { selectedPartDetails } = params;

  if (InventoryPartDomain.isPartPresentInInventory(selectedPartDetails)) {
    setInventoryPartDetailsToForm({ getState, setState, params }); // check sushma why its refetching part id
    return;
  }

  if (inventoryPartReader.isAvailableInMaster(selectedPartDetails)) {
    setMasterPartDetailsToForm({ getState, setState, params });
    return;
  }

  setNonMasterPartDetailsToForm({ getState, setState, params });
};

export const duplicatePartCheckHandler = async ({ setState, params, getState }) => {
  const { brandCode, subBrandCode, partNumber, currentSelectedPartId, sourceCodeId, sourceCodeDetailsById } =
    params || EMPTY_OBJECT;
  const sourceCodePartNumberPrefix = SourceCodeReader.partPrefix(_get(sourceCodeDetailsById, sourceCodeId));
  const { selectedWarehouse: warehouseId } = getState();

  try {
    setState({
      isFormDisabled: true,
    });
    const { isDuplicatePart } = await checkForDuplicatePartAction({
      brandCode,
      partNumber,
      sourceCodeId,
      sourceCodeDetailsById,
      currentSelectedPartId,
      isUsingOldWithPartSearch: false,
      subBrandCode,
      warehouseId,
    });
    const duplicatePartError = _isNil(sourceCodePartNumberPrefix)
      ? FORM_MESSAGES.DUPLICATE_PART
      : FORM_MESSAGES.DUPLICATE_PART_WITH_PREFIX;

    setState(({ values: currentValues }) => ({
      isFormDisabled: false,
      values: {
        ...currentValues,
        [REPLACE_PART_FORM_KEYS.IS_DUPLICATE_PART]: isDuplicatePart,
      },
      errors: {
        [REPLACE_PART_FORM_KEYS.PART]: isDuplicatePart ? duplicatePartError : undefined,
      },
    }));
    return isDuplicatePart;
  } catch {
    setState({
      isFormDisabled: false,
    });
    toaster(TOASTER_TYPE.ERROR, __('Failed to resolve part details in the selected brand'));
    return undefined;
  }
};
