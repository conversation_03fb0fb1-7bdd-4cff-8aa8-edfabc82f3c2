import _delay from 'lodash/delay';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { ES_REFETCH_DELAY } from '@tekion/tekion-base/constants/general';
import { formatPartName } from '@tekion/tekion-base/formatters/parts/general';
import { getErrorMessage } from '@tekion/tekion-base/utils/errorUtils';

import InventoryPartDomain from '@tekion/tekion-business/src/appServices/parts/partsAndInventory/domain/InventoryPart.domain';

import MULTI_LINGUAL_FORM_ACTION_HANDLERS from '@tekion/tekion-components/src/hoc/withMultiLingualForm/MultiLingualForm.actionHandlers';
import FORM_ACTION_TYPES from '@tekion/tekion-components/src/organisms/FormBuilder/constants/actionTypes';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import FORM_PAGE_ACTION_TYPES from '@tekion/tekion-components/src/pages/formPage/constants/actionTypes';

import { listPriceValidator } from 'helpers/validators/listPriceValidator';

import { replacePartAction } from '../../../actions/inventoryPart.actions';
import { PART_SUPERSESSION_ERROR_CODE } from '../../../domain/PartSupersessions.domain';
import { REPLACE_PART_FORM_KEYS } from '../constants/form.constants';
import { showPartAlreadyReplacedToaster, showCannotReplacePartToaster } from '../helpers/form.helpers';
import { getReplacePartApiPayload } from '../helpers/submitPayload.helpers';

import { FIELD_ON_CHANGE_HANDLERS } from './field.actionHandlers';

const defaultOnChangeHandler = ({ setState, params }) => {
  setState(({ values: prevValues }) => ({
    values: {
      ...prevValues,
      [params.id]: params.value,
    },
  }));
};

const handleChange = ({ getState, setState, params = EMPTY_OBJECT }) => {
  const { id } = params || EMPTY_OBJECT;
  const onChangeHandler = FIELD_ON_CHANGE_HANDLERS[id] || defaultOnChangeHandler;
  onChangeHandler({ setState, getState, params });
};

const handleOnFormSubmit = async ({ setState, getState }) => {
  const {
    onSubmit,
    values = EMPTY_OBJECT,
    onCancel: closeModal,
    pageSourceType,
    selectedPartDetails,
    currentInventoryPartDetails,
    selectedWarehouse,
    sourceCodeDetailsById,
  } = getState();
  setState({ isSubmitting: true });
  const payload = getReplacePartApiPayload({
    values,
    pageSourceType,
    selectedPartDetails,
    currentInventoryPartDetails,
    sourceCodeDetailsById,
    selectedWarehouse,
  });
  const partMetaData = {
    partNumber: payload?.replacePartNumber,
    partName: payload?.replacePartDescription,
    brandCode: payload.replaceBrandCode,
  };
  const isNewInventoryPart = !InventoryPartDomain.isPartPresentInInventory(selectedPartDetails);
  const partNumberName = _isEmpty(selectedPartDetails)
    ? formatPartName(partMetaData)
    : formatPartName(selectedPartDetails);
  // const replacementPartsResponse = await getReplacementPartsAction(_castArray(oldPartId)); // sushma check with jay on this

  replacePartAction(payload)
    .then(() => {
      const successMessage = isNewInventoryPart
        ? __('The part {{partNumberName}} has been successfully added and replaced the old part', { partNumberName })
        : __('Part has been replaced successfully with {{partNumberName}}', {
            partNumberName,
          });
      const toasterHeader = isNewInventoryPart ? __('Successfully added and replaced') : __('Successfully replaced');
      toaster(TOASTER_TYPE.SUCCESS, successMessage, EMPTY_OBJECT, toasterHeader);
      _delay(() => {
        onSubmit();
        setState({
          isSubmitting: false,
        });
      }, ES_REFETCH_DELAY); // sushma check with jay on this
    })
    .catch(error => {
      const errorCode = _get(error, 'data.errorCode');

      if (errorCode === PART_SUPERSESSION_ERROR_CODE.TIMEOUT) {
        toaster(TOASTER_TYPE.INFO, __('Part is being replaced, please wait for sometime')); // sushma check with jay on how to resolve this
        closeModal();
        return;
      }

      if (errorCode === PART_SUPERSESSION_ERROR_CODE.REPLACED_WITH_SAME_PART) {
        // sushma check with jay on error codes
        showPartAlreadyReplacedToaster(partNumberName);
        _delay(() => {
          onSubmit();
        }, ES_REFETCH_DELAY);
        return;
      }

      if (errorCode === PART_SUPERSESSION_ERROR_CODE.ALREADY_REPLACED) {
        showCannotReplacePartToaster(partNumberName);
        setState({
          isSubmitting: false,
        });
        return;
      }

      toaster(TOASTER_TYPE.ERROR, getErrorMessage(error, __("Part couldn't be replaced")));
    });
};

const getPriceChangeErrors = getState => {
  const { values } = getState();
  const { [REPLACE_PART_FORM_KEYS.LIST_PRICE]: listPrice } = values || EMPTY_OBJECT;
  if (_isNil(listPrice)) return EMPTY_OBJECT;
  const { message } = listPriceValidator(REPLACE_PART_FORM_KEYS.LIST_PRICE, listPrice, values);
  return {
    [REPLACE_PART_FORM_KEYS.LIST_PRICE]: message,
  };
};

const handleError = ({ setState, getState, params }) => {
  const { errors } = params;
  const priceChangeErrors = getPriceChangeErrors(getState);
  setState({
    errors: {
      ...errors,
      ...priceChangeErrors,
    },
  });
};

export const FORM_ACTION_HANDLERS = {
  ...MULTI_LINGUAL_FORM_ACTION_HANDLERS,
  [FORM_ACTION_TYPES.VALIDATION_SUCCESS]: handleError,
  [FORM_ACTION_TYPES.ON_FIELD_CHANGE]: handleChange,
  [FORM_PAGE_ACTION_TYPES.ON_FORM_SUBMIT]: handleOnFormSubmit,
};
