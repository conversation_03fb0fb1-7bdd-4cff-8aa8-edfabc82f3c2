import _castArray from 'lodash/castArray';
import _get from 'lodash/get';
import _head from 'lodash/head';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';
import _trim from 'lodash/trim';

import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import { getErrorMessage } from '@tekion/tekion-base/utils/errorUtils';
import { isNewOption } from '@tekion/tekion-base/utils/general';

import { fetchInventoryPartMetaDataByPartIds } from '@tekion/tekion-business/src/appServices/parts/partsAndInventory/actions/inventoryPart.actions';
import PartBinMappingDomain from '@tekion/tekion-business/src/appServices/parts/partsAndInventory/domain/PartBinMapping.domain';
import inventoryPartReader from '@tekion/tekion-business/src/appServices/parts/partsAndInventory/readers/inventoryPart.reader';

import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';

import { DEFAULT_PART_BRAND_CODE } from 'constants/brand.constants';

import { getDefaultSourceCodeOfManufacturerByBrandCode } from 'helpers/parts.helper';

import { fetchMasterAndInventoryListFromPartNumberV2 } from 'shared/PartsInventory/actions';
import { getPartDetailsFromPartInventoryList } from 'shared/PartsInventory/helpers';

import { REPLACE_PART_FORM_KEYS, FORM_MESSAGES } from '../constants/form.constants';
import { getShouldCheckForDuplicatePartOnSourceCodeChange } from '../helpers/general.helpers';

import { setPartDetailsToForm, duplicatePartCheckHandler } from './common.actionHandlers';

const handleTransferBins = ({ getState, setState, params }) => {
  const { id, value } = params;
  const { values, currentInventoryPartDetails } = getState();
  const partBinMappings = inventoryPartReader.partBinMappings(currentInventoryPartDetails);
  const oldPartPrimaryBinId = PartBinMappingDomain.getPrimaryBinId(partBinMappings);

  setState({
    values: {
      ...values,
      [id]: value,
      ...(value && {
        [REPLACE_PART_FORM_KEYS.PRIMARY_BIN_ID]: _isNil(oldPartPrimaryBinId) ? null : _castArray(oldPartPrimaryBinId),
      }),
    },
  });
};

const handlePrimaryBinChange = ({ getState, setState, params }) => {
  const { id, value } = params;
  const { values, currentInventoryPartDetails } = getState();
  const { [REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_BINS]: allowTransferBins } = values || EMPTY_OBJECT;
  const partBinMappings = inventoryPartReader.partBinMappings(currentInventoryPartDetails);
  const oldPartPrimaryBinId = PartBinMappingDomain.getPrimaryBinId(partBinMappings);
  setState({
    values: {
      ...values,
      [id]: value,
      ...(allowTransferBins &&
        oldPartPrimaryBinId !== _head(value) && {
          [REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_BINS]: false,
        }),
    },
  });
};

const handleTransferSourceCode = ({ getState, setState, params }) => {
  const { id, value } = params;
  const { values, currentInventoryPartDetails } = getState();
  const oldPartSourceCodeId = inventoryPartReader.sourceCodeId(currentInventoryPartDetails);
  const currentSelectedSourceCodeId = _get(values, [REPLACE_PART_FORM_KEYS.SOURCE_CODE_ID, 0]);
  const sourceCodeIdToSet = _isNil(oldPartSourceCodeId) ? null : _castArray(oldPartSourceCodeId);
  setState(
    {
      values: {
        ...values,
        [id]: value,
        ...(value && {
          [REPLACE_PART_FORM_KEYS.SOURCE_CODE_ID]: _isNil(oldPartSourceCodeId) ? null : _castArray(oldPartSourceCodeId),
        }),
      },
    },
    () => {
      if (oldPartSourceCodeId !== currentSelectedSourceCodeId) {
        handleSourceCodeChange({
          getState,
          setState,
          params: {
            value: sourceCodeIdToSet,
            id: REPLACE_PART_FORM_KEYS.SOURCE_CODE_ID,
          },
        });
      }
    },
  );
};

const handleCreateNewPart = async ({ params, setState, getState }) => {
  const { values } = getState();
  const { id, value, option } = params || EMPTY_OBJECT;
  const partNumber = _trim(_head(value));
  const brandCode = DEFAULT_PART_BRAND_CODE;

  // set form to disabled state while fetching master/inventory part details
  setState({ isFormDisabled: true });
  try {
    const { partInventoryList = EMPTY_ARRAY } = await fetchMasterAndInventoryListFromPartNumberV2({
      partNumber,
      brandCode,
    });
    const inventoryPartDetails = getPartDetailsFromPartInventoryList({
      partInventoryList,
      partNumber,
      brandCode,
      isUsingOldWithPartSearch: false,
    });

    if (!_isEmpty(inventoryPartDetails)) {
      setState({
        isFormDisabled: false,
        values: {
          ...values,
          [id]: value,
          [REPLACE_PART_FORM_KEYS.BRAND_CODE]: brandCode,
          [REPLACE_PART_FORM_KEYS.PART_NUMBER]: partNumber,
          [REPLACE_PART_FORM_KEYS.COST_PRICE]: null,
          [REPLACE_PART_FORM_KEYS.LIST_PRICE]: null,
          [REPLACE_PART_FORM_KEYS.PART_NAME]: null,
          [REPLACE_PART_FORM_KEYS.IS_DUPLICATE_PART]: true,
          [REPLACE_PART_FORM_KEYS.IS_PART_AVAILABLE_IN_MASTER]: false,
        },
        errors: {
          [REPLACE_PART_FORM_KEYS.PART]: FORM_MESSAGES.DUPLICATE_PART,
        },
      });
      return;
    }

    setPartDetailsToForm({
      getState,
      setState,
      params: {
        newPartFieldOption: option,
        selectedPartDetails: EMPTY_OBJECT,
      },
    });
  } catch (error) {
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(error, __('Failed to fetch part details')));
    setState({ isFormDisabled: false });
  }
};

const handleBrandCodeChange = ({ getState, setState, params }) => {
  const { option } = params;
  const brandCode = _get(option, 'value');
  const { values, selectedPartDetails, sourceCodeDetailsById, currentInventoryPartDetails, manufacturerConfig } =
    getState();
  const currentPartSourceCodeId = inventoryPartReader.sourceCodeId(currentInventoryPartDetails);
  const currentBrandCode = _get(values, REPLACE_PART_FORM_KEYS.BRAND_CODE);

  const partNumber = _get(values, REPLACE_PART_FORM_KEYS.PART_NUMBER);
  const sourceCodeId = getDefaultSourceCodeOfManufacturerByBrandCode({
    brandCode,
    manufacturerConfig,
  });

  // If user selects the same brand do nothing, else we need to check for part number duplication always
  if (currentBrandCode === brandCode) return;

  setState(
    {
      selectedPartDetails: EMPTY_OBJECT,
      isFormDisabled: true,
      errors: undefined,
      values: {
        ...values,
        [REPLACE_PART_FORM_KEYS.BRAND_CODE]: brandCode,
        [REPLACE_PART_FORM_KEYS.SOURCE_CODE_ID]: _isNil(sourceCodeId) ? null : _castArray(sourceCodeId),
        ...(currentPartSourceCodeId !== sourceCodeId &&
          !_isNil(sourceCodeId) && {
            [REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_SOURCE_CODE]: false,
          }),
      },
    },
    async () => {
      const currentSelectedPartId = inventoryPartReader.partId(selectedPartDetails);
      const isDuplicatePart = await duplicatePartCheckHandler({
        getState,
        setState,
        params: {
          brandCode,
          partNumber,
          sourceCodeId,
          sourceCodeDetailsById,
          currentSelectedPartId,
        },
      });
      if (!isDuplicatePart) {
        setState({
          isFormDisabled: false,
        });
      }
    },
  );
};

/**
 * If selected replacement part is not in inventory
 *    1. If transfer source code and primary bin is true -> set these values with source code and bin of part to be replaced
 *    2. If the boolean is false -> then do not set these fields
 * If selected replacement part is present in inventory ( active or inactive)
 *    1. If boolean is set to true -> override the bin and source code id with part to be replaced
 *    2. If boolean is false -> then keep the selected replacement part bin and source code details
 */
const handlePartChange = async ({ params, getState, setState }) => {
  const { id, value, option } = params || EMPTY_OBJECT;
  const { values, currentInventoryPartDetails } = getState();
  const currentPartId = inventoryPartReader.partId(currentInventoryPartDetails);

  if (currentPartId === _head(value)) {
    toaster(TOASTER_TYPE.ERROR, __('Self replacement is not allowed'));
    return;
  }

  setState(
    {
      isFormDisabled: true,
      values: {
        ...values,
        [id]: value,
      },
    },
    async () => {
      if (isNewOption(option)) {
        handleCreateNewPart({ getState, setState, params });
        return;
      }

      try {
        const response = await fetchInventoryPartMetaDataByPartIds(value);
        const selectedPartDetails = _head(_get(response, 'hits'));

        setPartDetailsToForm({
          getState,
          setState,
          params: {
            selectedPartDetails,
          },
        });
      } catch (error) {
        toaster(TOASTER_TYPE.ERROR, getErrorMessage(error, __('Failed to fetch part details')));
        setState({ isFormDisabled: false });
      }
    },
  );
};

const handleSourceCodeChange = async ({ getState, setState, params }) => {
  const { value } = params;
  const { values, sourceCodeDetailsById, pageSourceType, currentInventoryPartDetails, selectedPartDetails } =
    getState();
  const currentPartSourceCodeId = inventoryPartReader.sourceCodeId(currentInventoryPartDetails);
  const selectedSourceCodeId = _head(value);
  const { [REPLACE_PART_FORM_KEYS.PART_NUMBER]: partNumber, [REPLACE_PART_FORM_KEYS.BRAND_CODE]: brandCode } =
    values || EMPTY_OBJECT;
  const isSourceCodeDifferentFromOldPart =
    currentPartSourceCodeId !== selectedSourceCodeId && !_isNil(selectedSourceCodeId);

  // check if part duplication needs to be checked or not with source code prefix
  const shouldCheckForPartDuplication = getShouldCheckForDuplicatePartOnSourceCodeChange({
    pageSourceType,
    selectedPartDetails,
  });

  setState(
    {
      ...(shouldCheckForPartDuplication && {
        isFormDisabled: true,
        errors: undefined,
      }),
      values: {
        ...values,
        [REPLACE_PART_FORM_KEYS.SOURCE_CODE_ID]: value,
        ...(isSourceCodeDifferentFromOldPart && {
          [REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_SOURCE_CODE]: false,
        }),
      },
    },
    async () => {
      if (!shouldCheckForPartDuplication) return;
      const currentSelectedPartId = inventoryPartReader.partId(selectedPartDetails);
      const isDuplicatePart = await duplicatePartCheckHandler({
        getState,
        setState,
        params: {
          brandCode,
          partNumber,
          sourceCodeId: selectedSourceCodeId,
          sourceCodeDetailsById,
          currentSelectedPartId,
        },
      });
      if (!isDuplicatePart) {
        setState({
          isFormDisabled: false,
        });
      }
    },
  );
};

export const FIELD_ON_CHANGE_HANDLERS = {
  [REPLACE_PART_FORM_KEYS.PART]: handlePartChange,
  [REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_BINS]: handleTransferBins,
  [REPLACE_PART_FORM_KEYS.PRIMARY_BIN_ID]: handlePrimaryBinChange,
  [REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_SOURCE_CODE]: handleTransferSourceCode,
  [REPLACE_PART_FORM_KEYS.BRAND_CODE]: handleBrandCodeChange,
  [REPLACE_PART_FORM_KEYS.SOURCE_CODE_ID]: handleSourceCodeChange,
};
