import InventoryPartDomain from '@tekion/tekion-business/src/appServices/parts/partsAndInventory/domain/InventoryPart.domain';

import { getSanitizedPartNumberDetailsWithSourceCodePartPrefix } from 'shared/PartsInventory/helpers';

import { isSourceCodePartPrefixEnabled } from 'utils/general';

import { PART_REPLACEMENT_PAGE_SOURCE_TYPE } from '../../../domain/PartSupersessions.domain';

export const isPartSupersessionPageSourceType = pageSourceType =>
  pageSourceType === PART_REPLACEMENT_PAGE_SOURCE_TYPE.PART_SUPERSESSION;

export const getShouldConsiderSourceCodePartPrefix = ({ sourceType, partNumber, partPrefix, selectedPartDetails }) => {
  if (
    isPartSupersessionPageSourceType(sourceType) ||
    InventoryPartDomain.isPartPresentInInventory(selectedPartDetails)
  ) {
    return false;
  }
  const { isPartNumberPrefixedWithSourceCodePartPrefix } = getSanitizedPartNumberDetailsWithSourceCodePartPrefix({
    partNumber,
    sourceCodePartNumberPrefix: partPrefix,
  });

  return !isPartNumberPrefixedWithSourceCodePartPrefix;
};

export const getShouldCheckForDuplicatePartOnSourceCodeChange = ({ sourceType, selectedPartDetails }) => {
  if (
    isPartSupersessionPageSourceType(sourceType) ||
    InventoryPartDomain.isPartPresentInInventory(selectedPartDetails)
  ) {
    return false;
  }
  return isSourceCodePartPrefixEnabled();
};
