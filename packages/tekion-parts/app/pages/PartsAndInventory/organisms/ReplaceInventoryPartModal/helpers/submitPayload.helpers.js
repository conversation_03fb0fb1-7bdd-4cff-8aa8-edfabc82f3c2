import _get from 'lodash/get';
import _head from 'lodash/head';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import SourceCodeReader from '@tekion/tekion-base/readers/PartsSourceCode';

import InventoryPartDomain from '@tekion/tekion-business/src/appServices/parts/partsAndInventory/domain/InventoryPart.domain';
import inventoryPartReader from '@tekion/tekion-business/src/appServices/parts/partsAndInventory/readers/inventoryPart.reader';

import {
  getSanitizedPartNumberDetailsWithSourceCodePartPrefix,
  getSanitizedDmsPartNumber,
} from 'shared/PartsInventory/helpers';

import { getIsMultiWarehouseEnabled, isMultilingualEnabled } from 'utils/general';
import { multilingualValuesToParserWithDealerConfig } from 'utils/multiLingualUtils';

import { REPLACE_PART_FORM_KEYS, FORM_FIELD_VS_LANGUAGE_PAYLOAD_KEY } from '../constants/form.constants';

import { getShouldConsiderSourceCodePartPrefix, isPartSupersessionPageSourceType } from './general.helpers';

const getSanitizedPartMetaDataForSavePayload = ({ values, selectedPartDetails, sourceType, sourceCodeDetailsById }) => {
  const {
    [REPLACE_PART_FORM_KEYS.PART_NUMBER]: replacePartNumber,
    [REPLACE_PART_FORM_KEYS.IS_PART_AVAILABLE_IN_MASTER]: isPartAvailableInMaster,
    [REPLACE_PART_FORM_KEYS.SOURCE_CODE_ID]: replaceSourceCodeId,
  } = values || EMPTY_OBJECT;
  const sourceCodeDetails = _get(sourceCodeDetailsById, replaceSourceCodeId);
  const sourceCodePartNumberPrefix = SourceCodeReader.partPrefix(sourceCodeDetails);
  const shouldConsiderPartPrefix = getShouldConsiderSourceCodePartPrefix({
    sourceType,
    partNumber: replacePartNumber,
    partPrefix: sourceCodePartNumberPrefix,
    selectedPartDetails,
  });

  if (shouldConsiderPartPrefix) {
    const { sanitizedPartNumber } = getSanitizedPartNumberDetailsWithSourceCodePartPrefix({
      partNumber: replacePartNumber,
      sourceCodePartNumberPrefix,
    });
    const sanitizedDmsPartNumber = getSanitizedDmsPartNumber({
      partMasterDetails: selectedPartDetails,
      isPartAvailableInMaster,
      partNumber: replacePartNumber,
      sourceCodePartNumberPrefix,
    });
    return {
      sanitizedPartNumber,
      sanitizedPartId: null,
      sanitizedDmsPartNumber,
    };
  }

  return {
    sanitizedPartId: inventoryPartReader.partId(selectedPartDetails),
    sanitizedPartNumber: replacePartNumber,
    sanitizedDmsPartNumber: inventoryPartReader.dmsPartNumber(selectedPartDetails), // sushma check with jay and remove the usage of this
    partSourceType: inventoryPartReader.partSourceType(selectedPartDetails), // sushma check can this be determined in BE instead of UI sending it
  };
};

export const getReplacePartApiPayload = ({
  values,
  sourceType,
  selectedPartDetails,
  currentInventoryPartDetails,
  sourceCodeDetailsById,
  selectedWarehouse,
}) => {
  const {
    [REPLACE_PART_FORM_KEYS.PART_NAME]: replacePartDescription,
    [REPLACE_PART_FORM_KEYS.COST_PRICE]: replaceCost,
    [REPLACE_PART_FORM_KEYS.LIST_PRICE]: replaceListPrice,
    [REPLACE_PART_FORM_KEYS.BRAND_CODE]: brandCode,
    [REPLACE_PART_FORM_KEYS.SOURCE_CODE_ID]: replaceSourceCodeId,
    [REPLACE_PART_FORM_KEYS.PRIMARY_BIN_ID]: primaryBinId,
    [REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_SOURCE_CODE]: transferSourceCode,
    [REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_BINS]: transferBins,
    [REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_ON_HAND]: transferOnHand,
    [REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_CUSTOMIZED_PRICE_OVERRIDE]: transferCustomizedPriceOverride,
    [REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_PART_LEVEL_COMMENTS]: transferPartLevelComments,
  } = values || EMPTY_OBJECT;

  const oldPartId = inventoryPartReader.partId(currentInventoryPartDetails);
  const isNewInventoryPart = !InventoryPartDomain.isPartPresentInInventory(selectedPartDetails);

  const { sanitizedPartNumber, sanitizedPartId, sanitizedDmsPartNumber, partSourceType } =
    getSanitizedPartMetaDataForSavePayload({ values, selectedPartDetails, sourceType, sourceCodeDetailsById });
  return {
    oldPartId,
    partSourceType,
    replacePartId: sanitizedPartId,
    replacePartNumber: sanitizedPartNumber,
    replaceDmsPartNumber: sanitizedDmsPartNumber, // sushma:  this should not be sent BE should calculate check with jay on entire payload
    replaceBrandCode: brandCode,
    replacePartDescription,
    replaceCost,
    replaceListPrice,
    replaceSourceCodeId: _head(replaceSourceCodeId),
    replaceBinId: _head(primaryBinId),
    transferSourceCode,
    transferBins,
    transferOnHand,
    transferCustomizedPriceOverride,
    transferPartLevelComments,
    newPart: isNewInventoryPart,
    ...(isMultilingualEnabled()
      ? {
          replacePartLanguages: multilingualValuesToParserWithDealerConfig({
            entity: values,
            formKeyToEntityKeyMap: FORM_FIELD_VS_LANGUAGE_PAYLOAD_KEY,
          }),
        }
      : EMPTY_OBJECT),
    // partSupersessionChange is used to track wether the replacement is done from inventory details screen or part replacement screen
    partSupersessionChange: isPartSupersessionPageSourceType(sourceType),
    ...(getIsMultiWarehouseEnabled() ? { warehouseId: selectedWarehouse } : EMPTY_OBJECT),
  };
};
