import _castArray from 'lodash/castArray';
import _isNil from 'lodash/isNil';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import { multilingualValuesFromParser } from '@tekion/tekion-business/src/appServices/parts/helpers/multiLingual.helper';
import PartBinMappingDomain from '@tekion/tekion-business/src/appServices/parts/partsAndInventory/domain/PartBinMapping.domain';
import inventoryPartReader from '@tekion/tekion-business/src/appServices/parts/partsAndInventory/readers/inventoryPart.reader';

import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';

import { getPartCommentDisplayValue } from 'shared/PartsInventory/helpers';

import { getLocaleValue } from 'utils/multiLingualUtils';

import { REPLACE_PART_FORM_KEYS, FORM_FIELD_VS_LANGUAGE_PAYLOAD_KEY } from '../constants/form.constants';

export const showPartAlreadyReplacedToaster = partName => {
  toaster(
    TOASTER_TYPE.SUCCESS,
    __('This old part {{partName}} has been already replaced with the new part.', {
      partName,
    }),
    EMPTY_OBJECT,
    __('Part Successfully Replaced'),
  );
};

export const getPartFormValues = ({ selectedPartDetails, currentInventoryPartDetails, values }) => {
  const {
    [REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_BINS]: allowTransferBins,
    [REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_SOURCE_CODE]: allowTransferSourceCode,
  } = values || EMPTY_OBJECT;
  const currentPartSourceCodeId = inventoryPartReader.sourceCodeId(currentInventoryPartDetails);
  const currentPartBinMappings = inventoryPartReader.partBinMappings(currentInventoryPartDetails);
  const currentPartPrimaryBinId = PartBinMappingDomain.getPrimaryBinId(currentPartBinMappings);

  const selectedPartSourceCodeId = inventoryPartReader.sourceCodeId(selectedPartDetails);
  const selectedPartBinMappings = inventoryPartReader.partBinMappings(selectedPartDetails);
  const selectedPartPrimaryBinId = PartBinMappingDomain.getPrimaryBinId(selectedPartBinMappings);

  const partId = inventoryPartReader.partId(selectedPartDetails);
  const languages = multilingualValuesFromParser({
    entity: selectedPartDetails,
    formKeyToEntityKeyMap: FORM_FIELD_VS_LANGUAGE_PAYLOAD_KEY,
  });

  return {
    [REPLACE_PART_FORM_KEYS.PART_COMMENTS]: getPartCommentDisplayValue(currentInventoryPartDetails),
    [REPLACE_PART_FORM_KEYS.PART]: _isNil(partId) ? null : _castArray(partId),
    [REPLACE_PART_FORM_KEYS.PART_NAME]: getLocaleValue({
      entity: selectedPartDetails,
      entityKeyReader: inventoryPartReader.partName,
    }),
    [REPLACE_PART_FORM_KEYS.PART_NUMBER]: inventoryPartReader.partNumber(selectedPartDetails),
    [REPLACE_PART_FORM_KEYS.COST_PRICE]: inventoryPartReader.cost(selectedPartDetails),
    [REPLACE_PART_FORM_KEYS.LIST_PRICE]: inventoryPartReader.listPrice(selectedPartDetails),
    [REPLACE_PART_FORM_KEYS.BRAND_CODE]: inventoryPartReader.brandCode(selectedPartDetails),
    [REPLACE_PART_FORM_KEYS.IS_PART_AVAILABLE_IN_MASTER]: inventoryPartReader.isAvailableInMaster(selectedPartDetails),
    [REPLACE_PART_FORM_KEYS.SOURCE_CODE_ID]: _isNil(selectedPartSourceCodeId)
      ? null
      : _castArray(selectedPartSourceCodeId),
    [REPLACE_PART_FORM_KEYS.PRIMARY_BIN_ID]: _isNil(selectedPartPrimaryBinId)
      ? null
      : _castArray(selectedPartPrimaryBinId),
    ...(allowTransferSourceCode && {
      [REPLACE_PART_FORM_KEYS.SOURCE_CODE_ID]: _isNil(currentPartSourceCodeId)
        ? null
        : _castArray(currentPartSourceCodeId),
    }),
    ...(allowTransferBins && {
      [REPLACE_PART_FORM_KEYS.PRIMARY_BIN_ID]: _isNil(currentPartPrimaryBinId)
        ? null
        : _castArray(currentPartPrimaryBinId),
    }),
    isDuplicatePart: false,
    languages,
  };
};

export const showCannotReplacePartToaster = () => {
  toaster(
    TOASTER_TYPE.ERROR,
    __(
      'Part is already been replaced from Inventory. You can only do defer or unlink part from inventory screen and then come do replacement.',
    ),
    EMPTY_OBJECT,
    __("Can't be Replaced"),
  );
};
