import React, { useEffect, useMemo, useCallback } from 'react';

import { connect } from 'react-redux';

import _noop from 'lodash/noop';

import PropTypes from 'prop-types';
import { compose } from 'recompose';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import TEnvReader from '@tekion/tekion-base/readers/Env';

import Modal from '@tekion/tekion-components/src/molecules/Modal';
import Spinner from '@tekion/tekion-components/src/molecules/SpinnerComponent';
import { FormWithSubmission } from '@tekion/tekion-components/src/pages/formPage';
import { triggerSubmit } from '@tekion/tekion-components/src/pages/formPage/utils/formAction';

import { useSubAppAnalytics } from '@tekion/tekion-widgets/src/utils/analytics/hooks';

import withActions from 'connectors/withActions';

import { MODAL_PRIMARY_ACTION_TOOLTIP_PROPS } from 'constants/general.constants';

import { getPartsAnalyticsAdditionalPayload } from 'helpers/globalAnalytics.helpers';

import WithShortcut from 'molecules/WithShortcut';

import { getLocations, getManufacturers, getSourceCodesByIds } from 'pages/base/app.selectors';

import { getLanguageOptions } from 'utils/multiLingualUtils';
import partsEnv from 'utils/partsEnv';
import { getDisabledModalButtonProps } from 'utils/partsUtil';

import { ACTION_HANDLERS, ACTION_TYPES } from './actionHandlers';
import { createSelectorForFormFields } from './config/replaceInventoryPartModal.fields';
import { createSelectorForFormSections } from './config/replaceInventoryPartModal.sections';
import { FORM_CONTEXT_ID } from './constants/form.constants';

const Body = props => {
  const {
    onAction,
    values,
    errors,
    shouldLockPartNumber,
    locationDetailsById,
    sourceCodeDetailsById,
    isFormDisabled,
    isLoading,
    pageSourceType,
    multiLingualFormProps,
    multiLingualFieldDetails,
    selectedPartDetails,
    selectedWarehouse,
  } = props;

  useSubAppAnalytics({
    isLoading,
    ...getPartsAnalyticsAdditionalPayload('replace-inventory-part-modal'),
  });

  const { getFormFields, getFormSections } = useMemo(
    () => ({
      getFormFields: createSelectorForFormFields(),
      getFormSections: createSelectorForFormSections(),
    }),
    [],
  );

  return isLoading ? (
    <Spinner className="full-height full-width flex-center" />
  ) : (
    <FormWithSubmission
      contextId={FORM_CONTEXT_ID}
      onAction={onAction}
      values={values}
      errors={errors}
      fields={getFormFields({
        ...values,
        locationDetailsById,
        sourceCodeDetailsById,
        onAction,
        shouldLockPartNumber,
        pageSourceType,
        selectedPartDetails,
        selectedWarehouse,
      })}
      sections={getFormSections()}
      isLoading={isLoading}
      isFormDisabled={isFormDisabled}
      multiLingualFormProps={{
        ...multiLingualFormProps,
        languageOptions: getLanguageOptions(),
        currentLanguageId: TEnvReader.getCurrentLanguageId(),
      }}
      multiLingualFieldDetails={multiLingualFieldDetails}
    />
  );
};

const ReplaceInventoryPartModal = props => {
  const { onAction, isVisible, isSubmitting, isFormDisabled, onCancel, title, pageSourceType } = props;

  useEffect(() => {
    if (!isVisible) return;
    onAction({
      type: ACTION_TYPES.INIT_FORM,
      payload: { pageSourceType },
    });
  }, [pageSourceType, isVisible, onAction]);

  const triggerFormSubmit = useCallback(() => {
    triggerSubmit(FORM_CONTEXT_ID);
  }, []);

  return (
    <WithShortcut>
      <Modal
        visible={isVisible}
        title={title}
        onCancel={onCancel}
        submitBtnText={__('Submit')}
        onSubmit={triggerFormSubmit}
        width={Modal.SIZES.MD}
        primaryBtnTooltipProps={MODAL_PRIMARY_ACTION_TOOLTIP_PROPS}
        okButtonProps={getDisabledModalButtonProps(isSubmitting, isFormDisabled)}
        cancelButtonProps={getDisabledModalButtonProps(false, isSubmitting)}
        destroyOnClose
      >
        {isVisible ? <Body {...props} /> : null}
      </Modal>
    </WithShortcut>
  );
};

ReplaceInventoryPartModal.propTypes = {
  onAction: PropTypes.func,
  isVisible: PropTypes.bool,
  isSubmitting: PropTypes.bool,
  isFormDisabled: PropTypes.bool,
  isLoading: PropTypes.bool,
  onCancel: PropTypes.func,
  values: PropTypes.object,
  errors: PropTypes.object,
  locationDetailsById: PropTypes.object,
  sourceCodeDetailsById: PropTypes.object,
  title: PropTypes.string,
  pageSourceType: PropTypes.string.isRequired,
  multiLingualFormProps: PropTypes.object,
  multiLingualFieldDetails: PropTypes.object,
  shouldLockPartNumber: PropTypes.bool,
};

ReplaceInventoryPartModal.defaultProps = {
  onAction: _noop,
  isVisible: false,
  isSubmitting: false,
  isFormDisabled: false,
  isLoading: false,
  onCancel: _noop,
  values: EMPTY_OBJECT,
  errors: EMPTY_OBJECT,
  locationDetailsById: EMPTY_OBJECT,
  sourceCodeDetailsById: EMPTY_OBJECT,
  title: __('Add & Replace Part'),
  multiLingualFormProps: EMPTY_OBJECT,
  multiLingualFieldDetails: EMPTY_OBJECT,
  shouldLockPartNumber: false,
};

const mapStateToProps = state => ({
  locationDetailsById: getLocations(state),
  sourceCodeDetailsById: getSourceCodesByIds(state),
  manufacturerData: getManufacturers(state),
});

export default compose(
  connect(mapStateToProps),
  withActions({ selectedWarehouse: partsEnv.userPreferredWarehouseId }, ACTION_HANDLERS),
)(ReplaceInventoryPartModal);
