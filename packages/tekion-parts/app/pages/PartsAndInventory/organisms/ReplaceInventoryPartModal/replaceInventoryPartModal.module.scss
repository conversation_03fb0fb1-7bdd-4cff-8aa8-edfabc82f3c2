@import 'tstyles/component.scss';

.partCommentPopoverContent{
  @include content;
  padding: 1.2rem;
}

.partComment{
  margin-right: 0;
  align-items: flex-start;

  &Icon{
    height: 1.2rem;
    margin-top: -0.8rem;
    margin-left: 0 !important;
    color: $black;
  }

  &Wrapper{
    @include flex();
    margin-right: 4.6rem; // according to design
  }
}

.transferBin{
  @include flex($justify-content: flex-start);
  padding-left: 4rem;
  margin-bottom: 0;
  margin-right: 0;
}

.singleColumn{
  @include flex($justify-content: center);
  margin-left: 31rem;
}

.transferCustomizedPrice{
  margin-top: -2rem;
}

.transferPartLevel{
  margin-top: -1.2rem; // as per design need this
}

.binDetailsHeader {
  width: 13.2rem;
}

.additionalInfoHeader {
  width: 28.4rem;
}

.fieldLabel {
  white-space: normal;
}
