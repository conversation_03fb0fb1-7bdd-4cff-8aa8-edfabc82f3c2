export const FORM_CONTEXT_ID = 'REPLACE_INVENTORY_PART_FORM';

export const REPLACE_PART_FORM_KEYS = {
  PART: 'PART',
  PART_NAME: 'partName',
  PART_NUMBER: 'partNumber',
  COST_PRICE: 'costPrice',
  LIST_PRICE: 'listPrice',
  SOURCE_CODE_ID: 'sourceCodeId',
  PRIMARY_BIN_ID: 'primaryBinId',
  BRAND_CODE: 'brandCode',
  OEM_CODE: 'oemCode',
  ALLOW_TRANSFER_SOURCE_CODE: 'allowTransferSourceCode',
  ALLOW_TRANSFER_BINS: 'allowTransferBins',
  ALLOW_TRANSFER_ON_HAND: 'allowTransferOnHand',
  ALLOW_TRANSFER_CUSTOMIZED_PRICE_OVERRIDE: 'allowTransferCustomizedPriceOverride',
  ALLOW_TRANSFER_PART_LEVEL_COMMENTS: 'allowTransferPartLevelComments',
  BIN_DETAILS_HEADER: 'binDetailsHeader',
  ADDITIONAL_INFORMATION_HEADER: 'additionalInformationHeader',
  PART_COMMENTS: 'partComments',
  IS_DUPLICATE_PART: 'isDuplicatePart',
  IS_PART_AVAILABLE_IN_MASTER: 'isPartAvailableInMaster', // This is used in validators hence required in values
};

export const FORM_FIELD_VS_LANGUAGE_PAYLOAD_KEY = {
  [REPLACE_PART_FORM_KEYS.PART_NAME]: 'partName',
};

export const FORM_MESSAGES = {
  DUPLICATE_PART: __('Duplicate part number in the current brand'),
  DUPLICATE_PART_WITH_PREFIX: __('Duplicate part number with source code prefix in the current brand'),
};
