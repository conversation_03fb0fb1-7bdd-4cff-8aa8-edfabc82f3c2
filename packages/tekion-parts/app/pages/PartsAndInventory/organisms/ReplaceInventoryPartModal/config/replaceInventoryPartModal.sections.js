import _property from 'lodash/property';

import { createSelector } from 'reselect';

import { REPLACE_PART_FORM_KEYS } from '../constants/form.constants';
import styles from '../replaceInventoryPartModal.module.scss';

const getSections = () => [
  {
    className: 'is-paddingless',
    rows: [
      {
        columns: [REPLACE_PART_FORM_KEYS.PART, REPLACE_PART_FORM_KEYS.PART_NAME],
      },
      {
        columns: [REPLACE_PART_FORM_KEYS.COST_PRICE, REPLACE_PART_FORM_KEYS.LIST_PRICE],
      },
      {
        columns: [REPLACE_PART_FORM_KEYS.SOURCE_CODE_ID, REPLACE_PART_FORM_KEYS.BRAND_CODE],
      },
      {
        columns: [REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_SOURCE_CODE],
      },
      {
        className: 'm-b-16',
        columns: [
          REPLACE_PART_FORM_KEYS.BIN_DETAILS_HEADER,
          REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_BINS,
          REPLACE_PART_FORM_KEYS.ADDITIONAL_INFORMATION_HEADER,
        ],
      },
      {
        columns: [REPLACE_PART_FORM_KEYS.PRIMARY_BIN_ID, REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_ON_HAND],
      },
      {
        className: styles.singleColumn,
        columns: [REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_CUSTOMIZED_PRICE_OVERRIDE],
      },
      {
        className: styles.singleColumn,
        columns: [REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_PART_LEVEL_COMMENTS],
      },
    ],
  },
];

const FIELDS_TO_PICK_FOR_SECTION_CONFIG = [];

export const createSelectorForFormSections = () =>
  createSelector(FIELDS_TO_PICK_FOR_SECTION_CONFIG.map(_property), getSections);
