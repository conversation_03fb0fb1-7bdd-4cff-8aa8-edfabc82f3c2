import React from 'react';

import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';
import _property from 'lodash/property';
import _trim from 'lodash/trim';

import cx from 'classnames';
import { compose } from 'recompose';
import { createSelector } from 'reselect';
import colors from 'tstyles/exports.scss';

import { RESOURCE_TYPE } from '@tekion/tekion-base/bulkResolvers/constants/resourceType';
import SourceCodeReader from '@tekion/tekion-base/readers/PartsSourceCode';
import { isRequiredRule } from '@tekion/tekion-base/utils/formValidators';

import { siteFilterESPayloadSanitizer } from '@tekion/tekion-business/src/appServices/parts/helpers/dealerSiteFilter.helpers';

import Heading from '@tekion/tekion-components/src/atoms/Heading';
import {
  AdvanceAsyncSelect,
  Select,
  AdvanceCreatableAsyncSelect,
} from '@tekion/tekion-components/src/molecules/advancedSelect';
import withPopoverOnSide, { STATUS } from '@tekion/tekion-components/src/molecules/withPopoverOnSide/withPopoverOnSide';
import CheckboxRenderer from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/checkbox';
import TextInputRenderer from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/textInput';
import withDecimalInputFormField from '@tekion/tekion-components/src/organisms/FormBuilder/hoc/withDecimalInputFormField';
import withSelectInput from '@tekion/tekion-components/src/organisms/FormBuilder/hoc/withSelectInput';

import CurrencyInput from '@tekion/tekion-widgets/src/appServices/parts/components/CellRenderers/CurrencyInput';
import withTriggerChangeOnBlur from '@tekion/tekion-widgets/src/hocs/parts/withTriggerChangeOnBlur';

import { BIN_NUMBER_SORT } from 'constants/sortEntities';

import { getBinWithLocationName } from 'helpers/parts.helper';
import { costPriceValidator } from 'helpers/validators/costPriceValidator';
import { listPriceValidator } from 'helpers/validators/listPriceValidator';
import { createPartNumberValidator } from 'helpers/validators/partNumberValidator';

import BrandCodeSelectField from 'molecules/BrandCodeSelectField';
import PartNameWithOemTagRenderer from 'molecules/PartNameWithOemTagRenderer';

import { masterPartLookupByKeysApi } from 'shared/PartsInventory/actions';

import { getIsPartInventoryAtSiteLevel, isMultilingualEnabled, getIsMultiWarehouseEnabled } from 'utils/general';
import { getSourceCodeList } from 'utils/sourceCodeUtils';

import { REPLACE_PART_FORM_KEYS } from '../constants/form.constants';
import { getShouldConsiderSourceCodePartPrefix } from '../helpers/general.helpers';
import styles from '../replaceInventoryPartModal.module.scss';

const POPOVER_TYPE_CONFIG = {
  error: {
    icon: 'icon-caution-filled',
    color: colors.carrotOrange,
  },
};

const CurrencyInputField = withDecimalInputFormField(withTriggerChangeOnBlur(CurrencyInput));
const AsyncSelectInput = withSelectInput(AdvanceAsyncSelect, { useFilterOption: false });
const CreatableAsyncSelectInput = withSelectInput(AdvanceCreatableAsyncSelect, { useFilterOption: false });
const PopoverCheckbox = withPopoverOnSide(CheckboxRenderer);
const SourceCodeSelect = compose(withSelectInput, withPopoverOnSide)(Select);

const isValidNewOption = value => !!_trim(value);

const getFormattedCreateLabel = value => __('Create "{{value}}" as a new Part', { value });

const getPartNumberField = ({ isDisabled, sourceCodeDetailsById, selectedWarehouse }) => ({
  id: REPLACE_PART_FORM_KEYS.PART,
  renderer: CreatableAsyncSelectInput,
  renderOptions: {
    resourceType: RESOURCE_TYPE.PART,
    label: __('Part Number'),
    required: true,
    validators: [createPartNumberValidator(sourceCodeDetailsById)],
    placeHolder: __('Search'),
    isDisabled,
    ...siteFilterESPayloadSanitizer({}, { isMultiSiteEnabled: getIsPartInventoryAtSiteLevel() }),
    shouldFetchOnValueChange: true,
    formatCreateLabel: getFormattedCreateLabel,
    isValidNewOption,
    menuPosition: 'fixed',
    components: { Option: PartNameWithOemTagRenderer },
    ...(getIsMultiWarehouseEnabled() && { lookupByKeysApi: masterPartLookupByKeysApi(selectedWarehouse) }),
  },
});

const getPartDescriptionRenderer = ({ disabled }) => ({
  id: REPLACE_PART_FORM_KEYS.PART_NAME,
  renderer: TextInputRenderer,
  renderOptions: {
    label: __('Part Description'),
    disabled,
    placeHolder: __('Type Here'),
    required: true,
    validators: disabled ? [] : [isRequiredRule],
    isMultiLingual: isMultilingualEnabled(),
  },
});

const getCostPriceRenderer = ({ disabled }) => ({
  id: REPLACE_PART_FORM_KEYS.COST_PRICE,
  renderer: CurrencyInputField,
  renderOptions: {
    label: __('Cost'),
    required: true,
    enforcePrecision: false,
    disabled,
    validators: disabled ? [] : [costPriceValidator],
  },
});

const getListPriceRenderer = ({ disabled }) => ({
  id: REPLACE_PART_FORM_KEYS.LIST_PRICE,
  renderer: CurrencyInputField,
  renderOptions: {
    label: __('List Price'),
    required: true,
    enforcePrecision: false,
    disabled,
    validators: disabled ? [] : [listPriceValidator],
  },
});

const getSourceCodeRenderer = ({
  sourceCodeDetailsById,
  disabled,
  sourceCodeId,
  selectedPartDetails,
  pageSourceType,
  partNumber,
}) => {
  const sourceCodeOptions = getSourceCodeList({ byId: sourceCodeDetailsById });
  const sourceCodeDetails = _get(sourceCodeDetailsById, sourceCodeId);
  const partPrefix = SourceCodeReader.partPrefix(sourceCodeDetails);

  const canShowPopover = getShouldConsiderSourceCodePartPrefix({
    pageSourceType,
    partNumber,
    sourceCodeId,
    partPrefix,
    selectedPartDetails,
  });

  return {
    id: REPLACE_PART_FORM_KEYS.SOURCE_CODE_ID,
    renderer: SourceCodeSelect,
    renderOptions: {
      label: __('Source Code'),
      options: sourceCodeOptions,
      placeholder: __('Select'),
      validators: [isRequiredRule],
      required: true,
      size: 6,
      fieldClassName: 'm-b-12',
      isDisabled: disabled,
      showPopover: canShowPopover,
      trigger: 'hover',
      statusPopoverDetails: {
        detailTypeConfigMap: POPOVER_TYPE_CONFIG,
        type: STATUS.ERROR,
        message: partPrefix
          ? __('Source Code prefix {{ prefix }} will be appended to part number while creating new parts', {
              prefix: partPrefix,
            })
          : null,
      },
    },
  };
};

const getTransferSourceCodeRenderer = ({ disabled }) => ({
  id: REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_SOURCE_CODE,
  renderer: CheckboxRenderer,
  renderOptions: {
    label: __('Transfer Source Code'),
    disabled,
    position: 'right',
    fieldClassName: 'flex-row align-items-center ',
    size: 6,
    fieldLabelClassName: 'm-l-8',
  },
});

const getTransferBinsRenderer = ({ disabled }) => ({
  id: REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_BINS,
  renderer: CheckboxRenderer,
  renderOptions: {
    label: __('Transfer Bins'),
    disabled,
    position: 'right',
    fieldLabelClassName: cx(styles.fieldLabel, 'm-l-8'),
    size: 4,
    fieldClassName: `flex align-items-center ${styles.transferBin}`,
  },
});

const getTransferOnHandQtyRenderer = ({ disabled }) => ({
  id: REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_ON_HAND,
  renderer: CheckboxRenderer,
  renderOptions: {
    label: __('Transfer On Hand Qty'),
    disabled,
    position: 'right',
    fieldClassName: 'flex-row align-items-center m-b-0 m-b-32',
    fieldLabelClassName: 'm-l-8',
  },
});

const getTransferPartLevelComment = ({ disabled, partComments }) => ({
  id: REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_PART_LEVEL_COMMENTS,
  renderer: !_isEmpty(partComments) ? PopoverCheckbox : CheckboxRenderer,
  renderOptions: {
    label: __('Transfer Part Level Comments'),
    disabled,
    position: 'right',
    fieldClassName: `flex-row align-items-center ${styles.transferPartLevel} m-r-8`,
    fieldLabelClassName: cx(styles.fieldLabel, 'm-l-8'),
    ...(!_isEmpty(partComments) && {
      statusPopoverDetails: {
        type: STATUS.INFO,
        message: partComments,
      },
      trigger: 'hover',
      iconClassName: styles.partCommentIcon,
      wrapperClassName: styles.partCommentWrapper,
      popoverContentClassName: styles.partCommentPopoverContent,
      popoverPlacement: 'bottom',
    }),
  },
});

const getTransferCustomizedPriceRenderer = ({ disabled }) => ({
  id: REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_CUSTOMIZED_PRICE_OVERRIDE,
  renderer: CheckboxRenderer,
  renderOptions: {
    disabled,
    position: 'right',
    fieldClassName: `flex-row align-items-center ${styles.transferCustomizedPrice}`,
    label: __('Transfer Customized Price Overrides'),
    fieldLabelClassName: cx(styles.fieldLabel, 'm-l-8'),
  },
});

const getPrimaryBinRenderer = ({ locationDetailsById, disabled }) => ({
  id: REPLACE_PART_FORM_KEYS.PRIMARY_BIN_ID,
  renderer: AsyncSelectInput,
  renderOptions: {
    label: __('Primary Bin'),
    required: true,
    validators: [isRequiredRule],
    resourceType: RESOURCE_TYPE.BINS,
    isDisabled: disabled,
    ...siteFilterESPayloadSanitizer({}, { isMultiSiteEnabled: getIsPartInventoryAtSiteLevel() }),
    sort: BIN_NUMBER_SORT,
    placeHolder: __('Search'),
    shouldFetchOnValueChange: true,
    resourceParams: {
      getOptionLabel: option => getBinWithLocationName(option, locationDetailsById),
    },
    fieldClassName: 'm-b-0',
  },
});

const FieldHeader = ({ label, fieldClassName = '' }) => (
  <div className={fieldClassName}>
    <Heading size={3}>{label}</Heading>
  </div>
);

const getBinDetailsHeaderRenderer = () => ({
  id: REPLACE_PART_FORM_KEYS.BIN_DETAILS_HEADER,
  renderer: () => <FieldHeader fieldClassName={styles.binDetailsHeader} label={__('Bin Details')} />,
  renderOptions: { size: 4 },
});

const getAdditionalInfoHeaderRenderer = () => ({
  id: REPLACE_PART_FORM_KEYS.ADDITIONAL_INFORMATION_HEADER,
  renderer: () => <FieldHeader fieldClassName={styles.additionalInfoHeader} label={__('Additional Info')} />,
  renderOptions: { size: 6 },
});

const getBrandCodeRenderer = ({ disabled }) => ({
  renderer: BrandCodeSelectField,
  renderOptions: {
    size: 6,
    validators: [isRequiredRule],
    required: true,
    label: __('Brand'),
    disabled,
    fieldClassName: 'm-b-12',
  },
});

const getFields = (
  partFieldValue,
  partNumber,
  partComments,
  sourceCodeId,
  sourceCodeDetailsById,
  locationDetailsById,
  shouldLockPartNumber,
  pageSourceType,
  selectedPartDetails,
  isPartAvailableInMaster,
  selectedWarehouse,
) => {
  const isFieldDisabledOnEmptyPartNumber = _isNil(partFieldValue);
  const isFieldDisabled = isPartAvailableInMaster || isFieldDisabledOnEmptyPartNumber;
  return {
    [REPLACE_PART_FORM_KEYS.PART]: getPartNumberField({
      isDisabled: shouldLockPartNumber,
      sourceCodeDetailsById,
      selectedWarehouse,
    }),
    [REPLACE_PART_FORM_KEYS.PART_NAME]: getPartDescriptionRenderer({
      disabled: isFieldDisabled,
    }),
    [REPLACE_PART_FORM_KEYS.BRAND_CODE]: getBrandCodeRenderer({
      disabled: isPartAvailableInMaster || isFieldDisabledOnEmptyPartNumber,
    }),
    [REPLACE_PART_FORM_KEYS.COST_PRICE]: getCostPriceRenderer({
      disabled: isFieldDisabled,
    }),
    [REPLACE_PART_FORM_KEYS.LIST_PRICE]: getListPriceRenderer({
      disabled: isFieldDisabled,
    }),
    [REPLACE_PART_FORM_KEYS.SOURCE_CODE_ID]: getSourceCodeRenderer({
      sourceCodeDetailsById,
      disabled: isFieldDisabledOnEmptyPartNumber,
      sourceCodeId,
      pageSourceType,
      partNumber,
      selectedPartDetails,
    }),
    [REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_SOURCE_CODE]: getTransferSourceCodeRenderer({
      disabled: isFieldDisabledOnEmptyPartNumber,
    }),
    [REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_BINS]: getTransferBinsRenderer({
      disabled: isFieldDisabledOnEmptyPartNumber,
    }),
    [REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_ON_HAND]: getTransferOnHandQtyRenderer({
      disabled: isFieldDisabledOnEmptyPartNumber,
    }),
    [REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_CUSTOMIZED_PRICE_OVERRIDE]: getTransferCustomizedPriceRenderer({
      disabled: isFieldDisabledOnEmptyPartNumber,
    }),
    [REPLACE_PART_FORM_KEYS.ALLOW_TRANSFER_PART_LEVEL_COMMENTS]: getTransferPartLevelComment({
      disabled: isFieldDisabledOnEmptyPartNumber,
      partComments,
    }),
    [REPLACE_PART_FORM_KEYS.PRIMARY_BIN_ID]: getPrimaryBinRenderer({
      locationDetailsById,
      disabled: isFieldDisabledOnEmptyPartNumber,
    }),
    [REPLACE_PART_FORM_KEYS.BIN_DETAILS_HEADER]: getBinDetailsHeaderRenderer(),
    [REPLACE_PART_FORM_KEYS.ADDITIONAL_INFORMATION_HEADER]: getAdditionalInfoHeaderRenderer(),
  };
};

const FIELDS_TO_PICK_FOR_FORM_CONFIG = [
  REPLACE_PART_FORM_KEYS.PART,
  REPLACE_PART_FORM_KEYS.PART_NUMBER,
  REPLACE_PART_FORM_KEYS.PART_COMMENTS,
  REPLACE_PART_FORM_KEYS.SOURCE_CODE_ID,
  'sourceCodeDetailsById',
  'locationDetailsById',
  'shouldLockPartNumber',
  'pageSourceType',
  'selectedPartDetails',
  'isPartAvailableInMaster',
  'selectedWarehouse',
];

export const createSelectorForFormFields = () =>
  createSelector(FIELDS_TO_PICK_FOR_FORM_CONFIG.map(_property), getFields);
