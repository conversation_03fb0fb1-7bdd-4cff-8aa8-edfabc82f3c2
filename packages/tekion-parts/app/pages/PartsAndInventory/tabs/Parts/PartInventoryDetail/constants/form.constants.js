import { STOCK_PARAM_TYPE } from '@tekion/tekion-base/constants/parts/stockingParams';

import { STOCKING_STATUS } from '@tekion/tekion-business/src/appServices/parts/partsAndInventory/constants/stockingStatus.constants';
import { OVERRIDABLE_MASTER_FIELD_KEY } from '@tekion/tekion-business/src/appServices/parts/partsAndInventory/domain/InventoryPart.domain';

import { DEFAULT_PART_BRAND_CODE, DEFAULT_PART_SUB_BRAND_CODE } from 'constants/brand.constants';

import { FORM_SECTION_KEY } from 'pages/PartsAndInventory/constants/inventoryForm.constants';

import { PART_TYRE_INFORMATION_CONFIG } from 'shared/PartsInventory/constants';

import { getIsAfterMarketPartsEnabled } from 'utils/general';

import { FRACTIONAL_SALE_CRITERIA_DEFAULT_ROW_VALUE } from './general.constants';

export const PART_INVENTORY_DETAILS_FORM_CONTEXT_ID = 'partInventoryForm';

export const BASIC_DETAILS_FORM_KEY = {
  PART: 'part',
  PART_NUMBER: 'partNumber',
  PART_NAME: 'partName',
  GROUP: 'group',
  UNIT_PACK_QTY: 'unitPackQty',
  AUTO_UPDATE_FLAG: 'autoUpdateFlag',
  BRAND_CODE: 'brandCode',
  SUB_BRAND_CODE: 'subBrandCode',
  OEM_CODE: 'oemCode',
  ASSOCIATED_PART_NUMBER: 'associatedPartNumber',
  PART_COMMENT: 'comment',
};

export const STOCKING_DETAILS_FORM_KEY = {
  SOURCE_CODE_ID: 'sourceCodeId',
  TOTAL_ON_HAND_QUANTITY: 'totalOnHandQuantity',
  HOLD_QUANTITY: 'holdQuantity',
  ON_ORDER_QUANTITY: 'onOrderQuantity',
  OPEN_DOCS: 'openDocs',
  STOCK_PARAM_TYPE: 'stockParamType',
  BEST_REORDER_POINT: 'bestReOrderPoint',
  BEST_STOCKING_LEVEL: 'bestStockingLevel',
  INFO_NOTE: 'infoNote',
  AVG_DEMAND_QUANTITY: 'avgDemandQuantity',
  MANUAL_ORDER: 'manualOrder',
  STOCKING_STATUS: 'stockingStatus',
  MINIMUM_QTY: 'minimumQty',
  MAXIMUM_QTY: 'maximumQty',
  IMPREST_STOCK_HEADER: 'imprestStockHeader',
  IMPREST_STOCK_QUANTITY: 'imprestStockQuantity',
  LAST_PURCHASE_DATE: 'lastPurchaseDate',
  LAST_SALE_DATE: 'lastSaleDate',
};

export const BIN_DETAILS_FORM_KEY = {
  BIN_LIST: 'binList',
  CONSOLIDATED_BIN_VIEW: 'consolidatedBinView',
};

export const PART_PRICING_FORM_KEY = {
  COST_PRICE: 'costPrice',
  AVERAGE_COST_PRICE: 'averageCost',
  LIST_PRICE: 'listPrice',
  TRADE_PRICE: 'tradePrice',
  COMP_PRICE: 'compPrice',
  CORE_VALUE: 'coreValue',
  MPL_PRICE: 'mplPrice',
};

export const PART_GROUPS_FORM_KEY = {
  PART_GROUPS: 'partGroups',
};

export const FRACTIONAL_SALES_FORM_KEY = {
  FRACTIONAL_SALE_SWITCH: 'fractionalSaleEnabled',
  FRACTIONAL_SALE_CRITERIA: 'fractionalSaleCriteria',
};

export const LINKED_PARTS_FORM_KEY = {
  REPLACED_BY_PART_LIST: 'replacedByPartList',
  REPLACED_FOR_PART_LIST: 'replacedForPartList',
  OLD_PART_LIST: 'oldPartList',
  NEW_PART_LIST: 'newPartList',
  ALTERNATE_PART_LIST: 'alternatePartList',
  ASSOCIATED_PART_LIST: 'associatedPartList',
  LINKED_PARTS_KITS: 'linkedPartsKit',

  // renault
  NORMAL_PART_LIST: 'normalParts',
  STANDARD_EXCHANGE_PART_LIST: 'standardExchangeParts',
  CORE_PART_LIST: 'coreParts',
};

export const BIN_TABLE_COLUMN_KEY = {
  ID: 'id',
  IS_PRIMARY_BIN: 'primaryBin',
  BIN_ID: 'binId',
  BIN_NUMBER: 'binNumber',
  SHELF_ID: 'shelfId',
  SHELF_NUMBER: 'shelfNumber',
  DRAWER_ID: 'drawerId',
  DRAWER_NUMBER: 'drawerNumber',
  QUANTITY: 'quantity',
  ROW_ACTION: 'rowAction',
  LOCATION_ID: 'locationId',
  REF_ID: 'refId',
  ON_HOLD_QUANTITY: 'onHoldQuantity',
  ON_HAND_QUANTITY: 'onHandQuantity',
};

export const ADDITIONAL_DETAILS_FORM_KEY = {
  // general form keys
  UNIT_OF_MEASURE: 'unitOfMeasure',
  ABCD_CODE: 'abcdCode',
  MFR_CONTROLLED: 'mfrControlled',
  MFR_CONTROL_UNIT: 'mfrControlledCode',
  REPACK_CODE: 'repackCode',
  RE_MANUFACTURER_INDICATOR: 'remanufactureIndicator',
  OEM_BRAND: 'oemBrand',
  MATERIAL_RETURN_CODE: 'materialReturnCode',
  PART_HEIGHT: 'partHeight',
  PART_HEIGHT_UNIT: 'partHeightUnit',
  PART_LENGTH: 'partLength',
  PART_LENGTH_UNIT: 'partLengthUnit',
  PART_WEIGHT: 'partWeight',
  PART_WEIGHT_UNIT: 'partWeightUnit',
  PART_WIDTH: 'partWidth',
  PART_WIDTH_UNIT: 'partWidthUnit',
  CORE_PART_NUMBER: 'corePartNumber',
  ...PART_TYRE_INFORMATION_CONFIG,

  // GM
  LIFE_TIME_WARRANTY: 'lifetimeWarranty',
  SERVICE_LANE_INDICATOR: 'serviceLaneIndicator',
  PART_CLASSIFICATION: 'partsClassification',
  MATERIAL_RETURN_INDICATOR: 'materialReturnIndicator',
  CSO_RETURN_INDICATOR: 'csoReturnIndicator',
  DISTRIBUTION_QTY: 'distributionQuantity',
  ITEM_CATALOG_GROUP_ID: 'itemCatalogGroupId',
  MIN_BUY_QUANTITY: 'minimumBuyQuantity',
  PART_ITEM_DESCRIPTION: 'partItemDescription',
  RE_MANUFACTURED_PART_DESCRIPTION: 'remanufacturedPartDescription',
  PART_MASTER_EFFECTIVE_DATE: 'partMasterEffectiveDate',
  FRANCHISE_CODE: 'franchiseCode',
  SUPERSEDING_PART_NUMBER: 'supersedingPartNumber',
  SUPERSESSION_DATE: 'supersessionDate',
  EMRP_PRICE: 'emrpPrice',
  FUTURE_EMRP_PRICE: 'futureEmrpPrice',
  ALTERNATE_ITEM_ID: 'alternateItemId',
  CUSTOMER_PAY_PROGRAM_PARTS: 'cppp',
  EXTENDED_PART_HEIGHT: 'extendedPartHeight',
  EXTENDED_PART_HEIGHT_UNIT: 'extendedPartHeightUnit',
  EXTENDED_PART_LENGTH: 'extendedPartLength',
  EXTENDED_PART_LENGTH_UNIT: 'extendedPartLengthUnit',
  EXTENDED_PART_WEIGHT: 'extendedPartWeight',
  EXTENDED_PART_WEIGHT_UNIT: 'extendedPartWeightUnit',
  EXTENDED_PART_WIDTH: 'extendedPartWidth',
  EXTENDED_PART_WIDTH_UNIT: 'extendedPartWidthUnit',
  // camping world
  VENDOR_CODE: 'vendorCode',
  VENDOR_PART_NUMBER: 'vendorPartNumber',
  SELL_IN_FRACTION: 'sellInFractionsFlag',

  // Acura
  FUNCTION_CODE: 'functionCode',
  DEALER_GROSS_PROFIT_PERCENT: 'dealerGrossProfitPercent',
  CP_CODE: 'cpCode',
  PARITY_PRICE_CODE: 'parityPriceCode',

  // Honda
  STOCK_RETURN_ALLOWANCE_CODE: 'stockReturnAllowanceCode',

  // AstonMartin
  VOR_DISCOUNT: 'vorDiscount',
  PRIMARY_MODEL_USAGE: 'primaryModelUsage',

  // BMW
  PART_COMMON_CODE: 'partCommonCode',
  PART_DEMAND_CODE: 'partDemandCode',
  SALES_TAX_APPLICABLE: 'salesTaxApplicable',
  PART_COUNTRY: 'partCountry',
  TARIFF_CODE: 'tariffCode',
  TIRE_TYPE_CODE: 'tireTypeCode',
  ACCESSORIES_DESCRIPTION: 'accessoriesDescription',
  PART_MARKETING_PRODUCT_LINE: 'partMarketingProductLine',
  ASSORTMENT_CATEGORY: 'assortmentCategory',
  PART_SUBSTITUTION_CODE: 'partSubstitutionCode',
  PART_FUNCTION_CODE: 'partFunctionCode',
  PLANNING_STATUS: 'planningStatus',
  PART_BRAND_CODE: 'oemBrand',
  PART_RETURNABLE_FLAG: 'partReturnableFlag',
  DISTRIBUTION_CODE: 'distributionCode',

  // Nissan
  EXCHANGE_RETAIL_PRICE: 'exchangeRetailPrice',
  EXCHANGE_DEALER_PRICE: 'exchangeDealerPrice',
  DEALER_SALES_RESTRICTION: 'dealerSalesRestrictions',
  WHOLE_SALE_TYPE_CODE: 'wholesaleTypeCode',
  INTERCHANGEABILITY_CODE: 'interchangeabilityCode',
  PRICE_CONTROL_INDICATOR: 'priceControlIndicator',

  // Toyota
  DISCOUNT_PRICING_CODE: 'discountPricingCodeQty',
  DISCONTINUE_DATE: 'discontinuedDate',

  // FCA
  MSDS_INDICATOR: 'msdsIndicator',
  DEPOSIT_FOR_OEM_PARTS: 'depositForOemParts',
  PRODUCT_LINE_MAJOR: 'productLineMajor',
  PRODUCT_LINE_MINOR: 'productLineMinor',
  RULING_DATE: 'rulingDate',
  PART_SHELF_LIFE: 'partShelfLife',
  MFR_GUARANTEE_QTY: 'mfrGuaranteeQuantity',

  // ferrari
  URGENT_PRICE: 'urgentPrice',
  FAST_STOCK_COST: 'fastStockCost',

  // Ford
  UNIT_SELL_PACK: 'unitSellPack',
  PREFIX_SERVICE_PART_NUMBER: 'prefixServicePartNumber',
  BASE_SERVICE_PART_NUMBER: 'baseServicePartNumber',
  SUFFIX_SERVICE_PART_NUMBER: 'suffixServicePartNumber',
  MOTORCRAFT_PART_NUMBER: 'motorcraftPartNumber',
  PREFIX_MOTORCRAFT_PART_NUMBER: 'prefixMotorcraftPartNumber',
  BASE_MOTORCRAFT_PART_NUMBER: 'baseMotorcraftPartNumber',
  SUFFIX_MOTORCRAFT_PART_NUMBER: 'suffixMotorcraftPartNumber',
  PIPP_RETURN_CODE: 'pippReturnCode',
  PIPP_PRICE: 'pippPrice',
  ALLOWANCE_CODE: 'allowanceCode',
  DEALER_TO_DEALER_INDICATOR: 'dealerToDealerIndicator',
  DEALER_TO_DEALER_PRICE: 'dealerToDealerPrice',
  NATIONAL_FLEET_PRICE: 'nationalFleetPrice',
  SELL_PACK_QUANTITY: 'sellPackQuantity',
  INSTALLER_PRICE: 'installerPrice',
  BATTERY_FLEET_ALLOWANCE: 'batteryFleetAllowance',
  MARKETING_CODE: 'marketingCode',
  WHOLE_SALE_INCENTIVE_PRICE: 'wholeSaleIncentive',
  FET_PRICE: 'fetPrice',

  // hundai
  VARIABLE_RETAIL_MARGIN: 'variableRetailMargin',
  REMARK_FLAG: 'remarkFlag',
  PART_PROMOTION_FLAG: 'partPromotionFlag',

  // isuzu
  GM_PART_NUMBER: 'gmPartNumber',
  QUANTITY_DISCOUNT_CODE: 'quantityDiscountCode',
  ACCESSORY_CODE: 'accessoryCode',
  FLEET_PRICE: 'fleetPrice',
  EQUALIZED_HANDLING_CHARGE: 'handlingCharge',

  // Kia
  DEALER_STOCK_CODE: 'dealerStockCode',
  PART_BULLETIN_NUMBER: 'partBulletinNumber',

  // lamborgini
  DEALERSHIP_COST_DISCOUNT: 'dealershipCostDiscount',
  WARRANTY_DISCOUNT: 'warrantyDiscount',
  URGENT_PRICE_DISCOUNT: 'urgentPriceDiscount',

  // maclaren
  COUNTRY_OF_ORIGIN: 'countryOfOrigin',

  // mistibishi
  VINTAGE_PART_INDICATOR: 'vintagePartIndicator',
  PART_NOTE_INDICATOR: 'partNoteIndicator',

  // porsche
  DIVISION: 'division',

  // ranault
  MANUFACTURER_CODE: 'manufacturerCode',
  ORIGIN_CODE: 'originCode',
  ORIGIN_NAME: 'originName',
  OEM_BRAND_NAME: 'oemBrandName',
  TAX_CODE: 'taxCode',
  REFERENTIAL_CODE: 'referentialCode',
  POLE: 'pole',
  VOLUME: 'volume',
  COMMERCIAL_UNIT: 'commercialUnit',
  PROVISIONAL_PRICE_CODE: 'priceCode',
  DIRECT_FLOW: 'directFlowFlag',
  MANDATORY_FDS: 'fdsMandatoryFlag',
  TECHNICAL_FAMILY: 'technicalFamily',
  TECHNICAL_FAMILY_NAME: 'technicalFamilyName',
  COMPETITOR_CODE: 'competitorCode',
  COMPETITION_NAME: 'competitionName',
  PROMOTIONAL_NAME: 'promotionalName',
  SEGMENT_CODE: 'segmentCode',
  UNIT_PACK_QTY: 'unitPackQty',
  PACKAGE_CODE: 'packageCode',

  // volkswagan
  TOP_VOLKSWAGEN_SELLING_PART: 'topVolkswagenSellingPart',
  TOP_AUDI_SELLING_PART: 'topAudiSellingPart',
  TOP_VOLKSWAGEN_AUDI_SELLING_PART: 'topVolkswagenAudiSellingPart',

  // volvo
  FUNCTION_GROUP: 'functionGroup',
  IMPORTER_PRODUCT_GROUP: 'importerProductGroup',

  // mercedes benz
  PROMOTIONAL_VALUE: 'promotionValue',
  VENDOR_PRICE: 'vendorPrice',
  VIN_REQUIRED: 'vinRequired',
  SHELF_LIFE: 'shelfLifeFlag',

  // fields used in multiple brands
  ASR_BSL: 'asrBsl', // fca, nissan, ford, bmw, lexus
  ASR_BRP: 'asrBrp', // fca, nissan, ford, bmw, lexus
  MINIMUM_BUY_QTY: 'minimumBuyQty', // used in fca, lexus, rollsroyce, subaru, toyota
  MAXIMUM_BUY_QTY: 'maximumBuyQty', // used in fca, lexus, rollsroyce, subaru, toyota
  CLASS_CODE: 'classCode', // used in kia, mazda, fca, merdedez
  PART_FLAG: 'partFlag', // used in mazda , acura
  LEVEL_FILLER: 'levelFiller', // used in Ford, acura
  LAST_YEAR: 'lastYear', // used in lexus , toyota
  PART_SUPPLY_STATUS_CODE: 'partSupplyStatusCode', // toyota, lexus, fca, acura

  STOCK_CLASS: 'stockClass', // toyota, lexus
  PART_SURCHARGE: 'partSurcharge', // toyota, lexus
  SURCHARGE_VALUE: 'surchargeValue', // toyota, lexus

  WHOLE_SALE_DISCOUNT_ELIGIBLE: 'wholesaleDiscountEligible', // fca, acura
  PART_CLASS_CODE: 'partClassCode', // kia, acura, bmw
  PART_SOURCE_CODE: 'partSourceCode', // subaru, rollroyce, kia, ford
  SHIP_CODE: 'shipCode', // acura bmw
  PART_TYPE_CODE: 'partTypeCode', // jlr, fca, bmw, toyota, lexus
  OBSOLETE_STATUS: 'obsoleteStatus', // mazda, ford
  PRICING_DISCOUNT: 'pricingDiscount', // subaru , rollsroyce, bmw
  PRODUCT_HIERARCHY: 'productHierarchy', // porsche , mclaren
  OEM_COUNTRY: 'oemCountry', // mclaren, jlr, astonMartin, bentley
  KIT_CONDITIONAL_ORDER: 'kitConditionalOrder', // mistibushi , bentley
  DEALER_PRICE_VOR: 'dealerPriceVor', // porsche, astonMartin
  DISCOUNT_CODE: 'discountCode', // jlr, astonMartin, bentley, ford, renault, toyota, lexus
  HAZARDOUS_INDICATOR: 'hazardousIndicator', // volkswagen, bmw, renault
  PROMOTIONAL_CODE: 'promotionalCode', // fca, renault
  WARRANTY_PRICE: 'warrantyPrice', // porche, volkswagen
};

export const DEFAULT_FORM_VALUES = {
  [BASIC_DETAILS_FORM_KEY.BRAND_CODE]: DEFAULT_PART_BRAND_CODE,
  [BASIC_DETAILS_FORM_KEY.SUB_BRAND_CODE]: DEFAULT_PART_SUB_BRAND_CODE,
  [STOCKING_DETAILS_FORM_KEY.MANUAL_ORDER]: false,
  [STOCKING_DETAILS_FORM_KEY.STOCK_PARAM_TYPE]: STOCK_PARAM_TYPE.DAYS,
  [STOCKING_DETAILS_FORM_KEY.STOCKING_STATUS]: STOCKING_STATUS.NON_STOCK,
  [FRACTIONAL_SALES_FORM_KEY.FRACTIONAL_SALE_CRITERIA]: FRACTIONAL_SALE_CRITERIA_DEFAULT_ROW_VALUE,
};

export const STATE_KEY = {
  OEM_PART_AVAILABILITY_MODAL: 'OEM_PART_AVAILABILITY_MODAL',
  PART_ASSORTMENT_EXIT_MODAL: 'PART_ASSORTMENT_EXIT_MODAL',
  DELETE_PART_MODAL: 'DELETE_PART_MODAL',
  ON_HOLD_PAGE_MODAL: 'ON_HOLD_PAGE_MODAL',
  ON_ORDER_PAGE_MODAL: 'ON_ORDER_PAGE_MODAL',
  PART_OPEN_DOC_PAGE_MODAL: 'PART_OPEN_DOC_PAGE_MODAL',
  STOCKING_HISTORY_MODAL: 'STOCKING_HISTORY_MODAL',
  EXISTING_INVENTORY_PART_INFO_MODAL: 'EXISTING_INVENTORY_PART_INFO_MODAL',
  PART_CHANGE_MODAL: 'PART_CHANGE_MODAL',
  MASTER_FIELD_EDIT_MODAL: 'MASTER_FIELD_EDIT_MODAL',
  ADD_NEW_BIN_MODAL: 'ADD_NEW_BIN_MODAL',
  EXTENDED_PART_MASTER_FETCH: 'EXTENDED_PART_MASTER_FETCH',
  UNLINK_PART_MODAL: 'UNLINK_PART_MODAL',
  REPLACE_INVENTORY_PART_MODAL: 'REPLACE_INVENTORY_PART_MODAL',
  OEM_RESTORE_CONFIRMATION_MODAL: 'OEM_RESTORE_CONFIRMATION_MODAL',
  PART_PREFIX_CONFIRMATION_MODAL: 'PART_PREFIX_CONFIRMATION_MODAL',
  GO_BACK_CONFIRMATION_MODAL: 'GO_BACK_CONFIRMATION_MODAL',
  CANCEL_CONFIRMATION_MODAL: 'CANCEL_CONFIRMATION_MODAL',
  VENDOR_PRICING_FORM: 'VENDOR_PRICING_FORM',
  MARK_LOST_SALE_MODAL: 'MARK_LOST_SALE_MODAL',
};

export const CUSTOM_PRICE_FIELD_BASE_KEY = 'CUSTOM_PRICE_';

export const BIN_MAPPING_KEYS_TO_COMPARE = ['binId', 'shelfId', 'drawerId', 'quantity', 'primaryBin'];

export const COMMON_FORM_MESSAGE = {
  DUPLICATE_PART: getIsAfterMarketPartsEnabled()
    ? __('Duplicate part number in the current brand/sub-brand')
    : __('Duplicate part number in the current brand'),
  DUPLICATE_PART_WITH_PREFIX: getIsAfterMarketPartsEnabled()
    ? __('Duplicate part number with source code prefix in the current brand/sub-brand')
    : __('Duplicate part number with source code prefix in the current brand'),
};

export const MASTER_OVERRIDE_FIELD_GROUP = {
  PRICING: 'pricing',
};

export const MASTER_OVERRIDE_FIELD_GROUP_VS_FIELD_ID_LIST = {
  [MASTER_OVERRIDE_FIELD_GROUP.PRICING]: [
    PART_PRICING_FORM_KEY.COST_PRICE,
    PART_PRICING_FORM_KEY.LIST_PRICE,
    PART_PRICING_FORM_KEY.TRADE_PRICE,
    PART_PRICING_FORM_KEY.COMP_PRICE,
    PART_PRICING_FORM_KEY.CORE_VALUE,
  ],
};

export const FIELD_ID_VS_MASTER_OVERRIDE_AUDIT_FIELD_NAME = {
  [PART_PRICING_FORM_KEY.COST_PRICE]: OVERRIDABLE_MASTER_FIELD_KEY.COST_PRICE,
  [PART_PRICING_FORM_KEY.LIST_PRICE]: OVERRIDABLE_MASTER_FIELD_KEY.LIST_PRICE,
  [PART_PRICING_FORM_KEY.TRADE_PRICE]: OVERRIDABLE_MASTER_FIELD_KEY.TRADE_PRICE,
  [PART_PRICING_FORM_KEY.COMP_PRICE]: OVERRIDABLE_MASTER_FIELD_KEY.COMP_PRICE,
  [PART_PRICING_FORM_KEY.CORE_VALUE]: OVERRIDABLE_MASTER_FIELD_KEY.CORE_PRICE,
  [BASIC_DETAILS_FORM_KEY.PART_NAME]: OVERRIDABLE_MASTER_FIELD_KEY.PART_NAME,
  [LINKED_PARTS_FORM_KEY.OLD_PART_LIST]: OVERRIDABLE_MASTER_FIELD_KEY.OLD_SUPERSESSIONS,
  [LINKED_PARTS_FORM_KEY.NEW_PART_LIST]: OVERRIDABLE_MASTER_FIELD_KEY.NEW_SUPERSESSIONS,
  [LINKED_PARTS_FORM_KEY.ALTERNATE_PART_LIST]: OVERRIDABLE_MASTER_FIELD_KEY.ALTERNATE_PARTS,
  [LINKED_PARTS_FORM_KEY.ASSOCIATED_PART_LIST]: OVERRIDABLE_MASTER_FIELD_KEY.ASSOCIATED_PARTS,
};

export const MULTI_LANG_SUPPORTED_FIELD_IDS = [
  BASIC_DETAILS_FORM_KEY.PART_NAME,
  BASIC_DETAILS_FORM_KEY.GROUP,
  BASIC_DETAILS_FORM_KEY.PART_COMMENT,
];

export const NO_OF_COLUMNS_PER_ROW = 4;

export const EXCLUDED_SECTIONS_IN_CREATE_MODE = [FORM_SECTION_KEY.FAVOURITES, FORM_SECTION_KEY.PART_GROUPS];

export const TYRE_INFORMATION_CONFIG_KEYS = [
  ADDITIONAL_DETAILS_FORM_KEY.TYRE_SIZE,
  ADDITIONAL_DETAILS_FORM_KEY.LOAD_CAPACITY_INDEX,
  ADDITIONAL_DETAILS_FORM_KEY.TYRE_TYPE,
  ADDITIONAL_DETAILS_FORM_KEY.WET_GRIP,
  ADDITIONAL_DETAILS_FORM_KEY.FUEL_RATING,
  ADDITIONAL_DETAILS_FORM_KEY.EXTERNAL_ROLLING_NOISE_CODE,
  ADDITIONAL_DETAILS_FORM_KEY.EXTERNAL_ROLLING_NOISE_VALUE,
];

export const TYRE_CONFIGS_VS_MAX_LENGTH = {
  [ADDITIONAL_DETAILS_FORM_KEY.TYRE_SIZE]: 10,
  [ADDITIONAL_DETAILS_FORM_KEY.LOAD_CAPACITY_INDEX]: 4,
  [ADDITIONAL_DETAILS_FORM_KEY.TYRE_TYPE]: 20,
  [ADDITIONAL_DETAILS_FORM_KEY.EXTERNAL_ROLLING_NOISE_CODE]: 3,
};
