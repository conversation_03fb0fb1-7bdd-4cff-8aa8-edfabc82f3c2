import _isNil from 'lodash/isNil';

import { mapProps } from 'recompose';

import { NO_DATA } from '@tekion/tekion-base/app.constants';

import inventoryPartReader from '@tekion/tekion-business/src/appServices/parts/partsAndInventory/readers/inventoryPart.reader';
import PartMasterReader from '@tekion/tekion-business/src/appServices/parts/partsAndInventory/readers/partMaster.reader';

import NumberField from 'pages/PartsAndInventory/components/fieldRenderers/NumberField';
import SelectOptionField from 'pages/PartsAndInventory/components/fieldRenderers/SelectOptionField';
import TextField from 'pages/PartsAndInventory/components/fieldRenderers/TextField';

import { ADDITIONAL_DETAILS_FORM_KEY, DEFAULT_BOOL_SELECT_OPTIONS } from '../../../constants/form.constants';

const getPromotionPriceFieldConfig = ({
  frozenInventoryPartDetails,
  frozenOemPartMasterDetails,
  isEditing,
  pinnedFieldIds,
}) => ({
  renderer: mapProps(props => ({
    ...props,
    value: _isNil(frozenInventoryPartDetails)
      ? PartMasterReader.promotionValue(frozenOemPartMasterDetails)
      : inventoryPartReader.promotionValue(frozenInventoryPartDetails?.part),
  }))(NumberField),
  renderOptions: {
    label: __('Promotion Price'),
    isCurrencyField: true,
    placeholder: NO_DATA,
    disabled: true,
    isEditing,
    pinnedFieldIds,
  },
});

const getPartDemandCodeFieldConfig = ({
  frozenInventoryPartDetails,
  frozenOemPartMasterDetails,
  isEditing,
  pinnedFieldIds,
}) => ({
  renderer: mapProps(props => ({
    ...props,
    value: _isNil(frozenInventoryPartDetails)
      ? PartMasterReader.partDemandCode(frozenOemPartMasterDetails)
      : inventoryPartReader.partDemandCode(frozenInventoryPartDetails?.part),
  }))(TextField),
  renderOptions: {
    label: __('Part Demand Code'),
    disabled: true,
    placeholder: NO_DATA,
    isEditing,
    pinnedFieldIds,
  },
});

const getMarketingCodeFieldConfig = ({
  frozenInventoryPartDetails,
  frozenOemPartMasterDetails,
  isEditing,
  pinnedFieldIds,
}) => ({
  renderer: mapProps(props => ({
    ...props,
    value: _isNil(frozenInventoryPartDetails)
      ? PartMasterReader.marketingCode(frozenOemPartMasterDetails)
      : inventoryPartReader.marketingCode(frozenInventoryPartDetails?.part),
  }))(TextField),
  renderOptions: {
    label: __('Marketing Code'),
    disabled: true,
    placeholder: NO_DATA,
    isEditing,
    pinnedFieldIds,
  },
});

const getShelfLifeFieldConfig = ({
  frozenOemPartMasterDetails,
  frozenInventoryPartDetails,
  isEditing,
  pinnedFieldIds,
}) => ({
  renderer: mapProps(props => ({
    ...props,
    value: _isNil(frozenInventoryPartDetails)
      ? PartMasterReader.shelfLifeFlag(frozenOemPartMasterDetails)
      : inventoryPartReader.shelfLifeFlag(frozenInventoryPartDetails?.part),
  }))(SelectOptionField),
  renderOptions: {
    label: __('Shelf Life'),
    isDisabled: true,
    placeholder: NO_DATA,
    options: DEFAULT_BOOL_SELECT_OPTIONS,
    isEditing,
    pinnedFieldIds,
  },
});

const getVinRequiredFieldConfig = ({
  frozenOemPartMasterDetails,
  frozenInventoryPartDetails,
  isEditing,
  pinnedFieldIds,
}) => ({
  renderer: mapProps(props => ({
    ...props,
    value: _isNil(frozenInventoryPartDetails)
      ? PartMasterReader.shelfLifeFlag(frozenOemPartMasterDetails)
      : inventoryPartReader.shelfLifeFlag(frozenInventoryPartDetails?.part),
  }))(SelectOptionField),
  renderOptions: {
    label: __('VIN Required'),
    isDisabled: true,
    placeholder: NO_DATA,
    options: DEFAULT_BOOL_SELECT_OPTIONS,
    isEditing,
    pinnedFieldIds,
  },
});

const getVendorCodeFieldConfig = ({
  frozenInventoryPartDetails,
  frozenOemPartMasterDetails,
  isEditing,
  pinnedFieldIds,
}) => ({
  renderer: mapProps(props => ({
    ...props,
    value: _isNil(frozenInventoryPartDetails)
      ? PartMasterReader.vendorCode(frozenOemPartMasterDetails)
      : inventoryPartReader.vendorCode(frozenInventoryPartDetails?.part),
  }))(TextField),
  renderOptions: {
    label: __('Vendor Code'),
    placeholder: NO_DATA,
    disabled: true,
    isEditing,
    pinnedFieldIds,
  },
});

const getVendorPriceFieldConfig = ({
  frozenInventoryPartDetails,
  frozenOemPartMasterDetails,
  isEditing,
  pinnedFieldIds,
}) => ({
  renderer: mapProps(props => ({
    ...props,
    value: _isNil(frozenInventoryPartDetails)
      ? PartMasterReader.pippPrice(frozenOemPartMasterDetails)
      : inventoryPartReader.pippPrice(frozenInventoryPartDetails?.part),
  }))(NumberField),
  renderOptions: {
    label: __('Vendor Price'),
    isCurrencyField: true,
    placeholder: NO_DATA,
    disabled: true,
    isEditing,
    pinnedFieldIds,
  },
});

export const getMBPartFieldsConfig = ({
  frozenInventoryPartDetails,
  frozenOemPartMasterDetails,
  isEditing,
  pinnedFieldIds,
}) => ({
  [ADDITIONAL_DETAILS_FORM_KEY.PROMOTIONAL_VALUE]: getPromotionPriceFieldConfig({
    frozenInventoryPartDetails,
    frozenOemPartMasterDetails,
    isEditing,
    pinnedFieldIds,
  }),
  [ADDITIONAL_DETAILS_FORM_KEY.PART_DEMAND_CODE]: getPartDemandCodeFieldConfig({
    frozenInventoryPartDetails,
    frozenOemPartMasterDetails,
    isEditing,
    pinnedFieldIds,
  }),
  [ADDITIONAL_DETAILS_FORM_KEY.MARKETING_CODE]: getMarketingCodeFieldConfig({
    frozenInventoryPartDetails,
    frozenOemPartMasterDetails,
    isEditing,
    pinnedFieldIds,
  }),
  [ADDITIONAL_DETAILS_FORM_KEY.MARKETING_CODE]: getMarketingCodeFieldConfig({
    frozenInventoryPartDetails,
    frozenOemPartMasterDetails,
    isEditing,
    pinnedFieldIds,
  }),
  [ADDITIONAL_DETAILS_FORM_KEY.SHELF_LIFE]: getShelfLifeFieldConfig({
    frozenInventoryPartDetails,
    frozenOemPartMasterDetails,
    isEditing,
    pinnedFieldIds,
  }),
  [ADDITIONAL_DETAILS_FORM_KEY.VENDOR_CODE]: getVendorCodeFieldConfig({
    frozenInventoryPartDetails,
    frozenOemPartMasterDetails,
    isEditing,
    pinnedFieldIds,
  }),
  [ADDITIONAL_DETAILS_FORM_KEY.VIN_REQUIRED]: getVinRequiredFieldConfig({
    frozenInventoryPartDetails,
    frozenOemPartMasterDetails,
    isEditing,
    pinnedFieldIds,
  }),
  [ADDITIONAL_DETAILS_FORM_KEY.VENDOR_PRICE]: getVendorPriceFieldConfig({
    frozenInventoryPartDetails,
    frozenOemPartMasterDetails,
    isEditing,
    pinnedFieldIds,
  }),
});
