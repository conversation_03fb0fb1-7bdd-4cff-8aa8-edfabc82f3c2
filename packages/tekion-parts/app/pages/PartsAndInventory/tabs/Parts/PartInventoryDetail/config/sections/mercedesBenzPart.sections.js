import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import { filterRows } from '@tekion/tekion-components/src/organisms/FormBuilder/utils/general';

import { getIsAverageCostSettingEnabled } from 'helpers/partsSettings.helpers';

import { FORM_SECTION_KEY_VS_LABEL, FORM_SECTION_KEY } from 'pages/PartsAndInventory/constants/inventoryForm.constants';

import { getIsDealerCountryCodeCanada, isInchCape, isRRG } from 'utils/general';

import {
  ADDITIONAL_DETAILS_FORM_KEY,
  BASIC_DETAILS_FORM_KEY,
  PART_PRICING_FORM_KEY,
} from '../../constants/form.constants';
import { getCustomizedPriceFieldIds } from '../../helpers/general.helpers';
import { getTyreInformationSections } from '../../helpers/part.helpers';
import styles from '../../partInventoryDetail.module.scss';

import {
  getDefaultStockingDetailsSection,
  getDefaultBinDetailsSection,
  getDefaultLinkedPartSection,
  getDefaultPartGroupsSection,
  getDefaultFractionalSaleSection,
} from './general.sections';

const ROW_CHECK = {
  [BASIC_DETAILS_FORM_KEY.AUTO_UPDATE_FLAG]: ({ isInCreateMode }) => !isInCreateMode,
  [PART_PRICING_FORM_KEY.AVERAGE_COST_PRICE]: ({ isAverageCostEnabled, isInCreateMode }) =>
    isAverageCostEnabled && !isInCreateMode,
  [ADDITIONAL_DETAILS_FORM_KEY.PROMOTIONAL_VALUE]: () => getIsDealerCountryCodeCanada(),
};

const getBasicDetailsSection = ({ isInCreateMode }) => {
  const columns = [
    ...filterRows(
      [
        BASIC_DETAILS_FORM_KEY.PART,
        BASIC_DETAILS_FORM_KEY.PART_NAME,
        BASIC_DETAILS_FORM_KEY.BRAND_CODE,
        BASIC_DETAILS_FORM_KEY.OEM_CODE,
        BASIC_DETAILS_FORM_KEY.UNIT_PACK_QTY,
        BASIC_DETAILS_FORM_KEY.GROUP,
        BASIC_DETAILS_FORM_KEY.AUTO_UPDATE_FLAG,
        BASIC_DETAILS_FORM_KEY.PART_COMMENT,
      ],
      { isInCreateMode },
      ROW_CHECK,
    ),
  ];
  return [
    {
      header: {
        hashId: FORM_SECTION_KEY.BASIC_DETAILS,
      },
      subHeader: {
        label: FORM_SECTION_KEY_VS_LABEL[FORM_SECTION_KEY.BASIC_DETAILS],
        className: styles.subHeader,
      },
      className: styles.cardSection,
      rows: [
        {
          columns,
          className: styles.columnContainer,
        },
      ],
    },
  ];
};

const getAdditionalDetailsSection = ({ isInCreateMode, isTyreGroupPresentForPart }) => {
  const additionalColumns = [
    ADDITIONAL_DETAILS_FORM_KEY.UNIT_OF_MEASURE,
    ADDITIONAL_DETAILS_FORM_KEY.ABCD_CODE,
    ADDITIONAL_DETAILS_FORM_KEY.MFR_CONTROLLED,
    ADDITIONAL_DETAILS_FORM_KEY.MFR_CONTROL_UNIT,
    ADDITIONAL_DETAILS_FORM_KEY.REPACK_CODE,
    ADDITIONAL_DETAILS_FORM_KEY.RE_MANUFACTURER_INDICATOR,
    ADDITIONAL_DETAILS_FORM_KEY.CLASS_CODE,
    ADDITIONAL_DETAILS_FORM_KEY.PART_DEMAND_CODE,
    ADDITIONAL_DETAILS_FORM_KEY.FRANCHISE_CODE,
    ADDITIONAL_DETAILS_FORM_KEY.MARKETING_CODE,
    ADDITIONAL_DETAILS_FORM_KEY.MATERIAL_RETURN_CODE,
    ADDITIONAL_DETAILS_FORM_KEY.PART_CLASS_CODE,
    ADDITIONAL_DETAILS_FORM_KEY.SHELF_LIFE,
    ADDITIONAL_DETAILS_FORM_KEY.VENDOR_CODE,
    ADDITIONAL_DETAILS_FORM_KEY.VENDOR_PRICE,
    ADDITIONAL_DETAILS_FORM_KEY.VIN_REQUIRED,
    ADDITIONAL_DETAILS_FORM_KEY.PROMOTIONAL_VALUE,
    ...(isInchCape() || isRRG() ? [ADDITIONAL_DETAILS_FORM_KEY.TAX_CODE] : EMPTY_ARRAY),
    ...getTyreInformationSections({ isInCreateMode, isTyreGroupPresentForPart }),
  ];
  return [
    {
      header: {
        hashId: FORM_SECTION_KEY.ADDITIONAL_DETAILS,
      },
      subHeader: {
        label: FORM_SECTION_KEY_VS_LABEL[FORM_SECTION_KEY.ADDITIONAL_DETAILS],
        className: styles.subHeader,
      },
      className: styles.cardSection,
      rows: [
        {
          columns: filterRows(additionalColumns, EMPTY_OBJECT, ROW_CHECK),
          className: styles.columnContainer,
        },
      ],
    },
  ];
};

const getPricingDetailsSection = ({ customizedPriceDetailsByIds, isInCreateMode }) => [
  {
    subHeader: {
      label: FORM_SECTION_KEY_VS_LABEL[FORM_SECTION_KEY.PRICING_DETAILS],
      className: styles.subHeader,
    },
    header: {
      hashId: FORM_SECTION_KEY.PRICING_DETAILS,
    },
    className: styles.cardSection,
    rows: [
      {
        columns: filterRows(
          [
            PART_PRICING_FORM_KEY.COST_PRICE,
            PART_PRICING_FORM_KEY.AVERAGE_COST_PRICE,
            PART_PRICING_FORM_KEY.LIST_PRICE,
            PART_PRICING_FORM_KEY.TRADE_PRICE,
            PART_PRICING_FORM_KEY.COMP_PRICE,
            PART_PRICING_FORM_KEY.CORE_VALUE,
            PART_PRICING_FORM_KEY.MPL_PRICE,
          ],
          { isAverageCostEnabled: getIsAverageCostSettingEnabled(), isInCreateMode },
          ROW_CHECK,
        ),
        className: styles.columnContainer,
      },
      {
        columns: getCustomizedPriceFieldIds(customizedPriceDetailsByIds),
        className: styles.columnContainer,
      },
    ],
  },
];

export const getMercedesBenzPartSections = ({
  isInCreateMode,
  customizedPriceDetailsByIds,
  isEditing,
  isTyreGroupPresentForPart,
  onAction,
  isFractionalSaleEnabled,
}) => [
  ...getBasicDetailsSection({ isInCreateMode }),
  ...getDefaultStockingDetailsSection({ isEditing }),
  ...getDefaultBinDetailsSection({ isInCreateMode }),
  ...getPricingDetailsSection({ customizedPriceDetailsByIds, isInCreateMode }),
  ...getDefaultLinkedPartSection({ isInCreateMode }),
  ...getDefaultPartGroupsSection({ isInCreateMode }),
  ...getAdditionalDetailsSection({ isInCreateMode, isTyreGroupPresentForPart }),
  ...getDefaultFractionalSaleSection({ onAction, isEditing, isFractionalSaleEnabled }),
];
