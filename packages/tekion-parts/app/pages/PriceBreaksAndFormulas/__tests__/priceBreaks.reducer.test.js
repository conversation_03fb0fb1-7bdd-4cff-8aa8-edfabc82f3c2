import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import ACTIONS from '../actionTypes';
import priceBreaks from '../priceBreaks.reducer';

describe('PriceBreaksAndFormulas - reducer', () => {
  const initialState = {
    priceBreaksList: {
      isError: false,
      data: EMPTY_ARRAY,
      isLoading: false,
      error: EMPTY_OBJECT,
    },
    parts: {
      isError: false,
      data: EMPTY_ARRAY,
      isLoading: true,
      error: EMPTY_OBJECT,
    },
    addPriceBreak: {
      isError: false,
      data: EMPTY_OBJECT,
      isLoading: false,
      error: EMPTY_OBJECT,
      fetched: false,
    },
  };

  it('should return the initial state', () => {
    expect(priceBreaks(undefined, EMPTY_OBJECT)).toEqual(initialState);
  });

  it('should handle GET_PRICE_BREAKS', () => {
    const action = { type: ACTIONS.GET_PRICE_BREAKS };
    const expectedState = {
      ...initialState,
      priceBreaksList: {
        isError: false,
        data: EMPTY_ARRAY,
        isLoading: true,
        error: EMPTY_OBJECT,
      },
      addPriceBreak: {
        ...initialState.addPriceBreak,
        fetched: false,
      },
    };
    expect(priceBreaks(initialState, action)).toEqual(expectedState);
  });

  it('should handle GET_PRICE_BREAKS_SUCCESS', () => {
    const payload = [{ id: 1, name: 'Price Break 1' }];
    const action = { type: ACTIONS.GET_PRICE_BREAKS_SUCCESS, payload };
    const expectedState = {
      ...initialState,
      priceBreaksList: {
        isError: false,
        data: payload,
        isLoading: false,
        error: EMPTY_OBJECT,
      },
    };
    expect(priceBreaks(initialState, action)).toEqual(expectedState);
  });

  it('should handle GET_PRICE_BREAKS_ERROR', () => {
    const payload = { message: 'Error fetching price breaks' };
    const action = { type: ACTIONS.GET_PRICE_BREAKS_ERROR, payload };
    const expectedState = {
      ...initialState,
      priceBreaksList: {
        isError: true,
        data: EMPTY_ARRAY,
        error: payload,
        isLoading: false,
      },
    };
    expect(priceBreaks(initialState, action)).toEqual(expectedState);
  });

  it('should handle GET_PARTS', () => {
    const action = { type: ACTIONS.GET_PARTS };
    const expectedState = {
      ...initialState,
      parts: {
        isError: false,
        data: EMPTY_ARRAY,
        isLoading: true,
        error: EMPTY_OBJECT,
      },
      addPriceBreak: {
        ...initialState.addPriceBreak,
        fetched: false,
      },
    };
    expect(priceBreaks(initialState, action)).toEqual(expectedState);
  });

  it('should handle GET_PARTS_SUCCESS', () => {
    const payload = [{ id: 1, name: 'Part 1' }];
    const action = { type: ACTIONS.GET_PARTS_SUCCESS, payload };
    const expectedState = {
      ...initialState,
      parts: {
        isError: false,
        data: payload,
        isLoading: false,
        error: EMPTY_OBJECT,
      },
    };
    expect(priceBreaks(initialState, action)).toEqual(expectedState);
  });

  it('should handle GET_PARTS_ERROR', () => {
    const payload = { message: 'Error fetching parts' };
    const action = { type: ACTIONS.GET_PARTS_ERROR, payload };
    const expectedState = {
      ...initialState,
      parts: {
        isError: true,
        data: EMPTY_ARRAY,
        error: payload,
        isLoading: false,
      },
    };
    expect(priceBreaks(initialState, action)).toEqual(expectedState);
  });

  it('should handle ADD_PRICE_BREAK', () => {
    const action = { type: ACTIONS.ADD_PRICE_BREAK };
    const expectedState = {
      ...initialState,
      addPriceBreak: {
        isError: false,
        data: EMPTY_ARRAY,
        isLoading: true,
        error: EMPTY_OBJECT,
        fetched: false,
      },
    };
    expect(priceBreaks(initialState, action)).toEqual(expectedState);
  });

  it('should handle ADD_PRICE_BREAK_SUCCESS', () => {
    const payload = { id: 1, name: 'New Price Break' };
    const action = { type: ACTIONS.ADD_PRICE_BREAK_SUCCESS, payload };
    const expectedState = {
      ...initialState,
      addPriceBreak: {
        isError: false,
        data: payload,
        isLoading: false,
        error: EMPTY_OBJECT,
        fetched: true,
      },
    };
    expect(priceBreaks(initialState, action)).toEqual(expectedState);
  });

  it('should handle ADD_PRICE_BREAK_FAILURE', () => {
    const payload = { message: 'Error adding price break' };
    const action = { type: ACTIONS.ADD_PRICE_BREAK_FAILURE, payload };
    const expectedState = {
      ...initialState,
      addPriceBreak: {
        isError: true,
        data: EMPTY_ARRAY,
        error: payload,
        isLoading: false,
        fetched: false,
      },
    };
    expect(priceBreaks(initialState, action)).toEqual(expectedState);
  });
});
