import { EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';

import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';

import { isAPICallSuccessful } from 'helpers/utils/validation';

import types from '../actionTypes';
import * as actions from '../priceBreaks.actions';
import PriceBreaksAPI from '../priceBreaks.api';

jest.mock('../priceBreaks.api');
jest.mock('helpers/utils/validation', () => ({
  isAPICallSuccessful: jest.fn(),
}));
jest.mock('@tekion/tekion-components/src/organisms/NotificationWrapper', () => ({
  ...jest.requireActual('@tekion/tekion-components/src/organisms/NotificationWrapper'),
  toaster: jest.fn(),
}));

describe('PriceBreaksAndFormulas - Actions', () => {
  const dispatch = jest.fn();

  describe('getPriceBreaks', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should get price breaks on success', async () => {
      const mockResponse = { data: { data: { priceBreaks: EMPTY_ARRAY } } };
      PriceBreaksAPI.getPriceBreaksList.mockResolvedValue(mockResponse);
      isAPICallSuccessful.mockReturnValue(true);
      await actions.getPriceBreaks()(dispatch);
      expect(dispatch.mock.calls[0][0]).toEqual({
        type: types.GET_PRICE_BREAKS,
      });
      expect(dispatch.mock.calls[1][0]).toEqual({
        type: types.GET_PRICE_BREAKS_SUCCESS,
        payload: mockResponse.data.data,
      });
    });

    it('should dispatch price breaks failure on isAPICallSuccessful fail', async () => {
      const mockResponse = { data: { data: { priceBreaks: EMPTY_ARRAY } } };
      PriceBreaksAPI.getPriceBreaksList.mockResolvedValue(mockResponse);
      isAPICallSuccessful.mockReturnValue(false);
      await actions.getPriceBreaks()(dispatch);
      expect(dispatch.mock.calls[0][0]).toEqual({
        type: types.GET_PRICE_BREAKS,
      });
      expect(dispatch.mock.calls[1][0]).toEqual({
        type: types.GET_PRICE_BREAKS_ERROR,
        payload: mockResponse.data,
      });
    });

    it('should throw error when api call fails', async () => {
      const mockResponse = 'error';
      PriceBreaksAPI.getPriceBreaksList.mockRejectedValue(mockResponse);
      await actions.getPriceBreaks()(dispatch);
      expect(dispatch.mock.calls[0][0]).toEqual({
        type: types.GET_PRICE_BREAKS,
      });
      expect(dispatch.mock.calls[1][0]).toEqual({
        type: types.GET_PRICE_BREAKS_ERROR,
        payload: mockResponse,
      });
    });
  });

  describe('addPriceBreak', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should add price breaks on success', async () => {
      const mockResponse = { data: { data: { priceBreaks: EMPTY_ARRAY } } };
      PriceBreaksAPI.submitPriceBreaks.mockResolvedValue(mockResponse);
      isAPICallSuccessful.mockReturnValue(true);
      await actions.addPriceBreak()(dispatch);
      expect(dispatch.mock.calls[0][0]).toEqual({
        type: types.ADD_PRICE_BREAK,
      });
      expect(dispatch.mock.calls[1][0]).toEqual({
        type: types.ADD_PRICE_BREAK_SUCCESS,
        payload: mockResponse.data.data,
      });
    });

    it('should dispatch price breaks failure on isAPICallSuccessful fail', async () => {
      const mockResponse = { data: { data: { priceBreaks: EMPTY_ARRAY } } };
      PriceBreaksAPI.submitPriceBreaks.mockResolvedValue(mockResponse);
      isAPICallSuccessful.mockReturnValue(false);
      await actions.addPriceBreak()(dispatch);
      expect(dispatch.mock.calls[0][0]).toEqual({
        type: types.ADD_PRICE_BREAK,
      });
      expect(dispatch.mock.calls[1][0]).toEqual({
        type: types.ADD_PRICE_BREAK_FAILURE,
        payload: mockResponse.data,
      });
      expect(toaster).toHaveBeenCalledWith(TOASTER_TYPE.ERROR, actions.MESSAGES.PRICE_BREAK_FAILED);
    });

    it('should throw error when api call fails', async () => {
      const mockResponse = 'error';
      PriceBreaksAPI.submitPriceBreaks.mockRejectedValue(mockResponse);
      await actions.addPriceBreak()(dispatch);
      expect(dispatch.mock.calls[0][0]).toEqual({
        type: types.ADD_PRICE_BREAK,
      });
      expect(dispatch.mock.calls[1][0]).toEqual({
        type: types.ADD_PRICE_BREAK_FAILURE,
        payload: mockResponse,
      });
      expect(toaster).toHaveBeenCalledWith(TOASTER_TYPE.ERROR, actions.MESSAGES.PRICE_BREAK_FAILED);
    });
  });

  describe('editPriceBreak', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should edit price breaks on success', async () => {
      const mockResponse = { data: { data: { priceBreaks: EMPTY_ARRAY } } };
      PriceBreaksAPI.editPriceBreak.mockResolvedValue(mockResponse);
      isAPICallSuccessful.mockReturnValue(true);
      await actions.editPriceBreak()(dispatch);
      expect(dispatch.mock.calls[0][0]).toEqual({
        type: types.ADD_PRICE_BREAK,
      });
      expect(dispatch.mock.calls[1][0]).toEqual({
        type: types.ADD_PRICE_BREAK_SUCCESS,
        payload: mockResponse.data.data,
      });
    });

    it('should dispatch price breaks failure on isAPICallSuccessful fail', async () => {
      const mockResponse = { data: { data: { priceBreaks: EMPTY_ARRAY } } };
      PriceBreaksAPI.editPriceBreak.mockResolvedValue(mockResponse);
      isAPICallSuccessful.mockReturnValue(false);
      await actions.editPriceBreak()(dispatch);
      expect(dispatch.mock.calls[0][0]).toEqual({
        type: types.ADD_PRICE_BREAK,
      });
      expect(dispatch.mock.calls[1][0]).toEqual({
        type: types.ADD_PRICE_BREAK_FAILURE,
        payload: mockResponse.data,
      });
      expect(toaster).toHaveBeenCalledWith(TOASTER_TYPE.ERROR, actions.MESSAGES.PRICE_BREAK_FAILED);
    });

    it('should throw error when api call fails', async () => {
      const mockResponse = 'error';
      PriceBreaksAPI.editPriceBreak.mockRejectedValue(mockResponse);
      await actions.editPriceBreak()(dispatch);
      expect(dispatch.mock.calls[0][0]).toEqual({
        type: types.ADD_PRICE_BREAK,
      });
      expect(dispatch.mock.calls[1][0]).toEqual({
        type: types.ADD_PRICE_BREAK_FAILURE,
        payload: mockResponse,
      });
      expect(toaster).toHaveBeenCalledWith(TOASTER_TYPE.ERROR, actions.MESSAGES.PRICE_BREAK_FAILED);
    });
  });

  describe('checkPriceBreak', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should edit price breaks on success', async () => {
      const mockResponse = { data: { data: { priceBreaks: EMPTY_ARRAY } } };
      PriceBreaksAPI.getCustomziedPrice.mockResolvedValue(mockResponse);
      isAPICallSuccessful.mockReturnValue(true);
      await actions.checkPriceBreak()(dispatch);
      expect(dispatch.mock.calls[0][0]).toEqual({
        type: types.ADD_PRICE_BREAK_SUCCESS,
        payload: mockResponse.data.data,
      });
    });

    it('should dispatch price breaks failure on isAPICallSuccessful fail', async () => {
      const mockResponse = { data: { data: { priceBreaks: EMPTY_ARRAY } } };
      PriceBreaksAPI.getCustomziedPrice.mockResolvedValue(mockResponse);
      isAPICallSuccessful.mockReturnValue(false);
      await actions.checkPriceBreak()(dispatch);
      expect(dispatch.mock.calls[0][0]).toEqual({
        type: types.ADD_PRICE_BREAK_FAILURE,
        payload: mockResponse.data,
      });
    });

    it('should throw error when api call fails', async () => {
      const mockResponse = 'error';
      PriceBreaksAPI.getCustomziedPrice.mockRejectedValue(mockResponse);
      await actions.checkPriceBreak()(dispatch);
      expect(dispatch.mock.calls[0][0]).toEqual({
        type: types.ADD_PRICE_BREAK_FAILURE,
        payload: mockResponse,
      });
    });
  });

  describe('deletePriceFormula', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should edit price breaks on success', async () => {
      const mockResponse = { data: { data: { priceBreaks: EMPTY_ARRAY } } };
      PriceBreaksAPI.deletePriceFormula.mockResolvedValue(mockResponse);
      isAPICallSuccessful.mockReturnValue(true);
      await actions.deletePriceFormula()(dispatch);
      expect(dispatch.mock.calls[0][0]).toEqual({
        type: types.ADD_PRICE_BREAK_SUCCESS,
        payload: mockResponse.data.data,
      });
      expect(toaster).toHaveBeenCalledWith(TOASTER_TYPE.SUCCESS, actions.MESSAGES.GENERAL_SUCCESS);
    });

    it('should dispatch price breaks failure on isAPICallSuccessful fail', async () => {
      const mockResponse = { data: { data: { priceBreaks: EMPTY_ARRAY } } };
      PriceBreaksAPI.deletePriceFormula.mockResolvedValue(mockResponse);
      isAPICallSuccessful.mockReturnValue(false);
      await actions.deletePriceFormula()(dispatch);
      expect(dispatch.mock.calls[0][0]).toEqual({
        type: types.ADD_PRICE_BREAK_FAILURE,
        payload: mockResponse.data,
      });
      expect(toaster).toHaveBeenCalledWith(TOASTER_TYPE.ERROR, actions.MESSAGES.SOMETHING_WENT_WRONG);
    });

    it('should throw error when api call fails', async () => {
      const mockResponse = 'error';
      PriceBreaksAPI.deletePriceFormula.mockRejectedValue(mockResponse);
      await actions.deletePriceFormula()(dispatch);
      expect(dispatch.mock.calls[0][0]).toEqual({
        type: types.ADD_PRICE_BREAK_FAILURE,
        payload: mockResponse,
      });
      expect(toaster).toHaveBeenCalledWith(TOASTER_TYPE.ERROR, actions.MESSAGES.SOMETHING_WENT_WRONG);
    });
  });

  describe('setSearchText', () => {
    it('should create action to set search text', () => {
      const searchText = 'test search';
      const expectedAction = {
        type: types.PRICE_BREAKS_SET_SEARCH_TEXT,
        payload: searchText,
      };
      expect(actions.setSearchText(searchText)).toEqual(expectedAction);
    });
  });
});
