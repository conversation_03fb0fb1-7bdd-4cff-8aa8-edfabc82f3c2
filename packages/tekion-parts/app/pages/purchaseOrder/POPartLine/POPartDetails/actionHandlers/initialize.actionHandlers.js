import _get from 'lodash/get';
import _head from 'lodash/head';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';
import _keyBy from 'lodash/keyBy';
import _map from 'lodash/map';
import _set from 'lodash/set';

import produce from 'immer';

import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import { getWithPartSearchResponseInFlatStructure } from '@tekion/tekion-business/src/appServices/parts/partsAndInventory/helpers/inventoryPart.helpers';
import poPartLineReader from '@tekion/tekion-business/src/readers/parts/POPartLine';

import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';

import { fetchPartsByPartId } from 'actions/parts.actions';

import { PO_TAB_TYPES } from 'pages/purchaseOrder/constants/general';

import { getPOListViewRoute } from 'shared/PurchaseOrder/purchaseOrder.routes.helpers';

import { fetchPOPartListData } from '../poPartDetails.actions';
import { VENDOR_ID } from '../poPartDetails.constants';
import { createInitialValues, getPOPartListPayload, getAlternatePartIds } from '../poPartDetails.helpers';
export const INITIALIZE_ACTION_TYPE = {
  INIT_FORM: 'INIT_FORM',
};

const resolvePartDetails = async ({ params, setState }) => {
  const { partId } = params || EMPTY_OBJECT;
  setState(({ resolvedPart = EMPTY_OBJECT }) => ({
    resolvedPart: produce(resolvedPart, draft => {
      _set(draft, 'isLoading', true);
    }),
  }));
  try {
    const resolvedParts = await fetchPartsByPartId([partId])();
    const formattedPart = getWithPartSearchResponseInFlatStructure(_head(resolvedParts));

    // TODO: once the new inventory service is api is used,
    //  we will be using inventoryPart.domain to get the alternate part ids
    const additionPartIdsToBeResolved = getAlternatePartIds(formattedPart);

    const additionalResolvedParts = _isEmpty(additionPartIdsToBeResolved)
      ? EMPTY_ARRAY
      : await fetchPartsByPartId(additionPartIdsToBeResolved)();
    const resolvedPartsById = _keyBy(
      [formattedPart, ..._map(additionalResolvedParts, getWithPartSearchResponseInFlatStructure)],
      'partId',
    );

    setState(({ resolvedPart = EMPTY_OBJECT }) => ({
      resolvedPart: produce(resolvedPart, draft => {
        _set(draft, 'isLoading', false);
        _set(draft, 'data', resolvedPartsById);
      }),
    }));
  } catch {
    toaster(TOASTER_TYPE.ERROR, __('Failed to fetch PO Part Details'));
    setState(({ resolvedPart = EMPTY_OBJECT }) => ({
      resolvedPart: produce(resolvedPart, draft => {
        _set(draft, 'isLoading', false);
      }),
    }));
  }
};

const resolveVendorDetails = ({ params, setState }) => {
  const { vendorId } = params || EMPTY_OBJECT;
  // TODO: Fetch Vendor Details here once the vendor management API is available
  const response = [{ vendorId, vendorName: 'Renault' }];
  const resolvedVendorsById = _keyBy(response, 'vendorId');
  setState({
    resolvedVendor: { isLoading: false, data: resolvedVendorsById },
  });
};

const fetchAndInitializeStateWithPOPartDetails = async ({ params, getState, setState }) => {
  const currentState = getState();
  const { navigate } = currentState;

  setState({ isLoading: true });
  try {
    const payload = getPOPartListPayload(params);
    const response = await fetchPOPartListData(payload);
    const orderData = _get(response, ['hits', 0], EMPTY_OBJECT);
    if (_isEmpty(orderData)) throw new Error('Part details not found'); // we are throwing error if the orderdata is empty
    const partId = poPartLineReader.partId(orderData);
    setState({
      isLoading: false,
      frozenPoPartDetails: Object.freeze(orderData),
      values: createInitialValues(orderData),
    });
    resolvePartDetails({ params: { partId }, getState, setState });
    // We will get the vendor id from the orderData once the vendor integration is done
    resolveVendorDetails({ params: { vendorId: VENDOR_ID }, getState, setState });
  } catch (err) {
    toaster(TOASTER_TYPE.ERROR, __('Failed to fetch PO Parts Details'));
    navigate(getPOListViewRoute({ tabType: PO_TAB_TYPES.PART_LINE }));
  }
};

const initialize = ({ setState, getState }) => {
  const { navigate, params = EMPTY_OBJECT } = getState();
  const poId = _get(params, 'poId');
  const orderType = _get(params, 'orderType');
  const poLineId = _get(params, 'poLineId');
  if (!_isNil(poId) && !_isEmpty(orderType) && !_isNil(poLineId)) {
    fetchAndInitializeStateWithPOPartDetails({
      params: { poId, orderType, poLineId },
      setState,
      getState,
    });
    return;
  }
  toaster(TOASTER_TYPE.ERROR, __('Incorrect params to load PO Part Details'));
  navigate(getPOListViewRoute({ tabType: PO_TAB_TYPES.PART_LINE }));
};

export const INITIALIZE_ACTION_HANDLERS = {
  [INITIALIZE_ACTION_TYPE.INIT_FORM]: initialize,
};
