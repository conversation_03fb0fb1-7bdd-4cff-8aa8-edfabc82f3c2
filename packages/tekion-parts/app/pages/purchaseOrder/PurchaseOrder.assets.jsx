import React, { PureComponent } from 'react';

import { connect } from 'react-redux';

import _get from 'lodash/get';
import _includes from 'lodash/includes';
import _keys from 'lodash/keys';

import PropTypes from 'prop-types';
import { compose } from 'recompose';

import Button from '@tekion/tekion-components/src/atoms/Button';
import withAsyncReducer from '@tekion/tekion-components/src/connectors/withAsyncReducer';
import withRouter from '@tekion/tekion-components/src/hoc/withRouter';

import ErrorBoundary from '@tekion/tekion-widgets/src/molecules/ErrorBoundary';
import { getPrintModalPreference } from '@tekion/tekion-widgets/src/organisms/AppSkeleton/appSkeleton/skeleton.selector';

import { getPOListViewRoute } from 'shared/PurchaseOrder/purchaseOrder.routes.helpers';

import {
  fetchAPSetupAccountsAction,
  fetchOemPoCreateOrUpdateRequestStateAction,
  setOemPoCreateOrUpdateRequestInProgress,
} from './actions/purchaseOrder.actions';
import ASSET_TYPES from './constants/assetTypes';
import { PURCHASE_ORDER_STORE_KEY } from './constants/general';
import CreditPurchaseOrder from './CreditPurchaseOrder';
import { checkIsManualReceipt } from './helpers/PurchaseOrder.general';
import OEMPurchaseOrder from './OEMPurchaseOrder';
import Parts from './organisms/POParts';
import Sublet from './organisms/POSublet';
import POPartLine from './POPartLine';
import styles from './purchaseOrder.module.scss';
import purchaseOrderReducer from './reducers/purchaseOrder.reducer';

const PURCHASE_ORDER_ASSETS = {
  [ASSET_TYPES.PARTS]: Parts,
  [ASSET_TYPES.PART_LINE]: POPartLine,
  [ASSET_TYPES.SUBLET]: Sublet,
  [ASSET_TYPES.OEM_ORDER]: OEMPurchaseOrder,
  [ASSET_TYPES.MISC]: Parts,
  [ASSET_TYPES.NON_OEM_SPECIAL_ORDER]: Parts,
  [ASSET_TYPES.CREDIT_PO]: CreditPurchaseOrder,
};

class PurchaseOrderAssets extends PureComponent {
  constructor(props) {
    super(props);
    const { params, navigate } = this.props;
    const assetType = _get(params, 'assetType');
    const isValidAssetType = _includes(_keys(PURCHASE_ORDER_ASSETS), assetType);
    if (!isValidAssetType) {
      navigate(getPOListViewRoute());
    }
    this.state = {
      isValidAssetType,
    };
  }

  async componentDidMount() {
    const { fetchAPSetupAccounts } = this.props;
    await fetchAPSetupAccounts();
  }

  redirectToListView = () => {
    const { navigate } = this.props;
    navigate(getPOListViewRoute());
  };

  renderMessage = () => (
    <div className="flex flex-column align-items-center m-t-24">
      <div className={styles.permissionError}>{__('You do not have the permissions to access current document.')}</div>
      <Button view={Button.VIEW.TERTIARY} onClick={this.redirectToListView}>
        {__('Home')}
      </Button>
    </div>
  );

  render() {
    const { isValidAssetType } = this.state;
    const { props } = this;
    const assetType = _get(props, 'params.assetType');

    if (!isValidAssetType) {
      return null;
    }

    const isManualReceipt = checkIsManualReceipt(props);
    const PurchaseOrderAsset = PURCHASE_ORDER_ASSETS[assetType];
    return (
      <ErrorBoundary fallback={this.renderMessage}>
        <PurchaseOrderAsset {...props} assetType={assetType} isManualReceipt={isManualReceipt} />
      </ErrorBoundary>
    );
  }
}

PurchaseOrderAssets.propTypes = {
  fetchAPSetupAccounts: PropTypes.func.isRequired,
};

const mapStateToProps = state => ({
  showPrintOptionForUser: getPrintModalPreference(state),
});

const mapDispatchToProps = {
  setOemPoCreateOrUpdateRequestInProgress,
  fetchAPSetupAccounts: fetchAPSetupAccountsAction,
  checkOemPoCreateOrUpdateRequestState: fetchOemPoCreateOrUpdateRequestStateAction,
};

export default compose(
  withAsyncReducer({
    storeKey: PURCHASE_ORDER_STORE_KEY,
    reducer: purchaseOrderReducer,
  }),
  withRouter,
  connect(mapStateToProps, mapDispatchToProps),
)(PurchaseOrderAssets);
