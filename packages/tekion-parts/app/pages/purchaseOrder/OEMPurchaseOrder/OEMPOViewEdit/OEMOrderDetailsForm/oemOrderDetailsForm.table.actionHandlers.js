import _findIndex from 'lodash/findIndex';
import _get from 'lodash/get';
import _head from 'lodash/head';
import _isEmpty from 'lodash/isEmpty';
import _isFunction from 'lodash/isFunction';
import _map from 'lodash/map';
import _omit from 'lodash/omit';
import _partition from 'lodash/partition';
import _toString from 'lodash/toString';

import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import partReader from '@tekion/tekion-base/readers/Part';
import { subtract, multiply } from '@tekion/tekion-base/utils/math.utils';
import Money from '@tekion/tekion-base/utils/money';

import { REMOVE_ACTION } from '@tekion/tekion-components/src/molecules/tableInputField/constants/general';
import TABLE_ACTION_TYPES from '@tekion/tekion-components/src/molecules/tableInputField/constants/TableInputField.actionTypes';

import { PART_LIST_ROW_ACTION_TYPES } from '../../../constants/purchaseOrder.actionTypes';
import { getFormattedPartNameFromPartDetails } from '../../../helpers/cancelPartFromPO.helpers';
import { OEM_ORDER_DETAILS_FORM_FIELDS } from '../oemPOViewEdit.constants';

import { getUnitCostAndOrderQtyOfRemovedItem } from './oemOrderDetailsForm.helpers';

const handleItemRemove = ({ getState, setState, params }) => {
  const { nestingPath, id, rowID } = params || EMPTY_OBJECT;
  const currentValuesState = _get(getState(), 'values', EMPTY_OBJECT);
  const currentFieldValue = _get(currentValuesState, id, EMPTY_ARRAY);
  const indexOfRowToDelete = _findIndex(currentFieldValue, row => _toString(row?.id) === _head(nestingPath));
  const {
    partsToDelete,
    values = EMPTY_OBJECT,
    errors = EMPTY_OBJECT,
    totalElements,
    totalPartsCount,
    totalPartsAmount,
    resolvedData = EMPTY_OBJECT,
    selectedRowIds,
  } = getState();

  const currentPartList = values[OEM_ORDER_DETAILS_FORM_FIELDS.ASSIGN_PARTS_LIST];
  const currentPartListWithIndex = _map(currentPartList, (partList, index) => ({
    ...partList,
    index,
  }));
  const [removedItemFromList, filteredPartList] = _partition(currentPartListWithIndex, { index: rowID });

  const currentFieldErrorState = _get(errors, OEM_ORDER_DETAILS_FORM_FIELDS.ASSIGN_PARTS_LIST, EMPTY_OBJECT);
  const filteredFieldErrorState = _omit(currentFieldErrorState, _head(nestingPath));

  const deletedRow = _head(removedItemFromList);
  const currentPartIdToRemove = _get(deletedRow, 'partId');
  const { unitCost, orderQtyFromPartLine } = getUnitCostAndOrderQtyOfRemovedItem({
    resolvedData,
    partIdToRemove: currentPartIdToRemove,
    deletedRowData: deletedRow,
  });
  const sanitizedFilteredPartList = _map(filteredPartList, partLine => _omit(partLine, ['index']));
  setState({
    totalElements: totalElements - 1,
    totalPartsCount: subtract(totalPartsCount, orderQtyFromPartLine),
    totalPartsAmount: Money.withPrecision(
      subtract(Money.toInt(totalPartsAmount), multiply(orderQtyFromPartLine, Money.toInt(unitCost))),
    ),
    values: {
      ...values,
      [OEM_ORDER_DETAILS_FORM_FIELDS.ASSIGN_PARTS_LIST]: sanitizedFilteredPartList,
    },
    errors: {
      ...errors,
      [OEM_ORDER_DETAILS_FORM_FIELDS.ASSIGN_PARTS_LIST]: filteredFieldErrorState,
    },
    partsToDelete: [...partsToDelete, _get(currentFieldValue, [indexOfRowToDelete, 'id'])],
    selectedRowIds: _omit(selectedRowIds, _head(nestingPath)),
  });
};

const openCancelPartModal = ({ setState, getState, params }) => {
  const { nestingPath } = params;
  const { values } = getState();
  const assignPartsList = values[OEM_ORDER_DETAILS_FORM_FIELDS.ASSIGN_PARTS_LIST];
  const index = _findIndex(assignPartsList, row => _toString(row?.id) === _head(nestingPath));
  const partDetailsValue = _get(assignPartsList, index, EMPTY_OBJECT);
  const partId = _get(partDetailsValue, 'partId');
  const partResolvedData = _get(getState(), ['resolvedData', 'part', 'data', [partId]]) || EMPTY_OBJECT;
  const selectedPartForCancelling = {
    ...partDetailsValue,
    partForDisplay: getFormattedPartNameFromPartDetails({
      selectedPart: partDetailsValue,
      partResolvedData,
      isOemPurchaseOrder: true,
    }),
    isPartPresentInInventory: !_isEmpty(partReader.partId(partDetailsValue)),
  };
  setState({ isCancelPartModalVisible: true, selectedPartForCancelling });
};

const TABLE_ACTION_TO_HANDLER = {
  [REMOVE_ACTION.id]: handleItemRemove,
  [PART_LIST_ROW_ACTION_TYPES.CANCEL_PART]: openCancelPartModal,
};

const handleTableActionClick = ({ getState, setState, params = EMPTY_OBJECT }) => {
  const { actionType } = params;
  const actionHandler = TABLE_ACTION_TO_HANDLER[actionType];
  if (_isFunction(actionHandler)) {
    actionHandler({ setState, getState, params });
  }
};

const TABLE_ACTION_HANDLERS = {
  [TABLE_ACTION_TYPES.TABLE_ACTION_CLICK]: handleTableActionClick,
};

export default TABLE_ACTION_HANDLERS;
