import _includes from 'lodash/includes';

import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import PO_STATUS from '@tekion/tekion-base/constants/purchaseOrder/status';
import { SUBLET_PO_RECIEVING_STATUS } from '@tekion/tekion-base/constants/purchaseOrder/subletPO.status';
import { RO_STATUS_VALUES as RO_STATUS } from '@tekion/tekion-base/constants/repairOrder/roStatus';
import purchaseOrderReader from '@tekion/tekion-base/readers/PurchaseOrder';

import { getSubletTotalAmount } from '@tekion/tekion-widgets/src/appServices/parts/organisms/SubletForm/helpers/general.helper';

import { hasEditPermissionsSubletPO, hasClosePermissionsSubletPO } from 'permissions/partsPermissions';

import PurchaseOrderDomain from 'shared/PurchaseOrder/domain/PurchaseOrder.domain';

import { isRRG, isInchCape, getIsSubletReceivingAndReissueIgnoreEnabled } from 'utils/general';

import { PURCHASE_ORDER_ACTIONS } from '../../../constants/general';
import modes from '../../../constants/modes';
import { isClosed, isSubmitted, isCancelled, isInvoiced } from '../../../utils/globalHelpers';
import { getOrderedActionMenuItems as _getOrderedActionMenuItems } from '../../../utils/purchaseOrder.utils';

const EDIT_SUBLET_MENU_ITEM = {
  label: __('Edit'),
  key: PURCHASE_ORDER_ACTIONS.EDIT,
};

const getViewPDFMenuItem = (isFetchingHtmlPdfConfiguration, isFetchingDocServicePDFs) => ({
  label: __('View Pdf'),
  key: PURCHASE_ORDER_ACTIONS.VIEW_PDF,
  disabled: isFetchingHtmlPdfConfiguration || isFetchingDocServicePDFs,
});

const PRINT_PDF_MENU_ITEM = {
  label: __('Print Pdf'),
  key: PURCHASE_ORDER_ACTIONS.PRINT_PDF,
};
const VOID_SUBLET_MENU_ITEM = {
  label: __('Void Sublet'),
  key: PURCHASE_ORDER_ACTIONS.VOID,
};
const REISSUE_SUBLET_MENU_ITEM = {
  label: __('Re-issue'),
  key: PURCHASE_ORDER_ACTIONS.RE_ISSUE,
};
const CLOSE_SUBLET_MENU_ITEM = {
  label: __('Close'),
  key: PURCHASE_ORDER_ACTIONS.CLOSE,
};
const PRE_INVOICE_SUBLET_ITEM = {
  label: __('Pre Invoice'),
  key: PURCHASE_ORDER_ACTIONS.PRE_INVOICE,
};

const getDraftViewMenuItems = (isFetchingHtmlPdfConfiguration, isFetchingDocServicePDFs, subletStatus) => {
  const isReceivedOrPartiallyReceived = _includes([PO_STATUS.RECEIVED, PO_STATUS.PARTIALLY_RECEIVED], subletStatus);
  return [
    /**
     * For Temporary Purpose we are hiding the Reissue option for received/partially received sublets
     * ...(isReceivedOrPartiallyReceived ? [REISSUE_SUBLET_MENU_ITEM] : [EDIT_SUBLET_MENU_ITEM]), ref: ENG-161748
     */
    getViewPDFMenuItem(isFetchingHtmlPdfConfiguration, isFetchingDocServicePDFs),
    PRINT_PDF_MENU_ITEM,
    ...(isReceivedOrPartiallyReceived ? EMPTY_ARRAY : [VOID_SUBLET_MENU_ITEM]),
    ...(subletStatus === PO_STATUS.RECEIVED ? [CLOSE_SUBLET_MENU_ITEM] : EMPTY_ARRAY),
  ];
};

const getCancelledViewMenuItems = (isFetchingHtmlPdfConfiguration, isFetchingDocServicePDFs) => [
  getViewPDFMenuItem(isFetchingHtmlPdfConfiguration, isFetchingDocServicePDFs),
  PRINT_PDF_MENU_ITEM,
];

const getClosedViewMenuItem = (isFetchingHtmlPdfConfiguration, isFetchingDocServicePDFs) => [
  getViewPDFMenuItem(isFetchingHtmlPdfConfiguration, isFetchingDocServicePDFs),
  PRINT_PDF_MENU_ITEM,
];

const getROInvoicedViewMenuItems = (isFetchingHtmlPdfConfiguration, isFetchingDocServicePDFs) => [
  getViewPDFMenuItem(isFetchingHtmlPdfConfiguration, isFetchingDocServicePDFs),
  PRINT_PDF_MENU_ITEM,
];

const getReissueViewMenuItems = isFetchingHtmlPdfConfiguration => [
  getViewPDFMenuItem(isFetchingHtmlPdfConfiguration),
  PRINT_PDF_MENU_ITEM,
];

const CREATE_MENU_ITEMS = EMPTY_ARRAY;

const getEditMenuItems = (subletStatus, isFetchingHtmlPdfConfiguration, isFetchingDocServicePDFs) => {
  if (subletStatus === PO_STATUS.REISSUED) {
    return getReissueViewMenuItems(isFetchingHtmlPdfConfiguration);
  }
  if (_includes([PO_STATUS.DRAFT, PO_STATUS.APPROVAL_DECLINED], subletStatus)) {
    return [VOID_SUBLET_MENU_ITEM];
  }
  return [getViewPDFMenuItem(isFetchingHtmlPdfConfiguration, isFetchingDocServicePDFs), PRINT_PDF_MENU_ITEM];
};

const getSubmittedMenuActions = ({
  values = EMPTY_OBJECT,
  orderInfo,
  isFetchingHtmlPdfConfiguration,
  isFetchingDocServicePDFs,
}) => {
  const { subletJobs } = values;
  const totalAmount = getSubletTotalAmount(subletJobs);
  const _hasEditPermissionsSubletPO = hasEditPermissionsSubletPO();
  const dueAmount = purchaseOrderReader.dueAmount(orderInfo);
  const isTemporaryVendor = purchaseOrderReader.isTemporaryVendor(orderInfo);
  const isAnyAmountInvoiced = PurchaseOrderDomain.isAnyAmountInvoiced(orderInfo);
  const isNotPreInvoiced = PurchaseOrderDomain.isNotPreInvoiced(orderInfo);

  const isFullyReceived = getIsSubletReceivingAndReissueIgnoreEnabled()
    ? purchaseOrderReader.status(orderInfo) === SUBLET_PO_RECIEVING_STATUS.RECEIVED
    : null;

  const isPartiallyReceived = getIsSubletReceivingAndReissueIgnoreEnabled()
    ? purchaseOrderReader.status(orderInfo) === SUBLET_PO_RECIEVING_STATUS.PARTIALLY_RECEIVED
    : null;

  const showPreIvoiceAction = !isTemporaryVendor && totalAmount && dueAmount && !(isRRG() || isInchCape());
  const BASE_ACTIONS = [...(showPreIvoiceAction ? [PRE_INVOICE_SUBLET_ITEM] : EMPTY_ARRAY)];
  const reIssueAction = [...(_hasEditPermissionsSubletPO ? [REISSUE_SUBLET_MENU_ITEM] : EMPTY_ARRAY)];
  const voidAction = [...(_hasEditPermissionsSubletPO ? [VOID_SUBLET_MENU_ITEM] : EMPTY_ARRAY)];
  const viewPdfAction = getViewPDFMenuItem(isFetchingHtmlPdfConfiguration, isFetchingDocServicePDFs);
  if (hasClosePermissionsSubletPO()) {
    return [
      ...(!isAnyAmountInvoiced ? reIssueAction : EMPTY_ARRAY),
      ...(isNotPreInvoiced && (isFullyReceived ?? true) ? [CLOSE_SUBLET_MENU_ITEM] : EMPTY_ARRAY),
      ...BASE_ACTIONS,
      viewPdfAction,
      PRINT_PDF_MENU_ITEM,
      ...(!isAnyAmountInvoiced && !(isPartiallyReceived ?? false) && !(isFullyReceived ?? false)
        ? voidAction
        : EMPTY_ARRAY),
    ];
  }
  return [...reIssueAction, ...BASE_ACTIONS, viewPdfAction, PRINT_PDF_MENU_ITEM, ...voidAction];
};

/**
 * Same logic for enabling pre-invoice in invoices tab is handled at
 * packages/tekion-parts/app/pages/purchaseOrder/organisms/POSublet/SubletPOFormPage/subletPOFormPage.helpers.js
 * Update at both the places if any changes are required for pre-invoice option.
 */

const getViewMenuItems = ({
  subletStatus,
  roStatus,
  values,
  orderInfo,
  isFetchingHtmlPdfConfiguration,
  isFetchingDocServicePDFs,
}) => {
  const isROInvoicedOrClosed = roStatus === RO_STATUS.INVOICED || roStatus === RO_STATUS.CLOSED;
  if (PurchaseOrderDomain.isApprovalRequestPending({ status: subletStatus })) {
    return EMPTY_ARRAY;
  }
  if (isClosed(subletStatus)) {
    return getClosedViewMenuItem(isFetchingHtmlPdfConfiguration, isFetchingDocServicePDFs);
  }
  if (isROInvoicedOrClosed && isSubmitted(subletStatus)) {
    return getSubmittedMenuActions({ values, orderInfo, isFetchingHtmlPdfConfiguration, isFetchingDocServicePDFs });
  }
  if (isCancelled(subletStatus)) {
    return getCancelledViewMenuItems(isFetchingHtmlPdfConfiguration, isFetchingDocServicePDFs);
  }
  if (isInvoiced(subletStatus)) {
    return getROInvoicedViewMenuItems(isFetchingHtmlPdfConfiguration, isFetchingDocServicePDFs);
  }
  if (isSubmitted(subletStatus)) {
    return getSubmittedMenuActions({ values, orderInfo, isFetchingHtmlPdfConfiguration, isFetchingDocServicePDFs });
  }
  return (
    hasEditPermissionsSubletPO() &&
    getDraftViewMenuItems(isFetchingHtmlPdfConfiguration, isFetchingDocServicePDFs, subletStatus)
  );
};

const getMenuItems = props => {
  const {
    mode,
    subletStatus,
    roStatus,
    invoiceStatus,
    values,
    orderInfo,
    isFetchingHtmlPdfConfiguration,
    isFetchingDocServicePDFs,
  } = props;
  switch (mode) {
    case modes.CREATE:
      return CREATE_MENU_ITEMS;

    case modes.EDIT:
      return getEditMenuItems(subletStatus, isFetchingHtmlPdfConfiguration, isFetchingDocServicePDFs);

    case modes.VIEW:
      return getViewMenuItems({
        subletStatus,
        invoiceStatus,
        roStatus,
        values,
        orderInfo,
        isFetchingHtmlPdfConfiguration,
        isFetchingDocServicePDFs,
      });

    default:
      return EMPTY_ARRAY;
  }
};

export const getOrderedActionMenuItems = props => _getOrderedActionMenuItems({ menuItems: getMenuItems(props) });

export const SUBLET_PO_FORM_ID = 'subletPOForm';

export const SUBLET_TOASTER_MESSAGES = {
  SUBMISSION_SUCCESS: __('Sublet Purchase Order successfully saved!'),
  FORM_ISSUE: __('Please correct the form issues!'),
  ERROR_ON_FETCH: __('Error while fetching Sublet!'),
  DEFAULT_SUCCESS: __('Action taken successfully!'),
  DEFAULT_ERROR: __('Something went wrong!'),
  ERROR_ON_SAVE: __('Error while saving Sublet!'),
  ERROR_ON_REISSUE: __('Unable to Re-Issue Sublet!'),
  VOID_SUCCESS: __('Sublet voided successfully!'),
  VOID_ERROR: __('Unable to Void Sublet!'),
  ERROR_ON_CLOSE: __('Unable to close Sublet!'),
};

export const FORM_SUBMIT_MODE = {
  PRIMARY: 'primary',
  TERTIARY: 'tertiary',
};

export const CANNOT_SUBMIT_SERVICE_SUBLET = 'PT233';

export const SUBLET_PO_TAB_KEYS = {
  DETAILS: 'details',
  INVOICES: 'invoices',
};

export const SUBLET_PO_TAB_KEYS_VS_TITLE = {
  [SUBLET_PO_TAB_KEYS.DETAILS]: 'Sublet PO | Details',
  [SUBLET_PO_TAB_KEYS.INVOICES]: 'Sublet PO | Invoices',
};

export const STATUS_TO_SHOW_MARK_AS_RECEIVED_BTN = [SUBLET_PO_RECIEVING_STATUS.PARTIALLY_RECEIVED, PO_STATUS.SUBMITTED];
export const STATUS_TO_HIDE_MARK_AS_RECEIVED_BTN = [PO_STATUS.VOIDED, SUBLET_PO_RECIEVING_STATUS.RECEIVED];
