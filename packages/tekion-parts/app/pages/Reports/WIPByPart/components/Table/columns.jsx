import React from 'react';

import _get from 'lodash/get';

import { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon/Icon.constants';
import { GENERIC_PART_REPORT_COLUMN_KEYS } from '@tekion/tekion-components/src/constants/parts/reports.constants';
import { TextRenderer } from '@tekion/tekion-components/src/molecules/CellRenderers/TextRenderer';
import Skeleton from '@tekion/tekion-components/src/molecules/skeleton';
import withIcon, { POSITIONS } from '@tekion/tekion-components/src/molecules/WithIcon.hoc';

import { UserPreference } from '@tekion/tekion-widgets/src/appServices/parts/components/Table';
import { CurrencyRenderer } from '@tekion/tekion-widgets/src/cellRenderers/CurrencyCellRenderer';
import DateRenderer from '@tekion/tekion-widgets/src/cellRenderers/dateRenderer';
import PercentageRenderer from '@tekion/tekion-widgets/src/cellRenderers/PercentDisplayCellRenderer';

import { DATE_TIME_FORMAT } from '@tekion/tekion-conversion-web';

import SourceCodeRenderer from 'shared/components/CellRenderers/SourceCodeRenderer/SourceCodeRenderer';
import UserName from 'shared/components/CellRenderers/UserName';

import ReferenceNumberRenderer from '../../../shared/cells/ReferenceNumberRenderer';

import JobNumber from './cells/JobNumber';
import PartName from './cells/PartName';
import PayType from './cells/PayType';

export const MemoizedTextRenderer = React.memo(({ data, loading, highlight }) => (
  <Skeleton active paragraph={false} loading={loading}>
    <TextRenderer data={data} highlight={highlight} />
  </Skeleton>
));

const TextWithTooltipRenderer = withIcon(MemoizedTextRenderer);

const HeaderWithTooltip = React.memo(({ data, toolTipTitle }) => (
  <TextWithTooltipRenderer
    data={data}
    icon="icon-info"
    highlight
    toolTipTitle={toolTipTitle}
    iconPosition={POSITIONS.RIGHT}
    iconSize={SIZES.S}
    iconClassName="m-l-8"
  />
));

const PART_NAME = {
  Header: __('Part Name'),
  accessor: row => row?.getPartName(),
  id: GENERIC_PART_REPORT_COLUMN_KEYS.PART_NAME,
  key: GENERIC_PART_REPORT_COLUMN_KEYS.PART_NAME,
  width: 272,
  Cell: ({ value, row }) => {
    const refId = row?.original?.refId;
    const refType = row?.original?.refType;
    const partId = row?.original?.partId;
    const temporaryPart = row?.original?.temporaryPart;

    return (
      <PartName
        data={value}
        refId={refId}
        refType={refType}
        partId={partId}
        temporaryPart={temporaryPart}
        isCoreReturnRow={row?.original?.getIsCoreReturn() || row?.original?.getIsPartReturnCoreReturn()}
      />
    );
  },
};

const REFERENCE_NUMBER = {
  Header: __('Reference Number'),
  accessor: row => row?.refNumber,
  id: GENERIC_PART_REPORT_COLUMN_KEYS.REFERENCE_NUMBER,
  key: GENERIC_PART_REPORT_COLUMN_KEYS.REFERENCE_NUMBER,
  width: 172,
  Cell: ({ value, row }) => {
    const refId = row?.original?.refId;
    const refType = row?.original?.refType;
    const orderType = row?.orderType;

    return <ReferenceNumberRenderer refNumber={value} refType={refType} refId={refId} orderType={orderType} />;
  },
};

const REFERENCE_TYPE = {
  Header: __('Reference Type'),
  accessor: row => row?.getReferenceType(),
  id: GENERIC_PART_REPORT_COLUMN_KEYS.REFERENCE_TYPE,
  key: GENERIC_PART_REPORT_COLUMN_KEYS.REFERENCE_TYPE,
  width: 152,
  Cell: ({ value }) => <MemoizedTextRenderer data={value} />,
};

const JOB_NUMBER = {
  Header: __('Job Number'),
  accessor: row => row?.assetId,
  id: GENERIC_PART_REPORT_COLUMN_KEYS.JOB_NUMBER,
  key: GENERIC_PART_REPORT_COLUMN_KEYS.JOB_NUMBER,
  width: 132,
  Cell: ({ value, row, loading }) => (
    <Skeleton active paragraph={false} loading={loading}>
      <JobNumber
        value={value}
        groupId={row?.original.groupId}
        refType={_get(row, ['original', 'partDetail', 'refType'])}
      />
    </Skeleton>
  ),
};

const PAY_TYPE = {
  Header: __('Pay Type'),
  accessor: row => row?.assetId,
  id: GENERIC_PART_REPORT_COLUMN_KEYS.PAY_TYPE,
  key: GENERIC_PART_REPORT_COLUMN_KEYS.PAY_TYPE,
  width: 132,
  Cell: ({ value, row, loading }) => (
    <Skeleton active paragraph={false} loading={loading}>
      <PayType
        value={value}
        groupId={row?.original.groupId}
        refType={_get(row, ['original', 'partDetail', 'refType'])}
      />
    </Skeleton>
  ),
};

const SOURCE_CODE = {
  Header: __('Source Code'),
  accessor: row => row?.sourceCodeId,
  id: GENERIC_PART_REPORT_COLUMN_KEYS.SOURCE_CODE,
  key: GENERIC_PART_REPORT_COLUMN_KEYS.SOURCE_CODE,
  width: 120,
  Cell: ({ value }) => <SourceCodeRenderer sourceCodeId={value} />,
};

const MANUFACTURER = {
  Header: __('Manufacturer'),
  accessor: row => row?.getManufacturer(),
  id: GENERIC_PART_REPORT_COLUMN_KEYS.MANUFACTURER,
  key: GENERIC_PART_REPORT_COLUMN_KEYS.MANUFACTURER,
  Cell: ({ value }) => <MemoizedTextRenderer data={value} />,
  width: 132,
};

const TOTAL_SALE_QUANTITY = {
  Header: __('Total Quantity'),
  accessor: row => row?.getQuantity(),
  id: GENERIC_PART_REPORT_COLUMN_KEYS.TOTAL_SALE_QUANTITY,
  key: GENERIC_PART_REPORT_COLUMN_KEYS.TOTAL_SALE_QUANTITY,
  width: 140,
  Cell: ({ value }) => <MemoizedTextRenderer data={value} />,
};

const getUnitCostColumn = isAverageCostEnabled => ({
  Header: isAverageCostEnabled ? __('Part Average Cost') : __('Unit Part Cost'),
  accessor: row => row?.getCostPrice(),
  id: GENERIC_PART_REPORT_COLUMN_KEYS.COST_PRICE,
  key: GENERIC_PART_REPORT_COLUMN_KEYS.COST_PRICE,
  width: 132,
  Cell: ({ value }) => <CurrencyRenderer data={value} />,
});

const UNIT_CORE_COST = {
  Header: __('Unit Core Value'),
  accessor: row => row?.getCoreCost(),
  id: GENERIC_PART_REPORT_COLUMN_KEYS.CORE_PRICE,
  key: GENERIC_PART_REPORT_COLUMN_KEYS.CORE_PRICE,
  width: 152,
  Cell: ({ value }) => <CurrencyRenderer data={value} />,
};

const TOTAL_COST = {
  Header: () => <HeaderWithTooltip data={__('Total Cost')} toolTipTitle={__('Unit part price + Unit core price')} />,
  displayName: __('Total Cost'),
  accessor: row => row?.getFormattedCostWithCore(),
  id: GENERIC_PART_REPORT_COLUMN_KEYS.COST_PRICE_WITH_CORE,
  key: GENERIC_PART_REPORT_COLUMN_KEYS.COST_PRICE_WITH_CORE,
  width: 152,
  Cell: ({ value }) => <CurrencyRenderer data={value} />,
};

const EXTENDED_PART_COST = {
  Header: () => <HeaderWithTooltip data={__('Extended Cost Price')} toolTipTitle={__('Includes core cost')} />,
  displayName: __('Extended Cost Price'),
  id: GENERIC_PART_REPORT_COLUMN_KEYS.EXTENDED_COST_PRICE,
  accessor: row => row?.getExtendedCostPrice(),
  key: GENERIC_PART_REPORT_COLUMN_KEYS.EXTENDED_COST_PRICE,
  width: 200,
  Cell: ({ value }) => <CurrencyRenderer data={value} />,
};

const SELLING_PRICE = {
  Header: () => (
    <HeaderWithTooltip data={__('Selling Price')} toolTipTitle={__('Sum of part selling price + core selling price')} />
  ),
  displayName: __('Selling Price'),
  accessor: row => row?.getSellingPrice(),
  id: GENERIC_PART_REPORT_COLUMN_KEYS.TOTAL_COST,
  key: GENERIC_PART_REPORT_COLUMN_KEYS.TOTAL_COST,
  Cell: ({ value }) => <CurrencyRenderer data={value} />,
};

const EXTENDED_SELLING_PRICE = {
  Header: () => (
    <HeaderWithTooltip
      data={__('Extended Selling Price')}
      toolTipTitle={__('Selling Price x Quantity (including core)')}
    />
  ),
  width: 220,
  displayName: __('Extended Selling Price'),
  accessor: row => row?.getTotalPrice(),
  id: GENERIC_PART_REPORT_COLUMN_KEYS.EXTENDED_SELLING_PRICE,
  key: GENERIC_PART_REPORT_COLUMN_KEYS.EXTENDED_SELLING_PRICE,
  Cell: ({ value }) => <CurrencyRenderer data={value} />,
};

const GROSS_PROFIT = {
  width: 200,
  Header: __('Gross Profit'),
  accessor: row => row?.getGrossProfit(),
  id: GENERIC_PART_REPORT_COLUMN_KEYS.GROSS_PROFIT,
  key: GENERIC_PART_REPORT_COLUMN_KEYS.GROSS_PROFIT,
  Cell: ({ value }) => <CurrencyRenderer data={value} />,
};

const GROSS_PROFIT_PERCENTAGE = {
  Header: () => (
    <HeaderWithTooltip data={__('Gross Profit Percentage')} toolTipTitle={__('Does not include core cost')} />
  ),
  displayName: __('Gross Profit Percentage'),
  width: 212,
  accessor: row => row?.getGrossProfitPercentage(),
  id: GENERIC_PART_REPORT_COLUMN_KEYS.GROSS_PROFIT_PERCENT,
  key: GENERIC_PART_REPORT_COLUMN_KEYS.GROSS_PROFIT_PERCENT,
  Cell: ({ value }) => <PercentageRenderer value={value} />,
};

const CREATED_DATE = {
  Header: __('Date Created'),
  accessor: row => row?.createdTime,
  id: GENERIC_PART_REPORT_COLUMN_KEYS.CREATED_DATE,
  key: GENERIC_PART_REPORT_COLUMN_KEYS.CREATED_DATE,
  width: 192,
  Cell: ({ value, loading }) => (
    <Skeleton active paragraph={false} loading={loading}>
      <DateRenderer format={DATE_TIME_FORMAT.DATE_ABBREVIATED_MONTH_YEAR_WITH_HOUR_MINUTE} data={value} />
    </Skeleton>
  ),
};

const CREATED_BY = {
  Header: __('Created By'),
  accessor: row => row?.createdByUserId,
  id: GENERIC_PART_REPORT_COLUMN_KEYS.CREATED_BY,
  key: GENERIC_PART_REPORT_COLUMN_KEYS.CREATED_BY,
  width: 128,
  Cell: ({ value }) => <UserName value={value} />,
};

const SITE_NAME = {
  Header: __('Site Name'),
  accessor: row => row?.getSiteName(),
  key: GENERIC_PART_REPORT_COLUMN_KEYS.SITE_ID,
  id: GENERIC_PART_REPORT_COLUMN_KEYS.SITE_ID,
  width: 120,
  disableSortBy: false,
  Cell: ({ value, loading }) => <MemoizedTextRenderer data={value} loading={loading} />,
};

const USER_PREFERENCE = {
  Header: UserPreference,
  align: 'center',
  id: 'USER_PREFERENCE_HEADER_GROUP',
  sticky: 'right',
  disableResizing: true,
  width: 44,
};

export const getColumns = ({ isMultiSiteEnabled, isAverageCostEnabled }) => [
  PART_NAME,
  REFERENCE_NUMBER,
  REFERENCE_TYPE,
  JOB_NUMBER,
  PAY_TYPE,
  SOURCE_CODE,
  MANUFACTURER,
  TOTAL_SALE_QUANTITY,
  getUnitCostColumn(isAverageCostEnabled),
  UNIT_CORE_COST,
  TOTAL_COST,
  EXTENDED_PART_COST,
  SELLING_PRICE,
  EXTENDED_SELLING_PRICE,
  GROSS_PROFIT,
  GROSS_PROFIT_PERCENTAGE,
  CREATED_DATE,
  CREATED_BY,
  ...(isMultiSiteEnabled ? [SITE_NAME] : []),
  USER_PREFERENCE,
];
