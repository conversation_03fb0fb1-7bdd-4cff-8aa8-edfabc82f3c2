import React from 'react';

import _compact from 'lodash/compact';
import _get from 'lodash/get';
import _includes from 'lodash/includes';
import _map from 'lodash/map';
import _size from 'lodash/size';

import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import { SORT } from '@tekion/tekion-base/builders/request/Request.constants';

import Content from '@tekion/tekion-components/src/atoms/Content';
import Spinner from '@tekion/tekion-components/src/molecules/SpinnerComponent';

import Table from '@tekion/tekion-widgets/src/appServices/parts/components/Table';
import tableStyles from '@tekion/tekion-widgets/src/appServices/parts/components/Table/styles.module.scss';
import { TABLE_ACTION_TYPES } from '@tekion/tekion-widgets/src/organisms/tableManager';

import { useTableUserPreferences } from 'shared/table/useUserPreference';
import { getSkeletonRows } from 'shared/table/utils';

import { getColumns } from './columns';

// import { getListViewTableProps as getTableProps, getHeaderProps, getCellProps } from '../../../helpers/table.helpers';

function WIPByPartTable(props) {
  const {
    style = EMPTY_OBJECT,
    totalNumberOfEntries,
    onAction,
    loading: isLoadingData,
    data = EMPTY_ARRAY,
    pageSizeOptions,
    currentPage,
    sortDetails,
    pageSize = 50,
    isFetching,
    assetType = 'WIP_BY_PART',
    isMultiSiteEnabled,
    isAverageCostEnabled,
    ...rest
  } = props;

  const pageCount = Math.ceil(totalNumberOfEntries / pageSize);
  // based on the group, we will fetch the preferences
  // for certain groupings, we dont show the column preferences
  const [queryState, tableProps] = useTableUserPreferences(assetType);
  const { isFetching: isLoadingTablePreferences } = queryState;

  const { initialState: preferences, onStateChange: onStateChangeProp, ...restPreferenceProps } = tableProps;

  const tableData = React.useMemo(
    () => (isFetching && !_size(data) ? [...getSkeletonRows(10, { paragraph: false })] : data),
    [isFetching, data],
  );

  const sortBy = React.useMemo(
    () => _map(sortDetails, (value, key) => ({ id: key, desc: value === SORT.DESC })),
    [sortDetails],
  );

  const getPaginationProps = React.useCallback(
    ({ pageSize: currentPageSize }) => ({
      isLoading: isLoadingData,
      resultsPerPage: currentPageSize,
      currentPage: currentPage + 1,
      totalPages: pageCount,
      fixedPagination: true,
      totalNumberOfEntries,
      resultsPerPageOptions: pageSizeOptions,
      displayFirstLast: true,
    }),
    [isLoadingData, pageCount, pageSizeOptions, totalNumberOfEntries, currentPage],
  );

  const onStateChange = React.useCallback(
    (newState, action) => {
      if (_includes([Table.actions.setPageSize, Table.actions.gotoPage], action && action.type)) {
        const resultsPerPage = _get(newState, 'pageSize');
        const currentPageIndex = _get(newState, 'pageIndex');

        onAction({
          type: TABLE_ACTION_TYPES.TABLE_ITEMS_PAGE_UPDATE,
          payload: {
            value: {
              resultsPerPage,
              page: currentPageIndex + 1,
            },
          },
        });
      }

      onStateChangeProp(newState, action);
    },
    [onAction, onStateChangeProp],
  );

  const initialState = React.useMemo(
    () => ({
      ...preferences,
      pageSize,
      pageIndex: currentPage,
      sortBy,
    }),
    [pageSize, currentPage, sortBy, preferences],
  );

  const columns = React.useMemo(() => _compact(getColumns({ isMultiSiteEnabled, isAverageCostEnabled })), []);

  return (
    <div style={style}>
      {isLoadingTablePreferences || isLoadingData ? (
        <div className="full-height full-width flex-center m-t-16">
          <Spinner />
        </div>
      ) : (
        <Table
          {...rest}
          usePagination
          hasPagination
          manualPagination
          data={tableData}
          onAction={onAction}
          styles={tableStyles}
          pageCount={pageCount}
          disableSortBy={false}
          autoResetPage={false}
          loading={isLoadingData}
          columns={columns}
          autoResetHiddenColumns={false}
          initialState={initialState}
          onStateChange={onStateChange}
          getPaginationProps={getPaginationProps}
          maxStickyLeft={_size(_compact(getColumns({ isMultiSiteEnabled, isAverageCostEnabled })))}
          {...restPreferenceProps}
        />
      )}
      {!_size(tableData) ? <Content className="m-32 text-center">{__('No rows found')}</Content> : null}
    </div>
  );
}

export default React.memo(WIPByPartTable);
