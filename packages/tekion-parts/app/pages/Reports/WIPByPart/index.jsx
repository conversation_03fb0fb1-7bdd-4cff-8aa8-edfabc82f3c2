import React from 'react';

import _noop from 'lodash/noop';

import { PropertyContext } from '@tekion/tekion-components/src/organisms/propertyProvider';

import FEATURE_NAME from '@tekion/tekion-widgets/src/constants/experienceEngine.featureName';

import QueryClientProvider from 'shared/components/QueryProvider';

import loadable from 'utils/loadable';

const WIPByPartReport = loadable(() => import('./WIPByPart'));

const WIPByPartWithQueryClientProvider = React.memo(props => {
  const { getDealerPropertyValue = _noop } = React.useContext(PropertyContext);
  const isMultiOEMSwitchEnabled = getDealerPropertyValue(FEATURE_NAME.MULTI_OEM_SWITCH_ENABLED);
  const isAverageCostEnabled = getDealerPropertyValue(FEATURE_NAME.AVERAGE_COST_ENABLED);

  return (
    <QueryClientProvider>
      <WIPByPartReport
        {...props}
        isMultiSiteEnabled={isMultiOEMSwitchEnabled}
        isAverageCostEnabled={isAverageCostEnabled}
      />
    </QueryClientProvider>
  );
});

export default WIPByPartWithQueryClientProvider;
