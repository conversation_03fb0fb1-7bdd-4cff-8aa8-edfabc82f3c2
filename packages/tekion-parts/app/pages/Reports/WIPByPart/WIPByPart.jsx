import React, { useCallback } from 'react';

import { useLocation } from 'react-router-dom';

import _filter from 'lodash/filter';
import _find from 'lodash/find';
import _get from 'lodash/get';
import _head from 'lodash/head';
import _isEmpty from 'lodash/isEmpty';
import _isNaN from 'lodash/isNaN';
import _isNil from 'lodash/isNil';

import cx from 'classnames';
import PropTypes from 'prop-types';
import { compose } from 'recompose';

import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import OPERATORS from '@tekion/tekion-base/constants/filterOperators';
import { ENTITY_REFERENCE_TYPES } from '@tekion/tekion-base/constants/parts/entityReferenceTypes';
import { DEFAULT_PAGE_SIZE } from '@tekion/tekion-base/constants/tableConstants';
import { isAfter, getToday, endOfDay } from '@tekion/tekion-base/utils/dateUtils';

import {
  getSanitizedFiltersWithSite,
  DEALER_SITES_FILTER_ID,
} from '@tekion/tekion-business/src/appServices/parts/helpers/dealerSiteFilter.helpers';
import { FILTER_GROUP_VARIANT } from '@tekion/tekion-business/src/appServices/parts/helpers/general.helpers';
import { DETAILED_BY_PART_STATIC_FILTER_GROUP_VARIANT } from '@tekion/tekion-business/src/appServices/parts/reports/DetailedWIPByPart/constants';
import { getFormattedCurrencyPrecisionWithSymbol } from '@tekion/tekion-business/src/appServices/parts/utils/currency.utils';

import Skeleton from '@tekion/tekion-components/molecules/skeleton';
import Button from '@tekion/tekion-components/src/atoms/Button';
import Content from '@tekion/tekion-components/src/atoms/Content';
import FontIcon from '@tekion/tekion-components/src/atoms/FontIcon';
import Heading from '@tekion/tekion-components/src/atoms/Heading';
import { GENERIC_PART_REPORT_COLUMN_KEYS } from '@tekion/tekion-components/src/constants/parts/reports.constants';
import withSize from '@tekion/tekion-components/src/hoc/withSize';
import Info from '@tekion/tekion-components/src/molecules/Info';
import Page from '@tekion/tekion-components/src/molecules/pageComponent';

import TableManager from '@tekion/tekion-widgets/src/organisms/tableManager';

// This will go to a domain as well
import { useTekionConversion } from '@tekion/tekion-conversion-web';

import { useServiceSettingsQuery } from 'queries/settings';
import { useSourceCodeListQuery } from 'queries/source-code';

import { getURLSearchParamValue } from 'utils/history';

import Table from './components/Table';
import { TABLE_PAGE_SIZE_OPTIONS, TABLE_MANAGER_PROPS } from './constants/table.constants';
import { getFilters } from './filters';
import { transformFilters, getSubHeaderProps } from './helpers';
import useFilters from './hooks/useFilters';
import useWIPByPart from './hooks/useWIPByPart';
import useWIPByPartDownload from './hooks/useWIPByPartDownload';
import styles from './styles.module.scss';

const AggregatedTotal = ({ children, loading, label, helpText }) => (
  <div className="flex align-items-center">
    <Content>{label}</Content>
    {helpText && <Info helpText={helpText} className="m-x-8" />} :
    <Skeleton paragraph={false} active loading={loading} className={cx(styles.aggregatedTotalsLoader, 'm-x-8')}>
      <Heading className="m-x-8" size={4}>
        {children}
      </Heading>
    </Skeleton>
  </div>
);

const refTypeMapping = {
  [DETAILED_BY_PART_STATIC_FILTER_GROUP_VARIANT.WIP_BY_REFERENCE_TYPE_REPAIR_ORDER]: ENTITY_REFERENCE_TYPES.FULFILMENT,
  [DETAILED_BY_PART_STATIC_FILTER_GROUP_VARIANT.WIP_BY_REFERENCE_TYPE_SALES_ORDER]: ENTITY_REFERENCE_TYPES.SALES_ORDER,
  [DETAILED_BY_PART_STATIC_FILTER_GROUP_VARIANT.WIP_BY_REFERENCE_TYPE_APPOINTMENT]: ENTITY_REFERENCE_TYPES.APPOINTMENT,
};

const WIPByPart = React.memo(props => {
  const {
    contentHeight,
    isMultiSiteEnabled,
    isWIPandDSRAsyncReportDownloadEnabled,
    isPartInventoryAtSiteLevel,
    isAverageCostEnabled,
  } = props;

  const location = useLocation();

  const getInitialFilters = useCallback(() => {
    const refType = getURLSearchParamValue(FILTER_GROUP_VARIANT, location?.search);

    if (_isNil(refType) || _isNil(_get(refTypeMapping, refType)))
      return getSanitizedFiltersWithSite([], { withAllAccessibleSites: true, isMultiSiteEnabled });

    if (refType === DETAILED_BY_PART_STATIC_FILTER_GROUP_VARIANT.WIP_BY_REFERENCE_PHYSICAL_INVENTORY) {
      return getSanitizedFiltersWithSite(EMPTY_ARRAY, {
        siteFilterField: DEALER_SITES_FILTER_ID,
        isMultiSiteEnabled: isPartInventoryAtSiteLevel,
      });
    }

    return getSanitizedFiltersWithSite(
      [
        {
          type: GENERIC_PART_REPORT_COLUMN_KEYS.REFERENCE_TYPE,
          operator: OPERATORS.IN,
          values: [_get(refTypeMapping, refType)],
        },
      ],
      { withAllAccessibleSites: true, isMultiSiteEnabled },
    );
  }, [isMultiSiteEnabled, isPartInventoryAtSiteLevel, location]);

  const tableManagerStyle = React.useMemo(
    () => ({
      height: contentHeight - 128,
      overflow: 'hidden',
    }),
    [contentHeight],
  );

  const initialState = React.useMemo(
    () => ({
      selectedFilters: getInitialFilters(),
      rows: DEFAULT_PAGE_SIZE,
      resultsPerPage: DEFAULT_PAGE_SIZE,
      currentPage: 0,
    }),
    [getInitialFilters],
  );

  const [filters, setFilters] = useFilters(initialState);
  const selectedFilters = _filter(filters?.selectedFilters, filter => !_isNil(filter?.values));

  const payload = React.useMemo(() => {
    const updatedFilters = transformFilters({ filters: selectedFilters, isMultiSiteEnabled });
    const dateRangeFilter = _find(selectedFilters, { type: 'wipOnDate' }) || EMPTY_OBJECT;
    const dateRangeFilterValue = _head(dateRangeFilter?.values);

    const updatedDateRangeFilter = {
      startDate: 0,
      endDate: dateRangeFilterValue,
      formattedEndDate: endOfDay(dateRangeFilterValue).format('DD/MM/YYYY'),
      field: 'ON_DATE',
    };

    return {
      additionalFilters: updatedFilters,
      ...(isAfter(getToday(), dateRangeFilterValue, { inclusive: true }) ||
      _isNil(dateRangeFilterValue) ||
      _isNaN(dateRangeFilterValue)
        ? EMPTY_OBJECT
        : { dateRange: updatedDateRangeFilter }),
      pageInfo: { start: filters?.currentPage * filters?.rows, rows: filters?.rows },
    };
  }, [isMultiSiteEnabled, selectedFilters, filters?.currentPage, filters?.rows]);

  const { getFormattedCurrency } = useTekionConversion();

  const {
    count,
    isFetching,
    isFetchingAggregate,
    partDetails,
    totalQuantity,
    totalCostPrice,
    totalSaleValue,
    totalGrossProfit,
    refetch: refetchReport,
    isLoading: isWIPByPartLoading,
    isLoadingAggregated: isWIPByPartAggregatedLoading,
  } = useWIPByPart(payload, { keepPreviousData: true });

  const isFetchingDataWithAggregate = isFetchingAggregate || isFetching;

  const tableProps = {
    totalNumberOfEntries: count,
    currentPage: filters?.currentPage,
    loading: isFetching && _isEmpty(partDetails),
    isFetching,
    isMultiSiteEnabled,
    isAverageCostEnabled,
  };

  const { data: sourceCodeList } = useSourceCodeListQuery({
    select: React.useCallback(sourceCodes => _filter(sourceCodes, 'active'), []),
  });

  // setting the cache time to 15 mins as this isn't bound to change frequently
  useServiceSettingsQuery({ cacheTime: 1000 * 60 * 15 });

  const { downloadReport, isDownloading } = useWIPByPartDownload({ payload, isWIPandDSRAsyncReportDownloadEnabled });

  return (
    <Page>
      {/* always redirecting to the reports view
      the origin has to change if sometime in future the report
      moves to somewhere else   */}
      <Page.Header hasBack goBackTo="/core/reports/parts" contentClassName="flex justify-content-between">
        <div className="flex align-items-center">
          <Heading size={1}>{__('Detailed WIP By Part')}</Heading>
          <Info
            helpText={__(
              'WIP by Part includes parts on open sales orders, repair orders, appointments and part returns, and includes core values. The report will show entries for parts on repair orders until the pay type is closed.',
            )}
          />
          <Button
            view={Button.VIEW.TERTIARY}
            disabled={isFetchingDataWithAggregate}
            className="m-l-12 flex align-items-center"
            onClick={refetchReport}
          >
            {__('Refresh')}
            <FontIcon className={cx('m-l-8', { [styles.loader]: isFetchingDataWithAggregate })}>icon-refresh</FontIcon>
          </Button>
        </div>
        <div className="flex">
          <AggregatedTotal
            label={__('Total Extended Selling Price')}
            loading={isWIPByPartAggregatedLoading}
            helpText={__('Sum of Extended Selling Price (including core)')}
          >
            {getFormattedCurrencyPrecisionWithSymbol(totalSaleValue, getFormattedCurrency)}
          </AggregatedTotal>
          <AggregatedTotal
            label={__('Total Extended Cost')}
            loading={isWIPByPartAggregatedLoading}
            helpText={__('Sum of Extended Cost Price (including core)')}
          >
            {getFormattedCurrencyPrecisionWithSymbol(totalCostPrice, getFormattedCurrency)}
          </AggregatedTotal>
          <AggregatedTotal label={__('Total Qty WIP')} loading={isWIPByPartAggregatedLoading}>
            {totalQuantity || '-'}
          </AggregatedTotal>
          <AggregatedTotal label={__('Gross Profit')} loading={isWIPByPartAggregatedLoading}>
            {getFormattedCurrencyPrecisionWithSymbol(totalGrossProfit, totalGrossProfit)}
          </AggregatedTotal>
        </div>
      </Page.Header>

      <Page.Body>
        <div className="full-width" style={tableManagerStyle}>
          <TableManager
            data={partDetails}
            tableComponent={Table}
            isFetching={isFetching}
            tableProps={tableProps}
            tableManagerProps={TABLE_MANAGER_PROPS}
            tablePageSizeOptions={TABLE_PAGE_SIZE_OPTIONS}
            subHeaderProps={getSubHeaderProps({ downloadReport, disabled: isDownloading || isFetching })}
            onAction={setFilters}
            loading={isWIPByPartLoading}
            selectedFilters={selectedFilters}
            filterProps={getFilters({
              sourceCodeList,
              isMultiSiteEnabled,
              selectedFilterGroup: filters?.selectedFilterGroup,
            })}
            disableHeight={_isEmpty(partDetails) && !isFetching}
          />
        </div>
      </Page.Body>
    </Page>
  );
});

WIPByPart.propTypes = {
  isMultiSiteEnabled: PropTypes.bool,
  isWIPandDSRAsyncReportDownloadEnabled: PropTypes.bool,
  isPartInventoryAtSiteLevel: PropTypes.bool,
};

WIPByPart.defaultProps = {
  isMultiSiteEnabled: false,
  isWIPandDSRAsyncReportDownloadEnabled: false,
  isPartInventoryAtSiteLevel: false,
};

export default compose(withSize({ hasPageFooter: 0, hasPageHeader: 0 }))(WIPByPart);
