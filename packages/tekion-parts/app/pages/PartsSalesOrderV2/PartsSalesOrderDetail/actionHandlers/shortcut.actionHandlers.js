import _compact from 'lodash/compact';
import _findIndex from 'lodash/findIndex';
import _get from 'lodash/get';
import _includes from 'lodash/includes';
import _isEmpty from 'lodash/isEmpty';
import _isFunction from 'lodash/isFunction';
import _maxBy from 'lodash/maxBy';
import _replace from 'lodash/replace';
import _size from 'lodash/size';

import produce from 'immer';

import { EMPTY_OBJECT, EMPTY_STRING, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import { SALES_STATUS } from '@tekion/tekion-base/constants/parts/sales';
import CustomerReader from '@tekion/tekion-base/readers/Customer';
import { focusApplicableElement } from '@tekion/tekion-base/utils/dom.utils';

import SalesOrderCustomerReader from '@tekion/tekion-business/src/appServices/parts/salesOrder/readers/SalesOrder.customer.reader';
import SalesOrderReader from '@tekion/tekion-business/src/appServices/parts/salesOrder/readers/SalesOrder.reader';
import SalesOrderPartLineItemReader from '@tekion/tekion-business/src/appServices/parts/salesOrder/readers/SalesOrderPartLineItem.reader';

import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import { ASSET_TYPE } from '@tekion/tekion-components/src/organisms/PdfViewerPartsV2/PdfViewer.constants';
import useKeyboardShortcuts from '@tekion/tekion-components/src/organisms/withKeyboardShortcuts/useKeyboardShortcuts';
import { triggerSubmit } from '@tekion/tekion-components/src/pages/formPage/utils/formAction';

import { handlePrintPdfWithOptionsOnlyWithParams } from '@tekion/tekion-widgets/src/helpers/Parts/printPdf.helpers';

import { triggerSnapOnImport } from 'molecules/SnapOnImportButton/SnapOnImportButton';

import { hasEditPermissionsSalesOrder } from 'permissions/partsPermissions';

import { getSalesOrderListViewRoute, getSalesOrderCreateRoute } from 'shared/SalesOrder/partsSalesOrder.routes.helpers';

import { getIsDeliverVoucherCreationEnabled, getIsPDFDocumentServiceEnabled, isRRG } from 'utils/general';

// import { triggerShowModal } from '@tekion/tekion-components/src/molecules/NotesModal/NotesModal';

import { PART_LIST_TABLE_TAB_KEY } from '../../components/FillPartsDrawer/fillPartsDrawer.constants';
import SalesOrderDomain from '../../domain/SalesOrder.domain';
import {
  SALES_ORDER_TAB,
  OLD_CREATE_SO_ROUTE,
  SALES_ORDER_TYPE,
  SALES_ORDER_INVOICE_VARIANT,
} from '../../partsSalesOrder.constants';
import { SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE } from '../config/shortcuts.config';
import { ITEM_TABLE_SEARCH_ID, ORDER_TABLE_SEARCH_ID } from '../constants/fieldId';
import {
  SALES_ORDER_CATALOGUE_IMPORT,
  // SALES_ORDER_GENERIC_NOTES,
  SALES_ORDER_FORM_KEYS,
} from '../constants/general.constants';
import { GENERATE_INVOICE_FORM_CONTEXT_ID } from '../constants/invoice.constants';
import { getCashieringViewAndDisabledStatus } from '../helpers/cashiering.helpers';
import { getCloseButtonList } from '../helpers/closeSalesOrder.helper';
import { blurAndDelay } from '../helpers/common.helpers';
import { showTabNavigationToaster } from '../helpers/commonDependent.helpers';
import { focusCustomerField } from '../helpers/field';
import { isBillingAddressPresent } from '../helpers/general.helpers';
import { isChorusProFlow } from '../helpers/generateInvoiceForm.helpers';
import { SALES_ORDER_TYPE_VS_VOID_MODAL_TITLE } from '../helpers/header.helpers';
import {
  getSalesOrderInvoicesListParams,
  getSalesOrderReturnsListParams,
  getMultiVoidInvoiceList,
  getMultiVoidReturnList,
  getSalesOrderVoidDetails,
} from '../helpers/multiPdfListModal.helper';
import { getIsFeeReCalculationRequiredForCurrentFocusedRow } from '../helpers/table';
import { COMPONENT_ID as BULK_ACTION_COMPONENT_ID } from '../PartsSalesOrderDetailBody/PartsSalesOrderDetailForm/components/BulkActionsDropDown/constants/general';
import { COMPONENT_ID as PARTS_IMPORT_COMPONENT_ID } from '../PartsSalesOrderDetailBody/PartsSalesOrderDetailForm/components/PartsImportDropDown/constants/general';

import { handleBulkFill } from './bulkActions.actionHandlers';
import { handleToggleSelectAllPrintModalItems } from './checkbox.actionHandlers';
import { handleCreateSalesOrder, handleSaveDraft } from './common.actionHandlers';
import { handleReissue, toggleCustomerPanelExpand, GENERAL_ACTION_HANDLERS } from './general.actionHandlers';
import { INVOICE_ACTION_HANDLERS, fetchAndSetChorusProFlowState } from './invoice.actionHandlers';
import {
  MODAL_ACTION_HANDLERS,
  openBulkResolveManualActionDrawer,
  openCashieringModal,
  openKeyboardShortcutDrawer,
  openTaxUpdateModal,
  openVoidModal,
  openFillPartsModal,
} from './modal.actionHandlers';
import { triggerPrintPdf, triggerViewPdf, triggerViewPDFV2 } from './pdf.actionHandlers';
import { handleStartShare } from './pos.actionHandler';
import { handleSelectedItemCreateReturn } from './return.actionHandlers';
import { openAddPartsToReturnModal } from './returnsModal.actionHandler';

// Note: For stopping the browser defaults return the false from action handlers

const historyPush = (getState, newTab) => {
  const { navigate, location, params } = getState();
  const { tabType } = params;
  const route = _replace(location.pathname, tabType, newTab);
  navigate(route);
};

const handleTriggerSnapOnImport = () => {
  triggerSnapOnImport(SALES_ORDER_CATALOGUE_IMPORT);
};

const handleGoBack = ({ getState, setState }) => {
  const { navigate, isPDFModalVisible, showPartsCashiering } = getState();
  if (isPDFModalVisible || showPartsCashiering) {
    setState({
      isPDFModalVisible: false,
      showPartsCashiering: false,
      pdfDocDetails: null,
    });
    return false;
  }
  navigate(getSalesOrderListViewRoute());
  return false;
};

// TODO: vidit should this getIsResolveAlertsPending be at detail.js?
const getIsResolveAlertsPending = ({ params }) => {
  const { isAnyBulkResolutionTaskPendingInTable, isPrimaryDisabled } = params;
  return isAnyBulkResolutionTaskPendingInTable && isPrimaryDisabled;
};

const handleSubmitCreateSalesOrder = async ({ getState, setState, params = EMPTY_OBJECT }) => {
  if (_size(params.event[useKeyboardShortcuts.TRIGGERED_SHORTCUTS_CONTEXT_KEY]) > 1) {
    return;
  }
  if (!hasEditPermissionsSalesOrder()) {
    toaster(TOASTER_TYPE.WARN, __("User Doesn't have permission"));
    return;
  }
  const { navigationContext, values } = getState();
  const isRecalculationRequired = await getIsFeeReCalculationRequiredForCurrentFocusedRow({
    navigationContext,
    values,
  });
  if (isRecalculationRequired) {
    await blurAndDelay();
    return;
  }

  await blurAndDelay();

  if (getIsResolveAlertsPending({ params })) {
    // TODO: yuvi once the below function no need directly come from parent component use the param
    openBulkResolveManualActionDrawer({ getState, setState, params: { isPrimaryActionKeyboardTriggered: true } });
    return;
  }
  handleCreateSalesOrder();
};

const handleSaveDraftSalesOrder = async ({ getState, setState, params = EMPTY_OBJECT }) => {
  if (_size(params.event[useKeyboardShortcuts.TRIGGERED_SHORTCUTS_CONTEXT_KEY]) > 1) {
    return;
  }
  const { isReissuing, values } = getState();
  if (isReissuing) {
    toaster(TOASTER_TYPE.WARN, __('Action not available in Re-Issue'));
    return;
  }
  if (!hasEditPermissionsSalesOrder()) {
    toaster(TOASTER_TYPE.WARN, __("User Doesn't have permission"));
    return;
  }

  const { navigationContext } = getState();
  const isRecalculationRequired = await getIsFeeReCalculationRequiredForCurrentFocusedRow({
    navigationContext,
    values,
  });
  if (isRecalculationRequired) {
    await blurAndDelay(); // while user has fee in the part line or so user level run blur, so can submit next time by shortcut without clicking outside
    return;
  }

  await blurAndDelay();

  // TODO: yuvi once the below function no need directly come from parent component use the param
  if (getIsResolveAlertsPending({ params })) {
    openBulkResolveManualActionDrawer({ getState, setState, params: { isTertiaryActionKeyboardTriggered: true } });
    return;
  }

  handleSaveDraft();
};

const openDropdown = ({ getState, COMPONENT_ID }) => {
  const { refContext } = getState();
  const componentRef = _get(refContext, `components.${COMPONENT_ID}`);
  componentRef.click();
};

const triggerPDFPrintShortcutAction = ({ getState, setState }) => {
  const { params, salesOrderInvoiceList, salesOrderReturnsList, frozenSalesOrderEntity } = getState();
  const currentTab = _get(params, 'tabType');
  if (currentTab === SALES_ORDER_TAB.INVOICES && !_isEmpty(salesOrderInvoiceList)) {
    setState({
      isMultiPdfPrintListModalVisible: true,
      multiPdfListParams: {
        list: getSalesOrderInvoicesListParams(salesOrderInvoiceList, frozenSalesOrderEntity),
        modalTitle: __('Choose Invoice to Print'),
        submitBtnText: __('Print'),
        currentTab,
        isViewSelect: false,
      },
    });
    handleToggleSelectAllPrintModalItems({ getState, setState });
    return;
  }

  if (currentTab === SALES_ORDER_TAB.PART_RETURNS && !_isEmpty(salesOrderReturnsList)) {
    setState({
      isMultiPdfPrintListModalVisible: true,
      multiPdfListParams: {
        list: getSalesOrderReturnsListParams(salesOrderReturnsList, frozenSalesOrderEntity, salesOrderInvoiceList),
        modalTitle: __('Choose Document to Print'),
        submitBtnText: __('Print'),
        currentTab,
        isViewSelect: false,
      },
    });
    return;
  }
  handlePrintPdfWithOptionsOnlyWithParams(triggerPrintPdf)({ getState, setState });
};

const triggerVoidSalesOrderShortcutAction = ({ setState, getState }) => {
  const {
    salesOrderInvoiceList,
    frozenSalesOrderEntity,
    params,
    salesOrderReturnsList,
    isFormEditable,
    checkboxSelectionContext = EMPTY_OBJECT,
  } = getState();
  const currentTab = _get(params, 'tabType');
  const list =
    currentTab === SALES_ORDER_TAB.INVOICES
      ? getMultiVoidInvoiceList(salesOrderInvoiceList)
      : getMultiVoidReturnList(salesOrderReturnsList, frozenSalesOrderEntity, salesOrderInvoiceList, isFormEditable);

  const salesOrderType = SalesOrderDomain.isCreditMemo(frozenSalesOrderEntity)
    ? SALES_ORDER_TYPE.CREDIT_MEMO
    : SALES_ORDER_TYPE.REGULAR;
  const title = SALES_ORDER_TYPE_VS_VOID_MODAL_TITLE[salesOrderType];

  const salesOrderVoidInfo = getSalesOrderVoidDetails(frozenSalesOrderEntity, salesOrderInvoiceList, isFormEditable);

  if (_isEmpty(list) && !_isEmpty(salesOrderVoidInfo)) {
    openVoidModal({ setState, getState, params: { voidType: SALES_ORDER_INVOICE_VARIANT.SALES_ORDER, title } });
    return;
  }

  const finalList = _compact([...list, salesOrderVoidInfo]);

  if (_isEmpty(finalList) === 0) {
    return;
  }

  if (currentTab === SALES_ORDER_TAB.INVOICES) {
    const { disabledRowIds = EMPTY_ARRAY } = _get(
      checkboxSelectionContext,
      SALES_ORDER_FORM_KEYS.PDF_LIST_FOR_PRINT_AND_VIEW,
      EMPTY_OBJECT,
    );
    const lastCreatedInvoiceId = SalesOrderPartLineItemReader.id(_maxBy(finalList, item => item?.createdTime));
    const rowIndex = _findIndex(finalList, { id: lastCreatedInvoiceId });
    const updatedCheckboxSelectionContext = produce(checkboxSelectionContext, draft => {
      draft[SALES_ORDER_FORM_KEYS.MULTI_VOID_LIST] = {
        checkedRowIds: lastCreatedInvoiceId ? [lastCreatedInvoiceId] : [],
        disabledRowIds,
      };
    });
    setState({
      checkboxSelectionContext: updatedCheckboxSelectionContext,
      isMultiVoidListModalVisible: true,
      multiVoidListParams: {
        list: finalList,
        onOpenMultiInvoiceVoidModalCheckboxFocusIndex: rowIndex,
      },
    });
    return;
  }

  if (currentTab === SALES_ORDER_TAB.PART_RETURNS) {
    setState({
      isMultiVoidListModalVisible: true,
      multiVoidListParams: {
        list: finalList,
      },
    });
  }
};

const triggerPDFViewShortcutAction = ({ getState, setState }) => {
  const { params, salesOrderInvoiceList, salesOrderReturnsList, frozenSalesOrderEntity } = getState();
  const currentTab = _get(params, 'tabType');
  if (currentTab === SALES_ORDER_TAB.INVOICES && !_isEmpty(salesOrderInvoiceList)) {
    setState({
      isMultiPdfPrintListModalVisible: true,
      multiPdfListParams: {
        list: getSalesOrderInvoicesListParams(salesOrderInvoiceList, frozenSalesOrderEntity),
        isViewSelect: true,
        modalTitle: __('Choose Invoice to View'),
        submitBtnText: __('View'),
        currentTab,
      },
    });
    handleToggleSelectAllPrintModalItems({ getState, setState });
    return;
  }

  if (currentTab === SALES_ORDER_TAB.PART_RETURNS && !_isEmpty(salesOrderReturnsList)) {
    setState({
      isMultiPdfPrintListModalVisible: true,
      multiPdfListParams: {
        list: getSalesOrderReturnsListParams(salesOrderReturnsList, frozenSalesOrderEntity, salesOrderInvoiceList),
        isViewSelect: true,
        modalTitle: __('Choose Document to View'),
        submitBtnText: __('View'),
        currentTab,
      },
    });
    return;
  }

  if (getIsPDFDocumentServiceEnabled()) {
    triggerViewPDFV2({
      getState,
      setState,
      params: {
        assetType:
          SalesOrderReader.status(frozenSalesOrderEntity) === SALES_STATUS.DRAFT
            ? ASSET_TYPE.SALES_ORDER_QUOTE
            : ASSET_TYPE.SALES_ORDER,
      },
    });
    return;
  }

  triggerViewPdf({ getState, setState });
};

// Note: For stopping the browser defaults return the false from action handlers
const generatePicklist = ({ getState }) => {
  const { navigate } = getState();
  navigate(SALES_ORDER_TAB.PICK_LIST, { state: { triggerGeneratePicklist: true } });
};

// const showNotesModal = () => {
//   triggerShowModal(SALES_ORDER_GENERIC_NOTES);
// };

const handleSearchFocus = ({ getState }) => {
  const { params: routeParams, location } = getState() || EMPTY_OBJECT;
  const tabType = _get(routeParams, 'tabType', EMPTY_STRING);
  const { pathname } = location;

  if (tabType === SALES_ORDER_TAB.SO_DETAILS || _includes(pathname, OLD_CREATE_SO_ROUTE)) {
    const itemsTabSearchInputElement = document.getElementById(ITEM_TABLE_SEARCH_ID);
    focusApplicableElement(itemsTabSearchInputElement);
    return;
  }
  if (tabType === SALES_ORDER_TAB.SPECIAL_ORDER_REQUEST) {
    const itemsTabSearchInputElement = document.getElementById(ORDER_TABLE_SEARCH_ID);
    focusApplicableElement(itemsTabSearchInputElement);
  }
};

const handleExpandCustomerSection = ({ setState }) => {
  toggleCustomerPanelExpand({ setState });
  return false;
};

const handleFocusCustomerSelectField = ({ setState, getState }) => {
  focusCustomerField(SALES_ORDER_FORM_KEYS.CUSTOMER);
  const { isCustomerSectionPanelExpanded } = getState();
  if (!isCustomerSectionPanelExpanded) {
    handleExpandCustomerSection({ setState });
  }
  return false;
};

const handleCreateNewSalesOrder = ({ getState }) => {
  const { navigate } = getState();
  navigate(getSalesOrderCreateRoute(), { state: { isRemount: true } });
};

// Doing this over here and not using disabled functionality in config because we need to inform user using toaster
const validateTabChange =
  callback =>
  ({ getState }) => {
    const { isReissuing } = getState();
    if (isReissuing) {
      showTabNavigationToaster();
      return;
    }
    callback({ getState });
  };

const openChargeAccountModalWrapper = ({ getState, setState }) => {
  const { frozenSalesOrderEntity, isFetchingSalesOrderCashieringDetails, salesOrderCashieringDetails } = getState();
  const { isEligibleForCashiering, areCashieringDetailsFetched } = getCashieringViewAndDisabledStatus({
    frozenSalesOrderEntity,
    isFetchingSalesOrderCashieringDetails,
    salesOrderCashieringDetails,
  });
  if (!isEligibleForCashiering || !areCashieringDetailsFetched) {
    return;
  }
  openCashieringModal({ getState, setState });
};

const handleCloseSO = ({ getState, setState }) => {
  const {
    userLevelSettingPreferences,
    customerUnusedCreditLimitBalance,
    frozenSalesOrderEntity,
    salesOrderCashieringDetails,
    salesOrderInvoiceList,
    salesOrderReturnsList,
    resolvedCustomerDetails,
    closeSOInProgress,
    creditLimitApprovalRequestDetails,
  } = getState();
  const partForbiddenPaymentMethods = CustomerReader.partForbiddenPaymentMethods(resolvedCustomerDetails);
  const closeButtonList = getCloseButtonList({
    invoiceVariant: SALES_ORDER_INVOICE_VARIANT.SALES_ORDER,
    frozenSalesOrderEntity,
    salesOrderCashieringDetails,
    salesOrderInvoiceList,
    salesOrderReturnsList,
    invoice: EMPTY_OBJECT,
    customerUnusedCreditLimitBalance,
    userLevelSettingPreferences,
    partForbiddenPaymentMethods,
    closeSOInProgress,
    creditLimitApprovalRequestDetails,
  });

  if (_size(closeButtonList) > 1) {
    return;
  }

  const { disabled, actionType } = _get(closeButtonList, [0], EMPTY_OBJECT);
  if (disabled) {
    return;
  }
  const handler =
    INVOICE_ACTION_HANDLERS[actionType] || GENERAL_ACTION_HANDLERS[actionType] || MODAL_ACTION_HANDLERS[actionType];
  if (!_isFunction(handler)) {
    return;
  }
  handler({
    getState,
    setState,
    params: {
      invoiceVariant: SALES_ORDER_INVOICE_VARIANT.SALES_ORDER,
      salesOrderCashieringDetails,
      invoice: EMPTY_OBJECT,
    },
  });
};

const generateInvoiceTrigger = async ({ getState, setState }) => {
  await blurAndDelay();
  const { frozenSalesOrderEntity, resolvedCustomerDetails } = getState();
  const isChargeCustomer =
    SalesOrderCustomerReader.chargeCustomer(SalesOrderReader.customer(frozenSalesOrderEntity)) === true;
  const isDeliverVoucherCreationEnabled = getIsDeliverVoucherCreationEnabled() && isChargeCustomer;
  if (isRRG() && !isBillingAddressPresent(frozenSalesOrderEntity)) {
    toaster(TOASTER_TYPE.ERROR, __('Cannot create invoice without billing address'));
    return;
  }
  if (isChorusProFlow(resolvedCustomerDetails)) {
    await fetchAndSetChorusProFlowState({
      setState,
      resolvedCustomerDetails,
      paramsToGenerateInvoice: { isDeliverVoucherCreation: isDeliverVoucherCreationEnabled },
    });
    return;
  }
  triggerSubmit(GENERATE_INVOICE_FORM_CONTEXT_ID, { isDeliverVoucherCreation: isDeliverVoucherCreationEnabled });
};

const handleMoveToTheToBeFilledTab = ({ setState }) => {
  setState({ selectedPartListTabKey: PART_LIST_TABLE_TAB_KEY.TO_BE_FILLED });
};

const handleMoveToTheFilledTab = ({ setState }) => {
  setState({ selectedPartListTabKey: PART_LIST_TABLE_TAB_KEY.FILLED });
};

const handleFillParts = ({ getState, setState, params = EMPTY_OBJECT }) => {
  const { isFormEditable } = params;
  if (isFormEditable) {
    handleBulkFill({ getState, setState });
    return;
  }
  openFillPartsModal({ getState, setState });
};

export const SHORTCUT_ACTION_HANDLERS = {
  // Document level
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.VIEW_PDF_SHORTCUT]: triggerPDFViewShortcutAction,
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.PRINT_PDF_SHORTCUT]: triggerPDFPrintShortcutAction,
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.VOID_SO_SHORTCUT]: triggerVoidSalesOrderShortcutAction,
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.MODIFY_DOCUMENT]: handleReissue,
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.PROCEED_TO_CASHIERING]: openChargeAccountModalWrapper,
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.VOID_SO]: openVoidModal,
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.SUBMIT_SALES_ORDER]: handleSubmitCreateSalesOrder,
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.SUBMIT_MODIFIED_SALES_ORDER]: handleSubmitCreateSalesOrder,
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.SAVE_DRAFT_SALES_ORDER]: handleSaveDraftSalesOrder,
  // [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.SO_NOTES]: showNotesModal,
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.MOVE_BACK]: handleGoBack,
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.CLOSE_SO]: handleCloseSO,
  /**  commenting for now, all the modal has built in ESC close functionality
   add later if need for any special case */
  // [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.CLOSE_MODAL_AND_SHEET]: () => {},
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.LEARN_SHORTCUT_DRAWER]: openKeyboardShortcutDrawer,
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.START_SHARE_POS]: handleStartShare,
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.EXPAND_CUSTOMER_SECTION]: handleExpandCustomerSection,

  // Inter-Tab Navigation
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.MOVE_ITEMS_TAB]: validateTabChange(({ getState }) => {
    historyPush(getState, SALES_ORDER_TAB.SO_DETAILS);
  }),
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.MOVE_INVOICE_TAB]: validateTabChange(({ getState }) => {
    historyPush(getState, SALES_ORDER_TAB.INVOICES);
  }),
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.MOVE_ORDERS_TAB]: validateTabChange(({ getState }) => {
    historyPush(getState, SALES_ORDER_TAB.SPECIAL_ORDER_REQUEST);
  }),
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.MOVE_PICK_LIST_TAB]: validateTabChange(({ getState }) => {
    historyPush(getState, SALES_ORDER_TAB.PICK_LIST);
  }),
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.MOVE_RETURN_TAB]: validateTabChange(({ getState }) => {
    historyPush(getState, SALES_ORDER_TAB.PART_RETURNS);
  }),
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.MOVE_TRANSACTION_TAB]: validateTabChange(({ getState }) => {
    historyPush(getState, SALES_ORDER_TAB.TRANSACTION_HISTORY);
  }),
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.FOCUS_CUSTOMER_SELECT_FILED]: handleFocusCustomerSelectField,
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.CREATE_NEW_SO]: handleCreateNewSalesOrder,

  // Table level
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.OPEN_PART_IMPORT]: ({ getState }) => {
    openDropdown({ getState, COMPONENT_ID: PARTS_IMPORT_COMPONENT_ID });
  },
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.OPEN_BULK_ACTION]: ({ getState }) => {
    openDropdown({ getState, COMPONENT_ID: BULK_ACTION_COMPONENT_ID });
  },
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.SEARCH]: handleSearchFocus,
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.FILL_PARTS]: handleFillParts,

  // Tab level
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.EDIT_TAX_FOR_SO]: openTaxUpdateModal,
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.CREATE_INVOICE_SHORTCUT]: generateInvoiceTrigger,
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.UNLINK_SOR]: () => {}, // TODO: rishi connect with tushar for this shortcut implementation
  // [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.CREATE_PO]: () => {},
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.CREATE_PICK_LIST]: generatePicklist,
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.RETURN_TAB_CREATE_RETURN]: openAddPartsToReturnModal,
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.ITEMS_TAB_CREATE_RETURN]: handleSelectedItemCreateReturn,

  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.SNAP_ON_IMPORT]: handleTriggerSnapOnImport,

  // fill all tab shortcut handlers
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.MOVE_TO_TO_BE_FILLED_TAB]: handleMoveToTheToBeFilledTab,
  [SALES_ORDER_FORM_SHORTCUTS_ACTION_TYPE.MOVE_TO_FILLED_TAB]: handleMoveToTheFilledTab,
};
