/* eslint-disable no-param-reassign */

import _compact from 'lodash/compact';
import _filter from 'lodash/filter';
import _find from 'lodash/find';
import _flatMap from 'lodash/flatMap';
import _forEach from 'lodash/forEach';
import _get from 'lodash/get';
import _groupBy from 'lodash/groupBy';
import _head from 'lodash/head';
import _includes from 'lodash/includes';
import _isEmpty from 'lodash/isEmpty';
import _isNaN from 'lodash/isNaN';
import _isNil from 'lodash/isNil';
import _keys from 'lodash/keys';
import _map from 'lodash/map';
import _mapValues from 'lodash/mapValues';
import _multiply from 'lodash/multiply';
import _partition from 'lodash/partition';
import _reduce from 'lodash/reduce';
import _split from 'lodash/split';
import _subtract from 'lodash/subtract';
import _sum from 'lodash/sum';
import _toNumber from 'lodash/toNumber';
import _uniq from 'lodash/uniq';

import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { CHARGE } from '@tekion/tekion-base/constants/parts/general';
import PartReader from '@tekion/tekion-base/readers/Part';
import { tget } from '@tekion/tekion-base/utils/general';
import moneyUtils from '@tekion/tekion-base/utils/money';

import { DEFAULT_GROUP_ID, TAX_CODE_COMPONENT_TYPE } from '@tekion/tekion-business/src/appServices/parts/constants/tax';
import { getParsedTaxDetailsForSavePayload } from '@tekion/tekion-business/src/appServices/parts/helpers/tax';
import {
  getTaxCodeIds,
  isManualTaxCode,
  getRowEntityTaxDetails,
  getTaxCodeIdsFromTaxConfig,
  getTaxCodeVsTaxRateDetailsMap,
  replacePeriodsWithUnderscores,
  getUpdatedTaxCodeIdsAfterOverride,
} from '@tekion/tekion-business/src/appServices/parts/helpers/taxV2';
import CustomerTaxOverrideReader from '@tekion/tekion-business/src/appServices/parts/readers/CustomerTaxOverride.reader';
import TaxCodeDetailsReader from '@tekion/tekion-business/src/appServices/parts/readers/TaxCodeDetails.reader';
import SalesOrderReader from '@tekion/tekion-business/src/appServices/parts/salesOrder/readers/SalesOrder.reader';
import SalesOrderReturnReader from '@tekion/tekion-business/src/appServices/parts/salesOrder/readers/SalesOrderReturn.reader';
import SalesOrderSummaryV2Reader from '@tekion/tekion-business/src/appServices/parts/salesOrder/readers/SalesOrderSummaryV2.reader';
import SalesOrderTaxConfigurationV2Reader from '@tekion/tekion-business/src/appServices/parts/salesOrder/readers/SalesOrderTaxConfigurationV2.reader';
import TaxSettingsReader from '@tekion/tekion-business/src/appServices/parts/salesOrder/readers/TaxSettings.reader';
import {
  getAmountInCurrency,
  getInHigherPrecisionCents,
  moneyMultiply,
} from '@tekion/tekion-business/src/appServices/parts/utils/tMoney.utils';

import { getIsCreditSaleEnabled } from 'utils/general';
import { getTaxRoundingMethod } from 'utils/tax.utils';

import SalesOrderDomain from '../../domain/SalesOrder.domain';
import SalesOrderPartLineItemDomain from '../../domain/SalesOrderPartLineItem.domain';
import SalesOrderReturnDomain from '../../domain/SalesOrderReturn.domain';
import SalesOrderReturnPartLineDomain from '../../domain/SalesOrderReturnPartLine.domain';
import { CHARGE_TYPE_VS_COMPONENT } from '../constants/taxV2.constants';

/**
 * Get default tax codes for each component after combining tax codes present in parts settings and customer overrides from CMS
 * @param { resolvedCustomerDetails, saleType, taxCodeSetupConfigList }
 * @returns for a given sale type for each component tax code is returned
 */
export const getDocumentDefaultTaxCodeMappings = ({ customerTaxOverrides, saleType, taxCodeSetupConfigList }) => {
  if (_isNil(customerTaxOverrides) && !saleType) {
    return EMPTY_OBJECT;
  }
  const dealerTaxCodeMappings = saleType
    ? TaxSettingsReader.taxCodeMappings(
        _find(taxCodeSetupConfigList, taxConfigMap => TaxSettingsReader.saleType(taxConfigMap) === saleType),
      ) || EMPTY_ARRAY
    : EMPTY_ARRAY;

  if (_isEmpty(customerTaxOverrides)) {
    return dealerTaxCodeMappings;
  }

  const customerTaxComponents = CustomerTaxOverrideReader.taxComponents(customerTaxOverrides);
  if (!customerTaxComponents) {
    return EMPTY_ARRAY;
  }
  const customerTaxConfigList = CustomerTaxOverrideReader.taxComponents(customerTaxOverrides) || EMPTY_ARRAY;
  const customerTaxOverridesForSaleType =
    _find(customerTaxConfigList, taxConfig => CustomerTaxOverrideReader.component(taxConfig) === saleType) ||
    EMPTY_OBJECT;
  const customerTaxOverrideList = CustomerTaxOverrideReader.values(customerTaxOverridesForSaleType) || EMPTY_ARRAY;
  if (_isEmpty(customerTaxOverrideList)) {
    return dealerTaxCodeMappings;
  }
  const customerOverrideTaxConfig = _map(dealerTaxCodeMappings, taxCodeConfig => {
    const component = TaxSettingsReader.component(taxCodeConfig);
    const groupId = TaxSettingsReader.groupId(taxCodeConfig);
    const taxCode = TaxSettingsReader.taxCode(taxCodeConfig);
    const taxCodeId = TaxSettingsReader.taxCodeId(taxCodeConfig);

    const customerTaxConfigForComponent = _find(customerTaxOverrideList, customerTaxConfig => {
      const customerConfigGroupId = CustomerTaxOverrideReader.partsGroupId(customerTaxConfig) || DEFAULT_GROUP_ID;
      if (!_isNil(groupId)) {
        return CustomerTaxOverrideReader.key(customerTaxConfig) === component && customerConfigGroupId === groupId;
      }
      return CustomerTaxOverrideReader.key(customerTaxConfig) === component;
    });
    const overrideTaxCode = CustomerTaxOverrideReader.taxCode(customerTaxConfigForComponent);
    const overrideTaxCodeId = CustomerTaxOverrideReader.taxCodeId(customerTaxConfigForComponent);
    return {
      ...taxCodeConfig,
      taxCode: overrideTaxCode || taxCode,
      taxCodeId: overrideTaxCodeId || taxCodeId,
    };
  });
  return customerOverrideTaxConfig;
};

export const getTaxCodesFromTaxConfig = orderTaxCodeMappings =>
  _uniq(_compact(_map(orderTaxCodeMappings, taxConfig => TaxSettingsReader.taxCode(taxConfig))));

/**
 * getPartTaxCodeIds -> get the tax code ids which are applied for a part
 * @param {
 *  part,
 *  orderTaxCodeMappings,
 * }
 * @returns Array taxCodeIds
 */
export const getPartTaxCodeIds = ({ part, orderTaxCodeMappings }) => {
  const partGroupIds = PartReader.groupIds(part);
  const updatedPartGroupIds = _isEmpty(partGroupIds) ? DEFAULT_GROUP_ID : partGroupIds;
  const defaultGroupId = [];
  const partTaxCodeMappings = _filter(orderTaxCodeMappings, taxCodeDetails => {
    const type = TaxSettingsReader.component(taxCodeDetails);
    const groupId = TaxSettingsReader.groupId(taxCodeDetails);
    if (type === TAX_CODE_COMPONENT_TYPE?.PARTS && _includes(DEFAULT_GROUP_ID, groupId)) {
      defaultGroupId.push(taxCodeDetails);
    }
    return type === TAX_CODE_COMPONENT_TYPE?.PARTS && _includes(updatedPartGroupIds, groupId);
  });

  const defaultTaxCodeIdList = getTaxCodeIds(defaultGroupId);
  // The default tax code will be used when there are partGroupIds that are not related to the tax code.
  if (_isEmpty(partTaxCodeMappings) && !_isEmpty(partGroupIds) && !_isEmpty(defaultGroupId)) {
    return defaultTaxCodeIdList;
  }

  const taxCodeIdsList = getTaxCodeIds(partTaxCodeMappings);
  // incase of the group don't have tax code selected
  if (_isEmpty(taxCodeIdsList)) {
    return defaultTaxCodeIdList;
  }
  return taxCodeIdsList;
};

const getQuantity = child => {
  const quantity = _toNumber(_get(child, 'quantity'));
  return _isNaN(quantity) ? 0 : quantity;
};

export const getGeneralChildData = child => ({
  quantity: getQuantity(child),
  price: getInHigherPrecisionCents({
    amount: tget(child, 'amount', 0),
    inCents: false,
  }),
  type: _get(child, 'type'),
  taxable: _get(child, 'taxable'),
});

export const getTaxCodeIdForCharge = ({ type: chargeType, orderTaxCodeMappings }) => {
  const chargeComponent = CHARGE_TYPE_VS_COMPONENT[chargeType];
  return TaxSettingsReader.taxCodeId(
    _find(orderTaxCodeMappings, taxConfigMap => TaxSettingsReader.component(taxConfigMap) === chargeComponent),
  );
};

const getGroupedChildAmountAndTax = ({
  groupedChildList,
  shouldNegateValue = false,
  taxCodeVsTaxRateDetailsMap = EMPTY_OBJECT,
  isChargeTaxable = false,
  orderTaxCodeMappings,
  taxCodeOverrides,
  taxCodeDetailsList,
}) =>
  _reduce(
    groupedChildList,
    (acc, chargeDetails) => {
      const { price, quantity, type, taxable } = getGeneralChildData(chargeDetails);
      const taxCodeId = getTaxCodeIdForCharge({ type, orderTaxCodeMappings });
      const calculatedTaxAmountSummary = getRowEntityTaxDetails({
        entityDetails: { ...chargeDetails, taxable: isChargeTaxable && taxable },
        unitAmount: price,
        quantity,
        taxCodeVsTaxRateDetailsMap,
        initialAccumulatorValue: acc,
        entityTaxCodeIds: [taxCodeId],
        taxCodeOverrides,
        taxCodeDetailsList,
        shouldNegateValue,
      });

      const { taxAmountByTaxCodeId: updatedTaxAmountByTaxCodeId = EMPTY_OBJECT, totalAmount: subTotal } =
        calculatedTaxAmountSummary;
      return {
        totalAmount: _sum([acc.totalAmount, subTotal]),
        taxAmountByTaxCodeId: updatedTaxAmountByTaxCodeId,
      };
    },
    { totalAmount: 0, taxAmountByTaxCodeId: EMPTY_OBJECT },
  );

const getRestockingFeeAmount = restockingFees =>
  _reduce(
    restockingFees,
    (acc, fee) => {
      const { price, quantity } = getGeneralChildData(fee);
      const amount = moneyMultiply({ amount: price, multiplier: quantity });
      return _multiply(_sum([acc, amount]), -1);
    },
    0,
  );

const getCouponAmount = coupons =>
  _reduce(
    coupons,
    (acc, coupon) => {
      const { price } = getGeneralChildData(coupon);
      return _sum([acc, price]);
    },
    0,
  );

export const getTaxCategoryListFromTaxDetails = ({ taxCodeIds, taxCodeDetailsList }) => {
  const taxDetailsListForDocument = _filter(taxCodeDetailsList, taxDetails =>
    _includes(taxCodeIds, _get(taxDetails, 'id')),
  );
  return _uniq(_compact(_map(taxDetailsListForDocument, taxDetails => TaxCodeDetailsReader.taxCategory(taxDetails))));
};

export const getTaxCodeListFromTaxDetails = ({
  taxCodeIds,
  taxCodeDetailsList,
  taxCodeOverrides,
  taxCodeVsTaxRateDetailsMap,
}) => {
  const taxDetailsListForDocument = _filter(taxCodeDetailsList, taxDetails =>
    _includes(taxCodeIds, _get(taxDetails, 'id')),
  );
  const taxCodeDetails = _map(taxDetailsListForDocument, taxDetails => {
    const taxRateDetails = taxCodeVsTaxRateDetailsMap
      ? taxCodeVsTaxRateDetailsMap[TaxCodeDetailsReader.id(taxDetails)]
      : EMPTY_OBJECT;
    const taxRate = _get(taxRateDetails, 'taxPercentage', 0);
    return `${TaxCodeDetailsReader.taxCode(taxDetails)} (${taxRate}%)`;
  });
  const manualTaxCodeDetails = _reduce(
    taxCodeOverrides,
    (acc, taxCodeOverride) => {
      if (!_includes(taxCodeIds, _get(taxCodeOverride, 'newTaxCodeId'))) {
        return acc;
      }
      const manualTaxRate = _get(taxCodeOverride, 'taxRate');
      const manualTaxCode = `Manual(${manualTaxRate}%)`;
      return [...acc, manualTaxCode];
    },
    EMPTY_ARRAY,
  );
  return _uniq(_compact([...taxCodeDetails, ...manualTaxCodeDetails]));
};

export const getTaxCategoryList = ({ taxCodeIds, taxCodeVsTaxRateDetailsMap = EMPTY_OBJECT }) =>
  _uniq(
    _compact(_map(taxCodeIds, taxCodeId => TaxCodeDetailsReader.taxCategory(taxCodeVsTaxRateDetailsMap[taxCodeId]))),
  );
/**
 * Get summary of all parts with children included
 * @param {*} param0
 */
const getPartListTotal = ({
  partList,
  orderTaxCodeMappings, // tax settings map for type * sale type stored at SO entity level
  taxCodeDetailsList,
  orderType,
  taxCodeVsTaxRateDetailsMap,
  taxCodeOverrides,
}) => {
  const taxCodeIds = getTaxCodeIdsFromTaxConfig({ orderTaxCodeMappings, taxCodeDetailsList, taxCodeOverrides });
  let subTotal = 0;
  let subTotalSale = 0;
  let subTotalReturn = 0;
  let subTotalFees = 0;
  let taxAmountByTaxCodeId = EMPTY_OBJECT;
  let discount = 0;

  _forEach(partList, part => {
    const partTaxCodeIds = getPartTaxCodeIds({ part, orderTaxCodeMappings });
    const quantity = SalesOrderDomain.isCreditMemo({ orderType })
      ? _toNumber(tget(part, 'returnQty', 0))
      : _toNumber(tget(part, 'requiredQty', 0));

    const sellingPrice = _toNumber(tget(part, 'sellingPrice', 0));
    const isPartTaxable = tget(part, 'taxable', false);

    if (_isNaN(quantity) || _isNaN(sellingPrice)) {
      return;
    }

    const children = tget(part, 'children', EMPTY_ARRAY);
    const unitAmount = getInHigherPrecisionCents({
      amount: sellingPrice,
      inCents: false,
    });

    const isCoreReturn = SalesOrderReturnPartLineDomain.isCoreReturnedGoodsType(part);
    const _coreReturnTaxCodeId = getTaxCodeIdForCharge({ type: CHARGE.CORE_RETURN, orderTaxCodeMappings });

    /* since there are no charges applied on the current part, just add the partAmount and calculate its tax per regime type */
    if (_isEmpty(children)) {
      const { totalAmount, taxAmountByTaxCodeId: rowTaxAmountByTaxCodeId } = getRowEntityTaxDetails({
        entityDetails: part,
        unitAmount,
        quantity,
        taxCodeVsTaxRateDetailsMap,
        initialAccumulatorValue: {
          taxAmountByTaxCodeId,
        },
        entityTaxCodeIds: isCoreReturn ? [_coreReturnTaxCodeId] : partTaxCodeIds,
        taxCodeOverrides,
        taxCodeDetailsList,
      });
      subTotal = _sum([subTotal, totalAmount]);
      taxAmountByTaxCodeId = rowTaxAmountByTaxCodeId;
      if (quantity > 0) {
        subTotalSale += totalAmount;
        return;
      }
      subTotalReturn += totalAmount;
      return;
    }

    const {
      core: coreList,
      coreReturn: coreReturnList,
      fee: feeList,
      stock: stockList,
      coupon: couponList,
    } = _groupBy(children, 'type');

    // Re-stocking fee is always non taxable
    const restockingFeeAmount = getRestockingFeeAmount(stockList);

    const { totalAmount: totalFeeAmount, taxAmountByTaxCodeId: totalFeeTaxAmountByTaxCodeId } =
      getGroupedChildAmountAndTax({
        groupedChildList: feeList,
        taxCodeVsTaxRateDetailsMap,
        isChargeTaxable: isPartTaxable,
        orderTaxCodeMappings,
        taxCodeOverrides,
        taxCodeDetailsList,
      });
    const { totalAmount: totalCoreAmount, taxAmountByTaxCodeId: totalCoreTaxAmountByTaxCodeId } =
      getGroupedChildAmountAndTax({
        groupedChildList: coreList,
        taxCodeVsTaxRateDetailsMap,
        isChargeTaxable: isPartTaxable,
        orderTaxCodeMappings,
        taxCodeOverrides,
        taxCodeDetailsList,
      });
    const { totalAmount: totalCoreReturnAmount, taxAmountByTaxCodeId: totalCoreReturnTaxAmountByTaxCodeId } =
      getGroupedChildAmountAndTax({
        groupedChildList: coreReturnList,
        shouldNegateValue: true,
        taxCodeVsTaxRateDetailsMap,
        isChargeTaxable: isPartTaxable,
        orderTaxCodeMappings,
        taxCodeOverrides,
        taxCodeDetailsList,
      });

    /* separate out the coupons on the basis of appliedAfterTax
    as beforeTaxCouponsAmount is the amount to be deducted from unit part amount on which tax will be calculated */
    const [afterTaxCoupons, beforeTaxCoupons] = _partition(couponList, 'appliedAfterTax');

    const beforeTaxCouponsAmount = getCouponAmount(beforeTaxCoupons);
    const afterTaxCouponsAmount = getCouponAmount(afterTaxCoupons);

    /* Subtract coupon amount from amount if coupon is before tax  */
    const unitTaxableAmount = moneyUtils.subtract(unitAmount, beforeTaxCouponsAmount);

    const { taxAmountByTaxCodeId: partTaxAmountByTaxCodeId } = getRowEntityTaxDetails({
      entityDetails: part,
      unitAmount: unitTaxableAmount,
      quantity,
      taxCodeVsTaxRateDetailsMap,
      initialAccumulatorValue: {
        taxAmountByTaxCodeId,
      },
      entityTaxCodeIds: partTaxCodeIds,
      taxCodeOverrides,
      taxCodeDetailsList,
    });

    /* discount is the sum of the all coupons */
    discount = _sum([
      discount,
      moneyMultiply({ amount: afterTaxCouponsAmount, multiplier: quantity }),
      moneyMultiply({ amount: beforeTaxCouponsAmount, multiplier: quantity }),
    ]);

    const partSubTotal = moneyMultiply({
      amount: unitAmount,
      multiplier: quantity,
    });
    subTotalSale = quantity > 0 ? subTotalSale + partSubTotal : subTotalSale;
    subTotalReturn = quantity < 0 ? subTotalReturn + partSubTotal : subTotalReturn;
    subTotal = _sum([
      _subtract(
        _sum([
          partSubTotal,
          totalCoreAmount,
          getIsCreditSaleEnabled() ? 0 : _sum([totalFeeAmount, restockingFeeAmount]),
        ]),
        totalCoreReturnAmount,
      ),
      subTotal,
    ]);
    subTotalFees += totalFeeAmount + restockingFeeAmount;
    taxAmountByTaxCodeId = _reduce(
      taxCodeIds,
      (acc, formattedTaxCodeId) => {
        const partTaxSummary = partTaxAmountByTaxCodeId[formattedTaxCodeId] || EMPTY_OBJECT;
        const {
          totalTaxAmount: totalPartTaxAmount,
          totalAmount: totalPartAmountPreTax,
          taxCode,
          taxRate,
          taxCodeId,
          superTaxCodeId,
        } = partTaxSummary;

        const coreTaxSummary = totalCoreTaxAmountByTaxCodeId[formattedTaxCodeId] || EMPTY_OBJECT;
        const {
          totalTaxAmount: totalCoreTaxAmount,
          totalAmount: totalCoreAmountPreTax,
          taxCodeId: coreTaxCodeId,
          superTaxCodeId: coreSuperTaxCodeId,
        } = coreTaxSummary;

        const feeTaxSummary = totalFeeTaxAmountByTaxCodeId[formattedTaxCodeId] || EMPTY_OBJECT;
        const {
          totalTaxAmount: totalFeeTaxAmount,
          totalAmount: totalFeeAmountPreTax,
          taxCodeId: feeTaxCodeId,
          superTaxCodeId: feeSuperTaxCodeId,
        } = feeTaxSummary;

        const coreReturnTaxSummary = totalCoreReturnTaxAmountByTaxCodeId[formattedTaxCodeId] || EMPTY_OBJECT;
        const {
          totalTaxAmount: totalCoreReturnTaxAmount,
          totalAmount: totalCoreReturnAmountPreTax,
          taxCodeId: coreReturnTaxCodeId,
          superTaxCodeId: coreReturnSuperTaxCodeId,
        } = coreReturnTaxSummary;

        return {
          ...acc,
          [formattedTaxCodeId]: {
            taxCodeId: taxCodeId || feeTaxCodeId || coreReturnTaxCodeId || coreTaxCodeId,
            superTaxCodeId: superTaxCodeId || feeSuperTaxCodeId || coreReturnSuperTaxCodeId || coreSuperTaxCodeId,
            taxCode,
            taxRate,
            totalTaxAmount: _sum([totalPartTaxAmount, totalCoreTaxAmount, totalFeeTaxAmount, totalCoreReturnTaxAmount]),
            totalAmount: _sum([
              totalPartAmountPreTax,
              totalCoreAmountPreTax,
              totalFeeAmountPreTax,
              totalCoreReturnAmountPreTax,
            ]),
          },
        };
      },
      taxAmountByTaxCodeId,
    );
  });
  return {
    partListSubTotal: _toNumber(subTotal),
    partListTaxByTaxCodeId: taxAmountByTaxCodeId,
    partListDiscount: _toNumber(discount),
    subTotalSale: _toNumber(subTotalSale),
    subTotalReturn: _toNumber(subTotalReturn),
    subTotalFees: _toNumber(subTotalFees),
  };
};

export const getAggregatedTaxDetails = ({
  partListTaxByTaxCodeId,
  assetFeeTaxByTaxCodeId,
  orderTaxCodeMappings,
  taxCodeDetailsList,
  taxCodeVsTaxRateDetailsMap,
  taxCodeOverrides,
}) => {
  let finalTotal = 0;
  let finalTotalTaxAmount = 0;
  const taxSetupTaxCodeIds = getTaxCodeIdsFromTaxConfig({ orderTaxCodeMappings, taxCodeDetailsList, taxCodeOverrides });
  const taxRoundingMethod = getTaxRoundingMethod();

  // Summing fee + part total and tax per taxCodeId to be used tax update modal
  const formattedTaxByTaxCodeId = _reduce(
    taxSetupTaxCodeIds,
    (acc, formattedTaxCodeId) => {
      const taxCodeId = _head(_split(formattedTaxCodeId, '_'));
      const updatedTaxCodeId = isManualTaxCode(taxCodeId) ? formattedTaxCodeId : taxCodeId;
      const taxCodeDetails = taxCodeVsTaxRateDetailsMap[formattedTaxCodeId] || EMPTY_OBJECT;
      const partTaxSummary = partListTaxByTaxCodeId[formattedTaxCodeId] || EMPTY_OBJECT;
      const assetFeeTaxSummary = assetFeeTaxByTaxCodeId[formattedTaxCodeId] || EMPTY_OBJECT;

      const { taxCode, taxPercentage, taxCategory, externalLabel } = taxCodeDetails || EMPTY_OBJECT;
      const { totalTaxAmount: totalPartTaxAmount, totalAmount: totalPartAmountPreTax, superTaxCodeId } = partTaxSummary;
      const {
        totalTaxAmount: totalAssetFeeTaxAmount,
        totalAmount: totalAssetFeeAmountPreTax,
        taxCodeId: assetTaxCodeId,
        superTaxCodeId: assetSuperTaxCodeId,
      } = assetFeeTaxSummary;
      const totalTaxAmount = _sum([totalPartTaxAmount, totalAssetFeeTaxAmount]) || 0;

      return {
        ...acc,
        [formattedTaxCodeId]: {
          taxCode,
          taxRate: taxPercentage,
          totalTaxAmount: getAmountInCurrency({
            amount: totalTaxAmount,
            taxRoundingMethod,
          }),
          totalAmount: getAmountInCurrency({
            amount: _sum([totalPartAmountPreTax, totalAssetFeeAmountPreTax]),
            taxRoundingMethod,
          }),
          taxCategory,
          externalLabel,
          taxCodeId: updatedTaxCodeId || assetTaxCodeId,
          superTaxCodeId: superTaxCodeId || assetSuperTaxCodeId,
          formattedTaxCodeId,
        },
      };
    },
    EMPTY_OBJECT,
  );

  // Calculating total tax and total amount per tax code id
  const aggregatedTaxByTaxCodeId = _reduce(
    taxSetupTaxCodeIds,
    (acc, formattedTaxCodeId) => {
      const taxCodeDetails = taxCodeVsTaxRateDetailsMap[formattedTaxCodeId] || EMPTY_OBJECT;
      const partTaxSummary = partListTaxByTaxCodeId[formattedTaxCodeId] || EMPTY_OBJECT;
      const assetFeeTaxSummary = assetFeeTaxByTaxCodeId[formattedTaxCodeId] || EMPTY_OBJECT;

      const { taxCode, taxPercentage, taxCategory, externalLabel } = taxCodeDetails;

      const subTaxCodeId = _head(_split(formattedTaxCodeId, '_'));
      const updatedSubTaxCodeId = isManualTaxCode(subTaxCodeId) ? `${subTaxCodeId}_${taxPercentage}` : subTaxCodeId;
      const { totalTaxAmount: totalPartTaxAmount, totalAmount: totalPartAmountPreTax } = partTaxSummary;
      const { totalTaxAmount: totalAssetFeeTaxAmount, totalAmount: totalAssetFeeAmountPreTax } = assetFeeTaxSummary;
      const totalTaxAmount = _sum([totalPartTaxAmount, totalAssetFeeTaxAmount]) || 0;

      const totalAmount = _sum([totalPartAmountPreTax, totalAssetFeeAmountPreTax]);
      const currentTaxDetails = acc[updatedSubTaxCodeId] || EMPTY_OBJECT;

      const { totalTaxAmount: currentTotalTaxAmount = 0, totalAmount: currentTotalAmount = 0 } = currentTaxDetails;
      const finalTaxAmount = _sum([totalTaxAmount, currentTotalTaxAmount]);
      const finalTotalAmount = _sum([totalAmount, currentTotalAmount]);
      return {
        ...acc,
        [updatedSubTaxCodeId]: {
          taxCode,
          taxRate: taxPercentage,
          totalTaxAmount: finalTaxAmount,
          totalAmount: finalTotalAmount,
          taxCategory,
          externalLabel,
          taxCodeId: isManualTaxCode(subTaxCodeId)
            ? replacePeriodsWithUnderscores(updatedSubTaxCodeId)
            : updatedSubTaxCodeId,
        },
      };
    },
    EMPTY_OBJECT,
  );

  // Rounding the tax amounts and calculating the sales order total + tax total amount
  const aggregatedTaxByTaxCodeIdRounded = _mapValues(aggregatedTaxByTaxCodeId, taxSummaryDetails => {
    const { totalTaxAmount = 0, totalAmount = 0 } = taxSummaryDetails || EMPTY_OBJECT;
    const totalTaxAmountRounded = getAmountInCurrency({ amount: totalTaxAmount, taxRoundingMethod });
    const totalAmountRounded = getAmountInCurrency({ amount: totalAmount, taxRoundingMethod });
    const totalAmountWithTaxRounded = _sum([totalTaxAmountRounded, totalAmountRounded]);
    finalTotalTaxAmount += totalTaxAmountRounded || 0;
    finalTotal += totalAmountWithTaxRounded || 0;
    return {
      ...taxSummaryDetails,
      totalTaxAmount: totalTaxAmountRounded,
      totalAmount: totalAmountRounded,
      totalAmountWithTax: totalAmountWithTaxRounded,
    };
  });

  // Calculating total tax amount per category
  const aggregatedTaxByTaxCategoryRounded = _reduce(
    aggregatedTaxByTaxCodeIdRounded,
    (acc, taxDetails) => {
      const { taxCategory, totalTaxAmount } = taxDetails;
      const currentTaxAmount = acc[taxCategory] || 0;
      return {
        ...acc,
        [taxCategory]: currentTaxAmount + totalTaxAmount,
      };
    },
    EMPTY_OBJECT,
  );

  return {
    aggregatedTaxByTaxCodeId: aggregatedTaxByTaxCodeIdRounded,
    formattedTaxByTaxCodeId,
    aggregatedTaxByTaxCategory: aggregatedTaxByTaxCategoryRounded,
    finalTotalTaxAmount,
    finalTotal,
  };
};

/**
 * Get full summary for sales order
 * @param {
 * partList,
 * assetFeeList,
 * orderTaxCodeMappings: response of getDocumentDefaultTaxCodeMappings(above)
 * taxCodeDetailsList: tax code details from core after fetching parts settings snapshot while creation of sales order
 * }
 */
export const getSalesOrderSummaryV2 = ({
  partList,
  assetFeeList,
  orderType,
  orderTaxCodeMappings,
  taxCodeDetailsList,
  taxCodeOverrides,
  taxable,
}) => {
  const taxRoundingMethod = getTaxRoundingMethod();

  const taxCodeVsTaxRateDetailsMap = getTaxCodeVsTaxRateDetailsMap({
    orderTaxCodeMappings,
    taxCodeDetailsList,
    taxCodeOverrides,
  });

  const {
    partListSubTotal,
    partListTaxByTaxCodeId,
    partListDiscount,
    subTotalSale,
    subTotalReturn,
    subTotalFees: partFeesTotal,
  } = getPartListTotal({
    partList,
    orderTaxCodeMappings,
    taxCodeDetailsList,
    orderType,
    taxCodeVsTaxRateDetailsMap,
    taxCodeOverrides,
  });
  const { totalAmount: assetFeeListSubtotal, taxAmountByTaxCodeId: assetFeeTaxByTaxCodeId } =
    getGroupedChildAmountAndTax({
      groupedChildList: assetFeeList,
      taxCodeVsTaxRateDetailsMap,
      isChargeTaxable: true,
      orderTaxCodeMappings,
      taxCodeOverrides,
      taxCodeDetailsList,
    });

  const subTotal = getAmountInCurrency({
    amount: _sum([partListSubTotal, getIsCreditSaleEnabled() ? 0 : assetFeeListSubtotal]),
    taxRoundingMethod,
  });

  const subTotalFees = getAmountInCurrency({
    amount: _sum([partFeesTotal, assetFeeListSubtotal]),
    taxRoundingMethod,
  });

  const finalTaxDetails = getAggregatedTaxDetails({
    partListTaxByTaxCodeId,
    assetFeeTaxByTaxCodeId,
    orderTaxCodeMappings,
    taxCodeDetailsList,
    taxCodeVsTaxRateDetailsMap,
    taxCodeOverrides,
  });

  const discount = getAmountInCurrency({ amount: partListDiscount, taxRoundingMethod });
  const finalTotal = _toNumber(
    taxable
      ? _sum([
          subTotal,
          getIsCreditSaleEnabled() ? subTotalFees : 0,
          finalTaxDetails?.finalTotalTaxAmount,
          discount * -1,
        ])
      : _sum([subTotal, discount * -1]),
  );
  const summary = {
    subTotal,
    discount,
    totalTaxAmount: taxable ? finalTaxDetails?.finalTotalTaxAmount : 0,
    aggregatedTaxByTaxCodeId: finalTaxDetails?.aggregatedTaxByTaxCodeId,
    formattedTaxByTaxCodeId: finalTaxDetails?.formattedTaxByTaxCodeId,
    aggregatedTaxByTaxCategory: finalTaxDetails?.aggregatedTaxByTaxCategory,
    finalTotal,
    subTotalSale: getAmountInCurrency({ amount: subTotalSale, taxRoundingMethod }),
    subTotalReturn: getAmountInCurrency({ amount: subTotalReturn, taxRoundingMethod }),
    subTotalFees,
  };
  return summary;
};

export const getTableDataForTaxUpdateModalV2 = ({ aggregatedTaxByTaxCodeId, taxCodeDetailsList }) => {
  const taxList = _flatMap(aggregatedTaxByTaxCodeId);
  const tableData = _reduce(
    taxList,
    (acc, taxDetails) => {
      const {
        taxCode,
        taxCodeId,
        taxRate,
        taxCategory,
        externalLabel,
        superTaxCodeId,
        totalAmount,
        totalTaxAmount,
        formattedTaxCodeId,
      } = taxDetails;
      if (_isNil(superTaxCodeId)) {
        return {
          ...acc,
          [taxCodeId]: {
            currentTaxCode: taxCode,
            currentTaxCodeId: taxCodeId,
            taxCategory,
            externalLabel,
            taxRate,
            totalAmount,
            totalTaxAmount,
            formattedTaxCodeId,
          },
        };
      }
      if (!_isEmpty(acc[superTaxCodeId])) {
        return acc;
      }
      const subTaxCodeList = _filter(taxList, taxDetail => _get(taxDetail, 'superTaxCodeId') === superTaxCodeId);
      const superTaxCodeDetails = _find(taxCodeDetailsList, detail => _get(detail, 'id') === superTaxCodeId);
      const superTaxCode = _get(superTaxCodeDetails, 'taxCode');
      const superTaxCodeTaxCategory = _get(superTaxCodeDetails, 'taxCategory');
      const superTaxCodeExternalLabel = _get(superTaxCodeDetails, 'externalLabel');
      const { superTaxRate, superTotalTaxAmount } = _reduce(
        subTaxCodeList,
        (superTaxAcc, subTaxCodeDetails) => ({
          superTaxRate: superTaxAcc.superTaxRate + _get(subTaxCodeDetails, 'taxRate', 0),
          superTotalTaxAmount: superTaxAcc.superTotalTaxAmount + _get(subTaxCodeDetails, 'totalTaxAmount', 0),
        }),
        {
          superTaxRate: 0,
          superTotalTaxAmount: 0,
        },
      );
      return {
        ...acc,
        [superTaxCodeId]: {
          currentTaxCode: superTaxCode,
          currentTaxCodeId: superTaxCodeId,
          taxCategory: superTaxCodeTaxCategory,
          externalLabel: superTaxCodeExternalLabel,
          taxRate: superTaxRate,
          totalAmount,
          totalTaxAmount: superTotalTaxAmount,
          formattedTaxCodeId: superTaxCodeId,
        },
      };
    },
    EMPTY_OBJECT,
  );
  return _flatMap(tableData);
};

export const formatDataForSummary = aggregatedTaxByTaxCodeId => {
  const tableData = _flatMap(aggregatedTaxByTaxCodeId);
  const filteredDataForSummary = _filter(tableData, taxDetails => _get(taxDetails, 'totalAmount', 0) !== 0);
  return filteredDataForSummary;
};

export const getParsedTaxDetailsForSavePayloadV2 = ({
  dealerConfig,
  rowTaxDetails,
  isRowTaxable,
  siteId,
  documentTaxDetails,
  isNewTaxCodeSetupEnabled,
}) => {
  if (isNewTaxCodeSetupEnabled) {
    return {
      taxable: isRowTaxable,
    };
  }
  return getParsedTaxDetailsForSavePayload({
    rowTaxDetails,
    isRowTaxable,
    siteId,
    documentTaxDetails,
    dealerConfig,
  });
};

export const getTaxConfigurationDetailsV2 = salesOrderEntity => {
  const taxConfiguration = SalesOrderReader.taxConfiguration(salesOrderEntity);
  const taxCodeOverrides = SalesOrderTaxConfigurationV2Reader.taxCodeOverrides(taxConfiguration);
  const taxSummary = SalesOrderReader.taxSummary(salesOrderEntity);
  const taxCodeGrid = SalesOrderTaxConfigurationV2Reader.taxCodeGrid(taxConfiguration);
  const taxDetails = SalesOrderTaxConfigurationV2Reader.taxDetails(taxConfiguration);
  const taxExempt = SalesOrderTaxConfigurationV2Reader.taxExempt(taxConfiguration);
  return {
    taxCodeOverrides,
    isDocumentTaxableInNewTaxCodeSetup: !taxExempt,
    taxCodeDetailsList: taxDetails,
    taxSummaryV2: taxSummary,
    partSaleTaxDetails: taxCodeGrid,
  };
};

const getUpdatedSummaryForCreditSale = (summary = EMPTY_OBJECT, salesOrderReturnsList = EMPTY_ARRAY) => {
  if (!getIsCreditSaleEnabled()) {
    return summary;
  }
  const {
    aggregatedTaxByTaxCategory: itemListAggregatedTaxByTaxCategory,
    aggregatedTaxByTaxCodeId: itemListAggregatedTaxByTaxCodeId,
    formattedTaxByTaxCodeId: itemListFormattedTaxByTaxCodeId,
    totalTaxAmount: itemListTotalTaxAmount,
    preciseTotalTaxAmount: itemListPreciseTotalTaxAmount,
  } = summary || EMPTY_OBJECT;
  const taxCategoryList = _keys(itemListAggregatedTaxByTaxCategory) || EMPTY_ARRAY;
  const taxCodeIdList = _keys(itemListAggregatedTaxByTaxCodeId) || EMPTY_ARRAY;
  const formattedTaxCodeIdList = _keys(itemListFormattedTaxByTaxCodeId) || EMPTY_ARRAY;
  const eligibleSalesOrderReturnList = _filter(
    salesOrderReturnsList,
    salesOrderReturn => !SalesOrderReturnDomain.isVoided(salesOrderReturn),
  );
  const {
    aggregatedTaxByTaxCategory,
    aggregatedTaxByTaxCodeId,
    formattedTaxByTaxCodeId,
    preciseTotalTaxAmount,
    totalTaxAmount,
  } = _reduce(
    eligibleSalesOrderReturnList,
    (acc, salesOrderReturn) => {
      const taxSummary = SalesOrderReturnReader.taxSummary(salesOrderReturn);
      const {
        aggregatedTaxByTaxCategory: _aggregatedTaxByTaxCategory,
        aggregatedTaxByTaxCodeId: _aggregatedTaxByTaxCodeId,
        formattedTaxByTaxCodeId: _formattedTaxByTaxCodeId,
        totalTaxAmount: returnTotalTaxAmount,
        preciseTotalTaxAmount: returnPreciseTotalTaxAmount,
      } = taxSummary;
      _forEach(taxCategoryList, taxCategory => {
        const taxAmount = acc.aggregatedTaxByTaxCategory[taxCategory] || 0;
        acc.aggregatedTaxByTaxCategory = {
          ...acc.aggregatedTaxByTaxCategory,
          [taxCategory]: _subtract(taxAmount - _aggregatedTaxByTaxCategory[taxCategory] || 0),
        };
      });
      _forEach(taxCodeIdList, taxCodeId => {
        const returnAggregatedTaxCodeIdDetails = _aggregatedTaxByTaxCodeId[taxCodeId] || EMPTY_OBJECT;
        const {
          totalAmount = 0,
          totalAmountWithTax = 0,
          totalTaxAmount: _totalTaxAmount = 0,
        } = returnAggregatedTaxCodeIdDetails;
        acc.aggregatedTaxByTaxCodeId = {
          ...acc.aggregatedTaxByTaxCodeId,
          [taxCodeId]: {
            ...acc.aggregatedTaxByTaxCodeId[taxCodeId],
            totalAmount: _subtract(acc.aggregatedTaxByTaxCodeId[taxCodeId].totalAmount, totalAmount),
            totalAmountWithTax: _subtract(
              acc.aggregatedTaxByTaxCodeId[taxCodeId].totalAmountWithTax,
              totalAmountWithTax,
            ),
            totalTaxAmount: _subtract(acc.aggregatedTaxByTaxCodeId[taxCodeId].totalTaxAmount, _totalTaxAmount),
          },
        };
      });
      _forEach(formattedTaxCodeIdList, taxCodeId => {
        const returnFormattedTaxCodeIdDetails = _formattedTaxByTaxCodeId[taxCodeId] || EMPTY_OBJECT;
        const {
          totalAmount = 0,
          totalAmountWithTax = 0,
          totalTaxAmount: _totalTaxAmount = 0,
        } = returnFormattedTaxCodeIdDetails;
        acc.formattedTaxByTaxCodeId = {
          ...acc.formattedTaxByTaxCodeId,
          [taxCodeId]: {
            ...acc.formattedTaxByTaxCodeId[taxCodeId],
            totalAmount: _subtract(acc.formattedTaxByTaxCodeId[taxCodeId].totalAmount, totalAmount),
            totalAmountWithTax: _subtract(
              acc.formattedTaxByTaxCodeId[taxCodeId].totalAmountWithTax,
              totalAmountWithTax,
            ),
            totalTaxAmount: _subtract(acc.formattedTaxByTaxCodeId[taxCodeId].totalTaxAmount, _totalTaxAmount),
          },
        };
      });
      acc.totalTaxAmount -= returnTotalTaxAmount;
      acc.preciseTotalTaxAmount -= returnPreciseTotalTaxAmount;

      return acc;
    },
    {
      aggregatedTaxByTaxCategory: itemListAggregatedTaxByTaxCategory,
      aggregatedTaxByTaxCodeId: itemListAggregatedTaxByTaxCodeId,
      formattedTaxByTaxCodeId: itemListFormattedTaxByTaxCodeId,
      preciseTotalTaxAmount: itemListPreciseTotalTaxAmount,
      totalTaxAmount: itemListTotalTaxAmount,
    },
  );
  return {
    ...summary,
    aggregatedTaxByTaxCategory,
    aggregatedTaxByTaxCodeId,
    formattedTaxByTaxCodeId,
    preciseTotalTaxAmount,
    totalTaxAmount,
  };
};

export const getTaxCodeSummaryDetails = ({
  isFormEditable,
  partList: partListWithLostSaleLines,
  salesOrderFees,
  taxCodeDetailsList,
  taxCodeOverrides,
  taxDetails,
  orderType,
  taxSummaryV2,
  isDocumentTaxableInNewTaxCodeSetup,
  salesOrderSummary = EMPTY_OBJECT,
  salesOrderReturnsList = EMPTY_ARRAY,
  isCustomerTaxExemptedInNewTaxCode,
}) => {
  const partList = _filter(partListWithLostSaleLines, part => !SalesOrderPartLineItemDomain.isLostSaleLine(part));
  const isCreditSaleEnabled = getIsCreditSaleEnabled();
  const summaryV2 = isFormEditable
    ? getSalesOrderSummaryV2({
        partList,
        assetFeeList: salesOrderFees,
        orderType,
        orderTaxCodeMappings: taxDetails,
        taxCodeDetailsList,
        taxCodeOverrides,
        taxable: isDocumentTaxableInNewTaxCodeSetup && !isCustomerTaxExemptedInNewTaxCode,
      })
    : taxSummaryV2;
  const updatedSummaryV2 = isFormEditable
    ? summaryV2
    : getUpdatedSummaryForCreditSale(summaryV2, salesOrderReturnsList);

  const { aggregatedTaxByTaxCategory = EMPTY_OBJECT } = updatedSummaryV2 || EMPTY_OBJECT;
  const taxCodeIds = getTaxCodeIdsFromTaxConfig({
    orderTaxCodeMappings: taxDetails,
    taxCodeDetailsList,
    taxCodeOverrides,
  });
  const taxCategoryList = _keys(aggregatedTaxByTaxCategory) || EMPTY_ARRAY;

  const updatedTaxSummaryV2 = {
    ...updatedSummaryV2,
    taxCodeIds,
    taxCategoryList,
    ...(!isFormEditable && isCreditSaleEnabled
      ? {
          subTotal: moneyUtils.withPrecision(SalesOrderSummaryV2Reader.subTotal(salesOrderSummary)),
          subTotalSale: moneyUtils.withPrecision(SalesOrderSummaryV2Reader.subTotalSale(salesOrderSummary)),
          subTotalReturn: moneyUtils.withPrecision(SalesOrderSummaryV2Reader.subTotalReturn(salesOrderSummary)),
          subTotalFees: moneyUtils.withPrecision(SalesOrderSummaryV2Reader.subTotalFees(salesOrderSummary)),
        }
      : EMPTY_OBJECT),
  };

  const aggregatedTaxByTaxCodeId = _get(updatedSummaryV2, 'aggregatedTaxByTaxCodeId', EMPTY_OBJECT);
  const formattedDataForSummary = formatDataForSummary(aggregatedTaxByTaxCodeId);
  return {
    updatedTaxSummaryV2,
    formattedDataForSummary,
  };
};

export const getTaxCodeCellProps = ({
  isChildOrFeeOrCoreReturnLine,
  type,
  groupIds,
  partId,
  partSaleTaxDetails,
  taxCodeOverrides,
  taxCodeDetailsList,
  taxCodeVsTaxRateDetailsMap,
}) => {
  const taxCodeIds = isChildOrFeeOrCoreReturnLine
    ? getTaxCodeIdForCharge({ type, orderTaxCodeMappings: partSaleTaxDetails })
    : getPartTaxCodeIds({ part: { groupIds }, orderTaxCodeMappings: partSaleTaxDetails });
  const overridenTaxCodeIds = getUpdatedTaxCodeIdsAfterOverride({
    taxCodeIds: isChildOrFeeOrCoreReturnLine ? [taxCodeIds] : taxCodeIds,
    taxCodeOverrides,
  });
  const taxCodeList =
    partId || isChildOrFeeOrCoreReturnLine
      ? getTaxCodeListFromTaxDetails({
          taxCodeIds: overridenTaxCodeIds,
          taxCodeDetailsList,
          taxCodeOverrides,
          taxCodeVsTaxRateDetailsMap,
        })
      : EMPTY_ARRAY;
  const taxCodeProps = {
    items: _isEmpty(taxCodeList) ? ['-'] : taxCodeList,
    numberOfElementsToshow: 2,
  };
  return taxCodeProps;
};
