import _castArray from 'lodash/castArray';
import _ceil from 'lodash/ceil';
import _compact from 'lodash/compact';
import _delay from 'lodash/delay';
import _filter from 'lodash/filter';
import _find from 'lodash/find';
import _floor from 'lodash/floor';
import _forEach from 'lodash/forEach';
import _get from 'lodash/get';
import _head from 'lodash/head';
import _includes from 'lodash/includes';
import _isEmpty from 'lodash/isEmpty';
import _isEqual from 'lodash/isEqual';
import _isNil from 'lodash/isNil';
import _isObject from 'lodash/isObject';
import _join from 'lodash/join';
import _keyBy from 'lodash/keyBy';
import _map from 'lodash/map';
import _min from 'lodash/min';
import _noop from 'lodash/noop';
import _reduce from 'lodash/reduce';
import _reject from 'lodash/reject';
import _some from 'lodash/some';
import _toString from 'lodash/toString';

import { produce } from 'immer';
import { Map as IMap } from 'immutable';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { ENTITY_REFERENCE_TYPES } from '@tekion/tekion-base/constants/parts/entityReferenceTypes';
import { SALES_STATUS } from '@tekion/tekion-base/constants/parts/sales';
import { SOR_STATUS } from '@tekion/tekion-base/constants/parts/specialOrderRequests';
import { SALE_TYPES } from '@tekion/tekion-base/constants/saleTypes';
import { STATUS } from '@tekion/tekion-base/constants/status.constants';
import { formatPartName, formatPartNumber, formatPartDescription } from '@tekion/tekion-base/formatters/parts/general';
import AddressReader from '@tekion/tekion-base/readers/Address';
import CouponReader from '@tekion/tekion-base/readers/Coupon';
import FeeReader from '@tekion/tekion-base/readers/fee';
import OEMSitesReader from '@tekion/tekion-base/readers/OemSites';
import PartReader from '@tekion/tekion-base/readers/Part';
import sourceCodeReader from '@tekion/tekion-base/readers/PartsSourceCode';
import priceCodeReader from '@tekion/tekion-base/readers/PriceCode';
import VehicleReader from '@tekion/tekion-base/readers/Vehicle';
import {
  getValueFromApplicationLocalStorage,
  setValueInApplicationLocalStorage,
} from '@tekion/tekion-base/utils/applicationLocalStorage.utils';
import { getMonth, getCurrentYearValue } from '@tekion/tekion-base/utils/dateUtils';
import { tget } from '@tekion/tekion-base/utils/general';
import getCurrentSiteIdFromLS from '@tekion/tekion-base/utils/getCurrentSiteIdFromLS';
import { add, divide, multiply, subtract, sum } from '@tekion/tekion-base/utils/math.utils';
import tMoneyUtils from '@tekion/tekion-base/utils/money';

import { getFormattedBinNumberListFromPartDetails } from '@tekion/tekion-business/src/appServices/parts/helpers/bin.helpers';
import { getUserAccessibleSites } from '@tekion/tekion-business/src/appServices/parts/helpers/dealerSiteFilter.helpers';
import SalesOrderCustomerReader from '@tekion/tekion-business/src/appServices/parts/salesOrder/readers/SalesOrder.customer.reader';
import SalesOrderReader from '@tekion/tekion-business/src/appServices/parts/salesOrder/readers/SalesOrder.reader';
import SalesOrderFeeLineItemReader from '@tekion/tekion-business/src/appServices/parts/salesOrder/readers/SalesOrderFeeLineItem.reader';
import SalesOrderInvoiceReader from '@tekion/tekion-business/src/appServices/parts/salesOrder/readers/SalesOrderInvoice.reader';
import SalesOrderPartLineChargeReader from '@tekion/tekion-business/src/appServices/parts/salesOrder/readers/SalesOrderPartLine.charge.reader';
import SalesOrderPartLineItemReader from '@tekion/tekion-business/src/appServices/parts/salesOrder/readers/SalesOrderPartLineItem.reader';
import SalesOrderPOLineItemReader from '@tekion/tekion-business/src/appServices/parts/salesOrder/readers/SalesOrderPOLineItem.reader';
import SalesOrderReturnReader from '@tekion/tekion-business/src/appServices/parts/salesOrder/readers/SalesOrderReturn.reader';
import SalesOrderSorPartLineReader from '@tekion/tekion-business/src/appServices/parts/salesOrder/readers/SalesOrderSorPartLine.reader';
import { isPrimarySaleType } from '@tekion/tekion-business/src/appServices/parts/salesOrder/salesOrder.helpers';
import {
  getAmountInCurrency,
  getInHigherPrecisionCents,
} from '@tekion/tekion-business/src/appServices/parts/utils/tMoney.utils';

import { LOST_SALE_REASONS } from '@tekion/tekion-widgets/src/appServices/parts/constants/lostSaleConstants';
import {
  REGULATION_VS_DISPLAY_MESSAGE,
  REGULATION_TYPES,
} from '@tekion/tekion-widgets/src/molecules/CustomerDeletionIconRenderer/CustomerDeletionIconRenderer.constants';

import { CHILD_TYPES, MPL_REF_TYPES } from 'constants/general.constants';
import { FIELD_IMPORT_SOURCE_TYPE_KEYS } from 'constants/part.constants';

import {
  getPartIdentifierId,
  getResolvedPartDetails,
  getResolvedPartDetailsByPartIdentifierId,
} from 'helpers/multiWarehouse/partResolve.helpers';

import { LOST_SALE_PART_LIST_COLUMN_IDS } from 'organisms/LostSaleDrawer/lostSaleDrawer.constants';
import { CREATE_OPTION_ACTION_TYPES } from 'organisms/PartSearch/partSearch.constants';

import { hasAllowSalesOrderVoidPermission, hasEditPermissionsSalesOrder } from 'permissions/partsPermissions';

import GeneralSettingsReader from 'readers/generalSettings.reader';
import PriceCodeReader from 'readers/priceCode.reader';

import GeneralSettingsDomain from 'shared/PartsSettings/domain/GeneralSettings.domain';
import { GROSS_PROFIT_TYPES } from 'shared/PartsSettings/generalSettings/constants/grossProfit.constants';
import { UNLINK_ACTION_TYPES } from 'shared/specialOrderRequest/constants/sorCancellation.constants';

import {
  getIsPartCrossSiteAccessEnabled,
  getIsBinShelfDrawerFeatureEnabled,
  getIsMultiOemSwitchEnabled,
  getIsBringozDeliveryEnabled,
  getIsCoreInventoryEnabled,
  isROV3ServiceEnabled,
  isPartsSideDynamicSplitEnabled,
} from 'utils/general';
import { getLocaleValue } from 'utils/multiLingualUtils';
import partsEnv from 'utils/partsEnv';
import { makeLocalStorageKey } from 'utils/storageUtils';
import { getTaxRoundingMethod } from 'utils/tax.utils';

import { PART_LIST_COLUMN_IDS as SOR_CANCELLATION_PART_LIST_COLUMN_KEYS } from '../../components/SORCancellationDrawer/constants/general.constants';
import SalesOrderDomain from '../../domain/SalesOrder.domain';
import SalesOrderInvoiceDomain from '../../domain/SalesOrderInvoice.domain';
import SalesOrderPartLineChargeDomain, { CHARGE_TYPES } from '../../domain/SalesOrderPartLineCharge.domain';
import SalesOrderPartLineItemDomain from '../../domain/SalesOrderPartLineItem.domain';
import SalesOrderReturnDomain from '../../domain/SalesOrderReturn.domain';
import SalesOrderSORPartLineDomain from '../../domain/SalesOrderSORPartLine.domain';
import SalesOrderTaskDomain from '../../domain/SalesOrderTask.domain';
import { INTERNAL_ACCOUNTS_VS_PRICING_MAP, SALES_ORDER_INVOICE_VARIANT } from '../../partsSalesOrder.constants';
import {
  SALES_ORDER_FORM_KEYS,
  DELIVERY_METHOD_KEYS,
  SALE_TYPE_PRICING,
  SALES_ORDER_ITEM_LINE_TYPE,
  PRICING_TYPES,
  CUSTOM_ROW_TYPES,
} from '../constants/general.constants';

import { getOnHandQuantityAccordingToSelectedBinId } from './bin.helper';
import { getIsSOInEditableState } from './general.helpers';
import { getDocumentDefaultTaxCodeMappings } from './taxV2.helpers';

const DELAY_AFTER_BLUR = 100;

export const getDocumentInitialSiteId = () => {
  const siteOptions = _reject(getUserAccessibleSites(), site => !!OEMSitesReader.mainSite(site));
  return getIsPartCrossSiteAccessEnabled() ? OEMSitesReader.siteId(_head(siteOptions)) : getCurrentSiteIdFromLS();
};

export const isSaleTypeDisabled = ({ isReissuing = false, frozenSalesOrderEntity, salesOrderInvoiceList }) => {
  if (_isEmpty(frozenSalesOrderEntity)) return false;

  const isPaymentMade = !SalesOrderDomain.isSalesOrderUnpaid(frozenSalesOrderEntity);

  return (
    !(isReissuing || SalesOrderDomain.isDraft(frozenSalesOrderEntity)) ||
    SalesOrderDomain.isCreditMemo(frozenSalesOrderEntity) ||
    SalesOrderDomain.isClosed(frozenSalesOrderEntity) ||
    SalesOrderInvoiceDomain.hasActiveInvoice(salesOrderInvoiceList) ||
    isPaymentMade
  );
};

export const sanitizeVehicleList = (vehicleList, customerId) =>
  _reduce(
    vehicleList,
    (acc, vehicleItem) => {
      const associatedCustomers = VehicleReader.associatedCustomers(vehicleItem);
      const isCustomerAttachedToVehicle = _some(associatedCustomers, {
        customerId,
        attached: true,
      });
      if (isCustomerAttachedToVehicle) {
        return [
          ...acc,
          {
            ...vehicleItem,
            id: VehicleReader.id(vehicleItem),
            vin: VehicleReader.vinIgnoreCase(vehicleItem),
          },
        ];
      }
      return acc;
    },
    [],
  );

export const canVoidSoWithPermissionCheck = salesOrder => {
  if (hasAllowSalesOrderVoidPermission() && SalesOrderDomain.canVoid(salesOrder)) return true;
  return false;
};
const isSaleTypeActive = (saleType, activeSaleTypes) => !_isEmpty(_find(activeSaleTypes, { saleSubTypeId: saleType }));

export const getCustomerSaleTypeDetails = ({ customerDetails, activeSaleTypes }) => {
  const customerSaleType = _get(customerDetails, 'setup.part.defaultSaleType');
  const ifSalesTypeAttachedToCustomer = !!customerSaleType;
  if (
    customerSaleType &&
    !isPrimarySaleType(customerSaleType) &&
    !isSaleTypeActive(customerSaleType, activeSaleTypes)
  ) {
    return {
      ifSalesTypeAttachedToCustomer: false,
      saleType: _get(_head(activeSaleTypes), 'saleSubTypeId'),
    };
  }

  return {
    ifSalesTypeAttachedToCustomer,
    saleType: customerSaleType || SALE_TYPES.RETAIL,
  };
};

export const getAddressLine = (address = EMPTY_OBJECT) => {
  const line1 = AddressReader.line1(address) || '';
  const line2 = AddressReader.line2(address) || '';
  const line3 = AddressReader.line3(address) || '';
  const line4 = AddressReader.line4(address) || '';

  return `${line1} ${line2} ${line3} ${line4}`;
};

export const getAddressValuesForForm = address => {
  const line1 = AddressReader.line1(address);
  if (_isEmpty(line1)) return EMPTY_STRING;
  const routeNumber = _get(address, 'routeNumber');
  const addressLine = getAddressLine(address);
  const city = AddressReader.city(address);
  const country = AddressReader.country(address);
  const state = AddressReader.state(address);
  const pincode = AddressReader.pincode(address);
  const postalCode = AddressReader.postalCode(address);
  const routeNumberDisplay = `, ${routeNumber}`;
  const countryDisplay = `, ${country}`;
  const cityDisplay = `, ${city}`;
  const stateDisplay = `, ${state}`;

  return `${addressLine}${city ? cityDisplay : EMPTY_STRING}${state ? stateDisplay : EMPTY_STRING}, ${
    postalCode || pincode
  }${country ? countryDisplay : EMPTY_STRING}${routeNumber ? routeNumberDisplay : EMPTY_STRING}`;
};

export function getDefaultPricingForRetailAndWholeSale(defaultPricing, saleType) {
  const accessorForDefaultPricing = SALE_TYPE_PRICING[saleType] || _noop;
  return accessorForDefaultPricing({ defaultPricing });
}

export const getDefaultPricingForCustomSale = ({ defaultPricing = EMPTY_OBJECT, saleType }) => {
  const { saleTypePricing = EMPTY_ARRAY } = defaultPricing;
  const saleTypePricingById = _keyBy(saleTypePricing, 'saleSubTypeId');
  return _get(saleTypePricingById, [saleType, 'defaultPricing']);
};

export const getCustomerDefaultPriceCodeDetails = ({
  customerDetails,
  siteId = getCurrentSiteIdFromLS(),
  saleType,
  defaultPricing,
}) => {
  const customerPriceCode = getIsMultiOemSwitchEnabled()
    ? _get(customerDetails, `setup.part.siteLevelDefaultPartPricing.${siteId}`)
    : _get(customerDetails, 'setup.part.defaultPartPricing');
  const ifPriceCodeAttachedToCustomer = !!customerPriceCode;
  const priceCodeId = customerPriceCode || getDefaultPricingForRetailAndWholeSale(defaultPricing, saleType);
  return {
    priceCodeId,
    ifPriceCodeAttachedToCustomer,
  };
};

// This function if being used for both invoice and return
const getEntityStatusCount = entity =>
  _reduce(
    entity,
    (result, item) => {
      const { status } = item;
      if (status === SALES_STATUS.VOIDED) return result;
      const total = result.total + 1;
      if (result[status]) {
        return {
          ...result,
          [status]: result[status] + 1,
          total,
        };
      }
      return {
        ...result,
        [status]: 1,
        total,
      };
    },
    { total: 0 },
  );

export const getInvoiceStatus = (invoice, frozenSalesOrderEntity) => {
  const isSalesOrderClosedOrReopened = SalesOrderDomain.isSalesOrderClosedOrReopened(frozenSalesOrderEntity);
  if (SalesOrderInvoiceDomain.isInvoiceOpen(invoice) && isSalesOrderClosedOrReopened) {
    return SALES_STATUS.CLOSED_BY_SO;
  }
  return SalesOrderInvoiceReader.status(invoice);
};

export const getSalesOrderMetaInfo = ({ salesOrderInvoiceList, salesOrderReturnsList, frozenSalesOrderEntity }) => {
  const salesOrderInvoicesWithStatus = _map(salesOrderInvoiceList, invoice => ({
    ...invoice,
    status: getInvoiceStatus(invoice, frozenSalesOrderEntity),
  }));

  const salesOrderReturnsWithStatus = _map(salesOrderReturnsList, salesOrderReturn => ({
    ...salesOrderReturn,
    status: SalesOrderReturnDomain.getReturnOrderStatus(salesOrderReturn),
  }));

  const salesOrderInvoiceCountMapByStatus = getEntityStatusCount(salesOrderInvoicesWithStatus);
  const salesOrderReturnCountMapByStatus = getEntityStatusCount(salesOrderReturnsWithStatus);

  return {
    salesOrderInvoiceCountMapByStatus,
    salesOrderReturnCountMapByStatus,
  };
};

export const mergeVehicleListWithSoVehicle = (vehicleList, salesOrderDetails) => {
  const customer = SalesOrderReader.customer(salesOrderDetails);
  const customerId = SalesOrderCustomerReader.id(customer);
  const soVehicle = SalesOrderCustomerReader.vehicle(customer);
  const sanitizedVehicleList = sanitizeVehicleList(vehicleList, customerId);
  const soVehicleId = _get(soVehicle, 'id', null);
  if (soVehicleId && !_includes(_map(sanitizedVehicleList, 'id'), soVehicleId)) {
    return [...sanitizedVehicleList, soVehicle];
  }

  return sanitizedVehicleList;
};

export const normalizeResolvedPartsDataById = ({ resolvedPartDetailsByPartIdentifierId, sorOnHoldDetails }) => {
  const sorOnHoldDetailsKeyByPartIdentifierId = getResolvedPartDetailsByPartIdentifierId({
    partDetailsList: sorOnHoldDetails,
  });
  return _reduce(
    resolvedPartDetailsByPartIdentifierId,
    (acc, partData) => {
      const partId = PartReader.id(partData.part);
      const warehouseId = PartReader.warehouseId(partData.part);
      const partIdentifierId = getPartIdentifierId({ partId, warehouseId });
      return {
        ...acc,
        [partIdentifierId]: {
          ...partData,
          onHoldSORParts: sorOnHoldDetailsKeyByPartIdentifierId[partIdentifierId] || null,
        },
      };
    },
    EMPTY_OBJECT,
  );
};

export const getBinsForPart = (partListMap, item) => {
  const partDetails = tget(partListMap, item, EMPTY_ARRAY);
  return getFormattedBinNumberListFromPartDetails({
    partDetails,
    isBinShelfDrawerFeatureEnabled: getIsBinShelfDrawerFeatureEnabled(),
  });
};

export const isCommentRow = props => _get(props, 'row.original.type') === CUSTOM_ROW_TYPES.COMMENT;
export const isReturnReasonRow = props => _get(props, 'row.original.type') === CUSTOM_ROW_TYPES.RETURN_REASON;
export const isExistingReferencedReturnRow = rowData => !_isNil(_get(rowData, 'returnNumber'));

export const getIsPartSourceCodeTaxable = ({ partData, resolvedSourceCodeById }) => {
  const { sourceCodeId } = partData || EMPTY_OBJECT;
  const sourceCodeDetails = _get(resolvedSourceCodeById, sourceCodeId, EMPTY_OBJECT);
  return !sourceCodeReader.taxExempted(sourceCodeDetails);
};

export const isCreateNewOptionEnterEvent = eventDetails => {
  const isCreateOption = _get(eventDetails, 'action', '') === CREATE_OPTION_ACTION_TYPES.ON_ENTER;
  return isCreateOption;
};

export const getShortageResolvedQuantity = shortageSaleLineItem => _get(shortageSaleLineItem, 'resolvedQuantity', 0);

export const getCorePartDetailsForCalculatingPrices = (children, coreInventoryDetails) => {
  const coreChild = _find(children, SalesOrderPartLineChargeDomain.isCoreCharge);
  if (!coreChild || !getIsCoreInventoryEnabled()) return null;
  const corePartId = SalesOrderPartLineChargeReader.corePartId(coreChild);
  const corePartInventoryDetails = _find(coreInventoryDetails, { partId: corePartId });
  return {
    partId: corePartId,
    listPrice: PartReader.listPrice(corePartInventoryDetails),
    costPrice: PartReader.cost(corePartInventoryDetails),
    sourceCodeId: PartReader.partSourceId(corePartInventoryDetails),
  };
};

export const getSellingPriceReCalculationPayload = ({ partList, priceCodeId, coreInventoryDetails }) =>
  _reduce(
    partList,
    (acc, partLineItem) => {
      const children = SalesOrderPartLineItemReader.charges(partLineItem);
      const corePartDetails = children ? getCorePartDetailsForCalculatingPrices(children, coreInventoryDetails) : null;
      const formattedPartDetails = {
        lineId: SalesOrderPartLineItemReader.lineId(partLineItem),
        partId: SalesOrderPartLineItemReader.partId(partLineItem),
        costPrice: SalesOrderPartLineItemReader.costPrice(partLineItem),
        listPrice: SalesOrderPartLineItemReader.listPrice(partLineItem),
        tradePrice: SalesOrderPartLineItemReader.tradePrice(partLineItem),
        comp: SalesOrderPartLineItemReader.comp(partLineItem),
        sourceCodeId: SalesOrderPartLineItemReader.sourceCodeId(partLineItem),
        dynamicPricing: SalesOrderPartLineItemReader.listPrice(partLineItem) <= 0,
        priceCodeId,
        temporaryPart: SalesOrderPartLineItemReader.isTemporaryPart(partLineItem),
        orderQty: SalesOrderPartLineItemReader.orderQty(partLineItem),
        sorPartExist: SalesOrderPartLineItemReader.sorPartExist(partLineItem),
      };
      if (corePartDetails) {
        return [...acc, corePartDetails, formattedPartDetails];
      }
      return [...acc, formattedPartDetails];
    },
    EMPTY_ARRAY,
  );

export const getIsPartsUnusualSaleAllowedValue = ({
  isCustomerDefaultToPartsUnusualSale,
  siteId,
  partsSettingsMap,
}) => {
  const siteIdToConsider = siteId;
  const unusualSaleValue = GeneralSettingsReader.unusualSale(partsSettingsMap?.get(siteIdToConsider)) || false;
  return {
    isPartsUnusualSaleAllowed: unusualSaleValue || isCustomerDefaultToPartsUnusualSale,
  };
};

export const hasCommentChild = props => {
  const { row } = props;
  const children = _get(row, `original.children`);
  if (_isEmpty(children)) return false;

  return _some(children, { type: CUSTOM_ROW_TYPES.COMMENT });
};

export const hasReturnReasonChild = props => {
  const { row } = props;
  const children = _get(row, `original.children`);
  if (_isEmpty(children)) return false;

  return _some(children, { type: CUSTOM_ROW_TYPES.RETURN_REASON });
};

export const isDuplicateFee = (rowData, selectedFeeValue) =>
  _some(_get(rowData, 'children'), item => SalesOrderPartLineChargeReader.feeCode(item) === selectedFeeValue);

export const getSellingPrice = ({ responseData, partDetails }) => {
  const { inventoryDetails, sellingPrice: partSellingPriceDetails } = responseData;
  const { oecImportPricing = EMPTY_OBJECT } = partDetails;
  const { part } = inventoryDetails;

  const { listPrice: importedListPrice, netBuyerCost } = oecImportPricing;
  const listPrice = importedListPrice || responseData?.listPrice || PartReader.listPrice(part);
  const calculatedSellingPrice = netBuyerCost || partSellingPriceDetails;
  return calculatedSellingPrice || listPrice;
};

export const getMinimumGrossProfitPercentageRequired = ({
  priceCodeDetails,
  partsSettingsMap = IMap(),
  currentSiteId,
}) => {
  const partSettings = partsSettingsMap.get(currentSiteId) || partsEnv.partsSettings;
  if (GeneralSettingsReader.profitType(partSettings) === GROSS_PROFIT_TYPES.PRICE_CODE_LEVEL) {
    if (!_isEmpty(priceCodeDetails)) {
      return (
        PriceCodeReader.minimumSalesOrderGrossProfit(priceCodeDetails) ||
        GeneralSettingsReader.minimumSalesOrderGrossProfitPriceCodeLevel(partSettings)
      );
    }

    return GeneralSettingsReader.minimumSalesOrderGrossProfitPriceCodeLevel(partSettings);
  }
  return GeneralSettingsReader.minimumSalesOrderGrossProfit(partSettings);
};

export const getPartListWithPriceMismatch = partList =>
  _filter(
    partList,
    ({ trackedSellingPrice, sellingPrice, isExternalImportedSellingPrice }) =>
      isExternalImportedSellingPrice && !_isEqual(_toString(trackedSellingPrice), _toString(sellingPrice)),
  );

export const getPartListWithGrossProfitLessThanMinimumLimit = ({
  partList,
  partsSettingsMap,
  currentSiteId,
  resolvedPriceCodeDetails,
  salesOrderInvoiceList,
}) => {
  const minimumGrossProfitPercentageRequired = getMinimumGrossProfitPercentageRequired({
    priceCodeDetails: resolvedPriceCodeDetails,
    partsSettingsMap,
    currentSiteId,
  });

  return {
    partListWithGrossProfitLessThanMinimumLimit: _filter(partList, part => {
      const tasks = _get(part, 'tasks');
      const { isLineItemEditable = true, isSellingPriceDisabled, isExchangePartLine } = part;
      const partRefLineId = SalesOrderPartLineItemReader.id(part);
      const { invoicedQuantity } = SalesOrderInvoiceDomain.getPartInvoicedQtyInfo(
        salesOrderInvoiceList,
        partRefLineId,
        false,
      );
      const isExistingExchangeLine =
        isExchangePartLine && !_isNil(SalesOrderPartLineItemReader.returnReferenceId(part));
      if (
        SalesOrderTaskDomain.areInProgress(tasks) ||
        SalesOrderTaskDomain.areToBeExecuted(tasks) ||
        SalesOrderTaskDomain.isCreateNewPartTaskPending(tasks) ||
        !isLineItemEditable ||
        isSellingPriceDisabled ||
        invoicedQuantity > 0 ||
        isExistingExchangeLine
      ) {
        return false;
      }
      const grossProfit = SalesOrderPartLineItemDomain.getGrossProfitPercentage(part);
      return grossProfit < minimumGrossProfitPercentageRequired;
    }),
    minimumGrossProfitPercentageRequired,
  };
};

export const isDeliveryMethodSubFieldVisible = _deliveryMethod => {
  const deliveryMethod = _head(_castArray(_deliveryMethod));
  if (getIsBringozDeliveryEnabled() && deliveryMethod === DELIVERY_METHOD_KEYS.DRIVER_DELIVERY) {
    return false;
  }
  return true;
};

export const isRouteInfoLabelVisible = _deliveryMethod => {
  const deliveryMethod = _head(_castArray(_deliveryMethod));
  if (deliveryMethod === DELIVERY_METHOD_KEYS.CUSTOMER_PICKUP || !deliveryMethod) {
    return true;
  }

  return false;
};

export const getTotalSorHoldQuantityForPart = (partSorHoldDetails = EMPTY_ARRAY) =>
  _reduce(partSorHoldDetails, (acc, sorPart) => (sorPart.sorQty ? add(acc, sorPart.sorQty) : acc), 0);

export const isSorHoldQtyGreaterThanRequiredQty = (partData, partSorExpectedHoldRequestDetails) => {
  const requiredQty = SalesOrderPartLineItemReader.requiredQty(partData);
  const totalQtyInProgress = SalesOrderPartLineItemDomain.getEffectiveConfirmedQty(partData);
  const actualRequiredQty = subtract(requiredQty, totalQtyInProgress);
  const totalSorHoldQty = getTotalSorHoldQuantityForPart(partSorExpectedHoldRequestDetails);
  return totalSorHoldQty > actualRequiredQty;
};

const getStockVehicleKeys = (stockVehicleAccounts, costCentersByKey) =>
  _map(stockVehicleAccounts, stockVehicleAccountItem => _get(costCentersByKey, `${stockVehicleAccountItem}.value`));

export const getShouldShowStockVehicle = (values, stockVehicleAccounts, costCenters) => {
  const internalAccount = _get(values, SALES_ORDER_FORM_KEYS.INTERNAL_DEBIT_ACCOUNT);
  const saleType = _get(values, SALES_ORDER_FORM_KEYS.SALE_TYPE);
  const costCentersByKey = _keyBy(costCenters, 'value');

  return (
    saleType === SALE_TYPES.INTERNAL &&
    _includes(getStockVehicleKeys(stockVehicleAccounts, costCentersByKey), _head(internalAccount)) &&
    _includes(stockVehicleAccounts, _head(internalAccount))
  );
};

export const getReCalculateFeesForAssetChargesPayload = ({
  assetCharges,
  salesOrderInvoiceList,
  totalPartsListAmount,
  frozenSalesOrderEntity,
}) =>
  _reduce(
    assetCharges,
    (acc, feeLineItem) => {
      const invoicedQty = SalesOrderInvoiceDomain.getFeeInvoicedQty(
        salesOrderInvoiceList,
        SalesOrderFeeLineItemReader.id(feeLineItem),
      );
      const overridden = SalesOrderFeeLineItemReader.overridden(feeLineItem);
      const feeCode = SalesOrderFeeLineItemReader.feeCode(feeLineItem);
      const overriddenCostAmount = SalesOrderFeeLineItemReader.chargeCostPrice(feeLineItem);
      const isInvoiced = invoicedQty > 0;
      if (isInvoiced || overridden || _isNil(feeCode)) {
        return acc;
      }
      return [
        ...acc,
        {
          requestId: SalesOrderFeeLineItemReader.id(feeLineItem),
          appliedFeeCodesV2: [
            {
              feeCode,
              ...(!_isNil(overriddenCostAmount) && {
                overriddenCostAmount: tMoneyUtils.toInt(overriddenCostAmount),
              }),
            },
          ],
          partsAmount: tMoneyUtils.toInt(totalPartsListAmount),
          ...getFeePayerPayloadWithServiceV3Context({
            frozenSalesOrderEntity,
            partsAmount: tMoneyUtils.toInt(totalPartsListAmount),
          }),
        },
      ];
    },
    EMPTY_ARRAY,
  );

export const isMplQuoteButtonDisabled = mplRefType => {
  if (_isNil(mplRefType)) return false;
  return mplRefType === MPL_REF_TYPES.EXPORT;
};

export const isMplExportButtonDisabled = mplRefType => {
  if (_isNil(mplRefType)) return false;
  return mplRefType !== MPL_REF_TYPES.EXPORT;
};

export const isQuoteChanged = (currentQuoteRequestId, newQuoteRequestId) => {
  const validCurrentValue = currentQuoteRequestId || '';
  const validNewValue = newQuoteRequestId || '';

  return validCurrentValue !== validNewValue;
};

// This function compares if quoteApplied var is changed or not
export const isQuoteAppliedChanged = (currentValue, newValue) => !!currentValue !== !!newValue;

export const getOrderQuantity = salesOrderPartLineItem => {
  const sorParts = SalesOrderPartLineItemReader.sorParts(salesOrderPartLineItem);
  if (_isEmpty(sorParts)) {
    return SalesOrderPartLineItemReader.orderPendingQty(salesOrderPartLineItem);
  }
  const sorDetails = getSOROrderDetails(salesOrderPartLineItem);
  return tget(sorDetails, 'qty', 0);
};

export const getNonDraftSors = sorParts =>
  _find(
    sorParts,
    item => !SalesOrderSorPartLineReader.soPartStatus(item) && !SalesOrderSorPartLineReader.sourceDeleted(item),
  );

export const getSOROrderDetails = salesOrderPartLineItem => {
  const sorParts = SalesOrderPartLineItemReader.sorParts(salesOrderPartLineItem);
  const pendingSORQtyDetail = SalesOrderPartLineItemReader.pendingSORQtyDetail(salesOrderPartLineItem);
  if (_isEmpty(sorParts)) {
    return pendingSORQtyDetail || EMPTY_OBJECT;
  }

  const anySorWithNoPo = getNonDraftSors(sorParts);

  if (!_isEmpty(anySorWithNoPo)) {
    return {
      qty: subtract(anySorWithNoPo.sorQty, anySorWithNoPo.unlinkedQty),
      priorityCodeId: anySorWithNoPo.priorityCode,
      activeSorItem: anySorWithNoPo,
      sorId: anySorWithNoPo.sorId,
      id: anySorWithNoPo.id,
    };
  }

  // soPartStatus will be DRAFT, If there is any Draft PO for that SOR
  const anySorWithDraftedPo = _find(
    sorParts,
    item =>
      SalesOrderSorPartLineReader.soPartStatus(item) === SOR_STATUS.DRAFT &&
      !SalesOrderSorPartLineReader.sourceDeleted(item),
  );

  if (!_isEmpty(anySorWithDraftedPo)) {
    return {
      qty: subtract(anySorWithDraftedPo.sorQty, anySorWithDraftedPo.unlinkedQty),
      priorityCodeId: anySorWithDraftedPo.priorityCode,
      activeSorItem: anySorWithDraftedPo,
      sorId: anySorWithDraftedPo.sorId,
      id: anySorWithDraftedPo.id,
    };
  }

  return EMPTY_OBJECT;
};

export const childTypeVsIcon = chargeType => {
  switch (chargeType) {
    case CHILD_TYPES.CORE: {
      return 'icon-copyright';
    }
    case CHILD_TYPES.CORE_RETURN: {
      return 'icon-arrow-left-circled';
    }
    case CHILD_TYPES.COUPON: {
      return 'icon-coupon';
    }
    case CHILD_TYPES.FEE: {
      return 'icon-monetary';
    }
    case CHILD_TYPES.STOCK: {
      return 'icon-monetary';
    }
    case CUSTOM_ROW_TYPES.RETURN_REASON: {
      return 'icon-comment';
    }
    case CUSTOM_ROW_TYPES.COMMENT: {
      return 'icon-comment';
    }
    default: {
      return 'icon-copyright';
    }
  }
};

export const getReCalculateFeesPayload = (partList, frozenSalesOrderEntity) => {
  const arr = [];

  // TODO shivam change forEach to reduce
  _forEach(partList, item => {
    const appliedFeeCodes = _reduce(
      SalesOrderPartLineItemReader.charges(item),
      (acc, child) => {
        if (
          SalesOrderPartLineChargeDomain.isFeeCharge(child) &&
          (!SalesOrderPartLineChargeReader.overridden(child) || SalesOrderPartLineChargeReader.costOverridable(child))
        ) {
          const feeCode = SalesOrderPartLineChargeReader.feeCode(child);
          const overriddenCostAmount = _get(child, 'costPrice');
          return _isNil(feeCode)
            ? acc
            : [
                ...acc,
                {
                  feeCode,
                  ...(!_isNil(overriddenCostAmount) && {
                    overriddenCostAmount: tMoneyUtils.toInt(overriddenCostAmount),
                  }),
                },
              ];
        }
        return acc;
      },
      [],
    );

    const partsAmount = tMoneyUtils.toInt(SalesOrderPartLineItemReader.sellingPrice(item));
    if (!_isEmpty(appliedFeeCodes)) {
      arr.push({
        requestId: SalesOrderPartLineItemReader.lineId(item),
        partsAmount,
        appliedFeeCodesV2: appliedFeeCodes,
        ...getFeePayerPayloadWithServiceV3Context({
          frozenSalesOrderEntity,
          partsAmount,
        }),
      });
    }
  });

  return arr;
};

const CHILD_SELLING_PRICE_BY_CHILD_TYPE = {
  [CHARGE_TYPES.FEE]: ({ feeMap, child, lineId } = EMPTY_OBJECT) => {
    const feeCode = SalesOrderPartLineChargeReader.feeCode(child);
    const feeAmount = _get(feeMap, `${lineId}.calculatedAmount.${feeCode}`);
    return feeAmount || feeAmount === 0 ? tMoneyUtils.withPrecision(feeAmount) : '';
  },

  [CHARGE_TYPES.COUPON]: ({ couponMap, child, lineId } = EMPTY_OBJECT) => {
    const couponId = SalesOrderPartLineChargeReader.couponId(child);
    const couponPrice = _get(couponMap, `${lineId}.${couponId}.amount`);

    return couponPrice || couponPrice === 0 ? tMoneyUtils.withPrecision(couponPrice) : '';
  },
};

export const getPartsWithUpdatedFeesAndCoupons = (
  parts,
  feesByPartIds,
  couponPriceByPartRequestIds,
  extra = EMPTY_OBJECT,
) => {
  // mapping over each of the part
  const extraToSet = _isObject(extra) ? extra : EMPTY_OBJECT;
  const updatedParts = _map(parts, part => {
    const { children } = part;
    const partId = SalesOrderPartLineItemReader.partId(part);
    const lineId = SalesOrderPartLineItemReader.lineId(part);
    let updatedChildren = [];

    // if child is not empty, update the fee child
    if (!_isEmpty(children)) {
      updatedChildren = produce(children, childrenDraft =>
        _reduce(
          childrenDraft,
          (acc, child) => {
            const { overridden, costOverridable } = child;
            // we need to recalculate fee price even if the selling price is overridden
            if (!costOverridable && overridden) {
              acc.push(child);
              return acc;
            }
            let updatedChild = { ...child };
            let childSellingPriceHandler = CHILD_SELLING_PRICE_BY_CHILD_TYPE[child.type];

            childSellingPriceHandler = childSellingPriceHandler || _noop; // safety only
            const childSellingPrice = childSellingPriceHandler({
              partId,
              lineId,
              child: updatedChild,
              feeMap: feesByPartIds,
              couponMap: couponPriceByPartRequestIds,
              part,
              ...extraToSet,
            });

            if (childSellingPrice || childSellingPrice === 0) {
              updatedChild = {
                ...updatedChild,
                amount: childSellingPrice,
              };
            }

            acc.push(updatedChild);
            return acc;
          },
          [],
        ),
      );
    }
    // return the updated part
    return {
      ...part,
      children: updatedChildren,
    };
  });

  return updatedParts;
};

export const getCouponPriceForPartPayload = partList => {
  const arr = [];

  _forEach(partList, item => {
    const couponIds = _reduce(
      SalesOrderPartLineItemReader.charges(item),
      (acc, child) => {
        if (SalesOrderPartLineChargeDomain.isCouponCharge(child)) {
          const couponId = SalesOrderPartLineChargeReader.couponId(child);
          return _isNil(couponId) ? acc : [...acc, couponId];
        }
        return acc;
      },
      [],
    );

    if (couponIds.length) {
      arr.push({
        partId: SalesOrderPartLineItemReader.partId(item),
        lineItemId: SalesOrderPartLineItemReader.lineId(item),
        totalPartsAmountInCents: tMoneyUtils.toInt(SalesOrderPartLineItemReader.sellingPrice(item)),
        couponIds,
      });
    }
  });
  return arr;
};

export const priceMismatchValidator = ({ partLine }) => {
  const trackedSellingPrice = _get(partLine, 'trackedSellingPrice');
  const sellingPrice = SalesOrderPartLineItemReader.sellingPrice(partLine);
  const res = !_isEqual(sellingPrice, trackedSellingPrice);

  return res;
};

export const getMinimumSellingPriceForPart = ({ partLine, priceCodeDetails, partsSettingsMap, currentSiteId }) => {
  const minimumGrossProfitPercentageRequired = getMinimumGrossProfitPercentageRequired({
    partsSettingsMap,
    currentSiteId,
    priceCodeDetails,
  });
  const costPrice = SalesOrderPartLineItemReader.costPrice(partLine);
  const minSellingPrice = tMoneyUtils.divide(
    multiply(tMoneyUtils.toInt(costPrice), 100),
    subtract(1, divide(minimumGrossProfitPercentageRequired, 100)),
  );
  const minSellingPriceCeil = _ceil(minSellingPrice);
  const minSellingPriceHigherPrecision = getInHigherPrecisionCents({ amount: minSellingPriceCeil });
  const taxRoundingMethod = getTaxRoundingMethod();
  const roundedOfSellingPrice = getAmountInCurrency({
    amount: minSellingPriceHigherPrecision,
    taxRoundingMethod,
  });

  return {
    minimumSellingPrice: roundedOfSellingPrice,
    minimumGrossProfitPercentageRequired,
  };
};

export const getCanReissueSO = ({ isReissuing, salesOrderEntity }) =>
  hasEditPermissionsSalesOrder() &&
  !getIsSOInEditableState({ salesOrderEntity, isReissuing }) &&
  SalesOrderDomain.canReissue(salesOrderEntity);

export const isPartItemLine = itemLine => _get(itemLine, 'lineType') === SALES_ORDER_ITEM_LINE_TYPE.PART_LINE;

export const isFeeItemLine = itemLine => _get(itemLine, 'lineType') === SALES_ORDER_ITEM_LINE_TYPE.FEE_LINE;

export const isCouponItemLine = itemLine => _get(itemLine, 'lineType') === SALES_ORDER_ITEM_LINE_TYPE.COUPON_LINE;

export const isCouponReturnItemLine = itemLine =>
  _get(itemLine, 'lineType') === SALES_ORDER_ITEM_LINE_TYPE.COUPON_RETURN_LINE;

export const isFeeReturnItemLine = itemLine =>
  _get(itemLine, 'lineType') === SALES_ORDER_ITEM_LINE_TYPE.FEE_RETURN_LINE;

export const isPartReturnItemLine = itemLine =>
  _get(itemLine, 'lineType') === SALES_ORDER_ITEM_LINE_TYPE.PART_RETURN_LINE;

export const isCoreReturnItemLine = itemLine =>
  _get(itemLine, 'lineType') === SALES_ORDER_ITEM_LINE_TYPE.CORE_RETURN_LINE;

export const isReferencedCoreReturnItemLine = itemLine =>
  _get(itemLine, 'lineType') === SALES_ORDER_ITEM_LINE_TYPE.REFERENCED_CORE_RETURN;

export const getListByItemType = itemList => {
  const { feeList, partList, partReturnList, feeReturnList, referencedCoreReturnList, couponList } = _reduce(
    itemList,
    (acc, rowDetails) => {
      if (isFeeItemLine(rowDetails)) {
        return {
          ...acc,
          feeList: [...acc.feeList, rowDetails],
        };
      }

      if (isPartReturnItemLine(rowDetails)) {
        return {
          ...acc,
          partReturnList: [...acc.partReturnList, rowDetails],
        };
      }

      if (isFeeReturnItemLine(rowDetails)) {
        return {
          ...acc,
          feeReturnList: [...acc.feeReturnList, rowDetails],
        };
      }

      if (isReferencedCoreReturnItemLine(rowDetails)) {
        return {
          ...acc,
          referencedCoreReturnList: [...acc.referencedCoreReturnList, rowDetails],
        };
      }

      if (isCouponItemLine(rowDetails)) {
        return {
          ...acc,
          couponList: [...acc.couponList, rowDetails],
        };
      }

      return {
        ...acc,
        partList: [...acc.partList, rowDetails],
      };
    },
    {
      feeList: EMPTY_ARRAY,
      partList: EMPTY_ARRAY,
      partReturnList: EMPTY_ARRAY,
      feeReturnList: EMPTY_ARRAY,
      referencedCoreReturnList: EMPTY_ARRAY,
      couponList: EMPTY_ARRAY,
    },
  );
  return {
    feeList,
    partList,
    partReturnList,
    feeReturnList,
    referencedCoreReturnList,
    couponList,
  };
};

export const setRowValueOnPriceCodeChange = ({ currentRow, updatedRow } = EMPTY_OBJECT) => {
  const currentPricingType = SalesOrderPartLineItemReader.currentPricingType(currentRow);
  if (currentPricingType === PRICING_TYPES.PART_KIT_FIXED) {
    return currentRow;
  }
  return {
    ...updatedRow,
    currentPricingType: PRICING_TYPES.COMPUTED,
  };
};

export function getDefaultPricingForInternalType(internalSaleType, defaultPricing) {
  const accessorForDefaultPricing = INTERNAL_ACCOUNTS_VS_PRICING_MAP[internalSaleType] || _noop;
  return accessorForDefaultPricing({ defaultPricing });
}

const getFormattedPartLineForSorCancellationDrawer = (partLine, additional) => {
  const { cancelledQtyForSorPart, sorPart } = additional;
  const defaultSORUnlinkType =
    SalesOrderSorPartLineReader.soPartStatus(sorPart) === STATUS.ORDERED
      ? UNLINK_ACTION_TYPES.INVENTORY
      : UNLINK_ACTION_TYPES.VOID;
  const unlinkType = SalesOrderSorPartLineReader.unlinkedType(sorPart) || defaultSORUnlinkType;
  const partSaleConversionConfig = SalesOrderPartLineItemReader.partSaleConversionConfig(partLine);
  const unitOfMeasureId = SalesOrderPartLineItemReader.unitOfMeasureId(partLine);
  const unitOfMeasureShortForm = _get(partLine, 'unitOfMeasureShortForm', null);
  const isFractionalSaleEnabledForThePart = _get(partLine, 'isFractionalSaleEnabledForThePart', false);
  return {
    [SOR_CANCELLATION_PART_LIST_COLUMN_KEYS.PART]: formatPartName(partLine),
    [SOR_CANCELLATION_PART_LIST_COLUMN_KEYS.UNLINK_QTY]: add(
      cancelledQtyForSorPart,
      subtract(
        SalesOrderSorPartLineReader.unlinkedQty(sorPart),
        SalesOrderSorPartLineReader.unlinkedCancelledQty(sorPart),
      ),
    ),
    [SOR_CANCELLATION_PART_LIST_COLUMN_KEYS.SOR_QTY]: SalesOrderSorPartLineReader.sorQty(sorPart),
    [SOR_CANCELLATION_PART_LIST_COLUMN_KEYS.SOR_NUMBER]: SalesOrderSorPartLineReader.sorNumber(sorPart),
    [SOR_CANCELLATION_PART_LIST_COLUMN_KEYS.MOVE_TO_INVENTORY]: unlinkType === UNLINK_ACTION_TYPES.INVENTORY,
    [SOR_CANCELLATION_PART_LIST_COLUMN_KEYS.VOID_PART]: unlinkType === UNLINK_ACTION_TYPES.VOID,
    [SOR_CANCELLATION_PART_LIST_COLUMN_KEYS.RESERVE_ON_SOR_HOLD]: unlinkType === UNLINK_ACTION_TYPES.SOR_HOLD,
    [SOR_CANCELLATION_PART_LIST_COLUMN_KEYS.RECEIVED_QTY]: subtract(
      SalesOrderSorPartLineReader.receivedQty(sorPart),
      SalesOrderSorPartLineReader.cancelledQuantity(sorPart),
    ),
    action: unlinkType,
    status: SalesOrderSorPartLineReader.soPartStatus(sorPart) || STATUS.REQUESTED,
    sorPartLineId: SalesOrderSorPartLineReader.id(sorPart),
    lineId: SalesOrderPartLineItemReader.lineId(partLine),
    unlinkedReceivedQty: SalesOrderSorPartLineReader.unlinkedReceivedQty(sorPart),
    cancellationId: SalesOrderSorPartLineReader.cancellationId(sorPart),
    partSaleConversionConfig,
    unitOfMeasureId,
    unitOfMeasureShortForm,
    isFractionalSaleEnabledForThePart,
  };
};

export const getPartsListApplicableForSORCancellationOnReissue = (frozenSalesOrderEntity, newPartList) => {
  const oldPartList = SalesOrderReader.partSaleDetails(frozenSalesOrderEntity);
  const oldPartListByIds = _keyBy(oldPartList, 'id');

  const partListApplicableForSORCancellation = _reduce(
    newPartList,
    (acc, partRow) => {
      const partLineId = SalesOrderPartLineItemReader.id(partRow);
      const oldPartLineData = _get(oldPartListByIds, partLineId);

      if (_isEmpty(oldPartLineData)) {
        return acc;
      }

      const holdQty = SalesOrderPartLineItemReader.holdQty(oldPartLineData);
      const reservedQty = SalesOrderPartLineItemReader.reservedQuantity(oldPartLineData);

      const oldRequiredQty = SalesOrderPartLineItemDomain.getOldRequiredQtyForSORCancellation(oldPartLineData);
      const newRequiredQty = SalesOrderPartLineItemReader.requiredQty(partRow);

      // we first remove holdQty and reservedQty of a part line before considering sorQty reduction in BE
      const quantityToBeUnlinked = subtract(oldRequiredQty, sum([holdQty, reservedQty, newRequiredQty]));

      if (quantityToBeUnlinked < 0) {
        return acc;
      }

      if (SalesOrderPartLineItemReader.sorPartExist(oldPartLineData)) {
        const orderedSorParts = SalesOrderPartLineItemDomain.getOrderedSORPartsForUnlink(oldPartLineData);
        let totalCancelledQty = quantityToBeUnlinked;
        _forEach(orderedSorParts, sorPart => {
          const totalCancellableQty = SalesOrderSORPartLineDomain.getTotalCancellableQtyForSORCancellation(sorPart);
          const cancelledQtyForSorPart = _min([totalCancelledQty, totalCancellableQty]);

          if (cancelledQtyForSorPart <= 0) return;

          // if sor part is in requested or draft state and partial cancellation (totalCancellableQty - totalCancelledQty > 0) happens
          if (
            SalesOrderSORPartLineDomain.isSORPartEligibleForAutomatedUnlinking(sorPart) &&
            subtract(totalCancellableQty, totalCancelledQty) > 0
          ) {
            // sor qty will automatically get reduced, we don't need to send any info. we keep exhausting the cancellable qty sor part line by sor part line
            totalCancelledQty -= cancelledQtyForSorPart;
            return;
          }

          if (SalesOrderSORPartLineDomain.isSORPartEligibleForUnlinking(sorPart)) {
            acc.push(
              getFormattedPartLineForSorCancellationDrawer(oldPartLineData, {
                cancelledQtyForSorPart,
                sorPart,
              }),
            );
            // we keep exhausting the cancellable qty sor part line by sor part line
            totalCancelledQty -= cancelledQtyForSorPart;
          }
        });
      }
      return acc;
    },
    [],
  );
  return partListApplicableForSORCancellation;
};

export const getPartsListApplicableForSORCancellationOnVoid = frozenSalesOrderEntity => {
  const partList = SalesOrderReader.partSaleDetails(frozenSalesOrderEntity);
  const partListApplicableForSORCancellation = _reduce(
    partList,
    (acc, partRow) => {
      if (SalesOrderPartLineItemReader.sorPartExist(partRow)) {
        const sorParts = SalesOrderPartLineItemReader.sorParts(partRow);
        _forEach(sorParts, sorPart => {
          if (SalesOrderSORPartLineDomain.isSORPartEligibleForUnlinking(sorPart)) {
            const totalCancellableQty = SalesOrderSORPartLineDomain.getTotalCancellableQtyForSORCancellation(sorPart);

            if (totalCancellableQty > 0) {
              acc.push(
                getFormattedPartLineForSorCancellationDrawer(partRow, {
                  cancelledQtyForSorPart: totalCancellableQty,
                  sorPart,
                }),
              );
            }
          }
        });
      }
      return acc;
    },
    [],
  );
  return partListApplicableForSORCancellation;
};

const getFormattedPartLineForLostSaleDrawer = ({ partRowDetails, resolvedInfoByPartIdentifierId }) => {
  const partId = SalesOrderPartLineItemReader.partId(partRowDetails);
  const warehouseId = SalesOrderPartLineItemReader.warehouseId(partRowDetails);
  const partInventoryDetails = getResolvedPartDetails({ partId, warehouseId, resolvedInfoByPartIdentifierId });
  const partMasterDetails = _get(partInventoryDetails, 'part');
  const onHandQuantityAccordingToSelectedBinId = getOnHandQuantityAccordingToSelectedBinId({
    rowData: partRowDetails,
    resolvedPartDetails: partMasterDetails,
  });
  const requiredQty = SalesOrderPartLineItemReader.requiredQty(partRowDetails);

  const formattedPartName = formatPartNumber(
    SalesOrderPartLineItemDomain.isTemporaryPart(partRowDetails)
      ? SalesOrderPartLineItemReader.adhocPartInfo(partRowDetails)
      : partMasterDetails || partRowDetails,
    { defaultValue: formatPartNumber(partRowDetails, { defaultValue: partId }) },
  );
  const resolvedPartDescription = formatPartDescription(
    SalesOrderPartLineItemDomain.isTemporaryPart(partRowDetails)
      ? SalesOrderPartLineItemReader.adhocPartInfo(partRowDetails)
      : partMasterDetails || partRowDetails,
  );

  const partSaleConversionConfig = SalesOrderPartLineItemReader.partSaleConversionConfig(partRowDetails);
  const precisionForFractionalSale = SalesOrderPartLineItemReader.precision(partSaleConversionConfig);
  const unitOfMeasureShortForm = _get(partRowDetails, 'unitOfMeasureShortForm');
  const isFractionalSaleEnabled = _get(partRowDetails, 'isFractionalSaleEnabledForThePart', false);

  return {
    id: SalesOrderPartLineItemReader.id(partRowDetails),
    [LOST_SALE_PART_LIST_COLUMN_IDS.PART]: formattedPartName,
    [LOST_SALE_PART_LIST_COLUMN_IDS.PART]: formattedPartName,
    [LOST_SALE_PART_LIST_COLUMN_IDS.PART_DESCRIPTION]: resolvedPartDescription,
    [LOST_SALE_PART_LIST_COLUMN_IDS.SALE_QTY]: requiredQty,
    [LOST_SALE_PART_LIST_COLUMN_IDS.ON_HAND_QTY]: onHandQuantityAccordingToSelectedBinId,
    [LOST_SALE_PART_LIST_COLUMN_IDS.LOST_SALE_QTY]:
      requiredQty > onHandQuantityAccordingToSelectedBinId ? requiredQty : 0,
    [LOST_SALE_PART_LIST_COLUMN_IDS.LOST_SALE_REASON]:
      requiredQty > onHandQuantityAccordingToSelectedBinId ? LOST_SALE_REASONS.INSUFFICIENT_OH_QTY : null,
    [LOST_SALE_PART_LIST_COLUMN_IDS.MAX_LOST_SALE_QTY]: requiredQty,
    [LOST_SALE_PART_LIST_COLUMN_IDS.WAREHOUSE]: warehouseId,
    partId,
    unitOfMeasureShortForm,
    isFractionalSaleEnabled,
    partSaleConversionConfig,
    precisionForFractionalSale,
    costPrice: SalesOrderPartLineItemReader.costPrice(partRowDetails),
  };
};

export const getPartsListApplicableForLostSale = ({
  partList,
  resolvedInfoByPartIdentifierId,
  resolvedSourceCodeById,
  frozenSalesOrderEntity,
}) => {
  const updatedPartListApplicableForLostSale = _reduce(
    partList,
    (acc, partRow) => {
      const lostSale = SalesOrderPartLineItemReader.lostSale(partRow);
      const sorPartExist = SalesOrderPartLineItemReader.sorPartExist(partRow);
      const shouldPartConsiderForLostSale = isPartLineEligibleForLostSaleTracking({
        partRowDetails: partRow,
        resolvedSourceCodeById,
        frozenSalesOrderEntity,
      });
      if (lostSale || sorPartExist || !shouldPartConsiderForLostSale) {
        return acc;
      }
      acc.push(getFormattedPartLineForLostSaleDrawer({ partRowDetails: partRow, resolvedInfoByPartIdentifierId }));
      return acc;
    },
    [],
  );
  return updatedPartListApplicableForLostSale;
};

export const getExchangePartsListApplicableForSORCancellationOnReturnVoid = partReturn => {
  const exchangePartList = SalesOrderReturnReader.exchangedPartLines(partReturn);
  const partListApplicableForSORCancellation = _reduce(
    exchangePartList,
    (acc, partRow) => {
      if (SalesOrderPartLineItemReader.sorPartExist(partRow)) {
        const sorParts = SalesOrderPartLineItemReader.sorParts(partRow);
        _forEach(sorParts, sorPart => {
          if (SalesOrderSORPartLineDomain.isSORPartEligibleForUnlinking(sorPart)) {
            const totalCancellableQty = SalesOrderSORPartLineDomain.getTotalCancellableQtyForSORCancellation(sorPart);

            if (totalCancellableQty > 0) {
              acc.push(
                getFormattedPartLineForSorCancellationDrawer(partRow, {
                  cancelledQtyForSorPart: totalCancellableQty,
                  sorPart,
                }),
              );
            }
          }
        });
      }
      return acc;
    },
    [],
  );
  return partListApplicableForSORCancellation;
};

export const getUnlinkRequestDetails = sorCancellationModalSubmittedValues => {
  const sorPartsForCancellation = _get(sorCancellationModalSubmittedValues, 'sorParts', []);
  return _map(sorPartsForCancellation, sorPart => {
    const sorPartLineId = _get(sorPart, 'sorPartLineId');
    const unlinkedType = _get(sorPart, 'action');
    const cancellationId = _get(sorPart, 'cancellationId');
    return { sorPartLineId, unlinkedType, cancellationId };
  });
};

export const getFormattedPartForManualUnlink = ({
  sorPart,
  resolvedInfoByPartIdentifierId,
  frozenSalesOrderEntity,
}) => {
  const partItemList = SalesOrderReader.partSaleDetails(frozenSalesOrderEntity);
  const refPartLineId = SalesOrderPOLineItemReader.refPartLineId(sorPart);
  const refPartDetails = _find(partItemList, refPart => SalesOrderPartLineItemReader.id(refPart) === refPartLineId);
  const sorQty = SalesOrderPOLineItemReader.sorQuantity(sorPart);
  const receivedQty = SalesOrderPOLineItemReader.receivedQuantity(sorPart);
  const cancelledQuantity = SalesOrderPOLineItemReader.cancelledQuantity(sorPart);
  const defaultSORUnlinkType =
    SalesOrderPOLineItemReader.status(sorPart) === STATUS.ORDERED
      ? UNLINK_ACTION_TYPES.INVENTORY
      : UNLINK_ACTION_TYPES.VOID;
  const unlinkedType = SalesOrderPOLineItemReader.unlinkedType(sorPart) || defaultSORUnlinkType;
  const unlinkedReceivedQty = SalesOrderPOLineItemReader.unlinkedReceivedQty(sorPart);
  const sorPartStatus = SalesOrderPOLineItemReader.status(sorPart) || STATUS.REQUESTED;
  const sorPartLineId = SalesOrderPOLineItemReader.id(sorPart);
  const sorNumber = SalesOrderPOLineItemReader.sorNumber(sorPart);
  const partId = SalesOrderPOLineItemReader.partId(sorPart);
  const warehouseId =
    SalesOrderPOLineItemReader.warehouseId(sorPart) || SalesOrderPartLineItemReader.warehouseId(refPartDetails);

  const partInventoryDetails = getResolvedPartDetails({ partId, warehouseId, resolvedInfoByPartIdentifierId });

  const partMasterDetails = _get(partInventoryDetails, 'part', EMPTY_OBJECT);
  const resolvedPartName = formatPartName(
    SalesOrderPartLineItemDomain.isTemporaryPart(refPartDetails)
      ? SalesOrderPartLineItemReader.adhocPartInfo(refPartDetails)
      : partMasterDetails || refPartDetails,
    { defaultValue: formatPartName(refPartDetails, { defaultValue: partId }) },
  );

  const formattedPartData = {
    [SOR_CANCELLATION_PART_LIST_COLUMN_KEYS.PART]: resolvedPartName,
    [SOR_CANCELLATION_PART_LIST_COLUMN_KEYS.UNLINK_QTY]: subtract(sorQty, sum([receivedQty, cancelledQuantity])) ?? 0,
    [SOR_CANCELLATION_PART_LIST_COLUMN_KEYS.SOR_QTY]: sorQty,
    [SOR_CANCELLATION_PART_LIST_COLUMN_KEYS.SOR_NUMBER]: sorNumber,
    [SOR_CANCELLATION_PART_LIST_COLUMN_KEYS.MOVE_TO_INVENTORY]: unlinkedType === UNLINK_ACTION_TYPES.INVENTORY,
    [SOR_CANCELLATION_PART_LIST_COLUMN_KEYS.VOID_PART]: unlinkedType === UNLINK_ACTION_TYPES.VOID,
    [SOR_CANCELLATION_PART_LIST_COLUMN_KEYS.RESERVE_ON_SOR_HOLD]: unlinkedType === UNLINK_ACTION_TYPES.SOR_HOLD,
    [SOR_CANCELLATION_PART_LIST_COLUMN_KEYS.RECEIVED_QTY]: add(receivedQty, unlinkedReceivedQty),
    action: unlinkedType,
    status: sorPartStatus,
    sorPartLineId,
    unlinkedReceivedQty,
    cancellationId: SalesOrderSorPartLineReader.cancellationId(sorPart),
  };
  return formattedPartData;
};

export const shouldShowGrossProfitAlert = (partsSettingsMap, currentSiteId) => {
  const partsSettings = partsSettingsMap.get(currentSiteId) || partsEnv.partsSettings;
  return GeneralSettingsDomain.shouldShowConfirmationModalForGrossProfit(partsSettings);
};

export const isSOLevelInvoiceVariant = invoiceVariant => invoiceVariant === SALES_ORDER_INVOICE_VARIANT.INVOICE;

export const blurAndDelay = async (delay = DELAY_AFTER_BLUR) => {
  try {
    document.activeElement.blur();
  } catch (error) {
    console.warn(error);
  }
  await new Promise(resolve => _delay(resolve, delay));
};

export const getPriceCodeLabel = priceCode => {
  const { code, description: defaultDescription } = priceCode || EMPTY_OBJECT;
  const priceCodeLabel = getLocaleValue({ entity: priceCode, entityKeyReader: priceCodeReader.description });
  return _join(_compact([code, priceCodeLabel || defaultDescription]), ' | ');
};

export const getRegulationBasedDisplayMessage = regulationType => {
  if (regulationType) {
    return REGULATION_VS_DISPLAY_MESSAGE[regulationType];
  }
  return REGULATION_VS_DISPLAY_MESSAGE[REGULATION_TYPES.DEFAULT];
};

export const getAddressVerificationPayload = ({ id, address }) => {
  const { line1, line2, line3, line4, postalCode, city, country, state } = address || EMPTY_OBJECT;
  if (!line1) {
    return null;
  }
  return {
    id,
    line1,
    line2,
    line3,
    line4,
    postalCode,
    city,
    country,
    state,
  };
};

export const getCustomerInfoSetByCRMLeads = () => {
  const storageKey = makeLocalStorageKey(FIELD_IMPORT_SOURCE_TYPE_KEYS.CRM_LEADS);
  const customerInfo = getValueFromApplicationLocalStorage(storageKey);
  setValueInApplicationLocalStorage(storageKey, EMPTY_OBJECT);
  return customerInfo;
};

export const getUpdatedTaxDetailsOnSaleTypeChange = ({
  defaultPartSaleTaxDetails,
  taxCodeSetupConfigList,
  customerTaxOverrides,
  saleType,
  partSaleTaxDetails = EMPTY_OBJECT,
  isNewTaxCodeSetupEnabled,
}) => {
  if (isNewTaxCodeSetupEnabled) {
    return getDocumentDefaultTaxCodeMappings({ taxCodeSetupConfigList, customerTaxOverrides, saleType });
  }
  const { taxable: isOrderTaxableByDefault } = defaultPartSaleTaxDetails || partSaleTaxDetails;
  // If sale type is internal then default taxable to false, else to customer/dealer taxable value
  const updatedPartSaleTaxDetails = {
    ...partSaleTaxDetails,
    taxable: saleType === SALE_TYPES.INTERNAL ? false : isOrderTaxableByDefault,
  };
  return updatedPartSaleTaxDetails;
};

export const getCustomerCreditInfoPayload = customerId => ({
  customerIds: [customerId],
  snapshotRequest: {
    month: getMonth() + 1,
    year: getCurrentYearValue(),
  },
});

export const isPartLineEligibleForLostSaleTracking = ({
  partRowDetails,
  resolvedSourceCodeById,
  frozenSalesOrderEntity,
}) => {
  const isPartLostSale = SalesOrderPartLineItemReader.lostSale(partRowDetails);
  const isExchangeLine = _get(partRowDetails, 'isExchangePartLine', false);
  const unusualSale = SalesOrderPartLineItemReader.unusualSale(partRowDetails);
  const isTemporaryPart = SalesOrderPartLineItemReader.isTemporaryPart(partRowDetails);
  const negativeSaleQty = SalesOrderPartLineItemReader.negativeSaleQty(partRowDetails) || 0;
  const isExchangeOnSOLevel = SalesOrderDomain.isExchangeOnSOLevel(frozenSalesOrderEntity);
  const isExchangePartLine = isExchangeOnSOLevel && _get(partRowDetails, 'isLineItemEditable', true);
  const isNegativeOHSale = negativeSaleQty > 0;
  if (
    isPartLostSale ||
    !isPartItemLine(partRowDetails) ||
    isExchangeLine ||
    isExchangePartLine ||
    unusualSale ||
    isTemporaryPart ||
    isNegativeOHSale
  ) {
    return false;
  }
  const sourceCodeId = SalesOrderPartLineItemReader.sourceCodeId(partRowDetails);
  const lostSaleTrackingDisabledForSourceCode = _get(
    resolvedSourceCodeById,
    `${sourceCodeId}.lostSaleTrackingDisabled`,
    false,
  );
  return !lostSaleTrackingDisabledForSourceCode;
};

export const getIsPartEligibleToTrackLostSaleOnRemoval = ({ partRowDetails, frozenSalesOrderEntity }) => {
  if (_isEmpty(frozenSalesOrderEntity)) {
    return true;
  }
  const partLineId = SalesOrderPartLineItemReader.id(partRowDetails);
  const partSaleDetails = SalesOrderReader.partSaleDetails(frozenSalesOrderEntity);
  const isPartSavedInBE = !_isNil(_find(partSaleDetails, part => SalesOrderPartLineItemReader.id(part) === partLineId));
  return isPartSavedInBE;
};

export const getFeePayerPayloadWithServiceV3Context = ({ frozenSalesOrderEntity, partsAmount }) => {
  if (!isROV3ServiceEnabled() || !isPartsSideDynamicSplitEnabled()) return EMPTY_ARRAY;

  const createdTime = SalesOrderReader.createdTime(frozenSalesOrderEntity);
  const salesOrderCustomer = SalesOrderReader.customer(frozenSalesOrderEntity);
  const customerId = SalesOrderCustomerReader.id(salesOrderCustomer);

  return {
    partsPayerAmount: [
      {
        payerId: customerId,
        partsAmount,
      },
    ],
    assetType: ENTITY_REFERENCE_TYPES.SALES_ORDER,
    assetCreationTime: createdTime,
  };
};

export const getFeePayerPayloadWithServiceV3ContextActionV2 = ({
  frozenSalesOrderEntity,
  resolvedCustomerDetails,
  partsAmount,
  overriddenCostAmount = null,
}) => {
  if (!isROV3ServiceEnabled() || !isPartsSideDynamicSplitEnabled()) return EMPTY_ARRAY;

  const createdTime = SalesOrderReader.createdTime(frozenSalesOrderEntity) || new Date().getTime();
  const salesOrderCustomer = SalesOrderReader.customer(frozenSalesOrderEntity);
  const customerId =
    SalesOrderCustomerReader.id(salesOrderCustomer) || SalesOrderCustomerReader.id(resolvedCustomerDetails); // salesOrderCustomer will be empty when new SO is created

  return {
    payerAmounts: [
      {
        payerId: customerId,
        partsAmount,
        overriddenCostAmount,
      },
    ],
    assetType: ENTITY_REFERENCE_TYPES.SALES_ORDER,
    assetCreationTime: createdTime,
  };
};

export const getFormattedCalculatedFeeResponse = ({ calculatedFeeResponse }) => {
  if (isROV3ServiceEnabled() && isPartsSideDynamicSplitEnabled()) return _keyBy(calculatedFeeResponse, 'requestId');

  return calculatedFeeResponse;
};

export const getFormattedCalculatedFeeResponseV2 = ({ calculatedFeeResponse, partId }) => {
  if (isROV3ServiceEnabled() && isPartsSideDynamicSplitEnabled()) return { [partId]: calculatedFeeResponse };

  return calculatedFeeResponse;
};
export const isDuplicateSOLevelFee = (itemList, selectedFeeDetails, shouldPreserveItemData, existingSOFeeColumnData) =>
  _some(
    itemList,
    item =>
      SalesOrderPartLineItemReader.lineType(item) === SALES_ORDER_ITEM_LINE_TYPE.FEE_LINE &&
      SalesOrderFeeLineItemReader.feeCode(item) === FeeReader.feeCode(selectedFeeDetails) &&
      (shouldPreserveItemData
        ? SalesOrderFeeLineItemReader.id(item) !== SalesOrderFeeLineItemReader.id(existingSOFeeColumnData)
        : true),
  );

export const getCouponCodeFromResolvedCouponList = (rowData, resolvedPartsCouponList) => {
  const selectedCouponDetails = _find(resolvedPartsCouponList, { id: CouponReader.couponId(rowData) });
  return CouponReader.couponCode(selectedCouponDetails);
};

/**
 * @param number - number that needs to be truncated to given precision
 * @param precision - precision to slice to
 * @returns sliced number
 */
export const sliceToPrecision = ({ number, precision }) => {
  const multiplier = 10 ** precision;
  const truncatedNum = _floor(number * multiplier);
  return truncatedNum / multiplier;
};

export const getIsSaleTypeDisabledForCustomer = ({ customerOption }) => {
  const customerSaleType = _get(customerOption, 'setup.part.defaultSaleType');
  const ifSalesTypeAttachedToCustomer = !!customerSaleType;
  const { accountingInfo = EMPTY_OBJECT } = customerOption || EMPTY_OBJECT;
  const { interCompanyTxnEnabled } = accountingInfo || EMPTY_OBJECT;
  const shouldShowInterCompanyTxnWarningIcon = interCompanyTxnEnabled && !ifSalesTypeAttachedToCustomer;
  if (interCompanyTxnEnabled && ifSalesTypeAttachedToCustomer)
    return {
      isSaleTypeDisabledForCustomer: true,
      shouldShowInterCompanyTxnWarningIcon,
    };
  return {
    isSaleTypeDisabledForCustomer: false,
    shouldShowInterCompanyTxnWarningIcon,
  };
};
