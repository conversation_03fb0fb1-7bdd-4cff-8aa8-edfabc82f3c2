import React, { useCallback, useEffect, useMemo, useRef } from 'react';

import { connect } from 'react-redux';
import { useNavigate } from 'react-router-dom';

import _find from 'lodash/find';
import _get from 'lodash/get';
import _head from 'lodash/head';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';
import _noop from 'lodash/noop';
import _set from 'lodash/set';
import _size from 'lodash/size';
import _some from 'lodash/some';
import _toNumber from 'lodash/toNumber';
import _toString from 'lodash/toString';
import _values from 'lodash/values';

import cx from 'classnames';
import { Map as IMap } from 'immutable';
import PropTypes from 'prop-types';
import { compose } from 'recompose';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { SALES_ORDER_MODULES, PARTS_NOTES_SOURCE_TYPES } from '@tekion/tekion-base/constants/parts/general';
import PO_TYPE from '@tekion/tekion-base/constants/purchaseOrderTypes';
import { getErrorCode } from '@tekion/tekion-base/utils/errorUtils';
import { isRRG } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';

import SalesOrderReader from '@tekion/tekion-business/src/appServices/parts/salesOrder/readers/SalesOrder.reader';

import Button from '@tekion/tekion-components/src/atoms/Button';
import withActions from '@tekion/tekion-components/src/connectors/withActions';
import withRouter from '@tekion/tekion-components/src/hoc/withRouter';
import OnPushMessage from '@tekion/tekion-components/src/molecules/OnPushMessage';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import PermissionError from '@tekion/tekion-components/src/widgets/permissionsHelper/PermissionError';

import ChorusProInvoicingModal from '@tekion/tekion-widgets/src/appServices/accounting/organisms/chorusProInvoicingModal';
import withCashieringRemoteLoader from '@tekion/tekion-widgets/src/appServices/cashiering/hocs/withCashieringRemoteLoader';
import NewTaxUpdateModalWrapper from '@tekion/tekion-widgets/src/appServices/parts/molecules/NewTaxUpdateModal';
import { getPdfViewerGroupIdVsPrinterLogIdsMap } from '@tekion/tekion-widgets/src/appServices/parts/Pdf/pdfV2.helpers';
import usePdfRequestListener from '@tekion/tekion-widgets/src/hooks/usePdfRequestListener';
import usePrevious from '@tekion/tekion-widgets/src/hooks/usePrevious';
import useThirdPaneonClose from '@tekion/tekion-widgets/src/hooks/useThirdPaneOnClose';
import ApprovalRequestDrawer from '@tekion/tekion-widgets/src/organisms/approvalWorkFlowCommon/organisms/approvalRequestDrawer';
import approvalRequestReader from '@tekion/tekion-widgets/src/organisms/approvalWorkFlowCommon/readers/approvalRequest.reader';
import { getPrintModalPreference } from '@tekion/tekion-widgets/src/organisms/AppSkeleton/appSkeleton/skeleton.selector';
import { useSubAppAnalytics } from '@tekion/tekion-widgets/src/utils/analytics/hooks';

import { withTekionConversion } from '@tekion/tekion-conversion-web';

import { CANNOT_ACCESS_SITE_DOCUMENT_ERROR_CODE } from 'constants/errorCodes.constants';

import { getPartsAnalyticsAdditionalPayload } from 'helpers/globalAnalytics.helpers';

import { useFractionalSaleUOMListAndTemplates } from 'hooks/useFractionalSaleUOMListAndTemplates';

import StaticOptionsAutocomplete from 'organisms/Autocomplete/StaticOptionsAutocomplete';
import CustomerNotesModalWrapper from 'organisms/CustomerNotesModalWrapper';
import { PICKLIST_TYPES } from 'organisms/PickList/pickList.constants';

import { getPriorityCodes, getSourceCodesByIds, getCostCenters, getManufacturerConfig } from 'pages/base/app.selectors';

import { useGetUpdatedSellingPriceQuery, useInventoryAverageCostByInventoryIdQuery } from 'queries/parts';

import GeneralSettingsReader from 'readers/generalSettings.reader';
import { mplValuesReader } from 'readers/mpl.reader';

import PartKitAdditionDrawer from 'shared/components/PartKitAdditionDrawer';
import { triggerFormSubmitWithBlurActiveElement } from 'shared/general/drawer.helpers';
import { PICKLIST_ACTION } from 'shared/PicklistV2/UserSettings/constants';
import { getPartsPOPDFGenerationPusherChannelName } from 'shared/PurchaseOrder/purchaseOrderPDF/general';
import { getSalesOrderListViewRoute, getSalesOrderCreateRoute } from 'shared/SalesOrder/partsSalesOrder.routes.helpers';
import SORCancellationDrawerWrapper from 'shared/SORDetail/components/SORCancellationDrawerWrapper';

import loadable from 'utils/loadable';

import { getIsPDFDocumentServiceEnabled } from '../../../utils/general';
import AdvancedSearchDrawerWrapper from '../components/AdvancedSearchDrawerWrapper';
import AverageCostPriceChangeReviewDrawerWrapper from '../components/AverageCostPriceChangeReviewDrawerWrapper';
import { PART_LIST_TABLE_TAB_KEY } from '../components/FillPartsDrawer/fillPartsDrawer.constants';
import MinGPSellingPriceDrawer from '../components/MinGPSellingPriceDrawer';
import AddNewPartModalWrapper from '../components/modals/AddNewPartModalWrapper';
import AddPartsToReturnModal from '../components/modals/AddPartsToReturnModal';
import BulkRestockingFeeModal from '../components/modals/BulkRestockingFeeModal';
import BulkReturnReasonModal from '../components/modals/BulkReturnReasonModal';
import CloseToChargeAccountModal from '../components/modals/CloseToChargeAccountModal';
import CloseViaCODModal from '../components/modals/CloseViaCODModal';
import EtkaCatalogueImportModalWrapper from '../components/modals/EtkaCatalogueImportModalWrapper';
import MplSelectionModal from '../components/modals/MplSelectionModal';
import PartsReturnModal from '../components/modals/PartsReturnModal';
import PartsSorReferenceDrawer from '../components/modals/PartsSorReferenceDrawer';
// import { COLUMNS } from './PartsSalesOrderDetailBody/PartsSalesOrderDetailForm/components/PartsList/columns.constants';
import PassCodeModalWrapper from '../components/modals/PassCodeModalWrapper';
import PriceChangeConfirmationModal from '../components/modals/PriceChangeConfirmationModal';
import PriorityCodeSelectionForBulkActionModal from '../components/modals/PriorityCodeSelectModal';
import StatusInquiryDrawerWrapper from '../components/modals/StatusInquiryDrawerWrapper';
import TaxUpdateModalWrapper from '../components/modals/TaxUpdateModalWrapper';
import VehicleModal from '../components/modals/VehicleModal';
import VoidModalWrapper from '../components/modals/VoidModalWrapper';
import PartsAvailabilityDrawer from '../components/OemPartsAvailabilityBottomDrawer';
import PartsCashiering from '../components/PartsCashiering';
import PartUpdateModal from '../components/PartUpdateDrawer';
import SellingPriceUpdateMasterDrawerWrapper from '../components/SellingPriceUpdateMasterDrawerWrapper';
import SalesOrderDomain from '../domain/SalesOrder.domain';
import { SALES_ORDER } from '../partsSalesOrder.constants';

import ACTION_HANDLERS, {
  GENERAL_ACTION_TYPES,
  INITIALIZE_ACTION_TYPES,
  MODAL_ACTION_TYPES,
  PDF_ACTION_TYPES,
  AVERAGE_COST_ACTION_TYPE,
  PO_ACTION_TYPES,
  ASSET_COUPON_FEES_ACTION_TYPES,
  SELLING_PRICE_MASTER_UPDATE,
  PART_KIT_ACTION_TYPE,
} from './actionHandlers';
import AddCustomerBottomDrawer from './components/AddCustomerBottomDrawer';
import CancelConfirmationModal from './components/CancelConfirmationModal';
import CoreNotAvailableModalWrapper from './components/CoreNotAvailableModalWrapper';
import CreditLimitInfoDrawer from './components/CreditLimitInfoDrawer';
import CustomLoader from './components/CustomLoader';
import EmailInvoiceModal from './components/EmailInvoiceModal';
import FillPartsDrawerWrapper from './components/FillPartsDrawerWrapper';
import GoBackConfirmationModal from './components/GoBackConfirmationModal';
import LostSaleDrawerWrapper from './components/LostSaleDrawerWrapper';
import MultiPdfListModal from './components/MultiPdfListModal';
import MultiVoidListModal from './components/MultiVoidListModal';
import NegativeSaleConfirmationDrawerWrapper from './components/NegativeSaleConfirmationDrawerWrapper';
import OemLoginModal from './components/OemLoginModal';
import PartCommentsModalWrapper from './components/PartCommentsModalWrapper';
import SubmitConfirmationDrawerWrapper from './components/SubmitConfirmationDrawerWrapper';
import UserLevelSettingDrawer from './components/UserLevelSettingDrawer';
import { ITEM_LIST_DILL_DOWN_FILTERS } from './constants/drillDownFilters.constants';
import { SALES_ORDER_FORM_KEYS } from './constants/general.constants';
import { ROW_SETTINGS_FORM_KEYS } from './constants/rowSettings.constants';
import { USER_LEVEL_SETTING_PREFERENCE_IDS } from './constants/userLevelSetting.constants';
import { formatAverageCostInventoryResponse, getIsInventoryAverageCostRequired } from './helpers/averageCost.helper';
import {
  getHasPermissionToEditSO,
  getIsSOInEditableState,
  getShouldRegisterShortcutKeyForModal,
  getAsyncPaymentPusherChannelName,
  getPOUpdatePusherChannelName,
  getPartReceiviePusherChannelName,
  getPartsSOPDFGenerationPusherChannelName,
  getPartsSOBaseUpdatePusherChannelName,
  getCustomerNameForSalesOrder,
} from './helpers/general.helpers';
import { getWarehouseForPOCreation } from './helpers/purchaseOrder.helpers';
import {
  isPendingTasksPresentInPartList,
  getIsUserInputNeededBulkResolutionTaskPresentInTable,
  getInventoryIds,
  isPendingTasksPresentInPartListForFeesAndCouponSPRecalculation,
} from './helpers/table';
import { useSalesOrderExternalStoreUpdater } from './partsSalesOrderDetail.externalStore';
import styles from './partsSalesOrderDetail.module.scss';
import PartsSalesOrderDetailBody from './PartsSalesOrderDetailBody';
import KeyboardShortcutDrawerWrapper from './PartsSalesOrderDetailBody/KeyboardShortcutDrawerWrapper';
import AddressModal from './PartsSalesOrderDetailBody/PartsSalesOrderDetailForm/components/AddressModalWrapper';
import BulkResolveManualActionDrawer from './PartsSalesOrderDetailBody/PartsSalesOrderDetailForm/components/BulkResolveManualActionDrawer';
import ReplacePartsDrawerWrapper from './PartsSalesOrderDetailBody/PartsSalesOrderDetailForm/components/ReplacePartsDrawerWrapper';
import PartsSalesOrderDetailFooter from './PartsSalesOrderDetailFooter';
import PartsOverviewDrawerWrapper from './PartsSalesOrderDetailFooter/components/PartsOverviewDrawerWrapper';
import PartsSalesOrderDetailHeader from './PartsSalesOrderDetailHeader';
import PDFViewerWrapper from './PartsSalesOrderDetailHeader/components/PDFViewerWrapper';

const VendorSpecialOrder = loadable(() => import('pages/PurchaseOrderNew/VendorSpecialOrder/VendorSpecialOrder'));
const OEMSpecialOrder = loadable(() => import('pages/PurchaseOrderNew/OEMSpecialOrder/OEMSpecialOrder'));
const MiscellaneousPurchaseOrder = loadable(
  () => import('pages/PurchaseOrderNew/MiscellaneousPurchaseOrder/MiscellaneousPurchaseOrder'),
);
const CustomerSORDrawerWrapper = loadable(
  () => import('../components/CustomerSORDrawerWrapper' /* webpackChunkName: "CustomerSORDrawerWrapper" */),
);

import './partsSalesOrderDetail.scss';

const INITIAL_STATE = {
  isFetchingSO: true,
  values: {
    [SALES_ORDER_FORM_KEYS.ITEM_LIST]: [],
  },
  resolvedInfoByPartIdentifierId: {},
  tableDrillDownFilterContext: {
    [SALES_ORDER_FORM_KEYS.ITEM_LIST]: {
      drillDownFilters: ITEM_LIST_DILL_DOWN_FILTERS,
      searchText: null,
      searchFields: ['partNumber', 'partName', 'name'],
      filterAppliedTimeStamp: null,
      rowChildrenFilters: [],
      rowPartLineFilters: [],
    },
  },
  userLevelSettingPreferences: {
    [USER_LEVEL_SETTING_PREFERENCE_IDS.ENABLE_CUSTOMER_SECTION_AUTO_COLLAPSE]: true, // Default Values are set to true for everyone, keeping it here because backend cannot store default values as its a generic api
    [USER_LEVEL_SETTING_PREFERENCE_IDS.SHOW_PART_TABLE_LINE_ACTION]: true,
    [USER_LEVEL_SETTING_PREFERENCE_IDS.SHOW_KEYBOARD_HELP_SECTION]: true,
    [USER_LEVEL_SETTING_PREFERENCE_IDS.SHOW_COD_CONFIRMATION_POPUP]: true,
    [USER_LEVEL_SETTING_PREFERENCE_IDS.SHOW_CHARGE_CONFIRMATION_POPUP]: true,
    [USER_LEVEL_SETTING_PREFERENCE_IDS.PICK_LIST_PRIMARY_ACTION]: isRRG()
      ? PICKLIST_ACTION.PICK
      : PICKLIST_ACTION.PRINT_AND_PICK,
    [USER_LEVEL_SETTING_PREFERENCE_IDS.TABLE_ROWS_SETTINGS]: {
      [ROW_SETTINGS_FORM_KEYS.CORE]: true,
      [ROW_SETTINGS_FORM_KEYS.CORE_RETURN]: true,
      [ROW_SETTINGS_FORM_KEYS.FEE]: true,
      [ROW_SETTINGS_FORM_KEYS.RETURN_REASON]: true,
      [ROW_SETTINGS_FORM_KEYS.COMMENT]: true,
    },
  },
};

// TODO tushar -> move from setInterval to setTimeout
const PENDING_PDF_REQUESTS_EXPIRE_TIMER = 2 * 60 * 1000; // 2 mins

function PartsSalesOrderDetail(props) {
  // TODO 3 kuldeep to move all show* to isVisible and also ensure that all modal/drawers
  const {
    onAction,
    currentSiteId,
    soFlagId,
    soFlagLabel,
    location,
    frozenSalesOrderEntity,
    orderType,
    draftedByUserDetails,
    invoicedByUserDetails,
    createdByUserDetails,
    isFetchingSO,
    salesOrderCashieringDetails,
    values,
    countries,
    states,
    addressModalType,
    addressModalVisible,
    resolvedCustomerDetails,
    isCloseViaCODModalOpen,
    isCloseViaCODModalLoading,
    closeViaCODModalParams,
    isVoidModalVisible,
    submittingVoid,
    voidModalParams,
    pendingPdfRequests,
    isPDFModalVisible,
    isChargeAccountModalOpen,
    chargeAccountModalParams,
    isChargeAccountModalLoading,
    showPartsCashiering,
    salesOrderInvoiceList,
    salesOrderReturnsList,
    partsSettingsMap,
    showChargePaymentMethod,
    activeCashieringInvoice,
    customerUnusedCreditLimitBalance,
    isReissuing,
    isReissueDisabled,
    reIssueActionLoading,
    vehicleModalVisible,
    isTaxUpdateModalVisible,
    isNewTaxUpdateModalVisible,
    taxUpdateModalParams,
    taxUpdateModalV2Params,
    taxCodeList,
    isAvailabilityModalVisible,
    isFillPartsDrawerVisible,
    fillPartsModalParams,
    isSubmitting,
    isSubmittingAsDraft,
    isEtkaCatalogueImportModalVisible,
    isAddNewPartModalVisible,
    addNewPartModalParams,
    shouldPromptForMplSelection,
    mplModalParams,
    canPromptUserForCredentials,
    isCustomerAddDrawerVisible,
    permanentCustomerName,
    partsSorPullDrawerProps,
    isPartSORPullDrawerVisible,
    pdfItems,
    emailOverrides,
    resolvedInfoByPartIdentifierId,
    shouldPromptForReplacedParts,
    replacePartModalParams,
    bulkResolveManualActionProps,
    isBulkResolveManualActionDrawerVisible,
    partCommentsData,
    partCommentsVisible,
    isCancelModalVisible,
    isPriorityCodeSelectModalVisible,
    priorityCodeOptions,
    submittingPriorityCode,
    isCustomerSORDrawerVisible,
    customerSORDrawerParams,
    isKeyboardShortcutDrawerVisible,
    shouldPromptForPassCode,
    isShortageSaleModalOpen,
    isPartsOverviewDrawerVisible,
    isUpdatePartRequestModalOpen,
    isUnSavedChangesWarningModalVisible,
    isCoresNotAvailableModalVisible,
    isBulkResolveManualActionModalVisible,
    isPOSShareConfirmationVisible,
    isGrossProfitConfirmationVisible,
    isStatusInquiryModalVisible,
    isPriceChangeConfirmationModalVisible,
    isAdvancedSearchVisible,
    isRecalculatingFeePricing, // TODO: kj vidit add this props
    isFetchingFeeInfo, // TODO: kj vidit add this props
    vehicleList,
    partsDepartments,
    deletedPartList,
    defaultPartSaleTaxDetails,
    advancedSearchParams,
    isAddPartsToReturnModalVisible,
    addPartsToReturnModalParams,
    isSubmittingAddPartsToReturn,
    isPartReturnMetaInfoModalVisisble,
    partsReturnMetaInfoModalParams,
    isBulkReturnReasonMetaInfoModalVisible,
    isBulkRestockingFeeMetaInfoModalVisible,
    posSessionInfo,
    temporaryOrderNoForPOSInCreateMode,
    isFetchingPOSSessionInfo,
    isPOSDropdownVisible,
    shouldPromptUserForCredentials,
    isFormLockedByPassCode,
    isMultiPdfPrintListModalVisible,
    multiPdfListParams,
    isMultiVoidListModalVisible,
    multiVoidListParams,
    voidReasons,
    checkboxSelectionContext,
    priceChangeConfirmationModalParams,
    isRecalculatingOnPriceCodeChange,
    generateInvoiceTableSelectedRows,
    tertiaryButtonToolTipTitle, // TODO bhavika to remove all these footer props which can be part of footer itself | do we even store these in state, why?
    primaryButtonToolTipTitle,
    tertiaryButtonLabel,
    primaryButtonLabel,
    displayWarningAlongsidePrimaryButtonLabel,
    additionalButtonLabel,
    navigationContext,
    isFetchingSalesOrderCashieringDetails,
    isFetchingSalesOrderInvoiceList,
    isFetchingSalesOrderReturnList,
    errors,
    sorCancellationDrawerParams,
    isSORCancellationDrawerVisible,
    partUpdateModalParams,
    resolvedColumnPreferences,
    isSubmitConfirmationDrawerVisible,
    submitConfirmationDrawerParams,
    currentUserDetails,
    returnReasons,
    tableDrillDownFilterContext,
    isFetchingCustomerCreditLimit,
    isNegativeSaleConfirmationDrawerVisible,
    negativeSaleConfirmationDrawerParams,
    isFetchFailed,
    error,
    isBootstrapFetchFailed,
    resolvedCoreDetailByPartId,
    isUserLevelSettingDrawerVisible,
    userLevelSettingPreferences,
    isSubmittingUserPreferences,
    sortDetails,
    selectedPartListTabKey,
    resolvedPartsFeeList,
    resolvedPartsCouponList,
    taxCodeDetailsList,
    taxCodeOverrides,
    isSaleTypeChangeConfirmationDialogOpen,
    isAverageCostPriceChangeReviewDrawerVisible,
    isFetchingBootstrap,
    averageCostChangedPartLineItemList,
    isAverageCostPriceChangeSubmitting,
    isCreditLimitInfoDrawerVisible,
    customerCreditLimitInfo,
    getDefaultCountryCode,
    isEmailInvoiceModalVisible,
    emailInvoiceModalParams,
    afterPrintPDFCallback,
    isSilentUpdateInProgress,
    isAverageCostUpdated,
    isPdfDownloading,
    isLostSaleDrawerVisible,
    lostSaleDrawerParams,
    activePOModalType,
    isVendorSpecialPOSubmitting,
    vendorSpecialPOPartsList,
    oemSpecialPOPartsList,
    vendorSpecialPOReissueParams = EMPTY_OBJECT,
    isOEMSpecialPOSubmitting,
    oemSpecialPOReissueParams = EMPTY_OBJECT,
    isMiscellaneousPOSubmitting,
    miscellaneousPOPartsList,
    miscellaneousPOReissueParams = EMPTY_OBJECT,
    isPDFDownloadedBySource,
    viewPdfDocumentType,
    pdfDocDetails,
    isGeneratingHTMLPdf,
    isFetchingHtmlPdfConfiguration,
    shouldEnableHTMLPdf,
    additionalActionsForPdfHeader,
    closeSOInProgress,
    initialSelectedPdfGroupId,
    initialSelectedPdfVersion,
    lastTriggeredPrintTime,
    assetIdvsWatermarkStatusPDFV2,
    currentPdfWaterMarkStatus,
    docServicePDFs,
    resolvedPODetails,
    chorusProModalParams,
    resolvedCustomerNotes,
    isNotesModalVisible,
    coresNotAvailableModalParams,
    isFetchingCouponInfo,
    isRecalculatingCouponPricing,
    getFormattedDateAndTime,
    manufacturerConfig,
    isUpdateSellingPriceMasterDrawerVisible,
    isUpdateSellingPriceMasterBannerVisible,
    isAddPartKitDrawerVisible,
    partKitInfoToResolve,
    fractionalSaleUOMValuesById,
    fractionalSaleTemplateById,
    isFetchPurchaseOrderDetailsTaskDone,
    isFractionalSaleUOMListAndTemplatesFetched,
    isCustomerTaxExemptedInNewTaxCode,
    isMinGPSellingPriceUpdateDrawerVisible,
    minGPSellingPriceUpdateDrawerParams,
    resolvedPriceCodeDetails,
    params,
    isEPCDrawerVisible,
    isApprovalRequestDrawerVisible,
    creditLimitApprovalRequestId,
    creditLimitApprovalRequestDetails,
    selectedWarehouse,
  } = props;

  const headerRef = useRef(null);
  const navigate = useNavigate();
  const currentSalesOrderRefId = SalesOrderReader.id(frozenSalesOrderEntity);

  const taxDetails = _get(values, SALES_ORDER_FORM_KEYS.TAX_DETAILS, EMPTY_OBJECT);

  const isRedirectToCreateNewSoScreenAfterSoClosedEnabled =
    userLevelSettingPreferences[USER_LEVEL_SETTING_PREFERENCE_IDS.REDIRECT_TO_CREATE_NEW_SO_SCREEN_AFTER_SO_CLOSED];

  const { data: sellingPriceUpdatedPartsList = EMPTY_ARRAY } = useGetUpdatedSellingPriceQuery(
    {
      refType: SALES_ORDER,
      refId: currentSalesOrderRefId,
    },
    {
      enabled: !!currentSalesOrderRefId && !SalesOrderDomain.isVoided(frozenSalesOrderEntity),
      retry: false,
      onSuccess: data => {
        if (_size(data)) {
          onAction({ type: SELLING_PRICE_MASTER_UPDATE.OPEN_UPDATE_SELLING_PRICE_BANNER });
          return;
        }
        onAction({ type: SELLING_PRICE_MASTER_UPDATE.CLOSE_UPDATE_SELLING_PRICE_BANNER });
      },
    },
  );

  const previousSalesOrderEntity = usePrevious(frozenSalesOrderEntity);

  useEffect(() => {
    const currentSalesOrderStatus = SalesOrderReader.status(frozenSalesOrderEntity);
    const previousSalesOrderStatus = SalesOrderReader.status(previousSalesOrderEntity);
    if (
      isRedirectToCreateNewSoScreenAfterSoClosedEnabled &&
      !_isEmpty(previousSalesOrderStatus) &&
      previousSalesOrderStatus !== currentSalesOrderStatus &&
      SalesOrderDomain.isClosed(frozenSalesOrderEntity)
    ) {
      navigate(getSalesOrderCreateRoute(), { state: { isRemount: true } });
    }
  }, [
    frozenSalesOrderEntity,
    navigate,
    isRedirectToCreateNewSoScreenAfterSoClosedEnabled,
    onAction,
    previousSalesOrderEntity,
  ]);

  const isMounted = useRef(false);
  usePdfRequestListener(pendingPdfRequests, onAction);
  const showPermissionError = isFetchFailed && getErrorCode(error) === CANNOT_ACCESS_SITE_DOCUMENT_ERROR_CODE;

  useEffect(() => {
    if (!_isNil(frozenSalesOrderEntity)) {
      onAction({
        type: GENERAL_ACTION_TYPES.ON_SALES_ORDER_ENTITY_UPDATE,
        payload: {
          previousSalesOrderEntity,
        },
      });
    }
  }, [onAction, frozenSalesOrderEntity, previousSalesOrderEntity, partsDepartments]);

  useEffect(() => {
    if (!isMounted.current) {
      // TODO 3 vidit add explanation on why this check is needed
      isMounted.current = true;
      onAction({
        type: INITIALIZE_ACTION_TYPES.INITIALIZE,
      });
    }
  }, [onAction, location]); // TODO 2 tushar ensure this deps array is proper even after moving this inside, if we are dependant on soemthing from these inside our action handlers

  useEffect(() => {
    // TODO 2 tushar -> move from setInterval to setTimeout
    const intervalId = setInterval(() => {
      onAction({
        type: PDF_ACTION_TYPES.CHECK_PENDING_REQUEST_EXPIRATION,
      });
    }, PENDING_PDF_REQUESTS_EXPIRE_TIMER);
    return () => {
      clearInterval(intervalId);
    };
  }, [onAction]);

  const asyncPaymentPusherChannelName = getAsyncPaymentPusherChannelName();
  const salesOrderPOCreationUpdateChannelName = getPOUpdatePusherChannelName();
  const salesOrderReceiveUpdateChannelName = getPartReceiviePusherChannelName();
  const salesOrderPDFGenerationChannelName = getPartsSOPDFGenerationPusherChannelName();
  const purchaseOrderPDFGenerationChannelName = getPartsPOPDFGenerationPusherChannelName();
  const salesOrderBaseUpdateChannelName = getPartsSOBaseUpdatePusherChannelName();

  useSubAppAnalytics({
    isLoading: isFetchingSO || isFetchingBootstrap,
    ...getPartsAnalyticsAdditionalPayload('so-revamp-detail-view'),
  });

  const partsList = _get(values, SALES_ORDER_FORM_KEYS.ITEM_LIST, EMPTY_ARRAY);
  const salesOrderFees = _get(values, SALES_ORDER_FORM_KEYS.SALES_ORDER_FEES, EMPTY_ARRAY);
  const vehicleId = _get(values, [SALES_ORDER_FORM_KEYS.VIN, 0], null);
  const saleType = _get(values, [SALES_ORDER_FORM_KEYS.SALE_TYPE, 0], null);
  const customerId = _get(values, SALES_ORDER_FORM_KEYS.CUSTOMER, null);

  const valuesForPOS = useMemo(
    () => ({ partsList, salesOrderFees, vehicleId, saleType, customerId, taxDetails }),
    [partsList, salesOrderFees, vehicleId, saleType, customerId, taxDetails],
  );

  const printerLogIdsMap = useMemo(() => getPdfViewerGroupIdVsPrinterLogIdsMap(docServicePDFs), [docServicePDFs]);

  // TODO bhavika move inside the header itself
  const getHeaderProps = () => ({
    valuesForPOS,
    module: SALES_ORDER_MODULES.SALES_ORDER,
    goBackTo: getSalesOrderListViewRoute(), // TODO why bhavika are we always re-creating this
    currentSiteId,
    onAction,
    soFlagId,
    soFlagLabel,
    frozenSalesOrderEntity,
    orderType,
    draftedByUserDetails,
    invoicedByUserDetails,
    createdByUserDetails,
    isFetchingSO,
    salesOrderCashieringDetails,
    pendingPdfRequests,
    isReissuing,
    isReissueDisabled,
    reIssueActionLoading,
    resolvedCustomerDetails,
    posSessionInfo,
    isPOSDropdownVisible,
    temporaryOrderNoForPOSInCreateMode,
    isFetchingPOSSessionInfo,
    // TODO 2 vidit get this saved in form itself as a single object -> then it will be easier
    quoteApplied: mplValuesReader.quoteApplied(values),
    mplRefType: mplValuesReader.mplRefType(values),
    mplRefId: mplValuesReader.mplRefId(values),
    currentUserDetails,
    salesOrderInvoiceList,
    lastTriggeredPrintTime,
    printerLogIdsMap,
    getFormattedDateAndTime,
    isUpdateSellingPriceMasterBannerVisible,
    navigate,
    creditLimitApprovalRequestDetails,
    selectedWarehouse,
  });

  const quoteLocked = SalesOrderReader.quoteLocked(frozenSalesOrderEntity);

  const isSOInEditableState = getIsSOInEditableState({ salesOrderEntity: frozenSalesOrderEntity, isReissuing });
  const hasPermissionToEditSO = getHasPermissionToEditSO({ isFormLockedByPassCode });

  const isFormEditable = isSOInEditableState && hasPermissionToEditSO && !quoteLocked;
  const currentTab = _get(params, 'tabType');
  // TODO tushar, why are we adding these methods in this main component???
  const handleVehicleModalSuccess = value => {
    onAction({
      type: MODAL_ACTION_TYPES.ADD_NEW_VEHICLE,
      payload: {
        vehicle: value,
      },
    });
  };

  const handleToggleVehicleModal = () => {
    onAction({
      type: MODAL_ACTION_TYPES.TOGGLE_VEHICLE_MODAL,
    });
  };

  // TODO Fix these flags -> possibly merge them in one function or so
  const isAnyPendingTaskPresentInTables = isPendingTasksPresentInPartList({
    partList: values[SALES_ORDER_FORM_KEYS.ITEM_LIST],
  });

  // TODO -1 vidit lets always get both the values and wrap it useMemo
  const isAnyPendingTaskPresentInTablesApartFromUserInputNeeded = isPendingTasksPresentInPartList({
    partList: values[SALES_ORDER_FORM_KEYS.ITEM_LIST],
    skipUserInputNeededCheck: true,
  });

  const isUserInputNeededBulkResolutionTaskPresentInTable = useMemo(
    () =>
      getIsUserInputNeededBulkResolutionTaskPresentInTable({
        partList: values[SALES_ORDER_FORM_KEYS.ITEM_LIST],
      }),
    [values],
  );

  const itemList = _get(values, SALES_ORDER_FORM_KEYS.ITEM_LIST);

  const isPrimaryDisabled =
    isSubmitting ||
    isAnyPendingTaskPresentInTables ||
    isRecalculatingFeePricing ||
    isFetchingFeeInfo ||
    isFetchingCouponInfo ||
    isRecalculatingCouponPricing;

  const _contextForFormLevelValidation = useRef({});
  useSalesOrderExternalStoreUpdater({
    values,
    errors,
    isFormEditable,
    frozenSalesOrderEntity,
    currentSiteId,
    orderType,
    taxDetails,
    navigationContext,
    resolvedInfoByPartIdentifierId,
    resolvedColumnPreferences,
    onAction,
    tableDrillDownFilterContext,
    salesOrderInvoiceList,
    userLevelSettingPreferences,
    sortDetails,
    resolvedPartsFeeList,
    resolvedPartsCouponList,
    partSaleTaxDetails: taxDetails,
    taxCodeDetailsList,
    taxCodeOverrides,
    averageCostChangedPartLineItemList,
    returnReasons,
    resolvedPriceCodeDetails,
    selectedWarehouse,
  });

  const isAnyPendingTaskPresentInTablesForFeesAndCouponSPRecalculation = useMemo(
    () =>
      isPendingTasksPresentInPartListForFeesAndCouponSPRecalculation({
        partList: values[SALES_ORDER_FORM_KEYS.ITEM_LIST],
      }),
    [values],
  );
  useEffect(() => {
    if (
      !isAnyPendingTaskPresentInTablesForFeesAndCouponSPRecalculation &&
      !_isEmpty(values[SALES_ORDER_FORM_KEYS.ITEM_LIST])
    ) {
      onAction({
        type: ASSET_COUPON_FEES_ACTION_TYPES.RECALCULATE_SO_FEES_AND_COUPON_SELLING_PRICE,
      });
    }
  }, [isAnyPendingTaskPresentInTablesForFeesAndCouponSPRecalculation]); // Intended

  useEffect(() => {
    _set(_contextForFormLevelValidation, 'current.resolvedInfoByPartIdentifierId', resolvedInfoByPartIdentifierId);
    _set(_contextForFormLevelValidation, 'current.frozenSalesOrderEntity', frozenSalesOrderEntity);
    _set(_contextForFormLevelValidation, 'current.salesOrderInvoiceList', salesOrderInvoiceList);
    _set(_contextForFormLevelValidation, 'current.resolvedCoreDetailByPartId', resolvedCoreDetailByPartId);
    _set(_contextForFormLevelValidation, 'current.isFormEditable', isFormEditable);
  }, [resolvedInfoByPartIdentifierId, frozenSalesOrderEntity, salesOrderInvoiceList, resolvedCoreDetailByPartId]);

  useEffect(() => {
    if (!_isNil(frozenSalesOrderEntity) && !_isEmpty(resolvedInfoByPartIdentifierId) && !isFormEditable) {
      onAction({
        type: GENERAL_ACTION_TYPES.ON_SALES_ORDER_ENTITY_UPDATE,
        payload: {
          previousSalesOrderEntity,
        },
      });
    }
  }, [frozenSalesOrderEntity, resolvedInfoByPartIdentifierId, isFormEditable, previousSalesOrderEntity, onAction]);

  const handlePOrefreshOnThirdPaneClose = useCallback(() => {
    onAction({
      type: GENERAL_ACTION_TYPES.REFRESH_SO_ON_THIRDPANE_CLOSE,
    });
  }, [onAction]);

  useThirdPaneonClose(handlePOrefreshOnThirdPaneClose);

  const setFractionalSaleTempleToState = useCallback(
    payload => {
      onAction({ type: GENERAL_ACTION_TYPES.HANDEL_FETCH_FRACTIONAL_SALE_TEMPLATE, payload });
    },
    [onAction],
  );
  useFractionalSaleUOMListAndTemplates({ setFractionalSaleTempleToState, isFractionalSaleUOMListAndTemplatesFetched });

  const contextForFormLevelValidation = useMemo(
    () => ({
      contextForFormLevelValidation: _contextForFormLevelValidation.current,
    }),
    [],
  );

  const inventoryIdList = useMemo(
    () => getInventoryIds(resolvedInfoByPartIdentifierId),
    [resolvedInfoByPartIdentifierId],
  );
  const isInventoryAverageCostRequired = useMemo(
    () =>
      getIsInventoryAverageCostRequired({
        isFetchingSO,
        isFetchFailed,
        inventoryIdList,
        isFetchingBootstrap,
        isBootstrapFetchFailed,
        frozenSalesOrderEntity,
      }),
    [isFetchingSO, isFetchFailed, inventoryIdList, isFetchingBootstrap, isBootstrapFetchFailed, frozenSalesOrderEntity],
  );

  useEffect(() => {
    if (isInventoryAverageCostRequired) {
      onAction({ type: GENERAL_ACTION_TYPES.CHECK_ANY_SALES_ORDER_VALUE_UPDATED });
    }
  }, [
    values,
    orderType,
    sortDetails,
    isReissuing,
    vehicleList,
    currentSiteId,
    isFormEditable,
    deletedPartList,
    partsDepartments,
    priorityCodeOptions,
    frozenSalesOrderEntity,
    resolvedCustomerDetails,
    defaultPartSaleTaxDetails,
    isInventoryAverageCostRequired,
    onAction,
  ]); // intended

  const {
    data: inventoryAverageCostByPartIdentifierId,
    dataUpdatedAt: averageCostDataUpdatedAt,
    isFetching: isInventoryAverageCostQueryFetching,
    refetch: refetchInventoryAverageCostByInventoryIdQuery,
  } = useInventoryAverageCostByInventoryIdQuery(inventoryIdList, {
    enabled: isInventoryAverageCostRequired && !isFormEditable,
    select: useCallback(
      data => formatAverageCostInventoryResponse({ resolvedInfoByPartIdentifierId, data }),
      [resolvedInfoByPartIdentifierId],
    ),
  });

  const partLineItemList = values[SALES_ORDER_FORM_KEYS.ITEM_LIST];
  useEffect(() => {
    if (
      !isFormEditable &&
      !isAverageCostUpdated &&
      isInventoryAverageCostRequired &&
      !isAverageCostPriceChangeSubmitting &&
      isFetchPurchaseOrderDetailsTaskDone
    ) {
      onAction({
        type: AVERAGE_COST_ACTION_TYPE.COMPUTE_AVERAGE_COST_CHANGED_PART_LINE_LIST,
        payload: { inventoryAverageCostByPartIdentifierId },
      });
    }
  }, [
    onAction,
    isFormEditable,
    partLineItemList,
    isAverageCostUpdated,
    inventoryAverageCostByPartIdentifierId,
    isInventoryAverageCostRequired,
    isAverageCostPriceChangeSubmitting,
    isFetchPurchaseOrderDetailsTaskDone,
  ]);

  const resolvedPartInfoForAddPartKitDrawer = useMemo(
    () => ({
      resolvedPartKit: _get(partKitInfoToResolve, ['resolvedPartKitDetails', 'resolvedPartKit'], EMPTY_OBJECT),
      resolvedParts: _get(partKitInfoToResolve, ['resolvedPartKitDetails', 'resolvedParts'], EMPTY_OBJECT),
    }),
    [partKitInfoToResolve],
  );

  const asyncPaymentReceiveHandler = useCallback(
    params => {
      const soId = SalesOrderReader.id(frozenSalesOrderEntity);
      const salesOrderId = _get(params, 'defaultPayload.salesOrderId', EMPTY_OBJECT);
      if (_toNumber(soId) === _toNumber(salesOrderId)) {
        onAction({
          type: GENERAL_ACTION_TYPES.ASYNC_PAYMENT_RECEIVED_EVENT_HANDLER,
        });
      }
    },
    [onAction, frozenSalesOrderEntity],
  );

  const poCreationUpdateHandler = useCallback(
    params => {
      const soId = SalesOrderReader.id(frozenSalesOrderEntity);
      const salesOrderId = _get(params, 'defaultPayload.salesOrderId', EMPTY_OBJECT);
      const status = _get(params, 'defaultPayload.status', EMPTY_OBJECT);
      if (_toNumber(soId) === _toNumber(salesOrderId)) {
        onAction({
          type: GENERAL_ACTION_TYPES.PO_CREATION_UPDATE_EVENT_HANDLER,
          payload: { status },
        });
      }
    },
    [onAction, frozenSalesOrderEntity],
  );

  const receiveSilentUpdateHandler = useCallback(
    params => {
      if (isFormEditable) {
        return;
      }
      const soId = SalesOrderReader.id(frozenSalesOrderEntity);
      const salesOrderIdList = _get(params, 'defaultPayload.salesOrderIds', EMPTY_ARRAY);
      const isCurrentSOUpdated = _some(salesOrderIdList, id => _toNumber(id) === _toNumber(soId));
      if (isCurrentSOUpdated) {
        onAction({
          type: GENERAL_ACTION_TYPES.REFRESH_SO_ON_RECEIVING,
        });
      }
    },
    [onAction, frozenSalesOrderEntity, isFormEditable],
  );

  const fetchPOPdfUpdateHandler = useCallback(
    params => {
      if (!getIsPDFDocumentServiceEnabled()) return;

      const pusherActionType = _get(params, ['defaultPayload', 'action'], EMPTY_STRING);

      const assetTypeFromPusherEvent = _get(params, ['defaultPayload', 'assetType'], EMPTY_STRING);
      const assetIdFromPusherEvent = _get(params, ['defaultPayload', 'assetId'], EMPTY_STRING);
      const partPOCreatedArray = _get(resolvedPODetails, 'partPOCreated', EMPTY_ARRAY);

      const selectedPO = _find(partPOCreatedArray, partPO => {
        const currOrderNumber = _get(partPO, ['purchaseOrderDetail', 'orderNumber'], EMPTY_STRING);
        const currOrderType = _get(partPO, ['purchaseOrderDetail', 'orderType'], EMPTY_STRING);

        return (
          assetTypeFromPusherEvent === currOrderType && _toString(assetIdFromPusherEvent) === _toString(currOrderNumber)
        );
      });

      if (_isEmpty(selectedPO)) return;

      onAction({
        type: GENERAL_ACTION_TYPES.REFRESH_SO_PDF,
        payload: {
          salesOrderEntity: frozenSalesOrderEntity,
          actionType: pusherActionType,
        },
      });
    },
    [onAction, frozenSalesOrderEntity, resolvedPODetails],
  );

  const fetchPDFupdateHandler = useCallback(
    params => {
      if (!getIsPDFDocumentServiceEnabled()) return;
      const parentAssetIdFromPusherEvent = _get(params, ['defaultPayload', 'parentAssetId'], EMPTY_STRING);
      const pusherActionType = _get(params, ['defaultPayload', 'action'], EMPTY_STRING);
      const isCurrentSOUpdated =
        _toNumber(parentAssetIdFromPusherEvent) === _toNumber(SalesOrderReader.id(frozenSalesOrderEntity));
      if (isCurrentSOUpdated) {
        onAction({
          type: GENERAL_ACTION_TYPES.REFRESH_SO_PDF,
          payload: {
            salesOrderEntity: frozenSalesOrderEntity,
            actionType: pusherActionType,
          },
        });
      }
    },
    [onAction, frozenSalesOrderEntity],
  );

  const updateSalesOrderBaseStateHandler = useCallback(
    params => {
      if (isFormEditable) {
        return;
      }
      const soId = SalesOrderReader.id(frozenSalesOrderEntity);
      const salesOrderId = _get(params, 'defaultPayload.salesOrderId', EMPTY_ARRAY);
      if (_toNumber(salesOrderId) === _toNumber(soId)) {
        onAction({
          type: GENERAL_ACTION_TYPES.BASE_REFRESH_SALES_ORDER,
        });
      }
    },
    [onAction, frozenSalesOrderEntity, isFormEditable],
  );

  const hidePOModal = useCallback(() => {
    onAction({
      type: PO_ACTION_TYPES.HIDE_PO_MODAL,
    });
  }, [onAction]);

  const handleVendorPOSubmit = useCallback(
    payload => {
      onAction({
        type: PO_ACTION_TYPES.HANDLE_VENDOR_PO_SUBMIT,
        payload,
      });
    },
    [onAction],
  );

  const handleOEMSpecialPOSubmit = useCallback(
    payload => {
      onAction({
        type: PO_ACTION_TYPES.HANDLE_OEM_PO_SUBMIT,
        payload,
      });
    },
    [onAction],
  );

  const handleMiscPOSubmit = useCallback(
    payload => {
      onAction({
        type: PO_ACTION_TYPES.HANDLE_MISC_PO_SUBMIT,
        payload,
      });
    },
    [onAction],
  );

  const customerName = useMemo(
    () =>
      getCustomerNameForSalesOrder({
        frozenSalesOrderEntity,
        resolvedCustomerDetails,
      }),
    [frozenSalesOrderEntity, resolvedCustomerDetails],
  );

  const closePartKitAdditionDrawer = useCallback(
    () =>
      onAction({
        type: GENERAL_ACTION_TYPES.CLOSE_PART_KIT_DRAWER,
      }),
    [onAction],
  );

  const submitPartKitAdditionDrawer = useCallback(
    partIdsToResolve => {
      onAction({
        type: PART_KIT_ACTION_TYPE.SUBMIT_ADD_PART_KIT_DRAWER,
        payload: {
          partIdsToResolve,
        },
      });
      closePartKitAdditionDrawer();
    },
    [onAction, closePartKitAdditionDrawer],
  );

  const closeApprovalRequestDrawer = useCallback(() => {
    onAction({ type: MODAL_ACTION_TYPES.CLOSE_APPROVAL_REQUEST_DRAWER });
  }, [onAction]);

  const closeDrawerAndRefetchApprovalRequestDetails = useCallback(() => {
    onAction({ type: MODAL_ACTION_TYPES.ON_APPROVAL_REQUEST_DRAWER_WITHDRAW_OR_REJECT_OR_APPROVE });
  }, [onAction]);

  const withPartSearchData = useMemo(() => _values(resolvedInfoByPartIdentifierId), [resolvedInfoByPartIdentifierId]);

  const redirectToListView = useCallback(() => {
    navigate(getSalesOrderListViewRoute());
  }, [navigate]);

  const renderMessage = useCallback(
    () => (
      <>
        <div>{__('You do not have the permissions to access current document.')}</div>
        <Button view={Button.VIEW.TERTIARY} onClick={redirectToListView}>
          {__('Home')}
        </Button>
      </>
    ),
    [redirectToListView],
  );

  if (showPermissionError) {
    return <PermissionError message={renderMessage()} />;
  }
  if (isFetchFailed) {
    if (isBootstrapFetchFailed) {
      return (
        <div className={styles.fetchFailed}>
          {__('Sales order bootstrap data fetching failed, please reload to try again')}
        </div>
      );
    }
    return <div className={styles.fetchFailed}>{__('Sales order fetching failed, please reload to try again')}</div>;
  }

  if (activePOModalType === PO_TYPE.NON_OEM_SPECIAL_ORDER) {
    return (
      <VendorSpecialOrder
        isInWidgetMode
        isSubmitting={isVendorSpecialPOSubmitting}
        onCancelAction={hidePOModal}
        handleBackAction={hidePOModal}
        onSubmitAction={handleVendorPOSubmit}
        partsList={vendorSpecialPOPartsList}
        withPartSearchData={withPartSearchData}
        refId={SalesOrderReader.id(frozenSalesOrderEntity)}
        refType={PICKLIST_TYPES.SALES_ORDER}
        selectedWarehouse={getWarehouseForPOCreation(vendorSpecialPOPartsList)}
        {...vendorSpecialPOReissueParams}
      />
    );
  }

  if (activePOModalType === PO_TYPE.OEM_SPECIAL_ORDER) {
    return (
      <OEMSpecialOrder
        isInWidgetMode
        isSubmitting={isOEMSpecialPOSubmitting}
        onCancelAction={hidePOModal}
        handleBackAction={hidePOModal}
        onSubmitAction={handleOEMSpecialPOSubmit}
        partsList={oemSpecialPOPartsList}
        selectedWarehouse={getWarehouseForPOCreation(oemSpecialPOPartsList)}
        {...oemSpecialPOReissueParams}
      />
    );
  }

  if (activePOModalType === PO_TYPE.MISCELLANEOUS) {
    return (
      <MiscellaneousPurchaseOrder
        isInWidgetMode
        isSubmitting={isMiscellaneousPOSubmitting}
        onCancelAction={hidePOModal}
        handleBackAction={hidePOModal}
        onSubmitAction={handleMiscPOSubmit}
        partsList={miscellaneousPOPartsList}
        selectedWarehouse={getWarehouseForPOCreation(miscellaneousPOPartsList)}
        {...miscellaneousPOReissueParams}
      />
    );
  }

  const creditLimitApprovalId = approvalRequestReader.id(creditLimitApprovalRequestDetails);

  return (
    <div
      className={cx(styles.main, 'full-height', {
        [styles.disabled]: isRecalculatingOnPriceCodeChange,
      })}
    >
      {/* TODO bhavika send only what we need */}
      <PartsSalesOrderDetailHeader
        {...getHeaderProps()}
        headerRef={headerRef}
        isFormEditable={isFormEditable}
        isFetchingHtmlPdfConfiguration={isFetchingHtmlPdfConfiguration}
        getFormattedDateAndTime={getFormattedDateAndTime}
      />
      <PartsSalesOrderDetailBody
        {...props}
        printerLogIdsMap={printerLogIdsMap}
        headerRef={headerRef}
        areTasksExecutionInProgress={isAnyPendingTaskPresentInTablesApartFromUserInputNeeded}
        isFormEditable={isFormEditable}
        hasPermissionToEditSO={hasPermissionToEditSO}
        contextForFormLevelValidation={contextForFormLevelValidation}
      />
      {/* TODO bhavika send only what we need */}
      <PartsSalesOrderDetailFooter
        isAnyPendingTaskPresentInTablesApartFromUserInputNeeded={
          isAnyPendingTaskPresentInTablesApartFromUserInputNeeded
        }
        tertiaryButtonToolTipTitle={tertiaryButtonToolTipTitle}
        primaryButtonToolTipTitle={primaryButtonToolTipTitle}
        tertiaryButtonLabel={tertiaryButtonLabel}
        primaryButtonLabel={primaryButtonLabel}
        isTertiaryButtonDisabled={isPrimaryDisabled}
        isPrimaryButtonDisabled={isPrimaryDisabled}
        displayWarningAlongsidePrimaryButtonLabel={displayWarningAlongsidePrimaryButtonLabel} // TODO vidit lets discuss on this
        currentTab={currentTab} // TODO vidit lets only send the actual objects - location ... any issues?
        onAction={onAction}
        frozenSalesOrderEntity={frozenSalesOrderEntity}
        isReissuing={isReissuing}
        taxDetails={taxDetails}
        isSubmitting={isSubmitting}
        additionalButtonLabel={additionalButtonLabel}
        partsSettingsMap={partsSettingsMap}
        currentSiteId={currentSiteId}
        orderType={orderType}
        isPartsOverviewDrawerVisible={isPartsOverviewDrawerVisible}
        resolvedCustomerDetails={resolvedCustomerDetails}
        isFormEditable={isFormEditable}
        primaryActionLoading={isSubmitting && !isSubmittingAsDraft}
        tertiaryActionLoading={isSubmittingAsDraft}
        salesOrderInvoiceList={salesOrderInvoiceList}
        salesOrderCashieringDetails={salesOrderCashieringDetails}
        salesOrderReturnsList={salesOrderReturnsList}
        isFetchingSalesOrderInvoiceList={isFetchingSalesOrderInvoiceList}
        isFetchingSalesOrderCashieringDetails={isFetchingSalesOrderCashieringDetails}
        isFetchingSalesOrderReturnList={isFetchingSalesOrderReturnList}
        isUserInputNeededBulkResolutionTaskPresentInTable={isUserInputNeededBulkResolutionTaskPresentInTable}
        isFetchingCustomerCreditLimit={isFetchingCustomerCreditLimit}
        customerCreditLimitInfo={customerCreditLimitInfo}
        customerUnusedCreditLimitBalance={customerUnusedCreditLimitBalance}
        closeSOInProgress={closeSOInProgress}
        resolvedPriceCodeDetails={resolvedPriceCodeDetails}
        creditLimitApprovalRequestDetails={creditLimitApprovalRequestDetails}
      />

      {/* -------------------------------------------------- modal / drawer components start -------------------------------------------------- */}
      {/* TODO tushar send only what we need and if more state use *ModalParams | even compute the address value in the same upon visible */}
      <AddressModal
        onAction={onAction}
        visible={addressModalVisible}
        addressModalType={addressModalType}
        resolvedCustomerDetails={resolvedCustomerDetails}
        countries={countries}
        states={states}
        address={_get(values, addressModalType)}
        isDisabled={!isFormEditable}
      />
      <CloseViaCODModal
        {...closeViaCODModalParams}
        isVisible={isCloseViaCODModalOpen}
        onAction={onAction}
        loading={isCloseViaCODModalLoading} // TODO 2 shivam move this inside closeViaCODModalParams
        frozenSalesOrderEntity={frozenSalesOrderEntity}
      />
      <CloseToChargeAccountModal
        visible={isChargeAccountModalOpen}
        chargeAccountModalParams={chargeAccountModalParams} // TODO 2 need to add onCancel and onSubmit actions
        loading={isChargeAccountModalLoading}
        frozenSalesOrderEntity={frozenSalesOrderEntity}
        onAction={onAction}
        customerUnusedCreditLimitBalance={customerUnusedCreditLimitBalance}
      />
      <KeyboardShortcutDrawerWrapper
        isKeyboardShortcutDrawerVisible={isKeyboardShortcutDrawerVisible}
        onAction={onAction}
        isFormEditable={isFormEditable}
        hasPermissionToEditSO={hasPermissionToEditSO}
        isPrimaryDisabled={isPrimaryDisabled}
        isPDFModalVisible={isPDFModalVisible}
        showPartsCashiering={showPartsCashiering}
        isVoidModalVisible={isVoidModalVisible}
        vehicleModalVisible={vehicleModalVisible}
        isAddNewPartModalVisible={isAddNewPartModalVisible}
        shouldPromptForPassCode={shouldPromptForPassCode}
        isShortageSaleModalOpen={isShortageSaleModalOpen}
        partCommentsVisible={partCommentsVisible}
        shouldPromptForMplSelection={shouldPromptForMplSelection}
        shouldPromptForReplacedParts={shouldPromptForReplacedParts}
        isTaxUpdateModalVisible={isTaxUpdateModalVisible}
        isChargeAccountModalOpen={isChargeAccountModalOpen}
        isAvailabilityModalVisible={isAvailabilityModalVisible}
        isFillPartsDrawerVisible={isFillPartsDrawerVisible}
        isPartsOverviewDrawerVisible={isPartsOverviewDrawerVisible}
        isUpdatePartRequestModalOpen={isUpdatePartRequestModalOpen}
        isUnSavedChangesWarningModalVisible={isUnSavedChangesWarningModalVisible}
        isCancelModalVisible={isCancelModalVisible}
        isCoresNotAvailableModalVisible={isCoresNotAvailableModalVisible}
        isBulkResolveManualActionModalVisible={isBulkResolveManualActionModalVisible}
        isPOSShareConfirmationVisible={isPOSShareConfirmationVisible}
        isGrossProfitConfirmationVisible={isGrossProfitConfirmationVisible}
        isStatusInquiryModalVisible={isStatusInquiryModalVisible}
        isPriceChangeConfirmationModalVisible={isPriceChangeConfirmationModalVisible}
        isEtkaCatalogueImportModalVisible={isEtkaCatalogueImportModalVisible}
        isCloseViaCODModalOpen={isCloseViaCODModalOpen}
        isAdvancedSearchVisible={isAdvancedSearchVisible}
        isCustomerAddDrawerVisible={isCustomerAddDrawerVisible}
        isPriorityCodeSelectModalVisible={isPriorityCodeSelectModalVisible}
        isPartReturnMetaInfoModalVisisble={isPartReturnMetaInfoModalVisisble}
        isBulkReturnReasonMetaInfoModalVisible={isBulkReturnReasonMetaInfoModalVisible}
        isBulkRestockingFeeMetaInfoModalVisible={isBulkRestockingFeeMetaInfoModalVisible}
        isCustomerSORDrawerVisible={isCustomerSORDrawerVisible}
        isPartSORPullDrawerVisible={isPartSORPullDrawerVisible}
        isAddPartsToReturnModalVisible={isAddPartsToReturnModalVisible}
        isOemLoginModalVisible={shouldPromptUserForCredentials}
        isMultiPdfPrintListModalVisible={isMultiPdfPrintListModalVisible}
        isMultiVoidListModalVisible={isMultiVoidListModalVisible}
        isRecalculatingOnPriceCodeChange={isRecalculatingOnPriceCodeChange}
        salesOrderInvoiceList={salesOrderInvoiceList}
        salesOrderEntity={frozenSalesOrderEntity}
        isReissuing={isReissuing}
        generateInvoiceTableSelectedRows={generateInvoiceTableSelectedRows}
        checkboxSelectionContext={checkboxSelectionContext}
        resolvedCustomerDetails={resolvedCustomerDetails}
        isSORCancellationDrawerVisible={isSORCancellationDrawerVisible}
        isSubmitConfirmationDrawerVisible={isSubmitConfirmationDrawerVisible}
        isNegativeSaleConfirmationDrawerVisible={isNegativeSaleConfirmationDrawerVisible}
        isFetchingSalesOrderCashieringDetails={isFetchingSalesOrderCashieringDetails}
        salesOrderCashieringDetails={salesOrderCashieringDetails}
        isBulkResolveManualActionDrawerVisible={isBulkResolveManualActionDrawerVisible}
        isUserLevelSettingDrawerVisible={isUserLevelSettingDrawerVisible}
        isSaleTypeChangeConfirmationDialogOpen={isSaleTypeChangeConfirmationDialogOpen}
        isAverageCostPriceChangeReviewDrawerVisible={isAverageCostPriceChangeReviewDrawerVisible}
        isNewTaxUpdateModalVisible={isNewTaxUpdateModalVisible}
        partsSettingsMap={partsSettingsMap}
        isLostSaleDrawerVisible={isLostSaleDrawerVisible}
        routeParams={params}
        isApprovalRequestDrawerVisible={isApprovalRequestDrawerVisible}
        creditLimitApprovalRequestDetails={creditLimitApprovalRequestDetails}
      />
      <PartsOverviewDrawerWrapper {...props} />
      {/* TODO bhavika send only what we need and if more state use *ModalParams */}
      <VoidModalWrapper
        visible={isVoidModalVisible}
        onAction={onAction}
        isSubmitting={submittingVoid}
        voidModalParams={voidModalParams}
      />
      <PriorityCodeSelectionForBulkActionModal
        visible={isPriorityCodeSelectModalVisible}
        onAction={onAction}
        isSubmitting={submittingPriorityCode}
        priorityCodeOptions={priorityCodeOptions}
      />
      <VehicleModal
        visible={vehicleModalVisible}
        onSuccess={handleVehicleModalSuccess}
        onCancel={handleToggleVehicleModal}
      />
      <AddNewPartModalWrapper
        onAction={onAction}
        isAddNewPartModalVisible={isAddNewPartModalVisible}
        addNewPartModalParams={addNewPartModalParams}
        siteId={currentSiteId}
        selectedWarehouse={selectedWarehouse}
      />
      <EtkaCatalogueImportModalWrapper isVisible={isEtkaCatalogueImportModalVisible} onAction={onAction} />
      <MplSelectionModal
        onAction={onAction}
        shouldPromptForMplSelection={shouldPromptForMplSelection}
        mplModalParams={mplModalParams}
        canPromptUserForCredentials={canPromptUserForCredentials}
      />
      <AddPartsToReturnModal
        {...addPartsToReturnModalParams}
        frozenSalesOrderEntity={frozenSalesOrderEntity}
        returnReasons={returnReasons}
        salesOrderReturnsList={salesOrderReturnsList}
        salesOrderInvoiceList={salesOrderInvoiceList}
        currentSiteId={currentSiteId}
        visible={isAddPartsToReturnModalVisible}
        parentOnAction={onAction}
        isSubmitting={isSubmittingAddPartsToReturn}
        transferOnHand={GeneralSettingsReader.transferOnHand(partsSettingsMap.get(currentSiteId))}
      />
      <TaxUpdateModalWrapper
        onAction={onAction}
        isTaxUpdateModalVisible={isTaxUpdateModalVisible}
        taxUpdateModalParams={taxUpdateModalParams}
      />
      {isNewTaxUpdateModalVisible && (
        <NewTaxUpdateModalWrapper
          onAction={onAction}
          isNewTaxUpdateModalVisible={isNewTaxUpdateModalVisible}
          taxUpdateModalV2Params={taxUpdateModalV2Params}
          taxCodeList={taxCodeList}
          DropdownComponent={StaticOptionsAutocomplete}
          getShouldRegisterShortcutKeyForModal={getShouldRegisterShortcutKeyForModal}
          triggerFormSubmitWithBlurActiveElement={triggerFormSubmitWithBlurActiveElement}
          isCustomerTaxExemptedInNewTaxCode={isCustomerTaxExemptedInNewTaxCode}
        />
      )}
      {/* TODO 2 vidit dont we have some functionality like -> show availability to the ones which are selected only? */}
      <PartsAvailabilityDrawer
        isAvailabilityModalVisible={isAvailabilityModalVisible}
        frozenSalesOrderEntity={frozenSalesOrderEntity}
        values={values}
        onAction={onAction}
        checkboxSelectionContext={checkboxSelectionContext}
      />
      <PartsSorReferenceDrawer
        isVisible={isPartSORPullDrawerVisible}
        onHandQtyColumnAction={onAction}
        partsSorPullDrawerProps={partsSorPullDrawerProps}
      />
      <FillPartsDrawerWrapper
        {...fillPartsModalParams}
        frozenSalesOrderEntity={frozenSalesOrderEntity}
        isPrimaryDisabled={isPrimaryDisabled}
        isVisible={isFillPartsDrawerVisible}
        onAction={onAction}
        selectedPartListTabKey={selectedPartListTabKey}
        contextForFormLevelValidation={contextForFormLevelValidation}
      />
      <PropertyControlledComponent controllerProperty={isCustomerSORDrawerVisible}>
        <CustomerSORDrawerWrapper
          onAction={onAction}
          isCustomerSORDrawerVisible={isCustomerSORDrawerVisible}
          customerSORDrawerParams={customerSORDrawerParams}
          isEditable={isFormEditable}
          fractionalSaleUOMValuesById={fractionalSaleUOMValuesById}
          fractionalSaleTemplateById={fractionalSaleTemplateById}
        />
      </PropertyControlledComponent>
      <PartsReturnModal
        {...partsReturnMetaInfoModalParams}
        returnReasons={returnReasons}
        onAction={onAction}
        isVisible={isPartReturnMetaInfoModalVisisble}
        itemList={itemList}
        values={values}
      />
      <BulkReturnReasonModal
        isVisible={isBulkReturnReasonMetaInfoModalVisible}
        returnReasons={returnReasons}
        onAction={onAction}
      />
      <BulkRestockingFeeModal isVisible={isBulkRestockingFeeMetaInfoModalVisible} onAction={onAction} />
      <PartUpdateModal
        isVisible={isUpdatePartRequestModalOpen}
        onAction={onAction}
        partUpdateModalParams={partUpdateModalParams}
      />
      {showPartsCashiering && (
        <PartsCashiering
          onAction={onAction}
          navigate={navigate}
          approvalId={creditLimitApprovalId}
          resolvedCustomerDetails={resolvedCustomerDetails}
          salesOrderCashieringDetails={salesOrderCashieringDetails}
          salesOrderInvoiceList={salesOrderInvoiceList}
          salesOrderReturnsList={salesOrderReturnsList}
          showChargePaymentMethod={showChargePaymentMethod} // TODO: do we need this?
          activeCashieringInvoice={activeCashieringInvoice} // TODO: pass active CM invoice id
          customerUnusedCreditLimitBalance={customerUnusedCreditLimitBalance}
          partsSettingsMap={partsSettingsMap}
          docServicePDFs={docServicePDFs}
          isSilentUpdateInProgress={isSilentUpdateInProgress}
        />
      )}
      <AddCustomerBottomDrawer
        visible={isCustomerAddDrawerVisible}
        onAction={onAction}
        customerName={permanentCustomerName}
        countries={countries}
        states={states}
      />
      {/* TODO yuvi send only what we need and if more state use *ModalParams */}
      <PDFViewerWrapper
        pdfs={pdfItems}
        printerLogIdsMap={printerLogIdsMap}
        emailOverrides={emailOverrides}
        details={pdfDocDetails || frozenSalesOrderEntity}
        assetIdvsWatermarkStatusPDFV2={assetIdvsWatermarkStatusPDFV2}
        currentPdfWaterMarkStatus={currentPdfWaterMarkStatus}
        viewType={viewPdfDocumentType}
        isVisible={isPDFModalVisible}
        onAction={onAction}
        afterPrintPDFCallback={afterPrintPDFCallback}
        isPdfDownloading={isPdfDownloading}
        isPDFDownloadedBySource={isPDFDownloadedBySource}
        isGeneratingHTMLPdf={isGeneratingHTMLPdf}
        shouldEnableHTMLPdf={shouldEnableHTMLPdf}
        additionalActionsForPdfHeader={additionalActionsForPdfHeader}
        lastTriggeredPrintTime={lastTriggeredPrintTime}
        initialSelectedPdfGroupId={initialSelectedPdfGroupId}
        initialSelectedPdfVersion={initialSelectedPdfVersion}
      />
      <PartCommentsModalWrapper
        onAction={onAction}
        partCommentsVisible={partCommentsVisible}
        partCommentsData={partCommentsData}
      />
      <ReplacePartsDrawerWrapper
        onAction={onAction}
        shouldPromptForReplacedParts={shouldPromptForReplacedParts}
        replacePartModalParams={replacePartModalParams}
      />
      <BulkResolveManualActionDrawer
        onParentAction={onAction}
        isVisible={isBulkResolveManualActionDrawerVisible}
        bulkResolveManualActionProps={bulkResolveManualActionProps}
      />
      <CancelConfirmationModal isVisible={isCancelModalVisible} onAction={onAction} />
      <GoBackConfirmationModal
        getDefaultCountryCode={getDefaultCountryCode}
        onAction={onAction}
        isFormEditable={isFormEditable}
        values={values}
        frozenSalesOrderEntity={frozenSalesOrderEntity}
        currentSiteId={currentSiteId}
        resolvedCustomerDetails={resolvedCustomerDetails}
        vehicleList={vehicleList}
        defaultPartSaleTaxDetails={defaultPartSaleTaxDetails}
        isSubmitting={isSubmitting}
        deletedPartList={deletedPartList}
        orderType={orderType}
        partsDepartments={partsDepartments}
        sortDetails={sortDetails}
        isFetchingSO={isFetchingSO}
      />
      <AdvancedSearchDrawerWrapper
        onAction={onAction}
        advancedSearchParams={advancedSearchParams}
        isVisible={isAdvancedSearchVisible}
        manufacturerConfig={manufacturerConfig}
      />
      <OemLoginModal isVisible={shouldPromptUserForCredentials} onAction={onAction} />
      <PassCodeModalWrapper onAction={onAction} visible={shouldPromptForPassCode} />
      <MultiPdfListModal
        isVisible={isMultiPdfPrintListModalVisible}
        onAction={onAction}
        multiPdfListParams={multiPdfListParams}
        checkboxSelectionContext={checkboxSelectionContext}
        frozenSalesOrderEntity={frozenSalesOrderEntity}
      />
      <MultiVoidListModal
        isVisible={isMultiVoidListModalVisible}
        onAction={onAction}
        multiVoidListParams={multiVoidListParams}
        checkboxSelectionContext={checkboxSelectionContext}
        frozenSalesOrderEntity={frozenSalesOrderEntity}
        voidReasons={voidReasons}
      />
      <PriceChangeConfirmationModal
        params={priceChangeConfirmationModalParams}
        visible={isPriceChangeConfirmationModalVisible}
        onAction={onAction}
      />
      <CustomLoader
        isVisible={isRecalculatingOnPriceCodeChange}
        loaderMessage={__('Recalculating selling prices...')}
      />
      <SORCancellationDrawerWrapper
        onAction={onAction}
        isSORCancellationDrawerVisible={isSORCancellationDrawerVisible}
        sorCancellationDrawerParams={sorCancellationDrawerParams}
      />
      <SubmitConfirmationDrawerWrapper
        onAction={onAction}
        isSubmitConfirmationDrawerVisible={isSubmitConfirmationDrawerVisible}
        submitConfirmationDrawerParams={submitConfirmationDrawerParams}
      />
      <NegativeSaleConfirmationDrawerWrapper
        onAction={onAction}
        isVisible={isNegativeSaleConfirmationDrawerVisible}
        drawerParams={negativeSaleConfirmationDrawerParams}
      />
      <UserLevelSettingDrawer
        onAction={onAction}
        isVisible={isUserLevelSettingDrawerVisible}
        userLevelSettingPreferences={userLevelSettingPreferences}
        isSubmittingUserPreferences={isSubmittingUserPreferences}
      />
      <StatusInquiryDrawerWrapper
        isStatusInquiryModalVisible={isStatusInquiryModalVisible}
        frozenSalesOrderEntity={frozenSalesOrderEntity}
        onAction={onAction}
      />
      <PropertyControlledComponent controllerProperty={isAverageCostPriceChangeReviewDrawerVisible}>
        <AverageCostPriceChangeReviewDrawerWrapper
          onAction={onAction}
          orderType={orderType}
          taxDetails={taxDetails}
          currentSiteId={currentSiteId}
          salesOrderFees={salesOrderFees}
          averageCostDataUpdatedAt={averageCostDataUpdatedAt}
          isVisible={isAverageCostPriceChangeReviewDrawerVisible}
          isInventoryAverageCostRequired={isInventoryAverageCostRequired}
          priceChangedPartLineItemList={averageCostChangedPartLineItemList}
          isInventoryAverageCostQueryFetching={isInventoryAverageCostQueryFetching}
          refetchInventoryAverageCostByInventoryIdQuery={refetchInventoryAverageCostByInventoryIdQuery}
        />
      </PropertyControlledComponent>
      <PropertyControlledComponent
        controllerProperty={
          isUpdateSellingPriceMasterDrawerVisible && !SalesOrderDomain.isVoided(frozenSalesOrderEntity)
        }
      >
        <SellingPriceUpdateMasterDrawerWrapper
          onAction={onAction}
          orderType={orderType}
          taxDetails={taxDetails}
          currentSiteId={currentSiteId}
          salesOrderFees={salesOrderFees}
          isVisible={isUpdateSellingPriceMasterDrawerVisible}
          currentPartLineList={_get(values, SALES_ORDER_FORM_KEYS.ITEM_LIST) || []}
          priceChangedPartLineItemList={sellingPriceUpdatedPartsList}
          frozenSalesOrderEntity={frozenSalesOrderEntity}
        />
      </PropertyControlledComponent>
      <CreditLimitInfoDrawer
        onAction={onAction}
        isVisible={isCreditLimitInfoDrawerVisible}
        customerCreditLimitInfo={customerCreditLimitInfo}
      />
      <EmailInvoiceModal
        {...emailInvoiceModalParams}
        visible={isEmailInvoiceModalVisible}
        frozenSalesOrderEntity={frozenSalesOrderEntity}
        resolvedCustomerDetails={resolvedCustomerDetails}
        onAction={onAction}
      />
      <ChorusProInvoicingModal
        visible={_get(chorusProModalParams, 'visible')}
        siretCodeDetails={_get(chorusProModalParams, 'siretCodeDetails')}
        onAction={onAction}
      />
      <PropertyControlledComponent controllerProperty={isLostSaleDrawerVisible}>
        <LostSaleDrawerWrapper
          onAction={onAction}
          isVisible={isLostSaleDrawerVisible}
          lostSaleDrawerParams={lostSaleDrawerParams}
        />
      </PropertyControlledComponent>
      <CustomerNotesModalWrapper
        onAction={onAction}
        resolvedCustomerNotes={resolvedCustomerNotes}
        isNotesModalVisible={isNotesModalVisible}
        customerId={customerId}
        customerName={customerName}
        sourceId={SalesOrderReader.id(frozenSalesOrderEntity)}
        sourceType={PARTS_NOTES_SOURCE_TYPES.SALES_ORDER}
      />
      <PartKitAdditionDrawer
        isVisible={isAddPartKitDrawerVisible}
        onClose={closePartKitAdditionDrawer}
        handleSubmit={submitPartKitAdditionDrawer}
        resolvedData={resolvedPartInfoForAddPartKitDrawer}
      />
      <CoreNotAvailableModalWrapper
        onAction={onAction}
        coresNotAvailableModalParams={coresNotAvailableModalParams}
        isCoresNotAvailableModalVisible={isCoresNotAvailableModalVisible}
      />
      <MinGPSellingPriceDrawer
        isVisible={isMinGPSellingPriceUpdateDrawerVisible}
        minGPSellingPriceUpdateDrawerParams={minGPSellingPriceUpdateDrawerParams}
        onAction={onAction}
        priceCodeId={_head(_get(values, SALES_ORDER_FORM_KEYS.PRICE_CODE))}
        currentSiteId={currentSiteId}
        partsSettingsMap={partsSettingsMap}
      />

      <PropertyControlledComponent controllerProperty={isApprovalRequestDrawerVisible}>
        <ApprovalRequestDrawer
          navigate={navigate}
          closeDrawer={closeApprovalRequestDrawer}
          approvalId={creditLimitApprovalRequestId}
          onRequestApprovalSuccessCb={closeDrawerAndRefetchApprovalRequestDetails}
          onRequestRejectionSuccessCb={closeDrawerAndRefetchApprovalRequestDetails}
          onRequestWithdrawalSuccessCb={closeDrawerAndRefetchApprovalRequestDetails}
        />
      </PropertyControlledComponent>

      <OnPushMessage eventKey={asyncPaymentPusherChannelName} eventHandler={asyncPaymentReceiveHandler} />
      <OnPushMessage eventKey={salesOrderPOCreationUpdateChannelName} eventHandler={poCreationUpdateHandler} />
      <OnPushMessage eventKey={salesOrderReceiveUpdateChannelName} eventHandler={receiveSilentUpdateHandler} />
      <OnPushMessage eventKey={salesOrderPDFGenerationChannelName} eventHandler={fetchPDFupdateHandler} />
      <OnPushMessage eventKey={purchaseOrderPDFGenerationChannelName} eventHandler={fetchPOPdfUpdateHandler} />
      <OnPushMessage eventKey={salesOrderBaseUpdateChannelName} eventHandler={updateSalesOrderBaseStateHandler} />
    </div>
  );
}

PartsSalesOrderDetail.propTypes = {
  onAction: PropTypes.func.isRequired,
  isFetchingSO: PropTypes.bool,
  isFetchingBootstrap: PropTypes.bool,
  addressModalVisible: PropTypes.bool,
  addressModalType: PropTypes.string,
  currentSiteId: PropTypes.string.isRequired,
  soFlagId: PropTypes.string,
  soFlagLabel: PropTypes.string,
  location: PropTypes.object.isRequired,
  frozenSalesOrderEntity: PropTypes.object,
  orderType: PropTypes.string,
  draftedByUserDetails: PropTypes.object,
  invoicedByUserDetails: PropTypes.object,
  createdByUserDetails: PropTypes.object,
  salesOrderCashieringDetails: PropTypes.object,
  values: PropTypes.object,
  countries: PropTypes.array,
  states: PropTypes.object,
  resolvedCustomerDetails: PropTypes.object,
  isCloseViaCODModalOpen: PropTypes.bool,
  isCloseViaCODModalLoading: PropTypes.bool,
  closeViaCODModalParams: PropTypes.object,
  isVoidModalVisible: PropTypes.bool,
  submittingVoid: PropTypes.bool,
  voidModalParams: PropTypes.object,
  pendingPdfRequests: PropTypes.array,
  isPDFModalVisible: PropTypes.bool,
  isChargeAccountModalOpen: PropTypes.bool,
  chargeAccountModalParams: PropTypes.object,
  isChargeAccountModalLoading: PropTypes.bool,
  showPartsCashiering: PropTypes.bool,
  salesOrderInvoiceList: PropTypes.array,
  salesOrderReturnsList: PropTypes.array,
  partsSettingsMap: PropTypes.object,
  showChargePaymentMethod: PropTypes.bool,
  activeCashieringInvoice: PropTypes.string,
  customerUnusedCreditLimitBalance: PropTypes.number,
  isReissuing: PropTypes.bool,
  isReissueDisabled: PropTypes.bool,
  reIssueActionLoading: PropTypes.bool,
  vehicleModalVisible: PropTypes.bool,
  isFetchingCustomerNotes: PropTypes.bool,
  isSubmitting: PropTypes.bool,
  isSubmittingAsDraft: PropTypes.bool,
  isEtkaCatalogueImportModalVisible: PropTypes.bool,
  isAddNewPartModalVisible: PropTypes.bool,
  addNewPartModalParams: PropTypes.object,
  isTaxUpdateModalVisible: PropTypes.bool,
  taxUpdateModalParams: PropTypes.object,

  // create customer drawer
  isCustomerAddDrawerVisible: PropTypes.bool,
  permanentCustomerName: PropTypes.string,
  isAvailabilityModalVisible: PropTypes.bool,
  PartsSorReferenceDrawerProps: PropTypes.object,
  isFillPartsDrawerVisible: PropTypes.bool,
  visible: PropTypes.bool,
  partsSorPullDrawerProps: PropTypes.object,
  isPartSORPullDrawerVisible: PropTypes.bool,
  pdfItems: PropTypes.object,
  emailOverrides: PropTypes.object,
  shouldPromptForReplacedParts: PropTypes.bool,
  replacePartModalParams: PropTypes.object,
  bulkResolveManualActionProps: PropTypes.object,
  isBulkResolveManualActionDrawerVisible: PropTypes.bool,
  isPriorityCodeSelectModalVisible: PropTypes.bool,
  submittingPriorityCode: PropTypes.bool,
  priorityCodeOptions: PropTypes.array,
  isCustomerSORDrawerVisible: PropTypes.bool,
  customerSORDrawerParams: PropTypes.object,
  isCancelModalVisible: PropTypes.bool,
  isKeyboardShortcutDrawerVisible: PropTypes.bool,
  generateInvoiceTableSelectedRows: PropTypes.bool,

  // TODO vidit below add all the remaining modals
  isBulkResolveManualActionModalVisible: PropTypes.bool,
  isShortageSaleModalOpen: PropTypes.bool,
  PartCommentsModalWrapper: PropTypes.bool,
  partCommentsVisible: PropTypes.bool,
  shouldPromptForMplSelection: PropTypes.bool,
  shouldPromptForPassCode: PropTypes.bool,
  isPartsOverviewDrawerVisible: PropTypes.bool,
  isUpdatePartRequestModalOpen: PropTypes.bool,
  isUnSavedChangesWarningModalVisible: PropTypes.bool,
  isCoresNotAvailableModalVisible: PropTypes.bool,
  isPOSShareConfirmationVisible: PropTypes.bool,
  isGrossProfitConfirmationVisible: PropTypes.bool,
  isStatusInquiryModalVisible: PropTypes.bool,
  isPriceChangeConfirmationModalVisible: PropTypes.bool,
  isAdvancedSearchVisible: PropTypes.bool,
  isRecalculatingFeePricing: PropTypes.bool,
  isFetchingFeeInfo: PropTypes.bool,
  vehicleList: PropTypes.array,
  partsDepartments: PropTypes.array,
  deletedPartList: PropTypes.array,
  defaultPartSaleTaxDetails: PropTypes.object,
  advancedSearchParams: PropTypes.object,
  isAddPartsToReturnModalVisible: PropTypes.bool,
  addPartsToReturnModalParams: PropTypes.object,
  isSubmittingAddPartsToReturn: PropTypes.bool,
  isPartReturnMetaInfoModalVisisble: PropTypes.bool,
  partsReturnMetaInfoModalParams: PropTypes.object,
  isBulkReturnReasonMetaInfoModalVisible: PropTypes.bool,
  isBulkRestockingFeeMetaInfoModalVisible: PropTypes.bool,
  posSessionInfo: PropTypes.object,
  temporaryOrderNoForPOSInCreateMode: PropTypes.string,
  isFetchingPOSSessionInfo: PropTypes.bool,
  shouldPromptUserForCredentials: PropTypes.bool,
  isFormLockedByPassCode: PropTypes.bool,
  isMultiPdfPrintListModalVisible: PropTypes.bool,
  multiPdfListParams: PropTypes.object,
  checkboxSelectionContext: PropTypes.object,
  priceChangeConfirmationModalParams: PropTypes.object,
  isRecalculatingOnPriceCodeChange: PropTypes.bool,
  isFetchingSalesOrderCashieringDetails: PropTypes.bool,
  sorCancellationDrawerParams: PropTypes.object,
  isSORCancellationDrawerVisible: PropTypes.bool,
  partUpdateModalParams: PropTypes.object,
  resolvedColumnPreferences: PropTypes.object,
  isSubmitConfirmationDrawerVisible: PropTypes.bool,
  submitConfirmationDrawerParams: PropTypes.object,
  returnReasons: PropTypes.array,
  isNegativeSaleConfirmationDrawerVisible: PropTypes.bool,
  negativeSaleConfirmationDrawerParams: PropTypes.object,
  isFetchFailed: PropTypes.bool,
  error: PropTypes.object,
  isBootstrapFetchFailed: PropTypes.bool,
  fillPartsModalParams: PropTypes.object,
  selectedPartListTabKey: PropTypes.string,
  showPrintOptionForUser: PropTypes.bool,
  resolvedPartsFeeList: PropTypes.array,
  resolvedPartsCouponList: PropTypes.array,
  partSaleTaxDetails: PropTypes.array,
  taxCodeDetailsList: PropTypes.array,
  taxCodeOverrides: PropTypes.array,
  userLevelSettingPreferences: PropTypes.object,
  isSaleTypeChangeConfirmationDialogOpen: PropTypes.bool,
  isAverageCostPriceChangeReviewDrawerVisible: PropTypes.bool,
  averageCostChangedPartLineItemList: PropTypes.array,
  isAverageCostPriceChangeSubmitting: PropTypes.bool,
  customerCreditLimitInfo: PropTypes.object,
  isCreditLimitInfoDrawerVisible: PropTypes.bool,
  getDefaultCountryCode: PropTypes.func.isRequired,
  afterPrintPDFCallback: PropTypes.func,
  isAverageCostUpdated: PropTypes.bool,
  isPDFDownloadedBySource: PropTypes.bool,
  viewPdfDocumentType: PropTypes.string,
  pdfDocDetails: PropTypes.object,
  isGeneratingHTMLPdf: PropTypes.bool,
  isFetchingHtmlPdfConfiguration: PropTypes.bool,
  shouldEnableHTMLPdf: PropTypes.bool,
  additionalActionsForPdfHeader: PropTypes.array,
  initialSelectedPdfGroupId: PropTypes.string,
  initialSelectedPdfVersion: PropTypes.number,
  lastTriggeredPrintTime: PropTypes.number,
  assetIdvsWatermarkStatusPDFV2: PropTypes.object,
  currentPdfWaterMarkStatus: PropTypes.bool,
  closeSOInProgress: PropTypes.bool,
  isNewTaxUpdateModalVisible: PropTypes.bool,
  taxUpdateModalV2Params: PropTypes.object,
  taxCodeList: PropTypes.array,
  mplModalParams: PropTypes.object,
  canPromptUserForCredentials: PropTypes.bool,
  resolvedInfoByPartIdentifierId: PropTypes.object,
  partCommentsData: PropTypes.object,
  isPOSDropdownVisible: PropTypes.bool,
  isMultiVoidListModalVisible: PropTypes.bool,
  multiVoidListParams: PropTypes.object,
  voidReasons: PropTypes.array,
  tertiaryButtonToolTipTitle: PropTypes.string,
  primaryButtonToolTipTitle: PropTypes.string,
  tertiaryButtonLabel: PropTypes.string,
  primaryButtonLabel: PropTypes.string,
  displayWarningAlongsidePrimaryButtonLabel: PropTypes.string,
  additionalButtonLabel: PropTypes.string,
  navigationContext: PropTypes.object,
  isFetchingSalesOrderInvoiceList: PropTypes.bool,
  isFetchingSalesOrderReturnList: PropTypes.bool,
  errors: PropTypes.object,
  currentUserDetails: PropTypes.object,
  tableDrillDownFilterContext: PropTypes.object,
  isFetchingCustomerCreditLimit: PropTypes.bool,

  resolvedCoreDetailByPartId: PropTypes.object,
  isUserLevelSettingDrawerVisible: PropTypes.bool,
  isSubmittingUserPreferences: PropTypes.bool,
  sortDetails: PropTypes.object,

  isEmailInvoiceModalVisible: PropTypes.bool,
  emailInvoiceModalParams: PropTypes.object,
  isSilentUpdateInProgress: PropTypes.bool,
  isPdfDownloading: PropTypes.bool,
  isLostSaleDrawerVisible: PropTypes.bool,
  lostSaleDrawerParams: PropTypes.object,
  activePOModalType: PropTypes.string,
  isVendorSpecialPOSubmitting: PropTypes.bool,
  vendorSpecialPOPartsList: PropTypes.array,
  oemSpecialPOPartsList: PropTypes.array,
  vendorSpecialPOReissueParams: PropTypes.object,
  isOEMSpecialPOSubmitting: PropTypes.bool,
  oemSpecialPOReissueParams: PropTypes.object,
  isMiscellaneousPOSubmitting: PropTypes.bool,
  miscellaneousPOPartsList: PropTypes.array,
  miscellaneousPOReissueParams: PropTypes.object,
  chorusProModalParams: PropTypes.array,
  docServicePDFs: PropTypes.array,
  isFetchingCouponInfo: PropTypes.bool,
  isRecalculatingCouponPricing: PropTypes.bool,
  manufacturerConfig: PropTypes.array,
  partKitInfoToResolve: PropTypes.object,
  isAddPartKitDrawerVisible: PropTypes.bool,
  fractionalSaleUOMValuesById: PropTypes.object,
  fractionalSaleTemplateById: PropTypes.object,
  isFetchPurchaseOrderDetailsTaskDone: PropTypes.bool,
  isFractionalSaleUOMListAndTemplatesFetched: PropTypes.bool,
  isCustomerTaxExemptedInNewTaxCode: PropTypes.bool,
  isMinGPSellingPriceUpdateDrawerVisible: PropTypes.bool,
  minGPSellingPriceUpdateDrawerParams: PropTypes.object,
  resolvedPriceCodeDetails: PropTypes.object,
  isApprovalRequestDrawerVisible: PropTypes.bool,
  creditLimitApprovalRequestId: PropTypes.string,
  creditLimitApprovalRequestDetails: PropTypes.object,
  selectedWarehouse: PropTypes.string,
};

PartsSalesOrderDetail.defaultProps = {
  isFetchingSO: false,
  isFetchingBootstrap: true,
  addressModalVisible: false,
  addressModalType: '',
  soFlagId: EMPTY_STRING,
  soFlagLabel: EMPTY_STRING,
  orderType: EMPTY_STRING,
  frozenSalesOrderEntity: null,
  draftedByUserDetails: EMPTY_OBJECT,
  invoicedByUserDetails: EMPTY_OBJECT,
  createdByUserDetails: EMPTY_OBJECT,
  salesOrderCashieringDetails: EMPTY_OBJECT,
  values: EMPTY_OBJECT,
  countries: EMPTY_ARRAY,
  states: EMPTY_OBJECT,
  resolvedCustomerDetails: EMPTY_OBJECT,
  isCloseViaCODModalOpen: false,
  isCloseViaCODModalLoading: false,
  closeViaCODModalParams: EMPTY_OBJECT,
  isVoidModalVisible: false,
  submittingVoid: false,
  voidModalParams: EMPTY_OBJECT,
  pendingPdfRequests: EMPTY_ARRAY,
  isPDFModalVisible: false,
  isChargeAccountModalOpen: false,
  chargeAccountModalParams: EMPTY_OBJECT,
  isChargeAccountModalLoading: false,
  showPartsCashiering: false,
  salesOrderInvoiceList: EMPTY_ARRAY,
  salesOrderReturnsList: EMPTY_ARRAY,
  partsSettingsMap: IMap(),
  showChargePaymentMethod: false,
  activeCashieringInvoice: null,
  customerUnusedCreditLimitBalance: 0,
  isReissuing: false,
  isReissueDisabled: false,
  reIssueActionLoading: false,
  vehicleModalVisible: false,
  isFetchingCustomerNotes: false,
  isSubmitting: false,
  isSubmittingAsDraft: false,
  isEtkaCatalogueImportModalVisible: false,
  isAddNewPartModalVisible: false,
  addNewPartModalParams: EMPTY_OBJECT,
  isTaxUpdateModalVisible: false,
  taxUpdateModalParams: EMPTY_OBJECT,

  // create customer drawer
  isCustomerAddDrawerVisible: false,
  permanentCustomerName: '',
  isAvailabilityModalVisible: false,
  PartsSorReferenceDrawerProps: EMPTY_OBJECT,
  isFillPartsDrawerVisible: false,
  visible: false,
  partsSorPullDrawerProps: EMPTY_OBJECT,
  isPartSORPullDrawerVisible: false,
  emailOverrides: EMPTY_OBJECT,
  pdfItems: EMPTY_OBJECT,
  shouldPromptForReplacedParts: false,
  replacePartModalParams: EMPTY_OBJECT,
  bulkResolveManualActionProps: EMPTY_OBJECT,
  isBulkResolveManualActionDrawerVisible: false,
  isPriorityCodeSelectModalVisible: false,
  submittingPriorityCode: false,
  priorityCodeOptions: EMPTY_ARRAY,
  isCustomerSORDrawerVisible: false,
  customerSORDrawerParams: EMPTY_OBJECT,
  isCancelModalVisible: false,
  isKeyboardShortcutDrawerVisible: false,
  generateInvoiceTableSelectedRows: false,
  isFetchingSalesOrderCashieringDetails: true,

  // TODO vidit below add all the remaining modals
  isBulkResolveManualActionModalVisible: false,
  isShortageSaleModalOpen: false,
  PartCommentsModalWrapper: false,
  partCommentsVisible: false,
  shouldPromptForMplSelection: false,
  shouldPromptForPassCode: false,
  isPartsOverviewDrawerVisible: false,
  isUpdatePartRequestModalOpen: false,
  isUnSavedChangesWarningModalVisible: false,
  isCoresNotAvailableModalVisible: false,
  isPOSShareConfirmationVisible: false,
  isGrossProfitConfirmationVisible: false,
  isStatusInquiryModalVisible: false,
  isPriceChangeConfirmationModalVisible: false,
  isAdvancedSearchVisible: false,
  isRecalculatingFeePricing: false,
  isFetchingFeeInfo: false,
  vehicleList: EMPTY_ARRAY,
  partsDepartments: EMPTY_ARRAY,
  deletedPartList: EMPTY_ARRAY,
  defaultPartSaleTaxDetails: EMPTY_OBJECT,
  advancedSearchParams: EMPTY_OBJECT,
  isAddPartsToReturnModalVisible: false,
  addPartsToReturnModalParams: EMPTY_OBJECT,
  isSubmittingAddPartsToReturn: false,
  isPartReturnMetaInfoModalVisisble: false,
  partsReturnMetaInfoModalParams: EMPTY_OBJECT,
  isBulkReturnReasonMetaInfoModalVisible: false,
  isBulkRestockingFeeMetaInfoModalVisible: false,
  posSessionInfo: EMPTY_OBJECT,
  temporaryOrderNoForPOSInCreateMode: EMPTY_STRING,
  isFetchingPOSSessionInfo: false,
  shouldPromptUserForCredentials: false,
  isFormLockedByPassCode: false,
  isMultiPdfPrintListModalVisible: false,
  multiPdfListParams: EMPTY_OBJECT,
  checkboxSelectionContext: EMPTY_OBJECT,
  priceChangeConfirmationModalParams: EMPTY_OBJECT,
  isRecalculatingOnPriceCodeChange: false,
  sorCancellationDrawerParams: EMPTY_OBJECT,
  isSORCancellationDrawerVisible: false,
  partUpdateModalParams: EMPTY_OBJECT,
  resolvedColumnPreferences: EMPTY_OBJECT,
  isSubmitConfirmationDrawerVisible: false,
  submitConfirmationDrawerParams: EMPTY_OBJECT,
  returnReasons: EMPTY_ARRAY,
  isNegativeSaleConfirmationDrawerVisible: false,
  negativeSaleConfirmationDrawerParams: EMPTY_OBJECT,
  isFetchFailed: false,
  error: EMPTY_OBJECT,
  isBootstrapFetchFailed: false,
  fillPartsModalParams: EMPTY_OBJECT,
  selectedPartListTabKey: PART_LIST_TABLE_TAB_KEY.TO_BE_FILLED,
  showPrintOptionForUser: false,
  resolvedPartsFeeList: EMPTY_ARRAY,
  resolvedPartsCouponList: EMPTY_ARRAY,
  partSaleTaxDetails: EMPTY_ARRAY,
  taxCodeDetailsList: EMPTY_ARRAY,
  taxCodeOverrides: EMPTY_ARRAY,
  userLevelSettingPreferences: EMPTY_OBJECT,
  isSaleTypeChangeConfirmationDialogOpen: false,
  isAverageCostPriceChangeReviewDrawerVisible: false,
  averageCostChangedPartLineItemList: EMPTY_ARRAY,
  isAverageCostPriceChangeSubmitting: false,
  customerCreditLimitInfo: EMPTY_OBJECT,
  isCreditLimitInfoDrawerVisible: false,
  afterPrintPDFCallback: _noop,
  isAverageCostUpdated: false,
  isPDFDownloadedBySource: false,
  viewPdfDocumentType: null,
  pdfDocDetails: null,
  isGeneratingHTMLPdf: true,
  initialSelectedPdfGroupId: EMPTY_STRING,
  initialSelectedPdfVersion: -1,
  lastTriggeredPrintTime: 0,
  assetIdvsWatermarkStatusPDFV2: EMPTY_OBJECT,
  currentPdfWaterMarkStatus: true,
  isFetchingHtmlPdfConfiguration: false,
  shouldEnableHTMLPdf: false,
  additionalActionsForPdfHeader: EMPTY_ARRAY,
  closeSOInProgress: false,
  isNewTaxUpdateModalVisible: false,
  taxUpdateModalV2Params: EMPTY_OBJECT,
  taxCodeList: EMPTY_ARRAY,
  mplModalParams: EMPTY_OBJECT,
  canPromptUserForCredentials: false,
  resolvedInfoByPartIdentifierId: EMPTY_OBJECT,
  partCommentsData: EMPTY_OBJECT,
  isPOSDropdownVisible: false,
  isMultiVoidListModalVisible: false,
  multiVoidListParams: EMPTY_OBJECT,
  voidReasons: EMPTY_ARRAY,
  tertiaryButtonToolTipTitle: EMPTY_STRING,
  primaryButtonToolTipTitle: EMPTY_STRING,
  tertiaryButtonLabel: __('Save As Quote'),
  primaryButtonLabel: __('Create Sales Order'),
  displayWarningAlongsidePrimaryButtonLabel: EMPTY_STRING,
  additionalButtonLabel: EMPTY_STRING,
  navigationContext: EMPTY_OBJECT,
  isFetchingSalesOrderInvoiceList: false,
  isFetchingSalesOrderReturnList: false,
  errors: EMPTY_OBJECT,
  currentUserDetails: EMPTY_OBJECT,
  tableDrillDownFilterContext: EMPTY_OBJECT,
  isFetchingCustomerCreditLimit: false,
  resolvedCoreDetailByPartId: EMPTY_OBJECT,
  isUserLevelSettingDrawerVisible: false,
  isSubmittingUserPreferences: false,
  sortDetails: EMPTY_OBJECT,
  isEmailInvoiceModalVisible: false,
  emailInvoiceModalParams: EMPTY_OBJECT,
  isSilentUpdateInProgress: false,
  isPdfDownloading: false,
  isLostSaleDrawerVisible: false,
  lostSaleDrawerParams: EMPTY_OBJECT,
  activePOModalType: EMPTY_STRING,
  isVendorSpecialPOSubmitting: false,
  vendorSpecialPOPartsList: EMPTY_ARRAY,
  oemSpecialPOPartsList: EMPTY_ARRAY,
  vendorSpecialPOReissueParams: EMPTY_OBJECT,
  isOEMSpecialPOSubmitting: false,
  oemSpecialPOReissueParams: EMPTY_OBJECT,
  isMiscellaneousPOSubmitting: false,
  miscellaneousPOPartsList: EMPTY_ARRAY,
  miscellaneousPOReissueParams: EMPTY_OBJECT,
  chorusProModalParams: EMPTY_OBJECT,
  docServicePDFs: EMPTY_ARRAY,
  isFetchingCouponInfo: false,
  isRecalculatingCouponPricing: false,
  manufacturerConfig: EMPTY_ARRAY,
  partKitInfoToResolve: EMPTY_OBJECT,
  isAddPartKitDrawerVisible: false,
  fractionalSaleUOMValuesById: EMPTY_OBJECT,
  fractionalSaleTemplateById: EMPTY_OBJECT,
  isFetchPurchaseOrderDetailsTaskDone: false,
  isFractionalSaleUOMListAndTemplatesFetched: false,
  isCustomerTaxExemptedInNewTaxCode: false,
  isMinGPSellingPriceUpdateDrawerVisible: false,
  minGPSellingPriceUpdateDrawerParams: EMPTY_OBJECT,
  resolvedPriceCodeDetails: EMPTY_OBJECT,
  isEPCDrawerVisible: false,
  isApprovalRequestDrawerVisible: false,
  creditLimitApprovalRequestId: EMPTY_STRING,
  creditLimitApprovalRequestDetails: EMPTY_OBJECT,
  selectedWarehouse: EMPTY_STRING,
};

const mapStateToProps = state => ({
  priorityCodes: getPriorityCodes(state),
  resolvedSourceCodeById: getSourceCodesByIds(state),
  shouldDrivePartTaxableFromSourceCode: true,
  costCenters: getCostCenters(state),
  showPrintOptionForUser: getPrintModalPreference(state),
  manufacturerConfig: getManufacturerConfig(state),
});

export default compose(
  withRouter,
  withTekionConversion,
  connect(mapStateToProps),
  withActions(INITIAL_STATE, ACTION_HANDLERS),
  withCashieringRemoteLoader,
)(PartsSalesOrderDetail);
