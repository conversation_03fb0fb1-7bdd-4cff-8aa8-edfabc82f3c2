import React from 'react';

import _get from 'lodash/get';
import _map from 'lodash/map';
import _noop from 'lodash/noop';
import _without from 'lodash/without';

import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import PDF_HEADER_ACTION_TYPES from '@tekion/tekion-components/src/organisms/PDFRenderer/components/pdfHeader/pdfHeader.actionTypes';
import { ACTION_PROPERTIES } from '@tekion/tekion-components/src/organisms/PDFRenderer/components/pdfHeader/pdfHeader.config';

import { localPrintPdfFromBlobUrl } from '@tekion/tekion-widgets/src/appServices/parts/Pdf/helper';
import PDFHeader from '@tekion/tekion-widgets/src/appServices/parts/PdfHeaderWithCustomEmailSender';

import { usePartsSettingsQuery } from 'queries/settings';

import generalSettingsReader from 'readers/generalSettings.reader';

import HtmlPDFComponent from 'shared/pdf/components/HtmlPDFComponent';
import { ADDITIONAL_HTML_PDF_HEADER_ACTION } from 'shared/pdf/components/HtmlPDFComponent/htmlPDFComponent.constants';
import { formatPDFHeaderActionsV1 } from 'shared/pdf/helper';

import { CURRENT_MEDIA } from './constants';

const BACKEND_PDF_FORMAT_ACTIONS = {
  [PDF_HEADER_ACTION_TYPES.LOCAL_PRINT_PDF_DOCUMENT]: PDF_HEADER_ACTION_TYPES.LOCAL_PRINT_BACKEND_PDF_DOCUMENT,
};

const formatBackendPdfActions = actions =>
  _map(actions, actionDetails => ({
    ...actionDetails,
    [ACTION_PROPERTIES.ACTION_TYPE]: BACKEND_PDF_FORMAT_ACTIONS[actionDetails?.actionType] || actionDetails?.actionType,
  }));

export function usePdfPreview(promiseFn, options = {}) {
  const [status, setStatus] = React.useState(usePdfPreview.TYPES.IDLE);
  const [selectedMediaId, setSelectedMediaId] = React.useState(null);

  const { siteId } = options;

  const { data: isLocalPdfPrintEnabled } = usePartsSettingsQuery(siteId, {
    select: React.useCallback(result => generalSettingsReader.localPdfPrintEnabled(result), []),
    staleTime: 1000 * 60 * 15, // 15 mins
  });

  const isLoading = status === usePdfPreview.TYPES.LOADING;
  const isReady = status === usePdfPreview.TYPES.SUCCESS;
  const isVisible = Boolean(selectedMediaId);

  const pdfActionsRef = React.useRef();
  const promiseRef = React.useRef();
  const documentRef = React.useRef();

  promiseRef.current = promiseFn;

  const reset = React.useCallback(() => setStatus(usePdfPreview.TYPES.IDLE), []);

  const openPdfViewer = React.useCallback((mediaId = CURRENT_MEDIA.mediaId) => {
    setSelectedMediaId(mediaId);
  }, []);

  const closePdfViewer = React.useCallback(() => setSelectedMediaId(null), []);

  const handlePdfHeaderActions = React.useCallback(
    ({ params: { pdfActionType }, getState }) => {
      const { details, isFetchingPdf } = getState();

      if (pdfActionType === PDF_HEADER_ACTION_TYPES.ASYNC_PRINT_PDF_DOCUMENT) {
        const printPicklist = options.handlePrintPicklist ?? _noop;
        printPicklist();
      }

      // restricting the download when the data is in loading state.
      if (!isLoading && pdfActionType === PDF_HEADER_ACTION_TYPES.ASYNC_DOWNLOAD_PDF_DOCUMENT) {
        pdfActionsRef.current.downloadPdf(details);
      }

      // restricting the print when the data is in loading state.
      if (!isLoading && pdfActionType === PDF_HEADER_ACTION_TYPES.LOCAL_PRINT_HTML_PDF_DOCUMENT) {
        pdfActionsRef.current.printPdf(details);
      }

      // When the user selects a version of the pdf and the mediaId is being signed and the pdf content
      // is being downloaded the isFetchingPdf property's value would be true and restricting the print
      // when the data is in loading state.
      if (!isFetchingPdf && pdfActionType === PDF_HEADER_ACTION_TYPES.LOCAL_PRINT_BACKEND_PDF_DOCUMENT) {
        const documentInfo = documentRef.current;

        if (!documentInfo) {
          toaster(TOASTER_TYPE.ERROR, __('Something went wrong. Please check again in a little while.'));
          return;
        }

        documentInfo.getData().then(data => {
          const blob = new Blob([data], { type: 'application/pdf' });
          const blobUrl = URL.createObjectURL(blob);
          localPrintPdfFromBlobUrl(blobUrl);
        });
      }
    },
    [options.handlePrintPicklist, isLoading],
  );

  const getIsSelectedMediaCurrent = React.useCallback(selectedMedia => selectedMedia.id === CURRENT_MEDIA.mediaId, []);

  const getPdfViewerProps = (restProps = {}) => ({
    onCancel: closePdfViewer,
    shouldRenderCustomPDFBody: ({ selectedMedia }) => getIsSelectedMediaCurrent(selectedMedia),
    getValidMediaIds: mediaIds => _without(mediaIds, CURRENT_MEDIA.mediaId),
    initialSelectedMediaId: selectedMediaId,
    onPdfHeaderAction: handlePdfHeaderActions,
    onDocumentLoadSuccess(documentInfo) {
      documentRef.current = documentInfo;
    },
    getCustomPDFBodyRenderer: htmlPdfProps => (
      <HtmlPDFComponent {...htmlPdfProps} isGeneratingHTMLPdf={isLoading} ref={pdfActionsRef} />
    ),
    renderHeader: pdfHeaderProps => {
      const isHTMLPDF = _get(pdfHeaderProps, 'pdfType') === 'HTML';
      const isSelectedMediaCurrent = getIsSelectedMediaCurrent(_get(pdfHeaderProps, 'selectedMedia'));

      return (
        <PDFHeader
          {...pdfHeaderProps}
          additionalActions={isLocalPdfPrintEnabled ? ADDITIONAL_HTML_PDF_HEADER_ACTION : []}
          formatActions={
            isSelectedMediaCurrent || isHTMLPDF ? formatPDFHeaderActionsV1(false) : formatBackendPdfActions
          }
        />
      );
    },
    ...restProps,
  });

  React.useEffect(() => {
    if (isVisible) {
      const run = async () => {
        setStatus(usePdfPreview.TYPES.LOADING);

        try {
          const resolveData = promiseRef.current;
          const payload = await resolveData();
          window.getPdfsToBeGeneratedInfo = () => Promise.resolve({ variants: [{ ...payload, isHtmlViewPdf: true }] });
          setStatus(usePdfPreview.TYPES.SUCCESS);
        } catch (error) {
          setStatus(usePdfPreview.TYPES.ERROR);
        }
      };

      run();
    }

    return () => {
      if (isVisible) {
        delete window.getPdfsToBeGeneratedInfo;
      }
    };
  }, [isVisible]);

  return {
    isLoading,
    isReady,
    isVisible,
    reset,
    openPdfViewer,
    getPdfViewerProps,
    selectedMediaId,
  };
}

usePdfPreview.TYPES = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
};
