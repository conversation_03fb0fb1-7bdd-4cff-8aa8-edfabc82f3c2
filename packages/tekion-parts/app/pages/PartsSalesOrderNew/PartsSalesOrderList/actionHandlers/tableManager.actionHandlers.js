import _castArray from 'lodash/castArray';
import _filter from 'lodash/filter';
import _find from 'lodash/find';
import _get from 'lodash/get';
import _identity from 'lodash/identity';
import _includes from 'lodash/includes';
import _isBoolean from 'lodash/isBoolean';
import _isEmpty from 'lodash/isEmpty';
import _isFunction from 'lodash/isFunction';
import _isNil from 'lodash/isNil';
import _keys from 'lodash/keys';
import _map from 'lodash/map';
import _omit from 'lodash/omit';
import _pick from 'lodash/pick';
import _reduce from 'lodash/reduce';
import _size from 'lodash/size';
import _some from 'lodash/some';
import _throttle from 'lodash/throttle';
import _toString from 'lodash/toString';

import { Map as IMap } from 'immutable';
import { compose } from 'recompose';

import { getSetAdditionalDataAction } from '@tekion/tekion-base/actionCreators/tableItems';
import { EMPTY_OBJECT, EMPTY_ARRAY, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { FILE_EXTENSIONS } from '@tekion/tekion-base/constants/downloadFileType';
import { downloadFile, tget } from '@tekion/tekion-base/utils/general';

import { getSanitizedFiltersWithSite } from '@tekion/tekion-business/src/appServices/parts/helpers/dealerSiteFilter.helpers';
import { getShouldSetInitialFilterGroupVariant } from '@tekion/tekion-business/src/appServices/parts/helpers/general.helpers';
import { getSelectedFiltersWithWarehouse } from '@tekion/tekion-business/src/appServices/parts/helpers/warehouseManagement.helpers';
import SalesOrderListViewItemReader from '@tekion/tekion-business/src/appServices/parts/salesOrder/readers/SalesOrderListViewItem.reader';
import SalesOrderPartLineItemReader from '@tekion/tekion-business/src/appServices/parts/salesOrder/readers/SalesOrderPartLineItem.reader';
import { getReturnReasons } from '@tekion/tekion-business/src/appServices/parts/services/returnReasonService';

import { getSignedURLsForV3Service } from '@tekion/tekion-components/src/actions/uploadAction';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import { ASSET_TYPE } from '@tekion/tekion-components/src/organisms/PdfViewerPartsV2/PdfViewer.constants';

import {
  BULK_ACTION_DOCUMENTS,
  BULK_ACTIONS_REQUEST_TYPES,
} from '@tekion/tekion-widgets/src/appServices/parts/constants/bulkActionConstants';
import { QUICK_FILTERS_ACTION_TYPE } from '@tekion/tekion-widgets/src/organisms/QuickFilters/QuickFilters.constants';
import { TABLE_ACTION_TYPES } from '@tekion/tekion-widgets/src/organisms/tableManager';

import { getCurrentUser } from 'utils';

import { GENERAL_ACTIONS, MAX_EXCEL_DOWNLOADABLE_LIST_COUNT } from 'constants/general.constants';

import { getActionButtonConfig, getBulkNotificationText } from 'helpers/bulkActionNotifications.helpers';
import {
  getPartIdentifierId,
  getResolvedPartDetailsByPartIdentifierId,
} from 'helpers/multiWarehouse/partResolve.helpers';
import { getPartSearchV2RequestPayload } from 'helpers/requestBuilders/part';
import { getFilterActionPayload } from 'helpers/tableGenerator.filter.actionhandlers';

import { BULK_ACTION_ASSETS } from 'organisms/SOBulkActions/constants/bulkActions.constants';
import { formatErrorMessage } from 'organisms/SOBulkActions/helpers/bulkActionApi.helpers';
import { getBulkPayload, removeSalesOrderAssets } from 'organisms/SOBulkActions/helpers/bulkPrintAction.helpers';

import {
  bulkActionFetchMetadata,
  bulkActionTriggerRequest,
  bulkActionFetchRequestData,
} from 'services/bulkActionServices';
import { withPartSearchV2 } from 'services/partsService';

import { getSearchableFields, handleTriggerSearchInput } from 'shared/general/tablemanager/helper';
import { getSalesOrderCreateRoute, getCreditMemoCreateRoute } from 'shared/SalesOrder/partsSalesOrder.routes.helpers';
import { CURRENT_SO_PDF_COPY_TYPES } from 'shared/SalesOrder/salesOrderPdfVersioning/salesOrderPdfVersioning.constants';

import { getIsMultiOemSwitchEnabled, getIsPartCrossSiteAccessEnabled, getIsMultiWarehouseEnabled } from 'utils/general';
import partsEnv from 'utils/partsEnv';
import { handleExcelDownload } from 'utils/tableManager.utils.v2';

import SalesOrderDomain from '../../domain/SalesOrder.domain';
import SalesOrderPartLineItemDomain from '../../domain/SalesOrderPartLineItem.domain';
import { fetchPartSettingsAction } from '../../partsSalesOrder.settings.actions';
import {
  BULK_ACTION_KEYS,
  BULK_ACTION_KEY_VS_DOWNLOAD_BULK_ACTION_DOCUMENT,
  BULK_ACTION_KEY_VS_DOWNLOAD_TOASTER_MESSAGE,
  BULK_ACTION_NOTIFICATION_STATUS_VS_COLOR_CONFIG,
} from '../constants/bulkActions.constants';
import {
  ITEM_TYPE,
  CUSTOM_ACTIONS,
  DEFAULT_PSO_DOWNLOAD_DETAILS,
  DEFAULT_SORTING,
  PSO_TABLE_ID,
} from '../constants/general.constants';
import { SEARCH_FIELD_TYPE, SEARCH_FIELD_TYPE_VS_OVERRIDE_VALUE } from '../constants/search.constant';
import {
  filterSalesOrderForBulkInvoiceGeneration,
  getBulkActionNotificationHandlerTypeKey,
  getBulkDownloadInvoiceLevelPayload,
  getBulkInvoiceCreationPayload,
  getMetaDataAPIPayload,
  getParsedMetadataForBulkRequest,
  updateBulkActionArray,
} from '../helpers/bulkActions.helpers';
import {
  getQuickFilterProps,
  getFacetSearchRequest,
  getDefaultFilters,
  getDetailsRoute,
  scrollToTop,
  getInitialTableFilters,
  changeExistOperatorsToBooleanValue,
  createPayloadForMarkingCustomerReceived,
} from '../helpers/general.helper';
import {
  fetchSalesOrderList,
  pollSalesOrderList,
  changePage,
  fetchAndDownloadSalesOrder,
  markCustomerReceivedAction,
} from '../partsSaleOrderList.action';

export const handleTableItemsRefetch = ({ payload }, { dispatch, getState }) => {
  const { searchQuery, sortDetails, filterValue, rows, selectedFilters, additional, searchField } = getState();
  const { multiSearchSelectedItems, expandedRowDetails, resolvedPartDetailsByIdentifierId, reasonsList } = additional;
  const currentPage = _get(payload, 'currentPage', 0);
  compose(
    dispatch,
    pollSalesOrderList,
  )({
    searchQuery,
    searchText: searchQuery,
    searchField,
    currentPage: currentPage >= 0 ? currentPage : 0,
    filterValue,
    rows,
    selectedFilters,
    sortDetails,
    multiSearchSelectedItems,
    expandedRowDetails,
    resolvedPartDetailsByIdentifierId,
    reasonsList,
    ...getQuickFilterProps(additional),
  });
};

const updateQuickFilters = ({ payload }, { dispatch, getState }) => {
  const { quickFilters } = payload;
  const { additional } = getState();

  compose(dispatch, getSetAdditionalDataAction)(ITEM_TYPE, {
    additional: { ...additional, quickFilters },
  });
};

const handleToggleExpandRow = async ({ payload }, { dispatch, getState }) => {
  const { additional, salesOrders } = getState();
  const { resolvedPartDetailsByIdentifierId = {}, reasonsList, partsSettingsMap = IMap() } = additional;
  let updatedReasonsList = reasonsList;
  let updatedResolvedPartDetailsByIdentifierId = resolvedPartDetailsByIdentifierId;
  let updatedPartsSettingsMap = partsSettingsMap;
  const currentExpandedRowDetails = _get(additional, 'expandedRowDetails', EMPTY_OBJECT);
  const id = _get(payload, 'id');
  const isExpanded = _get(payload, 'isExpanded', false);
  const rowData = _filter(salesOrders, partData => partData?.id === id);
  const siteId = SalesOrderListViewItemReader.siteId(rowData[0]);
  const partSaleDetails = SalesOrderListViewItemReader.partSaleDetails(rowData[0]);
  const migratedPartReturn = SalesOrderListViewItemReader.creditPartList(rowData[0]);
  const isSalesOrder = SalesOrderDomain.isSalesOrder(rowData[0]);
  const partDetailsToResolveByPartIdentifierId = _reduce(
    isSalesOrder ? partSaleDetails : migratedPartReturn,
    (acc, partData) => {
      const partIdentifierId = getPartIdentifierId({ partDetails: partData });
      return {
        ...acc,
        [partIdentifierId]: partData,
      };
    },
    {},
  );

  _map(isSalesOrder ? partSaleDetails : migratedPartReturn, partData => partData);
  if (_isEmpty(partsSettingsMap.get(siteId))) {
    const partSettingsData = await fetchPartSettingsAction({ partsSettingsMap, siteId });
    updatedPartsSettingsMap = partsSettingsMap.set(siteId, partSettingsData);
  }
  if (_isEmpty(reasonsList) && !isSalesOrder) {
    const reasonResponse = await getReturnReasons();
    const reasons = tget(reasonResponse, 'data.data', EMPTY_ARRAY);
    updatedReasonsList = reasons;
  }
  const { partIdsToResolve, warehouseIdsToResolve } = _reduce(
    partDetailsToResolveByPartIdentifierId,
    (acc, partDetails, partIdentifierId) => {
      if (_isNil(resolvedPartDetailsByIdentifierId[partIdentifierId])) {
        const partId = SalesOrderPartLineItemReader.partId(partDetails);
        const warehouseId = SalesOrderPartLineItemReader.warehouseId(partDetails);
        acc.partIdsToResolve.push(partId);
        acc.warehouseIdsToResolve.push(warehouseId);
      }
      return acc;
    },
    { partIdsToResolve: [], warehouseIdsToResolve: [] },
  );
  if (!_isEmpty(partIdsToResolve)) {
    const partResolvePayload = getPartSearchV2RequestPayload(partIdsToResolve, warehouseIdsToResolve);
    const response = await withPartSearchV2(partResolvePayload);
    const resolvedPartDetailsByPartIdentifierId = getResolvedPartDetailsByPartIdentifierId({
      partDetailsList: _get(_get(response, 'data.data'), 'hits'),
    });

    updatedResolvedPartDetailsByIdentifierId = {
      ...resolvedPartDetailsByIdentifierId,
      ...resolvedPartDetailsByPartIdentifierId,
    };
  }

  compose(dispatch, getSetAdditionalDataAction)(ITEM_TYPE, {
    additional: {
      ...additional,
      partsSettingsMap: updatedPartsSettingsMap,
      reasonsList: updatedReasonsList,
      resolvedPartDetailsByIdentifierId: updatedResolvedPartDetailsByIdentifierId,
      expandedRowDetails: { ...currentExpandedRowDetails, [id]: isExpanded },
    },
  });
};

const bulkMarkCustomerReceived = async ({ payload }, { dispatch, getState, setState }) => {
  const rowData = _get(payload, 'rowData', EMPTY_OBJECT);
  const salesOrderInfo = _get(rowData, 'row.original', EMPTY_OBJECT);

  const partsEligibleForMarkingCustomerReceived =
    SalesOrderDomain.getPartsEligibleForMarkingCustomerReceived(salesOrderInfo);

  setState({
    isBulkMarkingCustomerReceived: true,
  });
  toaster(TOASTER_TYPE.INFO, __('Marking prepaid parts as customer received.'));

  try {
    const submitPayload = createPayloadForMarkingCustomerReceived({
      partLineIds: _map(partsEligibleForMarkingCustomerReceived, part => SalesOrderPartLineItemReader.id(part)),
      salesOrder: salesOrderInfo,
    });
    await markCustomerReceivedAction(submitPayload);
    handleTableItemsRefetch({}, { dispatch, getState });
    toaster(TOASTER_TYPE.SUCCESS, __('Prepaid parts are successfully marked as customer received.'));
  } catch {
    toaster(TOASTER_TYPE.ERROR, __('Failed while marking prepaid parts as customer received'));
  } finally {
    setState({
      isBulkMarkingCustomerReceived: false,
    });
  }
};

const bulkInvoiceCreationHandler = async ({ payload }, { getState }) => {
  const { salesOrders = EMPTY_ARRAY } = getState();
  const { selectedRowIds } = payload;
  const salesOrderAssets = _filter(salesOrders, salesOrder =>
    _includes(_keys(selectedRowIds), _toString(salesOrder?.id)),
  );
  const filteredInvoices = filterSalesOrderForBulkInvoiceGeneration(salesOrderAssets);

  if (_isEmpty(filteredInvoices)) {
    toaster(TOASTER_TYPE.ERROR, __('Selected items are not applicable for this action'));
    return;
  }

  const metadataPayload = getMetaDataAPIPayload(filteredInvoices, BULK_ACTION_DOCUMENTS.INVOICE);
  const metaDataResponse = await bulkActionFetchMetadata(metadataPayload);
  const assetMetaData = getParsedMetadataForBulkRequest(metaDataResponse, BULK_ACTION_DOCUMENTS.INVOICE);

  const bulkInvoiceGenerationPayload = getBulkInvoiceCreationPayload(assetMetaData);
  const requestPayload = getBulkPayload({
    bulkPrintDocumentType: BULK_ACTION_DOCUMENTS.INVOICE,
    requestType: BULK_ACTIONS_REQUEST_TYPES.SALES_ORDER_AUTO_INVOICE,
    requestListItems: bulkInvoiceGenerationPayload,
  });

  try {
    await bulkActionTriggerRequest(requestPayload);
  } catch (err) {
    const errorMessage = _get(err, ['data', 'errorDetails', 'displayMessage'], EMPTY_STRING);
    const errorCode = _get(err, ['data', 'errorDetails', 'errorCode'], EMPTY_STRING);
    const formattedErrorMessage = formatErrorMessage(errorMessage);
    toaster(TOASTER_TYPE.ERROR, __(`${errorCode} : ${formattedErrorMessage}`));
    return;
  }
  toaster(TOASTER_TYPE.INFO, __("Invoices are being generated. You'll be notified upon completion."));
};

export const openBulkActionModal = ({ payload }, { getState, setState }) => {
  const { salesOrders = EMPTY_ARRAY } = getState();
  const {
    selectedRowIds = EMPTY_OBJECT,
    type = EMPTY_STRING,
    bulkNotificationId,
    bulkNotificationLastUpdatedAt,
  } = payload;

  const salesOrdersInfo = _filter(salesOrders, salesOrder =>
    _includes(_keys(selectedRowIds), _toString(salesOrder?.id)),
  );

  if (_isEmpty(salesOrdersInfo) && _isEmpty(_toString(bulkNotificationId))) {
    toaster(TOASTER_TYPE.ERROR, __('Sales Orders could not be resolved. Please try again.'));
    return;
  }

  setState({
    isBulkActionDrawerVisible: true,
    selectedBulkActionkey: type,
    selectedSalesOrdersInfo: salesOrdersInfo,
    currentBulkNotificationId: bulkNotificationId,
    currentBulkNotificationLastUpdatedAt: bulkNotificationLastUpdatedAt,
  });
};

export const closeBulkActionModal = (_, { setState }) => {
  setState({
    isBulkActionDrawerVisible: false,
  });
};

export const closeBulkActionNotification = ({ payload }, { getState, setState }) => {
  const { bulkNotificationId = EMPTY_STRING } = payload;
  const { additional } = getState();
  const { bulkActionNotifications = EMPTY_ARRAY } = additional;

  const newBulkActionNotifications = _filter(bulkActionNotifications, ({ id }) => id !== bulkNotificationId);

  setState({
    additional: {
      ...additional,
      bulkActionNotifications: newBulkActionNotifications,
    },
  });
};

const throttledSetState = _throttle(
  (setState, dataToSet) => {
    setState(dataToSet);
  },
  1000,
  { leading: true, trailing: true },
);

const addBulkActionNotification = ({ payload }, { getState, setState }) => {
  const { request = EMPTY_OBJECT } = payload;
  const { id: currentUserId } = getCurrentUser();

  if (
    request?.parentAssetType !== ASSET_TYPE.SALES_ORDER ||
    (!_isNil(request?.userId) && currentUserId !== request?.userId)
  ) {
    return;
  }

  const { currentBulkNotificationId = EMPTY_STRING, additional } = getState();
  const { bulkActionNotifications = EMPTY_ARRAY } = additional;

  const {
    requestType = EMPTY_STRING,
    bulkRequestId: id = EMPTY_STRING,
    assetType = EMPTY_STRING,
    status = EMPTY_STRING,
    totalRequest = 0,
    successRequest = 0,
    failedRequest = 0,
    response = EMPTY_STRING,
  } = request;

  if (_isEmpty(id)) return;

  const bannerText = getBulkNotificationText({
    bulkAssetType: assetType,
    status,
    totalRequest,
    successRequest,
    failedRequest,
    requestType,
    response,
    extra: {
      mediaId: _get(request, 'mediaId'),
    },
  });

  const { type, color, barColor } = BULK_ACTION_NOTIFICATION_STATUS_VS_COLOR_CONFIG[status];
  const bulkActionKey = getBulkActionNotificationHandlerTypeKey(requestType, assetType);

  const bulkNotification = {
    id,
    text: bannerText,
    type,
    color,
    barColor,
    handlerKey: bulkActionKey,
    viewDetailButtonConfig: getActionButtonConfig(request),
    lastUpdatedAt: Date.now(),
  };

  const updatedBulkNotifications = updateBulkActionArray([bulkNotification], bulkActionNotifications);

  if (_includes(_map(bulkActionNotifications, 'id'), bulkNotification?.id)) {
    throttledSetState(setState, {
      additional: {
        ...additional,
        bulkActionNotifications: updatedBulkNotifications,
      },
      ...(currentBulkNotificationId === bulkNotification.id && {
        currentBulkNotificationLastUpdatedAt: bulkNotification.lastUpdatedAt,
      }),
    });
    return;
  }

  setState({
    additional: {
      ...additional,
      bulkActionNotifications: [
        ..._filter(bulkActionNotifications, notif => notif?.id !== bulkNotification?.id),
        bulkNotification,
      ],
    },
    ...(currentBulkNotificationId === bulkNotification.id && {
      currentBulkNotificationLastUpdatedAt: bulkNotification.lastUpdatedAt,
    }),
  });
};

const bulkDownloadHandler = async ({ payload }, { getState }) => {
  const { salesOrders = EMPTY_ARRAY } = getState();
  const { type, selectedRowIds } = payload;
  const bulkDocumentType = BULK_ACTION_KEY_VS_DOWNLOAD_BULK_ACTION_DOCUMENT[type] || BULK_ACTION_DOCUMENTS.INVOICE;
  const salesOrderAssets = _filter(salesOrders, salesOrder =>
    _includes(_keys(selectedRowIds), _toString(salesOrder?.id)),
  );
  const filteredInvoices = removeSalesOrderAssets(salesOrderAssets, bulkDocumentType);

  if (_isEmpty(filteredInvoices)) {
    toaster(TOASTER_TYPE.ERROR, __('Selected items are not applicable for this action'));
    return;
  }

  const metadataPayload = getMetaDataAPIPayload(filteredInvoices, bulkDocumentType);
  const metaDataResponse = await bulkActionFetchMetadata(metadataPayload);
  const assetMetaData = getParsedMetadataForBulkRequest(metaDataResponse, bulkDocumentType);

  const downloadRequestInvoicePayloads = getBulkDownloadInvoiceLevelPayload(assetMetaData, salesOrders);
  const requestPayload = getBulkPayload({
    bulkPrintDocumentType: bulkDocumentType,
    requestType: BULK_ACTIONS_REQUEST_TYPES.DOWNLOAD,
    requestListItems: downloadRequestInvoicePayloads,
    extra: {
      copyTypes: CURRENT_SO_PDF_COPY_TYPES,
    },
  });

  bulkActionTriggerRequest(requestPayload).catch(err => {
    console.error(err);
    const errorMessage = _get(err, ['data', 'errorDetails', 'displayMessage'], EMPTY_STRING);
    const errorCode = _get(err, ['data', 'errorDetails', 'errorCode'], EMPTY_STRING);
    const formattedErrorMessage = formatErrorMessage(errorMessage);
    toaster(TOASTER_TYPE.ERROR, __(`${errorCode} : ${formattedErrorMessage}`));
  });
  toaster(TOASTER_TYPE.INFO, BULK_ACTION_KEY_VS_DOWNLOAD_TOASTER_MESSAGE[type]);
};

const bulkDownloadSuccessHandler = async ({ payload }) => {
  const { bulkNotificationId } = payload;

  let mediaId = _get(payload, 'mediaId', EMPTY_STRING);
  let folderName = _get(payload, 'folderName', 'BULK_ASSETS');

  if (_isEmpty(mediaId)) {
    const bulkRequestResponse = await bulkActionFetchRequestData(bulkNotificationId);

    mediaId = _get(bulkRequestResponse, ['data', 'data', 'postProcessingActions', '0', 'response', 'id'], EMPTY_STRING);
    folderName = _get(
      bulkRequestResponse,
      ['data', 'data', 'postProcessingActions', '0', 'request', 'folderName'],
      'BULK_ASSETS',
    );
  }

  const { data } = await getSignedURLsForV3Service(_castArray(mediaId), EMPTY_OBJECT)();

  const downloadURL = _get(data, [mediaId]);
  if (_isEmpty(downloadURL)) {
    toaster(TOASTER_TYPE.INFO, __('Assets could not be resolved'));
    return;
  }

  downloadFile(downloadURL, folderName, false);
};

const uploadBulkSOExcelS3MediaId = async ({ payload }) => {
  const { mediaId, fileName = 'SALES_ORDER_CREATION_EXCEL_DEFAULT.xlsx' } = payload;

  if (_isEmpty(mediaId)) {
    toaster(TOASTER_TYPE.ERROR, __('Invalid S3 mediaId'));
  }

  const requestPayload = getBulkPayload({
    bulkPrintDocumentType: BULK_ACTION_ASSETS.SALES_ORDER,
    requestType: BULK_ACTIONS_REQUEST_TYPES.SALES_ORDER_CREATION,
    extra: {
      mediaId,
      fileName,
    },
  });
  try {
    toaster(TOASTER_TYPE.INFO, __('Excel file uploaded successfully.'));
    await bulkActionTriggerRequest(requestPayload);
  } catch (err) {
    const errorMessage = _get(err, ['data', 'errorDetails', 'displayMessage'], EMPTY_STRING);
    const errorCode = _get(err, ['data', 'errorDetails', 'errorCode'], EMPTY_STRING);
    const formattedErrorMessage = formatErrorMessage(errorMessage);
    toaster(TOASTER_TYPE.ERROR, __(`${errorCode} : ${formattedErrorMessage}`));
  }
};

const BULK_ACTION_KEY_VS_HANDLER = {
  [BULK_ACTION_KEYS.DOWNLOAD_INVOICE]: bulkDownloadHandler,
  [BULK_ACTION_KEYS.DOWNLOAD_CREDIT_MEMO]: bulkDownloadHandler,
  [BULK_ACTION_KEYS.DOWNLOAD_QUOTE]: bulkDownloadHandler,
  [BULK_ACTION_KEYS.DOWNLOAD_INVOICE_SUCCESS]: bulkDownloadSuccessHandler,
  [BULK_ACTION_KEYS.DOWNLOAD_CREDIT_MEMO_SUCCESS]: bulkDownloadSuccessHandler,
  [BULK_ACTION_KEYS.DOWNLOAD_QUOTE_SUCCESS]: bulkDownloadSuccessHandler,
  [BULK_ACTION_KEYS.UPLOAD_EXCEL_S3_MEDIA_ID]: uploadBulkSOExcelS3MediaId,
  [BULK_ACTION_KEYS.DOWNLOAD_BULK_SO_CREATION_ASSET]: bulkDownloadSuccessHandler,
};

export const triggerBulkActions = ({ payload }, { getState, setState }) => {
  const { type } = payload;

  const bulkActionHandler = BULK_ACTION_KEY_VS_HANDLER[type] || openBulkActionModal;

  if (_isFunction(bulkActionHandler)) {
    bulkActionHandler({ payload }, { getState, setState });
    return;
  }

  toaster(TOASTER_TYPE.ERROR, __('Bulk Action could not be resolved'));
};

const triggerHeadingDropdownAction = ({ payload }, { setState, getState }) => {
  const { type } = payload;

  if (!_isEmpty(type) && _includes(BULK_ACTION_KEYS, type)) {
    triggerBulkActions({ payload }, { setState, getState });
    return;
  }

  toaster(TOASTER_TYPE.ERROR, __('Dropdown action could not be resolved'));
};

const toggleAllRowsSelected = ({ payload }, { getState, setState }) => {
  const toggleValue = _get(payload, 'toggleValue');
  const { selectedRowIds, salesOrders } = getState();
  if (_isBoolean(toggleValue)) {
    const newSelectedRowIds = toggleValue
      ? _reduce(salesOrders, (acc, salesOrder) => ({ ...acc, [salesOrder?.id]: true }), EMPTY_OBJECT)
      : EMPTY_OBJECT;

    setState({
      selectedRowIds: newSelectedRowIds,
    });
    return;
  }

  const isAnyRowNotSelected = _size(selectedRowIds) < _size(salesOrders);
  const newSelectedRowIds = isAnyRowNotSelected
    ? _reduce(salesOrders, (acc, salesOrder) => ({ ...acc, [salesOrder?.id]: true }), EMPTY_OBJECT)
    : EMPTY_OBJECT;
  setState({
    selectedRowIds: newSelectedRowIds,
  });
};

const toggleRowSelected = ({ payload }, { getState, setState }) => {
  const { rowId, toggleValue } = payload;
  const { selectedRowIds } = getState();
  if (_isBoolean(toggleValue)) {
    if (toggleValue) {
      setState({
        selectedRowIds: {
          ...selectedRowIds,
          [rowId]: true,
        },
      });
      return;
    }

    setState({
      selectedRowIds: _omit(selectedRowIds, [rowId]),
    });
    return;
  }

  if (_get(selectedRowIds, [rowId], false)) {
    setState({
      selectedRowIds: _omit(selectedRowIds, [rowId]),
    });
    return;
  }

  setState({
    selectedRowIds: {
      ...selectedRowIds,
      [rowId]: true,
    },
  });
};

const ACTION_HANDLER = {
  [TABLE_ACTION_TYPES.TABLE_ITEMS_FETCH]: ({ payload } = EMPTY_OBJECT, { dispatch, getState, setState }) => {
    const {
      filterValue,
      currentPage,
      searchQuery,
      searchField,
      rows,
      sortDetails,
      mergeFiltersWithQueryParams = _identity,
      additional,
      partsSettingsMap = IMap(),
    } = getState();
    const {
      multiSearchSelectedItems,
      expandedRowDetails,
      resolvedPartDetailsByIdentifierId,
      reasonsList,
      bulkActionNotifications = EMPTY_ARRAY,
    } = additional || EMPTY_OBJECT;
    // if user is redirected to SO from parts performance report, we check the
    // filterGroupVariant and decide the initial filters whereas if it is from customer
    // management, then mergeFiltersWithQueryParams is used to get initial filters
    // not using mergeFiltersWithQueryParams if the redirection is from parts performance
    // so as to avoid the filterGroupVariant from being sent in the filter payload.
    // CQA - 1924
    const isRedirectedFromPartsPerformanceReport = getShouldSetInitialFilterGroupVariant(getState());

    const showShowLoaderFromParams = _get(payload, 'shouldShowLoader', false);

    const initialFilters = getInitialTableFilters(getState());
    const pageNumber = currentPage >= 0 ? currentPage : 0;
    const selectedFilters = isRedirectedFromPartsPerformanceReport
      ? initialFilters
      : mergeFiltersWithQueryParams(initialFilters);

    setState({
      userFilterDialogState: selectedFilters,
    });

    compose(
      dispatch,
      fetchSalesOrderList,
    )({
      shouldShowLoader: showShowLoaderFromParams,
      rows,
      searchText: searchQuery || '',
      searchQuery: searchQuery || '',
      searchField: searchField || SEARCH_FIELD_TYPE.ALL,
      filterValue,
      selectedFilters,
      pageNumber,
      currentPage: pageNumber,
      sortDetails: sortDetails || DEFAULT_SORTING,
      multiSearchSelectedItems,
      expandedRowDetails,
      resolvedPartDetailsByIdentifierId,
      reasonsList,
      partsSettingsMap,
      bulkActionNotifications,
      ...getQuickFilterProps(additional),
    });
  },
  [TABLE_ACTION_TYPES.TABLE_ITEMS_PAGE_UPDATE]: ({ payload }, { dispatch, getState }) => {
    const { value: params } = payload;
    const { filterValue, searchText, searchQuery, searchField, sortDetails, selectedFilters, additional } = getState();
    const { multiSearchSelectedItems, expandedRowDetails, resolvedPartDetailsByIdentifierId } = additional;
    const paginationBuilder = props => {
      const { pageNumber, rows } = props;
      return {
        start: rows * pageNumber,
        rows,
      };
    };
    scrollToTop(PSO_TABLE_ID);
    const rows = params.resultsPerPage;
    const pageNumber = _get(params, 'page', 0);
    const pagePayload = paginationBuilder({ pageNumber: pageNumber - 1, rows });
    compose(
      dispatch,
      changePage,
    )({
      shouldShowLoader: true,
      searchText,
      searchQuery,
      searchField,
      filterValue,
      pageInfo: pagePayload,
      currentPage: pageNumber - 1,
      rows,
      selectedFilters,
      sortDetails,
      multiSearchSelectedItems,
      expandedRowDetails,
      resolvedPartDetailsByIdentifierId,
      ...getQuickFilterProps(additional),
    });
  },
  [TABLE_ACTION_TYPES.TABLE_ITEMS_SET_FILTER]: ({ payload }, { dispatch, getState, setState }) => {
    const { searchQuery, searchField, filterValue, rows, sortDetails, additional, currentPage } = getState();
    const { multiSearchSelectedItems, expandedRowDetails, currentFilterList, resolvedPartDetailsByIdentifierId } =
      additional;

    const isInitialFilterApply = _get(payload, 'additional.isInitialFilterApply', false);
    const isQuickFilterSelection = _get(payload, 'isQuickFilterSelection', false);

    if (!_isEmpty(currentFilterList) && isInitialFilterApply) return;

    setState({ selectedFilterGroup: payload.selectedFilterGroup });

    const { selectedFilters: tableFilters } = getFilterActionPayload({
      payload,
      defaultFilterValue: getDefaultFilters(),
    });
    const isCrossSiteDocAccessAllowed = getIsPartCrossSiteAccessEnabled();
    const selectedFilters = isCrossSiteDocAccessAllowed
      ? getSanitizedFiltersWithSite(tableFilters, {
          isMultiSiteEnabled: getIsMultiOemSwitchEnabled(),
        })
      : tableFilters;

    if (!isQuickFilterSelection) {
      setState({
        userFilterDialogState: selectedFilters,
      });
    }

    const pageNumber = currentPage >= 0 ? currentPage : 0;
    compose(
      dispatch,
      fetchSalesOrderList,
    )({
      shouldShowLoader: true,
      searchQuery,
      searchText: searchQuery,
      searchField,
      pageNumber,
      currentPage: isInitialFilterApply ? pageNumber : 0,
      filterValue,
      selectedFilters,
      rows,
      sortDetails,
      multiSearchSelectedItems,
      expandedRowDetails,
      resolvedPartDetailsByIdentifierId,
      ...getQuickFilterProps(payload),
    });
  },
  [TABLE_ACTION_TYPES.TABLE_SEARCH]: ({ payload }, { dispatch, getState }) => {
    const { value, selectedItems } = payload;
    const { filterValue, searchField, rows, selectedFilters, additional } = getState();
    compose(
      dispatch,
      fetchSalesOrderList,
    )({
      shouldShowLoader: true,
      searchQuery: value,
      searchText: value,
      searchField,
      currentPage: 0,
      filterValue,
      rows,
      selectedFilters,
      sortDetails: _isEmpty(value) ? DEFAULT_SORTING : EMPTY_OBJECT, // for better search results by relevance.
      ...getQuickFilterProps(additional),
      multiSearchSelectedItems: selectedItems,
    });
  },
  [TABLE_ACTION_TYPES.TABLE_ITEM_CLICK]: ({ payload }, { getState }) => {
    const { navigate, salesOrders } = getState();
    const original = _get(payload, 'value.original');
    const isCreditMemo = SalesOrderDomain.isCreditMemo(original);
    const salesOrderId = SalesOrderListViewItemReader.id(original);
    if (_isNil(salesOrderId)) {
      return;
    }
    const detailsPagePath = getDetailsRoute(salesOrderId, isCreditMemo);
    const salesOrderDetailsFromListView = _find(
      salesOrders,
      order => SalesOrderListViewItemReader.id(order) === salesOrderId,
    );
    const partsList = SalesOrderListViewItemReader.partSaleDetails(salesOrderDetailsFromListView);
    const hasShortageSale = _some(partsList, SalesOrderPartLineItemDomain.hasShortageSaleQty);
    const isOpen = !(
      SalesOrderDomain.isClosed(salesOrderDetailsFromListView) ||
      SalesOrderDomain.isReopened(salesOrderDetailsFromListView)
    );
    navigate(detailsPagePath, {
      state: { salesOrderDetailsFromListView, isOldSOView: hasShortageSale && isOpen },
    });
  },
  [CUSTOM_ACTIONS.CREATE_SALES_ORDER]: (data, { getState }) => {
    const { navigate } = getState();
    navigate(getSalesOrderCreateRoute());
  },

  [CUSTOM_ACTIONS.OPEN_CREDIT_MEMO_MODAL]: (data, { setState }) => {
    setState({
      isCreditMemoSelectorVisible: true,
    });
  },

  [CUSTOM_ACTIONS.CLOSE_CREDIT_MEMO_MODAL]: (data, { setState }) => {
    setState({
      isCreditMemoSelectorVisible: false,
    });
  },

  [CUSTOM_ACTIONS.CREATE_CREDIT_MEMO]: ({ payload: salesOrderId }, { getState, setState }) => {
    setState({
      isCreditMemoSelectorVisible: false,
    });

    const { navigate } = getState();
    navigate(getCreditMemoCreateRoute({ salesOrderId }));
  },

  [CUSTOM_ACTIONS.TOGGLE_SEARCH_SHORTCUT]: handleTriggerSearchInput,

  [GENERAL_ACTIONS.CLOSE_DOWNLOAD_FAILED_MODAL]: (_, { setState }) => {
    setState({ isDownloadLimitModalVisible: false });
  },

  [GENERAL_ACTIONS.DOWNLOAD_EXCEL]: async ({ payload }, { getState, setState }) => {
    const { searchQuery, searchField, sortDetails, totalCount, selectedFilters, additional } = getState();
    const selectedWarehouse = partsEnv.userPreferredWarehouseId;
    const selectedFiltersWithWarehouse = getSelectedFiltersWithWarehouse(
      selectedFilters,
      selectedWarehouse,
      getIsMultiWarehouseEnabled(),
    );
    const { multiSearchSelectedItems } = additional;
    if (totalCount > MAX_EXCEL_DOWNLOADABLE_LIST_COUNT) {
      setState({
        isDownloadLimitModalVisible: true,
      });
      return;
    }
    const params = {
      searchQuery,
      searchField: getSearchableFields(searchField, SEARCH_FIELD_TYPE, SEARCH_FIELD_TYPE_VS_OVERRIDE_VALUE),
      selectedFilters: changeExistOperatorsToBooleanValue(selectedFiltersWithWarehouse),
      sortDetails,
      downloadApi: fetchAndDownloadSalesOrder,
      defaultSortKey: DEFAULT_PSO_DOWNLOAD_DETAILS.SORT_KEY,
      fileName: DEFAULT_PSO_DOWNLOAD_DETAILS.FILE_NAME,
      fileExtension: FILE_EXTENSIONS.CSV,
      searchTextFields: getFacetSearchRequest(multiSearchSelectedItems),
    };
    handleExcelDownload(params);
  },

  [TABLE_ACTION_TYPES.TABLE_ITEMS_SORT]: ({ payload }, { dispatch, getState }) => {
    const { value } = payload;
    const { searchQuery, searchField, filterValue, rows, selectedFilters, additional } = getState();
    const { multiSearchSelectedItems, expandedRowDetails } = additional;
    const sortTypeMap = _get(value, 'sortTypeMap', EMPTY_OBJECT);
    const keyToSort = _get(value, 'column.key', EMPTY_OBJECT);
    const sortDetails = _pick(sortTypeMap, keyToSort);
    compose(
      dispatch,
      fetchSalesOrderList,
    )({
      shouldShowLoader: true,
      searchQuery,
      searchText: searchQuery,
      searchField,
      currentPage: 0,
      filterValue,
      rows,
      selectedFilters,
      sortDetails,
      multiSearchSelectedItems,
      expandedRowDetails,
      ...getQuickFilterProps(additional),
    });
  },

  [TABLE_ACTION_TYPES.TABLE_SEARCH_FIELD]: ({ payload }, { dispatch, getState, setState }) => {
    const { value } = payload;
    const { searchQuery, sortDetails, filterValue, rows, selectedFilters, additional } = getState();

    setState({ searchField: value });
    if (_isEmpty(searchQuery)) return;

    compose(
      dispatch,
      fetchSalesOrderList,
    )({
      shouldShowLoader: true,
      searchQuery,
      searchText: searchQuery,
      searchField: value,
      currentPage: 0,
      filterValue,
      rows,
      selectedFilters,
      sortDetails,
      ...getQuickFilterProps(additional),
    });
  },

  [CUSTOM_ACTIONS.TABLE_ITEMS_REFETCH_LIST]: handleTableItemsRefetch,
  [QUICK_FILTERS_ACTION_TYPE.UPDATE_QUICK_FILTERS]: updateQuickFilters,
  [CUSTOM_ACTIONS.TOGGLE_EXPAND_TABLE_ROW]: handleToggleExpandRow,
  [CUSTOM_ACTIONS.BULK_MARK_CUSTOMER_RECEIVED]: bulkMarkCustomerReceived,
  [CUSTOM_ACTIONS.TRIGGER_HEADING_DROPDOWN_ACTION]: triggerHeadingDropdownAction,
  // bulk actionhandlers
  [CUSTOM_ACTIONS.TOGGLE_ALL_ROWS_SELECTED]: toggleAllRowsSelected,
  [CUSTOM_ACTIONS.TOGGLE_ROW_SELECTED]: toggleRowSelected,
  [CUSTOM_ACTIONS.TRIGGER_BULK_ACTION]: triggerBulkActions,
  [CUSTOM_ACTIONS.OPEN_BULK_ACTION_MODAL]: openBulkActionModal,
  [CUSTOM_ACTIONS.CLOSE_BULK_ACTION_MODAL]: closeBulkActionModal,
  [CUSTOM_ACTIONS.CLOSE_BULK_ACTION_NOTIFICATION]: closeBulkActionNotification,
  [CUSTOM_ACTIONS.BULK_INVOICE_CREATION]: bulkInvoiceCreationHandler,
  [CUSTOM_ACTIONS.ADD_BULK_ACTION_NOTIFICATION]: addBulkActionNotification,
};

export default ACTION_HANDLER;
