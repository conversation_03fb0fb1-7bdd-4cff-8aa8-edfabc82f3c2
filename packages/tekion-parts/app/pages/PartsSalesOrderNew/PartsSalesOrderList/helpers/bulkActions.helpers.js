import _filter from 'lodash/filter';
import _find from 'lodash/find';
import _get from 'lodash/get';
import _includes from 'lodash/includes';
import _isEmpty from 'lodash/isEmpty';
import _keyBy from 'lodash/keyBy';
import _map from 'lodash/map';
import _reduce from 'lodash/reduce';
import _some from 'lodash/some';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';

import SalesOrderReader from '@tekion/tekion-business/src/appServices/parts/salesOrder/readers/SalesOrder.reader';

import {
  BULK_ACTION_DOCUMENTS,
  BULK_ACTION_STATUS,
} from '@tekion/tekion-widgets/src/appServices/parts/constants/bulkActionConstants';
import { getEnsurePDFPayload } from '@tekion/tekion-widgets/src/appServices/parts/Pdf/pdfV2.helpers';

import { getBulkNotificationText } from 'helpers/bulkActionNotifications.helpers';

import {
  BULK_ACTIONS_REQUEST_TYPES,
  BULK_ACTION_ASSETS,
  BULK_ACTION_ASSETS_VS_PRINT_ASSETS,
} from 'organisms/SOBulkActions/constants/bulkActions.constants';
import { parseMetaData } from 'organisms/SOBulkActions/helpers/bulkActionApi.helpers';

import { isMultilingualEnabled } from 'utils/general';

import SalesOrderDomain from '../../domain/SalesOrder.domain';
import {
  BULK_ACTION_REQUEST_TYPE_VS_NOTIFICATION_ACTION_KEY,
  BULK_ACTION_NOTIFICATION_STATUS_VS_COLOR_CONFIG,
} from '../constants/bulkActions.constants';

export const getMetaDataAPIPayload = (salesOrders, bulkActionDocumentType, printInfoRequired = false) => ({
  ids: _map(salesOrders, so => SalesOrderReader.id(so)),
  assetTypes:
    BULK_ACTION_ASSETS_VS_PRINT_ASSETS[bulkActionDocumentType] ||
    BULK_ACTION_ASSETS_VS_PRINT_ASSETS[BULK_ACTION_ASSETS.INVOICE],
  printInfoRequired,
});

export const getParsedMetadataForBulkRequest = (metadataAPIResponse, bulkActionDocumentType) =>
  parseMetaData(_get(metadataAPIResponse, ['data', 'data'], EMPTY_ARRAY), bulkActionDocumentType);

export const getBulkDownloadInvoiceLevelPayload = (assetMetaData, salesOrders) =>
  _reduce(
    assetMetaData,
    (acc, curr) => {
      const {
        assetId = EMPTY_STRING,
        assetType = EMPTY_STRING,
        salesOrderId = EMPTY_STRING,
        salesOrderNo = EMPTY_STRING,
      } = curr;
      const resolvedCustomerDetails = _get(
        _find(salesOrders, salesOrder => salesOrder.id === salesOrderId),
        'customer',
        EMPTY_OBJECT,
      );
      const assetNo = _get(curr, 'invoiceNo', EMPTY_STRING);
      const assetModifiedTime = _get(curr, 'modifiedTime', EMPTY_STRING);
      return [
        ...acc,
        {
          assetId,
          parentAssetId: salesOrderId,
          requestDetails: getEnsurePDFPayload({
            assetId,
            assetNo,
            assetType,
            salesOrderId,
            salesOrderNo,
            assetModifiedTime,
            isMultilingualEnabled: isMultilingualEnabled(),
            resolvedCustomerDetails,
            requestId: Date.now(),
          }),
        },
      ];
    },
    EMPTY_ARRAY,
  );

export const getBulkActionNotificationHandlerTypeKey = (bulkRequestType, bulkActionDocumentType) =>
  _get(BULK_ACTION_REQUEST_TYPE_VS_NOTIFICATION_ACTION_KEY, [bulkRequestType, bulkActionDocumentType], EMPTY_STRING);

export const getViewDetailButtonConfig = (requestStatus, requestType) => {
  if (requestType === BULK_ACTIONS_REQUEST_TYPES.EMAIL) {
    return {
      visible: false,
      label: __('View Details'),
    };
  }

  if (requestType === BULK_ACTIONS_REQUEST_TYPES.DOWNLOAD) {
    return {
      visible: requestStatus === BULK_ACTION_STATUS.SUCCESS,
      label: __('Download'),
    };
  }

  if (requestType === BULK_ACTIONS_REQUEST_TYPES.SALES_ORDER_CREATION) {
    return {
      visible: _includes([BULK_ACTION_STATUS.SUCCESS, BULK_ACTION_STATUS.FAILED], requestStatus),
      label: __('Download'),
    };
  }

  return {
    visible: true,
    label: __('View Details'),
  };
};

export const getBulkActionNotifRenderConfig = request => {
  const { id, requestType, assetType, status: bulkRequestStatus = BULK_ACTION_STATUS.IN_PROGRESS } = request;

  const totalRequest = _get(request, 'totalRequest', 0);
  const successRequest = _get(request, 'successRequest', 0);
  const failedRequest = _get(request, 'failedRequest', 0);

  const bannerText = getBulkNotificationText({
    bulkAssetType: assetType,
    status: bulkRequestStatus,
    totalRequest,
    successRequest,
    failedRequest,
    requestType,
  });

  const { type, color, barColor } = BULK_ACTION_NOTIFICATION_STATUS_VS_COLOR_CONFIG[bulkRequestStatus];
  const handlerKey = getBulkActionNotificationHandlerTypeKey(requestType, assetType);
  return {
    id,
    text: bannerText,
    type,
    color,
    barColor,
    handlerKey,
    lastUpdatedAt: Date.now(),
    viewDetailButtonConfig: getViewDetailButtonConfig(bulkRequestStatus, requestType),
  };
};

export const isValidSearchParams = ({ bulkRequestId, requestType, assetType }) => {
  const isNotEmpty = _some([bulkRequestId, requestType, assetType], item => !_isEmpty(item));
  const isValidAsset = _includes(
    [
      BULK_ACTION_DOCUMENTS.INVOICE,
      BULK_ACTION_DOCUMENTS.QUOTE,
      BULK_ACTION_DOCUMENTS.PICKLIST,
      BULK_ACTION_DOCUMENTS.CREDIT_MEMO,
    ],
    assetType,
  );
  const isValidRequest = _includes(
    [BULK_ACTIONS_REQUEST_TYPES.LABEL_PRINT, BULK_ACTIONS_REQUEST_TYPES.PRINT, BULK_ACTIONS_REQUEST_TYPES.PICKLIST],
    requestType,
  );
  return isNotEmpty && isValidAsset && isValidRequest;
};

export const updateBulkActionArray = (newBulkActions, currentBulkActions) => {
  const currentBulkActionsOrderedIds = _map(currentBulkActions, 'id');
  const newBulkActionsIds = _map(newBulkActions, 'id');

  const newBulkActionsById = _keyBy(newBulkActions, 'id');
  const currentBulkActionsById = _keyBy(currentBulkActions, 'id');

  const updatedBulkActionsArray = _map(
    currentBulkActionsOrderedIds,
    id => newBulkActionsById[id] || currentBulkActionsById[id] || EMPTY_OBJECT,
  );

  const newlyAddedBulkActions = _map(
    _filter(newBulkActionsIds, id => !_includes(currentBulkActionsOrderedIds, id)),
    id => newBulkActionsById[id] || EMPTY_OBJECT,
  );

  const res = _filter([...updatedBulkActionsArray, ...newlyAddedBulkActions], item => !_isEmpty(item));

  return res;
};

export const filterSalesOrderForBulkInvoiceGeneration = salesOrders =>
  _filter(salesOrders, salesOrder => {
    const isDraft = SalesOrderDomain.isDraft(salesOrder);
    const isVoided = SalesOrderDomain.isVoided(salesOrder);
    const isClosed = SalesOrderDomain.isClosed(salesOrder);

    return SalesOrderDomain.isSalesOrder(salesOrder) && !isDraft && !isVoided && !isClosed;
  });

export const getBulkInvoiceCreationPayload = assetMetaData =>
  _reduce(
    assetMetaData,
    (acc, curr) => {
      const { assetId = EMPTY_STRING, salesOrderId = EMPTY_STRING } = curr;
      return [
        ...acc,
        {
          assetId,
          parentAssetId: salesOrderId,
          requestDetails: EMPTY_OBJECT,
        },
      ];
    },
    EMPTY_ARRAY,
  );
