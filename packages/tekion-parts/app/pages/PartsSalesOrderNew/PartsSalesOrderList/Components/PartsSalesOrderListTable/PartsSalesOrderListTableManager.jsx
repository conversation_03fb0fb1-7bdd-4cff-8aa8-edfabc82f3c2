import React, { Fragment, useCallback, useRef, useState, useEffect, useMemo, useContext } from 'react';

import { connect } from 'react-redux';
import { useNavigate, useLocation } from 'react-router-dom';

import _debounce from 'lodash/debounce';
import _get from 'lodash/get';
import _identity from 'lodash/identity';
import _isEmpty from 'lodash/isEmpty';
import _map from 'lodash/map';
import _noop from 'lodash/noop';
import _pick from 'lodash/pick';

import classNames from 'classnames';
import PropTypes from 'prop-types';
import { defaultMemoize } from 'reselect';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { isRRG } from '@tekion/tekion-base/utils/dealerProgramUtils';
import getCurrentSiteIdFromLS from '@tekion/tekion-base/utils/getCurrentSiteIdFromLS';
import pure from '@tekion/tekion-base/utils/pure';

import { getShouldSetInitialFilterGroupVariant } from '@tekion/tekion-business/src/appServices/parts/helpers/general.helpers';
import SalesOrderListViewItemReader from '@tekion/tekion-business/src/appServices/parts/salesOrder/readers/SalesOrderListViewItem.reader';

import Button from '@tekion/tekion-components/src/atoms/Button';
import Content from '@tekion/tekion-components/src/atoms/Content';
import FontIcon from '@tekion/tekion-components/src/atoms/FontIcon';
import { PropertyControlledComponent } from '@tekion/tekion-components/src/molecules';
import ActionButton from '@tekion/tekion-components/src/molecules/actionButton';
import Info from '@tekion/tekion-components/src/molecules/Info';
import OnPushMessage from '@tekion/tekion-components/src/molecules/OnPushMessage';
import { POPOVER_TRIGGER } from '@tekion/tekion-components/src/molecules/popover';
import Skeleton from '@tekion/tekion-components/src/molecules/skeleton';
import { DEFAULT_FILTER_GROUP } from '@tekion/tekion-components/src/organisms/filterSection';
import { DEFAULT_FILTER_BEHAVIOR } from '@tekion/tekion-components/src/organisms/filterSection/constants/filterSection.constants';
import { getSignedPDFUrls } from '@tekion/tekion-components/src/organisms/PDFRenderer/PDFRenderer.actions';
import PdfViewer from '@tekion/tekion-components/src/organisms/PdfViewerParts';
import { DOCUMENT_TYPE } from '@tekion/tekion-components/src/organisms/PdfViewerParts/PdfViewer.constants';
import { ASSET_TYPE } from '@tekion/tekion-components/src/organisms/PdfViewerPartsV2/PdfViewer.constants';
import withCustomSortTable from '@tekion/tekion-components/src/organisms/withCustomSortTable';
import useKeyboardShortcuts from '@tekion/tekion-components/src/organisms/withKeyboardShortcuts/useKeyboardShortcuts';

import { BULK_ACTIONS_ROUTE_SEARCH_PARAMS } from '@tekion/tekion-widgets/src/appServices/parts/constants/bulkActionConstants';
import AmountInputFormatContext from '@tekion/tekion-widgets/src/appServices/parts/context/amountInputFormatContext';
import PDFHeader from '@tekion/tekion-widgets/src/appServices/parts/PdfHeaderWithCustomEmailSender';
import { getSalesOrderFilterProps as _getSalesOrderFilterProps } from '@tekion/tekion-widgets/src/appServices/parts/salesOrder/salesOrder.filters';
import usePdfRequestListener from '@tekion/tekion-widgets/src/hooks/usePdfRequestListener';
import usePDFV2RequestListener from '@tekion/tekion-widgets/src/hooks/usePDFV2RequestListener/usePDFV2RequestListener';
import { getPrintModalPreference } from '@tekion/tekion-widgets/src/organisms/AppSkeleton/appSkeleton/skeleton.selector';
import TableManager, {
  TABLE_MANAGER_DEFAULT_PROPS,
  TABLE_TYPES,
  TABLE_ACTION_TYPES,
} from '@tekion/tekion-widgets/src/organisms/tableManager';

import { useTekionConversion, DATE_TIME_FORMAT } from '@tekion/tekion-conversion-web';

import PartsDepartmentActions from 'actions/partsDepartment.actions';

import { getPageSummaryField, getDownloadButtonField } from 'atoms/commonFields';

import { GENERAL_ACTIONS } from 'constants/general.constants';

import { getPdfPrintFileAction } from 'helpers/printPdf.helpers';

import { useDebounceCallback } from 'hooks/useDebounce';

import CreditMemoSelectorModal from 'molecules/modals/CreditMemoSelectorModal';
import MaxDownloadLimitModal from 'molecules/modals/MaxDownloadLimitModal';
import SalesOrderHeaderSideBar from 'molecules/SalesOrderHeaderSideBar';
import { menuConfig } from 'molecules/SalesOrderHeaderSideBar/SalesOrderHeaderside.config';
import { APP, ROUTES } from 'molecules/SalesOrderHeaderSideBar/salesOrderHeaderSideBar.constant';
import ShortcutAction from 'molecules/ShortcutAction';

import BulkActionBanner from 'organisms/BulkActionBanner/BulkActionBanner';
import BannerContent from 'organisms/BulkActionBanner/components/BannerContent/BannerContent';
import FacetSearch from 'organisms/FacetSearch';
import { SalesOrderListViewToggle } from 'organisms/SalesOrderListViewToggle';
import WarehouseSingleSelectionRenderer from 'organisms/WarehouseSingleSelectionRenderer';

import { ROUTE_MODULES_VS_DOCUMENT_HEAD_PROPS } from 'pages/base/routes/route.constants';

import { hasEditPermissionsSalesOrder, hasCreatePermissionsCreditMemo } from 'permissions/partsPermissions';

import { useTableUserPreference } from 'queries/preference';

import PriceUpdateIndicator from 'shared/components/PriceUpdateIndicator';
import useGetPriceUpdates from 'shared/components/PriceUpdateIndicator/hooks/useGetPriceUpdates';
import { SEARCH_INPUT_WITH_SHORTCUT_PLACEHOLDER } from 'shared/general/tablemanager/constants';
import styles from 'shared/general/tablemanager/tablemanager.module.scss';
import { getSelectedPreference } from 'shared/table/utils';

import { getSelectedFilterCount } from 'utils/filterUtils';
import {
  isInchCape,
  getIsPartCrossSiteAccessEnabled,
  getIsMultiOemSwitchEnabled,
  getIsSalesOrderV3Enabled,
  getIsPrepaidSalesEnabled,
  getIsPeriodicInvoiceEnabled,
  getIsPaymentModesConfigEnabled,
  getIsPDFDocumentServiceEnabled,
  getIsMultiWarehouseEnabled,
  getIsBulkSOCreationEnabled,
} from 'utils/general';
import partsEnv from 'utils/partsEnv';

import SalesOrderDomain from '../../../domain/SalesOrder.domain';
import {
  TABLE_MANAGER_PROPS,
  TABLE_PAGE_SIZE_OPTIONS,
  TABLE_ROW_HEIGHT,
  DEFAULT_PAGE_SIZE,
  CUSTOM_ACTIONS,
  PSO_TABLE_ID,
  TOGGLE_SEARCH_SHORTCUT,
  CREATE_CREDIT_MEMO_BUTTON,
  SALES_ORDER_HEADING_DROPDOWN,
  CREATE_SALES_ORDER_SHORTCUT_FOR_SO_V3_ENABLED_DEALER,
  CREATE_CREDIT_MEMO_SHORTCUT,
  INFO_ICON_CONTENT,
  POLL_DEBOUNCE_DELAY,
  SELECT_ALL_CHECKBOXES_SHORTCUT,
  getEPCCartIcon,
  getCreateSalesOrderButton,
  PREFERENCE_SERVICE_ITEM_NAME,
} from '../../constants/general.constants';
import { SEARCH_FIELD_CONFIG, SEARCH_FIELD_TYPE, FACETS } from '../../constants/search.constant';
import { getBulkActionNotificationHandlerTypeKey, isValidSearchParams } from '../../helpers/bulkActions.helpers';
import {
  getDefaultFilters,
  getBulkActionChannelName,
  getRouteOptionsFromAPI,
  getModuleName,
} from '../../helpers/general.helper';
import { getChannelAndEventName } from '../../helpers/pdf.helper';
import { getSalesOrderShortcutItems } from '../../helpers/shortcut.helpers';
import { fetchAllRoutesAPI } from '../../partsSalesOrderList.api';
import BulkActionDrawer from '../BulkActionDrawer';
import BulkActionDropdown from '../BulkActionDropdown';
import DrillDownFilters from '../DrillDownFilters';
import PDFViewerWrapper from '../PDFViewerWrapper';

import PartsSalesOrderListTable from './PartsSalesOrderListTable';
import tableManagerStyles from './styles.module.scss';

const PENDING_PDF_REQUESTS_EXPIRE_TIMER = 2 * 60 * 1000; // 2 mins
const getKeyBoardShortcutConfigs = isSalesOrderV3Enabled => {
  const shortcutConfig = [CREATE_CREDIT_MEMO_SHORTCUT, SELECT_ALL_CHECKBOXES_SHORTCUT];
  if (isSalesOrderV3Enabled) {
    shortcutConfig.push(CREATE_SALES_ORDER_SHORTCUT_FOR_SO_V3_ENABLED_DEALER);
  }
  return shortcutConfig;
};
const getSalesOrderFilterProps = (
  selectedFilterGroup,
  shouldSetInitialSelectedFilters,
  departments,
  routeOptions,
  includeTwoDecimalPlacesInAmountFields,
) => ({
  ..._getSalesOrderFilterProps({
    selectedFilterGroup,
    shouldSetInitialSelectedFilters,
    isCrossSiteDocumentAccessAllowed: getIsPartCrossSiteAccessEnabled(),
    isMultiOemSwitchEnabled: getIsMultiOemSwitchEnabled(),
    isPrepaidSalesEnabled: getIsPrepaidSalesEnabled(),
    isSalesOrderV3Enabled: getIsSalesOrderV3Enabled(),
    partsEnv,
    departments,
    isPCardEnabled: isRRG(),
    isPaymentModesConfigEnabled: getIsPaymentModesConfigEnabled(),
    routeOptions,
    includeTwoDecimalPlacesInAmountFields,
  }),
  defaultFilterBehavior: DEFAULT_FILTER_BEHAVIOR.PERSIST_LOCAL,
  defaultFilterValues: getDefaultFilters(),
  showClear: true,
  alwaysRenderDefaultToolBar: true,
  getSelectedFilterCount,
});

const getTbodyProps = () => ({
  id: PSO_TABLE_ID,
});

const getTableProps = (
  {
    totalCount,
    currentPage,
    salesOrders,
    pageSize,
    headerHeight = 40,
    rows,
    resizedProps,
    additional = EMPTY_OBJECT,
    sortDetails,
    pendingPdfRequests,
    resolvedPartDetailsByIdentifierId,
    reasonsList,
    selectedRowIds,
    ...props
  },
  setSelectedPreference,
) => ({
  totalNumberOfEntries: totalCount,
  currentPage,
  resultsPerPage: pageSize,
  tableHeaderHeight: headerHeight,
  showPagination: salesOrders.length,
  rowHeight: TABLE_ROW_HEIGHT,
  pageSize: rows || DEFAULT_PAGE_SIZE,
  pageSizeOptions: TABLE_PAGE_SIZE_OPTIONS,
  minRows: 0,
  type: TABLE_TYPES.FIXED_COLUMN,
  getTdProps: () => ({
    ..._get(additional, 'resolvedData', EMPTY_OBJECT),
    pendingPdfRequests,
  }),
  getTbodyProps,
  additional: { ...additional, setSelectedPreference },
  pendingPdfRequests,
  sortDetails,
  resolvedPartDetailsByIdentifierId,
  reasonsList,
  selectedRowIds,
  ...resizedProps,
  ..._pick(props, TABLE_MANAGER_DEFAULT_PROPS),
});

const getFacetSearchField = ({ onSelectedItemsChange, searchInputRef, multiSearchSelectedItems }) => ({
  renderer: FacetSearchRenderer,
  renderOptions: {
    placeholder: TOGGLE_SEARCH_SHORTCUT.label,
    onSelectedItemsChange,
    facets: FACETS,
    searchInputRef,
    multiSearchSelectedItems,
  },
});

const getInfoButtonField = popoverText => ({
  renderer: Info,
  renderOptions: {
    className: styles.soSearchInfoIcon,
    helpText: popoverText,
    tooltipClass: 'p-4 width-auto',
    trigger: POPOVER_TRIGGER.HOVER,
    destroyOnClose: true,
  },
});

const getBulkActionDropdown = ({ selectedRowIds, onAction, partsSettingsMap, currentSiteId }) => ({
  renderer: BulkActionDropdown,
  renderOptions: {
    selectedRowIds,
    onAction,
    partsSettingsMap,
    currentSiteId,
  },
});

const getSubHeaderProps = ({
  rows,
  onAction,
  currentPage,
  totalCount,
  onSelectedItemsChange,
  searchInputRef,
  multiSearchSelectedItems,
  selectedRowIds = EMPTY_ARRAY,
  partsSettingsMap,
  currentSiteId,
}) => ({
  subHeaderLeftActions: [getPageSummaryField({ rows, currentPage, totalCount })],
  subHeaderRightActions: [
    getInfoButtonField(INFO_ICON_CONTENT),
    getFacetSearchField({ onSelectedItemsChange, searchInputRef, multiSearchSelectedItems }),
    getBulkActionDropdown({ selectedRowIds, onAction, partsSettingsMap, currentSiteId }),
    getDownloadButtonField(),
  ],
  expandableSearchClassName: styles.expandableSearchByField,
  className: styles.facetSearch,
  rightHeaderClassName: styles.subHeaderRight,
});

const closeModal = onAction => () => {
  onAction({
    type: CUSTOM_ACTIONS.CLOSE_CREDIT_MEMO_MODAL,
  });
};

const createCreditMemo = onAction => refSalesOrder => {
  onAction({
    type: CUSTOM_ACTIONS.CREATE_CREDIT_MEMO,
    payload: refSalesOrder,
  });
};

const handleMaxLimitDownloadModalClose = onAction => () => {
  onAction({
    type: GENERAL_ACTIONS.CLOSE_DOWNLOAD_FAILED_MODAL,
  });
};

const SortableTableManager = withCustomSortTable(TableManager);

const renderListPageShortcuts = (onAction, options) => (
  <Fragment>
    {_map(getSalesOrderShortcutItems(options), shortcut => (
      <ShortcutAction onAction={onAction} item={shortcut} />
    ))}
  </Fragment>
);

const FacetSearchRenderer = ({ onSelectedItemsChange, ...props }) => {
  const { searchInputRef, multiSearchSelectedItems } = props;
  const [isSearchActive, setSearchActive] = useState(true);

  const comboboxProps = {
    initialIsOpen: true,
  };

  const multiSelectProps = {
    initialSelectedItems: multiSearchSelectedItems,
  };

  const filterFacet = React.useCallback((item, inputValue) => {
    if (_isEmpty(inputValue)) {
      return false;
    }
    return true;
  }, []);

  const handleSearchOpen = () => setSearchActive(true);

  const debouncedOnSelectedItemsChange = useDebounceCallback(onSelectedItemsChange, 500);

  const handleSearchClose = params => {
    const { inputValue, selectedFacet, selectedItems } = params;
    if (_isEmpty(inputValue) && !selectedFacet && _isEmpty(selectedItems)) {
      setSearchActive(false);
    }
    if (isSearchActive && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  if (!isSearchActive)
    return <ActionButton icon="icon-search" color="black" onClick={handleSearchOpen} className="m-r-8" />;

  return (
    <FacetSearch
      filterFacet={filterFacet}
      onReset={handleSearchClose}
      comboboxProps={comboboxProps}
      multiSelectProps={multiSelectProps}
      onSelectedItemsChange={debouncedOnSelectedItemsChange}
      {...props}
    />
  );
};

const SKELETON_TITLE_STYLE = { width: '16rem', style: { margin: 0 } };
const SalesOrderListHeading = ({ lastUpdatedTime, isLoading, onAction, handleWarehouseChange }) => {
  const { getFormattedDateAndTime = _noop } = useTekionConversion() || EMPTY_OBJECT;
  const refreshButtonHandler = () => {
    onAction({
      type: TABLE_ACTION_TYPES.TABLE_ITEMS_FETCH,
      payload: {
        shouldShowLoader: true,
      },
    });
  };

  const { isNoUpdate } = useGetPriceUpdates();

  return (
    <div className="d-flex">
      {getIsPeriodicInvoiceEnabled() && (
        <SalesOrderHeaderSideBar
          selectedModule={APP.SALES_ORDER}
          title={__('Sales Order')}
          routes={ROUTES}
          menuConfig={menuConfig}
        />
      )}
      <div className={classNames(tableManagerStyles.titleAndChangeListViewContainer, 'd-flex')}>
        <div>{__('Part Sales Order')}</div>
        <SalesOrderListViewToggle />
      </div>
      <div className={styles.divider} />
      <div className="d-flex align-self-center">
        <Content className="flex-shrink-0">{__('Last Updated on :')}&nbsp;</Content>
        {isLoading ? (
          <Skeleton paragraph={false} active title={SKELETON_TITLE_STYLE} />
        ) : (
          <Content>
            {getFormattedDateAndTime({
              value: lastUpdatedTime,
              formatType: DATE_TIME_FORMAT.DATE_ABBREVIATED_MONTH_YEAR_WITH_HOUR_MINUTE,
            })}
          </Content>
        )}
      </div>
      {getIsMultiWarehouseEnabled() && (
        <WarehouseSingleSelectionRenderer value={partsEnv.userPreferredWarehouseId} onChange={handleWarehouseChange} />
      )}

      <Button view={Button.VIEW.TERTIARY} disabled={isLoading} onClick={refreshButtonHandler} className="m-l-8">
        <div className="d-flex">
          {__('Refresh')}
          <FontIcon className={classNames('m-l-8', { [styles.loader]: isLoading })}>icon-refresh</FontIcon>
        </div>
      </Button>
      {isNoUpdate ? null : (
        <>
          <div className={classNames(styles.divider, 'm-x-16')} />
          <PriceUpdateIndicator disabled={isLoading} />
        </>
      )}
    </div>
  );
};

const getHeaderProps = ({
  navigate,
  lastUpdatedTime,
  isLoading,
  onAction,
  handleOpenEPCCartListDrawer,
  handleWarehouseChange,
}) => {
  const actions = [];

  const soPermissions = hasEditPermissionsSalesOrder();
  const cmPermissions = hasCreatePermissionsCreditMemo();

  actions.push(getEPCCartIcon({ handleOpenEPCCartListDrawer }));

  if (cmPermissions && !isInchCape()) {
    actions.push(CREATE_CREDIT_MEMO_BUTTON);
  }
  if (soPermissions) {
    const isBulkSOCreationEnabled = getIsBulkSOCreationEnabled();
    actions.push(
      getCreateSalesOrderButton({
        isBulkSOCreationEnabled,
      }),
    );
    if (isBulkSOCreationEnabled) {
      actions.push(SALES_ORDER_HEADING_DROPDOWN);
    }
  }

  return {
    label: (
      <SalesOrderListHeading
        isLoading={isLoading}
        lastUpdatedTime={lastUpdatedTime}
        onAction={onAction}
        navigate={navigate}
        handleWarehouseChange={handleWarehouseChange}
      />
    ),
    headerRightActions: actions,
  };
};

const PartSalesOrderListTableManager = props => {
  const { onAction, emailOverrides, pendingPdfRequests, currentPage, currentPDFV2Request } = props;
  usePdfRequestListener(pendingPdfRequests, onAction);
  usePDFV2RequestListener(currentPDFV2Request, onAction, getIsPDFDocumentServiceEnabled());
  usePDFV2RequestListener(currentPDFV2Request, onAction, getIsPDFDocumentServiceEnabled(), {
    options: { assetType: ASSET_TYPE.CREDIT_MEMO },
  });
  usePDFV2RequestListener(currentPDFV2Request, onAction, getIsPDFDocumentServiceEnabled(), {
    options: { assetType: ASSET_TYPE.PURCHASE_ORDER },
  });

  const channelName = getChannelAndEventName();
  const bulkActionChannelName = getBulkActionChannelName();

  useEffect(() => {
    const intervalId = setInterval(() => {
      onAction({
        type: CUSTOM_ACTIONS.CHECK_PENDING_REQUEST_EXPIRATION,
      });
    }, PENDING_PDF_REQUESTS_EXPIRE_TIMER);
    return () => {
      clearInterval(intervalId);
    };
  }, [onAction]);

  useEffect(() => {
    const { title } = ROUTE_MODULES_VS_DOCUMENT_HEAD_PROPS.SALES_ORDER;
    document.title = title;
  }, []);

  const closePdfModal = useCallback(() => {
    onAction({
      type: GENERAL_ACTIONS.CLOSE_PDF_MODAL,
    });
  }, [onAction]);

  const refetchListRef = useRef(
    _debounce(
      currentPageToFetch => {
        onAction({
          type: CUSTOM_ACTIONS.TABLE_ITEMS_REFETCH_LIST,
          payload: { currentPage: currentPageToFetch },
        });
      },
      POLL_DEBOUNCE_DELAY,
      {
        leading: true,
        trailing: false,
      },
    ),
  );

  const refetchList = useCallback(() => {
    refetchListRef.current(currentPage);
  }, [currentPage]);

  const handleWarehouseChange = useCallback(() => refetchList(), [refetchList]);

  const triggerBulkActionUpdatesBanner = useCallback(
    response =>
      onAction({
        type: CUSTOM_ACTIONS.ADD_BULK_ACTION_NOTIFICATION,
        payload: { request: _get(response, ['defaultPayload'], EMPTY_OBJECT) },
      }),
    [onAction],
  );

  const fetchFixedOperation = () => Promise.resolve(EMPTY_OBJECT);

  const renderPdfViewerHeader = useCallback(
    pdfViewerHeaderProps => (
      <PDFHeader {...pdfViewerHeaderProps} emailOverrides={emailOverrides} fetchFixedOperation={fetchFixedOperation} />
    ),
    [emailOverrides],
  );

  const onSelectedItemsChange = changes => {
    const selectedItems = _get(changes, 'selectedItems', EMPTY_ARRAY);
    return onAction({
      type: TABLE_ACTION_TYPES.TABLE_SEARCH,
      payload: { selectedItems },
    });
  };
  const {
    totalCount,
    salesOrders,
    rows,
    searchQuery,
    searchField,
    selectedFilters,
    isCreditMemoSelectorVisible,
    sortDetails,
    isDownloadLimitModalVisible,
    filterOutTypesFromFiltersInTable,
    selectedFilterGroup,
    isPdfModalVisible,
    pdfItems,
    salesOrderInfo,
    shouldSetDefaultFilterGroup,
    afterPrintPDFCallback,
    additional,
    loading: isLoadingSalesOrder,
    showPrintOptionForUser,
    isPdfDownloading,
    // bulk action props
    isBulkActionDrawerVisible,
    selectedRowIds,
    selectedBulkActionkey,
    selectedSalesOrdersInfo,
    currentBulkNotificationId,
    currentBulkNotificationLastUpdatedAt,

    // pdf props
    initialSelectedPdfGroupId,
    lastTriggeredPrintTime,
    printerLogIdsMap,
    assetIdvsWatermarkStatusPDFV2,
    isLocalPrintSettingEnabled,

    // quickFilter/drillDownFilter props
    userFilterDialogState,
  } = props;
  const [filterValues, setFilterValues] = useState(EMPTY_OBJECT);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const fetch = async () => {
      try {
        const departmentsResponse = await PartsDepartmentActions.fetchPartsDepartments();
        const routeResponse = await fetchAllRoutesAPI();
        setFilterValues({
          departments: departmentsResponse,
          routes: getRouteOptionsFromAPI(routeResponse),
        });
      } catch {
        // do nothing
      }
    };
    fetch();
  }, []);

  useEffect(() => {
    const { pathname, search: locationSearchQuery = EMPTY_STRING } = location;
    const searchParams = new URLSearchParams(locationSearchQuery);

    const bulkRequestId = searchParams.get(BULK_ACTIONS_ROUTE_SEARCH_PARAMS.BULK_REQUEST_ID);
    const requestType = searchParams.get(BULK_ACTIONS_ROUTE_SEARCH_PARAMS.REQUEST_TYPE);
    const assetType = searchParams.get(BULK_ACTIONS_ROUTE_SEARCH_PARAMS.ASSET_TYPE);

    if (
      !isValidSearchParams({
        bulkRequestId,
        requestType,
        assetType,
      })
    ) {
      return;
    }

    const payload = {
      type: getBulkActionNotificationHandlerTypeKey(requestType, assetType),
      bulkNotificationId: bulkRequestId,
      bulkNotificationLastUpdatedAt: 0,
    };
    onAction({
      type: CUSTOM_ACTIONS.TRIGGER_BULK_ACTION,
      payload,
    });
    navigate(pathname);
  }, [navigate, onAction, location]);

  const searchInputRef = useRef();
  const shouldSetInitialSelectedFilters = !getShouldSetInitialFilterGroupVariant(props);
  const includeTwoDecimalPlacesInAmountFields = useContext(AmountInputFormatContext);
  const getFilterProps = defaultMemoize(getSalesOrderFilterProps);
  const filterProps = getFilterProps(
    shouldSetDefaultFilterGroup ? DEFAULT_FILTER_GROUP : selectedFilterGroup,
    shouldSetInitialSelectedFilters,
    _get(filterValues, 'departments'),
    _get(filterValues, 'routes', EMPTY_ARRAY),
    includeTwoDecimalPlacesInAmountFields,
  );
  const isSalesOrderV3Enabled = getIsSalesOrderV3Enabled();
  const shouldListenToShortcuts = !isCreditMemoSelectorVisible && !isDownloadLimitModalVisible;
  useKeyboardShortcuts({
    shouldListenToShortcuts,
    keyboardShortcutConfigs: getKeyBoardShortcutConfigs(isSalesOrderV3Enabled),
    onAction,
  });

  const multiSearchSelectedItems = _get(additional, 'multiSearchSelectedItems', EMPTY_ARRAY);
  const SODocumentType = SalesOrderDomain.isDraft(salesOrderInfo) ? DOCUMENT_TYPE.SALES_ORDER_QUOTE : DOCUMENT_TYPE.SO;

  const bulkActionNotifications = _get(additional, 'bulkActionNotifications', EMPTY_ARRAY);
  const partsSettingsMap = _get(additional, 'partsSettingsMap');
  const currentSiteId = getCurrentSiteIdFromLS();

  const getSignedPDFUrlsOverride = selectedMedia => () =>
    new Promise(resolve => {
      getSignedPDFUrls(selectedMedia)().then(response => {
        resolve(response);
        afterPrintPDFCallback(selectedMedia);
      });
    });

  const onCloseBulkNotificationClick = useCallback(
    bulkNotificationId => {
      onAction({
        type: CUSTOM_ACTIONS.CLOSE_BULK_ACTION_NOTIFICATION,
        payload: {
          bulkNotificationId,
        },
      });
    },
    [onAction],
  );

  const onViewDetailNotificationClick = useCallback(
    payload =>
      onAction({
        type: CUSTOM_ACTIONS.TRIGGER_BULK_ACTION,
        payload,
      }),
    [onAction],
  );

  const handleOpenEPCCartListDrawer = useCallback(
    () =>
      onAction({
        type: CUSTOM_ACTIONS.OPEN_EPC_CART_LIST_DRAWER,
      }),
    [onAction],
  );
  const [selectedPreference, setSelectedPreference] = useState(PREFERENCE_SERVICE_ITEM_NAME);
  const moduleName = getModuleName(selectedPreference);
  const queryState = useTableUserPreference(moduleName);
  const viewProps = useMemo(() => getSelectedPreference({ queryState, moduleName }), [queryState, moduleName]);
  return (
    <Fragment>
      <PropertyControlledComponent controllerProperty={!_isEmpty(bulkActionNotifications)}>
        <BulkActionBanner bulkActionNotifications={bulkActionNotifications}>
          <BannerContent
            onCloseBulkNotification={onCloseBulkNotificationClick}
            onViewDetailNotificationClick={onViewDetailNotificationClick}
          />
        </BulkActionBanner>
      </PropertyControlledComponent>
      <SortableTableManager
        tableComponent={PartsSalesOrderListTable}
        searchText={searchQuery}
        navigate={navigate}
        tableProps={getTableProps(props, setSelectedPreference)}
        tableManagerProps={TABLE_MANAGER_PROPS}
        headerProps={getHeaderProps({
          lastUpdatedTime: _get(additional, 'lastUpdatedTime'),
          isLoading: isLoadingSalesOrder || _get(additional, 'isFetchingSOListByPusherEvent'),
          onAction,
          handleOpenEPCCartListDrawer,
          navigate,
          handleWarehouseChange,
        })}
        subHeaderProps={getSubHeaderProps({
          rows,
          onAction,
          currentPage,
          totalCount,
          onSelectedItemsChange,
          searchInputRef,
          multiSearchSelectedItems,
          selectedRowIds,
          partsSettingsMap,
          currentSiteId,
        })}
        columns={[EMPTY_ARRAY]}
        l3HeaderProps={{
          render: drillDownProps => (
            <DrillDownFilters {...drillDownProps} userFilterDialogState={userFilterDialogState} />
          ),
          className: 'subHeaderContainer',
        }}
        onAction={onAction}
        data={salesOrders}
        selectedFilters={filterOutTypesFromFiltersInTable(selectedFilters, filterProps)}
        filterProps={filterProps}
        sortDetails={sortDetails}
        searchField={searchField}
        searchInputRef={searchInputRef}
        searchableFieldsOptions={SEARCH_FIELD_CONFIG}
        searchPlaceholder={SEARCH_INPUT_WITH_SHORTCUT_PLACEHOLDER}
        additional={{ ...additional }}
        disableHeight={_isEmpty(salesOrders) && !isLoadingSalesOrder}
        viewProps={viewProps}
      />
      <CreditMemoSelectorModal
        visible={isCreditMemoSelectorVisible}
        onClose={closeModal(onAction)}
        onSubmit={createCreditMemo(onAction)}
      />
      <MaxDownloadLimitModal
        visible={isDownloadLimitModalVisible}
        onCancel={handleMaxLimitDownloadModalClose(onAction)}
      />
      {getIsPDFDocumentServiceEnabled() && (
        <PDFViewerWrapper
          pdfs={pdfItems}
          onAction={onAction}
          details={salesOrderInfo}
          isPdfModalVisible={isPdfModalVisible}
          afterPrintPDFCallback={afterPrintPDFCallback}
          initialSelectedPdfGroupId={initialSelectedPdfGroupId}
          lastTriggeredPrintTime={lastTriggeredPrintTime}
          printerLogIdsMap={printerLogIdsMap}
          id={SalesOrderListViewItemReader.orderNo(salesOrderInfo)}
          dealerConfig={_get(partsEnv, 'dealerConfig', EMPTY_OBJECT)}
          isLocalPrintSettingEnabled={isLocalPrintSettingEnabled}
          assetIdvsWatermarkStatusPDFV2={assetIdvsWatermarkStatusPDFV2}
        />
      )}
      {isPdfModalVisible && !getIsPDFDocumentServiceEnabled() && (
        <PdfViewer
          dealerConfig={_get(partsEnv, 'dealerConfig', EMPTY_OBJECT)}
          pdfs={pdfItems}
          id={SalesOrderListViewItemReader.orderNo(salesOrderInfo)}
          details={salesOrderInfo}
          viewType={SODocumentType}
          onCancel={closePdfModal}
          renderHeader={renderPdfViewerHeader}
          afterPrintPDFCallback={afterPrintPDFCallback}
          printFile={getPdfPrintFileAction(showPrintOptionForUser)}
          getSignedPDFUrls={getSignedPDFUrlsOverride}
          isPdfDownloading={isPdfDownloading}
          isPDFDownloadedBySource
        />
      )}
      <BulkActionDrawer
        onAction={onAction}
        isBulkActionDrawerVisible={isBulkActionDrawerVisible}
        showPrintOptionForUser={showPrintOptionForUser}
        selectedBulkActionkey={selectedBulkActionkey}
        selectedSalesOrdersInfo={selectedSalesOrdersInfo}
        currentBulkNotificationId={currentBulkNotificationId}
        currentBulkNotificationLastUpdatedAt={currentBulkNotificationLastUpdatedAt}
      />
      {!isCreditMemoSelectorVisible &&
        !isDownloadLimitModalVisible &&
        renderListPageShortcuts(onAction, { searchInputRef, isSalesOrderV3Enabled })}
      <OnPushMessage eventKey={channelName} eventHandler={refetchList} />
      <OnPushMessage eventKey={bulkActionChannelName} eventHandler={triggerBulkActionUpdatesBanner} />
    </Fragment>
  );
};

PartSalesOrderListTableManager.propTypes = {
  searchQuery: PropTypes.string,
  totalCount: PropTypes.number,
  currentPage: PropTypes.number,
  isCreditMemoSelectorVisible: PropTypes.bool,
  onAction: PropTypes.func,
  salesOrders: PropTypes.array,
  rows: PropTypes.number,
  selectedFilterGroup: PropTypes.string,
  selectedFilters: PropTypes.array,
  sortDetails: PropTypes.object,
  isDownloadLimitModalVisible: PropTypes.bool,
  salesOrderInfo: PropTypes.object,
  pdfItems: PropTypes.array,
  isPdfModalVisible: PropTypes.bool,
  emailOverrides: PropTypes.object,
  filterOutTypesFromFiltersInTable: PropTypes.func,
  shouldSetDefaultFilterGroup: PropTypes.bool,
  searchField: PropTypes.string,
  pendingPdfRequests: PropTypes.array,
  currentPDFV2Request: PropTypes.object,
  showPrintOptionForUser: PropTypes.bool.isRequired,
  initialSelectedPdfGroupId: PropTypes.string,
  lastTriggeredPrintTime: PropTypes.number,
  printerLogIdsMap: PropTypes.object,
  assetIdvsWatermarkStatusPDFV2: PropTypes.object,
  isLocalPrintSettingEnabled: PropTypes.bool,
  selectedRowIds: PropTypes.object,
  isBulkActionDrawerVisible: PropTypes.bool,
  selectedBulkActionkey: PropTypes.string,
  selectedSalesOrdersInfo: PropTypes.array,
  bulkActionNotifications: PropTypes.array,
  currentBulkNotificationId: PropTypes.object,
  currentBulkNotificationLastUpdatedAt: PropTypes.number,
  isEPCCartListBottomDrawerVisible: PropTypes.bool,
  epcCartListDrawerParams: PropTypes.object,
  isEPCPartListBottomDrawerVisible: PropTypes.bool,
  epcPartListDrawerParams: PropTypes.object,
  userFilterDialogState: PropTypes.array,
};

PartSalesOrderListTableManager.defaultProps = {
  searchQuery: '',
  totalCount: 0,
  currentPage: 0,
  isCreditMemoSelectorVisible: false,
  onAction: _noop,
  salesOrders: EMPTY_ARRAY,
  selectedFilterGroup: DEFAULT_FILTER_GROUP,
  selectedFilters: EMPTY_ARRAY,
  rows: 0,
  sortDetails: EMPTY_OBJECT,
  isDownloadLimitModalVisible: false,
  salesOrderInfo: EMPTY_OBJECT,
  pdfItems: EMPTY_ARRAY,
  isPdfModalVisible: false,
  emailOverrides: EMPTY_OBJECT,
  filterOutTypesFromFiltersInTable: _identity,
  shouldSetDefaultFilterGroup: false,
  searchField: SEARCH_FIELD_TYPE.ALL,
  pendingPdfRequests: EMPTY_ARRAY,
  currentPDFV2Request: EMPTY_OBJECT,
  initialSelectedPdfGroupId: EMPTY_STRING,
  lastTriggeredPrintTime: 0,
  printerLogIdsMap: EMPTY_OBJECT,
  assetIdvsWatermarkStatusPDFV2: EMPTY_OBJECT,
  isLocalPrintSettingEnabled: false,
  selectedRowIds: EMPTY_OBJECT,
  isBulkActionDrawerVisible: false,
  selectedBulkActionkey: EMPTY_STRING,
  selectedSalesOrdersInfo: EMPTY_ARRAY,
  bulkActionNotifications: EMPTY_ARRAY,
  currentBulkNotificationId: EMPTY_OBJECT,
  currentBulkNotificationLastUpdatedAt: 0,
  isEPCCartListBottomDrawerVisible: false,
  epcCartListDrawerParams: EMPTY_OBJECT,
  isEPCPartListBottomDrawerVisible: false,
  epcPartListDrawerParams: EMPTY_OBJECT,
  userFilterDialogState: EMPTY_ARRAY,
};

const mapStateToProps = state => ({
  showPrintOptionForUser: getPrintModalPreference(state),
});

export default connect(mapStateToProps)(pure(PartSalesOrderListTableManager));
