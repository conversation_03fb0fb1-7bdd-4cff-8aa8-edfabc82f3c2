import React, { memo, useCallback } from 'react';

import _isNil from 'lodash/isNil';
import _noop from 'lodash/noop';

import PropTypes from 'prop-types';
import { mapProps } from 'recompose';

import { EMPTY_ARRAY, EMPTY_STRING } from '@tekion/tekion-base/app.constants';

import { Content } from '@tekion/tekion-components/src/atoms';
import { PropertyControlledComponent } from '@tekion/tekion-components/src/molecules';
import Drawer from '@tekion/tekion-components/src/molecules/drawer';
import { ASSET_TYPE } from '@tekion/tekion-components/src/organisms/PdfViewerPartsV2/PdfViewer.constants';

import LabelPrint from 'organisms/SOBulkActions/BulkLabelPrint/LabelPrint/LabelPrint';
import LabelPrintSummary from 'organisms/SOBulkActions/BulkLabelPrint/LabelPrintSummary/LabelPrintSummary';
import { BULK_ACTION_ASSETS } from 'organisms/SOBulkActions/constants/bulkActions.constants';
import EmailInvoice from 'organisms/SOBulkActions/EmailInvoice';
import GenerateInvoice from 'organisms/SOBulkActions/GenerateInvoice';
import GenerateInvoiceSummary from 'organisms/SOBulkActions/GenerateInvoiceSummary';
import InvoicePrint from 'organisms/SOBulkActions/PrintInvoice';
import PrintInvoiceSummary from 'organisms/SOBulkActions/PrintInvoiceSummary/PrintInvoiceSummary';
import BulkPrintPicklist from 'organisms/SOBulkActions/PrintPicklist/BulkPicklist';
import ViewPrintPicklist from 'organisms/SOBulkActions/PrintPicklist/ViewPicklist/ViewPrintPicklist';

import { BULK_ACTION_KEYS } from '../../constants/bulkActions.constants';
import { CUSTOM_ACTIONS } from '../../constants/general.constants';

import styles from './bulkActionDrawer.module.scss';

const BULK_ACTION_VS_COMPONENT = {
  // print drawers
  [BULK_ACTION_KEYS.PRINT_INVOICE]: mapProps(propsToMap => ({
    ...propsToMap,
    bulkActionAssetType: BULK_ACTION_ASSETS.INVOICE,
  }))(InvoicePrint),
  [BULK_ACTION_KEYS.PRINT_CREDIT_MEMO]: mapProps(propsToMap => ({
    ...propsToMap,
    bulkActionAssetType: BULK_ACTION_ASSETS.CREDIT_MEMO,
  }))(InvoicePrint),
  [BULK_ACTION_KEYS.PRINT_QUOTE]: mapProps(propsToMap => ({
    ...propsToMap,
    bulkActionAssetType: BULK_ACTION_ASSETS.QUOTE,
  }))(InvoicePrint),
  [BULK_ACTION_KEYS.PRINT_INVOICE_SUMMARY]: mapProps(propsToMap => ({
    ...propsToMap,
    bulkActionAssetType: BULK_ACTION_ASSETS.INVOICE,
  }))(PrintInvoiceSummary),
  [BULK_ACTION_KEYS.PRINT_CREDIT_MEMO_SUMMARY]: mapProps(propsToMap => ({
    ...propsToMap,
    bulkActionAssetType: BULK_ACTION_ASSETS.CREDIT_MEMO,
  }))(PrintInvoiceSummary),
  [BULK_ACTION_KEYS.PRINT_QUOTE_SUMMARY]: mapProps(propsToMap => ({
    ...propsToMap,
    bulkActionAssetType: BULK_ACTION_ASSETS.QUOTE,
  }))(PrintInvoiceSummary),

  [BULK_ACTION_KEYS.PRINT_PICKLIST]: mapProps(propsToMap => ({
    ...propsToMap,
    bulkActionAssetType: ASSET_TYPE.PICKLIST,
  }))(BulkPrintPicklist),
  [BULK_ACTION_KEYS.PRINT_PICKLIST_SUMMARY]: mapProps(propsToMap => ({
    ...propsToMap,
    bulkActionAssetType: ASSET_TYPE.PICKLIST,
  }))(ViewPrintPicklist),

  // email drawers
  [BULK_ACTION_KEYS.EMAIL_INVOICE]: mapProps(propsToMap => ({
    ...propsToMap,
    bulkActionAssetType: BULK_ACTION_ASSETS.INVOICE,
  }))(EmailInvoice),
  [BULK_ACTION_KEYS.EMAIL_CREDIT_MEMO]: mapProps(propsToMap => ({
    ...propsToMap,
    bulkActionAssetType: BULK_ACTION_ASSETS.CREDIT_MEMO,
  }))(EmailInvoice),
  [BULK_ACTION_KEYS.EMAIL_QUOTE]: mapProps(propsToMap => ({
    ...propsToMap,
    bulkActionAssetType: BULK_ACTION_ASSETS.QUOTE,
  }))(EmailInvoice),

  // label print drawers
  [BULK_ACTION_KEYS.LABEL_PRINT_PICKLIST]: mapProps(propsToMap => ({
    ...propsToMap,
    bulkActionAssetType: BULK_ACTION_ASSETS.PICKLIST,
  }))(LabelPrint),
  [BULK_ACTION_KEYS.LABEL_PRINT_PICKLIST_SUMMARY]: mapProps(propsToMap => ({
    ...propsToMap,
    bulkActionAssetType: BULK_ACTION_ASSETS.PICKLIST,
  }))(LabelPrintSummary),

  // generate invoice drawer
  [BULK_ACTION_KEYS.GENERATE_INVOICE]: GenerateInvoice,
  [BULK_ACTION_KEYS.GENERATE_INVOICE_SUMMARY]: mapProps(propsToMap => ({
    ...propsToMap,
    bulkActionAssetType: BULK_ACTION_ASSETS.INVOICE,
  }))(GenerateInvoiceSummary),
};

const BulkActionDrawer = props => {
  const {
    isBulkActionDrawerVisible,
    onAction,
    selectedBulkActionkey,
    showPrintOptionForUser,
    selectedSalesOrdersInfo,
    currentBulkNotificationId,
    currentBulkNotificationLastUpdatedAt,
  } = props;

  const BulkActionComponent = BULK_ACTION_VS_COMPONENT[selectedBulkActionkey];

  const closeBulkActionModal = useCallback(
    () =>
      onAction({
        type: CUSTOM_ACTIONS.CLOSE_BULK_ACTION_MODAL,
      }),
    [onAction],
  );

  return (
    <Drawer
      visible={isBulkActionDrawerVisible}
      className={styles.root}
      onClose={closeBulkActionModal}
      placement="bottom"
      height={600}
      headerStyle={styles.drawerTitle}
    >
      {_isNil(BulkActionComponent) ? (
        <Content>{__('Work in Progress...')}</Content>
      ) : (
        <PropertyControlledComponent controllerProperty={isBulkActionDrawerVisible}>
          <BulkActionComponent
            showPrintOptionForUser={showPrintOptionForUser}
            salesOrders={selectedSalesOrdersInfo}
            bulkRequestId={currentBulkNotificationId}
            closeBulkActionModal={closeBulkActionModal}
            lastUpdatedAt={currentBulkNotificationLastUpdatedAt}
            onAction={onAction}
          />
        </PropertyControlledComponent>
      )}
    </Drawer>
  );
};

BulkActionDrawer.propTypes = {
  onAction: PropTypes.func,
  showPrintOptionForUser: PropTypes.bool,
  selectedBulkActionkey: PropTypes.string,
  isBulkActionDrawerVisible: PropTypes.bool,
  selectedSalesOrdersInfo: PropTypes.array,
  currentBulkNotificationId: PropTypes.string,
  currentBulkNotificationLastUpdatedAt: PropTypes.number,
};

BulkActionDrawer.defaultProps = {
  onAction: _noop,
  showPrintOptionForUser: false,
  selectedBulkActionkey: EMPTY_STRING,
  isBulkActionDrawerVisible: false,
  selectedSalesOrdersInfo: EMPTY_ARRAY,
  currentBulkNotificationId: EMPTY_STRING,
  currentBulkNotificationLastUpdatedAt: 0,
};

export default memo(BulkActionDrawer);
