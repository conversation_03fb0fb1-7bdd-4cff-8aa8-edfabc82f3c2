import React, { useCallback, useMemo } from 'react';

import _filter from 'lodash/filter';
import _isEmpty from 'lodash/isEmpty';
import _noop from 'lodash/noop';

import cx from 'classnames';
import PropTypes from 'prop-types';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import { Button } from '@tekion/tekion-components/src/atoms';
import Icon, { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';
import Dropdown from '@tekion/tekion-components/src/molecules/DropDown';

import { BULK_ACTION_KEYS } from '../../constants/bulkActions.constants';
import { CUSTOM_ACTIONS } from '../../constants/general.constants';

import { getMenu } from './bulkActionDropdown.configs';
import { MENU_MAP } from './bulkActionDropdown.constants';
import { isBulkGenerateInvoiceEnabled } from './bulkActionDropdown.helpers';
import styles from './bulkActionDropdown.module.scss';

const BulkActionDropdown = props => {
  const { selectedRowIds, onAction, partsSettingsMap, currentSiteId } = props;

  const isDropdownDisabled = useMemo(() => _isEmpty(selectedRowIds), [selectedRowIds]);

  const filteredMenuMap = useMemo(() => {
    if (!isBulkGenerateInvoiceEnabled({ partsSettingsMap, currentSiteId })) {
      return _filter(MENU_MAP, menuItem => menuItem.key !== BULK_ACTION_KEYS.GENERATE_INVOICE);
    }

    return MENU_MAP;
  }, [partsSettingsMap, currentSiteId]);

  const triggerBulkAction = useCallback(
    bulkActionKey => {
      onAction({
        type: CUSTOM_ACTIONS.TRIGGER_BULK_ACTION,
        payload: {
          selectedRowIds,
          type: bulkActionKey,
        },
      });
    },
    [selectedRowIds, onAction],
  );

  return (
    <Dropdown
      overlay={getMenu(filteredMenuMap, triggerBulkAction)}
      trigger={['click']}
      disabled={isDropdownDisabled}
      className={cx(styles.dropdown)}
      placement="bottomRight"
    >
      <Button disabled={isDropdownDisabled} view={Button.VIEW.SECONDARY} className={cx(styles.placeholder)}>
        <div className={cx('m-r-8')}>{__('Action ')}</div>
        <Icon size={SIZES.S} className={cx(styles.placeholderIcon)}>
          icon-chevron-down
        </Icon>
      </Button>
    </Dropdown>
  );
};

BulkActionDropdown.propTypes = {
  selectedRowIds: PropTypes.object,
  onAction: PropTypes.func,
  partsSettingsMap: PropTypes.object,
  currentSiteId: PropTypes.string,
};

BulkActionDropdown.defaultProps = {
  selectedRowIds: EMPTY_OBJECT,
  onAction: _noop,
  partsSettingsMap: null,
  currentSiteId: null,
};

export default React.memo(BulkActionDropdown);
