import { Map as IMap } from 'immutable';

import getCurrentSiteIdFromLS from '@tekion/tekion-base/utils/getCurrentSiteIdFromLS';

import GeneralSettingsReader from 'readers/generalSettings.reader';

import { getIsNewTaxCodeEnabled } from 'utils/general';
import partsEnv from 'utils/partsEnv';

export const isBulkGenerateInvoiceEnabled = ({ partsSettingsMap = IMap(), currentSiteId }) => {
  const siteId = currentSiteId || getCurrentSiteIdFromLS();
  const partsSettings = partsSettingsMap?.get(siteId) || partsEnv.partsSettings;
  const autoGenerateMultiInvoiceInSalesOrder =
    GeneralSettingsReader.autoGenerateMultiInvoiceInSalesOrder(partsSettings);
  return !autoGenerateMultiInvoiceInSalesOrder && getIsNewTaxCodeEnabled();
};
