import getCurrentSiteIdFromLS from '@tekion/tekion-base/utils/getCurrentSiteIdFromLS';

import GeneralSettingsReader from 'readers/generalSettings.reader';

import { getIsNewTaxCodeEnabled } from 'utils/general';

export const isBulkGenerateInvoiceEnabled = (partsSettingsMap, currentSiteId) => {
  const siteId = currentSiteId || getCurrentSiteIdFromLS();
  const partsSettings = partsSettingsMap?.get(siteId);
  const autoGenerateMultiInvoiceInSalesOrder =
    GeneralSettingsReader.autoGenerateMultiInvoiceInSalesOrder(partsSettings);
  return !autoGenerateMultiInvoiceInSalesOrder && getIsNewTaxCodeEnabled();
};
