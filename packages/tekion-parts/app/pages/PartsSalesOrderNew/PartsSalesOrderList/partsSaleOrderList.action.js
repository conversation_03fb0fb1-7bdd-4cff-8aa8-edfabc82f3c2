import _compact from 'lodash/compact';
import _difference from 'lodash/difference';
import _forEach from 'lodash/forEach';
import _get from 'lodash/get';
import _groupBy from 'lodash/groupBy';
import _indexOf from 'lodash/indexOf';
import _isEmpty from 'lodash/isEmpty';
import _keyBy from 'lodash/keyBy';
import _keysIn from 'lodash/keysIn';
import _map from 'lodash/map';
import _merge from 'lodash/merge';
import _reduce from 'lodash/reduce';
import _uniq from 'lodash/uniq';
import _without from 'lodash/without';

import { compose } from 'recompose';

import {
  getLoadingItemsAction,
  getUpdatePageAction,
  getPageRowAction,
  getErrorItemsAction,
  getSetAdditionalDataAction,
} from '@tekion/tekion-base/actionCreators/tableItems';
import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import Request from '@tekion/tekion-base/builders/request';
import { RESOURCE_TYPE } from '@tekion/tekion-base/bulkResolvers/constants/resourceType';
import OPERATORS from '@tekion/tekion-base/constants/filterOperators';
import NOTES_ASSET_TYPES from '@tekion/tekion-base/constants/notesAssetTypes';
import { getLookupByKeys } from '@tekion/tekion-base/services/lookupService';
import { fetchBulkNotes } from '@tekion/tekion-base/services/notesServices';
import {
  fetchSalesOrderListApi,
  fetchSalesOrderQuickFiltersCount,
} from '@tekion/tekion-base/services/parts/salesOrderServices';
import { getCurrentTime } from '@tekion/tekion-base/utils/dateUtils';
import { getErrorMessage } from '@tekion/tekion-base/utils/errorUtils';
import { tget } from '@tekion/tekion-base/utils/general';
import moneyUtils from '@tekion/tekion-base/utils/money';

import { resolveUsers } from '@tekion/tekion-business/src/actions/parts/userActions';
import { siteFilterESPayloadSanitizer } from '@tekion/tekion-business/src/appServices/parts/helpers/dealerSiteFilter.helpers';
import {
  getSelectedFiltersWithWarehouse,
  getQuickFiltersPayloadWithWarehouse,
} from '@tekion/tekion-business/src/appServices/parts/helpers/warehouseManagement.helpers';

import { getBatchedActionsToDispatch } from '@tekion/tekion-components/src/helpers/tableItemHelper';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import { ASSET_TYPE } from '@tekion/tekion-components/src/organisms/PdfViewerPartsV2/PdfViewer.constants';
import { isPageItemsLoaded, getFilterValue } from '@tekion/tekion-components/src/reducers/tableItemReducer';

import {
  getAmountDueFilter,
  getAmountPaidFilter,
  SALES_ORDER_LIST_VIEW_FILTER_KEY,
  getSORReceivedAndPrepaidHoldFilterValue,
} from '@tekion/tekion-widgets/src/appServices/parts/salesOrder/salesOrder.filters';

import { getCurrentUser } from 'utils';

import { bulkActionFetchInProgressRequests } from 'services/bulkActionServices';

import { getSearchableFields } from 'shared/general/tablemanager/helper';

import { sanitizeTenantUserMinimal } from 'utils/apiUtils';
import { hasExistOperator } from 'utils/filterUtils';
import { getIsMultiOemSwitchEnabled, getIsPrepaidSalesEnabled, getIsMultiWarehouseEnabled } from 'utils/general';
import partsEnv from 'utils/partsEnv';

import { getPermanentCustomerIds } from '../../PartSalesOrder/utils/salesOrder.utils';
import { SALES_ORDER_NOTE_ASSET_TYPE } from '../partsSalesOrder.constants';

import { COLUMN_KEYS } from './constants/columnKeys.constant';
import { ITEM_TYPE } from './constants/general.constants';
import { SEARCH_FIELD_TYPE, SEARCH_FIELD_TYPE_VS_OVERRIDE_VALUE } from './constants/search.constant';
import { getBulkActionNotifRenderConfig } from './helpers/bulkActions.helpers';
import {
  changeExistOperatorsToBooleanValue,
  removeUnwantedFilters,
  getCurrentTableStateForSOListPolling,
  getQuickFilterVSCount,
  getFacetSearchRequest,
  getFormattedPayload,
  getCustomerIds,
  formatSalesOrderListData,
  getUserIds,
  getSalesOrderIds,
  getQuickFiltersPayloadV2,
} from './helpers/general.helper';
import {
  generateSalesOrderFileAPI,
  markCustomerReceivedAPI,
  fetchSalesOrderInvoiceList,
  fetchPODetailsAPI,
} from './partsSalesOrderList.api';

// import SALES_ORDER_API_KEYS from '../readers/SalesOrder.reader';
const getQuickFiltersCount = quickFiltersPayload =>
  fetchSalesOrderQuickFiltersCount(quickFiltersPayload)
    .then(response => {
      const quickFiltersData = _get(response, 'data.data', EMPTY_ARRAY);
      return getQuickFilterVSCount(quickFiltersData);
    })
    .catch(err => {
      toaster(TOASTER_TYPE.ERROR, getErrorMessage(err, __('Unable to fetch quick filters count.')));
      return EMPTY_OBJECT;
    });

const addSortDetailToRequest = (request, sortType, key) => {
  if (key === COLUMN_KEYS.SALE_AMOUNT) {
    /* changing the sort key for sale amount as this value is tMoney and sorting takes on the actual value */
    return request.addSort(`${COLUMN_KEYS.SALE_AMOUNT}.amount`, sortType);
  }
  if (key === COLUMN_KEYS.GROSS_PROFIT) {
    return request.addSort('summary.totalGrossProfit.amount', sortType);
  }
  if (key === COLUMN_KEYS.GROSS_PROFIT_PERCENT) {
    return request.addSort('summary.totalGrossProfitPercentage', sortType);
  }
  if (key === COLUMN_KEYS.AMOUNT_PAID) {
    /* changing the sort key for sale amount as this value is tMoney and sorting takes on the actual value */
    return request.addSort('invoice.customerPay.paidAmount.amount', sortType);
  }
  if (key === COLUMN_KEYS.AMOUNT_DUE) {
    /* changing the sort key for sale amount as this value is tMoney and sorting takes on the actual value */
    return request.addSort('invoice.customerPay.dueAmount.amount', sortType);
  }
  return request.addSort(key, sortType);
};

const getSearchRequest = (params = EMPTY_OBJECT) => {
  const { sortDetails } = params;
  const request = new Request().setPage(params.currentPage, params.rows).setSearchString(params.searchQuery);
  return _reduce(sortDetails, addSortDetailToRequest, request);
};

// We can use customer bulk resolver for SO list view to get the display id of the customer if display id is not present in SO document
// No need to resolve for deleted customer as its entity will be removed from customer management
const fetchCustomerDetails = customerIds =>
  getLookupByKeys(RESOURCE_TYPE.CUSTOMER, customerIds)
    .then(response => response)
    .catch(() => EMPTY_OBJECT);

const fetchUsers = userIds =>
  getLookupByKeys(RESOURCE_TYPE.TENANT_USER_MINIMAL_V2, userIds)
    .then(response => sanitizeTenantUserMinimal(response))
    .catch(() => EMPTY_OBJECT);

const addSearchFields = (requestDTO, searchField) => ({
  ...requestDTO,
  searchFields: getSearchableFields(searchField, SEARCH_FIELD_TYPE, SEARCH_FIELD_TYPE_VS_OVERRIDE_VALUE),
});

export function fetchSalesOrderList(data, includeTwoDecimalPlacesInAmountFields) {
  return async (dispatch, getState) => {
    try {
      const {
        searchField,
        isQuickFilterSelection,
        currentFilterList,
        quickFilters,
        multiSearchSelectedItems,
        expandedRowDetails,
        shouldShowLoader,
        resolvedPartDetailsByIdentifierId,
        reasonsList,
        partsSettingsMap,
        bulkActionNotifications = EMPTY_ARRAY,
      } = data;
      // expandedRowDetails { order: true }
      const currentAdditionalData = _get(getState(), `${ITEM_TYPE}.tableItems.additional`);
      compose(dispatch, getSetAdditionalDataAction)(ITEM_TYPE, {
        additional: {
          ...currentAdditionalData,
          shouldShowLoader,
        },
      });
      compose(dispatch, getLoadingItemsAction)(ITEM_TYPE, data);
      const requestDTO = getSearchRequest(data);
      const selectedFilters = removeUnwantedFilters(data.selectedFilters);
      const selectedWarehouse = partsEnv.userPreferredWarehouseId;
      const selectedFiltersWithWarehouse = getSelectedFiltersWithWarehouse(
        selectedFilters,
        selectedWarehouse,
        getIsMultiWarehouseEnabled(),
      );
      if (selectedFiltersWithWarehouse && selectedFiltersWithWarehouse.length > 0) {
        _forEach(selectedFiltersWithWarehouse, item => {
          if (
            item.type === getAmountDueFilter(includeTwoDecimalPlacesInAmountFields).id ||
            item.type === getAmountPaidFilter(includeTwoDecimalPlacesInAmountFields).id
          ) {
            requestDTO.addFilter(
              item.type,
              item.operator,
              _map(item.values, moneyUtils.toInt),
              false,
              item.type,
              EMPTY_OBJECT,
            );
            return;
          }

          if (getIsPrepaidSalesEnabled() && item.type === SALES_ORDER_LIST_VIEW_FILTER_KEY.SOR_RECEIVED) {
            const { operator } = item || EMPTY_OBJECT;
            const sorReceivedOrPrepaidHoldFilterValue = getSORReceivedAndPrepaidHoldFilterValue({ operator });
            const filters = {
              orFilters: sorReceivedOrPrepaidHoldFilterValue?.orFilters,
              field: SALES_ORDER_LIST_VIEW_FILTER_KEY.SOR_RECEIVED,
            };

            if (operator === OPERATORS.EXISTS) {
              requestDTO.addFilter(null, OPERATORS.BOOL, null, null, null, filters, {
                isBooleanOperator: true,
                skipValueCheck: true,
              });
            } else {
              _forEach(sorReceivedOrPrepaidHoldFilterValue?.orFilters, filterValue => {
                requestDTO.addFilter(
                  filterValue?.key,
                  filterValue?.operator,
                  filterValue?.values,
                  false,
                  filterValue?.key,
                  EMPTY_OBJECT,
                );
              });
            }
            return;
          }

          if (item.type === SALES_ORDER_LIST_VIEW_FILTER_KEY.DEPARTMENT) {
            const hasDefaultDepartment = _indexOf(item.values, null) >= 0;
            const restDepartments = _without(item.values, null);
            const filters = {
              orFilters: [
                ...(hasDefaultDepartment
                  ? [
                      {
                        field: item.type,
                        operator: OPERATORS.NOT_EXISTS,
                      },
                    ]
                  : []),
                ...(!_isEmpty(restDepartments)
                  ? [
                      {
                        field: item.type,
                        operator: item.operator,
                        values: restDepartments,
                        key: item.type,
                      },
                    ]
                  : []),
              ],
            };
            requestDTO.addFilter(null, OPERATORS.BOOL, null, null, null, filters, {
              isBooleanOperator: true,
              skipValueCheck: true,
            });
            return;
          }
          requestDTO.addFilter(item.type, item.operator, item.values, false, item.type, EMPTY_OBJECT, {
            skipValueCheck: hasExistOperator(item),
          });
        });
      }

      const requestDTOWithFilters = {
        ...requestDTO,
        filters: changeExistOperatorsToBooleanValue(requestDTO.filters),
      };

      const requestDTOWithSearchFields = addSearchFields(requestDTOWithFilters, searchField);
      const sanitizedRequestDTO = siteFilterESPayloadSanitizer(requestDTOWithSearchFields, {
        isMultiSiteEnabled: getIsMultiOemSwitchEnabled(),
      });
      const sanitizedRequestDTOWithIncludeFields = {
        ...getFormattedPayload(sanitizedRequestDTO),
        includeFields: [
          'id',
          'orderNo',
          'orderType',
          'createdTime',
          'modifiedTime',
          'saleType',
          'saleAmount',
          'userId',
          'poNumber',
          'paymentStatus',
          'customer',
          'onOrdered',
          'status',
          'refType',
          'refId',
          'refNumber',
          'invoice',
          'sorRequestIds',
          'purchaseOrderHoldExists',
          'returnDetails',
          'siteId',
          'saleSubTypeId',
          'partSaleDetails',
          'partCounterPersonName',
          'migratedPartReturn',
          'summary',
          'currentInvoiceSeq',
          'deliveryMethod',
          'departmentName',
          'departmentId',
          'sourceType',
          'prepaidOrderExists',
          'prepaidHoldExists',
          'estimatedArrivalTime',
          'partReturn',
          'quoteNumber',
          'autoFill',
          'currency',
          ...(getIsMultiWarehouseEnabled() ? ['warehouseId'] : EMPTY_ARRAY),
        ],
      };
      const searchTextFields = getFacetSearchRequest(multiSearchSelectedItems);
      const payloadForFacetedSearch = {
        ...sanitizedRequestDTOWithIncludeFields,
        searchTextFields,
      };
      const quickFiltersPayloadV2 = getQuickFiltersPayloadV2(currentFilterList, payloadForFacetedSearch?.filters);
      const quickFiltersPayloadWithWarehouseFilter = getQuickFiltersPayloadWithWarehouse(
        quickFiltersPayloadV2,
        selectedWarehouse,
        getIsMultiWarehouseEnabled(),
      );
      const [response, quickFiltersCount] = await Promise.all([
        fetchSalesOrderListApi(payloadForFacetedSearch),
        getQuickFiltersCount(quickFiltersPayloadWithWarehouseFilter),
      ]);
      const items = _get(response, 'data.data.hits', EMPTY_ARRAY);
      const formattedListData = formatSalesOrderListData({
        salesOrderListData: items,
        parentAssetType: 'SALES_ORDER',
      });
      const currentAdditionalResolvedData = _get(getState(), `${ITEM_TYPE}.tableItems.additional.resolvedData`);
      const resolvedUsers = _get(currentAdditionalResolvedData, 'user.data', EMPTY_OBJECT);
      const resolvedCustomers = _get(currentAdditionalResolvedData, 'customer.data', EMPTY_OBJECT);
      // did caching here for minor performance improvements, and remove redundant lookup API calls and shimmers
      const additionalResolvedData = {
        user: { isLoading: true, data: resolvedUsers },
        customer: { isLoading: true, data: resolvedCustomers },
      };

      compose(dispatch, getSetAdditionalDataAction)(ITEM_TYPE, {
        additional: {
          quickFiltersCount,
          isQuickFilterSelection,
          currentFilterList,
          quickFilters,
          multiSearchSelectedItems,
          resolvedData: additionalResolvedData,
          expandedRowDetails,
          shouldShowLoader: false,
          lastUpdatedTime: getCurrentTime(),
          resolvedPartDetailsByIdentifierId,
          reasonsList,
          partsSettingsMap,
          bulkActionNotifications,
        },
      });
      compose(
        dispatch,
        getBatchedActionsToDispatch,
      )({
        ...data,
        selectedFilters,
        pageNumber: data.currentPage,
        itemType: ITEM_TYPE,
        rows: _get(data, 'pageInfo.rows', 0),
        items: formattedListData,
        totalCount: _get(response, 'data.data.count', 0),
      });
      resolveUser(dispatch, getState, { items, itemType: ITEM_TYPE });
      resolveCustomer(dispatch, getState, { items, itemType: ITEM_TYPE });
      resolveNotes(dispatch, getState, { items, itemType: ITEM_TYPE });
      resolveBulkNotifications(dispatch, getState, { items, itemType: ITEM_TYPE });
    } catch (error) {
      toaster('error', __('Something went wrong while fetching the table data'));
      // eslint-disable-next-line no-param-reassign
      data.error = error;
      compose(dispatch, getErrorItemsAction)(ITEM_TYPE, data);
    }
  };
}

const resolveBulkNotifications = async (dispatch, getState, { itemType }) => {
  try {
    const additional = _get(getState(), `${itemType}.tableItems.additional`);
    const { id: currentUserId } = getCurrentUser();
    const inProgressBulkNotificationsResponse = await bulkActionFetchInProgressRequests({
      fetchLineCountRequest: true,
      parentAssetType: ASSET_TYPE.SALES_ORDER,
      userId: currentUserId,
    });
    const inProgressBulkNotifications = _get(inProgressBulkNotificationsResponse, 'data.data', EMPTY_ARRAY);
    const formattedBulkNotifications = _map(inProgressBulkNotifications, req => getBulkActionNotifRenderConfig(req));
    const updatedAdditional = {
      ...additional,
      bulkActionNotifications: formattedBulkNotifications,
    };
    dispatch(getSetAdditionalDataAction(itemType, { additional: updatedAdditional }));
  } catch (error) {
    console.error(error);
  }
};

const resolveCustomer = async (dispatch, getState, { items, itemType }) => {
  const customerIdsToBeResolved = getPermanentCustomerIds(items);
  const currentAdditional = _get(getState(), `${itemType}.tableItems.additional`);
  const currentResolvedCustomers = _get(currentAdditional, 'resolvedData.customer.data', EMPTY_OBJECT);
  const currentResolvedCustomerIds = _keysIn(currentResolvedCustomers);
  const unresolvedCustomerIds = _difference(customerIdsToBeResolved, currentResolvedCustomerIds);
  const resolvedCustomers = _isEmpty(unresolvedCustomerIds)
    ? currentResolvedCustomers
    : await fetchCustomerDetails(unresolvedCustomerIds);
  const allResolvedCustomers = _merge(resolvedCustomers, currentResolvedCustomers);
  const additional = _get(getState(), `${itemType}.tableItems.additional`);
  const updatedAdditional = {
    ...additional,
    resolvedData: {
      ..._get(additional, 'resolvedData'),
      customer: { isLoading: false, data: allResolvedCustomers },
    },
  };
  dispatch(getSetAdditionalDataAction(itemType, { additional: updatedAdditional }));
};
const resolveNotes = async (dispatch, getState, { items, itemType }) => {
  const salesOrderIds = getSalesOrderIds(items);
  const customerIds = getCustomerIds(items);
  const salesOrderNotesPromise = _isEmpty(salesOrderIds)
    ? Promise.resolve(EMPTY_ARRAY)
    : fetchBulkNotes(SALES_ORDER_NOTE_ASSET_TYPE, salesOrderIds);
  const customerNotesPromise = _isEmpty(customerIds)
    ? Promise.resolve(EMPTY_ARRAY)
    : fetchBulkNotes(NOTES_ASSET_TYPES.CUSTOMER, customerIds);
  const [salesOrderNotes, customerNotes] = await Promise.all([salesOrderNotesPromise, customerNotesPromise]);
  const userIdsToBeResolved = _compact(
    _uniq(
      _reduce(
        [...salesOrderNotes, ...customerNotes],
        (notesUserList, note) => [...notesUserList, note.userId, note.createdBy],
        [],
      ),
    ),
  );
  const resolvedUsersById = await resolveUsers(userIdsToBeResolved);
  const salesOrderNotesMap = _groupBy(salesOrderNotes, 'assetId');
  const customerNotesMap = _groupBy(customerNotes, 'assetId');
  const additional = _get(getState(), `${itemType}.tableItems.additional`);
  const updatedAdditional = {
    ...additional,
    resolvedData: {
      ..._get(additional, 'resolvedData'),
      notes: { isLoading: false, data: { salesOrderNotesMap, customerNotesMap, resolvedUsersById } },
    },
  };
  dispatch(getSetAdditionalDataAction(itemType, { additional: updatedAdditional }));
};
const resolveUser = async (dispatch, getState, { items, itemType }) => {
  const userIdsToBeResolved = getUserIds(items);
  const currentAdditional = _get(getState(), `${itemType}.tableItems.additional`);
  const currentResolvedUsers = _get(currentAdditional, 'resolvedData.user.data', EMPTY_OBJECT);
  const currentResolvedUserIds = _keysIn(currentResolvedUsers);
  const unresolvedUserIds = _difference(userIdsToBeResolved, currentResolvedUserIds);
  const resolvedUsers = _isEmpty(unresolvedUserIds) ? EMPTY_OBJECT : await fetchUsers(unresolvedUserIds);

  const usersById = _keyBy(resolvedUsers, 'id');
  const allResolvedUsers = _merge(usersById, currentResolvedUsers);
  const additional = _get(getState(), `${itemType}.tableItems.additional`);
  const updatedAdditional = {
    ...additional,
    resolvedData: {
      ..._get(additional, 'resolvedData'),
      user: { isLoading: false, data: allResolvedUsers },
    },
  };
  dispatch(getSetAdditionalDataAction(itemType, { additional: updatedAdditional }));
};

export function pollSalesOrderList(data) {
  return async (dispatch, getState) => {
    const currentTableState = getCurrentTableStateForSOListPolling({
      ITEM_TYPE,
      getReduxState: getState,
    });
    if (currentTableState.isTableLoading) return;
    try {
      const currentAdditionalData = _get(getState(), `${ITEM_TYPE}.tableItems.additional`);
      compose(dispatch, getSetAdditionalDataAction)(ITEM_TYPE, {
        additional: {
          ...currentAdditionalData,
          isFetchingSOListByPusherEvent: true,
        },
      });
      const {
        searchField,
        isQuickFilterSelection,
        currentFilterList,
        quickFilters,
        multiSearchSelectedItems,
        resolvedPartDetailsByIdentifierId,
        reasonsList,
      } = data;
      const requestDTO = getSearchRequest(data);
      const selectedFilters = removeUnwantedFilters(data.selectedFilters);
      const selectedWarehouse = partsEnv.userPreferredWarehouseId;
      const selectedFiltersWithWarehouse = getSelectedFiltersWithWarehouse(
        selectedFilters,
        selectedWarehouse,
        getIsMultiWarehouseEnabled(),
      );

      if (selectedFiltersWithWarehouse && selectedFiltersWithWarehouse.length > 0) {
        _forEach(selectedFiltersWithWarehouse, item => {
          if (getIsPrepaidSalesEnabled() && item.type === SALES_ORDER_LIST_VIEW_FILTER_KEY.SOR_RECEIVED) {
            const { operator } = item || EMPTY_OBJECT;
            const sorReceivedOrPrepaidHoldFilterValue = getSORReceivedAndPrepaidHoldFilterValue({ operator });
            const filters = { orFilters: sorReceivedOrPrepaidHoldFilterValue?.orFilters };

            if (operator === OPERATORS.EXISTS) {
              requestDTO.addFilter(null, OPERATORS.BOOL, null, null, null, filters, {
                isBooleanOperator: true,
                skipValueCheck: true,
              });
            } else {
              _forEach(sorReceivedOrPrepaidHoldFilterValue?.orFilters, filterValue => {
                requestDTO.addFilter(
                  filterValue?.key,
                  filterValue?.operator,
                  filterValue?.values,
                  false,
                  filterValue?.key,
                  EMPTY_OBJECT,
                );
              });
            }
            return;
          }

          requestDTO.addFilter(item.type, item.operator, item.values, false, item.type, EMPTY_OBJECT, {
            skipValueCheck: hasExistOperator(item),
          });
        });
      }

      const requestDTOWithFilters = {
        ...requestDTO,
        filters: changeExistOperatorsToBooleanValue(requestDTO.filters),
      };

      const quickFiltersPayload = getQuickFiltersPayloadV2(currentFilterList, requestDTOWithFilters?.filters);
      const quickFiltersPayloadWithWarehouseFilter = getQuickFiltersPayloadWithWarehouse(
        quickFiltersPayload,
        selectedWarehouse,
        getIsMultiWarehouseEnabled(),
      );
      const requestDTOWithSearchFields = addSearchFields(requestDTOWithFilters, searchField);
      const sanitizedRequestDTO = siteFilterESPayloadSanitizer(requestDTOWithSearchFields, {
        isMultiSiteEnabled: getIsMultiOemSwitchEnabled(),
      });
      const searchTextFields = getFacetSearchRequest(multiSearchSelectedItems);

      const sanitizedRequestDTOWithIncludeFields = {
        ...getFormattedPayload(sanitizedRequestDTO),
        includeFields: [
          'id',
          'orderNo',
          'orderType',
          'createdTime',
          'modifiedTime',
          'saleType',
          'saleAmount',
          'userId',
          'poNumber',
          'paymentStatus',
          'customer',
          'onOrdered',
          'status',
          'refType',
          'refId',
          'refNumber',
          'invoice',
          'sorRequestIds',
          'purchaseOrderHoldExists',
          'returnDetails',
          'siteId',
          'saleSubTypeId',
          'partSaleDetails',
          'partCounterPersonName',
          'migratedPartReturn',
          'summary',
          'currentInvoiceSeq',
          'deliveryMethod',
          'departmentName',
          'departmentId',
          'sourceType',
          'prepaidOrderExists',
          'prepaidHoldExists',
          'estimatedArrivalTime',
          'partReturn',
          'quoteNumber',
          'autoFill',
          'currency',
          ...(getIsMultiWarehouseEnabled() ? ['warehouseId'] : EMPTY_ARRAY),
        ],
      };

      const payloadForFacetedSearch = {
        ...sanitizedRequestDTOWithIncludeFields,
        searchTextFields,
      };
      const [response, quickFiltersCount] = await Promise.all([
        fetchSalesOrderListApi(payloadForFacetedSearch),
        getQuickFiltersCount(quickFiltersPayloadWithWarehouseFilter),
      ]);
      const items = _get(response, 'data.data.hits', EMPTY_ARRAY);
      const formattedListData = formatSalesOrderListData({ salesOrderListData: items });
      const additionalResolvedData = _get(getState(), `${ITEM_TYPE}.tableItems.additional.resolvedData`);
      const userDetails = _get(additionalResolvedData, 'user.data', EMPTY_OBJECT);
      const customerDetails = _get(additionalResolvedData, 'customer.data', EMPTY_OBJECT);
      const updatedAdditionalResolvedData = {
        user: { isLoading: true, data: userDetails },
        customer: { isLoading: true, data: customerDetails },
      };
      compose(dispatch, getSetAdditionalDataAction)(ITEM_TYPE, {
        additional: {
          quickFiltersCount,
          isQuickFilterSelection,
          currentFilterList,
          quickFilters,
          multiSearchSelectedItems,
          resolvedData: updatedAdditionalResolvedData,
          lastUpdatedTime: getCurrentTime(),
          isFetchingSOListByPusherEvent: false,
          resolvedPartDetailsByIdentifierId,
          reasonsList,
        },
      });
      compose(
        dispatch,
        getBatchedActionsToDispatch,
      )({
        ...data,
        selectedFilters,
        pageNumber: data.currentPage,
        itemType: ITEM_TYPE,
        rows: _get(data, 'pageInfo.rows', 0),
        items: formattedListData,
        totalCount: _get(response, 'data.data.count', 0),
      });
      resolveUser(dispatch, getState, { items, itemType: ITEM_TYPE });
      resolveCustomer(dispatch, getState, { items, itemType: ITEM_TYPE });
      resolveNotes(dispatch, getState, { items, itemType: ITEM_TYPE });
      // removing resolveBulkNotifications to avoid high frequency updates
    } catch (error) {
      // no handling required
    }
  };
}

export const changePage = data => (dispatch, getState) => {
  const { tableItems } = _get(getState(), ITEM_TYPE);
  const filterValue = getFilterValue(tableItems);

  if (!isPageItemsLoaded(tableItems, filterValue, data.currentPage, _get(data, 'pageInfo.rows', 0))) {
    compose(dispatch, fetchSalesOrderList)(data);
    return;
  }

  dispatch([
    getPageRowAction(ITEM_TYPE, {
      filterValue,
      rows: data.pageInfo.rows,
    }),
    getUpdatePageAction(data.currentPage, {
      itemType: ITEM_TYPE,
      filterValue,
    }),
  ]);
};

export const fetchAndDownloadSalesOrder = payload => {
  const finalPayload = getFormattedPayload(
    siteFilterESPayloadSanitizer(payload, { isMultiSiteEnabled: getIsMultiOemSwitchEnabled() }),
  );
  return generateSalesOrderFileAPI(finalPayload);
};

export const markCustomerReceivedAction = payload => markCustomerReceivedAPI(payload);

export const fetchSalesOrderInvoiceAction = async salesOrderId => {
  const response = await fetchSalesOrderInvoiceList(salesOrderId);
  const data = _get(response, ['data', 'data'], EMPTY_OBJECT);
  return data;
};

export const fetchPurchaseOrderDetailAction = async payload => {
  const response = await fetchPODetailsAPI(payload);
  return tget(response, 'data.data', EMPTY_OBJECT);
};
