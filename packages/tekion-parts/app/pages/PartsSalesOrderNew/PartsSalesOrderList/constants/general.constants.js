import _get from 'lodash/get';

// import { getSiteNameFromSiteId } from 'tbase/helpers/oemSite';
import { SORT } from '@tekion/tekion-base/builders/request/Request.constants';
import OPERATORS from '@tekion/tekion-base/constants/filterOperators';
import { ENTITY_REFERENCE_TYPES } from '@tekion/tekion-base/constants/parts/entityReferenceTypes';
import {
  DRAFT,
  INVOICED,
  DELIVERED,
  PARTIALLY_DELIVERED,
  REOPENED,
  VOIDED,
} from '@tekion/tekion-base/constants/parts/salesOrderFilters';

import { COPY_TYPE } from '@tekion/tekion-components/src/organisms/PdfViewerParts/PdfViewer.constants';

import { SALE_STATUS_FILTER } from '@tekion/tekion-widgets/src/appServices/parts/salesOrder/salesOrder.filters';

import { withPartsReducerBasePath } from 'helpers/moduleFederation.helpers';
import { getShortcutCtrlOrAltKeyForCurrentOS } from 'helpers/parts.helper';

import { EPCCartIconRenderer } from 'shared/components/SnapOnProRenderer';
import { EPC_CART_SOURCE } from 'shared/components/SnapOnProRenderer/epc.constants';
import { LABEL_FOR_CMD_CTRL, CMD_FOR_MAC_CTRL_FOR_WINDOWS } from 'shared/general/tablemanager/constants';
import { SALES_ORDER_STATIC_FILTER_GROUP_VARIANT } from 'shared/SalesOrder/constants';

import ButtonWithTooltip from '../Components/ButtonWithTooltip';
import SalesOrderHeadingDropdown from '../Components/SalesOrderHeadingDropdown/SalesOrderHeadingDropdown';
import styles from '../partsSalesOrderList.module.scss';

export const DEFAULT_PAGE_SIZE = 20;

export const TABLE_ROW_HEIGHT = 40;

export const TABLE_PAGE_SIZE_OPTIONS = [10, 15, 20, 30, 50];

export const ITEM_TYPE = withPartsReducerBasePath('partsSalesOrder');

export const PREFERENCE_SERVICE_ITEM_NAME = 'SO';

export const TABLE_MANAGER_PROPS = {
  isSearchActive: true,
  showSearchByField: false,
  showFilter: true,
  showHeader: true,
  showSubHeader: true,
  showL3Header: true,
  showPersonaPreferenceSave: true,
};

export const CUSTOM_ACTIONS = {
  CREATE_SALES_ORDER: 'CREATE_SALES_ORDER',
  OPEN_CREDIT_MEMO_MODAL: 'OPEN_CREDIT_MEMO_MODAL',
  CLOSE_CREDIT_MEMO_MODAL: 'CLOSE_CREDIT_MEMO_MODAL',
  CREATE_CREDIT_MEMO: 'CREATE_CREDIT_MEMO',
  TOGGLE_SEARCH_SHORTCUT: 'TOGGLE_SEARCH_SHORTCUT',
  TABLE_ITEMS_REFETCH_LIST: 'TABLE_ITEMS_REFETCH_LIST',
  TOGGLE_EXPAND_TABLE_ROW: 'EXPAND_TABLE_ROW',
  TRIGGER_VIEW_PDF: 'TRIGGER_VIEW_PDF',
  TRIGGER_PRINT_PDF: 'TRIGGER_PRINT_PDF',
  CHECK_PENDING_REQUEST_EXPIRATION: 'CHECK_PENDING_REQUEST_EXPIRATION',
  BULK_MARK_CUSTOMER_RECEIVED: 'BULK_MARK_CUSTOMER_RECEIVED',
  TRIGGER_HEADING_DROPDOWN_ACTION: 'TRIGGER_HEADING_DROPDOWN_ACTION',

  SET_SELECTED_ROWS: 'SET_SELECTED_ROWS',
  TRIGGER_BULK_ACTION: 'TRIGGER_BULK_ACTION',
  OPEN_BULK_ACTION_MODAL: 'OPEN_BULK_ACTION_MODAL',
  CLOSE_BULK_ACTION_MODAL: 'CLOSE_BULK_ACTION_MODAL',
  ADD_BULK_ACTION_NOTIFICATION: 'ADD_BULK_ACTION_NOTIFICATION',
  CLOSE_BULK_ACTION_NOTIFICATION: 'CLOSE_BULK_ACTION_NOTIFICATION',
  TOGGLE_ALL_ROWS_SELECTED: 'TOGGLE_ALL_ROWS_SELECTED',
  TOGGLE_ROW_SELECTED: 'TOGGLE_ROW_SELECTED',
  BULK_INVOICE_CREATION: 'BULK_INVOICE_CREATION',

  EMAIL_PDF_V2: 'EMAIL_PDF_V2',
  PRINT_PDF_V2: 'PRINT_PDF_V2',
  LOCAL_PRINT_BACKEND_PDF: 'LOCAL_PRINT_BACKEND_PDF',
  ENSURE_DOCUMENT_SERVICE_PDF: 'ENSURE_DOCUMENT_SERVICE_PDF',
  REGENERATE_PDF_DOCUMENT_SERVICE: 'REGENERATE_PDF_DOCUMENT_SERVICE',
  SHOW_WATERMARK_DOCUMENT_SERVICE: 'SHOW_WATERMARK_DOCUMENT_SERVICE',
};

export const CREATE_SALES_ORDER_SHORTCUT = {
  held: [['Meta'], ['Control']],
  ordered: ['Enter'],
  label: __('{{- labelForCmd}} + Enter', { labelForCmd: LABEL_FOR_CMD_CTRL }),
  key: CUSTOM_ACTIONS.CREATE_SALES_ORDER,
};

export const CREATE_SALES_ORDER_SHORTCUT_FOR_SO_V3_ENABLED_DEALER = {
  keys: {
    DEFAULT: ['mod+enter'],
  },
  label: __('{{- labelForCmd}} + Enter', { labelForCmd: LABEL_FOR_CMD_CTRL }),
  global: true,
  id: CUSTOM_ACTIONS.CREATE_SALES_ORDER,
};

export const SELECT_ALL_CHECKBOXES_SHORTCUT = {
  keys: {
    DEFAULT: [`${_get(getShortcutCtrlOrAltKeyForCurrentOS(), 'key')}+shift+a`],
  },
  global: true,
  label: __('{{labelForCtrl}} + Shift + A', { labelForCtrl: _get(getShortcutCtrlOrAltKeyForCurrentOS(), 'label') }),
  id: CUSTOM_ACTIONS.TOGGLE_ALL_ROWS_SELECTED,
};

export const CREATE_CREDIT_MEMO_SHORTCUT = {
  keys: {
    DEFAULT: ['c+m'],
  },
  label: __('C + M'),
  global: false,
  id: CUSTOM_ACTIONS.OPEN_CREDIT_MEMO_MODAL,
};

export const TOGGLE_SEARCH_SHORTCUT = {
  held: [[CMD_FOR_MAC_CTRL_FOR_WINDOWS, 'Shift']],
  ordered: ['l'],
  triggerWithCapsLock: true,
  label: __('{{- labelForCmd}} + Shift + L', { labelForCmd: LABEL_FOR_CMD_CTRL }),
  key: CUSTOM_ACTIONS.TOGGLE_SEARCH_SHORTCUT,
};

export const DEFAULT_PSO_DOWNLOAD_DETAILS = {
  FILE_NAME: 'Sales Order',
  SORT_KEY: 'modifiedTime',
};

export const CREATE_CREDIT_MEMO_BUTTON = {
  renderer: ButtonWithTooltip,
  action: CUSTOM_ACTIONS.OPEN_CREDIT_MEMO_MODAL,
  renderOptions: {
    children: __('Create Credit Memo'),
    tooltipText: CREATE_CREDIT_MEMO_SHORTCUT.label,
  },
};

export const getEPCCartIcon = ({ handleOpenEPCCartListDrawer }) => ({
  renderer: EPCCartIconRenderer,
  renderOptions: {
    orderType: ENTITY_REFERENCE_TYPES.SALES_ORDER,
    containerClassName: styles.epcCartIconContainer,
    handleOnClick: handleOpenEPCCartListDrawer,
    source: EPC_CART_SOURCE.LIST_VIEW,
  },
});

export const getCreateSalesOrderButton = ({ isBulkSOCreationEnabled = false }) => ({
  renderer: ButtonWithTooltip,
  action: CUSTOM_ACTIONS.CREATE_SALES_ORDER,
  renderOptions: {
    children: __('Create'),
    view: 'primary',
    className: isBulkSOCreationEnabled ? styles.createSOButton : 'margin-left-20',
    tooltipText: CREATE_SALES_ORDER_SHORTCUT.label,
  },
});

export const SALES_ORDER_HEADING_DROPDOWN = {
  renderer: SalesOrderHeadingDropdown,
  action: CUSTOM_ACTIONS.TRIGGER_HEADING_DROPDOWN_ACTION,
};

export const DEFAULT_SORTING = {
  modifiedTime: SORT.DESC,
};

export const PSO_TABLE_ID = 'PSOTable';

export const SALES_ORDER_STATIC_FILTER_GROUP_VARIANT_VS_FILTERS = {
  [SALES_ORDER_STATIC_FILTER_GROUP_VARIANT.SO_BY_SALE_STATUS_DRAFT]: [
    {
      type: SALE_STATUS_FILTER.id,
      field: SALE_STATUS_FILTER.id,
      operator: OPERATORS.IN,
      values: [DRAFT.id],
    },
  ],
  [SALES_ORDER_STATIC_FILTER_GROUP_VARIANT.SO_BY_SALE_STATUS_FILLED]: [
    {
      type: SALE_STATUS_FILTER.id,
      field: SALE_STATUS_FILTER.id,
      operator: OPERATORS.IN,
      values: [DELIVERED.id],
    },
  ],
  [SALES_ORDER_STATIC_FILTER_GROUP_VARIANT.SO_BY_SALE_STATUS_INVOICED]: [
    {
      type: SALE_STATUS_FILTER.id,
      field: SALE_STATUS_FILTER.id,
      operator: OPERATORS.IN,
      values: [INVOICED.id],
    },
  ],
  [SALES_ORDER_STATIC_FILTER_GROUP_VARIANT.SO_BY_SALE_STATUS_PARTIALLY_FILLED]: [
    {
      type: SALE_STATUS_FILTER.id,
      field: SALE_STATUS_FILTER.id,
      operator: OPERATORS.IN,
      values: [PARTIALLY_DELIVERED.id],
    },
  ],
  [SALES_ORDER_STATIC_FILTER_GROUP_VARIANT.SO_BY_SALE_STATUS_REOPENED]: [
    {
      type: SALE_STATUS_FILTER.id,
      field: SALE_STATUS_FILTER.id,
      operator: OPERATORS.IN,
      values: [REOPENED.id],
    },
  ],
  [SALES_ORDER_STATIC_FILTER_GROUP_VARIANT.SO_BY_SALE_STATUS_VOIDED]: [
    {
      type: SALE_STATUS_FILTER.id,
      field: SALE_STATUS_FILTER.id,
      operator: OPERATORS.IN,
      values: [VOIDED.id],
    },
  ],
};

export const SALES_ORDER_COPY_TYPES = [COPY_TYPE.CUSTOMER, COPY_TYPE.DEALER];

export const SALES_ORDER_LIST_UPDATE = 'SALES_ORDER_LIST';

export const SALES_ORDER_LIST_UPDATE_EVENT_TYPE = 'SALES_ORDER_UPDATED';

export const BULK_ACTION_CHANNEL_NAME = 'BULK_ACTION_UPDATES';

export const BULK_ACTION_EVENT = 'PARTS_BULK_ACTION_UPDATES';

export const INFO_ICON_CONTENT =
  'Generic search will not be applicable for the part name. Please search specifically for part description via the search dropdown';

export const POLL_DEBOUNCE_DELAY = 2000;
