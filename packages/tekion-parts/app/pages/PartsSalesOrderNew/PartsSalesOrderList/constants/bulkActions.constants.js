import COLORS from 'tstyles/exports.scss';

import { BANNER_TYPES } from '@tekion/tekion-components/src/molecules/banner';

import { CDN_STATIC_ASSET_PATH } from 'constants/cdn.constants';

import {
  BULK_ACTIONS_REQUEST_TYPES,
  BULK_ACTION_ASSETS,
  BULK_ACTIONS_STATUS,
} from 'organisms/SOBulkActions/constants/bulkActions.constants';

export const BULK_PRINT_ACTION_KEYS = {
  PRINT_INVOICE: 'PRINT_INVOICE',
  PRINT_QUOTE: 'PRINT_QUOTE',
  PRINT_PICKLIST: 'PRINT_PICKLIST',
  PRINT_CREDIT_MEMO: 'PRINT_CREDIT_MEMO',
  LABEL_PRINT_PICKLIST: 'LABEL_PRINT_PICKLIST',
};

export const BULK_EMAIL_ACTION_KEYS = {
  EMAIL_INVOICE: 'EMAIL_INVOICE',
  EMAIL_QUOTE: 'EMAIL_QUOTE',
  EMAIL_CREDIT_MEMO: 'EMAIL_CREDIT_MEMO',
};

export const DOWNLOAD_ACTION_KEYS = {
  DOWNLOAD_INVOICE: 'DOWNLOAD_INVOICE',
  DOWNLOAD_QUOTE: 'DOWNLOAD_QUOTE',
  DOWNLOAD_PICKLIST: 'DOWNLOAD_PICKLIST',
  DOWNLOAD_CREDIT_MEMO: 'DOWNLOAD_CREDIT_MEMO',
};

export const SUMMARY_MODAL_ACTION_KEYS = {
  PRINT_INVOICE_SUMMARY: 'PRINT_INVOICE_SUMMARY',
  PRINT_QUOTE_SUMMARY: 'PRINT_QUOTE_SUMMARY',
  PRINT_PICKLIST_SUMMARY: 'PRINT_PICKLIST_SUMMARY',
  PRINT_CREDIT_MEMO_SUMMARY: 'PRINT_CREDIT_MEMO_SUMMARY',
  LABEL_PRINT_PICKLIST_SUMMARY: 'LABEL_PRINT_PICKLIST_SUMMARY',
};

export const DOWNLOAD_SUCCESS_ACTION_KEYS = {
  DOWNLOAD_INVOICE_SUCCESS: 'DOWNLOAD_INVOICE_SUCCESS',
  DOWNLOAD_CREDIT_MEMO_SUCCESS: 'DOWNLOAD_CREDIT_MEMO_SUCCESS',
  DOWNLOAD_QUOTE_SUCCESS: 'DOWNLOAD_QUOTE_SUCCESS',
};

export const BULK_SALES_ORDER_CREATION_ACTION_KEYS = {
  UPLOAD_EXCEL_S3_MEDIA_ID: 'UPLOAD_EXCEL_S3_MEDIA_ID',
  DOWNLOAD_BULK_SO_CREATION_ASSET: 'DOWNLOAD_BULK_SO_CREATION_ASSET',
};

export const BULK_ACTION_KEYS = {
  ...BULK_PRINT_ACTION_KEYS,
  ...BULK_EMAIL_ACTION_KEYS,
  ...DOWNLOAD_ACTION_KEYS,
  ...SUMMARY_MODAL_ACTION_KEYS,
  ...DOWNLOAD_SUCCESS_ACTION_KEYS,
  ...BULK_SALES_ORDER_CREATION_ACTION_KEYS,
  GENERATE_INVOICE: 'GENERATE_INVOICE',
  GENERATE_INVOICE_SUMMARY: 'GENERATE_INVOICE_SUMMARY',
};

export const BULK_ACTION_BANNER_BUTTON = {
  CLOSE: 'CLOSE',
  VIEW_DETAILS: 'VIEW_DETAILS',
};

export const BULK_ACTION_BANNER_BUTTON_LABELS = {
  [BULK_ACTION_BANNER_BUTTON.CLOSE]: __('Close'),
  [BULK_ACTION_BANNER_BUTTON.VIEW_DETAILS]: __('View Details'),
};

export const FORMATTED_BULK_ACTION_REQUEST = {
  [BULK_ACTIONS_REQUEST_TYPES.PRINT]: 'print',
  [BULK_ACTIONS_REQUEST_TYPES.PICKLIST]: 'print',
  [BULK_ACTIONS_REQUEST_TYPES.EMAIL]: 'email',
  [BULK_ACTIONS_REQUEST_TYPES.DOWNLOAD]: 'download',
  [BULK_ACTIONS_REQUEST_TYPES.LABEL_PRINT]: 'print',
  [BULK_ACTIONS_REQUEST_TYPES.SALES_ORDER_CREATION]: 'creat',
};

export const BULK_ACTION_REQUEST_TYPE_VS_FORMATTED_DOCUMENT_NAME = {
  [BULK_ACTIONS_REQUEST_TYPES.PRINT]: {
    [BULK_ACTION_ASSETS.INVOICE]: __('invoices'),
    [BULK_ACTION_ASSETS.QUOTE]: __('quotes'),
    [BULK_ACTION_ASSETS.CREDIT_MEMO]: __('credit memos'),
  },
  [BULK_ACTIONS_REQUEST_TYPES.EMAIL]: {
    [BULK_ACTION_ASSETS.INVOICE]: __('invoices'),
    [BULK_ACTION_ASSETS.QUOTE]: __('quotes'),
    [BULK_ACTION_ASSETS.CREDIT_MEMO]: __('credit memos'),
  },
  [BULK_ACTIONS_REQUEST_TYPES.PICKLIST]: {
    [BULK_ACTION_ASSETS.PICKLIST]: __('picklists'),
  },
  [BULK_ACTIONS_REQUEST_TYPES.DOWNLOAD]: {
    [BULK_ACTION_ASSETS.INVOICE]: __('invoices'),
    [BULK_ACTION_ASSETS.CREDIT_MEMO]: __('credit memos'),
    [BULK_ACTION_ASSETS.QUOTE]: __('quotes'),
  },
  [BULK_ACTIONS_REQUEST_TYPES.LABEL_PRINT]: {
    [BULK_ACTION_ASSETS.PICKLIST]: __('part labels'),
  },
  [BULK_ACTIONS_REQUEST_TYPES.SALES_ORDER_CREATION]: {
    [BULK_ACTION_ASSETS.SALES_ORDER]: __('sales orders'),
  },
  [BULK_ACTIONS_REQUEST_TYPES.SALES_ORDER_AUTO_INVOICE]: {
    [BULK_ACTION_ASSETS.INVOICE]: __('invoices'),
  },
};

export const BULK_ACTION_REQUEST_TYPE_VS_NOTIFICATION_ACTION_KEY = {
  [BULK_ACTIONS_REQUEST_TYPES.PRINT]: {
    [BULK_ACTION_ASSETS.INVOICE]: BULK_ACTION_KEYS.PRINT_INVOICE_SUMMARY,
    [BULK_ACTION_ASSETS.QUOTE]: BULK_ACTION_KEYS.PRINT_QUOTE_SUMMARY,
    [BULK_ACTION_ASSETS.CREDIT_MEMO]: BULK_ACTION_KEYS.PRINT_CREDIT_MEMO_SUMMARY,
  },
  [BULK_ACTIONS_REQUEST_TYPES.PICKLIST]: {
    [BULK_ACTION_ASSETS.PICKLIST]: BULK_ACTION_KEYS.PRINT_PICKLIST_SUMMARY,
  },
  [BULK_ACTIONS_REQUEST_TYPES.DOWNLOAD]: {
    [BULK_ACTION_ASSETS.INVOICE]: BULK_ACTION_KEYS.DOWNLOAD_INVOICE_SUCCESS,
    [BULK_ACTION_ASSETS.CREDIT_MEMO]: BULK_ACTION_KEYS.DOWNLOAD_CREDIT_MEMO_SUCCESS,
    [BULK_ACTION_ASSETS.QUOTE]: BULK_ACTION_KEYS.DOWNLOAD_QUOTE_SUCCESS,
  },
  [BULK_ACTIONS_REQUEST_TYPES.LABEL_PRINT]: {
    [BULK_ACTION_ASSETS.PICKLIST]: BULK_ACTION_KEYS.LABEL_PRINT_PICKLIST_SUMMARY,
  },
  [BULK_ACTIONS_REQUEST_TYPES.SALES_ORDER_CREATION]: {
    [BULK_ACTION_ASSETS.SALES_ORDER]: BULK_ACTION_KEYS.DOWNLOAD_BULK_SO_CREATION_ASSET,
  },
  [BULK_ACTIONS_REQUEST_TYPES.SALES_ORDER_AUTO_INVOICE]: {
    [BULK_ACTION_ASSETS.INVOICE]: BULK_ACTION_KEYS.GENERATE_INVOICE_SUMMARY,
  },
};

export const BULK_ACTION_KEY_VS_DOWNLOAD_BULK_ACTION_DOCUMENT = {
  [BULK_ACTION_KEYS.DOWNLOAD_CREDIT_MEMO]: BULK_ACTION_ASSETS.CREDIT_MEMO,
  [BULK_ACTION_KEYS.DOWNLOAD_INVOICE]: BULK_ACTION_ASSETS.INVOICE,
  [BULK_ACTION_KEYS.DOWNLOAD_QUOTE]: BULK_ACTION_ASSETS.QUOTE,
};

export const BULK_ACTION_KEY_VS_DOWNLOAD_TOASTER_MESSAGE = {
  [BULK_ACTION_KEYS.DOWNLOAD_INVOICE]: __("Invoices PDFs are being generated. You'll be notified upon completion."),
  [BULK_ACTION_KEYS.DOWNLOAD_CREDIT_MEMO]: __(
    "Credit Memo PDFs are being generated. You'll be notified upon completion.",
  ),
  [BULK_ACTION_KEYS.DOWNLOAD_QUOTE]: __("Quote PDFs are being generated. You'll be notified upon completion."),
};

export const BULK_ACTION_NOTIFICATION_STATUS_VS_COLOR_CONFIG = {
  [BULK_ACTIONS_STATUS.FAILED]: {
    type: BANNER_TYPES.ERROR,
    color: COLORS.linen,
    barColor: COLORS.destructivetext,
  },
  [BULK_ACTIONS_STATUS.SUCCESS]: {
    type: BANNER_TYPES.SUCCESS,
    color: COLORS.whiteIce,
    barColor: COLORS.statusSuccess,
  },
  [BULK_ACTIONS_STATUS.PENDING]: {
    type: BANNER_TYPES.INFO,
    color: COLORS.aliceBlue,
    barColor: COLORS.dodgerBlue,
  },
  [BULK_ACTIONS_STATUS.IN_PROGRESS]: {
    type: BANNER_TYPES.INFO,
    color: COLORS.aliceBlue,
    barColor: COLORS.dodgerBlue,
  },
  [BULK_ACTIONS_STATUS.POST_PROCESSING]: {
    type: BANNER_TYPES.INFO,
    color: COLORS.aliceBlue,
    barColor: COLORS.dodgerBlue,
  },
};

export const BULK_SO_CREATION_TEMPLATE_LINK = `${CDN_STATIC_ASSET_PATH}/bulk_sales_order_creation_template.xlsx`;
