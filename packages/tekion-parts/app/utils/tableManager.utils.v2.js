import _forEach from 'lodash/forEach';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';
import _map from 'lodash/map';
import _omit from 'lodash/omit';
import _pick from 'lodash/pick';
import _reduce from 'lodash/reduce';
import _set from 'lodash/set';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import RequestBuilder from '@tekion/tekion-base/builders/request';
import { SORT } from '@tekion/tekion-base/builders/request/Request.constants';
import { FILE_EXTENSIONS } from '@tekion/tekion-base/constants/downloadFileType';
import OPERATORS from '@tekion/tekion-base/constants/filterOperators';
import { downloadData } from '@tekion/tekion-base/utils/downloadUtils';
import { getErrorMessage } from '@tekion/tekion-base/utils/errorUtils';
import moneyUtils from '@tekion/tekion-base/utils/money';

import { getCsvDataWithBomChar } from '@tekion/tekion-business/src/helpers/parts/general.helpers';

import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';

import {
  getAmountDueFilter,
  getAmountPaidFilter,
} from '@tekion/tekion-widgets/src/appServices/parts/salesOrder/salesOrder.filters';

import { ALLOWED_PAYLOAD_KEYS_FOR_EXPORT } from 'constants/general.constants';

const addSortDetailsToPayload = (sortDetails, defaultSortKey, payload) => {
  if (_isEmpty(sortDetails)) {
    return payload.addSort(defaultSortKey, SORT.DESC);
  }

  return _reduce(
    sortDetails,
    (acc, sortOrder, sortKey) => {
      acc.addSort(sortKey, sortOrder);
      return acc;
    },
    payload,
  );
};

const getAddFilterArguments = (item, includeTwoDecimalPlacesInAmountFields) => {
  const operator = _get(item, 'operator');
  if (operator === OPERATORS.BOOL) {
    return [
      undefined,
      operator,
      undefined,
      undefined,
      undefined,
      {
        ..._omit(item, 'operator'),
      },
      { skipValueCheck: true, isBooleanOperator: true },
    ];
  }

  if (
    item.type === getAmountDueFilter(includeTwoDecimalPlacesInAmountFields).id ||
    item.type === getAmountPaidFilter(includeTwoDecimalPlacesInAmountFields).id
  ) {
    return [item.type, item.operator, _map(item.values, moneyUtils.toInt), false, item.type, EMPTY_OBJECT];
  }

  if (operator === OPERATORS.EXISTS || operator === OPERATORS.NOT_EXISTS) {
    return [
      item.type,
      item.operator,
      item.values,
      false,
      item.type,
      EMPTY_OBJECT,
      {
        skipValueCheck: true,
      },
    ];
  }

  return [item.type, item.operator, item.values, false];
};

const getDownloadRequestPayload = ({
  searchQuery,
  selectedFilters,
  sortDetails,
  defaultSortKey,
  searchField,
  searchTextFields,
}) => {
  const payload = new RequestBuilder().setSearchString(searchQuery);
  _forEach(selectedFilters, item => {
    if (!_isEmpty(item)) {
      payload.addFilter(...getAddFilterArguments(item));
    }
  });

  addSortDetailsToPayload(sortDetails, defaultSortKey, payload);
  if (!_isNil(searchField)) {
    _set(payload, 'searchFields', searchField);
  }
  if (!_isNil(searchTextFields)) {
    _set(payload, 'searchTextFields', searchTextFields);
  }
  return payload;
};

export const handleExcelDownload = params => {
  const {
    searchQuery,
    selectedFilters,
    sortDetails,
    downloadApi,
    defaultSortKey,
    fileName,
    fileExtension,
    searchField,
    searchTextFields,
  } = params;
  const payload = getDownloadRequestPayload({
    searchQuery,
    selectedFilters,
    sortDetails,
    defaultSortKey,
    searchField,
    searchTextFields,
  });
  toaster(TOASTER_TYPE.INFO, __('Generating Excel export'));
  downloadApi(_pick(payload, ALLOWED_PAYLOAD_KEYS_FOR_EXPORT))
    .then(response => {
      const data = fileExtension === FILE_EXTENSIONS.CSV ? getCsvDataWithBomChar(response?.data) : response?.data;
      downloadData(data, __('{{fileName}}', { fileName }), fileExtension);
    })
    .catch(err => {
      toaster(TOASTER_TYPE.ERROR, getErrorMessage(err, __('Error while generating the Excel export')));
    });
};
