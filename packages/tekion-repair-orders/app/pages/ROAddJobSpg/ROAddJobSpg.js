import React from 'react';
import PropTypes from 'prop-types';

import { connect } from 'react-redux';
import { compose } from 'recompose';

import _noop from 'lodash/noop';

import withRouter from '@tekion/tekion-components/src/hoc/withRouter';
import WithPageLevelDataFetcher from 'twidgets/appServices/service/pages/WithPageLevelDataFetcher';

import { getServiceMenuSettings } from 'utils/selectors';

import { fetchServiceMenuBootstrapData } from 'actions/serviceMenu.actions';
import { createJob, createServiceMenu, importJobsFromQuote } from 'actions/jobDetails.action';
import { updateTechnicians } from 'actions/roDetails.actions';
import { resolveTechIds } from 'actions/roDispatch.actions';
import {
  createRecommendation,
  createServiceMenuRecommendation,
  addIntermediateRecommendation,
  updateIntermediateRecommendation,
} from 'actions/recommendation.action';
import ROConstraints from 'helpers/constraints';
import { getCostCenters, getBootstrapData } from 'twidgets/appServices/service/helpers/ROContainer.selectors';
import { EMPTY_OBJECT, EMPTY_ARRAY } from 'tbase/app.constants';
import { URL_TYPES_VS_MODULE_TYPE } from 'organisms/ROForm/ROForm.constants';

import { RO_DETAILS_ENTITY_TYPE } from 'utils/constants';

import ROAddJob from '../ROAddJob/ROAddJob';
import { getRoDetails, getJobsDetails, getLaborRatesFromRepairOrder } from '../RODetailsPage/RODetailsPage.selectors';

const ROAddJobContainer = ({ type, ...props }) => (
  <ROAddJob
    {...props}
    isRONewAddJobViewEnabled={ROConstraints.isNewAddJobViewEnabled()}
    type={URL_TYPES_VS_MODULE_TYPE[type]}
  />
);

ROAddJobContainer.propTypes = {
  jobDetails: PropTypes.object,
  type: PropTypes.string,
  deferredRecommendations: PropTypes.array,
  onCancel: PropTypes.func,
};

ROAddJobContainer.defaultProps = {
  jobDetails: EMPTY_OBJECT,
  type: RO_DETAILS_ENTITY_TYPE.JOB,
  deferredRecommendations: EMPTY_ARRAY,
  onCancel: _noop,
};

const mapStateToProps = state => ({
  roDetails: getRoDetails(state),
  laborRates: getLaborRatesFromRepairOrder(state),
  jobDetails: getJobsDetails(state),
  costCentersByPayType: getCostCenters(state),
  serviceMenuSettings: getServiceMenuSettings(state),
});

export { ROAddJobContainer as BaseROAddJobContainer };

export default compose(
  connect(mapStateToProps, {
    createJob,
    createRecommendation,
    importJobsFromQuote,
    createServiceMenu,
    updateTechnicians,
    createServiceMenuRecommendation,
    resolveTechIds,
    addIntermediateRecommendation,
    updateIntermediateRecommendation,
  }),
  WithPageLevelDataFetcher(fetchServiceMenuBootstrapData, getBootstrapData),
  withRouter
)(ROAddJobContainer);
