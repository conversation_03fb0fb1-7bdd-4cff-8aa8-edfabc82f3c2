import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';

import _noop from 'lodash/noop';

import SaveComponent from 'tcomponents/molecules/SaveComponent';
import Spinner from 'tcomponents/molecules/loader';
import CreateJob from 'pages/ROAddJob/Components/CreateJob';
import CreateRecommendation from 'pages/ROAddJob/Components/CreateRecommendation';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import { getErrorMessage } from 'tbase/utils/errorUtils';
import { EMPTY_OBJECT, EMPTY_ARRAY, EMPTY_STRING } from 'tbase/app.constants';
import {
  ADD_SERVICE_TYPE_VS_ADAPTED_JOB_FUNC,
  ADD_SERVICE_TYPE_VS_ADAPTED_JOB_FUNC_COMPUTE_JOB,
} from 'pages/Quotes/QuoteDetails/Components/DetailedQuoteView/QuoteServiceDetails/Components/AddService/AddService.helper';
import { MODULE_TYPE_VS_PRIMARY_BUTTON_LABEL } from 'organisms/ROForm/ROForm.moduleType.factory';
import { MODULE_TYPES } from 'organisms/ROForm/ROForm.constants';

import ForcedPartsDisclaimer from 'twidgets/appServices/service/molecules/ForcedPartsDisclaimer';
import { PropertyControlledComponent } from '@tekion/tekion-components/src/molecules';
import ROConstraints from 'helpers/constraints';
import { DEFAULT_PRIMARY_BUTTON_LABEL } from '../../ROAddJob.constants';

const TYPE_VS_CREATE_COMPONENT = {
  [MODULE_TYPES.RO_JOB]: CreateJob,
  [MODULE_TYPES.RO_TECH_RECOMMENDATIONS]: CreateRecommendation,
  [MODULE_TYPES.PRE_RO_JOB]: CreateJob,
};

class CreateJobSpg extends PureComponent {
  state = { isLoading: false, data: EMPTY_OBJECT, areForcePartsSelected: false };

  componentDidMount() {
    this.setInitialFormValues();
  }

  componentDidUpdate(prevProps) {
    const { forcedPartSelectionEnabled } = this.props;
    const { forcedPartSelectionEnabled: prevForcedPartSelectionEnabled } = prevProps;
    if (forcedPartSelectionEnabled !== prevForcedPartSelectionEnabled) {
      this.setInitialFormValues();
    }
  }

  setInitialFormValues = () => {
    const {
      serviceData,
      roDetails,
      laborRates,
      type,
      togglePrimaryButton,
      forcedPartSelectionEnabled,
      jobDetails,
      resolveTechIds,
      payTypeConfigurations,
      payerPayTypeConfiguration,
      defaultSubPayType,
    } = this.props;
    const { addServiceType, ...restServiceData } = serviceData;
    this.setState({ isLoading: true });
    const adapterFunc = ROConstraints.isServiceV3Enabled()
      ? ADD_SERVICE_TYPE_VS_ADAPTED_JOB_FUNC_COMPUTE_JOB
      : ADD_SERVICE_TYPE_VS_ADAPTED_JOB_FUNC;
    adapterFunc[addServiceType]({
      serviceData: restServiceData,
      moduleType: type,
      roDetails,
      laborRates,
      forcedPartSelectionEnabled,
      jobDetails,
      resolveTechIds,
      payTypeConfigurations,
      isJobComputeFlow: ROConstraints.isServiceV3Enabled(),
      payerPayTypeConfiguration,
      defaultSubPayType,
      addServiceType,
      shouldSaveJobs: false,
    })
      .then(updatedJob => {
        togglePrimaryButton();
        this.setState({ data: updatedJob, isLoading: false });
      })
      .catch(error => {
        toaster(TOASTER_TYPE.ERROR, getErrorMessage(error, __('Unable to add Service, Please try after sometime.')));
      });
  };

  renderLoader = () => <Spinner id="roAddServicePage" />;

  handleForcePartsSelected = value => {
    this.setState({ areForcePartsSelected: value });
  };

  renderAdditionalFooterDetail = () => {
    const { forcedPartSelectionEnabled, isPrimaryButtonDisabled } = this.props;
    const { areForcePartsSelected } = this.state;
    return (
      <PropertyControlledComponent
        controllerProperty={
          forcedPartSelectionEnabled &&
          ROConstraints.isForcedPartFeatureEnabled() &&
          isPrimaryButtonDisabled &&
          !areForcePartsSelected
        }
      >
        <ForcedPartsDisclaimer headerWrapperClassName="margin-right-20" />
      </PropertyControlledComponent>
    );
  };

  renderFooterComponent = () => {
    const { type, isPrimaryButtonDisabled, isPrimaryButtonLoading, handleSubmit, redirectToHome } = this.props;
    return (
      <SaveComponent
        isPrimaryDisabled={isPrimaryButtonDisabled}
        primaryButtonLabel={MODULE_TYPE_VS_PRIMARY_BUTTON_LABEL[type] || DEFAULT_PRIMARY_BUTTON_LABEL}
        primaryClassName="automation_selector_save_new_job_button"
        secondaryClassName="automation_selector_cancel_new_job_button"
        onPrimaryAction={handleSubmit}
        onSecondaryAction={redirectToHome}
        primaryActionLoading={isPrimaryButtonLoading}
        renderAdditionalFooterDetail={this.renderAdditionalFooterDetail}
      />
    );
  };

  renderCreateForm = () => {
    const {
      type,
      laborRates,
      roDetails,
      costCentersByPayType,
      togglePrimaryButton,
      onFormSubmit,
      jobDetails,
      redirectToHome,
      serviceTypes,
      forcedPartSelectionEnabled,
      setForcedPartsEnabled,
      setUnselectedParts,
      unselectedParts,
      isForcedPartsFeatureEnabled,
      isPrimaryButtonDisabled,
      skills,
      validatePreRecommendationAdditionRules,
      payTypeConfigurations,
      payerPayTypeConfiguration,
      defaultSubPayType,
      addIntermediateRecommendation,
      updateIntermediateRecommendation,
    } = this.props;
    const { data } = this.state;
    const CreateComponent = TYPE_VS_CREATE_COMPONENT[type];
    return (
      <CreateComponent
        data={data}
        togglePrimaryButton={togglePrimaryButton}
        laborRates={laborRates}
        onFormSubmit={onFormSubmit}
        roDetails={roDetails}
        costCentersByPayType={costCentersByPayType}
        moduleType={type}
        containerClassName="right-panel-form-container full-height"
        formPageBodyProps={{ className: 'right-panel-form-body' }}
        renderFooterComponent={this.renderFooterComponent}
        jobDetails={jobDetails}
        redirectToHome={redirectToHome}
        serviceTypes={serviceTypes}
        forcedPartSelectionEnabled={forcedPartSelectionEnabled}
        setForcedPartsEnabled={setForcedPartsEnabled}
        setUnselectedParts={setUnselectedParts}
        unselectedParts={unselectedParts}
        isForcedPartsFeatureEnabled={isForcedPartsFeatureEnabled}
        isPrimaryButtonDisabled={isPrimaryButtonDisabled}
        skills={skills}
        validatePreRecommendationAdditionRules={validatePreRecommendationAdditionRules}
        handleForcePartsSelected={this.handleForcePartsSelected}
        payTypeConfigurations={payTypeConfigurations}
        payerPayTypeConfiguration={payerPayTypeConfiguration}
        defaultSubPayType={defaultSubPayType}
        addIntermediateRecommendation={addIntermediateRecommendation}
        updateIntermediateRecommendation={updateIntermediateRecommendation}
      />
    );
  };

  render() {
    const { isLoading } = this.state;
    if (isLoading) return this.renderLoader();
    return this.renderCreateForm();
  }
}

CreateJobSpg.propTypes = {
  serviceData: PropTypes.object,
  roDetails: PropTypes.object,
  laborRates: PropTypes.array,
  type: PropTypes.string,
  togglePrimaryButton: PropTypes.func,
  isPrimaryButtonDisabled: PropTypes.bool,
  isPrimaryButtonLoading: PropTypes.bool,
  handleSubmit: PropTypes.func,
  redirectToHome: PropTypes.func,
  costCentersByPayType: PropTypes.object,
  onFormSubmit: PropTypes.func,
  jobDetails: PropTypes.object,
  setForcedPartsEnabled: PropTypes.func,
  setUnselectedParts: PropTypes.func,
  forcedPartSelectionEnabled: PropTypes.bool,
  unselectedParts: PropTypes.array,
  isForcedPartsFeatureEnabled: PropTypes.bool,
  serviceTypes: PropTypes.array,
  skills: PropTypes.object,
  resolveTechIds: PropTypes.func,
  validatePreRecommendationAdditionRules: PropTypes.func,
  payTypeConfigurations: PropTypes.array,
  payerPayTypeConfiguration: PropTypes.object,
  defaultSubPayType: PropTypes.string,
  addIntermediateRecommendation: PropTypes.func,
  updateIntermediateRecommendation: PropTypes.func,
};

CreateJobSpg.defaultProps = {
  serviceData: EMPTY_OBJECT,
  roDetails: EMPTY_OBJECT,
  laborRates: EMPTY_ARRAY,
  type: '',
  togglePrimaryButton: _noop,
  isPrimaryButtonDisabled: false,
  isPrimaryButtonLoading: false,
  handleSubmit: _noop,
  redirectToHome: _noop,
  costCentersByPayType: EMPTY_OBJECT,
  onFormSubmit: _noop,
  jobDetails: EMPTY_OBJECT,
  setUnselectedParts: _noop,
  setForcedPartsEnabled: _noop,
  forcedPartSelectionEnabled: false,
  unselectedParts: EMPTY_ARRAY,
  isForcedPartsFeatureEnabled: false,
  serviceTypes: EMPTY_ARRAY,
  skills: EMPTY_OBJECT,
  resolveTechIds: _noop,
  validatePreRecommendationAdditionRules: _noop,
  payTypeConfigurations: EMPTY_ARRAY,
  payerPayTypeConfiguration: EMPTY_OBJECT,
  defaultSubPayType: EMPTY_STRING,
  addIntermediateRecommendation: _noop,
  updateIntermediateRecommendation: _noop,
};

export default CreateJobSpg;
