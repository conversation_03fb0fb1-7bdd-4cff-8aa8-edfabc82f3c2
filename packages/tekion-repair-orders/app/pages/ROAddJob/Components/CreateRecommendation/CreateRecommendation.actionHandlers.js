import _size from 'lodash/size';
import _isFunction from 'lodash/isFunction';
import _noop from 'lodash/noop';

// import { toaster } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import { triggerSubmit } from 'tcomponents/pages/formPage/utils/formAction';
// import { validateRecommendationForm } from 'organisms/TechRecommendForm/TechRecommendForm.helper';
// import { getRecommendationValidationFieldErrorWarning } from 'helpers/recommendation.helper';
import ACTION_HANDLERS from 'organisms/ROForm/ROForm.actionHandlers';

import { EMPTY_OBJECT } from 'tbase/app.constants';
import formActionTypes from 'tcomponents/pages/formPage/constants/actionTypes';
// import { NOTIFICATION_TYPE } from 'utils/constants';

import { getSaveParams } from './CreateRecommendation.helper';

export default {
  ...ACTION_HANDLERS,
  [formActionTypes.ON_FORM_SUBMIT]: async (payload, { getState, setState }) => {
    const {
      onFormSubmit,
      contextIds,
      receivedOnSubmit,
      redirectToHome,
      togglePrimaryButton,
      updatedOperationsInfo,
      handleForcePartsSelected,
      addIntermediateRecommendation,
      updateIntermediateRecommendation,
      ...restProps
    } = getState();
    if (_isFunction(handleForcePartsSelected)) {
      handleForcePartsSelected(true);
    }
    if (_size(contextIds) && receivedOnSubmit !== _size(contextIds) - 1) {
      setState({ errors: { ...getState().errors, [payload.id]: undefined }, receivedOnSubmit: receivedOnSubmit + 1 });
      triggerSubmit(contextIds[receivedOnSubmit + 1]);
      return null;
    }
    setState({ errors: EMPTY_OBJECT });
    // set intermediate recommendation addition
    // togglePrimaryButton(true);
    const intermediateRecommendationIds = await addIntermediateRecommendation({
      recommendations: [restProps.formValues],
    });
    redirectToHome();
    return onFormSubmit(
      { ...getSaveParams({ ...restProps }), updatedOperationsInfo, intermediateRecommendationIds },
      _noop,
      restProps.formValues
    );
    // return validateRecommendationForm(restProps)
    //   .then(({ isValid, message, preRecommendationAdditionIssues, showWarningAndContinue }) => {
    //     if (!isValid) {
    //       setState(getRecommendationValidationFieldErrorWarning({ preRecommendationAdditionIssues }));
    //       if (!showWarningAndContinue) {
    //         return toaster(NOTIFICATION_TYPE.ERROR, message);
    //       }
    //     }
    //     return onFormSubmit(
    //       { ...getSaveParams({ ...restProps }), updatedOperationsInfo },
    //       _noop,
    //       restProps.formValues
    //     ).then(() => {
    //       updateIntermediateRecommendation(intermediateRecommendationIds);
    //     });
    //   })
    //   .catch(err => {
    //     toaster(NOTIFICATION_TYPE.ERROR, err.message);
    //   })
    //   .finally(() => {
    //     // togglePrimaryButton(false);
    //   });
  },
};
