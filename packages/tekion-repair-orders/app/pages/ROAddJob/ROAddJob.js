import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';

import _noop from 'lodash/noop';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';

import { triggerSubmit } from 'tcomponents/pages/formPage/utils/formAction';
import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from 'tbase/app.constants';
import ROConstraints from 'helpers/constraints';
import ROReader from 'readers/RepairOrder.reader';
import { ROUTE_CONFIG } from 'tbusiness/appServices/service/helpers/route';
import { RECOMMENDATION_TYPES } from 'tbase/constants/repairOrder/recommendation';
import { JOB_TYPES } from 'tbase/constants/repairOrder/job';
import {
  MODULE_TYPE_VS_PRIMARY_BUTTON_LABEL,
  MODULE_TYPE_VS_JOB_TYPE_ACCESSOR,
} from 'organisms/ROForm/ROForm.moduleType.factory';
import {
  MODULE_TYPES,
  MODULE_TYPES_VS_URL_TYPE_FOR_REDIRECTION,
  RECOMMENDATION_MODULE_TYPES,
} from 'organisms/ROForm/ROForm.constants';
import { FORM_PAGE_CONTEXT_ID } from 'pages/ROAddJob/ROAddJob.constants';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import { getErrorMessage } from 'utils';
import { getPrimaryAndBillingCustomerId } from 'helpers/recommendation.helper';

import AddJobSpg from './Components/AddJobSpg';
import CreateJobSpg from './Components/AddJobSpg/CreateJobSpg';
import { JOB_TYPE_VS_RENDERER } from './ROAddJob.jobType.factory';

import styles from './roAddJob.module.scss';

const DummyComponent = () => null;

const JOB_TYPE_VS_CREATE_OPTION = {
  [JOB_TYPES.SERVICE_MENU]: ({ createServiceMenu, roId, createJob, roDetails }, values) => {
    const { fromDeferredRecommendation, isReturnJob } = values;
    if (fromDeferredRecommendation || isReturnJob) {
      return createJob(ROReader.getId(roDetails).roId, {
        ...values,
        serviceId: ROReader.getServiceId(roDetails),
      });
    }
    return createServiceMenu(roId, values);
  },
  [JOB_TYPES.QUOTES]: ({ importJobsFromQuote, roDetails, jobDetails }, values) =>
    importJobsFromQuote(ROReader.getId(roDetails).roId, roDetails, jobDetails, values),
  DEFAULT: ({ createJob, roDetails }, values) =>
    createJob(ROReader.getId(roDetails).roId, {
      ...values,
      serviceId: ROReader.getServiceId(roDetails),
    }),
};

const RECOMMENDATION_TYPE_VS_CREATE_OPTION = {
  [RECOMMENDATION_TYPES.SERVICE_MENU]: (
    { createServiceMenuRecommendation, roDetails, createRecommendation },
    values
  ) => {
    const { fromDeferredRecommendation, isReturnJob } = values;
    if (fromDeferredRecommendation || isReturnJob) {
      return createRecommendation(ROReader.getId(roDetails).roId, {
        ...values,
        ...getPrimaryAndBillingCustomerId(roDetails),
        serviceId: ROReader.getServiceId(roDetails),
      });
    }
    return createServiceMenuRecommendation(ROReader.getId(roDetails).roId, {
      ...values,
      ...getPrimaryAndBillingCustomerId(roDetails),
    });
  },
  DEFAULT: ({ createRecommendation, roDetails }, values) =>
    createRecommendation(
      ROReader.getId(roDetails).roId,
      {
        ...values,
        ...getPrimaryAndBillingCustomerId(roDetails),
        serviceId: ROReader.getServiceId(roDetails),
      },
      { useQueue: true }
    ),
};

const WEB_CHECK_IN_TYPE_VS_CREATE_OPTION = {
  [JOB_TYPES.QUOTES]: ({ onImportJob }, values) => onImportJob(values),
  DEFAULT: ({ onSave }, values) => onSave(values),
};

const TYPE_VS_CREATE_JOB_ACTION = {
  [MODULE_TYPES.RO_JOB]: JOB_TYPE_VS_CREATE_OPTION,
  [MODULE_TYPES.RO_TECH_RECOMMENDATIONS]: RECOMMENDATION_TYPE_VS_CREATE_OPTION,
  [MODULE_TYPES.ESTIMATE_JOB]: JOB_TYPE_VS_CREATE_OPTION,
  [MODULE_TYPES.ESTIMATE_TECH_RECOMMENDATIONS]: RECOMMENDATION_TYPE_VS_CREATE_OPTION,
  [MODULE_TYPES.PRE_RO_JOB]: WEB_CHECK_IN_TYPE_VS_CREATE_OPTION,
};

const WEB_CHECK_IN_TYPE_VS_DECLINE_OPTION = {
  [JOB_TYPES.SERVICE_MENU]: ({ onDecline }, values) => onDecline(values),
  DEFAULT: ({ onCancel }) => onCancel(),
};

const TYPE_VS_DECLINE_JOB_ACTION = {
  [MODULE_TYPES.PRE_RO_JOB]: WEB_CHECK_IN_TYPE_VS_DECLINE_OPTION,
};

class ROAddJob extends PureComponent {
  state = {
    isPrimaryButtonDisabled: true,
    isPrimaryButtonLoading: false,
    isSecondaryButtonLoading: false,
    forcedPartSelectionEnabled: false,
    unselectedParts: null,
  };

  onFormSubmit = (params, redirectToHome) => {
    const { type, isRONewAddJobViewEnabled } = this.props;
    const entityTypeReader = MODULE_TYPE_VS_JOB_TYPE_ACCESSOR[type] || MODULE_TYPE_VS_JOB_TYPE_ACCESSOR.DEFAULT;
    const entityType = entityTypeReader(params);
    this.setState({ isPrimaryButtonLoading: true });
    const promiseToEval = TYPE_VS_CREATE_JOB_ACTION[type][entityType]
      ? TYPE_VS_CREATE_JOB_ACTION[type][entityType](this.props, params)
      : TYPE_VS_CREATE_JOB_ACTION[type].DEFAULT(this.props, params);
    return promiseToEval
      .then(entity => {
        const entityId = entity?.id;
        this.setState({ isPrimaryButtonLoading: false });
        const moduleTypeSetToRedirectHome = new Set([MODULE_TYPES.PRE_RO_JOB, MODULE_TYPES.RO_TECH_RECOMMENDATIONS]);
        if (moduleTypeSetToRedirectHome.has(type) && isRONewAddJobViewEnabled) {
          return redirectToHome();
        }
        return this.handleOnCancelAfterSave(entityId);
      })
      .catch(() => {
        this.setState({ isPrimaryButtonLoading: false });
      });
  };

  handleOnCancelAfterSave = (newEntityId = this.state.newEntityId) => {
    const { navigate, roId, estimateId, type, onCancel, getRedirectEntityUrl } = this.props;
    if (!newEntityId) return onCancel();
    navigate(
      getRedirectEntityUrl({
        roId,
        estimateId,
        type: MODULE_TYPES_VS_URL_TYPE_FOR_REDIRECTION[type],
        typeId: newEntityId,
      })
    );
    return onCancel();
  };

  handleDecline = async params => {
    const { onDecline, onCancel } = this.props;
    this.setState({ isSecondaryButtonLoading: true });
    const declineResult = await onDecline(params).catch(error => {
      toaster(TOASTER_TYPE.ERROR, getErrorMessage(error, __('Failed to decline job.')));
    });
    this.setState({ isSecondaryButtonLoading: false });
    return onCancel().then((onCancelResult = EMPTY_OBJECT) => ({ ...onCancelResult, declineResult }));
  };

  handleCancel = params => {
    const { type, onCancel, jobType } = this.props;
    if (type !== MODULE_TYPES.PRE_RO_JOB || _isEmpty(params)) {
      return onCancel();
    }
    const jobTypeForPromise = jobType || params?.type;
    const promiseToEval =
      TYPE_VS_DECLINE_JOB_ACTION[MODULE_TYPES.PRE_RO_JOB][jobTypeForPromise] ||
      TYPE_VS_DECLINE_JOB_ACTION[MODULE_TYPES.PRE_RO_JOB].DEFAULT;
    return promiseToEval({ ...this.props, onDecline: this.handleDecline }, params);
  };

  handleSubmit = () => {
    triggerSubmit(FORM_PAGE_CONTEXT_ID);
  };

  togglePrimaryButton = disabled => {
    this.setState(({ isPrimaryButtonDisabled }) => ({
      isPrimaryButtonDisabled: !_isNil(disabled) ? disabled : !isPrimaryButtonDisabled,
    }));
  };

  setForcedPartsEnabled = value => {
    this.setState({
      forcedPartSelectionEnabled: !!value,
    });
  };

  setUnselectedParts = parts => {
    this.setState({
      unselectedParts: parts,
    });
  };

  renderJobTypeComponent = () => {
    const { jobType, type, roId, ...restProps } = this.props;
    const Component = JOB_TYPE_VS_RENDERER[jobType].component || DummyComponent;
    const containerClassName = JOB_TYPE_VS_RENDERER[jobType].className || styles.container;
    const { forcedPartSelectionEnabled, unselectedParts } = this.state;
    return (
      <div className={containerClassName} id="addJobContainer">
        <Component
          togglePrimaryButton={this.togglePrimaryButton}
          onFormSubmit={this.onFormSubmit}
          handleSubmit={this.handleSubmit}
          type={type}
          primaryBtnLabel={MODULE_TYPE_VS_PRIMARY_BUTTON_LABEL[type] || __('Add To $$(RO)')}
          {...restProps}
          {...this.state}
          onCancel={this.handleCancel}
          forcedPartSelectionEnabled={forcedPartSelectionEnabled}
          setForcedPartsEnabled={this.setForcedPartsEnabled}
          setUnselectedParts={this.setUnselectedParts}
          unselectedParts={unselectedParts}
          isForcedPartsFeatureEnabled={ROConstraints.isForcedPartFeatureEnabled()}
          isServiceV3Enabled={ROConstraints.isServiceV3Enabled()}
        />
      </div>
    );
  };

  renderCreateJobComponent = createJobProps => {
    const { type, ...restProps } = this.props;
    const { forcedPartSelectionEnabled, unselectedParts } = this.state;
    return (
      <CreateJobSpg
        togglePrimaryButton={this.togglePrimaryButton}
        onFormSubmit={this.onFormSubmit}
        handleSubmit={this.handleSubmit}
        type={type}
        {...restProps}
        {...createJobProps}
        {...this.state}
        onCancel={this.handleCancel}
        unselectedParts={unselectedParts}
        forcedPartSelectionEnabled={forcedPartSelectionEnabled}
        setForcedPartsEnabled={this.setForcedPartsEnabled}
        setUnselectedParts={this.setUnselectedParts}
        isForcedPartsFeatureEnabled={ROConstraints.isForcedPartFeatureEnabled()}
      />
    );
  };

  renderAddJobPage = () => {
    const {
      roDetails,
      jobDetails,
      laborRates,
      type,
      recommendations,
      serviceMenuSettings,
      handleDealerTireClick,
      offering,
      skills,
      serviceTypes,
      resolveTechIds,
      setIsServiceMenuPresented,
      shouldCheckForMenuPresentation,
      additional,
      onAddServiceListUpdate,
      getMandatoryParams,
      onDecline,
      cutomerSorProps,
      payTypeConfigurations,
      payerPayTypeConfiguration,
      defaultSubPayType,
    } = this.props;
    const { forcedPartSelectionEnabled, unselectedParts } = this.state;
    const jobs = RECOMMENDATION_MODULE_TYPES.has(type) ? recommendations : jobDetails;
    return (
      <AddJobSpg
        roDetails={roDetails}
        jobDetails={jobs}
        laborRates={laborRates}
        type={type}
        CreateJobComponent={this.renderCreateJobComponent}
        addToROAction={this.onFormSubmit}
        serviceMenuSettings={serviceMenuSettings}
        onCancel={this.handleCancel}
        handleDealerTireClick={handleDealerTireClick}
        offering={offering}
        forcedPartSelectionEnabled={forcedPartSelectionEnabled}
        setForcedPartsEnabled={this.setForcedPartsEnabled}
        setUnselectedParts={this.setUnselectedParts}
        unselectedParts={unselectedParts}
        isForcedPartsFeatureEnabled={ROConstraints.isForcedPartFeatureEnabled()}
        skills={skills}
        serviceTypes={serviceTypes}
        resolveTechIds={resolveTechIds}
        setIsServiceMenuPresented={setIsServiceMenuPresented}
        shouldCheckForMenuPresentation={shouldCheckForMenuPresentation}
        additional={additional}
        onAddServiceListUpdate={onAddServiceListUpdate}
        onServiceDecline={onDecline}
        getMandatoryParams={getMandatoryParams}
        cutomerSorProps={cutomerSorProps}
        payTypeConfigurations={payTypeConfigurations}
        payerPayTypeConfiguration={payerPayTypeConfiguration}
        defaultSubPayType={defaultSubPayType}
      />
    );
  };

  render() {
    const { isRONewAddJobViewEnabled } = this.props;
    const renderAddJobComponent = isRONewAddJobViewEnabled ? this.renderAddJobPage : this.renderJobTypeComponent;
    return renderAddJobComponent();
  }
}

ROAddJob.propTypes = {
  jobType: PropTypes.string,
  createJob: PropTypes.func,
  createRecommendation: PropTypes.func,
  roDetails: PropTypes.object,
  showDispatch: PropTypes.bool,
  type: PropTypes.string,
  roId: PropTypes.string,
  estimateId: PropTypes.string,
  updateTechnicians: PropTypes.func,
  onCancel: PropTypes.func,
  getRedirectEntityUrl: PropTypes.func,
  isRONewAddJobViewEnabled: PropTypes.bool,
  jobDetails: PropTypes.object,
  laborRates: PropTypes.array,
  recommendations: PropTypes.array,
  onDecline: PropTypes.func,
  serviceMenuSettings: PropTypes.object,
  handleDealerTireClick: PropTypes.func,
  offering: PropTypes.string,
  skills: PropTypes.object,
  serviceTypes: PropTypes.array,
  resolveTechIds: PropTypes.func,
  setIsServiceMenuPresented: PropTypes.func,
  shouldCheckForMenuPresentation: PropTypes.bool,
  additional: PropTypes.object,
  onAddServiceListUpdate: PropTypes.func,
  getMandatoryParams: PropTypes.func,
  cutomerSorProps: PropTypes.object,
  payTypeConfigurations: PropTypes.array,
  payerPayTypeConfiguration: PropTypes.object,
  defaultSubPayType: PropTypes.string,
  navigate: PropTypes.func.isRequired,
  params: PropTypes.object.isRequired,
};

ROAddJob.defaultProps = {
  jobType: '',
  createJob: _noop,
  showDispatch: false,
  type: '',
  roId: '',
  estimateId: '',
  roDetails: EMPTY_OBJECT,
  updateTechnicians: _noop,
  onCancel: _noop,
  createRecommendation: _noop,
  getRedirectEntityUrl: ROUTE_CONFIG.REPAIR_ORDER_ENTITY_DETAILS.getUrl,
  isRONewAddJobViewEnabled: false,
  jobDetails: EMPTY_OBJECT,
  laborRates: EMPTY_ARRAY,
  recommendations: EMPTY_ARRAY,
  onDecline: _noop,
  serviceMenuSettings: EMPTY_OBJECT,
  handleDealerTireClick: _noop,
  offering: EMPTY_STRING,
  skills: EMPTY_OBJECT,
  serviceTypes: EMPTY_ARRAY,
  resolveTechIds: _noop,
  setIsServiceMenuPresented: _noop,
  shouldCheckForMenuPresentation: false,
  additional: EMPTY_OBJECT,
  onAddServiceListUpdate: _noop,
  getMandatoryParams: _noop,
  cutomerSorProps: EMPTY_OBJECT,
  payTypeConfigurations: EMPTY_ARRAY,
  payerPayTypeConfiguration: EMPTY_OBJECT,
  defaultSubPayType: EMPTY_STRING,
};

export default ROAddJob;
