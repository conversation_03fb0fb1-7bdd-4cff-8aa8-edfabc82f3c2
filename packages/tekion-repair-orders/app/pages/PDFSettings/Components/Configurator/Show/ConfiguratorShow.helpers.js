import produce from 'immer';

import _filter from 'lodash/filter';
import _find from 'lodash/find';
import _forEach from 'lodash/forEach';
import _set from 'lodash/set';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _isString from 'lodash/isString';
import _keyBy from 'lodash/keyBy';
import _map from 'lodash/map';
import _values from 'lodash/values';
import _reduce from 'lodash/reduce';
import _includes from 'lodash/includes';
import _pick from 'lodash/pick';
import _head from 'lodash/head';
import _isNil from 'lodash/isNil';
import _split from 'lodash/split';
import _every from 'lodash/every';
import _noop from 'lodash/noop';
import _has from 'lodash/has';
import _reject from 'lodash/reject';
import _compact from 'lodash/compact';
import _sortBy from 'lodash/sortBy';
import _castArray from 'lodash/castArray';
import _endsWith from 'lodash/endsWith';
import _mergeWith from 'lodash/mergeWith';
import _isEqual from 'lodash/isEqual';
import _some from 'lodash/some';

import {
  ContentState,
  convertFromRaw,
  convertToRaw,
  EditorState,
} from 'tcomponents/molecules/richTextEditor/RichTextEditorWithTable';
import { resolveDisclosureKey } from 'tcomponents/molecules/pdfHeaderLeftSection/pdfConfiguratorComponents.helper';
import {
  generateTransformedConfig,
  getSectionProperties,
} from 'tbusiness/appServices/service/helpers/pdfConfig.helper';
import {
  getFieldValueInCurrentLocale,
  getCurrentUserPreferredLanguage,
} from '@tekion/tekion-widgets/src/appServices/service/utils/multilingual';
import { tget } from 'tbase/utils/general';
import FileReader from 'tbase/utils/File.reader';
import RepairOrderEnv from 'utils/repairOrderEnv';
import ROConstraints from 'helpers/constraints';
import { getSectionPropertyWithTargetLanguageValues } from 'helpers/pdfConfig.helper';
import { getDocumentTypeForInitConfig } from 'helpers/pdf';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING, NO_DATA } from 'tbase/app.constants';
import { LOCALE_LABEL_MAP } from 'tbase/constants/locales';
import { COPY_TYPES } from 'tbase/constants/pdf.constants';
import { PDF_SECTION } from 'tbase/constants/pdfBuilder.constants';
import { LANGUAGES, LOCALE } from 'tbase/constants/general';
import PdfConfigReader, {
  KEY,
  VALUE,
  ORDER,
  SECTION_NAME,
  LAYOUT_CONFIG,
  COLUMNS,
  SECTION_PROPERTIES,
  DISPLAY_OPTION,
  RENDER_TYPE,
  SELECT_OPTIONS,
} from 'tbase/readers/pdfConfig.reader';
import MediaReader from 'tbase/readers/Media';
import {
  HEADER_COMPONENTS,
  FOOTER_COMPONENTS,
  LOGO_SUPPORTED_COMPONENTS_SET,
  BODY_COMPONENTS,
} from 'tcomponents/molecules/pdfHeaderLeftSection/pdfConfiguratorComponents.constants';
import ROPdfConfigReader, { LABEL, COLUMN_TYPE_VS_LABEL, COLUMN_TYPE_VS_VALUE } from 'readers/pdfConfig.reader';
import { getPdfDocumentTypeCopyTypeLabel } from 'pages/PDFSettings/Components/Configurator/Configurator.helper';
import { DEFAULT_COPY_TYPE } from 'constants/pdfSettings.constants';
import {
  CONFIG_FIELD_TYPES,
  PDF_CONFIGURATOR_KEYS,
  SECTION_TYPE_LABELS,
} from 'tbusiness/appServices/service/constants/pdfConfigurator';

import { PAPER_TYPES } from '../Configurator.constants';
import {
  COMMON_PDF_SECTIONS,
  PDF_BODY_SECTIONS_VS_DOC_TYPE,
  DP_BASED_SECTION_PROPERTIES,
  MULTI_LINGUAL_SUPPORTED_CONFIG_FIELD_TYPES,
  MULTILINGUAL_SUFFIX_STRING,
  MAKE_DISPLAY_VALUE_FIELD,
  COLUMN_ID,
  SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME,
  SECTION_PROPERTY_KEYS_VS_ALTERNATE_PROPERTY_DISPLAY_NAME,
  TOGGLE_DISABLE_RULES,
  TOGGLE_DISABLE_RULES_FOR_E_SIGN,
  E_SIGN_CUSTOMER_SIGNATURE_VISIBILITY_CONFIG,
} from './ConfiguratorShow.constants';
import { DEPARTMENT_SEPARATOR } from '../../../PDFSettings.constants';

export const getRawContentFromEditorContent = editorContent => {
  switch (true) {
    case _isEmpty(editorContent):
    case _isNil(editorContent):
      return EMPTY_OBJECT;
    case editorContent instanceof EditorState:
      return convertToRaw(editorContent.getCurrentContent());
    case editorContent instanceof ContentState:
      return convertToRaw(editorContent);
    default:
      return editorContent;
  }
};

export const getEditorStateFromRawContent = rawContent => {
  switch (true) {
    case _isEmpty(rawContent):
    case _isNil(rawContent):
      return EditorState.createEmpty();
    case _isString(rawContent):
      try {
        return getEditorStateFromRawContent(JSON.parse(rawContent));
      } catch (e) {
        return EMPTY_OBJECT;
      }
    case rawContent instanceof EditorState:
      return rawContent;
    case rawContent instanceof ContentState:
      return EditorState.createWithContent(rawContent);
    default:
      try {
        return EditorState.createWithContent(convertFromRaw(rawContent));
      } catch (e) {
        return EditorState.createEmpty();
      }
  }
};

const getUpdatedRichTextEditorProperty = sectionProperty =>
  produce(sectionProperty, draft => {
    _set(draft, 'value', getRawContentFromEditorContent(_get(sectionProperty, 'value')));
    _forEach(_get(sectionProperty, [LANGUAGES, LOCALE]), (fieldDetails, locale) => {
      const rowContent = getRawContentFromEditorContent(_get(fieldDetails, 'value'));
      _set(draft, [LANGUAGES, LOCALE, locale, 'value'], rowContent);
    });
  });

const getUpdatedSectionProperties = relevantSectionProperties =>
  _map(relevantSectionProperties, sectionProperty => {
    if (_get(sectionProperty, 'inputType', EMPTY_STRING) !== CONFIG_FIELD_TYPES.TERMS_CONTENT) {
      return sectionProperty;
    }
    return getUpdatedRichTextEditorProperty(sectionProperty);
  });

const getSectionPropertiesWithOrder = relevantSectionProperties =>
  _reduce(
    relevantSectionProperties,
    (acc, property, index) => ({
      ...acc,
      [property[KEY]]: { ...property, order: index },
    }),
    {}
  );

const getOrderForBodySectionRow = (sectionName, sectionProperties) =>
  sectionName === PDF_SECTION.BODY ? _get(sectionProperties, [0, ORDER]) : undefined;

const sortLayoutConfigByOrder = layoutConfig => _sortBy(layoutConfig, ORDER);

// pdfSections = { HEADER: {}, HEADER_SECTION_CONFIG: {} }
export const getConfiguratorFormPayload = (pdfConfig, pdfSectionValues) => {
  const pdfSections = _get(pdfConfig, PDF_CONFIGURATOR_KEYS.PDF_SECTIONS, EMPTY_ARRAY);
  return _map(pdfSections, pdfSection => {
    const sectionName = _get(pdfSection, SECTION_NAME);
    const relevantSectionConfig = _get(pdfSectionValues, `${sectionName}_SECTION_CONFIG`, EMPTY_OBJECT);
    const relevantSectionProperties = _get(pdfSectionValues, sectionName, EMPTY_ARRAY);
    // adding order to get because object are not ordered by nature in JS
    const relevantSectionPropertiesByKey = getSectionPropertiesWithOrder(relevantSectionProperties);
    const layoutConfig = _map(_get(pdfSection, LAYOUT_CONFIG, EMPTY_ARRAY), row => {
      const rowSectionProperties = _get(row, COLUMNS, EMPTY_ARRAY);
      const updatedRowSectionProperties = _map(rowSectionProperties, ({ key }) => {
        const sectionProperty = _get(relevantSectionPropertiesByKey, key);
        if (_get(sectionProperty, 'inputType', EMPTY_STRING) !== CONFIG_FIELD_TYPES.TERMS_CONTENT) {
          return sectionProperty;
        }
        return getUpdatedRichTextEditorProperty(sectionProperty);
      });
      return {
        // to support reordering of body section
        ...row,
        order: getOrderForBodySectionRow(sectionName, updatedRowSectionProperties),
        [COLUMNS]: updatedRowSectionProperties,
      };
    });

    if (!_isEmpty(layoutConfig)) {
      return {
        ...relevantSectionConfig,
        [LAYOUT_CONFIG]: sortLayoutConfigByOrder(layoutConfig),
      };
    }

    const sectionProperties = getUpdatedSectionProperties(relevantSectionProperties);

    return {
      ...relevantSectionConfig,
      sectionProperties,
    };
  });
};

export const generateConfiguratorPayload = (pdfConfig, values, status) => {
  const { departmentId, ...restPdfConfig } = pdfConfig;

  const newPdfConfigs = getConfiguratorFormPayload(pdfConfig, values);
  return {
    pdfConfigurator: {
      ...restPdfConfig,
      pdfSections: newPdfConfigs,
      transformedConfig: generateTransformedConfig({ ...restPdfConfig, pdfSections: newPdfConfigs, status }),
      status,
    },
    departmentIds: _split(departmentId, DEPARTMENT_SEPARATOR),
  };
};

const getRequiredSectionPropsPerSection = (sectionProps, documentType, copyType) =>
  _filter(sectionProps, sectionProp => {
    const isSectionPropertyNotBlockedByRequiredDP = checkSectionPropertyNotBlockedByRequiredDP(sectionProp);
    const validDocumentTypes = _get(sectionProp, 'documentTypes', EMPTY_ARRAY);
    const validCopyTypes = tget(sectionProp, 'copyTypes', _values(COPY_TYPES)); // we all only introduce copyType when we want to use the Common Section for specify copyType
    return (
      isSectionPropertyNotBlockedByRequiredDP &&
      _includes(validDocumentTypes, documentType) &&
      _includes(validCopyTypes, copyType)
    );
  });

export const constructPdfSections = (pdfSections, documentType, copyType) => {
  const keyedSections = _keyBy(pdfSections, 'sectionName') || EMPTY_OBJECT;
  const headerSection = _head(_values(_pick(keyedSections, [PDF_SECTION.HEADER])));
  const footerSection = _head(_values(_pick(keyedSections, [PDF_SECTION.FOOTER])));
  const validHeaderSectionsPerDocType = getRequiredSectionPropsPerSection(
    _get(headerSection, 'sectionProperties', EMPTY_ARRAY),
    documentType,
    copyType
  );
  const validFooterSectionsPerDocType = getRequiredSectionPropsPerSection(
    _get(footerSection, 'sectionProperties', EMPTY_ARRAY),
    documentType,
    copyType
  );
  const newPdfSections = {
    ...keyedSections,
    [PDF_SECTION.HEADER]: { ...headerSection, sectionProperties: validHeaderSectionsPerDocType },
    [PDF_SECTION.FOOTER]: { ...footerSection, sectionProperties: validFooterSectionsPerDocType },
  };
  return _values(newPdfSections);
};

export const constructInitialConfig = (docProperties, bodySection) => {
  const pdfSections = [...COMMON_PDF_SECTIONS];
  const updatedPdfSections =
    constructPdfSections(
      pdfSections,
      getDocumentTypeForInitConfig(_get(docProperties, 'documentType')),
      _get(docProperties, 'copyType')
    ) || EMPTY_ARRAY;
  updatedPdfSections.push(bodySection);

  return {
    ...docProperties,
    // default paper size for new configurations
    paperSize: PAPER_TYPES.LETTER,
    pdfSections: updatedPdfSections,
  };
};

export const checkSectionPropertyNotBlockedByRequiredDP = sectionProperty => {
  const dealerProperties = _get(sectionProperty, 'dealerProperties', EMPTY_ARRAY);
  const dealerPropertiesToNotBeTruthy = _get(sectionProperty, 'dealerPropertiesToNotBeTruthy', EMPTY_ARRAY);
  return (
    _every(dealerProperties, dealerProperty => RepairOrderEnv.dealerProperty[dealerProperty]) &&
    _every(dealerPropertiesToNotBeTruthy, dealerProperty => !RepairOrderEnv.dealerProperty[dealerProperty])
  );
};

export const getBodySections = ({ documentType, copyType }) => {
  const section = _get(PDF_BODY_SECTIONS_VS_DOC_TYPE, getDocumentTypeForInitConfig(documentType), EMPTY_ARRAY);
  if (_isEmpty(section)) {
    return section;
  }
  const sectionProperties = _filter(_get(section, 'sectionProperties', EMPTY_ARRAY), property => {
    const supportedCopyTypes = _get(property, 'copyTypes', EMPTY_ARRAY);
    const isSectionPropertyNotBlockedByRequiredDP = checkSectionPropertyNotBlockedByRequiredDP(property);
    return isSectionPropertyNotBlockedByRequiredDP && _includes(supportedCopyTypes, copyType);
  });
  return {
    ...section,
    sectionProperties,
  };
};

const addDetailsToSectionConfig = (sectionProperties = EMPTY_ARRAY, sectionKey) =>
  _map(sectionProperties, property => ({ sectionKey, ...property }));

const addDetailsToLayoutConfig = (layoutConfig = EMPTY_ARRAY, sectionKey) =>
  _reduce(
    layoutConfig,
    (acc, row) => {
      const rowSectionProperties = _get(row, COLUMNS, EMPTY_ARRAY);
      return [...acc, ..._map(rowSectionProperties, property => ({ sectionKey, ...property }))];
    },
    []
  );

export const generateFormData = pdfSections =>
  _reduce(
    pdfSections,
    (acc, section) => {
      const { sectionProperties, [LAYOUT_CONFIG]: layoutConfig, ...restSectionData } = section;
      const sectionName = PdfConfigReader.sectionName(section);
      const sectionKey = `${sectionName}_SECTION_CONFIG`;
      return {
        ...acc,
        [sectionKey]: restSectionData,
        [sectionName]: !_isEmpty(sectionProperties)
          ? addDetailsToSectionConfig(sectionProperties, sectionName)
          : addDetailsToLayoutConfig(layoutConfig, sectionName),
      };
    },
    {}
  );

export const checkPropertyNotBlockedByDp = dealerProperties =>
  _every(dealerProperties, dealerProperty => RepairOrderEnv.dealerProperty[dealerProperty]);

export { getSectionProperties };

const filterPropertyWithTargetLanguageValues = ({
  sectionProperty,
  defaultSectionProperty,
  targetLanguage,
  sectionName,
}) => {
  const propertyKey = _get(sectionProperty, KEY);
  const dealerPropertiesToCheck = _get(DP_BASED_SECTION_PROPERTIES, resolveDisclosureKey(propertyKey));
  const isValidProperty = dealerPropertiesToCheck ? checkPropertyNotBlockedByDp(dealerPropertiesToCheck) : true;
  const shouldOverrideLocaleBasedValues = !LOGO_SUPPORTED_COMPONENTS_SET.has(propertyKey);
  return isValidProperty
    ? [
        getSectionPropertyWithTargetLanguageValues({
          sectionProperty,
          targetLanguage,
          defaultSectionProperty,
          shouldOverrideLocaleBasedValues,
          sectionName,
        }),
      ]
    : [];
};

const getUpdatedSectionPropertiesWithTargetLanguage = (
  sectionProperties,
  duplicateSectionPropertiesByKey,
  targetLanguage,
  sectionName
) =>
  _reduce(
    sectionProperties,
    (acc, sectionProperty) => [
      ...acc,
      ...filterPropertyWithTargetLanguageValues({
        sectionProperty: _get(duplicateSectionPropertiesByKey, _get(sectionProperty, KEY), sectionProperty),
        defaultSectionProperty: sectionProperty,
        targetLanguage,
        sectionName,
      }),
    ],
    []
  );

const getUpdatedLayoutConfig = (layoutConfig, duplicateSectionPropertiesByKey, targetLanguage, sectionName) =>
  _map(layoutConfig, row => {
    const rowSectionProperties = _get(row, COLUMNS, EMPTY_ARRAY);
    const updatedRowSectionProperties = getUpdatedSectionPropertiesWithTargetLanguage(
      rowSectionProperties,
      duplicateSectionPropertiesByKey,
      targetLanguage
    );
    return {
      // to support reordering of body section
      ...row,
      order: getOrderForBodySectionRow(sectionName, updatedRowSectionProperties),
      [COLUMNS]: updatedRowSectionProperties,
    };
  });

export const filterPdfSectionProperties = ({ defaultPdfConfig, publishedPdfConfig = EMPTY_OBJECT, targetLanguage }) => {
  // only take those properties which are supported by required dp.
  const defaultPdfSections = _get(defaultPdfConfig, PDF_CONFIGURATOR_KEYS.PDF_SECTIONS, EMPTY_ARRAY);
  const duplicatePdfSections = _keyBy(
    _get(publishedPdfConfig, PDF_CONFIGURATOR_KEYS.PDF_SECTIONS, EMPTY_ARRAY),
    SECTION_NAME
  );
  const updatedPdfSections = _map(defaultPdfSections, section => {
    const sectionName = _get(section, SECTION_NAME);
    const duplicateSection = _get(duplicatePdfSections, sectionName) || EMPTY_OBJECT;
    const { render } = duplicateSection;
    const duplicateSectionProperties = getSectionProperties(_get(duplicatePdfSections, sectionName));
    // adding order to get because object are not ordered by nature in JS
    const duplicateSectionPropertiesByKey = getSectionPropertiesWithOrder(duplicateSectionProperties);
    const layoutConfig = tget(section, LAYOUT_CONFIG);
    const sectionProperties = tget(section, SECTION_PROPERTIES);
    const updatedConfig = !_isNil(layoutConfig)
      ? getUpdatedLayoutConfig(layoutConfig, duplicateSectionPropertiesByKey, targetLanguage, sectionName)
      : getUpdatedSectionPropertiesWithTargetLanguage(
          sectionProperties,
          duplicateSectionPropertiesByKey,
          targetLanguage,
          sectionName
        );
    const configKey = !_isNil(layoutConfig) ? LAYOUT_CONFIG : SECTION_PROPERTIES;
    return {
      ...section,
      render,
      [configKey]: sortLayoutConfigByOrder(updatedConfig),
    };
  });

  return {
    ...defaultPdfConfig,
    pdfSections: updatedPdfSections,
  };
};

export const getRichTextFieldValueInCurrentLocale = ({ key, languages, locale, value, isCurrentLanguage }) =>
  isCurrentLanguage
    ? value
    : getFieldValueInCurrentLocale(
        key,
        {
          languages,
        },
        _noop,
        locale,
        false
      );

const getResolvedLabelByCopyType = (configItem, copyType) => {
  const copyTypeVsLabel = ROPdfConfigReader.copyTypeVsLabel(configItem);
  if (_isEmpty(copyTypeVsLabel)) return ROPdfConfigReader.label(configItem);
  return tget(copyTypeVsLabel, copyType, tget(copyTypeVsLabel, DEFAULT_COPY_TYPE, NO_DATA));
};

const getUpdatedConfigValue = ({
  config,
  defaultSectionConfigByKey,
  updatedSectionConfigByKey,
  copyType,
  sectionKey,
}) => {
  const configKey = PdfConfigReader.key(config);
  const defaultConfigValue = _get(defaultSectionConfigByKey, configKey);
  const updatedConfigValue = _get(updatedSectionConfigByKey, configKey);
  const configValue = PdfConfigReader.value(updatedConfigValue);
  const copyTypeVsValue = ROPdfConfigReader.copyTypeVsValue(updatedConfigValue);
  return {
    ...config,
    ...(_has(defaultConfigValue, COLUMN_TYPE_VS_LABEL)
      ? {
          [COLUMN_TYPE_VS_LABEL]: ROPdfConfigReader.columnTypeVsLabel(defaultConfigValue),
        }
      : {
          [LABEL]: getResolvedLabelByCopyType(defaultConfigValue, copyType),
        }),
    ...(_has(updatedConfigValue, COLUMN_TYPE_VS_VALUE)
      ? {
          [COLUMN_TYPE_VS_VALUE]: {
            ...ROPdfConfigReader.columnTypeVsValue(defaultConfigValue),
            ...ROPdfConfigReader.columnTypeVsValue(updatedConfigValue),
          },
        }
      : {
          [VALUE]: copyTypeVsValue ? copyTypeVsValue[copyType] || copyTypeVsValue[DEFAULT_COPY_TYPE] : configValue,
        }),
    [DISPLAY_OPTION]: _get(updatedConfigValue, DISPLAY_OPTION, _get(defaultConfigValue, DISPLAY_OPTION)),
    [RENDER_TYPE]: _get(updatedConfigValue, RENDER_TYPE, _get(defaultConfigValue, RENDER_TYPE)),
    [SELECT_OPTIONS]: _get(updatedConfigValue, SELECT_OPTIONS, EMPTY_ARRAY),
    sectionKey,
  };
};

// If the value exists in the second object, use it, otherwise use the value from the first object
const customMergeLogic = (objValue, srcValue) => (srcValue === undefined ? objValue : srcValue);

/**
 * @param defaultSectionConfig - UI stores defaultSectionConfig based on combination of sectionKey(BODY) + key( e.g. JOBS )
 * @param defaultSectionConfigByKey - store defaultSectionConfig by key only
 * @param sectionConfig - Backend Response for same sectionConfig
 * @returns {*}
 */

export const getDefaultSectionConfigWithUpdatedValue = ({
  defaultSectionConfigByKey,
  sectionConfig,
  copyType,
  sectionKey,
}) => {
  // The following ensures removal of deprecated keys and introductions of new keys without migration and maintain UI driven order
  const sectionConfigByKey = _keyBy(sectionConfig, KEY);
  const updatedSectionConfigByKey = _mergeWith({}, defaultSectionConfigByKey, sectionConfigByKey, customMergeLogic);
  return _reduce(
    defaultSectionConfigByKey,
    (acc, config) => {
      const supportedCopyTypes = ROPdfConfigReader.supportedCopyTypes(config);
      const isvalidSectionProperty =
        checkSectionPropertyNotBlockedByRequiredDP(config) &&
        (_isEmpty(supportedCopyTypes) || new Set(supportedCopyTypes).has(copyType));
      return isvalidSectionProperty
        ? [
            ...acc,
            getUpdatedConfigValue({
              config,
              defaultSectionConfigByKey,
              updatedSectionConfigByKey,
              copyType,
              sectionKey,
            }),
          ]
        : acc;
    },
    EMPTY_ARRAY
  );
};

const getFormattedConfigMetaData = (configMetadata, make) => {
  const configMetadataLabels = {
    departmentLabel: configMetadata?.departmentLabel,
    documentType: getPdfDocumentTypeCopyTypeLabel({
      documentTypeLabel: configMetadata?.documentTypeLabel,
      documentType: configMetadata?.documentType,
    }),
    copyType: getPdfDocumentTypeCopyTypeLabel({
      copyType: configMetadata?.copyType,
      subType: configMetadata?.subType,
    }),
    pdfSection: SECTION_TYPE_LABELS[configMetadata?.pdfSectionName],
    make,
  };
  return ROConstraints.isMultiLingualEnabled()
    ? __('{{departmentLabel}} - {{documentType}} - {{copyType}} - {{pdfSection}} - {{make}} - {{locale}}', {
        ...configMetadataLabels,
        locale: LOCALE_LABEL_MAP[configMetadata?.locale],
      })
    : __('{{departmentLabel}} - {{documentType}} - {{pdfSection}} - {{copyType}} - {{make}}', configMetadataLabels);
};

const getSanitizedConfigMetaDatalist = (acc, logoId, configMetadata, make) => {
  let updatedAcc = acc;
  const formattedConfigMetaData = getFormattedConfigMetaData(configMetadata, make);
  _forEach(acc, (configMetadataList, mediaId) => {
    updatedAcc = {
      ...updatedAcc,
      [mediaId]: _reject(configMetadataList, configString => configString === formattedConfigMetaData),
    };
  });

  const accWithUpdatedConfigMetaData = {
    ...updatedAcc,
    [logoId]: [...(updatedAcc[logoId] || EMPTY_ARRAY), formattedConfigMetaData],
  };

  return accWithUpdatedConfigMetaData;
};

const getLogoIdVsMultilingualConfigMetaDataList = (
  initialAccValue,
  multiLingualLogoDetailsByLocale,
  configMetaData
) => {
  const updatedAcc = _reduce(
    multiLingualLogoDetailsByLocale,
    (acc, logoDetails, locale) => {
      if (locale === getCurrentUserPreferredLanguage()) return acc;
      const logoValue = tget(logoDetails, 'logo', EMPTY_OBJECT);
      const logoId = MediaReader.mediaId(logoValue);
      const make = _get(logoValue, MAKE_DISPLAY_VALUE_FIELD, '');
      const updatedConfigMetaData = {
        ...configMetaData,
        locale,
      };
      return getSanitizedConfigMetaDatalist(acc, logoId, updatedConfigMetaData, make);
    },
    initialAccValue
  );
  return updatedAcc;
};

const getConfigMetaData = (configSettings, departmentIdVsLabel, pdfSectionName) => ({
  departmentLabel: tget(
    departmentIdVsLabel,
    ROPdfConfigReader.departmentId(configSettings),
    ROPdfConfigReader.departmentId(configSettings)
  ),
  documentType: ROPdfConfigReader.documentType(configSettings),
  documentTypeLabel: ROPdfConfigReader.documentTypeLabel(configSettings),
  copyType: ROPdfConfigReader.copyType(configSettings),
  subType: ROPdfConfigReader.subType(configSettings),
  locale: getCurrentUserPreferredLanguage(),
  pdfSectionName,
});

const getLogoIdVsConfigMetaDataListForSectionProperty = (acc, logoDetails, configMetaData) => {
  const logosWithMake = _compact(_castArray(PdfConfigReader.value(logoDetails)));

  let updatedAcc = acc;
  if (!_isEmpty(logosWithMake)) {
    _forEach(logosWithMake, item => {
      const logoId = MediaReader.mediaId(item);
      const make = _get(item, MAKE_DISPLAY_VALUE_FIELD, '');
      updatedAcc = getSanitizedConfigMetaDatalist(updatedAcc, logoId, configMetaData, make);
      const multiLingualLogoDetailsByLocale = tget(item, [LANGUAGES, LOCALE], EMPTY_OBJECT);
      if (!_isEmpty(multiLingualLogoDetailsByLocale)) {
        updatedAcc = getLogoIdVsMultilingualConfigMetaDataList(
          updatedAcc,
          multiLingualLogoDetailsByLocale,
          configMetaData
        );
      }
    });
  }
  return updatedAcc;
};

const getLogoDetails = ({ pdfSections, sectionPropertyKey, pdfSectionName }) => {
  const section = _find(pdfSections, pdfSection => PdfConfigReader.sectionName(pdfSection) === pdfSectionName);
  const sectionProperties = getSectionProperties(section);
  return _find(sectionProperties, sectionProperty => PdfConfigReader.key(sectionProperty) === sectionPropertyKey);
};

const getSingleSettingsLogoIdVsConfigMetaDataList = (acc, configuration, departmentIdVsLabel) => {
  const pdfSections = PdfConfigReader.pdfSections(configuration);
  let pdfSectionName = PDF_SECTION.HEADER;
  let configMetaData = getConfigMetaData(configuration, departmentIdVsLabel, pdfSectionName);
  const headerLogoDetails = getLogoDetails({
    pdfSections,
    sectionPropertyKey: HEADER_COMPONENTS.LOGO,
    pdfSectionName,
  });
  const updatedAcc = getLogoIdVsConfigMetaDataListForSectionProperty(acc, headerLogoDetails, configMetaData);
  pdfSectionName = PDF_SECTION.FOOTER;
  const footerLogoDetails = getLogoDetails({
    pdfSections,
    sectionPropertyKey: FOOTER_COMPONENTS.FOOTER_LOGO,
    pdfSectionName,
  });
  configMetaData = {
    ...configMetaData,
    pdfSectionName,
  };
  return getLogoIdVsConfigMetaDataListForSectionProperty(updatedAcc, footerLogoDetails, configMetaData);
};

export const getLogoIdVsConfigMetaDataList = (configurations, departmentIdVsLabel) =>
  _reduce(
    configurations,
    (acc, configuration) => getSingleSettingsLogoIdVsConfigMetaDataList(acc, configuration, departmentIdVsLabel),
    EMPTY_OBJECT
  );

export const getSaveLogoPayload = ({ mediaItem }) => {
  const { file, url, name, mediaId } = mediaItem;
  return {
    file,
    url,
    name,
    mediaId,
    contentType: FileReader.getFileType(file),
  };
};

export const getMultiLingualFieldId = fieldId => `${fieldId}${MULTILINGUAL_SUFFIX_STRING}`;

export const isMultiLingualFieldId = fieldId => _endsWith(fieldId, MULTILINGUAL_SUFFIX_STRING);

const checkMultiLingualSupportedInputTypeExist = sectionProperties => {
  const multiLingualSupportedInputTypeSectionProperty = _find(sectionProperties, sectionProperty =>
    MULTI_LINGUAL_SUPPORTED_CONFIG_FIELD_TYPES.has(PdfConfigReader.inputType(sectionProperty))
  );
  return !_isEmpty(multiLingualSupportedInputTypeSectionProperty);
};

export const shouldShowMultiLingualButton = pdfConfig => {
  let isMultiLingualSupportedInputTypeExist = false;
  _forEach(PdfConfigReader.pdfSections(pdfConfig), pdfSection => {
    const sectionProperties = getSectionProperties(pdfSection);
    if (checkMultiLingualSupportedInputTypeExist(sectionProperties)) {
      isMultiLingualSupportedInputTypeExist = true;
    }
  });
  return isMultiLingualSupportedInputTypeExist;
};

export const getLocaleSpecificPropertyDisplayName = ({ sectionPropertyKey, propertyDisplayName, languages }) => {
  const localeSpecificPropertyDisplayName = getFieldValueInCurrentLocale(
    COLUMN_ID.FIELD_NAME,
    { languages },
    _noop,
    getCurrentUserPreferredLanguage(),
    false
  );
  const defaultPropertyDisplayName = _get(
    SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME,
    sectionPropertyKey,
    propertyDisplayName
  );
  const alternatePropertyDisplayName = ROConstraints.isInchcapeProgram()
    ? _get(SECTION_PROPERTY_KEYS_VS_ALTERNATE_PROPERTY_DISPLAY_NAME, sectionPropertyKey)
    : undefined;
  return localeSpecificPropertyDisplayName || alternatePropertyDisplayName || defaultPropertyDisplayName;
};

export const isValueModified = (previousValue, currentValue) => !_isEqual(currentValue, previousValue);

const shouldIncludeInESign = activeDisclosureSections =>
  _some(activeDisclosureSections, ({ value }) =>
    _some(value, ({ fieldAnnotations, groupAnnotations }) => {
      const fieldHasMandatory = _some(fieldAnnotations, { mandatory: true });
      const groupHasMandatory = _some(groupAnnotations, { mandatory: true });
      return fieldHasMandatory || groupHasMandatory;
    })
  );

export const updatePdfConfigForESign = (data = EMPTY_OBJECT, copyType, documentType) => {
  if (!shouldRenderDisclosureSignatureComponent(documentType, copyType)) {
    return {
      data,
      showValidationMessage: false,
    };
  }

  const pdfConfigurator = _get(data, 'pdfConfigurator');
  const pdfSectionsArray = _get(pdfConfigurator, 'pdfSections', EMPTY_ARRAY);
  const pdfSections = _find(pdfSectionsArray, { sectionName: PDF_SECTION.BODY });
  const sectionProperties = _get(pdfSections, 'sectionProperties', EMPTY_ARRAY);
  const isCustomerSignatureEnabled = _get(_find(sectionProperties, { key: 'CUSTOMER_SIGNATURE' }), 'render', false);
  if (isCustomerSignatureEnabled) {
    return {
      data: {
        ...data,
        pdfConfigurator: {
          ...pdfConfigurator,
          includeInESignFlow: true,
        },
      },
      showValidationMessage: false,
    };
  }
  const activeDisclosureSections = _filter(
    sectionProperties,
    ({ key, render }) =>
      _includes(
        [
          BODY_COMPONENTS.DISCLOSURE,
          BODY_COMPONENTS.CUSTOM_DISCLOSURE_1,
          BODY_COMPONENTS.CUSTOM_DISCLOSURE_2,
          BODY_COMPONENTS.CUSTOM_DISCLOSURE_3,
          BODY_COMPONENTS.CUSTOM_DISCLOSURE_4,
        ],
        key
      ) && render
  );
  const includeInESign = shouldIncludeInESign(activeDisclosureSections);
  return {
    data: {
      ...data,
      pdfConfigurator: {
        ...pdfConfigurator,
        includeInESignFlow: includeInESign,
      },
    },
    showValidationMessage: !_isEmpty(activeDisclosureSections) && !includeInESign,
  };
};

export const getIsToggleDisabled = (bodyComponentKey, isDisabled, documentType, copyType) => {
  const toggledisableRules = ROConstraints.isNewESignEnhancementEnabled()
    ? TOGGLE_DISABLE_RULES_FOR_E_SIGN
    : TOGGLE_DISABLE_RULES;
  const docRules = toggledisableRules[bodyComponentKey]?.[documentType];
  const shouldDisable = docRules?.includes(copyType);
  return shouldDisable || isDisabled;
};

export const shouldRenderDisclosureSignatureComponent = (documentType, copyType) => {
  const docRules = E_SIGN_CUSTOMER_SIGNATURE_VISIBILITY_CONFIG[documentType];
  const shouldIncludeESign = _includes(docRules, copyType);
  return ROConstraints.isNewESignEnhancementEnabled() && shouldIncludeESign;
};
