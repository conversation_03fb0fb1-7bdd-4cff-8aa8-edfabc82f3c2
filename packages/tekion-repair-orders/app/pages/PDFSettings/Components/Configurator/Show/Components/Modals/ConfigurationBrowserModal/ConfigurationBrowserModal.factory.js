import React from 'react';

import _get from 'lodash/get';
import _noop from 'lodash/noop';

import NullComponent from 'tcomponents/atoms/nullComponent/NullComponent';
import Button from 'tcomponents/atoms/Button/Button';
import { toaster, TOASTER_TYPE } from 'tcomponents/organisms/NotificationWrapper';
import { getAdaptedCustomSectionConfiguratorState } from 'helpers/customSectionConfigurator.helpers';
import { BODY_COMPONENTS } from 'tcomponents/molecules/pdfHeaderLeftSection/pdfConfiguratorComponents.constants';
import { EMPTY_OBJECT } from 'tbase/app.constants';

import PreviewPanel from '../../SectionConfigurationDrawer/organisms/CustomSectionConfigurator/Components/PreviewPanel';

export const COMPONENT_VS_MODAL_HEADING = {
  [BODY_COMPONENTS.DISCLOSURE]: __('Custom Disclosure Statement'),
  [BODY_COMPONENTS.CUSTOM_DISCLOSURE_1]: __('Custom Disclosure Statement 1'),
  [BODY_COMPONENTS.CUSTOM_DISCLOSURE_2]: __('Custom Disclosure Statement 2'),
  [BODY_COMPONENTS.CUSTOM_DISCLOSURE_3]: __('Custom Disclosure Statement 3'),
  [BODY_COMPONENTS.CUSTOM_DISCLOSURE_4]: __('Custom Disclosure Statement 4'),
  DEFAULT: __('Browse Configurations'),
};

export const COMPONENT_VS_PREVIEW_RENDERER = {
  [BODY_COMPONENTS.DISCLOSURE]: PreviewPanel,
  DEFAULT: NullComponent,
};

export const GET_RENDERER_PROPS_FOR_COMPONENT = {
  [BODY_COMPONENTS.DISCLOSURE]: ({ documentSection }) => {
    const { value } = documentSection;
    const adapatedSections = getAdaptedCustomSectionConfiguratorState(value);
    const sections = _get(adapatedSections, 'values.sections');
    return { sections, componentKey: BODY_COMPONENTS.DISCLOSURE };
  },
  DEFAULT: () => EMPTY_OBJECT,
};

const getLeftSectionRendererForDisclosure =
  ({ handleCloseModal, handleOpenConfigurationDrawer, currentSectionValue }) =>
  () => {
    const handleConfigureSection = () => {
      handleCloseModal();
      handleOpenConfigurationDrawer(currentSectionValue);
    };

    return (
      <Button view={Button.VIEW.SECONDARY} onClick={handleConfigureSection}>
        {__('Choose and Configure Section')}
      </Button>
    );
  };

export const COMPONENT_VS_FOOTER_LEFT_SECTION_RENDERER = {
  [BODY_COMPONENTS.DISCLOSURE]: getLeftSectionRendererForDisclosure,
  DEFAULT: () => () => NullComponent,
};

export const COMPONENT_VS_SUBMIT_ACTION = {
  [BODY_COMPONENTS.DISCLOSURE]: () => toaster(TOASTER_TYPE.SUCCESS, __('Disclosure statement added successfully')),
  DEFAULT: _noop,
};
