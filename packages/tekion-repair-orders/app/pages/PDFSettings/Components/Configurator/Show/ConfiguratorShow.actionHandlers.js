import produce from 'immer';

import _filter from 'lodash/filter';
import _get from 'lodash/get';
import _head from 'lodash/head';
import _map from 'lodash/map';
import _set from 'lodash/set';
import _isNil from 'lodash/isNil';
import _keyBy from 'lodash/keyBy';
import _values from 'lodash/values';
import _dropRight from 'lodash/dropRight';
import _reject from 'lodash/reject';
import _noop from 'lodash/noop';
import _forEach from 'lodash/forEach';
import _isEmpty from 'lodash/isEmpty';

import { toaster, TOASTER_TYPE } from 'tcomponents/organisms/NotificationWrapper';
import { getMediaWithSignedUrlsUsingMediaV3 } from 'tbase/utils/mediaUtils';
import { getQueryParams, tget, getStateFromHistory, genericActionHandlerAdapter } from 'tbase/utils/general';
import MediaReader from 'tbase/readers/Media';
import { getCurrentUserPreferredLanguage } from 'twidgets/appServices/service/utils/multilingual';

import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import { ROUTE_CONFIG } from 'tbusiness/appServices/service/helpers/route';
import { DEFAULT_DEPARTMENT } from 'tbusiness/constants/department';
import FORM_PAGE_ACTION_TYPES from 'tcomponents/pages/formPage/constants/actionTypes';
import TABLE_ACTION_TYPES from 'tcomponents/molecules/tableInputField/constants/TableInputField.actionTypes';
import FORM_ACTION_TYPES from 'tcomponents/organisms/FormBuilder/constants/actionTypes';
import MULTI_LINGUAL_FORM_ACTION_HANDLERS from 'tcomponents/hoc/withMultiLingualForm/MultiLingualForm.actionHandlers';

import { FOOTER_COMPONENTS } from '@tekion/tekion-components/src/molecules/pdfHeaderLeftSection/pdfConfiguratorComponents.constants';
import { MULTI_LINGUAL_FORM_ACTION_TYPES } from 'tcomponents/hoc/withMultiLingualForm/MultiLingualForm.constants';
import { LANGUAGES, LOCALE } from 'tbase/constants/general';
import { VALUE, VALUES, KEY } from 'tbase/readers/pdfConfig.reader';

import { updatePDFConfig } from '../Configurator.actions';
import { fetchLogos, saveLogoToLibrary, deleteLogoFromLibrary } from './ConfiguratorShow.actions';
import {
  generateConfiguratorPayload,
  getSaveLogoPayload,
  isMultiLingualFieldId,
  updatePdfConfigForESign,
} from './ConfiguratorShow.helpers';
import { showSectionConfiguratorDrawer } from './ConfiguratorShowForm.events';

import { FIELD_IDS, INIT_FORM, CUSTOM_ACTION_TYPES, CONFIG_STATUSES } from './ConfiguratorShow.constants';

const initForm = ({ params, setState }) => {
  const { values } = params;
  setState({ values });
};

const handleOpenModal = ({ params: { openModal: modal, modalContent, handleOpenConfigurationDrawer }, setState }) => {
  setState({ modal, modalContent, handleOpenConfigurationDrawer });
};

const handleFieldChange = ({ params, getState, setState }) => {
  const { sectionKey, value } = params;
  const { values } = getState();

  setState({ values: { ...values, [sectionKey]: value } });
};

const defaultLocaleValueSetter = ({ property, value, locale, updatedId }) =>
  _set(property, [LANGUAGES, LOCALE, locale, updatedId], value);

const HEADER_SECTION_PROPERTY_KEY_TO_LOCALE_VALUE_SETTER = {
  [FOOTER_COMPONENTS.FOOTER_LOGO]: _noop,
  // since we're maintaining make level language object for logos
  DEFAULT: defaultLocaleValueSetter,
};

const handleSectionPropertyChange = ({ getState, setState, params }) => {
  const { values } = getState();
  const {
    sectionKey,
    key,
    id,
    value,
    closeModal = false,
    values: sectionValues,
    locale = getCurrentUserPreferredLanguage(),
    additionalUpdates = EMPTY_ARRAY,
  } = params;

  const updatedId = isMultiLingualFieldId(id) ? VALUE : id;
  const updatedValues = produce(values, draft => {
    const section = _get(draft, sectionKey);
    const property = _head(_filter(section, item => item.key === key));
    _set(property, updatedId, value);
    _set(property, VALUES, sectionValues);
    const localeValueSetter =
      HEADER_SECTION_PROPERTY_KEY_TO_LOCALE_VALUE_SETTER[key] ||
      HEADER_SECTION_PROPERTY_KEY_TO_LOCALE_VALUE_SETTER.DEFAULT;
    localeValueSetter({ property, value, locale, updatedId });
    if (!_isEmpty(additionalUpdates)) {
      _forEach(additionalUpdates, ({ id, value }) => _set(property, id, value));
    }
  });

  const newState = closeModal
    ? {
        values: updatedValues,
        modal: false,
        modalContent: EMPTY_OBJECT,
      }
    : {
        values: updatedValues,
      };

  setState(newState);
};

export const TABLE_ACTION_HANDLERS = {
  [FIELD_IDS.HEADER]: handleSectionPropertyChange,
  [FIELD_IDS.BODY]: handleSectionPropertyChange,
  [FIELD_IDS.FOOTER]: handleSectionPropertyChange,
  [FIELD_IDS.BACK_PRINT]: handleSectionPropertyChange,
  [FIELD_IDS.BASELINE_PAGE]: handleSectionPropertyChange,
};

const fieldOnChangeHandlers = {
  ...TABLE_ACTION_HANDLERS,
  DEFAULT: handleFieldChange,
};

const onFieldChange = ({ getState, setState, params = EMPTY_OBJECT }) => {
  const { sectionKey } = params;
  const handler = fieldOnChangeHandlers[sectionKey] || fieldOnChangeHandlers.DEFAULT;
  handler({ getState, setState, params });
};

const handleSubmit = ({ params, getState, setState }) => {
  const state = getState();
  const { pdfConfig, values, updateConfigurationList, navigate, location } = state;
  const { status } = params;

  const data = generateConfiguratorPayload(pdfConfig, values, status);

  setState(
    {
      [status === CONFIG_STATUSES.PUBLISHED ? 'isPublishing' : 'isSaving']: true,
    },
    () => {
      const queryParams = getQueryParams({
        navigate,
        location,
      });
      const {
        departmentId = DEFAULT_DEPARTMENT,
        copyType,
        documentType,
        subType,
        duplicateFromId,
        enhancedExport,
      } = queryParams;
      const { data: modifiedData, showValidationMessage } = updatePdfConfigForESign(data, copyType, documentType);
      if (showValidationMessage) {
        setState({
          isSaving: false,
          isPublishing: false,
          showConfirmPublishModal: false,
        });
        return toaster(
          TOASTER_TYPE.ERROR,
          __('At least one mandatory input field must be configured in any disclosure section.')
        );
      }
      return updatePDFConfig(modifiedData)
        .then(responseData => {
          const { parentSubType } = getStateFromHistory({
            navigate,
            location,
          });
          if (!_isNil(duplicateFromId)) {
            const url = ROUTE_CONFIG.PDF_CONFIGURATOR.getUrl({
              departmentId,
              copyType,
              subType,
              documentType,
              enhancedExport,
            });
            navigate(url, {
              state: { parentSubType },
            });
          }

          updateConfigurationList(responseData, departmentId);
        })
        .catch(() => {
          toaster(TOASTER_TYPE.ERROR, __('Not able to save configs right now, please try again later'));
          const { status: publishedStatus, ...restConfig } = modifiedData;
          updateConfigurationList(restConfig, departmentId);
        })
        .finally(() =>
          setState({
            isSaving: false,
            isPublishing: false,
            showConfirmPublishModal: false,
          })
        );
    }
  );
};

const handleTableRowsOrderChange = ({ getState, setState, params: { updatedRows } }) => {
  const { sectionKey } = _head(updatedRows);
  const { values } = getState();
  setState({
    values: {
      ...values,
      [sectionKey]: updatedRows,
    },
  });
};

const handleCloseModal = ({ setState }) => setState({ modal: false, modalContent: EMPTY_OBJECT });

const handleConfirmPublish = ({ setState }) => setState({ showConfirmPublishModal: true });

const handleCancelPublish = ({ setState }) => setState({ showConfirmPublishModal: false });

const handlePublish = ({ params, getState, setState }) => {
  _set(params, 'status', CONFIG_STATUSES.PUBLISHED);
  handleSubmit({ params, getState, setState });
};

const updatePaperSize = ({ params: paperSize, setState, getState }) => {
  const { pdfConfig } = getState();
  setState({ pdfConfig: { ...pdfConfig, paperSize } });
};

const parsedMediaItem = data => {
  const { id, mediaItem } = data;
  return { ...mediaItem, id };
};

const onFetchLogos = ({ setState }) => {
  fetchLogos().then(data => {
    getMediaWithSignedUrlsUsingMediaV3(_map(data, parsedMediaItem)).then(logos => {
      setState({ logos, finishedLoadingLogos: true });
    });
  });
};

const onAddLogoToLibrary = ({ params, getState, setState }) => {
  const { logos = EMPTY_ARRAY } = getState();
  saveLogoToLibrary(getSaveLogoPayload(params)).then(data => {
    const logoToAdd = {
      ..._get(data, 'mediaItem', EMPTY_OBJECT),
      url: _get(params, ['mediaItem', 'url']),
      id: _get(data, 'id'),
    };
    setState({ logos: [logoToAdd, ...logos] });
  });
};
const onRemoveLogoFromLibrary = ({ params, getState, setState }) => {
  const { logos = EMPTY_ARRAY, selectedLogo } = getState();
  const { id } = params;
  const selectedLogoId = MediaReader.id(selectedLogo);
  const isSelectedLogoRemoved = selectedLogoId === id;
  const updatedLogos = _reject(logos, { id });
  deleteLogoFromLibrary(id).then(() => {
    setState({ logos: updatedLogos, ...(isSelectedLogoRemoved && { selectedLogo: null }) });
  });
};

const onToggleShowEditorViewController = ({ params, getState, setState }) => {
  const { checked, sectionKey, key, viewControllerKey } = params || EMPTY_OBJECT;
  const { values } = getState();
  const sectionConfig = _get(values, sectionKey);
  const keyedSectionConfig = _keyBy(sectionConfig, KEY) || EMPTY_OBJECT;
  const sectionValues = tget(keyedSectionConfig, [key, VALUES], EMPTY_OBJECT);
  const updatedShowEditorViewControllerConfig = {
    ...(keyedSectionConfig[key] || EMPTY_OBJECT),
    values: { ...sectionValues, [viewControllerKey]: checked },
  };
  const updatedFooterSectionConfig = _values({ ...keyedSectionConfig, [key]: updatedShowEditorViewControllerConfig });
  setState({
    values: { ...values, [sectionKey]: updatedFooterSectionConfig },
  });
};

const handleMultiLingualFieldChange = genericActionHandlerAdapter(({ setState, params }) => {
  const { value, language, values: sectionValues, valuePath } = params;
  const pathPrefix = _dropRight(valuePath);
  setState(
    produce(draftState => {
      _set(draftState, ['values', ...pathPrefix, LANGUAGES, LOCALE, language, VALUE], value);
      _set(draftState, ['values', ...pathPrefix, LANGUAGES, LOCALE, language, VALUES], sectionValues);
    })
  );
});

const handleMultiLingualSaveModal = ({ getState, setState, params }) => {
  const { values } = getState();
  const { sectionKey, key, id, value, closeModal = false, values: sectionValues, locale } = params;

  const updatedValues = produce(values, draft => {
    const section = _get(draft, sectionKey);
    const property = _head(_filter(section, item => item.key === key));
    _set(property, [LANGUAGES, LOCALE, locale, id], value);
    _set(property, [LANGUAGES, LOCALE, locale, VALUES], sectionValues);
  });

  const newState = closeModal
    ? {
        values: updatedValues,
        modal: false,
        modalContent: EMPTY_OBJECT,
      }
    : {
        values: updatedValues,
      };

  setState(newState);
};

const handleMultiLingualHideWindow = genericActionHandlerAdapter(({ setState }) => {
  setState(state => ({
    ...state,
    multiLingualFormProps: {
      ...state?.multiLingualFormProps,
      isMultiLingualWindowVisible: false,
    },
    modal: false,
    modalContent: EMPTY_OBJECT,
  }));
});

const handleOpenSectionConfigurationDrawer = ({ params: { drawerContent } }) => {
  showSectionConfiguratorDrawer(drawerContent);
};

export const ACTION_HANDLERS = {
  ...MULTI_LINGUAL_FORM_ACTION_HANDLERS,
  [MULTI_LINGUAL_FORM_ACTION_TYPES.ON_MULTI_LINGUAL_FIELD_CHANGE]: handleMultiLingualFieldChange,
  [MULTI_LINGUAL_FORM_ACTION_TYPES.HIDE_WINDOW]: handleMultiLingualHideWindow,
  [INIT_FORM]: initForm,
  [FORM_ACTION_TYPES.ON_FIELD_CHANGE]: onFieldChange,
  [FORM_PAGE_ACTION_TYPES.ON_FORM_SUBMIT]: handleSubmit,
  [TABLE_ACTION_TYPES.TABLE_ROWS_ORDER_CHANGE]: handleTableRowsOrderChange,
  [CUSTOM_ACTION_TYPES.OPEN_MODAL]: handleOpenModal,
  [CUSTOM_ACTION_TYPES.CLOSE_MODAL]: handleCloseModal,
  [CUSTOM_ACTION_TYPES.UPDATE_PAPER_SIZE]: updatePaperSize,
  [CUSTOM_ACTION_TYPES.CONFIRM_PUBLISH]: handleConfirmPublish,
  [CUSTOM_ACTION_TYPES.PUBLISH]: handlePublish,
  [CUSTOM_ACTION_TYPES.CANCEL_PUBLISH]: handleCancelPublish,
  [CUSTOM_ACTION_TYPES.FETCH_LOGOS]: onFetchLogos,
  [CUSTOM_ACTION_TYPES.ADD_LOGO_TO_LIBRARY]: onAddLogoToLibrary,
  [CUSTOM_ACTION_TYPES.TOGGLE_EDITOR_VIEW_CONTROLLER]: onToggleShowEditorViewController,
  [CUSTOM_ACTION_TYPES.ON_MULTI_LINGUAL_SAVE_MODAL]: handleMultiLingualSaveModal,
  [CUSTOM_ACTION_TYPES.OPEN_SECTION_CONFIGURATION_DRAWER]: handleOpenSectionConfigurationDrawer,
  [CUSTOM_ACTION_TYPES.REMOVE_LOGO_FROM_LIBRARY]: onRemoveLogoFromLibrary,
};
