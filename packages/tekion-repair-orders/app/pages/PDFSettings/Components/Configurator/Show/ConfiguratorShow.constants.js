import _values from 'lodash/values';
import _omit from 'lodash/omit';
import _get from 'lodash/get';
import _keys from 'lodash/keys';
import _map from 'lodash/map';

import ROConstraints from 'helpers/constraints';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_ROW } from 'tbase/app.constants';
import { COPY_TYPES } from 'tbase/constants/pdf.constants';
import { PDF_SECTION } from 'tbase/constants/pdfBuilder.constants';
import {
  SECTION_TYPES,
  SECTION_TYPE_PAGE_TYPES_MAPPING,
  PAGE_TYPES,
} from 'tbase/constants/pdfConfig/pdfConfigConstants';
import DEALER_PROPERTY from 'tbase/constants/dealerProperties';
import {
  HEADER_COMPONENTS,
  FOOTER_COMPONENTS,
  BODY_COMPONENTS,
  BACK_PRINT_COMPONENTS,
  BASELINE_PAGE_COMPONENTS,
  GENERAL_TERMS_COMPONENTS,
  LOGO_SUPPORTED_COMPONENTS_SET,
  DISCLOSURE_FIELDS,
} from 'tcomponents/molecules/pdfHeaderLeftSection/pdfConfiguratorComponents.constants';

import { BODY_SECTION_CONFIGURATIONS, COST_BREAKDOWN_FIELDS } from 'exports/exports.constants';
import { JOBS_COMPONENT_KEY, JOB_SUB_SECTION_KEY, SEVERITY_DETAILS } from 'constants/pdfSettings.constants';
import { CONFIG_FIELD_TYPES } from 'tbusiness/appServices/service/constants/pdfConfigurator';

import { DOC_TYPES } from '../Configurator.constants';

export const CONFIG_STATUSES = {
  PUBLISHED: 'PUBLISHED',
  NON_PUBLISHED: 'NON_PUBLISHED',
  NOT_CONFIGURED: 'NOT_CONFIGURED',
};

export const INITIAL_VALUES = EMPTY_ARRAY;
export const INIT_FORM = 'INIT_FORM';
export const RESET_FORM = 'RESET_FORM';

export const CUSTOM_ACTION_TYPES = {
  OPEN_TERMS_EDIT_MODAL: 'OPEN_TERMS_EDIT_MODAL',
  SAVE_TERM_CONTENT_FROM_MODAL: 'SAVE_TERM_CONTENT_FROM_MODAL',
  OPEN_MODAL: 'OPEN_MODAL',
  CLOSE_MODAL: 'CLOSE_MODAL',
  UPDATE_LOGO_FROM_LIBRARY: 'UPDATE_LOGO_FROM_LIBRARY',
  CONFIRM_PUBLISH: 'CONFIRM_PUBLISH',
  CANCEL_PUBLISH: 'CANCEL_PUBLISH',
  PUBLISH: 'PUBLISH',
  UPDATE_PAPER_SIZE: 'UPDATE_PAPER_SIZE',
  FETCH_LOGOS: 'FETCH_LOGOS',
  ADD_LOGO_TO_LIBRARY: 'ADD_LOGO_TO_LIBRARY',
  TOGGLE_EDITOR_VIEW_CONTROLLER: 'TOGGLE_EDITOR_VIEW_CONTROLLER',
  ON_MULTI_LINGUAL_SAVE_MODAL: 'ON_MULTI_LINGUAL_SAVE_MODAL',
  OPEN_SECTION_CONFIGURATION_DRAWER: 'OPEN_SECTION_CONFIGURATION_DRAWER',
  REMOVE_LOGO_FROM_LIBRARY: 'REMOVE_LOGO_FROM_LIBRARY',
  CONFIRMATION_DIALOG_MODAL: 'CONFIRMATION_DIALOG_MODAL',
};

export const MODAL_NAMES = {
  TERMS_EDIT_MODAL: 'TERMS_EDIT_MODAL',
  TERMS_BROWSER_MODAL: 'TERMS_BROWSER_MODAL',
  LOGO_BROWSER_MODAL: 'LOGO_BROWSER_MODAL',
  SECTION_CONFIGURATION_MODAL: 'SECTION_CONFIGURATION_MODAL',
  NESTED_SECTION_CONFIG_MODAL: 'NESTED_SECTION_CONFIG_MODAL',
  DISCLAIMER_MODAL: 'DISCLAIMER_MODAL',
  CONFIGURATION_BROWSER_MODAL: 'CONFIGURATION_BROWSER_MODAL',
};

export const CONTEXT_ID = 'CONFIGURATOR_FORM';

export const PDF_SECTION_KEYS = {
  SECTION_PROPERTIES: 'sectionProperties',
};

export const FIELD_IDS = {
  HEADER_SECTION_CONFIG: 'HEADER_SECTION_CONFIG',
  BODY_SECTION_CONFIG: 'BODY_SECTION_CONFIG',
  FOOTER_SECTION_CONFIG: 'FOOTER_SECTION_CONFIG',
  BACK_PRINT_SECTION_CONFIG: 'BACK_PRINT_SECTION_CONFIG',
  GENERAL_TERMS_SECTION_CONFIG: 'GENERAL_TERMS_SECTION_CONFIG',
  BASELINE_PAGE_SECTION_CONFIG: 'BASELINE_PAGE_SECTION_CONFIG',

  SECTION_CONFIG_TABLE: 'sectionProperties',

  HEADER: 'HEADER',
  BODY: 'BODY',
  FOOTER: 'FOOTER',
  BACK_PRINT: 'BACK_PRINT',
  BASELINE_PAGE: 'BASELINE_PAGE',
  GENERAL_TERMS: 'GENERAL_TERMS',

  INPUT_TYPE: 'inputType',
  KEY: 'key',
  VALUE: 'value',
  DISPLAY_IN: 'displayOption',
  ENABLED: 'enabled',
  RENDER: 'render',
};

export const COLUMN_ID = {
  FIELD_NAME: 'propertyDisplayName',
  TEXT_AREA: 'inputType',
  DISPLAY_IN: 'displayOption',
  RENDER: 'render',
  VALUE: 'value',
};

export { PAGE_TYPES };

export { SECTION_TYPES };

const NON_BACK_PRINT_SECTION_TYPES = _omit(SECTION_TYPES, [PDF_SECTION.BACK_PRINT]);

export const DOC_TYPE_VS_SECTION_TYPES = {
  [DOC_TYPES.ESTIMATE]: SECTION_TYPES,
  [DOC_TYPES.INVOICE]: SECTION_TYPES,
  [DOC_TYPES.MPVI]: NON_BACK_PRINT_SECTION_TYPES,
  [DOC_TYPES.DAMAGES]: NON_BACK_PRINT_SECTION_TYPES,
  [DOC_TYPES.TRANSACTION_HISTORY]: NON_BACK_PRINT_SECTION_TYPES,
  [DOC_TYPES.SERVICE_BOOKLET]: NON_BACK_PRINT_SECTION_TYPES,
  [DOC_TYPES.DTC_REPORT]: NON_BACK_PRINT_SECTION_TYPES,
  [DOC_TYPES.VEHICLE_DIAGNOSTIC_REPORT]: NON_BACK_PRINT_SECTION_TYPES,
  [DOC_TYPES.APPOINTMENT_REPORT]: NON_BACK_PRINT_SECTION_TYPES,
  [DOC_TYPES.VIS]: NON_BACK_PRINT_SECTION_TYPES,
  [DOC_TYPES.INSPECTIONS_AND_RECOMMENDATIONS]: NON_BACK_PRINT_SECTION_TYPES,
  [DOC_TYPES.RECOMMENDATIONS_AND_MPVI]: NON_BACK_PRINT_SECTION_TYPES,
  [DOC_TYPES.QUOTE]: NON_BACK_PRINT_SECTION_TYPES,
  [DOC_TYPES.APPOINTMENT_DETAILED_REPORT]: NON_BACK_PRINT_SECTION_TYPES,
  [DOC_TYPES.RO_DELIVERY_APPOINTMENT_DETAILED_REPORT]: NON_BACK_PRINT_SECTION_TYPES,
  [DOC_TYPES.RETURN_PARTS]: NON_BACK_PRINT_SECTION_TYPES,
};

export const SECTION_LEVEL_CONFIG_SELECTOR = {
  [SECTION_TYPES.BODY]: 'TOGGLE_BODY_SECTION',
};

const BODY_COMPONENTS_TOGGLE_SELECTOR = {
  [BODY_COMPONENTS.JOBS]: 'TOGGLE_JOBS',
  [BODY_COMPONENTS.JOBS_WITH_CONFIGURABLE_THIRD_PARTY_SPLIT]: 'TOGGLE_JOBS_WITH_CONFIGURABLE_THIRD_PARTY_SPLIT',
  [BODY_COMPONENTS.ADDITIONAL_FEE]: 'TOGGLE_ADDITIONAL_FEE',
  [BODY_COMPONENTS.ADDITIONAL_COUPONS]: 'TOGGLE_ADDITIONAL_COUPONS',
  [BODY_COMPONENTS.DEFERRED_RECOMMENDATIONS]: 'TOGGLE_DEFERRED_RECOMMENDATIONS',
  [BODY_COMPONENTS.SIGNATURE_SECTION]: 'TOGGLE_SIGNATURE_SECTION',
  [BODY_COMPONENTS.CUSTOMER_NOTES]: 'TOGGLE_CUSTOMER_NOTES',
  [BODY_COMPONENTS.CUSTOMER_SIGNATURE]: 'TOGGLE_CUSTOMER_SIGNATURE',
  [BODY_COMPONENTS.SUMMARY_TABLE]: 'TOGGLE_SUMMARY_TABLE',
  [BODY_COMPONENTS.SUMMARY]: 'TOGGLE_SUMMARY',
  [BODY_COMPONENTS.SUMMARY_TABLE_WITH_AUTHORIZED_REPAIR]: 'TOGGLE_SUMMARY_TABLE_WITH_AUTHORIZED_REPAIR',
  [BODY_COMPONENTS.SUMMARY_WITH_CONFIGURABLE_THIRD_PARTY_SPLIT]: 'TOGGLE_SUMMARY_WITH_CONFIGURABLE_THIRD_PARTY_SPLIT',
  [BODY_COMPONENTS.SA_NOTES]: 'TOGGLE_SA_NOTES',
  [BODY_COMPONENTS.CLOCK_IN_TABLE]: 'TOGGLE_CLOCK_IN_TABLE',
  [BODY_COMPONENTS.DEFERRED_RECALLS]: 'TOGGLE_DEFERRED_RECALLS',
  [BODY_COMPONENTS.NEXT_SERVICE_DUE]: 'TOGGLE_NEXT_SERVICE_DUE',
  [BODY_COMPONENTS.WARRANTY_STATEMENTS]: 'TOGGLE_WARRANTY_STATEMENTS',
  [BODY_COMPONENTS.LAST_PAGE_LEGAL_TERMS]: 'TOGGLE_LAST_PAGE_LEGAL_TERMS',
  [BODY_COMPONENTS.ACCOUNTING_POSTING_INFO]: 'TOGGLE_ACCOUNTING_POSTING_INFO',
  [BODY_COMPONENTS.GREETING_NOTE]: 'TOGGLE_GREETING_NOTE',
};

export const COMPONENTS_VS_TOGGLE_SELECTOR = {
  [SECTION_TYPES.BODY]: BODY_COMPONENTS_TOGGLE_SELECTOR,
};

const BODY_COMPONENTS_VS_CONFIGURATION_CELL_SELECTOR = {
  [BODY_COMPONENTS.JOBS]: 'CONFIGURATION_CELL_JOBS',
  [BODY_COMPONENTS.JOBS_WITH_CONFIGURABLE_THIRD_PARTY_SPLIT]:
    'CONFIGURATION_CELL_JOBS_WITH_CONFIGURABLE_THIRD_PARTY_SPLIT',
  [BODY_COMPONENTS.SUMMARY]: 'CONFIGURATION_CELL_SUMMARY',
  [BODY_COMPONENTS.SUMMARY_WITH_CONFIGURABLE_THIRD_PARTY_SPLIT]:
    'CONFIGURATION_CELL_SUMMARY_WITH_CONFIGURABLE_THIRD_PARTY_SPLIT',
  [BODY_COMPONENTS.DEFERRED_RECOMMENDATIONS]: 'CONFIGURATION_CELL_DEFERRED_RECOMMENDATIONS',
};

export const COMPONENTS_VS_CONFIGURATION_CELL_SELECTOR = {
  [SECTION_TYPES.BODY]: BODY_COMPONENTS_VS_CONFIGURATION_CELL_SELECTOR,
};

const BODY_COMPONENTS_VS_EDIT_CELL_SELECTOR = {
  [BODY_COMPONENTS.GREETING_NOTE]: 'EDIT_CELL_GREETING_NOTE',
  [BODY_COMPONENTS.LAST_PAGE_LEGAL_TERMS]: 'EDIT_CELL_LAST_PAGE_LEGAL_TERMS',
  [BODY_COMPONENTS.WARRANTY_STATEMENTS]: 'EDIT_CELL_WARRANTY_STATEMENTS',
};

export const COMPONENTS_VS_EDIT_CELL_SELECTOR = {
  [SECTION_TYPES.BODY]: BODY_COMPONENTS_VS_EDIT_CELL_SELECTOR,
};

const BODY_COMPONENTS_VS_BROWSER_CELL_SELECTOR = {
  [BODY_COMPONENTS.GREETING_NOTE]: 'BROWSER_CELL_GREETING_NOTE',
  [BODY_COMPONENTS.LAST_PAGE_LEGAL_TERMS]: 'BROWSER_CELL_LAST_PAGE_LEGAL_TERMS',
  [BODY_COMPONENTS.WARRANTY_STATEMENTS]: 'BROWSER_CELL_WARRANTY_STATEMENTS',
};

export const COMPONENTS_VS_BROWSER_CELL_SELECTOR = {
  [SECTION_TYPES.BODY]: BODY_COMPONENTS_VS_BROWSER_CELL_SELECTOR,
};

export const PAGE_TYPE_LABELS = {
  [PAGE_TYPES.ALL_PAGES]: __('All Pages'),
  [PAGE_TYPES.FIRST_PAGE]: __('First Page'),
  [PAGE_TYPES.LAST_PAGE]: __('Last Page'),
  [PAGE_TYPES.LAST_PAGE_AND_DUPLEX_PRINT]: __('Last Page And Duplex Print'),
};

export const ALL_PAGE_OPTION = { label: PAGE_TYPE_LABELS[PAGE_TYPES.ALL_PAGES], value: PAGE_TYPES.ALL_PAGES };
export const LAST_PAGE_OPTION = { label: PAGE_TYPE_LABELS[PAGE_TYPES.LAST_PAGE], value: PAGE_TYPES.LAST_PAGE };

export const PAGE_TYPE_OPTIONS = [
  ALL_PAGE_OPTION,
  { label: PAGE_TYPE_LABELS[PAGE_TYPES.FIRST_PAGE], value: PAGE_TYPES.FIRST_PAGE },
  LAST_PAGE_OPTION,
  { label: PAGE_TYPE_LABELS[PAGE_TYPES.LAST_PAGE_AND_DUPLEX_PRINT], value: PAGE_TYPES.LAST_PAGE_AND_DUPLEX_PRINT },
];

export const SECTION_TYPE_VS_PAGE_TYPE = {
  [SECTION_TYPES.HEADER]: [PAGE_TYPES.ALL_PAGES, PAGE_TYPES.FIRST_PAGE],
  [SECTION_TYPES.FOOTER]: [PAGE_TYPES.ALL_PAGES, PAGE_TYPES.LAST_PAGE],
  [SECTION_TYPES.BACK_PRINT]: [PAGE_TYPES.ALL_PAGES, PAGE_TYPES.LAST_PAGE, PAGE_TYPES.LAST_PAGE_AND_DUPLEX_PRINT],
  [SECTION_TYPES.BASELINE_PAGE]: [PAGE_TYPES.ALL_PAGES, PAGE_TYPES.LAST_PAGE, PAGE_TYPES.FIRST_PAGE],
};

export const MULTI_LINGUAL_SUPPORTED_CONFIG_FIELD_TYPES = new Set([
  CONFIG_FIELD_TYPES.LOGO,
  CONFIG_FIELD_TYPES.TEXT_AREA,
  CONFIG_FIELD_TYPES.TERMS_CONTENT,
]);

export const DRAWER_SUPPORTED_SECTION_CONFIGURATION_KEYS = new Set([
  BODY_COMPONENTS.JOBS,
  BODY_COMPONENTS.JOBS_WITH_CONFIGURABLE_THIRD_PARTY_SPLIT,
  BODY_COMPONENTS.ACCEPTED_SERVICES,
  BODY_COMPONENTS.DISCLOSURE,
  ..._keys(DISCLOSURE_FIELDS),
  ...LOGO_SUPPORTED_COMPONENTS_SET,
]);

export const SELECT_FROM_OTHER_DOCUMENT_SUPPORTED_CONFIGURATION_KEYS = new Set([
  BODY_COMPONENTS.DISCLOSURE,
  ..._keys(DISCLOSURE_FIELDS),
]);

export { SECTION_TYPE_PAGE_TYPES_MAPPING };
const NON_HEADER_CONFIGURABLE_DOC_TYPES = _values(
  _omit(DOC_TYPES, [
    DOC_TYPES.MPVI,
    DOC_TYPES.INSPECTIONS_AND_RECOMMENDATIONS,
    DOC_TYPES.RECOMMENDATIONS_AND_MPVI,
    DOC_TYPES.TRANSACTION_HISTORY,
    DOC_TYPES.APPOINTMENT_REPORT,
    DOC_TYPES.VIS,
    DOC_TYPES.QUOTE,
    DOC_TYPES.APPOINTMENT_DETAILED_REPORT,
    DOC_TYPES.RO_DELIVERY_APPOINTMENT_DETAILED_REPORT,
  ])
);

const FOOTER_QR_CODE_DOC_TYPES = _values(
  _omit(DOC_TYPES, [
    DOC_TYPES.MPVI,
    DOC_TYPES.INSPECTIONS_AND_RECOMMENDATIONS,
    DOC_TYPES.RECOMMENDATIONS_AND_MPVI,
    DOC_TYPES.TRANSACTION_HISTORY,
    DOC_TYPES.DAMAGES,
    DOC_TYPES.DTC_REPORT,
    DOC_TYPES.VEHICLE_DIAGNOSTIC_REPORT,
    DOC_TYPES.APPOINTMENT_REPORT,
    DOC_TYPES.CONTACTLESS_APPOINTMENT_REPORT,
  ])
);

/**
 * Maintain PropertyValue Vs key - make sure each key is uniquely map to propertyKey
 */
export const SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME = {
  [HEADER_COMPONENTS.LOGO]: __('Logo'),
  [HEADER_COMPONENTS.DEALER_PHONE_NUMBER]: __('Dealer Phone Number'),
  [HEADER_COMPONENTS.DEALER_EMAIL_ADDRESS]: __('Dealer Email Address'),
  [HEADER_COMPONENTS.ADDRESS]: __('Address'),
  [HEADER_COMPONENTS.FDACS_ID]: __('FDACS ID'),
  [HEADER_COMPONENTS.QUOTE_ESTIMATE_LABEL]: __('Quote Estimate Label'),
  [HEADER_COMPONENTS.QUOTE_ESTIMATE]: __('Quote Estimate'),
  [HEADER_COMPONENTS.QUOTE_NUMBER]: __('Quote Number'),
  [HEADER_COMPONENTS.QUOTE_TIME]: __('Quote Time'),
  [HEADER_COMPONENTS.QUOTE_EXPIRATION_TIME]: __('Quote Expiration'),
  [HEADER_COMPONENTS.APPOINTMENT_ESTIMATE]: __('Appointment Estimate'),
  [HEADER_COMPONENTS.APPOINTMENT_NUMBER]: __('Appointment Number'),
  [HEADER_COMPONENTS.DELIVERY_APPOINTMENT_NUMBER]: __('Delivery Appointment Number'),
  [HEADER_COMPONENTS.DELIVERY_APPOINTMENT_DATE_TIME]: __('Delivery Appointment Date Time'),
  [HEADER_COMPONENTS.APPOINTMENT_DATE_TIME]: __('Appointment Date & Time'),
  [HEADER_COMPONENTS.APPOINTMENT_CREATED_TIME]: __('Created Date & Time'),
  [HEADER_COMPONENTS.APPOINTMENT_PROMISE_TIME]: __('$$(Promise Time)'),
  [HEADER_COMPONENTS.RO_NUMBER]: __('#$$(RO)'),
  [HEADER_COMPONENTS.TAG_NUMBER]: __('#TAG'),
  [HEADER_COMPONENTS.LOCATION_TYPE]: __('Location Type'),
  [HEADER_COMPONENTS.LOCATION]: __('Location'),
  [HEADER_COMPONENTS.CHECK_IN]: __('Check IN'),
  [HEADER_COMPONENTS.READY_FOR_PICKUP]: __('Ready For Pickup'),
  [HEADER_COMPONENTS.PROMISE_TIME]: __('$$(Promise Time)'),
  [HEADER_COMPONENTS.INSPECTION_DATE]: __('$$(Inspection) Date'),
  [HEADER_COMPONENTS.TECHNICIAN]: __('Technician'),
  [HEADER_COMPONENTS.CUSTOMER_DETAILS]: __('Customer'),
  [HEADER_COMPONENTS.CUSTOMER_NAME]: __('Customer Name'),
  [HEADER_COMPONENTS.CUSTOMER_NUMBER]: __('Customer Number'),
  [HEADER_COMPONENTS.CUSTOMER_PHONE_NUMBER]: __('Customer Phone Number'),
  [HEADER_COMPONENTS.CUSTOMER_EMAIL]: __('Customer Email'),
  [HEADER_COMPONENTS.CUSTOMER_ADDRESS_LINE_ONE]: __('Customer Address Line 1'),
  [HEADER_COMPONENTS.CUSTOMER_ADDRESS_LINE_TWO]: __('Customer Address Line 2'),
  [HEADER_COMPONENTS.CUSTOMER_REFERENCE_NUMBER]: __('Customer Reference Number'),
  [HEADER_COMPONENTS.PAYER_DETAILS]: __('Payer Details'),
  [HEADER_COMPONENTS.BILLING_CUSTOMER_DETAILS]: __('Billing'),
  [HEADER_COMPONENTS.PICKUP_CUSTOMER_DETAILS]: __('Pickup Customer'),
  [HEADER_COMPONENTS.VEHICLE_DETAILS]: __('Vehicle'),
  [HEADER_COMPONENTS.VEHICLE_NAME]: __('Vehicle Name'),
  [HEADER_COMPONENTS.VEHICLE_TRIM]: __('Vehicle Trim'),
  [HEADER_COMPONENTS.MPVI_VEHICLE_TRIM]: __('Vehicle Trim'),
  [HEADER_COMPONENTS.VIN]: __('VIN'),
  [HEADER_COMPONENTS.VEHICLE_PLATE]: __('Vehicle Plate'),
  [HEADER_COMPONENTS.FLEET_NUMBER]: __('Fleet Number'),
  [HEADER_COMPONENTS.MILEAGE]: __('Vehicle $$(Mileage)'),
  [HEADER_COMPONENTS.MILEAGE_OUT]: __('Vehicle $$(Mileage) Out'),
  [HEADER_COMPONENTS.ENGINE_HRS]: __('Vehicle Engine Hrs'),
  [HEADER_COMPONENTS.STOCK_ID]: __('Vehicle Stock ID'),
  [HEADER_COMPONENTS.VEHICLE_SERVICE_DATE]: __('Vehicle Service Date'),
  [HEADER_COMPONENTS.VEHICLE_SOLD_DATE]: __('Vehicle Sold Date'),
  [HEADER_COMPONENTS.SA_DETAILS]: __('$$(Service Advisor)'),
  [HEADER_COMPONENTS.SA_NAME]: __('SA Name'),
  [HEADER_COMPONENTS.SA_EMPLOYEE_NUMBER]: __('SA Employee Number'),
  [HEADER_COMPONENTS.SA_PHONE_NUMBER]: __('SA Phone Number'),
  [HEADER_COMPONENTS.TRANSPORTATION_TYPE]: __('Transportation Type'),
  [HEADER_COMPONENTS.INSURANCE_COMPANY]: __('Insurance Company Details'),
  [HEADER_COMPONENTS.INSURANCE_NUMBER]: __('Insurance Company Name'),
  [HEADER_COMPONENTS.INSURANCE_COMPANY_NUMBER]: __('Insurance Company Number'),
  [HEADER_COMPONENTS.INSURANCE_COMPANY_MOBILE_NUMBER]: __('Insurance Company Mobile Number'),
  [HEADER_COMPONENTS.INSURANCE_COMPANY_EMAIL]: __('Insurance Company email'),
  [HEADER_COMPONENTS.RETURN_RO_DETAILS]: __('$$(Return RO) Details'),
  [HEADER_COMPONENTS.INVOICE_NUMBER]: __('$$(Invoice) Number'),
  [HEADER_COMPONENTS.SHOW_INVOICE_TOTAL]: __('Show Invoice Total'),
  [HEADER_COMPONENTS.RO_DETAILS]: __('RO Details'),
  [HEADER_COMPONENTS.COMPANY_INFORMATION_DETAILS]: __('Dealer Details'),
  APPOINTMENT_TYPE: __('$$(Appointment) Option'),
  [FOOTER_COMPONENTS.LEGAL_TERMS]: __('Terms'),
  [FOOTER_COMPONENTS.MESSAGE_TO_CUSTOMER]: __('Message'),
  [FOOTER_COMPONENTS.SIGNATURE_SECTION]: __('Signature & Place / Date'),
  [FOOTER_COMPONENTS.DEALER_REGISTRATION_NUMBER]: __('BAR / EPA'),
  [FOOTER_COMPONENTS.QR_CODE]: __('Display QR Code'),
  [FOOTER_COMPONENTS.FOOTER_LOGO]: __('Footer Logo'),
  [FOOTER_COMPONENTS.NOTE]: __('Note'),
  [BACK_PRINT_COMPONENTS.LEGAL_TERMS]: __('Terms'),
  [GENERAL_TERMS_COMPONENTS.TERMS_AND_CONDITIONS]: __('Terms And Conditions'),
  [BODY_COMPONENTS.TOTAL_SUMMARY]: __('RO Summary'),
  [BODY_COMPONENTS.BODY_LEGAL_TERMS]: __('Body Legal Terms'),
  [BODY_COMPONENTS.JOBS]: __('Jobs'),
  [BODY_COMPONENTS.JOB_TABLE]: __('Jobs'),
  [BODY_COMPONENTS.ADDITIONAL_JOB_SPACE]: __('Additional Jobs Space'),
  [BODY_COMPONENTS.CUSTOMER_NOTES]: __('$$(RO) External Notes'),
  [BODY_COMPONENTS.ADDITIONAL_FEE]: __('Additional Fees'),
  [BODY_COMPONENTS.ADDITIONAL_COUPONS]: __('Additional Coupons'),
  [BODY_COMPONENTS.SERVICE_HISTORY]: __('Dealer Service History'),
  [BODY_COMPONENTS.DEFERRED_RECOMMENDATIONS]: __('$$(Deferred) Recommendations'),
  [BODY_COMPONENTS.SUMMARY_TABLE]: __('Summary'),
  [BODY_COMPONENTS.SUMMARY_TABLE_WITH_AUTHORIZED_REPAIR]: __('Summary with Authorized Repair'),
  [BODY_COMPONENTS.DEFERRED_RECALLS]: __('$$(Deferred) Recalls'),
  [BODY_COMPONENTS.SUMMARY]: __('Estimate Breakdown'),
  [BODY_COMPONENTS.SA_NOTES]: __('Recommendation Approval Details'),
  [BODY_COMPONENTS.LAST_PAGE_LEGAL_TERMS]: __('Last Page Legal Terms'),
  [BODY_COMPONENTS.GREETING_NOTE]: __('Greeting Note'),
  [BODY_COMPONENTS.JOBS_WITH_CONFIGURABLE_THIRD_PARTY_SPLIT]: __('Jobs with Split'),
  [BODY_COMPONENTS.SUMMARY_WITH_CONFIGURABLE_THIRD_PARTY_SPLIT]: __('Invoice Breakdown'),
  [BODY_COMPONENTS.NEXT_SERVICE_DUE]: __('Next Service Due'),
  [BODY_COMPONENTS.CLOCK_IN_TABLE]: __('Clock In Details'),
  [BODY_COMPONENTS.ACCOUNTING_POSTING_INFO]: __('Accounting Posting Info'),
  [BODY_COMPONENTS.WARRANTY_STATEMENTS]: __('Warranty Statements'),
  [BODY_COMPONENTS.SIGNATURE_SECTION]: __('Signature Section'),
  [BODY_COMPONENTS.CUSTOMER_SIGNATURE]: __('Customer Signature'),
  [BODY_COMPONENTS.INSPECTION_DETAILS_WITH_CONFIGURABLE_SHOW_INSPECTION_RANGE_INFO]: __('$$(Inspection) Details'),
  [BODY_COMPONENTS.INSPECTION_DETAILS]: __('$$(Inspection) Details'),
  [BODY_COMPONENTS.PENDING_RECOMMENDATIONS]: __('Pending Recommendations'),
  [BODY_COMPONENTS.PENDING_RECOMMENDATIONS_SUMMARY]: __('Pending Recommendations Summary'),
  [BODY_COMPONENTS.CA_RECOMMENDATIONS]: __('CA Recommendations'),
  [BODY_COMPONENTS.CA_RECOMMENDATIONS_SUMMARY]: __('CA Recommendations Summary'),
  [BODY_COMPONENTS.JOBS_RECOMMENDATIONS_SUMMARY]: __('Jobs Recommendations Summary'),
  [BODY_COMPONENTS.DUMMY_SIGNATURE_SECTION]: __('Signature Placeholder'),
  [BODY_COMPONENTS.INSPECTION_MEDIA]: __('$$(Inspection) Media'),
  [BODY_COMPONENTS.VEHICLE_INFORMATION]: __('OEM Vehicle Information'),
  [BODY_COMPONENTS.ADDITIONAL_INFORMATION]: __('Additional Information'),
  [BODY_COMPONENTS.SUBSCRIPTION_STATUSES]: __('Subscription Statuses'),
  [BODY_COMPONENTS.WARRANTY_BLOCK]: __('Warranty Section'),
  [BODY_COMPONENTS.BRANDED_TITLE]: __('Branded Title'),
  [BODY_COMPONENTS.MOST_RECENT_SERVICES]: __('Most Recent Services'),
  [BODY_COMPONENTS.LAST_MAINTENANCE]: __('Last Maintenance'),
  [BODY_COMPONENTS.REQUIRED_FIELD_ACTIONS]: __('Required Field Actions'),
  [BODY_COMPONENTS.WARRANTY_COVERAGE]: __('Warranty Coverages'),
  [BODY_COMPONENTS.SERVICE_CONTRACTS]: __('Extended Warranty Information'),
  [BODY_COMPONENTS.WARRANTY_HISTORY]: __('Warranty History'),
  [BODY_COMPONENTS.APPLICABLE_WARRANTIES]: __('Applicable Warranty'),
  [BODY_COMPONENTS.DEALER_SERVICE_HISTORY]: __('OEM Service History'),
  [BODY_COMPONENTS.NEXT_APPOINTMENT]: __('Next Appointment'),
  [BODY_COMPONENTS.NEXT_REQUIRED_MAINTENANCE]: __('Next Required Maintenance'),
  [BODY_COMPONENTS.ACCEPTED_SERVICES]: __('Accepted Services'),
  [BODY_COMPONENTS.DISCLOSURE]: __('Custom Disclosure Statement'),
  [BODY_COMPONENTS.CUSTOM_DISCLOSURE_1]: __('Custom Disclosure Statement 1'),
  [BODY_COMPONENTS.CUSTOM_DISCLOSURE_2]: __('Custom Disclosure Statement 2'),
  [BODY_COMPONENTS.CUSTOM_DISCLOSURE_3]: __('Custom Disclosure Statement 3'),
  [BODY_COMPONENTS.CUSTOM_DISCLOSURE_4]: __('Custom Disclosure Statement 4'),
  [BODY_COMPONENTS.MPVI_INSPECTION_DETAILS]: __('$$(Inspection) Details'),
  [BODY_COMPONENTS.DECLINED_SERVICES]: __('Declined Services'),
  [BODY_COMPONENTS.DAMAGES]: __('Damages'),
  [BODY_COMPONENTS.SEVERITY_DETAILS]: __('Severity Details'),
  [BODY_COMPONENTS.MPI_READINGS]: __('$$(MPI) Readings'),
  [BODY_COMPONENTS.APPOINTMENT_INFORMATION]: __('Appointment Information'),
  [BODY_COMPONENTS.QUOTE_SUMMARY]: __('Quote Breakdown'),
  [BODY_COMPONENTS.MPVI_SUMMARY_TABLE]: __('Summary Table'),
  [BODY_COMPONENTS.RECALLS]: __('Recalls'),
  [BODY_COMPONENTS.SERVICE_INFORMATION]: __('Service Information'),
  [BODY_COMPONENTS.APPOINTMENT_SUMMARY]: __('Appointment Breakdown'),
  [BODY_COMPONENTS.TAX_BREAKDOWN_TABLE]: __('Tax Breakdown Table'),
  [BODY_COMPONENTS.PAYER_SPLIT_TABLE]: __('Payer Split Table'),
  [BODY_COMPONENTS.APPROVAL_LIST]: __('Approval List'),
  [BASELINE_PAGE_COMPONENTS.WATERMARK_TEXT]: __('Watermark Text'),
  TRANSPORT_TYPE_DETAILS: __('Transport Type Details'),
};

export const SECTION_PROPERTY_KEYS_VS_ALTERNATE_PROPERTY_DISPLAY_NAME = {
  [BACK_PRINT_COMPONENTS.LEGAL_TERMS]: __('Legal Terms'),
  [HEADER_COMPONENTS.VEHICLE_SERVICE_DATE]: __('$$(In Service Date)'),
};

const QUOTE_RIGHT_HEADER_SECTION = [
  {
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.QUOTE_ESTIMATE_LABEL],
    key: HEADER_COMPONENTS.QUOTE_ESTIMATE_LABEL,
    render: true,
    displayOption: PAGE_TYPES.ALL_PAGES,
    inputType: HEADER_COMPONENTS.QUOTE_ESTIMATE_LABEL,
    documentTypes: [DOC_TYPES.QUOTE],
  },
  {
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.QUOTE_ESTIMATE],
    key: HEADER_COMPONENTS.QUOTE_ESTIMATE,
    render: true,
    displayOption: PAGE_TYPES.ALL_PAGES,
    inputType: HEADER_COMPONENTS.QUOTE_ESTIMATE,
    documentTypes: [DOC_TYPES.QUOTE],
  },
  {
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.QUOTE_NUMBER],
    key: HEADER_COMPONENTS.QUOTE_NUMBER,
    render: true,
    displayOption: PAGE_TYPES.ALL_PAGES,
    inputType: HEADER_COMPONENTS.QUOTE_NUMBER,
    documentTypes: [DOC_TYPES.QUOTE],
  },
  {
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.QUOTE_TIME],
    key: HEADER_COMPONENTS.QUOTE_TIME,
    render: true,
    displayOption: PAGE_TYPES.ALL_PAGES,
    inputType: HEADER_COMPONENTS.QUOTE_TIME,
    documentTypes: [DOC_TYPES.QUOTE],
  },
  {
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.QUOTE_EXPIRATION_TIME],
    key: HEADER_COMPONENTS.QUOTE_EXPIRATION_TIME,
    render: false,
    displayOption: PAGE_TYPES.ALL_PAGES,
    inputType: HEADER_COMPONENTS.QUOTE_EXPIRATION_TIME,
    documentTypes: [DOC_TYPES.QUOTE],
  },
];

const APPOINTMENT_RIGHT_HEADER_SECTION = [
  {
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.APPOINTMENT_ESTIMATE],
    key: HEADER_COMPONENTS.APPOINTMENT_ESTIMATE,
    render: true,
    isDefault: true,
    displayOption: PAGE_TYPES.ALL_PAGES,
    inputType: HEADER_COMPONENTS.APPOINTMENT_ESTIMATE,
    documentTypes: [DOC_TYPES.APPOINTMENT_DETAILED_REPORT],
  },
  {
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.APPOINTMENT_NUMBER],
    key: HEADER_COMPONENTS.APPOINTMENT_NUMBER,
    render: true,
    isDefault: true,
    displayOption: PAGE_TYPES.ALL_PAGES,
    inputType: HEADER_COMPONENTS.APPOINTMENT_NUMBER,
    documentTypes: [DOC_TYPES.APPOINTMENT_DETAILED_REPORT],
  },
  {
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.DELIVERY_APPOINTMENT_NUMBER],
    key: HEADER_COMPONENTS.DELIVERY_APPOINTMENT_NUMBER,
    render: true,
    isDefault: true,
    displayOption: PAGE_TYPES.ALL_PAGES,
    inputType: HEADER_COMPONENTS.DELIVERY_APPOINTMENT_NUMBER,
    documentTypes: [DOC_TYPES.RO_DELIVERY_APPOINTMENT_DETAILED_REPORT],
  },
  {
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.APPOINTMENT_CREATED_TIME],
    key: HEADER_COMPONENTS.APPOINTMENT_CREATED_TIME,
    render: true,
    isDefault: true,
    displayOption: PAGE_TYPES.ALL_PAGES,
    inputType: HEADER_COMPONENTS.APPOINTMENT_CREATED_TIME,
    documentTypes: [DOC_TYPES.APPOINTMENT_DETAILED_REPORT],
  },
  {
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.APPOINTMENT_DATE_TIME],
    key: HEADER_COMPONENTS.APPOINTMENT_DATE_TIME,
    render: true,
    isDefault: true,
    displayOption: PAGE_TYPES.ALL_PAGES,
    inputType: HEADER_COMPONENTS.APPOINTMENT_DATE_TIME,
    documentTypes: [DOC_TYPES.APPOINTMENT_DETAILED_REPORT],
  },
  {
    propertyDisplayName:
      SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.DELIVERY_APPOINTMENT_DATE_TIME],
    key: HEADER_COMPONENTS.DELIVERY_APPOINTMENT_DATE_TIME,
    render: true,
    isDefault: true,
    displayOption: PAGE_TYPES.ALL_PAGES,
    inputType: HEADER_COMPONENTS.DELIVERY_APPOINTMENT_DATE_TIME,
    documentTypes: [DOC_TYPES.RO_DELIVERY_APPOINTMENT_DETAILED_REPORT],
  },
  {
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.APPOINTMENT_PROMISE_TIME],
    key: HEADER_COMPONENTS.APPOINTMENT_PROMISE_TIME,
    render: true,
    isDefault: true,
    displayOption: PAGE_TYPES.ALL_PAGES,
    inputType: HEADER_COMPONENTS.APPOINTMENT_PROMISE_TIME,
    documentTypes: [DOC_TYPES.APPOINTMENT_DETAILED_REPORT],
  },
];

export const INSPECTION_AND_RECOMMENDATIONS_SPECIFIC_HEADER_FIELDS = [
  {
    key: HEADER_COMPONENTS.INSPECTION_DATE,
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.INSPECTION_DATE],
    render: true,
    displayOption: PAGE_TYPES.ALL_PAGES,
    documentTypes: [DOC_TYPES.INSPECTIONS_AND_RECOMMENDATIONS, DOC_TYPES.MPVI],
  },
  {
    key: HEADER_COMPONENTS.TECHNICIAN,
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.TECHNICIAN],
    render: true,
    displayOption: PAGE_TYPES.ALL_PAGES,
    documentTypes: [DOC_TYPES.INSPECTIONS_AND_RECOMMENDATIONS, DOC_TYPES.MPVI],
  },
];

/*
 * Header, Footer and Back-Print sections and their respective sectionProperties
 */
export const COMMON_PDF_SECTIONS = [
  {
    sectionName: SECTION_TYPES.HEADER,
    render: true,
    sectionType: 'DEFAULT',
    sectionProperties: [
      // header left section
      {
        key: HEADER_COMPONENTS.LOGO,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.LOGO],
        render: true,
        displayOption: PAGE_TYPES.ALL_PAGES,
        inputType: 'LOGO',
        documentTypes: [
          ...NON_HEADER_CONFIGURABLE_DOC_TYPES,
          DOC_TYPES.INSPECTIONS_AND_RECOMMENDATIONS,
          DOC_TYPES.MPVI,
          DOC_TYPES.QUOTE,
          DOC_TYPES.APPOINTMENT_DETAILED_REPORT,
          DOC_TYPES.RO_DELIVERY_APPOINTMENT_DETAILED_REPORT,
        ],
      },
      {
        key: HEADER_COMPONENTS.ADDRESS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.ADDRESS],
        render: true,
        isDefault: true,
        inputType: CONFIG_FIELD_TYPES.TEXT_AREA,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [
          ...NON_HEADER_CONFIGURABLE_DOC_TYPES,
          DOC_TYPES.INSPECTIONS_AND_RECOMMENDATIONS,
          DOC_TYPES.MPVI,
          DOC_TYPES.QUOTE,
          DOC_TYPES.APPOINTMENT_DETAILED_REPORT,
          DOC_TYPES.RO_DELIVERY_APPOINTMENT_DETAILED_REPORT,
        ],
      },
      {
        key: HEADER_COMPONENTS.DEALER_PHONE_NUMBER,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.DEALER_PHONE_NUMBER],
        render: false,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [
          ...NON_HEADER_CONFIGURABLE_DOC_TYPES,
          DOC_TYPES.QUOTE,
          DOC_TYPES.APPOINTMENT_DETAILED_REPORT,
          DOC_TYPES.RO_DELIVERY_APPOINTMENT_DETAILED_REPORT,
        ],
      },
      {
        key: HEADER_COMPONENTS.DEALER_EMAIL_ADDRESS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.DEALER_EMAIL_ADDRESS],
        render: false,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [
          ...NON_HEADER_CONFIGURABLE_DOC_TYPES,
          DOC_TYPES.QUOTE,
          DOC_TYPES.APPOINTMENT_DETAILED_REPORT,
          DOC_TYPES.RO_DELIVERY_APPOINTMENT_DETAILED_REPORT,
        ],
      },
      {
        key: HEADER_COMPONENTS.FDACS_ID,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.FDACS_ID],
        render: true,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [
          ...NON_HEADER_CONFIGURABLE_DOC_TYPES,
          DOC_TYPES.QUOTE,
          DOC_TYPES.APPOINTMENT_DETAILED_REPORT,
          DOC_TYPES.RO_DELIVERY_APPOINTMENT_DETAILED_REPORT,
        ],
      },
      ...QUOTE_RIGHT_HEADER_SECTION,
      ...APPOINTMENT_RIGHT_HEADER_SECTION,
      // header right section
      {
        key: HEADER_COMPONENTS.RO_NUMBER,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.RO_NUMBER],
        render: true,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [
          ...NON_HEADER_CONFIGURABLE_DOC_TYPES,
          DOC_TYPES.MPVI,
          DOC_TYPES.INSPECTIONS_AND_RECOMMENDATIONS,
        ],
      },
      {
        key: HEADER_COMPONENTS.TAG_NUMBER,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.TAG_NUMBER],
        render: true,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [
          ...NON_HEADER_CONFIGURABLE_DOC_TYPES,
          DOC_TYPES.MPVI,
          DOC_TYPES.INSPECTIONS_AND_RECOMMENDATIONS,
        ],
      },
      {
        key: HEADER_COMPONENTS.SHOW_INVOICE_TOTAL,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.SHOW_INVOICE_TOTAL],
        render: true,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [DOC_TYPES.INVOICE, DOC_TYPES.RO_INVOICE, DOC_TYPES.RO_PAYER_INVOICE],
      },
      {
        key: HEADER_COMPONENTS.INVOICE_NUMBER,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.INVOICE_NUMBER],
        render: false,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [DOC_TYPES.RO_PAYER_INVOICE],
      },
      {
        key: HEADER_COMPONENTS.LOCATION_TYPE,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.LOCATION_TYPE],
        render: false,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [...NON_HEADER_CONFIGURABLE_DOC_TYPES, DOC_TYPES.APPOINTMENT_DETAILED_REPORT],
      },
      {
        key: HEADER_COMPONENTS.LOCATION,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.LOCATION],
        render: false,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [...NON_HEADER_CONFIGURABLE_DOC_TYPES, DOC_TYPES.APPOINTMENT_DETAILED_REPORT],
      },
      {
        key: HEADER_COMPONENTS.CHECK_IN,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.CHECK_IN],
        render: true,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: NON_HEADER_CONFIGURABLE_DOC_TYPES,
      },
      {
        key: HEADER_COMPONENTS.READY_FOR_PICKUP,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.READY_FOR_PICKUP],
        render: true,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [DOC_TYPES.INVOICE, DOC_TYPES.RO_INVOICE, DOC_TYPES.RO_PAYER_INVOICE],
      },
      {
        key: HEADER_COMPONENTS.PROMISE_TIME,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.PROMISE_TIME],
        render: true,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [...NON_HEADER_CONFIGURABLE_DOC_TYPES, DOC_TYPES.QUOTE],
      },
      // customer details
      {
        key: HEADER_COMPONENTS.CUSTOMER_DETAILS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.CUSTOMER_DETAILS],
        render: true,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [
          ...NON_HEADER_CONFIGURABLE_DOC_TYPES,
          DOC_TYPES.INSPECTIONS_AND_RECOMMENDATIONS,
          DOC_TYPES.MPVI,
          DOC_TYPES.QUOTE,
          DOC_TYPES.APPOINTMENT_DETAILED_REPORT,
          DOC_TYPES.RO_DELIVERY_APPOINTMENT_DETAILED_REPORT,
        ],
      },
      {
        key: HEADER_COMPONENTS.CUSTOMER_NAME,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.CUSTOMER_NAME],
        render: true,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [
          ...NON_HEADER_CONFIGURABLE_DOC_TYPES,
          DOC_TYPES.INSPECTIONS_AND_RECOMMENDATIONS,
          DOC_TYPES.MPVI,
          DOC_TYPES.QUOTE,
          DOC_TYPES.APPOINTMENT_DETAILED_REPORT,
          DOC_TYPES.RO_DELIVERY_APPOINTMENT_DETAILED_REPORT,
        ],
      },
      // enable later to avoid ambiguity from content settings screen
      // {
      //   key: HEADER_COMPONENTS.CUSTOMER_NUMBER,
      //   propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.CUSTOMER_NUMBER],
      //   render: true,
      //
      //   displayOption: PAGE_TYPES.ALL_PAGES,
      //   documentTypes: NON_HEADER_CONFIGURABLE_DOC_TYPES,
      // },
      {
        key: HEADER_COMPONENTS.CUSTOMER_PHONE_NUMBER,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.CUSTOMER_PHONE_NUMBER],
        render: true,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [
          ...NON_HEADER_CONFIGURABLE_DOC_TYPES,
          DOC_TYPES.INSPECTIONS_AND_RECOMMENDATIONS,
          DOC_TYPES.MPVI,
          DOC_TYPES.QUOTE,
          DOC_TYPES.APPOINTMENT_DETAILED_REPORT,
          DOC_TYPES.RO_DELIVERY_APPOINTMENT_DETAILED_REPORT,
        ],
      },
      {
        key: HEADER_COMPONENTS.CUSTOMER_EMAIL,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.CUSTOMER_EMAIL],
        render: true,
        displayOption: PAGE_TYPES.FIRST_PAGE,
        documentTypes: [
          ...NON_HEADER_CONFIGURABLE_DOC_TYPES,
          DOC_TYPES.INSPECTIONS_AND_RECOMMENDATIONS,
          DOC_TYPES.MPVI,
          DOC_TYPES.QUOTE,
          DOC_TYPES.APPOINTMENT_DETAILED_REPORT,
          DOC_TYPES.RO_DELIVERY_APPOINTMENT_DETAILED_REPORT,
        ],
      },
      {
        key: HEADER_COMPONENTS.CUSTOMER_ADDRESS_LINE_ONE,
        propertyDisplayName:
          SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.CUSTOMER_ADDRESS_LINE_ONE],
        render: true,
        displayOption: PAGE_TYPES.FIRST_PAGE,
        documentTypes: [
          ...NON_HEADER_CONFIGURABLE_DOC_TYPES,
          DOC_TYPES.APPOINTMENT_DETAILED_REPORT,
          DOC_TYPES.RO_DELIVERY_APPOINTMENT_DETAILED_REPORT,
        ],
      },
      {
        key: HEADER_COMPONENTS.CUSTOMER_ADDRESS_LINE_TWO,
        propertyDisplayName:
          SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.CUSTOMER_ADDRESS_LINE_TWO],
        render: true,
        displayOption: PAGE_TYPES.FIRST_PAGE,
        documentTypes: [
          ...NON_HEADER_CONFIGURABLE_DOC_TYPES,
          DOC_TYPES.APPOINTMENT_DETAILED_REPORT,
          DOC_TYPES.RO_DELIVERY_APPOINTMENT_DETAILED_REPORT,
        ],
      },
      {
        key: HEADER_COMPONENTS.CUSTOMER_REFERENCE_NUMBER,
        propertyDisplayName:
          SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.CUSTOMER_REFERENCE_NUMBER],
        render: true,
        displayOption: PAGE_TYPES.FIRST_PAGE,
        documentTypes: [DOC_TYPES.RO_PAYER_INVOICE],
      },
      {
        key: HEADER_COMPONENTS.PAYER_DETAILS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.PAYER_DETAILS],
        render: true,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [DOC_TYPES.RO_PAYER_INVOICE],
      },
      // Billing and pick-up customer header sections
      {
        key: HEADER_COMPONENTS.BILLING_CUSTOMER_DETAILS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.BILLING_CUSTOMER_DETAILS],
        render: true,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: NON_HEADER_CONFIGURABLE_DOC_TYPES,
      },
      {
        key: HEADER_COMPONENTS.PICKUP_CUSTOMER_DETAILS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.PICKUP_CUSTOMER_DETAILS],
        render: true,
        displayOption: PAGE_TYPES.FIRST_PAGE,
        documentTypes: NON_HEADER_CONFIGURABLE_DOC_TYPES,
      },
      // vehicle details
      {
        key: HEADER_COMPONENTS.VEHICLE_DETAILS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.VEHICLE_DETAILS],
        render: true,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [
          ...NON_HEADER_CONFIGURABLE_DOC_TYPES,
          DOC_TYPES.INSPECTIONS_AND_RECOMMENDATIONS,
          DOC_TYPES.MPVI,
          DOC_TYPES.QUOTE,
          DOC_TYPES.APPOINTMENT_DETAILED_REPORT,
          DOC_TYPES.RO_DELIVERY_APPOINTMENT_DETAILED_REPORT,
        ],
      },
      {
        key: HEADER_COMPONENTS.VEHICLE_NAME,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.VEHICLE_NAME],
        render: true,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [
          ...NON_HEADER_CONFIGURABLE_DOC_TYPES,
          DOC_TYPES.INSPECTIONS_AND_RECOMMENDATIONS,
          DOC_TYPES.MPVI,
          DOC_TYPES.QUOTE,
          DOC_TYPES.APPOINTMENT_DETAILED_REPORT,
          DOC_TYPES.RO_DELIVERY_APPOINTMENT_DETAILED_REPORT,
        ],
      },
      {
        key: HEADER_COMPONENTS.VEHICLE_TRIM,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.VEHICLE_TRIM],
        render: false,
        displayOption: PAGE_TYPES.FIRST_PAGE,
        documentTypes: [
          ...NON_HEADER_CONFIGURABLE_DOC_TYPES,
          DOC_TYPES.QUOTE,
          DOC_TYPES.APPOINTMENT_DETAILED_REPORT,
          DOC_TYPES.RO_DELIVERY_APPOINTMENT_DETAILED_REPORT,
        ],
      },
      {
        key: HEADER_COMPONENTS.MPVI_VEHICLE_TRIM,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.MPVI_VEHICLE_TRIM],
        render: true,
        displayOption: PAGE_TYPES.FIRST_PAGE,
        documentTypes: [DOC_TYPES.INSPECTIONS_AND_RECOMMENDATIONS, DOC_TYPES.MPVI],
      },
      {
        key: HEADER_COMPONENTS.VIN,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.VIN],
        render: true,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [
          ...NON_HEADER_CONFIGURABLE_DOC_TYPES,
          DOC_TYPES.INSPECTIONS_AND_RECOMMENDATIONS,
          DOC_TYPES.MPVI,
          DOC_TYPES.QUOTE,
          DOC_TYPES.APPOINTMENT_DETAILED_REPORT,
          DOC_TYPES.RO_DELIVERY_APPOINTMENT_DETAILED_REPORT,
        ],
      },
      {
        key: HEADER_COMPONENTS.VEHICLE_PLATE,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.VEHICLE_PLATE],
        render: true,
        displayOption: PAGE_TYPES.FIRST_PAGE,
        documentTypes: [
          ...NON_HEADER_CONFIGURABLE_DOC_TYPES,
          DOC_TYPES.QUOTE,
          DOC_TYPES.INSPECTIONS_AND_RECOMMENDATIONS,
          DOC_TYPES.MPVI,
        ],
      },
      {
        key: HEADER_COMPONENTS.FLEET_NUMBER,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.FLEET_NUMBER],
        render: false,
        displayOption: PAGE_TYPES.FIRST_PAGE,
        documentTypes: [
          ...NON_HEADER_CONFIGURABLE_DOC_TYPES,
          DOC_TYPES.QUOTE,
          DOC_TYPES.APPOINTMENT_DETAILED_REPORT,
          DOC_TYPES.RO_DELIVERY_APPOINTMENT_DETAILED_REPORT,
        ],
      },
      {
        key: HEADER_COMPONENTS.MILEAGE,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.MILEAGE],
        render: true,
        displayOption: PAGE_TYPES.FIRST_PAGE,
        documentTypes: [
          ...NON_HEADER_CONFIGURABLE_DOC_TYPES,
          DOC_TYPES.INSPECTIONS_AND_RECOMMENDATIONS,
          DOC_TYPES.MPVI,
          DOC_TYPES.QUOTE,
          DOC_TYPES.APPOINTMENT_DETAILED_REPORT,
          DOC_TYPES.RO_DELIVERY_APPOINTMENT_DETAILED_REPORT,
        ],
      },
      {
        key: HEADER_COMPONENTS.MILEAGE_OUT,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.MILEAGE_OUT],
        render: false,
        displayOption: PAGE_TYPES.FIRST_PAGE,
        documentTypes: [DOC_TYPES.ESTIMATE],
        copyTypes: [COPY_TYPES.TECHNICIAN],
      },
      {
        key: HEADER_COMPONENTS.ENGINE_HRS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.ENGINE_HRS],
        render: false,
        displayOption: PAGE_TYPES.FIRST_PAGE,
        documentTypes: [...NON_HEADER_CONFIGURABLE_DOC_TYPES, DOC_TYPES.QUOTE, DOC_TYPES.APPOINTMENT_DETAILED_REPORT],
        dealerProperties: [DEALER_PROPERTY.MULTIPLE_VEHICLE_TYPE_ENABLED],
      },
      {
        key: HEADER_COMPONENTS.STOCK_ID,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.STOCK_ID],
        render: true,
        displayOption: PAGE_TYPES.FIRST_PAGE,
        documentTypes: [...NON_HEADER_CONFIGURABLE_DOC_TYPES, DOC_TYPES.QUOTE, DOC_TYPES.APPOINTMENT_DETAILED_REPORT],
      },
      {
        key: HEADER_COMPONENTS.VEHICLE_SERVICE_DATE,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.VEHICLE_SERVICE_DATE],
        render: true,
        displayOption: PAGE_TYPES.FIRST_PAGE,
        documentTypes: NON_HEADER_CONFIGURABLE_DOC_TYPES,
      },
      {
        key: HEADER_COMPONENTS.VEHICLE_SOLD_DATE,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.VEHICLE_SOLD_DATE],
        render: true,
        displayOption: PAGE_TYPES.FIRST_PAGE,
        documentTypes: [
          ...NON_HEADER_CONFIGURABLE_DOC_TYPES,
          DOC_TYPES.MPVI,
          DOC_TYPES.INSPECTIONS_AND_RECOMMENDATIONS,
        ],
      },
      // SA details
      {
        key: HEADER_COMPONENTS.SA_DETAILS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.SA_DETAILS],
        render: true,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [
          ...NON_HEADER_CONFIGURABLE_DOC_TYPES,
          DOC_TYPES.APPOINTMENT_DETAILED_REPORT,
          DOC_TYPES.RO_DELIVERY_APPOINTMENT_DETAILED_REPORT,
        ],
      },
      {
        key: HEADER_COMPONENTS.SA_NAME,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.SA_NAME],
        render: true,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [
          ...NON_HEADER_CONFIGURABLE_DOC_TYPES,
          DOC_TYPES.APPOINTMENT_DETAILED_REPORT,
          DOC_TYPES.RO_DELIVERY_APPOINTMENT_DETAILED_REPORT,
        ],
      },
      {
        key: HEADER_COMPONENTS.SA_EMPLOYEE_NUMBER,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.SA_EMPLOYEE_NUMBER],
        render: true,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [
          ...NON_HEADER_CONFIGURABLE_DOC_TYPES,
          DOC_TYPES.APPOINTMENT_DETAILED_REPORT,
          DOC_TYPES.RO_DELIVERY_APPOINTMENT_DETAILED_REPORT,
        ],
      },
      {
        key: HEADER_COMPONENTS.SA_PHONE_NUMBER,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.SA_PHONE_NUMBER],
        render: true,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [
          ...NON_HEADER_CONFIGURABLE_DOC_TYPES,
          DOC_TYPES.APPOINTMENT_DETAILED_REPORT,
          DOC_TYPES.RO_DELIVERY_APPOINTMENT_DETAILED_REPORT,
        ],
      },
      {
        key: HEADER_COMPONENTS.TRANSPORTATION_TYPE,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.TRANSPORTATION_TYPE],
        render: false,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [...NON_HEADER_CONFIGURABLE_DOC_TYPES, DOC_TYPES.APPOINTMENT_DETAILED_REPORT],
      },
      {
        key: 'APPOINTMENT_TYPE',
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME.APPOINTMENT_TYPE,
        render: false,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [
          DOC_TYPES.INVOICE,
          DOC_TYPES.ESTIMATE,
          DOC_TYPES.SERVICE_BOOKLET,
          DOC_TYPES.VEHICLE_DIAGNOSTIC_REPORT,
        ],
        dealerProperties: [DEALER_PROPERTY.SERVICE_MULTIPLE_TRANSPORTATION_FLOW_ENABLED],
      },
      // insurance company
      {
        key: HEADER_COMPONENTS.INSURANCE_COMPANY,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.INSURANCE_COMPANY],
        render: true,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [DOC_TYPES.INVOICE],
        copyTypes: [COPY_TYPES.EXTERNAL_COMPANY],
      },
      {
        key: HEADER_COMPONENTS.INSURANCE_NUMBER,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.INSURANCE_NUMBER],
        render: true,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [DOC_TYPES.INVOICE],
        copyTypes: [COPY_TYPES.EXTERNAL_COMPANY],
      },
      {
        key: HEADER_COMPONENTS.INSURANCE_COMPANY_NUMBER,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.INSURANCE_COMPANY_NUMBER],
        render: true,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [DOC_TYPES.INVOICE],
        copyTypes: [COPY_TYPES.EXTERNAL_COMPANY],
      },
      {
        key: HEADER_COMPONENTS.INSURANCE_COMPANY_MOBILE_NUMBER,
        propertyDisplayName:
          SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.INSURANCE_COMPANY_MOBILE_NUMBER],
        render: true,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [DOC_TYPES.INVOICE],
        copyTypes: [COPY_TYPES.EXTERNAL_COMPANY],
      },
      {
        key: HEADER_COMPONENTS.INSURANCE_COMPANY_EMAIL,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.INSURANCE_COMPANY_EMAIL],
        render: true,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [DOC_TYPES.INVOICE],
        copyTypes: [COPY_TYPES.EXTERNAL_COMPANY],
      },
      {
        key: HEADER_COMPONENTS.RETURN_RO_DETAILS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[HEADER_COMPONENTS.RETURN_RO_DETAILS],
        render: false,
        isDefault: false,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: [...NON_HEADER_CONFIGURABLE_DOC_TYPES, DOC_TYPES.APPOINTMENT_DETAILED_REPORT],
      },
      ...INSPECTION_AND_RECOMMENDATIONS_SPECIFIC_HEADER_FIELDS,
    ],
  },
  {
    sectionName: SECTION_TYPES.FOOTER,
    sectionType: 'DEFAULT',
    render: true,
    sectionProperties: [
      {
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[FOOTER_COMPONENTS.LEGAL_TERMS],
        key: FOOTER_COMPONENTS.LEGAL_TERMS,
        order: 1,
        render: false,
        isDefault: false,
        displayOption: PAGE_TYPES.ALL_PAGES,
        inputType: CONFIG_FIELD_TYPES.TERMS_CONTENT,
        value: EMPTY_OBJECT,
        documentTypes: [
          DOC_TYPES.ESTIMATE,
          DOC_TYPES.INVOICE,
          DOC_TYPES.RO_INVOICE,
          DOC_TYPES.RO_PAYER_INVOICE,
          DOC_TYPES.QUOTE,
          DOC_TYPES.APPOINTMENT_DETAILED_REPORT,
          DOC_TYPES.RETURN_PARTS,
        ],
      },
      {
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[FOOTER_COMPONENTS.MESSAGE_TO_CUSTOMER],
        key: FOOTER_COMPONENTS.MESSAGE_TO_CUSTOMER,
        order: 2,
        render: false,
        isDefault: false,
        displayOption: PAGE_TYPES.ALL_PAGES,
        inputType: CONFIG_FIELD_TYPES.TERMS_CONTENT,
        documentTypes: [
          DOC_TYPES.ESTIMATE,
          DOC_TYPES.INVOICE,
          DOC_TYPES.RO_INVOICE,
          DOC_TYPES.RO_PAYER_INVOICE,
          DOC_TYPES.QUOTE,
          DOC_TYPES.APPOINTMENT_DETAILED_REPORT,
          DOC_TYPES.RETURN_PARTS,
        ],
      },
      // will be used later in future releases
      // {
      //   propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[FOOTER_COMPONENTS.SIGNATURE_SECTION],
      //   key: FOOTER_COMPONENTS.SIGNATURE_SECTION,
      //   order: 3,
      //   render: true,
      //   isDefault: false,
      //   displayOption: PAGE_TYPES.ALL_PAGES,
      // },
      {
        propertyDisplayName:
          SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[FOOTER_COMPONENTS.DEALER_REGISTRATION_NUMBER],
        key: FOOTER_COMPONENTS.DEALER_REGISTRATION_NUMBER,
        order: 5,
        render: false,
        isDefault: false,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: DOC_TYPES,
      },
      {
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[FOOTER_COMPONENTS.QR_CODE],
        key: FOOTER_COMPONENTS.QR_CODE,
        order: 7,
        render: true,
        isDefault: false,
        displayOption: PAGE_TYPES.ALL_PAGES,
        documentTypes: FOOTER_QR_CODE_DOC_TYPES,
      },
    ],
  },
  {
    sectionName: SECTION_TYPES.BACK_PRINT,
    render: false,
    sectionType: 'DEFAULT',
    sectionProperties: [
      {
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BACK_PRINT_COMPONENTS.LEGAL_TERMS],
        key: BACK_PRINT_COMPONENTS.LEGAL_TERMS,
        order: 1,
        render: false,
        isDefault: false,
        displayOption: PAGE_TYPES.ALL_PAGES,
        inputType: CONFIG_FIELD_TYPES.TERMS_CONTENT,
        documentTypes: [DOC_TYPES.ESTIMATE, DOC_TYPES.INVOICE, DOC_TYPES.RO_INVOICE, DOC_TYPES.RO_PAYER_INVOICE],
        value: EMPTY_OBJECT,
      },
    ],
  },
  {
    sectionName: SECTION_TYPES[PDF_SECTION.BASELINE_PAGE],
    render: false,
    sectionType: 'DEFAULT',
    sectionProperties: [
      {
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BASELINE_PAGE_COMPONENTS.WATERMARK_TEXT],
        key: BASELINE_PAGE_COMPONENTS.WATERMARK_TEXT,
        order: 9,
        render: false,
        isDefault: false,
        displayOption: PAGE_TYPES.ALL_PAGES,
        inputType: CONFIG_FIELD_TYPES.TEXT_AREA,
        documentTypes: [
          DOC_TYPES.ESTIMATE,
          DOC_TYPES.RO_ESTIMATE,
          DOC_TYPES.INVOICE,
          DOC_TYPES.RO_INVOICE,
          DOC_TYPES.RO_PAYER_INVOICE,
          DOC_TYPES.RO_ACCOUNTING_INVOICE,
        ],
      },
    ],
  },
];

const ALL_COPY_TYPES = _values(COPY_TYPES);

const INVOICE_BODY_SECTION_PROPERTIES = [
  {
    key: BODY_COMPONENTS.JOBS,
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.JOBS],
    render: true,
    value: _values(_get(BODY_SECTION_CONFIGURATIONS, [DOC_TYPES.INVOICE, JOBS_COMPONENT_KEY])),
    inputType: CONFIG_FIELD_TYPES.CONFIGURATION,
    copyTypes: [COPY_TYPES.WARRANTY, COPY_TYPES.INTERNAL, COPY_TYPES.ACCOUNTING, COPY_TYPES.SERVICE_ADVISOR],
  },
  {
    key: BODY_COMPONENTS.JOB_TABLE,
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.JOB_TABLE],
    render: true,
    value: _values(_get(BODY_SECTION_CONFIGURATIONS, [DOC_TYPES.INVOICE, BODY_COMPONENTS.JOB_TABLE])),
    inputType: CONFIG_FIELD_TYPES.CONFIGURATION,
    copyTypes: [COPY_TYPES.PAYER],
  },
  {
    key: BODY_COMPONENTS.JOBS_WITH_CONFIGURABLE_THIRD_PARTY_SPLIT,
    propertyDisplayName:
      SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.JOBS_WITH_CONFIGURABLE_THIRD_PARTY_SPLIT],
    render: true,
    value: _values(
      _omit(_get(BODY_SECTION_CONFIGURATIONS, [DOC_TYPES.INVOICE, JOBS_COMPONENT_KEY]), [
        JOB_SUB_SECTION_KEY.SHOW_APPROVAL_NOTES,
      ])
    ),
    inputType: CONFIG_FIELD_TYPES.CONFIGURATION,
    copyTypes: [COPY_TYPES.CUSTOMER, COPY_TYPES.EXTERNAL_COMPANY],
  },
  {
    key: BODY_COMPONENTS.ADDITIONAL_FEE,
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.ADDITIONAL_FEE],
    render: true,
    copyTypes: ALL_COPY_TYPES,
  },
  {
    key: BODY_COMPONENTS.ADDITIONAL_COUPONS,
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.ADDITIONAL_COUPONS],
    render: true,
    copyTypes: ALL_COPY_TYPES,
  },
  {
    key: BODY_COMPONENTS.DEFERRED_RECOMMENDATIONS,
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.DEFERRED_RECOMMENDATIONS],
    render: false,
    value: _values(BODY_SECTION_CONFIGURATIONS[DOC_TYPES.INVOICE].DEFFERED_RECOMMENDATIONS),
    inputType: CONFIG_FIELD_TYPES.CONFIGURATION,
    copyTypes: ALL_COPY_TYPES,
  },
  {
    key: BODY_COMPONENTS.DEFERRED_RECALLS,
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.DEFERRED_RECALLS],
    render: false,
    copyTypes: ALL_COPY_TYPES,
  },
  {
    key: BODY_COMPONENTS.SUMMARY,
    propertyDisplayName:
      SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.SUMMARY_WITH_CONFIGURABLE_THIRD_PARTY_SPLIT],
    render: true,
    value: ROConstraints.isServiceV3Enabled()
      ? [
          BODY_SECTION_CONFIGURATIONS[DOC_TYPES.INVOICE].SUMMARY[COST_BREAKDOWN_FIELDS.LABOR],
          BODY_SECTION_CONFIGURATIONS[DOC_TYPES.INVOICE].SUMMARY[COST_BREAKDOWN_FIELDS.PARTS],
          BODY_SECTION_CONFIGURATIONS[DOC_TYPES.INVOICE].SUMMARY[COST_BREAKDOWN_FIELDS.SUBLET_LABOR],
          BODY_SECTION_CONFIGURATIONS[DOC_TYPES.INVOICE].SUMMARY[COST_BREAKDOWN_FIELDS.SUBLET_PARTS],
          BODY_SECTION_CONFIGURATIONS[DOC_TYPES.INVOICE].SUMMARY[COST_BREAKDOWN_FIELDS.FEES],
          BODY_SECTION_CONFIGURATIONS[DOC_TYPES.INVOICE].SUMMARY[COST_BREAKDOWN_FIELDS.DISCOUNTS],
          BODY_SECTION_CONFIGURATIONS[DOC_TYPES.INVOICE].SUMMARY[COST_BREAKDOWN_FIELDS.TAX],
          BODY_SECTION_CONFIGURATIONS[DOC_TYPES.INVOICE].SUMMARY[COST_BREAKDOWN_FIELDS.NET],
          BODY_SECTION_CONFIGURATIONS[DOC_TYPES.INVOICE].SUMMARY[COST_BREAKDOWN_FIELDS.OTHER_PAYERS_CONTRIBUTION],
          BODY_SECTION_CONFIGURATIONS[DOC_TYPES.INVOICE].SUMMARY[COST_BREAKDOWN_FIELDS.SUBLET_TOTAL],
        ]
      : [BODY_SECTION_CONFIGURATIONS[DOC_TYPES.INVOICE].SUMMARY.SHOW_ESTIMATE],
    inputType: CONFIG_FIELD_TYPES.CONFIGURATION,
    copyTypes: [COPY_TYPES.WARRANTY, COPY_TYPES.INTERNAL, COPY_TYPES.SERVICE_ADVISOR, COPY_TYPES.EXTERNAL_COMPANY],
  },
  {
    key: BODY_COMPONENTS.SUMMARY_WITH_CONFIGURABLE_THIRD_PARTY_SPLIT,
    propertyDisplayName:
      SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.SUMMARY_WITH_CONFIGURABLE_THIRD_PARTY_SPLIT],
    render: true,
    value: ROConstraints.isServiceV3Enabled()
      ? [
          BODY_SECTION_CONFIGURATIONS[DOC_TYPES.INVOICE].SUMMARY[COST_BREAKDOWN_FIELDS.LABOR],
          BODY_SECTION_CONFIGURATIONS[DOC_TYPES.INVOICE].SUMMARY[COST_BREAKDOWN_FIELDS.PARTS],
          BODY_SECTION_CONFIGURATIONS[DOC_TYPES.INVOICE].SUMMARY[COST_BREAKDOWN_FIELDS.SUBLET_LABOR],
          BODY_SECTION_CONFIGURATIONS[DOC_TYPES.INVOICE].SUMMARY[COST_BREAKDOWN_FIELDS.SUBLET_PARTS],
          BODY_SECTION_CONFIGURATIONS[DOC_TYPES.INVOICE].SUMMARY[COST_BREAKDOWN_FIELDS.FEES],
          BODY_SECTION_CONFIGURATIONS[DOC_TYPES.INVOICE].SUMMARY[COST_BREAKDOWN_FIELDS.DISCOUNTS],
          BODY_SECTION_CONFIGURATIONS[DOC_TYPES.INVOICE].SUMMARY[COST_BREAKDOWN_FIELDS.TAX],
          BODY_SECTION_CONFIGURATIONS[DOC_TYPES.INVOICE].SUMMARY[COST_BREAKDOWN_FIELDS.NET],
          BODY_SECTION_CONFIGURATIONS[DOC_TYPES.INVOICE].SUMMARY[COST_BREAKDOWN_FIELDS.OTHER_PAYERS_CONTRIBUTION],
          BODY_SECTION_CONFIGURATIONS[DOC_TYPES.INVOICE].SUMMARY[COST_BREAKDOWN_FIELDS.SUBLET_TOTAL],
        ]
      : [
          BODY_SECTION_CONFIGURATIONS[DOC_TYPES.INVOICE].SUMMARY.SHOW_ESTIMATE,
          BODY_SECTION_CONFIGURATIONS[DOC_TYPES.INVOICE].SUMMARY.SHOW_PAID_VIA,
          BODY_SECTION_CONFIGURATIONS[DOC_TYPES.INVOICE].SUMMARY.SHOW_MY_GM_REWARDS,
        ],
    inputType: CONFIG_FIELD_TYPES.CONFIGURATION,
    copyTypes: [COPY_TYPES.CUSTOMER, COPY_TYPES.PAYER],
  },
  {
    key: BODY_COMPONENTS.SUMMARY_WITH_COST_INFO,
    propertyDisplayName:
      SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.SUMMARY_WITH_CONFIGURABLE_THIRD_PARTY_SPLIT],
    render: true,
    value: ROConstraints.isServiceV3Enabled()
      ? [BODY_SECTION_CONFIGURATIONS[DOC_TYPES.INVOICE].SUMMARY.SHOW_COST_INFO]
      : [
          BODY_SECTION_CONFIGURATIONS[DOC_TYPES.INVOICE].SUMMARY.SHOW_ESTIMATE,
          BODY_SECTION_CONFIGURATIONS[DOC_TYPES.INVOICE].SUMMARY.SHOW_COST_INFO,
        ],
    inputType: CONFIG_FIELD_TYPES.CONFIGURATION,
    copyTypes: [COPY_TYPES.ACCOUNTING],
  },
  {
    key: BODY_COMPONENTS.CUSTOMER_NOTES,
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.CUSTOMER_NOTES],
    render: true,
    copyTypes: ALL_COPY_TYPES,
  },
  {
    key: BODY_COMPONENTS.NEXT_SERVICE_DUE,
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.NEXT_SERVICE_DUE],
    render: true,
    copyTypes: [COPY_TYPES.CUSTOMER, COPY_TYPES.PAYER],
  },
  {
    key: BODY_COMPONENTS.CLOCK_IN_TABLE,
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.CLOCK_IN_TABLE],
    render: true,
    copyTypes: [COPY_TYPES.ACCOUNTING, COPY_TYPES.SERVICE_ADVISOR, COPY_TYPES.WARRANTY, COPY_TYPES.PAYER],
  },
  {
    key: BODY_COMPONENTS.ACCOUNTING_POSTING_INFO,
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.ACCOUNTING_POSTING_INFO],
    render: true,
    copyTypes: [COPY_TYPES.ACCOUNTING],
  },
  {
    key: BODY_COMPONENTS.SUMMARY_TABLE,
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.SUMMARY_TABLE],
    render: true,
    copyTypes: ALL_COPY_TYPES,
  },
  {
    key: BODY_COMPONENTS.SUMMARY_TABLE_WITH_AUTHORIZED_REPAIR,
    propertyDisplayName:
      SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.SUMMARY_TABLE_WITH_AUTHORIZED_REPAIR],
    render: false,
    copyTypes: ALL_COPY_TYPES,
  },
  {
    key: BODY_COMPONENTS.SA_NOTES,
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.SA_NOTES],
    render: true,
    copyTypes: ALL_COPY_TYPES,
  },
  {
    key: BODY_COMPONENTS.GREETING_NOTE,
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.GREETING_NOTE],
    render: false,
    copyTypes: ALL_COPY_TYPES,
    value: EMPTY_OBJECT,
    inputType: CONFIG_FIELD_TYPES.TERMS_CONTENT,
  },
  {
    key: BODY_COMPONENTS.LAST_PAGE_LEGAL_TERMS,
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.LAST_PAGE_LEGAL_TERMS],
    render: false,
    value: EMPTY_OBJECT,
    inputType: CONFIG_FIELD_TYPES.TERMS_CONTENT,
    copyTypes: ALL_COPY_TYPES,
  },
  {
    key: BODY_COMPONENTS.WARRANTY_STATEMENTS,
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.WARRANTY_STATEMENTS],
    render: false,
    value: EMPTY_OBJECT,
    copyTypes: ALL_COPY_TYPES,
    inputType: CONFIG_FIELD_TYPES.TERMS_CONTENT,
  },
  {
    key: BODY_COMPONENTS.SIGNATURE_SECTION,
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.SIGNATURE_SECTION],
    render: false,
    copyTypes: ALL_COPY_TYPES,
  },
  {
    key: BODY_COMPONENTS.CUSTOMER_SIGNATURE,
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.CUSTOMER_SIGNATURE],
    render: false,
    copyTypes: ALL_COPY_TYPES,
  },
  {
    key: BODY_COMPONENTS.DISCLOSURE,
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.DISCLOSURE],
    render: false,
    copyTypes: [COPY_TYPES.CUSTOMER],
    inputType: CONFIG_FIELD_TYPES.CONFIGURATION,
    value: EMPTY_ROW,
  },
  ..._map(_keys(DISCLOSURE_FIELDS), key => ({
    key,
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[key],
    render: false,
    copyTypes: [COPY_TYPES.CUSTOMER],
    inputType: CONFIG_FIELD_TYPES.CONFIGURATION,
    value: EMPTY_ROW,
  })),
  {
    key: 'TRANSPORT_TYPE_DETAILS',
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME.TRANSPORT_TYPE_DETAILS,
    render: false,
    copyTypes: ALL_COPY_TYPES,
    dealerProperties: [DEALER_PROPERTY.SERVICE_TRANSPORTATION_ADDITIONAL_FIELDS_ENABLED],
  },
];

const SERVICE_V3_SUPPORTED_INVOICE_BODY_SECTION_PROPERTIES = [
  ...INVOICE_BODY_SECTION_PROPERTIES,
  {
    key: BODY_COMPONENTS.TAX_BREAKDOWN_TABLE,
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.TAX_BREAKDOWN_TABLE],
    render: true,
    value: _values(_get(BODY_SECTION_CONFIGURATIONS, [DOC_TYPES.INVOICE, BODY_COMPONENTS.TAX_BREAKDOWN_TABLE])),
    inputType: CONFIG_FIELD_TYPES.CONFIGURATION,
    copyTypes: [COPY_TYPES.PAYER, COPY_TYPES.ACCOUNTING, COPY_TYPES.SERVICE_ADVISOR, COPY_TYPES.CUSTOMER],
  },
  {
    key: BODY_COMPONENTS.PAYER_SPLIT_TABLE,
    propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.PAYER_SPLIT_TABLE],
    render: true,
    copyTypes: [COPY_TYPES.ACCOUNTING, COPY_TYPES.SERVICE_ADVISOR],
  },
];

const SERVICE_V3_SUPPORTED_INVOICE_BODY_SECTIONS = {
  sectionName: SECTION_TYPES.BODY,
  render: true,
  sectionType: 'DEFAULT',
  sectionProperties: SERVICE_V3_SUPPORTED_INVOICE_BODY_SECTION_PROPERTIES,
};

export const PDF_BODY_SECTIONS_VS_DOC_TYPE = {
  [DOC_TYPES.ESTIMATE]: {
    sectionName: SECTION_TYPES.BODY,
    render: true,
    sectionType: 'DEFAULT',
    sectionProperties: [
      {
        key: BODY_COMPONENTS.BODY_LEGAL_TERMS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.BODY_LEGAL_TERMS],
        render: false,
        copyTypes: ALL_COPY_TYPES,
        value: EMPTY_OBJECT,
        inputType: CONFIG_FIELD_TYPES.TERMS_CONTENT,
      },
      {
        key: BODY_COMPONENTS.JOBS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.JOBS],
        render: true,
        value: _values(_get(BODY_SECTION_CONFIGURATIONS, [DOC_TYPES.ESTIMATE, JOBS_COMPONENT_KEY])),
        inputType: CONFIG_FIELD_TYPES.CONFIGURATION,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.ADDITIONAL_JOB_SPACE,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.ADDITIONAL_JOB_SPACE],
        render: false,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.CUSTOMER_NOTES,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.CUSTOMER_NOTES],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.ADDITIONAL_FEE,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.ADDITIONAL_FEE],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.ADDITIONAL_COUPONS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.ADDITIONAL_COUPONS],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.SERVICE_HISTORY,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.SERVICE_HISTORY],
        render: false,
        value: _values(BODY_SECTION_CONFIGURATIONS[DOC_TYPES.ESTIMATE].SERVICE_HISTORY),
        inputType: CONFIG_FIELD_TYPES.CONFIGURATION,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.DEFFERED_RECOMMENDATIONS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.DEFERRED_RECOMMENDATIONS],
        render: false,
        value: _values(BODY_SECTION_CONFIGURATIONS[DOC_TYPES.ESTIMATE].DEFFERED_RECOMMENDATIONS),
        inputType: CONFIG_FIELD_TYPES.CONFIGURATION,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.SUMMARY_TABLE,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.SUMMARY_TABLE],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.SUMMARY_TABLE_WITH_AUTHORIZED_REPAIR,
        propertyDisplayName:
          SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.SUMMARY_TABLE_WITH_AUTHORIZED_REPAIR],
        render: false,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.DEFERRED_RECALLS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.DEFERRED_RECALLS],
        render: false,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.SUMMARY,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.SUMMARY],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.SA_NOTES,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.SA_NOTES],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.LAST_PAGE_LEGAL_TERMS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.LAST_PAGE_LEGAL_TERMS],
        render: false,
        value: EMPTY_OBJECT,
        inputType: CONFIG_FIELD_TYPES.TERMS_CONTENT,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.GREETING_NOTE,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.GREETING_NOTE],
        render: false,
        copyTypes: ALL_COPY_TYPES,
        value: EMPTY_OBJECT,
        inputType: CONFIG_FIELD_TYPES.TERMS_CONTENT,
      },
      {
        key: BODY_COMPONENTS.SIGNATURE_SECTION,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.SIGNATURE_SECTION],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.DISCLOSURE,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.DISCLOSURE],
        render: false,
        copyTypes: [COPY_TYPES.CUSTOMER],
        inputType: CONFIG_FIELD_TYPES.CONFIGURATION,
        value: EMPTY_ROW,
      },
      ..._map(_keys(DISCLOSURE_FIELDS), key => ({
        key,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[key],
        render: false,
        copyTypes: [COPY_TYPES.CUSTOMER],
        inputType: CONFIG_FIELD_TYPES.CONFIGURATION,
        value: EMPTY_ROW,
      })),
      {
        key: 'TRANSPORT_TYPE_DETAILS',
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME.TRANSPORT_TYPE_DETAILS,
        render: false,
        copyTypes: ALL_COPY_TYPES,
        dealerProperties: [DEALER_PROPERTY.SERVICE_TRANSPORTATION_ADDITIONAL_FIELDS_ENABLED],
      },
    ],
  },
  [DOC_TYPES.INVOICE]: {
    sectionName: SECTION_TYPES.BODY,
    render: true,
    sectionType: 'DEFAULT',
    sectionProperties: INVOICE_BODY_SECTION_PROPERTIES,
  },
  [DOC_TYPES.RO_INVOICE]: SERVICE_V3_SUPPORTED_INVOICE_BODY_SECTIONS,
  [DOC_TYPES.RO_ACCOUNTING_INVOICE]: SERVICE_V3_SUPPORTED_INVOICE_BODY_SECTIONS,
  [DOC_TYPES.RO_PAYER_INVOICE]: SERVICE_V3_SUPPORTED_INVOICE_BODY_SECTIONS,
  [DOC_TYPES.RO_CANCELLED_INVOICE]: SERVICE_V3_SUPPORTED_INVOICE_BODY_SECTIONS,
  [DOC_TYPES.RO_ACCOUNTING_CANCELLED_INVOICE]: SERVICE_V3_SUPPORTED_INVOICE_BODY_SECTIONS,
  [DOC_TYPES.RO_PAYER_CANCELLED_INVOICE]: SERVICE_V3_SUPPORTED_INVOICE_BODY_SECTIONS,
  [DOC_TYPES.INSPECTIONS_AND_RECOMMENDATIONS]: {
    sectionName: SECTION_TYPES.BODY,
    render: true,
    sectionType: 'DEFAULT',
    sectionProperties: [
      {
        key: BODY_COMPONENTS.INSPECTION_DETAILS_WITH_CONFIGURABLE_SHOW_INSPECTION_RANGE_INFO,
        propertyDisplayName:
          SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[
            BODY_COMPONENTS.INSPECTION_DETAILS_WITH_CONFIGURABLE_SHOW_INSPECTION_RANGE_INFO
          ],
        render: true,
        value: _values(BODY_SECTION_CONFIGURATIONS[DOC_TYPES.INSPECTIONS_AND_RECOMMENDATIONS].INSPECTION_DETAILS),
        inputType: CONFIG_FIELD_TYPES.CONFIGURATION,
        copyTypes: [COPY_TYPES.CUSTOMER],
      },
      {
        key: BODY_COMPONENTS.INSPECTION_DETAILS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.INSPECTION_DETAILS],
        render: true,
        copyTypes: [COPY_TYPES.INTERNAL, COPY_TYPES.WARRANTY],
      },
      {
        key: BODY_COMPONENTS.PENDING_RECOMMENDATIONS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.PENDING_RECOMMENDATIONS],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.PENDING_RECOMMENDATIONS_SUMMARY,
        propertyDisplayName:
          SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.PENDING_RECOMMENDATIONS_SUMMARY],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.CA_RECOMMENDATIONS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.CA_RECOMMENDATIONS],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.CA_RECOMMENDATIONS_SUMMARY,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.CA_RECOMMENDATIONS_SUMMARY],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.JOBS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.JOBS],
        render: true,
        value: _values(
          _omit(_get(BODY_SECTION_CONFIGURATIONS, [DOC_TYPES.INSPECTIONS_AND_RECOMMENDATIONS, JOBS_COMPONENT_KEY]), [
            JOB_SUB_SECTION_KEY.SHOW_JOB_EXTERNAL_NOTES,
          ])
        ),
        inputType: CONFIG_FIELD_TYPES.CONFIGURATION,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.JOBS_RECOMMENDATIONS_SUMMARY,
        propertyDisplayName:
          SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.JOBS_RECOMMENDATIONS_SUMMARY],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.CUSTOMER_NOTES,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.CUSTOMER_NOTES],
        render: false,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.DEFERRED_RECOMMENDATIONS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.DEFERRED_RECOMMENDATIONS],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.SUMMARY_TABLE,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.MPVI_SUMMARY_TABLE],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.SIGNATURE_SECTION,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.SIGNATURE_SECTION],
        render: true,
        copyTypes: [COPY_TYPES.CUSTOMER],
      },
      {
        key: BODY_COMPONENTS.DUMMY_SIGNATURE_SECTION,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.DUMMY_SIGNATURE_SECTION],
        render: true,
        copyTypes: [COPY_TYPES.INTERNAL, COPY_TYPES.WARRANTY],
      },
      {
        key: BODY_COMPONENTS.DISCLOSURE,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.DISCLOSURE],
        render: false,
        copyTypes: [COPY_TYPES.CUSTOMER],
        inputType: CONFIG_FIELD_TYPES.CONFIGURATION,
        value: EMPTY_ROW,
      },
      ..._map(_keys(DISCLOSURE_FIELDS), key => ({
        key,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[key],
        render: false,
        copyTypes: [COPY_TYPES.CUSTOMER],
        inputType: CONFIG_FIELD_TYPES.CONFIGURATION,
        value: EMPTY_ROW,
      })),
      {
        key: BODY_COMPONENTS.SA_NOTES,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.SA_NOTES],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.INSPECTION_MEDIA,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.INSPECTION_MEDIA],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
    ],
  },
  [DOC_TYPES.VIS]: {
    sectionName: SECTION_TYPES.BODY,
    render: true,
    sectionType: 'DEFAULT',
    sectionProperties: [
      {
        key: BODY_COMPONENTS.VEHICLE_INFORMATION,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.VEHICLE_INFORMATION],
        render: false,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.ADDITIONAL_INFORMATION,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.ADDITIONAL_INFORMATION],
        render: false,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.SUBSCRIPTION_STATUSES,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.SUBSCRIPTION_STATUSES],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.WARRANTY_BLOCK,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.WARRANTY_BLOCK],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.BRANDED_TITLE,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.BRANDED_TITLE],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.MOST_RECENT_SERVICES,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.MOST_RECENT_SERVICES],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.LAST_MAINTENANCE,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.LAST_MAINTENANCE],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.REQUIRED_FIELD_ACTIONS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.REQUIRED_FIELD_ACTIONS],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.RECALLS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.RECALLS],
        render: false,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.WARRANTY_COVERAGE,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.WARRANTY_COVERAGE],
        render: false,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.SERVICE_CONTRACTS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.SERVICE_CONTRACTS],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.SERVICE_INFORMATION,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.SERVICE_INFORMATION],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.WARRANTY_HISTORY,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.WARRANTY_HISTORY],
        render: true,
        value: _values(BODY_SECTION_CONFIGURATIONS[DOC_TYPES.VIS].PART_DETAILS),
        inputType: CONFIG_FIELD_TYPES.CONFIGURATION,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.APPLICABLE_WARRANTIES,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.APPLICABLE_WARRANTIES],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.DEALER_SERVICE_HISTORY,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.DEALER_SERVICE_HISTORY],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
    ],
  },
  [DOC_TYPES.SERVICE_BOOKLET]: {
    sectionName: SECTION_TYPES.BODY,
    render: true,
    sectionType: 'DEFAULT',
    sectionProperties: [
      {
        key: BODY_COMPONENTS.NEXT_APPOINTMENT,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.NEXT_APPOINTMENT],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.NEXT_REQUIRED_MAINTENANCE,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.NEXT_REQUIRED_MAINTENANCE],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.ACCEPTED_SERVICES,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.ACCEPTED_SERVICES],
        render: true,
        value: [
          ..._values(
            _omit(_get(BODY_SECTION_CONFIGURATIONS, [DOC_TYPES.SERVICE_BOOKLET, JOBS_COMPONENT_KEY]), [
              JOB_SUB_SECTION_KEY.SHOW_JOB_EXTERNAL_NOTES,
              ...(ROConstraints.isServiceV3Enabled() ? [JOB_SUB_SECTION_KEY.SHOW_ESTIMATE] : []),
            ])
          ),
        ],
        inputType: CONFIG_FIELD_TYPES.CONFIGURATION,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.MPVI_INSPECTION_DETAILS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.MPVI_INSPECTION_DETAILS],
        render: true,
        value: _values(BODY_SECTION_CONFIGURATIONS[DOC_TYPES.SERVICE_BOOKLET].INSPECTION_DETAILS),
        inputType: CONFIG_FIELD_TYPES.CONFIGURATION,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.DECLINED_SERVICES,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.DECLINED_SERVICES],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.DAMAGES,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.DAMAGES],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
    ],
  },
  [DOC_TYPES.DAMAGES]: {
    sectionName: SECTION_TYPES.BODY,
    render: true,
    sectionType: 'DEFAULT',
    sectionProperties: [],
  },
  [DOC_TYPES.TRANSACTION_HISTORY]: {
    sectionName: SECTION_TYPES.BODY,
    render: true,
    sectionType: 'DEFAULT',
    sectionProperties: [],
  },
  [DOC_TYPES.DTC_REPORT]: {
    sectionName: SECTION_TYPES.BODY,
    render: true,
    sectionType: 'DEFAULT',
    sectionProperties: [],
  },
  [DOC_TYPES.VEHICLE_DIAGNOSTIC_REPORT]: {
    sectionName: SECTION_TYPES.BODY,
    render: true,
    sectionType: 'DEFAULT',
    sectionProperties: [
      {
        key: BODY_COMPONENTS.SEVERITY_DETAILS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.SEVERITY_DETAILS],
        render: true,
        copyTypes: [COPY_TYPES.TECHNICIAN],
        value: _values(_get(BODY_SECTION_CONFIGURATIONS, [DOC_TYPES.VEHICLE_DIAGNOSTIC_REPORT, SEVERITY_DETAILS])),
        inputType: CONFIG_FIELD_TYPES.CONFIGURATION,
        dealerProperties: [DEALER_PROPERTY.TELEMATIC_OBD_ENABLED, DEALER_PROPERTY.OBD_ENABLED],
      },
      {
        key: BODY_COMPONENTS.MPI_READINGS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.MPI_READINGS],
        render: false,
        copyTypes: [COPY_TYPES.TECHNICIAN],
        dealerProperties: [DEALER_PROPERTY.TELEMATIC_OBD_ENABLED],
      },
    ],
  },
  [DOC_TYPES.APPOINTMENT_REPORT]: {
    // RO Appointment report
    sectionName: SECTION_TYPES.BODY,
    render: true,
    sectionType: 'DEFAULT',
    sectionProperties: [
      {
        key: BODY_COMPONENTS.APPOINTMENT_INFORMATION,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.APPOINTMENT_INFORMATION],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.ACCEPTED_SERVICES,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.ACCEPTED_SERVICES],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
    ],
  },
  [DOC_TYPES.APPOINTMENT_DETAILED_REPORT]: {
    sectionName: SECTION_TYPES.BODY,
    render: true,
    sectionType: 'DEFAULT',
    sectionProperties: [
      {
        key: BODY_COMPONENTS.JOBS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.JOBS],
        render: true,
        value: _values(
          _omit(_get(BODY_SECTION_CONFIGURATIONS, [DOC_TYPES.APPOINTMENT_DETAILED_REPORT, JOBS_COMPONENT_KEY]), [
            JOB_SUB_SECTION_KEY.SHOW_JOB_EXTERNAL_NOTES,
            JOB_SUB_SECTION_KEY.SHOW_PART_NOTE,
            JOB_SUB_SECTION_KEY.SHOW_SOR_PART_TABLE,
            JOB_SUB_SECTION_KEY.SHOW_TECHNICIAN_EMPLOYEE_CERTIFICATION_NUMBER,
          ])
        ),
        inputType: CONFIG_FIELD_TYPES.CONFIGURATION,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.APPOINTMENT_SUMMARY,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.APPOINTMENT_SUMMARY],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
    ],
  },
  [DOC_TYPES.MPVI]: {
    sectionName: SECTION_TYPES.BODY,
    render: true,
    sectionType: 'DEFAULT',
    sectionProperties: [
      {
        key: BODY_COMPONENTS.INSPECTION_DETAILS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.INSPECTION_DETAILS],
        render: true,
        value: _values(BODY_SECTION_CONFIGURATIONS[DOC_TYPES.MPVI].INSPECTION_DETAILS),
        inputType: CONFIG_FIELD_TYPES.CONFIGURATION,
        copyTypes: [COPY_TYPES.CUSTOMER],
      },
      {
        key: BODY_COMPONENTS.PENDING_RECOMMENDATIONS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.PENDING_RECOMMENDATIONS],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.PENDING_RECOMMENDATIONS_SUMMARY,
        propertyDisplayName:
          SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.PENDING_RECOMMENDATIONS_SUMMARY],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.CA_RECOMMENDATIONS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.CA_RECOMMENDATIONS],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.CA_RECOMMENDATIONS_SUMMARY,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.CA_RECOMMENDATIONS_SUMMARY],
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.JOBS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.JOBS],
        render: true,
        value: _values(
          _omit(_get(BODY_SECTION_CONFIGURATIONS, [DOC_TYPES.MPVI, JOBS_COMPONENT_KEY]), [
            JOB_SUB_SECTION_KEY.SHOW_JOB_EXTERNAL_NOTES,
          ])
        ),
        inputType: CONFIG_FIELD_TYPES.CONFIGURATION,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.JOBS_RECOMMENDATIONS_SUMMARY,
        propertyDisplayName:
          SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.JOBS_RECOMMENDATIONS_SUMMARY],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.CUSTOMER_NOTES,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.CUSTOMER_NOTES],
        render: false,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.DEFERRED_RECOMMENDATIONS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.DEFERRED_RECOMMENDATIONS],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.SUMMARY_TABLE,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.MPVI_SUMMARY_TABLE],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.SIGNATURE_SECTION,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.SIGNATURE_SECTION],
        render: true,
        copyTypes: [COPY_TYPES.CUSTOMER],
      },
      {
        key: BODY_COMPONENTS.SA_NOTES,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.SA_NOTES],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.INSPECTION_MEDIA,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.INSPECTION_MEDIA],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
    ],
  },
  [DOC_TYPES.QUOTE]: {
    sectionName: SECTION_TYPES.BODY,
    render: true,
    sectionType: 'DEFAULT',
    sectionProperties: [
      {
        key: BODY_COMPONENTS.JOBS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.JOBS],
        render: true,
        value: _values(
          _omit(_get(BODY_SECTION_CONFIGURATIONS, [DOC_TYPES.QUOTE, JOBS_COMPONENT_KEY]), [
            JOB_SUB_SECTION_KEY.SHOW_JOB_EXTERNAL_NOTES,
            JOB_SUB_SECTION_KEY.SHOW_PART_NOTE,
            JOB_SUB_SECTION_KEY.SHOW_SOR_PART_TABLE,
            JOB_SUB_SECTION_KEY.SHOW_TECHNICIAN_EMPLOYEE_CERTIFICATION_NUMBER,
          ])
        ),
        inputType: CONFIG_FIELD_TYPES.CONFIGURATION,
        copyTypes: ALL_COPY_TYPES,
      },
      {
        key: BODY_COMPONENTS.SUMMARY,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.QUOTE_SUMMARY],
        render: true,
        copyTypes: ALL_COPY_TYPES,
      },
    ],
  },
  [DOC_TYPES.RETURN_PARTS]: {
    sectionName: SECTION_TYPES.BODY,
    render: true,
    sectionType: 'DEFAULT',
    sectionProperties: [
      {
        key: BODY_COMPONENTS.JOBS,
        propertyDisplayName: SECTION_PROPERTY_KEYS_VS_PROPERTY_DISPLAY_NAME[BODY_COMPONENTS.JOBS],
        render: true,
        value: _values(_get(BODY_SECTION_CONFIGURATIONS, [DOC_TYPES.RETURN_PARTS, JOBS_COMPONENT_KEY])),
        inputType: CONFIG_FIELD_TYPES.CONFIGURATION,
        copyTypes: [COPY_TYPES.SERVICE_ADVISOR],
      },
    ],
  },
};

export const PDF_SETTINGS_CONTEXT_ID = 'PDF_SETTINGS_CONTEXT_ID';

export const Z_INDEX_GREATER_THAN_MULTILINGUAL_MODAL = '10000';
export const DEFAULT_X_CO_ORDINATE = 1450;
export const DEFAULT_Y_CO_ORDINATE = 250;

export const DEFAULT_MULTILINGUAL_MODAL_WINDOW_POSITION = {
  x: DEFAULT_X_CO_ORDINATE,
  y: DEFAULT_Y_CO_ORDINATE,
};

export const DP_BASED_SECTION_PROPERTIES = {
  [HEADER_COMPONENTS.ENGINE_HRS]: [DEALER_PROPERTY.MULTIPLE_VEHICLE_TYPE_ENABLED],
  [BODY_COMPONENTS.MPI_READINGS]: [DEALER_PROPERTY.TELEMATIC_OBD_ENABLED],
  [BODY_COMPONENTS.APPROVAL_LIST]: [DEALER_PROPERTY.SERVICE_APPROVAL_WORKFLOW_ENABLED],
  [BODY_COMPONENTS.DISCLOSURE]: [DEALER_PROPERTY.E_SIGN_SERVICE_INTEGRATION_ENABLED],
  RENTAL_VEHICLE_DETAILS: [DEALER_PROPERTY.SERVICE_TRANSPORTATION_ADDITIONAL_FIELDS_ENABLED],
  [BODY_COMPONENTS.SEVERITY_DETAILS]: [DEALER_PROPERTY.TELEMATIC_OBD_ENABLED, DEALER_PROPERTY.OBD_ENABLED],
  APPOINTMENT_TYPE: [DEALER_PROPERTY.SERVICE_MULTIPLE_TRANSPORTATION_FLOW_ENABLED],
  TAX_BREAKDOWN_TABLE: [DEALER_PROPERTY.SERVICE_V3_ENABLED],
};

export const FIELD_VS_INFO_LABEL = {
  [HEADER_COMPONENTS.PAYER_DETAILS]: __('If payer details are enabled, payer details will display in the PDF always'),
  [HEADER_COMPONENTS.CUSTOMER_DETAILS]: __(
    'If RO Customer details is enabled and payer details is disabled, RO customer details will display in the PDF'
  ),
  [BACK_PRINT_COMPONENTS.LEGAL_TERMS]: __('Please ensure the text entered fits in a one page document'),
  [BASELINE_PAGE_COMPONENTS.WATERMARK_TEXT]: __(
    'Watermark will not appear in the preview but will appear on the actual PDFs when enabled'
  ),
  ...(ROConstraints.isNewESignEnhancementEnabled()
    ? {
        [BODY_COMPONENTS.CUSTOMER_SIGNATURE]: __('Signature will be captured via E-Sign and is mandatory with date'),
      }
    : EMPTY_OBJECT),
};

export const MULTILINGUAL_SUFFIX_STRING = '_MULTILINGUAL_VALUE';

export const FIELD_CATEGORIES = {
  CLIENT_CATEGORY: 'CLIENT_CATEGORY',
};

export const FIELD_VALUES_PAYLOAD = { fieldCategory: FIELD_CATEGORIES.CLIENT_CATEGORY };

export const MAKE_DISPLAY_VALUE_FIELD = 'makeDisplayValue';

export const DRAWER_SECTION_PROPERTY_KEYS_VS_HIDE_CUSTOM_HEADER = {
  [BODY_COMPONENTS.DISCLOSURE]: true,
};

export const TOGGLE_DISABLE_RULES_FOR_E_SIGN = {
  [BODY_COMPONENTS.CUSTOMER_SIGNATURE]: {
    [DOC_TYPES.ESTIMATE]: [COPY_TYPES.CUSTOMER],
    [DOC_TYPES.RO_ESTIMATE]: [COPY_TYPES.CUSTOMER],
    [DOC_TYPES.INSPECTIONS_AND_RECOMMENDATIONS]: [COPY_TYPES.CUSTOMER],
    [DOC_TYPES.RO_INSPECTIONS_AND_RECOMMENDATIONS]: [COPY_TYPES.CUSTOMER],
  },
};

export const TOGGLE_DISABLE_RULES = {
  [BODY_COMPONENTS.CUSTOMER_SIGNATURE]: {
    [DOC_TYPES.INSPECTIONS_AND_RECOMMENDATIONS]: [COPY_TYPES.WARRANTY, COPY_TYPES.INTERNAL],
    [DOC_TYPES.RO_INSPECTIONS_AND_RECOMMENDATIONS]: [COPY_TYPES.WARRANTY, COPY_TYPES.INTERNAL],
  },
};

export const E_SIGN_CUSTOMER_SIGNATURE_VISIBILITY_CONFIG = {
  [DOC_TYPES.ESTIMATE]: [COPY_TYPES.CUSTOMER, COPY_TYPES.TECHNICIAN],
  [DOC_TYPES.RO_ESTIMATE]: [COPY_TYPES.CUSTOMER, COPY_TYPES.TECHNICIAN],
  [DOC_TYPES.INSPECTIONS_AND_RECOMMENDATIONS]: [COPY_TYPES.CUSTOMER, COPY_TYPES.WARRANTY, COPY_TYPES.INTERNAL],
  [DOC_TYPES.RO_INSPECTIONS_AND_RECOMMENDATIONS]: [COPY_TYPES.CUSTOMER, COPY_TYPES.WARRANTY, COPY_TYPES.INTERNAL],
  [DOC_TYPES.RO_PAYER_INVOICE]: [COPY_TYPES.PAYER],
  [DOC_TYPES.RO_INVOICE]: [COPY_TYPES.CUSTOMER],
  [DOC_TYPES.INVOICE]: [COPY_TYPES.CUSTOMER, COPY_TYPES.WARRANTY, COPY_TYPES.INTERNAL, COPY_TYPES.EXTERNAL_COMPANY],
};
