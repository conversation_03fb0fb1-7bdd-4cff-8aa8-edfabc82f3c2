import produce from 'immer';

import _map from 'lodash/map';
import _reduce from 'lodash/reduce';
import _toString from 'lodash/toString';
import _get from 'lodash/get';
import _filter from 'lodash/filter';
import _head from 'lodash/head';
import _castArray from 'lodash/castArray';
import _last from 'lodash/last';
import _size from 'lodash/size';
import _isNil from 'lodash/isNil';
import _omit from 'lodash/omit';
import _isNull from 'lodash/isNull';
import _dropRight from 'lodash/dropRight';
import _join from 'lodash/join';
import _pick from 'lodash/pick';
import _values from 'lodash/values';
import _isArray from 'lodash/isArray';
import _has from 'lodash/has';
import _isEmpty from 'lodash/isEmpty';
import _orderBy from 'lodash/orderBy';
import _set from 'lodash/set';
import _slice from 'lodash/slice';
import _some from 'lodash/some';
import _flow from 'lodash/flow';

import { tget, getCRUDPayload } from 'tbase/utils/general';
import { getStartOfDayEpoch, getToday, getUnix } from 'tbase/utils/dateUtils';
import { getUserAccessibleSites } from 'tbase/helpers/oemSite';
import { REMOVE_ACTION } from 'tcomponents/molecules/tableInputField/constants/general';
import laborDataReader from 'twidgets/appServices/service/readers/LaborPricing.reader';
import LaborRateVersionReader from 'tbusiness/appServices/service/readers/LaborRateVersion';
import { isRequiredRule, nonNegativeRule } from 'tbase/utils/formValidators';
import { SORT_SEQUENCE } from 'tbase/constants/sortOrder';
import { EMPTY_OBJECT, EMPTY_ARRAY, EMPTY_STRING } from 'tbase/app.constants';
import DEALER_PROPERTIES from 'tbase/constants/dealerProperties';
import { TIME_PREFERENCE } from 'utils/constants';
import laborPricingReader from 'twidgets/appServices/service/readers/LaborPricing.reader';
import { RATE_TYPE_VALUES, RATE_TYPE_VS_LABEL } from 'tbusiness/appServices/service/constants/laborRates';

import { getColumnNameFromColumnNumber } from 'utils';
import RepairOrderEnv from 'utils/repairOrderEnv';
import Constraints from 'helpers/constraints';

import LaborDetailApi from './laborDetail.api';
import {
  EFFECTIVE_DATE_STATUSES_ORDER_PRIORITY,
  LABOR_RATE_VERSIONS_COLUMN_IDS,
  LABOR_FORM_FIELDS,
  DYNAMIC_RATE_TYPES,
  DEFAULT_FORM_VALUES,
  PRICING_GRID_KEYS,
  DEFAULT_HOUR_LABOR_RATE,
  KEYS_TO_CHECK,
  MAX_LABOR_RATE_VERSIONS,
  RATE_TYPES_IN_ORDER,
} from './laborPricingForm.constants';
import { RATE, RATE_TYPE, HOURLY_INCREMENT_TYPE } from './CreateDynamicPricing/createDynamicGrid.constants';
import { EFFECTIVE_DATE_STATUSES } from '../constants';

export const getGridValues = matrix =>
  _map(matrix, (item, matrixIndex) =>
    _reduce(
      item,
      (result, item1, index) => {
        const intermediate = { ...result };
        intermediate.hour = matrixIndex;
        intermediate[_toString(index)] = item1;
        return intermediate;
      },
      {}
    )
  );

const getSanitizedSitesAndPriceCodes = () => {
  const sites = getUserAccessibleSites();
  return _map(sites, ({ siteId, name }) => ({
    siteId,
    siteName: name,
  }));
};

export const getDefaultFormValues = () => ({
  ...DEFAULT_FORM_VALUES,
  partPriceIdBySiteMap: getSanitizedSitesAndPriceCodes(),
});

export const getMultiSiteDealerProperty = () =>
  RepairOrderEnv.dealerProperty[DEALER_PROPERTIES.MULTI_OEM_SWITCH_ENABLED];

export const formatPricingGridForSubmission = (formValue, defaultLaborRateAfterMaxHours) => {
  const { pricingRule: pricingRuleCopy } = formValue;
  const pricingRule = _map(
    _filter(pricingRuleCopy, item => !item.isLastRow),
    item => ({
      ...item,
      rate: _get(item, 'rate'),
      rateType: _head(_get(item, 'rateType')),
    })
  );
  return {
    ..._pick(formValue, _values(PRICING_GRID_KEYS)),
    centRounding: EMPTY_ARRAY,
    dollarRounding: EMPTY_ARRAY,
    pricingRule,
    [LABOR_FORM_FIELDS.DEFAULT_LABOR_RATE_AFTER_MAX_HRS]: defaultLaborRateAfterMaxHours,
  };
};

const getFormToApiPartPriceCodeAndSiteData = payload => {
  const { partPriceIdBySiteMap = EMPTY_OBJECT } = payload || EMPTY_OBJECT;
  return _reduce(
    partPriceIdBySiteMap,
    (acc, { siteId, partPriceCode }) => ({
      ...acc,
      [siteId]: _isNil(partPriceCode) ? EMPTY_STRING : _head(partPriceCode),
    }),
    EMPTY_OBJECT
  );
};

const getLaborTimePreference = payload => {
  const { customerPreferenceTime, warrantyPreferenceTime } = payload;

  return [
    {
      timePreference: TIME_PREFERENCE.CUSTOMER_PREFERENCE,
      multiplierValue: customerPreferenceTime?.multiplierValue,
    },
    {
      timePreference: TIME_PREFERENCE.WARRANTY_PREFERENCE,
      multiplierValue: warrantyPreferenceTime?.multiplierValue,
    },
  ];
};

export const parseDataForSubmission = (payload, isEdit, isDuplicate = false) => ({
  ..._omit(
    payload,
    isDuplicate
      ? ['id', LABOR_FORM_FIELDS.DEFAULT_LABOR_RATE_AFTER_MAX_HRS]
      : [LABOR_FORM_FIELDS.DEFAULT_LABOR_RATE_AFTER_MAX_HRS]
  ),
  timePreference: laborPricingReader.timePreference(payload),
  laborTimePreference: getLaborTimePreference(payload),
  rateType: laborPricingReader.rateType(payload),
  name: laborPricingReader.name(payload),
  description: laborPricingReader.description(payload),
  hourLaborRate: laborPricingReader.hourLaborRate(payload),
  partPriceCode: _head(payload.partPriceCode) || null,
  partPriceIdBySiteMap: getFormToApiPartPriceCodeAndSiteData(payload),
  status: payload.status,
  pricingGrid: formatPricingGridForSubmission(
    tget(payload, 'pricingGrid', EMPTY_OBJECT),
    tget(payload, LABOR_FORM_FIELDS.DEFAULT_LABOR_RATE_AFTER_MAX_HRS, EMPTY_STRING)
  ),
  defaultRate: isDuplicate ? false : laborDataReader.isDefaultRate(payload),
  ...(isEdit ? { ID: laborPricingReader.id(payload) } : EMPTY_OBJECT),
});

const getSanitizedPartPriceCodeAndSite = payload => {
  const { partPriceIdBySiteMap = EMPTY_OBJECT } = payload || EMPTY_OBJECT;
  const sites = getUserAccessibleSites();
  return _map(sites, ({ siteId, name }) => ({
    siteName: name,
    partPriceCode: _castArray(_get(partPriceIdBySiteMap, siteId, EMPTY_STRING)),
    siteId,
  }));
};

export const formatDataForForm = payload => {
  const data = {
    ...payload,
    timePreference: _toString(laborDataReader.timePreference(payload)),
    customerPreferenceTime: laborDataReader.customerPreferenceTime(payload),
    warrantyPreferenceTime: laborDataReader.warrantyPreferenceTime(payload),
    partPriceCode: payload.partPriceCode ? _castArray(payload.partPriceCode) : EMPTY_ARRAY,
    partPriceIdBySiteMap: getSanitizedPartPriceCodeAndSite(payload),
    pricingGrid: formatPricingGridForForm(tget(payload, 'pricingGrid', EMPTY_OBJECT)),
    status: payload.status,
  };
  return data;
};

export const formatPricingGridForForm = payload => ({
  ...payload,
  pricingRule: getFormattedPricingRule(payload.pricingRule),
});

export const getFormattedPricingRule = pricingRule => {
  const formattedData = _map(pricingRule, item => ({
    ...item,
    rateType: _castArray(item.rateType),
  }));
  const lastObject = {
    isLastRow: true,
    startHour: _size(pricingRule) ? _last(pricingRule).endHour : 0.1,
  };
  return [...formattedData, lastObject];
};

export const getUpdatedLaborPricingGrid = pricingGrid =>
  _map(pricingGrid, priceList => _map(priceList, price => (_isNull(price) ? 0 : price)));

const getUpdatedPricingRule = (pricingRule, clickedRow, value) =>
  _reduce(
    pricingRule,
    (acc, rule) => {
      const { startHour, endHour, rate, rateType, hourlyIncrementType } = rule || EMPTY_OBJECT;
      const previousRule = {
        startHour,
        endHour: parseFloat((clickedRow - 0.1).toFixed(1)),
        rate,
        rateType,
        hourlyIncrementType,
      };
      const selectedRule = {
        startHour: clickedRow,
        endHour: clickedRow,
        rate: value,
        rateType: RATE_TYPE.FLAT,
        hourlyIncrementType,
      };
      const nextRule = {
        startHour: parseFloat((clickedRow + 0.1).toFixed(1)),
        endHour,
        rate,
        rateType,
        hourlyIncrementType,
      };
      if (startHour < clickedRow && endHour > clickedRow) return [...acc, previousRule, selectedRule, nextRule];
      if (startHour === clickedRow && endHour === clickedRow) return [...acc, selectedRule];
      if (startHour < clickedRow && endHour === clickedRow) return [...acc, previousRule, selectedRule];
      if (startHour === clickedRow && endHour > clickedRow) return [...acc, selectedRule, nextRule];
      return [...acc, rule];
    },
    EMPTY_ARRAY
  );

export const getPricingRuleWithLastRow = (pricingRule, dynamicPricingGridLength, lastStartTime = 0) => {
  const lastEntry = parseFloat((dynamicPricingGridLength - 0.1).toFixed(1));
  if (lastStartTime >= lastEntry) return pricingRule;
  const updatedPricingRule = _dropRight(pricingRule);
  const startHour = parseFloat(lastStartTime?.toFixed(1));
  return [
    ...updatedPricingRule,
    {
      startHour,
      endHour: lastEntry,
      rate: RATE,
      rateType: RATE_TYPE.FLAT,
      hourlyIncrementType: HOURLY_INCREMENT_TYPE,
    },
    {
      isLastRow: true,
      startHour: parseFloat(dynamicPricingGridLength?.toFixed(1)),
    },
  ];
};

export const getUpdatedPricingMatrix = pricingMatrix =>
  _map(pricingMatrix, pricingList => _map(pricingList, price => price ?? 0));

export const getFinalStartTime = pricingRule => _get(_last(pricingRule), 'startHour');

export const handleConvertToExcelColumnName = (row, column) => {
  const row1 = `${row + 1}`;
  const column1 = getColumnNameFromColumnNumber(column, EMPTY_STRING);
  return _join([column1, row1], EMPTY_STRING);
};

export const getRateTypeValue = rateType => (_isArray(rateType) ? _head(rateType) : rateType);

export const getDataForUpdateConfigs = (laborRateVersions, initialLaborRateVersions) => {
  const { entriesToBeCreated, entriesToBeUpdated, entriesToBeDeleted } = getCRUDPayload(
    laborRateVersions,
    initialLaborRateVersions,
    { keysToBeChecked: KEYS_TO_CHECK }
  );
  return { addedConfigs: entriesToBeCreated, updatedConfigs: entriesToBeUpdated, deletedConfigs: entriesToBeDeleted };
};

const getLaborRateVersionDTORequestList = ({
  version,
  rateType,
  effectiveDate,
  pricingGrid,
  pricingRule,
  isUpdate,
}) => ({
  ..._omit(version, LABOR_FORM_FIELDS.LABOR_RATE_VERSIONS),
  [LABOR_RATE_VERSIONS_COLUMN_IDS.RATE_TYPE]: getRateTypeValue(rateType),
  effectiveDate: isUpdate ? effectiveDate : getStartOfDayEpoch(effectiveDate),
  pricingGrid: {
    ...pricingGrid,
    pricingRule: _dropRight(pricingRule),
  },
});

const getUpdatedPricingRuleForLaborRateVersion = ({ pricingRule }) =>
  _map(pricingRule, rule =>
    _has(rule, LABOR_RATE_VERSIONS_COLUMN_IDS.RATE_TYPE)
      ? {
          ...rule,
          [LABOR_RATE_VERSIONS_COLUMN_IDS.RATE_TYPE]: _head(_get(rule, LABOR_RATE_VERSIONS_COLUMN_IDS.RATE_TYPE)),
        }
      : rule
  );

const getAddedOrUpdatedLaborRateVersionDTORequestList = (laborRateVersions, isUpdate = false) =>
  _map(laborRateVersions, version => {
    const { effectiveDate, rateType } = version;
    const pricingGrid = tget(version, 'pricingGrid', EMPTY_OBJECT);
    const sanitizedRateType = getRateTypeValue(rateType);
    const { pricingRule = EMPTY_ARRAY } = pricingGrid;
    const updatedPricingRule = getUpdatedPricingRuleForLaborRateVersion({ pricingRule });
    return getLaborRateVersionDTORequestList({
      version,
      rateType: sanitizedRateType,
      effectiveDate,
      pricingGrid,
      pricingRule: updatedPricingRule,
      isUpdate,
    });
  });

export const getUpdatedValues = ({ values, addedConfigs, updatedConfigs, deletedConfigs }) => {
  const addedLaborRateVersionReqDtoList = getAddedOrUpdatedLaborRateVersionDTORequestList(addedConfigs);
  const updatedLaborRateVersionReqDtoList = getAddedOrUpdatedLaborRateVersionDTORequestList(updatedConfigs, true);
  return {
    ...values,
    addedLaborRateVersionReqDtoList,
    updatedLaborRateVersionReqDtoList,
    deletedLaborRateVersionIds: deletedConfigs,
  };
};

const getRateTypeForLaborRateVersions = laborRateVersions => {
  if (_isEmpty(laborRateVersions)) return 0;
  const rateType = tget(laborRateVersions, [0, LABOR_RATE_VERSIONS_COLUMN_IDS.RATE_TYPE], RATE_TYPE_VALUES.HOURLY);
  return getRateTypeValue(rateType);
};

export const getUpdatedValuesForStatus = ({ values, laborRateVersions }) => ({
  ...values,
  [LABOR_RATE_VERSIONS_COLUMN_IDS.RATE_TYPE]: getRateTypeForLaborRateVersions(laborRateVersions),
  hourLaborRate: tget(laborRateVersions, [0, LABOR_FORM_FIELDS.RATE_VALUE], DEFAULT_HOUR_LABOR_RATE),
  pricingGrid: tget(laborRateVersions, [0, DYNAMIC_RATE_TYPES.PRICING_GRID], EMPTY_OBJECT),
});

export const getUpdatedLaborRateVersions = laborRateVersions =>
  _map(laborRateVersions, version => {
    const { rateType } = version;
    if (rateType !== RATE_TYPE_VALUES.DYNAMIC_PRICING) return version;
    const pricingMatrix = tget(
      version,
      [DYNAMIC_RATE_TYPES.PRICING_GRID, DYNAMIC_RATE_TYPES.PRICING_MATRIX],
      EMPTY_ARRAY
    );
    const dynamicPricingGrid = getGridValues(getUpdatedLaborPricingGrid(pricingMatrix));
    const pricingRuleOfLaborRateVersion = getFormattedPricingRule(
      tget(version, [DYNAMIC_RATE_TYPES.PRICING_GRID, DYNAMIC_RATE_TYPES.PRICING_RULE], EMPTY_ARRAY)
    );
    const finalStartTime = getFinalStartTime(pricingRuleOfLaborRateVersion);
    const updatedPricingRule = getPricingRuleWithLastRow(
      pricingRuleOfLaborRateVersion,
      _size(dynamicPricingGrid),
      finalStartTime
    );
    const updatedPricingMatrix = getUpdatedPricingMatrix(pricingMatrix);
    return produce(version, draftState => {
      _set(draftState, LABOR_FORM_FIELDS.DYNAMIC_PRICING_GRID, dynamicPricingGrid);
      _set(draftState, [DYNAMIC_RATE_TYPES.PRICING_GRID, DYNAMIC_RATE_TYPES.PRICING_RULE], updatedPricingRule);
      _set(draftState, [DYNAMIC_RATE_TYPES.PRICING_GRID, DYNAMIC_RATE_TYPES.PRICING_MATRIX], updatedPricingMatrix);
    });
  });

const isLaborRateVersionNotDeleted = laborRateVersion => !LaborRateVersionReader.deleted(laborRateVersion);

export const getSortedLaborRateVersions = laborRateVersions => {
  const filteredLaborRateVersions = _filter(laborRateVersions, isLaborRateVersionNotDeleted);
  const updatedLaborRateVersions = getUpdatedLaborRateVersions(filteredLaborRateVersions);
  const sortedLaborRateVersionsByDate = _orderBy(
    updatedLaborRateVersions,
    LABOR_RATE_VERSIONS_COLUMN_IDS.EFFECTIVE_DATE,
    SORT_SEQUENCE.ASC
  );
  return _orderBy(
    sortedLaborRateVersionsByDate,
    version => {
      const { effectiveStatus } = version;
      return _get(EFFECTIVE_DATE_STATUSES_ORDER_PRIORITY, effectiveStatus);
    },
    SORT_SEQUENCE.ASC
  );
};

export const updatePricingState = ({
  indexToUpdate,
  dynamicPricingGrid,
  dynamicPricingMatrix,
  updatedPricingRule,
  defaultLaborRateAfterMaxHours,
}) =>
  produce(draft => {
    const commonPath = [
      'values',
      LABOR_FORM_FIELDS.LABOR_RATE_VERSIONS,
      indexToUpdate,
      DYNAMIC_RATE_TYPES.PRICING_GRID,
    ];
    const pathWithoutPricingGrid = _dropRight(commonPath);
    _set(draft, [...pathWithoutPricingGrid, LABOR_FORM_FIELDS.DYNAMIC_PRICING_GRID], dynamicPricingGrid);
    _set(draft, [...commonPath, DYNAMIC_RATE_TYPES.PRICING_MATRIX], dynamicPricingMatrix);
    _set(draft, [...commonPath, DYNAMIC_RATE_TYPES.PRICING_RULE], updatedPricingRule);
    _set(draft, [...commonPath, LABOR_FORM_FIELDS.DEFAULT_LABOR_RATE_AFTER_MAX_HRS], defaultLaborRateAfterMaxHours);
    _set(draft, 'showLaborPriceModal', false);
  });

export const getNewRowDefaultValues = () => ({
  [LABOR_RATE_VERSIONS_COLUMN_IDS.RATE_TYPE]: RATE_TYPE_VALUES.DYNAMIC_PRICING,
  [LABOR_RATE_VERSIONS_COLUMN_IDS.EFFECTIVE_DATE]: getToday(),
  [LABOR_RATE_VERSIONS_COLUMN_IDS.EFFECTIVE_STATUS]: EFFECTIVE_DATE_STATUSES.UPCOMING,
});

export const getUpdatedPricingData = ({ dynamicPricingGrid, pricingGrid, index, item, value, clickedRow }) => {
  const { pricingRule, pricingMatrix } = pricingGrid;
  const pathToUpdate = [index, item];
  const updatedDynamicPricingGrid = produce(dynamicPricingGrid, draft => {
    _set(draft, pathToUpdate, value);
  });
  const updatedPricingMatrix = produce(pricingMatrix, draft => {
    _set(draft, pathToUpdate, value);
  });
  const updatedPricingRule = getUpdatedPricingRule(pricingRule, clickedRow, value);
  return { updatedPricingMatrix, updatedDynamicPricingGrid, updatedPricingRule };
};

export const RATE_TYPE_VS_GET_ADD_ON_LABEL = {
  [RATE_TYPE_VALUES.FIXED]: getCurrencySymbol => getCurrencySymbol(),
  DEFAULT: getCurrencySymbol => __('{{currencySymbol}}/hr', { currencySymbol: getCurrencySymbol() }),
};

export const getAddonBefore = (getCurrencySymbol, sanitizedLaborRate) =>
  (RATE_TYPE_VS_GET_ADD_ON_LABEL[sanitizedLaborRate] || RATE_TYPE_VS_GET_ADD_ON_LABEL.DEFAULT)(getCurrencySymbol);

export const getTableValue = (shouldShowAllEffectiveDateRows, laborRateVersions) =>
  shouldShowAllEffectiveDateRows ? laborRateVersions : _slice(laborRateVersions, 0, MAX_LABOR_RATE_VERSIONS);

export const getErrorMessage = hourLaborRate => {
  const { isValid, message } = isRequiredRule(undefined, hourLaborRate);
  if (!isValid) return message;
  const { message: errorMessage } = nonNegativeRule(undefined, hourLaborRate);
  return errorMessage;
};

export const LABOR_RATE_CONFIG = {
  getLaborRate: LaborDetailApi.getLaborRateByIDV2,
  getUpdatedValuesForStatus,
  getUpdatedValues,
};

const pickValues = ({ values }) => values;

const getLaborRateForDefault = id => LaborDetailApi.getLaborRateByID(id);

export const DEFAULT_CONFIG = {
  getLaborRate: getLaborRateForDefault,
  getUpdatedValuesForStatus: pickValues,
  getUpdatedValues: pickValues,
};

export const getRowActions = () => _castArray(REMOVE_ACTION);

const createRateTypeOption = rateType => ({ value: rateType, label: RATE_TYPE_VS_LABEL[rateType] });

export const getRateTypeOptions = () => _map(RATE_TYPES_IN_ORDER, createRateTypeOption);

const isHourLaborRateInvalid = hourLaborRate => _isNil(hourLaborRate) || hourLaborRate === '' || hourLaborRate < 0;

const isLaborRateEmpty = ({ rateType, hourLaborRate, pricingGrid }) => {
  const rateTypeValue = getRateTypeValue(rateType);
  return rateTypeValue !== RATE_TYPE_VALUES.DYNAMIC_PRICING
    ? isHourLaborRateInvalid(hourLaborRate)
    : _isEmpty(_get(pricingGrid, 'pricingMatrix'));
};

export const hasEmptyLaborRate = laborRateVersions => _some(laborRateVersions, isLaborRateEmpty);

const getEffectiveDateEpoch = _flow([LaborRateVersionReader.effectiveDate, getUnix]);

export const validateLaborRateVersions = laborRateVersions => {
  if (!Constraints.isLaborRateConfigEnabled()) {
    return { isValid: true };
  }
  const effectiveDateEpochs = _map(laborRateVersions, getEffectiveDateEpoch);
  const effectiveDateEpochsSet = new Set(effectiveDateEpochs);
  if (_size(effectiveDateEpochs) !== _size(effectiveDateEpochsSet)) {
    return { isValid: false, message: __('Effective dates cannot be the same') };
  }
  if (_isEmpty(effectiveDateEpochs)) {
    return { isValid: false, message: __('Labor rate versions can not be empty') };
  }
  if (hasEmptyLaborRate(laborRateVersions)) {
    return { isValid: false, message: __('Please fill valid labor rate value') };
  }
  return { isValid: true };
};
