import React, { useCallback, useState } from 'react';
import PropTypes from 'prop-types';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import classnames from 'classnames';

import _noop from 'lodash/noop';
import _keyBy from 'lodash/keyBy';
import _reduce from 'lodash/reduce';
import _isEmpty from 'lodash/isEmpty';
import _flatMap from 'lodash/flatMap';
import _map from 'lodash/map';
import _size from 'lodash/size';
import _get from 'lodash/get';
import _filter from 'lodash/filter';
import _includes from 'lodash/includes';

import Spinner from '@tekion/tekion-components/src/molecules/SpinnerComponent';
import Label from '@tekion/tekion-components/src/atoms/Label';
import Ellipsis from '@tekion/tekion-components/src/atoms/Ellipsis';
import StatusItem from '@tekion/tekion-components/src/atoms/StatusItem';
import Content from '@tekion/tekion-components/src/atoms/Content';
import NotesWithIcon from 'organisms/NotesWithIcon';
import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import { getServiceTitle } from '@tekion/tekion-business/src/appServices/service/helpers/recommendation';

import { getTranslatedValue } from '@tekion/tekion-service-mpvi-inspection-utils/helpers/utils';
import {
  updateMpviFormDetails,
  removeRecommendation,
  unlinkMpviRecommendation,
  submitMpviFormDetails,
  fetchInspectionsMediaCount,
  unLinkMpviJob,
  fetchOBDData,
} from 'actions/mpvi.actions';
import { updateMilesInOut, forceRefreshRO } from 'actions/roDetails.actions';
import { updateValidateJobCompleteFlag } from 'actions/jobDetails.action';
import MpviFormReader, {
  MPVI_FORM_SECTION_ID_FIELD_NAME,
  MPVI_FORM_ATTRIBUTES_FIELD_NAME,
  MPVI_FORM_MEDIA_COUNT,
} from '@tekion/tekion-service-mpvi-inspection-utils/readers/Mpvi';
import { getDealerUserROLocaleOptions } from '@tekion/tekion-widgets/src/appServices/service/utils/multilingual';
import ROJobReader, { getStatus } from 'readers/Jobs.reader';
import ROReader from 'readers/RepairOrder.reader';
import RecommendationReader from 'readers/Recommendation.reader';
import Constraints from 'helpers/constraints';
import { getFormattedDateTime, DATE_TIME_FORMATS } from '@tekion/tekion-base/utils/dateUtils';
import { getInspectionFormSection } from '@tekion/tekion-service-mpvi-inspection-components/organisms/MpviForm/MpviForm.helper';
import { getShouldDisplayMediaIcon } from '@tekion/tekion-service-mpvi-inspection-utils/helpers/mpvi';
import { getRecommendationsByInspectionId } from 'helpers/recommendation.helper';
import { getJobsByInspectionId } from 'helpers/job.helper';
import { hasPermissionToEditMpviForm } from 'permissions/roPermission';
import {
  MPVI_FORM_VIEW_TYPES,
  BUTTON_ACTIONS,
  CONDITION_VALUE_VS_SEVERITY,
} from '@tekion/tekion-service-mpvi-inspection-components/organisms/MpviForm/MpviForm.constants';
import { MODULE_TYPES } from 'organisms/ROForm/ROForm.constants';
import { NOTES } from '@tekion/tekion-business/src/appServices/service/constants/note.constants';
import {
  MPVI_STATUS_VALUES,
  MEDIA_ITEM_REQUIRED_VALUES,
} from '@tekion/tekion-service-mpvi-inspection-utils/constants/mpvi';
import { RECOMMENDATION_POPUP_MODES } from './Components/AddRecommendation/AddRecommendation.constants';

import AddRecommendation from './Components/AddRecommendation';
import LinkMpviEntity from './Components/LinkMpviEntity';
import InspectionMedia from './Components/InspectionMedia';
import ButtonWithKebabMenu from './Components/ButtonWithKebabMenu';
import AddRecommendationHeader from './Components/AddRecommendationHeader';
import ROEntityItem from './Components/ROEntityItem';
import MpviRecommendationInfo from './Components/MpviRecommendationInfo';
import MpviInspectionForm from './MpviInspectionForm';
import { VIEW_TYPES } from './Components/ROEntityItem/ROEntityItem.constants';
import { CONDITION_VALUE_CONFIG } from './MpviInspectionForm.constants';

import styles from './mpviInspectionFormContainer.module.scss';

const getFirstSections = (inspectionSection, inspectionItemsById) =>
  _reduce(
    inspectionSection,
    (acc, { childrens, id }) => (!_isEmpty(childrens) ? [...acc, inspectionItemsById[id]] : acc),
    []
  );

const renderLoader = () => (
  <div className="roDetails-loader full-width full-height">
    <Spinner id="roDetailsPage" />
  </div>
);

const recommendationRequiredConditionSet = new Set([
  CONDITION_VALUE_CONFIG.WARNING.id,
  CONDITION_VALUE_CONFIG.ERROR.id,
]);

const MpviInspectionFormContainer = props => {
  const {
    mpviDetails,
    recommendationByIds,
    intermediateRecommendationByIds,
    isAddRecommendationInProgress,
    roDetails,
    jobDetails,
    updateMpviFormDetails: updateMpviFormDetailsAction,
    validatePreRecommendationAdditionRules,
    fetchOBDData: fetchOBDDataAction,
    payTypeConfigurations,
    payerPayTypeConfiguration,
    defaultSubPayType,
  } = props;

  const [footerMediaCount, setFooterMediaCount] = useState({ photoCount: 0, videoCount: 0 });

  const { location, ...restProps } = props;
  const { currentForm, isLoading, settings, viewPending, inspectionHistoryValues, shouldFillOBDValues } =
    mpviDetails || EMPTY_OBJECT;
  const items = MpviFormReader.items(currentForm);
  const inspectionJobType = ROJobReader.getType(jobDetails[MpviFormReader.assetId(currentForm)]);
  const { idVersion = {}, formSharedtoCustomer } = currentForm || EMPTY_OBJECT;
  const inspectionItemsById = React.useMemo(() => _keyBy(items, MPVI_FORM_SECTION_ID_FIELD_NAME), [items]);
  const inspectionAllSection = React.useMemo(
    () => getInspectionFormSection(items, { viewPending: false, viewType: MPVI_FORM_VIEW_TYPES.SUBMISSION_VIEW }),
    [idVersion.formId, items]
  );
  const allVisibleFieldIds = React.useMemo(
    () => _flatMap(inspectionAllSection, ({ fieldIds }) => fieldIds),
    [inspectionAllSection]
  );
  const inspectionAllFirstSections = React.useMemo(
    () => getFirstSections(inspectionAllSection, inspectionItemsById),
    [inspectionAllSection, inspectionItemsById]
  );
  const recommendationsByInspectionId = React.useMemo(
    () => getRecommendationsByInspectionId(recommendationByIds),
    [recommendationByIds]
  );
  const intermeidateRecommendationsByInspectionId = React.useMemo(
    () => getRecommendationsByInspectionId(intermediateRecommendationByIds),
    [intermediateRecommendationByIds]
  );
  const jobsByInspectionId = React.useMemo(() => getJobsByInspectionId(jobDetails), [jobDetails]);
  const localeOptions = React.useMemo(() => getDealerUserROLocaleOptions(ROReader.getLocale(roDetails)), [roDetails]);

  // check this
  const handleRecommendationButtonClick = useCallback(
    ({ handleVisibilityChange, handleLinkVisibilityChange }) =>
      ({ type }) => {
        if (type === BUTTON_ACTIONS.ADD_RECOMMENDATION_ACTION)
          return handleVisibilityChange(RECOMMENDATION_POPUP_MODES.ADD_RECOMMENDATION_MODE)();
        if (type === BUTTON_ACTIONS.LINK_ACTION) return handleLinkVisibilityChange();
        return null;
      },
    []
  );

  const renderRecommendationModalTitle =
    ({ rowData, headerDetails }) =>
    () => (
      <AddRecommendationHeader
        inspection={rowData}
        parentLevelName={getTranslatedValue({
          data: headerDetails,
          localeOptions,
          valueReader: MpviFormReader.sectionName,
        })}
        localeOptions={localeOptions}
      />
    );

  const renderNotes = ({ rowData, isDisabled, handleNoteModalVisiblity, isNoteModalVisible }) => (
    <NotesWithIcon
      onNoteModalVisibility={handleNoteModalVisiblity}
      isNoteModalVisible={isNoteModalVisible}
      isNoteModalDisabled={isDisabled}
      assetType={NOTES.MPVI_INSPECTION_ITEM}
      mpviInstanceId={MpviFormReader.id(currentForm)}
      singleComment
      inspection={rowData}
      className={styles.notesIcon}
    />
  );

  const renderMediaAttachmentIcon = ({ rowData, isDisabled, onClick }) => {
    const shouldDisplayMediaIcon = getShouldDisplayMediaIcon(
      _get(rowData, MPVI_FORM_ATTRIBUTES_FIELD_NAME, EMPTY_ARRAY)
    );
    return (
      <>
        {shouldDisplayMediaIcon && (
          <InspectionMedia
            className={classnames('p-r-12', {
              [styles.mandatoryMediaIcon]: shouldDisplayMediaIcon === MEDIA_ITEM_REQUIRED_VALUES.MANDATORY,
            })}
            mediaCount={_get(rowData, MPVI_FORM_MEDIA_COUNT, 0)}
            onClick={onClick}
            isDisabled={isDisabled}
          />
        )}
      </>
    );
  };

  const renderRecommendation = recommendation => (
    <div key={RecommendationReader.id(recommendation)} className={styles.recommendation}>
      <div className={styles.recommendationDescription}>
        <Content>{`${getServiceTitle(recommendation)} - `}</Content>
        <Content className="flex">
          <Ellipsis length={15}>{RecommendationReader.description(recommendation)}</Ellipsis>
        </Content>
      </div>
      <StatusItem {...RecommendationReader.statusConfig(recommendation)} statusClass={styles.status} />
    </div>
  );

  const renderMpviRecommendationInfo = ({ progressDetails, value }) => {
    const { photoCount, videoCount } = footerMediaCount;
    return (
      <MpviRecommendationInfo
        progressDetails={progressDetails}
        recommendationByIds={recommendationByIds}
        conditionConfig={CONDITION_VALUE_CONFIG}
        value={value}
        settings={settings}
        photoCount={photoCount}
        videoCount={videoCount}
      />
    );
  };

  const renderRecommendationModal = ({
    mode,
    recommendation,
    isVisible,
    contentClassName = '',
    popOverProps,
    rowData,
    headerDetails,
    recentHistoryRecommendation = EMPTY_OBJECT,
    handleRecommendationSubmit = _noop,
    handleVisibilityChange,
    conditionUserValue,
    isDisabled,
    handleOpenMediaDrawer,
    handleRecommendationIntermediateSubmit = _noop,
  }) => (
    <AddRecommendation
      roDetails={roDetails}
      vin={ROReader.getVin(roDetails)}
      renderTitle={renderRecommendationModalTitle({ rowData, headerDetails })}
      inspectionId={MpviFormReader.id(rowData)}
      mediaCount={MpviFormReader.mediaCount(rowData)}
      mpviInstanceId={MpviFormReader.id(currentForm)}
      severity={CONDITION_VALUE_VS_SEVERITY[conditionUserValue]}
      editRecommendationId={RecommendationReader.id(recommendation)}
      mode={mode}
      onSubmit={handleRecommendationSubmit(mode)}
      onIntermediateSubmit={handleRecommendationIntermediateSubmit(mode)}
      onCancel={handleVisibilityChange(mode)}
      prevRecommendationDate={recentHistoryRecommendation?.createdAt}
      historyRecommendations={recentHistoryRecommendation?.recommendations}
      visible={isVisible}
      isDisabled={isDisabled}
      popOverProps={{
        mask: true,
        onVisibleChange: handleVisibilityChange(mode),
        placement: 'rightBottom',
        ...popOverProps,
      }}
      localeOptions={localeOptions}
      validatePreRecommendationAdditionRules={validatePreRecommendationAdditionRules}
      inspectionJobType={inspectionJobType}
      payTypeConfigurations={payTypeConfigurations}
      payerPayTypeConfiguration={payerPayTypeConfiguration}
      defaultSubPayType={defaultSubPayType}
      handleOpenMediaDrawer={handleOpenMediaDrawer}
      attributeInfos={MpviFormReader.attributes(rowData)}
      inspectionItemsById={inspectionItemsById}
      inspectionItemParentId={MpviFormReader.parentId(rowData)}
      shouldDisplayMediaIcon={getShouldDisplayMediaIcon(_get(rowData, MPVI_FORM_ATTRIBUTES_FIELD_NAME, EMPTY_ARRAY))}>
      <div role="button" tabIndex={0} className={contentClassName} onClick={handleVisibilityChange(mode)} />
    </AddRecommendation>
  );

  const renderLinkMpviEntity = ({
    rowData,
    headerDetails,
    isRecommendationLinkVisible,
    handleLinkVisibilityChange,
    handleLinkRecommendation,
    handleUnlinkRecommendation,
    allowLinkingJobs,
    allowLinkingRecommendations,
  }) => (
    <LinkMpviEntity
      inspectionId={MpviFormReader.id(rowData)}
      mpviInstanceId={MpviFormReader.id(currentForm)}
      renderTitle={renderRecommendationModalTitle({ rowData, headerDetails })}
      roId={ROReader.getROId(roDetails)}
      visible={isRecommendationLinkVisible}
      onCancel={handleLinkVisibilityChange}
      onLink={handleLinkRecommendation}
      onUnlink={handleUnlinkRecommendation}
      allowLinkingJobs={allowLinkingJobs}
      allowLinkingRecommendations={allowLinkingRecommendations}
      popOverProps={{
        mask: true,
        arrowPointAtCenter: true,
        onVisibleChange: handleLinkVisibilityChange,
        placement: 'right',
      }}
      localeOptions={localeOptions}>
      <span />
    </LinkMpviEntity>
  );

  const renderRecommendationItem =
    ({
      isDisabled,
      handleEditRecommendation,
      handleDeleteRecommendation,
      handleUnlink,
      rowData,
      headerDetails,
      recommendationModalVisiblityMode,
      editModalRecommendationId,
      handleVisibilityChange,
      compositeInspectionKey: key,
      conditionUserValue,
      handleOpenMediaDrawer,
      isAddingInProgress,
      handleRecommendationIntermediateSubmit,
    }) =>
    recommendationId => {
      const inspectionRecommendationsById = _keyBy(recommendationsByInspectionId[key], RecommendationReader.id);
      const intermediateInspectionRecommendationsById = _keyBy(
        intermeidateRecommendationsByInspectionId[key],
        RecommendationReader.id
      );
      const recommendation =
        inspectionRecommendationsById[recommendationId] || intermediateInspectionRecommendationsById[recommendationId];
      if (_isEmpty(recommendation)) return null;
      return (
        <div key={RecommendationReader.id(recommendation)} className={styles.recommendationItem}>
          <ROEntityItem
            viewType={VIEW_TYPES.SUBMISSION_FORM_VIEW}
            isDisabled={isDisabled}
            onEdit={handleEditRecommendation}
            onDelete={handleDeleteRecommendation}
            entity={recommendation}
            entityType={MODULE_TYPES.RO_TECH_RECOMMENDATIONS}
            onUnlink={handleUnlink(MpviFormReader.id(rowData))}
            isAddingInProgress={isAddingInProgress}
          />
          {renderRecommendationModal({
            mode: RECOMMENDATION_POPUP_MODES.EDIT_RECOMMENDATION_MODE,
            recommendation,
            isVisible:
              recommendationModalVisiblityMode === RECOMMENDATION_POPUP_MODES.EDIT_RECOMMENDATION_MODE &&
              editModalRecommendationId &&
              editModalRecommendationId === RecommendationReader.id(recommendation),
            rowData,
            headerDetails,
            handleVisibilityChange,
            conditionUserValue,
            handleOpenMediaDrawer,
            handleRecommendationIntermediateSubmit,
          })}
        </div>
      );
    };

  const renderJobItem =
    ({ handleUnlink, rowData, isDisabled, compositeInspectionKey }) =>
    jobId => {
      const inspectionJobsById = _keyBy(jobsByInspectionId[compositeInspectionKey], ROJobReader.getJobId);
      const job = inspectionJobsById[jobId];
      if (_isEmpty(job)) return null;
      return (
        <div key={ROJobReader.getJobId(job)} className={styles.recommendationItem}>
          <ROEntityItem
            viewType={VIEW_TYPES.SUBMISSION_FORM_VIEW}
            isDisabled={isDisabled}
            entity={job}
            entityType={MODULE_TYPES.RO_JOB}
            onUnlink={handleUnlink(MpviFormReader.id(rowData))}
          />
        </div>
      );
    };

  const renderHistoryRecommendation = ({
    recentHistoryRecommendation,
    recommendationModalVisiblityMode,
    headerDetails,
    rowData,
    handleRecommendationSubmit,
    handleVisibilityChange,
    conditionUserValue,
    isDisabled,
    handleOpenMediaDrawer,
    handleRecommendationIntermediateSubmit,
  }) => {
    if (_isEmpty(recentHistoryRecommendation)) return null;
    const recentHistoryRecommendationDate = recentHistoryRecommendation.createdAt
      ? __(`on ${getFormattedDateTime(recentHistoryRecommendation.createdAt, DATE_TIME_FORMATS.DATE_ALPHA_MONTH)} `)
      : '';
    const recommendationTitle = _size(recentHistoryRecommendation.recommendations) === 1 ? __('Rec.') : __('Recs.');
    return (
      <div className={styles.historyRecommendation}>
        {renderRecommendationModal({
          mode: RECOMMENDATION_POPUP_MODES.PREVIOUS_RECOMMENDATIONS_MODE,
          recommendation: undefined,
          isVisible: recommendationModalVisiblityMode === RECOMMENDATION_POPUP_MODES.PREVIOUS_RECOMMENDATIONS_MODE,
          contentClassName: `icon-info ${styles.infoIcon}`,
          headerDetails,
          rowData,
          handleRecommendationSubmit,
          handleVisibilityChange,
          conditionUserValue,
          isDisabled,
          recentHistoryRecommendation,
          handleOpenMediaDrawer,
          handleRecommendationIntermediateSubmit,
        })}
        <Label>
          {__(
            `${_size(
              recentHistoryRecommendation.recommendations
            )} ${recommendationTitle} ${recentHistoryRecommendationDate}`
          )}
        </Label>
      </div>
    );
  };

  const renderRecommendationWithAddItem = ({
    inspectionRecommendationIds,
    inspectionJobIds,
    isRecommendationRequired,
    isDisabled,
    allowLinkingJobs,
    allowLinkingRecommendations,
    recommendationModalVisiblityMode,
    editModalRecommendationId,
    handleUnlink,
    rowData,
    headerDetails,
    handleEditRecommendation,
    handleDeleteRecommendation,
    isRecommendationLinkVisible,
    handleLinkVisibilityChange,
    handleLinkRecommendation,
    handleUnlinkRecommendation,
    recentHistoryRecommendation,
    handleRecommendationSubmit,
    handleVisibilityChange,
    conditionUserValue,
    compositeInspectionKey,
    handleOpenMediaDrawer,
    handleRecommendationIntermediateSubmit,
    inspectionIntermediateRecommendationIds,
  }) => {
    const filteredInspectionRecommendationIds = _filter(
      inspectionRecommendationIds,
      recommendationId => !_includes(inspectionIntermediateRecommendationIds, recommendationId)
    );
    return (
      <div
        className={classnames(styles.recommendationsWithAdd, {
          [styles.showBorder]: !_isEmpty(inspectionRecommendationIds) || isRecommendationRequired,
        })}>
        {_map(
          filteredInspectionRecommendationIds,
          renderRecommendationItem({
            compositeInspectionKey,
            isDisabled,
            handleEditRecommendation,
            handleDeleteRecommendation,
            handleUnlink,
            rowData,
            headerDetails,
            recommendationModalVisiblityMode,
            editModalRecommendationId,
            handleVisibilityChange,
            conditionUserValue,
            handleOpenMediaDrawer,
            handleRecommendationIntermediateSubmit,
          })
        )}
        {_map(
          inspectionIntermediateRecommendationIds,
          renderRecommendationItem({
            compositeInspectionKey,
            isDisabled: true,
            isAddingInProgress: true,
            handleUnlink: _noop,
            rowData,
            headerDetails,
            recommendationModalVisiblityMode,
            editModalRecommendationId,
            handleVisibilityChange,
            conditionUserValue,
          })
        )}
        {_map(inspectionJobIds, renderJobItem({ handleUnlink, rowData, isDisabled, compositeInspectionKey }))}
        {isRecommendationRequired && !isDisabled && (
          <div className={styles.buttonWrapper}>
            <ButtonWithKebabMenu
              action={BUTTON_ACTIONS.ADD_RECOMMENDATION_ACTION}
              onAction={handleRecommendationButtonClick({ handleVisibilityChange, handleLinkVisibilityChange })}
              containerClassName={styles.button}
              label={_isEmpty(inspectionRecommendationIds) ? __('Add Recommendation') : __('Add More')}
              allowLinkingEntity={allowLinkingJobs || allowLinkingRecommendations}
            />
            {renderRecommendationModal({
              mode: RECOMMENDATION_POPUP_MODES.ADD_RECOMMENDATION_MODE,
              recommendation: undefined,
              isVisible: recommendationModalVisiblityMode === RECOMMENDATION_POPUP_MODES.ADD_RECOMMENDATION_MODE,
              headerDetails,
              rowData,
              recentHistoryRecommendation,
              handleRecommendationSubmit,
              handleVisibilityChange,
              conditionUserValue,
              handleOpenMediaDrawer,
              handleRecommendationIntermediateSubmit,
            })}
            {renderLinkMpviEntity({
              rowData,
              headerDetails,
              isRecommendationLinkVisible,
              handleLinkVisibilityChange,
              handleLinkRecommendation,
              handleUnlinkRecommendation,
              allowLinkingJobs,
              allowLinkingRecommendations,
            })}
          </div>
        )}
      </div>
    );
  };

  const renderSelectedCondition = ({
    recentHistoryRecommendation,
    handleVisibilityChange,
    onSubmit,
    inspectionId,
    selectedValue,
    value,
    isAddRecomendationVisible,
    renderPopoverWithConditionIcon,
    rowData,
    headerDetails,
    handleOpenMediaDrawer,
    onIntermediateSubmit,
  }) => {
    if (value !== selectedValue || !isAddRecomendationVisible) return renderPopoverWithConditionIcon();
    return (
      <AddRecommendation
        roDetails={roDetails}
        vin={ROReader.getVin(roDetails)}
        inspectionId={inspectionId}
        mediaCount={MpviFormReader.mediaCount(rowData)}
        renderTitle={renderRecommendationModalTitle({ rowData, headerDetails })}
        mpviInstanceId={MpviFormReader.id(currentForm)}
        severity={CONDITION_VALUE_VS_SEVERITY[selectedValue]}
        visible={isAddRecomendationVisible}
        prevRecommendationDate={recentHistoryRecommendation.createdAt}
        historyRecommendations={recentHistoryRecommendation.recommendations}
        onSubmit={onSubmit}
        onIntermediateSubmit={onIntermediateSubmit}
        onCancel={handleVisibilityChange}
        popOverProps={{
          onVisibleChange: handleVisibilityChange,
          placement: 'leftBottom',
          arrowPointAtCenter: true,
        }}
        validatePreRecommendationAdditionRules={validatePreRecommendationAdditionRules}
        inspectionJobType={inspectionJobType}
        payTypeConfigurations={payTypeConfigurations}
        payerPayTypeConfiguration={payerPayTypeConfiguration}
        defaultSubPayType={defaultSubPayType}
        handleOpenMediaDrawer={handleOpenMediaDrawer}
        attributeInfos={MpviFormReader.attributes(rowData)}
        inspectionItemsById={inspectionItemsById}
        inspectionItemParentId={MpviFormReader.parentId(rowData)}>
        {renderPopoverWithConditionIcon()}
      </AddRecommendation>
    );
  };

  const renderMpviInspectionForm = () => (
    <MpviInspectionForm
      {...restProps}
      shouldFillOBDValues={shouldFillOBDValues}
      inspectionItemsById={inspectionItemsById}
      settings={settings}
      formId={idVersion.formId}
      mpviInstanceId={MpviFormReader.id(currentForm)}
      isDisabled={
        !Constraints.isMpviEditable({
          jobStatus: getStatus(jobDetails[MpviFormReader.assetId(currentForm)]),
          roStatus: ROReader.getROStatus(roDetails),
        }) || !hasPermissionToEditMpviForm()
      }
      viewPending={viewPending}
      formSharedtoCustomer={formSharedtoCustomer}
      isCompleted={MpviFormReader.status(currentForm) === MPVI_STATUS_VALUES.SUBMITTED}
      updateMpviFormDetails={updateMpviFormDetailsAction}
      fetchOBDData={fetchOBDDataAction}
      recommendationByIds={recommendationByIds}
      intermediateRecommendationByIds={intermediateRecommendationByIds}
      isAddRecommendationInProgress={isAddRecommendationInProgress}
      roDetails={roDetails}
      jobDetails={jobDetails}
      isUpdateInProgress={currentForm.isUpdateInProgress}
      inspectionHistoryValues={inspectionHistoryValues}
      recommendationsByInspectionId={recommendationsByInspectionId}
      jobsByInspectionId={jobsByInspectionId}
      inspectionFirstSections={inspectionAllFirstSections}
      inspectionSection={inspectionAllSection}
      allVisibleFieldIds={allVisibleFieldIds}
      renderRecommendationWithAddItem={renderRecommendationWithAddItem}
      renderHistoryRecommendation={renderHistoryRecommendation}
      renderSelectedCondition={renderSelectedCondition}
      handleRecommendationButtonClick={handleRecommendationButtonClick}
      renderRecommendation={renderRecommendation}
      renderMpviRecommendationInfo={renderMpviRecommendationInfo}
      renderNotes={renderNotes}
      renderMediaAttachmentIcon={renderMediaAttachmentIcon}
      conditionConfig={CONDITION_VALUE_CONFIG}
      recommendationRequiredConditionSet={recommendationRequiredConditionSet}
      localeOptions={localeOptions}
      inspectionJobType={inspectionJobType}
      setFooterMediaCount={setFooterMediaCount}
      footerMediaCount={footerMediaCount}
    />
  );

  return isLoading ? renderLoader() : renderMpviInspectionForm();
};

MpviInspectionFormContainer.propTypes = {
  fetchMpviDetails: PropTypes.func,
  roId: PropTypes.string,
  mpviDetails: PropTypes.array,
};

MpviInspectionFormContainer.defaultProps = {
  fetchMpviDetails: _noop,
  roId: '',
  mpviDetails: EMPTY_ARRAY,
};

const mapDispatchToProps = dispatch => ({
  ...bindActionCreators(
    {
      updateMpviFormDetails,
      fetchOBDData,
      removeRecommendation,
      unlinkMpviRecommendation,
      submitMpviFormDetails,
      fetchInspectionsMediaCount,
      unLinkMpviJob,
      updateMilesInOut,
      forceRefreshRO,
      updateValidateJobCompleteFlag,
    },
    dispatch
  ),
  dispatch,
});

export default connect(null, mapDispatchToProps)(MpviInspectionFormContainer);
