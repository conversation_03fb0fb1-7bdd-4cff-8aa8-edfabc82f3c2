import _isEmpty from 'lodash/isEmpty';
import _isFunction from 'lodash/isFunction';
import _get from 'lodash/get';
import _map from 'lodash/map';
import _pick from 'lodash/pick';
import _includes from 'lodash/includes';
import _head from 'lodash/head';
import _compact from 'lodash/compact';
import _filter from 'lodash/filter';
import _drop from 'lodash/drop';
import _noop from 'lodash/noop';

import { toaster } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import { fetchTireOpcodes } from 'twidgets/services/tireStorage';
import { printTransactionReceipts } from 'tcomponents/actions/TransactionActions';
import QuoteReader from 'tbusiness/appServices/service/readers/Quote';
import { isValidCashieringStatus, isValidPayTypeStatus } from 'tbusiness/appServices/service/helpers/general';
import DEALER_PROPERTY from 'tbase/constants/dealerProperties';
import { PAY_TYPE_VALUES } from 'tbase/constants/payTypes';
import { STATUS } from 'tbase/constants/statuses';
import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import { STATUS_ACTIONS } from 'tbusiness/appServices/service/constants/quote';
import { TIRE_OPCODE_PAYLOAD } from 'twidgets/constants/tireStorage';

import ROReader from 'readers/RepairOrder.reader';
import { getErrorMessage, getSerialNumber } from 'utils';
import RepairOrderEnv from 'utils/repairOrderEnv';
import { canCloseWPRO, canCloseIPRO, canCloseCPRO, canClosePayers } from 'validators/repairOrder';
import ROConstraints from 'helpers/constraints';
import { getROPayTypeStatuses, shouldShowJournalEntryErrorOnRoClose } from 'helpers/repairOrder.helper';
import { getClosePayersParams, isPostingAtBasePayType } from 'helpers/payer.helper';
import { INTERNAL_JOB_PENDING_REVIEW_STATUS_MESSAGE } from 'constants/common.constants';
import {
  NOTIFICATION_TYPE,
  RO_STATUS_ACTION,
  WARRANTY_PAY_RO_STATUS_VALUE,
  INTERNAL_PAY_RO_STATUS_VALUE,
  CASHIERING_PAID_DUE_AMOUNT_STATUSES_SET,
} from 'utils/constants';
import { MODULE_TYPES } from 'organisms/ROForm/ROForm.constants';

import styles from 'pages/ROPdfViewer/roPdfViewer.module.scss';

import {
  getHeaderActionTypeToUpdate,
  getUpdatedHeaderActionTypeWithDropRight,
} from '../HeaderNavigation/HeaderMenu/HeaderMenu.helper';
import { getQuoteStatusUpdateParams } from '../../../Quotes/QuoteDetails/Components/QuoteHeader/Components/QuoteRightSection/QuoteRightSection.helper';
import {
  getCanCloseReviewedIpRO,
  canCloseSelectedPayTypes,
  getBulkUpdateROPayTypeStatusPayload,
  getPayersToClosedByPayType,
  getBulkUpdateParamsToCloseBasePayType,
  getPayTypesToClose,
} from './RightSection.helper';
import { BULK_ACTION_VS_SAVE_PARAMS_GENERATOR } from './Components/ROBulkActionsScreen/ROBulkActionsScreen.helper';
import ACTION_TYPE_VS_ACTION_HANDLERS, {
  handlePayTypeCloseError,
  closeCPRO,
  closeIPRO,
  closeWPRO,
  handleTogglePostingError,
  obdReadingActionHandler,
  getCloseErrorMessage,
} from '../HeaderNavigation/HeaderMenu/HeaderMenu.actionHandlers';
import QUOTES_ACTION_HANDLERS from '../../../Quotes/QuoteDetails/Components/QuoteHeader/Components/QuoteRightSection/QuoteRightSection.actionHandler';
import { KEY_VS_ROUTE } from '../../../Quotes/QuoteDetails/Components/QuoteHeader/Components/QuoteRightSection/QuoteRightSection.constants';
import { ACTION_TYPE_KEYS } from '../HeaderNavigation/HeaderMenu/HeaderMenu.constants';
import { BULK_ACTIONS } from './Components/ROBulkActionsScreen/ROBulkActionsScreen.constants';
import { RIGHT_SECTION_ACTION_TYPES } from './RightSection.actionTypes';

const handleBulkPayTypeClose = ({ params, getState, setState }) => {
  const { bulkUpdateParams, payTypesToClose: payTypes } = params;
  const {
    handleBulkPayTypeClose: handleBulkPayTypeCloseAction,
    roDetails,
    redirectUrlAfterClose,
    navigate,
  } = getState();
  const { roId } = ROReader.getId(roDetails);
  const { hasWarrantyOEMSubmission } = RepairOrderEnv.settingsBySiteId[ROReader.getSiteId(roDetails)] || EMPTY_OBJECT;
  const payTypesToClose =
    payTypes ||
    (hasWarrantyOEMSubmission
      ? [PAY_TYPE_VALUES.CUSTOMER_PAY, PAY_TYPE_VALUES.INTERNAL, PAY_TYPE_VALUES.WARRANTY]
      : [PAY_TYPE_VALUES.CUSTOMER_PAY, PAY_TYPE_VALUES.INTERNAL]);
  return handleBulkPayTypeCloseAction(roId, bulkUpdateParams)
    .then(() => {
      toaster(NOTIFICATION_TYPE.SUCCESS, __('Successfully closed $$(RO)'));
      if (redirectUrlAfterClose) navigate(redirectUrlAfterClose);
    })
    .catch(
      handlePayTypeCloseError({
        params: { payTypeLabel: '', payTypesToClose },
        getState,
        setState,
      })
    );
};

const handleBulkPayTypeCloseForBasePayType = ({ params, getState, setState }) => {
  const { payersToBeClosed, payTypesToClose } = params;
  const {
    payTypeConfigurations,
    roDetails,
    jobDetails,
    handlCloseInvoiceV3 = _noop,
    redirectUrlAfterClose,
    navigate,
  } = getState();
  if (
    !canClosePayers({
      payTypeConfigurations,
      payersToBeClosed,
      invoices: ROReader.getInvoicesForMultiPayer(roDetails),
      ipStatus: ROReader.getIpStatus(roDetails),
      jobDetails,
    })
  )
    return null;

  const payload = getBulkUpdateParamsToCloseBasePayType(
    payTypesToClose,
    !shouldShowJournalEntryErrorOnRoClose(ROReader.getSiteId(roDetails))
  );

  return handlCloseInvoiceV3(ROReader.getROId(roDetails), payload)
    .then(() => {
      toaster(NOTIFICATION_TYPE.SUCCESS, __('Successfully closed $$(RO)'));
      if (redirectUrlAfterClose) navigate(redirectUrlAfterClose);
    })
    .catch(
      handlePayTypeCloseError({
        params: { payTypeLabel: '', payTypesToClose },
        getState,
        setState,
      })
    );
};

const closeRO = ({ getState, setState }) => {
  const { roDetails, jobDetails, costCentersByPayType, payTypeConfigurations } = getState();
  const { hasWarrantyOEMSubmission } = RepairOrderEnv.settingsBySiteId[ROReader.getSiteId(roDetails)] || EMPTY_OBJECT;
  const shouldShowJournalEntryErrorOnROClose = shouldShowJournalEntryErrorOnRoClose(ROReader.getSiteId(roDetails));
  const {
    roInternalPayStatus: ipStatus,
    roWarrantyPayStatus: wpStatus,
    cashieringStatus,
    roCustomerPayStatus: cpStatus,
  } = getROPayTypeStatuses(roDetails);
  const payTypesToClose = getPayTypesToClose({ ipStatus, cpStatus, wpStatus, cashieringStatus });
  if (ROConstraints.isServiceV3Enabled() && isPostingAtBasePayType(ROReader.postingSettings(roDetails))) {
    if (
      _includes(payTypesToClose, PAY_TYPE_VALUES.CUSTOMER_PAY) &&
      !CASHIERING_PAID_DUE_AMOUNT_STATUSES_SET.has(cashieringStatus)
    ) {
      toaster(NOTIFICATION_TYPE.ERROR, __('There is a pending due amount'));
      return Promise.resolve();
    }
    if (_isEmpty(payTypesToClose)) {
      toaster(NOTIFICATION_TYPE.ERROR, __("There aren't any jobs to close or submit"));
      return Promise.resolve();
    }
    handleBulkPayTypeCloseForBasePayType({
      params: {
        payersToBeClosed: getPayersToClosedByPayType({
          payTypesToClose,
          payers: ROReader.getPayers(roDetails),
          payTypeConfigurations,
        }),
        payTypesToClose,
      },
      getState,
      setState,
    });
    return Promise.resolve();
  }

  if (
    !canCloseCPRO(roDetails) ||
    !canCloseIPRO(roDetails, jobDetails, costCentersByPayType) ||
    (hasWarrantyOEMSubmission && !canCloseWPRO(roDetails, jobDetails, costCentersByPayType))
  )
    return Promise.reject();
  const canCloseReviewedIpRO = getCanCloseReviewedIpRO(ipStatus);
  const [wpStatusToSet, canForceCloseWp] = [
    hasWarrantyOEMSubmission ? WARRANTY_PAY_RO_STATUS_VALUE.CLOSED : WARRANTY_PAY_RO_STATUS_VALUE.SUBMITTED,
    hasWarrantyOEMSubmission ? !shouldShowJournalEntryErrorOnROClose : true,
  ];
  const cpBulkUpdateParam = isValidCashieringStatus(cashieringStatus)
    ? getBulkUpdateROPayTypeStatusPayload(
        PAY_TYPE_VALUES.CUSTOMER_PAY,
        STATUS.CLOSED,
        !shouldShowJournalEntryErrorOnROClose
      )
    : undefined;
  const ipBulkUpdateParam =
    isValidPayTypeStatus(ipStatus) && ipStatus !== INTERNAL_PAY_RO_STATUS_VALUE.CLOSED && canCloseReviewedIpRO
      ? getBulkUpdateROPayTypeStatusPayload(
          PAY_TYPE_VALUES.INTERNAL,
          STATUS.CLOSED,
          !shouldShowJournalEntryErrorOnROClose
        )
      : undefined;
  const wpBulkUpdateParam =
    isValidPayTypeStatus(wpStatus) &&
    wpStatus !== WARRANTY_PAY_RO_STATUS_VALUE.CLOSED &&
    wpStatus !== WARRANTY_PAY_RO_STATUS_VALUE.SUBMITTED
      ? getBulkUpdateROPayTypeStatusPayload(PAY_TYPE_VALUES.WARRANTY, wpStatusToSet, canForceCloseWp)
      : undefined;
  const bulkUpdateParams = _compact([cpBulkUpdateParam, ipBulkUpdateParam, wpBulkUpdateParam]);
  if (_isEmpty(bulkUpdateParams)) {
    const defaultMessage = !canCloseReviewedIpRO
      ? INTERNAL_JOB_PENDING_REVIEW_STATUS_MESSAGE
      : __("There aren't any jobs to close or submit");
    toaster(NOTIFICATION_TYPE.ERROR, defaultMessage);
    return Promise.reject();
  }
  return handleBulkPayTypeClose({
    params: { bulkUpdateParams },
    getState,
    setState,
  });
};

const handleCreateAppointmentOrWalkInFromQuote = ({ getState }) => {
  const { roDetails, headerActionTypes } = getState();
  const quoteId = QuoteReader.getId(roDetails).roId;
  const redirectUrl = KEY_VS_ROUTE[_head(headerActionTypes)]({ withMicroServiceApp: true, mode: 'create' });
  window.location.href = `${window.location.origin}${redirectUrl}?quoteId=${quoteId}`;
  return Promise.resolve();
};

const CONFIRMATION_DIALOG_SUBMIT_VS_FUNCTION = {
  [ACTION_TYPE_KEYS.RO_CLOSE]: closeRO,
  [ACTION_TYPE_KEYS.CREATE_APPOINTMENT]: handleCreateAppointmentOrWalkInFromQuote,
  [ACTION_TYPE_KEYS.CREATE_WALK_IN]: handleCreateAppointmentOrWalkInFromQuote,
  DEFAULT: () => Promise.resolve(),
};

const CLOSE_RO_VS_PAY_TYPE_ACTION = {
  [PAY_TYPE_VALUES.CUSTOMER_PAY]: closeCPRO,
  [PAY_TYPE_VALUES.INTERNAL]: closeIPRO,
  [PAY_TYPE_VALUES.WARRANTY]: closeWPRO,
  DEFAULT: () => Promise.reject(),
};

const handleRefreshRODetails = getState => async () => {
  const { onRefreshRODetails, forceRefreshRO } = getState();
  await onRefreshRODetails();
  forceRefreshRO(true);
};

const handleQuoteReOpenSubmit = ({ params, getState }) => {
  const { roId, notes } = params;
  const { updateQuoteBulkStatus } = getState();
  updateQuoteBulkStatus(roId, getQuoteStatusUpdateParams(roId, STATUS_ACTIONS.REOPEN, { reason: notes }))
    .then(() => {
      toaster(NOTIFICATION_TYPE.SUCCESS, __('Successfully Reopened Quote'));
    })
    .catch(err => {
      toaster(NOTIFICATION_TYPE.ERROR, getErrorMessage(err, __('Failed to Reopen Quote')));
    });
};

const handleROReOpenSubmit = ({ params, getState }) => {
  const { roId, notes } = params;
  const { reOpenClosedRO, updateROStatus, headerActionTypes } = getState();
  const reOpenROPromise =
    _head(headerActionTypes) === ACTION_TYPE_KEYS.REOPEN_RO
      ? updateROStatus(roId, { reason: notes, action: RO_STATUS_ACTION.REOPEN })
      : reOpenClosedRO(roId, notes);
  return reOpenROPromise.catch(err => {
    toaster(NOTIFICATION_TYPE.ERROR, getErrorMessage(err, __('Failed to Reopen $$(RO)')));
  });
};

const MODULE_TYPE_VS_RE_OPEN_SUBMIT_ACTION = {
  [MODULE_TYPES.RO]: handleROReOpenSubmit,
  [MODULE_TYPES.QUOTE]: handleQuoteReOpenSubmit,
};

const MODULE_TYPE_VS_FEE_UPDATE_ACTION = {
  [MODULE_TYPES.RO]: ({ getState }) => getState().applyROLevelFees,
  [MODULE_TYPES.QUOTE]: ({ getState }) => getState().applyQuoteLevelFees,
  [MODULE_TYPES.PRE_RO]: ({ getState }) => getState().applyPreROLevelFees,
  [MODULE_TYPES.BDC]: ({ getState }) => getState().applyAppointmentLevelFees,
};

const handleJobBulkStatusChange =
  ({ bulkAction, successMessage, errorMessage = __('Failed to Bulk Update Job') }) =>
  ({ getState, setState }) => {
    const { jobDetails, roDetails, updateBulkJobStatus, bulkJobCompleteIds, headerActionTypes } = getState();
    const selectedJobsForCompletion = {
      checkboxJobOperationTable: _map(
        _pick(jobDetails, bulkJobCompleteIds),
        job => ({ ...job, selected: true }),
        job => ({ ...job, serialNumber: getSerialNumber(job) })
      ),
    };
    const params = BULK_ACTION_VS_SAVE_PARAMS_GENERATOR[bulkAction]({
      value: selectedJobsForCompletion,
    });
    return updateBulkJobStatus(ROReader.getROId(roDetails), params, roDetails)
      .then(() => {
        toaster(NOTIFICATION_TYPE.INFO, successMessage);
      })
      .catch(error => {
        toaster(NOTIFICATION_TYPE.ERROR, getErrorMessage(error, errorMessage));
      })
      .finally(() => {
        setState({
          headerActionTypes: getUpdatedHeaderActionTypeWithDropRight(headerActionTypes),
          bulkJobCompleteIds: EMPTY_ARRAY,
        });
      });
  };

export default {
  [RIGHT_SECTION_ACTION_TYPES.RIGHT_SECTION_INIT]: ({ getState, setState }) => {
    const { roDetails, fetchROMediaCount, fetchApprovalCount } = getState();
    if (!_isFunction(fetchROMediaCount)) return;
    const roId = ROReader.getROId(roDetails);
    fetchROMediaCount(roId);
    if (ROConstraints.isServiceApprovalFlowV2Enabled()) {
      fetchApprovalCount(roId);
    }
    if (RepairOrderEnv.dealerProperty[DEALER_PROPERTY.TIRE_STORAGE_ENABLED]) {
      fetchTireOpcodes(TIRE_OPCODE_PAYLOAD).then(opcodes => {
        setState({ tireOpcodes: opcodes?.hits });
      });
    }
  },
  [RIGHT_SECTION_ACTION_TYPES.MENU_ACTION_CLICK]: ({ params, getState, setState }) => {
    const { actionType } = params;
    const { headerActionTypes: headerActionTypesInState } = getState();
    const funcToExec = ACTION_TYPE_VS_ACTION_HANDLERS[actionType];
    if (_isFunction(funcToExec)) {
      funcToExec({ params, getState, setState });
      return;
    }
    setState({
      headerActionTypes: getHeaderActionTypeToUpdate(headerActionTypesInState, actionType),
    });
  },
  [RIGHT_SECTION_ACTION_TYPES.UPDATE_FEES_ACTION]: ({ params, setState, getState }) => {
    const { roDetails, headerActionTypes, moduleType } = getState();
    setState(
      {
        isROLevelFeeModalPrimaryLoading: true,
      },
      () => {
        const funcToExec = MODULE_TYPE_VS_FEE_UPDATE_ACTION[moduleType]({ getState });
        funcToExec(ROReader.getId(roDetails).roId, params)
          .then(() => {
            setState({
              headerActionTypes: getUpdatedHeaderActionTypeWithDropRight(headerActionTypes),
              isROLevelFeeModalPrimaryLoading: false,
            });
          })
          .catch(err => {
            toaster(NOTIFICATION_TYPE.ERROR, getErrorMessage(err, __('Something went wrong')));
            setState({
              isROLevelFeeModalPrimaryLoading: false,
            });
          });
      }
    );
  },
  [RIGHT_SECTION_ACTION_TYPES.UPDATE_SERVICE_MODE_ACTION]: ({ params, getState, setState }) => {
    const { roDetails, updateServiceMode, headerActionTypes } = getState();
    const { newServiceMode } = params;
    setState({ headerActionTypes: getUpdatedHeaderActionTypeWithDropRight(headerActionTypes) });
    updateServiceMode(ROReader.getROId(roDetails), newServiceMode);
  },
  [RIGHT_SECTION_ACTION_TYPES.TOGGLE_OBD_READING]: obdReadingActionHandler,
  [RIGHT_SECTION_ACTION_TYPES.UPDATE_OBD_CHECKIN]: ({ params, getState }) => {
    const { updateOBDCheckinId, roDetails } = getState();
    const obdCheckId = _get(params, 'payload.checkinId');
    updateOBDCheckinId(ROReader.getROId(roDetails), obdCheckId)
      .then(() => toaster(NOTIFICATION_TYPE.SUCCESS, __('Successfully updated OBD Checkin ID')))
      .catch(err => toaster(NOTIFICATION_TYPE.ERROR, getErrorMessage(err, 'Failed to update OBD Checkin ID')));
  },
  [RIGHT_SECTION_ACTION_TYPES.HANDLE_PRE_JOB_COMPLETE_VALIDATIONS]: ({ params, getState, setState }) => {
    const { headerActionTypes: headerActionTypesInState } = getState();
    const { jobIds } = params;
    setState({
      bulkJobCompleteIds: jobIds,
      headerActionTypes: getHeaderActionTypeToUpdate(
        headerActionTypesInState,
        ACTION_TYPE_KEYS.PRE_JOB_COMPLETE_VALIDATIONS
      ),
    });
  },
  [RIGHT_SECTION_ACTION_TYPES.HANDLE_PRE_TECH_FINISHED_VALIDATIONS]: ({ params, getState, setState }) => {
    const { headerActionTypes: headerActionTypesInState } = getState();
    const { jobIds } = params;
    setState({
      bulkJobCompleteIds: jobIds,
      headerActionTypes: getHeaderActionTypeToUpdate(
        headerActionTypesInState,
        ACTION_TYPE_KEYS.PRE_TECH_FINISHED_COMPLETE_VALIDATIONS
      ),
    });
  },
  [RIGHT_SECTION_ACTION_TYPES.HANDLE_PRE_JOB_COMPLETE_VALIDATIONS_CLOSE]: ({ getState, setState }) => {
    const { headerActionTypes } = getState();
    setState({
      bulkJobCompleteIds: EMPTY_ARRAY,
      headerActionTypes: getUpdatedHeaderActionTypeWithDropRight(headerActionTypes),
    });
  },
  [RIGHT_SECTION_ACTION_TYPES.HANDLE_MARK_JOB_COMPLETE]: handleJobBulkStatusChange({
    bulkAction: BULK_ACTIONS.MARK_COMPLETE,
    successMessage: __('Marked selected jobs as complete'),
  }),
  [RIGHT_SECTION_ACTION_TYPES.HANDLE_MARK_JOB_TECH_FINISHED]: handleJobBulkStatusChange({
    bulkAction: BULK_ACTIONS.TECH_FINISHED,
    successMessage: __('Marked selected jobs as tech finished'),
  }),
  [RIGHT_SECTION_ACTION_TYPES.HANDLE_RE_OPEN_CLOSE_PAYER]: ({ params, getState, setState }) => {
    const { hasPostingError } = params;
    const { headerActionTypes } = getState();
    const updatedHeaderActionTypes = getUpdatedHeaderActionTypeWithDropRight(headerActionTypes);
    setState({
      headerActionTypes: updatedHeaderActionTypes,
    });
    if (hasPostingError) handleTogglePostingError({ params, getState, setState });
  },
  [RIGHT_SECTION_ACTION_TYPES.RESET_MENU_ACTION_CLICK]: ({ getState, setState }) => {
    const { headerActionTypes } = getState();
    const updatedHeaderActionTypes = getUpdatedHeaderActionTypeWithDropRight(headerActionTypes);
    setState({
      headerActionTypes: updatedHeaderActionTypes,
    });
  },
  [RIGHT_SECTION_ACTION_TYPES.TOGGLE_POSTING_ERROR]: handleTogglePostingError,
  [RIGHT_SECTION_ACTION_TYPES.HANDLE_RE_OPEN_SUBMIT]: ({ params, getState }) => {
    const { moduleType } = getState();
    const funcToExec = MODULE_TYPE_VS_RE_OPEN_SUBMIT_ACTION[moduleType];
    return funcToExec({ params, getState });
  },
  [RIGHT_SECTION_ACTION_TYPES.HANDLE_HOLD_SUBMIT]: ({ getState, setState }) => {
    const { roDetails, getRODetails, headerActionTypes } = getState();
    const updatedHeaderActionTypes = getUpdatedHeaderActionTypeWithDropRight(headerActionTypes);
    setState({
      headerActionTypes: updatedHeaderActionTypes,
    });
    getRODetails(ROReader.getId(roDetails).roId, {
      isBackgroundFetch: true,
      shouldFetchRecommendations: false,
      isForcedPartFeatureEnabled: ROConstraints.isForcedPartFeatureEnabled(),
    });
  },
  [RIGHT_SECTION_ACTION_TYPES.HANDLE_CLOSE_RO_SUBMIT]: ({ params, getState, setState }) => {
    const { payTypesToClose } = params;
    const { roDetails, jobDetails, costCentersByPayType, payTypeConfigurations } = getState();
    if (ROConstraints.isServiceV3Enabled() && isPostingAtBasePayType(ROReader.postingSettings(roDetails))) {
      handleBulkPayTypeCloseForBasePayType({
        params: {
          payersToBeClosed: getPayersToClosedByPayType({
            payTypesToClose,
            payers: ROReader.getPayers(roDetails),
            payTypeConfigurations,
          }),
          payTypesToClose,
        },
        getState,
        setState,
      });
      return Promise.resolve();
    }
    const { roInternalPayStatus: ipStatus } = getROPayTypeStatuses(roDetails);
    if (_includes(payTypesToClose, PAY_TYPE_VALUES.INTERNAL) && !getCanCloseReviewedIpRO(ipStatus)) {
      toaster(NOTIFICATION_TYPE.ERROR, INTERNAL_JOB_PENDING_REVIEW_STATUS_MESSAGE);
      return Promise.resolve();
    }
    if (!canCloseSelectedPayTypes(payTypesToClose, roDetails, jobDetails, costCentersByPayType))
      return Promise.resolve();
    const bulkUpdateParams = _map(payTypesToClose, payType =>
      getBulkUpdateROPayTypeStatusPayload(
        payType,
        STATUS.CLOSED,
        !shouldShowJournalEntryErrorOnRoClose(ROReader.getSiteId(roDetails))
      )
    );
    return handleBulkPayTypeClose({
      params: { bulkUpdateParams, payTypesToClose },
      getState,
      setState,
    });
  },
  [RIGHT_SECTION_ACTION_TYPES.HANDLE_CONFIRMATION_DIALOG_SUBMIT]: ({ getState, setState }) => {
    const { headerActionTypes } = getState();
    setState({ isConfirmationDialogLoading: true });
    const updatedHeaderActionTypes = getUpdatedHeaderActionTypeWithDropRight(headerActionTypes);
    const funcToExec =
      CONFIRMATION_DIALOG_SUBMIT_VS_FUNCTION[_head(headerActionTypes)] ||
      CONFIRMATION_DIALOG_SUBMIT_VS_FUNCTION.DEFAULT;
    funcToExec({ getState, setState })
      .then(({ headerActionTypes: headerActionTypesToUpdate }) => {
        if (_includes(headerActionTypesToUpdate, ACTION_TYPE_KEYS.RO_CLOSE)) {
          setState({
            headerActionTypes: _drop(headerActionTypesToUpdate) || updatedHeaderActionTypes,
            isConfirmationDialogLoading: false,
          });
          return;
        }
        setState({
          headerActionTypes: _drop(headerActionTypesToUpdate) || updatedHeaderActionTypes,
          isConfirmationDialogLoading: false,
        });
      })
      .catch(() => {
        setState({
          headerActionTypes: updatedHeaderActionTypes,
          isConfirmationDialogLoading: false,
        });
      });
  },
  [RIGHT_SECTION_ACTION_TYPES.HANDLE_FORCE_CLOSE_RO]: ({ params, getState, setState }) => {
    const { roId, payTypes, payerIds } = params;
    const { handleBulkPayTypeClose: handleBulkPayTypeCloseAction, roDetails, handleBulkPayersClose } = getState();
    setState({ isForceClosingROPayType: true });
    const [onForceCloseROAction, forceCloseROPayload] = ROConstraints.isServiceV3Enabled()
      ? [handleBulkPayersClose, getClosePayersParams(payTypes, payerIds, ROReader.postingSettings(roDetails))]
      : [
          handleBulkPayTypeCloseAction,
          _map(payTypes, payType => getBulkUpdateROPayTypeStatusPayload(payType, STATUS.CLOSED, true)),
        ];
    onForceCloseROAction(roId, forceCloseROPayload, { shouldFetchRO: true })
      .then(() => {
        const { headerActionTypes } = getState();
        const updatedHeaderActionTypes = getUpdatedHeaderActionTypeWithDropRight(headerActionTypes);
        setState({
          headerActionTypes: updatedHeaderActionTypes,
          isForceClosingROPayType: false,
        });
        const successMessage = ROConstraints.isServiceV3Enabled()
          ? __('Successfully Closed Payers')
          : __('Successfully Closed $$(RO)');
        toaster(NOTIFICATION_TYPE.SUCCESS, successMessage);
      })
      .catch(err => {
        getCloseErrorMessage(err, roDetails).then(message => toaster(NOTIFICATION_TYPE.ERROR, message));
        setState({ isForceClosingROPayType: false });
      });
  },
  [RIGHT_SECTION_ACTION_TYPES.HANDLE_CLOSE_RO]: ({ getState, setState }) => {
    const { closeType } = getState();
    const closeROActionByPayType = CLOSE_RO_VS_PAY_TYPE_ACTION[closeType] || CLOSE_RO_VS_PAY_TYPE_ACTION.DEFAULT;
    return closeROActionByPayType({ getState, setState });
  },
  [RIGHT_SECTION_ACTION_TYPES.HANDLE_PRINT_ALL_DOCS]: ({ getState }) => {
    const { roDetails } = getState();
    toaster(NOTIFICATION_TYPE.INFO, __('Please give us a moment triggering print'));
    printTransactionReceipts(ROReader.getROId(roDetails))
      .then(pdfPrintResponse => {
        const failedPdfs = _filter(pdfPrintResponse, ['resolved', false]);
        const failedDocuments = _map(failedPdfs, ({ receiptNumber }, index) => `${index + 1}. ${receiptNumber}`);
        if (_isEmpty(failedDocuments)) return toaster(NOTIFICATION_TYPE.SUCCESS, __('Successfully Triggered printing'));
        return toaster(
          NOTIFICATION_TYPE.ERROR,
          __('Failed to print following receipts\n{{documents}}', { documents: failedDocuments.join('\n') }),
          undefined,
          undefined,
          undefined,
          styles.errorMsg
        );
      })
      .catch(err => {
        toaster('error', getErrorMessage(err) || __('Failed to print payment receipts'));
      });
  },
  [RIGHT_SECTION_ACTION_TYPES.HANDLE_REFRESH_ICON_CLICK]: ({ getState }) => {
    const { onFormChange, hasFormChanged } = getState();
    const refreshRODetails = handleRefreshRODetails(getState);
    if (hasFormChanged) {
      onFormChange(true, refreshRODetails);
      return;
    }
    refreshRODetails();
  },
  ...QUOTES_ACTION_HANDLERS,
};
