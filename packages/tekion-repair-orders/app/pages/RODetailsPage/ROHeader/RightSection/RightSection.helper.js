import _pick from 'lodash/pick';
import _isNil from 'lodash/isNil';
import _get from 'lodash/get';
import _isFunction from 'lodash/isFunction';
import _flatten from 'lodash/flatten';
import _some from 'lodash/some';
import _filter from 'lodash/filter';
import _uniq from 'lodash/uniq';

import _map from 'lodash/map';
import _includes from 'lodash/includes';

import { CUSTOMER_PAY_RO_STATUS_VALUE, WARRANTY_PAY_RO_STATUS_VALUE } from 'tbase/constants/repairOrder/general';

import ROReader from 'readers/RepairOrder.reader';
import {
  hasPermissionToCloseInternalRO,
  hasPermissionToCloseWarrantyRO,
  hasPermissionToCloseCustomerPayRO,
  hasPermissionToVoidRO,
  hasPermissionToCloseReviewedInternalRO,
  hasPermissionToSubmitWarrantyRO,
  hasPermissionToReOpenRO,
} from 'permissions/roPermission';
import { canCloseCPRO, canCloseIPRO, canCloseWPRO } from 'validators/repairOrder';
import { getROPayTypeStatuses } from 'helpers/repairOrder.helper';
import Constraints from 'helpers/constraints';
import { isROOrJobOnHold } from 'twidgets/appServices/service/helpers/hold.helper';
import { isValidCashieringStatus, isValidPayTypeStatus } from 'tbusiness/appServices/service/helpers/general';
import { isValidActionByPayTypeStatus } from 'tbusiness/appServices/service/helpers/job';
import { getBasePayTypeForSubPayType } from 'tbusiness/appServices/service/helpers/payTypes.helpers';
import RepairOrderEnv from 'utils/repairOrderEnv';
import { PAY_TYPE_VALUES } from 'tbase/constants/payTypes';
import { EMPTY_OBJECT } from 'tbase/app.constants';
import { JOB_STATUS } from 'tbase/constants/repairOrder/constants';
import {
  INVOICED_CLOSED_PAYER_STATUS_SET,
  PAYER_STATUS,
  PAYER_STATUS_VALUES,
} from 'tbase/constants/repairOrder/payerStatus';
import { RO_CLOSE_LEVELS } from 'tbusiness/appServices/service/constants/payer.constants';

import { isPostingAtPayerLevel } from 'helpers/payer.helper';
import { isCPReadyToClose } from 'utils/helpers';
import { PAYERS_SELECTION_VIEW_TYPES } from 'constants/invoiceConfirmation.constants';
import { MODULE_TYPES } from 'tbusiness/appServices/service/constants/general';
import {
  INTERNAL_PAY_RO_STATUS_VALUE,
  RO_STATUS_VALUES,
  PAY_TYPE_VS_STATUSES_ELIGIBLE_TO_CLOSE_SET,
} from 'utils/constants';

import { isCloneQuoteAllowed, isVoidQuoteAllowed, isDeclineQuoteAllowed } from '../../../Quotes/Quote.constraints';
import {
  isApplyCouponDisabled,
  isQuoteLevelFeeDisabled,
  isVehicleUpdateDisabled,
  isReOpenQuoteDisabled,
  isQuoteBulkActionsDisabled,
  isViewQuotePdfDisabled,
} from '../../../Quotes/QuoteDetails/Components/QuoteHeader/Components/QuoteRightSection/QuoteRightSection.helper';
import { ACTION_TYPES as HEADER_ACTION_TYPES } from '../HeaderNavigation/HeaderMenu/HeaderMenu.constants';

export const HEADER_ACTION_TYPE_VS_CONFIRMATION_DIALOG_PROPS = {
  [HEADER_ACTION_TYPES.RO_CLOSE.key]: roDetails => ({
    content: _get(RepairOrderEnv.settingsBySiteId[ROReader.getSiteId(roDetails)], 'hasWarrantyOEMSubmission')
      ? __('Are you sure you want to close all pay types for $$(RO)')
      : __('Are you sure you want to close Customer & Internal pay types for $$(RO)'),
    title: __('Close $$(RO)'),
    submitBtnText: __('Yes'),
    showModal: true,
  }),
  DEFAULT: () => EMPTY_OBJECT,
};

export const isCloseCPRODisabled = (cashieringStatus, roDetails, jobDetails) =>
  !Constraints.isCloseROEnabled(cashieringStatus, ROReader.getROStatus(roDetails)) ||
  !isValidCashieringStatus(cashieringStatus) ||
  isROOrJobOnHold(roDetails, jobDetails);

const isViewPostingPreviewDisabled = (roDetails, payTypeStatuses) => {
  const { roCustomerPayStatus, roWarrantyPayStatus, roInternalPayStatus } = payTypeStatuses;
  return Constraints.isServiceV3Enabled()
    ? ROReader.getROStatus(roDetails) === RO_STATUS_VALUES.CLOSED
    : ROReader.getInvoice(roDetails) &&
        !(
          isValidPayTypeStatus(roCustomerPayStatus) &&
          isValidActionByPayTypeStatus(PAY_TYPE_VALUES.CUSTOMER_PAY, payTypeStatuses)
        ) &&
        !(
          isValidPayTypeStatus(roWarrantyPayStatus) &&
          isValidActionByPayTypeStatus(PAY_TYPE_VALUES.WARRANTY, payTypeStatuses)
        ) &&
        !(
          isValidPayTypeStatus(roInternalPayStatus) &&
          isValidActionByPayTypeStatus(PAY_TYPE_VALUES.INTERNAL, payTypeStatuses)
        );
};

const canCloseCP = (isServiceV3Enabled, roDetails, roCustomerPayStatus) => {
  if (!isServiceV3Enabled) return !_isNil(ROReader.cpAmounts(roDetails));
  return isCPReadyToClose(roCustomerPayStatus, ROReader.getCashieringStatus(roDetails));
};

const canCloseWP = (isServiceV3Enabled, roDetails, roWarrantyPayStatus) => {
  if (!isServiceV3Enabled) return !_isNil(ROReader.wpAmounts(roDetails));
  return roWarrantyPayStatus === WARRANTY_PAY_RO_STATUS_VALUE.INVOICED;
};

const canCloseIP = (isServiceV3Enabled, roDetails, roInternalPayStatus) => {
  if (!isServiceV3Enabled) return !_isNil(ROReader.ipAmounts(roDetails));
  return (
    roInternalPayStatus === INTERNAL_PAY_RO_STATUS_VALUE.INVOICED ||
    roInternalPayStatus === INTERNAL_PAY_RO_STATUS_VALUE.REVIEW_REQUESTED
  );
};

const getIsReopenPayerVisible = ({
  payerStatuses,
  isReopenPayerDisabled,
  isServiceV3Enabled,
  isPostingPayerLevel,
  roStatus,
}) => {
  const isValidPayerStatusToReopen = _some(
    [PAYER_STATUS_VALUES.INVOICED, PAYER_STATUS_VALUES.CLOSED, PAYER_STATUS_VALUES.PAID],
    status => payerStatuses.has(status)
  );

  if (isReopenPayerDisabled || !isServiceV3Enabled || !hasPermissionToReOpenRO() || !isPostingPayerLevel) {
    return !INVOICED_CLOSED_PAYER_STATUS_SET.has(roStatus) && isValidPayerStatusToReopen;
  }

  return isValidPayerStatusToReopen;
};

export const getROConstraints = (roDetails, props) => {
  const isServiceV3Enabled = Constraints.isServiceV3Enabled();
  const payTypeStatuses = getROPayTypeStatuses(roDetails);
  const { roCustomerPayStatus, roWarrantyPayStatus, roInternalPayStatus, cashieringStatus } = payTypeStatuses;
  const siteId = ROReader.getSiteId(roDetails);
  const payerStatuses = new Set(_map(ROReader.getPayers(roDetails), PAYER_STATUS));
  const isPostingPayerLevel = isPostingAtPayerLevel(ROReader.postingSettings(roDetails));
  const canCloseInternalRO =
    hasPermissionToCloseInternalRO() &&
    _get(RepairOrderEnv.settingsBySiteId[siteId], 'canCloseROInDetailsView') &&
    props.canCloseIPRO &&
    canCloseIP(isServiceV3Enabled, roDetails, roInternalPayStatus);
  const { vehicleDetails } = ROReader.getVehicleDetails(roDetails) || EMPTY_OBJECT;
  return {
    isROLevelFeeDisable: !Constraints.canAddROLevelFee(ROReader.getROStatus(roDetails)),
    isReOpenROMenuDisabled: props.isReOpenROMenuDisabled || Constraints.isReOpenROMenuDisabled(roDetails),
    isReOpenROAfterCloseDisabled:
      props.isReOpenROMenuDisabled ||
      Constraints.isReOpenROAfterCloseDisabled(ROReader.getROStatus(roDetails), payTypeStatuses),
    isApplyCouponDisabled: props.applyCouponDisabled,
    isROLevelFeeDisabled: props.feeDisabled,
    canCloseAllROTypes:
      props.canCloseAllROTypes &&
      _get(RepairOrderEnv.settingsBySiteId[siteId], 'canCloseROInDetailsView') &&
      hasPermissionToCloseCustomerPayRO() &&
      hasPermissionToCloseInternalRO() &&
      hasPermissionToCloseWarrantyRO(),
    isVoidDisabled:
      props.isVoidDisabled || !Constraints.canVoidRO(roDetails, props.jobDetails) || !hasPermissionToVoidRO(),
    isROMediaDrawerDisabled: Constraints.isROMediaDrawerDisabled(roDetails),
    isApplyCouponMenuDisabled: Constraints.isApplyCouponMenuDisabled(roDetails),
    canChangeWarrantyStatus:
      hasPermissionToSubmitWarrantyRO() &&
      !_get(RepairOrderEnv.settingsBySiteId[siteId], 'hasWarrantyOEMSubmission') &&
      props.showWarrantySubmitAction,
    canCloseCPRO:
      hasPermissionToCloseCustomerPayRO() &&
      _get(RepairOrderEnv.settingsBySiteId[siteId], 'canCloseROInDetailsView') &&
      props.canCloseCPRO &&
      canCloseCP(isServiceV3Enabled, roDetails, roCustomerPayStatus),
    canCloseIPRO:
      roInternalPayStatus === INTERNAL_PAY_RO_STATUS_VALUE.REVIEW_REQUESTED
        ? canCloseInternalRO && hasPermissionToCloseReviewedInternalRO()
        : canCloseInternalRO,
    canCloseWPRO:
      hasPermissionToCloseWarrantyRO() &&
      _get(RepairOrderEnv.settingsBySiteId[siteId], 'canCloseROInDetailsView') &&
      props.canCloseWPRO &&
      canCloseWP(isServiceV3Enabled, roDetails, roWarrantyPayStatus),
    isCloseCPRODisabled: isCloseCPRODisabled(cashieringStatus, roDetails, props.jobDetails),
    isCloseIPRODisabled:
      !Constraints.isCloseROEnabled(roInternalPayStatus, ROReader.getROStatus(roDetails)) ||
      isROOrJobOnHold(roDetails, props.jobDetails),
    isCloseWPRODisabled:
      !Constraints.isCloseROEnabled(roWarrantyPayStatus, ROReader.getROStatus(roDetails)) ||
      isROOrJobOnHold(roDetails, props.jobDetails),
    isChangeWarrantyStatusDisabled:
      Constraints.isChangeWarrantyStatusDisabled(roWarrantyPayStatus) && props.showWarrantySubmitAction,
    isVehicleUpdateDisabled:
      props.isVehicleUpdateDisabled ||
      Constraints.isVehicleUpdateDisabled(ROReader.getROStatus(roDetails), payTypeStatuses),
    isROBulkActionDisabled:
      !props.canDoROBulkAction || Constraints.isROBulkActionDisabled(ROReader.getROStatus(roDetails)),
    isCloseAllROTypesDisabled:
      !Constraints.isCloseAllROEnabled(
        cashieringStatus,
        roInternalPayStatus,
        roWarrantyPayStatus,
        ROReader.getROStatus(roDetails)
      ) || isROOrJobOnHold(roDetails, props.jobDetails),
    ..._pick(props, ['canViewInvoice', 'canViewCloseRO', 'isApplyCouponMenuDisabled', 'shouldHideMediaUploader']),
    isViewPostingPreviewDisabled:
      props.isViewPostingPreviewDisabled || isViewPostingPreviewDisabled(roDetails, payTypeStatuses),
    isViewCashieringVisible: _isFunction(props.toggleCashieringOverlay),
    isChangeServiceModeDisabled:
      !Constraints.isExpressModeEnabled() ||
      Constraints.isChangeServiceModeDisabledForRO(
        ROReader.getServiceMode(roDetails),
        ROReader.getROStatus(roDetails),
        ROReader.getSiteId(roDetails),
        payTypeStatuses
      ),
    isViewROPdfDisabled: props.isViewROPdfDisabled || false,
    isROClockedTimeDisabled: props.isROClockedTimeDisabled || false,
    isHoldDisabled: props.isROHoldDisabled || false,
    isAuditLogDisabled: props.isAuditLogDisabled || false,
    isViewQuotePdfDisabled: true,
    isReOpenQuoteDisabled: true,
    isQuoteBulkActionDisabled: true,
    isQuoteVoidDisabled: true,
    isQuoteDeclineDisabled: true,
    isCloneQuoteDisabled: true,
    isOEMSyncDisabled: props.isOEMSyncDisabled || !Constraints.isOEMSyncEnabled(vehicleDetails?.make),
    isOBDEnabled: !props.isOBDDisabled && Constraints.isOBDEnabled(),
    isServiceCRMEnabled: !props.isServiceCRMDisabled && Constraints.isServiceCRMEnabled(),
    canAccessTireStorageDetails: !props.isTireStorageDisabled && Constraints.canAccessTireStorageDetails(),
    isProfitLossViewEnabled: !props.isProfitLossViewDisabled && Constraints.isProfitLossViewEnabled(),
    isIrisFlowEnabled: !props.isIrisFlowDisabled && Constraints.isIrisFlowEnabled(),
    isROBulkActionScreenViewEnabled: true,
    isROReadyForInvoice: ROReader.getStatus(roDetails)?.status === RO_STATUS_VALUES.READY_FOR_INVOICE,
    isCloseROPayersEnabled: !props.isCloseROPayersDisabled && isServiceV3Enabled && isPostingPayerLevel,
    isReopenPayerVisible: getIsReopenPayerVisible({
      payerStatuses,
      isReopenPayerDisabled: props.isReopenPayerDisabled,
      isServiceV3Enabled,
      isPostingPayerLevel,
      roStatus: ROReader.getROStatus(roDetails),
    }),
    isInvoicePreviewDisabled: props.isInvoicePreviewDisabled,
    isMCSSymptomBasketEnabled: !props.isMCSSymptomBasketDisabled && Constraints.isMCSSymptomBasketEnabled(),
    isPayersViewEnabled: isServiceV3Enabled && !props.hidePayersViewOption,
    isUpdateEstimateDisabled: props.isUpdateEstimateDisabled,
    isPostingPayerLevel,
  };
};

const getQuoteConstraints = roDetails => ({
  isApplyCouponDisabled: isApplyCouponDisabled(roDetails),
  isROLevelFeeDisabled: isQuoteLevelFeeDisabled(roDetails),
  isVehicleUpdateDisabled: isVehicleUpdateDisabled(roDetails),
  isReOpenQuoteDisabled: isReOpenQuoteDisabled(roDetails),
  isViewCashieringVisible: false,
  isQuoteBulkActionDisabled: isQuoteBulkActionsDisabled(roDetails),
  isQuoteDeclineDisabled: !isDeclineQuoteAllowed(roDetails),
  isQuoteVoidDisabled: !isVoidQuoteAllowed(roDetails),
  isCloseCPRODisabled: true,
  isCloseIPRODisabled: true,
  isVoidDisabled: true,
  isCloseAllROTypesDisabled: true,
  isCloseWPRODisabled: true,
  isHoldDisabled: true,
  shouldHideMediaUploader: true,
  isROClockedTimeDisabled: true,
  isViewPostingPreviewDisabled: true,
  isChangeWarrantyStatusDisabled: true,
  isReOpenROAfterCloseDisabled: true,
  isReOpenROMenuDisabled: true,
  isAuditLogDisabled: true,
  isInvoicePreviewDisabled: true,
  isROBulkActionDisabled: true,
  isViewROPdfDisabled: true,
  isChangeServiceModeDisabled: true,
  isViewQuotePdfDisabled: isViewQuotePdfDisabled(roDetails),
  isCloneQuoteDisabled: !isCloneQuoteAllowed(roDetails),
  isOEMSyncDisabled: true,
});

export const getPreROConstraints = () => ({
  isApplyCouponDisabled: Constraints.shouldHideAddCouponsForWebCheckIn(),
  isROLevelFeeDisabled: false,
  isVehicleUpdateDisabled: true,
  isReOpenQuoteDisabled: true,
  isViewCashieringVisible: false,
  isQuoteBulkActionDisabled: true,
  isQuoteDeclineDisabled: true,
  isQuoteVoidDisabled: true,
  isCloseCPRODisabled: true,
  isCloseIPRODisabled: true,
  isVoidDisabled: true,
  isCloseAllROTypesDisabled: true,
  isCloseWPRODisabled: true,
  isHoldDisabled: true,
  shouldHideMediaUploader: true,
  isROClockedTimeDisabled: true,
  isViewPostingPreviewDisabled: true,
  isChangeWarrantyStatusDisabled: true,
  isReOpenROAfterCloseDisabled: true,
  isReOpenROMenuDisabled: true,
  isAuditLogDisabled: true,
  isInvoicePreviewDisabled: true,
  isROBulkActionDisabled: true,
  isViewROPdfDisabled: true,
  isChangeServiceModeDisabled: true,
  isViewQuotePdfDisabled: true,
  isCloneQuoteDisabled: true,
  isOEMSyncDisabled: true,
  showCancelCheckin: true,
});

const getBDCConstraints = () => ({
  isAuditLogDisabled: true,
  isChangeServiceModeDisabled: true,
  isCloneQuoteDisabled: true,
  isCloseCPRODisabled: true,
  isCloseIPRODisabled: true,
  isCloseAllROTypesDisabled: true,
  isCloseWPRODisabled: true,
  isQuoteDeclineDisabled: true,
  isHoldDisabled: true,
  isInvoicePreviewDisabled: true,
  shouldHideMediaUploader: true,
  isQuoteBulkActionDisabled: true,
  isReOpenROAfterCloseDisabled: true,
  isReOpenQuoteDisabled: true,
  isOEMSyncDisabled: true,
  isROBulkActionDisabled: true,
  isROClockedTimeDisabled: true,
  isVehicleUpdateDisabled: true,
  isViewQuotePdfDisabled: true,
  isViewPostingPreviewDisabled: true,
  isViewROPdfDisabled: true,
  isQuoteVoidDisabled: true,
  isVoidDisabled: true,
  isChangeWarrantyStatusDisabled: true,
});

const MODULE_TYPE_VS_CONSTRAINTS = {
  [MODULE_TYPES.QUOTE]: getQuoteConstraints,
  [MODULE_TYPES.RO]: getROConstraints,
  [MODULE_TYPES.PRE_RO]: getPreROConstraints,
  [MODULE_TYPES.BDC]: getBDCConstraints,
};

export const getConstraints = (roDetails, props) => {
  const { moduleType } = props;
  const funcToExec = MODULE_TYPE_VS_CONSTRAINTS[moduleType];
  return funcToExec(roDetails, props);
};

export const isValidPayerStatus = (roDetails, statusToCheck) => {
  const payerStatuses = new Set(_map(ROReader.getPayers(roDetails), PAYER_STATUS));
  return payerStatuses.has(statusToCheck);
};

export const canCloseSelectedPayTypes = (payTypes, roDetails, jobDetails, costCentersByPayType) => {
  const payTypesSet = new Set(payTypes);
  return (
    (!payTypesSet.has(PAY_TYPE_VALUES.CUSTOMER_PAY) || canCloseCPRO(roDetails)) &&
    (!payTypesSet.has(PAY_TYPE_VALUES.WARRANTY) || canCloseWPRO(roDetails, jobDetails, costCentersByPayType)) &&
    (!payTypesSet.has(PAY_TYPE_VALUES.INTERNAL) || canCloseIPRO(roDetails, jobDetails, costCentersByPayType))
  );
};

export const getBulkUpdateROPayTypeStatusPayload = (payType, status, closeWithError) => ({
  status,
  payType,
  closeWithError,
});

export const isJobVoided = (jobDetails, jobId) => _get(jobDetails, [jobId, 'status']) !== JOB_STATUS.VOID;

export const getPayerSelectionViewType = (showCloseROTab, showPayerCloseOverlay) => {
  if (showCloseROTab || showPayerCloseOverlay) return PAYERS_SELECTION_VIEW_TYPES.CLOSE;
  return PAYERS_SELECTION_VIEW_TYPES.RE_OPEN;
};

export const getCanCloseReviewedIpRO = ipStatus =>
  ipStatus === INTERNAL_PAY_RO_STATUS_VALUE.REVIEW_REQUESTED ? hasPermissionToCloseReviewedInternalRO() : true;

export const getIsNewDialogysLinkDisabled = roDetails =>
  _includes([RO_STATUS_VALUES.VOID, RO_STATUS_VALUES.CLOSED], ROReader.getROStatus(roDetails));

export const isTireStorageVisible = (jobDetails, tireOpcodes) => {
  const tireOpcodeIds = _map(tireOpcodes, 'opcode');
  const jobOperations = _flatten(_map(jobDetails, 'operations'));
  const hasTireOpcodes = _some(
    jobOperations,
    ({ opcode, jobId } = EMPTY_OBJECT) => _includes(tireOpcodeIds, opcode) && isJobVoided(jobDetails, jobId)
  );
  return hasTireOpcodes && Constraints.isTireStorageEnabled();
};

export const getBulkUpdateParamsToCloseBasePayType = (payTypesToClose, closeWithError) => ({
  closeAt: RO_CLOSE_LEVELS.BASE_PAY_TYPE,
  closeWithError,
  payTypes: _uniq(payTypesToClose),
});

export const getPayersToClosedByPayType = ({ payTypesToClose, payers, payTypeConfigurations }) =>
  _filter(payers, ({ subPayType }) => {
    const basePayType = getBasePayTypeForSubPayType(subPayType, payTypeConfigurations);
    return _includes(payTypesToClose, basePayType);
  });

export const getPayTypesToClose = ({ ipStatus, cpStatus, wpStatus, cashieringStatus }) => {
  const payTypesToClose = [];
  if (PAY_TYPE_VS_STATUSES_ELIGIBLE_TO_CLOSE_SET[PAY_TYPE_VALUES.INTERNAL].has(ipStatus))
    payTypesToClose.push(PAY_TYPE_VALUES.INTERNAL);
  if (isCPReadyToClose(cpStatus, cashieringStatus) || cpStatus === CUSTOMER_PAY_RO_STATUS_VALUE.INVOICED)
    payTypesToClose.push(PAY_TYPE_VALUES.CUSTOMER_PAY);
  if (PAY_TYPE_VS_STATUSES_ELIGIBLE_TO_CLOSE_SET[PAY_TYPE_VALUES.WARRANTY].has(wpStatus))
    payTypesToClose.push(PAY_TYPE_VALUES.WARRANTY);
  return payTypesToClose;
};
