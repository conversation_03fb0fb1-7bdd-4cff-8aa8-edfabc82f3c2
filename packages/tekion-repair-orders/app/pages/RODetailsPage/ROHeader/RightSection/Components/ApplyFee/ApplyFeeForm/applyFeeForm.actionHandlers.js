import produce from 'immer';

import _curryRight from 'lodash/curryRight';
import _set from 'lodash/set';
import _map from 'lodash/map';
import _keyBy from 'lodash/keyBy';

import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import ROFeeReader from 'tbusiness/appServices/service/readers/ROFee';
import ROReader from 'readers/RepairOrder.reader';
import { getPayersListFromAllowedPayers } from 'tbusiness/appServices/service/helpers/payTypes.helpers';
import { getErrorMessage } from 'tbase/utils/errorUtils';
import { FEE_DATA_FIELD_NAMES } from 'tbase/constants/fee.constants';
import { ACTION_TYPES } from 'organisms/ROForm/ROForm.constants';
import RO_FORM_ACTION_HANDLERS from 'organisms/ROForm/ROForm.actionHandlers';
import RO_FORM_FIELD_ACTION_HANDLERS from 'organisms/ROForm/ROFormField.actionHandlers';
import { reComputeAllFees, getPayTypeConfigurationForApplicablePayTypes } from 'organisms/ROForm/ROForm.helper';
import { getPayTypeFromOverrideFlags } from 'helpers/fee.helper';
import Constraints from 'helpers/constraints';

import { EMPTY_OBJECT } from 'tbase/app.constants';

export const ON_FIELD_CHANGE_HANDLERS = {
  DEFAULT: ({ id, value }, { setState, getState }) =>
    setState({ formValues: produce(getState().formValues, _curryRight(_set)(value)(id)) }),
  fees: ({ params, setState, getState }) => {
    const { moduleType } = getState();
    const wrappedGetState = () => ({ ...getState(), formValues: getState().values, moduleType });
    const wrappedSetState = ({ formValues }) => setState({ values: formValues });
    RO_FORM_FIELD_ACTION_HANDLERS.fees(
      { ...(params || EMPTY_OBJECT), isROLevelFee: true },
      { getState: wrappedGetState, setState: wrappedSetState }
    );
  },
};

const SET_FIELD_WITH_ERROR_FIELD_CHANGE = {
  fees: (params, { setState, getState }) => {
    const { moduleType } = getState();
    const wrappedGetState = () => ({ ...getState(), formValues: getState().values, moduleType });
    const wrappedSetState = ({ formValues }) => setState({ values: formValues });
    RO_FORM_FIELD_ACTION_HANDLERS.fees(params, { getState: wrappedGetState, setState: wrappedSetState });
  },
};

const getAdditionalFieldsForFees = (fees, payTypeConfigurations) => {
  return _map(fees, fee => {
    const updatedPayType = Constraints.isServiceV3Enabled()
      ? EMPTY_OBJECT
      : { payType: getPayTypeFromOverrideFlags(fee) };
    return {
      ...fee,
      ...updatedPayType,
      payTypeConfigurationForApplicablePayTypes: getPayTypeConfigurationForApplicablePayTypes(
        fee,
        payTypeConfigurations
      ),
    };
  });
};

const addPayTypeToFees = fees => _map(fees, fee => ({ ...fee, payType: getPayTypeFromOverrideFlags(fee) }));

export const ACTION_HANDLERS = {
  INIT: async ({ setState, getState }) => {
    const { initialValues, roDetails, jobDetails, moduleType, payTypeConfigurations } = getState();
    const billingCustomerId = ROReader.getBillingCustomerId(roDetails);
    setState({ isLoading: true });
    try {
      const reComputeAllFeesFuncToExec = Constraints.isServiceV3Enabled()
        ? () => Promise.resolve(initialValues.fees)
        : reComputeAllFees;
      const fees = await reComputeAllFeesFuncToExec(EMPTY_OBJECT, initialValues.fees, { moduleType, roDetails, jobDetails });
      const updatedFees = Constraints.isServiceV3Enabled()
        ? getAdditionalFieldsForFees(fees, payTypeConfigurations, billingCustomerId)
        : addPayTypeToFees(fees);

      setState({
        values: { ...getState().values, fees: updatedFees },
      });
    } catch (error) {
      toaster(TOASTER_TYPE.ERROR, getErrorMessage(error, __('Failed to load fees')));
    } finally {
      setState({ isLoading: false });
    }
  },
  [ACTION_TYPES.SET_FIELD_WITH_ERROR]: ({ params: payload, setState, getState }) => {
    RO_FORM_ACTION_HANDLERS[ACTION_TYPES.SET_FIELD_WITH_ERROR](payload, {
      setState,
      getState,
      fieldActionHandler: SET_FIELD_WITH_ERROR_FIELD_CHANGE,
    });
  },
};
