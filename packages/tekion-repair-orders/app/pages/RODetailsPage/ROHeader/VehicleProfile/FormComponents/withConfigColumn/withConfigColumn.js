import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { compose, withProps } from 'recompose';

import _constant from 'lodash/constant';
import _intersectionBy from 'lodash/intersectionBy';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from 'tbase/app.constants';

import withUserPreferenceColumn from 'tcomponents/connectors/withUserPreferenceColumn';

import {
  adaptColumnsForColumnConfig,
  createTableInputColumnConfig,
  getColumnConfigByKey,
} from './withConfigColumn.helpers';

import styles from './withConfigColumn.module.scss';

const mapPropsFomUserPreferenceColumn = adaptedColumnsToDisplay =>
  withProps(({ columns: originalColumns, columnConfigurator }) => ({
    columns: [
      ..._intersectionBy(originalColumns, adaptedColumnsToDisplay, 'key'),
      createTableInputColumnConfig({ ...columnConfigurator, headerClassName: styles.columnConfiguratorHeader }),
    ],
  }));

const withConfigColumn = ComposedComponent => {
  class WithConfigColumn extends PureComponent {
    state = { ComponentToRender: _constant(null) };

    componentDidMount() {
      const { id, columns: columnsToDisplay, allColumns, assetType } = this.props;
      const adaptedColumnsToDisplay = adaptColumnsForColumnConfig(columnsToDisplay, assetType);
      const allAdaptedColumns = adaptColumnsForColumnConfig(allColumns);
      this.setState({
        ComponentToRender: compose(
          withUserPreferenceColumn(
            `VEHICLE_PROFILE_${id}`,
            EMPTY_ARRAY,
            getColumnConfigByKey(allAdaptedColumns, adaptedColumnsToDisplay),
            true,
            false,
            EMPTY_OBJECT,
            EMPTY_STRING,
            false,
            false,
            true
          ),
          mapPropsFomUserPreferenceColumn(adaptedColumnsToDisplay)
        )(ComposedComponent),
      });
    }

    render() {
      const { ComponentToRender } = this.state;
      const { columns, ...restProps } = this.props;
      return <ComponentToRender {...restProps} />;
    }
  }

  WithConfigColumn.propTypes = {
    id: PropTypes.string.isRequired,
    columns: PropTypes.array.isRequired,
    allColumns: PropTypes.array.isRequired,
    assetType: PropTypes.string.isRequired,
  };

  return WithConfigColumn;
};

export default withConfigColumn;
