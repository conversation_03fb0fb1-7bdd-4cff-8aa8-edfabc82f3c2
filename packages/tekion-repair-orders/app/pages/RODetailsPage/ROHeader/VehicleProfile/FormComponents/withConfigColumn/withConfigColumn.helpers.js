import _constant from 'lodash/constant';
import _reduce from 'lodash/reduce';
import _map from 'lodash/map';
import _keyBy from 'lodash/keyBy';
import _get from 'lodash/get';

import { ColumnConfig, CellConfig } from 'tcomponents/molecules/tableInputField';

export const createTableInputColumnConfig = columnConfigurator => {
  const { Header, width, getHeaderProps, fixed, headerClassName } = columnConfigurator;
  const { key } = getHeaderProps();
  return new ColumnConfig(key)
    .setTitle(Header)
    .setKey(key)
    .addCellConfig(new CellConfig().setComponent(_constant(null)))
    .setFixed(fixed)
    .setHeaderClassName(headerClassName)
    .setMinWidth(width);
};

export const getColumnConfigByKey = (allColumns, columnsToDisplay) =>
  _reduce(
    allColumns,
    (acc, { key: columnKey, cellConfig, ...restColumnOptions }) =>
      acc[columnKey]
        ? acc
        : {
            ...acc,
            [columnKey]: { ...restColumnOptions, isVisibleInConfigurator: false },
          },
    _keyBy(columnsToDisplay, 'key')
  );

// This is necessary because the initial id of the column is used for the strainer and from here it must be used for the config
export const adaptColumnsForColumnConfig = (columns, assetType) =>
  _map(columns, column => ({
    ...column,
    id: `${assetType}_${_get(column, 'id')}`,
  }));
