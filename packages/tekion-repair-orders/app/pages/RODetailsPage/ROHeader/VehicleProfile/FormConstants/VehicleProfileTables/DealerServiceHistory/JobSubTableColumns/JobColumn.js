import { isCockpitDetailsViewEnabled } from 'helpers/repairOrder.helper';

import {
  SERVICE_HISTORY_DEALER_JOB_LINE_FIELD,
  SERVICE_HISTORY_DEALER_TOTAL_PRICE_FIELD,
  SERVICE_HISTORY_DEALER_JOB_CONCERN_FIELD,
  SERVICE_HISTORY_DEALER_JOB_CAUSE_FIELD,
  SERVICE_HISTORY_DEALER_JOB_HOURS,
  SERVICE_HISTORY_DEALER_CONCERN_TYPE_FIELD,
  SERVICE_HISTORY_DEALER_NUMBER_OF_OPERATIONS,
  SERVICE_HISTORY_DEALER_SUBLET_VENDOR,
  SERVICE_HISTORY_DEALER_PO_ID,
  SERVICE_HISTORY_DEALER_PO_LINE_ID,
  SERVICE_HISTORY_DEALER_SUBLET_TYPE,
  SERVICE_HISTORY_DEALER_SUBLET_LABOR,
  SERVICE_HISTORY_DEALER_SUBLET_PARTS,
  SERVICE_HISTORY_DEALER_JOB_TECHNICIANS_FIELD,
  SERVICE_HISTORY_DEALER_JOB_TYPE_FIELD,
  SERVICE_HISTORY_DEALER_REASON_FOR_VOIDING_JOB,
  SERVICE_HISTORY_DEALER_RETRACTED_ACTUAL_HOURS,
  SERVICE_HISTORY_DEALER_OPERATION_DESCRIPTION_FIELD,
  SERVICE_HISTORY_DEALER_LABOR_TYPE,
  FIELDS_ELLIPSIS_LENGTH,
  SERVICE_HISTORY_DEALER_OPCODE_FIELD,
  SERVICE_HISTORY_DEALER_STORY_LINE,
  SERVICE_HISTORY_DEALER_LABOR_HOURS,
  SERVICE_HISTORY_DEALER_LABOR_RATE,
  SERVICE_HISTORY_DEALER_BILL_RATE,
  SERVICE_HISTORY_DEALER_BILL_HRS,
  SERVICE_HISTORY_DEALER_LABOR_PRICE,
} from 'tbusiness/appServices/service/constants/vehicleProfile/vehicleProfileFields.constants';
import { EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import { VEHICLE_PROFILE_FIELDS_LABELS } from 'tbusiness/appServices/service/constants/vehicleProfile/vehicleProfileFields.labels';

import { createReadOnlyTextDisplayColumn, createColumnWithEllipsis } from '../../CommonColumn/commonColumn.factory';

import TECHNICIANS_COLUMN from '../Technicians.column';
import JOB_PAY_TYPE_COLUMN from './JobPayType.column';
import JOB_STORY_LINE_COLUMN from './JobStoryLine.column';
import JOB_STATUS_COLUMN from './JobStatus.column';

import styles from '../../../../vehicleProfile.module.scss';

const JOB_LINE_COLUMN = createReadOnlyTextDisplayColumn(
  VEHICLE_PROFILE_FIELDS_LABELS[SERVICE_HISTORY_DEALER_JOB_LINE_FIELD],
  SERVICE_HISTORY_DEALER_JOB_LINE_FIELD,
  undefined,
  styles.columnWithTextWrapping
);

const COCKPIT_VIEW_SPECIFIC_COLUMNS = [
  createReadOnlyTextDisplayColumn(
    VEHICLE_PROFILE_FIELDS_LABELS[SERVICE_HISTORY_DEALER_OPCODE_FIELD],
    SERVICE_HISTORY_DEALER_OPCODE_FIELD
  ),
  createColumnWithEllipsis(
    VEHICLE_PROFILE_FIELDS_LABELS[SERVICE_HISTORY_DEALER_OPERATION_DESCRIPTION_FIELD],
    SERVICE_HISTORY_DEALER_OPERATION_DESCRIPTION_FIELD,
    300,
    styles.columnWithTextWrapping,
    FIELDS_ELLIPSIS_LENGTH
  ),
  createReadOnlyTextDisplayColumn(
    VEHICLE_PROFILE_FIELDS_LABELS[SERVICE_HISTORY_DEALER_LABOR_HOURS],
    SERVICE_HISTORY_DEALER_LABOR_HOURS
  ),
  createReadOnlyTextDisplayColumn(
    VEHICLE_PROFILE_FIELDS_LABELS[SERVICE_HISTORY_DEALER_LABOR_RATE],
    SERVICE_HISTORY_DEALER_LABOR_RATE
  ),
  createReadOnlyTextDisplayColumn(
    VEHICLE_PROFILE_FIELDS_LABELS[SERVICE_HISTORY_DEALER_BILL_RATE],
    SERVICE_HISTORY_DEALER_BILL_RATE
  ),
  createReadOnlyTextDisplayColumn(
    VEHICLE_PROFILE_FIELDS_LABELS[SERVICE_HISTORY_DEALER_BILL_HRS],
    SERVICE_HISTORY_DEALER_BILL_HRS
  ),
  createReadOnlyTextDisplayColumn(
    VEHICLE_PROFILE_FIELDS_LABELS[SERVICE_HISTORY_DEALER_LABOR_PRICE],
    SERVICE_HISTORY_DEALER_LABOR_PRICE
  ),
  createColumnWithEllipsis(
    VEHICLE_PROFILE_FIELDS_LABELS[SERVICE_HISTORY_DEALER_STORY_LINE],
    SERVICE_HISTORY_DEALER_STORY_LINE,
    300,
    styles.columnWithTextWrapping,
    FIELDS_ELLIPSIS_LENGTH
  ),
];

export const getDealerJobLevelColumns = () => [
  JOB_LINE_COLUMN,
  JOB_STORY_LINE_COLUMN,
  JOB_STATUS_COLUMN,
  createColumnWithEllipsis(
    VEHICLE_PROFILE_FIELDS_LABELS[SERVICE_HISTORY_DEALER_JOB_CAUSE_FIELD],
    SERVICE_HISTORY_DEALER_JOB_CAUSE_FIELD,
    300,
    styles.columnWithTextWrapping,
    FIELDS_ELLIPSIS_LENGTH
  ),
  createColumnWithEllipsis(
    VEHICLE_PROFILE_FIELDS_LABELS[SERVICE_HISTORY_DEALER_JOB_CONCERN_FIELD],
    SERVICE_HISTORY_DEALER_JOB_CONCERN_FIELD,
    300,
    styles.columnWithTextWrapping,
    FIELDS_ELLIPSIS_LENGTH
  ),
  createReadOnlyTextDisplayColumn(
    VEHICLE_PROFILE_FIELDS_LABELS[SERVICE_HISTORY_DEALER_TOTAL_PRICE_FIELD],
    SERVICE_HISTORY_DEALER_TOTAL_PRICE_FIELD
  ),
  createReadOnlyTextDisplayColumn(
    VEHICLE_PROFILE_FIELDS_LABELS[SERVICE_HISTORY_DEALER_JOB_HOURS],
    SERVICE_HISTORY_DEALER_JOB_HOURS
  ),
  createReadOnlyTextDisplayColumn(
    VEHICLE_PROFILE_FIELDS_LABELS[SERVICE_HISTORY_DEALER_CONCERN_TYPE_FIELD],
    SERVICE_HISTORY_DEALER_CONCERN_TYPE_FIELD,
    undefined,
    styles.columnWithTextWrapping
  ),
  createReadOnlyTextDisplayColumn(
    VEHICLE_PROFILE_FIELDS_LABELS[SERVICE_HISTORY_DEALER_NUMBER_OF_OPERATIONS],
    SERVICE_HISTORY_DEALER_NUMBER_OF_OPERATIONS
  ),
  createReadOnlyTextDisplayColumn(
    VEHICLE_PROFILE_FIELDS_LABELS[SERVICE_HISTORY_DEALER_SUBLET_VENDOR],
    SERVICE_HISTORY_DEALER_SUBLET_VENDOR,
    undefined,
    styles.columnWithTextWrapping
  ),
  createReadOnlyTextDisplayColumn(
    VEHICLE_PROFILE_FIELDS_LABELS[SERVICE_HISTORY_DEALER_PO_ID],
    SERVICE_HISTORY_DEALER_PO_ID,
    undefined,
    styles.columnWithTextWrapping
  ),
  createReadOnlyTextDisplayColumn(
    VEHICLE_PROFILE_FIELDS_LABELS[SERVICE_HISTORY_DEALER_PO_LINE_ID],
    SERVICE_HISTORY_DEALER_PO_LINE_ID,
    undefined,
    styles.columnWithTextWrapping
  ),
  createReadOnlyTextDisplayColumn(
    VEHICLE_PROFILE_FIELDS_LABELS[SERVICE_HISTORY_DEALER_SUBLET_TYPE],
    SERVICE_HISTORY_DEALER_SUBLET_TYPE,
    undefined,
    styles.columnWithTextWrapping
  ),
  createReadOnlyTextDisplayColumn(
    VEHICLE_PROFILE_FIELDS_LABELS[SERVICE_HISTORY_DEALER_SUBLET_LABOR],
    SERVICE_HISTORY_DEALER_SUBLET_LABOR
  ),
  createReadOnlyTextDisplayColumn(
    VEHICLE_PROFILE_FIELDS_LABELS[SERVICE_HISTORY_DEALER_SUBLET_PARTS],
    SERVICE_HISTORY_DEALER_SUBLET_PARTS
  ),
  createReadOnlyTextDisplayColumn(
    VEHICLE_PROFILE_FIELDS_LABELS[SERVICE_HISTORY_DEALER_JOB_TYPE_FIELD],
    SERVICE_HISTORY_DEALER_JOB_TYPE_FIELD
  ),
  createReadOnlyTextDisplayColumn(
    VEHICLE_PROFILE_FIELDS_LABELS[SERVICE_HISTORY_DEALER_REASON_FOR_VOIDING_JOB],
    SERVICE_HISTORY_DEALER_REASON_FOR_VOIDING_JOB,
    undefined,
    styles.columnWithTextWrapping
  ),
  createReadOnlyTextDisplayColumn(
    VEHICLE_PROFILE_FIELDS_LABELS[SERVICE_HISTORY_DEALER_RETRACTED_ACTUAL_HOURS],
    SERVICE_HISTORY_DEALER_RETRACTED_ACTUAL_HOURS,
    undefined,
    styles.columnWithTextWrapping
  ),
  TECHNICIANS_COLUMN(
    SERVICE_HISTORY_DEALER_JOB_TECHNICIANS_FIELD,
    VEHICLE_PROFILE_FIELDS_LABELS[SERVICE_HISTORY_DEALER_JOB_TECHNICIANS_FIELD]
  ),
  JOB_PAY_TYPE_COLUMN,
  ...(isCockpitDetailsViewEnabled() ? COCKPIT_VIEW_SPECIFIC_COLUMNS : EMPTY_ARRAY),
];

export const THIRD_PARTY_JOB_COLUMNS = [
  JOB_LINE_COLUMN,
  createReadOnlyTextDisplayColumn(
    VEHICLE_PROFILE_FIELDS_LABELS[SERVICE_HISTORY_DEALER_OPERATION_DESCRIPTION_FIELD],
    SERVICE_HISTORY_DEALER_OPERATION_DESCRIPTION_FIELD
  ),
  createReadOnlyTextDisplayColumn(
    VEHICLE_PROFILE_FIELDS_LABELS[SERVICE_HISTORY_DEALER_LABOR_TYPE],
    SERVICE_HISTORY_DEALER_LABOR_TYPE
  ),
  TECHNICIANS_COLUMN(
    SERVICE_HISTORY_DEALER_JOB_TECHNICIANS_FIELD,
    VEHICLE_PROFILE_FIELDS_LABELS[SERVICE_HISTORY_DEALER_JOB_TECHNICIANS_FIELD],
    250
  ),
];
