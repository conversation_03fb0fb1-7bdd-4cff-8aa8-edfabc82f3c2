import _get from 'lodash/get';
import _map from 'lodash/map';
import _compact from 'lodash/compact';
import { tget } from 'tbase/utils/general';
import {
  getRepairOrderDetailsReducerKey,
  getROBootstrapReducer<PERSON>ey,
  getRONotesReducer<PERSON>ey,
  getPreRODetailsReducerKey,
  getAsyncQueueReducerKey,
} from 'twidgets/appServices/service/helpers/reducer.helper';
import { EMPTY_OBJECT, EMPTY_ARRAY } from 'tbase/app.constants';
import { NOTES } from 'tbusiness/appServices/service/constants/note.constants';
import { DSE_NOTES_REDUCER_KEY } from 'twidgets/appServices/service/constants/appModuleInitializer.constants';

const getJobsDetails = state => _get(state, `${getRepairOrderDetailsReducerKey()}.jobsData`, EMPTY_OBJECT);

const getRecommendationsDetails = state =>
  _get(state, `${getRepairOrderDetailsReducerKey()}.recommendationById`, EMPTY_OBJECT);

const getDeferredRecommendationIds = obj => _compact(_map(obj, 'id'));

const getRoDetails = state => _get(state, `${getRepairOrderDetailsReducerKey()}.roData`, EMPTY_OBJECT);

const getClockStatuses = storeKey => appState =>
  _get(appState, `${getRepairOrderDetailsReducerKey()}.clockStatuses.${storeKey}`, EMPTY_OBJECT);

const getDeferredRecommendations = state => _get(state, `${getRepairOrderDetailsReducerKey()}.deferredRecommendations`);

const getLaborRates = state => _get(state, `${getROBootstrapReducerKey()}.laborRates`, EMPTY_ARRAY);

const getRODetailsFetchStatus = state => _get(state, `${getRepairOrderDetailsReducerKey()}.isFetching`);

const getJobTags = state => _get(state, `${getROBootstrapReducerKey()}.jobTags`, EMPTY_ARRAY);

const getRecommendationTags = state => _get(state, `${getROBootstrapReducerKey()}.recommendationTags`, EMPTY_ARRAY);

const getRODetailsError = state => _get(state, `${getRepairOrderDetailsReducerKey()}.error`);

const getJobIds = state => _get(state, `${getRepairOrderDetailsReducerKey()}.jobIds`, EMPTY_ARRAY);

const getRecommendationIds = state =>
  _get(state, `${getRepairOrderDetailsReducerKey()}.recommendationIds`, EMPTY_ARRAY);

const getRecommendationByIds = state =>
  _get(state, `${getRepairOrderDetailsReducerKey()}.recommendationById`, EMPTY_OBJECT);

const getIntermediateRecommendationIds = state =>
  _get(state, `${getRepairOrderDetailsReducerKey()}.intermediateRecommendationIds`, EMPTY_ARRAY);

const getIntermediateRecommendationByIds = state =>
  _get(state, `${getRepairOrderDetailsReducerKey()}.intermedidateRecommendationById`, EMPTY_OBJECT);

const getIsAddRecommendationInProgress = state =>
  _get(state, `${getRepairOrderDetailsReducerKey()}.isAddRecommendationInProgress`, false);

const getCustomerType = state => _get(getRoDetails(state), 'additionalDetail.customerInfo.customerType');

const getJobNotes = (state, jobId) =>
  _get(state, `${getRONotesReducerKey()}.${NOTES.JOB}.[${jobId}]notes`, EMPTY_ARRAY);

const getJobNoteCount = (state, jobId) => _get(state, `${getRONotesReducerKey()}.${NOTES.JOB}.[${jobId}]count`, 0);

const getCustomerNotes = (state, jobId) =>
  _get(state, `${getRONotesReducerKey()}.${NOTES.CUSTOMER}.[${jobId}]notes`, EMPTY_ARRAY);

const getCustomerNoteCount = (state, customerId) =>
  _get(state, `${getRONotesReducerKey()}.${NOTES.CUSTOMER}.[${customerId}]count`, 0);

const getMpviInspectionNotes = (state, inspectionId) =>
  _get(state, `${getRONotesReducerKey()}.${NOTES.MPVI_INSPECTION_ITEM}.[${inspectionId}]notes`, EMPTY_ARRAY);

const getMpviInspectionNoteCount = (state, inspectionItemId) =>
  _get(state, `${getRONotesReducerKey()}.${NOTES.MPVI_INSPECTION_ITEM}.[${inspectionItemId}]count`, 0);

const getPartFulfillmentNotes = (state, fulfillmentId) =>
  _get(state, `${getRONotesReducerKey()}.${NOTES.FULFILLMENT}.[${fulfillmentId}]notes`, EMPTY_ARRAY);

const getPartFulfillmentNotesForAppt = (state, fulfillmentId) =>
  _get(state, `${DSE_NOTES_REDUCER_KEY}.${NOTES.APPOINTMENT_PART_LINE}.[${fulfillmentId}]notes`, EMPTY_ARRAY);

const getPartFulfillmentCount = (state, fulfillmentId) =>
  _get(state, `${getRONotesReducerKey()}.${NOTES.FULFILLMENT}.[${fulfillmentId}]count`, 0);

const getPartAvailabilityNotes = (state, pnaId) =>
  _get(state, `${getRONotesReducerKey()}.${NOTES.PNA}.[${pnaId}]notes`, EMPTY_ARRAY);

const getPartAvailabilityCount = (state, pnaId) =>
  _get(state, `${getRONotesReducerKey()}.${NOTES.PNA}.[${pnaId}]count`, 0);

const getPartsNotesForDseStandalone = (state, pnaId) =>
  _get(state, `${getRONotesReducerKey()}.${NOTES.PARTS_PNA_DSEV2}.[${pnaId}]notes`, EMPTY_ARRAY);

const getPartsNotesCountForDseStandalone = (state, pnaId) =>
  _get(state, `${getRONotesReducerKey()}.${NOTES.PARTS_PNA_DSEV2}.[${pnaId}]count`, 0);

const getTechRecommendationNotes = (state, recommendationId) =>
  _get(state, `${getRONotesReducerKey()}.${NOTES.JOB}.[${recommendationId}]notes`, EMPTY_ARRAY);

const getRONotes = (state, roId) =>
  _get(state, `${getRONotesReducerKey()}.${NOTES.REPAIR_ORDER}.[${roId}].notes`, EMPTY_ARRAY);

const getRONoteCount = (state, roId) =>
  _get(state, `${getRONotesReducerKey()}.${NOTES.REPAIR_ORDER}.[${roId}].count`, 0);

const getAssetTypeNotes = (state, assetId, assetType) =>
  _get(state, `${getRONotesReducerKey()}.${assetType}.[${assetId}]notes`, EMPTY_ARRAY);

const getAssetTypeNoteCount = (state, assetId, assetType) =>
  _get(state, `${getRONotesReducerKey()}.${assetType}.[${assetId}].count`, 0);

const getAppointmentNotes = state => _get(state, `${getRONotesReducerKey()}.${NOTES.APPOINTMENT}`, EMPTY_OBJECT);

const getMpviInspectionItems = state => tget(state, `${getRepairOrderDetailsReducerKey()}.mpviDetails`, EMPTY_OBJECT);

const getServiceTypes = state => tget(state, `${getROBootstrapReducerKey()}.serviceTypes`, EMPTY_ARRAY);

const getSkills = state => tget(state, `${getROBootstrapReducerKey()}.skills`, EMPTY_OBJECT);

const getOEMSyncStatus = state => tget(state, `${getRepairOrderDetailsReducerKey()}.oemSyncData`, EMPTY_OBJECT);

const getROApprovalData = state => tget(state, `${getRepairOrderDetailsReducerKey()}.roApprovalData`, EMPTY_OBJECT);

const getIsApprovalStatusFetched = state =>
  tget(state, `${getRepairOrderDetailsReducerKey()}.roApprovalData.isApprovalStatusFetched`, false);

const getLaborRatesFromRepairOrder = state =>
  _get(state, `${getRepairOrderDetailsReducerKey()}.laborRates`, EMPTY_OBJECT);

const getLaborRatesFromPreRO = state => _get(state, `${getPreRODetailsReducerKey()}.laborRates`, EMPTY_OBJECT);

const getAsyncQueue = state => tget(state, getAsyncQueueReducerKey(), EMPTY_OBJECT);
const getAsyncQueueItems = state => tget(state, `${getAsyncQueueReducerKey()}.queue`, EMPTY_OBJECT);
const getAsyncQueueIsProcessing = state => tget(state, `${getAsyncQueueReducerKey()}.isProcessing`, EMPTY_OBJECT);
const getAsyncQueueCount = state => tget(state, `${getAsyncQueueReducerKey()}.count`, EMPTY_OBJECT);

export {
  getCustomerType,
  getDeferredRecommendationIds,
  getDeferredRecommendations,
  getJobsDetails,
  getJobIds,
  getJobNotes,
  getPartAvailabilityNotes,
  getPartAvailabilityCount,
  getPartFulfillmentNotes,
  getPartFulfillmentCount,
  getJobNoteCount,
  getCustomerNotes,
  getCustomerNoteCount,
  getMpviInspectionNoteCount,
  getMpviInspectionNotes,
  getRoDetails,
  getClockStatuses,
  getLaborRates,
  getRecommendationByIds,
  getRecommendationIds,
  getRODetailsFetchStatus,
  getRODetailsError,
  getRONotes,
  getRONoteCount,
  getAssetTypeNotes,
  getAssetTypeNoteCount,
  getTechRecommendationNotes,
  getAppointmentNotes,
  getMpviInspectionItems,
  getPartsNotesForDseStandalone,
  getPartsNotesCountForDseStandalone,
  getServiceTypes,
  getOEMSyncStatus,
  getSkills,
  getRecommendationsDetails,
  getJobTags,
  getROApprovalData,
  getIsApprovalStatusFetched,
  getRecommendationTags,
  getLaborRatesFromRepairOrder,
  getLaborRatesFromPreRO,
  getPartFulfillmentNotesForAppt,
  getIntermediateRecommendationIds,
  getIntermediateRecommendationByIds,
  getIsAddRecommendationInProgress,
  getAsyncQueue,
  getAsyncQueueItems,
  getAsyncQueueIsProcessing,
  getAsyncQueueCount,
};
