import { createAction } from 'redux-actions';

import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import { getErrorMessage } from 'tbase/utils/errorUtils';
import { EMPTY_OBJECT } from 'tbase/app.constants';
import { MODULE_TYPES } from 'tbusiness/appServices/service/constants/general';

import InvoiceConfirmationService from 'services/InvoiceConfirmation';
import RepairOrderDetailsRepositoryService from 'services/RepairOrderDetailsRepository';
import ChorusPro from 'services/ChorusPro';
import { invoiceConfirmationActions } from 'actions/invoiceConfirmation.actionTypes';
import { validatePreInvoiceRules } from 'actions/roDetails.actions';
import ROConstraints from 'helpers/constraints';
import { VALIDATION_LEVELS } from 'utils/constants';

import { getCalculationAndCouponDistributions } from './InvoiceConfirmationPage.helper';
import { CHORUS_PRO_PAYER_FIELD_IDS, LIST_OF_ASSET_TYPES } from './InvoiceConfirmationPage.constants';

export const fetchTransientInvoiceConfirmationDetails = (roId, params) =>
  InvoiceConfirmationService.fetchTransientInvoiceConfirmationDetails(roId, params)
    .then(getCalculationAndCouponDistributions)
    .catch(err => {
      toaster(TOASTER_TYPE.ERROR, getErrorMessage(err, __('Failed to fetch details')));
    });

export const fetchTransientInvoiceConfirmationDetailsForMultiPayer = (roId, params) =>
  InvoiceConfirmationService.fetchTransientInvoiceConfirmationDetailsForMultiPayer(roId, params).catch(err => {
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(err, __('Failed to fetch details')));
  });

export const saveInvoiceConfirmationDetails = (roId, params) =>
  InvoiceConfirmationService.saveInvoiceConfirmationDetails(roId, params);

export const savePayerTaxCodeDetails = (roId, params) =>
  InvoiceConfirmationService.savePayerTaxCodeDetails(roId, params);

export const fetchADMPayerList = ({ roId } = EMPTY_OBJECT) => {
  const payload = {
    orderType: MODULE_TYPES.RO,
    orderId: roId,
    assetType: LIST_OF_ASSET_TYPES.PAYER,
    fields: CHORUS_PRO_PAYER_FIELD_IDS,
  };
  return InvoiceConfirmationService.getAdditionalPayerDetails(payload).catch(err =>
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(err, __('Failed to fetch ADM Payer List')))
  );
};

export const getChorusProPayerDetails = siretNumber =>
  ChorusPro.getChorusProInvoicingDetails(siretNumber).catch(err =>
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(err, __('Failed to fetch Chorus Pro Details')))
  );

export const submitChorusProModalDetails = (roId, payerId, params) =>
  ChorusPro.addChorusProInvoicingDetails(roId, payerId, params).catch(err =>
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(err, __('Failed to submit Chorus Pro Details')))
  );

export const submitCustomerReferenceNumber = ({ roId, payload }) =>
  RepairOrderDetailsRepositoryService.submitCustomerReferenceNumber(roId, payload);

export const getROValidationIssues = (repairOrder, roId) =>
  ROConstraints.canViewInvoice(repairOrder)
    ? validatePreInvoiceRules(roId, VALIDATION_LEVELS.RO)
    : Promise.resolve(EMPTY_OBJECT);

export const getValidationIssuesForMultiPayer = ({ repairOrder, roId, payerIds, ruleLevel = VALIDATION_LEVELS.RO }) =>
  ROConstraints.canViewInvoiceForMultiPayer(repairOrder)
    ? validatePreInvoiceRules(roId, ruleLevel, payerIds)
    : Promise.resolve(EMPTY_OBJECT);

export const getROJobValidationIssues = (repairOrder, roId) =>
  ROConstraints.canViewInvoice(repairOrder)
    ? validatePreInvoiceRules(roId, VALIDATION_LEVELS.JOB)
    : Promise.resolve(EMPTY_OBJECT);

const fetchInvoiceAfterManageExcessSelection = createAction(
  invoiceConfirmationActions.FETCH_AFTER_MANAGE_EXCESS_SELECTION
);

export const toggleFetchAfterManageExcessSelection = () => (dispatch, getState) => {
  const { toggleFetchAfterManageExcessSelection } = getState();
  dispatch(
    fetchInvoiceAfterManageExcessSelection({
      toggleFetchAfterManageExcessSelection: !toggleFetchAfterManageExcessSelection,
    })
  );
};
