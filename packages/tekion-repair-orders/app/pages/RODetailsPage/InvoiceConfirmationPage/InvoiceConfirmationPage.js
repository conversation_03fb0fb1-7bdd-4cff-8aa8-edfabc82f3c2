import React, { useEffect, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';
import { compose } from 'recompose';
import { connect } from 'react-redux';

import _noop from 'lodash/noop';

import withRouter from '@tekion/tekion-components/src/hoc/withRouter';
import withActionHandlers from 'tcomponents/connectors/withActionHandlers';
import withAsyncReducer from 'tcomponents/connectors/withAsyncReducer';
import Drawer, { PLACEMENTS } from 'tcomponents/molecules/drawer';
import Tab from 'tcomponents/molecules/Tabs/Tabs';
import withSize from 'tcomponents/hoc/withSize';
import { tget } from 'tbase/utils/general';
import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from 'tbase/app.constants';
import { CTA_LOCK_DETAILS_REDUCER_KEY } from 'tbusiness/appServices/service/constants/approvalFlow';

import drawerStyles from 'twidgets/appServices/accounting/organisms/transactionDrawer/transactionDrawer.module.scss';

import InvoiceConfirmationReducer from 'reducers/InvoiceConfirmationDetail.reducer';
import ROHeader from 'pages/RODetailsPage/ROHeader';
import RepairOrderReader from 'readers/RepairOrder.reader';
import { fetchInvoiceConfirmationDetails } from 'actions/roDetails.actions';
import {
  getInvoiceConfirmationReducerKey,
  getRepairOrderDetailsReducerKey,
} from 'twidgets/appServices/service/helpers/reducer.helper';
import ROConstraints from 'helpers/constraints';
import { isCockpitDetailsViewEnabled } from 'helpers/repairOrder.helper';

import { getCockpitViewClasses } from 'pages/RODetailsPage/DetailedROView/DetailedROView.helper';
import style from 'pages/RODetailsPage/DetailedROView/PreInvoicing/preInvoicing.module.scss';

import { toggleFetchAfterManageExcessSelection } from './InvoiceConfirmationPage.actions';
import InvoiceSelectionTab from './Components/Tabs/InvoiceSelection';
import ConsolidatedView from './Components/Tabs/ConsolidatedTab';
import PayersConsolidatedTab from './Components/Tabs/PayersConsolidatedTab';
import { ACTION_HANDLERS } from './InvoiceConfirmationPage.actionHandler';
import {
  TABS_CONFIG,
  ON_TAB_CHANGE,
  tabStyle,
  INVOICE_CONFIRMATION_VIEW_TYPES,
  INIT_FORM,
  PAYERS_SELECTION_VIEW_TYPES,
} from './InvoiceConfirmationPage.constants';

import {
  getIsModificationDisabled,
  arePayersSelectedForInvoice,
  shouldDisableInvoiceConfirmationPage,
  isViewTypeInvoice,
} from './InvoiceConfirmationPage.helper';
import styles from './InvoiceConfirmationPage.module.scss';
import { VIEW_TYPE_VS_TAB_CONFIG } from './InvoiceConfirmationPage.factory';

const BODY_STYLE = {
  height: '100%',
  padding: 0,
};

const InvoiceConfirmationPage = props => {
  const {
    contentHeight,
    location,
    activeTabKey,
    loading,
    roId,
    fetchInvoiceConfirmationDetails: fetchInvoiceConfirmationDetailsAction,
    roLevelIssues,
    roIssuesByJob,
    handleInvoiceConfirmationOverlay,
    handleCashieringOverlay,
    roDetails,
    jobDetails,
    dispatch,
    onAction,
    invoiceSelectionDetails,
    viewType,
    validateROByIdForMultiPayer,
    togglePostingError,
    payerPayTypeConfiguration,
    defaultSubPayType,
    ctaLockDetails,
    navigate,
  } = props;

  const [showInvoiceSelectionConfirmationModal, setShowInvoiceSelectionConfirmationModal] = useState(false);
  const [hasInvoiceSelectionModified, setHasInvoiceSelectionModified] = useState(false);
  const [selectedTab, setSelectedTab] = useState(EMPTY_STRING);
  const [isCockpitViewTabActive, setIsCockpitViewTabActive] = useState(false);

  const invoiceSelectionDataForViewType = tget(invoiceSelectionDetails, viewType, EMPTY_ARRAY);

  useEffect(() => {
    onAction({ type: INIT_FORM });
  }, [onAction]);

  const handleTabChange = (key, isSaveActionCall) => {
    onAction({
      type: ON_TAB_CHANGE,
      payload: {
        key,
        setShowInvoiceSelectionConfirmationModal,
        hasFormModified: hasInvoiceSelectionModified,
        isSaveActionCall,
      },
    });
  };

  const getInvoiceSelectionTab = isInvoiceSelection => {
    const sanitizedViewType = isInvoiceSelection ? PAYERS_SELECTION_VIEW_TYPES.INVOICE : viewType;
    return (
      <Tab.TabPane
        className="full-width"
        tab={VIEW_TYPE_VS_TAB_CONFIG[sanitizedViewType]?.label}
        key={VIEW_TYPE_VS_TAB_CONFIG[sanitizedViewType]?.key}>
        <InvoiceSelectionTab
          handleInvoiceConfirmationOverlay={handleInvoiceConfirmationOverlay}
          handleTabChange={handleTabChange}
          showInvoiceSelectionConfirmationModal={showInvoiceSelectionConfirmationModal}
          setShowInvoiceSelectionConfirmationModal={setShowInvoiceSelectionConfirmationModal}
          setHasInvoiceSelectionModified={setHasInvoiceSelectionModified}
          hasInvoiceSelectionModified={hasInvoiceSelectionModified}
          viewType={sanitizedViewType}
          jobDetails={jobDetails}
          roDetails={roDetails}
          roId={roId}
          togglePostingError={togglePostingError}
          activeTabKey={activeTabKey}
        />
      </Tab.TabPane>
    );
  };

  const disableInvoiceConfirmationPage = shouldDisableInvoiceConfirmationPage(roDetails);
  const isMultiPayerEnabled = ROConstraints.isServiceV3Enabled();
  const isPayerPayTypeSetupEnabled = ROConstraints.isPayerPayTypeSetupEnabled();
  const isModificationDisabled = useMemo(
    () => getIsModificationDisabled(RepairOrderReader.getPayers(roDetails)),
    [roDetails]
  );
  const isConsolidatedAndPayersTabDisabled = useMemo(
    () =>
      isViewTypeInvoice(viewType) &&
      isMultiPayerEnabled &&
      !arePayersSelectedForInvoice(invoiceSelectionDataForViewType),
    [invoiceSelectionDataForViewType, viewType, isMultiPayerEnabled]
  );

  const handleSelectedTabForCockpitView = selectedCockpitTab => {
    setSelectedTab(selectedCockpitTab);
  };

  const { cockpitViewHighlightClass, cockpitViewHeaderClassName } = getCockpitViewClasses({
    isCockpitViewTabActive,
    cockpitChevronActiveClass: style.cockpitTabHighlight,
    cockpitChevronClass: style.cockpitChevronClass,
    headerCockpitView: style.headerCockpitView,
  });

  const handleCockpitViewTabClick = isCockpitTabActive => {
    setIsCockpitViewTabActive(isCockpitTabActive);
  };

  const commonPropsForTabs = useMemo(
    () => ({
      contentHeight,
      handleInvoiceConfirmationOverlay,
      fetchInvoiceConfirmationDetails: fetchInvoiceConfirmationDetailsAction,
      roId,
      roLevelIssues,
      roIssuesByJob,
      navigate,
      location,
      disableInvoiceConfirmationPage,
      handleCashieringOverlay,
      loading,
      dispatch,
      invoiceSelectionData: invoiceSelectionDataForViewType,
      roDetails,
      ctaLockDetails,
    }),
    [
      contentHeight,
      handleInvoiceConfirmationOverlay,
      fetchInvoiceConfirmationDetailsAction,
      roId,
      roLevelIssues,
      roIssuesByJob,
      disableInvoiceConfirmationPage,
      handleCashieringOverlay,
      loading,
      dispatch,
      invoiceSelectionDataForViewType,
      roDetails,
      ctaLockDetails,
      navigate,
      location,
    ]
  );

  const renderPayersConsolidatedTab = () => (
    <Tab.TabPane
      className="full-width"
      tab={TABS_CONFIG.PAYERS_CONSOLIDATED.label}
      key={TABS_CONFIG.PAYERS_CONSOLIDATED.key}>
      <PayersConsolidatedTab
        {...commonPropsForTabs}
        jobDetails={jobDetails}
        validateROByIdForMultiPayer={validateROByIdForMultiPayer}
        viewType={INVOICE_CONFIRMATION_VIEW_TYPES.PAYERS_CONSOLIDATED_VIEW}
        payerPayTypeConfiguration={payerPayTypeConfiguration}
        defaultSubPayType={defaultSubPayType}
      />
    </Tab.TabPane>
  );

  const renderConsolidatedTab = () => (
    <Tab.TabPane
      className="full-width"
      tab={TABS_CONFIG.CONSOLIDATED.label}
      key={TABS_CONFIG.CONSOLIDATED.key}
      disabled={isConsolidatedAndPayersTabDisabled}>
      <ConsolidatedView
        {...commonPropsForTabs}
        viewType={INVOICE_CONFIRMATION_VIEW_TYPES.CONSOLIDATED_VIEW}
        jobDetails={jobDetails}
        isModificationDisabled={isModificationDisabled}
        validateROByIdForMultiPayer={validateROByIdForMultiPayer}
      />
    </Tab.TabPane>
  );

  return (
    <Drawer
      visible
      height={`calc(100% - ${drawerStyles.skeletonHeight})`}
      className={drawerStyles.drawerContainer}
      bodyStyle={BODY_STYLE}
      placement={PLACEMENTS.TOP}
      destroyOnClose
      closable={false}
      mask={false}
      keyboard={false}>
      <ROHeader
        goBackCallback={handleInvoiceConfirmationOverlay}
        showWorkflowActions
        roDetails={roDetails}
        jobDetails={jobDetails}
        navigate={navigate}
        location={location}
        renderRightSection={null}
        selectedTab={selectedTab}
        handleSelectedTabForCockpitView={handleSelectedTabForCockpitView}
        isCockpitDetailsViewEnabled={isCockpitDetailsViewEnabled()}
        cockpitViewHighlightClass={cockpitViewHighlightClass}
        handleCockpitTabOnClick={handleCockpitViewTabClick}
        cockpitViewHeaderClassName={cockpitViewHeaderClassName}
      />
      <div style={tabStyle} className="full-height">
        <Tab
          className={cx(styles.tabContainer, 'flex-grow-1 flex-column full-height')}
          activeKey={activeTabKey}
          onChange={handleTabChange}>
          {!isMultiPayerEnabled && renderConsolidatedTab()}
          {isMultiPayerEnabled && renderPayersConsolidatedTab()}
          {isMultiPayerEnabled && viewType !== PAYERS_SELECTION_VIEW_TYPES.INVOICE && getInvoiceSelectionTab()}
        </Tab>
      </div>
    </Drawer>
  );
};

InvoiceConfirmationPage.propTypes = {
  onAction: PropTypes.func,
  contentHeight: PropTypes.number,
  navigate: PropTypes.func.isRequired,
  location: PropTypes.object.isRequired,
  activeTabKey: PropTypes.string,
  loading: PropTypes.bool,
  roId: PropTypes.string,
  fetchInvoiceConfirmationDetails: PropTypes.func,
  roLevelIssues: PropTypes.object,
  roIssuesByJob: PropTypes.object,
  handleInvoiceConfirmationOverlay: PropTypes.func,
  handleCashieringOverlay: PropTypes.func,
  roDetails: PropTypes.object,
  dispatch: PropTypes.func,
  invoiceSelectionDetails: PropTypes.array,
  jobDetails: PropTypes.object,
  viewType: PropTypes.string,
  validateROByIdForMultiPayer: PropTypes.func,
  togglePostingError: PropTypes.func,
  payerPayTypeConfiguration: PropTypes.object,
  defaultSubPayType: PropTypes.string,
  ctaLockDetails: PropTypes.array,
};

InvoiceConfirmationPage.defaultProps = {
  onAction: _noop,
  contentHeight: 0,
  activeTabKey: TABS_CONFIG.CONSOLIDATED.key,
  loading: false,
  roId: EMPTY_STRING,
  fetchInvoiceConfirmationDetails: _noop,
  roLevelIssues: EMPTY_OBJECT,
  roIssuesByJob: EMPTY_OBJECT,
  handleInvoiceConfirmationOverlay: _noop,
  handleCashieringOverlay: _noop,
  roDetails: EMPTY_OBJECT,
  dispatch: _noop,
  invoiceSelectionDetails: EMPTY_ARRAY,
  jobDetails: EMPTY_OBJECT,
  viewType: PAYERS_SELECTION_VIEW_TYPES.INVOICE,
  validateROByIdForMultiPayer: _noop,
  togglePostingError: _noop,
  payerPayTypeConfiguration: EMPTY_OBJECT,
  defaultSubPayType: EMPTY_STRING,
  ctaLockDetails: EMPTY_ARRAY,
};

function mapStateToProps(state) {
  const { invoiceSelectionData, [CTA_LOCK_DETAILS_REDUCER_KEY]: ctaLockDetails } = tget(
    state,
    `${getRepairOrderDetailsReducerKey()}`,
    EMPTY_ARRAY
  );
  return {
    invoiceSelectionDetails: invoiceSelectionData,
    ctaLockDetails,
  };
}

export default compose(
  connect(mapStateToProps, {
    fetchInvoiceConfirmationDetails,
    toggleFetchAfterManageExcessSelection,
  }),
  withRouter,
  withActionHandlers(ACTION_HANDLERS),
  withSize({ hasPageFooter: true, hasPageHeader: true }),
  withAsyncReducer({
    storeKey: getInvoiceConfirmationReducerKey(),
    reducer: InvoiceConfirmationReducer,
  })
)(InvoiceConfirmationPage);
