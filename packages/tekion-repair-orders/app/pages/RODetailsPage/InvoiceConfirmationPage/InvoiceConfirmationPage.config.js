import _reduce from 'lodash/reduce';
import _keys from 'lodash/keys';
import _identity from 'lodash/identity';

import ChorusProInvoicingModal from 'twidgets/appServices/accounting/organisms/chorusProInvoicingModal';
import PayerReader from 'tbusiness/appServices/service/readers/Payer';
import { filterRows } from 'tcomponents/organisms/FormBuilder/utils/general';
import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';

import ROConstraints from 'helpers/constraints';

import AddEditCoupon from './Components/AddEditCoupon';
import ManageExcess from './Components/ManageExcessModal';
import AddEditDeductibleButton from './Components/AddEditDeductibleButton';
import ROBreakdownTable from './Components/ROBreakdownTable';
import TaxBreakdownTable from './Components/TaxBreakdownTable';
import WarningsAndLikesSection from './Components/WarningAndLikesSection';
import PayerConsolidatedSection from './Components/Tabs/PayersConsolidatedTab/Components/PayerConsolidatedSection';
import AdditionalPayerDetails from './Components/AdditionalPayerDetails';
import { getPayerConsolidatedPayerFieldId } from './InvoiceConfirmationPage.helper';
import { PAYERS_CONSOLIDATED_FIELD_IDS } from './InvoiceConfirmationPage.constants';

import styles from './InvoiceConfirmationPage.module.scss';

export const INVOICE_CONFIRMATION_PAGE_FIELD_IDS = {
  REPAIR_ORDER_BREAK_DOWN: 'REPAIR_ORDER_BREAK_DOWN',
  TAX_BREAK_DOWN: 'TAX_BREAK_DOWN',
  WARNINGS_AND_LIKES_SECTION: 'WARNINGS_AND_LIKES_SECTION',
};

export const getConsolidatedViewSections = () => [
  {
    className: styles.roBreakdownContainer,
    rows: [
      {
        className: styles.warningAndLikeSections,
        columns: [INVOICE_CONFIRMATION_PAGE_FIELD_IDS.WARNINGS_AND_LIKES_SECTION],
      },
      {
        className: 'full-width',
        columns: [INVOICE_CONFIRMATION_PAGE_FIELD_IDS.REPAIR_ORDER_BREAK_DOWN],
      },
    ],
  },
];

export const PAYERS_VIEW_SECTIONS = [
  {
    className: styles.roBreakdownContainer,
    rows: [
      {
        className: 'full-width',
        columns: [INVOICE_CONFIRMATION_PAGE_FIELD_IDS.REPAIR_ORDER_BREAK_DOWN],
      },
      {
        className: 'full-width',
        columns: [INVOICE_CONFIRMATION_PAGE_FIELD_IDS.TAX_BREAK_DOWN],
      },
    ],
  },
];

export const getConsolidatedViewFields = ({
  jobLines,
  errors,
  warnings,
  roLevelFees,
  roDetails,
  couponDistributions,
  roId,
  roEntitiesAmount,
  viewType,
  selectedPayType,
  selectedPayerId,
  showAddEditDeductibleModal,
  accountingTaxCodes,
  payersOptions,
  paySplit,

  payTypeConfigurations,
  isModificationDisabled,
  roPayerTaxCodes,
  payTypeOptions,
}) => ({
  [INVOICE_CONFIRMATION_PAGE_FIELD_IDS.WARNINGS_AND_LIKES_SECTION]: {
    id: INVOICE_CONFIRMATION_PAGE_FIELD_IDS.WARNINGS_AND_LIKES_SECTION,
    renderer: WarningsAndLikesSection,
    renderOptions: { showWarningAndErrorSection: true, errors, warnings, roId },
  },
  [INVOICE_CONFIRMATION_PAGE_FIELD_IDS.REPAIR_ORDER_BREAK_DOWN]: {
    id: INVOICE_CONFIRMATION_PAGE_FIELD_IDS.REPAIR_ORDER_BREAK_DOWN,
    renderer: ROBreakdownTable,
    renderOptions: {
      jobLines,
      paySplit,
      roLevelFees,
      roDetails,
      couponDistributions,
      roEntitiesAmount,
      viewType,
      selectedPayType,
      selectedPayerId,
      showAddEditDeductibleModal,
      accountingTaxCodes,
      payersOptions,
      payTypeConfigurations,
      isModificationDisabled,
      roPayerTaxCodes,
      payTypeOptions,
    },
  },
  [INVOICE_CONFIRMATION_PAGE_FIELD_IDS.TAX_BREAK_DOWN]: {
    id: INVOICE_CONFIRMATION_PAGE_FIELD_IDS.TAX_BREAK_DOWN,
    renderer: TaxBreakdownTable,
    renderOptions: { roEntitiesAmount, viewType, paySplit, selectedPayType, selectedPayerId },
  },
});

const getPayersSections = roPayers =>
  _reduce(
    roPayers,
    (acc, payer) => [
      ...acc,
      {
        className: 'full-width m-y-20 p-x-24',
        columns: [getPayerConsolidatedPayerFieldId(PayerReader.payerId(payer))],
      },
    ],
    EMPTY_ARRAY
  );

const rowCheck = {
  [PAYERS_CONSOLIDATED_FIELD_IDS.ADD_EDIT_DEDUCTIBLE]: _identity,
};

export const getPayerConsolidatedViewSections = roPayers => [
  {
    className: styles.sectionContainer,
    rows: [
      {
        className: 'full-width',
        columns: [INVOICE_CONFIRMATION_PAGE_FIELD_IDS.WARNINGS_AND_LIKES_SECTION],
      },
      {
        className: 'd-flex full-width justify-content-end m-t-20 p-x-24',
        columns: filterRows(
          [
            PAYERS_CONSOLIDATED_FIELD_IDS.ADD_EDIT_DEDUCTIBLE,
            PAYERS_CONSOLIDATED_FIELD_IDS.MANAGE_EXCESS,
            PAYERS_CONSOLIDATED_FIELD_IDS.ADD_EDIT_COUPON,
          ],
          ROConstraints.isPayerDeductibleEnabled(),
          rowCheck
        ),
      },
      {
        columns: filterRows(
          [
            PAYERS_CONSOLIDATED_FIELD_IDS.CHORUS_PRO_INVOICING_MODAL,
            PAYERS_CONSOLIDATED_FIELD_IDS.ADDITIONAL_PAYER_DETAILS,
          ],
          ROConstraints.isChorusProEnabled(),
          rowCheck
        ),
      },
      ...getPayersSections(roPayers),
    ],
  },
];

const getPayerFields = ({ roPayersById, ...rest }) =>
  _reduce(
    _keys(roPayersById),
    (acc, payerId) => {
      const fieldId = getPayerConsolidatedPayerFieldId(payerId);
      return {
        ...acc,
        [fieldId]: {
          id: fieldId,
          renderer: PayerConsolidatedSection,
          renderOptions: {
            roPayersById,
            payerId,
            ...rest,
          },
        },
      };
    },
    EMPTY_OBJECT
  );

export const getPayerConsolidatedViewFields = ({
  jobLines,
  errors,
  warnings,
  disableInvoiceConfirmationPage,
  roDetails,
  roId,
  viewType,
  defaultSubPayType,
  payerPayTypeConfiguration,
  showAddEditDeductibleModal,
  payTypeConfigurations,
  onAction,
  repairOrderDetails,
  showChorusProModal,
  showAdditionalPayerModal,
  actionOnParent,
  currentPayerId,
  currentCustomerReferenceNumber,
  onCustomerReferenceNumberUpdate,
  siretCodeDetails,
  serviceContracts,
  ...rest
}) => ({
  [INVOICE_CONFIRMATION_PAGE_FIELD_IDS.WARNINGS_AND_LIKES_SECTION]: {
    id: INVOICE_CONFIRMATION_PAGE_FIELD_IDS.WARNINGS_AND_LIKES_SECTION,
    renderer: WarningsAndLikesSection,
    renderOptions: { showWarningAndErrorSection: true, errors, warnings, roId },
  },
  [PAYERS_CONSOLIDATED_FIELD_IDS.ADD_EDIT_DEDUCTIBLE]: {
    id: PAYERS_CONSOLIDATED_FIELD_IDS.ADD_EDIT_DEDUCTIBLE,
    renderer: AddEditDeductibleButton,
    renderOptions: {
      onAction,
      showAddEditDeductibleModal,
      roDetails: repairOrderDetails,
      payTypeConfigurations,
      payerPayTypeConfiguration,
      defaultSubPayType,
    },
  },
  [PAYERS_CONSOLIDATED_FIELD_IDS.MANAGE_EXCESS]: {
    id: PAYERS_CONSOLIDATED_FIELD_IDS.MANAGE_EXCESS,
    renderer: ManageExcess,
    renderOptions: {
      roPayersById: rest.roPayersById,
      roDetails,
      jobDetails: jobLines,
      viewType,
      roId,
      paySplit: rest.paySplit,
      disabled: disableInvoiceConfirmationPage,
      repairOrderDetails,
      serviceContracts,
    },
  },
  [PAYERS_CONSOLIDATED_FIELD_IDS.ADD_EDIT_COUPON]: {
    id: PAYERS_CONSOLIDATED_FIELD_IDS.ADD_EDIT_COUPON,
    renderer: AddEditCoupon,
    renderOptions: {
      roDetails,
      jobDetails: jobLines,
      viewType,
      buttonClassName: styles.addEditCouponButton,
      disabled: disableInvoiceConfirmationPage,
    },
  },
  [PAYERS_CONSOLIDATED_FIELD_IDS.CHORUS_PRO_INVOICING_MODAL]: {
    id: PAYERS_CONSOLIDATED_FIELD_IDS.CHORUS_PRO_INVOICING_MODAL,
    renderer: ChorusProInvoicingModal,
    renderOptions: {
      visible: showChorusProModal,
      siretCodeDetails,
    },
  },
  [PAYERS_CONSOLIDATED_FIELD_IDS.ADDITIONAL_PAYER_DETAILS]: {
    id: PAYERS_CONSOLIDATED_FIELD_IDS.ADDITIONAL_PAYER_DETAILS,
    renderer: AdditionalPayerDetails,
    renderOptions: {
      visible: showAdditionalPayerModal,
      actionOnParent,
      currentPayerId,
      currentCustomerReferenceNumber,
      roDetails,
      roPayersById: rest?.roPayersById,
      onSubmit: onCustomerReferenceNumberUpdate,
    },
  },
  ...getPayerFields({
    viewType,
    jobLines,
    roDetails,
    onAction,
    payTypeConfigurations,
    ...rest,
  }),
});
