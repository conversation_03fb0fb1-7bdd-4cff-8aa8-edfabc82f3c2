import _get from 'lodash/get';
import _keys from 'lodash/keys';
import _curry from 'lodash/curry';
import _forEach from 'lodash/forEach';
import _isEmpty from 'lodash/isEmpty';
import _head from 'lodash/head';
import _reduce from 'lodash/reduce';
import _set from 'lodash/set';

import { toaster, TOASTER_TYPE } from 'tcomponents/organisms/NotificationWrapper';
import { getErrorMessage } from 'tbase/utils/errorUtils';
import CustomerReader from 'tbase/readers/Customer';
import { tget } from 'tbase/utils/general';
import RepairOrderEnv from 'utils/repairOrderEnv';
import { EMPTY_OBJECT, EMPTY_STRING } from 'tbase/app.constants';

import { CLAIM_FIELDS, EXCESS_FORM_SECTIONS, PAYER_FIELDS, RECHARGE_FIELDS } from './ManageExcess.constants';

const mapCostCenters = costCentres =>
  costCentres.map(costCentreItem => ({
    ...costCentreItem,
    controlTags: [
      {
        refType: _get(costCentreItem, 'control'),
        refText: _get(costCentreItem, 'control2'),
      },
    ],
  }));

const mapPayerValues = (payerId, payerData, costCenters, amount = null) => ({
  payerId,
  ...(amount !== null && { amount }),
  payerNo: _get(payerData, 'displayId'),
  firstName: _get(payerData, 'firstName'),
  lastName: _get(payerData, 'lastName'),
  defaultPayerCostCenter: tget(payerData, ['setup', 'service', 'defaultCostCenter']),
  roLevelDiscount: tget(payerData, ['setup', 'service', 'serviceDiscount']),
  clientCategory: CustomerReader.clientCategory(payerData) || EMPTY_STRING,
  inheritFromClientCategory: CustomerReader.inheritPayTypeFromClientCategory(payerData),
  costCenters,
});

export const getIsDeductibleMetaFieldMandatory = () =>
  _get(RepairOrderEnv, 'settingsBySiteId.deductibleMetaFieldMandatory', true);

const checkIfValidFormValues = _curry((isDeductibleMetaFieldMandatory, values, payerId) => {
  const claimForm = _get(values, [payerId, EXCESS_FORM_SECTIONS.CLAIM_SECTION]);
  const payerForm = _get(values, [payerId, EXCESS_FORM_SECTIONS.EXCESS_PAYER_SECTION]);
  const rechargeForm = _get(values, [payerId, EXCESS_FORM_SECTIONS.RECHARGE_SECTION]);

  const claim = _get(claimForm, CLAIM_FIELDS.CLAIM);
  const policy = _get(claimForm, CLAIM_FIELDS.POLICY);

  if (isDeductibleMetaFieldMandatory && _isEmpty(claim)) throw new Error('Missing claim field!');
  if (isDeductibleMetaFieldMandatory && _isEmpty(policy)) throw new Error('Missing policy field!');

  const excessPayer = _get(payerForm, PAYER_FIELDS.PAYER);
  const rechargePayer = _get(rechargeForm, RECHARGE_FIELDS.PAYER);

  if (_isEmpty(excessPayer) && _isEmpty(rechargePayer)) throw new Error('Please select one payer!');
});

export const validateMandatoryFields = values => {
  try {
    if (_isEmpty(values)) throw new Error('Missing fields data !');

    const isDeductibleMetaFieldMandatory = getIsDeductibleMetaFieldMandatory();

    const payerIds = _keys(values);

    _forEach(payerIds, checkIfValidFormValues(isDeductibleMetaFieldMandatory, values));

    return { isValid: true };
  } catch (error) {
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(error, __('Failed to submit Manage Excess Form!')));

    return { isValid: false };
  }
};

export const getInitialFormValues = serviceContracts => {
  if (_isEmpty(serviceContracts)) return [];
  return _reduce(
    serviceContracts,
    (acc, contract) => {
      const { claimNumber, contractNumber, contractPayerId, deductiblePayers, taxPayers } = contract;

      _set(acc, [contractPayerId, 'claimValues'], { claim: claimNumber, policy: contractNumber });

      // Process deductible payers (excess payers)
      const excessPayer = _head(deductiblePayers) || EMPTY_OBJECT;
      _set(acc, [contractPayerId, 'excessPayerValues'], {
        [PAYER_FIELDS.PAYER]: excessPayer.payerId,
        [PAYER_FIELDS.AMOUNT]: excessPayer.amount,
        [PAYER_FIELDS.COST_CENTRE]: excessPayer.costCenters,
      });

      // Process tax payers (recharge VAT payers)
      const taxPayer = _head(taxPayers) || EMPTY_OBJECT;
      _set(acc, [contractPayerId, 'rechargeValues'], {
        [RECHARGE_FIELDS.PAYER]: taxPayer.payerId,
        [RECHARGE_FIELDS.AMOUNT]: taxPayer.amount,
        [RECHARGE_FIELDS.COST_CENTRE]: taxPayer.costCenters,
      });
      return acc;
    },
    {}
  );
};

export function getPayloadForManageExcess(allFormValues, selectedExcessAndRechargePayers, roPayersById) {
  const serviceContracts = [];

  _forEach(allFormValues, (forms, payerId) => {
    if (selectedExcessAndRechargePayers.has(payerId)) return;
    const claimForm = _get(forms, EXCESS_FORM_SECTIONS.CLAIM_SECTION);
    const payerForm = _get(forms, EXCESS_FORM_SECTIONS.EXCESS_PAYER_SECTION);
    const rechargeForm = _get(forms, EXCESS_FORM_SECTIONS.RECHARGE_SECTION);

    const payer = _get(roPayersById, payerId);
    if (!payer) return;

    const excessPayerId = _get(payerForm, PAYER_FIELDS.PAYER);
    const rechargePayerId = _get(rechargeForm, RECHARGE_FIELDS.PAYER);
    const deductibleAmount = parseFloat(_get(payerForm, PAYER_FIELDS.AMOUNT, 0));

    const excessPayerData = _get(payerForm, 'selectedCustomerData');
    const rechargePayerData = _get(rechargeForm, 'selectedCustomerData');

    let deductiblePayers = [];
    if (excessPayerId) {
      const excessCostCentres = _get(payerForm, PAYER_FIELDS.COST_CENTRE, []);
      const costCentersValues = mapCostCenters(excessCostCentres);

      deductiblePayers = [
        ...deductiblePayers,
        mapPayerValues(excessPayerId, excessPayerData, costCentersValues, deductibleAmount),
      ];
    }

    let taxPayers = [];
    if (rechargePayerId) {
      const rechargeCostCentres = _get(rechargeForm, RECHARGE_FIELDS.COST_CENTRE, []);
      const costCenterValues = mapCostCenters(rechargeCostCentres);

      taxPayers = [...taxPayers, mapPayerValues(rechargePayerId, rechargePayerData, costCenterValues)];
    }

    if (deductiblePayers.length > 0 || taxPayers.length > 0) {
      serviceContracts.push({
        claimNumber: _get(claimForm, CLAIM_FIELDS.CLAIM),
        contractNumber: _get(claimForm, CLAIM_FIELDS.POLICY),
        contractPayerId: _get(payer, 'payerId'),
        contractPayerSubPayType: _get(payer, 'subPayType'),
        deductiblePayers,
        taxPayers,
      });
    }
  });

  return {
    serviceContracts,
  };
}
