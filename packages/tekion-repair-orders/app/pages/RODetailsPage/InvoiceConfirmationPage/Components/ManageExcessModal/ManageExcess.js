import React, { useCallback, useEffect } from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';
import _noop from 'lodash/noop';

import { EMPTY_OBJECT, EMPTY_STRING, EMPTY_ARRAY } from 'tbase/app.constants';
import Button from 'tcomponents/atoms/Button';
import Modal from 'tcomponents/molecules/Modal';
import withActionHandlers from 'tcomponents/connectors/withActionHandlers';
import Constraints from 'helpers/constraints';
import ManageExcessForm from './ManageExcessForm';
import ACTION_HANDLERS from './ManageExcess.actionHandlers';
import { ACTION_TYPE, PARENT_FORM_CONTEXT_ID } from './ManageExcess.constants';

const ManageExcess = props => {
  const {
    onAction,
    values,
    roPayersById,
    formSubmitted,
    isModalVisible,
    paySplit,
    roId,
    selectedExcessAndRechargePayers,
    serviceContracts,
  } = props;

  const onSubmit = useCallback(() => {
    onAction({
      type: ACTION_TYPE.INITIATE_SUBMISSION,
      payload: { roId, roPayersById },
    });
  }, [onAction, roPayersById, roId]);

  const onCancel = useCallback(() => {
    onAction({ type: ACTION_TYPE.ON_CANCEL });
  }, [onAction]);

  const showModal = useCallback(() => {
    onAction({ type: ACTION_TYPE.SHOW_MODAL });
  }, [onAction]);

  useEffect(() => {
    onAction({ type: ACTION_TYPE.INIT_FORM });
  }, [onAction]);

  return (
    <>
      {Constraints.isExcessPayerEnabled() && (
        <div className={cx('m-r-20')}>
          <Button onClick={showModal}>{__('$$(Manage Excess)')}</Button>
        </div>
      )}
      {isModalVisible && (
        <Modal
          visible={isModalVisible}
          destroyOnClose
          onSubmit={onSubmit}
          onCancel={onCancel}
          width={Modal.SIZES.L}
          title={__('$$(Manage Excess)')}>
          <ManageExcessForm
            onAction={onAction}
            payers={roPayersById}
            values={values}
            formSubmitted={formSubmitted}
            paySplit={paySplit}
            selectedExcessAndRechargePayers={selectedExcessAndRechargePayers}
            serviceContracts={serviceContracts}
          />
        </Modal>
      )}
    </>
  );
};

ManageExcess.propTypes = {
  roPayersById: PropTypes.object,
  onAction: PropTypes.func,
  formSubmitted: PropTypes.bool,
  isModalVisible: PropTypes.bool,
  values: PropTypes.object,
  paySplit: PropTypes.object,
  roId: PropTypes.string,
  selectedExcessAndRechargePayers: PropTypes.object,
  serviceContracts: PropTypes.array,
};

ManageExcess.defaultProps = {
  roPayersById: EMPTY_OBJECT,
  onAction: _noop,
  formSubmitted: false,
  isModalVisible: false,
  values: EMPTY_OBJECT,
  paySplit: EMPTY_OBJECT,
  roId: EMPTY_STRING,
  selectedExcessAndRechargePayers: new Set(),
  serviceContracts: EMPTY_ARRAY,
};

export default withActionHandlers(ACTION_HANDLERS, {
  formSubmitted: false,
  contextId: PARENT_FORM_CONTEXT_ID,
  values: EMPTY_OBJECT,
  isModalVisible: false,
  selectedExcessAndRechargePayers: new Set(),
})(ManageExcess);
