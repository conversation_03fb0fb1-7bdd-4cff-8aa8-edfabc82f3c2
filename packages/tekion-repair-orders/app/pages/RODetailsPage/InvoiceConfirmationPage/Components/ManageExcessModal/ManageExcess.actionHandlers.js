import _set from 'lodash/set';
import _get from 'lodash/get';
import { produce } from 'immer';

import FORM_ACTION_TYPES from 'tcomponents/organisms/FormBuilder/constants/actionTypes';
import { triggerSubmit } from 'tcomponents/pages/formPage/utils/formAction';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import getDataFromResponse from 'tbase/utils/getDataFromResponse';
import Http from 'tbase/services/apiService/httpClient';
import { URL_TYPES } from 'tbase/constants/api';
import { getErrorMessage } from 'tbase/utils/errorUtils';

import {
  ACTION_TYPE,
  EXCESS_FORM_SECTIONS,
  PAYER_FIELDS,
  MANAGE_EXCESS_FORM_CONTEXT_ID,
  RECHARGE_FIELDS,
} from './ManageExcess.constants';

import { getInitialFormValues, getPayloadForManageExcess } from './ManageExcess.helpers';
import { getSelectedExcessAndRechargePayers } from './ManageExcessForm/ManageExcessForm.helpers';

const updateServiceContractDetails = (roId, payload) => {
  const apiUrl = `service-module/u/ros/${roId}/contract-details`;
  return Http.put(URL_TYPES.CDMS, apiUrl, payload).then(getDataFromResponse);
};

const handleFieldChange = (params, { setState, getState }) => {
  const { id, value, payerId, formSection, selectedCustomerData } = params.payload;

  setState(
    produce(getState(), draft => {
      // Update the form field value
      _set(draft.values, [payerId, formSection, id], value);

      // Copy excess payer value to recharge payer if excess payer is selected
      const rechargePayerValue = _get(draft.values, [
        payerId,
        EXCESS_FORM_SECTIONS.RECHARGE_SECTION,
        RECHARGE_FIELDS.PAYER,
      ]);
      if (id === PAYER_FIELDS.PAYER && !rechargePayerValue) {
        _set(draft.values, [payerId, EXCESS_FORM_SECTIONS.RECHARGE_SECTION, RECHARGE_FIELDS.PAYER], value);
      }

      // Update selected excess and recharge payers
      if (id === PAYER_FIELDS.PAYER || id === RECHARGE_FIELDS.PAYER) {
        _set(draft, 'selectedExcessAndRechargePayers', getSelectedExcessAndRechargePayers(draft.values));
        _set(draft.values, [payerId, formSection, 'selectedCustomerData'], selectedCustomerData);
      }
    })
  );
};

const handleCancel = (_, { setState }) => {
  setState({ isModalVisible: false });
};

const handleInitiateSubmission = (_, { getState, setState }) => {
  const { formSubmitted } = getState();
  setState({ formSubmitted: !formSubmitted }, () => {
    triggerSubmit(MANAGE_EXCESS_FORM_CONTEXT_ID);
  });
};

const handleSubmit = async (params, { getState, setState, dispatch }) => {
  const { values, roId, roPayersById, toggleFetchAfterManageExcessSelection, selectedExcessAndRechargePayers } = getState();
  const payload = getPayloadForManageExcess(values, selectedExcessAndRechargePayers, roPayersById);
  const { serviceContracts } = payload;
  const apiPayload = { serviceContracts };
  try {
    await updateServiceContractDetails(roId, apiPayload);
    dispatch(toggleFetchAfterManageExcessSelection());
  } catch (error) {
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(error, __('Failed to submit manage excess details')));
  }
};

const handleShowModal = (_params, { setState }) => {
  setState({ isModalVisible: true });
};

const handleInitForm = (_params, { getState, setState }) => {
  const { serviceContracts = [] } = getState();
  const initialFormValues = getInitialFormValues(serviceContracts);
  setState({
    values: initialFormValues,
    selectedExcessAndRechargePayers: getSelectedExcessAndRechargePayers(initialFormValues),
    formSubmitted: false,
  });
};

const handleSetFieldWithError = (params, { setState, getState }) => {
  const { id, value, formSection, payerId } = params.payload;

  setState(
    produce(getState(), draft => {
      _set(draft.values, [payerId, formSection, id], value);
    })
  );
};

const handleValidationSuccess = ({ setState, params }) => {
  const { errors } = params;
  setState({ errors });
};

const ACTION_HANDLERS = {
  [ACTION_TYPE.ON_CANCEL]: handleCancel,
  [FORM_ACTION_TYPES.ON_FIELD_CHANGE]: handleFieldChange,
  [ACTION_TYPE.INITIATE_SUBMISSION]: handleInitiateSubmission,
  [FORM_ACTION_TYPES.ON_FORM_SUBMIT]: handleSubmit,
  [ACTION_TYPE.SHOW_MODAL]: handleShowModal,
  [FORM_ACTION_TYPES.VALIDATION_SUCCESS]: handleValidationSuccess,
  [ACTION_TYPE.INIT_FORM]: handleInitForm,
  [ACTION_TYPE.SET_FIELD_WITH_ERROR]: handleSetFieldWithError,
};

export default ACTION_HANDLERS;
