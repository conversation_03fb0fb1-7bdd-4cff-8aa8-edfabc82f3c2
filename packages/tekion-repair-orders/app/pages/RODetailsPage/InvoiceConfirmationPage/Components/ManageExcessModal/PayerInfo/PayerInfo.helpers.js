import _get from 'lodash/get';
import _filter from 'lodash/filter';

import ROConstraints from 'helpers/constraints';
import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';

import { getTaxAmount } from '../../TaxBreakdownTable/TaxBreakdown.helper';
import { EXCESS_FORM_SECTIONS, RECHARGE_FIELDS } from '../ManageExcess.constants';
import { getPayersSections } from './PayerInfo.section';

export const getPaySplitForPayer = (paySplit, payerId, subPayType) => {
  const isMultiPayerEnabled = _get(ROConstraints, 'isServiceV3Enabled', () => false)();
  return isMultiPayerEnabled
    ? _filter(
        paySplit || EMPTY_ARRAY,
        ({ payerId: pId, subPayType: sType } = EMPTY_OBJECT) => pId === payerId && sType === subPayType
      )
    : EMPTY_ARRAY;
};

export const getPayerInfoFormValues = (values, payer, paySplit) => {
  const { payerId, subPayType } = payer || EMPTY_OBJECT;

  const claimFormValues = _get(values, EXCESS_FORM_SECTIONS.CLAIM_SECTION);
  const payerFormValues = _get(values, EXCESS_FORM_SECTIONS.EXCESS_PAYER_SECTION);

  const paySplitForPayer = getPaySplitForPayer(paySplit, payerId, subPayType);
  const payersTax = getTaxAmount(paySplitForPayer);

  const rechargeFormValues = {
    ..._get(values, EXCESS_FORM_SECTIONS.RECHARGE_SECTION),
    [RECHARGE_FIELDS.AMOUNT]: payersTax,
  };

  return {
    ...claimFormValues,
    ...payerFormValues,
    ...rechargeFormValues,
  };
};

export const getPayerInfoFormSections = payer => {
  const payerId = _get(payer, 'payerId');

  return getPayersSections(payerId);
};

export const validatePayerAmount = () => (...args) => {
  console.log("params == ", args);
  return { isValid: true };
};
