import _mapValues from 'lodash/mapValues';
import _get from 'lodash/get';

import { TextInput } from 'tcomponents/organisms/FormBuilder/fieldRenderers';
import CostCenter from 'twidgets/appServices/service/organisms/CostCenter';
import CurrencyInput from '@tekion/tekion-widgets/src/fieldRenderers/currencyInputField';
import PayerSelect from 'twidgets/appServices/service/molecules/PayerPayTypeSelect/PayerSelect';
import { isRequiredRule } from 'tbase/utils/formValidators';
import { EMPTY_OBJECT } from 'tbase/app.constants';

import { PAYMENT_METHOD_VALUE } from 'utils/constants';
import { validatePayerAmount, getPaySplitForPayer } from './PayerInfo.helpers';
import { getTaxAmount } from '../../TaxBreakdownTable/TaxBreakdown.helper';
import {
  CLAIM_FIELDS,
  COLLECT_AT_CASHIERING,
  PAYER_FIELDS,
  RECHARGE_FIELDS,
  EXCESS_FORM_SECTIONS,
} from '../ManageExcess.constants';

import { getIsDeductibleMetaFieldMandatory } from '../ManageExcess.helpers';

export const getformFieldIds = payer => {
  const { payerId } = payer || EMPTY_OBJECT;

  const isDeductibleMetaFieldMandatory = getIsDeductibleMetaFieldMandatory();

  const claimPolicyFieldObj = getClaimPolicyField(isDeductibleMetaFieldMandatory);
  const payerFieldsObj = getExcessFormFields(payerId);
  const rechargeFieldsObj = getRechargeFormFields({}, { payerId });

  const claimFieldsMapping = _mapValues(claimPolicyFieldObj, () => EXCESS_FORM_SECTIONS.CLAIM_SECTION);
  const payerFieldsMapping = _mapValues(payerFieldsObj, () => EXCESS_FORM_SECTIONS.EXCESS_PAYER_SECTION);
  const rechargeFieldsMapping = _mapValues(rechargeFieldsObj, () => EXCESS_FORM_SECTIONS.RECHARGE_SECTION);

  return {
    ...claimFieldsMapping,
    ...payerFieldsMapping,
    ...rechargeFieldsMapping,
  };
};

export const getPayerInfoFormFields = (values, payer, paySplit) => {
  const { payerId, subPayType } = payer || EMPTY_OBJECT;

  const isDeductibleMetaFieldMandatory = getIsDeductibleMetaFieldMandatory();

  const paySplitForPayer = getPaySplitForPayer(paySplit, payerId, subPayType);
  const payersTax = getTaxAmount(paySplitForPayer);

  const rechargeFormValues = {
    ..._get(values, EXCESS_FORM_SECTIONS.RECHARGE_SECTION),
    [RECHARGE_FIELDS.AMOUNT]: payersTax,
  };

  const rechargeContext = {
    [PAYER_FIELDS.PAYER]: _get(values, [EXCESS_FORM_SECTIONS.EXCESS_PAYER_SECTION, PAYER_FIELDS.PAYER]),
    payerId,
  };

  const claimPolicyFields = getClaimPolicyField(isDeductibleMetaFieldMandatory);
  const payerFields = getExcessFormFields(payerId);
  const rechargeFields = getRechargeFormFields(rechargeFormValues, rechargeContext);

  return {
    ...claimPolicyFields,
    ...payerFields,
    ...rechargeFields,
  };
};

export const getClaimPolicyField = (isMandatory = false) => ({
  [CLAIM_FIELDS.CLAIM]: {
    renderer: TextInput,
    renderOptions: {
      label: 'Claim #',
      size: 6,
      placeholder: 'Claim #',
      required: isMandatory,
      validators: isMandatory ? [isRequiredRule] : [],
    },
  },
  [CLAIM_FIELDS.POLICY]: {
    renderer: TextInput,
    renderOptions: {
      label: 'Policy #',
      size: 6,
      placeholder: 'Policy #',
      required: isMandatory,
      validators: isMandatory ? [isRequiredRule] : [],
    },
  },
});

export const getExcessFormFields = payerId => {
  const handleFilterOption = option => {
    if (option.value === payerId) return undefined;
    return option;
  };

  return {
    [PAYER_FIELDS.PAYER]: {
      renderer: PayerSelect,
      renderOptions: {
        label: __('Payer'),
        size: 6,
        shouldShowFieldLabel: true,
        validators: [isRequiredRule],
        filterOption: handleFilterOption,
      },
    },
    [PAYER_FIELDS.AMOUNT]: {
      renderer: CurrencyInput,
      renderOptions: {
        label: __('Amount'),
        size: 6,
        validators: [validatePayerAmount({ fieldId: PAYER_FIELDS.AMOUNT })],
      },
    },
    [PAYER_FIELDS.COST_CENTRE]: {
      renderer: CostCenter,
      renderOptions: {
        size: 12,
        columnsToOmit: [],
        showControlColumns: true,
        canAddCostCenter: true,
        costCenterSplitType: PAYMENT_METHOD_VALUE.PERCENTAGE,
        options: [{ label: __('Collect at Cashiering'), value: COLLECT_AT_CASHIERING }],
      },
    },
  };
};

export const getRechargeFormFields = (values, context) => {
  const currentPayerId = context.payerId;
  const payerFieldId = RECHARGE_FIELDS.PAYER;
  const rechargePayer = values[payerFieldId];
  const excessPayer = context?.excessPayer;

  const shouldDisableCostCenter = rechargePayer === excessPayer || rechargePayer === currentPayerId;

  return {
    [RECHARGE_FIELDS.PAYER]: {
      renderer: PayerSelect,
      renderOptions: {
        label: __('Payer'),
        shouldShowFieldLabel: true,
        validators: [isRequiredRule],
        size: 6,
      },
    },
    [RECHARGE_FIELDS.AMOUNT]: {
      renderer: CurrencyInput,
      renderOptions: {
        label: __('Amount'),
        size: 6,
        disabled: true,
        validators: [validatePayerAmount({ fieldId: RECHARGE_FIELDS.AMOUNT })],
      },
    },
    [RECHARGE_FIELDS.COST_CENTRE]: {
      renderer: CostCenter,
      renderOptions: {
        size: 12,
        disabled: shouldDisableCostCenter,
        columnsToOmit: [],
        showControlColumns: true,
        canAddCostCenter: true,
        costCenterSplitType: PAYMENT_METHOD_VALUE.PERCENTAGE,
        options: [{ label: __('Collect at Cashiering'), value: COLLECT_AT_CASHIERING }],
      },
    },
  };
};
