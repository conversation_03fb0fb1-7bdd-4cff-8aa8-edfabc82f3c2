import React from 'react';
import { connect } from 'react-redux';
import { compose } from 'recompose';

import withFormPageState from 'tcomponents/connectors/withFormPageState';
import { tget } from 'tbase/utils/general';
import { EMPTY_OBJECT } from 'tbase/app.constants';

import { getInvoiceConfirmationReducerKey } from 'twidgets/appServices/service/helpers/reducer.helper';
import {
  getAccountingTaxCodes,
  getPayTypeConfigurations,
} from 'twidgets/appServices/service/helpers/ROContainer.selectors';

import ConsolidatedTab from '../ConsolidatedTab/ConsolidatedTab';
import ACTION_HANDLERS from './PayersConsolidatedTab.actionHandlers';
import { ACTION_HANDLERS as CONSOLIDATED_TAB_ACTION_HANDLERS } from '../ConsolidatedTab/ConsolidatedTab.actionHandler';
import { getSanitizedInvoiceConfirmationData } from '../../../InvoiceConfirmationPage.helper';

const PayersConsolidatedTab = props => <ConsolidatedTab {...props} />;

function mapStateToProps(state) {
  const { jobs, ro, couponDistributions, isFetching, postingSetting, serviceContracts } = tget(
    state,
    `${getInvoiceConfirmationReducerKey()}`,
    EMPTY_OBJECT
  );
  return {
    repairOrderDetails: getSanitizedInvoiceConfirmationData(jobs, ro, couponDistributions),
    payTypeConfigurations: getPayTypeConfigurations(state),
    loading: isFetching,
    couponDistributions,
    accountingTaxCodes: getAccountingTaxCodes(state),
    postingSetting,
    serviceContracts,
  };
}

export default compose(
  connect(mapStateToProps),
  withFormPageState(EMPTY_OBJECT, { ...CONSOLIDATED_TAB_ACTION_HANDLERS, ...ACTION_HANDLERS })
)(PayersConsolidatedTab);
