import React, { useCallback, useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';

import _noop from 'lodash/noop';
import _isEmpty from 'lodash/isEmpty';
import _keyBy from 'lodash/keyBy';

import { PropertyControlledComponent } from 'tcomponents/molecules';
import Loader from 'tcomponents/molecules/loader';
import Spinner from 'tcomponents/molecules/SpinnerComponent';
import SaveComponent from 'tcomponents/molecules/SaveComponent';
import FormPage from 'tcomponents/pages/formPage';
import { triggerSubmit } from 'tcomponents/pages/formPage/utils/formAction';
import RepairOrderReader from 'tbusiness/appServices/service/readers/RepairOrder';
import { PAYER_FIELDS } from 'tbusiness/appServices/service/readers/Payer';
import { PAY_TYPE_VALUES } from 'tbase/constants/payTypes';
import { PAYER_STATUS_VALUES } from 'tbase/constants/repairOrder/payerStatus';
import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from 'tbase/app.constants';

import { isValidPayerStatus } from 'pages/RODetailsPage/ROHeader/RightSection/RightSection.helper';
import { JOB_COMPLETE_DISABLED_MESSAGE } from 'organisms/ROForm/ROForm.constants';
import ROReader from 'readers/RepairOrder.reader';
import JobsReader from 'readers/Jobs.reader';
import ROConstraints from 'helpers/constraints';
import { isROReadyForInvoice, shouldDisableInvoiceCTA } from 'helpers/repairOrder.helper';
import { getApplicablePayersByModuleType } from 'helpers/payer.helper';

import PrintNotificationConfirmation from './Components/PrintNotificationConfirmation';
import {
  VIEW_TYPE_VS_FIELDS_GETTER,
  VIEW_TYPE_VS_SECTION_GETTER,
  VIEW_TYPE_VS_FORM_DISABILITY,
  VIEW_TYPE_VS_INVOICE_BUTTON_LABEL,
} from '../../../InvoiceConfirmationPage.factory';
import { getJobDetailsFromJobLines, getApplicableResults } from '../../../InvoiceConfirmationPage.helper';
import {
  getErrorsAndWarnings,
  getROEntitiesAmount,
  getSanitizedPayers,
  handleInvoiceRO,
  handleToggleNotifyCustomer,
  handleTogglePrintInvoice,
  isROLevelErrorsPresent,
  isViewTypeConsolidatedTab,
} from './ConsolidatedTab.helper';
import { arePayersSelectedForInvoice } from '../PayersConsolidatedTab/PayersConsolidatedTab.helper';
import { INVOICE_CONFIRMATION_VIEW_TYPES, TAB_HEIGHT } from '../../../InvoiceConfirmationPage.constants';
import {
  LOCAL_ACTIONS,
  TAX_BREAK_DOWN_HEIGHT_CONSOLIDATED_VIEW,
  TAX_BREAK_DOWN_HEIGHT_PAYERS_VIEW,
} from './ConsolidatedTab.constant';
import ADDITIONAL_PAYER_DETAILS_ACTION_TYPES from '../../AdditionalPayerDetails/constants/AdditionalPayerDetailsModal.constants';

import styles from './ConsolidatedTab.module.scss';
import TaxBreakdownTable from '../../TaxBreakdownTable';

const ConsolidatedTab = props => {
  const {
    contentHeight,
    onAction,
    formValues,
    loading,
    roId,
    isPrimaryDisabled,
    couponDistributions,
    shouldNotifyCustomerOnInvoice,
    shouldPrintInvoice,
    isTransientLoading,
    disableInvoiceConfirmationPage,
    dispatch,
    invoiceSelectionData,
    viewType,
    selectedPayType,
    selectedPayerId,
    showAddEditDeductibleModal,
    accountingTaxCodes,
    payersOptions,
    payTypeConfigurations,
    roDetails,
    isModificationDisabled,
    isSecondaryDisabled,
    payTypeOptions,
    payerPayTypeConfiguration,
    defaultSubPayType,
    ctaLockDetails,
    repairOrderDetails: repairOrderDetailsFromProps,
    showChorusProModal,
    showAdditionalPayerModal,
    currentPayerId,
    currentCustomerReferenceNumber,
    siretCodeDetails,
    admPayerDetails,
    serviceContracts,
    toggleFetchAfterManageExcessSelection,
  } = props;

  const roApplicablePayers = useMemo(
    () =>
      getSanitizedPayers(
        getApplicablePayersByModuleType(ROReader.getPayers(ROReader.getRepairOrder(repairOrderDetailsFromProps)))
      ),
    [repairOrderDetailsFromProps]
  );
  const { currentRepairOrderDetails } = formValues;
  const { hasFormModified } = formValues;
  const repairOrderDetails = RepairOrderReader.getRepairOrder(currentRepairOrderDetails);
  const jobLines = JobsReader.getJobLines(currentRepairOrderDetails) || EMPTY_OBJECT;
  const jobDetails = getJobDetailsFromJobLines(jobLines);
  const roEntitiesAmount = useMemo(() => getROEntitiesAmount(repairOrderDetails), [repairOrderDetails]);
  const roApplicablePayersById = _keyBy(roApplicablePayers, PAYER_FIELDS.PAYER_ID);
  const paySplit = RepairOrderReader.getPaySplit(repairOrderDetails);

  useEffect(() => {
    onAction({ type: LOCAL_ACTIONS.INIT_FORM });
  }, [invoiceSelectionData, toggleFetchAfterManageExcessSelection, onAction]);

  const { errors, warnings } = useMemo(
    () => getErrorsAndWarnings({ formValues, jobDetails, roDetails, payers: roApplicablePayers }),
    [formValues, jobDetails, roDetails, roApplicablePayers]
  );
  const sections = useMemo(
    () => VIEW_TYPE_VS_SECTION_GETTER[viewType](roApplicablePayers),
    [viewType, roApplicablePayers]
  );

  const isROLevelIssuePresent = useMemo(
    () => isROLevelErrorsPresent({ formValues, roDetails, payers: roApplicablePayers }),
    [formValues, roDetails, roApplicablePayers]
  );

  const onCustomerReferenceNumberUpdate = useCallback(
    customerReferenceNumber => {
      onAction({
        type: ADDITIONAL_PAYER_DETAILS_ACTION_TYPES.UPDATE_CUSTOMER_REFERENCE_NUMBER_ON_SUBMIT,
        payload: { customerReferenceNumber, payerId: currentPayerId },
      });
    },
    [currentPayerId, onAction]
  );

  const fields = useMemo(
    () =>
      VIEW_TYPE_VS_FIELDS_GETTER[viewType]({
        jobLines,
        errors,
        warnings,
        roLevelFees: RepairOrderReader.getROLevelFees(repairOrderDetails),
        roDetails,
        couponDistributions,
        roId,
        roEntitiesAmount,
        viewType,
        selectedPayType,
        selectedPayerId,
        showAddEditDeductibleModal,
        accountingTaxCodes,
        payersOptions,
        paySplit,
        payTypeConfigurations,
        isModificationDisabled,
        roPayerTaxCodes: RepairOrderReader.getPayerTaxCodes(repairOrderDetails),
        payTypeOptions,
        roPayersById: roApplicablePayersById,
        disableInvoiceConfirmationPage,
        payerPayTypeConfiguration,
        defaultSubPayType,
        repairOrderDetails: ROReader.getRepairOrder(repairOrderDetailsFromProps),
        showChorusProModal,
        showAdditionalPayerModal,
        currentCustomerReferenceNumber,
        siretCodeDetails,
        admPayerDetails,
        actionOnParent: onAction,
        onCustomerReferenceNumberUpdate,
        currentPayerId,
        serviceContracts,
        toggleFetchAfterManageExcessSelection,
      }),
    [
      jobLines,
      errors,
      warnings,
      repairOrderDetails,
      couponDistributions,
      roId,
      viewType,
      selectedPayType,
      selectedPayerId,
      showAddEditDeductibleModal,
      accountingTaxCodes,
      payersOptions,
      payTypeConfigurations,
      roDetails,
      isModificationDisabled,
      payTypeOptions,
      roApplicablePayersById,
      disableInvoiceConfirmationPage,
      roEntitiesAmount,
      paySplit,
      payerPayTypeConfiguration,
      defaultSubPayType,
      repairOrderDetailsFromProps,
      showChorusProModal,
      showAdditionalPayerModal,
      siretCodeDetails,
      admPayerDetails,
      currentPayerId,
      currentCustomerReferenceNumber,
      onCustomerReferenceNumberUpdate,
      onAction,
      serviceContracts,
      toggleFetchAfterManageExcessSelection,
    ]
  );

  const isApplicableErrorsPresent = useMemo(() => !_isEmpty(getApplicableResults(errors, true)), [errors]);
  const renderPrintNotificationConformation = useCallback(
    () => (
      <PropertyControlledComponent controllerProperty={!disableInvoiceConfirmationPage}>
        <PrintNotificationConfirmation
          shouldNotifyCustomerOnInvoice={shouldNotifyCustomerOnInvoice}
          handleToggleNotifyCustomer={handleToggleNotifyCustomer(onAction)}
          shouldPrintInvoice={shouldPrintInvoice}
          isApplicableErrorsPresent={isApplicableErrorsPresent}
          hasFormModified={hasFormModified}
          handleTogglePrintInvoice={handleTogglePrintInvoice(onAction)}
          primaryCustomerId={ROReader.getPrimaryCustomerId(roDetails)}
          payers={ROReader.payers(roDetails)}
        />
      </PropertyControlledComponent>
    ),
    [
      hasFormModified,
      onAction,
      shouldNotifyCustomerOnInvoice,
      isApplicableErrorsPresent,
      shouldPrintInvoice,
      disableInvoiceConfirmationPage,
      roDetails,
    ]
  );

  const canInvoiceRO = useMemo(
    () =>
      ROConstraints.isServiceV3Enabled()
        ? isValidPayerStatus(repairOrderDetails, PAYER_STATUS_VALUES.READY_FOR_INVOICE)
        : isROReadyForInvoice(repairOrderDetails),
    [repairOrderDetails]
  );

  const onSaveAction = () => triggerSubmit(viewType);
  const taxBreakDownHeight = isViewTypeConsolidatedTab(viewType)
    ? TAX_BREAK_DOWN_HEIGHT_CONSOLIDATED_VIEW
    : TAX_BREAK_DOWN_HEIGHT_PAYERS_VIEW;

  const values = useMemo(
    () => (viewType === INVOICE_CONFIRMATION_VIEW_TYPES.PAYERS_CONSOLIDATED_VIEW ? formValues : EMPTY_OBJECT),
    [formValues, viewType]
  );

  const arePayersCheckedForInvoice = useMemo(
    () => arePayersSelectedForInvoice({ roApplicablePayers, formValues, viewType }),
    [viewType, roApplicablePayers, formValues]
  );

  if (loading && !isTransientLoading) return <Loader />;

  const shouldBlockCTA = shouldDisableInvoiceCTA({
    ctaLockDetails,
    payers: RepairOrderReader.getPayers(roDetails),
    roId,
    jobDetails,
    isMultiPayerEnabled: ROConstraints.isServiceV3Enabled(),
  });

  return (
    <div className="full-width">
      <FormPage
        fields={fields}
        sections={sections}
        onAction={onAction}
        headerComponent={null}
        isFormDisabled={VIEW_TYPE_VS_FORM_DISABILITY[viewType](disableInvoiceConfirmationPage)}
        dispatch={dispatch}
        values={values}
        footerComponent={() => (
          <>
            <PropertyControlledComponent controllerProperty={isViewTypeConsolidatedTab(viewType)}>
              <TaxBreakdownTable roEntitiesAmount={roEntitiesAmount} viewType={viewType} paySplit={paySplit} />
            </PropertyControlledComponent>
            <SaveComponent
              primaryButtonLabel={VIEW_TYPE_VS_INVOICE_BUTTON_LABEL[viewType]}
              secondaryButtonLabel={__('Save')}
              renderAdditionalFooterDetail={renderPrintNotificationConformation}
              onSecondaryAction={onSaveAction}
              onPrimaryAction={handleInvoiceRO(onAction, roApplicablePayers)}
              isPrimaryDisabled={
                isPrimaryDisabled ||
                isROLevelIssuePresent ||
                isApplicableErrorsPresent ||
                !canInvoiceRO ||
                disableInvoiceConfirmationPage ||
                shouldBlockCTA ||
                !arePayersCheckedForInvoice
              }
              isSecondaryDisabled={disableInvoiceConfirmationPage || isModificationDisabled || isSecondaryDisabled}
              isTertiaryDisabled={disableInvoiceConfirmationPage}
              isAdditionalDisabled={disableInvoiceConfirmationPage}
              primaryButtonIcon={shouldBlockCTA ? 'icon-lock-filled' : undefined}
              primaryButtonToolTipTitle={shouldBlockCTA ? JOB_COMPLETE_DISABLED_MESSAGE.PENDING_APPROVAL : EMPTY_STRING}
            />
          </>
        )}
        bodyProps={{ contentHeight: contentHeight - TAB_HEIGHT - taxBreakDownHeight }}
        loading={loading}
        contextId={viewType}
      />
      <PropertyControlledComponent controllerProperty={isTransientLoading}>
        <div className={`absolute full-width full-height ${styles.loadingContainer}`}>
          <Spinner id="couponApply" className={`absolute ${styles.hvCenter}`} />
        </div>
      </PropertyControlledComponent>
    </div>
  );
};

ConsolidatedTab.propTypes = {
  contentHeight: PropTypes.number,
  onAction: PropTypes.func,
  formValues: PropTypes.object,
  loading: PropTypes.bool,
  roId: PropTypes.string,
  isPrimaryDisabled: PropTypes.bool,
  couponDistributions: PropTypes.array,
  shouldNotifyCustomerOnInvoice: PropTypes.bool,
  shouldPrintInvoice: PropTypes.bool,
  disableInvoiceConfirmationPage: PropTypes.bool,
  isTransientLoading: PropTypes.bool,
  dispatch: PropTypes.func,
  invoiceSelectionData: PropTypes.array,
  viewType: PropTypes.string,
  selectedPayType: PropTypes.string,
  selectedPayerId: PropTypes.string,
  showAddEditDeductibleModal: PropTypes.bool,
  accountingTaxCodes: PropTypes.array,
  payersOptions: PropTypes.array,
  payTypeConfigurations: PropTypes.object,
  isModificationDisabled: PropTypes.bool,
  isSecondaryDisabled: PropTypes.bool,
  payTypeOptions: PropTypes.array,
  roDetails: PropTypes.object,
  repairOrderDetails: PropTypes.object,
  payerPayTypeConfiguration: PropTypes.object,
  defaultSubPayType: PropTypes.string,
  ctaLockDetails: PropTypes.array,
  showChorusProModal: PropTypes.bool,
  showAdditionalPayerModal: PropTypes.bool,
  currentPayerId: PropTypes.string,
  currentCustomerReferenceNumber: PropTypes.string,
  siretCodeDetails: PropTypes.object,
  admPayerDetails: PropTypes.object,
  toggleFetchAfterManageExcessSelection: PropTypes.bool,
};

ConsolidatedTab.defaultProps = {
  contentHeight: 0,
  onAction: _noop,
  formValues: EMPTY_OBJECT,
  loading: false,
  roId: EMPTY_STRING,
  isPrimaryDisabled: false,
  couponDistributions: EMPTY_ARRAY,
  shouldNotifyCustomerOnInvoice: false,
  shouldPrintInvoice: false,
  disableInvoiceConfirmationPage: false,
  isTransientLoading: false,
  dispatch: _noop,
  invoiceSelectionData: EMPTY_ARRAY,
  viewType: INVOICE_CONFIRMATION_VIEW_TYPES.CONSOLIDATED_VIEW,
  selectedPayType: PAY_TYPE_VALUES.CUSTOMER_PAY,
  selectedPayerId: EMPTY_STRING,
  showAddEditDeductibleModal: false,
  accountingTaxCodes: EMPTY_ARRAY,
  payersOptions: EMPTY_ARRAY,
  payTypeConfigurations: EMPTY_OBJECT,
  isModificationDisabled: false,
  isSecondaryDisabled: false,
  payTypeOptions: EMPTY_ARRAY,
  roDetails: EMPTY_OBJECT,
  repairOrderDetails: EMPTY_OBJECT,
  payerPayTypeConfiguration: EMPTY_OBJECT,
  defaultSubPayType: EMPTY_STRING,
  ctaLockDetails: EMPTY_ARRAY,
  showChorusProModal: false,
  showAdditionalPayerModal: false,
  currentPayerId: EMPTY_STRING,
  currentCustomerReferenceNumber: EMPTY_STRING,
  siretCodeDetails: EMPTY_OBJECT,
  admPayerDetails: EMPTY_OBJECT,
  toggleFetchAfterManageExcessSelection: true,
};

export default ConsolidatedTab;
