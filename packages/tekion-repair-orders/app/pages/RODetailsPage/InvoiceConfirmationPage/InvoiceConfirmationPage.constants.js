import { FEE_DATA_FIELD_NAMES } from 'tbase/constants/fee.constants';
import { BASE_PAY_TYPE } from 'tbase/constants/payTypes';

import { PAYERS_SELECTION_VIEW_TYPES as INVOICE_SELECTION_VIEW_TYPES } from 'constants/invoiceConfirmation.constants';

export const ENTITY_TYPE = 'entityType';
export const INIT_FORM = 'INIT_FORM';
export const ON_TAB_CHANGE = 'ON_TAB_CHANGE';
export const TABLE_ROW_CELL_VALUE_UPDATE = 'TABLE_ROW_CELL_VALUE_UPDATE';

export const LOCAL_ACTIONS = {
  ON_APPLY_COUPON: 'ON_APPLY_COUPON',
  BULK_PAY_TYPE_CLOSE: 'BULK_PAY_TYPE_CLOSE',
};

export const INVOICE_CONFIRMATION_ITEM_TYPE = 'INVOICE_CONFIRMATION_ITEM_TYPE';

export const tabStyle = { height: TAB_HEIGHT };

export const ENTITY_TYPES = {
  RO_FEES: 'RO_FEES',
  JOB: 'JOB',
  OPERATION: 'OPERATION',
  PART: 'PART',
  JOB_FEES: 'JOB_FEES',
  SUBLET: 'SUBLET',
  COUPONS: 'COUPONS',
  TOTAL_ROW: 'TOTAL_ROW',
  TAX_CODE: 'TAX_CODE',
};

export const FIELD_IDS = {
  PRE_TAX_COUPON_FIELD_ID: 'preTaxCoupon',
  POST_TAX_COUPON_FIELD_ID: 'postTaxCoupon',
  TAXABLE_AMOUNT_FIELD_ID: 'taxableAmount',
  ACTUAL_AMOUNT: 'actualAmount',
  CP_RO_ENTITIES_AMOUNT: 'cpROEntitiesAmount',
  WP_RO_ENTITIES_AMOUNT: 'wpROEntitiesAmount',
  IP_RO_ENTITIES_AMOUNT: 'ipROEntitiesAmount',
  PAYER: 'payer',
  PAY_TYPE: 'payType',
};
export const CURD_OPERATION_FIELD_NAMES = {
  ADDED_FEES: 'addedFees',
  DELETED_FEES: 'deletedFees',
  UPDATED_FEES: 'updatedFees',
  ADDED_COUPONS: 'addedCoupons',
  DELETED_COUPONS: 'deletedCoupons',
  UPDATED_COUPONS: 'updatedCoupons',
};

export const COUPON_APPLIED_ON_COMPONENTS = {
  LABOR: 'LABOR',
  PART: 'PART',
};

export const COUPON_DISTRIBUTION_TYPES = {
  OPERATION: 'OPERATION',
};

export const COUPON_LEVEL = {
  RO: 'RO',
  JOB: 'JOB',
};

export const INVOICE_CONFIRMATION_TAB_KEYS = {
  CONSOLIDATED: 'consolidated',
  PAYERS: 'payers',
  INVOICE_SELECTION: 'invoiceSelection',
  PAYER_CLOSING: 'payerClosing',
  RE_OPEN_PAYER: 'reOpenPayer',
  PAYERS_CONSOLIDATED: 'payersConsolidated',
};

export const TABS_CONFIG = {
  CONSOLIDATED: { key: INVOICE_CONFIRMATION_TAB_KEYS.CONSOLIDATED, label: __('Consolidated') },
  PAYERS: { key: INVOICE_CONFIRMATION_TAB_KEYS.PAYERS, label: __('Payers View') },
  INVOICE_SELECTION: { key: INVOICE_CONFIRMATION_TAB_KEYS.INVOICE_SELECTION, label: __('Payer Invoice Selection') },
  PAYER_CLOSING: { key: INVOICE_CONFIRMATION_TAB_KEYS.PAYER_CLOSING, label: __('Payer Closing') },
  RE_OPEN_PAYER: { key: INVOICE_CONFIRMATION_TAB_KEYS.RE_OPEN_PAYER, label: __('Reopen Payer') },
  PAYERS_CONSOLIDATED: {
    key: INVOICE_CONFIRMATION_TAB_KEYS.PAYERS_CONSOLIDATED,
    label: __('Payers Consolidated View'),
  },
};

export const TAB_HEIGHT = 60;

export const FEE_KEYS_TO_CHECK = [FEE_DATA_FIELD_NAMES.OVERIDDEN_SALE_AMOUNT];
export const COUPONS_KEYS_TO_CHECK = ['overriddenAmount'];

export const INVOICE_CONFIRMATION_VIEW_TYPES = {
  CONSOLIDATED_VIEW: 'CONSOLIDATED_VIEW',
  PAYERS_VIEW: 'PAYERS_VIEW',
  PAYERS_CONSOLIDATED_VIEW: 'PAYERS_CONSOLIDATED_VIEW',
};

export const PAYERS_SELECTION_VIEW_TYPES = INVOICE_SELECTION_VIEW_TYPES;

export const PAYERS_SELECTION_VIEW_TYPES_VS_HEADER = {
  [PAYERS_SELECTION_VIEW_TYPES.INVOICE]: __('Invoice following Payer(s)'),
  [PAYERS_SELECTION_VIEW_TYPES.CLOSE]: __('Close following Payer(s)'),
  [PAYERS_SELECTION_VIEW_TYPES.RE_OPEN]: __('Reopen following Payer(s)'),
};

export const PAYERS_SELECTION_VIEW_TYPES_VS_PRIMARY_BUTTON_LABEL = {
  [PAYERS_SELECTION_VIEW_TYPES.INVOICE]: __('Invoice Selected Payer(s)'),
  [PAYERS_SELECTION_VIEW_TYPES.CLOSE]: __('Close Selected Payer(s)'),
  [PAYERS_SELECTION_VIEW_TYPES.RE_OPEN]: __('Reopen Selected Payer(s)'),
};

export const TAX_CODE_FIELD_ID = 'TAX_CODE';

export const TAX_CODE_SPLIT_TYPES = {
  PARTS: 'PARTS',
  OPERATION: 'OPERATION',
  FEE: 'FEE',
  LABOR: 'LABOR',
};

export const SPLITS_FIELD_NAME = 'splits';
export const SPLIT_AMOUNT_FIELD_NAME = 'splitAmount';
export const COMPONENTS_FIELD_NAME = 'components';

export const NOTIFY_CUSTOMER_BY_SMS = 'notifyCustomerBySmsOnInvoice';
export const NOTIFY_CUSTOMER_BY_EMAIL = 'notifyCustomerByEmailOnInvoice';

export const BASE_PAY_TYPE_LABELS = {
  [BASE_PAY_TYPE.CUSTOMER_PAY]: __('C - Base $$(Customer)'),
  [BASE_PAY_TYPE.WARRANTY]: __('W - Base $$(Customer)'),
  [BASE_PAY_TYPE.INTERNAL]: __('I - Base $$(Customer)'),
};

export const PRINT_PDF_LABEL = __('Print PDF');
export const INVOICE_RO_LABEL = __('Invoice $$(RO)');

export const PAYERS_CONSOLIDATED_FIELD_IDS = {
  PAYER_CONSOLIDATED: 'PAYER_CONSOLIDATED',
  ADD_EDIT_COUPON: 'ADD_EDIT_COUPON',
  ADD_EDIT_DEDUCTIBLE: 'ADD_EDIT_DEDUCTIBLE',
  RO_TOTAL_TAX_BREAKDOWN: 'RO_TOTAL_TAX_BREAKDOWN',
  CHORUS_PRO_INVOICING_MODAL: 'CHORUS_PRO_INVOICING_MODAL',
  ADDITIONAL_PAYER_DETAILS: 'ADDITIONAL_PAYER_DETAILS',
  MANAGE_EXCESS: 'MANAGE_EXCESS',
};

export const LIST_OF_ASSET_TYPES = {
  PAYER: 'PAYER',
};

export const CHORUS_PRO_FIELD_IDS = {
  TYPOLOGY: 'TYPOLOGY',
  SIRET: 'SIRET',
  INVOICING_SIRET: 'INVOICING_SIRET',
  ADM: 'ADM',
};

export const CHORUS_PRO_PAYER_FIELD_IDS = [
  CHORUS_PRO_FIELD_IDS.TYPOLOGY,
  CHORUS_PRO_FIELD_IDS.SIRET,
  CHORUS_PRO_FIELD_IDS.INVOICING_SIRET,
];
