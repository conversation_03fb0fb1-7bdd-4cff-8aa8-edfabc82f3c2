/* eslint-disable consistent-return */
import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { compose } from 'recompose';

import _head from 'lodash/head';
import _noop from 'lodash/noop';

import withRouter from '@tekion/tekion-components/src/hoc/withRouter';
import withDocumentTitle from 'tcomponents/connectors/withDocumentTitle';
import withSize from '@tekion/tekion-components/src/hoc/withSize';
import withROEntityDetails from 'organisms/withROEntityDetails';
import GlobalAnalytics from 'tbase/utils/GlobalAnalytics';
import { tget } from 'tbase/utils/general';
import { CTA_LOCK_DETAILS_REDUCER_KEY } from 'tbusiness/appServices/service/constants/approvalFlow';

import {
  getRODetails,
  handlePayTypeStatusUpdate,
  resetRODetails,
  createROInvoice,
  updateOemSyncStatus,
  fetchPreviousRO,
  shouldHideIrisStatusBanner,
  setCTALockDetails,
  unsetStickyBannerDetails,
  fetchApprovalCount,
  setStickyBannerDetails,
} from 'actions/roDetails.actions';
import { updateJobStatus, updateJob, updateJobTags, handleDeductibleSplitSaveSuccess } from 'actions/jobDetails.action';
import {
  updateRecommendationStatus,
  updateBulkRecommendationStatus,
  createRecommendation,
  updateBulkRORecommendationStatus,
  setAISuggestionDetails,
  unsetAISuggestionDetails,
  handleRecommendationDeductibleSplitSaveSuccess,
} from 'actions/recommendation.action';
import { showDialog, hideDialog } from 'tcomponents/actions/dialogActions';
import { EMPTY_STRING, EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import MODULES from 'tbase/constants/modules';
import { RO_DETAILS_ENTITY_TYPE, NEW_ENTITY_URL, RO_APP_TYPES } from 'utils/constants';
import { ROUTE_CONFIG } from 'tbusiness/appServices/service/helpers/route';
import { isDoubleOptInFeatureEnabled } from 'twidgets/appServices/service/helpers/dealer.helper';
import { isProfitLossView, getAppSentryPayload } from 'utils';
import { getTabURL, getRouteParam } from 'utils/selectors';
import { getRepairOrderDetailsReducerKey } from 'twidgets/appServices/service/helpers/reducer.helper';
import {
  getCostCenters,
  getAccountingTaxCodes,
  getDealerCentralOptInSettings,
  getPartReturnReasons,
  getPayTypeConfigurations,
  getPayerPayTypeConfiguration,
  getDefaultSubPayType,
  getStickyBannerConfig,
  getAISuggestionDetails,
} from 'twidgets/appServices/service/helpers/ROContainer.selectors';
import {
  getRoDetails,
  getJobsDetails,
  getRODetailsError,
  getRODetailsFetchStatus,
  getJobIds,
  getRecommendationIds,
  getRecommendationByIds,
  getDeferredRecommendations,
  getMpviInspectionItems,
  getServiceTypes,
  getOEMSyncStatus,
  getSkills,
  getJobTags,
  getROApprovalData,
  getIsApprovalStatusFetched,
  getRecommendationTags,
  getLaborRatesFromRepairOrder,
  getIntermediateRecommendationIds,
  getIntermediateRecommendationByIds,
  getIsAddRecommendationInProgress,
  getAsyncQueueItems,
  getAsyncQueueIsProcessing,
  getAsyncQueueCount,
} from 'pages/RODetailsPage/RODetailsPage.selectors';
import ROConstraints from 'helpers/constraints';
import { hasPermissionToViewJob, hasPermissionToViewRecommendation } from 'permissions/roPermission';
import { SERVICES } from 'tbase/constants/appServices';
import RODetailsPage from './RODetails.page';
import { getRoIdFromUrl } from './RODetailsPage.readers';
import {
  getTypeFromUrl,
  shouldRouteToFirstTypeEntity,
  navigatedFromProfitLoss,
  shouldFetchProfitLoss,
  navigatedFromInvoiceConfirmation,
  shouldDefaultToNewRoute,
} from './RODetails.page.helper';
import { RO_DETAILS_FETCH_CALL_VS_ENTITY_TYPE } from './RODetails.page.entityType.factory';

class RODetailsPageContainer extends React.PureComponent {
  componentDidMount() {
    GlobalAnalytics.sendSubAppInitializingEvent();
    const { getRODetails: fetchRODetails, roId = EMPTY_STRING, params } = this.props;
    const type = getTypeFromUrl(params);
    fetchRODetails(roId, {
      isForcedPartFeatureEnabled: ROConstraints.isForcedPartFeatureEnabled(),
      ...(RO_DETAILS_FETCH_CALL_VS_ENTITY_TYPE[type] || RO_DETAILS_FETCH_CALL_VS_ENTITY_TYPE.DEFAULT),
    });
  }

  componentDidUpdate(prevProps) {
    const {
      jobIds,
      params,
      recommendationIds,
      roDetails,
      getRODetails: fetchRODetails,
      roId = EMPTY_STRING,
      isFetchingRO,
    } = this.props;
    const { jobIds: prevJobIds, recommendationIds: prevRecommendationIds, roId: prevROId } = prevProps;
    const type = getTypeFromUrl(params);
    if (!isFetchingRO && prevProps.isFetchingRO !== isFetchingRO) {
      GlobalAnalytics.sendSubAppIntializedEvent(getAppSentryPayload(RO_APP_TYPES.RO_DETAILS_PAGE));
    }
    if (
      roId !== prevROId ||
      navigatedFromProfitLoss({ prevMatch: prevProps.params, currentMatch: params }) ||
      navigatedFromInvoiceConfirmation({ prevMatch: prevProps.params, currentMatch: params })
    ) {
      return fetchRODetails(roId, {
        shouldFetchMpviInspections: true,
        isForcedPartFeatureEnabled: ROConstraints.isForcedPartFeatureEnabled(),
      });
    }
    if (
      hasPermissionToViewJob() &&
      shouldRouteToFirstTypeEntity({
        params,
        prevIds: prevJobIds,
        ids: jobIds,
        roDetails,
        typeToCheck: RO_DETAILS_ENTITY_TYPE.JOB,
      })
    ) {
      this.routeToRepairOrderEntity(RO_DETAILS_ENTITY_TYPE.JOB, _head(jobIds));
    } else if (
      hasPermissionToViewRecommendation() &&
      shouldRouteToFirstTypeEntity({
        params,
        prevIds: prevRecommendationIds,
        ids: recommendationIds,
        roDetails,
        typeToCheck: RO_DETAILS_ENTITY_TYPE.RECOMMENDATION,
      })
    ) {
      this.routeToRepairOrderEntity(RO_DETAILS_ENTITY_TYPE.RECOMMENDATION, _head(recommendationIds) || NEW_ENTITY_URL);
    } else if (
      isProfitLossView(type) &&
      ROConstraints.isProfitLossViewEnabled() &&
      shouldRouteToFirstTypeEntity({
        params,
        prevIds: prevJobIds,
        ids: jobIds,
        roDetails,
        typeToCheck: RO_DETAILS_ENTITY_TYPE.PROFIT_LOSS,
      })
    ) {
      this.routeToRepairOrderEntity(RO_DETAILS_ENTITY_TYPE.PROFIT_LOSS, _head(jobIds));
    } else if (
      ROConstraints.canAccessNewRoute(roDetails, RO_DETAILS_ENTITY_TYPE.JOB) &&
      params.typeId === NEW_ENTITY_URL &&
      params.type === RO_DETAILS_ENTITY_TYPE.JOB
    ) {
      if (prevProps.params.typeId === params.typeId) return;
      this.routeToRepairOrderEntity(RO_DETAILS_ENTITY_TYPE.JOB, NEW_ENTITY_URL);
    } else if (
      ROConstraints.canAccessNewRoute(roDetails, RO_DETAILS_ENTITY_TYPE.RECOMMENDATION) &&
      params.typeId === NEW_ENTITY_URL &&
      params.type === RO_DETAILS_ENTITY_TYPE.RECOMMENDATION
    ) {
      if (prevProps.params.typeId === params.typeId) return;
      this.routeToRepairOrderEntity(RO_DETAILS_ENTITY_TYPE.RECOMMENDATION, NEW_ENTITY_URL);
    } else if (hasPermissionToViewRecommendation() && params.typeId === NEW_ENTITY_URL) {
      if (!_head(recommendationIds)) return;
      this.routeToRepairOrderEntity(RO_DETAILS_ENTITY_TYPE.RECOMMENDATION, _head(recommendationIds));
    } else if (
      shouldDefaultToNewRoute({
        type: params.type,
        typeId: params.typeId,
        isFetchingRO,
        isFetchingROFromPrevProps: prevProps.isFetchingRO,
        jobIds,
        recommendationIds,
        roDetails,
      })
    ) {
      const routeType = type || RO_DETAILS_ENTITY_TYPE.JOB;
      this.routeToRepairOrderEntity(routeType, NEW_ENTITY_URL);
    } else if (shouldFetchProfitLoss(params, prevProps)) {
      return fetchRODetails(roId, {
        shouldFetchProfitLoss: true,
        shouldFetchJobs: false,
        shouldFetchRecommendations: false,
      });
    } else if (hasPermissionToViewJob() && params.typeId === undefined && params.type === undefined) {
      this.routeToRepairOrderEntity(RO_DETAILS_ENTITY_TYPE.JOB, _head(jobIds));
    }
  }

  componentWillUnmount() {
    const { resetRODetails: resetRODetailsAction, unsetAISuggestionDetails: unsetAISuggestionDetailsAction } =
      this.props;
    resetRODetailsAction();
    unsetAISuggestionDetailsAction(EMPTY_OBJECT);
  }

  routeToRepairOrderEntity = (entityType, entityId) => {
    const { navigate, params } = this.props;
    const roId = getRoIdFromUrl(params);
    navigate(
      ROUTE_CONFIG.REPAIR_ORDER_ENTITY_DETAILS.getUrl({
        roId,
        type: entityType,
        typeId: entityId,
      }),
      {
        replace: true,
      }
    );
  };

  render() {
    return <RODetailsPage {...this.props} isForcedPartFeatureEnabled={ROConstraints.isForcedPartFeatureEnabled()} />;
  }
}

RODetailsPageContainer.propTypes = {
  getRODetails: PropTypes.func.isRequired,
  resetRODetails: PropTypes.func.isRequired,
  roId: PropTypes.string,
  laborRates: PropTypes.array,
  jobIds: PropTypes.array,
  recommendationIds: PropTypes.array,
  roDetails: PropTypes.object,
  unsetAISuggestionDetails: PropTypes.func,
  navigate: PropTypes.func.isRequired,
  params: PropTypes.object.isRequired,
};

RODetailsPageContainer.defaultProps = {
  roId: '',
  laborRates: EMPTY_ARRAY,
  jobIds: EMPTY_ARRAY,
  recommendationIds: EMPTY_ARRAY,
  roDetails: EMPTY_OBJECT,
  unsetAISuggestionDetails: _noop,
};

const mapStateToProps = (state, props) => ({
  roDetails: getRoDetails(state),
  jobDetails: getJobsDetails(state),
  jobIds: getJobIds(state),
  recommendationIds: getRecommendationIds(state),
  recommendationByIds: getRecommendationByIds(state),
  intermediateRecommendationIds: getIntermediateRecommendationIds(state),
  intermediateRecommendationByIds: getIntermediateRecommendationByIds(state),
  isAddRecommendationInProgress: getIsAddRecommendationInProgress(state),
  costCentersByPayType: getCostCenters(state),
  deferredRecommendations: getDeferredRecommendations(state),
  tabURL: getTabURL(props),
  typeId: getRouteParam(props, 'typeId'),
  roId: getRouteParam(props, 'roId', ROUTE_CONFIG.REPAIR_ORDER_DETAILS.uiAbsoluteRoute),
  laborRates: getLaborRatesFromRepairOrder(state),
  mpviDetails: getMpviInspectionItems(state),
  isFetchingRO: getRODetailsFetchStatus(state),
  hasRODetailsError: getRODetailsError(state),
  serviceTypes: getServiceTypes(state),
  oemSyncStatus: getOEMSyncStatus(state),
  skills: getSkills(state),
  jobTags: getJobTags(state),
  recommendationTags: getRecommendationTags(state),
  accountingTaxCodes: getAccountingTaxCodes(state),
  roApprovalRequests: getROApprovalData(state),
  isApprovalStatusFetched: getIsApprovalStatusFetched(state),
  isDoubleOptInFeatureEnabled: isDoubleOptInFeatureEnabled(getDealerCentralOptInSettings(state)),
  partReturnReasons: getPartReturnReasons(state),
  payTypeConfigurations: getPayTypeConfigurations(state),
  ctaLockDetails: tget(state, `${getRepairOrderDetailsReducerKey()}.${CTA_LOCK_DETAILS_REDUCER_KEY}`, EMPTY_ARRAY),
  payerPayTypeConfiguration: getPayerPayTypeConfiguration(state),
  defaultSubPayType: getDefaultSubPayType(state),
  stickyBannerConfig: getStickyBannerConfig(state),
  aiSuggestionDetails: getAISuggestionDetails(state),
  queueItems: getAsyncQueueItems(state),
  queueIsProcessing: getAsyncQueueIsProcessing(state),
  queueCount: getAsyncQueueCount(state),
});

export default compose(
  withRouter,
  connect(mapStateToProps, {
    getRODetails,
    showDialog,
    hideDialog,
    updateJobStatus,
    updateRecommendationStatus,
    updateBulkRecommendationStatus,
    handlePayTypeStatusUpdate,
    updateJob,
    resetRODetails,
    createROInvoice,
    createRecommendation,
    updateBulkRORecommendationStatus,
    updateOemSyncStatus,
    updateJobTags,
    fetchPreviousRO,
    shouldHideIrisStatusBanner,
    setCTALockDetails,
    unsetStickyBannerDetails,
    fetchApprovalCount,
    setStickyBannerDetails,
    setAISuggestionDetails,
    unsetAISuggestionDetails,
    handleDeductibleSplitSaveSuccess,
    handleRecommendationDeductibleSplitSaveSuccess,
  }),
  withROEntityDetails,
  withDocumentTitle(SERVICES, MODULES.RO_DETAILS),
  withSize({ hasPageFooter: 0, hasPageHeader: 0 })
)(RODetailsPageContainer);
