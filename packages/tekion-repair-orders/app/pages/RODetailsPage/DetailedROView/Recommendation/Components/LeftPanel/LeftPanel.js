import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';

import _map from 'lodash/map';
import _isFunction from 'lodash/isFunction';
import _noop from 'lodash/noop';
import _isEmpty from 'lodash/isEmpty';

import PropertyControlledComponent from 'tcomponents/molecules/PropertyControlledComponent';
import RepairOrderCard from 'organisms/repairOrderCard';
import Heading from 'tcomponents/atoms/Heading';
import Content from 'tcomponents/atoms/Content';
import AddService from 'pages/RODetailsPage/DetailedROView/JobDetails/LeftPanel/AddService';
import CollapsiblePanel from '@tekion/tekion-components/src/molecules/CollapsiblePanel';

import RecommendationReader from 'readers/Recommendation.reader';
import ROReader from 'readers/RepairOrder.reader';

import noService from 'assets/noService.svg';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from 'tbase/app.constants';
import { RO_DETAILS_RECOMMENDATION_SUMMARY_URL, NEW_ENTITY_URL } from 'utils/constants';
import ROConstraints from 'helpers/constraints';
import COLORS from 'tstyles/exports.scss';

import { CollapsedFooter, ExpandedFooter } from './Footer';
import CardDescription from './Components/CardDescription';
import CardTitle from './Components/CardTitle';
import SummaryDescription from './Components/SummaryDescription';
import RecommendationTagsBadge from './Components/RecommendationTagsBadge';
import { isCardActive, getRecommendationListClassName } from './LeftPanel.helper';
import styles from './leftPanel.module.scss';
import '../../../JobDetails/LeftPanel/leftPanel.scss';

const renderSummaryHeader = () => <Heading size={4}>{__('Recommendation Summary')}</Heading>;
const renderDefaultCollapsedFooter = props => <CollapsedFooter {...props} />;
const renderDefaultExpandedFooter = props => (
  <ExpandedFooter renderCollapsedFooter={renderDefaultCollapsedFooter} {...props} />
);

class LeftPanel extends PureComponent {
  handleEntityClick = link => () => {
    const { onClick, navigate } = this.props;
    if (_isFunction(onClick)) return onClick(link);
    return navigate(link);
  };

  renderRecommendationCard = (recommendation, index) => {
    const {
      tabURL,
      typeId,
      showRecommendationAmountsBreakup,
      shouldShowSnapshotAmounts,
      itemClassname,
      isRoDashboardDrawerVisible,
      profitLossParams,
      recommendationTags,
      payTypeConfigurations,
      roEntitiesAmount: { selectedPayers, selectedSubPayTypes } = EMPTY_OBJECT,
    } = this.props;
    const id = RecommendationReader.id(recommendation);
    const isActive = isCardActive(id, typeId);
    const { isIntermediateRecommendation } = recommendation;
    const shouldAllowOnClick = isActive || isRoDashboardDrawerVisible || isIntermediateRecommendation;
    return (
      <div
        role="presentation"
        key={id}
        onClick={shouldAllowOnClick ? _noop : this.handleEntityClick(`${tabURL}/${id}`)}
        className={classnames(styles.trLink, itemClassname, {
          [styles.addRecommendation]: isIntermediateRecommendation,
        })}
        id={index}>
        <RepairOrderCard
          data={recommendation}
          index={index}
          renderHeader={CardTitle}
          renderContent={CardDescription}
          isActive={isCardActive(id, typeId)}
          serialNumber={RecommendationReader.serialNumber(recommendation)}
          shouldShowAmountBreakup={showRecommendationAmountsBreakup || isIntermediateRecommendation}
          shouldShowSnapshotAmounts={shouldShowSnapshotAmounts}
          profitLossParams={profitLossParams}
          renderBadge={RecommendationTagsBadge}
          recommendationTags={recommendationTags}
          payTypeConfigurations={payTypeConfigurations}
          selectedPayers={selectedPayers}
          selectedSubPayTypes={selectedSubPayTypes}
        />
      </div>
    );
  };

  renderSummary = () => {
    const { tabURL, recommendations, typeId, itemClassname } = this.props;
    const headerProps = ROConstraints.isServiceV3Enabled() ? EMPTY_OBJECT : { renderHeader: renderSummaryHeader };
    return (
      <div
        role="presentation"
        key="summary"
        onClick={this.handleEntityClick(`${tabURL}/${RO_DETAILS_RECOMMENDATION_SUMMARY_URL}`)}
        className={classnames(styles.trLink, itemClassname)}
        id="recommendationSummary">
        <RepairOrderCard
          data={recommendations}
          {...headerProps}
          renderContent={SummaryDescription}
          isActive={isCardActive(RO_DETAILS_RECOMMENDATION_SUMMARY_URL, typeId)}
        />
      </div>
    );
  };

  getFooterProps = () => {
    const {
      roEntitiesAmount,
      payType,
      onPayTypeChange,
      isFetchingEstimatePricing,
      roDetails,
      payTypeConfigurations,
      handleLeftPanelFooterSelectionChange,
    } = this.props;
    return {
      ...roEntitiesAmount,
      payTypeConfigurations,
      payType,
      onPayTypeChange,
      isFetchingEstimatePricing,
      roTaxConfigDetails: ROReader.getTaxLabelConfigs(roDetails),
      handleLeftPanelFooterSelectionChange,
    };
  };

  renderAddService = () => {
    const { typeId, tabURL, type, itemClassname } = this.props;
    const isActive = typeId === NEW_ENTITY_URL;
    return (
      <div
        role="presentation"
        key="addRecommendation"
        onClick={isActive ? _noop : this.handleEntityClick(`${tabURL}/new`)}
        className={classnames(styles.trLink, itemClassname)}
        id="addRecommendation">
        <AddService isActive={isActive} type={type} />
      </div>
    );
  };

  render() {
    const {
      recommendations,
      renderCollapsedFooter,
      renderExpandedFooter,
      showSummaryView,
      showAddRecommendation,
      intermediateRecommendations,
    } = this.props;
    const showFooter = _isFunction(renderCollapsedFooter) && _isFunction(renderExpandedFooter);
    const isServiceV3Enabled = ROConstraints.isServiceV3Enabled();
    return (
      <div className={`${styles.container} left-panel`}>
        <PropertyControlledComponent controllerProperty={showAddRecommendation}>
          {this.renderAddService()}
        </PropertyControlledComponent>
        <PropertyControlledComponent controllerProperty={showSummaryView && isServiceV3Enabled}>
          {this.renderSummary()}
        </PropertyControlledComponent>
        <div
          className={classnames(
            styles.list,
            getRecommendationListClassName(showAddRecommendation, isServiceV3Enabled)
          )}>
          <PropertyControlledComponent
            controllerProperty={_isEmpty(recommendations) && _isEmpty(intermediateRecommendations)}>
            <div className="no-service-container">
              <img alt="empty cart" src={noService} />
              <Content>{__('No Recommendation Added')}</Content>
            </div>
          </PropertyControlledComponent>
          {_map(recommendations, this.renderRecommendationCard)}
          {_map(intermediateRecommendations, this.renderRecommendationCard)}
          <PropertyControlledComponent controllerProperty={showSummaryView && !isServiceV3Enabled}>
            {this.renderSummary()}
          </PropertyControlledComponent>
        </div>
        <PropertyControlledComponent controllerProperty={showFooter}>
          <div className="footer">
            <CollapsiblePanel
              direction="top"
              background={COLORS.glitter}
              position="relative"
              collapsedContent={renderCollapsedFooter && renderCollapsedFooter(this.getFooterProps())}>
              {renderExpandedFooter && renderExpandedFooter(this.getFooterProps())}
            </CollapsiblePanel>
          </div>
        </PropertyControlledComponent>
      </div>
    );
  }
}

LeftPanel.propTypes = {
  recommendations: PropTypes.array,
  tabURL: PropTypes.string,
  typeId: PropTypes.string,
  onClick: PropTypes.func,
  showSummaryView: PropTypes.bool,
  itemClassname: PropTypes.string,
  roEntitiesAmount: PropTypes.object,
  payType: PropTypes.string,
  onPayTypeChange: PropTypes.func,
  renderCollapsedFooter: PropTypes.func,
  renderExpandedFooter: PropTypes.func,
  isFetchingEstimatePricing: PropTypes.bool,
  showAddRecommendation: PropTypes.bool,
  type: PropTypes.string,
  showRecommendationAmountsBreakup: PropTypes.bool,
  isRoDashboardDrawerVisible: PropTypes.bool,
  roDetails: PropTypes.object,
  payTypeConfigurations: PropTypes.array,
  handleLeftPanelFooterSelectionChange: PropTypes.func,
  shouldShowSnapshotAmounts: PropTypes.bool,
  navigate: PropTypes.func.isRequired,
  intermediateRecommendations: PropTypes.array,
};

LeftPanel.defaultProps = {
  recommendations: EMPTY_ARRAY,
  tabURL: EMPTY_STRING,
  typeId: EMPTY_STRING,
  onClick: undefined,
  roEntitiesAmount: EMPTY_OBJECT,
  payType: EMPTY_STRING,
  onPayTypeChange: _noop,
  showSummaryView: false,
  renderCollapsedFooter: renderDefaultCollapsedFooter,
  renderExpandedFooter: renderDefaultExpandedFooter,
  isFetchingEstimatePricing: true,
  showAddRecommendation: false,
  type: EMPTY_STRING,
  showRecommendationAmountsBreakup: true,
  itemClassname: EMPTY_STRING,
  roDetails: EMPTY_OBJECT,
  isRoDashboardDrawerVisible: false,
  payTypeConfigurations: EMPTY_ARRAY,
  handleLeftPanelFooterSelectionChange: _noop,
  shouldShowSnapshotAmounts: false,
  intermediateRecommendations: EMPTY_ARRAY,
};

export default LeftPanel;
