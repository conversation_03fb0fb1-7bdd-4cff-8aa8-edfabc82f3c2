@import "tstyles/component.scss";
@import "../../../JobDetails/LeftPanel/leftPanel.scss";

$footer-height: 6.4rem;

.footer {
  border-top: 1px solid $platinum;
  height: $footer-height;
  background-color: $glitter;
}

.container {
  border: 1px solid $platinum;
  border-width: 1px 1px 0 0px;
  @include flex($flex-flow: column nowrap);
  @extend .left-panel;
}

.list {
  height: calc(100% - #{$footer-height});
  overflow: auto;
}

.trLink {
  cursor: pointer;
  overflow: hidden;
  text-decoration: none !important;
  display: block;
  &:hover {
    color: inherit;
  }
  &:active {
    text-decoration: none;
  }
  color: inherit;
}

.footerLoader{
  background-color: rgba($color: $white, $alpha: 0.4);
  z-index: 1;
}

.addRecommendation {
  cursor: not-allowed;
  background-color: $lightGray;
}