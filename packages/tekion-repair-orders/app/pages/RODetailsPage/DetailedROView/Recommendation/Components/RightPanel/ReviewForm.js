import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { compose } from 'recompose';
import cx from 'classnames';

import _noop from 'lodash/noop';
import _map from 'lodash/map';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';

import {
  updateTechnicians,
  getRODetails,
  markROStale,
  setCTALockDetails,
  fetchCustomerNotificationDetails,
  fetchApprovalCount,
} from 'actions/roDetails.actions';
import { fetchJob, fetchJobs, markJobStale } from 'actions/jobDetails.action';
import {
  createRecommendationSummary,
  fetchRecommendations,
  markRecommendationStale,
  updateBulkRORecommendationStatus,
  generateRecommendationPdf,
} from 'actions/recommendation.action';
import ServiceDocumentsService from 'tbusiness/appServices/service/services/documentService';

import FormPage from 'tcomponents/pages/formPage';
import RODispatch from 'pages/RODispatch';
import SaveComponent from 'tcomponents/molecules/SaveComponent';
import FontIcon from 'tcomponents/atoms/FontIcon';
import Spinner from 'tcomponents/molecules/SpinnerComponent';
import PropertyControlledComponent from 'tcomponents/molecules/PropertyControlledComponent';
import PopOver from 'tcomponents/molecules/popover';
import { toaster } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import withNotificationHandler from 'organisms/withNotificationHandler';
import Checkbox from 'tcomponents/atoms/checkbox';
import Content from 'tcomponents/atoms/Content';
import withPropertyConsumer from '@tekion/tekion-components/src/organisms/propertyProvider/withPropertyConsumer';
import { withTekionConversion } from '@tekion/tekion-conversion-web';
import { getTaxLabelsByTaxType } from 'twidgets/appServices/service/helpers/taxRegime.helpers';
import { getPhoneNumberValue } from 'twidgets/helpers/phoneNumberInput';
import formActionTypes from 'tcomponents/pages/formPage/constants/actionTypes';
import { triggerSubmit } from 'tcomponents/pages/formPage/utils/formAction';
import actionTypes from 'tcomponents/organisms/FormBuilder/constants/actionTypes';

import ROReader from 'readers/RepairOrder.reader';
import RecommendationReader from 'readers/Recommendation.reader';
import CheckInSettingsReader from 'tbusiness/readers/service/CheckInSettings';

import { getCustomerPhoneNo } from 'helpers/recommendation.helper';
import ROConstraints from 'helpers/constraints';
import RepairOrderEnv from 'utils/repairOrderEnv';
import { getDetailsViewNotificationChannels } from 'helpers/repairOrder.helper';
import {
  areAllInspectionMediasSharedToCustomer,
  isSendToCustomerDisabled,
  shouldShowSendToCustomer,
  getInspectionMediasToSendToCustomer,
} from 'tbusiness/appServices/service/helpers/media';
import { getCheckinSettingSourceFromRepairOrder } from 'tbusiness/appServices/service/helpers/general';
import { getErrorMessage } from 'utils';
import { getTaxRegimeConfig } from 'utils/selectors';
import { getPromiseTimeErrorMessage } from 'organisms/TechRecommendForm/TechRecommendForm.helper';

import { getPayTypeConfigurations } from 'twidgets/appServices/service/helpers/ROContainer.selectors';
import {
  createSelectorForFormSection,
  createSelectorForFormConfig,
} from 'organisms/TechRecommendForm/RecommendSummaryForm.config';
import PromiseTime from 'organisms/TechRecommendForm/Components/PromiseTime';
import {
  getCustomerApprovedReviewRecommendations,
  getReviewedRecommendations,
  getApprovedDeferredRecommendations,
} from 'pages/RODetailsPage/DetailedROView/Recommendation/Recommendation.helper';
import { hasPermissionToSendToCustomer, hasPermissionToSendMpviVideoToCustomer } from 'permissions/roPermission';
import { getMomentObject, toMoment } from '@tekion/tekion-base/utils/dateUtils';
import { getCustomerName } from 'tbase/formatters/customer';
import CustomerReader from 'tbase/readers/Customer';
import { EMPTY_OBJECT, EMPTY_ARRAY } from 'tbase/app.constants';
import { BASE_PAY_TYPE_VALUES } from 'tbase/constants/payTypes';

import {
  NOTIFICATION_TYPE,
  TEXT_MESSAGE_ACTIONS,
  RECOMMENDATION_STATUS_VALUES,
  MAX_COUNT_ALLOWED_TO_SEND_TO_CUSTOMER,
} from 'utils/constants';
import { CTA_LOCK_MESSAGE } from 'organisms/ROForm/ROForm.constants';
import { ESTIMATE_VIEW_TYPES } from 'organisms/TechRecommendForm/TechRecommendForm.constants';
import { CUSTOMER_FIELDS } from 'constants/recommendation.constants';

import { CustomerService } from 'services';

import DeferRecommendationModal from './Components/DeferRecommendationModal';

import {
  isFormDisabled,
  getUpdateRecommendationStatusList,
  getEstimatedPricesFromROAndRecommendationDetails,
  getEstimatedPricesFromROAndRecommendationDetailsForMultiPayer,
  getDefaultCommunicationMode,
  getDefaultComment,
  getLastNotificationSentTimePayload,
  getFormattedLastNotificationSentTime,
  shouldLockSendToCustomerCta,
  getPayersDetailForCustomerApprovedReviewedRecommendations,
} from './ReviewForm.helpers';
import REVIEW_FORM_ACTION_HANDLER from './ReviewForm.actionHandlers';
import {
  FETCH_ESTIMATE_PRICING_DETAILS,
  FETCH_UPDATED_PROMISE_TIME,
  DEFER_MODAL_VISIBILITY_ACTION,
  CONTEXT_ID,
} from './ReviewForm.constants';

import styles from './rightPanel.module.scss';

const HEADER_PROPS = { className: styles.header };
const FORM_PAGE_BODY_PROPS = { className: styles.rightPanelFormBody };

const getInitialFormValues = ({ recommendations, roDetails, payTypeConfigurations, getDefaultCountryCode }) => {
  const customerApprovedReviewRecommendations = getCustomerApprovedReviewRecommendations(recommendations);
  const getEstimatedPricesFromROAndRecommendationDetailsFuncToExec = ROConstraints.isServiceV3Enabled()
    ? getEstimatedPricesFromROAndRecommendationDetailsForMultiPayer
    : getEstimatedPricesFromROAndRecommendationDetails;
  const customerInfo = ROReader.getCustomerInfo(roDetails);
  const customerPhoneNo = getCustomerPhoneNo(customerInfo);
  const approvedDeferredRecommendations = getApprovedDeferredRecommendations(recommendations);
  const payersForReviewedRespondedRecommendations = getPayersDetailForCustomerApprovedReviewedRecommendations([
    ...customerApprovedReviewRecommendations,
    ...approvedDeferredRecommendations,
  ]);
  return {
    updateCustomerResponse: _map(customerApprovedReviewRecommendations, recommendation => {
      const recommendationStatus = RecommendationReader.status(recommendation);
      return {
        ...recommendation,
        statusToReview: recommendationStatus,
        status:
          recommendationStatus === RECOMMENDATION_STATUS_VALUES.CUSTOMER_APPROVED
            ? RECOMMENDATION_STATUS_VALUES.APPROVED
            : recommendationStatus,
      };
    }),
    approvedDeferredRecommendations,
    isTaxIncluded: false,
    showEstimateDetailsBy: ESTIMATE_VIEW_TYPES.PAY_TYPES,
    selectedPayers: EMPTY_ARRAY,
    selectedPayTypes: BASE_PAY_TYPE_VALUES,
    payers: payersForReviewedRespondedRecommendations,
    estimatedPrice: {
      isLoading: true,
      estimatedPriceValues: getEstimatedPricesFromROAndRecommendationDetailsFuncToExec(
        roDetails,
        customerApprovedReviewRecommendations,
        payTypeConfigurations
      ),
    },
    customerApprovedReviewRecommendations,
    promiseTimeData: {
      isSameDay: true,
      isLoading: false,
      promiseTimeValue: toMoment(ROReader.getPromiseTime(roDetails)),
    },
    communicatedOn: getMomentObject(),
    communicatedAt: getMomentObject(),
    communicationMode: getDefaultCommunicationMode(recommendations),
    comment: getDefaultComment(recommendations),
    [CUSTOMER_FIELDS.APPROVER]: getCustomerName(customerInfo),
    [CUSTOMER_FIELDS.EMAIL]: CustomerReader.email(customerInfo),
    [CUSTOMER_FIELDS.PHONE]: getPhoneNumberValue({
      value: CustomerReader.number(customerPhoneNo),
      getDefaultCountryCode,
      phoneNumberTypeConfig: {
        [CUSTOMER_FIELDS.PHONE]: { extension: CustomerReader.extension(customerPhoneNo) },
      },
      fieldId: CUSTOMER_FIELDS.PHONE,
    }),
  };
};

const FormPageWithNotification = withNotificationHandler(getDetailsViewNotificationChannels)(FormPage);

class RightPanel extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      formValues: getInitialFormValues(props),
      recommendations: props.recommendations,
      isDisabled: isFormDisabled(props.roDetails, props.recommendations),
      reviewedRecommendations: getReviewedRecommendations(props.recommendations),
      primaryActionLoading: false,
      isTertiaryBtnLoading: false,
      footerProps: {
        shouldNotifyUser: true,
        notifyMpviVideoAttach: false,
        allMediaList: EMPTY_ARRAY,
        areAllInspectionMediasShared: false,
      },
      customerNotificationDetails: {},
      lastNotificationSentTime: '',
      fetchingLastNotificationTime: false,
      deferRecommendationParams: {
        isDeferRecommendationModalVisible: false,
      },
    };
    this.setParentState = this.setState.bind(this);
    this.getFormSection = createSelectorForFormSection();
    this.getFormConfig = createSelectorForFormConfig();
  }

  componentDidMount() {
    this.handleAction({ type: FETCH_ESTIMATE_PRICING_DETAILS });
    this.handleAction({ type: FETCH_UPDATED_PROMISE_TIME });
    this.fetchCustomerUpdateDetails();
    this.fetchMediaDetails();
  }

  static getDerivedStateFromProps(nextProps, nextState) {
    if (nextState.recommendations === nextProps.recommendations)
      return { ...nextState, isDisabled: isFormDisabled(nextProps.roDetails, nextProps.recommendations) };
    return {
      formValues: getInitialFormValues(nextProps),
      recommendations: nextProps.recommendations,
      reviewedRecommendations: getReviewedRecommendations(nextProps.recommendations),
      isDisabled: isFormDisabled(nextProps.roDetails, nextProps.recommendations),
    };
  }

  componentDidUpdate(prevProps) {
    const { recommendations, mpviDetails } = this.props;
    if (mpviDetails !== prevProps.mpviDetails) {
      this.fetchMediaDetails();
    }
    if (recommendations === prevProps.recommendations) return;
    this.handleAction({ type: FETCH_ESTIMATE_PRICING_DETAILS });
    this.handleAction({ type: FETCH_UPDATED_PROMISE_TIME });
    this.fetchCustomerUpdateDetails();
  }

  getParentState = () => ({ ...this.state, ...this.props });

  fetchCustomerUpdateDetails = () => {
    const { roDetails, fetchCustomerNotificationDetails: fetchCustomerNotificationDetailsAction } = this.props;
    if (!ROConstraints.isConsumerPortalEnabled()) return;
    fetchCustomerNotificationDetailsAction(ROReader.getId(roDetails).roId, TEXT_MESSAGE_ACTIONS.SENT_TO_CUSTOMER)
      .then(customerNotificationDetails => {
        const { sentCount } = customerNotificationDetails || EMPTY_OBJECT;
        this.setState(state => ({
          customerNotificationDetails: customerNotificationDetails || EMPTY_OBJECT,
          footerProps: { ...state.footerProps, sentCount },
        }));
      })
      .catch(() => {
        this.setState({ customerNotificationDetails: EMPTY_OBJECT });
      });
  };

  fetchMediaDetails = () => {
    const { roDetails } = this.props;
    ServiceDocumentsService.fetchAllMedia(ROReader.getROId(roDetails))
      .then(({ mediaItemList }) => {
        const areAllInspectionMediasShared = areAllInspectionMediasSharedToCustomer(mediaItemList);
        this.setState(state => ({
          footerProps: {
            ...state.footerProps,
            allMediaList: mediaItemList,
            notifyMpviVideoAttach: areAllInspectionMediasShared,
            areAllInspectionMediasShared,
          },
        }));
      })
      .catch(error => toaster(NOTIFICATION_TYPE.ERROR, getErrorMessage(error, __('Failed to load media details'))));
  };

  handleSaveOnEntityChange = () =>
    new Promise(resolve => {
      this.resolveHandleSave = resolve;
      triggerSubmit(CONTEXT_ID);
    });

  handleAction = async ({ type, payload = EMPTY_OBJECT }) => {
    const funcToExec = REVIEW_FORM_ACTION_HANDLER[type] || REVIEW_FORM_ACTION_HANDLER.DEFAULT;
    const { hasError } =
      (await funcToExec(
        { ...payload, handleSave: this.handleSaveOnEntityChange },
        { setState: this.setState.bind(this), getState: this.getParentState }
      )) || EMPTY_OBJECT;
    let resolveData = null;
    /**
     * Handling form error and save behavior during tab switches:
     * - The `handleSubmit` function in `withROEntityDetails` expects a Promise to resolve.
     * - To ensure this, we store the resolve function and resolve it when form actions complete.
     */
    if (this.resolveHandleSave) {
      if (type === formActionTypes.ON_FORM_SUBMIT || type === actionTypes.VALIDATION_SUCCESS) {
        resolveData = { hasError: type === actionTypes.VALIDATION_SUCCESS };
      }
    }
    // Resolve the stored promise with error status and clean up
    if (this.resolveHandleSave && resolveData) {
      this.resolveHandleSave({ hasError: hasError || resolveData.hasError });
      this.resolveHandleSave = null;
    }
  };

  handleOnHideDispatch = () => this.setState({ showDispatch: false });

  handleShouldNotifyUser = () =>
    this.setState(state => ({
      footerProps: { ...state.footerProps, shouldNotifyUser: !_get(state.footerProps, 'shouldNotifyUser') },
    }));

  handleOnSaveDispatch = () => {
    const { updateTechnicians: updateTechnicianAction } = this.props;
    updateTechnicianAction().then(this.handleOnHideDispatch);
  };

  handleNotifyMpviVideoAttach = () =>
    this.setState(state => ({
      footerProps: { ...state.footerProps, notifyMpviVideoAttach: !_get(state, 'footerProps.notifyMpviVideoAttach') },
    }));

  handleSendToCustomer = () => {
    const { reviewedRecommendations, footerProps } = this.state;
    const { allMediaList } = footerProps;
    const {
      updateBulkRORecommendationStatus: updateBulkSendToCustomerStatusAction,
      roDetails,
      mpviDetails,
    } = this.props;
    this.setState({ footerProps: { ...footerProps, isTertiaryBtnLoading: true } });
    const notifyMpviVideoAttach = footerProps.notifyMpviVideoAttach && !footerProps.areAllInspectionMediasShared;
    const mediaItems = notifyMpviVideoAttach
      ? getInspectionMediasToSendToCustomer({
          allMediaList,
          mpviDetails,
          roDetails,
          isConsumerPortalEnabled: ROConstraints.isConsumerPortalEnabled(),
          hasPermissionToSendMpviVideoToCustomer: hasPermissionToSendMpviVideoToCustomer(),
        })
      : EMPTY_ARRAY;
    updateBulkSendToCustomerStatusAction({
      roId: ROReader.getROId(roDetails),
      preROId: ROReader.getROId(roDetails),
      recommendationSubmitList: getUpdateRecommendationStatusList(
        reviewedRecommendations,
        footerProps.shouldNotifyUser
      ),
      status: RECOMMENDATION_STATUS_VALUES.SENT_TO_CUSTOMER,
      notifyMpviVideoAttach,
      mediaItems,
    })
      .then(() => {
        this.setState(state => ({
          footerProps: {
            ...state.footerProps,
            areAllInspectionMediasShared: footerProps.notifyMpviVideoAttach || footerProps.areAllInspectionMediasShared,
          },
        }));
      })
      .catch(error => toaster(NOTIFICATION_TYPE.ERROR, getErrorMessage(error, __('Failed to send to customer'))))
      .finally(() => {
        this.setState(state => ({
          footerProps: { ...state.footerProps, shouldNotifyUser: false, isTertiaryBtnLoading: false },
        }));
      });
  };

  handleLastSentPopoverVisibleChange = visible => {
    if (!visible) return;
    const { roDetails, getFormattedDateAndTime } = this.props;
    const payload = getLastNotificationSentTimePayload(roDetails);
    this.setState({ fetchingLastNotificationTime: true });
    CustomerService.getLastNotificationSentTime(payload)
      .then(data =>
        this.setState({
          lastNotificationSentTime: getFormattedLastNotificationSentTime(
            data?.lastNotificationSentTime,
            getFormattedDateAndTime
          ),
          fetchingLastNotificationTime: false,
        })
      )
      .catch(error => {
        this.setState({ fetchingLastNotificationTime: false });
        toaster(NOTIFICATION_TYPE.ERROR, getErrorMessage(error, __('Failed to fetch last notification sent time')));
      });
  };

  handleDeferRecommendationModalClose = () => {
    this.handleAction({
      type: DEFER_MODAL_VISIBILITY_ACTION,
      payload: { deferRecommendationParams: { isDeferRecommendationModalVisible: false } },
    });
  };

  renderLastSentPopover = () => {
    const { lastNotificationSentTime, fetchingLastNotificationTime } = this.state;
    return (
      <PopOver
        placement="right"
        trigger="click"
        content={
          <Content className={styles.notificationContent}>
            {fetchingLastNotificationTime ? (
              <div className={styles.footerPopOverPaddingForLoader}>
                <Spinner size="small" />
              </div>
            ) : (
              __('Notification last sent: {{lastNotificationSentTime}}', { lastNotificationSentTime })
            )}
          </Content>
        }
        onVisibleChange={this.handleLastSentPopoverVisibleChange}>
        <FontIcon className={styles.infoIcon}>icon-info</FontIcon>
      </PopOver>
    );
  };

  renderMpviVideoCheckbox = ({ isNotifyCustomerDisabled }) => {
    const {
      footerProps: { notifyMpviVideoAttach, allMediaList, areAllInspectionMediasShared },
    } = this.state;
    const { mpviDetails, roDetails } = this.props;
    const shouldRenderCheckbox = shouldShowSendToCustomer({
      allMediaList,
      isConsumerPortalEnabled: ROConstraints.isConsumerPortalEnabled(),
    });
    const isDisabled =
      isNotifyCustomerDisabled ||
      isSendToCustomerDisabled({
        allMediaList,
        mpviDetails,
        roDetails,
        isConsumerPortalEnabled: ROConstraints.isConsumerPortalEnabled(),
        hasPermissionToSendMpviVideoToCustomer: hasPermissionToSendMpviVideoToCustomer(),
      });
    const label = areAllInspectionMediasShared ? __('$$(Inspection) Video Sent') : __('Send $$(Inspection) Video');

    return (
      <PropertyControlledComponent controllerProperty={shouldRenderCheckbox}>
        <div className={styles.sendCustomerOption}>
          <Checkbox
            disabled={isDisabled}
            onChange={this.handleNotifyMpviVideoAttach}
            value={notifyMpviVideoAttach}
            label={label}
          />
        </div>
      </PropertyControlledComponent>
    );
  };

  renderNotifyCustomer = () => {
    const {
      footerProps: { shouldNotifyUser, sentCount = 0 },
      isDisabled,
      reviewedRecommendations,
    } = this.state;
    if (!hasPermissionToSendToCustomer()) return null;
    const hasExceededMaxNotifyCustomer = sentCount >= MAX_COUNT_ALLOWED_TO_SEND_TO_CUSTOMER;
    const isNotifyCustomerDisabled = isDisabled || _isEmpty(reviewedRecommendations) || hasExceededMaxNotifyCustomer;
    return (
      <div className={styles.sendCustomerOptions}>
        <div className={styles.sendCustomerOption}>
          <Checkbox
            disabled={isNotifyCustomerDisabled}
            onChange={this.handleShouldNotifyUser}
            value={isNotifyCustomerDisabled ? false : shouldNotifyUser}
            label={__('Notify $$(Customer)')}
          />
          {this.renderLastSentPopover()}
        </div>
        {this.renderMpviVideoCheckbox({ isNotifyCustomerDisabled })}
      </div>
    );
  };

  renderFooterComponent = footerProps => {
    const { isDisabled, reviewedRecommendations, formValues } = this.state;
    const { ctaLockDetails, roDetails } = this.props;
    const reviewedRecommendationsIds = _map(reviewedRecommendations, RecommendationReader.id);
    const isCTABlocked = shouldLockSendToCustomerCta(ctaLockDetails, reviewedRecommendationsIds);
    const lockedIcon = isCTABlocked ? 'icon-lock-filled' : null;
    const ctaLockMessage = isCTABlocked ? CTA_LOCK_MESSAGE : null;
    const promiseTimeRoundUp = CheckInSettingsReader.getPromiseTimeRoundUp(
      RepairOrderEnv.checkinSettings,
      getCheckinSettingSourceFromRepairOrder(roDetails)
    );
    const isServiceV3Enabled = ROConstraints.isServiceV3Enabled();
    return (
      <div className={cx({ [styles.footerContainer]: isServiceV3Enabled })}>
        <PropertyControlledComponent controllerProperty={isServiceV3Enabled}>
          <div className="d-flex align-items-start p-16">
            <Content highlight className={styles.promiseTimeLabel}>
              {__('Update $$(Promise Time)')}
            </Content>
            <div className={styles.updatePromiseTimeContainer}>
              <PromiseTime
                error={getPromiseTimeErrorMessage(formValues.promiseTimeData)}
                onAction={this.handleAction}
                promiseTimeRoundUp={promiseTimeRoundUp}
                value={_get(formValues, 'promiseTimeData')}
                id="promiseTimeData"
                datePickerContainerClassName={styles.promiseTimeDatePicker}
              />
            </div>
          </div>
        </PropertyControlledComponent>
        <SaveComponent
          onPrimaryAction={footerProps.onSubmit}
          primaryActionLoading={footerProps.primaryActionLoading}
          primaryButtonLabel={__('Submit')}
          showTertiaryButton={ROConstraints.isConsumerPortalEnabled()}
          tertiaryActionLoading={footerProps.isTertiaryBtnLoading}
          onTertiaryAction={this.handleSendToCustomer}
          isTertiaryDisabled={isDisabled || _isEmpty(reviewedRecommendations) || isCTABlocked}
          tertiaryButtonLabel={__('Send To $$(Customer)')}
          isPrimaryDisabled={isDisabled || _get(formValues, ['estimatedPrice', 'isLoading']) || isCTABlocked}
          showSecondaryButton={false}
          renderAdditionalFooterDetail={this.renderNotifyCustomer}
          tertiaryButtonIcon={lockedIcon}
          primaryButtonIcon={lockedIcon}
          tertiaryButtonToolTipTitle={ctaLockMessage}
          primaryButtonToolTipTitle={ctaLockMessage}
        />
      </div>
    );
  };

  render() {
    const {
      formValues,
      errors,
      footerProps,
      isDisabled,
      isGeneratePdfLoading,
      isSendingTextMessage,
      showDispatch,
      defaultActiveJobIds,
      deferRecommendationParams,
    } = this.state;
    const { isTaxIncluded, showEstimateDetailsBy, selectedPayers, selectedPayTypes, payers } = formValues;
    const { roDetails, mpviDetails, payTypeConfigurations, getCountOfPhoneNumberDigits, getFormattedDateAndTime } =
      this.props;
    const promiseTimeRoundUp = CheckInSettingsReader.getPromiseTimeRoundUp(
      RepairOrderEnv.checkinSettings,
      getCheckinSettingSourceFromRepairOrder(roDetails)
    );
    return (
      <>
        <div className={`${styles.rightPanel} full-height`}>
          <FormPageWithNotification
            sections={this.getFormSection({
              [CUSTOMER_FIELDS.COMMUNICATION_MODE]: _get(formValues, CUSTOMER_FIELDS.COMMUNICATION_MODE),
            })}
            fields={this.getFormConfig({
              isFormDisabled: isDisabled,
              isGeneratePdfLoading,
              isSendingTextMessage,
              taxDetailsByTaxType: getTaxLabelsByTaxType(
                ROReader.getTaxLabelConfigs(roDetails),
                getTaxRegimeConfig(ROReader.getSiteId(roDetails))
              ),
              promiseTimeRoundUp,
              payTypeConfigurations,
              getCountOfPhoneNumberDigits,
              approvedDeferredRecommendations: _get(formValues, 'approvedDeferredRecommendations'),
              getFormattedDateAndTime,
              isTaxIncluded,
              showEstimateDetailsBy,
              selectedPayers,
              selectedPayTypes,
              payers,
              roTaxConfigDetails: ROReader.getTaxLabelConfigs(roDetails),
              siteId: ROReader.getSiteId(roDetails),
            })}
            values={formValues}
            errors={errors}
            roDetails={roDetails}
            mpviDetails={mpviDetails}
            headerProps={HEADER_PROPS}
            containerClassName={`${styles.rightPanelFormContainer} full-height`}
            bodyProps={FORM_PAGE_BODY_PROPS}
            onAction={this.handleAction}
            footerProps={footerProps}
            footerComponent={this.renderFooterComponent}
            contextId={CONTEXT_ID}
          />
          <RODispatch
            show={showDispatch}
            roId={ROReader.getId(roDetails).roId}
            defaultActiveJobIds={defaultActiveJobIds}
            onHide={this.handleOnHideDispatch}
            onSave={this.handleOnSaveDispatch}
          />
        </div>
        <PropertyControlledComponent
          controllerProperty={_get(deferRecommendationParams, 'isDeferRecommendationModalVisible')}>
          <DeferRecommendationModal
            handleCloseModal={this.handleDeferRecommendationModalClose}
            customerApprovedReviewRecommendations={_get(formValues, 'updateCustomerResponse')}
            deferRecommendationParams={deferRecommendationParams}
            parentOnAction={this.handleAction}
          />
        </PropertyControlledComponent>
      </>
    );
  }
}

RightPanel.propTypes = {
  recommendations: PropTypes.array,
  roDetails: PropTypes.object, //eslint-disable-line
  mpviDetails: PropTypes.object,
  togglePrimaryButton: PropTypes.func, //eslint-disable-line
  updateTechnicians: PropTypes.func,
  onEntityChange: PropTypes.func, //eslint-disable-line
  createRecommendationSummary: PropTypes.func, //eslint-disable-line
  updateBulkRORecommendationStatus: PropTypes.func,
  shouldShowDispatch: PropTypes.bool, //eslint-disable-line
  fetchCustomerNotificationDetails: PropTypes.func.isRequired,
  generateRecommendationPdf: PropTypes.func, // eslint-disable-line react/no-unused-prop-types
  getFormattedDateAndTime: PropTypes.func,
  ctaLockDetails: PropTypes.array,
  getCountOfPhoneNumberDigits: PropTypes.func,
};

RightPanel.defaultProps = {
  recommendations: EMPTY_ARRAY,
  shouldShowDispatch: true,
  roDetails: EMPTY_OBJECT,
  mpviDetails: EMPTY_OBJECT,
  togglePrimaryButton: _noop,
  updateTechnicians: _noop,
  createRecommendationSummary: _noop,
  onEntityChange: _noop,
  updateBulkRORecommendationStatus: _noop,
  generateRecommendationPdf,
  getFormattedDateAndTime: _noop,
  ctaLockDetails: EMPTY_ARRAY,
  getCountOfPhoneNumberDigits: _noop,
};

export { RightPanel as BaseReviewForm };

const mapStateToProps = state => ({
  payTypeConfigurations: getPayTypeConfigurations(state),
});

const mapDispatchToProps = {
  createRecommendationSummary,
  updateTechnicians,
  getRODetails,
  fetchJob,
  fetchJobs,
  markJobStale,
  fetchRecommendations,
  markRecommendationStale,
  updateBulkRORecommendationStatus,
  markROStale,
  fetchCustomerNotificationDetails,
  setCTALockDetails,
  fetchApprovalCount,
};

export default compose(
  connect(mapStateToProps, mapDispatchToProps),
  withPropertyConsumer,
  withTekionConversion
)(RightPanel);
