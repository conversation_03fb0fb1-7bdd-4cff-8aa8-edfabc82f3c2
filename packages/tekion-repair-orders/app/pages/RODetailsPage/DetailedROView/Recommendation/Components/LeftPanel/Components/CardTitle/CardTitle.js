import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';

import _noop from 'lodash/noop';

import ROEntityDescription from 'tcomponents/molecules/ROEntityDescription';
import PropertyControlledComponent from 'tcomponents/molecules/PropertyControlledComponent';
import { EMPTY_OBJECT, EMPTY_ARRAY } from 'tbase/app.constants';

import { getServiceTitle } from 'tbusiness/appServices/service/helpers/recommendation';

import RecommendationReader from 'readers/Recommendation.reader';
import ROConstraints from 'helpers/constraints';

import styles from './cardTitle.module.scss';

class CardTitle extends PureComponent {
  render() {
    const { data, containerClassName, renderBadge, recommendationTags, profitLossParams } = this.props;
    const { isProfitLossView } = profitLossParams;
    const { isIntermediateRecommendation } = data;
    return (
      <div className={`${styles.container} ${containerClassName}`}>
        <ROEntityDescription
          description={RecommendationReader.description(data)}
          highlightedText={getServiceTitle(data)}
          serialNumber={RecommendationReader.serialNumber(data)}
          shouldShowOpcodeName={ROConstraints.shouldShowOpcodeName()}
          isIntermediateRecommendation={isIntermediateRecommendation}
        />
        <PropertyControlledComponent controllerProperty={!isProfitLossView}>
          {renderBadge({ data, recommendationTags })}
        </PropertyControlledComponent>
      </div>
    );
  }
}

CardTitle.propTypes = {
  data: PropTypes.object,
  containerClassName: PropTypes.string,
  renderBadge: PropTypes.func,
  recommendationTags: PropTypes.array,
  profitLossParams: PropTypes.object,
};

CardTitle.defaultProps = {
  data: EMPTY_OBJECT,
  containerClassName: '',
  renderBadge: _noop,
  recommendationTags: EMPTY_ARRAY,
  profitLossParams: EMPTY_OBJECT,
};

export default CardTitle;
