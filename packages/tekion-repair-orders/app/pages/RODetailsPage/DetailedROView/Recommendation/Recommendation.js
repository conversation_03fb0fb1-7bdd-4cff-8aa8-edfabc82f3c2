import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';

import _map from 'lodash/map';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _noop from 'lodash/noop';
import _omit from 'lodash/omit';

import { toaster } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import ROReader from 'readers/RepairOrder.reader';
import RecommendationReader from 'readers/Recommendation.reader';
import {
  getRecommendationsAmountByPayType,
  getRecommendationsWithEstimateAmountForMultiPayer,
} from 'tbusiness/appServices/service/helpers/calculation';
import { getMultiPayTypeDefaultValues } from 'tbusiness/appServices/service/helpers/general';
import { getTaxLabelsByTaxType } from 'twidgets/appServices/service/helpers/taxRegime.helpers';

import {
  fetchRecommendationEstimatePricingDetails,
  fetchRecommendationEstimatePricingDetailsForMultiPayer,
} from 'actions/recommendation.action';
import { hasPermissionToViewRecommendationSummary } from 'permissions/roPermission';
import { getErrorMessage } from 'utils';
import { PAY_TYPE_VALUES } from 'tbase/constants/payTypes';
import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import { MODULE_TYPES } from 'tbusiness/appServices/service/constants/general';
import {
  MODULE_TYPE_VS_EXPANDED_FOOTER_FIELD_CONFIG,
  MODULE_TYPE_VS_COLLAPSED_FOOTER_FIELD_CONFIG,
} from 'twidgets/appServices/service/config/MultiPayTypeFooter.config';

import ROConstraints from 'helpers/constraints';
import { getTaxTypesForDealer } from 'helpers/tax.helper';

import { NOTIFICATION_TYPE } from 'utils/constants';
import { getTaxRegimeConfig } from 'utils/selectors';
import RepairOrderEnv from 'utils/repairOrderEnv';

import CollapsedFooterMultiPayType from 'organisms/LeftPanelFooterMultiPayer/CollapsedFooter';
import ExpandedFooterMultiPayType from 'organisms/LeftPanelFooterMultiPayer/ExpandedFooter';

import LeftPanel from './Components/LeftPanel';
import RightPanel from './Components/RightPanel';
import {
  getAdaptedEstimatePricingDetailsByPayType,
  getCustomerApprovedReviewRecommendations,
  getEstimatePricingDetailsAPIPayload,
  getEstimatePricingDetailsAPIPayloadForMultiPayer,
  shouldFetchRecommendationEstimatePricingDetails,
  getAdaptedEstimatePricingDetailsForMultiPayer,
  shouldFetchPreInvoicingRules,
} from './Recommendation.helper';
import { checkIfJobsAreAdded } from '../DetailedROView.helper';

import styles from './recommendation.module.scss';

class Recommendation extends PureComponent {
  state = {
    payType: PAY_TYPE_VALUES.CUSTOMER_PAY,
    isFetchingEstimatePricing: true,
  };

  componentDidMount() {
    this.fetchEstimatePricingDetails();
    const { handleUpdateSnapshotDetails, roDetails } = this.props;
    handleUpdateSnapshotDetails({
      moduleType: MODULE_TYPES.RO_TECH_RECOMMENDATIONS,
      assetId: ROReader.getROId(roDetails),
    });
  }

  componentDidUpdate(prevProps) {
    const { recommendations: prevRecommendations } = prevProps;
    const { recommendations: currentRecommendations, roDetails, validateRO } = this.props;
    const shouldFetchRecommendationPricingDetails = shouldFetchRecommendationEstimatePricingDetails(
      prevRecommendations,
      currentRecommendations
    );
    if (checkIfJobsAreAdded(prevRecommendations, currentRecommendations) || shouldFetchRecommendationPricingDetails) {
      const { handleUpdateSnapshotDetails, roDetails } = this.props;
      handleUpdateSnapshotDetails({
        moduleType: MODULE_TYPES.RO_TECH_RECOMMENDATIONS,
        assetId: ROReader.getROId(roDetails),
      });
    }
    if (shouldFetchPreInvoicingRules(roDetails, prevRecommendations, currentRecommendations)) {
      validateRO(ROReader.getROId(roDetails));
    }
    if (!shouldFetchRecommendationPricingDetails) return;
    this.fetchEstimatePricingDetails();
  }

  renderCollapsedFooter = props => (
    <CollapsedFooterMultiPayType
      {...props}
      collapsedFooterFieldConfig={MODULE_TYPE_VS_COLLAPSED_FOOTER_FIELD_CONFIG[MODULE_TYPES.RO_TECH_RECOMMENDATIONS]()}
    />
  );

  renderExpandedFooter = props => {
    const { includeFeesInEstimate, includeCouponsInEstimate, includeTaxesInEstimate } = _get(
      RepairOrderEnv,
      'settings'
    );
    return (
      <ExpandedFooterMultiPayType
        {...props}
        expandedFooterPanelFieldConfig={MODULE_TYPE_VS_EXPANDED_FOOTER_FIELD_CONFIG[
          MODULE_TYPES.RO_TECH_RECOMMENDATIONS
        ]({ includeFeesInEstimate, includeCouponsInEstimate, includeTaxesInEstimate })}
        collapsedFooterFieldConfig={MODULE_TYPE_VS_COLLAPSED_FOOTER_FIELD_CONFIG[
          MODULE_TYPES.RO_TECH_RECOMMENDATIONS
        ]()}
      />
    );
  };

  getFooterComponent = () =>
    ROConstraints.isServiceV3Enabled()
      ? { renderCollapsedFooter: this.renderCollapsedFooter, renderExpandedFooter: this.renderExpandedFooter }
      : EMPTY_OBJECT;

  fetchEstimatePricingDetails = () => {
    const { recommendations, roDetails } = this.props;
    const { roId } = ROReader.getId(roDetails);
    const taxDetailsByTaxType = getTaxLabelsByTaxType(
      ROReader.getTaxLabelConfigs(roDetails),
      getTaxRegimeConfig(ROReader.getSiteId(roDetails))
    );
    const customerApprovedReviewRecommendations = getCustomerApprovedReviewRecommendations(recommendations);
    const recommendationIdsForEstimate = _map(customerApprovedReviewRecommendations, RecommendationReader.id);
    const [funcToExec, payloadFuncToExc] = ROConstraints.isServiceV3Enabled()
      ? [fetchRecommendationEstimatePricingDetailsForMultiPayer, getEstimatePricingDetailsAPIPayloadForMultiPayer]
      : [fetchRecommendationEstimatePricingDetails, getEstimatePricingDetailsAPIPayload];
    this.setState({ isFetchingEstimatePricing: true });
    funcToExec(roId, payloadFuncToExc(recommendationIdsForEstimate))
      .then(estimatePricingDetails => {
        this.setState({
          estimatePricingDetailsByPayType: getAdaptedEstimatePricingDetailsByPayType(
            estimatePricingDetails,
            customerApprovedReviewRecommendations,
            taxDetailsByTaxType
          ),
          ...(ROConstraints.isServiceV3Enabled()
            ? {
                sanitizedPaySplitDetails: getAdaptedEstimatePricingDetailsForMultiPayer(
                  estimatePricingDetails,
                  customerApprovedReviewRecommendations
                ),
              }
            : EMPTY_OBJECT),
        });
      })
      .catch(error => {
        toaster(NOTIFICATION_TYPE.ERROR, getErrorMessage(error, __('Error Fetching Estimate Pricing Details')));
      })
      .finally(() => {
        this.setState({ isFetchingEstimatePricing: false });
      });
  };

  handlePayTypeChange = ({ payload: { value } }) => {
    this.setState({ payType: value });
  };

  handleLeftPanelFooterSelectionChange = ({ selectedSubPayTypes, selectedPayers, selectedBasePayType }) =>
    this.setState({
      leftPanelFooterSelection: {
        isLeftPanelFooterModified: true,
        selectedSubPayTypes,
        selectedPayers,
        selectedBasePayType,
      },
    });

  getFooterProps = () => {
    if (!ROConstraints.isServiceV3Enabled()) return EMPTY_OBJECT;
    return {
      handleLeftPanelFooterSelectionChange: this.handleLeftPanelFooterSelectionChange,
    };
  };

  renderLeftPanel = props => {
    const {
      payType,
      isFetchingEstimatePricing,
      estimatePricingDetailsByPayType,
      sanitizedPaySplitDetails,
      leftPanelFooterSelection,
    } = this.state;
    const {
      recommendations,
      isNewAddRecommendationViewEnabled,
      payTypeConfigurations,
      roDetails,
      snapshotDetails,
      intermediateRecommendations,
    } = props;
    const estimatePricingDetails = _get(estimatePricingDetailsByPayType, payType) || EMPTY_OBJECT;
    const snapshotDetailsForModule = _get(snapshotDetails, [MODULE_TYPES.RO_TECH_RECOMMENDATIONS], EMPTY_OBJECT);
    const shouldShowSnapshotAmounts = _get(snapshotDetails, 'showSnapshotsForRORecommendations', false);
    const roEntitiesAmount = ROConstraints.isServiceV3Enabled()
      ? getMultiPayTypeDefaultValues({
          paySplit: sanitizedPaySplitDetails,
          payTypeConfigurations,
          payers: ROReader.getPayers(roDetails),
          leftPanelFooterSelection,
          shouldShowSnapshotAmounts: false,
          dealerTaxTypes: getTaxTypesForDealer(),
          taxConfigDetails: ROReader.getTaxLabelConfigs(roDetails),
        })
      : estimatePricingDetails;
    const { selectedPayers, selectedSubPayTypes } = roEntitiesAmount;
    const recommendationsWithAmount = ROConstraints.isServiceV3Enabled()
      ? getRecommendationsWithEstimateAmountForMultiPayer({
          recommendations,
          selectedPayers,
          selectedSubPayTypes,
          shouldCalculateBasedOnSelection: true,
          snapshotRecDetails: snapshotDetailsForModule,
        })
      : getRecommendationsAmountByPayType(recommendations, payType);
    const intermediateRecommendationsWithAmount = ROConstraints.isServiceV3Enabled()
      ? getRecommendationsWithEstimateAmountForMultiPayer({
          recommendations: intermediateRecommendations,
          selectedPayers,
          selectedSubPayTypes,
          shouldCalculateBasedOnSelection: true,
          snapshotRecDetails: snapshotDetailsForModule,
        })
      : getRecommendationsAmountByPayType(intermediateRecommendations, payType);
    return (
      <LeftPanel
        {...props}
        roEntitiesAmount={roEntitiesAmount}
        payTypeConfigurations={payTypeConfigurations}
        isFetchingEstimatePricing={isFetchingEstimatePricing}
        recommendations={recommendationsWithAmount}
        intermediateRecommendations={intermediateRecommendationsWithAmount}
        payType={payType}
        onPayTypeChange={this.handlePayTypeChange}
        showSummaryView={hasPermissionToViewRecommendationSummary() && !_isEmpty(recommendations)}
        showAddRecommendation={isNewAddRecommendationViewEnabled}
        shouldShowSnapshotAmounts={shouldShowSnapshotAmounts}
        {...this.getFooterProps()}
        {...this.getFooterComponent()}
      />
    );
  };

  renderRightPanel = props => (
    <RightPanel
      {..._omit(props, [
        'intermediateRecommendations',
        'intermediateRecommendationByIds',
        'queueIsProcessing',
        'queueCount',
        'queueItems',
      ])}
    />
  );

  render() {
    const { props } = this;
    return (
      <div className={`${styles.container} full-width`}>
        {this.renderLeftPanel(props)}
        {this.renderRightPanel(props)}
      </div>
    );
  }
}

Recommendation.propTypes = {
  recommendations: PropTypes.array,
  roDetails: PropTypes.object,
  payTypeConfigurations: PropTypes.array,
  handleUpdateSnapshotDetails: PropTypes.func,
  validateRO: PropTypes.func,
  intermediateRecommendations: PropTypes.array,
};

Recommendation.defaultProps = {
  recommendations: EMPTY_ARRAY,
  roDetails: EMPTY_OBJECT,
  payTypeConfigurations: EMPTY_ARRAY,
  handleUpdateSnapshotDetails: _noop,
  validateRO: _noop,
  intermediateRecommendations: EMPTY_ARRAY,
};

export default Recommendation;
