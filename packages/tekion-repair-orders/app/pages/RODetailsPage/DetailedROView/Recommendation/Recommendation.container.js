import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';

import { defaultMemoize } from 'reselect';

import _map from 'lodash/map';
import _isEmpty from 'lodash/isEmpty';
import _noop from 'lodash/noop';
import _get from 'lodash/get';

import { EMPTY_OBJECT, EMPTY_ARRAY } from 'tbase/app.constants';
import Heading from 'tcomponents/atoms/Heading';
import Button from 'tcomponents/atoms/Button';
import { MODULE_TYPES } from 'tbusiness/appServices/service/constants/general';

import ROConstraints from 'helpers/constraints';
import { RO_ADD_ENTITY_TYPE_VS_CONFIG } from 'pages/ROAddJob/ROAddJob.helpers';
import { EMPTY_PLACEHOLDER_IMAGE_URL } from 'tbase/constants/general';
import { hasPermissionToCreateRoSubItem } from 'permissions/roPermission';
import { RO_DETAILS_ENTITY_TYPE } from 'utils/constants';
import Recommendation from './Recommendation';

import styles from './recommendation.module.scss';

class RecommendationContainer extends PureComponent {
  getMemoizedRecommendations = defaultMemoize((byId, ids) => _map(ids, id => byId[id]));

  renderEmptyPlaceholder = () => {
    const { roDetails, type, onToggleAddFlow } = this.props;
    const { label } =
      (RO_ADD_ENTITY_TYPE_VS_CONFIG[type] || RO_ADD_ENTITY_TYPE_VS_CONFIG[RO_DETAILS_ENTITY_TYPE.JOB])(this.props) ||
      EMPTY_OBJECT;
    return (
      <div className={`${styles.emptyPlaceholder} full-width full-height`}>
        <img className={styles.noRecommendationImg} src={EMPTY_PLACEHOLDER_IMAGE_URL} alt="No recommendations" />
        <div className="flex-center">
          <Heading size={2} regular>
            {__('No recommendations added')}
          </Heading>
          {hasPermissionToCreateRoSubItem(roDetails, type) && (
            <Button className="ml-4" onClick={onToggleAddFlow(true)} view="secondary">
              {label}
            </Button>
          )}
        </div>
      </div>
    );
  };

  renderRecommendation = isNewAddRecommendationViewEnabled => {
    const {
      recommendationByIds,
      recommendationIds,
      ctaLockDetails,
      intermediateRecommendationIds,
      intermediateRecommendationByIds,
      ...restProps
    } = this.props;
    return (
      <Recommendation
        recommendations={this.getMemoizedRecommendations(recommendationByIds, recommendationIds)}
        recommendationByIds={recommendationByIds}
        intermediateRecommendations={this.getMemoizedRecommendations(
          intermediateRecommendationByIds,
          intermediateRecommendationIds
        )}
        intermediateRecommendationByIds={intermediateRecommendationByIds}
        isNewAddRecommendationViewEnabled={isNewAddRecommendationViewEnabled}
        ctaLockDetails={_get(ctaLockDetails, [MODULE_TYPES.RO, MODULE_TYPES.RO_TECH_RECOMMENDATIONS])}
        {...restProps}
      />
    );
  };

  render() {
    const { recommendationIds, roDetails, type } = this.props;
    const isNewAddRecommendationViewEnabled = ROConstraints.canAccessNewRoute(roDetails, type);
    if (_isEmpty(recommendationIds) && !isNewAddRecommendationViewEnabled) return this.renderEmptyPlaceholder();
    return this.renderRecommendation(isNewAddRecommendationViewEnabled);
  }
}

RecommendationContainer.propTypes = {
  recommendationByIds: PropTypes.object,
  recommendationIds: PropTypes.array,
  jobIds: PropTypes.array,
  roDetails: PropTypes.object,
  type: PropTypes.string.isRequired,
  onToggleAddFlow: PropTypes.func,
  ctaLockDetails: PropTypes.array,
  intermediateRecommendationIds: PropTypes.array,
  intermediateRecommendationByIds: PropTypes.object,
};

RecommendationContainer.defaultProps = {
  recommendationByIds: EMPTY_OBJECT,
  recommendationIds: EMPTY_ARRAY,
  jobIds: EMPTY_ARRAY,
  roDetails: EMPTY_OBJECT,
  onToggleAddFlow: () => _noop,
  ctaLockDetails: EMPTY_ARRAY,
  intermediateRecommendationIds: EMPTY_ARRAY,
  intermediateRecommendationByIds: EMPTY_OBJECT,
};

export default RecommendationContainer;
