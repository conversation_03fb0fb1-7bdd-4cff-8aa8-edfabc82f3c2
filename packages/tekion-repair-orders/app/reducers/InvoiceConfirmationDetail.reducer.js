import produce from 'immer';

import _assign from 'lodash/assign';
import _set from 'lodash/set';

import { EMPTY_OBJECT } from 'tbase/app.constants';

import { invoiceConfirmationActions } from 'actions/invoiceConfirmation.actionTypes';

const DEFAULT_STATE = EMPTY_OBJECT;

export default (state = DEFAULT_STATE, action) =>
  produce(state, draft => {
    switch (action.type) {
      case invoiceConfirmationActions.FETCH_INVOICE_CONFIRMATION_REQUEST:
        draft.isFetching = true;
        _assign(draft, DEFAULT_STATE);
        break;

      case invoiceConfirmationActions.FETCH_INVOICE_CONFIRMATION_SUCCESS: {
        draft.isFetching = false;
        const { jobs, ro, couponDistributions, postingSetting, serviceContracts } = action.payload;
        _set(draft, 'jobs', jobs);
        _set(draft, 'ro', ro);
        _set(draft, 'couponDistributions', couponDistributions);
        _set(draft, 'postingSetting', postingSetting);
        _set(draft, 'serviceContracts', serviceContracts);
        break;
      }

      case invoiceConfirmationActions.FETCH_AFTER_MANAGE_EXCESS_SELECTION: {
        const { toggleFetchAfterManageExcessSelection } = action.payload;
        _set(draft, 'toggleFetchAfterManageExcessSelection', toggleFetchAfterManageExcessSelection);
        break;
      }

      case invoiceConfirmationActions.FETCH_INVOICE_CONFIRMATION_FAILURE:
        draft.isFetching = false;
        draft.invoiceConfirmationDetails = EMPTY_OBJECT;
        break;
      // skip default
    }
  });
