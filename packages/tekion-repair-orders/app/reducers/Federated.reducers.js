import {
  getROListReducer<PERSON><PERSON>,
  getRONotesReducer<PERSON>ey,
  getLaborDetailReducerKey,
  getROBootstrapReducer<PERSON>ey,
  getServiceMenuReducer<PERSON>ey,
  getLaborPricingReducer<PERSON>ey,
  getAsyncQueueReducer<PERSON>ey,
} from 'twidgets/appServices/service/helpers/reducer.helper';

import roListReducer from './ROList.reducers';
import noteReducer from './Notes.reducers';
import laborDetail from '../pages/laborPrice/LaborDetail/laborDetail.reducer';
import laborPricing from '../pages/laborPrice/LaborPricing/reducer';
import bootstrap from './Bootstrap.reducers';
import serviceMenuReducer from '../pages/ServiceMenuSetups/ServiceMenuSetups.reducers';
import asyncQueueReducer from './asyncQueue.reducer';

export default {
  [getROListReducerKey()]: roListReducer,
  [getRONotesReducer<PERSON>ey()]: noteReducer,
  [getLaborDetailReducerKey()]: laborDetail,
  [getLaborPricingReducerKey()]: laborPricing,
  [getROBootstrapReducerKey()]: bootstrap,
  [getServiceMenuReducerKey()]: serviceMenuReducer,
  [getAsyncQueueReducerKey()]: asyncQueueReducer,
};
