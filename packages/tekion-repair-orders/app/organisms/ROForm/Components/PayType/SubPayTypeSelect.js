import React, { useState, useMemo } from 'react';
import PropTypes from 'prop-types';

import _get from 'lodash/get';
import _noop from 'lodash/noop';
import _map from 'lodash/map';
import _groupBy from 'lodash/groupBy';
import _compact from 'lodash/compact';
import _filter from 'lodash/filter';
import _isEmpty from 'lodash/isEmpty';
import _includes from 'lodash/includes';
import _lowerCase from 'lodash/lowerCase';
import _size from 'lodash/size';

import Icon from 'tcomponents/atoms/FontIcon';
import Button from 'tcomponents/atoms/Button';
import Heading from 'tcomponents/atoms/Heading';
import Menu from 'tcomponents/molecules/Menu';
import Search from 'tcomponents/molecules/Search';
import DropDown, { DROPDOWN_PLACEMENT } from 'tcomponents/molecules/DropDown';
import PropertyControlledComponent from 'tcomponents/molecules/PropertyControlledComponent';
import PayType from 'tcomponents/organisms/PayType';

import { getPayType, getBasePayTypeForSubPayType } from 'tbusiness/appServices/service/helpers/payTypes.helpers';
import { getPayTypeOptions } from 'twidgets/appServices/service/molecules/PayTypeSelect/PayTypeSelect.helpers';

import { EMPTY_ARRAY, EMPTY_STRING, EMPTY_OBJECT } from 'tbase/app.constants';
import actionTypes from 'tcomponents/organisms/FormBuilder/constants/actionTypes';
import { DEFAULT_ALLOWED_PAY_TYPES } from 'organisms/ROJobForm/ROJobForm.constants';

import { SUB_PAY_TYPE_FIELD } from '../../ROForm.constants';

import { PAY_TYPE_VS_RADIO_OPTIONS, PAY_TYPE_ACRONYM_LABEL, PAY_TYPE_CONFIG } from './SubPayTypeSelect.factory';
import { getActiveFilteredOptions, getBasePayTypeDisabled, getAdditionalPayTypeProps } from './SubPayTypeSelect.helper';

import styles from './SubPayTypeSelect.module.scss';

const getPayTypeProps = ({
  payType,
  payTypeConfigurations,
  shouldDisableBasePayType,
  allowedPayTypes,
  roPayTypeStatuses,
  showTooltipForDisabledPayType,
  payTypeTooltipText,
  radioOptions,
}) => {
  const basePayType = getBasePayTypeForSubPayType(payType, payTypeConfigurations);
  const { isCustomerPayDisabled, isWarrantyPayDisabled, isInternalPayDisabled } = getBasePayTypeDisabled({
    shouldDisableBasePayType,
    roPayTypeStatuses,
    allowedPayTypes,
  });

  const getAdditionalProps = getAdditionalPayTypeProps({
    showTooltipForDisabledPayType,
    payTypeTooltipText,
    allowedPayTypes,
  });

  const getRadioOptions = PAY_TYPE_VS_RADIO_OPTIONS[basePayType] || PAY_TYPE_VS_RADIO_OPTIONS.DEFAULT;
  const getPayTypeConfig = PAY_TYPE_CONFIG[basePayType] || PAY_TYPE_CONFIG.DEFAULT;
  const getPayTypeAcronymLabels = PAY_TYPE_ACRONYM_LABEL[basePayType] || PAY_TYPE_ACRONYM_LABEL.DEFAULT;
  return {
    radioOptions: !_isEmpty(radioOptions)
      ? radioOptions
      : getRadioOptions({
          payType,
          isCustomerPayDisabled,
          isWarrantyPayDisabled,
          isInternalPayDisabled,
          getAdditionalProps,
        }),
    payTypeConfig: getPayTypeConfig(payType),
    payTypeAcronymLabels: getPayTypeAcronymLabels(payType),
  };
};

const SubPayTypeSelect = props => {
  const {
    value,
    payTypeConfigurations,
    disabled,
    isDisabled,
    shouldDisableBasePayType,
    allowedPayTypes,
    roPayTypeStatuses,
    showTooltipForDisabledPayType,
    payTypeTooltipText,
    radioOptions,
  } = props;
  const payTypeOptions = getPayTypeOptions(payTypeConfigurations);
  const activePayTypeOptions = getActiveFilteredOptions({
    filteredOptions: payTypeOptions,
    radioOptions,
    shouldDisableBasePayType,
    roPayTypeStatuses,
    allowedPayTypes,
    subPayType: value,
  });

  const [searchQuery, setSearchQuery] = useState(EMPTY_STRING);
  const [visible, onVisibleChange] = useState(false);

  const handlePayTypeChange = action => {
    const { onAction, payTypeConfigurations } = props;
    const value = _get(action, 'payload.value', _get(action, 'key'));
    onVisibleChange(false);
    onAction({
      type: actionTypes.ON_FIELD_CHANGE,
      payload: {
        id: SUB_PAY_TYPE_FIELD,
        value: {
          showModal: true,
          newSubPayType: value,
          newPayType: getPayType(payTypeConfigurations, value),
          additional: { shouldTriggerOnEntityChange: false, isPayTypeChange: true },
        },
      },
    });
  };

  const renderDropdownOption = dropdownOption => {
    const { label, value } = dropdownOption;
    return (
      <Menu.Item value={value} key={value}>
        {label}
      </Menu.Item>
    );
  };

  const getGroups = data => {
    const { groupKey } = props;
    const groupedOptions = _groupBy(data, groupKey);
    return _compact(
      _map(Object.keys(groupedOptions), key => {
        const groups = groupedOptions[key];
        const filteredOptions = _filter(groups, ({ label }) => _includes(_lowerCase(label), _lowerCase(searchQuery)));
        const activeFilteredOptions = getActiveFilteredOptions({
          filteredOptions,
          radioOptions,
          shouldDisableBasePayType,
          roPayTypeStatuses,
          allowedPayTypes,
          subPayType: value,
        });
        if (_isEmpty(activeFilteredOptions)) return null;
        return (
          <Menu.ItemGroup key={key} title={<Heading size={5}>{key}</Heading>}>
            {_map(activeFilteredOptions, item => renderDropdownOption(item))}
          </Menu.ItemGroup>
        );
      })
    );
  };

  const handleSearch = e => {
    setSearchQuery(e.currentTarget.value);
  };

  const renderDropdownMenu = () => (
    <div className={styles.dropDownMenu}>
      <Search placeholder={__('Search')} value={searchQuery} onChange={handleSearch} autoFocus />
      <Menu className={styles.subPayTypeMenu} onClick={handlePayTypeChange}>
        {getGroups(payTypeOptions)}
      </Menu>
    </div>
  );

  const payTypeProps = useMemo(
    () =>
      getPayTypeProps({
        payType: value,
        payTypeConfigurations,
        shouldDisableBasePayType,
        allowedPayTypes,
        roPayTypeStatuses,
        showTooltipForDisabledPayType,
        payTypeTooltipText,
        radioOptions,
      }),
    [
      value,
      payTypeConfigurations,
      shouldDisableBasePayType,
      allowedPayTypes,
      roPayTypeStatuses,
      showTooltipForDisabledPayType,
      payTypeTooltipText,
      radioOptions,
    ]
  );

  const isPayTypeDisabled = isDisabled || disabled;

  const shouldShowAddSubPayTypeButton = _size(activePayTypeOptions) > 3 && !isPayTypeDisabled;

  return (
    <div className={styles.containerClass}>
      <PayType
        {...props}
        radioClassName={styles.radioClassName}
        onAction={handlePayTypeChange}
        {...payTypeProps}
        disabledPayTypeClassName={styles.disabledPayTypeClassName}
      />
      <PropertyControlledComponent controllerProperty={shouldShowAddSubPayTypeButton}>
        <DropDown
          placement={DROPDOWN_PLACEMENT.BOTTOM_CENTER}
          overlay={renderDropdownMenu}
          trigger={['click']}
          disabled={isPayTypeDisabled}
          visible={visible}
          onVisibleChange={onVisibleChange}>
          <div className={styles.addButtonDiv}>
            <Button view={Button.VIEW.TERTIARY} className={styles.addButton}>
              <Icon className={styles.plusIcon}>icon-add</Icon>
            </Button>
          </div>
        </DropDown>
      </PropertyControlledComponent>
    </div>
  );
};

SubPayTypeSelect.propTypes = {
  onAction: PropTypes.func,
  payTypeConfigurations: PropTypes.array,
  value: PropTypes.string,
  groupKey: PropTypes.string,
  disabled: PropTypes.bool,
  isDisabled: PropTypes.bool,
  shouldDisableBasePayType: PropTypes.bool,
  allowedPayTypes: PropTypes.object,
  roPayTypeStatuses: PropTypes.object,
  showTooltipForDisabledPayType: PropTypes.bool,
  payTypeTooltipText: PropTypes.string,
  radioOptions: PropTypes.array,
};
SubPayTypeSelect.defaultProps = {
  onAction: _noop,
  payTypeConfigurations: EMPTY_ARRAY,
  value: EMPTY_STRING,
  groupKey: 'group',
  disabled: false,
  isDisabled: false,
  shouldDisableBasePayType: false,
  allowedPayTypes: DEFAULT_ALLOWED_PAY_TYPES,
  roPayTypeStatuses: EMPTY_OBJECT,
  showTooltipForDisabledPayType: false,
  payTypeTooltipText: EMPTY_STRING,
  radioOptions: undefined,
};

export default SubPayTypeSelect;
