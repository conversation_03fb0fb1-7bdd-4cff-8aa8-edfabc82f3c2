import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { defaultMemoize } from 'reselect';

import _noop from 'lodash/noop';
import _get from 'lodash/get';
import _once from 'lodash/once';
import _isEmpty from 'lodash/isEmpty';
import _reduce from 'lodash/reduce';
import _memoize from 'lodash/memoize';

import { TableFieldWithActions } from 'tcomponents/molecules/tableInputField';
import { REMOVE_ACTION } from 'tcomponents/molecules/tableInputField/constants/general';
import CostCenter from 'twidgets/appServices/service/organisms/CostCenter';
import TdInputComponent from 'twidgets/appServices/service/molecules/TdInputComponent';

import { tget } from 'tbase/utils/general';
import { getBasePayTypeForSubPayType } from 'tbusiness/appServices/service/helpers/payTypes.helpers';
import { isSourceTypeService } from 'tbusiness/appServices/service/helpers/fee';

import { EMPTY_OBJECT, EMPTY_ARRAY, EMPTY_STRING } from 'tbase/app.constants';
import { FEE_DATA_FIELD_NAMES } from 'tbase/constants/fee.constants';

import ROConstraints from 'helpers/constraints';
import { getIsChargeCustomer } from 'helpers/customer.helper';
import { getCostCenterOptions } from 'helpers/costCenters.helper';
import { isPostingAtBasePayType } from 'helpers/payer.helper';
import ROFeeReader from 'readers/ROFee.reader';
import ROReader from 'readers/RepairOrder.reader';
import { getPaymentMethodPricingMethod, getPayTypesByBasePayType } from 'utils/helpers';

import {
  isCostCenterDisabled,
  getFeeColumns,
  getCostCenterValue,
  isPayTypeCustomerPayDerivative,
  getColumnsToOmit,
} from './FeeTable.helper';
import {
  RO_FEE_TABLE_COLUMNS,
  FEE_TABLE_COLUMNS,
  RO_FEE_TABLE_COLUMNS_FOR_MULTI_PAYER,
  FEE_TABLE_COLUMNS_FOR_MULTI_PAYER,
} from './FeeTable.column';

import styles from '../../fee.module.scss';

const getTableRowKey =
  ({ assetId, payType, subPayType, isServiceV3Enabled } = EMPTY_OBJECT) =>
  (_, rowGroupProps) => {
    const tableTrGroupNestingPath = _get(rowGroupProps, 'nestingPath').join(',');
    const payTypeOrSubPayType = isServiceV3Enabled ? subPayType : payType;
    return `${tableTrGroupNestingPath}_${payTypeOrSubPayType}_${assetId}`;
  };

const REMOVE_ROW_ACTION = [REMOVE_ACTION];
const DISABLED_REMOVE_ROW_ACTION = [{ ...REMOVE_ACTION, disabled: true }];

const getTableItems = (data, canAddFees) => {
  const valueToAdd = {};
  if (!canAddFees) return data;
  return _isEmpty(data) ? [valueToAdd] : [...data, valueToAdd];
};

const getDefaultExpanded = (fees, isServiceV3Enabled) =>
  _reduce(
    fees,
    (acc, fee, index) => {
      const payType = ROFeeReader.payType(fee);
      if (isPayTypeCustomerPayDerivative(payType, isServiceV3Enabled)) return acc;
      return { ...acc, [index]: {} };
    },
    EMPTY_OBJECT
  );

class FeeTable extends PureComponent {
  isServiceV3Enabled = ROConstraints.isServiceV3Enabled();

  getFeeColumns = _memoize(getFeeColumns);

  getRowActions = ({ original }) => {
    const { isDeleteFeeDisabled } = this.props;
    return isDeleteFeeDisabled || !ROConstraints.canEditFee(original) || !isSourceTypeService(original)
      ? DISABLED_REMOVE_ROW_ACTION
      : REMOVE_ROW_ACTION;
  };

  getMemoizedDefaultExpanded = _once(getDefaultExpanded);

  getAdditionalFeeTableProps = defaultMemoize(
    (
      isSaleAmountDisabled,
      isCostAmountDisabled,
      isDisabledByPayType,
      isDisabledByPayer,
      disabled,
      menuPortalTarget,
      isROLevelFeeSelection,
      assetId,
      payType,
      fees,
      locale,
      profitLossParams,
      grossInfo,
      subPayType,
      isServiceV3Enabled,
      payTypeConfigurations,
      isJobWithDynamicSplit,
      siteId,
      payers,
      payerPayTypeConfiguration,
      defaultSubPayType,
      customerId,
      shouldDisableBasePayType
    ) => ({
      isSaleAmountDisabled,
      disabled,
      isDisabledByPayType,
      isDisabledByPayer,
      isCostAmountDisabled,
      menuPortalTarget,
      isROLevelFeeSelection,
      assetId,
      payType,
      fees,
      locale,
      profitLossParams,
      grossInfo,
      subPayType,
      isServiceV3Enabled,
      payTypesByBasePayType: getPayTypesByBasePayType(payTypeConfigurations),
      isJobWithDynamicSplit,
      siteId,
      payers,
      payerPayTypeConfiguration,
      defaultSubPayType,
      payTypeConfigurations,
      customerId,
      shouldDisableBasePayType,
    })
  );

  getCustomTableComponentProps = () => {
    const { value, payTypeConfigurations } = this.props;
    return {
      TdComponent: TdInputComponent,
      defaultExpanded: this.getMemoizedDefaultExpanded(value, payTypeConfigurations, this.isServiceV3Enabled),
      checkExpanderVisibility: this.isExpanderHidden,
    };
  };

  isExpanderHidden = ({ original }) => {
    const payType = ROFeeReader.payType(original);
    return isPayTypeCustomerPayDerivative(payType, this.isServiceV3Enabled);
  };

  handleCostCenterOnAction =
    index =>
    ({ type, payload }) => {
      const { onAction } = this.props;
      onAction({ type, payload: { ...payload, index } });
    };

  renderSubComponent = ({ original, index }) => {
    const {
      error,
      warning,
      costCentersByPayType,
      disabled,
      isDisabledByPayType,
      isDisabledByPayer,
      payTypeConfigurations,
      roDetails,
    } = this.props;
    const [payType, subPayType] = [ROFeeReader.payType(original), ROFeeReader.subPayType(original)];
    const payerId = ROFeeReader.payerId(original);
    const isPayerEditDisabled = tget(isDisabledByPayer, payerId, false);
    const isPayTypeEditDisabled = tget(isDisabledByPayType, payType, false);
    const isChargeCustomer = getIsChargeCustomer(ROFeeReader.payerData(original), roDetails, payerId);

    const basePayType = this.isServiceV3Enabled
      ? getBasePayTypeForSubPayType(subPayType, payTypeConfigurations)
      : payType;
    const costCenterDisabled = isCostCenterDisabled({
      disabled,
      isPayTypeEditDisabled,
      isPayerEditDisabled,
      fee: original,
      payType: basePayType,
    });
    if (isPayTypeCustomerPayDerivative(payType, this.isServiceV3Enabled)) return true;
    return (
      <CostCenter
        onAction={this.handleCostCenterOnAction(index)}
        id={FEE_DATA_FIELD_NAMES.COST_CENTER}
        value={getCostCenterValue(ROFeeReader.costCenters(original))}
        error={_get(error, `${index}.${FEE_DATA_FIELD_NAMES.COST_CENTER}`)}
        warning={_get(warning, `${index}.${FEE_DATA_FIELD_NAMES.COST_CENTER}`)}
        fieldClassName="p-t-12 p-b-12"
        canAddCostCenter={!disabled && !isPayTypeEditDisabled && !isPayerEditDisabled}
        disabled={costCenterDisabled}
        options={getCostCenterOptions({
          costCentersByPayType,
          payType: basePayType,
          isChargeCustomer,
        })}
        costCenterSplitType={_get(getPaymentMethodPricingMethod(), '[0].value')}
        showControlColumns
        showControlTagColumn={ROConstraints.isAccountingMultiControlEnabled()}
      />
    );
  };

  render() {
    const {
      onAction,
      value,
      fieldClassName,
      disabled,
      isAddFeeDisabled,
      isSaleAmountDisabled,
      isCostAmountDisabled,
      menuPortalTarget,
      isROLevelFeeSelection,
      assetId,
      payType,
      isDisabledByPayType,
      isDisabledByPayer,
      fees,
      locale,
      profitLossParams,
      grossInfo,
      subPayType,
      payTypeConfigurations,
      isJobWithDynamicSplit,
      siteId,
      payerPayTypeConfiguration,
      defaultSubPayType,
      payers,
      roDetails,
    } = this.props;
    const { isProfitLossView } = profitLossParams;
    return (
      <TableFieldWithActions
        value={getTableItems(value, !isAddFeeDisabled)}
        columns={this.getFeeColumns({
          isProfitLossView,
          isROLevelFeeSelection,
          roFeeColumns: this.isServiceV3Enabled ? RO_FEE_TABLE_COLUMNS_FOR_MULTI_PAYER : RO_FEE_TABLE_COLUMNS,
          feeColumns: this.isServiceV3Enabled ? FEE_TABLE_COLUMNS_FOR_MULTI_PAYER : FEE_TABLE_COLUMNS,
          columnsToOmit: getColumnsToOmit(siteId),
        })}
        id="feeTable"
        className={`full-width ${fieldClassName} ${styles.feeTable}`}
        onAction={onAction}
        SubComponent={isROLevelFeeSelection ? this.renderSubComponent : undefined}
        shouldAddNewRow={false}
        getActionsForRow={this.getRowActions}
        customTableComponentProps={this.getCustomTableComponentProps}
        additional={this.getAdditionalFeeTableProps(
          isSaleAmountDisabled,
          isCostAmountDisabled,
          isDisabledByPayType,
          isDisabledByPayer,
          disabled,
          menuPortalTarget,
          isROLevelFeeSelection,
          assetId,
          payType,
          fees,
          locale,
          profitLossParams,
          grossInfo,
          subPayType,
          this.isServiceV3Enabled,
          payTypeConfigurations,
          isJobWithDynamicSplit,
          siteId,
          payers,
          payerPayTypeConfiguration,
          defaultSubPayType,
          ROReader.getBillingCustomerId(roDetails),
          isPostingAtBasePayType(ROReader.postingSettings(roDetails))
        )}
        getTableRowKey={getTableRowKey({ assetId, payType, subPayType, isServiceV3Enabled: this.isServiceV3Enabled })}
      />
    );
  }
}

FeeTable.propTypes = {
  onAction: PropTypes.func,
  value: PropTypes.array,
  costCentersByPayType: PropTypes.object,
  error: PropTypes.object,
  warning: PropTypes.object,
  isDisabledByPayType: PropTypes.object,
  isDisabledByPayer: PropTypes.object,
  disabled: PropTypes.bool,
  isSaleAmountDisabled: PropTypes.bool,
  isCostAmountDisabled: PropTypes.bool,
  isROLevelFeeSelection: PropTypes.bool,
  isDeleteFeeDisabled: PropTypes.bool,
  isAddFeeDisabled: PropTypes.bool,
  menuPortalTarget: PropTypes.node,
  assetId: PropTypes.string,
  fieldClassName: PropTypes.string,
  payType: PropTypes.string,
  fees: PropTypes.array,
  locale: PropTypes.string,
  profitLossParams: PropTypes.object,
  grossInfo: PropTypes.object,
  subPayType: PropTypes.string,
  payTypeConfigurations: PropTypes.array,
  isJobWithDynamicSplit: PropTypes.bool,
  siteId: PropTypes.string,
  defaultSubPayType: PropTypes.string,
  payerPayTypeConfiguration: PropTypes.object,
  payers: PropTypes.array,
  roDetails: PropTypes.object,
};

FeeTable.defaultProps = {
  onAction: _noop,
  value: EMPTY_ARRAY,
  costCentersByPayType: EMPTY_OBJECT,
  error: EMPTY_OBJECT,
  warning: EMPTY_OBJECT,
  isDisabledByPayType: EMPTY_OBJECT,
  isDisabledByPayer: EMPTY_OBJECT,
  menuPortalTarget: document.body,
  isSaleAmountDisabled: false,
  isCostAmountDisabled: false,
  isDeleteFeeDisabled: false,
  isROLevelFeeSelection: false,
  isAddFeeDisabled: false,
  disabled: false,
  assetId: EMPTY_STRING,
  fieldClassName: EMPTY_STRING,
  payType: EMPTY_STRING,
  fees: EMPTY_ARRAY,
  locale: EMPTY_STRING,
  profitLossParams: EMPTY_OBJECT,
  grossInfo: EMPTY_OBJECT,
  subPayType: EMPTY_STRING,
  payTypeConfigurations: EMPTY_ARRAY,
  isJobWithDynamicSplit: false,
  siteId: EMPTY_STRING,
  defaultSubPayType: EMPTY_STRING,
  payerPayTypeConfiguration: EMPTY_OBJECT,
  payers: EMPTY_ARRAY,
  roDetails: EMPTY_OBJECT,
};

export default FeeTable;
