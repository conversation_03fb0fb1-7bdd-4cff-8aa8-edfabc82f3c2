import _property from 'lodash/property';
import _get from 'lodash/get';
import _head from 'lodash/head';
import _omit from 'lodash/omit';
import _filter from 'lodash/filter';
import _isEmpty from 'lodash/isEmpty';
import _pick from 'lodash/pick';
import _differenceBy from 'lodash/differenceBy';
import _map from 'lodash/map';
import _uniqBy from 'lodash/uniqBy';
import _every from 'lodash/every';
import _includes from 'lodash/includes';
import _reduce from 'lodash/reduce';
import _noop from 'lodash/noop';
import _toNumber from 'lodash/toNumber';
import _dropRight from 'lodash/dropRight';
import _last from 'lodash/last';
import _keyBy from 'lodash/keyBy';
import _flatMap from 'lodash/flatMap';
import _has from 'lodash/has';

import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import {
  CHECKIN_MEDIUM_VS_CHECKIN_SETTINGS_SOURCE,
  CHECKIN_SOURCE_VS_CHECKIN_MEDIUM,
} from 'tbase/constants/service/configurator';
import {
  getAdaptedOperation,
  getAdaptedJobForComputeJob,
  getLaborRateFields,
} from 'tbusiness/appServices/service/utils/operation';
import { isPartSelectedByDefault } from 'tbusiness/appServices/service/utils/partAvailability';
import { isMpviOperation } from 'tbusiness/appServices/service/helpers/operation';
import { getPartColumnsToOmit } from 'twidgets/appServices/service/organisms/PartsAvailabilityTable/partsAvailabilityTable.helpers';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';

import { ID_FIELD_NAME } from 'tbusiness/appServices/service/readers/Job';
import OperationReader, {
  OPCODE_FIELD_ID,
  WARRANTY_OEM_OPCODE_WITH_LABOR_DETAILS,
  OPERATION_ID_FIELD_NAME,
} from 'readers/ROOperation.reader';
import CheckInSettingsReader from 'tbusiness/readers/service/CheckInSettings';
import { getJobId } from 'readers/Jobs.reader';
import { getParamsForOperationAdaption } from 'helpers/opcode.helper';
import { getOpcodeMapping } from 'utils';
import { isFieldEditable } from 'organisms/ROForm/ROForm.helper';
import ROConstraints from 'helpers/constraints';

import RepairOrderEnv from 'utils/repairOrderEnv';
import { PART_TYPES } from 'utils/constants';

import { hasPermissionToDeleteJobParts } from 'permissions/roPermission';

import {
  getPartAvailabilityHeading,
  getPartSearchParams,
} from 'organisms/ROForm/Components/OpCode/Components/PartsAvailability/PartsAvailability.helper';
import { getSanitizedOEMOperation } from 'twidgets/appServices/service/pages/LaborTimeGuide/Components/ROLaborTimeGuide/roLaborTimeGuide.helpers';
import PartsAvailability from 'organisms/ROForm/Components/OpCode/Components/PartsAvailability';
import { getFilteredPartTableColumns } from 'organisms/ROForm/Components/OpCode/Components/PartsAvailability/Components/PartsAvailabilityTable/PartsAvailabilityTable.helpers';
import DEALER_PROPERTIES from 'tbase/constants/dealerProperties';
import { JOB_TYPES } from 'tbase/constants/repairOrder/job';
import { VIEW_TYPE } from 'organisms/ROJobForm/ROJobForm.constants';
import { getPartParams } from '@tekion/tekion-business/src/appServices/service/utils/partAvailability';
import ROPartAvailability from '@tekion/tekion-business/src/appServices/service/readers/ROPartAvailability';
import { PARTS_AVAILABILITY_COLUMN } from 'twidgets/appServices/service/organisms/PartsAvailabilityTable/partsAvailabilityTable.constants';
import {
  getEditableViewOpCode,
  getEditableViewOemOpCode,
} from '../OpCode/Components/withOpCodeOverlayWrapper/withOpcodeOverlayWrapper.helpers';

import {
  MODIFY_OPERATION_MODE,
  FIELD_IDS,
  FIELD_NAMES,
  SHOULD_PRESERVE_PREVIOUS_OPCODE_VALUES,
  SUB_COMPONENT_TYPES,
  ERROR_MESSAGE_FOR_MODIFY_OPERATIONS,
} from './modifyOperations.constants';

export const getOemOpcodeFromText = oemOpcode => ({
  selectedLaborTimes: [],
  operation: {
    opCode: oemOpcode,
    opCodeDescription: oemOpcode,
  },
  isCustomOperation: true,
});

export const getAdditional = ({
  siteId,
  laborRates,
  isFormDisabled,
  isLaborComponentDisabled,
  mode,
  vehicleInfo,
  departmentId,
  moduleType,
  serviceMode,
  linkedOperations,
  isLaborHoursDisabled,
  isLaborPriceBillRateLaborRateDisabled,
  locale,
  jobType,
  shouldPreservePreviousOpcodeValues,
  isDealershipOpcodeDisabled,
  customerTags,
  skills,
  isJobWithDynamicSplit,
  isBillHoursDisabled,
}) => ({
  mode,
  laborRates,
  siteId,
  isFieldDisabled: (editableFields, fieldToCheck) =>
    isFormDisabled || isLaborComponentDisabled || !isFieldEditable(editableFields, fieldToCheck),
  isDraggable: mode !== MODIFY_OPERATION_MODE.SINGLE_EDIT,
  vehicleInfo,
  departmentId,
  moduleType,
  serviceMode,
  linkedOperations,
  isLaborHoursDisabled,
  isLaborPriceBillRateLaborRateDisabled,
  language: locale,
  jobType,
  shouldPreservePreviousOpcodeValues,
  isDealershipOpcodeDisabled,
  customerTags,
  skills,
  showSkillWithOpcode: ROConstraints.isDFMIntegrationEnabled(),
  isJobWithDynamicSplit,
  isBillHoursDisabled,
});

export const isFieldDisabledFn = _property('tdProps.rest.additional.isFieldDisabled') || false;

export const getLaborRatesFromProps = props => _get(props, 'tdProps.rest.additional.laborRates', EMPTY_ARRAY);

export const getSiteIdFromProps = props => _get(props, 'tdProps.rest.additional.siteId');

export const getVehicleInfoFromProps = props => _get(props, 'tdProps.rest.additional.vehicleInfo');

export const getDepartmentIdFromProps = props => _get(props, 'tdProps.rest.additional.departmentId');

export const getModuleTypeFromProps = props => _get(props, 'tdProps.rest.additional.moduleType');

export const getServiceModeFromProps = props => _get(props, 'tdProps.rest.additional.serviceMode');

export const getJobTypeFromProps = props => _get(props, 'tdProps.rest.additional.jobType');

export const isLaborPriceBillRateLaborRateDisabled = props =>
  _get(props, 'tdProps.rest.additional.isLaborPriceBillRateLaborRateDisabled', false);

export const getRowIndex = params => _head(_get(params, 'nestingPath'));

export const getEmptyLastRow = () => ({ isLastRow: true });

export const setGetValueFromOnChange = (_nestingPath, params) => _get(params, 'payload.value');

export const getSanitizedOperation = operation => _omit(operation, 'isLastRow');

const getOperationIndex = props => {
  const operationNumber = OperationReader.operationNumber(props) || 0;
  return _toNumber(operationNumber) - 1;
};

const shouldIncludeColumnInSingleMode = ({ id: columnId }) => {
  if (columnId === FIELD_IDS.OPERATION_REORDER) return false;
  if (columnId === FIELD_IDS.OEM_OPCODE) return !ROConstraints.isMultipleOemOpcodeEnabled();
  return true;
};

export const getColumnsForMode = (columns, mode) =>
  mode === MODIFY_OPERATION_MODE.SINGLE_EDIT
    ? _filter(columns, shouldIncludeColumnInSingleMode)
    : _filter(columns, ({ id }) => (id === FIELD_IDS.OEM_OPCODE ? !ROConstraints.isMultipleOemOpcodeEnabled() : true));

export const getAdaptedDealershipOpcode = (
  previousOpcode,
  selectedOpCode,
  {
    payType,
    customerType,
    make,
    laborRates,
    siteId,
    customerId,
    vehicleInfo,
    entityType,
    operationInfo,
    checkinMedium,
    applicableVehicleGroupIds,
    shouldPreservePreviousOpcodeValues,
    roSubType,
    entityCreationDate,
    customerTags,
    departmentId,
    payTypeConfigurations,
    subPayType,
    primaryPayer,
  },
  additional
) =>
  getAdaptedOperation(
    {
      operation: _pick(selectedOpCode, [OPCODE_FIELD_ID]),
      params: getParamsForOperationAdaption(EMPTY_OBJECT, payType, laborRates, {
        siteId,
        customerType,
        make,
        customerId,
        vehicleInfo,
        entityType,
        operationInfo,
        checkinMedium,
        applicableVehicleGroupIds,
        roSubType,
        entityCreationDate,
        customerTags,
        departmentId,
        payTypeConfigurations,
        subPayType,
        payer: primaryPayer,
      }),
      shouldResolveLinkedOpcodes: true,
    },
    additional
  )
    .then(operationData => {
      const { opCode, linkedOpcodes, operationInfo: operationInfoResponse } = operationData;
      return {
        opCode: _isEmpty(_get(previousOpcode, 'id'))
          ? opCode
          : getEditableViewOpCode(previousOpcode, opCode, shouldPreservePreviousOpcodeValues),
        linkedOpcodes,
        operationInfo: operationInfoResponse,
      };
    })
    .catch(() => {
      toaster(TOASTER_TYPE.ERROR, ERROR_MESSAGE_FOR_MODIFY_OPERATIONS);
    });

export const getAdaptedDealershipOpcodeForJobCompute = (
  previousOpcode,
  selectedOpCode,
  {
    payType,
    customerType,
    make,
    laborRates,
    siteId,
    customerId,
    vehicleInfo,
    entityType,
    operationInfo,
    checkinMedium,
    applicableVehicleGroupIds,
    shouldPreservePreviousOpcodeValues,
    roSubType,
    entityCreationDate,
    customerTags,
    departmentId,
    payTypeConfigurations,
    subPayType,
    primaryPayer,
    operationGroupId,
    contextType,
    job,
    moduleType,
    viewType,
    roId,
    primaryAdvisorId,
    payers,
  },
  additional
) =>
  getAdaptedJobForComputeJob(
    {
      operation: _pick(selectedOpCode, [OPCODE_FIELD_ID]),
      params: getParamsForOperationAdaption(EMPTY_OBJECT, payType, laborRates, {
        siteId,
        customerType,
        make,
        customerId,
        vehicleInfo,
        entityType,
        operationInfo,
        checkinMedium,
        applicableVehicleGroupIds,
        roSubType,
        entityCreationDate,
        customerTags,
        departmentId,
        payTypeConfigurations,
        subPayType,
        payer: primaryPayer,
        moduleType,
        viewType,
        roId,
        primaryAdvisorId,
        payers,
      }),
      shouldResolveLinkedOpcodes: true,
      contextType,
      operationGroupId,
      job,
      shouldPreservePreviousOpcodeValues,
      operationId: OperationReader.id(previousOpcode),
    },
    additional
  )
    .then(jobData => {
      const {
        operations,
        linkedOpcodes,
        operationInfo: operationInfoResponse,
        operationGroups,
        coupons,
        priceConfigs,
        existingOperationsOfJob,
        splitInfo,
        fees,
        primaryPayerCostCenters,
      } = jobData || EMPTY_OBJECT;
      return {
        opCode: operations,
        linkedOpcodes,
        operationInfo: operationInfoResponse,
        operationGroups,
        coupons,
        priceConfigs,
        existingOperationsOfJob,
        splitInfo,
        fees,
        primaryPayerCostCenters,
      };
    })
    .catch(() => {
      toaster(TOASTER_TYPE.ERROR, ERROR_MESSAGE_FOR_MODIFY_OPERATIONS);
    });

export const getOperationsWithEmptyRow = (operations, isLastRow, shouldAddNewRow) => {
  const newOperations = _map(operations, ({ forcedPartSelectionEnabled, parts, ...restOperation }) => ({
    ...restOperation,
    forcedPartSelectionEnabled,
    parts: _map(parts, part => ({
      ...part,
      selected: isPartSelectedByDefault(ROConstraints.isForcedPartFeatureEnabled(), parts) || part.selected,
    })),
  }));
  if (!shouldAddNewRow || !isLastRow) return newOperations;
  return [...newOperations, { isLastRow: true }];
};

const getOperationParams = (values, checkinSource, siteId) => {
  const opcodeMapping = getOpcodeMapping(RepairOrderEnv.settingsBySiteId[siteId]);
  const customOpcode = CheckInSettingsReader.getLtgOpcode(opcodeMapping);
  return {
    operations: _get(values, FIELD_NAMES.OPERATIONS),
    sanitizedSource: checkinSource || CHECKIN_MEDIUM_VS_CHECKIN_SETTINGS_SOURCE.WEB_CHECK_IN,
    customOpcode,
  };
};

export const getResolvedAndAdaptedOperation = async (
  {
    payType,
    customerType,
    make,
    laborRates,
    values,
    siteId,
    customerId,
    vehicleInfo,
    entityType,
    checkinSource,
    applicableVehicleGroupIds,
    roSubType,
    entityCreationDate,
    customerTags,
    departmentId,
    subPayType,
    payTypeConfigurations,
    primaryPayer,
  },
  additional
) => {
  const { operations, sanitizedSource, customOpcode } = getOperationParams(values, checkinSource, siteId);
  return getAdaptedDealershipOpcode(
    null,
    { opcode: customOpcode },
    {
      operations,
      payType,
      customerType,
      make,
      laborRates,
      siteId,
      customerId,
      vehicleInfo,
      entityType,
      checkinMedium: CHECKIN_SOURCE_VS_CHECKIN_MEDIUM[sanitizedSource],
      applicableVehicleGroupIds,
      roSubType,
      entityCreationDate,
      customerTags,
      departmentId,
      subPayType,
      payTypeConfigurations,
      primaryPayer,
    },
    additional
  );
};

export const getResolvedAndAdaptedOperationForComputeJob = async (
  {
    payType,
    customerType,
    make,
    laborRates,
    values,
    siteId,
    customerId,
    vehicleInfo,
    entityType,
    checkinSource,
    applicableVehicleGroupIds,
    roSubType,
    entityCreationDate,
    customerTags,
    departmentId,
    subPayType,
    payTypeConfigurations,
    primaryPayer,
    operationGroupId,
    contextType,
    job,
    moduleType,
    viewType,
    roId,
    primaryAdvisorId,
  },
  additional
) => {
  const { operations, sanitizedSource, customOpcode } = getOperationParams(values, checkinSource, siteId);
  return getAdaptedDealershipOpcodeForJobCompute(
    null,
    { opcode: customOpcode },
    {
      operations,
      payType,
      customerType,
      make,
      laborRates,
      siteId,
      customerId,
      vehicleInfo,
      entityType,
      checkinMedium: CHECKIN_SOURCE_VS_CHECKIN_MEDIUM[sanitizedSource],
      applicableVehicleGroupIds,
      roSubType,
      entityCreationDate,
      customerTags,
      departmentId,
      subPayType,
      payTypeConfigurations,
      primaryPayer,
      operationGroupId,
      contextType,
      job,
      moduleType,
      viewType,
      roId,
      primaryAdvisorId,
    },
    additional
  );
};

export const getPartFulfillmentComponent = partAvailabilityParams => {
  const applicableParams = _pick(partAvailabilityParams, [
    'disabled',
    'canAddParts',
    'canDeleteParts',
    'columnConfig',
    'showRequestedTotalPrice',
    'shouldRedirectToPartsSales',
    'roId',
    'roNumber',
    'isSORTableVisible',
    'laborRateId',
    'laborRateType',
    'showPartsSearch',
    'partsPricingEnabled',
    'customerId',
    'columnsToOmit',
    'isDealerTirePartEditable',
    'customerPartPricingEnabled',
    'partsSalePricingType',
    'maxPartsSaleAmount',
  ]);
  return {
    ...applicableParams,
    component: PartsAvailability,
    originalPartLabel: __('Original Part Request'),
    type: PART_TYPES.FULFILLMENT,
    headingLabel: getPartAvailabilityHeading(__('Billed Parts')),
  };
};

const PART_AVAILABILITY_COMPONENT_COLUMNS_TO_OMIT = {
  [VIEW_TYPE.ADD_VIEW]: [
    'payType',
    'estimatedArrival',
    'note',
    'onHandQty',
    'kebabAction',
    PARTS_AVAILABILITY_COLUMN.DISCOUNT,
    PARTS_AVAILABILITY_COLUMN.FULFILLMENT_ID,
    PARTS_AVAILABILITY_COLUMN.COUNTER_PERSON,
    ...getPartColumnsToOmit(),
  ],
  [VIEW_TYPE.EDIT_VIEW]: [
    'payType',
    'kebabAction',
    PARTS_AVAILABILITY_COLUMN.DISCOUNT,
    PARTS_AVAILABILITY_COLUMN.FULFILLMENT_ID,
    PARTS_AVAILABILITY_COLUMN.COUNTER_PERSON,
    ...getPartColumnsToOmit(),
  ],
  [VIEW_TYPE.VALIDATION_VIEW]: [
    'payType',
    'partCheckbox',
    PARTS_AVAILABILITY_COLUMN.DISCOUNT,
    PARTS_AVAILABILITY_COLUMN.FULFILLMENT_ID,
    PARTS_AVAILABILITY_COLUMN.COUNTER_PERSON,
    ...getPartColumnsToOmit(),
  ],
};

export const RO_JOB_PART_AVAILABILITY_COMPONENT = {
  [VIEW_TYPE.ADD_VIEW]: (jobStatus, type, roId, roNumber, additionalParams = EMPTY_OBJECT) =>
    getPartFulfillmentComponent({
      canAddParts: true,
      canDeleteParts: hasPermissionToDeleteJobParts(),
      disabled: false,
      showRequestedTotalPrice: true,
      showOriginalRequest: true,
      columnsToOmit: PART_AVAILABILITY_COMPONENT_COLUMNS_TO_OMIT[VIEW_TYPE.ADD_VIEW],
      columnConfig: getFilteredPartTableColumns(PART_AVAILABILITY_COMPONENT_COLUMNS_TO_OMIT[VIEW_TYPE.ADD_VIEW]),
      ...getPartSearchParams(additionalParams),
    }),
  [VIEW_TYPE.EDIT_VIEW]: (
    jobStatus,
    type,
    roId,
    roNumber,
    additionalParams = EMPTY_OBJECT,
    columnsToOmit = PART_AVAILABILITY_COMPONENT_COLUMNS_TO_OMIT[VIEW_TYPE.EDIT_VIEW]
  ) =>
    getPartFulfillmentComponent({
      canAddParts:
        ROConstraints.canAddParts(jobStatus) &&
        !(type === JOB_TYPES.SERVICE_MENU && !RepairOrderEnv.dealerProperty[DEALER_PROPERTIES.SERVICE_MENU_EDITABLE]),
      canDeleteParts: hasPermissionToDeleteJobParts(),
      disabled: ROConstraints.isPartsTableDisabled(jobStatus),
      showOriginalRequest: true,
      columnsToOmit,
      columnConfig: getFilteredPartTableColumns(columnsToOmit),
      shouldRedirectToPartsSales: true,
      roId,
      roNumber,
      isSORTableVisible: true,
      ...getPartSearchParams(additionalParams),
    }),
  [VIEW_TYPE.VALIDATION_VIEW]: (jobStatus, type, roId, roNumber, additionalParams = EMPTY_OBJECT) =>
    getPartFulfillmentComponent({
      canAddParts:
        ROConstraints.canAddParts(jobStatus) &&
        !(type === JOB_TYPES.SERVICE_MENU && !RepairOrderEnv.dealerProperty[DEALER_PROPERTIES.SERVICE_MENU_EDITABLE]),
      canDeleteParts: hasPermissionToDeleteJobParts(),
      disabled: ROConstraints.isPartsTableDisabled(jobStatus),
      showOriginalRequest: true,
      columnsToOmit: PART_AVAILABILITY_COMPONENT_COLUMNS_TO_OMIT[VIEW_TYPE.VALIDATION_VIEW],
      columnConfig: getFilteredPartTableColumns(PART_AVAILABILITY_COMPONENT_COLUMNS_TO_OMIT[VIEW_TYPE.VALIDATION_VIEW]),
      shouldRedirectToPartsSales: true,
      roId,
      roNumber,
      isSORTableVisible: true,
      ...getPartSearchParams(additionalParams),
    }),
};

export const getOperationInfoPartsWithOperation = (operations, operationInfoPartsById) =>
  _map(operations, operation => {
    const { id, parts, forcedPartSelectionEnabled, payType, subPayType, ...restOperation } = operation;
    const forcedParts = _differenceBy(
      _map(operationInfoPartsById[id] || EMPTY_ARRAY, part =>
        getPartParams(part, payType, id, {
          subPayType,
        })
      ),
      parts,
      ROPartAvailability.requestedPartName
    );
    return {
      id,
      forcedPartSelectionEnabled,
      parts,
      forcedParts,
      payType,
      subPayType,
      ...restOperation,
    };
  });

export const getPartWithSelectedState = (part, selected) => ({ ...part, selected });

export const getPartsSelectedOperations = (operations, operationId, selectedParts) =>
  _map(operations, operation => {
    const { id, parts, forcedParts = EMPTY_ARRAY, payType, isLastRow, ...restOperation } = operation;
    if (isLastRow) return operation;
    const selectedPartsName = _map(selectedParts, ROPartAvailability.id);
    const partsClubbed = _uniqBy([...parts, ...forcedParts], ROPartAvailability.id);
    const newParts =
      id === operationId
        ? _map(partsClubbed, part => {
            const selected = _includes(selectedPartsName, ROPartAvailability.id(part));
            return getPartWithSelectedState(part, selected);
          })
        : partsClubbed;
    return {
      id,
      parts: newParts,
      forcedParts,
      payType,
      ...restOperation,
    };
  });

export const hasSomeForcedPartsSelectedForOperations = (operations, selectedPartsMap) =>
  _every(operations, operation => {
    const { id, forcedPartSelectionEnabled } = operation;
    if (!forcedPartSelectionEnabled) return true;
    return !_isEmpty(_get(selectedPartsMap, [id, 'parts'], EMPTY_ARRAY));
  });

const filterSelectedPartsOnlyFromOperations = ({ parts, ...restOperation }) => ({
  ...restOperation,
  parts: _filter(parts, part => part.selected),
});

export const getSelectedPartsForOperations = ({ operations }) =>
  _reduce(
    operations,
    (acc, operation) => ({ ...acc, [OperationReader.id(operation)]: filterSelectedPartsOnlyFromOperations(operation) }),
    EMPTY_OBJECT
  );

export const hasOEMOpcodeChanged = adaptedOEMOperation => {
  const oemOpcode = _get(adaptedOEMOperation, FIELD_IDS.OEM_OPCODE);
  return !_isEmpty(oemOpcode);
};

export const isColumnDisabled = props =>
  _get(props, ['tdProps', 'rest', 'additional', SHOULD_PRESERVE_PREVIOUS_OPCODE_VALUES], false);

export const SUB_COMPONENT_TYPE_VS_PROPS = {
  [SUB_COMPONENT_TYPES.OEM_OPCODE]: ({ props, data }) => {
    const { original } = data;
    const { original: entityData } = props;
    return {
      ...props,
      value: OperationReader.warrantyOemOpcodesWithLaborDetails(original),
      shouldAddNewRow: true,
      jobId: getJobId(entityData),
      opcodeId: OperationReader.id(original),
      opcode: OperationReader.opcode(original),
    };
  },
  DEFAULT: _noop,
};

export const isEditOpCodeDisabledForRow = props => _get(props, ['original', 'isEditOpCodeDisabled'], false);

export const isJobWithDynamicSplitFromProps = props =>
  _get(props, ['tdProps', 'rest', 'additional', 'isJobWithDynamicSplit'], false);

export const getWarrantyOemFieldsToUpdate = ({
  columnId,
  warrantyOemOpcode,
  laborPriceProperties,
  billingTimeInSeconds,
}) => {
  if (columnId === FIELD_IDS.OEM_OPCODE && _isEmpty(warrantyOemOpcode))
    return _omit(laborPriceProperties, WARRANTY_OEM_OPCODE_WITH_LABOR_DETAILS);
  if (_isEmpty(warrantyOemOpcode)) return laborPriceProperties;
  return {
    ...laborPriceProperties,
    [WARRANTY_OEM_OPCODE_WITH_LABOR_DETAILS]: [
      {
        ..._get(laborPriceProperties, [WARRANTY_OEM_OPCODE_WITH_LABOR_DETAILS, 0], EMPTY_ARRAY),
        billingTimeInSeconds,
      },
    ],
  };
};

export const getOperationsWithEditRestrictions = ({ operations, isEditOpCodeDisabled = false, type }) =>
  _map(operations, (operation, index) => ({
    ...operation,
    isEditOpCodeDisabled,
    shouldDisableDrag: isEditOpCodeDisabled || isMpviOperation(type, index),
  }));

export const OPERATION_MODE_VS_OPERATION_INDEX = {
  [MODIFY_OPERATION_MODE.SINGLE_EDIT]: getOperationIndex,
  [MODIFY_OPERATION_MODE.BULK_EDIT]: (original, index) => index,
};
const getPartsWithSelected = parts => _map(parts, part => ({ ...part, selected: true }));

export const getOperationWithSelectedParts = operations =>
  ROConstraints.isForcedPartFeatureEnabled()
    ? _reduce(
        operations,
        (acc, operation) => {
          const { forcedPartSelectionEnabled, parts } = operation || EMPTY_OBJECT;
          if (!forcedPartSelectionEnabled) return [...acc, operation];
          const newParts = getPartsWithSelected(parts);
          return [...acc, { ...operation, parts: newParts }];
        },
        EMPTY_ARRAY
      )
    : operations;

export const getPartsColumnToOmit = (viewType, operationsById, id) => {
  if (viewType === VIEW_TYPE.ADD_VIEW) return EMPTY_ARRAY;
  return [
    'payType',
    PARTS_AVAILABILITY_COLUMN.FULFILLMENT_ID,
    PARTS_AVAILABILITY_COLUMN.COUNTER_PERSON,
    ...[!_isEmpty(operationsById[id]) ? 'partCheckbox' : 'kebabAction'],
    ...getPartColumnsToOmit(),
  ];
};

export const getOperationsWithoutLastRow = operations => {
  const lastRow = _last(operations);
  const isLastRow = _get(lastRow, 'isLastRow', false);
  return isLastRow ? _dropRight(operations) : operations;
};

export const getOperationsPayLoad = ({
  operations,
  operationGroups: updatedOperationGroups,
  coupons: updatedCoupons,
  priceConfigs: updatedPriceConfigs,
  job,
  laborRates,
}) => {
  const allOperations = getOperationsWithoutLastRow(operations);
  const { operationGroups, coupons, priceConfigs } = job;
  const allOperationsPayLoad = _map(allOperations, operation => ({
    ...operation,
    ...getLaborRateFields(operation, laborRates),
  }));
  return {
    operations: allOperationsPayLoad,
    operationGroups: !_isEmpty(updatedOperationGroups) ? updatedOperationGroups : operationGroups,
    coupons: !_isEmpty(updatedCoupons) ? updatedCoupons : coupons,
    priceConfigs: !_isEmpty(updatedPriceConfigs) ? updatedPriceConfigs : priceConfigs,
  };
};

export const getFieldId = params => _get(params, 'columnId');
export const getFieldValue = params => _get(params, 'value');
export const fieldOnChangeValueGetter = {
  [FIELD_IDS.DESCRIPTION]: payload => _get(payload, 'params.params[0].target.value'),
  [FIELD_IDS.SKILL]: payload => _get(payload, 'params.params[0].value[0]'),
  DEFAULT: payload => _get(payload, 'params.value.payload.value'),
};

export const getUpdatedOperationsOfJobFromJobCompute = ({
  existingOperationsOfJob,
  index,
  isLastRow,
  updatedOpcode,
  operations,
}) =>
  ROConstraints.isServiceV3Enabled()
    ? {
        updatedOperations: [
          ...existingOperationsOfJob.slice(0, index),
          updatedOpcode,
          ...existingOperationsOfJob.slice(index),
        ],
        sanitizedIsLastRow: true,
      }
    : {
        updatedOperations: [...operations.slice(0, index), updatedOpcode, ...operations.slice(index + 1)],
        sanitizedIsLastRow: isLastRow,
      };

export const getSanitizedExistingOperations = (existingOperations, operations) => {
  const existingOperationsById = _keyBy(existingOperations, ID_FIELD_NAME);
  return _reduce(
    operations,
    (acc, { id } = EMPTY_OBJECT) => {
      const existingOperation = _get(existingOperationsById, id);
      if (!_isEmpty(existingOperation)) {
        acc.push(existingOperation);
        return acc;
      }
      return acc;
    },
    []
  );
};

export const getUpdateOperationsForMultiPayer = ({
  isSingleEditMode,
  updatedOperationsFromJobCompute,
  operations,
  index,
  opcode,
}) => {
  const lastRow = isSingleEditMode ? EMPTY_ARRAY : [{ isLastRow: true }];
  const sanitizedUpdatedOperations = getSanitizedExistingOperations(updatedOperationsFromJobCompute, operations);
  const updatedOperations = !_isEmpty(sanitizedUpdatedOperations)
    ? [...sanitizedUpdatedOperations, ...lastRow]
    : [...operations.slice(0, index), opcode, ...operations.slice(index + 1)];
  return updatedOperations;
};

export const getAdditionalJobFieldsToSet = ({
  shouldSetFields,
  coupons,
  priceConfigs,
  fees,
  splitInfo,
  primaryPayerCostCentersToUpdate,
  primaryPayerCostCenters,
  operationGroups,
}) =>
  shouldSetFields
    ? {
        coupons,
        priceConfigs,
        fees,
        splitInfo,
        primaryPayerCostCenters: primaryPayerCostCentersToUpdate || primaryPayerCostCenters,
        operationGroups,
      }
    : EMPTY_OBJECT;

export const getOperationsToUpdate = ({
  operation,
  selectedOpCode,
  existingOperationsOfJob,
  index,
  isLastRow,
  operations,
}) => {
  const adaptedOemOperation = getEditableViewOemOpCode(operation, getSanitizedOEMOperation(selectedOpCode));
  const sanitizedExistingOperations = getSanitizedExistingOperations(existingOperationsOfJob, operations);
  const { updatedOperations, sanitizedIsLastRow } = getUpdatedOperationsOfJobFromJobCompute({
    existingOperationsOfJob: sanitizedExistingOperations,
    index,
    isLastRow,
    updatedOpcode: adaptedOemOperation,
    operations,
  });
  return { adaptedOemOperation, updatedOperations, sanitizedIsLastRow };
};

export const getExistingOperations = (existingOperationsOfJob, operations, operation) =>
  !_isEmpty(existingOperationsOfJob)
    ? existingOperationsOfJob
    : _differenceBy(getOperationsWithoutLastRow(operations), [operation], OPERATION_ID_FIELD_NAME);

export const getExistingOperationsOfJobWithOutOemOpcode = (adaptedOemOperationId, existingOperationsOfJob) =>
  _filter(existingOperationsOfJob, operation => OperationReader.id(operation) !== adaptedOemOperationId);

export const getUpdatedOperationsWithOemOpcode = ({
  selectedOpCode,
  updatedOperationsWithMpviConfig,
  selectedOpcodeIndex,
}) => {
  if (_isEmpty(selectedOpCode) || _isEmpty(updatedOperationsWithMpviConfig)) return updatedOperationsWithMpviConfig;
  const updatedOpcode = _get(updatedOperationsWithMpviConfig, selectedOpcodeIndex);
  const adaptedOemOperation = getEditableViewOemOpCode(updatedOpcode, getSanitizedOEMOperation(selectedOpCode));
  return [
    ...updatedOperationsWithMpviConfig.slice(0, selectedOpcodeIndex),
    adaptedOemOperation,
    ...updatedOperationsWithMpviConfig.slice(selectedOpcodeIndex + 1),
  ];
};

export const getLinkedOperationsByParentOperationId = (operations, linkedOperationsByParentId) => {
  if (_isEmpty(operations)) return EMPTY_OBJECT;
  const operationsById = _keyBy(operations, OPERATION_ID_FIELD_NAME);
  const updatedLinkedOperations = _reduce(
    linkedOperationsByParentId,
    (acc, linkedOperations, parentOperationId) => {
      const updatedOperations = _map(linkedOperations, ({ id }) => operationsById[id]);
      acc[parentOperationId] = updatedOperations;
      return acc;
    },
    {}
  );
  return {
    linkedOperations: updatedLinkedOperations,
  };
};

export const getUpdatedOperationsWithoutLinkedOperations = (operations, linkedOperations) => {
  const linkedOperationsById = _keyBy(_flatMap(linkedOperations), OPERATION_ID_FIELD_NAME);
  return _filter(operations, ({ id }) => !_has(linkedOperationsById, id));
};

export const getUpdatedOperationsOfJob = ({
  prevOperationsToUpdate,
  prevOperationsOfJob,
  modifiedOperationsToUpdateInState,
}) => {
  const sanitizedAllOperations = _isEmpty(prevOperationsToUpdate) ? prevOperationsOfJob : prevOperationsToUpdate;
  const updatedOperationsById = _keyBy(modifiedOperationsToUpdateInState, ID_FIELD_NAME);
  return _reduce(
    sanitizedAllOperations,
    (acc, operation = EMPTY_OBJECT) => {
      const id = OperationReader.id(operation);
      const updatedOperation = _get(updatedOperationsById, id);
      if (!_isEmpty(updatedOperation)) return [...acc, updatedOperation];
      return [...acc, operation];
    },
    []
  );
};
