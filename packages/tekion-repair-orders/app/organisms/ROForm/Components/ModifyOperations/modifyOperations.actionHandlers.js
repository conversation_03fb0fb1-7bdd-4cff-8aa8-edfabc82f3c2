import _get from 'lodash/get';
import _isFunction from 'lodash/isFunction';
import _head from 'lodash/head';
import _isEmpty from 'lodash/isEmpty';
import _pick from 'lodash/pick';
import _filter from 'lodash/filter';
import _omit from 'lodash/omit';
import _includes from 'lodash/includes';
import _map from 'lodash/map';
import _keyBy from 'lodash/keyBy';
import _reduce from 'lodash/reduce';
import _findIndex from 'lodash/findIndex';

import { EMPTY_OBJECT, EMPTY_ARRAY } from 'tbase/app.constants';
import {
  CHECKIN_SOURCE_VS_CHECKIN_MEDIUM,
  CHECKIN_MEDIUM_VS_CHECKIN_SETTINGS_SOURCE,
} from 'tbase/constants/service/configurator';
import { JOB_COMPUTE_CONTEXT_TYPES } from 'tbase/constants/repairOrder/constants';
import { tget, uuid } from 'tbase/utils/general';
import { getErrorMessage } from 'tbase/utils/errorUtils';
import TABLE_ACTION_TYPES from 'tcomponents/molecules/tableInputField/constants/TableInputField.actionTypes';
import { REMOVE_ACTION } from 'tcomponents/molecules/tableInputField/constants/general';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import FORM_PAGE_ACTION_TYPES from 'tcomponents/pages/formPage/constants/actionTypes';
import FORM_ACTION_TYPES from 'tcomponents/organisms/FormBuilder/constants/actionTypes';
import { getSanitizedOEMOperation } from 'twidgets/appServices/service/pages/LaborTimeGuide/Components/ROLaborTimeGuide/roLaborTimeGuide.helpers';
import MoneyReader from 'twidgets/appServices/service/readers/Money.reader';
import {
  getCoupons,
  getFees,
  getOperations,
  PRIMARY_PAYER_COST_CENTERS,
  operationGroups,
} from 'tbusiness/appServices/service/readers/Job';
import { ASSET_DETAILS_FIELDS_TO_PICK } from 'tbusiness/appServices/service/constants/general';
import { RETURN_PART_CONSTRAINT_MESSAGE } from 'twidgets/appServices/service/organisms/ReturnPart/ReturnPart.constants';

import ROReader from 'readers/RepairOrder.reader';
import OperationReader, {
  STORY_LINES_FIELD_NAME,
  OPERATION_ID_FIELD_NAME,
  LABOR_AMOUNT_FIELD_ID,
  WARRANTY_OEM_OPCODE_WITH_LABOR_DETAILS,
  LABOR_RATE_ID_FIELD_NAME,
} from 'readers/ROOperation.reader';

import { getSortedOperationsByOperationNumber } from 'tbusiness/appServices/service/utils/operation';
import ROConstraints from 'helpers/constraints';
import { getOperationsWithMpviConfig, getIsOperationWithReturnParts } from 'helpers/opcode.helper';
import { PREV_OPCODE_FIELDS_TO_OMIT } from 'constants/roOperation.constants';
import { LABOR_FIELDS } from 'constants/opCode.constants';
import { ACTION_TYPES, RO_PRICE_CALCULATION_TEXT } from 'organisms/ROForm/ROForm.constants';
import { VIEW_TYPE } from 'organisms/ROJobForm/ROJobForm.constants';

import { getOperationInfoPartsByOperationId } from 'tbusiness/appServices/service/helpers/general';
import { MODULE_TYPE_VS_ASSET_TYPE } from '@tekion/tekion-business/src/appServices/service/constants/general';
import {
  getCorrectionFieldsValue,
  FIELD_ID_VS_PARAMS_TO_UPDATE,
  getCorrectionFieldsValueForMultiPayer,
} from '../OpCode/OpCodeFormField.actionHandlers';
import {
  getEditableViewOpCode,
  getEditableViewOemOpCode,
} from '../OpCode/Components/withOpCodeOverlayWrapper/withOpcodeOverlayWrapper.helpers';
import {
  OPCODE_FIELD_CHANGE_ACTION,
  LABOR_PRICE_CALCULATION_INITIATED_ACTIONS,
  LABOR_PRICE_CALCULATION_COMPLETED_ACTIONS,
} from '../OpCode/OpCode.constants';
import { FIELD_NAMES, FIELD_IDS, LABOR_RATE_VERSION_ID } from './modifyOperations.constants';
import {
  INIT_FORM,
  BROWSE_CATALOG_ACTION,
  OEM_OPCODE_SELECT,
  LTG_OVERLAY_CLOSE,
  OPERATION_VALUE_CHANGE,
} from './modifyOperations.actions';
import {
  getOemOpcodeFromText,
  getRowIndex,
  getSanitizedOperation,
  getAdaptedDealershipOpcode,
  getOperationsWithEmptyRow,
  getResolvedAndAdaptedOperation,
  getOperationInfoPartsWithOperation,
  getPartsSelectedOperations,
  hasOEMOpcodeChanged,
  getOperationsWithEditRestrictions,
  getWarrantyOemFieldsToUpdate,
  getOperationWithSelectedParts,
  getAdaptedDealershipOpcodeForJobCompute,
  getResolvedAndAdaptedOperationForComputeJob,
  getOperationsPayLoad,
  getFieldId,
  getFieldValue,
  fieldOnChangeValueGetter,
  getUpdatedOperationsOfJobFromJobCompute,
  getSanitizedExistingOperations,
  getUpdateOperationsForMultiPayer,
  getAdditionalJobFieldsToSet,
  getOperationsToUpdate,
  getExistingOperations,
  getUpdatedOperationsWithOemOpcode,
  getLinkedOperationsByParentOperationId,
  getUpdatedOperationsWithoutLinkedOperations,
  getUpdatedOperationsOfJob,
  getExistingOperationsOfJobWithOutOemOpcode,
} from './modifyOperations.helpers';

const onFormInit = payload => {
  const { getState, setState } = payload;
  const {
    operations,
    shouldAddNewRow,
    type,
    fetchResolvedOpcodes = true,
    isEditOpCodeDisabled,
    viewType,
    departmentId,
    customerTags,
    operationGroupId,
    ...restParams
  } = getState();
  const { roDetails, job } = restParams;
  const { primaryPayerCostCenters } = job || EMPTY_OBJECT;
  const operationsWithSelectedParts =
    viewType === VIEW_TYPE.ADD_VIEW ? operations : getOperationWithSelectedParts(operations);
  const adaptedFunc = ROConstraints.isServiceV3Enabled()
    ? getResolvedAndAdaptedOperationForComputeJob
    : getResolvedAndAdaptedOperation;
  setState(
    {
      values: {
        [FIELD_NAMES.OPERATIONS]: getOperationsWithEmptyRow(
          getOperationsWithEditRestrictions({
            operations: operationsWithSelectedParts,
            isEditOpCodeDisabled,
            type,
          }),
          true,
          shouldAddNewRow
        ),
      },
      // Not resolving the promise here to show the modal immediately
      defaultConcernPromise: fetchResolvedOpcodes
        ? adaptedFunc(
            {
              ...restParams,
              departmentId,
              customerTags,
              viewType,
              entityType: MODULE_TYPE_VS_ASSET_TYPE[restParams.moduleType],
              operationGroupId,
              contextType: JOB_COMPUTE_CONTEXT_TYPES.NEW_OPERATION_CONTEXT_TYPE,
            },
            { language: ROReader.getLocale(roDetails) }
          )
        : Promise.resolve(EMPTY_OBJECT),
      replacedOpcodeOperationIds: EMPTY_ARRAY,
      linkedOperations: EMPTY_OBJECT,
      showConfirmationModalOnCancel: false,
      departmentId,
      customerTags,
      job,
      primaryPayerCostCenters,
    },
    () => {
      handleOperationsChange({ setState, getState, params: { payload: { operations } } });
    }
  );
};

const reArrangeOperations = payload => {
  const { setState, params, getState } = payload;
  const { job, operationsToUpdate: prevOperationsToUpdate } = getState();
  const operations = _get(params, 'value', []);
  const operationsToUpdate = getUpdatedOperationsOfJob({
    prevOperationsToUpdate,
    prevOperationsOfJob: getOperations(job),
    modifiedOperationsToUpdateInState: operations,
  });
  setState({
    values: {
      [FIELD_NAMES.OPERATIONS]: operations,
    },
    operationsToUpdate,
    showConfirmationModalOnCancel: true,
  });
};

const dealershipOpcodeChangeHandler = async ({ setState, getState, params } = EMPTY_OBJECT) => {
  setState({ isLoading: true, calculationsInProgress: 1 });
  const {
    payType,
    customerType,
    make,
    laborRates,
    values,
    shouldAddNewRow,
    siteId,
    replacedOpcodeOperationIds,
    operations: originalOperations,
    customerId,
    vehicleInfo,
    linkedOperations,
    moduleType,
    operationInfo: operationInfoState = [],
    checkinSource,
    applicableVehicleGroupIds,
    shouldPreservePreviousOpcodeValues,
    roSubType,
    entityCreationDate,
    departmentId,
    customerTags,
    payTypeConfigurations,
    subPayType,
    primaryPayer,
    operationGroupId,
    operationGroups = EMPTY_ARRAY,
    job,
    coupons,
    priceConfigs,
    viewType,
    type,
    roId,
    primaryAdvisorId,
    payers,
    primaryPayerCostCenters,
  } = getState();
  const operations = _get(values, FIELD_NAMES.OPERATIONS, EMPTY_ARRAY);
  const index = getRowIndex(params);
  const originalOperationsById = _keyBy(originalOperations, OPERATION_ID_FIELD_NAME);

  const opcodeValue = params?.value;
  const isLastRow = operations[index]?.isLastRow;
  const previousOpcode = getSanitizedOperation(operations[index]);
  const sanitizedSource = checkinSource || CHECKIN_MEDIUM_VS_CHECKIN_SETTINGS_SOURCE.WEB_CHECK_IN;
  const contextType = !_isEmpty(_get(previousOpcode, 'id'))
    ? JOB_COMPUTE_CONTEXT_TYPES.REPLACE_OPCODE_CONTEXT_TYPE
    : JOB_COMPUTE_CONTEXT_TYPES.NEW_OPERATION_CONTEXT_TYPE;
  const operationsPayload = ROConstraints.isServiceV3Enabled()
    ? getOperationsPayLoad({ operations: getOperations(job), operationGroups, coupons, priceConfigs, job, laborRates })
    : EMPTY_OBJECT;
  const updatedJob = { ...job, ...operationsPayload };
  const adaptedFunc = ROConstraints.isServiceV3Enabled()
    ? getAdaptedDealershipOpcodeForJobCompute
    : getAdaptedDealershipOpcode;
  return adaptedFunc(previousOpcode, opcodeValue, {
    operations,
    payType,
    customerType,
    make,
    laborRates,
    siteId,
    customerId,
    vehicleInfo,
    entityType: MODULE_TYPE_VS_ASSET_TYPE[moduleType],
    checkinMedium: CHECKIN_SOURCE_VS_CHECKIN_MEDIUM[sanitizedSource],
    applicableVehicleGroupIds,
    shouldPreservePreviousOpcodeValues,
    roSubType,
    entityCreationDate,
    departmentId,
    customerTags,
    payTypeConfigurations,
    subPayType,
    primaryPayer,
    contextType,
    operationGroupId,
    job: updatedJob,
    moduleType,
    viewType,
    roId,
    primaryAdvisorId,
    payers,
  })
    .then(
      ({
        opCode,
        linkedOpcodes,
        operationInfo = EMPTY_ARRAY,
        operationGroups: operationGroupsResponse,
        coupons: couponsResponse,
        priceConfigs: priceConfigsResponse,
        existingOperationsOfJob = EMPTY_ARRAY,
        splitInfo,
        fees,
        primaryPayerCostCenters: primaryPayerCostCentersToUpdate,
      }) => {
        if (_isEmpty(opCode)) {
          throw new Error(__('Error fetching opcode'));
        }
        if (OperationReader.operationPricingPresent(opCode) && getIsOperationWithReturnParts(previousOpcode)) {
          throw new Error(RETURN_PART_CONSTRAINT_MESSAGE);
        }
        // Marking an operation as updated only if it is already present in the job
        // and the opcode is changed.
        const operationInfoPartsById = !_isEmpty(operationInfo)
          ? getOperationInfoPartsByOperationId(operationInfo)
          : EMPTY_OBJECT;
        const markAsReplacedOpcodeOperation =
          !_isEmpty(originalOperationsById[OperationReader.id(opCode)]) &&
          !_includes(replacedOpcodeOperationIds, OperationReader.id(opCode));
        const updateExistingOperationsOfJobWithMpviConfig = getOperationsWithMpviConfig(
          existingOperationsOfJob,
          _get(operations, '0.mpviConfig'),
          type
        );
        const sanitizedExistingOperations = getSanitizedExistingOperations(
          updateExistingOperationsOfJobWithMpviConfig,
          operations
        );
        const updatedOperationsOfJob = getSortedOperationsByOperationNumber(
          [...updateExistingOperationsOfJobWithMpviConfig, ...linkedOpcodes, opCode],
          operationGroupsResponse
        );
        const { updatedOperations, sanitizedIsLastRow } = getUpdatedOperationsOfJobFromJobCompute({
          existingOperationsOfJob: sanitizedExistingOperations,
          index,
          isLastRow,
          updatedOpcode: opCode,
          operations,
        });
        const additionalJobFieldsToSet = getAdditionalJobFieldsToSet({
          shouldSetFields: true,
          coupons: couponsResponse,
          priceConfigs: priceConfigsResponse,
          fees,
          splitInfo,
          primaryPayerCostCentersToUpdate,
          primaryPayerCostCenters,
          operationGroups: operationGroupsResponse,
        });
        setState({
          values: {
            ...values,
            [FIELD_NAMES.OPERATIONS]: getOperationsWithEmptyRow(
              getOperationInfoPartsWithOperation(updatedOperations, operationInfoPartsById),
              sanitizedIsLastRow,
              shouldAddNewRow
            ),
          },
          linkedOperations: { ...linkedOperations, [OperationReader.id(opCode)]: linkedOpcodes },
          replacedOpcodeOperationIds: [
            ...replacedOpcodeOperationIds,
            ...(markAsReplacedOpcodeOperation ? [OperationReader.id(opCode)] : []),
          ],
          operationInfo: [...operationInfoState, ...(!_isEmpty(operationInfo) ? operationInfo : EMPTY_ARRAY)],
          showConfirmationModalOnCancel: true,
          ...additionalJobFieldsToSet,
          operationsToUpdate: getOperationInfoPartsWithOperation(
            [...updateExistingOperationsOfJobWithMpviConfig, opCode],
            operationInfoPartsById
          ),
          job: {
            ...job,
            operations: updatedOperationsOfJob,
            ...additionalJobFieldsToSet,
          },
          calculationsInProgress: 0,
        });
      }
    )
    .catch(error => {
      setState({
        values: {
          ...values,
          [FIELD_NAMES.OPERATIONS]: operations,
        },
        calculationsInProgress: 0,
      });
      toaster(TOASTER_TYPE.ERROR, getErrorMessage(error, __('Error fetching opcode')));
    });
};

const FIELDS_TO_PICK_FOR_ACTION = {
  [OPCODE_FIELD_CHANGE_ACTION.LABOR_PRICE_CHANGE_IN_PROGRESS]: [
    FIELD_IDS.BILL_HRS,
    FIELD_IDS.BILL_RATE,
    FIELD_IDS.LABOR_HRS,
    FIELD_IDS.LABOR_RATE,
    WARRANTY_OEM_OPCODE_WITH_LABOR_DETAILS,
    LABOR_RATE_ID_FIELD_NAME,
  ],
  [OPCODE_FIELD_CHANGE_ACTION.LABOR_PRICE_CHANGE_COMPLETED]: [
    FIELD_IDS.LABOR_PRICE,
    WARRANTY_OEM_OPCODE_WITH_LABOR_DETAILS,
  ],
  [OPCODE_FIELD_CHANGE_ACTION.LABOR_RATE_CHANGE_ACTION_COMPLETED]: [
    FIELD_IDS.BILL_HRS,
    FIELD_IDS.LABOR_HRS,
    FIELD_IDS.LABOR_PRICE,
    WARRANTY_OEM_OPCODE_WITH_LABOR_DETAILS,
    LABOR_RATE_VERSION_ID,
  ],
};

const handleCalculationInProgress = ({ payload, additional }) => {
  const { getState, params = EMPTY_OBJECT } = payload;
  const { values } = getState();
  let { calculationsInProgress = 0 } = getState();
  const { updateAction } = additional;
  const index = getRowIndex(params);
  const operations = _get(values, FIELD_NAMES.OPERATIONS);
  if (LABOR_PRICE_CALCULATION_INITIATED_ACTIONS.has(updateAction)) {
    calculationsInProgress += 1;
  } else if (LABOR_PRICE_CALCULATION_COMPLETED_ACTIONS.has(updateAction)) {
    calculationsInProgress -= 1;
  }
  return { calculationsInProgress, index, operations };
};

const triggerOnFieldChange =
  payload =>
  (value, additional = EMPTY_OBJECT) => {
    const { getState, params = EMPTY_OBJECT, setState } = payload;
    const { values } = getState();
    const { updateAction } = additional;
    const { columnId } = params;
    const { calculationsInProgress, index, operations } = handleCalculationInProgress({ payload, additional });
    const laborPriceProperties = _pick(value, FIELDS_TO_PICK_FOR_ACTION[updateAction]);
    const { warrantyOemOpcode } = value || EMPTY_OBJECT;
    const billingTimeInSeconds = _get(operations, [index, FIELD_IDS.BILL_HRS]);
    const fieldsToUpdate = getWarrantyOemFieldsToUpdate({
      columnId,
      warrantyOemOpcode,
      laborPriceProperties,
      billingTimeInSeconds,
    });

    setState({
      values: {
        ...values,
        [FIELD_NAMES.OPERATIONS]: [
          ...operations.slice(0, index),
          { ...operations[index], ...fieldsToUpdate },
          ...operations.slice(index + 1),
        ],
      },
      calculationsInProgress,
      showConfirmationModalOnCancel: true,
    });
  };

const triggerOnFieldChangeForMultiPayer =
  payload =>
  (value, additional = EMPTY_OBJECT) => {
    const { getState, setState, params } = payload;
    const { fees, isSingleEditMode, type, values, job, linkedOperations } = getState();
    const {
      fees: feesToUpdate,
      coupons: couponsToUpdate,
      updatedOperations: updatedOperationsFromJobCompute = EMPTY_ARRAY,
      updatedOperationGroups,
      priceConfigs,
      splitInfo,
    } = additional;
    const updatedOperationsWithMpviConfig = getOperationsWithMpviConfig(
      updatedOperationsFromJobCompute,
      _get(values, 'operations.0.mpviConfig'),
      type
    );
    const { calculationsInProgress, index, operations } = handleCalculationInProgress({ payload, additional });
    const { selectedOpCode } = params || EMPTY_OBJECT;
    const modifiedOperationId = OperationReader.id(operations[index]);
    const selectedOpcodeIndex = _findIndex(updatedOperationsWithMpviConfig, { id: modifiedOperationId });
    const updatedOperationsWithOemOpcode = getUpdatedOperationsWithOemOpcode({
      selectedOpCode,
      updatedOperationsWithMpviConfig,
      selectedOpcodeIndex,
    });
    const updatedOperations = getUpdateOperationsForMultiPayer({
      isSingleEditMode,
      updatedOperationsFromJobCompute: updatedOperationsWithOemOpcode,
      operations,
      index,
      opcode: value,
    });
    const updatedFees = feesToUpdate || fees;
    const additionalJobFieldsToSet = _omit(
      getAdditionalJobFieldsToSet({
        shouldSetFields: true,
        coupons: couponsToUpdate,
        priceConfigs,
        fees: updatedFees,
        splitInfo,
        operationGroups: updatedOperationGroups,
      }),
      [PRIMARY_PAYER_COST_CENTERS]
    );
    setState({
      values: {
        ...values,
        [FIELD_NAMES.OPERATIONS]: updatedOperations,
      },
      calculationsInProgress,
      showConfirmationModalOnCancel: true,
      operationsToUpdate: updatedOperationsWithOemOpcode,
      ...additionalJobFieldsToSet,
      ...getLinkedOperationsByParentOperationId(updatedOperationsWithOemOpcode, linkedOperations),
      job: {
        ...job,
        operations: updatedOperationsWithOemOpcode,
        ...additionalJobFieldsToSet,
      },
    });
  };

const getOperationProps = (nestingPath, { getState }) => {
  const { values, laborRates, payType, subPayType, job, fees, coupons, viewType, calculationsInProgress } = getState();
  const operationAtIndex = _get(values, `${[FIELD_NAMES.OPERATIONS]}.${_head(nestingPath)}`);

  return () => ({
    ..._pick(getState(), ASSET_DETAILS_FIELDS_TO_PICK),
    value: operationAtIndex,
    laborRates,
    payType,
    subPayType,
    job: {
      ...job,
      fees: fees || getFees(job),
      coupons: coupons || getCoupons(job),
    },
    viewType,
    isCalculationInProgress: calculationsInProgress > 0,
  });
};

const FIELD_VS_VALUE_MAP = fieldId => {
  switch (fieldId) {
    case FIELD_IDS.LABOR_HRS:
    case FIELD_IDS.BILL_HRS:
      return params => ({
        id: getFieldId(params),
        value: getFieldValue(params),
      });
    case FIELD_IDS.LABOR_PRICE:
      return params => {
        const value = _get(params, ['value', 'payload', 'value']) || getFieldValue(params);
        return {
          id: getFieldId(params),
          value,
        };
      };
    default:
      return params => ({
        id: getFieldId(params),
        value: getFieldValue(params),
      });
  }
};

const changeOpcodeFormField = async payload => {
  const { getState, params } = payload;
  const id = _get(params, 'columnId');
  const nestingPath = _get(params, 'nestingPath');
  const isServiceV3Enabled = ROConstraints.isServiceV3Enabled();
  const [funToExec, triggerOnFieldChangeFunToExec] = isServiceV3Enabled
    ? [getCorrectionFieldsValueForMultiPayer, triggerOnFieldChangeForMultiPayer]
    : [getCorrectionFieldsValue, triggerOnFieldChange];

  funToExec(FIELD_VS_VALUE_MAP(id)(params), {
    getProps: getOperationProps(nestingPath, { getState }),
    triggerChange: triggerOnFieldChangeFunToExec(payload),
    shouldCallJobComputeApi: true,
  });
};

const changeFieldsWithNoSideEffect = payload => {
  const { getState, setState, params } = payload;
  const columnId = _get(params, 'columnId');
  const valueGetter = fieldOnChangeValueGetter[columnId] || fieldOnChangeValueGetter.DEFAULT;
  const value = valueGetter(payload);
  const { values, job = EMPTY_OBJECT, operationsToUpdate: prevOperationsToUpdate } = getState();
  const operations = _get(values, FIELD_NAMES.OPERATIONS);
  const index = _get(params, ['nestingPath', 0]);
  const funcToExecute = FIELD_ID_VS_PARAMS_TO_UPDATE[columnId] || FIELD_ID_VS_PARAMS_TO_UPDATE.DEFAULT;
  const updatedFields = funcToExecute({ id: columnId, value }, { getProps: getOperationProps([index], { getState }) });
  const modifiedOperationsToUpdateInState = [
    ...operations.slice(0, index),
    { ...operations[index], ...updatedFields },
    ...operations.slice(index + 1),
  ];
  const operationsToUpdate = getUpdatedOperationsOfJob({
    prevOperationsToUpdate,
    prevOperationsOfJob: getOperations(job),
    modifiedOperationsToUpdateInState,
  });
  return setState({
    values: {
      ...values,
      [FIELD_NAMES.OPERATIONS]: modifiedOperationsToUpdateInState,
    },
    operationsToUpdate,
    showConfirmationModalOnCancel: true,
  });
};

const onFieldChange = ({ getState, setState, params = EMPTY_OBJECT }) => {
  const { handleModifyOperationsFieldChange } = getState();
  handleModifyOperationsFieldChange(true);
  const handler = fieldOnChangeHandlers[params.id] || fieldOnChangeHandlers.DEFAULT;
  return handler({ getState, setState, params });
};

const handleSubmit = ({ getState, setState }) => {
  const {
    values,
    onSubmit,
    calculationsInProgress,
    replacedOpcodeOperationIds,
    linkedOperations,
    operationInfo,
    operationGroups,
    coupons,
    deletedOperationIds,
    priceConfigs,
    operationsToUpdate,
    splitInfo,
    fees,
    job,
    primaryPayerCostCenters,
  } = getState();
  if (calculationsInProgress > 0) {
    return toaster(TOASTER_TYPE.INFO, RO_PRICE_CALCULATION_TEXT);
  }
  setState({ isSubmitAndCloseActionTriggered: true });
  const operations = _get(values, FIELD_NAMES.OPERATIONS);
  const updatedOperations = _isEmpty(operationsToUpdate) ? getOperations(job) : operationsToUpdate;
  const updatedOperationsWithoutLinkedOperations = getUpdatedOperationsWithoutLinkedOperations(
    updatedOperations,
    linkedOperations
  );
  return onSubmit(
    _filter(operations, operation => !_isEmpty(operation.opcode)),
    {
      replacedOpcodeOperationIds,
      linkedOperations,
      deletedOperationIds,
      operationInfo,
      operationGroups,
      coupons,
      priceConfigs,
      operations: updatedOperationsWithoutLinkedOperations,
      splitInfo,
      fees,
      primaryPayerCostCenters,
    }
  );
};

const handleDeleteRowAction = ({ getState, params = EMPTY_OBJECT, setState }) => {
  if (params.actionType !== REMOVE_ACTION.id) return;
  const { values, linkedOperations } = getState();
  const operations = _get(values, FIELD_NAMES.OPERATIONS);

  const index = getRowIndex(params);

  if (!_isEmpty(operations[index])) {
    if (ROConstraints.isServiceV3Enabled()) {
      changeOpcodeFormField({
        getState,
        params: { ...params, value: OperationReader.id(operations[index]), columnId: LABOR_FIELDS.DELETE_OPCODE },
        setState,
      });
      return;
    }
    setState({
      values: {
        ...values,
        [FIELD_NAMES.OPERATIONS]: [...operations.slice(0, index), ...operations.slice(index + 1)],
      },
      linkedOperations: { ...linkedOperations, [OperationReader.id(operations[index])]: EMPTY_ARRAY },
      showConfirmationModalOnCancel: true,
    });
  }
};

const handleBrowseCatalog = ({ setState, getState, params }) => {
  const { defaultConcernPromise, showLtgCatalog, linkedOperations } = getState();
  const hasOpcode = OperationReader.opcode(params?.operation);
  const operationPromise = hasOpcode
    ? Promise.resolve({
        opCode: params?.operation,
        linkedOpcodes: tget(linkedOperations, OperationReader.id(params?.operation), EMPTY_ARRAY),
      })
    : defaultConcernPromise;
  return operationPromise.then(({ opCode: operation, linkedOpcodes }) => {
    const updatedOperation = { ...operation, ...(!hasOpcode ? { id: uuid() } : {}) };
    setState(
      {
        ltgOperationParams: {
          ...params,
          operation: updatedOperation,
        },
        linkedOperations: { ...linkedOperations, [OperationReader.id(updatedOperation)]: linkedOpcodes },
      },
      showLtgCatalog
    );
  });
};

const handleSelectForcedParts = payload => {
  const { getState, setState, params } = payload;
  const { operationId, value } = params;
  const { values, job, operationsToUpdate: prevOperationsToUpdate } = getState();
  const { operations } = values;
  const partsSelectedOperations = getPartsSelectedOperations(operations, operationId, value);
  const operationsToUpdate = getUpdatedOperationsOfJob({
    prevOperationsToUpdate,
    prevOperationsOfJob: getOperations(job),
    modifiedOperationsToUpdateInState: partsSelectedOperations,
  });
  setState({
    values: {
      ...values,
      [FIELD_NAMES.OPERATIONS]: partsSelectedOperations,
    },
    operationsToUpdate,
  });
};

const handleOemOpcodeAdditionForMultipayer = ({ getState, setState, params }) => {
  const {
    shouldAddNewRow,
    showLtgCatalog,
    handleModifyOperationsFieldChange,
    type,
    isEditOpCodeDisabled,
    viewType,
    departmentId,
    customerTags,
    operationGroupId,
    job,
    primaryPayerCostCenters,
    moduleType,
    ...restParams
  } = getState();
  const { nestingPath, selectedOpCode } = params;
  const values = _get(getState(), 'values');
  const operations = tget(values, FIELD_NAMES.OPERATIONS, []);
  const operationIndex = _head(nestingPath);
  return getResolvedAndAdaptedOperationForComputeJob({
    ...restParams,
    moduleType,
    departmentId,
    customerTags,
    viewType,
    entityType: MODULE_TYPE_VS_ASSET_TYPE[moduleType],
    operationGroupId,
    contextType: JOB_COMPUTE_CONTEXT_TYPES.NEW_OPERATION_CONTEXT_TYPE,
    job,
  }).then(
    ({
      opCode,
      existingOperationsOfJob = EMPTY_ARRAY,
      operationGroups,
      coupons,
      priceConfigs,
      fees,
      splitInfo,
      primaryPayerCostCenters: primaryPayerCostCentersToUpdate,
    }) => {
      const { updatedOperations, sanitizedIsLastRow, adaptedOemOperation } = getOperationsToUpdate({
        operation: opCode,
        selectedOpCode,
        existingOperationsOfJob,
        index: operationIndex,
        isLastRow: true,
        operations,
      });
      const additionalJobFieldsToSet = getAdditionalJobFieldsToSet({
        shouldSetFields: true,
        coupons,
        priceConfigs,
        fees,
        splitInfo,
        primaryPayerCostCentersToUpdate,
        primaryPayerCostCenters,
        operationGroups,
      });

      const updatedOperationsOfJob = getSortedOperationsByOperationNumber(
        [...existingOperationsOfJob, adaptedOemOperation],
        operationGroups
      );

      return setState({
        values: {
          ...values,
          [FIELD_NAMES.OPERATIONS]: getOperationsWithEmptyRow(updatedOperations, sanitizedIsLastRow, shouldAddNewRow),
        },
        ...additionalJobFieldsToSet,
        operationsToUpdate: updatedOperationsOfJob,
        job: {
          ...job,
          operations: updatedOperationsOfJob,
          ...additionalJobFieldsToSet,
        },
        ltgOperationParams: null,
      });
    }
  );
};

// Action handler for LTG overlay opcode selection.
const handleOemOpcodeSelect = payload => {
  const { params, getState, setState } = payload || EMPTY_OBJECT;
  if (_isEmpty(params)) return;
  const { operation, nestingPath, selectedOpCode } = params;
  const { shouldAddNewRow, showLtgCatalog, handleModifyOperationsFieldChange } = getState();
  showLtgCatalog(false);
  const values = _get(getState(), 'values');
  const operations = tget(values, FIELD_NAMES.OPERATIONS, []);
  const operationIndex = _head(nestingPath);
  const { isLastRow } = operations[operationIndex];
  const adaptedOemOperation = getEditableViewOemOpCode(operation, getSanitizedOEMOperation(selectedOpCode));
  handleModifyOperationsFieldChange(hasOEMOpcodeChanged(adaptedOemOperation));
  if (ROConstraints.isServiceV3Enabled()) {
    if (isLastRow) {
      handleOemOpcodeAdditionForMultipayer({ getState, setState, params });
      return;
    }
    changeOpcodeFormField({
      getState,
      params: {
        ...params,
        value: _get(adaptedOemOperation, FIELD_IDS.LABOR_HRS),
        columnId: LABOR_FIELDS.LABOR_TIME,
        selectedOpCode,
      },
      setState,
    });
    return;
  }
  setState(
    {
      values: {
        ...values,
        [FIELD_NAMES.OPERATIONS]: getOperationsWithEmptyRow(
          [...operations.slice(0, operationIndex), adaptedOemOperation, ...operations.slice(operationIndex + 1)],
          operations[operationIndex]?.isLastRow,
          shouldAddNewRow
        ),
      },
      ltgOperationParams: null,
    },
    () => {
      if (operation[FIELD_IDS.LABOR_HRS] === adaptedOemOperation[FIELD_IDS.LABOR_HRS]) return;
      getCorrectionFieldsValue(
        { id: FIELD_IDS.LABOR_HRS, value: adaptedOemOperation[FIELD_IDS.LABOR_HRS] },
        {
          getProps: getOperationProps(nestingPath, { getState }),
          triggerChange: triggerOnFieldChange(payload),
        }
      );
    }
  );
};

const onDeleteOemOpcode = (index, { getState, setState }) => {
  const { values, shouldAddNewRow } = getState();
  const operations = _get(values, FIELD_NAMES.OPERATIONS);

  const previousOpcode = { ..._omit(operations[index], PREV_OPCODE_FIELDS_TO_OMIT), laborTimeBreakups: EMPTY_ARRAY };
  setState({
    values: {
      ...values,
      [FIELD_NAMES.OPERATIONS]: getOperationsWithEmptyRow(
        [
          ...operations.slice(0, index),
          getEditableViewOpCode(_omit(previousOpcode, STORY_LINES_FIELD_NAME), previousOpcode),
          ...operations.slice(index + 1),
        ],
        operations[index]?.isLastRow,
        shouldAddNewRow
      ),
    },
    showConfirmationModalOnCancel: true,
  });
};

const changeOemOpcode = async ({ getState, setState, params } = EMPTY_OBJECT) => {
  const selectedOpcode = _get(params, 'value');
  const index = getRowIndex(params);
  const { values, shouldAddNewRow, linkedOperations, job, primaryPayerCostCenters } = getState();
  const operations = _get(values, FIELD_NAMES.OPERATIONS);

  if (!_isEmpty(OperationReader.oemOpcode(operations[index])) && _isEmpty(selectedOpcode)) {
    onDeleteOemOpcode(index, { getState, setState });
    return false;
  }

  const adaptedFunc = ROConstraints.isServiceV3Enabled()
    ? getResolvedAndAdaptedOperationForComputeJob
    : getResolvedAndAdaptedOperation;

  const operationPromise = OperationReader.opcode(operations[index])
    ? Promise.resolve({
        opCode: operations[index],
        linkedOpcodes: tget(linkedOperations, OperationReader.id(operations[index]), EMPTY_ARRAY),
        operationGroups: operationGroups(job),
        isExistingOperation: true,
      })
    : adaptedFunc({ ...getState(), contextType: JOB_COMPUTE_CONTEXT_TYPES.NEW_OPERATION_CONTEXT_TYPE }, undefined);
  try {
    const {
      opCode: operation,
      linkedOpcodes,
      existingOperationsOfJob,
      operationGroups,
      coupons,
      priceConfigs,
      fees,
      splitInfo,
      primaryPayerCostCenters: primaryPayerCostCentersToUpdate,
      isExistingOperation = false,
    } = await operationPromise;
    const sanitizedExistingOperationsOfJob = getExistingOperations(existingOperationsOfJob, operations, operation);

    const { adaptedOemOperation, updatedOperations, sanitizedIsLastRow } = getOperationsToUpdate({
      operation: getSanitizedOperation(operation),
      selectedOpCode: getOemOpcodeFromText(selectedOpcode),
      existingOperationsOfJob: sanitizedExistingOperationsOfJob,
      isLastRow: _get(operations, [index, 'isLastRow']),
      operations,
      index,
    });

    const additionalJobFieldsToSet = getAdditionalJobFieldsToSet({
      shouldSetFields: _isEmpty(OperationReader.opcode(operations[index])),
      coupons,
      priceConfigs,
      fees,
      splitInfo,
      primaryPayerCostCentersToUpdate,
      primaryPayerCostCenters,
      operationGroups,
    });

    const existingOperations = getExistingOperations(getOperations(job), operations, operation);
    const existingOperationsOfJobWithOutOemOpcode = isExistingOperation
      ? getExistingOperationsOfJobWithOutOemOpcode(OperationReader.id(adaptedOemOperation), existingOperations)
      : existingOperations;
    const updatedOperationsWithOemOpcode = [...existingOperationsOfJobWithOutOemOpcode, adaptedOemOperation];

    const updatedOperationsOfJob = getSortedOperationsByOperationNumber(
      [...updatedOperationsWithOemOpcode, ...linkedOpcodes],
      operationGroups
    );

    return setState({
      values: {
        ...values,
        [FIELD_NAMES.OPERATIONS]: getOperationsWithEmptyRow(updatedOperations, sanitizedIsLastRow, shouldAddNewRow),
      },
      linkedOperations: { ...linkedOperations, [OperationReader.id(adaptedOemOperation)]: linkedOpcodes },
      showConfirmationModalOnCancel: true,
      ...additionalJobFieldsToSet,
      operationsToUpdate: updatedOperationsWithOemOpcode,
      job: {
        ...job,
        operations: updatedOperationsOfJob,
        ...additionalJobFieldsToSet,
      },
    });
  } catch (error) {
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(error, __('Error fetching opcode')));
  }
  return false;
};

const handleCatalogOverlayClose = ({ setState, getState }) => {
  const { showLtgCatalog } = getState();
  showLtgCatalog(false);
  setState({ ltgOperationParams: null });
};

const handleValidationSuccess = ({ params, getState, setState }) => {
  setState({ errors: { ...getState().errors, ...params.errors } });
};

const handlePartsFieldChange = ({ params, getState, setState }) => {
  const { value, operationId, id } = params;
  const { values, job, operationsToUpdate: prevOperationsToUpdate } = getState();
  const { operations } = values;
  const modifiedOperationsToUpdateInState = _map(operations, operation => {
    const { [id]: data, ...restOperation } = operation;
    return {
      ...restOperation,
      [id]: OperationReader.id(operation) === operationId ? value : data,
    };
  });
  const operationsToUpdate = getUpdatedOperationsOfJob({
    prevOperationsToUpdate,
    prevOperationsOfJob: getOperations(job),
    modifiedOperationsToUpdateInState,
  });
  setState({
    values: {
      ...values,
      [FIELD_NAMES.OPERATIONS]: modifiedOperationsToUpdateInState,
    },
    operationsToUpdate,
    showConfirmationModalOnCancel: true,
  });
};

const handleOperationsChange = ({ params, getState, setState }) => {
  const { operations } = params;
  const { values } = getState();
  const existingOperations = values?.operations;
  if (_isEmpty(operations) || _isEmpty(existingOperations)) return;
  const operationsById = _keyBy(operations, OPERATION_ID_FIELD_NAME);

  const updatedOperations = _reduce(
    existingOperations,
    (acc, existingOperation) => {
      if (_isEmpty(OperationReader.id(existingOperation))) return [...acc, existingOperation];
      const updatedOperation = operationsById[OperationReader.id(existingOperation)];
      if (
        MoneyReader.value(updatedOperation[LABOR_AMOUNT_FIELD_ID]) !==
        MoneyReader.value(existingOperation[LABOR_AMOUNT_FIELD_ID])
      )
        return [...acc, updatedOperation];
      return [...acc, existingOperation];
    },
    EMPTY_ARRAY
  );

  setState({
    values: {
      ...values,
      [FIELD_NAMES.OPERATIONS]: updatedOperations,
    },
    showConfirmationModalOnCancel: true,
  });
};

const cellOnChangeHandlers = {
  [FIELD_IDS.DEALERSHIP_OPCODE]: dealershipOpcodeChangeHandler,
  [FIELD_IDS.OEM_OPCODE]: changeOemOpcode,
  [FIELD_IDS.LABOR_HRS]: changeOpcodeFormField,
  [FIELD_IDS.LABOR_RATE]: changeOpcodeFormField,
  [FIELD_IDS.BILL_RATE]: changeOpcodeFormField,
  [FIELD_IDS.BILL_HRS]: changeOpcodeFormField,
  [FIELD_IDS.LABOR_PRICE]: changeFieldsWithNoSideEffect,
  [FIELD_IDS.SKILL]: changeFieldsWithNoSideEffect,
  [FIELD_IDS.DESCRIPTION]: changeFieldsWithNoSideEffect,
};

const cellOnChangeHandlerForMultiPayer = {
  ...cellOnChangeHandlers,
  [FIELD_IDS.LABOR_PRICE]: changeOpcodeFormField,
};

const handleTableFieldOnChange = params => {
  const { params: { columnId } = EMPTY_OBJECT } = params;
  const funcToExec = ROConstraints.isServiceV3Enabled()
    ? cellOnChangeHandlerForMultiPayer[columnId]
    : cellOnChangeHandlers[columnId];
  if (!_isFunction(funcToExec)) {
    reArrangeOperations(params);
    return null;
  }
  return funcToExec(params);
};

export const TABLE_ACTION_HANDLERS = {
  [FIELD_NAMES.OPERATIONS]: handleTableFieldOnChange,
  [FIELD_NAMES.PARTS]: handlePartsFieldChange,
};

const fieldOnChangeHandlers = {
  ...TABLE_ACTION_HANDLERS,
};

export const ACTION_HANDLERS = {
  [INIT_FORM]: onFormInit,
  [FORM_ACTION_TYPES.ON_FIELD_CHANGE]: onFieldChange,
  [FORM_PAGE_ACTION_TYPES.ON_FORM_SUBMIT]: handleSubmit,
  [TABLE_ACTION_TYPES.TABLE_ACTION_CLICK]: handleDeleteRowAction,
  [BROWSE_CATALOG_ACTION]: handleBrowseCatalog,
  [OEM_OPCODE_SELECT]: handleOemOpcodeSelect,
  [LTG_OVERLAY_CLOSE]: handleCatalogOverlayClose,
  [FORM_ACTION_TYPES.VALIDATION_SUCCESS]: handleValidationSuccess,
  [OPERATION_VALUE_CHANGE]: handleOperationsChange,
  [ACTION_TYPES.SET_FIELD_WITH_ERROR]: handlePartsFieldChange,
  PART_SELECTED: handleSelectForcedParts,
};
