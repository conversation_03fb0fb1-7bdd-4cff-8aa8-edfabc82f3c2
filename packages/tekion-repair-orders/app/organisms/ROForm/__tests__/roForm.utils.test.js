import FeeReader from 'readers/ROFee.reader';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import { validateApplicablePaytypes } from '../ROForm.utils';

// Mock dependencies
jest.mock('readers/ROFee.reader');
jest.mock('@tekion/tekion-components/src/organisms/NotificationWrapper');
jest.mock('utils', () => ({
  __: jest.fn(key => key),
}));

describe('validateApplicablePaytypes', () => {
  const mockFee = { id: 'fee-1', name: 'Test Fee' };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('when isROLevelFee is false', () => {
    it('should return the fee without validation', () => {
      const result = validateApplicablePaytypes(mockFee, false);

      expect(result).toBe(mockFee);
      expect(FeeReader.applicableSubPayTypes).not.toHaveBeenCalled();
      expect(toaster).not.toHaveBeenCalled();
    });
  });

  describe('when isROLevelFee is true', () => {
    describe('and applicable sub pay types are present', () => {
      it('should return the fee when applicable sub pay types exist', () => {
        FeeReader.applicableSubPayTypes.mockReturnValue(['paytype1', 'paytype2']);

        const result = validateApplicablePaytypes(mockFee, true);

        expect(result).toBe(mockFee);
        expect(FeeReader.applicableSubPayTypes).toHaveBeenCalledWith(mockFee);
        expect(toaster).not.toHaveBeenCalled();
      });
    });

    describe('and applicable sub pay types are not present', () => {
      it('should show error toaster and return EMPTY_OBJECT when applicable sub pay types is empty array', () => {
        FeeReader.applicableSubPayTypes.mockReturnValue(EMPTY_OBJECT);

        const result = validateApplicablePaytypes(mockFee, true);

        expect(result).toBe(EMPTY_OBJECT);
        expect(FeeReader.applicableSubPayTypes).toHaveBeenCalledWith(mockFee);
        expect(toaster).toHaveBeenCalledWith(
          TOASTER_TYPE.ERROR,
          'Fee is not Applicable, Applicable SubPayTypes not present'
        );
      });

      it('should show error toaster and return EMPTY_OBJECT when applicable sub pay types is null', () => {
        FeeReader.applicableSubPayTypes.mockReturnValue(null);

        const result = validateApplicablePaytypes(mockFee, true);

        expect(result).toBe(EMPTY_OBJECT);
        expect(FeeReader.applicableSubPayTypes).toHaveBeenCalledWith(mockFee);
        expect(toaster).toHaveBeenCalledWith(
          TOASTER_TYPE.ERROR,
          'Fee is not Applicable, Applicable SubPayTypes not present'
        );
      });

      it('should show error toaster and return EMPTY_OBJECT when applicable sub pay types is undefined', () => {
        FeeReader.applicableSubPayTypes.mockReturnValue(undefined);

        const result = validateApplicablePaytypes(mockFee, true);

        expect(result).toBe(EMPTY_OBJECT);
        expect(FeeReader.applicableSubPayTypes).toHaveBeenCalledWith(mockFee);
        expect(toaster).toHaveBeenCalledWith(
          TOASTER_TYPE.ERROR,
          'Fee is not Applicable, Applicable SubPayTypes not present'
        );
      });
    });
  });

  describe('edge cases', () => {
    it('should handle null fee parameter', () => {
      const result = validateApplicablePaytypes(null, false);

      expect(result).toBe(null);
      expect(FeeReader.applicableSubPayTypes).not.toHaveBeenCalled();
      expect(toaster).not.toHaveBeenCalled();
    });

    it('should handle undefined fee parameter', () => {
      const result = validateApplicablePaytypes(undefined, false);

      expect(result).toBe(undefined);
      expect(FeeReader.applicableSubPayTypes).not.toHaveBeenCalled();
      expect(toaster).not.toHaveBeenCalled();
    });

    it('should handle null fee parameter with RO level validation', () => {
      FeeReader.applicableSubPayTypes.mockReturnValue(EMPTY_OBJECT);

      const result = validateApplicablePaytypes(null, true);

      expect(result).toBe(EMPTY_OBJECT);
      expect(FeeReader.applicableSubPayTypes).toHaveBeenCalledWith(null);
      expect(toaster).toHaveBeenCalledWith(
        TOASTER_TYPE.ERROR,
        'Fee is not Applicable, Applicable SubPayTypes not present'
      );
    });
  });
});
