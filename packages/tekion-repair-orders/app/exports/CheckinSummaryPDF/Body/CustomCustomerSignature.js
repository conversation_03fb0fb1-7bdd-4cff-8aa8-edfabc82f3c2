import React from 'react';

import DisclosurePreview from 'exports/Components/DisclosurePreview/DisclosurePreview';
import { shouldRenderDisclosureSignatureComponent } from 'pages/PDFSettings/Components/Configurator/Show/ConfiguratorShow.helpers';

import SignatureSection from './SignatureSection';

const CustomCustomerSignature = props => {
  const { documentType, copyType } = props;
  if (shouldRenderDisclosureSignatureComponent(documentType, copyType)) {
    return <DisclosurePreview {...props} />;
  }

  return <SignatureSection {...props} />;
};

export default CustomCustomerSignature;
