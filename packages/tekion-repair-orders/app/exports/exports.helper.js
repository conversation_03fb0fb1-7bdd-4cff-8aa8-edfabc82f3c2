import _keyBy from 'lodash/keyBy';
import _head from 'lodash/head';
import _toUpper from 'lodash/toUpper';
import _get from 'lodash/get';
import _filter from 'lodash/filter';
import _isEmpty from 'lodash/isEmpty';
import _values from 'lodash/values';
import _has from 'lodash/has';
import _split from 'lodash/split';
import _map from 'lodash/map';
import _groupBy from 'lodash/groupBy';
import _trim from 'lodash/trim';
import _uniq from 'lodash/uniq';
import _join from 'lodash/join';
import _compact from 'lodash/compact';
import _reduce from 'lodash/reduce';
import _isNil from 'lodash/isNil';
import _isFunction from 'lodash/isFunction';
import _omit from 'lodash/omit';
import _some from 'lodash/some';
import _flatMap from 'lodash/flatMap';
import _truncate from 'lodash/truncate';
import _castArray from 'lodash/castArray';
import _find from 'lodash/find';
import _includes from 'lodash/includes';
import _toNumber from 'lodash/toNumber';
import _noop from 'lodash/noop';
import _every from 'lodash/every';
import _without from 'lodash/without';

import TransportationReader from 'tbase/readers/Transportation';
import { fetchEmployeeDetailsById } from 'tbase/services/employeeService';
import { fetchPdfConfigFromConfigurator } from 'tbase/actions/pdfConfig.actions';
import { getTransportationNameWithLocale } from 'tbusiness/helpers/dse/multiLingual';
import { getEntityLevelPaySplitsForAmountFieldList, getPaySplitKey, getTaxBreakupByTaxTypeForMultiPayer } from 'tbusiness/appServices/service/helpers/calculation';
import { getRepairOrderTaxTypes } from 'tbusiness/appServices/service/helpers/tax';

import ROTotalsReader from 'tbusiness/appServices/service/readers/RepairOrderTotals';
import FleetBoxReader, { EXTERNAL_DETAILS_KEY } from 'tbusiness/appServices/service/readers/FleetBox';
import ROSnapshotReader, { FIELD_IDS } from 'readers/ROSnapshot.reader';
import ROJobReader from 'readers/Jobs.reader';
import { ID } from 'tbusiness/readers/PartQuantityUOM.reader';
import ROReader from 'readers/RepairOrder.reader';
import NotesReader from 'readers/Notes.reader';
import ExportsReader from 'exports/exports.readers';
import TechnicianReader from 'readers/Technician';
import DealerEmployeeDetailsReader from 'tbase/readers/DealerEmployeeDetails';
import { ROOperationReader } from 'tbusiness/appServices/service/readers/PartROSalesJobAndOperation';
import ROPartAvailabilityReader, {
  PARTS_FULFILLMENT_LINE_ITEM_ID,
} from 'tbusiness/appServices/service/readers/ROPartAvailability';
import MediaReader from 'tbase/readers/Media';
import PDFConfigReader, { LOGO, URL, KEY, PDF_SECTIONS, VALUE, RENDER } from 'tbase/readers/pdfConfig.reader';
import ROPDFSectionPropertyReader, { SECTION_METADATA_FIELDS } from 'readers/pdfSectionProperty.reader';
import MandatoryAssetInfoReader from 'readers/MandatoryAssetInfo.reader';
import { shouldDisplayField } from 'tcomponents/molecules/pdfHeaderLeftSection/pdfConfiguratorComponents.helper';
import { getLookupUserFromResource } from 'tbusiness/appServices/service/utils/user';
import ExportsEnv from 'exports/ExportsEnv';
import pdfExportUtils from 'tcomponents/enhancers/withEnhancedExport/withEnhancedExport.utils';
import { gracefulFetchForPdf } from 'tbase/utils/exportUtils';
import { tget } from 'tbase/utils/general';
import Money from 'tbase/utils/money';
import MoneyReader from 'tbase/readers/TMoney';
import EmployeeReader from 'tbase/readers/Employee';
import BDCAppointmentReader from 'twidgets/appServices/dse/readers/BDCAppointmentReader';
import JobReader, {
  getPayType,
  getOperations,
  getJobId,
  PRICE_INFO,
  OVERRIDDEN_PRICE_INFO,
} from 'tbusiness/appServices/service/readers/Job';
import OperationReader from 'tbusiness/appServices/service/readers/ROOperation';
import CashieringSettingsReader from 'tbase/readers/CashieringSettings';
import FeeReader, { DESCRIPTION } from 'tbase/readers/fee';
import { getPdfConfigByDepartment, getPDFVariantConfigForDefaultDepartment } from 'tbase/helpers/pdfConfig.helper';
import { getSectionProperties } from 'tbusiness/appServices/service/helpers/pdfConfig.helper';
import { formatCurrency } from '@tekion/tekion-base/formatters/formatCurrency';
import VehicleReader, { MAKE_ID } from '@tekion/tekion-base/readers/Vehicle';
import {
  getFleetNumber,
  getExteriorColor,
  getColor,
  getManufacturerColorCode,
  getVehicleData,
} from 'tbusiness/appServices/service/readers/vehicleProfile/VehicleProfile';
import { shouldShowCreditSurchargeInfo } from 'tbusiness/appServices/service/helpers/creditSurcharge.helper';
import { getCombinedTrimValue } from 'tbase/helpers/serviceVehicle.helpers';
import { getPdfConfigurationWithTargetLanguageValues } from 'helpers/pdfConfig.helper';
import { isQuoteServiceVoided } from 'helpers/quoteService.helper';
import { isJobVoided } from 'helpers/job.helper';
import { getIsCustomerBasePayType } from 'helpers/payTypes.helpers';
import { getOpcodeLevelPriceType } from 'helpers/opcode.helper';
import { getCurrentUserPreferredLanguage } from 'twidgets/appServices/service/utils/multilingual';
import RepairOrderEnv from 'utils/repairOrderEnv';

import { RO_ID, CLOSED_TIME } from 'tbase/readers/RepairOrder';
import { getDocumentType } from 'helpers/pdf';
import { convertSecToHours } from '@tekion/tekion-base/utils/dateUtils';
import MoneyUtils from '@tekion/tekion-base/utils/money';

import { EMPTY_ARRAY, EMPTY_OBJECT, NO_DATA, EMPTY_SET } from 'tbase/app.constants';
import { DAMAGE_IMAGES_APPLICABLE_CATEGORY } from 'tbase/constants/mediaDrawer';
import { DEFAULT_TAX_TYPE, TAX_BREAKUP_BY_TAX_TYPE_FIELD } from 'tbase/constants/service/tax';
import { DEPARTMENT_TYPES } from 'tbusiness/constants/department';
import {
  COPY_TYPES_VS_SUPPORTED_BASE_PAY_TYPES_SET,
  COPY_TYPES_VS_SUPPORTED_BASE_PAY_TYPES_SET_FOR_V3,
} from 'tbusiness/constants/service/roPdf.constants';
import { MEDIA_TYPES } from 'tbase/constants/media';
import { COPY_TYPES, DOCUMENT_TYPES, DEFAULT, DOCUMENT_TYPES_LABEL } from 'tbase/constants/roPdf.constants';
import { PDF_SECTION, PDF_SECTION_COMPONENTS } from 'tbase/constants/pdfBuilder.constants';
import OPERATORS from 'tbase/constants/filterOperators';
import CDN_STATIC_ASSET_URL from 'tbusiness/constants/cdnStaticAssetUrl';
import { RECALL_DESCRIPTION } from 'tbusiness/appServices/service/constants/vehicleProfile/recalls';
import {
  HEADER_COMPONENTS,
  BODY_COMPONENTS,
  LOGO_SUPPORTED_COMPONENTS_SET,
  STATIC_IMAGE_SUPPORTED_COMPONENTS_SET,
} from 'tcomponents/molecules/pdfHeaderLeftSection/pdfConfiguratorComponents.constants';
import {
  PAPER_SIZES,
  ORIENTATION,
  ORIENTATION_STYLES,
} from 'tcomponents/enhancers/withEnhancedExportAdvanced/withEnhancedExportAdvanced.constants';
import DEALER_PROPERTY from 'tbase/constants/dealerProperties';
import { AMOUNT_FIELD_FOR_MULTI_PAYER } from 'tbusiness/appServices/service/constants/repairOrderTotals';
import { SUB_PAY_TYPE } from 'twidgets/organisms/CustomerDetailsForm/customerDetails.constants';
import { ENTITY_TYPES } from 'constants/paySplit.constants';
import { ASSET_TYPES } from 'constants/mandatoryAssetInfo.constants';
import { DEFERRED_RECALLS_TABLE_COLUMNS } from 'constants/deferredRecalls.constants';
import { PRICE_TYPES } from 'organisms/ROForm/Components/OpCode/Components/ServiceHeader/Components/OpcodePrice/OpcodePrice.constants';

import ROConstraints from 'helpers/constraints';
import { getComebackTagDetails } from 'helpers/tags.helpers';
import { getTaxCodesForMultiPayer, getTaxTypesForDealer } from 'helpers/tax.helper';
import ROSettingsReader, { JOB_TYPE_TAG } from 'readers/ROSettings.reader';
import TagSettingsReader, { TAG_NAME } from 'readers/TagSettings.reader';
import PayerDeductibleReader from '@tekion/tekion-widgets/src/appServices/service/readers/PayerDeductible.reader';
import Payer from '@tekion/tekion-business/src/appServices/service/readers/Payer';
import { PAYER_ID } from '@tekion/tekion-business/src/appServices/service/constants/general';
import { RO_STATUS_VALUES } from '@tekion/tekion-base/constants/repairOrder/roStatus';

import { SECTION_NAME } from 'readers/pdfConfig.reader';
import {
  DOCUMENT_TYPE_CODES,
  CONCERN_TYPE_VS_CONCERN_TEXT,
  NONE,
  VEHICLE_TYPES,
  PAY_TYPES,
  TAX_CODE_DISPLAY_MAX_LENGTH,
  AMOUNT_FIELDS,
  ENTITY_TYPES_VS_DOC_TYPES_AMOUNT_FIELD_LIST,
  SNAPSHOT_DETAILS_REQUEST_OPTIONS,
  GM_REDEMPTION_INFO_API_FALLBACK_VALUE,
  STATIC_IMAGE_FILENAME_MAP,
  TNC_SECTION_DEFAULT_MAKE_ID,
  TNC_FIRST_PAGE_NO,
  TNC_SECOND_PAGE_NO,
  MINIMUM_SURCHARGE_AMOUNT,
  DISCLOSURE_SECTION_LIST,
} from './exports.constants';

export const getFormattedAmount = amount => Money.format(amount, { isInt: true });

export const getMultiplierCoefficient = () => (ExportsEnv.isCreditMemoSupportedDocType ? -1 : 1);

export const getIndicateFieldValue = (value, isIndicateFieldChanged) => (isIndicateFieldChanged ? `${value}*` : value);

export const getTimeInHrs = (seconds, options) => getMultiplierCoefficient() * convertSecToHours(seconds, options);

export const getFormattedCurrency = amount => {
  if (_isNil(amount)) return NO_DATA;
  const formattedAmount = getFormattedAmount(getMultiplierCoefficient() * amount);
  return formatCurrency(formattedAmount);
};

export const getROExternalNotesByJobId = roJobExternalNotes => _groupBy(roJobExternalNotes, NotesReader.assetId);
export const vinFormatter = data => _toUpper(data);
export const vehiclePlateFormatter = data => _toUpper(data);

export const getSanitizedExpandedTableColumns = tableColumns => [
  {
    expander: true,
    width: 0,
    Expander: () => null,
  },
  ...tableColumns,
];

export const getImageMedia = roMedia =>
  _head(_split(MediaReader.contentType(_get(roMedia, 'mediaItem')), '/')) === MEDIA_TYPES.IMAGE;

export const getRODamageImages = roMediaDetails =>
  _filter(_get(roMediaDetails, 'mediaItemList', EMPTY_ARRAY), roMedia =>
    DAMAGE_IMAGES_APPLICABLE_CATEGORY.has(_get(roMedia, 'category'))
  );

export const getQRCodePayload = ({ documentType, documentVersion, assetNo, assetId, assetType } = EMPTY_OBJECT) => ({
  assetId,
  copyType: _get(DOCUMENT_TYPE_CODES, documentType, documentType),
  assetNumber: assetNo,
  assetType,
  department: DEPARTMENT_TYPES.SERVICE,
  copyTypeVersion: documentVersion,
});

export const getPDFVariantConfig = (data, departmentId) => {
  if (_has(data, 'pdfSections')) return data; // in case of fallBackSettings
  const configByDepartmentId = getPdfConfigByDepartment(data, departmentId);
  return !_isEmpty(configByDepartmentId)
    ? _head(_values(configByDepartmentId))
    : getPDFVariantConfigForDefaultDepartment(data);
};
const getLogoSectionProperties = sectionProperties =>
  _find(sectionProperties, sectionProperty => LOGO_SUPPORTED_COMPONENTS_SET.has(PDFConfigReader.key(sectionProperty)));

export const getResolvedLogoUrls = pdfSections =>
  _reduce(
    pdfSections,
    (acc, pdfSection) => {
      const sectionProperties = getSectionProperties(pdfSection);
      const logoSectionProperty = getLogoSectionProperties(sectionProperties);
      if (_isEmpty(logoSectionProperty)) {
        return acc;
      }
      const logo = PDFConfigReader.value(logoSectionProperty);
      const sectionName = PDFConfigReader.sectionName(pdfSection);
      return {
        ...acc,
        [sectionName]: {
          ...logo,
          sectionName,
          showLogo: shouldDisplayField(logoSectionProperty),
          [LOGO]: logo,
          isLogoUpdated: !_isEmpty(logo),
        },
      };
    },
    EMPTY_OBJECT
  );

export const getFormattedLogoDetails = pdfSections => {
  const resolvedLogoDetails = getResolvedLogoUrls(pdfSections) || EMPTY_OBJECT;
  const formattedLogoDetails = _reduce(
    resolvedLogoDetails,
    (acc, logoDetails, section) => {
      const logoMediaDetails = _get(logoDetails, [LOGO], EMPTY_ARRAY);
      const mediaList = _compact(_castArray(logoMediaDetails));
      return {
        ...acc,
        [section]: {
          ...logoDetails,
          [LOGO]: mediaList,
        },
      };
    },
    EMPTY_OBJECT
  );
  const firstPageLogoInfo = formattedLogoDetails[PDF_SECTION.FIRST_PAGE_HEADER];
  const pageLogoInfo = formattedLogoDetails[PDF_SECTION.HEADER];
  const footerLogoInfo = formattedLogoDetails[PDF_SECTION.FOOTER];
  const lastPageFooterLogoInfo = formattedLogoDetails[PDF_SECTION.LAST_PAGE_FOOTER];
  return {
    firstPageLogoInfo,
    pageLogoInfo,
    footerLogoInfo,
    lastPageFooterLogoInfo,
  };
};

export const getClassName = (paperSize = PAPER_SIZES.LETTER, orientation = ORIENTATION.PORTRAIT) => {
  const orientationClassName = ORIENTATION_STYLES[orientation];
  const paperSizeClassName = pdfExportUtils.getClassForPageSize(paperSize);
  return `${orientationClassName} export-pdf ${paperSizeClassName}`;
};

export const getCustomerConcern = ({ concern, concernType }) => {
  if (concernType === NONE) return concern;
  return __('{{concernType}}{{concern}}', { concernType: CONCERN_TYPE_VS_CONCERN_TEXT[concernType], concern });
};

const GM_REWARDS_SUPPORTED_COPY_TYPES = new Set([
  COPY_TYPES.CUSTOMER,
  COPY_TYPES.TECHNICIAN,
  COPY_TYPES.OEM_SERVICE_BOOKLET,
  COPY_TYPES.ACCOUNTING,
  COPY_TYPES.SERVICE_ADVISOR,
  COPY_TYPES.PAYER, // TODO: revisit with the proper requirement post service-3.0
]);

export const isAllowedCopyTypeForGMRewards = copyType => GM_REWARDS_SUPPORTED_COPY_TYPES.has(copyType);

export const shouldShowRecalls = currentPDFVariantConfig => {
  const { pdfSections } = currentPDFVariantConfig || EMPTY_OBJECT;
  const keyedSections = _keyBy(pdfSections, 'sectionName');
  const bodySection = _get(keyedSections, [PDF_SECTION.BODY], EMPTY_OBJECT);
  const { afterSections, beforeSections } = bodySection || EMPTY_OBJECT;
  const keyedBodySectionProps = _keyBy(
    [...(afterSections || EMPTY_ARRAY), ...(beforeSections || EMPTY_ARRAY)],
    'sectionName'
  );
  const showRecalls = _get(keyedBodySectionProps, [BODY_COMPONENTS.DEFERRED_RECALLS, 'render'], false);
  return showRecalls;
};

export const shouldRenderSpecificBodyComponent = (currentPDFVariantConfig, bodyComponentId) => {
  const { pdfSections } = currentPDFVariantConfig || EMPTY_OBJECT;
  const keyedSections = _keyBy(pdfSections, 'sectionName');
  const bodySection = _get(keyedSections, [PDF_SECTION.BODY], EMPTY_OBJECT);
  const { afterSections, beforeSections } = bodySection || EMPTY_OBJECT;
  const keyedBodySectionProps = _keyBy(
    [...(afterSections || EMPTY_ARRAY), ...(beforeSections || EMPTY_ARRAY)],
    'sectionName'
  );
  const shouldRenderThisBodyComponent = _get(keyedBodySectionProps, [bodyComponentId, 'render'], false);
  return shouldRenderThisBodyComponent;
};

export const getFormattedVehicleTrimDetails = vehicleDetails => {
  const vehicleTrimDetails = getCombinedTrimValue(
    vehicleDetails,
    ROConstraints.isVehicleAttributeStandardisationEnabled()
  );
  return _trim(vehicleTrimDetails).length === 0 ? __('Trim: NA') : vehicleTrimDetails;
};

export const getEmployeeCertificationNumberById = (employeeList, employeeId) => {
  const employeesById = _keyBy(employeeList, EmployeeReader.id);
  return EmployeeReader.certificationNumber(employeesById[employeeId]);
};

export const getTechnicianEmployeeIds = repairOrder => {
  const assignedTechIds = _uniq(ROReader.technicianIds(repairOrder));
  return _reduce(
    assignedTechIds,
    (acc, assignedTechId) => {
      const techDetails = getLookupUserFromResource(repairOrder, assignedTechId);
      const dealerEmployeeId = DealerEmployeeDetailsReader.employeeId(techDetails);
      if (_isNil(dealerEmployeeId)) return acc;
      return [...acc, dealerEmployeeId];
    },
    EMPTY_ARRAY
  );
};

export const fetchROEmployeeDetails = repairOrder => {
  const ids = getTechnicianEmployeeIds(repairOrder) || EMPTY_ARRAY;
  if (_isEmpty(ids)) return Promise.resolve(EMPTY_ARRAY);
  return gracefulFetchForPdf(
    fetchEmployeeDetailsById({
      ids,
    }),
    EMPTY_ARRAY
  );
};

export const getMergedString = (assets, stringToJoinWith) => _join(_compact(assets), stringToJoinWith);

const isMultipleVehicleTypeEnabled = () => RepairOrderEnv.dealerProperty[DEALER_PROPERTY.MULTIPLE_VEHICLE_TYPE_ENABLED];

export const isPdfConfiguratorEnabled = () => RepairOrderEnv.dealerProperty[DEALER_PROPERTY.PDF_CONFIGURATOR_ENABLED];

export const showEngineHrs = vehicleType => isMultipleVehicleTypeEnabled() && vehicleType === VEHICLE_TYPES.RV;

export const getDisplayVin = (sectionPropsByKeys, engineHrs, vehicleType) =>
  shouldDisplayField(sectionPropsByKeys[HEADER_COMPONENTS.ENGINE_HRS]) &&
  !_isEmpty(engineHrs) &&
  showEngineHrs(vehicleType);

export const getVehicleAdditionalDetails = (repairOrder, vehicleProfileDetails) => {
  const { vehicleData } = vehicleProfileDetails || EMPTY_OBJECT;
  return {
    stockId: ROReader.getVehicleStockId(repairOrder),
    mileageIn: ROReader.getVehicleMileageIn(repairOrder),
    mileageOut: ROReader.getVehicleMileage(repairOrder),
    engineHrsIn: ROReader.getVehicleEngineHoursIn(repairOrder),
    engineHrsOut: ROReader.getVehicleEngineHoursOut(repairOrder),
    vehicleInfoFromRO: ROReader.getVehicleDetails(repairOrder, RepairOrderEnv.standardisedMakes),
    vehicleType: isMultipleVehicleTypeEnabled()
      ? ROReader.getVehicleType(repairOrder) || VEHICLE_TYPES.CAR_AUTOMOBILE
      : VEHICLE_TYPES.CAR_AUTOMOBILE,
    fleetNumber: getFleetNumber(vehicleData),
    vehiclePlate: ROReader.getVehicleLicensePlate(repairOrder) || VehicleReader.licensePlate(vehicleData),
  };
};

/**
 * @deprecated
 */
export const fetchPdfConfigWithLocaleSpecificValue = (payload, siteId) => {
  const locale = getCurrentUserPreferredLanguage();
  return fetchPdfConfigFromConfigurator(payload, siteId).then(pdfConfigurationList =>
    _map(pdfConfigurationList, pdfConfiguration =>
      getPdfConfigurationWithTargetLanguageValues(pdfConfiguration, locale)
    )
  );
};

export const getFeeCodeDescription = fee =>
  _join(_compact([FeeReader.feeCode(fee), tget(fee, DESCRIPTION, __('Fee'))]), ' - ');

// Source of Truth
// Invoice Customer Copy - Content Settings && Configuration Screen Section Config
// for other Copy - Configuration Screen Section Config

export const canShowCauses = ({
  documentType,
  copyType,
  showCausesFromContentSettings,
  showCausesFromConfigurationSettings,
  causes,
}) => {
  if (_isEmpty(causes)) return false;
  return !(documentType === DOCUMENT_TYPES.INVOICE && copyType === COPY_TYPES.CUSTOMER)
    ? showCausesFromConfigurationSettings
    : showCausesFromContentSettings && showCausesFromConfigurationSettings;
};

const SUPPORTED_CUSTOMER_COPY_TYPES = new Set([COPY_TYPES.CUSTOMER, COPY_TYPES.OEM_SERVICE_BOOKLET]);

export const isCustomerCopy = copyType => SUPPORTED_CUSTOMER_COPY_TYPES.has(copyType);

export const shouldDisplayJob = (job, copyType, shouldRenderVoidedJobs, isQuoteDocType = false) => {
  const isVoided = isQuoteDocType ? isQuoteServiceVoided(job) : isJobVoided(job);
  if (copyType === COPY_TYPES.EXTERNAL_COMPANY) {
    /*
     * for Invoice - EXTERNAL_COMPANY, only CP jobs must be displayed as insurance/warranty split
     * only applies to CP jobs
     */
    return isVoided ? false : getPayType(job) === PAY_TYPES.CUSTOMER_PAY;
  }
  if (!isVoided) return true;
  return isCustomerCopy(copyType) ? false : shouldRenderVoidedJobs;
};

export const shouldDisplayJobForMultiPayer = ({
  job,
  copyType,
  shouldRenderVoidedJobs,
  isQuoteDocType = false,
  basePayType,
}) => {
  const isVoided = isQuoteDocType ? isQuoteServiceVoided(job) : isJobVoided(job);
  if (!isVoided) return true;
  return getIsCustomerBasePayType(basePayType) || isCustomerCopy(copyType) ? false : shouldRenderVoidedJobs;
};

export const getTransportationTypeData = ({ transportationList, transportationId, isMultiLingualEnabled = false }) => {
  if (_isEmpty(transportationId)) return EMPTY_OBJECT;
  const transportationListKeyById = _keyBy(transportationList, TransportationReader.id);
  const transportationDetails = _get(transportationListKeyById, transportationId, EMPTY_OBJECT);
  return {
    transportationType: TransportationReader.transportationType(transportationDetails),
    transportationTypeLabel:
      getTransportationNameWithLocale({
        isMultiLingualEnabled,
        transportationDetails,
        preferredLanguage: getCurrentUserPreferredLanguage(),
      }) || NO_DATA,
  };
};

export const logExportPDFMessage = message => {
  if (_isFunction(window.logMsgAtPuppeteer)) {
    window.logMsgAtPuppeteer(message);
  }
};

export const getJobClockPunchTimeDetails = siteId =>
  ROSettingsReader.getPunchTimeTags(RepairOrderEnv.settingsBySiteId[siteId]);

const getReturnROTagFromSettingsBySiteId = siteId => {
  const jobTagConfig = ROSettingsReader.getJobTagsList(RepairOrderEnv.settingsBySiteId[siteId]);
  return getComebackTagDetails({ tagSettings: jobTagConfig, tagName: JOB_TYPE_TAG });
};

export const getJobTagsList = (siteId, tagSettings) => {
  const returnROTagConfig = ROConstraints.isROJobTagsV2Enabled()
    ? getComebackTagDetails({ tagSettings, tagName: TAG_NAME })
    : getReturnROTagFromSettingsBySiteId(siteId);
  return {
    colorCode: TagSettingsReader.colorCode(returnROTagConfig),
    textColorCode: TagSettingsReader.textColorCode(returnROTagConfig),
    label: TagSettingsReader.label(returnROTagConfig) || TagSettingsReader.displayName(returnROTagConfig),
  };
};

export const getReturnROClosedDateSearchPayload = jobDetails => {
  const returnROIdList = _reduce(
    jobDetails,
    (returnRoIds, job) => {
      const returnROId = JobReader.getReturnROReferenceId(job);
      return returnROId ? [...returnRoIds, returnROId] : returnRoIds;
    },
    []
  );
  return {
    filters: [
      {
        operator: OPERATORS.IN,
        values: _uniq(returnROIdList),
        field: RO_ID,
      },
    ],
    includeFields: [RO_ID, CLOSED_TIME],
  };
};

const isDuplexPrintWithLastPageDisplayOptionSelected = pdfConfig => {
  const pdfSections = PDFConfigReader.pdfSections(pdfConfig);
  return _some(pdfSections, section => {
    const sectionName = PDFConfigReader.sectionName(section);
    const sectionProperties = PDFConfigReader.sectionProperties(section);
    return sectionName === PDF_SECTION.LAST_PAGE_AND_DUPLEX_PRINT_BACK_PRINT && !_isEmpty(sectionProperties);
  });
};

export const getParsedPDFBuilderComponents = (pdfComponents, pdfConfig) => {
  const parsedPDFBuilderComponents = isDuplexPrintWithLastPageDisplayOptionSelected(pdfConfig)
    ? _omit(pdfComponents, [
        PDF_SECTION_COMPONENTS.BACK_PRINT_COMPONENTS,
        PDF_SECTION_COMPONENTS.LAST_PAGE_BACK_PRINT_COMPONENTS,
      ])
    : _omit(pdfComponents, PDF_SECTION_COMPONENTS.DUPLEX_PRINT_WITH_LAST_PAGE_BACK_PRINT_COMPONENTS);
  return parsedPDFBuilderComponents;
};

export const getTotalFlagAndBillingTimeForTechnician = flagTimesWithPayDay =>
  _reduce(
    flagTimesWithPayDay,
    (acc, flagTimeWithPayDay) => {
      const totalFlagHrs = TechnicianReader.getFlagTimeInSeconds(flagTimeWithPayDay) + acc.totalFlagTimeInSeconds;
      const totalBillingTimeInSeconds =
        TechnicianReader.getBillingTimeInSeconds(flagTimeWithPayDay) + acc.totalAssignedBillingTimeInSeconds;
      return {
        ...acc,
        totalFlagTimeInSeconds: totalFlagHrs,
        totalAssignedBillingTimeInSeconds: totalBillingTimeInSeconds,
      };
    },
    { totalFlagTimeInSeconds: 0, totalAssignedBillingTimeInSeconds: 0 }
  );

export const getMediaSignedURLApiPayload = logoDetails =>
  _get(logoDetails, 'isLogoUpdated') ? _castArray(logoDetails) : EMPTY_ARRAY;

/*
 * Multi Payer helper functions for calculations
 */

const getAmountFieldList = (entityType, documentType) => {
  const amountFieldGetterForEntity = ENTITY_TYPES_VS_DOC_TYPES_AMOUNT_FIELD_LIST[entityType];
  return amountFieldGetterForEntity[documentType] || amountFieldGetterForEntity[DEFAULT];
};

const getBreakupDetailsForOtherPayers = ({
  repairOrder,
  jobs,
  documentType,
  copyType,
  subPayTypeVsBasePayTypeMap,
  allowedBasePayTypesSet,
}) =>
  _reduce(
    ROReader.getPayers(repairOrder),
    (breakupDetailsForOtherPayers, payer) => {
      const payerId = Payer.payerId(payer);
      const subPayType = Payer.subPayType(payer);
      return {
        ...breakupDetailsForOtherPayers,
        [payerId]: getBreakupDetailsForMultiPayer({
          jobs,
          documentType,
          copyType,
          subPayType,
          payerId,
          subPayTypeVsBasePayTypeMap,
          allowedBasePayTypesSet,
        }),
      };
    },
    EMPTY_OBJECT
  );

export const getBreakupDetailsForMultiPayer = ({
  jobs,
  documentType,
  copyType,
  subPayType,
  payerId,
  subPayTypeVsBasePayTypeMap,
  allowedBasePayTypesSet,
  jobEntityType = ENTITY_TYPES.JOB,
  operationEntityType = ENTITY_TYPES.OPERATION,
}) => {
  const sanitizedAllowedBasePayTypesSet = _isEmpty(allowedBasePayTypesSet)
    ? COPY_TYPES_VS_SUPPORTED_BASE_PAY_TYPES_SET[copyType]
    : allowedBasePayTypesSet;
  return _reduce(
    jobs,
    (breakupDetailsForMultiPayer, job) => {
      const jobId = getJobId(job);
      return {
        ...breakupDetailsForMultiPayer,
        [jobId]: {
          ...getEntityLevelPaySplitsForAmountFieldList({
            entityDetails: job,
            allowedBasePayTypesSet: sanitizedAllowedBasePayTypesSet,
            subPayType,
            payerId,
            subPayTypeVsBasePayTypeMap,
            amountFieldList: getAmountFieldList(jobEntityType, documentType),
          }),
          ..._reduce(
            JobReader.operations(job),
            (acc, operation) => {
              const operationId = OperationReader.id(operation);
              return {
                ...acc,
                [operationId]: getEntityLevelPaySplitsForAmountFieldList({
                  entityDetails: operation,
                  allowedBasePayTypesSet: sanitizedAllowedBasePayTypesSet,
                  subPayType,
                  payerId,
                  subPayTypeVsBasePayTypeMap,
                  amountFieldList: getAmountFieldList(operationEntityType, documentType),
                }),
              };
            },
            EMPTY_OBJECT
          ),
        },
      };
    },
    EMPTY_OBJECT
  );
};

const getROTotalSaleAmount = (repairOrder, subPayTypeVsBasePayTypeMap) => {
  const roComponentSplits = getEntityLevelPaySplitsForAmountFieldList({
    entityDetails: repairOrder,
    allowedBasePayTypesSet: COPY_TYPES_VS_SUPPORTED_BASE_PAY_TYPES_SET[COPY_TYPES.ACCOUNTING],
    amountFieldList: [AMOUNT_FIELD_FOR_MULTI_PAYER.TOTAL_SALE_AMOUNT],
    subPayTypeVsBasePayTypeMap,
  });
  return ROTotalsReader.totalSaleAmount(roComponentSplits);
};

const getROLevelTaxBreakup = ({subPayType, payerId, repairOrder}) => {
  const paySplitKey = getPaySplitKey(subPayType, payerId);
  
  if (paySplitKey) {
    const paySplit = tget(ROReader.getPaySplit(repairOrder), paySplitKey, EMPTY_OBJECT);
    return tget(paySplit, 'taxCalculationResults', EMPTY_ARRAY);
  } else {
    const allPaySplits = ROReader.getPaySplit(repairOrder) || EMPTY_OBJECT;
    return _reduce(
      allPaySplits,
      (acc, paySplit) => {
        if (_isEmpty(paySplit)) return acc;
        
        const splitTaxCalculations = tget(paySplit, 'taxCalculationResults', EMPTY_ARRAY);
        return [...acc, ...splitTaxCalculations];
      },
      EMPTY_ARRAY
    );
  }
};

const getTaxBreakupByTaxType = ({subPayType, payerId, repairOrder}) => {
  const taxCalculationResults = getROLevelTaxBreakup({subPayType, payerId, repairOrder});
  const roTaxTypes = getRepairOrderTaxTypes(repairOrder);
  const dealerTaxTypes = getTaxTypesForDealer() || [DEFAULT_TAX_TYPE];
  const taxTypes = !_isEmpty(roTaxTypes) ? roTaxTypes : dealerTaxTypes;
  return getTaxBreakupByTaxTypeForMultiPayer(taxCalculationResults, taxTypes)
};

export const getROLevelTotalBreakupDetailsForMultiPayer = ({
  repairOrder,
  documentType,
  copyType,
  subPayType,
  payerId,
  subPayTypeVsBasePayTypeMap,
  allowedBasePayTypesSet,
}) => {
  const roComponentSplits = getEntityLevelPaySplitsForAmountFieldList({
    entityDetails: repairOrder,
    allowedBasePayTypesSet: _isEmpty(allowedBasePayTypesSet)
      ? COPY_TYPES_VS_SUPPORTED_BASE_PAY_TYPES_SET[copyType]
      : allowedBasePayTypesSet,
    subPayType,
    payerId,
    subPayTypeVsBasePayTypeMap,
    amountFieldList: getAmountFieldList(ENTITY_TYPES.RO, documentType),
  });
  const taxBreakupByTaxType = getTaxBreakupByTaxType({subPayType, payerId, repairOrder});
  return {
    ...roComponentSplits,
    [TAX_BREAKUP_BY_TAX_TYPE_FIELD]: taxBreakupByTaxType,
    [AMOUNT_FIELDS.LABOR_TOTAL_WITHOUT_FEE]: Money.add(
      ROTotalsReader.laborSaleAmount(roComponentSplits),
      ROTotalsReader.laborAdjustmentAmount(roComponentSplits)
    ),
    [AMOUNT_FIELDS.PARTS_TOTAL_WITHOUT_TAX]: Money.add(
      ROTotalsReader.partSaleAmount(roComponentSplits),
      ROTotalsReader.partAdjustmentAmount(roComponentSplits)
    ),
    [AMOUNT_FIELDS.TOTAL_FEE_AMOUNT]: Money.add(
      ROTotalsReader.feeSaleAmount(roComponentSplits),
      ROTotalsReader.feeAdjustmentAmount(roComponentSplits)
    ),
    [AMOUNT_FIELDS.SUBLET_LABOR_AMOUNT_WITHOUT_TAX]: ROTotalsReader.subletLaborSaleAmount(roComponentSplits),
    [AMOUNT_FIELDS.SUBLET_PARTS_AMOUNT_WITHOUT_TAX]: ROTotalsReader.subletPartSaleAmount(roComponentSplits),
    [AMOUNT_FIELDS.TOTAL_DISCOUNT_AMOUNT]: Money.add(
      ROTotalsReader.discountAmount(roComponentSplits),
      ROTotalsReader.adjustmentAmount(roComponentSplits)
    ),
    [AMOUNT_FIELDS.PAYER_CONTRIBUTION_AMOUNT]: ROTotalsReader.totalSaleAmount(roComponentSplits),
    [AMOUNT_FIELDS.RO_TOTAL_ESTIMATE_AMOUNT]: ROTotalsReader.totalEstimateAmount(roComponentSplits),
    [AMOUNT_FIELDS.RO_TOTAL_SALE_AMOUNT]:
      _isNil(payerId) || isCustomerCopy(copyType)
        ? ROTotalsReader.totalSaleAmount(roComponentSplits)
        : getROTotalSaleAmount(repairOrder, subPayTypeVsBasePayTypeMap),
    [AMOUNT_FIELDS.RO_TOTAL_COST_AMOUNT]: ROTotalsReader.totalCostAmount(roComponentSplits),
  };
};

const DOC_TYPES_VS_TOTAL_AMOUNT_READER = {
  [DOCUMENT_TYPES.RO_PAYER_CANCELLED_INVOICE]: ExportsReader.payerContributionAmount,
  [DOCUMENT_TYPES.RO_PAYER_INVOICE]: ExportsReader.payerContributionAmount,
  [DOCUMENT_TYPES.RO_ESTIMATE]: ExportsReader.roTotalEstimateAmount,
  [DEFAULT]: ExportsReader.roTotalSaleAmount,
};

export const getTaxCodesForOtherPayers = (repairOrder, totals) =>
  _reduce(
    ROReader.getPayers(repairOrder),
    (taxCodesForOtherPayers, payer) => {
      const payerId = Payer.payerId(payer);
      return {
        ...taxCodesForOtherPayers,
        [payerId]: getTaxCodesForMultiPayer(totals, payerId),
      };
    },
    EMPTY_OBJECT
  );

const getTotalAmountForMultiPayer = (roLevelTotalBreakupDetailsForMultiPayer, documentType) =>
  (DOC_TYPES_VS_TOTAL_AMOUNT_READER[documentType] || DOC_TYPES_VS_TOTAL_AMOUNT_READER[DEFAULT])(
    roLevelTotalBreakupDetailsForMultiPayer
  );

export const getAmountDetailsForMultiPayer = ({
  repairOrder,
  jobs,
  documentType,
  copyType,
  payerId,
  subPayType,
  subPayTypeVsBasePayTypeMap,
  allowedBasePayTypesSet,
}) => {
  const breakupDetailsForMultiPayer = getBreakupDetailsForMultiPayer({
    jobs,
    documentType,
    copyType,
    payerId,
    subPayType,
    subPayTypeVsBasePayTypeMap,
    allowedBasePayTypesSet,
  });
  const roLevelTotalBreakupDetailsForMultiPayer = getROLevelTotalBreakupDetailsForMultiPayer({
    repairOrder,
    documentType,
    copyType,
    payerId,
    subPayType,
    subPayTypeVsBasePayTypeMap,
    allowedBasePayTypesSet,
  });
  const totalAmountForMultiPayer = getTotalAmountForMultiPayer(roLevelTotalBreakupDetailsForMultiPayer, documentType);
  const breakupDetailsForOtherPayers = getBreakupDetailsForOtherPayers({
    repairOrder,
    jobs,
    documentType,
    copyType,
    subPayTypeVsBasePayTypeMap,
    allowedBasePayTypesSet,
  });
  return {
    breakupDetailsForMultiPayer,
    roLevelTotalBreakupDetailsForMultiPayer,
    totalAmountForMultiPayer,
    breakupDetailsForOtherPayers,
  };
};

export const getDocumentTitle = documentType => DOCUMENT_TYPES_LABEL[getDocumentType(documentType)];

const getAllPartsFromJobs = resolvedJobs =>
  _flatMap(resolvedJobs, job => {
    const operations = getOperations(job);
    return _reduce(operations, (acc, operation) => [...acc, ...ROOperationReader.parts(operation)], []);
  });

const getValidPartDetails = partsInfo => {
  const nonDeletedPartsInfo = _filter(partsInfo, part => !ROPartAvailabilityReader.deleted(part));
  return _groupBy(nonDeletedPartsInfo, item => tget(item, 'groupId', ''));
};

export const getAllPartsDetails = jobs => {
  const allPartsFromJobs = getAllPartsFromJobs(jobs);
  return getValidPartDetails(allPartsFromJobs);
};

export const getAllPartsDetailsForMultiPayer = jobs => {
  const allPartsFromJobs = getAllPartsFromJobs(jobs);
  const partDetails = getValidPartDetails(allPartsFromJobs);
  return {
    partDetails,
    partByPartFulfilmentLineItemId: _keyBy(allPartsFromJobs, PARTS_FULFILLMENT_LINE_ITEM_ID),
  };
};

// TODO: Integrate API with Service2.0
export const getDeferRecallsDetailsFromMandatoryAssetInfo = mandatoryAssetInfo => {
  const deferRecallsDetailsAssetInfo = _head(
    _filter(mandatoryAssetInfo, assetInfo => MandatoryAssetInfoReader.assetType(assetInfo) === ASSET_TYPES.RECALL)
  );
  if (_isEmpty(deferRecallsDetailsAssetInfo)) return EMPTY_ARRAY;
  const recallDetails = MandatoryAssetInfoReader.recallDetails(deferRecallsDetailsAssetInfo);
  const time = MandatoryAssetInfoReader.time(deferRecallsDetailsAssetInfo);
  const userId = MandatoryAssetInfoReader.userId(deferRecallsDetailsAssetInfo);
  return _map(recallDetails, recall => ({
    [DEFERRED_RECALLS_TABLE_COLUMNS.RECALL_NUMBER]: MandatoryAssetInfoReader.recallId(recall),
    [DEFERRED_RECALLS_TABLE_COLUMNS.DEFERRED_BY]: userId,
    [DEFERRED_RECALLS_TABLE_COLUMNS.DATE_OF_DEFERRAL]: time,
    [DEFERRED_RECALLS_TABLE_COLUMNS.REASON_FOR_DEFERRAL]: MandatoryAssetInfoReader.reason(recall),
    [RECALL_DESCRIPTION]: MandatoryAssetInfoReader.description(recall),
  }));
};

export const getFeeOrCouponAmount = ({
  entity,
  payerId,
  copyType,
  entityReader,
  shouldDisplayOtherPayerContribution,
  isAdditionalFeesOrCoupons,
}) => {
  const basePayType = ROReader.payType(entity);
  const entityPayerId = ROReader.payerId(entity);

  if (ROConstraints.isServiceV3Enabled()) {
    if (
      (isAdditionalFeesOrCoupons && entityPayerId === payerId) ||
      _isNil(payerId) ||
      (!isAdditionalFeesOrCoupons &&
        ((COPY_TYPES_VS_SUPPORTED_BASE_PAY_TYPES_SET_FOR_V3[copyType] || EMPTY_SET).has(basePayType) ||
          shouldDisplayOtherPayerContribution))
    ) {
      return getFormattedCurrency(MoneyReader.amount(entityReader(entity)));
    }
    return getFormattedCurrency(0);
  }

  const feeOrCouponAmountValue =
    (COPY_TYPES_VS_SUPPORTED_BASE_PAY_TYPES_SET[copyType] || EMPTY_SET).has(basePayType) ||
    (isAdditionalFeesOrCoupons && entityPayerId === payerId) ||
    _isNil(payerId)
      ? MoneyReader.amount(entityReader(entity))
      : 0;
  return getFormattedCurrency(feeOrCouponAmountValue);
};

export const getPdfFormattedTaxCode = taxCode =>
  _truncate(taxCode, {
    length: TAX_CODE_DISPLAY_MAX_LENGTH,
  });

export const getTaxLabel = (taxType, taxLabelType) =>
  _isEmpty(taxLabelType) && _isEmpty(taxType)
    ? __('Tax')
    : __('Tax - {{taxLabelType}}', { taxLabelType: taxLabelType || taxType });

export const getShowRedeemableRewards = (redeemableRewards, showRedeemableRewards) =>
  redeemableRewards !== GM_REDEMPTION_INFO_API_FALLBACK_VALUE && showRedeemableRewards;

const DOC_TYPE_VS_SNAPSHOT_DETAILS_REQUEST_OPTIONS_GETTER = {
  [DOCUMENT_TYPES.RO_CANCELLED_INVOICE]: [
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.DIFFERENCE,
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.COMPONENT_CALCULATIONS,
  ],
  [DOCUMENT_TYPES.RO_ACCOUNTING_INVOICE]: [
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.POSTING_INFO,
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.COMPONENT_CALCULATIONS,
  ],
  [DOCUMENT_TYPES.RO_ACCOUNTING_CANCELLED_INVOICE]: [
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.DIFFERENCE,
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.POSTING_INFO,
  ],
  [DOCUMENT_TYPES.RO_PAYER_INVOICE]: [
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.PAYER_INFO,
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.INTEGRATION_CLAIM_DETAILS,
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.CHORUS_PRO_PAYER_FIELDS,
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.COMPONENT_CALCULATIONS,
  ],
  [DOCUMENT_TYPES.RO_DEDUCTIBLE_PAYER_INVOICE]: [
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.PAYER_INFO,
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.INTEGRATION_CLAIM_DETAILS,
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.COMPONENT_CALCULATIONS,
  ],
  [DOCUMENT_TYPES.RO_PROOF_OF_WORK]: [
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.PAYER_INFO,
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.INTEGRATION_CLAIM_DETAILS,
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.COMPONENT_CALCULATIONS,
  ],
  [DOCUMENT_TYPES.RO_PAYER_CANCELLED_INVOICE]: [
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.PAYER_INFO,
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.INTEGRATION_CLAIM_DETAILS,
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.COMPONENT_CALCULATIONS,
  ],
  [DOCUMENT_TYPES.RO_RECYCLAGE_PAYER_INVOICE]: [
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.PAYER_INFO,
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.COMPONENT_CALCULATIONS,
  ],
  [DOCUMENT_TYPES.RO_ESTIMATE]: [
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.PAYER_INFO,
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.ADDITIONAL_CUSTOMER_DETAILS,
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.INTEGRATION_CLAIM_DETAILS,
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.COMPONENT_CALCULATIONS,
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.APPOINTMENT_DETAILS,
  ],
  [DOCUMENT_TYPES.RO_INSPECTIONS_AND_RECOMMENDATIONS]: [
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.POSTING_INFO,
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.ADDITIONAL_CUSTOMER_DETAILS,
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.COMPONENT_CALCULATIONS,
  ],
  [DOCUMENT_TYPES.RO_QUOTE]: [
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.PAYER_INFO,
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.ADDITIONAL_CUSTOMER_DETAILS,
    SNAPSHOT_DETAILS_REQUEST_OPTIONS.COMPONENT_CALCULATIONS,
  ],
};

export const getSnapshotDetailsPayload = (snapshotId, documentType, parentAssetId) => ({
  snapshotId,
  documentType,
  parentAssetId,
  requestOptions: DOC_TYPE_VS_SNAPSHOT_DETAILS_REQUEST_OPTIONS_GETTER[documentType] || EMPTY_ARRAY,
});

export const getParsedPostingInfo = posting => ({
  ...posting,
  [FIELD_IDS.TRANSACTIONS]: _keyBy(ROSnapshotReader.transactions(posting), FIELD_IDS.POSTING_ASSET_VALUE),
});

export const getShouldShowOperationLevelPrice = (priceConfig, entityId, documentType) => {
  const operationPriceConfig = _get(priceConfig, entityId);
  const opcodeLevelPriceType =
    getOpcodeLevelPriceType(operationPriceConfig, OVERRIDDEN_PRICE_INFO) ||
    getOpcodeLevelPriceType(operationPriceConfig, PRICE_INFO);
  if (documentType !== DOCUMENT_TYPES.RO_ESTIMATE) return false;
  return opcodeLevelPriceType === PRICE_TYPES.ESTIMATE_PRICE || opcodeLevelPriceType === PRICE_TYPES.FIXED_PRICE;
};

const getParsedStaticImageSections = pdfSections => {
  const bodySection = _find(pdfSections, section => PDF_SECTION.BODY === PDFConfigReader.sectionName(section));
  const afterSections = PDFConfigReader.afterSections(bodySection);
  return _filter(afterSections, section => STATIC_IMAGE_SUPPORTED_COMPONENTS_SET.has(PDFConfigReader.key(section)));
};

export const getStaticImageDetailsByKey = pdfSections => {
  const parsedStaticImageSectionList = getParsedStaticImageSections(pdfSections);
  return _reduce(
    parsedStaticImageSectionList,
    (acc, staticImageSection) => {
      const value = PDFConfigReader.value(staticImageSection);
      const sectionMetadata = ROPDFSectionPropertyReader.sectionMetadata(value);
      const sectionKey = PDFConfigReader.key(staticImageSection);
      return {
        ...acc,
        [sectionKey]: {
          [SECTION_METADATA_FIELDS.SHOULD_BREAK_PAGE_BEFORE_SECTION]:
            ROPDFSectionPropertyReader.shouldBreakPageBeforeSection(sectionMetadata),
          [SECTION_METADATA_FIELDS.WIDTH]: ROPDFSectionPropertyReader.width(sectionMetadata),
          [SECTION_METADATA_FIELDS.HEIGHT]: ROPDFSectionPropertyReader.height(sectionMetadata),
          [URL]: `${CDN_STATIC_ASSET_URL}/static/media/${STATIC_IMAGE_FILENAME_MAP[sectionKey]}`,
        },
      };
    },
    EMPTY_OBJECT
  );
};

export const getPdfConfigurationListWithTargetLanguageValues = pdfConfigList => {
  const locale = getCurrentUserPreferredLanguage();
  return _map(pdfConfigList, pdfConfiguration => getPdfConfigurationWithTargetLanguageValues(pdfConfiguration, locale));
};

export const getPdfConfigRequestPayload = (payload, fallbackPayloadList) => {
  const requestPayloadList = [payload, ...fallbackPayloadList];
  return _map(requestPayloadList, reqPayload => ({
    departmentIds: _get(reqPayload, 'departmentIds'),
    documentTypes: [_get(reqPayload, 'documentType')],
    copyTypes: [_get(reqPayload, 'copyType')],
    subTypes: _compact([_get(reqPayload, 'subType')]),
  }));
};

export const getClaimDetailsTotals = ({ repairOrder, payerId: sourcePayerId, targetPayerId, documentType }) => {
  const deductibles = ROReader.getDeductibles(repairOrder);
  const deductiblesGroupedByPayer = _groupBy(deductibles, deductible => {
    const deductibleSourcePayer = PayerDeductibleReader.getSourcePayerId(deductible);
    const deductibleTargetPayer = PayerDeductibleReader.getTargetPayerId(deductible);
    return documentType === DOCUMENT_TYPES.RO_DEDUCTIBLE_PAYER_INVOICE
      ? `${deductibleSourcePayer}_${deductibleTargetPayer}`
      : deductibleSourcePayer;
  });
  const payerKeyToSearch =
    documentType === DOCUMENT_TYPES.RO_DEDUCTIBLE_PAYER_INVOICE ? `${sourcePayerId}_${targetPayerId}` : sourcePayerId;
  const deductiblesForPayer = _get(deductiblesGroupedByPayer, payerKeyToSearch, EMPTY_ARRAY);
  return _reduce(
    deductiblesForPayer,
    (deductibleTotals, deductible) => {
      const deductibleDetails = PayerDeductibleReader.getDeductibleDetails(deductible);
      const amount = PayerDeductibleReader.getCustomerComponentAmount(deductibleDetails);
      const deductibleAmount = PayerDeductibleReader.getDeductibleAmount(deductibleDetails);
      const additionalDetails = PayerDeductibleReader.getAdditionalDetails(deductibleDetails);
      const deprecationAmount = PayerDeductibleReader.getDepreciationAmount(additionalDetails);
      const customerTaxAmount = PayerDeductibleReader.getCustomerTaxAmount(additionalDetails);
      const aChargeAmount = PayerDeductibleReader.getAChargeAmount(additionalDetails);
      return {
        amount: MoneyUtils.add(PayerDeductibleReader.getCustomerComponentAmount(deductibleTotals), amount),
        deductibleAmount: MoneyUtils.add(PayerDeductibleReader.getDeductibleAmount(deductibleTotals), deductibleAmount),
        deprecationAmount: MoneyUtils.add(
          PayerDeductibleReader.getDepreciationAmount(deductibleTotals),
          deprecationAmount
        ),
        customerTaxAmount: MoneyUtils.add(
          PayerDeductibleReader.getCustomerTaxAmount(deductibleTotals),
          customerTaxAmount
        ),
        aChargeAmount: MoneyUtils.add(PayerDeductibleReader.getAChargeAmount(deductibleTotals), aChargeAmount),
      };
    },
    EMPTY_OBJECT
  );
};

export const getPartUnitOfMeasurementsById = partUnitOfMeasurements => _keyBy(partUnitOfMeasurements, ID);

export const getPayerContributedJobIdsSet = (jobLevelTotals, payerIdsList, shouldIncludeAllJobs) => {
  const payerIdsSet = new Set(payerIdsList);
  return _reduce(
    jobLevelTotals,
    (acc, total) => {
      const curPayerId = ROReader.payerId(total);
      const jobId = ROReader.jobId(total);
      if (shouldIncludeAllJobs || payerIdsSet.has(curPayerId)) acc.add(jobId);
      return acc;
    },
    new Set()
  );
};

export const getJobsAndJobLevelTotals = roDetails => {
  const jobs = ROSnapshotReader.jobs(roDetails);
  const jobCalculationResponse = ROSnapshotReader.jobCalculationResponse(roDetails);
  const totals = ROReader.totals(jobCalculationResponse);
  const jobLevelTotals = ROTotalsReader.jobLevelTotals(totals);

  return { entity: jobs, entityLevelTotals: jobLevelTotals };
};

const getRecommendationsAndRecommendationLevelTotals = roDetails => {
  const recommendations = ROSnapshotReader.recommendations(roDetails);
  const recommendationCalculationResponse = ROSnapshotReader.recommendationCalculationResponse(roDetails);
  const totals = ROReader.totals(recommendationCalculationResponse);
  const recommendationLevelTotals = ROTotalsReader.jobLevelTotals(totals);

  return { entity: recommendations, entityLevelTotals: recommendationLevelTotals };
};

const GET_ENTITY_AND_ENTITY_LEVEL_TOTALS = {
  [ENTITY_TYPES.JOB]: getJobsAndJobLevelTotals,
  [ENTITY_TYPES.RECOMMENDATION]: getRecommendationsAndRecommendationLevelTotals,
};

const DOCUMENT_TYPE_VS_PAYERS_GETTER = {
  [DOCUMENT_TYPES.RO_PROOF_OF_WORK]: ({ roDetails, payerId: primaryPayerId, selectedClientCategories }) => {
    const payerDetails = ROSnapshotReader.payerDetails(roDetails);
    return _reduce(
      payerDetails,
      (payerIds, payer) => {
        const payerId = _get(payer, PAYER_ID);
        const clientCategoryId = ROSnapshotReader.clientCategoryId(payer);
        if (payerId === primaryPayerId || (clientCategoryId && !_includes(selectedClientCategories, clientCategoryId)))
          return payerIds;
        return [...payerIds, payerId];
      },
      EMPTY_ARRAY
    );
  },
  DEFAULT: ({ payerId }) => (payerId ? [payerId] : EMPTY_ARRAY),
};

export const getParsedJobsByPayerContribution = ({
  roDetails,
  payerId,
  entityType = ENTITY_TYPES.JOB,
  documentType,
  selectedClientCategories,
  shouldIncludeAllJobs,
}) => {
  const { entity, entityLevelTotals } = _get(GET_ENTITY_AND_ENTITY_LEVEL_TOTALS, entityType)(roDetails);
  const funcToExec = DOCUMENT_TYPE_VS_PAYERS_GETTER[documentType] || DOCUMENT_TYPE_VS_PAYERS_GETTER.DEFAULT;
  const payersToGetJobFor = funcToExec({ roDetails, payerId, selectedClientCategories });
  const payerContributedJobIdsSet = getPayerContributedJobIdsSet(
    entityLevelTotals,
    payersToGetJobFor,
    shouldIncludeAllJobs
  );
  return _reduce(
    entity,
    (acc, job) => {
      const jobId = ROJobReader.getJobId(job);
      if ((payerContributedJobIdsSet || EMPTY_SET).has(jobId)) return [...acc, job];
      return acc;
    },
    EMPTY_ARRAY
  );
};

export const getLastBodySectionKey = pdfConfig => {
  const pdfSections = PDFConfigReader.pdfSections(pdfConfig);
  const pdfSectionsByKey = _keyBy(pdfSections, SECTION_NAME);
  const bodySection = _get(pdfSectionsByKey, PDF_SECTION.BODY);
  return bodySection?.lastBodySectionKey;
};

export const getAvailableCombinedKeys = ({ keysToCombine, seperator }) => _join(_compact(keysToCombine), seperator);

export const getPrimaryCustomerSubPayType = ({ payerId, repairOrder }) => {
  const payers = ROReader.payers(repairOrder);
  const payersById = _keyBy(payers, ROReader.payerId);
  return _get(payersById, [payerId, SUB_PAY_TYPE]);
};

export const getFleetBoxData = roDetails => {
  const fleetBoxClaimDetails = ROSnapshotReader.fleetBoxClaimDetails(roDetails);
  const fleetBoxExternalDetails = _get(fleetBoxClaimDetails, EXTERNAL_DETAILS_KEY, EMPTY_OBJECT);
  const fleetBoxAgreementNumber = FleetBoxReader.agreementNo(fleetBoxExternalDetails);
  const athorisReferenceNumber = FleetBoxReader.referenceNo(fleetBoxExternalDetails);
  return { fleetBoxAgreementNumber, athorisReferenceNumber };
};

const getGeneralTermsImgUrl = ({ assetUrlDetailsByMake, makeId = TNC_SECTION_DEFAULT_MAKE_ID, pageNo }) =>
  `${CDN_STATIC_ASSET_URL}/${_get(assetUrlDetailsByMake, [makeId, 'path'])}-${pageNo}.svg`;

const getMakeIdFromRepairOrder = repairOrder => {
  const vehicleInfo = ROReader.getVehicleInfoFromRepairOrder(repairOrder);
  const makeId = VehicleReader.makeId(vehicleInfo);
  return makeId;
};

export const getGeneralTermsImgUrlsFromConfig = (repairOrder, pdfSections) => {
  const generalSectionConfig = _find(
    pdfSections,
    pdfSection => PDFConfigReader.sectionName(pdfSection) === PDF_SECTION.GENERAL_TERMS
  );
  const sectionProperties = getSectionProperties(generalSectionConfig) || EMPTY_ARRAY;
  const sectionProperty = _head(sectionProperties) || EMPTY_OBJECT;
  const makeId = getMakeIdFromRepairOrder(repairOrder);
  const assetUrlDetailsByMake = _keyBy(PDFConfigReader.value(sectionProperty), MAKE_ID);
  return [
    getGeneralTermsImgUrl({ assetUrlDetailsByMake, makeId, pageNo: TNC_FIRST_PAGE_NO }),
    getGeneralTermsImgUrl({ assetUrlDetailsByMake, makeId, pageNo: TNC_SECOND_PAGE_NO }),
  ];
};

export const getVehicleColorFromVehicleProfile = vehicleProfileDetails => {
  const vehicleData = getVehicleData(vehicleProfileDetails);
  return (
    VehicleReader.consolidatedExteriorColor(vehicleData) ||
    getExteriorColor(vehicleData) ||
    getColor(vehicleData) ||
    getManufacturerColorCode(vehicleData)
  );
};

export const getAppointmentTakerUserId = (parsedSnapshotResponse = EMPTY_OBJECT) => {
  const { appointmentDetails = EMPTY_OBJECT } = parsedSnapshotResponse;
  return BDCAppointmentReader.appointmentTakerUserId(appointmentDetails);
};

export const getShouldShowSurchargeDetails = ({ totalAmount = MINIMUM_SURCHARGE_AMOUNT, copyType }) => {
  const { cashieringSettings } = RepairOrderEnv;
  const maximumSurchargePercentageInfo = CashieringSettingsReader.maximumSurchargePercentageInfo(cashieringSettings);
  if (
    _toNumber(totalAmount) < MINIMUM_SURCHARGE_AMOUNT ||
    copyType !== COPY_TYPES.CUSTOMER ||
    !shouldShowCreditSurchargeInfo(maximumSurchargePercentageInfo)
  )
    return false;
  return true;
};

export const getTransformedSnapshotResponse = (snapshotResponse, shouldCallTransientSnapshot) => {
  if (ROConstraints.isServiceV3Enabled() && shouldCallTransientSnapshot)
    return {
      snapshot: snapshotResponse,
      extraData: EMPTY_OBJECT,
    };
  return snapshotResponse;
};

export const getDisclosure = currentPDFVariantConfig => {
  if (!ROConstraints.isESignServiceIntegrationEnabled()) {
    return EMPTY_ARRAY;
  }
  const pdfSections = _get(currentPDFVariantConfig, PDF_SECTIONS);
  const bodySection = _get(_keyBy(pdfSections, SECTION_NAME), PDF_SECTION.BODY);
  const sectionProperties = getSectionProperties(bodySection);
  const disclosureSectionList = ROConstraints.isNewESignEnhancementEnabled() ? DISCLOSURE_SECTION_LIST : _without(DISCLOSURE_SECTION_LIST, BODY_COMPONENTS.CUSTOMER_SIGNATURE);
  const disclosures = _filter(
    sectionProperties,
    section => disclosureSectionList.has(_get(section, KEY)) && tget(section, RENDER, false)
  );
  return _flatMap(disclosures, disclosure => _get(disclosure, VALUE, EMPTY_ARRAY));
};

export const areExperienceEngineEntityConstraintsSatisfied = ({ getFeatureValue = _noop, entityConstraints }) =>
  _every(entityConstraints, getFeatureValue);

export const getFilterPayload = vin => ({
  searchAndAggregationRequest: {
    filters: [
      {
        field: 'additionalDetail.vehicleInfo.VIN',
        operator: 'IN',
        values: [`${vin}`],
      },
      {
        field: 'status',
        operator: 'NIN',
        values: [RO_STATUS_VALUES.VOID],
      },
    ],
    sort: [
      {
        key: 'byCreatedTime',
        field: 'createdTime',
        order: 'DESC',
      },
    ],
    pageInfo: {
      start: 0,
      rows: 200,
    },
  },
  requestOptions: [ENTITY_TYPES.JOB],
});
