import React from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';

import _map from 'lodash/map';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _noop from 'lodash/noop';
import _find from 'lodash/find';
import _isEqual from 'lodash/isEqual';

import { withTekionConversion, FormattedCurrency } from '@tekion/tekion-conversion-web';
import HelperText from 'tcomponents/atoms/HelperText';
import PartDetailsTable from 'exports/Components/PartDetails';
import TechnicianTable from 'exports/Components/TechnicianTable/TechnicianTable';
import OemOpcodeTable from 'exports/Components/OemOpcodeTable/OemOpcodeTable';
import PropertyControlledComponent from 'tcomponents/molecules/PropertyControlledComponent';
import BadgeWrapper from 'tcomponents/atoms/BadgeWrapper/BadgeWrapper';
import ReturnPartsTable from 'exports/Components/ReturnPartsTable';

import pdfSettingsReader from 'tbase/readers/pdfSettings.reader';
import ROPartReader from 'readers/ROPart.reader';
import ROPartAvailabilityReader from 'tbusiness/appServices/service/readers/ROPartAvailability';
import ExportsReader from 'exports/exports.readers';
import ROOperationReader from 'tbusiness/appServices/service/readers/ROOperation';
import MoneyReader from 'tbase/readers/TMoney';
import { getUpdatedPartDisplayName } from 'twidgets/appServices/service/organisms/PartsAvailabilityTable/partsAvailabilityTable.helpers';
import { isPartLineTypeReturn } from 'tbusiness/appServices/service/utils/partAvailability';
import { getOpCodeText, getOpcodeDisplayValue } from 'helpers/opcode.helper';
import ROConstraints from 'helpers/constraints';
import { getIndicateFieldValue, getTimeInHrs } from 'exports/exports.helper';

import { EMPTY_ARRAY, EMPTY_STRING, EMPTY_OBJECT } from 'tbase/app.constants';
import { COPY_TYPES, DOCUMENT_TYPES } from 'tbase/constants/roPdf.constants';
import { PAY_TYPE_VALUES } from 'tbase/constants/payTypes';
import { TECHNICIAN_TABLE_CELL_CONFIG } from 'exports/ROInvoiceNew/ROInvoiceNew.constants';
import { TEST_SELECTORS } from 'exports/exports.constants';
import { TAX_CODE_SPLIT_TYPES } from 'tbusiness/appServices/service/constants/tax';
import { getActualPrice } from 'organisms/ROForm/Components/OpCode/OpCodeForm.helpers';

import exportStyles from 'exports/exports.module.scss';
import variables from 'tstyles/exports.scss';

import SORTable from '../../../SORTable';
import ApprovalNotes from '../ApprovalNotes';

import roInvoiceReader from '../../../../ROInvoiceNew/ROInvoice.reader';

import { isMultiOemOpcodeApplicable } from '../../JobDetails.helpers';
import {
  getTechnicianTableBreakdown,
  getFormattedPartDetails,
  getFormattedSorPartTableData,
  getPartDetailsTableCellConfig,
  getTechnicianTableColumns,
  getPartTableColumns,
  getFormattedReturnPartsTableData,
  shouldNotShowPartDetails,
  getSanitizedWarrantyOemOpcodeDetails,
  shouldRenderInspectionStoryLines,
  getFilteredStoryLinesByType,
  getOperationLevelBreakupAmount,
  getPartLineDescription,
  getOperationOrLaborTaxCode,
  getPartPrice,
  shouldRenderTechniciansTable,
} from './Operations.helpers';
import { PARTS_TAG } from './Operations.constants';

import s from '../../jobDetails.module.scss';
import styles from './operations.module.scss';

const USED_VEHICLE_BADGE_STYLE = { backgroundColor: variables.tekionGreen, width: '4rem', marginLeft: '1rem' };
const RETURN_PARTS_BADGE_STYLE = { backgroundColor: variables.denim, marginLeft: '1rem' };

class Operations extends React.PureComponent {
  renderStoryLines = storyLines =>
    _map(storyLines, (storyLine, index) => (
      <HelperText
        sectiontype="advanced-general-section"
        type="exportText"
        className={cx(styles.operationsContent, styles.justifyText)}>
        <span className={s.textNoWrap}>{`${index + 1}. `}</span>
        {_get(storyLine, 'text', EMPTY_STRING)}
      </HelperText>
    ));

  renderInspectionLines = inspectionLines => (
    <div sectiontype="container-section">
      <HelperText sectiontype="container-header" type="exportText" highlight className={styles.partsHeading}>
        {__('Inspection/Diagnostic')}
      </HelperText>
      {this.renderStoryLines(inspectionLines)}
    </div>
  );

  renderPartDetails = (partDetails, shouldDisplayPartPrices, partTaxCodeByComponentId) => {
    const {
      copyType,
      showPartsPrices,
      showPartNote,
      showSORPartsTable,
      showReturnPartsTable,
      documentType,
      showPartNumber,
      showOperationTotalPartsCostPrice,
      shouldNotDisplayPrice,
      partUnitOfMeasurementsById,
      showTaxCode,
      roLevelComponentTotals,
      payerId,
    } = this.props;
    const partBreakdownByComponentId = ROConstraints.isServiceV3Enabled()
      ? _get(roLevelComponentTotals, 'PARTS', EMPTY_OBJECT)
      : EMPTY_OBJECT;

    if (copyType === COPY_TYPES.ACCOUNTING)
      return this.renderPartDetailsTable({
        partDetails,
        showPartNote,
        showPartsPrices,
        showOperationTotalPartsCostPrice,
        partBreakdownByComponentId,
        payerId,
      }); // SOR support for accounting copy will be added in next phase

    return _map(partDetails, item => {
      const sorParts = ROPartReader.sorParts(item);
      const hasSorParts = !_isEmpty(sorParts);
      const returnPart = roInvoiceReader.getReturnPart(item);
      const hasReturnPart = !_isEmpty(returnPart);
      const partId = ROPartReader.id(item);
      const partSplitDetails = _get(partBreakdownByComponentId, partId, EMPTY_OBJECT);
      const partSplitAmountDetails =
        _find(_get(partSplitDetails, 'postSplits', EMPTY_ARRAY), { payerId }) || EMPTY_OBJECT;
      const partPrice = getPartPrice({
        part: item,
        shouldNotDisplayPrice,
        documentType,
        partSplitAmountDetails,
      });
      const partName = getUpdatedPartDisplayName(item, showPartNumber);
      const approvedQty = isPartLineTypeReturn(item)
        ? ROPartAvailabilityReader.effectiveSaleQty(item)
        : roInvoiceReader.partQuantity(item);
      const partComment = ROPartReader.dealerComment(item);
      const partUnitOfMeasureDetails = partUnitOfMeasurementsById[ROPartReader.unitOfMeasureId(item)];
      const partLineDescription = getPartLineDescription({
        showPartsPrices: ROConstraints.isServiceV3Enabled() || shouldDisplayPartPrices,
        partName,
        partUnitOfMeasure: ROPartReader.unitOfMeasureName(partUnitOfMeasureDetails),
        approvedQty,
        partPrice,
        partTaxCodeByComponentId,
        partId,
        showTaxCode,
      });
      if (shouldNotShowPartDetails(partName, documentType, returnPart)) return null;

      return (
        <>
          <div sectiontype="general-section">
            <div className={cx(styles.partsLabelWithTag, styles.operationsContent)}>
              <HelperText type="exportText" testId={TEST_SELECTORS.PART_DETAIL}>
                {partLineDescription}
              </HelperText>
              <PropertyControlledComponent testId={TEST_SELECTORS.SOR_PART_TABLES} controllerProperty={hasSorParts}>
                <BadgeWrapper badgeContent={PARTS_TAG.SOR} style={USED_VEHICLE_BADGE_STYLE} />
              </PropertyControlledComponent>
              <PropertyControlledComponent
                testId={TEST_SELECTORS.SOR_PART_TABLES}
                controllerProperty={documentType === DOCUMENT_TYPES.RETURN_PARTS && hasReturnPart}>
                <BadgeWrapper badgeContent={PARTS_TAG.RETURN} style={RETURN_PARTS_BADGE_STYLE} />
              </PropertyControlledComponent>
            </div>
            <PropertyControlledComponent controllerProperty={showPartNote && partComment}>
              <HelperText
                testId={TEST_SELECTORS.PART_NOTES}
                type="exportText"
                className={cx(exportStyles.partComment, styles.operationsContent)}>
                {__('Part Note: {{partComment}}', { partComment })}
              </HelperText>
            </PropertyControlledComponent>
          </div>
          <PropertyControlledComponent
            testId={TEST_SELECTORS.SOR_PART_TABLES}
            controllerProperty={hasSorParts && showSORPartsTable}>
            <SORTable data={getFormattedSorPartTableData(sorParts)} />
            <div sectiontype="general-section" className={styles.partsTable} />
          </PropertyControlledComponent>
          <PropertyControlledComponent
            testId={TEST_SELECTORS.RETURN_PARTS_TABLE}
            controllerProperty={hasReturnPart && showReturnPartsTable}>
            <ReturnPartsTable data={getFormattedReturnPartsTableData(partName, returnPart)} />
            <div sectiontype="general-section" className={styles.partsTable} />
          </PropertyControlledComponent>
        </>
      );
    });
  };

  renderTechnicianTable = ({ techIdWithBillingTimes, operation, techIndicateFieldDetails, isOfTypeCustomerPay }) => {
    const {
      resolvedRODetails,
      pdfSettings,
      employeeList,
      resolvedClockInDetails,
      showLaborSaleCostForTechnician,
      notCustomerCopy,
      showTechnicianEmployeeCertificationNumber,
      showOperationTotalLaborCostPrice,
      getFormattedNumber,
      job,
    } = this.props;
    const showTechnicianNumberOnly = pdfSettingsReader.shouldShowTechnicianEmployeeNumberOnly(pdfSettings);
    const tableColumns = getTechnicianTableColumns({
      notCustomerCopy,
      showLaborSaleCostForTechnician,
      showTechnicianEmployeeCertificationNumber,
      showOperationTotalLaborCostPrice,
      isOfTypeCustomerPay,
    });
    return (
      !_isEmpty(techIdWithBillingTimes) && (
        <React.Fragment>
          <TechnicianTable
            techDetails={getTechnicianTableBreakdown({
              techIdWithBillingTimes,
              operation,
              resolvedRODetails,
              showTechnicianNumberOnly,
              employeeList,
              resolvedClockInDetails,
              getFormattedNumber,
              job,
              indicateFieldDetails: techIndicateFieldDetails,
            })}
            cellConfig={TECHNICIAN_TABLE_CELL_CONFIG}
            columns={tableColumns}
          />
          <div sectiontype="general-section" className={styles.partsTable} />
        </React.Fragment>
      )
    );
  };

  renderPartDetailsTable = ({
    partDetails,
    showPartNote,
    showPartsPrices,
    showOperationTotalPartsCostPrice,
    partBreakdownByComponentId,
    payerId,
  }) => (
    <React.Fragment>
      <PartDetailsTable
        partDetails={getFormattedPartDetails(partDetails, partBreakdownByComponentId, payerId)}
        cellConfig={getPartDetailsTableCellConfig(showPartNote)}
        columns={getPartTableColumns(showPartsPrices, showOperationTotalPartsCostPrice)}
      />
      <div sectiontype="general-section" className={styles.partsTable} />
    </React.Fragment>
  );

  renderOperationLaborPartsLineInfo = ({
    showPrice,
    label,
    price,
    isLaborContainer = false,
    taxCode,
    isIndicateFieldPriceChanged,
    showTaxCode,
  }) =>
    showPrice && (
      <div
        sectiontype="general-section"
        className={cx({
          [styles.totalHeaderContainer]: !isLaborContainer,
          [styles.laborContainer]: isLaborContainer,
        })}>
        {isLaborContainer ? (
          <div className={styles.laborSubContainer}>
            <HelperText sectiontype="general-section" type="exportText" highlight>
              {label}: {getIndicateFieldValue(roInvoiceReader.getFormattedCurrency(price), isIndicateFieldPriceChanged)}
            </HelperText>
          </div>
        ) : (
          <div className={styles.laborSubContainer}>
            <HelperText sectiontype="general-section" type="exportText" highlight>
              {label}
            </HelperText>
            <HelperText sectiontype="general-section" className={styles.operationPrice} type="exportText">
              {getIndicateFieldValue(roInvoiceReader.getFormattedCurrency(price), isIndicateFieldPriceChanged)}
            </HelperText>
          </div>
        )}

        {taxCode && showTaxCode && (
          <HelperText sectiontype="general-section" type="exportText" highlight>
            {taxCode}
          </HelperText>
        )}
      </div>
    );

  renderOperationTotalPartsInfo = ({
    showOperationTotalPartsPrice,
    showOperationTotalPartsCostPrice,
    showPartDetails,
    totalPartsPrice,
    totalPartsCostPrice,
    isCostPriceSupportedCopyTypes,
  }) => {
    if (!isCostPriceSupportedCopyTypes) {
      return (
        <div sectiontype="general-section" className={styles.totalHeaderContainer}>
          {(showOperationTotalPartsPrice || showPartDetails) && (
            <HelperText type="exportText" highlight>
              {__('Parts')}
            </HelperText>
          )}
          {showOperationTotalPartsPrice && (
            <div className={cx(styles.operationPrice)}>
              <HelperText type="exportText">{roInvoiceReader.getFormattedCurrency(totalPartsPrice)}</HelperText>
            </div>
          )}
        </div>
      );
    }
    return (
      <div sectiontype="general-section" className={styles.laborPartsInfoContainer}>
        {(showOperationTotalPartsPrice || showOperationTotalPartsCostPrice || showPartDetails) && (
          <HelperText className={styles.partsHeading} type="exportText" highlight>
            {__('Parts')}
          </HelperText>
        )}
        {this.renderOperationLaborPartsLineInfo({
          showPrice: showOperationTotalPartsPrice,
          label: __('Parts Total Sale Amount'),
          price: totalPartsPrice,
        })}
        {this.renderOperationLaborPartsLineInfo({
          showPrice: showOperationTotalPartsCostPrice,
          label: __('Parts Total Cost Amount'),
          price: totalPartsCostPrice,
        })}
      </div>
    );
  };

  renderOperationLaborInfo = ({
    showOperationTotalLaborPrice,
    showOperationTotalLaborCostPrice,
    isCostPriceSupportedCopyTypes,
    operationLaborPrice,
    operationLaborCostPrice,
    isLaborPriceChanged,
    isLaborCostPriceChanged,
    laborTaxCode,
    showTaxCode,
  }) => {
    if (!isCostPriceSupportedCopyTypes) {
      return (
        <div sectiontype="general-section" className={styles.laborPartsInfoContainer}>
          {this.renderOperationLaborPartsLineInfo({
            showPrice: showOperationTotalLaborPrice,
            label: __('Labor'),
            price: operationLaborPrice,
            isLaborContainer: true,
            taxCode: laborTaxCode,
            showTaxCode,
          })}
        </div>
      );
    }
    return (
      <div sectiontype="general-section" className={styles.laborPartsInfoContainer}>
        {this.renderOperationLaborPartsLineInfo({
          showPrice: showOperationTotalLaborPrice,
          label: __('Labor Sale'),
          price: operationLaborPrice,
          isLaborContainer: true,
          taxCode: laborTaxCode,
          isIndicateFieldPriceChanged: isLaborPriceChanged,
          showTaxCode,
        })}
        {this.renderOperationLaborPartsLineInfo({
          showPrice: showOperationTotalLaborCostPrice,
          label: __('Labor Cost'),
          price: operationLaborCostPrice,
          isLaborContainer: true,
          isIndicateFieldPriceChanged: isLaborCostPriceChanged,
        })}
      </div>
    );
  };

  renderOemOpcodeDetailsTable = (operation, isWarrantyCopyType, getFormattedNumber) => {
    const warrantyOemOpcodeDetails = ROOperationReader.warrantyOemOpcodesWithLaborDetails(operation);
    if (_isEmpty(warrantyOemOpcodeDetails)) return null;
    const updatedWarrantyOpcodeDetails = getSanitizedWarrantyOemOpcodeDetails(
      warrantyOemOpcodeDetails,
      getFormattedNumber
    );
    return (
      <>
        <OemOpcodeTable oemOpcodeData={updatedWarrantyOpcodeDetails} isWarrantyCopyType={isWarrantyCopyType} />
        <div sectiontype="general-section" className={styles.partsTable} />
      </>
    );
  };

  renderOperations = ({ operation, index, indicateFieldDetails, taxCodeByComponentType }) => {
    const { opcodeDescription, storyLines, id, billingTimeInSeconds, techIdWithBillingTimes } = operation;

    const {
      showOperationTotalLaborPrice,
      showOperationTotalPartsPrice,
      showOperationTotalLaborCostPrice,
      showOperationTotalPartsCostPrice,
      showPartDetails,
      notCustomerCopy,
      copyType,
      pdfSettings,
      showPartsPrices,
      documentType,
      showApprovalNotes,
      resolvedROApprovalSystemComments,
      jobId,
      isCostPriceSupportedCopyTypes,
      isWarrantyCopyType,
      shouldNotDisplayPrice,
      jobBreakupDetailsForMultiPayer,
      getFormattedNumber,
      partDetails,
      showOperationBillHrs,
      showTaxCode,
      showOperationTotalPrice,
      basePayType,
    } = this.props;

    const operationTaxCodeByComponentId = _get(taxCodeByComponentType, TAX_CODE_SPLIT_TYPES.OPERATION);
    const laborTaxCodeByComponentId = _get(taxCodeByComponentType, TAX_CODE_SPLIT_TYPES.LABOR);
    const partTaxCodeByComponentId = _get(taxCodeByComponentType, TAX_CODE_SPLIT_TYPES.PARTS);
    const operationTaxCode = getOperationOrLaborTaxCode(operationTaxCodeByComponentId, id);
    const laborTaxCode = getOperationOrLaborTaxCode(laborTaxCodeByComponentId, id);
    const operationPartDetails = _get(partDetails, id, EMPTY_ARRAY);
    const hasPartDetails = !_isEmpty(operationPartDetails);
    const shouldDisplayPartPrices = !shouldNotDisplayPrice && showPartsPrices;
    const isOfTypeCustomerPay =
      _isEqual(copyType, COPY_TYPES.PAYER) && _isEqual(basePayType, PAY_TYPE_VALUES.CUSTOMER_PAY);

    const { operationLaborPrice, operationLaborCostPrice, totalPartsPrice, totalPartsCostPrice } =
      getOperationLevelBreakupAmount({
        shouldNotDisplayPrice,
        operation,
        operationPartDetails,
        copyType,
        documentType,
        operationBreakupDetails: _get(jobBreakupDetailsForMultiPayer, id),
      });

    const billHrs = getIndicateFieldValue(
      `${getFormattedNumber(getTimeInHrs(billingTimeInSeconds))} hrs`,
      ExportsReader.billHrs(indicateFieldDetails)
    );
    const isLaborPriceChanged = ExportsReader.laborSaleAmount(indicateFieldDetails);
    const isLaborCostPriceChanged = ExportsReader.laborCostAmount(indicateFieldDetails);
    const techIndicateFieldDetails = ExportsReader.technicianFields(indicateFieldDetails);

    const [storyLinesWithoutInspectionType, inspectionTypeStoryLines] = getFilteredStoryLinesByType(storyLines);
    // showing technician table condition
    // we just check for invoice-customer pdf from content settings, in future we will remove it also
    // for other pdfs we just check show jobLines( operation ) is true or not , if it's true then only we execute this line
    const shouldRenderTechnicians = shouldRenderTechniciansTable({
      notCustomerCopy,
      documentType,
      pdfSettings,
      isOfTypeCustomerPay,
    });
    const opcodeText = ROConstraints.isMultipleOemOpcodeEnabled()
      ? getOpcodeDisplayValue(operation)
      : getOpCodeText(operation);
    const opCodeTextWithOpIndex = `Op.${index + 1} ${opcodeText} - `;

    return (
      <div sectiontype="container-section" key={id}>
        <div sectiontype="container-header" className={styles.headerContainer}>
          <div className={styles.operationsHeading}>
            <div className={cx(s.flex, styles.operationDescription)}>
              <HelperText className={styles.fitContent} type="exportText" highlight>
                {opCodeTextWithOpIndex}
              </HelperText>
              <HelperText type="exportText">{opcodeDescription}</HelperText>
            </div>
            <div className="d-flex">
              {showOperationTotalPrice && (
                <HelperText sectiontype="general-section" className={styles.operationPrice} type="exportText">
                  <FormattedCurrency value={MoneyReader.value(getActualPrice(operation))} />
                </HelperText>
              )}
              {operationTaxCode && (
                <HelperText sectiontype="general-section" className={styles.laborContainer} type="exportText" highlight>
                  {operationTaxCode}
                </HelperText>
              )}
            </div>
          </div>
          <div className={styles.billLaborContainer}>
            {showOperationBillHrs && (
              <div className={styles.billHrsContainer}>
                <HelperText sectiontype="general-section" type="exportText" highlight>
                  {__('Bill Hrs: {{billHrs}}', {
                    billHrs,
                  })}
                </HelperText>
              </div>
            )}
            {this.renderOperationLaborInfo({
              showOperationTotalLaborPrice,
              showOperationTotalLaborCostPrice,
              isCostPriceSupportedCopyTypes,
              operationLaborPrice,
              operationLaborCostPrice,
              isLaborPriceChanged,
              isLaborCostPriceChanged,
              laborTaxCode,
              showTaxCode,
            })}
          </div>
        </div>
        <PropertyControlledComponent controllerProperty={isMultiOemOpcodeApplicable(documentType)}>
          {this.renderOemOpcodeDetailsTable(operation, isWarrantyCopyType, getFormattedNumber)}
        </PropertyControlledComponent>
        <PropertyControlledComponent
          controllerProperty={shouldRenderTechnicians && documentType !== DOCUMENT_TYPES.RETURN_PARTS}>
          {this.renderTechnicianTable({
            techIdWithBillingTimes,
            operation,
            techIndicateFieldDetails,
            isOfTypeCustomerPay,
          })}
        </PropertyControlledComponent>
        <PropertyControlledComponent controllerProperty={showApprovalNotes}>
          <ApprovalNotes
            resolvedROApprovalSystemComments={resolvedROApprovalSystemComments}
            jobId={jobId}
            operationId={id}
          />
        </PropertyControlledComponent>
        <div sectiontype="container-section" className={styles.contentContainer}>
          {this.renderStoryLines(storyLinesWithoutInspectionType)}
          {shouldRenderInspectionStoryLines(operation, inspectionTypeStoryLines) &&
            this.renderInspectionLines(inspectionTypeStoryLines)}
          <PropertyControlledComponent controllerProperty={hasPartDetails}>
            <div sectiontype="container-section" testId={TEST_SELECTORS.PARTS}>
              {this.renderOperationTotalPartsInfo({
                showOperationTotalPartsPrice,
                showOperationTotalPartsCostPrice,
                showPartDetails,
                totalPartsPrice,
                totalPartsCostPrice,
                isCostPriceSupportedCopyTypes,
              })}
              <PropertyControlledComponent controllerProperty={showPartDetails}>
                <div
                  sectiontype="container-section"
                  className={styles.partsContainer}
                  testId={TEST_SELECTORS.PART_DETAIL}>
                  {this.renderPartDetails(operationPartDetails, shouldDisplayPartPrices, partTaxCodeByComponentId)}
                </div>
              </PropertyControlledComponent>
            </div>
          </PropertyControlledComponent>
        </div>
        <div sectiontype="general-section" className={styles.separator} />
      </div>
    );
  };

  render() {
    const { operations, indicateFieldDetails, taxCodeByOperationId } = this.props;
    const opLevelIndicateFieldDetails = ExportsReader.operationLevelFields(indicateFieldDetails);
    return _map(operations, (operation, index) =>
      this.renderOperations({
        operation,
        index,
        indicateFieldDetails: _get(opLevelIndicateFieldDetails, ROOperationReader.id(operation), EMPTY_OBJECT),
        taxCodeByComponentType: _get(taxCodeByOperationId, ROOperationReader.id(operation), EMPTY_STRING),
      })
    );
  }
}

Operations.propTypes = {
  operations: PropTypes.array.isRequired,
  partDetails: PropTypes.object.isRequired,
  notCustomerCopy: PropTypes.bool.isRequired,
  copyType: PropTypes.string.isRequired,
  resolvedRODetails: PropTypes.object.isRequired,
  pdfSettings: PropTypes.object.isRequired,
  employeeList: PropTypes.array.isRequired,
  resolvedClockInDetails: PropTypes.array.isRequired,
  showLaborSaleCostForTechnician: PropTypes.bool,
  showTechnicianEmployeeCertificationNumber: PropTypes.bool,
  showApprovalNotes: PropTypes.bool,
  showOperationTotalLaborPrice: PropTypes.bool,
  showOperationTotalPartsPrice: PropTypes.bool,
  showOperationTotalLaborCostPrice: PropTypes.bool,
  showOperationTotalPartsCostPrice: PropTypes.bool,
  showPartDetails: PropTypes.bool,
  showPartsPrices: PropTypes.bool,
  showPartNote: PropTypes.bool,
  showSORPartsTable: PropTypes.bool,
  showPartNumber: PropTypes.bool,
  documentType: PropTypes.string,
  resolvedROApprovalSystemComments: PropTypes.array,
  jobId: PropTypes.string,
  isCostPriceSupportedCopyTypes: PropTypes.bool,
  showReturnPartsTable: PropTypes.bool,
  shouldNotDisplayPrice: PropTypes.bool,
  jobBreakupDetailsForMultiPayer: PropTypes.object,
  getFormattedNumber: PropTypes.func,
  taxCodeByOperationId: PropTypes.object,
  job: PropTypes.object,
  indicateFieldDetails: PropTypes.object,
  partUnitOfMeasurementsById: PropTypes.object,
  showOperationBillHrs: PropTypes.bool,
  showTaxCode: PropTypes.bool,
  showOperationTotalPrice: PropTypes.bool,
  roLevelComponentTotals: PropTypes.object,
  payerId: PropTypes.string,
  basePayType: PropTypes.string,
};

Operations.defaultProps = {
  showLaborSaleCostForTechnician: false,
  showTechnicianEmployeeCertificationNumber: false,
  showApprovalNotes: false,
  showOperationTotalLaborPrice: false,
  showOperationTotalPartsPrice: false,
  showOperationTotalLaborCostPrice: false,
  showOperationTotalPartsCostPrice: false,
  showPartDetails: false,
  showPartsPrices: false,
  showPartNote: false,
  showSORPartsTable: false,
  showPartNumber: true,
  documentType: DOCUMENT_TYPES.INVOICE,
  resolvedROApprovalSystemComments: EMPTY_ARRAY,
  partUnitOfMeasurementsById: EMPTY_OBJECT,
  jobId: EMPTY_STRING,
  isCostPriceSupportedCopyTypes: false,
  showReturnPartsTable: false,
  shouldNotDisplayPrice: false,
  jobBreakupDetailsForMultiPayer: undefined,
  taxCodeByOperationId: EMPTY_OBJECT,
  getFormattedNumber: _noop,
  job: EMPTY_OBJECT,
  indicateFieldDetails: EMPTY_OBJECT,
  showOperationBillHrs: false,
  showTaxCode: false,
  showOperationTotalPrice: false,
  roLevelComponentTotals: EMPTY_OBJECT,
  payerId: EMPTY_STRING,
  basePayType: EMPTY_STRING,
};

export default withTekionConversion(Operations);
