import { withProps } from 'recompose';

import _map from 'lodash/map';
import _get from 'lodash/get';
import _reduce from 'lodash/reduce';
import _find from 'lodash/find';
import _isEmpty from 'lodash/isEmpty';
import _toNumber from 'lodash/toNumber';
import _compact from 'lodash/compact';
import _omit from 'lodash/omit';
import _pick from 'lodash/pick';
import _partition from 'lodash/partition';
import _isNil from 'lodash/isNil';
import _includes from 'lodash/includes';

import PartNameCellRenderer from 'exports/Components/PartDetails/Components/PartNameCellRenderer';
import UserReader from 'readers/User.reader';
import ClockStatusReader from 'readers/ClockStatus.reader';
import ExportsReader from 'exports/exports.readers';
import ROOperationReader, { OPERATION_PARTS_FIELD_NAME } from 'tbusiness/appServices/service/readers/ROOperation';
import ROPartAvailabilityReader from 'tbusiness/appServices/service/readers/ROPartAvailability';
import ROTotalsReader from 'tbusiness/appServices/service/readers/RepairOrderTotals';
import JobReader from 'tbusiness/appServices/service/readers/Job';
import { CHARGE } from 'tbase/constants/parts/general';
import ROInvoiceReader from 'exports/ROInvoiceNew/ROInvoice.reader';
import MoneyReader from 'twidgets/appServices/service/readers/Money.reader';
import { formatCurrency } from '@tekion/tekion-base/formatters/formatCurrency';
import { DATE_TIME_FORMATS, getFormattedDateTime } from '@tekion/tekion-base/utils/dateUtils';
import { tget } from 'tbase/utils/general';
import {
  getIndicateFieldValue,
  getTotalFlagAndBillingTimeForTechnician,
  getPdfFormattedTaxCode,
  getFormattedCurrency,
  getTimeInHrs,
} from 'exports/exports.helper';

import {
  getTechnicianDetails,
  getTechnicianName,
  getTechnicianCertificationNumber,
} from 'exports/ROInvoiceNew/roInvoice.helper';

import { EMPTY_OBJECT, EMPTY_STRING, EMPTY_ARRAY } from 'tbase/app.constants';
import { DOCUMENT_TYPES } from 'tbase/constants/roPdf.constants';
import { SERVICE_V3_SUPPORTED_DOC_TYPES } from 'tbusiness/constants/service/roPdf.constants';
import { STORY_LINE_TYPE } from 'utils/constants';
import {
  PART_DETAILS_COLUMNS,
  PARTS_TABLE_COLUMNS_KEYS_LIST,
} from 'exports/Components/PartDetails/partDetailsTable.constants';
import {
  TECHNICIAN_TABLE_COLUMNS,
  TECHNICIAN_TABLE_COLUMNS_KEYS_LIST,
} from 'exports/Components/TechnicianTable/technicianTable.constants';
import { JOB_TYPES } from 'tbase/constants/repairOrder/job';
import ROConstraints from 'helpers/constraints';
import pdfSettingsReader from 'tbase/readers/pdfSettings.reader';
import { PARTS_RETURN_CONFIG } from 'twidgets/appServices/parts/organisms/PartsReturnsTable/partsReturnTable.config';

import { SOR_TABLE_COLUMNS } from '../../../SORTable/SORTable.constants';
import { RETURN_PARTS_TABLE_COLUMNS } from '../../../ReturnPartsTable/ReturnPartsTable.constants';

export const getFormattedSorPartTableData = sorPartDetails =>
  _map(sorPartDetails, sorPart => {
    const sorNumber = _get(sorPart, 'sorNumber');
    const requestedBy = UserReader.name(ROPartAvailabilityReader.requestedByUser(sorPart));
    const sorDate = getFormattedDateTime(_get(sorPart, 'createdTime'), DATE_TIME_FORMATS.DATE_MONTH_YEAR);
    const sorQuantity = _get(sorPart, 'sorQty');
    return {
      [SOR_TABLE_COLUMNS.SOR_NUMBER]: sorNumber,
      [SOR_TABLE_COLUMNS.REQUESTED_BY]: requestedBy,
      [SOR_TABLE_COLUMNS.SOR_DATE]: sorDate,
      [SOR_TABLE_COLUMNS.SOR_QUANTITY]: sorQuantity,
    };
  });

const getPartColumDetails = ({ name, quantity, unitPrice, salePrice, costPrice, ...rest }) => ({
  ...rest,
  [PART_DETAILS_COLUMNS.NAME]: name,
  [PART_DETAILS_COLUMNS.QUANTITY]: quantity,
  [PART_DETAILS_COLUMNS.UNIT_PRICE]: unitPrice,
  [PART_DETAILS_COLUMNS.SALE_PRICE]: salePrice,
  [PART_DETAILS_COLUMNS.COST_PRICE]: costPrice,
});

const getCoreAndCoreReturnPartDetails = ({ partName, coreChargeDetails, coreReturnDetails }) => {
  const [coreChargeQuantity, coreReturnQuantity] = [
    _get(coreChargeDetails, 'quantity', 0),
    _get(coreReturnDetails, 'quantity', 0),
  ];
  const [coreUnitPrice, coreReturnUnitPrice] = [
    MoneyReader.value(_get(coreChargeDetails, 'amount', EMPTY_OBJECT)),
    MoneyReader.value(_get(coreReturnDetails, 'amount', EMPTY_OBJECT)),
  ];
  const [coreSalePrice, coreReturnSalePrice] = [
    formatCurrency(coreChargeQuantity * coreUnitPrice),
    formatCurrency(coreReturnQuantity * coreReturnUnitPrice),
  ];
  return [
    _isEmpty(coreChargeDetails)
      ? null
      : getPartColumDetails({
          name: __(`Core: ${partName}`),
          quantity: `${coreChargeQuantity}`,
          unitPrice: formatCurrency(coreUnitPrice),
          salePrice: coreSalePrice,
          costPrice: '-',
        }),
    _isEmpty(coreReturnDetails)
      ? null
      : getPartColumDetails({
          name: __(`Core Return: ${partName}`),
          quantity: `${coreReturnQuantity}`,
          unitPrice: formatCurrency(coreReturnUnitPrice),
          salePrice: coreReturnSalePrice,
          costPrice: '-',
        }),
  ];
};

export const getFormattedPartDetails = (partDetails, partBreakdownByComponentId, payerId) =>
  _reduce(
    partDetails,
    (acc, item) => {
      const partSplitDetails = _get(partBreakdownByComponentId, ROPartAvailabilityReader.id(item), EMPTY_OBJECT);
      const partPostSplitDetailsByPayerId =
        _find(_get(partSplitDetails, 'postSplits', EMPTY_ARRAY), { payerId }) || EMPTY_OBJECT;
      const charges = ROPartAvailabilityReader.charges(item);
      const coreChargeDetails = _find(charges, { type: CHARGE.CORE }) || EMPTY_OBJECT;
      const coreReturnDetails = _find(charges, { type: CHARGE.CORE_RETURN }) || EMPTY_OBJECT;
      const partName = ROInvoiceReader.partName(item);
      const oemAdjustmentCost = MoneyReader.value(
        ROPartAvailabilityReader.oemAdjustmentCostPerPart(item) || EMPTY_OBJECT
      );
      const partQuantity = ROInvoiceReader.partQuantity(item);
      const hasOEMCostOffset = _toNumber(oemAdjustmentCost);
      return _compact([
        ...acc,
        getPartColumDetails({
          ...item,
          name: partName,
          quantity: partQuantity,
          unitPrice: ROInvoiceReader.partUnitPrice(item),
          salePrice: _isEmpty(partPostSplitDetailsByPayerId)
            ? ROInvoiceReader.partSalePrice(item)
            : getFormattedCurrency(_get(partPostSplitDetailsByPayerId, 'effectiveSaleAmount', 0)),
          costPrice: _isEmpty(partPostSplitDetailsByPayerId)
            ? ROInvoiceReader.partCostPrice(item)
            : getFormattedCurrency(_get(partPostSplitDetailsByPayerId, 'costAmount', 0)),
        }),
        !hasOEMCostOffset
          ? null
          : getPartColumDetails({
              name: __('OEM cost offset'),
              quantity: partQuantity,
              unitPrice: '-',
              salePrice: '-',
              costPrice: formatCurrency(oemAdjustmentCost),
            }),
        ...getCoreAndCoreReturnPartDetails({ partName, coreChargeDetails, coreReturnDetails }),
      ]);
    },
    []
  );

const getActualTimeInSeconds = ({ techId, jobId, operationId, resolvedClockInDetails }) => {
  const actualTimeInMilliSeconds = _reduce(
    resolvedClockInDetails,
    (acc, resolvedClockInDetail) => {
      if (
        _get(resolvedClockInDetail, 'userId', '') !== techId ||
        ClockStatusReader.jobId(resolvedClockInDetail) !== jobId ||
        operationId !== ClockStatusReader.operationId(resolvedClockInDetail)
      )
        return acc;
      return acc + (ClockStatusReader.timeSpentInMilliSeconds(resolvedClockInDetail) || 0);
    },
    0
  );
  return actualTimeInMilliSeconds / 1000;
};

export const getTechnicianTableBreakdown = ({
  techIdWithBillingTimes = EMPTY_OBJECT,
  operation,
  resolvedRODetails,
  showTechnicianNumberOnly,
  employeeList,
  resolvedClockInDetails,
  getFormattedNumber,
  indicateFieldDetails,
  job,
}) =>
  _map(techIdWithBillingTimes, tech => {
    const techId = tget(tech, 'techId', '');
    const totalLaborAmount = MoneyReader.amount(ROOperationReader.laborAmount(operation));
    const operationBillingTimeInSeconds = ROOperationReader.billingTimeInSeconds(operation);
    const { totalFlagTimeInSeconds, totalAssignedBillingTimeInSeconds } = getTotalFlagAndBillingTimeForTechnician(
      ROOperationReader.flagTimesWithPayDay(tech)
    );
    const technicianDetails = getTechnicianDetails(techId, resolvedRODetails, job);
    const techIndicateFieldDetails = _get(indicateFieldDetails, techId, EMPTY_OBJECT);
    return {
      [TECHNICIAN_TABLE_COLUMNS.TECHNICIAN_NAME_WITH_ID]: getTechnicianName(
        technicianDetails,
        showTechnicianNumberOnly
      ),
      [TECHNICIAN_TABLE_COLUMNS.EMPLOYEE_CERTIFICATION_NUMBER]: getTechnicianCertificationNumber(
        technicianDetails,
        employeeList
      ),
      [TECHNICIAN_TABLE_COLUMNS.ACTUAL_HRS]: getFormattedNumber(
        getTimeInHrs(
          getActualTimeInSeconds({
            techId,
            jobId: ROOperationReader.jobId(operation),
            operationId: ROOperationReader.id(operation),
            resolvedClockInDetails,
          }),
          { shouldRoundOff: true }
        )
      ),
      [TECHNICIAN_TABLE_COLUMNS.ASSIGNED_BILL_HRS]: getIndicateFieldValue(
        getFormattedNumber(getTimeInHrs(totalAssignedBillingTimeInSeconds)),
        ExportsReader.billHrs(techIndicateFieldDetails)
      ),
      [TECHNICIAN_TABLE_COLUMNS.FLAG_HRS]: getIndicateFieldValue(
        getFormattedNumber(getTimeInHrs(totalFlagTimeInSeconds)),
        ExportsReader.flagHrs(techIndicateFieldDetails)
      ),
      [TECHNICIAN_TABLE_COLUMNS.LABOR_COST]: getIndicateFieldValue(
        ROInvoiceReader.getFormattedCurrency(MoneyReader.amount(ROOperationReader.totalCostAmount(tech))),
        ExportsReader.laborCostAmount(techIndicateFieldDetails)
      ),
      [TECHNICIAN_TABLE_COLUMNS.LABOR_SALE]: getIndicateFieldValue(
        ROInvoiceReader.getFormattedCurrency(
          (totalAssignedBillingTimeInSeconds / operationBillingTimeInSeconds) * totalLaborAmount
        ),
        ExportsReader.laborSaleAmount(techIndicateFieldDetails)
      ),
    };
  });

export const getPartDetailsTableCellConfig = showPartNote => ({
  [PART_DETAILS_COLUMNS.NAME]: {
    accessor: PART_DETAILS_COLUMNS.NAME,
    renderer: withProps({ showPartNote })(PartNameCellRenderer),
  },
  [PART_DETAILS_COLUMNS.QUANTITY]: {
    accessor: PART_DETAILS_COLUMNS.QUANTITY,
  },
  [PART_DETAILS_COLUMNS.UNIT_PRICE]: {
    accessor: PART_DETAILS_COLUMNS.UNIT_PRICE,
  },
  [PART_DETAILS_COLUMNS.SALE_PRICE]: {
    accessor: PART_DETAILS_COLUMNS.SALE_PRICE,
  },
  [PART_DETAILS_COLUMNS.COST_PRICE]: {
    accessor: PART_DETAILS_COLUMNS.COST_PRICE,
  },
});

export const getTechnicianTableColumns = ({
  notCustomerCopy,
  showLaborSaleCostForTechnician,
  showTechnicianEmployeeCertificationNumber,
  showOperationTotalLaborCostPrice,
  isOfTypeCustomerPay,
}) => {
  if (notCustomerCopy && !isOfTypeCustomerPay) {
    const technicianTableColumns = showOperationTotalLaborCostPrice
      ? TECHNICIAN_TABLE_COLUMNS
      : _omit(TECHNICIAN_TABLE_COLUMNS, [TECHNICIAN_TABLE_COLUMNS_KEYS_LIST.LABOR_COST]);
    const updatedTechnicianTableColumns = showLaborSaleCostForTechnician
      ? technicianTableColumns
      : _omit(technicianTableColumns, [
          TECHNICIAN_TABLE_COLUMNS_KEYS_LIST.LABOR_COST,
          TECHNICIAN_TABLE_COLUMNS_KEYS_LIST.LABOR_SALE,
        ]);
    return showTechnicianEmployeeCertificationNumber
      ? updatedTechnicianTableColumns
      : _omit(updatedTechnicianTableColumns, [TECHNICIAN_TABLE_COLUMNS_KEYS_LIST.EMPLOYEE_CERTIFICATION_NUMBER]);
  }
  return showTechnicianEmployeeCertificationNumber
    ? _pick(TECHNICIAN_TABLE_COLUMNS, [
        TECHNICIAN_TABLE_COLUMNS_KEYS_LIST.TECHNICIAN_NAME_WITH_ID,
        TECHNICIAN_TABLE_COLUMNS_KEYS_LIST.EMPLOYEE_CERTIFICATION_NUMBER,
      ])
    : _pick(TECHNICIAN_TABLE_COLUMNS, [TECHNICIAN_TABLE_COLUMNS_KEYS_LIST.TECHNICIAN_NAME_WITH_ID]);
};

export const getPartTableColumns = (showPartsPrices, showOperationTotalPartsCostPrice) => {
  const partDetailsColumn = showOperationTotalPartsCostPrice
    ? PART_DETAILS_COLUMNS
    : _omit(PART_DETAILS_COLUMNS, [PARTS_TABLE_COLUMNS_KEYS_LIST.COST_PRICE]);
  return showPartsPrices
    ? partDetailsColumn
    : _omit(partDetailsColumn, [
        PARTS_TABLE_COLUMNS_KEYS_LIST.UNIT_PRICE,
        PARTS_TABLE_COLUMNS_KEYS_LIST.SALE_PRICE,
        PARTS_TABLE_COLUMNS_KEYS_LIST.COST_PRICE,
      ]);
};

export const getFormattedReturnPartsTableData = (partName, returnPartDetails) => {
  const quantity = ROPartAvailabilityReader.returnQuantity(returnPartDetails);
  const roNo = _get(returnPartDetails, PARTS_RETURN_CONFIG.sourceAssetNumber);
  const returnReason = ROPartAvailabilityReader.returnReason(returnPartDetails);

  return [
    {
      [RETURN_PARTS_TABLE_COLUMNS.PARTS]: partName,
      [RETURN_PARTS_TABLE_COLUMNS.QUANTITY]: quantity,
      [RETURN_PARTS_TABLE_COLUMNS.RETURN_REFERENCE]: roNo ? __(`$$(RO)# -${roNo}`) : EMPTY_STRING,
      [RETURN_PARTS_TABLE_COLUMNS.RETURN_REASON]: returnReason,
    },
  ];
};

export const shouldNotShowPartDetails = (partName, documentType, returnPart) =>
  _isEmpty(partName) ||
  ((documentType === DOCUMENT_TYPES.RETURN_PARTS || documentType === DOCUMENT_TYPES.RO_RETURN_PARTS) &&
    _isEmpty(returnPart));

export const getSanitizedWarrantyOemOpcodeDetails = (oemOpcodeDetails, getFormattedNumber) =>
  _map(oemOpcodeDetails, opcodeDetails => {
    const billHrs = ROOperationReader.billHrs(opcodeDetails);
    return {
      ...opcodeDetails,
      billingTimeInSeconds: getFormattedNumber(billHrs),
      laborPrice: ROInvoiceReader.getFormattedCurrency(ROOperationReader.warrantyOemOpcodeLaborPrice(opcodeDetails)),
    };
  });

export const getFilteredStoryLinesByType = storyLines =>
  _partition(storyLines, values => ROOperationReader.type(values) !== STORY_LINE_TYPE.INSPECTION);

export const shouldRenderInspectionStoryLines = (job, inspectionLines) =>
  ROOperationReader.type(job) === JOB_TYPES.SERVICE_MENU && !_isEmpty(inspectionLines);

const getSanitizedAmountValue = (amountValue, shouldNotDisplayPrice) => (shouldNotDisplayPrice ? 0 : amountValue);

export const getOperationLevelBreakupAmount = ({
  shouldNotDisplayPrice,
  operation,
  operationPartDetails,
  copyType,
  documentType,
  operationBreakupDetails,
}) => {
  if (_isNil(operationBreakupDetails)) {
    return {
      operationLaborPrice: MoneyReader.amount(
        getSanitizedAmountValue(ROOperationReader.laborAmount(operation), shouldNotDisplayPrice)
      ),
      operationLaborCostPrice: MoneyReader.amount(
        getSanitizedAmountValue(ROOperationReader.laborCostAmount(operation), shouldNotDisplayPrice)
      ),
      totalPartsPrice: getSanitizedAmountValue(
        ROInvoiceReader.getOperationLevelTotalPartsPrice(
          { ...operation, [OPERATION_PARTS_FIELD_NAME]: operationPartDetails },
          copyType,
          documentType,
          operation
        ),
        shouldNotDisplayPrice
      ),
      totalPartsCostPrice: getSanitizedAmountValue(
        ROInvoiceReader.getOperationLevelTotalPartsCostPrice(operationPartDetails, copyType),
        shouldNotDisplayPrice
      ),
    };
  }
  return {
    operationLaborPrice: ROTotalsReader.laborSaleAmount(operationBreakupDetails),
    operationLaborCostPrice: ROTotalsReader.laborCostAmount(operationBreakupDetails),
    totalPartsPrice: ROTotalsReader.partSaleAmount(operationBreakupDetails),
    totalPartsCostPrice: ROTotalsReader.partCostAmount(operationBreakupDetails),
  };
};

export const getPartLineDescription = ({
  showPartsPrices,
  partName,
  partUnitOfMeasure,
  approvedQty,
  partPrice,
  partTaxCodeByComponentId,
  partId,
  showTaxCode,
}) => {
  const taxCode = _get(partTaxCodeByComponentId, partId);
  const partPriceString = showPartsPrices ? `- ${partPrice}` : EMPTY_STRING;
  const partUnitOfMeasureString = partUnitOfMeasure ? `${partUnitOfMeasure} ` : EMPTY_STRING;
  if (_isNil(taxCode) || !showTaxCode) {
    return __('{{partName}} {{approvedQty}} {{partUnitOfMeasureString}}{{partPrice}}', {
      partName,
      partUnitOfMeasureString,
      approvedQty,
      partPrice: partPriceString,
    });
  }
  const pdfFormattedTaxCode = getPdfFormattedTaxCode(taxCode);
  return __('{{partName}} {{approvedQty}} {{partUnitOfMeasureString}}- Tax Code: {{taxCode}} {{partPrice}}', {
    partName,
    approvedQty,
    partUnitOfMeasureString,
    taxCode: pdfFormattedTaxCode,
    partPrice: partPriceString,
  });
};

export const getOperationOrLaborTaxCode = (taxCodeByComponentId, operationId) => {
  const taxCode = _get(taxCodeByComponentId, operationId);
  if (_isNil(taxCode)) return EMPTY_STRING;
  const pdfFormattedTaxCode = getPdfFormattedTaxCode(taxCode);
  return __('Tax Code: {{taxCode}}', { taxCode: pdfFormattedTaxCode });
};

export const getPartPrice = ({ part, shouldNotDisplayPrice, documentType, partSplitAmountDetails }) => {
  if (SERVICE_V3_SUPPORTED_DOC_TYPES.has(documentType))
    return getFormattedCurrency(partSplitAmountDetails ? JobReader.effectiveSaleAmount(partSplitAmountDetails) : 0);

  return shouldNotDisplayPrice ? getFormattedCurrency(0) : ROInvoiceReader.partSalePrice(part);
};

export const getPartPriceForMultiPayer = (
  part,
  shouldNotDisplayPrice,
  partPriceByPartFulfilmentLineItemIdForMultiPayer
) => {
  if (shouldNotDisplayPrice) return getFormattedCurrency(0);
  if (_isEmpty(partPriceByPartFulfilmentLineItemIdForMultiPayer)) {
    return ROPartAvailabilityReader.totalSaleAmount(part) + ROPartAvailabilityReader.totalCoreAmount(part);
  }
  const partFulfilmentLineItemId = ROPartAvailabilityReader.partFulfilmentLineItemId(part);
  const partPrice = _get(partPriceByPartFulfilmentLineItemIdForMultiPayer, partFulfilmentLineItemId, 0);
  return partPrice;
};

/**
 * Determines whether technicians table should be rendered based on document type and settings
 * @param {Object} params - Parameters for the function
 * @param {boolean} params.notCustomerCopy - Whether this is not a customer copy
 * @param {string} params.documentType - Type of document being rendered
 * @param {Object} params.pdfSettings - PDF settings configuration
 * @returns {boolean} - Whether technicians table should be rendered
 */
export const shouldRenderTechniciansTable = ({ notCustomerCopy, documentType, pdfSettings, isOfTypeCustomerPay }) => {
  // Always show technicians for non-customer copies
  if (notCustomerCopy) {
    return true;
  }

  // For invoice documents, check PDF settings
  if (_includes([DOCUMENT_TYPES.RO_INVOICE, DOCUMENT_TYPES.INVOICE], documentType) || isOfTypeCustomerPay) {
    return pdfSettingsReader.shouldShowTechnicianInInvoiceCustomerPDF(pdfSettings);
  }

  // For all other document types, show technicians
  return true;
};
