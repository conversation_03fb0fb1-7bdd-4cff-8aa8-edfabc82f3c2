import _values from 'lodash/values';
import _omit from 'lodash/omit';
import _keyBy from 'lodash/keyBy';
import _filter from 'lodash/filter';

import { tget } from 'tbase/utils/general';
import { TRANSPORTATION_TYPE } from 'tbase/constants/transportationTypes';
import { PAY_TYPE_VALUES } from 'tbase/constants/payTypes';
import { GATEWAY_TYPES } from '@tekion/tekion-widgets/src/organisms/TransactionList/Transaction.constants';
import {
  RO_STATUS_LABEL_MAP,
  PAYMENT_METHOD_VALUE,
  CUSTOMER_TYPES,
  PAY_TYPE_VALUES_VS_AMOUNT_FIELD,
  CELL_RENDERER_OVERLAY_TYPES,
  JOB_TYPE_FILTERS,
  FLAG_TYPES_VALUES,
  WARRANTY_CLAIM_RO_STATUS_VALUE,
  CASHIERING_PAID_DUE_AMOUNT_STATUSES_SET,
} from 'tbase/constants/repairOrder/general';
import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import { JOB_TYPES } from 'tbase/constants/repairOrder/job';
import { OPCODE_SOURCE_TYPE } from 'tbase/constants/repairOrder/opcode';
import {
  RO_STATUS_LEVEL,
  STATUS_LEVEL,
  JOB_STATUS_LEVEL,
  WARRANTY_PAY_RO_STATUS,
  INTERNAL_PAY_RO_STATUS,
  CUSTOMER_PAY_RO_STATUS,
  CASHIERING_RO_STATUS,
  RO_SIGN_STATUS,
  RECOMMENDATION_STATUS,
  SEVERITY_CONFIG,
  JOB_LEVEL_PART_STATUS_VALUE,
  JOB_PART_STATUS,
  JOB_PART_STATUS_CONFIG,
} from 'tcomponents/constants/repairOrder';
import {
  WARRANTY_PAY_RO_STATUS_VALUE,
  INTERNAL_PAY_RO_STATUS_VALUE,
  CUSTOMER_PAY_RO_STATUS_VALUE,
  CASHIERING_RO_STATUS_VALUE,
  TECHNICIAN_STATUS_VALUES,
  WARRANTY_CLAIM_STATUS_VS_LABEL,
  WARRANTY_CLAIM_STATUS_VALUES,
  WARRANTY_CLAIM_STATUS,
  WARRANTY_CLAIM_STATUS_CONFIG,
  NOTIFICATION_TYPE,
  RO_STATUS_VALUES,
  RECOMMENDATION_APPROVAL_STATUS_VALUES,
  STATUS_VALUES,
  JOB_WORK_STATUS_VALUE,
  PAY_TYPE_FILTERS,
  FLAG_TYPES_FILTERS,
  MPVI_JOB_STATUS_FILTER_OPTIONS,
  SERVICE_MODE_OPTIONS,
  STATUS_CONFIG,
  JOB_WORK_STATUS_LABEL,
  JOB_WORK_STATUS_VALUE_VS_COLOR,
  JOB_WORK_STATUS_CONFIG,
  RECOMMENDATION_APPROVAL_STATUS_CONFIG,
  PROMISE_TIME_STATUS_VS_LABEL,
  PROMISE_TIME_STATUS_CONFIG,
  STATUS,
  TIME_PREFERENCE,
  PROMISE_TIME_STATUS_VS_COLOR,
} from 'twidgets/appServices/service/utils/commonConstants';
import {
  PART_TYPES,
  RESOLVED_PART_STATUS,
  PART_STATUS_VALUE,
  FULFILLED_PART_STATUS,
} from 'tbase/constants/repairOrder/partAvailability';
import { ETA_TYPES_VS_LABEL } from 'tbase/constants/etaTypes';
import { RECOMMENDATION_STATUS_VALUES, SEVERITY_VALUES } from 'tbase/constants/repairOrder/recommendation';
import { STORY_LINE_TYPE, STORYLINE_TYPE_VS_CONFIG } from 'tbusiness/appServices/service/constants/roOperation';
import { QUOTE_STATUS, QUOTE_SERVICE_STATUS } from 'tbusiness/appServices/service/constants/quote';
import { URL_TYPES } from 'tbase/constants/api';
import {
  MODULE_TYPES,
  SERVICE_MODE_TYPE_VS_LABEL,
  RO_DETAILS_ENTITY_TYPE,
} from 'tbusiness/appServices/service/constants/general';
import PART_ENTITY_TYPES from 'tbusiness/appServices/parts/constants/partEntitytypes';
import { OFFERING_TYPES } from 'tbase/constants/offerings';
import standardFieldOptionMapper from 'tbase/utils/optionMappers/standardFieldMapper';
import colors from 'tstyles/exports.scss';

export {
  RO_STATUS_LEVEL,
  STATUS_LEVEL,
  JOB_STATUS_LEVEL,
  STATUS,
  WARRANTY_PAY_RO_STATUS,
  INTERNAL_PAY_RO_STATUS,
  CUSTOMER_PAY_RO_STATUS,
  PART_TYPES,
  CELL_RENDERER_OVERLAY_TYPES,
  PAY_TYPE_VALUES_VS_AMOUNT_FIELD,
  URL_TYPES,
  RESOLVED_PART_STATUS,
  PART_STATUS_VALUE,
  CUSTOMER_TYPES,
  STORY_LINE_TYPE,
  STORYLINE_TYPE_VS_CONFIG,
  PAYMENT_METHOD_VALUE,
  OPCODE_SOURCE_TYPE,
  JOB_TYPE_FILTERS,
  WARRANTY_PAY_RO_STATUS_VALUE,
  INTERNAL_PAY_RO_STATUS_VALUE,
  CUSTOMER_PAY_RO_STATUS_VALUE,
  RECOMMENDATION_STATUS_VALUES,
  RO_STATUS_LABEL_MAP,
  STATUS_VALUES,
  RO_STATUS_VALUES,
  RECOMMENDATION_STATUS,
  STATUS_CONFIG,
  SEVERITY_CONFIG,
  SEVERITY_VALUES,
  FULFILLED_PART_STATUS,
  ETA_TYPES_VS_LABEL,
  CASHIERING_RO_STATUS_VALUE,
  CASHIERING_RO_STATUS,
  RO_SIGN_STATUS,
  FLAG_TYPES_VALUES,
  FLAG_TYPES_FILTERS,
  QUOTE_STATUS,
  QUOTE_SERVICE_STATUS,
  SERVICE_MODE_OPTIONS,
  SERVICE_MODE_TYPE_VS_LABEL,
  JOB_LEVEL_PART_STATUS_VALUE,
  JOB_PART_STATUS,
  JOB_PART_STATUS_CONFIG,
  WARRANTY_CLAIM_RO_STATUS_VALUE,
  TECHNICIAN_STATUS_VALUES,
  WARRANTY_CLAIM_STATUS_VS_LABEL,
  WARRANTY_CLAIM_STATUS_VALUES,
  WARRANTY_CLAIM_STATUS,
  WARRANTY_CLAIM_STATUS_CONFIG,
  NOTIFICATION_TYPE,
  PAY_TYPE_FILTERS,
  MPVI_JOB_STATUS_FILTER_OPTIONS,
  JOB_WORK_STATUS_LABEL,
  JOB_WORK_STATUS_VALUE_VS_COLOR,
  JOB_WORK_STATUS_CONFIG,
  JOB_WORK_STATUS_VALUE,
  RECOMMENDATION_APPROVAL_STATUS_CONFIG,
  PROMISE_TIME_STATUS_VS_LABEL,
  PROMISE_TIME_STATUS_CONFIG,
  RECOMMENDATION_APPROVAL_STATUS_VALUES,
  TIME_PREFERENCE,
  PROMISE_TIME_STATUS_VS_COLOR,
  CASHIERING_PAID_DUE_AMOUNT_STATUSES_SET,
};

// ----------------------------------------------------------------------------------------- //
// ----------------------------------Estimate Details--------------------------------------- //
// ----------------------------------------------------------------------------------------- //

export { RO_DETAILS_ENTITY_TYPE };

export const ESTIMATE_DETAILS_ENTITY_TYPE = {
  JOB: 'jobsEstimate',
  RECOMMENDATION: 'recommendationsEstimate',
  MPVI: 'mpviEstimate',
};

export const OFFERING_TYPE_VS_RECOMMENDATION_MODULE_TYPE = {
  [OFFERING_TYPES.DEFAULT]: MODULE_TYPES.RO_TECH_RECOMMENDATIONS,
  [OFFERING_TYPES.DSE_STANDALONE]: MODULE_TYPES.ESTIMATE_TECH_RECOMMENDATIONS,
};

// ----------------------------------------------------------------------------------------- //
// --------------------------------------- MPVI -------------------------------------------- //
// ----------------------------------------------------------------------------------------- //

export const MPVI_MODULE_TYPE = {
  RO: 'RO',
  ESTIMATE: 'ESTIMATE',
};

export const MULITCODE_INSPECTION_OPTIONS = {
  MPI: { value: 'STANDARD', label: __('$$(MPI)'), shortLabel: __('$$(MPI)') },
  UVI: { value: 'UVI', label: __('$$(UVI)'), shortLabel: __('$$(UVI)') },
  PDI: { value: 'PDI', label: __('$$(PDI)'), shortLabel: __('$$(PDI)') },
};

// ----------------------------------------------------------------------------------------- //
// ----------------------------------RO NOTIFICATIONS --------------------------------- //
// ----------------------------------------------------------------------------------------- //

export const RO_CLOSE_POSTING_ERROR_CODE = 'RO1130';

export const REFRESH_DELAY = 1000;

export const RO_CLOSE_CP_DUE_AMOUNT_ERROR_CODE = 'R1107';
export const JOB_INTERNAL_NOTES_MODAL_TITLE = __('Add Job Notes');
export const RO_NOTES_MODAL_TITLE = __('Notes');
export const VEHICLE_OVERRIDE_NOTES_MODAL_TITLE = __('Vehicle Row Notes');

export const RO_NOTIFICATIONS = {
  TECH_ASSIGNED: 'TECH_ASSIGNED',
  RO_BULK_CLOSE: 'RO_BULK_CLOSE',
  MANUAL_PDF_TRIGGERED: 'MANUAL_PDF_TRIGGERED',
  RO_REMINDER: 'RO_REMINDER',
  PARTS_DELETE_ERROR: 'PARTS_DELETE_ERROR',
  RO_AUTO_REFRESH: 'RO_AUTO_REFRESH',
  RO_RIDESHARE_FEE_UPDATE: 'RO_RIDESHARE_FEE_UPDATE',
  RETRIEVE_OEM_REPAIR_ORDER: 'RETRIEVE_OEM_REPAIR_ORDER',
  RO_AUTO_DISPATCH: 'RO_AUTO_DISPATCH',
  QUOTE_PDF_GENERATED: 'QUOTE_PDF_GENERATED',
  USER_RANK_UPDATE: 'USER_RANK_UPDATE',
  RO_REFRESH: 'RO_REFRESH',
  QUOTE_REFRESH: 'QUOTE_REFRESH',
  IRIS_STATUS_UPDATE: 'IRIS_STATUS_UPDATE',
  FLEETBOX_RO_ALERT: 'FLEETBOX_RO_ALERT',
  FLEETBOX_POST_TASK_SUCCESS_ALERT: 'FLEETBOX_POST_SUCCESS_UPDATE_TASK_ALERT',
  FLEETBOX_POST_TASK_ERROR_ALERT: 'FLEETBOX_POST_ERROR_UPDATE_TASK_ALERT',
  RO_RULE_BREACH: 'RO_RULE_BREACH',
  RO_RULE_BREACH_REFRESH: 'RO_RULE_BREACH_REFRESH',
  RO_SERVICE_APPROVAL_STATUS_CHANGE: 'RO_SERVICE_APPROVAL_STATUS_CHANGE',
  NEW_GENERATION_RECEIVED: 'NEW_GENERATION_RECEIVED',
};

export const MODAL_VIEW_TYPES = {
  VOID_JOB: 'VOID_JOB',
  DEFER_JOB: 'DEFER_JOB',
  FORCE_CLOCK: 'FORCE_CLOCK',
  REOPEN_RO: 'REOPEN_RO',
  REOPEN_JOB: 'REOPEN_JOB',
  RETURN_TO_TECH: 'RETURN_TO_TECH',
  REOPEN_RECOMMENDATION: 'REOPEN_RECOMMENDATION',
  REVIEW_RECOMMENDATION: 'REVIEW_RECOMMENDATION',
  REVIEW_ESTIMATE_RECOMMENDATION: 'REVIEW_ESTIMATE_RECOMMENDATION',
  REOPEN_QUOTE: 'REOPEN_QUOTE',
  REOPEN_QUOTE_SERVICE: 'REOPEN_QUOTE_SERVICE',
};

export const VALIDATION_TYPES = {
  PRE_INVOICE: 'PRE_INVOICE',
  PRE_JOB_TECH_FINISHED: 'PRE_JOB_TECH_FINISHED',
  PRE_JOB_COMPLETION: 'PRE_JOB_COMPLETION',
  EXPRESS_MODE: 'EXPRESS_MODE',
  PRE_RECOMMENDATION_ADDITION: 'PRE_RECOMMENDATION_ADDITION',
  POST_JOB_UPDATE: 'POST_JOB_UPDATE',
};

export const DEFAULT_TAXABLE_FEE = 9.25;

export const MIGRATED_RO_EXTERNAL_SOURCE = {
  CDK: 'CDK',
  TEKION_MIGRATED: 'TEKION_MIGRATED',
  TEKION_DSE: 'TEKION_DSE',
  TEKION_DMS: 'TEKION_DMS',
  TEKION_VI: 'TEKION_VI',
};

export const RO_FLAG_TYPE = {
  USER: 'USER',
  DEALER: 'DEALER',
};

export const TRANSPORTATION_TYPE_APPLICATION_ID = 'INTERNAL';

// -------- SUPPORTED OEM TYPES -----------

export const OEM_TYPES = {
  GM: 'GM',
  FCA: 'FCA',
  FORD: 'FORD',
  MAZDA: 'MAZDA',
  RENAULT_NISSAN_MITSUBISHI_ALLIANCE: 'RENAULT_NISSAN_MITSUBISHI_ALLIANCE',
  TATA: 'TATA',
  SUBARU: 'SUBARU',
  RENAULT: 'RENAULT',
  VOLVO: 'VOLVO',
  BMW: 'BMW',
  BENZ: 'BENZ',
};

// ----------------------------------------------------------------------------------------- //
// ----------------------------------JOB STATUS VALUES ICONS-------------------------------- //
// ----------------------------------------------------------------------------------------- //

export const STATUS_ICONS = {
  [STATUS_VALUES.NOT_STARTED]: 'icon-clock-stopped',
  [STATUS_VALUES.IN_PROGRESS]: 'icon-clock_out',
  [STATUS_VALUES.COMPLETED]: 'icon-waiting',
  [STATUS_VALUES.VOID_INITIATED]: 'icon-waiting',
  [STATUS_VALUES.VOID]: 'icon-waiting',
};

export const BUTTON_VIEWS = {
  PRIMARY: 'primary',
  SECONDARY: 'secondary',
};

// ----------------------------------------------------------------------------------------- //
// ----------------------------------USER & JOB TYPE CONFIG--------------------------------- //
// ----------------------------------------------------------------------------------------- //

export const USER_TYPE_CONFIG = {
  SERVICE_ADVISOR: 'ServiceAdvisor',
  TECHNICIAN: 'Technician',
};

export const JOB_TYPE_CONFIG = {
  MPVI: 'MPVI',
  SERVICE_CATALOG: 'serviceCatalog',
  SERVICE_MENU: 'serviceMenu',
};

export const MPVI_INSPECTION_TYPE = { BOOL: 'bool' };

// ----------------------------------------------------------------------------------------- //
// ----------------------------------CONCERN TYPE CONFIG------------------------------------ //
// ----------------------------------------------------------------------------------------- //

export const CONCERN_TYPE = [
  {
    label: __('C/S'),
    value: 'CUSTOMER_STATEMENT',
  },
  {
    label: __('C/R'),
    value: 'CUSTOMER_REQUEST',
  },
  {
    label: __('None'),
    value: 'NONE',
  },
];

export const CONCERN_TYPE_BY_VALUE = _keyBy(CONCERN_TYPE, 'value');

// ----------------------------------------------------------------------------------------- //
// ----------------------------------HOLD WORKFLOW---------------------------------- //
// ----------------------------------------------------------------------------------------- //

export const REASON_FOR_HOLD = [
  { label: __('Hold for sublet'), value: 'SUBLET_HOLD' },
  { label: __('Hold for Authorization'), value: 'AUTHORIZATION_HOLD' },
  { label: __('Hold for parts'), value: 'PARTS_HOLD' },
  { label: __('Hold for Diagnostic'), value: 'HOLD_FOR_DIAGNOSTIC' },
];

// ----------------------------------------------------------------------------------------- //
// ----------------------------------TABS IN DETAILED VIEW---------------------------------- //
// ----------------------------------------------------------------------------------------- //

export const WEB_CHECK_IN_URL = 'webcheckin';
export const RO_DETAILS_RECOMMENDATION_SUMMARY_URL = 'summary';
export const QUOTE_DETAILS_SERVICE = 'service';
export const NEW_ENTITY_URL = 'new';

export const DEFERRED_RECOMMENDATION_GROUP = {
  TITLE: __('$$(Deferred) Recommendations'),
  TYPE: 'DEFERRED_RECOMMENDATION',
};

export const TABS_CONFIG = [
  {
    id: RO_DETAILS_ENTITY_TYPE.JOB,
    displayName: __('Jobs'),
    routeName: RO_DETAILS_ENTITY_TYPE.JOB,
  },
  {
    id: RO_DETAILS_ENTITY_TYPE.RECOMMENDATION,
    displayName: __('Recommendations'),
    routeName: RO_DETAILS_ENTITY_TYPE.RECOMMENDATION,
  },
  {
    id: RO_DETAILS_ENTITY_TYPE.MPVI,
    displayName: __('$$(Inspection)'),
    routeName: RO_DETAILS_ENTITY_TYPE.MPVI,
  },
  {
    id: RO_DETAILS_ENTITY_TYPE.APPROVALS,
    displayName: __('Approval'),
  },
];

export const INSPECTION_TYPE_VS_LABEL = {
  [JOB_TYPES.MPVI]: __('$$(MPI)'),
  [JOB_TYPES.PDI]: __('$$(PDI)'),
  [JOB_TYPES.UVI]: __('$$(UVI)'),
};

export const USER_ROLES = {
  SERVICE_ADVISOR: 'ServiceAdvisor',
  TECHNICIAN: 'Technician',
};

// ----------------------------------------------------------------------------------------- //
// ----------------------------------------RO Status---------------------------------------- //
// ----------------------------------------------------------------------------------------- //

export const RO_STATUS_ACTION = {
  VOID: 'VOID',
  REOPEN: 'REOPEN',
};

export const QUOTE_STATUS_ACTION = {
  VOID: 'VOID',
  REOPEN: 'REOPEN',
  DECLINE: 'DECLINE',
  REVIEW: 'REVIEW',
};

export const AVAILABLE_STATUS = _values(
  _omit(RO_STATUS_VALUES, [RO_STATUS_VALUES.TECH_ASSIGNED, RO_STATUS_VALUES.VOID, RO_STATUS_VALUES.CLOSED])
);

// ----------------------------------------------------------------------------------------- //
// ----------------------------------------Tech Recommendation---------------------------------------- //
// ----------------------------------------------------------------------------------------- //

export const COMMUNICATION_MODE = {
  CALL: 'CALL',
  EMAIL: 'EMAIL',
  TEXT: 'TEXT',
  IN_PERSON: 'IN_PERSON',
  ONLINE: 'ONLINE',
};

export const COMMUNICATION_CONFIG = {
  [COMMUNICATION_MODE.CALL]: { label: __('Call'), value: COMMUNICATION_MODE.CALL },
  [COMMUNICATION_MODE.EMAIL]: { label: __('Email'), value: COMMUNICATION_MODE.EMAIL },
  [COMMUNICATION_MODE.TEXT]: { label: __('Text'), value: COMMUNICATION_MODE.TEXT },
  [COMMUNICATION_MODE.IN_PERSON]: { label: __('In Person'), value: COMMUNICATION_MODE.IN_PERSON },
  [COMMUNICATION_MODE.ONLINE]: { label: __('Consumer Portal'), value: COMMUNICATION_MODE.ONLINE },
};

export const COMMUNICATION_MODE_OPTIONS = [
  COMMUNICATION_CONFIG[COMMUNICATION_MODE.CALL],
  COMMUNICATION_CONFIG[COMMUNICATION_MODE.EMAIL],
  COMMUNICATION_CONFIG[COMMUNICATION_MODE.TEXT],
  COMMUNICATION_CONFIG[COMMUNICATION_MODE.IN_PERSON],
];

export const WARRANTY_CLAIM_RO_STATUS = {
  [WARRANTY_CLAIM_RO_STATUS_VALUE.DRAFTED]: {
    label: __('Drafted'),
    value: WARRANTY_CLAIM_RO_STATUS_VALUE.DRAFTED,
    color: STATUS_LEVEL.OPEN,
  },
  [WARRANTY_CLAIM_RO_STATUS_VALUE.SUBMITTED]: {
    label: __('Submitted'),
    value: WARRANTY_CLAIM_RO_STATUS_VALUE.SUBMITTED,
    color: STATUS_LEVEL.LOW,
  },
  [WARRANTY_CLAIM_RO_STATUS_VALUE.ACCEPTED]: {
    label: __('Accepted'),
    value: WARRANTY_CLAIM_RO_STATUS_VALUE.ACCEPTED,
    color: STATUS_LEVEL.LOW,
  },
  [WARRANTY_CLAIM_RO_STATUS_VALUE.ACCEPTED_AND_PAID]: {
    label: __('Accepted and paid'),
    value: WARRANTY_CLAIM_RO_STATUS_VALUE.ACCEPTED_AND_PAID,
    color: STATUS_LEVEL.LOW,
  },
  [WARRANTY_CLAIM_RO_STATUS_VALUE.UNACCEPTED]: {
    label: __('Unaccepted'),
    value: WARRANTY_CLAIM_RO_STATUS_VALUE.UNACCEPTED,
    color: STATUS_LEVEL.HIGH,
  },
  [WARRANTY_CLAIM_RO_STATUS_VALUE.REJECTED]: {
    label: __('Rejected'),
    value: WARRANTY_CLAIM_RO_STATUS_VALUE.REJECTED,
    color: STATUS_LEVEL.HIGH,
  },
  [WARRANTY_CLAIM_RO_STATUS_VALUE.PENDING]: {
    label: __('Pending'),
    value: WARRANTY_CLAIM_RO_STATUS_VALUE.PENDING,
    color: STATUS_LEVEL.MEDIUM,
  },
  [WARRANTY_CLAIM_RO_STATUS_VALUE.PARTIALLY_ACCEPTED]: {
    label: __('Partially Accepted'),
    value: WARRANTY_CLAIM_RO_STATUS_VALUE.PARTIALLY_ACCEPTED,
    color: STATUS_LEVEL.MEDIUM,
  },
  [WARRANTY_CLAIM_RO_STATUS_VALUE.PAID]: {
    label: __('Paid'),
    value: WARRANTY_CLAIM_RO_STATUS_VALUE.PAID,
    color: STATUS_LEVEL.LOW,
  },
  [WARRANTY_CLAIM_RO_STATUS_VALUE.FAILED]: {
    label: __('Failed'),
    value: WARRANTY_CLAIM_RO_STATUS_VALUE.FAILED,
    color: STATUS_LEVEL.HIGH,
  },
  [WARRANTY_CLAIM_RO_STATUS_VALUE.NEW]: {
    label: __('New'),
    value: WARRANTY_CLAIM_RO_STATUS_VALUE.NEW,
    color: STATUS_LEVEL.OPEN,
  },
  [WARRANTY_CLAIM_RO_STATUS_VALUE.VOIDED]: {
    label: __('Voided'),
    value: WARRANTY_CLAIM_RO_STATUS_VALUE.VOIDED,
    color: STATUS_LEVEL.VOID,
  },
  [WARRANTY_CLAIM_RO_STATUS_VALUE.READY_FOR_SUBMISSION]: {
    label: __('Ready for Submission'),
    value: WARRANTY_CLAIM_RO_STATUS_VALUE.READY_FOR_SUBMISSION,
    color: STATUS_LEVEL.MEDIUM,
  },
  [WARRANTY_CLAIM_RO_STATUS_VALUE.STORED]: {
    label: __('Stored'),
    value: WARRANTY_CLAIM_RO_STATUS_VALUE.STORED,
    color: STATUS_LEVEL.CAUTION,
  },
  [WARRANTY_CLAIM_RO_STATUS_VALUE.PARTIALLY_STORED]: {
    label: __('Partially Stored'),
    value: WARRANTY_CLAIM_RO_STATUS_VALUE.PARTIALLY_STORED,
    color: STATUS_LEVEL.CAUTION,
  },
  [WARRANTY_CLAIM_RO_STATUS_VALUE.IN_ERROR]: {
    label: __('In Error'),
    value: WARRANTY_CLAIM_RO_STATUS_VALUE.IN_ERROR,
    color: STATUS_LEVEL.HIGH,
  },
  [WARRANTY_CLAIM_RO_STATUS_VALUE.TO_BE_CONFIRMED]: {
    label: __('To be Confirmed'),
    value: WARRANTY_CLAIM_RO_STATUS_VALUE.TO_BE_CONFIRMED,
    color: STATUS_LEVEL.MEDIUM,
  },
  [WARRANTY_CLAIM_RO_STATUS_VALUE.RELEASABLE]: {
    label: __('Releasable'),
    value: WARRANTY_CLAIM_RO_STATUS_VALUE.RELEASABLE,
    color: STATUS_LEVEL.LOW,
  },
  [WARRANTY_CLAIM_RO_STATUS_VALUE.AWAITING_DECISION]: {
    label: __('Awaiting Decision'),
    value: WARRANTY_CLAIM_RO_STATUS_VALUE.AWAITING_DECISION,
    color: STATUS_LEVEL.INTERMEDIATE,
  },
  [WARRANTY_CLAIM_RO_STATUS_VALUE.DECIDED]: {
    label: __('Decided'),
    value: WARRANTY_CLAIM_RO_STATUS_VALUE.DECIDED,
    color: STATUS_LEVEL.PENDING,
  },
  [WARRANTY_CLAIM_RO_STATUS_VALUE.CREDITED]: {
    label: __('Credited'),
    value: WARRANTY_CLAIM_RO_STATUS_VALUE.CREDITED,
    color: STATUS_LEVEL.OPEN,
  },
  DEFAULT: {
    color: STATUS_LEVEL.LOW,
  },
  APPEAL: {
    label: __('-A'),
  },
};

// ----------------------------------------------------------------------------------------- //
// ----------------------------------------Parts---------------------------------------- //
// ----------------------------------------------------------------------------------------- //

export const QUANTITY_UNIT = [
  { value: 'ml', label: __('ml') },
  { value: 'pcs', label: __('pcs') },
  { value: 'ea', label: __('ea') },
];

export const PARTS_PRICING_MODE = {
  SINGLE: 'SINGLE',
  ALL: 'ALL',
};

export const CLOCK_TYPES = {
  OTHER_LABOR_HOUR: 'OTHER_LABOR_HOUR',
  REGULAR_TIME: 'REGULAR_TIME',
  DIAGNOSTIC_TIME: 'DIAGNOSTIC_TIME',
  ADDITIONAL_TIME: 'ADDITIONAL_TIME',
};

export const CLOCK_TYPE_OPTIONS = [
  {
    label: __('Other Hour'),
    value: CLOCK_TYPES.OTHER_LABOR_HOUR,
  },
  {
    label: __('Regular Hour'),
    value: CLOCK_TYPES.REGULAR_TIME,
  },
  {
    label: __('Diagnostic Hour'),
    value: CLOCK_TYPES.DIAGNOSTIC_TIME,
  },
  {
    label: __('Additional Hour'),
    value: CLOCK_TYPES.ADDITIONAL_TIME,
  },
];

export const REGULAR_CLOCK_TYPE_OPTION = {
  label: __('Regular Hour'),
  value: 'REGULAR_TIME',
};

export const FULFILLEMT_PART_STATUS = {
  [PART_STATUS_VALUE.REQUEST_PENDING]: {
    label: __('Request Pending'),
    value: PART_STATUS_VALUE.REQUEST_PENDING,
    color: STATUS_LEVEL.HIGH,
  },
  [PART_STATUS_VALUE.DRAFTED]: {
    label: __('Drafted'),
    value: PART_STATUS_VALUE.DRAFTED,
    color: STATUS_LEVEL.MEDIUM,
  },
  [PART_STATUS_VALUE.IN_COMPLETE]: {
    label: __('Requested'),
    value: PART_STATUS_VALUE.IN_COMPLETE,
    color: STATUS_LEVEL.OPEN,
  },
  [PART_STATUS_VALUE.CLAIMED]: {
    label: __('Claimed'),
    value: PART_STATUS_VALUE.CLAIMED,
    color: STATUS_LEVEL.MEDIUM,
  },
  [PART_STATUS_VALUE.HOLD]: {
    label: __('Claimed'),
    value: PART_STATUS_VALUE.HOLD,
    color: STATUS_LEVEL.MEDIUM,
  },
  [PART_STATUS_VALUE.PARTIALLY_HOLD]: {
    label: __('Claimed'),
    value: PART_STATUS_VALUE.PARTIALLY_HOLD,
    color: STATUS_LEVEL.MEDIUM,
  },
  [PART_STATUS_VALUE.DELIVERED]: {
    label: __('Fulfilled'),
    value: PART_STATUS_VALUE.DELIVERED,
    color: STATUS_LEVEL.LOW,
  },
  [PART_STATUS_VALUE.CLOSED]: {
    label: __('Fulfilled'),
    value: PART_STATUS_VALUE.CLOSED,
    color: STATUS_LEVEL.LOW,
  },
  [PART_STATUS_VALUE.PARTIALLY_DELIVERED]: {
    label: __('Partially Fulfilled'),
    value: PART_STATUS_VALUE.PARTIALLY_DELIVERED,
    color: STATUS_LEVEL.LOW,
  },
  [PART_STATUS_VALUE.REVIEWED]: {
    label: __('Reviewed'),
    value: PART_STATUS_VALUE.REVIEWED,
    color: STATUS_LEVEL.LOW,
  },
  [PART_STATUS_VALUE.VOIDED]: {
    label: __('Void'),
    value: PART_STATUS_VALUE.VOIDED,
    color: STATUS_LEVEL.VOID,
  },
};

export const PART_AVAILABILITY_PART_STATUS = {
  [PART_STATUS_VALUE.REQUEST_PENDING]: {
    label: __('Request Pending'),
    value: PART_STATUS_VALUE.REQUEST_PENDING,
    color: STATUS_LEVEL.HIGH,
  },
  [PART_STATUS_VALUE.DRAFTED]: {
    label: __('Drafted'),
    value: PART_STATUS_VALUE.DRAFTED,
    color: STATUS_LEVEL.MEDIUM,
  },
  [PART_STATUS_VALUE.IN_COMPLETE]: {
    label: __('Requested'),
    value: PART_STATUS_VALUE.IN_COMPLETE,
    color: STATUS_LEVEL.OPEN,
  },
  [PART_STATUS_VALUE.CLAIMED]: {
    label: __('Claimed'),
    value: PART_STATUS_VALUE.CLAIMED,
    color: STATUS_LEVEL.MEDIUM,
  },
  [PART_STATUS_VALUE.HOLD]: {
    label: __('Claimed'),
    value: PART_STATUS_VALUE.HOLD,
    color: STATUS_LEVEL.MEDIUM,
  },
  [PART_STATUS_VALUE.PARTIALLY_HOLD]: {
    label: __('Claimed'),
    value: PART_STATUS_VALUE.PARTIALLY_HOLD,
    color: STATUS_LEVEL.MEDIUM,
  },
  [PART_STATUS_VALUE.PARTIALLY_DELIVERED]: {
    label: __('Claimed'),
    value: PART_STATUS_VALUE.PARTIALLY_DELIVERED,
    color: STATUS_LEVEL.MEDIUM,
  },
  [PART_STATUS_VALUE.DELIVERED]: {
    label: __('Quoted'),
    value: PART_STATUS_VALUE.DELIVERED,
    color: STATUS_LEVEL.LOW,
  },
  [PART_STATUS_VALUE.CLOSED]: {
    label: __('Quoted'),
    value: PART_STATUS_VALUE.CLOSED,
    color: STATUS_LEVEL.LOW,
  },
  [PART_STATUS_VALUE.REVIEWED]: {
    label: __('Reviewed'),
    value: PART_STATUS_VALUE.REVIEWED,
    color: STATUS_LEVEL.LOW,
  },
  [PART_STATUS_VALUE.VOIDED]: {
    label: __('Void'),
    value: PART_STATUS_VALUE.VOIDED,
    color: STATUS_LEVEL.VOID,
  },
};

// ----------------------------------------------------------------------------------------- //
// -------------------------------------RO Job Storyline------------------------------------ //
// ----------------------------------------------------------------------------------------- //

export const STORYLINE_STATUS = {
  SELECTED: 'SELECTED',
  UNSELECTED: 'UNSELECTED',
};

// ----------------------------------------------------------------------------------------- //
// -------------------------------------RO Customer Info------------------------------------ //
// ----------------------------------------------------------------------------------------- //

const SENTIMENTAL_ANALYSIS_STATES = {
  NEGATIVE: 'negative',
  NEUTRAL: 'neutral',
  POSITIVE: 'positive',
};

export const SENTIMENTAL_ANALYSIS_STATE_COLOR = {
  [SENTIMENTAL_ANALYSIS_STATES.POSITIVE]: '#60D156',
  [SENTIMENTAL_ANALYSIS_STATES.NEUTRAL]: '#FF8800',
  [SENTIMENTAL_ANALYSIS_STATES.NEGATIVE]: '#F52908',
};

const SPEND_CATEGORY = {
  PLATINUM: 'platinum',
  GOLD: 'gold',
  NONE: 'none',
};

export const SPEND_CATEGORY_COLOR_MAP = {
  [SPEND_CATEGORY.NONE]: 'light-grey',
  [SPEND_CATEGORY.GOLD]: 'gold',
  [SPEND_CATEGORY.PLATINUM]: 'dark-grey',
};

export const CONTACT_ICON = {
  CALL: 'CALL',
  TEXT: 'TEXT',
  EMAIL: 'EMAIL',
};

export const CONTACT_ICON_MAP = {
  [CONTACT_ICON.CALL]: 'icon-call',
  [CONTACT_ICON.TEXT]: 'icon-message',
  [CONTACT_ICON.EMAIL]: 'icon-mail',
};

// ----------------------------------------------------------------------------------------- //
// -------------------------------------RO List Filters------------------------------------- //
// ----------------------------------------------------------------------------------------- //

const FILTER_FIELDS = {
  TECHNICIAN: 'assignedTechIds',
  PRIMARY_SERVICE_ADVISOR: 'primaryAdvisorId',
  RO_STATUS_VALUES: 'status',
};

export const AVAILABLE_FILTER_FIELDS = Object.values(FILTER_FIELDS);

export const FILTER_FIELD_TYPES = {
  MULTI_SELECT: 'multi_select',
  SINGLE_SELECT: 'single_select',
  TEXT: 'text',
};

export const FILTER_FIELD_TYPES_MAP = {
  [FILTER_FIELDS.PRIMARY_SERVICE_ADVISOR]: FILTER_FIELD_TYPES.MULTI_SELECT,
  [FILTER_FIELDS.RO_STATUS_VALUES]: FILTER_FIELD_TYPES.MULTI_SELECT,
  [FILTER_FIELDS.SEARCH_TERM]: FILTER_FIELD_TYPES.TEXT,
  [FILTER_FIELDS.TECHNICIAN]: FILTER_FIELD_TYPES.MULTI_SELECT,
};

export const ERROR_MESSAGE = {
  DEFAULT_SUCCESS: __('Data submitted successfully'),
  DEFAULT_ERROR: __('Something went wrong'),
};

export const TRANSACTION_TYPES = {
  CHECK: 'check',
  CASH: 'cash',
  CARD: 'card',
};

export const TRANSACTION_TYPE_LIST = Object.values(TRANSACTION_TYPES);

export const CARD_SERVICE_PROVIDERS = {
  VISA: 'VISA',
  MASTERCARD: 'MASTERCARD',
};

export const CARD_SERVICE_PROVIDER_LIST = Object.values(CARD_SERVICE_PROVIDERS);

export const CARD_TYPES = {
  CREDIT: 'CREDIT',
  DEBIT: 'DEBIT',
};

export const CARD_TYPE_LIST = Object.values(CARD_TYPES);

export const TRANSACTION_TYPE_LABELS = {
  [TRANSACTION_TYPES.CHECK]: __('Check'),
  [TRANSACTION_TYPES.CASH]: __('Cash'),
  [TRANSACTION_TYPES.CARD]: __('Credit/Debit Card'),
  [CARD_TYPES.CREDIT]: __('Credit card'),
  [CARD_TYPES.DEBIT]: __('Debit card'),
};

export const TRANSACTION_TYPE_ICONS = {
  [TRANSACTION_TYPES.CHECK]: 'icon-check',
  [TRANSACTION_TYPES.CASH]: 'icon-cash',
  [TRANSACTION_TYPES.CARD]: 'icon-card',
  [CARD_SERVICE_PROVIDERS.VISA]: 'icon-card',
  [CARD_SERVICE_PROVIDERS.MASTERCARD]: 'icon-card',
};

export const QUOTE_TYPES = {
  [JOB_TYPES.SERVICE_CATALOG]: JOB_TYPES.SERVICE_CATALOG,
  [JOB_TYPES.SERVICE_MENU]: JOB_TYPES.SERVICE_MENU,
};

export const SERVICE_MENU_DSE_VS_CDMS_VALUE = {
  premium: 'PREMIUM',
  OEM: 'BASIC',
};

export const AVAILABLE_TRANSPORT_OPTIONS = _values(TRANSPORTATION_TYPE);

export const USER_TYPES = {
  SERVICE_ADVISOR: 'sa',
  TECHNICIAN: 'technician',
};

// ----------------------------------------------------------------------------------------- //
// -------------------------------------Customer Type------------------------------------- //
// ----------------------------------------------------------------------------------------- //

const CUSTOMER_TYPES_IN_LABOR_RATE = {
  RETAIL: 'Retail Customer',
  FLEET: 'Fleet',
  EMPLOYEE: __('$$(Employee)'),
};

export const CUSTOMER_TYPE_MAPPING = {
  [CUSTOMER_TYPES.INDIVIDUAL]: CUSTOMER_TYPES_IN_LABOR_RATE.RETAIL,
  [CUSTOMER_TYPES.ENTERPRISE]: CUSTOMER_TYPES_IN_LABOR_RATE.FLEET,
};

// ----------------------------------------------------------------------------------------- //
// -------------------------------------OpCode------------------------------------- //
// ----------------------------------------------------------------------------------------- //

const SQUARE_PAYMENT_GATEWAY = GATEWAY_TYPES.SQUARE.id;
const MANUAL_PAYMENT = GATEWAY_TYPES.TERMINAL.id;
const PAY_FLOW_PAYMENT_GATEWAY = GATEWAY_TYPES.PAYPAL.id;

export const PAYMENT_GATEWAY = {
  SQUARE_PAYMENT_GATEWAY,
  MANUAL_PAYMENT,
  PAY_FLOW_PAYMENT_GATEWAY,
};

export const MANUAL_PAYMENT_CARD_TYPES = [
  {
    label: 'Visa',
    value: 'VISA',
  },
  {
    label: 'Mastercard',
    value: 'MASTERCARD',
  },
];

// ----------------------------------------------------------------------------------------- //
// ----------------------------------Payment  Method----------------------------------- //
// ----------------------------------------------------------------------------------------- //

export const SEVERITY_IDS = {
  SEVERITY_PASS_ID: 1,
  SEVERITY_WARNING_ID: 2,
  SEVERITY_CRITICAL_ID: 3,
  SEVERITY_NOT_APPLICABLE_ID: 4,
};

export const MAP_SEVERITY_TO_VALUE = {
  1: 'passInspection',
  2: 'cautionInspections',
  3: 'failedInspections',
  4: 'notApplicableInspections',
};

// ----------------------------------------------------------------------------------------- //
// ----------------------------------Pre Repair Authorization------------------------------- //
// ----------------------------------------------------------------------------------------- //

export const TEXT_MESSAGE_ACTIONS = {
  SENT_TO_CUSTOMER: 'SEND_TO_CUSTOMER',
};

export const MAX_COUNT_ALLOWED_TO_SEND_TO_CUSTOMER = 5;

// ----------------------------------------------------------------------------------------- //
// ----------------------------------Job Part & Work Status--------------------------------- //
// ----------------------------------------------------------------------------------------- //

export const JOBS_SUMMARY_OPTIONS = ['PARTS'];

// ----------------------------------------------------------------------------------------- //
// -------------------------------Recommendation Approval Status---------------------------- //
// ----------------------------------------------------------------------------------------- //

export const RECOMMENDATION_APPROVAL_STATUS_LABEL = {
  READY_FOR_REVIEW: __('Ready for Review'),
  REVIEWED: __('Reviewed'),
  SENT_TO_CUSTOMER: __('Sent to Customer'),
  CUSTOMER_APPROVED: __('Customer Approved'),
  OTHER: __('Other'),
};

export const RECOMMENDATION_PART_STATUS = {
  [JOB_LEVEL_PART_STATUS_VALUE.NO_PARTS]: {
    label: '-',
    value: JOB_LEVEL_PART_STATUS_VALUE.NO_PARTS,
  },
  [JOB_LEVEL_PART_STATUS_VALUE.PARTS_PENDING]: {
    label: __('Parts Pending'),
    value: JOB_LEVEL_PART_STATUS_VALUE.PARTS_PENDING,
  },
  [JOB_LEVEL_PART_STATUS_VALUE.PARTS_PARTIALLY_FULFILLED]: {
    label: __('Parts Pending'),
    value: JOB_LEVEL_PART_STATUS_VALUE.PARTS_PARTIALLY_FULFILLED,
  },
  [JOB_LEVEL_PART_STATUS_VALUE.PARTS_FULFILLED]: {
    label: __('Parts Quoted'),
    value: JOB_LEVEL_PART_STATUS_VALUE.PARTS_FULFILLED,
  },
};

export const RECOMMENDATION_PART_STATUS_CONFIG = {
  [JOB_LEVEL_PART_STATUS_VALUE.NO_PARTS]: EMPTY_OBJECT,
  [JOB_LEVEL_PART_STATUS_VALUE.PARTS_PENDING]: {
    ...RECOMMENDATION_PART_STATUS[JOB_LEVEL_PART_STATUS_VALUE.PARTS_PENDING],
    color: STATUS_LEVEL.OPEN,
  },
  [JOB_LEVEL_PART_STATUS_VALUE.PARTS_PARTIALLY_FULFILLED]: {
    ...RECOMMENDATION_PART_STATUS[JOB_LEVEL_PART_STATUS_VALUE.PARTS_PARTIALLY_FULFILLED],
    color: STATUS_LEVEL.MEDIUM,
  },
  [JOB_LEVEL_PART_STATUS_VALUE.PARTS_FULFILLED]: {
    ...RECOMMENDATION_PART_STATUS[JOB_LEVEL_PART_STATUS_VALUE.PARTS_FULFILLED],
    color: STATUS_LEVEL.LOW,
  },
};

// ----------------------------------------------------------------------------------------- //
// -------------------------------Warranty Claim Status---------------------------- //
// ----------------------------------------------------------------------------------------- //

export const WARRANTY_CLAIM_STATUS_VALUES_SET = new Set(_values(WARRANTY_CLAIM_STATUS_VALUES));

// --------------------------------Tech Status-------------------------------------

export const TECHNICIAN_STATUS = {
  NOT_CLOCKIN_FOR_DAY: 'NOT_CLOCKIN_FOR_DAY',
  AVAILABLE_TO_WORK: 'AVAILABLE_TO_WORK',
  WORKING_ON_ANOTHER_JOB: 'WORKING_ON_ANOTHER_JOB',
};

export const TECH_STATUS_VS_COLOR = {
  [TECHNICIAN_STATUS.NOT_CLOCKIN_FOR_DAY]: colors.mordantRed,
  [TECHNICIAN_STATUS.AVAILABLE_TO_WORK]: colors.parisGreen,
  [TECHNICIAN_STATUS.WORKING_ON_ANOTHER_JOB]: colors.carrotOrange,
};

export const TECH_STATUS_VS_LABEL = {
  [TECHNICIAN_STATUS.NOT_CLOCKIN_FOR_DAY]: __('Not clocked in for the day'),
  [TECHNICIAN_STATUS.AVAILABLE_TO_WORK]: __('Available to work'),
  [TECHNICIAN_STATUS.WORKING_ON_ANOTHER_JOB]: __('Working on another job'),
};

// ----------------------------------------------------------------------------------------- //
// -------------------------------RO List Search Field Config------------------------------- //
// ----------------------------------------------------------------------------------------- //

export const SEARCH_FIELD_CONFIG = {
  ALL: { id: 'ALL', name: __('All') },
  CUSTOMER: { id: 'CUSTOMER', name: __('Customer') },
  VEHICLE: { id: 'VEHICLE', name: __('Vehicle') },
  RO_NO: { id: 'RO_NO', name: __('$$(RO) Number') },
  TAG_NO: { id: 'TAG_NO', name: __('Tag Number') },
  PHONE: { id: 'PHONE', name: __('Phone') },
  FLEET_NUMBER: { id: 'FLEET_NO', name: __('Fleet Number') },
};

export const SEARCHABLE_FIELDS_WITHOUT_ALL = _filter(
  SEARCH_FIELD_CONFIG,
  field => field.id !== SEARCH_FIELD_CONFIG.ALL.id
);

export const SEARCHABLE_FIELDS_OPTIONS = standardFieldOptionMapper('', _values(SEARCH_FIELD_CONFIG));

// ----------------------------------------------------------------------------------------- //
// -------------------------------Service CRM List ------------------------------- //
// ----------------------------------------------------------------------------------------- //

export const SERVICE_CRM_RO_SEARCH_FIELD_CONFIG = {
  ALL: { id: 'ALL', name: __('All') },
  CUSTOMER: { id: 'CUSTOMER', name: __('Customer') },
  VEHICLE: { id: 'VEHICLE', name: __('Vehicle') },
  RO_NO: { id: 'RO_NO', name: __('$$(RO) Number') },
  TAG_NO: { id: 'TAG_NO', name: __('Tag Number') },
};

export const SERVICE_CRM_RO_SEARCHABLE_FIELDS_WITHOUT_ALL = _filter(
  SERVICE_CRM_RO_SEARCH_FIELD_CONFIG,
  field => field.id !== SERVICE_CRM_RO_SEARCH_FIELD_CONFIG.ALL.id
);

export const SERVICE_CRM_RO_SEARCHABLE_FIELDS_OPTIONS = standardFieldOptionMapper(
  '',
  _values(SERVICE_CRM_RO_SEARCH_FIELD_CONFIG)
);

// ----------------------------------------------------------------------------------------- //
// -------------------------------QUOTES------------------------------- //
// ----------------------------------------------------------------------------------------- //

export const QUOTE_PART_STATUS = {
  REQUEST_PENDING: 'REQUEST_PENDING',
  REQUESTED: 'REQUESTED',
  DRAFTED: 'DRAFTED',
  QUOTED: 'QUOTED',
  REVIEWED: 'REVIEWED',
};

export const QUOTE_SERVICE_STATUS_CONFIG = {
  OPEN: { value: QUOTE_SERVICE_STATUS.OPEN, label: __('Open'), color: STATUS_LEVEL.OPEN },
  VOIDED: { value: QUOTE_SERVICE_STATUS.VOIDED, label: __('Void'), color: STATUS_LEVEL.VOID },
  DECLINED: { value: QUOTE_SERVICE_STATUS.DECLINED, label: __('Declined'), color: STATUS_LEVEL.HIGH },
  REVIEWED: { value: QUOTE_SERVICE_STATUS.REVIEWED, label: __('Reviewed'), color: STATUS_LEVEL.LOW },
  PNA_REQUESTED: { value: QUOTE_SERVICE_STATUS.PNA_REQUESTED, label: __('P&A Requested'), color: STATUS_LEVEL.MEDIUM },
  PNA_COMPLETED: { value: QUOTE_SERVICE_STATUS.PNA_COMPLETED, label: __('P&A Done'), color: STATUS_LEVEL.MEDIUM },
};

export const QUOTE_PART_STATUS_CONFIG = {
  REQUEST_PENDING: { value: QUOTE_PART_STATUS.REQUEST_PENDING, label: __('Request Pending'), color: STATUS_LEVEL.HIGH },
  REQUESTED: { value: QUOTE_PART_STATUS.REQUESTED, label: __('Requested'), color: STATUS_LEVEL.OPEN },
  DRAFTED: { value: QUOTE_PART_STATUS.DRAFTED, label: __('Draft'), color: STATUS_LEVEL.MEDIUM },
  QUOTED: { value: QUOTE_PART_STATUS.QUOTED, label: __('Quoted'), color: STATUS_LEVEL.LOW },
  REVIEWED: { value: QUOTE_PART_STATUS.REVIEWED, label: __('Reviewed'), color: STATUS_LEVEL.LOW },
};

export const RO_DETAILS_OPTIONS = {
  JOBS: 'JOB',
};

export const PART_TYPE_VS_PART_RO_SALES_ENTITY_TYPE = {
  [PART_TYPES.FULFILLMENT]: PART_ENTITY_TYPES.PARTS_RO_SALES.FULFILMENT,
  [PART_TYPES.PRICE_AND_AVAILABILITY]: PART_ENTITY_TYPES.PARTS_RO_SALES.PNA,
  [PART_TYPES.QUOTE_PRICE_AND_AVAILABILITY]: PART_ENTITY_TYPES.PARTS_RO_SALES.QUOTE,
  [PART_TYPES.APPOINTMENT]: PART_ENTITY_TYPES.PARTS_RO_SALES.APPOINTMENT,
};

export const VALIDATION_LEVELS = {
  JOB: 'Job',
  RO: 'RO',
  RO_AND_JOB: 'RO_AND_JOB',
  RECOMMENDATION: 'Recommendation',
  PAYER: 'PAYER',
};

export const RULE_LEVEL_VS_PAYLOAD = {
  [VALIDATION_LEVELS.RO]: [VALIDATION_LEVELS.RO],
  [VALIDATION_LEVELS.JOB]: [VALIDATION_LEVELS.JOB],
  [VALIDATION_LEVELS.RO_AND_JOB]: [VALIDATION_LEVELS.RO, VALIDATION_LEVELS.JOB],
  [VALIDATION_LEVELS.RECOMMENDATION]: [VALIDATION_LEVELS.RECOMMENDATION],
};

// ----------------------------------------------------------------------------------------- //
// ----------------------------------------RO Approval---------------------------------------- //
// ----------------------------------------------------------------------------------------- //

export const APPROVAL_REQUEST_TYPE_VALUES = {
  OTHER_LABOR_HOURS: 'OTHER_LABOR_HOURS',
  RECOMMENDATION_TO_JOB: 'RECOMMENDATION_TO_JOB',
};

export const APPROVAL_REQUEST_TYPE_OPTIONS = [
  {
    label: __('Additional Hours'),
    value: APPROVAL_REQUEST_TYPE_VALUES.OTHER_LABOR_HOURS,
  },
  {
    label: __('Recommendation'),
    value: APPROVAL_REQUEST_TYPE_VALUES.RECOMMENDATION_TO_JOB,
  },
];

export const APPROVAL_REQUEST_INSTANCE_TYPE = {
  RECOMMENDATION_TO_JOB: 'RecommendationToJob',
};

export const APPROVAL_STATUS = {
  REQUESTED: 'REQUESTED',
  APPROVED: 'APPROVED',
  VOIDED: 'VOIDED',
  REJECTED: 'REJECTED',
};

// ----------------------------------------------------------------------------------------- //
// ----------------------------------------Job Summary---------------------------------------- //
// ----------------------------------------------------------------------------------------- //

export const SUMMARY_TYPE_VS_PAYLOAD = {
  [CELL_RENDERER_OVERLAY_TYPES.JOBS]: currentRepairOrder => ({
    roId: currentRepairOrder,
    options: JOBS_SUMMARY_OPTIONS,
  }),
  [CELL_RENDERER_OVERLAY_TYPES.RECOMMENDATION]: currentRepairOrder => ({
    roId: currentRepairOrder,
    options: JOBS_SUMMARY_OPTIONS,
  }),
  [CELL_RENDERER_OVERLAY_TYPES.WARRANTY_JOB]: currentRepairOrder => ({ roIds: [currentRepairOrder] }),
  [CELL_RENDERER_OVERLAY_TYPES.JOB_LIST]: () => [RO_DETAILS_OPTIONS.JOBS],
};

export const CELL_RENDERER_OVERLAY_TYPES_VS_MODULE_TYPE = {
  [CELL_RENDERER_OVERLAY_TYPES.JOBS]: MODULE_TYPES.RO_JOB,
  [CELL_RENDERER_OVERLAY_TYPES.RECOMMENDATION]: MODULE_TYPES.RO_TECH_RECOMMENDATIONS,
};

export const SUMMARY_TYPE_VS_JOB_LIST = {
  [CELL_RENDERER_OVERLAY_TYPES.JOBS]: jobList => ({
    jobList,
  }),
  [CELL_RENDERER_OVERLAY_TYPES.RECOMMENDATION]: jobList => ({
    jobList,
  }),
  [CELL_RENDERER_OVERLAY_TYPES.WARRANTY_JOB]: (jobList, currentRepairOrder) => ({
    jobList: jobList[currentRepairOrder],
  }),
  [CELL_RENDERER_OVERLAY_TYPES.JOB_LIST]: jobList => ({
    jobList: tget(jobList, 'jobs', EMPTY_ARRAY),
  }),
};

export const REPAIR_ORDER_VIEW_MODE = {
  PDF_VIEWER: 'PDF_VIEWER',
  RO_DETAILS: 'RO_DETAILS',
};

export const TOTAL_ALPHABETS = 26;

export const INITIAL_ALPHABET = 'A';

export const VALIDATION_ERROR_TYPES = {
  ERROR: 'Error',
  WARNING: 'Warning',
};

export const RO_APP_TYPES = {
  RO_LIST_VIEW: 'roListView',
  RO_DETAILS_PAGE: 'roDetailsPage',
};

export const WARRANTY_APPLICABLE_INVOICE_TYPES_SET = new Set([2, 8, 9]);

export const SERVICE_INVOICE_ACTIVITY = 'ATE';

export const PAY_TYPE_VS_STATUSES_ELIGIBLE_TO_CLOSE_SET = {
  [PAY_TYPE_VALUES.CUSTOMER_PAY]: new Set([CUSTOMER_PAY_RO_STATUS_VALUE.PAID]),
  [PAY_TYPE_VALUES.WARRANTY]: new Set([WARRANTY_PAY_RO_STATUS_VALUE.INVOICED]),
  [PAY_TYPE_VALUES.INTERNAL]: new Set([
    INTERNAL_PAY_RO_STATUS_VALUE.INVOICED,
    INTERNAL_PAY_RO_STATUS_VALUE.REVIEW_REQUESTED,
  ]),
};
