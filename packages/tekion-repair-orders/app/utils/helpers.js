import _get from 'lodash/get';
import _toNumber from 'lodash/toNumber';
import _sortBy from 'lodash/sortBy';
import _head from 'lodash/head';
import _isEmpty from 'lodash/isEmpty';
import _flow from 'lodash/flow';
import _property from 'lodash/property';
import _map from 'lodash/map';
import _size from 'lodash/size';
import _castArray from 'lodash/castArray';
import _every from 'lodash/every';
import _some from 'lodash/some';
import _noop from 'lodash/noop';
import _keyBy from 'lodash/keyBy';
import _reduce from 'lodash/reduce';
import _join from 'lodash/join';

import { getJobNumber } from 'readers/Jobs.reader';
import UserReader from 'readers/User.reader';
import { getROPayTypeStatuses } from 'tbusiness/appServices/service/helpers/general';
import { getClientSideFilteredData } from 'tbase/utils/filter';
import { getErrorMessage } from 'utils';
import { tget } from 'tbase/utils/general';
import { getFeeOverrideFlags } from 'tbase/utils/fee.utils';
import { getCurrencySymbol } from '@tekion/tekion-base/formatters/formatCurrency';
import { getPostingLevelFromServiceSettings } from 'twidgets/appServices/service/helpers/payer.helper';
import { getDealerId, getTenantId } from 'utils/selectors';
import { REMOVE_ACTION } from 'tcomponents/molecules/tableInputField/constants/general';
import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import { PAY_TYPE_VALUES } from 'tbase/constants/payTypes';
import { CUSTOMER_PAY_RO_STATUS_VALUE, CASHIERING_RO_STATUS_VALUE } from 'tbase/constants/repairOrder/general';
import { PROMISE_TIME_ROUND_UP_VALUES_VS_MINUTES } from 'tbase/constants/promiseTime.constants';
import {
  BASE_PAY_TYPE_DEFAULT_KEYS,
  BASE_PAY_TYPE_VS_KEY_GETTER,
} from 'tbusiness/appServices/service/constants/payTypeSetup';
import { RO_CLOSE_LEVELS } from 'tbusiness/appServices/service/constants/payer.constants';

import { PAYMENT_METHOD_VALUE, PAY_TYPE_VS_STATUSES_ELIGIBLE_TO_CLOSE_SET } from 'utils/constants';

export const HTTP_CODES = {
  OK: 200,
  CREATED: 201,
};

export const getPaymentMethodPricingMethod = () => [
  { label: '%', value: PAYMENT_METHOD_VALUE.PERCENTAGE },
  { label: getCurrencySymbol(), value: PAYMENT_METHOD_VALUE.FLAT },
];

export function isAPICallSuccessful(response) {
  const metaCode = _get(response, 'status');
  return metaCode && (metaCode === HTTP_CODES.OK || metaCode === HTTP_CODES.CREATED);
}

export const getSortedROEntity = entities => _sortBy(entities, entity => _toNumber(getJobNumber(entity)));

export const getCloseROErrorDetails = err => _get(err, 'data.errorDetails', EMPTY_OBJECT);

export const getCloseROErrorMessage = (err, usersById) => {
  const userName = UserReader.name(usersById[_head(_get(getCloseROErrorDetails(err), 'params', EMPTY_ARRAY))]).trim();
  return _isEmpty(userName) ? getErrorMessage(err) : `${getErrorMessage(err)} for ${userName}`;
};

export const getRepeatedValueWithLastRowBoolean = values =>
  _map(values, (value, index) => ({
    ...value,
    isLastRow: index === _size(values) - 1,
  }));

export const PAY_TYPE_VALUES_VS_STATUS = {
  [PAY_TYPE_VALUES.CUSTOMER_PAY]: _flow([getROPayTypeStatuses, _property('roCustomerPayStatus')]),
  [PAY_TYPE_VALUES.WARRANTY]: _flow([getROPayTypeStatuses, _property('roWarrantyPayStatus')]),
  [PAY_TYPE_VALUES.INTERNAL]: _flow([getROPayTypeStatuses, _property('roInternalPayStatus')]),
};

const getFormattedFeeConfigs = feeConfigs =>
  _map(feeConfigs, feeConfig => ({ ...feeConfig, overrideFlags: getFeeOverrideFlags(feeConfig) }));

const formatFeeOption = fee => ({ ...fee, configs: getFormattedFeeConfigs(fee.configs || EMPTY_ARRAY) });

const getOptionData = option => _get(option, 'data.optionData');

export const getFilterOption = (searchKeys, selectedFilters, validFilterIdsForClientSideFiltering) => (
  option,
  input
) => {
  const filteredOption = getClientSideFilteredData([formatFeeOption(getOptionData(option))], {
    searchQuery: input,
    searchKeys,
    selectedFilters,
    validFilterIdsForClientSideFiltering,
  });

  return _size(filteredOption) > 0;
};

export const getPayTypesByBasePayType = payTypeConfigurations =>
  _reduce(
    payTypeConfigurations,
    (acc, payType) => {
      const { basePayType, subPayType, active } = payType || EMPTY_OBJECT;
      const basePayTypeDefaultKey = BASE_PAY_TYPE_VS_KEY_GETTER[basePayType];
      if (!active || !basePayTypeDefaultKey) return acc;
      return {
        ...acc,
        [basePayTypeDefaultKey]: [...acc[basePayTypeDefaultKey], subPayType],
      };
    },
    {
      [BASE_PAY_TYPE_DEFAULT_KEYS.ALL_CUSTOMER_PAY]: [],
      [BASE_PAY_TYPE_DEFAULT_KEYS.ALL_WARRANTY_PAY]: [],
      [BASE_PAY_TYPE_DEFAULT_KEYS.ALL_INTERNAL_PAY]: [],
    }
  );

export const getFeeSubPayTypes = (subPayTypes, payTypesByBasePayType) =>
  _reduce(
    subPayTypes,
    (acc, subPayType) => {
      const basePayTypeDefaultKey = BASE_PAY_TYPE_DEFAULT_KEYS[subPayType];
      return [...acc, ...(payTypesByBasePayType[basePayTypeDefaultKey] || [subPayType])];
    },
    []
  );

const getFormattedFeeConfigsForMultiPayer = (feeConfigs, payTypesByBasePayType) =>
  _map(feeConfigs, feeConfig => ({
    ...feeConfig,
    overrideFlags: getFeeOverrideFlags(feeConfig),
    subPayTypes: getFeeSubPayTypes(feeConfig?.subPayTypes, payTypesByBasePayType),
  }));

const getFormattedFeeOptionForMultiPayer = (fee, payTypesByBasePayType) => ({
  ...fee,
  configs: getFormattedFeeConfigsForMultiPayer(fee.configs, payTypesByBasePayType),
});

export const getFeeFilterOptionForMultiPayer = ({
  searchKeys,
  selectedFilters,
  validFilterIdsForClientSideFiltering,
  payTypesByBasePayType,
}) => (option, input) => {
  const filteredOption = getClientSideFilteredData(
    [getFormattedFeeOptionForMultiPayer(getOptionData(option), payTypesByBasePayType)],
    {
      searchQuery: input,
      searchKeys,
      selectedFilters,
      validFilterIdsForClientSideFiltering,
    }
  );

  return !_isEmpty(filteredOption);
};

const getOption = (labelKey, valueKey, setLabel = _noop) => option => ({
  label: setLabel(option) || option[labelKey],
  value: option[valueKey],
  optionData: option,
});
export const getOptionsList = (optionDataList, labelKey, valueKey, setLabel = _noop) =>
  _map(optionDataList, getOption(labelKey, valueKey, setLabel));

export const getRowActions = ({ original, tdProps }) => {
  const { isLastRow, disabled } = original;
  const isTableDisabled = tget(tdProps, 'rest.additional.isDisabled', false);
  if (isTableDisabled || isLastRow || disabled) return false;
  return _castArray(REMOVE_ACTION);
};

export const getJobSelectStatus = operations => {
  const isJobSelected = _every(operations, 'selected');
  return {
    selected: isJobSelected,
    indeterminate: !isJobSelected && _some(operations, 'selected'),
  };
};

export const getPromiseTimeMinuteSteps = promiseTimeRoundUp =>
  PROMISE_TIME_ROUND_UP_VALUES_VS_MINUTES[promiseTimeRoundUp] || 1;

export const getPunchTimeTagsByValue = punchTimeTags => _keyBy(punchTimeTags, 'value');

export const getAuditAssetId = entityId => _join([getTenantId(), getDealerId(), entityId], '_');

export const isCPReadyToClose = (cpStatus, cashieringStatus) =>
  PAY_TYPE_VS_STATUSES_ELIGIBLE_TO_CLOSE_SET[PAY_TYPE_VALUES.CUSTOMER_PAY].has(cpStatus) ||
  (getPostingLevelFromServiceSettings(false) === RO_CLOSE_LEVELS.BASE_PAY_TYPE &&
    cpStatus === CUSTOMER_PAY_RO_STATUS_VALUE.NA &&
    cashieringStatus === CASHIERING_RO_STATUS_VALUE.PAID);
