/* eslint-disable no-param-reassign */
import { produce } from 'immer';
import _get from 'lodash/get';
import _has from 'lodash/has';
import _set from 'lodash/set';
import _map from 'lodash/map';
import _setWith from 'lodash/setWith';
import _compact from 'lodash/compact';
import _isEmpty from 'lodash/isEmpty';
import _keyBy from 'lodash/keyBy';
import _keys from 'lodash/keys';
import _values from 'lodash/values';
import _forEach from 'lodash/forEach';
import _find from 'lodash/find';
import _isNil from 'lodash/isNil';
import _filter from 'lodash/filter';
import _merge from 'lodash/merge';
import _findIndex from 'lodash/findIndex';

import { handleActions } from 'redux-actions';
import { EMPTY_OBJECT, EMPTY_STRING, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import { findArrayIndex, removeMatchedItems, getLenderCodeNameOfLender, findArrayItem } from 'utils';
import Money from '@tekion/tekion-base/utils/money';
import {
  setMarketScanIdForVehicle,
  getBuyer,
  isMultipleDownPaymentsEnabled,
  getPrimaryVehicleDealVehicleId,
} from '@tekion/tekion-base/marketScan/readers/deal.reader';

import { CUSTOMER_TYPE } from 'constants/deal.constants';
import * as MarketScanReader from '@tekion/tekion-base/marketScan/readers/marketScan.reader';
import * as ColumnDataReader from '@tekion/tekion-base/marketScan/readers/columnData.reader';

import {
  PAYMENT_TYPES,
  DOWN_PAYMENT_TYPES,
  PAYMENT_TYPE_DOWNPAYMENT_TYPE_MAPPING,
  TAX_AND_ZIP_CODE_DETAILS_VS_KEY,
} from '@tekion/tekion-base/marketScan/constants/desking.constants';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';
import { getMomentValueOf } from '@tekion/tekion-base/utils/dateUtils';
import { getRebatesSuperset } from '@tekion/tekion-base/marketScan/readers/desking.reader';

import { getFeeFromSearch, getFeeCodes } from 'readers/feeUpdate.reader';
import CalcEngineProperties from 'utils/CalcEngineProperties';
import { filterHardAddsAccessoriesBasedOnLenderCode } from 'utils/dueBills.util';

import { MESSAGES } from 'constants/pages';
import {
  deletedIdsMapping,
  formatFNIsBasedOnPaymentType,
  setdaysToFirstPayment,
  formatAccessoriesBasedOnSetup,
  setRebatesInColumn,
  setFirstPaymentWaiverInColumn,
  getDownPaymentsForPaymentType,
  resetPaymentDataOnParamChange,
  getSelectedLenderDetails,
  getColumnRebatesWithModifiedFirstWaiverAmt,
  mergeRebatesSupersetAndColumnRebates,
  getUpdatedTaxAndZipCodeDetails,
  getUpdatedStateFeeTaxOptions,
} from '@tekion/tekion-base/marketScan/utils/desking.utils';
import ACTIONS from 'constants/desking.actionTypes';
import { DOWNPAYMENT_PERCENTAGE } from 'constants/desking.constants';

const initialState = {
  selected: EMPTY_STRING,
  deferredPayments: {},
  downPayments: [],
  dealJacket: [],
  deal: {
    fetching: false,
    data: EMPTY_OBJECT,
    error: null,
  },
  deskingpaymentDetails: [],
  fetchingMarketScanData: false,
  marketScan: {
    fetching: false,
    data: EMPTY_OBJECT,
    error: null,
  },
  fniAttributesCodes: {},
  fniProducts: {},
  fniProductsList: {},
  orderedFniProductsList: EMPTY_ARRAY,
  defaultPlanCodes: [],
  feeUpdateInfos: {},
  contract: {},
  scanDocuments: { docsetData: EMPTY_OBJECT, documents: EMPTY_ARRAY },
  isDirty: false,
  glAccount: {},
  dealSheetPrintPayload: EMPTY_STRING,
  contractValidations: [],
  packageDocuments: EMPTY_ARRAY,
  canCallMarketScan: true,
  showPdfLoaderDealJacket: false,
  defaultFees: EMPTY_ARRAY,
  blockEdocForthirtySec: false,
  mandatoryReviewFields: {},
  dealPreferences: {},
  dealershipInfo: EMPTY_OBJECT,
  signatureCordinateForEdocPackages: {},
  documentSignStatus: {},
  documentsToHighlightInDealJacket: EMPTY_ARRAY,
  changingDwnPmt: false,
  vehicleMakeAliases: {},
  routeOneErrors: EMPTY_OBJECT,
  showArchivedDocsInDealJacket: false,
  hiddenCompletionCertificates: EMPTY_ARRAY,
  documentSigningModal: EMPTY_STRING,
  eSignSessionInfo: {
    eSignAppUrl: EMPTY_STRING,
    visible: false,
  },
  mscanVehcileIdFetched: false,
  hasEstimatedDeliveryDateChanged: false,
  virtualMeetingRoomDetails: EMPTY_OBJECT,
  customerPIIData: EMPTY_OBJECT,
};

export default handleActions(
  {
    [ACTIONS.DESKING_DATA_RESET]: () => initialState,
    [ACTIONS.MODIFY_DEAL_LEVEL_INFO]: (state, { payload }) =>
      produce(state, draft => {
        _map(payload, (val, key) => {
          _set(draft, `deal.data.${key}`, val);
        });
      }),
    [ACTIONS.DESKING_SET_SELECTED_DEAL_DATA]: (state, { payload }) =>
      produce(state, draft => {
        draft.deal.data = payload;
      }),
    [ACTIONS.SET_CONCIERGE_FLAG]: state =>
      produce(state, draft => {
        _set(draft, 'deal.data.conciergeActivated', true);
      }),
    [ACTIONS.SET_MARKET_SCAN_VEHICLE_ID_IN_DEAL]: (state, { payload }) =>
      produce(state, draft => {
        const { dealVehicleId, marketScanId, grossWeight, unladenWeight, styleId, additionalOEMInfo, id } = payload;
        setMarketScanIdForVehicle(
          draft.deal.data,
          dealVehicleId,
          marketScanId,
          grossWeight,
          unladenWeight,
          styleId,
          additionalOEMInfo,
          id
        );
      }),
    [ACTIONS.SET_COLUMNS_DATA]: (state, { payload }) =>
      produce(state, draft => {
        draft.deskingpaymentDetails = payload;
      }),

    [ACTIONS.SET_PACKAGE_DOCS]: (state, { payload }) =>
      produce(state, draft => {
        draft.packageDocuments = payload;
      }),

    [ACTIONS.SET_SPP_PACKAGE_DOCS]: (state, { payload }) =>
      produce(state, draft => {
        draft.sppPackageDocuments = payload;
      }),

    [ACTIONS.SET_SCAN_DOCS]: (state, { payload }) =>
      produce(state, draft => {
        draft.scanDocuments = { ...state.scanDocuments, ...payload };
      }),
    [ACTIONS.SET_DOWN_PAYMENTS]: (state, { payload }) =>
      produce(state, draft => {
        draft.downPayments = payload;
      }),
    [ACTIONS.UPDATE_DEAL_JACKET_SUCCESS]: (state, { payload: { resolvedData, gdprConfig } }) =>
      produce(state, draft => {
        draft.dealJacket = resolvedData;
        draft.dealJacketGdprConfig = gdprConfig;
      }),

    [ACTIONS.UPDATE_CUSTOM_STATUS_SUCCESS]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, 'deal.data.subStatus', payload);
      }),
    [ACTIONS.SET_CUSTOMERS]: (state, { payload: customers }) =>
      produce(state, draft => {
        _set(draft, 'deal.data.customers', customers);
      }),

    [ACTIONS.SET_FETCHING_MARKET_SCAN_DATA_STATUS]: (state, { payload }) =>
      produce(state, draft => {
        draft.fetchingMarketScanData = payload;
      }),

    [ACTIONS.SET_SELECTED_FINANCE_MANAGER]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, 'deal.data.assignee.financeManager', [payload]);
      }),

    [ACTIONS.SET_SELECTED_SALES_MANAGER]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, 'deal.data.assignee.salesManager', [payload]);
      }),
    [ACTIONS.SAVE_TAXGROUP]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, 'deal.data.dmsTaxGroup', payload);
      }),

    [ACTIONS.SET_SELECTED_DOWN_PAYMENT]: (state, { payload }) =>
      produce(state, draft => {
        const { id, downPaymentValue, downPaymentId } = payload;
        const deskingpaymentDetails = draft.deskingpaymentDetails.map(item => {
          if (id === item.id) {
            return {
              ...item,
              selected: true,
              selectedDownPaymentId: downPaymentId,
              selectedDownPayment: downPaymentValue,
            };
          }
          return { ...item, selected: false };
        });

        draft.deskingpaymentDetails = deskingpaymentDetails;
      }),

    [ACTIONS.SET_DEFERRED_PAYMENT_DETAILS]: (state, { payload }) =>
      produce(state, draft => {
        const { deferredPayment, downpaymentId, deferredPayments } = payload;

        if (deferredPayments) {
          _setWith(draft, ['deferredPayments'], deferredPayments, Object);
        } else {
          const { deferredPayment1, deferredPayment2, deferredPayment3, deferredPayment4 } = deferredPayment;
          _setWith(
            draft,
            ['deferredPayments', downpaymentId],
            {
              deferredPayment1,
              deferredPayment2,
              deferredPayment3,
              deferredPayment4,
            },
            Object
          );
        }
      }),

    [ACTIONS.ON_VIEW_ON_DESKING_REPLACE_LENDER_WITH_NEW_LENDER_DETAILS]: (state, { payload }) =>
      produce(state, draft => {
        const { id, newLenderId, newLenderCodeName, newTier } = payload;
        draft.deskingpaymentDetails.forEach((columnData, index) => {
          if (columnData.id !== id) return;

          const newTermPaymentDetails = [];
          const termPaymentDetails = ColumnDataReader.getTermPaymentInfoForSelectedLender(columnData);
          const downPayments = Object.keys(termPaymentDetails || {});
          downPayments.forEach(downPaymentId => {
            const downPaymentDetails = _get(termPaymentDetails, [downPaymentId]) || {};
            const newDownPaymentDetails = { ...downPaymentDetails, lenderId: newLenderId };
            newTermPaymentDetails.push(newDownPaymentDetails);
          });
          _setWith(
            draft.deskingpaymentDetails[index],
            ['termPaymentDetails', newLenderId],
            newTermPaymentDetails,
            Object
          );
          _setWith(draft.deskingpaymentDetails[index], 'selectedLenderId', newLenderId, Object);
          _setWith(draft.deskingpaymentDetails[index], ['selectedLender'], newLenderCodeName, Object);
          _setWith(draft.deskingpaymentDetails[index], ['selectedTier'], newTier, Object);
        });
      }),

    [ACTIONS.SET_SELECTED_LENDER]: (state, { payload }) =>
      produce(state, draft => {
        const { id, defaultTermPaymentsData, selectedLenderCodeName, selectedLenderId, selectedTier } = payload;

        draft.deskingpaymentDetails.forEach((deskingPaymentDetail, index) => {
          if (deskingPaymentDetail.id !== id) return;

          const existingTermPaymentDetail = _get(draft.deskingpaymentDetails, [
            index,
            'termPaymentDetails',
            selectedLenderId,
          ]);
          if (_isEmpty(existingTermPaymentDetail)) {
            const defaultTermPayment = _get(defaultTermPaymentsData, [deskingPaymentDetail.id]) || {};
            _setWith(
              draft.deskingpaymentDetails[index],
              ['termPaymentDetails', selectedLenderId],
              defaultTermPayment,
              Object
            );
          }
          _setWith(draft.deskingpaymentDetails[index], ['selectedLender'], selectedLenderCodeName, Object);
          _setWith(draft.deskingpaymentDetails[index], ['selectedLenderId'], selectedLenderId, Object);
          _setWith(draft.deskingpaymentDetails[index], ['selectedTier'], selectedTier, Object);
        });
      }),

    [ACTIONS.SET_SELECTED_CASH_LENDER]: (state, { payload }) =>
      produce(state, draft => {
        const { columnId, lender } = payload;

        draft.deskingpaymentDetails.forEach((deskingPaymentDetail, index) => {
          if (deskingPaymentDetail.id !== columnId) return;

          const { id: lenderId, osfLender } = lender;
          const lenderCodeName = getLenderCodeNameOfLender(lender);

          const termPaymentDetails = ColumnDataReader.getTermPaymentInfoForSelectedLender(deskingPaymentDetail);
          const selectedLenderIdOld = ColumnDataReader.getSelectedLenderId(deskingPaymentDetail);

          if (lenderId) {
            _setWith(draft.deskingpaymentDetails[index], ['selectedLender'], lenderCodeName, Object);
            _setWith(draft.deskingpaymentDetails[index], ['selectedLenderId'], lenderId, Object);
            _setWith(draft.deskingpaymentDetails[index], ['termPaymentDetails', lenderId], termPaymentDetails, Object);
          }

          const downPayments = Object.keys(termPaymentDetails || {});
          downPayments.forEach(downPaymentId => {
            // setting osfLender=false if user want to remove lender for cash
            _set(
              draft.deskingpaymentDetails[index],
              ['termPaymentDetails', lenderId || selectedLenderIdOld, downPaymentId, 'osfLender'],
              osfLender
            );
            if (lenderId) {
              _set(
                draft.deskingpaymentDetails[index],
                ['termPaymentDetails', lenderId, downPaymentId, 'lenderId'],
                lenderId
              );
              _set(
                draft.deskingpaymentDetails[index],
                ['termPaymentDetails', lenderId, downPaymentId, 'lender'],
                lenderCodeName
              );
            }
          });
        });
      }),

    [ACTIONS.SET_CHANGING_DWN_PMT]: (state, { payload }) => ({ ...state, changingDwnPmt: payload }),

    [ACTIONS.DESKING_ON_DOWN_PAYMENT_CHANGE]: (state, { payload }) =>
      produce(state, draft => {
        const { id: indexInPayload, downPayment: changedDownPayment, downPaymentType } = payload;
        if (!_has(payload, 'id')) {
          return;
        }
        const downPayment = Number(changedDownPayment);
        const isMultipleDownPaymentsFlagEnabled = isMultipleDownPaymentsEnabled(_get(draft, ['deal', 'data']));
        const downPaymentsArray = Array.isArray(downPaymentType) ? downPaymentType : [downPaymentType];
        const downPaymentTypes =
          isMultipleDownPaymentsFlagEnabled && !_isEmpty(downPaymentsArray)
            ? downPaymentsArray
            : _values(DOWN_PAYMENT_TYPES);

        draft.deskingpaymentDetails.forEach(deskingPaymentDetail => {
          const selectedLenderId = ColumnDataReader.getSelectedLenderId(deskingPaymentDetail);
          const paymentType = ColumnDataReader.getPaymentType(deskingPaymentDetail);
          const downPaymentTypeForColumn = PAYMENT_TYPE_DOWNPAYMENT_TYPE_MAPPING[paymentType];
          if (downPaymentTypes.includes(downPaymentTypeForColumn)) {
            _set(deskingPaymentDetail, 'selectedDownPayment', changedDownPayment);
            _set(deskingPaymentDetail, 'selectedDownPaymentId', indexInPayload);
            _set(
              deskingPaymentDetail,
              ['termPaymentDetails', selectedLenderId, indexInPayload, 'downPayment'],
              downPayment
            );
            _set(
              deskingPaymentDetail,
              ['termPaymentDetails', selectedLenderId, indexInPayload, 'calcBasedOnDownPmtPct'],
              false
            );
          }
        });

        downPaymentTypes.forEach(dt => {
          _set(draft, ['downPayments', indexInPayload, dt], downPayment);
        });
      }),
    [ACTIONS.DESKING_ON_DOWN_PAYMENT_PERCENTAGE_CHANGE]: (state, { payload }) =>
      produce(state, draft => {
        const { downPaymentPercentage: changedDownPaymentPercentage, id: indexInPayload } = payload;
        if (!_has(payload, 'id')) {
          return;
        }
        const downPaymentPercentage = Number(changedDownPaymentPercentage);
        draft.deskingpaymentDetails.forEach(deskingPaymentDetail => {
          const selectedLenderId = ColumnDataReader.getSelectedLenderId(deskingPaymentDetail);
          _set(
            deskingPaymentDetail,
            ['termPaymentDetails', selectedLenderId, indexInPayload, DOWNPAYMENT_PERCENTAGE],
            downPaymentPercentage
          );
          _set(
            deskingPaymentDetail,
            ['termPaymentDetails', selectedLenderId, indexInPayload, 'calcBasedOnDownPmtPct'],
            true
          );
        });
      }),
    [ACTIONS.SET_MULTIPLE_DOWNPAYMENT_STATUS]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, ['deal', 'data', 'multipleDownPaymentsEnabled'], payload);
      }),

    [ACTIONS.SET_SECURITY_DEPOSIT]: (state, { payload }) =>
      produce(state, draft => {
        const { id, securityDepositType, securityDepositOverridden, securityDeposit } = payload;

        draft.deskingpaymentDetails.forEach((deskingPaymentDetail, index) => {
          if (deskingPaymentDetail.id !== id) return;
          const { selectedLenderId } = deskingPaymentDetail;
          const downPaymentIds = Object.keys(
            _get(deskingPaymentDetail, ['termPaymentDetails', selectedLenderId]) || EMPTY_OBJECT
          );
          downPaymentIds.forEach(downPaymentId => {
            const program = _get(deskingPaymentDetail, ['termPaymentDetails', selectedLenderId, downPaymentId]);
            // _set(program, ['securityDepositId'], securityDepositId);
            _set(program, ['securityDepositType'], securityDepositType);
            _set(program, ['securityDeposit'], securityDeposit);
            _set(program, ['securityDepositOverridden'], securityDepositOverridden);
            _set(draft.deskingpaymentDetails, [index, 'termPaymentDetails', selectedLenderId, downPaymentId], program);
          });
        });
      }),

    [ACTIONS.CHANGE_APR_MANUAL_FLAG]: (state, { payload }) =>
      produce(state, draft => {
        const { manuallyUpdated } = payload;
        draft.deskingpaymentDetails.forEach((deskingPaymentDetail, index) => {
          const { selectedLenderId } = deskingPaymentDetail;
          const downPaymentIds = Object.keys(
            _get(deskingPaymentDetail, ['termPaymentDetails', selectedLenderId]) || EMPTY_OBJECT
          );
          downPaymentIds.forEach(downPaymentId => {
            const program = _get(deskingPaymentDetail, ['termPaymentDetails', selectedLenderId, downPaymentId]);
            program.apr = { ...program.apr, manuallyUpdated };
            _set(draft.deskingpaymentDetails, [index, 'termPaymentDetails', selectedLenderId, downPaymentId], program);
          });
        });
      }),
    [ACTIONS.CHANGE_APR]: (state, { payload }) =>
      produce(state, draft => {
        const { id, aprData } = payload;
        const { apr = 0, buyRate = 0, markUp = 0, financeReserve = 0, ...restData } = aprData;

        draft.deskingpaymentDetails.forEach((deskingPaymentDetail, index) => {
          if (deskingPaymentDetail.id !== id) return;
          const { selectedLenderId } = deskingPaymentDetail;
          const downPaymentIds = Object.keys(
            _get(deskingPaymentDetail, ['termPaymentDetails', selectedLenderId]) || EMPTY_OBJECT
          );
          _forEach(downPaymentIds, downPaymentId => {
            const program = _get(deskingPaymentDetail, ['termPaymentDetails', selectedLenderId, downPaymentId]);
            program.apr = {
              ...program.apr,
              apr,
              buyRate,
              markUp,
              financeReserve,
              ...restData,
            };
            _set(draft.deskingpaymentDetails, [index, 'termPaymentDetails', selectedLenderId, downPaymentId], program);
          });
        });
      }),

    [ACTIONS.CHANGE_RESIDUAL_DATA]: (state, { payload }) =>
      produce(state, draft => {
        const { id, lender, residualData = EMPTY_OBJECT, resetProgramId } = payload?.residualInfo;

        draft.deskingpaymentDetails.forEach((deskingPaymentDetail, index) => {
          if (deskingPaymentDetail.id !== id) return;
          const downPayments = Object.keys(_get(deskingPaymentDetail, ['termPaymentDetails', lender]) || EMPTY_OBJECT);
          downPayments.forEach(downPayment => {
            const program = _get(deskingPaymentDetail, ['termPaymentDetails', lender, downPayment]);
            if (resetProgramId) {
              program.programId = '';
            }

            program.residual = {
              ...program.residual,
              ...residualData,
              manuallyEdited: true,
              demoMilesResidualAdjustment: payload?.resetPenaltyOnResidualChange
                ? null
                : residualData?.demoMilesResidualAdjustment,
              demoMilesResidualAdjustmentPct: payload?.resetPenaltyOnResidualChange
                ? null
                : residualData?.demoMilesResidualAdjustmentPct,
            };

            if (payload?.resetPenaltyOnResidualChange) {
              program.yearlyMiles = {
                ...program.yearlyMiles,
                totalPenalty: null,
                demoMilesResidualAdjustment: null,
                demoMilesResidualAdjustmentPct: null,
              };
            }

            _setWith(draft.deskingpaymentDetails[index], ['termPaymentDetails', lender, downPayment], program, Object);
          });
        });
      }),
    [ACTIONS.CHANGE_RESIDUAL_DATA_ON_CHANGE_YEARLY_MILES]: (state, { payload }) =>
      produce(state, draft => {
        const { residualData = EMPTY_OBJECT } = payload;

        draft.deskingpaymentDetails.forEach((deskingPaymentDetail, index) => {
          const { id, selectedLenderId } = deskingPaymentDetail;
          const downPayments = Object.keys(
            _get(deskingPaymentDetail, ['termPaymentDetails', selectedLenderId]) || EMPTY_OBJECT
          );
          if (!residualData[id]) return;
          downPayments.forEach(downPayment => {
            const program = _get(deskingPaymentDetail, ['termPaymentDetails', selectedLenderId, downPayment]);
            program.residual = {
              ...program.residual,
              ...residualData[id],
            };
            _setWith(
              draft.deskingpaymentDetails[index],
              ['termPaymentDetails', selectedLenderId, downPayment],
              program,
              Object
            );
          });
        });
      }),
    [ACTIONS.CHANGE_YEARLY_MILES]: (state, { payload }) =>
      produce(state, draft => {
        const { id, lender: targetLender, yearlyMilesInfo, applyToAll } = payload;

        draft.deskingpaymentDetails.forEach((deskingPaymentDetail, index) => {
          if (!applyToAll && deskingPaymentDetail.id !== id) return;

          const { paymentOption } = deskingPaymentDetail;
          const { paymentType } = paymentOption;
          if (![PAYMENT_TYPES.LEASE, PAYMENT_TYPES.ONE_TIME_LEASE, PAYMENT_TYPES.BALLOON].includes(paymentType)) return;

          const termPaymentDetails = _get(deskingPaymentDetail, 'termPaymentDetails') || EMPTY_OBJECT;
          const existingAllLenders = Object.keys(termPaymentDetails);
          const lendersToUse = applyToAll ? existingAllLenders : [targetLender];

          lendersToUse.forEach(lender => {
            const downPayments = Object.keys(
              _get(deskingPaymentDetail, ['termPaymentDetails', lender]) || EMPTY_OBJECT
            );
            downPayments.forEach(downPayment => {
              const program = _get(deskingPaymentDetail, ['termPaymentDetails', lender, downPayment]);
              program.yearlyMiles = {
                ...program.yearlyMiles,
                ...yearlyMilesInfo,
              };
              _setWith(
                draft.deskingpaymentDetails[index],
                ['termPaymentDetails', lender, downPayment],
                program,
                Object
              );
            });
          });
        });
      }),

    [ACTIONS.SET_YEARLY_MILES]: (state, { payload }) =>
      produce(state, draft => {
        const { yearlyMilesInfo } = payload;
        draft.deskingpaymentDetails.forEach(column => {
          const { id } = column;
          const info = yearlyMilesInfo[id];
          if (info) {
            const termPaymentDetails = _get(column, 'termPaymentDetails') || EMPTY_OBJECT;
            _forEach(termPaymentDetails, downpaymentWise => {
              _forEach(downpaymentWise, program => {
                program.yearlyMiles = {
                  ...program.yearlyMiles,
                  ...info,
                };
              });
            });
          }
        });
      }),

    [ACTIONS.CHANGE_DAYS_TO_FIRST_PAYMENT]: (state, { payload }) =>
      produce(state, draft => {
        const { id, daysToFirstPayment, applyToAll = false } = payload;

        draft.deskingpaymentDetails.forEach((deskingPaymentDetail, index) => {
          if (!applyToAll && deskingPaymentDetail.id !== id) return;

          const { paymentOption } = deskingPaymentDetail;
          const { paymentType } = paymentOption;
          if ([PAYMENT_TYPES.CASH].includes(paymentType)) return;

          setdaysToFirstPayment(deskingPaymentDetail, daysToFirstPayment);

          const termPaymentDetails = _get(deskingPaymentDetail, 'termPaymentDetails') || EMPTY_OBJECT;

          Object.keys(termPaymentDetails).forEach(lender => {
            const downPayments = Object.keys(
              _get(deskingPaymentDetail, ['termPaymentDetails', lender]) || EMPTY_OBJECT
            );
            downPayments.forEach(downPayment => {
              const program = _get(deskingPaymentDetail, ['termPaymentDetails', lender, downPayment]);
              _setWith(
                draft.deskingpaymentDetails[index],
                ['termPaymentDetails', lender, downPayment],
                program,
                Object
              );
            });
          });
        });
      }),

    [ACTIONS.SET_PAYMENT_OPTIONS]: (state, { payload }) =>
      produce(state, draft => {
        const { id, paymentType, paymentSubType, value, paymentFrequency, newColumnId, programType } = payload;
        const deskingpaymentDetails = draft.deskingpaymentDetails.map(item => {
          if (id === item.id) {
            const { paymentOption } = item;
            return {
              ...item,
              id: newColumnId,
              paymentOption: {
                ...paymentOption,
                paymentType,
                paymentSubType: paymentSubType || null,
                value,
                paymentFrequency,
                programType: programType || paymentOption?.programType || null,
              },
            };
          }
          return item;
        });

        draft.deskingpaymentDetails = deskingpaymentDetails;
      }),

    [ACTIONS.SET_NEW_COLUMN_IDS]: (state, { payload }) =>
      produce(state, draft => {
        draft.deskingpaymentDetails.forEach(item => {
          if (payload[item.id]) {
            _set(item, 'id', payload[item.id]);
          }
        });
      }),

    [ACTIONS.DESKING_SET_PAYMENT_FREQUENCY_TYPE]: (state, { payload }) =>
      produce(state, draft => {
        const { id, newFrequency: paymentFrequency } = payload;
        const deskingpaymentDetails = draft.deskingpaymentDetails.map(item => {
          if (item.id === id) {
            const { paymentOption } = item;
            return {
              ...item,
              paymentOption: { ...paymentOption, paymentFrequency },
            };
          }
          return item;
        });
        draft.deskingpaymentDetails = deskingpaymentDetails;
      }),

    [ACTIONS.DESKING_SET_DEAL_TYPE]: (state, { payload }) =>
      produce(state, draft => {
        draft.deal.data.type = payload;
      }),

    [ACTIONS.SET_FNI_SURVEY_STATUS]: (state, { payload }) =>
      produce(state, draft => {
        draft.deal.data.fniSurveyStatus = payload;
      }),

    [ACTIONS.DESKING_SET_DEAL_STATUS]: (state, { payload }) =>
      produce(state, draft => {
        draft.deal.data.status = payload;
      }),

    [ACTIONS.DESKING_SET_DEAL_ACTIVITY]: (state, { payload }) =>
      produce(state, draft => {
        draft.deal.data.activity = payload;
      }),

    [ACTIONS.DESKING_SET_CONTRACT_DATE]: (state, { payload }) =>
      produce(state, draft => {
        draft.deal.data.soldTime = payload;
      }),

    [ACTIONS.DESKING_SET_DEAL_ACCOUNT_POSTING_ID]: (state, { payload }) =>
      produce(state, draft => {
        draft.deal.data.accountPostingId = payload;
        draft.deal.data.postedToAccounting = true;
      }),

    [ACTIONS.DEAL_SET_PRIMARY_VEHICLE_DATA]: (state, { payload }) =>
      produce(state, draft => {
        payload = payload && Array.isArray(payload) ? payload : [payload];
        const deal = draft.deal.data;
        const matchingIndex = findArrayIndex(_get(deal, 'vehicles'), 'primaryVehicle', true);
        if (matchingIndex !== -1) {
          const primaryVehicle = deal.vehicles[matchingIndex];

          _forEach(payload, item => {
            const { key, value } = item;
            const keys = Array.isArray(key) ? key : key.split('.');
            _set(primaryVehicle, keys, value);
          });

          draft.deal.data = deal;
        }
      }),

    [ACTIONS.DEAL_SET_VEHICLE_DATA]: (state, { payload }) =>
      produce(state, draft => {
        const { key, value, dealVehicleId } = payload;
        const deal = draft.deal.data;
        const matchingIndex = findArrayIndex(_get(deal, 'vehicles'), 'dealVehicleId', dealVehicleId);
        if (dealVehicleId && matchingIndex !== -1) {
          const primaryVehicle = deal.vehicles[matchingIndex];
          const keys = Array.isArray(key) ? key : key.split('.');
          _set(primaryVehicle, keys, value);
          draft.deal.data = deal;
        }
      }),

    [ACTIONS.DEAL_SET_PRIMARY_CUSTOMER_DATA]: (state, { payload }) =>
      produce(state, draft => {
        const { key, value } = payload;
        const deal = draft.deal.data;
        let buyer = getBuyer(deal);
        if (!buyer) {
          buyer = { type: CUSTOMER_TYPE.BUYER };
          _set(deal, 'customers', [buyer]);
        }
        _set(buyer, key.split('.'), value);
        draft.deal.data = deal;
      }),

    [ACTIONS.DESKING_REMOVE_COLUMN]: (state, { payload }) =>
      produce(state, draft => {
        const id = payload;
        const deskingpaymentDetails = removeMatchedItems(draft.deskingpaymentDetails, 'id', id);
        const selectedColumnIndex = deskingpaymentDetails.findIndex(({ selected }) => selected);
        if (selectedColumnIndex < 0 && deskingpaymentDetails.length > 0) {
          const primaryVehicleId = getPrimaryVehicleDealVehicleId(draft.deal.data);
          const primaryVehicleFirstColumnIndex =
            deskingpaymentDetails.findIndex(({ dealVehicleId }) => dealVehicleId === primaryVehicleId) || 0;
          _setWith(deskingpaymentDetails, [primaryVehicleFirstColumnIndex, 'selected'], true, Object);
        }
        draft.deskingpaymentDetails = deskingpaymentDetails;
      }),

    [ACTIONS.DEAL_SET_BUYER_ADDRESS]: (state, { payload }) =>
      produce(state, draft => {
        const { addressType, key, value } = payload;
        const { customers } = draft.deal.data;
        const buyerIndex = customers.findIndex(({ type }) => type === 'BUYER');
        const buyeRInfo = getBuyer(draft.deal.data);
        let buyerAddresses = _get(buyeRInfo, 'address') || [];

        if (!buyerAddresses || buyerAddresses.findIndex(({ addressType: at }) => at === addressType) < 0) {
          buyerAddresses = _compact([{ addressType: 'CURRENT' }, ...buyerAddresses]);
          _setWith(customers, [buyerIndex, 'address'], buyerAddresses, Object);
        }
        const updateAddressIndex = buyerAddresses.findIndex(({ addressType: at }) => at === addressType);
        if (key) _setWith(buyerAddresses, [updateAddressIndex, key], value, Object);
        else
          buyerAddresses[updateAddressIndex] = {
            ...buyerAddresses[updateAddressIndex],
            ...value,
          };
        _setWith(customers, [buyerIndex, 'address'], buyerAddresses, Object);
        draft.deal.data.customers = customers;
      }),

    [ACTIONS.DEAL_SET_TAX_AND_ZIP_CODE_DETAILS]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, 'deal.data.taxAndZipCodeDetails', getUpdatedTaxAndZipCodeDetails(draft, payload));
      }),

    [ACTIONS.DEAL_SET_TAX_CODE]: (state, { payload }) =>
      produce(state, draft => {
        const { value } = payload;
        draft.deal.data.taxCode = value;
      }),

    [ACTIONS.DESKING_ADD_NEW_COLUMN]: (state, { payload }) =>
      produce(state, draft => {
        const { columnData, referenceIndex } = payload;
        if (!_isNil(referenceIndex)) {
          const columnsBeforeReferenceIndex = draft.deskingpaymentDetails.slice(0, referenceIndex + 1);
          const columnsAfterReferenceIndex = draft.deskingpaymentDetails.slice(
            referenceIndex + 1,
            draft.deskingpaymentDetails.length
          );
          draft.deskingpaymentDetails = [...columnsBeforeReferenceIndex, columnData, ...columnsAfterReferenceIndex];
        } else {
          draft.deskingpaymentDetails.push(columnData);
        }
      }),

    [ACTIONS.DESKING_REPLACE_COLUMN]: (state, { payload }) =>
      produce(state, draft => {
        const { id, columnData: newColumnData } = payload;
        const matchingIndex = draft.deskingpaymentDetails.findIndex(({ id: columnId }) => id === columnId);
        draft.deskingpaymentDetails[matchingIndex] = newColumnData;
      }),

    [ACTIONS.DEAL_SET_TRADE_IN_VEHICLES]: (state, { payload }) =>
      produce(state, draft => {
        const { id, key, value } = payload;
        const tradeIns = _get(draft, 'deal.data.tradeIns') || [];
        tradeIns[id] = tradeIns[id] ? tradeIns[id] : {};
        _set(tradeIns[id], key.split('.'), value);
        _set(draft, ['deal', 'data', 'tradeIns'], tradeIns);
      }),

    [ACTIONS.DEAL_DELETE_TRADE_IN_VEHICLE]: (state, { payload }) =>
      produce(state, draft => {
        const { id } = payload;
        const tradeIns = _get(draft, 'deal.data.tradeIns');
        if (tradeIns) tradeIns.splice(id, 1);
        _set(draft, ['deal', 'data', 'tradeIns'], tradeIns);
      }),

    [ACTIONS.DEAL_SET_TRADE_IN_DEAL]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, ['deal', 'data', 'tradeIns'], payload);
      }),

    [ACTIONS.DESKING_FEE_COLUMN_SAVE]: (state, { payload }) =>
      produce(state, draft => {
        const { collectRegFee, columnFees, deletedFees } = payload;

        const hasDeletedFees = _has(payload, 'deletedFees');

        draft.deskingpaymentDetails.forEach(column => {
          const { id } = column;
          const newFees = columnFees[id];
          if (newFees) {
            const termPaymentDetails = _get(column, 'termPaymentDetails') || EMPTY_OBJECT;
            _forEach(termPaymentDetails, downpaymentWise => {
              _forEach(downpaymentWise, program => {
                program.dealFees = newFees;
                program.collectRegFee = collectRegFee;
                if (hasDeletedFees) {
                  program.manuallyDeletedFees = deletedFees[id];
                }
              });
            });
          }
        });
      }),

    [ACTIONS.UPDATE_DEAL_FEES]: (state, { payload }) =>
      produce(state, draft => {
        _forEach(payload, data => {
          const { selectedLenderId, columnIndex, downPaymentId, updatedFees } = data;
          _set(
            draft.deskingpaymentDetails,
            [columnIndex, 'termPaymentDetails', selectedLenderId, downPaymentId, 'dealFees'],
            updatedFees
          );
        });
      }),

    [ACTIONS.DESKING_SAVE_DEFAULT_FEE]: (state, { payload }) =>
      produce(state, draft => {
        draft.defaultFees = payload;
      }),

    [ACTIONS.DESKING_ADD_NEW_EMPTY_FEE_TO_ALL_COLUMN]: (state, { payload }) =>
      produce(state, draft => {
        const emptyFee = payload;
        const deskingpaymentDetails = draft.deskingpaymentDetails.map(dpd => ({
          ...dpd,
          dealFees: [...dpd.dealFees, emptyFee],
        }));
        draft.deskingpaymentDetails = deskingpaymentDetails;
      }),

    [ACTIONS.DESKING_REMOVE_UNNWANTED_FEE]: (state, { payload }) =>
      produce(state, draft => {
        deletedIdsMapping(draft.deskingpaymentDetails, payload);
      }),

    [ACTIONS.RECAP_SAVE_FNI]: (state, { payload }) =>
      produce(state, draft => {
        const { columnId, fnIs } = payload;
        const deskingpaymentDetails = draft.deskingpaymentDetails.map(dpd => {
          const { id } = dpd;
          if (!columnId || id === columnId) {
            return {
              ...dpd,
              fnIs,
            };
          }

          return dpd;
        });
        draft.deskingpaymentDetails = deskingpaymentDetails;
      }),

    [ACTIONS.DESKING_SAVE_FNI]: (state, { payload }) =>
      produce(state, draft => {
        const {
          fnis,
          fniCodesForPaymentTypes,
          onInit = false,
          dealType,
          columnIds,
          sppPaymentOption = EMPTY_OBJECT,
          updateSppPaymentOption = false,
        } = payload;

        // dont save if deal type has changed
        const deal = draft.deal.data;
        if (_get(deal, 'type') !== dealType) return;
        const deskingpaymentDetails = draft.deskingpaymentDetails.map(dpd => {
          const { id } = dpd;
          const paymentType = _get(dpd, ['paymentOption', 'paymentType']);
          const formattedFNIs = formatFNIsBasedOnPaymentType({
            fnis,
            fniCodesForPaymentTypes,
            paymentType,
            onInit,
          });
          const shouldUpdateFeesForCol = columnIds && columnIds.includes(id);

          if (shouldUpdateFeesForCol) {
            return {
              ...dpd,
              ...(updateSppPaymentOption && { sppPaymentOption }),
              fnIs: formattedFNIs.map(fni => ({
                ...fni,
                profit: Money.subtract(fni.price, fni.cost),
              })),
            };
          }
          return dpd;
        });
        draft.deskingpaymentDetails = deskingpaymentDetails;
      }),

    [ACTIONS.SET_FNIS_IN_COLUMN_IDS]: (state, { payload }) =>
      produce(state, draft => {
        const { fnIs, columnIds } = payload;

        draft.deskingpaymentDetails.forEach(column => {
          const { id } = column;
          const shouldUpdateFnisForCol = columnIds && columnIds.includes(id);

          if (shouldUpdateFnisForCol) {
            _set(column, 'fnIs', fnIs);
          }
        });
      }),

    [ACTIONS.ADD_REBATES_IN_DESKING]: (state, { payload }) =>
      produce(state, draft => {
        draft.deskingpaymentDetails.map(columnData => setRebatesInColumn(columnData, payload));
      }),

    [ACTIONS.MODIFY_WAIVER_REBATE_IN_DESKING]: state =>
      produce(state, draft => {
        draft.deskingpaymentDetails.map(columnData =>
          setRebatesInColumn(columnData, getColumnRebatesWithModifiedFirstWaiverAmt(columnData))
        );
      }),

    [ACTIONS.SET_VALID_REBATES_IN_COLUMN]: (state, { payload }) =>
      produce(state, draft => {
        const { selectedColumnId } = payload;
        if (selectedColumnId) {
          const rebatesSuperset = getRebatesSuperset({
            deskingpaymentDetails: state.deskingpaymentDetails,
            selectedColumnId,
          });
          const columnDataIndex = draft.deskingpaymentDetails.findIndex(({ id }) => id === selectedColumnId);
          const columnData = draft.deskingpaymentDetails[columnDataIndex];
          draft.deskingpaymentDetails[columnDataIndex] = setRebatesInColumn(
            columnData,
            mergeRebatesSupersetAndColumnRebates(rebatesSuperset, columnData)
          );
        }
        // add else part if needed to set in all columns
      }),

    [ACTIONS.MODIFY_FIRST_PAYMENT_WAIVER]: (state, { payload }) =>
      produce(state, draft => {
        const { rebates, selectedColumnId } = payload;
        const waiverAmount = _get(
          _find(rebates, ({ firstMonthWaiver }) => firstMonthWaiver),
          'rebateAmount'
        );
        if (!_isNil(waiverAmount)) {
          if (selectedColumnId) {
            const columnDataIndex = draft.deskingpaymentDetails.findIndex(({ id }) => id === selectedColumnId);
            draft.deskingpaymentDetails[columnDataIndex] = setFirstPaymentWaiverInColumn(
              draft.deskingpaymentDetails[columnDataIndex],
              waiverAmount
            );
          } else {
            draft.deskingpaymentDetails.map(columnData => setFirstPaymentWaiverInColumn(columnData, waiverAmount));
          }
        }
      }),

    [ACTIONS.MODIFY_FPW_RELATED_KEYS]: (state, { payload }) =>
      produce(state, draft => {
        const { value, selectedColumnId, key } = payload;
        if (selectedColumnId) {
          const columnDataIndex = draft.deskingpaymentDetails.findIndex(({ id }) => id === selectedColumnId);
          draft.deskingpaymentDetails[columnDataIndex] = setFirstPaymentWaiverInColumn(
            draft.deskingpaymentDetails[columnDataIndex],
            value,
            key
          );
        } else {
          draft.deskingpaymentDetails.map(columnData => setFirstPaymentWaiverInColumn(columnData, value, key));
        }
      }),

    [ACTIONS.ADD_REBATES_IN_SELECTED_COLUMN]: (state, { payload }) =>
      produce(state, draft => {
        const { rebates, selectedColumnId } = payload;
        const columnDataIndex = draft.deskingpaymentDetails.findIndex(({ id }) => id === selectedColumnId);
        draft.deskingpaymentDetails[columnDataIndex] = setRebatesInColumn(
          draft.deskingpaymentDetails[columnDataIndex],
          rebates
        );
      }),

    [ACTIONS.SET_REBATE_FILTERS]: (state, { payload }) =>
      produce(state, draft => {
        draft.deal.data.rebateFilters = payload;
      }),

    [ACTIONS.SET_DEAL]: (state, { payload }) =>
      produce(state, draft => {
        draft.deal.data = { ...(_get(state, 'deal.data') || {}), ...payload };
      }),

    [ACTIONS.SAVE_ACCESSORIES_FROM_RECAP]: (state, { payload }) =>
      produce(state, draft => {
        const { accessories, columnId } = payload;

        const deskingpaymentDetails = draft.deskingpaymentDetails.map(dpd => {
          const { id } = dpd;

          if (!columnId || columnId === id) {
            return {
              ...dpd,
              accessories,
            };
          }

          return dpd;
        });
        draft.deskingpaymentDetails = deskingpaymentDetails;
      }),

    [ACTIONS.DESKING_SAVE_ACCESSORIES]: (state, { payload }) =>
      produce(state, draft => {
        const {
          accessories,
          onInit = false,
          dueBillSetupConfig,
          allDueBillById,
          keepManuallyUpdatedAccessories,
          columnIds,
          lenders,
          applicableLender,
        } = payload;
        const deal = draft.deal.data;

        draft.deskingpaymentDetails.forEach(column => {
          const { id } = column;
          const shouldUpdateColAcc = columnIds && columnIds.includes(id);
          if (shouldUpdateColAcc) {
            const formattedAccessories = formatAccessoriesBasedOnSetup({
              accessories,
              onInit,
              dueBillSetupConfig,
              allDueBillById,
              columnData: column,
              deal,
              keepManuallyUpdatedAccessories,
              standardizedMakeSetupEnabled: DealerPropertyHelper.standardizedMakeSetupEnabled(),
              vehicleMakeAliases: draft.vehicleMakeAliases,
            });
            const filterHardAddessAccessories = filterHardAddsAccessoriesBasedOnLenderCode({
              accessories: formattedAccessories,
              applicableLender,
              columnData: column,
              lenders,
            });
            _set(column, 'accessories', filterHardAddessAccessories);
          }
        });
      }),

    [ACTIONS.DESKING_SAVE_MULTIPLE_ACCESSORIES]: (state, { payload: allPayload }) =>
      produce(state, draft => {
        allPayload.forEach(payload => {
          const { accessories, columnId } = payload;

          const deskingpaymentDetails = draft.deskingpaymentDetails.map(dpd => {
            const { id } = dpd;

            if (!columnId || columnId === id) {
              return {
                ...dpd,
                accessories,
              };
            }

            return dpd;
          });
          draft.deskingpaymentDetails = deskingpaymentDetails;
        });
      }),

    [ACTIONS.SET_COMMISSION_DETAILS]: (state, { payload }) =>
      produce(state, draft => {
        const { commissions, assignee, lookupUsers, version } = payload;
        const deal = draft.deal.data;
        if (commissions) _set(deal, 'commissions', commissions);
        if (assignee) _set(deal, 'assignee', assignee);
        if (lookupUsers) _set(deal, '_lookup', lookupUsers);
        if (version) _set(deal, 'version', version);
      }),

    [ACTIONS.ON_FEE_SEARCH_SUCCESS]: (state, { payload }) =>
      produce(state, draft => {
        const feeSearchResults = getFeeFromSearch(payload);
        const feeCodes = getFeeCodes(feeSearchResults);
        draft.feeUpdateInfos = {
          feeSearchResults,
          feeCodes,
        };
      }),

    [ACTIONS.SET_FNI_ATTRIBUTES_FROM_IDS]: (state, { payload }) =>
      produce(state, draft => {
        const idSegregations = {};
        const fniCodes = payload;
        fniCodes.forEach(fni => {
          const { id } = fni;
          idSegregations[id] = fni;
        });

        draft.fniAttributesCodes = {
          idSegregations,
          fniAttributesData: fniCodes,
        };
      }),

    [ACTIONS.SET_WORKINNG_CASH_CONFIG_IN_DEAL]: (state, { payload }) =>
      produce(state, draft => {
        draft.deal.data.workingCashConfig = payload;
      }),

    [ACTIONS.SET_DMV_ROS_IN_DEAL]: (state, { payload }) =>
      produce(state, draft => {
        draft.deal.data.dmvRosNumber = payload;
      }),

    [ACTIONS.FETCH_CONTRACT]: (state, { payload }) =>
      produce(state, draft => {
        draft.contract = payload;
      }),

    [ACTIONS.CLEAR_CONTRACT]: state =>
      produce(state, draft => {
        draft.contract = EMPTY_OBJECT;
      }),

    [ACTIONS.SET_PAYMENT_DETAILS_IN_DEAL]: (state, { payload }) =>
      produce(state, draft => {
        const { paymentDetails } = payload;
        _set(draft, ['deal', 'data', 'dealVehiclePaymentInfos'], paymentDetails);
      }),

    [ACTIONS.REMOVE_ALL_COLUMNS]: state =>
      produce(state, draft => {
        draft.deskingpaymentDetails = [];
      }),

    [ACTIONS.SET_EMI_AMOUNT]: (state, { payload }) =>
      produce(state, draft => {
        draft.deskingpaymentDetails.forEach(column => {
          const termPaymentDetails = _get(column, 'termPaymentDetails') || EMPTY_OBJECT;
          Object.keys(termPaymentDetails).forEach(lenders => {
            const downPayments = _get(termPaymentDetails, lenders) || EMPTY_OBJECT;
            Object.keys(downPayments).forEach(dp => {
              _set(termPaymentDetails, [lenders, dp, 'emiAmount'], payload);
            });
          });
        });
      }),

    [ACTIONS.SET_TAX_DETAILS]: (state, { payload }) =>
      produce(state, draft => {
        const { columnData, taxes } = payload;

        const matcedColumn = findArrayItem(draft.deskingpaymentDetails, 'id', columnData.id);
        if (matcedColumn) {
          const { selectedDownPaymentId, selectedLenderId, termPaymentDetails } = matcedColumn;
          const existingTaxes = _get(termPaymentDetails, [selectedLenderId, selectedDownPaymentId, 'taxes']) || {};
          _forEach(taxes, (value, key) => {
            _set(existingTaxes, [key], value);
          });
          _set(termPaymentDetails, [selectedLenderId, selectedDownPaymentId, 'taxes'], existingTaxes);
        }
      }),

    [ACTIONS.SET_DEAL_DIRTY_STATUS]: (state, { payload }) =>
      produce(state, draft => {
        draft.isDirty = payload;
      }),

    [ACTIONS.SET_BASE_EMI_EMOUNT]: (state, { payload }) =>
      produce(state, draft => {
        const { columnId: targetColumnId, lender: targetLender, downPaymentId, baseEmiAmount } = payload;
        draft.deskingpaymentDetails.forEach(column => {
          const { id } = column;
          if (id !== targetColumnId) return;
          const termPaymentDetails = _get(column, 'termPaymentDetails') || EMPTY_OBJECT;
          Object.keys(termPaymentDetails).forEach(lender => {
            if (lender !== targetLender) return;
            _set(termPaymentDetails, [lender, downPaymentId, 'baseEmiAmount'], baseEmiAmount);
          });
        });
      }),

    [ACTIONS.RESET_TRADE_IN]: state =>
      produce(state, draft => {
        _set(draft, ['deal', 'data', 'tradeIns'], []);
      }),

    [ACTIONS.RECAP_SET_FINANCE_RESERVE]: (state, { payload }) =>
      produce(state, draft => {
        const { columnId, financeReserve } = payload;

        draft.deskingpaymentDetails.forEach((columnData, index) => {
          if (columnData.id !== columnId) return;
          const { selectedLenderId, selectedDownPaymentId } = columnData;
          const program = _get(columnData, ['termPaymentDetails', selectedLenderId, selectedDownPaymentId]);
          program.apr = {
            ...program.apr,
            financeReserve,
          };
          _set(
            draft.deskingpaymentDetails,
            [index, 'termPaymentDetails', selectedLenderId, selectedDownPaymentId],
            program
          );
        });
      }),

    [ACTIONS.GET_GL_BALANCE]: (state, { payload }) =>
      produce(state, draft => {
        draft.glAccount = payload;
      }),

    [ACTIONS.DESKING_SET_ACTIVE_TAB]: (state, { payload }) =>
      produce(state, draft => {
        draft.selected = payload;
      }),

    [ACTIONS.SET_RESIDUAL_DISPLAY_TYPE]: (state, { payload }) =>
      produce(state, draft => {
        const { id, lender, residualDisplayType } = payload;

        draft.deskingpaymentDetails.forEach((deskingPaymentDetail, index) => {
          if (deskingPaymentDetail.id !== id) return;
          const downPayments = Object.keys(_get(deskingPaymentDetail, ['termPaymentDetails', lender]) || EMPTY_OBJECT);
          downPayments.forEach(downPayment => {
            const program = _get(deskingPaymentDetail, ['termPaymentDetails', lender, downPayment]);

            program.residual = { ...program.residual, residualDisplayType };
            _setWith(draft.deskingpaymentDetails[index], ['termPaymentDetails', lender, downPayment], program, Object);
          });
        });
      }),

    [ACTIONS.SET_STATE_FEE_TAX_OPTIONS]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, 'deal.data.stateFeeTaxOptions', getUpdatedStateFeeTaxOptions(draft, payload));
      }),

    [ACTIONS.SET_MARKET_SCAN_DATA]: (state, { payload: actionPayload }) =>
      produce(state, draft => {
        const { requestPayload, response, generic, lenders, collectRegistrationFeeInCap } = actionPayload;
        const {
          marketScanData = EMPTY_OBJECT,
          successColumnIds: mScanSuccessCIDs = EMPTY_ARRAY,
          errorColumnIds = EMPTY_ARRAY,
          emptyColumnIds = EMPTY_ARRAY,
        } = response;
        const isMultipleDownPaymentsFlagEnabled = isMultipleDownPaymentsEnabled(_get(draft, ['deal', 'data']));

        draft.deskingpaymentDetails.forEach((existingColumnData, index) => {
          const columnId = existingColumnData.id;
          const requestForColumn = requestPayload.find(({ Id }) => Id === columnId) || EMPTY_OBJECT;
          const { lender: lenderInRequest } = requestForColumn;
          if (mScanSuccessCIDs.includes(columnId)) {
            const mScanInfoForId = _get(marketScanData, [columnId]);
            const paymentType = ColumnDataReader.getPaymentType(existingColumnData);
            const existingDownPaymentsForColumn = getDownPaymentsForPaymentType(
              state.downPayments,
              paymentType,
              isMultipleDownPaymentsFlagEnabled
            );
            // set column level values
            const programDesc = _values(mScanInfoForId)[0]?.FullDetails?.Description;
            if (programDesc) _set(existingColumnData, 'programDesc', programDesc);
            // set term level values
            Object.keys(mScanInfoForId).forEach(async downPaymentString => {
              const downPayment = Number(downPaymentString);
              existingDownPaymentsForColumn.forEach((existingDownPayment, downpaymentId) => {
                if (existingDownPayment === downPayment) {
                  const existingProgram = _get(existingColumnData, [
                    'termPaymentDetails',
                    lenderInRequest,
                    downpaymentId,
                  ]);
                  const newProgram = _get(mScanInfoForId, [downPaymentString]);
                  const lenderInfoFromSetup = getSelectedLenderDetails(lenders, lenderInRequest) || {};
                  const mergedProgram = MarketScanReader.getMergedProgram({
                    existingProgram,
                    newProgram,
                    term: ColumnDataReader.getTerm(existingColumnData),
                    paymentType: ColumnDataReader.getPaymentType(existingColumnData),
                    generic,
                    lenderInfoFromSetup,
                    collectRegistrationFeeInCap,
                    deal: state?.deal?.data,
                  });
                  _setWith(
                    existingColumnData,
                    ['termPaymentDetails', lenderInRequest, downpaymentId],
                    mergedProgram,
                    Object
                  );
                }
              });
            });
          } else if (errorColumnIds.includes(columnId) || (emptyColumnIds.includes(columnId) && generic)) {
            const downPayments = Object.keys(
              _get(existingColumnData, ['termPaymentDetails', lenderInRequest]) || EMPTY_OBJECT
            );
            downPayments.forEach(downPayment => {
              const program = resetPaymentDataOnParamChange(
                _get(existingColumnData, ['termPaymentDetails', lenderInRequest, downPayment])
              );
              program.error = MESSAGES.MARKET_SCAN_FAILURE_GENERIC_MESSAGE;
              _set(draft.deskingpaymentDetails, [index, 'termPaymentDetails', lenderInRequest, downPayment], program);
            });
          }
        });
      }),

    [ACTIONS.SET_PROGRAM_IDS]: (state, { payload: actionPayload }) =>
      produce(state, draft => {
        const { requestPayload, marketScanData = EMPTY_OBJECT } = actionPayload;
        const { deskingpaymentDetails } = draft;
        const isMultipleDownPaymentsFlagEnabled = isMultipleDownPaymentsEnabled(_get(draft, ['deal', 'data']));

        _keys(marketScanData).forEach(columnId => {
          const existingColumnData = deskingpaymentDetails.find(({ id }) => id === columnId);
          if (!existingColumnData) return;
          const marketScanDataForId = _get(marketScanData, [columnId]);
          const requestForColumn = requestPayload.find(({ Id }) => Id === columnId);
          const { lender } = requestForColumn;

          const paymentType = ColumnDataReader.getPaymentType(existingColumnData);
          const existingDownPaymentsForColumn = getDownPaymentsForPaymentType(
            state.downPayments,
            paymentType,
            isMultipleDownPaymentsFlagEnabled
          );

          Object.keys(marketScanDataForId).forEach(downPaymentString => {
            const downPayment = Number(downPaymentString);
            const marketScanProgram = _get(marketScanDataForId, [downPaymentString]);
            const programId = MarketScanReader.getProgramId(marketScanProgram);
            const aprCode = MarketScanReader.getAPRCode(marketScanProgram);
            const residualName = MarketScanReader.getResidualName(marketScanProgram);

            existingDownPaymentsForColumn.forEach((existingDownPayment, downpaymentId) => {
              if (existingDownPayment === downPayment) {
                _setWith(
                  existingColumnData,
                  ['termPaymentDetails', lender, downpaymentId, 'programId'],
                  programId,
                  Object
                );
                _setWith(
                  existingColumnData,
                  ['termPaymentDetails', lender, downpaymentId, 'apr', 'aprCode'],
                  aprCode,
                  Object
                );
                _setWith(
                  existingColumnData,
                  ['termPaymentDetails', lender, downpaymentId, 'residual', 'residualName'],
                  residualName,
                  Object
                );
              }
            });
          });
        });
      }),

    [ACTIONS.SET_GENERIC_AND_ERROR_STATUS]: (state, { payload: actionPayload }) =>
      produce(state, draft => {
        const columnIds = _keys(actionPayload) || EMPTY_ARRAY;
        draft.deskingpaymentDetails.forEach((deskingPaymentDetail, index) => {
          if (!columnIds.includes(deskingPaymentDetail.id)) return;
          const { lender, generic, error } = actionPayload[deskingPaymentDetail.id];
          const downPayments = Object.keys(_get(deskingPaymentDetail, ['termPaymentDetails', lender]) || EMPTY_OBJECT);
          downPayments.forEach(downPayment => {
            const program = _get(deskingPaymentDetail, ['termPaymentDetails', lender, downPayment]);
            program.error = error;
            program.generic = generic;
            _set(draft.deskingpaymentDetails, [index, 'termPaymentDetails', lender, downPayment], program);
          });
        });
      }),

    [ACTIONS.SET_DEAL_SHEET_PRINT_PAYLOAD]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, 'dealSheetPrintPayload', payload);
      }),

    [ACTIONS.REPLACE_PEN_CONTRACT_INFO_IN_DEAL]: (state, { payload }) =>
      produce(state, draft => {
        const { penInfo } = payload;
        _set(draft, ['deal', 'data', 'penInfo'], penInfo);
      }),

    [ACTIONS.REMOVE_PEN_CONTRACT_INFO_DEAL]: (state, { payload }) =>
      produce(state, draft => {
        const productContractInfos = _get(draft, 'deal.data.penInfo.productContractInfos') || [];
        const productId = _get(payload, 'productId');
        const providerId = _get(payload, 'providerId');
        const newProductContractInfos = productContractInfos.filter(contractInfo => {
          const itemProductId = _get(contractInfo, 'productId');
          const itemProviderId = _get(contractInfo, 'providerId');

          return itemProductId !== productId || providerId !== itemProviderId;
        });

        _set(draft, ['deal', 'data', 'penInfo', 'productContractInfos'], newProductContractInfos);
      }),

    [ACTIONS.SAVE_COLUMN_BASED_FNIS]: (state, { payload }) =>
      produce(state, draft => {
        const fnisByColumnIds = _keyBy(payload, 'columnId');

        draft.deskingpaymentDetails.forEach(column => {
          const { id } = column;
          const fnisToBeSaved = _get(fnisByColumnIds, [id, 'fnis']) || [];
          _set(column, ['fnIs'], fnisToBeSaved);
        });
      }),

    [ACTIONS.GET_DEFAULT_PLAN_CODES]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, ['defaultPlanCodes'], payload);
      }),

    [ACTIONS.RESET_KEYS_IN_TERM_PAYMENT_DETAILS]: (state, { payload }) =>
      produce(state, draft => {
        const { newModifiedTermPaymentData } = payload;
        draft.deskingpaymentDetails.forEach(column => {
          const { id } = column;
          const data = newModifiedTermPaymentData[id];
          if (data) {
            const dataKeys = _keys(data);
            const { termPaymentDetails } = column;
            const lenders = _keys(termPaymentDetails);

            lenders.forEach(lender => {
              const downpayments = _keys(termPaymentDetails[lender]);
              downpayments.forEach(dp => {
                dataKeys.forEach(key => {
                  const info = _get(termPaymentDetails, [lender, dp]);
                  _set(info, key.split('.'), _get(data, key));
                });
              });
            });
          }
        });
      }),

    // use this to set different values in all termpayments in different columns
    [ACTIONS.SET_VALUES_IN_TERM_PMT_DETAILS]: (state, { payload }) =>
      produce(state, draft => {
        const { data } = payload;
        const columnIds = _keys(data);
        draft.deskingpaymentDetails.forEach(column => {
          const { id } = column;

          if (columnIds.includes(id)) {
            const dataKeys = _keys(data[id]);
            const { termPaymentDetails } = column;
            const lenders = _keys(termPaymentDetails);

            lenders.forEach(lender => {
              const downpayments = _keys(termPaymentDetails[lender]);
              downpayments.forEach(dp => {
                dataKeys.forEach(key => {
                  _set(termPaymentDetails[lender][dp], key.split('.'), data[id][key]);
                });
              });
            });
          }
        });
      }),

    // use this to set different values in different columns
    [ACTIONS.SET_VALUES_IN_COLUMNS]: (state, { payload }) =>
      produce(state, draft => {
        const { data } = payload;
        const columnIds = _keys(data);
        _forEach(draft.deskingpaymentDetails, column => {
          const { id } = column;

          if (columnIds.includes(id)) {
            const dataKeys = _keys(data[id]);
            _forEach(dataKeys, key => {
              _set(column, key.split('.'), data[id][key]);
            });
          }
        });
      }),

    [ACTIONS.GET_CONTRACTS_VALIDATIONS]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, ['contractValidations'], payload);
      }),

    [ACTIONS.SET_PEN_INFO]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, 'deal.data.penInfo', payload);
      }),

    [ACTIONS.SET_CALL_MARKET_SCAN]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, 'canCallMarketScan', payload);
      }),

    [ACTIONS.SET_PDF_LOADER_DEAL_JACKET]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, 'showPdfLoaderDealJacket', !!payload);
      }),

    [ACTIONS.SET_LEASE_CONFIGURATIONS]: (state, { payload }) =>
      produce(state, draft => {
        const currentConfigurations = _get(draft, 'deal.data.leaseConfigurations') || {};
        _set(draft, 'deal.data.leaseConfigurations', {
          ...currentConfigurations,
          ...payload,
        });
      }),

    [ACTIONS.SET_SECURITY_DEPOSIT_WAIVER_REASON]: (state, { payload }) =>
      produce(state, draft => {
        const { value } = payload;
        const isGalaxyEnabled = CalcEngineProperties.updateByGalaxyEngine();
        if (isGalaxyEnabled) {
          const { selectedColumnId } = payload;
          const selectedColumnIndex = draft.deskingpaymentDetails.findIndex(({ id }) => id === selectedColumnId);
          _set(draft, ['deskingpaymentDetails', selectedColumnIndex, 'securityDepositWaiverReason'], value);
        } else {
          _set(draft, 'deal.data.securityDepositWaiverReason', value);
        }
      }),

    [ACTIONS.SET_ECONTRACT_PACKAGES_WITH_SIGNATURES]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, 'econtractDocPackages', _get(payload, 'econtractDocPackages.packages') || []);
      }),

    [ACTIONS.SET_ECONTRACT_PACKAGES]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, 'econtractDocPackages', _get(payload, 'packages') || []);
      }),

    [ACTIONS.SET_COLLECT_TAXES]: (state, { payload }) =>
      produce(state, draft => {
        const { collectTax, type } = payload;
        if (type === TAX_AND_ZIP_CODE_DETAILS_VS_KEY.LEASE) _set(draft, 'deal.data.collectTaxLease', collectTax);
        else if (type === TAX_AND_ZIP_CODE_DETAILS_VS_KEY.RETAIL) _set(draft, 'deal.data.collectTaxRetail', collectTax);
        else {
          _set(draft, 'deal.data.collectTaxRetail', collectTax);
          _set(draft, 'deal.data.collectTaxLease', collectTax);
        }
      }),

    [ACTIONS.SET_COLLECT_FEES]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, 'deal.data.collectFees', payload);
      }),

    [ACTIONS.SET_SELECTED_LENDER_TIER]: (state, { payload }) =>
      produce(state, draft => {
        const { lenderId, tier, columnIds } = payload;
        draft.deskingpaymentDetails.forEach(column => {
          const { selectedLenderId, id } = column;
          if (selectedLenderId === lenderId && columnIds.includes(id)) {
            _set(column, 'selectedTier', tier);
          }
        });
      }),

    [ACTIONS.SET_SCAN_VALUES]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, 'deal.data.scanDefaults', {
          ..._get(state, 'deal.data.scanDefaults'),
          ...payload,
        });
      }),

    [ACTIONS.SET_ADJUSTED_RESIDUAL]: (state, { payload }) =>
      produce(state, draft => {
        const { adjustedResiduals } = payload;
        draft.deskingpaymentDetails.forEach((column, index) => {
          const { selectedLenderId, id } = column;
          if (_get(adjustedResiduals, [id])) {
            const existingTermPaymentDetail = _get(draft.deskingpaymentDetails, [
              index,
              'termPaymentDetails',
              selectedLenderId,
            ]);
            _forEach(existingTermPaymentDetail, (paymentDetail, downpaymentId) => {
              const residualAdjustmentPCT = _get(adjustedResiduals, [id, downpaymentId, 'pctDiff']);
              const residualAdjustmentValue = _get(adjustedResiduals, [id, downpaymentId, 'valueDiff']);

              if (residualAdjustmentPCT) {
                _set(paymentDetail, ['residual', 'adjustedPercentage'], residualAdjustmentPCT);
              }
              if (residualAdjustmentValue) {
                _set(paymentDetail, ['residual', 'adjustedValue'], residualAdjustmentValue);
              }
            });
          }
        });
      }),

    [ACTIONS.SET_UPDATED_COLUMN_DATA]: (state, { payload }) =>
      produce(state, draft => {
        draft.deskingpaymentDetails.forEach((existingColumnData, index) => {
          const existingColumnId = existingColumnData.id;
          const matchingColumnInPayload = _find(payload, newColumn => _get(newColumn, 'id') === existingColumnId);
          if (matchingColumnInPayload) {
            const { selected, ...paymentDetails } = matchingColumnInPayload; // DMS-24006
            const tempDeskingpaymentDetails = draft.deskingpaymentDetails[index];
            draft.deskingpaymentDetails[index] = {
              ...tempDeskingpaymentDetails,
              ...paymentDetails,
            };
          }
        });
      }),

    [ACTIONS.SET_PAYMENT_TYPE]: (state, { payload }) =>
      produce(state, draft => {
        const { id, paymentType, paymentSubType, months } = payload;

        _forEach(draft.deskingpaymentDetails, column => {
          if (column.id === id) {
            _set(column, 'paymentOption.paymentType', paymentType);
            _set(column, 'paymentOption.paymentSubType', paymentSubType || null);
            if (!_isNil(months)) {
              _set(column, 'paymentOption.value', months);
            }
          }
        });
      }),

    [ACTIONS.SET_OPTION_CONTRACT_VALIDITY_DATE]: (state, { payload }) =>
      produce(state, draft => {
        const { id, optionContractValidityDate } = payload;
        _forEach(draft.deskingpaymentDetails, column => {
          if (column.id === id) {
            _set(column, 'optionContractValidityDate', getMomentValueOf(optionContractValidityDate));
          }
        });
      }),

    [ACTIONS.SET_REBATES]: (state, { payload }) =>
      produce(state, draft => {
        const {
          rebateInDealStructure,
          rebateProvider,
          vehicleIndex = 0,
          dealVehicleId,
          rebateValue,
          isGlobalUpdate,
        } = payload;
        draft.deskingpaymentDetails.forEach((columnData, index) => {
          _forEach(_get(columnData, 'termPaymentDetails'), lender => {
            _forEach(lender, downPayment => {
              const currentDealVehicleId = _get(columnData, 'dealVehicleId');
              _set(downPayment, ['rebates'], rebateInDealStructure[index]);
              if (!isGlobalUpdate && dealVehicleId === currentDealVehicleId) {
                _set(downPayment, 'vcciFields.rebateApplicableIfCashDeal', rebateValue);
              } else if (isGlobalUpdate) {
                _set(downPayment, 'vcciFields.rebateApplicableIfCashDeal', rebateValue);
              }
            });
          });
        });
        _set(draft, ['deal', 'data', 'dealVehiclePaymentInfos', vehicleIndex, 'rebateProvider'], rebateProvider);
      }),

    [ACTIONS.SET_SELECTED_LENDER_ID]: (state, { payload }) =>
      produce(state, draft => {
        const { id, selectedLenderId } = payload;

        draft.deskingpaymentDetails.forEach((deskingPaymentDetail, index) => {
          if (deskingPaymentDetail.id !== id) return;
          _set(draft.deskingpaymentDetails, [index, 'selectedLenderId'], selectedLenderId);
        });
      }),

    [ACTIONS.SET_DEPOSIT_DETAILS]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, 'deal.data.depositDetails', {
          ..._get(state, 'deal.data.depositDetails'),
          ...payload,
        });
      }),

    [ACTIONS.SET_AUTO_REWARD_DETAILS]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, 'deal.data.autoRewardsDetails', {
          ..._get(state, 'deal.data.autoRewardsDetails'),
          ...payload,
        });
      }),

    [ACTIONS.SET_DEAL_PRODUCT_CONTRACT_INFOS]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, 'deal.data.penInfo.productContractInfos', payload);
      }),

    [ACTIONS.SET_LEAD_UPDATED]: (state, { payload }) =>
      produce(state, draft => {
        const customers = _get(payload, 'customers') || [];

        _set(draft, 'deal.data.leadModified', false);
        _set(draft, 'deal.data.customers', customers);
      }),

    [ACTIONS.SAVE_MANDATORY_FIELDS]: (state, { payload }) =>
      produce(state, draft => {
        const { response: deal, mandatoryReviewFieldsResponse } = payload;
        _set(draft, 'deal.data', deal);
        const { mandatoryAndReviewFieldsResponse, newStatus } = mandatoryReviewFieldsResponse;
        const filteredMandatoryAndReviewFieldsResponse = {};

        _forEach(mandatoryAndReviewFieldsResponse, (value, key) => {
          const filteredFields = _filter(value, ({ dealValue }) => _isEmpty(dealValue));
          filteredMandatoryAndReviewFieldsResponse[key] = filteredFields;
        });

        _set(draft, 'mandatoryReviewFields', {
          mandatoryAndReviewFieldsResponse: filteredMandatoryAndReviewFieldsResponse,
          newStatus,
        });
      }),

    [ACTIONS.UPDATE_MANDATORY_FIELDS]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, 'mandatoryReviewFields', payload);
      }),

    [ACTIONS.UPDATE_BUYER]: (state, { payload }) =>
      produce(state, draft => {
        const { key, value } = payload;
        const buyer = getBuyer(draft.deal.data);
        _set(buyer, key, value);
      }),

    [ACTIONS.SET_DEAL_PREFERENCES]: (state, { payload }) =>
      produce(state, draft => {
        const newDealPrefs = {};
        _merge(newDealPrefs, _get(draft, 'dealPreferences'), payload);
        _set(draft, 'dealPreferences', newDealPrefs);
      }),

    [ACTIONS.SET_DOCUMENTS_VIEW_PREFERENCES]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, 'documentsViewPreferences', payload);
      }),

    [ACTIONS.UPDATE_DELIVERY_CODE]: (state, { payload }) =>
      produce(state, draft => {
        const { deliveryCode, vehicleIndex = 0 } = payload;
        _set(draft, ['deal', 'data', 'dealVehiclePaymentInfos', vehicleIndex, 'deliveryCode'], deliveryCode);
      }),

    [ACTIONS.UPDATE_STATE_FEE_TAX_METADATA]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, 'taxFeeConfigMetadata', payload);
      }),

    [ACTIONS.SET_DEALERSHIP_INFO]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, ['dealershipInfo'], payload);
      }),
    [ACTIONS.SET_DESKING_PAYMENT_DETAIL_DEAL_VEHICLE_ID]: (state, { payload }) =>
      produce(state, draft => {
        const { primaryVehicleDealVehicleId } = payload;
        _forEach(draft.deskingpaymentDetails, columnData => {
          _set(columnData, 'dealVehicleId', primaryVehicleDealVehicleId);
        });
      }),

    [ACTIONS.SET_PREVIOUS_LENDER_ID]: (state, { payload }) =>
      produce(state, draft => {
        const { id, lenderId } = payload;
        _forEach(draft.deskingpaymentDetails, columnData => {
          if (columnData?.id === id) {
            _set(columnData, 'previousLenderId', lenderId);
          }
        });
      }),

    [ACTIONS.DELETE_VEHICLE]: (state, { payload }) =>
      produce(state, draft => {
        const { dealVehicleId } = payload;
        const updatedDeskingPaymentDetails = _filter(
          draft.deskingpaymentDetails,
          ({ dealVehicleId: id }) => id !== dealVehicleId
        );
        const updatedVehicles = _filter(draft.deal.data.vehicles, ({ dealVehicleId: id }) => id !== dealVehicleId);
        draft.deskingpaymentDetails = updatedDeskingPaymentDetails;
        draft.deal.data.vehicles = updatedVehicles;
      }),

    [ACTIONS.SET_NEW_LEAD_ID]: (state, { payload }) =>
      produce(state, draft => {
        const { leadId } = payload;
        _set(draft, ['deal', 'data', 'leadId'], leadId);
      }),

    [ACTIONS.RESET_REFRESH_PAYMENTS]: state =>
      produce(state, draft => {
        const currentStatus = _get(draft, ['deal', 'data', 'refreshMScanPayments']) || false;
        _set(draft, ['deal', 'data', 'refreshMScanPayments'], !currentStatus);
        _set(draft, ['deal', 'data', 'calculationRequired'], false);
      }),

    [ACTIONS.CHANGE_MULTI_VEHICLE_DESKING_STATUS]: (state, { payload }) =>
      produce(state, draft => {
        draft.deal.data.multiVehicleDeskingEnabled = payload;
      }),

    [ACTIONS.SET_EDOC_FEEDBACK]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, 'deal.data.econtractPackageStatus', payload);
      }),

    [ACTIONS.SET_PROG_MAN_OVERRIDDEN]: (state, { payload }) =>
      produce(state, draft => {
        const { value, selectedColumnId } = payload;
        const columnDataIndex = draft.deskingpaymentDetails.findIndex(({ id }) => id === selectedColumnId);
        if (columnDataIndex !== -1) {
          _set(draft, ['deskingpaymentDetails', columnDataIndex, 'programManuallyOverridden'], value);
        }
      }),

    [ACTIONS.UPDATE_DOCUMENT_STATUS]: (state, { payload }) =>
      produce(state, draft => {
        draft.documentSignStatus = { ...state.documentSignStatus, ...payload };
      }),

    [ACTIONS.SET_DOCUMENTS_TO_HIGHLIGHT_IN_DEALJACKET]: (state, { payload }) =>
      produce(state, draft => {
        draft.documentsToHighlightInDealJacket = payload;
      }),

    [ACTIONS.SET_DEALER_TRADE_SWAP]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, 'deal.data.dealerTradeSwap', {
          ..._get(state, 'deal.data.dealerTradeSwap'),
          ...payload,
        });
      }),
    [ACTIONS.VEHICLE_MAKE_ALIASES]: (state, { payload }) =>
      produce(state, draft => {
        draft.vehicleMakeAliases = payload;
      }),

    [ACTIONS.UPDATE_ONE_ACCESSORY_FOR_ALL_COLUMNS]: (state, { payload }) =>
      produce(state, draft => {
        const { accesoryToUpdate } = payload;
        _forEach(draft.deskingpaymentDetails, columnData => {
          const index = _findIndex(
            columnData.accessories,
            draftData => draftData?.unigueId === accesoryToUpdate?.unigueId
          );
          _set(columnData, ['accessories', index], accesoryToUpdate);
        });
      }),

    [ACTIONS.UPDATE_ROUTEONE_ERRORS_BY_SIGNATURE_COORDINATE_IDS]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, ['routeOneErrors'], {
          ...draft.routeOneErrors,
          ...payload,
        });
      }),

    [ACTIONS.APPLY_COLUMN_DATA]: (state, { payload }) =>
      produce(state, draft => {
        const { columnData } = payload;
        draft.deskingpaymentDetails = draft.deskingpaymentDetails.map(column => {
          if (column?.id === columnData?.id) {
            return columnData;
          }
          return column;
        });
      }),

    [ACTIONS.SET_SHOW_ARCHIVED_DOCUMENTS_IN_DEALJACKET]: (state, { payload }) =>
      produce(state, draft => {
        draft.showArchivedDocsInDealJacket = payload;
      }),

    [ACTIONS.SET_HIDDEN_COMPLETION_CERTIFICATES]: (state, { payload }) =>
      produce(state, draft => {
        draft.hiddenCompletionCertificates = payload;
      }),

    [ACTIONS.UPDATE_DOCUMENT_SIGN_MODAL]: (state, { payload }) =>
      produce(state, draft => {
        draft.documentSigningModal = payload;
      }),

    [ACTIONS.SET_ESIGN_SESSION_INFO]: (state, { payload }) =>
      produce(state, draft => {
        draft.eSignSessionInfo = payload;
      }),

    [ACTIONS.SET_MSCAN_VEHICLE_ID_FETCHED]: (state, { payload }) =>
      produce(state, draft => {
        draft.mscanVehcileIdFetched = payload?.mscanVehcileIdFetched || false;
      }),

    [ACTIONS.UPDATE_DEAL_SYNC_MISSING_MAPPING]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, 'dealSyncMissingMappings', payload);
      }),

    [ACTIONS.UPDATE_DEAL_SYNC_ERRORS]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, 'dealSyncErrors', payload);
      }),

    [ACTIONS.SET_DMS_PROVIDER]: (state, { payload }) => {
      const primaryDMSValue = produce(state, draft => {
        _set(draft, 'primaryDMS', payload);
      });
      return primaryDMSValue;
    },

    [ACTIONS.SET_ESTIMATED_DELIVERY_DATE]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, 'deal.data.vehicles[0].estimatedDeliveryDate', payload);
        _set(draft, 'hasEstimatedDeliveryDateChanged', true);
      }),

    [ACTIONS.SET_MSD]: (state, { payload }) =>
      produce(state, draft => {
        const { id, updatedMSDArray: multipleSecurityDepositDetailsDesking } = payload;
        draft.deskingpaymentDetails.forEach((deskingPaymentDetail, index) => {
          if (deskingPaymentDetail.id !== id) return;
          const { selectedLenderId } = deskingPaymentDetail;
          const downPaymentIds = Object.keys(
            _get(deskingPaymentDetail, ['termPaymentDetails', selectedLenderId]) || EMPTY_OBJECT
          );
          downPaymentIds.forEach(downPaymentId => {
            const program = _get(deskingPaymentDetail, ['termPaymentDetails', selectedLenderId, downPaymentId]);
            _set(program, ['multipleSecurityDepositProgramList'], multipleSecurityDepositDetailsDesking);
            _set(draft.deskingpaymentDetails, [index, 'termPaymentDetails', selectedLenderId, downPaymentId], program);
          });
        });
      }),

    [ACTIONS.RESET_MSD_FOR_SELECTED_COLUMN]: (state, { payload }) =>
      produce(state, draft => {
        const { id } = payload;

        draft.deskingpaymentDetails.forEach((deskingPaymentDetail, index) => {
          if (deskingPaymentDetail.id !== id) return;
          const { selectedLenderId } = deskingPaymentDetail;
          const downPaymentIds = Object.keys(
            _get(deskingPaymentDetail, ['termPaymentDetails', selectedLenderId]) || EMPTY_OBJECT
          );
          downPaymentIds.forEach(downPaymentId => {
            const program = _get(deskingPaymentDetail, ['termPaymentDetails', selectedLenderId, downPaymentId]);
            const multipleSecurityDepositDetailsDesking =
              _get(program, ['multipleSecurityDepositProgramList']) || EMPTY_ARRAY;
            const updatedMultipleSecurityDepositDetailsDesking = multipleSecurityDepositDetailsDesking.map(item => ({
              ...item,
              adjustment: { ...(_get(item, ['adjustment']) || EMPTY_OBJECT) },
              selected: false,
            }));
            _set(program, ['multipleSecurityDepositProgramList'], updatedMultipleSecurityDepositDetailsDesking);
            _set(draft.deskingpaymentDetails, [index, 'termPaymentDetails', selectedLenderId, downPaymentId], program);
          });
        });
      }),

    [ACTIONS.SET_WAIVER_REASON]: (state, { payload }) =>
      produce(state, draft => {
        const { id, updatedWaiverReasonArray: securityDepositWaiverDetailsDesking } = payload;

        draft.deskingpaymentDetails.forEach((deskingPaymentDetail, index) => {
          if (deskingPaymentDetail.id !== id) return;
          const { selectedLenderId } = deskingPaymentDetail;
          const downPaymentIds = Object.keys(
            _get(deskingPaymentDetail, ['termPaymentDetails', selectedLenderId]) || EMPTY_OBJECT
          );
          downPaymentIds.forEach(downPaymentId => {
            const program = _get(deskingPaymentDetail, ['termPaymentDetails', selectedLenderId, downPaymentId]);
            _set(program, ['securityDepositWaiverDetails'], securityDepositWaiverDetailsDesking);
            _set(draft.deskingpaymentDetails, [index, 'termPaymentDetails', selectedLenderId, downPaymentId], program);
          });
        });
      }),

    [ACTIONS.ADD_UPDATE_DEAL_PAYMENT_OSF_LENDER]: (state, { payload }) =>
      produce(state, draft => {
        const { id, lenderData } = payload;

        draft.deskingpaymentDetails.forEach((deskingPaymentDetail, index) => {
          if (deskingPaymentDetail.id === id) {
            _set(draft.deskingpaymentDetails, [index, 'osfLenderDetails'], lenderData);
          }
        });
      }),

    [ACTIONS.RESET_WAIVER_REASON_FOR_SELECTED_COLUMN]: (state, { payload }) =>
      produce(state, draft => {
        const { id } = payload;

        draft.deskingpaymentDetails.forEach((deskingPaymentDetail, index) => {
          if (deskingPaymentDetail.id !== id) return;
          const { selectedLenderId } = deskingPaymentDetail;
          const downPaymentIds = Object.keys(
            _get(deskingPaymentDetail, ['termPaymentDetails', selectedLenderId]) || EMPTY_OBJECT
          );
          downPaymentIds.forEach(downPaymentId => {
            const program = _get(deskingPaymentDetail, ['termPaymentDetails', selectedLenderId, downPaymentId]);
            const securityDepositWaiverDetailsDesking = _get(program, ['securityDepositWaiverDetails']) || EMPTY_ARRAY;
            const updatedSecurityDepositWaiverDetailsDesking = securityDepositWaiverDetailsDesking.map(item => ({
              ...item,
              waiverProgram: { ...(_get(item, ['waiverProgram']) || EMPTY_OBJECT) },
              selected: false,
            }));
            _set(program, ['securityDepositWaiverDetails'], updatedSecurityDepositWaiverDetailsDesking);
            _set(draft.deskingpaymentDetails, [index, 'termPaymentDetails', selectedLenderId, downPaymentId], program);
          });
        });
      }),

    [ACTIONS.UPDATE_CUSTOMER_MASKED_DATA]: (state, { payload }) =>
      produce(state, draft => {
        _set(draft, 'customerPIIData', payload);
      }),

    [ACTIONS.SET_VIRTUAL_MEETING_ROOM_DETAILS]: (state, { payload }) =>
      produce(state, draft => {
        draft.virtualMeetingRoomDetails = payload;
      }),

    [ACTIONS.SET_VIRTUAL_MEETING_PARTICIPANTS]: (state, { payload }) =>
      produce(state, draft => {
        draft.virtualMeetingRoomDetails.participants = payload;
      }),
  },
  initialState
);
