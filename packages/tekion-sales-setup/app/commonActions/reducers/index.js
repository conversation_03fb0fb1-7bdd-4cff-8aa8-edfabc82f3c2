import { produce } from 'immer';
import { handleActions } from 'redux-actions';
import _get from 'lodash/get';
import _keyBy from 'lodash/keyBy';
import _isEmpty from 'lodash/isEmpty';
import _map from 'lodash/map';
import _keys from 'lodash/keys';
import _set from 'lodash/set';
import _forEach from 'lodash/forEach';

import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import ACTIONS from 'commonActions/actionTypes';

import ACCOUNTING_REDUCERS from './accounting.reducers';

const initialState = {
  users: EMPTY_ARRAY,
  customers: EMPTY_OBJECT,
  vehicles: EMPTY_OBJECT,
  dealerConfig: EMPTY_OBJECT,
  dealerInfo: EMPTY_OBJECT,
  userConfig: EMPTY_OBJECT,
  printersInfo: {
    mappedPrinters: EMPTY_ARRAY,
    allPrinters: EMPTY_ARRAY,
  },
  accounting: {
    bootstrapData: EMPTY_OBJECT,
    salesChainList: EMPTY_OBJECT,
    fieldsMapToId: EMPTY_OBJECT,
    viSettingsRes: EMPTY_OBJECT,
    allGLAccounts: EMPTY_ARRAY,
    allJournals: EMPTY_ARRAY,
    isLoadingBootstrapByDealerId: EMPTY_OBJECT,
    bootstrapByDealerId: EMPTY_OBJECT,
  },
  trimInfos: {
    trims: EMPTY_ARRAY,
  },
  vehicleOptions: {
    makes: EMPTY_ARRAY,
    models: EMPTY_OBJECT,
    allMakes: EMPTY_ARRAY,
    allModels: EMPTY_ARRAY,
  },
  dealers: EMPTY_ARRAY,
  tenantDealers: EMPTY_ARRAY,
  taxCodes: EMPTY_ARRAY,
  recentAssignees: { fetched: false, recentAssignees: EMPTY_OBJECT },
  isSalesSetupFetched: false,
  dealerSites: EMPTY_OBJECT,
  stateListDetails: EMPTY_OBJECT,
  customViFields: EMPTY_OBJECT,
  userLookupInfoByIds: EMPTY_OBJECT,
  generatedDocumentFromImage: EMPTY_OBJECT,
  cmsTekionLenderCodes: EMPTY_ARRAY,
  fniDisclosureTypeOptions: EMPTY_ARRAY,
  dueBillDisclosureTypeOptions: EMPTY_ARRAY,
};

export default handleActions(
  {
    ...ACCOUNTING_REDUCERS,

    [ACTIONS.FETCH_USER_LIST_BY_ROLES]: (state, { payload }) =>
      produce(state, draft => {
        const users = _keyBy(payload, 'id');
        draft.users = { ...draft.users, ...users };
      }),

    [ACTIONS.FETCH_ALL_USERS]: (state, { payload }) =>
      produce(state, draft => {
        const users = _keyBy(payload, 'id');
        draft.users = { ...users };
      }),

    [ACTIONS.FETCH_USER_LIST_BY_IDS]: (state, { payload }) =>
      produce(state, draft => {
        const users = _keyBy(payload, 'userID');
        draft.users = { ...draft.users, ...users };
      }),

    [ACTIONS.GET_CUSTOMERS_BY_ID]: (state, { payload }) =>
      produce(state, draft => {
        const customers = _keyBy(payload, 'id');
        draft.customers = { ...draft.customers, ...customers };
      }),
    [ACTIONS.GET_VEHICLES_BY_ID]: (state, { payload }) =>
      produce(state, draft => {
        const { hits } = payload || EMPTY_OBJECT;
        const vehicles = _keyBy(hits, 'id');
        draft.vehicles = { ...draft.vehicles, ...vehicles };
      }),

    [ACTIONS.GET_DEALER_CONFIGS]: (state, { payload }) =>
      produce(state, draft => {
        const { isSalesSetupFetched, response } = payload;
        draft.isSalesSetupFetched = isSalesSetupFetched;
        draft.dealerConfig = response;
      }),

    [ACTIONS.GET_CONCIERGE_SETUP]: (state, { payload }) =>
      produce(state, draft => {
        draft.conciergeSetupConfig = payload;
      }),

    [ACTIONS.GET_DEALER_INFO]: (state, { payload }) =>
      produce(state, draft => {
        draft.dealerInfo = payload;
      }),

    [ACTIONS.GET_USER_ROLES_AND_PERMISSIONS]: (state, { payload }) =>
      produce(state, draft => {
        draft.userConfig = payload;
      }),

    [ACTIONS.GET_ALL_ROLES]: (state, { payload = EMPTY_ARRAY }) =>
      produce(state, draft => {
        draft.rolesInfo = _keyBy(payload, 'id');
      }),

    [ACTIONS.GET_ALL_ROLES_V2]: (state, { payload = EMPTY_ARRAY }) =>
      produce(state, draft => {
        draft.rolesInfoV2 = payload;
      }),

    [ACTIONS.GET_MAPPED_PRINTERS]: (state, { payload = EMPTY_ARRAY }) =>
      produce(state, draft => {
        draft.printersInfo.mappedPrinters = payload;
      }),

    [ACTIONS.GET_ALL_PRINTERS]: (state, { payload = EMPTY_ARRAY }) =>
      produce(state, draft => {
        draft.printersInfo.allPrinters = payload;
      }),

    [ACTIONS.SET_ACCOUNTING_DATA]: (state, { payload = {} }) =>
      produce(state, draft => {
        const { bootstrapData, salesChainList, fieldsMapToId, viSettingsRes } = payload || EMPTY_OBJECT;
        draft.accounting.bootstrapData = bootstrapData;
        draft.accounting.salesChainList = salesChainList;
        draft.accounting.fieldsMapToId = fieldsMapToId;
        draft.accounting.viSettingsRes = viSettingsRes;
      }),

    [ACTIONS.UPDATE_VEHICLE_DETAILS_IN_ALL_VEHICLES]: (state, { payload = {} }) =>
      produce(state, draft => {
        const { vehicleId, vehicleDetails } = payload;
        if (vehicleId && !_isEmpty(vehicleDetails)) {
          draft.vehicles[vehicleId] = vehicleDetails;
        }
      }),

    [ACTIONS.UPDATE_VI_VEHICLE_IN_STORE]: (state, { payload = {} }) =>
      produce(state, draft => {
        const { vehicleId, updatedViVehicleInfo } = payload;
        const viVehicleInfo = _get(state, ['vehicles', vehicleId]);
        if (viVehicleInfo)
          _set(draft, ['vehicles', vehicleId], {
            ...viVehicleInfo,
            ...updatedViVehicleInfo,
          });
      }),

    [ACTIONS.SET_ALL_GL_ACCOUNTS]: (state, { payload = EMPTY_ARRAY }) =>
      produce(state, draft => {
        draft.accounting.allGLAccounts = payload;
      }),

    [ACTIONS.SET_ALL_JOURNALS]: (state, { payload = EMPTY_ARRAY }) =>
      produce(state, draft => {
        draft.accounting.allJournals = payload;
      }),

    [ACTIONS.SET_ALL_VEHICLE_OPTIONS]: (state, { payload = EMPTY_ARRAY }) =>
      produce(state, draft => {
        draft.vehicleOptions.allMakes = payload.makes;
        draft.vehicleOptions.allModels = payload.models;
        draft.vehicleOptions.modelsGroupedByMake = payload.modelsGroupedByMake;
      }),

    [ACTIONS.GET_BODY_STYLES]: (state, { payload = EMPTY_ARRAY }) =>
      produce(state, draft => {
        const bodyStyles = _get(payload, 'bodystyle') || EMPTY_ARRAY;
        const bodyStyleOptions = bodyStyles.map(style => ({
          value: style,
          label: style,
        }));
        draft.trimInfos.bodyStyle = bodyStyleOptions;
      }),

    [ACTIONS.GET_FUEL_TYPES]: (state, { payload = EMPTY_ARRAY }) =>
      produce(state, draft => {
        const fuelTypes = _get(payload, 'fueltype') || EMPTY_ARRAY;
        const fuelTypeOptions = fuelTypes.map(fuel => ({
          value: fuel,
          label: fuel,
        }));
        draft.trimInfos.fuelTypes = fuelTypeOptions;
      }),

    [ACTIONS.GET_VEHICLE_MAKES]: (state, { payload = EMPTY_ARRAY }) =>
      produce(state, draft => {
        draft.vehicleOptions.makes = _get(payload, 'makes');
      }),

    [ACTIONS.GET_VEHICLE_MODELS]: (state, { payload = EMPTY_OBJECT }) =>
      produce(state, draft => {
        _map(_keys(payload), make => _set(draft, ['vehicleOptions', 'models', make], _get(payload, [make, 'data'])));
      }),

    [ACTIONS.GET_VEHICLE_MFR_CODES]: (state, { payload = EMPTY_OBJECT }) =>
      produce(state, draft => {
        _map(_keys(payload), make => _set(draft, ['vehicleOptions', 'mfrCodes', make], _get(payload, [make])));
      }),

    [ACTIONS.GET_COST_ADJUSTMENTS]: (state, { payload = EMPTY_OBJECT }) =>
      produce(state, draft => {
        draft.costAdjustments = payload;
      }),

    [ACTIONS.GET_DUE_BILLS]: (state, { payload = EMPTY_OBJECT }) =>
      produce(state, draft => {
        draft.dueBills = payload;
      }),

    [ACTIONS.GET_CMS_TEKION_LENDER_CODES]: (state, { payload = EMPTY_OBJECT }) =>
      produce(state, draft => {
        draft.cmsTekionLenderCodes = payload?.response || EMPTY_ARRAY;
      }),

    [ACTIONS.UPDATE_LENDER_IN_STORE]: (state, { payload = EMPTY_OBJECT }) =>
      produce(state, draft => {
        const lenderId = _get(payload, 'id');
        if (lenderId) {
          const existingLenders = _get(state, ['dealerConfig', 'lenders']) || [];
          const matchingIndex = existingLenders.findIndex(({ id }) => id === lenderId);
          if (matchingIndex >= 0) {
            _set(draft, ['dealerConfig', 'lenders', matchingIndex], payload);
          } else {
            draft.dealerConfig.lenders.push(payload);
          }
        }
      }),
    [ACTIONS.GET_VI_METADATA]: (state, { payload }) =>
      produce(state, draft => {
        draft.viMetaData = payload;
      }),
    [ACTIONS.FETCH_VEHICLE_PO]: (state, { payload = EMPTY_OBJECT }) =>
      produce(state, draft => {
        draft.vehiclePOs = {
          ...(draft.vehiclePOs || EMPTY_OBJECT),
          ...(_get(payload, 'data') || EMPTY_OBJECT),
        };
      }),
    [ACTIONS.GET_VEHICLE_TYPES]: (state, { payload = EMPTY_ARRAY }) =>
      produce(state, draft => {
        draft.vehicleTypes = payload;
      }),
    [ACTIONS.SET_VI_SETTINGS]: (state, { payload = EMPTY_ARRAY }) =>
      produce(state, draft => {
        draft.viSettings = payload;
      }),
    [ACTIONS.SET_CUSTOM_VEHICLE_INV_FIELDS_BULK]: (state, { payload = EMPTY_ARRAY }) =>
      produce(state, draft => {
        draft.customViFields = payload;
      }),
    [ACTIONS.SET_CUSTOM_VEHICLE_INV_FIELDS]: (state, { payload = EMPTY_ARRAY }) =>
      produce(state, draft => {
        const { source, response } = payload;
        _set(draft.customViFields, source, response);
      }),
    [ACTIONS.GET_VI_V2_INVANTORY_SETUP]: (state, { payload = EMPTY_ARRAY }) =>
      produce(state, draft => {
        draft.viV2InvantorySetup = payload;
      }),
    [ACTIONS.GET_TRIM_LIST]: (state, { payload }) =>
      produce(state, draft => {
        draft.trimInfos.trims = payload;
      }),
    [ACTIONS.GET_DEALERS]: (state, { payload }) =>
      produce(state, draft => {
        draft.dealers = payload;
      }),
    [ACTIONS.GET_TAX_CODES]: (state, { payload }) =>
      produce(state, draft => {
        draft.taxCodes = payload;
      }),
    [ACTIONS.SET_RECENT_ASSIGNEES]: (state, { payload }) =>
      produce(state, draft => {
        draft.recentAssignees = payload;
      }),

    [ACTIONS.GET_VEHICLE_INFO_BY_ID]: (state, { payload }) =>
      produce(state, draft => {
        const vehicleId = _get(payload, 'id');
        if (vehicleId) {
          _set(draft.vehicles, [vehicleId], payload);
        }
      }),

    [ACTIONS.DEALER_SITES]: (state, { payload }) =>
      produce(state, draft => {
        const { siteInfos, dealerIds } = payload;

        _forEach(dealerIds, (id, index) => {
          _set(draft.dealerSites, [id], siteInfos[index] || EMPTY_ARRAY);
        });
      }),

    [ACTIONS.SET_STATE_LIST]: (state, { stateListDetails }) =>
      produce(state, draft => {
        draft.stateListDetails = stateListDetails || EMPTY_OBJECT;
      }),

    [ACTIONS.STORE_USER_LOOKUP_INFO_BY_IDS]: (state, { usersInfo }) =>
      produce(state, draft => {
        draft.userLookupInfoByIds = { ...draft.userLookupInfoByIds, ...usersInfo } || EMPTY_OBJECT;
      }),
    [ACTIONS.SET_GENERATED_DOCUMENT_FROM_IMAGE]: (state, { payload }) =>
      produce(state, draft => {
        draft.generatedDocumentFromImage = payload;
      }),
    [ACTIONS.SET_ALL_FNI_DISCLOSURE_TYPE_OPTIONS]: (state, { disclosureTypeOptions }) =>
      produce(state, draft => {
        draft.fniDisclosureTypeOptions = disclosureTypeOptions;
      }),
    [ACTIONS.SET_ALL_DUE_BILL_DISCLOSURE_TYPE_OPTIONS]: (state, { disclosureTypeOptions }) =>
      produce(state, draft => {
        draft.dueBillDisclosureTypeOptions = disclosureTypeOptions;
      }),
  },
  initialState
);
