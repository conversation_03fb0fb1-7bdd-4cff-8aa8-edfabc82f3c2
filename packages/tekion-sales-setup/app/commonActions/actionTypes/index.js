import ACCOUNTING_ACTION_TYPES from './accounting.actionTypes';

export default {
  ...ACCOUNTING_ACTION_TYPES,
  FETCH_USER_LIST_BY_ROLES: 'FETCH_USER_LIST_BY_ROLES',
  FETCH_USER_LIST_BY_IDS: 'FETCH_USER_LIST_BY_IDS',
  GET_CUSTOMERS_BY_ID: 'GET_CUSTOMERS_BY_ID',
  GET_VEHICLES_BY_ID: 'GET_VEHICLES_BY_ID',
  UPDATE_VI_VEHICLE_IN_STORE: 'UPDATE_VI_VEHICLE_IN_STORE',
  GET_DEALER_INFO: 'GET_DEALER_INFO',
  GET_DEALER_CONFIGS: 'GET_DEALER_CONFIGS',
  GET_USER_ROLES_AND_PERMISSIONS: 'GET_USER_ROLES_AND_PERMISSIONS',
  GET_ALL_ROLES: 'GET_ALL_ROLES',
  GET_ALL_ROLES_V2: 'GET_ALL_ROLES_V2',
  GET_MAPPED_PRINTERS: 'GET_MAPPED_PRINTERS',
  GET_ALL_PRINTERS: 'GET_ALL_PRINTERS',
  SET_ACCOUNTING_DATA: 'SET_ACCOUNTING_DATA',
  UPDATE_VEHICLE_DETAILS_IN_ALL_VEHICLES: 'UPDATE_VEHICLE_DETAILS_IN_ALL_VEHICLES',
  SET_ALL_GL_ACCOUNTS: 'SET_ALL_GL_ACCOUNTS',
  SET_ALL_JOURNALS: 'SET_ALL_JOURNALS',
  SET_ALL_VEHICLE_OPTIONS: 'SET_ALL_VEHICLE_OPTIONS',
  GET_FUEL_TYPES: 'GET_FUEL_TYPES',
  GET_BODY_STYLES: 'GET_BODY_STYLES',
  GET_VEHICLE_MAKES: 'GET_VEHICLE_MAKES',
  GET_VEHICLE_MODELS: 'GET_VEHICLE_MODELS',
  GET_VEHICLE_MFR_CODES: 'GET_VEHICLE_MFR_CODES',
  FETCH_ALL_USERS: 'FETCH_ALL_USERS',
  GET_COST_ADJUSTMENTS: 'GET_COST_ADJUSTMENTS',
  GET_DUE_BILLS: 'GET_DUE_BILLS',
  UPDATE_LENDER_IN_STORE: 'UPDATE_LENDER_IN_STORE',
  GET_VI_METADATA: 'GET_VI_METADATA',
  FETCH_VEHICLE_PO: 'FETCH_VEHICLE_PO',
  GET_VEHICLE_TYPES: 'GET_VEHICLE_TYPES',
  GET_TRIM_LIST: 'GET_TRIM_LIST',
  GET_DEALERS: 'GET_DEALERS',
  GET_CONCIERGE_SETUP: 'GET_CONCIERGE_SETUP',
  GET_TAX_CODES: 'GET_TAX_CODES',
  SET_RECENT_ASSIGNEES: 'SET_RECENT_ASSIGNEES',
  GET_VEHICLE_INFO_BY_ID: 'GET_VEHICLE_INFO_BY_ID',
  DEALER_SITES: 'DEALER_SITES',
  SET_VI_SETTINGS: 'SET_VI_SETTINGS',
  SET_CUSTOM_VEHICLE_INV_FIELDS_BULK: 'SET_CUSTOM_VEHICLE_INV_FIELDS_BULK',
  SET_STATE_LIST: 'SET_STATE_LIST',
  SET_CUSTOM_VEHICLE_INV_FIELDS: 'SET_CUSTOM_VEHICLE_INV_FIELDS',
  STORE_USER_LOOKUP_INFO_BY_IDS: 'STORE_USER_LOOKUP_INFO_BY_IDS',
  SET_GENERATED_DOCUMENT_FROM_IMAGE: 'SET_GENERATED_DOCUMENT_FROM_IMAGE',
  GET_CMS_TEKION_LENDER_CODES: 'GET_CMS_TEKION_LENDER_CODES',
  SET_ALL_FNI_DISCLOSURE_TYPE_OPTIONS: 'SET_ALL_FNI_DISCLOSURE_TYPE_OPTIONS',
  SET_ALL_DUE_BILL_DISCLOSURE_TYPE_OPTIONS: 'SET_ALL_DUE_BILL_DISCLOSURE_TYPE_OPTIONS',
  GET_VI_V2_INVANTORY_SETUP: 'GET_VI_V2_INVANTORY_SETUP',
};
