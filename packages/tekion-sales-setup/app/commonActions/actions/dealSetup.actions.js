import _get from 'lodash/get';
import _map from 'lodash/map';
import _isEmpty from 'lodash/isEmpty';
import _forEach from 'lodash/forEach';

import { createAction } from 'redux-actions';
import { toaster } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import { BASE_REDUCER_KEY } from 'constants/constants';
import DealSetupAPI from 'commonActions/apis/dealSetup.api';
import { getStateTaxConfigMetaData, fetchCountiesAndCities } from 'utils/dealSetup.utils';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';
import { isCommercialSalesSetup } from 'utils/dealerUtils';
import { modifySalesSetupInfo } from 'utils/desking.helpers';
import ACTION_TYPES from '../actionTypes/dealSetup.actionTypes';

export const changeDealTypeInfo = createAction(ACTION_TYPES.DEAL_TYPE_INNFO_CHANGE);
export const changeDealStatusInfo = createAction(ACTION_TYPES.DEAL_STATUS_INFO_CHANGE);
export const changeDealStatusRule = createAction(ACTION_TYPES.DEAL_STATUS_RULE_CHANGE);

export const changeRebateConfig = createAction(ACTION_TYPES.REBATE_CONFIGS);
export const setDeskingFieldsOrderInfo = createAction(ACTION_TYPES.SET_DESKING_FIELDS_ORDER_INFO);
export const setTemplateDetails = createAction(ACTION_TYPES.SET_TEMPLATE_DETAILS);
export const setTemplateName = createAction(ACTION_TYPES.SET_TEMPLATE_NAME);
export const changeDeskingFieldsInfo = createAction(ACTION_TYPES.DESKING_FIELDS_INFO_CHANGE);
export const customStatusInfoChange = createAction(ACTION_TYPES.CUSTOM_STATUS_INFO_CHANGE);
export const changeWorkingCash = createAction(ACTION_TYPES.WORKING_CASH_INFO_CHANGE);

export const changePaymentOptionConfig = createAction(ACTION_TYPES.CHANGE_PAYMENT_OPTION_CONFIG);
export const bulkUpdatePaymentOptionConfigs = createAction(ACTION_TYPES.BULK_UPDATE_PAYMENT_OPTION_CONFIGS);
export const changeGross = createAction(ACTION_TYPES.GROSS_CHANGE);

// can be one action
export const changeDealConfig = createAction(ACTION_TYPES.DEAL_CONFIG_CHANGE);
export const setupValueSalesSetupInfo = createAction(ACTION_TYPES.SET_VALUE_SALES_SETUP_INFO);

export const changeDownPaymentValue = createAction(ACTION_TYPES.DOWN_PAYMENT_VALUE_CHANGE);
export const changeCalculationParameter = createAction(ACTION_TYPES.DOWN_PAYMENT_CALCAULATION_PARAMTER_CHANGE);

export const addNewTarget = createAction(ACTION_TYPES.CONFIG_ADD_NEW_TARGET);
export const removeTarget = createAction(ACTION_TYPES.REMOVE_TARGET);
export const changeTargettingParams = createAction(ACTION_TYPES.TARGET_CHANGE);

export const addNewApplicationCriterion = createAction(ACTION_TYPES.CONFIG_ADD_NEW_APPLICATION_CRITERION);
export const removeApplicationCriterion = createAction(ACTION_TYPES.REMOVE_APPLICATION_CRITERION);
export const changeApplicationCriterion = createAction(ACTION_TYPES.APPLICATION_CRITERION_CHANGE);

export const removeConfig = createAction(ACTION_TYPES.REMOVE_CONFIG);
export const changeConfigName = createAction(ACTION_TYPES.CONFIG_NAME_CHANGE);
export const addNewConfig = createAction(ACTION_TYPES.ADD_CONFIG);

export const changeFeeCodes = createAction(ACTION_TYPES.CHANGE_FEE_CODES);

export const changeIntegrations = createAction(ACTION_TYPES.CHANGE_INTEGRATIONS);
export const changeAddressForRegistration = createAction(
  ACTION_TYPES.CHANGE_INTEGRATIONS_PEN_SITEMAPPING_ADDRESS_REGISTRATION
);
export const changeSiteMappingIntegration = createAction(ACTION_TYPES.CHANGE_INTEGRATIONS_PEN_SITEMAPPING);
export const changeComplianceSetup = createAction(ACTION_TYPES.CHANGE_COMPLIANCE_SETUP);
export const changeStateFeeConfig = createAction(ACTION_TYPES.CHANGE_STATE_FEE_CONFIG);
export const changeSalesFeeSetup = createAction(ACTION_TYPES.CHANGE_SALES_FEE_RULE_SETUP);
export const changeStateTaxOption = createAction(ACTION_TYPES.CHANGE_STATE_TAX_OPTION);
export const changeReciprocalTaxConfig = createAction(ACTION_TYPES.CHANGE_RECIPROCAL_TAX_CONFIG);
export const changeReciprocalTaxOption = createAction(ACTION_TYPES.CHANGE_RECIPROCAL_TAX_OPTION);
export const changeReciprocalTaxPercentValue = createAction(ACTION_TYPES.CHANGE_RECIPROCAL_TAX_PERCENT_VALUE);
export const onReciprocalEdit = createAction(ACTION_TYPES.ON_RECIPROCAL_EDIT);
export const onReciprocalDelete = createAction(ACTION_TYPES.ON_RECIPROCAL_DELETE);
export const onReciprocalDuplicate = createAction(ACTION_TYPES.ON_RECIPROCAL_DUPLICATE);
export const onReciprocalTitleChange = createAction(ACTION_TYPES.ON_RECIPROCAL_TITLE_CHANGE);
export const changeTaxPercentValue = createAction(ACTION_TYPES.CHANGE_TAX_PCT_VALUE);
export const changeCollectFnIValue = createAction(ACTION_TYPES.CHANGE_COLLECT_FNI_VALUE);
export const updateDisclosureTaxFNIConfig = createAction(ACTION_TYPES.UPDATE_DISCLOSURE_TAX_FNI_CONFIG);
export const changeGeneralFeeConfig = createAction(ACTION_TYPES.CHANGE_GENERAL_FEE_CONFIG);
export const changeStateFeeValue = createAction(ACTION_TYPES.CHANGE_FEE_VALUE);
export const splitTaxConfig = createAction(ACTION_TYPES.SPLIT_TAX_CONFIG);
export const mergeTaxConfig = createAction(ACTION_TYPES.MERGE_TAX_CONFIG);
export const updateStateFeeTaxConfigOnZipUpdate = createAction(ACTION_TYPES.UPDATE_STATE_FEE_TAX_ON_ZIP_UPDATE);
export const updateOtherTaxConfig = createAction(ACTION_TYPES.UPDATE_OTHER_TAX_CONFIG);
export const updateGeneralFeeConfig = createAction(ACTION_TYPES.UPDATE_GENERAL_FEE_CONFIG);
export const changeFeesAcrossStates = createAction(ACTION_TYPES.CHANGE_FEE_ACROSS_STATES);

export const changeDealPostingPacks = createAction(ACTION_TYPES.CHANGE_DEAL_POSTING_PACKS);
export const addEmptyPostingPack = createAction(ACTION_TYPES.ADD_EMPTY_POSTING_PACK);

export const updateCostAdjustments = createAction(ACTION_TYPES.UPDATE_COST_ADJUSTMENTS);
export const changeSetupKeys = createAction(ACTION_TYPES.CHANGE_SETUP_KEYS);

export const changeUsedVehicleValuation = createAction(ACTION_TYPES.CHANGE_USED_VEHICLE_VALUATION);
export const setDefaultUsedVehicleValuation = createAction(ACTION_TYPES.SET_DEFAULT_USED_VEHICLE_VALUATION);

export const addDisclosureConfig = createAction(ACTION_TYPES.ADD_DISCLOSURE_TYPE_CONFIG);
export const discountMatrixUpdate = createAction(ACTION_TYPES.DISCOUNT_MATRIX_UPDATE);
export const updateDisclosureTypeConfig = createAction(ACTION_TYPES.UPDATE_DISCLOSURE_TYPE_CONFIG);

export const customFormCategoriesUpdate = createAction(ACTION_TYPES.CUSTOM_FORM_CATEGORIES);

export const updateDepositRule = createAction(ACTION_TYPES.UPDATE_DEPOSIT_RULE);

export const setStateFeeTargetingConfig = createAction(ACTION_TYPES.SET_STATE_FEE_TARGETING_CONFIG);

export const updateVendor = createAction(ACTION_TYPES.UPDATE_VENDOR);
export const deleteVendor = createAction(ACTION_TYPES.DELETE_VENDOR);
export const setFormValuesForSppLender = createAction(ACTION_TYPES.SAVE_SPP_FORM_VALUES);

export const updateVehicleDeliveryConfiguration = createAction(ACTION_TYPES.VEHICLE_DELIVERY_CONFIG);
export const updateVehicleType = createAction(ACTION_TYPES.SET_SELECTED_VEHICLE_TYPE);

export const getSetupInfo = selectedTemplateId => async dispatch => {
  const fetchSetupInfo = isCommercialSalesSetup() ? DealSetupAPI.getCommercialSetupInfo : DealSetupAPI.getSetupInfo;

  const { response, error } = await fetchSetupInfo(selectedTemplateId);
  const modifiedSalesSetupInfo = modifySalesSetupInfo(response);
  if (response) {
    dispatch({
      type: ACTION_TYPES.GET_SETUP_INFO,
      payload: modifiedSalesSetupInfo,
    });
  }

  return { response, error };
};

export const getStateTaxFeeUIConfig = vehicleCategory => async dispatch => {
  const isCalcEngineEnabled = DealerPropertyHelper.isCalcEngineEnabled();
  const payload = await getStateTaxConfigMetaData({ vehicleCategory, isCalcEngineEnabled });
  if (payload) {
    dispatch({
      type: ACTION_TYPES.STATE_TAX_FEE_UI_CONFIG,
      payload,
    });
  }
  return { payload };
};

export const saveSetupInfo =
  (payload, templateID = '') =>
  async dispatch => {
    const saveNonCommercialSetupInfo = templateID ? DealSetupAPI.saveSetupInfoV2 : DealSetupAPI.saveSetupInfo;
    const saveSaleSetupInfo = isCommercialSalesSetup()
      ? DealSetupAPI.saveCommercialSetupInfo
      : saveNonCommercialSetupInfo;
    const { response, error } = await saveSaleSetupInfo(payload, templateID);

    if (response) {
      dispatch({
        type: ACTION_TYPES.SAVE_SETUP_INFO,
        payload: response,
      });
      toaster('success', __('Saved Succesfully'));
      return;
    }

    if (_get(error, 'detail.displayMessage')) {
      toaster('error', __(error.detail.displayMessage));
    } else {
      toaster('error', __('Failed to save'));
    }
  };

export const fniSearch = payload => async (dispatch, getState) => {
  const fniDefaults = _get(getState()[BASE_REDUCER_KEY], 'genericSalesSetup.fniDefaults');

  if (!_isEmpty(fniDefaults)) {
    return {
      response: fniDefaults,
    };
  }

  const { response, error } = await DealSetupAPI.fniSearch(payload);
  if (response) {
    dispatch({
      type: ACTION_TYPES.FNI_ES_SEARCH,
      payload: response,
    });
  }

  return { response, error };
};

export const feeSearch = text => async () => {
  const { response, error } = await DealSetupAPI.feeSearch(text);
  return { data: { text, results: response }, error };
};

export const feeBulkLookUp = payload => async dispatch => {
  const { response, error } = await DealSetupAPI.feeBulkLookUp(payload);

  if (response) {
    dispatch({
      type: ACTION_TYPES.FEE_BULK_LOOKUP,
      payload: response,
    });
  }

  return { response, error };
};

export const getFNIAttributes = payload => async () => {
  const { response, error } = await DealSetupAPI.getFNIAttributes(payload);
  return { response, error };
};

export const getDueBills = () => async dispatch => {
  const { response, error } = await DealSetupAPI.getDueBills();
  if (response) {
    dispatch({
      type: ACTION_TYPES.GET_DUE_BILLS,
      payload: response,
    });
  }
  return { response, error };
};

export const getGLAccountList = () => async dispatch => {
  const { response, error } = await DealSetupAPI.getGLAccountList();
  if (response) {
    dispatch({
      type: ACTION_TYPES.GL_ALL_GL_ACCOUNTS,
      payload: response,
    });
  }
  return { response, error };
};

export const getFNIProducts = () => async () => {
  const response = await DealSetupAPI.getFNIProducts();
  return response;
};

export const getLendersList = () => async dispatch => {
  const { response, error } = await DealSetupAPI.getLendersList();

  if (response) {
    dispatch({
      type: ACTION_TYPES.GET_LENDERS_LIST,
      payload: response,
    });
  }

  return { response, error };
};

export const getCountiesAndCities = stateCodes => async (dispatch, getState) => {
  const couniesList = _get(getState()[BASE_REDUCER_KEY], 'genericSalesSetup.counties');
  const requiredStates = _forEach(stateCodes, code => !couniesList?.[code]);
  const countiesList = await fetchCountiesAndCities(requiredStates);

  dispatch({
    type: ACTION_TYPES.SET_COUNTIES_AND_CITIES,
    payload: countiesList,
  });
};

export const getDealerProviderMappings = () => async dispatch => {
  const { creditDealerIdMappings = EMPTY_ARRAY } = await DealSetupAPI.getDealerProviderMappings();
  dispatch({
    type: ACTION_TYPES.UPDATE_DEALER_IDS_IN_SETUP,
    payload: _map(creditDealerIdMappings, item => ({ ...item, saved: true })),
  });
};

export const updateDealerProviderId = (provider, providerDealerId) => dispatch => {
  dispatch({
    type: ACTION_TYPES.SET_DEALER_PROVIDER_ID_FOR_PROVIDER,
    payload: {
      provider,
      providerDealerId,
    },
  });
};

export const getAverageAprData = () => async dispatch => {
  const response = await DealSetupAPI.getAverageAprData();
  dispatch({
    type: ACTION_TYPES.GET_AVERAGE_APR_DATA,
    payload: response || EMPTY_OBJECT,
  });
};

export const saveAverageConfig = payload => dispatch => {
  const { updatedAprConfigData } = payload;
  dispatch({
    type: ACTION_TYPES.SAVE_APR_CONFIG,
    payload: updatedAprConfigData || EMPTY_OBJECT,
  });
};

export const getVehiclePrefixOptions = () => async () => {
  const response = await DealSetupAPI.getVehiclePrefixOptions();
  return response || EMPTY_OBJECT;
};
