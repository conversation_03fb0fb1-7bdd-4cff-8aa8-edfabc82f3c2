import {
  DEAL_SERVICE,
  FNI_SERVICE,
  INVENTORY_SERVICE,
  SALES_SETTINGS,
} from '@tekion/tekion-base/constants/deal/endPoint';
import { APP_ROOT_FEE, APP_ROOT_SERVICE_AGGREGATION } from '@tekion/tekion-base/constants/service/appRootEndPoints';

import { Http } from 'services/http';
import { getResponse } from 'utils';

const API_PATHS = {
  SALES_SETUP: `${SALES_SETTINGS}/`,
  COMMERCIAL_SALES_SETUP: `${SALES_SETTINGS}/commercial-setup`,
  STATE_TAX_FEE_UI_CONFIG: '/desking/u/taxsetup/metaData',
  COMMERCIAL_SETUP_STATE_TAX_FEE_UI_CONFIG: '/deskingv2/u/v2/tax-metadata',
  FNI_SEARCH: `${FNI_SERVICE}/u/v1.0.0/fni/fnIDefaults/search/`,
  FEE_SEARCH: `/${APP_ROOT_FEE}/search?withNoDealers=false`,
  BULK_LOOK_UP: '/lookup/ids',
  GET_FNI_ATTRIBITES: `${FNI_SERVICE}/u/v1.0.0/fni/fnIAttributes/`,
  DUE_BILLS: `${SALES_SETTINGS}/duebills`,
  GL_ACCOUNT_LIST: '/accounting/u/glAccount/getAccountsAndKeys',
  FNI_PRODUCTS: `${FNI_SERVICE}/u/v1.0.0/fni/fnIProducts`,
  GET_LENDERER_LIST: `${SALES_SETTINGS}/lender/list`,
  PROVIDERS_LIST: `${FNI_SERVICE}/u/fni/provider/list`,
  UPDATE_CHANGE_IN_VAUTO_FLAGS: `${INVENTORY_SERVICE}/u/v1.0.0/vautoflag/trigger`,
  GET_ALL_FORMS: '/print/u/form/search',
  UPDATE_VENDOR_CREDS: '/realtime/integration/u/v1/UpdateCredentials/Integrations',
  OTHER_CREDIT_BUREAU_SETUP: 'sales/core/u/deal/bureau/dataSource',
  REGISTER_DEALER: `${FNI_SERVICE}/u/v1.0.0/fni/dealer/register`,
  UNREGISTER_DEALER: `${FNI_SERVICE}/u/v1.0.0/fni/dealer/unregister`,
  GET_COUNTIES_AND_CITIES: 'desking/u/fetchCitiesByStateCode',
  SEARCH_VENDORS: 'vms/u/vendors/search',
  GET_ALL_FEES: `${APP_ROOT_SERVICE_AGGREGATION}/fee/v3/all`,
  FETCH_APPONE_OPTIONS: 'retail/sales/u/sales-credit/credit/appone/fetchMappings',
  FETCH_TAX_OPTIONS: 'retail/sales/u/sales-credit/credit/credit/fetchTaxTypes',
  FETCH_DEALER_PROVIDER_MAPPINGS: 'retail/sales/u/sales-credit/credit/getAllDealerProviderMappings',
  SAVE_DEALER_PROVIDER_MAPPING: 'retail/sales/u/sales-credit/credit/addDealerProviderMapping',
  SALES_SETUP_V2: `${SALES_SETTINGS}/v2`,
  SALES_FEE_SETUP: `${DEAL_SERVICE}/u/fee/rule`,
  GET_AVERAGE_APR: 'dealJobs/u/deal/averageAprMap',
  VEHICLE_PREFIX_OPTIONS: `${DEAL_SERVICE}/u/acc-setups/ledger-prefix`,
};

export default class DealSetupAPI {
  // Always fetch master data in salessetup, so use old api with out site id
  static getSetupInfo(templateID) {
    if (templateID) {
      return Http.get(`${API_PATHS.SALES_SETUP_V2}/${templateID}`);
    }
    return Http.get(`${API_PATHS.SALES_SETUP}`);
  }

  static getCommercialSetupInfo() {
    return Http.get(API_PATHS.COMMERCIAL_SALES_SETUP);
  }

  static getStateTaxFeeUIConfig(vehicleCategory) {
    return Http.get(`${API_PATHS.STATE_TAX_FEE_UI_CONFIG}?vehicleCategory=${vehicleCategory}`);
  }

  static getCommercialSetupStateTaxFeeUIConfig() {
    // Note: extension will be based on the programs after onboarding more dealer on commercial sales setup
    return Http.get(`${API_PATHS.COMMERCIAL_SETUP_STATE_TAX_FEE_UI_CONFIG}/FORD_US`);
  }

  static saveSetupInfo(payload) {
    return Http.post(`${API_PATHS.SALES_SETUP}`, payload);
  }

  // save sales setup info as a template
  static saveSetupInfoV2(payload, templateID) {
    return Http.post(`${API_PATHS.SALES_SETUP_V2}/${templateID}`, payload);
  }

  static saveCommercialSetupInfo(payload) {
    return Http.post(API_PATHS.COMMERCIAL_SALES_SETUP, payload);
  }

  static fniSearch(payload) {
    return Http.post(`${API_PATHS.FNI_SEARCH}`, payload);
  }

  static getFNIProducts() {
    return Http.get(`${API_PATHS.FNI_PRODUCTS}`).then(getResponse);
  }

  static feeSearch(payload) {
    return Http.post(`${API_PATHS.FEE_SEARCH}`, payload);
  }

  static feeBulkLookUp(payload) {
    return Http.post(`${API_PATHS.BULK_LOOK_UP}`, payload);
  }

  static getFNIAttributes(payload) {
    return Http.post(`${API_PATHS.GET_FNI_ATTRIBITES}`, payload);
  }

  static getDueBills() {
    return Http.get(API_PATHS.DUE_BILLS);
  }

  static getGLAccountList() {
    return Http.get(API_PATHS.GL_ACCOUNT_LIST);
  }

  static getAllFNIProvidersList() {
    return Http.get(`${API_PATHS.PROVIDERS_LIST}`).then(getResponse);
  }

  static getLendersList() {
    return Http.get(`${API_PATHS.GET_LENDERER_LIST}`);
  }

  static updateChangeInVAutoFlag() {
    return Http.put(API_PATHS.UPDATE_CHANGE_IN_VAUTO_FLAGS);
  }

  static getAllForms = payload => Http.post(API_PATHS.GET_ALL_FORMS, payload);

  static updateVendorCreds(payload) {
    return Http.post(`${API_PATHS.UPDATE_VENDOR_CREDS}`, payload);
  }

  static getOtherCreditBureauSetup() {
    return Http.get(API_PATHS.OTHER_CREDIT_BUREAU_SETUP);
  }

  static updateOtherCreditBureauSetup(payload) {
    return Http.post(`${API_PATHS.OTHER_CREDIT_BUREAU_SETUP}`, payload);
  }

  static getCountiesAndCities(states) {
    return Http.post(`${API_PATHS.GET_COUNTIES_AND_CITIES}`, { stateCode: states });
  }

  static searchVendors({ searchText, start, rows }) {
    const payload = {
      sort: [],
      filters: [],
      searchText,
      groupBy: [],
      includeFields: [],
      searchableFields: [],
      pageInfo: {
        start,
        rows,
      },
    };
    // TODO: Update Payload
    return Http.post(API_PATHS.SEARCH_VENDORS, payload);
  }

  static async getAllFeesConfig() {
    return Http.get(API_PATHS.GET_ALL_FEES).then(getResponse);
  }

  static async getAppOneOptions(appOneDealerId) {
    return Http.post(API_PATHS.FETCH_APPONE_OPTIONS, { appOneDealerId }).then(getResponse);
  }

  static async getTaxOptions() {
    return Http.get(API_PATHS.FETCH_TAX_OPTIONS).then(getResponse);
  }

  static async getDealerProviderMappings() {
    return Http.get(API_PATHS.FETCH_DEALER_PROVIDER_MAPPINGS).then(getResponse);
  }

  static async saveDealerProviderMappings(payload) {
    return Http.post(API_PATHS.SAVE_DEALER_PROVIDER_MAPPING, payload).then(getResponse);
  }

  static getDeliveryChargeSetup() {
    return Http.get(`${API_PATHS.SALES_FEE_SETUP}`);
  }

  static saveDeliveryChargeSetup(payload) {
    return Http.post(API_PATHS.SALES_FEE_SETUP, payload);
  }

  static getAverageAprData() {
    return Http.get(API_PATHS.GET_AVERAGE_APR).then(getResponse);
  }

  static saveAverageAprData(payload) {
    return Http.post(API_PATHS.GET_AVERAGE_APR, payload);
  }

  static getVehiclePrefixOptions() {
    return Http.get(API_PATHS.VEHICLE_PREFIX_OPTIONS).then(getResponse);
  }
}
