import { defaultMemoize } from 'reselect';
import _includes from 'lodash/includes';
import mileageLabeler from 'utils/label';

import {
  SELLING_PRICE_RANGE,
  INVOICE_PRICE_RANGE,
  PAYMENT_TYPE,
  STOCK_TYPE,
  DEAL_TYPE,
  TRADE_IN_ACV_RANGE,
  F_N_I_PRODUCT_SELLING_PRICE_RANGE,
  DUE_BILL_SELLING_PRICE_RANGE,
  MAKES,
  MANUFACTURE_MODEL_CODE,
  MODELS,
  TRIMS,
  TRADE_INS,
  IN_STATE,
  OUT_STATE,
  F_N_I_PRODUCT_COST_PRICE_RANGE,
  DUE_BILL_COST_PRICE_RANGE,
  BODY_CLASS,
  TRADE_IN_OWNERSIP_TYPE,
  STATES,
  DEALERSHIP_STATE,
  COAPPLICANT_STATE,
  LENDERS,
  VEHICLE_STATUS,
  DEALER_CERTIFIED,
  FUEL_TYPE,
  LICENSE_EXPIRATION_DATE_IN_DAYS,
  M<PERSON><PERSON>_YEAR,
  TRADE_IN_YEAR,
  TRADE_IN_MILEAGE_RANGE,
  COLLECT_ONLY_WHEN_COLLECTFEE_IS_ON,
  VEHICLE_CERTIFIED,
  OEM_CERTIFIED,
  STOCK_SUB_TYPE,
  SOLD_VEHICLE_SUB_TYPE,
  TRADE_IN_SUB_TYPE,
  SITE,
  VEHICLE_MILEAGE,
  MODEL_YEAR_DIFFERENCE_LESSER_THAN,
  AGE,
  LICENSE_EXPIRATION_DAYS_GREATER_THAN,
  COUNTY,
  CITY,
  RV_TYPE,
  LANGUAGE,
  APPLICANT_TYPE,
  VEHICLE_CATEGORY,
  TIER_1_WEBSITE,
  COUNTRY,
  CAR_TYPE,
  PART_EXCHANGE,
  REGISTRATION_EXPIRATION_GREATER_THAN,
  REGISTRATION_EXPIRATION_LESSER_THAN,
  CERTIFICATION_STATUS,
  INTERIOR_TRIM,
  DEPOSIT_AMOUNT,
  PAYMENT_OPTION,
  SEAT_MATERIAL,
  VEHICLE_PREFIX,
} from '@tekion/tekion-base/marketScan/constants/constants';

import Select from 'molecules/tableCellRenderers/select';
import ButtonCell from 'molecules/tableCellRenderers/button';
import Input from 'molecules/tableCellRenderers/input';
import VirtualizedMultiSelect from 'molecules/tableCellRenderers/virtualizedMultiSelect';
import { LOAN, LEASE, CASH, ONE_TIME_LEASE, BALLOON } from 'constants/desking.constants';
import { PARAMETERS } from 'pages/salesSetup/components/defaultValues/vendorManagement/vendorManagement.config';

const INPUT_FIELDS = [
  LICENSE_EXPIRATION_DATE_IN_DAYS,
  MODEL_YEAR,
  MODEL_YEAR_DIFFERENCE_LESSER_THAN,
  LICENSE_EXPIRATION_DAYS_GREATER_THAN,
  PART_EXCHANGE,
  REGISTRATION_EXPIRATION_GREATER_THAN,
  REGISTRATION_EXPIRATION_LESSER_THAN,
];
const APPLICATION_CRITERION_INPUT_FIELDS = [
  LICENSE_EXPIRATION_DATE_IN_DAYS,
  MODEL_YEAR_DIFFERENCE_LESSER_THAN,
  LICENSE_EXPIRATION_DAYS_GREATER_THAN,
];

export const MULTI_SELECT_FIELDS = [
  PARAMETERS.STATE_TAX_LOCATION,
  PARAMETERS.COUNTY_TAX_LOCATION,
  PARAMETERS.CITY_TAX_LOCATION,
  PARAMETERS.RTA_TAX_LOCATION,
  PARAMETERS.SALES_TAX_LOCATION,
];

const VIRTUALIZED_MULTI_SELECT_FIELDS = [MODELS, TRIMS, CITY, COUNTY, PARAMETERS.TAX_TYPE, ...MULTI_SELECT_FIELDS];

export const TABLE_IDS = {
  OPERATOR: 'operator',
  ATTRIBUTE: 'attribute',
  VALUES: 'values',
  DELETE: 'delete',
  FROM: 'values.0',
  TO: 'values.1',
};

export const TABLE_ROW_HEIGHT = 40;

export const DEFAULT_COLUMNS = [
  {
    Header: __('Include'),
    accessor: TABLE_IDS.OPERATOR,
    id: TABLE_IDS.OPERATOR,
  },
  {
    Header: __('Parameters'),
    accessor: TABLE_IDS.ATTRIBUTE,
    id: TABLE_IDS.ATTRIBUTE,
  },
  {
    Header: __('Values'),
    accessor: TABLE_IDS.VALUES,
    id: TABLE_IDS.VALUES,
  },
];

export const COLUMNS = defaultMemoize((deleteRequired, hasColumnsForRange) => {
  const requiredColumns = [...DEFAULT_COLUMNS];

  if (hasColumnsForRange) {
    requiredColumns.push({
      Header: __('From'),
      accessor: TABLE_IDS.FROM,
      id: TABLE_IDS.FROM,
      width: 200,
    });
    requiredColumns.push({
      Header: __('To'),
      accessor: TABLE_IDS.TO,
      id: TABLE_IDS.TO,
      width: 200,
    });
  }

  if (deleteRequired) {
    requiredColumns.push({
      Header: __('Actions'),
      accessor: TABLE_IDS.DELETE,
      id: TABLE_IDS.DELETE,
      width: 100,
    });
  }

  return requiredColumns;
});

const getAttributeComponent = (attribute, isApplicationCriteria, selectedAttribute) => {
  if (
    (!isApplicationCriteria && _includes(INPUT_FIELDS, attribute)) ||
    (isApplicationCriteria && _includes(APPLICATION_CRITERION_INPUT_FIELDS, attribute))
  ) {
    return Input;
  }
  if (VIRTUALIZED_MULTI_SELECT_FIELDS.includes(attribute)) {
    return VirtualizedMultiSelect;
  }
  if (selectedAttribute?.showSelectAll) {
    return VirtualizedMultiSelect;
  }
  return Select;
};

export const COLUMN_KEY_VS_RENDERER = defaultMemoize(() => ({
  [TABLE_IDS.OPERATOR]: () => Select,
  [TABLE_IDS.ATTRIBUTE]: () => Select,
  [TABLE_IDS.VALUES]: (attribute, isApplicationCriteria, selectedAttribute) =>
    getAttributeComponent(attribute, isApplicationCriteria, selectedAttribute),
  [TABLE_IDS.DELETE]: () => ButtonCell,
  [TABLE_IDS.FROM]: () => Input,
  [TABLE_IDS.TO]: () => Input,
}));

export const TABLE_MANAGER_PROPS = {
  showSearch: false,
  showFilter: false,
  showHeader: false,
  showOverview: false,
  showSubHeader: false,
};

export const INCULDE_OPTIONS = [
  {
    label: __('Include'),
    value: true,
  },
];

export const VALUES_OPTIONS = [
  {
    label: __('Lease'),
    value: LEASE,
  },
  {
    label: __('Loan'),
    value: LOAN,
  },
  {
    label: __('One Time Pay'),
    value: ONE_TIME_LEASE,
  },
  {
    label: __('Cash'),
    value: CASH,
  },
  {
    label: __('Balloon'),
    value: BALLOON,
  },
];

export const ALLOWED_COLUMNS_FOR_ATTRIBUTE = {
  [SELLING_PRICE_RANGE]: [TABLE_IDS.FROM, TABLE_IDS.TO],
  [INVOICE_PRICE_RANGE]: [TABLE_IDS.FROM, TABLE_IDS.TO],
  [PAYMENT_TYPE]: [TABLE_IDS.VALUES],
  [STOCK_TYPE]: [TABLE_IDS.VALUES],
  [DEAL_TYPE]: [TABLE_IDS.VALUES],
  [TIER_1_WEBSITE]: [TABLE_IDS.VALUES],
  [TRADE_IN_ACV_RANGE]: [TABLE_IDS.FROM, TABLE_IDS.TO],
  [F_N_I_PRODUCT_SELLING_PRICE_RANGE]: [TABLE_IDS.FROM, TABLE_IDS.TO],
  [DUE_BILL_SELLING_PRICE_RANGE]: [TABLE_IDS.FROM, TABLE_IDS.TO],
  [TRADE_INS]: [TABLE_IDS.VALUES],
  [IN_STATE]: [TABLE_IDS.VALUES],
  [OUT_STATE]: [TABLE_IDS.VALUES],
  [MAKES]: [TABLE_IDS.VALUES],
  [MANUFACTURE_MODEL_CODE]: [TABLE_IDS.VALUES],
  [MODELS]: [TABLE_IDS.VALUES],
  [TRIMS]: [TABLE_IDS.VALUES],
  [F_N_I_PRODUCT_COST_PRICE_RANGE]: [TABLE_IDS.FROM, TABLE_IDS.TO],
  [DUE_BILL_COST_PRICE_RANGE]: [TABLE_IDS.FROM, TABLE_IDS.TO],
  [BODY_CLASS]: [TABLE_IDS.VALUES],
  [INTERIOR_TRIM]: [TABLE_IDS.VALUES],
  [SEAT_MATERIAL]: [TABLE_IDS.VALUES],
  [CERTIFICATION_STATUS]: [TABLE_IDS.VALUES],
  [TRADE_IN_OWNERSIP_TYPE]: [TABLE_IDS.VALUES],
  [STATES]: [TABLE_IDS.VALUES],
  [DEALERSHIP_STATE]: [TABLE_IDS.VALUES],
  [COAPPLICANT_STATE]: [TABLE_IDS.VALUES],
  [LENDERS]: [TABLE_IDS.VALUES],
  [VEHICLE_STATUS]: [TABLE_IDS.VALUES],
  [DEALER_CERTIFIED]: [TABLE_IDS.VALUES],
  [FUEL_TYPE]: [TABLE_IDS.VALUES],
  [LICENSE_EXPIRATION_DATE_IN_DAYS]: [TABLE_IDS.VALUES],
  [MODEL_YEAR]: [TABLE_IDS.VALUES],
  [AGE]: [TABLE_IDS.FROM, TABLE_IDS.TO],
  [MODEL_YEAR_DIFFERENCE_LESSER_THAN]: [TABLE_IDS.VALUES],
  [TRADE_IN_YEAR]: [TABLE_IDS.VALUES],
  [TRADE_IN_MILEAGE_RANGE]: [TABLE_IDS.FROM, TABLE_IDS.TO],
  [COLLECT_ONLY_WHEN_COLLECTFEE_IS_ON]: [TABLE_IDS.VALUES],
  [VEHICLE_CERTIFIED]: [TABLE_IDS.VALUES],
  [OEM_CERTIFIED]: [TABLE_IDS.VALUES],
  [STOCK_SUB_TYPE]: [TABLE_IDS.VALUES],
  [SOLD_VEHICLE_SUB_TYPE]: [TABLE_IDS.VALUES],
  [TRADE_IN_SUB_TYPE]: [TABLE_IDS.VALUES],
  [SITE]: [TABLE_IDS.VALUES],
  [VEHICLE_MILEAGE]: [TABLE_IDS.FROM, TABLE_IDS.TO],
  [LICENSE_EXPIRATION_DAYS_GREATER_THAN]: [TABLE_IDS.VALUES],
  [COUNTY]: [TABLE_IDS.VALUES],
  [CITY]: [TABLE_IDS.VALUES],
  [PARAMETERS.TAX_TYPE]: [TABLE_IDS.VALUES],
  [PARAMETERS.STATE_TAX_LOCATION]: [TABLE_IDS.VALUES],
  [PARAMETERS.COUNTY_TAX_LOCATION]: [TABLE_IDS.VALUES],
  [PARAMETERS.CITY_TAX_LOCATION]: [TABLE_IDS.VALUES],
  [PARAMETERS.RTA_TAX_LOCATION]: [TABLE_IDS.VALUES],
  [PARAMETERS.SALES_TAX_LOCATION]: [TABLE_IDS.VALUES],
  [RV_TYPE]: [TABLE_IDS.VALUES],
  [LANGUAGE]: [TABLE_IDS.VALUES],
  [APPLICANT_TYPE]: [TABLE_IDS.VALUES],
  [VEHICLE_CATEGORY]: [TABLE_IDS.VALUES],
  [COUNTRY]: [TABLE_IDS.VALUES],
  [CAR_TYPE]: [TABLE_IDS.VALUES],
  [PART_EXCHANGE]: [TABLE_IDS.VALUES],
  [REGISTRATION_EXPIRATION_GREATER_THAN]: [TABLE_IDS.VALUES],
  [REGISTRATION_EXPIRATION_LESSER_THAN]: [TABLE_IDS.VALUES],
  [DEPOSIT_AMOUNT]: [TABLE_IDS.VALUES],
  [PAYMENT_OPTION]: [TABLE_IDS.VALUES],
  [VEHICLE_PREFIX]: [TABLE_IDS.VALUES],
};

export const ATTRIBUTES_VS_FROM_AND_TO_PROPS = {
  [TRADE_IN_MILEAGE_RANGE]: {
    addonBefore: '',
  },
  [VEHICLE_MILEAGE]: {
    addonBefore: '',
    addonAfter: mileageLabeler.getMileageUnitLabel(),
  },
};

export const MODE_SINGLE_STATUS = [
  VEHICLE_STATUS,
  DEALER_CERTIFIED,
  VEHICLE_CERTIFIED,
  OEM_CERTIFIED,
  COLLECT_ONLY_WHEN_COLLECTFEE_IS_ON,
  VEHICLE_CATEGORY,
];
