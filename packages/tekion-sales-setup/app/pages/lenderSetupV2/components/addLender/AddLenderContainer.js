import React, { useCallback, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import { compose } from 'recompose';
import { produce } from 'immer';

import _isEmpty from 'lodash/isEmpty';
import _noop from 'lodash/noop';
import _set from 'lodash/set';
import _get from 'lodash/get';
import _debounce from 'lodash/debounce';
import _size from 'lodash/size';
import _uniq from 'lodash/uniq';

import getRoute from '@tekion/tekion-business/src/factories/route';
import { SALES } from '@tekion/tekion-base/constants/appServices';
import { LENDER_SETUP } from '@tekion/tekion-base/constants/appConfigs/salesAppConfigs';
import MODES from '@tekion/tekion-business/src/constants/routeModes';
import withRouter from '@tekion/tekion-components/src/hoc/withRouter';
import MODULE_ASSET_TYPES from '@tekion/tekion-base/constants/moduleAssetTypes';
import withUserPreferenceColumn from '@tekion/tekion-components/src/connectors/withUserPreferenceColumn';
import { EMPTY_OBJECT, EMPTY_ARRAY, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { DEFAULT_FILTER_GROUP } from '@tekion/tekion-components/src/organisms/filterSection';
import { getQueryParams } from '@tekion/tekion-base/utils/general';
import TableManagerActions from '@tekion/tekion-components/src/organisms/TableManager/constants/actionTypes';
import FORM_ACTION_TYPES from '@tekion/tekion-components/src/organisms/FormBuilder/constants/actionTypes';
import { DELETE_FILTER_LIST_ITEM } from '@tekion/tekion-components/src/organisms/filterSection/filterDialog/molecules/filterListItem/constants/filterListItem.actions';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';

import { findArrayIndex } from 'utils';

import {
  getFilterProps,
  getMultiSortPayload,
  getQueryParamsFilters,
  getSelectedFilterValues,
  getTableProps,
} from '../../utils/LenderSetup.utils';
import { INITAIL_SORT_OBJECT } from '../../constants/LenderSetup.constants';
import TRANSLATIONS from '../../constants/translations';

import {
  ADD_LENDER_COLUMNS_KEYS,
  FILTER_PROPS,
  PAGE_SIZE,
  PREFERANCE_API_END_POINT,
  STATE_OPTIONS,
} from './constants/AddLender.constants';
import { getTabelColumns } from './utils/AddLender.config';
import {
  formateCmsLenderFetchResponse,
  getAddLenderListAPIPayload,
  getCountryCode,
  getDisabledLenderIds,
  getLenderList,
  getLenderSavePayload,
  getSelectionRows,
  getSubHeaderProps,
  getTotalCount,
  isTableItemSelectionDisabledForRow,
} from './utils/addLenders.utils';
import * as AddLenderApis from './apis/AddLender.apis';

import AddLender from './AddLender';

const AddLenderContainer = ({ onClose, navigate, columns, resizedProps, isSingleSelect, onSwapLender, ...rest }) => {
  const [lenders, setLenders] = useState([]);
  const [paramFilters, _setPageFilters] = getQueryParamsFilters(getQueryParams(rest));
  const [isLoading, setIsLoading] = useState(true);
  const [pageDetails, setPageDetails] = useState({
    pageSize: 100,
    count: 0,
    selection: [],
    currentPage: 1,
    searchText: '',
    sort: [],
    sortDetails: {},
    selectedFilters: [],
    selectedFilterGroup: DEFAULT_FILTER_GROUP,
    lenderIds: EMPTY_ARRAY,
    isPrimaryDisabled: false,
    scrollLoading: false,
  });

  const onSearch = useCallback(
    _debounce(async searchText => {
      const { selectedFilters, sort } = pageDetails;
      fetchLenderList(1, selectedFilters, sort, searchText);
    }, 1000),
    [pageDetails]
  );

  const onSearchTextChange = useCallback(
    searchText => {
      setPageDetails(prev => ({ ...prev, searchText }));
      onSearch(searchText);
    },
    [onSearch]
  );

  const onCreateLender = useCallback(() => {
    const lenderFormRoute = getRoute(SALES, LENDER_SETUP.getKey(), {
      mode: MODES.CREATE,
    });
    navigate(lenderFormRoute);
  }, [navigate]);

  const updateStateOnLenderFetch = useCallback(
    async ({ currentPage, response }) => {
      const { lenderList, total, lenderIds } = formateCmsLenderFetchResponse(response);
      const newLenderList = getLenderList(lenderList, currentPage, lenders);
      setLenders(newLenderList);
      setPageDetails(prevState => ({
        ...prevState,
        count: total,
        currentPage: currentPage + 1,
        pageSize: Math.max(_size(newLenderList), PAGE_SIZE),
        selection: EMPTY_ARRAY,
        lenderIds: [...prevState.lenderIds, ...lenderIds],
      }));
    },
    [lenders]
  );

  const fetchLenderList = useCallback(
    async (currentPage = 1, selectedFilters = EMPTY_ARRAY, sort = EMPTY_ARRAY, searchText = EMPTY_STRING) => {
      setIsLoading(true);
      const payload = getAddLenderListAPIPayload({
        ...pageDetails,
        selectedFilters: selectedFilters || pageDetails.selectedFilters,
        currentPage,
        sort: sort || pageDetails.sort,
        searchText: searchText || pageDetails.searchText,
      });
      const response = await AddLenderApis.getLenderList({ payload, searchText: searchText || pageDetails.searchText });
      if (response) {
        updateStateOnLenderFetch({ currentPage, response });
      } else {
        toaster(TOASTER_TYPE.ERROR, __('Failed to fetch Cms lenders'));
        setLenders(EMPTY_ARRAY);
      }
      setIsLoading(false);
    },
    [pageDetails, updateStateOnLenderFetch]
  );

  const openLenderForm = lenderId => {
    const lenderFormRoute = getRoute(SALES, LENDER_SETUP.getKey(), {
      mode: MODES.EDIT,
      id: lenderId,
    });

    navigate(lenderFormRoute);
  };

  const onAddLender = async () => {
    const { selection } = pageDetails;
    setIsLoading(true);
    setPageDetails(prev => ({ ...prev, isPrimaryDisabled: true }));

    const payload = getLenderSavePayload(selection, lenders);
    if (onSwapLender) {
      await onSwapLender(payload, onClose);
      setIsLoading(false);
      setPageDetails(prev => ({ ...prev, isPrimaryDisabled: false }));
      return;
    }

    const response = await AddLenderApis.saveLenders(payload);
    if (response && !_isEmpty(response)) {
      const lenderId = response[selection[0]];
      if (selection.length === 1) {
        openLenderForm(lenderId);
      } else {
        onClose({ fetchLenders: true });
      }
    } else {
      toaster(TOASTER_TYPE.ERROR, TRANSLATIONS.SWAP_LENDER_ERROR);
    }
    setPageDetails(prev => ({ ...prev, isPrimaryDisabled: false }));
    setIsLoading(false);
  };

  const onFilterAction = useCallback(
    action => {
      const { payload, type: actionType } = action;
      if (actionType === FORM_ACTION_TYPES.ON_FIELD_CHANGE) {
        const { operator, type, values } = _get(payload, 'value', {});
        const { filterChanges: prevFilterChanges } = pageDetails;
        const newFilters = { ...prevFilterChanges, [type]: { operator, values } };
        pageDetails(prev => ({ ...prev, filterChanges: newFilters }));
      }
      if (actionType === DELETE_FILTER_LIST_ITEM) {
        pageDetails(prev => ({ ...prev, filterChanges: {} }));
      }
    },
    [pageDetails]
  );

  const onLenderTypeChange = useCallback(
    id => value => {
      const newLenders = produce(lenders, draft => {
        const matchedLenderIndex = findArrayIndex(draft, 'id', id);
        if (matchedLenderIndex >= 0) {
          _set(draft, [matchedLenderIndex, ADD_LENDER_COLUMNS_KEYS.TYPE], value);
        }
      });
      setLenders(newLenders);
      setPageDetails(prev => ({
        ...prev,
        selection: _uniq([...prev.selection, id]),
      }));
    },
    [lenders]
  );

  const onTableItemSelection = useCallback(
    ({ value: selection }) => setPageDetails(prev => ({ ...prev, selection })),
    []
  );

  const onMultiSort = useCallback(
    value => {
      const sortDetails = _get(value, 'sortDetails') || _get(value, 'sortTypeMap');
      const sort = getMultiSortPayload(sortDetails);

      setPageDetails(prev => ({ ...prev, sort, sortDetails }));
      const { selectedFilters, searchText } = pageDetails;
      fetchLenderList(1, selectedFilters, sort, searchText);
    },
    [fetchLenderList, pageDetails]
  );

  const onFilterChange = useCallback(
    (retainSort = false, selectedFilters) => {
      const { sortDetails, searchText } = pageDetails;
      const sort = retainSort ? getMultiSortPayload(sortDetails) : [INITAIL_SORT_OBJECT];
      setPageDetails(prev => ({ ...prev, sort }));
      fetchLenderList(1, selectedFilters, sort, searchText);
    },
    [fetchLenderList, pageDetails]
  );

  const handleRowClick = payload => {
    const rowId = _get(payload, ['value', 'original', 'id']);
    if (isTableItemSelectionDisabledForRow(pageDetails.selection, rowId, isSingleSelect)) return;
    setPageDetails(prev => ({
      ...prev,
      selection: getSelectionRows(prev.selection, rowId),
    }));
  };

  const handleTableScroll = async () => {
    const { selection } = pageDetails;
    setPageDetails(prev => ({ ...prev, scrollLoading: true }));
    await fetchLenderList(
      pageDetails.currentPage,
      pageDetails.selectedFilters,
      pageDetails.sort,
      pageDetails.searchText
    );
    setPageDetails(prev => ({ ...prev, selection, scrollLoading: false }));
  };

  const onAction = async action => {
    const { payload } = action;
    switch (action.type) {
      case TableManagerActions.TABLE_ITEMS_PAGE_UPDATE: {
        await fetchLenderList(
          payload.value.page,
          pageDetails.selectedFilters,
          pageDetails.sort,
          pageDetails.searchText
        );
        break;
      }
      case TableManagerActions.TABLE_ITEM_CLICK: {
        handleRowClick(payload);
        break;
      }
      case TableManagerActions.TABLE_SCROLL_FETCH: {
        handleTableScroll();
        break;
      }
      case TableManagerActions.TABLE_ITEM_SELECT: {
        onTableItemSelection(payload);
        break;
      }
      case TableManagerActions.TABLE_ITEMS_SORT:
      case TableManagerActions.TABLE_ITEMS_FETCH_WITH_FILTER_AND_SORT_PREFERENCES: {
        onMultiSort(payload.value);
        break;
      }
      case TableManagerActions.TABLE_ITEMS_SET_FILTER: {
        const { value, selectedFilterGroup } = payload || EMPTY_OBJECT;
        const selectedFilters = getSelectedFilterValues(value, paramFilters);
        setPageDetails(prev => ({
          ...prev,
          selectedFilters,
          selectedFilterGroup,
        }));
        onFilterChange(true, selectedFilters);
        break;
      }
      default:
    }
  };

  const tableProps = useMemo(
    () =>
      getTableProps({
        ...pageDetails,
        isLoading,
        resizedProps,
        showTotalCount: false,
        isSingleSelect,
        showPagination: false,
        disabled: getDisabledLenderIds(pageDetails.lenderIds, pageDetails.selection, isSingleSelect),
        useInfiniteScroll: _size(lenders) < pageDetails.count,
        scrollLoading: pageDetails.scrollLoading,
      }),
    [isLoading, isSingleSelect, lenders, pageDetails, resizedProps]
  );

  const subHeaderProps = useMemo(
    () => getSubHeaderProps({ onSearchTextChange, searchText: pageDetails.searchText }),
    [onSearchTextChange, pageDetails.searchText]
  );

  const filterProps = useMemo(
    () =>
      getFilterProps({
        selectedFilters: pageDetails.selectedFilters,
        selectedFilterGroup: _isEmpty(paramFilters) ? pageDetails.selectedFilterGroup : DEFAULT_FILTER_GROUP,
        onAction: onFilterAction,
        assetType: MODULE_ASSET_TYPES.CMS_LENDERS,
        filterProps: FILTER_PROPS(STATE_OPTIONS[getCountryCode()]),
        isFilterLabelStacked: true,
        showResultsCount: false,
      }),
    [onFilterAction, pageDetails.selectedFilterGroup, pageDetails.selectedFilters, paramFilters]
  );

  return (
    <AddLender
      lenders={lenders}
      columns={getTabelColumns({ columns, onLenderTypeChange, selection: pageDetails.selection, isSingleSelect })}
      isPrimaryDisabled={!_size(pageDetails.selection) || pageDetails.isPrimaryDisabled}
      onAddLender={onAddLender}
      onCreateLender={onCreateLender}
      onCancel={onClose}
      navigate={navigate}
      onAction={onAction}
      tableProps={tableProps}
      subHeaderProps={subHeaderProps}
      sortDetails={pageDetails.sortDetails}
      filterProps={filterProps}
      selection={pageDetails.selection}
      {...rest}
    />
  );
};

AddLenderContainer.propTypes = {
  navigate: PropTypes.func.isRequired,
  columns: PropTypes.array.isRequired,
  onClose: PropTypes.func,
  resizedProps: PropTypes.object,
  isSingleSelect: PropTypes.bool,
  onSwapLender: PropTypes.func,
};

AddLenderContainer.defaultProps = {
  onClose: _noop,
  resizedProps: EMPTY_OBJECT,
  isSingleSelect: false,
  onSwapLender: null,
};

export default compose(
  withUserPreferenceColumn(
    MODULE_ASSET_TYPES.CMS_LENDERS,
    EMPTY_ARRAY,
    EMPTY_OBJECT,
    true,
    false,
    EMPTY_OBJECT,
    PREFERANCE_API_END_POINT,
    true,
    undefined,
    true
  ),
  withRouter
)(AddLenderContainer);
