import React, { Component } from 'react';
import { Route, Routes, Navigate } from 'react-router-dom';
import withRouter from '@tekion/tekion-components/src/hoc/withRouter';
import { compose } from 'recompose';

import { PermissionsRoute } from '@tekion/tekion-components/src/widgets/permissionsHelper';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';
import DealerPropertyHelperOld from '@tekion/tekion-components/src/helpers/sales/dealerPropertyHelper';

// constants
import { PERMISSIONS } from '@tekion/tekion-base/constants/permissions';

import withDealerPropertyContext from 'organisms/withDealerPropertyContext';
import PermissionHelper from 'permissions/permissions.helper';
import ROUTES from '@tekion/tekion-base/utils/sales/routes';

import SalesSetup from 'pages/salesSetup/SalesSetupLazy';
import CommissionsSetupLazy from 'pages/commissionsSetup/CommissionsSetupLazy';
import CommissionDetailsLazy from 'pages/commissionsSetup/components/commissionDetails/CommissionDetailsLazy';
import SpiffDetailsLazy from 'pages/commissionsSetup/components/spiffDetails/SpiffDetailsLazy';
import LenderSetupV2 from 'pages/lenderSetupV2';
import DueBillsSetup from 'pages/dueBillsSetup/DueBillsSetupLazy';
import LenderForm from 'pages/lenderSetupV2/components/lenderForm';
import FieldRuleSetup from '../../fieldRulesSetup/FieldRulesSetupLazy';
import CostAdjustmentSetup from '../../costAdjustmentSetup/CostAdjustmentSetupLazy';
import ExternalDMSMappingV2 from '../../ExternalDMSMappingV2/ExternalDMSMappingV2Lazy';

const {
  SALES: {
    DEALS: { SETUP },
  },
} = PERMISSIONS;

export const SalesRoutes = () => (
  <React.Suspense fallback={null}>
    <Routes>
      <Route
        path="/*"
        element={<PermissionsRoute component={SalesSetup} validFor={[SETUP.SALES_EDIT]} type="error" />}
      />
      <Route
        path={ROUTES.COMMISSION_SETUP}
        element={
          <PermissionsRoute
            component={CommissionsSetupLazy}
            validFor={PERMISSIONS.CORE.COMMISSION_SETUP.EDIT}
            type="error"
          />
        }
      />
      <Route
        path={`${ROUTES.COMMISSION_SETUP_COMMISSION_PLAN}/:id/*`}
        element={
          <PermissionsRoute
            component={CommissionDetailsLazy}
            validFor={PERMISSIONS.CORE.COMMISSION_SETUP.EDIT}
            type="error"
          />
        }
      />
      <Route
        path={`${ROUTES.COMMISSION_SETUP_SPIFF}/:id/*`}
        element={
          <PermissionsRoute
            component={SpiffDetailsLazy}
            validFor={PERMISSIONS.CORE.COMMISSION_SETUP.EDIT}
            type="error"
          />
        }
      />

      <Route
        path={ROUTES.EDIT_LENDER}
        element={<PermissionsRoute component={LenderForm} validFor={[SETUP.LENDER_EDIT]} type="error" />}
      />

      <Route
        path={ROUTES.CREATE_LENDER}
        element={<PermissionsRoute component={LenderForm} validFor={[SETUP.LENDER_EDIT]} type="error" />}
      />

      <Route
        path={ROUTES.LENDER_SETUP_V2}
        element={<PermissionsRoute component={LenderSetupV2} validFor={[SETUP.LENDER_EDIT]} type="error" />}
      />

      <Route path={`${ROUTES.DUE_BILLS_SETUP}/*`} element={<DueBillsSetup />} />
      <Route path={`${ROUTES.FIELD_RULES_SETUP}/*`} element={<FieldRuleSetup />} />
      <Route path={`${ROUTES.COST_ADJUSTMENT_SETUP}/*`} element={<CostAdjustmentSetup />} />
      <Route path={`${ROUTES.EXTERNAL_DMS_MAPPING_V2}/*`} element={<ExternalDMSMappingV2 />} />

      <Route path="*" element={<Navigate to="/home" replace />} />
    </Routes>
  </React.Suspense>
);

class CustomRoutes extends Component {
  constructor(props) {
    super(props);
    // eslint-disable-next-line react/prop-types
    const { permission, getDealerPropertyValue } = props;
    PermissionHelper.init(permission);
    DealerPropertyHelper.initPropertyGetter(getDealerPropertyValue);
    DealerPropertyHelperOld.initPropertyGetter(getDealerPropertyValue);
  }

  render() {
    return <SalesRoutes />;
  }
}

CustomRoutes.propTypes = {};

CustomRoutes.defaultProps = {};

export default compose(withDealerPropertyContext, withRouter)(CustomRoutes);
