import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';

import _get from 'lodash/get';

import { BASE_REDUCER_KEY } from 'constants/constants';

import { COMMISSION_PLAN_TYPE } from 'pages/commissionsSetup/CommissionsSetup.constants';
import { connect } from 'react-redux';
import * as CommissionSelectors from '../../CommissionsSetup.selectors';
import CommissionListBuilder from '../../commissionListBuilder';
import { createFilterProps } from './CommissionPlans.filter';

class CommissionPlans extends PureComponent {
  componentDidMount() {
    const { CommissionsActions } = this.props;
    CommissionsActions.getAllCommissionPlans();
  }

  onSearchCommissionList = searchText => {
    const { CommissionsActions } = this.props;
    CommissionsActions.setCommissionPlanListSearchText(searchText);
  };

  onUpdateFilterParams = payload => {
    const { CommissionsActions } = this.props;
    const { selectedFilterGroup, value: selectedFilters } = payload;
    CommissionsActions.setCommissionPlanListSelectedFilters({ selectedFilters, selectedFilterGroup });
  };

  render() {
    const {
      commissionPlans,
      CommissionsActions,
      openCommissionPlanBuilder,
      conditionParamVsDisplayName,
      searchText,
      selectedFilters,
      selectedFilterGroup,
    } = this.props;
    return (
      <CommissionListBuilder
        listHeading={__('Commission Plans')}
        filterProps={createFilterProps({
          selectedFilters,
          selectedFilterGroup,
          conditionParamVsDisplayName,
          totalResults: commissionPlans.length,
        })}
        listType={COMMISSION_PLAN_TYPE.COMMISSION_AMOUNT}
        data={commissionPlans}
        openCommissionPlanBuilder={openCommissionPlanBuilder}
        CommissionsActions={CommissionsActions}
        onUpdateFilterParams={this.onUpdateFilterParams}
        searchText={searchText}
        onSearch={this.onSearchCommissionList}
      />
    );
  }
}

CommissionPlans.propTypes = {
  CommissionsActions: PropTypes.object.isRequired,
  openCommissionPlanBuilder: PropTypes.func.isRequired,
  commissionPlans: PropTypes.array.isRequired,
  conditionParamVsDisplayName: PropTypes.object.isRequired,
  searchText: PropTypes.string.isRequired,
  selectedFilters: PropTypes.array.isRequired,
  selectedFilterGroup: PropTypes.string.isRequired,
};

function mapStateToProps(globalState) {
  const state = _get(globalState, BASE_REDUCER_KEY);
  return {
    searchText: CommissionSelectors.getCommissionPlansSearchText(state),
    selectedFilters: CommissionSelectors.getCommissionPlansSelectedFilters(state),
    selectedFilterGroup: CommissionSelectors.getCommissionPlansSelectedFilterGroup(state),
  };
}

export default connect(mapStateToProps, {})(CommissionPlans);
