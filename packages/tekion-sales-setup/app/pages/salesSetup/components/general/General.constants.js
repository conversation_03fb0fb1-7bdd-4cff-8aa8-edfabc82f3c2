import { isInchcape, isRRG } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';

const programLevelLabels = () => {
  const newLabels = { ONLY_TRADES: __('ONLY_TRADES'), APR_MONEYFACTOR: __('APR_MONEYFACTOR') };

  if (isInchcape()) {
    newLabels.ONLY_TRADES = __('PURCHASE_ONLY');
  }

  if (isRRG()) {
    newLabels.APR_MONEYFACTOR = __('TAEG');
  }

  return newLabels;
};

export const ENUM_LABELS = () => ({
  PERSONAL: __('PERSONAL'),
  BUSINESS: __('BUSINESS'),
  DEALER_TRADE: __('DEALER_TRADE'),
  DEAL_TRADE_SWAP: __('DEAL_TRADE_SWAP'),
  ONLY_FINANCE_AND_INSURANCE: __('ONLY_FINANCE_AND_INSURANCE'),
  FLEET: __('FLEET'),
  WHOLESALE: __('WHOLESALE'),
  INTERNET: __('INTERNET'),
  REFINANCING: __('REFINANCING'),
  ONLY_TRADES: programLevelLabels().ONLY_TRADES,
  HOUSE: __('HOUSE'),
  INTERNAL_TRANSFER: __('INTERNAL_TRANSFER'),
  EMPLOYEE_PURCHASE: __('EMPLOYEE_PURCHASE'),
  APPROVED: __('APPROVED'),
  APPROVED_PLUS: __('APPROVED_PLUS'),
  CASH_REALISATION: __('CASH_REALISATION'),
  EMPLOYEE_SALE: __('EMPLOYEE_SALE'),
  TRADE: __('TRADE'),
  LENDER: __('LENDER'),
  APR_MONEYFACTOR: programLevelLabels().APR_MONEYFACTOR,
  RESIDUAL: __('RESIDUAL'),
  YEARLY_MILES: __('YEARLY_MILES'),
  REBATE: __('REBATE'),
  DUEBILLS: __('DUEBILLS'),
  FEES: __('FEES'),
  FNIMENU: __('FNIMENU'),
  DAYS_TO_FIRSTPAYMENT: __('DAYS_TO_FIRSTPAYMENT'),
  FINANCE_CONTRACT_NUMBER: __('FINANCE_CONTRACT_NUMBER'),
  SECURITY_DEPOSIT: __('SECURITY_DEPOSIT'),
  PAYMENT_FREQUENCY: __('PAYMENT_FREQUENCY'),
  FINANCE_ADJUSTED_CAPITAL_COST: __('FINANCE_ADJUSTED_CAPITAL_COST'),
  LOAN_TO_VALUE_RATIO: __('LOAN_TO_VALUE_RATIO'),
  TAXES: __('TAXES'),
  DRIVE_OFF: __('DRIVE_OFF'),
  FINANCE_CHARGE: __('FINANCE_CHARGE'),
  SUBVENTION_COST: __('SUBVENTION_COST'),
  OUT_OF_POCKET_CASH: __('OUT_OF_POCKET_CASH'),
  'PROGRAM NUMBER': __('PROGRAM NUMBER'),
  'PROGRAM CODE': __('PROGRAM CODE'),
  'CERTIFICATE NUMBER': __('CERTIFICATE NUMBER'),
  RATE_OF_INTEREST: __('RATE_OF_INTEREST'),
  TOTAL_DEPOSIT: __('TOTAL_DEPOSIT'),
  BALANCE_TO_FINANCE: __('BALANCE_TO_FINANCE'),
  TOTAL_CHARGE_FOR_CREDIT: __('TOTAL_CHARGE_FOR_CREDIT'),
  TOTAL_AMOUNT_PAYABLE: __('TOTAL_AMOUNT_PAYABLE'),
  EXCESS_MILEAGE_CHARGE: __('EXCESS_MILEAGE_CHARGE'),
  OPTION_TO_PURCHASE_FEE: __('OPTION_TO_PURCHASE_FEE'),
  OPTIONAL_FINAL_PAYMENT: __('OPTIONAL_FINAL_PAYMENT'),
  CASH: __('CASH'),
  LOAN: __('LOAN'),
  BALLOON: __('BALLOON'),
  ONE_PAY: __('ONE_PAY'),
  ONE_TIME_LEASE: __('ONE_TIME_LEASE'),
  LEASE: __('LEASE'),
  MOTABILITY: __('MOTABILITY'),
});
