import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import produce from 'immer';
import _get from 'lodash/get';
import _set from 'lodash/set';
import _isEmpty from 'lodash/isEmpty';
import _dropRight from 'lodash/dropRight';
import _castArray from 'lodash/castArray';
import _range from 'lodash/range';
import _map from 'lodash/map';
import _toString from 'lodash/toString';
import _noop from 'lodash/noop';
import _find from 'lodash/find';
import _head from 'lodash/head';
import _isNil from 'lodash/isNil';
import _size from 'lodash/size';
import _filter from 'lodash/filter';

import { findArrayItem, getRandomId, removeMatchedItems } from 'utils';
import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import Heading from '@tekion/tekion-components/src/atoms/Heading';
import Button from '@tekion/tekion-components/src/atoms/Button';
import Select from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/select';

import {
  ATTRIBUTE_OPTIONS_FOR_DEAL_POSTING_PACKS,
  PACK_SPECIFIC_ATTRIBUTE_OPTIONS_FOR_DEAL_POSTING_PACKS,
  SET_UP_KEYS,
  EMPTY_TARGET_PARAMS,
  TARGETING_OPTIONS,
  LOADING_MODELS,
  TARGETING_VALUES_OPTIONS_FOR_BODY_CLASS,
} from 'pages/salesSetup/salesSetup.constants';
import {
  MAKES,
  MODELS,
  MODEL_YEAR,
  TRIMS,
  STOCK_SUB_TYPE,
  SOLD_VEHICLE_SUB_TYPE,
  TRADE_IN_SUB_TYPE,
  SITE,
  VEHICLE_TYPE,
  VEHICLE_CATEGORY,
  VEHICLE_CATEGORY_OPTIONS,
  START_YEAR,
  END_YEAR,
  MANUFACTURE_MODEL_CODE,
  BODY_CLASS,
} from 'constants/constants';
import { getVehicleCategory } from 'pages/salesSetup/salesSetup.reader';
import {
  getLastRowId,
  getVehicleSubTypeOptionsByType,
  getMakeIdsFromMake,
  getDefaultModelOptions,
  getMakeDataFromVehicleCategory,
  filterSelectedModelBasedOnMake,
  filterSelectedMfrCodesBasedOnMake,
} from 'utils/dealSetup.utils';
import Targetting from 'organisms/target/targetTable';
import { TABLE_IDS } from 'organisms/target/targetTable/targeting.constants';
import { getOptionsForMake } from 'utils/vehicle.reader';
import { PropertyControlledComponent } from '@tekion/tekion-components/src/molecules';
import { isInchcapeOrRRG } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
import { STOCK_TYPE, VEHICLE_PREFIX } from '@tekion/tekion-base/marketScan/constants/constants';

import { shouldShowV2 } from '@tekion/tekion-business/src/helpers/sales/viFields';
import { getVehiclePrefixOptions } from '@tekion/tekion-base/marketScan/utils/desking.utils';
import {
  TRADEIN_1,
  COST_TYPES,
  TABLE_IDS as DEAL_POSTING_TABLE_IDS,
  USAGE_RULE_OPTIONS_INCHCAPE,
} from './dealPosting.constants';

import UsageRuleSelector from './UsageRuleSelector';

import { formatPackTypesToValues, formatValuePackTypes } from './dealPosting.utils';
import PostingPackTable from './PostingPackTable';
import { getSanitizedAttrOptions } from '../defaultValues.utils';
import {
  filterMfrCodesBasedOnMakes,
  filterMfrCodesBasedOnModels,
  filterModalOptionBasedOnMakes,
  getBodyClassOptions,
  getBodyClassOptionsV2,
  getMakesFromMfrCodes,
  getStockSubTypeValuesOnStockTypeChange,
  showInfoIconForStockSubType,
} from '../../../salesSetup.utils';

import { isCanadaDealer } from '../../../../../utils/dealerUtils';

const { DEAL_POSTING_PACKS } = SET_UP_KEYS;

export default class PostingPackBody extends PureComponent {
  constructor(props) {
    super(props);
    this.allFNIOptions = [];
    this.state = {
      postingPacks: props.postingPacks,
      targetingParams: props.targetingParams,
      loaders: {},
      defaultModelOptions: [],
      optionsForMake: [],
    };
    this.onAttributesValueSelection = {
      MAKE: this.onMakeSelection,
      STOCK_TYPE: this.onStockTypeSelection,
      MFR_MODEL_CODE: this.onMfrCodeSelectionSetMakeIds,
    };
  }

  componentDidMount() {
    const { targetingParams, packTypes, allModelOptions, makeData } = this.props;
    const packTypeValue = formatPackTypesToValues(packTypes);

    const defaultModelOptions = getDefaultModelOptions(targetingParams);

    if (_isEmpty(allModelOptions)) {
      this.setState({
        loaders: LOADING_MODELS.TRUE,
      });
    }
    const vehicleCategory = getVehicleCategory(targetingParams);
    const optionsForMake = getMakeDataFromVehicleCategory(vehicleCategory?.[0], makeData);
    if (!_isEmpty(optionsForMake)) {
      this.setState({ optionsForMake });
    }

    this.setState(prevState =>
      produce(prevState, draft => {
        if (!_isEmpty(defaultModelOptions)) {
          draft.defaultModelOptions = defaultModelOptions;
        }
        draft.postingPacks.push({
          id: getRandomId(),
          costType: COST_TYPES.FLAT_AMT,
        });

        if (_isEmpty(targetingParams)) {
          const emptyTarget = EMPTY_TARGET_PARAMS[DEAL_POSTING_PACKS];
          draft.targetingParams.push(emptyTarget());
        }
        draft.packTypeValue = packTypeValue;
      })
    );
  }

  componentDidUpdate() {
    const { allModelOptions } = this.props;
    const { loaders } = this.state;
    const isModalLoading = _get(loaders, MODELS) || true;
    if (!_isEmpty(allModelOptions) && isModalLoading) {
      this.setLoadingAction();
    }
  }

  setLoadingAction = () => this.setState({ loaders: LOADING_MODELS.FALSE });

  getAttributeOptions = packTypeValue => {
    const { getStockSubTypeParamWithInfoIcon } = this.props;
    const { targetingParams } = this.state;
    const extraAttributes = PACK_SPECIFIC_ATTRIBUTE_OPTIONS_FOR_DEAL_POSTING_PACKS(packTypeValue) || EMPTY_ARRAY;
    let options = [...ATTRIBUTE_OPTIONS_FOR_DEAL_POSTING_PACKS(), ...extraAttributes];
    const stockTypeAttribute = _find(targetingParams, param => param?.attribute === STOCK_TYPE);
    options = showInfoIconForStockSubType(stockTypeAttribute, options, getStockSubTypeParamWithInfoIcon);

    return getSanitizedAttrOptions(options, targetingParams);
  };

  onPackChange = (rowData, payload) => {
    const rowId = _get(rowData, 'id');
    const key = _get(payload, 'id');
    const value = _get(payload, 'value');

    this.setState(
      prevState =>
        produce(prevState, draft => {
          const matchedPack = findArrayItem(draft.postingPacks, 'id', rowId);
          if (matchedPack) {
            switch (key) {
              case DEAL_POSTING_TABLE_IDS.AMOUNT: {
                const costType = _get(matchedPack, [DEAL_POSTING_TABLE_IDS.COST_TYPE]);
                const isPercentageValue = costType && costType !== COST_TYPES.FLAT_AMT;
                let formattedValue = value;
                if (isPercentageValue && value > 100) {
                  formattedValue = 100;
                }
                _set(matchedPack, [key], formattedValue);
                break;
              }
              case DEAL_POSTING_TABLE_IDS.COST_TYPE: {
                _set(matchedPack, [key], value);
                _set(matchedPack, [DEAL_POSTING_TABLE_IDS.AMOUNT], 0);
                break;
              }
              default: {
                _set(matchedPack, [key], value);
              }
            }
          }
          const lastRowId = getLastRowId(draft.postingPacks);
          if (lastRowId === rowId) {
            draft.postingPacks.push({
              id: getRandomId(),
              costType: COST_TYPES.FLAT_AMT,
            });
          }
        }),
      this.setDealPostings
    );
  };

  setDealPostings = () => {
    const { DealSetupActions, postingId } = this.props;
    const { postingPacks } = this.state;

    DealSetupActions.changeDealPostingPacks({
      setupInfoType: DEAL_POSTING_PACKS,
      key: 'packs',
      postingId,
      data: _dropRight(postingPacks),
    });
  };

  deletePacks = rowData => {
    const { id } = rowData;
    const { postingPacks } = this.state;

    const lastRowId = getLastRowId(postingPacks);

    if (lastRowId === id) {
      return;
    }
    this.setState(
      prevState =>
        produce(prevState, draft => {
          const newPostingPacks = removeMatchedItems(draft.postingPacks, 'id', id);
          if (newPostingPacks) {
            draft.postingPacks = newPostingPacks;
          }
        }),
      this.setDealPostings
    );
  };

  addTargeting = () => {
    this.setState(prevState =>
      produce(prevState, draft => {
        const emptyTarget = EMPTY_TARGET_PARAMS[DEAL_POSTING_PACKS];
        draft.targetingParams.push(emptyTarget());
      })
    );
  };

  onMfrCodeSelectionSetMakeIds = (mfrCodes, rowData) => {
    const { makeData, allMfrCodesOptions } = this.props;
    this.setState(
      prevState =>
        produce(prevState, draft => {
          const matchedTarget = findArrayItem(draft.targetingParams, 'id', rowData?.id);
          if (matchedTarget) {
            const makes = getMakesFromMfrCodes(mfrCodes, allMfrCodesOptions);
            const makeIds = getMakeIdsFromMake(makeData, makes);
            _set(matchedTarget, 'makeIds', makeIds);
          }
        }),
      this.setTargetingParams
    );
  };

  onFNITargetChange = (rowData, payload) => {
    const rowId = _get(rowData, 'id');
    const key = _get(payload, 'id');
    const value = _get(payload, 'value');
    const rowDataAttribute = _get(rowData, 'attribute');
    const { makeData, CommonActions, allModelOptions, targetingParams, allMfrCodesOptions } = this.props;
    let applicableModelValuesInSelectedModels = [];
    let applicableMfrCodeValuesInSelectedMfrBox = [];

    if (rowDataAttribute === VEHICLE_CATEGORY && key === 'values') {
      const optionsForMake = getMakeDataFromVehicleCategory(value, makeData);
      this.setState({ optionsForMake });
    }

    if (rowDataAttribute === MAKES && key === 'values') {
      CommonActions.getVehicleModelFromMakeBasedOnVehicleCategory(value);
      if (isCanadaDealer()) {
        CommonActions.getVehicleMfrCodeFromMakeBasedOnVehicleCategory(value);
      }
      applicableModelValuesInSelectedModels = filterSelectedModelBasedOnMake(allModelOptions, targetingParams, value);
      applicableMfrCodeValuesInSelectedMfrBox = filterSelectedMfrCodesBasedOnMake(
        allMfrCodesOptions,
        targetingParams,
        applicableModelValuesInSelectedModels
      );
    }
    if (rowDataAttribute === MODELS && key === 'values') {
      applicableMfrCodeValuesInSelectedMfrBox = filterSelectedMfrCodesBasedOnMake(
        allMfrCodesOptions,
        targetingParams,
        value
      );
    }

    this.setState(
      prevState =>
        produce(prevState, draft => {
          const matchedTarget = findArrayItem(draft.targetingParams, 'id', rowId);
          const modelTargeting = findArrayItem(draft.targetingParams, 'attribute', MODELS);
          const makeTargeting = findArrayItem(draft.targetingParams, 'attribute', MAKES);
          const mfrCodeTargeting = findArrayItem(draft?.targetingParams, 'attribute', MANUFACTURE_MODEL_CODE);

          if (matchedTarget) {
            const splitKeys = key.split('.');
            if (key === 'values') {
              _set(matchedTarget, splitKeys, _castArray(value));
              const funcToExec = this.onAttributesValueSelection[matchedTarget.attribute];
              if (funcToExec) {
                funcToExec(_castArray(value), rowData);
              }
              if (rowDataAttribute === MAKES) {
                if (modelTargeting) {
                  _set(modelTargeting, 'values', applicableModelValuesInSelectedModels);
                }
                if (mfrCodeTargeting) {
                  _set(mfrCodeTargeting, 'values', applicableMfrCodeValuesInSelectedMfrBox);
                }
              }
              if (rowDataAttribute === MODELS) {
                if (mfrCodeTargeting) {
                  _set(mfrCodeTargeting, 'values', applicableMfrCodeValuesInSelectedMfrBox);
                }
              }
              if (rowDataAttribute === VEHICLE_CATEGORY) {
                if (makeTargeting) {
                  _set(makeTargeting, 'values', EMPTY_ARRAY);
                }
                if (modelTargeting) {
                  _set(modelTargeting, 'values', EMPTY_ARRAY);
                }
              }
            } else {
              _set(matchedTarget, splitKeys, value);
            }
          }
          if (key === TABLE_IDS.ATTRIBUTE) {
            if (value === STOCK_TYPE || rowDataAttribute === STOCK_TYPE) {
              const stockSubTypeTargeting = findArrayItem(draft.targetingParams, 'attribute', STOCK_SUB_TYPE);
              _set(stockSubTypeTargeting, ['values'], EMPTY_ARRAY);
            }
            _set(matchedTarget, ['values'], []);
            _set(matchedTarget, ['makeIds'], []);
          }
        }),
      this.setTargetingParams
    );
  };

  onMakeSelection = (makes, rowData) => {
    const { makeData } = this.props;
    this.setState(
      prevState =>
        produce(prevState, draft => {
          const matchedTarget = findArrayItem(draft.targetingParams, 'id', rowData?.id);
          if (matchedTarget) {
            const makeIds = getMakeIdsFromMake(makeData, makes);
            _set(matchedTarget, 'makeIds', makeIds);
          }
        }),
      this.setTargetingParams
    );
  };

  onStockTypeSelection = (stockTypes, rowData) => {
    const { stockSubTypes } = this.props;

    this.setState(
      prevState =>
        produce(prevState, draft => {
          const matchedTarget = findArrayItem(draft.targetingParams, 'id', rowData?.id);
          if (matchedTarget?.attribute === STOCK_TYPE) {
            const stockSubTypeValues = getStockSubTypeValuesOnStockTypeChange({
              selectedStockTypeValues: stockTypes,
              stockSubTypesData: stockSubTypes,
              targetingParams: draft.targetingParams,
            });
            const stockSubTypeTargeting = findArrayItem(draft.targetingParams, 'attribute', STOCK_SUB_TYPE);
            _set(stockSubTypeTargeting, ['values'], stockSubTypeValues);
          }
        }),
      this.setTargetingParams
    );
  };

  setTargetingParams = () => {
    const { DealSetupActions, postingId } = this.props;
    const { targetingParams } = this.state;

    DealSetupActions.changeDealPostingPacks({
      setupInfoType: DEAL_POSTING_PACKS,
      key: 'targetingParams',
      postingId,
      data: targetingParams,
    });
  };

  deleteTarget = rowData => {
    const { id, attribute } = rowData;
    const { makeData } = this.props;
    if (!_isEmpty(id) && attribute === VEHICLE_CATEGORY) {
      const optionsForMake = getOptionsForMake(makeData);
      this.setState({ optionsForMake });
    }
    this.setState(
      prevState =>
        produce(prevState, draft => {
          const stockTypeTargeting = findArrayItem(draft.targetingParams, 'attribute', STOCK_TYPE);
          if (stockTypeTargeting?.id === id) {
            const stockSubTypeTargeting = findArrayItem(draft.targetingParams, 'attribute', STOCK_SUB_TYPE);
            _set(stockSubTypeTargeting, ['values'], EMPTY_ARRAY);
          }
          const newTargetParams = removeMatchedItems(draft.targetingParams, 'id', id);
          draft.targetingParams = newTargetParams;
        }),
      this.setTargetingParams
    );
  };

  onSelectPostingPackType = ({ payload }) => {
    const { value } = payload;

    this.setState(
      {
        packTypeValue: value,
      },
      this.savePackType
    );
  };

  savePackType = () => {
    const { fniProducts, dueBillsList, postingId, DealSetupActions } = this.props;
    const { packTypeValue } = this.state;
    const packTypes = formatValuePackTypes(packTypeValue, fniProducts, dueBillsList);
    if (packTypeValue !== TRADEIN_1) {
      this.setState(
        prevState =>
          produce(prevState, draft => {
            const { postingPacks } = draft;
            postingPacks.forEach((postingPack, index) => {
              if (index === postingPacks.length - 1) return;
              _set(postingPack, 'costType', COST_TYPES.FLAT_AMT);
            });
          }),
        this.setDealPostings
      );
    }

    DealSetupActions.changeDealPostingPacks({
      setupInfoType: DEAL_POSTING_PACKS,
      key: 'packTypes',
      postingId,
      data: packTypes,
    });
  };

  getOptions = values => _map(values, value => ({ label: value, value: _toString(value) }));

  getYearValuesOptions = () => {
    const years = _range(START_YEAR, END_YEAR);
    return this.getOptions(years);
  };

  getTargetingOptions = attribute => {
    const {
      dealTypes,
      oemSiteOptions,
      paymentOptionConfigs,
      targetingParams,
      stockSubTypes,
      allModelOptions,
      allMfrCodesOptions,
      viV2SetupResponse,
      prefixResponse,
    } = this.props;
    let mfrCodesOptions;
    const getModalTargetingParam = _head(
      _filter(targetingParams, targetingItem => targetingItem?.attribute === MODELS)
    );
    const selectedModels = getModalTargetingParam?.values;

    const { defaultModelOptions, optionsForMake } = this.state;
    if (attribute === MAKES) {
      return optionsForMake;
    }
    if (attribute === TRIMS) {
      const { trimOptions } = this.props;
      return trimOptions;
    }

    if (attribute === MODELS) {
      if (_isEmpty(allModelOptions)) {
        return defaultModelOptions;
      }
      return filterModalOptionBasedOnMakes(allModelOptions, targetingParams);
    }

    if (attribute === MANUFACTURE_MODEL_CODE) {
      const mfrCodesBasedOnMakes = filterMfrCodesBasedOnMakes(allMfrCodesOptions, targetingParams);
      mfrCodesOptions = mfrCodesBasedOnMakes;
      if (!_isNil(getModalTargetingParam) && _size(selectedModels) > 0) {
        mfrCodesOptions = filterMfrCodesBasedOnModels(mfrCodesBasedOnMakes, targetingParams);
      }
      return mfrCodesOptions;
    }

    if (attribute === TRADE_IN_SUB_TYPE) {
      const { vehicleTypeOptions } = this.props;
      return vehicleTypeOptions;
    }
    if (attribute === SOLD_VEHICLE_SUB_TYPE) {
      const { stockSubTypes } = this.props;
      return getVehicleSubTypeOptionsByType(stockSubTypes, VEHICLE_TYPE.USED);
    }
    if (attribute === SITE) {
      return oemSiteOptions;
    }
    if (attribute === MODEL_YEAR) {
      return this.getYearValuesOptions();
    }
    if (attribute === VEHICLE_CATEGORY) return VEHICLE_CATEGORY_OPTIONS;

    if (attribute === BODY_CLASS) {
      if (shouldShowV2()) {
        const currentOptions = getBodyClassOptions(targetingParams);
        return getBodyClassOptionsV2(viV2SetupResponse, currentOptions);
      }
      return TARGETING_VALUES_OPTIONS_FOR_BODY_CLASS;
    }

    if (attribute === VEHICLE_PREFIX) {
      return getVehiclePrefixOptions(prefixResponse);
    }

    return TARGETING_OPTIONS(dealTypes, paymentOptionConfigs, targetingParams, stockSubTypes)[attribute] || EMPTY_ARRAY;
  };

  render() {
    const { glAccountOptions, postingOptions, onAction, fieldOptions, descriptionModalData, dealPostingPacks } =
      this.props;
    const { postingPacks, targetingParams, packTypeValue, loaders } = this.state;
    return (
      <React.Fragment>
        <Heading size={4}>{__('Pack Type')}</Heading>
        <div className="marginT16" />
        <PropertyControlledComponent controllerProperty={!isInchcapeOrRRG()}>
          <UsageRuleSelector
            value={packTypeValue}
            usageRuleOptions={postingOptions}
            onAction={this.onSelectPostingPackType}
            id="postingOption"
          />
        </PropertyControlledComponent>
        <PropertyControlledComponent controllerProperty={isInchcapeOrRRG()}>
          <Select
            value={packTypeValue}
            options={USAGE_RULE_OPTIONS_INCHCAPE}
            onAction={this.onSelectPostingPackType}
            id="postingOption"
            style={{ width: 130 }}
          />
        </PropertyControlledComponent>
        <div className="marginT16" />

        <PostingPackTable
          onPackChange={this.onPackChange}
          onDeleteFNICode={this.onDeleteFNICode}
          postingTableData={postingPacks}
          deletePacks={this.deletePacks}
          glAccountOptions={glAccountOptions}
          packTypeValue={packTypeValue}
          fieldOptions={fieldOptions}
          descriptionModalData={descriptionModalData}
          onAction={onAction}
          dealPostingPacks={dealPostingPacks}
        />
        <div className="marginT16" />
        <Heading size={4}>{__('Targeting')}</Heading>
        <div className="marginT16" />

        <Targetting
          targetingParams={targetingParams}
          deleteTarget={this.deleteTarget}
          fniTargetChange={this.onFNITargetChange}
          attributeOptions={this.getAttributeOptions(packTypeValue)}
          deleteActionRequired
          hasColumnsForRange
          isApplicationCriteria
          getTargetingOptions={this.getTargetingOptions}
          loaders={loaders}
        />

        <Button view={Button.VIEW.TERTIARY} onClick={this.addTargeting}>
          {__('Add Targeting')}
        </Button>
      </React.Fragment>
    );
  }
}

PostingPackBody.propTypes = {
  targetingParams: PropTypes.array,
  glAccountOptions: PropTypes.array,
  DealSetupActions: PropTypes.object,
  postingId: PropTypes.string,
  postingPacks: PropTypes.array,
  fniProducts: PropTypes.array,
  dueBillsList: PropTypes.array,
  postingOptions: PropTypes.array,
  packTypes: PropTypes.object,
  vehicleTypeOptions: PropTypes.array.isRequired,
  oemSiteOptions: PropTypes.array.isRequired,
  dealTypes: PropTypes.array,
  stockSubTypes: PropTypes.array,
  makeData: PropTypes.array.isRequired,
  descriptionModalData: PropTypes.object,
  fieldOptions: PropTypes.object,
  onAction: PropTypes.func,
  dealPostingPacks: PropTypes.array,
};

PostingPackBody.defaultProps = {
  targetingParams: EMPTY_ARRAY,
  glAccountOptions: EMPTY_ARRAY,
  DealSetupActions: EMPTY_OBJECT,
  postingId: '',
  postingPacks: EMPTY_ARRAY,
  dueBillsList: EMPTY_ARRAY,
  fniProducts: EMPTY_ARRAY,
  postingOptions: EMPTY_ARRAY,
  packTypes: EMPTY_OBJECT,
  dealTypes: EMPTY_ARRAY,
  stockSubTypes: EMPTY_ARRAY,
  descriptionModalData: EMPTY_OBJECT,
  fieldOptions: EMPTY_OBJECT,
  onAction: _noop,
  dealPostingPacks: EMPTY_ARRAY,
};
