import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { defaultMemoize } from 'reselect';
import _get from 'lodash/get';
import Button from '@tekion/tekion-components/src/atoms/Button';
import Header from 'organisms/updateModal/UpdateModalHeader';
import Collapse from '@tekion/tekion-components/src/molecules/Collapse';
import withActions from '@tekion/tekion-components/src/connectors/withActions';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';

import { getConfigName, getConfigLang, getTargetingParams } from 'pages/salesSetup/salesSetup.reader';
import { SET_UP_KEYS } from 'pages/salesSetup/salesSetup.constants';
import { isUsOrCanadaDealer } from 'utils/dealerUtils';

import { EMPTY_OBJECT, EMPTY_ARRAY, EMPTY_STRING } from '@tekion/tekion-base/app.constants';

import CollapseHeader from 'molecules/collapseHeader';

import PostingPackBody from './PostingPackBody';
import { formatGLaccountToSelectOptions, getUsageRuleOptions } from './dealPosting.utils';

import styles from '../defaultValues.module.scss';
import { INITIAL_STATE } from './dealPosting.constants';
import { ACTION_HANDLERS } from './dealPosting.actionaHandler';
import { ACTION_TYPES } from './dealPosting.actionTypes';

const { DEAL_POSTING_PACKS } = SET_UP_KEYS;

class DealPosting extends PureComponent {
  state = {
    glAccountOptions: [],
    postingOptions: [],
    headerInEditMode: false,
    fniProducts: [],
  };

  componentDidMount() {
    this.init();
  }

  getRightActions = defaultMemoize(() => {
    const addPack = <Button onClick={this.addPacks}>{__('Add')}</Button>;
    return [addPack];
  });

  init = async () => {
    const { DealSetupActions, onAction } = this.props;

    if (isUsOrCanadaDealer()) {
      await onAction({
        type: ACTION_TYPES.FETCH_FORMULAS,
      });
    }

    const { response } = await DealSetupActions.getGLAccountList();
    if (response) {
      this.setState({
        glAccountOptions: formatGLaccountToSelectOptions(response),
      });
    }

    await DealSetupActions.getDueBills();
    const fniProducts = await DealSetupActions.getFNIProducts();
    const { dueBillsList } = this.props;
    const postingOptions = getUsageRuleOptions(fniProducts, dueBillsList);
    const prefixResponse = await this.fetchPrefixOptions();
    this.setState({
      postingOptions,
      fniProducts,
      prefixResponse,
    });
  };

  fetchPrefixOptions = async () => {
    const { DealSetupActions } = this.props;
    if (!DealerPropertyHelper.isAccountingInterPrefixEnabled()) return EMPTY_OBJECT;
    const prefixResponse = await DealSetupActions.getVehiclePrefixOptions();
    return prefixResponse;
  };

  addPacks = () => {
    const { DealSetupActions } = this.props;
    DealSetupActions.addNewConfig({
      setupInfoType: DEAL_POSTING_PACKS,
    });
  };

  onDuplicatePacks = rowId => {
    const { dealPostingPacks, DealSetupActions } = this.props;

    const data = dealPostingPacks.find(postingPack => postingPack.id === rowId);
    DealSetupActions.addNewConfig({
      data,
      setupInfoType: DEAL_POSTING_PACKS,
    });
  };

  onHeaderDelete = rowId => {
    const { DealSetupActions } = this.props;
    DealSetupActions.removeConfig({
      rowId,
      setupInfoType: DEAL_POSTING_PACKS,
    });
  };

  onNameChange = (rowId, changedData) => {
    const { DealSetupActions } = this.props;
    const { id, value } = changedData;

    DealSetupActions.changeConfigName({
      rowId,
      configName: value,
      key: id,
      setupInfoType: DEAL_POSTING_PACKS,
    });
  };

  onHeaderEdit = () => {
    this.setState(prevState => ({
      headerInEditMode: !prevState.headerInEditMode,
    }));
  };

  getPanels = dealPostingPacks => {
    const { glAccountOptions, postingOptions, fniProducts, prefixResponse } = this.state;
    const {
      dueBillsList,
      DealSetupActions,
      dealTypes,
      makeOptions,
      allModelOptions,
      allMfrCodesOptions,
      trimOptions,
      vehicleTypeOptions,
      stockSubTypes,
      oemSiteOptions,
      makeData,
      paymentOptionConfigs,
      CommonActions,
      onAction,
      descriptionModalData,
      fieldOptions,
      getStockSubTypeParamWithInfoIcon,
      viV2SetupResponse,
    } = this.props;
    return dealPostingPacks.map(postingPack => {
      const { id } = postingPack;
      const configName = getConfigName(postingPack);
      const confLanguages = getConfigLang(postingPack);
      const targetingParams = getTargetingParams(postingPack);
      const packs = _get(postingPack, 'packs') || EMPTY_ARRAY;
      const packTypes = _get(postingPack, 'packTypes') || EMPTY_STRING;
      return {
        header: (
          <CollapseHeader
            title={configName}
            titleLanguages={confLanguages}
            rowId={id}
            onDelete={this.onHeaderDelete}
            onNameChange={this.onNameChange}
            key={id}
            // extraRightActions={this.extraRightActions()}
            onHeaderEdit={this.onHeaderEdit}
            onDuplicatePacks={this.onDuplicatePacks}
            showDuplicate
          />
        ),
        key: id,
        body: (
          <PostingPackBody
            paymentOptionConfigs={paymentOptionConfigs}
            glAccountOptions={glAccountOptions}
            targetingParams={targetingParams}
            postingPacks={packs}
            postingId={id}
            DealSetupActions={DealSetupActions}
            postingOptions={postingOptions}
            packTypes={packTypes}
            fniProducts={fniProducts}
            dueBillsList={dueBillsList}
            dealTypes={dealTypes}
            makeOptions={makeOptions}
            trimOptions={trimOptions}
            allModelOptions={allModelOptions}
            allMfrCodesOptions={allMfrCodesOptions}
            vehicleTypeOptions={vehicleTypeOptions}
            stockSubTypes={stockSubTypes}
            oemSiteOptions={oemSiteOptions}
            makeData={makeData}
            CommonActions={CommonActions}
            fieldOptions={fieldOptions}
            descriptionModalData={descriptionModalData}
            onAction={onAction}
            dealPostingPacks={dealPostingPacks}
            getStockSubTypeParamWithInfoIcon={getStockSubTypeParamWithInfoIcon}
            viV2SetupResponse={viV2SetupResponse}
            prefixResponse={prefixResponse}
          />
        ),
      };
    });
  };

  render() {
    const { dealPostingPacks } = this.props;
    return (
      <div>
        <Header
          title={__('Deal Posting Packs')}
          rightActions={this.getRightActions()}
          containerClassName={styles.configHeader}
          id="POSTING_SETUP"
        />
        <Collapse panels={this.getPanels(dealPostingPacks)} />
      </div>
    );
  }
}

DealPosting.propTypes = {
  dealPostingPacks: PropTypes.array,
  DealSetupActions: PropTypes.object,
  dealTypes: PropTypes.array,
  dueBillsList: PropTypes.array,
  makeOptions: PropTypes.array.isRequired,
  vehicleTypeOptions: PropTypes.array,
  stockSubTypes: PropTypes.array,
  oemSiteOptions: PropTypes.array.isRequired,
  makeData: PropTypes.array.isRequired,
};

DealPosting.defaultProps = {
  DealSetupActions: EMPTY_OBJECT,
  dealPostingPacks: EMPTY_ARRAY,
  dealTypes: EMPTY_ARRAY,
  dueBillsList: EMPTY_ARRAY,
  stockSubTypes: EMPTY_ARRAY,
  vehicleTypeOptions: EMPTY_ARRAY,
};

export default withActions(INITIAL_STATE, ACTION_HANDLERS)(DealPosting);
