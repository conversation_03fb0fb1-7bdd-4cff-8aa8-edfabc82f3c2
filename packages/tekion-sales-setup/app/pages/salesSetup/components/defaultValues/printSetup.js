import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import _noop from 'lodash/noop';

import Heading from '@tekion/tekion-components/src/atoms/Heading';
import NumberInput from '@tekion/tekion-widgets/src/fieldRenderers/numberInputField';
import actionTypes from '@tekion/tekion-components/src/organisms/FormBuilder/constants/actionTypes';
import Checkbox from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/checkbox';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { SET_UP_KEYS, checkIsFieldIsApplicable } from 'pages/salesSetup/salesSetup.constants';

import { getsheetConfigs } from './defaultValues.reader';
import styles from './defaultValues.module.scss';

const {
  DEAL_SHEET_COUNT,
  RECAP_SHEET_COUNT,
  COVER_SHEET_COUNT,
  DOUBLE_SIDED_PRINT,
  PURCHASE_ORDER_FORM_COUNT,
  SALES_ORDER_FORM_COUNT,
} = SET_UP_KEYS;

export default class PrintSetup extends PureComponent {
  onSheetConfigValueChange = ({ type, payload }) => {
    if (type === actionTypes.ON_FIELD_CHANGE) {
      const { onSheetConfigValueChange } = this.props;
      onSheetConfigValueChange(payload);
    }
  };

  render() {
    const { sheetConfigs } = this.props;
    const {
      dealSheetCount,
      recapSheetCount,
      coverSheetCount,
      doubleSidedPrint,
      purchaseOrderFormCount,
      salesOrderFormCount,
    } = getsheetConfigs(sheetConfigs);
    return (
      <React.Fragment>
        <Heading size={3}>{__('Print Setup')}</Heading>

        <div className={classNames('d-flex justify-content-between marginT16', styles.dealValues)}>
          <PropertyControlledComponent controllerProperty={checkIsFieldIsApplicable(DEAL_SHEET_COUNT)}>
            <NumberInput
              label={__('Deal Sheet Copies')}
              value={dealSheetCount}
              onAction={this.onSheetConfigValueChange}
              id={DEAL_SHEET_COUNT}
              className={styles.dealValueInput}
            />
          </PropertyControlledComponent>
          <PropertyControlledComponent controllerProperty={checkIsFieldIsApplicable(RECAP_SHEET_COUNT)}>
            <NumberInput
              label={__('Recap Sheet Copies')}
              value={recapSheetCount}
              onAction={this.onSheetConfigValueChange}
              id={RECAP_SHEET_COUNT}
              className={styles.dealValueInput}
            />
          </PropertyControlledComponent>
        </div>

        <div className={classNames('d-flex justify-content-between marginT16', styles.dealValues)}>
          <PropertyControlledComponent controllerProperty={checkIsFieldIsApplicable(COVER_SHEET_COUNT)}>
            <NumberInput
              label={__('Cover Sheet Copies')}
              value={coverSheetCount}
              onAction={this.onSheetConfigValueChange}
              id={COVER_SHEET_COUNT}
              className={styles.dealValueInput}
            />
          </PropertyControlledComponent>
        </div>
        <div className={classNames('d-flex justify-content-between marginT16', styles.dealValues)}>
          <PropertyControlledComponent controllerProperty={checkIsFieldIsApplicable(PURCHASE_ORDER_FORM_COUNT)}>
            <NumberInput
              label={__('Purchase Order Form Copies')}
              value={purchaseOrderFormCount}
              onAction={this.onSheetConfigValueChange}
              id={PURCHASE_ORDER_FORM_COUNT}
              className={styles.dealValueInput}
            />
          </PropertyControlledComponent>

          <PropertyControlledComponent controllerProperty={checkIsFieldIsApplicable(SALES_ORDER_FORM_COUNT)}>
            <NumberInput
              label={__('Sales Order Form Copies')}
              value={salesOrderFormCount}
              onAction={this.onSheetConfigValueChange}
              id={SALES_ORDER_FORM_COUNT}
              className={styles.dealValueInput}
            />
          </PropertyControlledComponent>
        </div>
        <div className={classNames('d-flex justify-content-between m-t-16', styles.dealValues)}>
          <PropertyControlledComponent controllerProperty={checkIsFieldIsApplicable(DOUBLE_SIDED_PRINT)}>
            <Checkbox
              value={doubleSidedPrint}
              id={DOUBLE_SIDED_PRINT}
              checkboxLabel={__('Double Sided Print')}
              onAction={this.onSheetConfigValueChange}
            />
          </PropertyControlledComponent>
        </div>
      </React.Fragment>
    );
  }
}

PrintSetup.propTypes = {
  sheetConfigs: PropTypes.object,
  onSheetConfigValueChange: PropTypes.func,
};

PrintSetup.defaultProps = {
  sheetConfigs: EMPTY_OBJECT,
  onSheetConfigValueChange: _noop,
};
