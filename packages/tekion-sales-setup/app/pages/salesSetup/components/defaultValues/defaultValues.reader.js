import _get from 'lodash/get';
import _find from 'lodash/find';
import _isEmpty from 'lodash/isEmpty';
import _filter from 'lodash/filter';
import {
  SET_UP_KEYS,
  DELIVERY_FEE_TYPES,
  DELIVERY_FEE_CONSTANTS,
  LOAN_BUY_RATE_TYPE as loanBuyRateTypes,
} from 'pages/salesSetup/salesSetup.constants';
import { EMPTY_STRING, EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

const {
  LEASE_DAYS_TO_FIRST_PAYMENT,
  LOAN_DAYS_TO_FIRST_PAYMENT,
  ANNUAL_MILES,
  ZIP_CODE,
  CREDIT_SCORE,
  ACCESSORIES,
  FNI,
  REBATE,
  DEAL_SHEET_COUNT,
  RECAP_SHEET_COUNT,
  COVER_SHEET_COUNT,
  DOUBLE_SIDED_PRINT,
  LOAN_BUY_RATE_FOR_NEW,
  LOAN_BUY_RATE_FOR_USED,
  LOAN_BUY_RATE_TYPE,
  DAYS_SELECTED_FOR_AVERAGE_RATE,
  DAYS_SELECTED_FOR_AVERAGE_RATE_USED,
  DEFAULT_NEW_SELLING_PRICE,
  DEFAULT_COUNTRY_DIALING_CODE,
  DEALER_MONTH_START_DAY,
  DEPOSIT_DISABLED_VEHICLE_STATUSES,
  LOAN_FREQUENCY,
  LEASE_FREQUENCY,
  DAYS_TO_FIRST_PAYMENT,
  DEFAULT_COUNTY,
  PURCHASE_ORDER_FORM_COUNT,
  SALES_ORDER_FORM_COUNT,
  DEFAULT_RESERVATION_AMOUNT,
  CUSTOMER_CASH,
  DEFAULT_PARTEX_VAT_INFO,
  DEFAULT_DEPOSIT_AMOUNT_PRODUCT_CLASSIFICATION,
  DEFAULT_TRADE_IN_PRODUCT_CLASSIFICATION,
  DEFAULT_OLD_SELLING_PRICE,
  DEFAULT_DEMO_SELLING_PRICE,
  DEFAULT_DEPOSIT_ACCOUNTING_GROUP,
  DEFAULT_DEPOSIT_VAT_INFO,
  DISABLE_DELIVERY_APPOINTMENT_STATUS,
  QUOTE_EXPIRY_DURATION,
  FINANCE_COMMISSION_GROSS_TYPE,
  VEHICLE_DEAL_HEADER_IDENTIFIER,
  DISABLE_TRADE_IN,
} = SET_UP_KEYS;

export const getDealConfigs = dealConfigs => ({
  loanDaysToFirstPayment: _get(dealConfigs, LOAN_DAYS_TO_FIRST_PAYMENT) || EMPTY_STRING,
  leaseDaysToFirstPayment: _get(dealConfigs, LEASE_DAYS_TO_FIRST_PAYMENT) || EMPTY_STRING,
  annualMiles: _get(dealConfigs, ANNUAL_MILES) || EMPTY_STRING,
  loanBuyRateForNew: _get(dealConfigs, LOAN_BUY_RATE_FOR_NEW),
  loanBuyRateType: _get(dealConfigs, LOAN_BUY_RATE_TYPE) || loanBuyRateTypes.DEFAULT,
  daysSelectedForAverageRate: _get(dealConfigs, DAYS_SELECTED_FOR_AVERAGE_RATE) || 30,
  daysSelectedForAverageRateUsed: _get(dealConfigs, DAYS_SELECTED_FOR_AVERAGE_RATE_USED) || 30,
  loanBuyRateForUsed: _get(dealConfigs, LOAN_BUY_RATE_FOR_USED),
  defaultNewSellingPrice: _get(dealConfigs, DEFAULT_NEW_SELLING_PRICE),
  defaultOldSellingPrice: _get(dealConfigs, DEFAULT_OLD_SELLING_PRICE),
  defaultDemoSellingPrice: _get(dealConfigs, DEFAULT_DEMO_SELLING_PRICE),
  countryDialingCode: _get(dealConfigs, DEFAULT_COUNTRY_DIALING_CODE),
  dealerMonthStartDay: _get(dealConfigs, DEALER_MONTH_START_DAY),
  depositDisabledVehicleStatus: _get(dealConfigs, DEPOSIT_DISABLED_VEHICLE_STATUSES),
  defaultReservationAmount: _get(dealConfigs, DEFAULT_RESERVATION_AMOUNT) || EMPTY_STRING,
  defaultPartExVatInfo: _get(dealConfigs, [DEFAULT_PARTEX_VAT_INFO, 'taxId']) || EMPTY_STRING,
  defaultDepositVatInfo: _get(dealConfigs, [DEFAULT_DEPOSIT_VAT_INFO, 'taxId']) || EMPTY_STRING,
  customerCash: _get(dealConfigs, CUSTOMER_CASH) || EMPTY_STRING,
  defaultDepositProductClassification: _get(dealConfigs, DEFAULT_DEPOSIT_AMOUNT_PRODUCT_CLASSIFICATION) || EMPTY_STRING,
  defaultTradeinProductClassification: _get(dealConfigs, DEFAULT_TRADE_IN_PRODUCT_CLASSIFICATION) || EMPTY_STRING,
  defaultDepositAccountingGroup: _get(dealConfigs, DEFAULT_DEPOSIT_ACCOUNTING_GROUP) || EMPTY_STRING,
  disableDeliveryAppointmentStatus: _get(dealConfigs, DISABLE_DELIVERY_APPOINTMENT_STATUS) || EMPTY_STRING,
  quoteExpiryDuration: _get(dealConfigs, QUOTE_EXPIRY_DURATION) || EMPTY_STRING,
  financeCommissionGrossType: _get(dealConfigs, FINANCE_COMMISSION_GROSS_TYPE) || EMPTY_STRING,
  vehicleDealHeaderIdentifier: _get(dealConfigs, VEHICLE_DEAL_HEADER_IDENTIFIER) || EMPTY_STRING,
  disableTradeIn: _get(dealConfigs, DISABLE_TRADE_IN) || EMPTY_STRING,
});

export const getDealPaymentFreqConfig = config => ({
  loanFrequency: _get(config, LOAN_FREQUENCY) || EMPTY_STRING,
  leaseFrequency: _get(config, LEASE_FREQUENCY) || EMPTY_STRING,
});

export const getDaysToFirstPaymentConfig = config => _get(config, DAYS_TO_FIRST_PAYMENT) || EMPTY_ARRAY;

export const getCustomerConfigs = customerConfig => ({
  zipCode: _get(customerConfig, ZIP_CODE) || EMPTY_STRING,
  creditScore: _get(customerConfig, CREDIT_SCORE) || EMPTY_STRING,
  defaultCounty: _get(customerConfig, DEFAULT_COUNTY) || EMPTY_OBJECT,
});

export const getWebsiteURLs = urls => ({
  accessories: _get(urls, ACCESSORIES) || EMPTY_STRING,
  fnI: _get(urls, FNI) || EMPTY_STRING,
  rebate: _get(urls, REBATE) || EMPTY_STRING,
});

export const getsheetConfigs = sheetConfigs => ({
  dealSheetCount: _get(sheetConfigs, DEAL_SHEET_COUNT) || EMPTY_STRING,
  recapSheetCount: _get(sheetConfigs, RECAP_SHEET_COUNT) || EMPTY_STRING,
  coverSheetCount: _get(sheetConfigs, COVER_SHEET_COUNT) || EMPTY_STRING,
  doubleSidedPrint: _get(sheetConfigs, DOUBLE_SIDED_PRINT, true),
  purchaseOrderFormCount: _get(sheetConfigs, PURCHASE_ORDER_FORM_COUNT) || EMPTY_STRING,
  salesOrderFormCount: _get(sheetConfigs, SALES_ORDER_FORM_COUNT) || EMPTY_STRING,
});

export const getFeeTypesOptions = feeSearchResults => {
  const pricingTypeOptions = _find(feeSearchResults, item => {
    const detailEntry = _get(item, 'data.detailEntries[0]', EMPTY_ARRAY);
    return (
      detailEntry &&
      detailEntry?.pricingType === DELIVERY_FEE_CONSTANTS.CALCULATED_PRICE &&
      detailEntry?.feeStatus === 'ACTIVE'
    );
  });

  if (_isEmpty(pricingTypeOptions)) {
    return _filter(DELIVERY_FEE_TYPES, item => item?.value !== DELIVERY_FEE_CONSTANTS.CALCULATED_PRICE);
  }

  return DELIVERY_FEE_TYPES;
};
