import _reduce from 'lodash/reduce';
import _includes from 'lodash/includes';
import _map from 'lodash/map';
import _invert from 'lodash/invert';

import { getYearOptions, getDealTypesOptions } from '@tekion/tekion-base/utils/sales';
import { isInchcape, isInchcapeOrRRG, isRRG } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
import { MARKET_CLASS_CATEGORY_OPTIONS } from '@tekion/tekion-base/constants/vehicleInventory/trim';
import { DEAL_STATUS } from '@tekion/tekion-base/constants/deal/status';
import {
  UNLOCK_PIN_FOR_BOOKED_DEALS,
  UNLOCK_PIN_FOR_CLOSED_DEALS,
  DEAL_UNLOCK_PASSCODE_FOR_CLOSED_MONTHS_FOR_BOOKED_DEALS,
  DEAL_UNLOCK_PASSCODE_FOR_CLOSED_MONTHS_FOR_CLOSED_DEALS,
  DEAL_UNWIND_PASSCODE_FOR_BOOKED_DEALS,
  DEAL_UNWIND_PASSCODE_FOR_CLOSED_DEALS,
  DRS_FNI_MENU_DEAL_SWITCH_PASSCODE,
} from '@tekion/tekion-base/constants/deal/passCodes';
import { DEAL_STATUS_CONFIGS } from '@tekion/tekion-base/constants/retail/dealStatusConfigs.constants';
import { REBATES_FIELDS } from '@tekion/tekion-base/constants/deal/rebates';
import { PAYMENT_TYPES } from '@tekion/tekion-base/constants/deal/paymentType';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';
import { LOAN, LEASE, GROSS_TYPES } from 'constants/desking.constants';
import {
  canEditPasscodeForUnlockDeal,
  canEditPasscodeForUnbookDeal,
  canEditPasscodeForUnwindDeal,
} from 'permissions/desking.permissions';

import {
  TARGETING_OPTIONS_FOR_STATES,
  TARGETING_VALUES_OPTIONS_FOR_STOCK_TYPE,
  TARGETING_VALUES_OPTIONS_FOR_STOCK_TYPE_RRG,
} from '@tekion/tekion-base/constants/deal/targeting';
import { EMPTY_ARRAY, EMPTY_STRING } from '@tekion/tekion-base/app.constants';

import {
  VEHICLE_CATEGORIES,
  DEMO_MILEAGE_ADJUSTMENT_TYPES,
  TRADE_IN_ACV_RANGE,
  SELLING_PRICE_RANGE,
  INVOICE_PRICE_RANGE,
  VEHICLE_CERTIFIED,
  OEM_CERTIFIED,
  DEALER_CERTIFIED,
  PAYMENT_TYPE,
  STOCK_TYPE,
  INCLUDE_OPERATOR,
  DEAL_TYPE,
  F_N_I_PRODUCT_SELLING_PRICE_RANGE,
  F_N_I_PRODUCT_COST_PRICE_RANGE,
  DUE_BILL_COST_PRICE_RANGE,
  DUE_BILL_SELLING_PRICE_RANGE,
  MAKES,
  MANUFACTURE_MODEL_CODE,
  MODELS,
  TRIMS,
  TRADE_INS,
  IN_STATE,
  OUT_STATE,
  BODY_CLASS,
  RETAIL,
  INTERNET_PRICE,
  MSRP,
  DEFAULT_NEW_SELLING_PRICE,
  TRADE_IN_OWNERSIP_TYPE,
  STATES,
  NO_SPLIT,
  EQUAL_SPLIT,
  ONLY_FOR_PRIMARY_SALESPERSON,
  LENDERS,
  VEHICLE_STATUS,
  CALCULATION_PARAMETERS,
  AUTO_ROLL_DEFICIT_BEARER,
  LICENSE_EXPIRATION_DATE_IN_DAYS,
  MODEL_YEAR,
  FUEL_TYPE,
  TRADE_IN_YEAR,
  TRADE_IN_MILEAGE_RANGE,
  COLLECT_ONLY_WHEN_COLLECTFEE_IS_ON,
  VEHICLE_TYPE,
  STOCK_SUB_TYPE,
  TRADE_IN_SUB_TYPE,
  // SOLD_VEHICLE_SUB_TYPE,
  SITE,
  ONLY_FOR_PRIMARY_ASSIGNEE,
  VEHICLE_MILEAGE,
  MODEL_YEAR_DIFFERENCE_LESSER_THAN,
  AGE,
  LICENSE_EXPIRATION_DAYS_GREATER_THAN,
  COUNTY,
  CITY,
  LANGUAGE,
  APPLICANT_TYPE,
  VEHICLE_CATEGORY,
  PART_EXCHANGE,
  REGISTRATION_EXPIRATION_GREATER_THAN,
  REGISTRATION_EXPIRATION_LESSER_THAN,
  CERTIFICATION_STATUS,
  VEHICLE_PREFIX,
} from '@tekion/tekion-base/marketScan/constants/constants';
import {
  DISPLAY_MODEL,
  BEST_STYLE_NAME,
  INTEGRATION_KEYS,
} from '@tekion/tekion-base/constants/retail/salesSetup.constants';
import { getCurrentYear } from '@tekion/tekion-base/utils/dateUtils';
import { isCanadaDealer, isUSDealer } from 'utils/dealerUtils';

import { defaultMemoize } from 'reselect';
import { tget } from '@tekion/tekion-base/utils/general';
import { isArcLiteProgram } from 'utils/program.utils';
import { getTargettingValuesForTradeIns, getStockSubTypeTargetingOptions } from './salesSetup.utils';
import { getRandomId } from '../../utils';

import {
  PARAMETERS as VENDOR_ATTRIBUTES,
  TAX_TYPE_VALUES,
} from './components/defaultValues/vendorManagement/vendorManagement.config';

export const HEADER_HEIGHT = 64;

const FNNI_CONFIGS = 'fnIConfigs';
const FEE_CONFIGS = 'feeConfigs';
const DOWNPAYMENTS_CONFIGS = 'downPaymentConfigs';
const FNI_ATTRIBUTES_IDS = 'fniAttributeIds';
const FEE_CODES = 'feeCodes';
const DEAL_TYPE_CONFIGS = 'dealTypeConfigs';

export const TRADEIN_1 = 'tradeIn1';

const GENERAL_FEE_CONFIG_KEYS = {
  EnableServiceContractTax: 'enableServiceContractTax',
};

// setup keys related to State Fee & Tax Info only
export const STATE_FEE_TAX_INFO_KEYS = {
  GENERAL_CONFIG: 'generalConfig',
  STATE_FEE_CONFIG_V2: 'stateFeeConfigV2',
  STATE_TAX_CONFIG_V2: 'stateTaxConfigV2',
  GENERAL_FEE_CONFIG: 'generalFeeConfig',
  CUSTOMER_CONFIG: 'customerConfig',
  STATE_FEE_TARGETING_PARAMS: 'stateFeeTargetings',
};
const STATE_FEE_TAX_SELECTED_TEMPLATE_ID = 'stateFeeTaxSelectedTemplateId';

export const DEAL_HEADER_TYPES = {
  VIN: 'VIN',
  LICENSE_PLATE_NUMBER: 'LICENSE_PLATE_NUMBER',
  STOCK_NUMBER: 'STOCK_NUMBER',
};

export const SET_UP_KEYS = {
  fnIConfigs: FNNI_CONFIGS,
  feeConfigs: FEE_CONFIGS,
  downPaymentConfigs: DOWNPAYMENTS_CONFIGS,
  FNI_ATTRIBUTES_IDS,
  FEE_CODES,
  DEAL_TYPE_CONFIGS,
  DEAL_STATUS_CONFIGS,
  DEAL_STATUS_RULES: 'dealStatusRulesConfigs',
  FRONT_GROSS_CONFIG: 'frontGrossConfigs',
  BACK_GROSS_CONFIG: 'backGrossConfigs',
  DEALER_GROSS_CONFIG: 'dealerGrossConfigs',
  DEAL_CONFIG: 'dealConfig',
  DISCOUNT_CAP: 'discountCap',
  DEPOSIT_DISABLED_VEHICLE_STATUSES: 'depositDisabledVehicleStatus',
  WEBSITE_URLS: 'websiteurls',
  LOAN_DAYS_TO_FIRST_PAYMENT: 'loanDaysToFirstPayment',
  ANNUAL_MILES: 'annualMiles',
  ZIP_CODE: 'zipCode',
  DEALER_MARKET_ID: 'dealerMarketId',
  DEFAULT_COUNTY: 'defaultCounty',
  DEALER_STATE_CODE: 'dealerStateCode',
  CREDIT_SCORE: 'creditScore',
  ACCESSORIES: 'accessories',
  FNI: 'fnI',
  REBATE: 'rebate',
  DUE_BILLS: 'dueBills',
  DUE_BILL_ID: 'dueBillId',
  LEASE_DAYS_TO_FIRST_PAYMENT: 'leaseDaysToFirstPayment',
  LOAN_BUY_RATE_FOR_NEW: 'loanBuyRate',
  LOAN_BUY_RATE_FOR_USED: 'loanBuyRateForUsed',
  LOAN_BUY_RATE_TYPE: 'loanBuyRateType',
  DAYS_SELECTED_FOR_AVERAGE_RATE: 'daysSelectedForAverageRate',
  DAYS_SELECTED_FOR_AVERAGE_RATE_USED: 'daysSelectedForAverageRateUsed',
  DEFAULT_NEW_SELLING_PRICE,
  DEFAULT_OLD_SELLING_PRICE: 'usedVehiclePriceType',
  DEFAULT_DEMO_SELLING_PRICE: 'demoVehiclePriceType',
  DEFAULT_COUNTRY_DIALING_CODE: 'countryCode',
  DEAL_POSTING_PACKS: 'dealPostingPacksConfigs',
  SHEET_CONFIGS: 'sheetConfigs',
  LEASE_COMPARISON_SETUP: 'leaseComparisionSetup',
  YEARLY_MILES_1: 'yearlyMiles1',
  YEARLY_MILES_2: 'yearlyMiles2',
  YEARLY_MILES_3: 'yearlyMiles3',
  DEAL_SHEET_COUNT: 'dealSheetCount',
  RECAP_SHEET_COUNT: 'recapSheetCount',
  COVER_SHEET_COUNT: 'coverSheetCount',
  DOUBLE_SIDED_PRINT: 'doubleSidedPrint',
  OTHER_CONFIGS: 'otherConfigs',
  COMMISSION_SPLIT_TYPE: 'comissionSplittype',
  UNIT_COUNT_SPLIT_TYPE: 'unitCountSplitType',
  OVERRIDE_SALES_MANAGER_FNI_SUBMIT: 'overrideSalesManagerOnFAndISubmit',
  SERVICE_APPOINTMENT_CONFIG: 'serviceAppointmentConfig',
  NO_OF_DAYS_TO_LINK_TO_DEAL: 'noOfDaysToLinkToDeal',
  AUTO_ROLL_CASH_DEFICIENCY: 'cashDeficiencyAutoRoll',
  DEFAULT_AUTO_ROLL_CASH_DEFICIENCY: 'defaultAutoRollCashDeficiency',
  AUTO_ROLL_ZERO_PERCENT_APR: 'aprAutoRoll',
  ALWAYS_SHOW_APR_ON_DESKING: 'alwaysShowAprOnDesking',
  SHOW_HST_SEPARATELY: 'showHSTSeparately',
  REDUCE_INTEREST_FOR_EARLY_PAYMENT: 'reduceInterestForEarlyPayment',
  TRADE_IN_COUNT: 'noOfTradeIns',
  TRIM_REQUIRED_PROVIDER_IDS: 'trimRequiredProviderIds',
  OWNER_TYPE: 'ownerType',
  FNI_PDF_VIEW_TYPE: 'fniPdfViewType',
  PROVIDER_INFO: 'providerInfo',
  COST_ADJUSTMENTS: 'costAdjustments',
  COST_ADJUSTMENTS_IDS: 'costAdjustmentId',
  DEALER_MONTH_START_DAY: 'dealerMonthConfig.startDay',
  DEALER_MONTH_END_DAY: 'dealerMonthConfig.endDay',
  AUTO_UPDATE_COST_ADJ_RO: 'enableAutoCostAdjForRO',
  AUTO_UPDATE_COST_ADJ_PO: 'enableAutoCostAdjForPO',
  USE_DEALER_ZIP_MARKET_ID: 'useDealerZipMarketID',
  ADD_DISCLOSURE_TYPE_CONFIG: 'disclosureTypeTaxMappingForFNI',
  ENABLE_DEFERRED_PAYMENT_OPTIONS: 'enableDeferredPaymentOptions',
  ENABLE_DEAL_RECOMMENDATION: 'enableBudgetBasedRecommendationInDeals',
  ENABLE_VI_RECOMMENDATION: 'enableBudgetBasedRecommendationInVI',
  AUTO_ROLL_SERVICE_CONTRACTS: 'autoRollServiceContract',
  ADJUST_SELLING_PRICE_BASED_ON_VEHICLE_GROSS: 'adjustSellingPriceBasedOnVehicleGross',
  [UNLOCK_PIN_FOR_BOOKED_DEALS]: 'unlockPinForBookedDeals',
  [UNLOCK_PIN_FOR_CLOSED_DEALS]: 'unlockPinForClosedDeals',
  [DEAL_UNLOCK_PASSCODE_FOR_CLOSED_MONTHS_FOR_BOOKED_DEALS]: 'dealUnlockPasscodeForClosedMonthsForBookedDeals',
  [DEAL_UNLOCK_PASSCODE_FOR_CLOSED_MONTHS_FOR_CLOSED_DEALS]: 'dealUnlockPasscodeForClosedMonthsForClosedDeals',
  [DEAL_UNWIND_PASSCODE_FOR_BOOKED_DEALS]: 'dealUnlockPasscodeForUnwindForBookedDeals',
  [DEAL_UNWIND_PASSCODE_FOR_CLOSED_DEALS]: 'dealUnlockPasscodeForUnwindForClosedDeals',
  [DRS_FNI_MENU_DEAL_SWITCH_PASSCODE]: 'drsFnIMenuDealSwitchPasscode',
  VOLUME_BASED_COMMISSION: 'volumeBasedCommissions',
  VEHICLE_SUBTYPE_FOR_NEW_PROGRAM: 'usedVehicleSubTypesEligibleForNewVehicleProgram',
  DEAL_STATUS_FOR_MANDATORY_TAX_CODE: 'taxCodeMandatoryAfterDealStatus',
  REBATE_CONFIGS: 'rebateConfigs',
  SCAN_DEFAULTS: 'scanDefaults',
  RECALCULATE_PAYMENT: 'rolesWithDefaultAccessToPaymentRecalculation',
  ROLES_FOR_ASSIGNEE: 'rolesForAssignee',
  MAP_OF_ASSIGNEE: 'mapOfRolesForAssignee',
  USE_CUSTOMER_TIER_FOR_LOAN_APR: 'useCustomerTierForLoanApr',
  VIEW_DEMO_MILEAGE_ADJUSTMENT_AS: 'viewDemoMileageAdjustmentAs',
  INCLUDE_EXPIRED_PROGRAMS: 'includeExpiredPrograms',
  PRINT_ZERO_PRICE_PRODUCTS_IN_CONTRACT: 'printZeroPriceProductsInContract',
  USE_HARD_ADD_NUMBER: 'useHardAddNumber',
  RELEASE_VEHICLE_FROM_HOLD_IN_DAYS: 'releaseVehicleFromHoldInDays',
  MULTI_VEHICLE_DESKING: 'multiVehicleDesking',
  LENDERS: 'lenderConfigs',
  ENABLE_PRE_CLOSE_WORKFLOW: 'enablePreCloseWorkflow',
  AUTO_ARCHIVE_SHEETS: 'archiveSheets',
  DEFAULT_HEADER_DATE: 'defaultHeaderDate',
  SHOW_ACCOUNTING_DATE: 'showAccountingDateWhileUnwinding',
  ENABLE_EFFECTIVE_INTEREST_RATE: 'showEffectiveInterestRate',
  PAYMENT_FREQUENCY_CONFIG: 'paymentFrequencyConfig',
  LOAN_FREQUENCY: 'loanFrequency',
  LEASE_FREQUENCY: 'leaseFrequency',
  DAYS_TO_FIRST_PAYMENT: 'daysToFirstPayment',
  APR_CONFIG: 'aprConfig',
  SHOW_MAX_RESIDUAL_MSRP: 'showMaximumResidualizedMSRP',
  ADD_TRADEIN_VEHICLE_TO_VI_ON_DEAL_CONFIRM: 'addTradeInVehicleToViOnDealConfirm',
  ASSIGNEE_LIMITS: 'assigneeLimits',
  MAP_OF_ASSIGNEE_LIMITS: 'mapOfAssigneeLimits',
  ASSIGNEE_COMM_SPLIT_OPTIONS: 'assigneeCommissionSplitOptions',
  ASSIGNEE_UNIT_COUNT_SPLIT: 'assigneeUnitCountSplit',
  ALLOW_PAYMENT_BEYOND_DUE_AMOUNT: 'allowPaymentBeyondDueAmount',
  ALLOW_REFUND_BEYOND_REFUND_AMOUNT: 'allowRefundBeyondRefundAmount',
  ALLOW_RESERVE_AND_SOLD_VEHICLE: 'allowReserveAndSoldVehicleSelection',
  VEHICLE_CHANGE_SELLING_PRICE__UPDATE: 'vehicleChangeSellingPriceUpdate',
  AUTO_CAL_COMMISSION_FOR_BOOKED_CLOSED_DEALS: 'autocalculatecommisonforBookedandClosedDeals',
  ALLOW_MULTIPLE_RESERVATIONS: 'allowMultipleReservationsOnVehicle',
  CREATE_APPOINTMENTS_FROM_DEALS: 'createAppointmentFromDeals',
  DEPOSIT_RULES: 'depositRules',
  DISPLAY_MODEL_SOURCE: 'displayModelSource',
  SHOW_ASSUMPTIVE_PAY_OFF_POP_UP: 'showAssumptivePayOffPopUp',
  VENDOR_MANAGEMENT: 'vendorConfigs',
  ENABLE_CUSTOMER_CASH_PERCENTAGE: 'enableCustomerCashPercentage',
  ENABLE_CTA_RDR_EVENT: 'enableCtaRdrEvent',
  DEDUCT_CUSTOMER_CASH_FROM_AMOUNT_FINANCED_CASH_DEAL: 'deductCustomerCashfromAmountFinancedforCashDeal',
  ADD_INVOICE_TO_DEAL_JACKET: 'addInvoiceToDealJacket',
  UPDATE_DEAL_INFO_IN_DEAL_TASK_MANAGER: 'enableTaskManagerDealConfig',
  ...STATE_FEE_TAX_INFO_KEYS,
  STATE_FEE_TAX_SELECTED_TEMPLATE_ID,
  SALES_FEE_SETUP: 'salesFeeSetup',
  REMOVE_DEFAULT_OPTIONS: 'removeDefaultOptions',
  PAYMENT_OPTION_CONFIGS: 'paymentOptionConfigs',
  PURCHASE_ORDER_FORM_COUNT: 'purchaseOrderFormCount',
  SALES_ORDER_FORM_COUNT: 'salesOrderFormCount',
  DEFAULT_RESERVATION_AMOUNT: 'defaultReservationAmount',
  DEFAULT_PARTEX_VAT_INFO: 'defaultPartExVatInfo',
  CUSTOMER_CASH: 'customerCash',
  PLATFORM: 'platform',
  FNI_ATTRIBUTES: 'fniAttributes',
  DEFAULT_UNIT_COUNT_SPLIT_FI_MANAGER: 'defaultUnitCountSplitFIManager',
  DEFAULT_UNIT_COUNT_SPLIT_SALES_MANAGER: 'defaultUnitCountSplitSalesManager',
  DEFAULT_UNIT_COUNT_SPLIT_SALES_PERSON: 'defaultUnitCountSplitSalesperson',
  DEFAULT_UNIT_COUNT_SPLIT_E_ADVISOR: 'defaultUnitCountSplitEAdvisor',
  VIEW_DEMO_MILEAGE_ADJUSTMENT_TYPE_AS: 'viewDemoMileageAdjustmentTypeas',
  ROLE_IDS: 'roleIds',
  DEAL_STATUS_AFTER_WHICH_TAX_CODE_IS_MANDATORY: 'dealStatusAfterwhichTaxCodeIsMandatory',
  VEHICLE_SUBTYPES_ELIGIBLE_FOR_NEW_VEHICLE_PROGRAM: 'vehicleSubTypesEligibleForNewVehicleProgram',
  TRADE_IN_OWNER_TYPE: 'tradeInOwnerType',
  COMMISSION_SPLIT_OPTIONS_FI_MANAGER: 'commissionSplitOptionsFIManager',
  COMMISSION_SPLIT_OPTIONS_SALES_MANAGER: 'commissionSplitOptionsSalesManager',
  COMMISSION_SPLIT_OPTIONS_SALES_PERSON: 'commissionSplitOptionsSalesperson',
  COMMISSION_SPLIT_OPTIONS_E_ADVISOR: 'commissionSplitOptionsEAdvisor',
  AUTO_ROLL_0_APR: 'autoRoll0%APR',
  SHOW_AUTO_ROLL_CASH_DEFICIENCY_POPUP: 'showAutorollCashDeficiencyPopup',
  ASSIGN_SALES_MANAGER_FI_STATUS_UPDATE: 'assignSalesManagerFIStatusUpdate',
  DISABLE_FNI_UPDATES_FROM_DESKING: 'disableFNIUpdatesFromDesking',
  DEFAULT_DEPOSIT_AMOUNT_PRODUCT_CLASSIFICATION: 'defaultDepositProductClassification',
  DEFAULT_TRADE_IN_PRODUCT_CLASSIFICATION: 'defaultTradeinProductClassification',
  DEFAULT_DEPOSIT_ACCOUNTING_GROUP: 'defaultDepositAccountingGroup',
  DEFAULT_DEPOSIT_VAT_INFO: 'defaultDepositVatInfo',
  NUMBER_OF_DAYS_BEFORE_LAST_ACTIVITY: 'dealLastActivityDay',
  SHOW_CRM_TASKS_IN_DEAL_LIST: 'showCrmTasksInDeallist',
  DEAL_STATUS_FOR_LAST_ACTIVITY: 'dealStatusForLastActivity',
  DISABLE_DELIVERY_APPOINTMENT_STATUS: 'disableDeliveryAppointmentStatus',
  DISCOUNT_MATRIX: 'discountBasedOnVehicleType',
  QUOTE_EXPIRY_DURATION: 'quoteExpiryDuration',
  FINANCE_COMMISSION_GROSS_TYPE: 'financeCommissionGrossType',
  DEFAULT_DELIVERY_FEE_TYPE: 'defaultDeliveryFeeType',
  VEHICLE_DEAL_HEADER_IDENTIFIER: 'vehicleDealHeaderIdentifier',
  ENABLE_ROLL_PAYMENT: 'enableRollPayment',
  DISABLE_TRADE_IN: 'disableTradeIn',
  SHOW_EMI_FOR_PRODUCTS_AND_OTHER_BREAKUP: 'showEmiForProductsAndOtherBreakup',
  CAPTURE_SIGNATURE_TIMESTAMP: 'captureSignatureTimestamp',
  MANUAL_DEAL_PAUSE: 'enableDealPause',
};

export const LOCATION_WORKFLOW_KEYS = {
  USE_DEALERSHIP_ADDRESS_INFORMATION: 'useDealershipAddressInformation',
  ENABLE_VEHICLE_LOCATION_WORKFLOW: 'enableVehicleLocationOverflow',
};

export const LOCATION_WORKFLOW_OPTIONS = {
  USE_DEALERSHIP_ADDRESS_INFO_ON_DEAL_PDFS_FROM_FORMSETUP: __(
    'Use Dealership Address Information on Deal PDFs from Forms Setup'
  ),
  ENABLE_VEHICLE_LOCATION_WORKFLOW: __('Enable Vehicle Location Workflow'),
};

export const PPSA_FEES = {
  YEARLY: 'ppsaConsecutiveYearFee',
  FIRST_YEAR: 'ppsaFirstYearFee',
};

export const DEPOSIT_TYPES = {
  FIXED_AMOUNT: 'FIXED_AMOUNT',
  PERCENTAGE: 'PERCENTAGE',
};

export const DEPOSIT_PRICE_TYPES = {
  MSRP: 'MSRP',
  RETAIL_PRICE: 'RETAIL_PRICE',
};

export const DEPOSIT_RULE_KEYS = {
  DEPOSIT_TYPE: 'depositRuleType',
  AMOUNT: 'amount',
  PERCENTAGE: 'percentage',
  PRICE_TYPE: 'priceType',
};

export const CERTIFICATION_TYPE_NEW = {
  CERTIFIED_PRE_OWNED: 'CERTIFIED_PRE_OWNED',
  DEALER_CERTIFIED: 'DEALER_CERTIFIED',
  CPO_LIGHT: 'CPO_LIGHT',
};

export const ATTRIBUTE_OPTIONS_FOR_DOWN_PAYMENT = [
  {
    label: __('Selling Price'),
    value: SELLING_PRICE_RANGE,
  },
  {
    label: __('Deal Type'),
    value: DEAL_TYPE,
  },
];

export const DOWN_PAYMENT_TARGET_PARAMS_ATTRIBUTES_OPTION = [
  {
    label: __('Payment Type'),
    value: PAYMENT_TYPE,
  },
  {
    label: __('Makes'),
    value: MAKES,
  },
  {
    label: __('Stock Type'),
    value: STOCK_TYPE,
  },
  {
    label: __('Deal Type'),
    value: DEAL_TYPE,
  },
];

export const DUE_BILLS_TARGET_PARAMS_ATTRIBUTE_OPTIONS = () => {
  const isRVVehiclesSupported = DealerPropertyHelper.isRVVehiclesSupported();
  const isV2CertificationEnabled = DealerPropertyHelper.isV2CertificationEnabled();
  let dueBillsOptions;
  if (isInchcapeOrRRG()) {
    dueBillsOptions = [
      ...DOWN_PAYMENT_TARGET_PARAMS_ATTRIBUTES_OPTION,
      {
        label: __('Body Class'),
        value: BODY_CLASS,
      },
      {
        label: __('Models'),
        value: MODELS,
      },
      {
        label: __('Fuel Type'),
        value: FUEL_TYPE,
      },
    ];
  } else
    dueBillsOptions = [
      ...DOWN_PAYMENT_TARGET_PARAMS_ATTRIBUTES_OPTION,
      {
        label: __('Body Class'),
        value: BODY_CLASS,
      },
      {
        label: __('Models'),
        value: MODELS,
      },
      ...(isRVVehiclesSupported
        ? [
            {
              label: __('Vehicle Category'),
              value: VEHICLE_CATEGORY,
            },
          ]
        : []),
      ...(isCanadaDealer()
        ? [
            {
              label: __('Manufacturer Model Code'),
              value: MANUFACTURE_MODEL_CODE,
            },
          ]
        : []),
      ...(isV2CertificationEnabled
        ? [
            {
              label: __('Certification Status'),
              value: CERTIFICATION_STATUS,
            },
          ]
        : [
            {
              label: __('OEM Certified'),
              value: VEHICLE_STATUS,
            },
            {
              label: __('Dealer Certified'),
              value: DEALER_CERTIFIED,
            },
          ]),
    ];
  return dueBillsOptions;
};

const FEE_ATTRIBUTE_OPTION_FIELD_TO_LABEL_MAP = {
  [PAYMENT_TYPE]: __('Payment Type'),
  [STOCK_TYPE]: __('Stock Type'),
  [MAKES]: __('Makes'),
  [MANUFACTURE_MODEL_CODE]: __('Manufacturer Model Code'),
  [MODELS]: __('Models'),
  [STATES]: __('$$(State)'),
  [COUNTY]: __('County'),
  [CITY]: __('City'),
  [MODEL_YEAR]: __('Model Year Difference Greater Than'),
  [MODEL_YEAR_DIFFERENCE_LESSER_THAN]: __('Model Year Difference Lesser Than'),
  [FUEL_TYPE]: __('Fuel Type'),
  [COLLECT_ONLY_WHEN_COLLECTFEE_IS_ON]: __('Collect Only When Collect Fee is ON'),
  [BODY_CLASS]: __('Body Class'),
  [DEAL_TYPE]: __('Deal Type'),
  [PART_EXCHANGE]: __('Part-Exchange'),
  [REGISTRATION_EXPIRATION_GREATER_THAN]: __('Registration Expiration Date Greater Than'),
  [REGISTRATION_EXPIRATION_LESSER_THAN]: __('Registration Expiration Date Lesser Than'),
  [TRADE_INS]: __('Trade In'),
  [VEHICLE_CATEGORY]: __('Vehicle Category'),
  [TRADE_IN_OWNERSIP_TYPE]: __('Trade-In OwnershipType'),
  [LENDERS]: __('Lenders'),
  [LICENSE_EXPIRATION_DAYS_GREATER_THAN]: __('License Expiration Days Greater Than'),
  [LICENSE_EXPIRATION_DATE_IN_DAYS]: __('License Expiration Days Lesser Than'),
  [VEHICLE_STATUS]: __('OEM Certified'),
  [DEALER_CERTIFIED]: __('Dealer Certified'),
  [CERTIFICATION_STATUS]: __('Certification Status'),
};

const getFeeAttributeOptionFields = () => {
  if (isInchcape()) {
    return [
      PAYMENT_TYPE,
      STOCK_TYPE,
      MAKES,
      STATES,
      COUNTY,
      CITY,
      MODEL_YEAR,
      MODEL_YEAR_DIFFERENCE_LESSER_THAN,
      FUEL_TYPE,
      COLLECT_ONLY_WHEN_COLLECTFEE_IS_ON,
      BODY_CLASS,
      DEAL_TYPE,
      PART_EXCHANGE,
      REGISTRATION_EXPIRATION_GREATER_THAN,
      REGISTRATION_EXPIRATION_LESSER_THAN,
    ];
  }

  if (isRRG()) {
    return [
      PAYMENT_TYPE,
      STOCK_TYPE,
      MAKES,
      FUEL_TYPE,
      BODY_CLASS,
      COLLECT_ONLY_WHEN_COLLECTFEE_IS_ON,
      STATES,
      COUNTY,
      CITY,
      DEAL_TYPE,
      TRADE_INS,
      REGISTRATION_EXPIRATION_GREATER_THAN,
      REGISTRATION_EXPIRATION_LESSER_THAN,
      MODEL_YEAR,
      MODEL_YEAR_DIFFERENCE_LESSER_THAN,
    ];
  }

  const defaultFeeAttributeFields = [
    PAYMENT_TYPE,
    STOCK_TYPE,
    TRADE_INS,
    MAKES,
    TRADE_IN_OWNERSIP_TYPE,
    STATES,
    COUNTY,
    CITY,
    LENDERS,
    LICENSE_EXPIRATION_DAYS_GREATER_THAN,
    LICENSE_EXPIRATION_DATE_IN_DAYS,
    MODEL_YEAR,
    MODEL_YEAR_DIFFERENCE_LESSER_THAN,
    FUEL_TYPE,
    COLLECT_ONLY_WHEN_COLLECTFEE_IS_ON,
    BODY_CLASS,
    DEAL_TYPE,
    ...(DealerPropertyHelper.isV2CertificationEnabled() ? [CERTIFICATION_STATUS] : [VEHICLE_STATUS, DEALER_CERTIFIED]),
    ...(isCanadaDealer() ? [MODELS, MANUFACTURE_MODEL_CODE] : []),
  ];

  if (DealerPropertyHelper.isRVVehiclesSupported()) {
    return [...defaultFeeAttributeFields, VEHICLE_CATEGORY];
  }

  return defaultFeeAttributeFields;
};

export const ATTRIBUTE_OPTIONS_FOR_FEE = () => {
  const feeAttributeFields = getFeeAttributeOptionFields();

  return _map(feeAttributeFields, fieldId => ({
    value: fieldId,
    label: tget(FEE_ATTRIBUTE_OPTION_FIELD_TO_LABEL_MAP, fieldId),
  }));
};

export const ATTRIBUTE_OPTIONS_FOR_COST_ADJUSTMENT = () => {
  const isRVVehiclesSupported = DealerPropertyHelper.isRVVehiclesSupported();
  let costAdjustmentOptions;
  if (isInchcape()) {
    costAdjustmentOptions = [
      {
        label: __('Stock Type'),
        value: STOCK_TYPE,
      },
      {
        label: __('Stock Sub Type'),
        value: STOCK_SUB_TYPE,
      },
      {
        label: __('Deal Type'),
        value: DEAL_TYPE,
      },
      {
        label: __('Lenders'),
        value: LENDERS,
      },
      {
        label: __('Makes'),
        value: MAKES,
      },
    ];
  } else
    costAdjustmentOptions = [
      {
        label: __('Stock Type'),
        value: STOCK_TYPE,
      },
      {
        label: __('Stock Sub Type'),
        value: STOCK_SUB_TYPE,
      },
      {
        label: __('Deal Type'),
        value: DEAL_TYPE,
      },
      {
        label: __('Lenders'),
        value: LENDERS,
      },
      {
        label: __('Makes'),
        value: MAKES,
      },
      ...(isRVVehiclesSupported
        ? [
            {
              label: __('Vehicle Category'),
              value: VEHICLE_CATEGORY,
            },
          ]
        : []),
    ];
  return costAdjustmentOptions;
};
export const ATTRIBUTE_OPTIONS_FOR_LENDERS = () => {
  const isRVVehiclesSupported = DealerPropertyHelper.isRVVehiclesSupported();
  const lenderOptions = [
    {
      label: __('Payment Type'),
      value: PAYMENT_TYPE,
    },
    {
      label: __('Makes'),
      value: MAKES,
    },
    {
      label: __('Stock Type'),
      value: STOCK_TYPE,
    },
  ];
  if (isInchcapeOrRRG()) {
    return lenderOptions;
  }
  return [
    ...lenderOptions,
    ...(isRVVehiclesSupported
      ? [
          {
            label: __('Vehicle Category'),
            value: VEHICLE_CATEGORY,
          },
        ]
      : []),
    {
      label: __('$$(State)'),
      value: STATES,
    },
    {
      label: __('Site Sold'),
      value: SITE,
    },
  ];
};

export const DOWN_PAYMENT_TARGET_PARAMS_OPERATOR_OPTION = [
  {
    label: __('Include'),
    value: INCLUDE_OPERATOR,
  },
];

export const getEMptyDownPaymentConfig = () => ({
  id: getRandomId(),
  configName: '',
  downPayment1: 0,
  downPayment2: 0,
  downPayment3: 0,
  targetingParams: [
    {
      attribute: SELLING_PRICE_RANGE,
      operator: INCLUDE_OPERATOR,
      values: [0, 0],
      id: getRandomId(),
    },
  ],
});

export const getEmptyFNIConfig = () => ({
  id: getRandomId(),
  configName: '',
  fniAttributeIds: [],
  targetingParams: [
    {
      attribute: PAYMENT_TYPE,
      operator: INCLUDE_OPERATOR,
      values: [],
      id: getRandomId(),
    },
  ],
});

export const getEmptyDueBillConfig = () => ({
  id: getRandomId(),
  dueBillId: [],
  configName: '',
  targetingParams: [
    {
      attribute: PAYMENT_TYPE,
      operator: INCLUDE_OPERATOR,
      values: [],
      id: getRandomId(),
    },
  ],
});

export const getEmptyDepositRuleConfig = () => ({
  id: getRandomId(),
  depositRuleType: DEPOSIT_TYPES.FIXED_AMOUNT,
  configName: EMPTY_STRING,
  amount: 0,
  percentage: 0,
  priceType: DEPOSIT_PRICE_TYPES.MSRP,
  applicationCriterion: [
    {
      attribute: PAYMENT_TYPE,
      operator: INCLUDE_OPERATOR,
      values: EMPTY_ARRAY,
      id: getRandomId(),
    },
  ],
});

export const getEmptyCostAdjustmentConfig = () => ({
  id: getRandomId(),
  [SET_UP_KEYS.COST_ADJUSTMENTS_IDS]: [],
  configName: '',
  targetingParams: [
    {
      attribute: STOCK_TYPE,
      operator: INCLUDE_OPERATOR,
      values: [],
      id: getRandomId(),
    },
  ],
});

export const getEmptyLenderConfig = () => ({
  id: getRandomId(),
  lenderIds: [],
  configName: '',
  targetingParams: [
    {
      attribute: PAYMENT_TYPE,
      operator: INCLUDE_OPERATOR,
      values: [],
      id: getRandomId(),
    },
  ],
});

export const getEmptyVendorConfig = () => ({
  id: getRandomId(),
  configName: EMPTY_STRING,
  state: EMPTY_STRING,
  vendorId: EMPTY_STRING,
  targetingParams: [
    {
      attribute: VENDOR_ATTRIBUTES.TAX_TYPE,
      operator: INCLUDE_OPERATOR,
      values: [TAX_TYPE_VALUES.STATE_TAX],
      id: getRandomId(),
    },
  ],
});

export const getEmptyFeeConfig = () => ({
  id: getRandomId(),
  configName: '',
  feeCodes: [],
  targetingParams: [
    {
      attribute: PAYMENT_TYPE,
      operator: INCLUDE_OPERATOR,
      values: [],
      id: getRandomId(),
    },
    {
      attribute: STOCK_TYPE,
      operator: INCLUDE_OPERATOR,
      values: [],
      id: getRandomId(),
    },
  ],
});

export const getEmptyDueDealPostingPacks = () => ({
  id: getRandomId(),
  configName: '',
  packTypes: {},
  packs: [],
  targetingParams: [
    {
      attribute: PAYMENT_TYPE,
      values: [],
      operator: INCLUDE_OPERATOR,
      id: getRandomId(),
    },
  ],
});

export const getEmptyStateFeeTargetingConfigs = () => ({
  id: getRandomId(),
  configName: '',
  fees: [],
  targetingParams: [
    {
      attribute: PAYMENT_TYPE,
      operator: INCLUDE_OPERATOR,
      values: [],
      id: getRandomId(),
    },
    {
      attribute: STOCK_TYPE,
      operator: INCLUDE_OPERATOR,
      values: [],
      id: getRandomId(),
    },
  ],
});

// export const getInitialStateFeeConfig = () => {
//   const statesByMarketIds = _invert(MSStateID);
//   const flattenConfig = config => ({
//     key: config.key, enabled: config.defaultValue, hidden: config.hideInSetup, hasValue: config.hasValue, value: config.defaultInputValue, paymentType: config.paymentType,
//   });
//   const flattenTaxPctConfig = config => ({
//     key: config.key, enabled: false, value: 0, paymentType: config.paymentType,
//   });

//   return Array.from(
//     new Set([...Object.keys(defaultTaxConfig), ...Object.keys(defaultFeeConfig), ...Object.keys(defaultTaxPctConfig)])
//   ).map(stateKey => ({
//     taxes: (defaultTaxConfig[stateKey] || EMPTY_ARRAY).map(flattenConfig),
//     fees: (defaultFeeConfig[stateKey] || EMPTY_ARRAY).map(flattenConfig),
//     taxPercentages: (defaultTaxPctConfig[stateKey] || EMPTY_ARRAY).map(flattenTaxPctConfig),
//     general: (defaultGeneralConfig[stateKey] || EMPTY_ARRAY).map(flattenConfig),
//     stateId: statesByMarketIds[stateKey],
//   }));
// };

export const getInitialGeneralFeeConfig = () =>
  _reduce(
    GENERAL_FEE_CONFIG_KEYS,
    (result, value) => ({
      ...result,
      [value]: false,
    }),
    {}
  );

export const DOWN_PAYMENT_CONFIG = [getEMptyDownPaymentConfig()];
export const FEE_CONFIG = [getEmptyFeeConfig()];
export const FNI_CONFIG = [getEmptyFNIConfig()];
export const DUE_BILL_CONFIG = [getEmptyDueBillConfig()];
export const DEPOSIT_RULE_CONFIG = [getEmptyDepositRuleConfig()];
export const COST_ADJUSTMENT_CONFIG = [getEmptyCostAdjustmentConfig()];
// export const STATE_FEE_CONFIG = getInitialStateFeeConfig();
export const GENERAL_FEE_CONFIG = getInitialGeneralFeeConfig();

export const EMPTY_CONFIGS = {
  [DOWNPAYMENTS_CONFIGS]: () => getEMptyDownPaymentConfig(),
  [FEE_CONFIGS]: () => getEmptyFeeConfig(),
  [FNNI_CONFIGS]: () => getEmptyFNIConfig(),
  [SET_UP_KEYS.DUE_BILLS]: () => getEmptyDueBillConfig(),
  [SET_UP_KEYS.DEPOSIT_RULES]: () => getEmptyDepositRuleConfig(),
  [SET_UP_KEYS.DEAL_POSTING_PACKS]: () => getEmptyDueDealPostingPacks(),
  [SET_UP_KEYS.COST_ADJUSTMENTS]: () => getEmptyCostAdjustmentConfig(),
  [SET_UP_KEYS.LENDERS]: () => getEmptyLenderConfig(),
  [SET_UP_KEYS.VENDOR_MANAGEMENT]: getEmptyVendorConfig,
  [SET_UP_KEYS.STATE_FEE_TARGETING_PARAMS]: () => getEmptyStateFeeTargetingConfigs(),
};

export const getEmptyTargetParamDownPayment = () => ({
  attribute: '',
  operator: '',
  values: ['', ''],
  id: getRandomId(),
});

export const getEmptyTargetParamForFNI = () => ({
  attribute: '',
  operator: INCLUDE_OPERATOR,
  values: [],
  id: getRandomId(),
});

export const getEmptyTargetParamForFee = () => ({
  attribute: '',
  operator: INCLUDE_OPERATOR,
  values: [],
  id: getRandomId(),
});

export const getEmptyTargetParamForPostingPacksI = () => ({
  attribute: '',
  operator: INCLUDE_OPERATOR,
  values: [],
  id: getRandomId(),
});

export const getEmptyTargetParamForVendorManagement = () => ({
  attribute: '',
  operator: INCLUDE_OPERATOR,
  values: [],
  id: getRandomId(),
});

export const getEmptyTargetParamForCostAdjustments = () => ({
  attribute: '',
  operator: INCLUDE_OPERATOR,
  values: [],
  id: getRandomId(),
});

export const getEmptyTargetParamForDueBills = () => ({
  attribute: PAYMENT_TYPE,
  operator: INCLUDE_OPERATOR,
  values: [],
  id: getRandomId(),
});

export const getEmptyTargetParamForDepositRules = () => ({
  attribute: PAYMENT_TYPE,
  operator: INCLUDE_OPERATOR,
  values: ['', ''],
  id: getRandomId(),
});

export const EMPTY_TARGET_PARAMS = {
  [DOWNPAYMENTS_CONFIGS]: () => getEmptyTargetParamDownPayment(),
  [FEE_CONFIGS]: () => getEmptyTargetParamForFee(),
  [FNNI_CONFIGS]: () => getEmptyTargetParamForFNI(),
  [SET_UP_KEYS.DEAL_POSTING_PACKS]: () => getEmptyTargetParamForPostingPacksI(),
  [SET_UP_KEYS.COST_ADJUSTMENTS]: () => getEmptyTargetParamForCostAdjustments(),
  [SET_UP_KEYS.DUE_BILLS]: () => getEmptyTargetParamForDueBills(),
  [SET_UP_KEYS.LENDERS]: () => getEmptyTargetParamForFee(),
  [SET_UP_KEYS.STATE_FEE_TARGETING_PARAMS]: () => getEmptyTargetParamForFee(),
  [SET_UP_KEYS.DEPOSIT_RULES]: () => getEmptyTargetParamForDepositRules(),
  [SET_UP_KEYS.VENDOR_MANAGEMENT]: getEmptyTargetParamForVendorManagement,
};

export const TARGETING_VALUES_OPTIONS_COLLECT_ONLY_WHEN_COLLECTFEE_IS_ON = [
  {
    label: __('Yes'),
    value: 'true',
  },
  {
    label: __('Always Collect'),
    value: 'false',
  },
];

export const DEALER_CERTIFIED_STATUS = [
  {
    label: __('Dealer Certified'),
    value: 'DEALER_CERTIFIED',
  },
  {
    label: __('Dealer Not Certified'),
    value: 'DEALER_NOT_CERTIFIED',
  },
];

export const CERTIFICATION_STATUS_OPTIONS_US = [
  {
    label: __('Dealer Certified'),
    value: CERTIFICATION_TYPE_NEW.DEALER_CERTIFIED,
  },
  {
    label: __('Certified Pre-Owned'),
    value: CERTIFICATION_TYPE_NEW.CERTIFIED_PRE_OWNED,
  },
];

const getCertificationStatus = () => {
  let defaultOptions = CERTIFICATION_STATUS_OPTIONS_US;
  if (isCanadaDealer()) {
    defaultOptions = [
      ...defaultOptions,
      {
        label: __('Certified Pre-Owned Light'),
        value: CERTIFICATION_TYPE_NEW.CPO_LIGHT,
      },
    ];
  }
  return defaultOptions;
};

export const OEM_CERTIFIED_STATUS = [
  {
    label: __('OEM Certified'),
    value: 'OEM_CERTIFIED',
  },
  {
    label: __('OEM Not Certified'),
    value: 'OEM_NOT_CERTIFIED',
  },
];

export const VEHICLE_CERTIFIED_STATUS = [
  {
    label: __('False'),
    value: false,
  },
  {
    label: __('True'),
    value: true,
  },
];

export const TARGETING_VALUES_OPTIONS_FOR_IN_STATE = [
  {
    label: __('Yes'),
    value: true,
  },
];

export const TARGETING_VALUES_OPTIONS_FOR_OUT_STATE = [
  {
    label: __('Yes'),
    value: true,
  },
];

export const TARGETING_VALUES_OPTIONS_FOR_BODY_CLASS = [
  {
    label: __('Default'),
    value: 'Default',
  },
  ...MARKET_CLASS_CATEGORY_OPTIONS,
];

export const TARGETING_VALUES_OPTIONS_FOR_TRADEIN_OWNERSHIP_TYPE = [
  {
    label: __('Loan/Rental'),
    value: LOAN,
  },
  {
    label: __('Lease'),
    value: LEASE,
  },
];

export const TARGETING_VALUES_OPTIONS_FOR_DEPOSIT_RULES = () => {
  const isRVVehiclesSupported = DealerPropertyHelper.isRVVehiclesSupported();

  return [
    {
      label: __('Models'),
      value: MODELS,
    },
    {
      label: __('Makes'),
      value: MAKES,
    },
    {
      label: __('Stock Type'),
      value: STOCK_TYPE,
    },
    {
      label: __('Model Year'),
      value: MODEL_YEAR,
    },
    {
      label: __('Age'),
      value: AGE,
    },
    ...(isRVVehiclesSupported
      ? [
          {
            label: __('Vehicle Category'),
            value: VEHICLE_CATEGORY,
          },
        ]
      : []),
  ];
};

export const ATTRIBUTE_OPTIONS_FOR_DEAL_POSTING_PACKS = () => {
  const isRVVehiclesSupported = DealerPropertyHelper.isRVVehiclesSupported();
  const isV2CertificationEnabled = DealerPropertyHelper.isV2CertificationEnabled();
  return [
    {
      label: __('Vehicle Sub Type'),
      value: STOCK_SUB_TYPE,
    },
    {
      label: __('Trade Vehicle Sub Type'),
      value: TRADE_IN_SUB_TYPE,
    },
    // {
    //   label: __('Sold Vehicle Sub Type'),
    //   value: SOLD_VEHICLE_SUB_TYPE,
    // },
    {
      label: __('Trade Vehicle Cost Range'),
      value: TRADE_IN_ACV_RANGE,
    },
    {
      label: __('Selling Price Range'),
      value: SELLING_PRICE_RANGE,
    },
    {
      label: __('Invoice Price Range'),
      value: INVOICE_PRICE_RANGE,
    },
    {
      label: __('F&I Product Selling Price Range'),
      value: F_N_I_PRODUCT_SELLING_PRICE_RANGE,
    },
    {
      label: __('F&I Product Cost Range'),
      value: F_N_I_PRODUCT_COST_PRICE_RANGE,
    },
    {
      label: __('Due Bill Selling Price Range'),
      value: DUE_BILL_SELLING_PRICE_RANGE,
    },
    {
      label: __('Due Bill Cost Range'),
      value: DUE_BILL_COST_PRICE_RANGE,
    },
    {
      label: __('Stock Type'),
      value: STOCK_TYPE,
    },
    {
      label: __('Deal Type'),
      value: DEAL_TYPE,
    },
    {
      label: __('Body Class'),
      value: BODY_CLASS,
    },
    ...(isRVVehiclesSupported
      ? [
          {
            label: __('Vehicle Category'),
            value: VEHICLE_CATEGORY,
          },
        ]
      : []),
    ...(isV2CertificationEnabled
      ? [
          {
            label: __('Certification Status'),
            value: CERTIFICATION_STATUS,
          },
        ]
      : [
          {
            label: __('OEM Certified'),
            value: OEM_CERTIFIED,
          },
          {
            label: __('Dealer Certified'),
            value: DEALER_CERTIFIED,
          },
        ]),
    {
      label: __('Models'),
      value: MODELS,
    },
    {
      label: __('Makes'),
      value: MAKES,
    },
    ...(isCanadaDealer()
      ? [
          {
            label: __('Manufacturer Model Code'),
            value: MANUFACTURE_MODEL_CODE,
          },
        ]
      : []),
    {
      label: __('Trims'),
      value: TRIMS,
    },
    {
      label: __('Site Sold'),
      value: SITE,
    },
  ];
};

const TRADE_IN_VEHICLE_PREFIX_OPTION = [
  {
    label: __('Trade-in Vehicle Prefix'),
    value: VEHICLE_PREFIX,
  },
];

const VEHICLE_PREFIX_OPTIONS = [
  {
    label: __('Vehicle Prefix'),
    value: VEHICLE_PREFIX,
  },
];

export const PACK_SPECIFIC_ATTRIBUTE_OPTIONS_FOR_DEAL_POSTING_PACKS = packTypeValue => {
  const isAccountingInterPrefixEnabled = DealerPropertyHelper.isAccountingInterPrefixEnabled();

  if (packTypeValue === TRADEIN_1) {
    return [
      {
        label: __('Trade-in Vehicle Year'),
        value: TRADE_IN_YEAR,
      },
      {
        label: __('Trade-in Vehicle $$(Mileage)'),
        value: TRADE_IN_MILEAGE_RANGE,
      },
      ...(isAccountingInterPrefixEnabled ? TRADE_IN_VEHICLE_PREFIX_OPTION : EMPTY_ARRAY),
    ];
  }
  if (packTypeValue === VEHICLE_TYPE.USED) {
    return [
      {
        label: __('Vehicle Certified'),
        value: VEHICLE_CERTIFIED,
      },
      {
        label: __('Vehicle $$(Mileage)'),
        value: VEHICLE_MILEAGE,
      },
      {
        label: __('Vehicle Year'),
        value: MODEL_YEAR,
      },
      ...(isAccountingInterPrefixEnabled ? VEHICLE_PREFIX_OPTIONS : EMPTY_ARRAY),
    ];
  }
  if (packTypeValue === VEHICLE_TYPE.NEW) {
    return [
      {
        label: __('Vehicle $$(Mileage)'),
        value: VEHICLE_MILEAGE,
      },
      {
        label: __('Vehicle Year'),
        value: MODEL_YEAR,
      },
      ...(isAccountingInterPrefixEnabled ? VEHICLE_PREFIX_OPTIONS : EMPTY_ARRAY),
    ];
  }
  return [
    {
      label: __('Payment Type'),
      value: PAYMENT_TYPE,
    },
    ...(isAccountingInterPrefixEnabled && _includes(Object.values(VEHICLE_TYPE), packTypeValue)
      ? VEHICLE_PREFIX_OPTIONS
      : EMPTY_ARRAY),
  ];
};

const paymentModes = (paymentOptionConfigs = EMPTY_ARRAY) => {
  const LEASE_DISABLED_PAYMENT_MODES = paymentOptionConfigs?.filter(
    pmode =>
      pmode.value === PAYMENT_TYPES.LOAN || pmode.value === PAYMENT_TYPES.CASH || pmode.value === PAYMENT_TYPES.BALLOON
  );
  const LEASE_DISABLED_PAYMENT_MODES_FOR_AEC_PROGRAM = paymentOptionConfigs?.filter(
    pmode => pmode.value === PAYMENT_TYPES.LOAN || pmode.value === PAYMENT_TYPES.CASH
  );
  const PAYMENT_MODES_FOR_AEC_PROGRAM = paymentOptionConfigs?.filter(pmode => pmode.value !== PAYMENT_TYPES.BALLOON);
  if (DealerPropertyHelper.isLeaseNotSupported() && isArcLiteProgram()) {
    return LEASE_DISABLED_PAYMENT_MODES_FOR_AEC_PROGRAM;
  }
  if (DealerPropertyHelper.isLeaseNotSupported()) {
    return LEASE_DISABLED_PAYMENT_MODES;
  }
  if (isArcLiteProgram()) {
    return PAYMENT_MODES_FOR_AEC_PROGRAM;
  }

  return paymentOptionConfigs;
};

export const TARGETING_OPTIONS = (dealTypes, paymentOptionConfigs, targetingParams, stockSubTypes) => ({
  [VEHICLE_CERTIFIED]: VEHICLE_CERTIFIED_STATUS,
  [OEM_CERTIFIED]: OEM_CERTIFIED_STATUS,
  [DEALER_CERTIFIED]: DEALER_CERTIFIED_STATUS,
  [CERTIFICATION_STATUS]: getCertificationStatus(),
  [TRADE_IN_ACV_RANGE]: [],
  [SELLING_PRICE_RANGE]: [],
  [INVOICE_PRICE_RANGE]: [],
  [PAYMENT_TYPE]: paymentModes(paymentOptionConfigs),
  [STOCK_TYPE]: isRRG() ? TARGETING_VALUES_OPTIONS_FOR_STOCK_TYPE_RRG : TARGETING_VALUES_OPTIONS_FOR_STOCK_TYPE,
  [STOCK_SUB_TYPE]: getStockSubTypeTargetingOptions(targetingParams, stockSubTypes),
  [DEAL_TYPE]: getDealTypesOptions(dealTypes),
  [VEHICLE_STATUS]: VEHICLE_CERTIFIED_STATUS,
  [F_N_I_PRODUCT_SELLING_PRICE_RANGE]: [],
  [DUE_BILL_SELLING_PRICE_RANGE]: [],
  [TRADE_INS]: getTargettingValuesForTradeIns(),
  [IN_STATE]: TARGETING_VALUES_OPTIONS_FOR_IN_STATE,
  [OUT_STATE]: TARGETING_VALUES_OPTIONS_FOR_OUT_STATE,
  [F_N_I_PRODUCT_COST_PRICE_RANGE]: [],
  [DUE_BILL_COST_PRICE_RANGE]: [],
  [BODY_CLASS]: TARGETING_VALUES_OPTIONS_FOR_BODY_CLASS,
  [TRADE_IN_OWNERSIP_TYPE]: TARGETING_VALUES_OPTIONS_FOR_TRADEIN_OWNERSHIP_TYPE,
  [STATES]: TARGETING_OPTIONS_FOR_STATES,
  [FUEL_TYPE]: EMPTY_ARRAY,
  [LICENSE_EXPIRATION_DATE_IN_DAYS]: EMPTY_STRING,
  [MODEL_YEAR]: EMPTY_STRING,
  [AGE]: EMPTY_ARRAY,
  [TRADE_IN_YEAR]: getYearOptions(1970, Number(getCurrentYear()) + 5),
  [COLLECT_ONLY_WHEN_COLLECTFEE_IS_ON]: TARGETING_VALUES_OPTIONS_COLLECT_ONLY_WHEN_COLLECTFEE_IS_ON,
  [LICENSE_EXPIRATION_DAYS_GREATER_THAN]: EMPTY_STRING,
  [LANGUAGE]: EMPTY_ARRAY,
  [APPLICANT_TYPE]: EMPTY_ARRAY,
  [VEHICLE_CATEGORY]: EMPTY_ARRAY,
});

export const ATTRIBUTE_OPTIONS_FOR_DEFAULT_NEW_SELLING_PRICE = [
  {
    label: __('Retail Price'),
    value: RETAIL,
  },
  {
    label: __('MSRP'),
    value: MSRP,
  },
  {
    label: __('Internet Price'),
    value: INTERNET_PRICE,
  },
];

export const ATTRIBUTE_OPTIONS_FOR_DEFAULT_USED_SELLING_PRICE = [
  {
    label: __('Retail Price'),
    value: RETAIL,
  },
  {
    label: __('Internet Price'),
    value: INTERNET_PRICE,
  },
];

export const ATTRIBUTE_OPTIONS_FOR_COMISSION_SPLIT_TYPES = [
  {
    label: __('Split Equally'),
    value: EQUAL_SPLIT,
  },
  {
    label: __('Only for Primary Sales Person'),
    value: ONLY_FOR_PRIMARY_SALESPERSON,
  },
];

export const DISPLAY_MODEL_SOURCE_OPTIONS = [
  {
    label: __('Display Model'),
    value: DISPLAY_MODEL,
  },
  {
    label: __('Best Style Name'),
    value: BEST_STYLE_NAME,
  },
];

export const COMISSION_SPLIT_OPTIONS_SALES_MANAGER = [
  {
    label: __('Split Equally'),
    value: EQUAL_SPLIT,
  },
  {
    label: __('Only for Primary Sales Manager'),
    value: ONLY_FOR_PRIMARY_ASSIGNEE,
  },
];

export const COMISSION_SPLIT_OPTIONS_FNI_MANAGER = [
  {
    label: __('Split Equally'),
    value: EQUAL_SPLIT,
  },
  {
    label: __('Only for Primary F&I Manager'),
    value: ONLY_FOR_PRIMARY_ASSIGNEE,
  },
];

export const COMISSION_SPLIT_OPTIONS_E_ADVISOR = [
  {
    label: __('Split Equally'),
    value: EQUAL_SPLIT,
  },
  {
    label: __('Only for Primary E-Advisor'),
    value: ONLY_FOR_PRIMARY_ASSIGNEE,
  },
];

export const DO_NOT_SPLIT = {
  label: __('Do not Split'),
  value: NO_SPLIT,
};

export const DEFAULT_AUTO_ROLL_CASH_DEFICIENCY_OPTIONS = [
  {
    label: __('Dealership'),
    value: AUTO_ROLL_DEFICIT_BEARER.DEALERSHIP,
  },
  {
    label: __('Customer'),
    value: AUTO_ROLL_DEFICIT_BEARER.CUSTOMER,
  },
];

export const COUNTRY_CODES = {
  INDIA: '+91',
  USA: '+1',
  CANADA: '+1',
  FRANCE: '+33',
};

export const COUNTRY_DIALING_CODE_OPTIONS = [
  {
    label: __('US / CANADA (+1)'),
    value: COUNTRY_CODES.USA,
  },
  {
    label: __('FRANCE (+33)'),
    value: COUNTRY_CODES.FRANCE,
  },
  {
    label: __('INDIA (+91)'),
    value: COUNTRY_CODES.INDIA,
  },
];

export const CALCULATION_PARAMETER_OPTIONS = [
  {
    label: __('Flat Amount'),
    value: CALCULATION_PARAMETERS.FLAT_AMT,
  },
  {
    label: __('Percentage of Selling Price'),
    value: CALCULATION_PARAMETERS.SELLING_PRICE_PCT,
  },
];

export const CALCULATION_PARAMETER_WITH_AMOUNT_FINANCED = [
  ...CALCULATION_PARAMETER_OPTIONS,
  {
    label: __('Percentage of Amount financed'),
    value: CALCULATION_PARAMETERS.AMOUNT_FINANCED_PCT,
  },
];
export const DEPOSIT_PRICE_TYPE_PARAMETER_OPTIONS = [
  {
    label: __('MSRP'),
    value: DEPOSIT_PRICE_TYPES.MSRP,
  },
  {
    label: __('Retail Price'),
    value: DEPOSIT_PRICE_TYPES.RETAIL_PRICE,
  },
];

function getDealerMonths() {
  const options = [];
  for (let i = 1; i <= 31; i += 1) {
    options.push({
      label: i,
      value: i,
    });
  }
  return options;
}

export const DEALER_MONTH_OPTIONS = getDealerMonths();

export const DEFAULT_REBATE_FIELDS = [
  {
    rebateField: REBATES_FIELDS.PROGRAM_NUMBER,
    displayName: __('Program Number'),
  },
  {
    rebateField: REBATES_FIELDS.PROGRAM_CODE,
    displayName: __('Program Code'),
  },
  {
    rebateField: REBATES_FIELDS.REBATE,
    displayName: __('Rebate'),
  },
  {
    rebateField: REBATES_FIELDS.CERTIFICATE_NUMBER,
    displayName: __('Certificate Number'),
  },
];

export const MANDATORY_TAXCODE_AFTER_DEAL_STATUS = [
  DEAL_STATUS.FINANCE_AND_INSURANCE,
  DEAL_STATUS.RECAP,
  DEAL_STATUS.DOCS_SIGNING,
  DEAL_STATUS.BOOKED,
  DEAL_STATUS.CLOSED_OR_SOLD,
  DEAL_STATUS.PRE_CLOSED,
  DEAL_STATUS.CONFIRMED,
];

export const DEMO_MILEAGE_ADJUSTMENT_TYPE_OPTIONS = [
  {
    label: __('Radio Button'),
    value: DEMO_MILEAGE_ADJUSTMENT_TYPES.RADIO_BUTTON,
  },
  {
    label: __('Checkbox'),
    value: DEMO_MILEAGE_ADJUSTMENT_TYPES.CHECKBOX,
  },
];

export const DEPOSIT_RADIO_OPTIONS = [
  {
    label: __('Fixed Amount'),
    value: DEPOSIT_TYPES.FIXED_AMOUNT,
  },
  {
    label: __('Percentage'),
    value: DEPOSIT_TYPES.PERCENTAGE,
  },
];

export const DEFAULT_HEADER_RADIO_VALUES = {
  CONTRACT_DATE: 'CONTRACT_DATE',
  SOLD_DATE: 'SOLD_DATE',
  DELIVERY_DATE_TIME: 'DELIVERY_DATE_TIME',
  FIRST_PAYMENT_DATE: 'FIRST_PAYMENT_DATE',
  INVOICE_DATE: 'INVOICE_DATE',
};

export const DEFAULT_HEADER_RADIOS = [
  {
    label: __('Contract Date'),
    value: DEFAULT_HEADER_RADIO_VALUES.CONTRACT_DATE,
  },
  {
    label: __('Sold Date'),
    value: DEFAULT_HEADER_RADIO_VALUES.SOLD_DATE,
  },
  {
    label: __('Delivery Date & Time'),
    value: DEFAULT_HEADER_RADIO_VALUES.DELIVERY_DATE_TIME,
  },
];
export const PASSCODES_VS_PERMISSIONS = {
  [UNLOCK_PIN_FOR_BOOKED_DEALS]: canEditPasscodeForUnlockDeal,
  [UNLOCK_PIN_FOR_CLOSED_DEALS]: canEditPasscodeForUnlockDeal,
  [DEAL_UNLOCK_PASSCODE_FOR_CLOSED_MONTHS_FOR_BOOKED_DEALS]: canEditPasscodeForUnlockDeal,
  [DEAL_UNLOCK_PASSCODE_FOR_CLOSED_MONTHS_FOR_CLOSED_DEALS]: canEditPasscodeForUnlockDeal,
  [DEAL_UNWIND_PASSCODE_FOR_BOOKED_DEALS]: canEditPasscodeForUnbookDeal,
  [DEAL_UNWIND_PASSCODE_FOR_CLOSED_DEALS]: canEditPasscodeForUnwindDeal,
};

export const ASSIGNEE_LIMITS_KEYS = {
  SALES_PERSON: 'salesPerson',
  SALES_MANAGER: 'salesManager',
  FNI_MANAGER: 'fniManager',
  HEAD_OF_BUSINESS: 'headOfBusiness',
  MANAGER: 'manager',
  CUSTOMER_CONSULTANT: 'customerConsultant',
  SELL_MY_CAR_EXPERT: 'smcExpert',
  HOST: 'host',
  BDC_USER: 'bdcUser',
  E_ADVISOR: 'eadvisor',
};

export const TAX_VS_DEALER_PROPERTY_MAP = {
  taxBreakups: () => !DealerPropertyHelper.isTaxBreakUpEnabled(),
};

export const CUSTOM_ACTION_TYPES = {
  ADD: 'ADD',
  DELETE: 'DELETE',
};

export const SALES_SETUP_BACKUP_KEYS = ['integrations', 'trimRequiredProviderIds', 'fnIConfigs'];

export const CREDIT_AGENCY_VS_FORMAT_REQUIRED_FOR_DEALER_PROVIDER_ID = {
  [INTEGRATION_KEYS.CREDIT_APP_ROUTE_ONE]: 'routeone',
  [INTEGRATION_KEYS.CREDIT_APP_CUDL]: 'cudl',
  [INTEGRATION_KEYS.VW_CREDIT]: 'vw',
  [INTEGRATION_KEYS.AUDI_FINANCIAL_SERVICES]: 'audi',
  [INTEGRATION_KEYS.DEALER_TRACK]: 'DealerTrack',
  [INTEGRATION_KEYS.CREDIT_APP_APP_ONE]: 'appone',
  [INTEGRATION_KEYS.CODE_WEAVERS]: 'codeweavers',
};

export const VALUES = 'values';

export const LOADING_MODELS = {
  TRUE: {
    [MODELS]: true,
  },
  FALSE: {
    [MODELS]: false,
  },
};

const DIGITAL_RETAILING_DEFAULT_ICO_PROVIDER_VALUES = {
  KBB_ICO: 'KBB_ICO',
  BB_ICO: 'BB_ICO',
};

export const DIGITAL_RETAILING_DEFAULT_ICO_PROVIDER_OPTIONS = [
  {
    label: __('Kelley Blue Book ICO'),
    value: DIGITAL_RETAILING_DEFAULT_ICO_PROVIDER_VALUES.KBB_ICO,
  },
  {
    label: __('Black Book ICO'),
    value: DIGITAL_RETAILING_DEFAULT_ICO_PROVIDER_VALUES.BB_ICO,
  },
];

const ALL_VEHICLE_TYPE_SELECT_OPTION = [{ label: __('All Vehicle Type'), value: VEHICLE_CATEGORIES.ALL_TYPE }];

const VEHICLE_TYPE_SELECT_OPTIONS = [
  { label: __('Car'), value: VEHICLE_CATEGORIES.CAR },
  { label: __('RV'), value: VEHICLE_CATEGORIES.RV },
];

export const TAB_KEYS = {
  GENERAL: 'GENERAL',
  DEFAULT_VALUES: 'DEFAULT_VALUES',
  WORKING_CASH: 'WORKING_CASH',
  INTEGRATION: 'INTEGRATION',
  STATE_FEE_TAX: 'STATE_FEE_TAX',
  DEAL_TEMPLATE: 'DEAL_TEMPLATE',
  COMPLIANCE: 'COMPLIANCE',
  TRADE_IN: 'TRADE_IN',
};

export const TABS = {
  GENERAL: 'general',
  DEFAULT_VALUES: 'defaultValues',
  WORKING_CASH: 'workingCash',
  INTEGRATION: 'integration',
  STATE_FEE_TAX: 'stateFeeTax',
  DEAL_TEMPLATE: 'dealTemplate',
  COMPLIANCE: 'compliance',
  TRADE_IN: 'tradeIn',
};

export const TAB_VS_TAB_ID = {
  [TABS.GENERAL]: '1',
  [TABS.DEFAULT_VALUES]: '2',
  [TABS.WORKING_CASH]: '4',
  [TABS.INTEGRATION]: '5',
  [TABS.STATE_FEE_TAX]: '6',
  [TABS.DEAL_TEMPLATE]: '7',
  [TABS.COMPLIANCE]: '8',
  [TABS.TRADE_IN]: '9',
};

export const DEAL_SETUP_TABS_VS_VEHICLE_TYPE_SELECTOR_OPTIONS = {
  [TAB_KEYS.GENERAL]: ALL_VEHICLE_TYPE_SELECT_OPTION,
  [TAB_KEYS.DEFAULT_VALUES]: ALL_VEHICLE_TYPE_SELECT_OPTION,
  [TAB_KEYS.WORKING_CASH]: ALL_VEHICLE_TYPE_SELECT_OPTION,
  [TAB_KEYS.INTEGRATION]: ALL_VEHICLE_TYPE_SELECT_OPTION,
  [TAB_KEYS.STATE_FEE_TAX]: VEHICLE_TYPE_SELECT_OPTIONS,
  [TAB_KEYS.DEAL_TEMPLATE]: ALL_VEHICLE_TYPE_SELECT_OPTION,
  [TAB_KEYS.COMPLIANCE]: ALL_VEHICLE_TYPE_SELECT_OPTION,
};

export const DEFAULT_VEHICLE_TYPE_VS_TABS = {
  [TAB_KEYS.GENERAL]: VEHICLE_CATEGORIES.ALL_TYPE,
  [TAB_KEYS.DEFAULT_VALUES]: VEHICLE_CATEGORIES.ALL_TYPE,
  [TAB_KEYS.WORKING_CASH]: VEHICLE_CATEGORIES.ALL_TYPE,
  [TAB_KEYS.INTEGRATION]: VEHICLE_CATEGORIES.ALL_TYPE,
  [TAB_KEYS.STATE_FEE_TAX]: VEHICLE_CATEGORIES.CAR,
  [TAB_KEYS.DEAL_TEMPLATE]: VEHICLE_CATEGORIES.ALL_TYPE,
  [TAB_KEYS.COMPLIANCE]: VEHICLE_CATEGORIES.ALL_TYPE,
};

export const configKeysToPickForModels = [
  SET_UP_KEYS.DEPOSIT_RULES,
  SET_UP_KEYS.feeConfigs,
  SET_UP_KEYS.DUE_BILLS,
  SET_UP_KEYS.COST_ADJUSTMENTS,
  SET_UP_KEYS.DEAL_POSTING_PACKS,
  SET_UP_KEYS.LENDERS,
];

export const GENERAL_TAB_SECTION_COMPONENTS = {
  DEAL_TYPES: 'DealTypes',
  DESKING_FIELDS_DRAGGABLE: 'DeskingFieldsDraggable',
  PAYMENT_OPTIONS: 'PaymentOptions',
  REBATES_FIELDS_SECTION: 'RebatesFields',
  DEAL_STATUS_SECTION: 'DealStatus',
  DEAL_STATUS_RULES: 'DealStatusRules',
  CUSTOM_STATUS: 'CustomStatus',
  FORM_CATEGORIES: 'FormCategories',
  DESCRIPTION_SWITCH: 'DescriptionSwitch',
  DEAL_PASSCODES: 'DealPasscode',
};

export const INTEGRATIONS_TAB_SECTION_COMPONENTS = {
  VEHICLE_BUILDER: 'vehicleBuilder',
  TAXES_AND_FEES: 'taxesAndFees',
  DOWNPAYMENTS: 'downPayments',
  SMS_REPORT: 'smsReport',
  CREDIT_BUREAU: 'creditBureau',
  CREDIT_APPLICATION: 'creditApplication',
  CONSUMER_CREDIT_APPLICATION: 'consumerCreditApplication',
  ECONTRACT: 'eContract',
  DOCUMENT_PUSH_TO_PARTNER: 'documentPushToPartner',
  MARKETING_PLATFORMS: 'marketingPlatforms',
  CRM_AND_INVENTORY_FEEDS: 'crmAndInventoryFeeds',
  PREMIUM_WEBHOOKS: 'premiumWebhooks',
  DMV: 'dmv',
  OEM_INTEGRATIONS: 'oemIntegrations',
  DEALER_LOYALTY_PROGRAMS: 'dealerLoyaltyPrograms',
  FNI_MENU: 'fniMenu',
  COMMISSIONS: 'commissions',
  REBATES: 'rebates',
  DUE_BILLS_ACCESSORIES: 'dueBillsAccessories',
  KEYPER: 'keyper',
  AUDIT: 'audit',
  CONCIERGE: 'concierge',
  CROSS_DEALERSHIP_INFORMATION: 'crossDealershipInformation',
  GM_VLS: 'gmVls',
  AUTO_INSURANCE_QUOTES: 'autoInsuranceQuotes',
  SETTINGS: 'settings',
};

export const TRADEIN_TAB_SECTION_COMPONENTS = {
  TRADE_APPRAISALS: 'tradeAppraisals',
  VEHICLE_HISTORY_REPORT: 'vehicleHistoryReport',
  USED_VEHICLE_VALUATION: 'usedVehicleValuation',
  DEFAULT_ICO_PROVIDER: 'defaultICOProvider',
  DEALER_TRADEIN_VALUATION: 'dealerTradeInValuation',
  DEFAULT_VALUATION_PROVIDER_DEALERSHIP_WEBSITE: 'dealershipWebsiteDefaultValuationProvider',
};

export const DEFAULT_VALUES_TAB_COMPONENTS = {
  DEFAULT_PAYMENT_FREQUENCY: 'DefaultPaymentFrequency',
  DAYS_TO_FIRST_PAYMENT_SECTION: 'DaysToFirstPayment',
  DEFAULT_SELLING_PRICE: 'defaultSellingPrice',
  DEALS: 'deals',
  ASSIGNEE_LIMITS: 'assigneeLimits',
  APR_CONFIG: 'aprConfig',
  DEALER_MONTH: 'dealerMonth',
  DISCOUNT_CAP: 'discountCab',
  CUSTOMER_VALUES: 'customerValues',
  ASSIGNEE_FOR_PERSONA: 'assigneeForPersona',
  PRINT_SETUP: 'printSetup',
  SCAN_SETUP: 'scanSetup',
  VEHICLE_WORKFLOW_OPTIONS: 'vehicleWorkFlowOptions',
  OTHERS: 'others',
  LEASE_COMPARISON: 'leaseComparison',
  DOWN_PAYMENT: 'downPayment',
  DEPOSIT_RULES: 'depositRules',
  FEES: 'fees',
  DUE_BILLS: 'dueBills',
  COST_ADJUSMENTS: 'costAdjustments',
  DEAL_POSTING_PACKS: 'dealPostingPacks',
  VENDOR_MANAGEMENT: 'vendorManagement',
  LENDERS: 'lenders',
  DISABLE_DEPOSIT: 'disableDeposit',
  VEHICLE_DELIVERY_CONFIGURATION: 'vehicleDeliveryConfiguration',
  DISABLE_DELIVERY_APPOINTMENT: 'disableDeliveryAppointment',
  DISCOUNT_MATRIX: 'discountBasedOnVehicleType',
  FINANCE_COMMISSION_GROSS_TYPE_OPTIONS: 'financeCommissionGrossType',
  VEHICLE_DEAL_HEADER_IDENTIFIER: 'vehicleDealHeaderIdentifier',
  DISABLE_TRADE_IN: 'disableTradeIn',
};

export const TAB_SECTIONS = {
  [TABS.GENERAL]: {
    DEFAULT: [
      GENERAL_TAB_SECTION_COMPONENTS.DEAL_TYPES,
      GENERAL_TAB_SECTION_COMPONENTS.DESKING_FIELDS_DRAGGABLE,
      GENERAL_TAB_SECTION_COMPONENTS.PAYMENT_OPTIONS,
      GENERAL_TAB_SECTION_COMPONENTS.REBATES_FIELDS_SECTION,
      GENERAL_TAB_SECTION_COMPONENTS.DEAL_STATUS_SECTION,
      GENERAL_TAB_SECTION_COMPONENTS.DEAL_STATUS_RULES,
      GENERAL_TAB_SECTION_COMPONENTS.CUSTOM_STATUS,
      GENERAL_TAB_SECTION_COMPONENTS.FORM_CATEGORIES,
      GENERAL_TAB_SECTION_COMPONENTS.DESCRIPTION_SWITCH,
      GENERAL_TAB_SECTION_COMPONENTS.DEAL_PASSCODES,
    ],
    INCHCAPE: [
      GENERAL_TAB_SECTION_COMPONENTS.DEAL_TYPES,
      GENERAL_TAB_SECTION_COMPONENTS.DESKING_FIELDS_DRAGGABLE,
      GENERAL_TAB_SECTION_COMPONENTS.PAYMENT_OPTIONS,
      GENERAL_TAB_SECTION_COMPONENTS.REBATES_FIELDS_SECTION,
      GENERAL_TAB_SECTION_COMPONENTS.DEAL_STATUS_SECTION,
      GENERAL_TAB_SECTION_COMPONENTS.DEAL_STATUS_RULES,
      GENERAL_TAB_SECTION_COMPONENTS.CUSTOM_STATUS,
      GENERAL_TAB_SECTION_COMPONENTS.FORM_CATEGORIES,
      GENERAL_TAB_SECTION_COMPONENTS.DEAL_PASSCODES,
    ],
    RRG: [
      GENERAL_TAB_SECTION_COMPONENTS.DEAL_TYPES,
      GENERAL_TAB_SECTION_COMPONENTS.DESKING_FIELDS_DRAGGABLE,
      GENERAL_TAB_SECTION_COMPONENTS.REBATES_FIELDS_SECTION,
      GENERAL_TAB_SECTION_COMPONENTS.PAYMENT_OPTIONS,
      GENERAL_TAB_SECTION_COMPONENTS.FORM_CATEGORIES,
      GENERAL_TAB_SECTION_COMPONENTS.DEAL_PASSCODES,
      GENERAL_TAB_SECTION_COMPONENTS.DEAL_STATUS_RULES,
    ],
  },
  [TABS.INTEGRATION]: {
    DEFAULT: [
      INTEGRATIONS_TAB_SECTION_COMPONENTS.VEHICLE_BUILDER,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.TAXES_AND_FEES,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.DOWNPAYMENTS,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.SMS_REPORT,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.CREDIT_BUREAU,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.CREDIT_APPLICATION,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.CONSUMER_CREDIT_APPLICATION,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.ECONTRACT,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.DOCUMENT_PUSH_TO_PARTNER,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.MARKETING_PLATFORMS,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.CRM_AND_INVENTORY_FEEDS,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.PREMIUM_WEBHOOKS,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.DMV,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.OEM_INTEGRATIONS,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.DEALER_LOYALTY_PROGRAMS,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.FNI_MENU,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.COMMISSIONS,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.REBATES,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.DUE_BILLS_ACCESSORIES,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.KEYPER,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.AUDIT,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.CONCIERGE,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.CROSS_DEALERSHIP_INFORMATION,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.GM_VLS,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.AUTO_INSURANCE_QUOTES,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.SETTINGS,
    ],
    INCHCAPE: [
      INTEGRATIONS_TAB_SECTION_COMPONENTS.VEHICLE_BUILDER,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.CREDIT_APPLICATION,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.AUDIT,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.CONCIERGE,
    ],
    RRG: [
      INTEGRATIONS_TAB_SECTION_COMPONENTS.VEHICLE_BUILDER,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.DOCUMENT_PUSH_TO_PARTNER,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.AUDIT,
      INTEGRATIONS_TAB_SECTION_COMPONENTS.CONCIERGE,
    ],
  },
  [TABS.TRADE_IN]: {
    DEFAULT: [
      TRADEIN_TAB_SECTION_COMPONENTS.TRADE_APPRAISALS,
      TRADEIN_TAB_SECTION_COMPONENTS.VEHICLE_HISTORY_REPORT,
      TRADEIN_TAB_SECTION_COMPONENTS.USED_VEHICLE_VALUATION,
      TRADEIN_TAB_SECTION_COMPONENTS.DEFAULT_ICO_PROVIDER,
      TRADEIN_TAB_SECTION_COMPONENTS.DEALER_TRADEIN_VALUATION,
      TRADEIN_TAB_SECTION_COMPONENTS.DEFAULT_VALUATION_PROVIDER_DEALERSHIP_WEBSITE,
    ],
    INCHCAPE: [TRADEIN_TAB_SECTION_COMPONENTS.TRADE_APPRAISALS, TRADEIN_TAB_SECTION_COMPONENTS.VEHICLE_HISTORY_REPORT],
  },
  [TABS.DEFAULT_VALUES]: {
    DEFAULT: [
      DEFAULT_VALUES_TAB_COMPONENTS.DEFAULT_PAYMENT_FREQUENCY,
      DEFAULT_VALUES_TAB_COMPONENTS.DAYS_TO_FIRST_PAYMENT_SECTION,
      DEFAULT_VALUES_TAB_COMPONENTS.DEFAULT_SELLING_PRICE,
      DEFAULT_VALUES_TAB_COMPONENTS.DEALS,
      DEFAULT_VALUES_TAB_COMPONENTS.ASSIGNEE_LIMITS,
      DEFAULT_VALUES_TAB_COMPONENTS.DISABLE_DEPOSIT,
      DEFAULT_VALUES_TAB_COMPONENTS.DEALER_MONTH,
      DEFAULT_VALUES_TAB_COMPONENTS.DISCOUNT_CAP,
      DEFAULT_VALUES_TAB_COMPONENTS.CUSTOMER_VALUES,
      DEFAULT_VALUES_TAB_COMPONENTS.ASSIGNEE_FOR_PERSONA,
      DEFAULT_VALUES_TAB_COMPONENTS.PRINT_SETUP,
      DEFAULT_VALUES_TAB_COMPONENTS.SCAN_SETUP,
      DEFAULT_VALUES_TAB_COMPONENTS.VEHICLE_WORKFLOW_OPTIONS,
      DEFAULT_VALUES_TAB_COMPONENTS.OTHERS,
      DEFAULT_VALUES_TAB_COMPONENTS.LEASE_COMPARISON,
      DEFAULT_VALUES_TAB_COMPONENTS.DOWN_PAYMENT,
      DEFAULT_VALUES_TAB_COMPONENTS.DEPOSIT_RULES,
      DEFAULT_VALUES_TAB_COMPONENTS.FEES,
      DEFAULT_VALUES_TAB_COMPONENTS.DUE_BILLS,
      DEFAULT_VALUES_TAB_COMPONENTS.COST_ADJUSMENTS,
      DEFAULT_VALUES_TAB_COMPONENTS.DEAL_POSTING_PACKS,
      DEFAULT_VALUES_TAB_COMPONENTS.VENDOR_MANAGEMENT,
      DEFAULT_VALUES_TAB_COMPONENTS.LENDERS,
    ],
    INCHCAPE: [
      DEFAULT_VALUES_TAB_COMPONENTS.DEALS,
      DEFAULT_VALUES_TAB_COMPONENTS.ASSIGNEE_LIMITS,
      DEFAULT_VALUES_TAB_COMPONENTS.DISABLE_DEPOSIT,
      DEFAULT_VALUES_TAB_COMPONENTS.DEALER_MONTH,
      DEFAULT_VALUES_TAB_COMPONENTS.DISABLE_DELIVERY_APPOINTMENT,
      // DEFAULT_VALUES_TAB_COMPONENTS.DISCOUNT_CAP,
      DEFAULT_VALUES_TAB_COMPONENTS.ASSIGNEE_FOR_PERSONA,
      DEFAULT_VALUES_TAB_COMPONENTS.PRINT_SETUP,
      DEFAULT_VALUES_TAB_COMPONENTS.SCAN_SETUP,
      DEFAULT_VALUES_TAB_COMPONENTS.VEHICLE_WORKFLOW_OPTIONS,
      DEFAULT_VALUES_TAB_COMPONENTS.OTHERS,
      DEFAULT_VALUES_TAB_COMPONENTS.DOWN_PAYMENT,
      DEFAULT_VALUES_TAB_COMPONENTS.FEES,
      DEFAULT_VALUES_TAB_COMPONENTS.COST_ADJUSMENTS,
      DEFAULT_VALUES_TAB_COMPONENTS.DUE_BILLS,
      DEFAULT_VALUES_TAB_COMPONENTS.DEAL_POSTING_PACKS,
      DEFAULT_VALUES_TAB_COMPONENTS.LENDERS,
      DEFAULT_VALUES_TAB_COMPONENTS.DISCOUNT_MATRIX,
      DEFAULT_VALUES_TAB_COMPONENTS.FINANCE_COMMISSION_GROSS_TYPE_OPTIONS,
      DEFAULT_VALUES_TAB_COMPONENTS.VEHICLE_DEAL_HEADER_IDENTIFIER,
      DEFAULT_VALUES_TAB_COMPONENTS.DISABLE_TRADE_IN,
    ],
    RRG: [
      DEFAULT_VALUES_TAB_COMPONENTS.DEALS,
      DEFAULT_VALUES_TAB_COMPONENTS.ASSIGNEE_LIMITS,
      DEFAULT_VALUES_TAB_COMPONENTS.DISABLE_DEPOSIT,
      DEFAULT_VALUES_TAB_COMPONENTS.DEALER_MONTH,
      DEFAULT_VALUES_TAB_COMPONENTS.DEFAULT_SELLING_PRICE,
      DEFAULT_VALUES_TAB_COMPONENTS.DISCOUNT_CAP,
      DEFAULT_VALUES_TAB_COMPONENTS.ASSIGNEE_FOR_PERSONA,
      DEFAULT_VALUES_TAB_COMPONENTS.PRINT_SETUP,
      DEFAULT_VALUES_TAB_COMPONENTS.SCAN_SETUP,
      DEFAULT_VALUES_TAB_COMPONENTS.VEHICLE_WORKFLOW_OPTIONS,
      DEFAULT_VALUES_TAB_COMPONENTS.OTHERS,
      DEFAULT_VALUES_TAB_COMPONENTS.DOWN_PAYMENT,
      DEFAULT_VALUES_TAB_COMPONENTS.DEPOSIT_RULES,
      DEFAULT_VALUES_TAB_COMPONENTS.FEES,
      DEFAULT_VALUES_TAB_COMPONENTS.DUE_BILLS,
      DEFAULT_VALUES_TAB_COMPONENTS.COST_ADJUSMENTS,
      DEFAULT_VALUES_TAB_COMPONENTS.DEAL_POSTING_PACKS,
      DEFAULT_VALUES_TAB_COMPONENTS.LENDERS,
      DEFAULT_VALUES_TAB_COMPONENTS.DISABLE_TRADE_IN,
    ],
  },
};

export const getDefaultHeaderRadios = () => {
  if (isInchcape()) {
    return [
      {
        label: __('Sold Date'),
        value: DEFAULT_HEADER_RADIO_VALUES.SOLD_DATE,
      },
      {
        label: __('Delivery Date & Time'),
        value: DEFAULT_HEADER_RADIO_VALUES.DELIVERY_DATE_TIME,
      },
    ];
  }

  if (isRRG()) {
    return [
      {
        label: __('Sold Date'),
        value: DEFAULT_HEADER_RADIO_VALUES.SOLD_DATE,
      },
      {
        label: __('Delivery Date & Time'),
        value: DEFAULT_HEADER_RADIO_VALUES.DELIVERY_DATE_TIME,
      },
      {
        label: __('Invoiced Date'),
        value: DEFAULT_HEADER_RADIO_VALUES.INVOICE_DATE,
      },
    ];
  }

  return [
    {
      label: __('Contract Date'),
      value: DEFAULT_HEADER_RADIO_VALUES.CONTRACT_DATE,
    },
    {
      label: __('Sold Date'),
      value: DEFAULT_HEADER_RADIO_VALUES.SOLD_DATE,
    },
    {
      label: __('Delivery Date & Time'),
      value: DEFAULT_HEADER_RADIO_VALUES.DELIVERY_DATE_TIME,
    },
  ];
};

export const DELIVERY_FEE_CONSTANTS = {
  FLAT_PRICE: 'FLAT_PRICE',
  CALCULATED_PRICE: 'CALCULATED_PRICE',
};

export const DELIVERY_FEE_TYPES = [
  {
    label: __('Flat Price'),
    value: DELIVERY_FEE_CONSTANTS.FLAT_PRICE,
  },
  {
    label: __('Calculated Price'),
    value: DELIVERY_FEE_CONSTANTS.CALCULATED_PRICE,
  },
];

export const VEHICLE_DELIVERY_CONFIGURATION_KEYS = {
  removeDeliveryDate: 'daysBetweenPaymentandearliestRemoteDeliveryDate',
  beforeDeliveryHours: 'changesAllowedToRemoteDeliveryUpToXHoursBeforeDelivery',
  allowDeliveryOutsideTheCountry: 'allowDeliveryOutsideTheCountry',
};

export const VEHICLE_DELIVERY_CONFIGURATION_ERROR_HONDA = {
  [VEHICLE_DELIVERY_CONFIGURATION_KEYS.removeDeliveryDate]: __(
    'Dealership preparation time entered should be a valid value less than 7 days'
  ),
};

export const VEHICLE_DELIVERY_CONFIGURATION_ERROR = {
  [VEHICLE_DELIVERY_CONFIGURATION_KEYS.removeDeliveryDate]: __(
    'Please enter valid Days between Payment and earliest Remote Delivery Date'
  ),
  [VEHICLE_DELIVERY_CONFIGURATION_KEYS.beforeDeliveryHours]: __(
    'Please enter valid Changes allowed to Remote Delivery up to X hours before delivery'
  ),
};

const checkIsFieldPresentBasedOnProgram = defaultMemoize(fieldId => {
  if (isInchcape()) {
    return _includes(
      [
        SET_UP_KEYS.CUSTOMER_CASH,
        SET_UP_KEYS.ANNUAL_MILES,
        SET_UP_KEYS.DEFAULT_RESERVATION_AMOUNT,
        ASSIGNEE_LIMITS_KEYS.HEAD_OF_BUSINESS,
        ASSIGNEE_LIMITS_KEYS.MANAGER,
        ASSIGNEE_LIMITS_KEYS.CUSTOMER_CONSULTANT,
        ASSIGNEE_LIMITS_KEYS.SELL_MY_CAR_EXPERT,
        ASSIGNEE_LIMITS_KEYS.HOST,
        ASSIGNEE_LIMITS_KEYS.BDC_USER,
        SET_UP_KEYS.DEAL_SHEET_COUNT,
        SET_UP_KEYS.RECAP_SHEET_COUNT,
        SET_UP_KEYS.PURCHASE_ORDER_FORM_COUNT,
        SET_UP_KEYS.SALES_ORDER_FORM_COUNT,
        SET_UP_KEYS.DOUBLE_SIDED_PRINT,
        LOCATION_WORKFLOW_KEYS.ENABLE_VEHICLE_LOCATION_WORKFLOW,
        SET_UP_KEYS.AUTO_UPDATE_COST_ADJ_RO,
        SET_UP_KEYS.AUTO_UPDATE_COST_ADJ_PO,
        SET_UP_KEYS.VOLUME_BASED_COMMISSION,
        SET_UP_KEYS.AUTO_ARCHIVE_SHEETS,
        ...(DealerPropertyHelper.isCashieringInvoiceLevelEnabled()
          ? []
          : [SET_UP_KEYS.ALLOW_PAYMENT_BEYOND_DUE_AMOUNT, SET_UP_KEYS.ALLOW_REFUND_BEYOND_REFUND_AMOUNT]),
        SET_UP_KEYS.TRADE_IN_COUNT,
        SET_UP_KEYS.DEFAULT_HEADER_DATE,
        SET_UP_KEYS.NUMBER_OF_DAYS_BEFORE_LAST_ACTIVITY,
        SET_UP_KEYS.DEAL_STATUS_FOR_LAST_ACTIVITY,
        SET_UP_KEYS.DISABLE_DELIVERY_APPOINTMENT_STATUS,
        SET_UP_KEYS.SHOW_CRM_TASKS_IN_DEAL_LIST,
        SET_UP_KEYS.QUOTE_EXPIRY_DURATION,
        SET_UP_KEYS.FINANCE_COMMISSION_GROSS_TYPE,
        SET_UP_KEYS.DEFAULT_DELIVERY_FEE_TYPE,
        SET_UP_KEYS.VEHICLE_DEAL_HEADER_IDENTIFIER,
        SET_UP_KEYS.ENABLE_ROLL_PAYMENT,
        SET_UP_KEYS.DISABLE_TRADE_IN,
        SET_UP_KEYS.SHOW_EMI_FOR_PRODUCTS_AND_OTHER_BREAKUP,
        SET_UP_KEYS.CAPTURE_SIGNATURE_TIMESTAMP,
        SET_UP_KEYS.MANUAL_DEAL_PAUSE,
      ],
      fieldId
    );
  }
  if (isRRG()) {
    return _includes(
      [
        SET_UP_KEYS.CUSTOMER_CASH,
        SET_UP_KEYS.ANNUAL_MILES,
        SET_UP_KEYS.DEFAULT_RESERVATION_AMOUNT,
        SET_UP_KEYS.DEFAULT_DEPOSIT_VAT_INFO,
        ASSIGNEE_LIMITS_KEYS.SALES_PERSON,
        ASSIGNEE_LIMITS_KEYS.SALES_MANAGER,
        ASSIGNEE_LIMITS_KEYS.E_ADVISOR,
        SET_UP_KEYS.RECAP_SHEET_COUNT,
        SET_UP_KEYS.DOUBLE_SIDED_PRINT,
        LOCATION_WORKFLOW_KEYS.USE_DEALERSHIP_ADDRESS_INFORMATION,
        SET_UP_KEYS.ALLOW_MULTIPLE_RESERVATIONS,
        SET_UP_KEYS.AUTO_CAL_COMMISSION_FOR_BOOKED_CLOSED_DEALS,
        SET_UP_KEYS.ALWAYS_SHOW_APR_ON_DESKING,
        SET_UP_KEYS.AUTO_UPDATE_COST_ADJ_RO,
        SET_UP_KEYS.AUTO_UPDATE_COST_ADJ_PO,
        SET_UP_KEYS.VOLUME_BASED_COMMISSION,
        SET_UP_KEYS.AUTO_ARCHIVE_SHEETS,
        SET_UP_KEYS.ALLOW_PAYMENT_BEYOND_DUE_AMOUNT,
        SET_UP_KEYS.ALLOW_REFUND_BEYOND_REFUND_AMOUNT,
        SET_UP_KEYS.COMMISSION_SPLIT_OPTIONS_SALES_PERSON,
        SET_UP_KEYS.COMMISSION_SPLIT_OPTIONS_SALES_MANAGER,
        SET_UP_KEYS.TRADE_IN_OWNER_TYPE,
        SET_UP_KEYS.DEFAULT_UNIT_COUNT_SPLIT_SALES_PERSON,
        SET_UP_KEYS.DEFAULT_UNIT_COUNT_SPLIT_SALES_MANAGER,
        SET_UP_KEYS.DEFAULT_HEADER_DATE,
        SET_UP_KEYS.DEFAULT_DEPOSIT_AMOUNT_PRODUCT_CLASSIFICATION,
        SET_UP_KEYS.DEFAULT_NEW_SELLING_PRICE,
        SET_UP_KEYS.DEFAULT_OLD_SELLING_PRICE,
        SET_UP_KEYS.DEFAULT_DEMO_SELLING_PRICE,
        SET_UP_KEYS.TRADE_IN_COUNT,
        SET_UP_KEYS.DEFAULT_UNIT_COUNT_SPLIT_E_ADVISOR,
        SET_UP_KEYS.COMMISSION_SPLIT_OPTIONS_E_ADVISOR,
        SET_UP_KEYS.DEFAULT_DEPOSIT_ACCOUNTING_GROUP,
        SET_UP_KEYS.DEFAULT_DELIVERY_FEE_TYPE,
        SET_UP_KEYS.DISABLE_TRADE_IN,
        SET_UP_KEYS.MANUAL_DEAL_PAUSE,
      ],
      fieldId
    );
  }

  return _includes(
    [
      SET_UP_KEYS.DAYS_TO_FIRST_PAYMENT,
      SET_UP_KEYS.LOAN_BUY_RATE_FOR_NEW,
      SET_UP_KEYS.LOAN_BUY_RATE_FOR_USED,
      SET_UP_KEYS.ANNUAL_MILES,
      SET_UP_KEYS.DEFAULT_COUNTRY_DIALING_CODE,
      ASSIGNEE_LIMITS_KEYS.SALES_PERSON,
      ASSIGNEE_LIMITS_KEYS.FNI_MANAGER,
      ASSIGNEE_LIMITS_KEYS.SALES_MANAGER,
      SET_UP_KEYS.DEAL_SHEET_COUNT,
      SET_UP_KEYS.COVER_SHEET_COUNT,
      SET_UP_KEYS.RECAP_SHEET_COUNT,
      SET_UP_KEYS.DOUBLE_SIDED_PRINT,
      LOCATION_WORKFLOW_KEYS.USE_DEALERSHIP_ADDRESS_INFORMATION,
      LOCATION_WORKFLOW_KEYS.ENABLE_VEHICLE_LOCATION_WORKFLOW,
      SET_UP_KEYS.ALLOW_MULTIPLE_RESERVATIONS,
      SET_UP_KEYS.AUTO_CAL_COMMISSION_FOR_BOOKED_CLOSED_DEALS,
      SET_UP_KEYS.USE_DEALER_ZIP_MARKET_ID,
      SET_UP_KEYS.ENABLE_DEFERRED_PAYMENT_OPTIONS,
      SET_UP_KEYS.ASSIGN_SALES_MANAGER_FI_STATUS_UPDATE,
      SET_UP_KEYS.SHOW_AUTO_ROLL_CASH_DEFICIENCY_POPUP,
      SET_UP_KEYS.AUTO_ROLL_0_APR,
      SET_UP_KEYS.SHOW_HST_SEPARATELY,
      SET_UP_KEYS.ALWAYS_SHOW_APR_ON_DESKING,
      SET_UP_KEYS.REDUCE_INTEREST_FOR_EARLY_PAYMENT,
      SET_UP_KEYS.ADJUST_SELLING_PRICE_BASED_ON_VEHICLE_GROSS,
      SET_UP_KEYS.AUTO_UPDATE_COST_ADJ_RO,
      SET_UP_KEYS.AUTO_UPDATE_COST_ADJ_PO,
      SET_UP_KEYS.ENABLE_DEAL_RECOMMENDATION,
      SET_UP_KEYS.ENABLE_VI_RECOMMENDATION,
      SET_UP_KEYS.VOLUME_BASED_COMMISSION,
      SET_UP_KEYS.USE_CUSTOMER_TIER_FOR_LOAN_APR,
      SET_UP_KEYS.USE_HARD_ADD_NUMBER,
      SET_UP_KEYS.INCLUDE_EXPIRED_PROGRAMS,
      SET_UP_KEYS.MULTI_VEHICLE_DESKING,
      SET_UP_KEYS.PRINT_ZERO_PRICE_PRODUCTS_IN_CONTRACT,
      SET_UP_KEYS.ENABLE_PRE_CLOSE_WORKFLOW,
      SET_UP_KEYS.AUTO_ARCHIVE_SHEETS,
      SET_UP_KEYS.SHOW_ACCOUNTING_DATE,
      SET_UP_KEYS.ENABLE_EFFECTIVE_INTEREST_RATE,
      SET_UP_KEYS.SHOW_MAX_RESIDUAL_MSRP,
      SET_UP_KEYS.ADD_TRADEIN_VEHICLE_TO_VI_ON_DEAL_CONFIRM,
      SET_UP_KEYS.ALLOW_PAYMENT_BEYOND_DUE_AMOUNT,
      SET_UP_KEYS.ALLOW_REFUND_BEYOND_REFUND_AMOUNT,
      SET_UP_KEYS.CREATE_APPOINTMENTS_FROM_DEALS,
      SET_UP_KEYS.VEHICLE_CHANGE_SELLING_PRICE__UPDATE,
      SET_UP_KEYS.SHOW_ASSUMPTIVE_PAY_OFF_POP_UP,
      SET_UP_KEYS.ENABLE_CTA_RDR_EVENT,
      SET_UP_KEYS.ENABLE_CUSTOMER_CASH_PERCENTAGE,
      SET_UP_KEYS.DEDUCT_CUSTOMER_CASH_FROM_AMOUNT_FINANCED_CASH_DEAL,
      SET_UP_KEYS.ADD_INVOICE_TO_DEAL_JACKET,
      SET_UP_KEYS.REMOVE_DEFAULT_OPTIONS,
      SET_UP_KEYS.DEFAULT_AUTO_ROLL_CASH_DEFICIENCY,
      SET_UP_KEYS.COMMISSION_SPLIT_OPTIONS_SALES_PERSON,
      SET_UP_KEYS.COMMISSION_SPLIT_OPTIONS_SALES_MANAGER,
      SET_UP_KEYS.COMMISSION_SPLIT_OPTIONS_FI_MANAGER,
      SET_UP_KEYS.NO_OF_DAYS_TO_LINK_TO_DEAL,
      SET_UP_KEYS.TRADE_IN_OWNER_TYPE,
      SET_UP_KEYS.TRADE_IN_COUNT,
      SET_UP_KEYS.VEHICLE_SUBTYPES_ELIGIBLE_FOR_NEW_VEHICLE_PROGRAM,
      SET_UP_KEYS.ROLE_IDS,
      SET_UP_KEYS.VIEW_DEMO_MILEAGE_ADJUSTMENT_TYPE_AS,
      SET_UP_KEYS.RELEASE_VEHICLE_FROM_HOLD_IN_DAYS,
      SET_UP_KEYS.DEFAULT_UNIT_COUNT_SPLIT_SALES_PERSON,
      SET_UP_KEYS.DEFAULT_UNIT_COUNT_SPLIT_SALES_MANAGER,
      SET_UP_KEYS.DEFAULT_UNIT_COUNT_SPLIT_FI_MANAGER,
      SET_UP_KEYS.DISPLAY_MODEL_SOURCE,
      SET_UP_KEYS.DEFAULT_HEADER_DATE,
      SET_UP_KEYS.DEFAULT_NEW_SELLING_PRICE,
      SET_UP_KEYS.DEAL_STATUS_AFTER_WHICH_TAX_CODE_IS_MANDATORY,
      SET_UP_KEYS.DEFAULT_OLD_SELLING_PRICE,
      SET_UP_KEYS.MANUAL_DEAL_PAUSE,
    ],
    fieldId
  );
});

const checkIsFieldPresentBasedOnConditions = defaultMemoize(fieldId => {
  const fieldVsConditions = {
    [SET_UP_KEYS.AUTO_CAL_COMMISSION_FOR_BOOKED_CLOSED_DEALS]: !isArcLiteProgram(),
    [SET_UP_KEYS.SHOW_AUTO_ROLL_CASH_DEFICIENCY_POPUP]: !isCanadaDealer(),
    [SET_UP_KEYS.SHOW_HST_SEPARATELY]: isCanadaDealer(),
    [SET_UP_KEYS.DEFAULT_AUTO_ROLL_CASH_DEFICIENCY]: !isCanadaDealer(),
    [SET_UP_KEYS.AUTO_ROLL_0_APR]:
      !DealerPropertyHelper.isCalcEngineEnabled() || DealerPropertyHelper.isGalaxyEnabled(),
    [SET_UP_KEYS.VOLUME_BASED_COMMISSION]: !isArcLiteProgram(),
    [SET_UP_KEYS.ENABLE_PRE_CLOSE_WORKFLOW]: !DealerPropertyHelper.isDealStatusConfigEnabled(),
    [SET_UP_KEYS.SHOW_ACCOUNTING_DATE]: !isArcLiteProgram(),
    [SET_UP_KEYS.REMOVE_DEFAULT_OPTIONS]: isCanadaDealer(),
    [SET_UP_KEYS.COMMISSION_SPLIT_OPTIONS_SALES_PERSON]: !isArcLiteProgram(),
    [SET_UP_KEYS.COMMISSION_SPLIT_OPTIONS_SALES_MANAGER]: !isArcLiteProgram(),
    [SET_UP_KEYS.COMMISSION_SPLIT_OPTIONS_FI_MANAGER]: !isArcLiteProgram(),
    [SET_UP_KEYS.CREATE_APPOINTMENTS_FROM_DEALS]: DealerPropertyHelper.isCRMEnabled(),
    [SET_UP_KEYS.ADD_TRADEIN_VEHICLE_TO_VI_ON_DEAL_CONFIRM]: !isArcLiteProgram(),
    [SET_UP_KEYS.ENABLE_DEAL_RECOMMENDATION]: DealerPropertyHelper.isBudgetBasedRecommendationsEnabled(),
    [SET_UP_KEYS.ENABLE_VI_RECOMMENDATION]: DealerPropertyHelper.isBudgetBasedRecommendationsEnabled(),
    [SET_UP_KEYS.ENABLE_CUSTOMER_CASH_PERCENTAGE]: DealerPropertyHelper.isRVDealerEnabled(),
    [SET_UP_KEYS.DEFAULT_DELIVERY_FEE_TYPE]: isInchcape() || isRRG(),
    [SET_UP_KEYS.ENABLE_ROLL_PAYMENT]: isInchcape(),
    [SET_UP_KEYS.ENABLE_CTA_RDR_EVENT]: !isArcLiteProgram(),
  };

  return fieldId in fieldVsConditions ? fieldVsConditions[fieldId] : true;
});

export const checkIsFieldIsApplicable = defaultMemoize(fieldId => {
  const fieldPresentBasedOnProgram = checkIsFieldPresentBasedOnProgram(fieldId);
  const fieldPresentBasedOnConditions = checkIsFieldPresentBasedOnConditions(fieldId);
  return fieldPresentBasedOnProgram && fieldPresentBasedOnConditions;
});

export const FINANCE_COMMISSION_GROSS_TYPE_OPTIONS = [
  {
    label: __('Front'),
    value: GROSS_TYPES.FRONT,
  },
  {
    label: __('Back'),
    value: GROSS_TYPES.BACK,
  },
];

export const VEHICLE_DEAL_HEADER_IDENTIFIER_OPTIONS = [
  {
    label: __('VIN #'),
    value: DEAL_HEADER_TYPES.VIN,
  },
  {
    label: __('Registration Number'),
    value: DEAL_HEADER_TYPES.LICENSE_PLATE_NUMBER,
  },
  {
    label: __('Stock Number'),
    value: DEAL_HEADER_TYPES.STOCK_NUMBER,
  },
];

export const LOAN_BUY_RATE_TYPE = {
  AVERAGE: 'AVERAGE',
  DEFAULT: 'DEFAULT',
};

export const LOAN_BUY_RATE_TYPE_OPTIONS = [
  {
    label: __('Default Rate'),
    value: LOAN_BUY_RATE_TYPE.DEFAULT,
  },
  {
    label: __('Average Rate '),
    value: LOAN_BUY_RATE_TYPE.AVERAGE,
  },
];

export const VEHILCE_TYPES = {
  NEW: 'NEW',
  USED: 'USED',
};

export const VEHICLE_TYPE_VS_LABEL = {
  [VEHILCE_TYPES.NEW]: __('New'),
  [VEHILCE_TYPES.USED]: __('Used'),
};

export const VEHICLES_TYPES_LABELS = () => ({
  [VEHILCE_TYPES.NEW]: isUSDealer() ? __('New Vehicle') : __('Loan BuyRate for New Vehicle'),
  [VEHILCE_TYPES.USED]: isUSDealer() ? __('Used Vehicle') : __('Loan BuyRate for Used Vehicle'),
});

export const LAST_30_DAYS = 'LAST_30_DAYS';

export const AVERAGE_APR_KEYS_VS_VALUE_MAP = {
  LAST_30_DAYS: 30,
  LAST_45_DAYS: 45,
  LAST_60_DAYS: 60,
  LAST_90_DAYS: 90,
};

export const AVERAGE_APR_TERMS = {
  LAST_30_DAYS: 'LAST_30_DAYS',
  LAST_45_DAYS: 'LAST_45_DAYS',
  LAST_60_DAYS: 'LAST_60_DAYS',
  LAST_90_DAYS: 'LAST_90_DAYS',
};

export const AVERAGE_APR_VALUES_VS_KEY_MAP = _invert(AVERAGE_APR_KEYS_VS_VALUE_MAP);

export const AVERAGE_APR_LABELS_MAP = {
  LAST_30_DAYS: __('30 Days'),
  LAST_45_DAYS: __('45 Days'),
  LAST_60_DAYS: __('60 Days'),
  LAST_90_DAYS: __('90 Days'),
};

export const AVERAGE_APR_OPERATOR = {
  NO_UPPER_LIMIT: 'NO_UPPER_LIMIT',
  NO_LOWER_LIMIT: 'NO_LOWER_LIMIT',
  BOTH_LIMITS: 'BOTH_LIMITS',
  NO_LIMITS: 'NO_LIMITS',
};

export const AVERAGE_APR_OPERATOR_VS_LABEL = {
  NO_UPPER_LIMIT: __('No Upper Limit'),
  NO_LOWER_LIMIT: __('No Lower Limit'),
  BOTH_LIMITS: __('Both Limits'),
  NO_LIMITS: __('No Limits'),
};

export const MODEL_MFR_CODES_VALUES = [MODELS, MANUFACTURE_MODEL_CODE];
