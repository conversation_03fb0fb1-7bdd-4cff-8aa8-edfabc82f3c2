import { DEFAULT_NEW_SELLING_PRICE } from '@tekion/tekion-base/marketScan/constants/constants';

import {
  UNLOCK_PIN_FOR_BOOKED_DEALS,
  UNLOCK_PIN_FOR_CLOSED_DEALS,
  DEAL_UNLOCK_PASSCODE_FOR_CLOSED_MONTHS_FOR_BOOKED_DEALS,
  DEAL_UNLOCK_PASSCODE_FOR_CLOSED_MONTHS_FOR_CLOSED_DEALS,
  DEAL_UNWIND_PASSCODE_FOR_BOOKED_DEALS,
  DEAL_UNWIND_PASSCODE_FOR_CLOSED_DEALS,
  DRS_FNI_MENU_DEAL_SWITCH_PASSCODE,
} from '@tekion/tekion-base/constants/deal/passCodes';

export const ACTION_TYPES = {
  INIT: 'INIT',
  GET_SETUP_INFO: 'GET_SETUP_INFO',
  SAVE: 'SAVE',
  <PERSON><PERSON><PERSON>_DEAL_TYPE_INFO: 'CHANGE_DEAL_TYPE_INFO',
  SET_DESKING_FIELDS_ORDER_INFO: 'SET_DESKING_FIELDS_ORDER_INFO',
  SET_DESKING_FIELDS_INFO: 'SET_DESKING_FIELDS_INFO',
  CHANGE_REBATE_CONFIG: 'CHANGE_REBATE_CONFIG',
  CHANGE_DEAL_STATUS_INFO: 'CHANGE_DEAL_STATUS_INFO',
  CHANGE_SALES_SETUP_INFO: 'CHANGE_SALES_SETUP_INFO',
  CHANGE_DEAL_STATUS_RULE: 'CHANGE_DEAL_STATUS_RULE',
  CHANGE_CUSTOM_STATUS_INFO: 'CHANGE_CUSTOM_STATUS_INFO',
  CHANGE_CUSTOM_FORM_CATEGORIES: 'CHANGE_CUSTOM_FORM_CATEGORIES',
  CHANGE_DEAL_CONFIG: 'CHANGE_DEAL_CONFIG',
  CHANGE_CALCULATION_PARAMETER: 'CHANGE_CALCULATION_PARAMETER',
  ADD_NEW_CONFIG: 'ADD_NEW_CONFIG',
  REMOVE_CONFIG: 'REMOVE_CONFIG',
  CHANGE_CONFIG_NAME: 'CHANGE_CONFIG_NAME',
  ON_DOWN_PAYMENT_EDIT: 'ON_DOWN_PAYMENT_EDIT',
  CHANGE_TARGETTING_PARAMS: 'CHANGE_TARGETTING_PARAMS',
  ADD_TARGETING_PARAM: 'ADD_TARGETING_PARAM',
  REMOVE_TARGETING_PARAM: 'REMOVE_TARGETING_PARAM',
  CHANGE_DEPOSIT_RULE: 'CHANGE_DEPOSIT_RULE',
  ADD_APPLICATION_CRITERION: 'ADD_APPLICATION_CRITERION',
  REMOVE_APPLICATION_CRITERION: 'REMOVE_APPLICATION_CRITERION',
  CHANGE_APPLICATION_CRITERION: 'CHANGE_APPLICATION_CRITERION',
  GET_COUNTIES_AND_CITIES: 'GET_COUNTIES_AND_CITIES',
  CHANGE_WORKING_CASH_SETTINGS: 'CHANGE_WORKING_CASH_SETTINGS',
  CHANGE_STATE_TAX_OPTIONS: 'CHANGE_STATE_TAX_OPTIONS',
  CHANGE_STATE_TAX_PERCENTAGE_VALUE: 'CHANGE_STATE_TAX_PERCENTAGE_VALUE',
  CHANGE_STATE_FEE_CONFIG: 'CHANGE_STATE_FEE_CONFIG',
  CHANGE_STATE_FEE_VALUE: 'CHANGE_STATE_FEE_VALUE',
  SPLIT_TAX_CONFIG: 'SPLIT_TAX_CONFIG',
  MERGE_TAX_CONFIG: 'MERGE_TAX_CONFIG',
  UPDATE_GENERAL_CONFIG: 'UPDATE_GENERAL_CONFIG',
  UPDATE_GENERAL_FEE_CONFIG: 'UPDATE_GENERAL_FEE_CONFIG',
  CHANGE_FEES_ACROSS_STATES: 'CHANGE_FEES_ACROSS_STATES',
  CHANGE_RECIPROCAL_TAX_OPTION: 'CHANGE_RECIPROCAL_TAX_OPTION',
  CHANGE_RECIPROCAL_TAX_VALUE: 'CHANGE_RECIPROCAL_TAX_VALUE',
  CHANGE_RECIPROCAL_TAX_CONFIG: 'CHANGE_RECIPROCAL_TAX_CONFIG',
  ON_RECIPROCAL_DUPLICATE: 'ON_RECIPROCAL_DUPLICATE',
  ON_RECIPROCAL_DELETE: 'ON_RECIPROCAL_DELETE',
  ON_RECIPROCAL_EDIT: 'ON_RECIPROCAL_EDIT',
  ON_RECIPROCAL_TITLE_TEXT_CHANGE: 'ON_RECIPROCAL_TITLE_TEXT_CHANGE',
  SET_DEALER_STATE_CODE: 'SET_DEALER_STATE_CODE',
  SET_DEALER_MARKET_ID: 'SET_DEALER_MARKET_ID',
  UPDATE_OTHER_TAX_CONFIG: 'UPDATE_OTHER_TAX_CONFIG',
  FEE_BULK_LOOKUP: 'FEE_BULK_LOOKUP',
  ON_FEE_SEARCH: 'ON_FEE_SEARCH',
  SET_TEMPLATE_DETAILS: 'SET_TEMPLATE_DETAILS',
  SET_TEMPLATE_NAME: 'SET_TEMPLATE_NAME',
  CHNAGE_FEE_CODES: 'CHNAGE_FEE_CODES',
  SHOW_DEALER_PROPERTY_ENABLED_FIELDS: 'SHOW_DEALER_PROPERTY_ENABLED_FIELDS',
};

export const DOCUMENT_PUSH_PARTNER_KEYS = {
  ID: 'id',
  KEY: 'key',
  NAME: 'name',
  DEFAULT: 'default',
  VALUE: 'value',
  MANUALLYADDED: 'manuallyAdded',
  ENABLED: 'enabled',
};

export const EXTRA_FEE_KEYS = {
  WAIVE_REGISTRATION_FEE: 'waiveRegistrationFee',
};

export const EXTRA_FEES = [EXTRA_FEE_KEYS.WAIVE_REGISTRATION_FEE];

export const FEE_OPTIONS_FOR_TARGETING = [
  {
    label: 'Calculate Registration Fee',
    value: 'calculateRegistrationFee',
    extraKeys: [EXTRA_FEE_KEYS.WAIVE_REGISTRATION_FEE],
    infoText: null,
  },
  {
    label: 'Calculate Registration Fee for Used Cars',
    value: 'usedVehicleTransferCurrentRegistration',
    extraKeys: [],
    infoText: __('California State Only'),
  },
];

export const STATE_FEE_TAX_INFO_KEYS = {
  GENERAL_CONFIG: 'generalConfig',
  STATE_FEE_CONFIG_V2: 'stateFeeConfigV2',
  STATE_TAX_CONFIG_V2: 'stateTaxConfigV2',
  GENERAL_FEE_CONFIG: 'generalFeeConfig',
  CUSTOMER_CONFIG: 'customerConfig',
  RECIPROCAL_TAX_CONFIG: 'reciprocalTaxConfig',
};

export const SALES_SETUP_TAXES_FEES_AND_TAX_PERCENTAGES_BACKUP_KEYS = [
  'reciprocalTaxConfig',
  'stateFeeConfigV2',
  'stateTaxConfigV2',
];

const FNNI_CONFIGS = 'fnIConfigs';
const FEE_CONFIGS = 'feeConfigs';
const DOWNPAYMENTS_CONFIGS = 'downPaymentConfigs';
const FNI_ATTRIBUTES_IDS = 'fniAttributeIds';
const FEE_CODES = 'feeCodes';
const DEAL_TYPE_CONFIGS = 'dealTypeConfigs';

const STATE_FEE_TAX_SELECTED_TEMPLATE_ID = 'stateFeeTaxSelectedTemplateId';

export const DEAL_STATUS_CONFIGS = 'dealStatusConfigs';

export const SET_UP_KEYS = {
  fnIConfigs: FNNI_CONFIGS,
  feeConfigs: FEE_CONFIGS,
  downPaymentConfigs: DOWNPAYMENTS_CONFIGS,
  FNI_ATTRIBUTES_IDS,
  FEE_CODES,
  DEAL_TYPE_CONFIGS,
  DEAL_STATUS_CONFIGS,
  DEAL_STATUS_RULES: 'dealStatusRulesConfigs',
  FRONT_GROSS_CONFIG: 'frontGrossConfigs',
  BACK_GROSS_CONFIG: 'backGrossConfigs',
  DEALER_GROSS_CONFIG: 'dealerGrossConfigs',
  DEAL_CONFIG: 'dealConfig',
  DISCOUNT_CAP: 'discountCap',
  DEPOSIT_DISABLED_VEHICLE_STATUSES: 'depositDisabledVehicleStatus',
  WEBSITE_URLS: 'websiteurls',
  LOAN_DAYS_TO_FIRST_PAYMENT: 'loanDaysToFirstPayment',
  ANNUAL_MILES: 'annualMiles',
  ZIP_CODE: 'zipCode',
  DEALER_MARKET_ID: 'dealerMarketId',
  DEFAULT_COUNTY: 'defaultCounty',
  DEALER_STATE_CODE: 'dealerStateCode',
  CREDIT_SCORE: 'creditScore',
  ACCESSORIES: 'accessories',
  FNI: 'fnI',
  REBATE: 'rebate',
  DUE_BILLS: 'dueBills',
  DUE_BILL_ID: 'dueBillId',
  LEASE_DAYS_TO_FIRST_PAYMENT: 'leaseDaysToFirstPayment',
  LOAN_BUY_RATE_FOR_NEW: 'loanBuyRate',
  LOAN_BUY_RATE_FOR_USED: 'loanBuyRateForUsed',
  DEFAULT_NEW_SELLING_PRICE,
  DEFAULT_OLD_SELLING_PRICE: 'usedVehiclePriceType',
  DEFAULT_DEMO_SELLING_PRICE: 'demoVehiclePriceType',
  DEFAULT_COUNTRY_DIALING_CODE: 'countryCode',
  DEAL_POSTING_PACKS: 'dealPostingPacksConfigs',
  SHEET_CONFIGS: 'sheetConfigs',
  LEASE_COMPARISON_SETUP: 'leaseComparisionSetup',
  YEARLY_MILES_1: 'yearlyMiles1',
  YEARLY_MILES_2: 'yearlyMiles2',
  YEARLY_MILES_3: 'yearlyMiles3',
  DEAL_SHEET_COUNT: 'dealSheetCount',
  RECAP_SHEET_COUNT: 'recapSheetCount',
  COVER_SHEET_COUNT: 'coverSheetCount',
  DOUBLE_SIDED_PRINT: 'doubleSidedPrint',
  OTHER_CONFIGS: 'otherConfigs',
  COMMISSION_SPLIT_TYPE: 'comissionSplittype',
  UNIT_COUNT_SPLIT_TYPE: 'unitCountSplitType',
  OVERRIDE_SALES_MANAGER_FNI_SUBMIT: 'overrideSalesManagerOnFAndISubmit',
  SERVICE_APPOINTMENT_CONFIG: 'serviceAppointmentConfig',
  NO_OF_DAYS_TO_LINK_TO_DEAL: 'noOfDaysToLinkToDeal',
  AUTO_ROLL_CASH_DEFICIENCY: 'cashDeficiencyAutoRoll',
  DEFAULT_AUTO_ROLL_CASH_DEFICIENCY: 'defaultAutoRollCashDeficiency',
  AUTO_ROLL_ZERO_PERCENT_APR: 'aprAutoRoll',
  ALWAYS_SHOW_APR_ON_DESKING: 'alwaysShowAprOnDesking',
  SHOW_HST_SEPARATELY: 'showHSTSeparately',
  REDUCE_INTEREST_FOR_EARLY_PAYMENT: 'reduceInterestForEarlyPayment',
  TRADE_IN_COUNT: 'noOfTradeIns',
  TRIM_REQUIRED_PROVIDER_IDS: 'trimRequiredProviderIds',
  OWNER_TYPE: 'ownerType',
  FNI_PDF_VIEW_TYPE: 'fniPdfViewType',
  PROVIDER_INFO: 'providerInfo',
  COST_ADJUSTMENTS: 'costAdjustments',
  COST_ADJUSTMENTS_IDS: 'costAdjustmentId',
  DEALER_MONTH_START_DAY: 'dealerMonthConfig.startDay',
  DEALER_MONTH_END_DAY: 'dealerMonthConfig.endDay',
  AUTO_UPDATE_COST_ADJ_RO: 'enableAutoCostAdjForRO',
  AUTO_UPDATE_COST_ADJ_PO: 'enableAutoCostAdjForPO',
  USE_DEALER_ZIP_MARKET_ID: 'useDealerZipMarketID',
  ADD_DISCLOSURE_TYPE_CONFIG: 'disclosureTypeTaxMappingForFNI',
  ENABLE_DEFERRED_PAYMENT_OPTIONS: 'enableDeferredPaymentOptions',
  ENABLE_DEAL_RECOMMENDATION: 'enableBudgetBasedRecommendationInDeals',
  ENABLE_VI_RECOMMENDATION: 'enableBudgetBasedRecommendationInVI',
  AUTO_ROLL_SERVICE_CONTRACTS: 'autoRollServiceContract',
  ADJUST_SELLING_PRICE_BASED_ON_VEHICLE_GROSS: 'adjustSellingPriceBasedOnVehicleGross',
  [UNLOCK_PIN_FOR_BOOKED_DEALS]: 'unlockPinForBookedDeals',
  [UNLOCK_PIN_FOR_CLOSED_DEALS]: 'unlockPinForClosedDeals',
  [DEAL_UNLOCK_PASSCODE_FOR_CLOSED_MONTHS_FOR_BOOKED_DEALS]: 'dealUnlockPasscodeForClosedMonthsForBookedDeals',
  [DEAL_UNLOCK_PASSCODE_FOR_CLOSED_MONTHS_FOR_CLOSED_DEALS]: 'dealUnlockPasscodeForClosedMonthsForClosedDeals',
  [DEAL_UNWIND_PASSCODE_FOR_BOOKED_DEALS]: 'dealUnlockPasscodeForUnwindForBookedDeals',
  [DEAL_UNWIND_PASSCODE_FOR_CLOSED_DEALS]: 'dealUnlockPasscodeForUnwindForClosedDeals',
  [DRS_FNI_MENU_DEAL_SWITCH_PASSCODE]: 'drsFnIMenuDealSwitchPasscode',
  VOLUME_BASED_COMMISSION: 'volumeBasedCommissions',
  VEHICLE_SUBTYPE_FOR_NEW_PROGRAM: 'usedVehicleSubTypesEligibleForNewVehicleProgram',
  DEAL_STATUS_FOR_MANDATORY_TAX_CODE: 'taxCodeMandatoryAfterDealStatus',
  REBATE_CONFIGS: 'rebateConfigs',
  SCAN_DEFAULTS: 'scanDefaults',
  RECALCULATE_PAYMENT: 'rolesWithDefaultAccessToPaymentRecalculation',
  ROLES_FOR_ASSIGNEE: 'rolesForAssignee',
  USE_CUSTOMER_TIER_FOR_LOAN_APR: 'useCustomerTierForLoanApr',
  VIEW_DEMO_MILEAGE_ADJUSTMENT_AS: 'viewDemoMileageAdjustmentAs',
  INCLUDE_EXPIRED_PROGRAMS: 'includeExpiredPrograms',
  PRINT_ZERO_PRICE_PRODUCTS_IN_CONTRACT: 'printZeroPriceProductsInContract',
  USE_HARD_ADD_NUMBER: 'useHardAddNumber',
  RELEASE_VEHICLE_FROM_HOLD_IN_DAYS: 'releaseVehicleFromHoldInDays',
  MULTI_VEHICLE_DESKING: 'multiVehicleDesking',
  LENDERS: 'lenderConfigs',
  ENABLE_PRE_CLOSE_WORKFLOW: 'enablePreCloseWorkflow',
  AUTO_ARCHIVE_SHEETS: 'archiveSheets',
  DEFAULT_HEADER_DATE: 'defaultHeaderDate',
  SHOW_ACCOUNTING_DATE: 'showAccountingDateWhileUnwinding',
  ENABLE_EFFECTIVE_INTEREST_RATE: 'showEffectiveInterestRate',
  PAYMENT_FREQUENCY_CONFIG: 'paymentFrequencyConfig',
  LOAN_FREQUENCY: 'loanFrequency',
  LEASE_FREQUENCY: 'leaseFrequency',
  DAYS_TO_FIRST_PAYMENT: 'daysToFirstPayment',
  SHOW_MAX_RESIDUAL_MSRP: 'showMaximumResidualizedMSRP',
  ADD_TRADEIN_VEHICLE_TO_VI_ON_DEAL_CONFIRM: 'addTradeInVehicleToViOnDealConfirm',
  ASSIGNEE_LIMITS: 'assigneeLimits',
  APR_CONFIG: 'aprConfig',
  ASSIGNEE_COMM_SPLIT_OPTIONS: 'assigneeCommissionSplitOptions',
  ASSIGNEE_UNIT_COUNT_SPLIT: 'assigneeUnitCountSplit',
  ALLOW_PAYMENT_BEYOND_DUE_AMOUNT: 'allowPaymentBeyondDueAmount',
  ALLOW_REFUND_BEYOND_REFUND_AMOUNT: 'allowRefundBeyondRefundAmount',
  ALLOW_RESERVE_AND_SOLD_VEHICLE: 'allowReserveAndSoldVehicleSelection',
  VEHICLE_CHANGE_SELLING_PRICE__UPDATE: 'vehicleChangeSellingPriceUpdate',
  AUTO_CAL_COMMISSION_FOR_BOOKED_CLOSED_DEALS: 'autocalculatecommisonforBookedandClosedDeals',
  ALLOW_MULTIPLE_RESERVATIONS: 'allowMultipleReservationsOnVehicle',
  CREATE_APPOINTMENTS_FROM_DEALS: 'createAppointmentFromDeals',
  DEPOSIT_RULES: 'depositRules',
  DISPLAY_MODEL_SOURCE: 'displayModelSource',
  SHOW_ASSUMPTIVE_PAY_OFF_POP_UP: 'showAssumptivePayOffPopUp',
  VENDOR_MANAGEMENT: 'vendorConfigs',
  ENABLE_CUSTOMER_CASH_PERCENTAGE: 'enableCustomerCashPercentage',
  ENABLE_CTA_RDR_EVENT: 'enableCtaRdrEvent',
  DEDUCT_CUSTOMER_CASH_FROM_AMOUNT_FINANCED_CASH_DEAL: 'deductCustomerCashfromAmountFinancedforCashDeal',
  ADD_INVOICE_TO_DEAL_JACKET: 'addInvoiceToDealJacket',
  UPDATE_DEAL_INFO_IN_DEAL_TASK_MANAGER: 'enableTaskManagerDealConfig',
  ...STATE_FEE_TAX_INFO_KEYS,
  STATE_FEE_TAX_SELECTED_TEMPLATE_ID,
  SALES_FEE_SETUP: 'salesFeeSetup',
  REMOVE_DEFAULT_OPTIONS: 'removeDefaultOptions',
  PAYMENT_OPTION_CONFIGS: 'paymentOptionConfigs',
  PURCHASE_ORDER_FORM_COUNT: 'purchaseOrderFormCount',
  SALES_ORDER_FORM_COUNT: 'salesOrderFormCount',
  DEFAULT_RESERVATION_AMOUNT: 'defaultReservationAmount',
  DEFAULT_PARTEX_VAT_INFO: 'defaultPartExVatInfo',
  CUSTOMER_CASH: 'customerCash',
  PLATFORM: 'platform',
  FNI_ATTRIBUTES: 'fniAttributes',
  DEFAULT_UNIT_COUNT_SPLIT_FI_MANAGER: 'defaultUnitCountSplitFIManager',
  DEFAULT_UNIT_COUNT_SPLIT_SALES_MANAGER: 'defaultUnitCountSplitSalesManager',
  DEFAULT_UNIT_COUNT_SPLIT_SALES_PERSON: 'defaultUnitCountSplitSalesperson',
  DEFAULT_UNIT_COUNT_SPLIT_E_ADVISOR: 'defaultUnitCountSplitEAdvisor',
  VIEW_DEMO_MILEAGE_ADJUSTMENT_TYPE_AS: 'viewDemoMileageAdjustmentTypeas',
  ROLE_IDS: 'roleIds',
  DEAL_STATUS_AFTER_WHICH_TAX_CODE_IS_MANDATORY: 'dealStatusAfterwhichTaxCodeIsMandatory',
  VEHICLE_SUBTYPES_ELIGIBLE_FOR_NEW_VEHICLE_PROGRAM: 'vehicleSubTypesEligibleForNewVehicleProgram',
  TRADE_IN_OWNER_TYPE: 'tradeInOwnerType',
  COMMISSION_SPLIT_OPTIONS_FI_MANAGER: 'commissionSplitOptionsFIManager',
  COMMISSION_SPLIT_OPTIONS_SALES_MANAGER: 'commissionSplitOptionsSalesManager',
  COMMISSION_SPLIT_OPTIONS_SALES_PERSON: 'commissionSplitOptionsSalesperson',
  COMMISSION_SPLIT_OPTIONS_E_ADVISOR: 'commissionSplitOptionsEAdvisor',
  AUTO_ROLL_0_APR: 'autoRoll0%APR',
  SHOW_AUTO_ROLL_CASH_DEFICIENCY_POPUP: 'showAutorollCashDeficiencyPopup',
  ASSIGN_SALES_MANAGER_FI_STATUS_UPDATE: 'assignSalesManagerFIStatusUpdate',
  DEFAULT_DEPOSIT_AMOUNT_PRODUCT_CLASSIFICATION: 'defaultDepositProductClassification',
  DEFAULT_TRADE_IN_PRODUCT_CLASSIFICATION: 'defaultTradeinProductClassification',
  DEFAULT_DEPOSIT_ACCOUNTING_GROUP: 'defaultDepositAccountingGroup',
  DEFAULT_DEPOSIT_VAT_INFO: 'defaultDepositVatInfo',
  DISCOUNT_MATRIX: 'discountBasedOnVehicleType',
};

export const DEAL_TEMPLATE_TARGETTING_KEYS = {
  FNI: 'useDefaultFNIs',
};
