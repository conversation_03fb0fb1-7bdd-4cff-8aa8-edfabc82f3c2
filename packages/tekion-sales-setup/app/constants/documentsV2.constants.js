import OPERATORS from '@tekion/tekion-base/constants/filterOperators';
import { FORM_STATE } from '@tekion/tekion-base/constants/formConfigurator/constants';
import { DOCUMENTS_LABEL_TYPES, CUSTOMER_TYPES_VALUES } from '@tekion/tekion-base/constants/signature';
import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { FORM_ACTION } from '@tekion/tekion-base/constants/concierge';
import { DEPARTMENTS } from '@tekion/tekion-base/constants/crm';
import { DOCUMENT_STATUS } from '@tekion/tekion-base/constants/desking/documentStatus';

import { MODULE_TARGETING_TYPES } from '@tekion/tekion-business/src/constants/sales/moduleTargeting';
import { EXTERNAL_DOCUMENT_SOURCES } from '@tekion/tekion-business/src/constants/sales/documents';

import mileageLabeler from 'utils/label';

export const PRINTING_STATUS = {
  // TODO: to be removed
  INITIATED: 'SUBMITTED',
  IN_QUEUE: 'IN_QUEUE',
  PRINTING: 'IN_PROGRESS',
  FAILED: 'FAIL',
  PRINTED: 'SUCCESS',
  CANCELLED: 'CANCEL',
};

export const FORM_STATUS_TYPES = {
  SIGNED: 'SIGNED',
  SHARED: 'SHARED',
  PRINTED: 'PRINTED',
  INVALID: 'INVALID',
  FAILED: 'FAILED',
};

export const PRINTER_TYPES = {
  DOT_MATRIX: 'DOT_MATRIX',
  LASER: 'LASER',
};

export const FORM_STATUS = {
  ACTIVE: 'ACTIVE',
  IN_ACTIVE: 'IN_ACTIVE',
};

export const CUSTOM_FIELD_TYPES = {
  SINGLE_SELECT: 'SINGLE_SELECT',
  MULTI_SELECT: 'MULTI_SELECT',
  FREE_TEXT: 'FREE_TEXT',
  DATE: 'DATE',
  PRICE: 'PRICE',
  CHECKBOX: 'CHECKBOX',
  PRICE_OR_COST: 'PRICE_OR_COST',
  TEXT_AREA: 'TEXT_AREA',
  PHONE: 'PHONE',
};

export const FIELD_TYPES_HAVING_DYNAMIC_PDF_KEY = [CUSTOM_FIELD_TYPES.SINGLE_SELECT];

export const STATIC_DOCUMENTS_SEARCH_PAYLOAD = {
  pageInfo: {
    start: 0,
    rows: 400,
  },

  filters: [
    {
      field: 'department',
      values: [DEPARTMENTS.SALES],
    },
    {
      field: 'formState',
      operator: OPERATORS.NIN,
      values: [FORM_STATE.DRAFT],
    },
    {
      field: 'targetedModules',
      operator: OPERATORS.IN,
      values: [MODULE_TARGETING_TYPES.DESKING],
    },
  ],
};

export const FORM_TYPES = {
  PRINTED: 'PRINTED',
  MANUALLY_ADDED: 'MANUALLY_ADDED',
  DELETED: 'DELETED',
  ADDED: 'ADDED',
};

export const FNI_DETAILS_SOURCES = {
  LGM: 'lgm',
};

export const CONTRACT_DOCS_USED_FOR_LABEL = __('Route One');

export const CUSTOMER_TYPES = [
  {
    label: __('Buyer'),
    value: CUSTOMER_TYPES_VALUES.BUYER,
  },
  {
    label: __('Co-Buyer'),
    value: CUSTOMER_TYPES_VALUES.CO_BUYER,
  },
];

export const signWidthScale = 0.09;

export const DOCUMENT_VIEW = {
  SEQUENCE: 'SEQUENCE',
  CATEGORY: 'CATEGORY',
};

export const DEALER_DISCRETION_MESSAGE = __(
  'Critical Contract Validation errors exist. Funding will likely be delayed and customers may need to re-contract. In order to generate the form, please acknowledge that you understand the risk by entering your initials.'
);

export const FORM_DEFAULT_PRIORITY = 4;

export const DOCUMENTS_LABEL_TYPES_VS_PRIORITY = {
  [DOCUMENTS_LABEL_TYPES.CUSTOMER_ELECTRONIC_CONSENT]: 2,
  [DOCUMENTS_LABEL_TYPES.CO_APPLICANT_CUSTOMER_ELECTRONIC_CONSENT]: 2,
  [DOCUMENTS_LABEL_TYPES.APPLICANT_CUSTOMER_ELECTRONIC_CONSENT]: 1,
  [DOCUMENTS_LABEL_TYPES.NOTICE_TO_CO_SIGNER]: 3,
  [DOCUMENTS_LABEL_TYPES.CONTRACT]: FORM_DEFAULT_PRIORITY + 1,
};

export const UPDATE_FORM_MODAL_CONFIG = {
  heading: __('Update Form'),
  submitButtonText: __('Update'),
};

export const VOID_AND_REGENERATE_MODAL_CONFIG = {
  heading: __('Void & Regenerate'),
  submitButtonText: __('Regenerate'),
  isRegeneratingContracts: true,
};

export const FORM_UPDATION_TABLE_IDS = {
  FIELD_NAME: 'FIELD_NAME',
  UPDATES: 'UPDATES',
};

export const REGENERATION_WARNING_MESSAGE = __('Please note that the customer would need to sign the regenerated form');

export const FORM_UPDATION_TABLE_CONFIG = [
  {
    Header: __('Field Name'),
    accessor: 'fieldName',
    id: FORM_UPDATION_TABLE_IDS.FIELD_NAME,
    minWidth: 100,
  },
  {
    Header: __('Updates'),
    accessor: 'updates',
    id: FORM_UPDATION_TABLE_IDS.UPDATES,
    minWidth: 200,
  },
];

export const FORM_UPDATION_FIELDS = {
  BUYER: 'BUYER',
  COBUYER: 'COBUYER',
  VEHICLE: 'VEHICLE',
  DEAL: 'DEAL',
  LIEN_HOLDER: 'LIEN_HOLDER',
  PRODUCT: 'PRODUCT',
};

export const FORM_UPDATION_FIELD_LABELS = {
  [FORM_UPDATION_FIELDS.BUYER]: 'Buyer',
  [FORM_UPDATION_FIELDS.COBUYER]: 'CoBuyer',
  [FORM_UPDATION_FIELDS.VEHICLE]: 'Vehicle',
  [FORM_UPDATION_FIELDS.DEAL]: 'Deal',
  [FORM_UPDATION_FIELDS.LIEN_HOLDER]: 'Lien Holder',
  [FORM_UPDATION_FIELDS.PRODUCT]: 'Product',
};

export const FORM_UPDATION_SUB_FIELDS = {
  BUYER_FIRST_NAME: 'BUYER_FIRST_NAME',
  BUYER_LAST_NAME: 'BUYER_LAST_NAME',
  BUYER_SUFFIX: 'BUYER_SUFFIX',
  BUYER_MIDDLE_INITIAL: 'BUYER_MIDDLE_INITIAL',
  BUYER_DOB: 'BUYER_DOB',
  BUYER_PHONE_NO: 'BUYER_PHONE_NO',
  BUYER_EMAIL: 'BUYER_EMAIL',
  BUYER_BUSINESS_PHONE_NUMBER: 'BUYER_BUSINESS_PHONE_NUMBER',
  BUYER_CUSTOMER_ID: 'BUYER_CUSTOMER_ID',
  BUYER_ADDRESS1: 'BUYER_ADDRESS1',
  BUYER_ADDRESS2: 'BUYER_ADDRESS2',
  BUYER_CITY: 'BUYER_CITY',
  BUYER_STATE: 'BUYER_STATE',
  BUYER_COUNTRY: 'BUYER_COUNTRY',
  BUYER_ZIPCODE: 'BUYER_ZIPCODE',
  COBUYER_FIRST_NAME: 'COBUYER_FIRST_NAME',
  COBUYER_LAST_NAME: 'COBUYER_LAST_NAME',
  COBUYER_SUFFIX: 'COBUYER_SUFFIX',
  COBUYER_MIDDLE_INITIAL: 'COBUYER_MIDDLE_INITIAL',
  COBUYER_DOB: 'COBUYER_DOB',
  COBUYER_PHONE_NO: 'COBUYER_PHONE_NO',
  COBUYER_EMAIL: 'COBUYER_EMAIL',
  COBUYER_BUSINESS_PHONE_NUMBER: 'COBUYER_BUSINESS_PHONE_NUMBER',
  COBUYER_CUSTOMER_ID: 'COBUYER_CUSTOMER_ID',
  COBUYER_ADDRESS1: 'COBUYER_ADDRESS1',
  COBUYER_ADDRESS2: 'COBUYER_ADDRESS2',
  COBUYER_CITY: 'COBUYER_CITY',
  COBUYER_STATE: 'COBUYER_STATE',
  COBUYER_COUNTRY: 'COBUYER_COUNTRY',
  COBUYER_ZIPCODE: 'COBUYER_ZIPCODE',
  MAKE: 'MAKE',
  MODEL: 'MODEL',
  MODEL_YEAR: 'MODEL_YEAR',
  EXTERIOR_COLOR: 'EXTERIOR_COLOR',
  STOCK_ID: 'STOCK_ID',
  ODOMETER: 'ODOMETER',
  LICENSE_PLATE_NO: 'LICENSE_PLATE_NO',
  NO_OF_WHEELS: 'NO_OF_WHEELS',
  FINANCED_AMOUNT: 'FINANCED_AMOUNT',
  CASHDOWN: 'CASHDOWN',
  EFFECTIVE_DATE: 'EFFECTIVE_DATE',
  TOTAL_AMOUNT: 'TOTAL_AMOUNT',
  RESIDUAL_TOTAL_VALUE: 'RESIDUAL_TOTAL_VALUE',
  LEASE_ALLOWABLE_MILEAGE: 'LEASE_ALLOWABLE_MILEAGE',
  FINANCE_TERM: 'FINANCE_TERM',
  MONTHLY_PAYMENT: 'MONTHLY_PAYMENT',
  DEAL_TYPE: 'DEAL_TYPE',
  TRANSACTION_DATE: 'TRANSACTION_DATE',
  DAYS_TO_FIRST_PAYMENT: 'DAYS_TO_FIRST_PAYMENT',
  FINANCE_MANAGER_NAME: 'FINANCE_MANAGER_NAME',
  LIEN_HOLDER_NAME: 'LIEN_HOLDER_NAME',
  LIEN_HOLDER_ADDRESS1: 'LIEN_HOLDER_ADDRESS1',
  LIEN_HOLDER_ADDRESS2: 'LIEN_HOLDER_ADDRESS2',
  LIEN_HOLDER_CITY: 'LIEN_HOLDER_CITY',
  LIEN_HOLDER_STATE: 'LIEN_HOLDER_STATE',
  LIEN_HOLDER_ZIPCODE: 'LIEN_HOLDER_ZIPCODE',
  LIEN_HOLDER_PHONE: 'LIEN_HOLDER_PHONE',
  LIEN_HOLDER_FAX: 'LIEN_HOLDER_FAX',
  FINANCED_SEPARATELY: 'FINANCED_SEPARATELY',
  PAYMENT_PLAN_PROVIDER_NAME: 'PAYMENT_PLAN_PROVIDER_NAME',
  SALES_TAX: 'SALES_TAX',
  TAX_RATE: 'TAX_RATE',
  COVERAGE_NAME: 'COVERAGE_NAME',
  TERM_MONTHS: 'TERM_MONTHS',
  TERM_MILES: 'TERM_MILES',
  DEDUCTIBLE: 'DEDUCTIBLE',
  COST: 'COST',
  PRICE: 'PRICE',
  FIMANAGERID: 'FIMANAGERID',
  NO_OF_KEYS: 'NO_OF_KEYS',
  CONTRACT_NUMBER: 'CONTRACT_NUMBER',
  SRP: 'SRP',
  APR: 'APR',
  VEHICLE_PURCHASE_PRICE: 'VEHICLE_PURCHASE_PRICE',
  VEHICLE_MSRP: 'VEHICLE_MSRP',
  INSERVICE_DATE: 'INSERVICE_DATE',
};

export const FORM_UPDATION_SUB_FIELDS_LABELS = {
  [FORM_UPDATION_SUB_FIELDS.BUYER_FIRST_NAME]: 'Buyer First Name',
  [FORM_UPDATION_SUB_FIELDS.BUYER_LAST_NAME]: 'Buyer Last Name',
  [FORM_UPDATION_SUB_FIELDS.BUYER_SUFFIX]: 'Buyer Suffix',
  [FORM_UPDATION_SUB_FIELDS.BUYER_MIDDLE_INITIAL]: 'Buyer Middle Initial',
  [FORM_UPDATION_SUB_FIELDS.BUYER_DOB]: 'Buyer DOB',
  [FORM_UPDATION_SUB_FIELDS.BUYER_PHONE_NO]: 'Buyer Phone No.',
  [FORM_UPDATION_SUB_FIELDS.BUYER_EMAIL]: 'Buyer Email',
  [FORM_UPDATION_SUB_FIELDS.BUYER_BUSINESS_PHONE_NUMBER]: 'Buyer Business Phone No.',
  [FORM_UPDATION_SUB_FIELDS.BUYER_CUSTOMER_ID]: 'Buyer Customer ID',
  [FORM_UPDATION_SUB_FIELDS.BUYER_ADDRESS1]: 'Buyer Address1',
  [FORM_UPDATION_SUB_FIELDS.BUYER_ADDRESS2]: 'Buyer Address2',
  [FORM_UPDATION_SUB_FIELDS.BUYER_CITY]: 'Buyer City',
  [FORM_UPDATION_SUB_FIELDS.BUYER_STATE]: 'Buyer State',
  [FORM_UPDATION_SUB_FIELDS.BUYER_COUNTRY]: 'Buyer Country',
  [FORM_UPDATION_SUB_FIELDS.BUYER_ZIPCODE]: 'Buyer ZIP Code',
  [FORM_UPDATION_SUB_FIELDS.COBUYER_FIRST_NAME]: 'Co-Buyer First Name',
  [FORM_UPDATION_SUB_FIELDS.COBUYER_LAST_NAME]: 'Co-Buyer Last Name',
  [FORM_UPDATION_SUB_FIELDS.COBUYER_SUFFIX]: 'Co-Buyer Suffix',
  [FORM_UPDATION_SUB_FIELDS.COBUYER_MIDDLE_INITIAL]: 'Co-Buyer Middle Initial',
  [FORM_UPDATION_SUB_FIELDS.COBUYER_DOB]: 'Co-Buyer DOB',
  [FORM_UPDATION_SUB_FIELDS.COBUYER_PHONE_NO]: 'Co-Buyer Phone No.',
  [FORM_UPDATION_SUB_FIELDS.COBUYER_EMAIL]: 'Co-Buyer Email',
  [FORM_UPDATION_SUB_FIELDS.COBUYER_BUSINESS_PHONE_NUMBER]: 'Co-Buyer Business Phone No.',
  [FORM_UPDATION_SUB_FIELDS.COBUYER_CUSTOMER_ID]: 'Co-Buyer Customer ID',
  [FORM_UPDATION_SUB_FIELDS.COBUYER_ADDRESS1]: 'Co-Buyer Address1',
  [FORM_UPDATION_SUB_FIELDS.COBUYER_ADDRESS2]: 'Co-Buyer Address2',
  [FORM_UPDATION_SUB_FIELDS.COBUYER_CITY]: 'Co-Buyer City',
  [FORM_UPDATION_SUB_FIELDS.COBUYER_STATE]: 'Co-Buyer State',
  [FORM_UPDATION_SUB_FIELDS.COBUYER_COUNTRY]: 'Co-Buyer Country',
  [FORM_UPDATION_SUB_FIELDS.COBUYER_ZIPCODE]: 'Co-Buyer ZIP Code',
  [FORM_UPDATION_SUB_FIELDS.MAKE]: 'Make',
  [FORM_UPDATION_SUB_FIELDS.MODEL]: 'Model',
  [FORM_UPDATION_SUB_FIELDS.MODEL_YEAR]: 'Model Year',
  [FORM_UPDATION_SUB_FIELDS.EXTERIOR_COLOR]: 'Exterior Color',
  [FORM_UPDATION_SUB_FIELDS.STOCK_ID]: 'Stock Number',
  [FORM_UPDATION_SUB_FIELDS.ODOMETER]: 'Odometer',
  [FORM_UPDATION_SUB_FIELDS.LICENSE_PLATE_NO]: 'License Plate No.',
  [FORM_UPDATION_SUB_FIELDS.NO_OF_WHEELS]: 'No. Of Wheels',
  [FORM_UPDATION_SUB_FIELDS.FINANCED_AMOUNT]: 'Financed Amount',
  [FORM_UPDATION_SUB_FIELDS.CASHDOWN]: 'Down Payment',
  [FORM_UPDATION_SUB_FIELDS.EFFECTIVE_DATE]: 'Effective Date',
  [FORM_UPDATION_SUB_FIELDS.TOTAL_AMOUNT]: 'Total Amount',
  [FORM_UPDATION_SUB_FIELDS.RESIDUAL_TOTAL_VALUE]: 'Residual Total Value',
  [FORM_UPDATION_SUB_FIELDS.LEASE_ALLOWABLE_MILEAGE]: 'Lease Allowable Mileage',
  [FORM_UPDATION_SUB_FIELDS.FINANCE_TERM]: 'Finance Term',
  [FORM_UPDATION_SUB_FIELDS.MONTHLY_PAYMENT]: 'Monthly Payment',
  [FORM_UPDATION_SUB_FIELDS.DEAL_TYPE]: 'Deal Type',
  [FORM_UPDATION_SUB_FIELDS.TRANSACTION_DATE]: 'Transaction Date',
  [FORM_UPDATION_SUB_FIELDS.DAYS_TO_FIRST_PAYMENT]: 'Days To First Payment',
  [FORM_UPDATION_SUB_FIELDS.FINANCE_MANAGER_NAME]: 'Finance Manager Name',
  [FORM_UPDATION_SUB_FIELDS.LIEN_HOLDER_NAME]: 'Lien Holder Name',
  [FORM_UPDATION_SUB_FIELDS.LIEN_HOLDER_ADDRESS1]: 'Lien Holder Address1',
  [FORM_UPDATION_SUB_FIELDS.LIEN_HOLDER_ADDRESS2]: 'Lien Holder Address2',
  [FORM_UPDATION_SUB_FIELDS.LIEN_HOLDER_CITY]: 'Lien Holder City',
  [FORM_UPDATION_SUB_FIELDS.LIEN_HOLDER_STATE]: 'Lien Holder State',
  [FORM_UPDATION_SUB_FIELDS.LIEN_HOLDER_ZIPCODE]: 'Lien Holder Zipcode',
  [FORM_UPDATION_SUB_FIELDS.LIEN_HOLDER_PHONE]: 'Lien Holder Phone',
  [FORM_UPDATION_SUB_FIELDS.LIEN_HOLDER_FAX]: 'Lien Holder Fax',
  [FORM_UPDATION_SUB_FIELDS.FINANCED_SEPARATELY]: 'Financed Separately',
  [FORM_UPDATION_SUB_FIELDS.PAYMENT_PLAN_PROVIDER_NAME]: 'Payment Provider Plan Name',
  [FORM_UPDATION_SUB_FIELDS.SALES_TAX]: 'Sales Tax',
  [FORM_UPDATION_SUB_FIELDS.TAX_RATE]: 'Tax Rate',
  [FORM_UPDATION_SUB_FIELDS.COVERAGE_NAME]: 'Coverage Name',
  [FORM_UPDATION_SUB_FIELDS.TERM_MONTHS]: 'Term Months',
  [FORM_UPDATION_SUB_FIELDS.TERM_MILES]: `Term ${mileageLabeler.getMilesLabel()}`,
  [FORM_UPDATION_SUB_FIELDS.DEDUCTIBLE]: 'Deductible',
  [FORM_UPDATION_SUB_FIELDS.COST]: 'Cost',
  [FORM_UPDATION_SUB_FIELDS.PRICE]: 'Price',
  [FORM_UPDATION_SUB_FIELDS.FIMANAGERID]: 'Finance Manager ID',
  [FORM_UPDATION_SUB_FIELDS.NO_OF_KEYS]: 'No of Keys',
  [FORM_UPDATION_SUB_FIELDS.CONTRACT_NUMBER]: 'Contract Number',
  [FORM_UPDATION_SUB_FIELDS.SRP]: 'Srp',
  [FORM_UPDATION_SUB_FIELDS.APR]: 'APR',
  [FORM_UPDATION_SUB_FIELDS.VEHICLE_PURCHASE_PRICE]: 'Purchase Price',
  [FORM_UPDATION_SUB_FIELDS.VEHICLE_MSRP]: 'MSRP',
  [FORM_UPDATION_SUB_FIELDS.INSERVICE_DATE]: 'In Service Date',
};

export const FORM_UPDATION_CATEGORY_1 = [
  FORM_UPDATION_SUB_FIELDS.BUYER_FIRST_NAME,
  FORM_UPDATION_SUB_FIELDS.BUYER_LAST_NAME,
  FORM_UPDATION_SUB_FIELDS.BUYER_SUFFIX,
  FORM_UPDATION_SUB_FIELDS.BUYER_MIDDLE_INITIAL,
  FORM_UPDATION_SUB_FIELDS.BUYER_DOB,
  FORM_UPDATION_SUB_FIELDS.COBUYER_FIRST_NAME,
  FORM_UPDATION_SUB_FIELDS.COBUYER_LAST_NAME,
  FORM_UPDATION_SUB_FIELDS.COBUYER_SUFFIX,
  FORM_UPDATION_SUB_FIELDS.COBUYER_MIDDLE_INITIAL,
  FORM_UPDATION_SUB_FIELDS.COBUYER_DOB,
  FORM_UPDATION_SUB_FIELDS.MAKE,
  FORM_UPDATION_SUB_FIELDS.MODEL,
  FORM_UPDATION_SUB_FIELDS.MODEL_YEAR,
  FORM_UPDATION_SUB_FIELDS.EXTERIOR_COLOR,
  FORM_UPDATION_SUB_FIELDS.STOCK_ID,
  FORM_UPDATION_SUB_FIELDS.ODOMETER,
  FORM_UPDATION_SUB_FIELDS.FINANCED_AMOUNT,
  FORM_UPDATION_SUB_FIELDS.CASHDOWN,
  FORM_UPDATION_SUB_FIELDS.EFFECTIVE_DATE,
  FORM_UPDATION_SUB_FIELDS.LICENSE_PLATE_NO,
  FORM_UPDATION_SUB_FIELDS.TOTAL_AMOUNT,
  FORM_UPDATION_SUB_FIELDS.RESIDUAL_TOTAL_VALUE,
  FORM_UPDATION_SUB_FIELDS.FINANCED_SEPARATELY,
  FORM_UPDATION_SUB_FIELDS.SALES_TAX,
  FORM_UPDATION_SUB_FIELDS.TAX_RATE,
  FORM_UPDATION_SUB_FIELDS.COVERAGE_NAME,
  FORM_UPDATION_SUB_FIELDS.TERM_MONTHS,
  FORM_UPDATION_SUB_FIELDS.TERM_MILES,
  FORM_UPDATION_SUB_FIELDS.DEDUCTIBLE,
  FORM_UPDATION_SUB_FIELDS.COST,
  FORM_UPDATION_SUB_FIELDS.PRICE,
  FORM_UPDATION_SUB_FIELDS.APR,
  FORM_UPDATION_SUB_FIELDS.VEHICLE_PURCHASE_PRICE,
];

export const FORM_UPDATION_CATEGORY_2 = [
  FORM_UPDATION_SUB_FIELDS.BUYER_PHONE_NO,
  FORM_UPDATION_SUB_FIELDS.BUYER_EMAIL,
  FORM_UPDATION_SUB_FIELDS.BUYER_ADDRESS1,
  FORM_UPDATION_SUB_FIELDS.BUYER_ADDRESS2,
  FORM_UPDATION_SUB_FIELDS.BUYER_CITY,
  FORM_UPDATION_SUB_FIELDS.BUYER_STATE,
  FORM_UPDATION_SUB_FIELDS.BUYER_COUNTRY,
  FORM_UPDATION_SUB_FIELDS.BUYER_ZIPCODE,
  FORM_UPDATION_SUB_FIELDS.COBUYER_PHONE_NO,
  FORM_UPDATION_SUB_FIELDS.COBUYER_EMAIL,
  FORM_UPDATION_SUB_FIELDS.COBUYER_ADDRESS1,
  FORM_UPDATION_SUB_FIELDS.COBUYER_ADDRESS2,
  FORM_UPDATION_SUB_FIELDS.COBUYER_CITY,
  FORM_UPDATION_SUB_FIELDS.COBUYER_STATE,
  FORM_UPDATION_SUB_FIELDS.COBUYER_COUNTRY,
  FORM_UPDATION_SUB_FIELDS.COBUYER_ZIPCODE,
  FORM_UPDATION_SUB_FIELDS.LIEN_HOLDER_NAME,
  FORM_UPDATION_SUB_FIELDS.LIEN_HOLDER_ADDRESS1,
  FORM_UPDATION_SUB_FIELDS.LIEN_HOLDER_ADDRESS2,
  FORM_UPDATION_SUB_FIELDS.LIEN_HOLDER_CITY,
  FORM_UPDATION_SUB_FIELDS.LIEN_HOLDER_STATE,
  FORM_UPDATION_SUB_FIELDS.LIEN_HOLDER_ZIPCODE,
  FORM_UPDATION_SUB_FIELDS.LIEN_HOLDER_PHONE,
  FORM_UPDATION_SUB_FIELDS.LIEN_HOLDER_FAX,
  FORM_UPDATION_SUB_FIELDS.MONTHLY_PAYMENT,
  FORM_UPDATION_SUB_FIELDS.DEAL_TYPE,
  FORM_UPDATION_SUB_FIELDS.TRANSACTION_DATE,
  FORM_UPDATION_SUB_FIELDS.DAYS_TO_FIRST_PAYMENT,
  FORM_UPDATION_SUB_FIELDS.FINANCE_MANAGER_NAME,
  FORM_UPDATION_SUB_FIELDS.NO_OF_KEYS,
  FORM_UPDATION_SUB_FIELDS.NO_OF_WHEELS,
  FORM_UPDATION_SUB_FIELDS.BUYER_BUSINESS_PHONE_NUMBER,
  FORM_UPDATION_SUB_FIELDS.COBUYER_BUSINESS_PHONE_NUMBER,
  FORM_UPDATION_SUB_FIELDS.FIMANAGERID,
  FORM_UPDATION_SUB_FIELDS.CONTRACT_NUMBER,
  FORM_UPDATION_SUB_FIELDS.SRP,
  FORM_UPDATION_SUB_FIELDS.VEHICLE_MSRP,
  FORM_UPDATION_SUB_FIELDS.INSERVICE_DATE,
  FORM_UPDATION_SUB_FIELDS.PAYMENT_PLAN_PROVIDER_NAME,
];
export const FNI_CONTRACT_DOCS_USED_FOR_LABEL = __('F&I');

export const CREDIT_AGENCIES_FOR_MANDATORY_CONTRACT_SUBMISSION = ['MBCC', 'VCI', 'AUDIFS'];

export const FORM_UPDATION_CALL_TYPES = {
  UPDATE: 'UPDATE',
  DO_NOT_UPDATE: 'DO_NOT_UPDATE',
  VOID_AND_REGENERATE: 'VOID_AND_REGENERATE',
};

export const FORM_UPDATION_DATE_FIELDS = [
  FORM_UPDATION_SUB_FIELDS.EFFECTIVE_DATE,
  FORM_UPDATION_SUB_FIELDS.TRANSACTION_DATE,
  FORM_UPDATION_SUB_FIELDS.INSERVICE_DATE,
  FORM_UPDATION_SUB_FIELDS.BUYER_DOB,
];

export const ESIGN_ENABLE_R1_TOOLTIP_MSG = __(`
  Only applicable for R1 documents. Please unselect other documents to avail the option`);

export const DISABLE_SIGNED_RIC_TOOLTIP_MSG = __(`
Share is not applicable for signed RIC. Please unselect the signed RIC to avail the option`);

export const DISABLE_SIGNED_COMPLIANCE_TOOLTIP_MSG = __(`
Share is not applicable for signed Compliance documents. Please unselect the signed Compliance documents to avail the option`);

export const SIGNED_FNI_FORMS_WARNING = __(
  `Few of the documents have signed versions. Please note that this action will use the signed version of the same. do you want to proceed?`
);

export const CREDIT_COMPLIANCE_ACTIONS_DISABLED_MESSAGE = __('Not applicable for this document');

export const EXTERNAL_DOCUMENT_SOURCE_VS_CATEGORY = {
  [EXTERNAL_DOCUMENT_SOURCES.CREDIT_COMPLIANCE]: __('Compliance'),
};

export const SHARING_DROPDOWN_SECTIONS = [
  {
    label: __('Concierge'),
    sectionItems: [{ value: FORM_ACTION.SHARE_ON_CONCIERGE }, { value: FORM_ACTION.SIGN_AT_HOME }],
  },
  {
    label: __('Cast to iPad'),
    sectionItems: [{ value: FORM_ACTION.IPAD_CASTING }],
  },
];

export const FORM_SHARING_ACTIONS_VS_ICONS = {
  [FORM_ACTION.SIGN_AT_HOME]: 'icon-signature2',
  [FORM_ACTION.IPAD_CASTING]: 'icon-cast',
  [FORM_ACTION.SHARE_ON_CONCIERGE]: 'icon-concierge-shared-filled',
};

export const TABLE_COLUMNS = {
  FORM_NAME: 'formName',
  STATUS: 'status',
  NEXT_ACTION: 'nextAction',
  FORM_CATEGORY: 'formCategory',
  USED_FOR: 'usedFor',
  LAST_ACTIVITY: 'lastActivity',
  DOCUMENT: 'document',
  ACTIONS: 'actions',
  ORDER: 'printSequence',
  SPECIFICATIONS: 'specifications',
  DOC_TYPE: 'docType',
};

export const ROUTE_ONE_SIGNATURE_POINTS_SCALING_RATIO = 0.5;

export const NO_CUSTOM_FIELDS_POPOVER_MESSAGE = __('Only applicable if there are custom fields');

export const INITIAL_STATE = {
  documentPreviewDrawer: { isOpen: false, documentsList: EMPTY_ARRAY, selectedDocument: EMPTY_OBJECT },
  documentsApprovalModal: { showModal: false, preSelectedDocumentId: '' },
  documentsCount: {},
  autoSyncDocumentsEnabled: false,
  viDocumentsDetails: {
    viDocuments: EMPTY_ARRAY,
    newDocumentIds: EMPTY_ARRAY,
    removedDocumentIds: EMPTY_ARRAY,
  },
  showDTUploadModal: false,
};

export const RUNTIME_GENERATED_SHEETS_EXTERNAL_FORM_SOURCE = [
  EXTERNAL_DOCUMENT_SOURCES.DR_GENERATED_STATIC_DOCUMENTS,
  EXTERNAL_DOCUMENT_SOURCES.BUSINESS_PROPOSAL_SHEET,
  EXTERNAL_DOCUMENT_SOURCES.COVER_SHEET,
  EXTERNAL_DOCUMENT_SOURCES.CASHIERING_INVOICE_RECEIPT,
  EXTERNAL_DOCUMENT_SOURCES.DEAL_SHEET,
  EXTERNAL_DOCUMENT_SOURCES.DECLINATION_SHEET,
  EXTERNAL_DOCUMENT_SOURCES.FOUR_SQUARE_BOX_SHEET,
  EXTERNAL_DOCUMENT_SOURCES.FNI_MENU_SHEET,
  EXTERNAL_DOCUMENT_SOURCES.PURCHASE_ORDER,
  EXTERNAL_DOCUMENT_SOURCES.RECAP_SHEET,
  EXTERNAL_DOCUMENT_SOURCES.REGISTRATION_MANDATE,
  EXTERNAL_DOCUMENT_SOURCES.VEHICLE_SALES_ORDER_SHEET,
  EXTERNAL_DOCUMENT_SOURCES.ORDER_FORM_SHEET,
];

export const TABS = {
  GENERATED_DOCUMENTS: 'GENERATED_DOCUMENTS',
  UPLOADED_DOCUMENTS: 'UPLOADED_DOCUMENTS',
  VI_DOCUMENTS: 'VI_DOCUMENTS',
};

export const TABS_HEADING = {
  [TABS.GENERATED_DOCUMENTS]: __('Generated Documents'),
  [TABS.UPLOADED_DOCUMENTS]: __('Uploaded Documents'),
  [TABS.VI_DOCUMENTS]: 'VI',
};

export const DOWNLOAD_DOCUMENT_PERMISSION_WARNING = __(
  `Enable permission 'Documents Download' to download the documents`
);

export const IN_PROGRESS_DOCUMENT_STATUSES = [
  DOCUMENT_STATUS.SIGNED,
  DOCUMENT_STATUS.SIGNING_IN_PROGRESS,
  DOCUMENT_STATUS.SIGN_AT_CONCIERGE_SHARED_DIGITAL,
  DOCUMENT_STATUS.SIGN_AT_HOME_SHARED_DIGITAL,
  DOCUMENT_STATUS.SIGN_AT_iPAD_SHARED_DIGITAL,
  DOCUMENT_STATUS.SHARED_DIGITAL,
];
export const COMPLIANCE_CATEGORY = {
  key: 'COMPLIANCE',
  label: __('Compliance'),
  values: ['COMPLIANCE'],
};

export const OTHER_CATEGORY = {
  key: 'OTHERS',
  label: __('Others'),
  values: [null, undefined],
};

export const DIGITAL_CONTRACT_SIGN_OPTION_DISABLED = __(
  `User needs to be an assignee in the deal to be able to sign eligible documents`
);

export const GDPR_STATUS_TYPES = {
  DELETION_DUE: 'DELETION_DUE',
  ON_HOLD: 'ON_HOLD',
  DELETED: 'DELETED',
};

export const SIGNATURE_BANNER_IDS = {
  RFL_DOC_FAILED_BANNER: 'RFL_DOC_FAILED_BANNER',
  NON_RFL_DOC_FAILED_BANNER: 'NON_RFL_DOC_FAILED_BANNER',
};

export const CLOSABLE_SIGNATURE_BANNER_IDS = [
  SIGNATURE_BANNER_IDS.RFL_DOC_FAILED_BANNER,
  SIGNATURE_BANNER_IDS.NON_RFL_DOC_FAILED_BANNER,
];
