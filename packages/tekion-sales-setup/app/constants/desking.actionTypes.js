export default {
  MOD<PERSON><PERSON>_DEAL_LEVEL_INFO: 'M<PERSON><PERSON><PERSON>_DEAL_LEVEL_INFO',
  DESKING_DATA_RESET: 'DESKING_DATA_RESET',
  DESKING_SET_SELECTED_DEAL_DATA: 'DESKING_SET_SELECTED_DEAL_DATA',

  GET_DEAL_DATA_REQUEST: 'GET_DEAL_DATA_REQUEST',
  GET_DEAL_DATA_SUCCESS: 'GET_DEAL_DATA_SUCCESS',
  GET_DEAL_DATA_FAILURE: 'GET_DEAL_DATA_FAILURE',

  GET_MARKET_PROGRAM_INFO_SUCCESS: 'GET_MARKET_PROGRAM_INFO_SUCCESS',
  GET_MARKET_PROGRAM_INFO_FAILURE: 'GET_MARKET_PROGRAM_INFO_FAILURE',

  GET_MARKET_SCAN_DATA_REQUEST: 'GET_MARKET_SCAN_DATA_REQUEST',
  GET_MARKET_SCAN_DATA_SUCCESS: 'GET_MARKET_SCAN_DATA_SUCCESS',
  GET_MARKET_SCAN_DATA_FAILURE: 'GET_MARKET_SCAN_DATA_FAILURE',

  SET_FETCHING_MARKET_SCAN_DATA_STATUS: 'SET_FETCHING_MARKET_SCAN_DATA_STATUS',

  SET_COMMISSION_DETAILS: 'SET_COMMISSION_DETAILS',
  SET_DEAL_ASSIGNEES: 'SET_DEAL_ASSIGNEES',

  SET_SELECTED_DOWN_PAYMENT: 'SET_SELECTED_DOWN_PAYMENT',

  DESKING_REPLACE_COLUMN: 'DESKING_REPLACE_COLUMN',
  DESKING_ADD_NEW_COLUMN: 'DESKING_ADD_NEW_COLUMN',
  SET_DOWN_PAYMENTS: 'SET_DOWN_DOWNPAYMENTS',
  SET_COLUMNS_DATA: 'SET_COLUMNS_DATA',
  SET_UPDATED_COLUMN_DATA: 'SET_UPDATED_COLUMN_DATA',
  SET_SELECTED_LENDER: 'SET_SELECTED_LENDER',
  ON_VIEW_ON_DESKING_REPLACE_LENDER_WITH_NEW_LENDER_DETAILS:
    'ON_VIEW_ON_DESKING_REPLACE_LENDER_WITH_NEW_LENDER_DETAILS',
  SET_SELECTED_CASH_LENDER: 'SET_SELECTED_CASH_LENDER',
  SET_MARKET_SCAN_VEHICLE_ID_IN_DEAL: 'SET_MARKET_SCAN_VEHICLE_ID_IN_DEAL',
  CHANGE_RESIDUAL_DATA: 'CHANGE_RESIDUAL_DATA',
  CHANGE_YEARLY_MILES: 'CHANGE_YEARLY_MILES',
  SET_YEARLY_MILES: 'SET_YEARLY_MILES',
  CHANGE_DAYS_TO_FIRST_PAYMENT: 'CHANGE_DAYS_TO_FIRST_PAYMENT',
  CHANGE_APR: 'CHANGE_APR',
  CHANGE_APR_MANUAL_FLAG: 'CHANGE_APR_MANUAL_FLAG',

  DESKING_ON_DOWN_PAYMENT_CHANGE: 'DESKING_ON_DOWN_PAYMENT_CHANGE',

  SET_PAYMENT_OPTIONS: 'SET_PAYMENT_OPTIONS',
  DESKING_REMOVE_COLUMN: 'DESKING_REMOVE_COLUMN',

  DESKING_SET_DEAL_TYPE: 'DESKING_SET_DEAL_TYPE',
  DESKING_SET_DEAL_STATUS: 'DESKING_SET_DEAL_STATUS',
  DESKING_SET_DEAL_ACTIVITY: 'DESKING_SET_DEAL_ACTIVITY',
  DESKING_SET_DEAL_ACCOUNT_POSTING_ID: 'DESKING_SET_DEAL_ACCOUNT_POSTING_ID',

  UPDATE_DEAL_SUCCESS: 'UPDATE_DEAL_SUCCESS',
  UPDATE_DEAL: 'UPDATE_DEAL',
  UPDATE_DEAL_FAILURE: 'UPDATE_DEAL_FAILURE',
  DESKING_SET_PAYMENT_FREQUENCY_TYPE: 'DESKING_SET_PAYMENT_FREQUENCY_TYPE',

  DESKING_SAVE_DEFAULT_FEE: 'DESKING_SAVE_DEFAULT_FEE',

  DEAL_SET_TRADE_IN_VEHICLES: 'DEAL_SET_TRADE_IN_VEHICLES',
  DEAL_DELETE_TRADE_IN_VEHICLE: 'DEAL_DELETE_TRADE_IN_VEHICLE',

  DESKING_FEES_BULK_SAVE: 'DESKING_FEES_BULK_SAVE',
  DESKING_FEE_COLUMN_SAVE: 'DESKING_FEE_COLUMN_SAVE',
  UPDATE_DEAL_FEES: 'UPDATE_DEAL_FEES',
  DESKING_ADD_NEW_EMPTY_FEE_TO_ALL_COLUMN: 'DESKING_ADD_NEW_EMPTY_FEE_TO_ALL_COLUMN',
  DESKING_REMOVE_UNNWANTED_FEE: 'DESKING_REMOVE_UNNWANTED_FEE',

  DEAL_SET_PRIMARY_VEHICLE_DATA: 'DEAL_SET_PRIMARY_VEHICLE_DATA',
  DEAL_SET_VEHICLE_DATA: 'DEAL_SET_VEHICLE_DATA',
  DEAL_SET_PRIMARY_CUSTOMER_DATA: 'DEAL_SET_PRIMARY_CUSTOMER_DATA',

  ADD_REBATES_IN_DESKING: 'ADD_REBATES_IN_DESKING',
  MODIFY_WAIVER_REBATE_IN_DESKING: 'MODIFY_WAIVER_REBATE_IN_DESKING',
  SET_VALID_REBATES_IN_COLUMN: 'SET_VALID_REBATES_IN_COLUMN',
  MODIFY_FIRST_PAYMENT_WAIVER: 'MODIFY_FIRST_PAYMENT_WAIVER',
  MODIFY_FPW_RELATED_KEYS: 'MODIFY_FPW_RELATED_KEYS',
  ADD_REBATES_IN_SELECTED_COLUMN: 'ADD_REBATES_IN_SELECTED_COLUMN',
  SET_REBATE_FILTERS: 'SET_REBATE_FILTERS',
  SET_SCAN_VALUES: 'SET_SCAN_VALUES',

  DESKING_SAVE_FNI: 'DESKING_SAVE_FNI',
  DESKING_SAVE_ACCESSORIES: 'DESKING_SAVE_ACCESSORIES',
  DESKING_SAVE_MULTIPLE_ACCESSORIES: 'DESKING_SAVE_MULTIPLE_ACCESSORIES',

  UPDATE_DEAL_JACKET_SUCCESS: 'UPDATE_DEAL_JACKET_SUCCESS',
  UPDATE_CUSTOM_STATUS_SUCCESS: 'UPDATE_CUSTOM_STATUS_SUCCESS',
  ON_FEE_SEARCH_SUCCESS: 'ON_FEE_SEARCH_SUCCESS',
  ON_FEE_BULK_LOOKUP_SUCCESS: 'ON_FEE_BULK_LOOKUP_SUCCESS',
  SET_FNI_ATTRIBUTES_FROM_IDS: 'SET_FNI_ATTRIBUTES_FROM_IDS',
  FNI_SEARCH_SUCCESS: 'FNI_SEARCH_SUCCESS',

  SET_SELECTED_FINANCE_MANAGER: 'SET_SELECTED_FINANCE_MANAGER',
  SET_SELECTED_SALES_MANAGER: 'SET_SELECTED_SALES_MANAGER',

  SET_WORKINNG_CASH_CONFIG_IN_DEAL: 'SET_WORKINNG_CASH_CONFIG_IN_DEAL',

  FETCH_CONTRACT: 'FETCH_CONTRACT',

  SET_DEFERRED_PAYMENT_DETAILS: 'SET_DEFERRED_PAYMENT_DETAILS',
  SET_SECURITY_DEPOSIT: 'SET_SECURITY_DEPOSIT',

  SET_PAYMENT_DETAILS_IN_DEAL: 'SET_PAYMENT_DETAILS_IN_DEAL',

  REMOVE_ALL_COLUMNS: 'REMOVE_ALL_COLUMNS',
  SET_EMI_AMOUNT: 'SET_EMI_AMOUNT',
  SET_TAX_DETAILS: 'SET_TAX_DETAILS',
  DEAL_SET_BUYER_ADDRESS: 'DEAL_SET_BUYER_ADDRESS',

  SET_DMV_ROS_IN_DEAL: 'SET_DMV_ROS_IN_DEAL',

  SET_DEAL_DIRTY_STATUS: 'SET_DEAL_DIRTY_STATUS',

  SET_BASE_EMI_EMOUNT: 'SET_BASE_EMI_EMOUNT',
  RESET_TRADE_IN: 'RESET_TRADE_IN',

  DESKING_SET_CONTRACT_DATE: 'DESKING_SET_CONTRACT_DATE',

  SET_DEAL: 'SET_DEAL',
  SAVE_ACCESSORIES_FROM_RECAP: 'SAVE_ACCESSORIES_FROM_RECAP',
  RECAP_SAVE_FNI: 'RECAP_SAVE_FNI',
  RECAP_SET_FINANCE_RESERVE: 'RECAP_SET_FINANCE_RESERVE',
  GET_GL_BALANCE: 'GET_GL_BALANCE',

  // for tab switch
  DESKING_SET_ACTIVE_TAB: 'DESKING_SET_ACTIVE_TAB',

  SET_RESIDUAL_DISPLAY_TYPE: 'SET_RESIDUAL_DISPLAY_TYPE',

  SET_STATE_FEE_TAX_OPTIONS: 'SET_STATE_FEE_TAX_OPTIONS',
  FNI_PRODUCTS: 'FNI_PRODUCTS',
  SET_MULTIPLE_DOWNPAYMENT_STATUS: 'SET_MULTIPLE_DOWNPAYMENT_STATUS',
  DEAL_SET_TRADE_IN_DEAL: 'DEAL_SET_TRADE_IN_DEAL',

  SET_MARKET_SCAN_DATA: 'SET_MARKET_SCAN_DATA',
  SET_PROGRAM_IDS: 'SET_PROGRAM_IDS',
  SET_GENERIC_AND_ERROR_STATUS: 'SET_GENERIC_AND_ERROR_STATUS',

  SET_SCAN_DOCS: 'SET_SCAN_DOCS',
  SET_CUSTOMERS: 'SET_CUSTOMERS',
  SET_CONCIERGE_FLAG: 'SET_CONCIERGE_FLAG',

  FNI_GET_PRODUCTS_LIST: 'FNI_GET_PRODUCTS_LIST',

  SET_DEAL_SHEET_PRINT_PAYLOAD: 'SET_DEAL_SHEET_PRINT_PAYLOAD',
  REMOVE_PEN_CONTRACT_INFO_DEAL: 'REMOVE_PEN_CONTRACT_INFO_DEAL',
  REPLACE_PEN_CONTRACT_INFO_IN_DEAL: 'REPLACE_PEN_CONTRACT_INFO_IN_DEAL',
  FLAG_TO_SET_REMOVE_CRDIT_LIFE_PRODUCTS: 'FLAG_TO_SET_REMOVE_CRDIT_LIFE_PRODUCTS',
  SAVE_COLUMN_BASED_FNIS: 'SAVE_COLUMN_BASED_FNIS',
  GET_DEFAULT_PLAN_CODES: 'GET_DEFAULT_PLAN_CODES',

  RESET_KEYS_IN_TERM_PAYMENT_DETAILS: 'RESET_KEYS_IN_TERM_PAYMENT_DETAILS',
  SET_VALUES_IN_TERM_PMT_DETAILS: 'SET_VALUES_IN_TERM_PMT_DETAILS',
  SET_VALUES_IN_COLUMNS: 'SET_VALUES_IN_COLUMNS',
  GET_CONTRACTS_VALIDATIONS: 'GET_CONTRACTS_VALIDATIONS',

  DEAL_SET_TAX_AND_ZIP_CODE_DETAILS: 'DEAL_SET_TAX_AND_ZIP_CODE_DETAILS',
  DEAL_SET_TAX_CODE: 'DEAL_SET_TAX_CODE',
  CLEAR_CONTRACT: 'CLEAR_CONTRACT',
  SET_PEN_INFO: 'SET_PEN_INFO',
  SET_PACKAGE_DOCS: 'SET_PACKAGE_DOCS',
  SET_SPP_PACKAGE_DOCS: 'SET_SPP_PACKAGE_DOCS',

  SET_CALL_MARKET_SCAN: 'SET_CALL_MARKET_SCAN',
  SET_PDF_LOADER_DEAL_JACKET: 'SET_PDF_LOADER_DEAL_JACKET',
  SET_LEASE_CONFIGURATIONS: 'SET_LEASE_CONFIGURATIONS',
  SET_SECURITY_DEPOSIT_WAIVER_REASON: 'SET_SECURITY_DEPOSIT_WAIVER_REASON',
  SET_ECONTRACT_PACKAGES: 'SET_ECONTRACT_PACKAGES',

  SET_COLLECT_TAXES: 'SET_COLLECT_TAXES',
  SET_COLLECT_FEES: 'SET_COLLECT_FEES',

  SET_SELECTED_LENDER_TIER: 'SET_SELECTED_LENDER_TIER',

  SET_ADJUSTED_RESIDUAL: 'SET_ADJUSTED_RESIDUAL',
  UPDATE_DESKING: 'UPDATE_DESKING',
  SET_PAYMENT_TYPE: 'SET_PAYMENT_TYPE',
  SET_REBATES: 'SET_REBATES',

  SET_SELECTED_LENDER_ID: 'SET_SELECTED_LENDER_ID',
  SET_DEPOSIT_DETAILS: 'SET_DEPOSIT_DETAILS',
  SET_AUTO_REWARD_DETAILS: 'SET_AUTO_REWARD_DETAILS',
  SAVE_TAXGROUP: 'SAVE_TAXGROUP',

  SET_DEAL_PRODUCT_CONTRACT_INFOS: 'SET_DEAL_PRODUCT_CONTRACT_INFOS',

  SET_LEAD_UPDATED: 'SET_LEAD_UPDATED',

  EDOCS_FEEDBACK: 'EDOCS_FEEDBACK',

  UPDATE_SCANNED_DOCUMENT: 'UPDATE_SCANNED_DOCUMENT',

  SAVE_MANDATORY_FIELDS: 'SAVE_MANDATORY_FIELDS',

  UPDATE_MANDATORY_FIELDS: 'UPDATE_MANDATORY_FIELDS',

  UPDATE_BUYER: 'UPDATE_BUYER',

  SET_DEAL_PREFERENCES: 'SET_DEAL_PREFERENCES',

  SET_DOCUMENTS_VIEW_PREFERENCES: 'SET_DOCUMENTS_VIEW_PREFERENCES',

  UPDATE_DELIVERY_CODE: 'UPDATE_DELIVERY_CODE',

  UPDATE_STATE_FEE_TAX_METADATA: 'UPDATE_STATE_FEE_TAX_METADATA',

  SET_DEALERSHIP_INFO: 'SET_DEALERSHIP_INFO',

  SET_DESKING_PAYMENT_DETAIL_DEAL_VEHICLE_ID: 'SET_DESKING_PAYMENT_DETAIL_DEAL_VEHICLE_ID',

  SET_PREVIOUS_LENDER_ID: 'SET_PREVIOUS_LENDER_ID',

  DELETE_VEHICLE: 'DELETE_VEHICLE',

  SET_ECONTRACT_PACKAGES_WITH_SIGNATURES: 'SET_ECONTRACT_PACKAGES_WITH_SIGNATURES',

  SET_NEW_LEAD_ID: 'SET_NEW_LEAD_ID',

  RESET_REFRESH_PAYMENTS: 'RESET_REFRESH_PAYMENTS',

  CHANGE_MULTI_VEHICLE_DESKING_STATUS: 'CHANGE_MULTI_VEHICLE_DESKING_STATUS',

  SET_FNI_SURVEY_STATUS: 'SET_FNI_SURVEY_STATUS',

  SET_EDOC_FEEDBACK: 'SET_EDOC_FEEDBACK',

  SET_PROG_MAN_OVERRIDDEN: 'SET_PROG_MAN_OVERRIDDEN',

  UPDATE_DOCUMENT_STATUS: 'UPDATE_DOCUMENT_STATUS',

  DESKING_UPDATE_FNI_ON_ZIP_CHANGE: 'DESKING_UPDATE_FNI_ON_ZIP_CHANGE',

  SET_DOCUMENTS_TO_HIGHLIGHT_IN_DEALJACKET: 'SET_DOCUMENTS_TO_HIGHLIGHT_IN_DEALJACKET',
  SET_FNIS_IN_COLUMN_IDS: 'SET_FNIS_IN_COLUMN_IDS',

  SET_DEALER_TRADE_SWAP: 'SET_DEALER_TRADE_SWAP',
  SET_CHANGING_DWN_PMT: 'SET_CHANGING_DWN_PMT',
  VEHICLE_MAKE_ALIASES: 'VEHICLE_MAKE_ALIASES',
  DESKING_ON_DOWN_PAYMENT_PERCENTAGE_CHANGE: 'DESKING_ON_DOWN_PAYMENT_PERCENTAGE_CHANGE',
  UPDATE_ONE_ACCESSORY_FOR_ALL_COLUMNS: 'UPDATE_ONE_ACCESSORY_FOR_ALL_COLUMNS',

  UPDATE_ROUTEONE_ERRORS_BY_SIGNATURE_COORDINATE_IDS: 'UPDATE_ROUTEONE_ERRORS_BY_SIGNATURE_COORDINATE_IDS',
  APPLY_COLUMN_DATA: 'APPLY_COLUMN_DATA',
  SET_OPTION_CONTRACT_VALIDITY_DATE: 'SET_OPTION_CONTRACT_VALIDITY_DATE',
  SET_SHOW_ARCHIVED_DOCUMENTS_IN_DEALJACKET: 'SET_SHOW_ARCHIVED_DOCUMENTS_IN_DEALJACKET',
  SET_HIDDEN_COMPLETION_CERTIFICATES: 'SET_HIDDEN_COMPLETION_CERTIFICATES',
  UPDATE_DOCUMENT_SIGN_MODAL: 'UPDATE_DOCUMENT_SIGN_MODAL',
  SET_ESIGN_SESSION_INFO: 'SET_ESIGN_SESSION_INFO',
  CHANGE_RESIDUAL_DATA_ON_CHANGE_YEARLY_MILES: 'CHANGE_RESIDUAL_DATA_ON_CHANGE_YEARLY_MILES',
  SET_MSCAN_VEHICLE_ID_FETCHED: 'SET_MSCAN_VEHICLE_ID_FETCHED',
  UPDATE_DEAL_SYNC_MISSING_MAPPING: 'UPDATE_DEAL_SYNC_MISSING_MAPPING',
  UPDATE_DEAL_SYNC_ERRORS: 'UPDATE_DEAL_SYNC_ERRORS',

  SET_NEW_COLUMN_IDS: 'SET_NEW_COLUMN_IDS',

  SET_ESTIMATED_DELIVERY_DATE: 'SET_ESTIMATED_DELIVERY_DATE',
  SET_MSD: 'SET_MSD',
  SET_WAIVER_REASON: 'SET_WAIVER_REASON',
  RESET_WAIVER_REASON_FOR_SELECTED_COLUMN: 'RESET_WAIVER_REASON_FOR_SELECTED_COLUMN',
  RESET_MSD_FOR_SELECTED_COLUMN: 'RESET_MSD_FOR_SELECTED_COLUMN',
  SET_DMS_PROVIDER: 'SET_DMS_PROVIDER',
  UPDATE_CUSTOMER_MASKED_DATA: 'UPDATE_CUSTOMER_MASKED_DATA',
  ADD_UPDATE_DEAL_PAYMENT_OSF_LENDER: 'ADD_UPDATE_DEAL_PAYMENT_OSF_LENDER',
  SET_VIRTUAL_MEETING_ROOM_DETAILS: 'SET_VIRTUAL_MEETING_ROOM_DETAILS',
  SET_VIRTUAL_MEETING_PARTICIPANTS: 'SET_VIRTUAL_MEETING_PARTICIPANTS',
};
