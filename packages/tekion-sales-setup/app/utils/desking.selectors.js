import { createSelector } from 'reselect';
import _property from 'lodash/property';
import _get from 'lodash/get';
import _reduce from 'lodash/reduce';
import _pick from 'lodash/pick';
import _map from 'lodash/map';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { STATE_BY_MARKET_IDS } from '@tekion/tekion-base/marketScan/constants/marketScan.constants';
import * as DealReader from '@tekion/tekion-base/marketScan/readers/deal.reader';
import TEnvReader from '@tekion/tekion-base/readers/Env';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';

import { DEPARTMENTS, DEFAULT_NEW_SELLING_PRICE, TRADE_IN_MIN_VALUE } from 'constants/constants';
import { findArrayItem } from 'utils';
import * as VehicleReader from 'utils/vehicle.reader';
import * as CustomerReader from '@tekion/tekion-base/marketScan/readers/customer.reader';

import { formatLenderCodes } from '@tekion/tekion-base/marketScan/utils/desking.utils';

import { PAYMENT_TYPES, KEYS_FOR_CUSTOMER_OWNED_VEHICLE } from 'constants/desking.constants';

const { LOAN, LEASE, CASH, ONE_TIME_LEASE, BALLOON } = PAYMENT_TYPES;
// _property('genericSalesSetup.salesSetupInfo.dealTypeConfigs')

function sortLenders(lhs, rhs) {
  const { code } = lhs;
  const { code: rhsCode } = rhs;
  return code < rhsCode ? -1 : 1;
}

export const getModifiedLienLender = lender => {
  const { lienHolderAddress } = lender || EMPTY_OBJECT;

  return {
    ...lender,
    lienHolderPhone: _get(lienHolderAddress, 'phoneNumber'), // coz we need lienHolderPhone field in trade in form
    lienHolderAddress: {
      ..._get(lender, 'lienHolderAddress'),
      address1: _get(lienHolderAddress, 'address'), // coz we need address1 field in trade in form
    },
  };
};

function getModifiedLender(lender) {
  const {
    name,
    code,
    displayName,
    feeMarkup,
    address,
    lienHolderAddress,
    sameAsLender,
    preferred,
    id,
    loanSupported,
    leaseSupported,
    dispositionFee,
    dispositionThresholdValue,
    terminationThresholdValue,
    financeReserveSplitPercentage,
    codeName,
    defaultLender,
    legalName,
    marketScan,
    hidden,
    financeReserve,
    osfLender,
    enableTxFinanceCharge,
    aprMFType,
    acquisitionFeeMarkupAmt,
    removeDemoMileageFromAnnualMileage,
    useMPEqDispFeeWhenMpLtDispFee,
    securityDepositWaiverReason,
    removeDemoMileageFromAnnualMileageDeciderValue,
    firstPaymentWaivedDefaultValue,
    includeSecurityDeposits,
    removeDemoMileageFromAnnualMileageDisplayName,
    freeMilesforResidualAdjustment,
    onePay2DigitsAfterCommaMonthlyPayment,
    collectSecurityDepositWaiverReason,
    tiers,
    roundOffMileageToNearestThousand,
    vehicleMileCutOffForRemoveDemoMileage,
    totalNumberOfSecurityDeposits,
    personalPropertyTaxLenderType,
    roundSecurityDepositTo,
    useReserveSplitInCalculationForLoan,
    useReserveSplitInCalculationForLease,
    typeOfSecurityDeposit,
    actualPenaltyPerMile,
    penaltyPerMile,
    bankFeeUpfront,
    taxExemptForUseTax,
    addVehicleMilesToAnnualmiles,
    enableYearlyMilesForBalloon,
    loanMaxMarkup,
    leaseMaxMarkup,
    onePayMaxMarkup,
    fullTermBalloon,
    acquisitionFee,
    paymentDueDateAdjustment,
    oddDaysBasedOnDaysPerMonth,
    daysPerYearForOddDaysInterest,
    subventionCostType,
    subventionCostValue,
    annuityDue,
    doNotItemizeCCRTax,
    moneyFactorDecimalPoints,
    moneyFactorOperations,
    lenderDetailsForDealerTrack,
    optionContractValidityInCalendarDays,
    supportedVehicleCategory,
    tekionLenderCode,
    useDecimalPaymentCalc,
  } = lender;
  const marketScanCodes = _get(lender, 'marketScanCodes') || EMPTY_ARRAY;
  const financeReserveMethods = _get(lender, 'financeReserveMethods') || EMPTY_ARRAY;

  const paymentTypeSupported = [];
  if (loanSupported && leaseSupported) {
    paymentTypeSupported.push(LOAN, LEASE, ONE_TIME_LEASE, BALLOON);
  } else if (loanSupported) {
    paymentTypeSupported.push(LOAN, BALLOON);
  } else if (leaseSupported) {
    paymentTypeSupported.push(LEASE, ONE_TIME_LEASE);
  } else if (osfLender) {
    paymentTypeSupported.push(CASH);
  } else {
    paymentTypeSupported.push(LOAN, LEASE, ONE_TIME_LEASE, BALLOON);
  }

  return {
    code,
    label: displayName || name,
    feeMarkup,
    financeReserveMethods,
    actualPenaltyPerMile,
    penaltyPerMile,
    bankFeeUpfront,
    address,
    lienHolderAddress,
    sameAsLender,
    preferred,
    id,
    loanSupported,
    leaseSupported,
    paymentTypeSupported,
    dispositionFee,
    dispositionThresholdValue,
    terminationThresholdValue,
    financeReserveSplitPercentage,
    codeName,
    marketScanCodes: formatLenderCodes(marketScanCodes),
    defaultLender,
    legalName,
    marketScan,
    hidden,
    financeReserve,
    enableTxFinanceCharge,
    aprMFType,
    loanMaxMarkup,
    leaseMaxMarkup,
    onePayMaxMarkup,
    removeDemoMileageFromAnnualMileage,
    removeDemoMileageFromAnnualMileageDeciderValue,
    removeDemoMileageFromAnnualMileageDisplayName,
    freeMilesforResidualAdjustment,
    onePay2DigitsAfterCommaMonthlyPayment,
    firstPaymentWaivedDefaultValue,
    acquisitionFeeMarkupAmt,
    useMPEqDispFeeWhenMpLtDispFee,
    securityDepositWaiverReason,
    includeSecurityDeposits,
    collectSecurityDepositWaiverReason,
    tiers,
    roundOffMileageToNearestThousand,
    vehicleMileCutOffForRemoveDemoMileage,
    personalPropertyTaxLenderType,
    totalNumberOfSecurityDeposits,
    roundSecurityDepositTo,
    typeOfSecurityDeposit,
    useReserveSplitInCalculationForLoan,
    useReserveSplitInCalculationForLease,
    taxExemptForUseTax,
    addVehicleMilesToAnnualmiles,
    enableYearlyMilesForBalloon,
    fullTermBalloon,
    acquisitionFee,
    paymentDueDateAdjustment,
    oddDaysBasedOnDaysPerMonth,
    daysPerYearForOddDaysInterest,
    subventionCostType,
    subventionCostValue,
    annuityDue,
    doNotItemizeCCRTax,
    moneyFactorDecimalPoints,
    moneyFactorOperations,
    lenderDetailsForDealerTrack,
    optionContractValidityInCalendarDays,
    supportedVehicleCategory,
    tekionLenderCode,
    useDecimalPaymentCalc,
  };
}

function formatAndSortLenders(lendersFromServer, isMigratedDeal) {
  // function formatAndSortLenders(lendersFromServer) {
  const { preferredLenders, nonPreferredLenders, hiddenLenders, preferredDefaultLender } = _reduce(
    lendersFromServer,
    (acc, lender) => {
      const { defaultLender, preferred, hidden } = lender;
      const modifiedLender = getModifiedLender(lender);

      if (hidden) {
        if (isMigratedDeal) {
          acc.hiddenLenders.push(modifiedLender);
        }
        return acc;
      }
      if (defaultLender) {
        acc.preferredDefaultLender.push(modifiedLender);
        return acc;
      }
      if (preferred) {
        acc.preferredLenders.push(modifiedLender);
      } else {
        acc.nonPreferredLenders.push(modifiedLender);
      }

      return acc;
    },
    {
      preferredLenders: [],
      nonPreferredLenders: [],
      hiddenLenders: [],
      preferredDefaultLender: [],
    }
  );

  const preferredLendersSorted = preferredLenders.sort(sortLenders || []);
  const nonPreferredLendersSorted = nonPreferredLenders.sort(sortLenders || []);
  const hiddenLendersSorted = hiddenLenders.sort(sortLenders || []);

  const lendersInOnrder = [
    ...preferredDefaultLender,
    ...preferredLendersSorted,
    ...nonPreferredLendersSorted,
    ...hiddenLendersSorted,
  ];

  return lendersInOnrder;
}

const getLenders = state => _get(state, 'common.dealerConfig.lenders') || EMPTY_ARRAY;
const getMigrated = state => _get(state, 'desking.deal.data.migrated') || false;
export const getDealershipInfo = state => _get(state, 'desking.dealershipInfo');

const getDealerZipCode = _property('common.dealerConfig.customerConfig.zipCode');
const getDealerCreditCode = _property('common.dealerConfig.customerConfig.creditScore');
const getDealerWebsiteUris = _property('common.dealerConfig.websiteurls');
const getAnnualMiles = _property('common.dealerConfig.dealConfig.annualMiles');
const getDealerDownpayments = _property('common.dealerConfig.downPaymentConfigs');
const getOutOfStateTaxNFee = _property('common.dealerConfig.integrations.outOfStateTax');
const getDealerConfig = _property('common.dealerConfig');
export const getSalesSetupInfo = _property('common.dealerConfig');

const getGeneralTaxFeeSettings = _property('common.dealerConfig.generalConfig');

export const getLienHolderLenders = lendersFromServer =>
  _reduce(
    lendersFromServer,
    (acc, lender) => {
      const { lienHolderLender } = lender;
      if (lienHolderLender) acc.push(getModifiedLienLender(lender));

      return acc;
    },
    []
  );

// to do check with respective developer. this fun no where used. eslint error.
// const formatCostAdjustments = (costAdjustments) => {
//   const costAdjustmentsConfigStockTypes = {
//     [VEHICLE_TYPE.NEW]: [],
//     [VEHICLE_TYPE.USED]: [],
//   };
//   const allCostAdjustmentIDS = costAdjustments.map(({ costAdjustmentId }) => costAdjustmentId || EMPTY_ARRAY);

//   costAdjustments.forEach((costAdjustment) => {
//     const costAdjustmentIds = _get(costAdjustment, 'costAdjustmentId') || EMPTY_ARRAY;
//     const targetingParams = _get(costAdjustment, 'targetingParams') || EMPTY_ARRAY;
//     targetingParams.forEach((tparam) => {
//       const value = _get(tparam, 'values');
//       const operator = _get(tparam, 'operator');

//       if (operator === INCLUDE_OPERATOR && value) {
//         value.forEach((stockType) => {
//           if (costAdjustmentsConfigStockTypes[stockType]) {
//             costAdjustmentsConfigStockTypes[stockType].push(costAdjustmentIds);
//           }
//         });
//       }
//     });
//   });

//   return {
//     allCostAdjustmentIds: _flattenDeep(allCostAdjustmentIDS),
//     costAdjustmentsConfigStockTypes: _reduce(costAdjustmentsConfigStockTypes, (acc, sTypeCostAdjustmentIdsNotFlattened, stockType) => ({ ...acc, [stockType]: _uniq(_flattenDeep(sTypeCostAdjustmentIdsNotFlattened)) }), {}),
//   };
// };

export const formatDealTypes = dealTypeConfigs => {
  const dealTypes = [];
  dealTypeConfigs.forEach(dT => {
    const { dealType, displayName, enabled } = dT;
    if (enabled) {
      dealTypes.push({
        label: displayName,
        value: dealType,
      });
    }
  });
  return dealTypes;
};

export const formatDeskingFields = deskingFieldsConfigs => {
  const deskingFields = {};
  deskingFieldsConfigs.forEach(dfc => {
    const deskingFieldType = _get(dfc, 'deskingFieldType') || '';
    const displayName = _get(dfc, 'displayName') || '';
    const isEnabled = _get(dfc, 'enabled') || false;
    if (isEnabled && deskingFieldType) {
      deskingFields[deskingFieldType] = displayName;
    }
  });

  return deskingFields;
};

export const formatPrinters = (printers, allPrinters) => {
  const salesPrinter = findArrayItem(printers, 'department', DEPARTMENTS.SALES);

  const printerMappings = {};

  allPrinters.forEach(printer => {
    const printerId = _get(printer, '_id');
    printerMappings[printerId] = printer;
  });

  return {
    printers: salesPrinter || EMPTY_OBJECT,
    printerMappings,
  };
};

export const getTradeInCount = state => _get(state.common, 'dealerConfig.noOfTradeIns') || TRADE_IN_MIN_VALUE;

export const getDealerFNIs = state => _get(state.common, 'dealerConfig.fnIConfigs') || EMPTY_ARRAY;

export const getDueBills = state => _get(state.common, 'dealerConfig.dueBills') || EMPTY_ARRAY;

export const getCostAdjustments = state => _get(state.common, 'dealerConfig.costAdjustments') || EMPTY_ARRAY;

export const getDeal = state => _get(state, 'desking.deal.data') || EMPTY_OBJECT;

export const getPrinters = state => _get(state.common, 'printersInfo.mappedPrinters') || EMPTY_ARRAY;

export const getAllPrinters = state => _get(state.common, 'printersInfo.allPrinters') || EMPTY_ARRAY;

export const getDealTypes = state => _get(state.common, 'dealerConfig.dealTypeConfigs') || EMPTY_ARRAY;

export const getDealCustomStatuses = state => _get(state.common, 'dealerConfig.customStatuses') || EMPTY_ARRAY;

export const getDeskingFields = state => _get(state.common, 'dealerConfig.deskingFieldConfigs') || EMPTY_ARRAY;

export const getDeskingPaymentDetails = state => _get(state, 'desking.deskingpaymentDetails');

export const getTaxAndFeeMetadata = state => _get(state, 'desking.taxFeeConfigMetadata');

export const getDownPayments = state => _get(state, 'desking.downPayments');

export const getDefaultssheetConfigs = state => _get(state.common, 'dealerConfig.sheetConfigs') || EMPTY_OBJECT;

export const getShowPdfLoaderDealJacket = state => _get(state, 'desking.showPdfLoaderDealJacket');

export const getEstimatedDeliveryDateChanged = state => _get(state, 'desking.hasEstimatedDeliveryDateChanged');

export const getDocumentsViewPreferences = state => _get(state, 'desking.documentsViewPreferences');

export const getRebateFilters = state => _get(state, 'desking.deal.data.rebateFilters') || EMPTY_OBJECT;

export const getVehicleType = state => {
  const deal = getDeal(state);
  const primaryVehicleInDeal = DealReader.getPrimaryVehicle(deal);
  const vehicleType = VehicleReader.getVehicleType(primaryVehicleInDeal);
  return vehicleType;
};

export const selectLendersFromDealerConfig = createSelector(
  [getLenders, getMigrated],
  // getLenders,
  formatAndSortLenders
);

export const selectLienHolderLenders = createSelector(getLenders, getLienHolderLenders);

export const selectDealerZipCode = createSelector(getDealerZipCode, zipCode => zipCode || EMPTY_STRING);

export const selectDealerCreditScore = createSelector(getDealerCreditCode, creditScore => creditScore || EMPTY_STRING);

export const selectDealerWebsiteUris = createSelector(getDealerWebsiteUris, websiteurls => websiteurls || EMPTY_OBJECT);

export const selectYearlyMiles = createSelector(getAnnualMiles, annualMiles => annualMiles || EMPTY_STRING);

export const generalTaxFeeSettings = createSelector(
  getGeneralTaxFeeSettings,
  generalTaxFeeSettingsData => generalTaxFeeSettingsData || EMPTY_OBJECT
);

export const selectDefaultDownpayments = createSelector(
  getDealerDownpayments,
  dealerDownpayments => dealerDownpayments || EMPTY_ARRAY
);

export const selectDealTypes = createSelector(getDealTypes, dealTypes => formatDealTypes(dealTypes) || EMPTY_ARRAY);

export const selectDeskingFields = createSelector(
  getDeskingFields,
  deskingFields => formatDeskingFields(deskingFields) || EMPTY_OBJECT
);

function getSellPriceForNewCars(state) {
  return _get(state, ['common', 'dealerConfig', 'dealConfig', DEFAULT_NEW_SELLING_PRICE]);
}

export const selectDefaultNewSellPrice = createSelector(getSellPriceForNewCars, newSellPrice => ({
  [DEFAULT_NEW_SELLING_PRICE]: newSellPrice,
}));

export const selectCostAdjustments = createSelector(
  getCostAdjustments,
  // costAdjustments => formatCostAdjustments(costAdjustments) || EMPTY_ARRAY
  costAdjustments => costAdjustments || EMPTY_ARRAY
);

export const selectPrinters = createSelector(
  getPrinters,
  getAllPrinters,
  (printers, allPrinters) => formatPrinters(printers, allPrinters) || EMPTY_OBJECT
);

export const getDealerInfo = state => _get(state, 'common.dealerInfo');

export const getVehicleList = state => _get(state, 'common.vehicles');

export const getDeferredPayments = state => _get(state, 'desking.deferredPayments') || EMPTY_OBJECT;

export const selectOutOfStateTaxNFee = createSelector(
  getOutOfStateTaxNFee,
  outOfStateTaxNFee => outOfStateTaxNFee || EMPTY_ARRAY
);

export function isIntegrationEnabled(integrations, type) {
  return _get(integrations, [type, 'enabled']) || false;
}

export function getIntegrationValue(integrations, type) {
  return _get(integrations, [type, 'value']) || '';
}

export const selectPackageDocs = createSelector(
  _property('desking.packageDocuments'),
  packageDocuments => packageDocuments
);

export const selectSppPackageDocs = createSelector(
  _property('desking.sppPackageDocuments'),
  sppPackageDocuments => sppPackageDocuments
);

export const getAllDealForms = state => (
  DealerPropertyHelper.isEnhancedDocumentsTabEnabled()
    ? _get(state, 'documentsV2.allDealForms')
    : _get(state, 'documents.allDealForms'),
  allDealForms => allDealForms
);

export const selectScanDocs = createSelector(
  _property('desking.scanDocuments'),
  scanDocuments => scanDocuments.documents
);

export const selectDocsetData = createSelector(
  _property('desking.scanDocuments'),
  scanDocuments => scanDocuments.docsetData
);

export const selectdefaultsheetConfigs = createSelector(
  getDefaultssheetConfigs,
  defaultsheetConfigs => defaultsheetConfigs || EMPTY_OBJECT
);

export function getVehicleMake(state) {
  const deal = getDeal(state);
  const primaryVehicleInDeal = DealReader.getPrimaryVehicle(deal);
  const { make } = VehicleReader.getYMM(primaryVehicleInDeal);
  return make;
}

export function getTradeIns(state) {
  const deal = getDeal(state);
  const tradeIns = _get(deal, 'tradeIns') || EMPTY_ARRAY;
  return tradeIns;
}

export function getCustomerState(state) {
  const deal = getDeal(state);
  const primaryCustomer = DealReader.getBuyer(deal);
  const marketId = CustomerReader.getCurrentMarketIdOfBuyerInDeal(primaryCustomer);
  return STATE_BY_MARKET_IDS[marketId];
}

export const getOwnerType = createSelector(getDealerConfig, dealerConfig => _get(dealerConfig, 'ownerType'));

export const getVehiclesOwnedByCustomers = state => {
  const deal = getDeal(state);
  const buyerId = _get(DealReader.getBuyer(deal), 'customerId');
  const coBuyerId = _get(DealReader.getCoBuyer(deal), 'customerId');
  const buyerVehicles = _get(state, ['common', 'customers', buyerId, 'data', 'vehicleInfo']) || EMPTY_ARRAY;
  const coBuyerVehicles = _get(state, ['common', 'customers', coBuyerId, 'data', 'vehicleInfo']) || EMPTY_ARRAY;
  return [...buyerVehicles, ...coBuyerVehicles].map(vehicle => _pick(vehicle, KEYS_FOR_CUSTOMER_OWNED_VEHICLE));
};

export const getVehicleTypes = state => _get(state, 'common.viSettings.typeSetting.vehicleTypes') || EMPTY_ARRAY;

export const getVehicleStockSubTypes = createSelector(getVehicleTypes, stockTypes =>
  _reduce(
    stockTypes,
    (allSubTypes, { subTypes }) => [...allSubTypes, ..._map(subTypes, ({ name }) => ({ label: name, value: name }))],
    EMPTY_ARRAY
  )
);

const getAllDealerSites = _property('common.dealerSites');

export const getDealerSites = createSelector(getAllDealerSites, allDealerSites => {
  const userInfo = TEnvReader.userInfo();
  const { dealerId } = userInfo;

  return _get(allDealerSites, [dealerId]) || [];
});

export const getDocumentsToHighlightInDealJacket = state => _get(state, 'desking.documentsToHighlightInDealJacket');

export const selectMandatoryReviewFields = state =>
  _get(state, 'desking.mandatoryReviewFields.mandatoryAndReviewFieldsResponse') || EMPTY_OBJECT;

export const selectBuyerDetails = createSelector(getDeal, deal => DealReader.getBuyer(deal));

export const getRouteOneErrorsBySignatureCoordinateIds = state => _get(state, 'desking.routeOneErrors') || EMPTY_OBJECT;

export const getShowArchivedDocuments = state => _get(state, 'desking.showArchivedDocsInDealJacket');

export const getFormUpdationModalConfig = state =>
  (DealerPropertyHelper.isEnhancedDocumentsTabEnabled()
    ? _get(state, 'documentsV2.formUpdationModalConfig')
    : _get(state, 'documents.formUpdationModalConfig')) || EMPTY_OBJECT;

export const selectFormsToBeRetained = state =>
  (DealerPropertyHelper.isEnhancedDocumentsTabEnabled()
    ? _get(state, 'documentsV2.formsToBeRetained')
    : _get(state, 'documents.formsToBeRetained')) || EMPTY_ARRAY;

export const getSelectedFormsForFormUpdation = state =>
  (DealerPropertyHelper.isEnhancedDocumentsTabEnabled()
    ? _get(state, 'documentsV2.selectedFormsForFormUpdation')
    : _get(state, 'documents.selectedFormsForFormUpdation')) || EMPTY_ARRAY;

export const getAllForms = state =>
  (DealerPropertyHelper.isEnhancedDocumentsTabEnabled()
    ? _get(state, 'documentsV2.allForms')
    : _get(state, 'documents.allForms')) || EMPTY_ARRAY;

export const getPreviewURL = state =>
  (DealerPropertyHelper.isEnhancedDocumentsTabEnabled()
    ? _get(state, 'documentsV2.previewURL')
    : _get(state, 'documents.previewURL')) || EMPTY_OBJECT;

export const getSelectedFormId = state =>
  DealerPropertyHelper.isEnhancedDocumentsTabEnabled()
    ? _get(state, 'documentsV2.selectedFormId')
    : _get(state, 'documents.selectedFormId');

export const getDocumentsPushPartnerList = state =>
  _get(state, 'common.dealerConfig.integrations.documentPushPartnerList') || EMPTY_ARRAY;

export const getDealJacketGdprConfig = state => _get(state, 'desking.dealJacketGdprConfig') || EMPTY_OBJECT;

export const getVMDetails = state => _get(state, 'desking.virtualMeetingRoomDetails') || EMPTY_OBJECT;

export const getVMParticipantsList = state =>
  _get(state, 'desking.virtualMeetingRoomDetails.participants') || EMPTY_ARRAY;
