import { defaultMemoize } from 'reselect';

import _map from 'lodash/map';
import _get from 'lodash/get';
import _reject from 'lodash/reject';
import _forEach from 'lodash/forEach';
import _find from 'lodash/find';
import _isEmpty from 'lodash/isEmpty';
import _includes from 'lodash/includes';
import _reduce from 'lodash/reduce';
import _castArray from 'lodash/castArray';

import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import {
  PRINT_ASSET_TYPES,
  PRINT_ASSET_TYPES_VS_SHEET_TYPE,
  PRINT_ASSET_TYPE_DISPLAY_NAMES,
  USE_DISPLAY_NAME_FOR_ASSETS,
} from '@tekion/tekion-base/marketScan/constants/printAssetTypes.constants';
import { ACTIVITY_ASSET_TYPES } from '@tekion/tekion-base/marketScan/constants/desking.constants';
import FileReader from '@tekion/tekion-base/utils/File.reader';
import { getAssetTypeFromSheetType } from '@tekion/tekion-base/marketScan/readers/desking.reader';

import { DEAL_JACKET } from 'constants/deal.constants';
import dealJacketDocumentReader from 'readers/dealJacketDocument.reader';

import { getRandomId } from 'utils';

export const getModifiedNames = defaultMemoize((documents, dealNumber) => {
  const sortedDocuments = documents.sort((a, b) => Number(_get(a, 'createdTime') - Number(_get(b, 'createdTime')))); // Ascending order
  return _map(sortedDocuments, document => {
    const assetType = getAssetTypeFromSheetType(document);
    const assetDisplayName = _includes(USE_DISPLAY_NAME_FOR_ASSETS, assetType)
      ? PRINT_ASSET_TYPE_DISPLAY_NAMES[assetType]
      : assetType;
    const extension = FileReader.getFileExtension(_get(document, 'name'));
    const isPackageDocument = DEAL_JACKET.PACKAGE === _get(document, 'category');
    const isFileRenamed = _get(document, 'fileRenamed') || false;
    if (assetDisplayName && !isPackageDocument && !isFileRenamed) {
      return {
        ...document,
        name: `${assetDisplayName}_${dealNumber}.${extension}`,
      };
    }
    return document;
  });
});

export const getFiltererdDocuments = documents => _reject(documents, 'deleted');

export const addIdIfNotPresent = documents =>
  _map(documents, document => ({
    ...document,
    id: _get(document, 'id') || getRandomId(),
  }));

export const getRouteOneAndNonRouteOneForms = forms => {
  const routeOneForms = [];
  const nonRouteOneForms = [];
  _forEach(forms, _data => {
    if (_data.routeOne) {
      routeOneForms.push(_data);
    } else {
      nonRouteOneForms.push(_data);
    }
  });
  return { routeOneForms, nonRouteOneForms };
};

export const getRegeneratedAndFailedProducts = ({
  contractFormResponseDOList,
  formsToBeRetained,
  selectedFormsForFormUpdation,
}) => {
  const regeneratedPenForms = [];
  const failedPenForms = [];
  _forEach(contractFormResponseDOList, contractInfo => {
    const { productId: contractProductId, providerId: contractProviderId, error } = contractInfo;
    const errorMessage = _get(error, 'errorMessage');
    const fniForm = _find(
      formsToBeRetained,
      ({ productId, providerId, penForm }) =>
        contractProductId === productId && contractProviderId === providerId && penForm
    );

    if (!_isEmpty(fniForm)) {
      const { formId, productDisplayName } = fniForm;
      if (_includes(selectedFormsForFormUpdation, formId)) {
        if (_isEmpty(errorMessage)) {
          regeneratedPenForms.push(formId);
        } else if (_includes(errorMessage, 'Voiding of existing contract failed')) {
          // Adding specific string check to differentiate Void action fail.
          failedPenForms.push({
            formId,
            productDisplayName,
            error: `The F&I Product Void Failed for ${productDisplayName}, kindly void the same from F&I Provider's portal`,
          });
        } else {
          failedPenForms.push({ formId, productDisplayName, error: errorMessage });
        }
      }
    }
  });

  return { regeneratedPenForms, failedPenForms };
};

export const separateCompletionCertsFromDealJacket = documents =>
  _reduce(
    documents,
    (filteredDocuments, document) => {
      const isCompletionCertificate = _get(document, ['certificateOfCompletionDetails', 'certOfCompletion'], false);
      if (isCompletionCertificate) {
        filteredDocuments.completionCertificates.push(document);
        return filteredDocuments;
      }

      filteredDocuments.documentsWithoutCompletionCertificates.push(document);
      return filteredDocuments;
    },
    { documentsWithoutCompletionCertificates: [], completionCertificates: [] }
  );

export const getAssetTypeFromDocument = document => {
  const name = _get(document, 'name') || '';

  switch (true) {
    case name.includes(PRINT_ASSET_TYPES.MISCELLANEOUS_INVOICE_SHEET):
      return PRINT_ASSET_TYPES.MISCELLANEOUS_INVOICE_SHEET;

    default:
      return null;
  }
};

export const getDocumentIdBasedOnFromSheet = ({ dealNumber = '', document = EMPTY_OBJECT }) => {
  let formId = dealJacketDocumentReader.FORM_ID(document);
  if (!_isEmpty(formId)) {
    return formId;
  }

  const assetType = _get(document, 'assetType');
  formId = `${dealNumber}_${PRINT_ASSET_TYPES_VS_SHEET_TYPE[assetType]}`;
  return formId;
};

export const getUpdateFormPrintStatusPaylod = ({ dealNumber = '', formIds = EMPTY_ARRAY }) => ({
  assetId: dealNumber,
  assetType: ACTIVITY_ASSET_TYPES.DEAL,
  formIdList: _castArray(formIds),
});
