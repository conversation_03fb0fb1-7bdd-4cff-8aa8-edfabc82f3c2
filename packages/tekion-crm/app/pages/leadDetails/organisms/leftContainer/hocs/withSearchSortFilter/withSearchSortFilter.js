import React, { useEffect, useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import { compose } from 'recompose';
import { connect } from 'react-redux';
import _find from 'lodash/find';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _keys from 'lodash/keys';
import _noop from 'lodash/noop';
import _reduce from 'lodash/reduce';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from 'tbase/app.constants';
import OPERATORS from 'tbase/constants/filterOperators';
import { getBaseUrl } from 'tbase/helpers/crm/getUrlType.helpers';
import { tget, getQueryParams } from 'tbase/utils/general';
import isStringEmpty from 'tbase/utils/isStringEmpty';
import DEALER_PROPERTIES from 'tbase/constants/dealerProperties';
import { CUSTOM_FETCH_COLUMNS_ENDPOINT } from 'tbusiness/appServices/crm/constants/service';
import Spinner from 'tcomponents/molecules/SpinnerComponent';
import withUserPreferenceColumn from 'tcomponents/connectors/withUserPreferenceColumn';
import withSalesMetadata from '@tekion/tekion-widgets/src/hocs/withSalesMetadata';
import { DEFAULT_FILTER_BEHAVIOR } from '@tekion/tekion-components/src/organisms/filterSection/constants/filterSection.constants';
import withFilterGroup from 'twidgets/hocs/withFilterGroup';
import { useTekionConversion } from '@tekion/tekion-conversion-web';

import { FILTER_IDS } from 'constants/filters';
import { getFilterProps } from 'helpers/filters';
import { getLanguageOptions } from 'selectors';
import useColumnMetaData from 'hooks/useColumnMetaData';
import useFilterChange from 'hooks/useFilterChange';
import useSearchSortChange from 'hooks/useSearchSortChange';
import useGenerateInitialFilters from 'hooks/useGenerateInitialFiltersAndSort';

import { getIsLegacyAssigneeEnabled } from '../../../../../../constraints/legacyAssignee';
import { getLegacyOptions } from '../../../../../../selectors/legacyAssignee';
import { getAllColumnsWithSortedField } from './helpers/withSearchSortFilter.columns';
import { addSiteFiltersToSelectedFilters, generateFilters } from './helpers/withSearchSortFilter.filters';
import { getSortFromSortDetails } from '../../../../../../utils/table';
import useFilterActions from '../../hooks/useFilterActions';
import { getCachedSortDetailsSelector, getCachedSortSelector } from './selectors/withSortFilterSections.general';
import { getCachedSortDetails } from './helpers/withSearchSortFilter.getCachedSort';
import { getAdditionalInfoConfig, getParsedSearchText } from './helpers/withSearchSortFilter.general';
import {
  getCachedSelectedFilters,
  getCachedSelectedFiltersGroup,
  getMetaData,
  getSearchText,
} from './selectors/withSearchSortFilter.getCachedContent';
import { getEntityConfig } from './helpers/withSearchSortFilter.entityConfig';
import LeftContainerSkeleton from '../../../../molecules/leftContainerSkeleton';

const entityConfig = getEntityConfig();

const WithSearchSortFilter =
  ({ assetType, queryParams }) =>
  WrappedComponent => {
    const SortFilterSection = props => {
      const {
        getSources,
        navigate,
        location,
        settings,
        columns,
        allColumns,
        getFilterGroupByKey,
        cachedSelectedFilters,
        cachedSelectedFilterGroup,
        data,
        getMakes,
        getModels,
        makes,
        searchText: searchString,
        sort: cachedSort,
        useContent,
        metaData: cachedMetaData,
        languageOptions,
        getSourceGroups,
        includeSiteFilter,
        legacyOptions,
        dealStatusOptions,
        leadStatusOptions,
        getDealerPropertyValue,
        cachedSortDetails,
        alwaysRenderDefaultToolBar,
        onFetchEntityData,
        resetMetaDataByKey,
        paymentOptionConfigs,
        updateRouteUrl,
        getLeadStatus,
        getDealStatus,
        assigneeOptionsByGroup,
        getAssigneeOptionsByGroup,
        shouldShowPersonaGroups,
        fetchLegacyOptions,
      } = props;
      const { getMeasureUnitLabel } = useTekionConversion();
      const customDealStatuses = tget(settings, 'salesSetup.customStatuses');
      const isCustomerOneViewEnabled = getDealerPropertyValue(DEALER_PROPERTIES.CRM_CUSTOMER_ONEVIEW_ENABLED);

      const initialCachedSortDetails = useMemo(
        () => getCachedSortDetails(cachedSortDetails, useContent),
        [cachedSortDetails, useContent]
      );

      const shouldConsiderCachedData = useMemo(
        () => useContent && !_isEmpty(cachedMetaData),
        [cachedMetaData, useContent]
      );

      const parsedSearchText = useMemo(() => getParsedSearchText(searchString, useContent), [searchString, useContent]);

      const { columnMetaData } = useColumnMetaData({ cachedMetaData, assetType });

      const { selectedFilters, selectedFilterGroup, handleFilterChange, applyInitFilter } = useFilterChange({
        cachedSelectedFilters,
        cachedSelectedFilterGroup,
        assetType,
        getFilterGroupByKey,
      });

      const { shouldFetchAdditionalInfo, fetchAdditionalInfoFn } = getAdditionalInfoConfig({
        assetType,
        shouldShowPersonaGroups,
        getAssigneeOptionsByGroup,
      });

      const { models, onFilterAction } = useFilterActions({
        getMakes,
        assetType,
        getModels,
        selectedFilters,
        getLeadStatus,
        getDealStatus,
        shouldFetchAdditionalInfo,
        fetchAdditionalInfoFn,
        additionalProps: {
          getDealerPropertyValue,
        },
        fetchLegacyOptions,
      });

      const { searchText, setSearchText, sortDetails, onSortChange, onSearchTextChange, applyInitSort } =
        useSearchSortChange({
          searchString: parsedSearchText,
          cachedSortDetails: initialCachedSortDetails,
          columnMetaData,
          getDealerPropertyValue,
        });

      const allSortColumns = useMemo(
        () => getAllColumnsWithSortedField(allColumns, columnMetaData),
        [columnMetaData, allColumns]
      );

      const { onInitialFilterResolve, onInitialSortResolve, initialFilters, isInitialFilterAndSortCreating } =
        useGenerateInitialFilters({
          navigate,
          location,
          assetType,
          cachedMetaData,
          getFilterGroupByKey,
          cachedSelectedFilterGroup,
          cachedSelectedFilters,
          shouldConsiderCachedData,
          applyInitFilter,
          applyInitSort,
          getDealerPropertyValue,
          useContent,
          cachedSort,
          cachedSortDetails,
          queryParams,
          updateRouteUrl,
        });

      useEffect(() => {
        const shouldFetchEntityData = !_isEmpty(initialFilters) && !isInitialFilterAndSortCreating;
        if (shouldFetchEntityData) {
          const { filters, sd } = initialFilters || {};
          fetchEntityData(filters, sd, true);
        }
      }, [initialFilters, isInitialFilterAndSortCreating, fetchEntityData]);

      const filterProps = useMemo(() => {
        const selectedFiltersWithDefault = addSiteFiltersToSelectedFilters({
          includeSiteFilter,
          selectedFilters,
          assetType,
        });
        return getFilterProps(
          {
            filters: entityConfig[assetType]?.filterGetter({
              sources: getSources(),
              makes,
              models,
              settings,
              columnMetaData,
              customDealStatuses,
              columns: allColumns,
              languageOptions,
              sourceGroups: getSourceGroups(),
              isCustomerOneViewEnabled,
              legacyOptions,
              isLegacyAssigneeEnabled: getIsLegacyAssigneeEnabled(),
              dealStatusOptions,
              leadStatusOptions,
              getDealerPropertyValue,
              getMeasureUnitLabel,
              shouldShowDistanceFilter: true,
              paymentOptionConfigs,
              assigneeOptionsByGroup,
              shouldShowPersonaGroups,
            }),
            assetType,
            selectedFilters: selectedFiltersWithDefault,
            selectedFilterGroup,
            shouldForwardAction: true,
            onAction: onFilterAction,
            alwaysRenderDefaultToolBar,
            additional: entityConfig[assetType]?.additionalFilterProps || EMPTY_OBJECT,
            defaultFilterBehavior: DEFAULT_FILTER_BEHAVIOR.PERSIST_LOCAL,
          },
          {
            [FILTER_IDS.ASSIGNEE]: [OPERATORS.IN, OPERATORS.NIN],
            [FILTER_IDS.CREATED_BY]: [OPERATORS.IN, OPERATORS.NIN],
            [FILTER_IDS.APPOINTMENT_ASSIGNEE]: [OPERATORS.IN, OPERATORS.NIN],
            ...(entityConfig[assetType]?.filterCountConfig || EMPTY_OBJECT),
          }
        );
      }, [
        makes,
        models,
        settings,
        columnMetaData,
        allColumns,
        customDealStatuses,
        selectedFilters,
        selectedFilterGroup,
        onFilterAction,
        languageOptions,
        getSourceGroups,
        getSources,
        isCustomerOneViewEnabled,
        includeSiteFilter,
        legacyOptions,
        dealStatusOptions,
        leadStatusOptions,
        getDealerPropertyValue,
        alwaysRenderDefaultToolBar,
        queryParams,
        assigneeOptionsByGroup,
        shouldShowPersonaGroups,
      ]);

      const handleFilterApply = useCallback(
        async (values, selectedFilterGroupParam, additional = EMPTY_OBJECT) => {
          const filters = await handleFilterChange(values, selectedFilterGroupParam, additional);
          if (filters) {
            resetMetaDataByKey(['checkContactAssignee']);
            fetchEntityData(filters, sortDetails);
          }
        },
        [fetchEntityData, sortDetails, handleFilterChange, resetMetaDataByKey]
      );

      const fetchEntityData = useCallback(
        (finalSelectedFilters, sortMap, isInitialFetch = false) => {
          onFetchEntityData({
            filters: generateFilters(finalSelectedFilters),
            searchText,
            isInitialFetch,
            sort: getSortFromSortDetails(sortMap, columnMetaData),
          });
        },
        [searchText, columnMetaData, onFetchEntityData]
      );

      useEffect(() => {
        const params = getQueryParams({ location });
        const queryParamSearchText = _get(params, 'searchText');
        if (!isStringEmpty(queryParamSearchText)) {
          setSearchText(queryParamSearchText);
        }
      }, [location, setSearchText]);

      const handleApplySearch = useCallback(
        (inputSearchText, sort) => {
          onFetchEntityData({
            searchText: inputSearchText,
            filters: generateFilters(selectedFilters),
            sort,
          });
        },
        [selectedFilters, onFetchEntityData]
      );

      const handleSearchTextChange = useCallback(
        inputSearchText => {
          if (isStringEmpty(inputSearchText)) {
            const { sort, searchText: updatedSearchText } = onSearchTextChange(inputSearchText);
            handleApplySearch(updatedSearchText, sort);
          } else {
            setSearchText(inputSearchText);
          }
        },
        [onSearchTextChange, handleApplySearch, setSearchText]
      );

      const onPressEnter = useCallback(() => {
        const { sort, searchText: updatedSearchText } = onSearchTextChange(searchText);
        handleApplySearch(updatedSearchText, sort);
      }, [searchText, onSearchTextChange, handleApplySearch]);

      const onApplySort = useCallback(
        sortInfo => {
          onSortChange(sortInfo);
          fetchEntityData(selectedFilters, sortInfo);
        },
        [selectedFilters, onSortChange]
      );

      const getUpdatedSortDetails = useCallback(
        (prev, curr) => {
          if (_find(allSortColumns, { key: curr })) {
            return { ...prev, [curr]: sortDetails[curr] };
          }
          return prev;
        },
        [allSortColumns, sortDetails]
      );

      const _sortDetails = useMemo(
        () => _reduce(_keys(sortDetails), getUpdatedSortDetails, EMPTY_OBJECT),
        [sortDetails, getUpdatedSortDetails]
      );

      if (_isEmpty(columnMetaData)) {
        return <LeftContainerSkeleton />;
      }

      return (
        <WrappedComponent
          {...props}
          searchText={searchText}
          handleSearchTextChange={handleSearchTextChange}
          onPressEnter={onPressEnter}
          handleFilterApply={handleFilterApply}
          onInitialFilterResolve={onInitialFilterResolve}
          onInitialSortResolve={onInitialSortResolve}
          onApplySort={onApplySort}
          sortDetails={_sortDetails}
          columns={columns}
          selectedFilterGroup={selectedFilterGroup}
          allSortColumns={allSortColumns}
          shouldConsiderCachedData={shouldConsiderCachedData}
          filterProps={filterProps}
          data={data}
          assetType={assetType}
          filters={generateFilters(selectedFilters)}
          columnMetaData={columnMetaData}
        />
      );
    };

    SortFilterSection.propTypes = {
      assetType: PropTypes.string.isRequired,
      getSources: PropTypes.func.isRequired,
      includeSiteFilter: PropTypes.bool,
      settings: PropTypes.object.isRequired,
      columns: PropTypes.array,
      allColumns: PropTypes.array,
      getFilterGroupByKey: PropTypes.func,
      cachedSelectedFilters: PropTypes.array,
      cachedSelectedFilterGroup: PropTypes.string,
      data: PropTypes.array,
      getMakes: PropTypes.func.isRequired,
      getModels: PropTypes.func.isRequired,
      makes: PropTypes.array.isRequired,
      languageOptions: PropTypes.array,
      getSourceGroups: PropTypes.func,
      legacyOptions: PropTypes.array.isRequired,
      dealStatusOptions: PropTypes.array,
      leadStatusOptions: PropTypes.array,
      getDealerPropertyValue: PropTypes.func,
      searchText: PropTypes.string,
      sort: PropTypes.array,
      metaData: PropTypes.object,
      resetMetaDataByKey: PropTypes.func,
      useContent: PropTypes.bool,
      cachedSortDetails: PropTypes.object,
      alwaysRenderDefaultToolBar: PropTypes.bool,
      onFetchEntityData: PropTypes.func,
      paymentOptionConfigs: PropTypes.array,
      getDealStatus: PropTypes.func,
      getLeadStatus: PropTypes.func,
      updateRouteUrl: PropTypes.bool,
      shouldShowPersonaGroups: PropTypes.bool,
      assigneeOptionsByGroup: PropTypes.array,
      getAssigneeOptionsByGroup: PropTypes.func,
      fetchLegacyOptions: PropTypes.func,
    };

    SortFilterSection.defaultProps = {
      columns: EMPTY_ARRAY,
      allColumns: EMPTY_ARRAY,
      getFilterGroupByKey: _noop,
      includeSiteFilter: true,
      cachedSelectedFilters: EMPTY_ARRAY,
      cachedSelectedFilterGroup: EMPTY_STRING,
      data: EMPTY_ARRAY,
      languageOptions: EMPTY_ARRAY,
      getSourceGroups: _noop,
      dealStatusOptions: EMPTY_ARRAY,
      leadStatusOptions: EMPTY_ARRAY,
      getDealerPropertyValue: _noop,
      searchText: undefined,
      sort: EMPTY_ARRAY,
      metaData: EMPTY_OBJECT,
      resetMetaDataByKey: _noop,
      useContent: false,
      cachedSortDetails: EMPTY_OBJECT,
      alwaysRenderDefaultToolBar: false,
      onFetchEntityData: _noop,
      paymentOptionConfigs: EMPTY_ARRAY,
      getLeadStatus: _noop,
      getDealStatus: _noop,
      updateRouteUrl: false,
      shouldShowPersonaGroups: false,
      assigneeOptionsByGroup: EMPTY_ARRAY,
      getAssigneeOptionsByGroup: _noop,
      fetchLegacyOptions: _noop,
    };

    const mapStateToProps = state => {
      const reducerKey = entityConfig[assetType]?.reducer;
      const legacyOptions = getLegacyOptions(state);
      const languageOptions = getLanguageOptions(state);
      const sort = getCachedSortSelector(reducerKey)(state);
      const cachedSortDetails = getCachedSortDetailsSelector(reducerKey)(state);
      const cachedSelectedFilters = getCachedSelectedFilters(reducerKey)(state);
      const cachedSelectedFilterGroup = getCachedSelectedFiltersGroup(reducerKey)(state);
      const searchText = getSearchText(reducerKey)(state);
      const metaData = getMetaData(reducerKey)(state);

      return {
        cachedSelectedFilters,
        cachedSelectedFilterGroup,
        sort,
        cachedSortDetails,
        searchText,
        legacyOptions,
        metaData,
        languageOptions,
      };
    };

    return compose(
      withSalesMetadata({ shouldInit: false }),
      withUserPreferenceColumn(
        assetType,
        EMPTY_ARRAY,
        EMPTY_OBJECT,
        true,
        false,
        EMPTY_OBJECT,
        CUSTOM_FETCH_COLUMNS_ENDPOINT[assetType](),
        true,
        undefined,
        true,
        false,
        { getBaseUrl },
        null
      ),
      withFilterGroup(assetType),
      connect(mapStateToProps)
    )(SortFilterSection);
  };

export default WithSearchSortFilter;
