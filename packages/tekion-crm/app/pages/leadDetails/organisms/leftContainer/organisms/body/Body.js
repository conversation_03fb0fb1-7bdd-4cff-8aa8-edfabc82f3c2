import { useNavigate } from 'react-router-dom';
import React, { useMemo, useState, useCallback, useEffect } from 'react';
import PropTypes from 'prop-types';
import _isEmpty from 'lodash/isEmpty';
import _noop from 'lodash/noop';
import _size from 'lodash/size';

import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import Label from 'tcomponents/atoms/Label';
import Illustration from 'tcomponents/molecules/illustration';
import { useTekionConversion } from '@tekion/tekion-conversion-web';

import ASSETS from 'constants/s3Assets';
import { ENTITY_TYPE_VS_RENDERER } from './constants/body.general';
import FilterSortSearchSection from './organisms/filterSortSearchSection';
import useScrolling from '../../hooks/useScrolling/useScrolling';
import ENTITY_COUNT_LABEL_CONFIG from './constants/body.entityConfig';
import { LeftContainerSkeletonBody } from '../../../../molecules/leftContainerSkeleton';

import styles from './body.module.scss';

function Body(props) {
  const navigate = useNavigate();
  const {
    entityType,
    data,
    current,
    isFetching,
    totalCount,
    filters,
    searchText,
    sortDetails,
    columnMetaData,
    onFetchEntityData,
    isLeadDetailsEnhancementEnabled,
    handleSearchTextChange,
    searchTextRef,
    onPressEnter,
    handleFilterApply,
    onInitialFilterResolve,
    onInitialSortResolve,
    onApplySort,
    columns,
    selectedFilterGroup,
    allSortColumns,
    shouldConsiderCachedData,
    filterProps,
    leadDetails,
    onLeadNavigation,
    scrollRef,
    location,
    onAction,
    getDealerPropertyValue,
    isTestDriveCrmSetupEnabled,
    setFilterDetails,
    queryParams,
  } = props;

  const Component = useMemo(() => ENTITY_TYPE_VS_RENDERER[entityType], [entityType]);

  const { getFormattedNumber } = useTekionConversion();

  const { ref, onScroll, isScrolling } = useScrolling({
    data,
    current,
    isFetching,
    totalCount,
    filters,
    searchText,
    sortDetails,
    columnMetaData,
    onFetchEntityData,
  });

  useEffect(() => {
    if (selectedFilterGroup && !_isEmpty(filters)) {
      setFilterDetails(filters, selectedFilterGroup, sortDetails);
    }
  }, [filters, selectedFilterGroup, sortDetails]);

  const [isFetchingFiltersAndSort, setIsFetchingFiltersAndSort] = useState(true);

  const isFetchingInProgress = useMemo(
    () => (isFetching && !isScrolling) || isFetchingFiltersAndSort,
    [isFetching, isFetchingFiltersAndSort, isScrolling]
  );

  const onFilterAndSortLoaded = useCallback(() => {
    setIsFetchingFiltersAndSort(false);
  }, []);

  const renderFallback = () => (
    <div className="flex flex-column full-width full-height justify-content-center align-items-center">
      <Illustration
        size="5"
        mainText={__('No result found')}
        image={ASSETS.NO_DOCUMENTS}
        altText={__('No Documents')}
      />
    </div>
  );

  return (
    <>
      <FilterSortSearchSection
        searchText={searchText}
        handleSearchTextChange={handleSearchTextChange}
        searchTextRef={searchTextRef}
        onPressEnter={onPressEnter}
        handleFilterApply={handleFilterApply}
        onInitialFilterResolve={onInitialFilterResolve}
        onInitialSortResolve={onInitialSortResolve}
        onApplySort={onApplySort}
        sortDetails={sortDetails}
        columns={columns}
        selectedFilterGroup={selectedFilterGroup}
        onFilterAndSortLoaded={onFilterAndSortLoaded}
        allSortColumns={allSortColumns}
        shouldConsiderCachedData={shouldConsiderCachedData}
        filterProps={filterProps}
        entityType={entityType}
      />

      {isFetchingInProgress ? (
        <LeftContainerSkeletonBody />
      ) : (
        <>
          {isLeadDetailsEnhancementEnabled && (
            <Label id="leadDetails-leftContainer-filter-search-count" className={`m-l-16 m-b-8 ${styles.countLabel}`}>
              {__('{{totalCount}} {{entityType}}', {
                totalCount: getFormattedNumber(totalCount),
                entityType: ENTITY_COUNT_LABEL_CONFIG[entityType]?.countLabel,
              })}
            </Label>
          )}
          <div ref={ref} className="overflow-auto p-b-16 full-height" onScroll={onScroll}>
            {_size(data) > 0 ? (
              <Component
                data={data}
                leadDetails={leadDetails}
                onLeadNavigation={onLeadNavigation}
                scrollRef={scrollRef}
                navigate={navigate}
                location={location}
                onAction={onAction}
                getDealerPropertyValue={getDealerPropertyValue}
                isTestDriveCrmSetupEnabled={isTestDriveCrmSetupEnabled}
                isLeadDetailsEnhancementEnabled={isLeadDetailsEnhancementEnabled}
                entityId={queryParams?.entityId}
              />
            ) : (
              renderFallback()
            )}
          </div>
        </>
      )}
    </>
  );
}

Body.propTypes = {
  entityType: PropTypes.string.isRequired,
  data: PropTypes.array,
  current: PropTypes.number,
  isFetching: PropTypes.bool,
  totalCount: PropTypes.number,
  filters: PropTypes.array,
  searchText: PropTypes.string,
  sortDetails: PropTypes.object,
  columnMetaData: PropTypes.object,
  onFetchEntityData: PropTypes.func,
  isLeadDetailsEnhancementEnabled: PropTypes.bool,
  handleSearchTextChange: PropTypes.func,
  searchTextRef: PropTypes.object,
  onPressEnter: PropTypes.func,
  handleFilterApply: PropTypes.func,
  onInitialFilterResolve: PropTypes.func,
  onInitialSortResolve: PropTypes.func,
  onApplySort: PropTypes.func,
  columns: PropTypes.array,
  selectedFilterGroup: PropTypes.string,
  allSortColumns: PropTypes.array,
  shouldConsiderCachedData: PropTypes.bool,
  filterProps: PropTypes.func,
  leadDetails: PropTypes.object,
  onLeadNavigation: PropTypes.func,
  scrollRef: PropTypes.object,
  onAction: PropTypes.func,
  getDealerPropertyValue: PropTypes.func,
  isTestDriveCrmSetupEnabled: PropTypes.bool,
  setFilterDetails: PropTypes.func,
  queryParams: PropTypes.object,
};

Body.defaultProps = {
  data: EMPTY_ARRAY,
  current: 1,
  isFetching: false,
  totalCount: 0,
  filters: EMPTY_ARRAY,
  searchText: '',
  sortDetails: EMPTY_ARRAY,
  columnMetaData: EMPTY_OBJECT,
  onFetchEntityData: _noop,
  isLeadDetailsEnhancementEnabled: false,
  handleSearchTextChange: _noop,
  searchTextRef: EMPTY_OBJECT,
  onPressEnter: _noop,
  handleFilterApply: _noop,
  onInitialFilterResolve: _noop,
  onInitialSortResolve: _noop,
  onApplySort: _noop,
  columns: EMPTY_ARRAY,
  selectedFilterGroup: '',
  allSortColumns: EMPTY_ARRAY,
  shouldConsiderCachedData: false,
  filterProps: _noop,
  leadDetails: EMPTY_OBJECT,
  onLeadNavigation: _noop,
  scrollRef: EMPTY_OBJECT,
  onAction: _noop,
  getDealerPropertyValue: _noop,
  isTestDriveCrmSetupEnabled: false,
  setFilterDetails: _noop,
  queryParams: EMPTY_OBJECT,
};

export default Body;
