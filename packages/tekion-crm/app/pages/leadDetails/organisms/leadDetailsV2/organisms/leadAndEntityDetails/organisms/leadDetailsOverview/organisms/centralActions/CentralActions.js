import { useNavigate } from 'react-router-dom';
import React, { memo, useContext, useMemo, useCallback, useEffect } from 'react';
import cx from 'classnames';
import PropTypes from 'prop-types';
import { compose } from 'recompose';
import _map from 'lodash/map';
import _noop from 'lodash/noop';

import { EMPTY_OBJECT } from 'tbase/app.constants';
import useToggle from 'tbase/customHooks/useToggle';
import LeadReader from 'tbase/readers/lead.reader';
import FontIcon from 'tcomponents/atoms/FontIcon';
import withSize from 'tcomponents/hoc/withSize';
import withPropertyConsumer from 'tcomponents/organisms/propertyProvider/withPropertyConsumer';
import Popover, { POPOVER_TRIGGER, POPOVER_PLACEMENT } from 'tcomponents/molecules/popover';
import { HEADER_KEYS } from 'twidgets/appServices/crm/constants/leads/leadDetails';
import ICONS from 'twidgets/appServices/crm/constants/icons';
import Tooltip, { TOOLTIP_PLACEMENT } from 'tcomponents/atoms/tooltip';

import ACTION_TYPES from '../../../../../../../../constants/leadDetails.actionTypes';
import LeadDetailsContext from '../../../../../../../../context/leadDetails';
import { renderActiveTab, renderMainAction, renderPopOverContent } from './helpers/centralActions.component';
import {
  getPrimaryAndInlinePrimaryActions,
  getLeadActionConfig,
  shouldDisableLeadActionPopover,
  onActionClick,
  getTriggerNode,
  isNotesConfigAvailable,
} from './helpers/centralActions.general';
import { makeMainActionsConfig } from './helpers/centralActions.mainActionsConfig';
import { makeSecondaryActionsConfig } from './helpers/centralActions.secondaryActionsConfig';
import { TERTIARY_ACTIONS } from './helpers/centralActions.tertiaryActions';
import NotesInputSkeleton from '../../../../molecules/notesInputSkeleton';
import CentralActionsShimmer from '../../../../molecules/centralActionsSkeleton';

import styles from './centralActions.module.scss';

function CentralActions(props) {
  const navigate = useNavigate();
  const {
    getDealerPropertyValue,
    settings,
    getLeadDetails,
    contentWidth,
    location,
    onAction,
    onMergeDuplicates,
    isLoading,
    isMarkRespondedEnabled,
    isCreatingDeal,
    isCTCDisabled,
  } = props;
  const { leadDetails, notRespondedTabs, isDealOpen, lockedBy } = useContext(LeadDetailsContext);

  const [isNotesVisible, toggleNotesVisibility] = useToggle(false);

  const [visible, toggleVisibility] = useToggle();

  const leadId = LeadReader.leadId(leadDetails);

  const leadActionConfig = useMemo(() => getLeadActionConfig(notRespondedTabs), [notRespondedTabs]);

  const handleNotificationEvents = useCallback(
    item => {
      onAction({ type: ACTION_TYPES.HANDLE_NOTIFICATION_EVENTS, payload: { type: item } });
    },
    [onAction]
  );

  const mainActions = useMemo(
    () =>
      makeMainActionsConfig({
        leadDetails,
        getDealerPropertyValue,
        settings,
        getLeadDetails,
        handleNotificationEvents,
        isDealOpen,
        activeTab: isNotesVisible && HEADER_KEYS.NOTES,
        onAction,
        isCreatingDeal,
        isCTCDisabled,
        tooltipClassName: styles.centralActionTooltipOverlay,
      }),
    [
      leadDetails,
      getDealerPropertyValue,
      settings,
      getLeadDetails,
      handleNotificationEvents,
      isDealOpen,
      isNotesVisible,
      onAction,
      isCreatingDeal,
      isCTCDisabled,
    ]
  );
  const { inlinePrimaryActions, primaryActions } = useMemo(
    () => getPrimaryAndInlinePrimaryActions(mainActions, contentWidth),
    [mainActions, contentWidth]
  );

  const secondaryActions = useMemo(
    () =>
      makeSecondaryActionsConfig({
        leadDetails,
        getDealerPropertyValue,
        getLeadDetails,
        isMarkRespondedEnabled,
      }),
    [leadDetails, getDealerPropertyValue, getLeadDetails, isMarkRespondedEnabled]
  );

  const disableLeadActionPopover = useMemo(
    () => shouldDisableLeadActionPopover(lockedBy, leadDetails),
    [lockedBy, leadDetails]
  );

  const onClick = useMemo(
    () =>
      onActionClick({
        leadDetails,
        settings,
        getDealerPropertyValue,
        handleNotificationEvents,
        getLeadDetails,
        onNotesClick: toggleNotesVisibility,
        onMergeDuplicates,
      }),
    [
      leadDetails,
      settings,
      getDealerPropertyValue,
      handleNotificationEvents,
      getLeadDetails,
      toggleNotesVisibility,
      onMergeDuplicates,
    ]
  );

  const isNotePrimary = isNotesConfigAvailable(inlinePrimaryActions);

  useEffect(() => {
    const notesVisibility = isNotePrimary && !isLoading;
    toggleNotesVisibility(notesVisibility);
  }, [leadId, isLoading, isNotePrimary]);

  if (!leadId) {
    return <CentralActionsShimmer />;
  }

  return (
    <>
      <div className="flex flex-row flex-wrap align-items-center relative">
        {_map(
          inlinePrimaryActions,
          renderMainAction({
            inlinePrimaryActions,
            navigate,
            leadDetails,
            onClick,
            location,
            isCTCDisabled,
          })
        )}
        <Popover
          placement={POPOVER_PLACEMENT.BOTTOM_LEFT}
          trigger={POPOVER_TRIGGER.CLICK}
          content={renderPopOverContent({
            secondaryActions,
            tertiaryActions: TERTIARY_ACTIONS,
            primaryActions,
            leadDetails,
            leadActionConfig,
            settings,
            toggleVisibility,
            onClick,
            lockedBy,
            isMarkRespondedEnabled,
          })}
          visible={visible}
          onVisibleChange={toggleVisibility}
          overlayClassName={styles.popoverOverlay}
          getPopupContainer={getTriggerNode}>
          <div
            role="button"
            tabIndex={-1}
            className={cx('m-x-4', styles.logIcon, {
              [styles.disabled]: disableLeadActionPopover,
              [styles.disabledIconContainer]: disableLeadActionPopover,
            })}>
            <Tooltip
              title={__('Additional Lead Actions')}
              placement={TOOLTIP_PLACEMENT.TOP}
              overlayClassName={styles.centralActionTooltipOverlay}>
              <FontIcon
                className={cx('m-x-8', styles.secondaryActionsIcon, {
                  [styles.disabledIcon]: disableLeadActionPopover,
                })}>
                {ICONS.OVERFLOW}
              </FontIcon>
            </Tooltip>
          </div>
        </Popover>
      </div>
      {isLoading && <NotesInputSkeleton />}
      {renderActiveTab(leadDetails, isNotesVisible)}
    </>
  );
}

CentralActions.propTypes = {
  getDealerPropertyValue: PropTypes.func,
  onAction: PropTypes.func,
  settings: PropTypes.object,
  getLeadDetails: PropTypes.func,
  contentWidth: PropTypes.number.isRequired,
  onMergeDuplicates: PropTypes.func,
  isLoading: PropTypes.bool,
  isMarkRespondedEnabled: PropTypes.bool,
  isCreatingDeal: PropTypes.bool,
  isCTCDisabled: PropTypes.bool,
};

CentralActions.defaultProps = {
  getDealerPropertyValue: _noop,
  onAction: _noop,
  settings: EMPTY_OBJECT,
  getLeadDetails: _noop,
  onMergeDuplicates: _noop,
  isLoading: false,
  isMarkRespondedEnabled: false,
  isCreatingDeal: false,
  isCTCDisabled: false,
};

export default compose(withPropertyConsumer, withSize(), memo)(CentralActions);
