@use "tstyles/component.scss";

.activityTabContainer {
  height: unset;
  :global(.ant-tabs-tabpane.ant-tabs-tabpane-active) {
    min-height: calc(100vh - 52.8rem) !important;
  }
}

.containerClassName {
  background-color: component.$white;
}

.contentClassName {
  font-weight: 600;
  color: component.$atomic;
}

.iconClassName {
  color: component.$atomic;
}

.scrollToTop {
  right: calc(50% - 3.2rem); // 50% - button width //TODO: Remove the dynamic calculations and find the soid soln
}

.scrollToTopWhenClosed {
  right: calc(50% + 7.6rem); // 50% - button width + left container width
}
