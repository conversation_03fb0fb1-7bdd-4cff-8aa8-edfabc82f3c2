import { useNavigate } from 'react-router-dom';
import React, { useCallback } from 'react';
import cx from 'classnames';
import PropTypes from 'prop-types';
import _noop from 'lodash/noop';

import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';

import { handleGoBack as onGoBack } from '../../../../helpers/leadDetails.general';
import LeftContainer from '../../../leftContainer';

import styles from './entityList.module.scss';
import commonStyles from '../../leadDetailsV2.module.scss';

const EntityList = props => {
  const navigate = useNavigate();
  const {
    showLeftContainer,
    setLeftContainerVisibility,
    location,
    useHistoryBack,
    leadDetails,
    isDefaultRoute,
    entityType,
    onLeadNavigation,
    onEntityActionCb,
    setShowLeadDetails,
    getDealerPropertyValue,
    isTestDriveCrmSetupEnabled,
    resetMetaDataByKey,
    data,
    totalCount,
    isFetching,
    current,
    footerProps,
    metaData,
    leads,
    tasks,
    appointments,
    contacts,
    useContent,
    includeSiteFilter,
    checkContactAssignee,
    onAction,
    settings,
    getSources,
    getSourceGroups,
    dealStatusOptions,
    leadStatusOptions,
    resetLeftContainerState,
    paymentOptionConfigs,
    showHeaderLeftSection,
    updateRouteUrl,
    queryParams,
    getLeadStatus,
    getDealStatus,
    fetchLegacyOptions,
  } = props;

  const handleGoBack = useCallback(() => {
    onGoBack({
      navigate,
      useHistoryBack,
      location,
    });
  }, [useHistoryBack, navigate, location]);

  return (
    <div
      className={cx({
        [commonStyles.entityListContainer]: showLeftContainer,
        [styles.entityListContainerCollapsed]: !showLeftContainer,
      })}>
      <LeftContainer
        leadDetails={leadDetails}
        isDefaultRoute={isDefaultRoute}
        entityType={entityType}
        onLeadNavigation={onLeadNavigation}
        show={showLeftContainer}
        onEntityActionCb={onEntityActionCb}
        setShowLeadDetails={setShowLeadDetails}
        getDealerPropertyValue={getDealerPropertyValue}
        isTestDriveCrmSetupEnabled={isTestDriveCrmSetupEnabled}
        resetMetaDataByKey={resetMetaDataByKey}
        setLeftContainerVisibility={setLeftContainerVisibility}
        data={data}
        totalCount={totalCount}
        isFetching={isFetching}
        current={current}
        footerProps={footerProps}
        metaData={metaData}
        leads={leads}
        tasks={tasks}
        appointments={appointments}
        contacts={contacts}
        useContent={useContent}
        includeSiteFilter={includeSiteFilter}
        checkContactAssignee={checkContactAssignee}
        isLeadDetailsEnhancementEnabled
        className={cx('full-height', {
          [styles.entityListContainerCollapsed]: !showLeftContainer,
        })}
        onGoBack={handleGoBack}
        onAction={onAction}
        settings={settings}
        getSources={getSources}
        getSourceGroups={getSourceGroups}
        dealStatusOptions={dealStatusOptions}
        leadStatusOptions={leadStatusOptions}
        navigate={navigate}
        location={location}
        resetLeftContainerState={resetLeftContainerState}
        paymentOptionConfigs={paymentOptionConfigs}
        showHeaderLeftSection={showHeaderLeftSection}
        updateRouteUrl={updateRouteUrl}
        queryParams={queryParams}
        getLeadStatus={getLeadStatus}
        getDealStatus={getDealStatus}
        fetchLegacyOptions={fetchLegacyOptions}
      />
    </div>
  );
};

EntityList.propTypes = {
  leadDetails: PropTypes.oneOf,
  isDefaultRoute: PropTypes.bool,
  entityType: PropTypes.string.isRequired,
  onLeadNavigation: PropTypes.func,
  onEntityActionCb: PropTypes.func,
  setShowLeadDetails: PropTypes.func,
  getDealerPropertyValue: PropTypes.func,
  isTestDriveCrmSetupEnabled: PropTypes.bool,
  resetMetaDataByKey: PropTypes.func,
  useHistoryBack: PropTypes.bool,
  setLeftContainerVisibility: PropTypes.func,
  showLeftContainer: PropTypes.bool,
  data: PropTypes.object,
  totalCount: PropTypes.number,
  isFetching: PropTypes.bool,
  current: PropTypes.number,
  footerProps: PropTypes.object,
  metaData: PropTypes.object,
  leads: PropTypes.object,
  tasks: PropTypes.object,
  appointments: PropTypes.object,
  contacts: PropTypes.object,
  useContent: PropTypes.bool,
  includeSiteFilter: PropTypes.bool,
  checkContactAssignee: PropTypes.bool,
  onAction: PropTypes.func,
  settings: PropTypes.object,
  getSources: PropTypes.func,
  getSourceGroups: PropTypes.func,
  dealStatusOptions: PropTypes.array,
  leadStatusOptions: PropTypes.array,
  resetLeftContainerState: PropTypes.func,
  paymentOptionConfigs: PropTypes.array,
  showHeaderLeftSection: PropTypes.bool,
  updateRouteUrl: PropTypes.bool,
  getLeadStatus: PropTypes.func,
  getDealStatus: PropTypes.func,
  fetchLegacyOptions: PropTypes.func,
};

EntityList.defaultProps = {
  leadDetails: EMPTY_OBJECT,
  isDefaultRoute: false,
  onLeadNavigation: _noop,
  onEntityActionCb: _noop,
  setShowLeadDetails: _noop,
  getDealerPropertyValue: _noop,
  isTestDriveCrmSetupEnabled: false,
  resetMetaDataByKey: _noop,
  useHistoryBack: false,
  setLeftContainerVisibility: _noop,
  showLeftContainer: true,
  data: EMPTY_OBJECT,
  totalCount: 0,
  isFetching: true,
  current: 1,
  footerProps: EMPTY_OBJECT,
  metaData: EMPTY_OBJECT,
  leads: EMPTY_OBJECT,
  tasks: EMPTY_OBJECT,
  appointments: EMPTY_OBJECT,
  contacts: EMPTY_OBJECT,
  useContent: false,
  includeSiteFilter: false,
  checkContactAssignee: false,
  onAction: EMPTY_OBJECT,
  settings: EMPTY_OBJECT,
  getSources: _noop,
  getSourceGroups: _noop,
  dealStatusOptions: EMPTY_ARRAY,
  leadStatusOptions: EMPTY_ARRAY,
  resetLeftContainerState: _noop,
  paymentOptionConfigs: EMPTY_ARRAY,
  showHeaderLeftSection: true,
  updateRouteUrl: true,
  getLeadStatus: _noop,
  getDealStatus: _noop,
  fetchLegacyOptions: _noop,
};

export default React.memo(EntityList);
