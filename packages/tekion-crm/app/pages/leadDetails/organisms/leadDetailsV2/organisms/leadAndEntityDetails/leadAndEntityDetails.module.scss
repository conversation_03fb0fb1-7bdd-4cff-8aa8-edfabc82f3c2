@use "tstyles/component.scss";

.customRoute {
  flex-grow: 1;
  margin-bottom: 0;
  height: auto;
  border-radius: 0.6rem;
}

// Common classes for both original components and skeletons

.leadDetailsOverviewContainer {
  background-color: component.$lightGray;
  flex-grow: 1;
  margin-bottom: 0;
  height: auto;
  row-gap: 1rem;
  overflow-x: hidden;
}

.journeyPanelContainer {
  background-color: component.$white;
  border-radius: 0.8rem;
  max-height: 16.2rem;
  opacity: 1;
  position: relative;
  box-shadow: 0 0.2rem 0.4rem 0 component.$lightShadowBlack;
  margin-bottom: 3.6rem;
}

.activitiesCardContainer {
  background-color: component.$white;
  border-radius: 0.8rem;
  opacity: 1;
  transition: 0.25s ease;
  height: max-content;
}

.entityInfoContainer {
  width: 33.6rem;
  min-width: 33.6rem;
  background-color: component.$white;
  height: auto;
  border-radius: 0.8rem;
}

.activityLogContainer {
  border-radius: 0.8rem;
  height: calc(100% - 13.2rem);
}
