import { useNavigate } from 'react-router-dom';
import React, { useCallback, useContext, useMemo, useRef, useEffect } from 'react';
import cx from 'classnames';
import PropTypes from 'prop-types';
import _debounce from 'lodash/debounce';
import _noop from 'lodash/noop';

import LeadReader from 'tbase/readers/lead.reader';
import { EMPTY_OBJECT } from 'tbase/app.constants';
import isStringEmpty from 'tbase/utils/isStringEmpty';
import showAiBdcBanner from 'tbusiness/appServices/crm/helpers/showAIBDCBanner';
import SetupReader from 'tbusiness/appServices/crm/readers/setup.reader';
import { LEAD_ACTIVITY_PANEL_ID } from 'twidgets/appServices/crm/constants/leads/leadDetails';
import useScrollIntoView from 'twidgets/appServices/crm/hooks/useScrollIntoView';
import ActivityLogs from 'twidgets/appServices/crm/organisms/activityLogs';
import AISummaryBanner from 'twidgets/appServices/crm/organisms/aiBdcEngagedBanner';
import useFetchData from 'twidgets/appServices/crm/hooks/useFetchData';
import { getQueryParamValue } from 'twidgets/appServices/crm/utils';
import useAiBdcUser from 'twidgets/hooks/useAiBdcUser';

import { isPastOpportunitiesEnabled, isCRMContactLinked } from 'helpers/lead';

import ACTION_TYPES from '../../../../../../constants/leadDetails.actionTypes';
import LeadDetailsContext from '../../../../../../context/leadDetails';
import { getIsMarkRespondedEnabled } from '../../../../../../helpers/leadDetails.customer';
import useTodo from './hooks/useTodo';
import ActivitiesCard from './organisms/activitiesCard';
import ScrollToTopButton from '../../../../../../molecules/scrollToTopButton';
import SyncDataIllustration from '../../../../../../molecules/syncDataIllustration';
import { getMarkRespondedStatus } from '../../../../../../services/markResponded';
import PastOpportunities, {
  PAST_OPPORTUNITIES_ACTION_TYPES,
  PastOpportunitiesButton,
} from '../../../../../pastOpportunities';
import { OFFSET_BOTTOM_HEIGHT, SCROLL_DEBOUNCE_TIME } from './constants/leadDetailsOverview.general';
import getElementScrollPosition from './helpers/leadDetailsOverview.getElementScrollPosition';
import useActivityCardPusher from './hooks/useActivityCardPusher';
import useActivityLogs from './hooks/useActivityLogs';
import CentralActions from './organisms/centralActions';
import JourneyPanel from './organisms/journeyPanel';

import styles from './leadDetailsOverview.module.scss';
import commonStyles from '../../leadAndEntityDetails.module.scss';

const LeadDetailsOverview = props => {
  const navigate = useNavigate();
  const {
    openLeadInfo,
    getLeadDetails,
    getDealerPropertyValue,
    setLeadDetails,
    onAction,
    isLoading,
    leadDetails,
    isCustomerOneViewEnabled,
    location,
    isMinimized,
    settings,
    activityLogsOnAction,
    liveChatCount,
    dealerInfo,
    communicationOptinPreference,
    onMergeDuplicates,
    isLeftContainerVisible,
    isCreatingDeal,
    params,
    updateRouteUrl,
    isCTCDisabled,
  } = props;

  const context = useContext(LeadDetailsContext);
  const {
    fetchMoreActivities,
    setHasMoreActivities,
    setActivityLogsGetterFns,
    hasMoreActivities,
    isLoading: isFetchingActivitiesOnScroll,
  } = useActivityLogs();
  const containerRef = useRef(null);
  const { ref: scrollIntoRef, isVisible } = useScrollIntoView();
  const { aiBdcUser } = useAiBdcUser(getDealerPropertyValue);
  const { activities, isLoading: isActivitiesLoading, fetchTodoActivities } = useTodo({ leadDetails });
  const { customerOneViewProps, isDealOpen } = context;
  const {
    isPastOpportunitiesOpen,
    linkedLeadLoading,
    isFetchingLinkedLeadsCount,
    linkedLeadsCount,
    selectedOEMDealerSites,
    selectedFilters,
  } = customerOneViewProps;

  const department = LeadReader.department(leadDetails);
  const crmCustomerId = LeadReader.crmBuyerCustomerId(leadDetails);

  const entityId = getQueryParamValue(location, 'entityId');
  const {
    data: markRespondedEnabledData,
    isFetching: isFetchingRespondedStatus,
    fetchDataFunc: fetchMarkRespondedStatus,
  } = useFetchData({
    fetchData: getMarkRespondedStatus,
    payload: [crmCustomerId],
    shouldFetchDataOnMount: false,
  });

  useEffect(() => {
    if (!isStringEmpty(crmCustomerId)) {
      fetchMarkRespondedStatus();
    }
    onAction({
      type: ACTION_TYPES.UPDATE_DEAL_CREATION_STATUS,
      payload: { value: false },
    });
  }, [crmCustomerId]);

  useActivityCardPusher({ fetchTodoActivities, leadDetails, fetchMarkRespondedStatus });

  const isMarkRespondedEnabled = getIsMarkRespondedEnabled({
    markRespondedEnabledData,
    crmCustomerId,
    isFetchingRespondedStatus,
  });

  const handleScrollToTop = useCallback(() => {
    if (!containerRef?.current) return;
    containerRef.current.scrollTo({ top: 0, behavior: 'smooth' });
  }, [containerRef]);

  const handleBackToLeadClick = useCallback(() => {
    onAction({
      type: PAST_OPPORTUNITIES_ACTION_TYPES.HANDLE_BACK_TO_LEAD_BUTTON_CLICK,
    });
  }, [onAction]);

  const handleDropdownClick = useCallback(() => {
    onAction({
      type: PAST_OPPORTUNITIES_ACTION_TYPES.HANDLE_PAST_OPPORTUNITIES_BUTTON_CLICK,
    });
  }, [onAction]);

  const showPastOpportunitiesButton = isPastOpportunitiesEnabled({
    selectedOEMDealerSites,
    selectedFilters,
    isFetchingLinkedLeadsCount,
    leadDetails,
    isCustomerOneViewEnabled,
    linkedLeadsCount,
  });

  const showSyncData = isCRMContactLinked(leadDetails, isCustomerOneViewEnabled);

  const isTestDriveCrmSetupEnabled = SetupReader.advancedTestDrive(settings);

  const shouldShowAiBdcBanner = useMemo(
    () => showAiBdcBanner(getDealerPropertyValue, leadDetails),
    [getDealerPropertyValue, leadDetails]
  );

  const handleScroll = _debounce(() => {
    const element = containerRef.current;
    const scrollBarPosition = getElementScrollPosition(element);
    const shouldFetchMoreActivities = scrollBarPosition <= OFFSET_BOTTOM_HEIGHT && hasMoreActivities;
    if (shouldFetchMoreActivities && !isFetchingActivitiesOnScroll) {
      fetchMoreActivities();
    }
  }, SCROLL_DEBOUNCE_TIME);

  return (
    <div ref={containerRef} className={`p-24 ${commonStyles.leadDetailsOverviewContainer}`} onScroll={handleScroll}>
      {!isVisible ? (
        <ScrollToTopButton
          className={cx('flex', {
            [styles.scrollToTop]: isLeftContainerVisible,
            [styles.scrollToTopWhenClosed]: !isLeftContainerVisible,
          })}
          onClick={handleScrollToTop}
        />
      ) : null}
      <JourneyPanel
        openLeadInfo={openLeadInfo}
        getLeadDetails={getLeadDetails}
        getDealerPropertyValue={getDealerPropertyValue}
        setLeadDetails={setLeadDetails}
        onBackToLeadClick={handleBackToLeadClick}
        isLoading={isLoading || linkedLeadLoading}
        handleDropdownClick={handleDropdownClick}
        showPastOpportunitiesButton={showPastOpportunitiesButton}
        scrollIntoRef={scrollIntoRef}
        navigate={navigate}
        location={location}
        onAction={onAction}
        isCreatingDeal={isCreatingDeal}
      />
      {showSyncData ? (
        <SyncDataIllustration />
      ) : (
        <div>
          {isPastOpportunitiesOpen ? (
            <div>
              <PastOpportunities
                leadDetails={leadDetails}
                isMinimized={isMinimized}
                navigate={navigate}
                location={location}
                onAction={onAction}
              />
              <div className="m-t-4 m-b-24">
                {showPastOpportunitiesButton && (
                  <PastOpportunitiesButton
                    handleDropdownClick={handleDropdownClick}
                    containerClassName={styles.containerClassName}
                    contentClassName={styles.contentClassName}
                    iconClassName={styles.iconClassName}
                  />
                )}
              </div>
            </div>
          ) : (
            <div>
              <CentralActions
                isCTCDisabled={isCTCDisabled}
                settings={settings}
                getLeadDetails={getLeadDetails}
                navigate={navigate}
                location={location}
                onAction={onAction}
                onMergeDuplicates={onMergeDuplicates}
                isLoading={isLoading}
                isMarkRespondedEnabled={isMarkRespondedEnabled}
                isCreatingDeal={isCreatingDeal}
              />
              <ActivitiesCard
                isCTCDisabled={isCTCDisabled}
                activities={activities}
                settings={settings}
                navigate={navigate}
                location={location}
                getLeadDetails={getLeadDetails}
                leadDetails={leadDetails}
                isTestDriveCrmSetupEnabled={isTestDriveCrmSetupEnabled}
                isLoading={isLoading || isActivitiesLoading}
                onAction={activityLogsOnAction}
                fetchTodoActivities={fetchTodoActivities}
                getDealerPropertyValue={getDealerPropertyValue}
              />
              {shouldShowAiBdcBanner && <AISummaryBanner aiBdcUser={aiBdcUser} />}
              <ActivityLogs
                isCTCDisabled={isCTCDisabled}
                department={department}
                leadDetails={leadDetails}
                parentOnAction={activityLogsOnAction}
                panelId={LEAD_ACTIVITY_PANEL_ID}
                isCustomerOneViewEnabled={isCustomerOneViewEnabled}
                entityId={entityId ?? ''}
                liveChatCount={liveChatCount}
                getDealerPropertyValue={getDealerPropertyValue}
                isFetchingLead={isLoading || isFetchingLinkedLeadsCount}
                linkedLeadsCount={linkedLeadsCount}
                isDealOpen={isDealOpen}
                dealerInfo={dealerInfo}
                communicationOptinPreference={communicationOptinPreference}
                tabClassName={styles.activityTabContainer}
                setHasMoreActivities={setHasMoreActivities}
                setActivityLogsGetterFns={setActivityLogsGetterFns}
                params={params}
                updateRouteUrl={updateRouteUrl}
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
};

LeadDetailsOverview.propTypes = {
  openLeadInfo: PropTypes.func,
  getLeadDetails: PropTypes.func,
  getDealerPropertyValue: PropTypes.func,
  setLeadDetails: PropTypes.func,
  onAction: PropTypes.func,
  isLoading: PropTypes.bool,
  leadDetails: PropTypes.object.isRequired,
  isCustomerOneViewEnabled: PropTypes.bool,
  activityLogsOnAction: PropTypes.func,
  liveChatCount: PropTypes.number,
  dealerInfo: PropTypes.object,
  communicationOptinPreference: PropTypes.object,
  onMergeDuplicates: PropTypes.func,
  isLeftContainerVisible: PropTypes.bool,
  isCreatingDeal: PropTypes.bool,
  isCTCDisabled: PropTypes.bool,
};

LeadDetailsOverview.defaultProps = {
  openLeadInfo: _noop,
  getLeadDetails: _noop,
  getDealerPropertyValue: _noop,
  setLeadDetails: _noop,
  onAction: _noop,
  isLoading: false,
  isCustomerOneViewEnabled: true,
  liveChatCount: undefined,
  activityLogsOnAction: _noop,
  dealerInfo: EMPTY_OBJECT,
  communicationOptinPreference: EMPTY_OBJECT,
  onMergeDuplicates: _noop,
  isLeftContainerVisible: false,
  isCreatingDeal: false,
  isCTCDisabled: false,
};

export default React.memo(LeadDetailsOverview);
