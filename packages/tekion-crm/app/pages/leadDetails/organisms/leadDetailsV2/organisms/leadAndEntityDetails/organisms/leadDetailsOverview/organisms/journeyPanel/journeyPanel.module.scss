@use "tstyles/colors.scss";
@use "tstyles/variables.scss";

@use "../../../../../../../../leadDetails.module.scss" as leadDetailsStyles;

$custom-stage-width: 11.2rem;

.journeyPanel {
  .stages {
    flex: 1 1;
    max-width: calc(100% - $custom-stage-width - 2.4rem);
  }

  @include leadDetailsStyles.screenSizeBetween1200And1400 {
    max-height: 19.6rem;
  }
}

.customStage {
  width: $custom-stage-width;

  :global(.ant-select-selection) {
    background: none !important;
  }

  :global(.ant-select-selection__rendered) {
    margin-right: 0.4rem;
    margin-left: 0rem;
  }

  :global(.ant-select-selection__clear) {
    right: 0.45rem;
  }

  :global(.ant-select-arrow) {
    right: 0.4rem;
  }
}

.customStageContainer {
  text-align: right;
  width: $custom-stage-width;
}

.departmentIndicator {
  position: absolute;
  top: 0;
  width: max-content;
  min-width: 1.5rem;
  height: 1.5rem;
  left: 0;
  border-radius: 0.6rem 0rem;
  text-align: center;
}

.lockPopover {
  position: absolute;
  top: 0;
  width: max-content;
  min-width: 1.5rem;
  height: 1.5rem;
  right: 0;
  border-radius: 0.6rem 0rem;
  text-align: center;
}

.addVehicleButtonClassName {
  padding: 0rem !important;
  margin-left: 0 !important;
  text-align: left;
}

.addVehicleButtonContentClassName {
  margin-left: 0 !important;
  font-size: variables.$font-normal;
  line-height: variables.$line-xxnormal;
}

.addVOI {
  @include leadDetailsStyles.screenSizeBetween1200And1400 {
    max-width: 13.2rem;
    white-space: normal;
  }
}

.tradeInVehicleTitle {
  font-weight: 600;
}

.containerClassName {
  background-color: colors.$white;
  border: 1px solid colors.$lightGray;
}

.contentClassName {
  font-weight: 600;
  color: colors.$atomic;
}

.iconClassName {
  color: colors.$atomic;
}
.ellipsisClassName {
  margin: 1rem;
}

.shadowDiv {
  width: calc(100% - 1.6rem);
  height: 1.2rem;
  border-radius: 0.8rem;
  background-color: colors.$white;
  box-shadow: 0 0.2rem 0.4rem 0 colors.$lightShadowBlack;
}
