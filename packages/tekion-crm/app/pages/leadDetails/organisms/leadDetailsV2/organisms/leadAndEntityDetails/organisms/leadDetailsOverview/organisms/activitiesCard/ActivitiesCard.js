import { useNavigate } from 'react-router-dom';
import React, { memo, useMemo } from 'react';
import _isEmpty from 'lodash/isEmpty';
import _noop from 'lodash/noop';
import _size from 'lodash/size';
import PropTypes from 'prop-types';

import { EMPTY_OBJECT } from 'tbase/app.constants';
import useToggle from 'tbase/customHooks/useToggle';

import { DATA_TEST_CONFIG } from 'pages/leadDetails/constants/leadDetails.dataTestConfig';
import useTaskActions from 'hocs/useTaskActions';

import { shouldShowAddTaskAction } from './helpers/activitiesCard.constraints';
import ActivitiesCardSkeleton from '../../../../molecules/activitiesCardSkeleton';
import Footer from './molecules/footer';
import TodoHeader from './molecules/todoHeader';
import Future from './organisms/future';
import Todo from './organisms/todo';

import commonstyles from '../../../../leadAndEntityDetails.module.scss';

const ActivitiesCard = props => {
  const navigate = useNavigate();
  const {
    getDealerPropertyValue,
    settings,
    leadDetails,
    activities,
    dealerId,
    isTestDriveCrmSetupEnabled,
    location,
    onAction,
    dealerInfo,
    communicationOptinPreference,
    fetchTodoActivities,
    isLoading,
    isCTCDisabled,
  } = props;

  const { handleAddTaskClick } = useTaskActions({ leadDetails });

  const { totalTodosCount, totalActivitiesCount, futureAppointments, totalUpcomingActivitiesCount } = activities;

  const [showMore, toggleShowMore] = useToggle(false);

  const subText =
    !_isEmpty(futureAppointments) && totalTodosCount
      ? __('{{upcomingAppointments}} Upcoming appointment(s)', { upcomingAppointments: _size(futureAppointments) })
      : undefined;

  const handleOnAction = action => {
    const { type, payload } = action;
    const actions = {
      type,
      payload: {
        data: payload?.response,
      },
    };
    onAction(actions);
  };

  const shouldShowAddTaskButton = useMemo(
    () => shouldShowAddTaskAction({ leadDetails, totalActivitiesCount }),
    [leadDetails, totalActivitiesCount]
  );

  const todoHeaderCount = isLoading ? 0 : totalTodosCount;

  const hasActivities = totalActivitiesCount > 0;

  return (
    <div
      className={`flex flex-column full-height m-y-24 relative ${commonstyles.activitiesCardContainer}`}
      data-test={DATA_TEST_CONFIG.TODO_COMPONENT}>
      <TodoHeader
        todoCount={todoHeaderCount}
        onAddTaskClick={handleAddTaskClick}
        shouldShowAddTask={shouldShowAddTaskButton}
        isLoading={isLoading}
        totalUpcomingActivitiesCount={totalUpcomingActivitiesCount}
      />
      {isLoading ? (
        <ActivitiesCardSkeleton />
      ) : (
        hasActivities && (
          <>
            <Todo
              isCTCDisabled={isCTCDisabled}
              activities={activities}
              settings={settings}
              getDealerPropertyValue={getDealerPropertyValue}
              leadDetails={leadDetails}
              dealerId={dealerId}
              isTestDriveCrmSetupEnabled={isTestDriveCrmSetupEnabled}
              navigate={navigate}
              location={location}
              showMore={showMore}
              onAction={handleOnAction}
              dealerInfo={dealerInfo}
              communicationOptinPreference={communicationOptinPreference}
              fetchTodoActivities={fetchTodoActivities}
              isLoading={isLoading}
            />
            <Future
              isCTCDisabled={isCTCDisabled}
              activities={activities}
              settings={settings}
              getDealerPropertyValue={getDealerPropertyValue}
              leadDetails={leadDetails}
              dealerId={dealerId}
              navigate={navigate}
              location={location}
              showMore={showMore}
              onAction={onAction}
              fetchTodoActivities={fetchTodoActivities}
              isLoading={isLoading}
              isTestDriveCrmSetupEnabled={isTestDriveCrmSetupEnabled}
            />
            {totalActivitiesCount > 2 && <Footer subText={subText} onClick={toggleShowMore} isExpanded={showMore} />}
          </>
        )
      )}
    </div>
  );
};

ActivitiesCard.propTypes = {
  activities: PropTypes.shape({
    overdueAppointments: PropTypes.array,
    todaysAppointments: PropTypes.array,
    overdueTasks: PropTypes.array,
    todaysTasks: PropTypes.array,
    totalTodosCount: PropTypes.number,
  }),

  settings: PropTypes.object,
  getDealerPropertyValue: PropTypes.func,
  leadDetails: PropTypes.object,
  dealerId: PropTypes.string,
  isTestDriveCrmSetupEnabled: PropTypes.bool,
  onAction: PropTypes.func,
  dealerInfo: PropTypes.object,
  communicationOptinPreference: PropTypes.object,
  fetchTodoActivities: PropTypes.func,
  isLoading: PropTypes.bool,
  isCTCDisabled: PropTypes.bool,
};

ActivitiesCard.defaultProps = {
  activities: EMPTY_OBJECT,
  settings: EMPTY_OBJECT,
  getDealerPropertyValue: _noop,
  leadDetails: EMPTY_OBJECT,
  dealerId: undefined,
  isTestDriveCrmSetupEnabled: false,
  onAction: _noop,
  dealerInfo: EMPTY_OBJECT,
  communicationOptinPreference: EMPTY_OBJECT,
  fetchTodoActivities: _noop,
  isLoading: false,
  isCTCDisabled: false,
};

export default memo(ActivitiesCard);
