@use "tstyles/colors.scss";

.divider {
  width: 0.1rem !important;
  background: colors.$lightGray;
}

.upcomingHeaderDivider {
  background: colors.$lightGray;
  margin: 0rem;
  margin-left: 0.4rem;
  min-width: fit-content;
  background: colors.$platinum;
}

.button {
  &:focus {
    color: colors.$denim;
  }
  span {
    text-decoration: none;
  }
}

.roundedBorders {
  border-bottom-left-radius: 0.8rem !important;
  border-bottom-right-radius: 0.8rem !important;
}

.labelClassName {
  color: colors.$atomic;
  font-weight: 500;
}

.iconClassName {
  color: colors.$atomic;

  :hover {
    color: colors.$denim;
  }
}
