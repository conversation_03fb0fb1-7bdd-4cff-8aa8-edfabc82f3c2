import { useNavigate } from 'react-router-dom';
import React, { useCallback, useMemo, Suspense, lazy } from 'react';
import PropTypes from 'prop-types';
import _noop from 'lodash/noop';
import { isEmpty } from 'lodash';

import { EMPTY_OBJECT } from 'tbase/app.constants';
import CRMCustomerReader from 'tbase/readers/CrmCustomer';
import { getCTCDisabled } from '@tekion/tekion-business/src/helpers/crm/communication';
import isCRMCustomerOneViewEnabled from 'tbusiness/appServices/crm/helpers/isCRMCustomerOneViewEnabled';
import Illustration from 'tcomponents/molecules/illustration';
import PropertyControlledComponent from 'tcomponents/molecules/PropertyControlledComponent';
import EntityInformationSkeleton from 'twidgets/appServices/crm/molecules/entityInformationSkeleton';

import ASSETS from 'constants/s3Assets';
import { setDocumentTitle } from 'utils';

// Services
import crmSetupApi from '@tekion/tekion-business/src/services/crm/crmSetup.api';

// Hooks
import useFetchData from '@tekion/tekion-widgets/src/appServices/crm/hooks/useFetchData';

import {
  isDefaultLeadRoute,
  getCustomRouteComponent,
  getErrorComponent,
} from '../../../../helpers/leadDetails.customRoute';
import LEAD_DETAILS_ACTION_TYPES from '../../../../constants/leadDetails.actionTypes';
import CONTACT_ACTION_TYPES from '../../../../constants/leadDetails.contactActionTypes';
import { getEntity } from '../../helpers/leadDetailsV2.entity';

import LeadDetailsOverviewSkeleton from './molecules/leadDetailsOverviewSkeleton';

const LazyEntityInformation = lazy(
  () => import('./organisms/entityInformation' /* webpackChunkName: "EntityInformation" */)
);
const LazyLeadDetailsOverview = lazy(
  () => import('./organisms/leadDetailsOverview' /* webpackChunkName: "LeadDetailsOverview" */)
);

import styles from './leadAndEntityDetails.module.scss';

const LeadAndEntityDetails = props => {
  const navigate = useNavigate();
  const {
    settings,
    rightPanelTab,
    showSectionLoader,
    showLeadDetails,
    location,
    onAction,
    getDealerPropertyValue,
    crmCustomerDetails,
    patchCustomerTypeDetails,
    getLeadDetails,
    updateLeadInState,
    activityLogsOnAction,
    isLoading,
    leadDetails,
    liveChatCount,
    dealerInfo,
    communicationOptinPreference,
    onMergeDuplicates,
    isLeftContainerVisible,
    isCreatingDeal,
    params,
    error,
    updateRouteUrl,
    communicationSetupConfig,
  } = props;

  const hasError = !isEmpty(error);
  const isCTCDisabled = getCTCDisabled(communicationSetupConfig);

  const entityType = useMemo(
    () =>
      getEntity({
        navigate,
        location,
      }),
    [navigate, location]
  );
  const { isDefaultRoute, CustomRouteComponent } = useMemo(
    () => ({
      isDefaultRoute: isDefaultLeadRoute(params),
      CustomRouteComponent: hasError ? getErrorComponent(error) : getCustomRouteComponent(params),
    }),
    [params, hasError, error]
  );

  const handleCustomRouteCTA = useCallback(
    action => {
      onAction({
        type: LEAD_DETAILS_ACTION_TYPES.HANDLE_CUSTOM_ROUTE_CTA,
        payload: action,
      });
    },
    [onAction]
  );

  const handleCreateLeadClick = useCallback(() => {
    onAction({
      type: CONTACT_ACTION_TYPES.HANDLE_OPEN_CREATE_LEAD_MODAL,
    });
  }, [onAction]);

  const showLeadInfoModal = useCallback(
    ({ tabKey, formToBeHighlighted, additionalInfo, onSubmitCb, entity, initialValues, viewType }) => {
      onAction({
        type: LEAD_DETAILS_ACTION_TYPES.SHOW_LEAD_INFO_MODAL,
        payload: {
          tabKey,
          formToBeHighlighted,
          additionalInfo,
          onSubmitCb,
          entity,
          initialValues,
          viewType,
        },
      });
    },
    [onAction]
  );

  const setInfoActiveTab = useCallback(
    activeTab => {
      onAction({
        type: LEAD_DETAILS_ACTION_TYPES.SET_INFO_ACTIVE_TAB,
        payload: { rightPanelTab: activeTab },
      });
    },
    [onAction]
  );

  const handleSetContactDetails = useCallback(
    (contact, params = EMPTY_OBJECT) => {
      setDocumentTitle(CRMCustomerReader.displayName(contact));
      onAction({
        type: CONTACT_ACTION_TYPES.HANDLE_SET_CONTACT_DETAILS,
        payload: { contact, ...params },
      });
    },
    [onAction]
  );

  const handleOnLeadCreateCb = useCallback(
    lead => {
      onAction({
        type: CONTACT_ACTION_TYPES.HANDLE_ON_LEAD_CREATE_CALLBACK,
        payload: { lead },
      });
    },
    [onAction]
  );

  if (isDefaultRoute && showLeadDetails) {
    return (
      <Illustration
        mainText={__('Sit back and relax! While we load the data')}
        image={ASSETS.SIT_BACK_AND_RELAX}
        alt={__('Loading')}
        size={4}
      />
    );
  }

  if (isDefaultRoute) {
    return null;
  }

  const customRouteComponent = (
    <CustomRouteComponent
      className={styles.customRoute}
      handleCustomRouteCTA={handleCustomRouteCTA}
      onCreateLeadClick={handleCreateLeadClick}
    />
  );

  const isCustomerOneViewEnabled = isCRMCustomerOneViewEnabled(getDealerPropertyValue);

  return (
    <>
      <PropertyControlledComponent controllerProperty={!CustomRouteComponent} fallback={customRouteComponent}>
        <Suspense fallback={<LeadDetailsOverviewSkeleton />}>
          <LazyLeadDetailsOverview
            settings={settings}
            getLeadDetails={getLeadDetails}
            navigate={navigate}
            location={location}
            onAction={onAction}
            leadDetails={leadDetails}
            activityLogsOnAction={activityLogsOnAction}
            isLoading={isLoading}
            liveChatCount={liveChatCount}
            isCTCDisabled={isCTCDisabled}
            getDealerPropertyValue={getDealerPropertyValue}
            openLeadInfo={showLeadInfoModal}
            setLeadDetails={updateLeadInState}
            isCustomerOneViewEnabled
            dealerInfo={dealerInfo}
            communicationOptinPreference={communicationOptinPreference}
            onMergeDuplicates={onMergeDuplicates}
            isLeftContainerVisible={isLeftContainerVisible}
            isCreatingDeal={isCreatingDeal}
            params={params}
            updateRouteUrl={updateRouteUrl}
          />
        </Suspense>
      </PropertyControlledComponent>
      {!hasError && (
        <Suspense
          fallback={
            <EntityInformationSkeleton className={`${styles.entityInfoContainer} m-t-24 m-r-24 flex flex-column`} />
          }>
          <LazyEntityInformation
            patchBuyerDetails={patchCustomerTypeDetails}
            openLeadInfo={showLeadInfoModal}
            getLeadDetails={getLeadDetails}
            setLeadDetails={updateLeadInState}
            settings={settings}
            rightPanelTab={rightPanelTab}
            isLoading={showSectionLoader}
            crmCustomerDetails={crmCustomerDetails}
            isCustomerOneViewEnabled={isCustomerOneViewEnabled}
            navigate={navigate}
            location={location}
            setInfoActiveTab={setInfoActiveTab}
            entityType={entityType}
            handleSetContactDetails={handleSetContactDetails}
            handleOnLeadCreateCb={handleOnLeadCreateCb}
            hasError={hasError}
          />
        </Suspense>
      )}
    </>
  );
};

LeadAndEntityDetails.propTypes = {
  onAction: PropTypes.func.isRequired,
  rightPanelTab: PropTypes.string,
  settings: PropTypes.object,
  showSectionLoader: PropTypes.bool,
  showLeadDetails: PropTypes.bool,
  crmCustomerDetails: PropTypes.object,
  getDealerPropertyValue: PropTypes.func,
  patchCustomerTypeDetails: PropTypes.func,
  getLeadDetails: PropTypes.func,
  updateLeadInState: PropTypes.func,
  activityLogsOnAction: PropTypes.func,
  isLoading: PropTypes.bool,
  leadDetails: PropTypes.object,
  liveChatCount: PropTypes.number,
  dealerInfo: PropTypes.object,
  communicationOptinPreference: PropTypes.object,
  onMergeDuplicates: PropTypes.func,
  isLeftContainerVisible: PropTypes.bool,
  isCreatingDeal: PropTypes.bool,
  error: PropTypes.object,
  params: PropTypes.object,
  updateRouteUrl: PropTypes.bool,
  communicationSetupConfig: PropTypes.object,
};

LeadAndEntityDetails.defaultProps = {
  showSectionLoader: false,
  showLeadDetails: true,
  crmCustomerDetails: EMPTY_OBJECT,
  rightPanelTab: undefined,
  settings: EMPTY_OBJECT,
  getDealerPropertyValue: _noop,
  patchCustomerTypeDetails: _noop,
  getLeadDetails: _noop,
  updateLeadInState: _noop,
  activityLogsOnAction: _noop,
  isLoading: false,
  leadDetails: EMPTY_OBJECT,
  liveChatCount: undefined,
  dealerInfo: EMPTY_OBJECT,
  communicationOptinPreference: EMPTY_OBJECT,
  onMergeDuplicates: _noop,
  isLeftContainerVisible: false,
  isCreatingDeal: false,
  error: EMPTY_OBJECT,
  params: EMPTY_OBJECT,
  updateRouteUrl: true,
  communicationSetupConfig: EMPTY_OBJECT,
};

export default React.memo(LeadAndEntityDetails);
