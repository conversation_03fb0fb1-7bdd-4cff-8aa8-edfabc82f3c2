import { useNavigate } from 'react-router-dom';
import cx from 'classnames';
import React, { useContext, useMemo, useCallback, memo } from 'react';
import PropTypes from 'prop-types';
import { compose } from 'recompose';

import _noop from 'lodash/noop';
import { CLIENT } from 'tbase/constants/crm';
import { isSalesDepartment } from 'tbusiness/helpers/crm/lead.helpers';
import PropertyControlledComponent from 'tcomponents/molecules/PropertyControlledComponent';
import withSize from 'tcomponents/hoc/withSize';
import LeadDepartmentBadge from 'twidgets/appServices/crm/atoms/leadDepartmentBadge';
import LockPopover from 'twidgets/appServices/crm/molecules/LockPopover';

import { hasVehicleEdit } from 'helpers/lead';
import CustomStages from 'molecules/customStages';

import LeadDetailsContext from '../../../../../../../../context/leadDetails';
import LEAD_DETAILS_ACTION_TYPES from '../../../../../../../../constants/leadDetails.actionTypes';
import {
  getDealConfig,
  isAddNewDealButtonDisabled,
} from '../../../../../../../../helpers/journeyPanel/leadDetails.dealConfig';
import {
  getLeadStatusByDepartment,
  isCustomStageDisabled,
} from '../../../../../../../../helpers/journeyPanel/leadDetails.general';
import TradeInCard from '../../../../../../../../molecules/tradeInCard';
import VehicleCard from '../../../../../../../vehicleCard';
import LeadStages from '../../../../../../../leadStages';
import { DEPARTMENT_VS_RENDERER } from '../../../../../../../../constants/leadDetails.departmentVsRenderer';
import { MIN_BREAK_POINT } from '../../../../../../../../constants/leadDetails.journeyPanel';
import LeadNavigationButton from '../../../../../../../../molecules/leadNavigationButton';
import { makeCustomStageUpdatePayload } from '../../../../../../../../helpers/journeyPanel/leadDetails.request';
import { PastOpportunitiesButton } from '../../../../../../../pastOpportunities';
import getJourneyPanelData from '../../../../../../../../helpers/journeyPanel/leadDetails.getJourneyPanelData';
import { getEntity } from '../../../../../../helpers/leadDetailsV2.entity';
import LeadInfoCard from './organisms/leadInfoCard';
import { handleTradeInClick, handleVehicleClick } from './helpers/journeyPanel.vehicle';
import { getShadowDivStyles } from './helpers/journeyPanel.general';
import JourneyPanelSkeleton from '../../../../molecules/journeyPanelSkeleton';

import commonStyles from '../../../../leadAndEntityDetails.module.scss';
import styles from './journeyPanel.module.scss';

const JourneyPanel = props => {
  const navigate = useNavigate();
  const {
    openLeadInfo,
    getLeadDetails,
    getDealerPropertyValue,
    setLeadDetails,
    onBackToLeadClick,
    isLoading,
    handleDropdownClick,
    showPastOpportunitiesButton,
    contentWidth,
    isMinimized,
    scrollIntoRef,
    location,
    onAction,
    isCreatingDeal,
  } = props;
  const { leadDetails, leadDetailsOnAction, customerOneViewProps, locked, lockedBy, onLeadUnlocked } =
    useContext(LeadDetailsContext);

  const {
    name,
    leadStatus,
    createdTime,
    department,
    showEnrichedLead,
    leadId,
    vehicleInterest,
    vehicleWishlists,
    additionalVehicleInterests,
    isVAL,
    tradeInVehicles,
    leadStages,
    completedLeadStageIds,
    stageValue,
  } = useMemo(() => getJourneyPanelData(leadDetails, getDealerPropertyValue), [leadDetails, getDealerPropertyValue]);

  const entityType = useMemo(
    () =>
      getEntity({
        navigate,
        location,
      }),
    [navigate, location]
  );

  const handleVehicleCardClick = useCallback(
    action => handleVehicleClick({ isVAL, action, leadDetails, getDealerPropertyValue, openLeadInfo }),
    [isVAL, leadDetails, getDealerPropertyValue, openLeadInfo]
  );

  const handleTradeInCardClick = useCallback(() => handleTradeInClick(openLeadInfo), [openLeadInfo]);

  const handleVehicleInterestUpdate = useCallback(
    updatedLead =>
      leadDetailsOnAction({
        type: LEAD_DETAILS_ACTION_TYPES.SET_LEAD_DETAILS,
        payload: { leadDetails: updatedLead },
      }),
    [leadDetailsOnAction]
  );

  const { canEdit, customStageDisabled } = useMemo(
    () => ({
      canEdit: hasVehicleEdit(leadDetails),
      customStageDisabled: isCustomStageDisabled(leadDetails, true),
    }),
    [leadDetails]
  );

  const status = useMemo(() => getLeadStatusByDepartment(stageValue, department), [stageValue, department]);

  const handleCustomStageChange = useCallback(
    customStage => {
      const actionType = customStage
        ? LEAD_DETAILS_ACTION_TYPES.HANDLE_PATCH_LEAD
        : LEAD_DETAILS_ACTION_TYPES.HANDLE_DELETE_LEAD_VALUES;
      const payload = makeCustomStageUpdatePayload({
        customStage,
        leadDetails,
        getDealerPropertyValue,
        client: CLIENT.CRM_UI,
      });
      leadDetailsOnAction({
        type: actionType,
        payload,
      });
    },
    [leadDetailsOnAction, leadDetails, getDealerPropertyValue]
  );
  const isMinimizedByWidth = contentWidth < MIN_BREAK_POINT;

  const _isMinimized = isMinimized || isMinimizedByWidth;

  const { disableCreateDeal, popupMessage } = getDealConfig(leadDetails, getDealerPropertyValue);

  const disableAddNewDealButton = isAddNewDealButtonDisabled(leadDetails, getDealerPropertyValue);

  const Component = DEPARTMENT_VS_RENDERER[department];

  return (
    <>
      {!!showPastOpportunitiesButton && <div style={getShadowDivStyles()} className={styles.shadowDiv}></div>}
      <div
        id="lead-details-journey-panel"
        ref={scrollIntoRef}
        className={cx('p-24', styles.journeyPanel, commonStyles.journeyPanelContainer, {
          'm-b-24': !showPastOpportunitiesButton,
        })}>
        {!!department && (
          <div className={`flex align-items-center justify-content-between ${styles.departmentIndicator}`}>
            <LeadDepartmentBadge department={department} />
          </div>
        )}
        <div className={`flex align-items-center justify-content-between ${styles.lockPopover}`}>
          <LockPopover userDetails={lockedBy} unLockLead={onLeadUnlocked} />
        </div>

        {isLoading ? (
          <JourneyPanelSkeleton />
        ) : (
          <>
            <div className="flex align-items-start m-b-16">
              <LeadInfoCard
                name={name}
                status={leadStatus}
                createdTime={createdTime}
                isEnrichedLead={showEnrichedLead}
                entityType={entityType}
                leadDetails={leadDetails}
                getDealerPropertyValue={getDealerPropertyValue}
                setLeadDetails={setLeadDetails}
              />
              <VehicleCard
                vehicle={vehicleInterest}
                onClick={handleVehicleCardClick}
                canEdit={canEdit}
                department={department}
                additionalVehicleInterests={additionalVehicleInterests}
                vehicleWishlists={vehicleWishlists}
                handleVehicleInterestUpdate={handleVehicleInterestUpdate}
                leadId={leadId}
                displayAvatar={false}
                addVehicleButtonClassName={`${styles.addVehicleButtonClassName} ${styles.addVOI}`}
                addVehicleButtonContentClassName={styles.addVehicleButtonContentClassName}
                getDealerPropertyValue={getDealerPropertyValue}
                highlightReservedAndSoldVehicles
                showCustomYMM
                vehicleInterestContainerClassName="width-auto"
              />

              {!!department && (
                <Component
                  leadDetails={leadDetails}
                  getLeadDetails={getLeadDetails}
                  getDealerPropertyValue={getDealerPropertyValue}
                  isVAL={isVAL}
                  setLeadDetails={setLeadDetails}
                  openLeadInfo={openLeadInfo}
                  disabled={disableCreateDeal}
                  popupMessage={popupMessage}
                  disableAddDeal={disableAddNewDealButton}
                  onAction={onAction}
                  isCreatingDeal={isCreatingDeal}
                />
              )}
              <div className="ml-auto">
                <LeadNavigationButton
                  isMinimized={_isMinimized}
                  handleBackToLeadClick={onBackToLeadClick}
                  leadId={leadId}
                  leadNavigationDetails={customerOneViewProps?.leadNavigationDetails}
                  labelText={__('Latest Lead')}
                />
              </div>
            </div>
            {isSalesDepartment(department) && (
              <TradeInCard
                tradeInVehicles={tradeInVehicles}
                onClick={handleTradeInCardClick}
                leadId={leadId}
                locked={locked}
                isVAL={isVAL}
                shouldDisplayAvatar={false}
                addVehicleButtonClassName={styles.addVehicleButtonClassName}
                addVehicleButtonContentClassName={styles.addVehicleButtonContentClassName}
                titleClassName={styles.tradeInVehicleTitle}
                showCustomYMM
              />
            )}
            <div className="flex justify-content-between">
              <div className={styles.stages}>
                <LeadStages
                  department={department}
                  {...leadStages}
                  status={status}
                  completedStageIds={completedLeadStageIds}
                  isVAL={isVAL}
                />
              </div>
              <CustomStages
                department={department}
                className={styles.customStage}
                onChange={handleCustomStageChange}
                placeholder={__('Custom Stage')}
                value={leadDetails.customStage}
                containerClassName={styles.customStageContainer}
                disabled={customStageDisabled}
              />
            </div>
          </>
        )}
        {!!showPastOpportunitiesButton && (
          <PastOpportunitiesButton
            handleDropdownClick={handleDropdownClick}
            containerClassName={styles.containerClassName}
            contentClassName={styles.contentClassName}
            iconClassName={styles.iconClassName}
          />
        )}
      </div>
    </>
  );
};

JourneyPanel.propTypes = {
  openLeadInfo: PropTypes.func,
  getLeadDetails: PropTypes.func,
  getDealerPropertyValue: PropTypes.func,
  setLeadDetails: PropTypes.func,
  onBackToLeadClick: PropTypes.func,
  isLoading: PropTypes.bool.isRequired,
  handleDropdownClick: PropTypes.func,
  showPastOpportunitiesButton: PropTypes.bool,
  contentWidth: PropTypes.number.isRequired,
  isMinimized: PropTypes.bool,
  scrollIntoRef: PropTypes.object,
  onAction: PropTypes.func,
  isCreatingDeal: PropTypes.bool,
};

JourneyPanel.defaultProps = {
  openLeadInfo: _noop,
  getLeadDetails: _noop,
  getDealerPropertyValue: _noop,
  setLeadDetails: _noop,
  onBackToLeadClick: _noop,
  handleDropdownClick: _noop,
  isMinimized: true,
  showPastOpportunitiesButton: false,
  scrollIntoRef: undefined,
  onAction: _noop,
  isCreatingDeal: false,
};

export default compose(withSize(), memo)(JourneyPanel);
