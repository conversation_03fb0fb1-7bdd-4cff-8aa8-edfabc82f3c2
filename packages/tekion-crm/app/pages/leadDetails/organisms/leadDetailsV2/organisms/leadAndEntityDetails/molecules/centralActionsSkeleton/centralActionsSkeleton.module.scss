@use "tstyles/colors.scss";

.shimmerContainer {
  background-color: colors.$white;
  border-radius: 0.4rem;
}

.notesButton {
  width: 8rem;
  border-radius: 2rem;
  overflow: hidden;

  :global(.ant-skeleton-paragraph) {
    margin: 0;
  }

  :global(.ant-skeleton-paragraph > li) {
    height: 3rem;
  }
}

.shimmerDivider {
  height: 2.2rem;
  width: 1px;
  background-color: colors.$platinum;
  margin: 0 1.5rem;
}

.actionButtonsContainer {
  gap: 1.2rem;
}
