import { useParams, useNavigate } from 'react-router-dom';
import React, { memo, useCallback } from 'react';
import PropTypes from 'prop-types';
import _noop from 'lodash/noop';
import { compose } from 'recompose';

// Components
import { EMPTY_STRING, EMPTY_OBJECT } from 'tbase/app.constants';
import LockPopover from 'twidgets/appServices/crm/molecules/LockPopover';

import CONTACT_ACTION_TYPES from '../../../../../../constants/leadDetails.contactActionTypes';
import { isLimitedAccessRoute } from '../../../../../../helpers/leadDetails.customRoute';
import withLeadDetailsContext from '../../../../../../hocs/withLeadDetailsContext';
import LeadInfoCollapsible from '../../../../../leadInfoCollapsible';

import commonStyles from '../../leadAndEntityDetails.module.scss';

function EntityInformation(props) {
  const params = useParams();
  const navigate = useNavigate();
  const {
    openLeadInfo,
    patchBuyerDetails,
    settings,
    getLeadDetails,
    crmCustomerDetails,
    isCustomerOneViewEnabled,
    location,
    handleSetContactDetails,
    handleOnLeadCreateCb,
    setLeadDetails,
    rightPanelTab,
    entityType,
    setInfoActiveTab,
    leadDetailsOnAction,
    className,
    lockedBy,
    onLeadUnlocked,
    isLoading,
  } = props;

  const handleContactUpdate = useCallback(
    payload => {
      leadDetailsOnAction({
        type: CONTACT_ACTION_TYPES.HANDLE_CONTACT_INFO_UPDATE,
        payload,
      });
    },
    [leadDetailsOnAction]
  );

  const limitedAccess = isLimitedAccessRoute(params);

  return (
    <div className={`${commonStyles.entityInfoContainer} m-t-24 m-r-24 ${className} flex flex-column`}>
      <LockPopover containerClassName="m-r-8 m-t-8" userDetails={lockedBy} unLockLead={onLeadUnlocked} />
      <div className="overflow-auto full-height">
        <LeadInfoCollapsible
          openLeadInfo={openLeadInfo}
          patchBuyerDetails={patchBuyerDetails}
          settings={settings}
          getLeadDetails={getLeadDetails}
          setLeadDetails={setLeadDetails}
          crmCustomerDetails={crmCustomerDetails}
          isCustomerOneViewEnabled={isCustomerOneViewEnabled}
          entityType={entityType}
          onContactInfoUpdate={handleContactUpdate}
          navigate={navigate}
          location={location}
          showChronologicalViewFooter
          setContactDetails={handleSetContactDetails}
          onLeadCreateCb={handleOnLeadCreateCb}
          limitedAccess={limitedAccess}
          hasRestrictedView
          infoActiveTab={rightPanelTab}
          setInfoActiveTab={setInfoActiveTab}
          isLoading={isLoading}
        />
      </div>
    </div>
  );
}

EntityInformation.propTypes = {
  className: PropTypes.string,
  isLoading: PropTypes.bool,
  leadDetailsOnAction: PropTypes.func,
  lockedBy: PropTypes.object,
  openLeadInfo: PropTypes.func,
  onLeadUnlocked: PropTypes.func,
  patchBuyerDetails: PropTypes.func,
  settings: PropTypes.object.isRequired,
  getLeadDetails: PropTypes.func.isRequired,
  crmCustomerDetails: PropTypes.object,
  isCustomerOneViewEnabled: PropTypes.bool,
  handleSetContactDetails: PropTypes.func,
  handleOnLeadCreateCb: PropTypes.func,
  setLeadDetails: PropTypes.func.isRequired,
};

EntityInformation.defaultProps = {
  className: EMPTY_STRING,
  isLoading: false,
  leadDetailsOnAction: _noop,
  lockedBy: EMPTY_OBJECT,
  openLeadInfo: _noop,
  onLeadUnlocked: _noop,
  patchBuyerDetails: _noop,
  crmCustomerDetails: EMPTY_OBJECT,
  isCustomerOneViewEnabled: false,
  handleSetContactDetails: _noop,
  handleOnLeadCreateCb: _noop,
};

export default compose(withLeadDetailsContext, memo)(EntityInformation);
