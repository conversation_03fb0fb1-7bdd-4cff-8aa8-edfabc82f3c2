import React from 'react';

import ActivityLogSkeleton from '@tekion/tekion-widgets/src/appServices/crm/molecules/activityLogSkeleton';

import JourneyPanelSkeleton from '../journeyPanelSkeleton';
import CentralActionsSkeleton from '../centralActionsSkeleton';
import ActivitiesCardSkeleton from '../activitiesCardSkeleton';
import NotesInputSkeleton from '../notesInputSkeleton';

import styles from '../../leadAndEntityDetails.module.scss';

const LeadDetailsOverviewSkeleton = () => (
  <div className={`p-24 ${styles.leadDetailsOverviewContainer}`}>
    <div className={`p-24 ${styles.journeyPanelContainer}`}>
      <JourneyPanelSkeleton />
    </div>
    <div>
      <CentralActionsSkeleton />
      <NotesInputSkeleton />
      <div className={`flex flex-column full-height m-y-24 relative ${styles.activitiesCardContainer}`}>
        <ActivitiesCardSkeleton />
      </div>
      <div className={styles.activityLogContainer}>
        <ActivityLogSkeleton />
      </div>
    </div>
  </div>
);

export default LeadDetailsOverviewSkeleton;
