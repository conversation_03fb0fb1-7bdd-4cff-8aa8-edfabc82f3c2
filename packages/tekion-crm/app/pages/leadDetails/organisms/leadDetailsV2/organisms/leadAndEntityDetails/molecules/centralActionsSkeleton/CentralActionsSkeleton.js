import _times from 'lodash/times';
import React from 'react';

import Skeleton from '@tekion/tekion-components/src/molecules/skeleton';

import { renderActionButton } from './helpers/centralActionsSkeleton.component';
import { PARAGRAPH_PROPS } from './constants/centralActionsSkeleton.general';

import styles from './centralActionsSkeleton.module.scss';

const CentralActionsSkeleton = () => (
  <div className={`flex align-items-center p-8 ${styles.shimmerContainer}`}>
    <Skeleton active title={false} paragraph={PARAGRAPH_PROPS} className={styles.notesButton} />
    <div className={styles.shimmerDivider}></div>
    <div className={`flex align-items-center ${styles.actionButtonsContainer}`}>{_times(8, renderActionButton)}</div>
  </div>
);

export default CentralActionsSkeleton;
