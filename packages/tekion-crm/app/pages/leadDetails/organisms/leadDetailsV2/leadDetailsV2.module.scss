@use "tstyles/component.scss";

.container {
  background-color: component.$lightGray;
  :global(::-webkit-scrollbar) {
    width: 0.6rem;
  }
  :global(::-webkit-scrollbar-track) {
    background: component.$glitter;
    border-radius: 0.4rem;
  }
  :global(::-webkit-scrollbar-thumb) {
    background: component.$platinum;
    border-radius: 0.4rem;
  }
}

.containerWithBanner {
  height: calc(100% - 4rem);
}

.entityListContainer {
  min-width: 28.8rem;
  width: 28.8rem;
  background-color: component.$white;
}
