import produce from 'immer';
import { handleActions } from 'redux-actions';
import _forEach from 'lodash/forEach';
import _includes from 'lodash/includes';
import _set from 'lodash/set';

import ACTION_TYPES from './leadDetailsV2.actionTypes';

const INITIAL_STATE = {
  includeSiteFilter: true,
  useHistoryBack: false,
  useContent: false,
};

const setMetaData = (state, { payload }) =>
  produce(state, draft => {
    if (payload?.useHistoryBack) _set(draft, 'useHistoryBack', payload?.useHistoryBack === 'true');
    if (payload?.useContent) _set(draft, 'useContent', payload?.useContent === 'true');
    if (payload?.includeSiteFilter)
      _set(draft, 'includeSiteFilter', _includes(['true', true], payload?.includeSiteFilter));
    if (payload?.checkContactAssignee)
      _set(draft, 'checkContactAssignee', payload?.checkContactAssignee === 'false' ? false : undefined);
  });

const resetMetaData = () => INITIAL_STATE;

const resetMetaDataByKey = (state, { payload: keysToReset }) =>
  produce(state, draft => {
    _forEach(keysToReset, key => {
      _set(draft, key, INITIAL_STATE[key]);
    });
  });

const ACTION_HANDLERS = {
  [ACTION_TYPES.SET_METADATA]: setMetaData,
  [ACTION_TYPES.RESET_METADATA]: resetMetaData,
  [ACTION_TYPES.RESET_METADATA_BY_KEY]: resetMetaDataByKey,
};

export default handleActions(ACTION_HANDLERS, INITIAL_STATE);
