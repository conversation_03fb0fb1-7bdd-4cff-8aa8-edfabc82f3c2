import React, { PureComponent, Suspense, lazy } from 'react';
import cx from 'classnames';
import PropTypes from 'prop-types';
import { defaultMemoize } from 'reselect';

// Lodash
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _isEqual from 'lodash/isEqual';
import _noop from 'lodash/noop';
import _partial from 'lodash/partial';

// Components
import { EMPTY_OBJECT, EMPTY_STRING, EMPTY_ARRAY } from 'tbase/app.constants';
import { CLIENT, CRM_ENTITIES } from 'tbase/constants/crm';
import { ENTITY_TYPE } from 'tbase/constants/emailCommunication';
import { CUSTOMER_TYPES } from 'tbase/constants/crm/leads/leadCustomer';
import LeadReader from 'tbase/readers/lead.reader';
import LeadAPI from 'tbase/services/crm/leads.api';
import { tget } from 'tbase/utils/general';
import { PlatformPrivacyPreferencesEvents } from 'tbase/helpers/eventEmitter';
import isStringEmpty from 'tbase/utils/isStringEmpty';
import setupReader from 'tbusiness/appServices/crm/readers/setup.reader';
import { isRRGEnabled } from 'tbusiness/appServices/crm/helpers/isRRGEnabled';
import DealerPropertyHelper from 'tcomponents/helpers/sales/dealerPropertyHelper';
import { MESSAGES } from 'twidgets/appServices/crm/constants/toaster';
import { getPatchCustomerTypePayload } from 'twidgets/appServices/crm/factories/lead';
import QuickQuoteAgent from 'twidgets/organisms/templateBuilder/bodyshop/agents/web/quickQuote/quickQuote.agent';
import { NOTIFICATION_ACTIONS } from 'twidgets/appServices/crm/organisms/activityLogs/activityLogs.constants';
import { getLeadName, setCustomVersionAndLastUpdatedSource } from 'twidgets/appServices/crm/helpers/lead';
import { getQueryParamValue, getAPIErrorMsg } from 'twidgets/appServices/crm/utils';
import { getCoordinateToTypeToPrefMap } from 'twidgets/appServices/crm/molecules/communicationIcon/communicationIcon.helpers';
import EditContactDetailsDrawer from 'twidgets/appServices/crm/organisms/editContactDetails';
import { showError } from 'twidgets/appServices/crm/utils/toaster';
import { PLATFORM_PREFERENCE_EVENTS } from 'twidgets/appServices/crm/organisms/platformPrivacyPreferences/platformPreference.constants';
import PlatformPreferenceHelper from 'twidgets/appServices/crm/organisms/platformPrivacyPreferences/PlatformPreferenceHelper';
import ConsentReversalBanner from 'twidgets/organisms/ConsentReversalBanner';
import {
  hideCustomer360Portal,
  updateCustomerInsightsData,
} from 'twidgets/organisms/Customer360/helpers/customerInsightsV2.events';
import QuickQuoteModal from 'twidgets/organisms/sales/quickQuote';
import { withCommunicationSetupConsumer } from 'twidgets/appServices/crm/communicationSetup';

import PDFPrintModalWrapper from 'molecules/pdfPrintModalWrapper';
import CreditApplication from 'organisms/creditApplication';
import LeadsAPI from 'services/leads.api';
import { setDocumentTitle } from 'utils';

import { getIsLegacyAssigneeEnabled } from '../../../../constraints/legacyAssignee';
import { parseComplianceDetails } from '../../../../helpers/compliance';
import LEAD_DETAILS_ACTION_TYPES from '../../constants/leadDetails.actionTypes';
import { FETCH_DUPLICATE_COUNT_DELAY } from '../../constants/leadDetails.general';
import { getPullCustomerDataChanges } from '../../helpers/leadDetails.customer';
import { isCoordinateUpdated } from '../../helpers/leadDetails.coordinate';
import { isCustomLeadRoute, isDefaultLeadRoute } from '../../helpers/leadDetails.customRoute';
import { handleConsentReversalSuccess, showConsentReversalBanner } from '../../helpers/leadDetails.general';
import generateRowsForPullCustomerData from '../../helpers/leadDetails.generateRowsForPullCustomerData';
import LeadDetailsContext from '../../context/leadDetails';
import CompareAndUpdateLeadModal from '../leadInfoCollapsible/oem/gm/components/compareAndUpdateLeadModal';
import PullCustomerDataModal from '../../molecules/pullCustomerDataModal';
import { getParamsToBePersisted, getLeadRoute } from '../../helpers/leadDetails.leadNavigation';
import CHECK_LEAD_LOCK_PROCESS from '../../helpers/leadDetails.leadLockProcess';
import { getQueryParamsValues } from '../../helpers/leadDetails.queryParams';
import DealsListModal from '../dealsListModal';
import { PAST_OPPORTUNITIES_ACTION_TYPES } from '../pastOpportunities';
import { fetchLeadDuplicateCount } from './helpers/leadDetailsV2.general';
import LeftContainerSkeleton from '../../molecules/leftContainerSkeleton';
import LeadAndEntityDetails from './organisms/leadAndEntityDetails';

const LazyEntityList = lazy(() => import('./organisms/entityList' /* webpackChunkName: "EntityList" */));

import styles from './leadDetailsV2.module.scss';

export class LeadDetails extends PureComponent {
  generateRowsForPullCustomerDataMemoized = defaultMemoize(generateRowsForPullCustomerData);

  constructor(props) {
    super(props);
    props.subscribePusher(this);
  }

  componentDidMount() {
    const { params, onAction, navigate, location, setMetaData, entityType, showEntityList } = this.props;
    const isCustomRoute = isCustomLeadRoute(params);
    const {
      useHistoryBack,
      useContent,
      rightContainerPanel,
      includeSiteFilter,
      checkContactAssignee,
      customerInsightsExpand: isCustomerInsightsExpand,
    } = getQueryParamsValues({
      navigate,
      location,
    });
    setMetaData({ useHistoryBack, useContent, includeSiteFilter, checkContactAssignee });
    this.initMetadata(isCustomRoute);
    onAction({
      type: LEAD_DETAILS_ACTION_TYPES.INIT_CONTAINER_VISIBILITY,
      payload: { entityType, isCustomRoute, showEntityList, rightContainerPanel, isCustomerInsightsExpand },
    });
    // subscribe only if central modal is enabled
    if (DealerPropertyHelper.isCentralCommunicationOptionPreferenceEnabled()) {
      PlatformPreferenceHelper.init();
      PlatformPrivacyPreferencesEvents.on(
        PLATFORM_PREFERENCE_EVENTS.CENTRAL_PREFERENCE_UPDATED,
        this.getCommunicationPreferences
      );
    }
  }

  componentDidUpdate(prevProps) {
    const { params, leadDetails, leadLockData, onAction, location, getDealerPropertyValue } = this.props;

    if (isCustomLeadRoute(prevProps.params) && !isCustomLeadRoute(params)) {
      this.init(false);
      return;
    }
    if (isCustomLeadRoute(params)) {
      const entityId = getQueryParamValue(location, 'entityId');
      const prevEntityId = getQueryParamValue(prevProps.location, 'entityId');
      if (entityId !== prevEntityId) {
        this.initCustomRoute();
        return;
      }
    }
    const currentLeadId = LeadReader.leadId(leadDetails);
    const prevLeadId = LeadReader.leadId(prevProps.leadDetails);
    if (currentLeadId !== prevLeadId) {
      hideCustomer360Portal();
    }
    const prevCrmBuyerCustomerId = LeadReader.crmBuyerCustomerId(prevProps.leadDetails);
    const prevLockedId = tget(prevProps, ['leadLockData', 'lockedId']);
    const currentLockedValue = leadLockData?.locked;
    const prevLockedValue = tget(prevProps, ['leadLockData', 'locked'], false);
    const { id: nextLead } = _get(this.props, 'params') || EMPTY_OBJECT;
    const { id: currentLead } = _get(prevProps, 'params') || EMPTY_OBJECT;
    if (nextLead !== currentLead && nextLead && !isCustomLeadRoute(params)) {
      this.getLeadDetails(nextLead, _noop, {
        shouldShowSectionLoader: true,
        isNewLead: true,
        syncWithRTC: isRRGEnabled(getDealerPropertyValue),
      });
    }

    if (!_isEqual(currentLeadId, prevLeadId)) {
      if (!isStringEmpty(currentLeadId) && !isStringEmpty(prevLeadId)) {
        onAction({
          type: LEAD_DETAILS_ACTION_TYPES.HANDLE_LEAD_CHANGE,
          payload: { id: currentLeadId, previousLeadId: prevLeadId, prevCrmBuyerCustomerId },
        });
      }
      setDocumentTitle(getLeadName(leadDetails, getDealerPropertyValue));
      this.QuoteAgent = new QuickQuoteAgent({
        entityType: ENTITY_TYPE.LEAD,
        entityId: currentLeadId,
      });
    }
    if (!currentLockedValue && prevLockedValue && _isEqual(currentLeadId, prevLockedId)) {
      this.handleUnlockLead();
    }
  }

  componentWillUnmount() {
    const { resetMetaData, onAction, leadDetails, resetLeftContainerState } = this.props;
    resetMetaData();
    resetLeftContainerState();
    onAction({
      type: LEAD_DETAILS_ACTION_TYPES.UNSUBSCRIBE_LEAD_EVENTS,
      payload: {
        previousLeadId: LeadReader.leadId(leadDetails),
      },
    });
  }

  getContextValues = defaultMemoize(
    (
      leadDetails,
      leadLockData,
      onLeadUnlocked,
      onSyncData,
      saveLeadDetails,
      onAction,
      isOpenedThroughWorkspace,
      notRespondedTabs,
      liveChatCount,
      customerOneViewProps,
      getDealerPropertyValue,
      isCustomRoute,
      duplicatesCount,
      onMergeDuplicates
    ) => {
      const isDealOpen = !!leadLockData?.locked;
      const locked = CHECK_LEAD_LOCK_PROCESS.run({
        isDealOpen,
        getDealerPropertyValue,
        leadDetails,
      });
      return {
        ...{
          ...leadLockData,
          locked,
        },
        isDealOpen,
        customerOneViewProps,
        leadDetails,
        onLeadUnlocked,
        onSyncData,
        saveLeadDetails,
        leadDetailsOnAction: onAction,
        isOpenedThroughWorkspace,
        notRespondedTabs,
        liveChatCount,
        isCustomRoute,
        duplicatesCount,
        onMergeDuplicates,
      };
    }
  );

  initMetadata = isCustomRoute => {
    if (!isCustomRoute) {
      this.init();
    } else {
      this.initCustomRoute();
    }
  };

  handleUnlockLead = () => {
    const { onAction } = this.props;
    onAction({ type: LEAD_DETAILS_ACTION_TYPES.HANDLE_UNLOCK_LEAD });
  };

  setLeftContainerVisibility = showEntityList => {
    const { onAction } = this.props;
    onAction({
      type: LEAD_DETAILS_ACTION_TYPES.SET_LEFT_CONTAINER_VISIBILITY,
      payload: {
        showEntityList,
      },
    });
  };

  handleNotificationAction = () => {
    const { location, onAction } = this.props;
    const notificationType = getQueryParamValue(location, 'action') || EMPTY_STRING;
    if (notificationType)
      onAction({
        type: LEAD_DETAILS_ACTION_TYPES.HANDLE_NOTIFICATION_EVENTS,
        payload: { type: NOTIFICATION_ACTIONS[notificationType] },
      });
  };

  fetchLeadSuccessCb = () => {
    const { onAction, id } = this.props;
    this.handleNotificationAction();
    onAction({
      type: LEAD_DETAILS_ACTION_TYPES.INSTANTIATE_LEAD_PUSHER,
    });
    onAction({ type: LEAD_DETAILS_ACTION_TYPES.FETCH_LEAD_DUPLICATE_COUNT, payload: { id } });
  };

  init = (showLoader = true) => {
    const { checkLockStatus, getDealerPropertyValue } = this.props;
    const { id } = _get(this.props, 'params') || EMPTY_OBJECT;
    this.getLeadDetails(id, this.fetchLeadSuccessCb, {
      showLoader,
      isNewLead: true,
      syncWithRTC: isRRGEnabled(getDealerPropertyValue),
    });
    this.QuoteAgent = new QuickQuoteAgent({
      entityType: ENTITY_TYPE.LEAD,
      entityId: id,
    });
    checkLockStatus(id);
  };

  initCustomRoute = () => {
    const { onAction } = this.props;
    onAction({
      type: LEAD_DETAILS_ACTION_TYPES.HANDLE_INIT_CUSTOM_ROUTE,
    });
  };

  getCommunicationPreferences = async lead => {
    const { onAction } = this.props;
    onAction({
      type: LEAD_DETAILS_ACTION_TYPES.FETCH_COMMUNICATION_PREFERENCES,
      payload: { lead },
    });
  };

  getNDCPreferences = async () => {
    const { onAction } = this.props;
    onAction({
      type: LEAD_DETAILS_ACTION_TYPES.FETCH_NDC_PREFERENCES,
    });
  };

  componentDidReceiveEvent = event => {
    const { onAction } = this.props;
    onAction({
      type: LEAD_DETAILS_ACTION_TYPES.HANDLE_PUSHER_EVENTS,
      payload: {
        event,
      },
    });
  };

  getLeadDetails = async (
    id,
    onFetchLeadSuccessCb = _noop,
    {
      showLoader = false,
      shouldShowSectionLoader = false,
      isNewLead = false,
      shouldUpdateLookupInfo = false,
      syncWithRTC = false,
      silentUpdateLinkedLeads,
    } = EMPTY_OBJECT
  ) => {
    const { onAction } = this.props;
    onAction({
      type: LEAD_DETAILS_ACTION_TYPES.FETCH_LEAD_DETAILS,
      payload: {
        onFetchLeadSuccessCb,
        id,
        showLoader,
        shouldShowSectionLoader,
        isNewLead,
        shouldUpdateLookupInfo,
        syncWithRTC,
        silentUpdateLinkedLeads,
      },
    });
  };

  updateLeadInState = (leadDetails, onUpdateCb = _noop) => {
    const { onAction } = this.props;
    onAction({
      type: LEAD_DETAILS_ACTION_TYPES.SET_LEAD_DETAILS,
      payload: { leadDetails, onUpdateCb },
    });
  };

  saveLeadDetails = async payload => {
    const { getDealerPropertyValue, leadDetails } = this.props;
    const leadDetailsPayload = setCustomVersionAndLastUpdatedSource({
      payload,
      client: CLIENT.CRM_UI,
      leadDetails,
      getDealerPropertyValue,
    });
    const { response, error } = await LeadAPI.updateLead(leadDetailsPayload);
    if (_isEmpty(response)) {
      showError(getAPIErrorMsg(error) || MESSAGES.UPDATE_DETAILS_FAILED);
      return null;
    }

    this.updateLeadInState(response);

    return response;
  };

  hidePullCustomerDataModal = () => {
    const { onAction } = this.props;
    onAction({
      type: LEAD_DETAILS_ACTION_TYPES.HIDE_PULL_CUSTOMER_DATA_MODAL,
    });
  };

  onPullCustomerDataCheckboxChange = key => event => {
    const value = event?.target?.checked;
    const { onAction } = this.props;
    onAction({
      type: LEAD_DETAILS_ACTION_TYPES.PULL_CUSTOMER_DATA_CHECKBOX_CHANGE,
      payload: { key, value },
    });
  };

  handlePullCustomerDataModalSubmit = async () => {
    const { crmCustomerDetails, pullDataModalFormValues, leadDetails } = this.props;
    const changes = getPullCustomerDataChanges(leadDetails, crmCustomerDetails, pullDataModalFormValues);
    if (!_isEmpty(changes)) {
      this.patchCustomerTypeDetails(changes);
    }
    this.hidePullCustomerDataModal();
  };

  onQuickQuoteModalSubmit = submitCbProps => {
    const { onAction } = this.props;
    onAction({
      type: LEAD_DETAILS_ACTION_TYPES.ON_QUICK_QUOTE_SUBMIT,
      payload: { submitCbProps },
    });
  };

  hideQuickQuoteModal = () => {
    const { onAction } = this.props;
    onAction({
      type: LEAD_DETAILS_ACTION_TYPES.SET_QUICK_QUOTE_MODAL_VISIBILITY,
      payload: {
        isQuickQuoteModalVisible: false,
      },
    });
  };

  pollLeadHeartBeat = () => {
    const { onAction } = this.props;
    onAction({
      type: LEAD_DETAILS_ACTION_TYPES.UPDATE_HEARTBEAT,
    });
  };

  patchCustomerTypeDetails = async (changes, customerType = CUSTOMER_TYPES.BUYER) => {
    const { getDealerPropertyValue, leadDetails } = this.props;
    const payload = getPatchCustomerTypePayload({
      leadDetails,
      changes,
      customerType,
      client: CLIENT.CRM_UI,
      getDealerPropertyValue,
    });
    const { response, error } = await LeadsAPI.patchBuyerDetails(payload);

    if (!_isEmpty(response)) {
      this.updateLeadInState(response);
      updateCustomerInsightsData({
        leadDetails: response,
      });
      // TODO: this should only when coordinates are getting updated
      if (isCoordinateUpdated(changes)) {
        this.getCommunicationPreferences();
        this.getNDCPreferences();
      }
    } else showError(getAPIErrorMsg(error) || MESSAGES.UPDATE_DETAILS_FAILED);
    return response;
  };

  onSyncData = () => {
    const { onAction } = this.props;
    onAction({
      type: LEAD_DETAILS_ACTION_TYPES.SYNC_DATA_FROM_RRG,
    });
  };

  handleActivityLogsAction = action => {
    const { onAction } = this.props;
    onAction({
      type: LEAD_DETAILS_ACTION_TYPES.HANDLE_NOTIFICATION_EVENTS,
      payload: action,
    });
  };

  onEntityActionCb = leadId => {
    const { id: currentLead } = tget(this.props, 'params', EMPTY_OBJECT);
    if (leadId && leadId === currentLead) {
      this.getLeadDetails(leadId);
    }
  };

  setShowLeadDetails = value => {
    const { onAction } = this.props;
    onAction({
      type: LEAD_DETAILS_ACTION_TYPES.SHOW_LEAD_DETAILS,
      payload: { value },
    });
  };

  onMergeDuplicates = () => {
    const { id: leadId } = tget(this.props, 'params', EMPTY_OBJECT);
    this.getLeadDetails(leadId, async () => {
      const { onAction } = this.props;
      onAction({ type: PAST_OPPORTUNITIES_ACTION_TYPES.ON_APP_INIT });
      setTimeout(fetchLeadDuplicateCount(leadId, onAction), FETCH_DUPLICATE_COUNT_DELAY);
    });
  };

  render() {
    const {
      communicationPreferences,
      navigate,
      location,
      settings,
      leadDetails,
      leadLockData,
      onAction,
      onLeadUnlocked,
      liveChatCount,
      notRespondedTabs,
      getDealerPropertyValue,
      crmCustomerDetails,
      isPullDataModalVisible,
      pullDataModalFormValues,
      linkedLeadLoading,
      selectedFilters,
      leadNavigationDetails,
      pastOpportunitiesRoJobsLoading,
      pastOpportunitiesRoJobsError,
      pastOpportunitiesRoJobs,
      isPastOpportunitiesOpen,
      pastOpportunitiesLoading,
      isFetchingLinkedLeadsCount,
      linkedLeadsCount,
      pastOpportunitiesData,
      selectedOEMDealerSites,
      showSectionLoader,
      languageOptions,
      getFormattedPhoneNumber,
      rightPanelTab,
      showLeadDetails,
      isQuickQuoteModalVisible,
      dealerInfo,
      communicationOptinPreference,
      pdfEventInfo,
      resetMetaDataByKey,
      useHistoryBack,
      showLeftContainer,
      getSources,
      getSourceGroups,
      dealStatusOptions,
      leadStatusOptions,
      duplicatesCount,
      resetLeftContainerState,
      paymentOptionConfigs,
      params,
      isCreatingDeal,
      error,
      entityType,
      updateRouteUrl,
      onLeadNavigation,
      showHeaderLeftSection,
      queryParams,
      getLeadStatus,
      getDealStatus,
      communicationSetupConfig,
      fetchLegacyOptions,
    } = this.props;

    const leadWithEntityInfo = {
      ...leadDetails,
      communicationPreferences,
      coordinateToModeToCommPrefs: getCoordinateToTypeToPrefMap(communicationPreferences),
    };
    const isOpenedThroughWorkspace = getQueryParamValue(location, 'isOpenedThroughWorkspace');
    const customerOneViewProps = {
      pastOpportunitiesLoading: showSectionLoader || pastOpportunitiesLoading,
      isFetchingLinkedLeadsCount,
      linkedLeadsCount,
      isPastOpportunitiesOpen,
      pastOpportunitiesData,
      linkedLeadLoading,
      selectedFilters,
      leadNavigationDetails,
      isCustomerOneViewEnabled: true,
      pastOpportunitiesRoJobsLoading,
      pastOpportunitiesRoJobsError,
      pastOpportunitiesRoJobs,
      selectedOEMDealerSites,
    };
    const contextValues = this.getContextValues(
      leadWithEntityInfo,
      leadLockData,
      onLeadUnlocked,
      this.onSyncData,
      this.saveLeadDetails,
      onAction,
      isOpenedThroughWorkspace,
      notRespondedTabs,
      liveChatCount,
      customerOneViewProps,
      getDealerPropertyValue,
      isCustomLeadRoute(params),
      duplicatesCount,
      this.onMergeDuplicates
    );
    const rowsForPullCustomerData = this.generateRowsForPullCustomerDataMemoized(
      leadDetails,
      crmCustomerDetails,
      getFormattedPhoneNumber
    );
    const { locked } = leadLockData || EMPTY_OBJECT;

    const leadId = LeadReader.leadId(leadDetails);

    const onDealUpdateCb = _partial(this.getLeadDetails, leadId);

    const getLeadDetails = () => this.getLeadDetails(leadId);

    const isDefaultRoute = isDefaultLeadRoute(params);

    const isTestDriveCrmSetupEnabled = setupReader.advancedTestDrive(settings);

    const dataComplianceDetails = LeadReader.dataComplianceDetails(leadDetails);
    const consentInfo = parseComplianceDetails(dataComplianceDetails);

    const isConsentEnabled = showConsentReversalBanner(consentInfo, getDealerPropertyValue);
    const entityId = LeadReader.crmBuyerCustomerId(leadDetails);

    return (
      <LeadDetailsContext.Provider value={contextValues}>
        {isConsentEnabled && (
          <ConsentReversalBanner
            consentInfo={consentInfo}
            entityId={entityId}
            callback={handleConsentReversalSuccess({ getLeadDetails: this.getLeadDetails, leadId })}
          />
        )}
        <div
          className={cx('flex flex-row', styles.container, {
            'full-height': !isConsentEnabled,
            [styles.containerWithBanner]: isConsentEnabled,
          })}>
          <Suspense fallback={<LeftContainerSkeleton className={styles.entityListContainer} />}>
            <LazyEntityList
              leadDetails={leadDetails}
              isDefaultRoute={isDefaultRoute}
              entityType={entityType}
              onLeadNavigation={onLeadNavigation}
              navigate={navigate}
              location={location}
              onEntityActionCb={this.onEntityActionCb}
              setShowLeadDetails={this.setShowLeadDetails}
              getDealerPropertyValue={getDealerPropertyValue}
              isTestDriveCrmSetupEnabled={isTestDriveCrmSetupEnabled}
              resetMetaDataByKey={resetMetaDataByKey}
              useHistoryBack={useHistoryBack}
              setLeftContainerVisibility={this.setLeftContainerVisibility}
              showLeftContainer={showLeftContainer}
              settings={settings}
              getSources={getSources}
              getSourceGroups={getSourceGroups}
              dealStatusOptions={dealStatusOptions}
              leadStatusOptions={leadStatusOptions}
              resetLeftContainerState={resetLeftContainerState}
              paymentOptionConfigs={paymentOptionConfigs}
              showHeaderLeftSection={showHeaderLeftSection}
              queryParams={queryParams}
              updateRouteUrl={updateRouteUrl}
              getLeadStatus={getLeadStatus}
              getDealStatus={getDealStatus}
              fetchLegacyOptions={fetchLegacyOptions}
            />
          </Suspense>
          <LeadAndEntityDetails
            communicationSetupConfig={communicationSetupConfig}
            leadDetails={leadWithEntityInfo}
            params={params}
            settings={settings}
            rightPanelTab={rightPanelTab}
            showSectionLoader={showSectionLoader}
            showLeadDetails={showLeadDetails}
            navigate={navigate}
            location={location}
            onAction={onAction}
            getDealerPropertyValue={getDealerPropertyValue}
            crmCustomerDetails={crmCustomerDetails}
            languageOptions={languageOptions}
            liveChatCount={liveChatCount}
            patchCustomerTypeDetails={this.patchCustomerTypeDetails}
            getLeadDetails={this.getLeadDetails}
            updateLeadInState={this.updateLeadInState}
            activityLogsOnAction={this.handleActivityLogsAction}
            isLoading={showSectionLoader}
            dealerInfo={dealerInfo}
            communicationOptinPreference={communicationOptinPreference}
            onMergeDuplicates={this.onMergeDuplicates}
            isLeftContainerVisible={showLeftContainer}
            isCreatingDeal={isCreatingDeal}
            error={error}
            updateRouteUrl={updateRouteUrl}
          />

          {isPullDataModalVisible && (
            <PullCustomerDataModal
              handleCancel={this.hidePullCustomerDataModal}
              handleSubmit={this.handlePullCustomerDataModalSubmit}
              data={rowsForPullCustomerData}
              onChange={this.onPullCustomerDataCheckboxChange}
              pullDataModalFormValues={pullDataModalFormValues}
            />
          )}
          {isQuickQuoteModalVisible && (
            <QuickQuoteModal
              entity={_get(this.QuoteAgent, 'props')}
              getEntityData={_get(this.QuoteAgent, 'getEntityData')}
              onSubmit={this.onQuickQuoteModalSubmit}
              onClose={this.hideQuickQuoteModal}
              paymentOptionConfigs={paymentOptionConfigs}
            />
          )}
          <CreditApplication
            lead={leadDetails}
            settings={settings}
            onCreditAppSubmit={getLeadDetails}
            locked={locked}
            getDealerPropertyValue={getDealerPropertyValue}
            pollLeadHeartBeat={this.pollLeadHeartBeat}
          />
          <PDFPrintModalWrapper onPrint={getLeadDetails} pdfEventInfo={pdfEventInfo} />
          <CompareAndUpdateLeadModal onSubmit={this.patchCustomerTypeDetails} />
          <DealsListModal
            onSuccessCb={onDealUpdateCb}
            leadId={LeadReader.leadId(leadDetails)}
            crmBuyerCustomerId={LeadReader.crmBuyerCustomerId(leadDetails)}
            leadCustomerName={getLeadName(leadDetails, getDealerPropertyValue)}
            settings={settings}
            dealStatusOptions={dealStatusOptions}
            getDealStatus={getDealStatus}
          />
          <EditContactDetailsDrawer languageOptions={languageOptions} />
        </div>
      </LeadDetailsContext.Provider>
    );
  }
}

LeadDetails.propTypes = {
  fetchLegacyOptions: PropTypes.func.isRequired,
  checkLockStatus: PropTypes.func.isRequired,
  communicationPreferences: PropTypes.array.isRequired,
  leadDetails: PropTypes.object.isRequired,
  leadLockData: PropTypes.object.isRequired,
  liveChatCount: PropTypes.number.isRequired,
  onAction: PropTypes.func.isRequired,
  onLeadUnlocked: PropTypes.func.isRequired,
  rightPanelTab: PropTypes.string.isRequired,
  setMetaData: PropTypes.func.isRequired,
  settings: PropTypes.object.isRequired,
  showSectionLoader: PropTypes.bool,
  subscribePusher: PropTypes.func.isRequired,
  resetMetaData: PropTypes.func,
  showLeadDetails: PropTypes.bool,
  isPastOpportunitiesOpen: PropTypes.bool,
  pastOpportunitiesData: PropTypes.object,
  pastOpportunitiesLoading: PropTypes.bool,
  isFetchingLinkedLeadsCount: PropTypes.bool,
  linkedLeadsCount: PropTypes.object,
  linkedLeadLoading: PropTypes.bool,
  selectedFilters: PropTypes.array,
  leadNavigationDetails: PropTypes.string,
  crmCustomerDetails: PropTypes.object,
  getDealerPropertyValue: PropTypes.func,
  isPullDataModalVisible: PropTypes.bool,
  pullDataModalFormValues: PropTypes.array,
  languageOptions: PropTypes.array,
  pastOpportunitiesRoJobsLoading: PropTypes.bool,
  pastOpportunitiesRoJobsError: PropTypes.bool,
  pastOpportunitiesRoJobs: PropTypes.array,
  selectedOEMDealerSites: PropTypes.array,
  getDealStatus: PropTypes.func,
  getLeadStatus: PropTypes.func,
  pdfEventInfo: PropTypes.object.isRequired,
  resetLeftContainerState: PropTypes.func,
  dealerInfo: PropTypes.object,
  communicationOptinPreference: PropTypes.object,
  paymentOptionConfigs: PropTypes.array,
  duplicatesCount: PropTypes.number,
  navigate: PropTypes.func.isRequired,
  location: PropTypes.object.isRequired,
  params: PropTypes.object.isRequired,
  isCreatingDeal: PropTypes.bool,
  error: PropTypes.object,
  entityType: PropTypes.string,
  showEntityList: PropTypes.bool,
  updateRouteUrl: PropTypes.bool,
  onLeadNavigation: PropTypes.func,
  showHeaderLeftSection: PropTypes.bool,
  communicationSetupConfig: PropTypes.object,
};

LeadDetails.defaultProps = {
  showSectionLoader: false,
  resetMetaData: _noop,
  showLeadDetails: true,
  isPastOpportunitiesOpen: false,
  pastOpportunitiesData: EMPTY_OBJECT,
  pastOpportunitiesLoading: true,
  isFetchingLinkedLeadsCount: false,
  linkedLeadsCount: EMPTY_OBJECT,
  linkedLeadLoading: true,
  selectedFilters: EMPTY_ARRAY,
  leadNavigationDetails: EMPTY_OBJECT,
  crmCustomerDetails: EMPTY_OBJECT,
  getDealerPropertyValue: _noop,
  isPullDataModalVisible: false,
  pullDataModalFormValues: EMPTY_ARRAY,
  languageOptions: EMPTY_ARRAY,
  pastOpportunitiesRoJobsLoading: false,
  pastOpportunitiesRoJobsError: false,
  pastOpportunitiesRoJobs: EMPTY_ARRAY,
  selectedOEMDealerSites: EMPTY_ARRAY,
  getDealStatus: _noop,
  getLeadStatus: _noop,
  resetLeftContainerState: _noop,
  dealerInfo: EMPTY_OBJECT,
  communicationOptinPreference: EMPTY_OBJECT,
  paymentOptionConfigs: EMPTY_ARRAY,
  duplicatesCount: 0,
  isCreatingDeal: false,
  error: EMPTY_OBJECT,
  entityType: CRM_ENTITIES.LEADS,
  showEntityList: true,
  updateRouteUrl: true,
  onLeadNavigation: _noop,
  showHeaderLeftSection: true,
  communicationSetupConfig: EMPTY_OBJECT,
};

export default withCommunicationSetupConsumer(LeadDetails);
