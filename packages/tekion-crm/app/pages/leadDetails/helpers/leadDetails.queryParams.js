import _reduce from 'lodash/reduce';

import { EMPTY_OBJECT } from 'tbase/app.constants';
import { getQueryParamValue } from 'twidgets/appServices/crm/utils';

const queryParamsDefaults = {
  showEntityList: false,
  useHistoryBack: false,
  useContent: false,
  rightContainerPanel: undefined,
  includeSiteFilter: true,
  checkContactAssignee: undefined,
  customerInsightsExpand: false,
  filter: undefined,
  entityId: undefined,
};

const getQueryParamOrDefault = (location, paramName, defaultValue) =>
  getQueryParamValue(location, paramName) || defaultValue;

const queryParamsIteratee =
  ({ location }) =>
  (accumulator, defaultValue, paramName) => ({
    ...accumulator,
    [paramName]: getQueryParamOrDefault(location, paramName, defaultValue),
  });

export const getQueryParamsValues = ({ navigate, location }) =>
  _reduce(
    queryParamsDefaults,
    queryParamsIteratee({
      navigate,
      location,
    }),
    EMPTY_OBJECT
  );
