import React from 'react';
import PropTypes from 'prop-types';

import Header from './molecules/header';
import Body from './molecules/body';

const LeftContainerSkeleton = props => {
  const { className } = props;
  return (
    <div className={`p-16 ${className}`}>
      <Header />
      <Body />
    </div>
  );
};

LeftContainerSkeleton.propTypes = {
  className: PropTypes.string,
};

LeftContainerSkeleton.defaultProps = {
  className: '',
};

export default LeftContainerSkeleton;
