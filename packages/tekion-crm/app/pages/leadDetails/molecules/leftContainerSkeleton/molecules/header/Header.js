import React from 'react';

import Skeleton from '@tekion/tekion-components/src/molecules/skeleton';

import { FILTER_SECTION_TITLE_PROPS, SEARCH_SECTION_TITLE_PROPS } from './constant/header.general';

import styles from './header.module.scss';

const Header = () => (
  <div className={`p-16 flex flex-column ${styles.container}`}>
    <Skeleton active title={FILTER_SECTION_TITLE_PROPS} paragraph={false} />
    <Skeleton className={styles.searchSection} active title={SEARCH_SECTION_TITLE_PROPS} paragraph={false} />
  </div>
);

export default Header;
