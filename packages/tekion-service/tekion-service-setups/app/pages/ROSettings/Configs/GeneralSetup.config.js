import cx from 'classnames';

import _includes from 'lodash/includes';
import _toLower from 'lodash/toLower';
import _stubFalse from 'lodash/stubFalse';

import { getSupportedOems } from 'twidgets/appServices/service/utils/selectors';

import { AsyncSelectInput } from 'tcomponents/organisms/FormBuilder/fieldRenderers/AsyncSelect';

import NumberInput from 'twidgets/fieldRenderers/numberInputField';
import Heading from 'tcomponents/atoms/Heading';
import Content from 'tcomponents/atoms/Content';
import MultiSelectField from 'tcomponents/organisms/FormBuilder/fieldRenderers/MultiSelectField';
import VirtualizedAsyncMultiSelectFieldRenderer from 'tcomponents/organisms/FormBuilder/fieldRenderers/VirtualizedAsyncMultiSelectFieldRenderer';
import RadioGroupInput from 'tcomponents/organisms/FormBuilder/fieldRenderers/radio';
import Select from 'tcomponents/organisms/FormBuilder/fieldRenderers/select';
import TableInputField from 'tcomponents/molecules/tableInputField/TableInputField';
import VirtualizedSelectInput from 'tcomponents/organisms/FormBuilder/fieldRenderers/virtualizedSelectInput';
import ServiceEnv from 'utils/serviceEnv';
import { isRequiredRule } from 'tbase/utils/formValidators';
import { isLaneInspectionEnabled } from 'twidgets/appServices/service/helpers/laneInspection.helper';
import Constraints from 'helpers/constraints';
import { getSkillsOptions } from 'twidgets/appServices/service/utils';
import { getOpCodeSearchDefaultSort } from 'twidgets/appServices/service/helpers/opCode.helpers';
import { filterRows } from 'tcomponents/organisms/FormBuilder/utils/general';
import { getBatchedLookupApiParams } from 'tbusiness/actions/service/batchedLookup.actions';
import { OEM } from 'tbase/constants/oem.constants';
import { CONVERSION_IDS, UNIT_LABEL_TYPE, CONVERSION_LEVEL } from '@tekion/tekion-conversion-web';
import { EMPTY_ARRAY, EMPTY_STRING } from 'tbase/app.constants';
import {
  DEFAULT_SEARCH_FILTER,
  OPCODE_SOURCE_DEFAULT_FILTER,
  OPCODE_TYPE_DEFAULT_FILTER,
} from 'tbase/constants/repairOrder/opcode';
import DEALER_PROPERTY from 'tbase/constants/dealerProperties';
import {
  FIELD_IDS,
  JOB_SEQUENCE_TYPES,
  DEFAULT_SERVICE_ADVISOR_TYPES,
  ROUND_UPTO_OPTIONS,
  SECTIONAL_HASH_ID,
  ADDITIONAL_JOB_ADDITION_PAYTYPE,
} from 'tbase/constants/roSettings.constants';
import { OPCODE_ASYNC_SELECT_PROPS } from 'tbusiness/appServices/service/constants/opcode';
import { RESOURCE_TYPE } from 'tbase/bulkResolvers';
import selectStyles from 'tcomponents/molecules/Select/select.module.scss';

import SwitchWithLabel from '../Components/SwitchWithLabel/SwitchWithLabel';
import InfoWithMessage from '../Components/InfoWithMessage';
import SwitchWithModal from '../Components/SwitchWithModal/SwitchWithModal';
import { DATE_CONFIGURATION_COLUMN } from '../Components/DateConfigurationColumns';
import {
  getApplicableFormSections,
  getFlagHoursOnFilteredOptions,
  rowCheck,
  getApproversCommonRenderOptions,
} from '../roSettings.helper';
import {
  ESTIMATE_DISPLAY_SETTINGS_CONTROL_FIELDS_VS_LABEL,
  JOB_REVIEWER_DROPDOWN_OPTIONS,
  MARK_POLICY_NUMBER_AND_CLAIM_NUMBER_FIELDS_AS_MANDATORY_VS_LABELS,
} from '../roSettings.constants';

import styles from '../roSettings.module.scss';
import ReviewerWorkflowModal from '../Components/ReviewerWorkflowModal/ReviewerWorkflowModal';

const getMandatorySignInvoiceFields = allowDigitalSignInvoice =>
  allowDigitalSignInvoice ? [{ columns: [FIELD_IDS.CONSUMER_PORTAL_SIGN_MANDATORY] }] : EMPTY_ARRAY;

const getInvoiceDigitalSignFieldRows = (isDseStandalone, allowDigitalSignInvoice) => {
  if (!ServiceEnv.dealerProperty[DEALER_PROPERTY.CONSUMER_PORTAL_ENABLED] || isDseStandalone) return EMPTY_ARRAY;
  return [
    {
      columns: [FIELD_IDS.ALLOW_INVOICE_DIGITAL_SIGN],
    },
    {
      columns: [FIELD_IDS.ALLOW_INVOICE_DIGITAL_SIGN_INFO],
    },
    ...getMandatorySignInvoiceFields(allowDigitalSignInvoice),
  ];
};

const getMultipleClockIns = allowMultipleClockIns =>
  allowMultipleClockIns
    ? [
        { className: styles.subRows, columns: [FIELD_IDS.MULTIPLE_RO_CUST_PAY] },
        { className: styles.subRows, columns: [FIELD_IDS.MULTIPLE_RO_INT_PAY] },
        { className: styles.subRows, columns: [FIELD_IDS.MULTIPLE_RO_WAR_PAY] },
        { className: styles.subRows, columns: [FIELD_IDS.MULTIPLE_RO_WP_IP] },
        { className: styles.subRows, columns: [FIELD_IDS.MULTIPLE_RO_CP_WP] },
        { className: styles.subRows, columns: [FIELD_IDS.MULTIPLE_RO_CP_IP] },
        { className: styles.subRows, columns: [FIELD_IDS.MULTIPLE_RO_CP_IP_WP] },
      ]
    : EMPTY_ARRAY;

const getOBDIntegrationSection = () =>
  isLaneInspectionEnabled()
    ? [
        {
          columns: [FIELD_IDS.ENABLE_OBD_INTEGRATION],
        },
      ]
    : EMPTY_ARRAY;

const getEstimateDisplaySettingsForCard = () =>
  Constraints.isServiceV3Enabled()
    ? [
        {
          columns: [FIELD_IDS.DISPLAY_ESTIMATE_AMOUNT_ON_RO_JOB_CARD],
        },
        // to be enabled in phase 2
        // {
        //   columns: [FIELD_IDS.DISPLAY_ESTIMATE_AMOUNT_ON_QUOTE_JOB_CARD],
        // },
        // {
        //   columns: [FIELD_IDS.DISPLAY_ESTIMATE_AMOUNT_ON_APPOINTMENT_JOB_CARD],
        // },
        {
          columns: [FIELD_IDS.DISPLAY_ESTIMATE_AMOUNT_ON_CHECK_IN_JOB_CARD],
        },
        {
          columns: [FIELD_IDS.DISPLAY_ESTIMATE_AMOUNT_ON_RECOMMENDATION_JOB_CARD],
        },
      ]
    : EMPTY_ARRAY;

const getMarkPolicyAndClaimNoMandatoryFields = () =>
  Constraints.isExcessPayerEnabled()
    ? [
        {
          columns: [FIELD_IDS.MARK_POLICY_AND_CLAIM_NUMBER_MANDATORY],
        },
      ]
    : EMPTY_ARRAY;

const getMultipleServiceAdvisorSection = () =>
  Constraints.isMultipleServiceAdvisorsInROEnabled()
    ? [
        {
          columns: [FIELD_IDS.ENABLE_DEFAULT_SERVICE_ADVISOR],
        },
      ]
    : EMPTY_ARRAY;

const getDateConfiguratorSection = () =>
  Constraints.isDateConfiguratorEnabled()
    ? [
        {
          columns: [FIELD_IDS.DATE_CONFIGURATION_HEADER, FIELD_IDS.DATE_CONFIGURATION_HEADER_INFO],
          className: 'flex align-items-center m-t-12',
        },
        {
          columns: [FIELD_IDS.DATE_CONFIGURATION_TABLE],
        },
        {
          columns: [
            FIELD_IDS.DATE_CONFIGURATION_SERVICE_INTERVAL_HEADING,
            FIELD_IDS.DATE_CONFIGURATION_SERVICE_INTERVAL_MILAGE,
            FIELD_IDS.DATE_CONFIGURATION_SERVICE_INTERVAL_DURATION,
          ],
        },
      ]
    : EMPTY_ARRAY;

const getPaintOpcodeSkillsRow = () =>
  Constraints.isVehiclePaintQualityValidationEnabled()
    ? [
        {
          columns: [FIELD_IDS.PAINT_OPCODE_SKILLS],
        },
      ]
    : EMPTY_ARRAY;

const getJobReviewerWorkflowRow = () => {
  if (!Constraints.isReviewerWorkflowEnabled()) return EMPTY_ARRAY;
  return [{ columns: [FIELD_IDS.REVIEWER_WORKFLOW_ENABLED] }];
};

export const getGeneralSetupFieldConfig = (
  skills,
  dateConfigFilterTypes,
  getMeasureUnitLabel,
  dateFields,
  isDisabled,
  updateFlagHoursOn,
  reviewerWorkflowEnabled
) => ({
  [FIELD_IDS.JOB_SEQUENCE]: {
    renderer: RadioGroupInput,
    renderOptions: {
      label: __('Job Sequence'),
      radios: JOB_SEQUENCE_TYPES,
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.RECOMMENDATION_OPCDE]: {
    renderer: AsyncSelectInput,
    renderOptions: {
      ...OPCODE_ASYNC_SELECT_PROPS,
      label: __('Recommendation Opcode'),
      validators: [isRequiredRule],
      filters: [...DEFAULT_SEARCH_FILTER, ...OPCODE_SOURCE_DEFAULT_FILTER, ...OPCODE_TYPE_DEFAULT_FILTER],
      sort: getOpCodeSearchDefaultSort(),
      size: 6,
      isDisabled,
      ...getBatchedLookupApiParams(RESOURCE_TYPE.OPCODE),
    },
  },
  [FIELD_IDS.FLAG_TECH_ON]: {
    renderer: Select,
    accessor: FIELD_IDS.FLAG_TECH_ON,
    renderOptions: {
      label: __('Flag Tech on'),
      options: getFlagHoursOnFilteredOptions({ updateFlagHoursOn, reviewerWorkflowEnabled }),
      size: 6,
      disabled: isDisabled,
    },
  },

  [FIELD_IDS.AUTO_ADJUST_LABOUR_HOURS_ON_MANUAL_FLAGGING]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.AUTO_ADJUST_LABOUR_HOURS_ON_MANUAL_FLAGGING,
    renderOptions: {
      label: __('Auto adjust assigned labour hours on manual flagging'),
    },
  },

  [FIELD_IDS.PAINT_OPCODE_SKILLS]: {
    renderer: VirtualizedSelectInput,
    accessor: FIELD_IDS.PAINT_OPCODE_SKILLS,
    renderOptions: {
      label: __('Skills for Paint Quality Validation'),
      options: getSkillsOptions(skills),
      size: 6,
      disabled: isDisabled,
    },
  },

  [FIELD_IDS.DISPLAY_AMOUNT_ON_ESTIMATE]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.DISPLAY_AMOUNT_ON_ESTIMATE,
    renderOptions: {
      label: __('Display Amount On $$(Estimate)'),
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.INCLUDE_FEES_IN_ESTIMATE]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.INCLUDE_FEES_IN_ESTIMATE,
    renderOptions: {
      label: __('Include $$(Fees) in $$(Estimates)'),
      validators: [],
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.INCLUDE_TAXES_IN_ESTIMATE]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.INCLUDE_TAXES_IN_ESTIMATE,
    renderOptions: {
      label: __('Include Taxes in $$(Estimates)'),
      validators: [],
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.INCLUDE_COUPONS_IN_ESTIMATE]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.INCLUDE_COUPONS_IN_ESTIMATE,
    renderOptions: {
      label: __('Include Coupons in $$(Estimates)'),
      validators: [],
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.CLOSE_RO_IN_DETAILS_VIEW]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.CLOSE_RO_IN_DETAILS_VIEW,
    renderOptions: {
      label: __('Allow to close $$(RO) in details view'),
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.ALLOW_INVOICE_DIGITAL_SIGN]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.ALLOW_INVOICE_DIGITAL_SIGN,
    renderOptions: {
      label: __('Allow customers to sign invoices online'),
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.CONSUMER_PORTAL_SIGN_MANDATORY]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.CONSUMER_PORTAL_SIGN_MANDATORY,
    renderOptions: {
      label: __('Make online signature mandatory'),
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.ALLOW_INVOICE_DIGITAL_SIGN_INFO]: {
    renderer: InfoWithMessage,
    renderOptions: {
      message: __('Please check your local laws and ensure that you are compliant with your OEM'),
    },
  },
  [FIELD_IDS.SHOW_JOURNAL_ENTRY_ERROR_ON_RO_CLOSE]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.SHOW_JOURNAL_ENTRY_ERROR_ON_RO_CLOSE,
    renderOptions: {
      label: __('Display Journal Entry Error on $$(RO) Close'),
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.SHOW_OTHER_DEALERSHIP_SERVICE_HISTORY]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.SHOW_OTHER_DEALERSHIP_SERVICE_HISTORY,
    renderOptions: {
      label: __('Show Service History of Other Dealerships in Vehicle Profile'),
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.ALLOW_ACTUAL_HOUR_ROUND_OFF]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.ALLOW_ACTUAL_HOUR_ROUND_OFF,
    renderOptions: {
      label: __('Round Up Technician Actual Hours to nearest tenth of an hour'),
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.ACTUAL_HOUR_ROUND_OFF]: {
    renderer: Select,
    accessor: FIELD_IDS.ACTUAL_HOUR_ROUND_OFF,
    renderOptions: {
      label: __('If second decimal value is greater than'),
      options: ROUND_UPTO_OPTIONS,
      size: 3,
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.ALLOW_LABOR_AND_BILL_HOUR_ROUND_OFF]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.ALLOW_LABOR_AND_BILL_HOUR_ROUND_OFF,
    renderOptions: {
      label: __('Round up Labor/Bill Hours to nearest tenth of an hour'),
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.LABOR_AND_BILL_HOUR_ROUNDOFF]: {
    renderer: Select,
    accessor: FIELD_IDS.LABOR_AND_BILL_HOUR_ROUNDOFF,
    renderOptions: {
      label: __('If second decimal value is greater than'),
      options: ROUND_UPTO_OPTIONS,
      size: 3,
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.ALLOW_FLAG_HOUR_ROUND_OFF]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.ALLOW_FLAG_HOUR_ROUND_OFF,
    renderOptions: {
      label: __('Round up Flag Hours to nearest tenth of an hour'),
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.FLAG_HOUR_ROUND_OFF]: {
    renderer: Select,
    accessor: FIELD_IDS.FLAG_HOUR_ROUND_OFF,
    renderOptions: {
      label: __('If second decimal value is greater than'),
      options: ROUND_UPTO_OPTIONS,
      size: 3,
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.ALLOW_MULTIPLE_CLOCKINS]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.ALLOW_MULTIPLE_CLOCKINS,
    renderOptions: {
      label: __('Allow technicians to clock-in to multiple $$(ROs)/Jobs at the same time'),
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.MULTIPLE_RO_CUST_PAY]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.MULTIPLE_RO_CUST_PAY,
    renderOptions: {
      label: __('For Customer Pay Jobs'),
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.MULTIPLE_RO_INT_PAY]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.MULTIPLE_RO_INT_PAY,
    renderOptions: {
      label: __('For Internal Pay Jobs'),
      type: 'switch',
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.MULTIPLE_RO_WAR_PAY]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.MULTIPLE_RO_WAR_PAY,
    renderOptions: {
      label: __('For Warranty Pay Jobs'),
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.MULTIPLE_RO_WP_IP]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.MULTIPLE_RO_WP_IP,
    renderOptions: {
      label: __('For Warranty - Internal Jobs'),
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.MULTIPLE_RO_CP_WP]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.MULTIPLE_RO_CP_WP,
    renderOptions: {
      label: __('For Warranty - Customer Jobs'),
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.MULTIPLE_RO_CP_IP]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.MULTIPLE_RO_CP_IP,
    renderOptions: {
      label: __('For Customer - Internal Jobs'),
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.MULTIPLE_RO_CP_IP_WP]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.MULTIPLE_RO_CP_IP_WP,
    renderOptions: {
      label: __('For Customer - Warranty - Internal Jobs'),
      disabled: isDisabled,
    },
  },

  [FIELD_IDS.DUPLICATE_TAG_VALIDATION]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.DUPLICATE_TAG_VALIDATION,
    renderOptions: {
      label: __('Restrict entry of duplicate Tag#'),
      disabled: isDisabled,
    },
  },

  [FIELD_IDS.ENABLE_ONSTAR_INTEGRATION]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.ENABLE_ONSTAR_INTEGRATION,
    renderOptions: {
      label: __('Enable Onstar Integration'),
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.ENABLE_OBD_INTEGRATION]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.ENABLE_OBD_INTEGRATION,
    renderOptions: {
      label: __('Enable OBD Integration'),
      disabled: isDisabled,
    },
  },

  [FIELD_IDS.ENABLE_DEFAULT_SERVICE_ADVISOR]: {
    renderer: RadioGroupInput,
    accessor: FIELD_IDS.ENABLE_DEFAULT_SERVICE_ADVISOR,
    renderOptions: {
      label: __('Select Default $$(Service Advisor) for jobs'),
      radios: DEFAULT_SERVICE_ADVISOR_TYPES,
      disabled: isDisabled,
    },
  },

  [FIELD_IDS.ALLOW_CUSTOM_CONCERN_OPCODE_CREATION]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.ALLOW_CUSTOM_CONCERN_OPCODE_CREATION,
    renderOptions: {
      label: __('Allow Creation of Custom Concern Opcode'),
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.ALLOW_OPCODE_TO_SHOW_IN_JOB_SUMMARY]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.ALLOW_OPCODE_TO_SHOW_IN_JOB_SUMMARY,
    renderOptions: {
      label: __('Show opcodes in job/service list view in Quote, $$(Appointment), $$(RO) etc'),
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.HIDE_FEE_COST_AMOUNT]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.HIDE_FEE_COST_AMOUNT,
    renderOptions: {
      label: __('Hide Fee Cost Amount'),
      disabled: isDisabled,
    },
  },

  [FIELD_IDS.DATE_CONFIGURATION_HEADER]: {
    renderer: Heading,
    renderOptions: {
      children: __('Date Values and Updation rules'),
      className: 'm-b-4 m-r-16',
      size: 5,
    },
  },
  [FIELD_IDS.DATE_CONFIGURATION_HEADER_INFO]: {
    renderer: InfoWithMessage,
    renderOptions: {
      message: __('Following Updation Values and Rules apply only at $$(RO) closure'),
      className: 'm-b-4',
    },
  },
  [FIELD_IDS.DATE_CONFIGURATION_TABLE]: {
    id: FIELD_IDS.DATE_CONFIGURATION_TABLE,
    renderer: TableInputField,
    renderOptions: {
      shouldAddNewRow: _stubFalse,
      columns: DATE_CONFIGURATION_COLUMN,
      additional: { dateFields, filterTypes: dateConfigFilterTypes, isDisabled },
    },
  },
  [FIELD_IDS.DATE_CONFIGURATION_SERVICE_INTERVAL_HEADING]: {
    renderer: Content,
    renderOptions: {
      children: __('Service Interval'),
      className: 'm-r-12',
      size: 2,
    },
  },
  [FIELD_IDS.DATE_CONFIGURATION_SERVICE_INTERVAL_MILAGE]: {
    renderer: NumberInput,
    renderOptions: {
      addonAfter: getMeasureUnitLabel({
        conversionMeasureId: CONVERSION_IDS.DISTANCE,
        unitLevel: CONVERSION_LEVEL.FOUR,
        labelType: UNIT_LABEL_TYPE.PLURAL,
      }),
      fieldClassName: styles.dateConfiguratorNumberField,
      label: __('Mileage'),
      size: 2,
      precision: 0,
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.DATE_CONFIGURATION_SERVICE_INTERVAL_DURATION]: {
    renderer: NumberInput,
    renderOptions: {
      addonAfter: __('Months'),
      label: __('Duration'),
      fieldClassName: styles.dateConfiguratorNumberField,
      size: 2,
      precision: 0,
      disabled: isDisabled,
    },
  },

  [FIELD_IDS.DISPLAY_ESTIMATE_AMOUNT_ON_RO_JOB_CARD]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.DISPLAY_ESTIMATE_AMOUNT_ON_RO_JOB_CARD,
    renderOptions: {
      label: ESTIMATE_DISPLAY_SETTINGS_CONTROL_FIELDS_VS_LABEL[FIELD_IDS.DISPLAY_ESTIMATE_AMOUNT_ON_RO_JOB_CARD],
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.DISPLAY_ESTIMATE_AMOUNT_ON_QUOTE_JOB_CARD]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.DISPLAY_ESTIMATE_AMOUNT_ON_QUOTE_JOB_CARD,
    renderOptions: {
      label: ESTIMATE_DISPLAY_SETTINGS_CONTROL_FIELDS_VS_LABEL[FIELD_IDS.DISPLAY_ESTIMATE_AMOUNT_ON_QUOTE_JOB_CARD],
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.DISPLAY_ESTIMATE_AMOUNT_ON_APPOINTMENT_JOB_CARD]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.DISPLAY_ESTIMATE_AMOUNT_ON_APPOINTMENT_JOB_CARD,
    renderOptions: {
      label:
        ESTIMATE_DISPLAY_SETTINGS_CONTROL_FIELDS_VS_LABEL[FIELD_IDS.DISPLAY_ESTIMATE_AMOUNT_ON_APPOINTMENT_JOB_CARD],
      disabled: isDisabled,
    },
  },

  /// //here
  [FIELD_IDS.MARK_POLICY_NUMBER_AND_CLAIM_NUMBER_FIELDS_AS_MANDATORY]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.MARK_POLICY_NUMBER_AND_CLAIM_NUMBER_FIELDS_AS_MANDATORY,
    renderOptions: {
      label:
        MARK_POLICY_NUMBER_AND_CLAIM_NUMBER_FIELDS_AS_MANDATORY_VS_LABELS[
          FIELD_IDS.MARK_POLICY_NUMBER_AND_CLAIM_NUMBER_FIELDS_AS_MANDATORY
        ],
      disabled: isDisabled,
    },
  },

  [FIELD_IDS.MARK_POLICY_AND_CLAIM_NUMBER_MANDATORY]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.MARK_POLICY_AND_CLAIM_NUMBER_MANDATORY,
    renderOptions: {
      label: __('Mark Policy Number and Claim Number fields as mandatory'),
      disabled: isDisabled,
    },
  },

  [FIELD_IDS.NOTIFY_CUSTOMER_ON_INVOICE]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.NOTIFY_CUSTOMER_ON_INVOICE,
    renderOptions: {
      label: __('Notify customer on invoice'),
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.REVIEWER_WORKFLOW_ENABLED]: {
    renderer: SwitchWithModal,
    accessor: FIELD_IDS.REVIEWER_WORKFLOW_ENABLED,
    renderOptions: {
      switchWithLabelProps: {
        label: __('Enable $$(Booker) workflow'),
        disabled: isDisabled,
      },
      modalProps: {
        title: __('Enable $$(Booker) Workflow'),
        submitBtnText: __('Accept and Enable'),
        width: '88rem',
      },
      modalContentRenderer: ReviewerWorkflowModal,
    },
  },
  [FIELD_IDS.JOB_REVIEWER]: {
    renderer: Select,
    accessor: FIELD_IDS.JOB_REVIEWER,
    renderOptions: {
      label: __('Default $$(Booker)'),
      options: JOB_REVIEWER_DROPDOWN_OPTIONS,
      size: 6,
      disabled: isDisabled,
      required: true,
      validators: [isRequiredRule],
    },
  },
  [FIELD_IDS.ENABLE_RO_APPROVAL]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.ENABLE_RO_APPROVAL,
    renderOptions: {
      label: __('Enable $$(RO) Approval flow'),
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.LABOR_HOUR_RULES_HEADER]: {
    renderer: Heading,
    renderOptions: {
      size: 5,
      className: cx(styles.subHeader, 'p-t-8'),
      children: __('Labor Hour Rules'),
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.ADDITIONAL_LABOR_HOUR_NEED_APPROVAL]: {
    renderer: SwitchWithLabel,
    accessor: FIELD_IDS.ADDITIONAL_LABOR_HOUR_NEED_APPROVAL,
    renderOptions: {
      label: __('Additional Labor hour changes will need approval'),
      disabled: true,
    },
  },
  [FIELD_IDS.LABOR_HOUR_APPROVERS]: {
    renderer: VirtualizedAsyncMultiSelectFieldRenderer,
    renderOptions: {
      ...getApproversCommonRenderOptions(),
      dropDownClassName: cx(selectStyles.selectBoxBorder, styles.halfWidth),
      isDisabled,
      label: __('Add Approvers for Labor Hour and Paytype'),
    },
  },
  [FIELD_IDS.PAYTYPE_RULES_HEADER]: {
    renderer: Heading,
    renderOptions: {
      size: 5,
      className: cx(styles.subHeader, 'p-t-4 p-b-8'),
      children: __('Paytype Rules'),
      disabled: isDisabled,
    },
  },
  [FIELD_IDS.ADDITIONAL_JOB_ADDITION_ALLOWED_PAY_TYPES]: {
    renderer: MultiSelectField,
    renderOptions: {
      label: __('Additional Jobs can be added for the following Paytypes'),
      dropDownClassName: cx(selectStyles.selectBoxBorder, styles.halfWidth),
      options: ADDITIONAL_JOB_ADDITION_PAYTYPE,
      fieldClassName: styles.boldFieldLabel,
      disabled: isDisabled,
    },
  },
});

export const getGeneralSetupSectionConfig = (
  isDseStandalone,
  allowDigitalSignInvoice,
  allowActualHourRoundup,
  allowLaborAndBillHourRoundOff,
  allowFlagHourRoundOff,
  allowMultipleClockIns,
  reviewerWorkflowEnabled,
  roApprovalFlowEnabled
) => [
  {
    header: {
      label: __('General Setup'),
      className: styles.roSettingCardHeader,
      hashId: SECTIONAL_HASH_ID.GENERAL_SETUP,
    },
    className: cx(styles.roSettingCard, styles.roSettingFirstCard),
    rows: [
      {
        columns: [FIELD_IDS.JOB_SEQUENCE],
      },
      {
        columns: [FIELD_IDS.RECOMMENDATION_OPCDE],
      },
      {
        columns: [FIELD_IDS.FLAG_TECH_ON],
      },
      {
        columns: [FIELD_IDS.AUTO_ADJUST_LABOUR_HOURS_ON_MANUAL_FLAGGING],
        className: 'p-b-8',
      },
      ...getPaintOpcodeSkillsRow(),
      {
        columns: [FIELD_IDS.DISPLAY_AMOUNT_ON_ESTIMATE],
      },
      {
        columns: [FIELD_IDS.INCLUDE_FEES_IN_ESTIMATE],
      },
      {
        columns: [FIELD_IDS.INCLUDE_TAXES_IN_ESTIMATE],
      },
      {
        columns: [FIELD_IDS.INCLUDE_COUPONS_IN_ESTIMATE],
      },
      {
        columns: [FIELD_IDS.CLOSE_RO_IN_DETAILS_VIEW],
      },
      ...getInvoiceDigitalSignFieldRows(isDseStandalone, allowDigitalSignInvoice),
      {
        columns: [FIELD_IDS.SHOW_JOURNAL_ENTRY_ERROR_ON_RO_CLOSE],
      },
      {
        columns: [FIELD_IDS.SHOW_OTHER_DEALERSHIP_SERVICE_HISTORY],
      },
      {
        columns: [FIELD_IDS.NOTIFY_CUSTOMER_ON_INVOICE],
      },
      {
        columns: [FIELD_IDS.ALLOW_ACTUAL_HOUR_ROUND_OFF],
      },
      ...getApplicableFormSections(allowActualHourRoundup, FIELD_IDS.ACTUAL_HOUR_ROUND_OFF, styles.subRows),

      {
        columns: [FIELD_IDS.ALLOW_LABOR_AND_BILL_HOUR_ROUND_OFF],
      },
      ...getApplicableFormSections(
        allowLaborAndBillHourRoundOff,
        FIELD_IDS.LABOR_AND_BILL_HOUR_ROUNDOFF,
        styles.subRows
      ),
      {
        columns: [FIELD_IDS.ALLOW_FLAG_HOUR_ROUND_OFF],
      },
      ...getApplicableFormSections(allowFlagHourRoundOff, FIELD_IDS.FLAG_HOUR_ROUND_OFF, styles.subRows),
      {
        columns: [FIELD_IDS.ALLOW_MULTIPLE_CLOCKINS],
      },
      ...getMultipleClockIns(allowMultipleClockIns),
      {
        columns: [FIELD_IDS.DUPLICATE_TAG_VALIDATION],
      },
      ...(_includes(getSupportedOems(), _toLower(OEM.GM))
        ? [
            {
              columns: [FIELD_IDS.ENABLE_ONSTAR_INTEGRATION],
            },
          ]
        : EMPTY_ARRAY),
      ...getOBDIntegrationSection(),
      ...getMultipleServiceAdvisorSection(),
      {
        columns: [FIELD_IDS.ALLOW_CUSTOM_CONCERN_OPCODE_CREATION],
      },
      {
        columns: [FIELD_IDS.ALLOW_OPCODE_TO_SHOW_IN_JOB_SUMMARY],
      },
      {
        columns: [FIELD_IDS.HIDE_FEE_COST_AMOUNT],
      },
      ...getDateConfiguratorSection(),
      ...getEstimateDisplaySettingsForCard(),
      ...getMarkPolicyAndClaimNoMandatoryFields(),
      ...getJobReviewerWorkflowRow(),
      ...getApplicableFormSections(
        reviewerWorkflowEnabled && Constraints.isReviewerWorkflowEnabled(),
        FIELD_IDS.JOB_REVIEWER,
        styles.subRows
      ),
      ...(Constraints.isServiceApprovalFlowV2Enabled()
        ? [
            {
              columns: [FIELD_IDS.ENABLE_RO_APPROVAL],
            },
            ...(roApprovalFlowEnabled
              ? [
                  {
                    columns: [FIELD_IDS.PAYTYPE_RULES_HEADER],
                  },
                  {
                    columns: [FIELD_IDS.ADDITIONAL_JOB_ADDITION_ALLOWED_PAY_TYPES],
                  },
                  {
                    columns: [FIELD_IDS.LABOR_HOUR_RULES_HEADER],
                  },
                  {
                    columns: [FIELD_IDS.ADDITIONAL_LABOR_HOUR_NEED_APPROVAL],
                  },
                  {
                    columns: [FIELD_IDS.LABOR_HOUR_APPROVERS],
                  },
                ]
              : EMPTY_ARRAY),
          ]
        : EMPTY_ARRAY),
    ],
  },
];
