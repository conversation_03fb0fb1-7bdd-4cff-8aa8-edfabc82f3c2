import _stubTrue from 'lodash/stubTrue';

import Constraints from 'helpers/constraints';
import ServiceEnv from 'utils/serviceEnv';

import {
  SECTIONAL_HASH_ID,
  FIELD_IDS,
  ARCHIVE_ACTION_ID,
  UNARCHIVE_ACTION_ID,
  RETURN_RO_TRIGGER_TYPES,
} from 'tbase/constants/roSettings.constants';
import DEALER_PROPERTIES from 'tbase/constants/dealerProperties';
import { EMPTY_OBJECT, EMPTY_STRING, EMPTY_ARRAY } from 'tbase/app.constants';
import {
  FILTER_STATUS_KEY,
  FILTER_PAY_TYPES,
  DEPARTMENT_ID,
  FILTER_JOB_TYPES,
  FILTER_JOB_STATUSES_KEY,
  RO_FLAG_ID,
  JOB_PART_STATUS,
  RO_TYPE,
  SUBLET_JOB,
  JOB_TAG_ID,
} from 'twidgets/appServices/service/constants/ROFilter.constants';
import { RO_ENTITY_TYPES } from '@tekion/tekion-business/src/appServices/service/constants/general';
import { ROUTE_CONFIG } from 'tbusiness/appServices/service/helpers/route';
import { TIME_UOM_DAYS } from 'tbase/constants/unitsOfMeasure';
import {
  TAG_LABELS,
  STANDARD_JOB_TAG_TYPES,
  STANDARD_RO_TAG_TYPES,
  STANDARD_RECOMMENDATION_TAG_TYPES,
} from 'tbusiness/appServices/service/constants/tags';
import { DUE_BILL, SUBLET, RECALLS, CUSTOM_CONCERNS_OPCODE } from 'tbusiness/readers/service/CheckInSettings';

export const PRE_INVOICE_COLUMN_IDS = {
  RULE: 'rule',
  MANDATORY: 'mandatory',
  CONDITION: 'condition',
  WARNING_OR_ERROR: 'warning_or_error',
  VALIDATION_MESSAGE: 'validation_message',
  POPUP_WARNING: 'pop_up_warning',
  APPLICABLE_JOB_TYPES: 'applicable_job_types',
  APPLICABLE_PAY_TYPES: 'applicable_pay_types',
  APPLICABLE_VEHICLE_TYPES: 'applicable_vehicle_types',
  RULE_LEVEL: 'rule_level',
};

export const PRE_INVOICE_COLUMN_VS_WIDTH = {
  [PRE_INVOICE_COLUMN_IDS.RULE]: 225,
  [PRE_INVOICE_COLUMN_IDS.MANDATORY]: 101,
  [PRE_INVOICE_COLUMN_IDS.CONDITION]: 238,
  [PRE_INVOICE_COLUMN_IDS.WARNING_OR_ERROR]: 101,
  [PRE_INVOICE_COLUMN_IDS.VALIDATION_MESSAGE]: 340,
  [PRE_INVOICE_COLUMN_IDS.POPUP_WARNING]: 85,
  [PRE_INVOICE_COLUMN_IDS.APPLICABLE_JOB_TYPES]: 200,
  [PRE_INVOICE_COLUMN_IDS.APPLICABLE_PAY_TYPES]: 200,
  [PRE_INVOICE_COLUMN_IDS.APPLICABLE_VEHICLE_TYPES]: 200,
  [PRE_INVOICE_COLUMN_IDS.RULE_LEVEL]: 100,
};

export const TAB_SECTION_VS_VISIBILITY = {
  [SECTIONAL_HASH_ID.QUOTES]: () => Constraints.isQuoteServiceEnabled(),
  [SECTIONAL_HASH_ID.EXPRESS_MODE_SETUP]: () => Constraints.isExpressModeEnabled(),
  [SECTIONAL_HASH_ID.JOB_ADDITION_RULES]: () => Constraints.isExpressModeEnabled(),
  [SECTIONAL_HASH_ID.CONSUMER_PORTAL]: () => ServiceEnv.dealerProperty[DEALER_PROPERTIES.CONSUMER_PORTAL_ENABLED],
  [SECTIONAL_HASH_ID.APPROVAL_SETTINGS]: () =>
    !Constraints.isServiceApprovalFlowV2Enabled() && Constraints.isROApprovalSystemEnabled(),
  [SECTIONAL_HASH_ID.PROFIT_LOSS]: () => Constraints.isProfitLossViewEnabled(),
  [SECTIONAL_HASH_ID.RO_TAGS]: () => !Constraints.isROJobTagsV2Enabled(),
  [SECTIONAL_HASH_ID.ARCHIVED_TAGS]: () => !Constraints.isROJobTagsV2Enabled(),
  [SECTIONAL_HASH_ID.JOB_TAGS]: () => !Constraints.isROJobTagsV2Enabled(),
  [SECTIONAL_HASH_ID.TAGS]: () => Constraints.isROJobTagsV2Enabled(),
  [SECTIONAL_HASH_ID.TIRE_FLAG]: () => Constraints.isTireStorageEnabled(),
  [SECTIONAL_HASH_ID.RECOMMENDATION_TAGS]: () => !Constraints.isROJobTagsV2Enabled(),
  [SECTIONAL_HASH_ID.MACHINE_LEARNING]: () => Constraints.isTekionSmartSuggestPartsEnabled(),
  [SECTIONAL_HASH_ID.OPCODE_PRICING]: () => Constraints.isServiceV3Enabled(),
  [SECTIONAL_HASH_ID.SERVICE_RESTRICTED_SETTINGS]: () => Constraints.isServiceV3Enabled(),
  [SECTIONAL_HASH_ID.INSURANCE_INFORMATION]: () => !Constraints.isServiceV3Enabled(),
  [SECTIONAL_HASH_ID.CREDIT_NOTE_SETUP]: () => Constraints.isServiceV3Enabled(),
  [SECTIONAL_HASH_ID.PRE_TECH_FINISHED]: () => Constraints.isReviewerWorkflowEnabled(),
  DEFAULT: _stubTrue,
};

export const ARCHIVE_ACTION = {
  id: ARCHIVE_ACTION_ID,
  name: __('Archive'),
  icon: 'icon-archive',
};

export const ARCHIVE_ACTION_WITH_DELETE_ICON = {
  id: ARCHIVE_ACTION_ID,
  name: __('Archive'),
  icon: 'icon-trash',
};

export const UNARCHIVE_ACTION = {
  id: UNARCHIVE_ACTION_ID,
  name: __('Unarchive'),
  icon: 'icon-restore-from-trash',
};

export const INITIAL_STATE = {
  multiFactorWarningModalInfo: EMPTY_OBJECT,
};

export const JOB_TAG_TYPE_OPTIONS = [
  { label: TAG_LABELS[STANDARD_JOB_TAG_TYPES.PDI], value: STANDARD_JOB_TAG_TYPES.PDI },
  { label: TAG_LABELS[STANDARD_JOB_TAG_TYPES.HOLD], value: STANDARD_JOB_TAG_TYPES.HOLD },
  { label: TAG_LABELS[STANDARD_JOB_TAG_TYPES.SERVICE_MENU], value: STANDARD_JOB_TAG_TYPES.SERVICE_MENU },
  { label: TAG_LABELS[STANDARD_JOB_TAG_TYPES.SUBLET], value: STANDARD_JOB_TAG_TYPES.SUBLET },
  { label: TAG_LABELS[STANDARD_JOB_TAG_TYPES.ADD_ON_REPAIR], value: STANDARD_JOB_TAG_TYPES.ADD_ON_REPAIR },
  { label: TAG_LABELS[STANDARD_JOB_TAG_TYPES.DUE_BILL], value: STANDARD_JOB_TAG_TYPES.DUE_BILL },
  { label: TAG_LABELS[STANDARD_JOB_TAG_TYPES.RECALL], value: STANDARD_JOB_TAG_TYPES.RECALL },
  { label: TAG_LABELS[STANDARD_JOB_TAG_TYPES.RECOMMENDATION], value: STANDARD_JOB_TAG_TYPES.RECOMMENDATION },
  {
    label: TAG_LABELS[STANDARD_JOB_TAG_TYPES.DEFERRED_RECOMMENDATION],
    value: STANDARD_JOB_TAG_TYPES.DEFERRED_RECOMMENDATION,
  },
  { label: TAG_LABELS[STANDARD_JOB_TAG_TYPES.CUSTOMER_PAY_SPLIT], value: STANDARD_JOB_TAG_TYPES.CUSTOMER_PAY_SPLIT },
  { label: TAG_LABELS[STANDARD_JOB_TAG_TYPES.INTERNAL_SPLIT], value: STANDARD_JOB_TAG_TYPES.INTERNAL_SPLIT },
  { label: TAG_LABELS[STANDARD_JOB_TAG_TYPES.WARRANTY_SPLIT], value: STANDARD_JOB_TAG_TYPES.WARRANTY_SPLIT },
  { label: TAG_LABELS[STANDARD_JOB_TAG_TYPES.MANUALLY_ADDED], value: STANDARD_JOB_TAG_TYPES.MANUALLY_ADDED },
  { label: TAG_LABELS[STANDARD_JOB_TAG_TYPES.ADJUSTED_POST_CLOSE], value: STANDARD_JOB_TAG_TYPES.ADJUSTED_POST_CLOSE },
  { label: TAG_LABELS[STANDARD_JOB_TAG_TYPES.UVI], value: STANDARD_JOB_TAG_TYPES.UVI },
  { label: TAG_LABELS[STANDARD_JOB_TAG_TYPES.MPVI], value: STANDARD_JOB_TAG_TYPES.MPVI },
  { label: TAG_LABELS[STANDARD_JOB_TAG_TYPES.COMEBACK], value: STANDARD_JOB_TAG_TYPES.COMEBACK },
  { label: TAG_LABELS[STANDARD_JOB_TAG_TYPES.MOBILE], value: STANDARD_JOB_TAG_TYPES.MOBILE },
  { label: TAG_LABELS[STANDARD_JOB_TAG_TYPES.EXPRESS], value: STANDARD_JOB_TAG_TYPES.EXPRESS },
];

export const RO_TAG_TYPE_OPTIONS = [
  { label: TAG_LABELS[STANDARD_RO_TAG_TYPES.MAIN_SHOP], value: STANDARD_RO_TAG_TYPES.MAIN_SHOP },
  { label: TAG_LABELS[STANDARD_RO_TAG_TYPES.BODY_SHOP], value: STANDARD_RO_TAG_TYPES.BODY_SHOP },
  { label: TAG_LABELS[STANDARD_RO_TAG_TYPES.MOBILE_SHOP], value: STANDARD_RO_TAG_TYPES.MOBILE_SHOP },
  { label: TAG_LABELS[STANDARD_RO_TAG_TYPES.EXPRESS_SHOP], value: STANDARD_RO_TAG_TYPES.EXPRESS_SHOP },
  { label: TAG_LABELS[STANDARD_RO_TAG_TYPES.NEW_VEHICLE_SHOP], value: STANDARD_RO_TAG_TYPES.NEW_VEHICLE_SHOP },
  { label: TAG_LABELS[STANDARD_RO_TAG_TYPES.USED_VEHICLE_SHOP], value: STANDARD_RO_TAG_TYPES.USED_VEHICLE_SHOP },
];

export const RECOMMENDATION_TAG_TYPE_OPTIONS = [
  {
    label: TAG_LABELS[STANDARD_RECOMMENDATION_TAG_TYPES.MPI_RECOMMENDATION],
    value: STANDARD_RECOMMENDATION_TAG_TYPES.MPI_RECOMMENDATION,
  },
  {
    label: TAG_LABELS[STANDARD_RECOMMENDATION_TAG_TYPES.UVI_RECOMMENDATION],
    value: STANDARD_RECOMMENDATION_TAG_TYPES.UVI_RECOMMENDATION,
  },
  {
    label: TAG_LABELS[STANDARD_RECOMMENDATION_TAG_TYPES.PDI_RECOMMENDATION],
    value: STANDARD_RECOMMENDATION_TAG_TYPES.PDI_RECOMMENDATION,
  },
];

export const defaultActiveTagsFilter = [
  {
    type: EMPTY_STRING,
    operator: EMPTY_STRING,
    values: EMPTY_ARRAY,
  },
];

export const SIDEBAR_ACTION_TYPES = {
  MENU_ITEM_CLICK: 'MENU_ITEM_CLICK',
};

export const RO_SETTINGS_KEY = {
  SERVICE_SETTINGS: 'SERVICE_SETTINGS',
  TAX_CODES: 'TAX_CODES',
  PRODUCTIVITY_BONUS_SETTINGS: 'PRODUCTIVITY_BONUS_SETTINGS',
};

export const RO_SETTINGS_KEY_VS_TITLE = {
  [RO_SETTINGS_KEY.SERVICE_SETTINGS]: __('Service Settings'),
  [RO_SETTINGS_KEY.TAX_CODES]: __('Tax Codes Settings'),
  [RO_SETTINGS_KEY.PRODUCTIVITY_BONUS_SETTINGS]: __('Productivity Bonus Settings'),
};

export const QUANTITY_INPUT_FIELD_DEFAULT_VALUE = {
  unit: TIME_UOM_DAYS.value,
};

export const RO_SETTINGS_INITIAL_STATE = {
  selectedMenuKey: RO_SETTINGS_KEY.SERVICE_SETTINGS,
  taxCodes: EMPTY_OBJECT,
  isEditView: false,
  isDirty: false,
  isLoading: true,
  tagsFiltersConfig: EMPTY_OBJECT,
};

export const KPI_TOTAL_SALE_ROLES = ['Technician', 'ServiceAdvisor'];

export const PRE_INVOICE_RULES_OPTIONS_KEY = {
  VALUES: 'values',
  SELECTED: 'selected',
};

export const REQUEST_OPTIONS = 'requestOptions';

export const RULE_SETTINGS_KEY = {
  JOB_RECOMMENDATION_RULE: 'jobRecommendationRule',
  PRE_TECH_FINISHED: 'preJobTechFinishRule',
  PRE_JOB_COMPLETION: 'preJobCompletion',
  SERVICE_SETTINGS: 'serviceSettings',
  EXPRESS_MODE: 'expressMode',
};

export const AGGREGATED_RULE_SETTINGS_KEY = {
  FEATURE_ACCESS_CONFIG: 'featureAccessConfig',
  APPLICATION_CONFIG: 'applicationConfig',
  ALL_TAGS: 'allTags',
  RO_TAGS: 'roTags',
  RECOMMENDATION_TAGS: 'recommendationTags',
  SERVICE_SHOP: 'serviceShop',
  TIRE_STORAGE_SETTING: 'tireStorageSetting',
  TAGS_FILTER_CONFIG: 'tagsFilterConfig',
};

export const DEFERRED_RECOMMENDATION_RULE_FIELDS = {
  NAME: 'displayName',
  TIME_INTERVAL: 'timeInterval',
  ENABLED: 'enabled',
};

export const RULES_V2_KEY = 'rulesV2';

export const OPCODE_MAPPING_AND_SELECTION_FIELDS = {
  DUE_BILL_OPCODE_LABEL: 'dueBillOpcodeLabel',
  DUE_BILL_OPCODE: DUE_BILL,
  RECALLS_OPCODE_LABEL: 'recallOpcodeLabel',
  RECALLS_OPCODE: RECALLS,
  CUSTOM_CONCERNS_OPCODE_LABEL: 'customConcernOpcodeLabel',
  CUSTOM_CONCERNS_OPCODE,
  SUBLET_OPCODE_LABEL: 'subletOpcodeLabel',
  SUBLET_OPCODE: SUBLET,
  POPULAR_SERVICES_OPCODES: 'popularServices',
  POPULAR_SERVICES_OPCODES_LABEL: 'popularServicesLabel',
  LTG_OPCODE_LABEL: 'ltgOpcodeLabel',
  LTG_OPCODE: 'ltgOpcode',
  DEFAULT_ADJUSTMENT_HEADER: 'defaultAdjustmentHeader',
  DEFAULT_ADJUSTMENT_INFO: 'defaultAdjustmentInfo',
};

export const OPCODE_MAPPING_AND_SELECTION_FIELDS_VS_LABELS = {
  [OPCODE_MAPPING_AND_SELECTION_FIELDS.DUE_BILL_OPCODE_LABEL]: __('Due Bill Opcode'),
  [OPCODE_MAPPING_AND_SELECTION_FIELDS.RECALLS_OPCODE_LABEL]: __('Recalls'),
  [OPCODE_MAPPING_AND_SELECTION_FIELDS.CUSTOM_CONCERNS_OPCODE_LABEL]: __('Custom Concerns Opcode'),
  [OPCODE_MAPPING_AND_SELECTION_FIELDS.SUBLET_OPCODE_LABEL]: __('Sublet'),
  [OPCODE_MAPPING_AND_SELECTION_FIELDS.POPULAR_SERVICES_OPCODES_LABEL]: __('Popular Services'),
  [OPCODE_MAPPING_AND_SELECTION_FIELDS.LTG_OPCODE_LABEL]: __('LTG Default Opcode'),
};

export const OPCODE_MAPPING_AND_SELECTION_FIELDS_IDS = [
  [OPCODE_MAPPING_AND_SELECTION_FIELDS.DUE_BILL_OPCODE],
  [OPCODE_MAPPING_AND_SELECTION_FIELDS.RECALLS_OPCODE],
  [OPCODE_MAPPING_AND_SELECTION_FIELDS.CUSTOM_CONCERNS_OPCODE],
  [OPCODE_MAPPING_AND_SELECTION_FIELDS.SUBLET_OPCODE],
  [OPCODE_MAPPING_AND_SELECTION_FIELDS.LTG_OPCODE],
];

export const OPCODE_MAPPING_AND_SELECTION_FIELDS_TO_PICK = [
  OPCODE_MAPPING_AND_SELECTION_FIELDS.DUE_BILL_OPCODE,
  OPCODE_MAPPING_AND_SELECTION_FIELDS.RECALLS_OPCODE,
  OPCODE_MAPPING_AND_SELECTION_FIELDS.CUSTOM_CONCERNS_OPCODE,
  OPCODE_MAPPING_AND_SELECTION_FIELDS.SUBLET_OPCODE,
  OPCODE_MAPPING_AND_SELECTION_FIELDS.LTG_OPCODE,
  OPCODE_MAPPING_AND_SELECTION_FIELDS.POPULAR_SERVICES_OPCODES,
];
export const SERVICE_COMMUNICATION_SETUP_COLUMN_IDS = {
  COMMUNICATION_TYPE: 'communicationType',
  PRE_CHECK_IN: 'preCheckIn',
  POST_CHECK_IN: 'postCheckIn',
};

export const CUSTOM_COMMUNICATION_KEYS = {
  CUSTOM_EMAIL: 'CUSTOM_EMAIL',
  CUSTOM_PHONE_NUMBER: 'CUSTOM_PHONE_NUMBER',
};

export const SERVICE_COMMUNICATION_SETUP_CELL_IDS = {
  SOURCE: 'source',
  NUMBER: 'number',
  EMAIL: 'email',
  SHOW_IN_CONSUMER_PORTAL_HEADERS: 'showInConsumerPortalHeaders',
  SHOW_IN_CONSUMER_SCHEDULING_HEADERS: 'showInConsumerSchedulingHeaders',
  SHOW_IN_CONSUMER_KEY_LOUNGE: 'showInKeyLoungeHeaders',
  COUNTRY_CODE: 'countryCode',
};

export const SERVICE_COMMUNICATION_SETUP_ROW_IDS = {
  PHONE_NUMBER: 'PHONE_NUMBER',
  EMAIL: 'EMAIL',
};

export const SERVICE_COMMUNICATION_SETUP_SOURCE_VALUES = {
  USE_DEALER_CONFIG: 'USE_DEALER_CONFIG',
  CUSTOM: 'CUSTOM',
};

export const SERVICE_COMMUNICATION_SETUP_SOURCE_VS_OPTIONS = {
  PHONE_NUMBER: [
    { label: __('Use Dealer Configuration'), value: SERVICE_COMMUNICATION_SETUP_SOURCE_VALUES.USE_DEALER_CONFIG },
    { label: __('Custom Phone Number'), value: SERVICE_COMMUNICATION_SETUP_SOURCE_VALUES.CUSTOM },
  ],
  EMAIL: [
    { label: __('Use Dealer Configuration'), value: SERVICE_COMMUNICATION_SETUP_SOURCE_VALUES.USE_DEALER_CONFIG },
    { label: __('Custom Email Address'), value: SERVICE_COMMUNICATION_SETUP_SOURCE_VALUES.CUSTOM },
  ],
};

export const SERVICE_COMMUNICATION_SETUP_KEYS = {
  PRE_CHECK_IN_PHONE_SETUP: 'preCheckinPhoneSetup',
  POST_CHECK_IN_PHONE_SETUP: 'postCheckinPhoneSetup',
  PRE_CHECK_IN_EMAIL_SETUP: 'preCheckinEmailSetup',
  POST_CHECK_IN_EMAIL_SETUP: 'postCheckinEmailSetup',
};

export const SERVICE_COMMUNICATION_SETUP_KEYS_VS_COLUMN_IDS = {
  [SERVICE_COMMUNICATION_SETUP_KEYS.PRE_CHECK_IN_PHONE_SETUP]: SERVICE_COMMUNICATION_SETUP_COLUMN_IDS.PRE_CHECK_IN,
  [SERVICE_COMMUNICATION_SETUP_KEYS.PRE_CHECK_IN_EMAIL_SETUP]: SERVICE_COMMUNICATION_SETUP_COLUMN_IDS.PRE_CHECK_IN,
  [SERVICE_COMMUNICATION_SETUP_KEYS.POST_CHECK_IN_PHONE_SETUP]: SERVICE_COMMUNICATION_SETUP_COLUMN_IDS.POST_CHECK_IN,
  [SERVICE_COMMUNICATION_SETUP_KEYS.POST_CHECK_IN_EMAIL_SETUP]: SERVICE_COMMUNICATION_SETUP_COLUMN_IDS.POST_CHECK_IN,
};

export const SERVICE_COMMUNICATION_SETUP_KEYS_VS_ROW_IDS = {
  [SERVICE_COMMUNICATION_SETUP_KEYS.PRE_CHECK_IN_PHONE_SETUP]: SERVICE_COMMUNICATION_SETUP_ROW_IDS.PHONE_NUMBER,
  [SERVICE_COMMUNICATION_SETUP_KEYS.PRE_CHECK_IN_EMAIL_SETUP]: SERVICE_COMMUNICATION_SETUP_ROW_IDS.EMAIL,
  [SERVICE_COMMUNICATION_SETUP_KEYS.POST_CHECK_IN_PHONE_SETUP]: SERVICE_COMMUNICATION_SETUP_ROW_IDS.PHONE_NUMBER,
  [SERVICE_COMMUNICATION_SETUP_KEYS.POST_CHECK_IN_EMAIL_SETUP]: SERVICE_COMMUNICATION_SETUP_ROW_IDS.EMAIL,
};

export const QUESTION_TYPE = {
  TEXT_ONLY: 'TEXT_ONLY',
  STAR_RATING_AND_TEXT: 'STAR_RATING_AND_TEXT',
};

export const QUESTION_SOURCE = {
  CUSTOM: 'CUSTOM',
  DEFAULT: 'DEFAULT',
};

export const MAX_CUSTOMER_SURVEY_ROWS_ALLOWED = 3;

const REVIEW_SITE_NAME = 'name';

export const REVIEW_SITE_IDS_MAP = {
  [FIELD_IDS.REVIEW_SITE_TITLE]: REVIEW_SITE_NAME,
};

export const TECH_PERFORMANCE_REPORT_FORMULA_VALUES = {
  CALCULATE_PRODUCTIVITY_BASED_ON_ACTUAL_HOURS_BY_ATTENDANCE_HOURS:
    'CALCULATE_PRODUCTIVITY_BASED_ON_ACTUAL_HOURS_BY_ATTENDANCE_HOURS',
  CALCULATE_PRODUCTIVITY_BASED_ON_BILL_HOURS_BY_ATTENDANCE_HOURS:
    'CALCULATE_PRODUCTIVITY_BASED_ON_BILL_HOURS_BY_ATTENDANCE_HOURS',
  CALCULATE_PRODUCTIVITY_BASED_ON_ACTUAL_HOURS_WITH_LOST_TIME_BY_ATTENDANCE_HOURS:
    'CALCULATE_PRODUCTIVITY_BASED_ON_ACTUAL_HOURS_WITH_LOST_TIME_BY_ATTENDANCE_HOURS',
  CALCULATE_EFFICIENCY_BASED_ON_FLAG_HOURS_BY_ACTUAL_HOURS: 'CALCULATE_EFFICIENCY_BASED_ON_FLAG_HOURS_BY_ACTUAL_HOURS',
  CALCULATE_EFFICIENCY_BASED_ON_BILL_HOURS_BY_ACTUAL_HOURS: 'CALCULATE_EFFICIENCY_BASED_ON_BILL_HOURS_BY_ACTUAL_HOURS',
  CALCULATE_EFFICIENCY_BASED_ON_ACTUAL_HOURS_BY_ASSIGNED_HOURS:
    'CALCULATE_EFFICIENCY_BASED_ON_ACTUAL_HOURS_BY_ASSIGNED_HOURS',
  CALCULATE_EFFICIENCY_BASED_ON_BILL_HOURS_BY_FLAG_HOURS: 'CALCULATE_EFFICIENCY_BASED_ON_BILL_HOURS_BY_FLAG_HOURS',
  CALCULATE_UTILIZATION_BASED_ON_ACTUAL_HOURS_BY_ATTENDANCE_HOURS:
    'CALCULATE_UTILIZATION_BASED_ON_ACTUAL_HOURS_BY_ATTENDANCE_HOURS',
  CALCULATE_PROFICIENCY_BASED_ON_FLAG_HOURS_BY_ATTENDANCE_HOURS:
    'CALCULATE_PROFICIENCY_BASED_ON_FLAG_HOURS_BY_ATTENDANCE_HOURS',
};

export const TECH_PERFORMANCE_REPORT_PRODUCTIVITY_FORMULA_OPTIONS = [
  {
    label: __('(Actual Hours / Attendance Hours) x 100'),
    value: TECH_PERFORMANCE_REPORT_FORMULA_VALUES.CALCULATE_PRODUCTIVITY_BASED_ON_ACTUAL_HOURS_BY_ATTENDANCE_HOURS,
  },
  {
    label: __('(Bill Hours / Attendance Hours) x 100'),
    value: TECH_PERFORMANCE_REPORT_FORMULA_VALUES.CALCULATE_PRODUCTIVITY_BASED_ON_BILL_HOURS_BY_ATTENDANCE_HOURS,
  },
  {
    label: __('((Actual hours + Lost time ) / Attendance Hours) x 100'),
    value:
      TECH_PERFORMANCE_REPORT_FORMULA_VALUES.CALCULATE_PRODUCTIVITY_BASED_ON_ACTUAL_HOURS_WITH_LOST_TIME_BY_ATTENDANCE_HOURS,
  },
];

export const TECH_PERFORMANCE_REPORT_EFFICIENCY_FORMULA_OPTIONS = [
  {
    label: __('(Flagged Hours / Actual Hours) x 100'),
    value: TECH_PERFORMANCE_REPORT_FORMULA_VALUES.CALCULATE_EFFICIENCY_BASED_ON_FLAG_HOURS_BY_ACTUAL_HOURS,
  },
  {
    label: __('(Assigned Bill Hours / Actual Hours) x 100'),
    value: TECH_PERFORMANCE_REPORT_FORMULA_VALUES.CALCULATE_EFFICIENCY_BASED_ON_BILL_HOURS_BY_ACTUAL_HOURS,
  },
  {
    label: __('(Actual Hours / Assigned Bill hours) x 100'),
    value: TECH_PERFORMANCE_REPORT_FORMULA_VALUES.CALCULATE_EFFICIENCY_BASED_ON_ACTUAL_HOURS_BY_ASSIGNED_HOURS,
  },
  {
    label: __('(Assigned Bill Hours / Flag Hours) x 100'),
    value: TECH_PERFORMANCE_REPORT_FORMULA_VALUES.CALCULATE_EFFICIENCY_BASED_ON_BILL_HOURS_BY_FLAG_HOURS,
  },
];

export const TECH_PERFORMANCE_REPORT_UTILIZATION_FORMULA_OPTIONS = [
  {
    label: __('Actual Hours / Attendance Hours'),
    value: TECH_PERFORMANCE_REPORT_FORMULA_VALUES.CALCULATE_UTILIZATION_BASED_ON_ACTUAL_HOURS_BY_ATTENDANCE_HOURS,
  },
];

export const TECH_PERFORMANCE_REPORT_PROFICIENCY_FORMULA_OPTIONS = [
  {
    label: __('Flag Hours/Attendance Hours'),
    value: TECH_PERFORMANCE_REPORT_FORMULA_VALUES.CALCULATE_PROFICIENCY_BASED_ON_FLAG_HOURS_BY_ATTENDANCE_HOURS,
  },
];
export const TAGS_FILTER_CONFIG_KEYS = {
  TAGS_FILTER_CONFIG: 'tagsFiltersConfig',
  FIELD_ID: 'id',
  NAME: 'name',
  CRITERIA_LABEL: 'criteriaLabel',
};

export const COLLECT_AT_CASHIERING = 'COLLECT_AT_CASHIERING';

export const SELECTED_MENU_KEY_VS_URL = {
  [RO_SETTINGS_KEY.SERVICE_SETTINGS]: ROUTE_CONFIG.RO_SETTINGS.uiAbsoluteRoute,
  [RO_SETTINGS_KEY.PRODUCTIVITY_BONUS_SETTINGS]: ROUTE_CONFIG.PRODUCTIVITY_BONUS_SETTINGS.uiAbsoluteRoute,
  [RO_SETTINGS_KEY.TAX_CODES]: ROUTE_CONFIG.TAX_CODE_SETTINGS.uiAbsoluteRoute,
};

export const MAKE_OPTIONS = [
  {
    label: __('Renault'),
    value: 'renault',
  },
  {
    label: __('Alpine'),
    value: 'alpine',
  },
  {
    label: __('Dacia'),
    value: 'dacia',
  },
];

// The values are invoice categories setup by CMS team to decide the type of invoice.
export const INVOICE_CATEGORY_OPTIONS = [
  {
    label: __('2'),
    value: 2,
  },
  {
    label: __('8'),
    value: 8,
  },
  {
    label: __('9'),
    value: 9,
  },
];

export const INCLUDE_SERVICE_RESTRICED_SETTINGS = false;

export const DEFAULT_FOOTER_HEIGHT = '64';
export const RETURN_RO_PAYER = 'PAYER';
export const RETURN_RO_PERIOD = 'PERIOD';

export const HOLD_HEADER = __(
  'To set up hold automation, you need to define conditions based on certain filters. If the conditions in both "Hold When" and "Remove When" columns are left empty, the hold will be manual. For automated holds, ensure that the "Hold When" column includes at least one job-level filter such as: Job Type/Pay Type, Job Status, Job Parts Status, Sublet Job, Job Tags, Approval Rule Name, or Approval Status.'
);

export const FILTER_TYPE_VS_RO_ENTITY_TYPE = {
  [FILTER_PAY_TYPES]: RO_ENTITY_TYPES.JOB,
  [DEPARTMENT_ID]: RO_ENTITY_TYPES.RO,
  [FILTER_JOB_TYPES]: RO_ENTITY_TYPES.JOB,
  [FILTER_JOB_STATUSES_KEY]: RO_ENTITY_TYPES.JOB,
  [FILTER_STATUS_KEY]: RO_ENTITY_TYPES.RO,
  [RO_FLAG_ID]: RO_ENTITY_TYPES.RO,
  [JOB_PART_STATUS]: RO_ENTITY_TYPES.JOB,
  [RO_TYPE]: RO_ENTITY_TYPES.RO,
  [SUBLET_JOB]: RO_ENTITY_TYPES.JOB,
  [JOB_TAG_ID]: RO_ENTITY_TYPES.JOB,
};

export const HOLD_COLUMN_ID_VS_ERROR_COLUMN_ID = {
  [FIELD_IDS.HOLD_ADDED_BY]: FIELD_IDS.ADD_WHEN,
  [FIELD_IDS.HOLD_REMOVED_BY]: FIELD_IDS.REMOVE_WHEN,
};

export const SERVICE_SETTINGS_AUDIT_TIME_SPECIFIC_FIELDS = [
  'modifiedTime',
  'createdTime',
  'promiseTime',
  'promiseTimeHour',
  'promiseTimeMins',
];

export const SERVICE_SETTINGS_AUDIT_CURRENCY_SPECIFIC_FIELDS = [
  FIELD_IDS.DEFAULT_WARRANTY_LABOR_RATE,
  FIELD_IDS.DEFAULT_INTERNAL_LABOR_RATE,
  FIELD_IDS.DEFAULT_CUSTOMER_PAY_LABOR_RATE,
];

export const SERVICE_SETTINGS_AUDIT_MINUTE_SPECIFIC_FIELDS = [
  'roStatusSnoozeIntervalInSeconds',
  FIELD_IDS.CUSTOMER_ARRIVAL_NOTIFICATION_BEFORE,
];

export const ESTIMATE_DISPLAY_SETTINGS_CONTROL_FIELDS_VS_LABEL = {
  [FIELD_IDS.DISPLAY_ESTIMATE_AMOUNT_ON_RO_JOB_CARD]: __('Display Estimate amount on RO Job Card'),
  [FIELD_IDS.DISPLAY_ESTIMATE_AMOUNT_ON_QUOTE_JOB_CARD]: __('Display Estimate amount on each Quote Job Card'),
  [FIELD_IDS.DISPLAY_ESTIMATE_AMOUNT_ON_APPOINTMENT_JOB_CARD]: __(
    'Display Estimate amount on each Appointment Job Card'
  ),
  [FIELD_IDS.DISPLAY_ESTIMATE_AMOUNT_ON_CHECK_IN_JOB_CARD]: __(
    'Display Estimate amount on each Check-In Job Card in the cart'
  ),
  [FIELD_IDS.DISPLAY_ESTIMATE_AMOUNT_ON_RECOMMENDATION_JOB_CARD]: __(
    'Display Estimate amount on each Recommendation Card'
  ),
};
export const MARK_POLICY_NUMBER_AND_CLAIM_NUMBER_FIELDS_AS_MANDATORY_VS_LABELS = {
  [FIELD_IDS.MARK_POLICY_NUMBER_AND_CLAIM_NUMBER_FIELDS_AS_MANDATORY]: __(
    'Mark Policy Number and Claim Number fields as mandatory'
  ),
};

export const TRIGGER_RETURN_WORK_FLOW_TYPES = {
  [RETURN_RO_TRIGGER_TYPES.PERIOD]: {
    label: __('Return $$(RO) Period'),
    value: RETURN_RO_TRIGGER_TYPES.PERIOD,
  },
  [RETURN_RO_TRIGGER_TYPES.PAYER]: {
    label: __('Return $$(RO) Payer'),
    value: RETURN_RO_TRIGGER_TYPES.PAYER,
  },
};

export const APPROVAL_METADATA_REQUEST_TYPES = {
  OTHER_LABOR_HOURS: 'OTHER_LABOR_HOURS',
  RECOMMENDATION_TO_JOB: 'RECOMMENDATION_TO_JOB',
};

export const APPROVAL_METADATA_REQUEST_TYPE_VS_FIELDS_IDS = {
  [APPROVAL_METADATA_REQUEST_TYPES.OTHER_LABOR_HOURS]: FIELD_IDS.LABOR_HOUR_APPROVERS,
  [APPROVAL_METADATA_REQUEST_TYPES.RECOMMENDATION_TO_JOB]: FIELD_IDS.RO_RECOMMENDATION_APPROVERS,
};

export const JOB_REVIEWER_DROPDOWN_OPTIONS = [
  { label: __('Service Advisor on $$(RO)'), value: 'SERVICE_ADVISOR' },
  { label: __('Manual'), value: 'MANUAL_ASSIGNMENT' },
];
