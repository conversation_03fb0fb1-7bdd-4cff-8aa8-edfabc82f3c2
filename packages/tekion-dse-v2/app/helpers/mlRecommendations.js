import _get from 'lodash/get';
import _map from 'lodash/map';
import _head from 'lodash/head';
import _floor from 'lodash/floor';
import _reduce from 'lodash/reduce';
import _concat from 'lodash/concat';
import _isEmpty from 'lodash/isEmpty';

import { getSignedURLs } from 'tcomponents/actions/uploadAction';
import { getMediaConfig } from 'tcomponents/organisms/mediaUploader/mediaUploader.utils';
import { EMPTY_ARRAY } from 'tbase/app.constants';

import RecommendedServicesReader from 'tbusiness/appServices/dse/readers/RecommendedServices';

import { CART_CONSTANTS } from 'constants/general';
import { CARD_WIDTH } from 'organisms/ServiceCard';
import { APPOINTMENT_JOB_TYPES } from 'constants/appointmentJobType';
import { RECOMMENDED_SERVICES_CONSTANTS, RECOMMENDATION_TYPES } from 'constants/mlRecommendations';

const getMLServices = (serviceRecommendations) => {
  const recommendedServices = _get(serviceRecommendations, 'data.data.serviceRecommendations', EMPTY_ARRAY);
  const promotionalServices = _get(serviceRecommendations, 'data.data.promotions', EMPTY_ARRAY);

  const recommendedServicesWithType = _map(recommendedServices, service => ({
    ...service,
    [RECOMMENDED_SERVICES_CONSTANTS.RECOMMENDATION_TYPE]: RECOMMENDATION_TYPES.SERVICE,
  }));

  const promotionalServicesWithType = _map(promotionalServices, service => ({
    ...service,
    [RECOMMENDED_SERVICES_CONSTANTS.RECOMMENDATION_TYPE]: RECOMMENDATION_TYPES.PROMOTION,
  }));

  return _concat(recommendedServicesWithType, promotionalServicesWithType);
};

const getMediaIdFromServiceResponse = (service, mediaKey) => _get(_head(RecommendedServicesReader.mediaIds(service)), mediaKey);

const parseRecommendationsData = (serviceData, mediaResponse, isPresigned = false) => {
  const parsedData = _map(serviceData, (service, index) => {
    const mediaId = getMediaIdFromServiceResponse(service, 'mediaId');
    const imageURL = isPresigned ? mediaResponse[mediaId] : mediaResponse[index];
    return {
      [CART_CONSTANTS.ID]: RecommendedServicesReader.serviceId(service) || RecommendedServicesReader.promotionId(service),
      [CART_CONSTANTS.IS_ADDED]: false,
      [RECOMMENDED_SERVICES_CONSTANTS.LABEL]: RecommendedServicesReader.displayName(service),
      [RECOMMENDED_SERVICES_CONSTANTS.DISCOUNTED_PRICE]: RecommendedServicesReader.price(service),
      [RECOMMENDED_SERVICES_CONSTANTS.PRE_TAX_AMOUNT]: RecommendedServicesReader.preTaxAmount(service),
      [RECOMMENDED_SERVICES_CONSTANTS.ORIGINAL_PRICE]: '', // currently there is no original_price in API response.
      [RECOMMENDED_SERVICES_CONSTANTS.IMAGE_URL]: imageURL,
      [RECOMMENDED_SERVICES_CONSTANTS.OPCODE]: RecommendedServicesReader.opcode(service),
      [RECOMMENDED_SERVICES_CONSTANTS.RECOMMENDATION_TYPE]: RecommendedServicesReader.mlRecommendationType(service),
      [RECOMMENDED_SERVICES_CONSTANTS.SHOW_PRICE]: RecommendedServicesReader.showPrice(service),
      [RECOMMENDED_SERVICES_CONSTANTS.PRICE_TYPE]: RecommendedServicesReader.priceType(service),
      [CART_CONSTANTS.APPOINTMENT_JOB_TYPE]: APPOINTMENT_JOB_TYPES.ML_SERVICE_RECOMMENDATION,
      [RECOMMENDED_SERVICES_CONSTANTS.LABOUR_PRICE]: RecommendedServicesReader.labourPrice(service),
      [RECOMMENDED_SERVICES_CONSTANTS.PART_PRICE]: RecommendedServicesReader.partPrice(service),
      [RECOMMENDED_SERVICES_CONSTANTS.ML_DEFAULT_OPCODE]: RecommendedServicesReader.mlDefaultOpcode(service),
      [RECOMMENDED_SERVICES_CONSTANTS.POST_TAX_AMOUNT]: RecommendedServicesReader.postTaxAmount(service),
    };
  });
  return parsedData;
};

export const getPaginationCount = contentWidth => _floor(((contentWidth * 0.75 - 20)) / CARD_WIDTH);

const getMediaValuesByKey = (recommendedServices, mediaKey) => _reduce(recommendedServices, (mediaValuesByKey, service) => {
  const mediaValue = getMediaIdFromServiceResponse(service, mediaKey);

  if (mediaValue) return [...mediaValuesByKey, mediaValue];
  return mediaValuesByKey;
}, EMPTY_ARRAY);

export const getCurrentLastIndex = (totalCount, currentIndex, cardsPerPage) => {
  const updatedIndex = currentIndex + cardsPerPage;
  return totalCount < updatedIndex ? totalCount : updatedIndex;
};

export const getMLRecommendationsWithImages = async (fetchRecommendationsAction, payload) => {
  let mlRecommendations;
  try {
    const serviceRecommendations = await fetchRecommendationsAction(payload);
    const mlServices = getMLServices(serviceRecommendations);
    let serviceImageUrls = getMediaValuesByKey(mlServices, 'publicUrl');
    let isPresigned = false;
    if (_isEmpty(serviceImageUrls)) {
      const mediaIds = getMediaValuesByKey(mlServices, 'mediaId');
      if (!_isEmpty(mediaIds)) {
        const mediaResponse = await getSignedURLs(getMediaConfig(), mediaIds)();
        serviceImageUrls = _get(mediaResponse, 'data');
      } else {
        serviceImageUrls = EMPTY_ARRAY;
      }
      isPresigned = true;
    }
    mlRecommendations = parseRecommendationsData(mlServices, serviceImageUrls, isPresigned);
  } catch {
    mlRecommendations = EMPTY_ARRAY;
  }
  return mlRecommendations;
};
