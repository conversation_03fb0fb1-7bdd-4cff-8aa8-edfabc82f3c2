/* eslint-disable consistent-return */
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _values from 'lodash/values';
import _find from 'lodash/find';
import _compact from 'lodash/compact';
import _toString from 'lodash/toString';
import _filter from 'lodash/filter';
import _cloneDeep from 'lodash/cloneDeep';
import _groupBy from 'lodash/groupBy';
import _forEach from 'lodash/forEach';
import _includes from 'lodash/includes';
import _noop from 'lodash/noop';
import _map from 'lodash/map';
import _pick from 'lodash/pick';
import _uniq from 'lodash/uniq';
import _head from 'lodash/head';
import _isNil from 'lodash/isNil';
import _castArray from 'lodash/castArray';
import _reduce from 'lodash/reduce';
import _keys from 'lodash/keys';
import _reject from 'lodash/reject';
import _isFunction from 'lodash/isFunction';
import _toNumber from 'lodash/toNumber';
import _unset from 'lodash/unset';
import _sortBy from 'lodash/sortBy';
import _isEqual from 'lodash/isEqual';

import { createAction } from 'redux-actions';
import produce from 'immer';

import resolver from '@tekion/tekion-base/bulkResolvers';
import { ENTITIES } from '@tekion/tekion-base/bulkResolvers/entities';

import { BASE_REDUCER_KEY } from 'constants/constants';
import { EMPTY_OBJECT, EMPTY_ARRAY, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { createActionWithLog } from '@tekion/tekion-base/logger/createActionWithLog';
import * as ColumnReader from '@tekion/tekion-base/marketScan/readers/columnData.reader';
import * as DealReader from '@tekion/tekion-base/marketScan/readers/deal.reader';
import { getOpenRecall } from '@tekion/tekion-base/helpers/vehicle.helper';
import { toaster, dismissToast, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import { USMetaData } from '@tekion/tekion-base/marketScan/constants/marketScan.constants';
import TEnvReader from '@tekion/tekion-base/readers/Env';
import { getSelectedDealerSites } from '@tekion/tekion-base/helpers/oemSite';
import { CUSTOMER_TYPES_VALUES } from '@tekion/tekion-base/constants/signature';
import { DOCUMENTS_SHARE_FLOW_FOR_ESIGN } from '@tekion/tekion-base/constants/concierge';
import UserReader from '@tekion/tekion-base/readers/User';
import * as VehicleReader from 'utils/vehicleReader';
import { DynamicPropertyHelper } from '@tekion/tekion-widgets/src/helpers/dynamicPropertyHelper';

import {
  getDealFollowUpByDate,
  getDealFollowUpForPayment,
} from 'pages/desking/components/Contracts/Contracts.selectors';
import { RECEIVABLES_LABEL } from 'pages/desking/components/Contracts/ContractTable/ContractTable.constants';
import VehicleAPI from 'pages/StartDeal/Components/Vehicle/vehicle.api';
import { API_STATUS, MESSAGES, DEPOSIT_MESSAGES, IS_RE_SIGN_DECLINATION_MANDATORY } from 'constants/pages';
import { PRINT_ASSET_TYPES_VS_SHEET_TYPE } from '@tekion/tekion-base/marketScan/constants/printAssetTypes.constants';
import { errorCallback } from 'utils/toast';
import { getAPIError } from 'utils/error.reader';
import { getNumber } from 'utils';
import {
  getAccessoryForCashDeficiency,
  removeCashDeficiencyAccessory,
} from 'pages/desking/components/accessoriesUpdate/accessoriesUpdateReader';
import * as ScanService from 'pages/desking/headers/dealJacket/scan.service';
import * as ScanDocHelpers from 'pages/desking/headers/dealJacket/scanDocuments/scanDocuments.helpers';
import DealJacketApis from 'pages/desking/headers/dealJacket/dealJacket.api';
import CommonAPI from 'commonActions/apis';
import COMMON_ACTION_TYPES from 'commonActions/actionTypes';
import setDealDirtyStatus from 'commonActions/actions/dealDirtyAction';
import FNI_ACTION_TYPES from 'pages/desking/components/fniUpdate/fniMenu.actionTypes';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';
import { findArrayItem } from '@tekion/tekion-base/marketScan/utils';
import DealSetupAPI from 'commonActions/apis/dealSetup.api';
import FormSetupAPI from 'pages/formMappingSetup/formMappingSetup.api';
import { getAccessibleDealerSites } from '@tekion/tekion-widgets/src/appServices/sales/dealerSiteFilter.helpers';
import { DEAL_TYPES } from '@tekion/tekion-base/constants/deal/type';
import {
  getDealerLogo,
  getDealerName,
  getDealerWebsite,
  getDealerPhone,
  getDealerEmail,
  getFormattedDefaultDealerAddress,
} from 'utils/dealerConfig.reader';
import { canUseDealershipAddressInformation } from '@tekion/tekion-base/marketScan/readers/salesSetup.reader';
import { getSignedURLsWithMediaMap } from '@tekion/tekion-widgets/src/hocs/sales/mediaUploaderHOCV3/MediaUploaderHOCV3.service';
import {
  separateCompletionCertsFromDealJacket,
  getUpdateFormPrintStatusPaylod,
  getGDPRConfig,
  getDocumentIdBasedOnFromSheet,
} from 'utils/document.util';
import CalcEngineProperties from 'utils/CalcEngineProperties';
import { DSP_SHARING_TYPES, SIGNING_PARTNER } from 'constants/signature';
import { getIpadDeviceId } from 'utils/sessionStorage.reader';

import {
  formatDeskingDetailsForServer,
  getFinalCashDeficiencyForDownpaymentId,
  getDeferredPayments,
  getDownPaymentObjectForAllTypes,
  isCategory1Deal,
} from '@tekion/tekion-base/marketScan/utils/desking.utils';
import { isInchcape, isInchcapeOrRRG, isRRG } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
import { tget } from '@tekion/tekion-base/utils/general';
import { getApprovalRequestByIdV3 } from '@tekion/tekion-widgets/src/organisms/approvalWorkFlowCommon/actions/approvalWorkspace.actions';
import { fetchUserByIds } from '@tekion/tekion-business/src/actions/userActions';

import {
  getAddNewParticipantToDSPPayload,
  parseGetSigningAppUrlsResponse,
  getSigningUrlFromAddUserResponse,
  getIsSharingOnConcierge,
  getIsIpadCasting,
  getESignPlatformUrlWithAdditionalParameters,
  handleDigitalSigningPlatformErrors,
} from 'utils/digitalSigningPlatform.utils';
import { getPayloadForVirtulMeetingInit, getVirtualMeetingParticipantList } from 'utils/virtualMeeting.utils';
import { MAP_DMS_TO_API_PATH } from 'pages/externalDMSMapping/externalDMSMapping.constants';
import { DSP_DEFAULT_SIGNING_USERS, IPAD_CASTING_REQUEST_TYPES } from 'constants/digitalSigningPlatform';
import { getVehiclesById, getVehiclesByIdV2 } from 'commonActions/actions';
import { hasFniEdit, hasCostAndGrossView } from 'permissions/desking.permissions';
import { getTradeInVehicleId } from 'tbase/utils/sales';
import { addMissingDealKeys } from '../../utils/deal.util';
import StopShare from './components/digitalRetail/components/StopShare';
import ACTION_TYPES from './desking.actionTypes';
import DeskingAPI from './desking.api';
import CRMApi from './crm.api';
import DigitalRetailAPI from './components/digitalRetail/digitalRetail.api';
import {
  selectLendersFromDealerConfig,
  getDeskingPaymentDetails,
  getDeal,
  getTaxAndFeeMetadata,
  getVMDetails,
  getVMParticipantsList,
  getESignSessionActive,
} from './desking.selectors';
import { isFlowSimilarToPEN } from './components/fniUpdate/fniUpdate.reader';
import { getFniSurveyStatus, getFniCustomAidStatus } from './components/fniUpdate/fniUpdate.util';
import { FNI_VIEW_TYPE } from './components/fniUpdate/fniUpdate.constants';
import { FNI_SURVEY_STATUS } from './components/fniUpdate/Survey/survey.constants';
import { CUSTOM_AID_STATUS } from './components/fniUpdate/fniMenu/components/customAid/customAid.constants';
import { DEAL_STATUS, DEAL_JACKET } from '../deallist/deal.constants';
import { LEFT_PANEL_ITEMS, DMS_WITH_ADD_CUSTOMER_SUPPORT } from './desking.constants';
import { setDealVersion } from '../../commonActions/apis/dealUpdateQueue.api';
import { getFinanceContractStatusDetails } from '../../readers/columnDataReader';
import {
  FINANCE_CONTRACT_STATUS_NUMBER_TO_STATUS_MAP,
  STATUS_VS_TOASTER_MESSAGE,
  STATUS_VS_TOASTER_TYPE,
} from './components/dealDetails/titleColumn/lenderPayment/components/financeContractSuffixIconComponent/financeContractSuffixIconComponent.constants';
import { fetchSinglePreOrderDetails, fetchSingleCustomOrderDetails } from '../orderManagement/services/orderManagement';
import { mapFieldConfigToFni, mapFniToFieldConfig } from '../../organisms/pdfControllers/pdfControllers.helper';
import {
  isLenderProgramExpired,
  getLenderMetaDataAPIPayload,
  getPayloadForUpdateCustomerApprovalDetails,
  getApprovalDetails,
  formatTradeInMediaDetailsToForm,
  formatTradeInMediaDetailsToPayload,
} from './desking.helpers';
import { recapApprovalDataReader } from './components/fniRecap/FNIRecap.readers';
import { getTradeInVehicleMediaAssociations } from './readers/desking.reader';

const makeDeskingDirty = () => ({ dealDirty: true });

const dispatchWithDealDirty = type => payload => async dispatch => {
  dispatch([
    {
      type,
      payload,
    },
    {
      type: ACTION_TYPES.SET_DEAL_DIRTY_STATUS,
      payload: true,
    },
  ]);
};

export { setDealDirtyStatus };

export const setNewVehiclesInDeal = createActionWithLog(ACTION_TYPES.SET_NEW_VEHICLES_IN_DEAL);
export const setMarketScanVehicleIdInDeal = createActionWithLog(ACTION_TYPES.SET_MARKET_SCAN_VEHICLE_ID_IN_DEAL);
export const setVehicleOptionsInDeal = createActionWithLog(ACTION_TYPES.SET_VEHICLE_OPTIONS_IN_DEAL);
export const setVehicleTrimDetailsInDeal = createActionWithLog(ACTION_TYPES.SET_VEHICLE_TRIM_DETAILS_IN_DEAL);
export const setDownPayments = createActionWithLog(ACTION_TYPES.SET_DOWN_PAYMENTS);
export const setColumnsData = createActionWithLog(ACTION_TYPES.SET_COLUMNS_DATA);
export const setUpdatedColumnsData = createAction(ACTION_TYPES.SET_UPDATED_COLUMN_DATA);
export const resetDeskingData = createActionWithLog(ACTION_TYPES.DESKING_DATA_RESET);
export const setSelectedDealForDesking = createActionWithLog(ACTION_TYPES.DESKING_SET_SELECTED_DEAL_DATA);
export const setConciergeFlag = createActionWithLog(ACTION_TYPES.SET_CONCIERGE_FLAG);
export const replaceColumnData = createAction(ACTION_TYPES.DESKING_REPLACE_COLUMN);
export const addNewColumn = createActionWithLog(ACTION_TYPES.DESKING_ADD_NEW_COLUMN);
export const onDownPaymentChange = dispatchWithDealDirty(ACTION_TYPES.DESKING_ON_DOWN_PAYMENT_CHANGE, makeDeskingDirty);
export const onDownPaymentPercentageChange = createActionWithLog(
  ACTION_TYPES.DESKING_ON_DOWN_PAYMENT_PERCENTAGE_CHANGE,
  makeDeskingDirty
);
export const setChangingDwnPmt = createActionWithLog(ACTION_TYPES.SET_CHANGING_DWN_PMT);
export const setMultipleDownPaymentStatus = createActionWithLog(
  ACTION_TYPES.SET_MULTIPLE_DOWNPAYMENT_STATUS,
  makeDeskingDirty
);
export const setSelectedDownPayment = createActionWithLog(ACTION_TYPES.SET_SELECTED_DOWN_PAYMENT);
export const setSelectedLender = dispatchWithDealDirty(ACTION_TYPES.SET_SELECTED_LENDER, makeDeskingDirty);
export const onViewDeskingReplaceLenderWithNewLenderDetails = createActionWithLog(
  ACTION_TYPES.ON_VIEW_ON_DESKING_REPLACE_LENDER_WITH_NEW_LENDER_DETAILS
);
export const setLenderId = dispatchWithDealDirty(ACTION_TYPES.SET_SELECTED_LENDER_ID, makeDeskingDirty);

export const setSelectedLenderForCash = dispatchWithDealDirty(ACTION_TYPES.SET_SELECTED_CASH_LENDER, makeDeskingDirty);
// export const changeResidualData = dispatchWithDealDirty(ACTION_TYPES.CHANGE_RESIDUAL_DATA, makeDeskingDirty);
export const changeYearlyMiles = dispatchWithDealDirty(ACTION_TYPES.CHANGE_YEARLY_MILES, makeDeskingDirty);
export const setYearlyMiles = dispatchWithDealDirty(ACTION_TYPES.SET_YEARLY_MILES, makeDeskingDirty);
export const changeDaysToFirstPayment = dispatchWithDealDirty(
  ACTION_TYPES.CHANGE_DAYS_TO_FIRST_PAYMENT,
  makeDeskingDirty
);

export const setMsd = dispatchWithDealDirty(ACTION_TYPES.SET_MSD, makeDeskingDirty);
export const setWaiverReason = dispatchWithDealDirty(ACTION_TYPES.SET_WAIVER_REASON, makeDeskingDirty);
export const resetWaiverReason = dispatchWithDealDirty(
  ACTION_TYPES.RESET_WAIVER_REASON_FOR_SELECTED_COLUMN,
  makeDeskingDirty
);
export const resetMSD = dispatchWithDealDirty(ACTION_TYPES.RESET_MSD_FOR_SELECTED_COLUMN, makeDeskingDirty);

export const changeAPR = dispatchWithDealDirty(ACTION_TYPES.CHANGE_APR, makeDeskingDirty);
export const changeAPRManualFlag = createActionWithLog(ACTION_TYPES.CHANGE_APR_MANUAL_FLAG);
export const modifyDealLevelInfo = dispatchWithDealDirty(ACTION_TYPES.MODIFY_DEAL_LEVEL_INFO);

export const setPaymentOptions = dispatchWithDealDirty(ACTION_TYPES.SET_PAYMENT_OPTIONS, makeDeskingDirty);
export const setNewColumnIds = createAction(ACTION_TYPES.SET_NEW_COLUMN_IDS);
export const setDeal = createActionWithLog(ACTION_TYPES.SET_DEAL);
export const setDealv2 = createActionWithLog(ACTION_TYPES.SET_DEAL_V2);
export const invalidateFieldsAndSetDeal = createActionWithLog(ACTION_TYPES.INVALIDATE_FIELDS_AND_SET_DEAL);
export const setDealType = createActionWithLog(ACTION_TYPES.DESKING_SET_DEAL_TYPE);
export const setFniSurveyStatus = createActionWithLog(ACTION_TYPES.SET_FNI_SURVEY_STATUS);
export const setDealStatus = createActionWithLog(ACTION_TYPES.DESKING_SET_DEAL_STATUS);
export const setDealActivity = createActionWithLog(ACTION_TYPES.DESKING_SET_DEAL_ACTIVITY);
export const setContractDate = createActionWithLog(ACTION_TYPES.DESKING_SET_CONTRACT_DATE);
export const setPaymentFrquencyType = createActionWithLog(ACTION_TYPES.DESKING_SET_PAYMENT_FREQUENCY_TYPE);
export const setDealAccountPostingId = createActionWithLog(ACTION_TYPES.DESKING_SET_DEAL_ACCOUNT_POSTING_ID);
export const setTradeInVehicles = createActionWithLog(ACTION_TYPES.DEAL_SET_TRADE_IN_VEHICLES);
export const setTradeInDeal = dispatchWithDealDirty(ACTION_TYPES.DEAL_SET_TRADE_IN_DEAL, makeDeskingDirty);
export const setCommissionAndAssigneeDetails = createActionWithLog(ACTION_TYPES.SET_COMMISSION_DETAILS);

export const deleteTradeInVehicle = createActionWithLog(ACTION_TYPES.DEAL_DELETE_TRADE_IN_VEHICLE);
export const feesSave = createActionWithLog(ACTION_TYPES.DESKING_FEE_COLUMN_SAVE);
export const updateDealFees = createActionWithLog(ACTION_TYPES.UPDATE_DEAL_FEES);
export const addNewEmptyFeeToAllColumn = createActionWithLog(ACTION_TYPES.DESKING_ADD_NEW_EMPTY_FEE_TO_ALL_COLUMN);
export const removeUnwantedFeeInDealFees = createActionWithLog(ACTION_TYPES.DESKING_REMOVE_UNNWANTED_FEE);
export const setPrimaryVehicleInfo = createActionWithLog(ACTION_TYPES.DEAL_SET_PRIMARY_VEHICLE_DATA);
export const setIsVehicleTargetingValue = createActionWithLog(ACTION_TYPES.SET_IS_VEHICLE_TARGETING);
export const setVehicleInfo = createActionWithLog(ACTION_TYPES.DEAL_SET_VEHICLE_DATA);
export const setPrimaryCustomerInfo = dispatchWithDealDirty(
  ACTION_TYPES.DEAL_SET_PRIMARY_CUSTOMER_DATA,
  makeDeskingDirty
);
export const deleteColumn = createActionWithLog(ACTION_TYPES.DESKING_REMOVE_COLUMN);
export const saveFNIs = createActionWithLog(ACTION_TYPES.DESKING_SAVE_FNI);
export const setFNIsInColIds = createActionWithLog(ACTION_TYPES.SET_FNIS_IN_COLUMN_IDS);
export const setMarketScanDataFetchingStatus = createAction(ACTION_TYPES.SET_FETCHING_MARKET_SCAN_DATA_STATUS);
export const setFetchingUpdateDealStaus = createAction(ACTION_TYPES.SET_UPDATE_DEAL_STATUS);
// export const getAndSetValidRebates = createAction(ACTION_TYPES.GET_AND_SET_VALID_REBATES);
export const saveAccessories = createActionWithLog(ACTION_TYPES.DESKING_SAVE_ACCESSORIES);
export const saveMultipleAccessories = createActionWithLog(ACTION_TYPES.DESKING_SAVE_MULTIPLE_ACCESSORIES);

export const setSelectedFinanceManager = createAction(ACTION_TYPES.SET_SELECTED_FINANCE_MANAGER);
export const setSelectedSalesManager = createAction(ACTION_TYPES.SET_SELECTED_SALES_MANAGER);
export const setWorkingCashConfiginDeal = createActionWithLog(ACTION_TYPES.SET_WORKINNG_CASH_CONFIG_IN_DEAL);
export const setDeferredPaymentDetails = createActionWithLog(ACTION_TYPES.SET_DEFERRED_PAYMENT_DETAILS);
export const setSecurityDeposit = createActionWithLog(ACTION_TYPES.SET_SECURITY_DEPOSIT);
export const setSubventionCost = createActionWithLog(ACTION_TYPES.SET_SUBVENTION_COST);
export const setPaymentDetailsInDeal = createAction(ACTION_TYPES.SET_PAYMENT_DETAILS_IN_DEAL);
export const setStateFeeTaxOptions = dispatchWithDealDirty(ACTION_TYPES.SET_STATE_FEE_TAX_OPTIONS, makeDeskingDirty);
export const resetTradeIn = createAction(ACTION_TYPES.RESET_TRADE_IN);
export const removeAllColumns = createAction(ACTION_TYPES.REMOVE_ALL_COLUMNS);
export const setEMIAmountForDealerTrade = createAction(ACTION_TYPES.SET_EMI_AMOUNT);
export const setTaxDetails = createAction(ACTION_TYPES.SET_TAX_DETAILS);
export const setDMVROSInDeal = createAction(ACTION_TYPES.SET_DMV_ROS_IN_DEAL);
export const setBaseEMIAmount = createActionWithLog(ACTION_TYPES.SET_BASE_EMI_EMOUNT);

export const saveAccessoriesFromRecap = createAction(ACTION_TYPES.SAVE_ACCESSORIES_FROM_RECAP);
export const saveFNIFromRecap = createAction(ACTION_TYPES.RECAP_SAVE_FNI);
export const setFinanceReserveFromRecap = createAction(ACTION_TYPES.RECAP_SET_FINANCE_RESERVE);
// for tab switch
export const setSelectedtab = createAction(ACTION_TYPES.DESKING_SET_ACTIVE_TAB);

export const setResidualDisplayType = createActionWithLog(ACTION_TYPES.SET_RESIDUAL_DISPLAY_TYPE);

export const setMarketScanData = createAction(ACTION_TYPES.SET_MARKET_SCAN_DATA);
export const setProgramIds = createAction(ACTION_TYPES.SET_PROGRAM_IDS);
export const setGenericAndErrorStatus = createActionWithLog(ACTION_TYPES.SET_GENERIC_AND_ERROR_STATUS);

export const setDealSheetPrintPayload = createAction(ACTION_TYPES.SET_DEAL_SHEET_PRINT_PAYLOAD);
export const removePenContractInDeal = createAction(ACTION_TYPES.REMOVE_PEN_CONTRACT_INFO_DEAL);
export const replacePenContractInfoInDeal = createAction(ACTION_TYPES.REPLACE_PEN_CONTRACT_INFO_IN_DEAL);
export const saveColumnBasedFNIs = createAction(ACTION_TYPES.SAVE_COLUMN_BASED_FNIS);
export const resetKeysTermpaymentDetails = createAction(ACTION_TYPES.RESET_KEYS_IN_TERM_PAYMENT_DETAILS);
export const setValuesInTermPmtDetails = createAction(ACTION_TYPES.SET_VALUES_IN_TERM_PMT_DETAILS);
export const setValuesInColumns = createAction(ACTION_TYPES.SET_VALUES_IN_COLUMNS);
export const setBuyerAddressInfo = createAction(ACTION_TYPES.DEAL_SET_BUYER_ADDRESS);
export const setTaxAndZipCodeDetails = dispatchWithDealDirty(
  ACTION_TYPES.DEAL_SET_TAX_AND_ZIP_CODE_DETAILS,
  makeDeskingDirty
);
export const setTaxOverridenDetails = dispatchWithDealDirty(
  ACTION_TYPES.DEAL_SET_TAX_OVERIDE_DETAILS,
  makeDeskingDirty
);
export const setTaxAndZipCodeDetailsForFee = dispatchWithDealDirty(
  ACTION_TYPES.DEAL_SET_TAX_AND_ZIP_CODE_DETAILS_FEE,
  makeDeskingDirty
);
export const setTaxCode = dispatchWithDealDirty(ACTION_TYPES.DEAL_SET_TAX_CODE, makeDeskingDirty);
export const clearDeskingContract = createAction(ACTION_TYPES.CLEAR_CONTRACT);
export const setCallMarketScan = createAction(ACTION_TYPES.SET_CALL_MARKET_SCAN);
export const setPdfLoaderDealJacket = createAction(ACTION_TYPES.SET_PDF_LOADER_DEAL_JACKET);
export const setLeaseConfigurations = createAction(ACTION_TYPES.SET_LEASE_CONFIGURATIONS);
export const setSecurityDepositWaiverReason = createAction(ACTION_TYPES.SET_SECURITY_DEPOSIT_WAIVER_REASON);
export const setCollectTaxesFlag = createAction(ACTION_TYPES.SET_COLLECT_TAXES);
export const setCollectFeesFlag = createAction(ACTION_TYPES.SET_COLLECT_FEES);
export const setScanValues = createAction(ACTION_TYPES.SET_SCAN_VALUES);

export const setSelectedLenderTier = createAction(ACTION_TYPES.SET_SELECTED_LENDER_TIER);

export const setResidualAdjustment = createAction(ACTION_TYPES.SET_ADJUSTED_RESIDUAL);

export const setPaymentType = createAction(ACTION_TYPES.SET_PAYMENT_TYPE);
export const setOptionContractValidityDate = createAction(ACTION_TYPES.SET_OPTION_CONTRACT_VALIDITY_DATE);

export const setRebates = createAction(ACTION_TYPES.SET_REBATES);

export const updateDeliveryCode = createAction(ACTION_TYPES.UPDATE_DELIVERY_CODE);

export const updateBuyer = createAction(ACTION_TYPES.UPDATE_BUYER);

export const setDeskingPaymentDetailsDelvehicleId = createAction(
  ACTION_TYPES.SET_DESKING_PAYMENT_DETAIL_DEAL_VEHICLE_ID
);

export const setPreviousLenderId = createAction(ACTION_TYPES.SET_PREVIOUS_LENDER_ID);

export const deleteVehicle = createAction(ACTION_TYPES.DELETE_VEHICLE);

export const setNewLeadId = createAction(ACTION_TYPES.SET_NEW_LEAD_ID);

export const resetRefreshPayments = createAction(ACTION_TYPES.RESET_REFRESH_PAYMENTS);

export const changeMultiVehicleDeskingStatus = createAction(ACTION_TYPES.CHANGE_MULTI_VEHICLE_DESKING_STATUS);

export const setEcontractFundingStatus = createAction(ACTION_TYPES.SET_EDOC_FEEDBACK);

export const setProgManOverridden = createAction(ACTION_TYPES.SET_PROG_MAN_OVERRIDDEN);

export const updateDefaultDealFNIsOnZipChange = createAction(ACTION_TYPES.DESKING_UPDATE_FNI_ON_ZIP_CHANGE);

export const setDocumentsToHighlightInDealJacket = createAction(ACTION_TYPES.SET_DOCUMENTS_TO_HIGHLIGHT_IN_DEALJACKET);

export const updateOneAccessoryForAllColumns = createAction(ACTION_TYPES.UPDATE_ONE_ACCESSORY_FOR_ALL_COLUMNS);

export const setMandatoryFields = createAction(ACTION_TYPES.UPDATE_MANDATORY_FIELDS);

export const addUpdateDealPayementOSFLender = createAction(ACTION_TYPES.ADD_UPDATE_DEAL_PAYMENT_OSF_LENDER);

export const applyColumnData = createAction(ACTION_TYPES.APPLY_COLUMN_DATA);
export const setResidualOnYearlyMilesChange = createAction(ACTION_TYPES.CHANGE_RESIDUAL_DATA_ON_CHANGE_YEARLY_MILES);
export const setCustomerOnlyInStore = createAction(ACTION_TYPES.SET_CUSTOMERS);
export const setMscanVehicleIdFecthed = createAction(ACTION_TYPES.SET_MSCAN_VEHICLE_ID_FETCHED);
export const saveTaxGroup = createAction(ACTION_TYPES.SAVE_TAXGROUP);
export const setEstimatedDeliveryDate = createAction(ACTION_TYPES.SET_ESTIMATED_DELIVERY_DATE);

export const setTradeInApprovalDetails = createAction(ACTION_TYPES.SET_TRADEIN_APPROVAL_DETAILS);
export const setRebateApprovalDetails = createAction(ACTION_TYPES.SET_REBATE_APPROVAL_DETAILS);
export const setMotabilityLead = createAction(ACTION_TYPES.SET_MOTABILITY_TYPE);

export const updateTradeInMediaDetails = createAction(ACTION_TYPES.UPDATE_TRADE_IN_MEDIA_DETAILS);
export const setTradeInMediaDetails = createAction(ACTION_TYPES.SET_TRADE_IN_MEDIA_DETAILS);

export const makeDealDirty = () => async (dispatch, getState) => {
  const state = getState()[BASE_REDUCER_KEY];
  const isDealDirty = _get(state, 'desking.isDirty');
  if (!isDealDirty) dispatch(setDealDirtyStatus(true));
};

export const saveDeskingPaymentDetailsV3 = isImbalanceInContracts => async (dispatch, getState) => {
  const state = getState()[BASE_REDUCER_KEY];
  const deal = _get(state, 'desking.deal.data');
  const dealNumber = _get(state, 'desking.deal.data.dealNumber');
  const paymentDetails = getPaymentDetailsForState(getState);
  const dealStatus = DealReader.getDealStatus(deal);

  await dispatch(
    setPaymentDetailsInDeal({
      paymentDetails,
    })
  );

  const { response, error } = await DeskingAPI.savePaymentDetailsV3(dealNumber, {
    vehicleList: paymentDetails,
    ...((dealStatus === DEAL_STATUS.CLOSED_OR_SOLD ||
      dealStatus === DEAL_STATUS.BOOKED ||
      dealStatus === DEAL_STATUS.PRE_CLOSED) && {
      recalculatePayments: _isNil(isImbalanceInContracts) ? state.desking.canCallMarketScan : isImbalanceInContracts,
    }),
  });

  if (DynamicPropertyHelper.isRestrictCRMEventFlowEnabled()) {
    await dispatch(setDoNotSendCrmUpdateEventFlag(false));
  }

  const newDeal = _get(response, 'deal');
  const mandatoryReviewFields = _get(response, 'mandatoryFieldsValidationResponse');

  dispatch({
    type: ACTION_TYPES.UPDATE_MANDATORY_FIELDS,
    payload: mandatoryReviewFields,
  });
  dispatch(setDealDirtyStatus(false));
  return { response: newDeal, error };
};

export const saveCIAMinCustomerData = data => async (dispatch, getState) => {
  const { CIAM_ID, CUSTOMER_ID, EUSER_IDENT } = data;
  const state = getState()[BASE_REDUCER_KEY];
  const customers = _get(state, 'desking.deal.data.customers');
  const newCustomersData = customers.map(customer =>
    customer.customerId === CUSTOMER_ID ? { ...customer, ciamId: CIAM_ID, euserIdent: EUSER_IDENT } : customer
  );
  await dispatch({
    type: ACTION_TYPES.SET_CUSTOMERS,
    payload: newCustomersData,
  });
  if (!_isEmpty(CIAM_ID) && !_isEmpty(EUSER_IDENT)) {
    const dealNumber = _get(state, 'desking.deal.data.dealNumber');
    const newDealObject = await _fetchDeal(dealNumber);
    if (newDealObject) {
      await dispatch([...getUpdateStoreDetailsActionList(newDealObject)]);
    }
  }
};

export const saveUpdatedDeliveryDate = data => async dispatch => {
  dispatch({
    type: ACTION_TYPES.SET_ESTIMATED_DELIVERY_DATE,
    payload: data,
  });
};

export const saveDeskingPaymentDetails =
  ({ dealVehicleId }) =>
  async (dispatch, getState) => {
    const state = getState()[BASE_REDUCER_KEY];
    const deal = _get(state, 'desking.deal.data');
    const dealNumber = _get(state, 'desking.deal.data.dealNumber');
    const mappedDownpaymentDetails = getFormattedDeskingDetailsForServer(getState);
    const rebateProvider = DealReader.getRebateProviderType(deal);
    const deliveryCode = DealReader.getDeliveryCode(deal);

    const paymentDetails = getPaymentDetailsForState(getState);

    await dispatch(setPaymentDetailsInDeal({ paymentDetails }));

    const { response, error } = await DeskingAPI.savePaymentDetails(dealNumber, {
      dealVehicleId,
      deskingpaymentDetails: mappedDownpaymentDetails,
      rebateProvider,
      deliveryCode,
    });

    const newDeal = _get(response, 'deal');
    const mandatoryReviewFields = _get(response, 'mandatoryFieldsValidationResponse');

    dispatch({
      type: ACTION_TYPES.UPDATE_MANDATORY_FIELDS,
      payload: mandatoryReviewFields,
    });

    return { response: newDeal, error };
  };

export const getRebateRegionIds = (payload, rebateFilters) => async dispatch => {
  const response = await DeskingAPI.getRebateRegionIds(payload);
  const vehicleRebateRegions = _get(response, 'rawResponse.data.vehicleRebateRegions') || [];
  let { regionId } = rebateFilters;
  if (!_find(vehicleRebateRegions, ({ RegionID }) => getNumber(regionId) === getNumber(RegionID))) {
    // so that older/invalid RegionID is not sent in list rebates request
    regionId = 0;
  }

  dispatch(setRebateFilters({ regionId, vehicleRebateRegions }));
  return regionId;
};

export const setCommissionDetails = requestpayload => async dispatch => {
  const { dealNumber, data } = requestpayload;
  const { response } = await DeskingAPI.saveCommissions(dealNumber, data);
  if (!_isEmpty(response)) {
    const resolvedDeal = await resolver.getResolvedData(ENTITIES.DEALS, [response]);
    const assignee = _get(resolvedDeal, '0.assignee');
    const commissions = _get(resolvedDeal, '0.commissions');
    const lookupUsers = _get(resolvedDeal, '0._lookup');
    const version = _get(resolvedDeal, '0.version');

    dispatch({
      type: ACTION_TYPES.SET_COMMISSION_DETAILS,
      payload: { commissions, assignee, lookupUsers, version },
    });
    return resolvedDeal;
  }
  return EMPTY_OBJECT;
};

export const setAssigneeList = payload => async (dispatch, getState) => {
  const { dealNumber, assignees } = payload;
  const deal = _get(getState()[BASE_REDUCER_KEY], ['desking', 'deal', 'data']);
  const oldAssignees = _get(deal, 'assignee');
  await dispatch({
    type: ACTION_TYPES.SET_COMMISSION_DETAILS,
    payload: { assignee: assignees },
  });
  const { response } = await DeskingAPI.updateAssignees(dealNumber, assignees);
  if (DealerPropertyHelper.isExternalDmsEnabled()) dispatch(fetchDealSyncMappingWarning(dealNumber));

  if (response) {
    const resolvedDeal = await resolver.getResolvedData(ENTITIES.DEALS, [response]);

    const assignee = _get(resolvedDeal, '0.assignee');
    const commissions = _get(resolvedDeal, '0.commissions');
    const lookupUsers = _get(resolvedDeal, '0._lookup');
    const version = _get(resolvedDeal, '0.version');

    if (!_isEmpty(assignee)) {
      dispatch({
        type: ACTION_TYPES.SET_COMMISSION_DETAILS,
        payload: { assignee, commissions, lookupUsers, version },
      });
      return { assignee, commissions, lookupUsers };
    }
    dispatch({
      type: ACTION_TYPES.SET_COMMISSION_DETAILS,
      payload: { assignee: oldAssignees },
    });
  }

  return EMPTY_OBJECT;
};

export const addRebatesInDesking = payload => dispatch =>
  dispatch({ type: ACTION_TYPES.ADD_REBATES_IN_DESKING, payload });

export const modifyWaiverRebateInDesking = () => dispatch =>
  dispatch({ type: ACTION_TYPES.MODIFY_WAIVER_REBATE_IN_DESKING });

export const setValidRebatesInColumn = payload => dispatch =>
  dispatch({ type: ACTION_TYPES.SET_VALID_REBATES_IN_COLUMN, payload });

export const modifyFirstPaymentWaiver = payload => dispatch =>
  dispatch({ type: ACTION_TYPES.MODIFY_FIRST_PAYMENT_WAIVER, payload });

export const modifyFPWRelatedKeys = payload => dispatch =>
  dispatch({ type: ACTION_TYPES.MODIFY_FPW_RELATED_KEYS, payload });

export const addRebatesInSelectedColumn = payload => dispatch =>
  dispatch({ type: ACTION_TYPES.ADD_REBATES_IN_SELECTED_COLUMN, payload });

export const setRebateFilters = payload => (dispatch, getState) => {
  const rebateFilters = _get(getState()[BASE_REDUCER_KEY], 'desking.deal.data.rebateFilters');

  dispatch({
    type: ACTION_TYPES.SET_REBATE_FILTERS,
    payload: { ...rebateFilters, ...payload },
  });
};

export const feeBulkLookUp = payload => async dispatch => {
  const { response } = await DeskingAPI.feeBulkLookUp(payload);
  if (response) {
    dispatch({
      type: ACTION_TYPES.ON_FEE_BULK_LOOKUP_SUCCESS,
      payload: response,
    });
    return {
      response,
    };
  }
  return {};
};

export const feeInitialLookupForSetupConfig = payload => async (dispatch, getState) => {
  const defaultFees = _get(getState()[BASE_REDUCER_KEY], 'desking.defaultFees');
  if (!_isEmpty(defaultFees)) return { response: defaultFees };

  const { response } = await DeskingAPI.feeBulkLookUp(payload);
  if (!_isEmpty(response)) {
    dispatch({
      type: ACTION_TYPES.DESKING_SAVE_DEFAULT_FEE,
      payload: response,
    });
  }
  return {
    response,
  };
};

export const getVIfeesFromFeesSetup = payload => async (dispatch, getState) => {
  const payloadVehicleFeeCodes = _get(payload, 'FEE.ids', []);
  const viSourceFees = _get(getState()[BASE_REDUCER_KEY], 'desking.viSourceFees');
  const existingVIfeeCodes = _map(viSourceFees, viFee => _get(viFee, 'id'));

  // Check if payloadVehicleFeeCodes match existingVIfeeCodes
  const sortedPayloadCodes = _sortBy(payloadVehicleFeeCodes);
  const sortedExistingCodes = _sortBy(existingVIfeeCodes);

  if (_isEqual(sortedPayloadCodes, sortedExistingCodes)) {
    // All values match, return cached viSourceFees
    return {
      response: viSourceFees,
    };
  }

  // Differences found, call the API
  const { response } = await DeskingAPI.feeBulkLookUp(payload);
  if (!_isEmpty(response)) {
    dispatch({
      type: ACTION_TYPES.DESKING_SAVE_VI_SOURCE_FEES,
      payload: response,
    });
    return {
      response,
    };
  }
  return {};
};

export const feeSearch = payload => async dispatch => {
  const { response } = await DeskingAPI.feeSearch(payload);
  if (response) {
    dispatch({
      type: ACTION_TYPES.ON_FEE_SEARCH_SUCCESS,
      payload: response,
    });
    return {
      response,
    };
  }
  return {};
};

export const getFNIAttributesForSetupConfig = payload => async () => {
  const { response } = await DeskingAPI.getFNIAttributes(payload);
  if (response) {
    return {
      response,
    };
  }
  return {};
};

export const getFeeTaxRegimes = () => async () => {
  const { response } = await DeskingAPI.feeTaxRegimes();
  if (response) {
    return { response };
  }
  return {};
};

export const getFNIProducts = () => async (dispatch, getState) => {
  const existingFNIProducts = _get(getState()[BASE_REDUCER_KEY], 'desking.fniProductsList');
  if (!_isEmpty(existingFNIProducts)) return _values(existingFNIProducts);
  const fniProductsList = await DeskingAPI.getFNIProducts();
  if (!_isEmpty(fniProductsList)) {
    dispatch({
      type: ACTION_TYPES.FNI_GET_PRODUCTS_LIST,
      payload: fniProductsList,
    });
    return fniProductsList;
  }
  return [];
};

export const fetchContractDetails = dealNumber => async dispatch => {
  const response = await DeskingAPI.fetchContractDetails(dealNumber);

  if (response) {
    dispatch({
      type: ACTION_TYPES.FETCH_CONTRACT,
      payload: response,
    });
  }
  return response;
};

export const updateContract =
  (dealNumber, { key, ...updatedContract }) =>
  async (dispatch, getState) => {
    const dealFollowUpByDate = getDealFollowUpByDate(getState()[BASE_REDUCER_KEY]) || [];
    const dealFollowUpForPayment = getDealFollowUpForPayment(getState()[BASE_REDUCER_KEY]) || [];
    const payload = {
      dealFollowUpByDate,
      dealFollowUpForPayment,
    };
    let updatedPayloadValue;

    switch (key) {
      case 'dealFollowUpByDate':
        updatedPayloadValue = produce(payload, draft => {
          const index = draft[key].findIndex(data => data.type === updatedContract.type);

          if (index > -1) draft[key][index] = updatedContract;
          else draft[key].push(updatedContract);
        });
        break;

      case 'dealFollowUpForPayment':
        updatedPayloadValue = produce(payload, draft => {
          let index = -1;
          if (updatedContract.type === RECEIVABLES_LABEL.REBATE) {
            index = draft[key].findIndex(data => _toString(data.rebateId) === _toString(updatedContract.rebateId));
          } else {
            index = draft[key].findIndex(data => data.type === updatedContract.type);
          }

          if (index > -1) draft[key][index] = updatedContract;
          else draft[key].push(updatedContract);
        });
        break;

      default:
        updatedPayloadValue = payload;
        break;
    }

    const response = await DeskingAPI.updateContract(dealNumber, updatedPayloadValue);

    if (response) {
      dispatch({
        type: ACTION_TYPES.FETCH_CONTRACT,
        payload: response,
      });
    }
    return response;
  };

export const resolveDealJacket = documents => {
  if (_isEmpty(documents)) return Promise.resolve(EMPTY_ARRAY);
  return resolver.getResolvedData(ENTITIES.DEAL_JACKET, documents);
};

export const fetchDealJacket = dealNumber => async dispatch => {
  let response = EMPTY_OBJECT;
  if (!_isEmpty(dealNumber)) {
    response = await DeskingAPI.fetchDealJacket(dealNumber);
  }

  const { documents, showArchive, gdprMetadata = EMPTY_OBJECT } = response || EMPTY_OBJECT;
  let resolvedData = await resolveDealJacket(documents);
  if (resolvedData) {
    if (!DealerPropertyHelper.isCompletionCertificateEnabled()) {
      const { documentsWithoutCompletionCertificates, completionCertificates } =
        separateCompletionCertsFromDealJacket(resolvedData);

      resolvedData = documentsWithoutCompletionCertificates;
      dispatch({
        type: ACTION_TYPES.SET_HIDDEN_COMPLETION_CERTIFICATES,
        payload: completionCertificates,
      });
    }
    dispatch({
      type: ACTION_TYPES.UPDATE_DEAL_JACKET_SUCCESS,
      payload: { resolvedData, gdprConfig: getGDPRConfig(gdprMetadata) },
    });
  }

  dispatch({
    type: ACTION_TYPES.SET_SHOW_ARCHIVED_DOCUMENTS_IN_DEALJACKET,
    payload: showArchive,
  });

  return resolvedData;
};

export const fetchLenderMetaData = deal => async dispatch => {
  const lenderPayload = getLenderMetaDataAPIPayload(deal);
  const response = await DeskingAPI.lenderMetadata(lenderPayload);
  const lenderMetadata = _get(response, ['rawResponse', 'data']);
  const isExpired = isLenderProgramExpired(lenderMetadata?.data, deal);
  dispatch({
    type: ACTION_TYPES.SET_LENDER_PROGRAM_EXPIRED_STATUS,
    payload: lenderMetadata?.data,
  });
  return isExpired;
};

export const updateDealJacket = (dealNumber, payload) => async (dispatch, getState) => {
  const filteredDocuments = _filter(payload.documents, doc => doc.category !== DEAL_JACKET.PACKAGE);
  const hiddenCompletionCertificates = _get(
    getState()[BASE_REDUCER_KEY],
    'desking.hiddenCompletionCertificates',
    EMPTY_ARRAY
  );
  const shouldUpdateState = _get(payload, 'shouldUpdateState', true);
  const { documents, gdprMetadata } =
    (await DeskingAPI.updateDealJacket(dealNumber, {
      documents: [...filteredDocuments, ...hiddenCompletionCertificates],
    })) || EMPTY_OBJECT;
  let resolvedData = await resolveDealJacket(documents);
  if (resolvedData && shouldUpdateState) {
    if (!DealerPropertyHelper.isCompletionCertificateEnabled()) {
      const { documentsWithoutCompletionCertificates, completionCertificates } =
        separateCompletionCertsFromDealJacket(resolvedData);

      resolvedData = documentsWithoutCompletionCertificates;
      dispatch({
        type: ACTION_TYPES.SET_HIDDEN_COMPLETION_CERTIFICATES,
        payload: completionCertificates,
      });
    }
    dispatch({
      type: ACTION_TYPES.UPDATE_DEAL_JACKET_SUCCESS,
      payload: { resolvedData, gdprConfig: getGDPRConfig(gdprMetadata) },
    });

    return true;
  }
  return false;
};

export const mergeAndAddToPackage = (dealNumber, payload) => async dispatch => {
  const { packages } = (await DeskingAPI.mergeAndAddToPackage(dealNumber, payload)) || EMPTY_OBJECT;
  if (packages) dispatch({ type: ACTION_TYPES.SET_PACKAGE_DOCS, payload: packages });
  return packages;
};

export const updateCustomStatus =
  (dealNumber, oldSubStatusId, oldSubStatus, newSubStatusId, newSubStatus) => async dispatch => {
    dispatch({
      type: ACTION_TYPES.UPDATE_CUSTOM_STATUS_SUCCESS,
      payload: { subStatusId: newSubStatusId, subStatus: newSubStatus },
    });
    const response = await DeskingAPI.updateCustomStatusInDeal(dealNumber, newSubStatusId, newSubStatus);
    if (_get(response, ['rawResponse', 'data', 'status']) !== API_STATUS.SUCCESS) {
      dispatch({
        type: ACTION_TYPES.UPDATE_CUSTOM_STATUS_SUCCESS,
        payload: { subStatusId: oldSubStatusId, subStatus: oldSubStatus },
      });

      toaster('error', MESSAGES.UPDATE_CUSTOM_STATUS_FAILURE);
    }
    const mandatoryReviewFields = _get(response, 'response.mandatoryFieldValidationResponse') || {};
    if (mandatoryReviewFields) {
      dispatch({
        type: ACTION_TYPES.UPDATE_MANDATORY_FIELDS,
        payload: mandatoryReviewFields,
      });
    }
  };

export const saveCustomer = (dealNumber, payload) => async dispatch => {
  const { customers } = payload || EMPTY_ARRAY;
  if (!_isEmpty(customers)) dispatch({ type: ACTION_TYPES.SET_CUSTOMERS, payload: customers });
  const response = await DeskingAPI.saveCustomer(dealNumber, payload);
  const newDealInfo = _get(response, 'response.deal');
  const mandatoryReviewFields = _get(response, 'response.mandatoryFieldsValidationResponse') || {};
  const warning = _get(response, 'response.warning');
  if (!_isEmpty(warning)) {
    toaster('error', warning);
  }

  if (newDealInfo) {
    if (mandatoryReviewFields) {
      dispatch({
        type: ACTION_TYPES.UPDATE_MANDATORY_FIELDS,
        payload: mandatoryReviewFields,
      });
    }
    return newDealInfo;
  }
  return null;
};

export const printedSheetsCount = payload => async () => {
  await DeskingAPI.printedSheetsCount(payload);
};

export const getPrintedInvoicesCount = mediaIds => async () => DeskingAPI.getPrintedInvoicesCount(_castArray(mediaIds));

export const setPrintedInvoicesCount = payload => async () => DeskingAPI.setPrintedInvoicesCount(payload);

export const printDocuments =
  (payload, printWithReporting = false) =>
  async () => {
    const printAction = printWithReporting ? DeskingAPI.printDocumentsWithReporting : DeskingAPI.printDocuments;
    const { response, error } = await printAction(payload);

    if (response) {
      toaster('success', __('Printing'));
      return true;
    }
    const errorMessage = _get(error, 'detail.displayMessage') || '';
    errorCallback(errorMessage);
    return false;
  };

export const printDocumentsCore = (payload, mediaId) => async (dispatch, getState) => {
  const showPrintOptions = _get(
    getState(),
    'skeleton.applicationPreference.data.printerPreferences.showPrintOptions',
    false
  );
  const { response, error } = !showPrintOptions
    ? await DeskingAPI.printDocumentsV2(payload)
    : await DeskingAPI.printDocumentsCore(payload);

  if (response) {
    toaster('success', __('Printing'));
    dispatch(updateDocumentPrintStatus(payload, mediaId));
    return true;
  }
  const errorMessage = _get(error, 'detail.displayMessage') || '';
  errorCallback(errorMessage);
  return false;
};

export const getGLBalance = vehicleId => async () => {
  const result = await VehicleAPI.getGLBalance(vehicleId);
  const glBalance = _get(result, ['response', 'glBalance']) || 0;
  return glBalance;
};

/**
 * Updates in deal object(both in redux and db) only whatever is need
 * implements optimistic UI/revert changes in redux in case of API error
 * @param {object} payload: subset of deal object
 */
export const patchDeal = payload => async (dispatch, getState) => {
  const deal = _get(getState()[BASE_REDUCER_KEY], 'desking.deal.data');
  if (_isEmpty(deal)) return errorCallback();
  const { dealNumber } = deal;
  dispatch(setDeal({ ...deal, ...payload }));
  const response = await DeskingAPI.patchDeal(dealNumber, payload);
  if (_get(response, ['rawResponse', 'data', 'status']) !== API_STATUS.SUCCESS) {
    errorCallback();
    dispatch(setDeal(deal));
  }
  dispatch(setDeal({ ...deal, ...payload, version: response?.response?.version }));
  return _get(response, ['rawResponse', 'data']);
};

export const sendToSales = payload => async (dispatch, getState) => {
  const deal = _get(getState()[BASE_REDUCER_KEY], 'desking.deal.data');
  if (_isEmpty(deal)) return errorCallback();
  const { dealNumber } = deal;
  dispatch(setDeal({ ...deal, ...payload }));
  const response = await DeskingAPI.sendToSales(dealNumber, payload);
  if (_get(response, ['rawResponse', 'data', 'status']) !== API_STATUS.SUCCESS) {
    errorCallback();
    dispatch(setDeal(deal));
  }
  const newDealObject = await _fetchDeal(dealNumber);
  const vehicleId = DealReader.getPrimaryVehicleId(deal);
  if (newDealObject) {
    await dispatch(setDeal(newDealObject));
    await dispatch(getVehiclesById([vehicleId]));
  }
  return _get(response, ['rawResponse', 'data']);
};

export const getFNIMenuPackages = dealNumber => async () => {
  const response = await DeskingAPI.getFNIMenuPackages(dealNumber);
  if (response) {
    return response;
  }
  return null;
};

export const setPackageDocs = dealNumber => async dispatch => {
  const response = await DeskingAPI.getPackageDocuments(dealNumber);
  if (_isEmpty(response)) {
    return EMPTY_ARRAY;
  }
  await dispatch({
    type: ACTION_TYPES.SET_PACKAGE_DOCS,
    payload: response.packages || EMPTY_ARRAY,
  });

  return response.packages;
};

export const moveDocumentsToPackage = payload => async dispatch => {
  const { dealNumber } = payload;
  const response = await DeskingAPI.moveDocumentsToPackage(payload);
  dispatch(setPackageDocs(dealNumber));
  return response;
};

export const deleteFinanceDocs = financeDocIdsToDelete => async () => {
  const deleteFinanceDocsRes = await DealJacketApis.deleteFinanceDocs(financeDocIdsToDelete);
  if (!_get(deleteFinanceDocsRes, 'response.data') === 'success') {
    toaster('error', __(MESSAGES.DELETE_DOCUMENT_FAILED));
  } else {
    toaster('success', MESSAGES.DEAL_JACKET_UPDATE_SUCCESS);
  }
};

export const setSppPackageDocs = dealNumber => async dispatch => {
  const response = await DeskingAPI.getSppPackageDocuments(dealNumber);
  if (_isEmpty(response)) {
    return;
  }
  await dispatch({
    type: ACTION_TYPES.SET_SPP_PACKAGE_DOCS,
    payload: response.packages || EMPTY_ARRAY,
  });
};

export const moveDocumentsToSppPackage = payload => async dispatch => {
  const { dealNumber } = payload;
  const response = await DeskingAPI.moveDocumentsToSppPackage(payload);
  dispatch(setSppPackageDocs(dealNumber));
  return response;
};

export const setScanData = payload => async dispatch => {
  await dispatch({ type: ACTION_TYPES.SET_SCAN_DOCS, payload });
};

export const setScanDocs = deal => async (dispatch, getState) => {
  let data = _get(getState()[BASE_REDUCER_KEY], 'desking.scanDocuments.docsetData');
  const scanJobMeta = ScanDocHelpers.getCreateDocSetRequest(DealReader.getDealId(deal), DealReader.getDealNumber(deal));

  if (_isEmpty(data)) {
    const scanListRes = await ScanService.fetchScanList(
      ScanDocHelpers.getDealDocSetSearchRequest(DealReader.getDealNumber(deal))
    );
    data = _get(scanListRes, 'response.hits[0]') || EMPTY_OBJECT; // hits[0]: since deal docSet is unique
    if (_isEmpty(data.id)) {
      const createDocSetRes = await ScanService.createDocSet(scanJobMeta);
      data = _get(createDocSetRes, 'response') || EMPTY_OBJECT;
      if (_isEmpty(data.id)) return;
    }
  }
  const getDocSetDataRes = await ScanService.fetchScanMetaDataDetails(data.id);
  const documents = _get(getDocSetDataRes, 'response') || EMPTY_ARRAY;
  await dispatch(setScanData({ documents, docsetData: data }));

  return documents;
};

export const deleteScanDocs = (scanDocIdsToDelete, deal) => async dispatch => {
  const deleteScanDocsRes = await ScanService.deleteDocuments(scanDocIdsToDelete);
  if (!_get(deleteScanDocsRes, 'response.data') === 'success') {
    toaster('error', __(MESSAGES.DELETE_DOCUMENT_FAILED));
  } else {
    dispatch(setScanDocs(deal));
    toaster('success', MESSAGES.DEAL_JACKET_UPDATE_SUCCESS);
  }
};

export const removeCashDeficiencyAccessoryForColumnIds = columnIds => async (dispatch, getState) => {
  const deskingPaymentDetails = getDeskingPaymentDetails(getState()[BASE_REDUCER_KEY]);
  const allPayload = columnIds.map(columnId => {
    const currentColumn = ColumnReader.getColumnDataById(deskingPaymentDetails, columnId);
    const existingAccessories = ColumnReader.getColumnAccessories(currentColumn);
    const accessoryWithoutCashDefAccessory = removeCashDeficiencyAccessory(existingAccessories);

    return {
      accessories: accessoryWithoutCashDefAccessory,
      columnId,
    };
  });

  return dispatch(saveMultipleAccessories(allPayload));
};

export const addCashDeficiencyToAccessories =
  ({ columnIds, downPaymentId }) =>
  async (dispatch, getState) => {
    const deal = getDeal(getState()[BASE_REDUCER_KEY]);
    const deskingPaymentDetails = getDeskingPaymentDetails(getState()[BASE_REDUCER_KEY]);
    const taxAndFeeMetaData = getTaxAndFeeMetadata(getState()[BASE_REDUCER_KEY]);
    const allPayload = columnIds.map(columnId => {
      const currentColumn = ColumnReader.getColumnDataById(deskingPaymentDetails, columnId);

      const cashDef = getNumber(ColumnReader.getCashDeficiencyForDownpaymentId(currentColumn, downPaymentId));
      const collectSalesTaxUpfront = DealReader.shouldCollectSalesTaxUpfront(
        deal,
        ColumnReader.getColumnTaxAndZipCodeType(currentColumn),
        taxAndFeeMetaData
      );

      let accessoryValue = 0;
      if (cashDef) {
        accessoryValue = getNumber(
          getFinalCashDeficiencyForDownpaymentId(currentColumn, downPaymentId, collectSalesTaxUpfront)
        );
      }
      const cashDefAccessory = getAccessoryForCashDeficiency(accessoryValue);
      const existingAccessories = ColumnReader.getColumnAccessories(currentColumn);
      const accessoryWithoutCashDefAccessory = removeCashDeficiencyAccessory(existingAccessories);

      if (cashDef) {
        accessoryWithoutCashDefAccessory.push(cashDefAccessory);
      }
      return {
        accessories: accessoryWithoutCashDefAccessory,
        columnId,
      };
    });

    return dispatch(saveMultipleAccessories(allPayload));
  };

export const getDefaultPlanCodesForPenPostAsync = payload => async (dispatch, getState) => {
  const defaultPlanCodes = _get(getState()[BASE_REDUCER_KEY], ['desking', 'defaultPlanCodes']) || EMPTY_ARRAY;

  if (!_isEmpty(defaultPlanCodes)) {
    return defaultPlanCodes;
  }
  const response = await DeskingAPI.getDefaultPlanCodesForPen(payload);

  if (response) {
    dispatch({
      type: ACTION_TYPES.GET_DEFAULT_PLAN_CODES,
      payload: response,
    });
    return response;
  }
  return [];
};

export const getDefaultPlanCodesForPenPostAsyncV2 = payload => async (dispatch, getState) => {
  const defaultPlanCodes = _get(getState()[BASE_REDUCER_KEY], ['desking', 'defaultPlanCodes']) || EMPTY_ARRAY;

  if (!_isEmpty(defaultPlanCodes)) {
    return defaultPlanCodes;
  }
  const response = await DeskingAPI.getDefaultPlanCodesForPenV2(payload);

  if (response) {
    dispatch({
      type: ACTION_TYPES.GET_DEFAULT_PLAN_CODES,
      payload: response,
    });
    return response;
  }
  return EMPTY_ARRAY;
};

export const getDefaultPlanCodesForPenAsync = payload => async (dispatch, getState) => {
  const defaultPlanCodes = _get(getState()[BASE_REDUCER_KEY], ['desking', 'defaultPlanCodes']) || EMPTY_ARRAY;

  if (!_isEmpty(defaultPlanCodes)) {
    return defaultPlanCodes;
  }
  const response = await DeskingAPI.getDefaultPlanCodesForPenAsync(payload);
  return response;
};

export const getDefaultPlanCodesForPenAsyncV2 = payload => async (dispatch, getState) => {
  const defaultPlanCodes = _get(getState()[BASE_REDUCER_KEY], ['desking', 'defaultPlanCodes']) || EMPTY_ARRAY;

  if (!_isEmpty(defaultPlanCodes)) {
    return defaultPlanCodes;
  }
  const response = await DeskingAPI.getDefaultPlanCodesForPenAsyncV2(payload);
  return response;
};

export const voidContract = (dealNumber, productToVoid) => async (dispatch, getState) => {
  const { source } = productToVoid || EMPTY_OBJECT;
  const isPenSimilarProduct = isFlowSimilarToPEN(source);
  if (!isPenSimilarProduct) return false;

  const productContractInfos =
    _get(getState()[BASE_REDUCER_KEY], 'desking.deal.data.penInfo.productContractInfos') || EMPTY_ARRAY;

  const productId = _get(productToVoid, 'productId');
  const providerId = _get(productToVoid, 'providerId');

  const contractForProductToVoid =
    productContractInfos.find(contractInfo => {
      const itemProductId = _get(contractInfo, 'productId');
      const itemProviderId = _get(contractInfo, 'providerId');
      return itemProductId === productId && providerId === itemProviderId;
    }) || EMPTY_OBJECT;
  // void only if contract exists
  if (!contractForProductToVoid.contractNumber) return false;

  const response = await DeskingAPI.voidContract({
    dealNumber,
    financeAndInsurance: productToVoid,
  });

  const voidSuccess = _get(response, 'voidSuccess') || false;

  if (voidSuccess) {
    dispatch({
      type: ACTION_TYPES.REMOVE_PEN_CONTRACT_INFO_DEAL,
      payload: { productId, providerId },
    });
  }
  return voidSuccess;
};

export const voidContractInBulk = payload => async dispatch => {
  const response = await DeskingAPI.voidContractInBulk(payload);
  const penInfo = _get(response, 'penInfo');
  if (penInfo) {
    dispatch({
      type: ACTION_TYPES.SET_PEN_INFO,
      payload: penInfo,
    });
  }
};

export const updateBaseEmiAmount = () => async dispatch => {
  const payload = dispatch(getSavePmtDetailsCalcEnginePayload());
  const { response, error } = await DeskingAPI.updateBaseEmiAmount(payload);
  if (error) {
    errorCallback();
  }
  dispatch(setDeal(_get(response, 'deal')));
};

export const updateBaseEmiAmountV2 = payload => async dispatch => {
  const { response, error } = await DeskingAPI.updateBaseEmiAmountV2(payload);
  if (error) {
    errorCallback();
  }
  dispatch(setDeal(_get(response, 'deal')));
};

export const getVehicleEquipments = () => async () => {
  const response = await DeskingAPI.getVehicleEquipments();
  const vehicleEquipments = _get(response, 'rawResponse.data.data.vehicleEquipments') || EMPTY_ARRAY;
  return vehicleEquipments;
};

export const getContractsValidations = dealNumber => async dispatch => {
  const response = await DeskingAPI.contractValidation(dealNumber);

  if (response) {
    dispatch({
      type: ACTION_TYPES.GET_CONTRACTS_VALIDATIONS,
      payload: response,
    });
  }
  return response;
};

export const forceContracting = (dealNumber, extraPayload) => async (dispatch, getState) => {
  const response = await DeskingAPI.forceContracting(dealNumber);
  if (response) {
    dispatch({
      type: ACTION_TYPES.SET_PEN_INFO,
      payload: _get(response, 'response.penInfo') || EMPTY_OBJECT,
    });
  }
  const deal = _get(getState()[BASE_REDUCER_KEY], 'desking.deal.data');
  const currentDealNumber = DealReader.getDealNumber(deal);
  if (dealNumber !== currentDealNumber) {
    return;
  }
  const apiResponse = await DeskingAPI.updateDeal(currentDealNumber, {
    ...deal,
    ...extraPayload,
    penInfo: _get(response, 'response.penInfo') || EMPTY_OBJECT,
  });
  const { response: dealResponse, error } = apiResponse || EMPTY_OBJECT;
  const updatedDealInfo = _get(dealResponse, 'deal');
  const mandatoryReviewFields = _get(dealResponse, 'mandatoryFieldsValidationResponse') || EMPTY_OBJECT;
  if (_isEmpty(updatedDealInfo)) {
    toaster('error', getAPIError(error) || MESSAGES.DEAL_UPDATION_FAILED);
  }
  dispatch({
    type: ACTION_TYPES.UPDATE_MANDATORY_FIELDS,
    payload: mandatoryReviewFields,
  });
};

export const updateLgmModelVariantInDeal =
  ({ dealNumber, vehicles }) =>
  async (dispatch, getState) => {
    const deal = _get(getState()[BASE_REDUCER_KEY], 'desking.deal.data');
    const apiResponse = await DeskingAPI.updateDeal(dealNumber, {
      ...deal,
      vehicles,
    });
    const { response: dealResponse } = apiResponse || EMPTY_OBJECT;
    const updatedDealInfo = _get(dealResponse, 'deal');
    dispatch(setDeal(updatedDealInfo));
  };

export const unwindDeal = payload => async () => {
  const response = await DeskingAPI.unwindDeal(payload);
  return response;
};

export const getBonusCode = payload => async () => {
  const response = await DeskingAPI.getBonusCode(payload);
  return response;
};

export const getGMCardDetails = payload => async () => {
  const response = await DeskingAPI.getGMCardDetails(payload);
  return response;
};

export const removeContractInfo = payload => async dispatch => {
  const response = await DeskingAPI.removeContractInfo(payload);

  if (response) {
    dispatch({
      type: ACTION_TYPES.SET_PEN_INFO,
      payload: _get(response, 'penInfo') || {},
    });
  }
};

export const getSheetLevelFnIMenuConfig = async (assetType, dealNumber) => {
  const dealLevelDefaults = await CommonAPI.getDealLevelSheetConfig(
    dealNumber,
    PRINT_ASSET_TYPES_VS_SHEET_TYPE[assetType]
  );
  return dealLevelDefaults?.menuConfig;
};

const getImgFromMediaId = async mediaId => {
  if (mediaId) {
    const { data } = await getSignedURLsWithMediaMap([mediaId]);
    return data[mediaId];
  }
};

const checkAttributeMapping = (field, attributeMappingField) =>
  attributeMappingField ? field === attributeMappingField : true;

const selectDealershipInfoFromAddressMappings = (dealershipInfo, deal, siteId) => {
  const primaryVehicle = _find(deal?.vehicles, vehicle => vehicle.primaryVehicle);
  const vehicleType = _get(primaryVehicle, 'vehicleType');
  const make = _get(primaryVehicle, 'make');
  const locationCode = _get(primaryVehicle, 'locationCode');
  const selectedDealershipInfo = _find(dealershipInfo, row => {
    const attributes = _get(row, 'attributesMapping');
    if (
      checkAttributeMapping(siteId, attributes?.SITE_SOLD) &&
      checkAttributeMapping(vehicleType, attributes?.STOCK_TYPE) &&
      checkAttributeMapping(make, attributes?.MAKE) &&
      checkAttributeMapping(locationCode, attributes?.LOCATION_CODE)
    ) {
      return true;
    }
    return false;
  });
  return selectedDealershipInfo;
};

const getRequiredDealershipInfo = async (useDealershipAddressInformation, deal, siteId, dealerInfo) => {
  const timeZone = _get(dealerInfo, 'timeZone');
  if (useDealershipAddressInformation) {
    const response = await FormSetupAPI.getDealershipInformation();
    const dealershipInfo = _get(response, 'addressRules[0].addressMappings');
    const selectedDealershipInfo = selectDealershipInfoFromAddressMappings(dealershipInfo, deal, siteId);
    const requiredDealershipInfo =
      selectedDealershipInfo || _get(response, 'addressRules[0].defaultAddressMapping') || {};
    if (requiredDealershipInfo) {
      requiredDealershipInfo.dealerLogo = await getImgFromMediaId(requiredDealershipInfo?.dealerLogo);
    }
    const requiredDealershipInfoWithTimeZone = {
      ...requiredDealershipInfo,
      timeZone: timeZone || null,
    };
    return requiredDealershipInfoWithTimeZone;
  }
  return {
    address: getFormattedDefaultDealerAddress(siteId, dealerInfo),
    county: '',
    city: '',
    zipCode: '',
    state: '',
    dealerLogo: getDealerLogo(siteId, dealerInfo),
    dealershipName: getDealerName(siteId, dealerInfo),
    dealerWebsite: getDealerWebsite(siteId, dealerInfo),
    dealerEmail: getDealerEmail(siteId, dealerInfo),
    dealerPhoneNumber: getDealerPhone(siteId, dealerInfo),
    timeZone: timeZone || null,
  };
};

export const getDealershipInfo = (deal, dealerOemSites) => async (dispatch, getState) => {
  const state = getState()[BASE_REDUCER_KEY];
  const salesSetupInfo = _get(state, ['common', 'dealerConfig']);
  const dealerInfo = _get(state, ['common', 'dealerInfo']);

  const { dealerId } = TEnvReader.userInfo();
  const accessibleDealerSites = dealerOemSites
    ? getSelectedDealerSites(dealerId, dealerOemSites)
    : getAccessibleDealerSites();
  const dealCreatedAtSite = DealReader.getDealCreatedAtSite(deal);
  const siteId = _get(findArrayItem(accessibleDealerSites, 'siteId', dealCreatedAtSite), 'siteId');
  const useDealershipAddressInformation = canUseDealershipAddressInformation(salesSetupInfo);
  const requiredDealershipInfo = await getRequiredDealershipInfo(
    useDealershipAddressInformation,
    deal,
    siteId,
    dealerInfo
  );
  dispatch({
    type: ACTION_TYPES.SET_DEALERSHIP_INFO,
    payload: requiredDealershipInfo,
  });
};

export const getRecapSheetContols = async (assetType, dealNumber) => {
  const dealLevelDefaults = await CommonAPI.getDealLevelSheetConfig(
    dealNumber,
    PRINT_ASSET_TYPES_VS_SHEET_TYPE[assetType]
  );
  const fields = _get(dealLevelDefaults, 'fields') || EMPTY_ARRAY;
  const mergedDataSetupFormat = mapFieldConfigToFni(fields, assetType);
  const mergedDataDealFormat = mapFniToFieldConfig(mergedDataSetupFormat, assetType, fields);
  return { mergedDataDealFormat, mergedDataSetupFormat };
};

export const generateContract =
  ({ products, dealNumber }) =>
  async () => {
    const responses = await Promise.all(
      products.map(product =>
        DeskingAPI.generateContract({
          dealNumber,
          fnIs: [product],
        })
      )
    );

    const generatedContractsList = _compact(responses);
    // dispatch({
    //   type: ACTION_TYPES.SET_PEN_CONTRACT_INFO_IN_DEAL,
    //   payload: generatedContractsList,
    // });
    return generatedContractsList;
  };

export const getEContractPackages = dealNumber => async dispatch => {
  const econtractDocPackages = await DeskingAPI.getEContractPackages(dealNumber);

  dispatch({
    type: ACTION_TYPES.SET_ECONTRACT_PACKAGES_WITH_SIGNATURES,
    payload: { econtractDocPackages },
  });
  return econtractDocPackages;
};

export const saveEContractPackages = (dealNumber, payload) => async dispatch => {
  const response = await DeskingAPI.saveEContractPackages(dealNumber, payload);
  dispatch({
    type: ACTION_TYPES.SET_ECONTRACT_PACKAGES,
    payload: response,
  });
  return response;
};

export const updatePackages = (dealNumber, payload) => async dispatch => {
  const response = await DeskingAPI.saveEContractPackages(dealNumber, payload);
  await dispatch({
    type: ACTION_TYPES.SET_PACKAGE_DOCS,
    payload: response.packages || EMPTY_ARRAY,
  });
  return response;
};

export const deleteEcontractPackage = (dealNumber, payload) => async dispatch => {
  const response = await DeskingAPI.deleteEcontractPackage(dealNumber, payload);
  if (response) {
    dispatch({
      type: ACTION_TYPES.SET_ECONTRACT_PACKAGES,
      payload: response,
    });
    return response;
  }
};

const _fetchDeal = async dealNumber => {
  const dealInfo = await DeskingAPI.getDealInfo(dealNumber);
  return _get(dealInfo, 'response.deal');
};

export const fetchDeal = dealNumber => async dispatch => {
  const deal = await _fetchDeal(dealNumber);
  if (deal) {
    setDealVersion(deal);
    dispatch(setDealv2(deal));
  }
  return deal;
};

export const addDMSCustomer =
  ({ dealNumber, customers, triggerEvent, primaryDMS }) =>
  async dispatch => {
    if (DMS_WITH_ADD_CUSTOMER_SUPPORT.has(primaryDMS)) {
      const customersForPayload = _map(customers, customer => {
        const { externalCustomerId, ...fieldsForPayload } = customer || EMPTY_OBJECT;
        return fieldsForPayload;
      });
      const { response: customersList } = await DeskingAPI.addCustomerV2({
        primaryDMS: MAP_DMS_TO_API_PATH[primaryDMS],
        customers: customersForPayload,
        triggerEvent,
      });
      await dispatch(
        saveCustomer(dealNumber, {
          customers: customersList,
        })
      );
      return !!customersList;
    }
    const { response } = await DeskingAPI.addCustomer({ dealNumber, customers, triggerEvent });
    const deal = response;
    if (deal) {
      dispatch(setDeal(deal));
    }
    return !!deal;
  };

export const fetchDealWithoutTax = dealNumber => async dispatch => {
  const dealInfo = await DeskingAPI.getDealInfoWithoutTax(dealNumber, { withoutTax: false });
  const deal = _get(dealInfo, 'response.deal');
  if (deal) {
    dispatch(updateStorePaymentDetails(deal));
  }
};

export const fetchDealAndUpdatePaymentDetails = dealNumber => async dispatch => {
  const deal = await _fetchDeal(dealNumber);
  if (deal) {
    dispatch(updateStorePaymentDetails(deal));
    setDealVersion(deal);
    return deal;
  }
};

export const updateDesking = payload => async dispatch => {
  const dealInfo = await dispatch(getUpdateDeskingV3Res(payload));
  if (dealInfo) {
    dispatch(setDeal(dealInfo));
    dispatch(fetchDealSyncMappingWarning(_get(dealInfo, 'dealNumber')));
  }
};

export const setReinitialiseDesking = createActionWithLog(ACTION_TYPES.SET_REINITIALISE_DESKING_FLAG);

export const updateDeskingOnDealTypeChange = (payload, dealNumber) => async dispatch => {
  const dealInfo = await DeskingAPI.updateDeskingOnDealTypeChange(payload, dealNumber);
  if (dealInfo) {
    dispatch(setDeal(dealInfo));
  } else {
    toaster('error', __('Failed to change deal type'));
  }
};

export const setSelectedLenderWhilePushingDealToDesking = payload => async (dispatch, getState) => {
  await dispatch({ type: ACTION_TYPES.SET_SELECTED_LENDER, payload });
  await dispatch(setDealDirtyStatus(true));
  const state = getState()[BASE_REDUCER_KEY];
  const deskingpaymentDetails = _get(state, 'desking.deskingpaymentDetails');
  return deskingpaymentDetails;
};

export const addNewColumnAndGetUpdatedDeskingPaymentInfoDetails = payload => async (dispatch, getState) => {
  await dispatch(addNewColumn(payload));
  const state = getState()[BASE_REDUCER_KEY];
  const deskingpaymentDetails = _get(state, 'desking.deskingpaymentDetails');
  return deskingpaymentDetails;
};

// Its a special case when we have to update dealPayment
// and dealItem together but don't wants to loose any state in between
export const updatePaymentWithItemsGalaxy =
  ({ dealKeysToCache, syncPaymentPayload = {} }, intertermitentLoadingCallback = _noop) =>
  async (dispatch, getState) => {
    const state = getState()[BASE_REDUCER_KEY];
    const desking = _get(state, 'desking');
    const deal = _get(desking, 'deal.data');
    const dealKeysPayload = _reduce(
      dealKeysToCache,
      (acc, key) => ({
        ...acc,
        [key]: _cloneDeep(_get(deal, key)),
      }),
      {}
    );

    await dispatch(syncPaymentDetailsGalaxy(syncPaymentPayload, intertermitentLoadingCallback));
    await dispatch(updateDealItemPaymentDetailsGalaxy(dealKeysPayload, false));
    dispatch(setMarketScanDataFetchingStatus(false));
    dispatch(setDealDirtyStatus(false));
  };

// Its a special case when we have to update dealPayment
// and dealItem together but don't wants to loose any state in between
export const updatePaymentWithItems =
  ({ dealKeysToCache, syncPaymentPayload = {} }) =>
  async (dispatch, getState) => {
    dispatch(setMarketScanDataFetchingStatus(true));
    const state = getState()[BASE_REDUCER_KEY];
    const desking = _get(state, 'desking');
    const deal = _get(desking, 'deal.data');
    const dealKeysPayload = _reduce(
      dealKeysToCache,
      (acc, key) => ({
        ...acc,
        [key]: _cloneDeep(_get(deal, key)),
      }),
      {}
    );

    await dispatch(syncPaymentDetails(syncPaymentPayload));
    await dispatch(updateDealItemPaymentDetails(dealKeysPayload, false));
    dispatch(setMarketScanDataFetchingStatus(false));
    dispatch(setDealDirtyStatus(false));
  };

const wrapLoaderWithPromise = loadingCallback =>
  new Promise(resolve => {
    setTimeout(() => {
      if (_isFunction(loadingCallback)) {
        loadingCallback();
      }
      resolve();
    }, 200);
  });

// use this to update the deal using deal items like zipCodes, vehicle change, tradeIn etc
export const updateDealItemPaymentDetailsGalaxy =
  (
    updateParams,
    includeDefaultPayload = true,
    params = { previewCall: false, updateStore: true, queryParams: EMPTY_STRING },
    loadingCallback = _noop
  ) =>
  async (dispatch, getState) => {
    const state = getState()[BASE_REDUCER_KEY];
    const desking = _get(state, 'desking');
    const deal = _get(desking, 'deal.data');
    const dealNumber = DealReader.getDealNumber(deal);
    const { pickFromDeal = [], ...payloadData } = updateParams || {};

    let payload = includeDefaultPayload
      ? { vehicles: _get(deal, 'vehicles'), customers: _get(deal, 'customers'), ...payloadData }
      : payloadData;
    if (pickFromDeal?.length) {
      payload = { ...payload, ..._pick(deal, pickFromDeal) };
    }

    if (DealerPropertyHelper.isAECProgram()) {
      const primaryVehicle = DealReader.getPrimaryVehicle(deal);
      payload = { ...payload, program: tget(primaryVehicle, 'programId', null) };
    }

    const { previewCall, updateStore, queryParams } = params;
    const [{ response, error }] = await Promise.all([
      previewCall
        ? DeskingAPI.previewDealItemPaymentDetails({ dealNumber, ...payload })
        : DeskingAPI.updateDealItemPaymentDetails({ payload: { dealNumber, ...payload }, queryParams }),
      dispatch(setMarketScanDataFetchingStatus(true)),
      wrapLoaderWithPromise(loadingCallback),
    ]);

    const sanitizedDeal = addMissingDealKeys(response?.deal);
    dispatch([
      ...(updateStore ? getUpdateStoreDetailsActionList(sanitizedDeal) : []),
      setMarketScanDataFetchingStatus(false),
      setDealDirtyStatus(false),
    ]);
    return { response, error };
  };

// use this to update the deal using deal items like zipCodes, vehicle change, tradeIn etc
export const updateDealItemPaymentDetails =
  (
    updateParams,
    includeDefaultPayload = true,
    params = { previewCall: false, updateStore: true, queryParams: EMPTY_STRING }
  ) =>
  async (dispatch, getState) => {
    dispatch(setMarketScanDataFetchingStatus(true));
    const state = getState()[BASE_REDUCER_KEY];
    const desking = _get(state, 'desking');
    const deal = _get(desking, 'deal.data');
    const dealNumber = DealReader.getDealNumber(deal);
    const { pickFromDeal = [], ...payloadData } = updateParams || {};

    let payload = includeDefaultPayload
      ? { vehicles: _get(deal, 'vehicles'), customers: _get(deal, 'customers'), ...payloadData }
      : payloadData;
    if (pickFromDeal?.length) {
      payload = { ...payload, ..._pick(deal, pickFromDeal) };
    }

    const { previewCall, updateStore, queryParams } = params;
    const { response, error } = previewCall
      ? await DeskingAPI.previewDealItemPaymentDetails({ dealNumber, ...payload })
      : await DeskingAPI.updateDealItemPaymentDetails({ payload: { dealNumber, ...payload }, queryParams });

    if (response?.deal && updateStore) {
      dispatch(updateStorePaymentDetails(response.deal));
    }
    dispatch(setMarketScanDataFetchingStatus(false));
    dispatch(setDealDirtyStatus(false));
    return { response, error };
  };

// use this to update the backend deal object from the redux (only payment details)
export const syncPaymentDetails =
  (syncPaymentPayload = {}) =>
  async dispatch => {
    dispatch(setMarketScanDataFetchingStatus(true));

    const payload = dispatch(getSavePmtDetailsCalcEnginePayload(syncPaymentPayload));
    const { response } = await DeskingAPI.savePaymentDetailsCalcEngine(payload);
    const dealInfo = response?.deal;
    if (dealInfo) {
      dispatch(updateStorePaymentDetails(dealInfo));
    }
    dispatch(setMarketScanDataFetchingStatus(false));
  };

// use this to update the backend deal object from the redux (only payment details)
/*
  NOTE: AVOID USING CALLBACK, this is added clearly to improve perceived slowness which comes in when 2 APIs are hit sequentially
  */
export const syncPaymentDetailsGalaxy =
  (syncPaymentPayload = {}, loadingCallback = _noop) =>
  async (dispatch, getState) => {
    const payload = getSavePmtDetailsCalcEnginePayload(syncPaymentPayload)(dispatch, getState);

    const [{ response }] = await Promise.all([
      DeskingAPI.savePaymentDetailsCalcEngine(payload),
      dispatch([setDealDirtyStatus(true), setMarketScanDataFetchingStatus(true)]),
      wrapLoaderWithPromise(loadingCallback),
    ]);
    const dealInfo = response?.deal;
    dispatch([
      ...getUpdateStoreDetailsActionList(dealInfo),
      setMarketScanDataFetchingStatus(false),
      setDealDirtyStatus(false),
    ]);
  };

export const syncPaymentDetailsPreview = payload => async dispatch => {
  dispatch(setMarketScanDataFetchingStatus(true));
  const { response, error } = await DeskingAPI.savePaymentDetailsCalcEnginePreview(payload);
  dispatch(setMarketScanDataFetchingStatus(false));
  return { response, error };
};

export const updateDeskingDataByDeal = () => (dispatch, getState) => {
  const state = getState()[BASE_REDUCER_KEY];
  const deal = _get(state, 'desking.deal.data');
  if (deal) {
    dispatch(updateStorePaymentDetails(deal));
  }
};

const getUpdateStoreDetailsActionList = dealInfo => {
  if (dealInfo) {
    const isCalculationByBackendEnabled = CalcEngineProperties.updateCalculationByBackend();
    const paymentsInfoInDeal = DealReader.getDealPaymentsForAllVehicles(dealInfo);
    const columnsData = DealReader.getColumnsDataFromDeskingPaymentDetails(paymentsInfoInDeal);
    const downPayments = DealReader.getDownPaymentsFromDeskingPaymentDetails(dealInfo, isCalculationByBackendEnabled);
    const updateddeferredPayments = getDeferredPayments(columnsData);
    return [
      setDeal(dealInfo),
      setDeferredPaymentDetails({ deferredPayments: updateddeferredPayments }),
      setDownPayments(downPayments),
      setColumnsData(columnsData),
    ];
  }
  return [];
};

/**
 * https://tekion.atlassian.net/browse/DMS-67406
 * to ignore selected term received in sales/core/u/deal/v2/dealItem/update
 * by picking selected term from store
 */
// const retainSelectedTermFromStore = (columnsToOverride, columnsInStore) =>
//   _map(columnsToOverride, columnData => ({
//     ...columnData,
//     ..._pick(
//       _find(columnsInStore, ({ id }) => id === columnData.id),
//       ['selected', 'selectedDownPayment', 'selectedDownPaymentId']
//     ),
//   }));

// use this to update the local deal object in the redux
export const updateStorePaymentDetails = dealInfo => async dispatch => {
  /**
   * https://tekion.atlassian.net/browse/DMS-106267
   * point 1 - retainSelectedTermFromStore function call not required
   * since selected term is coming form BE
   */

  // const {
  //   desking: { deskingpaymentDetails },
  // } = getState()[BASE_REDUCER_KEY];
  // const columnsData = retainSelectedTermFromStore(
  //   DealReader.getColumnsDataFromDeskingPaymentDetails(paymentsInfoInDeal),
  //   deskingpaymentDetails
  // );

  if (!CalcEngineProperties.updateByGalaxyEngine()) {
    dispatch(setDeal(dealInfo));
  }
  const paymentsInfoInDeal = DealReader.getDealPaymentsForAllVehicles(dealInfo);
  const columnsData = DealReader.getColumnsDataFromDeskingPaymentDetails(paymentsInfoInDeal);
  const isCalculationByBackendEnabled = CalcEngineProperties.updateCalculationByBackend();
  const downPayments = DealReader.getDownPaymentsFromDeskingPaymentDetails(dealInfo, isCalculationByBackendEnabled);
  const updateddeferredPayments = getDeferredPayments(columnsData);

  if (CalcEngineProperties.updateByGalaxyEngine()) {
    // PERF FIX: should be batched always, to avoid impact in US/Canada, added check for galaxy
    dispatch([
      setDeal(dealInfo),
      setDeferredPaymentDetails({ deferredPayments: updateddeferredPayments }),
      setDownPayments(downPayments),
      setColumnsData(columnsData),
    ]);
    return;
  }

  dispatch(setDeferredPaymentDetails({ deferredPayments: updateddeferredPayments }));
  dispatch(setDownPayments(downPayments));
  dispatch(setColumnsData(columnsData));
};

export const updateDeskingColumns =
  (columnIds = []) =>
  async (dispatch, getState) => {
    const state = getState()[BASE_REDUCER_KEY];
    const dealNumber = _get(state, 'desking.deal.data.dealNumber');
    const mappedDownpaymentDetails = getFormattedDeskingDetailsForServer(getState);

    // TODO: update deal directly and send required columns to server
    const mappedDownpaymentDetailsForColumnIds = _filter(mappedDownpaymentDetails, column => {
      if (columnIds.includes(column.id)) return true;
      return false;
    });

    const payload = {
      paymentUpdateRequest: {
        updatedDealPayments: mappedDownpaymentDetailsForColumnIds,
      },
      dealNumber,
    };
    const dealInfo = await dispatch(getUpdateDeskingV3Res(payload));
    if (dealInfo) {
      dispatch(setDeal(dealInfo));
      const paymentsInfoInDeal = DealReader.getDealPaymentsForSelectedVehicle(dealInfo);
      const columnsData = DealReader.getColumnsDataFromDeskingPaymentDetails(paymentsInfoInDeal);
      const isCalculationByBackendEnabled = CalcEngineProperties.updateCalculationByBackend();
      const downPayments = DealReader.getDownPaymentsFromDeskingPaymentDetails(dealInfo, isCalculationByBackendEnabled);
      // const updatedColumnsDataById = _keyBy(columnsData, 'id');
      const updateddeferredPayments = getDeferredPayments(columnsData);

      dispatch(setDeferredPaymentDetails({ deferredPayments: updateddeferredPayments }));
      dispatch(setDownPayments(downPayments));
      dispatch(setColumnsData(columnsData));
      if (DealerPropertyHelper.isExternalDmsEnabled()) {
        dispatch(fetchDealSyncMappingWarning(_get(dealInfo, 'dealNumber')));
      }
    }

    // TODO: Based on columIds update only those in desking
    // const downPayments = DealReader.getDownPaymentsFromDeskingPaymentDetails(deal);
    // const updatedColumnsDataById = _keyBy(columnsData, 'id');
    // const deferredPayments = getDeferredPayments(columnsData);
    //   DeskingActions.setDeferredPaymentDetails({ deferredPayments });
    //   DeskingActions.setDownPayments(downPayments);
  };

export const setDepositDetails = createAction(ACTION_TYPES.SET_DEPOSIT_DETAILS);

export const setAutoRewardDetails = createAction(ACTION_TYPES.SET_AUTO_REWARD_DETAILS);

export const fetchContractsForDeal = refId => async dispatch => {
  const response = await DeskingAPI.fetchContracts(refId);
  const generatedContractsList = _get(response, 'productContractInfos') || [];
  dispatch({
    type: ACTION_TYPES.SET_DEAL_PRODUCT_CONTRACT_INFOS,
    payload: generatedContractsList,
  });
  return generatedContractsList;
};

export const setContractsForDeal = deal => async dispatch => {
  const productContractInfos = _get(deal, ['penInfo', 'productContractInfos'], EMPTY_ARRAY);
  dispatch({
    type: ACTION_TYPES.SET_DEAL_PRODUCT_CONTRACT_INFOS,
    payload: productContractInfos,
  });
};

export const updateDealAndCb =
  ({ dealNumber, payload, cb = _noop }) =>
  async dispatch => {
    const apiResponse = await DeskingAPI.updateDeal(dealNumber, payload);
    const { response, error } = apiResponse || {};
    const newDealInfo = _get(response, 'deal');

    dispatch(fetchDeal(dealNumber));
    const mandatoryReviewFields = _get(response, 'mandatoryFieldsValidationResponse') || {};

    if (_isEmpty(newDealInfo)) {
      const errorMSG = getAPIError(error) || MESSAGES.DEAL_UPDATION_FAILED;
      toaster('error', errorMSG);
    } else {
      cb(newDealInfo);
    }

    dispatch({
      type: ACTION_TYPES.UPDATE_MANDATORY_FIELDS,
      payload: mandatoryReviewFields,
    });

    return { response: newDealInfo, error };
  };

export const updateDealOnLeadNotification = payload => async dispatch => {
  const response = await CRMApi.updateDealFromLead(payload);
  const deal = _get(response, 'response');
  if (deal) {
    dispatch(updateStorePaymentDetails(deal));
  }
  return deal;
};

const getRevokeDealSuccessMessage = shouldRevokeDeal => {
  if (isInchcape()) {
    return shouldRevokeDeal
      ? DEPOSIT_MESSAGES.VEHICLE_RESERVATION_REVOKED
      : DEPOSIT_MESSAGES.REFUND_PROCESSED_SUCCESSFULLY;
  }
  if (isRRG()) {
    return shouldRevokeDeal
      ? DEPOSIT_MESSAGES.DEAL_CONFIRMATION_REVOKED
      : DEPOSIT_MESSAGES.REFUND_PROCESSED_SUCCESSFULLY;
  }
  return DEPOSIT_MESSAGES.DEAL_CONFIRMATION_REVOKED;
};

export const revokeDeal =
  (deal, refundId, shouldRevokeDeal, refundAmount, blockScreen = _noop, revokeAndRefund = false) =>
  async dispatch => {
    let payload = {
      dealNumber: DealReader.getDealNumber(deal),
      refundId,
      refund: !!refundId,
    };

    if (isInchcapeOrRRG()) {
      payload = {
        ...payload,
        refund: !shouldRevokeDeal,
        cancelDepositInvoice: shouldRevokeDeal,
        revokeConfirmation: shouldRevokeDeal,
        refundAmount,
        revokeAndRefund,
      };
    }

    const { error = {}, response: updatedDeal } = await DeskingAPI.revokeDeal(payload);
    const errorMsg = getAPIError(error);
    if (errorMsg) {
      toaster('error', errorMsg);
      return { errorMsg };
    }

    // explicitely assigning bookedDate
    // We need some default value(undefined) in case the bookedDate is missing in the updatedDeal
    dispatch(setDeal({ ...updatedDeal, bookedDate: updatedDeal.bookedDate }));
    const primaryVehicle = DealReader.getPrimaryVehicle(deal);
    const vehicleId = VehicleReader.getVehicleId(primaryVehicle);
    if (vehicleId) await dispatch(getVehiclesByIdV2(vehicleId));
    toaster('success', getRevokeDealSuccessMessage(shouldRevokeDeal));

    if (isInchcape() && DealReader.postInvoiceDp(deal)) {
      blockScreen();
      return updatedDeal;
    }
    return EMPTY_OBJECT;
  };

export const confirmDeal =
  (payload, onConfirmDeal = _noop, blockScreen = _noop) =>
  async dispatch => {
    const { error = {}, response } = await DeskingAPI.confirmDeal(payload);
    const errorMsg = getAPIError(error);
    if (errorMsg) {
      toaster('error', errorMsg);
      return { errorMsg };
    }
    const mandatoryReviewFields = _get(response, 'mandatoryFieldsValidationResponse') || {};
    dispatch({
      type: ACTION_TYPES.UPDATE_MANDATORY_FIELDS,
      payload: mandatoryReviewFields,
    });

    if (response.deal) {
      if (isInchcape() && DealReader.postInvoiceDp(response.deal)) {
        blockScreen();
      }
      await dispatch(setDeal(response.deal));
      onConfirmDeal(payload?.depositAmount);
    }

    return response;
  };

const getLatestRecallsForVehicle = (fromCache, isManualRefresh, dealVehicleId) => async (dispatch, getState) => {
  const { vin, id: vehicleId } = dealVehicleId
    ? DealReader.getVehicleBasedOnId(getDeal(getState()[BASE_REDUCER_KEY]), dealVehicleId)
    : DealReader.getPrimaryVehicle(getDeal(getState()[BASE_REDUCER_KEY])) || EMPTY_OBJECT;
  if (!vin || !vehicleId) return null; // !vehicleId => Build vehicle, dnt call
  const { response: recallDetails, error } = await CommonAPI.getLatestRecalls(vin, fromCache);
  if (error) {
    if (isManualRefresh) {
      const errorMsg = getAPIError(error) || __('Error!');
      toaster('error', errorMsg);
    }

    return null;
  }
  const openRecalls = getOpenRecall({ recallDetails });
  if (isManualRefresh && _isEmpty(openRecalls)) toaster('info', MESSAGES.NO_RECALLS_FOUND);

  await dispatch({
    type: COMMON_ACTION_TYPES.UPDATE_VI_VEHICLE_IN_STORE,
    payload: { vehicleId, updatedViVehicleInfo: { recallDetails } }, // set all(closed/open) recalls
  });

  return openRecalls;
};

export const showRecallWarning = (fromCache, isManualRefresh, showWarning, dealVehicleId) => async dispatch => {
  const recallDetails = await dispatch(getLatestRecallsForVehicle(fromCache, isManualRefresh, dealVehicleId));
  if (recallDetails) showWarning();

  return recallDetails;
};

export const saveMandatoryFields = payload => async (dispatch, getState) => {
  const state = getState()[BASE_REDUCER_KEY];
  const dealNumber = _get(state, 'desking.deal.data.dealNumber');
  let resolvedDealObj = {};

  const dealType = _get(state, 'desking.deal.data.type');
  const purchaseOnlyName = dealType === DEAL_TYPES.ONLY_TRADES ? __('Purchase') : null;

  const { error = {}, response } = (await DeskingAPI.saveMandatoryFields(payload, dealNumber)) || EMPTY_OBJECT;
  const errorMsg = getAPIError(error);
  if (errorMsg) {
    toaster('error', errorMsg);
    return { errorMsg };
  }
  if (!_isEmpty(response)) {
    const resolvedDeal = await resolver.getResolvedData(ENTITIES.DEALS, [response]);
    resolvedDealObj = _get(resolvedDeal, [0]);
  }
  dispatch({
    type: ACTION_TYPES.SAVE_MANDATORY_FIELDS,
    payload: { response: resolvedDealObj || response, mandatoryReviewFieldsResponse: payload },
  });

  if (!_isEmpty(response) && isInchcape()) {
    toaster(
      'info',
      __(
        `{{purchase}} Order Form Generation in Progress`,
        {
          purchase: purchaseOnlyName,
        },
        {
          autoClose: 3000,
        }
      )
    );
  }

  return response;
};

export const updateMandatoryFields = payload => dispatch => {
  dispatch({
    type: ACTION_TYPES.UPDATE_MANDATORY_FIELDS,
    payload,
  });
};

export const updateDeal = (dealNumber, payload) => async (dispatch, getState) => {
  dispatch(setFetchingUpdateDealStaus(true));
  const state = getState()[BASE_REDUCER_KEY];
  const doNotSendCrmUpdateEvent = _get(state, 'desking.doNotSendCrmUpdateEvent') || false;
  const dealData = _get(state, 'desking.deal.data');

  const isRestrictCRMEventFlowEnabled =
    DynamicPropertyHelper.isRestrictCRMEventFlowEnabled() && isCategory1Deal(dealData);
  const newPayload = {
    ...payload,
    ...(isRestrictCRMEventFlowEnabled ? { doNotSendCrmUpdateEvent } : EMPTY_OBJECT),
  };
  const apiResponse = await DeskingAPI.updateDeal(dealNumber, newPayload);
  const { response, error } = apiResponse || {};
  const newDealInfo = _get(response, 'deal');
  const mandatoryReviewFields = _get(response, 'mandatoryFieldsValidationResponse') || {};

  if (_isEmpty(newDealInfo)) {
    const errorMSG = getAPIError(error) || MESSAGES.DEAL_UPDATION_FAILED;
    if (DynamicPropertyHelper.isRestrictCRMEventFlowEnabled()) {
      await dispatch(setDoNotSendCrmUpdateEventFlag(false));
    }
    toaster('error', errorMSG);
  } else {
    const {
      commissions,
      assignee,
      grossDetails,
      tradeIns,
      version,
      vehicles,
      associatedOriginalDealNumber = EMPTY_STRING,
      associatedOriginalDealInvoiceDate = null,
      paused = false,
    } = newDealInfo;
    dispatch(
      setDeal({
        commissions,
        assignee,
        grossDetails,
        tradeIns,
        version,
        vehicles,
        associatedOriginalDealNumber,
        associatedOriginalDealInvoiceDate,
        paused,
      })
    );
    dispatch(setFetchingUpdateDealStaus(false));
  }

  dispatch({
    type: ACTION_TYPES.UPDATE_MANDATORY_FIELDS,
    payload: mandatoryReviewFields,
  });

  return { response: newDealInfo, error, mandatoryReviewFields };
};

export const updateInternalProductInfo = (dealNumber, payload) => async dispatch => {
  const apiResponse = await DeskingAPI.updateDeal(dealNumber, payload);
  const { response, error } = apiResponse || {};
  const newDealInfo = _get(response, 'deal');
  const mandatoryReviewFields = _get(response, 'mandatoryFieldsValidationResponse') || {};

  if (_isEmpty(newDealInfo)) {
    const errorMSG = getAPIError(error) || MESSAGES.DEAL_UPDATION_FAILED;
    toaster('error', errorMSG);
  } else {
    const { tekionInternalProductInfo } = newDealInfo;
    dispatch(
      setDeal({
        tekionInternalProductInfo,
      })
    );
  }

  dispatch({
    type: ACTION_TYPES.UPDATE_MANDATORY_FIELDS,
    payload: mandatoryReviewFields,
  });

  return { response: newDealInfo, error };
};

export const getMandatoryFields = () => async (dispatch, getState) => {
  const state = getState()[BASE_REDUCER_KEY];
  const dealNumber = _get(state, 'desking.deal.data.dealNumber');
  const response = (await DeskingAPI.getMandatoryFields(dealNumber)) || EMPTY_OBJECT;
  dispatch({
    type: ACTION_TYPES.UPDATE_MANDATORY_FIELDS,
    payload: response,
  });
};

export const getDealPreferences = () => async (dispatch, getState) => {
  const state = getState()[BASE_REDUCER_KEY];
  if (!_isEmpty(_get(state, 'desking.dealPreferences'))) {
    return _get(state, 'desking.dealPreferences');
  }

  const dealNumber = _get(state, 'desking.deal.data.dealNumber');
  if (!dealNumber) return {};
  const response = (await DeskingAPI.getDealPreferences(dealNumber)) || EMPTY_OBJECT;
  const fniSurveyStatus = getFniSurveyStatus(response);
  const customAidStatus = getFniCustomAidStatus(response);
  let viewType = FNI_VIEW_TYPE.FNI;

  if (!_includes(_values(CUSTOM_AID_STATUS), customAidStatus)) {
    viewType = FNI_VIEW_TYPE.CUSTOM_AID;
  } else if (!_includes(_values(FNI_SURVEY_STATUS), fniSurveyStatus)) {
    viewType = FNI_VIEW_TYPE.SURVEY;
  }

  dispatch({
    type: FNI_ACTION_TYPES.SET_FNI_VIEW_TYPE,
    payload: viewType,
  });
  dispatch({
    type: ACTION_TYPES.SET_DEAL_PREFERENCES,
    payload: response,
  });
  return response;
};

export const fetchDealPreferences = () => async (dispatch, getState) => {
  const state = getState()[BASE_REDUCER_KEY];
  const dealNumber = _get(state, 'desking.deal.data.dealNumber');
  if (!dealNumber) return {};
  const response = (await DeskingAPI.getDealPreferences(dealNumber)) || EMPTY_OBJECT;

  dispatch({
    type: ACTION_TYPES.SET_DEAL_PREFERENCES,
    payload: response,
  });
  return response;
};

export const setDealPreferences = payload => async (dispatch, getState) => {
  const state = getState()[BASE_REDUCER_KEY];
  const dealNumber = _get(state, 'desking.deal.data.dealNumber');
  await DeskingAPI.setDealPreferences(dealNumber, payload);
  dispatch({
    type: ACTION_TYPES.SET_DEAL_PREFERENCES,
    payload,
  });
};

export const getDocumentsViewPreferences = () => async (dispatch, getState) => {
  const state = getState()[BASE_REDUCER_KEY];
  if (!_isEmpty(_get(state, 'desking.documentsViewPreferences'))) {
    return _get(state, 'desking.documentsViewPreferences');
  }

  const response = await DeskingAPI.getDocumentsViewPreferences();
  const { documentsTabViewType, dealJacketViewType } = response || {};
  dispatch({
    type: ACTION_TYPES.SET_DOCUMENTS_VIEW_PREFERENCES,
    payload: { documentsTabViewType, dealJacketViewType },
  });
};

export const setDocumentsViewPreferences = payload => async (dispatch, getState) => {
  const state = getState()[BASE_REDUCER_KEY];
  DeskingAPI.setDocumentsViewPreferences({
    ..._get(state, 'desking.documentsViewPreferences'),
    ...payload,
  });
  dispatch({
    type: ACTION_TYPES.SET_DOCUMENTS_VIEW_PREFERENCES,
    payload,
  });
};

export const getStateFeeTaxConfigMetadata = vehicleCategory => async dispatch => {
  let payload = USMetaData();

  const isCalcEngineEnabled = CalcEngineProperties.showCalcEngineView();
  if (isCalcEngineEnabled) {
    const { response } = await DealSetupAPI.getStateTaxFeeUIConfig(vehicleCategory);
    payload = response;
  }
  dispatch({
    type: ACTION_TYPES.UPDATE_STATE_FEE_TAX_METADATA,
    payload,
  });
};

export const getDealInfo = dealNumber => async () => {
  const { response: dealInfo } = await DeskingAPI.getDealInfo(dealNumber);
  const deal = _get(dealInfo, 'deal');
  try {
    const resolvedDeal = await resolver.getResolvedData(ENTITIES.DEALS, [deal]);
    return _get(resolvedDeal, [0]);
  } catch (e) {
    toaster('error', __('Failed to resolve userinfo'));
  }

  return deal;
};

export const approveTradeIn = dealNumber => async () => {
  await DeskingAPI.approveTradeIn(dealNumber, { tradeInApproved: true });
};

export const saveFnIContractSnapshot = generatedProductsPayload => async (dispatch, getState) => {
  const dealNumber = _get(getState()[BASE_REDUCER_KEY], 'desking.deal.data.dealNumber');
  await DeskingAPI.saveFnIContractSnapshot(generatedProductsPayload, dealNumber);
};

export const updateDocumentSignStatus = payload => async dispatch => {
  dispatch({ type: ACTION_TYPES.UPDATE_DOCUMENT_STATUS, payload });
};

export const updateDealerTradeSwap = createAction(ACTION_TYPES.SET_DEALER_TRADE_SWAP);

export const getSavePmtDetailsCalcEnginePayload =
  (additionalPayload = {}) =>
  (_, getState) => {
    const {
      desking: {
        deal: { data: deal },
      },
    } = getState()[BASE_REDUCER_KEY];
    return {
      dealNumber: DealReader.getDealNumber(deal),
      ...additionalPayload,
      vehicleList: _map(_groupBy(getFormattedDeskingDetailsForServer(getState), 'dealVehicleId'), (value, key) => ({
        dealVehicleId: key,
        dealPayments: value,
        rebateProvider: DealReader.getRebateProviderType(deal, key),
        deliveryCode: DealReader.getDeliveryCode(deal, key),
      })),
    };
  };

export const getUpdateDeskingV3Res = payload => async dispatch => {
  let cb = DeskingAPI.updateDesking;
  let pld = payload;
  if (CalcEngineProperties.updateCalculationByBackend()) {
    cb = DeskingAPI.savePaymentDetailsCalcEngine;
    pld = dispatch(getSavePmtDetailsCalcEnginePayload());
  }
  const { response } = await cb(pld);

  return response?.deal;
};

export const getFormattedDeskingDetailsForServer = getState => {
  const state = getState()[BASE_REDUCER_KEY];
  const {
    desking: {
      deskingpaymentDetails,
      deal: { data: deal },
      downPayments,
      deferredPayments,
    },
  } = state;

  return formatDeskingDetailsForServer(
    deskingpaymentDetails,
    deferredPayments,
    selectLendersFromDealerConfig(state),
    downPayments,
    DealReader.isMultipleDownPaymentsEnabled(deal),
    DealReader.getDealSource(deal)
  );
};

const getPaymentDetailsForState = getState => {
  const state = getState()[BASE_REDUCER_KEY];
  const mappedDownpaymentDetails = getFormattedDeskingDetailsForServer(getState);
  const orderedByVehicleId = _groupBy(mappedDownpaymentDetails, 'dealVehicleId');
  const deal = _get(state, 'desking.deal.data');

  const paymentDetails = [];
  _forEach(orderedByVehicleId, (value, key) => {
    paymentDetails.push({
      dealVehicleId: key,
      dealPayments: value,
      rebateProvider: DealReader.getRebateProviderType(deal, key),
      deliveryCode: DealReader.getDeliveryCode(deal, key),
    });
  });
  return paymentDetails;
};

export const setMakeAliases = deal => async dispatch => {
  if (CalcEngineProperties.showCalcEngineView() || !DealerPropertyHelper.standardizedMakeSetupEnabled()) return;
  const vehicleMakes = _map(deal?.vehicles, vehicle => vehicle?.make);
  const response = await Promise.all(vehicleMakes.map(make => DeskingAPI.getMakeAlias([make])));
  const payload = response.reduce((acc, value, index) => {
    const { name, makeId, display, alternateNames = [] } = _head(value) || {};
    return { ...acc, [vehicleMakes[index]]: _compact(_uniq([name, display, makeId, ...(alternateNames || [])])) };
  }, {});

  dispatch({ type: ACTION_TYPES.VEHICLE_MAKE_ALIASES, payload });
};

export const deletePackageDocument = payload => async dispatch => {
  try {
    const { packageDoc } = payload;
    const id = _get(packageDoc, 'id');
    const category = _get(packageDoc, 'category');
    const { dealNumber } = payload;
    const apiPayload = {
      dealNumber,
      packageSignIds: [id],
    };

    if (category === DEAL_JACKET.PACKAGE) {
      await DeskingAPI.deletePackageDocuments(apiPayload);
      await dispatch(setPackageDocs(dealNumber));
      return;
    }

    await DeskingAPI.deleteSppPackageDocuments(apiPayload);
    await dispatch(setSppPackageDocs(dealNumber));
  } catch (e) {
    console.error(e);
  }
};

export const fetchRouteOneErrorsBySignatureCoordinateIds = payload => async dispatch => {
  if (_isEmpty(payload)) {
    return;
  }

  try {
    const response = await DeskingAPI.getRouteOneErrorsBySignatureCoordinateIds(payload);
    if (response) {
      dispatch({ type: ACTION_TYPES.UPDATE_ROUTEONE_ERRORS_BY_SIGNATURE_COORDINATE_IDS, payload: response });
    }
  } catch (e) {
    console.error(e);
    toaster('error', __('Error fetching RouteOne errors'));
  }
};

export const updateRouteOneErrorsBySignatureCoordinateIds = payload => async dispatch => {
  if (_isEmpty(payload)) {
    return;
  }

  dispatch({ type: ACTION_TYPES.UPDATE_ROUTEONE_ERRORS_BY_SIGNATURE_COORDINATE_IDS, payload });
};

const _callUpdateDealStatus = async ({ fromStatus, toStatus, deal }) => {
  const payload = {
    fromStatus: fromStatus || DealReader.getDealStatus(deal),
    toStatus,
    deal: { dealNumber: DealReader.getDealNumber(deal) },
  };
  const { response, error } = await DeskingAPI.updateDealStatus(payload);
  const hasError = _get(error, 'detail.displayMessage') || _get(response, 'error') || _get(response, 'messages.0');
  if (hasError) {
    throw new Error(hasError);
  }

  return response;
};

export const markDealAsDelivered = deal => async dispatch => {
  try {
    await _callUpdateDealStatus({ toStatus: DEAL_STATUS.DELIVERED, deal });
    await dispatch(setDealStatus(DEAL_STATUS.DELIVERED));
  } catch (e) {
    toaster('error', __('Error in marking the Deal as Delivered.'));
  }
};

export const updateShowArchivedDocuments =
  ({ dealNumber, archiveStatus }) =>
  async dispatch => {
    await DeskingAPI.updateShowArchiveStatus({ dealNumber, archiveStatus });

    dispatch({ type: ACTION_TYPES.SET_SHOW_ARCHIVED_DOCUMENTS_IN_DEALJACKET, payload: archiveStatus });
  };

export const updateDocumentSigningModal = value => async dispatch => {
  dispatch({ type: ACTION_TYPES.UPDATE_DOCUMENT_SIGN_MODAL, payload: value });
};

export const changeResidualData = residualInfo => async (dispatch, getState) => {
  const state = getState()[BASE_REDUCER_KEY];
  const resetPenaltyOnResidualChange = _get(state, ['common', 'dealerConfig', 'resetPenaltyOnResidualChange']);
  dispatch(setDealDirtyStatus(true));
  dispatch({ type: ACTION_TYPES.CHANGE_RESIDUAL_DATA, payload: { residualInfo, resetPenaltyOnResidualChange } });
};

export const switchVehicleFromMscanToCalcengine =
  ({ vehicles, dealNumber }) =>
  async dispatch => {
    const { response } = await DeskingAPI.switchVehicleFormMscanToCalcEngine({
      vehicles,
      dealNumber,
    });
    if (response?.deal) {
      dispatch(updateStorePaymentDetails(response.deal));
    }
  };

export const setESignSessionInfo =
  ({
    digitalSigningPayload,
    dealNumber,
    documentShareType = DOCUMENTS_SHARE_FLOW_FOR_ESIGN.STANDALONE,
    waitForSigningURLGeneration = false,
  }) =>
  async dispatch => {
    const { formSignPreference: signingMediumPreference, shouldOpenDealerSigningLink = true } = digitalSigningPayload;

    const toastId = toaster('info', __('Please Wait! Preparing documents for signing'), { autoClose: false });
    const { error, errorParams } =
      documentShareType === DOCUMENTS_SHARE_FLOW_FOR_ESIGN.STANDALONE
        ? await DeskingAPI.shareDocumentsForESign(digitalSigningPayload)
        : await DeskingAPI.createAssistedEsignSession(dealNumber);

    if (!_isEmpty(error)) {
      handleDigitalSigningPlatformErrors({ error, errorParams });
      dismissToast(toastId);
      return false;
    }
    if (signingMediumPreference !== DSP_SHARING_TYPES.SIGN_AT_HOME) {
      await dispatch(clearAllPreviousSigningSessions());
    }

    if (signingMediumPreference === DSP_SHARING_TYPES.SIGN_AT_IPAD) {
      if (waitForSigningURLGeneration) {
        return true;
      }
      const status = await dispatch(handleIpadCastingForDSP());
      dismissToast(toastId);
      return status;
    }

    if (!shouldOpenDealerSigningLink) {
      dismissToast(toastId);
      toaster(TOASTER_TYPE.SUCCESS, __('Documents shared successfully'));
      return true;
    }

    const status = await dispatch(
      setDSPSigningAppURLs({
        signingMediumPreference,
      })
    );
    dismissToast(toastId);
    return status;
  };

export const pushDocumentsToDealJacket = async payload => {
  const { response } = await DeskingAPI.pushDocumentsToDealJacket(payload);
  const hasError = _get(response, 'error') || _get(response, 'messages.0');
  if (hasError) {
    throw Error(hasError);
  }
};

export const clearESignSessionInfo = () => async dispatch => {
  dispatch({ type: ACTION_TYPES.SET_ESIGN_SESSION_INFO, EMPTY_OBJECT });
  dispatch({ type: ACTION_TYPES.SET_ESIGN_SESSION_ACTIVE, payload: false });
};

export const stopEsignSharingSession = payload => async (_, getState) => {
  await DeskingAPI.stopSharingSession(payload);
  const deal = getDeal(getState()[BASE_REDUCER_KEY]);
  await DigitalRetailAPI.stopAllSharing(DealReader.getDealNumber(deal), EMPTY_OBJECT);
};

export const changeDealStatusToOnQuote = deal => async dispatch => {
  const dealNumber = DealReader.getDealNumber(deal);
  const dealType = DealReader.getDealType(deal);
  try {
    await _callUpdateDealStatus({
      fromStatus: DealReader.getDealStatus(deal),
      toStatus: DEAL_STATUS.QUOTE,
      deal,
    });
    await dispatch(setDealStatus(DEAL_STATUS.QUOTE));
    toaster(
      'success',
      __(`{{purchase}} Order Form Cancelled Successfully.`, {
        purchase: dealType === DEAL_TYPES.ONLY_TRADES ? __('Purchase') : null,
      })
    );
    const newDealObject = await _fetchDeal(dealNumber);
    if (newDealObject) {
      dispatch(setDeal(newDealObject));
    }
  } catch (e) {
    toaster('error', __('Error in cancelling form.'));
  }
};

export const fetchDealSyncMappingWarning = dealNumber => async dispatch => {
  const { response } = (await DeskingAPI.fetchDealSyncWarning(dealNumber)) || EMPTY_OBJECT;
  if (response) {
    dispatch({
      type: ACTION_TYPES.UPDATE_DEAL_SYNC_MISSING_MAPPING,
      payload: _get(response, 'missingMappings') || [],
    });
    dispatch({
      type: ACTION_TYPES.UPDATE_DEAL_SYNC_ERRORS,
      payload: _get(response, 'dealSyncErrors') || [],
    });
  }
};

export const getDSPSigningAppURLs = payload => async dispatch => {
  const participantUrlResponse = await DeskingAPI.getDealjacketURLsForDocuments(payload);
  // eslint-disable-next-line prefer-const
  let { dspPreviewUrl, dspSigningUrl } = parseGetSigningAppUrlsResponse(participantUrlResponse);

  // This condition indicates that user is not added in DSP
  if (_isEmpty(dspSigningUrl) && !_isEmpty(dspPreviewUrl)) {
    dspSigningUrl = await dispatch(addCurrentUserToDSPAndGetUrl());
  }

  return { dspPreviewUrl, dspSigningUrl };
};

export const getIsAssistedSigningSessionInProgress = signingMediumPreference => {
  const isParallelSigningEnabled = DealerPropertyHelper.isParallelSigningEnabled();

  if (isParallelSigningEnabled && signingMediumPreference === DSP_SHARING_TYPES.SIGN_AT_IPAD) {
    return getIsIpadCasting(signingMediumPreference);
  }

  return getIsSharingOnConcierge(signingMediumPreference);
};

export const setDSPSigningAppURLs =
  ({ showUserSwitch = false, signingMediumPreference, fullScreen = true }) =>
  async (dispatch, getState) => {
    const state = getState()[BASE_REDUCER_KEY];
    const dealNumber = _get(state, 'desking.deal.data.dealNumber');
    const userInfo = TEnvReader.userInfo();
    const { id: userId } = userInfo;

    const { dspSigningUrl } = await dispatch(getDSPSigningAppURLs({ dealNumber, userId }));
    if (_isEmpty(dspSigningUrl)) {
      toaster('error', __('Error generating signing links. Please try again later'));
      return false;
    }
    const sessionInfo = {
      eSignAppUrl: getESignPlatformUrlWithAdditionalParameters(dspSigningUrl),
      visible: Boolean(dspSigningUrl),
      showUserSwitch,
      signingMediumPreference,
      fullScreen,
      isAssistedSigningSessionInProgress: getIsAssistedSigningSessionInProgress(signingMediumPreference),
    };
    dispatch({ type: ACTION_TYPES.SET_ESIGN_SESSION_INFO, payload: sessionInfo });
    dispatch(toggleESignSessionActive());
    return true;
  };

export const castSheetsFromDSP =
  ({ digitalSigningPayload }) =>
  async dispatch => {
    const { formSignPreference: signingMediumPreference } = digitalSigningPayload;
    const toastId = toaster('info', __('Please Wait! Preparing documents for signing'), { autoClose: false });
    const { error, errorParams } = await DeskingAPI.shareSheetsForESign(digitalSigningPayload);
    if (error) {
      handleDigitalSigningPlatformErrors({ errorParams, error });
      dismissToast(toastId);
      return false;
    }
    await dispatch(clearAllPreviousSigningSessions());

    if (signingMediumPreference === DSP_SHARING_TYPES.SIGN_AT_IPAD) {
      const status = await dispatch(handleIpadCastingForDSP());
      dismissToast(toastId);
      return status;
    }

    const status = await dispatch(
      setDSPSigningAppURLs({
        signingMediumPreference,
      })
    );

    dismissToast(toastId);
    return status;
  };

export const getHardAddsAccessories = payload => async () => {
  const response = await DeskingAPI.getHardAddsAccessories(payload);
  const vehicleEquipments = _get(response, 'lenderData.lenderDataItems[0].dealerInstalledOptions') || EMPTY_ARRAY;
  return vehicleEquipments;
};

export const getAllVehicleAlerts = vehicleId => async () => {
  const { response } = await DeskingAPI.getVehicleAlerts(vehicleId);
  const alerts = _reduce(response, (obj, item) => Object.assign(obj, { [item.id]: item.alerts }), {});
  return alerts;
};

export const getFinanceContractStatus = dealNumber => async dispatch => {
  const { response: deal, error } = await DeskingAPI.getFinanceContractStatus(dealNumber);

  if (!_isEmpty(error)) {
    const errorMsg = getAPIError(error) || __('There is an error in checking the status. Please try after some time');
    toaster('error', errorMsg);
    return;
  }

  if (!_isEmpty(deal)) {
    dispatch(updateStorePaymentDetails(deal));
    setDealVersion(deal);

    const deskingPaymentDetails = DealReader.getDeskingPaymentDetailsFromDeal(deal);
    const { financeContractReturnCode, financeContractStatusMessage, financeContractStatusNumber } =
      getFinanceContractStatusDetails(deskingPaymentDetails);

    if (_toNumber(financeContractReturnCode) === 0) {
      const financeContractStatus = FINANCE_CONTRACT_STATUS_NUMBER_TO_STATUS_MAP[financeContractStatusNumber];
      const toasterType = STATUS_VS_TOASTER_TYPE[financeContractStatus];
      const toasterMessage = STATUS_VS_TOASTER_MESSAGE[financeContractStatus];
      toaster(toasterType, __(toasterMessage));
    } else {
      toaster(TOASTER_TYPE.ERROR, financeContractStatusMessage);
    }
  }
};

export const dspSendNotificationToiPad = payload => async (_, getState) => {
  const state = getState()[BASE_REDUCER_KEY];
  const deal = getDeal(state);
  const apiPayload = {
    dealNumber: DealReader.getDealNumber(deal),
    deviceId: getIpadDeviceId(),
    userType: _get(payload, 'userType'),
    castingRequestType: _get(payload, 'castingRequestType', IPAD_CASTING_REQUEST_TYPES.START_CASTING),
  };
  const isClearingSession = _get(payload, 'isClearingSession', false);
  if (_isEmpty(apiPayload.deviceId)) {
    if (!isClearingSession) {
      toaster('error', __('Error casting to iPad'));
    }
    return false;
  }
  const isParallelSigningEnabled = DealerPropertyHelper.isParallelSigningEnabled();
  const externalSigningPartner = _get(state, 'documentsV2.documentsSignatureMetadata.externalSigningPartner');

  if (isParallelSigningEnabled && externalSigningPartner !== SIGNING_PARTNER.DEALER_TRACK) {
    const customers = _get(deal, 'customers');
    const userTypes = _map(customers, ({ type }) => type);
    const updatedApiPayload = {
      ...apiPayload,
      userTypes,
    };
    _unset(updatedApiPayload, 'userType');
    const { response } = await DeskingAPI.paralleleSigningdspSendNotificationToiPad(updatedApiPayload);
    return response;
  }
  const { response } = await DeskingAPI.dspSendNotificationToiPad(apiPayload);
  return response;
};

export const getPrimaryDMSInfo = () => async dispatch => {
  const { response } = await DeskingAPI.getPrimaryDMSInfoApi();
  if (response) {
    dispatch({ type: ACTION_TYPES.SET_DMS_PROVIDER, payload: response?.dmsProvider });
  }
  return response?.dmsProvider;
};

export const updateDocumentPrintStatus = (payload, mediaId) => async (_, getState) => {
  const state = getState()[BASE_REDUCER_KEY];
  const dealNumber = DealReader.getDealNumber(getDeal(state));
  if (DealerPropertyHelper.isEnhancedDocumentsTabEnabled()) {
    const updatePrintPayload = getUpdateFormPrintStatusPaylod({
      dealNumber,
      formIds: getDocumentIdBasedOnFromSheet({ dealNumber, document: payload }),
    });

    DeskingAPI.updatePrintActivityForDocument(updatePrintPayload);
    return;
  }

  DeskingAPI.updatePrintStatusForDJ({
    dealNumber,
    mediaId,
  });
};

export const syncPaymentDetailsV2 =
  (dealParams, requestParams, saveParams = { previewCall: false, updateStore: true }) =>
  async dispatch => {
    dispatch(setMarketScanDataFetchingStatus(true));
    const { previewCall, updateStore } = saveParams;
    const { deal } = dealParams;

    const payload = {
      dealNumber: DealReader.getDealNumber(deal),
      vehicleList: _map(
        _groupBy(getFormattedDeskingDetailsForLocalState(dealParams), 'dealVehicleId'),
        (value, key) => ({
          dealVehicleId: key,
          dealPayments: value,
          rebateProvider: DealReader.getRebateProviderType(deal, key),
          deliveryCode: DealReader.getDeliveryCode(deal, key),
        })
      ),
      ...requestParams,
    };

    const { response } = previewCall
      ? await DeskingAPI.savePaymentDetailsCalcEnginePreview(payload)
      : await DeskingAPI.savePaymentDetailsCalcEngine(payload);

    if (updateStore) {
      dispatch(updateStorePaymentDetails(response.deal));
    }
    dispatch(setMarketScanDataFetchingStatus(false));
    dispatch(setDealDirtyStatus(false));
    return { response };
  };

export const getFormattedDeskingDetailsForLocalState = params => {
  const { deal, updatedDeskingPaymentDetails, lenders } = params;
  const deferredPayments = getDeferredPayments(updatedDeskingPaymentDetails);
  const downPayments = [];

  const deskingPaymentDetailsWithTermPaymentAsArray = updatedDeskingPaymentDetails.map(deskingPayment => {
    const { termPaymentDetails } = deskingPayment;
    const newTermPaymentDetails = [];

    _forEach(_keys(termPaymentDetails), k => {
      newTermPaymentDetails.push(..._values(termPaymentDetails[k]));
    });
    _forEach(newTermPaymentDetails, newTermPayment => {
      downPayments.push(getDownPaymentObjectForAllTypes(newTermPayment.downPayment ?? 0));
    });
    return {
      ...deskingPayment,
      termPaymentDetails: newTermPaymentDetails.filter(Boolean),
    };
  });

  const countOfDownPayments = DealReader.maxTermPaymentsAllowed(deal);

  return formatDeskingDetailsForServer(
    updatedDeskingPaymentDetails,
    deferredPayments,
    lenders,
    downPayments.slice(0, countOfDownPayments),
    DealReader.isMultipleDownPaymentsEnabled(deal)
  );
};

export const updateCustomerMaskedData = data => async dispatch => {
  dispatch({
    type: ACTION_TYPES.UPDATE_CUSTOMER_MASKED_DATA,
    payload: data,
  });
};

export const getActiveRoomForParticipant = () => async dispatch => {
  const userInfo = TEnvReader.userInfo();
  const { id: userId } = userInfo;
  const response = await DeskingAPI.getActiveRoomForParticipant({
    participantId: userId,
  });

  await dispatch({
    type: ACTION_TYPES.SET_VIRTUAL_MEETING_ROOM_DETAILS,
    payload: _get(response, 'response', EMPTY_OBJECT),
  });

  return response;
};

export const createVMRoom = participantInfo => async (dispatch, getState) => {
  const commonPayload = getPayloadForVirtulMeetingInit(participantInfo);
  const existingRoomDetails = getVMDetails(getState()[BASE_REDUCER_KEY]);
  const roomSid = _get(existingRoomDetails, 'roomSid', '');
  let vmRoomDetails = EMPTY_OBJECT;
  if (roomSid) {
    vmRoomDetails = await DeskingAPI.getVMRoomAccess({
      roomSid,
      ...commonPayload,
    });
  } else {
    vmRoomDetails = await DeskingAPI.createVMRoom({
      recordParticipantsOnConnect: false,
      module: 'SALES_ARC_DEFAULT',
      ...commonPayload,
    });
  }

  const roomData = {
    roomSid,
    ..._get(vmRoomDetails, 'response', EMPTY_OBJECT),
    token: _get(vmRoomDetails, 'response.roomAccessKey') || _get(vmRoomDetails, 'response.accessToken'),
  };
  await dispatch({
    type: ACTION_TYPES.SET_VIRTUAL_MEETING_ROOM_DETAILS,
    payload: roomData,
  });
  return roomData;
};

export const updateVMSessionDetails = payload => async dispatch => {
  await dispatch({
    type: ACTION_TYPES.SET_VIRTUAL_MEETING_ROOM_DETAILS,
    payload,
  });
};

export const updateVMParticipantsList = payload => async (dispatch, getState) => {
  const participants = getVMParticipantsList(getState()[BASE_REDUCER_KEY]);
  const updatedParticipants = getVirtualMeetingParticipantList({ ...payload, participants });
  await dispatch({
    type: ACTION_TYPES.SET_VIRTUAL_MEETING_PARTICIPANTS,
    payload: updatedParticipants,
  });
};

export const searchVMParticipant = roomSid => async () => {
  const response = await DeskingAPI.searchVMParticipant({ roomSid });
  return _get(response, 'response', EMPTY_ARRAY);
};

export const handleIpadCastingForDSP = () => async (dispatch, getState) => {
  const status = await dispatch(dspSendNotificationToiPad({ userType: CUSTOMER_TYPES_VALUES.BUYER }));
  if (!status) {
    return false;
  }
  const state = getState()[BASE_REDUCER_KEY];
  const deal = getDeal(state);
  const hasCoBuyer = !_isEmpty(DealReader.getCoBuyer(deal));
  let signingUsers = DSP_DEFAULT_SIGNING_USERS;
  if (!hasCoBuyer) {
    signingUsers = _reject(signingUsers, ({ value }) => value === CUSTOMER_TYPES_VALUES.CO_BUYER);
  }
  const isParallelSigningEnabled = DealerPropertyHelper.isParallelSigningEnabled();
  const externalSigningPartner = _get(state, 'documentsV2.documentsSignatureMetadata.externalSigningPartner');
  const isFullscreen = isParallelSigningEnabled && externalSigningPartner !== SIGNING_PARTNER.DEALER_TRACK;
  const sessionInfo = {
    visible: true,
    showUserSwitch: true,
    signingMediumPreference: DSP_SHARING_TYPES.SIGN_AT_IPAD,
    signingUsers,
    fullScreen: isFullscreen,
  };

  if (isFullscreen) {
    const response = await dispatch(
      setDSPSigningAppURLs({
        signingMediumPreference: DSP_SHARING_TYPES.SIGN_AT_IPAD,
      })
    );
    return response;
  }

  await dispatch({ type: ACTION_TYPES.SET_ESIGN_SESSION_INFO, payload: sessionInfo });
  dispatch(toggleESignSessionActive());
  return true;
};

export const toggleESignSessionActive = () => async (dispatch, getState) => {
  const isESignSessionActive = getESignSessionActive(getState()[BASE_REDUCER_KEY]);
  dispatch({ type: ACTION_TYPES.SET_ESIGN_SESSION_ACTIVE, payload: !isESignSessionActive });
};

export const getPersonalisedOffers = (program, dealNumber) => async dispatch => {
  const { response } = await DeskingAPI.fetchPersonalisedOffers(program, dealNumber);
  if (response) {
    dispatch({ type: ACTION_TYPES.SET_DYNAMIC_PRICING_DETAILS, payload: _get(response, 'termAndMileageDataList') });
  }
};

export const addCurrentUserToDSPAndGetUrl = () => async (_, getState) => {
  const deal = getDeal(getState()[BASE_REDUCER_KEY]);
  const addDealerUserPayload = getAddNewParticipantToDSPPayload();
  const { response = EMPTY_OBJECT } = await DeskingAPI.addDealerToDSP(
    addDealerUserPayload,
    DealReader.getDealNumber(deal)
  );
  return getSigningUrlFromAddUserResponse(response);
};

// This action is speicifically used for the new eSign platform.
const clearAllPreviousSigningSessions = () => async dispatch => {
  // Required to remove the previous iPad session data
  StopShare.hide();
  await dispatch(
    dspSendNotificationToiPad({ castingRequestType: IPAD_CASTING_REQUEST_TYPES.STOP_CASTING, isClearingSession: true })
  );
  await dispatch(clearESignSessionInfo());
};

export const refreshDealForVatInfo = dealNumber => async dispatch => {
  const { response, error } = await DeskingAPI.refreshDealForVatInfo(dealNumber);
  if (response) {
    dispatch(updateStorePaymentDetails(response));
    setDealVersion(response);
  } else {
    toaster(TOASTER_TYPE.ERROR, getAPIError(error) || __('Something went wrong'));
  }
  return { response };
};

export const getESignPlatformSessionStatus = () => async (_, getState) => {
  const deal = getDeal(getState()[BASE_REDUCER_KEY]);
  const userInfo = TEnvReader.userInfo();
  const { response = null } = await DeskingAPI.getESignPlatformSessionStatus(
    DealReader.getDealNumber(deal),
    UserReader.id(userInfo)
  );

  return !_isEmpty(response);
};

export const setLenderContraSettlementsSupportedFlag = (columnId, contraSettlementsSupported) => async dispatch => {
  await dispatch({
    type: ACTION_TYPES.SET_LENDER_CONTRA_SETTLEMENT_SUPPORTED_FLAG,
    payload: { columnId, contraSettlementsSupported },
  });
};

export const setShowEmiToggleForGivenProduct = (showMonthlyPayment, productType) => async dispatch => {
  await dispatch({
    type: ACTION_TYPES.SET_SHOW_EMI_FLAG,
    payload: { showMonthlyPayment, productType },
  });
};

export const getDealObj = () => async (_, getState) => getDeal(getState()[BASE_REDUCER_KEY]);

const getSinglePreOrderDetails = reservationId => async () => {
  const response = await fetchSinglePreOrderDetails(reservationId);
  return _get(response, 'data.data', EMPTY_ARRAY);
};

const getSingleCustomOrderDetails = orderId => async () => {
  const response = await fetchSingleCustomOrderDetails(orderId);
  return _get(response, 'data.data', EMPTY_ARRAY);
};

const fetchOrderDetails = async (id, fetchFunction) => {
  if (_isEmpty(id)) return { orderNumber: EMPTY_STRING, createdTime: EMPTY_STRING };
  const { ORDER_DETAILS } = await fetchFunction(id)();
  return { id, orderNumber: _get(ORDER_DETAILS, 'orderNumber'), createdTime: _get(ORDER_DETAILS, 'createdTime') };
};

export const fetchCustomerAndReservedOrderDates = async (orderId, reservationId) => {
  if (!_isEmpty(orderId) || !_isEmpty(reservationId)) {
    try {
      const response = await Promise.all([
        fetchOrderDetails(orderId, getSingleCustomOrderDetails),
        fetchOrderDetails(reservationId, getSinglePreOrderDetails),
      ]);
      return response;
    } catch (e) {
      toaster('error', __('Failed to fetch order details'));
    }
  }
};

export const updateAssociatedOriginalDealNumber = payload => dispatch => {
  dispatch({
    type: ACTION_TYPES.SET_ASSOCIATED_ORIGINAL_DEAL_NUMBER,
    payload,
  });
};

export const setInvoiceData = payload => dispatch => {
  dispatch({
    type: ACTION_TYPES.SET_INVOICE_DATA,
    payload,
  });
};

export const updateDealForVehicleUpdate = () => async (dispatch, getState) => {
  const state = getState()[BASE_REDUCER_KEY];
  const dealNumber = _get(state, 'desking.deal.data.dealNumber');
  const newDealObject = await _fetchDeal(dealNumber);
  if (newDealObject) {
    await dispatch([...getUpdateStoreDetailsActionList(newDealObject)]);
  }
};

export const setReSignDeclinationSheetStatus = status => async dispatch => {
  if (!IS_RE_SIGN_DECLINATION_MANDATORY) {
    return;
  }

  dispatch({
    type: ACTION_TYPES.RE_SIGN_DECLINATION_SHEET,
    payload: status,
  });
};

export const getDeclinationSheetStatus = dealNumber => async dispatch => {
  if (!IS_RE_SIGN_DECLINATION_MANDATORY) {
    return;
  }

  const { response, error } = await DeskingAPI.getDeclinationSheetResignStatus(dealNumber);
  const status = _get(response, 'data');
  if (error) {
    toaster(TOASTER_TYPE.ERROR, __('Failed to fetch declination sheet status'));
  }

  dispatch(setReSignDeclinationSheetStatus(status));
};

export const fetchApprovalDetailsOnTradeInFieldsUpdate =
  ({ deal, updatedTradeIns }) =>
  async dispatch => {
    const dealNumber = DealReader.getDealNumber(deal);
    await DeskingAPI.updateCustomerApprovalDetails({ dealNumber, updatedTradeIns });
    await dispatch(fetchCustomerApprovalDetails(deal));
  };

export const fetchCustomerApprovalDetails = deal => async dispatch => {
  const dealNumber = DealReader.getDealNumber(deal);
  const response = await DeskingAPI.fetchCustomerApprovalDetails({ dealNumber });
  const approvalData = _get(response, 'data.approvalIdDetails') || EMPTY_ARRAY;
  const { tradeInApprovalData, rebateApprovalData } = getApprovalDetails(approvalData);
  await dispatch(setTradeInApprovalDetails(tradeInApprovalData));
  await dispatch(setRebateApprovalDetails(rebateApprovalData));
};

export const updateCustomerApprovalDetails =
  ({ action, deal, modifiedRebateIds = [], deletedRebateIds = [], allConsumerRebatesDeleted }) =>
  async dispatch => {
    const dealNumber = DealReader.getDealNumber(deal);
    const payload = getPayloadForUpdateCustomerApprovalDetails({
      action,
      dealNumber,
      modifiedRebateIds,
      deletedRebateIds,
      allConsumerRebatesDeleted,
    });
    await DeskingAPI.updateCustomerApprovalDetails(payload);
    await dispatch(fetchCustomerApprovalDetails(deal));
  };

export const setRecapApprovalDetails = payload => async dispatch => {
  await dispatch({
    type: ACTION_TYPES.SET_RECAP_APPROVAL_DETAILS,
    payload,
  });
};

export const setRecapApprovalStatus = payload => async (dispatch, getState) => {
  await dispatch({
    type: ACTION_TYPES.SET_RECAP_APPROVAL_STATUS,
    payload,
  });
};

export const setRecapApprovalRequestId = payload => async (dispatch, getState) => {
  await dispatch({
    type: ACTION_TYPES.SET_RECAP_APPROVAL_REQUEST_ID,
    payload,
  });
};

export const setRecapApprovalRequestDetails = payload => async (dispatch, getState) => {
  await dispatch({
    type: ACTION_TYPES.SET_RECAP_APPROVAL_REQUEST_DETAILS,
    payload,
  });
  const recapApprovalDetails = await _get(getState()[BASE_REDUCER_KEY], 'desking.recapApprovalDetails');
  return recapApprovalDetails;
};

export const fetchRecapApprovalRequestDetails = requestId => async dispatch => {
  if (_isEmpty(requestId)) return;
  const requestDetails = await getApprovalRequestByIdV3(requestId);
  if (_isEmpty(requestDetails)) {
    toaster(TOASTER_TYPE.ERROR, __('Failed to get request details'));
    return;
  }
  const status = _get(requestDetails, 'approval.status');
  const id = _get(requestDetails, 'approval.id');
  const lastModifiedBy = _get(requestDetails, 'approval.lastModifiedBy');
  let userDetails = {};
  if (!_isEmpty(lastModifiedBy)) {
    userDetails = await fetchUserByIds([lastModifiedBy]);
  }
  await dispatch(setRecapApprovalRequestId(id));
  await dispatch(setRecapApprovalStatus(status));
  await dispatch(setRecapApprovalRequestDetails({ ...requestDetails, modifiedByDetails: userDetails }));
  return requestDetails;
};

export const fetchRecapApprovalDetails = deal => async dispatch => {
  const dealNumber = DealReader.getDealNumber(deal);
  const recapEditPermission = hasFniEdit() && hasCostAndGrossView();
  const response = await DeskingAPI.getRecapApprovalDetails({ dealNumber, recapEditPermission });
  if (_isEmpty(response)) return;
  await dispatch(setRecapApprovalDetails(response));
  const isWorkFlowRequired = recapApprovalDataReader.workflowEnabled(response);
  const requestId = _get(response, 'dealRecapApproval.requestId');
  if (isWorkFlowRequired) {
    if (_isEmpty(requestId)) {
      await dispatch(setRecapApprovalStatus('NOT_SENT'));
      return;
    }
    await dispatch(fetchRecapApprovalRequestDetails(requestId));
  }
};

export const updateRecapApprovalRequestId = payload => async dispatch => {
  await DeskingAPI.updateRecapApprovalRequestId(payload);
};

export const createRecapApprovalRequest = payload => async dispatch => {
  const response = await DeskingAPI.createRecapApprovalRequest(payload);
  if (_isEmpty(response)) {
    toaster(TOASTER_TYPE.ERROR, __('Failed to create recap approval request'));
    return;
  }
  toaster(TOASTER_TYPE.SUCCESS, __('Recap approval requested successfully.'));
  return true;
};

export const resubmitRecapApprovalRequest = payload => async dispatch => {
  const response = await DeskingAPI.resubmitRecapApprovalRequest(payload);
  if (_isEmpty(response)) {
    toaster(TOASTER_TYPE.ERROR, __('Failed to resubmit recap approval request'));
    return;
  }
  toaster(TOASTER_TYPE.SUCCESS, __('Recap approval resubmitted successfully.'));
  return true;
};

export const removeTradeInMediaDetailsForDeletedTradeIns = deletedTradeIns => async dispatch => {
  if (_isEmpty(deletedTradeIns)) {
    return;
  }

  const deletedTradeinVehicleIds = _map(deletedTradeIns, getTradeInVehicleId);
  await dispatch({
    type: ACTION_TYPES.REMOVE_TRADE_IN_MEDIA_DETAILS_FOR_DELETED_TRADEINS,
    payload: { deletedTradeinVehicleIds },
  });
};

export const getTradeInMediaDetails = dealNumber => async dispatch => {
  const response = await DeskingAPI.getTradeInMediaDetails(dealNumber);
  const tradeInVehicleMediaAssociations = getTradeInVehicleMediaAssociations(response);

  const tradeInMediaDetails = formatTradeInMediaDetailsToForm(tradeInVehicleMediaAssociations);
  await dispatch(setTradeInMediaDetails(tradeInMediaDetails));
};

export const saveTradeInMediaDetails = (dealNumber, dealTradeInMediaList) => async dispatch => {
  const payload = formatTradeInMediaDetailsToPayload(dealTradeInMediaList, dealNumber);
  const response = await DeskingAPI.saveTradeInMediaDetails(dealNumber, payload);
  const tradeInVehicleMediaAssociations = getTradeInVehicleMediaAssociations(response);
  const tradeInMediaDetails = formatTradeInMediaDetailsToForm(tradeInVehicleMediaAssociations);
  await dispatch(setTradeInMediaDetails(tradeInMediaDetails));
};

export const setDoNotSendCrmUpdateEventFlag = createAction(ACTION_TYPES.SET_DO_NOT_SEND_CRM_UPDATE_EVENT_FLAG);
