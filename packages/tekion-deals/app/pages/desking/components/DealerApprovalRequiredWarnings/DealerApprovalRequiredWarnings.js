import React, { memo, useCallback } from 'react';
import PropTypes from 'prop-types';
import produce from 'immer';
import _get from 'lodash/get';
import _set from 'lodash/set';
import _uniqBy from 'lodash/uniqBy';

import Button from '@tekion/tekion-components/src/atoms/Button';
import { getRebatesSuperset } from '@tekion/tekion-base/marketScan/readers/desking.reader';
import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import { getRebateProviderType } from '@tekion/tekion-base/marketScan/readers/deal.reader';

import withDeskingContext from 'pages/desking/withDeskingContext';
import { LEFT_PANEL_ITEMS } from 'pages/desking/desking.constants';
import GlobalWarning from 'pages/desking/components/globalWarnings';
import { getCustomerRebatesThatRequireApproval, getTargetDealVehiclePaymentDetailsIndex } from 'utils/rebateUtils';
import { getCustomerTradeInsThatRequireApproval } from 'pages/desking/components/subHeader/subHeaderTabs/tradeInValuation/tradeInValuation.utils';
import { DEAL_STATUS, DEAL_STATUS_WEIGHTS } from 'pages/deallist/deal.constants';
import CalcEngineProperties from 'utils/CalcEngineProperties';

import { DynamicPropertyHelper } from '@tekion/tekion-widgets/src/helpers/dynamicPropertyHelper';
import TradeInApprovalModal from './TradeInApprovalModal';
import RebateApprovalModal from './RebateApprovalModal';
import { DEAL_REBATE_KEYS as REBATE_KEYS, DEALER_APPROVAL_STATUS_ENUM } from '../rebate/Rebate.constants';

const CUSTOMER_ADDED_REBATE_ID = 'customerAddedRebates';
const CUSTOMER_ADDED_TRADEIN_ID = 'customerAddedTradeins';

const DealerApprovalRequiredWarnings = ({
  deal,
  deskingpaymentDetails,
  DeskingActions,
  getMarketScanData,
  updateDealItemPaymentDetails,
  syncPaymentDetails,
  isDealLocked,
  isDealViewOnly,
  isDeskingViewOnly,
  saveDealData,
}) => {
  const showTradeInApprovalModal = useCallback(() => {
    TradeInApprovalModal.show();
  }, []);
  const showRebatesApprovalModal = useCallback(() => {
    RebateApprovalModal.show();
  }, []);

  const showDealerApprovalWarnings = useCallback(
    async (tradeInRequireApproval, rebatesRequireApproval) => {
      const existingWarnings = GlobalWarning.getCustomWarnings(LEFT_PANEL_ITEMS.DESKING);
      const existingWarningsIds = existingWarnings?.map(({ id }) => id);

      const newWarnings = [];
      const warningsToRemove = [];

      if (!existingWarningsIds?.includes(CUSTOMER_ADDED_TRADEIN_ID) && tradeInRequireApproval?.length > 0) {
        newWarnings.push({
          id: CUSTOMER_ADDED_TRADEIN_ID,
          type: 'WARNING',
          sortOrder: 0,
          info: __('Customer Added a Trade-in. Verify & Approve the Change.'),
          renderActionComponents: () => (
            <Button view={Button.VIEW.TERTIARY} onClick={showTradeInApprovalModal}>
              {__('Approve Trade-in')}
            </Button>
          ),
        });
      } else if (existingWarningsIds?.includes(CUSTOMER_ADDED_TRADEIN_ID) && tradeInRequireApproval?.length === 0) {
        warningsToRemove.push(CUSTOMER_ADDED_TRADEIN_ID);
      }

      if (!existingWarningsIds?.includes(CUSTOMER_ADDED_REBATE_ID) && rebatesRequireApproval?.length > 0) {
        newWarnings.push({
          id: CUSTOMER_ADDED_REBATE_ID,
          type: 'WARNING',
          sortOrder: 1,
          info: __('Customer Added Rebates. Verify & Approve the Changes.'),
          renderActionComponents: () => (
            <Button view={Button.VIEW.TERTIARY} onClick={showRebatesApprovalModal}>
              {__('Approve Rebates')}
            </Button>
          ),
        });
      } else if (existingWarningsIds?.includes(CUSTOMER_ADDED_REBATE_ID) && rebatesRequireApproval?.length === 0) {
        warningsToRemove.push(CUSTOMER_ADDED_REBATE_ID);
      }

      if (newWarnings.length || warningsToRemove.length) {
        const finalOldWarnings = existingWarnings?.filter(({ id }) => !warningsToRemove.includes(id));
        const finalWarnings = _uniqBy([...newWarnings, ...finalOldWarnings], 'id');
        GlobalWarning.showCustomWarnings(LEFT_PANEL_ITEMS.DESKING, finalWarnings);
      }
    },
    [showTradeInApprovalModal, showRebatesApprovalModal]
  );

  const approveCustomerTradeIn = useCallback(
    async (id, modifiedTradeIn, formDataChanged) => {
      const modifiedNewTradeIns = _get(deal, 'tradeIns');
      await DeskingActions.setTradeInDeal(
        produce(modifiedNewTradeIns, draft => {
          _set(draft, id, modifiedTradeIn);
        })
      );
      if (CalcEngineProperties.updateCalculationByBackend()) {
        await updateDealItemPaymentDetails({ tradeIns: modifiedNewTradeIns }, false);
      } else {
        if (formDataChanged && DynamicPropertyHelper.isRestrictCRMEventFlowEnabled()) {
          await DeskingActions.setDoNotSendCrmUpdateEventFlag(true);
        }
        await saveDealData();
        if (formDataChanged) {
          await getMarketScanData();
        }
      }
    },
    [deal, DeskingActions, getMarketScanData, updateDealItemPaymentDetails, saveDealData]
  );

  const approveCustomerRebates = useCallback(
    async (approvedRebates, deletedRebateIds) => {
      const oldRebates = getRebatesSuperset({ deskingpaymentDetails }) || EMPTY_ARRAY;
      const allRebates = oldRebates
        ?.filter(({ [REBATE_KEYS.REBATE_ID]: id }) => !deletedRebateIds?.includes(id))
        ?.map(reb => ({ ...reb, [REBATE_KEYS.DEALER_APPROVAL_STATUS]: DEALER_APPROVAL_STATUS_ENUM.APPROVED }));

      const rebateInDealStructure = {};
      (deskingpaymentDetails || []).forEach((det, index) => {
        rebateInDealStructure[index] = allRebates;
      });
      const rebateProvider = getRebateProviderType(deal);
      const vehicleIndex = getTargetDealVehiclePaymentDetailsIndex(deal, deskingpaymentDetails);

      await DeskingActions.setRebates({ rebateInDealStructure, rebateProvider, vehicleIndex });
      if (CalcEngineProperties.updateCalculationByBackend()) {
        await syncPaymentDetails();
      } else {
        await getMarketScanData();
      }
    },
    [DeskingActions, deal, deskingpaymentDetails, syncPaymentDetails, getMarketScanData]
  );

  const tradeInRequireApproval = getCustomerTradeInsThatRequireApproval(_get(deal, 'tradeIns') || []);
  const rebatesRequireApproval = getCustomerRebatesThatRequireApproval(
    getRebatesSuperset({ deskingpaymentDetails }) || EMPTY_ARRAY
  );
  showDealerApprovalWarnings(tradeInRequireApproval, rebatesRequireApproval);

  const { status } = deal || EMPTY_OBJECT;
  const disableTradeInConfirmBtn =
    isDealLocked || DEAL_STATUS_WEIGHTS[status] >= DEAL_STATUS_WEIGHTS[DEAL_STATUS.BOOKED];

  return (
    <>
      <PropertyControlledComponent controllerProperty={tradeInRequireApproval.length > 0}>
        <TradeInApprovalModal
          approveCustomerTradeIn={approveCustomerTradeIn}
          tradeIn={tradeInRequireApproval[0]}
          id={tradeInRequireApproval[0]?.index}
          isDealViewOnly={isDealViewOnly}
          disableTradeInConfirmBtn={disableTradeInConfirmBtn}
        />
      </PropertyControlledComponent>

      <PropertyControlledComponent controllerProperty={rebatesRequireApproval.length > 0}>
        <RebateApprovalModal
          approveCustomerRebates={approveCustomerRebates}
          rebatesRequireApproval={rebatesRequireApproval}
          disableRebateUpdatesBtn={isDeskingViewOnly}
        />
      </PropertyControlledComponent>
    </>
  );
};

DealerApprovalRequiredWarnings.propTypes = {
  deal: PropTypes.object,
  deskingpaymentDetails: PropTypes.object,
  DeskingActions: PropTypes.func.isRequired,
  updateDealItemPaymentDetails: PropTypes.func.isRequired,
  getMarketScanData: PropTypes.func.isRequired,
  syncPaymentDetails: PropTypes.func.isRequired,
  isDealViewOnly: PropTypes.bool.isRequired,
  isDealLocked: PropTypes.bool.isRequired,
  isDeskingViewOnly: PropTypes.bool.isRequired,
  saveDealData: PropTypes.func.isRequired,
};

DealerApprovalRequiredWarnings.defaultProps = {
  deal: EMPTY_OBJECT,
  deskingpaymentDetails: EMPTY_OBJECT,
};

export default withDeskingContext(memo(DealerApprovalRequiredWarnings));
