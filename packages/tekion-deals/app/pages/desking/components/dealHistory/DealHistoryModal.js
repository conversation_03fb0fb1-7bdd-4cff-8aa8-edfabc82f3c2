import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import _get from 'lodash/get';
import _noop from 'lodash/noop';
import _isEmpty from 'lodash/isEmpty';

import Modal from '@tekion/tekion-components/src/organisms/modalWithConfirmation';

import Spinner from '@tekion/tekion-components/src/molecules/SpinnerComponent';
import Radio from '@tekion/tekion-components/src/atoms/Radio';
import DeskingAPI from 'pages/desking/desking.api';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import PropertyControlledComponentWithMessage from 'pages/desking/components/propertyControlledComponentWithMessage';
import { EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import { isDealStatusGreaterThanStatus } from '@tekion/tekion-base/marketScan/readers/deal.reader';

import { getArrayLength } from 'utils';
import withDeskingContext from 'pages/desking/withDeskingContext';
import { DEAL_STATUS } from 'pages/deallist/deal.constants';
import DealHistoryItem from 'pages/desking/components/dealHistory/DealHistoyItem';
import styles from './dealHistory.module.scss';

import withIntersectionObserver from './IntersectionObserver';

const SpinnerWithIntersectionObserver = withIntersectionObserver(Spinner, {
  threshold: 0,
});
const PAGE_SIZE = 5;

class DealHistoryModal extends PureComponent {
  state = {
    offset: 0,
    data: [],
    loading: false,
    moreDataToBeFetched: true,
    selectedIndex: 0,
    totalSize: Infinity,
    dataDirty: false,
  };

  fetchDetails = async offset => {
    const { dealNumber } = this.props;
    const payload = {
      offset,
      pageSize: PAGE_SIZE,
    };

    const res = await DeskingAPI.getAuditLogListForDealSheet(dealNumber, payload);

    const data = _get(res, 'dealSheetResponseList') || EMPTY_ARRAY;
    const totalSize = _get(res, 'total') || data.length;
    const validData = [];
    data.forEach(dealSheetData => {
      try {
        const deal = JSON.parse(_get(dealSheetData, 'jsonEvent'));
        if (deal && !_isEmpty(deal)) {
          deal.dealSheetCreatedBy = _get(dealSheetData, 'user');
          deal.dealSheetCreatedTime = _get(dealSheetData, 'createdTime');
          validData.push(deal);
        }
      } catch {
        // Check for if JSON.parse causes error
      }
    });
    return {
      data: validData,
      totalSize,
    };
  };

  fetchMoreDetailsIfCurrentPageNotFilled = () => {
    const { data, offset, moreDataToBeFetched } = this.state;
    if (moreDataToBeFetched && getArrayLength(data) < offset * PAGE_SIZE) this.intersectionCallback();
  };

  intersectionCallback = () => {
    const { loading, moreDataToBeFetched } = this.state;
    if (moreDataToBeFetched && !loading) {
      this.setState({ loading: true }, async () => {
        const { offset, data } = this.state;
        const { data: dataToBePushed, totalSize } = await this.fetchDetails(offset);
        if (dataToBePushed && dataToBePushed.length !== 0) {
          const updatedData = [...data, ...dataToBePushed];
          const moreResults = getArrayLength(updatedData) < totalSize;
          this.setState({
            data: updatedData,
            offset: moreResults ? offset + 1 : offset,
            moreDataToBeFetched: moreResults,
            totalSize,
          });
        } else this.setState({ moreDataToBeFetched: false });
        this.setState({ loading: false }, this.fetchMoreDetailsIfCurrentPageNotFilled);
      });
    }
  };

  setSelectedDealIndexToRestore = ev => {
    this.setState({
      selectedIndex: _get(ev, 'target.value'),
      dataDirty: true,
    });
  };

  onSubmit = () => {
    const { onSubmit } = this.props;
    const { selectedIndex, data } = this.state;
    this.setState({ dataDirty: false });
    onSubmit(data[selectedIndex]);
  };

  onCancel = () => {
    const { onCancel } = this.props;
    this.setState({ dataDirty: false });
    onCancel();
  };

  render() {
    const { onCancel, visible, deal: currentDeal, isDealViewOnly, getLabelFromPaymentOptions } = this.props;
    const { moreDataToBeFetched, data, selectedIndex, totalSize, dataDirty } = this.state;
    const isDataEmpty = _isEmpty(data) && !moreDataToBeFetched;
    const isViewOnly =
      isDealViewOnly || isDealStatusGreaterThanStatus(currentDeal, DEAL_STATUS.DOCS_PRINTED) || _isEmpty(data);

    return (
      <Modal
        visible={visible}
        title={`${__('Deal History')} ${`(${totalSize === Infinity || !totalSize ? '-' : totalSize})`}`}
        width={1000}
        destroyOnClose
        submitBtnText={__('Restore')}
        onCancel={onCancel}
        onSubmit={this.onSubmit}
        okButtonProps={{
          disabled: isViewOnly,
        }}
        needConfirmation={dataDirty}
      >
        <div className={styles.modal}>
          <PropertyControlledComponentWithMessage valid={!isDataEmpty} message={__('No History Data Found.')}>
            {
              <Radio
                radios={data.map((dealData, index) => ({
                  value: index,
                  label: (
                    <DealHistoryItem
                      deal={dealData}
                      index={index}
                      getLabelFromPaymentOptions={getLabelFromPaymentOptions}
                    />
                  ),
                }))}
                value={selectedIndex}
                onChange={this.setSelectedDealIndexToRestore}
                disabled={isViewOnly}
              />
            }
            <PropertyControlledComponent controllerProperty={moreDataToBeFetched}>
              <SpinnerWithIntersectionObserver intersectionCallback={this.intersectionCallback} />
            </PropertyControlledComponent>
          </PropertyControlledComponentWithMessage>
        </div>
      </Modal>
    );
  }
}

DealHistoryModal.propTypes = {
  onSubmit: PropTypes.func,
  onCancel: PropTypes.func,
  visible: PropTypes.bool,
  dealNumber: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
  deal: PropTypes.object.isRequired,
  isDealViewOnly: PropTypes.bool.isRequired,
};

DealHistoryModal.defaultProps = {
  onSubmit: _noop,
  onCancel: _noop,
  visible: false,
};

export default withDeskingContext(DealHistoryModal);
