import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';

import Popover from '@tekion/tekion-components/src/molecules/popover';
import Button from '@tekion/tekion-components/src/atoms/Button';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import _noop from 'lodash/noop';

import DealHistoryModalV1 from '@tekion/tekion-widgets/src/organisms/dealHistory/DealHistoryModal';
import dealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';
import DealHistoryModal from './DealHistoryModal';

class DealHistory extends PureComponent {
  state = {
    visible: false,
  };

  setModalVisible = visible => {
    this.setState({ visible });
  };

  onButtonClick = () => {
    this.setModalVisible(true);
  };

  onSubmit = deal => {
    const { onDealRestore } = this.props;
    this.setModalVisible(false);
    onDealRestore(deal);
  };

  onCancel = () => {
    this.setModalVisible(false);
  };

  getPopoverContent = () => <div className="marginL16 marginR16">{__('Deal History')}</div>;

  render() {
    const { visible } = this.state;
    const { dealNumber, salesSetupInfo, getLabelFromPaymentOptions } = this.props;
    return (
      <>
        <Popover trigger="hover" content={this.getPopoverContent()}>
          <Button view={Button.VIEW.ICON} icon="icon-history" className="marginL16" onClick={this.onButtonClick} />
        </Popover>
        <PropertyControlledComponent controllerProperty={visible}>
          {dealerPropertyHelper.isInchcapeEnabled() ? (
            <DealHistoryModalV1
              visible={visible}
              onCancel={this.onCancel}
              dealNumber={dealNumber}
              salesSetupInfo={salesSetupInfo}
            />
          ) : (
            <DealHistoryModal
              visible={visible}
              onSubmit={this.onSubmit}
              onCancel={this.onCancel}
              dealNumber={dealNumber}
              getLabelFromPaymentOptions={getLabelFromPaymentOptions}
            />
          )}
        </PropertyControlledComponent>
      </>
    );
  }
}

DealHistory.propTypes = {
  dealNumber: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
  onDealRestore: PropTypes.func,
  getLabelFromPaymentOptions: PropTypes.func.isRequired,
};

DealHistory.defaultProps = {
  onDealRestore: _noop,
};

export default DealHistory;
