import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';

import { defaultMemoize } from 'reselect';
import { hasViewSubscreensInBookedDeals, hasCostAndGrossView } from 'permissions/desking.permissions';
import { VEHICLE_PRICING_KEYS } from '@tekion/tekion-base/marketScan/constants/constants';

import { isInchcape, isRRG } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
import * as DealReader from '@tekion/tekion-base/marketScan/readers/deal.reader';
import { DEAL_TYPES } from '@tekion/tekion-base/constants/deal/type';
import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import { getSelectedSellingPrice } from '@tekion/tekion-base/marketScan/utils/desking.utils';
// import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';

import ModalDropDown from 'molecules/modalDropDown';
import * as VehicleReader from 'utils/vehicleReader';
import Trigger from 'pages/desking/components/subHeader/subHeaderTabs/trigger/Trigger';
import SellingPrice from 'pages/desking/components/subHeader/subHeaderTabs/sellingPrice/SellingPrice';
import SellingPriceV2 from 'pages/desking/components/subHeader/subHeaderTabs/sellingPrice/SellingPriceV2';
import { formatCurrencyWithMoneyFormat, getNumber } from 'utils';
import withDefault from 'pages/desking/withDefault';
import { getSellingPriceSuggestion, isSellingPriceTabDataChanged } from 'pages/deallist/deal.util';
import withDeskingContext from 'pages/desking/withDeskingContext';
import { isInchCapeOrRrg } from 'utils/desking.helpers';
import withCRMLeadHeartbeatStatus from 'pages/desking/withCRMLeadHeartbeatStatus';
import CRMLeadHeartbeat from 'utils/CRMLeadHeartbeat';
import CalcEngineProperties from 'utils/CalcEngineProperties';
import { getRetailPrice, isDealClosedOrPreclosed } from './utils/sellingPriceTab.utils';

import { onSellingPriceTabSave } from './SellingPriceTabEvents';

class SellingPriceTab extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      glBalance: '',
      isTradeInUpdated: false,
      isVehicleUpdated: false,
      visible: false,
    };
    this.setOldDeal();
  }

  setOldDeal = () => {
    const { deal } = this.props;
    this.oldDeal = deal;
  };

  getPricesThatAreNotRequired = defaultMemoize(() => {
    if (hasCostAndGrossView()) return EMPTY_ARRAY;
    return [VEHICLE_PRICING_KEYS.INVOICE_PRICE];
  });

  setTreadeinDirty = value => {
    this.setState({ isTradeInUpdated: value });
  };

  setVehicleDirty = value => {
    this.setState({ isVehicleUpdated: value });
  };

  onPressEnter = async () => {
    const { saveSellingPrice, deal, saveDealData, DeskingActions, viewOnly } = this.props;
    if (viewOnly) return;

    const { deal: dealUpdatedWithGlBalance, isDealDirty, updateDealItemPaymentDetails } = this.props;
    const { isTradeInUpdated, isVehicleUpdated } = this.state;
    const tradeIns = isTradeInUpdated ? { tradeIns: deal?.tradeIns } : {};
    if (isDealDirty) {
      await saveSellingPrice(null, tradeIns, isVehicleUpdated);
      this.setTreadeinDirty(false);
      this.setVehicleDirty(false);
      DeskingActions.setDealDirtyStatus(false);
    } else if (isSellingPriceTabDataChanged(this.oldDeal, dealUpdatedWithGlBalance)) {
      if (CalcEngineProperties.updateCalculationByBackend()) {
        updateDealItemPaymentDetails();
      } else {
        saveDealData();
      }
    }
  };

  getGLBalance = async () => {
    const { DeskingActions, deal, CommonActions, dealGlBalance, saveDealData } = this.props;
    const primaryVehicle = DealReader.getPrimaryVehicle(deal);
    const vehicleId = VehicleReader.getVehicleId(primaryVehicle);
    const dealStatus = DealReader.getDealStatus(deal);

    // only if the deal status is not pre closed or sold or closed then fetch GL balance and save
    if (!isDealClosedOrPreclosed(dealStatus)) {
      await CommonActions.getVehiclesById([vehicleId]);
      let glBalance;
      if (vehicleId) {
        glBalance = await DeskingActions.getGLBalance(vehicleId);
      } else {
        glBalance = DealReader.getGLBalanceOfPrimaryVehicle(deal);
      }
      this.setState({ glBalance });

      // GL Balance has to be updated every time from the API, if different.
      if (getNumber(glBalance) && getNumber(glBalance) !== getNumber(dealGlBalance)) {
        await DeskingActions.setPrimaryVehicleInfo({ key: 'pricingDetails.glBalance', value: glBalance });
        saveDealData();
      }
    }
  };

  renderSPTrigger = () => {
    const { deal, viewOnly, selectedColumn } = this.props;
    const sellingPrice = getSelectedSellingPrice(deal, selectedColumn);
    const isFNIDeal = deal.type === DEAL_TYPES.FNI;
    return (
      <Trigger
        title={sellingPrice && !isFNIDeal ? formatCurrencyWithMoneyFormat(sellingPrice) : '–'}
        subTitle={__('Selling Price')}
        viewOnly={viewOnly}
      />
    );
  };

  toggleVisible = () => {
    const { visible } = this.state;
    if (visible) {
      if (isInchcape()) {
        onSellingPriceTabSave();
      } else {
        this.onPressEnter();
      }
    }
    const { crmHeartbeatStatus } = this.props;
    if (!crmHeartbeatStatus) {
      CRMLeadHeartbeat.updateCRMLeadHeartbeatStatus(
        { vehicleTabOpened: !visible },
        { isDelayedUpdate: !visible === false }
      );
    }
    this.setState({
      visible: !visible,
    });
  };

  render() {
    const {
      deal,
      DeskingActions,
      viewOnly,
      getJournalDetails,
      getAccountDetails,
      selectedColumn,
      updateRepairOrderPurchaseOrderAdjustment,
      dealGlBalance,
      canAdjustSellingPriceFromProfit,
      disablePricingEditSettings,
      showMaximumResidualMSRP,
      deskingpaymentDetails,
      discountCapConfig,
      crmHeartbeatStatus,
      discountMatrixConfig,
    } = this.props;
    const { glBalance, visible } = this.state;
    const invoicePrice = DealReader.getInvoicePriceOfPrimaryVehicle(deal);
    const loanSellingPrice = DealReader.getLoanSellingPriceOfPrimaryVehicle(deal);
    const leaseSellingPrice = DealReader.getLeaseSellingPriceOfPrimaryVehicle(deal);
    const originalLoanSellingPrice = DealReader.getOriginalLoanSellingPriceOfPrimaryVehicle(deal);
    const originalLeaseSellingPrice = DealReader.getOriginalLeaseSellingPriceOfPrimaryVehicle(deal);

    const maxResidualizationMSRP = DealReader.getMRMOfPrimaryVehicle(deal);
    const msrp = DealReader.getMSRPOfPrimaryVehicle(deal);
    const costAdjustments = DealReader.getCostAdjustmentsOfPrimaryVehicle(deal);
    const primaryVehicle = DealReader.getPrimaryVehicle(deal);
    const vehicleId = VehicleReader.getVehicleId(primaryVehicle);
    const discounts = VehicleReader.getDiscounts(primaryVehicle);
    const discountPrice = VehicleReader.getVehicleDiscount(primaryVehicle);
    const taxCode = VehicleReader.getTaxCode(primaryVehicle);
    const appliedDiscounts = isRRG()
      ? VehicleReader.getVehicleDiscount(primaryVehicle)
      : VehicleReader.getSelectedDiscounts(primaryVehicle);
    const pricesToBeNotShown = this.getPricesThatAreNotRequired();
    const sellingPriceSuggestions = getSellingPriceSuggestion(primaryVehicle, pricesToBeNotShown);
    const { type } = deal;
    const Component = isInchcape() ? SellingPriceV2 : SellingPrice;
    const retailPrice = getRetailPrice(deal);

    return (
      <ModalDropDown
        overlay={
          <Component
            costAdjustments={costAdjustments}
            pricingDetails={{
              invoicePrice,
              retailPrice,
              msrp,
              maxResidualizationMSRP,
              glBalance: getNumber(glBalance) || dealGlBalance,
              loanSellingPrice,
              leaseSellingPrice, // GL Balance coming from API has more priority than the one stored in deal
              originalLoanSellingPrice,
              originalLeaseSellingPrice,
              taxCode,
              discount: discountPrice,
            }}
            discountCapConfig={discountCapConfig}
            DeskingActions={DeskingActions}
            onPressEnter={this.onPressEnter}
            getGLBalance={this.getGLBalance}
            vehicleId={vehicleId}
            getJournalDetails={getJournalDetails}
            getAccountDetails={getAccountDetails}
            deal={deal}
            selectedColumn={selectedColumn}
            sellingPriceSuggestions={sellingPriceSuggestions}
            updateRepairOrderPurchaseOrderAdjustment={updateRepairOrderPurchaseOrderAdjustment}
            canAdjustSellingPriceFromProfit={canAdjustSellingPriceFromProfit}
            showMaximumResidualMSRP={showMaximumResidualMSRP}
            discounts={discounts}
            appliedDiscounts={appliedDiscounts}
            disablePricingEditSettings={disablePricingEditSettings}
            viewOnly={viewOnly || crmHeartbeatStatus}
            deskingpaymentDetails={deskingpaymentDetails}
            setTreadeinDirty={this.setTreadeinDirty}
            setVehicleDirty={this.setVehicleDirty}
            discountMatrixConfig={discountMatrixConfig}
          />
        }
        Trigger={this.renderSPTrigger}
        onClick={this.toggleVisible}
        overlayContainerClass=""
        triggerContainerClass=""
        borderWidth={1}
        borderColor="grey"
        placement="bottomRight"
        disabled={
          (viewOnly && !hasViewSubscreensInBookedDeals()) || (isInchCapeOrRrg() && type === DEAL_TYPES.ONLY_TRADES)
        }
        onModalDropDownOpenCb={this.setOldDeal}
        visible={visible}></ModalDropDown>
    );
  }
}

SellingPriceTab.propTypes = {
  deal: PropTypes.string,
  DeskingActions: PropTypes.string,
  viewOnly: PropTypes.bool,
  saveSellingPrice: PropTypes.func.isRequired,
  getJournalDetails: PropTypes.func.isRequired,
  dealGlBalance: PropTypes.number,
  getAccountDetails: PropTypes.func.isRequired,
  CommonActions: PropTypes.func,
  selectedColumn: PropTypes.object,
  isDealDirty: PropTypes.bool.isRequired,
  updateRepairOrderPurchaseOrderAdjustment: PropTypes.func.isRequired,
  saveDealData: PropTypes.func.isRequired,
  canAdjustSellingPriceFromProfit: PropTypes.bool.isRequired,
  disablePricingEditSettings: PropTypes.bool.isRequired,
  showMaximumResidualMSRP: PropTypes.bool.isRequired,
  deskingpaymentDetails: PropTypes.array.isRequired,
  updateDealItemPaymentDetails: PropTypes.func.isRequired,
};

SellingPriceTab.defaultProps = {
  deal: EMPTY_OBJECT,
  DeskingActions: EMPTY_OBJECT,
  viewOnly: false,
  dealGlBalance: 0,
  CommonActions: EMPTY_OBJECT,
  selectedColumn: EMPTY_OBJECT,
};

export default withCRMLeadHeartbeatStatus(withDeskingContext(withDefault(SellingPriceTab)));
