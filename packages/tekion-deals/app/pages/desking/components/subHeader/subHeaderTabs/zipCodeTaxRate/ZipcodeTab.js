import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import _noop from 'lodash/noop';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';
import _get from 'lodash/get';
import cx from 'classnames';

import * as ColumnDataReader from '@tekion/tekion-base/marketScan/readers/columnData.reader';
import * as DealReader from '@tekion/tekion-base/marketScan/readers/deal.reader';
import { EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { formatZipCode } from '@tekion/tekion-base/utils/general';
import {
  getZipCodeTaxRate,
  getFormattedTaxRateOverrides,
} from '@tekion/tekion-base/marketScan/readers/deskingSubHeader.reader';
import { hasViewSubscreensInBookedDeals } from 'permissions/desking.permissions';
import {
  isCategory2Deal,
  isCategory3Deal,
  isCategory1Deal,
  getIsInStateDeal,
} from '@tekion/tekion-base/marketScan/utils/desking.utils';
import {
  TAX_AND_ZIP_CODE_DETAILS_VS_KEY,
  PAYMENT_TYPE_VS_TAX_TYPE,
} from '@tekion/tekion-base/marketScan/constants/desking.constants';

import { isUSDealer } from 'utils/dealerUtils';
import {
  TAX_RATE_MANUALLY_UPDATED_API_KEY,
  TAXES_MANNUALLY_OVERRIDE,
  TOTAL_TAX_OVERRIDDEN,
} from 'pages/desking/desking.constants';
import ModalDropDown from 'molecules/modalDropDown';
import Trigger from 'pages/desking/components/subHeader/subHeaderTabs/trigger/Trigger';
import FontIcon, { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';
import Popover from 'tcomponents/molecules/popover';
import { getNumber } from 'utils';
import withDefault from 'pages/desking/withDefault';
import withDeskingContext from 'pages/desking/withDeskingContext';
import CalcEngineProperties from 'utils/CalcEngineProperties';

import { DROPDOWN_PLACEMENT } from '@tekion/tekion-components/src/molecules/DropDown';
import DeskingAPI from 'pages/desking/desking.api';
import { PropertyControlledComponent } from '@tekion/tekion-components/src/molecules';
import { getMarketId, getCustomerAddressDetails } from '@tekion/tekion-base/marketScan/readers/customer.reader';
import ZipCodeTaxRate from './ZipCodeTaxRate';
import TDPBenefitsPopup from './TDPBenefitsPopup';

import styles from './zipCodeTaxRate.module.scss';
import { getDealKeysToBePicked } from './ZipCodeTab.utils';

class ZipCodeTab extends PureComponent {
  state = {
    overridenLeaseTaxRate: null,
    overridenRetailTaxRate: null,
    isDealerEnrolledForTDP: false,
    showTDPInfo: false,
  };

  componentDidMount() {
    this.checkTdpIntegration();
  }

  componentDidUpdate(prevProps) {
    const { deal } = this.props;
    const prevDeal = prevProps?.deal;

    if (this.checkIfDealInState(deal) !== this.checkIfDealInState(prevDeal)) {
      this.checkTdpIntegration();
    }
  }

  checkIfDealInState = dealData => {
    const { salesSetupInfo } = this.props;
    const buyerAddress = getCustomerAddressDetails(DealReader.getBuyer(dealData));
    const customerState = _get(buyerAddress, 'state') ? _get(buyerAddress, 'state') : null;
    const customerMarketId = !CalcEngineProperties.showGalaxyView()
      ? getMarketId(DealReader.getBuyer(dealData))
      : customerState;
    return getIsInStateDeal(salesSetupInfo, customerMarketId);
  };

  checkTdpIntegration = async () => {
    const { deal } = this.props;

    let stateToUpdate;

    let response;
    const isInStateDeal = this.checkIfDealInState(deal);

    if (!isInStateDeal) {
      try {
        response = await DeskingAPI.fetchCheckDealerEnrolmentStatus();
      } catch (e) {
        // ignore if api fails
      }

      const enrolmentStatus = response?.status || '';
      stateToUpdate = {
        ...(enrolmentStatus ? { isDealerEnrolledForTDP: enrolmentStatus.match(/not enrolled/i) === null } : {}),
        showTDPInfo: !!enrolmentStatus,
      };
    } else {
      stateToUpdate = {
        showTDPInfo: false,
      };
    }

    this.setState(stateToUpdate);
  };

  showTDPIntegrationPopup = () => {
    const { deal, viewOnly, selectedColumn } = this.props;
    const { showTDPInfo } = this.state;

    return (
      showTDPInfo &&
      PAYMENT_TYPE_VS_TAX_TYPE[ColumnDataReader.getPaymentType(selectedColumn)] ===
        TAX_AND_ZIP_CODE_DETAILS_VS_KEY.RETAIL
    );
  };

  renderTitle = () => {
    const { deal, viewOnly, selectedColumn } = this.props;
    const { showTDPInfo } = this.state;
    const { zipCode } = getZipCodeTaxRate(
      deal,
      PAYMENT_TYPE_VS_TAX_TYPE[ColumnDataReader.getPaymentType(selectedColumn)]
    );
    const taxAppliedForSelectedColumn = ColumnDataReader.getTaxRate(selectedColumn);
    const zipCodeText = isUSDealer() ? formatZipCode(zipCode) : zipCode;

    return (
      <div className={cx(styles.zipCodeTabTitle, 'flex align-items-center')}>
        {`${zipCodeText} / ${getNumber(taxAppliedForSelectedColumn)}%`}
        <PropertyControlledComponent controllerProperty={this.showTDPIntegrationPopup()}>
          <Popover
            trigger="hover"
            content={
              <div className="px-1">
                {this.state.isDealerEnrolledForTDP
                  ? __('You can try out Tekion Digital Processing (TDP) by going inside the Zip Code/Tax Rate section')
                  : __(
                      'You can enroll into Tekion Digital Processing (TDP) by going inside the Zip Code/Tax Rate section'
                    )}
              </div>
            }>
            <FontIcon className="ml-auto" size={SIZES.S}>
              icon-info
            </FontIcon>
          </Popover>
        </PropertyControlledComponent>
      </div>
    );
  };

  renderTrigger = () => {
    const { viewOnly, deal, salesSetupInfo } = this.props;
    const { isDealerEnrolledForTDP, showTDPInfo } = this.state;

    return (
      <TDPBenefitsPopup
        deal={deal}
        salesSetupInfo={salesSetupInfo}
        isDealerEnrolledForTDP={isDealerEnrolledForTDP}
        showTDPInfo={this.showTDPIntegrationPopup()}>
        <Trigger title={this.renderTitle()} subTitle={__('$$(ZIP Code) / $$(Tax Rate)')} viewOnly={viewOnly} />
      </TDPBenefitsPopup>
    );
  };

  setFirstRowDataInDeal = collectTaxes => async (firstRowData, type) => {
    const { setDefaultStateFeeTaxOptions, deal, setZipCodeDetailsInDeal } = this.props;
    const { taxGroup, byTaxGroup, reciprocalCategory } = DealReader.getTaxAndZipCodeDetails(deal, type) || EMPTY_OBJECT;
    if (collectTaxes && !_isEmpty(firstRowData) && !_isNil(firstRowData)) {
      const { zipCode } = DealReader.getTaxAndZipCodeDetails(deal, type);
      const taxInformation = { ...firstRowData, taxGroup, byTaxGroup, reciprocalCategory };
      await setZipCodeDetailsInDeal({
        zipCode,
        selectedZipCodeTaxInfo: taxInformation,
        type,
      });
    } else {
      await setDefaultStateFeeTaxOptions(type);
    }
  };

  onCollectTaxSwitchToggle = async (collectTaxes, taxData, type) => {
    const { DeskingActions, deal, updateDealItemPaymentDetails } = this.props;
    const setFirstRowDataWithCollectTaxes = this.setFirstRowDataInDeal(collectTaxes);
    await DeskingActions.setCollectTaxesFlag({
      collectTax: collectTaxes,
      type,
    });
    if (!CalcEngineProperties.updateCalculationByBackend()) {
      if (type) {
        const firstRowData = _get(taxData, [type, 0]);
        await setFirstRowDataWithCollectTaxes(firstRowData, type);
      } else {
        const leaseFirstRowData = _get(taxData, [TAX_AND_ZIP_CODE_DETAILS_VS_KEY.LEASE, 0]);
        const retailFirstRowData = _get(taxData, [TAX_AND_ZIP_CODE_DETAILS_VS_KEY.RETAIL, 0]);
        await setFirstRowDataWithCollectTaxes(leaseFirstRowData, TAX_AND_ZIP_CODE_DETAILS_VS_KEY.LEASE);
        await setFirstRowDataWithCollectTaxes(retailFirstRowData, TAX_AND_ZIP_CODE_DETAILS_VS_KEY.RETAIL);
      }
    }
    if (CalcEngineProperties.updateCalculationByBackend()) {
      const payload = {
        collectTaxRetail: DealReader.collectTaxes(deal, TAX_AND_ZIP_CODE_DETAILS_VS_KEY.RETAIL),
        collectTaxLease: DealReader.collectTaxes(deal, TAX_AND_ZIP_CODE_DETAILS_VS_KEY.LEASE),
      };
      if (type) {
        payload[type === TAX_AND_ZIP_CODE_DETAILS_VS_KEY.LEASE ? 'collectTaxLease' : 'collectTaxRetail'] = collectTaxes;
      } else {
        payload.collectTaxLease = collectTaxes;
        payload.collectTaxRetail = collectTaxes;
      }
      await updateDealItemPaymentDetails(payload, false);
    } else {
      this.saveDealAndRefreshMarketScanIfDirty();
    }
  };

  updateOverriddenTaxRate = type => taxRate => {
    const { DeskingActions } = this.props;
    if (type) {
      if (TAX_AND_ZIP_CODE_DETAILS_VS_KEY.RETAIL === type) this.setState({ overridenRetailTaxRate: taxRate });
      if (TAX_AND_ZIP_CODE_DETAILS_VS_KEY.LEASE === type) this.setState({ overridenLeaseTaxRate: taxRate });
    } else
      this.setState({
        overridenRetailTaxRate: taxRate,
        overridenLeaseTaxRate: taxRate,
      });
    DeskingActions.setTaxAndZipCodeDetails({ mergeData: { [TOTAL_TAX_OVERRIDDEN]: true }, type });
  };

  onTaxOptionsChanges = type => (taxOption, changedValue) => {
    const { DeskingActions } = this.props;
    DeskingActions.setStateFeeTaxOptions({
      options: { [taxOption]: changedValue },
      type,
    });
  };

  updateNewTaxRate = async ({ taxType, taxRate }) => {
    const { DeskingActions, deal, columnDataForTaxDetails, taxFeeConfigMetadata } = this.props;
    const newTaxRate = getNumber(taxRate);
    const oldTaxRate = getNumber(ColumnDataReader.getTaxRate(columnDataForTaxDetails[taxType]));
    if (oldTaxRate === newTaxRate) return;
    const marketId = DealReader.getMarketId(deal, taxType);
    const taxAndZipCodeDetails = DealReader.getTaxAndZipCodeDetails(deal, taxType);
    const formattedTaxRateOverrides = getFormattedTaxRateOverrides(
      newTaxRate,
      marketId,
      taxAndZipCodeDetails,
      taxFeeConfigMetadata?.flatTaxConfig
    );
    await DeskingActions.setTaxAndZipCodeDetails({
      mergeData: {
        ...formattedTaxRateOverrides,
        [TAX_RATE_MANUALLY_UPDATED_API_KEY]: true,
        [TAXES_MANNUALLY_OVERRIDE]: true,
      },
      type: taxType,
    });

    this.onTaxOptionsChanges(taxType)('dontAutoApplyExtraTax', false); // TODO: Tax and fee cleanup
  };

  handleOverrideTaxRate = async () => {
    const { overridenRetailTaxRate, overridenLeaseTaxRate } = this.state;

    if (!_isNil(overridenRetailTaxRate)) {
      await this.updateNewTaxRate({
        taxType: TAX_AND_ZIP_CODE_DETAILS_VS_KEY.RETAIL,
        taxRate: overridenRetailTaxRate,
      });
    }

    if (!_isNil(overridenLeaseTaxRate)) {
      await this.updateNewTaxRate({
        taxType: TAX_AND_ZIP_CODE_DETAILS_VS_KEY.LEASE,
        taxRate: overridenLeaseTaxRate,
      });
    }
    this.setState({
      overridenRetailTaxRate: null,
      overridenLeaseTaxRate: null,
    });
  };

  saveDealAndRefreshMarketScanIfDirty = async ({ params } = EMPTY_OBJECT) => {
    await this.handleOverrideTaxRate();
    const {
      saveDealAndRefreshMarketScanIfDirty,
      addDefaultFee,
      isDealDirty,
      deal,
      updateEMIAmountOfFNIDeal,
      category3dealTypeChanges,
      canCallMarketScan,
      updateDealItemPaymentDetails,
    } = this.props;
    const isGalaxyEnabled = CalcEngineProperties.updateByGalaxyEngine();
    if (isDealDirty) {
      if (CalcEngineProperties.updateCalculationByBackend()) {
        const dealKeysToBePicked = getDealKeysToBePicked(isGalaxyEnabled);
        updateDealItemPaymentDetails({ pickFromDeal: dealKeysToBePicked }, false, params);
      } else {
        if (isCategory2Deal(deal)) {
          await updateEMIAmountOfFNIDeal();
        } else if (isCategory3Deal(deal)) {
          await category3dealTypeChanges();
        }
        if (canCallMarketScan) {
          await addDefaultFee({}, true);
        }

        saveDealAndRefreshMarketScanIfDirty();
      }
    }
  };

  updateDeskingOnZipCodeChange = async (zipCode, firstCounty, type) => {
    const {
      saveDealData,
      deal,
      updateEMIAmountOfFNIDeal,
      setZipCodeDetailsInDeal,
      category3dealTypeChanges,
      canCallMarketScan,
      updateDesking,
    } = this.props;

    if (isCategory1Deal(deal) && canCallMarketScan) {
      updateDesking({
        zipcode: zipCode,
      });
      return;
    }

    await setZipCodeDetailsInDeal({
      zipCode,
      selectedZipCodeTaxInfo: firstCounty,
      type,
    });
    if (isCategory2Deal(deal)) {
      await updateEMIAmountOfFNIDeal();
    } else if (isCategory3Deal(deal)) {
      await category3dealTypeChanges();
    }

    saveDealData();
  };

  render() {
    const {
      deal,
      DeskingActions,
      CommonActions,
      viewOnly,
      tradeTaxExempt,
      setupIntegrationKeys,
      countyTaxRateByMScan,
      generalTaxFeeSettings,
      setZipCodeDetailsInDeal,
      selectedColumnPaymentType,
      enableSeparateTaxCalc,
      columnDataForTaxDetails,
      salesSetupInfo,
      taxFeeConfigMetadata,
      isUSCalcEngineEnabled,
      getFormattedDateAndTime,
    } = this.props;
    const { isDealerEnrolledForTDP, showTDPInfo } = this.state;

    return (
      <ModalDropDown
        overlay={
          <ZipCodeTaxRate
            deal={deal}
            CommonActions={CommonActions}
            DeskingActions={DeskingActions}
            saveDealAndRefreshMarketScanIfDirty={this.saveDealAndRefreshMarketScanIfDirty}
            onCollectTaxSwitchToggle={this.onCollectTaxSwitchToggle}
            countyTaxRateByMScan={countyTaxRateByMScan}
            tradeTaxExempt={tradeTaxExempt}
            setupIntegrationKeys={setupIntegrationKeys}
            generalTaxFeeSettings={generalTaxFeeSettings}
            setZipCodeDetailsInDeal={setZipCodeDetailsInDeal}
            updateDeskingOnZipCodeChange={this.updateDeskingOnZipCodeChange}
            viewOnly={viewOnly}
            selectedColumnPaymentType={selectedColumnPaymentType}
            enableSeparateTaxCalc={enableSeparateTaxCalc}
            columnDataForTaxDetails={columnDataForTaxDetails}
            updateOverriddenTaxRate={this.updateOverriddenTaxRate}
            onTaxOptionsChanges={this.onTaxOptionsChanges}
            salesSetupInfo={salesSetupInfo}
            taxFeeConfigMetadata={taxFeeConfigMetadata}
            isUSCalcEngineEnabled={isUSCalcEngineEnabled}
            isDealerEnrolledForTDP={isDealerEnrolledForTDP}
            showTDPInfo={showTDPInfo}
            getFormattedDateAndTime={getFormattedDateAndTime}
          />
        }
        placement={DROPDOWN_PLACEMENT.BOTTOM_LEFT}
        Trigger={this.renderTrigger}
        overlayContainerClass="overflow-hidden-imp"
        triggerContainerClass=""
        borderWidth={1}
        borderColor="grey"
        onModalDropDownCloseCb={this.saveDealAndRefreshMarketScanIfDirty}
        disabled={viewOnly && !hasViewSubscreensInBookedDeals()}></ModalDropDown>
    );
  }
}

ZipCodeTab.propTypes = {
  deal: PropTypes.object,
  viewOnly: PropTypes.bool,
  DeskingActions: PropTypes.object.isRequired,
  countyTaxRateByMScan: PropTypes.number,
  saveDealAndRefreshMarketScanIfDirty: PropTypes.func.isRequired,
  tradeTaxExempt: PropTypes.number,
  setupIntegrationKeys: PropTypes.object,
  addDefaultFee: PropTypes.func,
  selectedColumnPaymentType: PropTypes.string,
  setDefaultStateFeeTaxOptions: PropTypes.func,
  isDealDirty: PropTypes.bool,
  updateEMIAmountOfFNIDeal: PropTypes.func.isRequired,
  category3dealTypeChanges: PropTypes.func.isRequired,
  CommonActions: PropTypes.object.isRequired,
  generalTaxFeeSettings: PropTypes.object,
  setZipCodeDetailsInDeal: PropTypes.func.isRequired,
  canCallMarketScan: PropTypes.bool.isRequired,
  updateDesking: PropTypes.func.isRequired,
  saveDealData: PropTypes.func,
  enableSeparateTaxCalc: false,
  selectedColumn: PropTypes.object,
  columnDataForTaxDetails: PropTypes.object.isRequired,
  salesSetupInfo: PropTypes.object.isRequired,
  taxFeeConfigMetadata: PropTypes.object.isRequired,
  isUSCalcEngineEnabled: PropTypes.bool.isRequired,
  getFormattedDateAndTime: PropTypes.func,
};

ZipCodeTab.defaultProps = {
  isDealDirty: false,
  deal: EMPTY_OBJECT,
  selectedColumn: EMPTY_OBJECT,
  viewOnly: false,
  countyTaxRateByMScan: 0,
  tradeTaxExempt: 0,
  setupIntegrationKeys: EMPTY_OBJECT,
  generalTaxFeeSettings: EMPTY_OBJECT,
  addDefaultFee: _noop,
  setDefaultStateFeeTaxOptions: _noop,
  selectedColumnPaymentType: EMPTY_STRING,
  saveDealData: _noop,
  enableSeparateTaxCalc: PropTypes.bool,
  getFormattedDateAndTime: _noop,
};

export default withDeskingContext(withDefault(ZipCodeTab));
