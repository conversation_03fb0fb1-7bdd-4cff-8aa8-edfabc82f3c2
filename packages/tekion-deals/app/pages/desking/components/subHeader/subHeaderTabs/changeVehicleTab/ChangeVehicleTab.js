import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import _size from 'lodash/size';
import _find from 'lodash/find';
import _filter from 'lodash/filter';
import _map from 'lodash/map';
import _isFunction from 'lodash/isFunction';
import cx from 'classnames';
import { hasViewSubscreensInBookedDeals } from 'permissions/desking.permissions';
import ModalDropDown from 'molecules/modalDropDown';
import Trigger from 'pages/desking/components/subHeader/subHeaderTabs/trigger/Trigger';
import * as DealReader from '@tekion/tekion-base/marketScan/readers/deal.reader';
import * as SalesSetupReader from '@tekion/tekion-base/marketScan/readers/salesSetup.reader';
import { isCategory1Deal } from '@tekion/tekion-base/marketScan/utils/desking.utils';
import { EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import { DEAL_TYPES } from '@tekion/tekion-base/constants/deal/type';
import { isInchCapeOrRrg } from 'utils/desking.helpers';
import withCRMLeadHeartbeatStatus from 'pages/desking/withCRMLeadHeartbeatStatus';
import CRMLeadHeartbeat from 'utils/CRMLeadHeartbeat';
import { isCanadaDealer } from 'utils/dealerUtils';
import { isArcLiteProgram } from 'utils/program.utils';

import { confirmSite } from 'utils/vehicleSiteConfirm';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';
import { isAllVehiclesHavingSameVehicleCategory } from 'utils/vehicleReader';

import { DynamicPropertyHelper } from '@tekion/tekion-widgets/src/helpers/dynamicPropertyHelper';
import ChangeVehicle from './ChangeVehicle';
import ChangeMultiVehicleStatus from './ChangeMultiVehicleStatus';
import { checkForBuidVehicle } from './changeVehicleTab.utils';

import styles from './ChangeVehicleTab.module.scss';

class ChangeVehicleTab extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      vehicles: props.vehicles,
      visible: false,
    };
  }

  switchMultiVehicleToSingleVehicle = async () => {
    const { deskingpaymentDetails, deal, DeskingActions, savePaymentsDataForSelectedVehicle, saveDealData } =
      this.props;
    const primaryVehicleDealVehicleId = DealReader.getPrimaryVehicleDealVehicleId(deal);
    const newDeskingPaymentDetails = _filter(
      deskingpaymentDetails,
      ({ dealVehicleId }) => dealVehicleId === primaryVehicleDealVehicleId
    );
    this.setState({ visible: false });
    if (DynamicPropertyHelper.isRestrictCRMEventFlowEnabled()) {
      await DeskingActions.setDoNotSendCrmUpdateEventFlag(true);
    }
    await DeskingActions.setColumnsData(newDeskingPaymentDetails);
    await DeskingActions.changeMultiVehicleDeskingStatus(false);
    savePaymentsDataForSelectedVehicle();
    saveDealData();
  };

  switchSingleVehicleToMultiVehicle = async () => {
    const {
      deal,
      updateDeskingPaymentsDataForSelectedVehicles,
      DeskingActions,
      saveDealData,
      setRequiredInitialDataForVehicles,
      blockScreen,
    } = this.props;
    const primaryVehicleDealVehicleId = DealReader.getPrimaryVehicleDealVehicleId(deal);
    const nonPrimaryDealVehicleId = _map(
      _filter(deal.vehicles, ({ dealVehicleId }) => dealVehicleId !== primaryVehicleDealVehicleId),
      'dealVehicleId'
    );
    this.setState({ visible: false });
    if (_isFunction(blockScreen)) blockScreen();
    await DeskingActions.changeMultiVehicleDeskingStatus(true);
    if (DynamicPropertyHelper.isRestrictCRMEventFlowEnabled()) {
      await DeskingActions.setDoNotSendCrmUpdateEventFlag(true);
    }
    const { deal: updatedDeal } = this.props;
    await setRequiredInitialDataForVehicles(updatedDeal);
    await saveDealData();
    updateDeskingPaymentsDataForSelectedVehicles(deal, nonPrimaryDealVehicleId);
  };

  renderChangeVehicleTrigger = () => {
    const { vehicles, deal, salesSetupInfo } = this.props;
    const isMultiVehicleDesking = SalesSetupReader.getIsMultiVehicleDesking(salesSetupInfo);
    const labelForMultiVehicleDesking = DealReader.isMultiVehicleDeskingEnabledV2(deal)
      ? __('Multi Vehicle Desking')
      : __('Single Vehicle Desking');
    return (
      <Trigger
        title={__(`{{vehicleCount}} Vehicle Options`, { vehicleCount: _size(vehicles) })}
        subTitle={
          DealerPropertyHelper.isMultiVehicleDeskingEnabled() && isMultiVehicleDesking
            ? labelForMultiVehicleDesking
            : __('Change Vehicle')
        }
        viewOnly
      />
    );
  };

  onModalDropDownOpenCb = () => {
    const { vehicles } = this.props;
    this.setState({
      vehicles,
    });
  };

  changeOnCondition = vehicles => {
    const primaryVehicle = _find(vehicles, item => item.primaryVehicle);
    confirmSite(
      {
        title: __('Vehicle in different site'),
        submitBtnText: __('Change Vehicle'),
        primaryVehicle,
      },
      () => this.onChangeVehicles(vehicles)
    );
  };

  onChangeVehicles = async vehicles => {
    const { onVehiclesUpdated } = this.props;
    this.setState({ vehicles });
    this.toggleVisible();
    const updateVehicleInventoryIfIdPresent = !isCanadaDealer();
    await onVehiclesUpdated(vehicles, updateVehicleInventoryIfIdPresent);
  };

  toggleVisible = () => {
    const { visible: isVisible } = this.state;
    const { crmHeartbeatStatus } = this.props;

    this.setState(({ visible }) => ({
      visible: !visible,
    }));
    if (!crmHeartbeatStatus) {
      CRMLeadHeartbeat.updateCRMLeadHeartbeatStatus(
        { vehicleTabOpened: !isVisible },
        { isDelayedUpdate: !isVisible === false }
      );
    }
  };

  render() {
    const {
      onVehiclesUpdated,
      disabled,
      deal,
      salesSetupInfo,
      DeskingActions,
      deskingpaymentDetails,
      crmHeartbeatStatus,
    } = this.props;
    const { vehicles, isUpdatingVehicle, visible } = this.state;
    const { type } = deal;
    const disableDropdown =
      (disabled && !hasViewSubscreensInBookedDeals()) || (type === DEAL_TYPES.ONLY_TRADES && isInchCapeOrRrg());

    const isBuildVehicleOptionPresent = isArcLiteProgram() ? checkForBuidVehicle(vehicles) : false;
    const isMultiVehicleDesking = SalesSetupReader.getIsMultiVehicleDesking(salesSetupInfo);

    return (
      <ModalDropDown
        overlay={
          <>
            <ChangeVehicle
              vehicles={vehicles}
              onVehiclesUpdated={onVehiclesUpdated}
              onChangeVehicles={this.changeOnCondition}
              disabled={disabled || crmHeartbeatStatus}
              deskingpaymentDetails={deskingpaymentDetails}
              setSelectedDownPayment={DeskingActions.setSelectedDownPayment}
              crmHeartbeatStatus={crmHeartbeatStatus}
            />
            {DealerPropertyHelper.isMultiVehicleDeskingEnabled() &&
              isMultiVehicleDesking &&
              isCategory1Deal(deal) &&
              deal?.type !== DEAL_TYPES.MOTABILITY &&
              isAllVehiclesHavingSameVehicleCategory(deal?.vehicles) && (
                <ChangeMultiVehicleStatus
                  deal={deal}
                  switchMultiVehicleToSingleVehicle={this.switchMultiVehicleToSingleVehicle}
                  switchSingleVehicleToMultiVehicle={this.switchSingleVehicleToMultiVehicle}
                  disabled={disabled || crmHeartbeatStatus || isBuildVehicleOptionPresent}
                />
              )}
          </>
        }
        visible={visible}
        onClick={!disableDropdown && this.toggleVisible}
        Trigger={this.renderChangeVehicleTrigger}
        overlayContainerClass={cx(styles.overlayContainer, {
          [styles.disableDropDown]: isUpdatingVehicle,
        })}
        triggerContainerClass=""
        borderWidth={1}
        borderColor="grey"
        disabled={disableDropdown}
        onModalDropDownOpenCb={this.onModalDropDownOpenCb}></ModalDropDown>
    );
  }
}

ChangeVehicleTab.defaultProps = {
  vehicles: EMPTY_ARRAY,
  disabled: false,
  crmHeartbeatStatus: false,
};

ChangeVehicleTab.propTypes = {
  vehicles: PropTypes.array,
  onVehiclesUpdated: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
  deal: PropTypes.object.isRequired,
  updateDeskingPaymentsDataForSelectedVehicles: PropTypes.func.isRequired,
  DeskingActions: PropTypes.object.isRequired,
  saveDealData: PropTypes.func.isRequired,
  deskingpaymentDetails: PropTypes.array.isRequired,
  savePaymentsDataForSelectedVehicle: PropTypes.func.isRequired,
  setRequiredInitialDataForVehicles: PropTypes.func.isRequired,
  salesSetupInfo: PropTypes.object.isRequired,
  crmHeartbeatStatus: PropTypes.bool,
  blockScreen: PropTypes.bool.isRequired,
};

export default withCRMLeadHeartbeatStatus(ChangeVehicleTab);
