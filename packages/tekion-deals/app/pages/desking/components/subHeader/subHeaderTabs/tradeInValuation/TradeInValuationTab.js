import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import _get from 'lodash/get';
import _includes from 'lodash/includes';
import _isEmpty from 'lodash/isEmpty';
import _noop from 'lodash/noop';
import { produce } from 'immer';
import _set from 'lodash/set';
import _size from 'lodash/size';
import _map from 'lodash/map';
import _forEach from 'lodash/forEach';
import _some from 'lodash/some';
import _isFunction from 'lodash/isFunction';

import { EMPTY_OBJECT, EMPTY_ARRAY, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { PAYMENT_TYPES } from '@tekion/tekion-base/marketScan/constants/desking.constants';
import { DEAL_TYPES } from '@tekion/tekion-base/constants/deal/type';
import { BASE_REDUCER_KEY, VEHICLE_TYPE } from 'constants/constants';
import { toaster } from '@tekion/tekion-components/src/organisms/NotificationWrapper';

import { getOS } from '@tekion/tekion-base/utils/general';
import { OS_TYPES } from '@tekion/tekion-base/constants/general';
import { isInchcape } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
import { hasViewSubscreensInBookedDeals } from 'permissions/desking.permissions';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';
import FontIcon, { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';
import Popover, { POPOVER_PLACEMENT, POPOVER_TRIGGER } from '@tekion/tekion-components/src/molecules/popover';
import GlobalWarning from 'pages/desking/components/globalWarnings';

import {
  getNetTradeInValue,
  getTradeInVehicleFromTradeIn,
  isTradeInsDeleted,
} from '@tekion/tekion-base/marketScan/readers/deskingSubHeader.reader';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';

import { formatCurrencyWithMoneyFormat, findArrayItem } from 'utils';

import {
  KEYS_THAT_REQUIRE_MS_CALL,
  KEYS,
  HEADER_FIELDS,
  PRIOR_LEASE_TYPE,
} from 'pages/desking/components/subHeader/subHeaderTabs/tradeInValuation/tradeInValuation.constants';
import {
  getDefaultTradeIn,
  getVehicleSubTypes,
  getCustomerTradeInsThatRequireApproval,
  areAllTradeInOptionsAdded,
  areTradeInOptionsAvailable,
  shouldShowTradeInVehicleOptionsModal,
  resetTradeInTaxFields,
  getTradeInFieldsUpdatePayload,
  updateSourceOnVinChange,
  filterDirtyMediaList,
  getDeletedTradeIns,
} from 'pages/desking/components/subHeader/subHeaderTabs/tradeInValuation/tradeInValuation.utils';
import ModalDropDown from 'molecules/modalDropDown';
import { getDealNumber } from '@tekion/tekion-base/marketScan/readers/deal.reader';

import PopOverIcon from 'molecules/PopOverIcon/PopOverIcon';
import Trigger from 'pages/desking/components/subHeader/subHeaderTabs/trigger/Trigger';
import TradeInValuation from 'pages/desking/components/subHeader/subHeaderTabs/tradeInValuation';
import * as VehicleReader from 'utils/vehicleReader';
import { getAllVehicleMakes, getAllModels } from 'pages/StartDeal/startDeal.selector';
import { isCategory3Deal, isCategory4Deal } from '@tekion/tekion-base/marketScan/utils/desking.utils';
import { getTradeInCount, getOwnerType, getModifiedLienLender } from 'pages/desking/desking.selectors';
import withDefault from 'pages/desking/withDefault';
import DeskingAPI from 'pages/desking/desking.api';
import withDeskingContext from 'pages/desking/withDeskingContext';
import { isInchCapeOrRrg, isSetDoNotSendCrmUpdateEventOnTradeInUpdate } from 'utils/desking.helpers';

import { onTradeInSaveEvent } from '@tekion/tekion-widgets/src/organisms/sales/tradeInForm';
import {
  mapTradeInVehicleCustomFieldsToServer,
  mapTradeInVehicleCustomFieldsToUI,
  formattedTradeInData,
} from '@tekion/tekion-widgets/src/appServices/sales/helpers/mapTradeInCustomFields';
import withCRMLeadHeartbeatStatus from 'pages/desking/withCRMLeadHeartbeatStatus';
import CRMLeadHeartbeat from 'utils/CRMLeadHeartbeat';
import CalcEngineProperties from 'utils/CalcEngineProperties';
import { isCanadaDealer } from 'utils/dealerUtils';
import { DynamicPropertyHelper } from '@tekion/tekion-widgets/src/helpers/dynamicPropertyHelper';
import { getConsumerAddedTradeIns } from '../../../../desking.helpers';
import CustomerOwnedVehiclesModal from './components/CustomerOwnedVehiclesModal';

import styles from './tradeInValuation.module.scss';

class TradeInValuationTab extends PureComponent {
  constructor(props) {
    super(props);
    this.initialTradeInsData = [];
    this.state = {
      tradeInsData: EMPTY_ARRAY,
      tradeInOptions: EMPTY_ARRAY,
      shouldShowCustomerOwnedVehicleOptions: false,
      keysRequiredForMSCallChanged: false,
      wasSkipClicked: false,
      lienHolderLenders: EMPTY_ARRAY,
    };
  }

  componentDidMount() {
    const { CommonActions } = this.props;
    CommonActions.getFuelTypes();
    CommonActions.getBodyStyles();
    CommonActions.getVehicleMake();
    CommonActions.getVISettings();
    this.fetchAllLienholderLenders();
    if (DealerPropertyHelper.isHondaCPOProgramEnabled() || DealerPropertyHelper.isHondaZDXProgramEnabled()) {
      this.getTradeInOptions();
    }
    this.checkSkipButtonState();
  }

  componentDidUpdate(prevState) {
    if (DealerPropertyHelper.isDealLeadSyncV2Enabled()) {
      const { visible } = this.state;
      const { crmHeartbeatStatus } = this.props;
      const isCRMHeartbeatStatusChanged = prevState.crmHeartbeatStatus === true && crmHeartbeatStatus === false;
      const isCRMChangesReceived = CRMLeadHeartbeat.getCustomerTradeinFormInitializationStatus();
      if (isCRMHeartbeatStatusChanged || isCRMChangesReceived) {
        CRMLeadHeartbeat.setCustomerTradeinFormInitializationStatus(false);
        CRMLeadHeartbeat.updateCRMLeadHeartbeatStatus({ tradeinTabOpened: visible }, { forceUpdate: true });
        if (isCRMChangesReceived) this.getTradeIns();
      }
    }
  }

  fetchAllLienholderLenders = async () => {
    const response = await DeskingAPI.fetchAllLienholderLenders();
    const finalLienHolders = _map(response, lienHolder => getModifiedLienLender(lienHolder));
    this.setState({
      lienHolderLenders: finalLienHolders,
    });
  };

  getTradeIns = () => {
    const { deal } = this.props;
    const tradeInsData = _get(deal, 'tradeIns') || EMPTY_ARRAY;
    this.initialTradeInsData = mapTradeInVehicleCustomFieldsToUI(tradeInsData);
    this.setState(
      {
        tradeInsData: this.initialTradeInsData,
      },
      () => this.setCustomViFieldsBulk()
    );
  };

  getTradeInOptions = async () => {
    const { deal } = this.props;
    const { dealNumber } = deal;
    const response = await DeskingAPI.fetchTradeInVehicleDetails(dealNumber);
    if (!_isEmpty(response)) {
      this.setState({ tradeInOptions: response });
    }
  };

  checkSkipButtonState = async () => {
    const { DeskingActions } = this.props;
    const dealPreferences = await DeskingActions.getDealPreferences();
    const { isTradeInRecommendationPopUpSkipped } = dealPreferences;
    this.setState({
      shouldShowCustomerOwnedVehicleOptions: !isTradeInRecommendationPopUpSkipped,
      wasSkipClicked: isTradeInRecommendationPopUpSkipped,
    });
  };

  setShouldShowCustomerOwnedVehicleOptions = value => this.setState({ shouldShowCustomerOwnedVehicleOptions: value });

  handleCustomerOwnedVehiclesModalSkipButtonClick = async () => {
    const { deal, dealPreferences } = this.props;
    const { dealNumber } = deal;
    await DeskingAPI.updateSkipButtonState(dealNumber, {
      ...dealPreferences,
      isTradeInRecommendationPopUpSkipped: true,
    });
    this.setState({ shouldShowCustomerOwnedVehicleOptions: false, wasSkipClicked: true });
  };

  setCustomViFieldsBulk = () => {
    const { tradeInsData: tradeIns } = this.state;
    const { CommonActions, vehicleTypes } = this.props;
    const usedSubtypes = getVehicleSubTypes(vehicleTypes, VEHICLE_TYPE.USED);
    const payload = _map(tradeIns, (item, index) => {
      const vehicleSubTypeID = _get(
        findArrayItem(usedSubtypes, 'name', _get(item, 'tradeInVehicle.vehicleSubType')),
        'id'
      );
      return {
        source: `TRADE_IN_${index + 1}`,
        stockType: VEHICLE_TYPE.USED,
        stockSubType: vehicleSubTypeID,
      };
    });
    CommonActions.setCustomViFieldsBulk(payload);
  };

  renderNetTradeInTrigger = () => {
    const { deal, viewOnly } = this.props;
    const { tradeInOptions } = this.state;
    const tradeInsData = _get(deal, 'tradeIns') || EMPTY_ARRAY;
    const program = _get(deal, 'program');
    const noOfTradeInsRequireApproval = getCustomerTradeInsThatRequireApproval(tradeInsData);

    const renderTradeInTriggerComponent = () => (
      <Trigger
        title={!_isEmpty(deal.tradeIns) ? formatCurrencyWithMoneyFormat(getNetTradeInValue(deal)) : '–'}
        subTitle={__('Net Trade-in Value')}
        viewOnly={viewOnly}
      />
    );

    const renderTradeInPopOverIcon = () => (
      <PropertyControlledComponent controllerProperty={noOfTradeInsRequireApproval.length > 0}>
        <PopOverIcon
          message={__(`Customer added {{count}} Trade-In Vehicle`, { count: noOfTradeInsRequireApproval.length })}
          className={styles.warning}
          onClick={_noop}
          icon="icon-alert"
          triggerRef={_noop}
        />
      </PropertyControlledComponent>
    );

    return (
      <>
        {areTradeInOptionsAvailable(tradeInOptions, program) ? (
          <Popover
            placement={POPOVER_PLACEMENT.BOTTOM}
            trigger={POPOVER_TRIGGER.HOVER}
            content={
              !areAllTradeInOptionsAdded(tradeInsData, tradeInOptions) ? (
                <div className={styles.popOverContent}>
                  {__('Customer already owns Honda vehicles. Please click on dropdown to start trade-in.')}
                </div>
              ) : (
                EMPTY_STRING
              )
            }
            className={styles.tradeInTabContainer}>
            {renderTradeInTriggerComponent()}
            <FontIcon size={SIZES.MD} className={styles.badgeIcon}>
              icon-star-badge
            </FontIcon>
            {renderTradeInPopOverIcon()}
          </Popover>
        ) : (
          <>
            {renderTradeInTriggerComponent()}
            {renderTradeInPopOverIcon()}
          </>
        )}
      </>
    );
  };

  onFormDataChange = (payload, callback = _noop) => {
    const { salesSetupInfo } = this.props;
    const isSupportPriorLeaseCreditEnabled = DealerPropertyHelper.isSupportPriorLeaseCreditEnabled();
    this.setState(
      prevState =>
        produce(prevState, draft => {
          const { id, key, value, isTradeInDirty = true } = payload;
          const tradeIns = draft.tradeInsData;
          tradeIns[id] = tradeIns[id] ? tradeIns[id] : {};
          if (KEYS_THAT_REQUIRE_MS_CALL.includes(key)) _set(draft, 'keysRequiredForMSCallChanged', true);
          if (key) {
            const keySplit = key?.split ? key.split('.') : key;
            _set(tradeIns[id], [...keySplit], value);
            if (key === KEYS.PRIOR_LEASE_TYPE && isSupportPriorLeaseCreditEnabled) {
              _set(tradeIns[id], KEYS.TRADE_ALLOWANCE, '');
              _set(tradeIns[id], KEYS.TRADE_PAY_OFF, '');
            }
            if (key === KEYS.OWNERSHIP_TYPE && isSupportPriorLeaseCreditEnabled) {
              _forEach(HEADER_FIELDS, keyItem => {
                _set(tradeIns[id], keyItem, '');
              });
              _set(tradeIns[id], KEYS.GROUNDED_VEHICLE_LEASE, false);
              if (value === PAYMENT_TYPES.LEASE) {
                _set(tradeIns[id], KEYS.PRIOR_LEASE_TYPE, PRIOR_LEASE_TYPE.BALANCE);
              }
            }
            if (key === KEYS.OWNERSHIP_TYPE && isCanadaDealer()) {
              const { tradeInTaxSettings, tradeInExemptionSettings } = resetTradeInTaxFields({
                ownerShipType: value,
                salesSetupInfo,
              });
              _set(tradeIns[id], 'tradeInTaxSettings', tradeInTaxSettings);
              _set(tradeIns[id], 'tradeInExemptionSettings', tradeInExemptionSettings);
            }
          } else {
            _set(tradeIns, [id], value);
            _set(draft, 'keysRequiredForMSCallChanged', true);
          }
          if (isTradeInDirty) _set(tradeIns[id], 'isTradeInDirty', true);
        }),
      callback
    );
  };

  onBulkFormDataChange = (payloadList, callback = _noop) => {
    this.setState(
      prevState =>
        produce(prevState, draft => {
          const tradeIns = draft.tradeInsData;
          _forEach(payloadList, payload => {
            const { id, key, value, isTradeInDirty = true } = payload;
            tradeIns[id] = tradeIns[id] ? tradeIns[id] : {};
            if (key) {
              const keySplit = key?.split ? key.split('.') : key;
              _set(tradeIns[id], [...keySplit], value);
            } else {
              _set(tradeIns, [id], value);
              _set(draft, 'keysRequiredForMSCallChanged', true);
            }
            if (isTradeInDirty) _set(tradeIns[id], 'isTradeInDirty', true);
          });
          if (_some(payloadList, ({ key }) => KEYS_THAT_REQUIRE_MS_CALL.includes(key) || !key)) {
            _set(draft, 'keysRequiredForMSCallChanged', true);
          }
        }),
      callback
    );
  };

  addTradeInVehicle = () => {
    const { ownerType, salesSetupInfo } = this.props;

    this.setState(
      prevState =>
        produce(prevState, draft => {
          const tradeIns = draft.tradeInsData || [];
          tradeIns.push(getDefaultTradeIn({ ownerType, salesSetupInfo }));
          _set(draft, 'keysRequiredForMSCallChanged', true);
        }),
      () => this.setCustomViFieldsBulk()
    );
  };

  addSelectedVehicleForTradeIn = selectedTradeInVehiclesList => {
    const { ownerType, salesSetupInfo } = this.props;
    this.setState(
      prevState =>
        produce(prevState, draft => {
          const tradeIns = draft.tradeInsData || [];
          selectedTradeInVehiclesList.forEach(selectedVehicle => {
            const selectedVehicleData = getDefaultTradeIn({ ownerType, salesSetupInfo });
            selectedVehicleData.ownerType = selectedVehicle.ownerType;
            selectedVehicleData.tradeInVehicle = { ...selectedVehicle.tradeInVehicle };
            selectedVehicleData.tradeInVehicle.displayModel = selectedVehicle.tradeInVehicle.model;
            selectedVehicleData.isTradeInDirty = true;
            tradeIns.unshift(selectedVehicleData);
          });
          _set(draft, 'keysRequiredForMSCallChanged', true);
          _set(draft, 'shouldShowCustomerOwnedVehicleOptions', false);
        }),
      () => this.setCustomViFieldsBulk()
    );
  };

  deleteTradeInVehicle = id => {
    this.setState(
      prevState =>
        produce(prevState, draft => {
          const tradeIns = draft.tradeInsData || [];
          if (tradeIns) tradeIns.splice(id, 1);
          _set(draft, 'keysRequiredForMSCallChanged', true);
        }),
      () => this.setCustomViFieldsBulk()
    );
  };

  approveTradeInVehicles = async () => {
    try {
      const { DeskingActions, deal } = this.props;
      const { dealNumber } = deal;
      await DeskingActions.approveTradeIn(dealNumber);
      await DeskingActions.getDealPreferences();
    } catch (err) {
      console.error(err);
    }
  };

  modifyTradeinDetails = tradeIns => {
    tradeIns.forEach(tradeIn => {
      const { tradeAllowance, actualCashValue } = tradeIn;
      if (tradeAllowance && !actualCashValue) {
        _set(tradeIn, 'actualCashValue', tradeAllowance);
      }
      const tradeInVehicle = getTradeInVehicleFromTradeIn(tradeIn);
      const stockIDExists = VehicleReader.getIsExistingStockId(tradeInVehicle);
      const registrationNumberAlreadyExisting = isInchcape() && VehicleReader.getIsExistingRegNumber(tradeInVehicle);

      if (stockIDExists) {
        _set(tradeIn, ['tradeInVehicle', 'stockID'], undefined);
      }
      if (registrationNumberAlreadyExisting) _set(tradeIn, ['tradeInVehicle', 'licensePlateNumber'], undefined);
      if (_get(tradeIn, KEYS.IS_IN_VALID_VIN)) _set(tradeIn, KEYS.VIN, '');
    });
  };

  saveTradeInMediaDetails = async () => {
    const { DeskingActions, dealTradeInMediaList, deal } = this.props;
    const { tradeInsData } = this.state;
    const dirtyMediaList = filterDirtyMediaList(dealTradeInMediaList);
    const deletedTradeIns = getDeletedTradeIns(deal, tradeInsData);
    if (!_isEmpty(dirtyMediaList) || !_isEmpty(deletedTradeIns)) {
      await DeskingActions.removeTradeInMediaDetailsForDeletedTradeIns(deletedTradeIns);

      const { dealTradeInMediaList: newDealTradeInMediaList } = this.props;
      const dealNumber = getDealNumber(deal);
      await DeskingActions.saveTradeInMediaDetails(dealNumber, newDealTradeInMediaList);
    }
  };

  onPressEnter = async () => {
    const { tradeInsData } = this.state;
    this.saveTradeInMediaDetails();
    if (tradeInsData !== this.initialTradeInsData) {
      this.setState(
        prevState =>
          produce(prevState, draft => {
            this.modifyTradeinDetails(draft.tradeInsData);
          }),
        this.saveTradeinData
      );
    }
  };

  handleReviewCustomerAddedTradeIns = async (oldTradeIns, newTradeIns) => {
    if (DealerPropertyHelper.isCustomerApprovalsEnabled()) {
      const { deal, DeskingActions, blockScreen, unBlockScreen } = this.props;
      const oldConsumerAddedTradeIns = getConsumerAddedTradeIns(oldTradeIns);
      const newConsumerAddedTradeIns = getConsumerAddedTradeIns(newTradeIns);
      const tradeInFieldsUpdatePayload = getTradeInFieldsUpdatePayload(
        oldConsumerAddedTradeIns,
        newConsumerAddedTradeIns
      );
      if (!_isEmpty(tradeInFieldsUpdatePayload)) {
        if (_isFunction(blockScreen)) blockScreen(true);
        await DeskingActions.fetchApprovalDetailsOnTradeInFieldsUpdate({
          deal,
          updatedTradeIns: tradeInFieldsUpdatePayload,
        });
        if (_isFunction(unBlockScreen)) unBlockScreen();
        GlobalWarning.showWarnings();
      }
    }
  };

  saveTradeInDealAndCalculate = async newTradeIns => {
    const {
      deal,
      calculateEMIamountForDealrTradeDealType,
      DeskingActions,
      saveDealIfDirty,
      calculateEMIamountForOnlyTrades,
      updateDealItemPaymentDetails,
      updateStateFeeTargetingAndSaveDealData,
      canCallMarketScan,
    } = this.props;
    const oldTradeIns = _get(deal, 'tradeIns') || EMPTY_ARRAY;
    const isTradeInsDataDeleted = isTradeInsDeleted(oldTradeIns, newTradeIns);

    const filteredNewTradeIns = newTradeIns.filter(({ newlyAdded, isTradeInDirty }) => !newlyAdded || isTradeInDirty); // removing the newly added tradein and if there are no values in it
    if (_size(filteredNewTradeIns) === 0 && !isTradeInsDataDeleted) return;
    const modifiedNewTradeIns = formattedTradeInData(mapTradeInVehicleCustomFieldsToServer(filteredNewTradeIns));
    // modifiedNewTradeIns = updateSourceOnVinChange(oldTradeIns, modifiedNewTradeIns);
    await DeskingActions.setTradeInDeal(modifiedNewTradeIns);
    if (CalcEngineProperties.updateCalculationByBackend()) {
      const response = await updateDealItemPaymentDetails({ tradeIns: modifiedNewTradeIns }, false);
      // await this.handleReviewCustomerAddedTradeIns(oldTradeIns, newTradeIns);
      const error = _get(response, 'error');
      if (error) {
        const errorMessage = error?.detail?.displayMessage || EMPTY_STRING;
        if (errorMessage) toaster('error', error?.detail?.displayMessage);
      }
      return;
    }

    if (isCategory3Deal(deal)) {
      await calculateEMIamountForDealrTradeDealType();
    }
    if (isCategory4Deal(deal)) {
      await calculateEMIamountForOnlyTrades();
    }
    await updateStateFeeTargetingAndSaveDealData(false);
    const { keysRequiredForMSCallChanged } = this.state;
    if (DynamicPropertyHelper.isRestrictCRMEventFlowEnabled()) {
      const doNotSendCrmUpdateEventFlag = isSetDoNotSendCrmUpdateEventOnTradeInUpdate({
        tradeIns: modifiedNewTradeIns,
        isTradeInsDataDeleted,
        keysRequiredForMSCallChanged,
        canCallMarketScan,
        deal,
      });
      await DeskingActions.setDoNotSendCrmUpdateEventFlag(doNotSendCrmUpdateEventFlag);
    }
    saveDealIfDirty();
    await this.refreshMarketScan(isTradeInsDataDeleted);
    // await this.handleReviewCustomerAddedTradeIns(oldTradeIns, newTradeIns);
    DeskingActions.setDealDirtyStatus(false);
  };

  saveTradeinData = async () => {
    const { tradeInsData: newTradeIns } = this.state;
    this.saveTradeInDealAndCalculate(newTradeIns);
  };

  onSaveNewTradeIn = ({ tradeIns, keysRequiredForMSCallChanged, tradeInDataModified }) => {
    this.saveTradeInMediaDetails();
    if (!tradeInDataModified) return;
    const modifiedTradeIns = produce(tradeIns, draft => {
      this.modifyTradeinDetails(draft);
    });

    this.setState({ keysRequiredForMSCallChanged }, () => {
      this.saveTradeInDealAndCalculate(modifiedTradeIns);
    });
  };

  refreshMarketScan = async isTradeInsDataDeleted => {
    const { keysRequiredForMSCallChanged } = this.state;
    const { addDefaultFee, getMarketScanData, deal, DeskingActions, canCallMarketScan } = this.props;
    const tradeInsData = _get(deal, 'tradeIns') || EMPTY_ARRAY;
    const isAnyValuesChangedInTradeIn =
      tradeInsData.some(({ isTradeInDirty }) => isTradeInDirty) || isTradeInsDataDeleted;
    if (isAnyValuesChangedInTradeIn && keysRequiredForMSCallChanged && canCallMarketScan) {
      this.setState({ keysRequiredForMSCallChanged: false });
      if (!isCategory3Deal(deal)) {
        await addDefaultFee({}, true);
      }
      getMarketScanData();
    }
    const tradeInsDataRemovingClientFlags = tradeInsData.map(({ newlyAdded, isTradeInDirty, ...rest }) => rest);
    DeskingActions.setTradeInDeal(tradeInsDataRemovingClientFlags);
  };

  toggleVisible = () => {
    const { visible, wasSkipClicked } = this.state;
    const { crmHeartbeatStatus } = this.props;
    if (visible) {
      if (DealerPropertyHelper.isNewTradeInEnabled()) {
        onTradeInSaveEvent();
      } else {
        this.onPressEnter();
      }
      if (!wasSkipClicked) this.setState({ shouldShowCustomerOwnedVehicleOptions: true });
    }
    if (!crmHeartbeatStatus) {
      CRMLeadHeartbeat.updateCRMLeadHeartbeatStatus(
        { tradeinTabOpened: !visible },
        { isDelayedUpdate: !visible === false }
      );
    }
    this.setState({
      visible: !visible,
    });
  };

  tradeInApprovalCB = () => {
    this.getTradeIns();
  };

  render() {
    const {
      DeskingActions,
      CommonActions,
      deal,
      trimInfos,
      vehicleMakes,
      vehicleModels,
      tradeInCount,
      contentHeight,
      isBookedDeal,
      vehicleTypes,
      isClosedOrSoldDeal,
      deskingpaymentDetails,
      viewOnly,
      customViFields,
      isDealLocked,
      ownerType,
      crmHeartbeatStatus,
    } = this.props;
    const { tradeInsData, tradeInOptions, visible, shouldShowCustomerOwnedVehicleOptions, lienHolderLenders } =
      this.state;
    const height = getOS() === OS_TYPES.WINDOWS ? contentHeight - 100 : contentHeight - 55;
    const { type, program } = deal;

    const disabled =
      (viewOnly && !hasViewSubscreensInBookedDeals()) ||
      (isInchCapeOrRrg() && _includes([DEAL_TYPES.DEALER_TRADE, DEAL_TYPES.CASH_REALISATION, DEAL_TYPES.FNI], type));

    return (
      <ModalDropDown
        overlay={
          shouldShowTradeInVehicleOptionsModal({
            program,
            shouldShowCustomerOwnedVehicleOptions,
            tradeInsData,
            tradeInOptions,
          }) ? (
            <CustomerOwnedVehiclesModal
              tradeIns={tradeInsData}
              tradeInOptions={tradeInOptions}
              addSelectedVehicle={this.addSelectedVehicleForTradeIn}
              handleModalSkipButtonClick={this.handleCustomerOwnedVehiclesModalSkipButtonClick}
            />
          ) : (
            <div className={styles.tradeInCont} style={{ height }}>
              <TradeInValuation
                DeskingActions={DeskingActions}
                CommonActions={CommonActions}
                tradeIns={tradeInsData}
                tradeInOptions={tradeInOptions}
                onPressEnter={this.onPressEnter}
                deal={deal}
                onFormDataChange={this.onFormDataChange}
                onBulkFormDataChange={this.onBulkFormDataChange}
                addTradeInVehicle={this.addTradeInVehicle}
                deleteTradeInVehicle={this.deleteTradeInVehicle}
                approveTradeInVehicles={this.approveTradeInVehicles}
                trimInfos={trimInfos}
                vehicleMakes={vehicleMakes}
                vehicleModels={vehicleModels}
                tradeInCount={tradeInCount}
                isBookedDeal={isBookedDeal}
                isClosedOrSoldDeal={isClosedOrSoldDeal}
                vehicleTypes={vehicleTypes}
                deskingpaymentDetails={deskingpaymentDetails}
                viewOnly={viewOnly || crmHeartbeatStatus}
                customViFields={customViFields}
                setShouldShowCustomerOwnedVehicleOptions={this.setShouldShowCustomerOwnedVehicleOptions}
                tradeInApprovalCB={this.tradeInApprovalCB}
                isDealLocked={isDealLocked || crmHeartbeatStatus}
                contentHeight={height}
                onSaveTradeIn={this.onSaveNewTradeIn}
                ownerType={ownerType}
                crmHeartbeatStatus={crmHeartbeatStatus}
                lienHolderLenders={lienHolderLenders}
              />
            </div>
          )
        }
        Trigger={this.renderNetTradeInTrigger}
        overlayContainerClass=""
        triggerContainerClass=""
        overlayClassName={DealerPropertyHelper.isNewTradeInEnabled() ? styles.overlayContainerClass : ''}
        borderWidth={1}
        borderColor="grey"
        // onModalDropDownCloseCb={this.onPressEnter}
        onModalDropDownOpenCb={this.getTradeIns}
        onClick={!disabled && this.toggleVisible}
        disabled={disabled}
        visible={visible}></ModalDropDown>
    );
  }
}

TradeInValuationTab.propTypes = {
  DeskingActions: PropTypes.object,
  viewOnly: PropTypes.bool,
  deal: PropTypes.object.isRequired,
  saveDealIfDirty: PropTypes.func.isRequired,
  getMarketScanData: PropTypes.func.isRequired,
  calculateEMIamountForDealrTradeDealType: PropTypes.func,
  calculateEMIamountForOnlyTrades: PropTypes.func,
  CommonActions: PropTypes.object,
  trimInfos: PropTypes.object,
  vehicleMakes: PropTypes.array,
  vehicleModels: PropTypes.object,
  addDefaultFee: PropTypes.func,
  tradeInApprovalData: PropTypes.array,
  contentHeight: PropTypes.number.isRequired,
  ownerType: PropTypes.string,
  isBookedDeal: PropTypes.bool.isRequired,
  isClosedOrSoldDeal: PropTypes.bool.isRequired,
  vehicleTypes: PropTypes.array,
  canCallMarketScan: PropTypes.bool.isRequired,
  customViFields: PropTypes.object,
  updateStateFeeTargetingAndSaveDealData: PropTypes.func.isRequired,
  dealPreferences: PropTypes.object,
  salesSetupInfo: PropTypes.object.isRequired,
  dealTradeInMediaList: PropTypes.array,
  blockScreen: PropTypes.func,
  unBlockScreen: PropTypes.func,
};

TradeInValuationTab.defaultProps = {
  DeskingActions: EMPTY_OBJECT,
  viewOnly: false,
  calculateEMIamountForDealrTradeDealType: _noop,
  calculateEMIamountForOnlyTrades: _noop,
  CommonActions: EMPTY_OBJECT,
  trimInfos: EMPTY_OBJECT,
  vehicleMakes: EMPTY_ARRAY,
  vehicleModels: EMPTY_OBJECT,
  addDefaultFee: _noop,
  ownerType: EMPTY_STRING,
  vehicleTypes: EMPTY_ARRAY,
  customViFields: EMPTY_OBJECT,
  dealPreferences: EMPTY_OBJECT,
  tradeInApprovalData: EMPTY_ARRAY,
  dealTradeInMediaList: EMPTY_ARRAY,
  blockScreen: _noop,
  unBlockScreen: _noop,
};

const mapStateToProps = globalState => {
  const state = _get(globalState, BASE_REDUCER_KEY);
  return {
    vehicleMakes: getAllVehicleMakes(state),
    vehicleModels: getAllModels(state),
    tradeInCount: getTradeInCount(state),
    ownerType: getOwnerType(state),
    canCallMarketScan: state.desking.canCallMarketScan,
    customViFields: _get(state, 'common.customViFields') || EMPTY_OBJECT,
    vehicleTypes: _get(state, 'common.viSettings.typeSetting.vehicleTypes') || EMPTY_OBJECT,
    dealPreferences: _get(state, 'desking.dealPreferences'),
    tradeInApprovalData: _get(state, 'desking.tradeInApprovalData'),
    dealTradeInMediaList: _get(state, 'desking.dealTradeInMediaList'),
  };
};

export default connect(
  mapStateToProps,
  null
)(withCRMLeadHeartbeatStatus(withDeskingContext(withDefault(TradeInValuationTab))));
