import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import { compose } from 'recompose';
import { connect } from 'react-redux';

import _get from 'lodash/get';
import _has from 'lodash/has';
import _noop from 'lodash/noop';
import _isEmpty from 'lodash/isEmpty';
import _isFunction from 'lodash/isFunction';
import _find from 'lodash/find';
import _includes from 'lodash/includes';
import _castArray from 'lodash/castArray';

import { DISCLOSURE_TYPES } from '@tekion/tekion-base/constants/deal/fnis';
import { EMPTY_OBJECT, EMPTY_ARRAY, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { BASE_REDUCER_KEY } from 'constants/constants';
import { TAX_AND_ZIP_CODE_DETAILS_VS_KEY } from '@tekion/tekion-base/marketScan/constants/desking.constants';
import { INTEGRATION_KEYS } from '@tekion/tekion-base/constants/retail/salesSetup.constants';
import * as SalesSetupReader from '@tekion/tekion-base/marketScan/readers/salesSetup.reader';
import * as ColumnDataReader from '@tekion/tekion-base/marketScan/readers/columnData.reader';
import * as DealReader from '@tekion/tekion-base/marketScan/readers/deal.reader';
import { DEAL_USER_TYPE, DEAL_USER_TYPE_DISPLAY_NAME } from '@tekion/tekion-base/constants/deal/userType';
import { isInchcape, isInchcapeOrRRG, isRRG } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
import VehicleTransferModalV2, {
  showVehicleTransferModal,
  VEHICLE_TRANSFER_MODAL_TYPES,
} from 'twidgets/appServices/sales/organisms/VehicleTransferModal';
import { isArcLiteProgram } from 'utils/program.utils';
import dealEnv from 'utils/dealEnv';
import { checkIsPrimaryVehicleFromDifferentDealer } from 'utils/vehicle.utils';

import Label from '@tekion/tekion-components/src/atoms/Label';
import FontIcon, { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import Popover, { POPOVER_PLACEMENT, POPOVER_TRIGGER } from '@tekion/tekion-components/src/molecules/popover';
import Button from '@tekion/tekion-components/src/atoms/Button';
import Divider from '@tekion/tekion-components/src/atoms/Divider';
import GlobalWarning from 'pages/desking/components/globalWarnings';
import * as DeskingReader from '@tekion/tekion-base/marketScan/readers/desking.reader';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';
import { isIntegrationEnabled } from 'pages/desking/desking.selectors';
import {
  calculateTotalEMIAmountForDealerTrade,
  calculateTotalEMIAmountForDealerTradeV1,
  isCategory3Deal,
  getAutoRollCashDeficiencyFlag,
  isCategory1Deal,
  isCategory4Deal,
  getColumnForGivenTaxType,
  formatPaymentOptionsFromSalesSetup,
  getLabelFromPaymentOptions,
} from '@tekion/tekion-base/marketScan/utils/desking.utils';
import { getCreditScore } from '@tekion/tekion-base/marketScan/readers/deskingSubHeader.reader';
import { DEAL_TYPES } from '@tekion/tekion-base/constants/deal/type';

import { getAllModels, getAllVehicleMakes } from 'pages/StartDeal/startDeal.selector';
import { getAPIError } from 'utils/error.reader';
import { checkTaxRateForWholeSaleDealEnabled, isDealerTradeDeal } from 'utils/deal.util';
import { LEFT_PANEL_ITEMS, LEFT_PANEL_DEAL_STATUS_MAP, VEHICLE_TRANSFER_STATUS } from 'pages/desking/desking.constants';
import * as VehicleReader from 'utils/vehicleReader';
import Trigger from 'pages/desking/components/subHeader/subHeaderTabs/trigger/Trigger';
import ConfirmationModal from 'molecules/ConfirmationModal';
import AssumptivePayOff from 'organisms/AssumptivePayOff';
import withAccountingHelpers from 'organisms/withAccountingHelpers';
import AutoScrollDiv from 'molecules/autoScrollDiv';
import GrossBreakUpModal from 'pages/desking/components/grossBreakUp/GrossBreakUpModal';
import WithMandatoryReviewHOC from 'organisms/MandatoryAndReviewForm/MandatoryReviewHOC';
import VehicleConfirmationModalHOC from 'molecules/VehicleConfirmationModalHOC/VehicleConfirmationModalHOC';
import AllowMultipleReservationsOnVehicleHOC from 'molecules/AllowMultipleReservationsOnVehicleHOC';
import {
  hasSellingPriceEdit,
  hasTradeInPricingEdit,
  hasTradeInDetailsEdit,
  hasFniEdit,
  hasDeskingEdit,
  hasDmvDeskEdit,
  hasZipcodeEdit,
  hasCreditScoreEdit,
  hasGrossSummaryView,
  hasCostAndGrossView,
  canSendDealsToSales,
  canEditDealBookedDate,
  hasDeskingEditAllFieldsAfterQuoteStatus,
  hasContractDateEdit,
} from 'permissions/desking.permissions';
import { TOASTER_STATUS } from 'constants/pages';
import {
  PERSONAS,
  DEAL_STATUS,
  DEAL_STATUS_WEIGHTS,
  DEAL_ACTIVITY,
  PERSONA_API_MAP,
  DEAL_STATUS_TO_NOT_SEND_TO_SALES,
} from 'pages/deallist/deal.constants';
import { formatCurrencyWithMoneyFormat } from 'utils';
import withDeskingContext from 'pages/desking/withDeskingContext';
import SellingPriceTab from 'pages/desking/components/subHeader/subHeaderTabs/sellingPrice/SellingPriceTab';
import ZipCodeTab from 'pages/desking/components/subHeader/subHeaderTabs/zipCodeTaxRate';
import DmvRosTab from 'pages/desking/components/subHeader/subHeaderTabs/dmvRos/DmvRosTab';
import AllPersonaTab from 'pages/desking/components/subHeader/subHeaderTabs/allPersonaTab';
import DealWithorWithoutVat from 'pages/desking/components/subHeader/subHeaderTabs/dealWithorWithoutVat';
import TradeInValuationTab from 'pages/desking/components/subHeader/subHeaderTabs/tradeInValuation/TradeInValuationTab';
import { getAssumptivePayOffVehicles } from 'pages/desking/components/subHeader/subHeaderTabs/tradeInValuation/tradeInValuation.utils';
import ContractDate from 'pages/desking/components/subHeader/subHeaderTabs/contractDate';
import DeskingAPI from 'pages/desking/desking.api';
import CalculationUpdateNotification from 'pages/desking/components/calculationUpdateNotification';

import CalcEngineProperties from 'utils/CalcEngineProperties';
import { tget } from '@tekion/tekion-base/utils/general';
import * as DueBillUtil from 'utils/dueBills.util';
import { BACKEND_GLOBAL_WARNINGS_ACTIONS } from 'commonActions/constants/backendWarnings.constant';
import SideMenu from './sideMenu';
import styles from './subHeader.module.scss';
import ChangeVehicleTab from './subHeaderTabs/changeVehicleTab/ChangeVehicleTab';
import BuildVehicleTab from './subHeaderTabs/BuildVehicle/BuildVehicleTab';
import OrderRequestButton from '../orderRequests/components/OrderRequestButton';
import UpdateVehicleModal from './subHeaderTabs/updateVehicleModal';

import {
  TRANSFER_STATUS_TO_MODAL_SECONDARY_BUTTON_TITLE,
  TRANSFER_STATUS_TO_MODAL_SUBMIT_BUTTON_TITLE,
  TRANSFER_STATUS_TO_MODAL_TITLE,
} from './subHeader.constants';

import VehicleTransferModal from './vehicleTransferModal';
import { getModalMessage } from './subHeader.helpers';
import { isPrimaryVehicleZDX } from '../accessoriesUpdate/accessoriesCollapseHeader.helpers';

class SubHeader extends PureComponent {
  constructor(props) {
    super(props);

    this.state = {
      dmvDesking: false,
      updateVehicleModalVisible: false,
      disableFNIButton: false,
      shouldShowVehicleTransferModal: false,
    };

    this.assumptiveTradePayOffRef = React.createRef();
  }

  componentDidMount() {
    const { CommonActions, DeskingActions } = this.props;
    CommonActions.getAllRoles();
    CommonActions.getAllRolesV2();
    DeskingActions.getDealPreferences();
    DeskingActions.getFNIProducts(); // api added as fni products are fetched to see if they are taxable or not while changing the zip code
  }

  delay = ms => new Promise(resolve => setTimeout(resolve, ms));

  handleGrossSummaryClick = () => {
    GrossBreakUpModal.open();
  };

  renderCreditScoreTrigger = () => {
    const { deal, isDealViewOnly } = this.props;

    return (
      <Trigger
        title={getCreditScore(deal)}
        subTitle={__('Credit Score')}
        viewOnly={!hasCreditScoreEdit() && isDealViewOnly}
      />
    );
  };

  renderDownpaymentTrigger = () => {
    const { deskingpaymentDetails } = this.props;

    const selectedColumn = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);
    const selectedDownPayment = ColumnDataReader.getSelectedDownPayment(selectedColumn);
    const selectedOutOfPocketCash = ColumnDataReader.getSelectedOutOfPocketCash(selectedColumn);
    const dataToShow = ColumnDataReader.isLeasePaymentType(selectedColumn)
      ? selectedOutOfPocketCash
      : selectedDownPayment;
    const label = ColumnDataReader.isLeasePaymentType(selectedColumn) ? __('Out Of Pocket Cash') : __('Customer Cash');

    return (
      <div className={classNames(styles.commonTriggerStyle, styles.triggerHeight)}>
        <div className={classNames('d-flex', 'flex-column', styles.columnStyle)}>
          <Label className={styles.netTradeInValueStyle}>{formatCurrencyWithMoneyFormat(dataToShow || 0)}</Label>
          <Label className={styles.netTradeInLabelStyle}>{label}</Label>
        </div>
        {/* <FontIcon size={SIZES.S}>icon-chevron-down</FontIcon> */}
      </div>
    );
  };

  renderPaymentGraphTrigger = () => (
    <div className={classNames(styles.paymentGraphTriggerStyle, styles.triggerHeight)}>
      <div className={classNames('d-flex', 'flex-column', styles.columnStyle)}>
        <Label className={styles.paymentGraphValueStyle}>Payment Graph</Label>
      </div>
      <FontIcon size={SIZES.S}>icon-chevron-down</FontIcon>
    </div>
  );

  saveDealAndRefreshMarketScanIfDirty = () => {
    const { isDealDirty } = this.props;
    if (isDealDirty) {
      this.saveDealAndRefreshMarketScan();
    }
  };

  saveDealIfDirty = () => {
    const { isDealDirty, saveDealData } = this.props;
    if (isDealDirty) saveDealData();
  };

  saveDealAndRefreshMarketScan = async () => {
    const {
      saveDealData,
      getMarketScanData,
      deskingpaymentDetails,
      updateDeskingForColumnIds,
      getUpdatedRebatesForColumn,
      syncPaymentDetails,
    } = this.props;

    const isUpdateCalculationByBackend = CalcEngineProperties.updateCalculationByBackend();
    if (!isUpdateCalculationByBackend) {
      await getUpdatedRebatesForColumn();
      await saveDealData();
    }

    if (DealerPropertyHelper.isCreateDealWithNewAPIEnabled()) {
      const columnIds = DeskingReader.getAllCColumnsIDs(deskingpaymentDetails);
      await updateDeskingForColumnIds(columnIds);
      return;
    }
    if (isUpdateCalculationByBackend) {
      await syncPaymentDetails();
      return;
    }

    getMarketScanData();
  };

  checkIsMultipleReservationsOnVehicleAllowed = async () => {
    const { deal, checkIsMultipleReservationAllowed } = this.props;
    this.setState({
      disableFNIButton: true,
    });

    if (DealerPropertyHelper.isStandAloneCRMEnabled()) {
      await this.pushToDMS();
      return;
    }

    const dealNumber = DealReader.getDealNumber(deal);
    const shouldProceed = await checkIsMultipleReservationAllowed({
      dealStatus: DEAL_STATUS.FINANCE_AND_INSURANCE,
      dealNumber,
    });
    if (shouldProceed) {
      await this.checkForAssumptivePayOff();
      this.setState({
        disableFNIButton: false,
      });
    }
  };

  onTransferRequestCreation = response => {
    const { CommonActions } = this.props;
    const vehicleTransferDetails = tget(response, 'data', EMPTY_OBJECT);
    CommonActions.updateVehicleTransferDetails(vehicleTransferDetails);
  };

  checkToContinueJourney = async () => {
    const { deal, DeskingActions } = this.props;
    let checkforManuallyPaused = false;
    const dealNumber = DealReader.getDealNumber(deal);
    if (DealReader.manualPaused(deal)) {
      checkforManuallyPaused = await ConfirmationModal.getConfirmation({
        title: __('Warning'),
        message: __(
          'Change in deal status will make all the paused deal updates visible to the customer. Are you sure you want to proceed?'
        ),
        submitBtnText: __('Yes'),
        secondaryBtnText: __('No'),
      });
      if (checkforManuallyPaused) {
        await DeskingActions.updateDeal(dealNumber, {
          ...deal,
          paused: false,
        });
        GlobalWarning.showWarnings();
        return true;
      }
      return false;
    }
    return true;
  };

  checkIfTheVehicleShouldBeTransferred = async () => {
    const canContinueJourneyIfPaused = await this.checkToContinueJourney();
    if (!canContinueJourneyIfPaused) return;
    const { deal, dealers, vehicleTransferDetails, deskingpaymentDetails } = this.props;
    const primaryVehicle = DealReader.getPrimaryVehicle(deal);
    const tenantId = tget(deal, 'tenantId');
    const primaryVehicleDealerId = tget(primaryVehicle, 'dealerId');
    const currentDealerId = _get(dealEnv, 'dealerConfig.id') || EMPTY_STRING;
    const getDealerName = () => tget(_find(dealers, { dealerId: currentDealerId }), 'displayName');
    const currentStatus = tget(vehicleTransferDetails, 'currentStatus', VEHICLE_TRANSFER_STATUS.DEFAULT);
    const centralApprovalStatus = tget(vehicleTransferDetails, 'centralApprovalStatus');
    const askPrice = tget(vehicleTransferDetails, 'askPrice');
    const requestNote = tget(vehicleTransferDetails, 'requestNote', __('Transferring Vehicle'));
    const selectedColumn = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);
    const { accessories } = selectedColumn;
    const isHEChargerPackageAdded = DueBillUtil.isHEChargerPackageAdded(accessories);
    const isDealTypeAllowedForHEChargerWarning = DueBillUtil.isDealTypeAllowedForHEChargerWarning(deal);
    let continueFnI = true;
    if (isPrimaryVehicleZDX(deal) && !isHEChargerPackageAdded && isDealTypeAllowedForHEChargerWarning) {
      continueFnI = await ConfirmationModal.getConfirmation({
        title: __('Warning'),
        message: __('Please add 1 HE charging package accessory to this vehicle before proceeding'),
        submitBtnText: __('Continue to F&I'),
        secondaryBtnText: __('Add Charger'),
      });
    }

    if (continueFnI) {
      if (checkIsPrimaryVehicleFromDifferentDealer(deal)) {
        if (_includes([VEHICLE_TRANSFER_STATUS.PENDING, VEHICLE_TRANSFER_STATUS.DEFAULT], currentStatus)) {
          showVehicleTransferModal(
            {
              modalType: VEHICLE_TRANSFER_MODAL_TYPES.REQUEST_MODAL,
              vehicleDetails: { ...primaryVehicle, tenantId },
              getDealerName,
              transferDetails: {
                transferPrice: askPrice,
                note: requestNote,
              },
              isDisabled: currentStatus === VEHICLE_TRANSFER_STATUS.PENDING,
              modalProps: {
                primaryBtnLabel: tget(TRANSFER_STATUS_TO_MODAL_SUBMIT_BUTTON_TITLE, currentStatus, EMPTY_STRING),
                secondaryBtnLabel: tget(TRANSFER_STATUS_TO_MODAL_SECONDARY_BUTTON_TITLE, currentStatus, EMPTY_STRING),
                title: tget(TRANSFER_STATUS_TO_MODAL_TITLE, currentStatus, EMPTY_STRING),
                message: getModalMessage(currentStatus, centralApprovalStatus, dealers, primaryVehicleDealerId),
              },
            },
            this.onTransferRequestCreation
          );
        } else {
          this.setState({ shouldShowVehicleTransferModal: true });
        }
      } else {
        this.checkIsMultipleReservationsOnVehicleAllowed();
      }
    }
  };

  pushToDMS = async () => {
    const { deal } = this.props;
    const dealNumber = DealReader.getDealNumber(deal);
    const response = await DeskingAPI.pushToDMS(dealNumber);
    if (!response) {
      toaster('error', __('Failed to push to DMS'));
    } else {
      toaster('success', __('Pushed to DMS'));
    }
    this.setState({
      disableFNIButton: false,
    });
  };

  checkForAssumptivePayOff = async handleConfirmDeal => {
    const { deal, salesSetupInfo, showVehicleConfirmationModal } = this.props;

    const pauseFlow = await showVehicleConfirmationModal(deal);
    if (pauseFlow) return;

    if (DealerPropertyHelper.isStandAloneCRMEnabled()) {
      await this.pushToDMS();
      return;
    }

    const isAssumptivePayOffPopUp = SalesSetupReader.getAssumptivePayOffPopUp(salesSetupInfo);
    const vehiclesWithPayOff = getAssumptivePayOffVehicles(deal);
    if (this.assumptiveTradePayOffRef && isAssumptivePayOffPopUp && !_isEmpty(vehiclesWithPayOff)) {
      this.assumptiveTradePayOffRef.current.show({
        vehiclesWithPayOff,
        onSubmitCb: () => this.onSubmitDeal(handleConfirmDeal),
      });
    } else {
      this.onSubmitDeal(handleConfirmDeal);
    }
  };

  onSubmitDeal = async handleConfirmDeal => {
    const { deal, deskingpaymentDetails, getMarketScanData } = this.props;
    const shouldConfirmDeal = _isFunction(handleConfirmDeal);
    // const isTargetDealStatusConfirmed = targetDealStatus === DEAL_STATUS.CONFIRMED;
    const columnData = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);
    const hasErrorForSelecteddownPayment = ColumnDataReader.getErrorMessageForSelectedDownpayment(columnData);
    if (hasErrorForSelecteddownPayment) {
      toaster('error', hasErrorForSelecteddownPayment);
      return;
    }

    if (!columnData) {
      toaster('error', __('Please select a Column'));
    }

    // const rolledDealsFromSessionStorage = JSON.parse(getValueFromSessionStorage(ROLLED_DEALS)) || EMPTY_OBJECT;
    // const popupShownAndCustomerCCR = _get(rolledDealsFromSessionStorage, [dealNumber, AUTO_ROLL]);
    const popupShownAndCustomerCCR = false;
    if (popupShownAndCustomerCCR) {
      const columnId = _get(columnData, 'id');
      const selectedDownpaymentId = ColumnDataReader.getSelectedDownPaymentID(columnData);

      await getMarketScanData(
        { columnIds: [columnId] },
        {
          shouldAddCashDeficiencyToAccessories: true,
          downPaymentId: selectedDownpaymentId,
        }
      );
      if (shouldConfirmDeal) handleConfirmDeal();
      else this.submitDeal();
    } else {
      const isLeaseOrOPLeasePaymentType = ColumnDataReader.isLeaseOrOPLeasePaymentType(columnData);
      const cashDeficiency = ColumnDataReader.getCashDeficiency(columnData);
      const shouldAutoRoll = !!getAutoRollCashDeficiencyFlag(deal);

      if (!shouldAutoRoll && isLeaseOrOPLeasePaymentType && cashDeficiency > 0) {
        ConfirmationModal.show({
          title: __('Cash Deficiency ?'),
          message: __('Are you sure to proceed with Cash Deficiency'),
          onSubmitCb: async () => {
            ConfirmationModal.close();
            if (shouldConfirmDeal) handleConfirmDeal();
            else this.submitDeal();
          },
        });
      } else if (shouldConfirmDeal) handleConfirmDeal();
      else this.submitDeal();
    }
  };

  submitDeal = async () => {
    const { deal, DeskingActions, changeDealStatusTo } = this.props;
    await changeDealStatusTo(DEAL_STATUS.FINANCE_AND_INSURANCE);
    if (DealReader.isDealOptionContract(deal)) {
      await DeskingActions.updateDeskingDataByDeal();
    }
    GlobalWarning.updateBEWarnings([BACKEND_GLOBAL_WARNINGS_ACTIONS.VEHICLE_RESERVED_IN_VI_WARNING]);
  };

  getAndSetPaymentDetailsWithoutFNIAndAccessories = async () => {
    const { getMarketScanPayloadObject, deskingpaymentDetails, DeskingActions, savePaymentsDataForSelectedVehicle } =
      this.props;
    const selectedColumnData = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);
    const selectedLenderId = ColumnDataReader.getSelectedLenderId(selectedColumnData);

    const selectedDownpayment = ColumnDataReader.getSelectedDownPayment(selectedColumnData);
    const isGeneric = ColumnDataReader.isGeneric(selectedColumnData);
    const marketScanPayload = getMarketScanPayloadObject({
      columnData: selectedColumnData,
      lender: selectedLenderId,
      downPayments: [selectedDownpayment],
      isGenericPayload: isGeneric,
      excludeDisclosureTypes: [
        DISCLOSURE_TYPES.THEFT_DETERRENT,
        DISCLOSURE_TYPES.SURFACE_PROTECTION,
        DISCLOSURE_TYPES.INSURANCE,
        DISCLOSURE_TYPES.DEBT_CANCELLATION_AGGREEMENT,
        DISCLOSURE_TYPES.SERVICE_CONTRACT,
      ],
    }).payload;
    const { marketScanData = EMPTY_OBJECT } = await DeskingAPI.getMarketScanData([marketScanPayload]);
    const { id: columnId } = selectedColumnData;
    const responseForColumn = _get(marketScanData, columnId);
    const responseDownPayment = _get(Object.keys(responseForColumn), [0]);
    let program = null;
    if (Number(responseDownPayment) === selectedDownpayment) {
      program = _get(responseForColumn, responseDownPayment);
    }
    const baseEmiAmount = _get(program, 'Payment') || 0;
    await DeskingActions.setBaseEMIAmount({
      columnId,
      lender: selectedLenderId,
      downPayment: selectedDownpayment,
      baseEmiAmount,
    });
    savePaymentsDataForSelectedVehicle();
  };

  onConfirmDeal = async depositAmount => {
    const { updatePaymentWithItems, unBlockScreen, deal } = this.props;
    GlobalWarning.showWarnings();
    GlobalWarning.updateBEWarnings([BACKEND_GLOBAL_WARNINGS_ACTIONS.VEHICLE_RESERVED_IN_VI_WARNING]);
    if (isInchcape()) {
      await updatePaymentWithItems({});
      if (DealReader.postInvoiceDp(deal)) {
        if (depositAmount === 0) {
          unBlockScreen();
        }
      }
    }
  };

  dmvDesk = async event => {
    event.stopPropagation();
    const { deal, DeskingActions } = this.props;
    const { setDealPreferences } = DeskingActions;
    const dealNumber = DealReader.getDealNumber(deal);
    this.setState({ dmvDesking: true });

    const { response, error, rawResponse } = await DeskingAPI.dmvDesking({ dealNumber });
    if (response) {
      toaster(TOASTER_STATUS.SUCCESS, __('DMV Desking is successful'));
      const { dmvurl, acertus_order_id: acertusOrderId } = response || {};
      await setDealPreferences({ acertusOrderId });
      if (dmvurl) window.open(dmvurl);
    } else {
      const errorMessage = getAPIError(error || rawResponse) || 'DMV Desking Failed';
      toaster(TOASTER_STATUS.ERROR, __(errorMessage));
    }

    this.setState({ dmvDesking: false });
  };

  calculateEMIamountForDealrTradeDealType = async () => {
    const { DeskingActions, deal, deskingpaymentDetails, salesSetupInfo, savePaymentsDataForSelectedVehicle } =
      this.props;
    let totalMoney = 0;

    const columnData = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);

    if (isCategory3Deal(deal)) {
      if (checkTaxRateForWholeSaleDealEnabled(deal, salesSetupInfo) || isDealerTradeDeal(deal)) {
        const { totalAmount, totalTax, taxRate, serviceContractTax, bpolTax } = calculateTotalEMIAmountForDealerTradeV1(
          deal,
          columnData
        );
        totalMoney = totalAmount;
        await DeskingActions.setTaxDetails({
          taxes: {
            taxRate,
            salesTax: totalTax,
            serviceContractTax,
            fairFaxCountyTax: bpolTax,
          },
          columnData,
        });
      } else {
        const totalAmount = calculateTotalEMIAmountForDealerTrade(deal, columnData);
        totalMoney = totalAmount;
      }

      await DeskingActions.setEMIAmountForDealerTrade(totalMoney);
      await DeskingActions.onDownPaymentChange({
        downPayment: totalMoney,
        id: 0,
      });
      await savePaymentsDataForSelectedVehicle();
    }
  };

  calculateEMIamountForOnlyTrades = async () => {
    const { DeskingActions, deal, savePaymentsDataForSelectedVehicle } = this.props;
    if (isCategory4Deal(deal)) {
      const netTradeINValue = -DealReader.getNetTradeInValue(deal);
      DeskingActions.setEMIAmountForDealerTrade(netTradeINValue);
      await DeskingActions.onDownPaymentChange({
        downPayment: netTradeINValue,
        id: 0,
      });
      savePaymentsDataForSelectedVehicle();
    }
  };

  changeDealStatusToQuote = async () => {
    const { DeskingActions, deal, CommonActions } = this.props;
    if (isCategory1Deal(deal)) {
      DeskingActions.setCallMarketScan(true);
    }
    const response = await DeskingActions.sendToSales({
      status: DEAL_STATUS.QUOTE,
      activity: DEAL_ACTIVITY.RETURNED_TO_QUOTE,
    });
    const warningMessage = _get(response, 'data.warning');
    if (warningMessage) {
      toaster(TOASTER_TYPE.WARN, warningMessage);
    }
    DeskingActions.setSelectedtab(LEFT_PANEL_DEAL_STATUS_MAP()[DEAL_STATUS.QUOTE]);

    const isDealSetupRulesEnabled = isArcLiteProgram() ? DealerPropertyHelper.isDealStatusRulesConfigEnabled() : true;
    if (isDealSetupRulesEnabled) {
      const dealNumber = DealReader.getDealNumber(deal);
      DeskingActions.fetchDeal(dealNumber);
      const primaryVehicle = DealReader.getPrimaryVehicle(deal);
      const vehicleId = VehicleReader.getVehicleId(primaryVehicle);
      await CommonActions.getVehicleInfoById(vehicleId);
    }
  };

  modifyAssignees = (persona, userId) => {
    const { DeskingActions, deal } = this.props;
    const previousAssignees = _get(deal, 'assignee');
    const updatedAssignees = {
      ...previousAssignees,
      [PERSONA_API_MAP[persona]]: [userId],
    };

    DeskingActions.setAssigneeList({
      dealNumber: DealReader.getDealNumber(deal),
      assignees: updatedAssignees,
    });
  };

  patchAssignees = async newAssignees => {
    const { DeskingActions, deal } = this.props;
    const previousAssignees = _get(deal, 'assignee');
    const updatedAssignees = {
      ...previousAssignees,
      ...newAssignees,
      mapOfAssignees: {
        ...(previousAssignees?.mapOfAssignees || {}),
        ...(newAssignees?.mapOfAssignees || {}),
      },
    };
    await DeskingActions.setAssigneeList({
      dealNumber: DealReader.getDealNumber(deal),
      assignees: updatedAssignees,
    });
    GlobalWarning.updateBEWarnings([BACKEND_GLOBAL_WARNINGS_ACTIONS.DEAL_PUSH_WARNINGS]);

    CalculationUpdateNotification.compareAndNotifyChange({
      dealId: _get(deal, 'id'),
    });
  };

  selectUser = async userId => {
    const { DeskingActions } = this.props;
    await DeskingActions.setSelectedFinanceManager(userId);
    this.modifyAssignees(PERSONAS.F_nd_I_MANAGER, userId);
  };

  // selectSalesManager = async (userId) => {
  //   const { DeskingActions } = this.props;
  //   await DeskingActions.setSelectedSalesManager(userId);
  //   this.modifyAssignees(PERSONAS.SALES_MANAGER, userId);
  // }

  isDmvDeskAvailable = () => {
    const { isDealAcquired, selectedLeftPanel } = this.props;
    const isRecapTabEnabled = LEFT_PANEL_ITEMS.RECAP === selectedLeftPanel;
    return isRecapTabEnabled && hasDmvDeskEdit() && isDealAcquired;
  };

  canSendtoSalesAvailable = () => {
    const { isDealAcquired, selectedLeftPanel, deal } = this.props;
    const isRecapTabEnabled = LEFT_PANEL_ITEMS.RECAP === selectedLeftPanel;
    const dealStatus = _get(deal, ['status']);
    const isDealUnwound = DEAL_STATUS.UNWIND === dealStatus;
    return isRecapTabEnabled && isDealAcquired && (canSendDealsToSales() || !isDealUnwound);
  };

  canSeeGrossSummary = () => {
    const { isDealAcquired, selectedLeftPanel } = this.props;
    const isRecapTabEnabled = LEFT_PANEL_ITEMS.RECAP === selectedLeftPanel;
    const isDeskingTabSelected = LEFT_PANEL_ITEMS.DESKING === selectedLeftPanel;
    return (
      hasGrossSummaryView() && hasCostAndGrossView() && (isDeskingTabSelected || isRecapTabEnabled) && isDealAcquired
    );
  };

  hasPermissionToOnlyViewSellingPriceTab = () => {
    const { deal, isDealViewOnly, isFuseDeal } = this.props;
    const dealType = DealReader.getDealType(deal);
    return isDealViewOnly || !hasSellingPriceEdit() || dealType === DEAL_TYPES.FNI || isFuseDeal;
  };

  renderPopoverContent = () => <div className="p-8">{__('Gross Summary')}</div>;

  renderAllPersonaTab = props => {
    const { deal, salesSetupInfo, rolesList, rolesListV2 } = this.props;

    return (
      <AllPersonaTab
        {...props}
        deal={deal}
        rolesList={rolesList}
        rolesListV2={rolesListV2}
        patchAssignees={this.patchAssignees}
        rolesForAssignee={salesSetupInfo?.rolesForAssignee}
        setupIntegrationKeys={SalesSetupReader.selectIntegrationKeys(salesSetupInfo)}
        assigneeLimits={
          isInchcape()
            ? SalesSetupReader.getMapofAssigneeLimits(salesSetupInfo)
            : SalesSetupReader.getAssigneeLimits(salesSetupInfo)
        }
      />
    );
  };

  isFNIDisabled = () => {
    const { deal } = this.props;
    const dealStatusWeight = DEAL_STATUS_WEIGHTS[deal.status];

    const dealStatuesRulesEnabled = isArcLiteProgram() ? DealerPropertyHelper.isDealStatusRulesConfigEnabled() : true;
    return dealStatuesRulesEnabled
      ? dealStatusWeight > DEAL_STATUS_WEIGHTS[DEAL_STATUS.CONFIRMED]
      : dealStatusWeight > DEAL_STATUS_WEIGHTS[DEAL_STATUS.QUOTE];
  };

  handleRefreshDeal = async () => {
    const { DeskingActions, deal, blockScreen, unBlockScreen } = this.props;
    const dealNumber = DealReader.getDealNumber(deal);
    if (_isFunction(blockScreen)) blockScreen(true, TOASTER_STATUS.INFO, __('Refreshing Vat Info for Deal'));
    await DeskingActions.refreshDealForVatInfo(dealNumber);
    if (_isFunction(unBlockScreen)) unBlockScreen();
  };

  toggleUpdateVehicleModalVisibility = () => {
    const { updateVehicleModalVisible } = this.state;
    this.setState({
      updateVehicleModalVisible: !updateVehicleModalVisible,
    });
  };

  handleSubmitVehicleTransferModal = async () => {
    const { vehicleTransferDetails, blockScreen, unBlockScreen, DeskingActions, CommonActions, deal } = this.props;
    const currentStatus = tget(vehicleTransferDetails, 'currentStatus');

    if (currentStatus === VEHICLE_TRANSFER_STATUS.REJECTED) {
      this.setState({ shouldShowVehicleTransferModal: false });
      if (_isFunction(blockScreen)) blockScreen(true);

      const newVehicles = _castArray({
        primaryVehicle: true,
        temp: true,
        pricingDetails: {},
      });

      const { response } = await DeskingActions.updateDeal(deal?.dealNumber, {
        ...deal,
        vehicles: newVehicles,
      });
      if (!_isEmpty(response)) {
        CommonActions.updateVehicleTransferDetails({});
      }
      if (_isFunction(unBlockScreen)) unBlockScreen();
    }

    GlobalWarning.showWarnings();
    this.setState({ shouldShowVehicleTransferModal: false });
  };

  handleCancelVehicleTransferModal = () => {
    this.setState({ shouldShowVehicleTransferModal: false });
  };

  renderVehicleTransferModal = () => {
    const { deal, vehicleTransferDetails, dealers } = this.props;
    const { shouldShowVehicleTransferModal } = this.state;
    const primaryVehicle = DealReader.getPrimaryVehicle(deal);
    const currentStatus = tget(vehicleTransferDetails, 'currentStatus');
    const centralApprovalStatus = tget(vehicleTransferDetails, 'centralApprovalStatus');

    return (
      <>
        <VehicleTransferModalV2 />
        <VehicleTransferModal
          showVehicleTransferModal={shouldShowVehicleTransferModal}
          vehicle={primaryVehicle}
          currentStatus={currentStatus}
          centralApprovalStatus={centralApprovalStatus}
          dealers={dealers}
          onSubmit={this.handleSubmitVehicleTransferModal}
          onCancel={this.handleCancelVehicleTransferModal}
        />
      </>
    );
  };

  render() {
    const {
      dealerSites,
      vehicleModels,
      vehicleMakes,
      deskingpaymentDetails,
      DeskingActions,
      deal,
      lenders,
      vehiclesInfo,
      saveDealData,
      selectedLeftPanel,
      savePaymentsDataForSelectedVehicle,
      isRecapViewEnabled,
      vehicleTypes,
      updateDeskingPaymentsDataForSelectedVehicles,
      getJournalDetails,
      getAccountDetails,
      setRequiredInitialDataForVehicles,
      CommonActions,
      unLockDealCallBack,
      isDealAcquired,
      isDealViewOnly,
      salesSetupInfo,
      trimInfos,
      isDealDirty,
      saveSellingPrice,
      onDuplicateDeal,
      updateEMIAmountOfFNIDeal,
      category3dealTypeChanges,
      contentHeight,
      onVehiclesUpdated,
      isClosedOrSoldDeal,
      setZipCodeDetailsInDeal,
      canCallMarketScan,
      updateDesking,
      toggleRecapView,
      disablePricingEditSettings,
      taxFeeConfigMetadata,
      updateStateFeeTargetingAndSaveDealData,
      isUSCalcEngineEnabled,
      isDealLocked,
      isFuseDeal,
      acertusOrderId,
      blockScreen,
      unBlockScreen,
      hasEstimatedDeliveryDateChanged,
      getFormattedDateAndTime,
      feeUpdateInfos,
      updatePaymentWithItems,
      navigate,
      location,
      invoices,
      recapApprovalDetails,
      onUpdateToggleRecapView,
    } = this.props;

    const { dmvDesking, updateVehicleModalVisible, disableFNIButton } = this.state;
    const dealType = _get(deal, 'type');
    const dealStatusWeight = DEAL_STATUS_WEIGHTS[deal.status];
    const selectedColumn = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);
    const countyTaxRateByMScan = ColumnDataReader.getCountyTaxRate(selectedColumn);
    const tradeTaxExempt = ColumnDataReader.getTradeTaxExempt(selectedColumn);
    const isDeskingTabSelected = LEFT_PANEL_ITEMS.DESKING === selectedLeftPanel;
    const isRecapTabEnabled = LEFT_PANEL_ITEMS.RECAP === selectedLeftPanel;
    const setupIntegrationKeys = SalesSetupReader.selectIntegrationKeys(salesSetupInfo);
    const generalTaxFeeSettings = SalesSetupReader.generalTaxFeeSettings(salesSetupInfo);
    const dmvDeskEnabled = isIntegrationEnabled(setupIntegrationKeys, INTEGRATION_KEYS.DMVDESK);
    const primaryVehicle = DealReader.getPrimaryVehicle(deal);
    const dealGlBalance = VehicleReader.getGLBalanace(primaryVehicle);
    const { vehicles } = deal;
    const vehiclesList = vehicles
      ? vehicles.map(vehicle => {
          const { id = '' } = vehicle;
          if (id && _has(vehiclesInfo, id)) return { ...vehiclesInfo[id], ...vehicle };
          return vehicle;
        })
      : [];

    const isVehicleChangeDisabled =
      deal.locked || isClosedOrSoldDeal || !isDealAcquired || isCategory4Deal(deal) || isFuseDeal;
    const canAdjustSellingPriceFromProfit = SalesSetupReader.getAdjustSellingPriceBasedOnVehicleGross(salesSetupInfo);
    const showMaximumResidualMSRP = SalesSetupReader.showMaximumResidualMSRP(salesSetupInfo);
    const enableSeparateTaxCalc = DealReader.getEnableSeparateTaxCalc(deal);

    const columnDataForTaxDetails = {
      [TAX_AND_ZIP_CODE_DETAILS_VS_KEY.RETAIL]: getColumnForGivenTaxType(
        deskingpaymentDetails,
        TAX_AND_ZIP_CODE_DETAILS_VS_KEY.RETAIL
      ),
      [TAX_AND_ZIP_CODE_DETAILS_VS_KEY.LEASE]: getColumnForGivenTaxType(
        deskingpaymentDetails,
        TAX_AND_ZIP_CODE_DETAILS_VS_KEY.LEASE
      ),
    };
    const dealStatus = _get(deal, ['status']);
    const editBookedDate = canEditDealBookedDate();
    const isDeskingEditAllFieldsAfterQuoteStatusEnabled = hasDeskingEditAllFieldsAfterQuoteStatus();
    const isDealTypeDealerTradeSwap = dealType === DEAL_TYPES.DEAL_TRADE_SWAP;
    const vehicleSubTypesForNewVehicleProgram = SalesSetupReader.selectVehicleSubtypeForNewProgram(salesSetupInfo);
    const isDigitalFlow = DealReader.isDealDigitalFlow(deal);
    const paymentOptions = formatPaymentOptionsFromSalesSetup(SalesSetupReader.getPaymentOptionConfigs(salesSetupInfo));
    const getLabelFromPaymentOptionsFunc = getLabelFromPaymentOptions(paymentOptions);
    const dealNumber = DealReader.getDealNumber(deal);

    return (
      <div className={classNames('d-flex', 'flex-row', 'full-width align-items-center', styles.container)}>
        <PropertyControlledComponent controllerProperty={DealerPropertyHelper.isVehicleTransferV2Enabled()}>
          {this.renderVehicleTransferModal()}
        </PropertyControlledComponent>
        <AssumptivePayOff ref={this.assumptiveTradePayOffRef} />
        <AutoScrollDiv className={classNames('d-flex', 'flex-row', 'align-items-center', styles.leftMenu)}>
          <PropertyControlledComponent controllerProperty={isDeskingTabSelected}>
            <SellingPriceTab
              deal={deal}
              DeskingActions={DeskingActions}
              vehiclesInfo={vehiclesInfo}
              discountCapConfig={SalesSetupReader.getDiscountCapData(salesSetupInfo)}
              discountMatrixConfig={SalesSetupReader.getDiscountMatrixData(salesSetupInfo)}
              selectedLeftPanel={selectedLeftPanel}
              viewOnly={this.hasPermissionToOnlyViewSellingPriceTab()}
              saveDealAndRefreshMarketScan={this.saveDealAndRefreshMarketScan}
              calculateEMIamountForDealrTradeDealType={this.calculateEMIamountForDealrTradeDealType}
              dealGlBalance={dealGlBalance}
              getJournalDetails={getJournalDetails}
              getAccountDetails={getAccountDetails}
              CommonActions={CommonActions}
              selectedColumn={selectedColumn}
              isDealDirty={isDealDirty}
              saveDealData={saveDealData}
              canAdjustSellingPriceFromProfit={canAdjustSellingPriceFromProfit}
              disablePricingEditSettings={disablePricingEditSettings}
              saveSellingPrice={saveSellingPrice}
              showMaximumResidualMSRP={showMaximumResidualMSRP}
              deskingpaymentDetails={deskingpaymentDetails}
            />
          </PropertyControlledComponent>

          <PropertyControlledComponent controllerProperty={!isRecapTabEnabled}>
            <div className={styles.divider} />
          </PropertyControlledComponent>

          <PropertyControlledComponent
            controllerProperty={(isDeskingTabSelected || isRecapTabEnabled) && !isInchcape()}>
            <ZipCodeTab
              deal={deal}
              viewOnly={!hasZipcodeEdit() || isDealViewOnly || isRecapTabEnabled || isFuseDeal}
              DeskingActions={DeskingActions}
              countyTaxRateByMScan={countyTaxRateByMScan}
              saveDealAndRefreshMarketScanIfDirty={this.saveDealAndRefreshMarketScanIfDirty}
              tradeTaxExempt={tradeTaxExempt}
              setupIntegrationKeys={setupIntegrationKeys}
              isDealDirty={isDealDirty}
              selectedColumnPaymentType={ColumnDataReader.getPaymentType(selectedColumn)}
              selectedColumn={selectedColumn}
              updateEMIAmountOfFNIDeal={updateEMIAmountOfFNIDeal}
              category3dealTypeChanges={category3dealTypeChanges}
              generalTaxFeeSettings={generalTaxFeeSettings}
              setZipCodeDetailsInDeal={setZipCodeDetailsInDeal}
              canCallMarketScan={canCallMarketScan}
              updateDesking={updateDesking}
              enableSeparateTaxCalc={enableSeparateTaxCalc}
              columnDataForTaxDetails={columnDataForTaxDetails}
              salesSetupInfo={salesSetupInfo}
              taxFeeConfigMetadata={taxFeeConfigMetadata}
              isUSCalcEngineEnabled={isUSCalcEngineEnabled}
              getFormattedDateAndTime={getFormattedDateAndTime}
            />
          </PropertyControlledComponent>
          <PropertyControlledComponent controllerProperty={isDeskingTabSelected && isRRG() && !isCategory4Deal(deal)}>
            <DealWithorWithoutVat deal={deal} DeskingActions={DeskingActions} />
          </PropertyControlledComponent>
          <div className={styles.divider} />
          <PropertyControlledComponent controllerProperty={isDeskingTabSelected || isRecapTabEnabled}>
            <ContractDate
              deal={deal}
              lenders={lenders}
              DeskingActions={DeskingActions}
              viewOnly={!hasContractDateEdit() || isDealViewOnly || isFuseDeal}
              defaultHeaderDate={SalesSetupReader.getDefaultHeaderDate(salesSetupInfo)}
              salesSetupInfo={salesSetupInfo}
              editBookedDate={hasDeskingEdit() && editBookedDate && !isDealViewOnly && !isFuseDeal}
              deskingpaymentDetails={deskingpaymentDetails}
              savePaymentsDataForSelectedVehicle={savePaymentsDataForSelectedVehicle}
              hasEstimatedDeliveryDateChanged={hasEstimatedDeliveryDateChanged}
              feeUpdateInfos={feeUpdateInfos}
              updatePaymentWithItems={updatePaymentWithItems}
              navigate={navigate}
              location={location}
              blockScreen={blockScreen}
              unBlockScreen={unBlockScreen}
            />
          </PropertyControlledComponent>
          <div className={styles.divider} />
          <PropertyControlledComponent controllerProperty={isDeskingTabSelected && !isDealTypeDealerTradeSwap}>
            <TradeInValuationTab
              DeskingActions={DeskingActions}
              saveDealIfDirty={this.saveDealIfDirty}
              viewOnly={!(hasTradeInPricingEdit() || hasTradeInDetailsEdit()) || isDealViewOnly || isFuseDeal}
              deal={deal}
              CommonActions={CommonActions}
              trimInfos={trimInfos}
              calculateEMIamountForDealrTradeDealType={this.calculateEMIamountForDealrTradeDealType}
              calculateEMIamountForOnlyTrades={this.calculateEMIamountForOnlyTrades}
              contentHeight={contentHeight}
              isBookedDeal={dealStatusWeight >= DEAL_STATUS_WEIGHTS[DEAL_STATUS.BOOKED]}
              isClosedOrSoldDeal={dealStatusWeight >= DEAL_STATUS_WEIGHTS[DEAL_STATUS.CLOSED_OR_SOLD]}
              vehicleTypes={vehicleTypes}
              updateStateFeeTargetingAndSaveDealData={updateStateFeeTargetingAndSaveDealData}
              isDealLocked={isDealLocked}
              salesSetupInfo={salesSetupInfo}
              blockScreen={blockScreen}
              unBlockScreen={unBlockScreen}
            />
            <div className={styles.divider} />
          </PropertyControlledComponent>

          <PropertyControlledComponent controllerProperty={isDealTypeDealerTradeSwap}>
            <BuildVehicleTab
              deal={deal}
              dealType={dealType}
              trimInfos={trimInfos}
              vehicleSubTypesForNewVehicleProgram={vehicleSubTypesForNewVehicleProgram}
              dealerSites={dealerSites}
              contentHeight={contentHeight}
              vehicleModels={vehicleModels}
              vehicleMakes={vehicleMakes}
              DeskingActions={DeskingActions}
              saveDealData={saveDealData}
              actions={CommonActions}
            />
          </PropertyControlledComponent>

          <PropertyControlledComponent controllerProperty={isDeskingTabSelected}>
            <ChangeVehicleTab
              vehicles={vehiclesList}
              onVehiclesUpdated={onVehiclesUpdated}
              disabled={isVehicleChangeDisabled}
              deal={deal}
              deskingpaymentDetails={deskingpaymentDetails}
              DeskingActions={DeskingActions}
              savePaymentsDataForSelectedVehicle={savePaymentsDataForSelectedVehicle}
              saveDealData={saveDealData}
              updateDeskingPaymentsDataForSelectedVehicles={updateDeskingPaymentsDataForSelectedVehicles}
              setRequiredInitialDataForVehicles={setRequiredInitialDataForVehicles}
              salesSetupInfo={salesSetupInfo}
              blockScreen={blockScreen}
            />
            <div className={styles.divider} />
          </PropertyControlledComponent>

          <PropertyControlledComponent controllerProperty={isDeskingTabSelected}>
            {this.renderAllPersonaTab({ viewOnly: !hasDeskingEdit() || isDealViewOnly })}

            <div className={styles.divider} />
          </PropertyControlledComponent>

          <PropertyControlledComponent controllerProperty={isRecapTabEnabled}>
            {this.renderDownpaymentTrigger()}
            <div className={styles.divider} />
          </PropertyControlledComponent>

          <PropertyControlledComponent
            controllerProperty={isRecapTabEnabled && !isInchcapeOrRRG() && !isArcLiteProgram()}>
            <DmvRosTab
              acertusOrderId={acertusOrderId}
              dmvDesking={dmvDesking}
              sendToDMV={this.dmvDesk}
              deal={deal}
              DeskingActions={DeskingActions}
              saveDealData={saveDealData}
              dmvRos={DealReader.getDmvRosNumber(deal)}
              viewOnly={!hasFniEdit() || isDealViewOnly || isFuseDeal}
              isDMVDeskingEnabled={dmvDeskEnabled && this.isDmvDeskAvailable()}
            />
            <div className={styles.divider} />
          </PropertyControlledComponent>
          <PropertyControlledComponent controllerProperty={isRecapTabEnabled && !isInchcapeOrRRG()}>
            {this.renderAllPersonaTab({
              viewOnly: !hasFniEdit() || isDealViewOnly,
              userTypesToShow: [DEAL_USER_TYPE.F_nd_I_MANAGER],
              triggerSubTitle: DEAL_USER_TYPE_DISPLAY_NAME[DEAL_USER_TYPE.F_nd_I_MANAGER],
              isRecapTabEnabled,
            })}

            <div className={styles.divider} />
          </PropertyControlledComponent>
        </AutoScrollDiv>
        <div className={styles.rightActions}>
          <PropertyControlledComponent controllerProperty={this.canSendtoSalesAvailable() && !isInchcapeOrRRG()}>
            <Button
              className="marginR16"
              onClick={this.changeDealStatusToQuote}
              disabled={
                DEAL_STATUS_TO_NOT_SEND_TO_SALES.includes(dealStatus) ||
                (isDeskingEditAllFieldsAfterQuoteStatusEnabled === false &&
                  ![DEAL_STATUS.QUOTE].includes(deal.status)) ||
                isFuseDeal
              }>
              {__('Send to Sales')}
            </Button>
          </PropertyControlledComponent>
          <PropertyControlledComponent
            controllerProperty={dmvDeskEnabled && this.isDmvDeskAvailable() && !isArcLiteProgram()}>
            <Button
              view="primary"
              className="marginR16"
              onClick={this.dmvDesk}
              loading={dmvDesking}
              disabled={isFuseDeal}>
              {__('DMV')}
            </Button>
          </PropertyControlledComponent>
          <PropertyControlledComponent controllerProperty={isDeskingTabSelected && hasDeskingEdit() && isDealAcquired}>
            <PropertyControlledComponent
              controllerProperty={!(isRRG() && [DEAL_TYPES.ONLY_TRADES, DEAL_TYPES.DEALER_TRADE].includes(dealType))}>
              {[DEAL_TYPES.WHOLESALE, DEAL_TYPES.DEALER_TRADE].includes(dealType) ? (
                <PropertyControlledComponent
                  controllerProperty={
                    !DealerPropertyHelper.isDealStatusConfigEnabled() ||
                    SalesSetupReader.isDealStatusEnabled(salesSetupInfo, DEAL_STATUS.RECAP)
                  }>
                  <Button
                    className="marginR16"
                    onClick={onUpdateToggleRecapView}
                    disabled={dealStatusWeight > DEAL_STATUS_WEIGHTS[DEAL_STATUS.QUOTE] || isFuseDeal}>
                    {__('Recap')}
                  </Button>
                </PropertyControlledComponent>
              ) : (
                <PropertyControlledComponent controllerProperty={!isInchcapeOrRRG()}>
                  <Button
                    className="marginR16"
                    onClick={this.checkIfTheVehicleShouldBeTransferred}
                    disabled={this.isFNIDisabled() || isFuseDeal || disableFNIButton}>
                    {DealerPropertyHelper.isStandAloneCRMEnabled() ? __('Push to DMS') : __('F & I')}
                  </Button>
                </PropertyControlledComponent>
              )}
            </PropertyControlledComponent>
          </PropertyControlledComponent>

          <PropertyControlledComponent controllerProperty={isInchcape() && dealType === DEAL_TYPES.FNI}>
            <Button className="marginR16" view="tertiary" onClick={this.toggleUpdateVehicleModalVisibility}>
              {__('Update Vehicle Details')}
            </Button>
            <PropertyControlledComponent controllerProperty={updateVehicleModalVisible}>
              <UpdateVehicleModal
                visible={updateVehicleModalVisible}
                toggleUpdateVehicleModalVisibility={this.toggleUpdateVehicleModalVisibility}
                vehicleInfo={primaryVehicle}
                deal={deal}
                DeskingActions={DeskingActions}
                component="subHeader"
              />
            </PropertyControlledComponent>
          </PropertyControlledComponent>

          <PropertyControlledComponent controllerProperty={this.canSeeGrossSummary()}>
            <Popover
              placement={POPOVER_PLACEMENT.TOP}
              trigger={POPOVER_TRIGGER.HOVER}
              content={this.renderPopoverContent()}>
              <Button
                view={Button.VIEW.ICON}
                icon="icon-gross-summary"
                className="marginR16"
                onClick={this.handleGrossSummaryClick}
              />
            </Popover>
          </PropertyControlledComponent>

          <PropertyControlledComponent controllerProperty={isRRG() && isDeskingTabSelected}>
            <OrderRequestButton dealNumber={dealNumber} dealStatus={DealReader.getDealStatus(deal)} />
            <Divider type="vertical" />
          </PropertyControlledComponent>

          <SideMenu
            isRecapViewEnabled={isRecapViewEnabled}
            toggleRecapView={onUpdateToggleRecapView}
            selectedLeftPanel={selectedLeftPanel}
            unLockDealCallBack={unLockDealCallBack}
            onDuplicateDeal={onDuplicateDeal}
            toggleRecapViewFromDesking={toggleRecapView}
            onSubmitDeal={this.checkForAssumptivePayOff}
            onConfirmDeal={this.onConfirmDeal}
            isDealLocked={isDealLocked}
            blockScreen={blockScreen}
            unBlockScreen={unBlockScreen}
            getLabelFromPaymentOptions={getLabelFromPaymentOptionsFunc}
            getFormattedDateAndTime={getFormattedDateAndTime}
            invoices={invoices}
            recapApprovalDetails={recapApprovalDetails}
          />
        </div>
      </div>
    );
  }
}

const mapStateToProps = globalState => {
  const state = _get(globalState, BASE_REDUCER_KEY);
  return {
    acertusOrderId: _get(state, ['desking', 'dealPreferences', 'acertusOrderId']) || EMPTY_STRING,
    rolesList: _get(state, 'common.rolesInfo') || EMPTY_ARRAY,
    rolesListV2: _get(state, 'common.rolesInfoV2') || EMPTY_OBJECT,
    dealerSites: _get(state, 'common.dealerSites'),
    vehicleModels: getAllModels(state),
    vehicleMakes: getAllVehicleMakes(state),
    feeUpdateInfos: _get(state, 'desking.feeUpdateInfos') || EMPTY_OBJECT,
    invoices: _get(state, 'desking.invoices', EMPTY_ARRAY),
    dealers: _get(state, 'common.dealers'),
    vehicleTransferDetails: tget(state, 'common.vehicleTransferDetails', EMPTY_OBJECT),
  };
};

const Subheader = connect(mapStateToProps, null)(SubHeader);

export default compose(
  withDeskingContext,
  withAccountingHelpers,
  WithMandatoryReviewHOC,
  VehicleConfirmationModalHOC,
  AllowMultipleReservationsOnVehicleHOC
)(Subheader);

SubHeader.defaultProps = {
  deskingpaymentDetails: EMPTY_OBJECT,
  vehiclesInfo: EMPTY_OBJECT,
  selectedLeftPanel: '',
  isRecapViewEnabled: false,
  toggleRecapView: _noop,
  isDealDirty: false,
  unLockDealCallBack: _noop,
  isDealAcquired: false,
  isDealViewOnly: false,
  trimInfos: EMPTY_OBJECT,
  vehicleTransferDetails: EMPTY_OBJECT,
  isClosedOrSoldDeal: false,
  vehicleTypes: EMPTY_ARRAY,
  checkIsMultipleReservationAllowed: _noop,
  showVehicleConfirmationModal: _noop,
  dealers: EMPTY_ARRAY,
  blockScreen: _noop,
  unBlockScreen: _noop,
};

SubHeader.propTypes = {
  deskingpaymentDetails: PropTypes.object,
  deal: PropTypes.object.isRequired,
  DeskingActions: PropTypes.object.isRequired,
  vehiclesInfo: PropTypes.object,
  saveDealData: PropTypes.func.isRequired,
  getMarketScanData: PropTypes.func.isRequired,
  selectedLeftPanel: PropTypes.string,
  isRecapViewEnabled: PropTypes.bool,
  toggleRecapView: PropTypes.func,
  getMarketScanPayloadObject: PropTypes.func.isRequired,
  savePaymentsDataForSelectedVehicle: PropTypes.func.isRequired,
  isDealDirty: PropTypes.bool,
  isDealViewOnly: PropTypes.bool,
  getJournalDetails: PropTypes.func.isRequired,
  getAccountDetails: PropTypes.func.isRequired,
  CommonActions: PropTypes.func.isRequired,
  unLockDealCallBack: PropTypes.bool,
  isDealAcquired: PropTypes.bool,
  salesSetupInfo: PropTypes.object.isRequired,
  trimInfos: PropTypes.object,
  vehicleTransferDetails: PropTypes.object,
  onDuplicateDeal: PropTypes.func.isRequired,
  updateEMIAmountOfFNIDeal: PropTypes.func.isRequired,
  category3dealTypeChanges: PropTypes.func.isRequired,
  contentHeight: PropTypes.number.isRequired,
  onVehiclesUpdated: PropTypes.func.isRequired,
  setZipCodeDetailsInDeal: PropTypes.func.isRequired,
  isClosedOrSoldDeal: PropTypes.bool,
  vehicleTypes: PropTypes.array,
  canCallMarketScan: PropTypes.bool.isRequired,
  updateDesking: PropTypes.func.isRequired,
  updateDeskingForColumnIds: PropTypes.func.isRequired,
  disablePricingEditSettings: PropTypes.bool.isRequired,
  getUpdatedRebatesForColumn: PropTypes.func.isRequired,
  showMandatoryForm: PropTypes.func.isRequired,
  taxFeeConfigMetadata: PropTypes.object.isRequired,
  saveSellingPrice: PropTypes.func.isRequired,
  updateDeskingPaymentsDataForSelectedVehicles: PropTypes.func.isRequired,
  setRequiredInitialDataForVehicles: PropTypes.func.isRequired,
  rolesList: PropTypes.array.isRequired,
  updateStateFeeTargetingAndSaveDealData: PropTypes.func.isRequired,
  isUSCalcEngineEnabled: PropTypes.bool.isRequired,
  checkIsMultipleReservationAllowed: PropTypes.func,
  showVehicleConfirmationModal: PropTypes.func,
  dealers: PropTypes.array,
  blockScreen: PropTypes.func,
  unBlockScreen: PropTypes.func,
  navigate: PropTypes.func.isRequired,
  location: PropTypes.object.isRequired,
};
