import React, { PureComponent } from 'react';
import { compose } from 'recompose';
import PropTypes from 'prop-types';
import classnames from 'classnames';

import produce from 'immer';
import _get from 'lodash/get';
import _map from 'lodash/map';
import _isEmpty from 'lodash/isEmpty';
import _noop from 'lodash/noop';
import _isEqual from 'lodash/isEqual';
import _isBoolean from 'lodash/isBoolean';
import _find from 'lodash/find';
import _toNumber from 'lodash/toNumber';
import _isNumber from 'lodash/isNumber';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import * as SalesSetupReader from '@tekion/tekion-base/marketScan/readers/salesSetup.reader';
import {
  isDealConfirmed,
  isDealIntermittent,
  isDealRevoked,
} from '@tekion/tekion-base/marketScan/helpers/deal.helpers';
import { getAllCColumnsIDs } from '@tekion/tekion-base/marketScan/readers/desking.reader';
import { INTEGRATION_KEYS } from '@tekion/tekion-base/constants/retail/salesSetup.constants';
import * as DealReader from '@tekion/tekion-base/marketScan/readers/deal.reader';
import { isInchcape, isInchcapeOrRRG, isRRG } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
import {
  isCategory1Deal,
  getLeaseorOnePayColumnIds,
  CashieringPdfEvent,
  CASHIERING_NOTIFICATION_TYPES,
} from '@tekion/tekion-base/marketScan/utils/desking.utils';
import { checkForActiveContracts } from '@tekion/tekion-base/marketScan/helpers/desking.helpers';
import TEnvReader from '@tekion/tekion-base/readers/Env';
import userReader from 'tbase/readers/User';
import { PERSONAS } from 'tbase/constants/personas';
import ActivityLogs from '@tekion/tekion-widgets/src/appServices/service/organisms/AuditLogs';
import Button from '@tekion/tekion-components/src/atoms/Button';
import Content from '@tekion/tekion-components/src/atoms/Content';
import Popover from '@tekion/tekion-components/src/molecules/popover';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import { toaster, dismissToast } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';
import FontIcon from '@tekion/tekion-components/src/atoms/FontIcon';

import { MESSAGES, TOASTER_STATUS, DEPOSIT_MESSAGES, VEHICLE_STATUS } from 'constants/pages';
import { getAPIError } from 'utils/error.reader';
import { isFuseDeal, shouldDuplicateBeAllowed } from 'utils/deal.util';
import DealsAPI from 'pages/desking/desking.api';
import GlobalWarning from 'pages/desking/components/globalWarnings';
import withDeskingContext from 'pages/desking/withDeskingContext';
import {
  hasLeaseOptionsEdit,
  hasGrossSummaryView,
  hasDealCreate,
  hasFniEdit,
  hasCostAndGrossView,
  hasWorkingCashEdit,
  isConfirmDealEnabled,
  isDeskingKebabActionsEnabled,
  isDeskingKebabSummaryEnabled,
  hasCashieringRefund,
} from 'permissions/desking.permissions';
import { LEFT_PANEL_ITEMS, LEFT_PANEL_DEAL_STATUS_MAP, SOURCE_TYPE } from 'pages/desking/desking.constants';
import DescriptionSwitch from 'molecules/descriptionSwitch';
import GrossBreakUpModal from 'pages/desking/components/grossBreakUp/GrossBreakUpModal';
import DealAuditLogs from 'pages/desking/components/dealAuditLogs';
import { DEAL_STATUS, PASSWORD_MODAL_TITLE, DEAL_STATUS_WEIGHTS, PROGRAM_CODE } from 'pages/deallist/deal.constants';
import { DEAL_SOURCE } from '@tekion/tekion-base/marketScan/constants/deal.constants';
import { UnwindDealModal, UnlockDealModal } from 'pages/deallist/components';
import { canUnbook, canUnlock } from 'pages/deallist/deal.util';
import OutsideClickDetector from 'molecules/outsideClickDetector';
import LeaseConfigurations from 'pages/desking/components/leaseConfigurations';
import ConfirmationModal from 'molecules/ConfirmationModal';
import WithMandatoryReviewHOC from 'organisms/MandatoryAndReviewForm/MandatoryReviewHOC';
import VehicleConfirmationModalHOC from 'molecules/VehicleConfirmationModalHOC/VehicleConfirmationModalHOC';
import AllowMultipleReservationsOnVehicleHOC from 'molecules/AllowMultipleReservationsOnVehicleHOC';
import { isIntegrationEnabled } from 'pages/desking/desking.selectors';
import CashieringModal from 'pages/desking/components/cashiering/CashieringModal';
import { findArrayItem, getNumber, formatCurrencyWithMoneyFormat } from 'utils';
import OrderFormGeneration from 'molecules/orderFormGeneration';
import { ORDER_FORM_KEYS } from 'molecules/orderFormGeneration/orderFormGeneration.constants';
import { DEAL_TYPES } from '@tekion/tekion-base/constants/deal/type';
import {
  LEASE_WORKING_CASH,
  NEGATIVE_TRADE_PAYOFFS,
  LEASE_WORKING_CASH_DISPLAY_NAMES,
  NEGATIVE_TRADE_PAYOFFS_DISPLAY_NAMES,
  DEPOSIT_LABELS,
  ACTIVITY_LOG_STATUS_CONFIGS,
} from 'constants/constants';
import CalcEngineProperties from 'utils/CalcEngineProperties';
import { BACKEND_GLOBAL_WARNINGS_ACTIONS } from 'commonActions/constants/backendWarnings.constant';

import resolveActivityLogReportsList from '../resolvers/activityLog.resolver';
import {
  cancelOrderFormVisibility,
  createOrderFormVisibility,
  getFilteredOverlaySections,
  regenerateOrderFormVisibility,
  formatActivityLogForUI,
  checkIfCustCashAndDepositGen,
  shouldShowPauseDealAction,
} from './sideMenu.helpers';
import CalculationUpdateNotification from '../../calculationUpdateNotification';

import styles from './sideMenu.module.scss';
import './sideMenu.antdCollpase.scss';
import { fetchAppointments } from '../../../desking.helpers';
import { CREDIT_APPLICATION_STATUS_CONFIGS } from './sideMenu.constants';
import { recapApprovalDataReader } from '../../fniRecap/FNIRecap.readers';
import { getRecapApprovalWarnings } from '../../fniRecap/fniRecap.utils';

const { SUCCESS, ERROR, INFO } = TOASTER_STATUS;

class SideMenu extends PureComponent {
  unlockDealRef = React.createRef();

  unwindDealRef = React.createRef();

  constructor(props) {
    super(props);
    this.state = {
      workingCashModalVisible: false,
      overlayVisible: false,
      activityLogVisibility: false,
      dealAuditLogsVisible: false,
      activityLogs: EMPTY_ARRAY,
      visibleCashieringModal: false,
      fniProductRegistrationVisible: false,
      orderFormRegenrationRequired: false,
      isRefundMode: false,
      appointmentsData: EMPTY_ARRAY,
      invoiceNumber: null,
    };
    this.cashieringModalRef = React.createRef();
  }

  componentDidMount() {
    this.addEventListener();
  }

  componentWillUnmount() {
    this.removeEventListeners();
  }

  removeEventListeners = () => {
    CashieringPdfEvent.removeListener(CASHIERING_NOTIFICATION_TYPES.PDF_GENERATED, this.toggleModal);
  };

  addEventListener = () => {
    CashieringPdfEvent.on(CASHIERING_NOTIFICATION_TYPES.PDF_GENERATED, this.toggleModal);
  };

  toggleModal = ({ pdfGenerated = false, openModal = false, refund = false, invoiceNumber = EMPTY_STRING }) => {
    if (pdfGenerated && openModal === 'true') {
      this.toggleCashieringModal(refund === 'true', invoiceNumber);
    }
  };

  openOverlay = () => {
    this.onOrderFormRegeneration();
    this.setState({ overlayVisible: true });
  };

  closeOverlay = () => {
    this.setState({ overlayVisible: false });
  };

  onClickGrossSummary = () => {
    this.closeOverlay();
    GrossBreakUpModal.open();
  };

  getLogsAndLookupData = async () => {
    const { deal, getFormattedDateAndTime, getLabelFromPaymentOptions } = this.props;
    const dealNumber = DealReader.getDealNumber(deal);
    const { response } = await DealsAPI.fetchActivityLogs(dealNumber);
    const activityLogs = await formatActivityLogForUI(response, getFormattedDateAndTime, getLabelFromPaymentOptions);
    return activityLogs;
  };

  openActivityLogs = async () => {
    this.closeOverlay();

    const { deal, getFormattedDateAndTime, getLabelFromPaymentOptions } = this.props;
    const dealNumber = DealReader.getDealNumber(deal);
    const { response } = await DealsAPI.fetchActivityLogs(dealNumber);
    const { activityLogs, lookupData } = await resolveActivityLogReportsList(
      formatActivityLogForUI(response, getFormattedDateAndTime, getLabelFromPaymentOptions)
    );

    this.setState({
      activityLogVisibility: true,
      activityLogs,
      lookupData,
    });
  };

  closeActivityLogs = () => {
    this.setState({ activityLogVisibility: false });
  };

  openDealAuditLogs = () => {
    this.closeOverlay();
    this.setState({ dealAuditLogsVisible: true });
  };

  closeDealAuditLogs = () => {
    this.setState({ dealAuditLogsVisible: false });
  };

  unwindByVoidingPenContracts = async () => {
    ConfirmationModal.showLoader();
    const newDealResponse = await this.onChoosingToUnwind(true);
    const newDeal = _get(newDealResponse, 'deal');
    const penInfo = DealReader.getPenInfo(newDeal);
    const hasActiveContracts = checkForActiveContracts(penInfo);
    if (hasActiveContracts) {
      const unwindError = _get(newDealResponse, 'penDealSetResponse.error.errorMessage');
      toaster(ERROR, unwindError || MESSAGES.FAILED_TO_VOID_CONTRACTS);
    } else {
      toaster(SUCCESS, MESSAGES.CONTRACTS_VOIDED_SUCCESSFULLY);
    }
    ConfirmationModal.hideLoader();
    ConfirmationModal.close();
  };

  unwindWithoutVoidingPencontracts = async () => {
    ConfirmationModal.showLoader();
    await this.onChoosingToUnwind(false);
    ConfirmationModal.hideLoader();
    ConfirmationModal.close();
  };

  onChoosingToUnwind = async voidStatus => {
    const { DeskingActions, deal: previousDealObj } = this.props;
    const dealNumber = DealReader.getDealNumber(previousDealObj);
    let apiResponse = EMPTY_OBJECT;
    if (isInchcapeOrRRG()) {
      const payload = {
        fromStatus: DealReader.getDealStatus(previousDealObj),
        toStatus: DEAL_STATUS.INVOICED,
        deal: { dealNumber: DealReader.getDealNumber(previousDealObj) },
      };
      const updateDealstatusResponse = await DealsAPI.updateDealStatus(payload);
      if (updateDealstatusResponse?.response) {
        const newDealObject = updateDealstatusResponse?.response?.deal || {};
        apiResponse = {
          response: {
            deal: { ...previousDealObj, ...newDealObject, version: updateDealstatusResponse?.response?.version },
          },
        };
      }
    } else {
      apiResponse = await DeskingActions.unwindDeal({
        dealNumber,
        voidPenContracts: voidStatus,
        scheduledTime: this.accountingDate,
      });
    }

    const { response, error } = apiResponse || {};

    if (error) {
      const errmsg = getAPIError(error) || __('Failed to Unwind');
      toaster(ERROR, errmsg);
      DeskingActions.setDeal(previousDealObj);
      return { deal: previousDealObj };
    }

    const warning = _get(response, 'warning');
    if (warning) {
      toaster(INFO, warning);
    }
    const newStatus = response.deal?.status || DEAL_STATUS.UNWIND;
    await DeskingActions.setDeal(response.deal);
    await DeskingActions.clearDeskingContract();
    await DeskingActions.setSelectedtab(LEFT_PANEL_DEAL_STATUS_MAP()[newStatus]);
    return response;
  };

  userConfirmationForUnwind = () => {
    this.closeOverlay();
    const { deal } = this.props;
    const dealStatus = DealReader.getDealStatus(deal);
    const isBookedDeal = dealStatus === DEAL_STATUS.BOOKED;
    ConfirmationModal.show({
      title: isBookedDeal ? PASSWORD_MODAL_TITLE.UNBOOK_DEAL : PASSWORD_MODAL_TITLE.UNWIND_DEAL,
      message: isBookedDeal ? MESSAGES.USER_CONFIRMATION_TO_UNBOOK : MESSAGES.USER_CONFIRMATION_TO_UNWIND,
      submitBtnText: __('Yes'),
      secondaryBtnText: __('No'),
      hideCloseIcon: true,
      onSubmitCb: async () => {
        ConfirmationModal.close();
        this.onUnwindSelection();
      },
    });
  };

  autoAdjustDealPosting = async () => {
    const { deal, DeskingActions } = this.props;
    ConfirmationModal.show({
      title: PASSWORD_MODAL_TITLE.AUTO_ADJUST_DEAL_POSTINGS,
      message: MESSAGES.AUTO_ADJUST_DEAL_POSTING,
      submitBtnText: __('Yes'),
      secondaryBtnText: __('No'),
      hideCloseIcon: true,
      onSubmitCb: async () => {
        ConfirmationModal.showLoader();
        const { rawResponse } = await DealsAPI.autoAdjustDealPosting(_get(deal, 'dealNumber'));
        const { status } = rawResponse;
        if (status === 200) {
          toaster('success', __('Auto Adjust Deal Posting is success'));
          await DeskingActions.fetchDeal(_get(deal, 'dealNumber'));
          ConfirmationModal.hideLoader();
          await GlobalWarning.showWarnings();
          ConfirmationModal.close();
        } else {
          toaster('error', __('Failed to Auto Adjust Deal Posting'));
          ConfirmationModal.hideLoader();
          ConfirmationModal.close();
        }
      },
    });
  };

  onUnwindSelection = async () => {
    if (this.unwindDealRef) {
      const { deal, salesSetupInfo } = this.props;
      const dealNumber = DealReader.getDealNumber(deal);

      const dealStatus = DealReader.getDealStatus(deal);
      const isBookedDeal = dealStatus === DEAL_STATUS.BOOKED;
      const { unLocked, accountingDate } = await this.unwindDealRef.current.show({
        title: isBookedDeal ? PASSWORD_MODAL_TITLE.UNBOOK_DEAL : PASSWORD_MODAL_TITLE.UNWIND_DEAL,
        dealNumber,
        showAccountingDateWhileUnwinding:
          SalesSetupReader.getShowAccountingDateWhileUnwinding(salesSetupInfo) && !isBookedDeal,
      });
      this.accountingDate = accountingDate;
      if (unLocked) {
        const penInfo = DealReader.getPenInfo(deal);
        const hasActiveContracts = checkForActiveContracts(penInfo);
        if (hasActiveContracts) {
          ConfirmationModal.show({
            title: MESSAGES.VOID_CONTRACTS_WHILE_UNWINDING,
            message: MESSAGES.DESCRIPTION_VOID_CONTRACTS_WHILE_UNWINDING,
            submitBtnText: __('Yes'),
            secondaryBtnText: __('No'),
            hideCloseIcon: true,
            onSubmitCb: async () => {
              this.unwindByVoidingPenContracts();
            },
            onCancelCb: async () => {
              this.unwindWithoutVoidingPencontracts();
            },
          });
        } else {
          const toastId = toaster(INFO, isBookedDeal ? MESSAGES.WAIT_TO_UNBOOK : MESSAGES.WAIT_TO_UNWIND);
          await this.onChoosingToUnwind(false);
          dismissToast(toastId);
        }
      }
    }
    return null;
  };

  onDuplicateDeal = () => {
    this.closeOverlay();
    const { onDuplicateDeal } = this.props;
    onDuplicateDeal();
  };

  onSeperateOptForLoanAndLease = async () => {
    const { DeskingActions, deal, saveDealData, getMarketScanData } = this.props;
    const isMultipleDownPaymentsEnabled = DealReader.isMultipleDownPaymentsEnabled(deal);
    await DeskingActions.setMultipleDownPaymentStatus(!isMultipleDownPaymentsEnabled);
    saveDealData();
    getMarketScanData();
  };

  onUnlockDealSelection = async () => {
    this.closeOverlay();

    if (this.unlockDealRef) {
      const { deal, salesSetupInfo } = this.props;
      const dealStatus = DealReader.getDealStatus(deal);
      const canDOMSCall =
        dealStatus === DEAL_STATUS.BOOKED && SalesSetupReader.canDoDefaultPaymentCalculation(salesSetupInfo);
      const dealNumber = DealReader.getDealNumber(deal);

      const { unlocked, recalculatePayments } = await this.unlockDealRef.current.show({
        title: PASSWORD_MODAL_TITLE.UNLOCK_DEAL,
        canDOMSCall,
        dealNumber,
      });
      if (unlocked) {
        const { unLockDealCallBack } = this.props;
        unLockDealCallBack({ recalculatePayments });
      }
    }
  };

  getHeader = heading => <Content className={styles.actionCategory}>{heading}</Content>;

  toggleCashieringModal = (isRefundMode = false, invoiceNumber) => {
    this.setState(({ visibleCashieringModal }) => ({
      isRefundMode,
      invoiceNumber,
      visibleCashieringModal: !visibleCashieringModal,
    }));
  };

  getRevokeDealPopupMessage = (currDeposit, deal) => {
    if (isInchcape()) {
      if (DealReader.postInvoiceDp(deal)) {
        if (_isEmpty(currDeposit) || _toNumber(currDeposit) === 0) {
          return __('Reservation would be revoked and the vehicle will be moved to Quote.');
        }
        return __(
          `Deposit credit note for the ${formatCurrencyWithMoneyFormat(
            currDeposit
          )} amount will be created and the vehicle will be moved to Quote.`
        );
      }
      return DEPOSIT_MESSAGES.REVOKE_VEHICLE_RESERVATION;
    }
    if (isRRG()) {
      return DEPOSIT_MESSAGES.REVOKE_DEAL_AND_REFUND_CONFIRMATION;
    }
    return DEPOSIT_MESSAGES.REVOKE_DEAL_CONFIRMATION;
  };

  getRevokeDealSubmitButtonMessage = deal => {
    if (isInchcape()) {
      if (DealReader.postInvoiceDp(deal)) {
        return DEPOSIT_LABELS.REVOKE_DEAL;
      }
      return DEPOSIT_LABELS.REVOKE_VEHICLE;
    }
    if (isRRG()) {
      return __('Yes');
    }
    return DEPOSIT_LABELS.REVOKE_DEAL;
  };

  getRevokeMessageWithoutPayment = (nonCancelledAppointments, message) => {
    let finalMessage = message;
    if (isInchcape()) {
      if (!_isEmpty(nonCancelledAppointments)) {
        finalMessage = `${DEPOSIT_MESSAGES.REVOKE_CANCEL_APPOIINTMENTS} ${finalMessage}`;
      }
    }
    return finalMessage;
  };

  handleDepositV2 = (
    nonCancelledAppointments,
    message,
    currDeposit,
    isZeroReservationAmount,
    depositTransactionIds
  ) => {
    const { DeskingActions, deal, blockScreen } = this.props;
    return ConfirmationModal.show({
      title: DEPOSIT_LABELS.REVOKE_VEHICLE_RESERVATION,
      message: this.getRevokeMessageWithoutPayment(nonCancelledAppointments, message),
      submitBtnText: __('Revoke & Refund'),
      okButtonProps: { disabled: !hasCashieringRefund() },
      secondaryBtnText: this.getRevokeDealSubmitButtonMessage(deal),
      hideSubmit: !isZeroReservationAmount && _isEmpty(depositTransactionIds),
      hideCancel: isZeroReservationAmount,
      onSecondarySubmit: async () => {
        await DeskingActions.revokeDeal(deal, undefined, true, undefined, undefined, false);
        ConfirmationModal.close();
      },
      renderFooterLeftSection: () => (
        <div className="d-inline-flex">
          <Button view={Button.VIEW.SECONDARY} onClick={ConfirmationModal.close}>
            {__('Cancel')}
          </Button>
        </div>
      ),
      onSubmitCb: async () => {
        await DeskingActions.revokeDeal(deal, undefined, true, undefined, blockScreen, true);
        ConfirmationModal.close();
      },
      onCancelCb: () => {
        ConfirmationModal.close();
      },
    });
  };

  showPopupToRevoke = async (depositTransactionIds, deposit, appointmentsData, currDeposit) => {
    const { DeskingActions, deal } = this.props;
    let message = this.getRevokeDealPopupMessage(currDeposit, deal);

    const isZeroReservationAmount = _isNumber(deposit) && _toNumber(currDeposit) === 0;

    const nonCancelledAppointments =
      _find(appointmentsData, item => item?.appointmentStatus !== 'CANCELLED') || EMPTY_OBJECT;

    if (!isDealRevoked(deal) && !_isEmpty(depositTransactionIds) && !isRRG()) {
      message = `${message}\n${__(DEPOSIT_MESSAGES.REFUND_IF_NEEDED, {
        deposit: formatCurrencyWithMoneyFormat(deposit),
      })}`;
      if (isInchcape() && !_isEmpty(nonCancelledAppointments)) {
        message = `${DEPOSIT_MESSAGES.REVOKE_CANCEL_APPOIINTMENTS} ${message}`;
      }
    }

    if (isInchcape() && DealReader.postInvoiceDp(deal)) {
      await this.handleDepositV2(
        nonCancelledAppointments,
        message,
        currDeposit,
        isZeroReservationAmount,
        depositTransactionIds
      );
    } else {
      const isInchcapeEnabled = isInchcape();
      await ConfirmationModal.getConfirmation({
        title: isInchcapeEnabled ? DEPOSIT_LABELS.REVOKE_VEHICLE_RESERVATION : DEPOSIT_LABELS.REVOKE_DEAL_CONFIRMATION,
        message: this.getRevokeMessageWithoutPayment(nonCancelledAppointments, message),
        submitBtnText: this.getRevokeDealSubmitButtonMessage(currDeposit),
        secondaryBtnText: __('Cancel'),
      }).then(async shouldRevoke => {
        if (shouldRevoke) {
          if (isInchcapeOrRRG()) {
            this.toggleCashieringModal(true);
          } else {
            await DeskingActions.revokeDeal(deal);
          }
        }
      });
    }
    GlobalWarning.updateBEWarnings([BACKEND_GLOBAL_WARNINGS_ACTIONS.VEHICLE_RESERVED_IN_VI_WARNING]);
  };

  mandatoryFieldsSubmitCb = async deposit => {
    const { deal, DeskingActions, onConfirmDeal } = this.props;
    const payload = {
      dealNumber: DealReader.getDealNumber(deal),
      depositAmount: getNumber(deposit),
      confirmDeal: true,
      overrideMandatoryFieldCheck: true,
    };
    await DeskingActions.confirmDeal(payload, onConfirmDeal);
  };

  // showAuditLogs = () => {
  //   const { deal } = this.props;
  //   const selectedDealId = _get(deal, 'id');
  //   showAuditLogDrawer(selectedDealId, AUDIT_LOG_ASSET_TYPES.DEAL, {
  //     isParentAsset: false,
  //     additionalPathsAndResourcesToBeResolved: PATH_AND_RESOURCES,
  //     useCustomAuditLogs: true,
  //     getCustomAuditLogs: CommonAPI.getDealAuditLogsByAssetType,
  //   });
  // };

  getButtonText = () => {
    const { isRefundMode } = this.props;
    if (isInchcape()) {
      return isRefundMode ? DEPOSIT_LABELS.REVOKE_VEHICLE : DEPOSIT_LABELS.RESERVE_VEHICLE;
    }
    return DEPOSIT_LABELS.CONFIRM_DEAL;
  };

  showPopupToConfirm = deposit => {
    const { deal, DeskingActions, onConfirmDeal, showMandatoryForm } = this.props;

    ConfirmationModal.getConfirmation({
      title: this.getButtonText(),
      message: __(DEPOSIT_MESSAGES.DEPOSIT_ALREADY_COLLECTED, { deposit }),
      submitBtnText: this.getButtonText(),
    }).then(async shouldConfirm => {
      if (shouldConfirm) {
        const payload = {
          dealNumber: DealReader.getDealNumber(deal),
          depositAmount: getNumber(deposit),
          confirmDeal: true,
        };
        await DeskingActions.confirmDeal(payload, onConfirmDeal);
        showMandatoryForm(() => {
          this.mandatoryFieldsSubmitCb(deposit);
        });
      }
    });
  };

  sendDealToPartnerHandler = async () => {
    const { deal } = this.props;
    const dealNumber = DealReader.getDealNumber(deal);
    const payload = {
      dealNumber,
    };
    const { rawResponse } = await DealsAPI.sendDealToPartnerFunction(dealNumber, payload);

    if (rawResponse?.status === 200) {
      toaster('success', __('Deal Data sent to Partner'));
    } else {
      toaster('error', __('Failed to send Deal Data to Partner'));
    }
    this.closeOverlay();
  };

  handleConfirmDealCl = (deal, deposit) => () => {
    if (isDealIntermittent(deal)) this.showPopupToConfirm(deposit);
    else this.toggleCashieringModal();
  };

  handleDeposit = async confirmDealFlow => {
    let shouldHandleConfirmDeal = false;
    let appointmentsData = EMPTY_ARRAY;
    const {
      deal,
      onSubmitDeal,
      showVehicleConfirmationModal,
      checkIsMultipleReservationAllowed,
      DeskingActions,
      onConfirmDeal,
      blockScreen,
    } = this.props;
    const { depositTransactionIds = EMPTY_ARRAY, deposit, currDeposit } = DealReader.getDepositDetails(deal);
    const dealNumber = DealReader.getDealNumber(deal);
    if (isInchcape() && confirmDealFlow && DealReader.postInvoiceDp(deal)) {
      shouldHandleConfirmDeal = await checkIsMultipleReservationAllowed({
        dealStatus: DEAL_STATUS.CONFIRMED,
        dealNumber,
      });
      if (shouldHandleConfirmDeal) {
        ConfirmationModal.show({
          title: __('Reserve Vehicle'),
          message:
            _isEmpty(currDeposit) || _toNumber(currDeposit) === 0
              ? __('Vehicle will be marked as reserved without reservation fee.')
              : __(
                  `Deposit invoice will be generated for the ${formatCurrencyWithMoneyFormat(
                    currDeposit
                  )} amount and the vehicle will be marked as reserved.`
                ),
          submitBtnText: __('Proceed'),
          secondaryBtnText: __('Cancel'),
          hideCloseIcon: true,
          onSubmitCb: async () => {
            ConfirmationModal.close();
            const payload = {
              dealNumber: DealReader.getDealNumber(deal),
              depositAmount: getNumber(currDeposit),
              confirmDeal: true,
              collectPayment: false,
            };
            await DeskingActions.confirmDeal(payload, onConfirmDeal, blockScreen);
          },
        });
      }
      return;
    }
    if (!isDealConfirmed(deal)) {
      const pauseFlow = await showVehicleConfirmationModal(deal);
      if (pauseFlow) return;
    }
    const handleConfirmDeal = this.handleConfirmDealCl(deal, deposit);
    if (confirmDealFlow) {
      shouldHandleConfirmDeal = await checkIsMultipleReservationAllowed({
        dealStatus: DEAL_STATUS.CONFIRMED,
        dealNumber,
      });
    }
    if (isDealConfirmed(deal)) {
      if (isInchcape()) {
        appointmentsData = await fetchAppointments(deal, undefined, undefined, 'NO_LOOKUP');
      }
      this.showPopupToRevoke(depositTransactionIds, deposit, appointmentsData, currDeposit);
    } else if (DealReader.getDealStatus(deal) !== DEAL_STATUS.QUOTE && shouldHandleConfirmDeal) handleConfirmDeal();
    else if (shouldHandleConfirmDeal) onSubmitDeal(handleConfirmDeal);

    this.closeOverlay();
  };

  onToggleRecap = () => {
    const { toggleRecapView } = this.props;
    toggleRecapView();
    this.closeOverlay();
  };

  onOrderFormRegeneration = async () => {
    const { deal, selectedLeftPanel } = this.props;

    const dealNumber = DealReader.getDealNumber(deal);
    const dealStatusWeight = DEAL_STATUS_WEIGHTS[deal.status];
    if (
      isInchcape() &&
      selectedLeftPanel === LEFT_PANEL_ITEMS.DESKING &&
      dealStatusWeight >= DEAL_STATUS_WEIGHTS[DEAL_STATUS.ON_ORDER]
    ) {
      const response = await DealsAPI.orderFormRegenrationRequired(dealNumber);
      if (_isBoolean(response)) {
        this.setState({
          orderFormRegenrationRequired: response,
        });
      }
    }
  };

  pauseDealUpdate = async () => {
    const { deal, DeskingActions } = this.props;
    const currentDealNumber = DealReader.getDealNumber(deal);
    const isDealPaused = DealReader.manualPaused(deal);
    await DeskingActions.updateDeal(currentDealNumber, {
      ...deal,
      paused: !isDealPaused,
    });
    GlobalWarning.showWarnings();
  };

  getDisabledFlag = isDealViewOnly => {
    const { deal } = this.props;
    const isDealPaused = DealReader.manualPaused(deal);
    if (isDealPaused) {
      return true;
    }
    return isInchcape() ? !isDeskingKebabActionsEnabled() || isDealViewOnly : isDealViewOnly;
  };

  getOverlayItems = () => {
    const {
      isRecapViewEnabled,
      selectedLeftPanel,
      deal,
      isDealAcquired,
      isDealViewOnly,
      salesSetupInfo,
      deskingpaymentDetails,
      isDealLocked,
      invoices,
      recapApprovalDetails,
    } = this.props;
    const { orderFormRegenrationRequired } = this.state;
    const setupIntegrationKeys = SalesSetupReader.selectIntegrationKeys(salesSetupInfo);

    const isMutipleDownPaymentsFeatureEnabled = isIntegrationEnabled(
      setupIntegrationKeys,
      INTEGRATION_KEYS.MULTIPLE_DOWNPAYMENTS
    );
    const isAuditEnabled = isIntegrationEnabled(setupIntegrationKeys, INTEGRATION_KEYS.AUDIT);
    const fuseDeal = isFuseDeal(deal);

    const isDeskingTabSelected = selectedLeftPanel === LEFT_PANEL_ITEMS.DESKING;
    const isRecapTabSelected = selectedLeftPanel === LEFT_PANEL_ITEMS.RECAP;
    const isBookedorClosedOrSoldDeal = [DEAL_STATUS.BOOKED, DEAL_STATUS.CLOSED_OR_SOLD].includes(deal.status);
    const isBookedDeal = [DEAL_STATUS.BOOKED].includes(deal.status);
    const isDealUnwind = [DEAL_STATUS.UNWIND].includes(deal.status);
    const preOrderDeal = DealReader.isPreOrderDeal(deal);
    const preClosedDeal = DealReader.isPreClosedDeal(deal);
    const leaseColumnIds = getLeaseorOnePayColumnIds(deskingpaymentDetails);
    const isMultipleDownPaymentsEnabled = DealReader.isMultipleDownPaymentsEnabled(deal);
    const isDepositDisabled = SalesSetupReader.getDepositDisabledVehicleStatuses(salesSetupInfo).includes(
      DealReader.getPrimaryVehicleStatus(deal)
    );
    const isAutoPostingDone = DealReader.isAutoPostingDone(deal);
    const isConfirmEnabled = SalesSetupReader.isDealStatusEnabled(salesSetupInfo, DEAL_STATUS.CONFIRMED);
    const isSendDealToPartnerEnabled = SalesSetupReader.isSendDealToPartnerEnabled(salesSetupInfo);
    const isConfirmDealPermissionEnabled = isConfirmDealEnabled();
    const isRRGProgram = isRRG();
    const isInchcapeProgram = isInchcape();
    const dealType = deal?.type;
    const isOnOrderDeal = [DEAL_STATUS.ON_ORDER].includes(deal.status);
    const isReservedDeal = [DEAL_STATUS.CONFIRMED].includes(deal.status);
    const isDealTypePurchaseOnly = [DEAL_TYPES.ONLY_TRADES].includes(dealType);
    const isCashRealisationDeal = DEAL_TYPES.CASH_REALISATION === dealType;
    const isDealStatusInvoiced = [DEAL_STATUS.INVOICED].includes(deal.status);
    const dealStatusReservedORQuote = [DEAL_STATUS.QUOTE, DEAL_STATUS.CONFIRMED].includes(deal.status);
    const isVehicleStatusReservedORStockedIn = [VEHICLE_STATUS.RESERVED, VEHICLE_STATUS.STOCKED_IN].includes(
      DealReader.getPrimaryVehicleStatus(deal)
    );
    const isDealTypeDealerTrade = [DEAL_TYPES.DEALER_TRADE].includes(dealType);
    const isDealTypeCashRealisation = [DEAL_TYPES.CASH_REALISATION].includes(dealType);
    const isDealTypeTrade = [DEAL_TYPES.TRADE].includes(dealType);
    const isDealTypeOnlyFni = [DEAL_TYPES.FNI].includes(dealType);
    const dealStatusWeight = DEAL_STATUS_WEIGHTS[deal.status];
    const userInfo = TEnvReader.userInfo();
    const userPersona = userReader.persona(userInfo);
    const isSalesManager = userPersona === PERSONAS.SALES_MANAGER;

    const source = _get(deal, 'source');
    const program = _get(deal, 'program');
    const sourceType = _get(deal, 'sourceType');
    const isAECProgram = DealerPropertyHelper.isAECProgram();
    const isHondaZdxProgram =
      _isEqual(source, DEAL_SOURCE.INTERNET) &&
      _isEqual(sourceType, SOURCE_TYPE.OMS) &&
      _isEqual(program, PROGRAM_CODE.ACURA_NEW) &&
      isAECProgram;

    // disable revoke confirmation if both deposit and customer cash invoice generated
    const isBothCustCashAndDepositGenerated = checkIfCustCashAndDepositGen(invoices);

    const disableConditionForActionsInchcape = isInchcape() ? !isDeskingKebabActionsEnabled() : false;
    const disableConditionForSummaryInchcape = isInchcape() ? !isDeskingKebabSummaryEnabled() : false;

    const recapWarnings = getRecapApprovalWarnings(recapApprovalDetails);
    const isRecapWorkflowEnabled = recapApprovalDataReader.workflowEnabled(recapApprovalDetails);
    const isRecapDone = recapApprovalDataReader.isRecapDone(recapWarnings);

    const showEnableRecapView =
      isRecapTabSelected && isDealAcquired && hasFniEdit() && hasCostAndGrossView() && !preOrderDeal;
    const isDealPaused = DealReader.manualPaused(deal);
    const isPauseDealEnabled = shouldShowPauseDealAction(salesSetupInfo, deal);

    return getFilteredOverlaySections([
      {
        section: __('Actions'),
        visible: true,
        items: [
          {
            name: isInchcapeProgram ? __('Reserve Vehicle') : __('Confirm Deal'),
            visible: isInchcapeProgram
              ? dealStatusWeight < DEAL_STATUS_WEIGHTS[DEAL_STATUS.CONFIRMED] &&
                !isCashRealisationDeal &&
                !isDealTypePurchaseOnly &&
                !isDealTypeDealerTrade &&
                !isDealTypeOnlyFni &&
                isConfirmEnabled
              : !isDealConfirmed(deal) &&
                !isDepositDisabled &&
                !isBookedorClosedOrSoldDeal &&
                !preOrderDeal &&
                !isDealUnwind &&
                isConfirmEnabled &&
                !preClosedDeal &&
                isConfirmDealPermissionEnabled &&
                !isHondaZdxProgram,
            disabled: this.getDisabledFlag(isDealViewOnly),
            onClick: isDealLocked ? _noop : () => this.handleDeposit(true),
            id: 'confirmDeal',
          },
          {
            name: __('Duplicate'),
            onClick: this.onDuplicateDeal,
            visible: hasDealCreate() && shouldDuplicateBeAllowed(deal),
          },
          {
            name: isInchcapeProgram ? __('Revoke Vehicle Reservation') : __('Revoke Deal Confirmation'),
            visible: isInchcapeProgram
              ? isReservedDeal && !isDealTypePurchaseOnly && !isDealTypeDealerTrade
              : isDealConfirmed(deal) &&
                !isBookedorClosedOrSoldDeal &&
                !preOrderDeal &&
                !isDealUnwind &&
                !preClosedDeal,
            onClick: isDealLocked ? _noop : () => this.handleDeposit(false),
            id: 'revokeDeal',
            disabled: isInchcape() ? !isDeskingKebabActionsEnabled() || isBothCustCashAndDepositGenerated : false,
          },
          {
            name: isMultipleDownPaymentsEnabled
              ? __('Same opt for loan and lease')
              : __('Separate opt for loan and lease'),
            visible:
              isMutipleDownPaymentsFeatureEnabled &&
              !isDealViewOnly &&
              isCategory1Deal(deal) &&
              !preOrderDeal &&
              !isInchcapeOrRRG(),
            onClick: this.onSeperateOptForLoanAndLease,
          },
          {
            name: isRecapViewEnabled ? __('Disable Recap View') : __('Enable Recap View'),
            visible:
              isRecapWorkflowEnabled && isRecapViewEnabled ? !isRecapDone && showEnableRecapView : showEnableRecapView,
            onClick: this.onToggleRecap,
            disabled: disableConditionForActionsInchcape,
          },
          {
            name: PASSWORD_MODAL_TITLE.UNLOCK_DEAL,
            visible:
              (isDeskingTabSelected || isRecapTabSelected) &&
              isDealAcquired &&
              !preOrderDeal &&
              canUnlock(deal.status, deal.locked),
            onClick: this.onUnlockDealSelection,
          },
          {
            name: isBookedDeal ? PASSWORD_MODAL_TITLE.UNBOOK_DEAL : PASSWORD_MODAL_TITLE.UNWIND_DEAL,
            visible:
              isDeskingTabSelected &&
              isDealAcquired &&
              !preOrderDeal &&
              !fuseDeal &&
              !(isInchcapeProgram && !isBookedDeal) &&
              canUnbook(deal.status, deal.locked),
            onClick: this.userConfirmationForUnwind,
          },
          {
            name: PASSWORD_MODAL_TITLE.AUTO_ADJUST_DEAL_POSTINGS,
            visible: isAutoPostingDone === false && DealerPropertyHelper.isAutoAdjustDealPostingsEnabled(),
            onClick: isDealLocked ? _noop : this.autoAdjustDealPosting,
          },
          {
            name: __('Send Deal to Partner'),
            visible: isSendDealToPartnerEnabled,
            onClick: isDealLocked ? _noop : this.sendDealToPartnerHandler,
          },
          {
            name: __('Create Order'),
            visible: createOrderFormVisibility(
              isInchcapeProgram,
              dealStatusReservedORQuote,
              // isVehicleStatusReservedORStockedIn,
              // isDealTypePurchaseOnly,
              isDealTypeDealerTrade,
              isDealTypeCashRealisation,
              isDealTypeTrade,
              isDealTypeOnlyFni
            ),
            id: ORDER_FORM_KEYS.CREATE_ORDER,
            disabled: disableConditionForActionsInchcape,
          },
          {
            name: isDealTypePurchaseOnly ? __('Regenerate Purchase Order Form') : __('Regenerate Order Form'),
            visible: regenerateOrderFormVisibility(isInchcapeProgram, dealStatusWeight, orderFormRegenrationRequired), // add that new key from deal object
            id: ORDER_FORM_KEYS.REGENERATE_ORDER,
            disabled: disableConditionForActionsInchcape,
          },
          {
            name: __('Cancel Order'),
            visible: cancelOrderFormVisibility(isInchcapeProgram, isOnOrderDeal),
            id: ORDER_FORM_KEYS.CANCEL_ORDER,
            disabled: disableConditionForActionsInchcape,
          },
          {
            name: isDealPaused ? __('Resume Deal update') : __(`Pause Deal Update`),
            visible: isPauseDealEnabled,
            id: 'pauseDealUpdate',
            shouldShowInfoIcon: !isDealPaused,
            onClick: this.pauseDealUpdate,
          },
        ],
        disabled: disableConditionForActionsInchcape,
      },
      {
        section: __('Summaries'),
        visible: true,
        items: [
          {
            name: __('Audit Logs'),
            visible: isAuditEnabled && hasCostAndGrossView(),
            onClick: this.openDealAuditLogs,
            id: 'auditLog',
            disabled: disableConditionForSummaryInchcape,
          },
          {
            name: __('Activity Logs'),
            visible: isInchcape(),
            onClick: this.openActivityLogs,
            id: 'activityLogs',
            disabled: disableConditionForSummaryInchcape,
          },
          {
            name: __('Gross Summary'),
            visible:
              hasGrossSummaryView() &&
              hasCostAndGrossView() &&
              (isDeskingTabSelected || isRecapTabSelected) &&
              isDealAcquired &&
              !isRRGProgram,
            onClick: this.onClickGrossSummary,
            disabled: disableConditionForSummaryInchcape,
          },
        ],
        disabled: disableConditionForSummaryInchcape,
      },
      {
        section: __('Settings'),
        visible: !isRRGProgram && !isInchcapeProgram,
        items: [
          {
            name: __('Working Cash settings'),
            visible:
              hasWorkingCashEdit() &&
              hasLeaseOptionsEdit() &&
              isDeskingTabSelected &&
              isDealAcquired &&
              !preOrderDeal &&
              isCategory1Deal(deal) &&
              !_isEmpty(leaseColumnIds),
            onClick: this.openWorkingCashModal,
          },
        ],
      },
    ]);
  };

  toolTipContent = id => {
    const { invoices, deal } = this.props;
    const isDealPaused = DealReader.manualPaused(deal);
    const isBothCustCashAndDepositGenerated = checkIfCustCashAndDepositGen(invoices);
    if (isInchcape() && DealReader.postInvoiceDp(deal) && isBothCustCashAndDepositGenerated && id === 'revokeDeal') {
      return (
        <div className="padding1">
          <div>{__('Please cancel Customer Cash invoice before revoking reservation')}</div>
        </div>
      );
    }
    if (id === 'pauseDealUpdate' && !isDealPaused) {
      return (
        <div className={classnames('padding1', styles.maxWidth)}>
          <div>
            {__(
              'Once clicked, any changes made on the deal will not be shown to the customer. You can change this later at any time.'
            )}
          </div>
        </div>
      );
    }

    return EMPTY_STRING;
  };

  renderOverlay = () => {
    const { overlayVisible } = this.state;
    const { deal, showMandatoryForm, blockScreen, DeskingActions, isDealViewOnly, salesSetupInfo } = this.props;
    if (!overlayVisible) return null;

    return (
      <OutsideClickDetector onOutsideClick={this.closeOverlay}>
        <div className={classnames('collapse-actions-side-menu', styles.accordionActionsSideMenu)}>
          {_map(this.getOverlayItems(), ({ section, items, visible: sectionVisible, disabled: sectionDisabled }) => (
            <PropertyControlledComponent controllerProperty={sectionVisible}>
              <div className={styles.section}>{section}</div>
              <div disabled={sectionDisabled}>
                {_map(items, ({ name, visible, onClick, id, disabled, shouldShowInfoIcon }, idx) => (
                  <PropertyControlledComponent controllerProperty={visible} key={name}>
                    {id === ORDER_FORM_KEYS.CREATE_ORDER ||
                    id === ORDER_FORM_KEYS.REGENERATE_ORDER ||
                    id === ORDER_FORM_KEYS.CANCEL_ORDER ? (
                      <OrderFormGeneration
                        name={name}
                        view={Button.VIEW.TERTIARY}
                        className={styles.contentItem}
                        itemClassname={styles.itemName}
                        id={id || idx}
                        deal={deal}
                        showMandatoryForm={showMandatoryForm}
                        blockScreen={blockScreen}
                        closeOverlay={this.closeOverlay}
                        DeskingActions={DeskingActions}
                        disabled={isDealViewOnly || disabled}
                        salesSetupInfo={salesSetupInfo}
                      />
                    ) : (
                      <Popover placement="left" trigger="hover" content={this.toolTipContent(id)}>
                        <Button
                          onClick={onClick}
                          view={Button.VIEW.TERTIARY}
                          className={disabled ? classnames(styles.disabled, styles.contentItem) : styles.contentItem}
                          disabled={disabled}>
                          <div className={styles.itemName} data-testid={id || idx}>
                            {name} {shouldShowInfoIcon && <FontIcon className={styles.inlineStyle}>icon-info</FontIcon>}
                          </div>
                        </Button>
                      </Popover>
                    )}
                  </PropertyControlledComponent>
                ))}
              </div>
            </PropertyControlledComponent>
          ))}
        </div>
      </OutsideClickDetector>
    );
  };

  openWorkingCashModal = () => {
    this.closeOverlay();
    this.setState({
      workingCashModalVisible: true,
    });
  };

  close = () => {
    this.setState({
      workingCashModalVisible: false,
    });
  };

  leaseWorkingCashTypeChange = ({ payload }) => {
    const { id: key, value } = payload;

    this.setState(prevState =>
      produce(prevState, draft => {
        const workingCashType = _get(draft, ['workingCashConfigs', LEASE_WORKING_CASH.CONFIG_TYPE]) || EMPTY_ARRAY;
        const matchedItem = findArrayItem(workingCashType, LEASE_WORKING_CASH.IDENTIFIER_KEY, key);
        if (matchedItem) {
          matchedItem.enabled = value;
        }
      })
    );
  };

  payOfftypeChange = ({ payload }) => {
    const { id: key, value } = payload;

    this.setState(prevState =>
      produce(prevState, draft => {
        const workingCashType = _get(draft, ['workingCashConfigs', NEGATIVE_TRADE_PAYOFFS.CONFIG_TYPE]) || EMPTY_ARRAY;
        const matchedItem = findArrayItem(workingCashType, NEGATIVE_TRADE_PAYOFFS.IDENTIFIER_KEY, key);
        if (matchedItem) {
          matchedItem.enabled = value;
        }
      })
    );
  };

  renderLeaseWorkingCash = item => {
    const { leaseWorkingCashType, enabled } = item;
    return (
      <DescriptionSwitch
        description={LEASE_WORKING_CASH_DISPLAY_NAMES[leaseWorkingCashType]}
        onAction={this.leaseWorkingCashTypeChange}
        identifier={leaseWorkingCashType}
        value={enabled}
        key={leaseWorkingCashType}
      />
    );
  };

  renderNegativeTradePayOffs = item => {
    const { payOfftype, enabled } = item;
    return (
      <DescriptionSwitch
        description={NEGATIVE_TRADE_PAYOFFS_DISPLAY_NAMES[payOfftype]}
        onAction={this.payOfftypeChange}
        identifier={payOfftype}
        value={enabled}
        key={payOfftype}
      />
    );
  };

  onSubmit = async workingCashConfigs => {
    const {
      DeskingActions,
      saveDealData,
      getMarketScanData,
      deskingpaymentDetails,
      updateDeskingForColumnIds,
      syncPaymentDetails,
    } = this.props;

    await DeskingActions.setWorkingCashConfiginDeal(workingCashConfigs);
    await saveDealData();
    const columnIds = getAllCColumnsIDs(deskingpaymentDetails);
    if (DealerPropertyHelper.isCreateDealWithNewAPIEnabled()) {
      this.close();
      await updateDeskingForColumnIds(columnIds);
      return;
    }
    if (CalcEngineProperties.updateCalculationByBackend()) {
      this.close();
      await syncPaymentDetails();
      return;
    }

    getMarketScanData({ columnIds });
    this.close();
  };

  render() {
    const {
      workingCashModalVisible,
      overlayVisible,
      dealAuditLogsVisible,
      toggleRecapView,
      activityLogVisibility,
      activityLogs,
      lookupData,
      visibleCashieringModal,
      isRefundMode,
      invoiceNumber,
    } = this.state;
    const {
      deal,
      dealTypesFromSetup,
      DeskingActions,
      onConfirmDeal,
      getLabelFromPaymentOptions,
      salesSetupInfo,
      syncPaymentDetails,
    } = this.props;
    const dealSubStatusList = SalesSetupReader.getDealSubStatusList(salesSetupInfo);

    return (
      <div className={styles.sideMenu}>
        <Popover content={this.renderOverlay()} placement="bottomRight" trigger="click" visible={overlayVisible}>
          <Button
            view="icon"
            className="iconRotate"
            icon="icon-overflow"
            onClick={this.openOverlay}
            data-testid="optionsButton"
          />
          {/* <FontIcon className="iconRotate"></FontIcon> */}
        </Popover>

        {workingCashModalVisible && (
          <LeaseConfigurations
            configureLease={workingCashModalVisible}
            onClose={this.close}
            getLabelFromPaymentOptions={getLabelFromPaymentOptions}
          />
        )}
        <PropertyControlledComponent controllerProperty={dealAuditLogsVisible}>
          <DealAuditLogs
            onClose={this.closeDealAuditLogs}
            dealId={_get(deal, 'id')}
            dealTypesFromSetup={dealTypesFromSetup}
            dealSubStatusList={dealSubStatusList}
            getLabelFromPaymentOptions={getLabelFromPaymentOptions}
          />
        </PropertyControlledComponent>

        <ActivityLogs
          hideSearch
          auditLogs={activityLogs}
          lookupData={lookupData}
          getAuditLogs={this.getLogsAndLookupData}
          visible={activityLogVisibility}
          headingLabel={__('Activity Logs')}
          onClose={this.closeActivityLogs}
          statusesConfig={{ ...ACTIVITY_LOG_STATUS_CONFIGS, ...CREDIT_APPLICATION_STATUS_CONFIGS }}
          showOldValuesToggle={false}
          showOldValues
        />

        <UnwindDealModal ref={this.unwindDealRef} />
        <UnlockDealModal ref={this.unlockDealRef} />

        <PropertyControlledComponent controllerProperty={visibleCashieringModal}>
          <CashieringModal
            toggleCashieringModal={this.toggleCashieringModal}
            isRefundMode={isRefundMode}
            toggleRecapView={toggleRecapView}
            deal={deal}
            DeskingActions={DeskingActions}
            onConfirmDeal={onConfirmDeal}
            syncPaymentDetails={syncPaymentDetails}
            invoiceNumber={invoiceNumber}
          />
        </PropertyControlledComponent>

        <CalculationUpdateNotification
          onNotificationClick={this.openDealAuditLogs}
          hideNotification={dealAuditLogsVisible}
        />
      </div>
    );
  }
}

SideMenu.defaultProps = {
  DeskingActions: EMPTY_OBJECT,
  saveDealData: _noop,
  deskingpaymentDetails: EMPTY_ARRAY,
  dealTypesFromSetup: EMPTY_ARRAY,
  deal: EMPTY_OBJECT,
  isRecapViewEnabled: false,
  toggleRecapView: _noop,
  selectedLeftPanel: null,
  unLockDealCallBack: _noop,
  isDealAcquired: false,
  isDealViewOnly: false,
  getMarketScanData: _noop,
  onSubmitDeal: _noop,
  onConfirmDeal: _noop,
  showVehicleConfirmationModal: _noop,
  checkIsMultipleReservationAllowed: _noop,
};

SideMenu.propTypes = {
  DeskingActions: PropTypes.object,
  saveDealData: PropTypes.func,
  deskingpaymentDetails: PropTypes.array,
  dealTypesFromSetup: PropTypes.array,
  deal: PropTypes.object,
  isRecapViewEnabled: PropTypes.bool,
  toggleRecapView: PropTypes.func,
  selectedLeftPanel: PropTypes.string,
  unLockDealCallBack: PropTypes.func,
  isDealAcquired: PropTypes.bool,
  isDealViewOnly: PropTypes.bool,
  salesSetupInfo: PropTypes.object.isRequired,
  getMarketScanData: PropTypes.func,
  onDuplicateDeal: PropTypes.func.isRequired,
  updateDeskingForColumnIds: PropTypes.func.isRequired,
  onSubmitDeal: PropTypes.func,
  onConfirmDeal: PropTypes.func,
  showVehicleConfirmationModal: PropTypes.func,
  checkIsMultipleReservationAllowed: PropTypes.func,
};

export default compose(
  withDeskingContext,
  WithMandatoryReviewHOC,
  VehicleConfirmationModalHOC,
  AllowMultipleReservationsOnVehicleHOC
)(SideMenu);
