import _isNil from 'lodash/isNil';

import { NO_DATA } from '@tekion/tekion-base/app.constants';

import { formatCurrencyWithMoneyFormat } from 'utils';

const ALL_LABEL = __('All');

export const ALL_VALUE = 'all';

export const TABLE_MANAGER_PROPS = {
  showSearch: false,
  showFilter: true,
  showSubHeader: true,
};

export const SELECT_PROGRAM_COLUMNS = {
  LENDER_NAME: 'lenderName',
  CODE: 'code',
  TIER: 'tiers',
  PROGRAM_TYPE: 'special',
  PAYMENT: 'payment',
  SELL_RATE: 'sellRate',
  BUY_RATE: 'buyRate',
  MAX_MARKUP: 'maxMarkup',
  FRONT_END_PROFIT: 'frontEndProfit',
  BACK_END_PROFIT: 'backEndProfit',
  TOTAL_PROFIT: 'totalProfit',
  REBATES: 'rebates',
  RANK: 'rank',
  TERM: 'term',
  MAX_ADVANCE: 'maxAdvance',
  SECURITY_DEPOSIT: 'securityDeposit',
  PROGRAM_DESC: 'description',
  PROGRAM_ID: 'id',
  PROGRAM_NUMBER: 'programNumber',
  PROGRAM_NAME: 'programName',
  START_DATE: 'startDate',
  END_DATE: 'endDate',
  ALTERNATE_PROGRAM_TYPE: 'alternateProgrammeName',
};

export const FILTER_TYPES_TO_UPDATE_TABLE_DATA = [SELECT_PROGRAM_COLUMNS.LENDER_NAME];

export const FILTER_TYPES_TO_UPDATE_TABLE_DATA_GALAXY = [
  SELECT_PROGRAM_COLUMNS.LENDER_NAME,
  SELECT_PROGRAM_COLUMNS.TIER,
  SELECT_PROGRAM_COLUMNS.ALTERNATE_PROGRAM_TYPE,
];

const programTypeColumnFormatter = value => SPECIAL_STANDARD_VS_PROGRAM_TYPE[Number(value)];
const programTypeFormatterGalaxy = value => SPECIAL_STANDARD_VS_PROGRAM_TYPE_GALAXY[value];
const programTypeFormatterCanada = value => SPECIAL_STANDARD_VS_PROGRAM_TYPE_CANADA[value];
const currencyFormatter = value => (_isNil(value) ? NO_DATA : formatCurrencyWithMoneyFormat(value));

export const SEARCHABLE_TABLE_COLUMNS_GALAXY = [
  SELECT_PROGRAM_COLUMNS.CODE,
  SELECT_PROGRAM_COLUMNS.PROGRAM_NAME,
  SELECT_PROGRAM_COLUMNS.TIER,
  SELECT_PROGRAM_COLUMNS.PROGRAM_TYPE,
  SELECT_PROGRAM_COLUMNS.TERM,
];

export const SEARCHABLE_TABLE_COLUMNS_CANADA = [
  SELECT_PROGRAM_COLUMNS.CODE,
  SELECT_PROGRAM_COLUMNS.LENDER_NAME,
  SELECT_PROGRAM_COLUMNS.PROGRAM_NAME,
  SELECT_PROGRAM_COLUMNS.TIER,
  SELECT_PROGRAM_COLUMNS.PROGRAM_TYPE,
  SELECT_PROGRAM_COLUMNS.TERM,
];

export const SELECT_PROGRAM_TABLE_CONFIG_GALAXY = [
  {
    accessor: SELECT_PROGRAM_COLUMNS.PROGRAM_NAME,
    Header: __('Program Name'),
    minWidth: 300,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.CODE,
    Header: __('Program #'),
    minWidth: 180,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.ALTERNATE_PROGRAM_TYPE,
    Header: __('Alternate Rate Program'),
    minWidth: 200,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.TIER,
    Header: __('Tier'),
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.PROGRAM_TYPE,
    Header: __('Program Type'),
    formatter: programTypeFormatterGalaxy,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.PAYMENT,
    Header: __('Monthly Payment'),
    formatter: currencyFormatter,
    sortable: true,
    minWidth: 120,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.SELL_RATE,
    Header: __('Sell Rate'),
    sortable: true,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.BUY_RATE,
    Header: __('Buy Rate'),
    sortable: true,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.MAX_MARKUP,
    Header: __('Max Markup'),
    sortable: true,
    minWidth: 120,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.FRONT_END_PROFIT,
    Header: __('Front End Profit'),
    formatter: currencyFormatter,
    sortable: true,
    minWidth: 120,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.BACK_END_PROFIT,
    Header: __('Back End Profit'),
    formatter: currencyFormatter,
    sortable: true,
    minWidth: 120,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.TOTAL_PROFIT,
    Header: __('Total Profit'),
    formatter: currencyFormatter,
    sortable: true,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.TERM,
    Header: __('Payment Term'),
    sortable: true,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.MAX_ADVANCE,
    Header: __('Max Advance'),
    formatter: currencyFormatter,
    sortable: true,
    minWidth: 120,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.SECURITY_DEPOSIT,
    Header: __('Security Deposit'),
    formatter: currencyFormatter,
    sortable: true,
    minWidth: 120,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.START_DATE,
    Header: __('Start Date'),
    minWidth: 130,
    sortable: true,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.END_DATE,
    Header: __('End Date'),
    minWidth: 130,
    sortable: true,
  },
];

export const SELECT_PROGRAM_TABLE_CONFIG_CANADA = [
  {
    accessor: SELECT_PROGRAM_COLUMNS.LENDER_NAME,
    Header: __('Lender'),
    minWidth: 180,
    sortable: true,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.PROGRAM_NAME,
    Header: __('Program Name'),
    minWidth: 250,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.CODE,
    Header: __('Program #'),
    minWidth: 180,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.TIER,
    Header: __('Tier'),
    sortable: true,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.PROGRAM_TYPE,
    Header: __('Program Type'),
    formatter: programTypeFormatterCanada,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.TERM,
    Header: __('Payment Term'),
    sortable: true,
    minWidth: 120,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.SELL_RATE,
    Header: __('Sell Rate'),
    sortable: true,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.BUY_RATE,
    Header: __('Buy Rate'),
    sortable: true,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.START_DATE,
    Header: __('Start Date'),
    minWidth: 130,
    sortable: true,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.END_DATE,
    Header: __('End Date'),
    minWidth: 130,
    sortable: true,
  },
];

export const SELECT_PROGRAM_TABLE_CONFIG = [
  {
    accessor: SELECT_PROGRAM_COLUMNS.LENDER_NAME,
    Header: __('Lender'),
    minWidth: 180,
    sortable: true,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.CODE,
    Header: __('Program #'),
    minWidth: 180,
    sortable: true,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.PROGRAM_DESC,
    Header: __('Program Description'),
    minWidth: 250,
    sortable: true,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.TIER,
    Header: __('Tier'),
    sortable: true,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.PROGRAM_TYPE,
    Header: __('Program Type'),
    formatter: programTypeColumnFormatter,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.PAYMENT,
    Header: __('Monthly Payment'),
    formatter: currencyFormatter,
    sortable: true,
    minWidth: 120,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.SELL_RATE,
    Header: __('Sell Rate'),
    sortable: true,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.BUY_RATE,
    Header: __('Buy Rate'),
    sortable: true,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.MAX_MARKUP,
    Header: __('Max Markup'),
    sortable: true,
    minWidth: 120,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.FRONT_END_PROFIT,
    Header: __('Front End Profit'),
    formatter: currencyFormatter,
    sortable: true,
    minWidth: 120,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.BACK_END_PROFIT,
    Header: __('Back End Profit'),
    formatter: currencyFormatter,
    sortable: true,
    minWidth: 120,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.TOTAL_PROFIT,
    Header: __('Total Profit'),
    formatter: currencyFormatter,
    sortable: true,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.RANK,
    Header: __('Rank'),
    sortable: true,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.TERM,
    Header: __('Payment Term'),
    sortable: true,
    minWidth: 120,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.MAX_ADVANCE,
    Header: __('Max Advance'),
    formatter: currencyFormatter,
    sortable: true,
    minWidth: 120,
  },
  {
    accessor: SELECT_PROGRAM_COLUMNS.SECURITY_DEPOSIT,
    Header: __('Security Deposit'),
    formatter: currencyFormatter,
    sortable: true,
    minWidth: 120,
  },
];

export const DEFAULT_FILTER_TYPES = [
  SELECT_PROGRAM_COLUMNS.LENDER_NAME,
  SELECT_PROGRAM_COLUMNS.PROGRAM_TYPE,
  SELECT_PROGRAM_COLUMNS.TERM,
];

export const DEFAULT_FILTER_TYPES_GALAXY = [
  SELECT_PROGRAM_COLUMNS.LENDER_NAME,
  SELECT_PROGRAM_COLUMNS.PROGRAM_TYPE,
  SELECT_PROGRAM_COLUMNS.ALTERNATE_PROGRAM_TYPE,
  SELECT_PROGRAM_COLUMNS.TIER,
];

export const EVENT_ACTIONS = {
  SELECT_OPTION: 'select-option',
  DE_SELECT_OPTION: 'deselect-option',
};

export const DEFAULT_PAGE = 1;

export const DEFAULT_PAGE_SIZE = 50;

export const ALL_OPTION = {
  value: ALL_VALUE,
  label: ALL_LABEL,
};

const PROGRAM_TYPE_KEYS = {
  SPECIAL_PROGRAM: 'SPECIAL_PROGRAM',
  STANDARD_PROGRAM: 'STANDARD_PROGRAM',
};

export const SPECIAL_STANDARD_VS_PROGRAM_TYPE = {
  1: __('Special'),
  0: __('Standard'),
};

const SPECIAL_STANDARD_VS_PROGRAM_TYPE_GALAXY = {
  [PROGRAM_TYPE_KEYS.SPECIAL_PROGRAM]: __('Special'),
  [PROGRAM_TYPE_KEYS.STANDARD_PROGRAM]: __('Standard'),
};

const SPECIAL_STANDARD_VS_PROGRAM_TYPE_CANADA = {
  [PROGRAM_TYPE_KEYS.SPECIAL_PROGRAM]: __('Special'),
  [PROGRAM_TYPE_KEYS.STANDARD_PROGRAM]: __('Standard'),
};

export const PROGRAM_TYPE_FILTER_OPTIONS_GALAXY = [
  { ...ALL_OPTION, label: __('Special & Standard') },
  { value: PROGRAM_TYPE_KEYS.SPECIAL_PROGRAM, label: __('Special') },
  { value: PROGRAM_TYPE_KEYS.STANDARD_PROGRAM, label: __('Standard') },
];

export const PROGRAM_TYPE_FILTER_OPTIONS_CANADA = [
  { ...ALL_OPTION, label: __('Special & Standard') },
  { value: PROGRAM_TYPE_KEYS.SPECIAL_PROGRAM, label: __('Special') },
  { value: PROGRAM_TYPE_KEYS.STANDARD_PROGRAM, label: __('Standard') },
];

export const PROGRAM_TYPE_FILTER_OPTIONS = [
  { ...ALL_OPTION, label: __('Special & Standard') },
  { value: true, label: __('Special') },
  { value: false, label: __('Standard') },
];

export const SAVE_COMPONENT_HEIGHT = 64;

export const SELECT_EVENT_TYPES = {
  SELECT: 'select-option',
  DESELECT: 'deselect-option',
};

export const FORD_GENERIC_CALL_DATA = [
  {
    term: 66,
    percentage: 15.0,
    FordFlexFirstTerm: 36,
  },
  {
    term: 66,
    percentage: 18.0,
    FordFlexFirstTerm: 36,
  },
  {
    term: 66,
    percentage: 21.0,
    FordFlexFirstTerm: 36,
  },
  {
    term: 75,
    percentage: 15.0,
    FordFlexFirstTerm: 36,
  },
];
