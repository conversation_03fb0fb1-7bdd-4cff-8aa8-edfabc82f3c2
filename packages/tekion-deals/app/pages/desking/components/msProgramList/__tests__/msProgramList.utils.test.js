import { LENDERS } from '@tekion/tekion-base/marketScan/constants/marketScan.constants';

import { LENDER_RATE_TYPES } from 'tbase/constants/retail/marketScan';
import {
  getSanitizedMscanPayload,
  getSanitizedProgram,
  getDefaultTierCode,
  getBuyOrSellRate,
  isFordFlexProgram,
  shouldMakeFordGenericCall,
} from '../msProgramList.utils';
import {
  mockV2TermsData,
  loanColumnData,
  leaseColumnData,
  sampleCodeName,
  sampleRetailMscanPayload,
  sanitizedMscanPayloadFilter,
  sampleLeaseMscanPayload,
  sanitizedLeaseMscanPayloadFilter,
  leasePart,
  retailPart,
  sampleMscanProgram,
  sampleRowData,
  lenders,
  defaultTier,
  columnDataWithTiers,
  defaultColumnTier,
  mockRowDataAPR,
  mockRowDataEmpty,
  mockRowDataMF,
  allTires,
  fordFlexProgramTrue,
  fordFlexProgramFalse,
  genericProgramsWithFordFlex,
  genericProgramsWithoutFordFlex,
  genericProgramsEmpty,
  selectedLenderCodeNamesWithFord,
  selectedLenderCodeNamesWithoutFord,
} from '../__mocks__/MSProgramList.container.mock';
import { SELECT_PROGRAM_COLUMNS } from '../msProgramList.constants';

const DeskingAPI = () => ({ getMScanV2TermsData });
const getMScanV2TermsData = async () => mockV2TermsData;

jest.mock('pages/desking/desking.api', () => DeskingAPI);

describe('Test MSProgramListContainer', () => {
  it('test getSanitizedMscanPayload', () => {
    const payload = getSanitizedMscanPayload(loanColumnData, sampleRetailMscanPayload, [sampleCodeName]);
    const payload1 = getSanitizedMscanPayload(loanColumnData, sampleRetailMscanPayload, [LENDERS.FORD_MOTOR_CREDIT]);
    const payload2 = getSanitizedMscanPayload(leaseColumnData, sampleLeaseMscanPayload, [sampleCodeName]);

    expect(payload.Filter).toMatchObject(sanitizedMscanPayloadFilter);
    expect(payload.RetailPart).toMatchObject(retailPart);
    expect(payload1.Filter).toMatchObject({
      ...sanitizedMscanPayloadFilter,
      Retail: { ...sanitizedMscanPayloadFilter.Retail, IncludeFordFlex: true },
    });
    // Lease
    expect(payload2.Filter).toMatchObject(sanitizedLeaseMscanPayloadFilter);
    expect(payload2.LeasePart).toMatchObject(leasePart);
  });

  it('test getSanitizedInitialData', () => {
    expect(getSanitizedProgram(sampleMscanProgram)).toMatchObject(sampleRowData);
  });

  it('test default tiers', () => {
    // When no tier is selected in columnData, should return all tiers
    expect(getDefaultTierCode({ lenders, selectedLenderId: '648d51fb5d60c60d1f052276' })).toMatchObject(allTires);

    // loanColumnData does NOT have a selected tier, so should also return all tiers
    expect(
      getDefaultTierCode({ columnData: loanColumnData, lenders, selectedLenderId: '648d51fb5d60c60d1f052276' })
    ).toMatchObject(allTires);

    // Should return the selected tier code from columnDataWithTiers
    expect(
      getDefaultTierCode({ columnData: columnDataWithTiers, lenders, selectedLenderId: '648d51fb5d60c60d1f052276' })
    ).toMatchObject(defaultColumnTier);

    // Should return empty array for unknown lender
    expect(getDefaultTierCode({ lenders, selectedLenderId: 'TEMP' })).toMatchObject([]);
  });

  describe('isFordFlexProgram', () => {
    it('should return true when IsFordFlexDeal is true', () => {
      expect(isFordFlexProgram(fordFlexProgramTrue)).toBe(true);
    });
    it('should return false when IsFordFlexDeal is false', () => {
      expect(isFordFlexProgram(fordFlexProgramFalse)).toBe(false);
    });
    it('should return false when program is null', () => {
      expect(isFordFlexProgram(null)).toBe(false);
    });
    it('should return false when FullDetails is missing', () => {
      expect(isFordFlexProgram({})).toBe(false);
    });
    it('should return false when Retail is missing', () => {
      expect(isFordFlexProgram({ FullDetails: {} })).toBe(false);
    });
  });

  describe('shouldMakeFordGenericCall', () => {
    it('should return false if Ford Motor Credit is not in selectedLenderCodeNames', () => {
      expect(shouldMakeFordGenericCall(genericProgramsEmpty, selectedLenderCodeNamesWithoutFord)).toBe(false);
    });
    it('should return false if Ford Flex program is present', () => {
      expect(shouldMakeFordGenericCall(genericProgramsWithFordFlex, selectedLenderCodeNamesWithFord)).toBe(false);
    });
    it('should return true if Ford Motor Credit is in selectedLenderCodeNames and no Ford Flex program is present', () => {
      expect(shouldMakeFordGenericCall(genericProgramsWithoutFordFlex, selectedLenderCodeNamesWithFord)).toBe(true);
    });
    it('should return true if Ford Motor Credit is in selectedLenderCodeNames and programs is empty', () => {
      expect(shouldMakeFordGenericCall(genericProgramsEmpty, selectedLenderCodeNamesWithFord)).toBe(true);
    });
  });
});

describe('Test getBuyOrSellRate', () => {
  describe('when rateType is MF (Money Factor)', () => {
    it('should return MF sell rate when accessor is SELL_RATE', () => {
      const result = getBuyOrSellRate(mockRowDataMF, SELECT_PROGRAM_COLUMNS.SELL_RATE, LENDER_RATE_TYPES.MF);
      expect(result).toBe(0.00125);
    });

    it('should return MF buy rate when accessor is BUY_RATE', () => {
      const result = getBuyOrSellRate(mockRowDataMF, SELECT_PROGRAM_COLUMNS.BUY_RATE, LENDER_RATE_TYPES.MF);
      expect(result).toBe(0.001);
    });

    it('should return MF buy rate when accessor is not SELL_RATE', () => {
      const result = getBuyOrSellRate(mockRowDataMF, 'someOtherAccessor', LENDER_RATE_TYPES.MF);
      expect(result).toBe(0.001);
    });

    it('should return undefined when MF rates are not available', () => {
      const result = getBuyOrSellRate(mockRowDataEmpty, SELECT_PROGRAM_COLUMNS.SELL_RATE, LENDER_RATE_TYPES.MF);
      expect(result).toBeUndefined();
    });
  });

  describe('when rateType is APR', () => {
    it('should return APR sell rate when accessor is SELL_RATE', () => {
      const result = getBuyOrSellRate(mockRowDataAPR, SELECT_PROGRAM_COLUMNS.SELL_RATE, LENDER_RATE_TYPES.APR);
      expect(result).toBe(6.5);
    });

    it('should return APR buy rate when accessor is BUY_RATE', () => {
      const result = getBuyOrSellRate(mockRowDataAPR, SELECT_PROGRAM_COLUMNS.BUY_RATE, LENDER_RATE_TYPES.APR);
      expect(result).toBe(5.5);
    });

    it('should return APR buy rate when accessor is not SELL_RATE', () => {
      const result = getBuyOrSellRate(mockRowDataAPR, 'someOtherAccessor', LENDER_RATE_TYPES.APR);
      expect(result).toBe(5.5);
    });

    it('should return undefined when APR rates are not available', () => {
      const result = getBuyOrSellRate(mockRowDataEmpty, SELECT_PROGRAM_COLUMNS.SELL_RATE, LENDER_RATE_TYPES.APR);
      expect(result).toBeUndefined();
    });
  });

  describe('when rateType is undefined or other value', () => {
    it('should default to APR behavior and return sell rate when accessor is SELL_RATE', () => {
      const result = getBuyOrSellRate(mockRowDataMF, SELECT_PROGRAM_COLUMNS.SELL_RATE, undefined);
      expect(result).toBe(5.5);
    });

    it('should default to APR behavior and return buy rate when accessor is not SELL_RATE', () => {
      const result = getBuyOrSellRate(mockRowDataMF, SELECT_PROGRAM_COLUMNS.BUY_RATE, undefined);
      expect(result).toBe(4.5);
    });

    it('should default to APR behavior with unknown rate type', () => {
      const result = getBuyOrSellRate(mockRowDataMF, SELECT_PROGRAM_COLUMNS.SELL_RATE, 'UNKNOWN_RATE_TYPE');
      expect(result).toBe(5.5);
    });
  });

  describe('edge cases', () => {
    it('should handle null rowData gracefully', () => {
      const result = getBuyOrSellRate(null, SELECT_PROGRAM_COLUMNS.SELL_RATE, LENDER_RATE_TYPES.MF);
      expect(result).toBeUndefined();
    });

    it('should handle undefined rowData gracefully', () => {
      const result = getBuyOrSellRate(undefined, SELECT_PROGRAM_COLUMNS.SELL_RATE, LENDER_RATE_TYPES.APR);
      expect(result).toBeUndefined();
    });

    it('should handle rowData without program property', () => {
      const result = getBuyOrSellRate({}, SELECT_PROGRAM_COLUMNS.SELL_RATE, LENDER_RATE_TYPES.MF);
      expect(result).toBeUndefined();
    });

    it('should handle empty accessor', () => {
      const result = getBuyOrSellRate(mockRowDataMF, '', LENDER_RATE_TYPES.MF);
      expect(result).toBe(0.001);
    });

    it('should handle null accessor', () => {
      const result = getBuyOrSellRate(mockRowDataMF, null, LENDER_RATE_TYPES.APR);
      expect(result).toBe(4.5);
    });
  });
});
