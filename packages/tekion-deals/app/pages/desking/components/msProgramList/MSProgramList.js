/* eslint-disable no-param-reassign */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { defaultMemoize } from 'reselect';
import { compose } from 'recompose';

import _get from 'lodash/get';
import _map from 'lodash/map';
import _noop from 'lodash/noop';
import _keys from 'lodash/keys';
import _values from 'lodash/values';
import _toLower from 'lodash/toLower';
import _size from 'lodash/size';
import _slice from 'lodash/slice';
import _identity from 'lodash/identity';
import _orderBy from 'lodash/orderBy';
import _isEmpty from 'lodash/isEmpty';
import _castArray from 'lodash/castArray';
import _includes from 'lodash/includes';
import _forEach from 'lodash/forEach';

import COLORS from 'tstyles/exports.scss';

import { EMPTY_OBJECT, NO_DATA } from '@tekion/tekion-base/app.constants';
import * as ColumnDataReader from '@tekion/tekion-base/marketScan/readers/columnData.reader';
import { getMarketScanPayloadObject } from '@tekion/tekion-base/marketScan';

import { toaster } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import SaveComponent from '@tekion/tekion-components/src/molecules/SaveComponent';
import TableManager from '@tekion/tekion-components/src/organisms/TableManager';
import withCustomSortTable from '@tekion/tekion-components/src/organisms/withCustomSortTable';
import TableManagerActions from '@tekion/tekion-components/src/organisms/TableManager/constants/actionTypes';
import { PropertyControlledComponent } from '@tekion/tekion-components/src/molecules';
import Tag from '@tekion/tekion-components/src/atoms/Tag';
import Content from '@tekion/tekion-components/src/atoms/Content';
import Popover, { POPOVER_PLACEMENT } from '@tekion/tekion-components/src/molecules/popover';

import ModalHeader from 'organisms/updateModal/UpdateModalHeader';
import DeskingAPI from 'pages/desking/desking.api';
import { MESSAGES, TOASTER_STATUS } from 'constants/pages';
import SearchInputWithState from 'molecules/searchInputWithState';
import { getLendersDataForDropDown } from 'pages/desking/components/dealDetails/infoColumn/lenderPayment/utils';

import CalcEngineProperties from 'utils/CalcEngineProperties';
import { isCanadaDealer } from 'utils/dealerUtils';
import { withTekionConversion, DATE_TIME_FORMAT } from '@tekion/tekion-conversion-web';
import * as MSProgramListUtils from './msProgramList.utils';
import * as MSProgramListConstants from './msProgramList.constants';

import styles from './msProgramList.module.scss';

const SortableTableManager = withCustomSortTable(TableManager);
class MSProgramList extends PureComponent {
  constructor(props) {
    super(props);
    const { columnData, lenders } = props;
    const selectedLenderId = ColumnDataReader.getSelectedLenderId(columnData);
    this.columns = this.getLenderProgramColumns();

    this.state = {
      loading: false,
      saving: false,
      data: [],
      selectedFilters: MSProgramListUtils.getInitialSelectedFilters(selectedLenderId, columnData, lenders),
      page: MSProgramListConstants.DEFAULT_PAGE,
      resultsPerPage: MSProgramListConstants.DEFAULT_PAGE_SIZE,
      searchText: '',
      selectedRow: null,
      terms: [],
      sortDetails: {
        lenderName: 'ASC', // Initially, data is sorted based on lender name
      },
    };
  }

  getCellMappingIterator = column => ({
    ...column,
    key: column.accessor,
    Cell: ({ original: rowData }) => this.renderCell(column, rowData),
  });

  getLenderProgramColumns = () => {
    let tableConfig = MSProgramListConstants.SELECT_PROGRAM_TABLE_CONFIG;
    if (CalcEngineProperties.updateByGalaxyEngine()) {
      tableConfig = MSProgramListConstants.SELECT_PROGRAM_TABLE_CONFIG_GALAXY;
    }
    if (isCanadaDealer()) {
      tableConfig = MSProgramListConstants.SELECT_PROGRAM_TABLE_CONFIG_CANADA;
    }
    return _map(tableConfig, this.getCellMappingIterator);
  };

  getSubHeaderProps = defaultMemoize((searchText, isGalaxyEnabled, isCanadaDealerEnabled) => ({
    subHeaderRightActions: [
      {
        renderer: SearchInputWithState,
        renderOptions: {
          onSearchTextChange: this.onSearchTextChange,
          searchText,
          containerClassName: styles.searchBox,
          inputClassName: isGalaxyEnabled ? styles.searchInput : null,
          placeholder: isGalaxyEnabled || isCanadaDealerEnabled ? __('Search') : __('Search by Lender or Program #'),
        },
      },
    ],
  }));

  getFilteredTableData = defaultMemoize((data, searchText, selectedFilters, isGalaxyEnabled, isCanadaDealerEnabled) => {
    const modifiedSearchText = searchText.toLowerCase();
    const programTypeFilter = MSProgramListUtils.getAppliedFilterOfType(
      selectedFilters,
      MSProgramListConstants.SELECT_PROGRAM_COLUMNS.PROGRAM_TYPE
    );
    const termsFilter = MSProgramListUtils.getAppliedFilterOfType(
      selectedFilters,
      MSProgramListConstants.SELECT_PROGRAM_COLUMNS.TERM
    );

    return MSProgramListUtils.getAppliedFilteredData(
      data,
      programTypeFilter,
      termsFilter,
      modifiedSearchText,
      isGalaxyEnabled,
      isCanadaDealerEnabled
    );
  });

  getSortedTableData = data => {
    const { page, resultsPerPage, sortDetails } = this.state;

    return _slice(
      _orderBy(
        data,
        _keys(sortDetails),
        _map(_values(sortDetails), value => _toLower(value))
      ),
      (page - 1) * resultsPerPage, // Entries sliced based on the page size
      page * resultsPerPage
    );
  };

  componentDidMount() {
    this.getTableData(true);
  }

  getTableData = async didMount => {
    const { deal, columnData } = this.props;
    const selectedLenderCodeNames = this.getCodeNames();
    const isCanadaDealerEnabled = isCanadaDealer();
    const selectedLenderIds = this.getAppliedLenderIds();
    const selectedAlternateRateProgramType = this.getAlternateRateProgramType();
    const selectedTiersDetail = this.getSelectedTiersDetail();
    const msPayload = this.getMscanPayload(columnData);
    const downPmtInt = Number(ColumnDataReader.getSelectedDownPayment(columnData));
    const isGalaxyEnabled = CalcEngineProperties.updateByGalaxyEngine();
    let lenderProgramsAPI = isGalaxyEnabled ? DeskingAPI.getLenderPrograms : DeskingAPI.getMarketScanDataV3;
    if (isCanadaDealerEnabled) {
      lenderProgramsAPI = DeskingAPI.getLeaseLoyaltyPrograms;
    }
    let lenderProgramsPayload = isGalaxyEnabled
      ? MSProgramListUtils.getLenderProgramsPayloadGalaxy(
          deal,
          columnData,
          selectedLenderCodeNames,
          selectedLenderIds,
          selectedAlternateRateProgramType,
          selectedTiersDetail
        )
      : MSProgramListUtils.getSanitizedMscanPayload(columnData, msPayload, selectedLenderCodeNames);

    if (isCanadaDealerEnabled) {
      lenderProgramsPayload = MSProgramListUtils.getLenderProgramsPayloadCanada(deal, columnData, selectedLenderIds);
    }
    this.setState({ loading: true });
    const { error, data } = await lenderProgramsAPI(lenderProgramsPayload, downPmtInt);
    let updatedData = data;
    if (isCanadaDealerEnabled) {
      updatedData = _get(data, 'multiProgramResponseItemList', []);
    }
    const fordPrograms = !CalcEngineProperties.updateByGalaxyEngine()
      ? await this.fetchGenericDataForFordMotorCredit(selectedLenderCodeNames, updatedData || [])
      : await Promise.resolve([]);
    if (error) {
      toaster(TOASTER_STATUS.ERROR, MESSAGES.DEFAULT_ERROR);
      this.setState({ loading: false });
    } else {
      this.init([...fordPrograms, ...(updatedData || [])], didMount);
    }
  };

  getFilterProps = defaultMemoize((terms, lenders, selectedFilters, columnData, lendersFromApi) => {
    const isGalaxyEnabled = CalcEngineProperties.updateByGalaxyEngine();
    const isCanadaDealerEnabled = isCanadaDealer();
    const paymentType = ColumnDataReader.getPaymentType(columnData);
    const appliedLenderIds = this.getAppliedLenderIds();
    const termOptions = MSProgramListUtils.getTermsOptions(terms);
    const lenderOptions = MSProgramListUtils.getLenderOptions(
      lenders,
      paymentType,
      isGalaxyEnabled,
      isCanadaDealerEnabled
    );
    const tierOptions = MSProgramListUtils.getTierOptions(lenders, appliedLenderIds);
    const alternateProgramTypeOptions = MSProgramListUtils.getAlternateRateProgramTypeOptions(
      lenders,
      appliedLenderIds,
      lendersFromApi
    );

    if (isCanadaDealerEnabled) {
      return MSProgramListUtils.getFilterPropsForCanada(selectedFilters, termOptions, lenderOptions);
    }

    return isGalaxyEnabled
      ? MSProgramListUtils.getFilterPropsForGalaxy(
          selectedFilters,
          lenderOptions,
          tierOptions,
          alternateProgramTypeOptions
        )
      : MSProgramListUtils.getFilterProps(selectedFilters, termOptions, lenderOptions);
  });

  fetchGenericDataForFordMotorCredit = async (codeNames, existingData = []) => {
    const { columnData } = this.props;

    // Check if Ford Motor Credit is in the selected lenders and if we need to make the generic call
    if (
      codeNames.includes('Ford Motor Credit') &&
      MSProgramListUtils.shouldMakeFordGenericCall(existingData, codeNames)
    ) {
      const modifiedColumns = MSProgramListUtils.modifyColumnData(columnData);

      const payloadLists = [];
      modifiedColumns.forEach(column => {
        const mscanPayload = this.getMscanPayload(column, true);
        const saniTizedPayload = MSProgramListUtils.getSanitizedMscanPayloadForFordGeneric(column, mscanPayload);
        payloadLists.push(saniTizedPayload);
      });
      const { marketScanData = EMPTY_OBJECT } = await DeskingAPI.getMarketScanData(payloadLists);
      const modifiedGenricLenderPrograms = [];
      payloadLists.forEach(item => {
        const { Id } = item || EMPTY_OBJECT;
        const dwnPmtVsPrograms = marketScanData?.[Id];
        const dwnPmtIntVsPrograms = _keys(dwnPmtVsPrograms).reduce((acc, dwnPmt) => {
          acc = {
            ...dwnPmtVsPrograms[dwnPmt],
            FullDetails: { ...dwnPmtVsPrograms[dwnPmt]?.FullDetails, LenderName: 'Ford Motor Credit' },
            ID: dwnPmtVsPrograms[dwnPmt]?.Payment,
          };
          return acc;
        }, {});
        modifiedGenricLenderPrograms.push(dwnPmtIntVsPrograms);
      });
      return modifiedGenricLenderPrograms;
    }
    return [];
  };

  getCodeNames = () => {
    const { lenders } = this.props;
    const appliedLenderIds = this.getAppliedLenderIds();
    return MSProgramListUtils.getLenderCodeNames(lenders, appliedLenderIds);
  };

  getAppliedLenderIds = () => {
    const { selectedFilters } = this.state;
    return MSProgramListUtils.getAppliedFilterOfType(
      selectedFilters,
      MSProgramListConstants.SELECT_PROGRAM_COLUMNS.LENDER_NAME
    )?.values;
  };

  getAlternateRateProgramType = () => {
    const { selectedFilters } = this.state;
    return MSProgramListUtils.getAppliedFilterOfType(
      selectedFilters,
      MSProgramListConstants.SELECT_PROGRAM_COLUMNS.ALTERNATE_PROGRAM_TYPE
    )?.values;
  };

  getSelectedTiersCode = () => {
    const { selectedFilters } = this.state;
    return MSProgramListUtils.getAppliedFilterOfType(
      selectedFilters,
      MSProgramListConstants.SELECT_PROGRAM_COLUMNS.TIER
    )?.values;
  };

  getSelectedTiersDetail = () => {
    const { lenders } = this.props;
    const selectedTiers = this.getSelectedTiersCode();
    const appliedLenderIds = this.getAppliedLenderIds();
    return MSProgramListUtils.getTierDetails(selectedTiers, lenders, appliedLenderIds);
  };

  getMscanPayload = (columnData, isGenericPayload = false) => {
    const { lenders, deal, salesSetupInfo } = this.props;
    const { selectedFilters } = this.state;
    const appliedLenderId = MSProgramListUtils.getAppliedFilterOfType(
      selectedFilters,
      MSProgramListConstants.SELECT_PROGRAM_COLUMNS.LENDER_NAME
    )?.values?.[0];
    return getMarketScanPayloadObject({
      columnData,
      lender: appliedLenderId,
      lenders,
      downPayments: _castArray(ColumnDataReader.getSelectedDownPayment(columnData)),
      isGenericPayload,
      excludeDisclosureTypes: [],
      deal,
      salesSetupInfo,
    })?.payload;
  };

  init = (programList, didMount) => {
    const { columnData, lenders } = this.props;
    const { selectedFilters } = this.state;
    const isGalaxyEnabled = CalcEngineProperties.updateByGalaxyEngine();
    const isCanadaDealerEnabled = isCanadaDealer();
    const appliedLenderIds = this.getAppliedLenderIds();
    const { data, selectedRow, terms } = MSProgramListUtils.getSanitizedInitialData(
      columnData,
      programList,
      appliedLenderIds,
      isGalaxyEnabled,
      isCanadaDealerEnabled
    );
    const sortedTerms = isGalaxyEnabled ? [] : Object.keys(terms).sort((a, b) => a - b);
    const appliedTermFilterValues = isGalaxyEnabled
      ? []
      : MSProgramListUtils.getAppliedTermFilterValues(columnData, sortedTerms);

    this.setState({
      data,
      selectedRow,
      loading: false,
      terms: sortedTerms,
      ...(didMount && {
        selectedFilters: MSProgramListUtils.getInitialTermFilter(selectedFilters, appliedTermFilterValues),
      }),
    });
  };

  setStateAndResetPagination = (stateToSet, cb = _noop) =>
    this.setState(
      {
        ...stateToSet,
        page: MSProgramListConstants.DEFAULT_PAGE,
        resultsPerPage: MSProgramListConstants.DEFAULT_PAGE_SIZE,
      },
      cb
    );

  onSearchTextChange = searchText => this.setStateAndResetPagination({ searchText });

  getSanitizedSelectedFilters = appliedFilters => {
    const { lenders, columnData } = this.props;
    const { terms } = this.state;
    const lenderFilterValues = getLendersDataForDropDown(lenders, ColumnDataReader.getPaymentType(columnData)).map(
      ({ value }) => value
    );
    return MSProgramListUtils.getSanitizedFilters(appliedFilters, terms, lenderFilterValues);
  };

  updateTableData = (appliedFilters, sanitizedSelectedFilters, filterType) => {
    const { additional } = MSProgramListUtils.getAppliedFilterOfType(appliedFilters, filterType) || EMPTY_OBJECT;
    const { values } = MSProgramListUtils.getAppliedFilterOfType(sanitizedSelectedFilters, filterType) || EMPTY_OBJECT;

    if (additional?.eventDetails?.action) {
      if (!CalcEngineProperties.updateByGalaxyEngine() && _isEmpty(values)) {
        this.setState({ data: [], selectedRow: null });
      } else {
        this.getTableData();
      }
    }
  };

  handleTableUpdate = (appliedFilters, sanitizedSelectedFilters) => () => {
    const filterTypes = CalcEngineProperties.updateByGalaxyEngine()
      ? MSProgramListConstants.FILTER_TYPES_TO_UPDATE_TABLE_DATA_GALAXY
      : MSProgramListConstants.FILTER_TYPES_TO_UPDATE_TABLE_DATA;

    _forEach(filterTypes, filterType => {
      this.updateTableData(appliedFilters, sanitizedSelectedFilters, filterType);
    });
  };

  handleFilterChange = payload => {
    const { lenders } = this.props;
    const { value: appliedFilters } = payload;
    let sanitizedSelectedFilters = this.getSanitizedSelectedFilters(appliedFilters);

    if (CalcEngineProperties.updateByGalaxyEngine()) {
      // On lender filter change, update selected tiers and alternate program types for galaxy
      sanitizedSelectedFilters = MSProgramListUtils.updateLenderDependentFilters(
        appliedFilters,
        sanitizedSelectedFilters,
        lenders
      );
    }

    this.setStateAndResetPagination(
      { selectedFilters: sanitizedSelectedFilters },
      this.handleTableUpdate(appliedFilters, sanitizedSelectedFilters)
    );
  };

  onAction = async action => {
    const { payload, type } = action || EMPTY_OBJECT;

    switch (type) {
      case TableManagerActions.TABLE_ITEMS_PAGE_UPDATE: {
        const {
          value: { page, resultsPerPage },
        } = payload;
        this.setState({ page, resultsPerPage });
        break;
      }

      case TableManagerActions.TABLE_ITEMS_SORT: {
        const sortTypeMap = payload?.value?.sortTypeMap;
        this.setState({
          sortDetails: sortTypeMap,
        });
        break;
      }

      case TableManagerActions.TABLE_ITEM_CLICK: {
        this.setState({ selectedRow: payload?.value?.original });
        break;
      }

      case TableManagerActions.TABLE_ITEMS_SET_FILTER: {
        this.handleFilterChange(payload);
        break;
      }

      default:
    }
  };

  renderProgramCodeCell = (rowData, column) => {
    const { minWidth } = column;
    const { fordFlexDeal, code } = rowData || EMPTY_OBJECT;

    return (
      <div className="d-flex align-items-center" style={{ minWidth }}>
        <div className={styles.code}>{code}</div>

        <PropertyControlledComponent controllerProperty={!CalcEngineProperties.updateByGalaxyEngine() && fordFlexDeal}>
          <Tag className="m-l-8">
            <Content>{__('Flex Buy')}</Content>
          </Tag>
        </PropertyControlledComponent>
      </div>
    );
  };

  renderProgramDescCell = (rowData, column) => {
    const { minWidth } = column;
    const { description } = rowData || EMPTY_OBJECT;

    if (!description) return NO_DATA;

    return (
      <div style={{ minWidth }}>
        <Popover
          trigger="hover"
          content={<div className={styles.desc}>{description}</div>}
          placement={POPOVER_PLACEMENT.TOP_LEFT}>
          {description}
        </Popover>
      </div>
    );
  };

  renderAlternateRateProgramType = (rowData, column) => {
    const { minWidth } = column;
    const { alternateProgrammeName } = rowData || EMPTY_OBJECT;
    if (_isEmpty(alternateProgrammeName)) return NO_DATA;

    const { lenders, lendersFromApi } = this.props;
    const appliedLenderIds = this.getAppliedLenderIds();
    const alternateProgrammeTypeDisplayVal = MSProgramListUtils.getAlternateRateProgramTypeDisplayVal(
      lenders,
      appliedLenderIds,
      alternateProgrammeName,
      lendersFromApi
    );

    return <div style={{ minWidth }}> {alternateProgrammeTypeDisplayVal} </div>;
  };

  getCellValue = (rowData, accessor) => {
    const { columnData } = this.props;
    if (
      CalcEngineProperties.updateByGalaxyEngine() &&
      _includes(
        [
          MSProgramListConstants.SELECT_PROGRAM_COLUMNS.SELL_RATE,
          MSProgramListConstants.SELECT_PROGRAM_COLUMNS.BUY_RATE,
        ],
        accessor
      )
    ) {
      const aprData = ColumnDataReader.getAPRData(columnData);
      const rateType = _get(aprData, 'rateType');
      return MSProgramListUtils.getBuyOrSellRate(rowData, accessor, rateType);
    }

    return rowData?.[accessor];
  };

  renderCell = (column, rowData) => {
    const { getFormattedDateAndTime } = this.props;
    const { accessor, minWidth, formatter } = column;
    const formatVal = formatter || _identity;
    const value = this.getCellValue(rowData, accessor);

    switch (accessor) {
      case MSProgramListConstants.SELECT_PROGRAM_COLUMNS.CODE:
        return this.renderProgramCodeCell(rowData, column);

      case MSProgramListConstants.SELECT_PROGRAM_COLUMNS.PROGRAM_DESC:
        return this.renderProgramDescCell(rowData, column);

      case MSProgramListConstants.SELECT_PROGRAM_COLUMNS.START_DATE:
      case MSProgramListConstants.SELECT_PROGRAM_COLUMNS.END_DATE:
        return getFormattedDateAndTime({
          value,
          formatType: DATE_TIME_FORMAT.ABBREVIATED_DAY_DATE_ABBREVIATED_MONTH_YEAR,
        });

      case MSProgramListConstants.SELECT_PROGRAM_COLUMNS.ALTERNATE_PROGRAM_TYPE:
        return this.renderAlternateRateProgramType(rowData, column);

      default:
        return <div style={{ minWidth }}>{formatVal(value)}</div>;
    }
  };

  getTrProps = ({ selectedRow }, rowInfo) => {
    if (selectedRow && MSProgramListUtils.matchRow(selectedRow, rowInfo)) {
      return { style: { backgroundColor: COLORS.aliceBlue } };
    }

    return {};
  };

  onPrimaryAction = () => {
    const { selectedRow, selectedFilters } = this.state;
    const { onSaveSelectedProgram, onCloseModal } = this.props;
    const cb = () => this.setState({ saving: false }, onCloseModal);

    this.setState({ saving: true }, () => {
      const appliedLenderId = MSProgramListUtils.getAppliedFilterOfType(
        selectedFilters,
        MSProgramListConstants.SELECT_PROGRAM_COLUMNS.LENDER_NAME
      )?.values?.[0];
      onSaveSelectedProgram(selectedRow?.program, appliedLenderId, cb);
    });
  };

  render() {
    const {
      saving,
      data,
      loading,
      selectedFilters,
      page,
      resultsPerPage,
      searchText,
      selectedRow,
      sortDetails,
      terms,
    } = this.state;
    const { onCloseModal, lenders, columnData, contentHeight, lendersFromApi } = this.props;
    const isGalaxyEnabled = CalcEngineProperties.updateByGalaxyEngine();
    const isCanadaDealerEnabled = isCanadaDealer();
    const filteredTableData = this.getFilteredTableData(
      data,
      searchText,
      selectedFilters,
      isGalaxyEnabled,
      isCanadaDealerEnabled
    );
    const tableData = this.getSortedTableData(filteredTableData);
    const tableProps = MSProgramListUtils.getTableProps(
      loading,
      _size(filteredTableData),
      page,
      resultsPerPage,
      selectedRow,
      this.getTrProps
    );
    const filterProps = this.getFilterProps(terms, lenders, selectedFilters, columnData, lendersFromApi);

    return (
      <>
        <ModalHeader title={__('Select Lender')} onClose={onCloseModal} />

        <div className="full-width" style={{ height: contentHeight - MSProgramListConstants.SAVE_COMPONENT_HEIGHT }}>
          <SortableTableManager
            onAction={this.onAction}
            tableManagerProps={MSProgramListConstants.TABLE_MANAGER_PROPS}
            containerClassName={styles.container}
            data={tableData}
            columns={this.columns}
            tableProps={tableProps}
            filterProps={filterProps}
            sortDetails={sortDetails}
            subHeaderProps={this.getSubHeaderProps(searchText, isGalaxyEnabled, isCanadaDealerEnabled)}
            isMultiSort={false}
          />
        </div>

        <SaveComponent
          onPrimaryAction={this.onPrimaryAction}
          onSecondaryAction={onCloseModal}
          primaryActionLoading={saving}
        />
      </>
    );
  }
}

MSProgramList.propTypes = {
  onCloseModal: PropTypes.func,
  columnData: PropTypes.object,
  deal: PropTypes.object,
  salesSetupInfo: PropTypes.object,
  lenders: PropTypes.array,
  onSaveSelectedProgram: PropTypes.func,
  contentHeight: PropTypes.number.isRequired,
};

MSProgramList.defaultProps = {
  onCloseModal: _noop,
  columnData: EMPTY_OBJECT,
  deal: EMPTY_OBJECT,
  salesSetupInfo: EMPTY_OBJECT,
  lenders: [],
  onSaveSelectedProgram: _noop,
};

export default compose(withTekionConversion)(MSProgramList);
