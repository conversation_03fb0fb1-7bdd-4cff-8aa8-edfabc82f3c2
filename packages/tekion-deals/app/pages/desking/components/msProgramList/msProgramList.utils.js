import { defaultMemoize } from 'reselect';
import produce from 'immer';

import _reduce from 'lodash/reduce';
import _map from 'lodash/map';
import _find from 'lodash/find';
import _get from 'lodash/get';
import _set from 'lodash/set';
import _every from 'lodash/every';
import _keys from 'lodash/keys';
import _filter from 'lodash/filter';
import _isEmpty from 'lodash/isEmpty';
import _compact from 'lodash/compact';
import _includes from 'lodash/includes';
import _toString from 'lodash/toString';
import _findIndex from 'lodash/findIndex';
import _castArray from 'lodash/castArray';
import _some from 'lodash/some';

import { generateColumnId } from 'utils/markteScan.util';

import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import FILTER_OPERATORS from '@tekion/tekion-base/constants/filterOperators';
import {
  isLoanOrCashOrBalloonPaymentType,
  isLeaseOrOPLeasePaymentType,
  getPaymentType,
  isBalloonPaymentType,
  getTerm,
  getProgramIdForLender,
  getSecurityDeposit,
  getSelectedTier,
  getAlternateRateProgramType,
} from '@tekion/tekion-base/marketScan/readers/columnData.reader';

import FILTER_TYPES from '@tekion/tekion-components/src/organisms/filterSection/constants/filterSection.filterTypes';

import { getLendersDataForDropDown } from 'pages/desking/components/dealDetails/infoColumn/lenderPayment/utils';
import { getDealNumber } from '@tekion/tekion-base/marketScan/readers/deal.reader';
import { getSelectedLenderDetails } from '@tekion/tekion-base/marketScan/utils/desking.utils';
import CalcEngineProperties from 'utils/CalcEngineProperties';
import { LENDER_RATE_TYPES } from 'tbase/constants/retail/marketScan';
import { getNumber } from '../../../../utils';

import * as MSProgramListConstants from './msProgramList.constants';

export const getSanitizedProgram = program => {
  const {
    FullDetails: {
      LenderName: lenderName,
      BuyRate: buyRate,
      Profit: frontEndProfit,
      MaxRateMarkup: maxMarkup,
      Tiers: tiers,
      Retail,
      Description: description,
      SpecialStandard: special,
      PaidReserve: backEndProfit,
      Lease,
      Term,
    },
    Code: code,
    Payment: payment,
    Rank: rank,
    SellRate: sellRate,
    MaxAdvance: maxAdvance,
    ID: id,
  } = program || { FullDetails: EMPTY_OBJECT };
  const { IsFordFlexDeal: fordFlexDeal } = Retail || EMPTY_OBJECT;
  const { SecurityDeposit: securityDeposit } = Lease || EMPTY_OBJECT;

  return {
    lenderName,
    code,
    payment,
    sellRate,
    buyRate,
    maxMarkup,
    rank,
    maxAdvance,
    frontEndProfit,
    term: getNumber(Term),
    program,
    id,
    tiers,
    special,
    fordFlexDeal,
    description,
    backEndProfit,
    totalProfit: frontEndProfit + backEndProfit,
    securityDeposit,
  };
};

const getSanitizedProgramForGalaxy = program => {
  const {
    programId: id,
    code,
    programName,
    startDate,
    endDate,
    term,
    sellRate,
    buyRate,
    monthlyPayment: payment,
    markup,
    maxMarkup,
    securityDeposit,
    frontEndProfit,
    backEndProfit,
    totalProfit,
    maxAdvance,
    programType: special,
    creditTierItem,
    alternateProgrammeType,
    alternateProgrammeName,
  } = program;
  const { name: tiers } = creditTierItem || EMPTY_OBJECT;

  return {
    program,
    id,
    code,
    programName,
    startDate,
    endDate,
    term,
    sellRate,
    buyRate,
    payment,
    markup,
    maxMarkup,
    securityDeposit,
    frontEndProfit,
    backEndProfit,
    totalProfit,
    maxAdvance,
    special,
    tiers,
    alternateProgrammeType,
    alternateProgrammeName,
  };
};

const getSanitizedProgramForCanada = program => {
  const {
    programId: id,
    lenderName,
    programName,
    programNumber: code,
    startDate,
    endDate,
    term,
    sellRate,
    buyRate,
    programType: special,
    creditTierItem,
  } = program;
  const { name: tiers } = creditTierItem || EMPTY_OBJECT;

  return {
    lenderName, // Lender Name
    program,
    id,
    programName, // Program Name
    code, // Program Number (Program # )
    startDate, // Start Date
    endDate, // End Date
    term, // Payment term
    sellRate, // Sell Rate
    buyRate, // Buy Rate
    special, // Program Type
    tiers, // Tier
  };
};

const checkIsProgramIdMatched = (programId, programIdInColumn) => programIdInColumn && programId === programIdInColumn;

const checkIsTermMatched = (termInProgram, selectedColumnTerm) => termInProgram === getNumber(selectedColumnTerm);

const checkIsTierMatched = (tiersInProgram, selectedTierInColumn) => tiersInProgram === selectedTierInColumn?.code;

const checkIsAlternateRateProgramTypeMatched = (alternateProgrammeName, alternateRateProgramInColumn) =>
  alternateProgrammeName === alternateRateProgramInColumn;

const isProgramMatched = (
  program,
  programIdInColumn,
  selectedColumnTerm,
  selectedTierInColumn,
  alternateRateProgramInColumn,
  isCanadaDealerEnabled
) => {
  const { id, term, tiers, alternateProgrammeName } = program;
  if (CalcEngineProperties.updateByGalaxyEngine()) {
    return (
      checkIsProgramIdMatched(id, programIdInColumn) &&
      checkIsTermMatched(term, selectedColumnTerm) &&
      checkIsTierMatched(tiers, selectedTierInColumn) &&
      checkIsAlternateRateProgramTypeMatched(alternateProgrammeName, alternateRateProgramInColumn)
    );
  }
  if (isCanadaDealerEnabled) {
    return programIdInColumn && id === programIdInColumn && checkIsTermMatched(term, selectedColumnTerm);
  }
  return (
    programIdInColumn && getNumber(id) === getNumber(programIdInColumn) && checkIsTermMatched(term, selectedColumnTerm)
  );
};

export const getSanitizedInitialData = (
  columnData,
  programList,
  appliedLenderIds,
  isGalaxyEnabled,
  isCanadaDealerEnabled
) => {
  const selectedProgramId = getProgramIdForLender(columnData, appliedLenderIds?.[0]);
  const selectedColumnTerm = getTerm(columnData);
  const selectedTierInColumn = getSelectedTier(columnData);
  const securityDepositForSelectedDwnPmt = getNumber(getSecurityDeposit(columnData));
  const alternateRateProgramInColumn = getAlternateRateProgramType(columnData);

  return _reduce(
    programList,
    (acc, program) => {
      if (_isEmpty(program)) return acc;

      let sanitizedProgram;
      if (isCanadaDealerEnabled) {
        sanitizedProgram = getSanitizedProgramForCanada(program);
      } else {
        sanitizedProgram = isGalaxyEnabled ? getSanitizedProgramForGalaxy(program) : getSanitizedProgram(program);
      }

      const { securityDeposit, term } = sanitizedProgram;
      const showLenderProgramSelected = isProgramMatched(
        sanitizedProgram,
        selectedProgramId,
        selectedColumnTerm,
        selectedTierInColumn,
        alternateRateProgramInColumn,
        isCanadaDealerEnabled
      );
      const showLenderProgram =
        isGalaxyEnabled ||
        isCanadaDealerEnabled ||
        securityDepositForSelectedDwnPmt === 0 ||
        getNumber(securityDeposit) > 0;
      // For Galaxy & Canada, show lender program irrespective of security deposit value

      if (!acc.terms[term]) acc.terms[term] = true;

      if (showLenderProgramSelected) {
        acc.data = [sanitizedProgram, ...acc.data];
        acc.selectedRow = sanitizedProgram;
      } else if (showLenderProgram) {
        acc.data.push(sanitizedProgram);
      }

      return acc;
    },
    { data: [], selectedRow: null, terms: {} }
  );
};

export const getTableProps = defaultMemoize(
  (loading, totalNumberOfEntries, page, resultsPerPage, selectedRow, getTrProps) => ({
    rowStyle: { cursor: 'pointer' },
    totalNumberOfEntries,
    showPagination: true,
    currentPage: page,
    loading,
    pageSize: resultsPerPage,
    selectedRow,
    getTrProps,
    displayFirstLast: false,
  })
);

export const getTermsOptions = defaultMemoize(terms =>
  [MSProgramListConstants.ALL_OPTION].concat(_map(terms, term => ({ value: term, label: term })))
);

export const getLenderOptions = defaultMemoize((lenders, paymentType, isGalaxyEnabled, isCanadaDealerEnabled) => {
  const lenderOptions = getLendersDataForDropDown(lenders, paymentType);
  return isGalaxyEnabled || isCanadaDealerEnabled
    ? lenderOptions
    : [MSProgramListConstants.ALL_OPTION, ...lenderOptions];
});

export const getTierDetails = (selectedTiers, lenders, appliedLenderIds) => {
  const allTiers = getTiersOfSelectedLender(lenders, appliedLenderIds);

  return _reduce(
    selectedTiers,
    (acc, tier) => {
      const tierDetail = _find(allTiers, ['code', tier]);
      return [...acc, tierDetail];
    },
    []
  );
};

const getTiersOfSelectedLender = (lenders, appliedLenderIds) => {
  const lenderId = appliedLenderIds?.[0];
  const lenderInfo = getSelectedLenderDetails(lenders, lenderId) || EMPTY_OBJECT;
  return _get(lenderInfo, 'tiers');
};

export const getTierOptions = defaultMemoize((lenders, appliedLenderIds) => {
  const allTiers = getTiersOfSelectedLender(lenders, appliedLenderIds);
  return _map(allTiers, ({ code, name }) => ({ value: code, label: name }));
});

export const getApiLenderWithSelectedLenderCode = (lenders, lendersFromApi, appliedLenderIds) => {
  const lenderId = appliedLenderIds?.[0];
  const lenderInfo = getSelectedLenderDetails(lenders, lenderId) || EMPTY_OBJECT;

  return _find(lendersFromApi, lender => {
    const lenderCodes = lender?.code;
    return _some(lenderCodes, lenderCode => lenderCode?.code === lenderInfo?.code);
  });
};

export const getAlternateRateProgramTypeDisplayVal = (
  lenders,
  appliedLenderIds,
  alternateProgrammeType,
  lendersFromApi
) => {
  const matchedLender = getApiLenderWithSelectedLenderCode(lenders, lendersFromApi, appliedLenderIds);
  const alternateRateProgramTypeOptions = _get(matchedLender, 'alternateProgramTypes');
  const program = _find(
    alternateRateProgramTypeOptions,
    alternateRateProgramTypeOption => alternateRateProgramTypeOption?.key === alternateProgrammeType
  );

  return _get(program, 'displayName');
};

export const getAlternateRateProgramTypeOptions = defaultMemoize((lenders, appliedLenderIds, lendersFromApi) => {
  const matchedLender = getApiLenderWithSelectedLenderCode(lenders, lendersFromApi, appliedLenderIds);
  const programTypes = _get(matchedLender, 'alternateProgramTypes');

  return _map(programTypes, alternateProgram => ({
    value: alternateProgram?.key,
    label: alternateProgram?.displayName,
  }));
});

const getFilterTypes = defaultMemoize((termsOptions, lenderOptions) => [
  {
    id: MSProgramListConstants.SELECT_PROGRAM_COLUMNS.LENDER_NAME,
    name: __('Lender'),
    type: FILTER_TYPES.MULTI_SELECT,
    additional: { options: lenderOptions },
  },
  {
    id: MSProgramListConstants.SELECT_PROGRAM_COLUMNS.PROGRAM_TYPE,
    name: __('Program Type'),
    type: FILTER_TYPES.SINGLE_SELECT,
    additional: { options: MSProgramListConstants.PROGRAM_TYPE_FILTER_OPTIONS },
  },
  {
    id: MSProgramListConstants.SELECT_PROGRAM_COLUMNS.TERM,
    name: __('Terms'),
    type: FILTER_TYPES.MULTI_SELECT,
    additional: { options: termsOptions },
  },
]);

const getFilterTypesForCanada = defaultMemoize((termsOptions, lenderOptions) => [
  {
    id: MSProgramListConstants.SELECT_PROGRAM_COLUMNS.LENDER_NAME,
    name: __('Lender'),
    type: FILTER_TYPES.SINGLE_SELECT,
    additional: { options: lenderOptions },
  },
  {
    id: MSProgramListConstants.SELECT_PROGRAM_COLUMNS.PROGRAM_TYPE,
    name: __('Program Type'),
    type: FILTER_TYPES.SINGLE_SELECT,
    additional: { options: MSProgramListConstants.PROGRAM_TYPE_FILTER_OPTIONS_CANADA },
  },
  {
    id: MSProgramListConstants.SELECT_PROGRAM_COLUMNS.TERM,
    name: __('Terms'),
    type: FILTER_TYPES.MULTI_SELECT,
    additional: { options: termsOptions },
  },
]);

const getFilterTypesForGalaxy = defaultMemoize((lenderOptions, tierOptions, alternateRateProgramTypeOptions) => [
  {
    id: MSProgramListConstants.SELECT_PROGRAM_COLUMNS.LENDER_NAME,
    name: __('Lender'),
    type: FILTER_TYPES.SINGLE_SELECT,
    additional: { options: lenderOptions },
  },
  {
    id: MSProgramListConstants.SELECT_PROGRAM_COLUMNS.TIER,
    name: __('Tier'),
    type: FILTER_TYPES.MULTI_SELECT,
    additional: { options: tierOptions },
  },
  {
    id: MSProgramListConstants.SELECT_PROGRAM_COLUMNS.PROGRAM_TYPE,
    name: __('Program Type'),
    type: FILTER_TYPES.SINGLE_SELECT,
    additional: { options: MSProgramListConstants.PROGRAM_TYPE_FILTER_OPTIONS_GALAXY },
  },
  {
    id: MSProgramListConstants.SELECT_PROGRAM_COLUMNS.ALTERNATE_PROGRAM_TYPE,
    name: __('Alternate Rate Program'),
    type: FILTER_TYPES.MULTI_SELECT,
    additional: { options: alternateRateProgramTypeOptions },
  },
]);

export const getFilterPropsForGalaxy = defaultMemoize(
  (selectedFilters, lenderOptions, tierOptions, alternateRateProgramTypeOptions) => ({
    showFilterTrigger: false,
    alwaysRenderDefaultToolBar: true,
    showReset: false,
    filterTypes: getFilterTypesForGalaxy(lenderOptions, tierOptions, alternateRateProgramTypeOptions),
    defaultFilterTypes: MSProgramListConstants.DEFAULT_FILTER_TYPES_GALAXY,
    selectedFilters,
    isFilterLabelStacked: true,
  })
);

export const getFilterPropsForCanada = defaultMemoize((selectedFilters, termsOptions, lenderOptions) => ({
  showFilterTrigger: false,
  alwaysRenderDefaultToolBar: true,
  showReset: false,
  filterTypes: getFilterTypesForCanada(termsOptions, lenderOptions),
  defaultFilterTypes: MSProgramListConstants.DEFAULT_FILTER_TYPES,
  selectedFilters,
}));

export const getFilterProps = defaultMemoize((selectedFilters, termsOptions, lenderOptions) => ({
  showFilterTrigger: false,
  alwaysRenderDefaultToolBar: true,
  showReset: false,
  filterTypes: getFilterTypes(termsOptions, lenderOptions),
  defaultFilterTypes: MSProgramListConstants.DEFAULT_FILTER_TYPES,
  selectedFilters,
}));

const getAllTiersCodeOfSelectedLender = selectedLenderTiers => _map(selectedLenderTiers, tier => _get(tier, 'code'));

export const getDefaultTierCode = ({ columnData, lenders, selectedLenderId }) => {
  const selectedTierInColumn = getSelectedTier(columnData);
  const selectedLenderTiers = getTiersOfSelectedLender(lenders, _castArray(selectedLenderId)) || EMPTY_ARRAY;
  if (_isEmpty(selectedTierInColumn)) {
    return getAllTiersCodeOfSelectedLender(selectedLenderTiers);
  }
  return _compact(_castArray(_get(selectedTierInColumn, 'code')));
};

const getDefaultAlternateRateProgramType = columnData => {
  const alternateRateProgramType = getAlternateRateProgramType(columnData);
  return _compact(_castArray(alternateRateProgramType));
};

const getInitialSelectedFiltersForGalaxy = (selectedLenderId, columnData, lenders) => {
  const defaultTierCode = getDefaultTierCode({ columnData, lenders, selectedLenderId });
  const defaultAlternateProgramType = getDefaultAlternateRateProgramType(columnData);

  const initialFilters = [
    {
      operator: FILTER_OPERATORS.IN,
      type: MSProgramListConstants.SELECT_PROGRAM_COLUMNS.LENDER_NAME,
      values: [selectedLenderId],
    },
    {
      operator: FILTER_OPERATORS.IN,
      type: MSProgramListConstants.SELECT_PROGRAM_COLUMNS.TIER,
      values: defaultTierCode,
    },
    {
      operator: FILTER_OPERATORS.IN,
      type: MSProgramListConstants.SELECT_PROGRAM_COLUMNS.PROGRAM_TYPE,
      values: [MSProgramListConstants.ALL_VALUE],
    },
    {
      operator: FILTER_OPERATORS.IN,
      type: MSProgramListConstants.SELECT_PROGRAM_COLUMNS.ALTERNATE_PROGRAM_TYPE,
      values: defaultAlternateProgramType,
    },
  ];
  return initialFilters;
};

export const getInitialSelectedFilters = (selectedLenderId, columnData, lenders) => {
  if (CalcEngineProperties.updateByGalaxyEngine()) {
    return getInitialSelectedFiltersForGalaxy(selectedLenderId, columnData, lenders);
  }
  const initialFilters = [
    {
      operator: FILTER_OPERATORS.IN,
      type: MSProgramListConstants.SELECT_PROGRAM_COLUMNS.LENDER_NAME,
      values: [selectedLenderId],
    },
    {
      operator: FILTER_OPERATORS.IN,
      type: MSProgramListConstants.SELECT_PROGRAM_COLUMNS.PROGRAM_TYPE,
      values: [MSProgramListConstants.ALL_VALUE],
    },
    {
      operator: FILTER_OPERATORS.IN,
      type: MSProgramListConstants.SELECT_PROGRAM_COLUMNS.TERM,
      values: [],
    },
  ];
  return initialFilters;
};

export const getSanitizedMscanPayload = (columnData, mscanPayload, codeNames) =>
  produce(mscanPayload, draft => {
    delete draft?.Filter?.IncludeOnlyTerm;
    delete draft?.Filter?.ProgramID;

    if (isLoanOrCashOrBalloonPaymentType(columnData)) {
      delete draft?.Filter?.Retail?.Terms;
      delete draft?.RetailPart?.UseOnlyLender;
      _set(draft, 'RetailPart.UseOnlyLenderCode', codeNames);
      _set(draft, 'Filter.Retail.IncludeFordFlex', true);
    }

    if (isLeaseOrOPLeasePaymentType(columnData)) {
      delete draft?.Filter?.Lease?.Terms;
      delete draft?.LeasePart?.UseOnlyLender;
      _set(draft, 'LeasePart.UseOnlyLenderCode', codeNames);
    }

    if (isBalloonPaymentType(columnData)) {
      delete draft?.LeasePart?.UseOnlyLender;
    }
  });

export const matchRow = defaultMemoize((selectedRow, rowInfo) =>
  _every(_keys(selectedRow), key => selectedRow[key] === rowInfo?.original?.[key])
);

const programTypeFilterPassed = (appliedFilterValue, special) => {
  if (appliedFilterValue === MSProgramListConstants.ALL_VALUE) return true;

  return special === appliedFilterValue;
};

const termsFilterPassed = (appliedFilterValues, term) => {
  if (appliedFilterValues.includes(MSProgramListConstants.ALL_VALUE)) return true;

  return !!_find(appliedFilterValues, value => getNumber(value) === term);
};

const isSearchTaxFilterPassed = (searchText, program, isGalaxyEnabled, isCanadaDealerEnabled) => {
  const { lenderName, code } = program;
  if (isGalaxyEnabled) {
    return _reduce(
      MSProgramListConstants.SEARCHABLE_TABLE_COLUMNS_GALAXY,
      (acc, key) => {
        const value = _get(program, key);
        if (_includes(_toString(value).toLowerCase(), searchText)) {
          return true;
        }
        return acc;
      },
      false
    );
  }

  if (isCanadaDealerEnabled) {
    return _reduce(
      MSProgramListConstants.SEARCHABLE_TABLE_COLUMNS_CANADA,
      (acc, key) => {
        const value = _get(program, key);
        if (_includes(_toString(value).toLowerCase(), searchText)) {
          return true;
        }
        return acc;
      },
      false
    );
  }

  return lenderName.toLowerCase().includes(searchText) || code.toLowerCase().includes(searchText);
};

export const getAppliedFilteredData = (
  data,
  programTypeFilter,
  termsFilter,
  modifiedSearchText,
  isGalaxyEnabled,
  isCanadaDealerEnabled
) =>
  _filter(data, program => {
    const { special, term } = program;

    if (programTypeFilter) {
      if (!programTypeFilterPassed(programTypeFilter.values[0], special)) return false;
    }

    if (!isGalaxyEnabled && termsFilter) {
      if (!termsFilterPassed(termsFilter.values, term, isGalaxyEnabled)) return false;
    }

    if (modifiedSearchText) {
      return isSearchTaxFilterPassed(modifiedSearchText, program, isGalaxyEnabled, isCanadaDealerEnabled);
    }

    return true;
  });

export const getAppliedFilterOfType = (appliedFilters, targetType) =>
  _find(appliedFilters, ({ type }) => type === targetType);

export const getLenderCodeNames = (lenders, appliedLenderIds) =>
  _reduce(
    appliedLenderIds,
    (acc, id) => {
      if (id !== MSProgramListConstants.ALL_VALUE) {
        const { codeName } = _find(lenders, ({ id: lenderId }) => id === lenderId) || EMPTY_OBJECT;
        if (codeName) {
          acc.push(codeName);
        }
      }

      return acc;
    },
    []
  );

export const getAppliedTermFilterValues = (columnData, sortedTerms) => {
  const selectedTerm = getTerm(columnData);
  const isTermMatched = sortedTerms.find(term => term === `${selectedTerm}`);
  if (isTermMatched) {
    return selectedTerm;
  }
  return [MSProgramListConstants.ALL_VALUE, ...sortedTerms];
};

export const getInitialTermFilter = (selectedFilters, selectedTerm) =>
  selectedFilters.map(filter => {
    if (filter.type === MSProgramListConstants.SELECT_PROGRAM_COLUMNS.TERM)
      return { ...filter, values: Array.isArray(selectedTerm) ? selectedTerm : [`${selectedTerm}`] };
    return filter;
  });

const getAllFilterValuesMap = (termFilterValues, lenderFilterValues) => ({
  [MSProgramListConstants.SELECT_PROGRAM_COLUMNS.TERM]: termFilterValues,
  [MSProgramListConstants.SELECT_PROGRAM_COLUMNS.LENDER_NAME]: lenderFilterValues,
});

const getDefaultTiersOnLenderChange = (appliedFilters, lenders) => {
  const selectedLenderId = getAppliedFilterOfType(
    appliedFilters,
    MSProgramListConstants.SELECT_PROGRAM_COLUMNS.LENDER_NAME
  )?.values;
  return getDefaultTierCode({ lenders, selectedLenderId });
};

export const updateLenderDependentFilters = (appliedFilters, sanitizedSelectedFilters, lenders) => {
  const filterType = MSProgramListConstants.SELECT_PROGRAM_COLUMNS.LENDER_NAME;
  const { additional } = getAppliedFilterOfType(appliedFilters, filterType) || EMPTY_OBJECT;

  if (additional?.eventDetails?.action) {
    const defaultTierCode = getDefaultTiersOnLenderChange(appliedFilters, lenders) || EMPTY_ARRAY;

    return produce(sanitizedSelectedFilters, draft => {
      const tiersFilterIndex = _findIndex(draft, ['type', MSProgramListConstants.SELECT_PROGRAM_COLUMNS.TIER]);
      const alternateRateProgramTypeFilterIndex = _findIndex(draft, [
        'type',
        MSProgramListConstants.SELECT_PROGRAM_COLUMNS.ALTERNATE_PROGRAM_TYPE,
      ]);

      if (tiersFilterIndex !== -1) {
        _set(draft[tiersFilterIndex], 'values', defaultTierCode);
        _set(draft[tiersFilterIndex], 'additional', null);
      }
      if (alternateRateProgramTypeFilterIndex !== -1) {
        _set(draft[alternateRateProgramTypeFilterIndex], 'values', []);
        _set(draft[alternateRateProgramTypeFilterIndex], 'additional', null);
      }
    });
  }
  return sanitizedSelectedFilters;
};

const sanitizeAppliedFilters = allFilterValuesMap => (targetFilter, filter) =>
  produce(filter, draft => {
    let { values } = filter;
    const { additional } = filter;
    const { action, option } = additional?.eventDetails || EMPTY_OBJECT;
    const isTargetFilterOptionSelected = action === MSProgramListConstants.SELECT_EVENT_TYPES.SELECT;
    const isTargetFilterOptionDeselected = action === MSProgramListConstants.SELECT_EVENT_TYPES.DESELECT;
    const optionAllClicked = option?.value === MSProgramListConstants.ALL_VALUE;

    if (isTargetFilterOptionSelected || isTargetFilterOptionDeselected) {
      if (isTargetFilterOptionSelected && optionAllClicked) {
        values = [MSProgramListConstants.ALL_VALUE, ...allFilterValuesMap[targetFilter]];
      } else if (!optionAllClicked) {
        values = _filter(values, val => val !== MSProgramListConstants.ALL_VALUE);
      } else {
        values = [];
      }

      _set(draft, 'values', values);
      _set(draft, 'additional', null); // additional from prev filter selection is persisted, so we need to remove
    }
  });

const getSanitizedFiltersForGalaxy = appliedFilters =>
  _map(
    appliedFilters,
    filter => ({ ...filter, additional: null }) // additional from prev filter selection is persisted, so we need to remove
  );

export const getSanitizedFilters = (appliedFilters, termFilterValues, lenderFilterValues) => {
  if (CalcEngineProperties.updateByGalaxyEngine()) {
    return getSanitizedFiltersForGalaxy(appliedFilters);
  }

  const getSanitizedTermsOrLenderFilters = sanitizeAppliedFilters(
    getAllFilterValuesMap(termFilterValues, lenderFilterValues)
  );

  return appliedFilters.map(filter => {
    const { type } = filter;

    switch (type) {
      case MSProgramListConstants.SELECT_PROGRAM_COLUMNS.TERM:
      case MSProgramListConstants.SELECT_PROGRAM_COLUMNS.LENDER_NAME:
        return getSanitizedTermsOrLenderFilters(type, filter);

      case MSProgramListConstants.SELECT_PROGRAM_COLUMNS.PROGRAM_TYPE:
        return { ...filter, additional: null }; // additional from prev filter selection is persisted, so we need to remove

      default:
        return filter;
    }
  });
};

export const modifyColumnData = columnData => {
  const { dealVehicleId } = columnData;

  const modifiedColumns = [];
  MSProgramListConstants.FORD_GENERIC_CALL_DATA.forEach(item => {
    const newColumnId = generateColumnId(
      {
        paymentType: getPaymentType(columnData),
        value: item.term,
      },
      dealVehicleId
    );
    const newColumnData = produce(columnData, draft => {
      _set(draft, 'id', newColumnId);
      _set(draft, 'paymentOption.value', item.term);
      // not actual keys just used here as it will be needed in getSanitizedMscanPayloadForFordGeneric
      _set(draft, 'fordFlexPCT', item.percentage);
      _set(draft, 'FordFlexFirstTerm', item.FordFlexFirstTerm);
    });

    modifiedColumns.push(newColumnData);
  });
  return modifiedColumns;
};
export const getSanitizedMscanPayloadForFordGeneric = (columnData, mscanPayload) =>
  produce(mscanPayload, draft => {
    if (isLoanOrCashOrBalloonPaymentType(columnData)) {
      _set(draft, 'RetailPart.UseOnlyLenderCode', ['Ford Motor Credit']);
      _set(draft, 'Filter.Retail.IncludeFordFlex', true);
      _set(draft, 'RetailPart.Generic.FordFlexFirstTerm', columnData?.FordFlexFirstTerm);
      _set(draft, 'RetailPart.Generic.FordFlexDecreasePct', columnData?.fordFlexPCT);
      _set(draft, 'RetailPart.Generic.TreatTermAsFordFlex', [columnData?.paymentOption?.value]);
    }
  });

export const getLenderProgramsPayloadGalaxy = (
  deal,
  columnData,
  codeNames,
  selectedLenderIds,
  alternateProgrammeTypes,
  selectedTiersDetail
) => {
  const dealNumber = getDealNumber(deal);
  const dealPaymentId = _get(columnData, 'id');

  return {
    dealNumber,
    dealPaymentId,
    lenderId: selectedLenderIds?.[0],
    lender: codeNames?.[0],
    alternateProgrammeTypes,
    tiers: selectedTiersDetail,
  };
};

export const getLenderProgramsPayloadCanada = (deal, columnData, selectedLenderIds) => {
  const dealNumber = getDealNumber(deal);
  const selectedDealPaymentId = _get(columnData, 'id');
  return {
    dealNumber,
    selectedDealPaymentId,
    selectedLenderIds,
  };
};

export const getBuyOrSellRate = (rowData, accessor, rateType) => {
  if (rateType === LENDER_RATE_TYPES.MF) {
    if (accessor === MSProgramListConstants.SELECT_PROGRAM_COLUMNS.SELL_RATE) {
      return _get(rowData, 'program.dealInterestRateMF.sellRate');
    }
    return _get(rowData, 'program.dealInterestRateMF.buyRate');
  }

  if (accessor === MSProgramListConstants.SELECT_PROGRAM_COLUMNS.SELL_RATE) {
    return _get(rowData, 'program.dealInterestRateAPR.sellRate');
  }
  return _get(rowData, 'program.dealInterestRateAPR.buyRate');
};

export const isFordFlexProgram = program => {
  if (!program || !program.FullDetails || !program.FullDetails.Retail) return false;
  return program.FullDetails.Retail.IsFordFlexDeal === true;
};

export const shouldMakeFordGenericCall = (programs, selectedLenderCodeNames) => {
  // If Ford Motor Credit is not in the selected lenders, no need for generic call
  if (!selectedLenderCodeNames.includes('Ford Motor Credit')) return false;

  // If we already have Ford Flex programs from the non-generic call, no need for generic call
  return !programs.some(program => isFordFlexProgram(program));
};
