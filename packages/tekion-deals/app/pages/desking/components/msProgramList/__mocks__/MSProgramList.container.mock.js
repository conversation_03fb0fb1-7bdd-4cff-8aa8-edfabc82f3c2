export const mockV2TermsData = {
  data: [
    {
      Programs: [
        {
          AmountFinanced: 35386,
          BuyRate: 3.35,
          Code: 'HMF_K81A',
          ID: *********,
          InceptionFeesTaxes: 386.0,
          Lender: 'HMF',
          MaxAdvance: 483.0,
          MaxCreditScore: 1000.0,
          Payment: '1035.98',
          Price: 35000,
          Rank: 1,
          SellRate: 3.35,
          PaidReserve: 300.0,
          FullDetails: {
            aprlmf: 3.35,
            aprlmftype: 0.0,
            msrp: 100,
            msrpbase: 100.0,
            APRLMF: 3.35,
            APRLMFType: 0.0,
            AdjustedSellingPrice: 35000,
            AmountFinanced: 35386.0,
            BuyRate: 3.35,
            CashDeficiency: 0.0,
            CurrentMileage: 123.0,
            CustomerCash: 0.0,
            DaysToFirstPayment: 45.0,
            DealerIncentive: 0.0,
            Description:
              'HMF_K81A : Hyundai Motor Finance (Standard Program) (H760) Flat Fee\r\nNon-Branded - Tier 1 - Credit Score 740+ - Verify Eligibility - NCA',
            DocStampTax: 0.0,
            DocStampTaxRate: 0.0,
            Downpayment: 0.0,
            DownpaymentFromCash: 0.0,
            DownpaymentFromEquity: 0,
            DownpaymentFromRebate: 0.0,
            EquipmentRecap: {},
            FinanceCharge: 1909.28,
            InventoryTax: 0.0,
            InventoryTaxRate: 0.0,
            Lender: 'HMF',
            LenderName: 'Hyundai Motor Finance',
            MSRP: 100,
            MSRPBase: 100.0,
            MonthlyPayment: 1035.98,
            MonthlyPaymentBeforeTax: 1035.98,
            NonCCRRebate: 0.0,
            NumberOfAdvancePayments: 0.0,
            NumberOfPayments: 36.0,
            PaidReserve: 300.0,
            PriceAdjustment: 0.0,
            Profit: 35200.0,
            RateMarket: 4.0,
            RegistrationFee: 23.0,
            Rejections: [0, 7],
            Retail: {
              CustomFee: [],
              TotalTaxesFees: 386.0,
              Participation: 0.0,
            },
            SalesTax: 0.0,
            CountyTax: 0.0,
            CountyTaxRate: 0.0,
            CityTax: 0.0,
            CityTaxRate: 0.0,
            StateTax: 0.0,
            StateTaxRate: 0.0,
            SellingPrice: 35000.0,
            SpecialStandard: false,
            State: 'CA',
            TaxRate: 0.0,
            Term: 36.0,
            Tiers: '1',
            TradeACV: 0,
            TradeAllowance: 0,
            TradeEquity: 0,
            TradePayoff: 0,
            TransactionType: 1.0,
            WarrantyCharge: 0.0,
            WasteTireFee: 0.0,
            MaxRateMarkup: 0.0,
            PaidReserveIsCapCostParticipation: false,
            PaidReserveIsFlatFee: true,
            RateMarkupIsIncremental: false,
            RegistrationItem: [],
            SupplementalTitleFee: 0.0,
          },
        },
      ],
      Term: 36,
    },
  ],
};

export const loanColumnData = { paymentOption: { paymentType: 'LOAN' } };

export const leaseColumnData = { paymentOption: { paymentType: 'LEASE' } };

export const sampleCodeName = 'Mercedes Benz Financial Services';

export const sampleRetailMscanPayload = {
  ScanType: 1,
  Run: 3,
  UseConfig: {},
  Filter: {
    Retail: {
      Terms: [18],
      DontIncludeSecurityDeposits: false,
      DontIncludeAcquisitionFeeWaived: true,
      IncludeMultipleSecurityDeposits: true,
      IncludeBalloonNotes: false,
      BalloonNotesOnly: false,
    },
    IncludeOnlyTerm: 18,
    IncludePastAdvance: true,
  },
  PaymentSchedule: {
    Type: 0,
  },
  lender: '619e6bee2ab79c00069c917a',
  IncludeScanResultField: {
    FullProgramDetails: true,
  },
  Engine: 'MARSE',
  RetailPart: {
    UseOnlyLenderCode: ['Mercedes Benz Financial Services'],
    IgnoreMaxAPR: true,
    FlatTax: 0,
    Override: [],
    JoinRebateOverride: false,
    RebateOverride: {},
    CustomFee: [],
    UseOnlyLender: [
      {
        Name: 'Mercedes Benz Financial Services',
        Tier: [
          {
            Code: '1',
            Name: 'Tier 1',
            Type: 1,
          },
        ],
      },
    ],
    ApplyUsedVehicleTaxRegistration: false,
  },
  Id: 'LOAN60_fbc9c1ca-4ead-4bf8-89d9-205be54b0afc_43c0b29b-86d3-44ff-b41d-99aa0818c3b7',
  DownPayments: [0, 0, 0],
  DaysToFirstPayment: 50,
  DesiredValue: 72500,
  Vehicle: {
    TotalDealerCost: 71145,
    TotalMSRP: 71145,
    BaseMSRPAmount: 71145,
    CurrentMileage: 5,
    ID: '100392',
    Year: 2021,
  },
  Location: {
    CustomerZIP: 11570,
    DealershipZIP: '11702',
    TaxInCity: true,
    TaxRegionID: '453102',
  },
  CreditScore: 0,
  Market: '31',
  RateMarket: 31,
  Customer: {
    DateOfBirth: '1980-01-01T13:51:24.203Z',
  },
  OutOfStateFeeTax: {
    Retail: {
      SalesUseTaxPct: 0,
    },
  },
  StateFeeTax: {},
  LeaseOverride: [],
  RegistrationDate: '2022-01-03T14:53:00.852Z',
  LeasePart: {
    DownpaymentTaxInCap: false,
  },
};

export const sampleLeaseMscanPayload = {
  ScanType: 1,
  Run: 1,
  UseConfig: {},
  Filter: {
    Lease: {
      Terms: [40],
      DontIncludeSecurityDeposits: false,
      DontIncludeAcquisitionFeeWaived: true,
      IncludeMultipleSecurityDeposits: true,
      IncludeBalloonNotes: false,
      BalloonNotesOnly: false,
    },
    IncludeOnlyTerm: 40,
    IncludePastAdvance: true,
    ProgramID: '66007600',
  },
  PaymentSchedule: {
    Type: 0,
  },
  lender: '619e6bee2ab79c00069c917a',
  IncludeScanResultField: {
    FullProgramDetails: true,
  },
  Engine: 'MARSE',
  LeasePart: {
    IncludeExpired: true,
    CCRCashTaxCollectionMethod: 1,
    CCRRebateTaxCollectionMethod: 1,
    CCRTradeInTaxCollectionMethod: 1,
    UseOnlyLenderCode: ['Mercedes Benz Financial Services'],
    IncludeDeficiencyInProfit: false,
    RegistrationFeeInCap: false,
    AnnualMileage: 20002,
    RemoveDemoMileageFromAnnualMileage: true,
    JoinRebateOverride: false,
    RebateOverride: {},
    CustomFee: [],
    BankFeeUpfront: false,
    RebateCanPayTrade: false,
    TreatRebateAsCCR: true,
    TreatTradeInAsCCR: true,
    CashCanPayTrade: false,
    CashDoesntCoverInceptionFee: true,
    TreatCashAsCCR: true,
    SpreadParticipationOverride: [],
    DontApplyFirstPaymentWaiver: true,
    DownpaymentTaxInCap: null,
    UseOnlyLender: [
      {
        Name: 'Mercedes Benz Financial Services',
        Tier: [
          {
            Code: '1',
            Name: 'Tier 1',
            Type: 0,
          },
        ],
      },
    ],
    ApplyUsedVehicleTaxRegistration: false,
  },
  Id: 'LEASE40_b4b4b328-a182-4442-98e1-5000cff9b297_67b95f3f-d701-4214-9c9c-45c57b2e172c',
  DownPayments: [0, 0, 0],
  DaysToFirstPayment: 30,
  DesiredValue: 72500,
  Vehicle: {
    TotalDealerCost: 71145,
    TotalMSRP: 71145,
    BaseMSRPAmount: 71145,
    CurrentMileage: 5,
    ID: '100392',
    Year: 2021,
  },
  Location: {
    CustomerZIP: 20005,
    DealershipZIP: '11702',
    TaxInCity: true,
    TaxRegionID: '458618',
  },
  CreditScore: 0,
  Market: '50',
  RateMarket: 31,
  VehicleEquipment: [],
  RegistrationDate: '2022-01-03T14:53:00.852Z',
};

export const sanitizedMscanPayloadFilter = {
  Retail: {
    DontIncludeSecurityDeposits: false,
    DontIncludeAcquisitionFeeWaived: true,
    IncludeMultipleSecurityDeposits: true,
    IncludeBalloonNotes: false,
    BalloonNotesOnly: false,
  },
  IncludePastAdvance: true,
};

export const sanitizedLeaseMscanPayloadFilter = {
  Lease: {
    DontIncludeSecurityDeposits: false,
    DontIncludeAcquisitionFeeWaived: true,
    IncludeMultipleSecurityDeposits: true,
    IncludeBalloonNotes: false,
    BalloonNotesOnly: false,
  },
  IncludePastAdvance: true,
};

export const leasePart = {
  UseOnlyLenderCode: ['Mercedes Benz Financial Services'],
  AnnualMileage: 20002,
  RemoveDemoMileageFromAnnualMileage: true,
};

export const retailPart = {
  UseOnlyLenderCode: ['Mercedes Benz Financial Services'],
};

export const sampleMscanProgram = {
  FullDetails: {
    LenderName: 'Mercedes Benz Financial Services',
    BuyRate: 2.14,
    Profit: 1355,
    MaxRateMarkup: 2.5,
    Tiers: 'FCA',
    Retail: { IsFordFlexDeal: true },
    Description: 'description',
    SpecialStandard: true,
    PaidReserve: 45,
    Lease: { SecurityDeposit: 10 },
    Term: 12.0,
  },
  Code: 'MBC_050A',
  Payment: 5969.97,
  Rank: 1,
  SellRate: 2.14,
  MaxAdvance: 97486,
  ID: 1,
};

export const sampleRowData = {
  lenderName: sampleMscanProgram.FullDetails.LenderName,
  code: sampleMscanProgram.Code,
  payment: sampleMscanProgram.Payment,
  sellRate: sampleMscanProgram.SellRate,
  buyRate: sampleMscanProgram.FullDetails.BuyRate,
  maxMarkup: sampleMscanProgram.FullDetails.MaxRateMarkup,
  rank: sampleMscanProgram.Rank,
  maxAdvance: sampleMscanProgram.MaxAdvance,
  frontEndProfit: sampleMscanProgram.FullDetails.Profit,
  backEndProfit: sampleMscanProgram.FullDetails.PaidReserve,
  totalProfit: sampleMscanProgram.FullDetails.Profit + sampleMscanProgram.FullDetails.PaidReserve,
  term: Number(sampleMscanProgram.FullDetails.Term),
  program: sampleMscanProgram,
  id: sampleMscanProgram.ID,
  tiers: sampleMscanProgram.FullDetails.Tiers,
  special: sampleMscanProgram.FullDetails.SpecialStandard,
  fordFlexDeal: sampleMscanProgram.FullDetails.Retail.IsFordFlexDeal,
  description: sampleMscanProgram.FullDetails.Description,
  securityDeposit: sampleMscanProgram.FullDetails.Lease.SecurityDeposit,
};

export const sanitizedInitialData = {};

export const lenders = [
  {
    code: 'ACUFS',
    label: 'ACUFS',
    feeMarkup: null,
    financeReserveMethods: [
      {
        paymentType: 'LOAN',
        reserveType: 'MARKUP',
        value: null,
      },
      {
        paymentType: 'LEASE',
        reserveType: 'MARKUP',
        value: null,
      },
    ],
    actualPenaltyPerMile: null,
    penaltyPerMile: 0,
    bankFeeUpfront: 'capped',
    address: {
      address: null,
      address2: null,
      address3: null,
      address4: null,
      state: null,
      city: null,
      county: null,
      zipCode: null,
      phoneNumber: null,
      country: null,
    },
    lienHolderAddress: {
      address: null,
      address2: null,
      address3: null,
      address4: null,
      state: null,
      city: null,
      county: null,
      zipCode: null,
      phoneNumber: null,
      country: null,
    },
    sameAsLender: true,
    preferred: true,
    id: '651fbda57ea1f4324d3571d6',
    loanSupported: true,
    leaseSupported: true,
    paymentTypeSupported: ['LOAN', 'LEASE', 'ONE_TIME_LEASE', 'BALLOON'],
    dispositionFee: null,
    dispositionThresholdValue: null,
    terminationThresholdValue: null,
    financeReserveSplitPercentage: 0,
    codeName: 'ACUFS',
    marketScanCodes: [
      {
        Code: 'ACUFS',
        Type: 0,
      },
    ],
    defaultLender: true,
    legalName: 'Acura Financial Services',
    marketScan: true,
    hidden: false,
    financeReserve: null,
    enableTxFinanceCharge: false,
    aprMFType: 0,
    loanMaxMarkup: null,
    leaseMaxMarkup: 4,
    onePayMaxMarkup: null,
    removeDemoMileageFromAnnualMileage: false,
    removeDemoMileageFromAnnualMileageDeciderValue: 0,
    removeDemoMileageFromAnnualMileageDisplayName: null,
    freeMilesforResidualAdjustment: 0,
    onePay2DigitsAfterCommaMonthlyPayment: false,
    firstPaymentWaivedDefaultValue: true,
    acquisitionFeeMarkupAmt: null,
    useMPEqDispFeeWhenMpLtDispFee: false,
    securityDepositWaiverReason: 'LEASE_LOYALTY',
    includeSecurityDeposits: false,
    collectSecurityDepositWaiverReason: true,
    tiers: [
      {
        code: 'Tier 1',
        type: null,
        name: 'Tier 1',
      },
      {
        code: 'Tier 2',
        type: null,
        name: 'Tier 2',
      },
      {
        code: 'Tier 3',
        type: null,
        name: 'Tier 3',
      },
      {
        code: 'Tier 4',
        type: null,
        name: 'Tier 4',
      },
      {
        code: 'Tier 5',
        type: null,
        name: 'Tier 5',
      },
      {
        code: 'Tier 6',
        type: null,
        name: 'Tier 6',
      },
      {
        code: 'Tier 7',
        type: null,
        name: 'Tier 7',
      },
      {
        code: 'Tier 8',
        type: null,
        name: 'Tier 8',
      },
      {
        code: 'Tier 9',
        type: null,
        name: 'Tier 9',
      },
      {
        code: 'Tier 10',
        type: null,
        name: 'Tier 10',
      },
      {
        code: 'Tier 11',
        type: null,
        name: 'Tier 11',
      },
      {
        code: 'Tier 12',
        type: null,
        name: 'Tier 12',
      },
      {
        code: 'Tier 13',
        type: null,
        name: 'Tier 13',
      },
      {
        code: 'Tier 14',
        type: null,
        name: 'Tier 14',
      },
    ],
    roundOffMileageToNearestThousand: true,
    vehicleMileCutOffForRemoveDemoMileage: 0,
    personalPropertyTaxLenderType: 0,
    totalNumberOfSecurityDeposits: null,
    roundSecurityDepositTo: null,
    typeOfSecurityDeposit: null,
    useReserveSplitInCalculationForLoan: false,
    useReserveSplitInCalculationForLease: false,
    taxExemptForUseTax: true,
    excludeFNIProductsInUseTaxCalculation: false,
    addVehicleMilesToAnnualmiles: false,
    enableYearlyMilesForBalloon: false,
    fullTermBalloon: false,
    acquisitionFee: 100,
    paymentDueDateAdjustment: false,
    oddDaysBasedOnDaysPerMonth: false,
    daysPerYearForOddDaysInterest: 365,
    annuityDue: false,
    doNotItemizeCCRTax: false,
    moneyFactorDecimalPoints: '5',
    moneyFactorOperations: null,
    lenderDetailsForDealerTrack: [],
    optionContractValidityInCalendarDays: 0,
    supportedVehicleCategory: null,
    tekionLenderCode: 'ACUFS',
    lenderSupportsCreditApplication: false,
  },
  {
    code: 'HFS',
    label: 'HFS',
    feeMarkup: null,
    financeReserveMethods: [
      {
        paymentType: 'LOAN',
        reserveType: 'MARKUP',
        value: null,
      },
      {
        paymentType: 'LEASE',
        reserveType: 'MARKUP',
        value: null,
      },
    ],
    actualPenaltyPerMile: null,
    penaltyPerMile: 0,
    bankFeeUpfront: 'capped',
    address: {
      address: null,
      address2: null,
      address3: null,
      address4: null,
      state: null,
      city: null,
      county: null,
      zipCode: null,
      phoneNumber: null,
      country: null,
    },
    lienHolderAddress: {
      address: null,
      address2: null,
      address3: null,
      address4: null,
      state: null,
      city: null,
      county: null,
      zipCode: null,
      phoneNumber: null,
      country: null,
    },
    sameAsLender: true,
    preferred: true,
    id: '64b904cac2c74f01519bc7d4',
    loanSupported: true,
    leaseSupported: true,
    paymentTypeSupported: ['LOAN', 'LEASE', 'ONE_TIME_LEASE', 'BALLOON'],
    dispositionFee: null,
    dispositionThresholdValue: null,
    terminationThresholdValue: null,
    financeReserveSplitPercentage: 75,
    codeName: 'HFS',
    marketScanCodes: [
      {
        Code: 'HFS',
        Type: 0,
      },
    ],
    defaultLender: false,
    legalName: 'HFS',
    marketScan: true,
    hidden: false,
    financeReserve: null,
    enableTxFinanceCharge: false,
    aprMFType: 0,
    loanMaxMarkup: 3,
    leaseMaxMarkup: 4,
    onePayMaxMarkup: null,
    removeDemoMileageFromAnnualMileage: false,
    removeDemoMileageFromAnnualMileageDeciderValue: 0,
    removeDemoMileageFromAnnualMileageDisplayName: null,
    freeMilesforResidualAdjustment: 0,
    onePay2DigitsAfterCommaMonthlyPayment: false,
    firstPaymentWaivedDefaultValue: false,
    acquisitionFeeMarkupAmt: null,
    useMPEqDispFeeWhenMpLtDispFee: false,
    securityDepositWaiverReason: null,
    includeSecurityDeposits: true,
    collectSecurityDepositWaiverReason: true,
    tiers: [
      {
        code: 'Tier 1',
        type: null,
        name: 'Tier 1',
      },
      {
        code: 'Tier 2',
        type: null,
        name: 'Tier 2',
      },
      {
        code: 'Tier 3',
        type: null,
        name: 'Tier 3',
      },
      {
        code: 'Tier 4',
        type: null,
        name: 'Tier 4',
      },
      {
        code: 'Tier 5',
        type: null,
        name: 'Tier 5',
      },
      {
        code: 'Tier 6',
        type: null,
        name: 'Tier 6',
      },
      {
        code: 'Tier 7',
        type: null,
        name: 'Tier 7',
      },
      {
        code: 'Tier 8',
        type: null,
        name: 'Tier 8',
      },
      {
        code: 'Tier 9',
        type: null,
        name: 'Tier 9',
      },
      {
        code: 'Tier 10',
        type: null,
        name: 'Tier 10',
      },
      {
        code: 'Tier 11',
        type: null,
        name: 'Tier 11',
      },
      {
        code: 'Tier 12',
        type: null,
        name: 'Tier 12',
      },
      {
        code: 'Tier 13',
        type: null,
        name: 'Tier 13',
      },
      {
        code: 'Tier 14',
        type: null,
        name: 'Tier 14',
      },
    ],
    roundOffMileageToNearestThousand: true,
    vehicleMileCutOffForRemoveDemoMileage: 0,
    personalPropertyTaxLenderType: 0,
    totalNumberOfSecurityDeposits: null,
    roundSecurityDepositTo: null,
    typeOfSecurityDeposit: null,
    useReserveSplitInCalculationForLoan: false,
    useReserveSplitInCalculationForLease: false,
    taxExemptForUseTax: true,
    excludeFNIProductsInUseTaxCalculation: false,
    addVehicleMilesToAnnualmiles: false,
    enableYearlyMilesForBalloon: false,
    fullTermBalloon: true,
    acquisitionFee: null,
    paymentDueDateAdjustment: false,
    oddDaysBasedOnDaysPerMonth: true,
    daysPerYearForOddDaysInterest: 365,
    annuityDue: false,
    doNotItemizeCCRTax: false,
    moneyFactorDecimalPoints: null,
    moneyFactorOperations: null,
    lenderDetailsForDealerTrack: [],
    optionContractValidityInCalendarDays: 0,
    supportedVehicleCategory: null,
    tekionLenderCode: 'HFS',
    lenderSupportsCreditApplication: false,
  },
  {
    code: 'HONFS',
    label: 'HONFS',
    feeMarkup: null,
    financeReserveMethods: [
      {
        paymentType: 'LOAN',
        reserveType: 'MARKUP',
        value: null,
      },
      {
        paymentType: 'LEASE',
        reserveType: 'MARKUP',
        value: null,
      },
    ],
    actualPenaltyPerMile: null,
    penaltyPerMile: 0,
    bankFeeUpfront: 'capped',
    address: {
      address: null,
      address2: null,
      address3: null,
      address4: null,
      state: null,
      city: null,
      county: null,
      zipCode: null,
      phoneNumber: null,
      country: null,
    },
    lienHolderAddress: {
      address: null,
      address2: null,
      address3: null,
      address4: null,
      state: null,
      city: null,
      county: null,
      zipCode: null,
      phoneNumber: null,
      country: null,
    },
    sameAsLender: true,
    preferred: true,
    id: '648d51fb5d60c60d1f052276',
    loanSupported: true,
    leaseSupported: true,
    paymentTypeSupported: ['LOAN', 'LEASE', 'ONE_TIME_LEASE', 'BALLOON'],
    dispositionFee: null,
    dispositionThresholdValue: null,
    terminationThresholdValue: null,
    financeReserveSplitPercentage: 0,
    codeName: 'HONFS',
    marketScanCodes: [
      {
        Code: 'HONFS',
        Type: 0,
      },
    ],
    defaultLender: false,
    legalName: 'Honda Financial Services',
    marketScan: true,
    hidden: false,
    financeReserve: null,
    enableTxFinanceCharge: false,
    aprMFType: 1,
    loanMaxMarkup: null,
    leaseMaxMarkup: null,
    onePayMaxMarkup: null,
    removeDemoMileageFromAnnualMileage: false,
    removeDemoMileageFromAnnualMileageDeciderValue: 0,
    removeDemoMileageFromAnnualMileageDisplayName: null,
    freeMilesforResidualAdjustment: 0,
    onePay2DigitsAfterCommaMonthlyPayment: false,
    firstPaymentWaivedDefaultValue: true,
    acquisitionFeeMarkupAmt: null,
    useMPEqDispFeeWhenMpLtDispFee: false,
    securityDepositWaiverReason: 'CREDIT_EXCEPTION',
    includeSecurityDeposits: false,
    collectSecurityDepositWaiverReason: false,
    tiers: [
      {
        code: 'Tier 1',
        type: null,
        name: 'Tier 1',
      },
      {
        code: 'Tier 2',
        type: null,
        name: 'Tier 2',
      },
      {
        code: 'Tier 3',
        type: null,
        name: 'Tier 3',
      },
      {
        code: 'Tier 4',
        type: null,
        name: 'Tier 4',
      },
      {
        code: 'Tier 5',
        type: null,
        name: 'Tier 5',
      },
      {
        code: 'Tier 6',
        type: null,
        name: 'Tier 6',
      },
      {
        code: 'Tier 7',
        type: null,
        name: 'Tier 7',
      },
      {
        code: 'Tier 8',
        type: null,
        name: 'Tier 8',
      },
      {
        code: 'Tier 9',
        type: null,
        name: 'Tier 9',
      },
      {
        code: 'Tier 10',
        type: null,
        name: 'Tier 10',
      },
      {
        code: 'Tier 11',
        type: null,
        name: 'Tier 11',
      },
      {
        code: 'Tier 12',
        type: null,
        name: 'Tier 12',
      },
      {
        code: 'Tier 13',
        type: null,
        name: 'Tier 13',
      },
      {
        code: 'Tier 14',
        type: null,
        name: 'Tier 14',
      },
    ],
    roundOffMileageToNearestThousand: true,
    vehicleMileCutOffForRemoveDemoMileage: 0,
    personalPropertyTaxLenderType: 0,
    totalNumberOfSecurityDeposits: null,
    roundSecurityDepositTo: null,
    typeOfSecurityDeposit: null,
    useReserveSplitInCalculationForLoan: false,
    useReserveSplitInCalculationForLease: false,
    taxExemptForUseTax: false,
    excludeFNIProductsInUseTaxCalculation: false,
    addVehicleMilesToAnnualmiles: false,
    enableYearlyMilesForBalloon: false,
    fullTermBalloon: false,
    acquisitionFee: 0,
    paymentDueDateAdjustment: false,
    oddDaysBasedOnDaysPerMonth: false,
    daysPerYearForOddDaysInterest: 365,
    annuityDue: false,
    doNotItemizeCCRTax: false,
    moneyFactorDecimalPoints: '9',
    moneyFactorOperations: null,
    lenderDetailsForDealerTrack: [],
    optionContractValidityInCalendarDays: 0,
    supportedVehicleCategory: null,
    tekionLenderCode: 'HONFS',
    lenderSupportsCreditApplication: false,
  },
];

export const allTires = [
  'Tier 1',
  'Tier 2',
  'Tier 3',
  'Tier 4',
  'Tier 5',
  'Tier 6',
  'Tier 7',
  'Tier 8',
  'Tier 9',
  'Tier 10',
  'Tier 11',
  'Tier 12',
  'Tier 13',
  'Tier 14',
];

export const defaultColumnTier = ['S'];

export const columnDataWithTiers = {
  selectedTier: {
    code: 'S',
    type: null,
    name: 'S',
  },
};

export const lenderProgramsCanadaData = [
  {
    programId: '4e2c50fd-98c6-4c09-9eaf-3af804015d53',
    lenderCode: '651fc33fbb778b5b045f9a70', // Audi financials
    programName: 'Ohio New Standard Retail',
    programNumber: 'STR',
    startDate: '2024-03-01',
    endDate: '2024-04-30',
    term: 24,
    sellRate: 7.54,
    buyRate: 7.54,
    programType: 'STANDARD_PROGRAM',
    creditTierItem: {
      code: null,
      name: 'Tier 3',
      score: null,
    },
  },
  {
    programId: '4e2c50fd-98c6-4c09-9eaf-3af804015d53',
    lenderCode: '62290d0c83630f0006ce5968', // BMWCA
    programName: 'Ontario New Retail',
    programNumber: 'ABC',
    startDate: '2022-03-01',
    endDate: '2022-04-30',
    term: 36,
    sellRate: 9,
    buyRate: 7,
    programType: 'SPECIAL_PROGRAM',
    creditTierItem: {
      code: null,
      name: 'Tier 4',
      score: null,
    },
  },
  {
    programId: '4e2c50fd-98c6-4c09-9eaf-3af804015d53',
    lenderCode: '65b03d3eefde9e3ff469b472', // Mercedes Benz Financials
    programName: 'Manitoba New Standard Retail',
    programNumber: 'XYZ',
    startDate: '2022-06-01',
    endDate: '2022-12-30',
    term: 48,
    sellRate: 5.22,
    buyRate: 3.75,
    programType: 'STANDARD_PROGRAM',
    creditTierItem: {
      code: null,
      name: 'Tier 7',
      score: null,
    },
  },
  {
    programId: '4e2c50fd-98c6-4c09-9eaf-3af804015d87',
    lenderCode: '651fc48ebb778b5b045f9a71', // vokswagen
    programName: 'Quebec New special Retail',
    programNumber: 'QBC',
    startDate: '2020-01-01',
    endDate: '2021-10-5',
    term: 24,
    sellRate: 4,
    buyRate: 2.43,
    programType: 'SPECIAL_PROGRAM',
    creditTierItem: {
      code: null,
      name: 'Tier 1',
      score: null,
    },
  },
  {
    programId: '4e2c50fd-98c6-4c09-9eaf-3af804015d53',
    lenderCode: '61d2cd54304cc9c294dea167', // JP Morgan Chase
    programName: 'Vancover Program for Lease',
    programNumber: 'VAN',
    startDate: '2022-06-01',
    endDate: '2022-12-30',
    term: 36,
    sellRate: 3,
    buyRate: 1.9,
    programType: 'STANDARD_PROGRAM',
    creditTierItem: {
      code: null,
      name: 'Tier 5',
      score: null,
    },
  },
];

export const mockRowDataMF = {
  program: {
    dealInterestRateMF: {
      sellRate: 0.00125,
      buyRate: 0.001,
    },
    dealInterestRateAPR: {
      sellRate: 5.5,
      buyRate: 4.5,
    },
  },
};

export const mockRowDataAPR = {
  program: {
    dealInterestRateAPR: {
      sellRate: 6.5,
      buyRate: 5.5,
    },
  },
};

export const mockRowDataEmpty = {
  program: {},
};

export const fordFlexProgramTrue = {
  FullDetails: {
    Retail: {
      IsFordFlexDeal: true,
    },
  },
};

export const fordFlexProgramFalse = {
  FullDetails: {
    Retail: {
      IsFordFlexDeal: false,
    },
  },
};

export const genericProgramsWithFordFlex = [fordFlexProgramTrue];
export const genericProgramsWithoutFordFlex = [fordFlexProgramFalse];
export const genericProgramsEmpty = [];
export const selectedLenderCodeNamesWithFord = ['Ford Motor Credit'];
export const selectedLenderCodeNamesWithoutFord = ['Some Other Lender'];
