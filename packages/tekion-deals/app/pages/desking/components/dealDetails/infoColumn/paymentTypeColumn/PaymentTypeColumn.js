import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import _noop from 'lodash/noop';

import { EMPTY_OBJECT, NO_DATA } from '@tekion/tekion-base/app.constants';
import * as SalesSetupReader from '@tekion/tekion-base/marketScan/readers/salesSetup.reader';
import * as ColumnDataReader from '@tekion/tekion-base/marketScan/readers/columnData.reader';
import * as DealReader from '@tekion/tekion-base/marketScan/readers/deal.reader';
import { INTEGRATION_KEYS } from '@tekion/tekion-base/constants/retail/salesSetup.constants';
import { INFO_COLUMN_FIELDS } from '@tekion/tekion-base/marketScan/constants/deskingFields.constants';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import { isInchCapeOrRrg, isPurchaseOnlyOrDealerTrade } from 'utils/desking.helpers';

import withDeskingContext from 'pages/desking/withDeskingContext';
import { DEAL_STATUS } from 'pages/deallist/deal.constants';
import { DOWN_PAYMENT_TYPES, PAYMENT_TYPE_DOWNPAYMENT_TYPE_MAPPING } from 'pages/desking/desking.constants';

import { isRRG } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
import TextCellRenderer from './PaymentTypeColumn.TextCellRenderrer';
import { getClassName } from './paymentTypeColumn.helpers';
import styles from './paymentTypeColumn.module.scss';

class PaymentTypeColumn extends PureComponent {
  componentDidMount() {}

  isLenderSource = (columnData, downPaymentId) => {
    const { selectedDownPaymentId } = columnData;
    const lenderApproved = ColumnDataReader.isColumnLenderApproved(columnData);
    return !lenderApproved || selectedDownPaymentId === downPaymentId;
  };

  render() {
    const {
      columnData,
      downPayments,
      DeskingActions,
      savePaymentsDataForSelectedVehicle,
      toggleDealSummaryVisibility,
      getMarketScanPayloadObject,
      getMarketScanData,
      deferredPayments,
      deal,
      saveDealData,
      salesSetupInfo,
      showDepositRow,
      onDownPaymentSelect,
      isUSCalcEngineEnabled,
      getFieldStatus,
      isDealDirty,
    } = this.props;
    const paymentType = ColumnDataReader.getPaymentType(columnData);
    const isMultipleDownPaymentsEnabled = DealReader.isMultipleDownPaymentsEnabled(deal);
    const downPaymentType = isMultipleDownPaymentsEnabled
      ? PAYMENT_TYPE_DOWNPAYMENT_TYPE_MAPPING[paymentType]
      : DOWN_PAYMENT_TYPES.LOAN_DOWNPAYMENTS;
    const setupIntegrationKeys = SalesSetupReader.selectIntegrationKeys(salesSetupInfo);
    const shouldAutoRollServiceContract = SalesSetupReader.getAutoRollServiceContract(salesSetupInfo);
    const isConfirmStatusEnabled = SalesSetupReader.isDealStatusEnabled(salesSetupInfo, DEAL_STATUS.CONFIRMED);
    const isAutoRewardEnabled = SalesSetupReader.isIntegrationEnabled(
      SalesSetupReader.selectIntegrationKeys(salesSetupInfo),
      INTEGRATION_KEYS.AUTO_REWARDS
    );
    const { type } = deal;
    const { isDisabled } = getFieldStatus(INFO_COLUMN_FIELDS.EMI_CELL, columnData);
    const showDepositColumn =
      !showDepositRow && isConfirmStatusEnabled && !(isInchCapeOrRrg() && isPurchaseOnlyOrDealerTrade(type));

    return (
      <div className={styles.paymentTypeColumn}>
        {downPayments.map((downPayment, index) => (
          <TextCellRenderer
            onDownPaymentSelect={onDownPaymentSelect}
            downPayments={downPayments}
            downPayment={downPayment[downPaymentType]}
            columnData={columnData || EMPTY_OBJECT}
            key={index} // eslint-disable-line
            savePaymentsDataForSelectedVehicle={savePaymentsDataForSelectedVehicle}
            toggleDealSummaryVisibility={toggleDealSummaryVisibility}
            getMarketScanPayloadObject={getMarketScanPayloadObject}
            downPaymentId={index}
            onDownPaymentChange={DeskingActions.onDownPaymentChange}
            addRebatesInSelectedColumn={DeskingActions.addRebatesInSelectedColumn}
            getMarketScanData={getMarketScanData}
            deferredPayments={deferredPayments}
            DeskingActions={DeskingActions}
            disabled={isDisabled}
            enableSelection={isRRG()}
            deal={deal}
            saveDealData={saveDealData}
            setupIntegrationKeys={setupIntegrationKeys}
            shouldAutoRollServiceContract={shouldAutoRollServiceContract}
            salesSetupInfo={salesSetupInfo}
            isUSCalcEngineEnabled={isUSCalcEngineEnabled}
            isDealDirty={isDealDirty}
          />
        ))}
        <PropertyControlledComponent controllerProperty={showDepositColumn}>
          <div className={getClassName(columnData)}>{NO_DATA}</div>
        </PropertyControlledComponent>
        {isAutoRewardEnabled && <div className={getClassName(columnData)}>{NO_DATA}</div>}
      </div>
    );
  }
}

PaymentTypeColumn.defaultProps = {
  columnData: EMPTY_OBJECT,
  DeskingActions: EMPTY_OBJECT,
  toggleDealSummaryVisibility: _noop,
  savePaymentsDataForSelectedVehicle: _noop,
  downPayments: EMPTY_OBJECT,
  deferredPayments: EMPTY_OBJECT,
  showDepositRow: false,
};

PaymentTypeColumn.propTypes = {
  columnData: PropTypes.object,
  DeskingActions: PropTypes.object,
  toggleDealSummaryVisibility: PropTypes.func,
  savePaymentsDataForSelectedVehicle: PropTypes.func,
  downPayments: PropTypes.object,
  getMarketScanPayloadObject: PropTypes.func.isRequired,
  getMarketScanData: PropTypes.func.isRequired,
  deferredPayments: PropTypes.object,
  salesSetupInfo: PropTypes.object.isRequired,
  saveDealData: PropTypes.func.isRequired,
  deal: PropTypes.object.isRequired,
  showDepositRow: PropTypes.bool,
  onDownPaymentSelect: PropTypes.func.isRequired,
  isUSCalcEngineEnabled: PropTypes.bool.isRequired,
  getFieldStatus: PropTypes.func.isRequired,
};

export default withDeskingContext(PaymentTypeColumn);
