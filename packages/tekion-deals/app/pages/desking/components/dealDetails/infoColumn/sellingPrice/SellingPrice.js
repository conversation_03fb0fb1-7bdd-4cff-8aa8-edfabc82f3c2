import React, { useState, useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';
import { defaultMemoize } from 'reselect';
import classNames from 'classnames';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _isNull from 'lodash/isNull';
import _size from 'lodash/size';

import {
  hasCostAndGrossView,
  hasDeskingUpdateSellingPriceLessThanMSRP,
  hasSellingPriceEdit,
} from 'permissions/desking.permissions';
import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import { VEHICLE_PRICING_KEYS } from '@tekion/tekion-base/marketScan/constants/constants';
import { MESSAGES } from 'constants/pages';
import { getSellingPriceSuggestion } from 'pages/deallist/deal.util';
import { getSellingPriceBasedOnVehicleId } from '@tekion/tekion-base/marketScan/utils/desking.utils';
import * as ColumnDataReader from '@tekion/tekion-base/marketScan/readers/columnData.reader';
import * as DealReader from '@tekion/tekion-base/marketScan/readers/deal.reader';
import DropDown from '@tekion/tekion-components/src/molecules/DropDown';
import CurrencyInput from '@tekion/tekion-widgets/src/organisms/currencyInput';
import withAddOnAfter from 'molecules/inputWithAddOnAfter';
import WithOnPressEnter from '@tekion/tekion-widgets/src/appServices/sales/hocs/withOnPressEnter';
import Menu from '@tekion/tekion-components/src/molecules/Menu';
import Popover from '@tekion/tekion-components/src/molecules/popover';
import FontIcon, { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';
import Content from '@tekion/tekion-components/src/atoms/Content';
import { getCurrencySymbol } from '@tekion/tekion-base/formatters/formatCurrency';

import styles from './sellingPrice.module.scss';

const getPricesThatAreNotRequired = defaultMemoize(() => {
  if (hasCostAndGrossView()) return EMPTY_ARRAY;
  return [VEHICLE_PRICING_KEYS.INVOICE_PRICE];
});

const CurrencyInputWithAddOnAfter = withAddOnAfter(WithOnPressEnter(CurrencyInput));

function SellingPrice({
  columnData,
  deal,
  DeskingActions,
  saveSellingPrice,
  isDealDirty,
  deskingpaymentDetails,
  isDeskingViewOnly,
}) {
  const vehicleDetails = DealReader.getVehicleBasedOnId(deal, _get(columnData, 'dealVehicleId')) || EMPTY_OBJECT;
  const sellingPriceSuggestions = getSellingPriceSuggestion(vehicleDetails, getPricesThatAreNotRequired()) || [];
  const isMultipleDownPaymentsEnabled = DealReader.isMultipleDownPaymentsEnabled(deal);
  const sellingPrice = getSellingPriceBasedOnVehicleId(deal, columnData, _get(columnData, 'dealVehicleId'));
  const dealVehicleId = _get(columnData, 'dealVehicleId');
  const selectedColumn = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);

  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [sellingPriceState, setSellingPrice] = useState(sellingPrice);
  const [errorMesssage, setErrorMessage] = useState(null);

  useEffect(() => {
    if (_isNull(errorMesssage)) {
      setSellingPrice(sellingPrice);
    }
  }, [sellingPrice, errorMesssage]);

  const changeSellingPrice = async (value, saveData = false) => {
    setSellingPrice(value);
    const target =
      isMultipleDownPaymentsEnabled && ColumnDataReader.isLeaseOrOPLeasePaymentType(columnData)
        ? 'leaseSellingPrice'
        : 'loanSellingPrice';
    if (isDropdownVisible) setIsDropdownVisible(false);
    const errorText = checkSellingPriceGreaterThanMSRP(value);
    setErrorMessage(errorText);
    if (!errorText) {
      await DeskingActions.setDealDirtyStatus(true);
      await DeskingActions.setVehicleInfo({
        key: `pricingDetails.${target}`,
        value: !value ? '' : Number(value),
        dealVehicleId,
      });

      if (
        !isMultipleDownPaymentsEnabled ||
        (ColumnDataReader.isLeaseOrBalloonPaymentType(selectedColumn) && target === 'loanSellingPrice') ||
        (ColumnDataReader.isLeaseOrOPLeasePaymentType(selectedColumn) && target === 'leaseSellingPrice')
      ) {
        if (!errorText) {
          await DeskingActions.setVehicleInfo({
            key: 'pricingDetails.sellingPrice',
            value: !value ? '' : Number(value),
            dealVehicleId,
          });
          await DeskingActions.setVehicleInfo({
            key: `pricingDetails.deskingSellingPrice`,
            value: !value ? '' : Number(value),
            dealVehicleId,
          });
        }
      }
      if (saveData) {
        saveSellingPrice(columnData?.dealVehicleId);
      }
    }
  };

  const checkSellingPriceGreaterThanMSRP = value => {
    const msrp = _get(vehicleDetails, ['pricingDetails', 'msrp']);
    if (!hasDeskingUpdateSellingPriceLessThanMSRP() && value < msrp) {
      return MESSAGES.SELLING_PRICE_LESS_THAN_MSRP_ERROR;
    }
    return null;
  };

  const toggleDropdownVisibility = () => setIsDropdownVisible(!isDropdownVisible);

  const onInputChange = value => {
    if (typeof Number(value) !== 'number') {
      return;
    }
    changeSellingPrice(value);
  };

  const onSellingPriceSelect = value => async () => {
    changeSellingPrice(value, true);
  };

  const save = () => {
    if (isDealDirty) saveSellingPrice(columnData?.dealVehicleId);
  };

  const renderMenu = () => (
    <Menu>
      {sellingPriceSuggestions.map(({ value, label }) => (
        <Menu.Item key={label} className={styles.menuItem} onClick={onSellingPriceSelect(value)}>
          <Content>{label}</Content>
        </Menu.Item>
      ))}
    </Menu>
  );
  const getInfoComponent = () =>
    errorMesssage ? (
      <Popover placement="top" trigger="hover" content={errorMesssage}>
        <FontIcon className={`${styles.warningIcon} mr-1`}>icon-alert</FontIcon>
      </Popover>
    ) : null;

  const isViewOnly = useMemo(() => isDeskingViewOnly || !hasSellingPriceEdit(), [isDeskingViewOnly]);

  return (
    <div className={_size(getCurrencySymbol()) > 1 ? styles.sellingPriceContainerCa : styles.sellingPriceContainer}>
      <DropDown
        overlay={renderMenu()}
        trigger={_isEmpty(sellingPriceSuggestions) ? '' : 'click'}
        visible={!isViewOnly && isDropdownVisible}
        disabled={isViewOnly}
        onVisibleChange={toggleDropdownVisibility}>
        <CurrencyInputWithAddOnAfter
          value={sellingPriceState}
          enforcePrecision={false}
          id="SELLING_PRICE"
          onChange={onInputChange}
          className={`${styles.sellingPriceInput}`}
          externalClassName={styles.containerClassName}
          addonAfterClassName={styles.iconClassName}
          inputNumberClassName={styles.inputNumberClassName}
          onBlur={save}
          addonAfter={
            <FontIcon
              size={SIZES.S}
              className={classNames(styles.iconUp, {
                [styles.iconDown]: !isDropdownVisible,
              })}
              onClick={toggleDropdownVisibility}>
              icon-chevron-down
            </FontIcon>
          }
          suffix={getInfoComponent()}
          onPressEnter={save}
        />
      </DropDown>
    </div>
  );
}

SellingPrice.propTypes = {
  columnData: PropTypes.object.isRequired,
  deal: PropTypes.object.isRequired,
  DeskingActions: PropTypes.object.isRequired,
  saveSellingPrice: PropTypes.func.isRequired,
  isDealDirty: PropTypes.bool.isRequired,
  deskingpaymentDetails: PropTypes.array.isRequired,
  isDeskingViewOnly: PropTypes.bool.isRequired,
};

export default SellingPrice;
