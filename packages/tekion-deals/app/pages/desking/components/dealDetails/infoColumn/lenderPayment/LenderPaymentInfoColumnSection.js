import React, { PureComponent } from 'react';
import { defaultMemoize } from 'reselect';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import _get from 'lodash/get';
import _debounce from 'lodash/debounce';
import _noop from 'lodash/noop';
import _isNil from 'lodash/isNil';
import _isEmpty from 'lodash/isEmpty';
import _filter from 'lodash/filter';
import _reduce from 'lodash/reduce';
import _compact from 'lodash/compact';
import _find from 'lodash/find';
import produce from 'immer';
import Tooltip from 'tcomponents/atoms/tooltip';
import { EMPTY_OBJECT, EMPTY_ARRAY, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { LENDER_RATE_TYPES } from '@tekion/tekion-base/constants/retail/marketScan';
import * as SalesSetupReader from '@tekion/tekion-base/marketScan/readers/salesSetup.reader';
import Money from '@tekion/tekion-base/utils/money';
import * as ColumnDataReader from '@tekion/tekion-base/marketScan/readers/columnData.reader';
import LenderReader from '@tekion/tekion-base/readers/retail/lender';
import LenderHelper from '@tekion/tekion-base/marketScan/helpers/lender';
import {
  getDealType,
  getPenEnabled,
  getMSRPOfPrimaryVehicle,
  getContractDate,
  isDealOptionContract,
  getPrimaryVehicleType,
  getDealStatus,
} from '@tekion/tekion-base/marketScan/readers/deal.reader';
import {
  getDefaultTierForPaymentType,
  isLeseOrOPLeasePaymentType,
  isLoanPaymentType,
} from '@tekion/tekion-base/marketScan/helpers/desking.helpers';
import { addDays, getDifferenceAsDays, toMoment, getMomentValueOf } from '@tekion/tekion-base/utils/dateUtils';
import { getDateInDayMonthYear } from '@tekion/tekion-widgets/src/hocs/withDateFormat/withDateFormat.helpers';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';
import Button from '@tekion/tekion-components/src/atoms/Button';
import { COUNTRY_CODE } from '@tekion/tekion-base/constants/countryCode';
import * as DealerConfigReader from 'utils/dealerConfig.reader';
import { isInchcape, isInchcapeOrRRG } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';

import UpdateModalStatic from 'organisms/updateModal/UpdateModalStatic';
import ConfirmationModal from 'molecules/ConfirmationModal';
import { formatCurrencyWithMoneyFormat, getNumber, getLenderCodeName, findArrayItem, getPopupContainer } from 'utils';
import FNIUpdate from 'pages/desking/components/fniUpdate/FNIUpdate';
import GlobalWarning from 'pages/desking/components/globalWarnings';
import { getBuyBackValuation } from 'organisms/BuybackValueModal/buybackValue.helper';
import AccessoriesUpdate from 'pages/desking/components/accessoriesUpdate';
import Rebate from 'pages/desking/components/rebate';
import MSProgramList from 'pages/desking/components/msProgramList';

import withDeskingContext from 'pages/desking/withDeskingContext';
import {
  PAYMENT_TYPES,
  RESIDUAL_DISPLAY_TYPE,
  MANUALLY_UPDATED_API_KEY,
  MARKUP_TYPE_VS_RESERVE_METHOD,
  RESERVE_METHOD,
  RATE_TYPE_FACTOR,
} from 'pages/desking/desking.constants';
import { setMarketScanDataForColumn } from 'pages/desking/DeskingMscanHelpers';

import * as DeskingReader from '@tekion/tekion-base/marketScan/readers/desking.reader';

import {
  getDealTypeFields,
  isCategory1Deal,
  isCategory2Deal,
  isCategory3Deal,
  getColumnRebatesFromAllRebates,
  getRebatesExcludingDealerCashAndEvTaxCredit,
  getRebatesTotal,
  getColumnAccessoriesFromAllAccessories,
  getColumnFNIsFromAllFNIs,
  getSelectedLenderDetails,
  getLabelFromPaymentOptions,
} from '@tekion/tekion-base/marketScan/utils/desking.utils';
import { LENDER_PAYMENT_FIELDS } from '@tekion/tekion-base/marketScan/constants/deskingFields.constants';
import InfoCell from 'pages/desking/components/dealDetails/cells/common/infoCell/index';
import withDefault from 'pages/desking/withDefault';
import { MARKUP_TYPES } from '@tekion/tekion-base/constants/retail/markupTypes';
import { checkTaxRateForWholeSaleDealEnabled, isDealerTradeDeal, isGenericPaymentSubType } from 'utils/deal.util';
import CalcEngineProperties from 'utils/CalcEngineProperties';
import { isCanadaDealer } from 'utils/dealerUtils';
import { appendPercentage } from '@tekion/tekion-base/marketScan/utils';

import { INTERNAL_FEE_CODES } from '@tekion/tekion-base/constants/retail/fee.constants';
import { VEHICLE_TYPE } from '@tekion/tekion-base/marketScan/constants/constants';
import { DESKING_FIELDS } from 'constants/desking.constants';
import { getImpactedColumnAndRowPayload } from 'utils/desking.helpers';
import { isFniUpdateDisabled } from 'pages/desking/components/dealDetails/dealDetailsReader';
import { isRebateEditDisabled } from 'pages/desking/components/dealDetails/dealDetails.utils';
import withHandleCustomerApprovals from 'pages/desking/hocs/withHandleCustomerApprovals';
import { BACKEND_GLOBAL_WARNINGS_ACTIONS } from 'commonActions/constants/backendWarnings.constant';
import salesDealerPropertyHelper from '@tekion/tekion-widgets/src/appServices/sales/helpers/salesDealerPropertyHelper';
import DropDownInfoCell from '../../cells/infoCells/dropDownInfoCell';
import AccordianTextCell from '../../cells/infoCells/accordianTextCell';
import TextCell from '../../cells/infoCells/textCell';
import InputInfoCell from '../../cells/infoCells/inputInfoCell';
import {
  getLendersDataForDropDown,
  getMaxMarkupForSelectedLenderBasedOnPaymentType,
  getSanitizedColumnData,
  getSanitizedLenderOptions,
  getUpdateTermPaymentDetailsColumnData,
  isLenderChanged,
  isTierChanged,
  getUpdatedSelectedTier,
} from './utils';
import {
  getFinanceContractStatusDetails,
  getPaymentSubType,
  filterSppFnisOut,
} from '../../../../../../readers/columnDataReader';
import APRMoneyFactorCell from './components/APRMoneyFactorCell';
import FinanceReserveCell from './components/FinanceReserveCell';
import ResidualCell from './components/ResidualCell';
import YearlyMilesCell from './components/YearlyMilesCell';
import DaysToFirstPaymentSuffix from './components/DaysToFirstPaymentSuffix';
import LenderCell from './components/lenderCell';
import styles from './index.module.scss';
import LeaseComparison from '../../leaseComparison/LeaseComparison';
import ContraSettlementsPopover from '../contraSettlements';
import { DEAL_STATUS } from '../../../../../deallist/deal.constants';

class LenderPaymentInfoColumn extends PureComponent {
  constructor(props) {
    super(props);
    this.aprSelectionRef = React.createRef();
    this.state = {
      showLeaseComparison: false,
    };
  }

  getApplicableLenders = defaultMemoize((lenders, paymentOption) => {
    const paymentType = _get(paymentOption, 'paymentType');
    return getLendersDataForDropDown(lenders, paymentType);
  });

  getRebatesForAccordian = defaultMemoize((allRebates, columnRebates) => {
    const isGalaxyEnabled = CalcEngineProperties.updateByGalaxyEngine();
    const validColumnRebates =
      getColumnRebatesFromAllRebates(allRebates, columnRebates, isGalaxyEnabled) || EMPTY_ARRAY;
    const rebatesInfo = validColumnRebates.map(({ rebateAmount: amount }) => ({
      amount,
    }));
    const rebateTotal = formatCurrencyWithMoneyFormat(
      getRebatesTotal(getRebatesExcludingDealerCashAndEvTaxCredit(validColumnRebates))
    );
    return {
      validColumnRebates: rebatesInfo,
      rebatesTotal: rebateTotal,
    };
  });

  getAccessoriesForAccordian = defaultMemoize((allAccessories, columnAccessories) => {
    const isVehicleProfileEnabled = salesDealerPropertyHelper.isVehicleProfileEnabled();
    const accessories =
      getColumnAccessoriesFromAllAccessories(allAccessories, columnAccessories, isVehicleProfileEnabled) || EMPTY_ARRAY;
    const accessoriesForAccordian = accessories.map(({ price, impactOnEmi }) => ({
      amount: price,
      emi: impactOnEmi,
    }));
    const accessoriesTotal = formatCurrencyWithMoneyFormat(
      accessoriesForAccordian
        .map(({ amount }) => getNumber(amount))
        .reduce((accumulator, currentValue) => Money.add(accumulator, currentValue), 0)
    );

    return {
      accessories: accessoriesForAccordian,
      accessoriesTotal,
    };
  });

  getFNIsForAccordian = defaultMemoize((allFNIs, columnFNIs, deskingpaymentDetails) => {
    const fnisWithoutSpp = ColumnDataReader.filterSppFnisOut(columnFNIs);
    const FNIs = filterSppFnisOut(
      getColumnFNIsFromAllFNIs(allFNIs, fnisWithoutSpp) || EMPTY_ARRAY,
      deskingpaymentDetails
    );
    const FNIsForAccordian = FNIs.map(({ price, impactOnEmi }) => ({ amount: price, emi: impactOnEmi }));
    const FNIsTotal = formatCurrencyWithMoneyFormat(
      FNIsForAccordian.map(({ amount }) => getNumber(amount)).reduce(
        (accumulator, currentValue) => Money.add(accumulator, currentValue),
        0
      )
    );

    return {
      FNIs: FNIsForAccordian,
      FNIsTotal,
    };
  });

  getSelectedLenderInfo = defaultMemoize((lenders, selectedLenderId) => {
    const matchedLender = findArrayItem(lenders, 'id', selectedLenderId);
    const lenderDetails = DeskingReader.getLenderDetails(matchedLender, [
      'removeDemoMileageFromAnnualMileage',
      'removeDemoMileageFromAnnualMileageDeciderValue',
      'removeDemoMileageFromAnnualMileageDisplayName',
      'enableYearlyMilesForBalloon',
    ]);
    return lenderDetails;
  });

  onCloseRebateModal = (refresh, showConfirmationModal, hideModal, getMarketScanData, deskingpaymentDetails) => {
    const { syncPaymentDetails, handleCustomerApprovalOnRebateUpdate } = this.props;
    if (refresh) {
      this.updateRebatesInDeal({ deskingpaymentDetails, syncPaymentDetails, getMarketScanData }).then(() => {
        if (DealerPropertyHelper.isCustomerApprovalsEnabled()) {
          // handleCustomerApprovalOnRebateUpdate(deskingpaymentDetails);
        }
      });
    }
    if (showConfirmationModal) {
      return ConfirmationModal.show({
        title: __('Warning'),
        message: __("Are you sure want to close this pop up? You'll lose the progress you've made."),
        submitBtnText: __('Yes'),
        onSubmitCb: () => {
          hideModal();
          ConfirmationModal.close();
        },
      });
    }

    return hideModal();
  };

  onBlur = _debounce(() => {
    const { isDealDirty } = this.props;
    if (isDealDirty) {
      this.refreshOnSubmit();
    }
  }, 1000);

  updateRebatesInDeal = async ({ deskingpaymentDetails, syncPaymentDetails, getMarketScanData }) => {
    if (CalcEngineProperties.updateCalculationByBackend()) {
      await syncPaymentDetails();
    } else {
      await getMarketScanData({
        columnIds: deskingpaymentDetails.map(({ id }) => id),
      });
    }
  };

  // Triggers market scan refresh only for this column and for selected lender
  refreshOnSubmit = () => {
    const { columnData, getMarketScanData, updateDeskingForColumnIds, syncPaymentDetails } = this.props;
    if (DealerPropertyHelper.isCreateDealWithNewAPIEnabled()) {
      updateDeskingForColumnIds([columnData.id]);
    } else if (CalcEngineProperties.updateCalculationByBackend()) {
      syncPaymentDetails();
    } else {
      getMarketScanData({ columnIds: [columnData.id] });
    }
  };

  onResidualDisplayTypeChange = async residualDisplayType => {
    const { columnData, DeskingActions, savePaymentsDataForSelectedVehicle, syncPaymentDetails } = this.props;
    const { id, selectedLenderId } = columnData;
    await DeskingActions.setResidualDisplayType({
      id,
      lender: selectedLenderId,
      residualDisplayType,
    });
    if (CalcEngineProperties.updateCalculationByBackend()) {
      syncPaymentDetails();
    } else {
      savePaymentsDataForSelectedVehicle();
    }
  };

  onResidualChange = async changedValue => {
    const { columnData, DeskingActions } = this.props;
    const { id, selectedLenderId } = columnData;
    const existingResidual = ColumnDataReader.getResidualData(columnData);
    const residualDisplayType = ColumnDataReader.getResidualDisplayType(columnData);
    const programId = ColumnDataReader.getProgramIdForLender(columnData, selectedLenderId);
    const residualData = produce(existingResidual, draft => {
      if (residualDisplayType === RESIDUAL_DISPLAY_TYPE.AMOUNT) {
        draft.totalValue = changedValue;
      } else {
        draft.totalPercentage = changedValue;
      }
      draft.residualOverridden = getNumber(changedValue) > 0;
      draft[MANUALLY_UPDATED_API_KEY] = true;
    });

    await DeskingActions.changeResidualData({
      id,
      lender: selectedLenderId,
      residualData,
      resetProgramId: !(isCanadaDealer() && !_isEmpty(programId)),
    });
  };

  resetResidual = async newYearlyMilesData => {
    const { columnData } = this.props;
    // reset residual if standard data coming fromm mscan and yearly miles changed
    const hasStandardData = ColumnDataReader.hasStandardData(columnData);
    const previousYealyMiles = ColumnDataReader.getTotalYearlyMiles(columnData);

    if (getNumber(previousYealyMiles) !== getNumber(newYearlyMilesData?.totalValue) && hasStandardData) {
      await this.onResidualChange(null);
    }
  };

  onYearlyMilesChange = yearlyMilesInfo => {
    const { columnData, DeskingActions } = this.props;
    const { id, selectedLenderId } = columnData;
    DeskingActions.changeYearlyMiles({
      id,
      yearlyMilesInfo: {
        ...yearlyMilesInfo,
        ...(CalcEngineProperties.updateByCalcEngine() ? { yearlyMilesUpdated: true } : {}),
      },
      lender: selectedLenderId,
    });
  };

  onOSFLenderSelected = async lender => {
    const { columnData, DeskingActions, savePaymentsDataForSelectedVehicle, syncPaymentDetails, deal } = this.props;
    const { id } = columnData;
    const lenderId = _get(lender, 'id');

    await DeskingActions.setSelectedLenderForCash({
      columnId: id,
      lender: lenderId ? lender : {},
    });
    if (CalcEngineProperties.updateCalculationByBackend()) {
      const payload = getImpactedColumnAndRowPayload({
        deskingField: DESKING_FIELDS.LENDER,
        impactedColumnIds: [id],
      });
      await syncPaymentDetails(payload);
    } else {
      await savePaymentsDataForSelectedVehicle();
    }
    if (DealerPropertyHelper.isExternalDmsEnabled()) {
      DeskingActions.fetchDealSyncMappingWarning(_get(deal, 'dealNumber'));
    }
    GlobalWarning.updateBEWarnings([BACKEND_GLOBAL_WARNINGS_ACTIONS.DEAL_PUSH_WARNINGS]);
  };

  getAprValueBasedonRateType = (previousRateType, newRateType, value) => {
    if (previousRateType === newRateType) return value;
    if (newRateType === LENDER_RATE_TYPES.MF) {
      return value / RATE_TYPE_FACTOR;
    }
    return value * RATE_TYPE_FACTOR;
  };

  onLenderContraSettlementChange = async contraSettlementsSupported => {
    // For Inchcape and CalcEngineProperties.updateByCalcEngine() is true
    const { columnData, DeskingActions, syncPaymentDetails } = this.props;
    const { id } = columnData;
    await DeskingActions.setLenderContraSettlementsSupportedFlag(id, contraSettlementsSupported);
    const payload = getImpactedColumnAndRowPayload({
      deskingField: DESKING_FIELDS.LENDER,
      impactedColumnIds: [id],
    });
    await syncPaymentDetails(payload);
  };

  onLenderChange = async lenderId => {
    const {
      DeskingActions,
      savePaymentsDataForSelectedVehicle,
      getMarketScanData,
      lenders,
      addDefaultCostAdjustments,
      salesSetupInfo,
      saveDealData,
      deal,
    } = this.props;
    const { columnData, getDefaultTermPaymentDataForColumn, syncPaymentDetails } = this.props;
    const { id } = columnData;

    const previousAPRData = ColumnDataReader.getAPRData(columnData);
    const previousLenderId = ColumnDataReader.getSelectedLenderId(columnData);
    // const previousSelectedLenderCodeName = getLenderCodeName(lenders, ColumnDataReader.getSelectedLenderId(columnData));
    const termPaymentsData = getDefaultTermPaymentDataForColumn(id, lenderId);
    const selectedLenderCodeName = getLenderCodeName(lenders, lenderId);
    const matchedLender = findArrayItem(lenders, 'id', lenderId);
    const paymentType = ColumnDataReader.getPaymentType(columnData);
    const useCustomerTierForLoanApr = SalesSetupReader.getUseCustomerTierForLoanApr(salesSetupInfo);
    const isLeasePaymentType = isLeseOrOPLeasePaymentType(paymentType);

    const {
      optionContractValidityInCalendarDays,
      tiers,
      contraSettlementsSupportedLender: contraSettlementsSupported,
    } = matchedLender;
    const selectedTier = getDefaultTierForPaymentType(paymentType, tiers);
    const contractDate = getContractDate(deal);
    if (contractDate) {
      const optionContractValidityDate = addDays(optionContractValidityInCalendarDays, contractDate);
      if (
        optionContractValidityDate &&
        isDealOptionContract(deal) &&
        isLoanPaymentType(paymentType) &&
        getDifferenceAsDays(optionContractValidityDate, new Date()) < 0
      ) {
        await new Promise(resolve => {
          ConfirmationModal.show({
            title: __('Warning'),
            message: __(
              'Option Contract Validity Date has Expired for the Selected Lender. Please change it Manually if Required.'
            ),
            submitBtnText: __('Close'),
            hideCancel: true,
            onSubmitCb: () => {
              resolve(true);
              ConfirmationModal.close();
            },
          });
        });
      }
      await DeskingActions.setOptionContractValidityDate({ optionContractValidityDate, id });
    }

    // to avoid generic calls for new lender in future, check getColumnsForGenericScan
    await DeskingActions.setProgManOverridden({
      value: false,
      selectedColumnId: columnData?.id,
    });
    // will set termPaymentsData only if lenderId is new and doesnot exist in store
    await DeskingActions.setSelectedLender({
      id,
      selectedLenderId: lenderId,
      selectedTier: useCustomerTierForLoanApr || isLeasePaymentType ? selectedTier : null,
      selectedLenderCodeName,
      contraSettlementsSupported,
      defaultTermPaymentsData: {
        [id]: termPaymentsData,
      },
    });
    this.showWarnings();
    const { columnData: columnDataAfterChangingLender } = this.props;
    const newAPRData = ColumnDataReader.getAPRData(columnDataAfterChangingLender);

    if (previousAPRData?.manuallyUpdated) {
      const { reserveMethod } = newAPRData;
      const {
        apr: previousSellRate,
        buyRate: previousBuyRate,
        markUp: previousMarkup,
        markUpOverridden: previousMarkUpOverridden,
        buyRateOverridden: previousBuyRateOverridden,
        rateType: previousRateType,
      } = previousAPRData || {};
      const isCapCostBasedReserveMethod = reserveMethod === RESERVE_METHOD.CAPCOST_BASED;

      const aprValue = this.getAprValueBasedonRateType(previousRateType, newAPRData?.rateType, previousSellRate);
      const buyrateValue = this.getAprValueBasedonRateType(
        previousRateType,
        newAPRData?.rateType,
        isCapCostBasedReserveMethod ? previousSellRate : previousBuyRate
      );
      await DeskingActions.changeAPR({
        id,
        aprData: {
          ...newAPRData,
          apr: aprValue,
          buyRate: buyrateValue, // equal to sellRate in case of % of Amt. Financed(CAPCOST_BASED)
          buyRateOverridden: isCapCostBasedReserveMethod ? true : previousBuyRateOverridden, // if  buyrate changes this will be true
          markUp: isCapCostBasedReserveMethod ? 0 : previousMarkup, // if buyRate is overidden, markup will change, in this case 0.
          markUpOverridden: isCapCostBasedReserveMethod ? true : previousMarkUpOverridden, // if we change markup, make markupOverriden true
          manuallyUpdated: true,

          // if previuous lenderRateType is different from new LenderRateType then apr should be converted
        },
      });
      await DeskingActions.setPreviousLenderId({
        id,
        lenderId: previousLenderId,
      });
    }
    // else {
    //   await DeskingActions.changeAPR({ id, aprData: defaultRates });
    // }
    // else if (selectedLenderCodeName === previousSelectedLenderCodeName) {
    //   await DeskingActions.changeAPR({ id, aprData: previousAPRData });
    // }

    // const { defaultRates } = this.getDefaultRates();
    // if (previousAPRData?.manuallyUpdated) {
    //   await DeskingActions.changeAPR({
    //     id,
    //     aprData: previousAPRData,
    //   });
    //   await DeskingActions.setPreviousLenderId({
    //     id,
    //     lenderId: previousLenderId,
    //   });
    // } else {
    //   await DeskingActions.changeAPR({
    //     id,
    //     aprData: defaultRates,
    //   });
    // }

    if (CalcEngineProperties.updateCalculationByBackend()) {
      const payload = getImpactedColumnAndRowPayload({
        deskingField: DESKING_FIELDS.LENDER,
        impactedColumnIds: [id],
      });
      await syncPaymentDetails(payload);
      if (DealerPropertyHelper.isExternalDmsEnabled()) {
        DeskingActions.fetchDealSyncMappingWarning(_get(deal, 'dealNumber'));
      }
      GlobalWarning.updateBEWarnings([BACKEND_GLOBAL_WARNINGS_ACTIONS.DEAL_PUSH_WARNINGS]);
      return;
    }
    await this.addLenderFees();
    await savePaymentsDataForSelectedVehicle();
    await addDefaultCostAdjustments();
    await DeskingActions.setDealPreferences({
      includeExpiredLenderPrograms: false,
    });

    if (
      isLeasePaymentType &&
      matchedLender?.securityDepositWaiverReason &&
      matchedLender?.collectSecurityDepositWaiverReason
    ) {
      await DeskingActions.setSecurityDepositWaiverReason({ value: matchedLender.securityDepositWaiverReason });
      await DeskingActions.setSecurityDeposit({
        id,
        securityDepositType: null,
        securityDeposit: 0,
        securityDepositOverridden: true,
      });
      await saveDealData();
    }

    await getMarketScanData({ columnIds: [id] });
    if (DealerPropertyHelper.isExternalDmsEnabled()) {
      DeskingActions.fetchDealSyncMappingWarning(_get(deal, 'dealNumber'));
    }
    GlobalWarning.updateBEWarnings([BACKEND_GLOBAL_WARNINGS_ACTIONS.DEAL_PUSH_WARNINGS]);
  };

  removeBankFee = async () => {
    const { columnData, DeskingActions } = this.props;
    const columnFees = ColumnDataReader.getColumnFees(columnData);
    const { id } = columnData;
    const feesAfterDeletingBankFee = _filter(
      columnFees,
      fee => fee.internalFeeCode !== INTERNAL_FEE_CODES.BANK_OR_ACQUISITION_FEE
    );

    await DeskingActions.feesSave({
      columnFees: { [id]: feesAfterDeletingBankFee },
    });
  };

  addLenderFees = async () => {
    await this.removeBankFee();
    const { columnData, addDefaultFee, updateStateFeeTargetingAndSaveDealData } = this.props;
    const keepManuallyAddedFee = true;
    await addDefaultFee(columnData, keepManuallyAddedFee);
    await updateStateFeeTargetingAndSaveDealData();
  };

  showWarnings = () => {
    GlobalWarning.showWarnings();
  };

  onDaysToFirstPaymentChange = async value => {
    const { columnData, DeskingActions, deal } = this.props;
    const { id } = columnData;
    await DeskingActions.changeDaysToFirstPayment({
      id,
      daysToFirstPayment: Number(value),
    });
    if (DealerPropertyHelper.isAECProgram()) {
      const contractDate = getContractDate(deal);
      const dateForFirstPayment = toMoment(contractDate).add(Number(value), 'days');
      await DeskingActions.patchDeal({
        leaseFirstPaymentDate: getMomentValueOf(dateForFirstPayment),
        leaseFirstPaymentDateSplit: getDateInDayMonthYear(dateForFirstPayment),
      });
    }
    this.showWarnings();
  };

  onAccessoriesDataChange = selectedLender => async newAccessories => {
    const {
      DeskingActions,
      deskingpaymentDetails,
      getMarketScanData,
      columnData,
      deal,
      category3dealTypeChanges,
      updateEMIAmountOfFNIDeal,
      updateDeskingForColumnIds,
      syncPaymentDetails,
      lenders,
    } = this.props;
    const { id } = columnData;
    this.hideModal();

    await DeskingActions.saveAccessories({
      accessories: newAccessories,
      columnIds: [id],
      applicableLender: selectedLender,
      lenders,
    });

    if (isCategory2Deal(deal)) {
      await updateEMIAmountOfFNIDeal();
      return;
    }

    if (isCategory1Deal(deal)) {
      const columnIds = id ? [id] : DeskingReader.getAllCColumnsIDs(deskingpaymentDetails);
      if (DealerPropertyHelper.isCreateDealWithNewAPIEnabled()) {
        await updateDeskingForColumnIds(columnIds);
      } else if (CalcEngineProperties.updateCalculationByBackend()) {
        await syncPaymentDetails();
      } else {
        await getMarketScanData({ columnIds });
      }
    }

    if (isCategory3Deal(deal)) {
      await category3dealTypeChanges();
    }
  };

  editAccessories = () => {
    const accessories = this.getColumnAccessories();
    const { columnData, deal, lenders } = this.props;
    const isLenderSpecificEnabled = ColumnDataReader.isLeaseOrOPLeasePaymentType(columnData);
    const selectedLenderId = ColumnDataReader.getSelectedLenderId(columnData);
    const lender = _find(lenders, ({ id }) => id === selectedLenderId);

    this.updateModal.show({
      component: AccessoriesUpdate,
      componentProps: {
        onCloseModal: this.hideModal,
        accessories,
        onSave: this.onAccessoriesDataChange(lender),
        restoreDefaultDueBills: this.restoreDefaultDueBills,
        isLenderSpecificEnabled,
        deal,
        lender,
      },
    });
  };

  editRebates = () => {
    const { columnData, getMarketScanData, deskingpaymentDetails } = this.props;
    const { id: selectedColumnId } = columnData;
    const columnRebates = ColumnDataReader.getColumnRebates(columnData);
    const isGlobalUpdate = false;
    this.updateModal.show({
      component: Rebate,
      componentProps: {
        columnRebates,
        selectedColumnId,
        onClose: (refresh, showConfirmationModal) => {
          this.onCloseRebateModal(
            refresh,
            showConfirmationModal,
            this.hideModal,
            getMarketScanData,
            deskingpaymentDetails
          );
        },
        isGlobalUpdate,
      },
    });
  };

  getColumnAccessories = () => {
    const { columnData } = this.props;
    return ColumnDataReader.getColumnAccessories(columnData);
  };

  restoreDefaultDueBills = async onRestoreSuccess => {
    const { addDefaultAccessories, columnData } = this.props;
    await addDefaultAccessories({ columnIds: _compact([columnData?.id]) });
    const accessories = this.getColumnAccessories();
    onRestoreSuccess(accessories);
  };

  editFNI = heading => () => {
    const { columnData, DeskingActions, salesSetupInfo } = this.props;
    const fnis = ColumnDataReader.getColumnFNIs(columnData);
    const setupIntegrationKeys = SalesSetupReader.selectIntegrationKeys(salesSetupInfo);

    this.updateModal.show({
      component: FNIUpdate,
      componentProps: {
        onCloseModal: this.hideModal,
        fnis,
        onSave: this.onFNIDataChange,
        restoreFNIdefaults: this.restoreFNIdefaults,
        selectedColumn: columnData?.id,
        setupIntegrationKeys,
        DeskingActions,
        heading,
      },
    });
  };

  onFNIDataChange = async newFNIs => {
    const {
      columnData,
      DeskingActions,
      getMarketScanData,
      deskingpaymentDetails,
      deal,
      updateEMIAmountOfFNIDeal,
      syncPaymentDetails,
      updateDeskingForColumnIds,
    } = this.props;
    const { id } = columnData;

    this.hideModal();
    const dealType = getDealType(deal);

    await DeskingActions.saveFNIs({
      fnis: newFNIs,
      columnIds: [id],
      dealType,
    });

    if (CalcEngineProperties.updateCalculationByBackend()) {
      await syncPaymentDetails();
      return;
    }

    if (isCategory2Deal(deal)) {
      updateEMIAmountOfFNIDeal();
      return;
    }

    if (isCategory1Deal(deal)) {
      const columnIds = id ? [id] : DeskingReader.getAllCColumnsIDs(deskingpaymentDetails);
      if (DealerPropertyHelper.isCreateDealWithNewAPIEnabled()) {
        await updateDeskingForColumnIds(columnIds);
      } else {
        getMarketScanData({
          columnIds,
        });
      }
    }
  };

  restoreFNIdefaults = async onRestoreCallBack => {
    const { addDefaultFNIs, columnData } = this.props;
    await addDefaultFNIs({ columnIds: _compact([columnData?.id]) });

    const fnis = this.getColumnFNIs(columnData);
    onRestoreCallBack(fnis);
  };

  getColumnFNIs = () => {
    const { columnData } = this.props;
    return ColumnDataReader.getColumnFNIsWithoutSPPs(columnData);
  };

  getUpdateModalRef = ref => {
    this.updateModal = ref;
  };

  hideModal = () => this.updateModal.hide();

  filterLenders = (searchText = EMPTY_STRING, option = EMPTY_OBJECT) => {
    const label = _get(option, 'props.label') || EMPTY_STRING;
    if (label.toLowerCase().includes(searchText.toLowerCase())) return true;
    return false;
  };

  getDisplayNamesForFields = () => {
    const { deal, salesSetupInfo } = this.props;
    const dealType = getDealType(deal);
    const showFeesTaxesCat3Deal = DealerConfigReader.getDealerCountryCode() === COUNTRY_CODE.CANADA;
    const fieldsToBeShown = getDealTypeFields(dealType, {
      shouldIncludeFees: showFeesTaxesCat3Deal,
      shouldIncludeTaxes:
        showFeesTaxesCat3Deal || checkTaxRateForWholeSaleDealEnabled(deal, salesSetupInfo) || isDealerTradeDeal(deal),
    });
    const deskingFieldsConfigs = SalesSetupReader.selectDeskingFields(salesSetupInfo);
    return DeskingReader.getDefaultDIsplayNames(deskingFieldsConfigs, fieldsToBeShown);
  };

  isFNIColumnEditable = () => {
    const { deal } = this.props;
    const isPenEnabled = getPenEnabled(deal);
    return !isPenEnabled;
  };

  updateLenderAndTierInColumnData = async (selectedLenderId, selectedProgram, tierChanged) => {
    const { DeskingActions, lenders, columnData } = this.props;
    const { id, selectedTier, termPaymentDetails, selectedLenderId: lenderIdInColumn } = columnData;
    const updatedSelectedTier = tierChanged ? getUpdatedSelectedTier(selectedProgram) : selectedTier;
    const selectedLenderCodeName = getLenderCodeName(lenders, selectedLenderId);
    await DeskingActions.setSelectedLender({
      id,
      selectedLenderId,
      selectedTier: updatedSelectedTier,
      selectedLenderCodeName,
      tierChanged,
      defaultTermPaymentsData: {
        [id]: termPaymentDetails[lenderIdInColumn],
      },
    });
  };

  onSaveSelectedProgramForGalaxyOrCanada = async (columnData, selectedProgram, selectedLenderId, cb) => {
    const { DeskingActions, syncPaymentDetails } = this.props;

    const lenderChanged = isLenderChanged(selectedLenderId, columnData);
    const tierChanged = isTierChanged(selectedProgram, columnData);
    const updatedColumnData = getUpdateTermPaymentDetailsColumnData(columnData, selectedProgram, lenderChanged); // Change ProgramId in columnData
    await DeskingActions.setUpdatedColumnsData([updatedColumnData]); // Update columnData

    if (lenderChanged || tierChanged) {
      await this.updateLenderAndTierInColumnData(selectedLenderId, selectedProgram, tierChanged); // Update Lender
    }
    await syncPaymentDetails();
    cb(); // Close Modal
  };

  onSaveSelectedProgram = columnData => async (selectedProgram, selectedLenderId, cb) => {
    const { DeskingActions, getMarketScanData, lenders, salesSetupInfo, deal } = this.props;

    if (CalcEngineProperties.updateByGalaxyEngine() || isCanadaDealer()) {
      await this.onSaveSelectedProgramForGalaxyOrCanada(columnData, selectedProgram, selectedLenderId, cb);
      if (getDealStatus(deal) !== DEAL_STATUS.BOOKED) {
        const isProgramExpired = await DeskingActions.fetchLenderMetaData(deal);
        if (isProgramExpired) {
          await GlobalWarning.showWarnings();
        }
      }
      return;
    }
    const sanitizedColumnData = getSanitizedColumnData(columnData, selectedProgram);
    const marketScanDataForColumn = _reduce(
      ColumnDataReader.getDownpayments(sanitizedColumnData),
      (acc, dwnPmt) => ({ ...acc, [dwnPmt]: selectedProgram }),
      {}
    );
    // . format selectedProgram and set in column in redux store
    await DeskingActions.setUpdatedColumnsData([
      setMarketScanDataForColumn(
        sanitizedColumnData,
        marketScanDataForColumn,
        true,
        getSelectedLenderDetails(lenders, ColumnDataReader.getSelectedLenderId(sanitizedColumnData)),
        SalesSetupReader.getRegistraionFeeInCap(salesSetupInfo),
        deal
      ),
    ]);

    // Updating daysToFirstPayment here based on IsFordFlexDeal
    const { id } = columnData;
    const isFordFlexDeal = _get(selectedProgram, ['FullDetails', 'Retail', 'IsFordFlexDeal']);
    const daysToFirstPayment = getNumber(ColumnDataReader.getDaysToFirstPayment(columnData));
    const updatedDaysToFirstPayment = isFordFlexDeal && daysToFirstPayment > 31 ? 30 : daysToFirstPayment;
    await DeskingActions.changeDaysToFirstPayment({
      id,
      daysToFirstPayment: updatedDaysToFirstPayment,
    });

    // . setProgManOverridden to true, so next(coz we need to send ford flex related keys in RetailPart.Generic) and all future mscan calls go generic
    await DeskingActions.setProgManOverridden({
      value: true,
      selectedColumnId: sanitizedColumnData?.id,
    });

    // make securityDepositOverridden: true, so the SD value in program will be considered
    await DeskingActions.setSecurityDeposit({
      id,
      securityDepositOverridden: true,
      updateOnlySDOverridden: true,
    });

    // . call mscan and save in desking db
    getMarketScanData({ columnIds: [columnData.id] });
    if (selectedLenderId !== ColumnDataReader.getSelectedLenderId(columnData)) {
      this.onLenderChange(selectedLenderId);
    }
    // . closeModal
    cb();
  };

  getDefaultRates = () => {
    const { columnData, lenders, deal, salesSetupInfo } = this.props;
    const paymentType = ColumnDataReader.getPaymentType(columnData);
    const lenderId = ColumnDataReader.getSelectedLenderId(columnData);
    const matchedLender = findArrayItem(lenders, 'id', lenderId);
    const reserveMethods = LenderReader.getFinanceReserveMethods(matchedLender) || [];
    const types = LenderHelper.mapReserveMethodsToPaymentType(reserveMethods);
    const reserveType = _get(types, [paymentType, 'reserveType']);
    const isFinanceReserveFlatAmount = reserveType === MARKUP_TYPES.FLAT_AMOUNT;
    const isFinanceReservePctOfCapCost = reserveType === MARKUP_TYPES.CAPCOST_BASED;
    const defaultBuyRates = SalesSetupReader.selectDefaultBuyRate(salesSetupInfo);
    const vehicleType = getPrimaryVehicleType(deal);
    const reserveTypeValue = _get(types, [paymentType, 'value']) || '';
    const aprMFTypeForLease = _get(matchedLender, 'aprMFType');
    const isMarkupOverriden = !_isNil(apr) ? false : reserveType === MARKUP_TYPES.MARKUP_BASED;

    const defaultBuyRate = !_isNil(apr)
      ? apr
      : _get(defaultBuyRates, [paymentType, vehicleType || VEHICLE_TYPE.NEW]) || 0;
    const markUp = isMarkupOverriden ? reserveTypeValue : 0;
    const apr = getNumber(defaultBuyRate) + getNumber(markUp);
    const lenderRateType = ColumnDataReader.isLoanOrBalloonPaymentType(columnData)
      ? LENDER_RATE_TYPES.APR
      : aprMFTypeForLease;

    return {
      defaultRates: {
        buyRate: defaultBuyRate,
        buyRateOverridden: !_isNil(defaultBuyRate),
        financeReserveOverridden: isFinanceReserveFlatAmount,
        markUpOverridden: isMarkupOverriden,
        markUp,
        apr: getNumber(defaultBuyRate) + getNumber(markUp),
        financeReserve: isFinanceReserveFlatAmount ? reserveTypeValue : '',
        participation: DeskingReader.getReserveSplitPercent(lenders, lenderId),
        pctOfCapCost: isFinanceReservePctOfCapCost ? reserveTypeValue : '',
        reserveMethod: MARKUP_TYPE_VS_RESERVE_METHOD[reserveType],
        lenderRateType,
        rateType: lenderRateType,
      },
      lenderCodeName: _get(matchedLender, 'label'),
    };
  };

  toggleLeaseComparisonModel = () => {
    this.setState(({ showLeaseComparison }) => ({ showLeaseComparison: !showLeaseComparison }));
  };

  renderDeskingField = fieldId => {
    const {
      columnData,
      lenders,
      activeAccordionKeys,
      DeskingActions,
      deal,
      isDealDirty,
      deskingpaymentDetails,
      salesSetupInfo,
      getFieldStatus,
      paymentOptions,
      savePaymentsDataForSelectedVehicle,
      updatePaymentWithItems,
    } = this.props;
    const { isDisabled, disableInputFieldOnly } = getFieldStatus(fieldId, columnData);
    const { selectedLenderId, selected, paymentOption } = columnData;
    const paymentType = ColumnDataReader.getPaymentType(columnData);
    const viewDemoMileageAdjustmentAs = SalesSetupReader.selectViewDemoMileageAdjustmentAs(salesSetupInfo);
    const lenderInfo = getSelectedLenderDetails(lenders, selectedLenderId) || EMPTY_OBJECT;
    const showAllLenderPrograms = !isDisabled && DealerPropertyHelper.showAllLenderPrograms();
    const isLeasePaymentType = isLeseOrOPLeasePaymentType(paymentType);
    const isCalcEngineEnabled = CalcEngineProperties.showCalcEngineView();
    const isColumnLenderApproved = ColumnDataReader.isColumnLenderApproved(columnData);
    const isPaymentSubTypeGeneric = isGenericPaymentSubType(getPaymentSubType(columnData));
    const isInchcapeEnabled = isInchcape();

    switch (fieldId) {
      case LENDER_PAYMENT_FIELDS.LENDER: {
        const { lenderDN } = this.getDisplayNamesForFields();
        return (
          <PropertyControlledComponent controllerProperty={lenderDN}>
            <InfoCell isColumnSelected={selected} isColumnLenderApproved={isColumnLenderApproved}>
              {ColumnDataReader.isCashPaymentType(columnData) ? (
                <LenderCell
                  DeskingActions={DeskingActions}
                  columnData={columnData}
                  updatePaymentWithItems={updatePaymentWithItems}
                  lendersList={this.getApplicableLenders(lenders, paymentOption)}
                  value={selectedLenderId}
                  onChange={this.onOSFLenderSelected}
                  isLenderSelected={ColumnDataReader.isLenderSelectedForCash(columnData)}
                  disabled={isDisabled}
                />
              ) : (
                <div className={classNames('full-width', 'd-flex')}>
                  <DropDownInfoCell
                    options={getSanitizedLenderOptions(
                      this.getApplicableLenders(lenders, paymentOption),
                      selectedLenderId,
                      ColumnDataReader.getProgramDesc(columnData)
                    )}
                    isColumnSelected={selected}
                    onChange={this.onLenderChange}
                    value={selectedLenderId}
                    filterOption={this.filterLenders}
                    disabled={isDisabled}
                    getPopupContainer={getPopupContainer}
                    containerClassName={classNames({ [styles.lenderSelect]: showAllLenderPrograms })}
                    showSearch
                  />

                  <PropertyControlledComponent controllerProperty={!isInchcapeEnabled && showAllLenderPrograms}>
                    <MSProgramList
                      columnData={columnData}
                      onSaveSelectedProgram={this.onSaveSelectedProgram(columnData)}>
                      <Button className="m-l-8" view={Button.VIEW.ICON} size="S" icon="icon-edit" />
                    </MSProgramList>
                  </PropertyControlledComponent>
                  <PropertyControlledComponent controllerProperty={isInchcapeEnabled}>
                    <ContraSettlementsPopover
                      columnData={columnData}
                      lenders={lenders}
                      onLenderContraSettlementChange={this.onLenderContraSettlementChange}
                      triggerComponent={<Button className="m-l-8" view={Button.VIEW.ICON} size="S" icon="icon-edit" />}
                    />
                  </PropertyControlledComponent>
                </div>
              )}
            </InfoCell>
          </PropertyControlledComponent>
        );
      }
      case LENDER_PAYMENT_FIELDS.APR_MONEYFACTOR: {
        const { aprDN } = this.getDisplayNamesForFields();
        const { defaultRates, lenderCodeName } = this.getDefaultRates();
        return (
          <PropertyControlledComponent controllerProperty={aprDN}>
            {paymentType === PAYMENT_TYPES.CASH ? (
              <FinanceReserveCell
                isColumnSelected={selected}
                paymentOption={paymentOption}
                paymentType={paymentType}
                aprData={ColumnDataReader.getAPRData(columnData)}
                columnId={columnData.id}
                DeskingActions={DeskingActions}
                disabled={isDisabled}
                onSubmit={this.refreshOnSubmit}
                isColumnLenderApproved={isColumnLenderApproved}
              />
            ) : (
              <APRMoneyFactorCell
                isColumnSelected={selected}
                paymentOption={paymentOption}
                aprData={ColumnDataReader.getAPRData(columnData)}
                paymentType={paymentType}
                columnId={columnData.id}
                securityDepositRateAdjustment={ColumnDataReader.getSecurityDepositRateAdjustment(columnData)}
                enableEffectiveInterestRate={SalesSetupReader.enableEffectiveInterestRate(salesSetupInfo)}
                DeskingActions={DeskingActions}
                disabled={isDisabled}
                disableInputFieldOnly={disableInputFieldOnly}
                onSubmit={this.refreshOnSubmit}
                lenderInfo={lenderInfo}
                defaultRates={defaultRates}
                lenderCodeName={lenderCodeName}
                columnData={columnData}
                maxMarkup={getMaxMarkupForSelectedLenderBasedOnPaymentType({
                  columnData,
                  lenders,
                })}
                isColumnLenderApproved={isColumnLenderApproved}
                salesSetupInfo={salesSetupInfo}
              />
            )}
          </PropertyControlledComponent>
        );
      }
      case LENDER_PAYMENT_FIELDS.RESIDUAL: {
        const { residualDN } = this.getDisplayNamesForFields();
        const msrp = getMSRPOfPrimaryVehicle(deal);
        const maxResidualizationMSRP = ColumnDataReader.getMRM(columnData);
        const showMaximumResidualMSRP = SalesSetupReader.showMaximumResidualMSRP(salesSetupInfo);
        return (
          <PropertyControlledComponent controllerProperty={residualDN}>
            <ResidualCell
              isColumnSelected={selected}
              paymentOption={paymentOption}
              data={ColumnDataReader.getResidualData(columnData)}
              totalMileagePenalty={ColumnDataReader.getTotalMileagePenalty(columnData)}
              onChange={this.onResidualChange}
              onResidualDisplayTypeChange={this.onResidualDisplayTypeChange}
              onSubmit={this.refreshOnSubmit}
              disabled={isDisabled}
              disableInputFieldOnly={disableInputFieldOnly}
              onBlur={this.onBlur}
              msrp={msrp}
              maxResidualizationMSRP={maxResidualizationMSRP}
              showMaximumResidualMSRP={showMaximumResidualMSRP}
              isColumnLenderApproved={isColumnLenderApproved}
              savePaymentsDataForSelectedVehicle={savePaymentsDataForSelectedVehicle}
            />
          </PropertyControlledComponent>
        );
      }
      case LENDER_PAYMENT_FIELDS.YEARLY_MILES: {
        const { yearlyMilesDN } = this.getDisplayNamesForFields();
        const isGeneric = ColumnDataReader.isProgramForSelectedLenderGeneric(columnData, isCalcEngineEnabled);
        const yearlyMilesData = ColumnDataReader.getYearlyMilesData(columnData);
        const isOemOfferColumn =
          ColumnDataReader.isOemOfferColumn(columnData) || ColumnDataReader.isOemOfferApprovedLeaseColumn(columnData);
        const isLeaseComparisionEnabled =
          ColumnDataReader.isLeaseOrOPLeasePaymentType(columnData) && DealerPropertyHelper.isLeaseComparisionEnabled();
        return (
          <PropertyControlledComponent controllerProperty={yearlyMilesDN}>
            <div
              className={classNames({
                [styles.yearlyKmsContainer]: isLeaseComparisionEnabled,
              })}>
              <YearlyMilesCell
                isColumnSelected={selected}
                paymentOption={paymentOption}
                onChange={this.onYearlyMilesChange}
                onSubmit={this.refreshOnSubmit}
                disabled={isDisabled}
                isDealDirty={isDealDirty}
                isGeneric={isGeneric}
                yearlyMilesData={yearlyMilesData}
                lenderInfo={this.getSelectedLenderInfo(lenders, selectedLenderId)}
                viewDemoMileageAdjustmentAs={viewDemoMileageAdjustmentAs}
                resetResidual={this.resetResidual}
                isLeaseComparisionEnabled={isLeaseComparisionEnabled}
                isColumnLenderApproved={isColumnLenderApproved}
                DeskingActions={DeskingActions}
                getLabelFromPaymentOptions={getLabelFromPaymentOptions(paymentOptions)}
                isOemOfferColumn={isOemOfferColumn}
              />
              <PropertyControlledComponent controllerProperty={isLeaseComparisionEnabled}>
                <Tooltip title={__('Lease Comparison')} overlayClassName={styles.tooltip}>
                  <Button
                    onClick={this.toggleLeaseComparisonModel}
                    className={classNames(styles.leaseComparisonIcon, {
                      [styles.leaseComparisonIconSelected]: selected,
                    })}
                    view={Button.VIEW.ICON}
                    disabled={isDisabled}
                    size="S"
                    icon="icon-switch"
                  />
                </Tooltip>
              </PropertyControlledComponent>
            </div>
          </PropertyControlledComponent>
        );
      }
      case LENDER_PAYMENT_FIELDS.REBATE: {
        const { rebatesDN } = this.getDisplayNamesForFields();
        const allRebates = DeskingReader.getRebatesSuperset({ deskingpaymentDetails });
        const columnRebates = ColumnDataReader.getColumnRebates(columnData);
        const { validColumnRebates, rebatesTotal } = this.getRebatesForAccordian(allRebates, columnRebates);
        return (
          <PropertyControlledComponent controllerProperty={rebatesDN}>
            <AccordianTextCell
              id="rebate"
              activeKeys={activeAccordionKeys}
              data={validColumnRebates}
              title={rebatesTotal}
              onEdit={this.editRebates}
              isColumnSelected={selected}
              disabled={isRebateEditDisabled(isDisabled, deal)}
            />
          </PropertyControlledComponent>
        );
      }
      case LENDER_PAYMENT_FIELDS.DUEBILLS: {
        const { dueBillsDN } = this.getDisplayNamesForFields();
        const accessoriesSuperSet = DeskingReader.getAccessoriesSuperSet(deskingpaymentDetails);
        const columnAccessories = ColumnDataReader.getColumnAccessories(columnData);
        const { accessories, accessoriesTotal } = this.getAccessoriesForAccordian(
          accessoriesSuperSet,
          columnAccessories
        );
        return (
          <PropertyControlledComponent controllerProperty={dueBillsDN}>
            <AccordianTextCell
              id="accessories"
              activeKeys={activeAccordionKeys}
              isColumnSelected={selected}
              onEdit={this.editAccessories}
              disabled={isDisabled}
              data={accessories}
              title={accessoriesTotal}
              paymentFrequency={ColumnDataReader.getPaymentFrequency(columnData)}
              emi={ColumnDataReader.getTotalEmiForGivenProductType(columnData, LENDER_PAYMENT_FIELDS.DUEBILLS)}
              showEmi={
                SalesSetupReader.showEmiForProductsAndOtherBreakup(salesSetupInfo) &&
                ColumnDataReader.showEmiForGivenProducts(deskingpaymentDetails, LENDER_PAYMENT_FIELDS.DUEBILLS)
              }
            />
          </PropertyControlledComponent>
        );
      }
      case LENDER_PAYMENT_FIELDS.FNIMENU: {
        const { fniMenuDN } = this.getDisplayNamesForFields();
        const fniSuperSet = DeskingReader.getFNISuperSet(deskingpaymentDetails);
        const columnFNIs = ColumnDataReader.getColumnFNIs(columnData);
        const isFniDisabled = isFniUpdateDisabled(isDisabled, salesSetupInfo);
        const { FNIs, FNIsTotal } = this.getFNIsForAccordian(fniSuperSet, columnFNIs, deskingpaymentDetails);

        return (
          <PropertyControlledComponent controllerProperty={fniMenuDN}>
            <AccordianTextCell
              id="finance"
              activeKeys={activeAccordionKeys}
              isColumnSelected={selected}
              onEdit={isFniDisabled ? null : this.editFNI(fniMenuDN)}
              data={FNIs}
              title={FNIsTotal}
              paymentFrequency={ColumnDataReader.getPaymentFrequency(columnData)}
              emi={ColumnDataReader.getTotalEmiForGivenProductType(columnData, LENDER_PAYMENT_FIELDS.FNIMENU)}
              showEmi={
                SalesSetupReader.showEmiForProductsAndOtherBreakup(salesSetupInfo) &&
                ColumnDataReader.showEmiForGivenProducts(deskingpaymentDetails, LENDER_PAYMENT_FIELDS.FNIMENU)
              }
              disabled={isFniDisabled}
            />
          </PropertyControlledComponent>
        );
      }
      case LENDER_PAYMENT_FIELDS.DAYS_TO_FIRSTPAYMENT: {
        const { daysToFirstPayment } = this.getDisplayNamesForFields();
        const contractDate = getContractDate(deal);

        const isDaysToFirstPaymentDisabled =
          isInchcapeOrRRG() && isPaymentSubTypeGeneric
            ? isDisabled
            : isDisabled || (isLeasePaymentType && isCalcEngineEnabled && !CalcEngineProperties.showGalaxyView());

        return (
          <PropertyControlledComponent controllerProperty={daysToFirstPayment}>
            <InfoCell isColumnSelected={selected} isColumnLenderApproved={isColumnLenderApproved}>
              <InputInfoCell
                isColumnSelected={selected}
                type="number"
                value={ColumnDataReader.getDaysToFirstPayment(columnData)}
                onChange={this.onDaysToFirstPaymentChange}
                onPressEnter={this.refreshOnSubmit}
                onBlur={this.onBlur}
                notApplicable={[PAYMENT_TYPES.CASH].includes(paymentType)}
                disabled={isDaysToFirstPaymentDisabled}
                addonAfter={
                  <DaysToFirstPaymentSuffix
                    contractDate={contractDate}
                    daysToFirstPayment={ColumnDataReader.getDaysToFirstPayment(columnData)}
                  />
                }
              />
            </InfoCell>
          </PropertyControlledComponent>
        );
      }
      case LENDER_PAYMENT_FIELDS.FINANCE_CONTRACT_NUMBER: {
        const { financeContractNumber } = getFinanceContractStatusDetails(deskingpaymentDetails, columnData);
        const { financeContractNumberDN } = this.getDisplayNamesForFields();
        return (
          <PropertyControlledComponent controllerProperty={financeContractNumberDN}>
            <InfoCell isColumnSelected={selected} isColumnLenderApproved={isColumnLenderApproved}>
              <TextCell
                text={financeContractNumber}
                thickness={TextCell.THICKNESS.TERTIARY}
                className="full-width text-right-align"
              />
            </InfoCell>
          </PropertyControlledComponent>
        );
      }
      case LENDER_PAYMENT_FIELDS.BUY_BACK_VALUE: {
        const { buyBackValueDN } = this.getDisplayNamesForFields();
        const buybackValue = getBuyBackValuation(deal, columnData);
        return (
          <PropertyControlledComponent controllerProperty={buyBackValueDN}>
            <InfoCell isColumnSelected={selected} isColumnLenderApproved={isColumnLenderApproved}>
              <TextCell
                text={buybackValue}
                thickness={TextCell.THICKNESS.TERTIARY}
                className="full-width text-right-align"
              />
            </InfoCell>
          </PropertyControlledComponent>
        );
      }

      case LENDER_PAYMENT_FIELDS.RATE_OF_INTEREST: {
        const { rateOfInterestDN } = this.getDisplayNamesForFields();
        const rateOfInterestValue = ColumnDataReader.getRateOfInterest(columnData);

        return (
          <PropertyControlledComponent controllerProperty={rateOfInterestDN && isInchcape()}>
            <InfoCell isColumnSelected={selected} isColumnLenderApproved={isColumnLenderApproved}>
              <TextCell
                text={appendPercentage(rateOfInterestValue)}
                thickness={TextCell.THICKNESS.TERTIARY}
                className="full-width text-right-align"
              />
            </InfoCell>
          </PropertyControlledComponent>
        );
      }

      default:
        return (
          <InfoCell
            isColumnSelected={selected}
            isColumnLenderApproved={isColumnLenderApproved}
            notApplicable></InfoCell>
        );
    }
  };

  render() {
    const { showLeaseComparison } = this.state;
    const {
      fieldKeysInOrder,
      columnData,
      lenders,
      deal,
      salesSetupInfo,
      DeskingActions,
      applyColumnDataAndDownpaymntToDesking,
      paymentOptions,
    } = this.props;
    const dealerMarketId = SalesSetupReader.getDealerMarketId(salesSetupInfo);
    return (
      <div className={classNames('marginT16', styles.sectionContainer)}>
        {fieldKeysInOrder.map(fieldId => this.renderDeskingField(fieldId))}

        <UpdateModalStatic ref={this.getUpdateModalRef} onClose={this.hideModal} />
        <PropertyControlledComponent controllerProperty={showLeaseComparison}>
          <LeaseComparison
            showModal={showLeaseComparison}
            columnData={columnData}
            lenders={lenders}
            deal={deal}
            salesSetupInfo={salesSetupInfo}
            onCancel={this.toggleLeaseComparisonModel}
            DeskingActions={DeskingActions}
            applyColumnDataAndDownpaymntToDesking={applyColumnDataAndDownpaymntToDesking}
            dealerMarketId={dealerMarketId}
            getLabelFromPaymentOptions={getLabelFromPaymentOptions(paymentOptions)}
          />
        </PropertyControlledComponent>
      </div>
    );
  }
}

LenderPaymentInfoColumn.defaultProps = {
  columnData: EMPTY_OBJECT,
  DeskingActions: EMPTY_OBJECT,
  getMarketScanData: _noop,
  lenders: EMPTY_ARRAY,
  activeAccordionKeys: EMPTY_ARRAY,
  savePaymentsDataForSelectedVehicle: _noop,
  deskingpaymentDetails: EMPTY_ARRAY,
  getFieldStatus: _noop,
  addDefaultAccessories: _noop,
  addDefaultFNIs: _noop,
  addDefaultFee: _noop,
  isDealDirty: false,
  getDefaultTermPaymentDataForColumn: _noop,
  updateEMIAmountOfFNIDeal: _noop,
  category3dealTypeChanges: _noop,
  syncPaymentDetails: _noop,
  updatePaymentWithItems: _noop,
  handleCustomerApprovalOnRebateUpdate: _noop,
};

LenderPaymentInfoColumn.propTypes = {
  columnData: PropTypes.object,
  DeskingActions: PropTypes.object,
  getMarketScanData: PropTypes.func,
  lenders: PropTypes.array,
  activeAccordionKeys: PropTypes.array,
  savePaymentsDataForSelectedVehicle: PropTypes.func,
  deskingpaymentDetails: PropTypes.array,
  salesSetupInfo: PropTypes.object.isRequired,
  getFieldStatus: PropTypes.func,
  addDefaultAccessories: PropTypes.func,
  addDefaultFNIs: PropTypes.func,
  addDefaultFee: PropTypes.func,
  isDealDirty: PropTypes.bool,
  getDefaultTermPaymentDataForColumn: PropTypes.func,
  deal: PropTypes.object.isRequired,
  updateEMIAmountOfFNIDeal: PropTypes.func,
  category3dealTypeChanges: PropTypes.func,
  fieldKeysInOrder: PropTypes.array.isRequired,
  addDefaultCostAdjustments: PropTypes.func.isRequired,
  updateDeskingForColumnIds: PropTypes.func.isRequired,
  updateStateFeeTargetingAndSaveDealData: PropTypes.func.isRequired,
  saveDealData: PropTypes.func.isRequired,
  applyColumnDataAndDownpaymntToDesking: PropTypes.func.isRequired,
  syncPaymentDetails: PropTypes.func,
  updatePaymentWithItems: PropTypes.func,
  handleCustomerApprovalOnRebateUpdate: PropTypes.func,
};

export default withDeskingContext(withDefault(withHandleCustomerApprovals(LenderPaymentInfoColumn)));
