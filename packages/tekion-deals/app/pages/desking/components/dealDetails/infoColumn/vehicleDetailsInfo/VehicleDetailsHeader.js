/* eslint-disable no-param-reassign */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import _reduce from 'lodash/reduce';
import _map from 'lodash/map';
import _get from 'lodash/get';
import _find from 'lodash/find';
import _size from 'lodash/size';
import _isEmpty from 'lodash/isEmpty';
import cx from 'classnames';
import { connect } from 'react-redux';
import { compose } from 'recompose';

import Content from '@tekion/tekion-components/src/atoms/Content';
import Ellipses from '@tekion/tekion-components/src/atoms/Ellipsis';
import Heading from '@tekion/tekion-components/src/atoms/Heading';
import Button from '@tekion/tekion-components/src/atoms/Button';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import FontIcon, { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';
import DropDown from '@tekion/tekion-components/src/molecules/DropDown';
import Menu from '@tekion/tekion-components/src/molecules/Menu';
import withSize from '@tekion/tekion-components/src/hoc/withSize';
import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { BASE_REDUCER_KEY } from 'constants/constants';
import { getVehiclePOs, getVISettings } from 'commonActions/actions';
import { selectStateListOptions } from 'commonActions/selectors';
import EditVehicleModal from 'organisms/editVehicleModal';
import copyTextToClipboard from '@tekion/tekion-base/utils/copyTextToClipboard';
import GlobalWarning from 'pages/desking/components/globalWarnings';
import {
  getVehicleBasedOnId,
  getPrimaryVehicleDealVehicleId,
  getDealNumber,
} from '@tekion/tekion-base/marketScan/readers/deal.reader';
import UpdateModal from 'organisms/updateModal/UpdateModalStatic';
import UpdateDeal from 'pages/StartDeal';
import { getAllModels, getAllVehicleMakes } from 'pages/StartDeal/startDeal.selector';
import { TAB_KEYS } from '@tekion/tekion-base/marketScan/constants/startDeal.constants';

import { getYearMakeModel, getStockID, getVehicleType, getVin, isTempVehicle } from 'utils/vehicleReader';

import styles from './vehicleDetails.module.scss';
class VehicleDetailsHeader extends PureComponent {
  state = {
    selectedVehicle: null,
  };

  toggleModalVisiblity = () => {
    EditVehicleModal.toggleVisibility();
  };

  onEditVehicleClick = vehicleId => () => {
    this.setState({ selectedVehicle: vehicleId }, this.toggleModalVisiblity);
  };

  copyVin = vin => event => {
    copyTextToClipboard(_get(event, 'domEvent'), document, vin);
  };

  onCheckRecalClick = vehicleId => () => {
    const { showRecallWarning } = this.props;
    showRecallWarning(true, true, GlobalWarning.showWarnings, vehicleId);
  };

  deleteVehicleAndColumns = vehicleId => async () => {
    const { deleteVehicle, savePaymentsDataForSelectedVehicle, saveDealData } = this.props;
    await deleteVehicle({ dealVehicleId: vehicleId });
    savePaymentsDataForSelectedVehicle();
    saveDealData();
  };

  updateDeal = () => {
    const { deal } = this.props;
    const dealNumber = getDealNumber(deal);
    this.updateModal.show({
      component: UpdateDeal,
      componentProps: {
        onCloseModal: this.hideModal,
        onClose: this.hideModal,
        onDealUpdate: this.handleDealUpdate,
        updateDeal: true,
        program: _get(deal, 'program'),
        dealNumber,
        dealStatus: _get(deal, 'status'),
        defaultActiveKey: TAB_KEYS.VEHICLE,
      },
    });
  };

  handleDealUpdate = async ({ vehicles, customers, additionalPayload }) => {
    const { onVehiclesAndCustomersUpdated, addDefaultCostAdjustments } = this.props;
    await onVehiclesAndCustomersUpdated({ vehicles, customers, additionalPayload });
    await addDefaultCostAdjustments();
    this.hideModal();
  };

  getUpdateModalRef = ref => {
    this.updateModal = ref;
  };

  hideModal = () => {
    if (this.updateModal) {
      this.updateModal.hide();
    }
  };

  renderMenu = (vin, vehicleId) => {
    const { deal } = this.props;
    const numberOfVehiclesInDeal = _size(deal.vehicles);
    const primaryVehicleDealVehicleId = getPrimaryVehicleDealVehicleId(deal);

    return (
      <Menu>
        <Menu.Item key="0" className="d-flex" onClick={this.onEditVehicleClick(vehicleId)}>
          <FontIcon size={SIZES.S} className="m-r-12 m-t-4">
            icon-edit
          </FontIcon>
          {__('Edit Vehicle Details')}
        </Menu.Item>

        <Menu.Item key="1" className="d-flex" onClick={this.updateDeal}>
          <FontIcon size={SIZES.S} className="m-r-12 m-t-4">
            icon-repeat
          </FontIcon>
          {__('Switch Vehicle')}
        </Menu.Item>

        <Menu.Item key="2" className="d-flex" onClick={this.copyVin(vin)}>
          <FontIcon size={SIZES.S} className="m-r-12 m-t-4">
            icon-copy
          </FontIcon>
          {__('Copy VIN')}
        </Menu.Item>

        <Menu.Item key="3" className="d-flex" onClick={this.onCheckRecalClick(vehicleId)}>
          <FontIcon size={SIZES.S} className="m-r-12 m-t-4">
            icon-reload
          </FontIcon>
          {__('Check For Recalls')}
        </Menu.Item>
        {numberOfVehiclesInDeal > 1 && vehicleId !== primaryVehicleDealVehicleId && (
          <Menu.Item key="4" className="d-flex" onClick={this.deleteVehicleAndColumns(vehicleId)}>
            <FontIcon size={SIZES.S} className="m-r-12 m-t-4">
              icon-trash
            </FontIcon>
            {__('Delete Vehicle')}
          </Menu.Item>
        )}
      </Menu>
    );
  };

  formattedVehicleData = () => {
    const {
      deskingpaymentDetails,
      deal,
      customStatuses,
      vehiclesInfo,
      CommonActions,
      trimInfos,
      vehicleSubTypesForNewVehicleProgram,
      dealerSites,
      locationCodes,
      contentHeight,
      disablePricingEditSettings,
      isDealViewOnly,
      onVehiclesUpdated,
      setTradeInVehicles,
      isDeskingViewOnly,
      vehicleModels,
      vehicleMakes,
      rvDetails,
      salesSetupInfo,
      stateListOptions,
    } = this.props;

    const { selectedVehicle } = this.state;
    const vehicles = _reduce(
      deskingpaymentDetails,
      (res, { dealVehicleId }) => {
        if (res[dealVehicleId]) res[dealVehicleId] += 1;
        else res[dealVehicleId] = 1;
        return res;
      },
      {}
    );
    const selectedVehicleId = getPrimaryVehicleDealVehicleId(deal);
    const noVehicleSelected = _isEmpty(deal.vehicles) || isTempVehicle(deal.vehicles);

    return _map(Object.keys(vehicles), vehicleId => {
      const vehicleDetails = getVehicleBasedOnId(deal, vehicleId) || {};
      const vehicle = _find(deal.vehicles, ({ dealVehicleId }) => dealVehicleId === vehicleId);
      const vin = getVin(vehicle) || EMPTY_STRING;
      const ymm = getYearMakeModel(vehicleDetails) || EMPTY_STRING;
      const stockId = getStockID(vehicleDetails) || EMPTY_STRING;
      const vehicleType = getVehicleType(vehicleDetails) || EMPTY_STRING;
      const numberOfColumns = vehicles[vehicleId];
      return (
        <div
          className={cx('p-l-12 p-r-4 p-y-4 d-flex justify-content-between', styles.vehicleDeatils, {
            [styles.selectedVehicle]: vehicleId === selectedVehicleId,
          })}
          style={{ width: `${16.2 * numberOfColumns}rem` }}
        >
          <PropertyControlledComponent controllerProperty={!noVehicleSelected}>
            <div>
              <div className="d-flex">
                <Content highlight>
                  <Ellipses length={7 * numberOfColumns} tooltip>
                    {stockId}
                  </Ellipses>
                </Content>
                <Content highlight>{`| ${vehicleType}`}</Content>
              </div>
              <Content>
                <Ellipses length={12 * numberOfColumns} tooltip>
                  {ymm}
                </Ellipses>
              </Content>
            </div>
            {!isDeskingViewOnly && (
              <DropDown overlay={this.renderMenu(vin, vehicleId)} trigger="click">
                <FontIcon className={`cursor-pointer m-t-12 ${styles.vehicleOptionsIcon}`}>icon-vertical-dots</FontIcon>
              </DropDown>
            )}
            {vehicleId === selectedVehicle && (
              <EditVehicleModal
                vin={vin}
                customStatuses={customStatuses}
                deal={deal}
                vehiclesInfo={vehiclesInfo}
                CommonActions={CommonActions}
                vehicleId={_get(vehicle, 'id')}
                dealVehicleId={_get(vehicle, 'dealVehicleId')}
                mileageForResidualCalculation={_get(vehicle, 'mileageForResidualCalculation')}
                options={_get(vehicle, 'options')}
                trimInfos={trimInfos}
                vehicleSubTypesForNewVehicleProgram={vehicleSubTypesForNewVehicleProgram}
                dealerSites={dealerSites}
                locationCodes={locationCodes}
                contentHeight={contentHeight}
                disablePricingEditSettings={disablePricingEditSettings}
                primaryVehicleStatus={_get(vehicle, 'status')}
                isDealViewOnly={isDealViewOnly}
                onVehiclesUpdated={onVehiclesUpdated}
                setTradeInVehicles={setTradeInVehicles}
                vehicleModels={vehicleModels}
                vehicleMakes={vehicleMakes}
                rvDetails={rvDetails}
                salesSetupInfo={salesSetupInfo}
                stateListOptions={stateListOptions}
              />
            )}
          </PropertyControlledComponent>
          <PropertyControlledComponent controllerProperty={noVehicleSelected}>
            <div
              className="cursor-pointer d-flex justify-content-center"
              onClick={this.updateDeal}
              role="button"
              tabIndex="-1"
            >
              <Button view="icon" className="marginR8" onClick={this.updateDeal}>
                <div className="d-flex align-items-center">
                  <FontIcon size="L">icon-steering</FontIcon>
                  <Heading size={5} className="marginL8">
                    {__('Select Vehicle')}
                  </Heading>
                </div>
              </Button>
            </div>
          </PropertyControlledComponent>
        </div>
      );
    });
  };

  render() {
    return (
      <div className={`d-flex ${styles.vehicleDeatilsWrapper}`}>
        {this.formattedVehicleData()}
        <UpdateModal ref={this.getUpdateModalRef} onClose={this.hideModal} />
      </div>
    );
  }
}

VehicleDetailsHeader.propTypes = {
  deskingpaymentDetails: PropTypes.array,
  deal: PropTypes.object,
  customStatuses: PropTypes.array,
  locationCodes: PropTypes.array,
  dealerSites: PropTypes.array,
  vehiclesInfo: PropTypes.array,
  CommonActions: PropTypes.object.isRequired,
  isDealViewOnly: PropTypes.bool,
  trimInfos: PropTypes.object.isRequired,
  disablePricingEditSettings: PropTypes.bool.isRequired,
  showRecallWarning: PropTypes.func.isRequired,
  vehicleSubTypesForNewVehicleProgram: PropTypes.array,
  contentHeight: PropTypes.number.isRequired,
  onVehiclesUpdated: PropTypes.func.isRequired,
  setTradeInVehicles: PropTypes.func.isRequired,
  deleteVehicle: PropTypes.func.isRequired,
  savePaymentsDataForSelectedVehicle: PropTypes.func.isRequired,
  saveDealData: PropTypes.func.isRequired,
  onVehiclesAndCustomersUpdated: PropTypes.func.isRequired,
  addDefaultCostAdjustments: PropTypes.func.isRequired,
  isDeskingViewOnly: PropTypes.bool.isRequired,
  rvDetails: PropTypes.object,
  salesSetupInfo: PropTypes.object,
  stateListOptions: PropTypes.array,
};

VehicleDetailsHeader.defaultProps = {
  deskingpaymentDetails: EMPTY_ARRAY,
  deal: EMPTY_OBJECT,
  customStatuses: EMPTY_ARRAY,
  locationCodes: EMPTY_ARRAY,
  dealerSites: EMPTY_ARRAY,
  vehiclesInfo: EMPTY_ARRAY,
  isDealViewOnly: false,
  vehicleSubTypesForNewVehicleProgram: EMPTY_ARRAY,
  rvDetails: EMPTY_OBJECT,
  salesSetupInfo: EMPTY_OBJECT,
  stateListOptions: EMPTY_ARRAY,
};

const mapStateToProps = globalState => {
  const state = _get(globalState, BASE_REDUCER_KEY);
  return {
    locationCodes: _get(state, 'common.viMetaData.locationCode') || EMPTY_ARRAY,
    customStatuses: _get(state, 'common.viSettings.otherSettings.customStatuses') || EMPTY_ARRAY,
    dealerSites: _get(state, 'common.dealerSites'),
    vehicleModels: getAllModels(state),
    vehicleMakes: getAllVehicleMakes(state),
    rvDetails: _get(state, 'common.viMetaData.rvDetails'),
    stateListOptions: selectStateListOptions(state),
  };
};

const mapDispatchToProps = {
  getVehiclePOs,
  getVISettings,
};

export default compose(
  connect(mapStateToProps, mapDispatchToProps),
  withSize({ hasPageFooter: 1, hasPageHeader: 1 })
)(VehicleDetailsHeader);
