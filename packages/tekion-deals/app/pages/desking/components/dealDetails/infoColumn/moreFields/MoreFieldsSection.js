import React, { PureComponent } from 'react';
import _noop from 'lodash/noop';
import _isNil from 'lodash/isNil';
import _get from 'lodash/get';
import _map from 'lodash/map';
import _first from 'lodash/first';
import classNames from 'classnames';

import PropTypes from 'prop-types';
import Logger from '@tekion/tekion-components/src/atoms/Logger';
import { EMPTY_OBJECT, EMPTY_ARRAY, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { defaultMemoize } from 'reselect';

import UpdateModal from 'organisms/updateModal/UpdateModalStatic';
import Money from '@tekion/tekion-base/utils/money';
import * as SalesSetupReader from '@tekion/tekion-base/marketScan/readers/salesSetup.reader';
import * as ZipCodeTaxReader from '@tekion/tekion-base/marketScan/readers/zipCodeTax.reader';
import FeeUpdate from 'pages/desking/components/feeUpdate';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';
import { COUNTRY_CODE } from '@tekion/tekion-base/constants/countryCode';
import * as DealerConfigReader from 'utils/dealerConfig.reader';
import CalcEngineProperties from 'utils/CalcEngineProperties';
import { isInchcape, isInchcapeOrRRG } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';

import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import withDeskingContext from 'pages/desking/withDeskingContext';
import {
  getDefaultDIsplayNames,
  showTaxesForAccordion,
  getFeeSuperSet,
  getAllCColumnsIDs,
} from '@tekion/tekion-base/marketScan/readers/desking.reader';
import { getPercentString } from '@tekion/tekion-base/formatters/number';
import * as ColumnDataReader from '@tekion/tekion-base/marketScan/readers/columnData.reader';
import * as DealReader from '@tekion/tekion-base/marketScan/readers/deal.reader';
import * as DeskingUtils from '@tekion/tekion-base/marketScan/utils/desking.utils';
import {
  setWaiverProgram,
  setMSDProgram,
  getMsdProgram,
  getFormattedMSDOptions,
  getMSDFieldVisibiltiy,
  isMsdDisabled,
  getWaiverProgram,
  getFormattedWaiverReasonOptions,
  getWaiverFieldVisibility,
} from 'pages/desking/components/dealDetails/infoColumn/moreFields/securityDeposit.utils';
import { getDriveOff, getTotalInterestToDisplay } from '@tekion/tekion-base/marketScan/utils/columnData.util';
import {
  isLeseOrOPLeasePaymentType,
  isCashPaymentType,
  isLoanPaymentType,
} from '@tekion/tekion-base/marketScan/helpers/desking.helpers';
import { formatCurrencyWithMoneyFormat, getNumber, getPopupContainer, getLenderCodeName } from 'utils';
import * as VehicleReader from 'utils/vehicleReader';
import { FEE_TYPES_MAP_API } from 'pages/desking/components/feeUpdate/feeUpdateTable.constants';
import InfoCell from 'pages/desking/components/dealDetails/cells/common/infoCell/index';

import Button from '@tekion/tekion-components/src/atoms/Button';
import { MORE_FIELDS, MISCELLANEOUS_ITEMS } from '@tekion/tekion-base/marketScan/constants/deskingFields.constants';
import SecurityDeposit from 'pages/desking/components/dealDetails/infoColumn/moreFields/SecurityDepositCell';
import { calcTotal } from 'pages/desking/components/dealSummary/dealSummary.reader';
import withDefault from 'pages/desking/withDefault';

import { Case, DefaultCase, SwitchCondition } from '@tekion/tekion-components/src/molecules/SwitchCondition';
import { TAGS } from '@tekion/tekion-base/marketScan/constants/constants';
import {
  PAYMENT_FREQUENCY_OPTIONS_PAYMENT_TYPE,
  PAYMENT_FREQUENCY_OPTIONS_PAYMENT_TYPE_CALC_ENGINE,
} from '@tekion/tekion-base/marketScan/constants/desking.constants';

import {
  PAYMENT_FREQUENCY_DISPLAY_MAP,
  DESKING_FIELD_KEYS,
  ADDITIONAL_SECURITY_DEPOSIT,
  WAIVER_REASON,
} from 'pages/desking/desking.constants';

import salesDealerPropertyHelper from '@tekion/tekion-widgets/src/appServices/sales/helpers/salesDealerPropertyHelper';
import CapCostRollPaymentPopUp from './CapCostRollpaymentPopUp';
import styles from './moreFieldsSection.module.scss';
import DropDownInfoCell from '../../cells/infoCells/dropDownInfoCell';
import AccordianTextCell from '../../cells/infoCells/accordianTextCell';
import InputInfoCell from '../../cells/infoCells/inputInfoCell';
import AccordionConfigCell from '../../cells/infoCells/accordianConfigCell/AccordionConfigCell';
import TextCell from '../../cells/infoCells/textCell';
import {
  getSecDepAndTypeFromLabel,
  getUpdatedFieldKeysInOrder,
  checkSelectedLenderCode,
  checkLenderMBFCA,
} from '../../dealDetails.utils';
import {
  isGenericPaymentSubType,
  isAmountFinancedDeductionForCashEnabled,
  isCanadaCat3Deal,
  checkTaxRateForWholeSaleDealEnabled,
  isDealerTradeDeal,
} from '../../../../../../utils/deal.util';
import { getPaymentSubType } from '../../../../../../readers/columnDataReader';
import { isCanadaDealer } from '../../../../../../utils/dealerUtils';

const { MORE_FIELD_SECTIONN_INFO_COLUMN } = TAGS;

class MoreFieldsSection extends PureComponent {
  getFeesForAccordian = defaultMemoize((allFees, columnFees, manuallyDeletedFees) => {
    const isVehicleProfileEnabled = salesDealerPropertyHelper.isVehicleProfileEnabled();
    const fees =
      DeskingUtils.getColumnFeeFromAllFee(allFees, columnFees, manuallyDeletedFees, isVehicleProfileEnabled) ||
      EMPTY_ARRAY;
    const feesForAccordian = DeskingUtils.removeFeesFromDealFees(fees, FEE_TYPES_MAP_API.leaseContractFees);
    const feesTotal = formatCurrencyWithMoneyFormat(
      feesForAccordian
        .map(({ amount }) => getNumber(amount))
        .reduce((accumulator, currentValue) => Money.add(accumulator, currentValue), 0)
    );

    return {
      fees,
      feesTotal,
    };
  });

  onPaymentFrequencyChange = async value => {
    const {
      columnData,
      DeskingActions,
      getMarketScanData,
      updateDeskingForColumnIds,
      syncPaymentDetails,
      salesSetupInfo,
    } = this.props;
    const { id, paymentOption } = columnData;
    const paymentType = _get(paymentOption, 'paymentType');
    await DeskingActions.setPaymentFrquencyType({ id, newFrequency: value });
    const defaultDaysToFirstPayment = SalesSetupReader.selectDaysToFirstPayment(salesSetupInfo, value);
    await DeskingActions.changeDaysToFirstPayment({
      id,
      daysToFirstPayment: defaultDaysToFirstPayment[paymentType],
    });

    if (DealerPropertyHelper.isCreateDealWithNewAPIEnabled()) {
      updateDeskingForColumnIds([id]);
    } else if (CalcEngineProperties.updateCalculationByBackend()) {
      await syncPaymentDetails();
    } else {
      await getMarketScanData({ columnIds: [id] });
    }
  };

  getPaymentFrequencyList = () => {
    if (CalcEngineProperties.showGalaxyView()) {
      return PAYMENT_FREQUENCY_OPTIONS_PAYMENT_TYPE;
    }
    return CalcEngineProperties.showCalcEngineView()
      ? PAYMENT_FREQUENCY_OPTIONS_PAYMENT_TYPE_CALC_ENGINE(isCanadaDealer())
      : PAYMENT_FREQUENCY_OPTIONS_PAYMENT_TYPE;
  };

  onMSDChange = async msdObj => {
    const { columnData, DeskingActions, syncPaymentDetails } = this.props;
    const { id } = columnData;
    const value = msdObj?.value;
    const selectedNoOfSecurityDepositPayments = _first(value);
    const multipleSecurityDepositProgramList = ColumnDataReader.getMSDValue(columnData);
    const updatedMSDArray = _map(
      multipleSecurityDepositProgramList,
      setMSDProgram(selectedNoOfSecurityDepositPayments)
    );
    await DeskingActions.setMsd({
      id,
      updatedMSDArray,
    });
    await this.onSecurityDepositInputFieldChange(0, false);
    await syncPaymentDetails();
  };

  onWaiverReasonChange = async waiverObj => {
    const { columnData, DeskingActions, syncPaymentDetails } = this.props;
    const value = waiverObj?.value;
    const selectedProgramName = _first(value);
    const { id } = columnData;
    const securityDepositWaiverDetails = ColumnDataReader.getWaiverReasonValue(columnData);
    const updatedWaiverReasonArray = _map(securityDepositWaiverDetails, setWaiverProgram(selectedProgramName));

    await DeskingActions.setWaiverReason({
      id,
      updatedWaiverReasonArray,
    });
    await this.onSecurityDepositInputFieldChange(0, false);

    await syncPaymentDetails();
  };

  getPaymentFrequencyOptions = paymentType => {
    const paymentFrequencyList = this.getPaymentFrequencyList();

    return paymentFrequencyList[paymentType]?.map(paymentFrequency => ({
      label: PAYMENT_FREQUENCY_DISPLAY_MAP[paymentFrequency],
      value: paymentFrequency,
    }));
  };

  getDisplayNamesForFields = () => {
    const { deal, salesSetupInfo } = this.props;
    const dealType = DealReader.getDealType(deal);
    const showFeesTaxesCat3Deal = DealerConfigReader.getDealerCountryCode() === COUNTRY_CODE.CANADA;
    const fieldsToBeShown = DeskingUtils.getDealTypeFields(dealType, {
      shouldIncludeFees: showFeesTaxesCat3Deal,
      shouldIncludeTaxes:
        showFeesTaxesCat3Deal || checkTaxRateForWholeSaleDealEnabled(deal, salesSetupInfo) || isDealerTradeDeal(deal),
    });
    const deskingFieldsConfigs = SalesSetupReader.selectDeskingFields(salesSetupInfo);

    return getDefaultDIsplayNames(deskingFieldsConfigs, fieldsToBeShown);
  };

  getTaxesForAccordian = () => {
    const { columnData, deskingpaymentDetails, deal, salesSetupInfo } = this.props;
    const generalTaxFeeSettings = SalesSetupReader.generalTaxFeeSettings(salesSetupInfo);
    const dealerMarketId = SalesSetupReader.getDealerMarketId(salesSetupInfo);
    const stateFeeTaxOptions = DealReader.getStateFeeTaxOptions(
      deal,
      ColumnDataReader.getColumnTaxAndZipCodeType(columnData)
    );
    const isCategory2Deal = DeskingUtils.isCategory2Deal(deal);
    const isCategory1Deal = DeskingUtils.isCategory1Deal(deal);
    const countryCode = DealerConfigReader.getDealerCountryCode();
    const isGalaxyEnabled = CalcEngineProperties.showGalaxyView();
    const isCalcEngineEnabled = CalcEngineProperties.showCalcEngineView();
    const isTaxBreakUpEnabled = DealerPropertyHelper.isTaxBreakUpEnabled() && DealReader.isTaxBreakUpEnabled(deal);
    const isTaxRateForWholeSaleDealEnabled = checkTaxRateForWholeSaleDealEnabled(deal, salesSetupInfo);
    const isTaxRateForDealerTradeDealEnabled = isDealerTradeDeal(deal);

    return showTaxesForAccordion({
      deskingpaymentDetails,
      deal,
      columnData,
      generalTaxFeeSettings,
      dealerMarketId,
      stateFeeTaxOptions,
      isCategory1Deal,
      isCategory2Deal,
      isCalcEngineEnabled,
      isCanadaCat3Deal: isCanadaCat3Deal(deal),
      isTaxBreakUpEnabled,
      countryCode,
      isTaxRateForWholeSaleDealEnabled,
      isTaxRateForDealerTradeDealEnabled,
      isShowHstSeparately: SalesSetupReader.getShowHstSeparately(salesSetupInfo),
      isGalaxyEnabled,
    });
  };

  onEditColumnFee = () => {
    const { columnData, deal, taxFeeConfigMetadata, getFieldStatus } = this.props;
    const columnTaxAndZipCodeType = ColumnDataReader.getColumnTaxAndZipCodeType(columnData);
    const primaryVehicleInDeal = DealReader.getPrimaryVehicle(deal);
    const stateFeeTaxOptions = DealReader.getStateFeeTaxOptions(deal, columnTaxAndZipCodeType);
    const marketId = DealReader.getMarketId(deal, columnTaxAndZipCodeType);
    const taxAndZipCodeDetails = DealReader.getTaxAndZipCodeDetails(deal, columnTaxAndZipCodeType);
    const stateFeeTaxInfo = ZipCodeTaxReader.getStateFeeTaxInfo(taxAndZipCodeDetails);
    Logger.log('', MORE_FIELD_SECTIONN_INFO_COLUMN, 'onEditColumnFee', {
      columnData,
    });

    const columnFees = this.getColumnFees(true);
    const manuallyDeletedFees = ColumnDataReader.getDeletedColumnFees(columnData) || [];
    const enableSeparateTaxCalc = DealReader.getEnableSeparateTaxCalc(deal);

    const collectRegFee = ColumnDataReader.getCollectRegFee(columnData);
    this.updateModal.show({
      title: __('Fees Update'),
      component: FeeUpdate,
      componentProps: {
        fees: columnFees,
        manuallyDeletedFees,
        onFeeUpdate: this.onFeeUpdate,
        onCloseModal: this.hideModal,
        restoreDefaultFees: this.restoreFees,
        getUpdatedFees: this.getUpdatedFees,
        collectRegFee,
        vehicleType: VehicleReader.getVehicleType(primaryVehicleInDeal),
        stateFeeTaxOptions,
        marketId,
        stateFeeTaxInfo,
        columnTaxAndZipCodeType,
        taxFeeConfigMetadata,
        selectedPaymentType: ColumnDataReader.getPaymentType(columnData),
        isDisabled: getFieldStatus(MISCELLANEOUS_ITEMS.FEE_UPDATE_FORM, columnData)?.isDisabled,
        isGlobalUpdate: false,
        columnId: columnData?.id,
        initialFeesSupersSetIncludingDeletedFees: columnFees,
        type: enableSeparateTaxCalc,
      },
    });
  };

  onFeeUpdate = async (fees, collectRegFee, deletedFees) => {
    const {
      columnData,
      getMarketScanData,
      DeskingActions,
      saveDealData,
      deal,
      updateEMIAmountOfFNIDeal,
      updateDeskingForColumnIds,
      updatePaymentWithItems,
      syncPaymentDetailsWithDealItemsUpdate,
    } = this.props;
    Logger.log('', MORE_FIELD_SECTIONN_INFO_COLUMN, 'onFeeUpdate', { fees });

    const { id } = columnData;

    await DeskingActions.feesSave({
      columnFees: { [id]: fees },
      deletedFees: { [id]: deletedFees },
      collectRegFee,
    });

    if (DealerPropertyHelper.isCreateDealWithNewAPIEnabled()) {
      await saveDealData(); // to save stateFeeTaxOptions
      await updateDeskingForColumnIds([id]);
      this.hideModal();
      return;
    }

    if (CalcEngineProperties.updateByGalaxyEngine()) {
      await syncPaymentDetailsWithDealItemsUpdate({
        overrideFees: DealReader.getOverrideFees(deal),
        stateFeeTax: _get(deal, 'stateFeeTaxOptions', EMPTY_OBJECT),
        taxAndZipCodeDetails: _get(deal, 'taxAndZipCodeDetails', EMPTY_OBJECT),
      });
      this.hideModal();
      return;
    }

    if (CalcEngineProperties.updateCalculationByBackend()) {
      await updatePaymentWithItems({
        dealKeysToCache: ['stateFeeTaxOptions', 'taxAndZipCodeDetails'],
        syncPaymentPayload: { overrideFees: DealReader.getOverrideFees(deal) },
      });
      this.hideModal();
      return;
    }

    saveDealData(); // to save stateFeeTaxOptions
    if (DeskingUtils.isCategory2Deal(deal)) {
      updateEMIAmountOfFNIDeal();
    } else {
      getMarketScanData({ columnIds: [id] });
    }
    this.hideModal();
  };

  removeDeletedFeesFromStore = async () => {
    const { columnData, DeskingActions } = this.props;

    const { id } = columnData;
    const currentFees = this.getColumnFees();

    // remove all deleted fees from LIST
    await DeskingActions.feesSave({
      columnFees: { [id]: currentFees },
      deletedFees: { [id]: [] },
    });
  };

  restoreFees = async (onSuccessCallBack, keepmanuallyAdded) => {
    const {
      addDefaultFee,
      getMarketScanData,
      updateDeskingForColumnIds,
      saveDealData,
      deal,
      updateDealItemPaymentDetails,
    } = this.props;

    await this.removeDeletedFeesFromStore();

    const { columnData } = this.props;
    await addDefaultFee(columnData, keepmanuallyAdded);
    const { id } = columnData;
    if (DealerPropertyHelper.isCreateDealWithNewAPIEnabled()) {
      await saveDealData(); // to save stateFeeTaxOptions
      await updateDeskingForColumnIds([id]);
    } else if (CalcEngineProperties.updateCalculationByBackend()) {
      await updateDealItemPaymentDetails({
        collectFees: DealReader.collectFees(deal),
      });
    } else {
      await getMarketScanData({ columnIds: [id] });
    }
    this.getUpdatedFees(onSuccessCallBack);
  };

  getUpdatedFees = onSuccessCallBack => {
    const newFees = this.getColumnFees();
    const deletedFees = this.getDeletedColumnFees();
    onSuccessCallBack(newFees, deletedFees);
  };

  getColumnFees = () => {
    const { columnData } = this.props;
    return ColumnDataReader.getColumnFees(columnData);
  };

  getDeletedColumnFees = () => {
    const { columnData } = this.props;
    return ColumnDataReader.getDeletedColumnFees(columnData);
  };

  onSecurityDepositSelected = async selectedLabel => {
    const { columnData, DeskingActions, getMarketScanData, updateDeskingForColumnIds, syncPaymentDetails } = this.props;
    const { securityDeposit, securityDepositType } = getSecDepAndTypeFromLabel(selectedLabel);

    const { id } = columnData;
    await DeskingActions.setSecurityDeposit({
      id,
      securityDepositType,
      securityDeposit,
      securityDepositOverridden: !_isNil(securityDeposit),
    });

    if (DealerPropertyHelper.isCreateDealWithNewAPIEnabled()) {
      updateDeskingForColumnIds([id]);
    } else if (CalcEngineProperties.updateCalculationByBackend()) {
      await syncPaymentDetails();
    } else {
      getMarketScanData({ columnIds: [id] });
    }
  };

  getUpdateModalRef = ref => {
    this.updateModal = ref;
  };

  hideModal = () => {
    if (this.updateModal) {
      this.updateModal.hide();
    }
  };

  getDriveOffAmount = () => {
    const { columnData, salesSetupInfo, deal } = this.props;
    const isCalcEngineEnabled = CalcEngineProperties.showCalcEngineView();
    const isCanadaDealerEnabled = isCanadaDealer();
    const isGalaxyEnabled = CalcEngineProperties.showGalaxyView();

    if (isCalcEngineEnabled) {
      const { inceptionFee } = ColumnDataReader.getSelectedDownPaymentInfo(columnData) || EMPTY_OBJECT;
      return formatCurrencyWithMoneyFormat(inceptionFee);
    }
    const generalTaxFeeSettings = SalesSetupReader.generalTaxFeeSettings(salesSetupInfo);
    const dealerMarketId = SalesSetupReader.getDealerMarketId(salesSetupInfo);
    const isTaxBreakUpEnabled = DealerPropertyHelper.isTaxBreakUpEnabled() && DealReader.isTaxBreakUpEnabled(deal);
    const driveOff = getDriveOff(
      columnData,
      null,
      dealerMarketId,
      generalTaxFeeSettings,
      isTaxBreakUpEnabled,
      isCalcEngineEnabled,
      isGalaxyEnabled,
      isCanadaDealerEnabled
    );
    return formatCurrencyWithMoneyFormat(calcTotal(driveOff));
  };

  updateDeskingAfterRollPayment = () => {
    const { getMarketScanData, deskingpaymentDetails, updateDeskingForColumnIds, syncPaymentDetails } = this.props;
    if (DealerPropertyHelper.isCreateDealWithNewAPIEnabled()) {
      const columnIds = getAllCColumnsIDs(deskingpaymentDetails);
      updateDeskingForColumnIds(columnIds);
    } else if (CalcEngineProperties.updateCalculationByBackend()) {
      syncPaymentDetails();
    } else {
      getMarketScanData();
    }
  };

  updateDeskingFieldLocally =
    (fieldKey, isLocatedInTermPayments = false) =>
    async value => {
      const {
        columnData: { id: columnId, selectedLenderId },
        DeskingActions,
        isDealDirty,
      } = this.props;
      if (!isDealDirty) {
        DeskingActions.setDealDirtyStatus(true);
      }

      // @todo this is the default index
      const termPaymentDetailIndex = 0;

      const exactFieldKey = isLocatedInTermPayments
        ? `termPaymentDetails.${selectedLenderId}.${termPaymentDetailIndex}.${DESKING_FIELD_KEYS[fieldKey]}`
        : DESKING_FIELD_KEYS[fieldKey];

      await DeskingActions.setValuesInColumns({
        data: {
          [columnId]: { [exactFieldKey]: value },
        },
      });
    };

  resetSubventionCost = async () => {
    const { syncPaymentDetails } = this.props;
    await this.onChangeSubventionCost(null, false);
    await syncPaymentDetails();
  };

  onSecurityDepositInputFieldChange = async (value, isOverridden = true) => {
    const { columnData, DeskingActions } = this.props;
    const { id } = columnData;
    const previousAprData = ColumnDataReader.getAPRData(columnData);
    await DeskingActions.setSecurityDeposit({
      id,
      securityDepositType: null,
      securityDeposit: value,
      securityDepositOverridden: isOverridden,
    });
    if (isOverridden) {
      await DeskingActions.resetWaiverReason({ id });
      await DeskingActions.resetMSD({ id });
      await DeskingActions.changeAPR({
        id,
        aprData: {
          ...previousAprData,
          buyRateOverriden: false,
        },
      });
    }
  };

  onChangeSubventionCost = async (value, isOverridden = true) => {
    const { columnData, DeskingActions } = this.props;
    const { id } = columnData;
    await DeskingActions.setSubventionCost({
      id,
      subventionCostValueAbsolute: _isNil(value) ? null : value,
      subventionCostOverridden: isOverridden,
    });
  };

  onPressEnterSyncPaymentDetails = async () => {
    const { syncPaymentDetails } = this.props;

    await syncPaymentDetails();
  };

  renderDeskingField = fieldId => {
    const {
      columnData,
      activeAccordionKeys,
      deskingpaymentDetails,
      getMarketScanPayloadObject,
      deferredPayments,
      DeskingActions,
      deal,
      downPayments,
      saveDealData,
      lenders,
      activeSubAccordionKeys,
      isUSCalcEngineEnabled,
      getFieldStatus,
      savePaymentsDataForSelectedVehicle,
      isDeskingViewOnly,
      salesSetupInfo,
    } = this.props;
    const paymentType = ColumnDataReader.getPaymentType(columnData);
    const { selected } = columnData;
    const isCalcEngineEnabled = CalcEngineProperties.showCalcEngineView();
    const isGalaxyEnabled = CalcEngineProperties.showGalaxyView();
    const isColumnLenderApproved = ColumnDataReader.isColumnLenderApproved(columnData);
    const selectedLenderId = ColumnDataReader.getSelectedLenderId(columnData);
    const selectedLenderCodeName = getLenderCodeName(lenders, selectedLenderId);
    const isSelectedLenderMBFCA = checkSelectedLenderCode(selectedLenderCodeName);
    const { isDisabled } = getFieldStatus(fieldId, columnData);
    const securityDepositData = [
      {
        type: ADDITIONAL_SECURITY_DEPOSIT,
        id: ADDITIONAL_SECURITY_DEPOSIT,
        isColumnLenderApproved,
        isColumnSelected: selected,
        value: getMsdProgram(columnData),
        onChange: this.onMSDChange,
        options: getFormattedMSDOptions(columnData),
        toShowField: getMSDFieldVisibiltiy(columnData, lenders),
        isDisabled: isMsdDisabled(columnData) || isDeskingViewOnly,
      },
      {
        type: WAIVER_REASON,
        id: WAIVER_REASON,
        isColumnLenderApproved,
        isColumnSelected: selected,
        value: getWaiverProgram(columnData),
        onChange: this.onWaiverReasonChange,
        options: getFormattedWaiverReasonOptions(columnData),
        toShowField: getWaiverFieldVisibility(columnData, lenders),
        isDisabled: isDeskingViewOnly,
      },
    ];

    // Generic subtype specific
    const isPaymentSubTypeGeneric = isGenericPaymentSubType(getPaymentSubType(columnData));
    const conditionToShowGenericInputFields = isInchcapeOrRRG() && isPaymentSubTypeGeneric;

    switch (fieldId) {
      case MORE_FIELDS.FEES: {
        const { feesDN } = this.getDisplayNamesForFields();
        const feesSuperSet = getFeeSuperSet(deskingpaymentDetails);
        const columnFee = ColumnDataReader.getColumnFees(columnData);
        const manuallyDeletedFees = ColumnDataReader.getDeletedColumnFees(columnData) || [];
        const { fees, feesTotal } = this.getFeesForAccordian(feesSuperSet, columnFee, manuallyDeletedFees);
        return (
          <PropertyControlledComponent controllerProperty={feesDN}>
            <AccordianTextCell
              id="fees"
              activeKeys={activeAccordionKeys}
              isColumnSelected={selected}
              onEdit={this.onEditColumnFee}
              data={fees}
              title={feesTotal}
              paymentFrequency={ColumnDataReader.getPaymentFrequency(columnData)}
              emi={ColumnDataReader.getTotalEmiForGivenProductType(columnData, MORE_FIELDS.FEES)}
              showEmi={
                SalesSetupReader.showEmiForProductsAndOtherBreakup(salesSetupInfo) &&
                ColumnDataReader.showEmiForGivenProducts(deskingpaymentDetails, MORE_FIELDS.FEES)
              }
              disabled={isDisabled}
            />
          </PropertyControlledComponent>
        );
      }
      case MORE_FIELDS.TAXES: {
        const { taxesDN } = this.getDisplayNamesForFields();
        const { infoColumnData: taxAccordianData, totalTax: totalTaxOnAccordian } = this.getTaxesForAccordian();

        return (
          <PropertyControlledComponent controllerProperty={taxesDN}>
            <AccordianTextCell
              id="tax"
              activeKeys={activeAccordionKeys}
              isColumnSelected={selected}
              data={taxAccordianData}
              title={formatCurrencyWithMoneyFormat(totalTaxOnAccordian)}
              disabled={isDisabled}
              activeSubAccordionKeys={activeSubAccordionKeys.tax}
            />
          </PropertyControlledComponent>
        );
      }
      case MORE_FIELDS.SECURITY_DEPOSIT: {
        const { securityDepositDN } = this.getDisplayNamesForFields();
        const isLeaseOrOpLeasePaymentTypeBool = isLeseOrOPLeasePaymentType(paymentType);
        const securityDeposits = ColumnDataReader.getSecurityDeposits(columnData);
        const securityDeposit = ColumnDataReader.getSecurityDeposit(columnData);
        const securityDepositType = ColumnDataReader.getSecurityDepositType(columnData);
        const disabledValue = isDisabled || !isLeaseOrOpLeasePaymentTypeBool;
        const placeholder = disabledValue ? __('N/A') : __('0.00');

        return (
          <PropertyControlledComponent controllerProperty={securityDepositDN}>
            <PropertyControlledComponent controllerProperty={isCanadaDealer()}>
              <AccordionConfigCell
                style={{ padding: 0 }}
                id="sdeposit"
                activeKeys={activeAccordionKeys}
                isColumnSelected={selected}
                data={securityDepositData}
                title={
                  <InfoCell isColumnLenderApproved={isColumnLenderApproved} isColumnSelected={selected}>
                    <div
                      className={classNames(styles.inputBox, {
                        [styles.disabled]: disabledValue,
                      })}>
                      <InputInfoCell
                        isColumnSelected={selected}
                        value={securityDeposit}
                        onChange={this.onSecurityDepositInputFieldChange}
                        onPressEnter={this.onPressEnterSyncPaymentDetails}
                        onBlur={this.onPressEnterSyncPaymentDetails}
                        showBorder
                        disabled={disabledValue}
                        placeholder={placeholder}
                        showCurrencyInput
                      />
                    </div>
                  </InfoCell>
                }
              />
            </PropertyControlledComponent>

            <PropertyControlledComponent controllerProperty={!isCanadaDealer()}>
              <InfoCell
                notApplicable={!isLeaseOrOpLeasePaymentTypeBool}
                isColumnLenderApproved={isColumnLenderApproved}
                isColumnSelected={selected}>
                <SecurityDeposit
                  options={DeskingUtils.getSecurityDepositOptions(securityDeposits)}
                  isColumnSelected={selected}
                  securityDeposit={DeskingUtils.getSecDepValForDropdown(securityDepositType, securityDeposit)}
                  onChange={this.onSecurityDepositSelected}
                  disabled={isDisabled}
                />
              </InfoCell>
            </PropertyControlledComponent>
          </PropertyControlledComponent>
        );
      }
      case MORE_FIELDS.PAYMENT_FREQUENCY: {
        const { paymentFrequencyDN } = this.getDisplayNamesForFields();
        const paymentFrequencyOptions = this.getPaymentFrequencyOptions(paymentType);
        return (
          <PropertyControlledComponent controllerProperty={paymentFrequencyDN}>
            <InfoCell isColumnSelected={selected} isColumnLenderApproved={isColumnLenderApproved}>
              <DropDownInfoCell
                options={paymentFrequencyOptions}
                isColumnSelected={selected}
                value={ColumnDataReader.getPaymentFrequency(columnData)}
                disabled={isInchcape() ? true : paymentFrequencyOptions?.length <= 1 || isDisabled}
                onChange={this.onPaymentFrequencyChange}
                getPopupContainer={getPopupContainer}
              />
            </InfoCell>
          </PropertyControlledComponent>
        );
      }
      case MORE_FIELDS.FINANCE_ADJUSTED_CAPITAL_COST: {
        const { salesSetupInfo } = this.props;
        const { amountFinancedDN } = this.getDisplayNamesForFields();
        const isDeductCustomerCashfromAmountFinancedforCashDealEnabled =
          SalesSetupReader.deductCustomerCashfromAmountFinancedforCashDealEnabled(salesSetupInfo);
        const isAmountFinancedDeductionForCashEnabledFlag = isAmountFinancedDeductionForCashEnabled(
          deal,
          isDeductCustomerCashfromAmountFinancedforCashDealEnabled
        );

        const amountFinanced = ColumnDataReader.getAmountFinanacedToDisplay(
          columnData,
          null, // downPaymentId
          isAmountFinancedDeductionForCashEnabledFlag
        );
        const displayedAmountFinanced = amountFinanced;
        const hideEditButton = isDisabled || (isCalcEngineEnabled && !isGalaxyEnabled);

        return (
          <PropertyControlledComponent controllerProperty={amountFinancedDN}>
            <InfoCell
              isColumnSelected={selected}
              className={styles.rollPaymentContainer}
              isColumnLenderApproved={isColumnLenderApproved}>
              <SwitchCondition>
                <Case condition={conditionToShowGenericInputFields}>
                  <InputInfoCell
                    isColumnSelected={columnData.selected}
                    value={ColumnDataReader.getAmountFinanaced(columnData)}
                    onChange={this.updateDeskingFieldLocally(MORE_FIELDS.FINANCE_ADJUSTED_CAPITAL_COST, true)}
                    onPressEnter={savePaymentsDataForSelectedVehicle}
                    onBlur={savePaymentsDataForSelectedVehicle}
                    disabled={isDisabled} // @todo there is other disable statement, check which one to use
                    showCurrencyInput
                  />
                </Case>
                <DefaultCase>
                  <CapCostRollPaymentPopUp
                    columnData={columnData}
                    getMarketScanPayloadObject={getMarketScanPayloadObject}
                    downPayments={downPayments}
                    getMarketScanData={this.updateDeskingAfterRollPayment}
                    deferredPayments={deferredPayments}
                    DeskingActions={DeskingActions}
                    deal={deal}
                    saveDealData={saveDealData}
                    isUSCalcEngineEnabled={isUSCalcEngineEnabled}>
                    {hideEditButton ? null : (
                      <Button view={Button.VIEW.ICON} size="S" icon="icon-edit" className={styles.icon} />
                    )}
                  </CapCostRollPaymentPopUp>
                  <TextCell
                    text={displayedAmountFinanced ? formatCurrencyWithMoneyFormat(displayedAmountFinanced) : ''}
                    thickness={TextCell.THICKNESS.TERTIARY}
                    className="full-width text-right-align marginL8"
                  />
                </DefaultCase>
              </SwitchCondition>
            </InfoCell>
          </PropertyControlledComponent>
        );
      }
      case MORE_FIELDS.LOAN_TO_VALUE_RATIO: {
        const { loanTOValue } = this.getDisplayNamesForFields();
        const loanToValueRatio = ColumnDataReader.getLoanToValueRatio(columnData);
        return (
          <PropertyControlledComponent controllerProperty={loanTOValue}>
            <InfoCell isColumnSelected={selected} isColumnLenderApproved={isColumnLenderApproved}>
              <TextCell
                text={loanToValueRatio && !isCashPaymentType(paymentType) ? getPercentString(loanToValueRatio) : ''}
                thickness={TextCell.THICKNESS.TERTIARY}
                className="full-width text-right-align"
              />
            </InfoCell>
          </PropertyControlledComponent>
        );
      }
      case MORE_FIELDS.DRIVE_OFF: {
        const { driveOffDN } = this.getDisplayNamesForFields();
        const inceptionFee = ColumnDataReader.getInceptionFee(columnData);

        return (
          <PropertyControlledComponent controllerProperty={driveOffDN}>
            <InfoCell isColumnSelected={selected} isColumnLenderApproved={isColumnLenderApproved}>
              <SwitchCondition>
                <Case condition={conditionToShowGenericInputFields}>
                  <InputInfoCell
                    isColumnSelected={columnData.selected}
                    value={inceptionFee}
                    onChange={this.updateDeskingFieldLocally(MORE_FIELDS.DRIVE_OFF, true)}
                    onPressEnter={savePaymentsDataForSelectedVehicle}
                    onBlur={savePaymentsDataForSelectedVehicle}
                    disabled={isDisabled}
                    showCurrencyInput
                  />
                </Case>
                <DefaultCase>
                  <TextCell
                    text={isLeseOrOPLeasePaymentType(paymentType) ? this.getDriveOffAmount() : EMPTY_STRING}
                    thickness={TextCell.THICKNESS.TERTIARY}
                    className="full-width text-right-align"
                  />
                </DefaultCase>
              </SwitchCondition>
            </InfoCell>
          </PropertyControlledComponent>
        );
      }

      case MORE_FIELDS.OUT_OF_POCKET_CASH: {
        const { outOfPocketCashDN } = this.getDisplayNamesForFields();
        const outOfPocketCashValue = ColumnDataReader.getSelectedOutOfPocketCash(columnData);
        return (
          <PropertyControlledComponent controllerProperty={outOfPocketCashDN}>
            <InfoCell isColumnSelected={selected} isColumnLenderApproved={isColumnLenderApproved}>
              <SwitchCondition>
                <Case condition={conditionToShowGenericInputFields}>
                  <InputInfoCell
                    isColumnSelected={columnData.selected}
                    value={outOfPocketCashValue || 0}
                    onChange={this.updateDeskingFieldLocally(MORE_FIELDS.OUT_OF_POCKET_CASH, true)}
                    onPressEnter={savePaymentsDataForSelectedVehicle}
                    onBlur={savePaymentsDataForSelectedVehicle}
                    disabled={isDisabled}
                    showCurrencyInput
                  />
                </Case>
                <DefaultCase>
                  <TextCell
                    text={
                      formatCurrencyWithMoneyFormat(ColumnDataReader.getSelectedOutOfPocketCash(columnData)) ||
                      EMPTY_STRING
                    }
                    thickness={TextCell.THICKNESS.TERTIARY}
                    className="full-width text-right-align"
                  />
                </DefaultCase>
              </SwitchCondition>
            </InfoCell>
          </PropertyControlledComponent>
        );
      }

      case MORE_FIELDS.FINANCE_CHARGE: {
        const { financeChargeDN } = this.getDisplayNamesForFields();
        const lender = ColumnDataReader.getSelectedLenderId(columnData);
        const lenderInfo = DeskingUtils.getSelectedLenderDetails(lenders, lender) || EMPTY_OBJECT;
        const displayText = formatCurrencyWithMoneyFormat(
          getTotalInterestToDisplay(columnData, deal, isCalcEngineEnabled, lenderInfo, isGalaxyEnabled)
        );

        const financeChargeValue = ColumnDataReader.getFinanceChargeFromCalcEngine(columnData);
        return (
          <PropertyControlledComponent controllerProperty={financeChargeDN}>
            <InfoCell isColumnSelected={selected} isColumnLenderApproved={isColumnLenderApproved}>
              <SwitchCondition>
                <Case condition={conditionToShowGenericInputFields}>
                  <InputInfoCell
                    isColumnSelected={columnData.selected}
                    value={financeChargeValue}
                    onChange={this.updateDeskingFieldLocally(MORE_FIELDS.FINANCE_CHARGE, true)}
                    onPressEnter={savePaymentsDataForSelectedVehicle}
                    onBlur={savePaymentsDataForSelectedVehicle}
                    disabled={isDisabled}
                    showCurrencyInput
                  />
                </Case>
                <DefaultCase>
                  <TextCell
                    text={displayText}
                    thickness={TextCell.THICKNESS.TERTIARY}
                    className="full-width text-right-align"
                  />
                </DefaultCase>
              </SwitchCondition>
            </InfoCell>
          </PropertyControlledComponent>
        );
      }

      case MORE_FIELDS.SUBVENTION_COST: {
        const subventionCost = ColumnDataReader.getSubventionCost(columnData);
        const isLoanLeaseOrOpLeasePaymentType =
          isLeseOrOPLeasePaymentType(paymentType) || isLoanPaymentType(paymentType);
        const isSubventionCostVisibile = checkLenderMBFCA({ deskingpaymentDetails, lenders }) && isCanadaDealer();

        return (
          <PropertyControlledComponent controllerProperty={isSubventionCostVisibile}>
            <InfoCell isColumnSelected={selected} isColumnLenderApproved={isColumnLenderApproved}>
              <SwitchCondition>
                <Case condition={isSelectedLenderMBFCA && isLoanLeaseOrOpLeasePaymentType}>
                  <InputInfoCell
                    isColumnSelected={columnData.selected}
                    value={subventionCost}
                    onChange={this.onChangeSubventionCost}
                    onPressEnter={this.onPressEnterSyncPaymentDetails}
                    onBlur={this.onPressEnterSyncPaymentDetails}
                    disabled={false}
                    className={styles.subventionInput}
                    showCurrencyInput
                  />
                  <Button
                    onClick={this.resetSubventionCost}
                    view={Button.VIEW.ICON}
                    size="S"
                    icon="icon-refresh"
                    disabled={false}
                    className={styles.resetIcon}
                  />
                </Case>
                <DefaultCase>
                  <TextCell
                    text={EMPTY_STRING}
                    thickness={TextCell.THICKNESS.TERTIARY}
                    className="full-width text-right-align"
                  />
                </DefaultCase>
              </SwitchCondition>
            </InfoCell>
          </PropertyControlledComponent>
        );
      }

      case MORE_FIELDS.TOTAL_DEPOSIT: {
        const { totalDepositDN } = this.getDisplayNamesForFields();
        const totalDepositValue = ColumnDataReader.getTotalDeposit(columnData);
        return (
          <PropertyControlledComponent controllerProperty={totalDepositDN && isInchcape()}>
            <InfoCell isColumnSelected={selected} isColumnLenderApproved={isColumnLenderApproved}>
              <SwitchCondition>
                <Case condition={conditionToShowGenericInputFields}>
                  <InputInfoCell
                    isColumnSelected={columnData.selected}
                    value={totalDepositValue}
                    onChange={this.updateDeskingFieldLocally(MORE_FIELDS.TOTAL_DEPOSIT, true)}
                    onPressEnter={savePaymentsDataForSelectedVehicle}
                    onBlur={savePaymentsDataForSelectedVehicle}
                    disabled={isDisabled}
                    showCurrencyInput
                  />
                </Case>
                <DefaultCase>
                  <TextCell
                    text={formatCurrencyWithMoneyFormat(totalDepositValue) || EMPTY_STRING}
                    thickness={TextCell.THICKNESS.TERTIARY}
                    className="full-width text-right-align"
                  />
                </DefaultCase>
              </SwitchCondition>
            </InfoCell>
          </PropertyControlledComponent>
        );
      }

      case MORE_FIELDS.TOTAL_AMOUNT_PAYABLE: {
        const { totalAmountPayableDN } = this.getDisplayNamesForFields();
        const totalAmountPayableValue = ColumnDataReader.getTotalAmountPayable(columnData);
        return (
          <PropertyControlledComponent controllerProperty={totalAmountPayableDN && isInchcape()}>
            <InfoCell isColumnSelected={selected} isColumnLenderApproved={isColumnLenderApproved}>
              <SwitchCondition>
                <Case condition={conditionToShowGenericInputFields}>
                  <InputInfoCell
                    isColumnSelected={columnData.selected}
                    value={totalAmountPayableValue}
                    onChange={this.updateDeskingFieldLocally(MORE_FIELDS.TOTAL_AMOUNT_PAYABLE, true)}
                    onPressEnter={savePaymentsDataForSelectedVehicle}
                    onBlur={savePaymentsDataForSelectedVehicle}
                    disabled={isDisabled}
                    showCurrencyInput
                  />
                </Case>
                <DefaultCase>
                  <TextCell
                    text={formatCurrencyWithMoneyFormat(totalAmountPayableValue) || EMPTY_STRING}
                    thickness={TextCell.THICKNESS.TERTIARY}
                    className="full-width text-right-align"
                  />
                </DefaultCase>
              </SwitchCondition>
            </InfoCell>
          </PropertyControlledComponent>
        );
      }

      case MORE_FIELDS.EXCESS_MILEAGE_CHARGE: {
        const { excessMileageChargeDN } = this.getDisplayNamesForFields();
        const excessMileageChargeValue = ColumnDataReader.getPenaltyPerMile(columnData);

        return (
          <PropertyControlledComponent controllerProperty={excessMileageChargeDN && isInchcape()}>
            <InfoCell isColumnSelected={selected} isColumnLenderApproved={isColumnLenderApproved}>
              <SwitchCondition>
                <Case condition={conditionToShowGenericInputFields}>
                  <InputInfoCell
                    isColumnSelected={columnData.selected}
                    value={excessMileageChargeValue}
                    onChange={this.updateDeskingFieldLocally(MORE_FIELDS.EXCESS_MILEAGE_CHARGE, true)}
                    onPressEnter={savePaymentsDataForSelectedVehicle}
                    onBlur={savePaymentsDataForSelectedVehicle}
                    disabled={isDisabled}
                    showCurrencyInput
                  />
                </Case>
                <DefaultCase>
                  <TextCell
                    text={formatCurrencyWithMoneyFormat(excessMileageChargeValue) || EMPTY_STRING}
                    thickness={TextCell.THICKNESS.TERTIARY}
                    className="full-width text-right-align"
                  />
                </DefaultCase>
              </SwitchCondition>
            </InfoCell>
          </PropertyControlledComponent>
        );
      }

      case MORE_FIELDS.OPTION_TO_PURCHASE_FEE: {
        const { optionToPurchaseFeeDN } = this.getDisplayNamesForFields();
        const optionToPurchaseFeeValue = ColumnDataReader.getOptionToPurchaseFee(columnData);
        return (
          <PropertyControlledComponent controllerProperty={optionToPurchaseFeeDN && isInchcape()}>
            <InfoCell isColumnSelected={selected} isColumnLenderApproved={isColumnLenderApproved}>
              <SwitchCondition>
                <Case condition={conditionToShowGenericInputFields}>
                  <InputInfoCell
                    isColumnSelected={columnData.selected}
                    value={optionToPurchaseFeeValue}
                    onChange={this.updateDeskingFieldLocally(MORE_FIELDS.OPTION_TO_PURCHASE_FEE, true)}
                    onPressEnter={savePaymentsDataForSelectedVehicle}
                    onBlur={savePaymentsDataForSelectedVehicle}
                    disabled={isDisabled}
                    showCurrencyInput
                  />
                </Case>
                <DefaultCase>
                  <TextCell
                    text={formatCurrencyWithMoneyFormat(optionToPurchaseFeeValue) || EMPTY_STRING}
                    thickness={TextCell.THICKNESS.TERTIARY}
                    className="full-width text-right-align"
                  />
                </DefaultCase>
              </SwitchCondition>
            </InfoCell>
          </PropertyControlledComponent>
        );
      }

      case MORE_FIELDS.OPTIONAL_FINAL_PAYMENT: {
        const { optionalFinalPaymentDN } = this.getDisplayNamesForFields();
        const optionalFinalPaymentValue = ColumnDataReader.getOptionalFinalPayment(columnData);
        return (
          <PropertyControlledComponent controllerProperty={optionalFinalPaymentDN && isInchcape()}>
            <InfoCell isColumnSelected={selected} isColumnLenderApproved={isColumnLenderApproved}>
              <SwitchCondition>
                <Case condition={conditionToShowGenericInputFields}>
                  <InputInfoCell
                    isColumnSelected={columnData.selected}
                    value={optionalFinalPaymentValue}
                    onChange={this.updateDeskingFieldLocally(MORE_FIELDS.OPTIONAL_FINAL_PAYMENT, true)}
                    onPressEnter={savePaymentsDataForSelectedVehicle}
                    onBlur={savePaymentsDataForSelectedVehicle}
                    disabled={isDisabled}
                    showCurrencyInput
                  />
                </Case>
                <DefaultCase>
                  <TextCell
                    text={formatCurrencyWithMoneyFormat(optionalFinalPaymentValue) || EMPTY_STRING}
                    thickness={TextCell.THICKNESS.TERTIARY}
                    className="full-width text-right-align"
                  />
                </DefaultCase>
              </SwitchCondition>
            </InfoCell>
          </PropertyControlledComponent>
        );
      }

      case MORE_FIELDS.TOTAL_DUE_AT_SIGNING: {
        const { totalDueAtSigninDN } = this.getDisplayNamesForFields();
        const columnData = ColumnDataReader.getSelectedDownPaymentInfo(columnData) || EMPTY_OBJECT;
        const dueFromCustomer = _get(columnData, DESKING_FIELD_KEYS[MORE_FIELDS.TOTAL_DUE_AT_SIGNING]);
        return (
          <PropertyControlledComponent controllerProperty={totalDueAtSigninDN}>
            <InfoCell isColumnSelected={selected} isColumnLenderApproved={isColumnLenderApproved}>
              <TextCell
                text={formatCurrencyWithMoneyFormat(dueFromCustomer) || EMPTY_STRING}
                thickness={TextCell.THICKNESS.TERTIARY}
                className="full-width text-right-align"
              />
            </InfoCell>
          </PropertyControlledComponent>
        );
      }

      default:
        return null;
    }
  };

  render() {
    const { fieldKeysInOrder, deskingpaymentDetails, lenders } = this.props;
    const updatedFieldKeysInOrder = getUpdatedFieldKeysInOrder({ fieldKeysInOrder, deskingpaymentDetails, lenders });
    return (
      <React.Fragment>
        <div className={styles.container}>
          {updatedFieldKeysInOrder?.map(fieldId => this.renderDeskingField(fieldId))}
        </div>
        <UpdateModal ref={this.getUpdateModalRef} onClose={this.hideModal} />
      </React.Fragment>
    );
  }
}

export default withDeskingContext(withDefault(MoreFieldsSection));

MoreFieldsSection.propTypes = {
  columnData: PropTypes.object.isRequired,
  DeskingActions: PropTypes.object.isRequired,
  activeAccordionKeys: PropTypes.array.isRequired,
  salesSetupInfo: PropTypes.object.isRequired,
  deskingpaymentDetails: PropTypes.array.isRequired,
  addDefaultFee: PropTypes.func,
  deal: PropTypes.object,
  deferredPayments: PropTypes.object,
  getMarketScanData: PropTypes.func,
  downPayments: PropTypes.array,
  getMarketScanPayloadObject: PropTypes.func,
  saveDealData: PropTypes.func.isRequired,
  fieldKeysInOrder: PropTypes.array.isRequired,
  updateDeskingForColumnIds: PropTypes.func.isRequired,
  taxFeeConfigMetadata: PropTypes.object.isRequired,
  lenders: PropTypes.array,
  isUSCalcEngineEnabled: PropTypes.bool.isRequired,
  isDealDirty: PropTypes.bool,
  savePaymentsDataForSelectedVehicle: PropTypes.func,
  isDeskingViewOnly: PropTypes.bool.isRequired,
};

MoreFieldsSection.defaultProps = {
  addDefaultFee: _noop,
  deal: EMPTY_OBJECT,
  getMarketScanData: _noop,
  downPayments: EMPTY_ARRAY,
  deferredPayments: EMPTY_OBJECT,
  getMarketScanPayloadObject: _noop,
  lenders: EMPTY_ARRAY,
  isDealDirty: false,
  savePaymentsDataForSelectedVehicle: _noop,
};
