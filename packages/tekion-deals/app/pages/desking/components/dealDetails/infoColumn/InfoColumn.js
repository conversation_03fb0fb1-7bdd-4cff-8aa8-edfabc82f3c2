import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import _noop from 'lodash/noop';
import _filter from 'lodash/filter';
import cx from 'classnames';

import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';

import { EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import * as ColumnDataReader from '@tekion/tekion-base/marketScan/readers/columnData.reader';

import TermHeader from './termHeader';
import PaymentTypeColumn from './paymentTypeColumn';
import MoreFieldsSection from './moreFields';
import SppSection from './spp';
import SellingPrice from './sellingPrice';
import LenderPaymentInfoColumn from './lenderPayment';
import styles from './infoColumn.module.scss';

export default class InfoColumn extends PureComponent {
  componentDidMount() {}

  render() {
    const {
      columnData,
      activeAccordionKeys,
      onAccordionClick,
      taxFeeConfigMetadata,
      deal,
      DeskingActions,
      onDuplicateColumn,
      deskingFieldsOrderInfo: { LENDER_PAYMENT_SECTION, MORE_FIELDS_SECTION },
      isMultiVehicleDeskingEnabledV2,
      addColumn,
      onDownPaymentSelect,
      isDealDirty,
      deskingpaymentDetails,
      saveSellingPrice,
      isDeskingViewOnly,
      updateStateFeeTargetingAndSaveDealData,
      activeSubAccordionKeys,
      isUSCalcEngineEnabled,
      onOpenLenderComparison,
      applyColumnDataAndDownpaymntToDesking,
      paymentOptions,
    } = this.props;
    const columnsOfSameVehicle = _filter(
      deskingpaymentDetails,
      ({ dealVehicleId }) => dealVehicleId === columnData.dealVehicleId
    );
    const isOemOfferColumn = ColumnDataReader.isOemOfferColumn(columnData);

    if (!columnData) return null;
    return (
      <div
        className={cx({
          [styles.oemOfferColumnContainer]: isOemOfferColumn,
        })}
      >
        <div
          className={cx(styles.stickyContainer, {
            [styles.multiDeskingEnabled]: isMultiVehicleDeskingEnabledV2,
          })}
        >
          <TermHeader
            paymentOptions={paymentOptions}
            columnData={columnData}
            columnsCount={columnsOfSameVehicle.length}
            addColumn={addColumn}
            onDuplicateColumn={onDuplicateColumn}
            onOpenLenderComparison={onOpenLenderComparison}
          />
          <PropertyControlledComponent controllerProperty={isMultiVehicleDeskingEnabledV2}>
            <SellingPrice
              columnData={columnData}
              deal={deal}
              DeskingActions={DeskingActions}
              saveSellingPrice={saveSellingPrice}
              isDealDirty={isDealDirty}
              deskingpaymentDetails={deskingpaymentDetails}
              isDeskingViewOnly={isDeskingViewOnly}
            />
          </PropertyControlledComponent>
          <PaymentTypeColumn
            columnData={columnData}
            onDownPaymentSelect={onDownPaymentSelect}
            isUSCalcEngineEnabled={isUSCalcEngineEnabled}
          />
        </div>
        <LenderPaymentInfoColumn
          paymentOptions={paymentOptions}
          columnData={columnData}
          activeAccordionKeys={activeAccordionKeys}
          onAccordionClick={onAccordionClick}
          fieldKeysInOrder={LENDER_PAYMENT_SECTION}
          updateStateFeeTargetingAndSaveDealData={updateStateFeeTargetingAndSaveDealData}
          applyColumnDataAndDownpaymntToDesking={applyColumnDataAndDownpaymntToDesking}
        />
        <MoreFieldsSection
          columnData={columnData}
          activeAccordionKeys={activeAccordionKeys}
          onAccordionClick={onAccordionClick}
          fieldKeysInOrder={MORE_FIELDS_SECTION}
          taxFeeConfigMetadata={taxFeeConfigMetadata}
          activeSubAccordionKeys={activeSubAccordionKeys}
          isUSCalcEngineEnabled={isUSCalcEngineEnabled}
          isDeskingViewOnly={isDeskingViewOnly}
        />
        <SppSection
          columnData={columnData}
          activeAccordionKeys={activeAccordionKeys}
          onAccordionClick={onAccordionClick}
          fieldKeysInOrder={MORE_FIELDS_SECTION}
          taxFeeConfigMetadata={taxFeeConfigMetadata}
        />
      </div>
    );
  }
}

InfoColumn.propTypes = {
  columnData: PropTypes.object.isRequired,
  activeAccordionKeys: PropTypes.array,
  onAccordionClick: PropTypes.func,
  deskingFieldsOrderInfo: PropTypes.object.isRequired,
  deal: PropTypes.object.isRequired,
  isMultiVehicleDeskingEnabledV2: PropTypes.bool.isRequired,
  DeskingActions: PropTypes.object.isRequired,
  addColumn: PropTypes.func.isRequired,
  onDuplicateColumn: PropTypes.func.isRequired,
  onDownPaymentSelect: PropTypes.func.isRequired,
  isDealDirty: PropTypes.bool.isRequired,
  deskingpaymentDetails: PropTypes.array.isRequired,
  saveSellingPrice: PropTypes.func.isRequired,
  isDeskingViewOnly: PropTypes.bool.isRequired,
  updateStateFeeTargetingAndSaveDealData: PropTypes.func.isRequired,
  isUSCalcEngineEnabled: PropTypes.bool.isRequired,
  applyColumnDataAndDownpaymntToDesking: PropTypes.func.isRequired,
  onOpenLenderComparison: PropTypes.func,
};
InfoColumn.defaultProps = {
  activeAccordionKeys: EMPTY_ARRAY,
  onAccordionClick: _noop,
  onOpenLenderComparison: _noop,
};
