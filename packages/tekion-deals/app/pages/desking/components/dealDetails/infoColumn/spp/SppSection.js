/* eslint-disable no-shadow */
import React, { PureComponent } from 'react';
import _isEmpty from 'lodash/isEmpty';

import PropTypes from 'prop-types';
import { EMPTY_ARRAY, SINGLE_SPACE } from '@tekion/tekion-base/app.constants';

// import * as DeskingReader from '@tekion/tekion-base/marketScan/readers/desking.reader';
import { getSppPaymentOption } from '@tekion/tekion-base/marketScan/readers/deal.reader';

import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import withDeskingContext from 'pages/desking/withDeskingContext';
// import * as ColumnDataReader from '@tekion/tekion-base/marketScan/readers/columnData.reader';
import * as DeskingUtils from '@tekion/tekion-base/marketScan/utils/desking.utils';

import withDefault from 'pages/desking/withDefault';

import { defaultMemoize } from 'reselect';
import styles from './sppSection.module.scss';
import AccordianTextCell from '../../cells/infoCells/accordianTextCell';
import { getSPPAccordion } from './sppSection.utils';

class SppSection extends PureComponent {
  getSPPAccordion = defaultMemoize((allFNIs, columnFNIs, deal) => {
    const defaultAmount = { amount: SINGLE_SPACE };
    const FNIs = DeskingUtils.getColumnFNIsFromAllFNIs(allFNIs, columnFNIs) || EMPTY_ARRAY;
    const sppFNIs = DeskingUtils.filterSppFromFni(FNIs);
    const sppPaymentOption = getSppPaymentOption(deal);
    const downpayment = sppPaymentOption?.downPayment;

    const sppProductAccordion = sppFNIs.map(() => defaultAmount);
    if (!_isEmpty(sppProductAccordion)) {
      sppProductAccordion.push(defaultAmount); // Adding one more item for taxes
      sppProductAccordion.push(defaultAmount); // Adding one more item for total value
      if (downpayment) {
        sppProductAccordion.push(defaultAmount); // Adding one more item for downpayment
      }
    }
    return { sppProductAccordion };
  });

  render() {
    const { columnData, deskingpaymentDetails, activeAccordionKeys, isDeskingViewOnly, deal } = this.props;
    const { selected } = columnData;
    // const fniSuperSet = DeskingReader.getFNISuperSet(deskingpaymentDetails);
    // const columnFNIs = ColumnDataReader.getColumnFNIs(columnData);
    // const { sppProductAccordion } = this.getSPPAccordion(fniSuperSet, columnFNIs, deal);

    const { sppProductAccordion, totalSppAmount } = getSPPAccordion({ deal, columnData, deskingpaymentDetails });
    return (
      <PropertyControlledComponent controllerProperty={!_isEmpty(sppProductAccordion)}>
        <div className={styles.container}>
          <AccordianTextCell
            id="spp"
            activeKeys={activeAccordionKeys}
            isColumnSelected={selected}
            onEdit={null}
            data={sppProductAccordion}
            title={totalSppAmount}
            disabled={isDeskingViewOnly}
          />
        </div>
      </PropertyControlledComponent>
    );
  }
}

export default withDeskingContext(withDefault(SppSection));

SppSection.defaultProps = {
  deskingpaymentDetails: EMPTY_ARRAY,
  activeAccordionKeys: EMPTY_ARRAY,
  isDeskingViewOnly: false,
};

SppSection.propTypes = {
  columnData: PropTypes.object.isRequired,
  deal: PropTypes.object.isRequired,
  deskingpaymentDetails: PropTypes.array,
  activeAccordionKeys: PropTypes.array,
  isDeskingViewOnly: PropTypes.bool,
};
