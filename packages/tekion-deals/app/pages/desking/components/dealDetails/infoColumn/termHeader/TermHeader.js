import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import _forEach from 'lodash/forEach';
import _find from 'lodash/find';
import _size from 'lodash/size';
import _noop from 'lodash/noop';
import _get from 'lodash/get';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import * as DeskingReader from '@tekion/tekion-base/marketScan/readers/desking.reader';
import { getDeferredPayments } from '@tekion/tekion-base/marketScan/utils/desking.utils';
import { INFO_COLUMN_FIELDS } from '@tekion/tekion-base/marketScan/constants/deskingFields.constants';
import { getCostAdjustmentsOfPrimaryVehicle } from '@tekion/tekion-base/marketScan/readers/deal.reader';

import GlobalWarning from 'pages/desking/components/globalWarnings';
import withDeskingContext from 'pages/desking/withDeskingContext';
import PaymentTypeSelectionHeader from 'pages/desking/components/dealDetails/cells/titleCells/paymentTypeSelectionHeader/PaymentTypeSelectionHeader';
import { PAYMENT_TYPES_INDICATOR } from 'pages/desking/desking.constants';
import { MANUAL_CALCULATED_AMOUNT_REBATES } from 'pages/desking/components/rebate/Rebate.constants';
import { isTenureDisabled } from './termHeader.utils';

class TermHeader extends PureComponent {
  removeCostAdjustmentsAfterColumnDeletion = async () => {
    // if any other column contains MANUAL_CALCULATED_AMOUNT_REBATES rebates dont delete cost adjustments from them
    // find all three rebates uniquesly and accordingly delete respective cost asjustments
    const { DeskingActions, deskingpaymentDetails, saveDealData, deal } = this.props;
    const allRebates = DeskingReader.getRebatesSuperset({ deskingpaymentDetails });

    const costAdjustmentsToBeRemoved = [];

    _forEach(MANUAL_CALCULATED_AMOUNT_REBATES, value => {
      const requiredRebate = _find(allRebates, rebate => value.includes(rebate.name));
      if (!requiredRebate) {
        costAdjustmentsToBeRemoved.push(...value);
      }
    });

    if (_size(costAdjustmentsToBeRemoved)) {
      const costAdjustments = getCostAdjustmentsOfPrimaryVehicle(deal) || [];
      const newCostAdjustments = costAdjustments.filter(
        existingCostAdjustment => !costAdjustmentsToBeRemoved.includes(existingCostAdjustment.description)
      );

      if (costAdjustments.length !== newCostAdjustments.length && costAdjustments.length > 0) {
        await DeskingActions.setPrimaryVehicleInfo({
          key: 'costAdjustments',
          value: newCostAdjustments,
        });
        saveDealData();
      }
    }
  };

  onDeleteColumn = async id => {
    const { savePaymentsDataForSelectedVehicle, DeskingActions, deskingpaymentDetails } = this.props;
    await DeskingActions.deleteColumn(id);
    const deferredPayments = getDeferredPayments(deskingpaymentDetails);
    await GlobalWarning.showWarnings();
    await DeskingActions.setDeferredPaymentDetails({ deferredPayments });
    await savePaymentsDataForSelectedVehicle();
    this.removeCostAdjustmentsAfterColumnDeletion();
  };

  render() {
    const {
      paymentOptions,
      DeskingActions,
      getMarketScanData,
      getEmptyColumnDataForPaymentOption,
      deal,
      deskingpaymentDetails,
      columnData,
      columnsCount,
      addColumn,
      onDuplicateColumn,
      getFieldStatus,
      onOpenLenderComparison,
    } = this.props;

    const { paymentOption } = columnData;
    const paymentType = _get(paymentOption, 'paymentType');
    const paymentSubType = _get(paymentOption, 'paymentSubType');
    const { isDisabled } = getFieldStatus(INFO_COLUMN_FIELDS.TERM_HEADER, columnData);
    const disableOnlyTenure = isTenureDisabled(paymentSubType, isDisabled);

    return (
      <PaymentTypeSelectionHeader
        paymentOptions={paymentOptions}
        paymentTypeValue={paymentType}
        paymentSubTypeValue={paymentSubType}
        indicator={PAYMENT_TYPES_INDICATOR[paymentType]}
        months={paymentOption?.value}
        DeskingActions={DeskingActions}
        getEmptyColumnDataForPaymentOption={getEmptyColumnDataForPaymentOption}
        setPaymentOptions={DeskingActions.setPaymentOptions}
        getMarketScanData={getMarketScanData}
        columnData={columnData}
        onDuplicateColumn={onDuplicateColumn}
        onDeleteColumn={this.onDeleteColumn}
        deskingpaymentDetails={deskingpaymentDetails}
        deal={deal}
        disabled={isDisabled}
        disableOnlyTenure={disableOnlyTenure}
        columnsCount={columnsCount}
        addColumn={addColumn}
        onOpenLenderComparison={onOpenLenderComparison}
      />
    );
  }
}

TermHeader.defaultProps = {
  deal: EMPTY_OBJECT,
  columnData: EMPTY_OBJECT,
  columnsCount: PropTypes.number,
  deskingpaymentDetails: EMPTY_OBJECT,
  onOpenLenderComparison: _noop,
};

TermHeader.propTypes = {
  DeskingActions: PropTypes.object.isRequired,
  getMarketScanData: PropTypes.func.isRequired,
  getEmptyColumnDataForPaymentOption: PropTypes.func.isRequired,
  deal: PropTypes.object,
  columnData: PropTypes.object,
  savePaymentsDataForSelectedVehicle: PropTypes.func.isRequired,
  columnsCount: 0,
  addColumn: PropTypes.func.isRequired,
  onDuplicateColumn: PropTypes.func.isRequired,
  deskingpaymentDetails: PropTypes.object,
  saveDealData: PropTypes.func.isRequired,
  onOpenLenderComparison: PropTypes.func,
  getFieldStatus: PropTypes.func.isRequired,
};

export default withDeskingContext(TermHeader);
