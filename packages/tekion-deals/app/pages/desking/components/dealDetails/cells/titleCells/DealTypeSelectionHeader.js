import React, { useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import _noop from 'lodash/noop';
import _isEmpty from 'lodash/isEmpty';

import { EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import { isInchcapeOrRRG, isRRG } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
import { getLeaseorOnePayColumnIds, isCategory1Deal } from '@tekion/tekion-base/marketScan/utils/desking.utils';
import * as DealReader from '@tekion/tekion-base/marketScan/readers/deal.reader';
import { DEAL_TYPES } from '@tekion/tekion-base/constants/deal/type';
import Heading from '@tekion/tekion-components/src/atoms/Heading';
import Select from '@tekion/tekion-components/src/molecules/Select';
import Button from '@tekion/tekion-components/src/atoms/Button';
import Popover from '@tekion/tekion-components/src/molecules/popover';
import Ellipsis from '@tekion/tekion-components/src/atoms/Ellipsis';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';

import { hasLeaseOptionsEdit } from 'permissions/desking.permissions';

import DealHistory from '../../../../../../pages/desking/components/dealHistory/DealHistory';
import LeaseConfigurations from '../../../../../../pages/desking/components/leaseConfigurations';
import { DEAL_STATUS } from '../../../../../../pages/deallist/deal.constants';

import commonStyles from '../../commonstyles.module.scss';

const DealTypeSelectionHeader = ({
  dealTypes,
  selectedDealType,
  onChange,
  disabled,
  dealNumber,
  onDealRestore,
  isDealViewOnly,
  deal,
  deskingpaymentDetails,
  salesSetupInfo,
  getLabelFromPaymentOptions,
}) => {
  const [configureLease, setLeaseConfiguarion] = useState(false);
  const canConfigureLeaseSettings = () => {
    const leaseColumnIds = getLeaseorOnePayColumnIds(deskingpaymentDetails);
    return hasLeaseOptionsEdit() && isCategory1Deal(deal) && !_isEmpty(leaseColumnIds) && !isInchcapeOrRRG();
  };

  const onCloseLeaseConfiguration = () => {
    setLeaseConfiguarion(false);
  };

  const openCloseLeaseConfiguration = () => {
    setLeaseConfiguarion(true);
  };

  const getHeaderLabel = useCallback(
    isMultiVehicleDeskingEnabledV2 => {
      if (isMultiVehicleDeskingEnabledV2) {
        return __('Payment Details');
      }
      if (selectedDealType === DEAL_TYPES.DEAL_TRADE_SWAP) {
        return __('Vehicle to Sell');
      }

      return __('Deal Details');
    },
    [selectedDealType]
  );

  const isViewOnly = isDealViewOnly || DealReader.isDealStatusGreaterThanStatus(deal, DEAL_STATUS.DOCS_PRINTED);
  const isMultiVehicleDeskingEnabledV2 = DealReader.isMultiVehicleDeskingEnabledV2(deal);

  return (
    <div className={classNames('flex-row justify-content-between padding16', commonStyles.headerTitleCell)}>
      <Heading size={isMultiVehicleDeskingEnabledV2 ? 5 : 3} className={commonStyles.headingTitleHeader}>
        <Ellipsis length={12} tooltip>
          {getHeaderLabel(isMultiVehicleDeskingEnabledV2)}
        </Ellipsis>
        <PropertyControlledComponent controllerProperty={!isRRG()}>
          <DealHistory
            dealNumber={dealNumber}
            onDealRestore={onDealRestore}
            salesSetupInfo={salesSetupInfo}
            getLabelFromPaymentOptions={getLabelFromPaymentOptions}
          />
        </PropertyControlledComponent>

        <PropertyControlledComponent controllerProperty={canConfigureLeaseSettings()}>
          <Popover placement="top" trigger="hover" content={<div className="padding1">{__('Lease Settings')}</div>}>
            <Button
              view={Button.VIEW.ICON}
              id="text"
              icon="icon-price-settings"
              onClick={openCloseLeaseConfiguration}
              className="marginL12"
              disabled={isViewOnly}
            />
          </Popover>
        </PropertyControlledComponent>
      </Heading>
      <Select
        value={selectedDealType}
        placeholder={__('Deal Type')}
        defaultActiveFirstOption={false}
        showArrow
        filterOption={false}
        onSelect={onChange}
        style={{ width: 200, border: 'none' }}
        options={dealTypes}
        disabled={disabled}
      />

      <LeaseConfigurations
        configureLease={configureLease}
        onClose={onCloseLeaseConfiguration}
        getLabelFromPaymentOptions={getLabelFromPaymentOptions}
      />
    </div>
  );
};

const DealTypesPropType = PropTypes.arrayOf(
  PropTypes.shape({
    value: PropTypes.string,
    label: PropTypes.string,
  })
);

DealTypeSelectionHeader.propTypes = {
  dealTypes: DealTypesPropType,
  selectedDealType: PropTypes.string,
  onChange: PropTypes.func,
  disabled: PropTypes.bool,
  dealNumber: PropTypes.string.isRequired,
  onDealRestore: PropTypes.func.isRequired,
  isDealViewOnly: PropTypes.bool.isRequired,
  deal: PropTypes.object.isRequired,
  deskingpaymentDetails: PropTypes.arrayOf.isRequired,
};

DealTypeSelectionHeader.defaultProps = {
  dealTypes: EMPTY_ARRAY,
  selectedDealType: '',
  onChange: _noop,
  disabled: false,
};

export default DealTypeSelectionHeader;
