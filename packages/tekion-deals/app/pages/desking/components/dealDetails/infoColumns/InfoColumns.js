import React, { Component } from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import { compose } from 'recompose';

import _set from 'lodash/set';
import _filter from 'lodash/filter';

import * as ColumnDataReader from '@tekion/tekion-base/marketScan/readers/columnData.reader';
import { getOverrideFees } from '@tekion/tekion-base/marketScan/readers/deal.reader';
import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';
import Logger from '@tekion/tekion-base/logger/Logger';
import { DMV } from '@tekion/tekion-base/marketScan/constants/feeUpdateTable.constants';
import { INFO_COLUMN_FIELDS } from '@tekion/tekion-base/marketScan/constants/deskingFields.constants';

import withDeskingContext from 'pages/desking/withDeskingContext';
import withDefault from 'pages/desking/withDefault';
import UpdateModalStatic from 'organisms/updateModal/UpdateModalStatic';
import LenderDecisionComparison from 'pages/desking/components/lenderDecisionComparison/LenderDecisionComparison';
import { getLenderDecisionComparisonComponentProps } from 'pages/desking/components/lenderDecisionComparison/lenderDecisionComparison.utils';
import { PAYMENT_TYPES, PAYMENT_FREQUENCY, SOURCES_OF_FEES } from 'pages/desking/desking.constants';
import {
  getDuplicatedColumnData,
  sortDeskingpaymentDetails,
} from 'pages/desking/components/dealDetails/dealDetails.utils';
import CalcEngineProperties from 'utils/CalcEngineProperties';

import { DESKING_ACTIONS } from '@tekion/tekion-base/marketScan/constants/desking.constants';
import { isInchcapeOrRRG } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
import { DEAL_TYPES } from 'tbase/constants/deal/type';
import InfoColumn from '../infoColumn';
import AddColumn from '../addColumn';
import VehicleDetails from '../infoColumn/vehicleDetailsInfo';
import AddVehicle from '../addVehicle';

import styles from './infoColumns.module.scss';

const TAG = 'ADD_COLUMN';
const log = (message, extraData) => Logger.log(TAG, message, extraData);

class InfoColumns extends Component {
  getUpdateModalRef = ref => {
    this.updateModal = ref;
  };

  hideModal = () => {
    if (this.updateModal) {
      this.updateModal.hide();
    }
  };

  addNewColumn = async () => {
    const { updateDesking } = this.props;
    const paymentOption = {
      paymentType: PAYMENT_TYPES.LOAN,
      value: 36,
      paymentFrequency: PAYMENT_FREQUENCY.MONTHLY,
    };
    updateDesking({
      paymentUpdateRequest: {
        updatedDealPayments: [{ paymentOption }],
      },
    });
  };

  onDuplicateColumn = (referenceColumn, referenceIndex) => async () => {
    const { DeskingActions, savePaymentsDataForSelectedVehicle, syncPaymentDetails } = this.props;
    const duplicateColumnData = getDuplicatedColumnData(referenceColumn);
    DeskingActions.addNewColumn({
      columnData: duplicateColumnData,
      referenceIndex,
    });
    if (CalcEngineProperties.updateCalculationByBackend()) {
      syncPaymentDetails();
    } else {
      savePaymentsDataForSelectedVehicle();
    }
  };

  addColumn = (referenceColumn, referenceIndex) => async () => {
    const {
      DeskingActions,
      deal,
      getEmptyColumnDataForPaymentOption,
      getMarketScanData,
      deskingpaymentDetails,
      addDefaultFee,
    } = this.props;

    let selectedColumn = referenceColumn || ColumnDataReader.getSelectedColumn(deskingpaymentDetails);
    selectedColumn = {
      ...selectedColumn,
      ...(isInchcapeOrRRG() ? { columnSource: null, conciergeColumn: false } : {}),
    };
    if (DealerPropertyHelper.isCreateDealWithNewAPIEnabled()) {
      this.addNewColumn();
      return;
    }

    const isOemOfferColumn =
      ColumnDataReader.isOemOfferColumn(selectedColumn) ||
      ColumnDataReader.isOemOfferApprovedLeaseColumn(selectedColumn);
    if (CalcEngineProperties.updateCalculationByBackend() && !isOemOfferColumn) {
      const { savePaymentsDataForSelectedVehicle } = this.props;
      const duplicateColumnData = getDuplicatedColumnData(selectedColumn);
      await DeskingActions.addNewColumn({ columnData: duplicateColumnData, referenceIndex });
      savePaymentsDataForSelectedVehicle();
      return;
    }

    const paymentOption = { paymentType: PAYMENT_TYPES.LOAN, value: 36 };

    const columnData = getEmptyColumnDataForPaymentOption(paymentOption, false, null, selectedColumn?.dealVehicleId);
    const rebates = ColumnDataReader.getColumnRebates(selectedColumn);
    const fnis = ColumnDataReader.getColumnFNIs(selectedColumn);
    const accessories = ColumnDataReader.getColumnAccessories(selectedColumn);

    const overrideFees = getOverrideFees(deal);
    const selectedDownPaymentId = ColumnDataReader.getSelectedDownPaymentID(selectedColumn);
    const feeData =
      _filter(
        ColumnDataReader.getColumnFees(selectedColumn, selectedDownPaymentId),
        ({ feeType, manuallyEdited, source }) =>
          overrideFees &&
          feeType === DMV &&
          manuallyEdited &&
          (source === SOURCES_OF_FEES.MARKET_SCAN || source === SOURCES_OF_FEES.GALAXY)
      ) || EMPTY_ARRAY;

    _set(columnData, 'accessories', accessories);
    _set(columnData, 'fnIs', fnis);

    try {
      const { termPaymentDetails } = columnData;
      Object.keys(termPaymentDetails).forEach(lender => {
        const downPayments = Object.keys(termPaymentDetails[lender]);
        downPayments.forEach(dp => {
          _set(termPaymentDetails, [lender, dp, 'rebates'], rebates);
          _set(termPaymentDetails, [lender, dp, 'dealFees'], feeData);
        });
      });
    } catch (er) {
      log('failed while adding rebated', { columnData, rebates, er });
    }

    const fniSppLenderEnabled = DealerPropertyHelper.isFniSppLenderEnabled();
    if (fniSppLenderEnabled) {
      const sppPaymentOption = ColumnDataReader.getSppPaymentOptions(selectedColumn);
      _set(columnData, 'sppPaymentOption', sppPaymentOption);
    }

    await DeskingActions.addNewColumn({ columnData, referenceIndex });
    await addDefaultFee(columnData, true);
    await DeskingActions.setValidRebatesInColumn({
      selectedColumnId: columnData.id,
    });

    getMarketScanData({ columnIds: [columnData.id] }, false);
  };

  onOpenLenderComparison = () => {
    const {
      deal,
      DeskingActions,
      savePaymentsDataForSelectedVehicle,
      getMarketScanData,
      creditApplications,
      creditApplicationDecisionMappings: decisionMap,
      paymentOptions,
    } = this.props;
    const lenderDecComponentProps = getLenderDecisionComparisonComponentProps({
      deal,
      creditApplications,
      decisionMap,
      savePaymentsDataForSelectedVehicle,
      getMarketScanData,
      DeskingActions,
      paymentOptions,
    });
    if (this.updateModal) {
      this.updateModal.show({
        component: LenderDecisionComparison,
        componentProps: {
          ...lenderDecComponentProps,
          closeModal: this.hideModal,
        },
      });
    }
  };

  render() {
    const {
      deskingpaymentDetails,
      DeskingActions,
      activeAccordionKeys,
      onAccordionClick,
      deskingFieldsOrderInfo,
      taxFeeConfigMetadata,
      savePaymentsDataForSelectedVehicle,
      deal,
      isMultiVehicleDeskingEnabledV2,
      onDownPaymentSelect,
      vehiclesInfo,
      CommonActions,
      isDealViewOnly,
      trimInfos,
      disablePricingEditSettings,
      vehicleSubTypesForNewVehicleProgram,
      contentHeight,
      onVehiclesUpdated,
      saveDealData,
      saveSellingPrice,
      isDealDirty,
      onVehiclesAndCustomersUpdated,
      addDefaultCostAdjustments,
      updateStateFeeTargetingAndSaveDealData,
      activeSubAccordionKeys,
      isUSCalcEngineEnabled,
      salesSetupInfo,
      applyColumnDataAndDownpaymntToDesking,
      isFuseDeal,
      paymentOptions,
      getFieldStatus,
    } = this.props;
    const { isDisabled: addColumnDisabled, addColumnVisible } = getFieldStatus(INFO_COLUMN_FIELDS.ADD_COLUMN);
    const sortedDeskingpaymentDetails = DealerPropertyHelper.isHondaZDXProgramEnabled()
      ? sortDeskingpaymentDetails(deskingpaymentDetails)
      : deskingpaymentDetails;

    return (
      <div>
        <PropertyControlledComponent controllerProperty={isMultiVehicleDeskingEnabledV2}>
          <div className={`d-flex ${styles.vehicleDeatilsWrapper}`}>
            <VehicleDetails
              deskingpaymentDetails={sortedDeskingpaymentDetails}
              deal={deal}
              vehiclesInfo={vehiclesInfo}
              CommonActions={CommonActions}
              isDealViewOnly={isDealViewOnly || isFuseDeal}
              trimInfos={trimInfos}
              disablePricingEditSettings={disablePricingEditSettings}
              vehicleSubTypesForNewVehicleProgram={vehicleSubTypesForNewVehicleProgram}
              contentHeight={contentHeight}
              onVehiclesUpdated={onVehiclesUpdated}
              setTradeInVehicles={DeskingActions.setTradeInVehicles}
              showRecallWarning={DeskingActions.showRecallWarning}
              deleteVehicle={DeskingActions.deleteVehicle}
              savePaymentsDataForSelectedVehicle={savePaymentsDataForSelectedVehicle}
              saveDealData={saveDealData}
              onVehiclesAndCustomersUpdated={onVehiclesAndCustomersUpdated}
              addDefaultCostAdjustments={addDefaultCostAdjustments}
              isDeskingViewOnly={getFieldStatus(DESKING_ACTIONS.MVD_VEHICLE_ACTIONS)?.isDisabled}
              salesSetupInfo={salesSetupInfo}
            />
            <AddVehicle
              onVehiclesAndCustomersUpdated={onVehiclesAndCustomersUpdated}
              addDefaultCostAdjustments={addDefaultCostAdjustments}
              disabled={getFieldStatus(DESKING_ACTIONS.MVD_VEHICLE_ACTIONS)?.isDisabled}
              deal={deal}
            />
          </div>
        </PropertyControlledComponent>
        <div className={classNames('d-flex', styles.infoColumns)}>
          {sortedDeskingpaymentDetails.map((deskingpaymentDetail, index) => (
            <InfoColumn
              paymentOptions={paymentOptions}
              addColumn={this.addColumn(deskingpaymentDetail, index)}
              onDuplicateColumn={this.onDuplicateColumn(deskingpaymentDetail, index)}
              columnData={deskingpaymentDetail}
              // key={deskingpaymentDetail.id}
              key={index} // eslint-disable-line
              activeAccordionKeys={activeAccordionKeys}
              onAccordionClick={onAccordionClick}
              deskingFieldsOrderInfo={deskingFieldsOrderInfo}
              taxFeeConfigMetadata={taxFeeConfigMetadata}
              deal={deal}
              isMultiVehicleDeskingEnabledV2={isMultiVehicleDeskingEnabledV2}
              DeskingActions={DeskingActions}
              onDownPaymentSelect={onDownPaymentSelect}
              saveSellingPrice={saveSellingPrice}
              isDealDirty={isDealDirty}
              deskingpaymentDetails={sortedDeskingpaymentDetails}
              isDeskingViewOnly={getFieldStatus(DESKING_ACTIONS.MVD_VEHICLE_ACTIONS)?.isDisabled}
              updateStateFeeTargetingAndSaveDealData={updateStateFeeTargetingAndSaveDealData}
              activeSubAccordionKeys={activeSubAccordionKeys}
              isUSCalcEngineEnabled={isUSCalcEngineEnabled}
              applyColumnDataAndDownpaymntToDesking={applyColumnDataAndDownpaymntToDesking}
              onOpenLenderComparison={this.onOpenLenderComparison}
            />
          ))}
          <PropertyControlledComponent
            controllerProperty={
              addColumnVisible && !isMultiVehicleDeskingEnabledV2 && deal?.type !== DEAL_TYPES.MOTABILITY
            }>
            <AddColumn addColumn={this.addColumn()} disabled={addColumnDisabled} />
          </PropertyControlledComponent>
          <PropertyControlledComponent controllerProperty={isMultiVehicleDeskingEnabledV2}>
            <div className={styles.extension} />
          </PropertyControlledComponent>
        </div>
        <UpdateModalStatic ref={this.getUpdateModalRef} onClose={this.hideModal} />
      </div>
    );
  }
}

InfoColumns.defaultProps = {
  deskingpaymentDetails: EMPTY_ARRAY,
  activeAccordionKeys: EMPTY_ARRAY,
  DeskingActions: EMPTY_OBJECT,
  isFuseDeal: false,
  vehiclesInfo: EMPTY_ARRAY,
  vehicleSubTypesForNewVehicleProgram: EMPTY_ARRAY,
  creditApplications: EMPTY_ARRAY,
  creditApplicationDecisionMappings: EMPTY_OBJECT,
};

InfoColumns.propTypes = {
  deskingpaymentDetails: PropTypes.array,
  DeskingActions: PropTypes.object,
  activeAccordionKeys: PropTypes.array,
  onAccordionClick: PropTypes.func.isRequired,
  isFuseDeal: PropTypes.bool,
  getEmptyColumnDataForPaymentOption: PropTypes.func.isRequired,
  getMarketScanData: PropTypes.func.isRequired,
  deal: PropTypes.object.isRequired,
  deskingFieldsOrderInfo: PropTypes.object.isRequired,
  updateDesking: PropTypes.func.isRequired,
  taxFeeConfigMetadata: PropTypes.object.isRequired,
  isMultiVehicleDeskingEnabledV2: PropTypes.bool.isRequired,
  addDefaultFee: PropTypes.func.isRequired,
  savePaymentsDataForSelectedVehicle: PropTypes.func.isRequired,
  onDownPaymentSelect: PropTypes.func.isRequired,
  vehiclesInfo: PropTypes.array,
  CommonActions: PropTypes.func.isRequired,
  isDealViewOnly: PropTypes.bool.isRequired,
  trimInfos: PropTypes.object.isRequired,
  disablePricingEditSettings: PropTypes.bool.isRequired,
  vehicleSubTypesForNewVehicleProgram: PropTypes.array,
  contentHeight: PropTypes.number.isRequired,
  onVehiclesUpdated: PropTypes.func.isRequired,
  saveDealData: PropTypes.func.isRequired,
  saveSellingPrice: PropTypes.func.isRequired,
  isDealDirty: PropTypes.bool.isRequired,
  onVehiclesAndCustomersUpdated: PropTypes.func.isRequired,
  addDefaultCostAdjustments: PropTypes.func.isRequired,
  updateStateFeeTargetingAndSaveDealData: PropTypes.func.isRequired,
  applyColumnDataAndDownpaymntToDesking: PropTypes.func.isRequired,
  creditApplications: PropTypes.func,
  creditApplicationDecisionMappings: PropTypes.object,
  getFieldStatus: PropTypes.func.isRequired,
};

export default compose(withDeskingContext, withDefault)(InfoColumns);
