import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import { compose } from 'recompose';

import _isEmpty from 'lodash/isEmpty';
import _noop from 'lodash/noop';
import _compact from 'lodash/compact';
import _map from 'lodash/map';
import _includes from 'lodash/includes';
import _difference from 'lodash/difference';
import _forEach from 'lodash/forEach';
import _get from 'lodash/get';

import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { DEAL_TYPES } from '@tekion/tekion-base/constants/deal/type';
import { LEFT_PANEL_ITEMS } from 'pages/desking/desking.constants';
import * as SalesSetupReader from '@tekion/tekion-base/marketScan/readers/salesSetup.reader';
import * as DealReader from '@tekion/tekion-base/marketScan/readers/deal.reader';
import * as CustomerReader from '@tekion/tekion-base/marketScan/readers/customer.reader';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import { toaster } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import { isInchcape } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
import { showEmiForGivenProducts } from '@tekion/tekion-base/marketScan/readers/columnData.reader';
import CheckBox from '@tekion/tekion-components/src/atoms/checkbox';

// import ImbalanceAmountModal from 'pages/desking/components/ImbalanceAmountModal';
import withDeskingContext from 'pages/desking/withDeskingContext';
import withCustomInfo from 'pages/desking/hocs/withCustomInfo';
import GlobalWarning from 'pages/desking/components/globalWarnings';
import AutoScrollDiv from 'molecules/autoScrollDiv';
import CommonAPI from 'commonActions/apis';
import { getNumber } from 'utils';
// import { isFuseDeal } from 'utils/deal.util';
// import CalcEngineProperties from 'utils/CalcEngineProperties';
import { isPurchaseOnlyOrDealerTrade } from 'utils/desking.helpers';
import { formatPaymentOptionsForDropdownOptions } from '@tekion/tekion-base/marketScan/utils/desking.utils';
import DeaLSyncWarnings from 'pages/desking/components/dealSyncWarnings';

import { BACKEND_GLOBAL_WARNINGS_ACTIONS } from 'commonActions/constants/backendWarnings.constant';
import TitleColumn from './titleColumn';
import InfoColumns from './infoColumns';
import styles from './dealDetails.module.scss';
import VehicleToPurchase from './VehicleToPurchase/VehicleToPurchase';
import DealerApprovalRequiredWarnings from '../DealerApprovalRequiredWarnings';

import ShowrderFormRegeneration from './orderFormRegeneration';

class DealDetails extends PureComponent {
  constructor(props) {
    super(props);
    const { deal } = props;

    this.currDeposit = DealReader.getDepositDetails(deal)?.currDeposit;
    this.autoRewardCurrDeposit = DealReader.getAutoRewardsDetails(deal)?.currDeposit;

    this.state = {
      activeAccordionKeys: [],
      activeSubAccordionKeys: {},
    };
  }

  onAccordionClick = activeAccordionKeys => {
    // if mainAccordian has subAccordians then on closing mainAccordian collapse all its subAccordian
    this.setState(prevState => {
      const cuurentlyClosedAccordian = _difference(prevState?.activeAccordionKeys, activeAccordionKeys);
      const activeSubAccordionKeys = prevState?.activeSubAccordionKeys;
      _forEach(cuurentlyClosedAccordian, key => {
        activeSubAccordionKeys[key] = [];
      });
      return {
        activeAccordionKeys,
        activeSubAccordionKeys,
      };
    });
  };

  onSubAccordionClick = (id, activeSubAccordionKeys) => {
    this.setState({
      activeSubAccordionKeys: {
        [id]: activeSubAccordionKeys,
      },
    });
  };

  toggleSubAccordianView = (id, data) => {
    const subAccordianKeys = _compact(_map(data, ({ type }) => type));
    this.setState(prevState => {
      const allExpanded = prevState?.activeSubAccordionKeys?.[id]?.length === subAccordianKeys?.length;
      const activeAccordionKeys = prevState?.activeAccordionKeys;
      const isParentAccordianExpanded = _includes(activeAccordionKeys, id);

      if (!isParentAccordianExpanded) {
        activeAccordionKeys.push(id);
      } else {
        activeAccordionKeys.pop(id);
      }
      return {
        activeSubAccordionKeys: {
          [id]: allExpanded ? [] : subAccordianKeys,
        },
        activeAccordionKeys,
      };
    });
  };

  toggleShowMonthlyPayment = productType => event => {
    const { DeskingActions } = this.props;
    DeskingActions.setShowEmiToggleForGivenProduct(_get(event, ['target', 'checked']), productType);
  };

  renderShowMonthlyPaymentCheckBox = productType => () => {
    const { deskingpaymentDetails, savePaymentsDataForSelectedVehicle, salesSetupInfo } = this.props;
    return (
      <PropertyControlledComponent
        controllerProperty={SalesSetupReader.showEmiForProductsAndOtherBreakup(salesSetupInfo)}>
        <div onClick={event => event.stopPropagation()} role="button" tabIndex="-1">
          <CheckBox
            className={styles.showMonthlyPayment}
            label={__('Show Monthly Payment')}
            value={showEmiForGivenProducts(deskingpaymentDetails, productType)}
            onChange={this.toggleShowMonthlyPayment(productType)}
            onBlur={savePaymentsDataForSelectedVehicle}
          />
        </div>
      </PropertyControlledComponent>
    );
  };

  setDeposit = value => {
    const { DeskingActions } = this.props;
    if (value === undefined) {
      DeskingActions.setDepositDetails({ currDeposit: null });
    } else if (value >= 0) {
      DeskingActions.setDepositDetails({ currDeposit: `${getNumber(value)}` }); // getNumber(value) for: '0123' -> 123
    }
  };

  setAutoRewards = value => {
    const { DeskingActions } = this.props;
    if (value === undefined) {
      DeskingActions.setAutoRewardDetails({ currDeposit: null });
    } else if (value >= 0) {
      DeskingActions.setAutoRewardDetails({ currDeposit: `${getNumber(value)}` }); // getNumber(value) for: '0123' -> 123
    }
  };

  onBlurDepositField = () => {
    const { deal, saveDealData } = this.props;
    const editedCurrDeposit = DealReader.getDepositDetails(deal)?.currDeposit;
    if (`${this.currDeposit}` !== `${editedCurrDeposit}`) {
      saveDealData(); // to save currDeposit in deal
      this.currDeposit = editedCurrDeposit;
    }
  };

  onBlurAutoRewardField = () => {
    const { deal, saveDealData } = this.props;
    const editedCurrDeposit = DealReader.getAutoRewardsDetails(deal)?.currDeposit;
    if (`${this.autoRewardCurrDeposit}` !== `${editedCurrDeposit}`) {
      saveDealData(); // to save currDeposit in deal
      this.autoRewardCurrDeposit = editedCurrDeposit;
    }
  };

  onClickOfCheckBalance = async () => {
    const { deal } = this.props;
    const buyer = DealReader.getBuyer(deal) || EMPTY_OBJECT;
    const firstName = CustomerReader.getFirstName(buyer);
    const lastName = CustomerReader.getLastName(buyer);
    if (_isEmpty(firstName) || _isEmpty(lastName)) {
      toaster('error', __('First name and last name are required to check Auto Award balance.'));
      return;
    }
    const result = await CommonAPI.getAutoRewardBalance({ firstName, lastName });
    const isSuccess = _get(result || {}, ['rawResponse', 'status']) === 200;
    if (isSuccess) {
      const url = _get(result || {}, ['rawResponse', 'data', 'url']);
      if (!_isEmpty(url)) window.open(url, '_blank');
    } else {
      toaster('error', __('Failed to get Auto Award balance.'));
    }
  };

  onGlobalwarningsMount = () => {
    GlobalWarning.updateBEWarnings([
      BACKEND_GLOBAL_WARNINGS_ACTIONS.DEAL_PUSH_WARNINGS,
      BACKEND_GLOBAL_WARNINGS_ACTIONS.VEHICLE_RESERVED_IN_VI_WARNING,
    ]);
  };

  render() {
    const {
      deskingpaymentDetails,
      salesSetupInfo,
      taxFeeConfigMetadata,
      deal,
      columnData,
      saveDealData,
      DeskingActions,
      onDownPaymentSelect,
      isDeskingViewOnly,
      contentHeight,
      onVehiclesUpdated,
      saveSellingPrice,
      onVehiclesAndCustomersUpdated,
      updateStateFeeTargetingAndSaveDealData,
      isUSCalcEngineEnabled,
      applyColumnDataAndDownpaymntToDesking,
      dealSyncMissingMappings,
      dealSyncErrors,
      selectedTab,
      getCountOfPhoneNumberDigits,
      getLabelFromPaymentOptions,
      isFetchingMarketScanData,
      isDealDirty,
      setPdfContent,
      isAecPlatformDealPushEnabled,
    } = this.props;
    const { activeAccordionKeys, moreFieldSectionVisibility, activeSubAccordionKeys } = this.state;
    const hasColumnsData = !_isEmpty(deskingpaymentDetails);
    const deskingFieldsConfigs = SalesSetupReader.selectDeskingFields(salesSetupInfo);
    const deskingFieldsOrderInfo = SalesSetupReader.selectDeskingFieldsOrderInfo(salesSetupInfo);
    const vehicleSubTypesForNewVehicleProgram =
      SalesSetupReader.selectVehicleSubtypeForNewProgram(salesSetupInfo) || EMPTY_ARRAY;
    const paymentOptionsFromSalesSetup = formatPaymentOptionsForDropdownOptions(
      SalesSetupReader.getPaymentOptionConfigs(salesSetupInfo) || EMPTY_ARRAY
    );
    const isMultiVehicleDeskingEnabledV2 = DealReader.isMultiVehicleDeskingEnabledV2(deal);
    const isDealTypeDealerTradeSwap = DealReader.getDealType(deal) === DEAL_TYPES.DEAL_TRADE_SWAP;
    // const isCalcEngineEnabled = CalcEngineProperties.showCalcEngineView();
    // const isFuseDealSource = isFuseDeal(deal);
    const { type } = deal;
    const isDealPaused = DealReader.manualPaused(deal);

    return (
      <PropertyControlledComponent controllerProperty={hasColumnsData}>
        <GlobalWarning
          getCountOfPhoneNumberDigits={getCountOfPhoneNumberDigits}
          isFetchingMarketScanData={isFetchingMarketScanData}
          isDealDirty={isDealDirty}
          setPdfContent={setPdfContent}
          isAecPlatformDealPushEnabled={isAecPlatformDealPushEnabled}
          onGlobalwarningsMount={this.onGlobalwarningsMount}
        />
        <AutoScrollDiv
          className={classNames(
            'd-flex',
            'scrollBarhidden',
            'full-height',
            'full-width',
            'margin16',
            styles.dealDetailsContainer,
            isDealPaused ? styles.warningHighlight : ''
          )}>
          <div className={styles.titleCellsContainer}>
            <TitleColumn
              activeAccordionKeys={activeAccordionKeys}
              onAccordionClick={this.onAccordionClick}
              deskingFieldsConfigs={deskingFieldsConfigs}
              deskingFieldsOrderInfo={deskingFieldsOrderInfo}
              setDeposit={this.setDeposit}
              taxFeeConfigMetadata={taxFeeConfigMetadata}
              isMultiVehicleDeskingEnabledV2={isMultiVehicleDeskingEnabledV2}
              onBlurDepositField={this.onBlurDepositField}
              activeSubAccordionKeys={activeSubAccordionKeys}
              onSubAccordionClick={this.onSubAccordionClick}
              toggleSubAccordianView={this.toggleSubAccordianView}
              setAutoRewards={this.setAutoRewards}
              onBlurAutoRewardField={this.onBlurAutoRewardField}
              onClickOfCheckBalance={this.onClickOfCheckBalance}
              isPurchaseOnlyOrDealerTrade={isPurchaseOnlyOrDealerTrade(type)}
              getLabelFromPaymentOptions={getLabelFromPaymentOptions}
              renderShowMonthlyPaymentCheckBox={this.renderShowMonthlyPaymentCheckBox}
            />
          </div>

          <div>
            <InfoColumns
              paymentOptions={paymentOptionsFromSalesSetup}
              activeAccordionKeys={activeAccordionKeys}
              onAccordionClick={this.onAccordionClick}
              moreFieldSectionVisibility={moreFieldSectionVisibility}
              deskingFieldsOrderInfo={deskingFieldsOrderInfo}
              deskingFieldsConfigs={deskingFieldsConfigs}
              taxFeeConfigMetadata={taxFeeConfigMetadata}
              deal={deal}
              isMultiVehicleDeskingEnabledV2={isMultiVehicleDeskingEnabledV2}
              onDownPaymentSelect={onDownPaymentSelect}
              vehicleSubTypesForNewVehicleProgram={vehicleSubTypesForNewVehicleProgram}
              contentHeight={contentHeight}
              onVehiclesUpdated={onVehiclesUpdated}
              saveSellingPrice={saveSellingPrice}
              onVehiclesAndCustomersUpdated={onVehiclesAndCustomersUpdated}
              updateStateFeeTargetingAndSaveDealData={updateStateFeeTargetingAndSaveDealData}
              activeSubAccordionKeys={activeSubAccordionKeys}
              isUSCalcEngineEnabled={isUSCalcEngineEnabled}
              applyColumnDataAndDownpaymntToDesking={applyColumnDataAndDownpaymntToDesking}
            />
          </div>
          <PropertyControlledComponent controllerProperty={isDealTypeDealerTradeSwap}>
            <VehicleToPurchase
              deal={deal}
              DeskingActions={DeskingActions}
              saveDealData={saveDealData}
              isDeskingViewOnly={isDeskingViewOnly}
              columnData={columnData}
            />
          </PropertyControlledComponent>
        </AutoScrollDiv>
        {/* <PropertyControlledComponent
          controllerProperty={
            canEditDeskingPermission && !isUSCalcEngineEnabled && !isCalcEngineEnabled && !isFuseDealSource
          }
        >
          <ImbalanceAmountModal selectedColumn={columnData} onConfirm={getMarketScanData} />
        </PropertyControlledComponent> */}
        <DealerApprovalRequiredWarnings
          deal={deal}
          deskingpaymentDetails={deskingpaymentDetails}
          DeskingActions={DeskingActions}
        />
        <PropertyControlledComponent controllerProperty={!isAecPlatformDealPushEnabled}>
          <DeaLSyncWarnings dealSyncMissingMappings={dealSyncMissingMappings} dealSyncErrors={dealSyncErrors} />
        </PropertyControlledComponent>

        <PropertyControlledComponent controllerProperty={isInchcape()}>
          <ShowrderFormRegeneration deal={deal} selectedTab={selectedTab} />
        </PropertyControlledComponent>
      </PropertyControlledComponent>
    );
  }
}

export default compose(withDeskingContext, withCustomInfo(LEFT_PANEL_ITEMS.DESKING))(DealDetails);

DealDetails.defaultProps = {
  deskingpaymentDetails: EMPTY_ARRAY,
  deal: EMPTY_OBJECT,
  saveDealData: _noop,
  columnData: EMPTY_OBJECT,
  dealSyncMissingMappings: EMPTY_ARRAY,
  dealSyncErrors: EMPTY_ARRAY,
  setPdfContent: _noop,
  isAecPlatformDealPushEnabled: false,
};

DealDetails.propTypes = {
  deskingpaymentDetails: PropTypes.array,
  salesSetupInfo: PropTypes.object.isRequired,
  DeskingActions: PropTypes.func.isRequired,
  taxFeeConfigMetadata: PropTypes.object.isRequired,
  onDownPaymentSelect: PropTypes.func.isRequired,
  saveSellingPrice: PropTypes.func.isRequired,
  onVehiclesAndCustomersUpdated: PropTypes.func.isRequired,
  updateStateFeeTargetingAndSaveDealData: PropTypes.func.isRequired,
  deal: PropTypes.object,
  saveDealData: PropTypes.func,
  isDeskingViewOnly: PropTypes.bool.isRequired,
  contentHeight: PropTypes.number.isRequired,
  onVehiclesUpdated: PropTypes.func.isRequired,
  columnData: PropTypes.object,
  applyColumnDataAndDownpaymntToDesking: PropTypes.func.isRequired,
  dealSyncMissingMappings: PropTypes.array,
  dealSyncErrors: PropTypes.array,
  setPdfContent: PropTypes.bool,
  isAecPlatformDealPushEnabled: PropTypes.bool,
};
