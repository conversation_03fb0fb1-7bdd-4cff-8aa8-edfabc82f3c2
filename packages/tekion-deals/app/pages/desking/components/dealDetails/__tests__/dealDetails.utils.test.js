import React from 'react';
import { EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import { DATA_SOURCE_VS_DISPLAY_LABELS } from '@tekion/tekion-base/marketScan/constants/desking.constants';

import { getContent, getUpdatedBodyContentWithInfoIcon } from '../dealDetails.utils';

jest.mock('../dealDetails.module.scss', () => ({
  popoverContent: 'mocked-popover-content-class',
}));

describe('dealDetails.utils', () => {
  describe('getContent', () => {
    it('should return a React element', () => {
      const result = getContent();

      expect(React.isValidElement(result)).toBe(true);
    });

    it('should return a div element with correct props', () => {
      const result = getContent();

      expect(result.type).toBe('div');
      expect(result.props.className).toBe('mocked-popover-content-class');
    });

    it('should contain the correct text content', () => {
      const result = getContent();

      expect(result.props.children).toBe(DATA_SOURCE_VS_DISPLAY_LABELS.PRE_CONFIGURED);
    });

    it('should return consistent result on multiple calls', () => {
      const result1 = getContent();
      const result2 = getContent();

      expect(result1.type).toBe(result2.type);
      expect(result1.props.className).toBe(result2.props.className);
      expect(result1.props.children).toBe(result2.props.children);
    });

    it('should use the PRE_CONFIGURED constant value', () => {
      const result = getContent();

      expect(result.props.children).toBe('Source: Vehicle Inventory');
    });
  });

  describe('getUpdatedBodyContentWithInfoIcon', () => {
    it('should return empty array when bodyContent is empty', () => {
      const result = getUpdatedBodyContentWithInfoIcon([]);

      expect(result).toEqual([]);
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBe(0);
    });

    it('should return empty array when bodyContent is undefined', () => {
      const result = getUpdatedBodyContentWithInfoIcon(undefined);

      expect(result).toEqual([]);
      expect(Array.isArray(result)).toBe(true);
    });

    it('should replace empty content with EMPTY_ARRAY', () => {
      const bodyContent = [
        { id: 1, content: '' },
        { id: 2, content: null },
      ];

      const result = getUpdatedBodyContentWithInfoIcon(bodyContent);

      expect(result).toHaveLength(2);
      expect(result[0].content).toBe(EMPTY_ARRAY);
      expect(result[1].content).toBe(EMPTY_ARRAY);
    });

    it('should replace non-empty content with getContent() result', () => {
      const bodyContent = [
        { id: 1, content: 'some content' },
        { id: 2, content: ['array'] },
      ];

      const result = getUpdatedBodyContentWithInfoIcon(bodyContent);

      expect(result).toHaveLength(2);
      expect(React.isValidElement(result[0].content)).toBe(true);
      expect(React.isValidElement(result[1].content)).toBe(true);
      expect(result[0].content.props.children).toBe(DATA_SOURCE_VS_DISPLAY_LABELS.PRE_CONFIGURED);
      expect(result[1].content.props.children).toBe(DATA_SOURCE_VS_DISPLAY_LABELS.PRE_CONFIGURED);
    });

    it('should preserve other properties of items', () => {
      const bodyContent = [
        {
          id: 1,
          content: 'test',
          title: 'Test Title',
          data: { key: 'value' },
        },
      ];

      const result = getUpdatedBodyContentWithInfoIcon(bodyContent);

      expect(result[0].id).toBe(1);
      expect(result[0].title).toBe('Test Title');
      expect(result[0].data).toEqual({ key: 'value' });
      expect(React.isValidElement(result[0].content)).toBe(true);
    });
  });
});
