import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';

import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';

import DealTypeSelectionHeader from './downPayment/DealTypeSelectionHeader';
import DownPaymentSection from './downPayment';
import LenderPaymentInfoSection from './lenderPayment';
import Spp from './spp';
import SellingPrice from './sellingPrice';
import VehicleDetails from './vehicleDetailsTitle';
import MoreFieldsSection from './moreFields';
import styles from './titleColumn.module.scss';

export default class TitleColumn extends PureComponent {
  render() {
    const {
      deskingFieldsOrderInfo: { LENDER_PAYMENT_SECTION, MORE_FIELDS_SECTION },
      setDeposit,
      isMultiVehicleDeskingEnabledV2,
      onBlurDepositField,
      setAutoRewards,
      onBlurA<PERSON>Reward<PERSON>ield,
      onClickOfCheckBalance,
      getLabelFromPaymentOptions,
    } = this.props;
    return (
      <div>
        <div className={styles.stickyContent}>
          <PropertyControlledComponent controllerProperty={isMultiVehicleDeskingEnabledV2}>
            <VehicleDetails />
          </PropertyControlledComponent>
          <DealTypeSelectionHeader getLabelFromPaymentOptions={getLabelFromPaymentOptions} />
          <PropertyControlledComponent controllerProperty={isMultiVehicleDeskingEnabledV2}>
            <SellingPrice />
          </PropertyControlledComponent>
          <DownPaymentSection
            setDeposit={setDeposit}
            onBlurDepositField={onBlurDepositField}
            setAutoRewards={setAutoRewards}
            onBlurAutoRewardField={onBlurAutoRewardField}
            onClickOfCheckBalance={onClickOfCheckBalance}
          />
        </div>

        <LenderPaymentInfoSection {...this.props} fieldKeysInOrder={LENDER_PAYMENT_SECTION} />
        <MoreFieldsSection {...this.props} fieldKeysInOrder={MORE_FIELDS_SECTION} />
        <Spp {...this.props} fieldKeysInOrder={[...LENDER_PAYMENT_SECTION, 'SPP']} />
      </div>
    );
  }
}

TitleColumn.propTypes = {
  deskingFieldsOrderInfo: PropTypes.object.isRequired,
  setDeposit: PropTypes.func.isRequired,
  isMultiVehicleDeskingEnabledV2: PropTypes.bool.isRequired,
  onBlurDepositField: PropTypes.func.isRequired,
  setAutoRewards: PropTypes.func.isRequired,
  onBlurAutoRewardField: PropTypes.func.isRequired,
  onClickOfCheckBalance: PropTypes.func.isRequired,
  isPurchaseOnlyOrDealerTrade: PropTypes.bool.isRequired,
};
