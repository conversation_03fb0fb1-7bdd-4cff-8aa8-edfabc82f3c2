import React, { PureComponent } from 'react';
import _noop from 'lodash/noop';
import _get from 'lodash/get';
import _flatten from 'lodash/flatten';
import _compact from 'lodash/compact';
import _uniq from 'lodash/uniq';
import _keyBy from 'lodash/keyBy';
import _filter from 'lodash/filter';
import _map from 'lodash/map';

import PropTypes from 'prop-types';

import { TAGS } from '@tekion/tekion-widgets/src/appServices/sales/constants/constants';
import Logger from '@tekion/tekion-components/src/atoms/Logger';
import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import * as ColumnDataReader from '@tekion/tekion-base/marketScan/readers/columnData.reader';
import * as DealReader from '@tekion/tekion-base/marketScan/readers/deal.reader';
import * as ZipCodeTaxReader from '@tekion/tekion-base/marketScan/readers/zipCodeTax.reader';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import withDeskingContext from 'pages/desking/withDeskingContext';
import FeeUpdate from 'pages/desking/components/feeUpdate';
import * as VehicleReader from 'utils/vehicleReader';
import * as DeskingReader from '@tekion/tekion-base/marketScan/readers/desking.reader';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';
import { COUNTRY_CODE } from '@tekion/tekion-base/constants/countryCode';
import * as DealerConfigReader from 'utils/dealerConfig.reader';
import { checkTaxRateForWholeSaleDealEnabled, isDealerTradeDeal } from 'utils/deal.util';
import { isInchcape } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';

import {
  removeFeesFromDealFees,
  getTotalFees,
  getDealTypeFields,
  isCategory2Deal,
  getSelectedLenderDetails,
} from '@tekion/tekion-base/marketScan/utils/desking.utils';
import { PAYMENT_TYPES_VS_FEES_NOT_APPLICABLE, depositDataList } from 'pages/desking/desking.constants';
import { MORE_FIELDS, MISCELLANEOUS_ITEMS } from '@tekion/tekion-base/marketScan/constants/deskingFields.constants';

import UpdateModal from 'organisms/updateModal/UpdateModalStatic';
import { FILTERS_FOR_FEE_SEARCH, FEE_TYPES_MAP_API } from 'pages/desking/components/feeUpdate/feeUpdateTable.constants';
import { formatCurrencyWithMoneyFormat } from 'utils';

import withDefault from 'pages/desking/withDefault';
import { getUpdatedColumnFees } from 'pages/desking/components/feeUpdate/feeUpdate.util';
import CalcEngineProperties from 'utils/CalcEngineProperties';
import SalesDealerPropertyHelper from '@tekion/tekion-widgets/src/appServices/sales/helpers/salesDealerPropertyHelper';
import {
  getTaxSummary,
  getUniqFeesIncludingDeletedFeesForSelectedVehicleColumns,
  getUpdatedFieldKeysInOrder,
  checkLenderMBFCA,
  getUpdatedBodyContentWithInfoIcon,
} from '../../dealDetails.utils';
import { SECURITY_DEPOSIT_OPTIONS } from './moreFields.constants';
import styles from './moreFieldsSection.module.scss';

import HeadingTitleCell from '../../cells/titleCells/HeadingTitleCell';
import AccordionTitleCell from '../../cells/titleCells/accordionTitleCell';
import DropDownCell from '../../cells/titleCells/dropdownCell';
import { isCanadaDealer } from '../../../../../../utils/dealerUtils';

const { MORE_FIELD_SECTIONN_TITLE_COLUMN } = TAGS;

class MoreFieldsSection extends PureComponent {
  componentDidMount() {
    this.getOptions();
  }

  getOptions = async () => {
    const { DeskingActions } = this.props;

    await DeskingActions.feeSearch({
      filters: FILTERS_FOR_FEE_SEARCH,
      searchText: '',
      pageInfo: {
        start: 0,
        rows: 300,
      },
    });

    const { feeUpdateInfos } = this.props;
    const feeCodes = _get(feeUpdateInfos, 'feeCodes') || [];
    DeskingActions.feeBulkLookUp({
      FEE: {
        ids: feeCodes,
      },
    });
  };

  getDisplayNamesForFields = () => {
    const { deal, deskingFieldsConfigs, salesSetupInfo } = this.props;
    const dealType = DealReader.getDealType(deal);

    const showFeesTaxesCat3Deal = DealerConfigReader.getDealerCountryCode() === COUNTRY_CODE.CANADA;
    const fieldsToBeShown = getDealTypeFields(dealType, {
      shouldIncludeFees: showFeesTaxesCat3Deal,
      shouldIncludeTaxes:
        showFeesTaxesCat3Deal || checkTaxRateForWholeSaleDealEnabled(deal, salesSetupInfo) || isDealerTradeDeal(deal),
    });
    return DeskingReader.getDefaultDIsplayNames(deskingFieldsConfigs, fieldsToBeShown);
  };

  getUpdateModalRef = ref => {
    this.updateModal = ref;
  };

  getBodyContent = () => {
    const { deskingpaymentDetails } = this.props;
    const isVehicleProfileEnabled = SalesDealerPropertyHelper.isVehicleProfileEnabled();
    const columnData = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);
    const fees = DeskingReader.getUniqueFees(deskingpaymentDetails, isVehicleProfileEnabled) || EMPTY_ARRAY;
    const { bodyContent } = DeskingReader.convertDataToBodyContent(fees, 'name', 'amount', columnData);
    const updatedBodyContent = getUpdatedBodyContentWithInfoIcon(bodyContent);

    const requiredFees = removeFeesFromDealFees(fees, FEE_TYPES_MAP_API.leaseContractFees);
    this.feeTotal = getTotalFees(requiredFees);

    return updatedBodyContent || EMPTY_OBJECT;
  };

  getTaxes = () => {
    const { deskingpaymentDetails, deal, salesSetupInfo } = this.props;
    return getTaxSummary({
      deal,
      salesSetupInfo,
      deskingpaymentDetails,
    });
  };

  showFeeUpdateModal = feeHeaderContent => {
    const { deskingpaymentDetails, deal, taxFeeConfigMetadata, getFieldStatus } = this.props;
    const isVehicleProfileEnabled = SalesDealerPropertyHelper.isVehicleProfileEnabled();
    const selectedColumn = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);
    const collectRegFee = ColumnDataReader.getCollectRegFee(selectedColumn);
    const primaryVehicleInDeal = DealReader.getPrimaryVehicle(deal);
    const columnTaxAndZipCodeType = ColumnDataReader.getColumnTaxAndZipCodeType(selectedColumn);
    const primaryVehicleId = DealReader.getPrimaryVehicleDealVehicleId(deal);
    const dealFees = DeskingReader.getUniqueFeesForVehicle(
      deskingpaymentDetails,
      primaryVehicleId,
      isVehicleProfileEnabled
    );
    const stateFeeTaxOptions = DealReader.getStateFeeTaxOptions(deal, columnTaxAndZipCodeType);
    const taxAndZipCodeDetails = DealReader.getTaxAndZipCodeDetails(deal, columnTaxAndZipCodeType);
    const stateFeeTaxInfo = ZipCodeTaxReader.getStateFeeTaxInfo(taxAndZipCodeDetails);
    const marketId = DealReader.getMarketId(deal, columnTaxAndZipCodeType);
    const heading = feeHeaderContent?.heading;
    const deletedFees = this.getDeletedColumnFees();
    const initialFeesSupersSetIncludingDeletedFees = getUniqFeesIncludingDeletedFeesForSelectedVehicleColumns(
      deskingpaymentDetails,
      primaryVehicleId
    );

    this.updateModal.show({
      title: __('Fees Update'),
      component: FeeUpdate,
      componentProps: {
        fees: dealFees,
        manuallyDeletedFees: _uniq(_compact(_flatten(deletedFees))),
        onFeeUpdate: this.onFeeUpdate,
        onCloseModal: this.hideModal,
        restoreDefaultFees: this.restoreFees,
        getUpdatedFees: this.getUpdatedFees,
        collectRegFee,
        vehicleType: VehicleReader.getVehicleType(primaryVehicleInDeal),
        stateFeeTaxOptions,
        marketId,
        stateFeeTaxInfo,
        columnTaxAndZipCodeType,
        taxFeeConfigMetadata,
        selectedPaymentType: ColumnDataReader.getPaymentType(ColumnDataReader.getSelectedColumn(deskingpaymentDetails)),
        isDisabled: getFieldStatus(MISCELLANEOUS_ITEMS.FEE_UPDATE_FORM)?.isDisabled,
        isGlobalUpdate: true,
        columnId: selectedColumn?.id,
        heading,
        initialFeesSupersSetIncludingDeletedFees,
        type: null,
      },
    });
  };

  removeDeletedFeesFromStore = async () => {
    const { deskingpaymentDetails, DeskingActions } = this.props;
    const columnFees = {};
    const deletedFees = {};

    // remove all fees with deleted: true, on restore
    deskingpaymentDetails.forEach(columnData => {
      const { id } = columnData;
      const fee = ColumnDataReader.getColumnFees(columnData);
      columnFees[id] = fee;
      deletedFees[id] = [];
    });

    await DeskingActions.feesSave({ columnFees, deletedFees });
  };

  getDeletedColumnFees = () => {
    const { deskingpaymentDetails } = this.props;
    const isGalaxyEnabled = CalcEngineProperties.updateByGalaxyEngine();
    return isGalaxyEnabled
      ? _map(
          _filter(deskingpaymentDetails, column => column.selected),
          column => ColumnDataReader.getDeletedColumnFees(column)
        )
      : _map(deskingpaymentDetails, column => ColumnDataReader.getDeletedColumnFees(column));
  };

  restoreFees = async (onSuccessCallBack, keepmanuallyAdded) => {
    const {
      addDefaultFee,
      getMarketScanData,
      saveDealData,
      updateDeskingForColumnIds,
      deal,
      updateDealItemPaymentDetails,
    } = this.props;

    await this.removeDeletedFeesFromStore();
    const { deskingpaymentDetails } = this.props;
    await addDefaultFee({}, keepmanuallyAdded);

    const columnIds = DeskingReader.getAllCColumnsIDs(deskingpaymentDetails);
    if (DealerPropertyHelper.isCreateDealWithNewAPIEnabled()) {
      await saveDealData(); // to save stateFeeTaxOptions
      await updateDeskingForColumnIds(columnIds);
    } else if (CalcEngineProperties.updateCalculationByBackend()) {
      await updateDealItemPaymentDetails({
        collectFees: DealReader.collectFees(deal),
      });
    } else {
      await getMarketScanData({ columnIds });
    }

    this.getUpdatedFees(onSuccessCallBack);
  };

  getUpdatedFees = onSuccessCallBack => {
    const { deskingpaymentDetails, deal } = this.props;
    const isVehicleProfileEnabled = SalesDealerPropertyHelper.isVehicleProfileEnabled();
    const primaryVehicleId = DealReader.getPrimaryVehicleDealVehicleId(deal);
    const dealFees = DeskingReader.getUniqueFeesForVehicle(
      deskingpaymentDetails,
      primaryVehicleId,
      isVehicleProfileEnabled
    );
    const deletedFees = _uniq(_compact(_flatten(this.getDeletedColumnFees())));
    onSuccessCallBack(dealFees, deletedFees);
  };

  hideModal = () => {
    this.updateModal.hide();
  };

  onFeeUpdate = async (fees, collectRegFee, manuallyDeletedFees, feesThatWereNotShown) => {
    Logger.log('', MORE_FIELD_SECTIONN_TITLE_COLUMN, 'onFeeUpdate', { fees });
    const {
      DeskingActions,
      getMarketScanData,
      deskingpaymentDetails,
      saveDealData,
      updateEMIAmountOfFNIDeal,
      deal,
      updateDeskingForColumnIds,
      updatePaymentWithItems,
      syncPaymentDetailsWithDealItemsUpdate,
    } = this.props;
    const primaryVehicleId = DealReader.getPrimaryVehicleDealVehicleId(deal);
    const isMultiVehicleDeskingEnabledV2 = DealReader.isMultiVehicleDeskingEnabledV2(deal);
    // dont save bank fee for loan and cash columns
    let columnFees = {};
    const deletedFees = {};

    deskingpaymentDetails.forEach(column => {
      const { id, dealVehicleId } = column;
      const shouldUpdateFeesForColumn = isMultiVehicleDeskingEnabledV2 ? dealVehicleId === primaryVehicleId : true;
      if (shouldUpdateFeesForColumn) {
        const paymentType = ColumnDataReader.getPaymentType(column);
        columnFees[id] = fees.filter(
          ({ internalFeeCode }) => !PAYMENT_TYPES_VS_FEES_NOT_APPLICABLE[paymentType].includes(internalFeeCode)
        );

        const columnFeesByFeeCode = _keyBy(ColumnDataReader.getAllFees(column), 'feeCode');
        const feesGotRemoved = [];

        // feesThatWereNotShown, based on paymenttype few fees will not be shown on UI on Update screen
        // so those fees should be added back to respective columns, if they are already part of it.

        feesThatWereNotShown.forEach(feeItem => {
          const isFeeAlreadyExists = columnFees[id].findIndex(item => feeItem?.feeCode === item?.feeCode) !== -1;
          if (columnFeesByFeeCode[feeItem?.feeCode] && !isFeeAlreadyExists) {
            feesGotRemoved.push(columnFeesByFeeCode[feeItem?.feeCode]);
          }
        });
        columnFees[id].push(...feesGotRemoved);
        deletedFees[id] = manuallyDeletedFees;

        columnFees = getUpdatedColumnFees(columnFees);
      }
    });

    await DeskingActions.feesSave({
      columnFees,
      deletedFees,
      collectRegFee,
    });

    if (CalcEngineProperties.updateByGalaxyEngine()) {
      await syncPaymentDetailsWithDealItemsUpdate({
        overrideFees: DealReader.getOverrideFees(deal),
        stateFeeTax: _get(deal, 'stateFeeTaxOptions', EMPTY_OBJECT),
        taxAndZipCodeDetails: _get(deal, 'taxAndZipCodeDetails', EMPTY_OBJECT),
      });
      this.hideModal();
      return;
    }

    if (CalcEngineProperties.updateCalculationByBackend()) {
      await updatePaymentWithItems({
        dealKeysToCache: ['stateFeeTaxOptions', 'taxAndZipCodeDetails'],
        syncPaymentPayload: { overrideFees: DealReader.getOverrideFees(deal) },
      });
      this.hideModal();
      return;
    }

    const columnIds = DeskingReader.getAllCColumnsIDs(deskingpaymentDetails);
    if (isCategory2Deal(deal)) {
      updateEMIAmountOfFNIDeal();
    } else {
      if (DealerPropertyHelper.isCreateDealWithNewAPIEnabled()) {
        await saveDealData(); // to save stateFeeTaxOptions
        await updateDeskingForColumnIds(columnIds);
        this.hideModal();
        return;
      }
      saveDealData();
      getMarketScanData({
        columnIds: deskingpaymentDetails.map(({ id }) => id),
      });
    }
    this.hideModal();
  };

  getFeeHeaderContent = isDisabled => {
    const { deskingFieldsConfigs, deal, salesSetupInfo } = this.props;
    const dealType = DealReader.getDealType(deal);
    const showFeesTaxesCat3Deal = DealerConfigReader.getDealerCountryCode() === COUNTRY_CODE.CANADA;
    const fieldsToBeShown = getDealTypeFields(dealType, {
      shouldIncludeFees: showFeesTaxesCat3Deal,
      shouldIncludeTaxes:
        showFeesTaxesCat3Deal || checkTaxRateForWholeSaleDealEnabled(deal, salesSetupInfo) || isDealerTradeDeal(deal),
    });
    const { feesDN } = DeskingReader.getDefaultDIsplayNames(deskingFieldsConfigs, fieldsToBeShown);
    return {
      heading: feesDN,
      value: formatCurrencyWithMoneyFormat(this.feeTotal),
      buttonName: isDisabled ? '' : __('Update'),
    };
  };

  onSelectSecurityDepositWaiverReason = async value => {
    const { DeskingActions, deskingpaymentDetails, saveDealData } = this.props;
    const selectedColumnData = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);
    const selectedColumnId = selectedColumnData?.id;
    await DeskingActions.setSecurityDepositWaiverReason({ value, selectedColumnId });
    saveDealData();
  };

  getLenderSetupIncludeSecurityDeposits = () => {
    const { deskingpaymentDetails, lenders } = this.props;
    const columnData = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);

    const selectedLender = ColumnDataReader.getSelectedLenderId(columnData);
    const lenderInfo = getSelectedLenderDetails(lenders, selectedLender) || EMPTY_OBJECT;
    return _get(lenderInfo, 'collectSecurityDepositWaiverReason');
  };

  getSecurityDepositWaiverReason = () => {
    const { deal, deskingpaymentDetails } = this.props;
    const isGalaxyEnabled = CalcEngineProperties.showGalaxyView();
    const selectedColumnData = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);
    return isGalaxyEnabled
      ? _get(selectedColumnData, 'securityDepositWaiverReason')
      : DealReader.getSecurityDepositWaiverReason(deal);
  };

  renderDeskingFields = fieldId => {
    const {
      activeAccordionKeys,
      onAccordionClick,
      deal,
      onSubAccordionClick,
      activeSubAccordionKeys,
      toggleSubAccordianView,
      getFieldStatus,
      deskingpaymentDetails,
      renderShowMonthlyPaymentCheckBox,
      lenders,
    } = this.props;
    const securityDepositWaiverReason = this.getSecurityDepositWaiverReason();
    const lenderSetupIncludeSecurityDeposits = this.getLenderSetupIncludeSecurityDeposits();
    const isTaxBreakUpEnabled = DealerPropertyHelper.isTaxBreakUpEnabled() && DealReader.isTaxBreakUpEnabled(deal);
    const { isDisabled } = getFieldStatus(fieldId);
    const columnData = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);

    switch (fieldId) {
      case MORE_FIELDS.FEES: {
        const { feesDN } = this.getDisplayNamesForFields();
        const feeBodyContent = this.getBodyContent();
        const feeHeaderContent = this.getFeeHeaderContent(isDisabled);
        return (
          <PropertyControlledComponent controllerProperty={feesDN}>
            <AccordionTitleCell
              id="fees"
              headerContent={feeHeaderContent}
              activeKeys={activeAccordionKeys}
              onCollapseStatusChange={onAccordionClick}
              onUpdate={isDisabled ? null : () => this.showFeeUpdateModal(feeHeaderContent)}
              bodyContent={feeBodyContent}
              renderAdditionalComponent={renderShowMonthlyPaymentCheckBox(MORE_FIELDS.FEES)}
            />
          </PropertyControlledComponent>
        );
      }
      case MORE_FIELDS.TAXES: {
        const { taxesDN } = this.getDisplayNamesForFields();
        const { titleColumnData: taxTitleColumnData, totalTax } = this.getTaxes();
        const allExpanded = !!activeAccordionKeys.length;
        const headerContent = {
          heading: taxesDN,
          value: formatCurrencyWithMoneyFormat(totalTax),
          buttonName: allExpanded ? __('Collapse All') : __('Expand All'),
        };
        if (!isTaxBreakUpEnabled) {
          headerContent.buttonName = '';
        }
        return (
          <PropertyControlledComponent controllerProperty={taxesDN}>
            <AccordionTitleCell
              id="tax"
              headerContent={headerContent}
              activeKeys={activeAccordionKeys}
              onCollapseStatusChange={onAccordionClick}
              bodyContent={taxTitleColumnData}
              onSubAccordionClick={onSubAccordionClick}
              activeSubAccordionKeys={activeSubAccordionKeys.tax || []}
              onUpdate={isTaxBreakUpEnabled ? toggleSubAccordianView : null}
            />
          </PropertyControlledComponent>
        );
      }
      case MORE_FIELDS.LOAN_TO_VALUE_RATIO: {
        const { loanTOValue } = this.getDisplayNamesForFields();
        return (
          <PropertyControlledComponent controllerProperty={loanTOValue}>
            <HeadingTitleCell label={loanTOValue} />
          </PropertyControlledComponent>
        );
      }
      case MORE_FIELDS.SECURITY_DEPOSIT: {
        const { securityDepositDN } = this.getDisplayNamesForFields();
        const securityDepositHeaderContent = { heading: __('Security Deposit') };

        return (
          <PropertyControlledComponent controllerProperty={securityDepositDN}>
            <PropertyControlledComponent controllerProperty={isCanadaDealer()}>
              <AccordionTitleCell
                id="sdeposit"
                headerContent={securityDepositHeaderContent}
                activeKeys={activeAccordionKeys}
                onCollapseStatusChange={onAccordionClick}
                onUpdate={null}
                bodyContent={depositDataList}
                style={{ height: '4rem' }}
              />
            </PropertyControlledComponent>

            <PropertyControlledComponent controllerProperty={!isCanadaDealer()}>
              <DropDownCell
                options={lenderSetupIncludeSecurityDeposits ? SECURITY_DEPOSIT_OPTIONS : EMPTY_ARRAY}
                value={securityDepositWaiverReason}
                onChange={this.onSelectSecurityDepositWaiverReason}
                label={securityDepositDN}
                disabled={isDisabled}
              />
            </PropertyControlledComponent>
          </PropertyControlledComponent>
        );
      }
      case MORE_FIELDS.PAYMENT_FREQUENCY: {
        const { paymentFrequencyDN } = this.getDisplayNamesForFields();
        return (
          <PropertyControlledComponent controllerProperty={paymentFrequencyDN}>
            <HeadingTitleCell label={paymentFrequencyDN} />
          </PropertyControlledComponent>
        );
      }
      case MORE_FIELDS.FINANCE_ADJUSTED_CAPITAL_COST: {
        const { amountFinancedDN } = this.getDisplayNamesForFields();
        return (
          <PropertyControlledComponent controllerProperty={amountFinancedDN}>
            <HeadingTitleCell label={amountFinancedDN} />
          </PropertyControlledComponent>
        );
      }
      case MORE_FIELDS.DRIVE_OFF: {
        const { driveOffDN } = this.getDisplayNamesForFields();
        return (
          <PropertyControlledComponent controllerProperty={driveOffDN}>
            <HeadingTitleCell label={driveOffDN} />
          </PropertyControlledComponent>
        );
      }

      case MORE_FIELDS.OUT_OF_POCKET_CASH: {
        const { outOfPocketCashDN } = this.getDisplayNamesForFields();
        return (
          <PropertyControlledComponent controllerProperty={outOfPocketCashDN}>
            <HeadingTitleCell label={outOfPocketCashDN} />
          </PropertyControlledComponent>
        );
      }

      case MORE_FIELDS.FINANCE_CHARGE: {
        const { financeChargeDN } = this.getDisplayNamesForFields();
        return (
          <PropertyControlledComponent controllerProperty={financeChargeDN}>
            <HeadingTitleCell label={financeChargeDN} />
          </PropertyControlledComponent>
        );
      }

      case MORE_FIELDS.SUBVENTION_COST: {
        const isSubventionCostVisibile = checkLenderMBFCA({ deskingpaymentDetails, lenders }) && isCanadaDealer();
        return (
          <PropertyControlledComponent controllerProperty={isSubventionCostVisibile}>
            <HeadingTitleCell label={__('Subvention Cost')} />
          </PropertyControlledComponent>
        );
      }

      case MORE_FIELDS.TOTAL_DEPOSIT: {
        const { totalDepositDN } = this.getDisplayNamesForFields();
        return (
          <PropertyControlledComponent controllerProperty={totalDepositDN && isInchcape()}>
            <HeadingTitleCell label={totalDepositDN} />
          </PropertyControlledComponent>
        );
      }

      case MORE_FIELDS.TOTAL_AMOUNT_PAYABLE: {
        const { totalAmountPayableDN } = this.getDisplayNamesForFields();
        return (
          <PropertyControlledComponent controllerProperty={totalAmountPayableDN && isInchcape()}>
            <HeadingTitleCell label={totalAmountPayableDN} />
          </PropertyControlledComponent>
        );
      }

      case MORE_FIELDS.EXCESS_MILEAGE_CHARGE: {
        const { excessMileageChargeDN } = this.getDisplayNamesForFields();
        return (
          <PropertyControlledComponent controllerProperty={excessMileageChargeDN && isInchcape()}>
            <HeadingTitleCell label={excessMileageChargeDN} />
          </PropertyControlledComponent>
        );
      }

      case MORE_FIELDS.OPTION_TO_PURCHASE_FEE: {
        const { optionToPurchaseFeeDN } = this.getDisplayNamesForFields();
        return (
          <PropertyControlledComponent controllerProperty={optionToPurchaseFeeDN && isInchcape()}>
            <HeadingTitleCell label={optionToPurchaseFeeDN} />
          </PropertyControlledComponent>
        );
      }

      case MORE_FIELDS.OPTIONAL_FINAL_PAYMENT: {
        const { optionalFinalPaymentDN } = this.getDisplayNamesForFields();
        return (
          <PropertyControlledComponent controllerProperty={optionalFinalPaymentDN && isInchcape()}>
            <HeadingTitleCell label={optionalFinalPaymentDN} />
          </PropertyControlledComponent>
        );
      }

      case MORE_FIELDS.TOTAL_DUE_AT_SIGNING: {
        const { totalDueAtSigninDN } = this.getDisplayNamesForFields();
        return (
          <PropertyControlledComponent controllerProperty={totalDueAtSigninDN}>
            <HeadingTitleCell label={totalDueAtSigninDN} />
          </PropertyControlledComponent>
        );
      }

      default:
        return null;
    }
  };

  render() {
    const { fieldKeysInOrder, deskingpaymentDetails, lenders } = this.props;
    const updatedFieldKeysInOrder = getUpdatedFieldKeysInOrder({ fieldKeysInOrder, deskingpaymentDetails, lenders });
    return (
      <React.Fragment>
        <div className={styles.cellContainer}>
          {updatedFieldKeysInOrder?.map(fieldId => this.renderDeskingFields(fieldId))}
        </div>
        <UpdateModal ref={this.getUpdateModalRef} onClose={this.hideModal} onCancel={this.hideModal} />
      </React.Fragment>
    );
  }
}

MoreFieldsSection.defaultProps = {
  activeAccordionKeys: EMPTY_ARRAY,
  onAccordionClick: _noop,
  feeUpdateInfos: EMPTY_OBJECT,
  getFieldStatus: _noop,
  addDefaultFee: _noop,
  deal: EMPTY_OBJECT,
};

MoreFieldsSection.propTypes = {
  activeAccordionKeys: PropTypes.array,
  onAccordionClick: PropTypes.func,
  deskingpaymentDetails: PropTypes.array.isRequired,
  DeskingActions: PropTypes.object.isRequired,
  getMarketScanData: PropTypes.func.isRequired,
  feeUpdateInfos: PropTypes.object,
  deskingFieldsConfigs: PropTypes.object.isRequired,
  getFieldStatus: PropTypes.func,
  addDefaultFee: PropTypes.func,
  deal: PropTypes.object,
  saveDealData: PropTypes.func.isRequired,
  fieldKeysInOrder: PropTypes.array.isRequired,
  salesSetupInfo: PropTypes.object.isRequired,
  lenders: PropTypes.object.isRequired,
  updateDeskingForColumnIds: PropTypes.func.isRequired,
  taxFeeConfigMetadata: PropTypes.object.isRequired,
};

export default withDeskingContext(withDefault(MoreFieldsSection));
