import React, { PureComponent, createRef } from 'react';
import PropTypes from 'prop-types';
import { produce } from 'immer';
import classNames from 'classnames';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _noop from 'lodash/noop';
import _set from 'lodash/set';
import _reduce from 'lodash/reduce';
import _find from 'lodash/find';
import _map from 'lodash/map';
import _filter from 'lodash/filter';
import _debounce from 'lodash/debounce';

import { defaultMemoize } from 'reselect';

import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import { APR_MF_DECIDER, LENDER_RATE_TYPES, LENDER_TIERS } from '@tekion/tekion-base/constants/retail/marketScan';
import * as SalesSetupReader from '@tekion/tekion-base/marketScan/readers/salesSetup.reader';
import * as ColumnDataReader from '@tekion/tekion-base/marketScan/readers/columnData.reader';
import { getDealType, getDealNumber } from '@tekion/tekion-base/marketScan/readers/deal.reader';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';
import { COUNTRY_CODE } from '@tekion/tekion-base/constants/countryCode';
import { isInchcape, isRRG } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';

import * as DealerConfigReader from 'utils/dealerConfig.reader';
import { checkTaxRateForWholeSaleDealEnabled, isDealerTradeDeal } from 'utils/deal.util';
import { getCustomerRebatesThatRequireApproval } from 'utils/rebateUtils';
import * as DueBillUtil from 'utils/dueBills.util';
import withDeskingContext from 'pages/desking/withDeskingContext';
import FNIUpdate from 'pages/desking/components/fniUpdate/FNIUpdate';
import AccessoriesUpdate from 'pages/desking/components/accessoriesUpdate';
import {
  getRebatesDisplayNames,
  getDealTypeFields,
  isCategory1Deal,
  isCategory2Deal,
  isCategory3Deal,
  getColumnRebatesFromAllRebates,
  getRebatesTotal,
  getRebatesExcludingDealerCashAndEvTaxCredit,
  getColumnAccessoriesFromAllAccessories,
  getColumnFNIsFromAllFNIs,
  isValueMoneyFactor,
  getColumnIdsWrtPaymentTypeOfColumn,
  getColIdsToSetDefValues,
} from '@tekion/tekion-base/marketScan/utils/desking.utils';

import { recomputeFNIsForRespectiveColumns } from 'pages/desking/components/fniUpdate/fniUpdate.util';
import { LENDER_PAYMENT_FIELDS } from '@tekion/tekion-base/marketScan/constants/deskingFields.constants';
import {
  PAYMENT_TYPES,
  RATE_TYPE_FACTOR,
  MANUALLY_UPDATED_API_KEY,
  DESKING_FIELD_KEYS,
  PROGRAM_TYPE,
} from 'pages/desking/desking.constants';
import * as DeskingReader from '@tekion/tekion-base/marketScan/readers/desking.reader';
import DropDownCell from 'pages/desking/components/dealDetails/cells/titleCells/dropdownCell';
import InputTitleCell from 'pages/desking/components/dealDetails/cells/titleCells/InputTitleCell';
import AccordionTitleCell from 'pages/desking/components/dealDetails/cells/titleCells/accordionTitleCell';
import UpdateModalStatic from 'organisms/updateModal/UpdateModalStatic';
import BuybackValueModal from 'organisms/BuybackValueModal/BuybackValueModal';
import ConfirmationModal from 'molecules/ConfirmationModal';
import Tooltip from '@tekion/tekion-components/atoms/tooltip';
import Icon, { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';
import { getNumber, formatCurrencyWithMoneyFormat, findArrayItem } from 'utils';
import mileageLabeler from 'utils/label';
import CalcEngineProperties from 'utils/CalcEngineProperties';
import { updateAPRDataForAPRChange } from 'pages/desking/utils/apr.utils';
import withDefault from 'pages/desking/withDefault';
import Rebate from 'pages/desking/components/rebate';
import YearlyMilesChangeModal from 'pages/desking/components/dealDetails/infoColumn/lenderPayment/components/YearlyMilesChangeModal';
import { DATA_SOURCE_VS_DISPLAY_LABELS } from '@tekion/tekion-base/marketScan/constants/desking.constants';

import COLORS from 'tstyles/exports.scss';
import { ES_REFETCH_DELAY } from '@tekion/tekion-base/constants/general';
import { POPOVER_PLACEMENT } from '@tekion/tekion-components/src/molecules/popover';
import withHandleCustomerApprovals from 'pages/desking/hocs/withHandleCustomerApprovals';
import salesDealerPropertyHelper from '@tekion/tekion-widgets/src/appServices/sales/helpers/salesDealerPropertyHelper';
import HeadingTitleCell from '../../cells/titleCells/HeadingTitleCell';
import FinanceContractSuffixIconComponent from './components/financeContractSuffixIconComponent';

import { getOverriddenResidualOnYearlyMilesChange, isRebateEditDisabled } from '../../dealDetails.utils';
import { getFinanceContractStatusDetails, filterSppFnisOut } from '../../../../../../readers/columnDataReader';

import styles from './index.module.scss';
import { isCanadaDealer } from '../../../../../../utils/dealerUtils';
import { fetchPackagesAccessoriesList } from './actions/lenderPaymentTitleColumnSection.actions';
import { isPrimaryVehicleZDX } from '../../../accessoriesUpdate/accessoriesCollapseHeader.helpers';

class LenderPaymentTitleColumnSection extends PureComponent {
  static propTypes = {
    onAccordionClick: PropTypes.func,
    activeAccordionKeys: PropTypes.array,
    getMarketScanData: PropTypes.func,
    deskingpaymentDetails: PropTypes.object,
    DeskingActions: PropTypes.object,
    columnData: PropTypes.object,
  };

  static defaultProps = {
    onAccordionClick: _noop,
    activeAccordionKeys: [],
    getMarketScanData: _noop,
    deskingpaymentDetails: EMPTY_OBJECT,
    DeskingActions: EMPTY_OBJECT,
    columnData: EMPTY_OBJECT,
  };

  aprValueRef = createRef();

  yearlyMilesRef = createRef();

  yearlyMilesChangeModalRef = React.createRef();

  state = {
    apr: '',
    residualValue: '',
    yearlyMiles: '',
    inputDays: '',
    isModified: false,
    showAddHEWarning: false,
    packagesAccessoriesList: [],
  };

  componentDidMount() {
    if (DealerPropertyHelper.isHondaProgramEnabled()) this.fetchPackagesList();
  }

  componentDidUpdate(prevProps) {
    const { deskingpaymentDetails } = this.props;
    const { deskingpaymentDetails: previousDeskingPaymentDetails } = prevProps;
    if (previousDeskingPaymentDetails !== deskingpaymentDetails) {
      if (DealerPropertyHelper.isHondaProgramEnabled()) this.fetchPackagesList();
    }
  }

  componentWillUnmount() {
    clearTimeout(this.showAddHEWarningTimer);
  }

  onAPRDataChange = value => {
    this.setState({ apr: value || '', isModified: true });
  };

  onBlurAPRField = async () => {
    const { apr, isModified } = this.state;

    if (isModified && apr) {
      const {
        DeskingActions,
        getMarketScanData,
        deskingpaymentDetails,
        updateDeskingForColumnIds,
        syncPaymentDetails,
      } = this.props;
      this.setState({ isModified: false });

      const changeAPRPromises = [];
      const columnIdsToRefresh = [];
      const isCalcEngineEnabled = CalcEngineProperties.updateByCalcEngine();
      const selectedColumn = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);
      const { id } = selectedColumn;
      deskingpaymentDetails.forEach(columnData => {
        const { id: columnId, paymentOption } = columnData;
        const aprData = ColumnDataReader.getAPRData(columnData);
        const lenderRateType = _get(aprData, 'lenderRateType');
        let newAPR = Number(apr) || 0;

        if (lenderRateType === LENDER_RATE_TYPES.MF && newAPR > APR_MF_DECIDER) {
          newAPR /= RATE_TYPE_FACTOR;
        } else if (lenderRateType === LENDER_RATE_TYPES.APR && newAPR < APR_MF_DECIDER) {
          newAPR *= RATE_TYPE_FACTOR;
        }

        const updatedAPRData = updateAPRDataForAPRChange(aprData, newAPR, isCalcEngineEnabled);

        changeAPRPromises.push(
          DeskingActions.changeAPR({
            id: columnId,
            aprData: {
              ...updatedAPRData,
              [MANUALLY_UPDATED_API_KEY]: true,
            },
          })
        );
        if (CalcEngineProperties.updateByGalaxyEngine()) {
          changeAPRPromises.push(
            DeskingActions.setPaymentOptions({
              ...paymentOption,
              id: columnId,
              programType: PROGRAM_TYPE.CUSTOM,
              newColumnId: columnId,
            })
          );
        }
        columnIdsToRefresh.push(columnId);
      });

      await Promise.all(changeAPRPromises);

      if (DealerPropertyHelper.isCreateDealWithNewAPIEnabled()) {
        updateDeskingForColumnIds(columnIdsToRefresh);
      } else if (CalcEngineProperties.updateCalculationByBackend()) {
        if (isCanadaDealer()) {
          await DeskingActions.resetWaiverReason({ id });
          await DeskingActions.resetMSD({ id });
        }
        await syncPaymentDetails();
      } else {
        getMarketScanData({ columnIds: columnIdsToRefresh });
      }

      this.setState({ apr: '' });
    }
  };

  onBlurResidualData = () => {
    const { isModified } = this.state;
    const { DeskingActions, getMarketScanData, deskingpaymentDetails } = this.props;
    if (isModified) {
      this.onResidualChange(DeskingActions.changeResidualData, getMarketScanData, deskingpaymentDetails)();
    }
  };

  onBlurDaystoFirstPayment = () => {
    const { isModified } = this.state;
    const { DeskingActions, getMarketScanData, deskingpaymentDetails, updateDeskingForColumnIds, syncPaymentDetails } =
      this.props;
    if (isModified) {
      this.onDaysToFirstPaymentChange(
        DeskingActions.changeDaysToFirstPayment,
        getMarketScanData,
        deskingpaymentDetails,
        updateDeskingForColumnIds,
        syncPaymentDetails
      )();
    }
  };

  onBlurYearlyMiles = () => {
    if (isInchcape()) {
      this.openYearlyMilesChangeModal();
    } else {
      this.onSubmitYearlyMilesChange();
    }
  };

  setNumberValuesInState = key => value => {
    this.setState({
      [key]: Number(value),
      isModified: true,
    });
  };

  getRebatesData = defaultMemoize((allRebates, columnRebates, isDisabled, deal) => {
    const { rebatesDN } = this.getDisplayNamesForFields();
    const isGalaxyEnabled = CalcEngineProperties.updateByGalaxyEngine();
    const validColumnRebates =
      getColumnRebatesFromAllRebates(allRebates, columnRebates, isGalaxyEnabled) || EMPTY_ARRAY;
    const customerAddedRebates = getCustomerRebatesThatRequireApproval(validColumnRebates);
    const disabled = isRebateEditDisabled(isDisabled, deal);

    let rebatesData = {
      id: 'rebate',
      headerContent: {
        heading: rebatesDN,
        value: 0,
        warnings: customerAddedRebates.length
          ? [__('Customer Abbed {{count}} Rebates', { count: customerAddedRebates.length })]
          : [],
        buttonName: disabled ? '' : __('Update'),
      },
      bodyContent: [],
    };
    rebatesData = produce(rebatesData, draft => {
      draft.bodyContent = getRebatesDisplayNames(validColumnRebates);
      const rebateTotal = getRebatesTotal(getRebatesExcludingDealerCashAndEvTaxCredit(validColumnRebates));
      draft.headerContent.value = formatCurrencyWithMoneyFormat(rebateTotal);
    });

    return rebatesData;
  });

  getAccessoriesUpdateLabel = (total, accessories, showAddHEWarning) => {
    const { deal } = this.props;
    const showAddHEWarningIcon =
      !DueBillUtil.isHEChargerPackageAdded(accessories) &&
      DealerPropertyHelper.isHondaZDXProgramEnabled() &&
      DueBillUtil.isDealTypeAllowedForHEChargerWarning(deal) &&
      isPrimaryVehicleZDX(deal);
    if (showAddHEWarningIcon) {
      return (
        <div className={styles.totalWithWarningContainer}>
          <Tooltip
            title={__('Add 1 HE charging package')}
            placement="topLeft"
            overlayClassName={styles.errorTooltip}
            visible={showAddHEWarning}>
            <Icon
              size={SIZES.M}
              color={COLORS.carrotOrange}
              className={styles.cautionIcon}
              onMouseEnter={this.handleMouseEnterForHEToolTip}
              onMouseLeave={this.handleMouseLeaveForHEToolTip}>
              icon-caution
            </Icon>
          </Tooltip>
          <span>{total}</span>
        </div>
      );
    }
    return total;
  };

  getPackagesAccessoriesName = args => {
    const { name, uniqueId, isPackage, source } = args;
    const { packagesAccessoriesList } = this.state;
    const packageAccessories = _get(packagesAccessoriesList, uniqueId);
    const packageAccessoriesNameList = _map(packageAccessories, accessory => accessory?.name);
    return (
      <>
        {isPackage && (
          <div className={styles.popoverContent}>
            {__(`${name} Includes:`)}
            <ul className={styles.listContainer}>
              {_map(packageAccessoriesNameList, accessoryName => (
                <li key={accessoryName}>{accessoryName}</li>
              ))}
            </ul>
          </div>
        )}
        {source && <div className={styles.popoverContent}>{DATA_SOURCE_VS_DISPLAY_LABELS.PRE_CONFIGURED}</div>}
      </>
    );
  };

  getAccessoriesForAccordian = (allAccessories, columnAccessories, isDisabled, showAddHEWarning) => {
    const { dueBillsDN } = this.getDisplayNamesForFields();
    const { packagesAccessoriesList } = this.state;
    const isVehicleProfileEnabled = salesDealerPropertyHelper.isVehicleProfileEnabled();
    const accessories =
      getColumnAccessoriesFromAllAccessories(allAccessories, columnAccessories, isVehicleProfileEnabled) || EMPTY_ARRAY;
    const className = styles.popoverContainer;
    const { accessoriesAccordianData, total } = DeskingReader.getAccessoriesAccordianData({
      accessories,
      getPackagesAccessoriesName: this.getPackagesAccessoriesName,
      packagesAccessoriesList,
      className,
      placement: POPOVER_PLACEMENT.TOP,
    });
    return {
      accessories: accessoriesAccordianData,
      accessoriesHeader: {
        heading: dueBillsDN,
        value: this.getAccessoriesUpdateLabel(total, accessories, showAddHEWarning),
        buttonName: isDisabled ? '' : __('Update'),
      },
    };
  };

  getFNIsForAccordian = (FNIsSuperSet, columnFNIs, isDisabled) => {
    const { deskingpaymentDetails } = this.props;
    const { fniMenuDN } = this.getDisplayNamesForFields();

    const fnisWithoutSpp = ColumnDataReader.filterSppFnisOut(columnFNIs);
    const allFnis = getColumnFNIsFromAllFNIs(FNIsSuperSet, fnisWithoutSpp) || EMPTY_ARRAY;
    const fnis = filterSppFnisOut(allFnis, deskingpaymentDetails);
    const { fniAccordianData, total } = DeskingReader.getFNIAccordianData(fnis);

    return {
      FNIs: fniAccordianData,
      FNIsHeader: { heading: fniMenuDN, value: total, buttonName: isDisabled ? '' : __('Update') },
    };
  };

  getSelectedLenderInfo = defaultMemoize((lenders, selectedLenderId) => {
    const matchedLender = findArrayItem(lenders, 'id', selectedLenderId);
    const lenderDetails = DeskingReader.getLenderDetails(matchedLender, [
      'removeDemoMileageFromAnnualMileage',
      'removeDemoMileageFromAnnualMileageDeciderValue',
    ]);
    return lenderDetails;
  });

  getTierValue = defaultMemoize(tier => {
    const code = _get(tier, 'code');
    const type = _get(tier, 'type');
    return `${code}${type}`;
  });

  getTiersList = defaultMemoize((column, lenders) => {
    const { salesSetupInfo } = this.props;
    const selectedLenderId = ColumnDataReader.getSelectedLenderId(column);
    const leaseOrOnePay = ColumnDataReader.isLeaseOrOPLeasePaymentType(column);

    if ((!leaseOrOnePay && !SalesSetupReader.getUseCustomerTierForLoanApr(salesSetupInfo)) || isInchcape())
      return EMPTY_ARRAY;

    const matchedLender = findArrayItem(lenders, 'id', selectedLenderId);
    const lenderDetails = DeskingReader.getLenderDetails(matchedLender, ['tiers']);
    const tiers = _get(lenderDetails, 'tiers') || EMPTY_ARRAY;

    return _reduce(
      tiers,
      (acc, item) => {
        const { code, type, name } = item || EMPTY_OBJECT;
        const obj = {
          label: name,
          value: this.getTierValue(item),
          type,
          name,
          code,
        };
        if (CalcEngineProperties.updateByGalaxyEngine()) acc.push(obj);
        else if (leaseOrOnePay) {
          if (type === LENDER_TIERS.LEASE) acc.push(obj);
        } else if (type === LENDER_TIERS.RETAIL) acc.push(obj);

        return acc;
      },
      []
    );
  });

  fetchPackagesList = async () => {
    const { packagesAccessoriesList } = this.state;
    const { deskingpaymentDetails, deal } = this.props;
    const isVehicleProfileEnabled = salesDealerPropertyHelper.isVehicleProfileEnabled();
    if (_isEmpty(packagesAccessoriesList)) {
      const selectedColumn = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);
      const columnAccessories = ColumnDataReader.getColumnAccessories(selectedColumn);
      const allAccessories = DeskingReader.getAccessoriesSuperSet(deskingpaymentDetails);
      const accessories =
        getColumnAccessoriesFromAllAccessories(allAccessories, columnAccessories, isVehicleProfileEnabled) ||
        EMPTY_ARRAY;
      const dealNumber = getDealNumber(deal);
      const accessoryIds = _map(_filter(accessories, { isPackage: true }), 'uniqueId');
      if (!_isEmpty(accessoryIds)) {
        const fetchedPackagesAccessoriesList = await fetchPackagesAccessoriesList({ accessoryIds, dealNumber });
        this.setState({ packagesAccessoriesList: fetchedPackagesAccessoriesList });
      }
    }
  };

  handleMouseEnterForHEToolTip = () => {
    this.setState({ showAddHEWarning: true });
  };

  handleMouseLeaveForHEToolTip = () => {
    this.setState({ showAddHEWarning: false });
  };

  scrollToResidualInput = () => {
    if (this.residualValueRef) {
      this.residualValueRef.current.scrollIntoView(false);
    }
  };

  scrollToYearlyMilesInput = () => {
    if (this.yearlyMilesRef) {
      this.yearlyMilesRef.current.scrollIntoView(false);
    }
  };

  onResidualChange = (changeResidualData, getMarketScanData, deskingpaymentDetails) => async () => {
    this.setState({ isModified: false });

    const { residualValue: totalResidualOverrideValue } = this.state;
    const columnsToRefresh = [];
    const changesPromiseArray = [];
    deskingpaymentDetails.forEach(async dpd => {
      const { paymentOption, id, selectedLenderId } = dpd;
      const { paymentType } = paymentOption;
      if ([PAYMENT_TYPES.LEASE, PAYMENT_TYPES.ONE_TIME_LEASE, PAYMENT_TYPES.BALLOON].includes(paymentType)) {
        const existingResidual = ColumnDataReader.getResidualData(dpd);
        const residualData = {
          ...existingResidual,
          totalPercentage: totalResidualOverrideValue,
          residualOverridden: getNumber(totalResidualOverrideValue) > 0,
          [MANUALLY_UPDATED_API_KEY]: true,
        };
        columnsToRefresh.push(id);
        changesPromiseArray.push(
          await changeResidualData({
            id,
            lender: selectedLenderId,
            residualData,
            resetProgramId: true,
          })
        );
      }
    });

    Promise.all(changesPromiseArray).then(async () => {
      const { updateDeskingForColumnIds, syncPaymentDetails } = this.props;
      if (DealerPropertyHelper.isCreateDealWithNewAPIEnabled()) {
        updateDeskingForColumnIds(columnsToRefresh);
      } else if (CalcEngineProperties.updateCalculationByBackend()) {
        await syncPaymentDetails();
      } else {
        getMarketScanData({ columnIds: columnsToRefresh });
      }
    });
  };

  /**
   * https://tekion.atlassian.net/browse/DMS-62738
   * On changing yearly miles, reset residual for those lease/onePayLease columns which have residualOverridden as false
   */
  resetResidual = async () => {
    const { DeskingActions, deskingpaymentDetails } = this.props;

    const data = getOverriddenResidualOnYearlyMilesChange(deskingpaymentDetails);

    if (!_isEmpty(data)) {
      await DeskingActions.setValuesInTermPmtDetails({ data });
    }
  };

  onYearlyMilesChange =
    (
      setYearlyMiles,
      setValuesInTermPmtDetails,
      getMarketScanData,
      deskingpaymentDetails,
      lenders,
      updateDeskingForColumnIds,
      salesSetupInfo,
      syncPaymentDetails,
      reasonForChange
    ) =>
    async () => {
      this.setState({
        isModified: false,
      });

      const { yearlyMiles: totalYearlyMiles } = this.state;

      // await this.resetResidual();

      const yearlyMilesForColumn = {};
      const residualForColumn = {};
      const isGalaxyEnabled = CalcEngineProperties.updateByGalaxyEngine();

      deskingpaymentDetails.forEach(column => {
        const { id } = column;
        const leaseOrOnePay = ColumnDataReader.isLeaseOrOPLeasePaymentType(column);
        const previousYealyMiles = ColumnDataReader.getTotalYearlyMiles(column);
        const hasStandardData = ColumnDataReader.hasStandardData(column);
        const isYearlyMilesChanged = getNumber(previousYealyMiles) !== getNumber(totalYearlyMiles);

        const isInchcapeEnableBalloon = isInchcape() && ColumnDataReader.isBalloonPaymentType(column);
        if (leaseOrOnePay || isInchcapeEnableBalloon) {
          const yearlyMiles = {
            totalValue: getNumber(totalYearlyMiles),
            totalMilesHidden: getNumber(totalYearlyMiles),
            ...(isGalaxyEnabled && isYearlyMilesChanged ? { yearlyMilesUpdated: true } : EMPTY_OBJECT),
            reasonForChange,
          };
          _set(yearlyMilesForColumn, [id], yearlyMiles);
          if (getNumber(previousYealyMiles) !== getNumber(totalYearlyMiles) && hasStandardData && !isGalaxyEnabled) {
            residualForColumn[id] = { residual: null };
          }
        }
      });
      await setYearlyMiles({
        yearlyMilesInfo: yearlyMilesForColumn,
      });
      await setValuesInTermPmtDetails({ data: residualForColumn });

      // Refresh only lease columns and one pay lease columns on changing yearly miles
      const columnsToRefresh = deskingpaymentDetails.filter(
        ({ paymentOption: { paymentType } }) =>
          paymentType === PAYMENT_TYPES.LEASE || paymentType === PAYMENT_TYPES.ONE_TIME_LEASE
      );

      const columnIds = columnsToRefresh.map(({ id }) => id);
      if (DealerPropertyHelper.isCreateDealWithNewAPIEnabled()) {
        updateDeskingForColumnIds(columnIds);
      } else if (CalcEngineProperties.updateCalculationByBackend()) {
        await syncPaymentDetails();
      } else {
        getMarketScanData({ columnIds });
      }
    };

  openYearlyMilesChangeModal = async () => {
    const { isModified } = this.state;
    if (isModified && this.yearlyMilesChangeModalRef) {
      const { submitted, reasonForChange } = await this.yearlyMilesChangeModalRef.current.show();
      if (submitted) {
        this.onSubmitYearlyMilesChange(reasonForChange);
      } else {
        this.onCancelYearlyMilesChangeModal();
      }
    }
  };

  onSubmitYearlyMilesChange = reasonForChange => {
    const { isModified } = this.state;
    const {
      DeskingActions,
      getMarketScanData,
      deskingpaymentDetails,
      lenders,
      updateDeskingForColumnIds,
      salesSetupInfo,
      syncPaymentDetails,
    } = this.props;
    if (isModified) {
      this.onYearlyMilesChange(
        DeskingActions.setYearlyMiles,
        DeskingActions.setValuesInTermPmtDetails,
        getMarketScanData,
        deskingpaymentDetails,
        lenders,
        updateDeskingForColumnIds,
        salesSetupInfo,
        syncPaymentDetails,
        reasonForChange
      )();
    }
  };

  onCancelYearlyMilesChangeModal = () => {
    this.setState({
      yearlyMiles: '',
      isModified: false,
    });
  };

  onDaysToFirstPaymentChange =
    (
      changeDaysToFirstPayment,
      getMarketScanData,
      deskingpaymentDetails,
      updateDeskingForColumnIds,
      syncPaymentDetails
    ) =>
    async () => {
      this.setState({ isModified: false });
      // Refresh only loan columns
      const { inputDays } = this.state;
      await changeDaysToFirstPayment({
        daysToFirstPayment: Number(inputDays),
        applyToAll: true,
      });
      const columnsToRefresh = deskingpaymentDetails.filter(
        ({ paymentOption: { paymentType } }) => paymentType !== PAYMENT_TYPES.CASH
      );
      const columnIds = columnsToRefresh.map(({ id }) => id);
      if (DealerPropertyHelper.isCreateDealWithNewAPIEnabled()) {
        updateDeskingForColumnIds(columnIds);
      } else if (CalcEngineProperties.updateCalculationByBackend()) {
        await syncPaymentDetails();
      } else {
        getMarketScanData({ columnIds });
      }
    };

  hideModal = () => this.updateModal.hide();

  updateRebatesInDeal = async ({ syncPaymentDetails, getMarketScanData, deskingpaymentDetails }) => {
    if (CalcEngineProperties.updateCalculationByBackend()) {
      await syncPaymentDetails();
    } else {
      await getMarketScanData({
        columnIds: deskingpaymentDetails.map(({ id }) => id),
      });
    }
  };

  onCloseRebateModal = (refresh, showConfirmationModal, hideModal, getMarketScanData, deskingpaymentDetails) => {
    const { syncPaymentDetails, handleCustomerApprovalOnRebateUpdate } = this.props;
    if (refresh) {
      this.updateRebatesInDeal({ syncPaymentDetails, getMarketScanData, deskingpaymentDetails }).then(() => {
        if (DealerPropertyHelper.isCustomerApprovalsEnabled()) {
          // handleCustomerApprovalOnRebateUpdate(deskingpaymentDetails);
        }
      });
    }
    if (showConfirmationModal) {
      return ConfirmationModal.show({
        title: __('Warning'),
        message: __("Are you sure want to close this pop up? You'll lose the progress you've made."),
        submitBtnText: __('Yes'),
        onSubmitCb: () => {
          hideModal();
          ConfirmationModal.close();
        },
      });
    }

    return hideModal();
  };

  showFNIMenu = FNIsHeader => {
    const { deskingpaymentDetails, salesSetupInfo, DeskingActions } = this.props;
    // const fnis = DeskingReader.getFNISuperSet(deskingpaymentDetails);
    const selectedColumn = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);
    const columnFNIs = ColumnDataReader.getColumnFNIs(selectedColumn);
    const setupIntegrationKeys = SalesSetupReader.selectIntegrationKeys(salesSetupInfo);
    const heading = FNIsHeader?.heading;

    this.updateModal.show({
      component: FNIUpdate,
      componentProps: {
        onCloseModal: this.hideModal,
        onSave: this.onFNIDataChange,
        fnis: columnFNIs,
        fniAttributes: this.fniAttributes || [],
        restoreFNIdefaults: this.restoreFNIdefaults,
        selectedColumn,
        setupIntegrationKeys,
        DeskingActions,
        heading,
      },
    });
  };

  showAccessoriesMenu = accessoriesHeader => {
    const { deskingpaymentDetails, deal, lenders } = this.props;
    // const accessories = DeskingReader.getAccessoriesSuperSet(deskingpaymentDetails);

    const selectedColumn = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);
    const columnAccessories = ColumnDataReader.getColumnAccessories(selectedColumn);
    const selectedLenderId = ColumnDataReader.getSelectedLenderId(selectedColumn);
    const lender = _find(lenders, ({ id }) => id === selectedLenderId);
    const heading = accessoriesHeader?.heading;
    this.setState({ showAddHEWarning: false });

    this.updateModal.show({
      component: AccessoriesUpdate,
      componentProps: {
        onCloseModal: this.hideModal,
        onSave: this.onAccessoriesDataChange(lender),
        accessories: columnAccessories,
        restoreDefaultDueBills: this.restoreDefaultDueBills,
        deal,
        lender,
        heading,
      },
    });
  };

  restoreDefaultDueBills = async onRestoreSuccess => {
    const { addDefaultAccessories, deskingpaymentDetails, deal } = this.props;

    await addDefaultAccessories({ columnIds: getColIdsToSetDefValues(deal, deskingpaymentDetails) });
    const accessories = DeskingReader.getAccessoriesSuperSet(deskingpaymentDetails);
    onRestoreSuccess(accessories);
  };

  onFNIDataChange = async fnis => {
    const {
      DeskingActions,
      getMarketScanData,
      deal,
      updateEMIAmountOfFNIDeal,
      syncPaymentDetails,
      deskingpaymentDetails,
      updateDeskingForColumnIds,
      savePaymentsDataForSelectedVehicle,
    } = this.props;

    const dealType = getDealType(deal);
    await DeskingActions.saveFNIs({
      fnis,
      dealType,
      columnIds: getColIdsToSetDefValues(deal, deskingpaymentDetails),
    });

    if (CalcEngineProperties.updateCalculationByBackend()) {
      await syncPaymentDetails();
      if (!CalcEngineProperties.updateByGalaxyEngine()) {
        await savePaymentsDataForSelectedVehicle();
      }
      this.hideModal();
      return;
    }

    if (isCategory2Deal(deal)) {
      await updateEMIAmountOfFNIDeal();
      this.hideModal();
      return;
    }

    if (isCategory1Deal(deal)) {
      const columnIds = DeskingReader.getAllCColumnsIDs(deskingpaymentDetails);
      if (DealerPropertyHelper.isCreateDealWithNewAPIEnabled()) {
        await updateDeskingForColumnIds(columnIds);
      } else {
        await getMarketScanData({ columnIds });
      }
    }
    this.hideModal();
  };

  restoreFNIdefaults = async onRestoreCallBack => {
    const { addDefaultFNIs, DeskingActions, deskingpaymentDetails, deal } = this.props;

    await addDefaultFNIs({ columnIds: getColIdsToSetDefValues(deal, deskingpaymentDetails) });
    // on restore calculate price and cost again
    const columnBasedFNIs = recomputeFNIsForRespectiveColumns(deskingpaymentDetails);
    await DeskingActions.saveColumnBasedFNIs(columnBasedFNIs);
    const fnis = this.getNewFNIs();

    onRestoreCallBack(fnis);
  };

  getNewFNIs = () => {
    const { deskingpaymentDetails } = this.props;
    return DeskingReader.getFNISuperSet(deskingpaymentDetails);
  };

  onAccessoriesDataChange = selectedLender => async dueBills => {
    const {
      DeskingActions,
      deskingpaymentDetails,
      getMarketScanData,
      deal,
      syncPaymentDetails,
      category3dealTypeChanges,
      updateEMIAmountOfFNIDeal,
      updateDeskingForColumnIds,
      lenders,
    } = this.props;
    this.hideModal();

    await DeskingActions.saveAccessories({
      accessories: dueBills,
      columnIds: getColIdsToSetDefValues(deal, deskingpaymentDetails),
      applicableLender: selectedLender,
      lenders,
    });

    if (CalcEngineProperties.updateCalculationByBackend()) {
      await syncPaymentDetails();
    }

    if (isCategory2Deal(deal)) {
      await updateEMIAmountOfFNIDeal();
      return;
    }

    if (isCategory1Deal(deal)) {
      const columnIds = DeskingReader.getAllCColumnsIDs(deskingpaymentDetails);
      if (DealerPropertyHelper.isCreateDealWithNewAPIEnabled()) {
        await updateDeskingForColumnIds(columnIds);
      } else if (!CalcEngineProperties.updateByGalaxyEngine()) {
        await getMarketScanData({ columnIds });
      }
      return;
    }

    if (isCategory3Deal(deal)) {
      await category3dealTypeChanges();
    }
  };

  getUpdateModalRef = ref => {
    this.updateModal = ref;
  };

  showRebateUpdateModal = rebatesData => {
    const { getMarketScanData, deskingpaymentDetails } = this.props;
    const isGlobalUpdate = true;
    const heading = rebatesData?.headerContent?.heading;

    this.updateModal.show({
      component: Rebate,
      componentProps: {
        onClose: (refresh, showConfirmationModal) => {
          this.onCloseRebateModal(
            refresh,
            showConfirmationModal,
            this.hideModal,
            getMarketScanData,
            deskingpaymentDetails
          );
        },
        isGlobalUpdate,
        heading,
      },
    });
  };

  getDisplayNamesForFields = () => {
    const { deal, salesSetupInfo } = this.props;
    const deskingFieldsConfigs = SalesSetupReader.selectDeskingFields(salesSetupInfo);
    const dealType = getDealType(deal);
    const showFeesTaxesCat3Deal = DealerConfigReader.getDealerCountryCode() === COUNTRY_CODE.CANADA;
    const fieldsToBeShown = getDealTypeFields(dealType, {
      shouldIncludeFees: showFeesTaxesCat3Deal,
      shouldIncludeTaxes:
        showFeesTaxesCat3Deal || checkTaxRateForWholeSaleDealEnabled(deal, salesSetupInfo) || isDealerTradeDeal(deal),
    });
    return DeskingReader.getDefaultDIsplayNames(deskingFieldsConfigs, fieldsToBeShown);
  };

  selectTier = async value => {
    const {
      DeskingActions,
      deskingpaymentDetails,
      getMarketScanData,
      lenders,
      updateDeskingForColumnIds,
      syncPaymentDetails,
    } = this.props;

    const selectedColumn = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);
    const selectedLenderId = ColumnDataReader.getSelectedLenderId(selectedColumn);

    const tiers = this.getTiersList(selectedColumn, lenders);
    const matchedTier = findArrayItem(tiers, 'value', value);
    const columnIds = getColumnIdsWrtPaymentTypeOfColumn(selectedColumn, deskingpaymentDetails);
    await DeskingActions.setSelectedLenderTier({
      columnIds,
      lenderId: selectedLenderId,
      tier: {
        name: matchedTier.name,
        code: matchedTier.code,
        type: matchedTier.type,
      },
    });

    if (DealerPropertyHelper.isCreateDealWithNewAPIEnabled()) {
      updateDeskingForColumnIds(columnIds);
    } else if (CalcEngineProperties.updateCalculationByBackend()) {
      await syncPaymentDetails();
    } else {
      await getMarketScanData({ columnIds });
      DeskingActions.setSelectedLenderTier({
        columnIds,
        lenderId: selectedLenderId,
        resetFlag: true,
      });
    }
  };

  setFinanceContractNumber = async event => {
    const { DeskingActions, deskingpaymentDetails } = this.props;
    const allColumnsIds = DeskingReader.getAllCColumnsIDs(deskingpaymentDetails);
    this.setState({ financeContractNumberChanged: true });

    await DeskingActions.setValuesInColumns({
      data: _reduce(
        allColumnsIds,
        (acc, columnId) => ({
          ...acc,
          [columnId]: {
            [DESKING_FIELD_KEYS[LENDER_PAYMENT_FIELDS.FINANCE_CONTRACT_NUMBER]]: event?.target?.value,
            [DESKING_FIELD_KEYS[LENDER_PAYMENT_FIELDS.FINANCE_CONTRACT_STATUS_DTO]]: {},
          },
        }),
        {}
      ),
    });
  };

  getFinanceContractStatus = _debounce(
    async () => {
      const { financeContractNumberChanged } = this.state;
      const { savePaymentsDataForSelectedVehicle, deal, DeskingActions } = this.props;
      this.setState({ isFetchingFinanceContractStatus: true });
      const dealNumber = getDealNumber(deal);

      if (financeContractNumberChanged) {
        await savePaymentsDataForSelectedVehicle();
        await DeskingActions.getFinanceContractStatus(dealNumber);
        this.setState({ financeContractNumberChanged: false });
      } else {
        await DeskingActions.getFinanceContractStatus(dealNumber);
      }

      this.setState({ isFetchingFinanceContractStatus: false });
    },
    ES_REFETCH_DELAY,
    { leading: true, trailing: false }
  );

  savePaymentsDataForSelectedVehicleOnBlur = async () => {
    const { financeContractNumberChanged } = this.state;
    const { savePaymentsDataForSelectedVehicle } = this.props;

    if (financeContractNumberChanged) {
      await savePaymentsDataForSelectedVehicle();
      this.setState({ financeContractNumberChanged: false });
    }
  };

  fetchLatestDealData = async () => {
    const { deal, DeskingActions } = this.props;
    return DeskingActions.fetchDealAndUpdatePaymentDetails(getDealNumber(deal));
  };

  isFniUpdateDisabled = isDisabled => {
    const { salesSetupInfo } = this.props;
    const { disableFNIUpdatesFromDesking } = salesSetupInfo;
    return isDisabled || disableFNIUpdatesFromDesking;
  };

  renderDeskingField = fieldId => {
    const {
      onAccordionClick,
      activeAccordionKeys,
      deskingpaymentDetails,
      lenders,
      getFieldStatus,
      deal,
      renderShowMonthlyPaymentCheckBox,
    } = this.props;
    const {
      yearlyMiles,
      residualValue,
      inputDays,
      apr,
      financeContractNumberChanged,
      isFetchingFinanceContractStatus,
    } = this.state;

    const selectedColumn = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);
    const selectedTier = ColumnDataReader.getSelectedTier(selectedColumn);
    const { isDisabled, disableInputFieldOnly } = getFieldStatus(fieldId);

    switch (fieldId) {
      case LENDER_PAYMENT_FIELDS.LENDER: {
        const { lenderDN } = this.getDisplayNamesForFields();

        return (
          <PropertyControlledComponent controllerProperty={lenderDN}>
            <DropDownCell
              options={this.getTiersList(selectedColumn, lenders)}
              value={this.getTierValue(selectedTier)}
              onChange={this.selectTier}
              label={lenderDN}
              selectClass={styles.lenderTiers}
              placeholder={__('Select tiers')}
              disabled={isDisabled}
            />
            {/* <HeadingTitleCell label={lenderDN} /> */}
          </PropertyControlledComponent>
        );
      }
      case LENDER_PAYMENT_FIELDS.APR_MONEYFACTOR: {
        const { aprDN } = this.getDisplayNamesForFields();
        return (
          <PropertyControlledComponent controllerProperty={aprDN}>
            {!isInchcape() ? (
              <InputTitleCell
                type="number"
                label={aprDN}
                placeholder={isRRG() ? __('APR[[financeInterestRate]]') : __('APR/MF[[financeInterestRate]]')}
                onInputPressEnter={this.onBlurAPRField}
                disabled={isDisabled || disableInputFieldOnly}
                onChange={this.onAPRDataChange}
                onBlur={this.onBlurAPRField}
                value={apr}
                suffixText={isValueMoneyFactor(apr) ? 'MF' : '%'}
              />
            ) : (
              <HeadingTitleCell label={aprDN} />
            )}
          </PropertyControlledComponent>
        );
      }
      case LENDER_PAYMENT_FIELDS.RESIDUAL: {
        const { residualDN } = this.getDisplayNamesForFields();
        return (
          <PropertyControlledComponent controllerProperty={residualDN}>
            {!isInchcape() ? (
              <div ref={this.residualValueRef}>
                <InputTitleCell
                  type="number"
                  label={residualDN}
                  placeholder={__('Input Residual')}
                  onInputPressEnter={this.onBlurResidualData}
                  disabled={isDisabled || disableInputFieldOnly}
                  onChange={this.setNumberValuesInState('residualValue')}
                  onBlur={this.onBlurResidualData}
                  onFocus={this.scrollToResidualInput}
                  value={residualValue}
                  suffixText="%"
                />
              </div>
            ) : (
              <div ref={this.residualValueRef}>
                <HeadingTitleCell label={residualDN} />
              </div>
            )}
          </PropertyControlledComponent>
        );
      }
      case LENDER_PAYMENT_FIELDS.YEARLY_MILES: {
        const { yearlyMilesDN } = this.getDisplayNamesForFields();
        return (
          <PropertyControlledComponent controllerProperty={yearlyMilesDN}>
            <div ref={this.yearlyMilesRef}>
              <InputTitleCell
                type="number"
                label={yearlyMilesDN}
                placeholder={__('Input Yearly {{miles}}', {
                  miles: mileageLabeler.getMilesLabel(),
                })}
                suffixText={__('{{mi}}', {
                  mi: mileageLabeler.getMileageUnitLabel(),
                })}
                onInputPressEnter={this.onBlurYearlyMiles}
                disabled={isDisabled}
                onChange={this.setNumberValuesInState('yearlyMiles')}
                onBlur={this.onBlurYearlyMiles}
                onFocus={this.scrollToYearlyMilesInput}
                value={yearlyMiles}
              />
            </div>
            <PropertyControlledComponent controllerProperty={isInchcape()}>
              <YearlyMilesChangeModal
                ref={this.yearlyMilesChangeModalRef}
                currentTotalValue={getNumber(yearlyMiles)}
                isAllColumnUpdate
              />
            </PropertyControlledComponent>
          </PropertyControlledComponent>
        );
      }
      case LENDER_PAYMENT_FIELDS.REBATE: {
        const { rebatesDN } = this.getDisplayNamesForFields();
        const allRebates = DeskingReader.getRebatesSuperset({ deskingpaymentDetails });
        const columnRebates = ColumnDataReader.getColumnRebates(selectedColumn);
        const rebatesData = this.getRebatesData(allRebates, columnRebates, isDisabled, deal);
        return (
          <PropertyControlledComponent controllerProperty={rebatesDN}>
            <AccordionTitleCell
              id="rebate"
              {...rebatesData}
              activeKeys={activeAccordionKeys}
              onCollapseStatusChange={onAccordionClick}
              onUpdate={
                isRebateEditDisabled(isDisabled, deal) ? undefined : () => this.showRebateUpdateModal(rebatesData)
              }
            />
          </PropertyControlledComponent>
        );
      }
      case LENDER_PAYMENT_FIELDS.DUEBILLS: {
        const { showAddHEWarning } = this.state;
        const { dueBillsDN } = this.getDisplayNamesForFields();
        const accessoriesSuperSet = DeskingReader.getAccessoriesSuperSet(deskingpaymentDetails);
        const columnAccessories = ColumnDataReader.getColumnAccessories(selectedColumn);
        const { accessories, accessoriesHeader } = this.getAccessoriesForAccordian(
          accessoriesSuperSet,
          columnAccessories,
          isDisabled,
          showAddHEWarning
        );
        return (
          <PropertyControlledComponent controllerProperty={dueBillsDN}>
            <AccordionTitleCell
              id="accessories"
              headerContent={accessoriesHeader}
              activeKeys={activeAccordionKeys}
              onCollapseStatusChange={onAccordionClick}
              onUpdate={isDisabled ? null : () => this.showAccessoriesMenu(accessoriesHeader)}
              bodyContent={accessories}
              renderAdditionalComponent={renderShowMonthlyPaymentCheckBox(LENDER_PAYMENT_FIELDS.DUEBILLS)}
            />
          </PropertyControlledComponent>
        );
      }
      case LENDER_PAYMENT_FIELDS.FNIMENU: {
        const { fniMenuDN } = this.getDisplayNamesForFields();
        const fniSuperSet = DeskingReader.getFNISuperSet(deskingpaymentDetails);
        const columnFNIs = ColumnDataReader.getColumnFNIs(selectedColumn);
        const isFniUpdateDisabled = this.isFniUpdateDisabled(isDisabled);
        const { FNIs, FNIsHeader } = this.getFNIsForAccordian(fniSuperSet, columnFNIs, isFniUpdateDisabled);

        return (
          <PropertyControlledComponent controllerProperty={fniMenuDN}>
            <AccordionTitleCell
              id="finance"
              headerContent={FNIsHeader}
              activeKeys={activeAccordionKeys}
              onCollapseStatusChange={onAccordionClick}
              onUpdate={isDisabled ? undefined : () => this.showFNIMenu(FNIsHeader)}
              bodyContent={FNIs}
              badgeTitle={__('Pre-Sold')}
              renderAdditionalComponent={renderShowMonthlyPaymentCheckBox(LENDER_PAYMENT_FIELDS.FNIMENU)}
            />
          </PropertyControlledComponent>
        );
      }
      case LENDER_PAYMENT_FIELDS.DAYS_TO_FIRSTPAYMENT: {
        const { daysToFirstPayment } = this.getDisplayNamesForFields();
        return (
          <PropertyControlledComponent controllerProperty={daysToFirstPayment}>
            <InputTitleCell
              type="number"
              label={daysToFirstPayment}
              placeholder={__('Input Days')}
              hasSuffixItem={false}
              onInputPressEnter={this.onBlurDaystoFirstPayment}
              onBlur={this.onBlurDaystoFirstPayment}
              disabled={isDisabled}
              onChange={this.setNumberValuesInState('inputDays')}
              value={inputDays}
            />
          </PropertyControlledComponent>
        );
      }

      case LENDER_PAYMENT_FIELDS.FINANCE_CONTRACT_NUMBER: {
        const { financeContractNumberDN } = this.getDisplayNamesForFields();
        const { financeContractNumber, financeContractStatusNumber, financeContractStatusLastUpdated } =
          getFinanceContractStatusDetails(deskingpaymentDetails);

        return (
          <PropertyControlledComponent controllerProperty={financeContractNumberDN}>
            <InputTitleCell
              label={financeContractNumberDN}
              placeholder={__('Input Number')}
              hasSuffixItem
              suffixText={
                <FinanceContractSuffixIconComponent
                  financeContractNumberChanged={financeContractNumberChanged}
                  isFetchingFinanceContractStatus={isFetchingFinanceContractStatus}
                  financeContractNumber={financeContractNumber}
                  financeContractNumberDN={financeContractNumberDN}
                  financeContractStatusNumber={financeContractStatusNumber}
                  financeContractStatusLastUpdated={financeContractStatusLastUpdated}
                  getFinanceContractStatus={this.getFinanceContractStatus}
                />
              }
              onInputPressEnter={this.getFinanceContractStatus}
              onBlur={this.savePaymentsDataForSelectedVehicleOnBlur}
              onChange={this.setFinanceContractNumber}
              disabled={isDisabled}
              value={financeContractNumber}
            />
          </PropertyControlledComponent>
        );
      }
      case LENDER_PAYMENT_FIELDS.BUY_BACK_VALUE: {
        const { buyBackValueDN } = this.getDisplayNamesForFields();
        return (
          <PropertyControlledComponent controllerProperty={buyBackValueDN}>
            <HeadingTitleCell
              label={
                <div className={styles.calculatebtn}>
                  {buyBackValueDN}
                  <BuybackValueModal
                    buttonTitle={__('Calculate')}
                    deal={deal}
                    disabled={isDisabled}
                    onBuyBackValueUpdate={this.fetchLatestDealData}
                  />
                </div>
              }
            />
          </PropertyControlledComponent>
        );
      }

      case LENDER_PAYMENT_FIELDS.RATE_OF_INTEREST: {
        const { rateOfInterestDN } = this.getDisplayNamesForFields();

        return (
          <PropertyControlledComponent controllerProperty={rateOfInterestDN && isInchcape()}>
            <HeadingTitleCell label={rateOfInterestDN} />
          </PropertyControlledComponent>
        );
      }
      default:
        return null;
    }
  };

  render() {
    const { fieldKeysInOrder } = this.props;
    return (
      <div className={classNames(styles.sectionContainer, 'marginT16')}>
        {fieldKeysInOrder.map(fieldId => this.renderDeskingField(fieldId))}
        <UpdateModalStatic ref={this.getUpdateModalRef} />
      </div>
    );
  }
}

LenderPaymentTitleColumnSection.defaultProps = {
  onAccordionClick: _noop,
  activeAccordionKeys: EMPTY_ARRAY,
  getMarketScanData: _noop,
  deskingpaymentDetails: EMPTY_ARRAY,
  DeskingActions: EMPTY_OBJECT,
  fniAttributesCodes: EMPTY_OBJECT,
  componentInfos: EMPTY_OBJECT,
  onFNIUpdate: _noop,
  isDeskingViewOnly: false,
  addDefaultFNIs: _noop,
  addDefaultAccessories: _noop,
  category3dealTypeChanges: _noop,
  updateEMIAmountOfFNIDeal: _noop,
  syncPaymentDetails: _noop,
  savePaymentsDataForSelectedVehicle: _noop,
  updatePaymentWithItems: _noop,
  packagesAccessoriesList: EMPTY_ARRAY,
  handleCustomerApprovalOnRebateUpdate: _noop,
};

LenderPaymentTitleColumnSection.propTypes = {
  onAccordionClick: PropTypes.func,
  activeAccordionKeys: PropTypes.array,
  getMarketScanData: PropTypes.func,
  deskingpaymentDetails: PropTypes.array,
  DeskingActions: PropTypes.object,
  fniAttributesCodes: PropTypes.object,
  componentInfos: PropTypes.object,
  onFNIUpdate: PropTypes.func,
  isDeskingViewOnly: PropTypes.bool,
  addDefaultFNIs: PropTypes.func,
  addDefaultAccessories: PropTypes.func,
  category3dealTypeChanges: PropTypes.func,
  deal: PropTypes.object.isRequired,
  updateEMIAmountOfFNIDeal: PropTypes.func,
  fieldKeysInOrder: PropTypes.array.isRequired,
  lenders: PropTypes.array.isRequired,
  salesSetupInfo: PropTypes.object.isRequired,
  updateDeskingForColumnIds: PropTypes.func.isRequired,
  syncPaymentDetails: PropTypes.func,
  savePaymentsDataForSelectedVehicle: PropTypes.func,
  updatePaymentWithItems: PropTypes.func,
  packagesAccessoriesList: PropTypes.array,
  handleCustomerApprovalOnRebateUpdate: PropTypes.func,
};

export default withDeskingContext(withDefault(withHandleCustomerApprovals(LenderPaymentTitleColumnSection)));
