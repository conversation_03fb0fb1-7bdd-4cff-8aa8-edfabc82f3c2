import React, { PureComponent } from 'react';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';

import withDeskingContext from 'pages/desking/withDeskingContext';
import DealTypeSelectionHeaderComponent from 'pages/desking/components/dealDetails/cells/titleCells/DealTypeSelectionHeader';
import {
  getDealType,
  getDealNumber,
  isMultiVehicleDeskingEnabledV2,
  getDealStatus,
} from '@tekion/tekion-base/marketScan/readers/deal.reader';
import {
  CATEGORY_1_DEAL_TYPES,
  CATEGORY_2_DEAL_TYPES,
  CATEGORY_3_DEAL_TYPES,
  CATEGORY_4_DEAL_TYPES,
} from '@tekion/tekion-base/marketScan/constants/startDeal.constants';
import PropTypes from 'prop-types';
import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import { LEFT_PANEL_ITEMS } from 'pages/desking/desking.constants';
import * as SalesSetupReader from '@tekion/tekion-base/marketScan/readers/salesSetup.reader';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';
import { DEAL_STATUS } from 'pages/deallist/deal.constants';
import { DEAL_TYPES } from '@tekion/tekion-base/constants/deal/type';
import { TITLE_COLUMN_FIELDS } from '@tekion/tekion-base/marketScan/constants/deskingFields.constants';
import * as DealReader from '@tekion/tekion-base/marketScan/readers/deal.reader';
import CalcEngineProperties from 'utils/CalcEngineProperties';
import { isInchcape } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
import GlobalWarnings from 'pages/desking/components/globalWarnings';
import { getDealTypesWithDisabledForInchCape } from '../../dealDetails.utils';
import { getDisbaledValue } from './dealTypeSelectionHeader.utils';

class DealTypeSelectionHeader extends PureComponent {
  static propTypes = {
    deal: PropTypes.object,
    salesSetupInfo: PropTypes.array.isRequired,
    onDealTypeChange: PropTypes.func.isRequired,
    restoreWithDeal: PropTypes.func.isRequired,
    updateDeskingOnDealTypeChange: PropTypes.func.isRequired,
    saveDealData: PropTypes.func.isRequired,
    DeskingActions: PropTypes.object.isRequired,
    getFieldStatus: PropTypes.func.isRequired,
  };

  static defaultProps = {
    deal: EMPTY_OBJECT,
  };

  changeDealStatus = async () => {
    const { saveDealData, deal: previousDealObj, DeskingActions } = this.props;
    await DeskingActions.setDealStatus(DEAL_STATUS.FINANCE_AND_INSURANCE);
    const { response: newDealObj } = await saveDealData();
    if (_isEmpty(newDealObj)) return DeskingActions.setDeal(previousDealObj);
    await DeskingActions.setDeal(newDealObj);
    DeskingActions.setSelectedtab(LEFT_PANEL_ITEMS.FINANCE_AND_INSURANCE);
    return null;
  };

  onDealTypeChange = async dealType => {
    const { onDealTypeChange, deal, updateDeskingOnDealTypeChange, updateDealItemPaymentDetails, DeskingActions } =
      this.props;

    if (
      isMultiVehicleDeskingEnabledV2(deal) &&
      dealType !== DEAL_TYPES.MOTABILITY &&
      !CATEGORY_1_DEAL_TYPES.includes(dealType)
    ) {
      await DeskingActions.changeMultiVehicleDeskingStatus(false);
    }

    if (getDealType(deal) !== dealType) {
      if (DealerPropertyHelper.isCreateDealWithNewAPIEnabled()) {
        await updateDeskingOnDealTypeChange({ dealType });
        GlobalWarnings.showWarnings();
        return;
      }

      if (CalcEngineProperties.updateCalculationByBackend()) {
        await updateDealItemPaymentDetails({ dealType });
        GlobalWarnings.showWarnings();
        return;
      }
      await onDealTypeChange(dealType);
      GlobalWarnings.showWarnings();
      if (dealType === DEAL_TYPES.FNI) {
        this.changeDealStatus();
      }
    }
  };

  onDealRestore = deal => {
    const { restoreWithDeal } = this.props;
    restoreWithDeal(deal);
  };

  findCategoryOfDealType = () => {
    const { deal } = this.props;
    const dealType = getDealType(deal);
    if (CATEGORY_1_DEAL_TYPES.includes(dealType)) return CATEGORY_1_DEAL_TYPES;
    if (CATEGORY_2_DEAL_TYPES.includes(dealType)) return CATEGORY_2_DEAL_TYPES;
    if (CATEGORY_3_DEAL_TYPES.includes(dealType)) return CATEGORY_3_DEAL_TYPES;
    if (CATEGORY_4_DEAL_TYPES.includes(dealType)) return CATEGORY_4_DEAL_TYPES;
    return EMPTY_ARRAY;
  };

  getDealTypesWithDisabled = () => {
    const { salesSetupInfo, deal } = this.props;
    const dealStatus = getDealStatus(deal);
    const defaultDealTypes = [...(SalesSetupReader.selectDealTypes(salesSetupInfo) || EMPTY_ARRAY)];
    const category = this.findCategoryOfDealType();
    const isDealPaused = DealReader.manualPaused(deal);
    const checkDealStatus =
      dealStatus === DEAL_STATUS.BOOKED ||
      dealStatus === DEAL_STATUS.PRE_CLOSED ||
      dealStatus === DEAL_STATUS.CLOSED_OR_SOLD;
    if (isInchcape()) return getDealTypesWithDisabledForInchCape(defaultDealTypes, getDealType(deal));

    return defaultDealTypes.map(dealTypeOption => {
      const dealType = _get(dealTypeOption, 'value');
      const disabledValue = getDisbaledValue(dealType, isDealPaused);
      return {
        ...dealTypeOption,
        disabled: disabledValue ? true : !category.includes(dealType) && checkDealStatus,
      };
    });
  };

  render() {
    const { deal, deskingpaymentDetails, getFieldStatus, getLabelFromPaymentOptions, salesSetupInfo } = this.props;
    const dealType = getDealType(deal);
    const dealNumber = getDealNumber(deal);
    const { isDisabled, isDealViewOnly } = getFieldStatus(TITLE_COLUMN_FIELDS.DEAL_TYPE_SELECTION_HEADER);

    return (
      <DealTypeSelectionHeaderComponent
        dealTypes={this.getDealTypesWithDisabled()}
        selectedDealType={dealType}
        onChange={this.onDealTypeChange}
        disabled={isDisabled}
        dealNumber={dealNumber}
        onDealRestore={this.onDealRestore}
        deal={deal}
        isDealViewOnly={isDealViewOnly}
        deskingpaymentDetails={deskingpaymentDetails}
        getLabelFromPaymentOptions={getLabelFromPaymentOptions}
        salesSetupInfo={salesSetupInfo}
      />
    );
  }
}

export default withDeskingContext(DealTypeSelectionHeader);
