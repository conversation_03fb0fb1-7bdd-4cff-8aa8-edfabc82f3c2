import React, { useCallback, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import _get from 'lodash/get';
import _noop from 'lodash/noop';
import _find from 'lodash/find';
import _isEmpty from 'lodash/isEmpty';
import _gt from 'lodash/gt';
import _isEqual from 'lodash/isEqual';
import _filter from 'lodash/filter';

import { EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { isDealConfirmed, isDealIntermittent } from '@tekion/tekion-base/marketScan/helpers/deal.helpers';
import { INTEGRATION_KEYS } from '@tekion/tekion-base/constants/retail/salesSetup.constants';
import {
  isMultipleDownPaymentsEnabled,
  getAutoRewardsDetails,
  getPrimaryVehicleStatus,
  getLeaseWorkingCashConfigs,
  maxTermPaymentsAllowed,
  getDealNumber,
  postInvoiceDp,
  getDealType,
} from '@tekion/tekion-base/marketScan/readers/deal.reader';
import { DEAL_TYPES } from '@tekion/tekion-base/constants/deal/type';
import * as SalesSetupReader from '@tekion/tekion-base/marketScan/readers/salesSetup.reader';
import * as ColumnDataReader from '@tekion/tekion-base/marketScan/readers/columnData.reader';
import { TITLE_COLUMN_FIELDS } from '@tekion/tekion-base/marketScan/constants/deskingFields.constants';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import CurrencyInput from '@tekion/tekion-widgets/src/organisms/currencyInput';
import FontIcon from '@tekion/tekion-components/src/atoms/FontIcon';
import Popover from '@tekion/tekion-components/src/molecules/popover';
import Button from '@tekion/tekion-components/src/atoms/Button';
import Heading from '@tekion/tekion-components/src/atoms/Heading';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';
import COLORS from 'tstyles/exports.scss';
import { isInchcape, isRRG } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
import { canEveryColCanHaveDiffDownpaymant } from '@tekion/tekion-base/marketScan/utils/desking.utils';

import withDeskingContext from 'pages/desking/withDeskingContext';
import { DEAL_STATUS, PROGRAM_CODE } from 'pages/deallist/deal.constants';
import { DOWN_PAYMENT_TYPES, DOWNPAYMENT_PERCENTAGE, SOURCE_TYPE } from 'pages/desking/desking.constants';
import { DEAL_SOURCE } from '@tekion/tekion-base/marketScan/constants/deal.constants';
import commonStyles from 'pages/desking/components/dealDetails/commonstyles.module.scss';
import classNames from 'classnames';
import withAddOnAfter from 'molecules/inputWithAddOnAfter';
import { getNumber } from 'utils';
import { isInchCapeOrRrg, isPurchaseOnlyOrDealerTrade } from 'utils/desking.helpers';

import { LEASE_WORKING_CASH } from '@tekion/tekion-base/marketScan/constants/constants';
import CurrencyInputCellRenderer from './DownPayment.CurrencyInputCellRenderer';
import InfoPopover from './InfoPopover';
import styles from './downPayment.module.scss';
import { doesDealContainAnyGenericPaymentSubType } from '../../../../../../utils/deal.util';
import InvoiceAPI from '../../../invoicing/actions/invoicing.api';
import { INVOICE_STATUS, INVOICE_TYPES } from '../../../invoicing/invoicing.constants';

const CurrencyInputWithAddOnAfter = withAddOnAfter(CurrencyInput);

const depositDisplayName = isLeasePaymentType =>
  isRRG() && isLeasePaymentType ? __('First increased payment') : __('Deposit');

const renderDepositSuffix = (
  currDeposit,
  dealConfirmed,
  deposit,
  isFixedDepositCollectedforZdx,
  isLeasePaymentType
) => {
  let content = '';
  const depositYetToBeCollected = currDeposit && !deposit && !isFixedDepositCollectedforZdx;
  const depositDN = depositDisplayName(isLeasePaymentType);

  if (dealConfirmed) content = __('{{depositDN}} Paid & Deal Confirmed', { depositDN });
  else if (depositYetToBeCollected) content = __('{{depositDN}} yet to be collected', { depositDN });
  else if (isFixedDepositCollectedforZdx) content = __('{{depositDN}} amount cannot be changed', { depositDN });

  return (
    <PropertyControlledComponent
      controllerProperty={dealConfirmed || depositYetToBeCollected || isFixedDepositCollectedforZdx}>
      <Popover
        trigger="hover"
        content={
          <div className={isFixedDepositCollectedforZdx ? styles.fixedDepositPopover : styles.depositPopover}>
            <div className={styles.depositText}>{content}</div>
          </div>
        }>
        <PropertyControlledComponent controllerProperty={dealConfirmed || isFixedDepositCollectedforZdx}>
          <FontIcon color={COLORS.parisGreen} className="cursor-pointer m-r-8">
            icon-circle-check-active
          </FontIcon>
        </PropertyControlledComponent>

        <PropertyControlledComponent controllerProperty={!dealConfirmed && depositYetToBeCollected}>
          <Button view="icon" icon="icon-info" />
        </PropertyControlledComponent>
      </Popover>
    </PropertyControlledComponent>
  );
};

const renderCollectAutoRewardsSuffix = (isDeskingViewOnly, autoRewardTransactionIds, onClickOfCheckBalance) => {
  if (!_isEmpty(autoRewardTransactionIds)) {
    return (
      <FontIcon color={COLORS.parisGreen} className="cursor-pointer m-r-8">
        icon-circle-check-active
      </FontIcon>
    );
  }

  return (
    <Popover
      trigger="hover"
      content={
        <div className={styles.depositPopover}>
          <div className={styles.depositText}>{__('Check Balance')}</div>
        </div>
      }>
      <Button
        className="cursor-pointer m-r-8"
        view="icon"
        icon="icon-credit1"
        onClick={onClickOfCheckBalance}
        disabled={isDeskingViewOnly}
      />
    </Popover>
  );
};

const DownPaymentSection = ({
  downPayments,
  getMarketScanData,
  DeskingActions,
  savePaymentsDataForSelectedVehicle,
  deferredPayments,
  isDealDirty,
  deal,
  deskingpaymentDetails,
  saveDealData,
  updateDeskingForColumnIds,
  setDeposit,
  salesSetupInfo,
  onBlurDepositField,
  setAutoRewards,
  onBlurAutoRewardField,
  onClickOfCheckBalance,
  getFieldStatus,
}) => {
  const isMultiDownPaymentsEnabled = isMultipleDownPaymentsEnabled(deal);
  const dealType = getDealType(deal);
  const [isCustomerCashInvoiceGenerated, setIsCustomerCashInvoiceGenerated] = useState(false);

  useEffect(() => {
    if (isInchcape() && postInvoiceDp(deal) && dealType !== DEAL_TYPES.FNI) fetchAllInvoices();
  }, [fetchAllInvoices, dealType]);

  const fetchAllInvoices = useCallback(async () => {
    const dealNumber = getDealNumber(deal);
    const { response } = await InvoiceAPI.fetchAllInvoices(dealNumber);
    DeskingActions.setInvoiceData(response?.invoices);
    if (!_isEmpty(response)) {
      const isInvoiceCustomerCashGen = !_isEmpty(
        _filter(
          response?.invoices,
          invoice =>
            invoice?.type === INVOICE_TYPES.DEPOSIT &&
            invoice?.subType === 'CUSTOMER_CASH' &&
            invoice?.status === INVOICE_STATUS.GENERATED
        )
      );
      setIsCustomerCashInvoiceGenerated(isInvoiceCustomerCashGen);
    }
  }, [deal]);

  const renderPopoverContent = () => {
    if (isCustomerCashInvoiceGenerated)
      return (
        <div className="padding1">
          <div>{__('Invoice for the complete customer cash amount has already been generated')}</div>
        </div>
      );
    return EMPTY_STRING;
  };

  const { deposit, currDeposit } = _get(deal, 'depositDetails') || EMPTY_OBJECT;
  const {
    currDeposit: autoRewardCurrDeposit,
    deposit: autoRewardDepositAmount,
    transactionIds: autoRewardTransactionIds,
  } = getAutoRewardsDetails(deal);
  const dealConfirmed = isDealConfirmed(deal);
  const dealIntermittent = isDealIntermittent(deal);
  const selectedColumn = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);
  const downpaymentsFromDeal = ColumnDataReader.getDownpayments(selectedColumn);
  const isLeasePaymentType = ColumnDataReader.isLeasePaymentType(selectedColumn);
  const outOfPocketCash = getNumber(ColumnDataReader.getSelectedOutOfPocketCash(selectedColumn));
  const selectedDownPaymentId = ColumnDataReader.getSelectedDownPaymentID(selectedColumn);
  const leaseWorkingCashConfigs = getLeaseWorkingCashConfigs(deal);
  const isWorkingCashCollectDriveOffEnabled = _get(
    _find(
      leaseWorkingCashConfigs,
      ({ leaseWorkingCashType }) => leaseWorkingCashType === LEASE_WORKING_CASH.CASH_ALWAYS_COVERS_DRIVEOFF
    ),
    'enabled'
  );
  const depositDisabled = SalesSetupReader.getDepositDisabledVehicleStatuses(salesSetupInfo);
  const isConfirmStatusEnabled = SalesSetupReader.isDealStatusEnabled(salesSetupInfo, DEAL_STATUS.CONFIRMED);
  const isEnableCustomerCashPercentage = SalesSetupReader.getIsEnableCustomerCashPercentage(salesSetupInfo);
  const isAutoRewardEnabled = SalesSetupReader.isIntegrationEnabled(
    SalesSetupReader.selectIntegrationKeys(salesSetupInfo),
    INTEGRATION_KEYS.AUTO_REWARDS
  );
  const { isDisabled } = getFieldStatus(TITLE_COLUMN_FIELDS.DOWNPAYMENT_SECTION);
  const { isDisabled: depositInputDisabled } = getFieldStatus(TITLE_COLUMN_FIELDS.DEPOSIT);
  const isCampingWorldEnabled = DealerPropertyHelper.isRVDealerEnabled();
  const { type, source, program, sourceType } = deal;
  const isAECProgram = DealerPropertyHelper.isAECProgram();
  const isFixedDepositCollectedforZdx =
    _isEqual(source, DEAL_SOURCE.INTERNET) &&
    _isEqual(sourceType, SOURCE_TYPE.OMS) &&
    _isEqual(program, PROGRAM_CODE.ACURA_NEW) &&
    (_gt(currDeposit, 0) || _gt(deposit, 0)) &&
    isAECProgram;

  const showDepositSection = isConfirmStatusEnabled && !(isInchCapeOrRrg() && isPurchaseOnlyOrDealerTrade(type));

  const doesDealcontainAnyGenericSubType = doesDealContainAnyGenericPaymentSubType(deal);
  const isDeferredPaymentEnabled =
    isRRG() && doesDealcontainAnyGenericSubType ? false : SalesSetupReader.getIsDeferredPaymentEnabled(salesSetupInfo);

  return (
    <div className={classNames(styles.container)}>
      {downPayments.map((downPayment, index) => (
        <div
          // eslint-disable-next-line react/no-array-index-key
          key={index}
          className={classNames(commonStyles.titleCell, styles.rowContainer, 'full-width d-flex flex-row')}>
          {' '}
          {/*eslint-disable-line */}
          {isEnableCustomerCashPercentage && (
            <Heading size={5}>
              {isMultiDownPaymentsEnabled || isCampingWorldEnabled
                ? `#${index + 1}`
                : __(`Cust. Cash {{value}}`, { value: index + 1 })}
            </Heading>
          )}
          {!isEnableCustomerCashPercentage && (
            <Heading size={5}>
              {isMultiDownPaymentsEnabled
                ? `#${index + 1}`
                : __('Customer Cash {{value}}', { value: maxTermPaymentsAllowed(deal) > 1 ? index + 1 : EMPTY_STRING })}
            </Heading>
          )}
          <div className={styles.infoIconSpace}>
            <PropertyControlledComponent
              controllerProperty={
                isLeasePaymentType &&
                selectedDownPaymentId === index &&
                getNumber(downPayment[DOWN_PAYMENT_TYPES.LEASE_DOWNPAYMENTS]) !== outOfPocketCash &&
                !isInchcape()
              }>
              <InfoPopover
                outOfPocketCash={outOfPocketCash}
                isWorkingCashCollectDriveOffEnabled={isWorkingCashCollectDriveOffEnabled}
              />
            </PropertyControlledComponent>
          </div>
          {isEnableCustomerCashPercentage && isCampingWorldEnabled && (
            <div className={classNames('marginL4', styles.textPercentageContainer)}>
              <CurrencyInputCellRenderer
                value={downPayment[DOWNPAYMENT_PERCENTAGE] || 0}
                onDownPaymentChange={DeskingActions.onDownPaymentChange}
                getMarketScanData={getMarketScanData}
                DeskingActions={DeskingActions}
                savePaymentsDataForSelectedVehicle={savePaymentsDataForSelectedVehicle}
                downpaymentId={index}
                deferredPayments={deferredPayments}
                disabled={isDisabled}
                isDealDirty={isDealDirty}
                downPaymentType={DOWNPAYMENT_PERCENTAGE}
                deskingpaymentDetails={deskingpaymentDetails}
                deal={deal}
                className="marginL8"
                inputClassName={isMultiDownPaymentsEnabled ? commonStyles.LOAN_CASH_BORDER : null}
                updateDeskingForColumnIds={updateDeskingForColumnIds}
                isDeferredPaymentEnabled={isDeferredPaymentEnabled}
                isEnableCustomerCashPercentage={isEnableCustomerCashPercentage}
              />
            </div>
          )}
          <Popover placement="top" trigger="hover" content={renderPopoverContent()}>
            <div className={classNames('marginL4', styles.textInputContainer)}>
              <CurrencyInputCellRenderer
                value={
                  canEveryColCanHaveDiffDownpaymant()
                    ? downpaymentsFromDeal[index]
                    : downPayment[DOWN_PAYMENT_TYPES.LOAN_DOWNPAYMENTS]
                }
                onDownPaymentChange={DeskingActions.onDownPaymentChange}
                getMarketScanData={getMarketScanData}
                DeskingActions={DeskingActions}
                savePaymentsDataForSelectedVehicle={savePaymentsDataForSelectedVehicle}
                downpaymentId={index}
                deferredPayments={deferredPayments}
                disabled={isDisabled || isCustomerCashInvoiceGenerated}
                isDealDirty={isDealDirty}
                downPaymentType={DOWN_PAYMENT_TYPES.LOAN_DOWNPAYMENTS}
                deskingpaymentDetails={deskingpaymentDetails}
                deal={deal}
                className="marginL8"
                inputClassName={isMultiDownPaymentsEnabled ? commonStyles.LOAN_CASH_BORDER : null}
                updateDeskingForColumnIds={updateDeskingForColumnIds}
                isDeferredPaymentEnabled={isDeferredPaymentEnabled}
              />
              <PropertyControlledComponent controllerProperty={isMultiDownPaymentsEnabled}>
                <CurrencyInputCellRenderer
                  value={downPayment[DOWN_PAYMENT_TYPES.LEASE_DOWNPAYMENTS]}
                  onDownPaymentChange={DeskingActions.onDownPaymentChange}
                  getMarketScanData={getMarketScanData}
                  className="marginL8"
                  inputClassName={commonStyles.LEASE_ONE_PAY_BORDER}
                  DeskingActions={DeskingActions}
                  savePaymentsDataForSelectedVehicle={savePaymentsDataForSelectedVehicle}
                  downpaymentId={index}
                  deferredPayments={deferredPayments}
                  disabled={isDisabled}
                  isDealDirty={isDealDirty}
                  downPaymentType={DOWN_PAYMENT_TYPES.LEASE_DOWNPAYMENTS}
                  deskingpaymentDetails={deskingpaymentDetails}
                  deal={deal}
                  saveDealData={saveDealData}
                  updateDeskingForColumnIds={updateDeskingForColumnIds}
                  isDeferredPaymentEnabled={isDeferredPaymentEnabled}
                />
              </PropertyControlledComponent>
            </div>
          </Popover>
        </div>
      ))}
      <PropertyControlledComponent controllerProperty={showDepositSection}>
        <div
          className={classNames(
            commonStyles.titleCell,
            styles.rowContainer,
            'full-width d-flex flex-row justify-content-between'
          )}>
          <Heading className="m-r-20" size={5}>
            {depositDisplayName(isLeasePaymentType)}
          </Heading>

          <CurrencyInputWithAddOnAfter
            className={classNames(styles.deposit, styles.fullWidth)}
            onChange={setDeposit}
            value={currDeposit || deposit}
            id="DEPOSIT"
            addonAfter={renderDepositSuffix(
              currDeposit,
              dealConfirmed,
              deposit,
              isFixedDepositCollectedforZdx,
              isLeasePaymentType
            )}
            disabled={
              depositInputDisabled ||
              depositDisabled.includes(getPrimaryVehicleStatus(deal)) ||
              dealConfirmed ||
              dealIntermittent ||
              isFixedDepositCollectedforZdx
            }
            onBlur={onBlurDepositField}
            enforcePrecision={false}
          />
        </div>
      </PropertyControlledComponent>
      {isAutoRewardEnabled && (
        <div
          className={classNames(
            commonStyles.titleCell,
            styles.rowContainer,
            'full-width d-flex flex-row justify-content-between'
          )}>
          <Heading className="m-r-20" size={5}>
            {__('Auto Awards')}
          </Heading>

          <CurrencyInputWithAddOnAfter
            className={classNames(styles.deposit, styles.fullWidth)}
            onChange={setAutoRewards}
            id="autoRewardDepositAmount"
            value={autoRewardCurrDeposit || autoRewardDepositAmount}
            addonAfter={renderCollectAutoRewardsSuffix(isDisabled, autoRewardTransactionIds, onClickOfCheckBalance)}
            disabled={isDisabled || !_isEmpty(autoRewardTransactionIds)}
            onBlur={onBlurAutoRewardField}
            enforcePrecision={false}
          />
        </div>
      )}
    </div>
  );
};

DownPaymentSection.defaultProps = {
  isDealDirty: false,
  setDeposit: _noop,
  salesSetupInfo: EMPTY_OBJECT,
  onBlurDepositField: _noop,
  setAutoRewards: _noop,
  onClickOfCheckBalance: _noop,
  onBlurAutoRewardField: _noop,
};

DownPaymentSection.propTypes = {
  downPayments: PropTypes.array.isRequired,
  getMarketScanData: PropTypes.func.isRequired,
  deskingpaymentDetails: PropTypes.object.isRequired,
  DeskingActions: PropTypes.object.isRequired,
  savePaymentsDataForSelectedVehicle: PropTypes.func.isRequired,
  deferredPayments: PropTypes.object.isRequired,
  isDealDirty: PropTypes.bool,
  deal: PropTypes.object.isRequired,
  saveDealData: PropTypes.func.isRequired,
  updateDeskingForColumnIds: PropTypes.func.isRequired,
  setDeposit: PropTypes.func,
  salesSetupInfo: PropTypes.object,
  onBlurDepositField: PropTypes.func,
  setAutoRewards: PropTypes.func,
  onBlurAutoRewardField: PropTypes.func,
  onClickOfCheckBalance: PropTypes.func,
  getFieldStatus: PropTypes.func.isRequired,
};
export default withDeskingContext(DownPaymentSection);
