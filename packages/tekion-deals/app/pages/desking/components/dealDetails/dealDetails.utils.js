import React from 'react';
import produce from 'immer';
import { defaultMemoize } from 'reselect';

import _split from 'lodash/split';
import _invert from 'lodash/invert';
import _reduce from 'lodash/reduce';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _map from 'lodash/map';
import _flatten from 'lodash/flatten';
import _compact from 'lodash/compact';
import _forEach from 'lodash/forEach';
import _filter from 'lodash/filter';
import _uniqBy from 'lodash/unionBy';
import _findIndex from 'lodash/findIndex';
import _clone from 'lodash/clone';

import * as DealReader from '@tekion/tekion-base/marketScan/readers/deal.reader';
import * as DeskingReader from '@tekion/tekion-base/marketScan/readers/desking.reader';
import * as ColumnDataReader from '@tekion/tekion-base/marketScan/readers/columnData.reader';
import * as SalesSetupReader from '@tekion/tekion-base/marketScan/readers/salesSetup.reader';
import { isCategory1Deal, isCategory2Deal } from '@tekion/tekion-base/marketScan/utils/desking.utils';
import {
  DEPOSIT_TYPE_VS_LABEL,
  TAX_AND_ZIP_CODE_DETAILS_VS_KEY,
  DATA_SOURCE_VS_DISPLAY_LABELS,
} from '@tekion/tekion-base/marketScan/constants/desking.constants';
import { MORE_FIELDS } from '@tekion/tekion-base/marketScan/constants/deskingFields.constants';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';

import { uuid, getNumber, getLenderCodeName } from 'utils';
import { isCanadaCat3Deal, checkTaxRateForWholeSaleDealEnabled, isDealerTradeDeal } from 'utils/deal.util';
import { generateColumnId } from 'utils/markteScan.util';
import { isCanadaDealer } from 'utils/dealerUtils';
import * as DealerConfigReader from 'utils/dealerConfig.reader';
import CalcEngineProperties from 'utils/CalcEngineProperties';
import { PAYMENT_TYPES, MANUALLY_UPDATED_API_KEY, RESIDUAL_DISPLAY_TYPE } from 'pages/desking/desking.constants';
import { DEAL_TYPES } from '@tekion/tekion-base/constants/deal/type';
import { EMPTY_ARRAY, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { isInchcape } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
import { canEditReabtesOnDesking } from 'permissions/desking.permissions';
import { isNewVehicle } from 'utils/vehicleReader';

import styles from './dealDetails.module.scss';

export function getDuplicatedColumnData(columnData) {
  const duplicateColumnData = produce(columnData, draft => {
    const { paymentOption, dealVehicleId, selectedLenderId = '', termPaymentDetails = {} } = draft;
    const terms = termPaymentDetails[selectedLenderId] || {};

    draft.id = generateColumnId(paymentOption, dealVehicleId);
    draft.termPaymentDetails[selectedLenderId] =
      Object.entries(terms).reduce((acc, [key, values]) => ({ ...acc, [key]: { ...values, id: uuid() } }), {}) || {};
    draft.selected = false;
  });
  return duplicateColumnData;
}

export const getTaxSummary = ({ deskingpaymentDetails, deal, salesSetupInfo }) => {
  const columnData = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);
  const generalTaxFeeSettings = SalesSetupReader.generalTaxFeeSettings(salesSetupInfo);
  const isShowHstSeparately = SalesSetupReader.getShowHstSeparately(salesSetupInfo);
  const dealerMarketId = SalesSetupReader.getDealerMarketId(salesSetupInfo);

  const { id } = columnData;
  const isCalcEngineEnabled = CalcEngineProperties.showCalcEngineView();
  const isGalaxyEnabled = CalcEngineProperties.showGalaxyView();
  const isTaxBreakUpEnabled = DealerPropertyHelper.isTaxBreakUpEnabled() && DealReader.isTaxBreakUpEnabled(deal);
  const countryCode = DealerConfigReader.getDealerCountryCode();
  const { marketId } = DealReader.getTaxAndZipCodeDetails(deal, TAX_AND_ZIP_CODE_DETAILS_VS_KEY.LEASE);
  const stateFeeTaxOptions = DealReader.getStateFeeTaxOptions(
    deal,
    ColumnDataReader.getColumnTaxAndZipCodeType(columnData)
  );
  const isTaxRateForWholeSaleDealEnabled = checkTaxRateForWholeSaleDealEnabled(deal, salesSetupInfo);
  const isTaxRateForDealerTradeDealEnabled = isDealerTradeDeal(deal);

  if (isGalaxyEnabled) {
    return DeskingReader.getTaxesSummaryForGalaxy({
      deskingpaymentDetails,
      id,
      generalTaxFeeSettings,
      dealerMarketId,
      stateFeeTaxOptions,
      isCalcEngineEnabled,
      countryCode,
      isTaxBreakUpEnabled,
      deal,
      marketId,
      isTaxRateForWholeSaleDealEnabled,
      isTaxRateForDealerTradeDealEnabled,
    });
  }

  if (isCategory2Deal(deal) || checkTaxRateForWholeSaleDealEnabled(deal, salesSetupInfo) || isDealerTradeDeal(deal)) {
    return DeskingReader.getTaxesForDueBillsAndFniMenu(
      columnData,
      isCalcEngineEnabled,
      isGalaxyEnabled,
      isShowHstSeparately,
      deal
    );
  }

  if (isCategory1Deal(deal) || isCanadaCat3Deal(deal)) {
    return DeskingReader.getTaxesForAccordian(
      deskingpaymentDetails,
      id,
      generalTaxFeeSettings,
      dealerMarketId,
      stateFeeTaxOptions,
      isCalcEngineEnabled,
      countryCode,
      isTaxBreakUpEnabled,
      deal
    );
  }
  return {
    totalTax: 0,
    titleColumnData: [],
    infoColumnData: [],
  };
};

export const getSecDepAndTypeFromLabel = defaultMemoize(selectedLabel => {
  const isGalaxyEnabled = CalcEngineProperties.updateByGalaxyEngine();
  const defaultSecurityDeposit = isGalaxyEnabled ? 0 : null;
  const labelSplit = _split(selectedLabel, '(');
  const securityDeposit = labelSplit[0] || defaultSecurityDeposit;
  const securityDepositType = _invert(DEPOSIT_TYPE_VS_LABEL)[_split(labelSplit[1], ')')[0]] || null;

  return { securityDeposit, securityDepositType };
});

export const getOverriddenResidualOnYearlyMilesChange = deskingpaymentDetails =>
  _reduce(
    deskingpaymentDetails,
    (acc, columnData) => {
      const { id } = columnData;
      const existingResidual = ColumnDataReader.getResidualData(columnData);
      const paymentType = ColumnDataReader.getPaymentType(columnData);
      const residualDisplayType = ColumnDataReader.getResidualDisplayType(columnData);

      if (
        (paymentType === PAYMENT_TYPES.LEASE || paymentType === PAYMENT_TYPES.ONE_TIME_LEASE) &&
        !existingResidual?.residualOverridden
      ) {
        const changedValue = null;
        const residual = produce(existingResidual, draft => {
          if (residualDisplayType === RESIDUAL_DISPLAY_TYPE.AMOUNT) {
            draft.totalValue = changedValue;
          } else {
            draft.totalPercentage = changedValue;
          }
          draft.residualOverridden = getNumber(changedValue) > 0;
          draft[MANUALLY_UPDATED_API_KEY] = true;
        });

        acc[id] = { residual };
      }

      return acc;
    },
    {}
  );
export const getDealTypesWithDisabledForInchCape = (defaultDealTypes, dealType) => {
  if ([DEAL_TYPES.DEALER_TRADE, DEAL_TYPES.MOTABILITY].includes(dealType)) {
    return _map(defaultDealTypes, dealType => ({
      ...dealType,
      disabled: true,
    }));
  }

  const restrictDealTypeChange = [DEAL_TYPES.DEALER_TRADE, DEAL_TYPES.MOTABILITY];
  return _map(defaultDealTypes, dealType => ({
    ...dealType,
    disabled: dealType?.value && restrictDealTypeChange.includes(dealType.value),
  }));
};

export const getUniqFeesIncludingDeletedFeesForSelectedVehicleColumns = (
  deskingpaymentDetails = EMPTY_ARRAY,
  primaryVehicleId
) => {
  const selectedVehicleColumns =
    _filter(deskingpaymentDetails || EMPTY_ARRAY, ({ dealVehicleId: id }) => id === primaryVehicleId) || EMPTY_ARRAY;
  const allColumnsFees = _map(selectedVehicleColumns, column => ColumnDataReader.getColumnFees(column));
  const uniqFees = _uniqBy(_compact(_flatten(allColumnsFees)), 'feeCode');
  return uniqFees;
};

export const sortDeskingpaymentDetails = deskingpaymentDetails => {
  const paymentDetails = _clone(deskingpaymentDetails);
  const oemOfferColumnIndex = _findIndex(paymentDetails, columnData => ColumnDataReader.isOemOfferColumn(columnData));
  if (oemOfferColumnIndex !== -1) {
    const oemOfferColumn = paymentDetails.splice(oemOfferColumnIndex, 1)[0];
    paymentDetails.unshift(oemOfferColumn);
  }
  return paymentDetails;
};

export const checkLenderMBFCA = ({ deskingpaymentDetails = EMPTY_ARRAY, lenders = EMPTY_ARRAY }) => {
  let isAnyLenderMBFCA = false;
  _forEach(deskingpaymentDetails, columnData => {
    const selectedLenderId = ColumnDataReader.getSelectedLenderId(columnData);
    const selectedLenderCodeName = getLenderCodeName(lenders, selectedLenderId);
    isAnyLenderMBFCA = isAnyLenderMBFCA || checkSelectedLenderCode(selectedLenderCodeName);
    // eslint-disable-next-line no-useless-return
    return;
  });
  return isAnyLenderMBFCA;
};

export const getUpdatedFieldKeysInOrder = ({
  fieldKeysInOrder = EMPTY_ARRAY,
  deskingpaymentDetails = EMPTY_ARRAY,
  lenders = EMPTY_ARRAY,
}) => {
  let updatedFieldKeysInOrder = fieldKeysInOrder;
  if (isCanadaDealer()) {
    updatedFieldKeysInOrder = _filter(fieldKeysInOrder, key => key !== MORE_FIELDS.OUT_OF_POCKET_CASH);
    const isAnyLenderMBFCA = checkLenderMBFCA({ deskingpaymentDetails, lenders });
    updatedFieldKeysInOrder = isAnyLenderMBFCA
      ? [...updatedFieldKeysInOrder, MORE_FIELDS.SUBVENTION_COST, MORE_FIELDS.OUT_OF_POCKET_CASH]
      : [...updatedFieldKeysInOrder, MORE_FIELDS.OUT_OF_POCKET_CASH];
  }
  return updatedFieldKeysInOrder;
};

export const checkSelectedLenderCode = (selectedLenderCodeName = EMPTY_STRING) => selectedLenderCodeName === 'MBFCA';

export const isRebateEditDisabled = (isDisabled, deal) => {
  if (!isInchcape()) {
    return isDisabled;
  }
  const primaryVehicle = DealReader.getPrimaryVehicle(deal);
  const isPrimaryVehicleNew = isNewVehicle(primaryVehicle);
  return !canEditReabtesOnDesking() || isDisabled || !isPrimaryVehicleNew;
};

export const getContent = () => (
  <div className={styles.popoverContent}>{DATA_SOURCE_VS_DISPLAY_LABELS.PRE_CONFIGURED}</div>
);

export const getUpdatedBodyContentWithInfoIcon = bodyContent =>
  _map(bodyContent, item => {
    const content = _get(item, 'content');
    return {
      ...item,
      content: _isEmpty(content) ? EMPTY_ARRAY : getContent(),
    };
  });
