export const VEHICLE = {
  deleted: false,
  vin: '5TFFY5F10DX133449',
  id: '6490771da69cb00cf97b03be',
  dealVehicleId: '7df4e9b1-4f46-4e86-bad3-fad65ee210ee',
  primaryVehicle: false,
  stockID: '77',
  year: 2022,
  make: 'TOYOTA',
  makeId: 'toyota',
  displayMake: 'Toyota',
  modelCode: '1864C',
  exteriorColor: 'Black Sand Pearl',
  interiorColor: 'Black Premium Fabric',
  exteriorColorCode: '0209',
  languages: {
    documentDefault: null,
    locale: {
      fr_FR: {
        interiorColor: 'Black Premium Fabric',
        exteriorColor: 'Black Sand Pearl',
      },
    },
  },
  customFields: {},
  status: 'ON_ORDER',
  sourceInfo: {
    source: 'OEM',
    sourceName: 'TOYOTA',
    sourceDealNumber: null,
    sourceSalesPerson: null,
    sourceSalesPersonName: null,
    sourceSalesManager: null,
    sourceSalesManagerName: null,
    sourceDealType: null,
    sourceAddress: null,
    languages: {
      documentDefault: null,
      locale: {
        fr_FR: {
          sourceName: 'TOYOTA',
        },
      },
    },
  },
  vehicleType: 'NEW',
  vehicleSubType: '',
  unladenWeightUnit: '',
  grossWeightUnit: '',
  mileageType: 'MILES',
  pricingDetails: {
    sellingPrice: 24000,
    finalCost: null,
    invoicePrice: 22578.01,
    retailPrice: null,
    profit: null,
    totalAdjustments: null,
    commPrice: null,
    msrp: 24000,
    costOfSale: null,
    vatRate: 5,
    vatAmount: 1142.86,
    holdBackAmount: 0,
    viHoldBackAmount: null,
    glBalance: 0,
    reconditioningCost: null,
    loanSellingPrice: 24000,
    leaseSellingPrice: 24000,
    flooringAmt: null,
    baseRetail: null,
    wholesalePrice: null,
    internetPrice: null,
    commissionPrice: null,
    employeePrice: null,
    supplierPrice: null,
    freightInPrice: 1025,
    takePrice: null,
    airTaxPrice: null,
    maxResidualizationMSRP: null,
    configuredOptionsMsrp: 249,
    configuredOptionsInvoice: 187,
    interiorInvoicePrice: null,
    interiorMSRP: null,
    exteriorInvoicePrice: null,
    exteriorMSRP: null,
    baseInvoice: 20948,
    totalCost: null,
    marketPrice: null,
    buyingPrice: null,
    realReconditioningCost: null,
    originalLoanSellingPrice: null,
    originalLeaseSellingPrice: null,
    gasGuzzlerTax: null,
    packageDiscounts: null,
  },
  licensePlateExpirationDate: 0,
  trimDetails: {
    oem: 'toyota',
    brand: 'toyota',
    actualBrand: 'toyota',
    mapped: true,
    trim: 'CF,FE',
    trimName: null,
    trimCode: 'CF,FE',
    trimColor: null,
    customBodyType: null,
    driveType: null,
    aspiration: null,
    engineDescription: null,
    engineSize: null,
    engineCode: null,
    engineType: null,
    engineNumber: null,
    brandGroup: null,
    floorPlan: null,
    horsePower: null,
    torque: null,
    engineCylinders: 0,
    fuelType: null,
    fuelDeliveryType: null,
    transmissionControlType: null,
    transmissionTypeDescription: null,
    bodyType: null,
    axleCount: 0,
    marketClassCategory: null,
    bodyClass: null,
    manufacturer: null,
    bodyDoorCount: null,
    maximumSpeedMeasure: {
      value: null,
      unitCode: 'MPH',
    },
    seatingCapacity: null,
    languages: {
      documentDefault: null,
      locale: {
        fr_FR: {
          trim: 'CF,FE',
        },
      },
    },
    bodyStyleCode: null,
  },
  temp: false,
  certified: false,
  dealerCertified: false,
  options: [
    {
      optionId: '6747bd90-70ae-4903-9382-eadeb04a9bb5',
      optionCode: 'CF',
      optionName: 'Carpet Mat Package[installed_msrp]',
      manufacturerName: 'TOYOTA',
      description: 'Carpet Floor Mats, Carpet Trunk Mat ',
      partType: null,
      quantity: 0,
      retailPrice: 249,
      invoicePrice: 187,
      status: null,
      fromOEM: true,
      priceImpacted: false,
      headerTitle: null,
      source: null,
      packageName: null,
      packageCode: null,
      dealerInstalledOption: false,
      languages: null,
      addToMsrp: false,
      addToRetailPrice: false,
      addToInternetPrice: false,
      addToInvoice: false,
      optionVatInfo: null,
      discount: null,
    },
    {
      optionId: '921e3808-444d-4771-b664-373cc1a3c6f5',
      optionCode: 'FE',
      optionName: '50 State Emissions',
      manufacturerName: 'TOYOTA',
      description: '50 State Emissions ',
      partType: null,
      quantity: 0,
      retailPrice: 0,
      invoicePrice: 0,
      status: null,
      fromOEM: true,
      priceImpacted: false,
      headerTitle: null,
      source: null,
      packageName: null,
      packageCode: null,
      dealerInstalledOption: false,
      languages: null,
      addToMsrp: false,
      addToRetailPrice: false,
      addToInternetPrice: false,
      addToInvoice: false,
      optionVatInfo: null,
      discount: null,
    },
  ],
  mileageForResidualCalculation: 0,
  vinLookupResolved: false,
  qualifiedForNewVehiclePrograms: false,
  auctionCar: false,
  demoVehicleIndicator: false,
  exteriorColorDetail: {
    hexCode: null,
    baseColor: null,
  },
  parts: [],
  accessories: [],
  invoiceDate: 0,
  reformeB: false,
  professionalUsage: false,
};

export const PRIMARY_VEHICLE = {
  deleted: false,
  vin: '5TFFY5F10DX133449',
  id: '6490771da69cb00cf97b03be',
  dealVehicleId: '7df4e9b1-4f46-4e86-bad3-fad65ee210ee',
  primaryVehicle: true,
  stockID: '77',
  year: 2022,
  make: 'TOYOTA',
  makeId: 'toyota',
  displayMake: 'Toyota',
  modelCode: '1864C',
  exteriorColor: 'Black Sand Pearl',
  interiorColor: 'Black Premium Fabric',
  exteriorColorCode: '0209',
  languages: {
    documentDefault: null,
    locale: {
      fr_FR: {
        interiorColor: 'Black Premium Fabric',
        exteriorColor: 'Black Sand Pearl',
      },
    },
  },
  customFields: {},
  status: 'ON_ORDER',
  sourceInfo: {
    source: 'OEM',
    sourceName: 'TOYOTA',
    sourceDealNumber: null,
    sourceSalesPerson: null,
    sourceSalesPersonName: null,
    sourceSalesManager: null,
    sourceSalesManagerName: null,
    sourceDealType: null,
    sourceAddress: null,
    languages: {
      documentDefault: null,
      locale: {
        fr_FR: {
          sourceName: 'TOYOTA',
        },
      },
    },
  },
  vehicleType: 'NEW',
  vehicleSubType: '',
  unladenWeightUnit: '',
  grossWeightUnit: '',
  mileageType: 'MILES',
  pricingDetails: {
    sellingPrice: 24000,
    finalCost: null,
    invoicePrice: 22578.01,
    retailPrice: null,
    profit: null,
    totalAdjustments: null,
    commPrice: null,
    msrp: 24000,
    costOfSale: null,
    vatRate: 5,
    vatAmount: 1142.86,
    holdBackAmount: 0,
    viHoldBackAmount: null,
    glBalance: 0,
    reconditioningCost: null,
    loanSellingPrice: 24000,
    leaseSellingPrice: 24000,
    flooringAmt: null,
    baseRetail: null,
    wholesalePrice: null,
    internetPrice: null,
    commissionPrice: null,
    employeePrice: null,
    supplierPrice: null,
    freightInPrice: 1025,
    takePrice: null,
    airTaxPrice: null,
    maxResidualizationMSRP: null,
    configuredOptionsMsrp: 249,
    configuredOptionsInvoice: 187,
    interiorInvoicePrice: null,
    interiorMSRP: null,
    exteriorInvoicePrice: null,
    exteriorMSRP: null,
    baseInvoice: 20948,
    totalCost: null,
    marketPrice: null,
    buyingPrice: null,
    realReconditioningCost: null,
    originalLoanSellingPrice: null,
    originalLeaseSellingPrice: null,
    gasGuzzlerTax: null,
    packageDiscounts: null,
  },
  licensePlateExpirationDate: 0,
  trimDetails: {
    oem: 'toyota',
    brand: 'toyota',
    actualBrand: 'toyota',
    mapped: true,
    trim: 'CF,FE',
    trimName: null,
    trimCode: 'CF,FE',
    trimColor: null,
    customBodyType: null,
    driveType: null,
    aspiration: null,
    engineDescription: null,
    engineSize: null,
    engineCode: null,
    engineType: null,
    engineNumber: null,
    brandGroup: null,
    floorPlan: null,
    horsePower: null,
    torque: null,
    engineCylinders: 0,
    fuelType: null,
    fuelDeliveryType: null,
    transmissionControlType: null,
    transmissionTypeDescription: null,
    bodyType: null,
    axleCount: 0,
    marketClassCategory: null,
    bodyClass: null,
    manufacturer: null,
    bodyDoorCount: null,
    maximumSpeedMeasure: {
      value: null,
      unitCode: 'MPH',
    },
    seatingCapacity: null,
    languages: {
      documentDefault: null,
      locale: {
        fr_FR: {
          trim: 'CF,FE',
        },
      },
    },
    bodyStyleCode: null,
  },
  temp: false,
  certified: false,
  dealerCertified: false,
  options: [
    {
      optionId: '6747bd90-70ae-4903-9382-eadeb04a9bb5',
      optionCode: 'CF',
      optionName: 'Carpet Mat Package[installed_msrp]',
      manufacturerName: 'TOYOTA',
      description: 'Carpet Floor Mats, Carpet Trunk Mat ',
      partType: null,
      quantity: 0,
      retailPrice: 249,
      invoicePrice: 187,
      status: null,
      fromOEM: true,
      priceImpacted: false,
      headerTitle: null,
      source: null,
      packageName: null,
      packageCode: null,
      dealerInstalledOption: false,
      languages: null,
      addToMsrp: false,
      addToRetailPrice: false,
      addToInternetPrice: false,
      addToInvoice: false,
      optionVatInfo: null,
      discount: null,
    },
    {
      optionId: '921e3808-444d-4771-b664-373cc1a3c6f5',
      optionCode: 'FE',
      optionName: '50 State Emissions',
      manufacturerName: 'TOYOTA',
      description: '50 State Emissions ',
      partType: null,
      quantity: 0,
      retailPrice: 0,
      invoicePrice: 0,
      status: null,
      fromOEM: true,
      priceImpacted: false,
      headerTitle: null,
      source: null,
      packageName: null,
      packageCode: null,
      dealerInstalledOption: false,
      languages: null,
      addToMsrp: false,
      addToRetailPrice: false,
      addToInternetPrice: false,
      addToInvoice: false,
      optionVatInfo: null,
      discount: null,
    },
  ],
  mileageForResidualCalculation: 0,
  vinLookupResolved: false,
  qualifiedForNewVehiclePrograms: false,
  auctionCar: false,
  demoVehicleIndicator: false,
  exteriorColorDetail: {
    hexCode: null,
    baseColor: null,
  },
  parts: [],
  accessories: [],
  invoiceDate: 0,
  reformeB: false,
  professionalUsage: false,
};

export const deskingpaymentDetails = [
  {
    lastModifiedBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
    createdBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
    deleted: false,
    createdTime: 1688637929184,
    modifiedTime: 1688637929184,
    id: '029c23ba-0b0c-4420-b46f-193a57ef2e7c',
    dealVehicleId: '1caf3219-2f9e-437b-bd2a-b0331c896926',
    termPaymentDetails: {
      '648980748b5fe56ef89d2735': [
        {
          lastModifiedBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
          createdBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
          deleted: false,
          createdTime: 1688637929184,
          modifiedTime: 1688637929184,
          id: '4caf8f10-b99c-4be7-ac34-5a416372fb53',
          downPayment: 0,
          downPaymentPercentage: null,
          downPaymentPct: null,
          downPaymentPctType: 'SELLING_PRICE',
          calcBasedOnDownPmtPct: true,
          outOfPocketCash: null,
          tradeEquityCashBack: null,
          securityDepositOverridden: false,
          securityDeposits: null,
          paymentInfo: null,
          securityDepositV2: null,
          securityDepositId: null,
          securityDepositType: null,
          securityDeposit: null,
          emiAmount: null,
          onePayPayment: null,
          baseEmiAmount: null,
          amountFinanced: null,
          loanToValueRatio: 0,
          selected: true,
          osfLender: false,
          lender: 'BMWCA',
          lenderId: '648980748b5fe56ef89d2735',
          lenderCode: null,
          lienFilingCode: null,
          apr: {
            buyRate: 0,
            effectiveRate: 0,
            buyRateOverridden: true,
            apr: 0,
            moneyFactor: null,
            financeReserve: 0,
            pctOfCapCost: 0,
            financeReserveOverridden: false,
            aprCode: null,
            markUp: 0,
            markUpOverridden: true,
            participation: 0,
            reserveMethod: 0,
            rateType: 0,
            lenderRateType: 0,
            manuallyUpdated: false,
            alternateRateAdjustments: null,
            rateOfInterest: null,
          },
          residual: {
            baseValue: null,
            manuallyUpdated: false,
            residualOverridden: false,
            basePercentage: 0,
            adjustedValue: null,
            adjustedPercentage: 0,
            totalValue: null,
            balloonPaymentValue: null,
            oneTimeBalloonPayment: null,
            totalPercentage: 0,
            residualName: null,
            demoMilesPenaltyMiles: 0,
            demoMilesRate: null,
            demoMilesResidualAdjustment: null,
            demoMilesResidualAdjustmentPct: null,
            residualDisplayType: 'PERCENTAGE',
            adjustedMsrp: null,
            crvMRM: null,
          },
          yearlyMiles: {
            baseValue: 0,
            additionalValue: 0,
            demoMileageNA: false,
            actualPenaltyPerMile: 0,
            penaltyPerMile: 0,
            penaltyPerMileOverridden: false,
            totalValue: 0,
            totalPenalty: null,
            removeDemoMileageFromAnnualMileage: false,
            totalMilesHidden: 0,
            yearlyMilesUpdated: null,
          },
          rebates: [],
          firstPaymentWaiverManuallyOveridden: false,
          securityDepositRateAdjustment: 0,
          totalRebateAmt: null,
          bankFee: null,
          inceptionFee: null,
          programId: null,
          capCostReductionfactors: null,
          workingCashTableData: null,
          driveOffReductionFactors: null,
          totalTaxAmt: null,
          generic: false,
          cashDeficiency: null,
          imbalanceAmount: 0,
          deferredPayment1: null,
          deferredPayment2: null,
          deferredPayment3: null,
          deferredPayment4: null,
          totalPayable: 0,
          daysToFirstPayment: 0,
          monthlyPaymentBeforeTax: null,
          taxes: null,
          vendorTaxes: null,
          dealFees: [],
          manuallyDeletedFees: null,
          firstPaymentWaived: false,
          firstPaymentWaivedAmount: null,
          firstPaymentWaivedMaxAmount: null,
          registrationnFee: null,
          stateFee: null,
          titleFee: null,
          wasteTireFee: null,
          bandOFee: null,
          lienFee: null,
          totalCapReduced: 0,
          paidReserveIsFlatFee: false,
          maxRateMarkup: null,
          msLenderCode: null,
          paidReserveIsCapCostParticipation: false,
          collectRegFee: true,
          downPaymentIndex: 0,
          singleScheduledPayment: null,
          taxWaivedPayments: null,
          programFlags: null,
          taxCode: null,
          taxZipCode: '10001',
          taxAddress: null,
          taxCity: null,
          taxCounty: null,
          taxExemptAmount: null,
          totalRebateAmount: 0,
          totalDealerCashAmount: 0,
          rolledOverCashDeficiency: null,
          totalMonthlyPayment: 0,
          financeCharge: null,
          fordFlexDecreasePct: null,
          fordFlexFirstPayment: null,
          fordFlexFirstTerm: null,
          fordFlexLastPayment: null,
          rebatesBreakup: null,
          proRataAdjustment: null,
          applyCanadaLuxuryTax: false,
          programExpirationDate: null,
          paymentBreakups: null,
          discount: null,
          hasStandardData: false,
          downPaymentWithoutCCRTax: null,
          totalAmountPayable: null,
          optionalFinalPayment: null,
          optionToPurchaseFee: null,
          totalDeposit: null,
          rebatesVerified: false,
          taxOutOfState: true,
        },
        {
          lastModifiedBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
          createdBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
          deleted: false,
          createdTime: 1688637929184,
          modifiedTime: 1688637929184,
          id: 'beea9a3f-07ba-4143-bf33-7f2c6e3ea753',
          downPayment: null,
          downPaymentPercentage: null,
          downPaymentPct: null,
          downPaymentPctType: 'SELLING_PRICE',
          calcBasedOnDownPmtPct: true,
          outOfPocketCash: null,
          tradeEquityCashBack: null,
          securityDepositOverridden: false,
          securityDeposits: null,
          paymentInfo: null,
          securityDepositV2: null,
          securityDepositId: null,
          securityDepositType: null,
          securityDeposit: null,
          emiAmount: null,
          onePayPayment: null,
          baseEmiAmount: null,
          amountFinanced: null,
          loanToValueRatio: 0,
          selected: false,
          osfLender: false,
          lender: 'BMWCA',
          lenderId: '648980748b5fe56ef89d2735',
          lenderCode: null,
          lienFilingCode: null,
          apr: {
            buyRate: 0,
            effectiveRate: 0,
            buyRateOverridden: true,
            apr: 0,
            moneyFactor: null,
            financeReserve: 0,
            pctOfCapCost: 0,
            financeReserveOverridden: false,
            aprCode: null,
            markUp: 0,
            markUpOverridden: true,
            participation: 0,
            reserveMethod: 0,
            rateType: 0,
            lenderRateType: 0,
            manuallyUpdated: false,
            alternateRateAdjustments: null,
            rateOfInterest: null,
          },
          residual: {
            baseValue: null,
            manuallyUpdated: false,
            residualOverridden: false,
            basePercentage: 0,
            adjustedValue: null,
            adjustedPercentage: 0,
            totalValue: null,
            balloonPaymentValue: null,
            oneTimeBalloonPayment: null,
            totalPercentage: 0,
            residualName: null,
            demoMilesPenaltyMiles: 0,
            demoMilesRate: null,
            demoMilesResidualAdjustment: null,
            demoMilesResidualAdjustmentPct: null,
            residualDisplayType: 'PERCENTAGE',
            adjustedMsrp: null,
            crvMRM: null,
          },
          yearlyMiles: {
            baseValue: 0,
            additionalValue: 0,
            demoMileageNA: false,
            actualPenaltyPerMile: 0,
            penaltyPerMile: 0,
            penaltyPerMileOverridden: false,
            totalValue: 0,
            totalPenalty: null,
            removeDemoMileageFromAnnualMileage: false,
            totalMilesHidden: 0,
            yearlyMilesUpdated: null,
          },
          rebates: [],
          firstPaymentWaiverManuallyOveridden: false,
          securityDepositRateAdjustment: 0,
          totalRebateAmt: null,
          bankFee: null,
          inceptionFee: null,
          programId: null,
          capCostReductionfactors: null,
          workingCashTableData: null,
          driveOffReductionFactors: null,
          totalTaxAmt: null,
          generic: false,
          cashDeficiency: null,
          imbalanceAmount: 0,
          deferredPayment1: null,
          deferredPayment2: null,
          deferredPayment3: null,
          deferredPayment4: null,
          totalPayable: null,
          daysToFirstPayment: 0,
          monthlyPaymentBeforeTax: null,
          taxes: null,
          vendorTaxes: null,
          dealFees: [],
          manuallyDeletedFees: null,
          firstPaymentWaived: false,
          firstPaymentWaivedAmount: null,
          firstPaymentWaivedMaxAmount: null,
          registrationnFee: null,
          stateFee: null,
          titleFee: null,
          wasteTireFee: null,
          bandOFee: null,
          lienFee: null,
          totalCapReduced: 0,
          paidReserveIsFlatFee: false,
          maxRateMarkup: null,
          msLenderCode: null,
          paidReserveIsCapCostParticipation: false,
          collectRegFee: true,
          downPaymentIndex: 0,
          singleScheduledPayment: null,
          taxWaivedPayments: null,
          programFlags: null,
          taxCode: null,
          taxZipCode: null,
          taxAddress: null,
          taxCity: null,
          taxCounty: null,
          taxExemptAmount: null,
          totalRebateAmount: null,
          totalDealerCashAmount: null,
          rolledOverCashDeficiency: null,
          totalMonthlyPayment: null,
          financeCharge: null,
          fordFlexDecreasePct: null,
          fordFlexFirstPayment: null,
          fordFlexFirstTerm: null,
          fordFlexLastPayment: null,
          rebatesBreakup: null,
          proRataAdjustment: null,
          applyCanadaLuxuryTax: false,
          programExpirationDate: null,
          paymentBreakups: null,
          discount: null,
          hasStandardData: false,
          downPaymentWithoutCCRTax: null,
          totalAmountPayable: null,
          optionalFinalPayment: null,
          optionToPurchaseFee: null,
          totalDeposit: null,
          rebatesVerified: false,
          taxOutOfState: false,
        },
        {
          lastModifiedBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
          createdBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
          deleted: false,
          createdTime: 1688637929184,
          modifiedTime: 1688637929184,
          id: 'd975c233-719b-4dfc-a9e1-ee841b2c6b5f',
          downPayment: null,
          downPaymentPercentage: null,
          downPaymentPct: null,
          downPaymentPctType: 'SELLING_PRICE',
          calcBasedOnDownPmtPct: true,
          outOfPocketCash: null,
          tradeEquityCashBack: null,
          securityDepositOverridden: false,
          securityDeposits: null,
          paymentInfo: null,
          securityDepositV2: null,
          securityDepositId: null,
          securityDepositType: null,
          securityDeposit: null,
          emiAmount: null,
          onePayPayment: null,
          baseEmiAmount: null,
          amountFinanced: null,
          loanToValueRatio: 0,
          selected: false,
          osfLender: false,
          lender: 'BMWCA',
          lenderId: '648980748b5fe56ef89d2735',
          lenderCode: null,
          lienFilingCode: null,
          apr: {
            buyRate: 0,
            effectiveRate: 0,
            buyRateOverridden: true,
            apr: 0,
            moneyFactor: null,
            financeReserve: 0,
            pctOfCapCost: 0,
            financeReserveOverridden: false,
            aprCode: null,
            markUp: 0,
            markUpOverridden: true,
            participation: 0,
            reserveMethod: 0,
            rateType: 0,
            lenderRateType: 0,
            manuallyUpdated: false,
            alternateRateAdjustments: null,
            rateOfInterest: null,
          },
          residual: {
            baseValue: null,
            manuallyUpdated: false,
            residualOverridden: false,
            basePercentage: 0,
            adjustedValue: null,
            adjustedPercentage: 0,
            totalValue: null,
            balloonPaymentValue: null,
            oneTimeBalloonPayment: null,
            totalPercentage: 0,
            residualName: null,
            demoMilesPenaltyMiles: 0,
            demoMilesRate: null,
            demoMilesResidualAdjustment: null,
            demoMilesResidualAdjustmentPct: null,
            residualDisplayType: 'PERCENTAGE',
            adjustedMsrp: null,
            crvMRM: null,
          },
          yearlyMiles: {
            baseValue: 0,
            additionalValue: 0,
            demoMileageNA: false,
            actualPenaltyPerMile: 0,
            penaltyPerMile: 0,
            penaltyPerMileOverridden: false,
            totalValue: 0,
            totalPenalty: null,
            removeDemoMileageFromAnnualMileage: false,
            totalMilesHidden: 0,
            yearlyMilesUpdated: null,
          },
          rebates: [],
          firstPaymentWaiverManuallyOveridden: false,
          securityDepositRateAdjustment: 0,
          totalRebateAmt: null,
          bankFee: null,
          inceptionFee: null,
          programId: null,
          capCostReductionfactors: null,
          workingCashTableData: null,
          driveOffReductionFactors: null,
          totalTaxAmt: null,
          generic: false,
          cashDeficiency: null,
          imbalanceAmount: 0,
          deferredPayment1: null,
          deferredPayment2: null,
          deferredPayment3: null,
          deferredPayment4: null,
          totalPayable: null,
          daysToFirstPayment: 0,
          monthlyPaymentBeforeTax: null,
          taxes: null,
          vendorTaxes: null,
          dealFees: [],
          manuallyDeletedFees: null,
          firstPaymentWaived: false,
          firstPaymentWaivedAmount: null,
          firstPaymentWaivedMaxAmount: null,
          registrationnFee: null,
          stateFee: null,
          titleFee: null,
          wasteTireFee: null,
          bandOFee: null,
          lienFee: null,
          totalCapReduced: 0,
          paidReserveIsFlatFee: false,
          maxRateMarkup: null,
          msLenderCode: null,
          paidReserveIsCapCostParticipation: false,
          collectRegFee: true,
          downPaymentIndex: 0,
          singleScheduledPayment: null,
          taxWaivedPayments: null,
          programFlags: null,
          taxCode: null,
          taxZipCode: null,
          taxAddress: null,
          taxCity: null,
          taxCounty: null,
          taxExemptAmount: null,
          totalRebateAmount: null,
          totalDealerCashAmount: null,
          rolledOverCashDeficiency: null,
          totalMonthlyPayment: null,
          financeCharge: null,
          fordFlexDecreasePct: null,
          fordFlexFirstPayment: null,
          fordFlexFirstTerm: null,
          fordFlexLastPayment: null,
          rebatesBreakup: null,
          proRataAdjustment: null,
          applyCanadaLuxuryTax: false,
          programExpirationDate: null,
          paymentBreakups: null,
          discount: null,
          hasStandardData: false,
          downPaymentWithoutCCRTax: null,
          totalAmountPayable: null,
          optionalFinalPayment: null,
          optionToPurchaseFee: null,
          totalDeposit: null,
          rebatesVerified: false,
          taxOutOfState: false,
        },
      ],
    },
    programManuallyOverridden: false,
    selectedLender: 'BMWCA',
    selectedTier: null,
    selectedLenderId: '648980748b5fe56ef89d2735',
    paymentOption: {
      paymentType: 'LOAN',
      paymentFrequency: 'MONTHLY',
      value: 48,
      apr: null,
      yearlyMiles: null,
      lenderId: null,
      securityDeposit: null,
      downPayment: null,
      residualValue: null,
      residualDisplayType: null,
      fnIs: null,
      rebates: null,
      accessories: null,
      fees: null,
      daysToFirstPayment: null,
      useDefaultFees: null,
      useDefaultAccessories: null,
      useDefaultFNIs: null,
      creditScore: null,
      rank: 1,
    },
    selectedLenderCode: null,
    accessories: [
      {
        lastModifiedBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
        createdBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
        deleted: false,
        createdTime: 1688637929184,
        modifiedTime: 1688637929184,
        id: 'LJ8HG18QDAAW0',
        name: 'Wheels',
        languages: null,
        partNumber: null,
        partId: null,
        cost: 400,
        price: 1000,
        installationCost: null,
        profitType: 'FRONT',
        taxable: true,
        upfront: true,
        taxUpFront: true,
        payType: 'CUSTOMER_PAY',
        code: 'W1',
        opCode: null,
        disclosureType: 'ACCESSORY',
        manuallyUpdated: true,
        uniqueId: 'LJ8HG18QDAAW0',
        residualValue: null,
        residualize: false,
        adjustResidualNotMsrp: false,
        manuallyResidualized: false,
        vehicleEquipmentId: null,
        hardAddNumber: null,
        free: false,
        vatInfo: {
          taxCode: 'A',
          vatPercent: 20,
          taxId: '64958addb7b7b839759e1c23',
        },
        preorder: false,
        source: null,
        discount: null,
        retailPrice: null,
      },
      {
        lastModifiedBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
        createdBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
        deleted: false,
        createdTime: 1688637929184,
        modifiedTime: 1688637929184,
        id: 'LJ8HK3L28OKQW',
        name: 'Spray',
        languages: null,
        partNumber: null,
        partId: null,
        cost: 350,
        price: 2000,
        installationCost: null,
        profitType: 'FRONT',
        taxable: true,
        upfront: true,
        taxUpFront: true,
        payType: 'CUSTOMER_PAY',
        code: 'S1',
        opCode: null,
        disclosureType: 'ACCESSORY',
        manuallyUpdated: true,
        uniqueId: 'LJ8HK3L28OKQW',
        residualValue: null,
        residualize: false,
        adjustResidualNotMsrp: false,
        manuallyResidualized: false,
        vehicleEquipmentId: null,
        hardAddNumber: null,
        free: false,
        vatInfo: {
          taxCode: 'N',
          vatPercent: 5,
          taxId: '64958aa1b7b7b839759e1c21',
        },
        preorder: false,
        source: null,
        discount: null,
        retailPrice: null,
      },
      {
        lastModifiedBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
        createdBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
        deleted: false,
        createdTime: 1688637929184,
        modifiedTime: 1688637929184,
        id: 'LJ8JI885WQLDM',
        name: 'Tyre Inflator',
        languages: null,
        partNumber: null,
        partId: null,
        cost: 200,
        price: 500,
        installationCost: null,
        profitType: 'FRONT',
        taxable: true,
        upfront: false,
        taxUpFront: false,
        payType: 'CUSTOMER_PAY',
        code: 'T1',
        opCode: null,
        disclosureType: 'ESSENTIAL_KIT',
        manuallyUpdated: true,
        uniqueId: 'LJ8JI885WQLDM',
        residualValue: null,
        residualize: false,
        adjustResidualNotMsrp: false,
        manuallyResidualized: false,
        vehicleEquipmentId: null,
        hardAddNumber: null,
        free: false,
        vatInfo: {
          taxCode: 'S',
          vatPercent: 10,
          taxId: '64958abcb7b7b839759e1c22',
        },
        preorder: false,
        source: null,
        discount: null,
        retailPrice: null,
      },
    ],
    fnIs: [],
    fnIsCount: 0,
    totalFnisPriceAmt: null,
    uniqueId: null,
    columnSource: null,
    previousLenderId: null,
    selectedPackageId: null,
    leaseProgramAvailabilityStatus: null,
    programDesc: null,
    sppPaymentOption: null,
    optionContractValidityDate: null,
    financingProduct: null,
    contractNumber: null,
    financeContractNumber: null,
    lenderChanged: false,
    exceptionFromCalc: null,
    ruleWarningMap: null,
    noOfInstallments: null,
  },
  {
    lastModifiedBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
    createdBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
    deleted: false,
    createdTime: 1688637929184,
    modifiedTime: 1688637929184,
    id: '6839fa24-ddcb-4ce5-b460-456b0e009226',
    dealVehicleId: '1caf3219-2f9e-437b-bd2a-b0331c896926',
    termPaymentDetails: {
      '648980748b5fe56ef89d2735': [
        {
          lastModifiedBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
          createdBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
          deleted: false,
          createdTime: 1688637929184,
          modifiedTime: 1688637929184,
          id: 'd012ea03-aed8-449f-b33b-73b6652fb1c5',
          downPayment: 0,
          downPaymentPercentage: null,
          downPaymentPct: null,
          downPaymentPctType: 'SELLING_PRICE',
          calcBasedOnDownPmtPct: true,
          outOfPocketCash: null,
          tradeEquityCashBack: null,
          securityDepositOverridden: false,
          securityDeposits: null,
          paymentInfo: null,
          securityDepositV2: null,
          securityDepositId: null,
          securityDepositType: null,
          securityDeposit: null,
          emiAmount: null,
          onePayPayment: null,
          baseEmiAmount: null,
          amountFinanced: null,
          loanToValueRatio: 0,
          selected: false,
          osfLender: false,
          lender: 'BMWCA',
          lenderId: '648980748b5fe56ef89d2735',
          lenderCode: null,
          lienFilingCode: null,
          apr: {
            buyRate: 0,
            effectiveRate: 0,
            buyRateOverridden: true,
            apr: 0,
            moneyFactor: null,
            financeReserve: 0,
            pctOfCapCost: 0,
            financeReserveOverridden: false,
            aprCode: null,
            markUp: 0,
            markUpOverridden: true,
            participation: 0,
            reserveMethod: 0,
            rateType: 0,
            lenderRateType: 0,
            manuallyUpdated: false,
            alternateRateAdjustments: null,
            rateOfInterest: null,
          },
          residual: {
            baseValue: null,
            manuallyUpdated: false,
            residualOverridden: false,
            basePercentage: 0,
            adjustedValue: null,
            adjustedPercentage: 0,
            totalValue: null,
            balloonPaymentValue: null,
            oneTimeBalloonPayment: null,
            totalPercentage: 0,
            residualName: null,
            demoMilesPenaltyMiles: 0,
            demoMilesRate: null,
            demoMilesResidualAdjustment: null,
            demoMilesResidualAdjustmentPct: null,
            residualDisplayType: 'PERCENTAGE',
            adjustedMsrp: null,
            crvMRM: null,
          },
          yearlyMiles: {
            baseValue: 0,
            additionalValue: 0,
            demoMileageNA: false,
            actualPenaltyPerMile: 0,
            penaltyPerMile: 0,
            penaltyPerMileOverridden: false,
            totalValue: 0,
            totalPenalty: null,
            removeDemoMileageFromAnnualMileage: false,
            totalMilesHidden: 0,
            yearlyMilesUpdated: null,
          },
          rebates: null,
          firstPaymentWaiverManuallyOveridden: false,
          securityDepositRateAdjustment: 0,
          totalRebateAmt: null,
          bankFee: null,
          inceptionFee: null,
          programId: null,
          capCostReductionfactors: null,
          workingCashTableData: null,
          driveOffReductionFactors: null,
          totalTaxAmt: null,
          generic: false,
          cashDeficiency: null,
          imbalanceAmount: 0,
          deferredPayment1: null,
          deferredPayment2: null,
          deferredPayment3: null,
          deferredPayment4: null,
          totalPayable: null,
          daysToFirstPayment: 0,
          monthlyPaymentBeforeTax: null,
          taxes: null,
          vendorTaxes: null,
          dealFees: [],
          manuallyDeletedFees: null,
          firstPaymentWaived: false,
          firstPaymentWaivedAmount: null,
          firstPaymentWaivedMaxAmount: null,
          registrationnFee: null,
          stateFee: null,
          titleFee: null,
          wasteTireFee: null,
          bandOFee: null,
          lienFee: null,
          totalCapReduced: 0,
          paidReserveIsFlatFee: false,
          maxRateMarkup: null,
          msLenderCode: null,
          paidReserveIsCapCostParticipation: false,
          collectRegFee: true,
          downPaymentIndex: 0,
          singleScheduledPayment: null,
          taxWaivedPayments: null,
          programFlags: null,
          taxCode: null,
          taxZipCode: null,
          taxAddress: null,
          taxCity: null,
          taxCounty: null,
          taxExemptAmount: null,
          totalRebateAmount: null,
          totalDealerCashAmount: null,
          rolledOverCashDeficiency: null,
          totalMonthlyPayment: null,
          financeCharge: null,
          fordFlexDecreasePct: null,
          fordFlexFirstPayment: null,
          fordFlexFirstTerm: null,
          fordFlexLastPayment: null,
          rebatesBreakup: null,
          proRataAdjustment: null,
          applyCanadaLuxuryTax: false,
          programExpirationDate: null,
          paymentBreakups: null,
          discount: null,
          hasStandardData: false,
          downPaymentWithoutCCRTax: null,
          totalAmountPayable: null,
          optionalFinalPayment: null,
          optionToPurchaseFee: null,
          totalDeposit: null,
          rebatesVerified: false,
          taxOutOfState: false,
        },
        {
          lastModifiedBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
          createdBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
          deleted: false,
          createdTime: 1688637929184,
          modifiedTime: 1688637929184,
          id: '0f8e2089-f816-4257-82cb-fefd3ad6a898',
          downPayment: null,
          downPaymentPercentage: null,
          downPaymentPct: null,
          downPaymentPctType: 'SELLING_PRICE',
          calcBasedOnDownPmtPct: true,
          outOfPocketCash: null,
          tradeEquityCashBack: null,
          securityDepositOverridden: false,
          securityDeposits: null,
          paymentInfo: null,
          securityDepositV2: null,
          securityDepositId: null,
          securityDepositType: null,
          securityDeposit: null,
          emiAmount: null,
          onePayPayment: null,
          baseEmiAmount: null,
          amountFinanced: null,
          loanToValueRatio: 0,
          selected: false,
          osfLender: false,
          lender: 'BMWCA',
          lenderId: '648980748b5fe56ef89d2735',
          lenderCode: null,
          lienFilingCode: null,
          apr: {
            buyRate: 0,
            effectiveRate: 0,
            buyRateOverridden: true,
            apr: 0,
            moneyFactor: null,
            financeReserve: 0,
            pctOfCapCost: 0,
            financeReserveOverridden: false,
            aprCode: null,
            markUp: 0,
            markUpOverridden: true,
            participation: 0,
            reserveMethod: 0,
            rateType: 0,
            lenderRateType: 0,
            manuallyUpdated: false,
            alternateRateAdjustments: null,
            rateOfInterest: null,
          },
          residual: {
            baseValue: null,
            manuallyUpdated: false,
            residualOverridden: false,
            basePercentage: 0,
            adjustedValue: null,
            adjustedPercentage: 0,
            totalValue: null,
            balloonPaymentValue: null,
            oneTimeBalloonPayment: null,
            totalPercentage: 0,
            residualName: null,
            demoMilesPenaltyMiles: 0,
            demoMilesRate: null,
            demoMilesResidualAdjustment: null,
            demoMilesResidualAdjustmentPct: null,
            residualDisplayType: 'PERCENTAGE',
            adjustedMsrp: null,
            crvMRM: null,
          },
          yearlyMiles: {
            baseValue: 0,
            additionalValue: 0,
            demoMileageNA: false,
            actualPenaltyPerMile: 0,
            penaltyPerMile: 0,
            penaltyPerMileOverridden: false,
            totalValue: 0,
            totalPenalty: null,
            removeDemoMileageFromAnnualMileage: false,
            totalMilesHidden: 0,
            yearlyMilesUpdated: null,
          },
          rebates: null,
          firstPaymentWaiverManuallyOveridden: false,
          securityDepositRateAdjustment: 0,
          totalRebateAmt: null,
          bankFee: null,
          inceptionFee: null,
          programId: null,
          capCostReductionfactors: null,
          workingCashTableData: null,
          driveOffReductionFactors: null,
          totalTaxAmt: null,
          generic: false,
          cashDeficiency: null,
          imbalanceAmount: 0,
          deferredPayment1: null,
          deferredPayment2: null,
          deferredPayment3: null,
          deferredPayment4: null,
          totalPayable: null,
          daysToFirstPayment: 0,
          monthlyPaymentBeforeTax: null,
          taxes: null,
          vendorTaxes: null,
          dealFees: [],
          manuallyDeletedFees: null,
          firstPaymentWaived: false,
          firstPaymentWaivedAmount: null,
          firstPaymentWaivedMaxAmount: null,
          registrationnFee: null,
          stateFee: null,
          titleFee: null,
          wasteTireFee: null,
          bandOFee: null,
          lienFee: null,
          totalCapReduced: 0,
          paidReserveIsFlatFee: false,
          maxRateMarkup: null,
          msLenderCode: null,
          paidReserveIsCapCostParticipation: false,
          collectRegFee: true,
          downPaymentIndex: 0,
          singleScheduledPayment: null,
          taxWaivedPayments: null,
          programFlags: null,
          taxCode: null,
          taxZipCode: null,
          taxAddress: null,
          taxCity: null,
          taxCounty: null,
          taxExemptAmount: null,
          totalRebateAmount: null,
          totalDealerCashAmount: null,
          rolledOverCashDeficiency: null,
          totalMonthlyPayment: null,
          financeCharge: null,
          fordFlexDecreasePct: null,
          fordFlexFirstPayment: null,
          fordFlexFirstTerm: null,
          fordFlexLastPayment: null,
          rebatesBreakup: null,
          proRataAdjustment: null,
          applyCanadaLuxuryTax: false,
          programExpirationDate: null,
          paymentBreakups: null,
          discount: null,
          hasStandardData: false,
          downPaymentWithoutCCRTax: null,
          totalAmountPayable: null,
          optionalFinalPayment: null,
          optionToPurchaseFee: null,
          totalDeposit: null,
          rebatesVerified: false,
          taxOutOfState: false,
        },
        {
          lastModifiedBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
          createdBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
          deleted: false,
          createdTime: 1688637929184,
          modifiedTime: 1688637929184,
          id: 'a41752f6-7fc7-4463-8d7b-e93d81982e9d',
          downPayment: null,
          downPaymentPercentage: null,
          downPaymentPct: null,
          downPaymentPctType: 'SELLING_PRICE',
          calcBasedOnDownPmtPct: true,
          outOfPocketCash: null,
          tradeEquityCashBack: null,
          securityDepositOverridden: false,
          securityDeposits: null,
          paymentInfo: null,
          securityDepositV2: null,
          securityDepositId: null,
          securityDepositType: null,
          securityDeposit: null,
          emiAmount: null,
          onePayPayment: null,
          baseEmiAmount: null,
          amountFinanced: null,
          loanToValueRatio: 0,
          selected: false,
          osfLender: false,
          lender: 'BMWCA',
          lenderId: '648980748b5fe56ef89d2735',
          lenderCode: null,
          lienFilingCode: null,
          apr: {
            buyRate: 0,
            effectiveRate: 0,
            buyRateOverridden: true,
            apr: 0,
            moneyFactor: null,
            financeReserve: 0,
            pctOfCapCost: 0,
            financeReserveOverridden: false,
            aprCode: null,
            markUp: 0,
            markUpOverridden: true,
            participation: 0,
            reserveMethod: 0,
            rateType: 0,
            lenderRateType: 0,
            manuallyUpdated: false,
            alternateRateAdjustments: null,
            rateOfInterest: null,
          },
          residual: {
            baseValue: null,
            manuallyUpdated: false,
            residualOverridden: false,
            basePercentage: 0,
            adjustedValue: null,
            adjustedPercentage: 0,
            totalValue: null,
            balloonPaymentValue: null,
            oneTimeBalloonPayment: null,
            totalPercentage: 0,
            residualName: null,
            demoMilesPenaltyMiles: 0,
            demoMilesRate: null,
            demoMilesResidualAdjustment: null,
            demoMilesResidualAdjustmentPct: null,
            residualDisplayType: 'PERCENTAGE',
            adjustedMsrp: null,
            crvMRM: null,
          },
          yearlyMiles: {
            baseValue: 0,
            additionalValue: 0,
            demoMileageNA: false,
            actualPenaltyPerMile: 0,
            penaltyPerMile: 0,
            penaltyPerMileOverridden: false,
            totalValue: 0,
            totalPenalty: null,
            removeDemoMileageFromAnnualMileage: false,
            totalMilesHidden: 0,
            yearlyMilesUpdated: null,
          },
          rebates: null,
          firstPaymentWaiverManuallyOveridden: false,
          securityDepositRateAdjustment: 0,
          totalRebateAmt: null,
          bankFee: null,
          inceptionFee: null,
          programId: null,
          capCostReductionfactors: null,
          workingCashTableData: null,
          driveOffReductionFactors: null,
          totalTaxAmt: null,
          generic: false,
          cashDeficiency: null,
          imbalanceAmount: 0,
          deferredPayment1: null,
          deferredPayment2: null,
          deferredPayment3: null,
          deferredPayment4: null,
          totalPayable: null,
          daysToFirstPayment: 0,
          monthlyPaymentBeforeTax: null,
          taxes: null,
          vendorTaxes: null,
          dealFees: [],
          manuallyDeletedFees: null,
          firstPaymentWaived: false,
          firstPaymentWaivedAmount: null,
          firstPaymentWaivedMaxAmount: null,
          registrationnFee: null,
          stateFee: null,
          titleFee: null,
          wasteTireFee: null,
          bandOFee: null,
          lienFee: null,
          totalCapReduced: 0,
          paidReserveIsFlatFee: false,
          maxRateMarkup: null,
          msLenderCode: null,
          paidReserveIsCapCostParticipation: false,
          collectRegFee: true,
          downPaymentIndex: 0,
          singleScheduledPayment: null,
          taxWaivedPayments: null,
          programFlags: null,
          taxCode: null,
          taxZipCode: null,
          taxAddress: null,
          taxCity: null,
          taxCounty: null,
          taxExemptAmount: null,
          totalRebateAmount: null,
          totalDealerCashAmount: null,
          rolledOverCashDeficiency: null,
          totalMonthlyPayment: null,
          financeCharge: null,
          fordFlexDecreasePct: null,
          fordFlexFirstPayment: null,
          fordFlexFirstTerm: null,
          fordFlexLastPayment: null,
          rebatesBreakup: null,
          proRataAdjustment: null,
          applyCanadaLuxuryTax: false,
          programExpirationDate: null,
          paymentBreakups: null,
          discount: null,
          hasStandardData: false,
          downPaymentWithoutCCRTax: null,
          totalAmountPayable: null,
          optionalFinalPayment: null,
          optionToPurchaseFee: null,
          totalDeposit: null,
          rebatesVerified: false,
          taxOutOfState: false,
        },
      ],
    },
    programManuallyOverridden: false,
    selectedLender: 'BMWCA',
    selectedTier: null,
    selectedLenderId: '648980748b5fe56ef89d2735',
    paymentOption: {
      paymentType: 'LEASE',
      paymentFrequency: 'MONTHLY',
      value: 60,
      apr: null,
      yearlyMiles: null,
      lenderId: null,
      securityDeposit: null,
      downPayment: null,
      residualValue: null,
      residualDisplayType: null,
      fnIs: null,
      rebates: null,
      accessories: null,
      fees: null,
      daysToFirstPayment: null,
      useDefaultFees: null,
      useDefaultAccessories: null,
      useDefaultFNIs: null,
      creditScore: null,
      rank: 1,
    },
    selectedLenderCode: null,
    accessories: [
      {
        lastModifiedBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
        createdBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
        deleted: false,
        createdTime: 1688637929184,
        modifiedTime: 1688637929184,
        id: 'LJ8HG18QDAAW0',
        name: 'Wheels',
        languages: null,
        partNumber: null,
        partId: null,
        cost: 400,
        price: 1000,
        installationCost: null,
        profitType: 'FRONT',
        taxable: true,
        upfront: true,
        taxUpFront: true,
        payType: 'CUSTOMER_PAY',
        code: 'W1',
        opCode: null,
        disclosureType: 'ACCESSORY',
        manuallyUpdated: true,
        uniqueId: 'LJ8HG18QDAAW0',
        residualValue: null,
        residualize: false,
        adjustResidualNotMsrp: false,
        manuallyResidualized: false,
        vehicleEquipmentId: null,
        hardAddNumber: null,
        free: false,
        vatInfo: {
          taxCode: 'A',
          vatPercent: 20,
          taxId: '64958addb7b7b839759e1c23',
        },
        preorder: false,
        source: null,
        discount: null,
        retailPrice: null,
      },
      {
        lastModifiedBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
        createdBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
        deleted: false,
        createdTime: 1688637929184,
        modifiedTime: 1688637929184,
        id: 'LJ8HK3L28OKQW',
        name: 'Spray',
        languages: null,
        partNumber: null,
        partId: null,
        cost: 350,
        price: 2000,
        installationCost: null,
        profitType: 'FRONT',
        taxable: true,
        upfront: true,
        taxUpFront: true,
        payType: 'CUSTOMER_PAY',
        code: 'S1',
        opCode: null,
        disclosureType: 'ACCESSORY',
        manuallyUpdated: true,
        uniqueId: 'LJ8HK3L28OKQW',
        residualValue: null,
        residualize: false,
        adjustResidualNotMsrp: false,
        manuallyResidualized: false,
        vehicleEquipmentId: null,
        hardAddNumber: null,
        free: false,
        vatInfo: {
          taxCode: 'N',
          vatPercent: 5,
          taxId: '64958aa1b7b7b839759e1c21',
        },
        preorder: false,
        source: null,
        discount: null,
        retailPrice: null,
      },
      {
        lastModifiedBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
        createdBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
        deleted: false,
        createdTime: 1688637929184,
        modifiedTime: 1688637929184,
        id: 'LJ8JI885WQLDM',
        name: 'Tyre Inflator',
        languages: null,
        partNumber: null,
        partId: null,
        cost: 200,
        price: 500,
        installationCost: null,
        profitType: 'FRONT',
        taxable: true,
        upfront: false,
        taxUpFront: false,
        payType: 'CUSTOMER_PAY',
        code: 'T1',
        opCode: null,
        disclosureType: 'ESSENTIAL_KIT',
        manuallyUpdated: true,
        uniqueId: 'LJ8JI885WQLDM',
        residualValue: null,
        residualize: false,
        adjustResidualNotMsrp: false,
        manuallyResidualized: false,
        vehicleEquipmentId: null,
        hardAddNumber: null,
        free: false,
        vatInfo: {
          taxCode: 'S',
          vatPercent: 10,
          taxId: '64958abcb7b7b839759e1c22',
        },
        preorder: false,
        source: null,
        discount: null,
        retailPrice: null,
      },
    ],
    fnIs: [],
    fnIsCount: 0,
    totalFnisPriceAmt: null,
    uniqueId: null,
    columnSource: null,
    previousLenderId: null,
    selectedPackageId: null,
    leaseProgramAvailabilityStatus: null,
    programDesc: null,
    sppPaymentOption: null,
    optionContractValidityDate: null,
    financingProduct: null,
    contractNumber: null,
    financeContractNumber: null,
    lenderChanged: false,
    exceptionFromCalc: null,
    ruleWarningMap: null,
    noOfInstallments: null,
  },
];

export const deskingpaymentDetailsWithDownPayments = [
  {
    lastModifiedBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
    createdBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
    deleted: false,
    createdTime: 1688637929184,
    modifiedTime: 1688637929184,
    id: '029c23ba-0b0c-4420-b46f-193a57ef2e7c',
    dealVehicleId: '1caf3219-2f9e-437b-bd2a-b0331c896926',
    termPaymentDetails: {
      '648980748b5fe56ef89d2735': [
        {
          lastModifiedBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
          createdBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
          deleted: false,
          createdTime: 1688637929184,
          modifiedTime: 1688637929184,
          id: '4caf8f10-b99c-4be7-ac34-5a416372fb53',
          downPayment: 200,
          downPaymentPercentage: null,
          downPaymentPct: null,
          downPaymentPctType: 'SELLING_PRICE',
          calcBasedOnDownPmtPct: true,
          outOfPocketCash: null,
          tradeEquityCashBack: null,
          securityDepositOverridden: false,
          securityDeposits: null,
          paymentInfo: null,
          securityDepositV2: null,
          securityDepositId: null,
          securityDepositType: null,
          securityDeposit: null,
          emiAmount: null,
          onePayPayment: null,
          baseEmiAmount: null,
          amountFinanced: null,
          loanToValueRatio: 0,
          selected: true,
          osfLender: false,
          lender: 'BMWCA',
          lenderId: '648980748b5fe56ef89d2735',
          lenderCode: null,
          lienFilingCode: null,
          apr: {
            buyRate: 0,
            effectiveRate: 0,
            buyRateOverridden: true,
            apr: 0,
            moneyFactor: null,
            financeReserve: 0,
            pctOfCapCost: 0,
            financeReserveOverridden: false,
            aprCode: null,
            markUp: 0,
            markUpOverridden: true,
            participation: 0,
            reserveMethod: 0,
            rateType: 0,
            lenderRateType: 0,
            manuallyUpdated: false,
            alternateRateAdjustments: null,
            rateOfInterest: null,
          },
          residual: {
            baseValue: null,
            manuallyUpdated: false,
            residualOverridden: false,
            basePercentage: 0,
            adjustedValue: null,
            adjustedPercentage: 0,
            totalValue: null,
            balloonPaymentValue: null,
            oneTimeBalloonPayment: null,
            totalPercentage: 0,
            residualName: null,
            demoMilesPenaltyMiles: 0,
            demoMilesRate: null,
            demoMilesResidualAdjustment: null,
            demoMilesResidualAdjustmentPct: null,
            residualDisplayType: 'PERCENTAGE',
            adjustedMsrp: null,
            crvMRM: null,
          },
          yearlyMiles: {
            baseValue: 0,
            additionalValue: 0,
            demoMileageNA: false,
            actualPenaltyPerMile: 0,
            penaltyPerMile: 0,
            penaltyPerMileOverridden: false,
            totalValue: 0,
            totalPenalty: null,
            removeDemoMileageFromAnnualMileage: false,
            totalMilesHidden: 0,
            yearlyMilesUpdated: null,
          },
          rebates: [],
          firstPaymentWaiverManuallyOveridden: false,
          securityDepositRateAdjustment: 0,
          totalRebateAmt: null,
          bankFee: null,
          inceptionFee: null,
          programId: null,
          capCostReductionfactors: null,
          workingCashTableData: null,
          driveOffReductionFactors: null,
          totalTaxAmt: null,
          generic: false,
          cashDeficiency: null,
          imbalanceAmount: 0,
          deferredPayment1: null,
          deferredPayment2: null,
          deferredPayment3: null,
          deferredPayment4: null,
          totalPayable: 0,
          daysToFirstPayment: 0,
          monthlyPaymentBeforeTax: null,
          taxes: null,
          vendorTaxes: null,
          dealFees: [],
          manuallyDeletedFees: null,
          firstPaymentWaived: false,
          firstPaymentWaivedAmount: null,
          firstPaymentWaivedMaxAmount: null,
          registrationnFee: null,
          stateFee: null,
          titleFee: null,
          wasteTireFee: null,
          bandOFee: null,
          lienFee: null,
          totalCapReduced: 0,
          paidReserveIsFlatFee: false,
          maxRateMarkup: null,
          msLenderCode: null,
          paidReserveIsCapCostParticipation: false,
          collectRegFee: true,
          downPaymentIndex: 0,
          singleScheduledPayment: null,
          taxWaivedPayments: null,
          programFlags: null,
          taxCode: null,
          taxZipCode: '10001',
          taxAddress: null,
          taxCity: null,
          taxCounty: null,
          taxExemptAmount: null,
          totalRebateAmount: 0,
          totalDealerCashAmount: 0,
          rolledOverCashDeficiency: null,
          totalMonthlyPayment: 0,
          financeCharge: null,
          fordFlexDecreasePct: null,
          fordFlexFirstPayment: null,
          fordFlexFirstTerm: null,
          fordFlexLastPayment: null,
          rebatesBreakup: null,
          proRataAdjustment: null,
          applyCanadaLuxuryTax: false,
          programExpirationDate: null,
          paymentBreakups: null,
          discount: null,
          hasStandardData: false,
          downPaymentWithoutCCRTax: null,
          totalAmountPayable: null,
          optionalFinalPayment: null,
          optionToPurchaseFee: null,
          totalDeposit: null,
          rebatesVerified: false,
          taxOutOfState: true,
        },
        {
          lastModifiedBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
          createdBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
          deleted: false,
          createdTime: 1688637929184,
          modifiedTime: 1688637929184,
          id: 'beea9a3f-07ba-4143-bf33-7f2c6e3ea753',
          downPayment: 400,
          downPaymentPercentage: null,
          downPaymentPct: null,
          downPaymentPctType: 'SELLING_PRICE',
          calcBasedOnDownPmtPct: true,
          outOfPocketCash: null,
          tradeEquityCashBack: null,
          securityDepositOverridden: false,
          securityDeposits: null,
          paymentInfo: null,
          securityDepositV2: null,
          securityDepositId: null,
          securityDepositType: null,
          securityDeposit: null,
          emiAmount: null,
          onePayPayment: null,
          baseEmiAmount: null,
          amountFinanced: null,
          loanToValueRatio: 0,
          selected: false,
          osfLender: false,
          lender: 'BMWCA',
          lenderId: '648980748b5fe56ef89d2735',
          lenderCode: null,
          lienFilingCode: null,
          apr: {
            buyRate: 0,
            effectiveRate: 0,
            buyRateOverridden: true,
            apr: 0,
            moneyFactor: null,
            financeReserve: 0,
            pctOfCapCost: 0,
            financeReserveOverridden: false,
            aprCode: null,
            markUp: 0,
            markUpOverridden: true,
            participation: 0,
            reserveMethod: 0,
            rateType: 0,
            lenderRateType: 0,
            manuallyUpdated: false,
            alternateRateAdjustments: null,
            rateOfInterest: null,
          },
          residual: {
            baseValue: null,
            manuallyUpdated: false,
            residualOverridden: false,
            basePercentage: 0,
            adjustedValue: null,
            adjustedPercentage: 0,
            totalValue: null,
            balloonPaymentValue: null,
            oneTimeBalloonPayment: null,
            totalPercentage: 0,
            residualName: null,
            demoMilesPenaltyMiles: 0,
            demoMilesRate: null,
            demoMilesResidualAdjustment: null,
            demoMilesResidualAdjustmentPct: null,
            residualDisplayType: 'PERCENTAGE',
            adjustedMsrp: null,
            crvMRM: null,
          },
          yearlyMiles: {
            baseValue: 0,
            additionalValue: 0,
            demoMileageNA: false,
            actualPenaltyPerMile: 0,
            penaltyPerMile: 0,
            penaltyPerMileOverridden: false,
            totalValue: 0,
            totalPenalty: null,
            removeDemoMileageFromAnnualMileage: false,
            totalMilesHidden: 0,
            yearlyMilesUpdated: null,
          },
          rebates: [],
          firstPaymentWaiverManuallyOveridden: false,
          securityDepositRateAdjustment: 0,
          totalRebateAmt: null,
          bankFee: null,
          inceptionFee: null,
          programId: null,
          capCostReductionfactors: null,
          workingCashTableData: null,
          driveOffReductionFactors: null,
          totalTaxAmt: null,
          generic: false,
          cashDeficiency: null,
          imbalanceAmount: 0,
          deferredPayment1: null,
          deferredPayment2: null,
          deferredPayment3: null,
          deferredPayment4: null,
          totalPayable: null,
          daysToFirstPayment: 0,
          monthlyPaymentBeforeTax: null,
          taxes: null,
          vendorTaxes: null,
          dealFees: [],
          manuallyDeletedFees: null,
          firstPaymentWaived: false,
          firstPaymentWaivedAmount: null,
          firstPaymentWaivedMaxAmount: null,
          registrationnFee: null,
          stateFee: null,
          titleFee: null,
          wasteTireFee: null,
          bandOFee: null,
          lienFee: null,
          totalCapReduced: 0,
          paidReserveIsFlatFee: false,
          maxRateMarkup: null,
          msLenderCode: null,
          paidReserveIsCapCostParticipation: false,
          collectRegFee: true,
          downPaymentIndex: 0,
          singleScheduledPayment: null,
          taxWaivedPayments: null,
          programFlags: null,
          taxCode: null,
          taxZipCode: null,
          taxAddress: null,
          taxCity: null,
          taxCounty: null,
          taxExemptAmount: null,
          totalRebateAmount: null,
          totalDealerCashAmount: null,
          rolledOverCashDeficiency: null,
          totalMonthlyPayment: null,
          financeCharge: null,
          fordFlexDecreasePct: null,
          fordFlexFirstPayment: null,
          fordFlexFirstTerm: null,
          fordFlexLastPayment: null,
          rebatesBreakup: null,
          proRataAdjustment: null,
          applyCanadaLuxuryTax: false,
          programExpirationDate: null,
          paymentBreakups: null,
          discount: null,
          hasStandardData: false,
          downPaymentWithoutCCRTax: null,
          totalAmountPayable: null,
          optionalFinalPayment: null,
          optionToPurchaseFee: null,
          totalDeposit: null,
          rebatesVerified: false,
          taxOutOfState: false,
        },
        {
          lastModifiedBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
          createdBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
          deleted: false,
          createdTime: 1688637929184,
          modifiedTime: 1688637929184,
          id: 'd975c233-719b-4dfc-a9e1-ee841b2c6b5f',
          downPayment: 600,
          downPaymentPercentage: null,
          downPaymentPct: null,
          downPaymentPctType: 'SELLING_PRICE',
          calcBasedOnDownPmtPct: true,
          outOfPocketCash: null,
          tradeEquityCashBack: null,
          securityDepositOverridden: false,
          securityDeposits: null,
          paymentInfo: null,
          securityDepositV2: null,
          securityDepositId: null,
          securityDepositType: null,
          securityDeposit: null,
          emiAmount: null,
          onePayPayment: null,
          baseEmiAmount: null,
          amountFinanced: null,
          loanToValueRatio: 0,
          selected: false,
          osfLender: false,
          lender: 'BMWCA',
          lenderId: '648980748b5fe56ef89d2735',
          lenderCode: null,
          lienFilingCode: null,
          apr: {
            buyRate: 0,
            effectiveRate: 0,
            buyRateOverridden: true,
            apr: 0,
            moneyFactor: null,
            financeReserve: 0,
            pctOfCapCost: 0,
            financeReserveOverridden: false,
            aprCode: null,
            markUp: 0,
            markUpOverridden: true,
            participation: 0,
            reserveMethod: 0,
            rateType: 0,
            lenderRateType: 0,
            manuallyUpdated: false,
            alternateRateAdjustments: null,
            rateOfInterest: null,
          },
          residual: {
            baseValue: null,
            manuallyUpdated: false,
            residualOverridden: false,
            basePercentage: 0,
            adjustedValue: null,
            adjustedPercentage: 0,
            totalValue: null,
            balloonPaymentValue: null,
            oneTimeBalloonPayment: null,
            totalPercentage: 0,
            residualName: null,
            demoMilesPenaltyMiles: 0,
            demoMilesRate: null,
            demoMilesResidualAdjustment: null,
            demoMilesResidualAdjustmentPct: null,
            residualDisplayType: 'PERCENTAGE',
            adjustedMsrp: null,
            crvMRM: null,
          },
          yearlyMiles: {
            baseValue: 0,
            additionalValue: 0,
            demoMileageNA: false,
            actualPenaltyPerMile: 0,
            penaltyPerMile: 0,
            penaltyPerMileOverridden: false,
            totalValue: 0,
            totalPenalty: null,
            removeDemoMileageFromAnnualMileage: false,
            totalMilesHidden: 0,
            yearlyMilesUpdated: null,
          },
          rebates: [],
          firstPaymentWaiverManuallyOveridden: false,
          securityDepositRateAdjustment: 0,
          totalRebateAmt: null,
          bankFee: null,
          inceptionFee: null,
          programId: null,
          capCostReductionfactors: null,
          workingCashTableData: null,
          driveOffReductionFactors: null,
          totalTaxAmt: null,
          generic: false,
          cashDeficiency: null,
          imbalanceAmount: 0,
          deferredPayment1: null,
          deferredPayment2: null,
          deferredPayment3: null,
          deferredPayment4: null,
          totalPayable: null,
          daysToFirstPayment: 0,
          monthlyPaymentBeforeTax: null,
          taxes: null,
          vendorTaxes: null,
          dealFees: [],
          manuallyDeletedFees: null,
          firstPaymentWaived: false,
          firstPaymentWaivedAmount: null,
          firstPaymentWaivedMaxAmount: null,
          registrationnFee: null,
          stateFee: null,
          titleFee: null,
          wasteTireFee: null,
          bandOFee: null,
          lienFee: null,
          totalCapReduced: 0,
          paidReserveIsFlatFee: false,
          maxRateMarkup: null,
          msLenderCode: null,
          paidReserveIsCapCostParticipation: false,
          collectRegFee: true,
          downPaymentIndex: 0,
          singleScheduledPayment: null,
          taxWaivedPayments: null,
          programFlags: null,
          taxCode: null,
          taxZipCode: null,
          taxAddress: null,
          taxCity: null,
          taxCounty: null,
          taxExemptAmount: null,
          totalRebateAmount: null,
          totalDealerCashAmount: null,
          rolledOverCashDeficiency: null,
          totalMonthlyPayment: null,
          financeCharge: null,
          fordFlexDecreasePct: null,
          fordFlexFirstPayment: null,
          fordFlexFirstTerm: null,
          fordFlexLastPayment: null,
          rebatesBreakup: null,
          proRataAdjustment: null,
          applyCanadaLuxuryTax: false,
          programExpirationDate: null,
          paymentBreakups: null,
          discount: null,
          hasStandardData: false,
          downPaymentWithoutCCRTax: null,
          totalAmountPayable: null,
          optionalFinalPayment: null,
          optionToPurchaseFee: null,
          totalDeposit: null,
          rebatesVerified: false,
          taxOutOfState: false,
        },
      ],
    },
    programManuallyOverridden: false,
    selectedLender: 'BMWCA',
    selectedTier: null,
    selectedLenderId: '648980748b5fe56ef89d2735',
    paymentOption: {
      paymentType: 'LOAN',
      paymentFrequency: 'MONTHLY',
      value: 48,
      apr: null,
      yearlyMiles: null,
      lenderId: null,
      securityDeposit: null,
      downPayment: null,
      residualValue: null,
      residualDisplayType: null,
      fnIs: null,
      rebates: null,
      accessories: null,
      fees: null,
      daysToFirstPayment: null,
      useDefaultFees: null,
      useDefaultAccessories: null,
      useDefaultFNIs: null,
      creditScore: null,
      rank: 1,
    },
    selectedLenderCode: null,
    accessories: [
      {
        lastModifiedBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
        createdBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
        deleted: false,
        createdTime: 1688637929184,
        modifiedTime: 1688637929184,
        id: 'LJ8HG18QDAAW0',
        name: 'Wheels',
        languages: null,
        partNumber: null,
        partId: null,
        cost: 400,
        price: 1000,
        installationCost: null,
        profitType: 'FRONT',
        taxable: true,
        upfront: true,
        taxUpFront: true,
        payType: 'CUSTOMER_PAY',
        code: 'W1',
        opCode: null,
        disclosureType: 'ACCESSORY',
        manuallyUpdated: true,
        uniqueId: 'LJ8HG18QDAAW0',
        residualValue: null,
        residualize: false,
        adjustResidualNotMsrp: false,
        manuallyResidualized: false,
        vehicleEquipmentId: null,
        hardAddNumber: null,
        free: false,
        vatInfo: {
          taxCode: 'A',
          vatPercent: 20,
          taxId: '64958addb7b7b839759e1c23',
        },
        preorder: false,
        source: null,
        discount: null,
        retailPrice: null,
      },
      {
        lastModifiedBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
        createdBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
        deleted: false,
        createdTime: 1688637929184,
        modifiedTime: 1688637929184,
        id: 'LJ8HK3L28OKQW',
        name: 'Spray',
        languages: null,
        partNumber: null,
        partId: null,
        cost: 350,
        price: 2000,
        installationCost: null,
        profitType: 'FRONT',
        taxable: true,
        upfront: true,
        taxUpFront: true,
        payType: 'CUSTOMER_PAY',
        code: 'S1',
        opCode: null,
        disclosureType: 'ACCESSORY',
        manuallyUpdated: true,
        uniqueId: 'LJ8HK3L28OKQW',
        residualValue: null,
        residualize: false,
        adjustResidualNotMsrp: false,
        manuallyResidualized: false,
        vehicleEquipmentId: null,
        hardAddNumber: null,
        free: false,
        vatInfo: {
          taxCode: 'N',
          vatPercent: 5,
          taxId: '64958aa1b7b7b839759e1c21',
        },
        preorder: false,
        source: null,
        discount: null,
        retailPrice: null,
      },
      {
        lastModifiedBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
        createdBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
        deleted: false,
        createdTime: 1688637929184,
        modifiedTime: 1688637929184,
        id: 'LJ8JI885WQLDM',
        name: 'Tyre Inflator',
        languages: null,
        partNumber: null,
        partId: null,
        cost: 200,
        price: 500,
        installationCost: null,
        profitType: 'FRONT',
        taxable: true,
        upfront: false,
        taxUpFront: false,
        payType: 'CUSTOMER_PAY',
        code: 'T1',
        opCode: null,
        disclosureType: 'ESSENTIAL_KIT',
        manuallyUpdated: true,
        uniqueId: 'LJ8JI885WQLDM',
        residualValue: null,
        residualize: false,
        adjustResidualNotMsrp: false,
        manuallyResidualized: false,
        vehicleEquipmentId: null,
        hardAddNumber: null,
        free: false,
        vatInfo: {
          taxCode: 'S',
          vatPercent: 10,
          taxId: '64958abcb7b7b839759e1c22',
        },
        preorder: false,
        source: null,
        discount: null,
        retailPrice: null,
      },
    ],
    fnIs: [],
    fnIsCount: 0,
    totalFnisPriceAmt: null,
    uniqueId: null,
    columnSource: null,
    previousLenderId: null,
    selectedPackageId: null,
    leaseProgramAvailabilityStatus: null,
    programDesc: null,
    sppPaymentOption: null,
    optionContractValidityDate: null,
    financingProduct: null,
    contractNumber: null,
    financeContractNumber: null,
    lenderChanged: false,
    exceptionFromCalc: null,
    ruleWarningMap: null,
    noOfInstallments: null,
  },
  {
    lastModifiedBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
    createdBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
    deleted: false,
    createdTime: 1688637929184,
    modifiedTime: 1688637929184,
    id: '6839fa24-ddcb-4ce5-b460-456b0e009226',
    dealVehicleId: '1caf3219-2f9e-437b-bd2a-b0331c896926',
    termPaymentDetails: {
      '648980748b5fe56ef89d2735': [
        {
          lastModifiedBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
          createdBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
          deleted: false,
          createdTime: 1688637929184,
          modifiedTime: 1688637929184,
          id: 'd012ea03-aed8-449f-b33b-73b6652fb1c5',
          downPayment: 300,
          downPaymentPercentage: null,
          downPaymentPct: null,
          downPaymentPctType: 'SELLING_PRICE',
          calcBasedOnDownPmtPct: true,
          outOfPocketCash: null,
          tradeEquityCashBack: null,
          securityDepositOverridden: false,
          securityDeposits: null,
          paymentInfo: null,
          securityDepositV2: null,
          securityDepositId: null,
          securityDepositType: null,
          securityDeposit: null,
          emiAmount: null,
          onePayPayment: null,
          baseEmiAmount: null,
          amountFinanced: null,
          loanToValueRatio: 0,
          selected: false,
          osfLender: false,
          lender: 'BMWCA',
          lenderId: '648980748b5fe56ef89d2735',
          lenderCode: null,
          lienFilingCode: null,
          apr: {
            buyRate: 0,
            effectiveRate: 0,
            buyRateOverridden: true,
            apr: 0,
            moneyFactor: null,
            financeReserve: 0,
            pctOfCapCost: 0,
            financeReserveOverridden: false,
            aprCode: null,
            markUp: 0,
            markUpOverridden: true,
            participation: 0,
            reserveMethod: 0,
            rateType: 0,
            lenderRateType: 0,
            manuallyUpdated: false,
            alternateRateAdjustments: null,
            rateOfInterest: null,
          },
          residual: {
            baseValue: null,
            manuallyUpdated: false,
            residualOverridden: false,
            basePercentage: 0,
            adjustedValue: null,
            adjustedPercentage: 0,
            totalValue: null,
            balloonPaymentValue: null,
            oneTimeBalloonPayment: null,
            totalPercentage: 0,
            residualName: null,
            demoMilesPenaltyMiles: 0,
            demoMilesRate: null,
            demoMilesResidualAdjustment: null,
            demoMilesResidualAdjustmentPct: null,
            residualDisplayType: 'PERCENTAGE',
            adjustedMsrp: null,
            crvMRM: null,
          },
          yearlyMiles: {
            baseValue: 0,
            additionalValue: 0,
            demoMileageNA: false,
            actualPenaltyPerMile: 0,
            penaltyPerMile: 0,
            penaltyPerMileOverridden: false,
            totalValue: 0,
            totalPenalty: null,
            removeDemoMileageFromAnnualMileage: false,
            totalMilesHidden: 0,
            yearlyMilesUpdated: null,
          },
          rebates: null,
          firstPaymentWaiverManuallyOveridden: false,
          securityDepositRateAdjustment: 0,
          totalRebateAmt: null,
          bankFee: null,
          inceptionFee: null,
          programId: null,
          capCostReductionfactors: null,
          workingCashTableData: null,
          driveOffReductionFactors: null,
          totalTaxAmt: null,
          generic: false,
          cashDeficiency: null,
          imbalanceAmount: 0,
          deferredPayment1: null,
          deferredPayment2: null,
          deferredPayment3: null,
          deferredPayment4: null,
          totalPayable: null,
          daysToFirstPayment: 0,
          monthlyPaymentBeforeTax: null,
          taxes: null,
          vendorTaxes: null,
          dealFees: [],
          manuallyDeletedFees: null,
          firstPaymentWaived: false,
          firstPaymentWaivedAmount: null,
          firstPaymentWaivedMaxAmount: null,
          registrationnFee: null,
          stateFee: null,
          titleFee: null,
          wasteTireFee: null,
          bandOFee: null,
          lienFee: null,
          totalCapReduced: 0,
          paidReserveIsFlatFee: false,
          maxRateMarkup: null,
          msLenderCode: null,
          paidReserveIsCapCostParticipation: false,
          collectRegFee: true,
          downPaymentIndex: 0,
          singleScheduledPayment: null,
          taxWaivedPayments: null,
          programFlags: null,
          taxCode: null,
          taxZipCode: null,
          taxAddress: null,
          taxCity: null,
          taxCounty: null,
          taxExemptAmount: null,
          totalRebateAmount: null,
          totalDealerCashAmount: null,
          rolledOverCashDeficiency: null,
          totalMonthlyPayment: null,
          financeCharge: null,
          fordFlexDecreasePct: null,
          fordFlexFirstPayment: null,
          fordFlexFirstTerm: null,
          fordFlexLastPayment: null,
          rebatesBreakup: null,
          proRataAdjustment: null,
          applyCanadaLuxuryTax: false,
          programExpirationDate: null,
          paymentBreakups: null,
          discount: null,
          hasStandardData: false,
          downPaymentWithoutCCRTax: null,
          totalAmountPayable: null,
          optionalFinalPayment: null,
          optionToPurchaseFee: null,
          totalDeposit: null,
          rebatesVerified: false,
          taxOutOfState: false,
        },
        {
          lastModifiedBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
          createdBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
          deleted: false,
          createdTime: 1688637929184,
          modifiedTime: 1688637929184,
          id: '0f8e2089-f816-4257-82cb-fefd3ad6a898',
          downPayment: 500,
          downPaymentPercentage: null,
          downPaymentPct: null,
          downPaymentPctType: 'SELLING_PRICE',
          calcBasedOnDownPmtPct: true,
          outOfPocketCash: null,
          tradeEquityCashBack: null,
          securityDepositOverridden: false,
          securityDeposits: null,
          paymentInfo: null,
          securityDepositV2: null,
          securityDepositId: null,
          securityDepositType: null,
          securityDeposit: null,
          emiAmount: null,
          onePayPayment: null,
          baseEmiAmount: null,
          amountFinanced: null,
          loanToValueRatio: 0,
          selected: false,
          osfLender: false,
          lender: 'BMWCA',
          lenderId: '648980748b5fe56ef89d2735',
          lenderCode: null,
          lienFilingCode: null,
          apr: {
            buyRate: 0,
            effectiveRate: 0,
            buyRateOverridden: true,
            apr: 0,
            moneyFactor: null,
            financeReserve: 0,
            pctOfCapCost: 0,
            financeReserveOverridden: false,
            aprCode: null,
            markUp: 0,
            markUpOverridden: true,
            participation: 0,
            reserveMethod: 0,
            rateType: 0,
            lenderRateType: 0,
            manuallyUpdated: false,
            alternateRateAdjustments: null,
            rateOfInterest: null,
          },
          residual: {
            baseValue: null,
            manuallyUpdated: false,
            residualOverridden: false,
            basePercentage: 0,
            adjustedValue: null,
            adjustedPercentage: 0,
            totalValue: null,
            balloonPaymentValue: null,
            oneTimeBalloonPayment: null,
            totalPercentage: 0,
            residualName: null,
            demoMilesPenaltyMiles: 0,
            demoMilesRate: null,
            demoMilesResidualAdjustment: null,
            demoMilesResidualAdjustmentPct: null,
            residualDisplayType: 'PERCENTAGE',
            adjustedMsrp: null,
            crvMRM: null,
          },
          yearlyMiles: {
            baseValue: 0,
            additionalValue: 0,
            demoMileageNA: false,
            actualPenaltyPerMile: 0,
            penaltyPerMile: 0,
            penaltyPerMileOverridden: false,
            totalValue: 0,
            totalPenalty: null,
            removeDemoMileageFromAnnualMileage: false,
            totalMilesHidden: 0,
            yearlyMilesUpdated: null,
          },
          rebates: null,
          firstPaymentWaiverManuallyOveridden: false,
          securityDepositRateAdjustment: 0,
          totalRebateAmt: null,
          bankFee: null,
          inceptionFee: null,
          programId: null,
          capCostReductionfactors: null,
          workingCashTableData: null,
          driveOffReductionFactors: null,
          totalTaxAmt: null,
          generic: false,
          cashDeficiency: null,
          imbalanceAmount: 0,
          deferredPayment1: null,
          deferredPayment2: null,
          deferredPayment3: null,
          deferredPayment4: null,
          totalPayable: null,
          daysToFirstPayment: 0,
          monthlyPaymentBeforeTax: null,
          taxes: null,
          vendorTaxes: null,
          dealFees: [],
          manuallyDeletedFees: null,
          firstPaymentWaived: false,
          firstPaymentWaivedAmount: null,
          firstPaymentWaivedMaxAmount: null,
          registrationnFee: null,
          stateFee: null,
          titleFee: null,
          wasteTireFee: null,
          bandOFee: null,
          lienFee: null,
          totalCapReduced: 0,
          paidReserveIsFlatFee: false,
          maxRateMarkup: null,
          msLenderCode: null,
          paidReserveIsCapCostParticipation: false,
          collectRegFee: true,
          downPaymentIndex: 0,
          singleScheduledPayment: null,
          taxWaivedPayments: null,
          programFlags: null,
          taxCode: null,
          taxZipCode: null,
          taxAddress: null,
          taxCity: null,
          taxCounty: null,
          taxExemptAmount: null,
          totalRebateAmount: null,
          totalDealerCashAmount: null,
          rolledOverCashDeficiency: null,
          totalMonthlyPayment: null,
          financeCharge: null,
          fordFlexDecreasePct: null,
          fordFlexFirstPayment: null,
          fordFlexFirstTerm: null,
          fordFlexLastPayment: null,
          rebatesBreakup: null,
          proRataAdjustment: null,
          applyCanadaLuxuryTax: false,
          programExpirationDate: null,
          paymentBreakups: null,
          discount: null,
          hasStandardData: false,
          downPaymentWithoutCCRTax: null,
          totalAmountPayable: null,
          optionalFinalPayment: null,
          optionToPurchaseFee: null,
          totalDeposit: null,
          rebatesVerified: false,
          taxOutOfState: false,
        },
        {
          lastModifiedBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
          createdBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
          deleted: false,
          createdTime: 1688637929184,
          modifiedTime: 1688637929184,
          id: 'a41752f6-7fc7-4463-8d7b-e93d81982e9d',
          downPayment: 700,
          downPaymentPercentage: null,
          downPaymentPct: null,
          downPaymentPctType: 'SELLING_PRICE',
          calcBasedOnDownPmtPct: true,
          outOfPocketCash: null,
          tradeEquityCashBack: null,
          securityDepositOverridden: false,
          securityDeposits: null,
          paymentInfo: null,
          securityDepositV2: null,
          securityDepositId: null,
          securityDepositType: null,
          securityDeposit: null,
          emiAmount: null,
          onePayPayment: null,
          baseEmiAmount: null,
          amountFinanced: null,
          loanToValueRatio: 0,
          selected: false,
          osfLender: false,
          lender: 'BMWCA',
          lenderId: '648980748b5fe56ef89d2735',
          lenderCode: null,
          lienFilingCode: null,
          apr: {
            buyRate: 0,
            effectiveRate: 0,
            buyRateOverridden: true,
            apr: 0,
            moneyFactor: null,
            financeReserve: 0,
            pctOfCapCost: 0,
            financeReserveOverridden: false,
            aprCode: null,
            markUp: 0,
            markUpOverridden: true,
            participation: 0,
            reserveMethod: 0,
            rateType: 0,
            lenderRateType: 0,
            manuallyUpdated: false,
            alternateRateAdjustments: null,
            rateOfInterest: null,
          },
          residual: {
            baseValue: null,
            manuallyUpdated: false,
            residualOverridden: false,
            basePercentage: 0,
            adjustedValue: null,
            adjustedPercentage: 0,
            totalValue: null,
            balloonPaymentValue: null,
            oneTimeBalloonPayment: null,
            totalPercentage: 0,
            residualName: null,
            demoMilesPenaltyMiles: 0,
            demoMilesRate: null,
            demoMilesResidualAdjustment: null,
            demoMilesResidualAdjustmentPct: null,
            residualDisplayType: 'PERCENTAGE',
            adjustedMsrp: null,
            crvMRM: null,
          },
          yearlyMiles: {
            baseValue: 0,
            additionalValue: 0,
            demoMileageNA: false,
            actualPenaltyPerMile: 0,
            penaltyPerMile: 0,
            penaltyPerMileOverridden: false,
            totalValue: 0,
            totalPenalty: null,
            removeDemoMileageFromAnnualMileage: false,
            totalMilesHidden: 0,
            yearlyMilesUpdated: null,
          },
          rebates: null,
          firstPaymentWaiverManuallyOveridden: false,
          securityDepositRateAdjustment: 0,
          totalRebateAmt: null,
          bankFee: null,
          inceptionFee: null,
          programId: null,
          capCostReductionfactors: null,
          workingCashTableData: null,
          driveOffReductionFactors: null,
          totalTaxAmt: null,
          generic: false,
          cashDeficiency: null,
          imbalanceAmount: 0,
          deferredPayment1: null,
          deferredPayment2: null,
          deferredPayment3: null,
          deferredPayment4: null,
          totalPayable: null,
          daysToFirstPayment: 0,
          monthlyPaymentBeforeTax: null,
          taxes: null,
          vendorTaxes: null,
          dealFees: [],
          manuallyDeletedFees: null,
          firstPaymentWaived: false,
          firstPaymentWaivedAmount: null,
          firstPaymentWaivedMaxAmount: null,
          registrationnFee: null,
          stateFee: null,
          titleFee: null,
          wasteTireFee: null,
          bandOFee: null,
          lienFee: null,
          totalCapReduced: 0,
          paidReserveIsFlatFee: false,
          maxRateMarkup: null,
          msLenderCode: null,
          paidReserveIsCapCostParticipation: false,
          collectRegFee: true,
          downPaymentIndex: 0,
          singleScheduledPayment: null,
          taxWaivedPayments: null,
          programFlags: null,
          taxCode: null,
          taxZipCode: null,
          taxAddress: null,
          taxCity: null,
          taxCounty: null,
          taxExemptAmount: null,
          totalRebateAmount: null,
          totalDealerCashAmount: null,
          rolledOverCashDeficiency: null,
          totalMonthlyPayment: null,
          financeCharge: null,
          fordFlexDecreasePct: null,
          fordFlexFirstPayment: null,
          fordFlexFirstTerm: null,
          fordFlexLastPayment: null,
          rebatesBreakup: null,
          proRataAdjustment: null,
          applyCanadaLuxuryTax: false,
          programExpirationDate: null,
          paymentBreakups: null,
          discount: null,
          hasStandardData: false,
          downPaymentWithoutCCRTax: null,
          totalAmountPayable: null,
          optionalFinalPayment: null,
          optionToPurchaseFee: null,
          totalDeposit: null,
          rebatesVerified: false,
          taxOutOfState: false,
        },
      ],
    },
    programManuallyOverridden: false,
    selectedLender: 'BMWCA',
    selectedTier: null,
    selectedLenderId: '648980748b5fe56ef89d2735',
    paymentOption: {
      paymentType: 'LEASE',
      paymentFrequency: 'MONTHLY',
      value: 60,
      apr: null,
      yearlyMiles: null,
      lenderId: null,
      securityDeposit: null,
      downPayment: null,
      residualValue: null,
      residualDisplayType: null,
      fnIs: null,
      rebates: null,
      accessories: null,
      fees: null,
      daysToFirstPayment: null,
      useDefaultFees: null,
      useDefaultAccessories: null,
      useDefaultFNIs: null,
      creditScore: null,
      rank: 1,
    },
    selectedLenderCode: null,
    accessories: [
      {
        lastModifiedBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
        createdBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
        deleted: false,
        createdTime: 1688637929184,
        modifiedTime: 1688637929184,
        id: 'LJ8HG18QDAAW0',
        name: 'Wheels',
        languages: null,
        partNumber: null,
        partId: null,
        cost: 400,
        price: 1000,
        installationCost: null,
        profitType: 'FRONT',
        taxable: true,
        upfront: true,
        taxUpFront: true,
        payType: 'CUSTOMER_PAY',
        code: 'W1',
        opCode: null,
        disclosureType: 'ACCESSORY',
        manuallyUpdated: true,
        uniqueId: 'LJ8HG18QDAAW0',
        residualValue: null,
        residualize: false,
        adjustResidualNotMsrp: false,
        manuallyResidualized: false,
        vehicleEquipmentId: null,
        hardAddNumber: null,
        free: false,
        vatInfo: {
          taxCode: 'A',
          vatPercent: 20,
          taxId: '64958addb7b7b839759e1c23',
        },
        preorder: false,
        source: null,
        discount: null,
        retailPrice: null,
      },
      {
        lastModifiedBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
        createdBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
        deleted: false,
        createdTime: 1688637929184,
        modifiedTime: 1688637929184,
        id: 'LJ8HK3L28OKQW',
        name: 'Spray',
        languages: null,
        partNumber: null,
        partId: null,
        cost: 350,
        price: 2000,
        installationCost: null,
        profitType: 'FRONT',
        taxable: true,
        upfront: true,
        taxUpFront: true,
        payType: 'CUSTOMER_PAY',
        code: 'S1',
        opCode: null,
        disclosureType: 'ACCESSORY',
        manuallyUpdated: true,
        uniqueId: 'LJ8HK3L28OKQW',
        residualValue: null,
        residualize: false,
        adjustResidualNotMsrp: false,
        manuallyResidualized: false,
        vehicleEquipmentId: null,
        hardAddNumber: null,
        free: false,
        vatInfo: {
          taxCode: 'N',
          vatPercent: 5,
          taxId: '64958aa1b7b7b839759e1c21',
        },
        preorder: false,
        source: null,
        discount: null,
        retailPrice: null,
      },
      {
        lastModifiedBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
        createdBy: '71f99a10-f406-4112-a75e-2e47d6b8a979',
        deleted: false,
        createdTime: 1688637929184,
        modifiedTime: 1688637929184,
        id: 'LJ8JI885WQLDM',
        name: 'Tyre Inflator',
        languages: null,
        partNumber: null,
        partId: null,
        cost: 200,
        price: 500,
        installationCost: null,
        profitType: 'FRONT',
        taxable: true,
        upfront: false,
        taxUpFront: false,
        payType: 'CUSTOMER_PAY',
        code: 'T1',
        opCode: null,
        disclosureType: 'ESSENTIAL_KIT',
        manuallyUpdated: true,
        uniqueId: 'LJ8JI885WQLDM',
        residualValue: null,
        residualize: false,
        adjustResidualNotMsrp: false,
        manuallyResidualized: false,
        vehicleEquipmentId: null,
        hardAddNumber: null,
        free: false,
        vatInfo: {
          taxCode: 'S',
          vatPercent: 10,
          taxId: '64958abcb7b7b839759e1c22',
        },
        preorder: false,
        source: null,
        discount: null,
        retailPrice: null,
      },
    ],
    fnIs: [],
    fnIsCount: 0,
    totalFnisPriceAmt: null,
    uniqueId: null,
    columnSource: null,
    previousLenderId: null,
    selectedPackageId: null,
    leaseProgramAvailabilityStatus: null,
    programDesc: null,
    sppPaymentOption: null,
    optionContractValidityDate: null,
    financingProduct: null,
    contractNumber: null,
    financeContractNumber: null,
    lenderChanged: false,
    exceptionFromCalc: null,
    ruleWarningMap: null,
    noOfInstallments: null,
  },
];

export const deal = {
  vehicles: [
    {
      createdTime: null,
      modifiedTime: null,
      deleted: false,
      vin: '2G4GT5GR8C9127978',
      id: '639169f2e6d14a727efa767a',
      marketScanVehicleId: null,
      dealVehicleId: '7a8f644c-6fc2-4e26-aa2b-698c368ca18e',
      dealerId: '4',
      primaryVehicle: true,
      rfIdTag: null,
      stockID: 'BR1145',
      year: 2012,
      make: 'Buick',
      makeId: 'buick',
      displayMake: 'Buick',
      model: 'Regal',
      range: null,
      segment: null,
      displayModel: 'Regal',
      modelCode: '0f1',
      modelType: 'Voltorb',
      mfrModelCode: '4GR69',
      titleInfo: {},
      modelDescription: null,
      exteriorColor: 'Olive',
      interiorColor: 'Green',
      interiorColorCode: 'AFM',
      exteriorColorCode: 'GAN',
      manufacturingColorCode: null,
      languages: {
        documentDefault: null,
        locale: {
          en_US: {
            bodyType: '4dr Car',
            interiorColor: 'Jet Black, Perforated Leather',
            exteriorColor: 'Quicksilver Metallic',
            trim: 'Premium 2',
            fuelType: 'Other',
            marketClassCategory: 'Car',
            driveType: 'Front Wheel Drive',
            aspiration: 'Naturally Aspirated',
            engineCylinders: '4',
            transmissionControlType: 'Automatic 6',
            model: 'Regal',
          },
        },
      },
      tradeInVehicleId: null,
      previousStockID: null,
      customFields: null,
      status: 'SOLD',
      sourceInfo: {
        languages: {
          documentDefault: null,
          locale: {
            en_US: {
              sourceName: null,
            },
          },
        },
      },
      vehicleType: 'SPECIAL',
      vehicleSubType: 'SP2/SP2',
      driveType: null,
      unladenWeightUnit: null,
      grossWeightUnit: null,
      grossVehicleWeightRating: null,
      mileage: 116625,
      mileageType: null,
      mileageStatus: 'ACTUAL',
      pricingDetails: {
        finalCost: 0,
        invoicePrice: 10240,
        retailPrice: 74185,
        deskingSellingPrice: 80758,
        retailPriceVatAmount: null,
        profit: 0,
        totalAdjustments: 0,
        commPrice: null,
        msrp: 80758,
        costOfSale: null,
        vatRate: null,
        vatAmount: null,
        holdBackAmount: 0,
        viHoldBackAmount: 0,
        glBalance: 1,
        reconditioningCost: null,
        loanSellingPrice: 80758,
        leaseSellingPrice: 80758,
        flooringAmt: null,
        adjustedRetailPrice: null,
        wholesalePrice: null,
        internetPrice: 32112,
        commissionPrice: null,
        employeePrice: null,
        supplierPrice: null,
        takePrice: null,
        maxResidualizationMSRP: null,
        marketPrice: null,
        buyingPrice: null,
        realReconditioningCost: null,
        licensePlatePrice: null,
        preparationPrice: null,
        originalLoanSellingPrice: 80758,
        originalLeaseSellingPrice: 80758,
        gasGuzzlerTax: null,
        packageDiscounts: null,
        taxCode: null,
        taxCodeId: null,
        productTaxClassification: null,
        vehicleVatableAmount: null,
        exteriorColorPrice: null,
        interiorColorPrice: null,
        retailVatAmount: null,
        baseRetailVatAmount: null,
        pricingTaxes: null,
        vatInclusive: true,
        colorUpCharge: null,
        discount: null,
        discountVatAmount: null,
      },
      costAdjustments: [
        {
          lastModifiedBy: '49ed43ff-42f0-4052-b758-************',
          createdBy: '49ed43ff-42f0-4052-b758-************',
          deleted: false,
          createdTime: *************,
          modifiedTime: *************,
          id: 'M3H22VE8JQEJ8',
          description: null,
          grossType: 'FRONT',
          amount: null,
          userId: null,
          manuallyAdded: true,
          manuallyUpdated: true,
          reconditioning: false,
          costOfSaleAccountId: null,
          departmentId: null,
          posted: false,
        },
      ],
      roDetails: [],
      licensePlateExpirationDate: 0,
      trimDetails: {
        oem: 'gm',
        brand: 'gm',
        actualBrand: 'gm',
        mapped: true,
        trim: 'Premium 2',
        engineCylinders: 4,
        bodyType: '4dr Car',
        axleCount: 0,
        bodyClass: 'Car',
        manufacturer: 'GENERAL_MOTORS',
        languages: {
          documentDefault: null,
          locale: {
            en_US: {
              bodyType: '4dr Car',
              trim: 'Premium 2',
            },
          },
        },
      },
      five64FuelType: null,
      five64BodyClass: null,
      vehicleAdditionalDetails: {
        rvLength: null,
      },
      temp: false,
      certified: false,
      dealerCertified: false,
      certificationTier: null,
      bodyType: '4dr Car',
      glAccountNumber: null,
      glAccountId: '4_4',
      glAccountNumberAndName: '4 - srvtest135235325235353',
      entryTime: *************,
      stockedInTime: null,
      leadId: null,
      vehicleSoldTime: null,
      viOptions: [
        {
          optionId: '2b9b6368-7c74-4516-bb59-fa571fa5c1e6',
          optionCode: '1SP',
          description: '1SP Premium 2 Package',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: '1SP Premium 2 Package',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '284151f4-e1d3-423f-afc4-73f5ea2522e2',
          optionCode: '1SZ',
          description: 'Discount Option Package',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Discount Option Package',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'd9010a83-65c6-4a6b-8314-37164ee321be',
          optionCode: 'APG',
          description: 'Seat adj, driver, 2 way, power lumbar',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Seat adj, driver, 2 way, power lumbar',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '1670cbbf-f969-49bb-9017-73c18efc3e25',
          optionCode: 'ASV',
          description: 'EQUIPMENT SENSOR AIR MOISTURE & W/S TEMP',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'EQUIPMENT SENSOR AIR MOISTURE & W/S TEMP',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'cdc473a8-b9de-4683-aa95-548a6f5a2fb7',
          optionCode: 'ATH',
          description: 'Keyless entry w/push button start',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Keyless entry w/push button start',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'e64e54c8-8b36-4a9c-ab52-ce6effff2b66',
          optionCode: 'AFM',
          description: 'Jet Black, Perforated Leather',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Jet Black, Perforated Leather',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '61af45aa-1efc-4863-92f4-a1179bacce78',
          optionCode: 'AG2',
          description: 'Power Seat: Passenger, Multi-Directional',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Power Seat: Passenger, Multi-Directional',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '0e01eee6-339d-49b3-85e3-46e2d77bb7b4',
          optionCode: 'AW7',
          description: 'Restraint System Seat, Inflatable, Dr, & Pass, Frt, Si',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Restraint System Seat, Inflatable, Dr, & Pass, Frt, Si',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'cc8e3b3c-51f1-427d-b2c8-4b9a37fad920',
          optionCode: 'B0T',
          description: 'Production Week 36',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Production Week 36',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '566e52b4-a9ae-4388-870f-284f704741f0',
          optionCode: 'B34',
          description: 'Floor Mats, Front',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Floor Mats, Front',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'bfd112ec-aff6-4618-aa7a-8947b5cd90a6',
          optionCode: 'B35',
          description: 'Floor Mats, Rear',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Floor Mats, Rear',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'd9d2d646-ab89-4873-bceb-abcc8f7805b2',
          optionCode: 'BTM',
          description: 'E-Z passive entry',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'E-Z passive entry',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '6432de24-277d-47ae-afe4-7a6c9f8ba372',
          optionCode: 'BTV',
          description: 'Remote vehicle starter system',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Remote vehicle starter system',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'd04cf0e6-a09b-403c-a83c-932c80abc33f',
          optionCode: 'CF5',
          description: 'Sunroof, power',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Sunroof, power',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '7c4a4b46-1bc8-4394-9d00-0d44f0e87037',
          optionCode: 'CJ2',
          description: 'HVAC Sys Air Cond. Frt, Auto Temp Cont, Aux Temp  Cont',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'HVAC Sys Air Cond. Frt, Auto Temp Cont, Aux Temp  Cont',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '728e5681-c2f8-4537-a2cc-d17d8fe73101',
          optionCode: 'E2C',
          description: 'Proc Opt - OTD Expedite',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Proc Opt - OTD Expedite',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '84db6223-e7b4-463c-96ca-c4282a5d4f33',
          optionCode: 'D7A',
          description: 'HANDLE O/S DOOR BODY COLOR, CHROME STRIP',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'HANDLE O/S DOOR BODY COLOR, CHROME STRIP',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '29091cc1-2d54-4147-ad63-b8e53019649c',
          optionCode: 'DCP',
          description: 'OnStar Turn-by-Turn Navigation',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'OnStar Turn-by-Turn Navigation',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '93860b2e-c642-4e69-96e0-31362ac29d1a',
          optionCode: 'DD8',
          description: 'Mirror-In/Outside, Rear Electrochromic W/Auto Dimming',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Mirror-In/Outside, Rear Electrochromic W/Auto Dimming',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '252bf887-e682-4723-a1ae-66f2ccf21982',
          optionCode: 'EF7',
          description: 'U.S. Country Code',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'U.S. Country Code',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'e918bd9b-ca52-4051-bb20-d4e8f544be74',
          optionCode: 'FE9',
          description: 'Federal Emissions',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Federal Emissions',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '9359c4dd-47e2-4504-b79c-fae692fa8acd',
          optionCode: 'GAN',
          description: 'Quicksilver Metallic',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Quicksilver Metallic',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'f6383479-9056-435e-96ba-2e98747cc38d',
          optionCode: 'HP6',
          description: 'Hybrid Propulsion Electric, Parallel, 14KW Continuous P',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Hybrid Propulsion Electric, Parallel, 14KW Continuous P',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '8634a08f-1b60-4a8b-9ce6-9cf28439eeb0',
          optionCode: 'KA1',
          description: 'Seats, heated driver and front passenger',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Seats, heated driver and front passenger',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'c845ceb5-709d-49c0-a351-b1a12c1fcc48',
          optionCode: 'J60',
          description: 'Brakes, 4-wheel antilock',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Brakes, 4-wheel antilock',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '9e91bbd4-f5c2-43c2-93f2-ae94fa79b071',
          optionCode: 'KI6',
          description: 'Receptacle Electrical, Frt Console Rr 120 Volt',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Receptacle Electrical, Frt Console Rr 120 Volt',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'fac9237b-db46-4c44-958e-f9569e2433db',
          optionCode: 'LUK',
          description: 'Engine, Gas, 4-Cyl, 2.4L, Hybrid Assist',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Engine, Gas, 4-Cyl, 2.4L, Hybrid Assist',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '3c40a8c9-0f1c-4b4c-9139-6bd85882f4a6',
          optionCode: 'MHH',
          description: 'Transmission: Automatic, 6-Speed, Hybrid',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Transmission: Automatic, 6-Speed, Hybrid',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '9ac85124-aaa1-470a-a70a-6e1ecae9634c',
          optionCode: 'Q05',
          description: 'Wheels: 17" Aluminum',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Wheels: 17" Aluminum',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'd5600304-b19b-4187-84b0-b80c6d8f37f6',
          optionCode: 'R7V',
          description: 'Show Vehicle ID',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Show Vehicle ID',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '3880af64-9c32-43db-8031-4d5d132552e3',
          optionCode: 'R9N',
          description: 'Premium Trim Pricing Code',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Premium Trim Pricing Code',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '6d24bdc2-fbf1-42f3-b7f2-794bdb9736a8',
          optionCode: 'R9Z',
          description: 'POMS Expedite-Sold',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'POMS Expedite-Sold',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '7a970e67-a6bf-4ac7-baac-fa3b6a8bf356',
          optionCode: 'SLM',
          description: 'Stock Order Modification',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Stock Order Modification',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '1f2df7df-9f22-465e-89b1-feccedceb6ab',
          optionCode: 'T3U',
          description: 'LAMP FRT FOG',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'LAMP FRT FOG',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '9d7aa656-cd12-4f0b-ab75-0a506e04baec',
          optionCode: 'T4F',
          description: 'Headlamps: HID',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Headlamps: HID',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'e319c299-af22-4176-8a88-2a5ed10978de',
          optionCode: 'U2K',
          description: 'XM Satellite Radio',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'XM Satellite Radio',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '1e08ae53-f8f1-4592-8fa4-e9b126978214',
          optionCode: 'UD7',
          description: 'Rear Park Assist',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Rear Park Assist',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '635c3d45-7d27-4183-87d5-9c848a05c117',
          optionCode: 'RAF',
          description: 'Tires All P235/50R17 SL 95T BW ALS',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Tires All P235/50R17 SL 95T BW ALS',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '375da7a9-0cdd-40d0-8283-03d00212d00b',
          optionCode: 'UE1',
          description: 'OnStar Communication System',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'OnStar Communication System',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'e14ae007-b926-4fe3-a9b1-5ea23fc9a8c0',
          optionCode: 'UEW',
          description: 'Audio System with Navigation',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Audio System with Navigation',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'b4ac6258-9e89-4568-a1c9-1fdc7325796c',
          optionCode: 'UG1',
          description: 'Universal Home Remote, Garage Door Opener',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Universal Home Remote, Garage Door Opener',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'a5637b5d-45fc-4c52-bc57-9f6066475a28',
          optionCode: 'USR',
          description: 'USB Port',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'USB Port',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '3304b972-dfbf-4890-b062-3e91e1059f58',
          optionCode: 'UPF',
          description: 'Bluetooth for Phone',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Bluetooth for Phone',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'd1b88d6e-f56b-4f65-b698-4f43f0b14864',
          optionCode: 'UQA',
          description: 'Premium Speaker System',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Premium Speaker System',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '5c0ad5f9-d826-49f9-85d2-5e06f10f7df2',
          optionCode: 'ZR6',
          description: 'Show Vehicles Zone',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Show Vehicles Zone',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
      ],
      options: [
        {
          optionId: '2b9b6368-7c74-4516-bb59-fa571fa5c1e6',
          optionCode: '1SP',
          description: '1SP Premium 2 Package',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: '1SP Premium 2 Package',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '284151f4-e1d3-423f-afc4-73f5ea2522e2',
          optionCode: '1SZ',
          description: 'Discount Option Package',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Discount Option Package',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'd9010a83-65c6-4a6b-8314-37164ee321be',
          optionCode: 'APG',
          description: 'Seat adj, driver, 2 way, power lumbar',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Seat adj, driver, 2 way, power lumbar',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '1670cbbf-f969-49bb-9017-73c18efc3e25',
          optionCode: 'ASV',
          description: 'EQUIPMENT SENSOR AIR MOISTURE & W/S TEMP',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'EQUIPMENT SENSOR AIR MOISTURE & W/S TEMP',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'cdc473a8-b9de-4683-aa95-548a6f5a2fb7',
          optionCode: 'ATH',
          description: 'Keyless entry w/push button start',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Keyless entry w/push button start',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'e64e54c8-8b36-4a9c-ab52-ce6effff2b66',
          optionCode: 'AFM',
          description: 'Jet Black, Perforated Leather',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Jet Black, Perforated Leather',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '61af45aa-1efc-4863-92f4-a1179bacce78',
          optionCode: 'AG2',
          description: 'Power Seat: Passenger, Multi-Directional',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Power Seat: Passenger, Multi-Directional',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '0e01eee6-339d-49b3-85e3-46e2d77bb7b4',
          optionCode: 'AW7',
          description: 'Restraint System Seat, Inflatable, Dr, & Pass, Frt, Si',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Restraint System Seat, Inflatable, Dr, & Pass, Frt, Si',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'cc8e3b3c-51f1-427d-b2c8-4b9a37fad920',
          optionCode: 'B0T',
          description: 'Production Week 36',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Production Week 36',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '566e52b4-a9ae-4388-870f-284f704741f0',
          optionCode: 'B34',
          description: 'Floor Mats, Front',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Floor Mats, Front',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'bfd112ec-aff6-4618-aa7a-8947b5cd90a6',
          optionCode: 'B35',
          description: 'Floor Mats, Rear',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Floor Mats, Rear',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'd9d2d646-ab89-4873-bceb-abcc8f7805b2',
          optionCode: 'BTM',
          description: 'E-Z passive entry',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'E-Z passive entry',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '6432de24-277d-47ae-afe4-7a6c9f8ba372',
          optionCode: 'BTV',
          description: 'Remote vehicle starter system',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Remote vehicle starter system',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'd04cf0e6-a09b-403c-a83c-932c80abc33f',
          optionCode: 'CF5',
          description: 'Sunroof, power',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Sunroof, power',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '7c4a4b46-1bc8-4394-9d00-0d44f0e87037',
          optionCode: 'CJ2',
          description: 'HVAC Sys Air Cond. Frt, Auto Temp Cont, Aux Temp  Cont',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'HVAC Sys Air Cond. Frt, Auto Temp Cont, Aux Temp  Cont',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '728e5681-c2f8-4537-a2cc-d17d8fe73101',
          optionCode: 'E2C',
          description: 'Proc Opt - OTD Expedite',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Proc Opt - OTD Expedite',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '84db6223-e7b4-463c-96ca-c4282a5d4f33',
          optionCode: 'D7A',
          description: 'HANDLE O/S DOOR BODY COLOR, CHROME STRIP',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'HANDLE O/S DOOR BODY COLOR, CHROME STRIP',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '29091cc1-2d54-4147-ad63-b8e53019649c',
          optionCode: 'DCP',
          description: 'OnStar Turn-by-Turn Navigation',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'OnStar Turn-by-Turn Navigation',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '93860b2e-c642-4e69-96e0-31362ac29d1a',
          optionCode: 'DD8',
          description: 'Mirror-In/Outside, Rear Electrochromic W/Auto Dimming',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Mirror-In/Outside, Rear Electrochromic W/Auto Dimming',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '252bf887-e682-4723-a1ae-66f2ccf21982',
          optionCode: 'EF7',
          description: 'U.S. Country Code',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'U.S. Country Code',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'e918bd9b-ca52-4051-bb20-d4e8f544be74',
          optionCode: 'FE9',
          description: 'Federal Emissions',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Federal Emissions',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '9359c4dd-47e2-4504-b79c-fae692fa8acd',
          optionCode: 'GAN',
          description: 'Quicksilver Metallic',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Quicksilver Metallic',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'f6383479-9056-435e-96ba-2e98747cc38d',
          optionCode: 'HP6',
          description: 'Hybrid Propulsion Electric, Parallel, 14KW Continuous P',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Hybrid Propulsion Electric, Parallel, 14KW Continuous P',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '8634a08f-1b60-4a8b-9ce6-9cf28439eeb0',
          optionCode: 'KA1',
          description: 'Seats, heated driver and front passenger',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Seats, heated driver and front passenger',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'c845ceb5-709d-49c0-a351-b1a12c1fcc48',
          optionCode: 'J60',
          description: 'Brakes, 4-wheel antilock',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Brakes, 4-wheel antilock',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '9e91bbd4-f5c2-43c2-93f2-ae94fa79b071',
          optionCode: 'KI6',
          description: 'Receptacle Electrical, Frt Console Rr 120 Volt',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Receptacle Electrical, Frt Console Rr 120 Volt',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'fac9237b-db46-4c44-958e-f9569e2433db',
          optionCode: 'LUK',
          description: 'Engine, Gas, 4-Cyl, 2.4L, Hybrid Assist',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Engine, Gas, 4-Cyl, 2.4L, Hybrid Assist',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '3c40a8c9-0f1c-4b4c-9139-6bd85882f4a6',
          optionCode: 'MHH',
          description: 'Transmission: Automatic, 6-Speed, Hybrid',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Transmission: Automatic, 6-Speed, Hybrid',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '9ac85124-aaa1-470a-a70a-6e1ecae9634c',
          optionCode: 'Q05',
          description: 'Wheels: 17" Aluminum',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Wheels: 17" Aluminum',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'd5600304-b19b-4187-84b0-b80c6d8f37f6',
          optionCode: 'R7V',
          description: 'Show Vehicle ID',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Show Vehicle ID',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '3880af64-9c32-43db-8031-4d5d132552e3',
          optionCode: 'R9N',
          description: 'Premium Trim Pricing Code',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Premium Trim Pricing Code',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '6d24bdc2-fbf1-42f3-b7f2-794bdb9736a8',
          optionCode: 'R9Z',
          description: 'POMS Expedite-Sold',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'POMS Expedite-Sold',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '7a970e67-a6bf-4ac7-baac-fa3b6a8bf356',
          optionCode: 'SLM',
          description: 'Stock Order Modification',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Stock Order Modification',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '1f2df7df-9f22-465e-89b1-feccedceb6ab',
          optionCode: 'T3U',
          description: 'LAMP FRT FOG',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'LAMP FRT FOG',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '9d7aa656-cd12-4f0b-ab75-0a506e04baec',
          optionCode: 'T4F',
          description: 'Headlamps: HID',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Headlamps: HID',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'e319c299-af22-4176-8a88-2a5ed10978de',
          optionCode: 'U2K',
          description: 'XM Satellite Radio',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'XM Satellite Radio',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '1e08ae53-f8f1-4592-8fa4-e9b126978214',
          optionCode: 'UD7',
          description: 'Rear Park Assist',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Rear Park Assist',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '635c3d45-7d27-4183-87d5-9c848a05c117',
          optionCode: 'RAF',
          description: 'Tires All P235/50R17 SL 95T BW ALS',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Tires All P235/50R17 SL 95T BW ALS',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '375da7a9-0cdd-40d0-8283-03d00212d00b',
          optionCode: 'UE1',
          description: 'OnStar Communication System',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'OnStar Communication System',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'e14ae007-b926-4fe3-a9b1-5ea23fc9a8c0',
          optionCode: 'UEW',
          description: 'Audio System with Navigation',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Audio System with Navigation',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'b4ac6258-9e89-4568-a1c9-1fdc7325796c',
          optionCode: 'UG1',
          description: 'Universal Home Remote, Garage Door Opener',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Universal Home Remote, Garage Door Opener',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'a5637b5d-45fc-4c52-bc57-9f6066475a28',
          optionCode: 'USR',
          description: 'USB Port',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'USB Port',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '3304b972-dfbf-4890-b062-3e91e1059f58',
          optionCode: 'UPF',
          description: 'Bluetooth for Phone',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Bluetooth for Phone',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: 'd1b88d6e-f56b-4f65-b698-4f43f0b14864',
          optionCode: 'UQA',
          description: 'Premium Speaker System',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Premium Speaker System',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
        {
          optionId: '5c0ad5f9-d826-49f9-85d2-5e06f10f7df2',
          optionCode: 'ZR6',
          description: 'Show Vehicles Zone',
          quantity: 0,
          fromOEM: false,
          priceImpacted: false,
          dealerInstalledOption: false,
          languages: {
            documentDefault: null,
            locale: {
              en_US: {
                description: 'Show Vehicles Zone',
                packageCode: null,
                packageName: null,
                optionName: null,
                headerTitle: null,
              },
            },
          },
          addToMsrp: false,
          addToRetailPrice: false,
          addToInternetPrice: false,
          addToInvoice: false,
          misMatch: false,
        },
      ],
      mileageForResidualCalculation: 0,
      inServiceDate: 1703164275363,
      serviceVehicleId: null,
      locationCode: '',
      vinLookupResolved: false,
      lastEightVin: null,
      lastSixVin: null,
      lastFourVin: null,
      qualifiedForNewVehiclePrograms: false,
      auctionCar: false,
      demoVehicleIndicator: false,
      fleetVehicleIndicator: false,
      discounts: null,
      offers: null,
      exteriorColorDetail: {},
      selectedDiscounts: null,
      dealConfirmationTime: null,
      amtFinancedDuringPreviousBuy: null,
      monthlyPaymentDuringPreviousBuy: null,
      sellingPriceDuringPreviousBuy: null,
      stockedInAtSiteId: '-1_4',
      soldAtSiteId: null,
      styleId: null,
      previousStatus: null,
      vehicleDisplayImage: null,
      bestStyleName: null,
      agStatusCode: null,
      agStatus: null,
      productionTime: null,
      vgStatusCode: null,
      demoCode: null,
      additionalOEMInfo: {
        makeName: 'Buick',
        modelName: 'Regal',
        modelYear: '2012',
        vehicleClass: 'Sedan',
        isFourByFour: 'false',
        vehicleGroup: null,
        vehicleTrim: ['Premium 2'],
      },
      locationDetails: null,
      seriesCode: null,
      seriesName: null,
      estimatedDeliveryDate: null,
      actualDeliveryDate: null,
      stopDeliveryIndicator: null,
      campaignCodes: null,
      preownedRegistration: null,
      valuationsData: {},
      features: null,
      invoiceDate: null,
      purchaseNumber: null,
      orderStatus: null,
      mada: null,
      madc: null,
      purchaseOrderDate: null,
      exteriorColorLocalization: null,
      interiorColorLocalization: null,
      modelLocalization: null,
      bodyTypeLocalization: null,
      provisionValue: null,
      previousUsage: null,
      optionsRetailPrice: null,
      transformationPrice: null,
      tradeInCommitment: null,
      termMileage: null,
      baseValue: null,
      dateOfFirstAvailability: null,
      emissionValue: null,
      vehiclePriceIncludingOptions: null,
      genre: null,
      reformeB: false,
      isVAT: null,
      customerExpectedDate: null,
      deliveryDealer: null,
      configUrl: null,
      vehicleCategory: null,
      buildVehicle: null,
      typeVariantVersion: null,
      stockOriginSource: null,
      wltpCo2: null,
      professionalUsage: false,
      isVATQualifying: null,
      thirdPartyProviders: null,
      originCountryCode: null,
      purchaseSource: null,
      tempVIN: null,
      foreignRegistration: false,
      policeNumberInfo: null,
      weightType: null,
      useType: null,
      totalCostAdjustmentsAmount: null,
      totalROCostsAmount: null,
      wheelPlan: null,
      isImported: null,
      nedcCo2: null,
      numberOfPreviousOwners: null,
      programId: null,
      hasSingleStyleId: false,
      trim: 'gm | gm | gm | true | Premium 2 | 4 | 4dr Car | Car | GENERAL_MOTORS',
      certificationGroup: {},
    },
  ],
};

export const salesSetupInfoTargetignMSRP = {
  dealConfig: {
    loanDaysToFirstPayment: 0,
    leaseDaysToFirstPayment: 0,
    customerCash: 0,
    defaultReservationAmount: 99,
    annualMiles: 15151,
    loanBuyRate: null,
    loanBuyRateForUsed: null,
    loanBuyRateType: 'DEFAULT',
    daysSelectedForAverageRate: null,
    daysSelectedForAverageRateUsed: null,
    priceType: 'MSRP',
    usedVehiclePriceType: 'INTERNET_PRICE',
    demoVehiclePriceType: null,
    countryCode: '+1',
    defaultDepositProductClassification: null,
    defaultTradeinProductClassification: null,
    defaultDepositAccountingGroup: null,
    dealerMonthConfig: {
      startDay: 1,
      endDay: 0,
    },
    defaultPartExVatInfo: null,
    defaultDepositVatInfo: null,
    depositDisabledVehicleStatus: ['DRAFT', 'IN_TRANSIT'],
    quoteExpiryDuration: 0,
    disableDeliveryAppointmentStatus: 'CONFIRMED',
    financeCommissionGrossType: 'BACK',
    vehicleDealHeaderIdentifier: 'LICENSE_PLATE_NUMBER',
    disableTradeIn: 'PARTIAL_INVOICED',
  },
};

export const salesSetupInfoTargetignRetailPricing = {
  dealConfig: {
    loanDaysToFirstPayment: 0,
    leaseDaysToFirstPayment: 0,
    customerCash: 0,
    defaultReservationAmount: 99,
    annualMiles: 15151,
    loanBuyRate: null,
    loanBuyRateForUsed: null,
    loanBuyRateType: 'DEFAULT',
    daysSelectedForAverageRate: null,
    daysSelectedForAverageRateUsed: null,
    priceType: 'RETAIL_PRICE',
    usedVehiclePriceType: 'INTERNET_PRICE',
    demoVehiclePriceType: null,
    countryCode: '+1',
    defaultDepositProductClassification: null,
    defaultTradeinProductClassification: null,
    defaultDepositAccountingGroup: null,
    dealerMonthConfig: {
      startDay: 1,
      endDay: 0,
    },
    defaultPartExVatInfo: null,
    defaultDepositVatInfo: null,
    depositDisabledVehicleStatus: ['DRAFT', 'IN_TRANSIT'],
    quoteExpiryDuration: 0,
    disableDeliveryAppointmentStatus: 'CONFIRMED',
    financeCommissionGrossType: 'BACK',
    vehicleDealHeaderIdentifier: 'LICENSE_PLATE_NUMBER',
    disableTradeIn: 'PARTIAL_INVOICED',
  },
};

export const vehiclesInfo = {
  id: '639169f2e6d14a727efa767a',
  createdTime: *************,
  modifiedTime: *************,
  deleted: false,
  vin: '2G4GT5GR8C9127978',
  year: 2012,
  make: 'Buick',
  makeId: 'buick',
  displayMake: 'Buick',
  model: 'Regal',
  displayModel: 'Regal',
  modelCode: '0f1',
  modelType: 'Voltorb',
  mfrModelCode: '4GR69',
  trimDetails: {
    oem: 'gm',
    brand: 'gm',
    actualBrand: 'gm',
    mapped: true,
    trim: 'Premium 2',
    engineCylinders: 4,
    bodyType: '4dr Car',
    axleCount: 0,
    bodyClass: 'Car',
    manufacturer: 'GENERAL_MOTORS',
    languages: {
      documentDefault: null,
      locale: {
        en_US: {
          bodyType: '4dr Car',
          trim: 'Premium 2',
        },
      },
    },
  },
  exteriorColorCode: 'GAN',
  exteriorColor: 'Olive',
  exteriorColorEditable: true,
  exteriorColorDetail: {},
  interiorColorDetail: {},
  interiorColorCode: 'AFM',
  interiorColor: 'Green',
  vehicleType: 'SPECIAL',
  vehicleSubType: 'SP2/SP2',
  certified: false,
  dealerCertified: false,
  driveType: 'Front Wheel Drive',
  numberOfKeys: -1,
  ownerManual: -1,
  stockID: 'BR1145',
  status: 'SOLD',
  dealerId: '4',
  tenantId: 'cacargroup',
  entryTime: *************,
  received: false,
  pricingDetails: {
    msrp: 80758,
    retailPrice: 74185,
    invoicePrice: 10240,
    holdBackAmount: 0,
    invoiceDate: 1670474225287,
    internetPrice: 32112,
    glBalance: 9393,
    sellingPrice: 0,
    transferBalance: 1211.37,
    inclusivePrice: 74185,
  },
  mileageForResidualCalculation: 0,
  trim: 'gm | gm | gm | true | Premium 2 | 4 | 4dr Car | Car | GENERAL_MOTORS',
  marketScanVehicleId: null,
  styleId: null,
  valuationsData: {},
  reformeB: false,
  useType: null,
  weightType: null,
};
