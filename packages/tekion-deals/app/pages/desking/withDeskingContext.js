import React, { PureComponent } from 'react';
import _noop from 'lodash/noop';
import _values from 'lodash/values';
import _get from 'lodash/get';
import { defaultMemoize } from 'reselect';
import PropTypes from 'prop-types';

import { DEAL_TYPES } from '@tekion/tekion-base/constants/deal/type';
import * as ColumnDataReader from '@tekion/tekion-base/marketScan/readers/columnData.reader';
import {
  getDealType,
  isTaxBreakUpEnabled,
  isDealDigitalFlow,
} from '@tekion/tekion-base/marketScan/readers/deal.reader';
import { Consumer } from 'pages/desking/desking.context';
import * as SalesSetupReader from '@tekion/tekion-base/marketScan/readers/salesSetup.reader';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';
import { isInchcape, isRRG } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants'; // EMPTY_ARRAY
import {
  calculateTotalEMIAmountForDealerTrade,
  calculateTotalEMIAmountForDealerTradeV1,
  isSameDealCategory,
  isCategory1Deal,
} from '@tekion/tekion-base/marketScan/utils/desking.utils';
import {
  LENDER_PAYMENT_FIELDS,
  MORE_FIELDS,
  TITLE_COLUMN_FIELDS,
  INFO_COLUMN_FIELDS,
  MISCELLANEOUS_ITEMS,
} from '@tekion/tekion-base/marketScan/constants/deskingFields.constants';

import { calculateEMIAmountForFNIDeal } from 'utils/fniUtils';
import { DOWN_PAYMENT_TYPES } from 'pages/desking/desking.constants';
import { canEditAPRMF, canEditResidual } from 'permissions/desking.permissions';
import { DESKING_ACTIONS } from '@tekion/tekion-base/marketScan/constants/desking.constants';
import CalcEngineProperties from 'utils/CalcEngineProperties';

import { DEAL_STATUS } from '@tekion/tekion-base/marketScan/constants/deal.constants';
import {
  isMoveDeal,
  checkTaxRateForWholeSaleDealEnabled,
  isDealerTradeDeal,
  doesDealContainAnyGenericPaymentSubType,
  isGenericPaymentSubType,
} from '../../utils/deal.util';

const WHOLESALE_DEAL = [DEAL_TYPES.WHOLESALE];
const DEALER_TRADE = [DEAL_TYPES.DEALER_TRADE];

function withDeskingContext(WrappedComponent) {
  class DealTypeHandler extends PureComponent {
    onDealTypeChange = async newDealType => {
      const {
        DeskingActions,
        setDefaultValues,
        deal,
        saveDealData,
        setDefaultStateFeeTaxOptions,
        setTaxPercentagesFromSetupForSpecificDealTypes,
      } = this.props;
      const currentDealType = getDealType(deal);
      await DeskingActions.setDealType(newDealType);
      if (WHOLESALE_DEAL.includes(newDealType) || DEALER_TRADE.includes(newDealType)) {
        await setTaxPercentagesFromSetupForSpecificDealTypes();
      }
      if (WHOLESALE_DEAL.includes(currentDealType) || DEALER_TRADE.includes(currentDealType)) {
        await setDefaultStateFeeTaxOptions();
      }
      const dealBelongsToSameCategory = isSameDealCategory(currentDealType, newDealType);
      if (dealBelongsToSameCategory && !WHOLESALE_DEAL.includes(newDealType) && !DEALER_TRADE.includes(newDealType)) {
        saveDealData();
        return;
      }
      await DeskingActions.setMultipleDownPaymentStatus(false);
      await DeskingActions.removeAllColumns();
      await setDefaultValues();
    };

    category3dealTypeChanges = async () => {
      if (CalcEngineProperties.updateCalculationByBackend()) {
        const { syncPaymentDetails } = this.props;
        await syncPaymentDetails();
        return;
      }

      const { DeskingActions, deskingpaymentDetails, deal, salesSetupInfo } = this.props;
      let totalMoney = 0;
      const columnData = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);

      if (checkTaxRateForWholeSaleDealEnabled(deal, salesSetupInfo) || isDealerTradeDeal(deal)) {
        const { totalAmount, totalTax, taxRate, serviceContractTax, bpolTax } = calculateTotalEMIAmountForDealerTradeV1(
          deal,
          columnData
        );
        totalMoney = totalAmount;
        await DeskingActions.setTaxDetails({
          taxes: {
            taxRate,
            salesTax: totalTax,
            serviceContractTax,
            fairFaxCountyTax: bpolTax,
          },
          columnData,
        });
      } else {
        const totalAmount = calculateTotalEMIAmountForDealerTrade(deal, columnData);
        totalMoney = totalAmount;
      }
      await DeskingActions.setEMIAmountForDealerTrade(totalMoney);
      this.changeDownPayment(totalMoney, _values(DOWN_PAYMENT_TYPES));
    };

    updateEMIAmountOfFNIDeal = async () => {
      const { DeskingActions, deskingpaymentDetails, deal, salesSetupInfo } = this.props;
      const generalTaxFeeSettings = SalesSetupReader.generalTaxFeeSettings(salesSetupInfo);

      const taxBreakUpEnabled = DealerPropertyHelper.isTaxBreakUpEnabled() && isTaxBreakUpEnabled(deal);

      const showServiceContractTax =
        _get(generalTaxFeeSettings, ['enableServiceContractTax'], false) || taxBreakUpEnabled;

      const selectedColumn = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);
      const savedColumnFNIs = ColumnDataReader.getColumnFNIs(selectedColumn);
      const { totalMoney, totalTax, taxRate, serviceContractTax } = calculateEMIAmountForFNIDeal(
        selectedColumn,
        savedColumnFNIs,
        deal,
        showServiceContractTax
      );
      await DeskingActions.setTaxDetails({
        taxes: {
          taxRate,
          salesTax: totalTax,
          serviceContractTax,
        },
        columnData: selectedColumn,
      });
      await DeskingActions.setEMIAmountForDealerTrade(totalMoney);
      await this.changeDownPayment(totalMoney, _values(DOWN_PAYMENT_TYPES));
    };

    changeDownPayment = async (totalMoney, downPaymentTypes) => {
      const { DeskingActions, savePaymentsDataForSelectedVehicle } = this.props;
      await DeskingActions.onDownPaymentChange({
        downPayment: totalMoney,
        id: 0,
        downPaymentType: downPaymentTypes,
      });
      await savePaymentsDataForSelectedVehicle();
      DeskingActions.setDealDirtyStatus(false);
    };

    render() {
      return (
        <WrappedComponent
          {...this.props}
          onDealTypeChange={this.onDealTypeChange}
          doFNIDealTypeChangesForAccessories={this.doFNIDealTypeChangesForAccessories}
          category3dealTypeChanges={this.category3dealTypeChanges}
          updateEMIAmountOfFNIDeal={this.updateEMIAmountOfFNIDeal}
        />
      );
    }
  }

  DealTypeHandler.defaultProps = {
    saveDealData: _noop,
    DeskingActions: EMPTY_OBJECT,
    savePaymentsDataForSelectedVehicle: _noop,
    deal: EMPTY_OBJECT,
    setDefaultValues: _noop,
    deskingpaymentDetails: EMPTY_ARRAY,
    getMarketScanData: _noop,
    setTaxPercentagesFromSetupForSpecificDealTypes: _noop,
  };

  DealTypeHandler.propTypes = {
    setTaxPercentagesFromSetupForSpecificDealTypes: PropTypes.func,
    saveDealData: PropTypes.func,
    DeskingActions: PropTypes.object,
    savePaymentsDataForSelectedVehicle: PropTypes.func,
    deal: PropTypes.object,
    setDefaultValues: PropTypes.func,
    deskingpaymentDetails: PropTypes.array,
    getMarketScanData: PropTypes.func,
    salesSetupInfo: PropTypes.object.isRequired,
  };

  return props => (
    <Consumer>
      {consumerProps => (
        <DealTypeHandler {...consumerProps} {...props} getFieldStatus={getFieldStatus(consumerProps)} />
      )}
    </Consumer>
  );
}

export default withDeskingContext;

const getRRGProgramDisableCondition = (deal, fieldId, deskingPaymentDetail) => {
  if (!isRRG()) return false;

  const paymentSubType = _get(deskingPaymentDetail, 'paymentOption.paymentSubType');
  const isPaymentSubTypeGeneric = isGenericPaymentSubType(paymentSubType);
  const doesDealcontainAnyGenericSubType = doesDealContainAnyGenericPaymentSubType(deal);
  const isDigitalDeal = isDealDigitalFlow(deal);
  const isConciergeColumn = _get(deskingPaymentDetail, 'conciergeColumn', false);

  if (fieldId) {
    switch (fieldId) {
      // Enabled if any dealPayment type has Generic SubType
      case TITLE_COLUMN_FIELDS.DOWNPAYMENT_SECTION:
      case TITLE_COLUMN_FIELDS.DEPOSIT:
        return !doesDealcontainAnyGenericSubType;
      // Enabled depending on column info
      case INFO_COLUMN_FIELDS.EMI_CELL:
      case LENDER_PAYMENT_FIELDS.LENDER:
      case LENDER_PAYMENT_FIELDS.YEARLY_MILES:
      case LENDER_PAYMENT_FIELDS.RESIDUAL:
      case LENDER_PAYMENT_FIELDS.DAYS_TO_FIRSTPAYMENT: // @todo update the condition for days to first payment
      case MORE_FIELDS.FINANCE_ADJUSTED_CAPITAL_COST:
      case MORE_FIELDS.DRIVE_OFF:
      case MORE_FIELDS.OUT_OF_POCKET_CASH:
      case MORE_FIELDS.FINANCE_CHARGE:
        return !isPaymentSubTypeGeneric;
      case LENDER_PAYMENT_FIELDS.APR_MONEYFACTOR:
        return isPaymentSubTypeGeneric ? false : isDigitalDeal;
      // Disabled
      case TITLE_COLUMN_FIELDS.DEAL_TYPE_SELECTION_HEADER:
      case MORE_FIELDS.TAXES:
      case MORE_FIELDS.PAYMENT_FREQUENCY:
      case MORE_FIELDS.SECURITY_DEPOSIT:
      case MORE_FIELDS.LOAN_TO_VALUE_RATIO:
      case MISCELLANEOUS_ITEMS.FEE_UPDATE_FORM:
        return true;
      case INFO_COLUMN_FIELDS.TERM_HEADER:
        return isConciergeColumn;
      default:
        break;
    }
  }

  // For all other fields do not disable
  return false;
};

const getInchcapeProgramDisableCondition = (deal, fieldId) => {
  if (!isInchcape()) return false;

  const dealStatus = _get(deal, 'status');

  if (fieldId) {
    switch (fieldId) {
      case TITLE_COLUMN_FIELDS.DEAL_TYPE_SELECTION_HEADER:
        return dealStatus !== DEAL_STATUS.QUOTE;
      case LENDER_PAYMENT_FIELDS.APR_MONEYFACTOR:
        return true;
      default:
        break;
    }
  }

  // For all other fields do not disable
  return false;
};

export const getFieldStatus = defaultMemoize(
  ({ isDeskingViewOnly, isFuseDeal, isDealViewOnly, deal }) =>
    (fieldId, deskingPaymentDetail = null) => {
      const response = { isDisabled: false, disableInputFieldOnly: false };
      const isAPRMFDisabled = !canEditAPRMF();
      const isResidualDisabled = !canEditResidual();
      const dealStatus = _get(deal, 'status');
      const dealType = getDealType(deal);
      const isDealBookedOrClosed = dealStatus === 'BOOKED' || dealStatus === 'CLOSED_OR_SOLD';

      // RRG Specific
      const isExternalSourceMove = isMoveDeal(deal);
      const disableCondtionForRRG = getRRGProgramDisableCondition(deal, fieldId, deskingPaymentDetail);

      // Inchcape Specific
      const disableConditionForInchcape = getInchcapeProgramDisableCondition(deal, fieldId);

      // Honda Specific
      const isOemOfferColumn =
        ColumnDataReader.isOemOfferColumn(deskingPaymentDetail) ||
        ColumnDataReader.isOemOfferApprovedLeaseColumn(deskingPaymentDetail);

      // return response;
      switch (fieldId) {
        case TITLE_COLUMN_FIELDS.DEAL_TYPE_SELECTION_HEADER:
          response.isDisabled =
            isDeskingViewOnly ||
            isFuseDeal ||
            isExternalSourceMove ||
            disableCondtionForRRG ||
            disableConditionForInchcape;
          response.isDealViewOnly = isDealViewOnly || isFuseDeal || isExternalSourceMove || disableCondtionForRRG;
          break;

        case TITLE_COLUMN_FIELDS.DOWNPAYMENT_SECTION:
        case INFO_COLUMN_FIELDS.EMI_CELL:
          response.isDisabled = isDeskingViewOnly || isFuseDeal || isExternalSourceMove || disableCondtionForRRG;
          break;
        case TITLE_COLUMN_FIELDS.DEPOSIT:
          response.isDisabled =
            isDeskingViewOnly ||
            isFuseDeal ||
            dealType === DEAL_TYPES.CASH_REALISATION ||
            isExternalSourceMove ||
            disableCondtionForRRG;
          break;
        case INFO_COLUMN_FIELDS.TERM_HEADER:
          response.isDisabled =
            isDeskingViewOnly ||
            isFuseDeal ||
            [
              DEAL_TYPES.CASH_REALISATION,
              DEAL_TYPES.FNI,
              DEAL_TYPES.DEALER_TRADE,
              DEAL_TYPES.ONLY_TRADES,
              DEAL_TYPES.MOTABILITY,
            ].includes(dealType) ||
            disableCondtionForRRG ||
            disableConditionForInchcape;
          break;

        case DESKING_ACTIONS.MVD_VEHICLE_ACTIONS:
          response.isDisabled = isDeskingViewOnly || isFuseDeal || isDealBookedOrClosed;
          break;

        case INFO_COLUMN_FIELDS.ADD_COLUMN:
          response.isDisabled = isDeskingViewOnly || isFuseDeal || isExternalSourceMove;
          response.addColumnVisible = isCategory1Deal(deal) && !isExternalSourceMove;
          break;

        case LENDER_PAYMENT_FIELDS.APR_MONEYFACTOR:
          response.isDisabled = isDeskingViewOnly || isFuseDeal || disableCondtionForRRG || isOemOfferColumn;
          response.disableInputFieldOnly = isAPRMFDisabled || disableConditionForInchcape;
          break;

        case LENDER_PAYMENT_FIELDS.RESIDUAL:
          response.isDisabled =
            isDeskingViewOnly || isFuseDeal || isExternalSourceMove || disableCondtionForRRG || isOemOfferColumn;
          response.disableInputFieldOnly = isResidualDisabled;
          break;

        case LENDER_PAYMENT_FIELDS.LENDER:
          response.isDisabled =
            isDeskingViewOnly || isFuseDeal || isExternalSourceMove || disableCondtionForRRG || isOemOfferColumn;
          break;
        case LENDER_PAYMENT_FIELDS.YEARLY_MILES:
        case LENDER_PAYMENT_FIELDS.DAYS_TO_FIRSTPAYMENT:
          response.isDisabled = isDeskingViewOnly || isFuseDeal || isExternalSourceMove || disableCondtionForRRG;
          break;

        case LENDER_PAYMENT_FIELDS.REBATE:
        case LENDER_PAYMENT_FIELDS.DUEBILLS:
        case LENDER_PAYMENT_FIELDS.FNIMENU:
        case LENDER_PAYMENT_FIELDS.FINANCE_CONTRACT_NUMBER:
          response.isDisabled = isDeskingViewOnly || isFuseDeal;
          break;

        case MORE_FIELDS.TAXES:
        case MORE_FIELDS.SECURITY_DEPOSIT:
        case MORE_FIELDS.PAYMENT_FREQUENCY:
        case MORE_FIELDS.FINANCE_ADJUSTED_CAPITAL_COST:
        case MORE_FIELDS.LOAN_TO_VALUE_RATIO:
        case MORE_FIELDS.DRIVE_OFF:
        case MORE_FIELDS.FINANCE_CHARGE:
        case MORE_FIELDS.OUT_OF_POCKET_CASH:
        case MORE_FIELDS.TOTAL_DEPOSIT:
        case MORE_FIELDS.TOTAL_AMOUNT_PAYABLE:
        case MORE_FIELDS.EXCESS_MILEAGE_CHARGE:
        case MORE_FIELDS.OPTIONAL_FINAL_PAYMENT:
        case MORE_FIELDS.OPTION_TO_PURCHASE_FEE:
          response.isDisabled = isDeskingViewOnly || isFuseDeal || isExternalSourceMove || disableCondtionForRRG;
          break;

        case MORE_FIELDS.FEES:
          response.isDisabled = isDeskingViewOnly || isFuseDeal || isExternalSourceMove || disableCondtionForRRG;
          break;

        case MISCELLANEOUS_ITEMS.FEE_UPDATE_FORM:
          response.isDisabled = isDeskingViewOnly || isFuseDeal || isExternalSourceMove || disableCondtionForRRG;
          break;

        default:
          break;
      }
      return response;
    }
);
