/* eslint-disable no-new */
/* eslint-disable consistent-return */
import React, { PureComponent } from 'react';

import classNames from 'classnames';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _isEqual from 'lodash/isEqual';
import _setWith from 'lodash/setWith';
import _isNil from 'lodash/isNil';
import _pick from 'lodash/pick';
import _map from 'lodash/map';
import _uniq from 'lodash/uniq';
import _filter from 'lodash/filter';
import _isFunction from 'lodash/isFunction';
import _some from 'lodash/some';
import _set from 'lodash/set';
import _toString from 'lodash/toString';
import _forEach from 'lodash/forEach';
import _values from 'lodash/values';
import _size from 'lodash/size';
import _compact from 'lodash/compact';
import _find from 'lodash/find';
import _flatten from 'lodash/flatten';
import _first from 'lodash/first';
import _findIndex from 'lodash/findIndex';
import _castArray from 'lodash/castArray';
import _every from 'lodash/every';
import _isNumber from 'lodash/isNumber';
import _noop from 'lodash/noop';
import _includes from 'lodash/includes';
import _reduce from 'lodash/reduce';
import _join from 'lodash/join';
import produce from 'immer';
import { compose } from 'recompose';
import queryString from 'query-string';

import { withTekionConversion } from '@tekion/tekion-conversion-web';

import { preloadPSPFKitInstance } from '@tekion/tekion-components/src/organisms/PSPDFkitSignature/PSPDFkitSignature.utils';

import { SALES } from '@tekion/tekion-base/constants/appServices';
import { DEALS } from '@tekion/tekion-base/constants/appConfigs/salesAppConfigs';
import MODES from 'tbusiness/constants/routeModes';
import getRoute from 'tbusiness/factories/route';
import {
  TAX_AND_ZIP_CODE_DETAILS_VS_KEY,
  PAYMENT_TYPE_VS_TAX_TYPE,
} from '@tekion/tekion-base/marketScan/constants/desking.constants';
import { PermissionsError } from '@tekion/tekion-components/src/widgets/permissionsHelper';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';
import SalesDealerPropertyHelper from 'twidgets/appServices/sales/helpers/salesDealerPropertyHelper';
import Logger from '@tekion/tekion-base/logger/Logger';
import { DISCLOSURE_TYPES } from '@tekion/tekion-base/constants/deal/fnis';
import { FNIMenuEvent, EVENT_TYPES } from '@tekion/tekion-base/constants/deal/fniMenu.events';
import { getColumnCreditLifeProducts } from '@tekion/tekion-base/marketScan/utils/fniUpdate.util';
import { getMarketscanPayloadForRecommendation } from '@tekion/tekion-base/marketScan/marketscanPayloadForRecommendation';
import { getMarketScanPayloadObject } from '@tekion/tekion-base/marketScan';
import withActionHandlers from '@tekion/tekion-components/src/connectors/withActionHandlers';

import { EMPTY_OBJECT, EMPTY_ARRAY, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import * as SalesSetupReader from '@tekion/tekion-base/marketScan/readers/salesSetup.reader';
import * as ColumnDataReader from '@tekion/tekion-base/marketScan/readers/columnData.reader';
import * as DealReader from '@tekion/tekion-base/marketScan/readers/deal.reader';
import { isDealAccessibleForCurrentSite } from '@tekion/tekion-widgets/src/appServices/sales/dealerSiteFilter.helpers';

import { toaster, TOASTER_TYPE, dismissToast } from '@tekion/tekion-components/src/organisms/NotificationWrapper';

import { formatValueBasedOnRateType } from '@tekion/tekion-base/constants/retail/marketScan';
import * as VehicleReader from 'utils/vehicleReader';
import * as DealerConfigReader from 'utils/dealerConfig.reader';
import * as CustomerReader from '@tekion/tekion-base/marketScan/readers/customer.reader';
import dealEnv from 'utils/dealEnv';
import {
  getFilterRebates,
  getTargetDealVehiclePaymentDetailsIndex,
  updateRequiredRebatesAmount,
  getUpdatedRebatesForColumn,
  getRebatePayload,
} from 'utils/rebateUtils';
import { getChangesInDeal, resetFees } from 'pages/deallist/deal.util';
import {
  getNumber,
  getModifiedZipForMS,
  findArrayItem,
  getTrimDetailsToSaveInVehicle,
  addStyleDetailListInVehicles,
} from 'utils';
import { setAppTitle } from 'utils/dom.utils';
import {
  isFuseDeal,
  isVehicleSwitchedFromAutomotiveToRV,
  isVehicleSwitchedFromRVToAutomotive,
  getIsProgramAcuraNewAndPrimaryVehicleNotZDX,
} from 'utils/deal.util';
import * as DeskingSubHeaderReader from '@tekion/tekion-base/marketScan/readers/deskingSubHeader.reader';
import AutoScrollDiv from 'molecules/autoScrollDiv';
import ConfettiText from 'molecules/confettiText';
import BackdropLoader from 'twidgets/appServices/oem-agent/molecules/backdropLoader';

import Heading from '@tekion/tekion-components/src/atoms/Heading';
import { Provider } from 'pages/desking/desking.context';
import DealSummary from 'pages/desking/components/dealSummary/DealSummary.container';
import DealCommissions from 'pages/desking/components/dealSummary/DealCommissions';
import GrossBreakUpModal from 'pages/desking/components/grossBreakUp/GrossBreakUpModal';
import DeskingLeftNavigation from 'pages/desking/components/DeskingLeftNavigation';
import CashDeficiencyModal from 'pages/desking/components/cashDeficiencyModal';
import ExpiredProgramsAlert from 'pages/desking/components/expiredProgramsAlert';
import DigitalRetail from 'pages/desking/components/digitalRetail';
import GlobalWarning from 'pages/desking/components/globalWarnings';
import TaskManager from 'pages/desking/components/taskManager';
import PollyCustomerInsightsProvider from 'twidgets/organisms/Polly';
import CalculationUpdateNotification from 'pages/desking/components/calculationUpdateNotification';
import ESignShareModal from 'organisms/ESignShareModal';
import DealAlerts from 'organisms/DealAlerts';
import {
  hasDeskingEdit,
  hasConciergeBuyerCobuyerLinkEditAndShare,
  hasDeskingEditAllFieldsAfterQuoteStatus,
} from 'permissions/desking.permissions';
import { LEASE_WORKING_CASH, AUTO_ROLL_DEFICIT_BEARER } from '@tekion/tekion-base/marketScan/constants/constants';

import { getFirstPaymentWaivedDefaultValue } from 'pages/desking/desking.selectors';
import SelectVehicle from 'organisms/SelectVehicle';
import UpdateVehicleVariant from 'organisms/UpdateVehicleVariant';
import AdditionalOEMVINInfo from 'organisms/AdditionalOEMVINInfo';

import { DEAL_TYPES } from '@tekion/tekion-base/constants/deal/type';
import { REBATE_TYPES } from '@tekion/tekion-base/constants/retail/rebate.constants';
import { getValueFromSessionStorage } from '@tekion/tekion-base/utils/sessionStorage';
import { DEAL_STATUS, DEAL_STATUS_WEIGHTS } from 'pages/deallist/deal.constants';
import PropertyControlledComponentWithMessage from 'pages/desking/components/propertyControlledComponentWithMessage';
import { MESSAGES, TOASTER_STATUS, REBATE_PAYMENT_TYPE_CODES } from 'constants/pages';
import { generateColumnId } from 'utils/markteScan.util';
import { MIGRATED_DEAL_NO_DOCUMENTS_ACCESS } from 'constants/documents';

import CommonApi from 'commonActions/apis';
import DigitalRetailAPI from 'pages/desking/components/digitalRetail/digitalRetail.api';
import MiscellaneousSetupAPI from 'pages/miscellaneousSetup/miscellaneousSetup.api';
import {
  PAYMENT_TYPES,
  LEFT_PANEL_ITEMS,
  LEFT_PANEL_DEAL_STATUS_MAP,
  TABS_THAT_NEEDS_SUBHEADER,
  LEFT_PANEL_ITEMS_PERMISSIONS_BASED,
  RESERVE_METHOD,
  KEYS_FOR_PROVIDER,
  TAG,
  FIRST_HEADER_HEIGHT,
  ZIP_CODE_MANUALLY_UPDATED_API_KEY,
  DEAL_ACQUIRE_KEY,
  PLEASE_FINISH_DESKING,
  getDealNotificationChannelInfo,
  PLEASE_FINISH_MARGIN_APPROVAL,
  DOCUMENT_SIGNING_MODALS,
  CRM_LEAD_SYNC_UPDATES,
  DEFAULT_TERM,
} from 'pages/desking/desking.constants';
import { isAutoGenerateFniMenu, shouldStopAllSharingInDesking } from 'utils/sessionStorage.reader';

import {
  makeFirstVehiclePrimary,
  makeVehiclePrimaryByDealVehicleId,
  getVehiclesWithNoOptions,
} from 'pages/StartDeal/startDeal.utils';
import Button from '@tekion/tekion-components/src/atoms/Button';
import TEnvReader from '@tekion/tekion-base/readers/Env';
import { setDealVersion } from 'commonActions/apis/dealUpdateQueue.api';
import {
  STATE_BY_MARKET_IDS,
  isCaliforniaMarket,
  MSStateID,
} from '@tekion/tekion-base/marketScan/constants/marketScan.constants';

import { RECOMMENDATION_USES_KEYS } from '@tekion/tekion-widgets/src/organisms/budgetBasedRecommendations/constants';

import PdfCast from 'pages/pdfCast';
import {
  getNewFeeItemForAdjustedRegistrationFee,
  removeAdjustedRegistrationFeeItem,
  getAdjustedRegistrationFeeAmount,
} from 'pages/desking/components/feeUpdate/feeUpdate.reader';
import { DEFAULT_ZIP_CODE } from 'constants/dealSetup.constant';

import * as DeskingUtils from '@tekion/tekion-base/marketScan/utils/desking.utils';

import {
  getCustomerAndVehicleIds,
  getRebatesSuperset,
  getTaxRateIndex,
  getAllCColumnsIDs,
} from '@tekion/tekion-base/marketScan/readers/desking.reader';
import { isCanadaDealer } from 'utils/dealerUtils';

import * as StartDealHelpers from 'pages/desking/utils/startDeal.helpers';
import * as FeeHelpers from 'pages/desking/utils/fee.helpers';
import CalcEngineProperties from 'utils/CalcEngineProperties';
import CRMLeadHeartbeat from 'utils/CRMLeadHeartbeat';

import { isInchcape, isRRG } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
import { getEnvironmentVariables } from '@tekion/tekion-base/helpers/envHelper';
import NotificationHandler from 'pages/desking/components/dealDetails/NotificationHandler';
import { getImpactedColumnAndRowPayload, isSetDoNotSendCrmUpdateEventOnVehicleSwitch } from 'utils/desking.helpers';
import { DESKING_FIELDS } from 'constants/desking.constants';
import { SIGNING_PARTNER } from 'constants/signature';
import { BACKEND_GLOBAL_WARNINGS_ACTIONS } from 'commonActions/constants/backendWarnings.constant';
import { DynamicPropertyHelper } from '@tekion/tekion-widgets/src/helpers/dynamicPropertyHelper';
import {
  getPayloadForVehicleTransfers,
  canEnableDocumentsTab,
  isFNIDone,
  formatRebatesResponse,
  shouldShowCustomerLoginModalOrWarning,
  shouldFetchPersonalisedOffers,
  shouldGetPersonalisedOffers,
  getDealItemUpdatePayload,
  addOrRemoveExpiredRebates,
  compareModelCodeOfVehilcels,
  isOEMAccessoriesAdded,
  shouldShowVehicleSwitchWarnings,
  isAnyApprovedColumnAdded,
  getArgumentToPassForTargetting,
  getCrmDetailsUpdated,
  convertUpdateParamsIntoDealKeys,
  getIsPrimaryVehicleChangedInMVD,
  getIsDealLocked,
  shouldFetchRebateApprovalDetails,
  shouldFetchTradeInApprovalDetails,
  shouldFetchRebateApprovalDetailsOnDealUpdate,
} from './desking.helpers';
import withUpdateVehicleVariantHandler from '../../hocs/withUpdateVehicleVariantHandler';
import DeskingFirstLevelHeader from './headers/DeskingFirstLevelHeader';
import SubHeader from './components/subHeader';
import Documents from './components/documents/DocumentsLazy';
import DocumentsV2 from './components/documentsV2/DocumentsV2Lazy';
import Cashiering from './components/cashiering/CashieringLazy';
import CreditApplication from './components/credit/CreditLazy';
import TransactionPosting from './components/transactionPosting/TransactionPostingLazy';
import PreviewGeneratedPDF from './components/PreviewGeneratedPDFModal/PreviewGeneratedPDFLazy';
import FniMenu from './components/fniUpdate/fniMenu/FniMenuLazy';
import FniMenuV2 from './components/fniUpdate/fniMenuV2';
import FNIRecap from './components/fniRecap/FNIRecapLazy';
import MarginApproval from './components/marginApproval/MarginApprovalLazy';
import BudgetBasedRecommendationWithCast from './components/BudgetBasedRecommendationWithCast/BudgetBasedRecommendationWithCastLazy';
import Invoicing from './components/invoicing/InvoicingLazy';
import DealDetails from './components/dealDetails/DealDetailsLazy';
import Contracts from './components/Contracts/ContractsLazy';
import styles from './desking.module.scss';
import DeskingAPI from './desking.api';

import VirtualMeeting from './components/VirtualMeeting/VirtualMeeting.container';
import VirtualMeetingGlobalWarning from './components/VirtualMeeting/Components/VirtualMeetingGlobalWarning';
import DeskingMSHandler from './Desking.MSActionHandlers';
import { deskingPropTypes, deskingDefaultProps } from './desking.proptypes';
import DeskingMscanHelpers from './DeskingMscanHelpers';
import { addMissingRebateKeys } from './components/rebate/rebate.helpers';

import { ACTION_HANDLERS } from './deskingActionHandlers';
import { ACTION_TYPES } from './deskinActionHandlers.actionTypes';

import { getPaymentSubType } from '../../readers/columnDataReader';
import CustomLabelConfig from '../../utils/CustomLabelConfig';
import ChargeCustomer from './headers/ChargeCustomer';
import KEYS_TO_BE_UPDATED_FOR_INVENTORY_VEHICLE_FROM_DEALS, {
  prioritizeVIOverDealPropertiesForVehicles,
} from '../../organisms/editVehicleModal/formFields';
import { FNI_ONLY_DEAL } from './components/fniUpdate/fniUpdate.constants';
import { VEHICLE_OR_CUSTOMER_SWITCH_ERROR } from './components/globalWarnings/globalWarnings.constants';
import { recapApprovalDataReader } from './components/fniRecap/FNIRecap.readers';
import { getRecapApprovalWarnings } from './components/fniRecap/fniRecap.utils';
import RecapApprovalUpdatesHandler from './components/RecapApprovalUpdatesHandler';
import WithMandatoryReviewHOC from '../../organisms/MandatoryAndReviewForm/MandatoryReviewHOC';
import withHandleCustomerApprovals from './hocs/withHandleCustomerApprovals';
import { shouldShowMandatoryReviewFields } from '../../organisms/MandatoryAndReviewForm/mandatoryAndReviewForm.helpers';
import { getRegulatedProductNamesForNotification } from './components/fniUpdate/fniUpdate.util';
const log = (message, extraData) => Logger.log(TAG, message, extraData);
const { LEASE: leaseTaxAndZipCodeType, RETAIL: retailTaxAndZipCodeType } = TAX_AND_ZIP_CODE_DETAILS_VS_KEY;
class Desking extends PureComponent {
  static propTypes = deskingPropTypes;

  static defaultProps = deskingDefaultProps;

  acquiredDealID = '';

  constructor(props) {
    super(props);
    this.selectVehicleRef = React.createRef();
    this.updateVehicleVariantRef = React.createRef();
    this.additionalOEMVINInfoRef = React.createRef();
    this.confettiText = React.createRef();
    this.state = {
      isCommissionListModalOpen: false,
      isRecapViewEnabled: false,
      isUnlockedDeal: false,
      disablePricingEditSettings: false,
      isDealAccessible: true,
      primaryDMS: '',
      chargeCustomer: false,
      stopCredit: false,
      isMarketIdSet: false,
    };
    this.hasConciergeBuyerCobuyerLinkEditAndShare = hasConciergeBuyerCobuyerLinkEditAndShare();

    this.tagettingCallBacks = {
      [CRM_LEAD_SYNC_UPDATES.UPDATE_FEE]: props.addDefaultFee,
      [CRM_LEAD_SYNC_UPDATES.UPDATE_FEE_KEEP_MANUALLY_ADDED]: props.addDefaultFee,
      [CRM_LEAD_SYNC_UPDATES.UPDATE_ACC]: props.addDefaultAccessories,
      [CRM_LEAD_SYNC_UPDATES.UPDATE_DOWNPAYMENT]: props.addDefaultDownPayment,
      [CRM_LEAD_SYNC_UPDATES.UPDATE_BUYER_ZIPCODE]: this.handleBuyerZipcodeFromCrmLeadSync,
      [CRM_LEAD_SYNC_UPDATES.UPDATE_COST_ADJUSTMENT]: props.addDefaultCostAdjustments,
      [CRM_LEAD_SYNC_UPDATES.UPDATE_REBATE_KEEP_CUSTOME_REBATES]: this.updateDealRebates,
      [CRM_LEAD_SYNC_UPDATES.UPDATE_REBATES]: this.updateDealRebates,
      [CRM_LEAD_SYNC_UPDATES.UPDATE_REBATES_PRICE]: this.getUpdatedRebatesForColumn,
    };
  }

  componentDidMount() {
    const { DeskingActions, fnIDeclinationSheetSigningMandatory, FniMenuActions, params } = this.props;
    this.init();
    const dealNumber = _get(params, 'id');
    const isDspEnabled = DealerPropertyHelper.isDigitalSigningPlatformEnabled();
    if (shouldStopAllSharingInDesking(dealNumber)) {
      this.stopAllSharing(true);
    }
    FniMenuActions.getMenuConfig();
    if (isDspEnabled && fnIDeclinationSheetSigningMandatory) {
      DeskingActions.getDeclinationSheetStatus(dealNumber);
    }
    this.addEventListeners();

    preloadPSPFKitInstance();
  }

  componentDidUpdate(prevProps) {
    const {
      deal,
      fnIDeclinationSheetSigningMandatory,
      DeskingActions,
      params,
      recapApprovalDetails,
      reintialiseDesking,
    } = this.props;
    const dealStatus = DealReader.getDealStatus(deal);
    const prevDealStatus = DealReader.getDealStatus(prevProps.deal);

    if (reintialiseDesking && !prevProps.reintialiseDesking) {
      this.reIntialiseDesking();
    }

    if (!_isEmpty(prevProps?.deal) && prevProps?.deal?.lastCalculatorUsed !== deal?.lastCalculatorUsed) {
      this.followupActionsAfterLastCalculatorUsedChange();
    }
    this.performChecksForDynamicPriceUpdate(prevProps, deal);
    if (!_isEmpty(deal)) {
      this.setMarketIdInTaxAndZipCodeDetailsIfNotPresent();
    }
    if (dealStatus !== prevDealStatus || recapApprovalDetails !== prevProps?.recapApprovalDetails) {
      this.handleRecapViewEnabled();
    }
    this.customerLookupOnCustomerUpdate(prevProps?.deal, deal);
    const isDspEnabled = DealerPropertyHelper.isDigitalSigningPlatformEnabled();
    const dealNumber = _get(params, 'id');
    const isPrevSignMandatoryFlagFetching = _isNil(prevProps.fnIDeclinationSheetSigningMandatory);
    if (isPrevSignMandatoryFlagFetching && isDspEnabled && fnIDeclinationSheetSigningMandatory) {
      DeskingActions.getDeclinationSheetStatus(dealNumber);
    }
  }

  componentWillUnmount() {
    const { DeskingActions, DigitalRetailActions, params } = this.props;
    const dealNumber = _get(params, 'id');
    DeskingActions.resetDeskingData();
    DeskingActions.resetCreditData();
    setAppTitle();
    DigitalRetailActions.resetDigitalRetailData();

    if (shouldStopAllSharingInDesking(dealNumber)) this.stopAllSharing(true);
    clearInterval(this.interval);
    if (DealerPropertyHelper.isDealLeadSyncV2Enabled()) CRMLeadHeartbeat.stop();
    this.removeEventListeners();
  }

  reIntialiseDesking = async () => {
    const { DeskingActions, reintialiseDesking } = this.props;
    await DeskingActions.setReinitialiseDesking(true);
    if (reintialiseDesking) {
      this.init();
    }
  };

  getDealViewOnlyFlag = () => {
    const { deal, isDealAcquired, vehicleTransferDetails } = this.props;
    const { isUnlockedDeal } = this.state;
    const isClosedOrSoldDeal = [DEAL_STATUS.BOOKED, DEAL_STATUS.CLOSED_OR_SOLD, DEAL_STATUS.UNWIND].includes(
      deal.status
    );
    const isDealLocked =
      deal.locked || (isClosedOrSoldDeal && !isUnlockedDeal) || !isDealAcquired || DealReader.isPreOrderDeal(deal);
    const isDeskingEditAllFieldsAfterQuoteStatusEnabled = hasDeskingEditAllFieldsAfterQuoteStatus();
    const canEditAllFieldsAfterQuoteStatus =
      isDeskingEditAllFieldsAfterQuoteStatusEnabled === false && ![DEAL_STATUS.QUOTE].includes(deal.status);
    return canEditAllFieldsAfterQuoteStatus || isDealLocked;
  };

  setMarketIdInTaxAndZipCodeDetailsIfNotPresent = async () => {
    const { isMarketIdSet } = this.state;

    const isDeskingViewOnly = !hasDeskingEdit() || this.getDealViewOnlyFlag();
    if (isMarketIdSet || isDeskingViewOnly) return;

    const { deal, DeskingActions } = this.props;
    const isCalcEngineEnabled = CalcEngineProperties.showCalcEngineView();
    const { marketId: retailMarketId, state: retailState } = DealReader.getTaxAndZipCodeDetails(
      deal,
      retailTaxAndZipCodeType
    );
    const { marketId: leaseMarketId, state: leaseState } = DealReader.getTaxAndZipCodeDetails(
      deal,
      leaseTaxAndZipCodeType
    );
    if (!retailMarketId && !_isNumber(retailMarketId) && retailState) {
      await DeskingActions.setTaxAndZipCodeDetails({
        key: 'marketId',
        value: isCalcEngineEnabled ? retailState : MSStateID[retailState],
        type: retailTaxAndZipCodeType,
      });
    }
    if (!leaseMarketId && !_isNumber(leaseMarketId) && leaseState) {
      await DeskingActions.setTaxAndZipCodeDetails({
        key: 'marketId',
        value: isCalcEngineEnabled ? leaseState : MSStateID[leaseState],
        type: leaseTaxAndZipCodeType,
      });
    }
    this.setState({ isMarketIdSet: true });
  };

  fetchCustomLabelConfigForAuditLogs = async () => {
    const { response } = await DeskingAPI.fetchCustomLabelConfigBasedonProgram();
    if (!response) {
      toaster(TOASTER_TYPE.ERROR, __('Audit Logs custom labels fetching failed'));
      return;
    }
    new CustomLabelConfig({ config: DeskingUtils.formatAndTranslateCustomLabelsForAuditLogs(response) });
  };

  performChecksForDynamicPriceUpdate = (prevProps, deal) => {
    const showCustomerLoginModal = shouldShowCustomerLoginModalOrWarning(deal);
    if (
      showCustomerLoginModal &&
      (prevProps?.deal?.vehicles !== deal?.vehicles || prevProps?.deal?.customers !== deal?.customers)
    ) {
      this.updateActivateConcierge(deal);
    }
    if (
      !_isEmpty(prevProps?.deal) &&
      shouldGetPersonalisedOffers(deal) &&
      shouldFetchPersonalisedOffers(prevProps?.deal, deal)
    ) {
      this.fetchPersonalisedOffers(deal);
    }
  };

  fetchPersonalisedOffers = async deal => {
    const { DeskingActions } = this.props;
    const program = DealReader.getProgramName(deal);
    const dealNumber = DealReader.getDealNumber(deal);
    await DeskingActions.getPersonalisedOffers(program, dealNumber);
  };

  customerLookupOnCustomerUpdate = async (prevDeal, deal) => {
    const { CommonActions } = this.props;
    const buyer = DealReader.getBuyer(deal);
    const prevBuyer = DealReader.getBuyer(prevDeal);
    if (!_isEmpty(buyer) && !_isEmpty(prevBuyer) && !_isEqual(prevBuyer?.customerId, buyer?.customerId)) {
      const { customerId } = buyer;
      await CommonActions.getCustomerLookup(customerId);
    }
  };

  updateActivateConcierge = async deal => {
    const { DigitalRetailActions } = this.props;
    const dealNumber = DealReader.getDealNumber(deal);
    const isDealConciergeEnabled = await DigitalRetailActions.getConciergeStatus(dealNumber);
    if (!isDealConciergeEnabled) {
      await DigitalRetailAPI.setConciergeActivateFromModal(dealNumber);
      await DigitalRetailActions.getConciergeDetails(dealNumber);
    }
  };

  followupActionsAfterLastCalculatorUsedChange = () => {
    if (!CalcEngineProperties.updateByGalaxyEngine()) return;
    const { deal, DeskingActions, CommonActions } = this.props;
    CalcEngineProperties.resetInstance({ deal });
    DeskingActions.getStateFeeTaxConfigMetadata(DealReader.getPrimaryVehicleCategory(deal));
    CommonActions.getSalesSetup(DealReader.getDealCreatedAtSite(deal));
  };

  stopAllSharing = (forceStopCasting = false) => {
    const { DigitalRetailActions, params } = this.props;
    const dealNumber = _get(params, 'id');
    DigitalRetailActions.stopAllSharing(dealNumber, EMPTY_ARRAY, false, forceStopCasting);
  };

  setActiveTab = () => {
    const { deal, DeskingActions, location } = this.props;
    const dealNumber = DealReader.getDealNumber(deal);

    if (isAutoGenerateFniMenu(dealNumber)) DeskingActions.setSelectedtab(LEFT_PANEL_ITEMS.FINANCE_AND_INSURANCE);
    else {
      const navigateToTab =
        _get(location, ['state', 'navigateToTab']) || _get(queryString.parse(location?.search), 'tab');

      const isFNIEnabled = DealerPropertyHelper.isFnIEnabled();
      const dealStatus = DealReader.getDealStatus(deal);
      const selectedTab =
        navigateToTab ||
        (isFNIEnabled ? LEFT_PANEL_DEAL_STATUS_MAP()[dealStatus] : LEFT_PANEL_DEAL_STATUS_MAP()[DEAL_STATUS.QUOTE]);
      DeskingActions.setSelectedtab(selectedTab);
    }
    this.handleRecapViewEnabled();
  };

  updateAssociatedOriginalDealNumber = () => {
    const { deal, DeskingActions } = this.props;
    const dealType = DealReader.getDealType(deal);

    if (dealType === FNI_ONLY_DEAL) {
      const associatedOriginalDealNumber = _get(deal, 'associatedOriginalDealNumber');
      DeskingActions.updateAssociatedOriginalDealNumber(associatedOriginalDealNumber);
    }
  };

  handleRecapViewEnabled = () => {
    const { deal, recapApprovalDetails } = this.props;
    const isWorkflowEnabled = recapApprovalDataReader.workflowEnabled(recapApprovalDetails);
    const recapWarnings = getRecapApprovalWarnings(recapApprovalDetails);
    const isRecapDone = recapApprovalDataReader.isRecapDone(recapWarnings);
    const isRecapApprovalCompleted = isWorkflowEnabled ? isRecapDone : false;
    const isRecapViewEnabled =
      DealReader.isDealStatusGreaterThanStatus(
        deal,
        isRRG() ? DEAL_STATUS.BUSINESS_PROPOSAL_ACKNOWLEDGEMENT_PENDING : DEAL_STATUS.FINANCE_AND_INSURANCE
      ) || isRecapApprovalCompleted;

    this.setState({ isRecapViewEnabled });
  };

  toggleCommissionListModal = () => {
    this.setState(prevState => ({
      isCommissionListModalOpen: !prevState.isCommissionListModalOpen,
    }));
  };

  toggleRecapView = () => {
    this.setState(prevState => ({
      isRecapViewEnabled: !prevState.isRecapViewEnabled,
    }));
  };

  setApplicationTitle = (vehicle, customer) => {
    const lNname = _get(customer, 'lastName');
    const stockId = _get(vehicle, 'stockID') || '';
    const model = _get(vehicle, 'model') || '';
    const displayModel = _get(vehicle, 'displayModel');
    const modelToShow = displayModel || model;

    if (!stockId) {
      return setAppTitle();
    }
    if (lNname) {
      return setAppTitle(`${stockId} | ${lNname}`);
    }
    if (modelToShow) {
      return setAppTitle(`${stockId} | ${modelToShow}`);
    }
  };

  heartbeatInterval = () => {
    this.interval = setInterval(() => {
      const key = getValueFromSessionStorage(DEAL_ACQUIRE_KEY);
      const { deal, isDealAcquired } = this.props;
      const dealNumber = DealReader.getDealNumber(deal);
      const crmLeadHeartbeatStatus = DealerPropertyHelper.isDealLeadSyncV2Enabled()
        ? CRMLeadHeartbeat.getCRMLeadHeartbeatStatus()
        : EMPTY_OBJECT;
      const payload = {
        dealNumber,
        uniqueId: key,
        ...crmLeadHeartbeatStatus,
      };
      if (dealNumber && isDealAcquired && DealerPropertyHelper.isCRMEnabled()) DeskingAPI.updateDealHeartBeat(payload);
    }, 10000);
  };

  init = async () => {
    const {
      CommonActions,
      DeskingActions,
      acquireCurrentDeal,
      isDealAcquired,
      DigitalRetailActions,
      blockScreen,
      unBlockScreen,
      startCastingForESignPlatform,
    } = this.props;

    if (_isFunction(blockScreen)) blockScreen(false, undefined, MESSAGES.PLEASE_WAIT);

    const dealerCountryCode = DealerConfigReader.getDealerCountryCode();
    DeskingActions.resetDocumentsData();
    CommonActions.getDealers();
    await DeskingActions.resetDeskingData();
    const deal = await this.getDeal();
    const dealNumber = DealReader.getDealNumber(deal);
    if (DealerPropertyHelper.isDealLeadSyncV2Enabled()) new CRMLeadHeartbeat(dealNumber, DealReader.getLeadId(deal));
    if (!isDealAcquired) {
      await acquireCurrentDeal();
    }
    if (!deal) {
      toaster('error', __('Failed to get Deal'));
      return;
    }
    DeskingActions.setMakeAliases(deal); // temp code for make standardisation
    if (!isDealAccessibleForCurrentSite(deal) && DealerPropertyHelper.isMultiOEMEnabled()) {
      this.setState({ isDealAccessible: false });
      return;
    }

    const payloadToFetchTransferDetails = getPayloadForVehicleTransfers(deal);
    CommonActions.fetchVehicleTransferDetails(payloadToFetchTransferDetails);
    CommonActions.getCostAdjustments();
    CommonActions.getDueBills();
    CommonActions.getConciergeSetup();
    DeskingActions.getDocumentsViewPreferences();
    CommonActions.getVIMetaData();
    CommonActions.fetchStateListFromCountryCode(dealerCountryCode);
    await CommonActions.getCMSLenderCodes();
    DeskingActions.fetchDealJacket(dealNumber);
    DeskingActions.getTradeInMediaDetails(dealNumber);
    DeskingActions.getStateFeeTaxConfigMetadata(DealReader.getPrimaryVehicleCategory(deal));
    this.heartbeatInterval();
    await this.getViSettings();
    await CommonActions.getSalesSetup(DealReader.getDealCreatedAtSite(deal));

    const deskingpaymentDetails = DealReader.getDeskingPaymentDetailsFromDeal(deal);
    const lenderIds = _uniq(_map(deskingpaymentDetails, ({ selectedLenderId }) => selectedLenderId));
    await CommonActions.getLenderDatabyIdsInSetup(lenderIds);
    CommonActions.fetchDueBillDisclosureTypes();
    CommonActions.fetchFniDisclosureTypes();
    setDealVersion(deal);
    this.initWithDeal(deal);
    const isDealConciergeEnabled = await DigitalRetailActions.getConciergeStatus(dealNumber);
    const isGetPersonalisedOffers = shouldGetPersonalisedOffers(deal);
    if (isGetPersonalisedOffers) {
      const program = DealReader.getProgramName(deal);
      await DeskingActions.getPersonalisedOffers(program, dealNumber);
    }
    const showCustomerLoginModal = shouldShowCustomerLoginModalOrWarning(deal);
    if (isDealConciergeEnabled) {
      await DigitalRetailActions.getConciergeDetails(dealNumber);
    } else if (showCustomerLoginModal) {
      await DigitalRetailAPI.setConciergeActivateFromModal(dealNumber);
      await DigitalRetailActions.getConciergeDetails(dealNumber);
    }
    DeskingActions.getMandatoryFields();
    const dmsProvider = await DeskingActions.getPrimaryDMSInfo();
    this.setState({ primaryDMS: dmsProvider });
    if (DealerPropertyHelper.isExternalDmsEnabled()) DeskingActions.fetchDealSyncMappingWarning(dealNumber);
    if (_isFunction(unBlockScreen)) unBlockScreen();

    // we are reading this because this key only populate when deal created from open api no other case
    const calculationNeededForApc = _get(deal, 'calculationNeededForApc', true);

    if (CalcEngineProperties.updateCalculationByBackend()) {
      if (calculationNeededForApc) {
        await this.cashDeficiencyAutoRollGalaxy();
      }
      await CommonActions.saveLenderDetails(dealerCountryCode);
    }
    if (isInchcape()) {
      await this.fetchCustomLabelConfigForAuditLogs();
      this.updateAssociatedOriginalDealNumber();
    }

    await CommonActions.getVehicleOpCodeData();
    startCastingForESignPlatform({ shouldFetchDealerESignLink: true });
    const buyer = DealReader.getBuyer(deal);
    if (!_isEmpty(buyer)) {
      const { customerId } = buyer;
      await CommonActions.getCustomerLookup(customerId);
    }

    const isVariantPresent = StartDealHelpers.isVehicleHaveVariantPresent(
      deal,
      CalcEngineProperties.updateByCalcEngine()
    );
    const isDeskingViewOnly = !hasDeskingEdit() || this.getDealViewOnlyFlag();
    const paymentsInfoInDeal = DealReader.getDealPaymentsForAllVehicles(deal);
    const isDealCorrupted = this.getIfDealIsCorrupted(deal, paymentsInfoInDeal);
    const migrated = DealReader.migrated(deal);
    if (
      !isDeskingViewOnly &&
      !isDealCorrupted &&
      !migrated &&
      !isVariantPresent &&
      calculationNeededForApc &&
      DealerPropertyHelper.isNewVehicleVariantEnabled()
    ) {
      await this.getMarketScanVehicleIdForVehicles();
    }
    if (DealReader.getDealStatus(deal) !== DEAL_STATUS.BOOKED && CalcEngineProperties.updateByGalaxyEngine()) {
      const isProgramExpired = await DeskingActions.fetchLenderMetaData(deal);
      if (isProgramExpired) {
        await GlobalWarning.showWarnings();
      }
    }
    if (DealerPropertyHelper.isCustomerApprovalsEnabled()) {
      await this.fetchAllCustomerApprovalDetails(deal);
    }
  };

  fetchAllCustomerApprovalDetails = async deal => {
    const { DeskingActions } = this.props;
    if (shouldFetchRebateApprovalDetails(deal) || shouldFetchTradeInApprovalDetails(deal)) {
      await DeskingActions.fetchCustomerApprovalDetails(deal);
      await this.showGlobalWarnings();
    }
  };

  fetchAllCustomerApprovalDetailsOnDealUpdate = async () => {
    const { DeskingActions, deal, rebateApprovalData } = this.props;
    if (shouldFetchRebateApprovalDetailsOnDealUpdate(rebateApprovalData)) {
      await DeskingActions.fetchCustomerApprovalDetails(deal);
      await this.showGlobalWarnings();
    }
  };

  showGlobalWarnings = async () => {
    await GlobalWarning.showWarnings();
  };

  getViSettings = async () => {
    const response = await CommonApi.getViSettings();
    const canEditPrice = _get(response, 'response.disablePricingEditSettings.enable') || false;
    this.setState({ disablePricingEditSettings: canEditPrice });
  };

  unLockDealCallBack = async ({ recalculatePayments = false }) => {
    const { DeskingActions } = this.props;
    this.setState({ isUnlockedDeal: true });
    await DeskingActions.setCallMarketScan(recalculatePayments);
  };

  resetIsUnlockedDealStatus = () => {
    this.setState({ isUnlockedDeal: false });
  };

  onDuplicateDeal = async () => {
    const {
      CommonActions: { createDuplicateDeal },
      deal,
    } = this.props;
    const newDealNumber = await createDuplicateDeal(deal);

    const deskingRoute = getRoute(SALES, DEALS.getKey(), {
      mode: MODES.EDIT,
      dealNumber: newDealNumber,
    });
    window.open(`${window.location.origin}${deskingRoute}`, '_blank');
  };

  setDefaultValues = async () => {
    const { deal } = this.props;
    await this.setCallMarketScanOnLoad(deal);
    await this.setRequiredInitialDataForVehicles(deal);
    if (!DeskingUtils.isCategory1Deal(deal)) {
      await this.setInitialValuesForOtherDeals();
      return;
    }
    await this.setInitialDefaultValues();
  };

  setCallMarketScanOnLoad = async deal => {
    const { DeskingActions, location } = this.props;
    let canCallMS = !!DeskingUtils.isCategory1Deal(deal) || CalcEngineProperties.updateCalculationByBackend();
    const isUnlockedDeal = _get(location, ['state', 'unlockedDeal']) || false;
    this.setState({ isUnlockedDeal });
    if (isUnlockedDeal) {
      canCallMS = _get(location, ['state', 'recalculatePayments']) || false;
      await DeskingActions.setCallMarketScan(canCallMS);
    } else {
      await DeskingActions.setCallMarketScan(canCallMS);
    }
  };

  initWithDeal = async deal => {
    const { blockScreen, unBlockScreen } = this.props;

    const { DeskingActions, NotesActions } = this.props;
    const dealId = DealReader.getDealId(deal);
    const dealNumber = DealReader.getDealNumber(deal);
    await DeskingActions.setSelectedDealForDesking(deal);
    blockScreen(false, undefined, MESSAGES.PLEASE_WAIT);
    await this.getFullDetailsOfCustomersAndVehiclesOfDeal(deal);
    await this.setCallMarketScanOnLoad(deal);
    await this.initColumnsDataFromDeal();
    this.setActiveTab();

    this.setApplicationTitle(DealReader.getPrimaryVehicle(deal), DealReader.getBuyer(deal));
    DeskingActions.setPackageDocs(dealNumber);
    DeskingActions.setSppPackageDocs(dealNumber);
    DeskingActions.setScanDocs(deal);
    this.getIntialCreditApplications();
    NotesActions.handleFetchNote(dealId, dealNumber);
    unBlockScreen();
  };

  restoreWithDeal = async deal => {
    const { blockScreen, unBlockScreen } = this.props;
    blockScreen();
    await this.initWithDeal(deal);
    this.savePaymentsDataForSelectedVehicle();
    this.saveDealData();
    unBlockScreen();
  };

  getIntialCreditApplications = async () => {
    const { DeskingActions, deal } = this.props;
    const dealNumber = DealReader.getDealNumber(deal);
    const applicationResponse = await DeskingActions.getExistingCreditApplicationsForCurrentDeal(dealNumber);
    await DeskingActions.getAllCreditApplicationDecisions(dealNumber);
    if (_isNil(applicationResponse)) toaster(TOASTER_STATUS.ERROR, MESSAGES.CREDIT_GET_EXISTING_APPLICATIONS_FAILED);
  };

  getDeal = async () => {
    const { params, DeskingActions } = this.props; //eslint-disable-line
    const dealNumber = _get(params, 'id');
    log(`Desking init for deal number ${dealNumber}`);
    const deal = await DeskingActions.getDealInfo(dealNumber);
    new CalcEngineProperties({ deal });
    return deal;
  };

  /**
   * 1. We will have only id's for certain customers and vehicles in deal. But we need full info for desking screen.
   * 2. We would have obtained most of the users and vehicles full info in deal list.
   * 3. In case user comes to this screen directly, then we would have skipped step 2 and so obtaining info here also.
   * 4. This info is also needed to take initial credit score, retail price, invoice price.
   */
  getFullDetailsOfCustomersAndVehiclesOfDeal = async deal => {
    const { vehiclesInfo, customersInfo, CommonActions } = this.props;
    const {
      // customerIds,
      vehicleIds,
    } = getCustomerAndVehicleIds(deal, vehiclesInfo, customersInfo);
    // log('Getting full information about customers ', customerIds);
    // await CommonActions.getCustomersById(customerIds);
    log('Getting full information about vehicles ', vehicleIds);
    if (_size(vehicleIds)) {
      await CommonActions.getVehiclesById(vehicleIds);
    }
  };

  /**
   * 1. Sets the selling price of the primary vehicle in deal to retail price, if selling price is not present initially.
   * 2. Set the qualifiedForNewVehiclePrograms based on the vehicleSubtype
   */
  setRequiredInitialDataForVehicles = async (deal, config = EMPTY_OBJECT) => {
    const { salesSetupInfo, vehiclesInfo, defaultNewSellPrice, defaultUsedSellingPriceType } = this.props;
    const dealType = DealReader.getDealType(deal);
    if (dealType === DEAL_TYPES.FNI) {
      // dont set pricing details for only fni deal
      return;
    }
    const { DeskingActions } = this.props;
    const vehicles = await StartDealHelpers.setRequiredInitialDataForVehicles({
      deal,
      salesSetupInfo,
      vehiclesInfo,
      setQualifiedForNewVehiclePrograms: config?.setQualifiedForNewVehiclePrograms,
      defaultNewSellPrice,
      defaultUsedSellingPriceType,
    });

    await DeskingActions.setDeal({ ...deal, vehicles });
  };

  setRequiredInitialDataForCustomer = async deal => {
    const { salesSetupInfo } = this.props;
    await this.setCreditScoreOfBuyerInDeal(deal);
    const defaultDealerZip = SalesSetupReader.getDealerZipCode(salesSetupInfo);
    const isSeparateTaxEnabled = DealReader.getEnableSeparateTaxCalc(deal);
    const buyer = DealReader.getBuyer(deal) || EMPTY_OBJECT;
    const zipCodeInCMS = CustomerReader.getCurrentZIPCodeOfBuyerInDeal(buyer);
    const buyerMarketId = CustomerReader.getMarketId(buyer);

    if (!buyerMarketId) await this.getAndSetMarketIdForPrimaryCustomerZip();
    const { deal: dealAfterSetingMarketId } = this.props;

    const buyerAfterUpdatingMarketId = DealReader.getBuyer(dealAfterSetingMarketId) || EMPTY_OBJECT;

    // const alwaysUseDealerZipInDeal = SalesSetupReader.getAlwaysUseDealerZipInDeal(salesSetupInfo);
    // const zipCodeInDeal = DealReader.getZipCode(deal, ColumnDataReader.getColumnTaxAndZipCodeType(ColumnDataReader.getSelectedColumn(deskingpaymentDetails)));
    // const zipCode = alwaysUseDealerZipInDeal ? defaultDealerZip : (zipCodeInDeal || zipCodeInCMS || defaultDealerZip);
    if (isSeparateTaxEnabled) {
      const { alwaysUseDealerZipInDealLease, alwaysUseDealerZipInDealRetail, collectTaxLease, collectTaxRetail } =
        this.getDefaultZipAndCollectTaxConfig();
      const zipCodeLease = alwaysUseDealerZipInDealLease ? defaultDealerZip : zipCodeInCMS || defaultDealerZip;
      const zipCodeRetail = alwaysUseDealerZipInDealRetail ? defaultDealerZip : zipCodeInCMS || defaultDealerZip;
      const address = DeskingUtils.getAddressForZipCodeDetailsFromBuyer(
        buyerAfterUpdatingMarketId,
        SalesSetupReader.getShowLocationInZipCodeScreen(salesSetupInfo)
      );
      await this.getAndSetZipCodeDetailsInDeal({
        zipCode: zipCodeLease,
        type: leaseTaxAndZipCodeType,
        address: alwaysUseDealerZipInDealLease ? {} : address,
        collectTax: collectTaxLease,
      });
      await this.getAndSetZipCodeDetailsInDeal({
        zipCode: zipCodeRetail,
        type: retailTaxAndZipCodeType,
        address: alwaysUseDealerZipInDealLease ? {} : address,
        collectTax: collectTaxRetail,
      });
    } else {
      const { alwaysUseDealerZipInDeal, collectTaxRetail } = this.getDefaultZipAndCollectTaxConfig();
      const zipCode = alwaysUseDealerZipInDeal ? defaultDealerZip : zipCodeInCMS || defaultDealerZip;
      const address = DeskingUtils.getAddressForZipCodeDetailsFromBuyer(
        buyerAfterUpdatingMarketId,
        SalesSetupReader.getShowLocationInZipCodeScreen(salesSetupInfo)
      );
      await this.getAndSetZipCodeDetailsInDeal({
        zipCode,
        address: alwaysUseDealerZipInDeal ? {} : address,
        collectTax: collectTaxRetail,
      });
      await this.getAndSetMarketIdForPrimaryCustomerZip();
    }
  };

  getAndSetMarketIdForPrimaryCustomerZip = async () => {
    const { CommonActions, deal, DeskingActions, salesSetupInfo } = this.props;
    const buyer = DealReader.getBuyer(deal) || EMPTY_OBJECT;
    const defaultDealerZip = SalesSetupReader.getDealerZipCode(salesSetupInfo);
    const buyerZipCode = CustomerReader.getCurrentZIPCodeOfBuyerInDeal(buyer) || defaultDealerZip;
    const leaseMarketId = DealReader.getMarketId(deal, leaseTaxAndZipCodeType);
    const retailMarketId = DealReader.getMarketId(deal, retailTaxAndZipCodeType);
    const leaseZipCode = DealReader.getZipCode(deal, leaseTaxAndZipCodeType);
    const retailZipCode = DealReader.getZipCode(deal, retailTaxAndZipCodeType);
    const isCalcEngineEnabled = CalcEngineProperties.showCalcEngineView();

    let marketId;

    if (leaseZipCode === buyerZipCode && leaseMarketId) {
      marketId = leaseMarketId;
    } else if (retailZipCode === buyerZipCode && retailMarketId) {
      marketId = retailMarketId;
    } else if (buyerZipCode) {
      const response = await CommonActions.getTaxRatesFromZIP({ zip: buyerZipCode, isCalcEngineEnabled });
      marketId = _get(response, 'marketId');
    }
    DeskingActions.setPrimaryCustomerInfo({ key: 'marketId', value: marketId });
    return marketId;
  };

  getAndSetZipCodeDetailsInDeal = async ({ zipCode, type, address, collectTax }) => {
    const { CommonActions, deal, deskingpaymentDetails, salesSetupInfo } = this.props;

    const buyer = DealReader.getBuyer(deal) || EMPTY_OBJECT;
    const columnTaxAndZipCodeType = ColumnDataReader.getColumnTaxAndZipCodeType(
      ColumnDataReader.getColumnTaxAndZipCodeType(deskingpaymentDetails)
    );
    const currentZipAddress = _get(
      DealReader.getTaxAndZipCodeDetails(deal, type || columnTaxAndZipCodeType),
      'address'
    );
    const addressInfo = !address ? currentZipAddress : address;
    const isCalcEngineEnabled = CalcEngineProperties.showCalcEngineView();

    const response = await CommonActions.getTaxRatesFromZIP({
      zip: zipCode,
      addressInfo: {
        address: _get(addressInfo, 'address1'),
        latitude: _get(addressInfo, 'latitudeAndLongitude.latitude'),
        longitude: _get(addressInfo, 'latitudeAndLongitude.longitude'),
      },
      pmtType: type,
      salesSetupInfo,
      deal,
      isCalcEngineEnabled,
    });
    const dealerZipCode = SalesSetupReader.getDealerZipCode(salesSetupInfo);
    const defaultCounty = SalesSetupReader.getDefaultCounty(salesSetupInfo);
    const zipTaxRates = _get(response, 'zipTaxRates') || EMPTY_ARRAY;
    const marketId = _get(response, 'marketId');
    const formattedData = DeskingSubHeaderReader.getTaxRateDataBasedOnCounty(
      zipTaxRates,
      marketId,
      defaultCounty,
      deal,
      salesSetupInfo,
      type,
      CalcEngineProperties.updateByCalcEngine(),
      CalcEngineProperties.updateByGalaxyEngine()
    );
    const errorMsg = 'Please enter valid zip code';
    let defaultTaxData = _find(formattedData, ({ defaultCounty: isDefaultCounty }) => !!isDefaultCounty);

    if (_isNil(marketId)) {
      toaster('error', type ? `${errorMsg} for ${type}` : errorMsg);
    }

    if (formattedData.length > 0) {
      if (String(dealerZipCode) !== String(zipCode) || _isEmpty(defaultTaxData)) {
        defaultTaxData = formattedData[getTaxRateIndex(formattedData, buyer)];
      }
    }

    await this.setZipCodeDetailsInDeal({
      zipCode,
      selectedZipCodeTaxInfo: { ...defaultTaxData, address: addressInfo },
      type,
      collectTax,
    });
  };

  setZipCodeDetailsInDeal = async ({ zipCode, selectedZipCodeTaxInfo, type, collectTax, replaceData }) => {
    const { DeskingActions, setDefaultStateFeeTaxOptions, updateDefaultFNIs } = this.props;
    const marketId = _get(selectedZipCodeTaxInfo, 'marketId');
    const state = STATE_BY_MARKET_IDS[marketId];

    await DeskingActions.setTaxAndZipCodeDetails({
      value: {
        zipCode,
        state,
        ...selectedZipCodeTaxInfo,
        [ZIP_CODE_MANUALLY_UPDATED_API_KEY]: true,
      },
      replaceData,
      type,
    });
    if (!_isNil(collectTax)) {
      this.setCollectTaxFlagBasedOnSetup(collectTax, type);
    }

    if (!CalcEngineProperties.updateCalculationByBackend()) {
      await setDefaultStateFeeTaxOptions(type); // https://tekion.atlassian.net/browse/DMS-22201
    }
    await updateDefaultFNIs();
  };

  setCollectTaxFlagBasedOnSetup = async (collectTax, type) => {
    const { deal, deskingpaymentDetails } = this.props;
    const columnTaxAndZipCodeType = ColumnDataReader.getColumnTaxAndZipCodeType(
      ColumnDataReader.getColumnTaxAndZipCodeType(deskingpaymentDetails)
    );
    const dealMarketId = DealReader.getMarketId(deal, type || columnTaxAndZipCodeType);
    const { salesSetupInfo, DeskingActions } = this.props;
    const dealerMarketId = SalesSetupReader.getDealerMarketId(salesSetupInfo);
    const isCalifornia = isCaliforniaMarket(dealerMarketId) && isCaliforniaMarket(dealMarketId);
    await DeskingActions.setCollectTaxesFlag({
      collectTax: isCalifornia || collectTax,
      type,
    });
  };

  getMarketID = async () => {
    const { deal } = this.props;
    let shouldSaveDealData = false;
    const leaseMarketId = DealReader.getMarketId(deal, leaseTaxAndZipCodeType);
    const retailMarketId = DealReader.getMarketId(deal, retailTaxAndZipCodeType);
    const leaseZipCode = DealReader.getZipCode(deal, leaseTaxAndZipCodeType);
    const retailZipCode = DealReader.getZipCode(deal, retailTaxAndZipCodeType);
    if (DealReader.getEnableSeparateTaxCalc(deal)) {
      if (_isNil(leaseMarketId)) {
        await this.getAndSetZipCodeDetailsInDeal({
          zipCode: leaseZipCode,
          type: leaseTaxAndZipCodeType,
        });
        shouldSaveDealData = true;
      }
      if (_isNil(retailMarketId)) {
        await this.getAndSetZipCodeDetailsInDeal({
          zipCode: retailZipCode,
          type: retailTaxAndZipCodeType,
        });
        shouldSaveDealData = true;
      }
    } else if (_isNil(leaseMarketId) || _isNil(retailMarketId)) {
      await this.getAndSetZipCodeDetailsInDeal({
        zipCode: retailZipCode || leaseZipCode,
      });
    }

    if (shouldSaveDealData) this.saveDealData();
  };

  setCreditScoreOfBuyerInDeal = async deal => {
    const creditScore = DealReader.getCreditScoreOfBuyer(deal);
    if (!creditScore) {
      const { DeskingActions, salesSetupInfo } = this.props;
      const defaultCreditScore = SalesSetupReader.selectDealerCreditScore(salesSetupInfo);
      DeskingActions.setTaxAndZipCodeDetails({
        key: 'creditScore',
        value: defaultCreditScore,
      });
    }
  };

  updateRecommendationUses = payload => {
    const { DeskingActions } = this.props;
    DeskingActions.patchDeal(payload);
  };

  makeRecommendedVehiclePrimary = async (recommendation, filters) => {
    const { deal, DeskingActions, salesSetupInfo, lenders, addDefaultFee, deskingpaymentDetails } = this.props;
    const selectedColumn = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);
    const { annualMiles, downPayment } = _get(filters, 'paymentProfile') || EMPTY_OBJECT;
    const similarityScore = _get(recommendation, 'similarityScore');
    const { vehicle, bestQuote, sellingPrice: desiredValue } = recommendation;
    const { paymentType, term } = bestQuote || EMPTY_OBJECT;
    const vin = _get(vehicle, 'vin');
    const vehicles = _get(deal, 'vehicles') || EMPTY_ARRAY;
    const buyer = DealReader.getBuyer(deal);
    const creditScore = _get(buyer, 'creditScore') || 0;
    const dealerZip = getNumber(SalesSetupReader.getDealerZipCode(salesSetupInfo));
    const zipCodeNTaxRateInfo = DealReader.getTaxAndZipCodeDetails(deal, PAYMENT_TYPE_VS_TAX_TYPE[paymentType]);
    const isCalcEngineEnabled = CalcEngineProperties.showCalcEngineView();
    const customerZIP = getNumber(getModifiedZipForMS(_get(zipCodeNTaxRateInfo, 'zipCode')), isCalcEngineEnabled);
    const taxInCity = _get(zipCodeNTaxRateInfo, 'inCity');

    const payload = await getMarketscanPayloadForRecommendation({
      paymentType,
      creditScore,
      desiredValue,
      term,
      annualMiles,
      downPayment,
      customerZIP,
      dealerZip,
      taxInCity,
      vin,
    });

    const { program } = await DeskingAPI.getMarketScanDataV2(payload, paymentType);

    if (_isEmpty(program)) {
      toaster('error', __('Failed to make vehicle primary'));
    } else {
      DeskingAPI.recommendationVehicleSet(DealReader.getDealNumber(deal), vin);
    }
    const lenderCode = _get(program, 'Lender');
    const lenderId = _get(
      _find(lenders, lender => _get(lender, 'code') === lenderCode),
      'id'
    );
    let columnData = this.getEmptyColumnDataForPaymentOption({ paymentType, value: term }, true, lenderId);
    const fnis = ColumnDataReader.getColumnFNIs(selectedColumn);
    const accessories = ColumnDataReader.getColumnAccessories(selectedColumn);
    _set(columnData, 'accessories', accessories);
    _set(columnData, 'fnIs', fnis);
    columnData = produce(columnData, draft => {
      let { paymentOption } = draft;
      paymentOption = { ...paymentOption, paymentType, value: term };
      draft.paymentOption = paymentOption;
    });

    const downpaymentId = ColumnDataReader.getSelectedDownPaymentID(columnData);
    const paymentsInfoInDeal = DealReader.getDealPaymentsForSelectedVehicle(deal);
    const columnsData = DealReader.getColumnsDataFromDeskingPaymentDetails(paymentsInfoInDeal);
    const deferredPayments = DeskingUtils.getDeferredPayments(columnsData);
    const msRebates = _get(program, 'AppliedRebate') || EMPTY_ARRAY;
    const apr = _get(program, 'SellRate');
    const buyRate = _get(program, 'BuyRate');
    const aprCode = _get(program, 'Code');
    const lenderRateType = _get(program, ['FullDetails', 'APRLMFType']);
    const markUp = formatValueBasedOnRateType(getNumber(apr) - getNumber(buyRate), lenderRateType) || 0;
    const aprData = ColumnDataReader.getAPRData(columnData);

    let targetSellingPrice;
    if (ColumnDataReader.isLoanOrCashOrBalloonPaymentType(columnData)) targetSellingPrice = 'loanSellingPrice';
    if (ColumnDataReader.isLeaseOrOPLeasePaymentType(columnData)) targetSellingPrice = 'leaseSellingPrice';
    const reqVehicleInfo = VehicleReader.getSelectedVehiclePayload(vehicle, targetSellingPrice, desiredValue);
    let updatedVehicles = [reqVehicleInfo, ..._filter(vehicles, vehicleInfo => _get(vehicleInfo, 'vin') !== vin)];
    updatedVehicles = makeFirstVehiclePrimary(updatedVehicles);

    const downPaymentSelectionPayload = {
      id: _get(columnData, 'id'),
      downPaymentValue: ColumnDataReader.getSelectedDownPayment(columnData),
      downPaymentId: ColumnDataReader.getSelectedDownPaymentID(columnData),
    };

    const downPaymentPayload = { id: downpaymentId, downPayment };

    const deferredPaymentsPayload = {
      deferredPayment: DeskingUtils.modifyDeferredPaymentsForNewDownPayment(deferredPayments, downPayment),
      downpaymentId,
    };

    const aprPayload = {
      id: _get(columnData, 'id'),
      aprData: {
        ...aprData,
        buyRate: formatValueBasedOnRateType(buyRate, lenderRateType),
        apr: formatValueBasedOnRateType(apr, lenderRateType),
        markUp,
        aprCode,
        lenderRateType,
        buyRateOverridden: true,
        markUpOverridden: markUp > 0,
        reserveMethod: RESERVE_METHOD.MARKUP_BASED,
      },
    };

    const rebatesPayload = {
      rebates: _compact(
        _map(msRebates, msRebate => {
          if (getNumber(_get(msRebate, 'Value')) !== 0) {
            return DeskingUtils.mapRebate({
              ...msRebate,
              rebateType: REBATE_TYPES.REBATE,
              applicablePaymentTypes: [REBATE_PAYMENT_TYPE_CODES.ALL_PAYMENT_TYPES],
            });
          }
        })
      ),
      selectedColumnId: _get(columnData, 'id'),
    };

    await DeskingActions.addNewColumn({ columnData });
    await addDefaultFee();
    await DeskingActions.setSelectedDownPayment(downPaymentSelectionPayload);
    await DeskingActions.onDownPaymentChange(downPaymentPayload);
    await DeskingActions.setDeferredPaymentDetails(deferredPaymentsPayload);
    await DeskingActions.changeAPR(aprPayload);
    await DeskingActions.addRebatesInSelectedColumn(rebatesPayload);
    await this.onVehiclesUpdated(updatedVehicles, false);
    this.updateRecommendationUses({
      [RECOMMENDATION_USES_KEYS.SIMILARITY_SCORE]: similarityScore,
      [RECOMMENDATION_USES_KEYS.RECOMMENDATION_USED]: true,
    });
  };

  onVehiclesUpdated = async (vehicles, updateVehicleInventoryIfIdPresent = true, unblockScreenAllowed = true) => {
    const { blockScreen, unBlockScreen, deal: oldDeal } = this.props;
    if (_isFunction(blockScreen)) blockScreen(); // Blocking entire UI, so that user wont be able to navigate to other screens
    const { isInventorySaveSuccess, isPrimaryVehicleChanged, isStockTypeChanged } =
      await this.setUpdatedVehiclesInStore(vehicles, updateVehicleInventoryIfIdPresent);
    await this.updatePaymentsOnVehicleChange({ oldDeal, vehicles });
    await this.executeEventsPostVehicleUpdate({ isInventorySaveSuccess, isPrimaryVehicleChanged, isStockTypeChanged });
    if (unblockScreenAllowed && _isFunction(unBlockScreen)) unBlockScreen();
  };

  refreshStockIdsForAllTradeIns = async () => {
    const { deal, DeskingActions, onAction } = this.props;
    await onAction({
      type: ACTION_TYPES.REFRESH_STOCK_IDS,
      DeskingActions,
      deal,
    });
  };

  setDefaultGeneralTaxAndFees = () => {
    const { getfeeAndTaxConfigForGeneralSetting, DeskingActions } = this.props;
    const generalStateFeeTaxOptionsLease = getfeeAndTaxConfigForGeneralSetting(leaseTaxAndZipCodeType);
    const generalStateFeeTaxOptionsRetail = getfeeAndTaxConfigForGeneralSetting(retailTaxAndZipCodeType);
    DeskingActions.setStateFeeTaxOptions({
      options: generalStateFeeTaxOptionsLease,
      type: leaseTaxAndZipCodeType,
    });
    DeskingActions.setStateFeeTaxOptions({
      options: generalStateFeeTaxOptionsRetail,
      type: retailTaxAndZipCodeType,
    });
  };

  setUpdatedVehiclesInStore = async (vehicles, updateVehicleInventoryIfIdPresent = true) => {
    const { deal: previousDeal, addDefaultCostAdjustments, DeskingActions } = this.props;

    const updatedDeal = { ...previousDeal, vehicles };
    await DeskingActions.setDeal(updatedDeal);
    const dealType = DealReader.getDealType(previousDeal);
    const tradeIns = _get(previousDeal, 'tradeIns');

    if (dealType === DEAL_TYPES.REFINANCING && _isEmpty(tradeIns)) {
      await DeskingActions.setTradeInDeal(DealReader.getTradeInDataForRefinanceDeal(vehicles));
    }

    const { deal } = this.props;
    new CalcEngineProperties({ deal });
    const primaryVehicle = DealReader.getPrimaryVehicle(deal);
    const primaryVehicleId = DealReader.getPrimaryVehicleId(deal);
    let isInventorySaveSuccess;
    const shouldUpdateInVehicleInventory = primaryVehicleId && updateVehicleInventoryIfIdPresent;
    if (shouldUpdateInVehicleInventory)
      isInventorySaveSuccess = await this.savePrimaryVehicleInInventory(primaryVehicle);
    const changesInDeal = getChangesInDeal(previousDeal, deal);
    const { isStockTypeChanged, isSubStockTypeChanged } = changesInDeal;
    const isPrimaryVehicleChanged = this.isPrimaryVehicleChanged();
    const isPrimaryVehicleChangedInMVD = getIsPrimaryVehicleChangedInMVD(changesInDeal, deal);
    if (!shouldUpdateInVehicleInventory || isInventorySaveSuccess) {
      await this.getFullDetailsOfCustomersAndVehiclesOfDeal(deal);
      await this.setRequiredInitialDataForVehicles(deal, {
        setQualifiedForNewVehiclePrograms: isPrimaryVehicleChanged || isSubStockTypeChanged,
      });
      if (isPrimaryVehicleChangedInMVD || isPrimaryVehicleChanged || isStockTypeChanged || isSubStockTypeChanged) {
        await this.refreshStockIdsForAllTradeIns();
        await addDefaultCostAdjustments();
        await this.setDefaultGeneralTaxAndFees();
      }
    }
    if (resetFees(changesInDeal)) {
      await this.updateStateFeeTargetingAndSaveDealData(false);
    }
    return {
      isInventorySaveSuccess: !shouldUpdateInVehicleInventory || isInventorySaveSuccess,
      isPrimaryVehicleChanged,
      isStockTypeChanged,
    };
  };

  savePrimaryVehicleInInventory = async primaryVehicle => {
    const { vehiclesInfo, CommonActions } = this.props;
    const vehicleDetails = _get(vehiclesInfo, primaryVehicle.id);
    if (!_isEmpty(vehicleDetails)) {
      const updatedVehicleFieldValues = _pick(
        primaryVehicle,
        _values(KEYS_TO_BE_UPDATED_FOR_INVENTORY_VEHICLE_FROM_DEALS)
      );

      const updatedVehicleDetails = prioritizeVIOverDealPropertiesForVehicles(
        vehicleDetails,
        updatedVehicleFieldValues
      );

      const response = await CommonActions.updateVehicleDetailsInAllVehicles({
        vehicleId: primaryVehicle.id,
        vehicleDetails: updatedVehicleDetails,
      });
      return response;
    }
    toaster('error', __('Vehicle Info not found.'));
  };

  getDefaultZipAndCollectTaxConfig = () => {
    const { salesSetupInfo, deal } = this.props;
    const customerMarketId = CustomerReader.getMarketId(DealReader.getBuyer(deal));
    const isSeparateTaxEnabled = DealReader.getEnableSeparateTaxCalc(deal);
    const isInStateDealForOtherConfigs = DeskingUtils.getIsInStateDeal(salesSetupInfo, customerMarketId);
    if (isSeparateTaxEnabled) {
      const { zipCode: zipCodeLease, collectTax: collectTaxLease } = SalesSetupReader.selectDefaultStateTaxOptions(
        salesSetupInfo,
        isInStateDealForOtherConfigs,
        leaseTaxAndZipCodeType,
        isSeparateTaxEnabled
      );
      const { zipCode: zipCodeRetail, collectTax: collectTaxRetail } = SalesSetupReader.selectDefaultStateTaxOptions(
        salesSetupInfo,
        isInStateDealForOtherConfigs,
        retailTaxAndZipCodeType,
        isSeparateTaxEnabled
      );
      return {
        alwaysUseDealerZipInDealLease: zipCodeLease === DEFAULT_ZIP_CODE.DEALER_ZIP,
        alwaysUseDealerZipInDealRetail: zipCodeRetail === DEFAULT_ZIP_CODE.DEALER_ZIP,
        collectTaxLease,
        collectTaxRetail,
      };
    }
    const { zipCode, collectTax: collectTaxRetail } = SalesSetupReader.selectDefaultStateTaxOptions(
      salesSetupInfo,
      isInStateDealForOtherConfigs,
      retailTaxAndZipCodeType,
      isSeparateTaxEnabled
    );
    return {
      alwaysUseDealerZipInDeal: zipCode === DEFAULT_ZIP_CODE.DEALER_ZIP,
      collectTaxRetail,
    };
  };

  setDealZipCodeIfBuyerZipCodeChanged = async (oldDeal, newDeal) => {
    const { salesSetupInfo } = this.props;
    let shouldCallMS = false;
    const oldBuyer = DealReader.getBuyer(oldDeal);
    const newBuyer = DealReader.getBuyer(newDeal);

    const dealerZip = SalesSetupReader.getDealerZipCode(salesSetupInfo);
    const buyerPreviousZipCode = CustomerReader.getCurrentZIPCodeOfBuyerInDeal(oldBuyer);
    const buyerCurrentZipCode = CustomerReader.getCurrentZIPCodeOfBuyerInDeal(newBuyer);
    const isSeparateTaxEnabled = DealReader.getEnableSeparateTaxCalc(newDeal);
    const isCustomerZipChanged = _toString(buyerPreviousZipCode) !== _toString(buyerCurrentZipCode);

    if (isCustomerZipChanged) {
      await this.getAndSetMarketIdForPrimaryCustomerZip();
    }
    const { deal } = this.props; // will have update marketid

    const addressChanged = DeskingUtils.isAddressChanged(
      DealReader.getBuyer(oldDeal),
      DealReader.getBuyer(deal),
      SalesSetupReader.getShowLocationInZipCodeScreen(salesSetupInfo)
    );

    if (!isCustomerZipChanged && !addressChanged) return;

    shouldCallMS = true;
    if (isSeparateTaxEnabled) {
      const { alwaysUseDealerZipInDealLease, alwaysUseDealerZipInDealRetail, collectTaxLease, collectTaxRetail } =
        this.getDefaultZipAndCollectTaxConfig();
      const address = DeskingUtils.getAddressForZipCodeDetailsFromBuyer(
        DealReader.getBuyer(deal),
        SalesSetupReader.getShowLocationInZipCodeScreen(salesSetupInfo)
      );

      await this.getAndSetZipCodeDetailsInDeal({
        zipCode: !alwaysUseDealerZipInDealLease && buyerCurrentZipCode ? buyerCurrentZipCode : dealerZip,
        type: leaseTaxAndZipCodeType,
        address: alwaysUseDealerZipInDealLease ? {} : address,
        collectTax: collectTaxLease,
      });
      await this.getAndSetZipCodeDetailsInDeal({
        zipCode: !alwaysUseDealerZipInDealRetail && buyerCurrentZipCode ? buyerCurrentZipCode : dealerZip,
        type: retailTaxAndZipCodeType,
        address: alwaysUseDealerZipInDealRetail ? {} : address,
        collectTax: collectTaxRetail,
      });
    } else {
      const { alwaysUseDealerZipInDeal, collectTaxRetail } = this.getDefaultZipAndCollectTaxConfig();
      const address = DeskingUtils.getAddressForZipCodeDetailsFromBuyer(
        DealReader.getBuyer(deal),
        SalesSetupReader.getShowLocationInZipCodeScreen(salesSetupInfo)
      );

      await this.getAndSetZipCodeDetailsInDeal({
        zipCode: !alwaysUseDealerZipInDeal && buyerCurrentZipCode ? buyerCurrentZipCode : dealerZip,
        address: alwaysUseDealerZipInDeal ? {} : address,
        collectTax: collectTaxRetail,
      });
    }

    return shouldCallMS;
  };

  // updateMSRPInVI = () => {
  //   const { deal, vehiclesInfo, CommonActions, salesSetupInfo, onAction } = this.props;

  //   onAction({
  //     type: ACTION_TYPES.UPDATE_MSRP_IN_VI,
  //     deal,
  //     vehiclesInfo,
  //     CommonActions,
  //     salesSetupInfo,
  //     onAction,
  //     oldDeal: this.oldDeal,
  //   });
  // };

  saveDealAndRefreshMarketScan = async vehicleId => {
    const { deskingpaymentDetails } = this.props;

    if (!CalcEngineProperties.updateCalculationByBackend()) {
      await this.getUpdatedRebatesForColumn(vehicleId);
      await this.saveDealData();
    }

    if (DealerPropertyHelper.isCreateDealWithNewAPIEnabled()) {
      const columnIds = getAllCColumnsIDs(deskingpaymentDetails);
      await this.updateDeskingForColumnIds(columnIds);
      return;
    }
    if (CalcEngineProperties.updateCalculationByBackend()) {
      await this.syncPaymentDetails();
      return;
    }

    this.getMarketScanData();
    // this.updateMSRPInVI();
  };

  setEMIAmountAndTaxDetailsForCategory3DealTypes = async () => {
    const { DeskingActions, deskingpaymentDetails, deal, salesSetupInfo, onAction } = this.props;
    await onAction({
      type: ACTION_TYPES.SET_EMI_AMOUNT_AND_TAX_FOR_CAT_3_DEAL_TYPES,
      DeskingActions,
      deskingpaymentDetails,
      deal,
      salesSetupInfo,
    });
  };

  saveSellingPrice = async (vehicleId, updatedParams, includeDefaultPayload = true) => {
    const { deal, DeskingActions } = this.props;

    if (DeskingUtils.isCategory3Deal(deal) && !isInchcape() && !CalcEngineProperties.updateByGalaxyEngine()) {
      await this.setEMIAmountAndTaxDetailsForCategory3DealTypes();
      await this.savePaymentsDataForSelectedVehicle();
    }
    if (DynamicPropertyHelper.isRestrictCRMEventFlowEnabled()) {
      await DeskingActions.setDoNotSendCrmUpdateEventFlag(true);
    }
    if (CalcEngineProperties.updateCalculationByBackend()) {
      // this.updateMSRPInVI();
      await this.updateDealItemPaymentDetails(updatedParams, includeDefaultPayload);
    } else {
      this.saveDealAndRefreshMarketScan(vehicleId);
      // this.updateMSRPInVI();
    }
  };

  onDownPaymentSelect = async (selectedDownPaymentDetails, selectedColumnData) => {
    const {
      DeskingActions,
      deskingpaymentDetails = EMPTY_ARRAY,
      deal,
      unBlockScreen,
      voidContractsOnColumnChange = _noop,
      handleCustomerApprovalOnSelectedColumnChange,
    } = this.props;
    const isColumnSelected = ColumnDataReader.isColumnSelected(selectedColumnData);
    const previousSelectedColumn = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);
    if (!isColumnSelected) {
      const shouldChangeColumn = await voidContractsOnColumnChange(previousSelectedColumn, selectedColumnData);
      if (!shouldChangeColumn) {
        toaster(
          TOASTER_STATUS.ERROR,
          __(
            'The payment terms cannot be altered because the cancellation of the following F&I products was unsuccessful due to technical issues'
          )
        );
        return;
      }
    }

    const { payload, columnData } = DeskingUtils.getDefaultDownpaymentToSelect(deskingpaymentDetails, deal);
    const dealVehicleId = DealReader.getPrimaryVehicleDealVehicleId(deal);
    const cd = selectedColumnData || columnData;
    const { id } = cd || EMPTY_OBJECT;
    const updatedSelectedDownpayment = selectedDownPaymentDetails || payload;
    await DeskingActions.setSelectedDownPayment(updatedSelectedDownpayment);
    await GlobalWarning.showWarnings();

    if (!CalcEngineProperties.updateByGalaxyEngine()) {
      await DeskingActions.addRebatesInSelectedColumn({
        rebates: DeskingUtils.getColumnRebatesWithModifiedFirstWaiverAmt(cd, updatedSelectedDownpayment?.downPaymentId),
        selectedColumnId: id,
      });
    }

    if (cd.dealVehicleId !== dealVehicleId) {
      const unblockScreenAllowed = false;
      const updatedVehicles = makeVehiclePrimaryByDealVehicleId(deal.vehicles, cd.dealVehicleId);
      await this.onVehiclesUpdated(updatedVehicles, false, unblockScreenAllowed);
    }

    if (!_isEmpty(getColumnCreditLifeProducts(columnData))) {
      await this.getMarketScanData({ columnIds: [id] });
    }

    await this.savePaymentsDataForSelectedVehicle();
    if (isInchcape()) this.syncPaymentDetails();
    if (!isColumnSelected) {
      if (DealerPropertyHelper.isExternalDmsEnabled()) {
        DeskingActions.fetchDealSyncMappingWarning(_get(deal, 'dealNumber'));
      }
      GlobalWarning.updateBEWarnings([BACKEND_GLOBAL_WARNINGS_ACTIONS.DEAL_PUSH_WARNINGS]);
    }
    // if (DealerPropertyHelper.isCustomerApprovalsEnabled()) {
    //   await handleCustomerApprovalOnSelectedColumnChange(previousSelectedColumn, selectedColumnData);
    //   await GlobalWarning.showWarnings();
    // }
    if (_isFunction(unBlockScreen)) unBlockScreen();
    CalculationUpdateNotification.compareAndNotifyChange({
      dealId: _get(deal, 'id'),
    });
  };

  setDefaultFeeFniAccInStoreForAllCols = async (addedVehicleIds, shouldSetDefCostAd) => {
    const { addDefaultFee, addDefaultAccessories, addDefaultCostAdjustments, deskingpaymentDetails, DeskingActions } =
      this.props;
    const columnIds = deskingpaymentDetails.map(({ id }) => id);
    const fnIs = ColumnDataReader.getColumnFNIs(
      deskingpaymentDetails.find(column => !_isEmpty(ColumnDataReader.getColumnFNIs(column)))
    );

    await addDefaultFee(EMPTY_OBJECT, true);
    await addDefaultAccessories({ columnIds, keepManuallyUpdatedAccessories: true });
    if (shouldSetDefCostAd) {
      await addDefaultCostAdjustments();
    }
    if (!_isEmpty(addedVehicleIds) && !_isEmpty(fnIs)) {
      const fniColIds = DeskingUtils.getColIdsForNewlyAddedVeh(deskingpaymentDetails, addedVehicleIds);

      if (!_isEmpty(fniColIds)) {
        await DeskingActions.setFNIsInColIds({ columnIds: fniColIds, fnIs });
      }
    }
  };

  updateDeskingPaymentsDataForSelectedVehiclesGalaxy = async (
    oldDeal,
    addedVehicleIds,
    removedVehicleIds,
    isUseUpdatedIds = false
  ) => {
    const { DeskingActions, deskingpaymentDetails, downPayments, lenders, salesSetupInfo } = this.props;
    await this.getMarketScanVehicleIdForVehicles(); // Get and set vehicle variant and vehicle options
    const { deal } = this.props;
    const newDeskingPaymentDetails = StartDealHelpers.getUpdateDeskingPaymentDetailsOnVehicleUpdate({
      deal,
      oldDeal,
      deskingpaymentDetails,
      addedVehicleIds,
      removedVehicleIds,
      downPayments,
      lenders,
      salesSetupInfo,
      isUseUpdatedIds,
    });
    await DeskingActions.setColumnsData(newDeskingPaymentDetails);
    await this.syncPaymentDetails();
  };

  updateDeskingPaymentsDataForSelectedVehicles = async (
    oldDeal,
    addedVehicleIds,
    removedVehicleIds,
    isUseUpdatedIds = false
  ) => {
    const { DeskingActions, deal, deskingpaymentDetails, downPayments, lenders, salesSetupInfo, unBlockScreen } =
      this.props;
    if (CalcEngineProperties.updateByGalaxyEngine()) {
      await this.updateDeskingPaymentsDataForSelectedVehiclesGalaxy(
        oldDeal,
        addedVehicleIds,
        removedVehicleIds,
        isUseUpdatedIds
      );
      return;
    }

    const newDeskingPaymentDetails = StartDealHelpers.getUpdateDeskingPaymentDetailsOnVehicleUpdate({
      deal,
      oldDeal,
      deskingpaymentDetails,
      addedVehicleIds,
      removedVehicleIds,
      downPayments,
      lenders,
      salesSetupInfo,
      isUseUpdatedIds,
    });
    await this.getMarketScanVehicleIdForVehicles();
    await DeskingActions.setColumnsData(newDeskingPaymentDetails);
    if (_isFunction(unBlockScreen)) {
      unBlockScreen();
    }
    if (CalcEngineProperties.updateCalculationByBackend()) {
      await this.syncPaymentDetails();
      return;
    }
    await this.setDefaultFeeFniAccInStoreForAllCols(addedVehicleIds);
    await this.getMarketScanData();
  };

  saveDealDataFromStoreAndCallMS = async oldDeal => {
    const {
      DeskingActions,
      addDefaultFee,
      salesSetupInfo,
      deskingpaymentDetails,
      addDefaultAccessories,
      blockScreen,
      unBlockScreen,
      lenders,
    } = this.props;
    if (_isFunction(blockScreen)) blockScreen();
    await DeskingActions.setMscanVehicleIdFecthed(false);
    if (CalcEngineProperties.updateByGalaxyEngine()) {
      const { deal: currentDeal } = this.props;
      await this.saveDealVehicleIdInDealIfNotPresent(currentDeal);
    } else {
      const apiResponse = await this.saveDealData();
      const { response, error } = apiResponse || {};
      if (error) {
        return;
      }
      if (!_isEmpty(response)) await DeskingActions.setDeal(response);
    }
    if (!CalcEngineProperties.updateCalculationByBackend()) {
      await this.getMarketScanVehicleIdForVehicles({ doNotSaveDealData: true });
    }

    const { deal } = this.props;
    const isVehicleProfileEnabled = SalesDealerPropertyHelper.isVehicleProfileEnabled();
    const changesInDeal = getChangesInDeal(oldDeal, deal);
    if ((changesInDeal?.isVINChanged || changesInDeal?.isVehicleDealVehicleIdChanged) && isVehicleProfileEnabled) {
      await DeskingActions.setIsVehicleTargetingValue({ isVehicleTargeting: true });
    }
    this.setApplicationTitle(DealReader.getPrimaryVehicle(deal), DealReader.getBuyer(deal));

    if (!DeskingUtils.isCategory1Deal(deal)) {
      await this.refreshValuesForCategory2And3Deals();
      await this.savePaymentsDataForSelectedVehicle();
      await this.saveDealData();
      DeskingActions.setDealDirtyStatus(false);
    }

    const isMultiVehicleDeskingEnabledV2 = DealReader.isMultiVehicleDeskingEnabledV2(deal);
    if (isMultiVehicleDeskingEnabledV2) {
      const { addedVehicleIds, removedVehicleIds } = StartDealHelpers.getNewAddedVehicles(oldDeal, deal);
      if (addedVehicleIds?.length > 0 || removedVehicleIds?.length > 0 || CalcEngineProperties.updateByGalaxyEngine()) {
        await this.updateDeskingPaymentsDataForSelectedVehicles(oldDeal, addedVehicleIds, removedVehicleIds, true);
      } else {
        const selectedColumn = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);
        const primaryVehicleDealVehicleId = DealReader.getPrimaryVehicleDealVehicleId(deal);
        if (selectedColumn.dealVehicleId !== primaryVehicleDealVehicleId) {
          await this.setDefaultFeeFniAccInStoreForAllCols(addedVehicleIds, true);
          this.onDownPaymentSelect();
        }
      }
      if (isVehicleProfileEnabled) {
        await DeskingActions.setIsVehicleTargetingValue({ isVehicleTargeting: false });
      }

      return;
    }

    const shouldCallMS = _some(changesInDeal, isChanged => isChanged);
    const isMultiVehicleDesking = SalesSetupReader.getIsMultiVehicleDesking(salesSetupInfo);

    if (changesInDeal?.isNewMakeAdded) {
      DeskingActions.setMakeAliases(deal);
    }

    if ((changesInDeal?.isVINChanged || changesInDeal?.isVehicleDealVehicleIdChanged) && isMultiVehicleDesking) {
      const dealVehicleId = DealReader.getPrimaryVehicleDealVehicleId(deal);
      const payload = {
        dealNumber: DealReader.getDealNumber(deal),
        dealVehicleId,
      };
      if (CalcEngineProperties.updateByGalaxyEngine()) {
        const apiResponse = await this.saveDealData();
        const { response, error } = apiResponse || {};
        if (error) {
          return;
        }
        if (!_isEmpty(response)) await DeskingActions.setDeal(response);
      }
      const { response: newVehiclePaymentdeatils } = await DeskingAPI.getVehiclePaymentInfo(payload);
      if (!_isNil(newVehiclePaymentdeatils)) {
        const dealVehiclePaymentInfos = [
          _pick(newVehiclePaymentdeatils, ['dealPayments', 'dealVehicleId', 'rebateProvider']),
        ];
        await DeskingActions.setDeal({ dealVehiclePaymentInfos });
        await this.initColumnsDataFromDealV2();
        await this.getMarketScanData();
        toaster(TOASTER_STATUS.SUCCESS, MESSAGES.DEAL_UPDATION_SUCCESS);
        if (isVehicleProfileEnabled) {
          await DeskingActions.setIsVehicleTargetingValue({ isVehicleTargeting: false });
        }
        return;
      }
    }

    if (_get(changesInDeal, 'isVehicleMSRPChanged') || _get(changesInDeal, 'isVehicleInvoicePriceChanged')) {
      this.getUpdatedRebatesForColumn();
    }

    if (!CalcEngineProperties.updateCalculationByBackend() && shouldCallMS) {
      const { isVINChanged, isBuyerCurrentZipChanged, isVehicleDealVehicleIdChanged, isNewTyreCountChanged } =
        changesInDeal;
      const keepManuallyAddedFees = true;

      if (resetFees(changesInDeal)) {
        // reset tyre fee on new tyre count change irrespective of manually updated flag
        if (isNewTyreCountChanged) {
          const columnCalculateFees = {};
          _forEach(deskingpaymentDetails, columnData => {
            const { id } = columnData;
            const columnFees = ColumnDataReader.getColumnFees(columnData);
            columnCalculateFees[id] = DeskingUtils.getCalculatedFeeValues(columnFees, deal);
          });
          await DeskingActions.feesSave({ columnFees: columnCalculateFees });
        }

        await addDefaultFee(EMPTY_OBJECT, keepManuallyAddedFees);
      }

      if (isVehicleDealVehicleIdChanged) {
        const primaryVehicleDealVehicleId = DealReader.getPrimaryVehicleDealVehicleId(deal);
        await DeskingActions.setDeskingPaymentDetailsDelvehicleId({
          primaryVehicleDealVehicleId,
        });
      }

      if (isVINChanged || isBuyerCurrentZipChanged || isVehicleDealVehicleIdChanged) {
        await this.updateDealRebates({
          removeManualRebates: DeskingUtils.shouldRemoveCustomRebates(
            VehicleReader.getVehicleType(DealReader.getPrimaryVehicle(oldDeal)),
            VehicleReader.getVehicleType(DealReader.getPrimaryVehicle(deal))
          ),
        });
        if (isVINChanged || isVehicleDealVehicleIdChanged) {
          const keepManuallyUpdatedAccessories = true;
          await addDefaultAccessories({
            columnIds: DeskingUtils.getColIdsToSetDefValues(deal, deskingpaymentDetails),
            keepManuallyUpdatedAccessories,
          });
          const setupData = { lenders, salesSetupInfo };
          const dealData = { vehicleType: DealReader.getPrimaryVehicleType(deal) };

          const newModifiedTermPaymentData = StartDealHelpers.getModifiedColumnsAfterVehicleSwitch(
            deskingpaymentDetails,
            setupData,
            dealData,
            lenders
          );

          await DeskingActions.resetKeysTermpaymentDetails({ newModifiedTermPaymentData });
        }
      }
      await this.getMarketScanData();
      if (_isFunction(unBlockScreen) && !this.selectVehicleRef && !this.updateVehicleVariantRef) {
        unBlockScreen();
      }
    }
    if (isVehicleProfileEnabled) {
      await DeskingActions.setIsVehicleTargetingValue({ isVehicleTargeting: false });
    }
    toaster(TOASTER_STATUS.SUCCESS, MESSAGES.DEAL_UPDATION_SUCCESS);
  };

  updateDealData = async (dealNumber, payload) => {
    const { deal, DeskingActions } = this.props;
    const dealWithoutPaymentInfo = DealReader.getDealWithoutPaymentInfo(deal);
    const dealUpdatePayload = { ...dealWithoutPaymentInfo, ...payload };
    const apiResponse = await DeskingActions.updateDeal(dealNumber, dealUpdatePayload);
    return apiResponse;
  };

  shouldRecalculatePayments = canCallMarketScan =>
    !CalcEngineProperties.updateCalculationByBackend() || canCallMarketScan;

  saveDealVehicleIdInDealIfNotPresent = async updateDealParams => {
    const { vehicles } = updateDealParams;
    const hasVehiclesWithNoDealVehicleId = StartDealHelpers.checkHasVehiclesWithNoDealVehicleIds(vehicles);
    if (hasVehiclesWithNoDealVehicleId) {
      const { DeskingActions } = this.props;
      const includeDefaultPayload = false;
      const params = { previewCall: true, updateStore: false, queryParams: EMPTY_STRING };
      const fetchDealAction = CalcEngineProperties.updateByGalaxyEngine()
        ? DeskingActions.updateDealItemPaymentDetailsGalaxy
        : DeskingActions.updateDealItemPaymentDetails;
      const { response } = await fetchDealAction(updateDealParams, includeDefaultPayload, params);
      const newDeal = response?.deal;
      if (newDeal) {
        const newVehicles = _get(newDeal, 'vehicles');
        await DeskingActions.setNewVehiclesInDeal(newVehicles);
      }
    }
  };

  getUpdatedDealParamsOnVehicleChange = updateDealParams => {
    if (_isEmpty(updateDealParams)) {
      return updateDealParams;
    }
    const { deal } = this.props;
    const newVehicles = _get(deal, 'vehicles');

    const newDealParams = {
      ...updateDealParams,
      vehicles: newVehicles,
    };
    return newDealParams;
  };

  saveVehicleVariantsAndOptionsForGalaxy = async (updateDealParams, isMultiVehicleDeskingEnabledV2) => {
    if (!isMultiVehicleDeskingEnabledV2) {
      await this.saveDealVehicleIdInDealIfNotPresent(updateDealParams);
      await this.getMarketScanVehicleIdForVehicles({ doNotSaveDealData: true });
      const updatedDealParams = this.getUpdatedDealParamsOnVehicleChange(updateDealParams);
      // For MultiVehicleDesking payment details will be saved in updateDeskingPaymentsDataForSelectedVehicles later in this flow
      await this.updateDealItemPaymentDetails(updatedDealParams, _isEmpty(updatedDealParams));
    }
  };

  saveVehicleDetails = async updateDealParams => {
    const { deal } = this.props;
    const isMultiVehicleDeskingEnabledV2 = DealReader.isMultiVehicleDeskingEnabledV2(deal);
    if (CalcEngineProperties.updateByGalaxyEngine()) {
      await this.saveVehicleVariantsAndOptionsForGalaxy(updateDealParams, isMultiVehicleDeskingEnabledV2);
    } else {
      if (!isMultiVehicleDeskingEnabledV2) {
        // For MultiVehicleDesking payment details will be saved in updateDeskingPaymentsDataForSelectedVehicles later in this flow
        await this.updateDealItemPaymentDetails(updateDealParams, _isEmpty(updateDealParams));
      } else {
        await this.saveDealVehicleIdInDealIfNotPresent(updateDealParams);
      }
      await this.getMarketScanVehicleIdForVehicles({ doNotSaveDealData: true, updateLGMData: true });
    }
    await this.getAdditionalOEMVINDecodingInfo({ doNotSaveDealData: isMultiVehicleDeskingEnabledV2 });
  };

  updatePaymentsOnVehicleChange = async ({ oldDeal, vehicles, updateDealParams = {} }) => {
    const {
      unBlockScreen,
      DeskingActions,
      deal: newDeal,
      CommonActions,
      canCallMarketScan,
      deskingpaymentDetails,
    } = this.props;

    if (DynamicPropertyHelper.isRestrictCRMEventFlowEnabled()) {
      const doNotSendCrmUpdateEventOnVehicleSwitch = isSetDoNotSendCrmUpdateEventOnVehicleSwitch({
        deal: newDeal,
        oldDeal,
        deskingpaymentDetails,
      });
      await DeskingActions.setDoNotSendCrmUpdateEventFlag(doNotSendCrmUpdateEventOnVehicleSwitch);
    }
    if (CalcEngineProperties.updateCalculationByBackend()) {
      if (isVehicleSwitchedFromAutomotiveToRV({ newVehicles: vehicles, oldDeal })) {
        await CommonActions.getSalesSetup(DealReader.getDealCreatedAtSite(newDeal));
        if (this.shouldRecalculatePayments(canCallMarketScan)) {
          await DeskingActions.switchVehicleFromMscanToCalcengine({
            vehicles,
            dealNumber: DealReader.getDealNumber(oldDeal),
          });
        } else {
          await this.updateDealData(DealReader.getDealNumber(oldDeal), { vehicles });
        }

        await DeskingActions.getStateFeeTaxConfigMetadata(DealReader.getPrimaryVehicleCategory(newDeal));
        if (_isFunction(unBlockScreen)) unBlockScreen();
        return;
      }
      await this.saveVehicleDetails(updateDealParams);
    } else if (isVehicleSwitchedFromRVToAutomotive({ newVehicles: vehicles, oldDeal })) {
      await CommonActions.getSalesSetup(DealReader.getDealCreatedAtSite(newDeal));
      await this.updateDealFromCalcEngineToMscan();
      if (_isFunction(unBlockScreen)) unBlockScreen();
      return;
    }
    await this.saveDealDataFromStoreAndCallMS(oldDeal);
  };

  onVehiclesAndCustomersUpdated = async ({ vehicles, customers, additionalPayload }) => {
    const { blockScreen, unBlockScreen, deal: oldDeal, DeskingActions, deskingpaymentDetails } = this.props;

    if (_isFunction(blockScreen)) blockScreen();
    await DeskingActions.setCustomerOnlyInStore(customers);
    const { isInventorySaveSuccess, isPrimaryVehicleChanged, isStockTypeChanged } =
      await this.setUpdatedVehiclesInStore(vehicles, false); // updateVehicleInventoryIfIdPresent needs to be set to false for not saving the vehicle in inventory
    const { deal: newDeal } = this.props;
    const updatedVehicles = newDeal?.vehicles;

    await this.updatePaymentsOnVehicleChange({
      oldDeal,
      updatedVehicles,
      updateDealParams: { vehicles: updatedVehicles, customers, ...additionalPayload },
    });
    await this.executeEventsPostVehicleUpdate({ isInventorySaveSuccess, isPrimaryVehicleChanged, isStockTypeChanged });
    if (_isFunction(unBlockScreen)) unBlockScreen();
    if (isAnyApprovedColumnAdded(deskingpaymentDetails) && shouldShowVehicleSwitchWarnings(oldDeal, newDeal))
      GlobalWarning.showCustomWarnings(LEFT_PANEL_ITEMS.DESKING, VEHICLE_OR_CUSTOMER_SWITCH_ERROR);

    if (DealerPropertyHelper.isAECProgram() && isOEMAccessoriesAdded(oldDeal)) {
      const isVehicleWithSameModelCode = compareModelCodeOfVehilcels(oldDeal, newDeal);
      if (isVehicleWithSameModelCode) {
        toaster(TOASTER_TYPE.INFO, MESSAGES.SWITCH_VEHICLE_WITH_SAME_MODEL_CODE, null, 'Vehicle Switched');
      } else {
        toaster(TOASTER_TYPE.INFO, MESSAGES.SWITCH_VEHICLE_WITH_DIFF_MODEL_CODE, null, 'Vehicle Switched');
      }
    }
  };

  executeEventsPostVehicleUpdate = async ({ isInventorySaveSuccess, isPrimaryVehicleChanged, isStockTypeChanged }) => {
    const { DeskingActions, updateRepairOrderPurchaseOrderAdjustment, blockScreen, unBlockScreen } = this.props;
    if (_isFunction(blockScreen)) blockScreen();
    await DeskingActions.getDealPreferences();
    if (isInventorySaveSuccess) updateRepairOrderPurchaseOrderAdjustment();
    if (isPrimaryVehicleChanged || isStockTypeChanged) {
      DeskingActions.showRecallWarning(true, false, GlobalWarning.showWarnings);
    }
    await this.saveDealData();
    if (_isFunction(unBlockScreen)) unBlockScreen();
  };

  updateDealFromCalcEngineToMscan = async () => {
    const { DeskingActions, deskingpaymentDetails, deal } = this.props;
    const paymentTemplateToUse = DeskingUtils.getPaymentTemplateFromModifiedDeal(deskingpaymentDetails);
    await DeskingActions.setPaymentDetailsInDeal({ paymentDetails: null });
    await DeskingActions.getStateFeeTaxConfigMetadata(DealReader.getPrimaryVehicleCategory(deal));
    this.initColumnsDataFromDeal(paymentTemplateToUse);
  };

  updateDesking = async payload => {
    const { DeskingActions, deal, blockScreen, unBlockScreen } = this.props;
    const dealNumber = DealReader.getDealNumber(deal);
    DeskingActions.setDealDirtyStatus(false);

    if (this.saveInDealWithoutCalculation()) return;
    if (_isFunction(blockScreen)) blockScreen();
    await DeskingActions.updateDesking({ ...payload, dealNumber });
    GlobalWarning.updateBEWarnings([BACKEND_GLOBAL_WARNINGS_ACTIONS.DEAL_PUSH_WARNINGS]);
    this.initColumnsDataFromDealV2();
    if (_isFunction(unBlockScreen)) unBlockScreen();
  };

  updateDeskingForColumnIds = async columnIds => {
    const { DeskingActions, blockScreen, unBlockScreen } = this.props;
    DeskingActions.setDealDirtyStatus(false);
    if (this.saveInDealWithoutCalculation()) return;
    if (_isFunction(blockScreen)) blockScreen();
    await DeskingActions.updateDeskingColumns(columnIds);
    GlobalWarning.updateBEWarnings([BACKEND_GLOBAL_WARNINGS_ACTIONS.DEAL_PUSH_WARNINGS]);
    this.initColumnsDataFromDealV2();
    if (_isFunction(unBlockScreen)) unBlockScreen();
  };

  updateDeskingOnDealTypeChange = async ({ dealType }) => {
    if (this.saveInDealWithoutCalculation()) return;

    const { DeskingActions, deal, blockScreen, unBlockScreen } = this.props;
    const dealNumber = DealReader.getDealNumber(deal);
    if (_isFunction(blockScreen)) blockScreen();
    const currentDealType = DealReader.getDealType(deal);
    const dealBelongsToSameCategory = DeskingUtils.isSameDealCategory(currentDealType, dealType);

    if (dealBelongsToSameCategory) {
      await DeskingActions.setDealType(dealType);
      this.saveDealData();
    } else {
      await DeskingActions.updateDeskingOnDealTypeChange({ dealType }, dealNumber);
      this.initColumnsDataFromDealV2();
    }
    if (_isFunction(unBlockScreen)) unBlockScreen();
  };

  renderReloadLink = ({ toasterId, paymentTemplateToUse }) => (
    <Button
      style={{ float: 'right' }}
      view={Button.VIEW.LINK}
      onClick={() => this.reloadDeal(toasterId, paymentTemplateToUse)}>
      {__('Reload')}
    </Button>
  );

  reloadDeal = async (toasterId, paymentTemplateToUse) => {
    const { unBlockScreen } = this.props;
    dismissToast(toasterId);
    if (CalcEngineProperties.updateCalculationByBackend()) {
      await this.updateDealItemPaymentDetails();
    } else {
      await this.reinitialiseDeal(paymentTemplateToUse);
    }
    if (_isFunction(unBlockScreen)) {
      unBlockScreen();
    }
  };

  initColumnsDataFromDealV2 = async () => {
    const { DeskingActions, deal } = this.props;
    if (_isEmpty(deal)) return;
    const paymentsInfoInDeal = DealReader.getDealPaymentsForAllVehicles(deal);
    const isCalculationByBackendEnabled = CalcEngineProperties.updateCalculationByBackend();
    const downPayments = DealReader.getDownPaymentsFromDeskingPaymentDetails(deal, isCalculationByBackendEnabled);
    const columnsData = DealReader.getColumnsDataFromDeskingPaymentDetails(paymentsInfoInDeal);
    const deferredPayments = DeskingUtils.getDeferredPayments(columnsData);
    await DeskingActions.setDeferredPaymentDetails({ deferredPayments });
    await DeskingActions.setDownPayments(downPayments);
    await DeskingActions.setColumnsData(columnsData);
  };

  initColumnsDataFromDeal = async paymentTemplateToUse => {
    const { deal } = this.props;

    if (_isEmpty(deal)) return;
    const paymentsInfoInDeal = DealReader.getDealPaymentsForAllVehicles(deal);
    const isCalculationByBackendEnabled = CalcEngineProperties.updateCalculationByBackend();
    const isDealCorrupted = this.getIfDealIsCorrupted(deal, paymentsInfoInDeal);
    const migrated = DealReader.migrated(deal);

    if (isDealCorrupted && !migrated) {
      const toasterId = toaster(
        TOASTER_STATUS.WARN,
        __('Payments could not be updated for the selected vehicle. Please click to Reload'),
        { autoClose: false, closeOnClick: false },
        __('Info'),
        () => this.renderReloadLink({ toasterId, paymentTemplateToUse })
      );
      // return;
    }
    if (!_isEmpty(paymentsInfoInDeal)) {
      const columnsData = DealReader.getColumnsDataFromDeskingPaymentDetails(paymentsInfoInDeal);
      const dealFnis = ColumnDataReader.getDealFNIs(columnsData);
      if (isCalculationByBackendEnabled) {
        const productNamesWithRegulation = getRegulatedProductNamesForNotification(dealFnis);
        if (_size(productNamesWithRegulation) >= 1) {
          toaster(
            TOASTER_TYPE.INFO,
            `${_join(productNamesWithRegulation, ', ')} ${
              _size(productNamesWithRegulation) > 1 ? __('have') : __('has')
            } ${__(' been adjusted to the regulated price')}`
          );
        }
      }
      await this.initColumnsDataFromDealV2();
      const { deal: updatedDeal } = this.props;
      const calculationRequired =
        DealerPropertyHelper.isDealLeadSyncV2Enabled() && DealReader.isCalculationRequired(updatedDeal);
      const refreshPayments = DealReader.getRefreshPayments(updatedDeal) || deal?.calculationRequired;
      if (!migrated && (refreshPayments || calculationRequired) && !isCalculationByBackendEnabled) {
        await this.updateDealDetailsOnCrmLeadSyncUpdates();
      }
    } else {
      await this.reinitialiseDeal(paymentTemplateToUse);
    }
  };

  updateDealDetailsOnCrmLeadSyncUpdates = async () => {
    const { deal: updatedDeal, DeskingActions, deskingpaymentDetails } = this.props;
    await DeskingActions.resetRefreshPayments();
    const crmDetailsUpdated = getCrmDetailsUpdated(updatedDeal);

    await _reduce(
      Object.values(CRM_LEAD_SYNC_UPDATES),
      async (promise, target) => {
        await promise; // Wait for the previous promise to complete
        const isRunTargetting = _includes(crmDetailsUpdated, target) || false;
        if (isRunTargetting) {
          const funToExe = this.tagettingCallBacks[target] || _noop;
          const argesTopass = getArgumentToPassForTargetting(target, deskingpaymentDetails);
          await funToExe(...argesTopass);
        }
        return Promise.resolve();
      },
      Promise.resolve()
    );

    await this.getMarketScanData(EMPTY_OBJECT, false);
    toaster('info', 'Deal payments has been modified from CRM.');
    await this.saveDealData();
  };

  handleBuyerZipcodeFromCrmLeadSync = async () => {
    const { updateDefaultFNIs, deal } = this.props;
    await this.setRequiredInitialDataForCustomer(deal);
    await updateDefaultFNIs();
  };

  reinitialiseDeal = async paymentTemplateToUse => {
    const { DeskingActions, deal } = this.props;

    await this.setRequiredInitialDataForVehicles(deal, {
      setQualifiedForNewVehiclePrograms: true,
    });
    await this.setRequiredInitialDataForCustomer(deal);
    await DeskingActions.setDeferredPaymentDetails({
      deferredPayments: EMPTY_OBJECT,
    });

    if (!DeskingUtils.isCategory1Deal(deal)) {
      await this.setInitialValuesForOtherDeals();
      return;
    }
    await this.setInitialDefaultValues(paymentTemplateToUse);
  };

  getIfDealIsCorrupted = (deal, paymentsInfoInDeal) => {
    if (isInchcape() && DealReader.getDealType(deal) === DEAL_TYPES.FNI) return false;
    const columnsData = DealReader.getColumnsDataFromDeskingPaymentDetails(paymentsInfoInDeal);
    const dealVehiclePaymentInfos = _get(deal, 'dealVehiclePaymentInfos') || [];
    const selectedColumn = _find(columnsData, item => item?.selected);
    const dealVehicleId = selectedColumn?.dealVehicleId || _get(dealVehiclePaymentInfos[0], 'dealVehicleId');
    const primaryDealVehicleId = DealReader.getPrimaryVehicleDealVehicleId(deal);
    if (dealVehicleId && primaryDealVehicleId) {
      return dealVehicleId !== primaryDealVehicleId;
    }
    return false;
  };

  setInitialValuesForOtherDeals = async () => {
    const { DeskingActions, deskingpaymentDetails, addDefaultCostAdjustments, blockScreen, unBlockScreen } = this.props;
    let columnData = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);
    if (_isFunction(blockScreen)) blockScreen(null, undefined, MESSAGES.PLEASE_WAIT);

    await addDefaultCostAdjustments();

    await DeskingActions.setDownPayments(DeskingUtils.getDownPaymentsForAllDownPaymentTypes([0]));
    await DeskingActions.removeAllColumns();
    const paymentType = PAYMENT_TYPES.CASH;
    const paymentOption = { paymentType, value: 48 };
    columnData = this.getEmptyColumnDataForPaymentOption(paymentOption, true);
    await DeskingActions.addNewColumn({ columnData });

    await this.refreshValuesForCategory2And3Deals();
    await this.savePaymentsDataForSelectedVehicle();
    await this.saveDealData();
    DeskingActions.setDealDirtyStatus(false);
    if (_isFunction(unBlockScreen)) unBlockScreen();
  };

  refreshValuesForCategory2And3Deals = async () => {
    const { deal, DeskingActions } = this.props;

    if (DeskingUtils.isCategory2Deal(deal)) {
      const { updateEMIAmountOfFNIDeal } = this.props;
      // should not show selling price in selling price tab for f&i only deal
      await DeskingActions.setPrimaryVehicleInfo({
        key: 'pricingDetails.loanSellingPrice',
        value: 0,
      });
      await DeskingActions.setPrimaryVehicleInfo({
        key: 'pricingDetails.leaseSellingPrice',
        value: 0,
      });
      await updateEMIAmountOfFNIDeal();
    }

    if (DeskingUtils.isCategory3Deal(deal)) await this.setEMIAmountAndTaxDetailsForCategory3DealTypes();
  };

  setInitialDefaultValues = async paymentTemplateToUse => {
    const {
      DeskingActions,
      deal,
      salesSetupInfo,
      setWorkingCashConfigInDeal,
      addDefaultFee,
      addDefaultAccessories,
      getDefaultPlanCodeAsync,
      addDefaultCostAdjustments,
    } = this.props;
    const defaultDownPayments = SalesSetupReader.selectDefaultDownpayments(salesSetupInfo);

    const paymentTemplates = paymentTemplateToUse || DealReader.getDefaultPaymentOptions(deal);
    const isMultiVehicleDeskingEnabledV2 = DealReader.isMultiVehicleDeskingEnabledV2(deal);
    const vehicleIdsToUse = isMultiVehicleDeskingEnabledV2
      ? DealReader.getAllDealVehicleIds(deal)
      : [DealReader.getPrimaryVehicleDealVehicleId(deal)];
    const sellingPrice = DealReader.getSellingPriceOfPrimaryVehicle(this.props.deal); // eslint-disable-line
    const dealType = DealReader.getDealType(deal);
    const dealerDownPayments = DealerConfigReader.getDownPayments(defaultDownPayments, sellingPrice, dealType);
    const downPaymentInPaymentTemplate = _get(paymentTemplates, [0, 'downPayment']);
    const downPaymentsToUse = !_isNil(downPaymentInPaymentTemplate)
      ? DeskingUtils.formatDownPaymentsFromPaymentOptions(downPaymentInPaymentTemplate)
      : DeskingUtils.getDownPaymentsForAllDownPaymentTypes(dealerDownPayments);
    await DeskingActions.setDownPayments(downPaymentsToUse);
    const initialColumnData = _flatten(
      _map(vehicleIdsToUse, (vehicleId, vehicleIndex) =>
        paymentTemplates.map((paymentOption, index) =>
          this.getEmptyColumnDataForPaymentOption(paymentOption, !index && !vehicleIndex, null, vehicleId)
        )
      )
    );
    await DeskingActions.setColumnsData(initialColumnData);
    // initialize rebateFilters
    DeskingActions.setRebateFilters(DeskingUtils.getInitialRebateFilters());
    await setWorkingCashConfigInDeal();
    await addDefaultFee();
    await this.updateStateFeeTargetingAndSaveDealData(false);
    await this.addSecurityDepositWaiverReason(initialColumnData);
    // no matter single/multi veh. desking, we need to set def. acc. in all columns
    await this.saveDealData();
    await addDefaultAccessories({
      columnIds: initialColumnData.map(({ id }) => id),
    });
    await this.getMarketScanData(EMPTY_OBJECT, false);

    await addDefaultCostAdjustments();
    await getDefaultPlanCodeAsync(this.processPostFNIPlanCodeFetch);
  };

  processPostFNIPlanCodeFetch = async data => {
    const { addDefaultFNIs, deal, deskingpaymentDetails } = this.props;

    // data will only come from pusher notification event when FNI config have pen similar products
    await addDefaultFNIs({
      columnIds: DeskingUtils.getColIdsToSetDefValues(deal, deskingpaymentDetails),
      defaultPlanCodesFetchId: data?.ratesRequestUniqueId,
    });
    this.saveDealData();
    this.getMarketScanData(EMPTY_OBJECT, false);
  };

  addEventListeners = () => {
    FNIMenuEvent.on(EVENT_TYPES.DEFAULT_PLAN_CODE, this.processPostFNIPlanCodeFetch);
  };

  removeEventListeners = () => {
    FNIMenuEvent.removeListener(EVENT_TYPES.DEFAULT_PLAN_CODE, this.processPostFNIPlanCodeFetch);
  };

  fetchRebates = async ({
    deal,
    salesSetupInfo,
    rebateFilters,
    deskingpaymentDetails,
    getFormattedDateAndTime,
    filters,
    selectedColumnId,
    urlQuery,
  }) => {
    const rebatesPayload = getRebatePayload({
      deal,
      salesSetupInfo,
      rebateFilters,
      filters,
      selectedColumnId,
      deskingpaymentDetails,
      getFormattedDateAndTime,
    });

    const getRebatesAPI = CalcEngineProperties.updateByGalaxyEngine()
      ? DeskingAPI.getRebatesForGalaxy
      : DeskingAPI.getRebates;
    const { response } = (await getRebatesAPI(rebatesPayload, urlQuery)) || EMPTY_OBJECT;
    return formatRebatesResponse(response, deskingpaymentDetails);
  };

  fetchRebatesInchcape = async includeExpired => {
    const { response } = await MiscellaneousSetupAPI.fetchAllItems({
      offset: 0,
      pageSize: 1000,
      searchText: '',
    });
    if (response) {
      const vatDetails = _get(response, 'vatDetails', EMPTY_ARRAY);
      return {
        ...response,
        rebates: addOrRemoveExpiredRebates(vatDetails, includeExpired),
      };
    }
  };

  getRebates = async (filters, selectedColumnId, urlQuery, includeExpired) => {
    const { isUSCalcEngineEnabled } = this.props;
    if (isInchcape()) {
      const response = await this.fetchRebatesInchcape(includeExpired);
      return response;
    }
    if (
      CalcEngineProperties.isRVVehicle() ||
      (isUSCalcEngineEnabled && !CalcEngineProperties.updateByGalaxyEngine()) ||
      isRRG()
    ) {
      return;
    }
    const { deal, salesSetupInfo, rebateFilters, deskingpaymentDetails, getFormattedDateAndTime, vehiclesInfo } =
      this.props;

    const response = await this.fetchRebates({
      deal,
      salesSetupInfo,
      rebateFilters,
      deskingpaymentDetails,
      getFormattedDateAndTime,
      filters,
      selectedColumnId,
      urlQuery,
    });

    if (response) {
      const columnData = selectedColumnId
        ? ColumnDataReader.getColumnDataById(deskingpaymentDetails, selectedColumnId)
        : ColumnDataReader.getSelectedColumn(deskingpaymentDetails);
      const selectedVehicle = _find(
        _get(deal, 'vehicles'),
        vehicle => vehicle.dealVehicleId === columnData?.dealVehicleId
      );
      const { pricingDetails } = StartDealHelpers.getVehicleTypeAndPricingDetails(selectedVehicle, vehiclesInfo);
      const { msrp, invoicePrice, freightInPrice, airTaxPrice } = pricingDetails;
      const rebates = _get(response, 'rebates');
      const rebateProvider = _get(response, 'rebateProvider');

      const formattedRebates = updateRequiredRebatesAmount(
        rebates,
        {
          msrp,
          invoicePrice,
          freightInPrice,
          airTaxPrice,
        },
        rebateProvider
      );

      return {
        rebates: addMissingRebateKeys(formattedRebates),
        rebateProvider,
        filters: _get(response, 'filters'),
        rebateColumnMapping: _get(response, 'rebateColumnMapping'),
      };
    }
    toaster('error', __('Failed To Fetch Rebates'));
  };

  updateDealRebates = async ({ removeManualRebates = false } = EMPTY_OBJECT) => {
    await this.getMarketScanVehicleIdForVehicles();
    const { DeskingActions, deskingpaymentDetails, deal } = this.props;
    if (_isEmpty(getRebatesSuperset({ deskingpaymentDetails }))) return null;
    const { rebates = [], rebateProvider } = (await this.getRebates()) || {};
    const validRebatesInDealStructure = getFilterRebates(deskingpaymentDetails, rebates, removeManualRebates);
    const vehicleIndex = getTargetDealVehiclePaymentDetailsIndex(deal, deskingpaymentDetails);
    await DeskingActions.setRebates({
      rebateInDealStructure: validRebatesInDealStructure,
      rebateProvider,
      vehicleIndex,
    });
    DeskingActions.setRebateFilters({ selectedRegionId: null });
  };

  isPrimaryVehicleChanged = () => {
    const { deal } = this.props;
    const primaryVehicleDealVehicleId = DealReader.getPrimaryVehicleDealVehicleId(deal);
    const paymentInfo = DealReader.getDealPaymentsForDealVehicleId(deal, primaryVehicleDealVehicleId);
    const doesPaymentInfoExistForPrimaryVehicle = !_isEmpty(paymentInfo);
    return !doesPaymentInfoExistForPrimaryVehicle;
  };

  getUpdatedRebatesForColumn = async dealVehicleId => {
    const { deskingpaymentDetails, deal, DeskingActions, vehiclesInfo } = this.props;
    const vehicle = dealVehicleId
      ? _find(deal.vehicles, ({ dealVehicleId: vehicleId }) => vehicleId === dealVehicleId)
      : DealReader.getPrimaryVehicle(deal);
    const { pricingDetails } = StartDealHelpers.getVehicleTypeAndPricingDetails(vehicle, vehiclesInfo);
    const rebatesSuperset = getRebatesSuperset({ deskingpaymentDetails });
    if (_isEmpty(rebatesSuperset)) return null;

    const rebateProvider = DealReader.getRebateProviderType(deal, dealVehicleId);
    const vehicleIndex = _findIndex(
      deal.dealVehiclePaymentInfo,
      ({ dealVehicleId: vehicleId }) => vehicleId === dealVehicleId
    );
    const rebateInDealStructure = getUpdatedRebatesForColumn(
      deskingpaymentDetails,
      pricingDetails,
      vehicle?.dealVehicleId
    );

    await DeskingActions.setRebates({
      rebateInDealStructure,
      rebateProvider,
      vehicleIndex,
    });
  };

  getDefaultTermPaymentDataForColumn = (columnId, lenderId) => {
    const { downPayments: allDownPayments, lenders, deskingpaymentDetails, deal, salesSetupInfo } = this.props;

    const columnData = deskingpaymentDetails.find(({ id }) => columnId === id);
    const paymentType = ColumnDataReader.getPaymentType(columnData);
    const lender = lenders.find(({ id }) => id === lenderId);
    const downPayments = DeskingUtils.getDownPaymentsForPaymentType(
      allDownPayments,
      paymentType,
      DealReader.isMultipleDownPaymentsEnabled(deal)
    );

    const termPaymentDetails = {};

    const { rebates } = ColumnDataReader.getRebatesForSelectedLender(columnData);
    const dealFees = ColumnDataReader.getColumnFees(columnData);
    const manuallyDeletedFees = ColumnDataReader.getDeletedColumnFees(columnData);
    const selectedDownPaymentId = ColumnDataReader.getSelectedDownPaymentID(columnData);
    const firstPaymentWaiverManuallyOveridden = ColumnDataReader.getFpwManuallyOverridden(
      columnData,
      selectedDownPaymentId
    );
    const firstPaymentWaivedAmount = ColumnDataReader.getFirstPaymentWaivedAmount(columnData, selectedDownPaymentId);
    const firstPaymentWaived = firstPaymentWaiverManuallyOveridden
      ? ColumnDataReader.isFirstPaymentWaived(columnData, selectedDownPaymentId)
      : getFirstPaymentWaivedDefaultValue(lenders, lenderId);

    const setupData = { lenders, salesSetupInfo };
    const dealData = { vehicleType: DealReader.getPrimaryVehicleType(deal) };
    downPayments.forEach((downPayment, downPaymentIndex) => {
      const program = StartDealHelpers.getEmptyProgramInfo(downPayment, paymentType, lender, setupData, dealData);
      // const program = this.getEmptyProgramInfo(paymentType, lender, null, downPayment);
      _setWith(
        termPaymentDetails,
        [downPaymentIndex],
        {
          ...program,
          rebates,
          dealFees,
          manuallyDeletedFees,
          lenderId,
          firstPaymentWaiverManuallyOveridden,
          firstPaymentWaived,
          firstPaymentWaivedAmount,
        },
        Object
      );
    });

    return termPaymentDetails;
  };

  getEmptyColumnDataForPaymentOption = (paymentOption, selected, newLenderId, dealVehicleId) => {
    const { downPayments: allDownPayments, lenders, deal, salesSetupInfo, vehicleMakeAliases } = this.props;

    const primaryVehicleId = DealReader.getPrimaryVehicleDealVehicleId(deal);
    const setupData = {
      downPayments: allDownPayments,
      lenders,
      salesSetupInfo,
    };
    const overrides = { lenderId: newLenderId };
    return StartDealHelpers.getEmptyColumnDataForPaymentOption(
      paymentOption,
      selected,
      deal,
      setupData,
      overrides,
      dealVehicleId || primaryVehicleId,
      vehicleMakeAliases
    );
  };

  setWorkingCashConfigOnAutoRoll = async () => {
    const { salesSetupInfo } = this.props;
    const shouldCashDeficiencyAutoRollPopup = SalesSetupReader.selectCashDeficiencyAutoRoll(salesSetupInfo);
    let shouldAutoReroll = false;

    if (shouldCashDeficiencyAutoRollPopup) {
      shouldAutoReroll = await this.showAutoRollPopup();
    } else {
      const defaultAutoRollCashDeficiency = SalesSetupReader.selectDefaultAutoRollCashDeficiency(salesSetupInfo);
      shouldAutoReroll = defaultAutoRollCashDeficiency === AUTO_ROLL_DEFICIT_BEARER.CUSTOMER;
    }

    if (shouldAutoReroll) {
      await this.patchLeaseWorkingCashConfig({
        [LEASE_WORKING_CASH.AUTO_ROLL_CASH_DEFICIENCY]: true,
      });
    } else {
      await this.patchLeaseWorkingCashConfig({
        [LEASE_WORKING_CASH.AUTO_ROLL_CASH_DEFICIENCY]: false,
      });
    }
  };

  autoRollCashDeficiency = async (columnIds, successColumnIds) => {
    const { deal, deskingpaymentDetails, downPayments } = this.props;
    const allDownPaymentIds = DeskingUtils.getDownPaymentsIds(downPayments);
    const hasCashDef = DeskingUtils.anyColumnHasCashDeficiency({
      columnIds,
      deskingPaymentDetails: deskingpaymentDetails,
      downPaymentIds: allDownPaymentIds,
    });
    if (hasCashDef && _isNil(DeskingUtils.getAutoRollCashDeficiencyFlag(deal))) {
      const leaseColumnIds = successColumnIds.filter(columnId =>
        ColumnDataReader.isLeasePaymentType(ColumnDataReader.getColumnDataById(deskingpaymentDetails, columnId))
      );
      await this.setWorkingCashConfigOnAutoRoll();
      await this.refreshMarketScan({ columnIds: leaseColumnIds });
    }
  };

  autoRoll0PercentApr = async successColumnIds => {
    const { deskingpaymentDetails, salesSetupInfo, DeskingActions, downPayments, deal } = this.props;
    const isMultipleDownpaymentsEnabled = DealReader.isMultipleDownPaymentsEnabled(deal);
    const loanDownpayments =
      DeskingUtils.getDownPaymentsForPaymentType(downPayments, PAYMENT_TYPES.LOAN, isMultipleDownpaymentsEnabled) ||
      EMPTY_ARRAY;
    const maxLoanDownpayment = Math.max(...loanDownpayments);
    const maxLoanDownpaymentId = loanDownpayments.findIndex(downPayment => downPayment === maxLoanDownpayment);

    const shouldZeroPercentAprAutoRoll = SalesSetupReader.selectZeroPercentAprAutoRoll(salesSetupInfo);

    if (shouldZeroPercentAprAutoRoll) {
      let isAnyAprRolled = false;
      const promises = successColumnIds.map(async columnId => {
        const columnData = ColumnDataReader.getColumnDataById(deskingpaymentDetails, columnId);
        if (ColumnDataReader.isLoanOrBalloonPaymentType(columnData)) {
          const downPaymentId = maxLoanDownpaymentId;
          const selectedLenderId = ColumnDataReader.getSelectedLenderId(columnData);
          const aprData = ColumnDataReader.getAPRDataForLender(columnData, selectedLenderId) || EMPTY_OBJECT;
          const amountFinanced = ColumnDataReader.getAmountFinanaced(columnData, downPaymentId);
          const term = DeskingUtils.getTermAsPerPaymentFrequency(columnData, deal);
          const monthlyPayment = ColumnDataReader.getEmiAmount(columnData, downPaymentId);
          const diffInAmountFinancedAndMP = term * monthlyPayment - amountFinanced;
          if (Number(aprData.apr) === 0 && diffInAmountFinancedAndMP !== 0 && amountFinanced > 0) {
            const dealFees = ColumnDataReader.getAllFees(columnData);
            const prevAdjustedRegFeeAmount = getAdjustedRegistrationFeeAmount(dealFees);
            const adjRegFeeAmount = Number(
              Number(prevAdjustedRegFeeAmount) + Number(diffInAmountFinancedAndMP)
            ).toFixed(2);
            const adjustedRegFee = getNewFeeItemForAdjustedRegistrationFee(adjRegFeeAmount);
            const dealFeesWithPreviousAdjustedRegFeeRemoved = removeAdjustedRegistrationFeeItem(dealFees);
            const newFees = [...dealFeesWithPreviousAdjustedRegFeeRemoved, adjustedRegFee];
            await DeskingActions.feesSave({
              columnFees: { [columnId]: newFees },
            });
            isAnyAprRolled = true;
          }
        }
      });
      await Promise.all(promises);
      if (isAnyAprRolled) {
        await this.refreshMarketScan({ columnIds: successColumnIds });
      }
    }
  };

  handleAutoRoll = async (columnIds, successColumnIds) => {
    await this.autoRollCashDeficiency(columnIds, successColumnIds);
    await this.autoRoll0PercentApr(successColumnIds);
  };

  removeAdjustedRegFeeForNeededColumns = async columnIds => {
    const { deskingpaymentDetails, DeskingActions } = this.props;

    const promises = columnIds.map(async columnId => {
      const columnData = ColumnDataReader.getColumnDataById(deskingpaymentDetails, columnId);
      const selectedLenderId = ColumnDataReader.getSelectedLenderId(columnData);
      const aprData = ColumnDataReader.getAPRDataForLender(columnData, selectedLenderId) || EMPTY_OBJECT;
      if (!ColumnDataReader.isLoanOrBalloonPaymentType(columnData) || getNumber(aprData.apr) !== 0) {
        const dealFees = ColumnDataReader.getAllFees(columnData);
        const dealFeesWithAdjustedRegFeeRemoved = removeAdjustedRegistrationFeeItem(dealFees);
        await DeskingActions.feesSave({
          columnFees: { [columnId]: dealFeesWithAdjustedRegFeeRemoved },
        });
      }
    });
    await Promise.all(promises);
  };

  saveInDealWithoutCalculation = () => {
    const { canCallMarketScan } = this.props;
    if (!canCallMarketScan) {
      this.savePaymentsDataForSelectedVehicle();
      return true;
    }
  };

  // https://tekion.atlassian.net/jira/software/c/projects/DMS/issues/DMS-13647
  updateDispositionFee = async () => {
    const { deskingpaymentDetails, lenders, DeskingActions, getDefaultFees, salesSetupInfo } = this.props;
    const allFees = await getDefaultFees();

    const dealerMarketId = SalesSetupReader.getDealerMarketId(salesSetupInfo);

    const { isUpdateRequired, allColumnFees } = FeeHelpers.getUpdatedDispositionAndTerminationFees({
      deskingpaymentDetails,
      lenders,
      allFees,
      isCalcEngineEnabled: CalcEngineProperties.showCalcEngineView(),
      dealerMarketId,
    });
    if (isUpdateRequired) DeskingActions.updateDealFees(allColumnFees);
  };

  updateDealItemPaymentDetailsWithoutCalculation = async updatedParams => {
    const { blockScreen, unBlockScreen, deal, DeskingActions } = this.props;
    blockScreen();
    const payload = convertUpdateParamsIntoDealKeys(deal, updatedParams);
    await DeskingActions.setDeal(payload);
    await this.saveDealData();
    unBlockScreen();
  };

  syncPaymentDetailsForWithoutCalculation = async () => {
    const { blockScreen, unBlockScreen } = this.props;
    blockScreen();
    await this.savePaymentsDataForSelectedVehicle();
    unBlockScreen();
  };

  updatePaymentWithItemsWithoutCalculation = async (requestParams = EMPTY_OBJECT) => {
    const { dealKeysToCache = EMPTY_ARRAY, syncPaymentPayload = {} } = requestParams;
    const { blockScreen, unBlockScreen, deal } = this.props;
    const dealNumber = DealReader.getDealNumber(deal);
    const payload = getDealItemUpdatePayload(deal, dealKeysToCache, syncPaymentPayload);
    blockScreen();
    await this.savePaymentsDataForSelectedVehicle();
    await this.updateDealData(dealNumber, payload);
    unBlockScreen();
  };

  updatePaymentWithItemsGalaxy = async (requestParams, intertermitentLoadingCallback) => {
    const { DeskingActions, blockScreen, unBlockScreen } = this.props;
    if (!intertermitentLoadingCallback && _isFunction(blockScreen)) blockScreen();
    await DeskingActions.updatePaymentWithItemsGalaxy(requestParams, () => {
      if (intertermitentLoadingCallback) {
        intertermitentLoadingCallback();
        if (_isFunction(blockScreen)) blockScreen();
      }
    });
    if (_isFunction(unBlockScreen)) unBlockScreen();
    if (CalcEngineProperties.updateCalculationByBackend()) {
      await this.cashDeficiencyAutoRollGalaxy();
    }
    await GlobalWarning.showWarnings();
  };

  updatePaymentWithItems = async (...params) => {
    const { intertermitentLoadingCallback } = params || EMPTY_OBJECT;
    const { DeskingActions, canCallMarketScan } = this.props;
    if (!canCallMarketScan) {
      if (intertermitentLoadingCallback) intertermitentLoadingCallback();
      await this.updatePaymentWithItemsWithoutCalculation(...params);
      return;
    }
    if (CalcEngineProperties.updateByGalaxyEngine()) {
      await this.updatePaymentWithItemsGalaxy(...params);
      return;
    }
    await DeskingActions.updatePaymentWithItems(...params);
    if (CalcEngineProperties.updateCalculationByBackend()) {
      await this.cashDeficiencyAutoRollGalaxy();
    }
    await GlobalWarning.showWarnings();
  };

  syncPaymentDetailsWithRecalculateCheck = async (...params) => {
    if (!this.saveInDealWithoutCalculation()) {
      await this.syncPaymentDetails(...params);
    }
  };

  // this function update payment with deal items(ex overrideFees, stateFeeTaxOptions, taxAndZipCodeDetails) in v3/update
  syncPaymentDetailsWithDealItemsUpdate = async (...params) => {
    const { canCallMarketScan } = this.props;
    if (!canCallMarketScan) {
      await this.updatePaymentWithItemsWithoutCalculation();
      return;
    }
    await this.syncPaymentDetails(...params);
  };

  syncPaymentDetailsForGalaxy = async params => {
    const { DeskingActions, blockScreen, unBlockScreen } = this.props;
    await DeskingActions.syncPaymentDetailsGalaxy(params, blockScreen);
    if (CalcEngineProperties.updateCalculationByBackend()) {
      await this.cashDeficiencyAutoRollGalaxy();
    }
    // update downpayments for inhcape
    // https://tekion.atlassian.net/browse/DMS-228855
    if (isInchcape()) {
      const { deal } = this.props;
      const isCalculationByBackendEnabled = CalcEngineProperties.updateCalculationByBackend();
      const downPayments = DealReader.getDownPaymentsFromDeskingPaymentDetails(deal, isCalculationByBackendEnabled);
      await DeskingActions.setDownPayments(downPayments);
    }
    if (_isFunction(unBlockScreen)) unBlockScreen();
    await GlobalWarning.showWarnings();
  };

  syncPaymentDetails = async (...params) => {
    const { DeskingActions, blockScreen, unBlockScreen, canCallMarketScan } = this.props;
    if (!canCallMarketScan) {
      await this.syncPaymentDetailsForWithoutCalculation();
      this.fetchAllCustomerApprovalDetailsOnDealUpdate();
      return;
    }
    if (CalcEngineProperties.updateByGalaxyEngine()) {
      await this.syncPaymentDetailsForGalaxy(...params);
      this.fetchAllCustomerApprovalDetailsOnDealUpdate();
      return;
    }
    if (_isFunction(blockScreen)) blockScreen();
    DeskingActions.setDealDirtyStatus(false);
    await DeskingActions.syncPaymentDetails(...params);
    if (CalcEngineProperties.updateCalculationByBackend()) {
      await this.cashDeficiencyAutoRollGalaxy();
    }
    // update downpayments for inhcape
    // https://tekion.atlassian.net/browse/DMS-228855
    if (isInchcape()) {
      const { deal } = this.props;
      const isCalculationByBackendEnabled = CalcEngineProperties.updateCalculationByBackend();
      const downPayments = DealReader.getDownPaymentsFromDeskingPaymentDetails(deal, isCalculationByBackendEnabled);
      await DeskingActions.setDownPayments(downPayments);
    }
    this.fetchAllCustomerApprovalDetailsOnDealUpdate();
    if (_isFunction(unBlockScreen)) unBlockScreen();
    await GlobalWarning.showWarnings();
  };

  syncPaymentDetailsPreview = async (...params) => {
    const { DeskingActions, blockScreen, unBlockScreen } = this.props;
    if (_isFunction(blockScreen)) blockScreen();
    DeskingActions.setDealDirtyStatus(false);
    const { response, error } = await DeskingActions.syncPaymentDetailsPreview(...params);
    if (_isFunction(unBlockScreen)) unBlockScreen();
    return { response, error };
  };

  cashDeficiencyAutoRollGalaxy = async () => {
    const { deskingpaymentDetails, deal, downPayments } = this.props;

    if (!_isNil(DeskingUtils.getAutoRollCashDeficiencyFlag(deal))) {
      return;
    }
    const allDownPaymentIds = DeskingUtils.getDownPaymentsIds(downPayments);
    const columnIds = [];

    const { dealVehiclePaymentInfos } = deal;
    _forEach(dealVehiclePaymentInfos, ({ dealPayments }) => {
      _map(dealPayments, ({ id }) => {
        columnIds.push(id);
      });
    });

    const hasCashDef = DeskingUtils.anyColumnHasCashDeficiency({
      columnIds,
      deskingPaymentDetails: deskingpaymentDetails,
      downPaymentIds: allDownPaymentIds,
    });

    if (hasCashDef) {
      await this.setWorkingCashConfigOnAutoRoll();
      await this.syncPaymentDetails();
    }
  };

  updateDealItemPaymentDetailsGalaxy = async (updateParams, includeDefaultPayload, params) => {
    const { DeskingActions, blockScreen, unBlockScreen } = this.props;
    const { response, error } = await DeskingActions.updateDealItemPaymentDetailsGalaxy(
      updateParams,
      includeDefaultPayload,
      params,
      () => {
        if (_isFunction(blockScreen)) {
          blockScreen();
        }
      }
    );
    if (!_isEmpty(response?.messages)) {
      toaster(
        TOASTER_STATUS.INFO,
        _map(response?.messages, message => <div>{message}</div>)
      );
    }

    if (CalcEngineProperties.updateCalculationByBackend()) {
      await this.cashDeficiencyAutoRollGalaxy();
    }
    if (_isFunction(unBlockScreen)) unBlockScreen();
    await GlobalWarning.showWarnings();
    return { response, error };
  };

  updateDealItemPaymentDetails = async (...params) => {
    const { DeskingActions, blockScreen, unBlockScreen, canCallMarketScan } = this.props;
    if (!canCallMarketScan) {
      await this.updateDealItemPaymentDetailsWithoutCalculation(...params);
      this.fetchAllCustomerApprovalDetailsOnDealUpdate();
      return;
    }
    if (CalcEngineProperties.updateByGalaxyEngine()) {
      await this.updateDealItemPaymentDetailsGalaxy(...params);
      this.fetchAllCustomerApprovalDetailsOnDealUpdate();
      return;
    }
    if (_isFunction(blockScreen)) blockScreen();
    const { response, error } = await DeskingActions.updateDealItemPaymentDetails(...params);
    if (!_isEmpty(response?.messages)) {
      toaster(
        TOASTER_STATUS.INFO,
        _map(response?.messages, message => <div>{message}</div>)
      );
    }

    if (CalcEngineProperties.updateCalculationByBackend()) {
      await this.cashDeficiencyAutoRollGalaxy();
    }
    this.fetchAllCustomerApprovalDetailsOnDealUpdate();
    if (_isFunction(unBlockScreen)) unBlockScreen();
    await GlobalWarning.showWarnings();
    return { response, error };
  };

  getMarketScanData = async (
    refreshMarketScanData = EMPTY_OBJECT,
    shouldNotifySystemCalculations = true,
    mandoryMscanCall = false,
    isImbalanceInContracts
  ) => {
    const { deskingpaymentDetails, DeskingActions, deal, unBlockScreen } = this.props;
    if (!mandoryMscanCall && this.saveInDealWithoutCalculation()) {
      return;
    }

    await GlobalWarning.showWarnings();
    DeskingActions.setDealDirtyStatus(false);
    DeskingActions.setMarketScanDataFetchingStatus(true);
    if (!CalcEngineProperties.updateCalculationByBackend()) {
      const requiredColumnIDs = _get(refreshMarketScanData, 'columnIds');
      const columnIds = _isEmpty(requiredColumnIDs) ? deskingpaymentDetails.map(({ id }) => id) : requiredColumnIDs;

      // set newCOlumnId for eachCOlumn and use tha
      const columnIdMapper = {};
      _forEach(deskingpaymentDetails, item => {
        if (columnIds.includes(item?.id)) {
          columnIdMapper[item?.id] = generateColumnId(
            {
              paymentType: ColumnDataReader.getPaymentType(item),
              value: ColumnDataReader.getTerm(item),
            },
            item?.dealVehicleId
          );
        }
      });
      const newColumnIds = _values(columnIdMapper);
      await DeskingActions.setNewColumnIds(columnIdMapper);

      await this.removeAdjustedRegFeeForNeededColumns(newColumnIds);

      const { successColumnIds = EMPTY_ARRAY } =
        (await this.refreshMarketScan({ columnIds: newColumnIds })) || EMPTY_OBJECT;

      await this.handleAutoRoll(newColumnIds, successColumnIds);

      /**
       * to update fpw in a column as per ms response
       */
      await DeskingActions.modifyWaiverRebateInDesking();
      await this.updateDispositionFee(); // call this after the monthly payment update is done
    }
    await this.savePaymentsDataForSelectedVehicle(isImbalanceInContracts);
    await DeskingActions.setMarketScanDataFetchingStatus(false);

    // TODO: Fix issue of popup repeatedly coming
    // ExpiredProgramsAlert.checkLenderProgramsExpired();
    GlobalWarning.showWarnings();
    CalculationUpdateNotification.compareAndNotifyChange({
      dealId: _get(deal, 'id'),
      shouldNotifySystemCalculations,
    });
    if (_isFunction(unBlockScreen)) unBlockScreen();
  };

  showAutoRollPopup = async () => {
    const { shouldAutoReroll } = await CashDeficiencyModal.shouldAutoReroll();
    return shouldAutoReroll;
  };

  onChangeOfColumnTermsV2 = async (columnData, months) => {
    const { DeskingActions, unBlockScreen, blockScreen } = this.props;
    const { id } = columnData;
    DeskingActions.setDealDirtyStatus(false);

    await DeskingActions.setPaymentOptions({
      paymentType: ColumnDataReader.getPaymentType(columnData),
      paymentSubType: getPaymentSubType(columnData),
      value: months,
      paymentFrequency: ColumnDataReader.getPaymentFrequency(columnData),
      id,
      newColumnId: id,
    });

    if (this.saveInDealWithoutCalculation()) {
      return;
    }

    if (CalcEngineProperties.updateCalculationByBackend()) {
      const payload = getImpactedColumnAndRowPayload({
        deskingField: DESKING_FIELDS.LENDER,
        impactedColumnIds: [id],
      });
      await this.syncPaymentDetails(payload);
    } else {
      if (_isFunction(blockScreen)) blockScreen();
      await DeskingActions.updateDeskingColumns([id]);
      GlobalWarning.updateBEWarnings([BACKEND_GLOBAL_WARNINGS_ACTIONS.DEAL_PUSH_WARNINGS]);
      if (_isFunction(unBlockScreen)) unBlockScreen();
    }
  };

  updateStateFeeTargetingAndSaveDealData = async (shouldSaveDeal = true) => {
    const { setDefaultStateFeeTargetingOptions, DeskingActions } = this.props;
    await setDefaultStateFeeTargetingOptions();
    const { isDealDirty } = this.props;
    if (isDealDirty && shouldSaveDeal) {
      await this.saveDealData();
      DeskingActions.setDealDirtyStatus(false);
    }
  };

  onChangePaymentType = async ({ paymentType, paymentSubType = null }, months, columnData, shouldCallMS = true) => {
    const { DeskingActions, addDefaultFee, deal, salesSetupInfo, addDefaultAccessories } = this.props;
    const isSelectedColumn = ColumnDataReader.isColumnSelected(columnData);
    const { id } = columnData;

    DeskingActions.setDealDirtyStatus(false);
    if (DeskingUtils.isCategory2Deal(deal)) {
      await DeskingActions.setPaymentOptions({
        paymentType,
        paymentSubType,
        value: months,
        paymentFrequency: ColumnDataReader.getPaymentFrequency(columnData),
        id,
        newColumnId: id,
      });
      this.savePaymentsDataForSelectedVehicle();
    } else {
      if (DealerPropertyHelper.isCreateDealWithNewAPIEnabled()) {
        const { blockScreen, unBlockScreen } = this.props;
        if (_isFunction(blockScreen)) blockScreen();
        await DeskingActions.setPaymentType({ paymentType, paymentSubType, id, months });
        if (!this.saveInDealWithoutCalculation()) {
          await DeskingActions.updateDeskingColumns([id]);
          GlobalWarning.updateBEWarnings([BACKEND_GLOBAL_WARNINGS_ACTIONS.DEAL_PUSH_WARNINGS]);
        }
        if (_isFunction(unBlockScreen)) unBlockScreen();
        return;
      }

      if (CalcEngineProperties.updateCalculationByBackend()) {
        await DeskingActions.setPaymentType({
          paymentType,
          paymentSubType,
          id,
          months: ColumnDataReader.isCashPaymentType(columnData) ? DEFAULT_TERM : months,
        });
        const payload = getImpactedColumnAndRowPayload({
          deskingField: DESKING_FIELDS.PAYMENT_TYPE,
          impactedColumnIds: [id],
        });
        await this.syncPaymentDetails(payload);
        if (DealerPropertyHelper.isExternalDmsEnabled()) {
          DeskingActions.fetchDealSyncMappingWarning(_get(deal, 'dealNumber'));
        }
        GlobalWarning.updateBEWarnings([BACKEND_GLOBAL_WARNINGS_ACTIONS.DEAL_PUSH_WARNINGS]);
        return;
      }

      const paymentFrequencyOptions = SalesSetupReader.selectPaymentFrequency(salesSetupInfo);
      const paymentOption = {
        paymentType,
        value: months,
        paymentFrequency: paymentFrequencyOptions[paymentType],
      };
      let newColumnData = this.getEmptyColumnDataForPaymentOption(
        paymentOption,
        isSelectedColumn,
        null,
        columnData?.dealVehicleId
      );
      newColumnData.accessories = columnData.accessories;
      newColumnData.fnIs = columnData.fnIs;

      const columnFees = ColumnDataReader.getColumnFees(columnData);
      const manuallyDeletedFees = ColumnDataReader.getDeletedColumnFees(columnData);
      const rebates = ColumnDataReader.getColumnRebates(columnData);

      newColumnData = DeskingUtils.setRebatesInColumn(newColumnData, rebates);
      newColumnData = DeskingUtils.setFeesInColumn(newColumnData, columnFees, manuallyDeletedFees);

      const fniSppLenderEnabled = DealerPropertyHelper.isFniSppLenderEnabled();
      if (fniSppLenderEnabled) {
        const sppPaymentOption = ColumnDataReader.getSppPaymentOptions(columnData);
        _set(newColumnData, 'sppPaymentOption', sppPaymentOption);
      }

      await DeskingActions.replaceColumnData({
        id,
        columnData: newColumnData,
      });

      const keepManuallyAddedFees = true;
      await addDefaultFee(newColumnData, keepManuallyAddedFees);
      await addDefaultAccessories({ columnIds: [newColumnData.id], keepManuallyUpdatedAccessories: true });

      // update security deposit waiver reason

      await this.updateStateFeeTargetingAndSaveDealData();
      await this.addSecurityDepositWaiverReason([newColumnData]);

      // set defererd ayments also
      const { deskingpaymentDetails } = this.props;
      const deferredPayments = DeskingUtils.getDeferredPayments(deskingpaymentDetails);
      await DeskingActions.setDeferredPaymentDetails({ deferredPayments });

      await DeskingActions.setValidRebatesInColumn({
        selectedColumnId: newColumnData.id,
      });
      if (shouldCallMS) this.getMarketScanData({ columnIds: [newColumnData.id] });
    }
  };

  applyColumnDataAndDownpaymntToDesking = async payload => {
    const { DeskingActions, deferredPayments } = this.props;
    await DeskingUtils.applyColumnDataAndDownpaymntToDesking({ payload, DeskingActions, deferredPayments });
    await this.getMarketScanData();
    this.saveDealData();
  };

  addSecurityDepositWaiverReason = async columns => {
    const { lenders, DeskingActions } = this.props;

    const leaseTypeColumns = _find(columns, columnData => ColumnDataReader.isLeaseOrOPLeasePaymentType(columnData));
    const lenderId = ColumnDataReader.getSelectedLenderId(leaseTypeColumns);
    const matchedLender = findArrayItem(lenders, 'id', lenderId);

    if (
      leaseTypeColumns &&
      matchedLender?.securityDepositWaiverReason &&
      matchedLender?.collectSecurityDepositWaiverReason
    ) {
      await DeskingActions.setSecurityDepositWaiverReason({ value: matchedLender.securityDepositWaiverReason });
      this.saveDealData();
    }
  };

  patchLeaseWorkingCashConfig = async (updatedSettings = {}) => {
    const { deal } = this.props;
    const workingCashConfigs = { ...deal.workingCashConfig };

    const updated = produce(workingCashConfigs, draft => {
      const leaseWorkingCashConfigs = _get(draft, 'leaseWorkingCashConfigs') || [];
      Object.keys(updatedSettings).forEach(leaseWorkingCashType => {
        const index = leaseWorkingCashConfigs.findIndex(obj => obj.leaseWorkingCashType === leaseWorkingCashType);
        if (index >= 0) {
          leaseWorkingCashConfigs[index].enabled = updatedSettings[leaseWorkingCashType];
        } else {
          leaseWorkingCashConfigs.push({
            leaseWorkingCashType,
            enabled: updatedSettings[leaseWorkingCashType],
          });
        }
      });
      draft.leaseWorkingCashConfigs = leaseWorkingCashConfigs;
    });

    const { DeskingActions } = this.props;
    await DeskingActions.setWorkingCashConfiginDeal(updated);
    await this.saveDealData();
  };

  // Performs market scan only for given column and selected lender inside given column
  refreshMarketScan = async ({ columnIds = [] }) => {
    const response = await this.refreshMarketScanV2({ columnIds });
    return response;
  };

  refreshMarketScanV2 = async ({ columnIds = [] }) => {
    const { deal, DeskingActions, lenders, salesSetupInfo, deskingpaymentDetails } = this.props;
    if (!DeskingUtils.isCategory1Deal(deal)) {
      return;
    }
    await this.getMarketScanVehicleIdForVehicles();
    await this.getAdditionalOEMVINDecodingInfo();
    await this.getMarketID();

    const { deal: dealWithProperVehicleAndMarketId } = this.props;

    const matchingColumns = deskingpaymentDetails.filter(({ id }) => columnIds.includes(id));

    // need to set new columnId

    const { failedColumnsData = [], columnsWithMscanData = [] } = await DeskingMscanHelpers.runMscan(
      matchingColumns,
      dealWithProperVehicleAndMarketId,
      lenders,
      salesSetupInfo
    );
    await DeskingActions.setUpdatedColumnsData([...failedColumnsData, ...columnsWithMscanData]);

    const successColumnIds = columnsWithMscanData.map(({ id }) => id);
    return { successColumnIds };
  };

  getAndSetPaymentDetailsWithoutFNIAndAccessories = async () => {
    if (!CalcEngineProperties.updateCalculationByBackend()) {
      const { deskingpaymentDetails, DeskingActions, includeAccessoriesInBasePrice } = this.props;
      const selectedColumnData = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);
      const selectedLenderId = ColumnDataReader.getSelectedLenderId(selectedColumnData);

      const selectedDownpayment = ColumnDataReader.getSelectedDownPayment(selectedColumnData);
      const selectedDownpaymentId = ColumnDataReader.getSelectedDownPaymentID(selectedColumnData);
      const isGeneric = ColumnDataReader.isGeneric(selectedColumnData);
      const marketScanPayload = this.getMarketScanPayloadObject({
        columnData: selectedColumnData,
        lender: selectedLenderId,
        downPayments: [selectedDownpayment],
        isGenericPayload: isGeneric,
        excludeDisclosureTypes: _values(DISCLOSURE_TYPES), // removes all fnis and accessories
        includeAccessoriesInBasePrice,
      }).payload;
      const { marketScanData = EMPTY_OBJECT } = await DeskingAPI.getMarketScanData([marketScanPayload]);
      const { id: columnId } = selectedColumnData;
      const responseForColumn = _get(marketScanData, columnId) || EMPTY_OBJECT;
      const responseDownPayment = _get(Object.values(responseForColumn), [0]);

      const baseEmiAmount = _get(responseDownPayment, 'Payment') || 0;
      await DeskingActions.setBaseEMIAmount({
        columnId,
        lender: selectedLenderId,
        downPaymentId: selectedDownpaymentId,
        baseEmiAmount,
      });
    }

    await this.savePaymentsDataForSelectedVehicle();
  };

  getMarketScanPayloadObject = ({
    columnData,
    lender,
    downPayments,
    isGenericPayload,
    excludeDisclosureTypes,
    includeAccessoriesInBasePrice = false,
  }) => {
    const { downPayments: allDownPayments, lenders, deal, vehiclesInfo, salesSetupInfo } = this.props;

    let downPaymentsToUse = downPayments;
    if (_isEmpty(downPaymentsToUse)) {
      downPaymentsToUse = DeskingUtils.getDownPaymentsForPaymentType(
        allDownPayments,
        ColumnDataReader.getPaymentType(columnData),
        DealReader.isMultipleDownPaymentsEnabled(deal)
      );
    }
    return getMarketScanPayloadObject({
      columnData,
      lender,
      lenders,
      downPayments: downPaymentsToUse,
      isGenericPayload,
      excludeDisclosureTypes,
      deal,
      vehiclesInfo,
      salesSetupInfo,
      includeAccessoriesInBasePrice,
    });
  };

  setMSVehicleIdInDeal = async (selectedVehicles, doNotSaveDealData) => {
    const { DeskingActions } = this.props;
    const isGalaxyEnabled = CalcEngineProperties.updateByGalaxyEngine();
    const isComLrEnabled = DealerPropertyHelper.isComLrEnabled();

    await _forEach(selectedVehicles, async selectedVehicle => {
      const { dealVehicleId, id, additionalOEMInfo = {}, options } = selectedVehicle || EMPTY_OBJECT;
      const vehicleId =
        CalcEngineProperties.updateByCalcEngine() && !isComLrEnabled
          ? _get(selectedVehicle, 'styleId')
          : _get(selectedVehicle, 'ID');

      const styleId = _get(selectedVehicle, 'styleId');
      const marketScanId = _get(selectedVehicle, 'ID');
      const selectedTrimDetails = getTrimDetailsToSaveInVehicle(selectedVehicle);

      log(`Market scan vehicleId for dealVehicleId ${dealVehicleId} is ${vehicleId}`);
      if (isGalaxyEnabled && !_isEmpty(options)) {
        await DeskingActions.setVehicleOptionsInDeal({ dealVehicleId, id, options });
      }
      if (DealerPropertyHelper.isNewVariantSelectionForCalcEnabled() && !_isEmpty(selectedTrimDetails)) {
        await DeskingActions.setVehicleTrimDetailsInDeal({ dealVehicleId, id, selectedTrimDetails });
      }
      if (marketScanId || styleId || !_isEmpty(additionalOEMInfo)) {
        await DeskingActions.setMarketScanVehicleIdInDeal({
          dealVehicleId,
          styleId,
          marketScanId,
          id,
          additionalOEMInfo,
          isComLrEnabled,
        });
      }
    });
    if (!doNotSaveDealData) {
      if (!CalcEngineProperties.updateCalculationByBackend()) {
        await this.saveDealData();
      } else {
        await this.updateDealItemPaymentDetails();
      }
    }
  };

  handleSelectVehicleVariant = async ({ vehiclesWithNoMSId, vehicleVariantsResponse, LGMBoxOpen, dealVehicles }) => {
    const doAllVehicleHaveSingleVariant = StartDealHelpers.doAllVehicleHaveSingleVariant(vehicleVariantsResponse);
    let selectedVehicles = EMPTY_ARRAY;
    let isVariantSelectionCancelled = false;

    if (doAllVehicleHaveSingleVariant && !LGMBoxOpen) {
      selectedVehicles = _map(vehicleVariantsResponse, (vehicleVariants, index) => ({
        ..._first(vehicleVariants?.response),
        ..._pick(vehiclesWithNoMSId[index], ['dealVehicleId', 'grossWeight', 'unladenWeight', 'id']),
      }));
    } else {
      const { isVariantSelectionCancelled: isModalSelectionCancelled, selectedVehicles: selectedVehiclesWithVariants } =
        await this.selectVehicleRef.current.show({
          vehicles: _map(vehicleVariantsResponse, (vehicleVariants, index) => ({
            vehicleVariants: _map(vehicleVariants?.response, variants => ({
              ...variants,
              ..._pick(vehiclesWithNoMSId[index], ['dealVehicleId', 'grossWeight', 'unladenWeight', 'id']),
            })),
            vehicle: vehiclesWithNoMSId[index],
          })),
        });
      isVariantSelectionCancelled = isModalSelectionCancelled;
      selectedVehicles = _values(selectedVehiclesWithVariants);
    }
    selectedVehicles = addStyleDetailListInVehicles(selectedVehicles, dealVehicles);
    return { selectedVehicles, isVariantSelectionCancelled };
  };

  getVehicleVariantsAndOptionsForGalaxy = async doNotSaveDealData => {
    const { deal, DeskingActions, CommonActions, vehiclesInfo, mscanVehcileIdFetched, handleUpdateVehicleVariant } =
      this.props;
    if (CalcEngineProperties.isRVVehicle() || isFuseDeal(deal)) {
      return false;
    }
    let selectedVehicles = EMPTY_ARRAY;
    let isVariantSelectionCancelled = false;
    const vehicles = StartDealHelpers.getDealVehicles(deal, vehiclesInfo);
    const isCalcEngineEnabled = CalcEngineProperties.updateByCalcEngine();
    const vehiclesWithNoMSId = StartDealHelpers.getVehiclesWithNoMSId({ vehicles, isCalcEngineEnabled });
    const vehiclesWithNoOptions = getVehiclesWithNoOptions(vehicles);
    const vehicleVariantsResponse = await CommonActions.getMarketScanVehiclesInfoV2({
      vehiclesData: vehiclesWithNoMSId,
    });

    if (this.updateVehicleVariantRef && !_isEmpty(vehiclesWithNoOptions) && !DealerPropertyHelper.isComLrEnabled()) {
      const { selectedVehicles: selectedVehiclesWithVariants, isVariantSelectionCancelled: isModalSelectionCancelled } =
        await handleUpdateVehicleVariant({
          deal,
          vehiclesInfo,
          vehiclesWithNoOptions,
          vehiclesWithNoMSId,
          vehicleVariantsResponse,
          ref: this.updateVehicleVariantRef,
        });
      selectedVehicles = selectedVehiclesWithVariants;
      isVariantSelectionCancelled = isModalSelectionCancelled;
    } else if (mscanVehcileIdFetched) {
      return;
    } else if (this.selectVehicleRef && !_isEmpty(vehiclesWithNoMSId)) {
      const { selectedVehicles: selectedVehiclesWithVariants, isVariantSelectionCancelled: isModalSelectionCancelled } =
        await this.handleSelectVehicleVariant({ vehiclesWithNoMSId, vehicleVariantsResponse, dealVehicles: vehicles });
      selectedVehicles = selectedVehiclesWithVariants;
      isVariantSelectionCancelled = isModalSelectionCancelled;
    }
    await this.setMSVehicleIdInDeal(selectedVehicles, doNotSaveDealData);
    await DeskingActions.setMscanVehicleIdFecthed(true);
    return isVariantSelectionCancelled;
  };

  getMarketScanVehicleId = async ({ doNotSaveDealData = false, updateLGMData = false } = EMPTY_OBJECT) => {
    const { deal, DeskingActions, CommonActions, mscanVehcileIdFetched, vehiclesInfo } = this.props;
    if (CalcEngineProperties.updateByGalaxyEngine()) {
      const isVariantSelectionCancelled =
        (await this.getVehicleVariantsAndOptionsForGalaxy(doNotSaveDealData)) || false;
      return isVariantSelectionCancelled;
    }
    if (mscanVehcileIdFetched || CalcEngineProperties.isRVVehicle() || isFuseDeal(deal)) {
      return false;
    }

    let isVariantSelectionCancelled = false;
    let selectedVehicles = EMPTY_ARRAY;
    let vehicles = StartDealHelpers.getDealVehicles(deal, vehiclesInfo);
    const isCalcEngineEnabled = CalcEngineProperties.updateByCalcEngine();
    let LGMBoxOpen = false;
    if (isCanadaDealer() && updateLGMData) {
      vehicles = await Promise.all(
        _map(vehicles, async vehicle => {
          if (!vehicle?.vin && !vehicle?.additionalOEMInfo) {
            const isLgmVehicle = await DeskingAPI.getLGMBoxValidation({ make: vehicle?.make, year: vehicle?.year });

            if (isLgmVehicle) {
              LGMBoxOpen = !vehicle?.additionalOEMInfo;
            }
            return { ...vehicle, isLgmVehicle };
          }
          return vehicle;
        })
      );
    }
    const vehiclesWithNoMSId = StartDealHelpers.getVehiclesWithNoMSId({ vehicles, isCalcEngineEnabled });
    if (_isEmpty(vehiclesWithNoMSId)) return;

    const vehicleVariantsResponse = await CommonActions.getMarketScanVehiclesInfoV2({
      vehiclesData: vehiclesWithNoMSId,
    });

    if (!_isEmpty(vehicleVariantsResponse) && this.selectVehicleRef) {
      const { selectedVehicles: selectedVehiclesWithVariants, isVariantSelectionCancelled: isModalSelectionCancelled } =
        await this.handleSelectVehicleVariant({
          vehiclesWithNoMSId,
          vehicleVariantsResponse,
          LGMBoxOpen,
          dealVehicles: vehicles,
        });
      isVariantSelectionCancelled = isModalSelectionCancelled;
      selectedVehicles = selectedVehiclesWithVariants;
    }
    await this.setMSVehicleIdInDeal(selectedVehicles, doNotSaveDealData);
    await DeskingActions.setMscanVehicleIdFecthed(true);
    return isVariantSelectionCancelled;
  };

  getNewVehicleVariantSelection = async payload => {
    const isVariantSelectionCancelled = (await this.getMarketScanVehicleId(payload)) || false;

    const { deal, DeskingActions } = this.props;
    const isCalcEngineEnabled = CalcEngineProperties.updateByCalcEngine();

    let modifiedDeal = deal;

    if (isVariantSelectionCancelled) {
      modifiedDeal = StartDealHelpers.removeVehicleWithoutVariant(deal, isCalcEngineEnabled);

      // update deal with removed vehicles
      await DeskingActions.setDeal(modifiedDeal);
      const isVariantPresent = StartDealHelpers.isVehicleHaveVariantPresent(modifiedDeal, isCalcEngineEnabled);
      if (!isVariantPresent) {
        modifiedDeal = await this.getNewVehicleVariantSelection(payload);
      }
    }
    return modifiedDeal;
  };

  getNewVehicleVariantSelectionForVehicles = async payload => {
    const { deal: oldDeal, DeskingActions } = this.props;
    const updatedDeal = await this.getNewVehicleVariantSelection(payload);

    const vehicles = _get(updatedDeal, 'vehicles');
    const customers = _get(oldDeal, 'customers');
    const oldVehiclesList = _get(oldDeal, 'vehicles');

    const isVehicleListUpdated = StartDealHelpers.isVehiclesRemovedFromVehiclesList(vehicles, oldVehiclesList);
    if (isVehicleListUpdated) {
      await this.onVehiclesAndCustomersUpdated({ vehicles, customers });
    }
    await DeskingActions.setMscanVehicleIdFecthed(true);
  };

  getMarketScanVehicleIdForVehicles = payload => {
    if (DealerPropertyHelper.isNewVehicleVariantEnabled()) {
      return this.getNewVehicleVariantSelectionForVehicles(payload);
    }

    return this.getMarketScanVehicleId(payload);
  };

  getAdditionalOEMVINDecodingInfo = async ({ doNotSaveDealData = false } = EMPTY_OBJECT) => {
    const { deal, DeskingActions, CommonActions, vehiclesInfo, salesSetupInfo } = this.props;
    if (CalcEngineProperties.isRVVehicle()) return;

    const primaryVehicleInDeal = DealReader.getPrimaryVehicle(deal);
    const primaryVehicleFullInfo = DealReader.getPrimaryVehicleInfo(deal, vehiclesInfo) || EMPTY_OBJECT;
    const vehicles = DealReader.isMultiVehicleDeskingEnabledV2(deal)
      ? _map(deal?.vehicles, vehicle => ({
          ...vehiclesInfo[vehicle?.id],
          ...vehicle,
        }))
      : [{ ...primaryVehicleFullInfo, ...primaryVehicleInDeal }];

    // add Additional OEM info in vehicels
    const modifiedVehicles = await this.additionalOEMVINInfoRef.current.show({
      selectedVehicles: vehicles,
      actions: CommonActions,
      salesSetupInfo,
    });

    if (_isEqual(modifiedVehicles, vehicles)) return;

    // add Additional OEM VIN Decoding info to vehicle in local deal
    modifiedVehicles.forEach(async selectedVehicle => {
      const { additionalOEMInfo, dealVehicleId } = selectedVehicle || EMPTY_OBJECT;
      if (!_isEmpty(additionalOEMInfo)) {
        await DeskingActions.setMarketScanVehicleIdInDeal({ dealVehicleId, additionalOEMInfo });
      }
    });

    if (doNotSaveDealData) return;
    // sync updated vehicle details in deal
    if (!CalcEngineProperties.updateCalculationByBackend()) await this.saveDealData();
    else await this.updateDealItemPaymentDetails();
  };

  saveDealData = async () => {
    const { DeskingActions, deal, onAction } = this.props;
    return onAction({
      type: ACTION_TYPES.SAVE_DEAL_DATA,
      DeskingActions,
      deal,
    });
  };

  saveDealDataWithDealStatusChange = async (newStatus = EMPTY_STRING) => {
    const { DeskingActions, deal, onAction } = this.props;

    const updatedDeal = !_isEmpty(newStatus) ? { ...deal, status: newStatus } : deal;

    return onAction({
      type: ACTION_TYPES.SAVE_DEAL_DATA,
      DeskingActions,
      deal: updatedDeal,
    });
  };

  /**
   * saves the payment details present is desking.deskingPaymentDetail as
   * payment details of selected vehicle in deal
   */
  savePaymentsDataForSelectedVehicle = async isImbalanceInContracts => {
    const { deal, DeskingActions, onAction } = this.props;

    await onAction({
      type: ACTION_TYPES.SAVE_PAYMENTS_DATA_FOR_SELECTED_VEHICLE,
      DeskingActions,
      deal,
      isImbalanceInContracts,
    });
  };

  canEnableTab = tab => {
    const { deal, salesSetupInfo, recapApprovalDetails } = this.props;
    const dealStatus = DealReader.getDealStatus(deal);
    const fniDone = isFNIDone(deal);
    const dealType = DealReader.getDealType(deal);
    const dTOrWSDealAndRecapDisabled = DeskingUtils.isDTOrWSDealAndRecapDisabled(
      dealType,
      DealerPropertyHelper.isDealStatusConfigEnabled(),
      SalesSetupReader.isDealStatusEnabled(salesSetupInfo, DEAL_STATUS.RECAP)
    );
    const migrated = DealReader.migrated(deal);

    switch (tab) {
      case LEFT_PANEL_ITEMS.INVOICING: {
        let valid = fniDone || dTOrWSDealAndRecapDisabled;
        let message = PLEASE_FINISH_DESKING;

        if (isInchcape()) {
          valid = true;
        } else if (valid && isRRG()) {
          valid = DEAL_STATUS_WEIGHTS[dealStatus] > DEAL_STATUS_WEIGHTS[DEAL_STATUS.MARGIN_APPROVAL_PENDING]; // In RRG check if margin approval is done
          message = PLEASE_FINISH_MARGIN_APPROVAL;
        }
        if ([DEAL_TYPES.ONLY_TRADES, DEAL_TYPES.DEALER_TRADE].includes(dealType) && (isRRG() || isInchcape())) {
          // In case of Dealer trade || Wholesale deal || recap is disabled the enable invoice tab
          // incase of inchcape or RRG if trade only/dealer trade deal type show invoicing tab
          valid = true;
        }
        return { valid, message: valid ? '' : message };
      }
      case LEFT_PANEL_ITEMS.CASHIERING: {
        let valid = fniDone || dTOrWSDealAndRecapDisabled;
        let message = PLEASE_FINISH_DESKING;

        if (isInchcape()) {
          valid = true;
        } else if (valid && isRRG()) {
          valid = DEAL_STATUS_WEIGHTS[dealStatus] > DEAL_STATUS_WEIGHTS[DEAL_STATUS.MARGIN_APPROVAL_PENDING]; // In RRG check if margin approval is done
          message = PLEASE_FINISH_MARGIN_APPROVAL;
        }
        if ([DEAL_TYPES.ONLY_TRADES, DEAL_TYPES.DEALER_TRADE].includes(dealType) && (isRRG() || isInchcape())) {
          // In case of Dealer trade || Wholesale deal || recap is disabled the enable invoice tab
          // incase of inchcape or RRG if trade only/dealer trade deal type show cashiering tab
          valid = true;
        }

        return { valid, message: valid ? '' : message };
      }
      case LEFT_PANEL_ITEMS.DOCUMENTS: {
        return {
          valid: canEnableDocumentsTab(deal, dTOrWSDealAndRecapDisabled),
          message: migrated ? MIGRATED_DEAL_NO_DOCUMENTS_ACCESS : PLEASE_FINISH_DESKING,
        };
      }
      case LEFT_PANEL_ITEMS.TRANSACTION_POSTING: {
        const isWorkflowEnabled = recapApprovalDataReader.workflowEnabled(recapApprovalDetails);
        const recapWarnings = getRecapApprovalWarnings(recapApprovalDetails);
        const isTransactionBlocked = recapApprovalDataReader.isTransactionBlocked(recapWarnings);
        const tabStatusValid = DEAL_STATUS_WEIGHTS[dealStatus] >= DEAL_STATUS_WEIGHTS[DEAL_STATUS.BOOKED];

        const valid = isWorkflowEnabled ? !isTransactionBlocked && tabStatusValid : !migrated && tabStatusValid;

        let message;
        if (migrated) {
          message = this.getRedirectToJournalEntryText();
        } else if (!isWorkflowEnabled || !isTransactionBlocked) {
          message = __('Please Finish Cashiering.');
        } else {
          message = tabStatusValid ? __('Please Finish Recap.') : __('Please Finish Cashiering & Recap.');
        }

        return { valid, message };
      }
      default:
        return { valid: true, message: '' };
    }
  };

  handleDSPSigningUserChange = userType => {
    const { DeskingActions } = this.props;
    DeskingActions.dspSendNotificationToiPad({ userType });
  };

  canGoBackToDealList = () => {
    const { eSignSessionInfo, DeskingActions, eSignPlatformRef } = this.props;
    const isAssistedSigningSessionInProgress = _get(eSignSessionInfo, 'isAssistedSigningSessionInProgress', false);
    if (isAssistedSigningSessionInProgress) {
      DeskingActions.toggleESignSessionActive();
      eSignPlatformRef.current.sendStopEventToESignPlatform();
      return false;
    }

    return true;
  };

  saveCustomer = async (dealNumber, payload) => {
    const { canCallMarketScan, DeskingActions } = this.props;

    // when user unblock deal without recalcuate the payment
    if (CalcEngineProperties.updateCalculationByBackend() && !canCallMarketScan) {
      const { response, error } = await this.updateDealData(dealNumber, payload);
      return error ? null : response;
    }

    const newDeal = await DeskingActions.saveCustomer(dealNumber, payload);
    return newDeal;
  };

  changeDealStatusTo = async (newStatus, onStatusChange = _noop) => {
    const { deal: previousDealObj, DeskingActions, showMandatoryForm, salesSetupInfo } = this.props;
    if (
      DealerPropertyHelper.isDealStatusConfigEnabled() &&
      !SalesSetupReader.isDealStatusEnabled(salesSetupInfo, newStatus)
    ) {
      return undefined;
    }

    const toasterId = toaster(TOASTER_STATUS.INFO, __('Please wait.'));

    const { response: newDealObj, mandatoryReviewFields } = await this.saveDealDataWithDealStatusChange(newStatus);

    dismissToast(toasterId);
    if (_isEmpty(newDealObj)) return DeskingActions.setDeal(previousDealObj);
    const response = await DeskingActions.setDeal(newDealObj);

    GlobalWarning.showWarnings();
    if (shouldShowMandatoryReviewFields(mandatoryReviewFields?.mandatoryAndReviewFieldsResponse)) {
      showMandatoryForm(onStatusChange);
    } else {
      onStatusChange();
    }

    return response;
  };

  onUpdateToggleRecapView = async () => {
    const { isRecapViewEnabled } = this.state;
    const { deal, salesSetupInfo } = this.props;
    const dealStatus = DealReader.getDealStatus(deal);
    const isWeightOfDealStatusMoreThanRecap = DEAL_STATUS_WEIGHTS[dealStatus] >= DEAL_STATUS_WEIGHTS[DEAL_STATUS.RECAP];
    const isDealUnwound = DEAL_STATUS.UNWIND === dealStatus;

    if (
      DealerPropertyHelper.isDealStatusConfigEnabled() &&
      !SalesSetupReader.isDealStatusEnabled(salesSetupInfo, DEAL_STATUS.RECAP)
    ) {
      this.toggleRecapView();
    } else if (!isRecapViewEnabled && !isWeightOfDealStatusMoreThanRecap && !isDealUnwound) {
      this.changeDealStatusTo(DEAL_STATUS.RECAP);
    } else {
      this.toggleRecapView();
    }
  };

  renderContent = (selected, isDealViewOnly, isDealLocked, canEditDeskingPermission) => {
    const { isCommissionListModalOpen, isRecapViewEnabled, primaryDMS, chargeCustomer, stopCredit } = this.state;

    const {
      isFetchingMarketScanData,
      isDealDirty,
      deal,
      contentHeight,
      isDealAcquired,
      deskingpaymentDetails,
      CommonActions,
      onStartCasting,
      DigitalRetailActions,
      downloadDSPDocuments,
      taxFeeConfigMetadata,
      navigate,
      location,
      salesSetupInfo,
      mergeScannedDocument,
      blockScreen,
      unBlockScreen,
      changingDwnPmt,
      isUSCalcEngineEnabled,
      buyerCreditReport,
      params,
      dealSyncMissingMappings,
      dealSyncErrors,
      isConciergeActivated,
      getCountOfPhoneNumberDigits,
      openGeneratedPDFAsModal,
      pdfContent,
      isFniModalVisible,
      DeskingActions,
      getCanProceedWithSharingDocuments,
      eSignSessionInfo,
      customerLookup,
      reSignDeclinationSheet,
      mediaURLs,
      fnIDeclinationSheetSigningMandatory,
      setPdfContent,
      recapApprovalDetails,
      startCastingForESignPlatform,
      isAecPlatformDealPushEnabled,
    } = this.props;

    const dealStatus = DealReader.getDealStatus(deal);
    const selectedTabPermissions = _compact(_castArray(LEFT_PANEL_ITEMS_PERMISSIONS_BASED[selected]));
    const migrated = DealReader.migrated(deal);
    const isProgramAcuraNewAndPrimaryVehicleNotZDX = getIsProgramAcuraNewAndPrimaryVehicleNotZDX(deal);
    const isConciergeBuyerCobuyerLinkEditAndShareDisable =
      isProgramAcuraNewAndPrimaryVehicleNotZDX || !this.hasConciergeBuyerCobuyerLinkEditAndShare || isDealViewOnly;
    const dealStatusWeight = DEAL_STATUS_WEIGHTS[dealStatus];
    const dealNumberFromRoute = params?.id;

    if (dealNumberFromRoute && dealNumberFromRoute !== DealReader.getDealNumber(deal)) return null;
    if (
      !_isEmpty(selectedTabPermissions) &&
      _every(selectedTabPermissions, selectedTabPermission => !selectedTabPermission())
    ) {
      return <PermissionsError />;
    }
    const fniDone = isFNIDone(deal);

    const columnData = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);

    switch (selected) {
      case LEFT_PANEL_ITEMS.DESKING:
        return (
          <DealDetails
            columnData={columnData}
            taxFeeConfigMetadata={taxFeeConfigMetadata}
            toggleDealSummaryVisibility={DealSummary.toggleDealSummaryVisibility}
            onDownPaymentSelect={this.onDownPaymentSelect}
            contentHeight={contentHeight}
            onVehiclesUpdated={this.onVehiclesUpdated}
            saveSellingPrice={this.saveSellingPrice}
            onVehiclesAndCustomersUpdated={this.onVehiclesAndCustomersUpdated}
            updateStateFeeTargetingAndSaveDealData={this.updateStateFeeTargetingAndSaveDealData}
            isUSCalcEngineEnabled={isUSCalcEngineEnabled}
            getMarketScanData={this.getMarketScanData}
            canEditDeskingPermission={canEditDeskingPermission}
            applyColumnDataAndDownpaymntToDesking={this.applyColumnDataAndDownpaymntToDesking}
            dealSyncMissingMappings={dealSyncMissingMappings}
            dealSyncErrors={dealSyncErrors}
            selectedTab={selected}
            getCountOfPhoneNumberDigits={getCountOfPhoneNumberDigits}
            getLabelFromPaymentOptions={DeskingUtils.getLabelFromPaymentOptions(
              DeskingUtils.formatPaymentOptionsFromSalesSetup(SalesSetupReader.getPaymentOptionConfigs(salesSetupInfo))
            )}
            reSignDeclinationSheet={reSignDeclinationSheet}
            isFetchingMarketScanData={isFetchingMarketScanData}
            isDealDirty={isDealDirty}
            setPdfContent={setPdfContent}
            isAecPlatformDealPushEnabled={isAecPlatformDealPushEnabled}
          />
        );
      case LEFT_PANEL_ITEMS.CREDIT: {
        return <CreditApplication buyerCreditReport={buyerCreditReport} isConciergeActivated={isConciergeActivated} />;
      }
      case LEFT_PANEL_ITEMS.FINANCE_AND_INSURANCE: {
        const FniMenuComponent = DealerPropertyHelper.isFniMenuV2Enabled() ? FniMenuV2 : FniMenu;
        const fniMenuProps = {
          isDealViewOnly: isDealViewOnly || isFuseDeal(deal),
          stopAllSharing: this.stopAllSharing,
          onStartCasting,
          DigitalRetailActions,
          contentHeight,
          openGeneratedPDFAsModal,
          pdfContent,
          isFniModalVisible,
          getCanProceedWithSharingDocuments,
          isAssistedSigningSessionInProgress: _get(eSignSessionInfo, 'isAssistedSigningSessionInProgress', false),
          reSignDeclinationSheet,
          isFetchingMarketScanData,
          isDealDirty,
          mediaURLs,
          setPdfContent,
        };

        return (
          <PropertyControlledComponentWithMessage valid={fniDone} message={PLEASE_FINISH_DESKING}>
            <FniMenuComponent {...fniMenuProps} />
          </PropertyControlledComponentWithMessage>
        );
      }
      case LEFT_PANEL_ITEMS.RECAP:
        return (
          <FNIRecap
            onOpenCommissionListModal={this.toggleCommissionListModal}
            isCommissionListModalOpen={isCommissionListModalOpen}
            isRecapViewEnabled={isRecapViewEnabled}
            isFuseDeal={isFuseDeal(deal)}
            onUpdateToggleRecapView={this.onUpdateToggleRecapView}
            selectedTab={selected}
          />
        );
      case LEFT_PANEL_ITEMS.MARGIN_APPROVAL:
        return <MarginApproval navigate={navigate} location={location} />;

      case LEFT_PANEL_ITEMS.CONTRACTS:
        return (
          <Contracts
            isDealAcquired={isDealAcquired}
            mergeScannedDocument={mergeScannedDocument}
            isDealLocked={isDealLocked}
          />
        );
      case LEFT_PANEL_ITEMS.DOCUMENTS: {
        const Component = DealerPropertyHelper.isEnhancedDocumentsTabEnabled() ? DocumentsV2 : Documents;

        const { valid, message } = this.canEnableTab(LEFT_PANEL_ITEMS.DOCUMENTS);
        const imbalanceAmountRecalculate = CalcEngineProperties.updateCalculationByBackend()
          ? DeskingActions.syncPaymentDetails
          : this.getMarketScanData;
        const documentsTabProps = {
          contentHeight,
          onStartCasting,
          isDealViewOnly: isDealLocked || isFuseDeal(deal),
          isUSCalcEngineEnabled,
          canEditDeskingPermission,
          isConciergeActivated,
          imbalanceAmountRecalculate,
          reSignDeclinationSheet,
          isFetchingMarketScanData,
          isDealDirty,
          fnIDeclinationSheetSigningMandatory,
          setPdfContent,
        };

        return (
          <PropertyControlledComponentWithMessage valid={valid} message={message}>
            <Component {...documentsTabProps} />
          </PropertyControlledComponentWithMessage>
        );
      }

      case LEFT_PANEL_ITEMS.INVOICING: {
        const { valid, message } = this.canEnableTab(LEFT_PANEL_ITEMS.INVOICING);
        return (
          <PropertyControlledComponentWithMessage valid={valid} message={message}>
            <Invoicing
              isDealViewOnly={isDealViewOnly}
              blockScreen={blockScreen}
              unBlockScreen={unBlockScreen}
              DeskingActions={DeskingActions}
              syncPaymentDetails={this.syncPaymentDetails}
              recapApprovalDetails={recapApprovalDetails}
            />
          </PropertyControlledComponentWithMessage>
        );
      }
      case LEFT_PANEL_ITEMS.CASHIERING: {
        const { vehiclesInfo } = this.props;
        const { valid, message } = this.canEnableTab(LEFT_PANEL_ITEMS.CASHIERING);
        const customerDetails = DealReader.getBuyer(deal);
        const customerId = _get(customerDetails, 'customerId');
        const customerDetailsFromLookup = _filter(customerLookup, entity => entity.id === customerId);
        const { chargeCustomer: isChargeCustomer, stopCredit: stopCreditValue } =
          _get(customerDetailsFromLookup, '0.data.accountingInfo') || {};
        return (
          <PropertyControlledComponentWithMessage valid={valid} message={message}>
            <Cashiering
              isFetchingMarketScanData={isFetchingMarketScanData}
              toggleRecapView={this.toggleRecapView}
              isRecapViewEnabled={isRecapViewEnabled}
              CommonActions={CommonActions}
              resetIsUnlockedDealStatus={this.resetIsUnlockedDealStatus}
              blockScreen={blockScreen}
              unBlockScreen={unBlockScreen}
              changingDwnPmt={changingDwnPmt}
              isDealViewOnly={isDealLocked}
              isDealLocked={isDealLocked}
              dealSyncMissingMappings={dealSyncMissingMappings}
              dealSyncErrors={dealSyncErrors}
              primaryDMS={primaryDMS}
              vehiclesInfo={vehiclesInfo}
              syncPaymentDetails={this.syncPaymentDetails}
              chargeCustomer={isChargeCustomer}
              stopCredit={stopCreditValue}
              startAnimationConfetti={this.startAnimation}
              customerLookup={customerLookup}
              selectedTab={selected}
              recapApprovalDetails={recapApprovalDetails}
              isAecPlatformDealPushEnabled={isAecPlatformDealPushEnabled}
            />
          </PropertyControlledComponentWithMessage>
        );
      }
      case LEFT_PANEL_ITEMS.TRANSACTION_POSTING: {
        const { valid, message } = this.canEnableTab(LEFT_PANEL_ITEMS.TRANSACTION_POSTING);
        return (
          <PropertyControlledComponentWithMessage valid={valid} message={message}>
            <TransactionPosting salesSetupInfo={salesSetupInfo} />
          </PropertyControlledComponentWithMessage>
        );
      }
      case LEFT_PANEL_ITEMS.TASK_MANAGER:
        return (
          <TaskManager
            dealNumber={DealReader.getDealNumber(deal)}
            navigate={navigate}
            location={location}
            isDealAcquired={isDealAcquired}
          />
        );
      case LEFT_PANEL_ITEMS.CONCEIRGE:
        return (
          <DigitalRetail
            dealNumber={DealReader.getDealNumber(deal)}
            downloadDSPDocuments={downloadDSPDocuments}
            isConciergeViewOnly={isConciergeBuyerCobuyerLinkEditAndShareDisable}
            onStartCasting={onStartCasting}
            navigate={navigate}
            location={location}
            getCanProceedWithSharingDocuments={getCanProceedWithSharingDocuments}
            startCastingForESignPlatform={startCastingForESignPlatform}
          />
        );

      default:
        return null;
    }
  };

  getRedirectToJournalEntryText = () => (
    <span>
      {__('Transaction access is restricted for migrated deals. Please go to ')}
      <Button view="link" onClick={this.redirectToJournal}>
        {__('Journal Entry')}
      </Button>
      <span>{__(' to view the accounting transaction.')} </span>
    </span>
  );

  redirectToJournal = () => {
    const baseURL = TEnvReader.cmdsUrl(getEnvironmentVariables());
    window.open(`${baseURL}accounting/journalEntry/list`);
  };

  setChargeCustomer = value => {
    this.setState({
      chargeCustomer: value,
    });
  };

  setStopCredit = stopCredit => {
    this.setState({
      stopCredit,
    });
  };

  startAnimation = text => {
    if (this?.confettiText?.current) {
      this.confettiText.current.performAnimation(text);
    }
  };

  getEcontractPackages = () => {
    const { DeskingActions, deal } = this.props;
    const dealNumber = DealReader.getDealNumber(deal);
    return DeskingActions.getEContractPackages(dealNumber);
  };

  render() {
    const {
      navigate,
      location,
      contentHeight,
      contentWidth,
      deskingpaymentDetails,
      deal,
      vehiclesInfo,
      monthlyPrice,
      isFetchingMarketScanData,
      DeskingActions,
      FniMenuActions,
      openGeneratedPDFAsModal,
      openOrCloseGeneratedPDFAsModal,
      mediaURLs = {},
      pdfContent = {},
      CommonActions,
      selectedTab,
      isDealAcquired,
      salesSetupInfo,
      creditApplications,
      citAmt,
      customerPaymentAmt,
      DealsActions,
      notes,
      conciergeDetails,
      DigitalRetailActions,
      newCustomerActivityCount,
      addDefaultCostAdjustments,
      isAnnotationMode,
      tooglePdfAnnotationMode,
      downloadDealSheet,
      isAnnotationMinimized,
      handleStopCasting,
      onStartCasting,
      vehicleTypes,
      isConciergeEnabled,
      onSetCastAction,
      action,
      togglePDFSignUpdates,
      isPDFSignUpdates,
      documentsViewPreferences,
      taxFeeConfigMetadata,
      dealSheetQueryParams,
      mergeScannedDocument,
      lenders,
      packageDocuments,
      menuPackagesList,
      setDefaultStateFeeTaxOptions,
      setTaxPercentagesFromSetupForSpecificDealTypes,
      isUSCalcEngineEnabled,
      conciergeSetupConfig,
      loginData,
      blockScreen,
      unBlockScreen,
      isScreenBlocked,
      routeOneErrors,
      showArchivedDocuments,
      documentSigningModal,
      eSignSessionInfo,
      documentPushPartnerList,
      downloadDSPDocuments,
      getFormattedDateAndTime,
      sharingBar,
      documentsSignUpdate,
      startVirtualMeeting,
      setVMContainer,
      updateDefaultFNIs,
      isConciergeActivated,
      isESignSessionActive,
      closeESignShareModal,
      startCastingForESignPlatform,
      setMediaUrlsandDocs,
      dealerOemSites,
      handleActionsForDealSheet,
      getCanProceedWithSharingDocuments,
      eSignPlatformRef,
      isEnterpriseV2Enabled,
      isShareBarLoading,
      recapApprovalDetails,
      externalSigningPartner,
      rebateApprovalData,
      isFetchingUpdateDealStaus,
    } = this.props;
    const { isDealAccessible, primaryDMS, chargeCustomer } = this.state;
    const defaultsheetConfigs = SalesSetupReader.selectdefaultsheetConfigs(salesSetupInfo);
    const comissionSplittype = SalesSetupReader.getComissionSplittype(salesSetupInfo);
    const unitCountSplitType = SalesSetupReader.getUnitCountSplittype(salesSetupInfo);

    const enableBudgetBasedRecommendationInDeals =
      SalesSetupReader.selectEnableBudgetBasedRecommendationInDeals(salesSetupInfo);
    const { isCommissionListModalOpen, isRecapViewEnabled, isUnlockedDeal, disablePricingEditSettings } = this.state;
    const isClosedOrSoldDeal = [DEAL_STATUS.BOOKED, DEAL_STATUS.CLOSED_OR_SOLD, DEAL_STATUS.UNWIND].includes(
      deal.status
    );

    const previewItemURL = _get(mediaURLs, [_get(pdfContent, 'mediaId')]) || '';
    const selectedColumn = ColumnDataReader.getSelectedColumn(deskingpaymentDetails);
    const isDeskingEditAllFieldsAfterQuoteStatusEnabled = hasDeskingEditAllFieldsAfterQuoteStatus();
    const canEditAllFieldsAfterQuoteStatus =
      isDeskingEditAllFieldsAfterQuoteStatusEnabled === false && ![DEAL_STATUS.QUOTE].includes(deal.status);
    const paymentsInfoInDeal = DealReader.getDealPaymentsForAllVehicles(deal);
    const isDealCorrupted = this.getIfDealIsCorrupted(deal, paymentsInfoInDeal);
    const isDealLocked = getIsDealLocked({
      deal,
      isClosedOrSoldDeal,
      isUnlockedDeal,
      isDealAcquired,
      isDealCorrupted,
    });
    const isDealViewOnly = canEditAllFieldsAfterQuoteStatus || isDealLocked;
    const isDeskingViewOnly = !hasDeskingEdit() || isDealViewOnly;
    const dealNumber = _get(deal, 'dealNumber') || EMPTY_STRING;
    const setupIntegrationKeys = SalesSetupReader.selectIntegrationKeys(salesSetupInfo);
    const vehicleSubTypesForNewVehicleProgram = SalesSetupReader.selectVehicleSubtypeForNewProgram(salesSetupInfo);
    const rolesForAssignee = SalesSetupReader.getRolesForAssignee(salesSetupInfo);
    const canEditDeskingPermission = !canEditAllFieldsAfterQuoteStatus && hasDeskingEdit(); // only if both permissions are true the user is considered having desking permission
    const pollyConfig = SalesSetupReader.getPollyConfig(salesSetupInfo);
    const showCustomerLoginModal = shouldShowCustomerLoginModalOrWarning(deal) && !_isEmpty(DealReader.getBuyer(deal));
    const dealStatus = DealReader.getDealStatus(deal);
    const isDealsVirtualMeetingEnabled = DealerPropertyHelper.isDealsVirtualMeetingEnabled();
    if (!isDealAccessible) {
      return (
        <Heading className="text-center text-white-50">{__('You do not have permission to view this deal')}</Heading>
      );
    }
    const isFuseDealSource = isFuseDeal(deal);
    const customerId = DealReader.getBuyer(deal)?.customerId;
    const buyer = DealReader.getBuyer(deal);
    const primaryVehicle = DealReader.getPrimaryVehicle(deal);
    const dealVersion = _get(deal, 'version');

    return (
      <Provider
        value={{
          ..._pick(this.props, KEYS_FOR_PROVIDER),
          getMarketScanData: this.getMarketScanData,
          onDownPaymentSelect: this.onDownPaymentSelect,
          updatePaymentWithItems: this.updatePaymentWithItems,
          updateDealItemPaymentDetails: this.updateDealItemPaymentDetails,
          syncPaymentDetails: this.syncPaymentDetails,
          syncPaymentDetailsPreview: this.syncPaymentDetailsPreview,
          syncPaymentDetailsWithRecalculateCheck: this.syncPaymentDetailsWithRecalculateCheck,
          getEmptyColumnDataForPaymentOption: this.getEmptyColumnDataForPaymentOption,
          savePaymentsDataForSelectedVehicle: this.savePaymentsDataForSelectedVehicle,
          saveDealData: this.saveDealData,
          getMarketScanPayloadObject: this.getMarketScanPayloadObject,
          toggleDealSummaryVisibility: DealSummary.toggleDealSummaryVisibility,
          setDefaultValues: this.setDefaultValues,
          getDefaultTermPaymentDataForColumn: this.getDefaultTermPaymentDataForColumn,
          getAndSetPaymentDetailsWithoutFNIAndAccessories: this.getAndSetPaymentDetailsWithoutFNIAndAccessories,
          restoreWithDeal: this.restoreWithDeal,
          onChangePaymentType: this.onChangePaymentType,
          updateDesking: this.updateDesking,
          updateDeskingOnDealTypeChange: this.updateDeskingOnDealTypeChange,
          updateDeskingForColumnIds: this.updateDeskingForColumnIds,
          onChangeOfColumnTermsV2: this.onChangeOfColumnTermsV2,
          getRebates: this.getRebates,
          saveCustomer: this.saveCustomer,
          syncPaymentDetailsWithDealItemsUpdate: this.syncPaymentDetailsWithDealItemsUpdate,
          showGlobalWarnings: this.showGlobalWarnings,
          isClosedOrSoldDeal,
          isDealAcquired,
          isDealViewOnly,
          isDeskingViewOnly,
          isDealLocked,
          addDefaultCostAdjustments,
          vehicleTypes,
          disablePricingEditSettings,
          setTaxPercentagesFromSetupForSpecificDealTypes,
          setDefaultStateFeeTaxOptions,
          isFuseDeal: isFuseDealSource,
          closeESignShareModal,
          startCastingForESignPlatform,
          getCanProceedWithSharingDocuments,
          rebateApprovalData,
        }}>
        <PropertyControlledComponent controllerProperty={isDealsVirtualMeetingEnabled}>
          <VirtualMeetingGlobalWarning isDealLocked={isDealLocked} />
        </PropertyControlledComponent>
        <PollyCustomerInsightsProvider
          loginData={loginData}
          pollyConfig={pollyConfig}
          customer={DealReader.getBuyer(deal)}
          vehicle={DealReader.getPrimaryVehicle(deal)}
          channelInfo={getDealNotificationChannelInfo(dealNumber)}
          dealNumber={dealNumber}
          dealerConfig={dealEnv}>
          {isShareBarLoading && <BackdropLoader />}
          <AutoScrollDiv className={classNames('d-flex flex-column full-height')} id="deskingScreen">
            {/* <PropertyControlledComponent controllerProperty={showCustomerLoginModal}>
              <CustomerLoginModal
                customer={DealReader.getBuyer(deal)}
                conciergeDetails={conciergeDetails}
                DigitalRetailActions={DigitalRetailActions}
                dealNumber={dealNumber}
              />
            </PropertyControlledComponent> */}
            <DeskingFirstLevelHeader
              navigate={navigate}
              location={location}
              contentHeight={contentHeight}
              contentWidth={contentWidth}
              DeskingActions={DeskingActions}
              CommonActions={CommonActions}
              FniMenuActions={FniMenuActions}
              onVehiclesAndCustomersUpdated={this.onVehiclesAndCustomersUpdated}
              onVehiclesUpdated={this.onVehiclesUpdated}
              DealsActions={DealsActions}
              defaultsheetConfigs={defaultsheetConfigs}
              notes={notes}
              conciergeDetails={conciergeDetails}
              salesSetupInfo={salesSetupInfo}
              DigitalRetailActions={DigitalRetailActions}
              isFetchingMarketScanData={isFetchingMarketScanData}
              dealSubStatusList={SalesSetupReader.getDealSubStatusList(salesSetupInfo)}
              vehicleSubTypesForNewVehicleProgram={vehicleSubTypesForNewVehicleProgram}
              rolesForAssignee={rolesForAssignee}
              documentsViewPreferences={documentsViewPreferences}
              isMultiVehicleDealSheet={dealSheetQueryParams?.isMultiVehicleDealSheet}
              mergeScannedDocument={mergeScannedDocument}
              packageDocuments={packageDocuments}
              conciergeSetupConfig={conciergeSetupConfig}
              blockScreen={blockScreen}
              unBlockScreen={unBlockScreen}
              isScreenBlocked={isScreenBlocked}
              routeOneErrors={routeOneErrors}
              showArchivedDocuments={showArchivedDocuments}
              documentPushPartnerList={documentPushPartnerList}
              menuPackagesList={menuPackagesList}
              downloadDSPDocuments={downloadDSPDocuments}
              deskingpaymentDetails={deskingpaymentDetails}
              primaryDMS={primaryDMS}
              startVirtualMeeting={startVirtualMeeting}
              updateDefaultFNIs={updateDefaultFNIs}
              setDealZipCodeIfBuyerZipCodeChanged={this.setDealZipCodeIfBuyerZipCodeChanged}
              saveDealDataFromStoreAndCallMS={this.saveDealDataFromStoreAndCallMS}
              isConciergeActivated={isConciergeActivated}
              showCustomerLoginModal={showCustomerLoginModal}
              isDealLocked={isDealLocked}
              chargeCustomer={chargeCustomer}
              handleActionsForDealSheet={handleActionsForDealSheet}
              canGoBackToDealList={this.canGoBackToDealList}
              isEnterpriseV2Enabled={isEnterpriseV2Enabled}
              syncPaymentDetails={this.syncPaymentDetails}
              isFetchingUpdateDealStaus={isFetchingUpdateDealStaus}
            />
            <div className="d-flex flex-row full-height" id="deskingWorkspace">
              <DeskingLeftNavigation
                deal={deal}
                setupIntegrationKeys={setupIntegrationKeys}
                deskingpaymentDetails={deskingpaymentDetails}
                creditApplications={creditApplications}
                citAmt={citAmt}
                customerPaymentAmt={customerPaymentAmt}
                setSelectedtab={DeskingActions.setSelectedtab}
                setConciergeFlag={DeskingActions.setConciergeFlag}
                selectedTab={selectedTab}
                setPrimaryVehicleInfo={DeskingActions.setPrimaryVehicleInfo}
                savePrimaryVehicleInInventory={this.savePrimaryVehicleInInventory}
                saveDealData={this.saveDealData}
                isFetchingMarketScanData={isFetchingMarketScanData}
                newCustomerActivityCount={newCustomerActivityCount}
                contentHeight={contentHeight}
                isConciergeEnabled={isConciergeEnabled}
                isDealViewOnly={isDealViewOnly}
                salesSetupInfo={salesSetupInfo}
                updateConciergeStatus={DigitalRetailActions.updateConciergeStatus}
                isConciergeActivated={isConciergeActivated}
                recapApprovalDetails={recapApprovalDetails}
                isRecapViewEnabled={isRecapViewEnabled}
                isFetchingUpdateDealStaus={isFetchingUpdateDealStaus}
                CommonActions={CommonActions}
              />
              <div className={classNames('d-flex full-width flex-column', styles.dealDetailsSection)}>
                <PropertyControlledComponent controllerProperty={TABS_THAT_NEEDS_SUBHEADER.includes(selectedTab)}>
                  <SubHeader
                    vehiclesInfo={vehiclesInfo}
                    taxFeeConfigMetadata={taxFeeConfigMetadata}
                    selectedLeftPanel={selectedTab}
                    isRecapViewEnabled={isRecapViewEnabled}
                    toggleRecapView={this.toggleRecapView}
                    unLockDealCallBack={this.unLockDealCallBack}
                    onDuplicateDeal={this.onDuplicateDeal}
                    contentHeight={contentHeight}
                    onVehiclesUpdated={this.onVehiclesUpdated}
                    setZipCodeDetailsInDeal={this.setZipCodeDetailsInDeal}
                    getUpdatedRebatesForColumn={this.getUpdatedRebatesForColumn}
                    saveSellingPrice={this.saveSellingPrice}
                    updateDeskingPaymentsDataForSelectedVehicles={this.updateDeskingPaymentsDataForSelectedVehicles}
                    setRequiredInitialDataForVehicles={this.setRequiredInitialDataForVehicles}
                    updateStateFeeTargetingAndSaveDealData={this.updateStateFeeTargetingAndSaveDealData}
                    isUSCalcEngineEnabled={isUSCalcEngineEnabled}
                    isDealLocked={isDealLocked}
                    blockScreen={blockScreen}
                    unBlockScreen={unBlockScreen}
                    getFormattedDateAndTime={getFormattedDateAndTime}
                    navigate={navigate}
                    location={location}
                    recapApprovalDetails={recapApprovalDetails}
                    onUpdateToggleRecapView={this.onUpdateToggleRecapView}
                    changeDealStatusTo={this.changeDealStatusTo}
                  />
                </PropertyControlledComponent>
                <div
                  style={{
                    height: TABS_THAT_NEEDS_SUBHEADER.includes(selectedTab)
                      ? contentHeight - FIRST_HEADER_HEIGHT
                      : contentHeight,
                  }}
                  className="d-flex flex-column">
                  {this.renderContent(selectedTab, isDealViewOnly, isDealLocked, canEditDeskingPermission)}
                </div>
                <PropertyControlledComponent controllerProperty={!isRRG()}>
                  <DealSummary
                    vehiclesInfo={vehiclesInfo}
                    deal={deal}
                    onOpenCommissionListModal={this.toggleCommissionListModal}
                    isCommissionListModalOpen={isCommissionListModalOpen}
                    deskingpaymentDetails={deskingpaymentDetails}
                    DeskingActions={DeskingActions}
                    monthlyPrice={monthlyPrice}
                    isDeskingViewOnly={isDeskingViewOnly}
                    autoRollSetting={DeskingUtils.getAutoRollCashDeficiencyFlag(deal)}
                    salesSetupInfo={salesSetupInfo}
                    lenders={lenders}
                    isDealViewOnly={isDealViewOnly}
                  />
                </PropertyControlledComponent>
              </div>
            </div>
            <PropertyControlledComponent controllerProperty={isCommissionListModalOpen}>
              <DealCommissions
                comissionSplittype={comissionSplittype}
                unitCountSplitType={unitCountSplitType}
                onCancel={this.toggleCommissionListModal}
                visible={isCommissionListModalOpen}
                DeskingActions={DeskingActions}
                CommonActions={CommonActions}
                deal={deal}
                salesSetupInfo={salesSetupInfo}
              />
            </PropertyControlledComponent>

            <GrossBreakUpModal
              deal={deal}
              deskingpaymentDetails={deskingpaymentDetails}
              integrations={salesSetupInfo?.integrations}
              loginData={loginData}
              DeskingActions={DeskingActions}
              saveDealData={this.saveDealData}
              saveSellingPrice={this.saveSellingPrice}
              updateDealItemPaymentDetails={this.updateDealItemPaymentDetails}
              syncPaymentDetails={this.syncPaymentDetails}
              isDealViewOnly={isDealViewOnly}
              salesSetupInfo={salesSetupInfo}
            />

            <PreviewGeneratedPDF
              openGeneratedPDFAsModal={openGeneratedPDFAsModal}
              openOrCloseGeneratedPDFAsModal={openOrCloseGeneratedPDFAsModal}
              mediaURLs={mediaURLs}
              previewItemURL={previewItemURL}
              document={pdfContent}
              DeskingActions={DeskingActions}
              defaultsheetConfigs={defaultsheetConfigs}
              dealNumber={dealNumber}
              deal={deal}
              conciergeDetails={conciergeDetails}
              DigitalRetailActions={DigitalRetailActions}
              tooglePdfAnnotationMode={tooglePdfAnnotationMode}
              handleStopCasting={handleStopCasting}
              onSetCastAction={onSetCastAction}
              // deskingPaymentDetails={deskingpaymentDetails}
              deskingpaymentDetails={deskingpaymentDetails}
              menuPackagesList={menuPackagesList}
              conciergeSetupConfig={conciergeSetupConfig}
              getLabelFromPaymentOptions={DeskingUtils.getLabelFromPaymentOptions(
                DeskingUtils.formatPaymentOptionsFromSalesSetup(
                  SalesSetupReader.getPaymentOptionConfigs(salesSetupInfo)
                )
              )}
              isDeductCustomerCashfromAmountFinancedforCashDealEnabled={SalesSetupReader.deductCustomerCashfromAmountFinancedforCashDealEnabled(
                salesSetupInfo
              )}
              startVirtualMeeting={startVirtualMeeting}
              setVMContainer={setVMContainer}
              onStartCasting={onStartCasting}
              closeESignShareModal={closeESignShareModal}
              toggleESignSessionActive={DeskingActions.toggleESignSessionActive}
              isConciergeActivated={isConciergeActivated}
              startCastingForESignPlatform={startCastingForESignPlatform}
              setMediaUrlsandDocs={setMediaUrlsandDocs}
              getFormattedDateAndTime={getFormattedDateAndTime}
              dealerOemSites={dealerOemSites}
              dealerInfo={dealEnv.dealerConfig}
              handleActionsForDealSheet={handleActionsForDealSheet}
              getCanProceedWithSharingDocuments={getCanProceedWithSharingDocuments}
              FniMenuActions={FniMenuActions}
            />
            <PropertyControlledComponent
              controllerProperty={
                previewItemURL && isAnnotationMode && documentSigningModal === DOCUMENT_SIGNING_MODALS.PDF_CAST
              }>
              <PdfCast
                documentUrl={previewItemURL}
                deal={deal}
                hideCastModalOnClose
                document={pdfContent}
                tooglePdfAnnotationMode={tooglePdfAnnotationMode}
                onStartCasting={onStartCasting}
                // mediaURLs={mediaURLs}
                handleDownload={downloadDealSheet}
                isAnnotationMinimized={isAnnotationMinimized}
                action={action}
                togglePDFSignUpdates={togglePDFSignUpdates}
                isPDFSignUpdates={isPDFSignUpdates}
                isMultiVehicleDealSheet={dealSheetQueryParams?.isMultiVehicleDealSheet}
                isCanadaSheet={dealSheetQueryParams?.isCanadaSheet}
                deskingpaymentDetails={deskingpaymentDetails}
                documentsSignUpdate={documentsSignUpdate}
              />
            </PropertyControlledComponent>
            <PropertyControlledComponent controllerProperty={enableBudgetBasedRecommendationInDeals}>
              <BudgetBasedRecommendationWithCast
                deal={deal}
                updateRecommendationUses={this.updateRecommendationUses}
                selectedColumn={selectedColumn}
                onSelectRecommendation={this.makeRecommendedVehiclePrimary}
                onStartCasting={onStartCasting}
              />
            </PropertyControlledComponent>
          </AutoScrollDiv>
        </PollyCustomerInsightsProvider>
        <SelectVehicle ref={this.selectVehicleRef} blockScreen={blockScreen} unBlockScreen={unBlockScreen} />
        <UpdateVehicleVariant ref={this.updateVehicleVariantRef} blockScreen={blockScreen} />
        <AdditionalOEMVINInfo ref={this.additionalOEMVINInfoRef} />
        <CashDeficiencyModal />
        <ExpiredProgramsAlert />
        <ESignShareModal
          ref={eSignPlatformRef}
          {...eSignSessionInfo}
          dealNumber={dealNumber}
          onStopCasting={DeskingActions.stopEsignSharingSession}
          onClose={closeESignShareModal}
          handleDSPSigningUserChange={this.handleDSPSigningUserChange}
          isESignSessionActive={isESignSessionActive}
          toggleESignSessionActive={DeskingActions.toggleESignSessionActive}
          showCloseModal={externalSigningPartner === SIGNING_PARTNER.DEALER_TRACK}
          selectedTab={selectedTab}
          getEcontractPackages={this.getEcontractPackages}
        />
        {sharingBar(this.getMarketScanData)}
        <NotificationHandler
          selectedTab={selectedTab}
          DeskingActions={DeskingActions}
          // blockScreen={blockScreen}
          // unBlockScreen={unBlockScreen}
        />
        <PropertyControlledComponent controllerProperty={isInchcape()}>
          <ChargeCustomer
            customerId={customerId}
            dealStatus={dealStatus}
            setChargeCustomer={this.setChargeCustomer}
            setStopCredit={this.setStopCredit}
          />
        </PropertyControlledComponent>
        <ConfettiText ref={this.confettiText} />
        <PropertyControlledComponent controllerProperty={isDealsVirtualMeetingEnabled}>
          <VirtualMeeting dealNumber={dealNumber} leadDetails={buyer} DeskingActions={DeskingActions} />
        </PropertyControlledComponent>
        <RecapApprovalUpdatesHandler deal={deal} DeskingActions={DeskingActions} />
        <DealAlerts dealNumber={dealNumber} primaryVehicle={primaryVehicle} dealVersion={dealVersion} />
      </Provider>
    );
  }
}

export default compose(
  withActionHandlers(ACTION_HANDLERS),
  DeskingMSHandler,
  withTekionConversion,
  withUpdateVehicleVariantHandler,
  WithMandatoryReviewHOC,
  withHandleCustomerApprovals
)(Desking);

// export default DeskingMSHandler(Desking);
