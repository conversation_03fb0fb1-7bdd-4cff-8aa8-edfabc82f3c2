import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { compose } from 'recompose';

import _get from 'lodash/get';
import _noop from 'lodash/noop';
import _isEmpty from 'lodash/isEmpty';
import _isEqual from 'lodash/isEqual';

import { EMPTY_ARRAY, EMPTY_STRING, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { BASE_REDUCER_KEY } from 'constants/constants';
import { tget } from '@tekion/tekion-base/utils/general';
import dealEnv from 'utils/dealEnv';

import copyTextToClipboard from '@tekion/tekion-base/utils/copyTextToClipboard';
import { hasViewSubscreensInBookedDeals } from 'permissions/desking.permissions';
import { isInchcape, isRRG } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';

import {
  getDealType,
  getPrimaryVehicle,
  getDealStatus,
  getPrimaryVehicleStatus,
  getPrimaryVehicleId,
  getDealNumber,
} from '@tekion/tekion-base/marketScan/readers/deal.reader';
import { DEAL_STATUS_WEIGHTS, DEAL_STATUS } from 'pages/deallist/deal.constants';
import { SOURCE_TYPE } from 'pages/desking/desking.constants';
import { DEAL_TYPES } from '@tekion/tekion-base/constants/deal/type';
import { isVehicleReserved } from '@tekion/tekion-base/helpers/dealVehicle.helper';
import CRMLeadHeartbeat from 'utils/CRMLeadHeartbeat';

import EditDeletePopover from 'molecules/editDeletePopover';
import withSize from '@tekion/tekion-components/src/hoc/withSize';
import { selectStateListOptions } from 'commonActions/selectors';

import SelectedVehicle from 'molecules/selectedVehicle';
import GlobalWarning from 'pages/desking/components/globalWarnings';
import { getAllModels, getAllVehicleMakes } from 'pages/StartDeal/startDeal.selector';
import {
  getVehiclePOs,
  getVISettings,
  getVehicleModel,
  getVehicleMake,
  getV2InventorySetup,
} from 'commonActions/actions';
import EditVehicleModal from 'organisms/editVehicleModal';
import { getVehicleTransferStatusMessage } from './ymmHeaderItem.helpers';

import styles from './ymmHeaderItem.module.scss';
import { VEHICLE_TRANSFER_STATUS_TITLE } from './ymmHeaderItem.constants';

class YMMHeaderItem extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      dealCount: null,
    };
  }

  async componentDidMount() {
    const {
      DealsActions,
      vehicleId,
      vin,
      getVehiclePOs: getVehiclePO,
      getVISettings: getVISettingsData,
      CommonActions,
      getVehicleMake: getVehicleMakes,
      getVehicleModel: getVehicleModels,
      deal,
    } = this.props;
    const vehicleMake = await getVehicleMakes();
    await getVehicleModels(_get(vehicleMake, 'makes'));
    getVISettingsData();
    getVehiclePO([vin]);
    CommonActions.fetchDealerSites(_get(deal, 'dealerId'));
  }

  copyVin = (event, vin) => {
    copyTextToClipboard(event, document, vin);
  };

  getSwitchVehicleDisableInfo = () => {
    const { deal } = this.props;
    if (DealerPropertyHelper.isAECProgram()) {
      const sourceType = _get(deal, 'sourceType');
      const isSourceTypeRestricted = _isEqual(sourceType, SOURCE_TYPE.OMS);

      const hoverMessage = isSourceTypeRestricted
        ? __('Switching vehicle is not allowed for deals created for custom orders.')
        : EMPTY_STRING;

      return { shouldDisable: isSourceTypeRestricted, hoverMessage };
    }

    if (isInchcape()) {
      const status = getDealStatus(deal);
      const type = getDealType(deal);
      return {
        shouldDisable:
          DEAL_STATUS_WEIGHTS[status] >= DEAL_STATUS_WEIGHTS[DEAL_STATUS.ON_ORDER] || type === DEAL_TYPES.FNI,
        hoverMessage: EMPTY_STRING,
      };
    }

    return { shouldDisable: false, hoverMessage: EMPTY_STRING };
  };

  getMenuActions = () => {
    const { deal, showRecallWarning, isDealViewOnly, isFuseDeal, crmHeartbeatStatus } = this.props;
    const { vin, id, dealerId: vehicleDealerId } = getPrimaryVehicle(deal) || EMPTY_OBJECT;
    const menuActions = [];
    const switchVehicleDisableInfo = this.getSwitchVehicleDisableInfo();
    const currentDealerId = _get(dealEnv, 'dealerConfig.id') || EMPTY_STRING;

    if (isDealViewOnly || isFuseDeal) {
      if (hasViewSubscreensInBookedDeals()) {
        menuActions.push({
          name: __('View Vehicle Details'),
          icon: 'icon-eye',
          onClick: () => this.onEditVehicle(),
        });
      }
    } else if (
      !DealerPropertyHelper.isVehicleTransferV2Enabled() ||
      !vehicleDealerId ||
      currentDealerId === vehicleDealerId
    ) {
      menuActions.push({
        name: __('Edit Vehicle Details'),
        icon: 'icon-edit',
        onClick: () => this.onEditVehicle(),
      });
    }
    const dealType = getDealType(deal);
    const status = getDealStatus(deal);
    if (
      dealType !== DEAL_TYPES.REFINANCING &&
      DEAL_STATUS_WEIGHTS[status] < DEAL_STATUS_WEIGHTS[DEAL_STATUS.BOOKED] &&
      !isFuseDeal &&
      !isRRG() &&
      !crmHeartbeatStatus
    ) {
      menuActions.push({
        name: __('Switch Vehicle'),
        icon: 'icon-refresh2',
        onClick: () => this.onSwitchVehicle(),
        disabled: switchVehicleDisableInfo.shouldDisable,
        tooltipMessage: switchVehicleDisableInfo.hoverMessage,
      });
    }
    menuActions.push({
      name: __('Copy VIN'),
      icon: 'icon-copy',
      onClick: ({ domEvent: event }) => this.copyVin(event, vin),
    });
    if (id && !isRRG()) {
      menuActions.push({
        name: __('Check for Recalls'),
        icon: 'icon-reload',
        onClick: () => showRecallWarning(true, true, GlobalWarning.showWarnings),
      });
    }

    return menuActions;
  };

  onSwitchVehicle = () => {
    const { onClick } = this.props;
    CRMLeadHeartbeat.updateCRMLeadHeartbeatStatus({ vehicleTabOpened: true }, { isDelayedUpdate: false });
    if (onClick) onClick();
  };

  onEditVehicle = () => {
    this.toggleModalVisiblity();
  };

  toggleModalVisiblity = () => {
    EditVehicleModal.toggleVisibility();
  };

  renderVehicleTransferPopup = () => {
    const { vehicleTransferDetails, dealers } = this.props;
    const vehicleTransferStatusMessage = getVehicleTransferStatusMessage(vehicleTransferDetails, dealers);
    const vehicleTransferStatusTitle = tget(
      VEHICLE_TRANSFER_STATUS_TITLE,
      vehicleTransferDetails?.currentStatus,
      EMPTY_STRING
    );

    return (
      <div className={styles.vehicleTransferPopup}>
        {!vehicleTransferDetails?.centralApprovalStatus ? (
          <div className={styles.vehicleTransferStatusTitle}>{vehicleTransferStatusTitle}</div>
        ) : null}
        <div className={styles.vehicleTransferStatusMessage}>{vehicleTransferStatusMessage}</div>
      </div>
    );
  };

  render() {
    const {
      vehicleYMM,
      vehicleImageUrl,
      subInfo,
      recallDetails,
      roDetails,
      disabled,
      vehicleId,
      vin,
      deal,
      DealsActions,
      vehiclePOs,
      isDealViewOnly,
      hasVehicleEdit,
      customStatuses,
      vehiclesInfo,
      CommonActions,
      mileageForResidualCalculation,
      options,
      trimInfos,
      vehicleSubTypesForNewVehicleProgram,
      dealerSites,
      locationCodes,
      contentHeight,
      disablePricingEditSettings,
      primaryVehicleStatus,
      onVehiclesUpdated,
      setTradeInVehicles,
      vehicleModels,
      vehicleMakes,
      rvDetails,
      getRecallData,
      isRVDealer,
      stopDeliveryIndicator,
      campaignCodes,
      modelCode,
      salesSetupInfo,
      isFuseDeal,
      stateListOptions,
      trimDetails,
      alerts,
      hasOptionAlerts,
      removeOptionAlert,
      activeTab,
      dealVehicleId,
      vehicleTransferDetails,
      viV2SetupResponse,
    } = this.props;
    const { dealCount } = this.state;
    const dealType = getDealType(deal);
    // make the popover disabled if the deal isn't viewOnly and has no edit access
    const isDisabled = (!isDealViewOnly && !hasVehicleEdit) || dealType === DEAL_TYPES.ONLY_TRADES;
    const modelCodeName = !_isEmpty(modelCode) ? `${vehicleYMM} | ${modelCode || ''}` : vehicleYMM;
    const vehicle = _get(vehiclesInfo, getPrimaryVehicleId(deal));
    const isPrimaryVehicleReservedFromVI = isVehicleReserved(_get(vehicle, 'status'));
    const vehicleTransferStatus = tget(vehicleTransferDetails, 'currentStatus');
    const VehicleTransferStatusComponent = this.renderVehicleTransferPopup();

    return (
      <EditDeletePopover data={this.getMenuActions()} trigger={['click', 'contextMenu']} disabled={isDisabled}>
        <div className={`cursor-pointer ${styles.parentContainer}`}>
          <SelectedVehicle
            isRVDealer={isRVDealer}
            getRecallData={getRecallData}
            vehicleImage={vehicleImageUrl}
            vehicleYMM={modelCodeName}
            withRightSeparator={false}
            subInfo={subInfo}
            icon="icon-chevron-down"
            recalls={{ recallDetails, roDetails }}
            disabled={disabled}
            onEdit={this.toggleModalVisiblity}
            vehicleId={vehicleId}
            getDetailedActiveDeals={DealsActions.getDetailedActiveDeals}
            vin={vin}
            vehiclePOs={vehiclePOs}
            showOnHold={isVehicleReserved(getPrimaryVehicleStatus(deal)) || isPrimaryVehicleReservedFromVI}
            stopDeliveryIndicator={stopDeliveryIndicator}
            campaignCodes={campaignCodes}
            trimDetails={trimDetails}
            alerts={alerts}
            hasOptionAlerts={hasOptionAlerts}
            removeOptionAlert={this.removeOptionAlert}
            ellipsisLength={32}
            vehicle={vehicle}
            vehicleTransferStatusContent={{ VehicleTransferStatusComponent, vehicleTransferStatus }}
            dealNumber={getDealNumber(deal)}
            externalReservationLink={_get(deal, 'externalReservationLink')}
          />

          <div onClick={e => e.stopPropagation()} onContextMenu={e => e.stopPropagation()} role="button" tabIndex="0">
            <EditVehicleModal
              activeTab={activeTab}
              vin={vin}
              customStatuses={customStatuses}
              deal={deal}
              vehiclesInfo={vehiclesInfo}
              CommonActions={CommonActions}
              vehicleId={vehicleId}
              dealVehicleId={dealVehicleId}
              mileageForResidualCalculation={mileageForResidualCalculation}
              options={options}
              trimInfos={trimInfos}
              vehicleSubTypesForNewVehicleProgram={vehicleSubTypesForNewVehicleProgram}
              dealerSites={dealerSites}
              locationCodes={locationCodes}
              contentHeight={contentHeight}
              disablePricingEditSettings={disablePricingEditSettings}
              primaryVehicleStatus={primaryVehicleStatus}
              isDealViewOnly={isDealViewOnly || isFuseDeal}
              onVehiclesUpdated={onVehiclesUpdated}
              setTradeInVehicles={setTradeInVehicles}
              vehicleModels={vehicleModels}
              vehicleMakes={vehicleMakes}
              rvDetails={rvDetails}
              salesSetupInfo={salesSetupInfo}
              stateListOptions={stateListOptions}
              alerts={alerts}
              hasOptionAlerts={hasOptionAlerts}
              removeOptionAlert={removeOptionAlert}
              viV2SetupResponse={viV2SetupResponse}
            />
          </div>
        </div>
      </EditDeletePopover>
    );
  }
}

YMMHeaderItem.propTypes = {
  vehicleImageUrl: PropTypes.string,
  vehicleYMM: PropTypes.string,
  subInfo: PropTypes.string,
  onClick: PropTypes.func.isRequired,
  recallDetails: PropTypes.arrayOf(PropTypes.Object),
  roDetails: PropTypes.arrayOf(PropTypes.object),
  disabled: PropTypes.bool,
  deal: PropTypes.object,
  vehicleId: PropTypes.string.isRequired,
  vehiclesInfo: PropTypes.object,
  vin: PropTypes.string.isRequired,
  onVehiclesUpdated: PropTypes.func.isRequired,
  DealsActions: PropTypes.object.isRequired,
  CommonActions: PropTypes.object.isRequired,
  trimInfos: PropTypes.object.isRequired,
  mileageForResidualCalculation: PropTypes.any,
  locationCodes: PropTypes.array,
  vehiclePOs: PropTypes.object.isRequired,
  options: PropTypes.array,
  vehicleSubTypesForNewVehicleProgram: PropTypes.array,
  setTradeInVehicles: PropTypes.func.isRequired,
  getVehiclePOs: PropTypes.func,
  getVISettings: PropTypes.func,
  contentHeight: PropTypes.number.isRequired,
  isDealViewOnly: PropTypes.bool,
  hasVehicleEdit: PropTypes.bool,
  disablePricingEditSettings: PropTypes.bool.isRequired,
  primaryVehicleStatus: PropTypes.string,
  showRecallWarning: PropTypes.func,
  customStatuses: PropTypes.array,
  dealers: PropTypes.array,
  rvDetails: PropTypes.object,
  modelCode: PropTypes.string,
  salesSetupInfo: PropTypes.object,
  vehicleTransferDetails: PropTypes.object,
  alerts: PropTypes.object,
  hasOptionAlerts: PropTypes.bool,
  removeOptionAlert: PropTypes.func,
  activeTab: PropTypes.string,
};

YMMHeaderItem.defaultProps = {
  vehicleImageUrl: EMPTY_STRING,
  vehicleYMM: EMPTY_STRING,
  subInfo: EMPTY_STRING,
  recallDetails: EMPTY_ARRAY,
  roDetails: EMPTY_ARRAY,
  disabled: false,
  deal: EMPTY_OBJECT,
  vehiclesInfo: EMPTY_OBJECT,
  mileageForResidualCalculation: '',
  locationCodes: EMPTY_ARRAY,
  options: EMPTY_ARRAY,
  dealers: EMPTY_ARRAY,
  vehicleSubTypesForNewVehicleProgram: EMPTY_ARRAY,
  getVehiclePOs: _noop,
  getVISettings: _noop,
  isDealViewOnly: false,
  hasVehicleEdit: false,
  primaryVehicleStatus: EMPTY_STRING,
  showRecallWarning: _noop,
  customStatuses: EMPTY_ARRAY,
  rvDetails: EMPTY_OBJECT,
  vehicleTransferDetails: EMPTY_OBJECT,
  modelCode: EMPTY_STRING,
  salesSetupInfo: EMPTY_OBJECT,
  alerts: EMPTY_OBJECT,
  hasOptionAlerts: false,
  removeOptionAlert: _noop,
  activeTab: EMPTY_STRING,
};

const mapStateToProps = globalState => {
  const state = _get(globalState, BASE_REDUCER_KEY);
  return {
    locationCodes: _get(state, 'common.viMetaData.locationCode') || EMPTY_ARRAY,
    vehiclePOs: _get(state, 'common.vehiclePOs') || EMPTY_OBJECT,
    vehicleTypes: _get(state, 'common.viSettings.typeSetting.vehicleTypes') || EMPTY_ARRAY,
    customStatuses: _get(state, 'common.viSettings.otherSettings.customStatuses') || EMPTY_ARRAY,
    dealerSites: _get(state, 'common.dealerSites'),
    vehicleModels: getAllModels(state),
    vehicleMakes: getAllVehicleMakes(state),
    rvDetails: _get(state, 'common.viMetaData.rvDetails'),
    stateListOptions: selectStateListOptions(state),
    dealers: _get(state, 'common.dealers'),
    vehicleTransferDetails: tget(state, 'common.vehicleTransferDetails', EMPTY_OBJECT),
    viV2SetupResponse: _get(state, 'common.viV2InvantorySetup') || EMPTY_ARRAY,
  };
};

const mapDispatchToProps = {
  getVehiclePOs,
  getVISettings,
  getVehicleModel,
  getVehicleMake,
  getV2InventorySetup,
};

export default compose(
  connect(mapStateToProps, mapDispatchToProps),
  withSize({ hasPageFooter: 1, hasPageHeader: 1 })
)(YMMHeaderItem);
