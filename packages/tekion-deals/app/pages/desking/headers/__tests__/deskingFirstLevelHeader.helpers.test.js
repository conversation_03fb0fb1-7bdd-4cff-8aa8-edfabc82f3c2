// Mock the problematic SalesOrderMetaDetails module that's causing the ES6 import issue
jest.mock('@tekion/tekion-widgets/src/appServices/parts/components/SalesOrderMetaDetails', () => ({
  SalesOrderMetaDetails: 'SalesOrderMetaDetails',
}));

import { shouldUpdateGlobalWarnings } from '../deskingFirstLevelHeader.helpers';

describe('Test shouldUpdateGlobalWarnings', () => {
  test('should return false when oldDeal and newDeal are undefined', () => {
    const oldDeal = undefined;
    const newDeal = undefined;
    const result = shouldUpdateGlobalWarnings(oldDeal, newDeal);
    expect(result).toBe(false);
  });
  test('should return false when oldDeal and newDeal are null', () => {
    const oldDeal = null;
    const newDeal = null;
    const result = shouldUpdateGlobalWarnings(oldDeal, newDeal);
    expect(result).toBe(false);
  });
  test('should return false when oldDeal and newDeal are {}', () => {
    const oldDeal = {};
    const newDeal = {};
    const result = shouldUpdateGlobalWarnings(oldDeal, newDeal);
    expect(result).toBe(false);
  });
  test('should return false when oldDeal and newDeal are {}', () => {
    const oldDeal = {};
    const newDeal = {};
    const result = shouldUpdateGlobalWarnings(oldDeal, newDeal);
    expect(result).toBe(false);
  });
  test('should return false when oldDeal and newDeal has no customers', () => {
    const oldDeal = { customers: [] };
    const newDeal = { customers: [] };
    const result = shouldUpdateGlobalWarnings(oldDeal, newDeal);
    expect(result).toBe(false);
  });
  test('should return true when oldDeal and newDeal has customer zip different', () => {
    const oldDeal = {
      customers: [
        {
          type: 'BUYER',
          address: [
            {
              cmsAddressId: '41eb5d45-f352-40a8-957c-0d353e85247b',
              address1: 'faridi nagar indira nagar lucknow',
              addressType: 'CURRENT',
              currentAddress: true,
              city: 'lucknow',
              state: 'WA',
              zipCode: '98001',
              country: '',
              countyName: '',
              inCity: false,
              periodOfResidenceDurationType: 'YEAR',
              moreThanTwoYears: false,
              capencyVerification: false,
            },
          ],
        },
      ],
    };
    const newDeal = {
      customers: [
        {
          type: 'BUYER',
          address: [
            {
              cmsAddressId: '41eb5d45-f352-40a8-957c-0d353e85247b',
              address1: 'faridi nagar indira nagar lucknow',
              addressType: 'CURRENT',
              currentAddress: true,
              city: 'lucknow',
              state: 'WA',
              zipCode: '94501',
              country: '',
              countyName: '',
              inCity: false,
              periodOfResidenceDurationType: 'YEAR',
              moreThanTwoYears: false,
              capencyVerification: false,
            },
          ],
        },
      ],
    };
    const result = shouldUpdateGlobalWarnings(oldDeal, newDeal);
    expect(result).toBe(true);
  });
});
