import React, { Component } from 'react';
import { defaultMemoize } from 'reselect';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { compose } from 'recompose';
import classNames from 'classnames';
import _has from 'lodash/has';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import _map from 'lodash/map';
import _partial from 'lodash/partial';
import _noop from 'lodash/noop';
import _concat from 'lodash/concat';
import _size from 'lodash/size';
import _omit from 'lodash/omit';
import _values from 'lodash/values';
import _find from 'lodash/find';
import _filter from 'lodash/filter';
import _reduce from 'lodash/reduce';
import _toLower from 'lodash/toLower';
import _toString from 'lodash/toString';
import _pick from 'lodash/pick';
import _sortBy from 'lodash/sortBy';
import _reverse from 'lodash/reverse';
import _isFunction from 'lodash/isFunction';
import _head from 'lodash/head';
import _drop from 'lodash/drop';
import _isEqual from 'lodash/isEqual';

import { withTekionConversion } from '@tekion/tekion-conversion-web';
import { tget, getOS } from '@tekion/tekion-base/utils/general';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { OS_TYPES } from '@tekion/tekion-base/constants/general';
import { NOTES_ASSET_TYPE } from '@tekion/tekion-base/constants/desking/contracts';
import { isInchcape, isInchcapeOrRRG } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
import { MODAL_TYPE, showGlobalModal } from '@tekion/tekion-components/src/emitters/ModalEventEmitter';
import APPOINTMENT_MODAL_FIELDS from 'twidgets/appServices/crm/constants/formFieldIds/appointment';
import { getLeadAssignee } from 'twidgets/appServices/crm/organisms/appointmentInfoModal/appointmentModal.helpers';
import { getLeadName } from 'twidgets/appServices/crm/helpers/lead';
import { CLIENT } from '@tekion/tekion-base/constants/crm';
import { CRM_ENTITIES } from 'twidgets/appServices/crm/constants/common';
import { getLeadDetails } from '@tekion/tekion-base/services/crm/leads.api';
import CUSTOMER_IDS from 'twidgets/appServices/crm/constants/formFieldIds/customer';
import Button from '@tekion/tekion-components/src/atoms/Button';
import FontIcon, { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';
import Heading from '@tekion/tekion-components/src/atoms/Heading';
import Ellipsis from '@tekion/tekion-components/src/atoms/Ellipsis';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import FullPageModalDrawer from '@tekion/tekion-components/src/organisms/FullPageModalDrawer/FullPageModalDrawer';
import WithMandatoryReviewHOC from 'organisms/MandatoryAndReviewForm/MandatoryReviewHOC';
import Content from '@tekion/tekion-components/src/atoms/Content';
import { toaster } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import { POPOVER_PLACEMENT } from '@tekion/tekion-components/src/molecules/popover';
import { CommunicationDefaultWidget } from 'twidgets/organisms/communications/nonEnterpriseCommunication/organisms/chatWidget';
import ASSET_TYPES from 'twidgets/organisms/communications/constants/communications.assetTypes';
import VehicleMismatchStickyBanner from '@tekion/tekion-widgets/src/organisms/VehicleMismatchStickyBanner';
import { ALERTS } from '@tekion/tekion-business/src/appServices/vehicleInventory/constants/vehicleAlerts';
import { handleFetchUsersToTagInNotes } from '@tekion/tekion-business/src/appServices/sales/utils/sales.utils';
import { DOCUMENT_SIGNING_PHASES } from '@tekion/tekion-business/src/constants/sales/eSign';
import { EXTERNAL_DOCUMENT_SOURCES } from '@tekion/tekion-business/src/constants/sales/documents';
import {
  getSpecificAlertMessageForVehicle,
  getFilteredAlerts,
  hasVehicleAlert,
} from '@tekion/tekion-business/src/appServices/vehicleInventory/helpers/vehicleAlerts';

import { API_STATUS, MESSAGES, TOASTER_STATUS } from 'constants/pages';
import {
  DEAL_JACKET_LABEL_TYPES,
  LABEL_CATEGORY,
} from 'twidgets/appServices/sales/organisms/labelpreview/labelPreview.constants';
import VehicleAPI from 'pages/StartDeal/Components/Vehicle/vehicle.api';
import DeskingAPI from 'pages/desking/desking.api';
import withPrintConsumer from 'pages/desking/withPrint.consumer';
import DealComparison from 'pages/desking/components/cashiering/components/DealComparison/DealComparison';
import DealSheetSetupAPI from 'pages/dealSheetSetup/DealSheetSetup.api';
import { hasCustomerEdit, hasVehicleEdit, hasViewSubscreensInBookedDeals } from 'permissions/desking.permissions';
import withCRMLeadHeartbeatStatus from 'pages/desking/withCRMLeadHeartbeatStatus';
import GlobalWarning from 'pages/desking/components/globalWarnings';
import { showDealCompareForDMS } from 'pages/desking/components/cashiering/components/DealComparison/dealComparison.util';
import EditVehicleModal from 'organisms/editVehicleModal';
import gdprConfigReader from 'readers/gdpr.reader';
import * as DealReader from '@tekion/tekion-base/marketScan/readers/deal.reader';

import { IS_LIVE_SESSION } from 'constants/fni';
import UpdateDeal from 'pages/StartDeal';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';
import { DEAL_JACKET } from 'pages/deallist/deal.constants';
import ModalDropDown from 'molecules/modalDropDown';
import DropdownActions from 'molecules/dropdownActions';
import UpdateModal from 'organisms/updateModal/UpdateModalStatic';
import NotesIconAndModal from 'organisms/notesIconAndModal';
import * as ContractDateService from 'pages/desking/components/subHeader/subHeaderTabs/contractDate/contractDate.service';
import {
  getDealNumber,
  getDealId,
  getCustomers,
  getPrimaryVehicleId,
  getPrimaryVehicleVIN,
  getBuyer,
  getDealType,
  getFormCustomer,
  getFormTitle,
  getPrimaryVehicleStatus,
  getLeadId,
  getPrimaryVehicleStockNumber,
  getDealCreatedAtSite,
  getDealExternalSource,
  getDealStatus,
  getPrimaryVehicleDealVehicleId,
} from '@tekion/tekion-base/marketScan/readers/deal.reader';
import { getSelectedColumn, getPaymentType } from '@tekion/tekion-base/marketScan/readers/columnData.reader';
import {
  isConsumerCreditApplicationEnabled,
  deductCustomerCashfromAmountFinancedforCashDealEnabled,
  getCreateAppointmentsFromDeals,
  getPaymentOptionConfigs,
  getDisableDeliveryAppointmentStatus,
  getVehicleDealHeaderIdentifier,
} from '@tekion/tekion-base/marketScan/readers/salesSetup.reader';
import { FEATURE_NAME, withExperienceEngineConsumer } from '@tekion/tekion-widgets/src/experienceEngine';

import CustomerAPI from 'commonActions/apis/customer.api';
import { DEAL_UPDATE_QUEUE } from 'commonActions/apis/dealUpdateQueue.api';
import { DEAL_TYPES } from '@tekion/tekion-base/constants/deal/type';
import {
  isTempCustomer,
  getCustomerNumberForDisplay,
  getCustomerAddressDetails,
} from '@tekion/tekion-base/marketScan/readers/customer.reader';
import { SEARCH_USER_BY_ALL_PERSONAS } from '@tekion/tekion-base/constants/deal/common';
import { isTempVehicle } from '@tekion/tekion-base/marketScan/readers/vehicle.reader';
import withDeskingContext from 'pages/desking/withDeskingContext';
import Label from '@tekion/tekion-components/src/atoms/Label';
import Tooltip, { TOOLTIP_PLACEMENT } from '@tekion/tekion-components/src/atoms/tooltip';
import {
  getPdfLoader,
  formatPaymentOptionsFromSalesSetup,
  getLabelFromPaymentOptions,
  getSubStatusAndIdFromValue,
} from '@tekion/tekion-base/marketScan/utils/desking.utils';
import { uuid } from 'utils';
import CalcEngineProperties from 'utils/CalcEngineProperties';
import { getRequiredMenuConfigParamsForCoordinates } from 'utils/fniUtils';
// import { getBuyBackValuation } from 'organisms/BuybackValueModal/buybackValue.helper';
import ConfirmationModal from 'molecules/ConfirmationModal';
import DealSheetPreview from 'pages/desking/components/dealSheetPreview';
import StopShare from 'pages/desking/components/digitalRetail/components/StopShare';
import {
  getPrimaryVehicleData,
  getCustomersData,
  getSelectedPaymentObject,
  isAnyPdfShared,
} from '@tekion/tekion-base/marketScan/readers/desking.reader';
import CRMLeadHeartbeat from 'utils/CRMLeadHeartbeat';

import CommonApi from 'commonActions/apis';
import {
  removeEmptyCustomerFormFields,
  isBusinessOrCustomerTaxClassificationChanged,
} from 'organisms/customerForm/CustomerForm.utils';
import { getFiltererdDocuments, getSignedPackagesInDealJacketSession } from 'utils/document.util';
import { getBuyBackSheetPayload } from 'organisms/BuybackValueModal/buybackValue.helper';
import { TAB_KEYS as EDIT_VEHICLE_MODAL_TAB_KEYS } from 'organisms/editVehicleModal/editVehicleTabs.constants';

import { getTargetURLForPDF, getTargetURLForMultiPDF } from '@tekion/tekion-base/utils/sales/exportPaths';
import { showDrawer } from '@tekion/tekion-components/src/organisms/FullPageModalDrawer';
import DealEnv from 'utils/dealEnv';
import { isArcLiteProgram } from 'utils/program.utils';
import { NOTIFICATION_TYPES, PDFGenerationEvents } from 'pages/desking/utils/pdfGeneration.utils';

import { SOURCE_TYPE, LEFT_PANEL_ITEMS, DMS_WITH_DEAL_COMPARE_SUPPORT } from 'pages/desking/desking.constants';
import { TAB_KEYS } from '@tekion/tekion-base/marketScan/constants/startDeal.constants';
import VmStartButton from 'pages/desking/components/VirtualMeeting/VmStartButton';

import { BASE_REDUCER_KEY } from '../../../constants/constants';
import {
  PRINT_ASSET_TYPES,
  PRINT_ASSET_TYPES_VS_ORIENTATION,
  PRINT_ASSET_TYPES_VS_SHEET_TYPE,
} from '../../../constants/printAssetTypes';
import ROUTES from '../../../constants/routes';

import styles from './DeskingHeader.module.scss';
import DealNumberHeaderItem from './DealNumberHeaderItem';
import CustomersHeaderItem from './CustomersHeaderItem';
import DealJacket from './dealJacket';
import DealInfoPreviewModal from './DealInfoPreviewModal';
import {
  filterMovedDocs,
  getModifiedDocs,
  filterRequiredDocs,
  getModifiedPackageDocs,
} from './dealJacket/dealJacket.util';
import CustomerForm from './customerItem';
import BuildCustomerForm from '../../StartDeal/Components/Customer';
import YMMHeaderItem from './ymmHeaderItem';
import PriceHeaderItem from './PriceHeaderItem';
import RecentDeals from './recentDeals';
import {
  getSheetLevelFnIMenuConfig,
  getAllVehicleAlerts,
  fetchCustomerAndReservedOrderDates,
} from '../desking.actions';

import ymmHeaderStyles from './ymmHeaderItem/ymmHeaderItem.module.scss';
import customerHeaderStyles from './customersHeaderItem.module.scss';
import InfoIconWithPopOver from '../../../organisms/infoIconWithPopOver/InfoIconWithPopOver';
import { printOrShareWithDoc } from '../components/invoicing/Invoicing.events';
import { isCanadaDealer } from '../../../utils/dealerUtils';
import { selectGDPRConfig } from '../components/documentsV2/documentsV2.selectors';
import { getDealJacketGdprConfig } from '../desking.selectors';
import { getLastFourDigitsForSSN } from '../components/subHeader/subHeaderTabs/tradeInValuation/tradeInValuation.utils';
import { deliveryLocation } from './dealInfoPreview.constants';
import AppointmentDropdown from './AppointmentDropdown';
import DealJacketViewPermissionWarning from './DealJacketViewPermissionWarning';
import withCustomerFormErrorsState from '../../../organisms/customerForm/withCustomerFormErrorsState';
import { getAPIError } from '../../../utils/error.reader';
import {
  getPayloadForUploadDocuments,
  showCustomerSwitchWarnings,
  showCustomerSwitchWarningsForApprovedColumn,
  getShareButtonActions,
  getBuyerCobuyerDetails,
  saveSSN,
  shouldUpdateGlobalWarnings,
} from './deskingFirstLevelHeader.helpers';
import { getFourSquareTemplates, getMasterTemplate } from '../../dealSheetSetup/DealSheetSetup.helpers';
import { hasDealJacketViewOrEdit } from '../../../permissions/documents.permissions';
import {
  isAnyApprovedColumnAdded,
  shouldShowCustomerLoginModalOrWarning,
  getCustomerTabClosableFlag,
} from '../desking.helpers';
import DealComparisonButtonV2 from '../../../organisms/DealComparisonButton';

class DeskingFirstLevelHeader extends Component {
  getShareButtonActions = defaultMemoize(getShareButtonActions);

  static propTypes = {
    navigate: PropTypes.func.isRequired,
    location: PropTypes.object.isRequired,
    deal: PropTypes.object.isRequired,
    toggleDealSummaryVisibility: PropTypes.func.isRequired,
    DeskingActions: PropTypes.object.isRequired,
    FniMenuActions: PropTypes.object.isRequired,
    DigitalRetailActions: PropTypes.object.isRequired,
    deskingpaymentDetails: PropTypes.array.isRequired,
    vehiclesInfo: PropTypes.object.isRequired,
    programInfo: PropTypes.object.isRequired,
    contentHeight: PropTypes.number.isRequired,
    sendToPrint: PropTypes.func.isRequired,
    pdfLoaders: PropTypes.object,
    getMarketScanData: PropTypes.func.isRequired,
    isDealViewOnly: PropTypes.bool,
    isDealAcquired: PropTypes.bool,
    saveDealData: PropTypes.func.isRequired,
    scanDocuments: PropTypes.array.isRequired,
    onVehiclesAndCustomersUpdated: PropTypes.func.isRequired,
    onVehiclesUpdated: PropTypes.func.isRequired,
    trimInfos: PropTypes.object,
    defaultsheetConfigs: PropTypes.object,
    dealJacket: PropTypes.array,
    DealsActions: PropTypes.object.isRequired,
    CommonActions: PropTypes.object.isRequired,
    notes: PropTypes.object,
    handlePrintFNISheet: PropTypes.func,
    dispatch: PropTypes.object.isRequired,
    isClosedOrSoldDeal: PropTypes.bool,
    conciergeDetails: PropTypes.object.isRequired,
    isFetchingMarketScanData: PropTypes.bool.isRequired,
    addDefaultCostAdjustments: PropTypes.func.isRequired,
    handlePrintDealSheet: PropTypes.func,
    packageDocuments: PropTypes.array,
    dealSubStatusList: PropTypes.array,
    vehicleSubTypesForNewVehicleProgram: PropTypes.array,
    rolesForAssignee: PropTypes.object.isRequired,
    getMarketScanPayloadObject: PropTypes.func.isRequired,
    disablePricingEditSettings: PropTypes.bool.isRequired,
    documentsViewPreferences: PropTypes.object.isRequired,
    agingField: PropTypes.string,
    isMultiVehicleDealSheet: PropTypes.bool,
    conciergeSetupConfig: PropTypes.object,
    blockScreen: PropTypes.func,
    unBlockScreen: PropTypes.func,
    routeOneErrors: PropTypes.object,
    menuPackagesList: PropTypes.object,
    downloadDSPDocuments: PropTypes.func.isRequired,
    crmHeartbeatStatus: PropTypes.bool,
    isDealDirty: PropTypes.bool,
    documentsGdprConfig: PropTypes.object,
    signingPhase: PropTypes.string,
    isConciergeActivated: PropTypes.bool.isRequired,
    canGoBackToDealList: PropTypes.func.isRequired,
    isEnterpriseV2Enabled: PropTypes.bool,
    getFeatureValue: PropTypes.func.isRequired,
    isFetchingUpdateDealStaus: PropTypes.bool,
    motabilityLead: PropTypes.bool,
  };

  static defaultProps = {
    blockScreen: _noop,
    unBlockScreen: _noop,
    pdfLoaders: EMPTY_OBJECT,
    isDealViewOnly: false,
    isDealAcquired: false,
    trimInfos: EMPTY_OBJECT,
    defaultsheetConfigs: EMPTY_OBJECT,
    notes: EMPTY_OBJECT,
    dealJacket: [],
    handlePrintFNISheet: _noop,
    isClosedOrSoldDeal: false,
    handlePrintDealSheet: _noop,
    packageDocuments: EMPTY_ARRAY,
    dealSubStatusList: EMPTY_ARRAY,
    vehicleSubTypesForNewVehicleProgram: EMPTY_ARRAY,
    agingField: 'entryTime',
    isMultiVehicleDealSheet: false,
    conciergeSetupConfig: EMPTY_OBJECT,
    routeOneErrors: EMPTY_OBJECT,
    menuPackagesList: EMPTY_OBJECT,
    crmHeartbeatStatus: false,
    isDealDirty: false,
    documentsGdprConfig: EMPTY_OBJECT,
    signingPhase: DOCUMENT_SIGNING_PHASES.DEFAULT,
    isEnterpriseV2Enabled: false,
    isFetchingUpdateDealStaus: false,
    motabilityLead: false,
  };

  labelPreviewRef = React.createRef();

  state = {
    showDealSheetPreview: false,
    showCanadaDealSheetPreview: false,
    showFourSquareSheetPreview: false,
    skipDealJacketVisiblityChange: false,
    isFNISheetPrinting: false,
    customerTabVisible: false,
    isDealJacketSigning: false,
    dealJacketSubmitCb: _noop,
    initialPackageDocs: EMPTY_OBJECT,
    dealJacketSessionId: '',
    labelConfig: {},
    alerts: EMPTY_OBJECT,
    hasOptionAlerts: false,
    showOptionsMismatchBanner: false,
    customOrderDetails: EMPTY_OBJECT,
    reservationOrderDetails: EMPTY_OBJECT,
    isCrmNotEnabledAndDuplicationPreventionEnabled: false,
    dealJacketTabVisible: false,
  };

  concatDocs = defaultMemoize((scanDocuments, dealJacket, packageDocuments, sppPackageDocuments) => {
    const allDocuments = _concat(
      dealJacket,
      packageDocuments,
      sppPackageDocuments,
      filterMovedDocs(getModifiedDocs(scanDocuments, dealJacket))
    );

    // filters out docs based on permissions
    return filterRequiredDocs(allDocuments);
  });

  componentDidMount() {
    const { DeskingActions } = this.props;
    this.getLabelConfiguration();
    DeskingActions.getDealPreferences();
    this.getAllAlerts();
    if (!isCanadaDealer()) {
      this.getDealSetupTemplates();
    }
  }

  shouldComponentUpdate(_, nextState) {
    const { isDealJacketSigning, dealJacketSubmitCb } = this.state;
    if (
      isDealJacketSigning !== nextState.isDealJacketSigning ||
      _toString(dealJacketSubmitCb) !== _toString(nextState.dealJacketSubmitCb)
    ) {
      return false;
    }

    return true;
  }

  componentDidUpdate(prevProps) {
    const { deal } = this.props;
    const currentVehicleId = getPrimaryVehicleId(deal);
    const prevVehicleId = getPrimaryVehicleId(prevProps.deal);
    const orderId = _get(deal, 'orderId');
    const reservationId = _get(deal, 'reservationId');
    const prevOrderId = _get(prevProps?.deal, 'orderId');
    const prevReservationId = _get(prevProps?.deal, 'reservationId');
    const sourceType = _get(deal, 'sourceType');
    const isSourceOms = _isEqual(sourceType, SOURCE_TYPE.OMS);
    if (currentVehicleId !== prevVehicleId) {
      this.getAllAlerts();
    }
    if (
      DealerPropertyHelper.isAECProgram() &&
      isSourceOms &&
      (orderId !== prevOrderId || reservationId !== prevReservationId)
    ) {
      this.getCustomerAndReservedOrderDates();
    }
  }

  getAllAlerts = async () => {
    const { deal } = this.props;
    const vehicleId = getPrimaryVehicleId(deal);
    if (vehicleId) {
      try {
        const alerts = await getAllVehicleAlerts([vehicleId])();
        this.setState({ alerts: alerts || EMPTY_OBJECT });
        const fileteredVehicleAlerts = getFilteredAlerts(vehicleId, alerts, ALERTS.MULTI_STYLE_ID_MISMATCH);
        const hasOptionAlerts = hasVehicleAlert(vehicleId, fileteredVehicleAlerts);
        this.setState({ hasOptionAlerts });
        this.setState({ showOptionsMismatchBanner: hasOptionAlerts });
      } catch (err) {
        this.setState({ alerts: EMPTY_OBJECT });
      }
    }
  };

  getDealSetupTemplates = async () => {
    const dealSetupTemplates = await DealSheetSetupAPI.getDealSheetTemplates();
    this.setState({ dealSetupTemplates });
  };

  getCustomerAndReservedOrderDates = async () => {
    const { deal } = this.props;
    const orderId = _get(deal, 'orderId');
    const reservationId = _get(deal, 'reservationId');
    let details = {
      customOrderDetails: { id: orderId, orderNumber: EMPTY_STRING, createdTime: EMPTY_STRING },
      reservationOrderDetails: { id: reservationId, orderNumber: EMPTY_STRING, createdTime: EMPTY_STRING },
    };
    if (!_isEmpty(orderId) || !_isEmpty(reservationId)) {
      const [customOrderDetails, reservationOrderDetails] = await fetchCustomerAndReservedOrderDates(
        orderId,
        reservationId
      );
      details = { customOrderDetails, reservationOrderDetails };
    }
    this.setState({ ...details });
  };

  getLabelConfiguration = async () => {
    const config = await CommonApi.getLabelConfiguration();
    const dealJacketConfig = _find(config, { labelCategory: LABEL_CATEGORY.DEAL_JACKET }) || {};
    this.setState({
      labelConfig: dealJacketConfig,
    });
  };

  getRecallData = async () => {
    const { deal } = this.props;
    const vin = getPrimaryVehicleVIN(deal);
    const stockNumber = getPrimaryVehicleStockNumber(deal);
    const response = await VehicleAPI.fetchROForRV({
      vin,
      stockNumber,
    });
    const data = _get(response, 'rawResponse.data') || [];
    return {
      recallDetails: [],
      roDetails: data,
    };
  };

  onClickOfPrintLabel = (dealJacketLabelType, title) => async () => {
    const { deal } = this.props;
    const { labelConfig } = this.state;
    const { dealNumber } = deal;
    const stockNumber = getPrimaryVehicleStockNumber(deal);
    const { labelOrientation } = labelConfig;
    const selectedLabelConfig =
      _find(
        _get(labelConfig, 'labels') || [],
        ({ labelType, selected, orientation }) =>
          selected && labelType === DEAL_JACKET_LABEL_TYPES.DEAL_INFO && labelOrientation === orientation
      ) || {};
    const payload =
      dealJacketLabelType === DEAL_JACKET_LABEL_TYPES.VEHICLE_INFO
        ? {
            labelType: dealJacketLabelType,
            printInfo: [
              {
                stockNumber,
                noOfCopies: 1,
              },
            ],
          }
        : {
            labelType: dealJacketLabelType,
            printInfo: [
              {
                dealNumber,
                noOfCopies: 1,
              },
            ],
          };
    const response = await this.labelPreviewRef.current.show({
      title,
      selectedLabelConfig,
      zplPayload: { ...payload, printLabel: false },
    });
    if (response) {
      const status = await CommonApi.printLabels(payload);
      if (_toLower(status) === API_STATUS.SUCCESS) toaster(TOASTER_STATUS.SUCCESS, __('Label printing initiated.'));
      else toaster(TOASTER_STATUS.ERROR, __('Label printing failed.'));
    }
  };

  shouldHideFourSquareBoxSheet = () => {
    if (isCanadaDealer()) {
      return false;
    }

    const { dealSetupTemplates } = this.state;
    const fourSquareMasterTemplate = getMasterTemplate(getFourSquareTemplates(dealSetupTemplates));
    const fourSquareBoxSheetEnabled = tget(fourSquareMasterTemplate, 'fourSquareBoxSheetEnabled', true);
    return !fourSquareBoxSheetEnabled;
  };

  hideOptionsMismatchBanner = () => this.setState({ showOptionsMismatchBanner: false });

  removeOptionAlert = () => this.setState({ hasOptionAlerts: false });

  removeOptionsMismatchFlag = async () => {
    const { deal } = this.props;
    const vehicleId = getPrimaryVehicleId(deal);
    const payload = { ids: [vehicleId], action: { RESOLVE_ALERT: ALERTS.OPTION_MISMATCH } };

    try {
      const response = await DeskingAPI.updateVehicleAlerts(payload);
      this.removeOptionAlert();
      toaster(TOASTER_STATUS.SUCCESS, __('Vehicle alert successfully updated'));
      return response;
    } catch (err) {
      toaster(TOASTER_STATUS.ERROR, __('Failed to update the vehicle alert'));
      return null;
    }
  };

  getDealJacketDocuments = async () => {
    const {
      DeskingActions,
      deal: { dealNumber = EMPTY_STRING },
    } = this.props;
    if (dealNumber) {
      const dealJacketDocuments = await DeskingActions.fetchDealJacket(dealNumber);
      this.setState({ dealJacketDocuments: _reverse(_sortBy(dealJacketDocuments, ['createdTime'])) });
    }
  };

  getInvoicesFromDealJacket = allDocuments => {
    if (allDocuments?.length) {
      const invoices = _filter(allDocuments, ({ sheetType }) =>
        [
          EXTERNAL_DOCUMENT_SOURCES.SALES_VEHICLE_INVOICE_SHEET,
          EXTERNAL_DOCUMENT_SOURCES.PURCHASE_VEHICLE_INVOICE_SHEET,
          EXTERNAL_DOCUMENT_SOURCES.SUPPLEMENTARY_INVOICE_SHEET,
        ].includes(sheetType)
      );
      return invoices;
    }
    return [];
  };

  toggleFNIPrint = () => {
    const { isFNISheetPrinting } = this.state;
    this.setState({
      isFNISheetPrinting: !isFNISheetPrinting,
    });
  };

  stopAllPdfSharing = () => {
    const { DigitalRetailActions, deal } = this.props;
    StopShare.isStopping(true);
    DigitalRetailActions.stopAllSharing(getDealNumber(deal), EMPTY_ARRAY, true);
    StopShare.hide();
  };

  getStopAllSharingBtn = showBtn => {
    if (!showBtn) return null;
    return (
      <PropertyControlledComponent controllerProperty={showBtn}>
        <Button view="tertiary" onClick={this.stopAllPdfSharing}>
          {__('Stop all Sharing')}
        </Button>
      </PropertyControlledComponent>
    );
  };

  canOpenShareMenuItem = () => {
    const { isFetchingMarketScanData, isDealDirty, isFetchingUpdateDealStaus } = this.props;
    if (isFetchingMarketScanData || isDealDirty || isFetchingUpdateDealStaus) {
      toaster(TOASTER_STATUS.INFO, MESSAGES.SAVE_IN_PROGRESS);
      return false;
    }
    return true;
  };

  getUpdateModalRef = ref => {
    this.updateModal = ref;
  };

  hideModal = () => {
    if (this.updateModal) {
      this.updateModal.hide();
    }
  };

  updateDeal = tab => {
    const { deal, crmHeartbeatStatus } = this.props;
    if (crmHeartbeatStatus) return;
    const dealNumber = getDealNumber(deal);
    this.updateModal.show({
      component: UpdateDeal,
      componentProps: {
        onCloseModal: this.hideModal,
        onClose: this.hideModal,
        onDealUpdate: this.handleDealUpdate,
        updateDeal: true,
        dealStatus: _get(deal, 'status'),
        program: _get(deal, 'program'),
        dealNumber,
        defaultActiveKey: tab,
      },
    });
    CRMLeadHeartbeat.updateCRMLeadHeartbeatStatus({ vehicleTabOpened: true }, { isDelayedUpdate: false });
  };

  clearLocationHash = () => {
    window.location.hash = EMPTY_STRING;
  };

  clickBack = () => {
    const { navigate, location, canGoBackToDealList } = this.props;
    if (!_isEmpty(DEAL_UPDATE_QUEUE)) {
      toaster('warn', __(`${DEAL_UPDATE_QUEUE.length} pending requests..!`));
    }

    if (!canGoBackToDealList()) {
      return;
    }

    navigate(`/sales${ROUTES.DEAL_LIST}`);
  };

  handlePrintDealSheet = (
    action,
    isMultiVehicleDealSheet,
    emails,
    isFourSquareSheet,
    isCanadaSheet,
    currentSessionId,
    isCustomerPii
  ) => {
    const {
      deal: { dealNumber = EMPTY_STRING },
      handlePrintDealSheet,
    } = this.props;
    handlePrintDealSheet({
      dealNumber,
      action,
      isMultiVehicleDealSheet,
      emails,
      isFourSquareSheet,
      isCanadaSheet,
      uniqueId: currentSessionId,
      isCustomerPii,
    });
    DeskingAPI.createAuditLogForDealSheet(dealNumber);
  };

  handleDealSheetPreview = () => {
    if (DealerPropertyHelper.isPreviewPdfEnabled()) {
      this.commonPrintHandler(ROUTES.DEAL_SHEET, PRINT_ASSET_TYPES.DEALSHEET);
      return;
    }
    if (!this.canOpenShareMenuItem()) {
      return;
    }
    this.setState(prevState => ({
      showDealSheetPreview: !prevState.showDealSheetPreview,
    }));
  };

  handleCanadaDealSheetPreview = () => {
    if (DealerPropertyHelper.isPreviewPdfEnabled()) {
      this.commonPrintHandler(ROUTES.CANADA_DEAL_SHEET, PRINT_ASSET_TYPES.CANADA_SHEET);
      return;
    }
    if (!this.canOpenShareMenuItem()) {
      return;
    }
    this.setState(prevState => ({
      showCanadaDealSheetPreview: !prevState.showCanadaDealSheetPreview,
    }));
  };

  handlePrintFourSquareSheetPreview = () => {
    if (DealerPropertyHelper.isPreviewPdfEnabled()) {
      this.commonPrintHandler(ROUTES.DEAL_SHEET, PRINT_ASSET_TYPES.FOUR_SQUARE_BOX_SHEET);
      return;
    }
    if (!this.canOpenShareMenuItem()) {
      return;
    }
    this.setState(prevState => ({
      showFourSquareSheetPreview: !prevState.showFourSquareSheetPreview,
    }));
  };

  handlePrintMultiVehicleDealSheet = () => {
    if (DealerPropertyHelper.isPreviewPdfEnabled()) {
      this.commonPrintHandler(ROUTES.DEAL_SHEET, PRINT_ASSET_TYPES.DEALSHEET, true);
      return;
    }
    this.handlePrintDealSheet(null, true);
  };

  handlePrintFourSquareSheet = (
    action,
    _,
    emails,
    isFourSquareSheet,
    isCanadaSheet,
    currentSessionId,
    isCustomerPii
  ) => {
    this.handlePrintDealSheet(action, false, emails, isFourSquareSheet, isCanadaSheet, currentSessionId, isCustomerPii);
  };

  commonPrintHandler = (route, sheetType, isMultiVehicleDealSheet) => {
    if (!this.canOpenShareMenuItem()) {
      return;
    }

    const {
      deal: { dealNumber = EMPTY_STRING },
      sendToPrint,
    } = this.props;
    // history.push(`/export/desking/${dealNumber}/coverSheet`);
    const isPreviewPdfEnabled = DealerPropertyHelper.isPreviewPdfEnabled();
    const targetUrlFunc = isPreviewPdfEnabled ? getTargetURLForMultiPDF : getTargetURLForPDF;
    const payload = {
      targetUrl: targetUrlFunc(dealNumber, route, isMultiVehicleDealSheet),
      // targetUrl: `/exports/sales/desking/${dealNumber}${ROUTES.RECAP_PDF}?enhancedExport=pdf`,
      assetType: sheetType,
      dealNumber,
      isMultiVehicleDealSheet,
      extras: {
        pdfType: PRINT_ASSET_TYPES_VS_SHEET_TYPE[sheetType],
      },
      isArcLiteProgramEnabled: isArcLiteProgram(),
    };

    if (isPreviewPdfEnabled) {
      PDFGenerationEvents.emit(NOTIFICATION_TYPES.OPEN_PREVIEW, {
        payload,
      });
    } else {
      sendToPrint(payload);
    }
  };

  handlePrintCoverSheet = () => {
    if (!this.canOpenShareMenuItem()) {
      return;
    }
    const {
      deal: { dealNumber = EMPTY_STRING },
      sendToPrint,
    } = this.props;
    // history.push(`/export/desking/${dealNumber}/coverSheet`);

    const payload = {
      targetUrl: getTargetURLForPDF(dealNumber, ROUTES.COVER_SHEET),
      assetType: PRINT_ASSET_TYPES.COVERSHEET,
      dealNumber,
      extras: {
        pdfType: PRINT_ASSET_TYPES_VS_SHEET_TYPE[PRINT_ASSET_TYPES.COVERSHEET],
      },
      isCustomerPii: true,
    };

    if (DealerPropertyHelper.isPreviewPdfEnabled()) {
      PDFGenerationEvents.emit(NOTIFICATION_TYPES.OPEN_PREVIEW, {
        payload,
      });
    } else {
      sendToPrint(payload);
    }
  };

  handlePrintLeaseCommparisonSheet = () => {
    if (!this.canOpenShareMenuItem()) {
      return;
    }
    const {
      deal: { dealNumber = EMPTY_STRING },
      sendToPrint,
    } = this.props;
    // history.push(`/export/desking/${dealNumber}/coverSheet`);
    const payload = {
      targetUrl: getTargetURLForPDF(dealNumber, ROUTES.LEASE_COMPARISON_SHEET),
      assetType: PRINT_ASSET_TYPES.LEASE_COMPARISON_SHEET,
      dealNumber,
      extras: {
        pdfType: PRINT_ASSET_TYPES_VS_SHEET_TYPE[PRINT_ASSET_TYPES.LEASE_COMPARISON_SHEET],
      },
    };
    sendToPrint(payload);
  };

  handleVehicleSalesOrderSheet = () => {
    if (!this.canOpenShareMenuItem()) {
      return;
    }
    const {
      deal: { dealNumber = EMPTY_STRING },
      sendToPrint,
    } = this.props;
    const payload = {
      targetUrl: getTargetURLForPDF(dealNumber, ROUTES.VEHICLE_SALES_ORDER_SHEET),
      assetType: PRINT_ASSET_TYPES.VEHICLE_SALES_ORDER_SHEET,
      dealNumber,
      data: { dealNumber },
      extras: {
        deviceType: 'WEB',
        viewType: PRINT_ASSET_TYPES_VS_ORIENTATION[PRINT_ASSET_TYPES.VEHICLE_SALES_ORDER_SHEET],
        pdfType: PRINT_ASSET_TYPES_VS_SHEET_TYPE[PRINT_ASSET_TYPES.VEHICLE_SALES_ORDER_SHEET],
      },
    };
    sendToPrint(payload);
  };

  handleInvoiceSheet = async () => {
    if (!this.canOpenShareMenuItem()) {
      return;
    }
    const {
      deal: { dealNumber = EMPTY_STRING },
    } = this.props;
    const { dealJacketDocuments } = this.state;
    const invoices = this.getInvoicesFromDealJacket(dealJacketDocuments);
    printOrShareWithDoc({
      dealNumber,
      documents: invoices || [],
    });
  };

  handlePrintBuyBackSheet = () => {
    if (!this.canOpenShareMenuItem()) {
      return;
    }
    const {
      deal: { dealNumber = EMPTY_STRING },
      sendToPrint,
      getFormattedDateAndTime,
    } = this.props;
    sendToPrint({
      ...getBuyBackSheetPayload({ dealNumber }, getFormattedDateAndTime),
      dealNumber,
      pickValuesFromDeal: true,
      extras: {
        deviceType: 'WEB',
        viewType: PRINT_ASSET_TYPES_VS_ORIENTATION[PRINT_ASSET_TYPES.BUY_BACK_VALUE_SHEET],
        pdfType: PRINT_ASSET_TYPES_VS_SHEET_TYPE[PRINT_ASSET_TYPES.BUY_BACK_VALUE_SHEET],
      },
    });
  };

  handlePrintMarginSheet = () => {
    if (!this.canOpenShareMenuItem()) {
      return;
    }
    const {
      deal: { dealNumber = EMPTY_STRING },
      sendToPrint,
    } = this.props;
    const payload = {
      targetUrl: getTargetURLForPDF(dealNumber, ROUTES.MARGIN_SHEET),
      assetType: PRINT_ASSET_TYPES.MARGIN_SHEET,
      dealNumber,
      extras: {
        deviceType: 'WEB',
        viewType: PRINT_ASSET_TYPES_VS_ORIENTATION[PRINT_ASSET_TYPES.MARGIN_SHEET],
        pdfType: PRINT_ASSET_TYPES_VS_SHEET_TYPE[PRINT_ASSET_TYPES.MARGIN_SHEET],
      },
    };
    sendToPrint(payload);
  };

  handlePrintRecap = () => {
    if (!this.canOpenShareMenuItem()) {
      return;
    }

    const {
      deal: { dealNumber = EMPTY_STRING },
      sendToPrint,
    } = this.props;
    // history.push(`/export/desking/${dealNumber}/coverSheet`);
    const isPreviewPdfEnabled = DealerPropertyHelper.isPreviewPdfEnabled();
    const targetUrlFunc = isPreviewPdfEnabled ? getTargetURLForMultiPDF : getTargetURLForPDF;
    const payload = {
      targetUrl: targetUrlFunc(dealNumber, ROUTES.RECAP_PDF),
      // targetUrl: `/exports/sales/desking/${dealNumber}${ROUTES.RECAP_PDF}?enhancedExport=pdf`,
      assetType: PRINT_ASSET_TYPES.RECAPSHEET,
      dealNumber,
      extras: {
        pdfType: PRINT_ASSET_TYPES_VS_SHEET_TYPE[PRINT_ASSET_TYPES.RECAPSHEET],
      },
      isArcLiteProgramEnabled: isArcLiteProgram(),
    };

    if (isPreviewPdfEnabled) {
      PDFGenerationEvents.emit(NOTIFICATION_TYPES.OPEN_PREVIEW, {
        payload,
      });
    } else {
      sendToPrint(payload);
    }
  };

  handlePrintFNISheet = async () => {
    if (!this.canOpenShareMenuItem()) {
      return;
    }
    const {
      deal: { dealNumber = EMPTY_STRING },
      handlePrintFNISheet,
      menuPackagesList,
    } = this.props;

    let extras = { [IS_LIVE_SESSION]: true };

    if (DealerPropertyHelper.isEnhancedDocumentsTabEnabled()) {
      const menuConfig = await getSheetLevelFnIMenuConfig(PRINT_ASSET_TYPES.FNI_MENU_SHEET, dealNumber);
      const fnIMenuPackages = _get(menuPackagesList, 'fnIMenuPackages');
      extras = {
        ...extras,
        ...getRequiredMenuConfigParamsForCoordinates({
          menuConfig,
          fnIMenuPackages,
        }),
        numberOfPacks: _size(_filter(fnIMenuPackages, packageItem => _get(packageItem, 'selected'))),
      };
    }

    // here
    handlePrintFNISheet({ dealNumber, extras });
  };

  handlePrintDeclinationSheet = () => {
    if (!this.canOpenShareMenuItem()) {
      return;
    }
    const {
      deal: { dealNumber = EMPTY_STRING },
      handlePrintFNISheet,
    } = this.props;

    // here;
    handlePrintFNISheet({ dealNumber, isDeclinedPDF: true });
  };

  handleSaveCustomer = async (payload, prevSSN = EMPTY_OBJECT) => {
    const { customers, formCustomer, formTitle, bankingDetails = {}, customerDocuments } = payload;
    const customerWithNonEmptyFields = removeEmptyCustomerFormFields(customers);
    await this.onCustomersUpdated(customerWithNonEmptyFields, prevSSN, customerDocuments);
    await this.saveSelectedFormCustomer(formCustomer, formTitle, bankingDetails);
    if (GlobalWarning && isInchcape()) {
      await GlobalWarning.showWarnings();
    }
  };

  updateLeadID = async (leadId, detachLead = false) => {
    const {
      DeskingActions,
      deal: { dealNumber = EMPTY_STRING },
      motabilityLead,
    } = this.props;
    const response = await DeskingAPI.patchDeal(dealNumber, {
      leadId,
      detachLead,
      ...(detachLead ? {} : { motabilityLead }),
    });
    const status = _get(response, 'rawResponse.data.status');
    if (status === API_STATUS.SUCCESS) {
      DeskingActions.setNewLeadId({ leadId });
      if (!isInchcapeOrRRG()) {
        if (!detachLead) toaster('success', __('Lead has been successfully attached to the deal.'));
        else toaster('info', __('Lead is removed from the deal'));
      }
      return true;
    }
    if (!isInchcapeOrRRG()) {
      if (!detachLead) toaster('error', __('Error attaching lead to the deal.'));
      else toaster('error', __('Error detaching lead from the deal'));
    }
    return false;
  };

  handleDealUpdate = async ({ vehicles, customers, assignee, additionalPayload }) => {
    // TODO: Assignees save has to be handled seperately
    const { onVehiclesAndCustomersUpdated, addDefaultCostAdjustments } = this.props;
    this.hideModal();
    await onVehiclesAndCustomersUpdated({ vehicles, customers, additionalPayload });
    await addDefaultCostAdjustments();
  };

  saveSelectedFormCustomer = async (formCustomer, formTitle, bankingDetails) => {
    const { deal, DeskingActions } = this.props;
    const existingFormCustomer = getFormCustomer(deal);
    const existingFormTitle = getFormTitle(deal);

    if (existingFormCustomer !== formCustomer || existingFormTitle !== formTitle) {
      await DeskingActions.patchDeal({
        formCustomerSelection: formCustomer,
        formCustomerTitleSelection: formTitle,
      });
    }
    if (!_isEmpty(bankingDetails)) {
      await this.saveBankingInfo(bankingDetails);
    }
  };

  updateVehicleDelivery = async data => {
    const { deal } = this.props;
    const dealNumber = getDealNumber(deal);
    const response = await ContractDateService.getVehicleDeliveryDetails(dealNumber);
    if (response) {
      const deliveryFieldValues = {
        code10: response?.code10,
        deliveryAddress: response?.deliveryAddress,
        deliveryPreference: response?.deliveryPreference,
        deliveryFee: response?.deliveryFee,
        isAllDayDelivery: response?.isAllDayDelivery,
        isPrimaryAddressSame: response?.isPrimaryAddressSame,
        scheduledStartDate: data?.scheduledStartDate,
        scheduledStartTime: data?.scheduledStartTime,
        customerName: response?.customerName,
        dealNumber,
      };
      await ContractDateService.updateVehicleDeliveryDetails(deliveryFieldValues);
    }
  };

  saveBankingInfo = async data => {
    const { deal } = this.props;
    const dealNumber = getDealNumber(deal);
    const customerId = getBuyer(deal)?.customerId;
    const payload = {
      dealNumber,
      customerId,
      ...data,
    };
    const { response } = await CustomerAPI.saveBankingData(payload);
    if (response) {
      return response || [];
    }
    return [];
  };

  renderBackButton = () => <Button icon="icon-left-arrow-thin" view="icon" onClick={this.clickBack} />;

  renderSalesPersonDealsMenu = () => {
    const { rolesForAssignee, deal } = this.props;
    return <RecentDeals rolesForAssignee={rolesForAssignee} dealNumber={getDealNumber(deal)} />;
  };

  skipDealJacketVisiblityChange = show => {
    this.setState({ skipDealJacketVisiblityChange: !!show });
  };

  setIsDealJacketSigning = (value, submitCb = _noop) => {
    this.setState({ isDealJacketSigning: value, dealJacketSubmitCb: submitCb });
  };

  toggleDealJacketTabVisible = async () => {
    const { isDealJacketSigning, dealJacketSubmitCb, dealJacketTabVisible, initialPackageDocs } = this.state;
    const { packageDocuments, isDealAcquired } = this.props;
    if (!hasDealJacketViewOrEdit() || !isDealAcquired) return;
    if (isDealJacketSigning) {
      const submitSignature = await ConfirmationModal.getConfirmation({
        message: __(
          `There are unsubmitted signatures in your document(s). Your changes will be lost if you exit the deal jacket  without submitting these documents.\nDo you wish to proceed?`
        ),
        title: __('Unsubmitted Signatures'),
        submitBtnText: __('Submit Signatures'),
      });

      if (submitSignature) {
        dealJacketSubmitCb();
      }

      return;
    }

    if (dealJacketTabVisible) {
      const signedPackagesInCurrentSession = getSignedPackagesInDealJacketSession({
        initialPackages: initialPackageDocs,
        finalPackages: packageDocuments,
      });
      const signedSessionIds = _map(signedPackagesInCurrentSession, ({ sessionId }) => sessionId);
      this.setState(() => ({
        initialPackageDocs: packageDocuments,
      }));

      if (!_isEmpty(signedSessionIds)) {
        await DeskingAPI.completeReportsBySession(signedSessionIds);
      }
    }

    let sessionId = '';
    if (!dealJacketTabVisible) {
      sessionId = `DJ_${uuid()}`;
    }
    this.setState({ dealJacketTabVisible: !dealJacketTabVisible, dealJacketSessionId: sessionId });
  };

  setInitialPackageDocs = (packageDocs = EMPTY_ARRAY) => {
    this.setState({ initialPackageDocs: packageDocs });
  };

  renderFirstHeaderItem = () => {
    const { skipDealJacketVisiblityChange, dealJacketTabVisible, dealJacketSessionId } = this.state;
    const {
      navigate,
      location,
      deal,
      isDealAcquired,
      scanDocuments,
      dealJacket,
      packageDocuments,
      sppPackageDocuments,
      documentsViewPreferences,
      routeOneErrors,
      downloadDSPDocuments,
    } = this.props;
    const { dealNumber = EMPTY_STRING } = deal || EMPTY_OBJECT;
    const updatedPackageDocs = getModifiedPackageDocs(packageDocuments, DEAL_JACKET.PACKAGE);
    const updatedSppPackageDocs = getModifiedPackageDocs(sppPackageDocuments, DEAL_JACKET.SPP_PACKAGE);
    const documents = this.concatDocs(scanDocuments, dealJacket, updatedPackageDocs, updatedSppPackageDocs);

    return (
      <ModalDropDown
        visible={dealJacketTabVisible}
        onClick={this.toggleDealJacketTabVisible}
        overlayContainerClass={styles.overlayContainerClass}
        overlay={
          <DealJacket
            {...this.props}
            navigate={navigate}
            location={location}
            dealNumber={dealNumber}
            documents={documents}
            scanDocuments={scanDocuments}
            skipDealJacketVisiblityChange={this.skipDealJacketVisiblityChange}
            documentsViewPreferences={documentsViewPreferences}
            setIsDealJacketSigning={this.setIsDealJacketSigning}
            setInitialPackageDocs={this.setInitialPackageDocs}
            dealJacketSessionId={dealJacketSessionId}
            routeOneErrors={routeOneErrors}
            downloadDSPDocuments={downloadDSPDocuments}
          />
        }
        skipVisiblityChange={skipDealJacketVisiblityChange}
        Trigger={this.renderDealJacketTrigger}
        placement="bottomRight"
        borderWidth={1}
        borderColor="grey"
        onModalDropDownCloseCb={this.clearLocationHash}
        dropdownIconClassName={(!hasDealJacketViewOrEdit() || !isDealAcquired) && 'disableScreen'}
      />
    );
  };

  onFlagChange = (dealNumber, oldSubStatusSelected) => newSubStatusSelected => {
    const { DeskingActions, dealSubStatusList } = this.props;
    const { status: oldSubStatus, subStatusId: oldSubStatusId } = getSubStatusAndIdFromValue(
      dealSubStatusList,
      oldSubStatusSelected
    );
    const { status: newSubStatus, subStatusId: newSubStatusId } = getSubStatusAndIdFromValue(
      dealSubStatusList,
      newSubStatusSelected
    );
    DeskingActions.updateCustomStatus(dealNumber, oldSubStatusId, oldSubStatus, newSubStatusId, newSubStatus);
  };

  renderDealJacketTrigger = () => {
    const {
      deal,
      scanDocuments,
      dealJacket,
      packageDocuments,
      sppPackageDocuments,
      dealSubStatusList,
      isDealAcquired,
    } = this.props;
    const { customOrderDetails, reservationOrderDetails } = this.state;
    const subStatus = _get(deal, 'subStatus') || EMPTY_STRING;
    const subStatusId = _get(deal, 'subStatusId') || EMPTY_STRING;
    const packageDocListing = getModifiedPackageDocs(packageDocuments, DEAL_JACKET.PACKAGE);
    const sppPackageDocListing = getModifiedPackageDocs(sppPackageDocuments, DEAL_JACKET.SPP_PACKAGE);

    const dealNumber = _get(deal, 'dealNumber') || EMPTY_STRING;
    const oldSubStatus = _get(deal, 'subStatus') || EMPTY_STRING;
    const status = _get(deal, 'status') || EMPTY_STRING;
    const dealSource = _get(deal, 'source');
    const program = _get(deal, 'program');
    const externalSource = getDealExternalSource(deal);

    return (
      <DealNumberHeaderItem
        dealNo={dealNumber}
        dealSource={dealSource}
        documentsCount={_size(
          getFiltererdDocuments(this.concatDocs(scanDocuments, dealJacket, packageDocListing, sppPackageDocListing))
        )}
        onFlagChange={this.onFlagChange(dealNumber, oldSubStatus)}
        subStatus={subStatus}
        subStatusId={subStatusId}
        dealSubStatusList={dealSubStatusList}
        status={status}
        program={program}
        externalSource={externalSource}
        customOrderDetails={customOrderDetails}
        reservationOrderDetails={reservationOrderDetails}
        disabled={!hasDealJacketViewOrEdit() || !isDealAcquired}
        tooltipMessage={!hasDealJacketViewOrEdit() ? <DealJacketViewPermissionWarning /> : ''}
      />
    );
  };

  renderSecondHeaderItem = () => {
    const {
      deal: { vehicles = EMPTY_ARRAY },
      vehiclesInfo,
      isDealViewOnly,
      deal,
      trimInfos,
      DealsActions,
      onVehiclesUpdated,
      isClosedOrSoldDeal,
      isDealAcquired,
      vehicleSubTypesForNewVehicleProgram,
      CommonActions,
      DeskingActions,
      disablePricingEditSettings,
      agingField,
      salesSetupInfo,
      isFuseDeal,
      crmHeartbeatStatus,
    } = this.props;
    const isVehicleChangeDisabled = deal.locked || isClosedOrSoldDeal || !isDealAcquired;
    const isVehicleEnabled = hasVehicleEdit() && !isDealViewOnly;
    const dealType = getDealType(deal);
    const onSectionClick =
      hasVehicleEdit() &&
      !isVehicleChangeDisabled &&
      dealType !== DEAL_TYPES.ONLY_TRADES &&
      !(dealType === DEAL_TYPES.FNI && isInchcape())
        ? this.updateDeal
        : _noop;
    if (_isEmpty(vehicles) || isTempVehicle(vehicles)) {
      return (
        <div
          className={`cursor-pointer ${ymmHeaderStyles.parentContainer}`}
          onClick={_partial(onSectionClick, TAB_KEYS.VEHICLE)}
          role="button"
          tabIndex="-1">
          <Button
            disabled={crmHeartbeatStatus}
            view="icon"
            className="marginR8"
            onClick={_partial(onSectionClick, TAB_KEYS.VEHICLE)}>
            <div className="d-flex align-items-center">
              <FontIcon size="XL">icon-steering</FontIcon>
              <Heading size={4} className="marginL8">
                {__('Select Vehicle')}
              </Heading>
            </div>
          </Button>
        </div>
      );
    }
    const vehiclesList = vehicles.map(vehicle => {
      const { id = '', primaryVehicle = false } = vehicle;
      if (id && _has(vehiclesInfo, id)) return { ...vehiclesInfo[id], primaryVehicle, ...vehicle };
      return vehicle;
    });
    const isRVDealer = CalcEngineProperties.isRVVehicle();
    const vehicleDealHeaderIdentifier = getVehicleDealHeaderIdentifier(salesSetupInfo);
    const item = getPrimaryVehicleData(
      vehiclesList,
      agingField,
      isRVDealer,
      vehicleDealHeaderIdentifier,
      getDealType(deal)
    );
    const vehicleId = getPrimaryVehicleId(deal);
    const dealVehicleId = getPrimaryVehicleDealVehicleId(deal);
    const vin = getPrimaryVehicleVIN(deal);
    const primaryVehicleStatus = getPrimaryVehicleStatus(deal);
    const { alerts, hasOptionAlerts } = this.state;
    const activeTab = hasOptionAlerts ? EDIT_VEHICLE_MODAL_TAB_KEYS.OPTIONS : EMPTY_STRING;

    return (
      <YMMHeaderItem
        {...item}
        roDetails={vehiclesInfo?.[vehicleId]?.roDetails}
        isRVDealer={isRVDealer}
        getRecallData={this.getRecallData}
        trimInfos={trimInfos}
        disabled={!isVehicleEnabled}
        editable={isVehicleEnabled}
        onClick={_partial(onSectionClick, TAB_KEYS.VEHICLE)}
        vehiclesInfo={vehiclesInfo}
        vehicleId={vehicleId}
        dealVehicleId={dealVehicleId}
        vin={vin}
        deal={deal}
        DealsActions={DealsActions}
        CommonActions={CommonActions}
        onVehiclesUpdated={onVehiclesUpdated}
        vehicleSubTypesForNewVehicleProgram={vehicleSubTypesForNewVehicleProgram}
        setTradeInVehicles={DeskingActions.setTradeInVehicles}
        isDealViewOnly={isDealViewOnly || crmHeartbeatStatus}
        hasVehicleEdit={hasVehicleEdit()}
        disablePricingEditSettings={disablePricingEditSettings}
        primaryVehicleStatus={primaryVehicleStatus}
        showRecallWarning={DeskingActions.showRecallWarning}
        salesSetupInfo={salesSetupInfo}
        isFuseDeal={isFuseDeal}
        crmHeartbeatStatus={crmHeartbeatStatus}
        alerts={alerts}
        hasOptionAlerts={hasOptionAlerts}
        removeOptionAlert={this.removeOptionAlert}
        activeTab={activeTab}
      />
    );
  };

  renderCustomerEditTrigger = () => {
    const {
      deal: { customers = EMPTY_ARRAY },
      isDealViewOnly,
      buyerCreditReport,
      DeskingActions,
      crmHeartbeatStatus,
      deal,
      chargeCustomer,
    } = this.props;
    const onSectionClick = _noop;
    if (_isEmpty(customers) || isTempCustomer(customers)) {
      return (
        <div className={`cursor-pointer m-l-12 ${customerHeaderStyles.parentContainer}`} role="button" tabIndex="-1">
          <Button view="icon" className="marginR8" onClick={_partial(onSectionClick, TAB_KEYS.CUSTOMER)}>
            <div className="d-flex align-items-center">
              <Heading size={4}>{__('Select Customer')}</Heading>
            </div>
          </Button>
        </div>
      );
    }

    const navigateToCreditTab = () => DeskingActions.setSelectedtab(LEFT_PANEL_ITEMS.CREDIT);

    const item = {
      ...getCustomersData(customers),
      ...getBuyerCobuyerDetails(customers),
    };
    return (
      <CustomersHeaderItem
        {...item}
        buyerCreditReport={buyerCreditReport}
        disabled={!hasCustomerEdit() || isDealViewOnly || crmHeartbeatStatus}
        navigateToCreditTab={navigateToCreditTab}
        deal={deal}
        chargeCustomer={chargeCustomer}
      />
    );
  };

  toggleCustomerTabVisible = disabled => () => {
    const { isScreenBlocked, checkIfCustomerFormHasErrors } = this.props;
    const { customerTabVisible: customerTabOpened } = this.state;
    if (isScreenBlocked) return;
    CRMLeadHeartbeat.updateCRMLeadHeartbeatStatus(
      { customerTabOpened: !customerTabOpened },
      { isDelayedUpdate: !customerTabOpened === false }
    );

    if (getCustomerTabClosableFlag(disabled, checkIfCustomerFormHasErrors)) {
      toaster('error', __('Please fill out all mandatory fields'));
      return;
    }

    this.setState(({ customerTabVisible }) => ({
      customerTabVisible: !customerTabVisible,
    }));
  };

  buildCustomerForCrmNotEnabledAndDuplicationPreventionEnabled = buildBuyerType => {
    this.setState({
      isCrmNotEnabledAndDuplicationPreventionEnabled: true,
      buildBuyerType,
    });
  };

  handleCloseBuildCustomerDrawer = () => {
    this.setState({
      isCrmNotEnabledAndDuplicationPreventionEnabled: false,
    });
  };

  setCustomerFormValidationErrors = (errors, index, field) => {
    const { setCustomerFormErrors } = this.props;
    setCustomerFormErrors(errors, index, field);
  };

  resetCustomerFormErrors = index => {
    const { resetCustomerFormErrors } = this.props;
    resetCustomerFormErrors(index);
  };

  renderThirdHeaderItem = () => {
    const {
      deal,
      isDealViewOnly,
      contentHeight,
      salesSetupInfo,
      blockScreen,
      unBlockScreen,
      isFuseDeal,
      crmHeartbeatStatus,
      DeskingActions,
      isDealLocked,
      navigate,
      location,
    } = this.props;
    const { customerTabVisible, isCrmNotEnabledAndDuplicationPreventionEnabled, buildBuyerType } = this.state;
    const height = getOS() === OS_TYPES.WINDOWS ? contentHeight - 40 : contentHeight - 21;
    const consumerCreditApplicationFlag = isConsumerCreditApplicationEnabled(salesSetupInfo);
    const { fleetNumber } = deal;
    const disableCustomerForm = (isDealViewOnly && !hasViewSubscreensInBookedDeals()) || !hasCustomerEdit();
    return (
      <>
        <ModalDropDown
          visible={customerTabVisible}
          onClick={this.toggleCustomerTabVisible(isDealViewOnly || isFuseDeal || fleetNumber || crmHeartbeatStatus)}
          overlay={
            <div className="deal-jacket-dropdown" style={{ height }}>
              <CustomerForm
                deal={deal}
                DeskingActions={DeskingActions}
                blockScreen={blockScreen}
                unBlockScreen={unBlockScreen}
                height={height}
                dealType={getDealType(deal)}
                customers={getCustomers(deal)}
                saveForm={this.handleSaveCustomer}
                showAddCustomer
                enableAttachment
                formCustomer={getFormCustomer(deal)}
                formTitle={getFormTitle(deal)}
                isDealViewOnly={isDealViewOnly || isFuseDeal || fleetNumber || crmHeartbeatStatus}
                leadId={getLeadId(deal)}
                updateLeadID={this.updateLeadID}
                isConsumerCreditApplicationEnabled={consumerCreditApplicationFlag}
                soldAtSiteId={getDealCreatedAtSite(deal)}
                crmHeartbeatStatus={crmHeartbeatStatus}
                customerTabVisible={customerTabVisible}
                updateHeartbeatStatusForCustomer={this.updateHeartbeatStatusForCustomer}
                isDealLocked={isDealLocked}
                setCustomerFormValidationErrors={this.setCustomerFormValidationErrors}
                buildCustomerForCrmNotEnabledAndDuplicationPreventionEnabled={
                  this.buildCustomerForCrmNotEnabledAndDuplicationPreventionEnabled
                }
                resetCustomerFormErrors={this.resetCustomerFormErrors}
                salesSetupInfo={salesSetupInfo}
              />
            </div>
          }
          Trigger={this.renderCustomerEditTrigger}
          placement="bottomRight"
          borderWidth={1}
          borderColor="grey"
          onModalDropDownCloseCb={this.clearLocationHash}
          disabled={disableCustomerForm}
          triggerContainerClass="full-height"
          dropdownIconClassName={isInchcape() ? 'm-l-32' : EMPTY_STRING}
        />
        <PropertyControlledComponent controllerProperty={isCrmNotEnabledAndDuplicationPreventionEnabled}>
          <BuildCustomerForm
            navigate={navigate}
            location={location}
            deal={deal}
            dealType={getDealType(deal)}
            contentHeight={height}
            removeSelectedCustomer={_noop}
            isCrmNotEnabledAndDuplicationPreventionEnabled={isCrmNotEnabledAndDuplicationPreventionEnabled}
            handleCloseBuildCustomerDrawer={this.handleCloseBuildCustomerDrawer}
            saveCustomerData={this.handleSaveCustomer}
            buildBuyerType={buildBuyerType}
            setCustomerOnlyInStore={DeskingActions.setCustomerOnlyInStore}
          />
        </PropertyControlledComponent>
      </>
    );
  };

  renderFourthHeaderItem = getLabelFromPaymentOptionsFunc => {
    const {
      deskingpaymentDetails = EMPTY_ARRAY,
      vehiclesInfo,
      deal,
      programInfo,
      DeskingActions,
      toggleDealSummaryVisibility,
      salesSetupInfo,
    } = this.props;
    const isDeductCustomerCashfromAmountFinancedforCashDealEnabled =
      deductCustomerCashfromAmountFinancedforCashDealEnabled(salesSetupInfo);
    const item = getSelectedPaymentObject(deskingpaymentDetails);
    if (item)
      return (
        <PriceHeaderItem
          {...item}
          vehiclesInfo={vehiclesInfo}
          deal={deal}
          deskingpaymentDetails={deskingpaymentDetails}
          programInfo={programInfo}
          DeskingActions={DeskingActions}
          isDeductCustomerCashfromAmountFinancedforCashDealEnabled={
            isDeductCustomerCashfromAmountFinancedforCashDealEnabled
          }
          toggleDealSummaryVisibility={toggleDealSummaryVisibility}
          PriceHeaderItem
          getLabelFromPaymentOptions={getLabelFromPaymentOptionsFunc}
        />
      );
    return null;
  };

  renderFifthHeaderItem = () => {
    const { deal } = this.props;
    const buyer = getBuyer(deal);
    const customerDisplayNumber = getCustomerNumberForDisplay(buyer);
    const oldCustomerDisplayId = _get(buyer, 'oldDisplayId') || null;
    return (
      <PropertyControlledComponent controllerProperty={!!customerDisplayNumber}>
        <div className="d-flex flex-row">
          <div className={styles.divider} />
          <div className="d-flex flex-column justify-content-center m-l-12">
            <div className="d-flex align-items-center">
              <Ellipsis length={7} className={styles.customerID} tooltip>
                {customerDisplayNumber}
              </Ellipsis>
              <PropertyControlledComponent controllerProperty={!_isEmpty(oldCustomerDisplayId)}>
                <InfoIconWithPopOver
                  infoMessage={oldCustomerDisplayId}
                  trigger="hover"
                  placement={POPOVER_PLACEMENT.RIGHT_BOTTOM}
                />
              </PropertyControlledComponent>
            </div>
            <Label>{__('Customer ID')}</Label>
          </div>
        </div>
      </PropertyControlledComponent>
    );
  };

  renderShareButton = () => {
    const {
      pdfLoaders,
      isDealAcquired,
      conciergeDetails,
      deal,
      documentsGdprConfig,
      isDealViewOnly,
      isConciergeActivated,
      showCustomerLoginModal,
      getMarketScanPayloadObject,
      DeskingActions,
      FniMenuActions,
      isMultiVehicleDealSheet,
      blockScreen,
      unBlockScreen,
      showMandatoryForm,
      salesSetupInfo,
    } = this.props;
    const { isFNISheetPrinting, labelConfig, dealJacketDocuments } = this.state;
    const showBtn = isAnyPdfShared(conciergeDetails, isConciergeActivated);
    const isGeneratingPDF = getPdfLoader(
      pdfLoaders,
      _values(_omit(PRINT_ASSET_TYPES, ['CASHIERING_RECEIPT', 'CASHIERING_INVOICE_RECEIPT']))
    );

    const isDealTypeDealerTrade = [DEAL_TYPES.DEALER_TRADE].includes(deal?.type);
    const isDealTypeCashRealisation = [DEAL_TYPES.CASH_REALISATION].includes(deal?.type);
    const isDealTypeTrade = [DEAL_TYPES.TRADE].includes(deal?.type);
    return (
      <DropdownActions
        disabled={
          !isDealAcquired ||
          gdprConfigReader.disabled(documentsGdprConfig) ||
          (isDealViewOnly && isInchcape()) ||
          showCustomerLoginModal
        }
        actions={this.getShareButtonActions(
          conciergeDetails,
          isConciergeActivated,
          deal,
          labelConfig,
          dealJacketDocuments,
          isDealTypeDealerTrade,
          isDealTypeCashRealisation,
          isDealTypeTrade,
          getMarketScanPayloadObject,
          DeskingActions,
          FniMenuActions,
          isMultiVehicleDealSheet,
          blockScreen,
          unBlockScreen,
          showMandatoryForm,
          salesSetupInfo,
          this.handlePrintCoverSheet,
          this.handleDealSheetPreview,
          this.handleCanadaDealSheetPreview,
          this.handlePrintMultiVehicleDealSheet,
          this.handlePrintFourSquareSheetPreview,
          this.shouldHideFourSquareBoxSheet,
          this.handleVehicleSalesOrderSheet,
          this.handlePrintBuyBackSheet,
          this.handlePrintMarginSheet,
          this.handleInvoiceSheet,
          this.getInvoicesFromDealJacket,
          this.handlePrintRecap,
          this.handlePrintFNISheet,
          this.toggleFNIPrint,
          this.handlePrintDeclinationSheet,
          this.onClickOfPrintLabel
        )}
        isGeneratingPDF={isGeneratingPDF || isFNISheetPrinting}
        prefixIcon="icon-share"
        defaultAction={this.getStopAllSharingBtn(showBtn)}
        triggerClassName={classNames({ [styles.isShared]: showBtn })}
        onDropDownOpenCallBack={this.getDealJacketDocuments}
        disableActionButtons={DealReader.manualPaused(deal)}
      />
    );
  };

  getDealSheetPreviewProps = () => {
    const {
      deal,
      defaultsheetConfigs,
      DeskingActions,
      pdfLoaders,
      conciergeDetails,
      DigitalRetailActions,
      deskingpaymentDetails,
      conciergeSetupConfig,
      startVirtualMeeting,
      isConciergeActivated,
    } = this.props;

    return {
      deal,
      deskingpaymentDetails,
      defaultsheetConfigs,
      setDealSheetPrintPayload: DeskingActions.setDealSheetPrintPayload,
      pdfLoaders,
      conciergeDetails,
      DigitalRetailActions,
      conciergeSetupConfig,
      startVirtualMeeting,
      isConciergeActivated,
    };
  };

  getDeliveryOptionForDropdown = () => (
    <div className={classNames('d-flex align-items-center', styles.dropdownLabel)}>
      <FontIcon size={SIZES.MD} className="pointer">
        icon-delivery
      </FontIcon>
      <Content colorVariant={Content.COLOR_VARIANTS.GREY}>{__('Delivery')}</Content>
    </div>
  );

  onAppointmentSuccess = response => {
    this.updateLeadID(response?.entityId);
    if (isInchcapeOrRRG()) {
      this.updateVehicleDelivery(response);
    }
  };

  openAppointmentModal = async () => {
    const { deal } = this.props;
    if (getLeadId(deal)) {
      const response = await getLeadDetails(getLeadId(deal));
      const leadInfo = _get(response, 'data.data');

      showGlobalModal({
        modalType: MODAL_TYPE.APPOINTMENT_MODAL,
        payload: {
          onSuccessCb: this.onAppointmentSuccess,
          disableLeadSelection: true,
          ...(isInchcapeOrRRG()
            ? {
                appointment: {
                  deliveryLocation,
                },
              }
            : {}),
          initialValues: {
            entityType: CRM_ENTITIES.LEAD,
            createdBySource: CLIENT.SALES_UI,
            ...(isInchcapeOrRRG() ? { [APPOINTMENT_MODAL_FIELDS.APPOINTMENT_TYPE]: 'DELIVERY' } : {}),
            [APPOINTMENT_MODAL_FIELDS.ASSIGNED_TO]: getLeadAssignee(leadInfo),
            [APPOINTMENT_MODAL_FIELDS.LEAD]: getLeadName(leadInfo) || null,
            [APPOINTMENT_MODAL_FIELDS.LEAD_INFO]: leadInfo,
          },
        },
      });
    } else {
      const buyer = getBuyer(deal);
      showGlobalModal({
        modalType: MODAL_TYPE.APPOINTMENT_MODAL,
        payload: {
          onSuccessCb: this.onAppointmentSuccess,
          disableLeadSelection: true,
          ...(isInchcapeOrRRG()
            ? {
                appointment: {
                  deliveryLocation,
                },
              }
            : {}),
          initialValues: {
            entityType: CRM_ENTITIES.CUSTOMER,
            createdBySource: CLIENT.SALES_UI,
            ...(isInchcapeOrRRG() ? { [APPOINTMENT_MODAL_FIELDS.APPOINTMENT_TYPE]: 'DELIVERY' } : {}),

            [APPOINTMENT_MODAL_FIELDS.LEAD_INFO]: buyer,
            [APPOINTMENT_MODAL_FIELDS.LEAD]: getLeadName(buyer) || null,
            [APPOINTMENT_MODAL_FIELDS.ASSIGNED_TO]: getLeadAssignee(buyer),
            customerId: buyer?.customerId,
            [CUSTOMER_IDS.MOBILE_PHONE]: buyer?.mobileNo,
            ..._pick(buyer, [
              CUSTOMER_IDS.FIRST_NAME,
              CUSTOMER_IDS.LAST_NAME,
              CUSTOMER_IDS.MIDDLE_NAME,
              CUSTOMER_IDS.EMAIL,
            ]),
          },
        },
      });
    }
  };

  toggleModalVisiblity = () => EditVehicleModal.toggleVisibility();

  onEditVehicleClick = () => this.toggleModalVisiblity();

  toggleDealCompareModal = (deal, deskingpaymentDetails) => {
    const { dealerAddress = [] } = DealEnv.dealerConfig || {};

    const dealerMarketId = _get(_get(dealerAddress, 0) || {}, 'state');
    const selectedColumns = getSelectedColumn(deskingpaymentDetails);
    const dealType = getPaymentType(selectedColumns);
    const { state } = getCustomerAddressDetails(getBuyer(deal));
    showDrawer({
      renderer: DealComparison,
      renderOptions: {
        dealType,
        dealNumber: getDealNumber(deal),
        customerState: state,
        dealerState: dealerMarketId,
      },
    });
  };

  getDealerComparisonIcon = () => {
    const { deal, deskingpaymentDetails } = this.props;
    return (
      <>
        <FontIcon
          size={SIZES.MD_S}
          className={styles.dealComparisonIcon}
          onClick={() => this.toggleDealCompareModal(deal, deskingpaymentDetails)}>
          icon-two-way-arrows
        </FontIcon>
        <FullPageModalDrawer closable={false} />
      </>
    );
  };

  getDealerComparisonV2Icon = props => (
    <FontIcon size={SIZES.MD_S} className={styles.dealComparisonIcon} {...props}>
      icon-two-way-arrows
    </FontIcon>
  );

  onCustomersUpdated = async (customers, prevSSN, customerDocuments) => {
    const {
      blockScreen,
      unBlockScreen,
      deal: oldDeal,
      updateDefaultFNIs,
      DeskingActions,
      saveDealDataFromStoreAndCallMS,
    } = this.props;
    if (_isFunction(blockScreen)) blockScreen(); // Blocking entire UI, so that user wont be able to navigate to other screens
    const currentCustomers = [...customers];
    const { buyer } = this.getCustomers(currentCustomers);
    const isMscanCallNeeded = await this.setUpdatedCustomersInStore(customers, prevSSN, customerDocuments);

    const { deal: newDeal, deskingpaymentDetails } = this.props;
    if (isAnyApprovedColumnAdded(deskingpaymentDetails)) {
      showCustomerSwitchWarningsForApprovedColumn(buyer, oldDeal);
    } else if (shouldShowCustomerLoginModalOrWarning(newDeal)) showCustomerSwitchWarnings(buyer, oldDeal);

    if (!CalcEngineProperties.updateCalculationByBackend() && isMscanCallNeeded) {
      await updateDefaultFNIs();
      await saveDealDataFromStoreAndCallMS(oldDeal);
    }
    if (_isFunction(unBlockScreen)) unBlockScreen();
    await DeskingActions.getDealPreferences();
  };

  updateCustomersForCMS = async dealCustomers => {
    const { deal, saveCustomer } = this.props;
    const dealNumber = getDealNumber(deal);
    const newDealInfo = await saveCustomer(dealNumber, {
      customers: dealCustomers,
    });
    return newDealInfo;
  };

  getCustomers = customers => {
    let buyer;
    let cobuyer;
    // eslint-disable-next-line no-restricted-syntax
    for (const customer of customers) {
      if (customer.type === 'BUYER') {
        buyer = customer;
      } else {
        cobuyer = customer;
      }
    }
    return { buyer, cobuyer };
  };

  handleCustomerUpdateInDeal = async customers => {
    const { deal: previousDeal, DeskingActions } = this.props;
    const updatedDeal = await this.updateCustomersForCMS(customers);

    if (!updatedDeal) {
      toaster('error', __('Failed to update the Customer info'));
      await DeskingActions.setDeal(previousDeal);
      await DeskingActions.updateDeskingDataByDeal();
      return false;
    }

    // Reverting the customer data to form payload if updatedCustomers failed
    await DeskingActions.setDeal(updatedDeal);
    await DeskingActions.updateDeskingDataByDeal();

    const customerIds = _map(customers, customer => _get(customer, 'customerId'));
    const ssnValues = _map(customers, customer => {
      const ssn = _get(customer, 'ssn');
      const ssnLast4 = getLastFourDigitsForSSN(ssn);
      return { ssnLast4 };
    });

    const customerPIIDataByCustomerId = _reduce(
      customerIds,
      (accum, customerId, index) => {
        accum[customerId] = {
          fetched: !_isEmpty(_get(ssnValues[index], 'ssnLast4')),
          pIIData: ssnValues[index],
        };

        return accum;
      },
      {}
    );
    DeskingActions.updateCustomerMaskedData(customerPIIDataByCustomerId);

    return updatedDeal;
  };

  saveDocumentsInSequence = async payloads => {
    if (_size(payloads) === 0) {
      return;
    }

    const payload = _head(payloads);
    const { error } = await CustomerAPI.saveCustomerDocuments(payload);
    if (!_isEmpty(error)) {
      toaster('error', getAPIError(error));
    }
    await this.saveDocumentsInSequence(_drop(payloads));
  };

  saveCustomerDocuments = async (updatedDeal, customerDocuments) => {
    const customerUploadPayloads = _reduce(
      customerDocuments,
      (prevList, customerDocument) => {
        const type = customerDocument?.type;
        const documents = getPayloadForUploadDocuments(updatedDeal, customerDocument, type);
        return [...prevList, ...documents];
      },
      []
    );

    if (!_isEmpty(customerUploadPayloads)) {
      await this.saveDocumentsInSequence(customerUploadPayloads);
    }
  };

  // Returns if mscan call needed or not
  setUpdatedCustomersInStore = async (customers, prevSSN, customerDocuments) => {
    const { deal: previousDeal, setDealZipCodeIfBuyerZipCodeChanged, syncPaymentDetails } = this.props;

    let currentCustomers = [...customers];
    let updatedDeal;
    let v2Happened = false;

    let { buyer, cobuyer } = this.getCustomers(currentCustomers);
    const buyerSsnSaved = await saveSSN(buyer, prevSSN);

    if ((buyer && !buyer.customerId) || (cobuyer && !cobuyer.customerId)) {
      updatedDeal = await this.handleCustomerUpdateInDeal(currentCustomers);

      const { deal } = this.props;
      const { customers: dealCustomers } = deal;

      const validCustomers = _map(dealCustomers, (customer, index) => ({
        ...currentCustomers[index],
        ...customer,
      }));
      ({ buyer, cobuyer } = this.getCustomers(validCustomers));
      currentCustomers = dealCustomers;
      v2Happened = true;
    }
    if (!buyerSsnSaved) {
      await saveSSN(buyer, prevSSN);
    }
    await saveSSN(cobuyer, prevSSN);

    if (!v2Happened) {
      updatedDeal = await this.handleCustomerUpdateInDeal(currentCustomers);
    }

    if (shouldUpdateGlobalWarnings(previousDeal, updatedDeal)) {
      GlobalWarning.showWarnings();
    }

    if (DealerPropertyHelper.isUSUploadDocSyncCrm()) {
      await this.saveCustomerDocuments(updatedDeal, customerDocuments);
    }

    if (isInchcape()) {
      const isValueChanged = isBusinessOrCustomerTaxClassificationChanged(customers, previousDeal);
      if (isValueChanged) await syncPaymentDetails();
    }

    if (CalcEngineProperties.updateCalculationByBackend()) {
      return false;
    }

    const isMscanCallNeeded = await setDealZipCodeIfBuyerZipCodeChanged(previousDeal, updatedDeal);
    return isMscanCallNeeded;
  };

  render() {
    const { showDealSheetPreview, showFourSquareSheetPreview, showCanadaDealSheetPreview } = this.state;
    const {
      deal,
      notes,
      salesSetupInfo,
      getFormattedDateAndTime,
      primaryDMS,
      navigate,
      location,
      handleActionsForDealSheet,
      isEnterpriseV2Enabled,
      isDealLocked,
      getFeatureValue,
    } = this.props;
    const buyer = getBuyer(deal) || EMPTY_OBJECT;
    const { customerId, mobileNo } = buyer;
    const dealNumber = getDealNumber(deal);
    const dealId = getDealId(deal);
    const createAppointmentsFromDeals = getCreateAppointmentsFromDeals(salesSetupInfo);
    const paymentOptions = formatPaymentOptionsFromSalesSetup(getPaymentOptionConfigs(salesSetupInfo));
    const getLabelFromPaymentOptionsFunc = getLabelFromPaymentOptions(paymentOptions);
    const { hasOptionAlerts, showOptionsMismatchBanner, alerts } = this.state;
    const vehicleId = getPrimaryVehicleId(deal);
    const optionMismatchAlertMessage = getSpecificAlertMessageForVehicle(vehicleId, alerts, ALERTS.OPTION_MISMATCH);
    const dealStatus = getDealStatus(deal);
    const disableDeliveryAppointmentStatus = getDisableDeliveryAppointmentStatus(salesSetupInfo);
    const isDealsVirtualMeetingEnabled = DealerPropertyHelper.isDealsVirtualMeetingEnabled();

    return (
      <>
        <PropertyControlledComponent controllerProperty={hasOptionAlerts && showOptionsMismatchBanner}>
          <VehicleMismatchStickyBanner
            alertMessage={optionMismatchAlertMessage}
            onClose={this.hideOptionsMismatchBanner}
            viewDetails={this.onEditVehicleClick}
            onRemove={this.removeOptionsMismatchFlag}
          />
        </PropertyControlledComponent>
        <div className={styles.firstLevelHeader}>
          <div className="d-flex flex-row">
            <div className={styles.backSection}>{this.renderBackButton()}</div>
            {this.renderSalesPersonDealsMenu()}
            {this.renderFirstHeaderItem()}
            <div className={classNames(styles.divider, styles.menuMarginRight)} />
            {this.renderSecondHeaderItem()}
            <div className={classNames(styles.divider, styles.menuMarginRight)} />
            {this.renderThirdHeaderItem()}
            <div className={classNames(styles.divider, styles.menuMarginRight)} />
            {this.renderFourthHeaderItem(getLabelFromPaymentOptionsFunc)}
            {this.renderFifthHeaderItem()}
          </div>

          <div className="d-flex align-items-center">
            {showDealCompareForDMS(dealStatus) &&
              DealerPropertyHelper.isExternalDmsEnabled() &&
              !getFeatureValue(FEATURE_NAME.AEC_PLATFROM_DEAL_PUSH_ENABLED) &&
              DMS_WITH_DEAL_COMPARE_SUPPORT.has(primaryDMS) &&
              this.getDealerComparisonIcon()}

            {getFeatureValue(FEATURE_NAME.AEC_PLATFROM_DEAL_PUSH_ENABLED) && (
              <DealComparisonButtonV2 triggerComponent={this.getDealerComparisonV2Icon} />
            )}

            <PropertyControlledComponent controllerProperty={isInchcapeOrRRG()}>
              <AppointmentDropdown
                deal={deal}
                openAppointmentModal={this.openAppointmentModal}
                navigate={navigate}
                location={location}
                disableDeliveryAppointmentStatus={disableDeliveryAppointmentStatus}
              />
            </PropertyControlledComponent>

            <PropertyControlledComponent controllerProperty={createAppointmentsFromDeals && !isInchcape()}>
              <Tooltip
                className="d-flex align-items-center m-l-8 m-r-12"
                onClick={this.openAppointmentModal}
                title={__('Create New Appointment')}
                placement={TOOLTIP_PLACEMENT.BOTTOM}>
                <FontIcon size={SIZES.L} className="cursor-pointer">
                  icon-appointment
                </FontIcon>
              </Tooltip>
            </PropertyControlledComponent>

            {/* <AppointmentInfoModal /> */}
            {customerId && dealNumber && (
              <CommunicationDefaultWidget
                className="m-r-12"
                assetId={dealId}
                customerId={customerId}
                phoneNumber={mobileNo}
                iconSize={SIZES.L}
                assetType={ASSET_TYPES.DEAL}
              />
            )}

            <NotesIconAndModal
              notes={notes}
              assetType={NOTES_ASSET_TYPE.CONTRACTS}
              assetId={dealId}
              dealNumber={dealNumber}
              title={__('Deal #{{dealNumber}}', { dealNumber })}
              className="m-r-12 m-t-4"
              placement="left"
              size={SIZES.L}
              personas={SEARCH_USER_BY_ALL_PERSONAS}
              fetchUsersToTag={handleFetchUsersToTagInNotes(isEnterpriseV2Enabled)}
              isMultiPinningAllowed
            />

            <PropertyControlledComponent controllerProperty={isDealsVirtualMeetingEnabled}>
              <VmStartButton isDealLocked={isDealLocked} />
            </PropertyControlledComponent>

            {this.renderShareButton()}
          </div>

          <UpdateModal ref={this.getUpdateModalRef} onClose={this.hideModal} />

          <PropertyControlledComponent controllerProperty={showDealSheetPreview}>
            <DealSheetPreview
              closeModal={this.handleDealSheetPreview}
              handlePrintDealSheet={this.handlePrintDealSheet}
              getLabelFromPaymentOptions={getLabelFromPaymentOptionsFunc}
              handleActionsForDealSheet={handleActionsForDealSheet}
              {...this.getDealSheetPreviewProps()}
            />
          </PropertyControlledComponent>

          <PropertyControlledComponent controllerProperty={showFourSquareSheetPreview}>
            <DealSheetPreview
              closeModal={this.handlePrintFourSquareSheetPreview}
              handlePrintDealSheet={this.handlePrintFourSquareSheet}
              getLabelFromPaymentOptions={getLabelFromPaymentOptionsFunc}
              isFourSquareSheet
              assetType={PRINT_ASSET_TYPES.FOUR_SQUARE_BOX_SHEET}
              handleActionsForDealSheet={handleActionsForDealSheet}
              {...this.getDealSheetPreviewProps()}
            />
          </PropertyControlledComponent>

          <PropertyControlledComponent controllerProperty={showCanadaDealSheetPreview}>
            <DealSheetPreview
              closeModal={this.handleCanadaDealSheetPreview}
              handlePrintDealSheet={this.handlePrintDealSheet}
              isCanadaSheet
              getFormattedDateAndTime={getFormattedDateAndTime}
              handleActionsForDealSheet={handleActionsForDealSheet}
              {...this.getDealSheetPreviewProps()}
            />
          </PropertyControlledComponent>

          <DealInfoPreviewModal ref={this.labelPreviewRef} />
        </div>
      </>
    );
  }
}

function mapStateToProps(globalState) {
  const state = _get(globalState, BASE_REDUCER_KEY);
  return {
    agingField: _get(state, 'common.viSettings.otherSettings.agingField'),
    buyerCreditReport: _get(state, 'desking.dealPreferences.buyerCreditReport', EMPTY_OBJECT),
    documentsGdprConfig: selectGDPRConfig(state),
    dealJacketGdprConfig: getDealJacketGdprConfig(state),
    motabilityLead: _get(state, 'desking.motabilityLead', false),
  };
}

export default compose(
  connect(mapStateToProps),
  withTekionConversion,
  withDeskingContext,
  withPrintConsumer,
  WithMandatoryReviewHOC,
  withCRMLeadHeartbeatStatus,
  withCustomerFormErrorsState,
  withExperienceEngineConsumer
)(DeskingFirstLevelHeader);
