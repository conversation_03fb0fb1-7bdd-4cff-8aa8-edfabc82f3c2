import React, { PureComponent, Fragment } from 'react';
import PropTypes from 'prop-types';
import produce from 'immer';

import _map from 'lodash/map';
import _isEmpty from 'lodash/isEmpty';
import _reduce from 'lodash/reduce';
import _get from 'lodash/get';
import _filter from 'lodash/filter';
import _set from 'lodash/set';
import _first from 'lodash/first';
import _noop from 'lodash/noop';
import _size from 'lodash/size';
import _findIndex from 'lodash/findIndex';
import _find from 'lodash/find';
import _isArray from 'lodash/isArray';
import _castArray from 'lodash/castArray';

import { EMPTY_STRING, EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { DEAL_TYPES } from '@tekion/tekion-base/constants/deal/type';
import * as SalesSetupReader from '@tekion/tekion-base/marketScan/readers/salesSetup.reader';
import * as DealReader from '@tekion/tekion-base/marketScan/readers/deal.reader';
import { formatSocialSecurityNumber } from '@tekion/tekion-base/formatters/number';
import { CUSTOMER_TYPE, BUYER_TYPE } from 'pages/deallist/deal.constants';
import { RECORD_TYPES } from 'constants/constants';
import CustomerForm from 'organisms/customerForm';
import SelectCustomer from 'organisms/customerForm/components/selectCustomer';
import { withTekionConversion } from '@tekion/tekion-conversion-web';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';

import {
  getInitialForm,
  getInitialValue,
  getInitialErrorState,
  getFormValueForExistingCustomer,
  getLeadVaultInfoFromLeadForm,
  intialErrorState,
  getFormDataFromLead,
  isCRMFlow,
} from 'organisms/customerForm/CustomerForm.utils';
import { MOTABILITY_MESSAGES } from 'utils/motabilityValidation.utils';
import CustomerAPI from 'commonActions/apis/customer.api';
import LeadsAPI from 'commonActions/apis/leads.api';
import { isInchcape } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
import CRMLeadHeartbeat from 'utils/CRMLeadHeartbeat';

import { toaster } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import {
  getCopiedCustomer,
  copyInsuranceDetails,
  isBuyerAddressPresent,
  validateSingleField,
  checkIFProperValuesExists,
} from './CustomerForm.utils';
import { getLastFourDigitsForSSN } from '../../components/subHeader/subHeaderTabs/tradeInValuation/tradeInValuation.utils';

class CustomerFormItem extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      formValues: _size(props.customers) ? getInitialForm(props.customers, props.dealType) : [],
      errors: _size(props.customers) ? getInitialErrorState(props.customers) : [],
      formAttr: {
        formCustomer: props.formCustomer,
        formTitle: props.formTitle,
      },
      prevSSNValues: {},
      showModal: false,
      isCustomerFormDirty: false,
      selectedCustomerBuyerType: null,
      selectedLeadId: null,
    };
    this.rules = {};
  }

  async componentDidMount() {
    const { formValues } = this.state;
    const { deal, DeskingActions, customers } = this.props;
    const dealNumber = DealReader.getDealNumber(deal);
    const customerId = DealReader.getBuyer(deal)?.customerId;

    let newFormValues = await this.getCustomersWithVaultInfo(formValues);

    const ssnValues = _reduce(
      newFormValues,
      (accum, formValue, index) => {
        const ssn = _get(formValue, 'customer.ssn');
        const ssnLast4 = getLastFourDigitsForSSN(ssn);

        accum[customers[index]?.customerId] = {
          pIIData: { ssnLast4 },
          fetched: !_isEmpty(_get(customers[index], 'customer.ssn')),
        };
        return accum;
      },
      {}
    );
    DeskingActions.updateCustomerMaskedData(ssnValues);

    if (isInchcape()) {
      newFormValues = await this.getBankingInfo(dealNumber, customerId, newFormValues);
    }
    this.setState({ formValues: _isArray(newFormValues) ? newFormValues : [newFormValues] });
  }

  componentDidUpdate(prevProps) {
    const { isCrmNotEnabledAndDuplicationPreventionEnabled } = this.props;
    if (DealerPropertyHelper.isDealLeadSyncV2Enabled()) {
      const { customerTabVisible, crmHeartbeatStatus } = this.props;
      const isCRMHeartbeatStatusChanged = prevProps.crmHeartbeatStatus === true && crmHeartbeatStatus === false;
      const isCRMChangesReceived = CRMLeadHeartbeat.getCustomerTradeinFormInitializationStatus();
      if (isCRMHeartbeatStatusChanged || isCRMChangesReceived) {
        CRMLeadHeartbeat.updateCRMLeadHeartbeatStatus({ customerTabOpened: customerTabVisible }, { forceUpdate: true });
        CRMLeadHeartbeat.setCustomerTradeinFormInitializationStatus(false);
        if (isCRMChangesReceived) this.initializeCustomerData();
      }
    }
    if (!DealerPropertyHelper.isCRMEnabledV2() && DealerPropertyHelper.isCustomerDuplicatePreventionEnabled()) {
      const { customers } = this.props;
      const { customers: prevCustomers } = prevProps;

      if (customers !== prevCustomers) {
        this.initializeCustomerData();
        this.setFormDirty();
      }
    }
  }

  initializeCustomerData = async () => {
    const { customers, dealType } = this.props;
    const formValues = _size(customers) ? getInitialForm(customers, dealType) : [];
    const formValuesWithVInfo = await this.getCustomersWithVaultInfo(formValues);
    this.setState({
      formValues,
    });
    this.setState({ formValues: _isArray(formValuesWithVInfo) ? formValuesWithVInfo : [formValuesWithVInfo] });
  };

  setRulesList = (key, rules) => {
    _set(this.rules, key, rules);
  };

  setFormDirty = () => {
    const { isCustomerFormDirty } = this.state;
    if (isCustomerFormDirty) return;
    this.setState({
      isCustomerFormDirty: true,
    });
  };

  setSelectedLeadId = leadId => {
    this.setState({ selectedLeadId: leadId });
  };

  setParentState = (stateToSet, shouldSetFormDirty = true) => {
    this.setState(stateToSet);

    if (shouldSetFormDirty) {
      this.setFormDirty();
    }
  };

  getParentState = () => this.state;

  getCustomersWithVaultInfo = async customers => {
    const validCustomers = _filter(customers, ({ customerId, tempId }) => customerId || tempId);
    const customerSSN = await Promise.all(
      _map(validCustomers, async ({ customerId, tempId }) => {
        const id = !_isEmpty(customerId) ? customerId : tempId;
        const { response } = await CustomerAPI.getCustomerVaultInfo(id);
        return { [id]: _get(response, 'socialSecurityNumber') || EMPTY_STRING };
      })
    );
    const ssn = _reduce(customerSSN, (ssnMap, item) => ({ ...ssnMap, ...item }), {});
    this.setState({ prevSSNValues: ssn });
    return _map(customers, item => {
      if (!_get(item, 'customer.ssn')) {
        return {
          ...item,
          customer: {
            ...item.customer,
            ssn: formatSocialSecurityNumber(ssn[item.customerId || item.tempId]),
          },
        };
      }
      return item;
    });
  };

  getBankingInfo = async (dealNumber, customerId, formValues) => {
    const payload = {
      dealNumber,
      customerId,
    };
    const { response } = await CustomerAPI.getBankingData(payload);
    if (response) {
      return Object.assign(formValues[0], { bankingDetails: response });
    }
    return formValues;
  };

  getLeadsWithVaultInfo = async leadId => {
    let response = EMPTY_OBJECT;
    if (leadId) {
      const payload = {
        entityType: RECORD_TYPES.LEAD,
        entityId: leadId,
        includeFields: ['buyer', 'coBuyer'],
      };
      response = (await LeadsAPI.getVaultInformation(payload)) || EMPTY_OBJECT;
    }
    return response;
  };

  onSaveForm = async payload => {
    const hasWrongValues = checkIFProperValuesExists(payload);
    if (hasWrongValues) {
      toaster('warn', __('Please Select proper values for Employment status and Other Income source'));
      // return;
    }
    const { selectedLeadId, prevSSNValues } = this.state;
    const { saveForm } = this.props;
    const { isCustomerFormDirty } = this.state;
    if (isCustomerFormDirty) {
      if (selectedLeadId) await this.updateLeadIdOfDeal(selectedLeadId);
      saveForm(payload, prevSSNValues);
    }
  };

  handleBuildCustomer = buyerType => {
    const { dealType, leadId: presentLeadID, salesSetupInfo } = this.props;
    const isCRMEnabled = isCRMFlow(dealType);
    const creditScore = SalesSetupReader.selectDealerCreditScore(salesSetupInfo);

    if (isCRMEnabled) {
      this.setState(
        produce(draft => {
          const formItem = _set(
            getInitialValue(dealType, buyerType === BUYER_TYPE.CO_BUYER, EMPTY_OBJECT, creditScore),
            'customer.leadId',
            presentLeadID
          );
          draft.formValues.push(formItem);
          draft.errors.push(intialErrorState);
        })
      );
    } else {
      this.setState(
        produce(draft => {
          draft.formValues.push(
            getInitialValue(dealType, buyerType === BUYER_TYPE.CO_BUYER, EMPTY_OBJECT, creditScore)
          );
          draft.errors.push(intialErrorState);
        })
      );
    }
    this.setState({
      isCustomerFormDirty: false,
    });
  };

  onBuyerChange = newBuyerIndex => {
    this.setState(
      produce(draft => {
        const currentBuyerIndex = _findIndex(draft.formValues, {
          type: BUYER_TYPE.BUYER,
        });
        if (draft.formValues[currentBuyerIndex]) {
          draft.formValues[currentBuyerIndex].type = BUYER_TYPE.CO_BUYER;
        }
        if (draft.formValues[newBuyerIndex]) {
          draft.formValues[newBuyerIndex].type = BUYER_TYPE.BUYER;
        }
      })
    );
  };

  validateSingleFieldErrors = async (sectionKey, { index, field, pathObj, id }) => {
    const { formValues: currentFormValue, errors } = this.state;
    const fieldErrors = await validateSingleField(this.rules, currentFormValue, sectionKey);
    const updatedErrors = produce(errors, draft => {
      draft[index][field][id] = fieldErrors[sectionKey];
    });

    this.setState({ errors: updatedErrors });
  };

  handleFormAttrChange = (key, value) => {
    this.setState(
      produce(draft => {
        _set(draft, ['formAttr', key], value);
      })
    );
    this.setFormDirty();
  };

  handlePrimaryCustomerChange = () => {
    const { formValues } = this.state;
    const buyerIndex = _findIndex(formValues, customer => _get(customer, 'type') === BUYER_TYPE.BUYER);
    const coBuyerIndex = _findIndex(formValues, customer => _get(customer, 'type') === BUYER_TYPE.CO_BUYER);
    if (buyerIndex !== -1 && coBuyerIndex !== -1) {
      const leadId = _get(formValues[buyerIndex], 'leadId');
      this.setState(
        produce(draft => {
          if (draft.formValues[buyerIndex]) {
            _set(draft.formValues[buyerIndex], 'type', BUYER_TYPE.CO_BUYER);
          }
          if (draft.formValues[coBuyerIndex]) {
            _set(draft.formValues[coBuyerIndex], 'type', BUYER_TYPE.BUYER);
            _set(draft.formValues[coBuyerIndex], 'leadId', leadId);
          }
        })
      );
    }
    this.setFormDirty();
  };

  toggleModal = selectedCustomerBuyerType => {
    if (!DealerPropertyHelper.isCRMEnabledV2() && DealerPropertyHelper.isCustomerDuplicatePreventionEnabled()) {
      const { buildCustomerForCrmNotEnabledAndDuplicationPreventionEnabled } = this.props;
      buildCustomerForCrmNotEnabledAndDuplicationPreventionEnabled(selectedCustomerBuyerType);
    } else {
      this.setState(prevState => ({
        showModal: !prevState.showModal,
        selectedCustomerBuyerType,
      }));
    }
  };

  addCustomer = async customers => {
    const { salesSetupInfo } = this.props;
    const { selectedCustomerBuyerType, formValues } = this.state;

    const creditScore = SalesSetupReader.selectDealerCreditScore(salesSetupInfo);

    if (selectedCustomerBuyerType === BUYER_TYPE.CO_BUYER) {
      const buyer = _find(formValues, { type: BUYER_TYPE.BUYER });
      if (customers?.[0]?.displayId === buyer?.displayId) {
        toaster('warn', __('Buyer and Co-buyer cannot be same'));
        return;
      }
    } else {
      const cobuyer = _find(formValues, { type: BUYER_TYPE.CO_BUYER });
      if (customers?.[0]?.displayId === cobuyer?.displayId) {
        toaster('warn', __('Buyer and Co-buyer cannot be same'));
        return;
      }
    }
    const parsedCustomer = getFormValueForExistingCustomer(customers, true, creditScore);
    const customersWithVaultInfo = await this.getCustomersWithVaultInfo(parsedCustomer);
    this.setState(
      produce(draft => {
        draft.formValues.push({
          ..._first(customersWithVaultInfo),
          type: selectedCustomerBuyerType,
        });
        draft.errors.push(intialErrorState);
      })
    );
    this.toggleModal();
    this.setFormDirty();
  };

  updateLeadIdOfDeal = async (leadId, detachLead = false) => {
    const { dealType } = this.props;
    const isCRMEnabled = isCRMFlow(dealType);
    let status = false;
    const { updateLeadID } = this.props;
    if (isCRMEnabled) {
      status = await updateLeadID(leadId, detachLead);
    }
    return status;
  };

  // Setting Lead
  setLead = formData => {
    this.setState(
      produce(draft => {
        draft.formValues = [];
        draft.errors = [];
        draft.formValues = formData;
        formData.forEach(() => {
          draft.errors.push(intialErrorState);
        });
      })
    );
    this.setFormDirty();
  };

  onRemoveLead = async () => {
    const status = await this.updateLeadIdOfDeal(EMPTY_STRING, true);
    if (status) this.setSelectedLeadId(EMPTY_STRING);
    return status;
  };

  onRemoveCustomer = isPrimaryCustomer => {
    if (isPrimaryCustomer) this.setSelectedLeadId(EMPTY_STRING);
  };

  checkMotabilityDealAndLead = motabilityLead => {
    const { dealType } = this.props;
    const isNonMotabilityDeal = dealType !== DEAL_TYPES.MOTABILITY;
    if (isNonMotabilityDeal && motabilityLead) {
      toaster('error', MOTABILITY_MESSAGES.NON_MOTABILITY_LEAD_REQUIRED);
    }
  };

  // Lead Model
  onAddLead = async (lead, formData) => {
    const { formValues } = this.state;
    const { dealType } = this.props;
    const { leadId } = lead;
    const presentCoBuyer = _find(formValues, { type: BUYER_TYPE.CO_BUYER });
    this.setSelectedLeadId(leadId);
    if (leadId) {
      if (!_isEmpty(formData)) {
        const { buyer: buyerVaultInfo, coBuyer: coBuyerVaultInfo } = getLeadVaultInfoFromLeadForm(formData);
        this.setLead(getFormDataFromLead(dealType, lead, presentCoBuyer, buyerVaultInfo, coBuyerVaultInfo));
      } else {
        const { buyer: buyerVaultInfo, coBuyer: coBuyerVaultInfo } = await this.getLeadsWithVaultInfo(leadId);
        this.setLead(getFormDataFromLead(dealType, lead, presentCoBuyer, buyerVaultInfo, coBuyerVaultInfo));
      }
    }
  };

  onAddContactWithLead = async (lead, buyerData, coBuyerData) => {
    const { salesSetupInfo, DeskingActions } = this.props;
    const creditScore = SalesSetupReader.selectDealerCreditScore(salesSetupInfo);
    const motabilityLead = _get(lead, 'buyer.motability', false);
    await DeskingActions.setMotabilityLead(motabilityLead);
    this.checkMotabilityDealAndLead(motabilityLead);
    if (!buyerData?.id) {
      await this.onAddLead(lead);
      return;
    }
    if (lead?.leadId) {
      this.setSelectedLeadId(lead?.leadId);
    }
    const formValues = [];
    if (buyerData?.id) {
      const parsedCustomer = getFormValueForExistingCustomer(
        _castArray({ ...buyerData, customerId: buyerData.id, type: CUSTOMER_TYPE.BUYER }),
        undefined,
        creditScore
      );
      const customersWithVaultInfo = await this.getCustomersWithVaultInfo(parsedCustomer);
      formValues.push({
        ..._first(customersWithVaultInfo),
        type: CUSTOMER_TYPE.BUYER,
        leadId: lead?.leadId,
        entityType: 'LEAD',
      });

      if (coBuyerData?.id) {
        const parsedCustomer = getFormValueForExistingCustomer(
          _castArray({ ...coBuyerData, customerId: coBuyerData.id, type: CUSTOMER_TYPE.CO_BUYER }),
          undefined,
          creditScore
        );
        const customersWithVaultInfo = await this.getCustomersWithVaultInfo(parsedCustomer);
        formValues.push({
          ..._first(customersWithVaultInfo),
          type: CUSTOMER_TYPE.CO_BUYER,
          leadId: lead?.leadId,
          entityType: 'LEAD',
        });
      }

      this.setState(
        produce(draft => {
          draft.formValues = formValues;
        })
      );

      this.setFormDirty();
    }
  };

  onContactCustomerSelection = async (customer = EMPTY_OBJECT, buyerType = CUSTOMER_TYPE.BUYER) => {
    const { salesSetupInfo } = this.props;
    const creditScore = SalesSetupReader.selectDealerCreditScore(salesSetupInfo);
    const parsedCustomer = getFormValueForExistingCustomer(
      _castArray({ ...customer, type: buyerType }),
      undefined,
      creditScore
    );
    const customersWithVaultInfo = await this.getCustomersWithVaultInfo(parsedCustomer);
    this.setState(
      produce(draft => {
        draft.formValues.push({
          ..._first(customersWithVaultInfo),
          type: buyerType,
        });
      })
    );
    this.setFormDirty();
  };

  // Duplicate Lead Model: Lead Selection
  handleDuplicateLeadSelection = lead => this.onAddLead(lead);

  // Duplicate Lead Model: Customer Selection
  handleDuplicateCustomerSelection = async (customer, type) => {
    const { leadId: presentLeadID, salesSetupInfo } = this.props;
    const creditScore = SalesSetupReader.selectDealerCreditScore(salesSetupInfo);
    const customers = !_isEmpty(customer) ? [customer] : [];
    const parsedCustomer = getFormValueForExistingCustomer(customers, false, creditScore);
    const customersWithVaultInfo = await this.getCustomersWithVaultInfo(parsedCustomer);
    this.setState(
      produce(draft => {
        const index = _findIndex(draft.formValues, { type });
        if (!_isEmpty(draft.formValues[index])) {
          draft.formValues[index] = { ..._first(customersWithVaultInfo), type };
          _set(draft.formValues[index], 'customer.leadId', presentLeadID);
          draft.errors[index] = intialErrorState;
        }
      })
    );
    this.setFormDirty();
  };

  onCopyAddress = () => {
    const { formValues } = this.state;
    const buyerIndex = _findIndex(formValues, { type: BUYER_TYPE.BUYER });
    const buyerAddressPresent = buyerIndex !== -1 ? isBuyerAddressPresent(formValues[buyerIndex]) : false;
    if (buyerAddressPresent) {
      this.setState(
        produce(draft => {
          const coBuyerIndex = _findIndex(draft.formValues, {
            type: BUYER_TYPE.CO_BUYER,
          });
          if (!_isEmpty(draft.formValues[coBuyerIndex])) {
            const copiedCustomer = getCopiedCustomer(draft.formValues[buyerIndex], draft.formValues[coBuyerIndex]);
            draft.formValues[coBuyerIndex] = copiedCustomer;
            draft.errors[coBuyerIndex] = intialErrorState;
          }
        })
      );
      this.setFormDirty();
    }
  };

  onCopyOfInsurance = () => {
    this.setState(
      produce(draft => {
        const buyerIndex = _findIndex(draft.formValues, {
          type: BUYER_TYPE.BUYER,
        });
        const coBuyerIndex = _findIndex(draft.formValues, {
          type: BUYER_TYPE.CO_BUYER,
        });
        if (!_isEmpty(draft.formValues[coBuyerIndex])) {
          const copiedInsuranceDetails = copyInsuranceDetails(
            draft.formValues[buyerIndex],
            draft.formValues[coBuyerIndex]
          );
          draft.formValues[coBuyerIndex] = copiedInsuranceDetails;
          draft.errors[coBuyerIndex] = intialErrorState;
        }
      })
    );
    this.setFormDirty();
  };

  render() {
    const { formValues = [], errors, formAttr, showModal, bankingDetails = [] } = this.state;
    const { showAddCustomer, isDealViewOnly, getCountOfPhoneNumberDigits, setCustomerFormValidationErrors } =
      this.props;
    return (
      <Fragment>
        <CustomerForm
          {...this.props}
          formValues={formValues}
          errors={errors}
          formAttr={formAttr}
          setParentState={this.setParentState}
          getParentState={this.getParentState}
          buildNew={this.handleBuildCustomer}
          saveForm={this.onSaveForm}
          onTouch={this.setFormDirty}
          onBuyerChange={this.onBuyerChange}
          handleFormAttrChange={this.handleFormAttrChange}
          handlePrimaryCustomerChange={this.handlePrimaryCustomerChange}
          isDealViewOnly={isDealViewOnly}
          showAddCustomer={showAddCustomer}
          toggleModal={this.toggleModal}
          onAddLead={this.onAddLead}
          onAddContactWithLead={this.onAddContactWithLead}
          onContactCustomerSelection={this.onContactCustomerSelection}
          onRemoveLead={this.onRemoveLead}
          onRemoveCustomer={this.onRemoveCustomer}
          onCopyAddress={this.onCopyAddress}
          onCopyOfInsurance={this.onCopyOfInsurance}
          handleDuplicateLeadSelection={this.handleDuplicateLeadSelection}
          handleDuplicateCustomerSelection={this.handleDuplicateCustomerSelection}
          bankingDetails={bankingDetails}
          getCountOfPhoneNumberDigits={getCountOfPhoneNumberDigits}
          setRulesList={this.setRulesList}
          validateSingleFieldErrors={this.validateSingleFieldErrors}
          makeCoBuyerAsBuyer
          setCustomerFormValidationErrors={setCustomerFormValidationErrors}
        />
        {showAddCustomer && (
          <SelectCustomer
            maximumCustomerLength={1}
            showModal={showModal}
            toggleModal={this.toggleModal}
            addCustomer={this.addCustomer}
          />
        )}
      </Fragment>
    );
  }
}

CustomerFormItem.propTypes = {
  deal: PropTypes.array,
  customers: PropTypes.array,
  showAddCustomer: PropTypes.bool,
  setSelectedCustomersFromDeal: PropTypes.func.isRequired,
  dealType: PropTypes.string.isRequired,
  formCustomer: PropTypes.string,
  formTitle: PropTypes.string,
  isDealViewOnly: PropTypes.bool,
  saveForm: PropTypes.func.isRequired,
  updateLeadID: PropTypes.func,
  getDefaultCountryCode: PropTypes.func,
  getCountOfPhoneNumberDigits: PropTypes.func,
  DeskingActions: PropTypes.object.isRequired,
  buildCustomerForCrmNotEnabledAndDuplicationPreventionEnabled: PropTypes.func,
};

CustomerFormItem.defaultProps = {
  deal: EMPTY_ARRAY,
  customers: EMPTY_ARRAY,
  showAddCustomer: false,
  isDealViewOnly: false,
  updateLeadID: _noop,
  getCountOfPhoneNumberDigits: _noop,
  formCustomer: CUSTOMER_TYPE.BUYER,
  formTitle: CUSTOMER_TYPE.BOTH,
  getDefaultCountryCode: _noop,
  buildCustomerForCrmNotEnabledAndDuplicationPreventionEnabled: _noop,
};

export default withTekionConversion(CustomerFormItem);
