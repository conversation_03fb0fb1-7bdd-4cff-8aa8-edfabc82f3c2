/* eslint-disable import/order */
import React from 'react';

import _map from 'lodash/map';
import _omit from 'lodash/omit';
import _find from 'lodash/find';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _toString from 'lodash/toString';

import OrderFormGeneration from 'molecules/orderFormGeneration';
import PdfActionItem from './pdfActionItem';
import globalWarnings from '../components/globalWarnings';
import PrintFNISheet from './PrintFNISheet';

import { EMPTY_STRING, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { DEAL_TYPES } from '@tekion/tekion-base/constants/deal/type';
import { DEAL_JACKET_LABEL_TYPES } from 'twidgets/appServices/sales/organisms/labelpreview/labelPreview.constants';
import { FORM_FIELD_TO_DOCUMENT_TYPES } from '../../../organisms/customerForm/CustomerForm.constants';
import {
  ORDER_FORM_KEYS,
  ORDER_FORM_REGENERATION,
} from '../../../molecules/orderFormGeneration/orderFormGeneration.constants';
import { LEFT_PANEL_ITEMS } from '../desking.constants';
import {
  CUSTOMER_SWITCH_ERROR,
  VEHICLE_OR_CUSTOMER_SWITCH_ERROR,
} from '../components/globalWarnings/globalWarnings.constants';
import { PRINT_ASSET_TYPES } from '../../../constants/printAssetTypes';
import { DECLINATION_SHEET_NAME } from 'constants/fni';

import {
  getCustomers,
  getBuyer,
  getPrimaryVehicleId,
  getDealType,
  isMultiVehicleDeskingEnabledV2,
  isDealDigitalFlow,
  getTradeIns,
} from '@tekion/tekion-base/marketScan/readers/deal.reader';
import { isPdfSharing } from '@tekion/tekion-base/marketScan/readers/desking.reader';
import dealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';
import { isRRG, isInchcape, isInchcapeOrRRG } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
import { hasCostAndGrossView, hasLabelConfigLabelPrintViewPermission } from 'permissions/desking.permissions';
import { isCanadaDealer } from '../../../utils/dealerUtils';
import { getPayloadForSavingDocuments } from '../../../organisms/customerForm/customerForm.helpers';
import {
  getCustomerId,
  getCustomerNumberForDisplay,
  getCurrentZIPCodeOfBuyerInDeal,
} from '@tekion/tekion-base/marketScan/readers/customer.reader';
import CustomerAPI from '../../../commonActions/apis/customer.api';

const FnILabel = __('F & I');

export const getPayloadForUploadDocuments = (deal, customerDocument, type) => {
  const customers = getCustomers(deal);

  return _map(_omit(customerDocument, 'type'), (document, id) => {
    const customer = _find(customers, { type });
    const docType = FORM_FIELD_TO_DOCUMENT_TYPES[id];
    const uploadMediaPayload = getPayloadForSavingDocuments(deal, customer, docType, document);
    return uploadMediaPayload;
  });
};

export const checkForCustomerUpdate = (buyer, previousDeal) => {
  const prevBuyer = getBuyer(previousDeal);
  return buyer && prevBuyer && buyer?.customerId !== prevBuyer.customerId && dealerPropertyHelper.isAECProgram();
};

export const showCustomerSwitchWarnings = (buyer, previousDeal) => {
  if (checkForCustomerUpdate(buyer, previousDeal))
    globalWarnings.showCustomWarnings(LEFT_PANEL_ITEMS.DESKING, CUSTOMER_SWITCH_ERROR);
};

export const showCustomerSwitchWarningsForApprovedColumn = (buyer, previousDeal) => {
  if (checkForCustomerUpdate(buyer, previousDeal)) {
    globalWarnings.showCustomWarnings(LEFT_PANEL_ITEMS.DESKING, VEHICLE_OR_CUSTOMER_SWITCH_ERROR);
  }
};

export const getShareButtonActions = (
  conciergeDetails,
  isConciergeVisible,
  deal,
  labelConfig,
  dealJacketDocuments,
  isDealTypeDealerTrade,
  isDealTypeCashRealisation,
  isDealTypeTrade,
  getMarketScanPayloadObject,
  DeskingActions,
  FniMenuActions,
  isMultiVehicleDealSheet,
  blockScreen,
  unBlockScreen,
  showMandatoryForm,
  salesSetupInfo,
  handlePrintCoverSheet,
  handleDealSheetPreview,
  handleCanadaDealSheetPreview,
  handlePrintMultiVehicleDealSheet,
  handlePrintFourSquareSheetPreview,
  shouldHideFourSquareBoxSheet,
  handleVehicleSalesOrderSheet,
  handlePrintBuyBackSheet,
  handlePrintMarginSheet,
  handleInvoiceSheet,
  getInvoicesFromDealJacket,
  handlePrintRecap,
  handlePrintFNISheet,
  toggleFNIPrint,
  handlePrintDeclinationSheet,
  onClickOfPrintLabel
) => {
  const isFnIEnabled = dealerPropertyHelper.isFnIEnabled();
  const { printLabelsFromDeskingScreen } = labelConfig;
  const vehicleId = getPrimaryVehicleId(deal);
  const regenerateKey = _get(deal, `messageMap.${ORDER_FORM_REGENERATION.KEY}`, false);
  const dealType = getDealType(deal);

  return {
    [__('Desking')]: [
      {
        label: (
          <PdfActionItem
            text={__('Cover Sheet')}
            isSharing={isPdfSharing(conciergeDetails, PRINT_ASSET_TYPES.COVERSHEET, isConciergeVisible)}
          />
        ),
        onClick: handlePrintCoverSheet,
        hide: isInchcapeOrRRG(),
      },
      {
        label: (
          <PdfActionItem
            text={__('Deal Sheet')}
            isSharing={
              isPdfSharing(conciergeDetails, PRINT_ASSET_TYPES.DEALSHEET, isConciergeVisible) &&
              !isMultiVehicleDealSheet
            }
          />
        ),
        hide: isInchcape(),
        onClick: handleDealSheetPreview,
      },
      {
        label: (
          <PdfActionItem
            text={__('Canada Deal Sheet')}
            isSharing={
              isPdfSharing(conciergeDetails, PRINT_ASSET_TYPES.CANADA_SHEET, isConciergeVisible) &&
              !isMultiVehicleDealSheet
            }
          />
        ),
        onClick: handleCanadaDealSheetPreview,
        hide: !isCanadaDealer(),
      },
      {
        label: (
          <PdfActionItem
            text={__('Multi Vehicle Deal Sheet')}
            isSharing={
              isPdfSharing(conciergeDetails, PRINT_ASSET_TYPES.DEALSHEET, isConciergeVisible) && isMultiVehicleDealSheet
            }
          />
        ),
        onClick: handlePrintMultiVehicleDealSheet,
        hide: !isMultiVehicleDeskingEnabledV2(deal) || isInchcapeOrRRG(),
      },
      {
        label: (
          <PdfActionItem
            text={__('Four Square Box Sheet')}
            isSharing={
              isPdfSharing(conciergeDetails, PRINT_ASSET_TYPES.FOUR_SQUARE_BOX_SHEET, isConciergeVisible) &&
              !isMultiVehicleDealSheet
            }
          />
        ),
        onClick: handlePrintFourSquareSheetPreview,
        hide: isInchcapeOrRRG() || shouldHideFourSquareBoxSheet(),
      },
      // {
      //   label: (
      //     <PdfActionItem
      //       text={__('Vehicle Sales Order Sheet')}
      //       isSharing={isPdfSharing(
      //         conciergeDetails,
      //         PRINT_ASSET_TYPES.VEHICLE_SALES_ORDER_SHEET,
      //         isConciergeVisible
      //       )}
      //     />
      //   ),
      //   onClick: handleVehicleSalesOrderSheet,
      //   hide: !isRRG(),
      // },
      // {
      //   label: (
      //     <PdfActionItem
      //       text={__('BuyBack Valuation Sheet')}
      //       isSharing={isPdfSharing(conciergeDetails, PRINT_ASSET_TYPES.BUY_BACK_VALUE_SHEET, isConciergeVisible)}
      //     />
      //   ),
      //   onClick: handlePrintBuyBackSheet,
      //   hide: !isRRG() || !getBuyBackValuation(deal),
      // },
      {
        label: (
          <PdfActionItem
            text={__('Invoice Sheet')}
            isSharing={
              isRRG()
                ? isPdfSharing(conciergeDetails, PRINT_ASSET_TYPES.INVOICE_SHEET, isConciergeVisible)
                : isPdfSharing(conciergeDetails, PRINT_ASSET_TYPES.SALES_INVOICE_SHEET_V2, isConciergeVisible)
            }
          />
        ),
        onClick: handleInvoiceSheet,
        hide: !isRRG() || !getInvoicesFromDealJacket(dealJacketDocuments)?.length,
      },
      {
        label: (
          <OrderFormGeneration
            deal={deal}
            name={dealType === DEAL_TYPES.ONLY_TRADES ? __('Purchase Order Form') : __('Order Form')}
            isSharing={isPdfSharing(conciergeDetails, PRINT_ASSET_TYPES.ORDER_FORM_SHEET, isConciergeVisible)}
            id={regenerateKey ? ORDER_FORM_KEYS.REGENERATE_ORDER : ORDER_FORM_KEYS.CREATE_ORDER}
            isSharePrint
            DeskingActions={DeskingActions}
            blockScreen={blockScreen}
            unBlockScreen={unBlockScreen}
            showMandatoryForm={showMandatoryForm}
            salesSetupInfo={salesSetupInfo}
          />
        ),
        onClick: () => {},
        hide: !isInchcape() || isDealTypeDealerTrade || isDealTypeCashRealisation || isDealTypeTrade,
      },
    ],
    ...(!isInchcapeOrRRG() &&
      hasCostAndGrossView() && {
        [__('Recap')]: [
          {
            label: (
              <PdfActionItem
                text={__('Recap Sheet')}
                isSharing={isPdfSharing(conciergeDetails, PRINT_ASSET_TYPES.RECAPSHEET, isConciergeVisible)}
              />
            ),
            onClick: handlePrintRecap,
          },
        ],
      }),
    ...(isFnIEnabled &&
      !isInchcapeOrRRG() && {
        [FnILabel]: [
          {
            // label: <PdfActionItem
            //   text={__('F&I Menu Sheet')}
            //   isSharing={isPdfSharing(conciergeDetails, PRINT_ASSET_TYPES.FNI_MENU_SHEET, isConciergeVisible)}
            // />,
            label: (
              <PrintFNISheet
                isSharing={isPdfSharing(conciergeDetails, PRINT_ASSET_TYPES.FNI_MENU_SHEET, isConciergeVisible)}
                handlePrintFNISheet={handlePrintFNISheet}
                getMarketScanPayloadObject={getMarketScanPayloadObject}
                getFNIProducts={DeskingActions.getFNIProducts}
                toggleFNIPrint={toggleFNIPrint}
                FniMenuActions={FniMenuActions}
              />
            ),
            onClick: () => {},
          },
          {
            label: (
              <PdfActionItem
                text={DECLINATION_SHEET_NAME}
                isSharing={isPdfSharing(conciergeDetails, PRINT_ASSET_TYPES.DECLINATION_SHEET, isConciergeVisible)}
              />
            ),
            onClick: handlePrintDeclinationSheet,
          },
        ],
      }),
    ...(hasLabelConfigLabelPrintViewPermission() && {
      [__('LABEL')]: printLabelsFromDeskingScreen
        ? [
            {
              label: __('Deal Info'),
              onClick: onClickOfPrintLabel(DEAL_JACKET_LABEL_TYPES.DEAL_INFO, __('Deal Info Label Preview')),
            },
            {
              label: __('Vehicle Info'),
              popOverText: vehicleId ? EMPTY_STRING : __('Vehicle Info label is not available for Built Vehicles'),
              onClick: onClickOfPrintLabel(DEAL_JACKET_LABEL_TYPES.VEHICLE_INFO, __('Vehicle Info Label Preview')),
            },
            {
              label: __('Trade-in Info'),
              onClick: onClickOfPrintLabel(DEAL_JACKET_LABEL_TYPES.TRADE_IN_INFO, __('Trade-in Info Label Preview')),
              hide: _isEmpty(getTradeIns(deal)),
            },
          ]
        : [],
    }),
  };
};

export const saveCustomerVaultInfo = async customerWithSSN => {
  const { customerId, ssn: socialSecurityNumber } = customerWithSSN || EMPTY_OBJECT;
  return CustomerAPI.saveCustomerVaultInfo(customerId, { socialSecurityNumber });
};

export const deleteCustomerVaultInfo = async customerWithSSN => {
  const { customerId } = customerWithSSN || EMPTY_OBJECT;
  return CustomerAPI.deleteCustomerVaultInfo(customerId);
};

export const saveSSN = async (customer, previousSSN) => {
  if (!customer) {
    return false;
  }
  const { customerId, ssn } = customer || EMPTY_OBJECT;
  if (customerId && previousSSN[customerId] !== ssn) {
    // if current SSN is empty and previous SSN is non empty then delete SSN from Valut info DB
    if (previousSSN[customerId] && !ssn) {
      await deleteCustomerVaultInfo(customer);
    } else {
      await saveCustomerVaultInfo(customer);
    }
    return true;
  }
  return false;
};

export const getBuyerCobuyerDetails = customers => {
  if (_isEmpty(customers)) return {};
  const customerData = {};
  customers.forEach(customer => {
    if (customer.type === 'BUYER') {
      customerData.buyerId = getCustomerId(customer);
      customerData.buyerDisplayId = getCustomerNumberForDisplay(customer);
    }
    if (customer.type === 'CO_BUYER') {
      customerData.coBuyerId = getCustomerId(customer);
      customerData.coBuyerDisplayId = getCustomerNumberForDisplay(customer);
    }
  });
  return customerData;
};

export const shouldUpdateGlobalWarnings = (oldDeal, newDeal) => {
  const oldBuyer = getBuyer(oldDeal);
  const newBuyer = getBuyer(newDeal);
  const buyerPreviousZipCode = getCurrentZIPCodeOfBuyerInDeal(oldBuyer);
  const buyerCurrentZipCode = getCurrentZIPCodeOfBuyerInDeal(newBuyer);
  const isCustomerZipChanged = _toString(buyerPreviousZipCode) !== _toString(buyerCurrentZipCode);
  return isCustomerZipChanged;
};
