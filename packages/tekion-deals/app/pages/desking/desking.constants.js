import _map from 'lodash/map';
import _join from 'lodash/join';
import _isEmpty from 'lodash/isEmpty';
import { defaultMemoize } from 'reselect';
import colors from 'tstyles/exports.scss';

import { DEAL_STATUS } from '@tekion/tekion-base/constants/deal/status';
import { MARKUP_TYPES } from '@tekion/tekion-base/constants/retail/markupTypes';
import { SALES_PRINTING_MODULE_NAMES } from '@tekion/tekion-base/constants/pdf.constants';
import { INTERNAL_FEE_CODES } from '@tekion/tekion-base/constants/retail/fee.constants';
import {
  INFO_COLUMN_FIELDS,
  LENDER_PAYMENT_FIELDS,
  MORE_FIELDS,
} from '@tekion/tekion-base/marketScan/constants/deskingFields.constants';
import { DEAL_TYPES } from '@tekion/tekion-base/constants/deal/type';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';
import { isInchcape, isInchcapeOrRRG, isRRG } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
import {
  hasDeskingViewOrEdit,
  hasFniViewOrEdit,
  hasCreditViewOrEdit,
  hasDocumentsViewOrEdit,
  hasDeliveryViewOrEdit,
  hasContractsViewOrEdit,
  hasTransactionPostingViewOrEdit,
  hasCashieringViewOrEdit,
} from 'permissions/desking.permissions';
import { isArcLiteProgram } from 'utils/program.utils';
import { LEASE_WORKING_CASH_PRESET } from '@tekion/tekion-widgets/src/appServices/sales/constants/constants';
import { LEASE_WORKING_CASH } from '@tekion/tekion-base/marketScan/constants/constants';
import { DMS_PROVIDERS, DMS_PROVIDER_LABELS } from '@tekion/tekion-business/src/constants/dms.constants';
import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { DEAL_SOURCE } from '@tekion/tekion-base/marketScan/constants/deal.constants';
import { TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import { STATUS } from '@tekion/tekion-base/constants/status.constants';
import { KEYS as TRADE_IN_KEYS } from '@tekion/tekion-base/marketScan/constants/tradeInValuation.constants';
import { MODEL_TYPE } from 'constants/constants';
import { CUSTOMER_APPROVAL_ITEMS } from '@tekion/tekion-base/constants/desking/desking.constants';

const { darkCyan, violetBlue, salmon, bizzare, tranquil, selago } = colors;

export const NA = __('NA');

export const DOUBLE_DASH = __('--');

export const FIRST_MONTH_WAIVER = __('First Month Waiver');

export const FIRST_MONTH_WAIVER_ID = 1234; // has to be a number

export const FIRST_HEADER_HEIGHT = 60;

export const TAG = __('DealList');

export const DEALER_CASH_LABEL = __('Dealer Cash');

export const REBATE_ID = 'ID';

export const LONGEST_LEASE_TERM = 72;

export const LONGEST_LOAN_TERM = 1188;

export const DEFAULT_TERM = 36;

export const DEFAULT_INTEREST_RATE = __('0% Interest');

export const PLEASE_FINISH_DESKING = __('Please Finish Desking.');

export const PLEASE_FINISH_INVOICING = __('Please Finish Invoicing.');

export const PLEASE_GENERATE_ORDER_FORM = __('Please Generate Order Form. ');

export const LEFT_PANEL_ITEMS = {
  DESKING: 'DESKING',
  CREDIT: 'CREDIT',
  FINANCE_AND_INSURANCE: 'FINANCE_AND_INSURANCE',
  RECAP: 'RECAP',
  DOCUMENTS: 'DOCUMENTS',
  DELIVERY: 'DELIVERY',
  CASHIERING: 'CASHIERING',
  CONTRACTS: 'CONTRACTS',
  TRANSACTION_POSTING: 'TRANSACTION_POSTING',
  TASK_MANAGER: 'TASK_MANAGER',
  CONCEIRGE: 'CONCEIRGE',
  INVOICING: 'INVOICING',
  FINANCE: 'FINANCE',
};

export const LEFT_PANEL_HIDDEN_ITEMS_STANDALONE_CRM = [
  { id: LEFT_PANEL_ITEMS.FINANCE_AND_INSURANCE },
  { id: LEFT_PANEL_ITEMS.DELIVERY },
  { id: LEFT_PANEL_ITEMS.CASHIERING },
  { id: LEFT_PANEL_ITEMS.TRANSACTION_POSTING },
  { id: LEFT_PANEL_ITEMS.TASK_MANAGER },
  { id: LEFT_PANEL_ITEMS.CONCEIRGE },
  { id: LEFT_PANEL_ITEMS.CONTRACTS },
];

export const LEFT_PANEL_ITEMS_PERMISSIONS_BASED = {
  [LEFT_PANEL_ITEMS.DESKING]: hasDeskingViewOrEdit,
  [LEFT_PANEL_ITEMS.CREDIT]: hasCreditViewOrEdit,
  [LEFT_PANEL_ITEMS.FINANCE_AND_INSURANCE]: hasFniViewOrEdit,
  [LEFT_PANEL_ITEMS.RECAP]: hasFniViewOrEdit,
  [LEFT_PANEL_ITEMS.DOCUMENTS]: [hasDocumentsViewOrEdit, hasCreditViewOrEdit],
  [LEFT_PANEL_ITEMS.DELIVERY]: hasDeliveryViewOrEdit,
  [LEFT_PANEL_ITEMS.CASHIERING]: hasCashieringViewOrEdit,
  [LEFT_PANEL_ITEMS.TRANSACTION_POSTING]: hasTransactionPostingViewOrEdit,
  [LEFT_PANEL_ITEMS.CONTRACTS]: hasContractsViewOrEdit,
  // TODO: SHOULD CHECK PERMISSION?
};

export const LEFT_PANEL_DEAL_STATUS_MAP = () => {
  const dealStatuesRulesEnabled = isArcLiteProgram() ? DealerPropertyHelper.isDealStatusRulesConfigEnabled() : true;

  return {
    [DEAL_STATUS.QUOTE]: LEFT_PANEL_ITEMS.DESKING,
    [DEAL_STATUS.FINANCE_AND_INSURANCE]: LEFT_PANEL_ITEMS.FINANCE_AND_INSURANCE,
    // [DEAL_STATUS.CONFIRMED]: LEFT_PANEL_ITEMS.FINANCE_AND_INSURANCE,
    [DEAL_STATUS.CONFIRMED]: dealStatuesRulesEnabled
      ? LEFT_PANEL_ITEMS.DESKING
      : LEFT_PANEL_ITEMS.FINANCE_AND_INSURANCE,
    [DEAL_STATUS.PRE_QUALIFICATION]: LEFT_PANEL_ITEMS.FINANCE,
    [DEAL_STATUS.RECAP]: LEFT_PANEL_ITEMS.RECAP,
    [DEAL_STATUS.DOCS_SIGNING]: LEFT_PANEL_ITEMS.DOCUMENTS,
    [DEAL_STATUS.DOCS_PRINTED]: isRRG() ? LEFT_PANEL_ITEMS.INVOICING : LEFT_PANEL_ITEMS.CASHIERING,
    [DEAL_STATUS.INVOICED]: LEFT_PANEL_ITEMS.CASHIERING,
    [DEAL_STATUS.BOOKED]: isInchcapeOrRRG() ? LEFT_PANEL_ITEMS.CASHIERING : LEFT_PANEL_ITEMS.CONTRACTS,
    [DEAL_STATUS.CLOSED_OR_SOLD]: LEFT_PANEL_ITEMS.DESKING,
    [DEAL_STATUS.LOST]: LEFT_PANEL_ITEMS.RECAP,
    [DEAL_STATUS.UNWIND]: LEFT_PANEL_ITEMS.RECAP,
    [DEAL_STATUS.PREORDER]: LEFT_PANEL_ITEMS.DESKING,
    [DEAL_STATUS.PRE_CLOSED]: LEFT_PANEL_ITEMS.DESKING,
    [DEAL_STATUS.ON_ORDER]: LEFT_PANEL_ITEMS.DESKING,
    [DEAL_STATUS.PARTIAL_INVOICED]: LEFT_PANEL_ITEMS.INVOICING,
  };
};

export const LEFT_PANEL_FINISH_DEAL_STATUS_MAP = () => ({
  [LEFT_PANEL_ITEMS.DESKING]: DEAL_STATUS.QUOTE,
  [LEFT_PANEL_ITEMS.FINANCE_AND_INSURANCE]: DEAL_STATUS.FINANCE_AND_INSURANCE,
  [LEFT_PANEL_ITEMS.FINANCE]: DEAL_STATUS.PRE_QUALIFICATION,
  [LEFT_PANEL_ITEMS.RECAP]: isRRG() || isInchcape() ? DEAL_STATUS.RECAP : DEAL_STATUS.FINANCE_AND_INSURANCE,
  [LEFT_PANEL_ITEMS.DOCUMENTS]: DEAL_STATUS.DOCS_SIGNING,
  [LEFT_PANEL_ITEMS.INVOICING]: DEAL_STATUS.DOCS_PRINTED,
  [LEFT_PANEL_ITEMS.CASHIERING]: isRRG() || isInchcape() ? DEAL_STATUS.INVOICED : DEAL_STATUS.DOCS_PRINTED,
  [LEFT_PANEL_ITEMS.TRANSACTION_POSTING]: DEAL_STATUS.PRE_CLOSED,
});

export const LOAN = 'LOAN';
export const LEASE = 'LEASE';
export const CASH = 'CASH';
export const ONE_TIME_LEASE = 'ONE_TIME_LEASE';
export const BALLOON = 'BALLOON';

export const OWNERSHIP_TYPES = {
  LOAN: 'LOAN',
  LEASE: 'LEASE',
};

export const PAYMENT_FREQUENCY = {
  MONTHLY: 'MONTHLY',
  WEEKLY: 'WEEKLY',
  BIWEEKLY: 'BIWEEKLY',
  ONE_TIME: 'ONE_TIME',
  YEARLY: 'YEARLY',
  SEMI_MONTHLY: 'SEMI_MONTHLY',
};

export const PAYMENT_FREQUENCY_DISPLAY = {
  MONTHLY: 'Mo',
  WEEKLY: 'Wk',
  BIWEEKLY: 'Bi',
  ONE_TIME: 'One Time',
  YEARLY: 'Yearly',
  SEMI_MONTHLY: 'Sm',
};

export const PAYMENT_FREQUENCY_MSCAN_MAP = {
  MONTHLY: 0,
  BIWEEKLY: 1,
  WEEKLY: 2,
  ONE_TIME: undefined,
  YEARLY: 3,
};

export const PAYMENT_FREQUENCY_CALCTYPE_MSCAN_MAP = {
  BIWEEKLY: 1,
  WEEKLY: 1,
};

export const PAYMENT_FREQUENCY_DISPLAY_MAP = {
  MONTHLY: __('Monthly'),
  WEEKLY: __('Weekly'),
  BIWEEKLY: __('Bi-Weekly'),
  ONE_TIME: __('One Time'),
  YEARLY: __('Yearly'),
  SEMI_MONTHLY: __('Semi-Monthly'),
};

export const PAYMENT_FREQUENCY_SHORT_DISPLAY = {
  MONTHLY: __('/mo'),
  WEEKLY: __('/wk'),
  BIWEEKLY: __('/bi'),
  ONE_TIME: __(''),
  YEARLY: __('/Yr'),
  SEMI_MONTHLY: __('/Sm'),
};

export const PAYMENT_TYPES = {
  LOAN,
  LEASE,
  CASH,
  ONE_TIME_LEASE,
  BALLOON,
};

export const PAYMENT_TYPES_DISPLAY_MAP = {
  LOAN: __('Loan'),
  LEASE: __('Lease'),
  CASH: __('Cash'),
  ONE_TIME_LEASE: __('One Pay Lease'),
  BALLOON: __('Balloon'),
};

export const SCAN_MODE_PAYMENT_TYPE_MAP = {
  LOAN: 3,
  LEASE: 1,
  CASH: 4,
  ONE_TIME_LEASE: 2,
  BALLOON: 1,
};

export const PAYMENT_TYPES_INDICATOR = {
  LOAN: salmon,
  CASH: darkCyan,
  LEASE: violetBlue,
  ONE_TIME_LEASE: violetBlue,
  BALLOON: salmon,
};

export const PAYMENT_TYPES_FILL_COLOR = {
  LOAN: bizzare,
  CASH: tranquil,
  LEASE: selago,
  ONE_TIME_LEASE: selago,
  BALLOON: bizzare,
};

export const MARKET_SCAN_PAYMENT_TYPES = {
  [PAYMENT_TYPES.LOAN]: 'Retail',
  [PAYMENT_TYPES.LEASE]: 'Lease',
  [PAYMENT_TYPES.CASH]: 'Cash',
  [PAYMENT_TYPES.ONE_TIME_LEASE]: 'OnePayLease',
  [PAYMENT_TYPES.BALLOON]: 'Balloon',
};

export const MARKET_SCAN_FILTER_TYPES = {
  [PAYMENT_TYPES.LOAN]: 'Retail',
  [PAYMENT_TYPES.CASH]: 'Retail',
  [PAYMENT_TYPES.LEASE]: 'Lease',
  [PAYMENT_TYPES.ONE_TIME_LEASE]: 'Lease',
  [PAYMENT_TYPES.BALLOON]: 'Lease',
};

export const RETAIL_PAYMENT_TYPES = [PAYMENT_TYPES.LOAN, PAYMENT_TYPES.CASH];

export const PAYMENT_TYPE_MARKET_SCAN_MAP = {
  Retail: PAYMENT_TYPES.LOAN,
  Lease: PAYMENT_TYPES.LEASE,
  Cash: PAYMENT_TYPES.CASH,
  OnePayLease: PAYMENT_TYPES.ONE_TIME_LEASE,
  Balloon: PAYMENT_TYPES.BALLOON,
};

export const FEE_OVERRIDES = {
  ALLOW_USER_OVERRIDE: 'ALLOW_USER_OVERRIDE',
  EXCLUDE_TAX_CALCULATIONS: 'EXCLUDE_TAX_CALCULATIONS',
};

export const DESKING_DISPLAY_NAME_FIELD_TYPES = {
  LENDER: 'LENDER',
  APR_MONEYFACTOR: 'APR_MONEYFACTOR',
  RESIDUAL: 'RESIDUAL',
  YEARLY_MILES: 'YEARLY_MILES',
  REBATE: 'REBATE',
  DUEBILLS: 'DUEBILLS',
  FEES: 'FEES',
  FNIMENU: 'FNIMENU',
  DAYS_TO_FIRSTPAYMENT: 'DAYS_TO_FIRSTPAYMENT',
  SECURITY_DEPOSIT: 'SECURITY_DEPOSIT',
  PAYMENT_FREQUENCY: 'PAYMENT_FREQUENCY',
  FINANCE_ADJUSTED_CAPITAL_COST: 'FINANCE_ADJUSTED_CAPITAL_COST',
  LOAN_TO_VALUE_RATIO: 'LOAN_TO_VALUE_RATIO',
  TAXES: 'TAXES',
  DRIVE_OFF: 'DRIVE_OFF',
  OUT_OF_POCKET_CASH: 'OUT_OF_POCKET_CASH',
  FINANCE_CHARGE: 'FINANCE_CHARGE',
};

export const FNI_CATEGORIES = {
  WARRANTY: 'WARRANTY',
  GAP: 'GAP',
  INSURANCE: 'INSURANCE',
  SERVICE_CONTRACT: 'SERVICE_CONTRACT',
  MULTI_LEVEL_SERVICE_CONTRACT: 'MULTI_LEVEL_SERVICE_CONTRACT',
};

export const CATEGORIES_BASED_ON_DISCLOSURE_TYPE = {
  SERVICE_CONTRACT: FNI_CATEGORIES.SERVICE_CONTRACT,
  INSURANCE: FNI_CATEGORIES.INSURANCE,
  DEBT_CANCELLATION_AGGREEMENT: FNI_CATEGORIES.GAP,
};

export const TABS_THAT_NEEDS_SUBHEADER = [LEFT_PANEL_ITEMS.DESKING, LEFT_PANEL_ITEMS.RECAP];

export const DUE_BILL_DATA_SOURCE = {
  PARTS: 'PARTS',
  SERVICES: 'SERVICES',
  SETUP: 'SETUP',
  VEHICLE_EQUIPMENTS: 'LENDER SPECIFIC RESIDUALIZED OPTIONS',
  OEM_ACCESSORIES: 'OEM_ACCESSORIES',
  HE_CHARGING_ACCESSORIES: 'HE_CHARGING_ACCESSORIES',
  OEM_SUBSCRIPTIONS: 'OEM_SUBSCRIPTIONS',
  SALES_SETUP: 'SALES_SETUP',
  PRE_CONFIGURED: 'PRE_CONFIGURED',
};

export const DUE_BILL_DATA_SOURCE_VS_DISPLAY_LABELS = {
  [DUE_BILL_DATA_SOURCE.PARTS]: __('Parts'),
  [DUE_BILL_DATA_SOURCE.SERVICES]: __('Services'),
  [DUE_BILL_DATA_SOURCE.SETUP]: __('Setup'),
  [DUE_BILL_DATA_SOURCE.VEHICLE_EQUIPMENTS]: __('Lender Specific Residualized Options'),
  [DUE_BILL_DATA_SOURCE.OEM_ACCESSORIES]: __('OEM Accessories'),
  [DUE_BILL_DATA_SOURCE.HE_CHARGING_ACCESSORIES]: __('HE Charging Accessories'),
  [DUE_BILL_DATA_SOURCE.OEM_SUBSCRIPTIONS]: __('Subscriptions'),
};

export const GROSS_TYPES = {
  FRONT: 'FRONT',
  BACK: 'BACK',
};

export const BACK_GROSS_TYPES = {
  FRONT_OFFICE_SALE: 'FRONT_OFFICE_SALE',
  BACK_OFFICE_SALE: 'BACK_OFFICE_SALE',
};

export const GROSS_TYPES_LABELS = {
  FRONT: __('Front Gross'),
  BACK: __('Back Gross'),
};

export const DUE_BILL_PROFIT_TYPES = [
  {
    label: __('Front[[dealsDueBillProfitType]]'),
    value: GROSS_TYPES.FRONT,
  },
  {
    label: __('Back[[dealsDueBillProfitType]]'),
    value: GROSS_TYPES.BACK,
  },
];

export const DEFAULT_MILEAGE_TYPE = 'mi';

export const GROSS_CATEGORY = {
  VEHCILE_SALE: 'VEHCILE_SALE',
  TRADE_IN: 'TRADE_IN',
  SUBVENTION_COST: 'SUBVENTION_COST',
  FRONT_COST_ADJUSTMENTS: 'FRONT_COST_ADJUSTMENTS',
  FRONT_DUE_BILLS_OR_ACCESSORIES: 'FRONT_DUE_BILLS_OR_ACCESSORIES',
  FRONT_CASH_DEFICIENCY: 'FRONT_CASH_DEFICIENCY',
  FRONT_DEALER_CASH: 'FRONT_DEALER_CASH',
  FINANCE_RESERVES: 'FINANCE_RESERVES',
  FNI_PRODUCTS: 'FNI_PRODUCTS',
  BACK_COST_ADJUSTMENTS: 'BACK_COST_ADJUSTMENTS',
  BACK_DUE_BILLS_OR_ACCESSORIES: 'BACK_DUE_BILLS_OR_ACCESSORIES',
  BANK_FEE_MARKUP: 'BANK_FEE_MARKUP',
  CAPPED_INCEPTION_FEE: 'CAPPED_INCEPTION_FEE',
  FRONT_FNI_PRODUCTS: 'FRONT_FNI_PRODUCTS',
  BACK_FNI_PRODUCTS: 'BACK_FNI_PRODUCTS',
  ACQUISITION_FEE_MARKUP: 'ACQUISITION_FEE_MARKUP',
};

export const GROSS_CATEGORY_LABELS = deskingFieldsConfigs => {
  const fniMenuDN = deskingFieldsConfigs[DESKING_DISPLAY_NAME_FIELD_TYPES.FNIMENU];
  const accessoriesDN = deskingFieldsConfigs[DESKING_DISPLAY_NAME_FIELD_TYPES.DUEBILLS];

  return {
    [GROSS_CATEGORY.VEHCILE_SALE]: __('Vehicle Sale'),
    [GROSS_CATEGORY.TRADE_IN]: __('Trade-ins'),
    [GROSS_CATEGORY.SUBVENTION_COST]: __('Subvention Cost'),
    [GROSS_CATEGORY.FRONT_COST_ADJUSTMENTS]: __('Cost Adjustment'),
    [GROSS_CATEGORY.FRONT_DUE_BILLS_OR_ACCESSORIES]: !_isEmpty(accessoriesDN) ? accessoriesDN : __('Accessories'),
    [GROSS_CATEGORY.FRONT_CASH_DEFICIENCY]: __('Cash Deficiency'),
    [GROSS_CATEGORY.FRONT_DEALER_CASH]: DEALER_CASH_LABEL,
    [GROSS_CATEGORY.FINANCE_RESERVES]: isInchcape() ? __('Finance Commission') : __('Finance Reserve'),
    [GROSS_CATEGORY.FNI_PRODUCTS]: !_isEmpty(fniMenuDN) ? fniMenuDN : __('F&I Menu'),
    [GROSS_CATEGORY.BACK_COST_ADJUSTMENTS]: __('Cost Adjustment'),
    [GROSS_CATEGORY.BACK_DUE_BILLS_OR_ACCESSORIES]: !_isEmpty(accessoriesDN) ? accessoriesDN : __('Accessories'),
    [GROSS_CATEGORY.BANK_FEE_MARKUP]: __('Bank Fee Markup'),
    [GROSS_CATEGORY.CAPPED_INCEPTION_FEE]: __('Rolled Over Cash Def'),
    [GROSS_CATEGORY.FRONT_FNI_PRODUCTS]: !_isEmpty(fniMenuDN) ? fniMenuDN : __('F&I Menu'),
    [GROSS_CATEGORY.BACK_FNI_PRODUCTS]: !_isEmpty(fniMenuDN) ? fniMenuDN : __('F&I Menu'),
    [GROSS_CATEGORY.ACQUISITION_FEE_MARKUP]: __('Acquisition Fee Markup'),
  };
};

export const ROW_IDS = {
  FEE_DESKING_ROW: 'feesDeskingRow',
  TAX_ROW: 'taxRow',
  LENDER_ROW: 'LENDER_ROW',
  APR_MONEYFACTOR_ROW: 'APR_MONEYFACTOR_ROW',
  RESIDUAL_ROW: 'RESIDUAL_ROW',
  YEARLY_MYEARLY_MILES_ROWILES: 'YEARLY_MILES_ROW',
  REBATE_ROW: 'REBATE_ROW',
  DUEBILLS_ROW: 'DUEBILLS_ROW',
  FNIMENU_ROW: 'FNIMENU_ROW',
  DAYS_TO_FIRSTPAYMENT_ROW: 'DAYS_TO_FIRSTPAYMENT_ROW',
  SECURITY_DEPOSIT_ROW: 'SECURITY_DEPOSIT_ROW',
  PAYMENT_FREQUENCY_ROW: 'PAYMENT_FREQUENCY_ROW',
  FINANCE_ADJUSTED_CAPITAL_COST_ROW: 'FINANCE_ADJUSTED_CAPITAL_COST_ROW',
  LOAN_TO_VALUE_RATIO_ROW: 'LOAN_TO_VALUE_RATIO_ROW',
  ADD_COLUMN: 'ADD_COLUMN',
};

export const DEFAULT_PAYMENT_TYPE_FILTER = '1000';
export const DEFAULT_REGION_ID = 0;

export const CASTING_ENABLED_MODULE = {
  [SALES_PRINTING_MODULE_NAMES.DEAL_SHEET]: {
    enable: true,
  },
  [SALES_PRINTING_MODULE_NAMES.COVER_SHEET]: {
    enable: true,
  },
  [SALES_PRINTING_MODULE_NAMES.DEAL_RECAP_SHEET]: {
    enable: true,
  },
  [SALES_PRINTING_MODULE_NAMES.DEAL_JACKET]: {
    enable: true,
  },
  [SALES_PRINTING_MODULE_NAMES.FNI_MENU_SHEET]: {
    enable: true,
  },
};

export const MARKUP_DIVISOR = 1000;

export const RATE_TYPE_FACTOR = 2400;

export const FEES_STATUS = {
  ACTIVE: 'ACTIVE',
};

export const MILEAGE_STATUS_OPTIONS = {
  ACTUAL: 'ACTUAL',
  NOT_ACTUAL: 'NOT_ACTUAL',
  EXCEEDS_MECH_LIMITS: 'EXCEEDS_MECH_LIMITS',
};

export const RESERVE_METHOD = {
  MARKUP_BASED: 0,
  FLAT_AMOUNT: 1,
  CAPCOST_BASED: 2,
};

export const MARKUP_TYPE_VS_RESERVE_METHOD = {
  [MARKUP_TYPES.MARKUP_BASED]: RESERVE_METHOD.MARKUP_BASED,
  [MARKUP_TYPES.FLAT_AMOUNT]: RESERVE_METHOD.FLAT_AMOUNT,
  [MARKUP_TYPES.CAPCOST_BASED]: RESERVE_METHOD.CAPCOST_BASED,
};

export const RESIDUAL_DISPLAY_TYPE = {
  PERCENTAGE: 'PERCENTAGE',
  AMOUNT: 'AMOUNT',
};

export const LEASE_CONTRACT_FEES = [INTERNAL_FEE_CODES.DISPOSITION_FEE, INTERNAL_FEE_CODES.LOAN_PROCESSING_FEE];

export const MS_FEES = [
  INTERNAL_FEE_CODES.BANK_OR_ACQUISITION_FEE,
  INTERNAL_FEE_CODES.REGISTRATION_CHARGES,
  INTERNAL_FEE_CODES.STATE_FEE,
  INTERNAL_FEE_CODES.TITLE_FEE,
  INTERNAL_FEE_CODES.WASTE_TIRE_FEE,
  INTERNAL_FEE_CODES.LICENSE_FEE,
  INTERNAL_FEE_CODES.LIEN_FEE,
  INTERNAL_FEE_CODES.STATE_EMISSION_EXEMPTION_FEE,
  INTERNAL_FEE_CODES.PLATE_TRANSFER_FEE,
  INTERNAL_FEE_CODES.PUBLIC_SAFETY_VEHICLE_FEE,
  INTERNAL_FEE_CODES.TRANSFER_TAX,
  INTERNAL_FEE_CODES.WHEELAGE_TAX,
  INTERNAL_FEE_CODES.EV_REGISTRATION,
  INTERNAL_FEE_CODES.ENVIRONMENT_PROTECT_FEE,
];

export const MILEAGE_TYPES = {
  MILES: 'MILES',
  KM: 'KM',
};

export const KM_TO_MILES_CONVERTOR_FACTOR = 1.60934;

export const DOWN_PAYMENT_TYPES = {
  LOAN_DOWNPAYMENTS: 'dp_loan',
  LEASE_DOWNPAYMENTS: 'dp_lease',
};

export const DOWNPAYMENT_PERCENTAGE = 'downPaymentPct';

export const PAYMENT_TYPE_DOWNPAYMENT_TYPE_MAPPING = {
  [PAYMENT_TYPES.LOAN]: DOWN_PAYMENT_TYPES.LOAN_DOWNPAYMENTS,
  [PAYMENT_TYPES.CASH]: DOWN_PAYMENT_TYPES.LOAN_DOWNPAYMENTS,
  [PAYMENT_TYPES.LEASE]: DOWN_PAYMENT_TYPES.LEASE_DOWNPAYMENTS,
  [PAYMENT_TYPES.ONE_TIME_LEASE]: DOWN_PAYMENT_TYPES.LEASE_DOWNPAYMENTS,
  [PAYMENT_TYPES.BALLOON]: DOWN_PAYMENT_TYPES.LOAN_DOWNPAYMENTS,
};

export const PAYMENT_TYPE_SELLINGPRICE_MAPPING = {
  [PAYMENT_TYPES.LOAN]: 'sellingPrice',
  [PAYMENT_TYPES.CASH]: 'sellingPrice',
  [PAYMENT_TYPES.LEASE]: 'leaseSellingPrice',
  [PAYMENT_TYPES.ONE_TIME_LEASE]: 'leaseSellingPrice',
  [PAYMENT_TYPES.BALLOON]: 'sellingPrice',
};

export const SOURCES_OF_FEES = {
  MARKET_SCAN: 'MARKET_SCAN',
  SALES_SETUP: 'SALES_SETUP',
  MANUALLY_ADDED: 'MANUALLY_ADDED',
  CALC_ENGINE: 'CALC_ENGINE',
  GALAXY: 'GALAXY',
  PRE_CONFIGURED: 'PRE_CONFIGURED',
};

export const PERMISSION_BASED_DISABLE_SOURCE_TYPE = ['SALES_SETUP', 'PRE_CONFIGURED'];

export const KEYS_FOR_PROVIDER = [
  'vehiclesInfo',
  'customersInfo',
  'bootstrapData',
  'salesChainList',
  'fieldsMapToId',
  'viSettingsRes',
  'deal',
  'downPayments',
  'deferredPayments',
  'deskingpaymentDetails',
  'fniMenu',
  'feeUpdateInfos',
  'fniAttributesCodes',
  'fniProductsList',
  'rebateFilters',
  'isDealDirty',
  'dealAcquiredInfo',
  'glBalance',
  'glAccountId',
  'lenders',
  'dealerZip',
  'creditApplications',
  'trimInfos',
  'DeskingActions',
  'CommonActions',
  'LeadsActions',
  'scanDocuments',
  'docsetData',
  'dealJacket',
  'packageDocuments',
  'sppPackageDocuments',
  'allDealForms',
  'salesSetupInfo',
  'dealTypesFromSetup',
  'showPdfLoaderDealJacket',
  'canCallMarketScan',
  'documentsToHighlightInDealJacket',
  'orderedFniProductsList',
  'creditApplicationDecisionMappings',
  'deptOfRegOptions',
  'cmsTekionLenderCodes',
  'hasEstimatedDeliveryDateChanged',
];
export const ROLLED_DEALS = 'rolledDeals';
export const AUTO_ROLL = 'autoRoll';

export const RESIDUAL_VALUE_FOR_EMPTY_PROGRAM_INFO = {
  residualOverridden: false,
  residualDisplayType: RESIDUAL_DISPLAY_TYPE.PERCENTAGE,
};

export const DOCUMENT_ASSET_TYPES = {
  DEAL_CONCIERGE_VEHICLE_WALKTHROUGH: 'SALES_VEHICLE_WALKTHROUGH',
  SALES_CONSUMER_DOCS: 'SALES_CONSUMER_DOCS',
};

export const ACTIVITY_ASSET_TYPES = {
  DEAL: 'DEAL',
};

export const KEYS_FOR_CUSTOMER_OWNED_VEHICLE = ['vin', 'year', 'make', 'model', 'vehicleId'];

export const ADJUSTMENT_TYPE = {
  RO: 'RO',
  PO: 'PO',
};

export const ECONTARCT_STATUS_TYPE = {
  SUCCESS: 'Success',
  FAILED: 'Failed',
};

export const NOTIFICATION_TYPE = {
  ECONTRACT_FORM_DETAILS_UPDATE: 'ECONTRACT_FORM_DETAILS_UPDATE',
  FORM_DOC_UPDATE: 'FORM_DOC_UPDATE',
  FORM_SHARED: 'FORM_SHARED',
  FORM_DOC_SUBMIT: 'FORM_DOC_SUBMIT',
  FORM_PACKAGE_SUBMIT: 'FORM_PACKAGE_SUBMIT',
  DRS_FORM_SIGN_STATUS_UPDATE: 'DRS_FORM_SIGN_STATUS_UPDATE',
  ONLINE_CAST_FOR_SIGNING: 'ONLINE_CAST_FOR_SIGNING',
  CONSUMER_DOCS_UPDATES: 'CONSUMER_DOCS_UPDATES',
  PDF_SIGNS_UPDATES: 'PDF_SIGNS_UPDATES',
  DEAL_MERGE_UPDATES: 'DEAL_MERGE_UPDATES',
  PAYMENT_REFRESH: 'PAYMENT_REFRESH',
  FORM_ERROR_DETAILS: 'FORM_ERROR_DETAILS',
  CONTRACT_GENERATION_CHANGES: 'CONTRACT_GENERATION_CHANGES',
  FORMS_VIEW: 'FORMS_VIEW',
  ECONTRACT_PACKAGE_SUBMIT: 'ECONTRACT_PACKAGE_SUBMIT',
  FETCH_DEAL_FORMS: 'FETCH_DEAL_FORMS',
  SIGNING_METADATA_UPDATES: 'SIGNING_METADATA_UPDATES',
  FNI_GENERATION: 'FNI_GENERATION',
  FNI_MENU_RERATE: 'FNI_MENU_RERATE',
  FNI_CASTING_UPDATE: 'FNI_CASTING_UPDATE',
  FNI_ROLLBACK_GENERATION: 'FNI_ROLLBACK_GENERATION',
  FNI_PRODUCT_REGISTRATION_RESPONSE: 'FNI_PRODUCT_REGISTRATION_RESPONSE',
  PRODUCT_RATE_GENERATE: 'PRODUCT_RATE_GENERATE',
  DEFAULT_PLAN_CODE: 'DEFAULT_PLAN_CODE',
  DEAL_LEAD_MERGED: 'DEAL_LEAD_MERGED',
  AUTOROUTED_LENDER_DECISION: 'AUTOROUTED_LENDER_DECISION',
  ACCEPT_DECLINE_SHEET_GENERATION: 'ACCEPT_DECLINE_SHEET_GENERATION',
  EXTERNAL_FORM_REGENERATE: 'EXTERNAL_FORM_REGENERATE',
  DEAL_FORMS_SIGN_STATUS: 'DEAL_FORMS_SIGN_STATUS',
  DEAL_FORMS_VAULT_STATUS: 'DEAL_FORMS_VAULT_STATUS',

  MERGED_SCANNED_DOCUMENT: 'MERGED_SCANNED_DOCUMENT',
  ALL_CASTING_STOPPED: 'ALL_CASTING_STOPPED',
  PEN_FORM_UPDATED_OR_REGENERATED: 'PEN_FORM_UPDATED_OR_REGENERATED',

  ORDER_FORM: 'ORDER_FORM',
  INTERNET_DEAL_UPDATE: 'INTERNET_DEAL_UPDATE',
  DEAL_CUSTOMER_CIAM_UPDATE: 'DEAL_CUSTOMER_CIAM_UPDATE',
  DEAL_VEHICLE_DELIVERY_DATE_UPDATE: 'DEAL_VEHICLE_DELIVERY_DATE_UPDATE',
  RO_UPDATES: 'RO_UPDATES',
  CREDIT_CUSTOMER_UPDATE: 'CREDIT_CUSTOMER_UPDATE',
  WORKFLOW_UPDATES: 'WORKFLOW_UPDATES',
  STOCK_ID_UPDATE: 'STOCK_ID_UPDATE',
  SALES_VEHICLE_TRANSFER_STATUS_UPDATE: 'SALES_VEHICLE_TRANSFER_STATUS_UPDATE',
  UPDATE_CONTRACT_DATE: 'UPDATE_CONTRACT_DATE',
  FNI_PRODUCT_RIC_VOID: 'FNI_PRODUCT_RIC_VOID',
};

export const DEAL_MODIFIEID_ERRORS = {
  [NOTIFICATION_TYPE.DEAL_LEAD_MERGED]: __('Deal has been modified with CRM user updates. Please Reload the page!'),
  [NOTIFICATION_TYPE.AUTOROUTED_LENDER_DECISION]: __(
    'Deal has been modified from Auto Routing. Please reload the page..!'
  ),
  [NOTIFICATION_TYPE.INTERNET_DEAL_UPDATE]: __('Deal has been modified by customer. Please click to reload.'),
  [NOTIFICATION_TYPE.RO_UPDATES]: __('RO Details have been updated. Kindly refresh for the latest updates.”'),
};

const PRESET_FOR_DOWN_PAYMENT_WITH_FRIVE_OFF = [
  {
    leaseWorkingCashType: LEASE_WORKING_CASH.USE_DOWNPAYMENT_FOR_CCR,
    enabled: true,
  },
  {
    leaseWorkingCashType: LEASE_WORKING_CASH.CASH_ALWAYS_COVERS_DRIVEOFF,
    enabled: true,
  },
  {
    leaseWorkingCashType: LEASE_WORKING_CASH.AUTO_ROLL_CASH_DEFICIENCY,
    enabled: false,
  },
];

const PRESET_FOR_TOTAL_OUT_OF_POCKET = [
  {
    leaseWorkingCashType: LEASE_WORKING_CASH.USE_DOWNPAYMENT_FOR_CCR,
    enabled: false,
  },
  {
    leaseWorkingCashType: LEASE_WORKING_CASH.CASH_ALWAYS_COVERS_DRIVEOFF,
    enabled: false,
  },
  {
    leaseWorkingCashType: LEASE_WORKING_CASH.AUTO_ROLL_CASH_DEFICIENCY,
    enabled: true,
  },
];

export const LEASE_WORKING_CASH_BASED_ON_PRESET = {
  [LEASE_WORKING_CASH_PRESET.TOTAL_OUT_OF_POCKET]: PRESET_FOR_TOTAL_OUT_OF_POCKET,
  [LEASE_WORKING_CASH_PRESET.SIGN_AND_DRIVE]: PRESET_FOR_TOTAL_OUT_OF_POCKET,
  [LEASE_WORKING_CASH_PRESET.DOWN_PAYMENT_WITH_DRIVE_OFF]: PRESET_FOR_DOWN_PAYMENT_WITH_FRIVE_OFF,
  [LEASE_WORKING_CASH_PRESET.DOWN_PAYMENT_WITH_MAX_DRIVE_OFF]: PRESET_FOR_DOWN_PAYMENT_WITH_FRIVE_OFF,
  [LEASE_WORKING_CASH_PRESET.DOWN_PAYMENT_WITH_MIN_DRIVE_OFF]: PRESET_FOR_DOWN_PAYMENT_WITH_FRIVE_OFF,
};

export const SECURITY_DEPOSIT_WAIVER_REASON = {
  CREDIT_EXCEPTION: 'CREDIT_EXCEPTION',
  LEASE_LOYALTY: 'LEASE_LOYALTY',
  MARKETING_PROGRAM: 'MARKETING_PROGRAM',
  SECURITY_DEPOSIT_PURCHASED: 'SECURITY_DEPOSIT_PURCHASED',
  WEAR_WAIVER: 'WEAR_WAIVER',
  WAIVER_ADJUSTMENT: 'WAIVER_ADJUSTMENT',
};

export const SECURITY_DEPOSIT_WAIVER_REASON_LABEL = {
  [SECURITY_DEPOSIT_WAIVER_REASON.CREDIT_EXCEPTION]: __('Credit Exception'),
  [SECURITY_DEPOSIT_WAIVER_REASON.LEASE_LOYALTY]: __('Lease Loyalty'),
  [SECURITY_DEPOSIT_WAIVER_REASON.MARKETING_PROGRAM]: __('Marketing Program'),
  [SECURITY_DEPOSIT_WAIVER_REASON.SECURITY_DEPOSIT_PURCHASED]: __('Security Deposit Purchased'),
  [SECURITY_DEPOSIT_WAIVER_REASON.WEAR_WAIVER]: __('Wear Waiver'),
  [SECURITY_DEPOSIT_WAIVER_REASON.WAIVER_ADJUSTMENT]: __('Waiver Adjustment'),
};

export const SECURITY_DEPOSIT_OPTIONS = _map(SECURITY_DEPOSIT_WAIVER_REASON, value => ({
  value,
  label: SECURITY_DEPOSIT_WAIVER_REASON_LABEL[value],
}));

export const MANUALLY_UPDATED_API_KEY = 'manuallyUpdated';
export const ZIP_CODE_MANUALLY_UPDATED_API_KEY = 'zipCodeManuallyUpdated';
export const TAX_RATE_MANUALLY_UPDATED_API_KEY = 'taxRateManuallyUpdated';
export const TAXES_MANNUALLY_OVERRIDE = 'taxRateOverridden';
export const SECOND_TAX_RATE_MANUALLY_UPDATED_API_KEY = 'secondTaxRateManuallyUpdated';
export const TOTAL_TAX_OVERRIDDEN = 'totalTaxOverridden'; // true when main tax rate is overriden, otherwise false. applicable fo US calcengine
export const DEFAULT_COUNTY_MATCH = 'defaultCountyMatch';

export const CONCIERGE_NOTIFICATION_LABEL = {
  CONSUMER_DOC_UPDATE: 'Customer has uploaded a document',
  PAYMENT_UPDATE: 'Payment has been updated by the customer',
};

/**
 * Deal Types disabled in dealType change dropdown
 *
 * Only Trades and Refinance deals disable vehicle selection and might cause
 * inconsistencies and hence are disabled
 */
export const DISABLED_DEAL_TYPES_AFTER_DEAL_CREATION = [DEAL_TYPES.ONLY_TRADES, DEAL_TYPES.REFINANCING];

export const CAP_COST_BASED = 'CAP_COST_BASED';
export const PENALTY_PER_MILE_OVERRIDDEN_BASED = 'PENALTY_PER_MILE_OVERRIDDEN_BASED';

export const CERTIFIED_FLAGS_VS_INFO_TEXT = {
  OEM_CERTIFIED: __(
    "CPO refers to certification of the vehicle in accordance with the OEM's specifications and requirements"
  ),
  DEALER_CERTIFIED: __(
    'Dealer Certified refers to certification of vehicles just by your dealership and not by the OEM'
  ),
};

export const DEAL_ACQUIRE_KEY = 'dealAcquireKey';

export const FNI_MESSAGES = {
  CONNECTING_TO_NETWORK: __('Pulling rates, Please wait.'),
  PRODUCT_NOT_SELECTED: __('Please select products to enable the option'),
  FNI_CASTING: __('Please clear the products with errors before casting.'),
  MANUAL_RATING: __('Re-rate product before proceeding to Contracts'),
};

export const RV_TYPES = ['Motorized', 'Towable', 'Other'];

export const PAYMENT_TYPES_VS_FEES_NOT_APPLICABLE = {
  [PAYMENT_TYPES.LOAN]: [INTERNAL_FEE_CODES.BANK_OR_ACQUISITION_FEE, INTERNAL_FEE_CODES.DISPOSITION_FEE],
  [PAYMENT_TYPES.CASH]: [
    INTERNAL_FEE_CODES.BANK_OR_ACQUISITION_FEE,
    INTERNAL_FEE_CODES.DISPOSITION_FEE,
    INTERNAL_FEE_CODES.LOAN_PROCESSING_FEE,
  ],
  [PAYMENT_TYPES.BALLOON]: [
    INTERNAL_FEE_CODES.BANK_OR_ACQUISITION_FEE,
    INTERNAL_FEE_CODES.DISPOSITION_FEE,
    INTERNAL_FEE_CODES.LOAN_PROCESSING_FEE,
  ],
  [PAYMENT_TYPES.LEASE]: [INTERNAL_FEE_CODES.LOAN_PROCESSING_FEE],
  [PAYMENT_TYPES.ONE_TIME_LEASE]: [INTERNAL_FEE_CODES.LOAN_PROCESSING_FEE],
};

export const PAYMENT_TYPES_VS_SPECIAL_FEES_APPLICABLE = {
  [PAYMENT_TYPES.LOAN]: [INTERNAL_FEE_CODES.LOAN_PROCESSING_FEE],
  [PAYMENT_TYPES.CASH]: [],
  [PAYMENT_TYPES.BALLOON]: [],
  [PAYMENT_TYPES.LEASE]: [INTERNAL_FEE_CODES.BANK_OR_ACQUISITION_FEE, INTERNAL_FEE_CODES.DISPOSITION_FEE],
  [PAYMENT_TYPES.ONE_TIME_LEASE]: [INTERNAL_FEE_CODES.BANK_OR_ACQUISITION_FEE, INTERNAL_FEE_CODES.DISPOSITION_FEE],
};

export const GROSS_ITEMS_NOT_APPLICABLE_FOR_RV_DEALERS = [
  GROSS_CATEGORY.FRONT_CASH_DEFICIENCY,
  GROSS_CATEGORY.ACQUISITION_FEE_MARKUP,
  GROSS_CATEGORY.FRONT_DEALER_CASH,
];

export const TAXABLE_FIELD = 'taxable';

export const getDealNotificationChannelInfo = defaultMemoize(dealNumber => ({ type: 'DEALS', extras: { dealNumber } }));
// Memoizing this to prevent having different object references everytime it's called.

export const TRIM_KEYS_TO_OMIT = ['marketClassCategory', 'languages', 'maximumSpeedMeasure'];

export const COLUMN_SOURCE = {
  LENDER_DECISION: 'LENDER_DECISION',
};
export const MAX_CHARACTER_FOR_VEHICLE_OPTION_CONTAINER_DEALSHEET = 521;
export const MAX_CHARACTER_FOR_VEHICLE_OPTION_CONTAINER_MULTI_VEHICLE = 374;

export const DOCUMENT_SIGNING_MODALS = {
  SHARED_DOCUMENTS_PREVIEW: 'SHARED_DOCUMENTS_PREVIEW',
  PDF_CAST: 'PDF_CAST',
};

export const DEAL_SYNC_CONSTANTS = {
  LENDER: 'LENDER',
  DEAL_SYNC: 'DEAL_SYNC',
  DEAL_SYNC_LABEL: __('Deal Push Setup'),
  DEAL_SYNC_SETUP_ROUTE: 'external-dms-mapping',
};

export const DEAL_SYNC_WARNING_TYPES = {
  LENDER: 'LENDER',
  TAX: 'TAX',
  FEES: 'FEES',
  ACCESSORY: 'ACCESSORY',
  FNI: 'FNI',
  ASSIGNEE: 'ASSIGNEE',
};

export const DEAL_SYNC_STATUS = {
  AWAIT: 'AWAITED',
  FAIL: 'FAILURE',
  IN_PROGRESS: 'IN_PROGRESS',
  SUCCESS: 'SUCCESS',
};

export const getMappingValuesList = mappingValues =>
  _join(
    _map(mappingValues, value => `'${value}'`),
    ' , '
  );

export const GET_DEAL_SYNC_WARNING_CUSTOM = customValues => ({
  ACCESSORY: `Chosen accessory ${getMappingValuesList(
    customValues
  )} isn't a part of Due Bills setup, and won't be synced to DMS. Add the accessory to Due Bills setup and map it in Deal Push Setup to avoid mismatches.`,
});

export const GET_DEAL_SYNC_WARNING = mappingValues => ({
  LENDER: `Chosen AEC lender ${getMappingValuesList(
    mappingValues
  )} is not mapped to DMS lender. Please map the lender in the Deal Push Setup to avoid failures.`,
  TAX: `Chosen AEC Tax ${getMappingValuesList(
    mappingValues
  )} is not mapped to DMS Tax. Please map the Tax in the Deal Push Setup app to avoid mismatches.`,
  FEES: `Chosen AEC Fee ${getMappingValuesList(
    mappingValues
  )} is not mapped to DMS Fee. Please map the Fee in the Deal Push Setup app to avoid mismatches.`,
  ACCESSORY: `Chosen AEC accessory ${getMappingValuesList(
    mappingValues
  )} is not mapped to DMS accessory. Please map the accessory in the Deal Push Setup to avoid mismatches.`,
  FNI: `Chosen AEC F&I Product ${getMappingValuesList(
    mappingValues
  )} is not mapped to DMS F&I Product. Please map the F&I Product in the Deal Push Setup to avoid mismatches.`,
  ASSIGNEE: `Chosen AEC Assignee ${getMappingValuesList(
    mappingValues
  )} is not mapped to DMS Assignee . Please map the Assignee in the Deal Push Setup to avoid mismatches.`,
});

// this only added for translations not used in app but do not remove them
export const RRG_PANELS_VS_DISPLAY_NAMES = {
  [LEFT_PANEL_ITEMS.INVOICING]: __('Invoicing'),
};

export const GLOBAL_WARNING_TYPE = {
  ERROR: 'ERROR',
  WARNING: 'WARNING',
  LOADING: 'LOADING',
  SUCCESS: 'SUCCESS',
  FAIL: 'FAIL',
};

export const DESKING_FIELD_KEYS = {
  [LENDER_PAYMENT_FIELDS.FINANCE_CONTRACT_NUMBER]: 'financeContractNumber',
  [LENDER_PAYMENT_FIELDS.FINANCE_CONTRACT_STATUS_DTO]: 'financeContractStatusDto',
  [LENDER_PAYMENT_FIELDS.FINANCE_CONTRACT_STATUS]: 'financeContractStatusDto.contractStatus',
  [LENDER_PAYMENT_FIELDS.FINANCE_CONTRACT_STATUS_LAST_UPDATED]: 'financeContractStatusDto.lastModifiedTime',
  [LENDER_PAYMENT_FIELDS.FINANCE_CONTRACT_STATUS_RETURN_CODE]: 'financeContractStatusDto.returnCode',
  [LENDER_PAYMENT_FIELDS.FINANCE_CONTRACT_STATUS_MESSAGE]: 'financeContractStatusDto.message',
  [MORE_FIELDS.FINANCE_CHARGE]: 'financeCharge',
  [MORE_FIELDS.SUBVENTION_COST]: 'subventionCostValueAbsolute',
  [MORE_FIELDS.OUT_OF_POCKET_CASH]: 'outOfPocketCash',
  [MORE_FIELDS.DRIVE_OFF]: 'inceptionFee',
  [MORE_FIELDS.FINANCE_ADJUSTED_CAPITAL_COST]: 'amountFinanced',
  [INFO_COLUMN_FIELDS.EMI_CELL]: 'emiAmount',
  [MORE_FIELDS.SECURITY_DEPOSIT]: 'securityDeposit',
  [MORE_FIELDS.TOTAL_DEPOSIT]: 'totalDeposit',
  [MORE_FIELDS.TOTAL_AMOUNT_PAYABLE]: 'totalAmountPayable',
  [MORE_FIELDS.EXCESS_MILEAGE_CHARGE]: 'yearlyMiles.penaltyPerMile',
  [MORE_FIELDS.OPTION_TO_PURCHASE_FEE]: 'optionToPurchaseFee',
  [MORE_FIELDS.OPTIONAL_FINAL_PAYMENT]: 'optionalFinalPayment',
  [MORE_FIELDS.TOTAL_DUE_AT_SIGNING]: 'dueFromCustomer',
};

export const SOURCE_TYPE = {
  OMS: 'OMS',
};

export const LAST_MODIFIED_SOURCE_OF_DEAL = {
  DRP: 'DRP',
};

export const IS_MINIMIZE_FNI_FUNCTIONALITY_ENABLED = true;

export const FNI_MENU_CONCIERGE = 'FNI_MENU_CONCIERGE';

export const OPERATION_VS_LABEL = {
  ADD: '+',
  SUBTRACT: '-',
  MULTIPLY: '*',
  DIVIDE: '/',
  NONE: '',
};
export const ADJUSTMENT_FIELD_VS_LABEL = {
  APR: '%',
  NONE: '',
};

export const SELECT_FIELD_VISIBILITY = {
  SHOW_FIELD: 'SHOW_FIELD',
  SHOW_NO_DATA: 'SHOW_NO_DATA',
};

export const ADDITIONAL_SECURITY_DEPOSIT = 'ADDITIONAL_SECURITY_DEPOSIT';

export const WAIVER_REASON = 'WAIVER_REASON';

export const depositDataList = [
  {
    type: ADDITIONAL_SECURITY_DEPOSIT,
    id: ADDITIONAL_SECURITY_DEPOSIT,
    name: __('Additional Security Deposit'),
  },
  {
    type: WAIVER_REASON,
    id: WAIVER_REASON,
    name: __('Waiver Reason'),
  },
];

export const PROGRAM_TYPE = {
  STANDARD: 'STANDARD',
  SPECIAL: 'SPECIAL',
  CUSTOM: 'CUSTOM',
  NONE: 'NONE',
};

export const DMS_WITH_DEAL_COMPARE_SUPPORT = new Set([
  DMS_PROVIDER_LABELS[DMS_PROVIDERS.DEALER_TRACK],
  DMS_PROVIDER_LABELS[DMS_PROVIDERS.DEALER_BUILT],
  DMS_PROVIDER_LABELS[DMS_PROVIDERS.AUTOMATE],
]);

export const DMS_WITH_TAX_GROUP_SUPPORT = new Set([DMS_PROVIDER_LABELS[DMS_PROVIDERS.DEALER_TRACK]]);

export const DMS_WITH_ADD_CUSTOMER_SUPPORT = new Set([
  DMS_PROVIDER_LABELS[DMS_PROVIDERS.DEALER_BUILT],
  DMS_PROVIDER_LABELS[DMS_PROVIDERS.AUTOMATE],
  DMS_PROVIDER_LABELS[DMS_PROVIDERS.CDK],
]);

export const DUE_BILL_PAYMENT_OPTION_VALUES = {
  UPFRONT: 'UPFRONT',
  FINANCED: 'FINANCED',
};

export const DUE_BILL_PAYMENT_OPTIONS = [
  { label: __('Upfront'), value: DUE_BILL_PAYMENT_OPTION_VALUES.UPFRONT },
  { label: __('Financed'), value: DUE_BILL_PAYMENT_OPTION_VALUES.FINANCED },
];

export const DOCUMENT_SUBMISSION_RESPONSE_TYPES = {
  SUCCESS: 'SUCCESS',
  FAILURE: 'FAILURE',
};

export const REBATES_FILTERS_FOR_GALAXY = [
  {
    isDefault: false,
    type: 'Payment Type',
    values: PAYMENT_TYPES_DISPLAY_MAP,
  },
  {
    isDefault: false,
    type: 'Payment Term',
    values: EMPTY_OBJECT,
  },
];

export const CONCIERGE_EVENT_TYPES = {
  CONCIERGE_COLUMN_UPDATE: 'CONCIERGE_COLUMN_UPDATE',
};

export const MAP_OLD_KEYS_TO_NEW_KEYS = dealerId => ({
  salesPersons: [__(`${dealerId}_SALES_PERSON`)],
  salesManager: [__(`${dealerId}_SALES_MANAGER`)],
});

export const DEAL_TYPES_NOT_SUPPORTING_DYNAMIC_PRICING = [
  DEAL_TYPES.FNI,
  DEAL_TYPES.DEALER_TRADE,
  DEAL_TYPES.INTERNAL_TRANSFER,
  DEAL_TYPES.EMPLOYEE_PURCHASE,
  DEAL_TYPES.ONLY_TRADES,
  DEAL_TYPES.WHOLESALE,
];
export const DEAL_SOURCE_SUPPORTING_DYNAMIC_PRICING = [DEAL_SOURCE.INTERNET];

export const EMAIL_FLOW_TRIGGER = {
  SHARE_PRINT: 'SHARE_PRINT',
};

export const VEHICLE_TRANSFER_STATUS = {
  PENDING: STATUS.PENDING,
  APPROVED: STATUS.APPROVED,
  REJECTED: STATUS.REJECTED,
  DEFAULT: 'DEFAULT',
};

export const TRANSFER_STATUS_TO_TOASTER_MESSAGE = {
  [VEHICLE_TRANSFER_STATUS.APPROVED]: __('Vehicle Transfer Request Approved'),
  [VEHICLE_TRANSFER_STATUS.PENDING]: __('Vehicle Transfer Request Pending'),
  [VEHICLE_TRANSFER_STATUS.REJECTED]: __('Vehicle Transfer Request Rejected'),
};

export const TRANSFER_STATUS_TO_TOASTER_TYPE = {
  [VEHICLE_TRANSFER_STATUS.APPROVED]: TOASTER_TYPE.SUCCESS,
  [VEHICLE_TRANSFER_STATUS.PENDING]: TOASTER_TYPE.WARN,
  [VEHICLE_TRANSFER_STATUS.REJECTED]: TOASTER_TYPE.ERROR,
};

export const EXTERNAL_SOURCE = 'externalSource';

export const CRM_LEAD_SYNC_REBATES_SPECIFIC_FLAGS = {
  VIN_CHANGED: 'VIN_CHANGED',
  STOCK_TYPE_CHANGED: 'STOCK_TYPE_CHANGED',
  MSRP_CHANGED: 'MSRP_CHANGED',
  INVOICE_PRICE_CHANGED: 'INVOICE_PRICE_CHANGED',
};

export const CRM_LEAD_SYNC_UPDATES = {
  UPDATE_LENDER: 'UPDATE_LENDER',
  UPDATE_DOWNPAYMENT: 'UPDATE_DOWNPAYMENT',
  UPDATE_BUYER_ZIPCODE: 'UPDATE_BUYER_ZIPCODE',
  UPDATE_FEE: 'UPDATE_FEE',
  UPDATE_FEE_KEEP_MANUALLY_ADDED: 'UPDATE_FEE_KEEP_MANUALLY_ADDED',
  UPDATE_ACC: 'UPDATE_ACC',
  UPDATE_COST_ADJUSTMENT: 'UPDATE_COST_ADJUSTMENT',
  UPDATE_REBATE_KEEP_CUSTOME_REBATES: 'UPDATE_REBATE_KEEP_CUSTOME_REBATES',
  UPDATE_REBATES: 'UPDATE_REBATES',
  UPDATE_REBATES_PRICE: 'UPDATE_REBATES_PRICE',
};

export const TRADE_IN_KEYS_TO_CHECK_FOR_REVIEW = [
  TRADE_IN_KEYS.TRADE_ALLOWANCE,
  TRADE_IN_KEYS.TRADE_PAY_OFF,
  TRADE_IN_KEYS.VIN,
];

export const CUSTOMER_APPROVAL_ACTIONS = {
  CANCEL: 'CANCEL',
  CANCELCREATE: 'CANCELCREATE',
};

export const TRADE_IN_SOURCE = {
  CONSUMER: 'CONSUMER',
};

export const REBATE_SOURCE = {
  CONSUMER: 'CONSUMER',
};

export const CUSTOMER_APPROVAL_ITEMS_SOURCE = {
  [CUSTOMER_APPROVAL_ITEMS.TRADEIN_APPROVAL]: TRADE_IN_SOURCE.CONSUMER,
  [CUSTOMER_APPROVAL_ITEMS.REBATES_APPROVAL]: REBATE_SOURCE.CONSUMER,
};

export const CUSTOMER_APPROVAL_DESKING_ACTIONS = {
  REBATE_UPDATE: 'REBATE_UPDATE',
  SELECTED_COLUMN_CHANGE: 'SELECTED_COLUMN_CHANGE',
};

export const DYNAMIC_PRICING_MODALS = [MODEL_TYPE.ZDX, MODEL_TYPE.ADX];

export const DMS_WITH_SUCCESS_BANNER_SUPPORT = [DMS_PROVIDERS.CDK];

export const ACCOUNTING_GROUP_KEYS = {
  ACCOUNTING_GROUP: 'accountingGroup',
  ACCOUNTING_GROUP_ID: 'accountingGroupId',
};

export const ACCOUNTING_GROUP_FEES_ACCESSORY_KEYS = {
  FEES: 'FEES',
  ACCESSORIES: 'ACCESSORIES',
  REBATES: 'REBATES',
};

export const ACCOUNTING_GROUP_FEES_ACCESSORY_VALUE_VS_KEY = {
  FEES: 'accountingGroup',
  ACCESSORIES: 'accountingGroupId',
  REBATES: 'accountingGroupId',
};

export const FILE_KEYS = {
  MEDIA_ID: 'mediaId',
  IS_UPLOADING: 'isUploading',
  IS_UPLOAD_FAILED: 'isUploadFailed',
  FILE_ORDER: 'fileOrder',
  FILE_NAME: 'fileName',

  // Tradein Damage section keys
  DAMAGE_TYPE: 'damageType',
  DAMAGE_DESCRIPTION: 'damageDescription',

  // Tradein Image section keys
  TRADE_IN_MEDIA_ITEMS: 'tradeInMediaItems',
  TRADE_IN_MEDIA_CATEGORY: 'tradeInMediaCategory',
  TRADE_IN_MEDIA_TYPE: 'tradeInMediaType',
  ADDITIONAL_DATA: 'additionalData',
  UPLOAD_SOURCE: 'uploadSource',
  UPLOAD_TIME: 'uploadTime',
};

export const TRADE_IN_MEDIA_EXTRA_SUPPORTED_IMAGE_TYPES = [
  '.gif',
  '.bmp',
  '.webp',
  'image/gif',
  'image/bmp',
  'image/webp',
];

export const TRADE_IN_MEDIA_MAX_FILES_ALLOWED = 10;

export const TRADE_IN_MEDIA_MAX_FILE_SIZE_ALLOWED = 15;

export const FETCH_TRADE_IN_MEDIA_SOURCE = 'DX';

export const TRADE_IN_MEDIA_REQUEST_SOURCE = 'DX';

export const TRADE_IN_MEDIA_REQUEST_OPERATION = 'REPLACE';

export const VALUATION_HEADERS = {
  'tek-tekionmodule': 'DEALS',
};

export const FEES_ACCESSORY_SOURCE_VS_DISPLAY_LABELS = {
  SALES_SETUP: __('Added from Setups'),
  PRE_CONFIGURED: __('Added from Vehicle Inventory'),
};
