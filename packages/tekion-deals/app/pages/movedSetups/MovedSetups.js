import { useNavigate } from 'react-router-dom';
import { useEffect } from 'react';
import ROUTES from 'constants/routes';

function MovedSetups({ path }) {
  const navigate = useNavigate();

  useEffect(() => {
    if (path === ROUTES.COMMISSION_SETUP) {
      navigate(`/sales/setup${ROUTES.COMMISSION_SETUP}`, { replace: true });
    } else if (path === `${ROUTES.COMMISSION_SETUP_COMMISSION_PLAN}/:id/*`) {
      navigate(`/sales/setup${ROUTES.COMMISSION_SETUP_COMMISSION_PLAN}/:id/*}`, { replace: true });
    } else if (path === `${ROUTES.COMMISSION_SETUP_SPIFF}/:id/*`) {
      navigate(`/sales/setup${ROUTES.COMMISSION_SETUP_SPIFF}/:id/*}`, { replace: true });
    } else if (path === ROUTES.FIELD_RULES_SETUP) {
      navigate('/sales/setup/fieldRuleSetup', { replace: true });
    } else if (path === ROUTES.DUE_BILLS_SETUP) {
      navigate(`/sales/setup${ROUTES.DUE_BILLS_SETUP}`, { replace: true });
    } else if (path === ROUTES.COST_ADJUSTMENT_SETUP) {
      navigate(`/sales/setup${ROUTES.COST_ADJUSTMENT_SETUP}`, { replace: true });
    }
  }, [navigate, path]);

  return null;
}

export default MovedSetups;
