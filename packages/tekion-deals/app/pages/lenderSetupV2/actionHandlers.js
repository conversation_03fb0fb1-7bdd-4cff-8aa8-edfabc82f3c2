import _get from 'lodash/get';
import _find from 'lodash/find';
import _size from 'lodash/size';
import _first from 'lodash/first';
import _isEmpty from 'lodash/isEmpty';
import _has from 'lodash/has';
import _omit from 'lodash/omit';

import ACTION_TYPES from '@tekion/tekion-components/src/organisms/FormBuilder/constants/actionTypes';
import MULTI_LINGUAL_FORM_ACTION_HANDLERS from '@tekion/tekion-components/src/hoc/withMultiLingualForm/MultiLingualForm.actionHandlers';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import { MULTI_LINGUAL_FORM_ACTION_TYPES } from '@tekion/tekion-components/src/hoc/withMultiLingualForm/MultiLingualForm.constants';
import { actionHandlers } from '@tekion/tekion-base/marketScan/constants/constants';
import { LENDER_SETUP_ACTIONS_TYPES } from '@tekion/tekion-base/marketScan/constants/desking.constants';
import { getAddressLine } from 'organisms/customerForm/customerFormReader';
import getAddressValue from 'helpers/address/getAddressValue';
import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { ERRORS } from 'constants/pages';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';

import { isInchcape, isInchcapeOrRRG, isRRG } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
import LenderAPI from './lenderSetup.api';

import {
  DEFAULT_LENDER_IDS,
  IS_MARKETSCAN_CODE,
  LENDER_CODES_FOR_EVAULT_REQUIRED_DISABLED_AND_TRUE,
  LENDER_FORM_KEYS,
} from './lenderSetup.constants';
import {
  formatLenderDataToPayload,
  formatLenderDataToForm,
  formatTiers,
  getCodesFromSelectedLender,
  makeDefaultLenderFalse,
  formatAlternateProgramTypes,
  makeDefaultMotabilityLenderFalse,
} from './utils';
import {
  getCMSLenderFromCMSExternalLenderCode,
  getCMSLenderMappings,
  getMarketScanCodeFromTekionCMSLenderCodes,
} from './cmslender.utils';
import { getFirstItem, getDefaultLender, isMotabilityLender } from './lenderSetup.reader';
import {
  formatAddressValues,
  getLenderCodesPayload,
  getMatchedLender,
  getTekionDirectAggregatorLenders,
  getMatchedTekionDirectAggregatorLenders,
  shouldUpdateCustomerNumberErrors,
} from './lenderSetup.helpers';

const onTekionLenderCodeSelection = payload => {
  const { id, value, getState, setState } = payload;
  const { LenderActions, origianlTekionLenderCodes, msLenders } = getState();
  LenderActions.onLenderDataChange({ id, value });
  const cmsTekionLenderCodes = DealerPropertyHelper.isGalaxyEnabled()
    ? [_get(getCMSLenderFromCMSExternalLenderCode(origianlTekionLenderCodes, value), 'external_lender_code')]
    : getCMSLenderMappings(origianlTekionLenderCodes, value);
  const msCodeObj = getMarketScanCodeFromTekionCMSLenderCodes(msLenders, cmsTekionLenderCodes);
  const key = DealerPropertyHelper.isGalaxyEnabled() ? 'Code[0].Code' : 'Name';
  const msCodeName = _get(msCodeObj, key) || value;
  if (msCodeName) {
    onCodeNameSelection({
      additional: null,
      getState,
      id: LENDER_FORM_KEYS.CODE_NAME,
      setState,
      value: msCodeName,
    });
  }
  /*
  else {
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.CODE_NAME, value: '' });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LENDER_CODE, value: '' });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.MARKET_SCAN_CODES, value: [] });
    LenderActions.onLenderDataChange({
      id: IS_MARKETSCAN_CODE,
      value: false,
    });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.TIRES, value: [] });
    LenderActions.onLenderDataChange({
      id: LENDER_FORM_KEYS.ACQUISITION_FEE_MARKUP,
      value: '',
    });
    LenderActions.onLenderDataChange({
      id: LENDER_FORM_KEYS.EVAULTING_REQUIRED,
      value: false,
    });
  }
  */
};

const setCMSTiers = (LenderActions, tiers) => {
  if (DealerPropertyHelper.isGalaxyEnabled()) {
    LenderActions.onLenderDataChange({ id: 'cmsTiers', value: tiers });
  }
};

const setAlternateProgramTypes = (LenderActions, alternateProgramTypes) => {
  if (DealerPropertyHelper.isGalaxyEnabled()) {
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.ALTERNATE_PROGRAM_NAMES, value: alternateProgramTypes });
  }
};

const onCodeNameSelection = payload => {
  const { id, value, getState, setState } = payload;
  const { LenderActions, msLenders, lenderMarkups, errors } = getState();
  const matchedLender = getMatchedLender({ lenders: msLenders, value: _get(payload, 'value') });
  if (matchedLender) {
    // selected lender is from MS
    const { marketScanCodes, code } = getCodesFromSelectedLender(matchedLender);
    const { Tier, Alternateprogramtypes } = matchedLender;
    const formattedTiers = formatTiers(Tier);
    const formattedAlternateProgramTypes = formatAlternateProgramTypes(Alternateprogramtypes);

    LenderActions.onLenderDataChange({ ...payload, code, marketScanCodes });
    LenderActions.onLenderDataChange({ id: IS_MARKETSCAN_CODE, value: true });
    LenderActions.onLenderDataChange({ id: 'tiers', value: formattedTiers });
    setCMSTiers(LenderActions, formattedTiers);
    setAlternateProgramTypes(LenderActions, formattedAlternateProgramTypes);
    LenderActions.onLenderDataChange({
      id: LENDER_FORM_KEYS.ACQUISITION_FEE_MARKUP,
      value: _get(lenderMarkups, [code, 'MaxMarkupValue']) || '',
    });
    if (LENDER_CODES_FOR_EVAULT_REQUIRED_DISABLED_AND_TRUE.includes(value)) {
      LenderActions.onLenderDataChange({
        id: LENDER_FORM_KEYS.EVAULTING_REQUIRED,
        value: true,
      });
    }
    handleDealerIdfield(payload, matchedLender);
  } else {
    // manually added lender
    LenderActions.onLenderDataChange({
      id: IS_MARKETSCAN_CODE,
      value: false,
    });
    LenderActions.onLenderDataChange({ id, value });
    LenderActions.onLenderDataChange({ id: 'code', value });
    LenderActions.onLenderDataChange({ id: 'tiers', value: [] });
    setCMSTiers(LenderActions, []);
    setAlternateProgramTypes(LenderActions, []);
    LenderActions.setShowDealerIdField(false);
    setState({ errors: { ...errors, [LENDER_FORM_KEYS.LENDER_DEALER_ID]: undefined } });
  }
};

const handleDealerIdfield = (payload, matchedLender) => {
  const { getState, setState } = payload;
  const { LenderActions, tekionLenderCodes, errors } = getState();
  const tekionAggregatorLenders = getTekionDirectAggregatorLenders(tekionLenderCodes);
  const matchedTekionAggregatorLender = getMatchedTekionDirectAggregatorLenders(tekionAggregatorLenders, matchedLender);
  const isDealerIdField = !_isEmpty(matchedTekionAggregatorLender);
  LenderActions.setShowDealerIdField(isDealerIdField);
  setState({
    errors: { ...errors, [LENDER_FORM_KEYS.LENDER_DEALER_ID]: isDealerIdField ? ERRORS.REQUIRED : undefined },
  });
};

const onLienHolderSelection = payload => {
  const { value, getState, setState } = payload;
  const { LenderActions, errors } = getState();
  LenderActions.onLenderDataChange(payload);
  if (!value) {
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIENHOLDER_NAME, value: '' });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.VENDOR_NUMBER, value: '' });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIENHOLDER_ADDRESS, value: '' });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIENHOLDER_ADDRESS2, value: '' });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIENHOLDER_ADDRESS3, value: '' });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIENHOLDER_ADDRESS4, value: '' });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIENHOLDER_STATE, value: '' });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIENHOLDER_CITY, value: '' });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIENHOLDER_ZIP_CODE, value: '' });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LINEHOLDER_COUNTY, value: '' });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LINEHOLDER_COUNTRY, value: '' });
    LenderActions.onLenderDataChange({
      id: LENDER_FORM_KEYS.LIENHOLDER_PHONE_NUMBER,
      value: '',
    });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LINEHOLDER_COUNTRY, value: '' });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIEN_PAY_OFF_COUNTY, value: '' });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIENHOLDER_ADDRESS2, value: '' });
  }
  setState({
    errors: { ...errors, [LENDER_FORM_KEYS.LIENHOLDER_STATE]: undefined },
  });
};

const onRemoveDemoMileage = payload => {
  const { value, getState } = payload;
  const { LenderActions } = getState();
  LenderActions.onLenderDataChange(payload);
  if (!value) {
    LenderActions.onLenderDataChange({
      id: LENDER_FORM_KEYS.REMOVE_DEMO_MILEAGE_DECIDER_VALUE,
      value: '',
    });
    LenderActions.onLenderDataChange({
      id: LENDER_FORM_KEYS.VEHICLE_MILEAGE_CUT_OFF_REMOVE_DEMO_MILEAGE,
      value: '',
    });
  }
};

const onLienPayOffAddressSelection = payload => {
  const { value, getState } = payload;
  const { LenderActions } = getState();
  LenderActions.onLenderDataChange(payload);

  if (!value) {
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIEN_PAY_OFF_ADDRESS, value: '' });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIEN_PAY_OFF_ADDRESS2, value: '' });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIEN_PAY_OFF_ADDRESS3, value: '' });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIEN_PAY_OFF_ADDRESS4, value: '' });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIEN_PAY_OFF_STATE, value: '' });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIEN_PAY_OFF_CITY, value: '' });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIEN_PAY_OFF_ZIP, value: '' });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIEN_PAY_OFF_COUNTY, value: '' });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIEN_PAY_OFF_COUNTRY, value: '' });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIEN_PAY_OFF_PH_NUM, value: '' });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIEN_PAY_OFF_COUNTRY, value: '' });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIEN_PAY_OFF_COUNTY, value: '' });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIEN_PAY_OFF_ADDRESS2, value: '' });
  }
};

const saveData = payload => {
  const { getState } = payload;
  const { LenderActions } = getState();
  LenderActions.onLenderDataChange(payload);
};

const formChangeHandler = {
  ...MULTI_LINGUAL_FORM_ACTION_HANDLERS,
  [LENDER_FORM_KEYS.CODE_NAME]: onCodeNameSelection,
  [LENDER_FORM_KEYS.LIENHOLDER]: onLienHolderSelection,
  [LENDER_FORM_KEYS.REMOVE_DEMO_MILEAGE]: onRemoveDemoMileage,
  [LENDER_FORM_KEYS.LIEN_PAY_OFF_ADDRESS_SAME_AS_LIEN_HOLDER_ADDRESS]: onLienPayOffAddressSelection,
  [LENDER_FORM_KEYS.TEKION_LENDER_CODE]: onTekionLenderCodeSelection,
  [LENDER_FORM_KEYS.LENDER_ADDRESS_DETAILS]: onLenderAddressDetailsChange,
  [LENDER_FORM_KEYS.LIENHOLDER_ADDRESS_DETAILS]: onLienHolderAddressDetailsChange,
  default: saveData,
};

const updateAddress = payload => {
  const { id, value, getState } = payload;
  const { LenderActions } = getState();
  const { searchedString, location } = value;
  const { line1: adddressLine1, line2, line3, line4, city, state, zipCode, county, country } = location || {};

  const line1 = adddressLine1 || getAddressLine(searchedString, location);
  if (id === LENDER_FORM_KEYS.LIENHOLDER_ADDRESS) {
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIENHOLDER_ADDRESS, value: line1 });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIENHOLDER_ADDRESS2, value: line2 });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIENHOLDER_ADDRESS3, value: line3 });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIENHOLDER_ADDRESS4, value: line4 });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIENHOLDER_STATE, value: state });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIENHOLDER_CITY, value: city });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LINEHOLDER_COUNTY, value: county });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LINEHOLDER_COUNTRY, value: country });
    LenderActions.onLenderDataChange({
      id: LENDER_FORM_KEYS.LIENHOLDER_ZIP_CODE,
      value: zipCode,
    });
  }

  if (id === LENDER_FORM_KEYS.LIEN_PAY_OFF_ADDRESS) {
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIEN_PAY_OFF_ADDRESS, value: line1 });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIEN_PAY_OFF_ADDRESS2, value: line2 });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIEN_PAY_OFF_ADDRESS3, value: line3 });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIEN_PAY_OFF_ADDRESS4, value: line4 });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIEN_PAY_OFF_STATE, value: state });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIEN_PAY_OFF_CITY, value: city });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIEN_PAY_OFF_COUNTY, value: county });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.LIEN_PAY_OFF_COUNTRY, value: country });
    LenderActions.onLenderDataChange({
      id: LENDER_FORM_KEYS.LIEN_PAY_OFF_ZIP,
      value: zipCode,
    });
  }

  if (id === LENDER_FORM_KEYS.ADDRESS) {
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.ADDRESS, value: line1 });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.ADDRESS2, value: line2 });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.ADDRESS3, value: line3 });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.ADDRESS4, value: line4 });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.STATE, value: state });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.CITY, value: city });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.ZIP_CODE, value: zipCode });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.COUNTY, value: county });
    LenderActions.onLenderDataChange({ id: LENDER_FORM_KEYS.COUNTRY, value: country });
  }
};

function onLenderAddressDetailsChange(payload) {
  const { value, getState } = payload;
  const location = getAddressValue(value);
  return updateAddress({ id: LENDER_FORM_KEYS.ADDRESS, getState, value: { location } });
}

function onLienHolderAddressDetailsChange(payload) {
  const { value, getState } = payload;
  const location = getAddressValue(value);
  return updateAddress({ id: LENDER_FORM_KEYS.LIENHOLDER_ADDRESS, getState, value: { location } });
}

const init = async (_, { setState, getState }) => {
  const { LenderActions } = getState();
  setState({ showStatusLoader: true });
  if (!DealerPropertyHelper.isCalcEngineEnabled() || DealerPropertyHelper.isGalaxyEnabled()) {
    const payload = getLenderCodesPayload();
    await LenderActions.getLenderCodes(payload);
  }
  if (!isInchcape()) {
    await LenderActions.getTekionLenderID();
  }
  await LenderActions.getLendererList();
  LenderActions.getFeeMarkups();
  setState({ showStatusLoader: false });
};

const onChange = ({ action }, { setState, getState }) => {
  const { type, payload } = action;
  switch (type) {
    case ACTION_TYPES.ON_FIELD_CHANGE: {
      const { id } = payload;
      const handler = formChangeHandler[id] || saveData;
      handler({ ...payload, value: _get(payload, 'value'), setState, getState });
      if (id === LENDER_FORM_KEYS.LIENHOLDER && isInchcapeOrRRG()) {
        const { selectedLenderData, errors } = getState();
        const { vendorNumber, customerNumber } = selectedLenderData;
        const sameAsLender = _get(payload, 'value');
        if (shouldUpdateCustomerNumberErrors(selectedLenderData, sameAsLender)) {
          checkForCustomerNumber(customerNumber, errors, setState, getState);
        }
        if (_isEmpty(vendorNumber) && !sameAsLender) {
          setState({
            errors: {
              ..._omit(errors, [LENDER_FORM_KEYS.CUSTOMER_NUMBER]),
              [LENDER_FORM_KEYS.VENDOR_NUMBER]: 'This field is mandatory',
            },
          });
          return;
        }
        if (_has(errors, LENDER_FORM_KEYS.VENDOR_NUMBER) && isRRG()) {
          setState({
            errors: _omit(errors, [LENDER_FORM_KEYS.VENDOR_NUMBER]),
          });
        }
      }
      if (id === LENDER_FORM_KEYS.VENDOR_NUMBER && isInchcapeOrRRG()) {
        const { errors } = getState();
        const vendorNumber = _get(payload, 'value');
        if (!vendorNumber) {
          setState({
            errors: { ...errors, [LENDER_FORM_KEYS.VENDOR_NUMBER]: 'This field is mandatory' },
          });
          return;
        }
        if (_has(errors, LENDER_FORM_KEYS.VENDOR_NUMBER)) {
          setState({
            errors: _omit(errors, [[LENDER_FORM_KEYS.VENDOR_NUMBER], [LENDER_FORM_KEYS.CUSTOM_ERROR]]),
          });
        }
      }
      if (id === LENDER_FORM_KEYS.CUSTOMER_NUMBER && isInchcape()) {
        const { selectedLenderData, errors } = getState();
        const { sameAsLender } = selectedLenderData;
        if (shouldUpdateCustomerNumberErrors(selectedLenderData, sameAsLender)) {
          checkForCustomerNumber(_get(payload, 'value'), errors, setState, getState);
        }
        const additional = _get(payload, 'additional');
        if (_size(additional)) {
          updateAddress({
            id: LENDER_FORM_KEYS.ADDRESS,
            value: { location: formatAddressValues(_first(additional)) },
            getState,
          });
        }
        if (_isEmpty(_get(payload, 'value')) || !_size(additional)) {
          updateAddress({
            id: LENDER_FORM_KEYS.ADDRESS,
            value: {
              location: formatAddressValues({
                line1: '',
                line2: '',
                line3: '',
                line4: '',
                state: '',
                city: '',
                postalCode: '',
                county: '',
                country: '',
              }),
            },
            getState,
          });
        }
        break;
      }
      break;
    }

    case actionHandlers.ADDRESS_RESOLVED: {
      updateAddress({ ...payload, getState });
      break;
    }

    case ACTION_TYPES.VALIDATION_SUCCESS: {
      const { errors } = payload;
      setState({ errors });
      break;
    }
    case MULTI_LINGUAL_FORM_ACTION_TYPES.ON_MULTI_LINGUAL_FIELD_CHANGE: {
      const { id, language } = payload;
      const handler = formChangeHandler[id] || saveData;
      handler({
        ...payload,
        id: `languages.locale.${language}.${id}`,
        value: _get(payload, 'value'),
        getState,
        setState,
      });
      break;
    }
    default: {
      if (formChangeHandler[type]) {
        formChangeHandler[type](action, {
          setState,
          getState,
        });
      }
      break;
    }
  }
};

const checkForCustomerNumber = (customerNumber, errors, setState, getState) => {
  if (_isEmpty(customerNumber)) {
    setState(prevState => {
      const newErrors = {
        ..._omit(prevState.errors, [LENDER_FORM_KEYS.VENDOR_NUMBER]),
        [LENDER_FORM_KEYS.CUSTOMER_NUMBER]: __('Select the Customer Number/Name for the Lender.'),
      };
      return {
        ...prevState,
        errors: newErrors,
      };
    });
    return;
  }

  setState(
    prevState => {
      const newErrors = _omit(prevState.errors, [LENDER_FORM_KEYS.CUSTOMER_NUMBER]);
      return {
        ...prevState,
        errors: newErrors,
      };
    },
    () => {
      checkForReciprocalAccount(customerNumber, getState, setState);
    }
  );
};

const checkForReciprocalAccount = async (customerNumber, getState, setState) => {
  const { errors } = getState();
  const payload = {
    customerIds: [customerNumber],
  };
  const { response } = await LenderAPI.fetchReciprocalDetails(payload);
  if (response?.length) {
    if (_has(errors, LENDER_FORM_KEYS.CUSTOM_ERROR)) {
      setState({
        errors: _omit(errors, [LENDER_FORM_KEYS.CUSTOM_ERROR]),
      });
    }
  } else {
    toaster(TOASTER_TYPE.ERROR, __('Please set up the reciprocal account in CMS.'));
    setState({
      errors: { ...errors, [LENDER_FORM_KEYS.CUSTOM_ERROR]: LENDER_FORM_KEYS.CUSTOM_ERROR },
    });
  }
};

const onHide = async ({ updatedLenderData }, { setState, getState }) => {
  const { LenderActions } = getState();
  if (!updatedLenderData?.id) return;
  setState({ isLenderUpdating: true });
  const payload = formatLenderDataToPayload(formatLenderDataToForm(updatedLenderData));
  await LenderActions.updateLenderInLenders(updatedLenderData?.id, payload);
  setState({ isLenderUpdating: false });
};

const onDelete = async ({ id }, { setState, getState }) => {
  const { LenderActions, lenderList } = getState();
  await LenderActions.deleteSelectedLender(id);
  const firstItem = getFirstItem(lenderList);
  LenderActions.resetSelectedLenderData(firstItem);
  setState({
    selectedLenderId: firstItem.id,
  });
};

const loadLenderData = async ({ lenderData }, { setState, getState }) => {
  const { LenderActions, tekionLenderCodes } = getState();
  const tekionAggregatorLenders = getTekionDirectAggregatorLenders(tekionLenderCodes);
  const matchedTekionAggregatorLender = _find(tekionAggregatorLenders, ({ key }) => key === _get(lenderData, 'code'));
  const isDealerIdField = !_isEmpty(matchedTekionAggregatorLender);
  LenderActions.setShowDealerIdField(isDealerIdField);
  setState({
    selectedLenderId: lenderData.id,
    errors: {
      ...(!lenderData[LENDER_FORM_KEYS.LENDER_DEALER_ID] && !_isEmpty(matchedTekionAggregatorLender)
        ? { [LENDER_FORM_KEYS.LENDER_DEALER_ID]: ERRORS.REQUIRED }
        : EMPTY_OBJECT),
    },
  });
  LenderActions.resetSelectedLenderData(lenderData);
};

const addNewLender = async (_, { setState, getState }) => {
  const { LenderActions } = getState();
  LenderActions.resetSelectedLenderData(formatLenderDataToForm({}));
  LenderActions.setShowDealerIdField(false);
  setState({
    selectedLenderId: null,
    errors: {
      [LENDER_FORM_KEYS.DISPLAY_NAME]: ERRORS.REQUIRED,
      [LENDER_FORM_KEYS.LEGAL_NAME]: ERRORS.REQUIRED,
      ...(isInchcape() && {
        [LENDER_FORM_KEYS.CUSTOMER_NUMBER]: __('Select the Customer Number/Name for the Lender.'),
      }),
    },
  });
};

const onCancel = async (_, { setState, getState }) => {
  const { LenderActions, selectedLenderId } = getState();
  setState({ fethingLenderInfo: true });
  await LenderActions.getLendererData(selectedLenderId);
  setState({ fethingLenderInfo: false });
};

const updateBulkLenders = async (lenders, filterFn, updateFn) => {
  if (!lenders?.length) return;
  await Promise.all(lenders.filter(filterFn).map(lender => LenderAPI.updateLender(lender.id, updateFn(lender))));
};

const onSave = async (_, { setState, getState }) => {
  const { selectedLenderId, LenderActions, selectedLenderData, lenderList } = getState();

  setState({ isLenderUpdating: true });

  /** update other lenders to not default for selected lender case */
  const isSelectedLenderDefaultLender = getDefaultLender(selectedLenderData);
  if (isSelectedLenderDefaultLender) {
    await updateBulkLenders(lenderList, getDefaultLender, makeDefaultLenderFalse);
  } else if (selectedLenderId === DEFAULT_LENDER_IDS.MOTABILITY) {
    await updateBulkLenders(lenderList, isMotabilityLender, makeDefaultMotabilityLenderFalse);
    selectedLenderData.defaultMotableLender = true;
  }

  const hidden = _get(
    _find(lenderList, ({ id }) => id === selectedLenderId),
    'hidden'
  );

  const createdBy =
    _get(
      _find(lenderList, item => item?.id === selectedLenderId),
      'createdBy'
    ) || null;

  const payload = formatLenderDataToPayload({
    ...selectedLenderData,
    hidden,
    createdBy,
  });

  if (selectedLenderId) {
    await LenderActions.updateLender(selectedLenderId, payload);
  } else {
    const { response } = await LenderActions.createLenderer(payload);
    if (response) {
      setState({
        selectedLenderId: response.id,
      });
    }
  }

  LenderActions.getLendererList();
  setState({
    isLenderUpdating: false,
  });
};

export const LENDER_SETUP_ACTION_HANDLERS = {
  [LENDER_SETUP_ACTIONS_TYPES.INIT]: init,
  [LENDER_SETUP_ACTIONS_TYPES.ON_CHANGE]: onChange,
  [LENDER_SETUP_ACTIONS_TYPES.ON_HIDE]: onHide,
  [LENDER_SETUP_ACTIONS_TYPES.ON_DELETE]: onDelete,
  [LENDER_SETUP_ACTIONS_TYPES.LOAD_LENDER_DATA]: loadLenderData,
  [LENDER_SETUP_ACTIONS_TYPES.ADD_NEW_LENDER]: addNewLender,
  [LENDER_SETUP_ACTIONS_TYPES.ON_CANCEL]: onCancel,
  [LENDER_SETUP_ACTIONS_TYPES.ON_SAVE]: onSave,
};
