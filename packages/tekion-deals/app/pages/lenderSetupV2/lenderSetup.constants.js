import _map from 'lodash/map';
import { LENDER_CODES, LENDER_CODE_LABELS } from '@tekion/tekion-base/constants/deal/credit';

export const DELIMITOR = '$';

export const IS_MARKETSCAN_CODE = 'marketScan';

export const FINANCE_RESERVE_CAP_COST_BASED_FORMULAS = {
  FORMULA_1: 'FORMULA_1', // % of Amount Financed * Amount Financed
  FORMULA_2: 'FORMULA_2', // % of Amount Financed * Amount Financed * Reserve Split %
};

export const FINANCE_RESERVE_MARKUP_BASED_FORMULAS = {
  FORMULA_1: 'FORMULA_1', // (Finance Charge based on Sell Rate - Finance Charge based on Buy Rate) * Reserve Split %
  FORMULA_2: 'FORMULA_2', // (Finance Charge based on Sell Rate - Finance Charge based on Buy Rate) * Reserve Split %
};

export const FINANCE_RESERVE_CAP_COST_BASED_FORMULAS_VS_KEYS = {
  [FINANCE_RESERVE_CAP_COST_BASED_FORMULAS.FORMULA_1]: false,
  [FINANCE_RESERVE_CAP_COST_BASED_FORMULAS.FORMULA_2]: true,
};

export const FINANCE_RESERVE_MARKUP_BASED_FORMULAS_VS_KEYS = {
  [FINANCE_RESERVE_MARKUP_BASED_FORMULAS.FORMULA_1]: false,
  [FINANCE_RESERVE_MARKUP_BASED_FORMULAS.FORMULA_2]: true,
};

export const LENDER_CODES_FOR_EVAULT_REQUIRED_DISABLED_AND_TRUE = [
  'Volkswagen Credit',
  'Audi Financial Services',
  'Mercedes Benz Financial Services',
];

export const LENDER_CODE_FOR_DEALER_TRACK_OPTIONS = _map(LENDER_CODES, code => ({
  value: code,
  label: __('{{label}}', { label: LENDER_CODE_LABELS[code] }),
}));

export const TRADE_IN_TAX_CREDIT_TO_LEASE_OPTIONS = [
  {
    label: __('Apply credit that includes payment with partial MUT'),
    value: 'APPLY_CREDIT_THAT_INCLUDES_PAYMENT_WITH_PARTIAL_MUT',
  },
  {
    label: __('Apply credit to full MUT rounded up a payment'),
    value: 'APPLY_CREDIT_TO_FULL_MUT_ROUNDED_UP_A_PAYMENT',
  },
  {
    label: __('Apply credit to full MUT rounded down a payment'),
    value: 'APPLY_CREDIT_TO_FULL_MUT_ROUNDED_DOWN_A_PAYMENT',
  },
];

export const LENDER_FORM_KEYS = {
  OPTION_CONTRACT_VALIDITY_IN_CALENDAR_DAYS: 'optionContractValidityInCalendarDays',

  LENDER_CODE: 'code',
  MARKET_SCAN_CODES: 'marketScanCodes',
  TIRES: 'tiers',
  ALTERNATE_PROGRAM_NAMES: 'alternateProgramNames',
  CODE_NAME: 'codeName',
  DEFAULT_LENDER: 'defaultLender',
  DISPLAY_NAME: 'displayName',
  REMOVE_DEMO_MILEAGE: 'removeDemoMileageFromAnnualMileage',
  REMOVE_DEMO_MILEAGE_DECIDER_VALUE: 'removeDemoMileageFromAnnualMileageDeciderValue',
  VEHICLE_MILEAGE_CUT_OFF_REMOVE_DEMO_MILEAGE: 'vehicleMileCutOffForRemoveDemoMileage',
  FIRST_PAYMENT_WAIVED_DEFAULT_VALUE: 'firstPaymentWaivedDefaultValue',
  LIENHOLDER_STATE: 'lienHolderState',
  LIENHOLDER: 'sameAsLender',
  ACQUISITION_FEE_MARKUP: 'acquisitionFeeMarkupAmt',
  BANK_FEE_UPFRONT: 'bankFeeUpfront',
  ACQUISITION_FEE: 'acquisitionFee',
  ACTUAL_PENALTY_PER_MILE: 'actualPenaltyPerMile',
  PENALTY_PER_MILE: 'penaltyPerMile',
  LEGAL_NAME: 'legalName',
  TEKION_LENDER_CODE: 'tekionLenderCode',
  DEALER_TYPE: 'dealerType',
  CUSTOMER_NUMBER: 'customerNumber',
  VENDOR_NUMBER: 'vendorNumber',
  LENDER_DEALER_ID: 'lenderDealerId',

  LIEN_FILING_CODE: 'lienFilingCode',
  LIENHOLDER_NAME: 'lienHolder',
  PREFERRED: 'preferred',
  LOAN_SUPPORTED: 'loanSupported',
  LEASE_SUPPORTED: 'leaseSupported',
  CONTRA_SETTLEMENTS_SUPPORTED_LENDER: 'contraSettlementsSupportedLender',
  CONTRA_SETTLEMENTS_SUPPORTED_FINANCE_COMPANY: 'contraSettlementsSupportedFinanceCompany',
  LIEN_HOLDER_LENDER: 'lienHolderLender',
  DISPOSITION_FEE_DEFAULTS_TO_MONTHLY_PAYMENT: 'useMPEqDispFeeWhenMpLtDispFee',
  THRESHOLD_VALUE_FOR_DISPOSITION_FEE: 'dispositionThresholdValue',
  THRESHOLD_VALUE_FOR_TERMINATION_FEE: 'terminationThresholdValue',
  SECURITY_DEPOSIT_DEFAULT: 'securityDepositWaiverReason',
  ADDRESS: 'address',
  ADDRESS2: 'address2',
  ADDRESS3: 'address3',
  ADDRESS4: 'address4',

  STATE: 'state',
  CITY: 'city',
  COUNTY: 'county',
  COUNTRY: 'country',
  ZIP_CODE: 'zipcode',
  PHONE: 'phoneNumber',
  LOAN_TYPES: 'loanTypes',
  DIGITAL_LOAN_TYPES: 'digitalLoanTypes',
  LOAN_TYPES_VALUES: 'loanTypesValue',
  DIGITAL_LOAN_TYPES_VALUES: 'digitalLoanTypesValue',
  LEASE_TYPES: 'leaseTypes',
  DIGITAL_LEASE_TYPES: 'digitalLeaseTypes',
  LEASE_TYPES_VALUES: 'leaseTypesValue',
  DIGITAL_LEASE_TYPES_VALUES: 'digitalLeaseTypesValue',
  FINANCE_RESERVE_SPLIT_PERCENTAGE: 'financeReserveSplitPercentage',
  // ENABLE_TAX_FINANCE_CHARGE :'enableTxFinanceCharge',
  FINANCE_RESERVE_CAPCOST_BASED_LOAN: 'financeReserveCapCostLoan',
  FINANCE_RESERVE_CAPCOST_BASED_LEASE: 'financeReserveCapCostLease',
  FINANCE_RESERVE_FLAT_AMOUNT_LOAN: 'financeReserveFlatAmountLoan',
  FINANCE_RESERVE_FLAT_AMOUNT_LEASE: 'financeReserveFlatAmountLease',
  FINANCE_RESERVE_MARKUP_BASED_LOAN: 'financeReserveMarkeupBasedLoan',
  FINANCE_RESERVE_MARKUP_BASED_LEASE: 'financeReserveMarkeupBasedLease',
  FINANCE_RESERVE: 'financeReserve',
  LIENHOLDER_ADDRESS: 'lienHolderAddress',
  LIENHOLDER_ADDRESS2: 'lienHolderAddress2',
  LIENHOLDER_ADDRESS3: 'lienHolderAddress3',
  LIENHOLDER_ADDRESS4: 'lienHolderAddress4',

  LIENHOLDER_CITY: 'lienHolderCity',
  LIENHOLDER_ZIP_CODE: 'lienHolderZipCode',
  LIENHOLDER_PHONE_NUMBER: 'lienHolderPhoneNumber',
  LINEHOLDER_COUNTY: 'lienHolderCounty',
  LINEHOLDER_COUNTRY: 'lienHolderCountry',

  OSFLENDER: 'osfLender',
  FULL_TERM_BALLOON: 'fullTermBalloon',
  APRMFTYPE: 'aprMFType',
  RESIDUALTYPE: 'residualType',
  MAX_MARKUP_FOR_LOAN: 'loanMaxMarkup',
  MAX_MARKUP_FOR_LEASE: 'leaseMaxMarkup',
  MONEY_FACTOR_DECIMAL_POINTS: 'moneyFactorDecimalPoints',
  MONEY_FACTOR_OPERATIONS: 'moneyFactorOperations',
  MAX_MARKUP_FOR_ONEPAY: 'onePayMaxMarkup',
  COLLECT_SECURITY_DEPOSIT_WAIVER_REASON: 'collectSecurityDepositWaiverReason',
  REMOVE_DEMO_MILEAGE_FROM_ANNUAL_MILAGE_DISPLAY_NAME: 'removeDemoMileageFromAnnualMileageDisplayName',
  FREE_MILES_FOR_RESIDUAL_ADJUSTMENT: 'freeMilesforResidualAdjustment',
  INCLUDE_SECURITY_DEPOSITS: 'includeSecurityDeposits',
  LIEN_PAY_OFF_ADDRESS: 'lienPayOffAddress',
  LIEN_PAY_OFF_ADDRESS2: 'lienPayOffAddress2',
  LIEN_PAY_OFF_ADDRESS3: 'lienPayOffAddress3',
  LIEN_PAY_OFF_ADDRESS4: 'lienPayOffAddress4',

  LIEN_PAY_OFF_STATE: 'lienPayOffState',
  LIEN_PAY_OFF_CITY: 'lienPayOffCity',
  LIEN_PAY_OFF_ZIP: 'lienPayOffZipCode',
  LIEN_PAY_OFF_PH_NUM: 'lienPayOffPhoneNumber',
  LIEN_PAY_OFF_COUNTY: 'lienPayOffCounty',
  LIEN_PAY_OFF_COUNTRY: 'lienPayOffCountry',
  FEIN_NUMBER: 'feinNumber',
  LIEN_PAY_OFF_ADDRESS_SAME_AS_LIEN_HOLDER_ADDRESS: 'lienPayOffAddressSameAsLienHolderAddress',
  ROUND_OFF_MILEAGE_TO_NEAREST_THOUSAND: 'roundOffMileageToNearestThousand',
  EVAULTING_REQUIRED: 'evaultingRequired',
  PERSONAL_PROPERTY_TAX_LENDER_TYPE: 'personalPropertyTaxLenderType',
  TOTAL_SECURITY_DEPOSITS: 'totalNumberOfSecurityDeposits',
  ROUND_SECURITY_DEPOSIT_TO: 'roundSecurityDepositTo',
  TYPE_OF_SECURITY_DEPOSIT: 'typeOfSecurityDeposit',
  APPLY_TAX_EXEMPT_FOR_USE_TAX: 'taxExemptForUseTax',
  EXCLUDE_FNI_PRODUCTS_IN_USE_TAX_CALCULATION: 'excludeFNIProductsInUseTaxCalculation',
  TRADE_IN_TAX_CREDIT_TO_LEASE: 'tradeInTaxCreditToLease',
  INCLUDE_TAXABLE_FNI_PRODUCTS_IN_MUT: 'includeTaxableFNIProductInMUTWashington',
  REMOVE_NONTAX_FNI_PRODUCT_FROM_USE_TAX: 'exemptMonthlyUseTaxOnFni',
  ADD_VEHICLE_MILES_TO_ANNUAL_MILES: 'addVehicleMilesToAnnualmiles',
  ENABLE_YEARLY_MILES_FOR_BALLOON: 'enableYearlyMilesForBalloon',
  PAYMENT_DUE_DATE_ADJUSTMENT: 'paymentDueDateAdjustment',
  ODD_DAYS_BASED_ON_DAYS_PER_MONTH: 'oddDaysBasedOnDaysPerMonth',
  DAYS_PER_YEAR_FOR_ODD_DAYS_INTEREST: 'daysPerYearForOddDaysInterest',
  SUBVENTION_COST_TYPE: 'subventionCostType',
  SUBVENTION_COST_VALUE: 'subventionCostValue',

  VEHICLE_CATEGORY: 'supportedVehicleCategory',
  ANNUITY_DUE: 'annuityDue',
  ONE_PAY_TWO_DIGITS_AFTER_COMMA_MONTHLY_PAYMENT: 'onePay2DigitsAfterCommaMonthlyPayment',
  ONE_PAY_APR_MONTHLY_LEASE_CHARGE_CALCULATION: 'onePayAPRMonthlyLeaseChargeCalculation',
  LENDER_CODE_FOR_DEALER_TRACK: 'lenderDetailsForDealerTrack',

  LIEN_HOLDER_FORM_SECTION_LABEL: 'lienHolderFormSectionLabel',
  PART_EX_LIEN_HOLDER_FORM_SECTION_LABEL: 'partExLineHolderFormSectionLabel',
  ITEMIZE_CCR_FOR_TAXATION: 'doNotItemizeCCRTax',
  LENDER_SUPPORTS_CREDIT_APPLICATION: 'lenderSupportsCreditApplication',
  USE_DECIMAL_PAYMENT_CALC: 'useDecimalPaymentCalc',
  LENDER_ADDRESS_DETAILS: 'lenderAddressDetails',
  LIENHOLDER_ADDRESS_DETAILS: 'lienholderAddressDetails',
  CUSTOM_ERROR: 'customError',
};

const DEFAULT_LENDER_SETUP_FORM_FIELDS_TO_LABEL_MAP = {
  [LENDER_FORM_KEYS.CODE_NAME]: __('Code'),
  [LENDER_FORM_KEYS.TEKION_LENDER_CODE]: __('Tekion Lender Id'),
  [LENDER_FORM_KEYS.LEGAL_NAME]: __('Legal Name'),
  [LENDER_FORM_KEYS.CUSTOMER_NUMBER]: __('Customer Number / Name'),
  [LENDER_FORM_KEYS.VENDOR_NUMBER]: __('Vendor Number / Name'),
  [LENDER_FORM_KEYS.DISPLAY_NAME]: __('Business Name'),
  [LENDER_FORM_KEYS.LIEN_FILING_CODE]: __('Lien Filing Code'),
  [LENDER_FORM_KEYS.SECURITY_DEPOSIT_DEFAULT]: __('Default Security Deposit'),
  [LENDER_FORM_KEYS.LENDER_CODE_FOR_DEALER_TRACK]: __('Lender Code for DealerTrack'),
  [LENDER_FORM_KEYS.LIENHOLDER]: __('Lienholder'),
  [LENDER_FORM_KEYS.LIENHOLDER_NAME]: __('Lienholder Name'),
  [LENDER_FORM_KEYS.PREFERRED]: __('Preferred Lender'),
  [LENDER_FORM_KEYS.LOAN_SUPPORTED]: __('Supports Loan'),
  [LENDER_FORM_KEYS.LEASE_SUPPORTED]: __('Supports Lease, One Pay'),
  [LENDER_FORM_KEYS.DEFAULT_LENDER]: __('Default Lender'),
  [LENDER_FORM_KEYS.OSFLENDER]: __('OSF'),
  [LENDER_FORM_KEYS.FULL_TERM_BALLOON]: __('Full Term Balloon '),
  [LENDER_FORM_KEYS.APRMFTYPE]: __('Lease APR/MF'),
  [LENDER_FORM_KEYS.RESIDUALTYPE]: __('Residual $/%'),
  [LENDER_FORM_KEYS.MONEY_FACTOR_DECIMAL_POINTS]: __('Money Factor Decimal Points'),
  [LENDER_FORM_KEYS.MONEY_FACTOR_OPERATIONS]: __('Money Factor Operation'),
  [LENDER_FORM_KEYS.MAX_MARKUP_FOR_LEASE]: __('Maximum Markup for Lease'),
  [LENDER_FORM_KEYS.MAX_MARKUP_FOR_LOAN]: __('Maximum Markup for Loan'),
  [LENDER_FORM_KEYS.MAX_MARKUP_FOR_ONEPAY]: __('Maximum Markup for One Pay'),
  [LENDER_FORM_KEYS.LOAN_TYPES]: __('Loan Markup'),
  [LENDER_FORM_KEYS.DIGITAL_LOAN_TYPES]: __('Digital Loan Markup'),
  [LENDER_FORM_KEYS.LOAN_TYPES_VALUES]: __(''),
  [LENDER_FORM_KEYS.LEASE_TYPES_VALUES]: __(''),
  [LENDER_FORM_KEYS.DIGITAL_LOAN_TYPES_VALUES]: __(''),
  [LENDER_FORM_KEYS.DIGITAL_LEASE_TYPES_VALUES]: __(''),
  [LENDER_FORM_KEYS.LEASE_TYPES]: __('Lease Markup'),
  [LENDER_FORM_KEYS.SUBVENTION_COST_TYPE]: __('Subvention Cost'),
  [LENDER_FORM_KEYS.DIGITAL_LEASE_TYPES]: __('Digital Lease Markup'),
  [LENDER_FORM_KEYS.FINANCE_RESERVE_CAPCOST_BASED_LOAN]: __('Loan Finance Reserve Formula'),
  [LENDER_FORM_KEYS.FINANCE_RESERVE_CAPCOST_BASED_LEASE]: __('Lease Finance Reserve Formula'),
  [LENDER_FORM_KEYS.FINANCE_RESERVE_MARKUP_BASED_LOAN]: __('Loan Finance Reserve Formula'),
  [LENDER_FORM_KEYS.FINANCE_RESERVE_MARKUP_BASED_LEASE]: __('Lease Finance Reserve Formula'),
  [LENDER_FORM_KEYS.FINANCE_RESERVE_FLAT_AMOUNT_LOAN]: __('Loan Finance Reserve Formula'),
  [LENDER_FORM_KEYS.FINANCE_RESERVE_FLAT_AMOUNT_LEASE]: __('Lease Finance Reserve Formula'),
  [LENDER_FORM_KEYS.FINANCE_RESERVE_SPLIT_PERCENTAGE]: __('Finance Reserve Split %'),
  [LENDER_FORM_KEYS.FINANCE_RESERVE]: __('Finance Reserve Adjustment'),

  [LENDER_FORM_KEYS.ADDRESS]: __('Address'),
  [LENDER_FORM_KEYS.ADDRESS2]: __('Address Line 2'),
  [LENDER_FORM_KEYS.ADDRESS3]: __('Address Line 3'),
  [LENDER_FORM_KEYS.ADDRESS4]: __('Address Line 4'),
  [LENDER_FORM_KEYS.CITY]: __('City'),
  [LENDER_FORM_KEYS.COUNTY]: __('County'),
  [LENDER_FORM_KEYS.COUNTRY]: __('Country'),
  [LENDER_FORM_KEYS.PHONE]: __('Phone'),
  [LENDER_FORM_KEYS.LIENHOLDER_ADDRESS]: __('Lienholder Address'),
  [LENDER_FORM_KEYS.LIENHOLDER_ADDRESS2]: __('Address Line 2'),
  [LENDER_FORM_KEYS.LIENHOLDER_ADDRESS3]: __('Address Line 3'),
  [LENDER_FORM_KEYS.LIENHOLDER_ADDRESS4]: __('Address Line 4'),
  [LENDER_FORM_KEYS.LIENHOLDER_STATE]: __('Lienholder'),
  [LENDER_FORM_KEYS.LIENHOLDER_CITY]: __('Lienholder City'),
  [LENDER_FORM_KEYS.LIENHOLDER_ZIP_CODE]: __('Lienholder'),
  [LENDER_FORM_KEYS.LIENHOLDER_PHONE_NUMBER]: __('Phone'),
  [LENDER_FORM_KEYS.LINEHOLDER_COUNTY]: __('Lienholder'),
  [LENDER_FORM_KEYS.LINEHOLDER_COUNTRY]: __('Lienholder'),
  [LENDER_FORM_KEYS.LIEN_HOLDER_LENDER]: __('TradeIn Lienholder'),
  [LENDER_FORM_KEYS.LIEN_PAY_OFF_ADDRESS]: __('Address'),
  [LENDER_FORM_KEYS.LIEN_PAY_OFF_ADDRESS2]: __('Address Line 2'),
  [LENDER_FORM_KEYS.LIEN_PAY_OFF_ADDRESS3]: __('Address Line 3'),
  [LENDER_FORM_KEYS.LIEN_PAY_OFF_ADDRESS4]: __('Address Line 4'),
  [LENDER_FORM_KEYS.LIEN_PAY_OFF_CITY]: __('City'),
  [LENDER_FORM_KEYS.LIEN_PAY_OFF_COUNTY]: __('Part-Ex Lien Holder County'),
  [LENDER_FORM_KEYS.LIEN_PAY_OFF_COUNTRY]: __('Part-Ex Lien Holder Country'),
  [LENDER_FORM_KEYS.LIEN_PAY_OFF_ZIP]: __(''),
  [LENDER_FORM_KEYS.LIEN_PAY_OFF_PH_NUM]: __('Phone'),
  [LENDER_FORM_KEYS.FEIN_NUMBER]: __('FEIN Number'),
  [LENDER_FORM_KEYS.PERSONAL_PROPERTY_TAX_LENDER_TYPE]: __('Personal Property Lender Type'),
  [LENDER_FORM_KEYS.FIRST_PAYMENT_WAIVED_DEFAULT_VALUE]: __('First Payment Waived'),
  [LENDER_FORM_KEYS.DEALER_TYPE]: __('Dealer Type'),
  [LENDER_FORM_KEYS.COLLECT_SECURITY_DEPOSIT_WAIVER_REASON]: __('Collect Security Deposit Waiver Reason'),
  [LENDER_FORM_KEYS.ONE_PAY_TWO_DIGITS_AFTER_COMMA_MONTHLY_PAYMENT]: __('OnePay 2 Digits After Monthly Payment'),
  [LENDER_FORM_KEYS.ONE_PAY_APR_MONTHLY_LEASE_CHARGE_CALCULATION]: __('OnePay APR Monthly Lease Charge Calculation'),
  [LENDER_FORM_KEYS.INCLUDE_SECURITY_DEPOSITS]: __('Include Security Deposits'),
  [LENDER_FORM_KEYS.DISPOSITION_FEE_DEFAULTS_TO_MONTHLY_PAYMENT]: __('Disposition Fee Defaults To Monthly Payment'),
  [LENDER_FORM_KEYS.THRESHOLD_VALUE_FOR_DISPOSITION_FEE]: __('Threshold Value For Disposition Fee'),
  [LENDER_FORM_KEYS.THRESHOLD_VALUE_FOR_TERMINATION_FEE]: __('Threshold Value For Termination Fee'),
  [LENDER_FORM_KEYS.ACQUISITION_FEE_MARKUP]: __('Acquisition Fee Markup'),
  [LENDER_FORM_KEYS.ACQUISITION_FEE]: __('Acquisition Fee'),
  [LENDER_FORM_KEYS.BANK_FEE_UPFRONT]: __('Bank Fee'),
  [LENDER_FORM_KEYS.EVAULTING_REQUIRED]: __('E-vaulting Required'),
  [LENDER_FORM_KEYS.TOTAL_SECURITY_DEPOSITS]: __('Total Number of Security Deposits'),
  [LENDER_FORM_KEYS.ROUND_SECURITY_DEPOSIT_TO]: __('Round Security Deposit to'),
  [LENDER_FORM_KEYS.TYPE_OF_SECURITY_DEPOSIT]: __('Type of Security Deposits'),
  [LENDER_FORM_KEYS.APPLY_TAX_EXEMPT_FOR_USE_TAX]: __(
    'Enable Tax Schedule Payments (Applicable for Idaho & Washington)'
  ),
  [LENDER_FORM_KEYS.EXCLUDE_FNI_PRODUCTS_IN_USE_TAX_CALCULATION]: __(
    'Exclude F&I Products in Use Tax Calculation (Applicable for Washington)'
  ),
  [LENDER_FORM_KEYS.TRADE_IN_TAX_CREDIT_TO_LEASE]: __('Trade-In Tax Credit to Lease'),
  [LENDER_FORM_KEYS.INCLUDE_TAXABLE_FNI_PRODUCTS_IN_MUT]: __(
    'Include Taxable F&I Products in MUT (Applicable for Washington)'
  ),
  [LENDER_FORM_KEYS.REMOVE_NONTAX_FNI_PRODUCT_FROM_USE_TAX]: __(
    'Remove non-taxable F&I products from MUT (Applicable for California)'
  ),
  [LENDER_FORM_KEYS.PAYMENT_DUE_DATE_ADJUSTMENT]: __('Payment Due Date Adjustment'),
  [LENDER_FORM_KEYS.ODD_DAYS_BASED_ON_DAYS_PER_MONTH]: __('Odd Days Based On Days Per Month'),
  [LENDER_FORM_KEYS.DAYS_PER_YEAR_FOR_ODD_DAYS_INTEREST]: __(' Days Per Year For Odd Days Interest '),
  [LENDER_FORM_KEYS.VEHICLE_CATEGORY]: __('Supported Vehicle Category'),
  [LENDER_FORM_KEYS.ANNUITY_DUE]: __('Annuity Due'),
  [LENDER_FORM_KEYS.ITEMIZE_CCR_FOR_TAXATION]: __(`Don't itemize CCR for Taxation`),
  [LENDER_FORM_KEYS.OPTION_CONTRACT_VALIDITY_IN_CALENDAR_DAYS]: __('Option Contract Validity in Calendar Days'),
  [LENDER_FORM_KEYS.LENDER_DEALER_ID]: __('Dealer Id'),
};

const RRG_LENDER_SETUP_FORM_FIELDS_TO_LABEL_MAP = {
  [LENDER_FORM_KEYS.LIEN_FILING_CODE]: __('Lien Filing Code'),
  [LENDER_FORM_KEYS.LIENHOLDER]: __(''),
  [LENDER_FORM_KEYS.LIENHOLDER_NAME]: __('Lienholder Name'),
  [LENDER_FORM_KEYS.LOAN_SUPPORTED]: __('Loan Supported'),
  [LENDER_FORM_KEYS.LEASE_SUPPORTED]: __('Lease Supported'),
  [LENDER_FORM_KEYS.ADDRESS]: __('Address Line 1'),
  [LENDER_FORM_KEYS.LIENHOLDER_ADDRESS]: __('Address Line 1'),
  [LENDER_FORM_KEYS.LIENHOLDER_CITY]: __('Lienholder City'),
  [LENDER_FORM_KEYS.LIENHOLDER_ZIP_CODE]: __('Lienholder'),
  [LENDER_FORM_KEYS.LINEHOLDER_COUNTY]: __('Lienholder'),
  [LENDER_FORM_KEYS.LINEHOLDER_COUNTRY]: __('Lienholder'),
  [LENDER_FORM_KEYS.LIEN_HOLDER_LENDER]: __('Trade-In Finance Company'),
  [LENDER_FORM_KEYS.LIEN_PAY_OFF_ADDRESS]: __('Address Line 1'),
  [LENDER_FORM_KEYS.LIEN_PAY_OFF_CITY]: __('Trade-In Lienholder City'),
  [LENDER_FORM_KEYS.LIEN_PAY_OFF_COUNTY]: __('Trade-In Lienholder County'),
  [LENDER_FORM_KEYS.LIEN_PAY_OFF_COUNTRY]: __('Trade-In Lienholder Country'),
  [LENDER_FORM_KEYS.LIEN_PAY_OFF_ZIP]: __('Trade-In Lienholder'),

  [LENDER_FORM_KEYS.LIEN_HOLDER_FORM_SECTION_LABEL]: __('Lienholder'),
  [LENDER_FORM_KEYS.PART_EX_LIEN_HOLDER_FORM_SECTION_LABEL]: __('Trade-In Finance Company'),
};

const INCHCAPE_LENDER_SETUP_FORM_FIELDS_TO_LABEL_MAP = {
  [LENDER_FORM_KEYS.LIEN_FILING_CODE]: __('Finance Company Filing code'),
  [LENDER_FORM_KEYS.LIENHOLDER]: __('Finance Company'),
  [LENDER_FORM_KEYS.LIENHOLDER_NAME]: __('Finance Company Name'),
  [LENDER_FORM_KEYS.LOAN_SUPPORTED]: __('Lender Supports HP'),
  [LENDER_FORM_KEYS.LEASE_SUPPORTED]: __('Lender Supports PCP'),
  [LENDER_FORM_KEYS.CONTRA_SETTLEMENTS_SUPPORTED_LENDER]: __('Lender Supports Contra Settlements'),
  [LENDER_FORM_KEYS.CONTRA_SETTLEMENTS_SUPPORTED_FINANCE_COMPANY]: __('Finance Company Supports Contra Settlements'),
  [LENDER_FORM_KEYS.ADDRESS]: __('Address Line 1'),
  [LENDER_FORM_KEYS.LIENHOLDER_ADDRESS]: __('Address Line 1'),
  [LENDER_FORM_KEYS.LIENHOLDER_STATE]: __('Finance Company'),
  [LENDER_FORM_KEYS.LIENHOLDER_CITY]: __('Finance Company City'),
  [LENDER_FORM_KEYS.LIENHOLDER_ZIP_CODE]: __('Finance Company'),
  [LENDER_FORM_KEYS.LINEHOLDER_COUNTY]: __('Finance Company'),
  [LENDER_FORM_KEYS.LINEHOLDER_COUNTRY]: __('Finance Company'),
  [LENDER_FORM_KEYS.LIEN_HOLDER_LENDER]: __('Part-EX Finance Company'),
  [LENDER_FORM_KEYS.LIEN_PAY_OFF_ADDRESS]: __('Address Line 1'),
  [LENDER_FORM_KEYS.LIEN_PAY_OFF_CITY]: __('Part-Ex Finance Company City'),
  [LENDER_FORM_KEYS.LIEN_PAY_OFF_COUNTY]: __('Part-Ex Finance Company County'),
  [LENDER_FORM_KEYS.LIEN_PAY_OFF_COUNTRY]: __('Part-Ex Finance Company Country'),
  [LENDER_FORM_KEYS.LIEN_PAY_OFF_ZIP]: __('Part-Ex Finance Company'),

  [LENDER_FORM_KEYS.LIEN_HOLDER_FORM_SECTION_LABEL]: __('Finance Company'),
  [LENDER_FORM_KEYS.PART_EX_LIEN_HOLDER_FORM_SECTION_LABEL]: __('Part-Ex Finance Company'),
  [LENDER_FORM_KEYS.LENDER_SUPPORTS_CREDIT_APPLICATION]: __('Lender Supports Credit Application'),
  [LENDER_FORM_KEYS.PREFERRED]: __('Approved Lender'),
};

export const ADDRESS_FORMAT_KEYS = {
  LINE1: 'line1',
  LINE2: 'line2',
  LINE3: 'line3',
  LINE4: 'line4',
  STATE: 'state',
  CITY: 'city',
  ZIP_CODE: 'zipCode',
  COUNTY: 'county',
  COUNTRY: 'country',
};

export {
  DEFAULT_LENDER_SETUP_FORM_FIELDS_TO_LABEL_MAP,
  RRG_LENDER_SETUP_FORM_FIELDS_TO_LABEL_MAP,
  INCHCAPE_LENDER_SETUP_FORM_FIELDS_TO_LABEL_MAP,
};

export const DEALER_ID_TOOLTIP = __('This id is used for submitting credit application directly to the lender');

export const GM_LENDER_CODES = ['GMF_FF', 'GMF', 'GMFB_FF', 'GMF_FF_FLP', 'GMFB', 'GMF_FLP', 'GMF'];

export const SUBVENTION_COST_TYPES_OPTIONS = {
  PERCENTAGE: 'PERCENTAGE',
  ABSOLUTE: 'ABSOLUTE',
};

export const DEFAULT_LENDER_IDS = {
  OSF: 'defaultOSFLender',
  MOTABILITY: 'defaultMotableLender',
};

export const DEFAULT_OSF_LENDER = {
  name: 'Default OSF Lender',
  displayName: 'Default OSF Lender',
  codeName: 'DEFAULT_OSF_LENDER',
  customerNumber: '',
  code: 'DEFAULT_OSF_LENDER',
  osfLender: true,
  id: DEFAULT_LENDER_IDS.OSF,
};

export const DEFAULT_MOTABILITY_LENDER = {
  name: 'Default Motability Lender',
  displayName: 'Default Motability Lender',
  codeName: 'DEFAULT_MOTABLE_LENDER',
  customerNumber: '',
  code: 'DEFAULT_MOTABLE_LENDER',
  osfLender: true,
  id: DEFAULT_LENDER_IDS.MOTABILITY,
};
