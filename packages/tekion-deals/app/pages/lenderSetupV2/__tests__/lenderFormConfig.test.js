import _get from 'lodash/get';

import * as DealerUtils from 'utils/dealerUtils';
import {
  LENDER_SETUP_FORM_WITH_OSF_ENABLED,
  LENDER_SETUP_FORM_WITH_OSF_NOT_ENABLED,
  FORM_WITH_SAME_AS_LENDER,
} from '../__mocks__/lenderSetup.mock';
import { getFormSectionLenderInchcapeRRG, getFormSection } from '../lenderFormConfig';
import { getLabelForLenderSetupForm } from '../lenderSetup.helpers';
import {
  DEFAULT_LENDER_SETUP_FORM_FIELDS_TO_LABEL_MAP,
  INCHCAPE_LENDER_SETUP_FORM_FIELDS_TO_LABEL_MAP,
  LENDER_FORM_KEYS,
} from '../lenderSetup.constants';

// Mock the dealer program utility functions from the correct import path
jest.mock('@tekion/tekion-base/utils/sales/dealerProgram.utils', () => ({
  ...jest.requireActual('@tekion/tekion-base/utils/sales/dealerProgram.utils'),
  isInchcape: jest.fn(() => false),
  isInchcapeOrRRG: jest.fn(() => false),
  isRRG: jest.fn(() => false),
}));

const { isInchcape, isInchcapeOrRRG } = require('@tekion/tekion-base/utils/sales/dealerProgram.utils');
const { ONE_PAY_APR_MONTHLY_LEASE_CHARGE_CALCULATION } = LENDER_FORM_KEYS;

describe('Basic info fields check in Lender Setup', () => {
  beforeEach(() => {
    // Reset mock before each test
    isInchcape.mockReset();

    // Set default mock value
    isInchcape.mockReturnValue(false);
  });

  it('INCHCAPE lender setup form OSF enabled', () => {
    expect(getFormSectionLenderInchcapeRRG(false, false, true, false)).toMatchObject(
      LENDER_SETUP_FORM_WITH_OSF_ENABLED
    );
  });

  it('INCHCAPE lender setup form OSF not enabled and isInchcape true', () => {
    isInchcape.mockReturnValue(true);

    expect(getFormSectionLenderInchcapeRRG(false, false, false, false)).toMatchObject(
      LENDER_SETUP_FORM_WITH_OSF_NOT_ENABLED
    );
  });

  it('INCHCAPE lender setup form OSF not enabled with lienholderSameAsLender, lienPayOffAddressSameAsLienHolderAddress with isInchcape true', () => {
    isInchcape.mockReturnValue(true);

    expect(getFormSectionLenderInchcapeRRG(true, true, false, false)).toMatchObject(FORM_WITH_SAME_AS_LENDER);
  });
});

describe('Check if getLabelForLenderSetupForm returns correct default,  Inchcape field labels', () => {
  const fieldId = LENDER_FORM_KEYS.LIENHOLDER_NAME;

  beforeEach(() => {
    // Set default mock value
    isInchcape.mockReturnValue(false);
  });

  it('Check if getLabelForLenderSetupForm returns correct default field labels', () => {
    isInchcape.mockReturnValue(false);
    const defaultLabel = _get(DEFAULT_LENDER_SETUP_FORM_FIELDS_TO_LABEL_MAP, fieldId);

    expect(getLabelForLenderSetupForm(fieldId)).toBe(defaultLabel);
  });

  it('Check if getLabelForLenderSetupForm returns correct Inchcape field labels', () => {
    isInchcape.mockReturnValue(true);
    isInchcapeOrRRG.mockReturnValue(true);
    const inchcapeLabel = _get(INCHCAPE_LENDER_SETUP_FORM_FIELDS_TO_LABEL_MAP, fieldId);

    expect(getLabelForLenderSetupForm(fieldId)).toBe(inchcapeLabel);
  });
});

describe('getFormSection', () => {
  it('should include ONE_PAY_APR_MONTHLY_LEASE_CHARGE_CALCULATION in the section if the dealer is in Canada', () => {
    // Mock the isCanadaDealer function to return true
    jest.spyOn(DealerUtils, 'isCanadaDealer').mockReturnValue(true);

    // Call the function with parameters
    const result = getFormSection(false, false, false, false, [], [], false, false, null, false, false);
    // Extract the relevant section
    const section = result[0].rows?.find(item => item.columns.includes(ONE_PAY_APR_MONTHLY_LEASE_CHARGE_CALCULATION));

    // Assert that the section is defined
    expect(section).toBeDefined();
  });

  it('should include ONE_PAY_APR_MONTHLY_LEASE_CHARGE_CALCULATION in the section if the dealer is in Canada', () => {
    // Mock the isCanadaDealer function to return false
    jest.spyOn(DealerUtils, 'isCanadaDealer').mockReturnValue(false);

    // Call the function with parameters
    const result = getFormSection(false, false, false, false, [], [], false, false, null, false, false);
    // Extract the relevant section
    const section = result[0].rows?.find(item => item.columns.includes(ONE_PAY_APR_MONTHLY_LEASE_CHARGE_CALCULATION));

    // Assert that the section is undefined
    expect(section).toBeUndefined();
  });
});
