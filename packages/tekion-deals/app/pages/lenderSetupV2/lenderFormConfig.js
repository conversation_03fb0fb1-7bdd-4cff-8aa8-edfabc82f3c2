import { defaultMemoize } from 'reselect';
import _map from 'lodash/map';

import { getCurrencySymbol } from '@tekion/tekion-base/formatters/formatCurrency';
import { VEHICLE_CATEGORIES } from '@tekion/tekion-base/marketScan/constants/constants';
import TextInput from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/textInput';
import PhoneNumberInput from '@tekion/tekion-widgets/src/appServices/sales/organisms/customPhoneNumberInputField';
import { makePhoneNumberValidator } from '@tekion/tekion-widgets/src/fieldRenderers/phoneNumberInputField';
import { formatPhoneFields } from '@tekion/tekion-base/formatters/number';

import CurrencyInput from '@tekion/tekion-widgets/src/fieldRenderers/currencyInputField';
import NumberInput from '@tekion/tekion-widgets/src/fieldRenderers/numberInputField';
import WithCustomCurrencyInput from '@tekion/tekion-widgets/src/appServices/sales/hocs/withCustomCurrencyInput';
import { isRequiredRule, nonNegativeRule } from 'tbase/utils/formValidators';
import Radio from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/radio';
import Checkbox from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/checkbox';
import Select from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/select';
import MultiSelect from 'organisms/multiSelectComponent';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';
import AddressFieldRenderer from '@tekion/tekion-widgets/src/fieldRenderers/addressField';
import withIcon, { POSITIONS } from '@tekion/tekion-components/src/molecules/WithIcon.hoc';
import { MARKUP_TYPES } from '@tekion/tekion-base/constants/retail/markupTypes';
import { isCanadaDealer } from 'utils/dealerUtils';

import {
  LENDER_RATE_TYPES,
  LENDER_RATE_TYPES_DISPLAY,
  PROPERTY_TAX_LENDER_TYPE,
  PROPERTY_TAX_LENDER_TYPE_DISPLAY_NAME,
} from '@tekion/tekion-base/constants/retail/marketScan';
import { EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import { FEATURE_INTEGRATION_ENTITIES } from '@tekion/tekion-business/src/constants/featureConfig';
import DealSetupAPI from 'commonActions/apis/dealSetup.api';
import { SECURITY_DEPOSIT_OPTIONS } from 'pages/desking/desking.constants';
import { UPFRONT, CAPPED } from 'pages/desking/components/feeUpdate/feeUpdateTable.constants';

import AddressField from 'organisms/addressField';
import { isIntegerRule } from 'utils/formValidator';
import { LENDER_ADDRESS_LAYOUT_BY_LOCALES } from 'constants/address/lenderAddress';
import { LIENHOLDER_LAYOUT_BY_LOCALES } from 'constants/address/lienholderAddress';

import { isInchcape, isInchcapeOrRRG, isRRG } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
import LenderSelect from './components/LenderSelect';
import InfoField from './components/InfoField';
import {
  FINANCE_RESERVE_CAP_COST_BASED_FORMULAS,
  FINANCE_RESERVE_MARKUP_BASED_FORMULAS,
  LENDER_CODES_FOR_EVAULT_REQUIRED_DISABLED_AND_TRUE,
  LENDER_CODE_FOR_DEALER_TRACK_OPTIONS,
  LENDER_FORM_KEYS,
  TRADE_IN_TAX_CREDIT_TO_LEASE_OPTIONS,
  DEALER_ID_TOOLTIP,
  SUBVENTION_COST_TYPES_OPTIONS,
  DEFAULT_LENDER_IDS,
} from './lenderSetup.constants';
import { formatCustomers, formatVendors, stateConstraint } from './utils';
import { getLabelForLenderSetupForm, isGMLender } from './lenderSetup.helpers';
import {
  LENDER_ADDRESS_FIELD_CONFIG_BY_LOCALES,
  LIENHOLDER_ADDRESS_FIELD_CONFIG_BY_LOCALES,
} from './lenderSetup.address';
import styles from './lenderSetup.module.scss';
import mileageLabeler from '../../utils/label';
import AsyncSelect from './components/AsyncSelect';
import { getCustomerList } from './lenderSetup.actions';

const {
  OPTION_CONTRACT_VALIDITY_IN_CALENDAR_DAYS,
  CODE_NAME,
  DEFAULT_LENDER,
  DISPLAY_NAME,
  REMOVE_DEMO_MILEAGE,
  REMOVE_DEMO_MILEAGE_DECIDER_VALUE,
  VEHICLE_MILEAGE_CUT_OFF_REMOVE_DEMO_MILEAGE,
  FIRST_PAYMENT_WAIVED_DEFAULT_VALUE,
  LIENHOLDER_STATE,
  LIENHOLDER,
  ACQUISITION_FEE_MARKUP,
  BANK_FEE_UPFRONT,
  ACQUISITION_FEE,
  ACTUAL_PENALTY_PER_MILE,
  PENALTY_PER_MILE,
  LEGAL_NAME,
  TEKION_LENDER_CODE,
  DEALER_TYPE,
  CUSTOMER_NUMBER,
  VENDOR_NUMBER,

  LIEN_FILING_CODE,
  LIENHOLDER_NAME,
  PREFERRED,
  LOAN_SUPPORTED,
  LEASE_SUPPORTED,
  CONTRA_SETTLEMENTS_SUPPORTED_LENDER,
  CONTRA_SETTLEMENTS_SUPPORTED_FINANCE_COMPANY,
  LIEN_HOLDER_LENDER,
  DISPOSITION_FEE_DEFAULTS_TO_MONTHLY_PAYMENT,
  THRESHOLD_VALUE_FOR_DISPOSITION_FEE,
  THRESHOLD_VALUE_FOR_TERMINATION_FEE,
  SECURITY_DEPOSIT_DEFAULT,
  ADDRESS,
  ADDRESS2,
  ADDRESS3,
  ADDRESS4,

  STATE,
  CITY,
  COUNTY,
  COUNTRY,
  ZIP_CODE,
  PHONE,
  LOAN_TYPES,
  DIGITAL_LOAN_TYPES,
  LOAN_TYPES_VALUES,
  DIGITAL_LOAN_TYPES_VALUES,
  LEASE_TYPES,
  DIGITAL_LEASE_TYPES,
  LEASE_TYPES_VALUES,
  DIGITAL_LEASE_TYPES_VALUES,
  FINANCE_RESERVE_SPLIT_PERCENTAGE,
  // ENABLE_TAX_FINANCE_CHARGE :'enableTxFinanceCharge',
  FINANCE_RESERVE_CAPCOST_BASED_LOAN,
  FINANCE_RESERVE_CAPCOST_BASED_LEASE,
  FINANCE_RESERVE_FLAT_AMOUNT_LOAN,
  FINANCE_RESERVE_FLAT_AMOUNT_LEASE,
  FINANCE_RESERVE_MARKUP_BASED_LOAN,
  FINANCE_RESERVE_MARKUP_BASED_LEASE,
  FINANCE_RESERVE,
  LIENHOLDER_ADDRESS,
  LIENHOLDER_ADDRESS2,
  LIENHOLDER_ADDRESS3,
  LIENHOLDER_ADDRESS4,

  LIENHOLDER_CITY,
  LIENHOLDER_ZIP_CODE,
  LIENHOLDER_PHONE_NUMBER,
  LINEHOLDER_COUNTY,
  LINEHOLDER_COUNTRY,

  OSFLENDER,
  FULL_TERM_BALLOON,
  APRMFTYPE,
  RESIDUALTYPE,
  MAX_MARKUP_FOR_LOAN,
  MAX_MARKUP_FOR_LEASE,
  MONEY_FACTOR_DECIMAL_POINTS,
  MONEY_FACTOR_OPERATIONS,
  MAX_MARKUP_FOR_ONEPAY,
  COLLECT_SECURITY_DEPOSIT_WAIVER_REASON,
  REMOVE_DEMO_MILEAGE_FROM_ANNUAL_MILAGE_DISPLAY_NAME,
  FREE_MILES_FOR_RESIDUAL_ADJUSTMENT,
  INCLUDE_SECURITY_DEPOSITS,
  LIEN_PAY_OFF_ADDRESS,
  LIEN_PAY_OFF_ADDRESS2,
  LIEN_PAY_OFF_ADDRESS3,
  LIEN_PAY_OFF_ADDRESS4,

  LIEN_PAY_OFF_STATE,
  LIEN_PAY_OFF_CITY,
  LIEN_PAY_OFF_ZIP,
  LIEN_PAY_OFF_PH_NUM,
  LIEN_PAY_OFF_COUNTY,
  LIEN_PAY_OFF_COUNTRY,
  FEIN_NUMBER,
  LIEN_PAY_OFF_ADDRESS_SAME_AS_LIEN_HOLDER_ADDRESS,
  ROUND_OFF_MILEAGE_TO_NEAREST_THOUSAND,
  EVAULTING_REQUIRED,
  PERSONAL_PROPERTY_TAX_LENDER_TYPE,
  TOTAL_SECURITY_DEPOSITS,
  ROUND_SECURITY_DEPOSIT_TO,
  TYPE_OF_SECURITY_DEPOSIT,
  APPLY_TAX_EXEMPT_FOR_USE_TAX,
  EXCLUDE_FNI_PRODUCTS_IN_USE_TAX_CALCULATION,
  TRADE_IN_TAX_CREDIT_TO_LEASE,
  INCLUDE_TAXABLE_FNI_PRODUCTS_IN_MUT,
  REMOVE_NONTAX_FNI_PRODUCT_FROM_USE_TAX,
  ADD_VEHICLE_MILES_TO_ANNUAL_MILES,
  ENABLE_YEARLY_MILES_FOR_BALLOON,
  PAYMENT_DUE_DATE_ADJUSTMENT,
  ODD_DAYS_BASED_ON_DAYS_PER_MONTH,
  DAYS_PER_YEAR_FOR_ODD_DAYS_INTEREST,
  SUBVENTION_COST_TYPE,
  SUBVENTION_COST_VALUE,

  VEHICLE_CATEGORY,
  ANNUITY_DUE,
  ONE_PAY_TWO_DIGITS_AFTER_COMMA_MONTHLY_PAYMENT,
  ONE_PAY_APR_MONTHLY_LEASE_CHARGE_CALCULATION,
  LENDER_CODE_FOR_DEALER_TRACK,

  LIEN_HOLDER_FORM_SECTION_LABEL,
  PART_EX_LIEN_HOLDER_FORM_SECTION_LABEL,
  ITEMIZE_CCR_FOR_TAXATION,
  LENDER_SUPPORTS_CREDIT_APPLICATION,
  LENDER_DEALER_ID,
  USE_DECIMAL_PAYMENT_CALC,
  LENDER_ADDRESS_DETAILS,
  LIENHOLDER_ADDRESS_DETAILS,
} = LENDER_FORM_KEYS;

const CheckBoxWithInfo = withIcon(Checkbox);
const CustomCurrencyInput = WithCustomCurrencyInput(NumberInput);
const HELP_DESCRIPTION_FOR_STATE_FIELD = __('Please Enter $$(State) Code, max 2 Characters');
const isGalaxyEnabled = DealerPropertyHelper.isGalaxyEnabled();

const LOAN_TYPES_VS_FORMULA = {
  [MARKUP_TYPES.MARKUP_BASED]: FINANCE_RESERVE_MARKUP_BASED_LOAN,
  [MARKUP_TYPES.FLAT_AMOUNT]: FINANCE_RESERVE_FLAT_AMOUNT_LOAN,
  [MARKUP_TYPES.CAPCOST_BASED]: FINANCE_RESERVE_CAPCOST_BASED_LOAN,
};

const LEASE_TYPES_VS_FORMULA = {
  [MARKUP_TYPES.MARKUP_BASED]: FINANCE_RESERVE_MARKUP_BASED_LEASE,
  [MARKUP_TYPES.FLAT_AMOUNT]: FINANCE_RESERVE_FLAT_AMOUNT_LEASE,
  [MARKUP_TYPES.CAPCOST_BASED]: FINANCE_RESERVE_CAPCOST_BASED_LEASE,
};

const currencySymbol = getCurrencySymbol();

const lenderRateTypeOptions = _map(LENDER_RATE_TYPES, value => ({
  label: LENDER_RATE_TYPES_DISPLAY[value],
  value,
}));

const propertyTaxLenderTypeOptions = _map(PROPERTY_TAX_LENDER_TYPE, value => ({
  label: PROPERTY_TAX_LENDER_TYPE_DISPLAY_NAME[value],
  value,
}));

const bankFeeUpfrontOptions = [
  {
    value: UPFRONT,
    label: __('Upfront'),
  },
  {
    value: CAPPED,
    label: __('Capped'),
  },
];

const DEALER_TYPE_VALUE_MAP = {
  NONE: 'NONE',
  CHAMPIONS_CLUB: 'CHAMPIONS_CLUB',
  FLOOR_PLAN: 'FLOOR_PLAN',
  NON_FLOOR_PLAN: 'NON_FLOOR_PLAN',
};

const DEALER_TYPE_OPTIONS = [
  {
    value: DEALER_TYPE_VALUE_MAP.NONE,
    label: __('None'),
  },
  {
    value: DEALER_TYPE_VALUE_MAP.CHAMPIONS_CLUB,
    label: __('Champions Club'),
  },
  {
    value: DEALER_TYPE_VALUE_MAP.FLOOR_PLAN,
    label: __('Floor-Plan'),
  },
  {
    value: DEALER_TYPE_VALUE_MAP.NON_FLOOR_PLAN,
    label: __('Non Floor-Plan'),
  },
];

const lenderTypeOptions = () => {
  const lenderTypeOption = [
    { value: 'AMOUNT', label: __('$') },
    { value: 'PERCENTAGE', label: __('%') },
  ];
  return lenderTypeOption;
};

const moneyFactorDecimalPointsOptions = () => {
  const moneyFactorDecimalPoints = [
    { value: '5', label: __('5') },
    { value: '6', label: __('6') },
    { value: '7', label: __('7') },
  ];
  if (DealerPropertyHelper.isCalcEngineEnabled()) {
    moneyFactorDecimalPoints.push(
      { value: '8', label: __('8') },
      { value: '9', label: __('9') },
      { value: '10', label: __('10') }
    );
  }
  return moneyFactorDecimalPoints;
};

const moneyFactorDecimalOperationsOptions = () => {
  const moneyFactorDecimalOperations = [
    { value: 'FLOOR', label: __('Floor') },
    { value: 'CEIL', label: __('Ceil') },
    { value: 'ROUND_OFF', label: __('Round Off') },
    { value: 'TRUNCATE', label: __('Truncate') },
  ];

  return moneyFactorDecimalOperations;
};

const getVehicleCategoryOptions = () => {
  const vehicleCategoryOptions = [
    { value: VEHICLE_CATEGORIES.CAR, label: __('Automotive') },
    { value: VEHICLE_CATEGORIES.RV, label: __('RV') },
  ];
  return vehicleCategoryOptions;
};

const getLienholderField = (lienholderSameAsLender, osfLender) =>
  osfLender
    ? []
    : [
        {
          className: styles.fixedHeightRow,
          columns: [LIENHOLDER, ...(lienholderSameAsLender ? [] : [LIENHOLDER_NAME])],
        },
      ];
const SECURITY_DEPOSIT_ROUNDING_STRATEGY_OPTIONS = [
  {
    label: __('0 - RoundedMinimum'),
    value: 0,
  },
  {
    label: __('2 - RoundedMaximum'),
    value: 2,
  },
  {
    label: __('3 - RoundedAdd'),
    value: 3,
  },
  {
    label: __('4 - RoundedMinimumMultiply'),
    value: 4,
  },
];

const DAYS_PER_YEAR_FOR_ODD_DAYS_INTEREST_OPTIONS = [
  { label: __('360'), value: 360 },
  { label: __('365'), value: 365 },
];
const getLienholderAddressFields = lienholderSameAsLender =>
  lienholderSameAsLender
    ? []
    : [
        {
          columns: [LIENHOLDER_ADDRESS],
        },
        {
          columns: [LIENHOLDER_STATE, LIENHOLDER_CITY],
        },
        {
          columns: [LIENHOLDER_ZIP_CODE, LIENHOLDER_PHONE_NUMBER],
        },
      ];

const getLienHolderPayOffAddressBody = sameAsLienHolder =>
  sameAsLienHolder
    ? []
    : [
        {
          columns: [LIEN_PAY_OFF_ADDRESS],
        },
        {
          columns: [LIEN_PAY_OFF_STATE, LIEN_PAY_OFF_CITY],
        },
        {
          columns: [LIEN_PAY_OFF_ZIP, LIEN_PAY_OFF_PH_NUM],
        },
      ];

const getLienHolderPayOffAddressSection = (lienPayOffAddressSameAsLienHolderAddress, lienHolderLender) =>
  lienHolderLender
    ? [
        {
          header: { label: __('TradeIn Lienholder Address') },
          rows: [
            {
              columns: [LIEN_PAY_OFF_ADDRESS_SAME_AS_LIEN_HOLDER_ADDRESS],
            },
            ...getLienHolderPayOffAddressBody(lienPayOffAddressSameAsLienHolderAddress),
          ],
        },
      ]
    : [];

const getAcquisitionFeeMarkup = osfLender =>
  !osfLender
    ? [
        {
          header: { label: __('Acquisition Fee Markup') },
          rows: [
            {
              columns: [ACQUISITION_FEE_MARKUP, ACQUISITION_FEE],
            },
            {
              columns: [PENALTY_PER_MILE, ACTUAL_PENALTY_PER_MILE],
            },
            {
              columns: [BANK_FEE_UPFRONT],
            },
          ],
        },
      ]
    : [];

const getCodeNameSection = osfLender => {
  if (osfLender) return [DISPLAY_NAME];
  return [CODE_NAME, DISPLAY_NAME];
};

const getFinanceReseveSection = (osfLender, loanTypes, leaseTypes, isNewZCSEnabled) => {
  if (osfLender) return [];
  return [
    {
      header: { label: __('Finance Reserve Adjustment') },
      rows: [
        {
          columns: [LOAN_TYPES, LEASE_TYPES],
        },
        {
          className: styles.loanLeaseMarkUpValueWrapper,
          columns: [LOAN_TYPES_VALUES, LEASE_TYPES_VALUES],
        },
        {
          className: styles.financeReserveFormula,
          columns: [LOAN_TYPES_VS_FORMULA[loanTypes], LEASE_TYPES_VS_FORMULA[leaseTypes]],
        },
        {
          columns: [FINANCE_RESERVE_SPLIT_PERCENTAGE, FINANCE_RESERVE],
        },
        {
          columns: isNewZCSEnabled ? [DIGITAL_LOAN_TYPES, DIGITAL_LEASE_TYPES] : [],
        },
        {
          className: styles.loanLeaseMarkUpValueWrapper,
          columns: isNewZCSEnabled ? [DIGITAL_LOAN_TYPES_VALUES, DIGITAL_LEASE_TYPES_VALUES] : [],
        },
      ],
    },
  ];
};

export const getFormSectionLenderInchcapeRRG = (
  lienholderSameAsLender,
  lienPayOffAddressSameAsLienHolderAddress,
  osfLender,
  isLienHolderLender,
  selectedLenderId,
  hideOSFLenderCheckBox
) => {
  if (selectedLenderId === DEFAULT_LENDER_IDS.OSF) return getDefaultLenderForm('osf');
  if (selectedLenderId === DEFAULT_LENDER_IDS.MOTABILITY) return getDefaultLenderForm('motability');
  return [
    ...getLenderSection(osfLender, hideOSFLenderCheckBox),
    ...getLenderAddressSection(osfLender),
    ...getLienHolderFormSection(lienholderSameAsLender, osfLender),
    ...getPartExLineHolderFormSection(lienPayOffAddressSameAsLienHolderAddress, osfLender, isLienHolderLender),
  ];
};

/**
 * Default lender config which will be mapped to CMS (inchcape only)
 * @param {'osf' | 'motability'} type
 */
const getDefaultLenderForm = type => {
  const headerConf = {
    osf: __('Default OSF Lender'),
    motability: __('Default Motability Lender'),
  };

  const subheaderConf = {
    osf: __('The Default OSF Lender will be mapped to a generic Customer account in CMS module.'),
    motability: __('The Default Motability Lender will be mapped to a generic Customer account in CMS module.'),
  };

  return [
    {
      header: { label: headerConf[type] },
      subHeader: {
        label: subheaderConf[type],
        className: styles.defaultLenderSubheader,
      },
      rows: [
        {
          columns: [CUSTOMER_NUMBER],
        },
      ],
    },
  ];
};

const getLenderCheckBoxes = defaultMemoize(hideOSFLenderCheckBox => {
  if (hideOSFLenderCheckBox) return [];
  return isInchcape() ? [LENDER_SUPPORTS_CREDIT_APPLICATION, CONTRA_SETTLEMENTS_SUPPORTED_LENDER] : [OSFLENDER];
});

function getLenderSection(osfLender, hideOSFLenderCheckBox) {
  return [
    {
      header: { label: __('Lender') },
      rows: [
        {
          columns: osfLender ? [DISPLAY_NAME] : [CODE_NAME, DISPLAY_NAME],
        },
        ...(osfLender
          ? EMPTY_ARRAY
          : [
              {
                columns: [LEGAL_NAME, LIEN_FILING_CODE],
              },
              {
                columns: [CUSTOMER_NUMBER, DEFAULT_LENDER],
              },
              {
                columns: [PREFERRED, LIEN_HOLDER_LENDER],
              },
              {
                columns: [LEASE_SUPPORTED, LOAN_SUPPORTED],
              },
            ]),
        {
          columns: getLenderCheckBoxes(hideOSFLenderCheckBox),
        },
      ],
    },
  ];
}

function getLenderAddressSection() {
  const header = { label: __('Lender Address') };
  if (isRRG()) {
    return [
      {
        header,
        rows: [
          {
            columns: [LENDER_ADDRESS_DETAILS],
          },
        ],
      },
    ];
  }
  return [
    {
      header,
      rows: [
        {
          columns: [ADDRESS],
        },
        {
          columns: [ADDRESS2],
        },
        {
          columns: [ADDRESS3],
        },
        {
          columns: [ADDRESS4],
        },
        {
          columns: [CITY, COUNTY],
        },
        {
          columns: [ZIP_CODE, COUNTRY],
        },
      ],
    },
  ];
}

const getLienHolderAddressSection = () => {
  if (isRRG()) {
    return [
      {
        columns: [LIENHOLDER_ADDRESS_DETAILS],
      },
    ];
  }

  return [
    {
      columns: [LIENHOLDER_ADDRESS],
    },
    {
      columns: [LIENHOLDER_ADDRESS2],
    },
    {
      columns: [LIENHOLDER_ADDRESS3],
    },
    {
      columns: [LIENHOLDER_ADDRESS4],
    },
    {
      columns: [LIENHOLDER_CITY, LINEHOLDER_COUNTY],
    },
    {
      columns: [LIENHOLDER_ZIP_CODE, LINEHOLDER_COUNTRY],
    },
  ];
};

function getLienHolderFormSection(lienholderSameAsLender, osfLender) {
  if (osfLender) return EMPTY_ARRAY;
  return [
    {
      header: { label: getLabelForLenderSetupForm(LIEN_HOLDER_FORM_SECTION_LABEL) },
      rows: [
        {
          columns: [LIENHOLDER],
        },
        ...(isInchcape() ? [{ columns: [CONTRA_SETTLEMENTS_SUPPORTED_FINANCE_COMPANY] }] : EMPTY_ARRAY),
        ...(!lienholderSameAsLender
          ? [
              {
                columns: [LIENHOLDER_NAME, VENDOR_NUMBER],
              },
              ...getLienHolderAddressSection(),
            ]
          : EMPTY_ARRAY),
      ],
    },
  ];
}

function getPartExLineHolderFormSection(
  lienPayOffAddressSameAsLienHolderAddress = false,
  osfLender,
  isLienHolderLender
) {
  if (osfLender || !isLienHolderLender) return EMPTY_ARRAY;
  return [
    {
      header: { label: getLabelForLenderSetupForm(PART_EX_LIEN_HOLDER_FORM_SECTION_LABEL) },
      rows: [
        {
          columns: [LIEN_PAY_OFF_ADDRESS_SAME_AS_LIEN_HOLDER_ADDRESS],
        },
        ...(!lienPayOffAddressSameAsLienHolderAddress
          ? [
              {
                columns: [LIEN_PAY_OFF_ADDRESS],
              },
              {
                columns: [LIEN_PAY_OFF_ADDRESS2],
              },
              {
                columns: [LIEN_PAY_OFF_ADDRESS3],
              },
              {
                columns: [LIEN_PAY_OFF_ADDRESS4],
              },
              {
                columns: [LIEN_PAY_OFF_CITY, LIEN_PAY_OFF_COUNTY],
              },
              {
                columns: [LIEN_PAY_OFF_ZIP, LIEN_PAY_OFF_COUNTRY],
              },
            ]
          : EMPTY_ARRAY),
      ],
    },
  ];
}

export const getFormSection = (
  lienholderSameAsLender,
  osfLender,
  lienPayOffAddressSameAsLienHolderAddress,
  lienHolderLender,
  loanTypes,
  leaseTypes,
  isNewZCSEnabled,
  isInchcapeOrRRGEnabled,
  selectedLenderId,
  hideOSFLenderCheckBox,
  isDealerIdFieldShown
) =>
  isInchcapeOrRRGEnabled
    ? getFormSectionLenderInchcapeRRG(
        lienholderSameAsLender,
        lienPayOffAddressSameAsLienHolderAddress,
        osfLender,
        lienHolderLender,
        selectedLenderId,
        hideOSFLenderCheckBox
      )
    : [
        {
          header: { label: __('Lender') },
          rows: [
            {
              columns: [...(DealerPropertyHelper.isRVVehiclesSupported() ? [VEHICLE_CATEGORY] : EMPTY_ARRAY)],
            },
            {
              columns: [...(isCanadaDealer() ? EMPTY_ARRAY : [TEKION_LENDER_CODE])],
            },
            {
              columns: getCodeNameSection(osfLender),
            },
            {
              columns: [LEGAL_NAME, LIEN_FILING_CODE],
            },
            {
              columns: isDealerIdFieldShown ? [LENDER_DEALER_ID] : EMPTY_ARRAY,
            },
            {
              columns: [FEIN_NUMBER, SECURITY_DEPOSIT_DEFAULT],
            },
            {
              columns: [...(isCanadaDealer() ? [LENDER_CODE_FOR_DEALER_TRACK] : EMPTY_ARRAY)],
            },
            ...getLienholderField(lienholderSameAsLender, osfLender),
            ...getLienholderAddressFields(lienholderSameAsLender),
            {
              columns: osfLender ? EMPTY_ARRAY : [PREFERRED, LOAN_SUPPORTED],
            },
            {
              columns: osfLender ? EMPTY_ARRAY : [LEASE_SUPPORTED, DEFAULT_LENDER],
            },
            {
              columns: [OSFLENDER, FULL_TERM_BALLOON],
            },
            {
              columns: [MAX_MARKUP_FOR_LOAN, MAX_MARKUP_FOR_LEASE],
            },
            {
              columns: [MAX_MARKUP_FOR_ONEPAY, ...(osfLender ? EMPTY_ARRAY : [APRMFTYPE])],
            },
            {
              columns: [MONEY_FACTOR_DECIMAL_POINTS, OPTION_CONTRACT_VALIDITY_IN_CALENDAR_DAYS],
            },
            {
              columns: [DEALER_TYPE],
            },
            {
              columns: [...(isCanadaDealer() ? [MONEY_FACTOR_OPERATIONS] : EMPTY_ARRAY)],
            },
            {
              columns: [...(isCanadaDealer() ? [RESIDUALTYPE] : EMPTY_ARRAY)],
            },
            {
              columns: osfLender ? EMPTY_ARRAY : [REMOVE_DEMO_MILEAGE, REMOVE_DEMO_MILEAGE_DECIDER_VALUE],
            },
            {
              columns: osfLender
                ? EMPTY_ARRAY
                : [COLLECT_SECURITY_DEPOSIT_WAIVER_REASON, REMOVE_DEMO_MILEAGE_FROM_ANNUAL_MILAGE_DISPLAY_NAME],
            },
            {
              columns: osfLender
                ? [ONE_PAY_TWO_DIGITS_AFTER_COMMA_MONTHLY_PAYMENT]
                : [FREE_MILES_FOR_RESIDUAL_ADJUSTMENT, ONE_PAY_TWO_DIGITS_AFTER_COMMA_MONTHLY_PAYMENT],
            },
            {
              columns: isCanadaDealer() ? [ONE_PAY_APR_MONTHLY_LEASE_CHARGE_CALCULATION] : [],
            },
            {
              columns: osfLender ? EMPTY_ARRAY : [INCLUDE_SECURITY_DEPOSITS, FIRST_PAYMENT_WAIVED_DEFAULT_VALUE],
            },
            {
              columns: [
                LIEN_HOLDER_LENDER,
                ...(osfLender ? EMPTY_ARRAY : [DISPOSITION_FEE_DEFAULTS_TO_MONTHLY_PAYMENT]),
              ],
            },
            {
              columns: osfLender
                ? EMPTY_ARRAY
                : [THRESHOLD_VALUE_FOR_DISPOSITION_FEE, THRESHOLD_VALUE_FOR_TERMINATION_FEE],
            },
            {
              columns: osfLender
                ? EMPTY_ARRAY
                : [
                    ...(!isCanadaDealer() ? [TOTAL_SECURITY_DEPOSITS] : EMPTY_ARRAY),
                    ...(!isCanadaDealer() ? [ROUND_SECURITY_DEPOSIT_TO] : EMPTY_ARRAY),
                  ],
            },
            {
              columns: osfLender
                ? EMPTY_ARRAY
                : [
                    ...(!isCanadaDealer() ? [TYPE_OF_SECURITY_DEPOSIT] : EMPTY_ARRAY),
                    ROUND_OFF_MILEAGE_TO_NEAREST_THOUSAND,
                  ],
            },
            {
              columns: osfLender
                ? EMPTY_ARRAY
                : [PERSONAL_PROPERTY_TAX_LENDER_TYPE, VEHICLE_MILEAGE_CUT_OFF_REMOVE_DEMO_MILEAGE],
            },
            {
              columns: [
                APPLY_TAX_EXEMPT_FOR_USE_TAX,
                ...(isGalaxyEnabled ? [TRADE_IN_TAX_CREDIT_TO_LEASE] : EMPTY_ARRAY),
                ...(!DealerPropertyHelper.isCalcEngineEnabled()
                  ? [EXCLUDE_FNI_PRODUCTS_IN_USE_TAX_CALCULATION]
                  : EMPTY_ARRAY),
              ],
            },
            {
              columns: [...(isGalaxyEnabled ? [INCLUDE_TAXABLE_FNI_PRODUCTS_IN_MUT] : EMPTY_ARRAY)],
            },
            {
              columns: [...(isGalaxyEnabled ? [REMOVE_NONTAX_FNI_PRODUCT_FROM_USE_TAX] : EMPTY_ARRAY)],
            },
            {
              columns: [EVAULTING_REQUIRED, ENABLE_YEARLY_MILES_FOR_BALLOON],
            },
            {
              columns: osfLender
                ? [PAYMENT_DUE_DATE_ADJUSTMENT]
                : [ADD_VEHICLE_MILES_TO_ANNUAL_MILES, PAYMENT_DUE_DATE_ADJUSTMENT],
            },
            {
              columns: [DAYS_PER_YEAR_FOR_ODD_DAYS_INTEREST, ODD_DAYS_BASED_ON_DAYS_PER_MONTH],
            },
            {
              columns: [ANNUITY_DUE, SUBVENTION_COST_TYPE],
            },
            {
              columns: [ITEMIZE_CCR_FOR_TAXATION, SUBVENTION_COST_VALUE],
            },
            {
              columns: [USE_DECIMAL_PAYMENT_CALC],
            },
          ],
        },
        ...getLienHolderPayOffAddressSection(lienPayOffAddressSameAsLienHolderAddress, lienHolderLender),
        ...getAcquisitionFeeMarkup(osfLender),
        {
          header: { label: __('Lender Address') },
          rows: [
            {
              columns: [ADDRESS],
            },
            {
              columns: [STATE, CITY],
            },
            {
              columns: [ZIP_CODE, PHONE],
            },
          ],
        },
        ...getFinanceReseveSection(osfLender, loanTypes, leaseTypes, isNewZCSEnabled),
      ];

export const getFormConfigForInchcapeAndRRg = defaultMemoize(
  (lenderCodes, osfFlagEditable, isMultiLingual, getCountOfPhoneNumberDigits, dealerInfo) => ({
    [CODE_NAME]: {
      renderer: LenderSelect,
      renderOptions: {
        label: getLabelForLenderSetupForm(CODE_NAME),
        options: lenderCodes,
        showSearch: true,
        dropdownMatchSelectWidth: false,
      },
    },

    [LEGAL_NAME]: {
      renderer: TextInput,
      renderOptions: {
        label: getLabelForLenderSetupForm(LEGAL_NAME),
        required: true,
        validators: [isRequiredRule],
        isMultiLingual,
      },
    },
    [CUSTOMER_NUMBER]: {
      renderer: AsyncSelect,
      renderOptions: {
        label: getLabelForLenderSetupForm(CUSTOMER_NUMBER),
        id: CUSTOMER_NUMBER,
        API: getCustomerList,
        formatOptions: formatCustomers,
        setInputOptions: () => {},
        responseReader: 'response.hits',
      },
    },

    [VENDOR_NUMBER]: {
      renderer: AsyncSelect,
      renderOptions: {
        label: getLabelForLenderSetupForm(VENDOR_NUMBER),
        id: VENDOR_NUMBER,
        API: DealSetupAPI.searchVendors,
        formatOptions: formatVendors,
        required: true,
        setInputOptions: () => {},
        responseReader: 'response.hits',
      },
    },

    [DISPLAY_NAME]: {
      renderer: TextInput,
      renderOptions: {
        label: getLabelForLenderSetupForm(DISPLAY_NAME),
        fieldClassName: styles.fixwidth_column,
        required: true,
        validators: [isRequiredRule],
        isMultiLingual,
      },
    },
    [LIEN_FILING_CODE]: {
      renderer: TextInput,
      renderOptions: {
        label: getLabelForLenderSetupForm(LIEN_FILING_CODE),
        fieldClassName: styles.fixwidth_column,
      },
    },
    [CONTRA_SETTLEMENTS_SUPPORTED_FINANCE_COMPANY]: {
      renderer: Checkbox,
      renderOptions: {
        label: getLabelForLenderSetupForm(CONTRA_SETTLEMENTS_SUPPORTED_FINANCE_COMPANY),
        fieldClassName: styles.fixwidth_column,
      },
    },
    [LIENHOLDER]: {
      renderer: Radio,
      renderOptions: {
        label: getLabelForLenderSetupForm(LIENHOLDER),
        radios: [
          {
            label: __('Same as Lender'),
            value: true,
          },
          {
            label: __('Other'),
            value: false,
          },
        ],
      },
    },

    [LIENHOLDER_NAME]: {
      renderer: TextInput,
      renderOptions: {
        label: getLabelForLenderSetupForm(LIENHOLDER_NAME),
      },
    },

    [PREFERRED]: {
      renderer: Checkbox,
      renderOptions: {
        label: getLabelForLenderSetupForm(PREFERRED),
        fieldClassName: styles.fixwidth_column,
      },
    },

    [LOAN_SUPPORTED]: {
      renderer: Checkbox,
      renderOptions: {
        label: getLabelForLenderSetupForm(LOAN_SUPPORTED),
        fieldClassName: styles.fixwidth_column,
      },
    },

    [LEASE_SUPPORTED]: {
      renderer: Checkbox,
      renderOptions: {
        label: getLabelForLenderSetupForm(LEASE_SUPPORTED),
        fieldClassName: styles.fixwidth_column,
      },
    },

    [CONTRA_SETTLEMENTS_SUPPORTED_LENDER]: {
      renderer: Checkbox,
      renderOptions: {
        label: getLabelForLenderSetupForm(CONTRA_SETTLEMENTS_SUPPORTED_LENDER),
        fieldClassName: styles.fixwidth_column,
      },
    },

    [DEFAULT_LENDER]: {
      renderer: Checkbox,
      renderOptions: {
        label: getLabelForLenderSetupForm(DEFAULT_LENDER),
        fieldClassName: styles.fixwidth_column,
      },
    },

    [OSFLENDER]: {
      renderer: Checkbox,
      renderOptions: {
        label: getLabelForLenderSetupForm(OSFLENDER),
        fieldClassName: styles.fixwidth_column,
        disabled: !osfFlagEditable,
      },
    },

    [LENDER_SUPPORTS_CREDIT_APPLICATION]: {
      renderer: Checkbox,
      renderOptions: {
        label: getLabelForLenderSetupForm(LENDER_SUPPORTS_CREDIT_APPLICATION),
        fieldClassName: styles.fixwidth_column,
      },
    },

    [FULL_TERM_BALLOON]: {
      renderer: Checkbox,
      renderOptions: {
        label: getLabelForLenderSetupForm(FULL_TERM_BALLOON),
        fieldClassName: styles.fixwidth_column,
      },
    },

    [ADDRESS]: {
      renderer: AddressField,
      renderOptions: {
        label: getLabelForLenderSetupForm(ADDRESS),
      },
    },
    [ADDRESS2]: {
      renderer: TextInput,
      renderOptions: {
        label: getLabelForLenderSetupForm(ADDRESS2),
      },
    },
    [ADDRESS3]: {
      renderer: TextInput,
      renderOptions: {
        label: getLabelForLenderSetupForm(ADDRESS3),
      },
    },
    [ADDRESS4]: {
      renderer: TextInput,
      renderOptions: {
        label: getLabelForLenderSetupForm(ADDRESS4),
      },
    },

    [STATE]: {
      renderer: TextInput,
      renderOptions: {
        label: __('$$(State)'),
        validators: [stateConstraint],
        helpText: HELP_DESCRIPTION_FOR_STATE_FIELD,
        infoPlacement: 'top',
      },
    },

    [CITY]: {
      renderer: TextInput,
      renderOptions: {
        label: getLabelForLenderSetupForm(CITY),
      },
    },

    [COUNTY]: {
      renderer: TextInput,
      renderOptions: {
        label: getLabelForLenderSetupForm(COUNTY),
      },
    },

    [COUNTRY]: {
      renderer: TextInput,
      renderOptions: {
        label: getLabelForLenderSetupForm(COUNTRY),
      },
    },

    [ZIP_CODE]: {
      renderer: TextInput,
      renderOptions: {
        label: __('$$(ZIP Code)'),
        fieldClassName: styles.fixwidth_column,
      },
    },
    [PHONE]: {
      renderer: PhoneNumberInput,
      renderOptions: {
        label: getLabelForLenderSetupForm(PHONE),
        validators: [makePhoneNumberValidator(getCountOfPhoneNumberDigits)],
        id: PHONE,
        fieldClassName: styles.fixwidth_column,
      },
    },

    [LIENHOLDER_ADDRESS]: {
      renderer: AddressField,
      renderOptions: {
        label: getLabelForLenderSetupForm(LIENHOLDER_ADDRESS),
      },
    },

    [LIENHOLDER_ADDRESS2]: {
      renderer: TextInput,
      renderOptions: {
        label: getLabelForLenderSetupForm(LIENHOLDER_ADDRESS2),
      },
    },

    [LIENHOLDER_ADDRESS3]: {
      renderer: TextInput,
      renderOptions: {
        label: getLabelForLenderSetupForm(LIENHOLDER_ADDRESS3),
      },
    },

    [LIENHOLDER_ADDRESS4]: {
      renderer: TextInput,
      renderOptions: {
        label: getLabelForLenderSetupForm(LIENHOLDER_ADDRESS4),
      },
    },
    [LIENHOLDER_STATE]: {
      renderer: TextInput,
      renderOptions: {
        label: getLabelForLenderSetupForm(LIENHOLDER_STATE) + __(' $$(State)'),
        validators: [stateConstraint],
        helpText: HELP_DESCRIPTION_FOR_STATE_FIELD,
        infoPlacement: 'top',
      },
    },
    [LIENHOLDER_CITY]: {
      renderer: TextInput,
      renderOptions: {
        label: getLabelForLenderSetupForm(LIENHOLDER_CITY),
      },
    },
    [LIENHOLDER_ZIP_CODE]: {
      renderer: TextInput,
      renderOptions: {
        label: getLabelForLenderSetupForm(LIENHOLDER_ZIP_CODE) + __(' $$(ZIP Code)'),
        fieldClassName: styles.fixwidth_column,
      },
    },
    [LIENHOLDER_PHONE_NUMBER]: {
      renderer: PhoneNumberInput,
      renderOptions: {
        label: getLabelForLenderSetupForm(LIENHOLDER_PHONE_NUMBER),
        validators: [makePhoneNumberValidator(getCountOfPhoneNumberDigits)],
        fieldClassName: styles.fixwidth_column,
        id: LIENHOLDER_PHONE_NUMBER,
      },
    },

    [LINEHOLDER_COUNTY]: {
      renderer: TextInput,
      renderOptions: {
        label: getLabelForLenderSetupForm(LINEHOLDER_COUNTY) + __(' $$(County)'),
        validators: [], // TODO
        fieldClassName: styles.fixwidth_column,
      },
    },

    [LINEHOLDER_COUNTRY]: {
      renderer: TextInput,
      renderOptions: {
        label: getLabelForLenderSetupForm(LINEHOLDER_COUNTRY) + __(' $$(Country)'),
        validators: [], // TODO
        fieldClassName: styles.fixwidth_column,
      },
    },

    [LIEN_HOLDER_LENDER]: {
      renderer: Checkbox,
      renderOptions: {
        label: getLabelForLenderSetupForm(LIEN_HOLDER_LENDER),
        fieldClassName: styles.fixwidth_column,
      },
    },

    [LIEN_PAY_OFF_ADDRESS]: {
      renderer: AddressField,
      renderOptions: {
        label: getLabelForLenderSetupForm(LIEN_PAY_OFF_ADDRESS),
      },
    },
    [LIEN_PAY_OFF_ADDRESS2]: {
      renderer: TextInput,
      renderOptions: {
        label: getLabelForLenderSetupForm(LIEN_PAY_OFF_ADDRESS2),
      },
    },

    [LIEN_PAY_OFF_ADDRESS3]: {
      renderer: TextInput,
      renderOptions: {
        label: getLabelForLenderSetupForm(LIEN_PAY_OFF_ADDRESS3),
      },
    },
    [LIEN_PAY_OFF_ADDRESS4]: {
      renderer: TextInput,
      renderOptions: {
        label: getLabelForLenderSetupForm(LIEN_PAY_OFF_ADDRESS4),
      },
    },
    [LIEN_PAY_OFF_STATE]: {
      renderer: TextInput,
      renderOptions: {
        label: __('$$(State)'),
        validators: [stateConstraint],
        helpText: HELP_DESCRIPTION_FOR_STATE_FIELD,
        infoPlacement: 'top',
      },
    },

    [LIEN_PAY_OFF_CITY]: {
      renderer: TextInput,
      renderOptions: {
        label: getLabelForLenderSetupForm(LIEN_PAY_OFF_CITY),
      },
    },

    [LIEN_PAY_OFF_COUNTY]: {
      renderer: TextInput,
      renderOptions: {
        label: getLabelForLenderSetupForm(LIEN_PAY_OFF_COUNTY),
      },
    },

    [LIEN_PAY_OFF_COUNTRY]: {
      renderer: TextInput,
      renderOptions: {
        label: getLabelForLenderSetupForm(LIEN_PAY_OFF_COUNTRY),
      },
    },

    [LIEN_PAY_OFF_ZIP]: {
      renderer: TextInput,
      renderOptions: {
        label: getLabelForLenderSetupForm(LIEN_PAY_OFF_ZIP) + __(' $$(ZIP Code)'),
        fieldClassName: styles.fixwidth_column,
      },
    },

    [LIEN_PAY_OFF_PH_NUM]: {
      renderer: PhoneNumberInput,
      renderOptions: {
        label: getLabelForLenderSetupForm(LIEN_PAY_OFF_PH_NUM),
        validators: [makePhoneNumberValidator(getCountOfPhoneNumberDigits)],
        fieldClassName: styles.fixwidth_column,
        id: LIEN_PAY_OFF_PH_NUM,
      },
    },

    [FEIN_NUMBER]: {
      renderer: TextInput,
      renderOptions: {
        label: getLabelForLenderSetupForm(FEIN_NUMBER),
        fieldClassName: styles.fixwidth_column,
      },
    },

    [LIEN_PAY_OFF_ADDRESS_SAME_AS_LIEN_HOLDER_ADDRESS]: {
      renderer: Radio,
      renderOptions: {
        radios: [
          {
            label: isRRG() ? __('Same as Lienholder') : __('Same as Finance Company'),
            value: true,
          },
          {
            label: __('Other'),
            value: false,
          },
        ],
      },
    },

    [ENABLE_YEARLY_MILES_FOR_BALLOON]: {
      renderer: Checkbox,
      renderOptions: {
        label: __('Enable Yearly {{miles}} for Balloon Payment', {
          miles: mileageLabeler.getMilesLabel(),
        }),
        fieldClassName: styles.fixwidth_column,
      },
    },
    [LENDER_ADDRESS_DETAILS]: {
      id: LENDER_ADDRESS_DETAILS,
      renderer: AddressFieldRenderer,
      renderOptions: {
        layoutByLocales: LENDER_ADDRESS_LAYOUT_BY_LOCALES,
        fieldConfigByLocales: LENDER_ADDRESS_FIELD_CONFIG_BY_LOCALES,
        entityType: FEATURE_INTEGRATION_ENTITIES.LENDER,
        dealerConfig: dealerInfo,
      },
    },
    [LIENHOLDER_ADDRESS_DETAILS]: {
      id: LIENHOLDER_ADDRESS_DETAILS,
      renderer: AddressFieldRenderer,
      renderOptions: {
        layoutByLocales: LIENHOLDER_LAYOUT_BY_LOCALES,
        fieldConfigByLocales: LIENHOLDER_ADDRESS_FIELD_CONFIG_BY_LOCALES,
        entityType: FEATURE_INTEGRATION_ENTITIES.LIENHOLDER,
        dealerConfig: dealerInfo,
      },
    },
  })
);

export const getFormConfig = defaultMemoize(
  (
    getCountOfPhoneNumberDigits,
    leaseTypes,
    subventionCostType,
    loanTypes,
    lenderCodes,
    osfFlagEditable,
    removeDemoMileageFromAnnualMileage,
    acquisitionFeeMaxMarkup,
    isMultiLingual,
    codeName,
    lenderDetailsForDealerTrack,
    supportedVehicleCategory,
    tekionLenderCodes,
    code,
    dealerInfo
  ) => {
    if (isInchcapeOrRRG()) {
      return getFormConfigForInchcapeAndRRg(
        lenderCodes,
        osfFlagEditable,
        isMultiLingual,
        getCountOfPhoneNumberDigits,
        dealerInfo
      );
    }
    return {
      [CODE_NAME]: {
        renderer: LenderSelect,
        renderOptions: {
          label: getLabelForLenderSetupForm(CODE_NAME),
          disabled: !isCanadaDealer(),
          options: lenderCodes,
          showSearch: true,
          dropdownMatchSelectWidth: false,
          labelAndKeyPair: DealerPropertyHelper.isGalaxyEnabled(),
        },
      },

      [TEKION_LENDER_CODE]: {
        renderer: LenderSelect,
        renderOptions: {
          label: getLabelForLenderSetupForm(TEKION_LENDER_CODE),
          disabled: false,
          options: tekionLenderCodes,
          className: styles.tekionLenderSelect,
          optionLenderName: styles.tekionOptionLenderName,
          labelAndKeyPair: true,
        },
      },

      [LEGAL_NAME]: {
        renderer: TextInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(LEGAL_NAME),
          required: true,
          validators: [isRequiredRule],
          isMultiLingual,
        },
      },
      [CUSTOMER_NUMBER]: {
        renderer: AsyncSelect,
        renderOptions: {
          label: getLabelForLenderSetupForm(CUSTOMER_NUMBER),
          id: CUSTOMER_NUMBER,
          API: getCustomerList,
          formatOptions: formatCustomers,
          setInputOptions: () => {},
          responseReader: 'response.hits',
        },
      },

      [VENDOR_NUMBER]: {
        renderer: AsyncSelect,
        renderOptions: {
          label: getLabelForLenderSetupForm(VENDOR_NUMBER),
          id: VENDOR_NUMBER,
          API: DealSetupAPI.searchVendors,
          formatOptions: formatVendors,
          setInputOptions: () => {},
          responseReader: 'response.hits',
        },
      },
      [LENDER_DEALER_ID]: {
        renderer: TextInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(LENDER_DEALER_ID),
          required: true,
          validators: [isRequiredRule],
          helpText: DEALER_ID_TOOLTIP,
          infoPlacement: 'top',
        },
      },
      [DISPLAY_NAME]: {
        renderer: TextInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(DISPLAY_NAME),
          fieldClassName: styles.fixwidth_column,
          required: true,
          validators: [isRequiredRule],
          isMultiLingual,
        },
      },
      [LIEN_FILING_CODE]: {
        renderer: TextInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(LIEN_FILING_CODE),
          fieldClassName: styles.fixwidth_column,
        },
      },

      [SECURITY_DEPOSIT_DEFAULT]: {
        renderer: Select,
        renderOptions: {
          label: getLabelForLenderSetupForm(SECURITY_DEPOSIT_DEFAULT),
          options: SECURITY_DEPOSIT_OPTIONS,
          showSearch: true,
          filterOption: true,
          allowClear: true,
        },
      },

      [LENDER_CODE_FOR_DEALER_TRACK]: {
        renderer: MultiSelect,
        renderOptions: {
          label: getLabelForLenderSetupForm(LENDER_CODE_FOR_DEALER_TRACK),
          options: LENDER_CODE_FOR_DEALER_TRACK_OPTIONS,
          values: lenderDetailsForDealerTrack,
          showSearch: true,
          allowClear: true,
        },
      },

      [LIENHOLDER]: {
        renderer: Radio,
        renderOptions: {
          label: getLabelForLenderSetupForm(LIENHOLDER),
          radios: [
            {
              label: __('Same as Lender'),
              value: true,
            },
            {
              label: __('Other'),
              value: false,
            },
          ],
        },
      },

      [LIENHOLDER_NAME]: {
        renderer: TextInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(LIENHOLDER_NAME),
        },
      },

      [PREFERRED]: {
        renderer: Checkbox,
        renderOptions: {
          label: getLabelForLenderSetupForm(PREFERRED),
          fieldClassName: styles.fixwidth_column,
        },
      },

      [LOAN_SUPPORTED]: {
        renderer: Checkbox,
        renderOptions: {
          label: getLabelForLenderSetupForm(LOAN_SUPPORTED),
          fieldClassName: styles.fixwidth_column,
        },
      },
      [LEASE_SUPPORTED]: {
        renderer: Checkbox,
        renderOptions: {
          label: getLabelForLenderSetupForm(LEASE_SUPPORTED),
          fieldClassName: styles.fixwidth_column,
        },
      },

      [DEFAULT_LENDER]: {
        renderer: Checkbox,
        renderOptions: {
          label: getLabelForLenderSetupForm(DEFAULT_LENDER),
          fieldClassName: styles.fixwidth_column,
        },
      },

      [OSFLENDER]: {
        renderer: Checkbox,
        renderOptions: {
          label: getLabelForLenderSetupForm(OSFLENDER),
          fieldClassName: styles.fixwidth_column,
          disabled: !osfFlagEditable,
        },
      },

      [LENDER_SUPPORTS_CREDIT_APPLICATION]: {
        renderer: Checkbox,
        renderOptions: {
          label: getLabelForLenderSetupForm(LENDER_SUPPORTS_CREDIT_APPLICATION),
          fieldClassName: styles.fixwidth_column,
        },
      },

      [FULL_TERM_BALLOON]: {
        renderer: Checkbox,
        renderOptions: {
          label: getLabelForLenderSetupForm(FULL_TERM_BALLOON),
          fieldClassName: styles.fixwidth_column,
        },
      },

      [APRMFTYPE]: {
        renderer: Select,
        renderOptions: {
          label: getLabelForLenderSetupForm(APRMFTYPE),
          options: lenderRateTypeOptions,
          showSearch: true,
          filterOption: true,
          allowClear: false,
        },
      },
      [RESIDUALTYPE]: {
        renderer: Select,
        renderOptions: {
          label: getLabelForLenderSetupForm(RESIDUALTYPE),
          options: lenderTypeOptions(),
          showSearch: true,
          filterOption: true,
          allowClear: false,
        },
      },

      [MONEY_FACTOR_DECIMAL_POINTS]: {
        renderer: Select,
        renderOptions: {
          label: getLabelForLenderSetupForm(MONEY_FACTOR_DECIMAL_POINTS),
          fieldClassName: styles.fixwidth_column,
          options: moneyFactorDecimalPointsOptions(),
          allowClear: false,
        },
      },

      [MONEY_FACTOR_OPERATIONS]: {
        renderer: Select,
        renderOptions: {
          label: getLabelForLenderSetupForm(MONEY_FACTOR_OPERATIONS),
          fieldClassName: styles.fixwidth_column,
          options: moneyFactorDecimalOperationsOptions(),
          allowClear: false,
        },
      },

      [MAX_MARKUP_FOR_LEASE]: {
        renderer: CustomCurrencyInput,
        renderOptions: {
          id: MAX_MARKUP_FOR_LEASE,
          label: getLabelForLenderSetupForm(MAX_MARKUP_FOR_LEASE),
          fieldClassName: styles.fixwidth_column,
          addonAfter: [MARKUP_TYPES.MARKUP_BASED, MARKUP_TYPES.CAPCOST_BASED].includes(loanTypes) && __('%'),
          showCurrencyInput: loanTypes === MARKUP_TYPES.FLAT_AMOUNT,
        },
      },
      [MAX_MARKUP_FOR_LOAN]: {
        renderer: NumberInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(MAX_MARKUP_FOR_LOAN),
          id: MAX_MARKUP_FOR_LOAN,
          fieldClassName: styles.fixwidth_column,
        },
      },
      [MAX_MARKUP_FOR_ONEPAY]: {
        renderer: NumberInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(MAX_MARKUP_FOR_ONEPAY),
          id: MAX_MARKUP_FOR_ONEPAY,
          fieldClassName: styles.fixwidth_column,
          labelClassName: styles.labelClass,
        },
      },

      [ADDRESS]: {
        renderer: AddressField,
        renderOptions: {
          label: getLabelForLenderSetupForm(ADDRESS),
        },
      },
      [ADDRESS2]: {
        renderer: TextInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(ADDRESS2),
        },
      },
      [ADDRESS3]: {
        renderer: TextInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(ADDRESS3),
        },
      },
      [ADDRESS4]: {
        renderer: TextInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(ADDRESS4),
        },
      },

      [STATE]: {
        renderer: TextInput,
        renderOptions: {
          label: __('$$(State)'),
          validators: [stateConstraint],
          helpText: HELP_DESCRIPTION_FOR_STATE_FIELD,
          infoPlacement: 'top',
        },
      },

      [CITY]: {
        renderer: TextInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(CITY),
        },
      },

      [COUNTY]: {
        renderer: TextInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(COUNTY),
        },
      },

      [COUNTRY]: {
        renderer: TextInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(COUNTRY),
        },
      },

      [ZIP_CODE]: {
        renderer: TextInput,
        renderOptions: {
          label: __('$$(ZIP Code)'),
          fieldClassName: styles.fixwidth_column,
        },
      },
      [PHONE]: {
        renderer: PhoneNumberInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(PHONE),
          validators: [makePhoneNumberValidator(getCountOfPhoneNumberDigits)],
          id: PHONE,
          fieldClassName: styles.fixwidth_column,
        },
      },
      [LOAN_TYPES]: {
        renderer: Radio,
        renderOptions: {
          fieldClassName: styles.lessMarginBottom,
          label: getLabelForLenderSetupForm(LOAN_TYPES),
          radios: [
            {
              label: __('Percentage'),
              value: MARKUP_TYPES.MARKUP_BASED,
            },
            {
              label: __('Flat Amount'),
              value: MARKUP_TYPES.FLAT_AMOUNT,
            },
            {
              label: __('% of Amt. Financed'),
              value: MARKUP_TYPES.CAPCOST_BASED,
            },
          ],
        },
      },
      [DIGITAL_LOAN_TYPES]: {
        renderer: Radio,
        renderOptions: {
          fieldClassName: styles.lessMarginBottom,
          label: getLabelForLenderSetupForm(DIGITAL_LOAN_TYPES),
          radios: [
            {
              label: __('Percentage'),
              value: MARKUP_TYPES.MARKUP_BASED,
            },
            {
              label: __('Flat Amount'),
              value: MARKUP_TYPES.FLAT_AMOUNT,
            },
            {
              label: __('% of Amt. Financed'),
              value: MARKUP_TYPES.CAPCOST_BASED,
            },
          ],
        },
      },

      [LOAN_TYPES_VALUES]: {
        renderer: TextInput,
        renderOptions: {
          label: __(''),
          fieldClassName: styles.fixwidth_column,
          type: 'number',
          addonAfter: [MARKUP_TYPES.MARKUP_BASED, MARKUP_TYPES.CAPCOST_BASED].includes(loanTypes) && __('%'),
          addonBefore: loanTypes === MARKUP_TYPES.FLAT_AMOUNT && currencySymbol,
        },
      },

      [SUBVENTION_COST_TYPE]: {
        renderer: Radio,
        renderOptions: {
          fieldClassName: styles.marginLeft,
          label: getLabelForLenderSetupForm(SUBVENTION_COST_TYPE),
          radios: [
            {
              label: __('% Percentage'),
              value: SUBVENTION_COST_TYPES_OPTIONS.PERCENTAGE,
            },
            {
              label: __('Flat Amount'),
              value: SUBVENTION_COST_TYPES_OPTIONS.ABSOLUTE,
            },
          ],
        },
      },

      [LEASE_TYPES_VALUES]: {
        renderer: TextInput,
        renderOptions: {
          label: __(''),
          fieldClassName: styles.fixwidth_column,
          type: 'number',
          addonBefore: leaseTypes === MARKUP_TYPES.FLAT_AMOUNT && currencySymbol,
          addonAfter: leaseTypes === MARKUP_TYPES.CAPCOST_BASED && __('%'),
        },
      },

      [SUBVENTION_COST_VALUE]: {
        renderer: TextInput,
        renderOptions: {
          label: __(''),
          fieldClassName: `${styles.fixwidth_column} ${styles.marginLeftClass}`,
          placeholder:
            subventionCostType === SUBVENTION_COST_TYPES_OPTIONS.ABSOLUTE
              ? __('Enter Subvention Cost Here')
              : __('Enter in Percentage Here'),
          type: 'number',
          addonBefore: subventionCostType === SUBVENTION_COST_TYPES_OPTIONS.ABSOLUTE && currencySymbol,
          addonAfter: subventionCostType === SUBVENTION_COST_TYPES_OPTIONS.PERCENTAGE && __('%'),
        },
      },

      [DIGITAL_LOAN_TYPES_VALUES]: {
        renderer: CustomCurrencyInput,
        renderOptions: {
          id: DIGITAL_LOAN_TYPES_VALUES,
          label: __(''),
          fieldClassName: styles.fixwidth_column,
          addonAfter: [MARKUP_TYPES.MARKUP_BASED, MARKUP_TYPES.CAPCOST_BASED].includes(loanTypes) && __('%'),
          showCurrencyInput: leaseTypes === MARKUP_TYPES.FLAT_AMOUNT,
        },
      },

      [DIGITAL_LEASE_TYPES_VALUES]: {
        renderer: CustomCurrencyInput,
        renderOptions: {
          id: DIGITAL_LEASE_TYPES_VALUES,
          label: __(''),
          fieldClassName: styles.fixwidth_column,
          showCurrencyInput: leaseTypes === MARKUP_TYPES.FLAT_AMOUNT,
          addonAfter: leaseTypes === MARKUP_TYPES.CAPCOST_BASED && __('%'),
        },
      },

      [LEASE_TYPES]: {
        renderer: Radio,
        renderOptions: {
          fieldClassName: styles.lessMarginBottom,
          label: getLabelForLenderSetupForm(LEASE_TYPES),
          radios: [
            {
              label: __('Money Factor'),
              value: MARKUP_TYPES.MARKUP_BASED,
            },
            {
              label: __('Flat Amount'),
              value: MARKUP_TYPES.FLAT_AMOUNT,
            },
            {
              label: __('% of Amt. Financed'),
              value: MARKUP_TYPES.CAPCOST_BASED,
            },
          ],
        },
      },
      [DIGITAL_LEASE_TYPES]: {
        renderer: Radio,
        renderOptions: {
          fieldClassName: styles.lessMarginBottom,
          label: getLabelForLenderSetupForm(DIGITAL_LEASE_TYPES),
          radios: [
            {
              label: __('Money Factor'),
              value: MARKUP_TYPES.MARKUP_BASED,
            },
            {
              label: __('Flat Amount'),
              value: MARKUP_TYPES.FLAT_AMOUNT,
            },
            {
              label: __('% of Amt. Financed'),
              value: MARKUP_TYPES.CAPCOST_BASED,
            },
          ],
        },
      },

      [FINANCE_RESERVE_CAPCOST_BASED_LOAN]: {
        renderer: Radio,
        renderOptions: {
          label: getLabelForLenderSetupForm(FINANCE_RESERVE_CAPCOST_BASED_LOAN),
          fieldClassName: styles.breakRadioLabel,
          size: 6,
          radios: [
            {
              label: __('% of Amount Financed * Amount Financed'),
              value: FINANCE_RESERVE_CAP_COST_BASED_FORMULAS.FORMULA_1,
            },
            {
              label: __('% of Amount Financed * Amount Financed * Reserve Split %'),
              value: FINANCE_RESERVE_CAP_COST_BASED_FORMULAS.FORMULA_2,
            },
          ],
        },
      },

      [FINANCE_RESERVE_CAPCOST_BASED_LEASE]: {
        renderer: Radio,
        renderOptions: {
          label: getLabelForLenderSetupForm(FINANCE_RESERVE_CAPCOST_BASED_LEASE),
          fieldClassName: styles.breakRadioLabel,
          size: 6,
          radios: [
            {
              label: __('% of Amount Financed * Amount Financed'),
              value: FINANCE_RESERVE_CAP_COST_BASED_FORMULAS.FORMULA_1,
            },
            {
              label: __('% of Amount Financed * Amount Financed * Reserve Split %'),
              value: FINANCE_RESERVE_CAP_COST_BASED_FORMULAS.FORMULA_2,
            },
          ],
        },
      },

      [FINANCE_RESERVE_MARKUP_BASED_LOAN]: {
        renderer: Radio,
        renderOptions: {
          label: getLabelForLenderSetupForm(FINANCE_RESERVE_MARKUP_BASED_LOAN),
          fieldClassName: styles.breakRadioLabel,
          size: 6,
          radios: [
            {
              label: __('(Finance Charge based on Sell Rate - Finance Charge based on Buy Rate) * Reserve Split %'),
              value: FINANCE_RESERVE_MARKUP_BASED_FORMULAS.FORMULA_1,
            },
            {
              label: __('{[(Sell Rate - Buy Rate)/Sell Rate] * Finance Charge based on Sell Rate * Reserve Split % }'),
              value: FINANCE_RESERVE_MARKUP_BASED_FORMULAS.FORMULA_2,
            },
          ],
        },
      },

      [FINANCE_RESERVE_MARKUP_BASED_LEASE]: {
        renderer: InfoField,
        renderOptions: {
          label: getLabelForLenderSetupForm(FINANCE_RESERVE_MARKUP_BASED_LEASE),
          helpText: __('Markup% / 2400 (ex. 1% is 0.00042)'),
        },
      },

      [FINANCE_RESERVE_FLAT_AMOUNT_LOAN]: {
        renderer: InfoField,
        renderOptions: {
          label: getLabelForLenderSetupForm(FINANCE_RESERVE_FLAT_AMOUNT_LOAN),
          helpText: __('Finance Reserve = Flat Amount'),
        },
      },

      [FINANCE_RESERVE_FLAT_AMOUNT_LEASE]: {
        renderer: InfoField,
        renderOptions: {
          label: getLabelForLenderSetupForm(FINANCE_RESERVE_FLAT_AMOUNT_LEASE),
          labelClassName: styles.labelClass,
          helpText: __('Finance Reserve = Flat Amount'),
        },
      },

      [FINANCE_RESERVE_SPLIT_PERCENTAGE]: {
        renderer: NumberInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(FINANCE_RESERVE_SPLIT_PERCENTAGE),
          id: FINANCE_RESERVE_SPLIT_PERCENTAGE,
          fieldClassName: styles.fixwidth_column,
          labelClassName: styles.labelClass,
          addonAfter: '%',
        },
      },

      // [ENABLE_TAX_FINANCE_CHARGE]: {
      //   renderer: withHelperText(Checkbox),
      //   renderOptions: {
      //     label: __('Use Other Finance Reserve Formula'),
      //     fieldClassName: styles.fixwidth_column,
      //     helperText: __('Finance Reserve = {[(Sell Rate - Buy Rate)/Sell Rate] * Finance Charge based on Sell Rate * Reserve Split % }'),
      //   },
      // },

      [FINANCE_RESERVE]: {
        renderer: CurrencyInput,
        renderOptions: {
          fieldClassName: styles.fixwidth_column,
          label: getLabelForLenderSetupForm(FINANCE_RESERVE),
          id: FINANCE_RESERVE,
          enforcePrecision: false,
        },
      },

      [LIENHOLDER_ADDRESS]: {
        renderer: AddressField,
        renderOptions: {
          label: getLabelForLenderSetupForm(LIENHOLDER_ADDRESS),
        },
      },

      [LIENHOLDER_ADDRESS2]: {
        renderer: TextInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(LIENHOLDER_ADDRESS2),
        },
      },
      [LIENHOLDER_STATE]: {
        renderer: TextInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(LIENHOLDER_STATE) + __(' $$(State)'),
          validators: [stateConstraint],
          helpText: HELP_DESCRIPTION_FOR_STATE_FIELD,
          infoPlacement: 'top',
        },
      },
      [LIENHOLDER_CITY]: {
        renderer: TextInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(LIENHOLDER_CITY),
        },
      },
      [LIENHOLDER_ZIP_CODE]: {
        renderer: TextInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(LIENHOLDER_ZIP_CODE) + __(' $$(ZIP Code)'),
          fieldClassName: styles.fixwidth_column,
        },
      },
      [LIENHOLDER_PHONE_NUMBER]: {
        renderer: PhoneNumberInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(LIENHOLDER_PHONE_NUMBER),
          validators: [makePhoneNumberValidator(getCountOfPhoneNumberDigits)],
          fieldClassName: styles.fixwidth_column,
          id: LIENHOLDER_PHONE_NUMBER,
        },
      },

      [LINEHOLDER_COUNTY]: {
        renderer: TextInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(LINEHOLDER_COUNTY) + __(' $$(County)'),
          validators: [], // TODO
          fieldClassName: styles.fixwidth_column,
        },
      },

      [LINEHOLDER_COUNTRY]: {
        renderer: TextInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(LINEHOLDER_COUNTRY) + __(' $$(Country)'),
          validators: [], // TODO
          fieldClassName: styles.fixwidth_column,
        },
      },
      [REMOVE_DEMO_MILEAGE]: {
        renderer: Checkbox,
        renderOptions: {
          label: __('Enable Remove Demo $$(Mileage) From Annual $$(Mileage)'),
          fieldClassName: styles.fixwidth_column,
          labelClassName: styles.labelClass,
          fieldLabelClassName: styles.checkboxLabelclass,
        },
      },

      [REMOVE_DEMO_MILEAGE_DECIDER_VALUE]: {
        renderer: NumberInput,
        renderOptions: {
          label: __('Remove Demo $$(Mileage) From Annual $$(Mileage) Decider Value'),
          id: REMOVE_DEMO_MILEAGE_DECIDER_VALUE,
          fieldClassName: styles.fixwidth_column,
          labelClassName: styles.labelClass,
          disabled: !removeDemoMileageFromAnnualMileage,
        },
      },

      [VEHICLE_MILEAGE_CUT_OFF_REMOVE_DEMO_MILEAGE]: {
        renderer: NumberInput,
        renderOptions: {
          label: __('Remove Demo $$(Mileage) From Annual $$(Mileage) Decider Value(Vehicle $$(Mileage))'),
          id: VEHICLE_MILEAGE_CUT_OFF_REMOVE_DEMO_MILEAGE,
          helpText: __(
            'Enter the threshold Value for Vehicle Mileage adjustment in Residual (Adj Residual = Residual - Vehicle Mileage* Penalty Per Mile). If left empty or 0 is entered, 1000 will be used by default'
          ),
          labelClassName: styles.labelClassWithInfo,
          infoPlacement: 'top',
          disabled: !removeDemoMileageFromAnnualMileage,
        },
      },

      [PERSONAL_PROPERTY_TAX_LENDER_TYPE]: {
        renderer: Select,
        renderOptions: {
          label: getLabelForLenderSetupForm(PERSONAL_PROPERTY_TAX_LENDER_TYPE),
          options: propertyTaxLenderTypeOptions,
          // showSearch: true,
          // filterOption: true,
          allowClear: false,
        },
      },

      [FIRST_PAYMENT_WAIVED_DEFAULT_VALUE]: {
        renderer: Checkbox,
        renderOptions: {
          label: getLabelForLenderSetupForm(FIRST_PAYMENT_WAIVED_DEFAULT_VALUE),
          fieldClassName: styles.fixwidth_column,
        },
      },

      [DEALER_TYPE]: {
        renderer: Select,
        renderOptions: {
          label: getLabelForLenderSetupForm(DEALER_TYPE),
          options: DEALER_TYPE_OPTIONS,
          allowClear: false,
          defaultValue: 'NONE',
        },
      },

      [COLLECT_SECURITY_DEPOSIT_WAIVER_REASON]: {
        renderer: Checkbox,
        renderOptions: {
          label: getLabelForLenderSetupForm(COLLECT_SECURITY_DEPOSIT_WAIVER_REASON),
          fieldClassName: styles.fixwidth_column,
          labelClassName: styles.labelClass,
        },
      },

      [REMOVE_DEMO_MILEAGE_FROM_ANNUAL_MILAGE_DISPLAY_NAME]: {
        renderer: TextInput,
        renderOptions: {
          label: __('Remove Demo $$(Mileage) From Annual $$(Mileage) Display Name'),
          labelClassName: styles.labelClass,
          fieldClassName: styles.fixwidth_column,
        },
      },

      [FREE_MILES_FOR_RESIDUAL_ADJUSTMENT]: {
        renderer: TextInput,
        renderOptions: {
          label: __('Free $$(Miles) for Residual Adjustment'),
          fieldClassName: styles.fixwidth_column,
          labelClassName: styles.labelClass,
        },
      },

      [ONE_PAY_TWO_DIGITS_AFTER_COMMA_MONTHLY_PAYMENT]: {
        renderer: CheckBoxWithInfo,
        renderOptions: {
          label: getLabelForLenderSetupForm(ONE_PAY_TWO_DIGITS_AFTER_COMMA_MONTHLY_PAYMENT),
          toolTipTitle: __('Select to round monthly calculated payment to two digits when dividing payment by term.'),
          icon: 'icon-info',
          iconPosition: POSITIONS.RIGHT,
          labelClassName: styles.labelClass,
          fieldClassName: styles.fixwidth_column,
          iconClassName: styles.onePayIcon,
        },
      },

      [ONE_PAY_APR_MONTHLY_LEASE_CHARGE_CALCULATION]: {
        renderer: CheckBoxWithInfo,
        renderOptions: {
          label: getLabelForLenderSetupForm(ONE_PAY_APR_MONTHLY_LEASE_CHARGE_CALCULATION),
          toolTipTitle: __('Enabling this will use PMT to calculate finance charge'),
          icon: 'icon-info',
          iconPosition: POSITIONS.RIGHT,
          labelClassName: styles.labelClass,
          fieldClassName: styles.fixwidth_column,
          iconClassName: styles.onePayIcon,
        },
      },

      [INCLUDE_SECURITY_DEPOSITS]: {
        renderer: Checkbox,
        renderOptions: {
          label: getLabelForLenderSetupForm(INCLUDE_SECURITY_DEPOSITS),
          fieldClassName: styles.fixwidth_column,
        },
      },

      [LIEN_HOLDER_LENDER]: {
        renderer: Checkbox,
        renderOptions: {
          label: getLabelForLenderSetupForm(LIEN_HOLDER_LENDER),
          fieldClassName: styles.fixwidth_column,
        },
      },

      [DISPOSITION_FEE_DEFAULTS_TO_MONTHLY_PAYMENT]: {
        renderer: Checkbox,
        renderOptions: {
          label: getLabelForLenderSetupForm(DISPOSITION_FEE_DEFAULTS_TO_MONTHLY_PAYMENT),
          labelClassName: styles.labelClass,
          fieldClassName: styles.fixwidth_column,
        },
      },

      [THRESHOLD_VALUE_FOR_DISPOSITION_FEE]: {
        renderer: NumberInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(THRESHOLD_VALUE_FOR_DISPOSITION_FEE),
          id: THRESHOLD_VALUE_FOR_DISPOSITION_FEE,
          fieldClassName: styles.fixwidth_column,
          labelClassName: styles.labelClass,
          validators: [nonNegativeRule, isIntegerRule],
        },
      },
      [THRESHOLD_VALUE_FOR_TERMINATION_FEE]: {
        renderer: NumberInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(THRESHOLD_VALUE_FOR_TERMINATION_FEE),
          id: THRESHOLD_VALUE_FOR_TERMINATION_FEE,
          fieldClassName: styles.fixwidth_column,
          labelClassName: styles.labelClass,
          validators: [nonNegativeRule, isIntegerRule],
        },
      },
      [ACQUISITION_FEE_MARKUP]: {
        renderer: CustomCurrencyInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(ACQUISITION_FEE_MARKUP),
          id: ACQUISITION_FEE_MARKUP,
          fieldClassName: styles.fixwidth_column,
          helpText: `Maximum markup supported is ${currencySymbol}${acquisitionFeeMaxMarkup}`,
          infoPlacement: 'top',
          showCurrencyInput: true,
          disabled: isGMLender(code),
        },
      },
      [ACQUISITION_FEE]: {
        renderer: CustomCurrencyInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(ACQUISITION_FEE),
          id: ACQUISITION_FEE,
          fieldClassName: styles.fixwidth_column,
          showCurrencyInput: true,
          helpText: __('Default Acquisition Fee when no lender programs are returned'),
          helpTextClassName: 'p-2',
          infoPlacement: 'top',
          disabled: isGMLender(code),
        },
      },

      [PENALTY_PER_MILE]: {
        renderer: CurrencyInput,
        renderOptions: {
          label: __('Additional $$(Mileage) Rate'),
          id: PENALTY_PER_MILE,
          fieldClassName: styles.fixwidth_column,
          enforcePrecision: false,
        },
      },

      [ACTUAL_PENALTY_PER_MILE]: {
        renderer: CurrencyInput,
        renderOptions: {
          label: __('Penalty/{{Mile}}', { Mile: mileageLabeler.getMileLabel() }),
          id: ACTUAL_PENALTY_PER_MILE,
          fieldClassName: styles.fixwidth_column,
          enforcePrecision: false,
          precision: 2,
        },
      },

      [BANK_FEE_UPFRONT]: {
        renderer: Select,
        renderOptions: {
          size: 6,
          label: getLabelForLenderSetupForm(BANK_FEE_UPFRONT),
          options: bankFeeUpfrontOptions,
          allowClear: false,
        },
      },

      [LIEN_PAY_OFF_ADDRESS]: {
        renderer: AddressField,
        renderOptions: {
          label: getLabelForLenderSetupForm(LIEN_PAY_OFF_ADDRESS),
        },
      },

      [LIEN_PAY_OFF_ADDRESS2]: {
        renderer: TextInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(LIEN_PAY_OFF_ADDRESS2),
        },
      },

      [LIEN_PAY_OFF_STATE]: {
        renderer: TextInput,
        renderOptions: {
          label: __('$$(State)'),
          validators: [stateConstraint],
          helpText: HELP_DESCRIPTION_FOR_STATE_FIELD,
          infoPlacement: 'top',
        },
      },

      [LIEN_PAY_OFF_CITY]: {
        renderer: TextInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(LIEN_PAY_OFF_CITY),
        },
      },

      [LIEN_PAY_OFF_COUNTY]: {
        renderer: TextInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(LIEN_PAY_OFF_COUNTY),
          labelClassName: styles.labelClass,
        },
      },

      [LIEN_PAY_OFF_COUNTRY]: {
        renderer: TextInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(LIEN_PAY_OFF_COUNTRY),
          labelClassName: styles.labelClass,
        },
      },

      [LIEN_PAY_OFF_ZIP]: {
        renderer: TextInput,
        renderOptions: {
          label: __('$$(ZIP Code)'),
          labelClassName: styles.labelClass,
          fieldClassName: styles.fixwidth_column,
        },
      },

      [LIEN_PAY_OFF_PH_NUM]: {
        renderer: PhoneNumberInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(LIEN_PAY_OFF_PH_NUM),
          validators: [makePhoneNumberValidator(getCountOfPhoneNumberDigits)],
          fieldClassName: styles.fixwidth_column,
          id: LIEN_PAY_OFF_PH_NUM,
        },
      },

      [FEIN_NUMBER]: {
        renderer: TextInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(FEIN_NUMBER),
          fieldClassName: styles.fixwidth_column,
        },
      },

      [LIEN_PAY_OFF_ADDRESS_SAME_AS_LIEN_HOLDER_ADDRESS]: {
        renderer: Radio,
        renderOptions: {
          radios: [
            {
              label: __('Same as Lienholder'),
              value: true,
            },
            {
              label: __('Other'),
              value: false,
            },
          ],
        },
      },

      [ROUND_OFF_MILEAGE_TO_NEAREST_THOUSAND]: {
        renderer: Checkbox,
        renderOptions: {
          label: __('Round off Annual $$(Mileage) to the nearest thousand'),
          labelClassName: styles.labelClass,
          fieldClassName: styles.fixwidth_column,
        },
      },

      [EVAULTING_REQUIRED]: {
        renderer: Checkbox,
        renderOptions: {
          label: getLabelForLenderSetupForm(EVAULTING_REQUIRED),
          fieldClassName: styles.fixwidth_column,
          disabled: LENDER_CODES_FOR_EVAULT_REQUIRED_DISABLED_AND_TRUE.includes(codeName),
        },
      },

      [TOTAL_SECURITY_DEPOSITS]: {
        renderer: NumberInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(TOTAL_SECURITY_DEPOSITS),
          id: TOTAL_SECURITY_DEPOSITS,
          fieldClassName: styles.fixwidth_column,
          labelClassName: styles.labelClass,
          validators: [nonNegativeRule, isIntegerRule],
        },
      },

      [ROUND_SECURITY_DEPOSIT_TO]: {
        renderer: NumberInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(ROUND_SECURITY_DEPOSIT_TO),
          id: ROUND_SECURITY_DEPOSIT_TO,
          fieldClassName: styles.fixwidth_column,
          labelClassName: styles.labelClass,
          validators: [nonNegativeRule, isIntegerRule],
        },
      },

      [TYPE_OF_SECURITY_DEPOSIT]: {
        renderer: Select,
        renderOptions: {
          label: getLabelForLenderSetupForm(TYPE_OF_SECURITY_DEPOSIT),
          fieldClassName: styles.fixwidth_column,
          options: SECURITY_DEPOSIT_ROUNDING_STRATEGY_OPTIONS,
        },
      },

      [APPLY_TAX_EXEMPT_FOR_USE_TAX]: {
        renderer: Checkbox,
        renderOptions: {
          label: getLabelForLenderSetupForm(APPLY_TAX_EXEMPT_FOR_USE_TAX),
          labelClassName: styles.labelClass,
          fieldClassName: styles.fixwidth_column,
        },
      },

      [EXCLUDE_FNI_PRODUCTS_IN_USE_TAX_CALCULATION]: {
        renderer: Checkbox,
        renderOptions: {
          label: getLabelForLenderSetupForm(EXCLUDE_FNI_PRODUCTS_IN_USE_TAX_CALCULATION),
          labelClassName: styles.labelClass,
          fieldClassName: styles.fixwidth_column,
        },
      },

      [TRADE_IN_TAX_CREDIT_TO_LEASE]: {
        renderer: Select,
        renderOptions: {
          label: getLabelForLenderSetupForm(TRADE_IN_TAX_CREDIT_TO_LEASE),
          options: TRADE_IN_TAX_CREDIT_TO_LEASE_OPTIONS,
          fieldClassName: styles.fixwidth_column,
          dropdownMatchSelectWidth: false,
          defaultValue: 'APPLY_CREDIT_THAT_INCLUDES_PAYMENT_WITH_PARTIAL_MUT',
        },
      },
      [INCLUDE_TAXABLE_FNI_PRODUCTS_IN_MUT]: {
        renderer: Checkbox,
        renderOptions: {
          label: getLabelForLenderSetupForm(INCLUDE_TAXABLE_FNI_PRODUCTS_IN_MUT),
          labelClassName: styles.labelClass,
          fieldClassName: styles.fixwidth_column,
        },
      },
      [REMOVE_NONTAX_FNI_PRODUCT_FROM_USE_TAX]: {
        renderer: Checkbox,
        renderOptions: {
          label: getLabelForLenderSetupForm(REMOVE_NONTAX_FNI_PRODUCT_FROM_USE_TAX),
          labelClassName: styles.labelClass,
          fieldClassName: styles.fixwidth_column,
        },
      },

      [ADD_VEHICLE_MILES_TO_ANNUAL_MILES]: {
        renderer: Checkbox,
        renderOptions: {
          label: __('Add Demo {{miles}} to Annual {{miles}}', {
            miles: mileageLabeler.getMilesLabel(),
          }),
          fieldClassName: styles.fixwidth_column,
        },
      },
      [PAYMENT_DUE_DATE_ADJUSTMENT]: {
        renderer: Checkbox,
        renderOptions: {
          label: getLabelForLenderSetupForm(PAYMENT_DUE_DATE_ADJUSTMENT),
          fieldClassName: styles.fixwidth_column,
        },
      },
      [ODD_DAYS_BASED_ON_DAYS_PER_MONTH]: {
        renderer: CheckBoxWithInfo,
        renderOptions: {
          label: getLabelForLenderSetupForm(ODD_DAYS_BASED_ON_DAYS_PER_MONTH),
          toolTipTitle: __(
            'Select to take into account the number of days in the current month when calculating odd days interest. This property only applies to the month of February, since it is the only month with fewer than 30 days.'
          ),
          icon: 'icon-info',
          iconPosition: POSITIONS.RIGHT,
          fieldClassName: styles.fixwidth_column,
          iconClassName: styles.onePayIcon,
        },
      },
      [DAYS_PER_YEAR_FOR_ODD_DAYS_INTEREST]: {
        renderer: Select,
        renderOptions: {
          label: getLabelForLenderSetupForm(DAYS_PER_YEAR_FOR_ODD_DAYS_INTEREST),
          fieldClassName: styles.fixwidth_column,
          labelClassName: styles.labelClass,
          options: DAYS_PER_YEAR_FOR_ODD_DAYS_INTEREST_OPTIONS,
          helpText: __('Days to add for odd days interest calculation.'),
          allowClear: true,
        },
      },

      [VEHICLE_CATEGORY]: {
        renderer: MultiSelect,
        renderOptions: {
          label: getLabelForLenderSetupForm(VEHICLE_CATEGORY),
          options: getVehicleCategoryOptions(),
          values: supportedVehicleCategory,
          fieldClassName: styles.fixwidth_column,
          allowClear: true,
        },
      },
      [ANNUITY_DUE]: {
        renderer: CheckBoxWithInfo,
        renderOptions: {
          label: getLabelForLenderSetupForm(ANNUITY_DUE),
          toolTipTitle: __(
            'This is used for non-standard In-house leasing where the calculation will be based on Annuity Due'
          ),
          icon: 'icon-info',
          iconPosition: POSITIONS.RIGHT,
          labelClassName: styles.labelClass,
          fieldClassName: styles.fixwidth_column,
          iconClassName: styles.onePayIcon,
        },
      },
      [USE_DECIMAL_PAYMENT_CALC]: {
        renderer: Checkbox,
        renderOptions: {
          label: __('Lease Decimal Calculation.', {
            miles: mileageLabeler.getMilesLabel(),
          }),
          fieldClassName: styles.fixwidth_column,
          labelClassName: styles.labelClass,
        },
      },
      [ITEMIZE_CCR_FOR_TAXATION]: {
        renderer: CheckBoxWithInfo,
        renderOptions: {
          label: getLabelForLenderSetupForm(ITEMIZE_CCR_FOR_TAXATION),
          toolTipTitle: __(' When selected, CCR Tax will be calculated without itemising the CCR.'),
          icon: 'icon-info',
          iconPosition: POSITIONS.RIGHT,
          labelClassName: styles.labelClass,
          fieldClassName: styles.fixwidth_column,
          iconClassName: styles.onePayIcon,
        },
      },
      [ENABLE_YEARLY_MILES_FOR_BALLOON]: {
        renderer: Checkbox,
        renderOptions: {
          label: __('Enable Yearly {{miles}} for Balloon Payment', {
            miles: mileageLabeler.getMilesLabel(),
          }),
          fieldClassName: styles.fixwidth_column,
          labelClassName: styles.labelClass,
        },
      },

      [OPTION_CONTRACT_VALIDITY_IN_CALENDAR_DAYS]: {
        renderer: NumberInput,
        renderOptions: {
          label: getLabelForLenderSetupForm(OPTION_CONTRACT_VALIDITY_IN_CALENDAR_DAYS),
          id: OPTION_CONTRACT_VALIDITY_IN_CALENDAR_DAYS,
          fieldClassName: styles.fixwidth_column,
          labelClassName: styles.labelClass,
          validators: [nonNegativeRule, isIntegerRule],
        },
      },
    };
  }
);

export const FORM_FIELD_FORMATTERS = {
  [PHONE]: formatPhoneFields,
  [LIENHOLDER_PHONE_NUMBER]: formatPhoneFields,
};
