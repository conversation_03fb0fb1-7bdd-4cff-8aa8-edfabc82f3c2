import { Http } from 'services/http';
import MandatoryAndReviewFormApi, { API_PATHS } from '../mandatoryAndReviewForm.api';

// Mock the Http service
jest.mock('services/http', () => ({
  Http: {
    get: jest.fn(),
  },
}));

describe('MandatoryAndReviewFormApi', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getPrefixOptions', () => {
    const mockResponse = {
      response: {
        dealerId: 'dealer-123',
        ledgers: [
          { id: 'PREFIX1', displayName: 'Prefix One' },
          { id: 'PREFIX2', displayName: 'Prefix Two' },
        ],
        separator: ' - ',
        tenantId: 'tenant-123',
      },
    };

    it('should call Http.get with correct URL', async () => {
      Http.get.mockResolvedValue(mockResponse);

      await MandatoryAndReviewFormApi.getPrefixOptions();

      expect(Http.get).toHaveBeenCalledWith(API_PATHS.FETCH_PREFIX_OPTIONS);
      expect(Http.get).toHaveBeenCalledTimes(1);
    });

    it('should return the response from Http.get', async () => {
      Http.get.mockResolvedValue(mockResponse);

      const result = await MandatoryAndReviewFormApi.getPrefixOptions();

      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors gracefully', async () => {
      const apiError = new Error('API Error');
      Http.get.mockRejectedValue(apiError);

      await expect(MandatoryAndReviewFormApi.getPrefixOptions()).rejects.toThrow('API Error');
      expect(Http.get).toHaveBeenCalledWith(API_PATHS.FETCH_PREFIX_OPTIONS);
    });
  });

  describe('API_PATHS', () => {
    it('should have correct FETCH_PREFIX_OPTIONS path', () => {
      expect(API_PATHS.FETCH_PREFIX_OPTIONS).toBe('/retail/sales/u/acc-setups/ledger-prefix');
    });
  });
});
