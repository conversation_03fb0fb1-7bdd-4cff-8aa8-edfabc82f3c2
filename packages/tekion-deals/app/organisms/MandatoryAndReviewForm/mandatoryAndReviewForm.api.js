import { Http } from 'services/http';

import { DEAL_SERVICE } from '@tekion/tekion-base/constants/deal/endPoint';

export const API_PATHS = {
  FETCH_PREFIX_OPTIONS: `${DEAL_SERVICE}/u/acc-setups/ledger-prefix`,
};

/**
 * API class for Mandatory and Review Form related operations
 */
export default class MandatoryAndReviewFormApi {
  /**
   * Fetches prefix options for a specific dealer
   * @returns {Promise} Promise that resolves to prefix options response
   */
  static getPrefixOptions() {
    return Http.get(`${API_PATHS.FETCH_PREFIX_OPTIONS}`);
  }
}
