import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { defaultMemoize } from 'reselect';
import produce from 'immer';

import _noop from 'lodash/noop';
import _map from 'lodash/map';
import _get from 'lodash/get';
import _size from 'lodash/size';

import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { actionHandlers } from '@tekion/tekion-base/marketScan/constants/constants';
import { tget } from '@tekion/tekion-base/utils/general';
import { isRRG } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
import Button from '@tekion/tekion-components/src/atoms/Button';
import Heading from '@tekion/tekion-components/src/atoms/Heading';
import FormPage from '@tekion/tekion-components/src/pages/formPage';
import actionTypes from '@tekion/tekion-components/src/organisms/FormBuilder/constants/actionTypes';
import PropertControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import Address from '@tekion/tekion-widgets/src/organisms/address';
import { FEATURE_INTEGRATION_ENTITIES } from '@tekion/tekion-business/src/constants/featureConfig';

import ADDRESS_ACTION_HANDLER, { addAddressForm } from './AddressForm.actionHandlers';
import addressActionTypes from './AddressForm.actionTypes';
import { getFormSections, getFormFields } from './AddressForm.config';
import { FORM_FIELD } from './AddressForm.constants';
import { ADDRESS_TYPE, SAME_AS_CURRENT_ADDRESS_TYPE } from '../../CustomerForm.constants';
import { PERSONAL_ADDRESS_LAYOUT_BY_LOCALES } from './addressForm.layout';
import { makeAddressValue, getUpdatedAddressValue } from './addressForm.formValues';
import styles from '../../customerForm.module.scss';

class AddressForm extends PureComponent {
  makeAddressValue = defaultMemoize(makeAddressValue);

  addForm = () => {
    const { setLocalState, field, index, value = [] } = this.props;
    const isCurrentAddress = _get(value, [0, FORM_FIELD.ADDRESS_TYPE]) === ADDRESS_TYPE.CURRENT;

    addAddressForm({
      setState: setLocalState,
      field,
      index,
      isCurrentAddress,
    });
  };

  handleAction =
    (address, formValues) =>
    async ({ type, payload }) => {
      const {
        setLocalState,
        getLocalState,
        field,
        index,
        setCustomerFormValidationErrors,
        setRulesList,
        validateSingleFieldErrors,
        disabled
      } = this.props;
      const { id } = payload;
      const funcToExec = ADDRESS_ACTION_HANDLER[type] || _noop;
      if (type === actionTypes.ON_RULE_CHANGE) {
        setRulesList([index, field, address], payload.rules || {});
      }
      await funcToExec(
        payload,
        {
          setState: setLocalState,
          getState: getLocalState,
          field,
          index,
          address,
          formValues,
          disabled,
        },
        setCustomerFormValidationErrors
      );
      if (type === actionHandlers.PERIOD) {
        validateSingleFieldErrors(`${index}.${field}.${address}.${id}`, { index, field, pathObj: address, id });
      }
    };

  copyFromBuyer = () => {
    const { id, onAction } = this.props;
    onAction({
      type: addressActionTypes.COPY_FROM_BUYER,
      payload: {
        id,
        value: {},
      },
    });
  };

  handleAddressChange = (address, index) => newAddress => {
    const { setLocalState, field, index: formIndex } = this.props;
    const updatedAddress = getUpdatedAddressValue(address, newAddress);
    setLocalState(
      produce(draft => {
        draft.formValues[formIndex][field][index] = updatedAddress;
      })
    );
  };

  renderAddress = (address, index) => {
    const { dealerInfo } = this.props;
    const addressValue = this.makeAddressValue(address);
    return (
      <Address
        layoutByLocales={PERSONAL_ADDRESS_LAYOUT_BY_LOCALES}
        dealerConfig={dealerInfo}
        value={addressValue}
        onChange={this.handleAddressChange(address, index)}
        entityType={FEATURE_INTEGRATION_ENTITIES.CUSTOMER}
        className={styles.formSection}
        setDealerCountryAsDefault
      />
    );
  };

  renderForm = (address, index) => {
    const {
      showExtraFields,
      disabled,
      showMapPopup,
      showSameAsCurrentAddressFields,
      deleteReasonForCPRAGDPR,
      showMortageAmount,
      customerType,
      errors,
      field,
      index: customerIndex,
      chargeCustomer,
      isCurrentAddressAvailable,
    } = this.props;
    const isAddressSameAsCurrent =
      _get(address, FORM_FIELD.SAME_AS_CURRENT_ADDRESS) === SAME_AS_CURRENT_ADDRESS_TYPE.SAME_AS_CURRENT;
    return (
      <FormPage
        sections={getFormSections(
          showExtraFields,
          showSameAsCurrentAddressFields,
          isAddressSameAsCurrent,
          showMortageAmount
        )}
        fields={getFormFields({
          address,
          disabled,
          showMapPopup,
          customerType,
          deleteReasonForCPRAGDPR,
          chargeCustomer,
          isCurrentAddressAvailable,
        })}
        values={address}
        errors={tget(errors, [customerIndex, field], EMPTY_OBJECT)}
        headerComponent={null}
        footerComponent={null}
        onAction={this.handleAction(index, address)}
        contentHeight="auto"
      />
    );
  };

  render() {
    const { label, value = [], showAdd: showAddProp, showCopyFromBuyer, disabled } = this.props;
    const showAdd = showAddProp && value.length < 2;
    const isCurrentAddress = _get(value, [0, FORM_FIELD.ADDRESS_TYPE]) === ADDRESS_TYPE.CURRENT;
    const addressRenderer = isRRG() ? this.renderAddress : this.renderForm;
    return (
      <div>
        <PropertControlledComponent controllerProperty={!!_size(value)}>
          <div className="d-flex align-items-center">
            <Heading size={4}>{label}</Heading>
            <PropertControlledComponent controllerProperty={showCopyFromBuyer}>
              <Button view={Button.VIEW.TERTIARY} disabled={disabled} onClick={this.copyFromBuyer}>
                {__('Copy from Buyer')}
              </Button>
            </PropertControlledComponent>
          </div>
        </PropertControlledComponent>

        {_map(value, addressRenderer)}

        <PropertControlledComponent controllerProperty={showAdd && isCurrentAddress}>
          <Button view={Button.VIEW.TERTIARY} disabled={disabled} onClick={this.addForm}>
            {__('Add Previous Address')}
          </Button>
        </PropertControlledComponent>
        <PropertControlledComponent controllerProperty={showAdd && !isCurrentAddress}>
          <Button view={Button.VIEW.TERTIARY} disabled={disabled} onClick={this.addForm}>
            {__('Add Current Address')}
          </Button>
        </PropertControlledComponent>
      </div>
    );
  }
}

AddressForm.propTypes = {
  label: PropTypes.string,
  value: PropTypes.array,
  showAdd: PropTypes.bool,
  showMortageAmount: PropTypes.bool,
  showExtraFields: PropTypes.bool,
  setLocalState: PropTypes.func.isRequired,
  getLocalState: PropTypes.func.isRequired,
  field: PropTypes.func.isRequired,
  index: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
  showMapPopup: PropTypes.bool,
  showCopyFromBuyer: PropTypes.bool,
  showSameAsCurrentAddressFields: PropTypes.bool,
  deleteReasonForCPRAGDPR: PropTypes.string,
  customerType: PropTypes.any.isRequired,
  setRulesList: PropTypes.func,
  validateSingleFieldErrors: PropTypes.func,
  dealerInfo: PropTypes.object,
  isCurrentAddressAvailable: PropTypes.bool,
};

AddressForm.defaultProps = {
  label: '',
  value: EMPTY_ARRAY,
  showAdd: false,
  showMortageAmount: true,
  showExtraFields: false,
  disabled: false,
  showMapPopup: false,
  showCopyFromBuyer: false,
  showSameAsCurrentAddressFields: false,
  deleteReasonForCPRAGDPR: '',
  setRulesList: _noop,
  validateSingleFieldErrors: _noop,
  dealerInfo: EMPTY_OBJECT,
  isCurrentAddressAvailable: false,
};

export default AddressForm;
