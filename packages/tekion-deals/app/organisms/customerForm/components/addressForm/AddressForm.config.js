import React from 'react';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import { defaultMemoize } from 'reselect';

import { isInchcape, isRRG } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
import TextInput from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/textInput';
import CurrencyInput from '@tekion/tekion-widgets/src/fieldRenderers/currencyInputField';
import Radio from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/radio';
import Select from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/select';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';
import ZipCodeInput from '@tekion/tekion-widgets/src/appServices/sales/molecules/ZipCodeInput';
import City from '@tekion/tekion-widgets/src/organisms/sales/addressFields/City';
import { isRequiredRule } from '@tekion/tekion-base/utils/formValidators';

import * as DealerConfigReader from '../../../../utils/dealerConfig.reader';
import AddressWithCopyAction from '../../../AddressWithCopyAction';
import { CAPANCY_VALIDATION_TYPES } from '../applicantsDetailsForm/CapancyVerificationIndicator/CapancyVarificationIndicator.constants';
import CapencyVerificationIndicator from '../applicantsDetailsForm/CapancyVerificationIndicator/CapancyVerificationIndicator';
import { FORM_FIELD } from './AddressForm.constants';
import { MAX_LENGTHS, ADDRESS_TYPE, SAME_AS_CURRENT_ADDRESS_TYPE } from '../../CustomerForm.constants';
import { getResidentTypeOptions, validateYear } from '../../CustomerForm.utils';
import styles from '../../customerForm.module.scss';
import withCPRAGDPR from '../withCPRAGDPR';
import PeriodWithDuration from '../../../periodWithDuration/PeriodWithDuration';

const TextInputCPRAGDPR = withCPRAGDPR(TextInput);
const CurrencyInputCPRAGDPR = withCPRAGDPR(CurrencyInput);
const ZipCodeInputCPRAGDPR = withCPRAGDPR(ZipCodeInput);
const CityWithCPRAGDPR = withCPRAGDPR(City);

const SAME_AS_OTHER_ADDRESS_OPTIONS = (isCurrentAddressAvailable = false) => [
  {
    label: isInchcape() ? __('Same as Trading Address') : __('Same as Current Address'),
    value: SAME_AS_CURRENT_ADDRESS_TYPE.SAME_AS_CURRENT,
    ...(!isCurrentAddressAvailable
      ? {
          disabled: true,
          additional: {
            showTooltip: true,
            tooltipProps: { title: isInchcape() ? __('No Trading Address present') : __('No Current Address present') },
          },
        }
      : {}),
  },
  {
    label: __('Other'),
    value: SAME_AS_CURRENT_ADDRESS_TYPE.OTHER,
  },
];

const extraFields = [
  {
    columns: [FORM_FIELD.RESIDENT_TYPE, FORM_FIELD.ADDRESS_TYPE],
  },
];

const sameAsCurrentAddressFields = [
  {
    columns: [FORM_FIELD.SAME_AS_CURRENT_ADDRESS],
  },
];

const INCHCAPE_FIELDS = [
  { columns: [FORM_FIELD.ADDRESS] },
  { columns: [FORM_FIELD.ADDRESS2] },
  { columns: [FORM_FIELD.ADDRESS3] },
  { columns: [FORM_FIELD.ADDRESS4] },
  { columns: [FORM_FIELD.CITY, FORM_FIELD.COUNTY] },
  { columns: [FORM_FIELD.ZIP_CODE, FORM_FIELD.COUNTRY] },
];

const ADDRESS_FIELDS = showMortageAmount =>
  isInchcape()
    ? INCHCAPE_FIELDS
    : [
        {
          columns: [FORM_FIELD.ADDRESS],
        },
        ...(isRRG()
          ? [
              { columns: [FORM_FIELD.ADDRESS2] },
              { columns: [FORM_FIELD.ZIP_CODE, FORM_FIELD.CITY] },
              { columns: [FORM_FIELD.COUNTRY] },
              ...(DealerPropertyHelper.isCapencyVerificationRequired()
                ? [{ columns: [FORM_FIELD.CAPANCY_VERIFICATION_STATUS] }]
                : []),
            ]
          : [
              { columns: [FORM_FIELD.ADDRESS2, FORM_FIELD.PERIOD_OF_RESIDENCE] },
              { columns: [FORM_FIELD.ZIP_CODE, FORM_FIELD.CITY] },
              { columns: [FORM_FIELD.COUNTY, FORM_FIELD.STATE] },
              showMortageAmount
                ? { columns: [FORM_FIELD.COUNTRY, FORM_FIELD.MORTGAGE_AMOUNT] }
                : { columns: [FORM_FIELD.COUNTRY] },
            ]),
      ];

export const getFormSections = defaultMemoize(
  (showExtraFields, showSameAsCurrentAddressFields, isAddressSameAsCurrent, showMortageAmount) => [
    {
      className: styles.formSection,
      rows: [
        ...(showExtraFields ? extraFields : []),
        ...(showSameAsCurrentAddressFields ? sameAsCurrentAddressFields : []),
        ...(isAddressSameAsCurrent && showSameAsCurrentAddressFields ? [] : ADDRESS_FIELDS(showMortageAmount)),
      ],
    },
  ]
);

const getPlaceHolderValue = () => {
  if (isRRG()) {
    return __('Street Number, Street Name');
  }
  if (isInchcape()) {
    return __('Select Address');
  }
  return '';
};

const getCityRenderer = isBusinessApplication => {
  if (isInchcape()) {
    return TextInputCPRAGDPR;
  }
  if (isBusinessApplication) return City;
  return CityWithCPRAGDPR;
};

export const getFormFields = ({
  address,
  disabled,
  showMapPopup,
  customerType,
  deleteReasonForCPRAGDPR,
  chargeCustomer,
  isCurrentAddressAvailable,
}) => {
  const isBusinessApplication = customerType === 1;
  const periodOfResidenceDurationType = _get(address, FORM_FIELD.PERIOD_OF_RESIDENCE_DURATION_TYPE);
  const addressTypeDisabled = _get(address, 'disabled');
  const zipCode = _get(address, 'zipCode');
  const disableCondition = disabled || chargeCustomer;
  return {
    [FORM_FIELD.RESIDENT_TYPE]: {
      renderer: isBusinessApplication ? Select : withCPRAGDPR(Select),
      renderOptions: {
        label: __('Resident Type'),
        options: getResidentTypeOptions(),
        disabled,
        deleteReasonForCPRAGDPR,
      },
    },
    [FORM_FIELD.PERIOD_OF_RESIDENCE]: {
      renderer: PeriodWithDuration,
      renderOptions: {
        label: __('Period of Residence'),
        durationType: periodOfResidenceDurationType,
        size: 6,
        validators: [validateYear],
        disabled,
        isBusinessApplication,
        deleteReasonForCPRAGDPR,
      },
    },

    [FORM_FIELD.ADDRESS_TYPE]: {
      renderer: Radio,
      renderOptions: {
        radios: [
          {
            label: __('Current Address'),
            value: ADDRESS_TYPE.CURRENT,
          },
          {
            label: __('Previous Address'),
            value: ADDRESS_TYPE.PREVIOUS,
          },
        ],
        disabled: isBusinessApplication
          ? disabled || addressTypeDisabled
          : disabled || addressTypeDisabled || !_isEmpty(deleteReasonForCPRAGDPR),
      },
    },
    [FORM_FIELD.ADDRESS]: {
      renderer: isBusinessApplication && !isInchcape() ? AddressWithCopyAction : withCPRAGDPR(AddressWithCopyAction),
      renderOptions: {
        label: isRRG() || isInchcape() ? __('Address Line 1') : __('Address'),
        address,
        placeholder: getPlaceHolderValue(),
        disabled: isInchcape() ? disableCondition : disabled,
        showMapPopup,
        deleteReasonForCPRAGDPR,
      },
    },
    [FORM_FIELD.ADDRESS2]: {
      renderer: isBusinessApplication && !isInchcape() ? TextInput : TextInputCPRAGDPR,
      renderOptions: {
        label: isRRG() || isInchcape() ? __('Address Line 2') : __('Address 2'),
        placeholder: isRRG() && __('Building name, Floor, Apartment number'),
        disabled: isInchcape() ? disableCondition : disabled,
        deleteReasonForCPRAGDPR,
      },
    },
    [FORM_FIELD.ADDRESS3]: {
      renderer: isBusinessApplication && !isInchcape() ? TextInput : TextInputCPRAGDPR,
      renderOptions: {
        label: __('Address Line 3'),
        disabled: isInchcape() ? disableCondition : disabled,
        deleteReasonForCPRAGDPR,
      },
    },
    [FORM_FIELD.ADDRESS4]: {
      renderer: isBusinessApplication && !isInchcape() ? TextInput : TextInputCPRAGDPR,
      renderOptions: {
        label: __('Address Line 4'),
        disabled: isInchcape() ? disableCondition : disabled,
        deleteReasonForCPRAGDPR,
      },
    },
    [FORM_FIELD.MORTGAGE_AMOUNT]: {
      renderer: isBusinessApplication ? CurrencyInput : CurrencyInputCPRAGDPR,
      renderOptions: {
        label: __('Mortgage/Rent Amount'),
        id: FORM_FIELD.MORTGAGE_AMOUNT,
        enforcePrecision: false,
        size: 6,
        disabled,
        deleteReasonForCPRAGDPR,
      },
    },
    [FORM_FIELD.ZIP_CODE]: {
      renderer: isBusinessApplication && !isInchcape() ? ZipCodeInput : ZipCodeInputCPRAGDPR,
      renderOptions: {
        label: __('$$(ZIP Code)'),
        placeholder: isRRG() && __('Type in postal Code'),
        disabled: isInchcape() ? disableCondition : disabled,
        deleteReasonForCPRAGDPR,
        enableZipcodeRefresh: !isRRG(),
        region: DealerConfigReader.getDealerCountryCode(),
        ...(DealerPropertyHelper.isAECProgram() ? { required: true, validators: [isRequiredRule] } : {}),
      },
    },
    [FORM_FIELD.CITY]: {
      renderer: getCityRenderer(isBusinessApplication),
      renderOptions: {
        label: __('City'),
        placeholder: isRRG() && __('Type in city name'),
        disabled: isInchcape() ? disableCondition : disabled,
        deleteReasonForCPRAGDPR,
        region: DealerConfigReader.getDealerCountryCode(),
        zipCode,
      },
    },
    [FORM_FIELD.COUNTRY]: {
      renderer: isBusinessApplication && !isInchcape() ? TextInput : TextInputCPRAGDPR,
      renderOptions: {
        label: __('Country'),
        disabled: isInchcape() ? disableCondition : disabled,
        size: 6,
        deleteReasonForCPRAGDPR,
        ...(DealerPropertyHelper.isAECProgram() ? { required: true, validators: [isRequiredRule] } : {}),
      },
    },
    [FORM_FIELD.COUNTY]: {
      renderer: isBusinessApplication && !isInchcape() ? TextInput : TextInputCPRAGDPR,
      renderOptions: {
        label: __('County'),
        disabled: isInchcape() ? disableCondition : disabled,
        deleteReasonForCPRAGDPR,
      },
    },
    [FORM_FIELD.STATE]: {
      renderer: isBusinessApplication ? TextInput : TextInputCPRAGDPR,
      renderOptions: {
        label: __('$$(State)'),
        maxLength: MAX_LENGTHS.STATE,
        disabled,
        deleteReasonForCPRAGDPR,
        ...(DealerPropertyHelper.isAECProgram() ? { required: true, validators: [isRequiredRule] } : {}),
      },
    },
    [FORM_FIELD.SAME_AS_CURRENT_ADDRESS]: {
      renderer: Radio,
      renderOptions: {
        radios: SAME_AS_OTHER_ADDRESS_OPTIONS(isCurrentAddressAvailable), // if address to be copied is available
        disabled:
          isBusinessApplication && !isInchcape() ? disabled : disableCondition || !_isEmpty(deleteReasonForCPRAGDPR),
      },
    },

    [FORM_FIELD.CAPANCY_VERIFICATION_STATUS]: {
      id: FORM_FIELD.CAPANCY_VERIFICATION_STATUS,
      renderer: ({ value }) => (
        <div className="margin-bottom-10">
          <CapencyVerificationIndicator isVerified={value} validationType={CAPANCY_VALIDATION_TYPES.ADDRESS} />
        </div>
      ),
    },
  };
};
