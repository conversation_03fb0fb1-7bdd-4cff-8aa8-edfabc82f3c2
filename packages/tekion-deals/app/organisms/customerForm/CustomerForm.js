import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import produce from 'immer';
import cx from 'classnames';
import { connect } from 'react-redux';
import { compose } from 'recompose';

import _noop from 'lodash/noop';
import _get from 'lodash/get';
import _findIndex from 'lodash/findIndex';
import _set from 'lodash/set';
import _has from 'lodash/has';
import _map from 'lodash/map';
import _isEmpty from 'lodash/isEmpty';
import _isEqual from 'lodash/isEqual';
import _reduce from 'lodash/reduce';
import _countBy from 'lodash/countBy';
import _isNil from 'lodash/isNil';
import _toUpper from 'lodash/toUpper';
import _keys from 'lodash/keys';
import _castArray from 'lodash/castArray';
import _keyBy from 'lodash/keyBy';
import _head from 'lodash/head';
import _find from 'lodash/find';
import _uniq from 'lodash/uniq';
import __compact from 'lodash/compact';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING, NO_DATA } from '@tekion/tekion-base/app.constants';
import Money from '@tekion/tekion-base/utils/money';
import getArraySafeValue from '@tekion/tekion-base/utils/getArraySafeValue';
import * as Validators from '@tekion/tekion-base/utils/validation';
import { getBuyer, getDealNumber } from '@tekion/tekion-base/marketScan/readers/deal.reader';
import FontIcon, { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';
import Heading from '@tekion/tekion-components/src/atoms/Heading';
import Content from '@tekion/tekion-components/src/atoms/Content';
import Checkbox from '@tekion/tekion-components/src/atoms/checkbox';
import Select from '@tekion/tekion-components/src/molecules/Select';
import FormPage from '@tekion/tekion-components/src/pages/formPage';
import Button from '@tekion/tekion-components/src/atoms/Button';
import Tabs from '@tekion/tekion-components/src/molecules/Tabs';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';
import Popover from '@tekion/tekion-components/src/molecules/popover';
import { getCustomerName } from '@tekion/tekion-base/marketScan/readers/customer.reader';
import { redirectToLeadDetails } from 'tbusiness/appServices/crm/helpers/routes';
import * as FieldSetupApi from '@tekion/tekion-base/services/fieldSetup.service';
import { isInchcape, isRRG, isInchcapeOrRRG } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
import withDealerInfo from '@tekion/tekion-components/src/hoc/withDealerMaster/withDealerMaster';
import { TOASTER_TYPE, toaster } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import CrmCustomerReader from '@tekion/tekion-base/readers/CrmCustomer';
import { findBuyer, getCoBuyer } from '@tekion/tekion-base/marketScan/utils/deal.util';
import { DEAL_TYPES } from '@tekion/tekion-base/constants/deal/type';

import StickyBanner, { BANNER_TYPE, STICKY_BANNER } from '@tekion/tekion-components/src/molecules/stickyBanner';
import { Tag } from '@tekion/tekion-components/src/atoms';
import Label from '@tekion/tekion-components/src/atoms/Label';
import ContactDetailsModal from 'organisms/contactDetailsModal';
import DropDown from '@tekion/tekion-components/src/molecules/DropDown';
import Menu from '@tekion/tekion-components/src/molecules/Menu';
import { tget } from '@tekion/tekion-base/utils/general';
import { STATUS } from '@tekion/tekion-base/constants/status.constants';
import { DEPARTMENTS } from '@tekion/tekion-base/constants/crm';
import DeskingAPI from 'pages/desking/desking.api';

import CMSAPI from 'commonActions/apis/cms.api';
import CustomerAPI from 'commonActions/apis/customer.api';
import { getUnformattedPhoneNumber, getPhoneNumberValue, formatCurrencyWithMoneyFormat } from 'utils';
import { BUYER_TYPE, PROGRAM_CODE, CUSTOMER_TYPE } from 'pages/deallist/deal.constants';
import NO_BUYER from 'assets/images/nuBuyer.png';
import NO_CO_BUYER from 'assets/images/noCoBuyer.png';
import { isArcLiteProgram } from 'utils/program.utils';
import { getAPIError } from 'utils/error.reader';
import { MODAL_TYPE, showGlobalModal } from '@tekion/tekion-components/src/emitters/ModalEventEmitter';
import isCRMMultiOEMSwitchEnabled from '@tekion/tekion-components/src/helpers/crm/isCRMMultiOEMSwitchEnabled';
import { DEFAULT_PAGE_SIZE } from '@tekion/tekion-base/constants/tableConstants';
import { getIsProgramAcuraNewAndPrimaryVehicleNotZDX } from 'utils/deal.util';
import { SHARE_MESSAGE_TEMPLATE_TYPE } from '../../pages/desking/components/digitalRetail/digitalRetail.constants';

import ConfirmationModal from '../../molecules/ConfirmationModal';
import CUSTOMER_FORM_ACTION_HANDLER from './CustomerForm.actionHandlers';
import { getFormFields, getFormSections } from './CustomerForm.config';
import { getCustomerType, getBuyerType } from './customerFormReader';
import {
  getPayloadFromForm,
  getInitialGuarantorValue,
  isCRMFlow,
  isCustomerEmpty,
  currencyFormatter,
} from './CustomerForm.utils';
import {
  getContactDataFromCoBuyerForm,
  getContactPayloadFromCustomer,
  getCustomerDocumentsBasedOnAssetId,
  getPayloadForDeletingUploadedDocuments,
  getPayloadForFetchingUploadedDocuments,
  getUpdatedCustomerDocuments,
} from './customerForm.helpers';
import AddLeadButton from './components/addLeadButton';
import DuplicateAlerts from './DuplicateAlerts';
import CreditApplicationAlert from './CreditApplicationAlert';
import customerFormActionTypes from './customerForm.actionTypes';
import {
  CUSTOMER_TABS,
  TITLE_OPTIONS,
  CUSTOMER_TABS_KEYS,
  CUSTOMER_TABS_TITLE,
  TAB_HEIGHT_TABS_STYLE_CONFIG,
  CUSTOMER_TYPE_OPTIONS,
} from './tabBar.constants';
import {
  CRM_VS_DEAL_CUSTOMER_TYPES,
  CUSTOMER_DOCUMENT_PAYLOAD_KEYS,
  FORM_FIELD_TO_DOCUMENT_TYPES,
  CUSTOMER_DOCUMENT_ASSET_TYPES,
  ADDRESS_TYPE,
} from './CustomerForm.constants';
import ChargeCustomer from '../../pages/desking/headers/ChargeCustomer';
import { canEditChargeCustomerPII } from '../../permissions/desking.permissions';
import { getDealerIdFromState } from '../../pages/desking/components/Contracts/Contracts.readers';
import { BASE_REDUCER_KEY } from '../../constants/constants';
import { FORM_FIELD as ADDRESS_FORM_FIELD } from './components/addressForm/AddressForm.constants';

import styles from './customerForm.module.scss';
import { TOASTER_STATUS } from '../../constants/pages';
import {
  getCustomerFromContact,
  getPayloadToFetchContacts,
  getPayloadToFetchCustomers,
} from '../Contacts/helpers/contacts.helpers';
import ContactsActions from '../Contacts/helpers/contacts.actions';

class CustomerForm extends PureComponent {
  constructor(props) {
    super(props);
    const guarantor = getInitialGuarantorValue(props.customers);
    const { deal, customers } = this.props;
    const program = _get(deal, 'program');
    const ciamId = _get(customers, `${0}.ciamId`) || '';
    this.state = {
      guarantor,
      customerTypologies: [],
      openShareLinkDropDown: false,
      showShareLinkBanner:
        (_isEqual(program, PROGRAM_CODE.ACURA_CPO) || _isEqual(program, PROGRAM_CODE.ACURA_NEW)) && _isEmpty(ciamId),
      customerBuyerType: CUSTOMER_TYPE.BUYER,
      ciamId: ciamId || '',
      chargeCustomer: false,
      isNewCustomer: false,
      addLeadId: '',
      availableCreditLimit: null,
      fetchedCustomerDocuments: [],
      customerDocumentsLoading: [],
      isAddingCustomer: _isEmpty(deal) || _isEmpty(customers),
    };
  }

  async componentDidMount() {
    const { isAddingCustomer } = this.state;

    if (!isAddingCustomer && DealerPropertyHelper.isUSUploadDocSyncCrm()) {
      this.handleToggleMediaLoading(_keys(FORM_FIELD_TO_DOCUMENT_TYPES));
      await this.fetchDealCustomerDocuments();
      this.handleToggleMediaLoading(_keys(FORM_FIELD_TO_DOCUMENT_TYPES));
    }

    if (isRRG()) {
      this.getCustomerTypologies();
      this.getPaymentMethodOptions();
      this.getDayOfCollectionOptions();
    }
    if (isInchcape()) {
      this.fetchCreditBalance();
    }
  }

  componentDidUpdate(_, prevState) {
    const { blockScreen = _noop, unBlockScreen = _noop } = this.props;
    const { customerDocumentsLoading } = this.state;

    if (!_isEmpty(customerDocumentsLoading) && _isEmpty(prevState?.customerDocumentsLoading)) {
      blockScreen(false, TOASTER_STATUS.INFO, __('Fetching customer documents...'));
    } else if (_isEmpty(customerDocumentsLoading) && !_isEmpty(prevState?.customerDocumentsLoading)) {
      unBlockScreen();
    }
  }

  async componentWillUnmount() {
    const { saveForm, saveOnUnmount, formValues = EMPTY_ARRAY, formAttr, makeCoBuyerAsBuyer } = this.props;
    const { formCustomer, formTitle } = formAttr;
    let newFormValues = [...formValues];

    if (!saveOnUnmount) return;
    const { guarantor, fetchedCustomerDocuments } = this.state;
    let customerDocuments = [];

    if (DealerPropertyHelper.isUSUploadDocSyncCrm()) {
      customerDocuments = getUpdatedCustomerDocuments(fetchedCustomerDocuments, newFormValues);
    }

    if (DealerPropertyHelper.isDealContactsEnabled()) {
      const coBuyerFormData = _find(newFormValues, formData => formData?.type === BUYER_TYPE.CO_BUYER);

      if (!_isEmpty(coBuyerFormData) && _isEmpty(coBuyerFormData?.crmCustomerId)) {
        const contactPayload = getContactPayloadFromCustomer(coBuyerFormData);
        const { response } = await CustomerAPI.createCoBuyerCustomer(contactPayload);

        if (!_isEmpty(response)) {
          const crmCustomerId = tget(response, 'crmCustomer.id');
          newFormValues = _map(newFormValues, formData => {
            if (formData?.type === BUYER_TYPE.CO_BUYER) {
              return { ...formData, crmCustomerId };
            }
            return formData;
          });
        }
      }
    }

    if (!makeCoBuyerAsBuyer) {
      saveForm({
        customers: getPayloadFromForm(newFormValues, guarantor),
        formCustomer,
        formTitle,
        customerDocuments,
      });
    } else {
      const updatedFormValues = produce(newFormValues, draft => {
        const buyerIndex = _findIndex(draft, { type: BUYER_TYPE.BUYER });
        if (buyerIndex === -1) {
          const coBuyerIndex = _findIndex(draft, { type: BUYER_TYPE.CO_BUYER });
          if (coBuyerIndex !== -1) _set(draft[coBuyerIndex], 'type', BUYER_TYPE.BUYER);
        }
      });
      const buyerIndex = _findIndex(updatedFormValues, { type: BUYER_TYPE.BUYER });

      saveForm({
        customers: getPayloadFromForm(updatedFormValues, guarantor),
        formCustomer,
        formTitle,
        bankingDetails: updatedFormValues[buyerIndex]?.bankingDetails,
        customerDocuments,
      });
    }
  }

  handleToggleMediaLoading = mediaFields => {
    const { customerDocumentsLoading = [] } = this.state;

    const newCustomerDocumentsLoading = _reduce(
      _countBy([...customerDocumentsLoading, ..._castArray(mediaFields)]),
      (prevMediaFields, mediaFieldCount, mediaField) => {
        if (mediaFieldCount === 1) {
          return [...prevMediaFields, mediaField];
        }
        return prevMediaFields;
      },
      []
    );

    this.setState({ customerDocumentsLoading: newCustomerDocumentsLoading });
  };

  fetchCustomerDocuments = async payload => {
    const { setParentState, formValues } = this.props;
    const { response: customerDocuments, error } = await CustomerAPI.fetchCustomerDocuments(payload);

    if (!_isNil(customerDocuments)) {
      const buyerDocuments = getCustomerDocumentsBasedOnAssetId(customerDocuments, tget(formValues, [0]));
      const coBuyerDocuments = getCustomerDocumentsBasedOnAssetId(customerDocuments, tget(formValues, [1]));

      await setParentState(
        produce(draft => {
          if (_has(formValues, [0])) {
            draft.formValues[0] = { ...(draft.formValues[0] || {}), ...buyerDocuments };
          }
          if (_has(formValues, [1])) {
            draft.formValues[1] = { ...(draft.formValues[1] || {}), ...coBuyerDocuments };
          }
        }),
        false
      );
      await this.setState({ fetchedCustomerDocuments: customerDocuments });
    } else {
      toaster(TOASTER_TYPE.ERROR, getAPIError(error) || __('Failed to fetch the documents'));
    }
  };

  fetchDealCustomerDocuments = async () => {
    const { deal } = this.props;
    const payload = getPayloadForFetchingUploadedDocuments(deal);
    await this.fetchCustomerDocuments(payload);
  };

  onAddFormContactWithLead = async (lead, buyerData, coBuyerData) => {
    const { onAddContactWithLead, deal } = this.props;
    await onAddContactWithLead(lead, buyerData, coBuyerData);

    // refresh docs related to the new buyer with crmCustomer id added
    const customerId = tget(buyerData, 'id');
    const coBuyerCustomerId = _get(getCoBuyer(deal), 'customerId', EMPTY_STRING);
    if (customerId) {
      this.fetchCustomerDocuments({
        [CUSTOMER_DOCUMENT_PAYLOAD_KEYS.DEAL_NUMBER]: getDealNumber(deal),
        [CUSTOMER_DOCUMENT_PAYLOAD_KEYS.ASSET_TYPE]: CUSTOMER_DOCUMENT_ASSET_TYPES.DEAL_CUSTOMER,
        [CUSTOMER_DOCUMENT_PAYLOAD_KEYS.ASSET_IDS]: __compact(_uniq([customerId, coBuyerCustomerId])),
      });
    }
  };

  fetchCreditBalance = async () => {
    const { deal, dealerId } = this.props;
    const buyer = getBuyer(deal) || EMPTY_OBJECT;
    const { customerId } = buyer;
    const payload = {
      customerIds: [customerId],
    };
    const { response } = await CustomerAPI.fetchCreditBalance(payload);
    if (!_isEmpty(response)) {
      const creditBalance = _get(response, [customerId, 'creditBalance', dealerId], EMPTY_OBJECT);
      let retailCreditLimit = _get(creditBalance, 'segregatedCreditLimit.RETAIL', 0);
      let retailOutstandingAmount = _get(creditBalance, 'segregatedOutstandingAmount.RETAIL.amount', 0);
      retailCreditLimit = Number(retailCreditLimit) || 0;
      retailOutstandingAmount = Number(retailOutstandingAmount) || 0;

      const availableCreditLimit = (retailCreditLimit - retailOutstandingAmount) / 100;

      this.setState({
        availableCreditLimit,
      });
    }
  };

  getCustomerTypologies = async () => {
    const { response } = (await CMSAPI.getCustomersTypologies()) || EMPTY_OBJECT;
    this.setState({
      customerTypologies: _map(response, ({ value, labelKey }) => ({ label: labelKey, value })),
    });
  };

  getPaymentMethodOptions = async () => {
    const { response } = (await CMSAPI.getPaymentsBootstrap()) || EMPTY_OBJECT;

    this.setState({
      paymentMethodOptions: _map(response.paymentMethods, ({ value, labelKey }) => ({
        label: labelKey,
        value,
      })),
    });
  };

  getDayOfCollectionOptions = async () => {
    const response = (await FieldSetupApi.fetchFieldValues({ fieldCategory: 'PAYMENT_DUE_DATE' })) || EMPTY_OBJECT;
    this.setState({
      dayOfCollectionOptions: _map(response, ({ description, id }) => ({
        label: description,
        value: id,
      })),
    });
  };

  getState = () => ({ ...this.state, ...this.props });

  handleDeleteCustomerDocument = async (index, payload, setParentState) => {
    const { deal, formValues } = this.props;

    const {
      id,
      additional: { removeMedia },
    } = payload;
    let isDocumentDeleted = false;

    if (DealerPropertyHelper.isUSUploadDocSyncCrm()) {
      const customer = tget(formValues, [index]);
      const docType = FORM_FIELD_TO_DOCUMENT_TYPES[id];
      const document = getArraySafeValue(tget(customer, [id]));
      const deleteDocumentPayload = getPayloadForDeletingUploadedDocuments(deal, customer, docType, document);

      if (!_isEmpty(tget(deleteDocumentPayload, CUSTOMER_DOCUMENT_PAYLOAD_KEYS.ASSET_ID))) {
        const { response, error } = await CustomerAPI.removeCustomerDocuments(deleteDocumentPayload);

        if (_toUpper(response) === STATUS.SUCCESS) {
          isDocumentDeleted = true;
        } else {
          toaster(TOASTER_TYPE.ERROR, getAPIError(error) || __('Failed to delete the media'));
        }
      } else {
        isDocumentDeleted = true;
      }
    }

    if (isDocumentDeleted || !DealerPropertyHelper.isUSUploadDocSyncCrm()) {
      removeMedia(tget(formValues, [index, id]));
      const funcToExec = CUSTOMER_FORM_ACTION_HANDLER[customerFormActionTypes.ON_FIELD_CHANGE] || _noop;
      funcToExec(payload, {
        setState: setParentState,
        getState: this.getState,
        index,
      });
    }
  };

  handleAction =
    index =>
    async ({ type, payload }) => {
      const { setParentState, onCopyAddress, onCopyOfInsurance } = this.props;
      switch (type) {
        case customerFormActionTypes.COPY_FROM_BUYER: {
          onCopyAddress();
          break;
        }
        case customerFormActionTypes.COPY_INSURANCE_DETAILS_FROM_BUYER: {
          onCopyOfInsurance();
          break;
        }
        case customerFormActionTypes.ON_REMOVE: {
          await this.handleDeleteCustomerDocument(index, payload, setParentState);
          break;
        }
        default: {
          const funcToExec = CUSTOMER_FORM_ACTION_HANDLER[type] || _noop;
          funcToExec(payload, {
            setState: setParentState,
            getState: this.getState,
            index,
          });
          break;
        }
      }
    };

  handleMarkAsGuarantor = () => {
    const { onTouch } = this.props;
    onTouch();
    this.setState(prevState => ({ guarantor: !prevState.guarantor }));
  };

  redirectTOCRM = () => {
    const { leadId } = this.props;
    redirectToLeadDetails(leadId);
  };

  getMessage = customerName => (
    <div>
      <Heading size={4} className={styles.confirmationTitle}>
        {__('Are you sure?')}
      </Heading>
      <Content>{__(`Lead ${customerName} will be detached and removed from the deal.`)}</Content>
    </div>
  );

  removeCustomerFromStore = index => {
    const { setParentState } = this.props;
    setParentState(
      produce(draft => {
        draft.formValues.splice(index, 1);
        draft.errors.splice(index, 1);
      })
    );
  };

  removeLeadFromStore = async index => {
    const { onRemoveLead, setParentState } = this.props;
    const status = await onRemoveLead();
    if (status) {
      this.checkForMotabilityDealAndLead();

      setParentState(
        produce(draft => {
          const coBuyerIndex = _findIndex(draft.formValues, {
            type: BUYER_TYPE.CO_BUYER,
          });
          if (coBuyerIndex !== -1) _set(draft.formValues[coBuyerIndex], 'customer.leadId', EMPTY_STRING);
          draft.formValues.splice(index, 1);
          draft.errors.splice(index, 1);
        })
      );
    }
  };

  checkForMotabilityDealAndLead = () => {
    const { dealType } = this.props;
    if (dealType === DEAL_TYPES.MOTABILITY) {
      toaster('error', __(`You have deleted the Customer, Please add a Motability lead to this deal`));
    }
  };

  removeCustomer = index => {
    const { leadId, formValues, dealType, onRemoveCustomer, resetCustomerFormErrors } = this.props;
    const isPrimaryCustomer = _get(formValues, [index, 'type']) === BUYER_TYPE.BUYER;
    const customerName =
      getCustomerName(_get(formValues, [index, 'customer'])) || (isPrimaryCustomer ? __('Buyer #1') : __('Buyer #2'));
    const isCRMEnabled = isCRMFlow(dealType);
    if (leadId && isPrimaryCustomer && isCRMEnabled) {
      ConfirmationModal.show({
        title: __('Delete Buyer'),
        message: this.getMessage(customerName),
        submitBtnText: __('Delete'),
        hideCancel: false,
        maskClosable: false,
        onSubmitCb: async () => {
          ConfirmationModal.showLoader();
          await this.removeLeadFromStore(index);
          ConfirmationModal.hideLoader();
          ConfirmationModal.close();
        },
      });
      return;
    }
    resetCustomerFormErrors(index);
    this.removeCustomerFromStore(index);
    if (isPrimaryCustomer) onRemoveCustomer(isPrimaryCustomer);
  };

  onAddLead = (lead, formData) => {
    const { onAddLead } = this.props;
    if (isInchcape()) {
      const buyerCustomerId = _get(lead, 'buyerCustomerId', EMPTY_STRING);
      this.setState({
        addLeadId: buyerCustomerId,
      });
    }
    onAddLead(lead, formData);
  };

  handleLeadSelection = (lead, type) => {
    const { handleDuplicateLeadSelection } = this.props;
    handleDuplicateLeadSelection(lead, type);
  };

  handleCustomerSelection = (customer, type) => {
    const { handleDuplicateCustomerSelection } = this.props;
    handleDuplicateCustomerSelection(customer, type);
  };

  getSearchQuery = index => {
    const { formValues } = this.props;
    const type = _get(formValues, [index, 'type']);
    const customer = _get(formValues, [index, 'customer']) || EMPTY_OBJECT;
    const { firstName, middleName, lastName, legalName, tradeName, email, mobileNo, workPhone, homePhone } = customer;
    const validateName = value => (!Validators.minLength(3)(value) ? value : null);
    const validatePhoneNumber = value =>
      !Validators.phoneNumber(getUnformattedPhoneNumber(value)) ? getUnformattedPhoneNumber(value) : null;
    const validateEmail = value => (!Validators.email(value) ? value : null);
    return {
      type,
      firstName: validateName(firstName),
      middleName: validateName(middleName),
      lastName: validateName(lastName),
      legalName: validateName(legalName),
      tradeName: validateName(tradeName),
      email: validateEmail(email),
      cellPhone: validatePhoneNumber(getPhoneNumberValue(mobileNo)),
      workPhone: validatePhoneNumber(getPhoneNumberValue(workPhone)),
      homePhone: validatePhoneNumber(getPhoneNumberValue(homePhone)),
      customerType: CRM_VS_DEAL_CUSTOMER_TYPES[getCustomerType(formValues, index)],
    };
  };

  renderHeader = (index, deleteReasonForCPRAGDPR) => {
    const { formValues, leadId, isDealViewOnly, showAddCustomer, dealType, isDealLocked } = this.props;
    const { guarantor, chargeCustomer, availableCreditLimit } = this.state;
    const type = _get(formValues, [index, 'type']);
    const customer = _get(formValues, [index, 'customer']) || EMPTY_OBJECT;
    const isPrimaryCustomer = type === BUYER_TYPE.BUYER;
    const currentCustomerName =
      getCustomerName(customer) ||
      (isPrimaryCustomer ? CUSTOMER_TABS_TITLE[CUSTOMER_TABS.BUYER] : CUSTOMER_TABS_TITLE[CUSTOMER_TABS.CO_BUYER]);
    const isCRMEnabled = isCRMFlow(dealType);
    const searchQuery = this.getSearchQuery(index);
    const deleteCustomerHideCondition = isDealLocked || !_isEmpty(deleteReasonForCPRAGDPR);
    const availableCreditLimitValue = !_isNil(availableCreditLimit)
      ? formatCurrencyWithMoneyFormat(currencyFormatter(availableCreditLimit))
      : NO_DATA;
    const isAvailableCreditPositive = availableCreditLimit > 0;
    const crmBuyerCustomerId = findBuyer(formValues)?.customerId;
    return (
      <PropertyControlledComponent controllerProperty={showAddCustomer}>
        <div className={styles.accordionHeader}>
          <div className="d-flex justify-content-between">
            <div className={cx('d-flex justify-content-between', styles.firstHalf)}>
              <div className="d-flex">
                {currentCustomerName && <Heading size={3}>{__(currentCustomerName)}</Heading>}
                {chargeCustomer && isInchcape() && (
                  <Tag key="primary" className={styles.primaryTag} contentClassName={styles.tagName}>
                    {__('Charge')}
                  </Tag>
                )}
                <PropertyControlledComponent controllerProperty={!!leadId && isCRMEnabled}>
                  <Popover
                    placement="top"
                    trigger="hover"
                    content={<div className="padding1">{__('View Lead Details')}</div>}>
                    <Button
                      view={Button.VIEW.ICON}
                      className="m-x-12"
                      icon="icon-redirect"
                      onClick={this.redirectTOCRM}
                    />
                  </Popover>
                </PropertyControlledComponent>
              </div>
              <PropertyControlledComponent
                controllerProperty={isPrimaryCustomer ? !leadId && isCRMEnabled : isCRMEnabled}>
                <DuplicateAlerts
                  {...searchQuery}
                  onLeadSelect={this.handleLeadSelection}
                  onCustomerSelect={this.handleCustomerSelection}
                  viewOnly={isDealViewOnly}
                  crmBuyerCustomerId={crmBuyerCustomerId}
                  dealCustomerType={type}
                />
              </PropertyControlledComponent>
            </div>
            <div className="d-flex align-items-center">
              {!isPrimaryCustomer && (
                <Checkbox
                  label={__('Mark Co-Buyer As Guarantor')}
                  onChange={this.handleMarkAsGuarantor}
                  value={guarantor}
                  disabled={isDealViewOnly}
                />
              )}
              <PropertyControlledComponent controllerProperty={!deleteCustomerHideCondition}>
                <FontIcon onClick={!isDealViewOnly ? () => this.removeCustomer(index) : _noop} className="marginL16">
                  icon-trash
                </FontIcon>
              </PropertyControlledComponent>
            </div>
          </div>
        </div>
        <PropertyControlledComponent controllerProperty={isInchcape() && chargeCustomer}>
          <div className="d-flex align-items-center">
            <Label className={styles.black}>{__('Available CL:')}</Label>
            <span className={cx('m-l-8', isAvailableCreditPositive ? styles.green : styles.maroon, styles.fontWeight)}>
              {availableCreditLimitValue}
            </span>
          </div>
        </PropertyControlledComponent>
      </PropertyControlledComponent>
    );
  };

  renderCreditApplicationAlert = index => {
    const { formValues, isConsumerCreditApplicationEnabled } = this.props;
    const type = getBuyerType(formValues, index);
    if (type === BUYER_TYPE.BUYER && isConsumerCreditApplicationEnabled) {
      const searchQuery = this.getSearchQuery(index);
      return <CreditApplicationAlert {...searchQuery} />;
    }
    return null;
  };

  shareApplicationUrlLink = async payload => {
    const { customerBuyerType } = this.state;
    const linkShared = await DeskingAPI.shareUrlLink(
      payload,
      customerBuyerType === CUSTOMER_TYPE.BUYER
        ? SHARE_MESSAGE_TEMPLATE_TYPE.URL_SHARE
        : SHARE_MESSAGE_TEMPLATE_TYPE.URL_SHARE_COBUYER
    );
    if (!linkShared.error) {
      ContactDetailsModal.cancel();
      this.setState({ showShareLinkBanner: false });
    }
  };

  openShareLinkModal = () => {
    const { deal } = this.props;
    const { customerBuyerType } = this.state;
    const shareType =
      customerBuyerType === CUSTOMER_TYPE.BUYER
        ? SHARE_MESSAGE_TEMPLATE_TYPE.URL_SHARE
        : SHARE_MESSAGE_TEMPLATE_TYPE.URL_SHARE_COBUYER;
    return ContactDetailsModal.init({
      title: __('Share Concierge Website Link'),
      onSubmit: this.shareApplicationUrlLink,
      checkisMobileAndEmailOptedIn: deal.checkisMobileAndEmailOptedIn,
      showCoBuyerIfAvailable: false,
      isBuyer: customerBuyerType === CUSTOMER_TYPE.BUYER,
      extra: { shareType },
    });
  };

  shareLinksDropDownOptions = () => (
    <Menu>
      <Content className={styles.shareLinkDropDownOptionsHeader}>{__('Share via')}</Content>
      <Menu.Item key="E&T" onClick={this.openShareLinkModal}>
        {__('Email & Text')}
      </Menu.Item>
    </Menu>
  );

  renderShareLinkOption = () => {
    const { isDealViewOnly } = this.props;
    const { openShareLinkDropDown } = this.state;
    return (
      <DropDown
        overlay={this.shareLinksDropDownOptions()}
        placement="bottomRight"
        trigger="click"
        disabled={isDealViewOnly}>
        <Button
          className={styles.shareLinkDropDownButton}
          onClick={() => this.setState(prevState => ({ openShareLinkDropDown: !prevState.openShareLinkDropDown }))}>
          <Content className={styles.shareLinkDropDownButtonContent}>{__('Share conceirge link')}</Content>
          <FontIcon className={styles.shareLinkDropDownButtonIcon} size={SIZES.S}>
            {openShareLinkDropDown ? 'icon-chevron-up' : 'icon-chevron-down'}
          </FontIcon>
        </Button>
      </DropDown>
    );
  };

  renderHondaCustomerLoginAlert = () => {
    const { ciamId, customerBuyerType } = this.state;
    // const { formValues } = this.props;
    const errorMessage = [
      {
        showVerticalStrip: true,
        key: 'LOGIN_ERROR',
        icon: 'icon-info',
        iconClass: styles.stickyBarIcon,
        info:
          customerBuyerType === CUSTOMER_TYPE.BUYER ? (
            <Content>{__('Share link for buyer login to retrive more information')}</Content>
          ) : (
            <Content>{__('Share link for co-buyer login to retrive more information')}</Content>
          ),
        showHeading: false,
      },
    ];
    return (
      <StickyBanner
        bannerStyle={STICKY_BANNER.INFO}
        bannerType={BANNER_TYPE.INFO}
        reminders={errorMessage}
        renderCustomAction={ciamId ? _noop : this.renderShareLinkOption}
        showDismiss={!_isEmpty(ciamId)}
        onClose={() => this.setState(prevState => ({ showShareLinkBanner: !prevState.showShareLinkBanner }))}
      />
    );
  };

  getFinalFormValues = formValues => {
    if (_has(formValues, 'customerTaxClassification') || _has(formValues, 'businessTaxClassification')) {
      return {
        taxClassification: {
          customerTaxClassification: _get(formValues, 'customerTaxClassification') || EMPTY_STRING,
          businessTaxClassification: _get(formValues, 'businessTaxClassification') || EMPTY_STRING,
        },
        ...formValues,
      };
    }
    return formValues;
  };

  setChargeCustomer = value => {
    this.setState({
      chargeCustomer: value,
    });
  };

  addCoBuyerDetails = async coBuyerContactDetails => {
    const { onContactCustomerSelection, deal } = this.props;
    const crmCoBuyerCustomerId = tget(coBuyerContactDetails, 'crmCoBuyerCustomerId');
    const buyerCustomerId = _get(getBuyer(deal), 'customerId', EMPTY_STRING);

    const filters = [
      {
        type: 'id',
        operator: 'IN',
        values: [crmCoBuyerCustomerId],
      },
    ];

    const payload = getPayloadToFetchContacts({
      selectedFilters: filters,
      pageSize: DEFAULT_PAGE_SIZE,
      currentPage: 1,
    });

    const response = await ContactsActions.fetchContactsList(payload);

    const hits = tget(response, 'hits', EMPTY_ARRAY);
    const crmCustomers = _keyBy(hits, 'id');
    const contactCoBuyerData = tget(crmCustomers, crmCoBuyerCustomerId, EMPTY_OBJECT);
    const coBuyerLinkedDMSCustomerIds = CrmCustomerReader.linkedDMSCustomerIds(contactCoBuyerData) || [];
    if (contactCoBuyerData?.dmsCustomerId) {
      coBuyerLinkedDMSCustomerIds.push(contactCoBuyerData?.dmsCustomerId);
    }

    let coBuyerCustomerData = {};
    if (!_isEmpty(coBuyerLinkedDMSCustomerIds)) {
      const payloadToFetchCoBuyerCustomers = getPayloadToFetchCustomers(coBuyerLinkedDMSCustomerIds);
      const customerResponse = await CustomerAPI.getCustomersList(payloadToFetchCoBuyerCustomers);
      const customers = tget(customerResponse, 'response.hits', EMPTY_ARRAY);
      coBuyerCustomerData = _head(customers);
    }

    let coBuyerCustomerDataFromContact = {};

    if (!_isEmpty(coBuyerCustomerData)) {
      coBuyerCustomerDataFromContact = {
        ...coBuyerCustomerData,
        customerId: coBuyerCustomerData?.id,
      };
    } else {
      coBuyerCustomerDataFromContact = !_isEmpty(contactCoBuyerData)
        ? getCustomerFromContact(getContactDataFromCoBuyerForm(contactCoBuyerData))
        : getCustomerFromContact(coBuyerContactDetails);
    }

    await onContactCustomerSelection(
      {
        ...coBuyerCustomerDataFromContact,
        crmCustomerId: crmCoBuyerCustomerId,
      },
      CUSTOMER_TYPE.CO_BUYER
    );

    // refresh docs related to the new crmCustomer id added
    this.fetchCustomerDocuments({
      [CUSTOMER_DOCUMENT_PAYLOAD_KEYS.DEAL_NUMBER]: getDealNumber(deal),
      [CUSTOMER_DOCUMENT_PAYLOAD_KEYS.ASSET_TYPE]: CUSTOMER_DOCUMENT_ASSET_TYPES.DEAL_CUSTOMER,
      [CUSTOMER_DOCUMENT_PAYLOAD_KEYS.ASSET_IDS]: __compact(
        _uniq([buyerCustomerId, coBuyerCustomerDataFromContact?.id])
      ),
    });
  };

  openLeadModalForCoBuyer = () => {
    const { soldAtSiteId: siteId, formValues } = this.props;
    const department = DEPARTMENTS.SALES;
    const { leadId: selectedBuyerLeadId } = this.getFinalFormValues(formValues?.[0]) || EMPTY_OBJECT; // index = 0 since need buyer details

    const props = {
      initialValues: { department, ...(isCRMMultiOEMSwitchEnabled() ? { siteId } : EMPTY_OBJECT) },
      additionalInfo: {
        onLookupActionCb: async ({ contactDetails }) => {
          const contactData = getContactDataFromCoBuyerForm(contactDetails);
          await this.addCoBuyerDetails(contactData);
        },
      },
      tabKey: 'coBuyer',
      viewType: 'CO_BUYER_FORM',
      leadDetails: {
        leadId: selectedBuyerLeadId,
      },
    };
    showGlobalModal({ modalType: MODAL_TYPE.LEAD_INFO_MODAL, payload: props });
  };

  renderAccordionBody = index => {
    const {
      enableAttachment,
      formValues,
      setParentState,
      getParentState,
      errors,
      isDealViewOnly,
      blockScreen,
      unBlockScreen,
      customers,
      getCountOfPhoneNumberDigits,
      deal,
      setRulesList,
      validateSingleFieldErrors,
      setCustomerFormValidationErrors,
      dealerInfo,
    } = this.props;
    const {
      customerTypologies,
      paymentMethodOptions,
      dayOfCollectionOptions,
      showShareLinkBanner,
      chargeCustomer,
      isNewCustomer,
      addLeadId,
      customerDocumentsLoading,
    } = this.state;
    const customerType = getCustomerType(formValues, index);
    const type = getBuyerType(formValues, index);
    const customerTypeOfBuyer = getCustomerType(formValues, 0);
    const deleteReasonForCPRAGDPR = _get(customers, `${index}.deleteReason`) || null;
    const oldCustomerDisplayId = _get(customers, `${index}.oldDisplayId`) || null;
    const ciamId = _get(customers, `${index}.ciamId`) || null;
    if (type === BUYER_TYPE.BUYER) {
      this.setState({ ciamId });
    }
    const finalFormValues = this.getFinalFormValues(formValues[index]);
    const buyer = getBuyer(deal) || EMPTY_OBJECT;
    const { customerId } = buyer;
    const chargeCustomerValue = chargeCustomer && !canEditChargeCustomerPII();
    const isCustomerPresent =
      !_isEmpty(tget(finalFormValues, 'customerId')) || !_isEmpty(tget(finalFormValues, 'displayId'));
    const isCurrentAddressAvailable = _find(finalFormValues?.personalAddress, {
      [ADDRESS_FORM_FIELD.ADDRESS_TYPE]: ADDRESS_TYPE.CURRENT,
    });
    const isProgramAcuraNewAndPrimaryVehicleNotZDX = getIsProgramAcuraNewAndPrimaryVehicleNotZDX(deal);
    return (
      <div>
        <PropertyControlledComponent controllerProperty={!DealerPropertyHelper.isCRMEnabledV2()}>
          {this.renderCreditApplicationAlert(index)}
        </PropertyControlledComponent>
        <PropertyControlledComponent
          controllerProperty={
            showShareLinkBanner && type === BUYER_TYPE.BUYER && !isProgramAcuraNewAndPrimaryVehicleNotZDX
          }>
          {this.renderHondaCustomerLoginAlert()}
        </PropertyControlledComponent>
        <div className={cx(styles.buyerOrCoBuyer)}>
          {this.renderHeader(index, deleteReasonForCPRAGDPR)}
          <FormPage
            sections={getFormSections({ customerType, enableAttachment, isCustomerPresent })}
            fields={getFormFields({
              dealerInfo,
              index,
              customerType,
              blockScreen,
              unBlockScreen,
              handleToggleMediaLoading: this.handleToggleMediaLoading,
              getState: this.getState,
              setLocalState: setParentState,
              getLocalState: getParentState,
              errors,
              disabled: isDealViewOnly,
              isCoBuyer: type === BUYER_TYPE.CO_BUYER,
              customerTypeOfBuyer,
              customerTypologies,
              deleteReasonForCPRAGDPR,
              oldCustomerDisplayId,
              getCountOfPhoneNumberDigits,
              deal,
              paymentMethodOptions,
              dayOfCollectionOptions,
              ciamId,
              setRulesList,
              validateSingleFieldErrors,
              setCustomerFormValidationErrors,
              chargeCustomer: chargeCustomerValue,
              customerDocumentsLoading,
              isCurrentAddressAvailable,
            })}
            values={finalFormValues}
            errors={errors}
            headerComponent={null}
            footerComponent={null}
            onAction={this.handleAction(index)}
            contentHeight="auto"
          />
        </div>
        <PropertyControlledComponent controllerProperty={isInchcape()}>
          <ChargeCustomer
            customerId={customerId}
            setChargeCustomer={this.setChargeCustomer}
            isNewCustomer={isNewCustomer}
            addLeadId={addLeadId}
          />
        </PropertyControlledComponent>
      </div>
    );
  };

  renderEmptyCustomerView = buyerType => {
    const {
      height,
      buildNew,
      showAddCustomer,
      toggleModal,
      dealType,
      soldAtSiteId,
      isDealViewOnly,
      deal,
      onContactCustomerSelection,
      onAddContactWithLead,
    } = this.props;
    const { isNewCustomer, addLeadId } = this.state;
    const isCRMEnabled = isCRMFlow(dealType);
    const isArcLiteProgramEnabled = isArcLiteProgram();
    const buyer = getBuyer(deal) || EMPTY_OBJECT;
    const { customerId } = buyer;
    return (
      <>
        <PropertyControlledComponent controllerProperty={showAddCustomer}>
          <div className={cx(styles.tabContent)}>
            <div style={{ height: height - TAB_HEIGHT_TABS_STYLE_CONFIG.height }} className={cx(styles.tabContent)}>
              <div className={cx(styles.noBuyer)}>
                <div className={cx(styles.placeHolder)}>
                  <img
                    src={buyerType === BUYER_TYPE.BUYER ? NO_BUYER : NO_CO_BUYER}
                    alt={EMPTY_STRING}
                    className={cx(styles.noBuyerImage)}
                  />
                  <Heading size={4} className="marginT16">
                    {buyerType === BUYER_TYPE.BUYER ? __('No Buyer Found') : __('No Co-buyer Found')}
                  </Heading>
                  <PropertyControlledComponent controllerProperty={!isCRMEnabled}>
                    <Button disabled={isDealViewOnly} className="marginT24" onClick={() => toggleModal(buyerType)}>
                      {__('Add Customer')}
                    </Button>
                    <PropertyControlledComponent
                      controllerProperty={!DealerPropertyHelper.isCustomerDuplicatePreventionEnabled()}>
                      <Content className="marginT12">{__('or')}</Content>
                      <Button
                        className="marginT12"
                        disabled={isDealViewOnly}
                        view={Button.VIEW.TERTIARY}
                        onClick={() => {
                          buildNew(buyerType);
                          this.setState({
                            isNewCustomer: true,
                          });
                        }}>
                        {__('Build New')}
                      </Button>
                    </PropertyControlledComponent>
                  </PropertyControlledComponent>
                  <PropertyControlledComponent controllerProperty={isCRMEnabled}>
                    <PropertyControlledComponent controllerProperty={buyerType === BUYER_TYPE.BUYER}>
                      <PropertyControlledComponent controllerProperty={!isArcLiteProgramEnabled}>
                        <AddLeadButton
                          disabled={isDealViewOnly}
                          isCRMEnabled={isCRMEnabled}
                          onLeadCreate={this.onAddLead}
                          siteId={soldAtSiteId}
                          dealType={dealType}
                          onContactCustomerSelection={onContactCustomerSelection}
                          onAddContactWithLead={this.onAddFormContactWithLead}
                        />
                      </PropertyControlledComponent>
                      <PropertyControlledComponent controllerProperty={!DealerPropertyHelper.isDealContactsEnabled()}>
                        <Content className="marginT12">{__('or')}</Content>
                        <Button
                          className="marginT12"
                          disabled={isDealViewOnly}
                          view={Button.VIEW.TERTIARY}
                          onClick={() => {
                            buildNew(buyerType);
                            this.setState({
                              isNewCustomer: true,
                            });
                          }}>
                          {__('Build New')}
                        </Button>
                      </PropertyControlledComponent>
                    </PropertyControlledComponent>
                    <PropertyControlledComponent controllerProperty={buyerType === BUYER_TYPE.CO_BUYER}>
                      <Button
                        className="marginT12"
                        disabled={isDealViewOnly}
                        onClick={() => {
                          if (DealerPropertyHelper.isDealContactsEnabled()) {
                            this.openLeadModalForCoBuyer();
                          } else {
                            buildNew(buyerType);
                            this.setState({
                              isNewCustomer: true,
                            });
                          }
                        }}>
                        {__('Add Co-buyer')}
                      </Button>
                    </PropertyControlledComponent>
                  </PropertyControlledComponent>
                </div>
              </div>
            </div>
          </div>
        </PropertyControlledComponent>
        <PropertyControlledComponent controllerProperty={isInchcape()}>
          <ChargeCustomer
            customerId={customerId}
            setChargeCustomer={this.setChargeCustomer}
            isNewCustomer={isNewCustomer}
            addLeadId={addLeadId}
          />
        </PropertyControlledComponent>
      </>
    );
  };

  renderTabContent = tab => {
    const { formValues } = this.props;

    switch (tab) {
      case CUSTOMER_TABS.BUYER: {
        const index = _findIndex(formValues, customer => _get(customer, 'type') === BUYER_TYPE.BUYER);
        if (index === -1) return this.renderEmptyCustomerView(BUYER_TYPE.BUYER);
        return this.renderAccordionBody(index);
      }
      case CUSTOMER_TABS.CO_BUYER: {
        const index = _findIndex(formValues, customer => _get(customer, 'type') === BUYER_TYPE.CO_BUYER);
        if (index === -1) return this.renderEmptyCustomerView(BUYER_TYPE.CO_BUYER);
        return this.renderAccordionBody(index);
      }
      default:
        return null;
    }
  };

  getSwitchPrimaryCustomerChangeMessage = () => {
    const { formValues } = this.props;
    const index = _findIndex(formValues, customer => _get(customer, 'type') === BUYER_TYPE.CO_BUYER);
    const customerName = getCustomerName(_get(formValues, [index, 'customer']));
    const isCRMEnabled = DealerPropertyHelper.isCRMEnabled();
    return (
      <div>
        <Content>
          {isCRMEnabled
            ? __(
                `Making {{customerName}} as the Buyer will make the Lead move into the {{customerName}}'s Contact profile in CRM. Also, the Contact Assignees in CRM for {{customerName}} will get updated. Are you sure you would like to Continue?`,
                { customerName }
              )
            : __(
                'Are you sure you want to switch the Buyer and Co-Buyer in the Deal? Making this update might need previously signed documents to be re-shared.'
              )}
        </Content>
      </div>
    );
  };

  handlePrimaryCustomerChange = () => {
    ConfirmationModal.show({
      title: __('Switch Buyer and Co-Buyer'),
      message: this.getSwitchPrimaryCustomerChangeMessage(),
      submitBtnText: __('Switch'),
      hideCancel: false,
      maskClosable: false,
      onSubmitCb: () => {
        const { handlePrimaryCustomerChange } = this.props;
        handlePrimaryCustomerChange();
        ConfirmationModal.close();
      },
    });
  };

  handleFormAttrChange = key => value => {
    const { handleFormAttrChange } = this.props;
    handleFormAttrChange(key, value);
  };

  renderRightActions = () => {
    const { formAttr, isDealViewOnly, showAddCustomer, formValues } = this.props;
    const { formCustomer, formTitle } = formAttr;
    const coBuyerIndex = _findIndex(formValues, customer => _get(customer, 'type') === BUYER_TYPE.CO_BUYER);
    const isCoBuyerPresent = coBuyerIndex !== -1 && isCustomerEmpty(_get(formValues[coBuyerIndex], 'customer'));
    return (
      showAddCustomer && (
        <div className={styles.rightActionContainer}>
          <Button
            className={styles.markAsBuyer}
            disabled={isDealViewOnly || !isCoBuyerPresent}
            onClick={this.handlePrimaryCustomerChange}>
            {__('Switch Buyer and Co-Buyer')}
          </Button>
          <Select
            style={{ width: 125 }}
            containerClassName={styles.titleContainer}
            selectClass={styles.selectItem}
            label={__('Title:')}
            placeholder={__('Select Title')}
            onChange={this.handleFormAttrChange('formTitle')}
            options={TITLE_OPTIONS}
            value={formTitle}
            showBorder={false}
            disabled={isDealViewOnly}
          />
          <Select
            style={{ width: 125 }}
            containerClassName={styles.registrationContainer}
            selectClass={styles.selectItem}
            label={__('Registration:')}
            placeholder={__('Select Registration')}
            onChange={this.handleFormAttrChange('formCustomer')}
            options={CUSTOMER_TYPE_OPTIONS}
            value={formCustomer}
            showBorder={false}
            disabled={isDealViewOnly}
          />
        </div>
      )
    );
  };

  setCurrentBuyerType = value => {
    this.setState({ customerBuyerType: value });
  };

  renderTabs() {
    return (
      <Tabs
        defaultActiveKey={CUSTOMER_TABS_KEYS.BUYER}
        onChange={this.setCurrentBuyerType}
        tabBarStyle={TAB_HEIGHT_TABS_STYLE_CONFIG}
        tabBarExtraContent={this.renderRightActions()}
        destroyInactiveTabPane>
        {CUSTOMER_TABS_KEYS.map(tab => (
          <Tabs.TabPane tab={CUSTOMER_TABS_TITLE[tab]} key={tab}>
            {this.renderTabContent(tab)}
          </Tabs.TabPane>
        ))}
      </Tabs>
    );
  }

  render() {
    const { showAddCustomer } = this.props;
    if (isInchcapeOrRRG()) {
      return this.renderTabContent(CUSTOMER_TABS.BUYER);
    }
    return showAddCustomer ? this.renderTabs() : this.renderTabContent(CUSTOMER_TABS.BUYER);
  }
}

const mapStateToProps = globalState => {
  const state = _get(globalState, BASE_REDUCER_KEY);
  return {
    dealerId: getDealerIdFromState(state),
  };
};

CustomerForm.propTypes = {
  customers: PropTypes.array,
  saveOnUnmount: PropTypes.bool,
  enableAttachment: PropTypes.bool,
  showAddCustomer: PropTypes.bool,
  saveForm: PropTypes.func,
  formAttr: PropTypes.object,
  formValues: PropTypes.array,
  errors: PropTypes.array,
  setParentState: PropTypes.func.isRequired,
  getParentState: PropTypes.func.isRequired,
  setRulesList: PropTypes.func.isRequired,
  validateSingleFieldErrors: PropTypes.func.isRequired,
  onTouch: PropTypes.func,
  isDealViewOnly: PropTypes.bool,
  leadId: PropTypes.string,
  handleFormAttrChange: PropTypes.func,
  toggleModal: PropTypes.func,
  onAddLead: PropTypes.func,
  onRemoveLead: PropTypes.func,
  onCopyAddress: PropTypes.func,
  onCopyOfInsurance: PropTypes.func,
  buildNew: PropTypes.func,
  handleDuplicateLeadSelection: PropTypes.func,
  handleDuplicateCustomerSelection: PropTypes.func,
  handlePrimaryCustomerChange: PropTypes.func,
  onRemoveCustomer: PropTypes.func,
  blockScreen: PropTypes.func,
  unBlockScreen: PropTypes.func,
  makeCoBuyerAsBuyer: PropTypes.bool,
  isConsumerCreditApplicationEnabled: PropTypes.bool,
  soldAtSiteId: PropTypes.string,
  deal: PropTypes.object,
  onContactCustomerSelection: PropTypes.func,
  onAddContactWithLead: PropTypes.func,
  dealType: PropTypes.string,
};

CustomerForm.defaultProps = {
  customers: EMPTY_ARRAY,
  saveOnUnmount: true,
  enableAttachment: false,
  showAddCustomer: false,
  saveForm: _noop,
  formAttr: EMPTY_OBJECT,
  formValues: EMPTY_ARRAY,
  errors: EMPTY_ARRAY,
  onTouch: _noop,
  isDealViewOnly: false,
  leadId: EMPTY_STRING,
  handleFormAttrChange: _noop,
  onAddLead: _noop,
  onRemoveLead: _noop,
  toggleModal: _noop,
  onCopyAddress: _noop,
  onCopyOfInsurance: _noop,
  buildNew: _noop,
  handleDuplicateLeadSelection: _noop,
  handleDuplicateCustomerSelection: _noop,
  handlePrimaryCustomerChange: _noop,
  onRemoveCustomer: _noop,
  blockScreen: _noop,
  unBlockScreen: _noop,
  makeCoBuyerAsBuyer: false,
  isConsumerCreditApplicationEnabled: false,
  soldAtSiteId: '',
  deal: EMPTY_OBJECT,
  onContactCustomerSelection: _noop,
  onAddContactWithLead: _noop,
  dealType: EMPTY_STRING,
};

export default compose(connect(mapStateToProps), withDealerInfo)(CustomerForm);
