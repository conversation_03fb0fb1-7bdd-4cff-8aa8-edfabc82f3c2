import { BUYER_TYPE } from 'pages/deallist/deal.constants';
import { FORM_FIELD as ADDRESS_FORM_FIELD } from '../components/addressForm/AddressForm.constants';
import { ADDRESS_TYPE, DURATION_TYPES, FORM_FIELD } from '../CustomerForm.constants';

import { getInitialAddress, getInitialPrimaryCustomer, getFormValueForExistingCustomer } from '../CustomerForm.utils';
import { FORM_FIELD as APPLICANT_FORM_FIELD } from '../components/applicantsDetailsForm/ApplicantsDetailsForm.constants';

describe('getInitialAddress()', () => {
  test('getInitialAddress returns an object with the expected structure', () => {
    const result = getInitialAddress();
    expect(result).toEqual([
      {
        [ADDRESS_FORM_FIELD.ADDRESS_TYPE]: ADDRESS_TYPE.CURRENT,
        [ADDRESS_FORM_FIELD.PERIOD_OF_RESIDENCE_DURATION_TYPE]: DURATION_TYPES.YEAR,
        currentAddress: true,
      },
    ]);
  });

  test('getInitialAddress correctly handles different addressType values', () => {
    const resultCurrent = getInitialAddress(ADDRESS_TYPE.CURRENT);
    expect(resultCurrent).toEqual([
      {
        [ADDRESS_FORM_FIELD.ADDRESS_TYPE]: ADDRESS_TYPE.CURRENT,
        [ADDRESS_FORM_FIELD.PERIOD_OF_RESIDENCE_DURATION_TYPE]: DURATION_TYPES.YEAR,
        currentAddress: true,
      },
    ]);

    const resultTrading = getInitialAddress(ADDRESS_TYPE.TRADING);
    expect(resultTrading).toEqual([
      {
        [ADDRESS_FORM_FIELD.ADDRESS_TYPE]: ADDRESS_TYPE.TRADING,
        [ADDRESS_FORM_FIELD.PERIOD_OF_RESIDENCE_DURATION_TYPE]: DURATION_TYPES.YEAR,
        currentAddress: false,
      },
    ]);
  });

  test('getInitialAddress correctly handles additional properties in the rest object', () => {
    const result = getInitialAddress(undefined, { additionalProperty: 'value' });
    expect(result).toEqual([
      {
        [ADDRESS_FORM_FIELD.ADDRESS_TYPE]: ADDRESS_TYPE.CURRENT,
        [ADDRESS_FORM_FIELD.PERIOD_OF_RESIDENCE_DURATION_TYPE]: DURATION_TYPES.YEAR,
        currentAddress: true,
        additionalProperty: 'value',
      },
    ]);
  });
});

describe('getInitialPrimaryCustomer()', () => {
  test('getInitialPrimaryCustomer returns 1 when the input array is empty', () => {
    const customers = [];
    expect(getInitialPrimaryCustomer(customers)).toBe(1);
  });

  test('getInitialPrimaryCustomer returns the correct index when the input array contains a buyer', () => {
    const customers = [{ type: BUYER_TYPE.BUYER }, { type: BUYER_TYPE.CO_BUYER }];
    expect(getInitialPrimaryCustomer(customers)).toBe(1);
  });

  test('getInitialPrimaryCustomer returns the correct index when the input array contains no buyers', () => {
    const customers = [{ type: BUYER_TYPE.CO_BUYER }];
    expect(getInitialPrimaryCustomer(customers)).toBe(0);
  });
});

describe('getFormValueForExistingCustomer()', () => {
  const mockCustomer = {
    customerId: 'customer-123',
    displayId: 'CUST-001',
    crmCustomerId: 'crm-456',
    firstName: 'John',
    lastName: 'Doe',
    customerType: 'PERSONAL',
    email: '<EMAIL>',
    mobilePhone: '**********',
    homePhone: '**********',
    workPhone: '**********',
    dateOfBirth: '1990-01-01',
    ssn: '***********',
    addresses: [
      {
        addressType: 'CURRENT',
        streetAddress: '123 Main St',
        city: 'Anytown',
        state: 'CA',
        zipCode: '12345',
        country: 'US',
      },
    ],
    drivingLicense: {
      licenseNumber: 'DL123456',
      state: 'CA',
      expirationDate: '2025-12-31',
    },
    insurance: {
      companyName: 'Insurance Co',
      policyNumber: 'POL123456',
      effectiveDate: '2023-01-01',
      expirationDate: '2024-01-01',
    },
    employment: {
      employerName: 'Tech Corp',
      position: 'Developer',
      workPhone: '**********',
      monthlyIncome: 5000,
    },
    attachments: [],
    contacts: [
      {
        name: 'Emergency Contact',
        phone: '**********',
      },
    ],
  };

  const mockCreditScore = 750;

  describe('Basic functionality', () => {
    it('should transform existing customer data to form values', () => {
      const customers = [mockCustomer];
      const result = getFormValueForExistingCustomer(customers, false, mockCreditScore);

      expect(result).toHaveLength(1);
      expect(result[0]).toHaveProperty('customerId', 'customer-123');
      expect(result[0]).toHaveProperty('displayId', 'CUST-001');
      expect(result[0]).toHaveProperty('crmCustomerId', 'crm-456');
      expect(result[0][FORM_FIELD.CUSTOMER_DETAILS]).toHaveProperty('firstName', 'John');
      expect(result[0][FORM_FIELD.CUSTOMER_DETAILS]).toHaveProperty('lastName', 'Doe');
      expect(result[0][FORM_FIELD.CUSTOMER_DETAILS]).toHaveProperty('customerType', 'PERSONAL');
    });

    it('should include credit score in customer details when provided', () => {
      const customers = [mockCustomer];
      const result = getFormValueForExistingCustomer(customers, false, mockCreditScore);

      expect(result[0][FORM_FIELD.CUSTOMER_DETAILS][APPLICANT_FORM_FIELD.CREDIT_VALUE]).toBe(mockCreditScore);
      expect(result[0][FORM_FIELD.CUSTOMER_DETAILS][APPLICANT_FORM_FIELD.CREDIT_RANGE_DETAILS]).toEqual({
        value: mockCreditScore,
        creditScoreType: 'SALES_SETUP_REPORTED',
      });
    });

    it('should handle null credit score', () => {
      const customers = [mockCustomer];
      const result = getFormValueForExistingCustomer(customers, false, null);

      expect(result[0][FORM_FIELD.CUSTOMER_DETAILS][APPLICANT_FORM_FIELD.CREDIT_VALUE]).toBeNull();
      expect(result[0][FORM_FIELD.CUSTOMER_DETAILS][APPLICANT_FORM_FIELD.CREDIT_RANGE_DETAILS]).toEqual({
        value: null,
        creditScoreType: 'SALES_SETUP_REPORTED',
      });
    });

    it('should handle undefined credit score (default parameter)', () => {
      const customers = [mockCustomer];
      const result = getFormValueForExistingCustomer(customers, false);

      expect(result[0][FORM_FIELD.CUSTOMER_DETAILS][APPLICANT_FORM_FIELD.CREDIT_VALUE]).toBeNull();
    });
  });

  describe('isAdded parameter behavior', () => {
    it('should return customer as-is when customer.added is false and isAdded is true', () => {
      const customerWithoutAdded = { ...mockCustomer, added: false };
      const customers = [customerWithoutAdded];
      const result = getFormValueForExistingCustomer(customers, true, mockCreditScore);

      expect(result[0]).toBe(customerWithoutAdded);
    });

    it('should process customer when customer.added is true and isAdded is true', () => {
      const customerWithAdded = { ...mockCustomer, added: true };
      const customers = [customerWithAdded];
      const result = getFormValueForExistingCustomer(customers, true, mockCreditScore);

      expect(result[0]).not.toBe(customerWithAdded);
      expect(result[0]).toHaveProperty('customerId', 'customer-123');
      expect(result[0][FORM_FIELD.CUSTOMER_DETAILS]).toHaveProperty('firstName', 'John');
    });

    it('should process customer when isAdded is false regardless of customer.added', () => {
      const customerWithAdded = { ...mockCustomer, added: true };
      const customers = [customerWithAdded];
      const result = getFormValueForExistingCustomer(customers, false, mockCreditScore);

      expect(result[0]).not.toBe(customerWithAdded);
      expect(result[0]).toHaveProperty('customerId', 'customer-123');
    });
  });

  describe('Multiple customers handling', () => {
    it('should handle multiple customers', () => {
      const customer2 = {
        ...mockCustomer,
        customerId: 'customer-456',
        firstName: 'Jane',
        lastName: 'Smith',
      };
      const customers = [mockCustomer, customer2];
      const result = getFormValueForExistingCustomer(customers, false, mockCreditScore);

      expect(result).toHaveLength(2);
      expect(result[0]).toHaveProperty('customerId', 'customer-123');
      expect(result[1]).toHaveProperty('customerId', 'customer-456');
      expect(result[0][FORM_FIELD.CUSTOMER_DETAILS]).toHaveProperty('firstName', 'John');
      expect(result[1][FORM_FIELD.CUSTOMER_DETAILS]).toHaveProperty('firstName', 'Jane');
    });

    it('should handle mix of added and non-added customers with isAdded=true', () => {
      const customer1 = { ...mockCustomer, added: false };
      const customer2 = { ...mockCustomer, customerId: 'customer-456', added: true, firstName: 'Jane' };
      const customers = [customer1, customer2];
      const result = getFormValueForExistingCustomer(customers, true, mockCreditScore);

      expect(result).toHaveLength(2);
      expect(result[0]).toBe(customer1);
      expect(result[1]).not.toBe(customer2);
      expect(result[1]).toHaveProperty('customerId', 'customer-456');
    });
  });

  describe('Edge cases', () => {
    it('should handle empty customers array', () => {
      const result = getFormValueForExistingCustomer([], false, mockCreditScore);
      expect(result).toEqual([]);
    });

    it('should handle customer with minimal data', () => {
      const minimalCustomer = {
        customerId: 'customer-minimal',
      };
      const customers = [minimalCustomer];
      const result = getFormValueForExistingCustomer(customers, false, mockCreditScore);

      expect(result).toHaveLength(1);
      expect(result[0]).toHaveProperty('customerId', 'customer-minimal');
      expect(result[0][FORM_FIELD.CUSTOMER_DETAILS]).toHaveProperty(APPLICANT_FORM_FIELD.CREDIT_VALUE, mockCreditScore);
    });

    it('should handle customer with null/undefined properties', () => {
      const customerWithNulls = {
        customerId: 'customer-nulls',
        firstName: null,
        lastName: undefined,
        email: null,
      };
      const customers = [customerWithNulls];
      const result = getFormValueForExistingCustomer(customers, false, mockCreditScore);

      expect(result).toHaveLength(1);
      expect(result[0]).toHaveProperty('customerId', 'customer-nulls');
      // Should handle null/undefined gracefully without throwing errors
    });

    it('should handle customer with empty arrays and objects', () => {
      const customerWithEmpties = {
        customerId: 'customer-empty',
        addresses: [],
        contacts: [],
        attachments: [],
      };
      const customers = [customerWithEmpties];
      const result = getFormValueForExistingCustomer(customers, false, mockCreditScore);

      expect(result).toHaveLength(1);
      expect(result[0]).toHaveProperty('customerId', 'customer-empty');
    });
  });

  describe('Data structure validation', () => {
    it('should include all required form fields', () => {
      const customers = [mockCustomer];
      const result = getFormValueForExistingCustomer(customers, false, mockCreditScore);

      const formValue = result[0];
      expect(formValue).toHaveProperty(FORM_FIELD.CUSTOMER_DETAILS);
      expect(formValue).toHaveProperty(FORM_FIELD.PERSONAL_ADDRESS);
      expect(formValue).toHaveProperty(FORM_FIELD.BUSINESS_ADDRESS);
      expect(formValue).toHaveProperty(FORM_FIELD.LICENSE_DETAILS);
      expect(formValue).toHaveProperty(FORM_FIELD.INSURANCE_DETAILS);
      expect(formValue).toHaveProperty(FORM_FIELD.EMPLOYMENT_DETAILS);
      expect(formValue).toHaveProperty(FORM_FIELD.GARAGING_ADDRESS);
      expect(formValue).toHaveProperty(FORM_FIELD.SHIPPING_ADDRESS);
      expect(formValue).toHaveProperty(FORM_FIELD.BILLING_ADDRESS);
      expect(formValue).toHaveProperty(FORM_FIELD.PRINCIPAL_OWNER);
      expect(formValue).toHaveProperty(FORM_FIELD.OTHER_INCOME_DETAILS);
      expect(formValue).toHaveProperty(FORM_FIELD.COMMUNICATION_PREFERENCES);
      expect(formValue).toHaveProperty(FORM_FIELD.PREFERRED_COMMUNICATION_MODE);
      expect(formValue).toHaveProperty(FORM_FIELD.MISCELLANEOUS);
    });

    it('should include communication preferences', () => {
      const customers = [mockCustomer];
      const result = getFormValueForExistingCustomer(customers, false, mockCreditScore);

      expect(result[0]).toHaveProperty('communicationPreferences');
    });

    it('should handle empty principal owner and other income details', () => {
      const customers = [mockCustomer];
      const result = getFormValueForExistingCustomer(customers, false, mockCreditScore);

      // Should provide default values for empty objects/arrays
      expect(result[0][FORM_FIELD.PRINCIPAL_OWNER]).toEqual([{}]);
      expect(result[0][FORM_FIELD.OTHER_INCOME_DETAILS]).toEqual({});
    });
  });

  describe('Address handling', () => {
    it('should use initial address when personal/business addresses are empty', () => {
      const customerWithoutAddresses = {
        ...mockCustomer,
        addresses: [],
      };
      const customers = [customerWithoutAddresses];
      const result = getFormValueForExistingCustomer(customers, false, mockCreditScore);

      // Should fall back to getInitialAddress() when addresses are empty
      expect(result[0][FORM_FIELD.PERSONAL_ADDRESS]).toBeDefined();
      expect(result[0][FORM_FIELD.BUSINESS_ADDRESS]).toBeDefined();
    });

    it('should handle various address types', () => {
      const customers = [mockCustomer];
      const result = getFormValueForExistingCustomer(customers, false, mockCreditScore);

      expect(result[0]).toHaveProperty(FORM_FIELD.GARAGING_ADDRESS);
      expect(result[0]).toHaveProperty(FORM_FIELD.SHIPPING_ADDRESS);
      expect(result[0]).toHaveProperty(FORM_FIELD.PREVIOUS_ADDRESS);
      expect(result[0]).toHaveProperty(FORM_FIELD.REGISTERED_ADDRESS);
      expect(result[0]).toHaveProperty(FORM_FIELD.TRADING_ADDRESS);
      expect(result[0]).toHaveProperty(FORM_FIELD.BILLING_ADDRESS);
    });
  });

  describe('Attachment handling', () => {
    it('should handle various attachment types', () => {
      const customers = [mockCustomer];
      const result = getFormValueForExistingCustomer(customers, false, mockCreditScore);

      expect(result[0]).toHaveProperty(FORM_FIELD.LICENSE_UPLOAD);
      expect(result[0]).toHaveProperty(FORM_FIELD.LICENSE_UPLOAD_BACK);
      expect(result[0]).toHaveProperty(FORM_FIELD.INSURANCE_UPLOAD);
      expect(result[0]).toHaveProperty(FORM_FIELD.EMPLOYMENT_UPLOAD);
    });
  });
});
