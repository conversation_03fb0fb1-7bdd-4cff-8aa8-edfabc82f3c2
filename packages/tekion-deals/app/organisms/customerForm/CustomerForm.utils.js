import _size from 'lodash/size';
import _map from 'lodash/map';
import _isEmpty from 'lodash/isEmpty';
import _values from 'lodash/values';
import _get from 'lodash/get';
import _some from 'lodash/some';
import _omit from 'lodash/omit';
import _omitBy from 'lodash/omitBy';
import _difference from 'lodash/difference';
import _isNil from 'lodash/isNil';
import _filter from 'lodash/filter';
import _pick from 'lodash/pick';
import _compact from 'lodash/compact';
import _set from 'lodash/set';
import _toNumber from 'lodash/toNumber';
import _floor from 'lodash/floor';
import _find from 'lodash/find';
import _forEach from 'lodash/forEach';
import _cloneDeep from 'lodash/cloneDeep';
import _isNull from 'lodash/isNull';

import { RESIDENT_TYPE, RESIDENT_TYPE_LABEL } from '@tekion/tekion-base/constants/deal/customer';
import { tget } from '@tekion/tekion-base/utils/general';

import { toMoment, getTimeStamp, isValidDate } from '@tekion/tekion-base/utils/dateUtils';
import { formatSocialSecurityNumber } from '@tekion/tekion-base/formatters/number';

import { getNumberFromString, getStartOfDayTimeStamp, getPhoneNumberValue } from 'utils';
import { BUYER_TYPE } from 'pages/deallist/deal.constants';
import { DEAL_TYPES } from '@tekion/tekion-base/constants/deal/type';
import { EMPTY_OBJECT, EMPTY_STRING, EMPTY_ARRAY, NO_DATA } from '@tekion/tekion-base/app.constants';
import {
  getLicenseData,
  getCustomerData,
  getAddressData,
  getInsuranceData,
  getEmployerData,
  getCustomerNumberForDisplay,
  getContactPreferences,
  getContactBasedOnTypes,
  parseCustomerAddress,
} from '@tekion/tekion-base/marketScan/readers/customer.reader';
import { DSE_CONTACT_TYPES } from '@tekion/tekion-base/marketScan/constants/constants';
import { getCustomers } from '@tekion/tekion-base/marketScan/readers/deal.reader';
import {
  BUSINESS_TYPE_LABEL,
  BUSINESS_TYPE_INCHCAPE,
  BUSINESS_TYPE,
} from '@tekion/tekion-base/marketScan/constants/customerForm.constants';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';
import { ADDRESS_KEYS_FOR_UPDATING_OTHER_ADDRESS } from '@tekion/tekion-widgets/src/appServices/sales/templates/creditApplicationForm/tabs/applicants/forms/addressDetailsForm/addressDetailsForm.fields';
import * as CreditUtils from 'twidgets/appServices/sales/templates/creditApplicationForm/creditCustomer.util';
import { isInchcape } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
import numberFormatter from '@tekion/tekion-business/src/factories/formatters/numberFormatter';
import { DEALS_EMPLOYMENT_TYPE } from '@tekion/tekion-base/constants/customer.constants';
import {
  LEAD_VS_DEAL_DOCUMENT_TYPES,
  LEAD_DOCUMENT_TYPES,
  ADDRESS_TYPE,
  EMPLOYER_TYPE,
  CUSTOMER_TYPE,
  EMPLOYMENT_TYPE_LABEL,
  PRIMARY_DRIVER_TYPE,
  MEDIA_TYPE,
  FORM_FIELD,
  EMPLOYER_CATEGORIES,
  EMPLOYER_CATEGORIES_LABEL,
  DURATION_TYPES,
  SAME_AS_CURRENT_ADDRESS_TYPE,
  INSURANCE_DETAILS_TO_BE_IGNORED,
  INDUSTRY,
  INDUSTRY_LABEL,
  BUSINESS_CLASSIFICATION,
  BUSINESS_CLASSIFICATION_LABEL,
  NUMBER_OF_EMPLOYEES,
  VAT_REGISTERED_VALUES,
  MIME_TYPE_MAPPING,
} from './CustomerForm.constants';

import { FORM_FIELD as ADDRESS_FORM_FIELD } from './components/addressForm/AddressForm.constants';
import { FORM_FIELD as EMPLOYMENT_FORM_FIELD } from './components/employmentInfoForm/EmploymentInfoForm.constants';
import {
  FORM_FIELD as APPLICANT_FORM_FIELD,
  CREDIT_SCORE_TYPE,
} from './components/applicantsDetailsForm/ApplicantsDetailsForm.constants';
import { FORM_FIELD as LICENSE_FORM_FIELD } from './components/licenseForm/LicenseForm.constants';
import { FORM_FIELD as INSURANCE_FORM_FIELD } from './components/insuranceForm/InsuranceForm.constants';
import { FORM_FIELD as DRIVER_FORM_FIELD } from './components/primaryDriverDetails/PrimaryDriverDetails.constants';
import { FORM_FIELD as CONTRACTING_FIELD } from './components/contractingDetails/contractingDetails.constants';

import {
  getCustomersData,
  getAddress,
  getLicenseDetails,
  getInsuranceDetails,
  getEmploymentDetails,
  getPrimaryDriver,
  getInsuranceAgencyDetails,
  getEmploymentDetailsForPayload,
  getPrimaryDriverForPayload,
  getAllAddressesByOmitingMarketId,
  getContractingDetailsForPayload,
  getPrincipalOwner,
  getOtherIncomeDetails,
  getCommunicationPreferences,
  getPrincipalOwnerDetails,
  getMiscellaneousSectionData,
  getCommunicationMode,
  getCustomerMedia,
  getMediaByType,
  getLicenseDetailsFromLead,
} from './customerFormReader';

const getMoment = date => (date ? toMoment(date) : null);

export const getInitialAddress = (addressType = ADDRESS_TYPE.CURRENT, rest = {}) => [
  {
    [ADDRESS_FORM_FIELD.ADDRESS_TYPE]: addressType,
    [ADDRESS_FORM_FIELD.PERIOD_OF_RESIDENCE_DURATION_TYPE]: DURATION_TYPES.YEAR,
    [ADDRESS_FORM_FIELD.CURRENT_ADDRESS]: addressType === ADDRESS_TYPE.CURRENT,
    // tradingAddress: addressType === ADDRESS_TYPE.TRADING,
    ...rest,
  },
];

export const getInitialEmployment = (employmentType = EMPLOYER_TYPE.CURRENT) => [
  {
    [EMPLOYMENT_FORM_FIELD.EMPLOYER_TYPE]: employmentType,
    [EMPLOYMENT_FORM_FIELD.PERIOD_OF_EMPLOYMENT_DURATION_TYPE]: DURATION_TYPES.YEAR,
  },
];

export const getInitialDriverDetail = (driverDetails = EMPTY_OBJECT) => {
  if (_isEmpty(driverDetails)) {
    return {
      [DRIVER_FORM_FIELD.PRIMARY_DRIVER_TYPE]: PRIMARY_DRIVER_TYPE.OTHER,
    };
  }
  return {
    [DRIVER_FORM_FIELD.FIRST_NAME]: driverDetails?.firstName || EMPTY_STRING,
    [DRIVER_FORM_FIELD.MIDDLE_NAME]: driverDetails?.middleName || EMPTY_STRING,
    [DRIVER_FORM_FIELD.LAST_NAME]: driverDetails?.lastName || EMPTY_STRING,
    [DRIVER_FORM_FIELD.PRIMARY_DRIVER_TYPE]: driverDetails?.driverType || EMPTY_STRING,
    [DRIVER_FORM_FIELD.EMAIL]: driverDetails?.email?.value || EMPTY_STRING,
    [DRIVER_FORM_FIELD.MOBILE]: driverDetails?.cellPhone?.value || EMPTY_STRING,
    [DRIVER_FORM_FIELD.ADDRESS]: driverDetails?.address?.address1 || EMPTY_STRING,
    [DRIVER_FORM_FIELD.ZIP_CODE]: driverDetails?.address?.zipCode || EMPTY_STRING,
    [DRIVER_FORM_FIELD.CITY]: driverDetails?.address?.city || EMPTY_STRING,
    [DRIVER_FORM_FIELD.COUNTY]: driverDetails?.address?.county || EMPTY_STRING,
    [DRIVER_FORM_FIELD.STATE]: driverDetails?.address?.state || EMPTY_STRING,
    [DRIVER_FORM_FIELD.COUNTRY]: driverDetails?.address?.country || EMPTY_STRING,
  };
};

export const getInitialValue = (
  dealType,
  isBuyerAlreadyPresent = false,
  driverDetails = EMPTY_OBJECT,
  creditScore = null
) => {
  if (isInchcape()) {
    return {
      [FORM_FIELD.CUSTOMER_DETAILS]: {
        customerType: isBuyerAlreadyPresent ? CUSTOMER_TYPE.PERSONAL : getCustomerTypeBasedOnDealType(dealType), // The Initial Co-Buyer's customer type should be personal
        [FORM_FIELD.EXTERNAL_CUSTOMER_ID]: null,
        [APPLICANT_FORM_FIELD.CREDIT_VALUE]: creditScore,
        [APPLICANT_FORM_FIELD.CREDIT_RANGE_DETAILS]: {
          value: creditScore,
          creditScoreType: CREDIT_SCORE_TYPE.SALES_SETUP_REPORTED,
        },
      },
      [FORM_FIELD.PERSONAL_ADDRESS]: getInitialAddress(),
      [FORM_FIELD.BUSINESS_ADDRESS]: getInitialAddress(),
      [FORM_FIELD.LICENSE_DETAILS]: {},
      [FORM_FIELD.INSURANCE_DETAILS]: {},
      [FORM_FIELD.EMPLOYMENT_DETAILS]: getInitialEmployment(),
      type: isBuyerAlreadyPresent ? BUYER_TYPE.CO_BUYER : BUYER_TYPE.BUYER,
      [FORM_FIELD.REGISTERED_ADDRESS]: getInitialAddress(ADDRESS_TYPE.REGISTERED, {
        [ADDRESS_FORM_FIELD.SAME_AS_TRADING_ADDRESS]: SAME_AS_CURRENT_ADDRESS_TYPE.SAME_AS_TRADING,
      }),
    };
  }

  return {
    [FORM_FIELD.CUSTOMER_DETAILS]: {
      customerType: isBuyerAlreadyPresent ? CUSTOMER_TYPE.PERSONAL : getCustomerTypeBasedOnDealType(dealType), // The Initial Co-Buyer's customer type should be personal
      [FORM_FIELD.EXTERNAL_CUSTOMER_ID]: null,
      [APPLICANT_FORM_FIELD.CREDIT_VALUE]: creditScore,
      [APPLICANT_FORM_FIELD.CREDIT_RANGE_DETAILS]: {
        value: creditScore,
        creditScoreType: CREDIT_SCORE_TYPE.SALES_SETUP_REPORTED,
      },
    },
    [FORM_FIELD.PERSONAL_ADDRESS]: getInitialAddress(),
    [FORM_FIELD.BUSINESS_ADDRESS]: getInitialAddress(),
    [FORM_FIELD.LICENSE_DETAILS]: {},
    [FORM_FIELD.INSURANCE_DETAILS]: {},
    [FORM_FIELD.EMPLOYMENT_DETAILS]: getInitialEmployment(),
    [FORM_FIELD.GARAGING_ADDRESS]: getInitialAddress(ADDRESS_TYPE.GARAGE, {
      [ADDRESS_FORM_FIELD.SAME_AS_CURRENT_ADDRESS]: SAME_AS_CURRENT_ADDRESS_TYPE.SAME_AS_CURRENT,
    }),
    [FORM_FIELD.SHIPPING_ADDRESS]: getInitialAddress(ADDRESS_TYPE.SHIPPING, {
      [ADDRESS_FORM_FIELD.SAME_AS_CURRENT_ADDRESS]: SAME_AS_CURRENT_ADDRESS_TYPE.SAME_AS_CURRENT,
    }),
    [FORM_FIELD.BILLING_ADDRESS]: getInitialAddress(ADDRESS_TYPE.BILLING, {
      [ADDRESS_FORM_FIELD.SAME_AS_CURRENT_ADDRESS]: SAME_AS_CURRENT_ADDRESS_TYPE.SAME_AS_CURRENT,
    }),
    [FORM_FIELD.PRIMARY_DRIVER]: getInitialDriverDetail(driverDetails),
    type: isBuyerAlreadyPresent ? BUYER_TYPE.CO_BUYER : BUYER_TYPE.BUYER,

    [FORM_FIELD.REGISTERED_ADDRESS]: getInitialAddress(ADDRESS_TYPE.REGISTERED, {
      [ADDRESS_FORM_FIELD.SAME_AS_TRADING_ADDRESS]: SAME_AS_CURRENT_ADDRESS_TYPE.SAME_AS_TRADING,
    }),
  };
};

export const intialErrorState = {
  [FORM_FIELD.CUSTOMER_DETAILS]: {},
  [FORM_FIELD.LICENSE_DETAILS]: {},
  [FORM_FIELD.INSURANCE_DETAILS]: {},
  [FORM_FIELD.PRIMARY_DRIVER]: {},
  [FORM_FIELD.CONTRACTING]: {},
  [FORM_FIELD.EMPLOYMENT_DETAILS]: {},
  [FORM_FIELD.PERSONAL_ADDRESS]: {},
  [FORM_FIELD.BUSINESS_ADDRESS]: {},
  [FORM_FIELD.GARAGING_ADDRESS]: {},
  [FORM_FIELD.REGISTERED_ADDRESS]: {},
  [FORM_FIELD.SHIPPING_ADDRESS]: {},
  [FORM_FIELD.BILLING_ADDRESS]: {},
};

export const getInitialErrorState = (customers = []) =>
  customers?.length ? _map(customers, () => intialErrorState) : [intialErrorState];

export const getInitialForm = (customers, dealType) => {
  if (_size(customers)) {
    return getFormDataFromPayload(customers, dealType);
  }
  return [getInitialValue(dealType)];
};

export const getInitialPrimaryCustomer = customers => {
  if (!_size(customers)) return 1;

  return customers.findIndex(customer => customer.type === BUYER_TYPE.BUYER) + 1;
};

export const getInitialGuarantorValue = customers => {
  if (!_size(customers)) return false;
  return _get(
    customers.find(({ type }) => type === BUYER_TYPE.CO_BUYER),
    'guarantor'
  );
};

const getCustomerTypeBasedOnDealType = dealType => {
  switch (dealType) {
    case DEAL_TYPES.BUSINESS:
    case DEAL_TYPES.WHOLESALE:
    case DEAL_TYPES.DEALER_TRADE:
    case DEAL_TYPES.INTERNAL_TRANSFER:
    case DEAL_TYPES.FLEET:
      return CUSTOMER_TYPE.BUSINESS;
    default:
      return CUSTOMER_TYPE.PERSONAL;
  }
};

const getCustomerType = (customer, dealType) => {
  if (customer.type === BUYER_TYPE.BUYER) return getCustomerTypeBasedOnDealType(dealType);
  return _get(customer, 'buyerTypeNum') || CUSTOMER_TYPE.PERSONAL;
};

// PART 1
/* START: Upon selecting lead from duplicate lead. lead is mapped to customer and Address section starts here */
const getLeadAddressMappedToAddressSection = ({ address1, county: countyName, ...rest }) => ({
  address1: _get(address1, 'address1'),
  countyName,
  currentAddress: true,
  addressType: 'CURRENT',
  ...rest,
});

const getLeadCustomerMappedToCustomerSection = (
  { dateOfBirth, anniversary, buyerType, cellPhone, ...rest },
  leadId,
  customerID
) => ({
  customerType: buyerType === 'BUSINESS' ? CUSTOMER_TYPE.BUSINESS : CUSTOMER_TYPE.PERSONAL,
  mobileNo: cellPhone,
  leadId,
  customerId: customerID,
  ...rest,
});

const mapDuplicateLeadDataToCustomerForm = (dealType, customerID, leadId, buyer, isBuyerPresent) => {
  const { basic, address } = buyer || {};
  const initialFormData = getInitialValue(dealType, isBuyerPresent);
  const customerSection = getLeadCustomerMappedToCustomerSection(basic || {}, leadId, customerID);
  _set(initialFormData, 'customer', customerSection);
  _set(initialFormData, 'customerId', customerID);

  const { customerType } = customerSection;
  if (customerType === CUSTOMER_TYPE.PERSONAL)
    _set(initialFormData, [FORM_FIELD.PERSONAL_ADDRESS, 0], getLeadAddressMappedToAddressSection(address || {}));
  else _set(initialFormData, [FORM_FIELD.BUSINESS_ADDRESS, 0], getLeadAddressMappedToAddressSection(address || {}));
  return initialFormData;
};

export const getFormDataFromDuplicateLead = (dealType, lead, presentCoBuyer) => {
  const { buyerCustomerId, coBuyerCustomerId, buyer, coBuyer, leadId } = lead;
  const buyerFormData = mapDuplicateLeadDataToCustomerForm(dealType, buyerCustomerId, leadId, buyer, false);
  const coBuyerFormData = !_isEmpty(coBuyer)
    ? mapDuplicateLeadDataToCustomerForm(dealType, coBuyerCustomerId, leadId, coBuyer, true)
    : presentCoBuyer || {};
  if (!_isEmpty(coBuyerFormData)) return [buyerFormData, coBuyerFormData];
  return [buyerFormData];
};
/* END: Upon selecting lead from duplicate lead. lead is mapped to customer and Address section ends here */

// PART 2
/* START:  Upon selecting customer from duplicate lead. customer is mapped to customer and Address section starts here */
const getCustomerAddressMappedToAddressSection = ({
  line1: address1,
  line2: address2,
  city,
  country,
  county: countyName,
  state,
  postalCode: zipCode,
}) => ({
  address1,
  address2,
  city,
  country,
  countyName,
  state,
  zipCode,
  currentAddress: true,
  addressType: 'CURRENT',
});

const getCustomerMappedToCustomerSection = (
  { firstName, lastName, middleName, email, phones },
  customerType,
  customerId
) => {
  const contacts = _map(phones, ({ phoneType: type, ...rest }) => ({
    type,
    ...rest,
  }));
  return {
    firstName,
    lastName,
    middleName,
    email,
    customerType,
    mobileNo: getContactBasedOnTypes(contacts, DSE_CONTACT_TYPES.MOBILE),
    homePhone: getContactBasedOnTypes(contacts, DSE_CONTACT_TYPES.HOME),
    workPhone: getContactBasedOnTypes(contacts, DSE_CONTACT_TYPES.WORK),
    customerId,
  };
};

export const getFormDataFromDuplicateCustomer = (dealType, customer, customerType, formType) => {
  const { address, id } = customer || {};
  const initialFormData = getInitialValue(dealType, formType === BUYER_TYPE.CO_BUYER);
  const customerSection = getCustomerMappedToCustomerSection(customer, customerType, id);
  _set(initialFormData, 'customer', customerSection);
  _set(initialFormData, 'customerId', id);

  if (customerType === CUSTOMER_TYPE.PERSONAL)
    _set(initialFormData, [FORM_FIELD.PERSONAL_ADDRESS, 0], getCustomerAddressMappedToAddressSection(address || {}));
  else _set(initialFormData, [FORM_FIELD.BUSINESS_ADDRESS, 0], getCustomerAddressMappedToAddressSection(address || {}));
  return initialFormData;
};
/* END: Upon selecting customer from duplicate lead. customer is mapped to customer and Address section ends here */

// PART 3
/* START Select Lead From Dialog Starts here */
const getLeadDialogAddressMappedToAddressSection = ({ county: countyName, ...rest }) => ({
  countyName,
  currentAddress: true,
  addressType: 'CURRENT',
  ...rest,
});

const getLeadDialogCustomerMappedToCustomerSection = (customer, customerId, isBuyerPresent, leadId, vaultInfo) => {
  const {
    demographics,
    businessType,
    email,
    firstName,
    homePhone,
    incorporationState,
    incorporationTime,
    lastName,
    legalName,
    middleName,
    taxId,
    tradeName,
    workPhone,
    buyerType,
    cellPhone,
  } = customer || {};
  const { birthDate, dateOfBirth } = demographics || {};
  const { day, month, year } = dateOfBirth || {};
  const customerType = buyerType === 'BUSINESS' ? CUSTOMER_TYPE.BUSINESS : CUSTOMER_TYPE.PERSONAL;
  const getValue = input => _get(input, 'value');
  const isBusinessCustomer = customerType === CUSTOMER_TYPE.BUSINESS;
  return {
    birthDate: getMoment(_toNumber(birthDate)),
    dob: {
      dayOfTheMonth: day,
      month,
      year,
    },
    businessType,
    email: getValue(email),
    firstName,
    homePhone: getValue(homePhone),
    incorporationState,
    incorporationTime: incorporationTime ? getMoment(_toNumber(incorporationTime)) : null,
    lastName,
    legalName,
    middleName,
    ssn: isBusinessCustomer ? EMPTY_STRING : formatSocialSecurityNumber(_get(vaultInfo, 'ssn') || EMPTY_STRING),
    taxId,
    tradeName,
    workPhone: getValue(workPhone),
    leadId,
    mobileNo: getValue(cellPhone),
    customerType,
    customerId,
  };
};

const getLeadDialogEmployerMappedToCustomerSection = customer => {
  const { documents } = customer || {};
  const employerInfo = _find(documents, { documentType: LEAD_DOCUMENT_TYPES.EMPLOYER }) || {};
  const {
    periodOfEmployment,
    periodOfEmploymentDurationType,
    status,
    employmentType,
    salary,
    salaryType,
    employerType,
    name,
    address,
    employerPhone,
    email,
    occupation,
  } = employerInfo;
  const { address1, address2, city, county, country, state, zipCode } = address || {};
  return [
    {
      currentEmployment: EMPLOYER_TYPE.CURRENT,
      periodOfEmploymentDurationType,
      status,
      employmentType,
      periodOfEmployment,
      grossIncome: salary,
      grossIncomeInterval: salaryType,
      employerType,
      employerName: name,
      address1: address1 || address2,
      country,
      zipCode,
      city,
      countyName: county,
      state,
      phoneNum: employerPhone || null,
      emailId: email,
      occupation,
      employeeId: null,
    },
  ];
};

const getLeadDialogInsuranceMappedToCustomerSection = customer => {
  const { documents } = customer || {};
  const insuranceInfo = _find(documents, { documentType: LEAD_DOCUMENT_TYPES.INSURANCE }) || {};
  const { policyNumber, insurer, effectiveDate, expiryDate, collision, comp, agencyPhone, address } = insuranceInfo;
  const { address1, address2, city, county, country, state, zipCode } = address || {};
  const getMomentDate = time => (time ? getMoment(_toNumber(time)) : null);
  return {
    policyNumber,
    insuranceCompany: insurer,
    effectiveTime: getMomentDate(effectiveDate),
    expirationTime: getMomentDate(expiryDate),
    deductibleCollision: collision,
    deductibleComprehensive: comp,
    agencyDetails: {
      name: null,
      phoneNume: null,
      address: null,
      city: null,
      zipCode: null,
      county: null,
      country: null,
      state: null,
      address2: null,
    },
    cmsInsuranceId: null,
    name: insurer,
    phoneNume: agencyPhone || null,
    address: address1 || address2,
    city,
    zipCode,
    county,
    country,
    state,
  };
};

const getLeadDialogLicenseDetailsMappedToCustomerSection = (customer, valutInfo) => {
  const { documents } = customer || {};
  const licenceInfo = _find(documents, { documentType: LEAD_DOCUMENT_TYPES.DRIVING_LICENSE }) || {};
  const { expiryDate, issuingTime, registration } = licenceInfo;
  const getMomentDate = time => (time ? getMoment(_toNumber(time)) : null);
  return {
    expirationTime: expiryDate ? getMomentDate(expiryDate) : null,
    issuingState: registration,
    issuingTime,
    licenseNumber: _get(valutInfo, 'drivingLicenseNumber'),
  };
};

const getLeadMediaMappedToCustomerForm = (customer, documentType) => {
  const { documents } = customer || {};
  const info = _find(documents, { documentType }) || EMPTY_OBJECT;
  const media = formatCustomerMedia(info);
  return media || EMPTY_ARRAY;
};

const initialCustomerDataFromLeadToCustomerForm = (
  dealType,
  customerId,
  customer,
  isBuyerPresent,
  leadId,
  valutInfo,
  motabilityLead
) => {
  const { address } = customer || {};
  const driverDetails = _get(customer, 'driverDetails') || EMPTY_OBJECT;
  const initialFormData = getInitialValue(dealType, isBuyerPresent, driverDetails);
  const customerSection =
    getLeadDialogCustomerMappedToCustomerSection(customer, customerId, isBuyerPresent, leadId, valutInfo) || {};
  _set(initialFormData, 'customer', customerSection);
  _set(initialFormData, 'customerId', customerId);

  const employerSection = getLeadDialogEmployerMappedToCustomerSection(customer);
  const insuranceSection = getLeadDialogInsuranceMappedToCustomerSection(customer);
  const licenseSection = getLeadDialogLicenseDetailsMappedToCustomerSection(customer, valutInfo);
  _set(initialFormData, 'employmentDetails', employerSection);
  _set(initialFormData, 'insuranceDetails', insuranceSection);
  _set(initialFormData, 'licenseDetails', licenseSection);
  _set(
    initialFormData,
    [FORM_FIELD.EMPLOYMENT_UPLOAD],
    getLeadMediaMappedToCustomerForm(customer, LEAD_DOCUMENT_TYPES.EMPLOYER)
  );
  _set(
    initialFormData,
    [FORM_FIELD.INSURANCE_UPLOAD],
    getLeadMediaMappedToCustomerForm(customer, LEAD_DOCUMENT_TYPES.INSURANCE)
  );
  _set(
    initialFormData,
    [FORM_FIELD.LICENSE_UPLOAD],
    getLeadMediaMappedToCustomerForm(customer, LEAD_DOCUMENT_TYPES.DRIVING_LICENSE)
  );
  _set(
    initialFormData,
    [FORM_FIELD.LICENSE_UPLOAD_BACK],
    getLeadMediaMappedToCustomerForm(customer, LEAD_DOCUMENT_TYPES.DRIVING_LICENSE_BACK)
  );

  _set(initialFormData, 'motabilityLead', motabilityLead);

  const { customerType } = customerSection;
  if (customerType === CUSTOMER_TYPE.PERSONAL)
    _set(initialFormData, [FORM_FIELD.PERSONAL_ADDRESS, 0], getLeadDialogAddressMappedToAddressSection(address || {}));
  else
    _set(initialFormData, [FORM_FIELD.BUSINESS_ADDRESS, 0], getLeadDialogAddressMappedToAddressSection(address || {}));
  return initialFormData;
};

export const getFormDataFromLead = (dealType, lead, presentCoBuyer, buyerValutInfo, coBuyerValultInfo) => {
  const { buyerCustomerId, coBuyerCustomerId, buyer, coBuyer, leadId, motabilityLead } = lead;
  const buyerFormData = initialCustomerDataFromLeadToCustomerForm(
    dealType,
    buyerCustomerId,
    buyer,
    false,
    leadId,
    buyerValutInfo,
    motabilityLead
  );
  const coBuyerFormData = !_isEmpty(coBuyer)
    ? initialCustomerDataFromLeadToCustomerForm(dealType, coBuyerCustomerId, coBuyer, true, leadId, coBuyerValultInfo)
    : presentCoBuyer || {};
  if (!_isEmpty(coBuyerFormData)) return [buyerFormData, coBuyerFormData];
  return [buyerFormData];
};

export const getAddressByType = (customer, addressType) => {
  const addressDetails = _get(customer, 'address') || _get(customer, 'addresses') || EMPTY_ARRAY;

  let requiredAddress = null;
  let currentAddress = null;

  _forEach(addressDetails, address => {
    if (address.addressType === addressType) {
      requiredAddress = _cloneDeep(address);
      requiredAddress.addressType = addressType;
    }
    if (address.addressType === ADDRESS_TYPE.CURRENT) {
      currentAddress = address;
    }
  });
  const formattedCurrentAddress = !_isEmpty(currentAddress)
    ? parseCustomerAddress([currentAddress])?.[0]
    : EMPTY_OBJECT;

  return _isEmpty(requiredAddress)
    ? getInitialAddress(addressType, {
        ..._pick(formattedCurrentAddress, ADDRESS_KEYS_FOR_UPDATING_OTHER_ADDRESS),
        [ADDRESS_FORM_FIELD.SAME_AS_CURRENT_ADDRESS]: SAME_AS_CURRENT_ADDRESS_TYPE.SAME_AS_CURRENT,
      })
    : [
        {
          ...requiredAddress,
          [ADDRESS_FORM_FIELD.SAME_AS_CURRENT_ADDRESS]: CreditUtils.checkIfGivenAddressIsSameAsCurrentAddress(
            requiredAddress,
            currentAddress
          )
            ? SAME_AS_CURRENT_ADDRESS_TYPE.SAME_AS_CURRENT
            : SAME_AS_CURRENT_ADDRESS_TYPE.OTHER,
        },
      ];
};

export const getAddressByType2 = (customer, addressType) => {
  const addressDetails = _get(customer, 'address') || _get(customer, 'addresses') || EMPTY_ARRAY;

  let requiredAddress = null;
  let currentAddress = null;

  _forEach(addressDetails, address => {
    if (address.addressType === addressType) {
      requiredAddress = _cloneDeep(address);
      requiredAddress.addressType = addressType;
    }
    if (address.addressType === ADDRESS_TYPE.CURRENT) {
      currentAddress = address;
    }
  });

  return _isEmpty(requiredAddress)
    ? getInitialAddress(addressType)
    : [
        {
          ...requiredAddress,
          [ADDRESS_FORM_FIELD.SAME_AS_CURRENT_ADDRESS]: CreditUtils.checkIfGivenAddressIsSameAsCurrentAddress(
            requiredAddress,
            currentAddress
          )
            ? SAME_AS_CURRENT_ADDRESS_TYPE.SAME_AS_CURRENT
            : SAME_AS_CURRENT_ADDRESS_TYPE.OTHER,
        },
      ];
};

/* END Select Lead From Lead Dialog Ends here */

const getCustomerMediaPayload = (mediaList, type) =>
  _map(mediaList, ({ file, mediaId, name, contentType, mediaSize, originalFileName }) => ({
    type,
    mediaId,
    name: originalFileName || name,
    mediaSize: _get(file, 'file.size', mediaSize),
    contentType: MIME_TYPE_MAPPING[contentType] || contentType,
  }));

const getCreditScoreRange = customer => {
  const minCreditScore = _get(customer, 'creditScoreDetails.min', null);
  const maxCreditScore = _get(customer, 'creditScoreDetails.max', null);
  const creditScore = _get(customer, 'creditScoreDetails.value', null);

  let creditScoreInfo = null;
  if (!_isNull(minCreditScore) || !_isNull(maxCreditScore)) {
    const min = !_isNull(minCreditScore) ? minCreditScore : maxCreditScore;
    const max = !_isNull(maxCreditScore) ? maxCreditScore : minCreditScore;
    creditScoreInfo = `${min} - ${max}`;
  } else if (creditScore != null) {
    creditScoreInfo = creditScore;
  }
  return creditScoreInfo;
};

export const getCustomerFormDataFromPayLoad = (customer, dealType) => {
  // intial
  const { buyerTypeNum } = customer || {};
  const address = getAddress(customer);
  const customerData = getCustomersData(customer);
  const licenseDetails = getLicenseDetails(customer);
  const insuranceDetails = getInsuranceDetails(customer);
  // const garagingAddress = getGaragingAddress(customer);

  const garagingAddress = getAddressByType2(customer, ADDRESS_TYPE.GARAGE);
  // const tradingAddress = getAddressByType(customer, ADDRESS_TYPE.TRADING);
  const registeredAddress = getAddressByType2(customer, ADDRESS_TYPE.REGISTERED);
  const shippingAddress = getAddressByType2(customer, ADDRESS_TYPE.SHIPPING);
  const customerMedia = getCustomerMedia(customer);
  const driverDetails = getPrimaryDriver(customer);
  const customerType = _isNil(buyerTypeNum) ? getCustomerType(customer, dealType) : buyerTypeNum;
  const employmentDetails = getEmploymentDetails(customer);
  const customerIdDisplayValue = getCustomerNumberForDisplay(customer); // used only for displaying in customer form
  const billingAddress = getAddressByType2(customer, ADDRESS_TYPE.BILLING);
  const principalOwner = getPrincipalOwner(customer);
  const otherIncomeDetails = getOtherIncomeDetails(customer);
  const communicationPreferences = getCommunicationPreferences(customer);
  const preferredCommunicationMode = { [FORM_FIELD.PREFERRED_COMMUNICATION_MODE]: getCommunicationMode(customer) };
  const miscellaneousSectionData = getMiscellaneousSectionData(customer);
  const creditScoreRange = getCreditScoreRange(customer);
  return {
    ...(DealerPropertyHelper.isUSUploadDocSyncCrm() ? _omit(customer, [FORM_FIELD.CUSTOMER_MEDIA]) : customer),
    [FORM_FIELD.CUSTOMER_DETAILS]: {
      customerType,
      ...customerData,
      [APPLICANT_FORM_FIELD.SSN]: formatSocialSecurityNumber(customerData[APPLICANT_FORM_FIELD.SSN]),
      [APPLICANT_FORM_FIELD.MOBILE]: customerData[APPLICANT_FORM_FIELD.MOBILE],
      [APPLICANT_FORM_FIELD.HOME_PHONE]: customerData[APPLICANT_FORM_FIELD.HOME_PHONE],
      [APPLICANT_FORM_FIELD.WORK_PHONE]: customerData[APPLICANT_FORM_FIELD.WORK_PHONE],
      [APPLICANT_FORM_FIELD.BUSINESS_PHONE]: customerData[APPLICANT_FORM_FIELD.BUSINESS_PHONE],
      [APPLICANT_FORM_FIELD.DOB]: getMoment(customerData[APPLICANT_FORM_FIELD.DOB]),
      [APPLICANT_FORM_FIELD.DATE_OF_INCORPORATION]: getMoment(customerData[APPLICANT_FORM_FIELD.DATE_OF_INCORPORATION]),
      [APPLICANT_FORM_FIELD.CUSTOMER_ID]: customerIdDisplayValue,
      [APPLICANT_FORM_FIELD.CONTACTS]: _get(customer, APPLICANT_FORM_FIELD.CONTACTS) || EMPTY_ARRAY,
      [APPLICANT_FORM_FIELD.CREDIT_VALUE]: creditScoreRange,
      [APPLICANT_FORM_FIELD.CREDIT_RANGE_DETAILS]: _get(customer, 'creditScoreDetails', EMPTY_OBJECT),
    },
    [FORM_FIELD.PERSONAL_ADDRESS]:
      customerType === CUSTOMER_TYPE.PERSONAL && !_isEmpty(address) ? address : getInitialAddress(),
    [FORM_FIELD.BUSINESS_ADDRESS]:
      customerType === CUSTOMER_TYPE.BUSINESS && !_isEmpty(address) ? address : getInitialAddress(),
    [FORM_FIELD.LICENSE_DETAILS]: {
      ...licenseDetails,
      [LICENSE_FORM_FIELD.EXPIRATION_DATE]: getMoment(licenseDetails[LICENSE_FORM_FIELD.EXPIRATION_DATE]),
      [LICENSE_FORM_FIELD.ISSUING_DATE]: getMoment(licenseDetails[LICENSE_FORM_FIELD.ISSUING_DATE]),
    },
    [FORM_FIELD.INSURANCE_DETAILS]: {
      ...insuranceDetails,
      [INSURANCE_FORM_FIELD.EFFECTIVE_DATE]: getMoment(insuranceDetails[INSURANCE_FORM_FIELD.EFFECTIVE_DATE]),
      [INSURANCE_FORM_FIELD.EXPIRATION_DATE]: getMoment(insuranceDetails[INSURANCE_FORM_FIELD.EXPIRATION_DATE]),
      [INSURANCE_FORM_FIELD.AGENCY_PHONE]: insuranceDetails[INSURANCE_FORM_FIELD.AGENCY_PHONE],
    },
    [FORM_FIELD.EMPLOYMENT_DETAILS]: _isEmpty(employmentDetails) ? getInitialEmployment() : employmentDetails,
    [FORM_FIELD.GARAGING_ADDRESS]: garagingAddress,
    [FORM_FIELD.SHIPPING_ADDRESS]: shippingAddress,
    [FORM_FIELD.REGISTERED_ADDRESS]: registeredAddress,
    [FORM_FIELD.PRIMARY_DRIVER]: _isEmpty(driverDetails) ? getInitialDriverDetail() : driverDetails,
    [FORM_FIELD.CONTRACTING]: _pick(customer, Object.values(CONTRACTING_FIELD)),
    [FORM_FIELD.BILLING_ADDRESS]: billingAddress,
    [FORM_FIELD.PRINCIPAL_OWNER]: _isEmpty(principalOwner) ? [{}] : principalOwner,
    [FORM_FIELD.OTHER_INCOME_DETAILS]: _isEmpty(otherIncomeDetails) ? {} : otherIncomeDetails,
    [FORM_FIELD.COMMUNICATION_PREFERENCES]: communicationPreferences,
    [FORM_FIELD.PREFERRED_COMMUNICATION_MODE]: preferredCommunicationMode,
    [FORM_FIELD.MISCELLANEOUS]: { customerType, ...miscellaneousSectionData },
    ...(!DealerPropertyHelper.isUSUploadDocSyncCrm()
      ? {
          [FORM_FIELD.LICENSE_UPLOAD]: getMediaByType(customerMedia, MEDIA_TYPE.LICENSE),
          [FORM_FIELD.LICENSE_UPLOAD_BACK]: getMediaByType(customerMedia, MEDIA_TYPE.LICENSE_BACK),
          [FORM_FIELD.INSURANCE_UPLOAD]: getMediaByType(customerMedia, MEDIA_TYPE.INSURANCE),
          [FORM_FIELD.EMPLOYMENT_UPLOAD]: getMediaByType(customerMedia, MEDIA_TYPE.EMPLOYMENT),
        }
      : {}),
  };
};

export const getFormDataFromPayload = (customers, dealType) =>
  _map(customers, customer => getCustomerFormDataFromPayLoad(customer, dealType));

const parsedCustomerData = customer =>
  _omit(customer, [
    APPLICANT_FORM_FIELD.FIRST_NAME,
    APPLICANT_FORM_FIELD.MIDDLE_NAME,
    APPLICANT_FORM_FIELD.LAST_NAME,
    APPLICANT_FORM_FIELD.SSN,
    APPLICANT_FORM_FIELD.DOB,
    APPLICANT_FORM_FIELD.LEGAL_NAME,
    APPLICANT_FORM_FIELD.TRADE_NAME,
    FORM_FIELD.LICENSE_UPLOAD,
    FORM_FIELD.LICENSE_UPLOAD_BACK,
    FORM_FIELD.INSURANCE_UPLOAD,
    FORM_FIELD.EMPLOYMENT_UPLOAD,
  ]);

const removeEmptyItems = obj =>
  _omitBy({ ...obj }, (value, key) => {
    if (INSURANCE_DETAILS_TO_BE_IGNORED.includes(key)) {
      return true;
    }
    if (typeof value === 'object' && !Array.isArray(value) && value !== null) {
      return _isEmpty(removeEmptyItems(value));
    }
    return !value && value !== 0;
  });

const getInsuranceDetailsWithNullRemoved = insuranceDetails => {
  if (_isEmpty(removeEmptyItems(insuranceDetails))) {
    return null;
  }
  return {
    ...insuranceDetails,
    agencyDetails: getInsuranceAgencyDetails(insuranceDetails),
    [INSURANCE_FORM_FIELD.EFFECTIVE_DATE]: getStartOfDayTimeStamp(
      insuranceDetails[INSURANCE_FORM_FIELD.EFFECTIVE_DATE]
    ),
    [INSURANCE_FORM_FIELD.EXPIRATION_DATE]: getStartOfDayTimeStamp(
      insuranceDetails[INSURANCE_FORM_FIELD.EXPIRATION_DATE]
    ),
    phoneNume: getPhoneNumberValue(insuranceDetails?.phoneNum),
  };
};

const getValidAddresses = (
  personalUse,
  {
    personalAddress = [],
    businessAddress = [],
    garagingAddress,
    billingAddress,
    shippingAddress,
    previousAddress,
    tradingAddress,
    registeredAddress = [],
  }
) => {
  const requiredAdress = [
    ...(personalUse ? personalAddress : businessAddress),
    ...(garagingAddress || []),
    ...(billingAddress || []),
    ...(shippingAddress || []),
    ...(previousAddress || []),
    ...(tradingAddress || []),
    ...(personalUse ? EMPTY_ARRAY : registeredAddress),
  ];
  const validAddresses = removeEmptyAddress(requiredAdress);
  return validAddresses;
};

export const getPayloadFromForm = (formValues, guarantor) =>
  _map(
    formValues,
    ({
      [FORM_FIELD.CUSTOMER_DETAILS]: customer = {},
      [FORM_FIELD.PERSONAL_ADDRESS]: personalAddress = {},
      [FORM_FIELD.BUSINESS_ADDRESS]: businessAddress = {},
      [FORM_FIELD.LICENSE_DETAILS]: licenseDetails = {},
      [FORM_FIELD.INSURANCE_DETAILS]: insuranceDetails = {},
      [FORM_FIELD.EMPLOYMENT_DETAILS]: employmentDetails = {},
      [FORM_FIELD.GARAGING_ADDRESS]: garagingAddress = [],
      [FORM_FIELD.SHIPPING_ADDRESS]: shippingAddress = [],
      [FORM_FIELD.PREVIOUS_ADDRESS]: previousAddress = [],
      [FORM_FIELD.REGISTERED_ADDRESS]: registeredAddress = [],
      [FORM_FIELD.TRADING_ADDRESS]: tradingAddress = [],
      [FORM_FIELD.PRIMARY_DRIVER]: driverDetails = {},
      [FORM_FIELD.CONTRACTING]: contracting = {},
      [FORM_FIELD.PRINCIPAL_OWNER]: principalOwner = [],
      [FORM_FIELD.BILLING_ADDRESS]: billingAddress = [],
      [FORM_FIELD.OTHER_INCOME_DETAILS]: otherIncomeDetails = {},
      [FORM_FIELD.COMMUNICATION_PREFERENCES]: communicationPreferences = {},
      [FORM_FIELD.PREFERRED_COMMUNICATION_MODE]: preferredCommunicationMode = {},
      [FORM_FIELD.MISCELLANEOUS]: miscellaneousSectionData = {},
      [FORM_FIELD.LICENSE_UPLOAD]: licenseUpload = {},
      [FORM_FIELD.LICENSE_UPLOAD_BACK]: licenseUploadBack = {},
      [FORM_FIELD.INSURANCE_UPLOAD]: insuranceUpload = {},
      [FORM_FIELD.EMPLOYMENT_UPLOAD]: employmentUpload = {},
      isDuplicate = false,
      ...rest
    }) => {
      // set billing addresstype and garagingAdreesType
      const billingAddressType = billingAddress?.[0]?.sameAsCurrentAddress;
      const garagingAddressType = garagingAddress?.[0]?.sameAsCurrentAddress;
      const shippingAddressType = shippingAddress?.[0]?.sameAsCurrentAddress;
      const registeredAddressType = registeredAddress?.[0]?.sameAsCurrentAddress;

      const personalUse = customer.customerType === CUSTOMER_TYPE.PERSONAL;
      const expirationDate = licenseDetails[LICENSE_FORM_FIELD.EXPIRATION_DATE];
      const expirationDateTimeStamp = expirationDate ? getStartOfDayTimeStamp(expirationDate) : null;
      const isPrimaryCustomer = _get(rest, 'type') === BUYER_TYPE.BUYER;
      const customerId = customer.customerId || rest.customerId;
      const externalCustomerId = customer.externalCustomerId || rest.externalCustomerId;
      const displayId = customer.displayId || rest.displayId;
      const dobFromForm = customer[APPLICANT_FORM_FIELD.DOB];
      const dob = dobFromForm && isValidDate(dobFromForm) ? getTimeStamp(dobFromForm) : null;
      const preferredCommunicationModeFinal =
        _get(preferredCommunicationMode, FORM_FIELD.PREFERRED_COMMUNICATION_MODE) || '';

      const customerMedia = [
        ...getCustomerMediaPayload(licenseUpload, MEDIA_TYPE.LICENSE),
        ...getCustomerMediaPayload(licenseUploadBack, MEDIA_TYPE.LICENSE_BACK),
        ...getCustomerMediaPayload(insuranceUpload, MEDIA_TYPE.INSURANCE),
        ...getCustomerMediaPayload(employmentUpload, MEDIA_TYPE.EMPLOYMENT),
      ];

      return {
        ...parsedCustomerData(rest),
        ...getContractingDetailsForPayload(contracting),
        temp: false,
        ..._omit(customer, APPLICANT_FORM_FIELD.CREDIT_VALUE),
        [APPLICANT_FORM_FIELD.SSN]: getNumberFromString(customer[APPLICANT_FORM_FIELD.SSN]),
        [APPLICANT_FORM_FIELD.MOBILE]: getNumberFromString(getPhoneNumberValue(customer[APPLICANT_FORM_FIELD.MOBILE])),
        [APPLICANT_FORM_FIELD.HOME_PHONE]: getNumberFromString(
          getPhoneNumberValue(customer[APPLICANT_FORM_FIELD.HOME_PHONE])
        ),
        [APPLICANT_FORM_FIELD.WORK_PHONE]: getNumberFromString(
          getPhoneNumberValue(customer[APPLICANT_FORM_FIELD.WORK_PHONE])
        ),
        [APPLICANT_FORM_FIELD.BUSINESS_PHONE]: getNumberFromString(
          getPhoneNumberValue(customer[APPLICANT_FORM_FIELD.BUSINESS_PHONE])
        ),
        [APPLICANT_FORM_FIELD.DOB]: dob,
        [APPLICANT_FORM_FIELD.CONTACTS]: customer[APPLICANT_FORM_FIELD.CONTACTS],
        [APPLICANT_FORM_FIELD.DATE_OF_INCORPORATION]: getStartOfDayTimeStamp(
          customer[APPLICANT_FORM_FIELD.DATE_OF_INCORPORATION]
        ),
        buyerTypeNum: personalUse ? CUSTOMER_TYPE.PERSONAL : CUSTOMER_TYPE.BUSINESS,
        buyerType: personalUse ? CUSTOMER_TYPE.PERSONAL : CUSTOMER_TYPE.BUSINESS,
        type: _get(rest, 'type'),
        guarantor: !isPrimaryCustomer && guarantor,
        address: getValidAddresses(personalUse, {
          personalAddress,
          businessAddress,
          garagingAddress,
          billingAddress,
          shippingAddress,
          previousAddress,
          registeredAddress,
          tradingAddress,
        }),
        [FORM_FIELD.LICENSE_DETAILS]: {
          ...licenseDetails,
          [LICENSE_FORM_FIELD.EXPIRATION_DATE]: expirationDateTimeStamp,
          [LICENSE_FORM_FIELD.ISSUING_DATE]: getStartOfDayTimeStamp(licenseDetails[LICENSE_FORM_FIELD.ISSUING_DATE]),
        },
        [FORM_FIELD.INSURANCE_DETAILS]: getInsuranceDetailsWithNullRemoved(insuranceDetails),
        [FORM_FIELD.EMPLOYMENT_DETAILS]: getEmploymentDetailsForPayload(employmentDetails),
        [FORM_FIELD.PRIMARY_DRIVER]: getPrimaryDriverForPayload(driverDetails),
        [FORM_FIELD.PRINCIPAL_OWNER]: getPrincipalOwnerDetails(principalOwner),
        driverType: driverDetails.driverType,
        externalCustomerId: isDuplicate ? null : externalCustomerId,
        customerId: isDuplicate ? null : customerId,
        displayId: isDuplicate ? null : displayId,
        [FORM_FIELD.OTHER_INCOME_DETAILS]: otherIncomeDetails,
        [FORM_FIELD.COMMUNICATION_PREFERENCES]: communicationPreferences,
        [FORM_FIELD.PREFERRED_COMMUNICATION_MODE]: preferredCommunicationModeFinal,
        billingAddressType,
        garagingAddressType,
        shippingAddressType,
        registeredAddressType,
        ...(!DealerPropertyHelper.isUSUploadDocSyncCrm() ? { customerMedia } : {}),
        ...miscellaneousSectionData,
      };
    }
  );

export const getBusinessTypeOptions = () =>
  _map(_values(isInchcape() ? BUSINESS_TYPE_INCHCAPE : BUSINESS_TYPE), type => ({
    label: BUSINESS_TYPE_LABEL[type],
    value: type,
  }));

export const getIndustryTypeOptions = () =>
  _map(_values(INDUSTRY), type => ({
    label: INDUSTRY_LABEL[type],
    value: type,
  }));

export const getBusinessClassificationTypeOptions = () =>
  _map(_values(BUSINESS_CLASSIFICATION), type => ({
    label: BUSINESS_CLASSIFICATION_LABEL[type],
    value: type,
  }));

export const getNumberOfEmployeesTypeOptions = () =>
  _map(NUMBER_OF_EMPLOYEES, val => ({
    label: val,
    value: val,
  }));

export const getVatRegisteredTypeOptions = () =>
  _map(VAT_REGISTERED_VALUES, val => ({
    label: val,
    value: val,
  }));

export const getTaxClassificationValues = taxClassification =>
  _map(taxClassification, tax => ({
    label: tax.classificationName,
    value: tax.id,
  }));

export const getResidentTypeOptions = () =>
  _map(_values(RESIDENT_TYPE), type => ({
    label: RESIDENT_TYPE_LABEL[type],
    value: type,
  }));

export const getEmploymentStatusOptions = () =>
  _map(_values(DEALS_EMPLOYMENT_TYPE), type => ({
    label: EMPLOYMENT_TYPE_LABEL[type],
    value: type,
  }));

export const getEmployerCategoryOptions = () =>
  _map(_values(EMPLOYER_CATEGORIES), type => ({
    label: EMPLOYER_CATEGORIES_LABEL[type],
    value: type,
  }));

const getAttachmentsByMediaType = (attachments, mediaType) =>
  _filter(attachments, attachment => attachment.mediaType === mediaType) || [];

const getDriverDetail = customer => {
  const primaryDriver = tget(customer, 'primaryDriver', EMPTY_OBJECT);
  const { address, city, county, country, email, firstName, lastName, middleName, mobilePhone, state, zipCode } =
    primaryDriver;
  return {
    [DRIVER_FORM_FIELD.ADDRESS]: address || EMPTY_STRING,
    [DRIVER_FORM_FIELD.CITY]: city || EMPTY_STRING,
    [DRIVER_FORM_FIELD.COUNTRY]: country || EMPTY_STRING,
    [DRIVER_FORM_FIELD.COUNTY]: county || EMPTY_STRING,
    [DRIVER_FORM_FIELD.PRIMARY_DRIVER_TYPE]: PRIMARY_DRIVER_TYPE.OTHER,
    [DRIVER_FORM_FIELD.EMAIL]: email || EMPTY_STRING,
    [DRIVER_FORM_FIELD.FIRST_NAME]: firstName || EMPTY_STRING,
    [DRIVER_FORM_FIELD.LAST_NAME]: lastName || EMPTY_STRING,
    [DRIVER_FORM_FIELD.MIDDLE_NAME]: middleName || EMPTY_STRING,
    [DRIVER_FORM_FIELD.MOBILE]: mobilePhone || EMPTY_STRING,
    [DRIVER_FORM_FIELD.STATE]: state || EMPTY_STRING,
    [DRIVER_FORM_FIELD.ZIP_CODE]: zipCode || EMPTY_STRING,
  };
};

export const getCustomerAdditionalDetails = customer => ({
  incorporationTime: _get(customer, 'incorporationTime'),
  incorporationState: _get(customer, 'incorporationState'),
  taxId: _get(customer, 'taxId'),
  maritalStatus: _get(customer, 'maritalStatus'),
  businessType: _get(customer, 'businessType'),
  gender: _get(customer, 'gender'),
  garagingAddressType: _get(customer, 'garagingAddressType'),
  billingAddressType: _get(customer, 'billingAddressType'),
});

export const getFormValueForExistingCustomer = (customers, isAdded, creditScore = null) =>
  _map(customers, customer => {
    if (!customer.added && isAdded) return customer;

    const customerData = getCustomerData(customer);
    const customerAdditionalDetails = getCustomerAdditionalDetails(customer);
    const licenseDetails = {
      ...getLicenseData(customer),
      ...getLicenseDetailsFromLead(customer),
    };
    const insuranceDetails = getInsuranceData(customer);
    const personalAddress = getAddressData(customer, CUSTOMER_TYPE.PERSONAL);
    const businessAddress = getAddressData(customer, CUSTOMER_TYPE.BUSINESS);
    const contactPrefs = getContactPreferences(customer);
    const { attachments, contacts, contact } = customer;
    // const billingAddress = getBillingAddress(customer);
    // const currentAddressForAddresstype = !_isEmpty(personalAddress) ? personalAddress : businessAddress;
    // const billingAddress = getAddressByType2(customer, currentAddressForAddresstype, 2);
    const billingAddress = getAddressByType(customer, ADDRESS_TYPE.BILLING);
    const garagingAddress = getAddressByType(customer, ADDRESS_TYPE.GARAGE);
    // const shippingAddress = getAddressByType2(customer, currentAddressForAddresstype, 1);
    const shippingAddress = getAddressByType(customer, ADDRESS_TYPE.SHIPPING);
    const previousAddress = getAddressByType(customer, ADDRESS_TYPE.PREVIOUS);
    const registeredAddress = getAddressByType(customer, ADDRESS_TYPE.REGISTERED);
    const tradingAddress = getAddressByType(customer, ADDRESS_TYPE.TRADING);
    const principalOwner = getPrincipalOwner(customer);
    const otherIncomeDetails = getOtherIncomeDetails(customer);
    const communicationPreferences = getCommunicationPreferences(customer);
    const preferredCommunicationMode = { [FORM_FIELD.PREFERRED_COMMUNICATION_MODE]: getCommunicationMode(customer) };
    const miscellaneousSectionData = getMiscellaneousSectionData(customer);

    return {
      customerId: customerData.customerId,
      displayId: customerData.displayId || undefined,
      crmCustomerId: customerData?.crmCustomerId,
      communicationPreferences: contactPrefs,
      [FORM_FIELD.CUSTOMER_DETAILS]: {
        ...customerAdditionalDetails,
        ...customerData,
        [APPLICANT_FORM_FIELD.DOB]: getMoment(customerData[APPLICANT_FORM_FIELD.DOB]),
        [APPLICANT_FORM_FIELD.DATE_OF_INCORPORATION]: getMoment(
          customerAdditionalDetails[APPLICANT_FORM_FIELD.DATE_OF_INCORPORATION]
        ),
        [APPLICANT_FORM_FIELD.CONTACTS]: contacts || contact || [],
        [APPLICANT_FORM_FIELD.CREDIT_VALUE]: creditScore,
        [APPLICANT_FORM_FIELD.CREDIT_RANGE_DETAILS]: {
          value: creditScore,
          creditScoreType: CREDIT_SCORE_TYPE.SALES_SETUP_REPORTED,
        },
      },
      [FORM_FIELD.PERSONAL_ADDRESS]: _size(personalAddress) ? personalAddress : getInitialAddress(),
      [FORM_FIELD.BUSINESS_ADDRESS]: _size(businessAddress) ? businessAddress : getInitialAddress(),
      [FORM_FIELD.LICENSE_DETAILS]: {
        ...licenseDetails,
        [LICENSE_FORM_FIELD.EXPIRATION_DATE]: getMoment(licenseDetails[LICENSE_FORM_FIELD.EXPIRATION_DATE]),
        [LICENSE_FORM_FIELD.ISSUING_DATE]: getMoment(licenseDetails[LICENSE_FORM_FIELD.ISSUING_DATE]),
      },
      [FORM_FIELD.INSURANCE_DETAILS]: {
        ...insuranceDetails,
        [INSURANCE_FORM_FIELD.EFFECTIVE_DATE]: getMoment(insuranceDetails[INSURANCE_FORM_FIELD.EFFECTIVE_DATE]),
        [INSURANCE_FORM_FIELD.EXPIRATION_DATE]: getMoment(insuranceDetails[INSURANCE_FORM_FIELD.EXPIRATION_DATE]),
      },
      [FORM_FIELD.EMPLOYMENT_DETAILS]: getEmployerData(customer) || getInitialEmployment(),
      [FORM_FIELD.GARAGING_ADDRESS]: garagingAddress,
      [FORM_FIELD.SHIPPING_ADDRESS]: shippingAddress,
      [FORM_FIELD.PREVIOUS_ADDRESS]: previousAddress,
      [FORM_FIELD.REGISTERED_ADDRESS]: registeredAddress,
      [FORM_FIELD.TRADING_ADDRESS]: tradingAddress,
      [FORM_FIELD.PRIMARY_DRIVER]: getDriverDetail(customer),
      [FORM_FIELD.LICENSE_UPLOAD]: getAttachmentsByMediaType(attachments, MEDIA_TYPE.LICENSE),
      [FORM_FIELD.LICENSE_UPLOAD_BACK]: getAttachmentsByMediaType(attachments, MEDIA_TYPE.LICENSE_BACK),
      [FORM_FIELD.INSURANCE_UPLOAD]: getAttachmentsByMediaType(attachments, MEDIA_TYPE.INSURANCE),
      [FORM_FIELD.EMPLOYMENT_UPLOAD]: getAttachmentsByMediaType(attachments, MEDIA_TYPE.EMPLOYMENT),
      [FORM_FIELD.CONTRACTING]: _pick(customer, Object.values(CONTRACTING_FIELD)),
      [FORM_FIELD.BILLING_ADDRESS]: billingAddress,
      [FORM_FIELD.PRINCIPAL_OWNER]: _isEmpty(principalOwner) ? [{}] : principalOwner,
      [FORM_FIELD.OTHER_INCOME_DETAILS]: _isEmpty(otherIncomeDetails) ? {} : otherIncomeDetails,
      [FORM_FIELD.COMMUNICATION_PREFERENCES]: communicationPreferences || EMPTY_OBJECT,
      [FORM_FIELD.PREFERRED_COMMUNICATION_MODE]: preferredCommunicationMode || EMPTY_STRING,
      [FORM_FIELD.MISCELLANEOUS]: miscellaneousSectionData || EMPTY_OBJECT,
    };
  });

export const defaultFormatter = value => value;

export function removeMarketIdFromCurrentCustomer(customers) {
  return customers.map(customer => ({
    ...customer,
    address: getAllAddressesByOmitingMarketId(customer),
  }));
}

export const removeEmptyAddress = address =>
  _filter(address, item => {
    const addressFields = _omit(item, [
      ADDRESS_FORM_FIELD.ADDRESS_TYPE,
      ADDRESS_FORM_FIELD.PERIOD_OF_RESIDENCE_DURATION_TYPE,
      ADDRESS_FORM_FIELD.CURRENT_ADDRESS,
      ADDRESS_FORM_FIELD.INCITY,
      ADDRESS_FORM_FIELD.CMS_ADDRESS_ID,
      ADDRESS_FORM_FIELD.LATITUDE_N_LONGITUDE,
      ADDRESS_FORM_FIELD.SAME_AS_CURRENT_ADDRESS,
    ]);
    return !_isEmpty(_compact(_values(addressFields)));
  });

export const removeEmptyCustomerFormFields = customers =>
  _map(customers, customer => {
    const address = _get(customer, 'address') || EMPTY_ARRAY;
    const addressAfterRemovingEmptys = removeEmptyAddress(address);
    return {
      ...customer,
      address: addressAfterRemovingEmptys,
    };
  });

export const isCRMFlow = dealType =>
  DealerPropertyHelper.isCRMEnabledV2() &&
  (dealType === DEAL_TYPES.PERSONAL || isInchcape() || DealerPropertyHelper.isDealContactsEnabled());

export const isCustomerEmpty = customer => {
  const { customerType, tradeName, legalName, firstName, lastName, middleName } = customer || {};
  if (customerType === CUSTOMER_TYPE.BUSINESS) return !!(tradeName || legalName);
  return !!(firstName || lastName || middleName);
};

export function formatCustomerMedia(document) {
  const { attachments, documentType } = document || {};
  if (!_isEmpty(attachments) && documentType) {
    return _map(attachments, item => {
      const { name, contentType, mediaId } = item;
      return {
        name,
        type: LEAD_VS_DEAL_DOCUMENT_TYPES[documentType],
        mediaId,
        mediaSize: null,
        contentType,
        objectURL: null,
        createdTime: null,
      };
    });
  }
  return [];
}

export const getLeadVaultInfoFromLeadForm = formData => {
  const getSSN = basic => _get(basic, 'ssn') || EMPTY_STRING;
  const getDL = buyerDocuments =>
    _get(buyerDocuments, [LEAD_DOCUMENT_TYPES.DRIVING_LICENSE, 'licenseNumber']) || EMPTY_STRING;

  const buyerBasic = _get(formData, 'buyer.basic') || EMPTY_OBJECT;
  const coBuyerBasic = _get(formData, 'coBuyer.basic') || EMPTY_OBJECT;
  const buyerDocuments = _get(formData, 'documents.buyer') || EMPTY_OBJECT;
  const coBuyerDocuments = _get(formData, 'documents.coBuyer') || EMPTY_OBJECT;

  const buyerVaultInfo = { ssn: getSSN(buyerBasic), drivingLicenseNumber: getDL(buyerDocuments) };
  const coBuyerVaultInfo = { ssn: getSSN(coBuyerBasic), drivingLicenseNumber: getDL(coBuyerDocuments) };

  return { buyer: buyerVaultInfo, coBuyer: coBuyerVaultInfo };
};

export const isAddressEmpty = address => {
  const addressFields = _difference(_values(ADDRESS_FORM_FIELD), [
    ADDRESS_FORM_FIELD.ADDRESS_TYPE,
    ADDRESS_FORM_FIELD.PERIOD_OF_RESIDENCE_DURATION_TYPE,
    ADDRESS_FORM_FIELD.CURRENT_ADDRESS,
    ADDRESS_FORM_FIELD.LATITUDE_N_LONGITUDE,
  ]);
  return !_some(addressFields, field => _get(address, field));
};

export const validateYear = (fieldId, valueToTest) => {
  if (!valueToTest) return { isValid: true, fieldId };
  const actualYears = _floor(valueToTest / 12);
  if (actualYears >= 0 && actualYears <= 99) return { isValid: true, fieldId };

  return {
    isValid: false,
    message: __('Enter between 0 to 99'),
    fieldId,
  };
};

export const currencyFormatter = value => (value ? numberFormatter(value, { precision: 2 }) : NO_DATA);

export const findCustomerDocumentByMediaId = (customerDocuments, mediaId) =>
  _find(customerDocuments, customerDocument => customerDocument?.activeDocumentMedia?.mediaId === mediaId);

export const isBusinessOrCustomerTaxClassificationChanged = (customers, previousDeal) => {
  const businessTaxClassification = _get(customers, '0.businessTaxClassification', null);
  const customerTaxClassification = _get(customers, '0.customerTaxClassification', null);
  const oldCustomers = getCustomers(previousDeal);
  const previousBusinessTaxClassfication = _get(oldCustomers, '0.businessTaxClassification', null);
  const previousCustomerTaxClassfication = _get(oldCustomers, '0.customerTaxClassification', null);
  if (
    previousBusinessTaxClassfication !== businessTaxClassification ||
    customerTaxClassification !== previousCustomerTaxClassfication
  ) {
    return true;
  }
  return false;
};
