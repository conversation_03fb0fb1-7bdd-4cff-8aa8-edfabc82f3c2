import _includes from 'lodash/includes';

import { isInchcape, isRRG } from '@tekion/tekion-base/utils/sales/dealerProgram.utils';
import { filterRows } from 'tcomponents/organisms/FormBuilder/utils/general';
import DealerPropertyHelper from '@tekion/tekion-widgets/src/helpers/dealerPropertyHelper';

import ApplicantsDetailsForm from './components/applicantsDetailsForm';
import AddressForm from './components/addressForm';
import LicenseForm from './components/licenseForm';
import InsuranceForm from './components/insuranceForm';
import EmploymentInfoForm from './components/employmentInfoForm';
import CustomerDetails from './components/customerDetails';
import MediaUpload from './components/mediaUpload';
import PrimaryDriverDetails from './components/primaryDriverDetails';
import ContractingDetails from './components/contractingDetails';
import PrincipalOwner from './components/principalOwnerDetailsForm';
import OtherIncomeDetails from './components/otherIncomeDetailsForm';
import CommunicationPreferences from './components/communicationPreferences/CommunicationPreferences';
import { FORM_FIELD, CUSTOMER_TYPE } from './CustomerForm.constants';
import styles from './customerForm.module.scss';
import BankingDetails from './components/BankingDetails/BankingDetails';
import MiscellaneousSectionForm from './components/miscellaneousSectionForm';
import TaxClassificationFields from './components/TaxClassificationFields/TaxClassificationFields';
import PreferredCommunicationMode from './components/preferredCommunicationMode/PreferredCommunicationMode';

const getCustomerSection = enableAttachment =>
  enableAttachment && !isInchcape() && !isRRG()
    ? [FORM_FIELD.CUSTOMER_DETAILS, FORM_FIELD.CUSTOMER_PREVIEW]
    : [FORM_FIELD.CUSTOMER_DETAILS];

const getLicenseSection = enableAttachment =>
  enableAttachment
    ? [FORM_FIELD.LICENSE_DETAILS, FORM_FIELD.LICENSE_UPLOAD, FORM_FIELD.LICENSE_UPLOAD_BACK]
    : [FORM_FIELD.LICENSE_DETAILS];

const getInsuranceSection = enableAttachment =>
  enableAttachment ? [FORM_FIELD.INSURANCE_DETAILS, FORM_FIELD.INSURANCE_UPLOAD] : [FORM_FIELD.INSURANCE_DETAILS];

const getEmploymentSection = enableAttachment =>
  enableAttachment ? [FORM_FIELD.EMPLOYMENT_DETAILS, FORM_FIELD.EMPLOYMENT_UPLOAD] : [FORM_FIELD.EMPLOYMENT_DETAILS];

const shouldShowCustomerDetailsFields = isCustomerPresent => {
  if (DealerPropertyHelper.isCRMEnabledV2() || isCustomerPresent) {
    return true;
  }
  if (!DealerPropertyHelper.isCRMEnabledV2() && !DealerPropertyHelper.isCustomerDuplicatePreventionEnabled()) {
    return true;
  }
  return false;
};

const formRowCheck = {
  [FORM_FIELD.CUSTOMER_DETAILS]: ({ isCustomerPresent }) => shouldShowCustomerDetailsFields(isCustomerPresent),
  [FORM_FIELD.CUSTOMER_PREVIEW]: ({ isCustomerPresent }) => shouldShowCustomerDetailsFields(isCustomerPresent),
};
const getInchcapePersonalCustomerFields = customerDetailSection => [
  {
    className: styles.form,
    columns: customerDetailSection,
  },
  {
    className: styles.form,
    columns: [FORM_FIELD.TAX_CLASSIFICATION],
  },
  {
    className: styles.form,
    columns: [FORM_FIELD.PREFERRED_COMMUNICATION_MODE],
  },
  {
    className: styles.form,
    columns: [FORM_FIELD.PERSONAL_ADDRESS],
  },
  {
    className: styles.form,
    columns: [FORM_FIELD.BANKING_DETAILS],
  },
];

const getRRGPersonalCustomerFields = customerDetailSection => [
  {
    className: styles.form,
    columns: customerDetailSection,
  },
  {
    className: styles.form,
    columns: [FORM_FIELD.TAX_CLASSIFICATION],
  },
  {
    className: styles.form,
    columns: [FORM_FIELD.PERSONAL_ADDRESS],
  },
  {
    className: styles.form,
    columns: [FORM_FIELD.COMMUNICATION_PREFERENCES],
  },
  {
    className: styles.form,
    columns: [FORM_FIELD.MISCELLANEOUS],
  },
];

const personalCustomerForm = (enableAttachment, isCustomerPresent) => {
  const customerDetailSection = filterRows(getCustomerSection(enableAttachment), { isCustomerPresent }, formRowCheck);

  if (isInchcape()) {
    return getInchcapePersonalCustomerFields(customerDetailSection);
  }

  if (isRRG()) {
    return getRRGPersonalCustomerFields(customerDetailSection);
  }

  return [
    {
      className: styles.form,
      columns: customerDetailSection,
    },
    {
      className: styles.form,
      columns: [FORM_FIELD.PERSONAL_ADDRESS],
    },
    {
      className: styles.form,
      columns: getLicenseSection(enableAttachment),
    },
    {
      className: styles.form,
      columns: getInsuranceSection(enableAttachment),
    },
    {
      className: styles.form,
      columns: getEmploymentSection(enableAttachment),
    },
    {
      className: styles.form,
      columns: [FORM_FIELD.GARAGING_ADDRESS],
    },
    {
      className: styles.form,
      columns: [FORM_FIELD.SHIPPING_ADDRESS],
    },
    {
      className: styles.form,
      columns: [FORM_FIELD.BILLING_ADDRESS],
    },
    {
      className: styles.form,
      columns: [FORM_FIELD.OTHER_INCOME_DETAILS],
    },
  ];
};

const getInchcapeBusinessCustomerFields = customerDetailSection => [
  {
    className: styles.form,
    columns: customerDetailSection,
  },
  {
    className: styles.form,
    columns: [FORM_FIELD.TAX_CLASSIFICATION],
  },
  {
    className: styles.form,
    columns: [FORM_FIELD.PREFERRED_COMMUNICATION_MODE],
  },
  {
    className: styles.form,
    columns: [FORM_FIELD.BUSINESS_ADDRESS],
  },
  {
    className: styles.form,
    columns: [FORM_FIELD.REGISTERED_ADDRESS],
  },
  {
    className: styles.form,
    columns: [FORM_FIELD.BANKING_DETAILS],
  },
];

const getRRGBusinessCustomerFields = customerDetailSection => [
  {
    className: styles.form,
    columns: customerDetailSection,
  },
  {
    className: styles.form,
    columns: [FORM_FIELD.TAX_CLASSIFICATION],
  },
  {
    className: styles.form,
    columns: [FORM_FIELD.BUSINESS_ADDRESS],
  },
  {
    className: styles.form,
    columns: [FORM_FIELD.COMMUNICATION_PREFERENCES],
  },
  {
    className: styles.form,
    columns: [FORM_FIELD.MISCELLANEOUS],
  },
];

const businessCustomerForm = (enableAttachment, isCustomerPresent) => {
  const customerDetailSection = filterRows(getCustomerSection(enableAttachment), { isCustomerPresent }, formRowCheck);

  if (isInchcape()) {
    return getInchcapeBusinessCustomerFields(customerDetailSection);
  }

  if (isRRG()) {
    return getRRGBusinessCustomerFields(customerDetailSection);
  }

  return [
    {
      className: styles.form,
      columns: customerDetailSection,
    },
    {
      className: styles.form,
      columns: [FORM_FIELD.BUSINESS_ADDRESS],
    },
    {
      className: styles.form,
      columns: getInsuranceSection(enableAttachment),
    },
    {
      className: styles.form,
      columns: [FORM_FIELD.GARAGING_ADDRESS],
    },
    {
      className: styles.form,
      columns: [FORM_FIELD.SHIPPING_ADDRESS],
    },
    {
      className: styles.form,
      columns: [FORM_FIELD.PRIMARY_DRIVER],
    },
    {
      className: styles.form,
      columns: [FORM_FIELD.CONTRACTING],
    },
    {
      className: styles.form,
      columns: [FORM_FIELD.PRINCIPAL_OWNER],
    },
    {
      className: styles.form,
      columns: [FORM_FIELD.BILLING_ADDRESS],
    },
  ];
};

const customerForm = {
  [CUSTOMER_TYPE.PERSONAL]: personalCustomerForm,
  [CUSTOMER_TYPE.BUSINESS]: businessCustomerForm,
};

export const getFormSections = ({ customerType = CUSTOMER_TYPE.PERSONAL, enableAttachment, isCustomerPresent }) => {
  const rows = customerForm[customerType] || personalCustomerForm;
  return [
    {
      className: styles.customerForm,
      rows: rows(enableAttachment, isCustomerPresent),
    },
  ];
};

const programSpeificProps = () => {
  if (isRRG() || isInchcape()) {
    return { showExtraFields: false, showAdd: false, showSameAsCurrentAddressFields: false };
  }
  return {};
};

export const getFormFields = ({
  customerType,
  unBlockScreen,
  handleToggleMediaLoading,
  isCoBuyer,
  deleteReasonForCPRAGDPR,
  oldCustomerDisplayId,
  getCountOfPhoneNumberDigits,
  ciamId,
  chargeCustomer,
  dealerInfo,
  customerDocumentsLoading,
  uploadCustomerDocumentQueue,
  ...rest
}) => ({
  [FORM_FIELD.CUSTOMER_DETAILS]: {
    renderer: ApplicantsDetailsForm,
    renderOptions: {
      customerType,
      deleteReasonForCPRAGDPR,
      oldCustomerDisplayId,
      isCoBuyer,
      getCountOfPhoneNumberDigits,
      ciamId,
      ...rest,
      field: FORM_FIELD.CUSTOMER_DETAILS,
      chargeCustomer,
    },
  },
  [FORM_FIELD.TAX_CLASSIFICATION]: {
    renderer: TaxClassificationFields,
    renderOptions: {
      label: __('Tax Classification'),
      customerType,
      field: FORM_FIELD.TAX_CLASSIFICATION,
      ...rest,
    },
  },
  [FORM_FIELD.CUSTOMER_PREVIEW]: {
    renderer: CustomerDetails,
  },
  [FORM_FIELD.PERSONAL_ADDRESS]: {
    renderer: AddressForm,
    renderOptions: {
      label: __('Address'),
      customerType,
      showCopyFromBuyer: isCoBuyer,
      dealerInfo,
      ...rest,
      showExtraFields: true,
      showAdd: true,
      field: FORM_FIELD.PERSONAL_ADDRESS,
      showMapPopup: !isInchcape(),
      deleteReasonForCPRAGDPR,
      ...programSpeificProps(),
      chargeCustomer,
    },
  },
  [FORM_FIELD.BUSINESS_ADDRESS]: {
    renderer: AddressForm,
    renderOptions: {
      label: isInchcape() ? __('Trading Address') : __('Address'),
      showCopyFromBuyer: isCoBuyer,
      customerType,
      ...rest,
      field: FORM_FIELD.BUSINESS_ADDRESS,
      showMapPopup: !isInchcape(),
      deleteReasonForCPRAGDPR,
      ...programSpeificProps(),
      chargeCustomer,
      dealerInfo,
    },
  },
  [FORM_FIELD.LICENSE_DETAILS]: {
    renderer: LicenseForm,
    renderOptions: {
      ...rest,
      field: FORM_FIELD.LICENSE_DETAILS,
      deleteReasonForCPRAGDPR,
    },
  },
  [FORM_FIELD.LICENSE_UPLOAD]: {
    renderer: MediaUpload,
    renderOptions: {
      ...rest,
      field: FORM_FIELD.LICENSE_UPLOAD,
      containerClass: styles.uploadContainer,
      label: __("Driver's License (Front)"),
      helperText: __('Add front view of customer’s Driving License'),
      unBlockScreen,
      handleToggleMediaLoading,
      imageParentContainer: styles.imageContainer,
      isMediaLoading: _includes(customerDocumentsLoading, FORM_FIELD.LICENSE_UPLOAD),
      uploadCustomerDocumentQueue,
      overlayLoaderClassName: styles.overlayLoader,
      shouldMutateFileName: false,
    },
  },
  [FORM_FIELD.LICENSE_UPLOAD_BACK]: {
    renderer: MediaUpload,
    renderOptions: {
      ...rest,
      field: FORM_FIELD.LICENSE_UPLOAD_BACK,
      containerClass: styles.uploadContainer,
      label: __("Driver's License (Back)"),
      helperText: __('Add back view of customer’s Driving License'),
      unBlockScreen,
      handleToggleMediaLoading,
      imageParentContainer: styles.imageContainer,
      isMediaLoading: _includes(customerDocumentsLoading, FORM_FIELD.LICENSE_UPLOAD_BACK),
      uploadCustomerDocumentQueue,
      overlayLoaderClassName: styles.overlayLoader,
      shouldMutateFileName: false,
    },
  },
  [FORM_FIELD.INSURANCE_DETAILS]: {
    renderer: InsuranceForm,
    renderOptions: {
      ...rest,
      showCopyFromBuyer: isCoBuyer,
      field: FORM_FIELD.INSURANCE_DETAILS,
      deleteReasonForCPRAGDPR,
    },
  },
  [FORM_FIELD.INSURANCE_UPLOAD]: {
    renderer: MediaUpload,
    renderOptions: {
      ...rest,
      containerClass: styles.uploadContainer,
      label: __('Insurance Information'),
      helperText: __('Add customer’s Insurance Details'),
      unBlockScreen,
      handleToggleMediaLoading,
      imageParentContainer: styles.imageContainer,
      isMediaLoading: _includes(customerDocumentsLoading, FORM_FIELD.INSURANCE_UPLOAD),
      uploadCustomerDocumentQueue,
      overlayLoaderClassName: styles.overlayLoader,
      shouldMutateFileName: false,
    },
  },
  [FORM_FIELD.EMPLOYMENT_DETAILS]: {
    renderer: EmploymentInfoForm,
    renderOptions: {
      ...rest,
      field: FORM_FIELD.EMPLOYMENT_DETAILS,
      deleteReasonForCPRAGDPR,
      showAdd: true,
    },
  },
  [FORM_FIELD.EMPLOYMENT_UPLOAD]: {
    renderer: MediaUpload,
    renderOptions: {
      ...rest,
      containerClass: styles.uploadContainer,
      label: __('Employment Information'),
      helperText: __('Add customer’s Employment Details'),
      unBlockScreen,
      handleToggleMediaLoading,
      imageParentContainer: styles.imageContainer,
      isMediaLoading: _includes(customerDocumentsLoading, FORM_FIELD.EMPLOYMENT_UPLOAD),
      uploadCustomerDocumentQueue,
      overlayLoaderClassName: styles.overlayLoader,
      shouldMutateFileName: false,
    },
  },
  [FORM_FIELD.GARAGING_ADDRESS]: {
    renderer: AddressForm,
    renderOptions: {
      label: __('Garaging Address'),
      customerType,
      ...rest,
      field: FORM_FIELD.GARAGING_ADDRESS,
      showSameAsCurrentAddressFields: true,
      deleteReasonForCPRAGDPR,
      dealerInfo,
    },
  },

  [FORM_FIELD.REGISTERED_ADDRESS]: {
    renderer: AddressForm,
    renderOptions: {
      label: __('Registered Address'),
      customerType,
      ...rest,
      showSameAsCurrentAddressFields: true,
      field: FORM_FIELD.REGISTERED_ADDRESS,
      showMapPopup: false,
      deleteReasonForCPRAGDPR,
      chargeCustomer,
      dealerInfo,
    },
  },
  [FORM_FIELD.SHIPPING_ADDRESS]: {
    renderer: AddressForm,
    renderOptions: {
      label: __('Shipping Address'),
      customerType,
      ...rest,
      field: FORM_FIELD.SHIPPING_ADDRESS,
      showSameAsCurrentAddressFields: true,
      deleteReasonForCPRAGDPR,
      showMortageAmount: false,
      dealerInfo,
    },
  },

  [FORM_FIELD.PRIMARY_DRIVER]: {
    renderer: PrimaryDriverDetails,
    renderOptions: {
      ...rest,
      field: FORM_FIELD.PRIMARY_DRIVER,
      deleteReasonForCPRAGDPR,
    },
  },
  [FORM_FIELD.CONTRACTING]: {
    renderer: ContractingDetails,
    renderOptions: {
      ...rest,
      field: FORM_FIELD.CONTRACTING,
      deleteReasonForCPRAGDPR,
    },
  },

  [FORM_FIELD.PRINCIPAL_OWNER]: {
    renderer: PrincipalOwner,
    renderOptions: {
      ...rest,
      field: FORM_FIELD.PRINCIPAL_OWNER,
      deleteReasonForCPRAGDPR,
    },
  },

  [FORM_FIELD.BILLING_ADDRESS]: {
    renderer: AddressForm,
    renderOptions: {
      label: __('Billing Address'),
      customerType,
      ...rest,
      field: FORM_FIELD.BILLING_ADDRESS,
      showSameAsCurrentAddressFields: true,
      deleteReasonForCPRAGDPR,
      dealerInfo,
    },
  },

  [FORM_FIELD.OTHER_INCOME_DETAILS]: {
    renderer: OtherIncomeDetails,
    renderOptions: {
      label: __('Other Income'),
      customerType,
      ...rest,
      field: FORM_FIELD.OTHER_INCOME_DETAILS,
      deleteReasonForCPRAGDPR,
    },
  },

  [FORM_FIELD.COMMUNICATION_PREFERENCES]: {
    renderer: CommunicationPreferences,
    renderOptions: {
      label: __('Communication Preferences'),
      ...rest,
      field: FORM_FIELD.COMMUNICATION_PREFERENCES,
    },
  },

  [FORM_FIELD.PREFERRED_COMMUNICATION_MODE]: {
    renderer: PreferredCommunicationMode,
    renderOptions: {
      label: __('Communication Preferences'),
      ...rest,
      field: FORM_FIELD.PREFERRED_COMMUNICATION_MODE,
    },
  },
  [FORM_FIELD.BANKING_DETAILS]: {
    renderer: BankingDetails,
    renderOptions: {
      label: __('Bank Details'),
      customerType,
      ...rest,
      field: FORM_FIELD.BANKING_DETAILS,
      deleteReasonForCPRAGDPR,
    },
  },
  [FORM_FIELD.MISCELLANEOUS]: {
    renderer: MiscellaneousSectionForm,
    renderOptions: {
      customerType,
      ...rest,
      field: FORM_FIELD.MISCELLANEOUS,
    },
  },
});
