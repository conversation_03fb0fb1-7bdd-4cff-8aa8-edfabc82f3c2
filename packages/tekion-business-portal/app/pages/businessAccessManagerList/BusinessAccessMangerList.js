/* eslint-disable import/order */
import React, { useMemo } from 'react';
import PropTypes from 'prop-types';

// Lodash
import _noop from 'lodash/noop';

// Constants
import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import { HEADER_PROPS, TABLE_MANAGER_PROPS } from './constants/businessAccessManagerList.tableManagerProps';
import { BASE_INITIAL_STATE } from './constants/businessAccessManagerList.general';
import ACTION_HANDLERS from './businessAccessManagerList.actionHandlers';

// Components
import TableManager from 'twidgets/organisms/tableManager';
import { BasicTable } from 'twidgets/appServices/accounting/organisms/table';
import withCheckboxTable from 'tcomponents/organisms/withCheckboxTable';
import RevokeAccessModal from './organisms/revokeAccessModal';
import ResendInvitationLinkModal from './organisms/resendInvitationLinkModal';

// helpers
import { getTableProps } from './helpers/businessAccessManagerList.tableProps';
import { getFilterProps } from './helpers/businessAccessManagerList.filters';
import { getSubHeaderProps } from './helpers/businessAccessMangerList.subHeader';
import { hasPermissionToInviteCustomer } from './helpers/businessAccessManagerList.permission';

// connectors
import withActionHandlers from 'tcomponents/connectors/withActionHandlers';

const BasicCheckboxTable = withCheckboxTable(BasicTable);
function BusinessAccessManagerList({
  businessAccessManagerList,
  loading,
  onAction,
  sortDetails,
  selectedFilters,
  searchText,
  searchField,
  columns,
  columnConfigurator,
  selectedCustomerIds,
  nextPageToken,
  isFetchingNextPage,
  entityMetadata,
  selectedFilterGroup,
  totalCount,
  shouldShowRevokeAccessModal,
  isRevokeCustomersSubmitting,
  shouldShowResendInvitationLinkModal,
  isResendInvitationLinkSubmitting,
  isEmailTemplateLoading,
  resendEmailTemplate,
  permissions,
}) {
  const hasInviteCustomerAccess = useMemo(() => hasPermissionToInviteCustomer(permissions), [permissions]);
  const tableProps = useMemo(
    () =>
      getTableProps({
        loading,
        selectedCustomerIds,
        nextPageToken,
        columnConfigurator,
        isFetchingNextPage,
        sortDetails,
        totalCount,
      }),
    [loading, selectedCustomerIds, nextPageToken, columnConfigurator, isFetchingNextPage, sortDetails, totalCount]
  );

  const filterProps = useMemo(
    () =>
      getFilterProps({
        selectedFilters,
        entityMetadata,
        selectedFilterGroup,
        shouldShowCount: totalCount > -1,
      }),
    [selectedFilters, entityMetadata, selectedFilterGroup, totalCount]
  );

  const subHeaderProps = useMemo(
    () => getSubHeaderProps({ businessAccessManagerList, selectedCustomerIds, hasInviteCustomerAccess }),
    [selectedCustomerIds, businessAccessManagerList, hasInviteCustomerAccess]
  );

  const tableComponent = hasInviteCustomerAccess ? BasicCheckboxTable : BasicTable;

  return (
    <div className="full-height full-width">
      <TableManager
        data={businessAccessManagerList}
        onAction={onAction}
        columns={columns}
        columnConfigurator={columnConfigurator}
        tableProps={tableProps}
        tableManagerProps={TABLE_MANAGER_PROPS}
        headerProps={HEADER_PROPS}
        selectedFilters={selectedFilters}
        searchText={searchText}
        searchField={searchField}
        sortDetails={sortDetails}
        filterProps={filterProps}
        selection={selectedCustomerIds}
        tableComponent={tableComponent}
        subHeaderProps={subHeaderProps}
      />
      {shouldShowRevokeAccessModal && (
        <RevokeAccessModal
          visible={shouldShowRevokeAccessModal}
          selectedCustomerIds={selectedCustomerIds}
          loading={isRevokeCustomersSubmitting}
          onAction={onAction}
        />
      )}
      {shouldShowResendInvitationLinkModal && (
        <ResendInvitationLinkModal
          visible={shouldShowResendInvitationLinkModal}
          selectedItems={selectedCustomerIds}
          loading={isResendInvitationLinkSubmitting}
          isEmailTemplateLoading={isEmailTemplateLoading}
          resendEmailTemplate={resendEmailTemplate}
          onAction={onAction}
        />
      )}
    </div>
  );
}

BusinessAccessManagerList.propTypes = {
  businessAccessManagerList: PropTypes.array,
  loading: PropTypes.bool,
  onAction: PropTypes.func,
  sortDetails: PropTypes.object,
  selectedFilters: PropTypes.array,
  searchText: PropTypes.string,
  searchField: PropTypes.string,
  columns: PropTypes.array,
  columnConfigurator: PropTypes.object,
  selectedCustomerIds: PropTypes.array,
  isFetchingNextPage: PropTypes.bool,
  nextPageToken: PropTypes.object,
  selectedFilterGroup: PropTypes.string,
  entityMetadata: PropTypes.object,
  totalCount: PropTypes.number,
  shouldShowRevokeAccessModal: PropTypes.bool,
  isRevokeCustomersSubmitting: PropTypes.bool,
  shouldShowResendInvitationLinkModal: PropTypes.bool,
  isResendInvitationLinkSubmitting: PropTypes.bool,
  isEmailTemplateLoading: PropTypes.bool,
  resendEmailTemplate: PropTypes.object,
  permissions: PropTypes.array,
};

BusinessAccessManagerList.defaultProps = {
  businessAccessManagerList: EMPTY_ARRAY,
  loading: false,
  onAction: _noop,
  sortDetails: EMPTY_OBJECT,
  selectedFilters: EMPTY_ARRAY,
  searchText: '',
  searchField: '',
  columns: null,
  columnConfigurator: null,
  selectedCustomerIds: EMPTY_ARRAY,
  isFetchingNextPage: false,
  nextPageToken: EMPTY_OBJECT,
  selectedFilterGroup: undefined,
  entityMetadata: undefined,
  totalCount: -1,
  shouldShowRevokeAccessModal: false,
  isRevokeCustomersSubmitting: false,
  shouldShowResendInvitationLinkModal: false,
  isResendInvitationLinkSubmitting: false,
  isEmailTemplateLoading: false,
  resendEmailTemplate: null,
  permissions: EMPTY_ARRAY,
};

export default withActionHandlers(ACTION_HANDLERS, BASE_INITIAL_STATE)(BusinessAccessManagerList);
