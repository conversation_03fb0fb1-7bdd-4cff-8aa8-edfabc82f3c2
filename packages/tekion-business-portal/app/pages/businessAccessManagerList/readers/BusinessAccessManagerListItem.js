// Lodash
import _property from 'lodash/property';

const customer = _property('customer');
const lastActivity = _property('lastActivity');
const mobileNumber = _property('primaryPhone');
const email = _property('email');
const portalStatus = _property('portalStatus');
const moduleAccess = _property('moduleAccess');

const READER = { customer, lastActivity, mobileNumber, email, portalStatus, moduleAccess };
export default READER;
