import mapProps from 'recompose/mapProps';

// Components
import Text<PERSON>enderer from 'tcomponents/molecules/CellRenderers/TextRenderer';
import { StatusCellRenderer } from 'tcomponents/molecules/CellRenderers/statusRenderer';
import PhoneNumberDisplayCellRenderer from 'twidgets/cellRenderers/PhoneNumberDisplayCellRenderer';
import DateRenderer from 'twidgets/cellRenderers/dateRenderer';

// Constants
import { DATE_TIME_FORMAT } from '@tekion/tekion-conversion-web';
import COLUMN_IDS from '../constants/businessAccessManagerList.columnIds';

// Reader
import businessAccessManagerListItemReader from '../readers/BusinessAccessManagerListItem';

// helpers
import {
  getPortalStatusCellProps,
  getPhoneNumberCellProps,
  getModuleAccessCellProps,
} from './businessAccessManagerList.cellProps';

const DateTimeRenderer = mapProps(props => ({
  data: props.value,
  format: DATE_TIME_FORMAT.DATE_ABBREVIATED_MONTH_YEAR_WITH_HOUR_MINUTE,
}))(DateRenderer);

export const COLUMN_CONFIG_BY_KEY = {
  [COLUMN_IDS.CUSTOMER]: {
    accessor: businessAccessManagerListItemReader.customer,
    Cell: TextRenderer,
    sortable: true,
    width: 200,
    resizable: false,
    id: COLUMN_IDS.CUSTOMER,
    key: COLUMN_IDS.CUSTOMER,
  },
  [COLUMN_IDS.PORTAL_STATUS]: {
    accessor: businessAccessManagerListItemReader.portalStatus,
    Cell: StatusCellRenderer,
    sortable: true,
    getProps: getPortalStatusCellProps,
    width: 150,
    resizable: false,
    id: COLUMN_IDS.PORTAL_STATUS,
    key: COLUMN_IDS.PORTAL_STATUS,
  },
  [COLUMN_IDS.LAST_LOGIN_ACTIVITY]: {
    accessor: businessAccessManagerListItemReader.lastActivity,
    Cell: DateTimeRenderer,
    sortable: true,
    id: COLUMN_IDS.LAST_LOGIN_ACTIVITY,
    key: COLUMN_IDS.LAST_LOGIN_ACTIVITY,
  },
  [COLUMN_IDS.MOBILE_NO]: {
    accessor: businessAccessManagerListItemReader.mobileNumber,
    Cell: PhoneNumberDisplayCellRenderer,
    getProps: getPhoneNumberCellProps,
    id: COLUMN_IDS.MOBILE_NO,
    key: COLUMN_IDS.MOBILE_NO,
  },
  [COLUMN_IDS.MODULE_ACCESS]: {
    accessor: businessAccessManagerListItemReader.moduleAccess,
    Cell: TextRenderer,
    getProps: getModuleAccessCellProps,
    id: COLUMN_IDS.MODULE_ACCESS,
    key: COLUMN_IDS.MODULE_ACCESS,
  },
  [COLUMN_IDS.EMAIL]: {
    accessor: businessAccessManagerListItemReader.email,
    Cell: TextRenderer,
    sortable: true,
    id: COLUMN_IDS.EMAIL,
    key: COLUMN_IDS.EMAIL,
  },
};

export default COLUMN_CONFIG_BY_KEY;
