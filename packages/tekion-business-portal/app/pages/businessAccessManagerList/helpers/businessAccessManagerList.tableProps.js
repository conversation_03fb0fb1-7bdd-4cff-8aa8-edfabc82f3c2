// Lodash
import _isNull from 'lodash/isNull';
import _stubTrue from 'lodash/stubTrue';
import _isEmpty from 'lodash/isEmpty';

// Constants
import { EMPTY_ARRAY } from 'tbase/app.constants';
import { BUSINESS_ACCESS_MANAGER_ITEM_TYPE } from '../constants/businessAccessManagerList.general';

export const getTableProps = ({
  loading,
  selectedCustomerIds = EMPTY_ARRAY,
  nextPageToken,
  columnConfigurator,
  isFetchingNextPage,
  sortDetails,
  totalCount,
}) => ({
  isFetchingNextPage,
  hasInfiniteScroll: true,
  loading,
  itemType: BUSINESS_ACCESS_MANAGER_ITEM_TYPE,
  getIsAllDataFetched: _isEmpty(nextPageToken) ? _stubTrue : () => _isNull(nextPageToken),
  nextPageToken,
  isSelectionEnabled: true,
  selection: selectedCustomerIds,
  columnConfigurator,
  sortDetails,
  totalNumberOfEntries: totalCount,
});
