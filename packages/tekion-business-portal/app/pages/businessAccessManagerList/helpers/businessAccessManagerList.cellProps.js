import _constant from 'lodash/constant';

// Readers
import businessAccessManagerListItemReader from '../readers/BusinessAccessManagerListItem';

// constants
import { PORTAL_STATUS_COLOR_MAP, PORTAL_STATUS_LABELS } from '../constants/businessAccessManagerList.portalStatus';
import { MODULE_ACCESS_LABELS, MODULE_ACCESS_TYPES } from '../constants/businessAccessManagerList.moduleAccess';

export const getPortalStatusCellProps = _constant({
  colorMap: PORTAL_STATUS_COLOR_MAP,
  labelMap: PORTAL_STATUS_LABELS,
});

export const getPhoneNumberCellProps = rowInfo => {
  const { original: customer } = rowInfo;
  const primaryPhone = businessAccessManagerListItemReader.mobileNumber(customer);
  const { number } = primaryPhone || '';
  return {
    ...primaryPhone,
    value: number,
  };
};

export const getModuleAccessCellProps = rowInfo => {
  const { original } = rowInfo;
  const { moduleAccess } = original;
  // module access value is hardcoded to AR Module, we will be adding "-" as default value once we have multiple modules
  return {
    value: MODULE_ACCESS_LABELS[moduleAccess || MODULE_ACCESS_TYPES.ACCOUNT_RECEIVABLE],
  };
};
