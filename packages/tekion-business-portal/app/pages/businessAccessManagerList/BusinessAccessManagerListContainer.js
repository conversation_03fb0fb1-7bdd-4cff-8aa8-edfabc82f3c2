/* eslint-disable import/order */
import React from 'react';
import PropTypes from 'prop-types';
import compose from 'recompose/compose';
import { connect } from 'react-redux';

// Lodash
import _get from 'lodash/get';
import _noop from 'lodash/noop';
import _isEmpty from 'lodash/isEmpty';

// Constants
import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import { BUSINESS_PORTAL } from 'tbase/constants/appServices';
import ASSET_TYPES_BY_MODULE from 'tbusiness/constants/configurableEntity/assetTypesByModule';
import {
  BUSINESS_ACCESS_MANAGER_ASSET_TYPE,
  BUSINESS_ACCESS_MANAGER_ITEM_TYPE,
  DEFAULT_SORT_DETAIL,
} from './constants/businessAccessManagerList.general';
import { COLUMN_CONFIG_BY_KEY } from './helpers/businessAccessManagerList.columnConfig';
import { DEFAULT_FILTER_VALUES } from './constants/businessAccessManagerList.filterTypes';

// Components
import BusinessAccessManagerList from './BusinessAccessMangerList';
import Loader from 'tcomponents/molecules/loader';

// Table reducers
import {
  getAllItems,
  getAppliedSortDetails,
  getFilterValue,
  isLoading,
  getSearchQuery,
  getSearchField,
  getSelectedFilters,
  getNextPageToken,
} from 'tcomponents/reducers/tableItemReducer';

// HOCs
import withAsyncReducer from 'tcomponents/connectors/withAsyncReducer';
import withUserPreferenceColumn from 'tcomponents/connectors/withUserPreferenceColumn';
import withPermissions from 'tcomponents/connectors/withPermissions';

// Table Generator
import { tableGeneratorReducer } from 'tcomponents/organisms/TableGenerator';

// Actions
import { getConfigurableEntityMetadata as getConfigurableEntityMetadataAction } from 'tbusiness/actions/configurableEntity';

// hooks
import useConfigurableEntityMetadata from 'twidgets/hooks/useConfigurableEntityMetadata';

import { getConfigurableEntityMetadata as getConfigurableEntityMetadataSelector } from 'tbusiness/reducers/configurableEntity';

const BusinessAccessManagerListContainer = props => {
  const {
    businessAccessManagerList,
    loading,
    sortDetails,
    selectedFilters,
    searchText,
    searchField,
    selectedCustomerIds,
    columns,
    columnConfigurator,
    nextPageToken,
    entityMetadata,
    getConfigurableEntityMetadata,
    permissions,
  } = props;

  const { isLoading: isMetadataLoading } = useConfigurableEntityMetadata(
    entityMetadata,
    getConfigurableEntityMetadata,
    ASSET_TYPES_BY_MODULE[BUSINESS_PORTAL].BUSINESS_ACCESS_MANAGER_LIST
  );

  if (isMetadataLoading) return <Loader />;

  return (
    <BusinessAccessManagerList
      businessAccessManagerList={businessAccessManagerList}
      loading={loading}
      sortDetails={sortDetails}
      selectedFilters={selectedFilters}
      searchText={searchText}
      searchField={searchField}
      columns={columns}
      columnConfigurator={columnConfigurator}
      selectedCustomerIds={selectedCustomerIds}
      nextPageToken={nextPageToken}
      entityMetadata={entityMetadata}
      permissions={permissions}
    />
  );
};

BusinessAccessManagerListContainer.propTypes = {
  businessAccessManagerList: PropTypes.array,
  loading: PropTypes.bool,
  sortDetails: PropTypes.object,
  selectedFilters: PropTypes.array,
  searchText: PropTypes.string,
  searchField: PropTypes.string,
  selectedCustomerIds: PropTypes.array,
  columns: PropTypes.array,
  columnConfigurator: PropTypes.object,
  nextPageToken: PropTypes.string,
  entityMetadata: PropTypes.object,
  getConfigurableEntityMetadata: PropTypes.func,
  permissions: PropTypes.array,
};

BusinessAccessManagerListContainer.defaultProps = {
  businessAccessManagerList: EMPTY_ARRAY,
  loading: true,
  sortDetails: DEFAULT_SORT_DETAIL,
  selectedFilters: DEFAULT_FILTER_VALUES,
  searchText: '',
  searchField: '',
  selectedCustomerIds: EMPTY_ARRAY,
  columns: EMPTY_ARRAY,
  columnConfigurator: null,
  nextPageToken: '',
  entityMetadata: undefined,
  getConfigurableEntityMetadata: _noop,
  permissions: EMPTY_ARRAY,
};

const mapStateToProps = state => {
  const data = _get(state, `${BUSINESS_PORTAL}.${BUSINESS_ACCESS_MANAGER_ITEM_TYPE}.tableItems`);
  const filterValue = getFilterValue(data);
  const additionalData = _get(data, 'additional', EMPTY_OBJECT);

  return {
    entityMetadata: getConfigurableEntityMetadataSelector(
      state,
      ASSET_TYPES_BY_MODULE[BUSINESS_PORTAL].BUSINESS_ACCESS_MANAGER_LIST
    ),
    businessAccessManagerList: getAllItems(data),
    sortDetails: getAppliedSortDetails(data) || DEFAULT_SORT_DETAIL,
    selectedCustomerIds: _get(additionalData, 'selectedCustomerIds', EMPTY_ARRAY),
    loading: isLoading(data, filterValue),
    searchText: getSearchQuery(data),
    searchField: getSearchField(data),
    selectedFilters: !_isEmpty(getSelectedFilters(data, filterValue))
      ? getSelectedFilters(data, filterValue)
      : DEFAULT_FILTER_VALUES,
    nextPageToken: getNextPageToken(data),
  };
};

const mapDispatchToProps = {
  getConfigurableEntityMetadata: getConfigurableEntityMetadataAction,
};

export default compose(
  withPermissions,
  withAsyncReducer({
    storeKey: `${BUSINESS_PORTAL}.${BUSINESS_ACCESS_MANAGER_ITEM_TYPE}`,
    reducer: tableGeneratorReducer(BUSINESS_ACCESS_MANAGER_ITEM_TYPE),
  }),
  withUserPreferenceColumn(
    BUSINESS_ACCESS_MANAGER_ASSET_TYPE,
    undefined,
    COLUMN_CONFIG_BY_KEY,
    true,
    undefined,
    undefined,
    undefined,
    true,
    true,
    true
  ),
  connect(mapStateToProps, mapDispatchToProps)
)(BusinessAccessManagerListContainer);
