/* eslint-disable no-template-curly-in-string */
// HTTP Client

// Constants
import { EMPTY_OBJECT, EMPTY_ARRAY } from 'tbase/app.constants';

// API Routes

// Dummy data utilities (commented out for actual API implementation)
import { DUMMY_CUSTOMERS, getPaginatedCustomers, filterCustomers, sortCustomers } from '../utils/dummyData';

export const fetchCustomerListCount = (request = EMPTY_OBJECT) =>
  new Promise((resolve, reject) => {
    setTimeout(
      () => {
        try {
          const searchText = request.searchText || '';
          const filters = request.filters || EMPTY_ARRAY;

          console.log('Fetching customer list count with request:', {
            searchText,
            filters: filters.length,
          });

          const filteredCustomers = filterCustomers(DUMMY_CUSTOMERS, searchText, filters);
          console.log(`Total count: ${filteredCustomers.length}`);

          // Actual API implementation
          // httpClient.post(URL_TYPES.CDMS, API_ROUTES.CUSTOMER_PORTAL_COUNT, request);

          resolve({
            data: {
              totalCount: filteredCustomers.length,
            },
            success: true,
          });
        } catch (error) {
          console.error('Error fetching customer list count:', error);
          reject(error);
        }
      },
      300 + Math.random() * 700
    );
  });

export const fetchCustomerList = (request = EMPTY_OBJECT) =>
  new Promise((resolve, reject) => {
    setTimeout(
      () => {
        try {
          // Comment out dummy implementation
          const searchText = request.searchText || '';
          const filters = request.filters || EMPTY_ARRAY;
          const sort = request.sort || EMPTY_ARRAY;
          const nextPageToken = request.nextPageToken || '';
          const size = 500; // Default batch size
          console.log('Fetching customer list with request:', {
            searchText,
            filters: filters.length,
            sort: sort.length,
            nextPageToken,
            size,
          });
          if (process.env.NODE_ENV === 'development') {
            console.log('Request object structure:', JSON.stringify(request, null, 2));
          }
          let filteredCustomers = filterCustomers(DUMMY_CUSTOMERS, searchText, filters);
          filteredCustomers = sortCustomers(filteredCustomers, sort);
          const paginatedResult = getPaginatedCustomers(filteredCustomers, nextPageToken, size);
          console.log(
            `Returning batch ${paginatedResult.currentBatch} of ${paginatedResult.totalBatches} (${paginatedResult.items.length} items)`
          );
          console.log('First customer in batch:', paginatedResult.items[0]);

          // Actual API implementation
          // httpClient.post(URL_TYPES.CDMS, API_ROUTES.CUSTOMER_PORTAL_SEARCH, request);

          // Return in expected API format with customerPortalInfos
          resolve({
            data: {
              customerPortalInfos: paginatedResult.items,
              totalCount: paginatedResult.totalCount,
              hasMore: paginatedResult.hasMore,
              nextPageToken:
                paginatedResult.totalBatches === paginatedResult.currentBatch ? null : paginatedResult.nextPageToken,
              currentPage: paginatedResult.currentBatch - 1,
              pageSize: size,
            },
            success: true,
          });
        } catch (error) {
          console.error('Error fetching customer list:', error);
          reject(error);
        }
      },
      500 + Math.random() * 1500
    );
  });

export const revokeAccessEndpoint = async (customerIds = EMPTY_ARRAY) => {
  try {
    console.log('Revoking access for customers:', {
      customerIds,
      endpoint: '/cpms/u/customer/deactivate/bulk',
    });

    // Simulate API delay
    await new Promise(resolve => {
      setTimeout(resolve, 300);
    });

    console.log(`Successfully revoked access for ${customerIds.length} customers`);

    // Mock response - always return success
    const processedCount = customerIds.length;
    const failedCount = 0;

    return {
      data: {
        success: true,
        message: `Portal Access has been successfully revoked for "${processedCount}" customers and Failed for "${failedCount}" customers.`,
        processedCount,
        failedCount,
      },
      success: true,
    };
  } catch (error) {
    console.error('Error revoking customer access:', error);
    throw error;
  }
};

export const resendInvitationLinkEndpoint = async (customerIds = EMPTY_ARRAY) => {
  try {
    console.log('Resending invitation link for customers:', {
      customerIds,
      endpoint: '/cpms/u/email/resend',
    });

    // Simulate API delay
    await new Promise(resolve => {
      setTimeout(resolve, 300);
    });

    console.log(`Successfully resent invitation link for ${customerIds.length} customers`);

    // Mock response - always return success
    const processedCount = customerIds.length;
    const failedCount = 0;

    return {
      data: {
        success: true,
        message: `Invitation Link has been successfully resent for "${processedCount}" customers and Failed for "${failedCount}" customers.`,
        processedCount,
        failedCount,
      },
      success: true,
    };
  } catch (error) {
    console.error('Error resending invitation link:', error);
    throw error;
  }
};

export const fetchEmailTemplate = async (emailType = 'resend') => {
  try {
    console.log('Fetching email template:', {
      emailType,
      endpoint: `/cpms/u/email/preview/type/${emailType}`,
    });

    // Simulate API delay
    await new Promise(resolve => {
      setTimeout(resolve, 300);
    });

    // Mock response with the specified template
    const mockResponse = {
      responseCode: null,
      errorCode: null,
      replyTo: '<EMAIL>',
      templateInfo: {
        subject: 'Reminder: Access to the Business Portal',
        templateBody:
          '<!DOCTYPE html><html>  <title></title>  <body>    <p style=${email_paragraph_style}>Dear Customer,</p>    <p style=${email_paragraph_style}>      We hope this email finds you well. This is a gentle reminder that access to the AR portal has already been granted to you. With this portal, you can efficiently manage your accounts receivable at your convenience. </p>    <p style=${email_paragraph_style}> Please use the link below to sign up or log in: <a href="${signUpLink}">Access the Business Portal</a></p> <p style=${email_closing_phrase}>Thank you,</p>    <p style=${email_paragraph_style}>Tech Motors Stage</p>  </body></html>',
      },
      message: null,
    };

    console.log('Successfully fetched email template');

    // Actual API implementation would be:
    // return httpClient.get(URL_TYPES.CDMS, API_ROUTES.EMAIL_PREVIEW.replace('{emailType}', emailType), { lan: language });

    return {
      data: mockResponse,
      success: true,
    };
  } catch (error) {
    console.error('Error fetching email template:', error);
    throw error;
  }
};
