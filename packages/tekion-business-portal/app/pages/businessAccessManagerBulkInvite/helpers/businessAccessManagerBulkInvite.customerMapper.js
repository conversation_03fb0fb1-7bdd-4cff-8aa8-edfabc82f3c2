// Readers
import customerReader from 'tbase/readers/Customer';
import bulkInviteCustomerReader from '../readers/CustomerReader';

// Builders
import CustomerBuilder from '../builders/CustomerBuilder';

export const mapCustomersFromInitialFetch = customerData =>
  new CustomerBuilder()
    .setCustomerId(bulkInviteCustomerReader.customerId(customerData))
    .setFirstName(bulkInviteCustomerReader.firstName(customerData))
    .setMiddleName(bulkInviteCustomerReader.middleName(customerData))
    .setLastName(bulkInviteCustomerReader.lastName(customerData))
    .setCustomerNumber(bulkInviteCustomerReader.customerNumber(customerData))
    .setEmail(bulkInviteCustomerReader.email(customerData))
    .setPrimaryPhone(bulkInviteCustomerReader.primaryPhone(customerData))
    .setCustomerType(bulkInviteCustomerReader.customerType(customerData))
    .setIsCharge(bulkInviteCustomerReader.isCharge(customerData))
    .setStatus(bulkInviteCustomerReader.status(customerData));

export const mapCustomersFromCMS = customerData =>
  new CustomerBuilder()
    .setCustomerId(customerReader.customerId(customerData))
    .setFirstName(customerReader.firstName(customerData))
    .setMiddleName(customerReader.middleName(customerData))
    .setLastName(customerReader.lastName(customerData))
    .setCustomerNumber(customerReader.customerNumber(customerData))
    .setEmail(customerReader.email(customerData))
    .setPrimaryPhone(customerReader.primaryPhone(customerData))
    .setCustomerType(customerReader.customerType(customerData))
    .setIsCharge(customerReader.chargeCustomer(customerData))
    .setStatus(customerReader.status(customerData));
