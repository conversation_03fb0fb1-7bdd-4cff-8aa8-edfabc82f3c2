import compose from 'recompose/compose';

// Lodash
import _concat from 'lodash/concat';
import _last from 'lodash/last';
import _map from 'lodash/map';
import _filter from 'lodash/filter';
import _head from 'lodash/head';

// Constants
import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';

// Readers
import esResponseReader from 'tbase/readers/EsResponse';
import esBucketReader from 'tbase/readers/EsBucket';
import esGroupReader from 'tbase/readers/EsGroup';
import emailTemplateReader from 'readers/EmailTemplateReader';

// Helpers
import { mapCustomersFromInitialFetch, mapCustomersFromCMS } from './businessAccessManagerBulkInvite.customerMapper';

const getLastCustomerIdFromPreviousBatch = compose(customer => customer.getCustomerId(), _last);

const normalizeCustomersFromInitialFetch = customers => _map(customers, mapCustomersFromInitialFetch);

export const getProcessedPaginationResponse = ({ newCustomers, existingCustomers, clearData }) => {
  const normalizedNewCustomers = normalizeCustomersFromInitialFetch(newCustomers);

  return {
    customers: clearData ? normalizedNewCustomers : _concat(existingCustomers, normalizedNewCustomers),
    nextPageKey: getLastCustomerIdFromPreviousBatch(normalizedNewCustomers),
  };
};

export const getCustomerFullName = customer => {
  const firstName = customer.getFirstName() || '';
  const middleName = customer.getMiddleName() || '';
  const lastName = customer.getLastName() || '';
  const fullName = [firstName, middleName, lastName].filter(name => name && name.trim()).join(' ');
  return fullName;
};

export const getCustomerDisplayName = customer => {
  const customerName = getCustomerFullName(customer);
  const customerNumber = customer.getCustomerNumber();
  return `${customerNumber} - ${customerName}`;
};

export const getCustomerPhoneNumberProps = rowInfo => {
  const { original: customer } = rowInfo;
  const primaryPhone = customer.getPrimaryPhone();
  if (!primaryPhone) {
    return null;
  }
  return {
    ...primaryPhone,
    value: primaryPhone.number,
  };
};

const isDuplicateGroup = group => {
  const customerCount = esResponseReader.docCount(group) || 0;
  return customerCount > 1;
};

const transformBucketToEmailGroup = bucket => {
  const docCount = esBucketReader.docCount(bucket) || 0;
  const customers = esBucketReader.hits(bucket) || EMPTY_ARRAY;
  const normalizedCustomers = _map(customers, mapCustomersFromCMS);
  return {
    email: esBucketReader.key(bucket),
    customerCount: docCount,
    customers: normalizedCustomers,
    isRestricted: docCount > 100,
  };
};

export const processDuplicateDetectionResponse = response => {
  const responseData = response?.data || EMPTY_OBJECT;
  const groups = esResponseReader.groups(responseData);
  const firstGroup = _head(groups) || EMPTY_OBJECT;
  const buckets = esGroupReader.buckets(firstGroup) || EMPTY_ARRAY;

  const duplicateBuckets = _filter(buckets, isDuplicateGroup);

  return _map(duplicateBuckets, transformBucketToEmailGroup);
};

export const getEmailTemplateDataFromResponse = response => {
  const templates = response?.data || EMPTY_ARRAY;
  const emailTemplate = _head(templates);
  const templateInfo = emailTemplateReader.templateInfo(emailTemplate);
  const subject = emailTemplateReader.subject(templateInfo);
  const fromEmail = emailTemplateReader.fromEmail(templateInfo);
  const replyToEmail = emailTemplateReader.replyToEmail(templateInfo);

  return {
    subject,
    fromEmail,
    replyToEmail,
    originalTemplate: emailTemplate,
  };
};
