// Lodash
import _map from 'lodash/map';
import _uniq from 'lodash/uniq';

// Constants
import { EMPTY_ARRAY } from 'tbase/app.constants';
import OPERATORS from 'tbase/constants/filterOperators';
import METRIC_FUNCTIONS_CONSTANTS from 'tbase/constants/metricFunction.constants';
import { PAGE_ROWS_MAX } from 'tbase/constants/pageInfo';

// Builders
import Request from 'tbase/builders/request';
import Projection from 'tbase/builders/request/Projection';
import GroupBy from 'tbase/builders/request/GroupBy';

const CUSTOMER_NUMBER_PROJECTION = new Projection()
  .setKey('customerNumber')
  .setField('customerNumber')
  .setRows(100)
  .addIncludeFields(['customerNumber', 'firstName', 'lastName', 'phones'])
  .setMetricFunction(METRIC_FUNCTIONS_CONSTANTS.TOP_HITS);

const EMAIL_GROUP_BY = new GroupBy()
  .setKey('byemailId')
  .setField('email')
  .setGroupType('FIELD')
  .setRows(PAGE_ROWS_MAX)
  .setProjections([CUSTOMER_NUMBER_PROJECTION]);

const extractEmailsFromCustomers = invitedCustomers => {
  const emails = _map(invitedCustomers, customer => customer.getEmail());
  return _uniq(emails);
};

export const createDuplicateDetectionPayload = (invitedCustomers = EMPTY_ARRAY) => {
  const emails = extractEmailsFromCustomers(invitedCustomers);
  const request = new Request().addFilter('email', OPERATORS.IN, emails).setPage(0, 0).setGroupBy([EMAIL_GROUP_BY]);

  return request;
};
