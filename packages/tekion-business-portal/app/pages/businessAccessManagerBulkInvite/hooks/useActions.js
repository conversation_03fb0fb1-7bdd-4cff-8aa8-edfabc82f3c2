import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';

// Lodash
import _noop from 'lodash/noop';

// Constants
import { ACTION_TYPES } from '../constants/businessAccessManagerBulkInvite.actionTypes';

const useActions = (onAction = _noop) => {
  const navigate = useNavigate();

  const handleRedirectToListView = useCallback(() => {
    onAction({ type: ACTION_TYPES.CANCEL, payload: { navigate } });
  }, [onAction, navigate]);

  const handleEmailPreviewClick = useCallback(() => {
    onAction({ type: ACTION_TYPES.EMAIL_PREVIEW_OPEN });
  }, [onAction]);

  const handleEmailPreviewClose = useCallback(() => {
    onAction({ type: ACTION_TYPES.EMAIL_PREVIEW_CLOSE });
  }, [onAction]);

  const handleSendInvite = useCallback(() => {
    onAction({
      type: ACTION_TYPES.SEND_INVITE,
      payload: {
        navigate,
      },
    });
  }, [onAction, navigate]);

  const handleCustomerSelect = useCallback(
    ({ customerIdsToSelect }) => {
      onAction({
        type: ACTION_TYPES.SELECT_CUSTOMERS,
        payload: {
          customerIdsToSelect,
        },
      });
    },
    [onAction]
  );

  const handleSearchChange = useCallback(
    event => {
      const value = event && event.target ? event.target.value : event;

      onAction({
        type: ACTION_TYPES.SEARCH_CHANGE,
        payload: { value },
      });
    },
    [onAction]
  );

  const handleFilterChange = useCallback(
    filters => {
      onAction({
        type: ACTION_TYPES.FILTER_CHANGE,
        payload: { filters },
      });
    },
    [onAction]
  );

  const handleNavigateToProfile = useCallback(
    customer => {
      onAction({
        type: ACTION_TYPES.NAVIGATE_TO_PROFILE,
        payload: { customer },
      });
    },
    [onAction]
  );

  const handleLoadMoreCustomers = useCallback(() => {
    onAction({
      type: ACTION_TYPES.LOAD_MORE_CUSTOMERS,
    });
  }, [onAction]);

  const handleAddCustomers = useCallback(() => {
    onAction({
      type: ACTION_TYPES.ADD_CUSTOMERS,
    });
  }, [onAction]);

  const handleRemoveSelectedCustomer = useCallback(
    customer => {
      onAction({
        type: ACTION_TYPES.REMOVE_SELECTED_CUSTOMER,
        payload: { customer },
      });
    },
    [onAction]
  );

  const handleRemoveAllSelectedCustomers = useCallback(() => {
    onAction({
      type: ACTION_TYPES.REMOVE_ALL_SELECTED_CUSTOMERS,
    });
  }, [onAction]);

  const handleDuplicateModalCancel = useCallback(() => {
    onAction({
      type: ACTION_TYPES.DUPLICATE_MODAL_CANCEL,
    });
  }, [onAction]);

  const handleSendInviteWithDuplicates = useCallback(
    selectedCustomerIds => {
      onAction({
        type: ACTION_TYPES.SEND_INVITE_WITH_DUPLICATES,
        params: { selectedCustomerIds },
      });
    },
    [onAction]
  );

  return {
    handleRedirectToListView,
    handleEmailPreviewClick,
    handleEmailPreviewClose,
    handleSendInvite,
    handleCustomerSelect,
    handleSearchChange,
    handleFilterChange,
    handleNavigateToProfile,
    handleLoadMoreCustomers,
    handleAddCustomers,
    handleRemoveSelectedCustomer,
    handleRemoveAllSelectedCustomers,
    handleDuplicateModalCancel,
    handleSendInviteWithDuplicates,
  };
};

export default useActions;
