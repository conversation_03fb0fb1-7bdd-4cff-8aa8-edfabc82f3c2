// Lodash
import _property from 'lodash/property';

const customerId = _property('customerId');
const firstName = _property('firstName');
const middleName = _property('middleName');
const lastName = _property('lastName');
const customerNumber = _property('customerNumber');
const email = _property('email');
const primaryPhone = _property('primaryPhone');
const lastActivity = _property('lastActivity');
const portalStatus = _property('portalStatus');
const customerType = _property('customerType');
const isCharge = _property('isCharge');
const status = _property('status');

const READER = {
  customerId,
  firstName,
  middleName,
  lastName,
  customerNumber,
  email,
  primaryPhone,
  lastActivity,
  portalStatus,
  customerType,
  isCharge,
  status,
};

export default READER;
