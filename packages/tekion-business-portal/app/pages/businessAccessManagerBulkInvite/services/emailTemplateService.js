// Utils
// import Http from 'tbusiness/utils/http';

// Mock data for development - remove when real API is available
const MOCK_EMAIL_TEMPLATE_RESPONSE = {
  data: [
    {
      lastModifiedBy: '2521b7fd-e9fe-40ae-99fb-4c41ee463996',
      createdBy: '2521b7fd-e9fe-40ae-99fb-4c41ee463996',
      deleted: false,
      createdTime: 1622452642380,
      modifiedTime: 1650884340467,
      id: '60b4a9a26739b000060b1c64',
      templateId: '414db2eb-e24f-466e-996c-efdc4a473807',
      templateType: 'ONBOARD',
      templateInfo: {
        templateBody: `<!DOCTYPE html>
<html>
  <head>
    <title>Exclusive Access to the Customer Portal</title>
    <style>
    </style>
  </head>
  <body>
        <p style=\${email_paragraph_style}>Dear Customer,</p>
        <p style=\${email_paragraph_style}>
          We are pleased to inform you that access to the AR portal has been granted.
          You can now manage your accounts receivable efficiently.
        </p>
        <p style=\${email_paragraph_style}>
          Please click the link to sign up or log in: <a href="#" style=" text-decoration: underline;">Access the Customer Portal</a>
        </p>
        <p style=\${email_closing_phrase}>Thank you,</p>
        <p style=\${email_paragraph_style}>Gateway Cars</p>

  </body>
</html>`,
        subject: 'Exclusive Access to the Customer Portal',
        fromEmail: '<EMAIL>',
        replyToEmail: '<EMAIL>',
      },
      locale: 'en_US',
      createdByUserId: '485',
      modifiedByUserId: '********-e884-4316-99fb-ae024d9dcf54',
      dealerId: '4',
      tenantId: 'cacargroup',
    },
  ],
  status: 'success',
};

export const fetchEmailTemplate = (_emailType, _language = 'en_US') =>
  // For now, return mock data immediately - replace with actual API call when ready
  Promise.resolve(MOCK_EMAIL_TEMPLATE_RESPONSE);

// Uncomment when real API is available:
// const url = addQueryParams(`/cpms/u/email/preview/type/${emailType}`, { lan: language });
// return Http.CPMS.get(url);
