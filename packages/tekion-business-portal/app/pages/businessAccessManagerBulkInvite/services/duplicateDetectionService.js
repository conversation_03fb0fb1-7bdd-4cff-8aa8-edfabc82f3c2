// Lodash
import _filter from 'lodash/filter';

// Services
import HTTP from 'tbase/services/apiService/httpClient';

// Constants
import { URL_TYPES } from 'tbase/constants/api';
import { EMPTY_ARRAY } from 'tbase/app.constants';

// Utils
import getDataFromResponse from 'tbase/utils/getDataFromResponse';

// Mock data for testing
import mockDuplicateData from './mockDuplicateData';

/**
 * API endpoint for customer advanced search
 */
const CUSTOMER_ADVANCED_SEARCH_ENDPOINT = '/api/cms/u/v2/customers/advancedsearch';

/**
 * Flag to enable/disable mock mode for testing
 */
const USE_MOCK_DATA = true; // Set to false to use real API

/**
 * Calls the customer advanced search API to detect duplicate emails
 * @param {Object} payload - Search payload with email filters and groupBy
 * @param {Array} originalInvitedCustomers - Original invited customers for proper preselection
 * @param {string} locale - Locale for the request (default: 'en_US')
 * @returns {Promise<Object>} - API response with grouped customer data
 */
export const fetchDuplicateCustomersByEmail = async (payload, originalInvitedCustomers = [], locale = 'en_US') => {
  if (USE_MOCK_DATA) {
    // Use mock data for testing
    return mockFetchDuplicateCustomersByEmail(payload, originalInvitedCustomers);
  }

  const response = await HTTP.post(URL_TYPES.CDMS, `${CUSTOMER_ADVANCED_SEARCH_ENDPOINT}?locale=${locale}`, payload);

  return getDataFromResponse(response);
};

/**
 * Mock implementation for duplicate detection
 * @param {Object} payload - Search payload with email filters
 * @param {Array} originalInvitedCustomers - Original invited customers for proper preselection
 * @returns {Promise<Object>} - Mock API response with grouped customer data
 */
const mockFetchDuplicateCustomersByEmail = async (payload, originalInvitedCustomers = []) => {
  // Simulate API delay
  await new Promise(resolve => {
    setTimeout(resolve, 1500);
  });

  const emailFilters = _filter(payload.filters || EMPTY_ARRAY, filter => filter.field === 'email');
  const emailsToCheck = emailFilters.length > 0 ? emailFilters[0].values || EMPTY_ARRAY : EMPTY_ARRAY;

  if (emailsToCheck.length === 0) {
    return {
      data: {
        groups: [
          {
            buckets: EMPTY_ARRAY,
          },
        ],
      },
    };
  }

  const mockResponse = mockDuplicateData.generateMockDuplicateResponse(emailsToCheck, originalInvitedCustomers);

  return {
    data: mockResponse,
  };
};
