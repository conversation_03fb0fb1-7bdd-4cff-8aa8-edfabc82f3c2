// Lodash
import _map from 'lodash/map';
import _filter from 'lodash/filter';
import _includes from 'lodash/includes';

// Constants
import { EMPTY_ARRAY } from 'tbase/app.constants';

/**
 * Mock customer data for duplicate detection testing
 * These customers match the duplicate email scenarios from customerService.js
 */
const MOCK_CUSTOMERS_POOL = [
  // <EMAIL> duplicates (3 customers)
  {
    id: 'dup_001',
    customerId: 'dup_001',
    customerNumber: 'D001',
    firstName: 'John',
    middleName: '',
    lastName: 'Smith',
    email: '<EMAIL>',
    phones: [{ number: '555-1001', countryCode: '+1', extension: '', ndc: '555' }],
    primaryPhone: { number: '555-1001', countryCode: '+1', extension: '', ndc: '555' },
    customerType: 'INDIVIDUAL',
    accountingInfo: { chargeCustomer: false },
    status: 'ACTIVE',
  },
  {
    id: 'dup_002',
    customerId: 'dup_002',
    customerNumber: 'D002',
    firstName: 'Johnny',
    middleName: '',
    lastName: 'Smith',
    email: '<EMAIL>',
    phones: [{ number: '555-1002', countryCode: '+1', extension: '', ndc: '555' }],
    primaryPhone: { number: '555-1002', countryCode: '+1', extension: '', ndc: '555' },
    customerType: 'INDIVIDUAL',
    accountingInfo: { chargeCustomer: true },
    status: 'ACTIVE',
  },
  {
    id: 'dup_003',
    customerId: 'dup_003',
    customerNumber: 'D003',
    firstName: 'Jon',
    middleName: '',
    lastName: 'Smith',
    email: '<EMAIL>',
    phones: [{ number: '555-1003', countryCode: '+1', extension: '', ndc: '555' }],
    primaryPhone: { number: '555-1003', countryCode: '+1', extension: '', ndc: '555' },
    customerType: 'INDIVIDUAL',
    accountingInfo: { chargeCustomer: false },
    status: 'ACTIVE',
  },

  // <EMAIL> duplicates (4 customers)
  {
    id: 'dup_004',
    customerId: 'dup_004',
    customerNumber: 'D004',
    firstName: 'Admin',
    middleName: '',
    lastName: 'User',
    email: '<EMAIL>',
    phones: [{ number: '555-2001', countryCode: '+1', extension: '', ndc: '555' }],
    primaryPhone: { number: '555-2001', countryCode: '+1', extension: '', ndc: '555' },
    customerType: 'BUSINESS',
    accountingInfo: { chargeCustomer: true },
    status: 'ACTIVE',
  },
  {
    id: 'dup_005',
    customerId: 'dup_005',
    customerNumber: 'D005',
    firstName: 'Info',
    middleName: '',
    lastName: 'Desk',
    email: '<EMAIL>',
    phones: [{ number: '555-2002', countryCode: '+1', extension: '', ndc: '555' }],
    primaryPhone: { number: '555-2002', countryCode: '+1', extension: '', ndc: '555' },
    customerType: 'BUSINESS',
    accountingInfo: { chargeCustomer: false },
    status: 'ACTIVE',
  },
  {
    id: 'dup_006',
    customerId: 'dup_006',
    customerNumber: 'D006',
    firstName: 'General',
    middleName: '',
    lastName: 'Info',
    email: '<EMAIL>',
    phones: [{ number: '555-2003', countryCode: '+1', extension: '', ndc: '555' }],
    primaryPhone: { number: '555-2003', countryCode: '+1', extension: '', ndc: '555' },
    customerType: 'BUSINESS',
    accountingInfo: { chargeCustomer: true },
    status: 'ACTIVE',
  },
  {
    id: 'dup_007',
    customerId: 'dup_007',
    customerNumber: 'D007',
    firstName: 'Contact',
    middleName: '',
    lastName: 'Center',
    email: '<EMAIL>',
    phones: [{ number: '555-2004', countryCode: '+1', extension: '', ndc: '555' }],
    primaryPhone: { number: '555-2004', countryCode: '+1', extension: '', ndc: '555' },
    customerType: 'BUSINESS',
    accountingInfo: { chargeCustomer: false },
    status: 'ACTIVE',
  },

  // <EMAIL> duplicates (2 customers)
  {
    id: 'dup_008',
    customerId: 'dup_008',
    customerNumber: 'D008',
    firstName: 'Sarah',
    middleName: '',
    lastName: 'Johnson',
    email: '<EMAIL>',
    phones: [{ number: '555-3001', countryCode: '+1', extension: '', ndc: '555' }],
    primaryPhone: { number: '555-3001', countryCode: '+1', extension: '', ndc: '555' },
    customerType: 'INDIVIDUAL',
    accountingInfo: { chargeCustomer: false },
    status: 'ACTIVE',
  },
  {
    id: 'dup_009',
    customerId: 'dup_009',
    customerNumber: 'D009',
    firstName: 'Sara',
    middleName: '',
    lastName: 'Johnson',
    email: '<EMAIL>',
    phones: [{ number: '555-3002', countryCode: '+1', extension: '', ndc: '555' }],
    primaryPhone: { number: '555-3002', countryCode: '+1', extension: '', ndc: '555' },
    customerType: 'INDIVIDUAL',
    accountingInfo: { chargeCustomer: true },
    status: 'ACTIVE',
  },

  // <EMAIL> duplicates (5 customers - warning scenario)
  {
    id: 'dup_010',
    customerId: 'dup_010',
    customerNumber: 'D010',
    firstName: 'Mike',
    middleName: '',
    lastName: 'Davis',
    email: '<EMAIL>',
    phones: [{ number: '555-4001', countryCode: '+1', extension: '', ndc: '555' }],
    primaryPhone: { number: '555-4001', countryCode: '+1', extension: '', ndc: '555' },
    customerType: 'INDIVIDUAL',
    accountingInfo: { chargeCustomer: false },
    status: 'ACTIVE',
  },
  {
    id: 'dup_011',
    customerId: 'dup_011',
    customerNumber: 'D011',
    firstName: 'Michael',
    middleName: '',
    lastName: 'Davis',
    email: '<EMAIL>',
    phones: [{ number: '555-4002', countryCode: '+1', extension: '', ndc: '555' }],
    primaryPhone: { number: '555-4002', countryCode: '+1', extension: '', ndc: '555' },
    customerType: 'INDIVIDUAL',
    accountingInfo: { chargeCustomer: true },
    status: 'ACTIVE',
  },
  {
    id: 'dup_012',
    customerId: 'dup_012',
    customerNumber: 'D012',
    firstName: 'Mikey',
    middleName: '',
    lastName: 'Davis',
    email: '<EMAIL>',
    phones: [{ number: '555-4003', countryCode: '+1', extension: '', ndc: '555' }],
    primaryPhone: { number: '555-4003', countryCode: '+1', extension: '', ndc: '555' },
    customerType: 'INDIVIDUAL',
    accountingInfo: { chargeCustomer: false },
    status: 'ACTIVE',
  },

  // <EMAIL> duplicates (continued - customers 4 & 5)
  {
    id: 'dup_013',
    customerId: 'dup_013',
    customerNumber: 'D013',
    firstName: 'Customer',
    middleName: '',
    lastName: 'Service',
    email: '<EMAIL>',
    phones: [{ number: '555-5001', countryCode: '+1', extension: '', ndc: '555' }],
    primaryPhone: { number: '555-5001', countryCode: '+1', extension: '', ndc: '555' },
    customerType: 'BUSINESS',
    accountingInfo: { chargeCustomer: true },
    status: 'ACTIVE',
  },
  {
    id: 'dup_014',
    customerId: 'dup_014',
    customerNumber: 'D014',
    firstName: 'Help',
    middleName: '',
    lastName: 'Desk',
    email: '<EMAIL>',
    phones: [{ number: '555-5002', countryCode: '+1', extension: '', ndc: '555' }],
    primaryPhone: { number: '555-5002', countryCode: '+1', extension: '', ndc: '555' },
    customerType: 'BUSINESS',
    accountingInfo: { chargeCustomer: false },
    status: 'ACTIVE',
  },

  // <EMAIL> duplicates (2 customers)
  {
    id: 'dup_015',
    customerId: 'dup_015',
    customerNumber: 'D015',
    firstName: 'Mike',
    middleName: '',
    lastName: 'Davis',
    email: '<EMAIL>',
    phones: [{ number: '555-6001', countryCode: '+1', extension: '', ndc: '555' }],
    primaryPhone: { number: '555-6001', countryCode: '+1', extension: '', ndc: '555' },
    customerType: 'INDIVIDUAL',
    accountingInfo: { chargeCustomer: false },
    status: 'ACTIVE',
  },
  {
    id: 'dup_016',
    customerId: 'dup_016',
    customerNumber: 'D016',
    firstName: 'Michael',
    middleName: 'D',
    lastName: 'Davis',
    email: '<EMAIL>',
    phones: [{ number: '555-6002', countryCode: '+1', extension: '', ndc: '555' }],
    primaryPhone: { number: '555-6002', countryCode: '+1', extension: '', ndc: '555' },
    customerType: 'INDIVIDUAL',
    accountingInfo: { chargeCustomer: true },
    status: 'ACTIVE',
  },
  {
    id: 'dup_015',
    customerId: 'dup_015',
    customerNumber: 'D015',
    firstName: 'Admin',
    middleName: '',
    lastName: 'Support',
    email: '<EMAIL>',
    phones: [{ number: '555-5003', countryCode: '+1', extension: '', ndc: '555' }],
    primaryPhone: { number: '555-5003', countryCode: '+1', extension: '', ndc: '555' },
    customerType: 'BUSINESS',
    accountingInfo: { chargeCustomer: true },
    status: 'ACTIVE',
  },
  {
    id: 'dup_016',
    customerId: 'dup_016',
    customerNumber: 'D016',
    firstName: 'Network',
    middleName: '',
    lastName: 'Admin',
    email: '<EMAIL>',
    phones: [{ number: '555-5004', countryCode: '+1', extension: '', ndc: '555' }],
    primaryPhone: { number: '555-5004', countryCode: '+1', extension: '', ndc: '555' },
    customerType: 'BUSINESS',
    accountingInfo: { chargeCustomer: false },
    status: 'ACTIVE',
  },
  {
    id: 'dup_017',
    customerId: 'dup_017',
    customerNumber: 'D017',
    firstName: 'Database',
    middleName: '',
    lastName: 'Admin',
    email: '<EMAIL>',
    phones: [{ number: '555-5005', countryCode: '+1', extension: '', ndc: '555' }],
    primaryPhone: { number: '555-5005', countryCode: '+1', extension: '', ndc: '555' },
    customerType: 'BUSINESS',
    accountingInfo: { chargeCustomer: true },
    status: 'ACTIVE',
  },

  // <EMAIL> duplicates (4 customers)
  {
    id: 'dup_018',
    customerId: 'dup_018',
    customerNumber: 'D018',
    firstName: 'Contact',
    middleName: '',
    lastName: 'Person',
    email: '<EMAIL>',
    phones: [{ number: '555-6001', countryCode: '+1', extension: '', ndc: '555' }],
    primaryPhone: { number: '555-6001', countryCode: '+1', extension: '', ndc: '555' },
    customerType: 'BUSINESS',
    accountingInfo: { chargeCustomer: false },
    status: 'ACTIVE',
  },
  {
    id: 'dup_019',
    customerId: 'dup_019',
    customerNumber: 'D019',
    firstName: 'Customer',
    middleName: '',
    lastName: 'Contact',
    email: '<EMAIL>',
    phones: [{ number: '555-6002', countryCode: '+1', extension: '', ndc: '555' }],
    primaryPhone: { number: '555-6002', countryCode: '+1', extension: '', ndc: '555' },
    customerType: 'BUSINESS',
    accountingInfo: { chargeCustomer: true },
    status: 'ACTIVE',
  },
  {
    id: 'dup_020',
    customerId: 'dup_020',
    customerNumber: 'D020',
    firstName: 'Main',
    middleName: '',
    lastName: 'Contact',
    email: '<EMAIL>',
    phones: [{ number: '555-6003', countryCode: '+1', extension: '', ndc: '555' }],
    primaryPhone: { number: '555-6003', countryCode: '+1', extension: '', ndc: '555' },
    customerType: 'BUSINESS',
    accountingInfo: { chargeCustomer: false },
    status: 'ACTIVE',
  },
  {
    id: 'dup_021',
    customerId: 'dup_021',
    customerNumber: 'D021',
    firstName: 'Primary',
    middleName: '',
    lastName: 'Contact',
    email: '<EMAIL>',
    phones: [{ number: '555-6004', countryCode: '+1', extension: '', ndc: '555' }],
    primaryPhone: { number: '555-6004', countryCode: '+1', extension: '', ndc: '555' },
    customerType: 'BUSINESS',
    accountingInfo: { chargeCustomer: true },
    status: 'ACTIVE',
  },

  // <EMAIL> duplicates (6 customers - high warning scenario)
  {
    id: 'dup_022',
    customerId: 'dup_022',
    customerNumber: 'D022',
    firstName: 'Support',
    middleName: '',
    lastName: 'Team',
    email: '<EMAIL>',
    phones: [{ number: '555-7001', countryCode: '+1', extension: '', ndc: '555' }],
    primaryPhone: { number: '555-7001', countryCode: '+1', extension: '', ndc: '555' },
    customerType: 'BUSINESS',
    accountingInfo: { chargeCustomer: true },
    status: 'ACTIVE',
  },
  {
    id: 'dup_023',
    customerId: 'dup_023',
    customerNumber: 'D023',
    firstName: 'Customer',
    middleName: '',
    lastName: 'Support',
    email: '<EMAIL>',
    phones: [{ number: '555-7002', countryCode: '+1', extension: '', ndc: '555' }],
    primaryPhone: { number: '555-7002', countryCode: '+1', extension: '', ndc: '555' },
    customerType: 'BUSINESS',
    accountingInfo: { chargeCustomer: false },
    status: 'ACTIVE',
  },
  {
    id: 'dup_024',
    customerId: 'dup_024',
    customerNumber: 'D024',
    firstName: 'Tech',
    middleName: '',
    lastName: 'Support',
    email: '<EMAIL>',
    phones: [{ number: '555-7003', countryCode: '+1', extension: '', ndc: '555' }],
    primaryPhone: { number: '555-7003', countryCode: '+1', extension: '', ndc: '555' },
    customerType: 'BUSINESS',
    accountingInfo: { chargeCustomer: true },
    status: 'ACTIVE',
  },
  {
    id: 'dup_025',
    customerId: 'dup_025',
    customerNumber: 'D025',
    firstName: 'Help',
    middleName: '',
    lastName: 'Desk',
    email: '<EMAIL>',
    phones: [{ number: '555-7004', countryCode: '+1', extension: '', ndc: '555' }],
    primaryPhone: { number: '555-7004', countryCode: '+1', extension: '', ndc: '555' },
    customerType: 'BUSINESS',
    accountingInfo: { chargeCustomer: false },
    status: 'ACTIVE',
  },
  {
    id: 'dup_026',
    customerId: 'dup_026',
    customerNumber: 'D026',
    firstName: 'Service',
    middleName: '',
    lastName: 'Support',
    email: '<EMAIL>',
    phones: [{ number: '555-7005', countryCode: '+1', extension: '', ndc: '555' }],
    primaryPhone: { number: '555-7005', countryCode: '+1', extension: '', ndc: '555' },
    customerType: 'BUSINESS',
    accountingInfo: { chargeCustomer: true },
    status: 'ACTIVE',
  },
  {
    id: 'dup_027',
    customerId: 'dup_027',
    customerNumber: 'D027',
    firstName: 'Online',
    middleName: '',
    lastName: 'Support',
    email: '<EMAIL>',
    phones: [{ number: '555-7006', countryCode: '+1', extension: '', ndc: '555' }],
    primaryPhone: { number: '555-7006', countryCode: '+1', extension: '', ndc: '555' },
    customerType: 'BUSINESS',
    accountingInfo: { chargeCustomer: false },
    status: 'ACTIVE',
  },
];

/**
 * Email groups that have duplicates in the mock data
 * These MUST match the exact duplicate scenarios from customerService.js initial fetch API
 */
const DUPLICATE_EMAIL_GROUPS = [
  {
    email: '<EMAIL>', // Scenario 1: 3 customers (indices [5, 15, 25])
    customers: ['dup_001', 'dup_002', 'dup_003'],
  },
  {
    email: '<EMAIL>', // Scenario 2: 4 customers (indices [8, 18, 28, 38])
    customers: ['dup_004', 'dup_005', 'dup_006', 'dup_007'],
  },
  {
    email: '<EMAIL>', // Scenario 3: 2 customers (indices [12, 32])
    customers: ['dup_008', 'dup_009'],
  },
  {
    email: '<EMAIL>', // Scenario 4: 5 customers (indices [3, 13, 23, 33, 43])
    customers: ['dup_010', 'dup_011', 'dup_012', 'dup_013', 'dup_014'],
  },
  {
    email: '<EMAIL>', // Scenario 5: 2 customers (indices [7, 27])
    customers: ['dup_015', 'dup_016'],
  },
  {
    email: '<EMAIL>', // Scenario 6: 3 customers (indices [10, 20, 40])
    customers: ['dup_017', 'dup_018', 'dup_019'],
  },
  {
    email: '<EMAIL>', // Scenario 7: 2 customers (indices [16, 36])
    customers: ['dup_020', 'dup_021'],
  },
  {
    email: '<EMAIL>', // Scenario 8: 6 customers (indices [2, 9, 19, 29, 39, 49])
    customers: ['dup_022', 'dup_023', 'dup_024', 'dup_025', 'dup_026', 'dup_027'],
  },
];

/**
 * Generates mock duplicate detection response based on provided emails
 * @param {Array} emailsToCheck - Array of email addresses to check for duplicates
 * @param {Array} originalInvitedCustomers - Original invited customers for context
 * @returns {Object} Mock API response in the expected format
 */
export const generateMockDuplicateResponse = (emailsToCheck = EMPTY_ARRAY, originalInvitedCustomers = EMPTY_ARRAY) => {
  const buckets = [];

  // Note: originalInvitedCustomers parameter available for future enhancement
  // For each email being checked, see if it has duplicates in our mock data
  emailsToCheck.forEach(email => {
    const duplicateGroup = DUPLICATE_EMAIL_GROUPS.find(group => group.email === email);

    if (duplicateGroup) {
      // Get existing customers from the system for this email
      const existingCustomers = _filter(MOCK_CUSTOMERS_POOL, customer =>
        _includes(duplicateGroup.customers, customer.customerId)
      );

      // Get original customers being invited with this email
      const originalCustomersWithThisEmail = _filter(originalInvitedCustomers, customer => customer.email === email);

      // Combine both existing and original customers
      const customersForEmail = [...existingCustomers, ...originalCustomersWithThisEmail];

      // Create bucket for this email group - structure matches real API response
      const bucket = {
        key: email,
        docCount: customersForEmail.length,
        groups: null,
        projections: null,
        pipelineGroups: null,
        nestedHits: null,
        // Customer data is directly in hits array, not wrapped in _source
        hits: customersForEmail,
        value: null,
        keyMap: null,
      };

      buckets.push(bucket);
    }
  });

  return {
    groups: [
      {
        buckets,
      },
    ],
  };
};

/**
 * Mock data for testing restricted email groups (over 100 customers)
 */
export const generateRestrictedEmailMockResponse = (emailsToCheck = EMPTY_ARRAY) => {
  const buckets = _map(emailsToCheck, email => ({
    key: email,
    docCount: 150, // Over 100 to trigger restricted mode
    customerNumber: {
      hits: {
        total: {
          value: 150,
        },
        hits: EMPTY_ARRAY, // No hits returned for restricted groups
      },
    },
  }));

  return {
    groups: [
      {
        buckets,
      },
    ],
  };
};

export default {
  generateMockDuplicateResponse,
  generateRestrictedEmailMockResponse,
  MOCK_CUSTOMERS_POOL,
  DUPLICATE_EMAIL_GROUPS,
};
