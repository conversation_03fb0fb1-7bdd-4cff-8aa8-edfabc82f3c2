// Lodash
import _filter from 'lodash/filter';
import _forEach from 'lodash/forEach';
import _map from 'lodash/map';
import _includes from 'lodash/includes';
import _slice from 'lodash/slice';
import _size from 'lodash/size';

// Constants
import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import { STATUS } from 'tbase/constants/status.constants';

// API call counter for testing - reset on page load
let _apiCallCounter = 0;

// Cache for consistent mock data across API calls
let cachedMockCustomers = null;

// Reset counter function for testing
export const resetApiCounter = () => {
  _apiCallCounter = 0;
  cachedMockCustomers = null; // Reset cache too
};

export const mockSearchCustomers = (params = EMPTY_OBJECT) => {
  _apiCallCounter += 1;
  const {
    TekSearchRequest = EMPTY_OBJECT,
    pageSize = 500,
    lastCustomerIdFromPreviousBatch = null,
    excludeCustomerIds = EMPTY_ARRAY,
  } = params;
  const { searchText = '', filters = EMPTY_ARRAY } = TekSearchRequest;

  // Generate 1000 mock customers for testing pagination and max limit
  // Force regeneration to ensure unique customer IDs
  cachedMockCustomers = generateMockCustomers(1000);
  const allCustomers = cachedMockCustomers;

  // Apply filters and search
  let filteredCustomers = allCustomers;

  // Exclude selected customers if provided
  if (excludeCustomerIds && _size(excludeCustomerIds) > 0) {
    filteredCustomers = _filter(filteredCustomers, customer => !_includes(excludeCustomerIds, customer.customerId));
  }

  // Apply search filter
  if (searchText) {
    const searchLower = searchText.toLowerCase();
    filteredCustomers = _filter(filteredCustomers, customer => {
      // Construct full name including middle name for search
      const fullName = [customer.firstName, customer.middleName, customer.lastName]
        .filter(part => part && part.trim())
        .join(' ')
        .toLowerCase();

      return (
        customer.customerId.toLowerCase().includes(searchLower) ||
        (customer.customerNumber && customer.customerNumber.toLowerCase().includes(searchLower)) ||
        fullName.includes(searchLower) ||
        (customer.firstName && customer.firstName.toLowerCase().includes(searchLower)) ||
        (customer.middleName && customer.middleName.toLowerCase().includes(searchLower)) ||
        (customer.lastName && customer.lastName.toLowerCase().includes(searchLower)) ||
        (customer.email && customer.email.toLowerCase().includes(searchLower)) ||
        (customer.mobileNumber && customer.mobileNumber.toString().includes(searchLower))
      );
    });
  }

  // Apply filters
  if (filters && _size(filters) > 0) {
    _forEach(filters, filter => {
      const { type, values } = filter;

      if (type === 'customerType' && values && _size(values) > 0) {
        filteredCustomers = _filter(filteredCustomers, customer => _includes(values, customer.customerType));
      }

      if (type === 'isCharge' && values && _size(values) > 0) {
        // Convert string 'true'/'false' to boolean
        const isChargeValues = _map(values, value => {
          if (value === 'true') return true;
          if (value === 'false') return false;
          return value;
        });
        filteredCustomers = _filter(filteredCustomers, customer => _includes(isChargeValues, customer.isCharge));
      }

      if (type === 'status' && values && _size(values) > 0) {
        // Filter directly by the status property
        filteredCustomers = _filter(filteredCustomers, customer => _includes(values, customer.status));
      }
    });
  }

  // Find starting index based on lastCustomerIdFromPreviousBatch
  let startIndex = 0;
  if (lastCustomerIdFromPreviousBatch) {
    const lastCustomerIndex = filteredCustomers.findIndex(
      customer => customer.customerId === lastCustomerIdFromPreviousBatch
    );
    startIndex = lastCustomerIndex >= 0 ? lastCustomerIndex + 1 : 0;
  }

  const endIndex = startIndex + pageSize;
  const paginatedCustomers = _slice(filteredCustomers, startIndex, endIndex);

  // Backend behavior: Return empty array when no more data
  // This will trigger an extra call to detect end of pagination

  // Return array directly (no wrapper object) - just like real backend
  return Promise.resolve({
    data: paginatedCustomers, // Simple array of customers, no pagination metadata
  });
};

const generateUniqueEmail = (isBusinessCustomer, firstName, lastName, index) => {
  if (isBusinessCustomer) {
    const domain = firstName.toLowerCase().replace(/[^a-z0-9]/g, '');
    return `info@${domain}${index}.com`;
  }
  const firstPart = firstName.toLowerCase().replace(/[^a-z0-9]/g, '');
  const lastPart = lastName ? lastName.toLowerCase().replace(/[^a-z0-9]/g, '') : '';
  return `${firstPart}${lastPart ? `.${lastPart}` : ''}${index}@example.com`;
};

const generateMockCustomers = count => {
  const customers = [...EMPTY_ARRAY];
  // Use status constants from tekion-base
  const statuses = [STATUS.ACTIVE, STATUS.IN_ACTIVE, STATUS.ON_HOLD];
  const customerTypes = ['INDIVIDUAL', 'BUSINESS'];

  for (let i = 0; i < count; i += 1) {
    const isBusinessCustomer = Math.random() > 0.7;
    const customerType = isBusinessCustomer ? customerTypes[1] : customerTypes[0];

    let firstName = '';
    let middleName = '';
    let lastName = '';
    if (isBusinessCustomer) {
      const businessNames = [
        'Acme Corp',
        'TechSolutions',
        'Global Enterprises',
        'City Motors',
        'Valley Services',
        'Mountain View Auto',
        'Riverside Dealership',
        'Sunset Motors',
        'Premier Auto Group',
        'Elite Motors',
        'AutoNation',
        'CarMax',
        'Hendrick Automotive',
        'Penske Automotive',
        'Sonic Automotive',
        'Group 1 Automotive',
        'Lithia Motors',
        'Asbury Automotive',
        'Ken Garff Automotive',
        'Larry H. Miller Group',
        'Metro Auto Sales',
        'Downtown Motors',
        'Northside Automotive',
        'Eastside Dealers',
        'Westgate Auto',
        'Central Valley Motors',
        'Parkway Automotive',
        'Crossroads Auto',
        'Midtown Motors',
        'Uptown Auto Group',
        'Southside Dealers',
        'Lakeside Motors',
        'Hillside Auto',
        'Riverside Auto Group',
        'Oceanview Motors',
        'Skyline Automotive',
        'Gateway Auto Sales',
        'Cornerstone Motors',
        'Pinnacle Auto Group',
        'Summit Automotive',
        'Horizon Motors',
        'Landmark Auto',
        'Heritage Motors',
        'Legacy Auto Group',
        'Crown Automotive',
        'Royal Motors',
        'Imperial Auto',
        'Majestic Motors',
        'Supreme Auto Group',
        'Ultimate Motors',
        'Platinum Automotive',
        'Diamond Auto Sales',
        'Gold Star Motors',
        'Silver Lake Auto',
        'Crystal Motors',
        'Emerald Auto Group',
        'Sapphire Motors',
        'Ruby Automotive',
        'Pearl Auto Sales',
        'Opal Motors',
        'Jade Auto Group',
        'Amber Motors',
        'Onyx Automotive',
        'Granite Auto Sales',
        'Marble Motors',
        'Slate Auto Group',
        'Stone Motors',
        'Rock Automotive',
        'Steel Auto Sales',
        'Iron Motors',
        'Copper Auto Group',
        'Bronze Motors',
        'Titanium Automotive',
        'Platinum Auto Sales',
        'Chrome Motors',
        'Velocity Auto Group',
        'Momentum Motors',
        'Acceleration Auto',
        'Turbo Motors',
        'Nitro Auto Group',
        'Boost Motors',
        'Power Automotive',
        'Force Auto Sales',
        'Energy Motors',
        'Dynamic Auto Group',
        'Motion Motors',
        'Speed Automotive',
        'Fast Track Auto',
        'Quick Motors',
        'Rapid Auto Group',
        'Swift Motors',
        'Flash Automotive',
        'Lightning Auto',
        'Thunder Motors',
        'Storm Auto Group',
        'Tornado Motors',
        'Hurricane Auto',
        'Cyclone Motors',
        'Tempest Auto Group',
        'Blizzard Motors',
        'Avalanche Auto',
        'Glacier Motors',
        'Frost Auto Group',
        'Ice Motors',
        'Snow Peak Auto',
        'Winter Motors',
        'Arctic Auto Group',
        'Polar Motors',
        'Tundra Automotive',
        'Alpine Auto Sales',
        'Peak Motors',
        'Summit Auto Group',
        'Ridge Motors',
        'Valley Automotive',
        'Canyon Auto Sales',
        'Mesa Motors',
        'Desert Auto Group',
        'Oasis Motors',
        'Mirage Automotive',
        'Dune Auto Sales',
        'Sand Motors',
        'Cactus Auto Group',
        'Sage Motors',
        'Prairie Automotive',
        'Plains Auto Sales',
        'Meadow Motors',
        'Field Auto Group',
        'Grove Motors',
        'Forest Automotive',
        'Woods Auto Sales',
        'Pine Motors',
        'Oak Auto Group',
        'Maple Motors',
        'Birch Automotive',
        'Cedar Auto Sales',
        'Elm Motors',
        'Willow Auto Group',
        'Aspen Motors',
        'Redwood Automotive',
        'Sequoia Auto Sales',
        'Cypress Motors',
        'Palm Auto Group',
        'Bay Motors',
        'Harbor Automotive',
        'Port Auto Sales',
        'Marina Motors',
        'Dock Auto Group',
        'Pier Motors',
        'Wharf Automotive',
        'Anchor Auto Sales',
        'Sail Motors',
        'Wave Auto Group',
        'Tide Motors',
        'Current Automotive',
        'Stream Auto Sales',
        'River Motors',
        'Creek Auto Group',
        'Brook Motors',
        'Spring Automotive',
        'Well Auto Sales',
        'Source Motors',
        'Origin Auto Group',
        'Genesis Motors',
        'Alpha Automotive',
        'Beta Auto Sales',
        'Gamma Motors',
        'Delta Auto Group',
        'Epsilon Motors',
        'Zeta Automotive',
        'Eta Auto Sales',
        'Theta Motors',
        'Iota Auto Group',
        'Kappa Motors',
        'Lambda Automotive',
        'Mu Auto Sales',
        'Nu Motors',
        'Xi Auto Group',
        'Omicron Motors',
        'Pi Automotive',
        'Rho Auto Sales',
        'Sigma Motors',
        'Tau Auto Group',
        'Upsilon Motors',
        'Phi Automotive',
        'Chi Auto Sales',
        'Psi Motors',
        'Omega Auto Group',
      ];
      firstName = businessNames[Math.floor(Math.random() * businessNames.length)];
      const locations = ['Inc', 'LLC', 'Group', 'Partners', 'Associates', 'International', 'USA', 'Holdings'];
      lastName = Math.random() > 0.5 ? locations[Math.floor(Math.random() * locations.length)] : '';
    } else {
      const firstNames = [
        'John',
        'Jane',
        'Michael',
        'Sarah',
        'Robert',
        'Emily',
        'David',
        'Lisa',
        'James',
        'Jennifer',
        'William',
        'Elizabeth',
        'Richard',
        'Susan',
        'Joseph',
        'Jessica',
        'Thomas',
        'Karen',
        'Charles',
        'Nancy',
        'Christopher',
        'Margaret',
        'Daniel',
        'Barbara',
        'Matthew',
        'Betty',
        'Anthony',
        'Sandra',
        'Mark',
        'Ashley',
        'Donald',
        'Dorothy',
        'Steven',
        'Kimberly',
        'Andrew',
        'Amanda',
        'Paul',
        'Melissa',
        'Kevin',
        'Michelle',
        'Brian',
        'Stephanie',
        'George',
        'Nicole',
        'Edward',
        'Angela',
        'Ronald',
        'Helen',
        'Timothy',
        'Samantha',
        'Jason',
        'Deborah',
        'Jeffrey',
        'Rachel',
        'Ryan',
        'Carolyn',
        'Jacob',
        'Janet',
        'Gary',
        'Catherine',
        'Nicholas',
        'Maria',
        'Eric',
        'Heather',
        'Jonathan',
        'Diane',
        'Stephen',
        'Ruth',
        'Larry',
        'Julie',
        'Justin',
        'Joyce',
        'Scott',
        'Virginia',
        'Brandon',
        'Victoria',
        'Benjamin',
        'Kelly',
        'Samuel',
        'Christina',
        'Gregory',
        'Joan',
        'Frank',
        'Evelyn',
        'Raymond',
        'Lauren',
        'Alexander',
        'Judith',
        'Patrick',
        'Megan',
        'Jack',
        'Cheryl',
        'Dennis',
        'Andrea',
        'Jerry',
        'Hannah',
        'Tyler',
        'Jacqueline',
        'Aaron',
        'Martha',
        'Jose',
        'Gloria',
        'Henry',
        'Teresa',
        'Adam',
        'Sara',
        'Douglas',
        'Janice',
        'Nathan',
        'Marie',
        'Peter',
        'Julia',
        'Zachary',
        'Kathryn',
        'Kyle',
        'Frances',
        'Noah',
        'Alexis',
        'Alan',
        'Brenda',
        'Carl',
        'Emma',
        'Wayne',
        'Olivia',
        'Arthur',
        'Sophia',
        'Harold',
        'Catherine',
        'Jordan',
        'Natalie',
        'Ralph',
        'Danielle',
        'Bobby',
        'Abigail',
        'Russell',
        'Julie',
        'Louis',
        'Joyce',
      ];
      const lastNames = [
        'Smith',
        'Johnson',
        'Williams',
        'Brown',
        'Jones',
        'Miller',
        'Davis',
        'Garcia',
        'Rodriguez',
        'Wilson',
        'Martinez',
        'Anderson',
        'Taylor',
        'Thomas',
        'Hernandez',
        'Moore',
        'Martin',
        'Jackson',
        'Thompson',
        'White',
        'Lopez',
        'Lee',
        'Gonzalez',
        'Harris',
        'Clark',
        'Lewis',
        'Robinson',
        'Walker',
        'Perez',
        'Hall',
        'Young',
        'Allen',
        'Sanchez',
        'Wright',
        'King',
        'Scott',
        'Green',
        'Baker',
        'Adams',
      ];
      firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
      lastName = lastNames[Math.floor(Math.random() * lastNames.length)];

      // Add middle name for some individual customers (30% chance)
      if (Math.random() < 0.3) {
        const middleNames = [
          'James',
          'John',
          'Robert',
          'Michael',
          'William',
          'David',
          'Richard',
          'Joseph',
          'Thomas',
          'Christopher',
          'Charles',
          'Daniel',
          'Matthew',
          'Anthony',
          'Mark',
          'Donald',
          'Steven',
          'Paul',
          'Andrew',
          'Joshua',
          'Kenneth',
          'Kevin',
          'Brian',
          'George',
          'Timothy',
          'Ronald',
          'Jason',
          'Edward',
          'Jeffrey',
          'Ryan',
          'Jacob',
          'Gary',
          'Nicholas',
          'Eric',
          'Jonathan',
          'Stephen',
          'Larry',
          'Justin',
          'Scott',
          'Brandon',
          'Benjamin',
          'Samuel',
          'Gregory',
          'Alexander',
          'Patrick',
          'Frank',
          'Raymond',
          'Jack',
          'Dennis',
          'Jerry',
          'Tyler',
          'Aaron',
          'Jose',
          'Henry',
          'Adam',
          'Douglas',
          'Nathan',
          'Peter',
          'Zachary',
          'Kyle',
          'Marie',
          'Ann',
          'Lynn',
          'Rose',
          'Grace',
          'Jane',
          'Elizabeth',
          'Nicole',
          'Michelle',
          'Christine',
          'Catherine',
          'Frances',
          'Samantha',
          'Deborah',
          'Rachel',
          'Carolyn',
          'Janet',
          'Virginia',
          'Maria',
          'Heather',
          'Diane',
          'Ruth',
          'Julie',
          'Joyce',
          'Victoria',
          'Kelly',
          'Christina',
          'Joan',
          'Evelyn',
          'Lauren',
          'Judith',
          'Megan',
          'Cheryl',
          'Andrea',
          'Hannah',
          'Jacqueline',
          'Martha',
          'Gloria',
          'Teresa',
          'Sara',
        ];
        middleName = middleNames[Math.floor(Math.random() * middleNames.length)];
      }
    }

    // Ensure most customers have emails (90% chance)
    const hasEmail = Math.random() > 0.1; // 10% chance of no email

    // Create a good mix of statuses for testing with more active customers
    // 85% active, 10% inactive, 5% on hold
    const [active, inactive, onHold] = statuses;
    let status;
    const statusRandom = Math.random();
    if (statusRandom < 0.85) {
      status = active;
    } else if (statusRandom < 0.95) {
      status = inactive;
    } else {
      status = onHold;
    }

    // Generate a unique customer ID with a letter prefix and sequential digits
    const customerIdPrefix = ['C', 'T', 'A', 'B', 'D', 'E'][i % 6]; // Cycle through prefixes
    const customerIdNumber = 10000 + i; // Sequential to ensure uniqueness
    const customerId = `${customerIdPrefix}${customerIdNumber}`;

    // Generate a customer number that's different from the ID
    const customerNumber = `${100000 + i}`;

    // Create email with proper formatting and realistic duplicate scenarios
    let email = '';
    if (hasEmail) {
      // Create realistic duplicate email scenarios for testing
      if (i < 50) {
        // First 50 customers: Create specific duplicate scenarios
        const duplicateScenarios = [
          // Scenario 1: <EMAIL> (3 customers)
          { email: '<EMAIL>', indices: [5, 15, 25] },
          // Scenario 2: <EMAIL> (4 customers)
          { email: '<EMAIL>', indices: [8, 18, 28, 38] },
          // Scenario 3: <EMAIL> (2 customers)
          { email: '<EMAIL>', indices: [12, 32] },
          // Scenario 4: <EMAIL> (5 customers - warning scenario)
          { email: '<EMAIL>', indices: [3, 13, 23, 33, 43] },
          // Scenario 5: <EMAIL> (2 customers)
          { email: '<EMAIL>', indices: [7, 27] },
          // Scenario 6: <EMAIL> (3 customers)
          { email: '<EMAIL>', indices: [10, 20, 40] },
          // Scenario 7: <EMAIL> (2 customers)
          { email: '<EMAIL>', indices: [16, 36] },
          // Scenario 8: <EMAIL> (6 customers - high warning scenario)
          { email: '<EMAIL>', indices: [2, 9, 19, 29, 39, 49] },
        ];

        // Check if current index matches any duplicate scenario
        const matchingScenario = duplicateScenarios.find(scenario => scenario.indices.includes(i));

        if (matchingScenario) {
          email = matchingScenario.email;
        } else {
          email = generateUniqueEmail(isBusinessCustomer, firstName, lastName, i);
        }
      } else {
        email = generateUniqueEmail(isBusinessCustomer, firstName, lastName, i);
      }
    }

    // Generate mock dealership data
    const dealershipNames = [
      'Downtown Toyota',
      'City Honda',
      'Metro Ford',
      'Valley Chevrolet',
      'Riverside BMW',
      'Sunset Mercedes',
      'Mountain View Audi',
      'Lakeside Nissan',
      'Hillside Hyundai',
      'Oceanview Lexus',
    ];

    const dealershipCount = Math.floor(Math.random() * 3) + 1; // 1-3 dealerships
    const dealerships = [...EMPTY_ARRAY];
    for (let j = 0; j < dealershipCount; j += 1) {
      const randomDealership = dealershipNames[Math.floor(Math.random() * dealershipNames.length)];
      if (!dealerships.includes(randomDealership)) {
        dealerships.push(randomDealership);
      }
    }

    // Generate phone number data in the new structure
    const phoneNumber = Math.floor(1000000000 + Math.random() * 9000000000).toString();
    const countryCodes = ['+1', '+91', '+44', '+61', '+33'];
    const countryCode = countryCodes[Math.floor(Math.random() * countryCodes.length)];
    const extension = Math.random() > 0.7 ? Math.floor(100 + Math.random() * 900).toString() : '';

    const primaryPhone = {
      number: phoneNumber,
      countryCode,
      extension,
      ndc: Math.random() > 0.8, // 20% chance of NDC
    };

    // Create customer object with direct property names
    customers.push({
      customerId,
      firstName,
      middleName,
      lastName,
      customerNumber,
      email,
      mobileNumber: phoneNumber, // Keep for backward compatibility
      primaryPhone,
      lastActivity: Date.now() - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000),
      customerType,
      isCharge: Math.random() > 0.5,
      status,
      dealerships,
    });
  }

  return customers;
};

export default mockSearchCustomers;
