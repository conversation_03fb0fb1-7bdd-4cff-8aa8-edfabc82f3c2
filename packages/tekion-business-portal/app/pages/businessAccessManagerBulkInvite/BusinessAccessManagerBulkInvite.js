/* eslint-disable import/order */
import React, { memo, useEffect } from 'react';
import PropTypes from 'prop-types';
import compose from 'recompose/compose';

// Lodash
import _noop from 'lodash/noop';

// Components
import Page from 'tcomponents/molecules/pageComponent';
import Heading from 'tcomponents/atoms/Heading';
import SaveComponent from 'tcomponents/molecules/SaveComponent';
import OverlayLoader from 'twidgets/organisms/OverlayLoader';
import CustomerSelection from './organisms/CustomerSelection';
import SelectedCustomersTable from './organisms/SelectedCustomersTable';
import DuplicateEmailModal from './organisms/DuplicateEmailModal';
import EmailPreviewModal from './organisms/EmailPreviewModal';

// HOCs
import withSize from 'tcomponents/hoc/withSize';
import withActions from 'tcomponents/connectors/withActions';

// Constants
import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import { KEYBOARD_CONSTANT_KEYS } from 'tbase/constants/keyboard.constants';
import { ACTION_TYPES } from './constants/businessAccessManagerBulkInvite.actionTypes';
import { INITIAL_STATE } from './constants/businessAccessManagerBulkInvite.initialState';
import { MAX_SELECTION_LIMIT } from './constants/businessAccessManagerBulkInvite.general';

// Hooks
import useActions from './hooks/useActions';
import useLayoutProps from './hooks/useLayoutProps';

// Builders
import CustomerBuilder from './builders/CustomerBuilder';

// Action Handlers
import { ACTION_HANDLERS } from './businessAccessManagerBulkInvite.actionHandlers';

// Styles
import styles from './businessAccessManagerBulkInvite.module.scss';

const BusinessAccessManagerBulkInvite = props => {
  const {
    onAction,
    contentHeight,
    customers,
    selectedCustomerIds,
    invitedCustomers,
    searchText,
    filters,
    loading,
    nextPageKey,
    isFetchingNextPage,
    showDuplicateCheckingOverlay,
    showDuplicateModal,
    duplicateGroups,
    showEmailPreviewModal,
    emailTemplateData,
  } = props;

  const actions = useActions(onAction);
  const { footerProps, headerProps } = useLayoutProps(actions, invitedCustomers);

  useEffect(() => {
    onAction({ type: ACTION_TYPES.INIT });
  }, [onAction]);

  return (
    <OverlayLoader isLoading={showDuplicateCheckingOverlay} className={styles.customOverlay}>
      <Page className="full-height">
        <Page.Header hasBack goBackHandler={headerProps.goBackHandler}>
          <div className={styles.headerContent}>
            <Heading size={1}>{__('Customer Bulk Invite')}</Heading>
            <span
              className={styles.emailPreview}
              onClick={headerProps.onEmailPreviewClick}
              role="button"
              tabIndex={0}
              onKeyDown={e => e.key === KEYBOARD_CONSTANT_KEYS.ENTER_KEY && headerProps.onEmailPreviewClick()}>
              {__('Email Preview')}
            </span>
          </div>
        </Page.Header>

        <Page.Body style={{ height: contentHeight }} className={`${styles.content} p-0`}>
          <div className={styles.leftSection}>
            <CustomerSelection
              customers={customers}
              selectedCustomerIds={selectedCustomerIds}
              onSelectCustomers={actions.handleCustomerSelect}
              onSearchChange={actions.handleSearchChange}
              onFilterApply={actions.handleFilterChange}
              selectedFilters={filters}
              onNavigateToProfile={actions.handleNavigateToProfile}
              onLoadMore={actions.handleLoadMoreCustomers}
              onAddCustomers={actions.handleAddCustomers}
              searchText={searchText}
              loading={loading}
              nextPageKey={nextPageKey}
              isFetchingNextPage={isFetchingNextPage}
              maxSelectonLimit={MAX_SELECTION_LIMIT}
            />
          </div>
          <div className={styles.rightSection}>
            <SelectedCustomersTable
              invitedCustomers={invitedCustomers}
              onRemoveCustomer={actions.handleRemoveSelectedCustomer}
              onRemoveAll={actions.handleRemoveAllSelectedCustomers}
            />
          </div>
        </Page.Body>

        <Page.Footer>
          <SaveComponent {...footerProps} />
        </Page.Footer>

        {showDuplicateModal && (
          <DuplicateEmailModal
            showDuplicateModal={showDuplicateModal}
            onDuplicateModalCancel={actions.handleDuplicateModalCancel}
            duplicateGroups={duplicateGroups}
            onSendInvite={actions.handleSendInviteWithDuplicates}
            maxSelectionLimit={MAX_SELECTION_LIMIT}
            invitedCustomers={invitedCustomers}
          />
        )}

        {showEmailPreviewModal && (
          <EmailPreviewModal
            isVisible={showEmailPreviewModal}
            onCancel={actions.handleEmailPreviewClose}
            emailTemplateData={emailTemplateData}
          />
        )}
      </Page>
    </OverlayLoader>
  );
};

BusinessAccessManagerBulkInvite.propTypes = {
  onAction: PropTypes.func,
  contentHeight: PropTypes.number.isRequired,
  customers: PropTypes.arrayOf(PropTypes.instanceOf(CustomerBuilder)),
  selectedCustomerIds: PropTypes.array,
  invitedCustomers: PropTypes.arrayOf(PropTypes.instanceOf(CustomerBuilder)),
  searchText: PropTypes.string,
  filters: PropTypes.array,
  loading: PropTypes.bool,
  nextPageKey: PropTypes.string,
  isFetchingNextPage: PropTypes.bool,
  showDuplicateCheckingOverlay: PropTypes.bool,
  showDuplicateModal: PropTypes.bool,
  duplicateGroups: PropTypes.array,
  showEmailPreviewModal: PropTypes.bool,
  emailTemplateData: PropTypes.object,
};

BusinessAccessManagerBulkInvite.defaultProps = {
  onAction: _noop,
  customers: EMPTY_ARRAY,
  selectedCustomerIds: EMPTY_ARRAY,
  invitedCustomers: EMPTY_ARRAY,
  searchText: '',
  filters: EMPTY_ARRAY,
  loading: false,
  nextPageKey: null,
  isFetchingNextPage: false,
  showDuplicateCheckingOverlay: false,
  showDuplicateModal: false,
  duplicateGroups: EMPTY_ARRAY,
  showEmailPreviewModal: false,
  emailTemplateData: EMPTY_OBJECT,
};

export default compose(
  withActions(INITIAL_STATE, ACTION_HANDLERS),
  withSize({ hasPageFooter: 1, hasPageHeader: 1 }),
  memo
)(BusinessAccessManagerBulkInvite);
