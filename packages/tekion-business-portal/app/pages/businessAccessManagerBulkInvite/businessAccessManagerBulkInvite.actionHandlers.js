/* eslint-disable no-promise-executor-return */
/* eslint-disable import/order */
// Lodash
import _debounce from 'lodash/debounce';
import _map from 'lodash/map';
import _concat from 'lodash/concat';
import _filter from 'lodash/filter';
import _includes from 'lodash/includes';
import _size from 'lodash/size';
import _union from 'lodash/union';

// Utils
import toastAPIError from 'tbusiness/utils/toastAPIError';
import toastErrorMessage from 'twidgets/utils/toastErrorMessage';
import openWindowInNewTab from 'tbase/utils/openWindowInNewTab';
import { toaster, TOASTER_TYPE } from 'tcomponents/organisms/NotificationWrapper';

// Helpers
import { getCustomerDetailedViewRoute } from 'tbusiness/helpers/customerManagement.routes.helpers';
import {
  getProcessedPaginationResponse,
  partitionCustomersByDuplicateStatus,
  processDuplicateDetectionResponse,
  getEmailTemplateDataFromResponse,
} from './helpers/businessAccessManagerBulkInvite.general';
import { createDuplicateDetectionPayload } from './helpers/businessAccessManagerBulkInvite.request';

// Constants
import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import { ACTION_TYPES } from './constants/businessAccessManagerBulkInvite.actionTypes';
import {
  SEARCH_DEBOUNCE_DELAY,
  REMOVAL_DEBOUNCE_DELAY,
  MAX_SELECTION_LIMIT,
} from './constants/businessAccessManagerBulkInvite.general';

// Services
import mockSearchCustomers from './services/customerService';
import { fetchDuplicateCustomersByEmail } from './services/duplicateDetectionService';
import { fetchEmailTemplate } from './services/emailTemplateService';

const navigateToListView = navigate => {
  if (navigate) {
    navigate('/businessPortal/businessAccessManager/list');
  }
};

const handleEmailPreviewOpen = ({ setState }) => {
  setState({ showEmailPreviewModal: true });
};

const handleEmailPreviewClose = ({ setState }) => {
  setState({ showEmailPreviewModal: false });
};

const handleCancel = ({ getState }) => {
  const { navigate } = getState();
  navigateToListView(navigate);
};

const handleSelectCustomers = ({ setState, params }) => {
  const { customerIdsToSelect } = params;

  setState({
    selectedCustomerIds: customerIdsToSelect,
  });
};

const debouncedFetchCustomers = _debounce(async ({ setState, getState }) => {
  try {
    await handleFetchCustomers({ setState, getState });
  } finally {
    setState({ loading: false });
  }
}, SEARCH_DEBOUNCE_DELAY);

const debouncedFetchCustomersAfterRemoval = _debounce(async ({ setState, getState }) => {
  try {
    await new Promise(resolve => setTimeout(resolve, 300));
    await handleFetchCustomers({ setState, getState });
  } finally {
    setState({ loading: false });
  }
}, REMOVAL_DEBOUNCE_DELAY);

const handleSearchChange = ({ setState, params, getState }) => {
  const { value } = params;

  debouncedFetchCustomers.cancel();

  setState({
    searchText: value,
    nextPageKey: null,
    loading: true,
    isFetchingNextPage: false,
    customers: EMPTY_ARRAY,
    selectedCustomerIds: EMPTY_ARRAY,
  });

  debouncedFetchCustomers({ setState, getState });
};

const handleFilterChange = async ({ setState, params, getState }) => {
  const { filters } = params;

  setState({
    filters,
    nextPageKey: null,
    loading: true,
    isFetchingNextPage: false,
    customers: EMPTY_ARRAY,
    selectedCustomerIds: EMPTY_ARRAY,
  });

  try {
    await new Promise(resolve => setTimeout(resolve, 300));

    await handleFetchCustomers({ setState, getState });
  } finally {
    setState({ loading: false });
  }
};

const handleNavigateToProfile = ({ params }) => {
  const { customer } = params;
  const customerId = customer ? customer.getCustomerId() : null;
  if (!customerId) {
    return;
  }

  const customerDetailedViewRoute = getCustomerDetailedViewRoute({
    entityId: customerId,
    isExternal: true,
  });
  openWindowInNewTab(customerDetailedViewRoute);
};

const handleFetchCustomers = async ({ setState, getState, clearData = true }) => {
  const { searchText, filters, nextPageKey = null, invitedCustomers = EMPTY_ARRAY } = getState();

  try {
    const TekSearchRequest = {
      searchText,
      filters,
    };

    const excludeCustomerIds = _map(invitedCustomers, customer => customer.getCustomerId());

    const pageSize = 500;
    const response = await mockSearchCustomers({
      TekSearchRequest,
      pageSize,
      lastCustomerIdFromPreviousBatch: clearData ? null : nextPageKey,
      excludeCustomerIds,
    });

    const newCustomers = response.data || EMPTY_ARRAY;

    const { customers: existingCustomers } = getState();
    const paginationResult = getProcessedPaginationResponse({
      newCustomers,
      existingCustomers,
      clearData,
    });

    setState(paginationResult);
  } catch (error) {
    toastAPIError(error, __('Failed to fetch customers'));
  }
};

const handleLoadMoreCustomers = async ({ setState, getState }) => {
  const { nextPageKey, isFetchingNextPage } = getState();

  if (!nextPageKey || isFetchingNextPage) {
    return;
  }

  setState({ isFetchingNextPage: true });

  try {
    await new Promise(resolve => setTimeout(resolve, 300));

    await handleFetchCustomers({ setState, getState, clearData: false });
  } finally {
    setState({ isFetchingNextPage: false });
  }
};

const handleAddCustomers = async ({ setState, getState }) => {
  const { selectedCustomerIds, invitedCustomers, customers } = getState();

  const totalCustomersForInvitation = _size(selectedCustomerIds) + _size(invitedCustomers);
  if (totalCustomersForInvitation > MAX_SELECTION_LIMIT) {
    toastErrorMessage(
      null,
      __('Cannot add more customers. The total selection exceeds the maximum limit of {{max}} customers', {
        max: MAX_SELECTION_LIMIT,
      })
    );
    return;
  }

  setState({ loading: true });

  const selectedCustomers = _filter(customers, customer => _includes(selectedCustomerIds, customer.getCustomerId()));
  const updatedInvitedCustomers = _concat(invitedCustomers, selectedCustomers);

  setState({
    invitedCustomers: updatedInvitedCustomers,
    selectedCustomerIds: EMPTY_ARRAY,
    duplicateGroups: EMPTY_ARRAY,
  });

  try {
    await new Promise(resolve => setTimeout(resolve, 300));

    await handleFetchCustomers({ setState, getState });
  } finally {
    setState({ loading: false });
  }
};

const handleRemoveSelectedCustomer = ({ setState, getState, params }) => {
  const { customer } = params;

  const { invitedCustomers } = getState();
  const updatedInvitedCustomers = _filter(
    invitedCustomers,
    invitedCustomer => invitedCustomer.getCustomerId() !== customer.getCustomerId()
  );

  setState({
    invitedCustomers: updatedInvitedCustomers,
    selectedCustomerIds: EMPTY_ARRAY,
    nextPageKey: null,
    loading: true,
  });

  debouncedFetchCustomersAfterRemoval({ setState, getState });
};

const handleRemoveAllSelectedCustomers = async ({ setState, getState }) => {
  debouncedFetchCustomersAfterRemoval.cancel();

  setState({
    loading: true,
    invitedCustomers: EMPTY_ARRAY,
    selectedCustomerIds: EMPTY_ARRAY,
    nextPageKey: null,
  });

  try {
    await new Promise(resolve => setTimeout(resolve, 300));
    await handleFetchCustomers({ setState, getState });
  } finally {
    setState({ loading: false });
  }
};

const handleSendInvite = async ({ setState, getState }) => {
  const { invitedCustomers } = getState();

  setState({
    showDuplicateCheckingOverlay: true,
  });

  try {
    const payload = createDuplicateDetectionPayload(invitedCustomers);
    const response = await fetchDuplicateCustomersByEmail(payload, invitedCustomers);
    const duplicateGroups = processDuplicateDetectionResponse(response);

    if (duplicateGroups.length === 0) {
      setState({
        duplicateGroups: EMPTY_ARRAY,
      });
      const customerIdsCount = _size(_map(invitedCustomers, customer => customer.getCustomerId()));
      toaster(
        TOASTER_TYPE.SUCCESS,
        __('Successfully sent invitations to {{count}} customers', { count: customerIdsCount })
      );
      const { navigate } = getState();
      navigateToListView(navigate);
    } else {
      setState({
        duplicateGroups,
        showDuplicateModal: true,
      });
    }
  } catch (error) {
    toastAPIError(error, __('Failed to check for duplicate customers'));
  } finally {
    setState({
      showDuplicateCheckingOverlay: false,
    });
  }
};

const handleDuplicateModalCancel = ({ setState }) => {
  setState({
    showDuplicateModal: false,
    duplicateGroups: EMPTY_ARRAY,
  });
};

const handleSendInviteWithDuplicates = ({ setState, getState, params = EMPTY_OBJECT }) => {
  const { selectedCustomerIds: selectedDuplicateCustomerIds = EMPTY_ARRAY } = params;
  const { navigate, invitedCustomers, duplicateGroups } = getState();

  const { readyToReceiveCustomers } = partitionCustomersByDuplicateStatus(invitedCustomers, duplicateGroups);
  const readyToReceiveCustomerIds = _map(readyToReceiveCustomers, customer => customer.getCustomerId());
  const allSelectedCustomerIds = _union(readyToReceiveCustomerIds, selectedDuplicateCustomerIds);

  setState({
    showDuplicateModal: false,
    duplicateGroups: EMPTY_ARRAY,
  });

  const totalSelectedCustomers = _size(allSelectedCustomerIds);
  toaster(
    TOASTER_TYPE.SUCCESS,
    __('Successfully sent invitations to {{count}} customers', { count: totalSelectedCustomers })
  );

  navigateToListView(navigate);
};

const fetchEmailTemplateData = async ({ setState }) => {
  try {
    const response = await fetchEmailTemplate('Onboard', 'en_US');
    const templateData = getEmailTemplateDataFromResponse(response);
    setState({ emailTemplateData: templateData });
  } catch (error) {
    toastAPIError('Failed to fetch email template:', error);
    setState({ emailTemplateData: EMPTY_OBJECT });
  }
};

const handleInit = async ({ setState, getState }) => {
  setState({ loading: true });

  try {
    await Promise.allSettled([handleFetchCustomers({ setState, getState }), fetchEmailTemplateData({ setState })]);
  } finally {
    setState({ loading: false });
  }
};

export const ACTION_HANDLERS = {
  [ACTION_TYPES.EMAIL_PREVIEW_OPEN]: handleEmailPreviewOpen,
  [ACTION_TYPES.EMAIL_PREVIEW_CLOSE]: handleEmailPreviewClose,
  [ACTION_TYPES.SEND_INVITE]: handleSendInvite,
  [ACTION_TYPES.CANCEL]: handleCancel,
  [ACTION_TYPES.SELECT_CUSTOMERS]: handleSelectCustomers,
  [ACTION_TYPES.SEARCH_CHANGE]: handleSearchChange,
  [ACTION_TYPES.FILTER_CHANGE]: handleFilterChange,
  [ACTION_TYPES.NAVIGATE_TO_PROFILE]: handleNavigateToProfile,
  [ACTION_TYPES.LOAD_MORE_CUSTOMERS]: handleLoadMoreCustomers,
  [ACTION_TYPES.ADD_CUSTOMERS]: handleAddCustomers,
  [ACTION_TYPES.REMOVE_SELECTED_CUSTOMER]: handleRemoveSelectedCustomer,
  [ACTION_TYPES.REMOVE_ALL_SELECTED_CUSTOMERS]: handleRemoveAllSelectedCustomers,
  [ACTION_TYPES.INIT]: handleInit,
  [ACTION_TYPES.DUPLICATE_MODAL_CANCEL]: handleDuplicateModalCancel,
  [ACTION_TYPES.SEND_INVITE_WITH_DUPLICATES]: handleSendInviteWithDuplicates,
};
