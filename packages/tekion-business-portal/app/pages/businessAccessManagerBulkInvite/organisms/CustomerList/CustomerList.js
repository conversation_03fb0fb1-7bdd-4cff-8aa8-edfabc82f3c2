import React, { memo, useCallback } from 'react';
import PropTypes from 'prop-types';

// Lodash
import _noop from 'lodash/noop';
import _size from 'lodash/size';

// Constants
import { EMPTY_ARRAY } from 'tbase/app.constants';

// Utils
import { getScaleAdjustedSize } from 'tcomponents/utils/general';

// Components
import Loader from 'tcomponents/molecules/loader';
import { List, AutoSizer } from 'react-virtualized';
import CustomerCard from './molecules/CustomerCard';

// Styles
import styles from './customerList.module.scss';

const LOAD_MORE_THRESHOLD = 200;
const OVERSCAN_ROW_COUNT = 10;
const ROW_HEIGHT = 74;

const CustomerList = ({
  customers,
  isCustomerSelected,
  onSelectCustomer,
  onNavigateToProfile,
  loading,
  nextPageKey,
  isFetchingMoreCustomers,
  fetchMore,
}) => {
  const handleScroll = useCallback(
    ({ scrollTop, scrollHeight, clientHeight }) => {
      if (!isFetchingMoreCustomers && nextPageKey) {
        // Load more when we're close to the bottom
        if (scrollHeight - scrollTop - clientHeight < LOAD_MORE_THRESHOLD) {
          fetchMore();
        }
      }
    },
    [isFetchingMoreCustomers, nextPageKey, fetchMore]
  );

  const renderCustomerCard = useCallback(
    ({ index, key, style }) => {
      const customer = customers[index];
      return (
        <div key={key} style={style} className={`${styles.customerCardWrapper} p-0 m-0`}>
          <CustomerCard
            customer={customer}
            isSelected={isCustomerSelected(customer)}
            onSelect={onSelectCustomer}
            onNavigateToProfile={onNavigateToProfile}
          />
        </div>
      );
    },
    [customers, isCustomerSelected, onSelectCustomer, onNavigateToProfile]
  );

  const renderNoResults = useCallback(
    () => <div className={`${styles.noResults} p-24`}>{__('No customers found')}</div>,
    []
  );

  return (
    <div className={styles.customerListContainer}>
      {loading && <Loader size="large" className={styles.overlayLoader} />}

      {!loading && customers.length === 0 && renderNoResults()}

      {!loading && customers.length > 0 && (
        <div className={`${styles.scrollContainer} p-0 m-0`}>
          <AutoSizer className={styles.autoSizer}>
            {({ width, height }) => (
              <List
                width={width}
                height={height}
                rowCount={_size(customers)}
                rowHeight={getScaleAdjustedSize(ROW_HEIGHT)}
                rowRenderer={renderCustomerCard}
                onScroll={handleScroll}
                overscanRowCount={OVERSCAN_ROW_COUNT}
                className="p-0 m-0"
              />
            )}
          </AutoSizer>
        </div>
      )}

      {isFetchingMoreCustomers && <Loader size="medium" className={styles.paginationLoader} />}
    </div>
  );
};

CustomerList.propTypes = {
  customers: PropTypes.array,
  isCustomerSelected: PropTypes.func,
  onSelectCustomer: PropTypes.func,
  onNavigateToProfile: PropTypes.func,
  loading: PropTypes.bool,
  nextPageKey: PropTypes.string,
  isFetchingMoreCustomers: PropTypes.bool,
  fetchMore: PropTypes.func,
};

CustomerList.defaultProps = {
  customers: EMPTY_ARRAY,
  isCustomerSelected: _noop,
  onSelectCustomer: _noop,
  onNavigateToProfile: _noop,
  loading: false,
  nextPageKey: null,
  isFetchingMoreCustomers: false,
  fetchMore: _noop,
};

export default memo(CustomerList);
