// Constants
import FILTER_TYPES from 'tcomponents/organisms/filterSection/constants/filterSection.filterTypes';
import { DEFAULT_FILTER_GROUP } from 'tcomponents/organisms/filterSection';
import { CUSTOMER_TYPE, CUSTOMER_TYPE_DISPLAY, CUSTOMER_STATUS_OPTIONS } from 'tbase/constants/customer.constants';

const FILTER_IDS = {
  CUSTOMER_TYPE: 'customerType',
  IS_CHARGE: 'isCharge',
  STATUS: 'status',
};

const CUSTOMER_TYPE_FILTER = {
  id: FILTER_IDS.CUSTOMER_TYPE,
  name: __('Customer Type'),
  type: FILTER_TYPES.SINGLE_SELECT,
  additional: {
    options: [
      {
        value: CUSTOMER_TYPE.INDIVIDUAL,
        label: CUSTOMER_TYPE_DISPLAY[CUSTOMER_TYPE.INDIVIDUAL],
      },
      {
        value: CUSTOMER_TYPE.BUSINESS,
        label: CUSTOMER_TYPE_DISPLAY[CUSTOMER_TYPE.BUSINESS],
      },
    ],
  },
};

const IS_CHARGE_FILTER = {
  id: FILTER_IDS.IS_CHARGE,
  name: __('Is Charge'),
  type: FILTER_TYPES.SINGLE_SELECT,
  additional: {
    options: [
      { label: __('Yes'), value: 'true' },
      { label: __('No'), value: 'false' },
    ],
  },
};

const STATUS_FILTER = {
  id: FILTER_IDS.STATUS,
  name: __('Status'),
  type: FILTER_TYPES.SINGLE_SELECT,
  additional: {
    options: CUSTOMER_STATUS_OPTIONS,
  },
};

export const FILTER_TYPES_LIST = [CUSTOMER_TYPE_FILTER, IS_CHARGE_FILTER, STATUS_FILTER];

export const DEFAULT_FILTER_TYPES = [FILTER_IDS.CUSTOMER_TYPE, FILTER_IDS.IS_CHARGE, FILTER_IDS.STATUS];

export const DEFAULT_FILTER_VALUES = [
  {
    type: FILTER_IDS.CUSTOMER_TYPE,
    operator: 'IN',
    values: [],
  },
  {
    type: FILTER_IDS.IS_CHARGE,
    operator: 'IN',
    values: [],
  },
  {
    type: FILTER_IDS.STATUS,
    operator: 'IN',
    values: [],
  },
];

export const FILTER_GROUPS = [
  {
    id: DEFAULT_FILTER_GROUP,
    name: __('Default'),
    filters: DEFAULT_FILTER_VALUES,
  },
];
