// Lodash
import _map from 'lodash/map';
import _filter from 'lodash/filter';
import _unionBy from 'lodash/unionBy';

// Constants
import { EMPTY_ARRAY } from 'tbase/app.constants';

const isCustomerEmailMatch = (email, customer) => customer.getEmail() === email;

const getCustomersForEmailGroup = (group, customersWithDuplicateEmails) => {
  const customersForThisEmail = _filter(customersWithDuplicateEmails, customer =>
    isCustomerEmailMatch(group.email, customer)
  );

  if (group.isRestricted) {
    return customersForThisEmail;
  }
  const customers = group.customers || EMPTY_ARRAY;
  return _unionBy(customers, customersForThisEmail, customer => customer.getCustomerId());
};

const transformGroupToTableRow = customersWithDuplicateEmails => group => {
  const customers = getCustomersForEmailGroup(group, customersWithDuplicateEmails);

  return {
    id: group.email,
    email: group.email,
    customers,
    docCount: group.customerCount,
    isRestricted: group.isRestricted || false,
  };
};

export const transformDuplicateGroupsToTableData = (duplicateGroups, customersWithDuplicateEmails) =>
  _map(duplicateGroups, transformGroupToTableRow(customersWithDuplicateEmails));

export const getRowId = row => row.original?.email;

export const getDuplicateModalInfoText = (readyToReceiveCount, duplicateCustomersCount) =>
  __(
    '{{readyCount}} customers are ready to receive the invite. {{duplicateCount}} have duplicate records with the same email ID. Review & Select the records to include.',
    {
      readyCount: readyToReceiveCount,
      duplicateCount: duplicateCustomersCount,
    }
  );

export const getSelectionLimitText = maxSelectionLimit =>
  __("You can invite up to {{limit}} customers. Inclusive of both 'Ready to Receive' & 'Selected Duplicates'.", {
    limit: maxSelectionLimit,
  });
