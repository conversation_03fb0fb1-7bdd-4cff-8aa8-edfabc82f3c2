// Lodash
import _map from 'lodash/map';
import _partition from 'lodash/partition';
import _includes from 'lodash/includes';

// Constants
import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';

const isCustomerEmailInDuplicateList = duplicateEmails => customer => {
  const customerEmail = customer.getEmail();
  return _includes(duplicateEmails, customerEmail);
};

const partitionCustomersByDuplicateStatus = (customers, duplicateGroups) => {
  const duplicateEmails = _map(duplicateGroups, group => group.email);
  const [customersWithDuplicateEmails, readyToReceiveCustomers] = _partition(
    customers,
    isCustomerEmailInDuplicateList(duplicateEmails)
  );

  return {
    customersWithDuplicateEmails,
    readyToReceiveCustomers,
  };
};

const makeInitialState = (props = EMPTY_OBJECT) => {
  const { duplicateGroups = EMPTY_ARRAY, invitedCustomers = EMPTY_ARRAY } = props;
  const { customersWithDuplicateEmails, readyToReceiveCustomers } = partitionCustomersByDuplicateStatus(
    invitedCustomers,
    duplicateGroups
  );
  const selectedCustomerIds = _map(customersWithDuplicateEmails, customer => customer.getCustomerId());
  return {
    selectedCustomerIds,
    readyToReceiveCustomers,
    customersWithDuplicateEmails,
  };
};

export default makeInitialState;
