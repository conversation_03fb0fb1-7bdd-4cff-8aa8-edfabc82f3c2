/* eslint-disable import/order */
import React, { useMemo, useCallback } from 'react';
import PropTypes from 'prop-types';

// Lodash
import _size from 'lodash/size';

// HOCs
import withActions from 'tcomponents/connectors/withActions';

// Constants
import { EMPTY_ARRAY } from 'tbase/app.constants';
import { TABLE_HEIGHT, MODAL_BODY_STYLE } from './constants/duplicateEmailModal.general';
import ACTION_TYPES from './constants/duplicateEmailModal.actionTypes';

// Components
import Modal from 'tcomponents/molecules/Modal';
import IconWithContent from 'tcomponents/molecules/IconWithContent';
import SubComponentTable from 'twidgets/appServices/accounting/organisms/table/subComponentTable';
import CustomerDetailsTable from './molecules/CustomerDetailsTable';
import DuplicateSectionHeader from './molecules/DuplicateSectionHeader';

// Helpers
import {
  transformDuplicateGroupsToTableData,
  getRowId,
  getDuplicateModalInfoText,
  getSelectionLimitText,
} from './helpers/duplicateEmailModal.general';
import createDuplicateEmailModalColumns from './helpers/duplicateEmailModal.columns';
import { getEmailGroupSelectionStatus } from './helpers/duplicateEmailModal.selection';
import makeInitialState from './helpers/duplicateEmailModal.initialState';

// Action Handlers
import ACTION_HANDLERS from './duplicateEmailModal.actionHandlers';

// Builders
import CustomerBuilder from '../../builders/CustomerBuilder';

// Styles
import styles from './duplicateEmailModal.module.scss';

const getSubComponent = (onCustomerSelectionChange, selectedCustomerIds) => row => {
  const emailGroup = row?.original;
  const { customers } = emailGroup;

  return (
    <CustomerDetailsTable
      customers={customers}
      selectedCustomerIds={selectedCustomerIds}
      onCustomerSelectionChange={onCustomerSelectionChange}
    />
  );
};

const DuplicateEmailModal = ({
  showDuplicateModal,
  onDuplicateModalCancel,
  duplicateGroups,
  onSendInvite,
  customersWithDuplicateEmails,
  readyToReceiveCustomers,
  selectedCustomerIds,
  onAction,
  maxSelectionLimit,
}) => {
  const duplicateSelectedCount = _size(selectedCustomerIds);
  const readyToReceiveCount = _size(readyToReceiveCustomers);
  const totalSelectedCount = readyToReceiveCount + duplicateSelectedCount;
  const isCustomerSelectionOverLimit = totalSelectedCount > maxSelectionLimit;
  const duplicateCustomersCount = _size(customersWithDuplicateEmails);

  const tableData = useMemo(
    () => transformDuplicateGroupsToTableData(duplicateGroups, customersWithDuplicateEmails),
    [duplicateGroups, customersWithDuplicateEmails]
  );

  const columns = useMemo(() => createDuplicateEmailModalColumns(selectedCustomerIds), [selectedCustomerIds]);

  const handleCustomerSelectionChange = useCallback(
    selectedIds => {
      onAction({
        type: ACTION_TYPES.CUSTOMER_SELECTION_CHANGE,
        payload: { selectedIds },
      });
    },
    [onAction]
  );

  const subComponent = useMemo(
    () => getSubComponent(handleCustomerSelectionChange, selectedCustomerIds),
    [selectedCustomerIds, handleCustomerSelectionChange]
  );

  const getSelectionStateForSubComponent = getEmailGroupSelectionStatus(selectedCustomerIds);

  const handleSendInvite = useCallback(() => {
    onSendInvite(selectedCustomerIds);
  }, [onSendInvite, selectedCustomerIds]);

  return (
    <Modal
      visible={showDuplicateModal}
      onCancel={onDuplicateModalCancel}
      onSubmit={handleSendInvite}
      width={Modal.SIZES.XXL}
      bodyStyle={MODAL_BODY_STYLE}
      title={__('Duplicates found')}
      submitBtnText={__('Send Invites')}
      primaryBtnDisabled={isCustomerSelectionOverLimit}
      maskClosable={false}>
      <IconWithContent
        icon="icon-information-filled"
        iconClassName={`${styles.infoBannerIcon} m-r-12`}
        contentClassName={styles.infoBannerText}
        bodyClassName={`${styles.customInfoBanner} p-y-12 p-x-16 m-b-24 d-flex align-items-center`}
        text={getDuplicateModalInfoText(readyToReceiveCount, duplicateCustomersCount)}
      />
      {isCustomerSelectionOverLimit && (
        <IconWithContent
          icon="icon-alert1"
          iconClassName={`${styles.alertIcon} m-r-12`}
          contentClassName={styles.overlimitMessageText}
          bodyClassName={`${styles.overlimitMessage} p-y-12 p-x-16 m-b-24 d-flex align-items-center`}
          text={getSelectionLimitText(maxSelectionLimit)}
        />
      )}
      <DuplicateSectionHeader totalSelectedCount={totalSelectedCount} />
      <SubComponentTable
        columns={columns}
        className="p-0"
        data={tableData}
        SubComponent={subComponent}
        getRowId={getRowId}
        onAction={onAction}
        getSelectionStateForSubComponent={getSelectionStateForSubComponent}
        isSelectionEnabled
        tableHeight={TABLE_HEIGHT}
        expandAllOnUpdate
        autoResetExpanded={false}
        isResizableTable
      />
    </Modal>
  );
};

DuplicateEmailModal.propTypes = {
  showDuplicateModal: PropTypes.bool.isRequired,
  onDuplicateModalCancel: PropTypes.func.isRequired,
  duplicateGroups: PropTypes.array,
  onSendInvite: PropTypes.func.isRequired,
  customersWithDuplicateEmails: PropTypes.arrayOf(PropTypes.instanceOf(CustomerBuilder)),
  readyToReceiveCustomers: PropTypes.arrayOf(PropTypes.instanceOf(CustomerBuilder)),
  selectedCustomerIds: PropTypes.array,
  onAction: PropTypes.func.isRequired,
  maxSelectionLimit: PropTypes.number,
};

DuplicateEmailModal.defaultProps = {
  duplicateGroups: EMPTY_ARRAY,
  customersWithDuplicateEmails: EMPTY_ARRAY,
  readyToReceiveCustomers: EMPTY_ARRAY,
  selectedCustomerIds: EMPTY_ARRAY,
  maxSelectionLimit: 0,
};

export default withActions(makeInitialState, ACTION_HANDLERS)(DuplicateEmailModal);
