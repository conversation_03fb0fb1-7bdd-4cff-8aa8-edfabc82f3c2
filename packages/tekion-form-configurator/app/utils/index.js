import _startCase from 'lodash/startCase';
import _lowerCase from 'lodash/lowerCase';
import _get from 'lodash/get';
import _join from 'lodash/join';
import _map from 'lodash/map';
import _size from 'lodash/size';
import _includes from 'lodash/includes';

import TEnvReader from 'tbase/readers/Env';
import { getTimeStamp, startOfDay, isMomentValid } from 'tbase/utils/dateUtils';
import { FORM_SOURCE } from '@tekion/tekion-base/constants/deal/formSetup';
import { NO_DATA } from '@tekion/tekion-base/app.constants';
import { FORMS } from '@tekion/tekion-base/constants/appServices';
import { DEALER_FORMS_TYPE, FORM_TYPES_FIELD_TYPE } from '../constants/constants';
import { FORM_TYPES } from '../constants/formConfigurator';
import {
  hasEditGlobalFieldsPermission,
  hasEditTekionRestrictedFieldsPermission,
  hasEditDPRestrictedFieldsPermission,
  hasViewOnlyFormConfigurator,
} from '../permissions/formConfigurator.permissions';

export const getArrayLength = a => (Array.isArray(a) ? a.length : 0);

export function getNumber(stringData) {
  const result = Number(stringData);
  return !result || isNaN(result) ? 0 : result; //eslint-disable-line
}

export const getStartOfDayTimeStamp = momentObj => {
  if (isMomentValid(momentObj)) return getTimeStamp(startOfDay(momentObj));
  return null;
};

export const snakeCaseToStartCase = str => str && _startCase(_lowerCase(str));

export const getResponse = response => _get(response, 'response');

export const getGlobalPusherChannelName = () => {
  const { tenantName, dealerId, id } = TEnvReader.userInfo();
  return `USER_${tenantName}_${dealerId}_${id}`;
};

export const getRandomId = () => (Date.now().toString(36) + Math.random().toString(36).substr(2, 5)).toUpperCase();

export function convertToLowerCase(string = '') {
  return string.toLowerCase();
}

export const getCommaSeparatedValues = values => (_size(values) ? _join(values, ', ') : NO_DATA);

export const formatOptions = options => _map(options, option => ({ label: option, value: option }));

export const getUpdatedFormsPath = path => `/${FORMS}/${path}`;

export const isTenantForm = form => _includes(_get(form, FORM_TYPES_FIELD_TYPE), DEALER_FORMS_TYPE);

export const getFormProgrammingType = form => {
  const formSource = _get(form, 'formSource');
  const formVersion = _get(form, 'version');

  const formCreatedInLibrary = formSource === FORM_SOURCE.FORM_LIBRARY;
  const formCreatedInDealership = formSource === FORM_SOURCE.FORM_PRO;

  if (formCreatedInLibrary && formVersion === 1 && !isTenantForm(form)) return FORM_TYPES.STANDARD;

  if (formCreatedInLibrary && formVersion > 1 && !isTenantForm(form)) return FORM_TYPES.CUSTOM;

  if (formCreatedInDealership || (formCreatedInLibrary && isTenantForm(form))) return FORM_TYPES.DEALERSHIP;

  return NO_DATA;
};

export const isFCEditFlow = () => hasEditGlobalFieldsPermission();

export const isFCLiteEditFlow = () =>
  hasEditTekionRestrictedFieldsPermission() || hasEditDPRestrictedFieldsPermission();

export const isFCLiteViewFlow = () => hasViewOnlyFormConfigurator();
