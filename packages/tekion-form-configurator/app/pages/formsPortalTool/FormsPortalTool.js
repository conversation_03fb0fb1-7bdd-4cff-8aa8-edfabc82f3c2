import React, { useContext } from 'react';

import { connect } from 'react-redux';
import _get from 'lodash/get';

import { EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import FormConfiguratorTool from 'twidgets/organisms/formConfiguratorTool';

import {
  hasEContractingEditPermission,
  hasViewOnlyFormConfigurator,
  hasEditGlobalFieldsPermission,
  hasAddVisibilityTargettingPermission,
  hasEditTekionRestrictedFieldsPermission,
  hasEditDPRestrictedFieldsPermission,
  hasPublishFormPermission,
  hasCreateFormPermission,
  hasSaveFormPermission,
} from 'permissions/formConfigurator.permissions';
import { EnterpriseContext } from 'providers/enterpriseProvider';

import { BASE_REDUCER_KEY } from '../../constants/formConfigurator';
import {
  getDueBills,
  getFniOptions,
  getVehicleTypes,
  getGlobalMetaData,
} from '../formConfigurator/FormConfigurator.selectors';
import {
  fetchDueBills,
  getFNIProducts,
  getViSettings,
  fetchSalesSetupInfo,
  fetchGlobalMetaData,
} from '../formConfigurator/FormConfigurator.actions';
import { CONFIGURATOR_PAGES } from '../../constants/formsConfiguratorTool';
import DealershipTableModal from '../../organisms/dealershipsTable';
import ACTION_TYPES from './FormsPortalTool.actionTypes';
import { getFCActionHandlers } from './FormsPortalTool.actionHandlers';
import { isFCEditFlow, isFCLiteEditFlow, isFCLiteViewFlow } from '../../utils';

const FormsPortal = props => {
  const { showFormsPortal, showEnterpriseView } = useContext(EnterpriseContext);
  const { onAction } = props;

  const showDealershipTableModal = _get(props, 'showDealershipTableModal', false);
  const formToPublishName = _get(props, 'formToPublishName', EMPTY_STRING);

  const handleOnSubmitDealershipSelect = selectedDealers => {
    onAction({
      type: ACTION_TYPES.PUBLISH_FORM_TO_SELECTED_DEALERSHIPS,
      payload: {
        selectedDealers,
      },
    });
  };

  const handleOnCancelDealershipSelect = () => {
    onAction({
      type: ACTION_TYPES.SET_SHOW_SELECT_DEALERSHIP_MODAL,
      payload: {
        showDealershipTableModal: false,
      },
    });
  };

  const fCLiteConfig = !isFCEditFlow()
    ? {
        hidePrinterAlignment: true,
        hidePageTypeInformation: true,
      }
    : EMPTY_OBJECT;

  return (
    <>
      <FormConfiguratorTool
        reducerKey={BASE_REDUCER_KEY}
        hasViewOnlyPermission={hasViewOnlyFormConfigurator()}
        hasEContractingEditPermission={hasEContractingEditPermission()}
        hasEditGlobalFieldsPermission={hasEditGlobalFieldsPermission()}
        hasAddVisibilityTargettingPermission={hasAddVisibilityTargettingPermission()}
        hasEditTekionRestrictedFieldsPermission={hasEditTekionRestrictedFieldsPermission()}
        hasEditDPRestrictedFieldsPermission={hasEditDPRestrictedFieldsPermission()}
        config={{ isLockingEnabled: true, disableGlobalEditAccess: true, ...fCLiteConfig }}
        isSaveAsNewDisabled={!hasPublishFormPermission() || !hasCreateFormPermission()}
        isSaveDisabled={!hasPublishFormPermission() && !hasSaveFormPermission()}
        pages={CONFIGURATOR_PAGES}
        customActionHandlers={getFCActionHandlers(onAction)}
        showFormsPortal={showFormsPortal}
        showEnterpriseView={showEnterpriseView}
        isFcLiteModeEnabled={!isFCEditFlow()}
        disableEditForFCLiteMode={!isFCLiteEditFlow()}
        isFCLiteViewOnly={isFCLiteViewFlow()}
        {...props}
      />
      <DealershipTableModal
        formName={formToPublishName}
        showModal={showDealershipTableModal}
        onCancel={handleOnCancelDealershipSelect}
        onSubmit={handleOnSubmitDealershipSelect}
      />
    </>
  );
};

const mapStateToProps = state => ({
  fniOptions: getFniOptions(state),
  dueBills: getDueBills(state),
  productsListFromProps: getFniOptions(state),
  vehicleTypes: getVehicleTypes(state),
  globalMetaData: getGlobalMetaData(state),
});

const mapDispatchToProps = {
  fetchDueBills,
  getFNIProducts,
  getViSettings,
  fetchSalesSetupInfo,
  fetchGlobalMetaData,
};

export default connect(mapStateToProps, mapDispatchToProps)(FormsPortal);
