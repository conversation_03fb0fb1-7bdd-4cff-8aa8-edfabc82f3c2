import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { compose } from 'recompose';
import { defaultMemoize } from 'reselect';
import _noop from 'lodash/noop';

import { EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import { URL_TYPES } from '@tekion/tekion-base/constants/api';
import { getPaymentOptionConfigs } from '@tekion/tekion-base/marketScan/readers/salesSetup.reader';
import { withPusher, makeEventKey } from '@tekion/tekion-base/services/pusherService';
import { toaster } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import withActionHandlers from '@tekion/tekion-components/connectors/withActionHandlers';
import withUsers from '@tekion/tekion-components/connectors/withUsers';
import withPropertyConsumer from '@tekion/tekion-components/src/organisms/propertyProvider/withPropertyConsumer';
import withSize from '@tekion/tekion-components/hoc/withSize';
import { withTekionConversion } from '@tekion/tekion-conversion-web';

import ADD_FORM_ACTION_HANDLERS from '@tekion/tekion-widgets/src/organisms/AddForm/AddForm.actionHandlers';
import { fetchStateListFromCountryCode } from '@tekion/tekion-widgets/src/organisms/formConfiguratorTool/actions';
import mediaUploaderHOCV3 from '@tekion/tekion-widgets/src/hocs/sales/mediaUploaderHOCV3';
import { getStateListOptions } from '@tekion/tekion-widgets/src/organisms/formConfiguratorTool/selectors/formConfiguratorTool.selectors';
import { EDIT_TYPE_PERMISSIONS } from '@tekion/tekion-widgets/src/organisms/formConfiguratorTool/FormConfiguratorTool.constants';
import TEnvReader from '@tekion/tekion-base/readers/Env';
import { Provider } from '@tekion/tekion-widgets/src/organisms/formConfiguratorTool/formConfiguratorTool.context';
import { getGlobalPusherChannelName, isFCEditFlow, isFCLiteEditFlow, isFCLiteViewFlow } from 'utils';
import {
  hasEditGlobalFieldsPermission,
  hasEditTekionRestrictedFieldsPermission,
  hasEditDPRestrictedFieldsPermission,
} from 'permissions/formConfigurator.permissions';
import { BASE_REDUCER_KEY } from 'constants/formConfigurator';
import { getVehicleMake, getVehicleModel, getAllVehicleMakesAndModels } from '../../commonActions/actions';
import FormsPortalDealership from './FormsPortalDealership';
import {
  fetchFormList,
  fetchPrinterTypes,
  fetchDocumentTypes,
  addNewForm,
  updateForm,
  testForm,
  getFNIProducts,
  fetchDueBills,
  fetchFuelTypes,
  fetchMetaData,
  fetchSalesSetupInfo,
  getViSettings,
  fetchDealerSites,
  getLegalProviders,
  fetchTrimList,
  fetchTenantList,
  fetchLabels,
  archiveForm,
  fetchArchivedFormsList,
  restoreArchivedForm,
  fetchGlobalMetaData,
} from './actions/FormsPortalDealership.actions';

import {
  getPrinterTypes,
  getDocTypes,
  getFniOptions,
  getDueBills,
  getMetaData,
  getFuelTypeSelector,
  getLenderTypeSelector,
  getDealTypes,
  getVehicleTypes,
  getFormCategories,
  getDealerTrackValue,
  getCustomStatus,
  getSiteOptions,
  getLicenseProviders,
  getTrimsList,
  getTekionVehicleMakes,
  getTenantList,
  getMakes,
  getModels,
  getFormLabels,
  getSalesSetupInfo,
  getDealStatus,
  getGlobalMetaData,
  getTradeInCount,
} from './selectors/FormsPortalDealership.selectors';

import ACTION_HANDLERS from './actionHandlers/FormsPortalDealership.actionHandlers';
import EnterpriseProvider from '../../providers/enterpriseProvider';

class FormsPortalDealershipContainer extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      previewUrls: {},
    };
  }
  componentDidMount() {
    const { changeMediaV3ModulePaths } = this.props;
    changeMediaV3ModulePaths(URL_TYPES.FORM_PLATFORM);
  }

  componentWillUnmount() {
    const { resetMediaV3ModulePaths } = this.props;
    resetMediaV3ModulePaths();
  }

  resetPreviewUrls = () => this.setState({ previewUrls: {} });

  handlePusherEvents = (event = {}) => {
    const { message, payload } = event;
    switch (message) {
      case 'PREVIEW_CREATED':
        {
          const { path, requestId } = payload;
          this.setState(({ previewUrls }) => ({
            previewUrls: { ...previewUrls, [requestId]: path },
          }));
        }

        break;

      default:
    }
  };

  getComponentWithPusher = defaultMemoize(pusherChannel => {
    const userInfo = TEnvReader.userInfo();
    const { tenantName, dealerId, id } = userInfo;
    const eventHandlers = {
      [makeEventKey(pusherChannel, 'message')]: this.handlePushNotification,
      [makeEventKey(`${tenantName}_${dealerId}_USER_${id}`, 'message')]: this.handlePusherEvents,
    };
    const component = withPusher(eventHandlers)(FormsPortalDealership);
    return component;
  });

  handlePushNotification = () => {
    toaster('success', __('Form test print request sent to printer successfully.'));
  };

  handleEditType = () => {
    if (hasEditGlobalFieldsPermission()) return EDIT_TYPE_PERMISSIONS.GLOBAL_EDIT;
    if (hasEditTekionRestrictedFieldsPermission()) return EDIT_TYPE_PERMISSIONS.TEKION_RESTRICTED_EDIT;
    if (hasEditDPRestrictedFieldsPermission()) return EDIT_TYPE_PERMISSIONS.DP_RESTRICTED_EDIT;
    return EDIT_TYPE_PERMISSIONS.NO_EDIT;
  };

  render() {
    const { previewUrls } = this.state;
    const pusherChannel = getGlobalPusherChannelName();
    const ComponentWithPusher = this.getComponentWithPusher(pusherChannel);

    return (
      <Provider value={{ previewUrls, resetPreviewUrls: this.resetPreviewUrls }}>
        <EnterpriseProvider>
          <ComponentWithPusher
            {...this.props}
            editType={this.handleEditType()}
            isFCEditFlow={isFCEditFlow()}
            isFCLiteEditFlow={isFCLiteEditFlow()}
            isFCLiteViewFlow={isFCLiteViewFlow()}
          />
        </EnterpriseProvider>
      </Provider>
    );
  }
}

FormsPortalDealershipContainer.propTypes = {
  printerList: PropTypes.array,
  changeMediaV3ModulePaths: PropTypes.func,
  resetMediaV3ModulePaths: PropTypes.func,
  labelsInfo: PropTypes.array,
};

FormsPortalDealershipContainer.defaultProps = {
  printerList: EMPTY_ARRAY,
  changeMediaV3ModulePaths: _noop,
  resetMediaV3ModulePaths: _noop,
  labelsInfo: EMPTY_ARRAY,
};

const mapStateToProps = state => ({
  printerTypes: getPrinterTypes(state),
  docTypes: getDocTypes(state),
  fniOptions: getFniOptions(state),
  dueBills: getDueBills(state),
  metaData: getMetaData(state),
  fuelTypes: getFuelTypeSelector(state),
  makes: getMakes(state),
  tekionMakes: getTekionVehicleMakes(state),
  models: getModels(state),
  lenderTypes: getLenderTypeSelector(state),
  dealTypeConfigs: getDealTypes(state),
  customStatus: getCustomStatus(state),
  vehicleTypes: getVehicleTypes(state),
  formCategories: getFormCategories(state),
  isDealerTrackEnabled: getDealerTrackValue(state),
  siteOptions: getSiteOptions(state),
  legalProviders: getLicenseProviders(state),
  trimsList: getTrimsList(state),
  tenantIds: getTenantList(state),
  labels: getFormLabels(state),
  stateListOptions: getStateListOptions(state, BASE_REDUCER_KEY),
  paymentOptionConfigs: getPaymentOptionConfigs(getSalesSetupInfo(state)),
  dealStatus: getDealStatus(state),
  globalMetaData: getGlobalMetaData(state),
  tradeInCount: getTradeInCount(state),
});

export default compose(
  connect(mapStateToProps, {
    fetchFormList,
    fetchArchivedFormsList,
    fetchPrinterTypes,
    fetchDocumentTypes,
    addNewForm,
    updateForm,
    testForm,
    archiveForm,
    restoreArchivedForm,
    getFNIProducts,
    fetchDueBills,
    fetchMetaData,
    fetchFuelTypes,
    getVehicleMake,
    getVehicleModel,
    getAllVehicleMakesAndModels,
    fetchSalesSetupInfo,
    getViSettings,
    fetchDealerSites,
    getLegalProviders,
    fetchTrimList,
    fetchTenantList,
    fetchLabels,
    fetchStateListFromCountryCode,
    fetchGlobalMetaData,
  }),
  withTekionConversion,
  withUsers,
  mediaUploaderHOCV3,
  withPropertyConsumer,
  withSize({ hasPageFooter: 0, hasPageHeader: 1 }),
  withActionHandlers({ ...ACTION_HANDLERS, ...ADD_FORM_ACTION_HANDLERS })
)(FormsPortalDealershipContainer);
