import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _filter from 'lodash/filter';
import _castArray from 'lodash/castArray';
import _head from 'lodash/head';
import _map from 'lodash/map';
import _toString from 'lodash/toString';
import _includes from 'lodash/includes';
import _set from 'lodash/set';
import _startCase from 'lodash/startCase';

import { withProps } from 'recompose';
import { produce } from 'immer';
import { EMPTY_ARRAY, EMPTY_STRING, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { getLookupByKeys } from '@tekion/tekion-base/services/lookupService';
import { RESOURCE_TYPE } from '@tekion/tekion-base/bulkResolvers/constants/resourceType';
import DEALER_PROPERTY_CONSTANTS from '@tekion/tekion-base/constants/dealerProperties';
import OPERATORS from '@tekion/tekion-base/constants/filterOperators';
import { isSupportedFileName } from '@tekion/tekion-base/utils/formConfigurator/utils';
import { DEFAULT_FORM_SCALE } from '@tekion/tekion-base/constants/formConfigurator/constants';
import GlobalAnalytics from '@tekion/tekion-base/utils/GlobalAnalytics';
import {
  nextday,
  getCurrentTime,
  getUnix,
  isValidDate,
  isBefore,
  isAfter,
  endOfDay,
  startOfDay,
} from '@tekion/tekion-base/utils/dateUtils';
import { getSignedURLs } from '@tekion/tekion-business/src/services/mediaV3';
import { getIsVisibilityAcrossModulesEnabled } from '@tekion/tekion-business/src/readers/core/FormSetup.reader';
import { downloadURI } from '@tekion/tekion-components/utils/downloadFile';
import TABLE_ACTION_TYPES from '@tekion/tekion-components/organisms/TableManager/constants/actionTypes';
import { getFiltersWithOutNullValue } from '@tekion/tekion-components/organisms/filterSection/utils/filterSection.utils';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/organisms/NotificationWrapper';
import { DEFAULT_PAGE_SIZE, FORM_STATE } from 'constants/formConfigurator';
import ROUTES from 'constants/routes';
import { MESSAGES, TOASTER_STATUS } from 'constants/pages';
import { DATE_TIME_FORMAT } from '@tekion/tekion-conversion-web';
import { getAnnotationDetailsFromResponse } from '@tekion/tekion-widgets/src/organisms/formConfiguratorTool/FormConfiguratorTool.utils';
import formConfiguratorToolAPI from '@tekion/tekion-widgets/src/organisms/formConfiguratorTool/FormConfiguratorTool.api';
import {
  hideFormsArchiveModal,
  showFormsArchiveModal,
  showArchiveConfirmationDialog as showFormArchiveConfirmationDialog,
  hideArchiveConfirmationDialog as hideFormArchiveConfirmationDialog,
  ARCHIVE_MODAL_VIEW_TYPES,
  ViewArchiveModalLink,
} from '@tekion/tekion-widgets/src/organisms/archivedFormsModal';
import { getDealerCountryCode } from '@tekion/tekion-widgets/src/organisms/formConfiguratorTool/utils/dealer.utils';
import { addSystemAdminToUsers } from '@tekion/tekion-business/src/utils/formConfigurator/formConfigurator.utils';

import { getAPIError } from '@tekion/tekion-widgets/src/appServices/sales/readers/error.reader';
import { getFieldsWithDateValuesInTimestamp } from '@tekion/tekion-widgets/src/organisms/formConfiguratorTool/FormConfiguratorTool.utils';

import { getUpdatedFormsPath, isFCEditFlow } from 'utils';

import {
  getSelectedFormData,
  appendDefaultSelectionToField,
  getFormListPayload,
  getSortObjForAPI,
  getFormFromPayload,
  getNewFormPayload,
  getUpdatedLabels,
} from '../utils/FormsPortalDealership.utils';
import {
  getFormsListWithPDFMetadata,
  getUserIdsToResolveFromResponse,
  getUserOptionLabel,
} from '../helpers/FormsPortalDealership.helpers';
import {
  LICENSED_BY_DISPLAY,
  VERSION_LIST_FILTER_TYPES as FILTER_TYPES,
  SEARCH_OPTION_IDS,
} from '../constants/FormsPortalDealership.constants';
import ACTION_TYPES from '../constants/FormsPortalDealership.actionTypes';
import formsPortalDealershipAPI from '../api/FormsPortalDealership.api';
import formsCommonAPI from '../../../services/common';
import { fetchLabelsInfo } from '../../../actions/common';
import { getFormattedFilters } from '../../../helpers/table';
import { getDefaultInternalFilters } from '../../formsViewTable/formsViewTable.constants';
import { FORMS_PORTAL } from '../../../constants/formConfigurator';
import { STATUS_KEYS } from '../../formsViewTable/formsViewTable.constants';
import { COLUMNS } from '../constants/FormsPortalDealership.constants';

const fetchFormsList = async ({ setState, getState, searchForms }) => {
  setState({ loading: true, showLibrarySearchHeader: false });
  const { searchText, currentPage, pageLength, sortDetailsForApi, fetchFormList, selectedFilters, searchField } =
    getState();
  const pageToFetch = currentPage > 0 && !searchForms ? currentPage - 1 : 0;
  const sortDetailsArray = _castArray(sortDetailsForApi || []);
  const sortDetails = _filter(sortDetailsArray, item => !_isEmpty(_get(item, 'key')));
  const showOnlyPublishedForm = !isFCEditFlow();

  const payload = getFormListPayload(
    searchText,
    pageToFetch,
    pageLength || DEFAULT_PAGE_SIZE,
    sortDetails,
    selectedFilters,
    searchField,
    showOnlyPublishedForm
  );

  const response = await fetchFormList(payload);

  const defaultFilters = getFormattedFilters([...getDefaultInternalFilters()]);
  const libraryReqPayload = {
    ...payload,
    filters: [
      ...defaultFilters,
      {
        field: COLUMNS.STATUS,
        key: COLUMNS.STATUS,
        operator: OPERATORS.IN,
        values: [STATUS_KEYS.NOT_OWNED],
      },
    ],
  };

  const libResponse = await formsCommonAPI.searchLibraryFormList(libraryReqPayload);

  if (libResponse) {
    const { count: libraryFormCount } = libResponse;

    setState({
      libraryFormCount,
      showLibrarySearchHeader: true,
    });
  }

  if (response) {
    const { count, hits } = response;
    const pageNumber = count > 0 && !searchForms ? currentPage : 1;
    const userIdsToResolve = getUserIdsToResolveFromResponse(hits);
    const usersList = await getLookupByKeys(RESOURCE_TYPE.TENANT_USER_MINIMAL_V2, userIdsToResolve);
    const resolvedUsers = addSystemAdminToUsers(usersList);

    setState({
      currentPage: pageNumber,
      count,
      formsList: await getFormsListWithPDFMetadata(hits),
      resolvedUsers,
    });
  }
  setState({ loading: false });
};

const fetchLabels = async ({ setState }) => {
  const { response } = await formsPortalDealershipAPI.getFormLabels();
  if (response) {
    setState({
      allLabels: _get(response, 'allLabels') || EMPTY_ARRAY,
      recentLabels: _get(response, 'recentLabels') || EMPTY_ARRAY,
    });
  } else {
    toaster(TOASTER_STATUS.ERROR, MESSAGES.FAILED_TO_FETCH_LABELS);
  }
};

const fetchDocumentSetupSettings = async ({ setState }) => {
  const formSetupSettings = await formsPortalDealershipAPI.fetchDocumentSetupSetings();
  const visibilityAcrossModulesEnabled = getIsVisibilityAcrossModulesEnabled(formSetupSettings);
  setState({ visibilityAcrossModulesEnabled });
};

const handleFetchDocumentTypes = async (action, { getState }) => {
  const { fetchDocumentTypes, isDealerTrackEnabled } = getState();
  fetchDocumentTypes(isDealerTrackEnabled);
};

const handlePageMount = async (action, { getState, setState }) => {
  GlobalAnalytics.sendSubAppLoadedEvent({ subAppName: 'FormConfigurator' });
  GlobalAnalytics.sendSubAppInitializingEvent({ subAppName: 'FormConfigurator' });
  const {
    fetchPrinterTypes,
    getFNIProducts,
    fetchSalesSetupInfo,
    fetchDueBills,
    fetchMetaData,
    fetchFuelTypes,
    getVehicleMake,
    getAllVehicleMakesAndModels,
    getViSettings,
    getLegalProviders,
    fetchTrimList,
    getDealerPropertyValue,
    fetchDealerSites,
    fetchTenantList,
    fetchStateListFromCountryCode,
    fetchGlobalMetaData,
  } = getState();
  const dealerCountryCode = getDealerCountryCode();
  fetchPrinterTypes();
  getFNIProducts();
  fetchDueBills();
  fetchMetaData();
  fetchFuelTypes();
  getVehicleMake();
  getAllVehicleMakesAndModels();
  fetchSalesSetupInfo();
  getViSettings();
  getLegalProviders();
  fetchTrimList();
  fetchTenantList();
  fetchStateListFromCountryCode(dealerCountryCode);
  if (getDealerPropertyValue(DEALER_PROPERTY_CONSTANTS.MULTI_OEM_SWITCH_ENABLED)) {
    fetchDealerSites();
  }
  fetchFormsList({ getState, setState });
  fetchLabels({ setState });
  fetchDocumentSetupSettings({ setState });
  fetchGlobalMetaData();
  fetchLabelsInfo({ setState });

  GlobalAnalytics.sendSubAppIntializedEvent({ subAppName: 'FormConfigurator' });
  GlobalAnalytics.sendBootstrapLoadedEvent();
};

const handleFormsListRefresh = (action, { setState, getState }) => {
  fetchFormsList({ getState, setState });
};

const handleOnSearch = (action, { getState, setState }) => {
  const {
    payload: { value: searchText },
  } = action;
  setState({ searchText, filterData: {}, selectedFilters: EMPTY_ARRAY }, () =>
    fetchFormsList({ getState, setState, searchForms: true })
  );
};

const handlePageChange = (action, { getState, setState }) => {
  const {
    payload: {
      value: { page, resultsPerPage },
    },
  } = action;
  setState(
    {
      currentPage: page,
      pageLength: resultsPerPage,
      page,
      resultsPerPage,
    },
    () => {
      fetchFormsList({ getState, setState });
    }
  );
};

const handleSortChange = (action, { getState, setState }) => {
  const sortDetails = _get(action, 'payload.value.sortTypeMap');
  const keyToSort = _get(action, 'payload.value.column.key');
  const sortDetailsForApi = getSortObjForAPI(keyToSort, sortDetails);
  setState({ sortDetails, sortDetailsForApi }, () => {
    fetchFormsList({ getState, setState });
  });
};

const handleFilterChange = (action, { getState, setState }) => {
  const {
    payload: { value: selectedFilters, selectedFilterGroup },
  } = action;

  setState(
    {
      selectedFilters: getFiltersWithOutNullValue(selectedFilters),
      selectedFilterGroup,
    },
    () => {
      fetchFormsList({ getState, setState });
    }
  );
};

const handleLabelChange = async (action, { setState }) => {
  const { payload: value } = action;
  setState(prevState =>
    produce(prevState, draft => {
      const { newRecentLabels, newAllLabels } = getUpdatedLabels({
        allLabels: draft.allLabels,
        recentLabels: draft.recentLabels,
        newLabels: value,
      });
      _set(draft, 'allLabels', newAllLabels);
      _set(draft, 'recentLabels', newRecentLabels);
    })
  );
};

const handleTableItemClick = async (action, { setState, getState }) => {
  const { payload } = action;
  const { formsList } = getState();
  const activeFormId = _get(payload, 'value.original.id');
  const selectedForm = getSelectedFormData(formsList, activeFormId);
  const digiCertEncryptionEnabled = _get(selectedForm, 'digiCertEncryptionEnabled');
  if (_get(selectedForm, 'formState') === FORM_STATE.DRAFT) {
    toaster('error', __('Form is in draft state. Please publish it to update form details'));
    return;
  }

  let pdfMetadata = EMPTY_OBJECT;
  const { pdfLibraryPdfKey } = selectedForm;
  if (pdfLibraryPdfKey) {
    pdfMetadata = await formConfiguratorToolAPI.getPDFMetadataFromPDFLibrary(pdfLibraryPdfKey);
  }

  const formData = getFormFromPayload(selectedForm, pdfMetadata);

  setState(prevState => ({
    showModal: !prevState.showModal,
    activeFormId,
    formValues: { ...formData, digiCertEncryptionEnabled },
    isEdit: true,
    warnings: {},
  }));
};

const handleAddFormModalToggle = (action, { setState }) => {
  setState(prevState => ({
    showModal: !prevState.showModal,
    formValues: {},
    isEdit: false,
    errors: {},
    warnings: {},
    selectedCategoryLabel: '',
  }));
};

const handleTestForm = (action, { getState }) => {
  const {
    payload: { formId },
  } = action;
  const { testForm, formsList } = getState();
  testForm(formsList, formId);
};

const handleAddForm = async (action, { setState, getState }) => {
  const {
    payload: { formData },
  } = action;
  const { addNewForm } = getState();
  setState({ isFormSubmitInProgress: true });
  const response = await addNewForm(formData);

  if (!response) return null;

  setState(
    produce(draft => {
      draft.formsList.unshift(response);
      draft.count += 1;
      draft.isFormSubmitInProgress = false;
      draft.showModal = false;
    })
  );
  return response;
};

const copyForm = async (copyFormNewName, formId, { getState, setState }) => {
  if (isSupportedFileName(copyFormNewName)) {
    setState({ isValidFormName: true });
  } else {
    setState({ isValidFormName: false });
    return false;
  }

  setState({ isCopyingForm: true, isValidFormName: true });
  const { response: formDetails } = await formConfiguratorToolAPI.getFormDetails(formId);
  const payload = {
    mediaId: formDetails.mediaId,
    formDisplayKey: copyFormNewName,
    originalFileName: copyFormNewName,
    effectiveDate: getUnix(getCurrentTime()),
    expiryDate: nextday(),
    formPrinterType: formDetails.formPrinterType,
    globalAnnotations: formDetails.globalAnnotations,
    customAnnotations: formDetails.customAnnotations,
    configurationsAnnotations: formDetails.configurationsAnnotations,
    fieldDisplaySections: _get(formDetails, 'fieldDisplaySections', EMPTY_ARRAY),
    previousFormSetupDetailsDocumentId: _get(formDetails, 'formSetupDetailsDocumentId'),
    pageCount: _get(formDetails, 'pageCount'),
  };
  const { response: saveResponse, rawResponse } = await formConfiguratorToolAPI.saveAnnotations(payload);

  if (_get(rawResponse, 'data.status') === 'success') {
    const { navigate } = getState();
    const newFormId = _get(saveResponse, 'dealerPdfKey', '');
    navigate(getUpdatedFormsPath(`${ROUTES.FORM_CONFIGURATOR_TOOL}/${newFormId}`));
    toaster(TOASTER_TYPE.SUCCESS, __(`Successfully created ${copyFormNewName}`));
  } else {
    toaster(TOASTER_TYPE.ERROR, __('Something went wrong'));
  }
  setState({ isCopyingForm: false });
  return true;
};

const handleCopyForm = async (_, { setState, getState }) => {
  const {
    isCopyingForm,
    copyFormNewName,
    copyFormDetails: { formId },
  } = getState();

  if (!isCopyingForm && copyFormNewName) {
    if (await copyForm(copyFormNewName, formId, { getState, setState })) {
      hideCopyConfirmationDialog(_, { setState });
    }
  }
};

const handleEditForm = async (action, { getState }) => {
  const {
    payload: { formId },
  } = action;
  const { navigate } = getState();

  navigate(getUpdatedFormsPath(`${ROUTES.FORM_CONFIGURATOR_TOOL}/${formId}`));
};

const handleEditFormRequest = async (_, { setState }) => {
  setState({ showRequestFormConfirmation: true });
};

const handleCloseEditFormRequest = async (_, { setState }) => {
  setState({ showRequestEditForm: false });
};

const onRequestFormConfirmation = (_, { setState }) => {
  setState({ showRequestFormConfirmation: false });
  setState({ showRequestEditForm: true });
};

const onRequestFormConfirmationClose = (_, { setState }) => {
  setState({ showRequestFormConfirmation: false });
};

const handleUpdateForm = async (action, { getState, setState }) => {
  const {
    payload: { formData },
  } = action;

  setState({ isFormSubmitInProgress: true });
  const { updateForm, formCategories, saveWithReason } = getState();
  const { formCategory } = formData;
  const validFormCategories = _filter(formCategories, category => _head(formCategory) === category.formCategory);
  const validatedFormCategory = !_isEmpty(validFormCategories) ? _head(formCategory) : null;
  // const digiCertEncryptionEnabled = _get(formData, 'digiCertFormEnabled');

  const response = await updateForm({
    ...formData,
    formCategory: validatedFormCategory,
    comment: saveWithReason,
    // digiCertEncryptionEnabled,
  });
  if (!response) {
    setState({ isFormSubmitInProgress: false });
    return null;
  }
  setState({
    isFormSubmitInProgress: false,
    showModal: false,
    isEdit: false,
    isSaveConfirmationModalVisible: false,
    saveWithReason: '',
  });

  setTimeout(() => {
    fetchFormsList({ getState, setState }); // to get the updated data from ES
    fetchLabels({ setState });
  }, 1000);
  return response;
};

const handleFormSubmit = async (action, { getState, setState }) => {
  const {
    formValues,
    usageTargetParams,
    fniOptions,
    dueBills,
    isEdit,
    navigate,
    makes,
    econtractForm,
    moduleVisibilityRules,
    metaData,
  } = getState();
  const {
    payload: { routeUsertoConfigurator, dynamicUsageCategoryVsRules },
  } = action;
  const saveFormData = isEdit ? handleUpdateForm : handleAddForm;
  const formData = getNewFormPayload({
    formValues,
    usageTargetParams,
    fnis: fniOptions,
    dueBills,
    makes,
    econtractForm,
    moduleVisibilityRules,
    metaData,
    dynamicUsageCategoryVsRules,
  });

  if (!routeUsertoConfigurator) await saveFormData({ payload: { formData } }, { getState, setState });

  if (routeUsertoConfigurator && _get(formValues, 'formKey')) {
    navigate(getUpdatedFormsPath(`${ROUTES.FORM_CONFIGURATOR_TOOL}/${_get(formValues, 'formKey')}`));
  }
};

const handleCreateForm = async (action, { getState }) => {
  const { navigate } = getState();
  navigate(getUpdatedFormsPath(ROUTES.FORM_CONFIGURATOR_TOOL));
};

const showCopyConfirmationDialog = (action, { setState }) => {
  const {
    payload: { formId, formName },
  } = action;
  setState({
    showCopyConfirmationDialog: true,
    copyFormDetails: {
      formId,
      formName,
    },
  });
};

const hideCopyConfirmationDialog = (_, { setState }) => {
  setState({
    showCopyConfirmationDialog: false,
    copyFormDetails: undefined,
    copyFormNewName: undefined,
    isValidFormName: true,
  });
};

const handleCopyFormNameChange = (action, { setState }) => {
  const {
    payload: { copyFormNewName },
  } = action;
  setState({ copyFormNewName, isValidFormName: true });
};

const showVersionHistoryModal = async (action, { getState, setState }) => {
  const {
    payload: { formKey, formName },
  } = action;
  const { getFormattedDateAndTime } = getState();
  const response = await formsPortalDealershipAPI.getVersionInfoList(formKey);
  const responseList = _get(response, 'formAnnotationDocumentVersionInfoList');
  const userIdsToResolve = getUserIdsToResolveFromResponse(responseList);
  const usersList = await getLookupByKeys(RESOURCE_TYPE.TENANT_USER_MINIMAL_V2, userIdsToResolve);
  const resolvedUsers = addSystemAdminToUsers(usersList);
  if (!_isEmpty(response)) {
    const versionHistoryListData = _map(responseList, formVersionInfo => {
      const { version, active, createdAt, createdBy } = formVersionInfo;
      const createdByUser = _get(resolvedUsers, createdBy);
      return {
        ...formVersionInfo,
        version: _toString(version),
        isFormActive: active,
        lastModifiedTimestamp: createdAt,
        createdBy: _startCase(getUserOptionLabel(createdByUser)),
        authorId: createdBy,
        lastModifiedDate: isValidDate(createdAt)
          ? getFormattedDateAndTime({
              value: createdAt,
              formatType: DATE_TIME_FORMAT.DATE_ABBREVIATED_MONTH_YEAR_WITH_HOUR_MINUTE,
            })
          : __('Date not available'),
      };
    });

    setState({
      showVersionHistoryModal: true,
      versionHistoryFormName: formName,
      versionHistoryFormKey: formKey,
      versionHistoryListData,
      versionHistoryListFormattedResponse: versionHistoryListData,
    });
  } else {
    toaster(TOASTER_TYPE.ERROR, __('Something went wrong'));
  }
};

const hideVersionHistoryModal = (action, { setState }) => {
  setState({
    showVersionHistoryModal: false,
    selectedFormVersionComment: EMPTY_STRING,
    versionHistoryFormName: EMPTY_STRING,
    versionHistoryFormKey: EMPTY_STRING,
    versionHistoryListData: EMPTY_ARRAY,
    versionHistoryListFormattedResponse: EMPTY_ARRAY,
    selectedFormVersion: EMPTY_STRING,
    versionListAppliedFilters: EMPTY_ARRAY,
    isLoadingVersionPreviewPDF: false,
    versionPreviewPDFLoaded: false,
  });
};

const handleFormVersionSelect = async (action, { getState, setState }) => {
  const {
    payload: { key, versionComment },
  } = action;
  const { versionHistoryFormKey } = getState();
  setState({
    selectedFormVersion: key,
    selectedFormVersionComment: versionComment,
    isLoadingVersionPreviewPDF: true,
    versionPreviewFormData: EMPTY_OBJECT,
    versionPreviewTotalPages: 1,
    versionPreviewAnnotatedFields: EMPTY_ARRAY,
    versionPreviewPageDimensions: EMPTY_OBJECT,
    versionPreviewPDFLoaded: false,
  });

  const versionPreviewFormResponse = await formsPortalDealershipAPI.fetchVersionInfo(versionHistoryFormKey, key);
  const versionPreviewFormData = {};

  if (!_isEmpty(versionPreviewFormResponse)) {
    const mediaId = _get(versionPreviewFormResponse, 'mediaId');
    if (mediaId) {
      const signedUrlResponse = await getSignedURLs([mediaId]);
      const url = _get(signedUrlResponse, '0.normal.url');
      versionPreviewFormData.mediaId = mediaId;
      versionPreviewFormData.url = url;
      setState({ versionPreviewFormResponse });
    }
  } else if (_isEmpty(versionPreviewFormResponse)) {
    toaster(TOASTER_TYPE.ERROR, __('Something went wrong'));
  }

  setState({ isLoadingVersionPreviewPDF: false, versionPreviewFormData });
};

const updateTotalPages = (action, { setState }) => {
  const {
    payload: { totalPages },
  } = action;

  setState({
    versionPreviewTotalPages: totalPages,
  });
};

const updatePDFViewHeight = (action, { setState, getState }) => {
  const {
    payload: { pageDimensions },
  } = action;

  const { versionPreviewTotalPages, versionPreviewFormResponse } = getState();

  if (!_isEmpty(versionPreviewFormResponse)) {
    const { annotatedFields } = getAnnotationDetailsFromResponse(
      versionPreviewFormResponse,
      pageDimensions,
      versionPreviewTotalPages,
      DEFAULT_FORM_SCALE
    );

    setState({
      versionPreviewAnnotatedFields: annotatedFields,
      versionPreviewPageDimensions: pageDimensions,
      versionPreviewPDFLoaded: true,
    });
  } else {
    setState({ versionPreviewAnnotatedFields: [] });
  }
};

const showRestoreConfirmationDialog = (action, { setState }) => {
  const {
    payload: { formVersionSelectedToRestore },
  } = action;
  setState({
    showRestoreConfirmationDialog: true,
    formVersionSelectedToRestore,
  });
};

const hideRestoreConfirmationDialog = (action, { setState }) => {
  setState({
    showRestoreConfirmationDialog: false,
  });
};

const handleFormVersionRestoreAndPublish = async (action, { getState, setState }) => {
  const { versionHistoryFormName, versionHistoryFormKey, formVersionSelectedToRestore, getUserInfoById } = getState();
  setState({ isLoadingFormVersionRestore: true });

  const response = await formsPortalDealershipAPI.restoreFormVersion(
    versionHistoryFormKey,
    formVersionSelectedToRestore
  );
  if (response) {
    toaster('success', __('Restored form version successfully'));
    showVersionHistoryModal(
      { payload: { formKey: versionHistoryFormKey, formName: versionHistoryFormName, getUserInfoById } },
      { getState, setState }
    );
    setTimeout(() => {
      handleFormsListRefresh(action, { getState, setState });
    }, 1000);
  } else toaster('error', __('Something went wrong'));

  setState({ isLoadingFormVersionRestore: false });
  hideRestoreConfirmationDialog(action, { setState });
};

const handleApplyVersionListFilter = (action, { getState, setState }) => {
  const {
    payload: { versionListAppliedFilters },
  } = action;
  const { versionHistoryListFormattedResponse, selectedFormVersion } = getState();

  if (_isEmpty(versionListAppliedFilters)) {
    setState({ versionHistoryListData: versionHistoryListFormattedResponse, versionListAppliedFilters });
    return;
  }

  const filteredVersionList = _filter(versionHistoryListFormattedResponse, version => {
    const { lastModifiedTimestamp, authorId } = version;
    const { value: dateFilterValue } = versionListAppliedFilters[FILTER_TYPES.DATE];
    const { operator: userFilterOperator, value: userFilterValue } = versionListAppliedFilters[FILTER_TYPES.USER];

    if (
      !_isEmpty(dateFilterValue) &&
      !(
        isBefore(lastModifiedTimestamp, dateFilterValue[0], { inclusive: true }) &&
        isAfter(lastModifiedTimestamp, dateFilterValue[1], { inclusive: true })
      )
    ) {
      return false;
    }

    if (!_isEmpty(userFilterValue)) {
      const userFilterValueArray = _map(userFilterValue, ({ value }) => value);
      if (userFilterOperator === OPERATORS.IN && !_includes(userFilterValueArray, authorId)) return false;
      if (userFilterOperator === OPERATORS.NIN && _includes(userFilterValueArray, authorId)) return false;
    }

    return true;
  });

  if (
    _includes(
      _map(filteredVersionList, ({ version }) => version),
      selectedFormVersion
    )
  ) {
    setState({ versionHistoryListData: filteredVersionList, versionListAppliedFilters });
  } else {
    setState({
      versionHistoryListData: filteredVersionList,
      versionListAppliedFilters,
      selectedFormVersion: EMPTY_STRING,
    });
  }
};

const handleResetVersionListFilter = (action, { getState, setState }) => {
  const { versionHistoryListFormattedResponse } = getState();
  setState({ versionHistoryListData: versionHistoryListFormattedResponse, versionListAppliedFilters: EMPTY_ARRAY });
};

const handleFormSelect = (action, { setState, getState }) => {
  const {
    payload: { selection },
  } = action;
  const { formsList } = getState();

  const selectedFormKeys = _map(
    _filter(formsList, form => _includes(selection, _get(form, 'id'))),
    form => _get(form, 'formKey')
  );
  setState({ selection, selectedFormKeys });
};

const handleFormMigration = async (action, { setState }) => {
  setState({
    showMigrateModal: true,
  });
};

const handleCloseFormMigration = async (action, { setState }) => {
  setState({ showMigrateModal: false });
};
const showDealers = async (action, { setState }) => {
  const {
    payload: { value },
  } = action;
  const response = await formsPortalDealershipAPI.getDealerList(_head(value));
  setState({
    dealerOptions: response,
  });
};

const handleMigrateFormSubmit = async (action, { getState, setState }) => {
  const { selectedFormKeys } = getState();
  const {
    payload: { targetTenantId, targetDealerId },
  } = action;
  const payload = {
    formKeys: selectedFormKeys,
    targetTenantId,
    targetDealerId,
  };
  const response = await formsPortalDealershipAPI.migrateForms(payload);
  if (response) {
    setState(() => ({
      migrationResponseData: response,
    }));
    toaster('success', __('Selected Forms Migrated Successfully'));
    setState({ selection: [] });
    setState({ showMigrateModal: false, showResponseModal: true });
  } else {
    toaster('error', __('Selected forms failed to Migrate'));
  }
};

const handleCloseMigrationResponse = async (action, { setState }) => {
  setState({ showResponseModal: false });
};

const showAuditLogs = (action, { setState }) => {
  const {
    payload: { formKey },
  } = action;
  setState({
    showAuditLogs: true,
    assetId: formKey,
  });
};

const toggleAuditLogs = (action, { setState }) => {
  setState({
    showAuditLogs: false,
  });
};

const showArchivedFormsModal = () => {
  showFormsArchiveModal({ modalViewType: ARCHIVE_MODAL_VIEW_TYPES.FORMS_CONFIGURATOR });
};

const hideArchivedFormsModal = () => {
  hideFormsArchiveModal();
};

const showArchiveConfirmationDialog = (action, { setState }) => {
  const { payload } = action;
  const { formId: formToArchiveId, formName: formToArchiveName } = payload;
  setState({ formToArchiveId, formToArchiveName });
  showFormArchiveConfirmationDialog({
    modalViewType: ARCHIVE_MODAL_VIEW_TYPES.FORMS_CONFIGURATOR,
    key: formToArchiveId,
    name: formToArchiveName,
  });
};

const hideArchiveConfirmationDialog = (_, { setState }) => {
  hideFormArchiveConfirmationDialog();
  setState({ formToArchiveId: EMPTY_STRING, formToArchiveName: EMPTY_STRING });
};

const handleFormArchive = async (action, { getState, setState }) => {
  const { payload } = action;
  const { formKey, formName } = payload;
  const { archiveForm } = getState();

  const handleViewArchiveLinkClick = () => {
    showArchivedFormsModal();
  };

  try {
    const response = await archiveForm(formKey);
    if (response) {
      toaster(
        TOASTER_TYPE.SUCCESS,
        __("Form '{{formName}}' successfully archived.", { formName }),
        EMPTY_OBJECT,
        __('Success'),
        withProps({ showModal: handleViewArchiveLinkClick })(ViewArchiveModalLink)
      );
      setTimeout(() => {
        handleFormsListRefresh(action, { getState, setState });
      }, 1000);
    }
  } catch (error) {
    toaster(TOASTER_TYPE.ERROR, __('Failed to archive form'));
  }

  hideArchiveConfirmationDialog(action, { getState, setState });
};

const handleRestoreArchivedForm = async (action, { getState, setState }) => {
  const { payload } = action;
  const { formKey, formName } = payload;
  const { restoreArchivedForm } = getState();

  try {
    const response = await restoreArchivedForm(formKey);
    if (response) {
      toaster(TOASTER_TYPE.SUCCESS, __("Form '{{formName}}' restored", { formName }));
      hideArchivedFormsModal();
      setTimeout(() => {
        handleFormsListRefresh(action, { getState, setState });
      }, 1000);
    }
  } catch (error) {
    toaster(TOASTER_TYPE.ERROR, __('Failed to restore form'));
  }
};

const handleSearchFieldChange = (action, { setState }) => {
  setState({ searchField: _get(action, 'payload.value') });
};

const refreshData = (_, { getState, setState }) => {
  fetchFormsList({ getState, setState, searchForms: true });
};

const showFormPreviewModel = (action, { setState }) => {
  const { payload } = action;

  const {
    additional: { event },
    values: previewModelData,
  } = payload;
  event.stopPropagation();
  setState({
    showFormPreviewModel: true,
    previewModelData,
  });
};

const hideFormPreviewModel = (_, { setState }) => {
  setState({
    showFormPreviewModel: false,
    previewModelFormKey: '',
  });
};

const onPreviewSubmit = (_, { getState, setState }) => {
  const {
    previewModelData: { id },
  } = getState();
  handleTableItemClick(
    {
      payload: {
        value: { original: { id } },
      },
    },
    { setState, getState }
  );
};

const onDownloadFormPdf = async action => {
  const {
    payload: { formKey },
  } = action;

  try {
    if (formKey) {
      const signedUrlResponse = await formsCommonAPI.downloadFormPdf(formKey);
      if (!signedUrlResponse) throw new Error('error');
      downloadURI(signedUrlResponse);
    }
  } catch (err) {
    toaster(TOASTER_TYPE.ERROR, __('Failed to download'));
  }
};

const hideLibrarySearchHeader = (_, { setState }) => {
  setState({
    showLibrarySearchHeader: false,
  });
};

const handleFormsLibraryViewNavigation = (_, { getState }) => {
  const { navigate, searchText = '', searchField = SEARCH_OPTION_IDS.ALL } = getState();
  let queryString = '';
  if (searchText?.length) {
    queryString += `searchText=${searchText}&`;
  }

  if (searchField?.length) {
    if (!searchText) {
      queryString += `searchField=${SEARCH_OPTION_IDS.ALL}&`;
    } else {
      queryString += `searchField=${searchField}&`;
    }
  }

  queryString += `source=${FORMS_PORTAL}`;

  navigate(getUpdatedFormsPath(`${ROUTES.FORM_LIBRARY_VIEW}?${queryString}`));
};

const showPreviewModel = async (action, { setState }) => {
  const {
    payload: { formKey },
  } = action;

  const { response: formDetails } = await formConfiguratorToolAPI.getFormDetails(formKey);

  const {
    configurationsAnnotations,
    customAnnotations,
    fieldDisplaySections,
    formDisplayKey,
    formPrinterType,
    globalAnnotations,
    mediaId,
    originalFileName,
  } = formDetails;

  const payload = {
    configurationsAnnotations,
    customAnnotations,
    effectiveDate: getUnix(startOfDay(getCurrentTime())),
    expiryDate: getUnix(endOfDay(nextday())),
    fieldDisplaySections,
    formDisplayKey,
    formPrinterType,
    globalAnnotations,
    mediaId,
    originalFileName,
  };

  const { response: previewModelData } = await formConfiguratorToolAPI.newPreviewFields(payload);
  const formIdForValidateForm = _get(formDetails, 'dealerPdfKey', null);

  setState({
    showPreviewModel: true,
    formDetails,
    formIdForValidateForm,
    previewModelData,
  });
};

const closePreviewModel = (action, { setState }) => {
  setState({
    showPreviewModel: false,
  });
};

const onApplyAndValidate = async (action, { getState, setState }) => {
  const {
    payload: { fields, validatePreviewId: assetId, validatePreviewBy: assetType, requestId },
  } = action;
  const { previewModelData, formDetails } = getState();
  const fieldsWithValues = getFieldsWithDateValuesInTimestamp(fields);

  const {
    configurationsAnnotations,
    customAnnotations,
    fieldDisplaySections,
    formDisplayKey,
    formPrinterType,
    globalAnnotations,
    mediaId,
    originalFileName,
  } = formDetails;

  const payload = {
    previewFormAnnotationRequest: {
      configurationsAnnotations,
      customAnnotations,
      effectiveDate: getUnix(startOfDay(getCurrentTime())),
      expiryDate: getUnix(endOfDay(nextday())),
      formDisplayKey,
      formPrinterType,
      globalAnnotations,
      mediaId,
      originalFileName,
    },
    formDetails: { ...previewModelData, fields: fieldsWithValues },
    fieldDisplaySections,
    mandatorySigningType: null,
    requestId,
    assetType,
    assetId,
  };

  const { error } = await formConfiguratorToolAPI.newPreviewUrl(payload);
  if (!_isEmpty(error)) {
    const errorMsg = getAPIError(error);
    if (errorMsg === undefined) {
      toaster(TOASTER_TYPE.ERROR, __('Error in previewing the form.'));
    } else toaster(TOASTER_TYPE.ERROR, errorMsg);
  }
};

const ACTION_HANDLERS = {
  [ACTION_TYPES.ON_PAGE_MOUNT]: handlePageMount,
  [ACTION_TYPES.GET_DOCUMENT_TYPE]: handleFetchDocumentTypes,
  [ACTION_TYPES.TABLE_ITEMS_REFRESH]: handleFormsListRefresh,
  [ACTION_TYPES.FETCH_FORMS_LIST]: handleFormsListRefresh,
  [ACTION_TYPES.ON_FORM_TEST]: handleTestForm,
  [ACTION_TYPES.ON_ADD_FORM]: handleAddForm,
  [ACTION_TYPES.ON_COPY_FORM]: handleCopyForm,
  [ACTION_TYPES.ON_FORM_EDIT]: handleEditForm,
  [ACTION_TYPES.ON_FORM_REQUEST_EDIT]: handleEditFormRequest,
  [ACTION_TYPES.ON_FORM_REQUEST_EDIT_CLOSE]: handleCloseEditFormRequest,
  [ACTION_TYPES.ON_REQUEST_FORM_CONFIRMATION]: onRequestFormConfirmation,
  [ACTION_TYPES.ON_REQUEST_FORM_CONFIRMATION_CLOSE]: onRequestFormConfirmationClose,
  [ACTION_TYPES.ON_UPDATE_FORM]: handleUpdateForm,
  [ACTION_TYPES.ON_TOGGLE_ADD_FORM_MODAL]: handleAddFormModalToggle,
  [ACTION_TYPES.SHOW_CREATE_COPY_MODAL]: showCopyConfirmationDialog,
  [ACTION_TYPES.HIDE_CREATE_COPY_MODAL]: hideCopyConfirmationDialog,
  [ACTION_TYPES.ON_FORM_SUBMIT]: handleFormSubmit,
  [ACTION_TYPES.ON_COPY_FORM_NAME_CHANGE]: handleCopyFormNameChange,
  [ACTION_TYPES.ON_CREATE_FORM_CLICK]: handleCreateForm,
  [ACTION_TYPES.SHOW_VERSION_HISTORY_MODAL]: showVersionHistoryModal,
  [ACTION_TYPES.HIDE_VERSION_HISTORY_MODAL]: hideVersionHistoryModal,
  [ACTION_TYPES.ON_FORM_VERSION_SELECT]: handleFormVersionSelect,
  [ACTION_TYPES.SHOW_RESTORE_VERSION_MODAL]: showRestoreConfirmationDialog,
  [ACTION_TYPES.HIDE_RESTORE_VERSION_MODAL]: hideRestoreConfirmationDialog,
  [ACTION_TYPES.ON_FORM_VERSION_RESTORE_AND_PUBLISH]: handleFormVersionRestoreAndPublish,
  [ACTION_TYPES.ON_VERSION_PREVIEW_PDF_PAGE_LOAD]: updatePDFViewHeight,
  [ACTION_TYPES.ON_VERSION_PREVIEW_PDF_LOAD]: updateTotalPages,
  [ACTION_TYPES.ON_APPLY_VERSION_LIST_FILTER]: handleApplyVersionListFilter,
  [ACTION_TYPES.ON_RESET_VERSION_LIST_FILTER]: handleResetVersionListFilter,
  [TABLE_ACTION_TYPES.TABLE_ITEM_CLICK]: handleTableItemClick,
  [TABLE_ACTION_TYPES.TABLE_SEARCH_TERM_UPDATE]: handleOnSearch,
  [TABLE_ACTION_TYPES.TABLE_ITEMS_PAGE_UPDATE]: handlePageChange,
  [TABLE_ACTION_TYPES.TABLE_ITEMS_SORT]: handleSortChange,
  [TABLE_ACTION_TYPES.TABLE_ITEMS_SET_FILTER]: handleFilterChange,
  [TABLE_ACTION_TYPES.TABLE_SEARCH_FIELD]: handleSearchFieldChange,
  [TABLE_ACTION_TYPES.TABLE_ITEMS_REFRESH]: refreshData,
  [ACTION_TYPES.ON_MIGRATE_FORM_CLICK]: handleFormMigration,
  [ACTION_TYPES.ON_CLOSE_MIGRATE_FORM]: handleCloseFormMigration,
  [ACTION_TYPES.ON_MIGRATION_FORM_SUBMIT]: handleMigrateFormSubmit,
  [ACTION_TYPES.ON_SELECT_FORM]: handleFormSelect,
  [ACTION_TYPES.ON_CLOSE_MIGRATION_RESPONSE]: handleCloseMigrationResponse,
  [ACTION_TYPES.SHOW_MIGRATE_DEALERS]: showDealers,
  [ACTION_TYPES.ON_LABEL_CHANGE]: handleLabelChange,
  [ACTION_TYPES.SHOW_AUDIT_LOGS_DRAWER]: showAuditLogs,
  [ACTION_TYPES.TOGGLE_AUDIT_LOGS]: toggleAuditLogs,
  [ACTION_TYPES.SHOW_ARCHIVED_FORMS_MODAL]: showArchivedFormsModal,
  [ACTION_TYPES.HIDE_ARCHIVED_FORMS_MODAL]: hideArchivedFormsModal,
  [ACTION_TYPES.SHOW_ARCHIVE_CONFIRMATION_MODAL]: showArchiveConfirmationDialog,
  [ACTION_TYPES.HIDE_ARCHIVE_CONFIRMATION_MODAL]: hideArchiveConfirmationDialog,
  [ACTION_TYPES.ON_ARCHIVE_FORM]: handleFormArchive,
  [ACTION_TYPES.ON_RESTORE_ARCHIVED_FORM]: handleRestoreArchivedForm,
  [ACTION_TYPES.SHOW_FORM_PREVIEW_MODEL]: showFormPreviewModel,
  [ACTION_TYPES.HIDE_FORM_PREVIEW_MODEL]: hideFormPreviewModel,
  [ACTION_TYPES.ON_PREVIEW_SUBMIT]: onPreviewSubmit,
  [ACTION_TYPES.DOWNLOAD_FORM_PDF]: onDownloadFormPdf,
  [ACTION_TYPES.HIDE_LIBRARY_SEARCH_HEADER]: hideLibrarySearchHeader,
  [ACTION_TYPES.NAVIGATE_TO_FORMS_LIBRARY_VIEW]: handleFormsLibraryViewNavigation,
  [ACTION_TYPES.SHOW_PREVIEW_MODEL]: showPreviewModel,
  [ACTION_TYPES.CLOSE_PREVIEW_MODEL]: closePreviewModel,
  [ACTION_TYPES.ON_APPLY_AND_VALIDATE]: onApplyAndValidate,
};

export default ACTION_HANDLERS;
