import { canEditEcontractingForms } from '@tekion/tekion-business/src/utils/formConfigurator/formConfigurator.utils';
import {
  hasViewOnlyFormConfigurator,
  hasFormArchivePermission,
  hasEContractingEditPermission,
  hasEditGlobalFieldsPermission,
  hasEditTekionRestrictedFieldsPermission,
  hasEditDPRestrictedFieldsPermission,
  hasCreateFormPermission,
} from 'permissions/formConfigurator.permissions';
import { KEBAB_ACTION_KEY, ACTIONS } from './kebabAction.constants';

export const getFormActionItemsConfig = ({ columnData, isFCEditFlow }) => {
  const isViewOnlyMode = hasViewOnlyFormConfigurator();
  const canEditEcontractForm = canEditEcontractingForms(columnData, hasEContractingEditPermission());
  const kebabMenuOptions = [
    {
      key: KEBAB_ACTION_KEY.GENERAL,
      label: __('General'),
      children: [
        {
          icon: 'icon-settings-wrench-outline',
          key: ACTIONS.TEST,
          label: __('Test'),
        },
        {
          key: ACTIONS.EDIT,
          icon: isViewOnlyMode ? 'icon-eye-outline' : 'icon-edit',
          label: isViewOnlyMode || !canEditEcontractForm ? __('View') : __('Edit'),
        },
        {
          key: ACTIONS.REQUEST_EDIT,
          label: __('Request Form Change'),
          icon: 'icon-T-logo',
          disabled:
            isViewOnlyMode ||
            (!hasEditGlobalFieldsPermission() &&
              !hasEditTekionRestrictedFieldsPermission() &&
              !hasEditDPRestrictedFieldsPermission()),
        },
      ],
    },
    {
      key: KEBAB_ACTION_KEY.SUMMARIES,
      label: __('Summaries'),
      children: [
        {
          key: ACTIONS.SHOW_VERSIONS,
          icon: 'icon-versions',
          label: __('Version History'),
        },
        {
          key: ACTIONS.AUDIT_LOGS,
          icon: 'icon-audit-log',
          label: __('Audit Logs'),
        },
      ],
    },
    {
      key: KEBAB_ACTION_KEY.OTHERS,
      label: __('Others'),
      children: [
        {
          icon: 'icon-duplicate',
          key: ACTIONS.COPY,
          label: __('Create a Copy'),
          disabled: isViewOnlyMode || !hasCreateFormPermission(),
        },
        {
          key: ACTIONS.DOWNLOAD,
          icon: 'icon-download1',
          label: __('Download'),
          disabled: false,
        },
        {
          icon: 'icon-archive',
          key: ACTIONS.ARCHIVE,
          label: __('Archive'),
          disabled: isViewOnlyMode || !hasFormArchivePermission(),
        },
      ],
    },
  ];
  const kebabMenuOptionsLite = [
    {
      key: ACTIONS.SHOW_FORM_SETTINGS,
      icon: 'icon-settings',
      label: __('Form Settings'),
    },
    {
      icon: 'icon-printer2',
      key: ACTIONS.SHOW_PREVIEW,
      label: __('Test Print'),
    },
    {
      key: ACTIONS.AUDIT_LOGS,
      icon: 'icon-audit-log',
      label: __('Audit Logs'),
    },
    {
      key: ACTIONS.DOWNLOAD,
      icon: 'icon-download1',
      label: __('Download'),
    },
  ];
  return isFCEditFlow ? kebabMenuOptions : kebabMenuOptionsLite;
};
