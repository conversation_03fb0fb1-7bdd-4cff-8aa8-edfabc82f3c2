/* eslint-disable import/order */
import React, { PureComponent, Fragment } from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';
import { defaultMemoize } from 'reselect';
import { withProps, compose } from 'recompose';

// Lodash
import _map from 'lodash/map';
import _get from 'lodash/get';
import _noop from 'lodash/noop';
import _keyBy from 'lodash/keyBy';
import _size from 'lodash/size';
import _isEmpty from 'lodash/isEmpty';
import _head from 'lodash/head';
import _filter from 'lodash/filter';
import _includes from 'lodash/includes';
import _uniqBy from 'lodash/uniqBy';
import _forEach from 'lodash/forEach';

// Action types
import TABLE_ACTION_TYPES from '@tekion/tekion-components/organisms/TableManager/constants/actionTypes';
import actionTypes from '@tekion/tekion-components/organisms/FormBuilder/constants/actionTypes';
import ACTION_TYPES from './constants/FormsPortalDealership.actionTypes';

// Components
import Content from '@tekion/tekion-components/atoms/Content';
import TableManager from '@tekion/tekion-components/organisms/TableManager';
import DefaultContent from '@tekion/tekion-components/molecules/CellRenderers/DefaultContent';
import Modal from '@tekion/tekion-components/molecules/Modal';
import ConfirmationDialog from '@tekion/tekion-components/molecules/confirmationDialog';
import withCheckboxTable from '@tekion/tekion-components/organisms/withCheckboxTable';
import FixedColumnTable from '@tekion/tekion-components/molecules/table/FixedColumnTable';
import IconWithText from '@tekion/tekion-components/atoms/IconWithText';
import { DEFAULT_FILTER_GROUP } from '@tekion/tekion-components/organisms/filterSection';
import FormBuilder from '@tekion/tekion-components/organisms/FormBuilder';
import Heading from '@tekion/tekion-components/atoms/Heading';
import Button from '@tekion/tekion-components/atoms/Button';
import Input from '@tekion/tekion-components/molecules/Input';
import PageHeader from '@tekion/tekion-components/molecules/pageComponent/PageHeader';
import FontIcon, { SIZES } from '@tekion/tekion-components/atoms/FontIcon';
import Menu from '@tekion/tekion-components/molecules/Menu';
import DropDown from '@tekion/tekion-components/molecules/DropDown';
import KebabMenu from '@tekion/tekion-components/molecules/KebabMenu';
import Ellipsis from '@tekion/tekion-components/atoms/Ellipsis';
import SelectInput from '@tekion/tekion-components/organisms/FormBuilder/fieldRenderers/SelectInput';
import AuditLogs from '@tekion/tekion-widgets/src/appServices/service/organisms/AuditLogs';
import AddFormModal from '@tekion/tekion-widgets/src/organisms/AddForm/AddFormModal';
import ArchivedFormsModal, {
  ARCHIVE_MODAL_VIEW_TYPES,
  ArchiveConfirmationDialog,
} from '@tekion/tekion-widgets/src/organisms/archivedFormsModal';
import VersionHistoryModal from './molecules/VersionHistoryModal/VersionHistoryModal';
import MigrationResponseModal from './molecules/MigrationResponseModal/MigrationResponseModal';
import Sidebar from '../../organisms/sidebar';
import KebabActionMenu from './molecules/kebabAction';
import PreviewModel from '../formsViewTable/components/PreviewModel';
import ModalWithIframe from '../../organisms/ModalWithIframe/ModalWithIframe';
import LibraryCountSearch from '../../molecules/libraryCountSearch';
import ValidateFormModal from '@tekion/tekion-widgets/src/organisms/formConfiguratorTool/components/ValidateFormModal';
import AddFormDrawer from '@tekion/tekion-widgets/src/organisms/AddForm/AddFormDrawer';

// Constants
import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { REQUIRED_FORM_FIELDS } from '@tekion/tekion-widgets/src/organisms/AddForm/AddForm.constants';

import {
  AUDIT_ID,
  AUDITS_LABEL,
  AUDIT_FALLBACK_CONFIG,
} from '@tekion/tekion-base/constants/formConfigurator/constants';
import { FORM_SOURCE } from '@tekion/tekion-base/constants/deal/formSetup';
import { NO_DATA } from '@tekion/tekion-base/app.constants';
import { DEFAULT_PAGE_SIZE, FORM_STATE, FORM_TYPE_DISPLAY_NAME } from 'constants/formConfigurator';
import {
  TABLE_MANAGER_PROPS,
  HEADER_PROPS,
  COLUMN_MIN_WIDTH,
  COLUMN_RENDERERS,
  FILTER_PROPS,
  ERRORS,
  FORM_FIELDS,
  ACTION_MENU_ITEMS,
  COLUMNS,
  FORMS_AUDITS_FILTER_TYPES,
  ARCHIVE_MODAL_COLUMN_IDS,
  SEARCH_OPTION_IDS,
  SEARCHABLE_FIELDS_OPTIONS,
  SUB_HEADER_HEIGHT,
} from './constants/FormsPortalDealership.constants';

// Helpers
import { getColumnsByConfig } from '../../helpers/table';

// HOCs
import withFormsPortalDealershipColumn from './hocs/withFormsPortalDealershipColumn';

// Permissions
import {
  hasEContractingEditPermission,
  hasViewOnlyFormConfigurator,
  hasFormMigrationEnabled,
  hasEditGlobalFieldsPermission,
  hasAddVisibilityTargettingPermission,
  hasFormRestorePermission,
  hasPublishFormPermission,
  hasSaveFormPermission,
  hasFormPortalLibraryViewPermission,
  hasEditTekionRestrictedFieldsPermission,
  hasEditDPRestrictedFieldsPermission,
  hasFormArchivePermission,
  hasCreateFormPermission,
  hasViewFormRequestPermission,
} from 'permissions/formConfigurator.permissions';

// Utils
import { getValidAndSortedUserOptions } from '@tekion/tekion-base/utils/formConfigurator/utils';
import {
  getFormsProAuditFormatters,
  getAuditFilters,
  canEditEcontractingForms,
} from '@tekion/tekion-business/src/utils/formConfigurator/formConfigurator.utils';
import {
  getDynamicUsageCategoryVsRulesOptions,
  getOptionsForCategories,
} from '@tekion/tekion-widgets/src/organisms/AddForm/AddForm.utils';
import {
  getColumnConfig,
  getFilterProps,
  makeLicenseProviderKeys,
  getTableProps,
  getTenantValues,
  getDealerValues,
  getSelectedFormData,
  getFormattedFilters,
  getFilterPropsConfig,
  additionalAttrFormatters,
  handleHideTooltip,
  getParsedFormList,
} from './utils/FormsPortalDealership.utils';
import { getFormProgrammingType } from '../../utils';
import * as ValidateFormUtils from '@tekion/tekion-widgets/src/organisms/formConfiguratorTool/utils/validateForm.utils';
import { isFCEditFlow } from 'utils';

// Contexts
import { Consumer } from '@tekion/tekion-widgets/src/organisms/formConfiguratorTool/formConfiguratorTool.context';

// Styles
import styles from './formsPortalDealership.module.scss';

const BODY_STYLE = { width: '88.8rem!important', height: '20.8rem' };

class FormsPortalDealership extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      formValues: {},
    };
  }

  componentDidMount() {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_PAGE_MOUNT,
    });
  }

  componentDidUpdate(prevProps) {
    const { onAction, isDealerTrackEnabled } = this.props;
    if (isDealerTrackEnabled !== prevProps.isDealerTrackEnabled) {
      onAction({
        type: ACTION_TYPES.GET_DOCUMENT_TYPE,
      });
    }
  }

  getTableProps = defaultMemoize(getTableProps);

  getSubHeaderProps = ({ count, selection, formsArchivePermission }) => {
    const { isFCEditFlow } = this.props;
    return {
      subHeaderLeftActions: [
        {
          renderer: Content,
          renderOptions: {
            children: `${count} Results`,
          },
        },
      ],
      subHeaderRightActions: isFCEditFlow && [
        {
          renderer: DropDown,
          renderOptions: {
            overlay: (
              <Menu onClick={this.handleMigrateForms}>
                {_map(ACTION_MENU_ITEMS, ({ label, value }) => (
                  <Menu.Item key={value} disabled={_isEmpty(selection) || !hasFormMigrationEnabled()}>
                    {label}
                  </Menu.Item>
                ))}
              </Menu>
            ),
            trigger: ['click'],
            children: (
              <Button view={Button.VIEW.SECONDARY} className="d-flex justify-content-center align-items-center m-r-4">
                {__('Actions')}
                &nbsp;
                <FontIcon size={12} className="m-l-8">
                  icon-chevron-down
                </FontIcon>
              </Button>
            ),
          },
        },
        {
          renderer: KebabMenu,
          renderOptions: {
            menuItems: [
              {
                key: 'FORMS_ARCHIVE',
                renderer: withProps({ icon: 'icon-archive2', textContent: __('Archive') })(IconWithText),
                disabled: !formsArchivePermission,
                className: cx({ [styles.disabledField]: !formsArchivePermission }),
              },
            ],
            triggerElement: <div className={cx('icon-overflow cursor-pointer p-12', styles.header_kebab_wrapper)} />,
            onClickAction: this.showArchivedFormsModal,
          },
        },
      ],
    };
  };

  showFormSettings = payload => {
    const { onAction } = this.props;
    onAction({
      type: TABLE_ACTION_TYPES.TABLE_ITEM_CLICK,
      payload,
    });
  };

  showPreviewModel = formKey => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.SHOW_PREVIEW_MODEL,
      payload: {
        formKey,
      },
    });
  };

  closePreviewModel = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.CLOSE_PREVIEW_MODEL,
    });
  };

  getRowActionCell = ({ original: columnData }) => {
    const { formCategories, metaData, isFCEditFlow } = this.props;
    return (
      <KebabActionMenu
        data={columnData[COLUMNS.ACTION]}
        columnData={columnData}
        testForm={this.testForm}
        editForm={this.editForm}
        requestEditForm={this.requestEditForm}
        copyForm={this.showCopyFormModal}
        showVersionHistory={this.showVersionHistoryModal}
        showAuditLogs={this.showAuditLogsDrawer}
        archiveForm={this.showArchiveConfirmationDialog}
        formCategories={formCategories}
        dynamicUsageCategoryVsRules={this.getDynamicOptionsForTargeting()}
        metadata={metaData}
        downloadFormPdf={this.downloadFormPdf}
        isFCEditFlow={isFCEditFlow}
        showFormSettings={this.showFormSettings}
        showPreviewModel={this.showPreviewModel}
      />
    );
  };

  getColumnConfigurator = () => {
    const { columnConfigurator } = this.props;
    return {
      ...columnConfigurator,
      Cell: this.getRowActionCell,
    };
  };

  getFormType = rowData => {
    const form = _get(rowData, 'original');

    const formType = getFormProgrammingType(form);

    if (formType === NO_DATA) return NO_DATA;

    return FORM_TYPE_DISPLAY_NAME[formType];
  };

  getColumns = defaultMemoize(
    (
      lenderTypes,
      categories,
      formCategories,
      siteOptions,
      getFormattedDateAndTime,
      resolvedUsers,
      paymentOptionConfigs,
      legalProviders
    ) => {
      const { columns, columnMetaData, globalMetaData, onAction } = this.props;
      const getColumnConfigurator = defaultMemoize(this.getColumnConfigurator);
      return _map(
        getColumnsByConfig({
          columns,
          columnMetaData,
          columnConfigurator: getColumnConfigurator(),
          config: getColumnConfig({
            lenderTypes,
            categories,
            formCategories,
            siteOptions,
            getFormattedDateAndTime,
            resolvedUsers,
            paymentOptionConfigs,
            legalProviders,
            columnConfigurator: getColumnConfigurator(),
            dynamicUsageCategoryVsRules: this.getDynamicOptionsForTargeting(),
            globalMetaData,
            onAction,
            getFormType: this.getFormType,
          }),
        }),
        column => ({
          ...column,
          width: COLUMN_MIN_WIDTH[column.accessor] || column?.width,
        })
      );
    }
  );

  getCategories = defaultMemoize(metadata => _keyBy(_get(metadata, 'category'), 'key'));

  getFilterProps = defaultMemoize(getFilterProps);

  getSections = () => [
    {
      rows: [
        {
          columns: [FORM_FIELDS.TENANT_ID, FORM_FIELDS.DEALER_ID],
        },
      ],
    },
  ];

  getFields = (tenantIds, dealerOptions) => ({
    [FORM_FIELDS.TENANT_ID]: {
      renderer: SelectInput,
      renderOptions: {
        required: true,
        label: __('Tenant Id'),
        placeholder: __('Select Tenant'),
        options: getTenantValues(tenantIds),
      },
    },
    [FORM_FIELDS.DEALER_ID]: {
      renderer: SelectInput,
      renderOptions: {
        required: true,
        label: __('Dealer'),
        placeholder: __('Select Dealer'),
        options: getDealerValues(dealerOptions),
      },
    },
  });

  onCreateFormClick = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_CREATE_FORM_CLICK,
    });
  };

  getTableComponent = withCheckboxTable(FixedColumnTable);

  toggleModal = (activeFormId = '') => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_TOGGLE_ADD_FORM_MODAL,
      payload: { activeFormId },
    });
  };

  addNewForm = async formData => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_ADD_FORM,
      payload: { formData },
    });
  };

  updateForm = async formData => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_UPDATE_FORM,
      payload: { formData },
    });
  };

  testForm = formId => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_FORM_TEST,
      payload: { formId },
    });
  };

  copyForm = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_COPY_FORM,
    });
  };

  showCopyFormModal = (formId, formName) => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.SHOW_CREATE_COPY_MODAL,
      payload: { formId, formName },
    });
  };

  editForm = formId => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_FORM_EDIT,
      payload: { formId },
    });
  };

  requestEditForm = formId => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_FORM_REQUEST_EDIT,
      payload: { formId },
    });
  };

  handleCloseEditFormRequest = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_FORM_REQUEST_EDIT_CLOSE,
    });
  };

  onFormRequestConfirmation = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_REQUEST_FORM_CONFIRMATION,
    });
  };

  onFormRequestConfirmationClose = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_REQUEST_FORM_CONFIRMATION_CLOSE,
    });
  };

  downloadFormPdf = formKey => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.DOWNLOAD_FORM_PDF,
      payload: { formKey },
    });
  };

  handleOnFormNameChange = event => {
    const { value } = event.target;
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_COPY_FORM_NAME_CHANGE,
      payload: { copyFormNewName: value },
    });
  };

  handleMigrateForms = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_MIGRATE_FORM_CLICK,
    });
  };

  handleCloseMigrateForms = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_CLOSE_MIGRATE_FORM,
    });
    this.setState({ formValues: {} });
  };

  handleCloseMigrationResponse = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_CLOSE_MIGRATION_RESPONSE,
    });
    this.setState({ formValues: {} });
  };

  handleShowMigrationResponse = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_MIGRATE_FORM_CLICK,
    });
  };

  onSubmit = async () => {
    const { selectedFormKeys } = this.props;
    const { formValues } = this.state;
    const { tenantId, dealer } = formValues;
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_MIGRATION_FORM_SUBMIT,
      payload: {
        forms: selectedFormKeys,
        targetTenantId: _head(tenantId),
        targetDealerId: _head(dealer),
      },
    });
  };

  handleFormArchive = ({ key: formKey, name: formName }) => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_ARCHIVE_FORM,
      payload: { formKey, formName },
    });
  };

  handleArchivedFormRestore = ({ key: formKey, name: formName }) => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_RESTORE_ARCHIVED_FORM,
      payload: { formKey, formName },
    });
  };

  hideCreateCopyConfirmationDialog = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.HIDE_CREATE_COPY_MODAL,
    });
  };

  showVersionHistoryModal = (formKey, formName) => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.SHOW_VERSION_HISTORY_MODAL,
      payload: { formKey, formName },
    });
  };

  hideVersionHistoryModal = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.HIDE_VERSION_HISTORY_MODAL,
    });
  };

  hideRestoreConfirmationDialog = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.HIDE_RESTORE_VERSION_MODAL,
    });
  };

  restoreFormVersion = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_FORM_VERSION_RESTORE_AND_PUBLISH,
    });
  };

  showAuditLogsDrawer = formKey => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.SHOW_AUDIT_LOGS_DRAWER,
      payload: { formKey },
    });
  };

  toggleAuditLogs = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.TOGGLE_AUDIT_LOGS,
    });
  };

  showArchivedFormsModal = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.SHOW_ARCHIVED_FORMS_MODAL,
    });
  };

  hideArchivedFormsModal = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.HIDE_ARCHIVED_FORMS_MODAL,
    });
  };

  showArchiveConfirmationDialog = (formId, formName) => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.SHOW_ARCHIVE_CONFIRMATION_MODAL,
      payload: { formId, formName },
    });
  };

  hideArchiveConfirmationModal = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.HIDE_ARCHIVE_CONFIRMATION_MODAL,
    });
  };

  getAuditTabs = (filters, assetId) => [
    {
      id: AUDIT_ID,
      label: AUDITS_LABEL,
      filterTypes: getAuditFilters(filters),
      assetType: AUDIT_ID,
      assetId,
    },
  ];

  renderCellByColumnKey =
    column =>
    ({ original: columnData }) => {
      const { formCategories, metaData } = this.props;
      let cell = null;
      const Component = _get(COLUMN_RENDERERS, column.accessor, DefaultContent);
      switch (_get(column, 'accessor')) {
        case COLUMNS.NAME:
        case COLUMNS.PDF_NAME:
          cell = (
            <Ellipsis length={40} tooltip>
              {columnData[column.accessor]}
            </Ellipsis>
          );
          break;

        default:
          cell = (
            <Component
              data={columnData[column.accessor]}
              formatter={column.formatter}
              getter={column.getter}
              columnData={columnData}
              testForm={this.testForm}
              editForm={this.editForm}
              copyForm={this.showCopyFormModal}
              showVersionHistory={this.showVersionHistoryModal}
              showAuditLogs={this.showAuditLogsDrawer}
              archiveForm={this.showArchiveConfirmationDialog}
              formCategories={formCategories}
              dynamicUsageCategoryVsRules={this.getDynamicOptionsForTargeting()}
              metadata={metaData}
            />
          );
      }
      return cell;
    };

  renderHeader = () => {
    const { navigate, isFCEditFlow } = this.props;
    const hasGlobalEdit = hasEditGlobalFieldsPermission();
    const hasDPRestrictedEdit = hasEditDPRestrictedFieldsPermission();
    const hasTekionRestrictedEdit = hasEditTekionRestrictedFieldsPermission();
    const portalLibraryView = hasFormPortalLibraryViewPermission();
    const createPermission = hasCreateFormPermission();
    const viewFormRequest = hasViewFormRequestPermission();

    return (
      <PageHeader>
        <div className="full-width d-flex justify-content-between align-items-center">
          <div className="d-flex justify-content-center align-items-center">
            {(portalLibraryView || hasGlobalEdit || hasDPRestrictedEdit || hasTekionRestrictedEdit) && (
              <Sidebar navigate={navigate} />
            )}

            <Heading size="1">{__('Forms Portal')} </Heading>
          </div>

          <div className="d-flex justify-content-center align-items-center">
            {isFCEditFlow && (
              <Button
                className="m-r-24"
                disabled={!createPermission}
                view={Button.VIEW.PRIMARY}
                onClick={this.onCreateFormClick}>
                {__('Create Form')}
              </Button>
            )}
          </div>
        </div>
      </PageHeader>
    );
  };

  handleAction = action => {
    const { type, payload = EMPTY_OBJECT } = action;
    const { value: selection } = payload;
    const { onAction, isFCEditFlow } = this.props;
    if (!isFCEditFlow && type === TABLE_ACTION_TYPES.TABLE_ITEM_CLICK) {
      onAction({
        type: ACTION_TYPES.ON_FORM_EDIT,
        payload: { formId: _get(selection, 'original.formKey') },
      });
      return;
    }
    if (type === TABLE_ACTION_TYPES.TABLE_ITEM_SELECT) {
      onAction({
        type: ACTION_TYPES.ON_SELECT_FORM,
        payload: { selection },
      });
    } else {
      onAction(action);
    }
  };

  handleMigrateAction = async ({ type, payload }) => {
    const { onAction } = this.props;
    let updatedVal;
    if (type === actionTypes.ON_FIELD_CHANGE) {
      const { id, value } = payload;
      const { formValues } = this.state;
      updatedVal = { ...formValues, [id]: value };
      this.setState({ formValues: updatedVal });
      if (id === 'tenantId') {
        updatedVal = { [id]: value };
        this.setState({ formValues: updatedVal });
        onAction({
          type: ACTION_TYPES.SHOW_MIGRATE_DEALERS,
          payload: { value },
        });
      }
    }
  };

  getDynamicOptionsForTargeting = () => {
    const {
      fniOptions,
      dueBills,
      fuelTypes,
      dealTypeConfigs,
      vehicleTypes,
      customStatus,
      models,
      trimsList,
      makes,
      tekionMakes,
      stateListOptions,
      dealStatus,
      lenderTypes,
      siteOptions,
      paymentOptionConfigs,
      tradeInCount,
    } = this.props;
    return getDynamicUsageCategoryVsRulesOptions({
      fnIs: fniOptions,
      dueBills,
      states: stateListOptions,
      makes,
      tekionMakes,
      models,
      fuelTypes,
      lenderTypes,
      dealTypeConfigs,
      vehicleTypes,
      customStatus,
      siteOptions,
      trimsList,
      paymentOptionConfigs,
      dealStatus,
      tradeInCount,
    });
  };

  // eslint-disable-next-line react/sort-comp
  renderBody() {
    const {
      navigate,
      location,
      printerTypes,
      docTypes,
      metaData,
      lenderTypes,
      formCategories,
      allDealershipUsers,
      siteOptions,
      currentPage,
      count,
      formsList,
      pageLength,
      loading,
      sortDetails,
      selectedFilters,
      selectedFilterGroup,
      contentHeight,
      selection,
      getFormattedDateAndTime,
      resolvedUsers,
      paymentOptionConfigs,
      legalProviders,
      searchField,
      globalMetaData,
      labelsInfo,
    } = this.props;
    const categories = this.getCategories(metaData);
    const filterProps = this.getFilterProps(FILTER_PROPS, {
      formCategories,
      selectedFilters,
      printerTypes,
      allDealershipUsers,
      selectedFilterGroup,
      docTypes,
      globalMetaData,
      labelsInfo,
    });
    const draftFormIds = _map(
      _filter(formsList, form => form.formState === FORM_STATE.DRAFT),
      form => form.id
    );
    const tableProps = this.getTableProps(selection, count, currentPage, pageLength, loading, draftFormIds);
    const tableColumns = this.getColumns(
      lenderTypes,
      categories,
      formCategories,
      siteOptions,
      getFormattedDateAndTime,
      resolvedUsers,
      paymentOptionConfigs,
      legalProviders
    );

    const formsArchivePermission = hasFormArchivePermission();

    return (
      <div style={{ height: contentHeight }}>
        <TableManager
          navigate={navigate}
          location={location}
          tableManagerProps={TABLE_MANAGER_PROPS}
          onAction={this.handleAction}
          data={getParsedFormList(formsList)}
          columns={tableColumns}
          tableProps={tableProps}
          headerProps={HEADER_PROPS}
          subHeaderProps={this.getSubHeaderProps({
            count,
            selection,
            formsArchivePermission,
          })}
          sortDetails={sortDetails}
          filterProps={filterProps}
          tableComponent={this.getTableComponent}
          searchField={searchField}
          searchableFieldsOptions={SEARCHABLE_FIELDS_OPTIONS}
          isMultiSort
        />
      </div>
    );
  }

  renderCopyPromptContent = () => {
    const { copyFormNewName, isValidFormName, copyFormDetails } = this.props;
    const { formName } = copyFormDetails || EMPTY_OBJECT;

    let errorMessage = '';
    if (!isValidFormName) {
      errorMessage = ERRORS.INVALID_FORM_NAME;
    }
    return (
      <div>
        <Heading size={5} className={styles.copyFormName}>
          {formName}
        </Heading>
        <Input
          label={__('New Form Name')}
          required
          value={copyFormNewName}
          onChange={this.handleOnFormNameChange}
          errorInPopover={!isValidFormName}
          error={errorMessage}
        />
      </div>
    );
  };

  renderRestoreConfirmationDialogContent = () => (
    <div className="d-flex flex-column">
      <Content className="font-weight-bold p-b-12">{__('Are you sure?')}</Content>
      <Content className={styles.restoreModalTextWrapper}>
        {__('The restored form will override the existing form.')}
      </Content>
      <IconWithText
        textContent={__('The previous version can be accessed in the Version History.')}
        icon="icon-information-filled"
        iconClassName={styles.infoIconClassName}
        iconSize={SIZES.S}
        className={styles.iconWithTextWrapper}
      />
    </div>
  );

  renderAuditLogs = () => {
    const { showAuditLogs, assetId } = this.props;
    return (
      <AuditLogs
        visible={showAuditLogs}
        onClose={this.toggleAuditLogs}
        tabs={this.getAuditTabs(FORMS_AUDITS_FILTER_TYPES, assetId)}
        attributeVsFormatter={additionalAttrFormatters(getFormsProAuditFormatters())}
        customDefaultAuditTypeConfig={AUDIT_FALLBACK_CONFIG}
        hideTooltip={handleHideTooltip}
      />
    );
  };

  handleClosePreviewSidebar = () => {
    const { onAction } = this.props;

    onAction({
      type: ACTION_TYPES.HIDE_FORM_PREVIEW_MODEL,
    });
  };

  handleSubmitPreviewSidebar = () => {
    const { onAction } = this.props;

    onAction({
      type: ACTION_TYPES.ON_PREVIEW_SUBMIT,
    });
  };

  handleLibrarySearchCloseIcon = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.HIDE_LIBRARY_SEARCH_HEADER,
    });
  };

  navigateToFormsLibraryView = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.NAVIGATE_TO_FORMS_LIBRARY_VIEW,
    });
  };

  renderLibraryCount = () => {
    const { libraryFormCount, showLibrarySearchHeader, searchText } = this.props;
    return (
      libraryFormCount > 0 &&
      searchText &&
      showLibrarySearchHeader && (
        <LibraryCountSearch
          count={libraryFormCount}
          searchText={searchText}
          handleClose={this.handleLibrarySearchCloseIcon}
          handleNavigation={this.navigateToFormsLibraryView}
        />
      )
    );
  };

  getSaveFormDetailsPayload = () => {
    const { previewModelData } = this.props;
    const configs = !_isEmpty(previewModelData?.configs)
      ? ValidateFormUtils.getParsedConfigDataFromFormDetails(previewModelData.configs)
      : ValidateFormUtils.addNewConfiguration([]);

    const previewFieldsForValidateForm = !_isEmpty(previewModelData.fields)
      ? ValidateFormUtils.addIndexAsIdToFields(previewModelData.fields)
      : ValidateFormUtils.addNewRow([]);
    const labels = previewModelData.labels || EMPTY_ARRAY;
    const configsForPayload = _map(configs, config => {
      const parsedData = {
        mergeAllConfig: config.mergeAllConfig,
      };
      _forEach(config.groupedFieldConfigs || [], ({ configFieldType, id, ...rest }) => {
        if (configFieldType) {
          parsedData[configFieldType] = rest;
        }
      });
      return parsedData;
    });
    let fieldsForPayload = _filter(
      _forEach(previewFieldsForValidateForm || []),
      ({ pdfKey, displayName }) => pdfKey || displayName
    );
    fieldsForPayload = _map(fieldsForPayload, ({ options, ...rest }) => ({
      ...rest,
      filedName: _get(rest, 'pdfKey'),
      options: _filter(options || [], ({ pdfKey, display }) => pdfKey || display),
    }));
    return {
      configs: configsForPayload,
      fields: fieldsForPayload,
      labels,
    };
  };

  onApplyAndValidate = ({ fields, validatePreviewId, validatePreviewBy, requestId, dealersListforPreview }) => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.ON_APPLY_AND_VALIDATE,
      payload: { fields, validatePreviewId, validatePreviewBy, requestId, dealersListforPreview },
    });
  };

  render() {
    const CopyPromptContent = this.renderCopyPromptContent;
    const {
      metaData,
      legalProviders,
      showModal,
      showCopyConfirmationDialog,
      isFormSubmitInProgress,
      isCopyingForm,
      showVersionHistoryModal,
      onAction,
      selectedFormVersion,
      showRestoreConfirmationDialog,
      isLoadingFormVersionRestore,
      showMigrateModal,
      tenantIds,
      selection,
      showResponseModal,
      dealerOptions,
      migrationResponseData,
      allDealershipUsers,
      formToDelete,
      formToArchiveName,
      fetchArchivedFormsList,
      resolvedUsers,
      showFormPreviewModel,
      previewModelData,
      showRequestEditForm,
      showRequestFormConfirmation,
      showPreviewModel,
      formIdForValidateForm,
      formDetails,
      isFCEditFlow,
      ...rest
    } = this.props;
    const { formValues } = this.state;
    const { activeFormId, formsList, lenderTypes, formCategories, siteOptions } = this.props;
    const selectedFormData = getSelectedFormData(formsList, activeFormId);
    const categories = this.getCategories(metaData);
    const categoryOptions = getOptionsForCategories(metaData);
    const numberOfForms = _size(selection);
    const archivedModalColumns = _filter(
      this.getColumns(lenderTypes, categories, formCategories, siteOptions, resolvedUsers),
      ({ accessor }) => _includes(ARCHIVE_MODAL_COLUMN_IDS, accessor)
    );

    const dynamicUsageCategoryVsRules = this.getDynamicOptionsForTargeting();
    const customAnnotations = _uniqBy(previewModelData?.fields, 'filedName');
    const fieldDisplaySections = previewModelData?.fieldDisplaySections;
    const originalFileName = formDetails?.originalFileName;
    return (
      <Fragment>
        {this.renderHeader()}
        {this.renderLibraryCount()}
        {this.renderBody()}
        <PreviewModel
          showFormPreviewModel={showFormPreviewModel}
          previewModelData={previewModelData}
          handleClose={this.handleClosePreviewSidebar}
          handleSubmit={this.handleSubmitPreviewSidebar}
          disableCart
        />
        {isFCEditFlow && showModal && (
          <AddFormModal
            requiredFields={REQUIRED_FORM_FIELDS}
            showRequiredFields
            showModal={showModal}
            toggleModal={this.toggleModal}
            addNewForm={this.addNewForm}
            updateForm={this.updateForm}
            categories={categoryOptions}
            dynamicUsageCategoryVsRules={dynamicUsageCategoryVsRules}
            licensedByKeys={makeLicenseProviderKeys(legalProviders)}
            hasFormConfiguratorView={
              hasViewOnlyFormConfigurator() ||
              !canEditEcontractingForms(selectedFormData, hasEContractingEditPermission())
            }
            hasEditGlobalFieldsPermission={hasEditGlobalFieldsPermission()}
            hasAddVisibilityTargettingPermission={hasAddVisibilityTargettingPermission()}
            hasEContractingEditPermission={hasEContractingEditPermission()}
            isSaveDisabled={!hasPublishFormPermission() && !hasSaveFormPermission()}
            {...this.props}
          />
        )}
        <ConfirmationDialog
          title={__('Create a form copy')}
          isVisible={showCopyConfirmationDialog}
          width={Modal.SIZES.SM}
          destroyOnClose
          onCancel={this.hideCreateCopyConfirmationDialog}
          onSubmit={this.copyForm}
          secondaryBtnText={__('Cancel')}
          submitBtnText={__('Copy')}
          okButtonProps={{
            loading: isCopyingForm,
          }}
          content={<CopyPromptContent />}
        />
        <VersionHistoryModal
          onAction={onAction}
          showModal={showVersionHistoryModal}
          onClose={this.hideVersionHistoryModal}
          selectedFormVersion={selectedFormVersion}
          allDealershipUsers={getValidAndSortedUserOptions(allDealershipUsers)}
          {...rest}
        />
        <ConfirmationDialog
          title={__('Restore & Publish')}
          isVisible={showRestoreConfirmationDialog}
          width={Modal.SIZES.SM}
          destroyOnClose
          onCancel={this.hideRestoreConfirmationDialog}
          onSubmit={this.restoreFormVersion}
          secondaryBtnText={__('Cancel')}
          submitBtnText={__('Publish')}
          content={this.renderRestoreConfirmationDialogContent()}
          okButtonProps={{
            loading: isLoadingFormVersionRestore,
          }}
        />
        <Modal
          onCancel={this.handleCloseMigrateForms}
          onSubmit={this.onSubmit}
          secondaryBtnText={__('Cancel')}
          submitBtnText={__('Migrate')}
          title={`Migrate ${numberOfForms} ${numberOfForms === 1 ? 'Form' : 'Forms'}`}
          visible={showMigrateModal}
          width={Modal.SIZES.MD}
          bodyStyle={BODY_STYLE}
          destroyOnClose
          primaryBtnDisabled={_size(formValues) !== 2}>
          <div>
            <div>
              <FormBuilder
                values={formValues}
                fields={this.getFields(tenantIds, dealerOptions)}
                sections={this.getSections()}
                onAction={this.handleMigrateAction}
              />
            </div>
          </div>
        </Modal>
        <MigrationResponseModal
          showResponseModal={showResponseModal}
          onCloseMigrationResponse={this.handleCloseMigrationResponse}
          migrationResponseData={migrationResponseData}
        />
        <div>{this.renderAuditLogs()}</div>
        <ArchivedFormsModal
          modalViewType={ARCHIVE_MODAL_VIEW_TYPES.FORMS_CONFIGURATOR}
          additionalColumns={archivedModalColumns}
          getFilterProps={getFilterPropsConfig}
          getFiltersPayload={getFormattedFilters}
          fetchFormsList={fetchArchivedFormsList}
          disableRestoreAction={!hasFormRestorePermission() && !hasEditGlobalFieldsPermission()}
          onFormRestore={this.handleArchivedFormRestore}
        />
        <ArchiveConfirmationDialog onArchive={this.handleFormArchive} />
        <ConfirmationDialog
          title={__('Confirm')}
          isVisible={showRequestFormConfirmation}
          width={Modal.SIZES.SM}
          destroyOnClose
          onCancel={this.onFormRequestConfirmationClose}
          onSubmit={this.onFormRequestConfirmation}
          secondaryBtnText={__('Cancel')}
          submitBtnText={__('Yes')}
          content={__(
            'An additional $220 per form charge shall apply if Tekion evaluates the request as a new custom form or a custom change request. Do you wish to proceed?'
          )}
        />
        <ModalWithIframe visible={showRequestEditForm} onClose={this.handleCloseEditFormRequest} />

        {showPreviewModel && (
          <Consumer>
            {({ previewUrls }) => (
              <ValidateFormModal
                showModal={showPreviewModel}
                previewUrls={previewUrls}
                toggleModal={this.closePreviewModel}
                fields={customAnnotations || EMPTY_ARRAY}
                formId={formIdForValidateForm}
                fieldDisplaySections={fieldDisplaySections}
                className={styles.validateForm}
                formName={`${originalFileName}.pdf`}
                isDisableFormFields={hasViewOnlyFormConfigurator()}
                getFormDetailsPayload={this.getSaveFormDetailsPayload}
                onApplyAndValidate={this.onApplyAndValidate}
              />
            )}
          </Consumer>
        )}

        {!isFCEditFlow && showModal && (
          <AddFormDrawer
            requiredFields={REQUIRED_FORM_FIELDS}
            showRequiredFields
            showModal={showModal}
            toggleModal={this.toggleModal}
            addNewForm={this.addNewForm}
            updateForm={this.updateForm}
            categories={categoryOptions}
            dynamicUsageCategoryVsRules={dynamicUsageCategoryVsRules}
            licensedByKeys={makeLicenseProviderKeys(legalProviders)}
            hasFormConfiguratorView={
              hasViewOnlyFormConfigurator() ||
              !canEditEcontractingForms(selectedFormData, hasEContractingEditPermission())
            }
            hasEditGlobalFieldsPermission={hasEditGlobalFieldsPermission()}
            hasAddVisibilityTargettingPermission={hasAddVisibilityTargettingPermission()}
            hasEContractingEditPermission={hasEContractingEditPermission()}
            isSaveDisabled={!hasPublishFormPermission() && !hasSaveFormPermission()}
            {...this.props}
          />
        )}
      </Fragment>
    );
  }
}

FormsPortalDealership.propTypes = {
  printerTypes: PropTypes.array,
  lenderTypes: PropTypes.array,
  metaData: PropTypes.object,
  legalProviders: PropTypes.array,
  allDealershipUsers: PropTypes.array,
  formCategories: PropTypes.array.isRequired,
  siteOptions: PropTypes.array.isRequired,
  showModal: PropTypes.bool,
  activeFormId: PropTypes.bool,
  copyFormNewName: PropTypes.string,
  currentPage: PropTypes.number,
  count: PropTypes.number,
  pageLength: PropTypes.number,
  formsList: PropTypes.array,
  loading: PropTypes.bool,
  onAction: PropTypes.func,
  selectedFilters: PropTypes.array,
  selectedFilterGroup: PropTypes.array,
  sortDetails: PropTypes.object,
  copyFormDetails: PropTypes.object,
  contentHeight: PropTypes.number,
  showCopyConfirmationDialog: PropTypes.bool,
  isFormSubmitInProgress: PropTypes.bool,
  isValidFormName: PropTypes.bool,
  isCopyingForm: PropTypes.bool,
  showVersionHistoryModal: PropTypes.bool,
  formName: PropTypes.string,
  selectedFormVersion: PropTypes.string,
  formsVersionList: PropTypes.array,
  showRestoreConfirmationDialog: PropTypes.bool,
  formKey: PropTypes.string,
  isLoadingFormVersionRestore: PropTypes.bool,
  onChange: PropTypes.func,
  showMigrateModal: PropTypes.bool,
  tenantIds: PropTypes.array,
  selection: PropTypes.array,
  showResponseModal: PropTypes.bool,
  dealerOptions: PropTypes.array,
  selectedFormKeys: PropTypes.array,
  migrationResponseData: PropTypes.object,
  formToDelete: PropTypes.string,
  showAuditLogs: PropTypes.bool,
  assetId: PropTypes.string,
  formToArchiveName: PropTypes.string,
  fetchArchivedFormsList: PropTypes.func,
  getFormattedDateAndTime: PropTypes.func,
  resolvedUsers: PropTypes.object,
  isDealerTrackEnabled: PropTypes.bool,
  paymentOptionConfigs: PropTypes.array,
  docTypes: PropTypes.array,
  fniOptions: PropTypes.array,
  dueBills: PropTypes.array,
  fuelTypes: PropTypes.array,
  dealTypeConfigs: PropTypes.array,
  vehicleTypes: PropTypes.array,
  customStatus: PropTypes.array,
  models: PropTypes.array,
  trimsList: PropTypes.array,
  makes: PropTypes.array,
  tekionMakes: PropTypes.array,
  stateListOptions: PropTypes.array,
  dealStatus: PropTypes.array,
  columnConfigurator: PropTypes.object.isRequired,
  searchField: PropTypes.string,
  columns: PropTypes.array,
  columnMetaData: PropTypes.object,
  globalMetaData: PropTypes.object,
  tradeInCount: PropTypes.number,
  labelsInfo: PropTypes.array,
  navigate: PropTypes.func.isRequired,
  location: PropTypes.object.isRequired,
  libraryFormCount: PropTypes.number,
  showLibrarySearchHeader: PropTypes.bool,
  showPreviewModel: PropTypes.bool,
};

FormsPortalDealership.defaultProps = {
  printerTypes: EMPTY_ARRAY,
  lenderTypes: EMPTY_ARRAY,
  metaData: EMPTY_OBJECT,
  legalProviders: EMPTY_ARRAY,
  allDealershipUsers: EMPTY_ARRAY,
  pageLength: DEFAULT_PAGE_SIZE,
  showModal: false,
  activeFormId: EMPTY_STRING,
  copyFormNewName: EMPTY_STRING,
  currentPage: 1,
  count: 0,
  formsList: EMPTY_ARRAY,
  loading: false,
  onAction: _noop,
  selectedFilters: EMPTY_ARRAY,
  selectedFilterGroup: DEFAULT_FILTER_GROUP,
  sortDetails: EMPTY_OBJECT,
  copyFormDetails: EMPTY_OBJECT,
  contentHeight: 0,
  showCopyConfirmationDialog: false,
  isFormSubmitInProgress: false,
  isValidFormName: true,
  isCopyingForm: false,
  showVersionHistoryModal: false,
  formName: EMPTY_STRING,
  selectedFormVersion: EMPTY_STRING,
  formsVersionList: EMPTY_ARRAY,
  showRestoreConfirmationDialog: false,
  formKey: EMPTY_STRING,
  isLoadingFormVersionRestore: false,
  onChange: _noop,
  showMigrateModal: false,
  tenantIds: EMPTY_ARRAY,
  selection: EMPTY_ARRAY,
  showResponseModal: false,
  dealerOptions: EMPTY_ARRAY,
  selectedFormKeys: EMPTY_ARRAY,
  migrationResponseData: EMPTY_OBJECT,
  formToDelete: EMPTY_STRING,
  showAuditLogs: false,
  assetId: EMPTY_STRING,
  formToArchiveName: EMPTY_STRING,
  fetchArchivedFormsList: _noop,
  getFormattedDateAndTime: _noop,
  resolvedUsers: EMPTY_OBJECT,
  isDealerTrackEnabled: false,
  paymentOptionConfigs: EMPTY_ARRAY,
  docTypes: EMPTY_ARRAY,
  fniOptions: EMPTY_ARRAY,
  dueBills: EMPTY_ARRAY,
  fuelTypes: EMPTY_ARRAY,
  dealTypeConfigs: EMPTY_ARRAY,
  vehicleTypes: EMPTY_ARRAY,
  customStatus: EMPTY_ARRAY,
  models: EMPTY_ARRAY,
  trimsList: EMPTY_ARRAY,
  makes: EMPTY_ARRAY,
  tekionMakes: EMPTY_ARRAY,
  stateListOptions: EMPTY_ARRAY,
  dealStatus: EMPTY_ARRAY,
  searchField: SEARCH_OPTION_IDS.ALL,
  columns: EMPTY_ARRAY,
  columnMetaData: EMPTY_OBJECT,
  globalMetaData: EMPTY_OBJECT,
  tradeInCount: 2,
  labelsInfo: EMPTY_ARRAY,
  libraryFormCount: 0,
  showLibrarySearchHeader: false,
  showPreviewModel: false,
};

export default compose(withFormsPortalDealershipColumn)(FormsPortalDealership);
