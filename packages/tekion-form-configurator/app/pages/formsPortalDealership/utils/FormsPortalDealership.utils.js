import React from 'react';
import { mapProps } from 'recompose';
import { defaultMemoize } from 'reselect';
import _isEmpty from 'lodash/isEmpty';
import _find from 'lodash/find';
import _values from 'lodash/values';
import _compact from 'lodash/compact';
import _size from 'lodash/size';
import _split from 'lodash/split';
import _get from 'lodash/get';
import _set from 'lodash/set';
import _uniqBy from 'lodash/uniqBy';
import _forEach from 'lodash/forEach';
import _map from 'lodash/map';
import _concat from 'lodash/concat';
import _filter from 'lodash/filter';
import _reduce from 'lodash/reduce';
import _keyBy from 'lodash/keyBy';
import _keys from 'lodash/keys';
import _omit from 'lodash/omit';
import _slice from 'lodash/slice';
import _difference from 'lodash/difference';
import _last from 'lodash/last';
import _trim from 'lodash/trim';
import _head from 'lodash/head';
import _startCase from 'lodash/startCase';
import _includes from 'lodash/includes';
import _join from 'lodash/join';

import { EMPTY_ARRAY, EMPTY_OBJECT, NO_DATA } from '@tekion/tekion-base/app.constants';
import {
  USAGE_CATEGORY,
  USAGE_RULES_LABEL,
  VEHICLE_TYPES,
  FORM_SOURCE,
  USAGE_TARGETTING_MAKEID,
} from '@tekion/tekion-base/constants/deal/formSetup';
import OPERATORS from '@tekion/tekion-base/constants/filterOperators';

import {
  DEFAULT_SEARCH_FILTER,
  FORM_MIGRATED_FILTER_OPTIONS,
  MODULE_TARGETTING_LABELS,
  MODULE_TARGETTING_TYPES,
} from '@tekion/tekion-base/constants/formConfigurator/constants';
import { PRINTER_TYPES_LABEL } from '@tekion/tekion-base/constants/formConfigurator/printers';
import MODULE_ASSET_TYPES from '@tekion/tekion-base/constants/moduleAssetTypes';
import { MARKET_CLASS_CATEGORY_OPTIONS } from '@tekion/tekion-base/constants/vehicleInventory/trim';
import { getTimeStamp, toMoment } from '@tekion/tekion-base/utils/dateUtils';
import { parseNumberFromText } from '@tekion/tekion-base/utils/parsers';
import { getValidAndSortedUserOptions } from '@tekion/tekion-base/utils/formConfigurator/utils';
import { getDisplayNameByPaymentType } from '@tekion/tekion-business/src/helpers/sales/paymentType.helper';
import DealerPropertyHelper from '@tekion/tekion-components/helpers/sales/dealerPropertyHelper';
import HeaderWithInfo from '@tekion/tekion-components/src/molecules/headerWithInfo/HeaderWithInfo';
import { DEFAULT_FILTER_BEHAVIOR } from '@tekion/tekion-components/organisms/filterSection/constants/filterSection.constants';
import { DEFAULT_FILTER_GROUP } from '@tekion/tekion-components/organisms/filterSection';
import { TABLE_TYPES } from '@tekion/tekion-components/organisms/TableManager';
import StatusItem from '@tekion/tekion-components/src/molecules/CellRenderers/StatusItem';
import Ellipsis from '@tekion/tekion-components/atoms/Ellipsis';
import { DATE_TIME_FORMAT } from '@tekion/tekion-conversion-web';
import { E_CONTRACTING_FORM_FIELD } from '@tekion/tekion-widgets/src/constants/eContractForm';
import {
  getFormattedUsageTargetParams,
  getModuleVisibilityRulesPayload,
} from '@tekion/tekion-widgets/src/organisms/formConfiguratorTool/utils/FormConfigurator.utils';
import {
  REQUIRED_FORM_FIELDS,
  FORM_FIELD,
  DEPARTMENTS_DISPLAY,
  DEPARTMENTS_OPTIONS,
  PDF_LIBRARY_FIELDS,
} from '@tekion/tekion-widgets/src/organisms/AddForm/AddForm.constants';
import {
  getEContractingFormDetails,
  getUsageTargetParamsFromPayload,
  formattedSelectFormsList,
  formattedCountriesList,
  formattedStatesList,
} from '@tekion/tekion-widgets/src/organisms/AddForm/AddForm.utils';
import {
  getGlobalDataDisplayNames,
  getStateDisplayNames,
} from '@tekion/tekion-business/src/utils/formConfigurator/formConfigurator.utils';

import previewIcon from 'assets/images/preview.svg';
import { FORM_STATE_LABEL, FORM_STATE, FORM_TYPES } from 'constants/formConfigurator';
import { getMakeIdsFromMake } from 'utils/dealSetup.utils';
import { getOptionsForMake } from 'utils/vehicle.reader';
import { formatModelOptions } from 'commonActions/selectors/dealSetup.selectors';
import { formatOptions, getFormProgrammingType } from '../../../utils';
import ModuleUsageRuleCell from '../molecules/ModuleUsageRuleCell/ModuleUsageRuleCell';
import AuditLogModel from '../../../molecules/AuditLogModel';
import {
  COLUMNS,
  SALESPERSON_OPTIONS,
  DEALER_CERTIFIED_STATUS,
  OEM_CERTIFIED_STATUS,
  TARGET_MODULE_FIELD,
  SEARCH_OPTION_IDS,
  FORM_STATUS,
} from '../constants/FormsPortalDealership.constants';
import ACTION_TYPES from '../constants/FormsPortalDealership.actionTypes';

import styles from '../formsPortalDealership.module.scss';
import TextAndIcon from '../../formsViewTable/components/TextAndIcon';
import { getPdfKeyColumnValue } from '../../../helpers/table';
import Tags from '../../../atoms/Tags';

const getProductIdProviderId = fni => ({
  productId: _get(fni, 'product.id'),
  providerId: _get(fni, 'provider.id'),
});
const combineId = (id1, id2) => `${id1}${id2}`;
const getFnIName = fnI => _get(fnI, 'product.displayName') || '';
const getDueBillName = dueBill => _get(dueBill, 'name');
const getDueBillId = dueBill => _get(dueBill, 'code');
const getFNIId = fni => {
  const { productId, providerId } = getProductIdProviderId(fni);
  return combineId(productId, providerId);
};

const getStateId = state => _get(state, 'id');
const getStateName = state => _get(state, 'name');

export const getFNIOptions = fnIs =>
  (fnIs || []).map(fnI => {
    const label = getFnIName(fnI);
    const value = getFNIId(fnI);
    return { label, value };
  });

export const getDueBillsOptions = dueBills =>
  (dueBills || []).map(dueBill => {
    const label = getDueBillName(dueBill);
    const value = getDueBillId(dueBill);
    return { label, value };
  });

export const getStateOptions = states =>
  states.map(state => ({
    label: getStateName(state),
    value: getStateId(state),
  }));

export const getMakesOptions = makes => (makes || []).map(make => ({ label: make, value: make }));

const getFuelTypeOptions = fuelTypes => (fuelTypes || []).map(fuelType => ({ label: fuelType, value: fuelType }));
const getLenderTypeOptions = lenderTypes =>
  _compact(
    (lenderTypes || []).map(lenderType => !lenderType.hidden && { label: lenderType.displayName, value: lenderType.id })
  );

const getDealTypeOptions = dealTypeConfigs =>
  (dealTypeConfigs || [])
    .filter(dealTypeConfig => dealTypeConfig.enabled === true)
    .map(dealTypeConfig => ({ label: dealTypeConfig.displayName, value: dealTypeConfig.dealType }));

const getCustomStatus = options =>
  (options || [])
    .filter(option => option.enabled === true)
    .map(option => ({ label: option.status, value: option.status }));

export const getVehicleSubTypeOptionsByType = defaultMemoize((stockTypes, vehicleType) =>
  _reduce(
    stockTypes,
    (allSubTypes, { subTypes, enabled, type }) => {
      const vehicleSubTypes =
        enabled && (vehicleType === VEHICLE_TYPES.ALL || type === vehicleType)
          ? _map(subTypes, ({ name }) => ({ label: name, value: name }))
          : [];
      return [...allSubTypes, ...vehicleSubTypes];
    },
    []
  )
);

export const getModelsOptions = models => {
  const uniqueModels = _compact([...new Set(models.map(model => model?.model))]);
  const modelsOptions = uniqueModels.map(model => {
    const option = { label: model, value: model };
    return option;
  });
  return modelsOptions;
};

export const getTrimsOptions = (trims = []) => trims.map(trim => ({ label: trim, value: trim }));

export const getDynamicUsageCategoryVsRulesOptions = ({
  fnIs,
  dueBills,
  states = [],
  makes,
  models,
  fuelTypes,
  lenderTypes,
  dealTypeConfigs,
  vehicleTypes,
  customStatus,
  siteOptions,
  trimsList,
}) => ({
  [USAGE_CATEGORY.FNI]: getFNIOptions(fnIs),
  [USAGE_CATEGORY.DUE_BILLS]: getDueBillsOptions(dueBills),
  [USAGE_CATEGORY.STATE]: getStateOptions(_uniqBy(states, 'id')),
  [USAGE_CATEGORY.MAKE]: getOptionsForMake(makes, DealerPropertyHelper.standardizedMakeSetupEnabled()),
  [USAGE_CATEGORY.MODEL]: formatModelOptions(models),
  [USAGE_CATEGORY.VEHICLE_FUEL_TYPE]: getFuelTypeOptions(fuelTypes),
  [USAGE_CATEGORY.LENDER]: getLenderTypeOptions(lenderTypes),
  [USAGE_CATEGORY.DEAL_TYPE]: getDealTypeOptions(dealTypeConfigs),
  [USAGE_CATEGORY.VEHICLE_SUB_TYPE]: makeVehicleTypeOptions(vehicleTypes),
  [USAGE_CATEGORY.DEAL_SUB_STATUS]: getCustomStatus(customStatus),
  [USAGE_CATEGORY.SITE_SOLD]: siteOptions,
  [USAGE_CATEGORY.TRADE_IN_SUB_TYPE]: getVehicleSubTypeOptionsByType(vehicleTypes, VEHICLE_TYPES.ALL),
  [USAGE_CATEGORY.SOLD_VEHICLE_SUB_TYPE]: getVehicleSubTypeOptionsByType(vehicleTypes, VEHICLE_TYPES.USED),
  [USAGE_CATEGORY.SOLD_VEHICLE_BODY_CLASS]: MARKET_CLASS_CATEGORY_OPTIONS,
  [USAGE_CATEGORY.DEALER_CERTIFIED]: DEALER_CERTIFIED_STATUS,
  [USAGE_CATEGORY.OEM_CERTIFIED]: OEM_CERTIFIED_STATUS,
  [USAGE_CATEGORY.SALESPERSON]: SALESPERSON_OPTIONS,
  [USAGE_CATEGORY.TRIM]: getTrimsOptions(trimsList),
});

const separator = ' by ';

const getFnIFromName = fniName => _split(fniName, separator, 2);

export const isFni = fniName => _size(getFnIFromName(fniName)) === 2;

export const getProductProviderIds = fniName => (isFni(fniName) ? getFnIFromName(fniName) : []);

export const shouldActionDisabled = (formValue, errors, customFields = REQUIRED_FORM_FIELDS) =>
  !_isEmpty(_find(customFields, field => !formValue[field] || _isEmpty(formValue[field]))) ||
  _size(_compact(_values(errors)));

export const getMakeIdTargetting = (makes, selectedMakes) => ({
  usageRules: getMakeIdsFromMake(makes, selectedMakes),
  usageCategory: USAGE_TARGETTING_MAKEID,
  operator: 'INCLUDE',
});

export const getNewFormPayload = ({
  formValues,
  usageTargetParams,
  fnis,
  dueBills,
  makes,
  econtractForm,
  moduleVisibilityRules,
  metaData,
  dynamicUsageCategoryVsRules,
}) => {
  const { [FORM_FIELD.USAGE_TARGET_PARAMS]: deskingUsageTargetParams, [FORM_FIELD.FROM_PEN]: fromPenSource } =
    getFormattedUsageTargetParams({
      formValues,
      usageTargetParams,
      targettedModule: MODULE_TARGETTING_TYPES.DESKING,
      fnis,
      dueBills,
      makes,
      metaData,
      dynamicUsageCategoryVsRules,
    });
  const { [FORM_FIELD.USAGE_TARGET_PARAMS]: crmUsageTargetParams } = getFormattedUsageTargetParams({
    formValues,
    usageTargetParams,
    targettedModule: MODULE_TARGETTING_TYPES.CRM,
    fnis,
    dueBills,
    makes,
    metaData,
    dynamicUsageCategoryVsRules,
  });
  const { [FORM_FIELD.USAGE_TARGET_PARAMS]: digitalRetailingUsageTargetParams } = getFormattedUsageTargetParams({
    formValues,
    usageTargetParams,
    targettedModule: MODULE_TARGETTING_TYPES.DIGITAL_RETAILING,
    fnis,
    dueBills,
    makes,
    metaData,
    dynamicUsageCategoryVsRules,
  });
  const { [FORM_FIELD.USAGE_TARGET_PARAMS]: accountingUsageTargetParams } = getFormattedUsageTargetParams({
    formValues,
    usageTargetParams,
    targettedModule: MODULE_TARGETTING_TYPES.ACCOUNTING,
    fnis,
    dueBills,
    makes,
    metaData,
    dynamicUsageCategoryVsRules,
  });

  const isPDFLibraryForm = _get(formValues, 'pdfLibraryPdfKey');
  const updatedFormValues = { ...formValues };
  if (isPDFLibraryForm) {
    _forEach(PDF_LIBRARY_FIELDS, pdfLibraryField => {
      _set(updatedFormValues, pdfLibraryField, null);
    });
  }

  return {
    ..._omit(updatedFormValues, [..._values(E_CONTRACTING_FORM_FIELD), 'deskingUsageTargetParams']),
    econtractForm,
    econtractFormDetails: econtractForm ? getEContractingFormDetails(updatedFormValues) : null,
    [FORM_FIELD.FORM_NAME]: _trim(_get(updatedFormValues, FORM_FIELD.FORM_NAME)),
    [FORM_FIELD.USAGE_TARGET_PARAMS]: _compact(deskingUsageTargetParams),
    [FORM_FIELD.CRM_USAGE_TARGET_PARAMS]: _compact(crmUsageTargetParams),
    [FORM_FIELD.DIGITAL_RETAILING_USAGE_TARGET_PARAMS]: _compact(digitalRetailingUsageTargetParams),
    [FORM_FIELD.ACCOUNTING_USAGE_TARGET_PARAMS]: _compact(accountingUsageTargetParams),
    [FORM_FIELD.EFFECTIVE_DATE]: parseNumberFromText(getTimeStamp(updatedFormValues[FORM_FIELD.EFFECTIVE_DATE])),
    [FORM_FIELD.EXPIRY_DATE]: parseNumberFromText(getTimeStamp(updatedFormValues[FORM_FIELD.EXPIRY_DATE])),
    [FORM_FIELD.FROM_PEN]: fromPenSource,
    [FORM_FIELD.MODULE_VISIBILITY_RULES]: getModuleVisibilityRulesPayload(updatedFormValues, moduleVisibilityRules),
  };
};

const getPDFMetadataForForm = (formValue, pdfMetadata) => {
  if (_get(formValue, 'pdfLibraryPdfKey')) {
    return {
      [FORM_FIELD.LICENSED_BY]: _get(pdfMetadata, FORM_FIELD.LICENSED_BY),
      [FORM_FIELD.LICENSED_PREFERED_NAME]: _get(pdfMetadata, FORM_FIELD.LICENSED_PREFERED_NAME),
      [FORM_FIELD.LICENSE_KEY]: _get(pdfMetadata, FORM_FIELD.LICENSE_KEY),
      [FORM_FIELD.LICENSED_FOR]: _get(pdfMetadata, FORM_FIELD.LICENSED_FOR),
      [FORM_FIELD.PRINTER]: _get(pdfMetadata, FORM_FIELD.PRINTER),
    };
  }
  return {};
};

export const getFormFromPayload = (formValue, pdfMetadata = {}) => ({
  ...formValue,
  deskingUsageTargetParams: _get(formValue, FORM_FIELD.USAGE_TARGET_PARAMS, EMPTY_ARRAY),
  [FORM_FIELD.USAGE_TARGET_PARAMS]: getUsageTargetParamsFromPayload(formValue),
  [FORM_FIELD.EFFECTIVE_DATE]: toMoment(formValue[FORM_FIELD.EFFECTIVE_DATE]),
  [FORM_FIELD.EXPIRY_DATE]: toMoment(formValue[FORM_FIELD.EXPIRY_DATE]),
  [FORM_FIELD.FORM_CATEGORY]: [formValue[FORM_FIELD.FORM_CATEGORY]],
  ...getPDFMetadataForForm(formValue, pdfMetadata),
});

export const getFormSourceSearchFilter = selectedFilters => {
  const formSourceFilter = _find(selectedFilters, { type: COLUMNS.FORM_SOURCE });
  if (!formSourceFilter) {
    return [DEFAULT_SEARCH_FILTER];
  }

  const filter = { ...formSourceFilter };
  if (_get(formSourceFilter, 'operator') === OPERATORS.NIN) {
    _set(filter, 'operator', OPERATORS.IN);
    _set(filter, 'values', [FORM_SOURCE.FORM_PRO]);
  }

  return mapFiltersToAPIPayload([filter]);
};

const getPublishedFormFilter = showOnlyPublishedForm => {
  if (showOnlyPublishedForm) {
    return [
      {
        field: COLUMNS.FORM_STATE,
        operator: OPERATORS.IN,
        values: [FORM_STATE.PUBLISHED],
      },
    ];
  }
  return EMPTY_ARRAY;
};

export const getFormattedFilters = defaultMemoize((selectedFilters, showOnlyPublishedForm) => [
  ...mapFiltersToAPIPayload(_filter(selectedFilters, filter => _get(filter, 'type') !== COLUMNS.FORM_SOURCE)),
  ...getFormSourceSearchFilter(selectedFilters),
  ...getPublishedFormFilter(showOnlyPublishedForm),
]);

export const getFormListPayload = (
  searchText = '',
  page,
  itemsPerPage,
  sort,
  selectedFilters,
  searchField = SEARCH_OPTION_IDS.ALL,
  showOnlyPublishedForm
) => {
  const pageInfo = {
    start: page * itemsPerPage,
    rows: itemsPerPage,
  };

  return {
    searchText,
    pageInfo,
    sort,
    filters: getFormattedFilters(selectedFilters, showOnlyPublishedForm),
    searchFields: _isEmpty(searchText) ? [SEARCH_OPTION_IDS.ALL] : [searchField],
  };
};

export const getSelectedFormData = (formList, id) => _find(formList, { id }) || {};
export const getSelectedFormDataFromFormKey = (formList, formKey) => _find(formList, { formKey }) || {};

export function checkFNI(value, fnis) {
  return fnis.find(fni => {
    const { productId, providerId } = getProductIdProviderId(fni);
    return `${productId}${providerId}` === value;
  });
}

export function checkDueBill(value, dueBills) {
  return dueBills.find(({ code }) => code === value);
}

export function appendDefaultSelectionToField(field, fieldName, fieldValue) {
  if (!_isEmpty(field)) {
    const targetField = _get(field, [fieldName]);
    if (_isEmpty(targetField)) {
      _set(field, [fieldName], fieldValue);
    }
  }
  return field;
}

export const getNewTargettingData = () => ({ operator: 'INCLUDE' });

const renderFormIcon = form => {
  const formType = getFormProgrammingType(form);

  if (formType === FORM_TYPES.STANDARD || formType === NO_DATA) return null;

  const tagDisplayValue = formType === FORM_TYPES.DEALERSHIP ? __('Dealership Form') : __('Custom');

  return <Tags values={[tagDisplayValue]} className={styles.customTag} />;
};

export const getColumnConfig = ({
  lenderTypes = [],
  categories = {},
  formCategories = [],
  siteOptions,
  getFormattedDateAndTime,
  resolvedUsers,
  paymentOptionConfigs,
  legalProviders,
  dynamicUsageCategoryVsRules,
  globalMetaData,
  onAction,
  getFormType,
}) => {
  const sitesById = _keyBy(siteOptions, 'value');
  const getDefaultDateFormat = date =>
    getFormattedDateAndTime({ value: date, formatType: DATE_TIME_FORMAT.DATE_ABBREVIATED_MONTH_YEAR });
  const displayNameByPaymentType = getDisplayNameByPaymentType(paymentOptionConfigs);
  const lenderTypesObject = lenderTypes.reduce(
    (acc, lenderType) => ({ ...acc, [lenderType.id]: lenderType.displayName }),
    {}
  );
  const categoryObject = Object.keys(categories).reduce(
    (acc, usageCategory) => ({
      ...acc,
      [usageCategory]: (categories[usageCategory].values || []).reduce(
        (usageRuleacc, usageRule) => ({
          ...usageRuleacc,
          [usageRule.key]: usageRule.displayName,
        }),
        {}
      ),
    }),
    {}
  );
  const getUsageDescription = rule => {
    const { usageCategory, rangeRuleParams } = rule;
    switch (usageCategory) {
      case USAGE_CATEGORY.FNI:
        return (rule.providerRuleParams || []).map(provider => provider.usageRuleDescription);
      case USAGE_CATEGORY.DUE_BILLS:
        return (rule.accRuleParams || []).map(provider => provider.usageRuleDescription);
      case USAGE_CATEGORY.LENDER:
        return (_get(rule, 'usageRules') || []).map(lenderId => lenderTypesObject[lenderId]);
      case USAGE_CATEGORY.SITE_SOLD:
        return (_get(rule, 'usageRules') || []).map(siteId => _get(sitesById[siteId], 'label'));

      case USAGE_CATEGORY.PAYMENT:
        return (_get(rule, 'usageRules') || []).map(paymentType => displayNameByPaymentType[paymentType]);

      case USAGE_CATEGORY.MAKE_ID:
        return [];

      default: {
        if (categoryObject[usageCategory]) {
          return (_get(rule, 'usageRules') || []).map(useageRule => categoryObject[usageCategory][useageRule]);
        }
        if (!_isEmpty(rangeRuleParams)) {
          const { lowerBound, upperBound } = _head(rangeRuleParams) || EMPTY_OBJECT;
          return [`${lowerBound ?? ''}-${upperBound ?? ''}`];
        }
        return (_get(rule, 'usageRules') || []).map(useageRule =>
          USAGE_RULES_LABEL[useageRule] ? USAGE_RULES_LABEL[useageRule] : useageRule
        );
      }
    }
  };
  const formatUsageRuleParams = usageTargetParams => {
    const usageRules = (usageTargetParams || []).reduce((allUsageRules, rule) => {
      const usageDescription = getUsageDescription(rule);
      return [...allUsageRules, ...usageDescription];
    }, []);
    return usageRules.join(', ');
  };
  const getDepartmentDisplayName = department => DEPARTMENTS_DISPLAY[department];

  const getFormCategoryDisplayName = category => {
    const validFormCategories = _filter(formCategories, formCategory => category === formCategory.formCategory);
    return validFormCategories[0] ? validFormCategories[0].displayName : '-';
  };

  const getFormStateDisplay = formState => FORM_STATE_LABEL[formState];
  const getIsMigratedFormLabel = formSource => (formSource === FORM_SOURCE.FORM_MIGRATED ? __('Yes') : __('No'));

  const getCreatedBy = id => {
    const displayName = resolvedUsers?.[id]?.displayName;
    return _startCase(displayName);
  };

  const getTargetedModulesLabel = modules =>
    _join(
      _map(modules, module => MODULE_TARGETTING_LABELS[module]),
      ', '
    );

  const getLegalProviderDisplayName = legalProviderName =>
    _get(
      _find(legalProviders, legalProvider => _get(legalProvider, 'legalProviderEnum') === legalProviderName),
      'displayName'
    );

  const getDuplexDisplay = isSelected => (isSelected ? __('Yes') : __('No'));

  const getCommaSeparatedValues = values => (_size(values) ? _join(values, ', ') : NO_DATA);

  return {
    [COLUMNS.NAME]: {
      Cell: mapProps(({ original }) => ({
        text: original?.formName,
        icon: previewIcon,
        customIcon: true,
        altData: 'Preview Icon',
        className: styles.fullWidth,
        actionType: ACTION_TYPES.SHOW_FORM_PREVIEW_MODEL,
        onAction,
        tooltipLabel: __('Preview'),
        data: {
          ...original,
        },
        renderBeforeIcon: () => renderFormIcon(original),
      }))(TextAndIcon),
      width: 260,
    },
    [COLUMNS.PDF_NAME]: {
      Cell: mapProps(({ original: columnData }) => ({
        children: _get(columnData, COLUMNS.PDF_NAME),
      }))(Ellipsis),
    },
    [COLUMNS.FORM_PDF_KEY]: {
      formatter: getPdfKeyColumnValue,
    },
    [COLUMNS.FORM_CATEGORY]: {
      formatter: getFormCategoryDisplayName,
      Header: (
        <HeaderWithInfo
          helpText={__('The category defines the deal jacket where the form would be added')}
          heading={__('Category')}
          tooltipClassName={styles.headerTooltip}
        />
      ),
    },
    [COLUMNS.TARGETED_MODULES]: {
      formatter: getTargetedModulesLabel,
    },
    [COLUMNS.DEPARTMENT]: {
      formatter: getDepartmentDisplayName,
    },
    [COLUMNS.LICENSED_BY]: {
      formatter: getLegalProviderDisplayName,
    },
    [COLUMNS.USAGE_TARGET_PARAMS]: {
      Cell: mapProps(({ original: columnData }) => ({
        data: _get(columnData, COLUMNS.USAGE_TARGET_PARAMS),
        columnData,
        formatter: formatUsageRuleParams,
        getter: getUsageRulesBasedOnModule,
        dynamicUsageCategoryVsRules,
      }))(ModuleUsageRuleCell),
    },
    [COLUMNS.EFFECTIVE_DATE]: {
      formatter: getDefaultDateFormat,
    },
    [COLUMNS.EXPIRY_DATE]: {
      formatter: getDefaultDateFormat,
    },
    [COLUMNS.STATUS]: {
      Cell: mapProps(({ value }) => ({
        data: value,
      }))(StatusItem),
    },
    [COLUMNS.FORM_STATE]: {
      formatter: getFormStateDisplay,
    },
    [COLUMNS.CREATED_DATE]: {
      formatter: getDefaultDateFormat,
    },
    [COLUMNS.FORM_SOURCE]: {
      formatter: getIsMigratedFormLabel,
    },
    [COLUMNS.MODIFIED_DATE]: {
      formatter: getDefaultDateFormat,
    },
    [COLUMNS.CREATED_BY]: {
      formatter: getCreatedBy,
    },
    [COLUMNS.MODIFIED_BY]: {
      formatter: getCreatedBy,
    },
    [COLUMNS.DOCUMENT_TYPE]: {
      Header: (
        <HeaderWithInfo
          helpText={__('Required for DealerTrack eSigning')}
          heading={__('Document Type')}
          tooltipClassName={styles.headerTooltip}
        />
      ),
    },
    [COLUMNS.COUNTRIES]: {
      formatter: getGlobalDataDisplayNames(globalMetaData, COLUMNS.COUNTRIES, 'name', 'code'),
    },
    [COLUMNS.STATES]: {
      formatter: getStateDisplayNames(globalMetaData, COLUMNS.STATES),
    },
    [COLUMNS.OEMS]: {
      formatter: getGlobalDataDisplayNames(globalMetaData, COLUMNS.OEMS),
    },
    [COLUMNS.LENDER]: {
      formatter: getGlobalDataDisplayNames(globalMetaData, COLUMNS.LENDER),
    },
    [COLUMNS.STATE_DEALER_ASSOCIATIONS]: {
      formatter: getGlobalDataDisplayNames(globalMetaData, COLUMNS.STATE_DEALER_ASSOCIATIONS),
    },
    [COLUMNS.FNI_PRODUCT_PROVIDER]: {
      formatter: getGlobalDataDisplayNames(globalMetaData, COLUMNS.FNI_PRODUCT_PROVIDER),
    },
    [COLUMNS.FORM_TYPES]: {
      formatter: getGlobalDataDisplayNames(globalMetaData, COLUMNS.FORM_TYPES),
    },
    [COLUMNS.LABELS]: {
      Cell: mapProps(({ original }) => {
        const values = original[COLUMNS.LABELS];
        return {
          values,
        };
      })(Tags),
    },
    [COLUMNS.TYPE]: {
      Cell: getFormType,
    },
    [COLUMNS.REVISION_DATE]: {
      formatter: getDefaultDateFormat,
    },
    [COLUMNS.LICENSED_FOR]: {
      formatter: getCommaSeparatedValues,
    },
    [COLUMNS.DUPLEX]: {
      formatter: getDuplexDisplay,
    },
  };
};

export const getSortObj = (key, sortDetails) => ({ key, field: key, order: _get(sortDetails, key) });

export const getSortObjForAPI = (selectedColumnId, sortDetails) =>
  _concat(
    getSortObj(selectedColumnId, sortDetails),
    _map(_keys(_omit(sortDetails, selectedColumnId)), key => getSortObj(key, sortDetails))
  );

export const makeVehicleTypeOptions = defaultMemoize(vehicleTypes => {
  let options = [];
  _forEach(vehicleTypes, type => {
    const { enabled, subTypes } = type;
    if (enabled) {
      const subTypeOptions = _map(subTypes, ({ name }) => ({ label: name, value: name }));
      options = _concat(options, subTypeOptions);
    }
  });
  return options;
});

export const getFilterPropsConfig = (selectedFilterGroup, selectedFilters) => ({
  showFilterTrigger: true,
  defaultFilterBehavior: DEFAULT_FILTER_BEHAVIOR.GENERAL,
  appliedFilterGroup: selectedFilterGroup || DEFAULT_FILTER_GROUP,
  selectedFilters,
  assetType: MODULE_ASSET_TYPES.FORM_SETUP,
});

export const getFilterProps = (
  filters,
  {
    selectedFilters,
    formCategories,
    printerTypes,
    allDealershipUsers,
    selectedFilterGroup,
    docTypes,
    globalMetaData,
    labelsInfo,
  }
) => {
  const filterProps = {
    ...filters,
    filterTypes: getFilterTypes({
      filters,
      formCategories,
      printerTypes,
      allDealershipUsers,
      docTypes,
      globalMetaData,
      labelsInfo,
    }),
    ...getFilterPropsConfig(selectedFilterGroup, selectedFilters),
  };

  return filterProps;
};

const getFilterTypes = defaultMemoize(
  ({ filters, formCategories, printerTypes, allDealershipUsers, docTypes, globalMetaData, labelsInfo }) => {
    const { filterTypes } = filters;
    return _map(filterTypes, filterType => {
      switch (filterType.id) {
        case COLUMNS.FORM_CATEGORY:
          return {
            ...filterType,
            additional: {
              options: _map(formCategories, ({ displayName, formCategory }) => ({
                label: displayName,
                value: formCategory,
              })),
            },
          };

        case COLUMNS.DEPARTMENT:
          return {
            ...filterType,
            additional: { options: DEPARTMENTS_OPTIONS },
          };

        case COLUMNS.PRINTER:
          return {
            ...filterType,
            additional: { options: getPrinterTypesOptions(printerTypes) },
          };

        case COLUMNS.CREATED_BY:
          return {
            ...filterType,
            additional: { options: getValidAndSortedUserOptions(allDealershipUsers) },
          };

        case COLUMNS.MODIFIED_BY:
          return {
            ...filterType,
            additional: { options: getValidAndSortedUserOptions(allDealershipUsers) },
          };

        case COLUMNS.FORM_SOURCE:
          return {
            ...filterType,
            additional: { options: FORM_MIGRATED_FILTER_OPTIONS },
          };

        case COLUMNS.DOCUMENT_TYPE:
          return {
            ...filterType,
            additional: {
              options: formattedSelectFormsList(docTypes),
            },
          };

        case COLUMNS.COUNTRIES:
          return {
            ...filterType,
            additional: { options: formattedCountriesList(_get(globalMetaData, COLUMNS.COUNTRIES)) },
          };

        case COLUMNS.STATES:
          return {
            ...filterType,
            additional: { options: formattedStatesList(_get(globalMetaData, COLUMNS.STATES)) },
          };

        case COLUMNS.OEMS:
          return {
            ...filterType,
            additional: { options: _get(globalMetaData, COLUMNS.OEMS) },
          };

        case COLUMNS.LENDER:
          return {
            ...filterType,
            additional: { options: _get(globalMetaData, COLUMNS.LENDER) },
          };

        case COLUMNS.STATE_DEALER_ASSOCIATIONS:
          return {
            ...filterType,
            additional: { options: _get(globalMetaData, COLUMNS.STATE_DEALER_ASSOCIATIONS) },
          };

        case COLUMNS.FNI_PRODUCT_PROVIDER:
          return {
            ...filterType,
            additional: { options: _get(globalMetaData, COLUMNS.FNI_PRODUCT_PROVIDER) },
          };

        case COLUMNS.FORM_TYPES:
          return {
            ...filterType,
            additional: { options: _get(globalMetaData, COLUMNS.FORM_TYPES) },
          };

        case COLUMNS.LABELS:
          return {
            ...filterType,
            additional: { options: formatOptions(labelsInfo) },
          };

        default:
          return filterType;
      }
    });
  }
);

export const mapFiltersToAPIPayload = filters => _map(filters, ({ type: field, ...rest }) => ({ field, ...rest }));

const getPrinterTypesOptions = printerTypes =>
  _filter(
    _map(printerTypes, printerType => ({
      label: PRINTER_TYPES_LABEL[printerType],
      value: printerType,
    })),
    'label'
  );

export const makeLicenseProviderKeys = licenseProviders => _keyBy(licenseProviders, 'legalProviderEnum');

export const getTableProps = (selection, count, currentPage, pageLength, loading, draftFormIds) => ({
  totalNumberOfEntries: count,
  currentPage,
  showPagination: true,
  pageSize: pageLength,
  type: TABLE_TYPES.FIXED_COLUMN,
  showSearch: true,
  showFilter: false,
  showHeader: true,
  showSubHeader: true,
  loading,
  rowHeight: 40,
  selection,
  disabled: draftFormIds,
  noHeadSelector: true,
});

export const getTenantValues = tenantIds =>
  _map(tenantIds, tenant => ({
    label: _get(tenant, 'displayName'),
    value: _get(tenant, 'tenantId'),
  }));

export const getDealerValues = dealers =>
  _map(dealers, dealer => ({
    label: _get(dealer, 'displayName'),
    value: _get(dealer, 'dealerId'),
  }));

export const getUpdatedLabels = ({ allLabels = EMPTY_ARRAY, recentLabels = EMPTY_ARRAY, newLabels = EMPTY_ARRAY }) => {
  const newlyAddedLabels = _difference(newLabels, [...allLabels, ...recentLabels]);
  let oldRecentLabels = recentLabels;
  let removedRecentLabel = null;
  if (_size(newlyAddedLabels) === 0) return { newRecentLabels: recentLabels, newAllLabels: allLabels };
  if (_size(recentLabels) > 4) {
    oldRecentLabels = _slice(recentLabels, 0, 4);
    removedRecentLabel = _last(recentLabels);
  }

  const newRecentLabels = [...newlyAddedLabels, ...oldRecentLabels];
  const newAllLabels = _compact([removedRecentLabel, ...allLabels]);
  return { newRecentLabels, newAllLabels };
};

export const additionalAttrFormatters = formatters => ({
  ...formatters,
  jexl: jexlScript => <AuditLogModel title={__('JEXL Script')} content={jexlScript} />,
});

export const handleHideTooltip = attributeId => !_includes(['jexl'], attributeId);

export const getUsageRulesBasedOnModule = (formData, module) =>
  _get(formData, TARGET_MODULE_FIELD[module], EMPTY_ARRAY);

export const getParsedFormList = formList =>
  _map(formList, form => ({
    ...form,
    status:
      form.formState === FORM_STATE.DRAFT
        ? FORM_STATUS.INACTIVE
        : FORM_STATUS[toMoment().isBefore(form.expiryDate) ? 'ACTIVE' : 'INACTIVE'],
    printerType: PRINTER_TYPES_LABEL[form.printerType],
    usageRule: USAGE_RULES_LABEL[form.usageRule],
  }));
