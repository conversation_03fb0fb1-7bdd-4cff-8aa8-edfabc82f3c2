@import "tstyles/component.scss";

.usageTargettingContainer {
  padding: 0 2.4rem;
  .heading {
    margin-bottom: 2.4rem;
  }
  .button {
    margin-top: 1.6rem;
  }
}

.licensedBy {
  flex: 0 1 auto;
  width: 31rem;
  margin-bottom: 2.4rem;
  margin-left: 1rem;
}

.sellingPriceUsageRuleContainer {
  display: flex;
  justify-content: space-between;
}

.sellingPriceInput {
  width: 48%;
}

.selectContainer {
  border: 0.1rem solid $ashGray;
  @include full-width;
}

.copyFormName {
  margin-bottom: 1rem;
}

.infoIconClassName {
  color: $dodgerBlueLight;
  margin-right: 1.2rem;
}

.iconWithTextWrapper {
  padding: 1.2rem 2.2rem 1.2rem 1.6rem;
  background-color: $aliceBlue;
  border-radius: 0.4rem;
  color: $atomic;
}

.restoreModalTextWrapper {
  padding-bottom: 5.3rem;
  margin-bottom: 1rem;
}

.header_kebab_wrapper {
  transform: rotate(90deg);
}

.disabledField {
  color: $ashGray;
}

.headerTooltip {
  padding: 0.3rem 0.8rem;
}

.validateForm div[class*="fieldLabel_label"] {
  white-space: unset;
}
