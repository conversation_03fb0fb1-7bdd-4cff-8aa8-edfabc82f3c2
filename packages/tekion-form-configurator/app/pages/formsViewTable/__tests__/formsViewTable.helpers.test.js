import MODULE_ASSET_TYPES from '@tekion/tekion-base/constants/moduleAssetTypes';
import { DEFAULT_FILTER_BEHAVIOR } from '@tekion/tekion-components/src/organisms/filterSection/constants/filterSection.constants';

import TEnvReader from '@tekion/tekion-base/readers/Env';
import userReader from '@tekion/tekion-base/readers/User';

import {
  getFilterTypes,
  getFilterPropsConfig,
  getFilterProps,
  getSubHeaderRightActions,
  getDealerInfoFromUserInfo,
  getParsedFormList,
  getKebabMenuDisabledOptions,
} from '../formsViewTable.helpers';
import { FORMS_VIEW_FILTER_IDS } from '../formsViewTable.constants';
import { getFilters } from '../formsViewTable.config';

describe('Testcase for formsviewTable helpers', () => {
  describe('Testcase for getFilterTypes method', () => {
    it('should return an object with filterTypes and defaultFilterTypes keys', () => {
      const result = getFilterTypes({});
      expect(result).toHaveProperty('filterTypes');
      expect(result).toHaveProperty('defaultFilterTypes');
    });
  });

  describe('Testcase for getFilterPropsConfig method', () => {
    it('should return an object with expected properties and values when given valid inputs', () => {
      const selectedFilterGroup = 'filterGroup1';
      const selectedFilters = ['filter1', 'filter2'];

      const filterPropsConfig = getFilterPropsConfig(selectedFilterGroup, selectedFilters);

      expect(filterPropsConfig).toEqual({
        assetType: MODULE_ASSET_TYPES.FORMS_LIBRARY_VIEW,
        defaultFilterBehavior: DEFAULT_FILTER_BEHAVIOR.GENERAL,
        appliedFilterGroup: selectedFilterGroup,
        selectedFilters,
        showDefaultFilter: true,
        shouldCloseOnOutsideClick: true,
        shouldCloseMultiSortOnOutsideClick: true,
        shouldClearMultiSortOnReset: true,
      });
    });

    it('should return an object with expected properties and values when given undefined for selectedFilterGroup and selectedFilters', () => {
      const filterPropsConfig = getFilterPropsConfig(undefined, undefined);

      expect(filterPropsConfig).toEqual({
        assetType: MODULE_ASSET_TYPES.FORMS_LIBRARY_VIEW,
        defaultFilterBehavior: DEFAULT_FILTER_BEHAVIOR.GENERAL,
        appliedFilterGroup: undefined,
        selectedFilters: undefined,
        showDefaultFilter: true,
        shouldCloseOnOutsideClick: true,
        shouldCloseMultiSortOnOutsideClick: true,
        shouldClearMultiSortOnReset: true,
      });
    });
  });

  describe('Testcase for getFilterProps method', () => {
    it('should return an object with filter types and filter props when given selected filter group, selected filters, and all dealership users', () => {
      const selectedFilterGroup = 'group';
      const selectedFilters = ['filter1', 'filter2'];
      const allDealershipUsers = ['user1', 'user2'];
      const filters = getFilters({});

      const result = getFilterProps({ selectedFilterGroup, selectedFilters, allDealershipUsers });

      expect(result).toEqual({
        filterTypes: [
          filters[FORMS_VIEW_FILTER_IDS.COUNTRIES],
          filters[FORMS_VIEW_FILTER_IDS.DEPARTMENT],
          filters[FORMS_VIEW_FILTER_IDS.FORM_TYPES],
          filters[FORMS_VIEW_FILTER_IDS.FORM_CATEGORY],
          filters[FORMS_VIEW_FILTER_IDS.FNI_PRODUCT_PROVIDER],
          filters[FORMS_VIEW_FILTER_IDS.LABELS],
          filters[FORMS_VIEW_FILTER_IDS.LENDER],
          filters[FORMS_VIEW_FILTER_IDS.LICENSED_BY],
          filters[FORMS_VIEW_FILTER_IDS.OEMS],
          filters[FORMS_VIEW_FILTER_IDS.PRINTER],
          filters[FORMS_VIEW_FILTER_IDS.STATE_DEALER_ASSOCIATIONS],
          filters[FORMS_VIEW_FILTER_IDS.STATUS],
          filters[FORMS_VIEW_FILTER_IDS.STATES],
          filters[FORMS_VIEW_FILTER_IDS.TARGETED_MODULES],
        ],
        defaultFilterTypes: [FORMS_VIEW_FILTER_IDS.STATES],
        assetType: MODULE_ASSET_TYPES.FORMS_LIBRARY_VIEW,
        defaultFilterBehavior: DEFAULT_FILTER_BEHAVIOR.GENERAL,
        appliedFilterGroup: selectedFilterGroup,
        selectedFilters,
        showDefaultFilter: true,
        shouldCloseOnOutsideClick: true,
        shouldCloseMultiSortOnOutsideClick: true,
        shouldClearMultiSortOnReset: true,
      });
    });
  });

  describe('Testcase for getSubHeaderRightActions method', () => {
    it('returns correct array of actions', () => {
      const bulkActionOverlay = 'Mocked bulk action overlay';
      const kebabActionOverlay = 'Mocked kebab action overlay';
      const handleSearchInputChange = jest.fn();
      const searchText = 'Mocked search text';
      const selectedItems = ['item1', 'item2'];

      const result = getSubHeaderRightActions({
        bulkActionOverlay,
        kebabActionOverlay,
        handleSearchInputChange,
        searchText,
        selectedItems,
      });

      expect(result).toHaveLength(1);
      expect(result[0].renderer).toBeDefined();
    });
  });

  describe('Testcase for getDealerInfoFromUserInfo method', () => {
    it('should return an object with dealerId when userInfo is available', () => {
      const mockUserInfo = { id: 'user123' };
      const mockDealerId = 'dealer456';
      jest.spyOn(TEnvReader, 'userInfo').mockReturnValue(mockUserInfo);
      jest.spyOn(userReader, 'dealerId').mockReturnValue(mockDealerId);

      const result = getDealerInfoFromUserInfo();

      expect(result).toEqual({ dealerId: mockDealerId });
    });

    it('should return an object with undefined dealerId when userInfo is undefined or null', () => {
      jest.spyOn(TEnvReader, 'userInfo').mockReturnValue(null);
      jest.spyOn(userReader, 'dealerId').mockReturnValue(undefined);

      const result = getDealerInfoFromUserInfo();

      expect(result).toEqual({ dealerId: undefined });
    });
  });

  describe('Testcase for getParsedFormList method', () => {
    it('should add a status key to each form in the list based on ownership', () => {
      const formList = [
        { id: 1, name: 'Form 1', ownerId: 'user1' },
        { id: 2, name: 'Form 2', ownerId: 'user2' },
      ];

      const result = getParsedFormList(formList);

      expect(result).toEqual([
        { id: 1, name: 'Form 1', ownerId: 'user1', status: 'NOT_OWNED' },
        { id: 2, name: 'Form 2', ownerId: 'user2', status: 'NOT_OWNED' },
      ]);
    });
  });

  describe('Testcase for getKebabMenuDisabledOptions method', () => {
    it('should return an empty array when rowInfo status is not "OWNED"', () => {
      const rowInfo = { status: 'NOT_OWNED' };
      const result = getKebabMenuDisabledOptions(rowInfo);
      expect(result).toEqual([]);
    });

    it('should return an empty array when rowInfo is undefined', () => {
      const result = getKebabMenuDisabledOptions(undefined);
      expect(result).toEqual([]);
    });
  });
});
