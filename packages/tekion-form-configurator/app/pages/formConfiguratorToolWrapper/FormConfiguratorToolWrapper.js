import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import GlobalAnalytics from '@tekion/tekion-base/utils/GlobalAnalytics';
import FormConfiguratorTool from 'twidgets/organisms/formConfiguratorTool';
import {
  hasEContractingEditPermission,
  hasViewOnlyFormConfigurator,
  hasEditGlobalFieldsPermission,
  hasAddVisibilityTargettingPermission,
  hasEditTekionRestrictedFieldsPermission,
  hasEditDPRestrictedFieldsPermission,
  hasPublishFormPermission,
  hasCreateFormPermission,
  hasSaveFormPermission,
} from 'permissions/formConfigurator.permissions';
import { BASE_REDUCER_KEY } from '../../constants/formConfigurator';
import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import {
  getDueBills,
  getFniOptions,
  getVehicleTypes,
  getGlobalMetaData,
} from '../formConfigurator/FormConfigurator.selectors';
import {
  fetchDueBills,
  getFNIProducts,
  getViSettings,
  fetchSalesSetupInfo,
  fetchGlobalMetaData,
} from '../formConfigurator/FormConfigurator.actions';
import { isFCEditFlow, isFCLiteEditFlow, isFCLiteViewFlow } from '../../utils';

/*
To pass custom action handlers use below commented code
const customActions = {
  [ACTION_TYPES.ON_CANCEL]: () => {
    console.warn('Clicked');
  },
};
<FormConfiguratorTool
  customActionHandlers={customActions}
  fieldPropertiesFormActionHandlers={fieldPropertiesFormActionHandlers}
  hasViewPermission={hasFormConfiguratorView()}
  {...props}
  hideTabs={[CONFIG_KEYS.FORMULA, CONFIG_KEYS.RULES]}
/>;
*/

const FormConfiguratorToolWrapper = props => {
  useEffect(() => {
    GlobalAnalytics.sendSubAppLoadingEvent({ app: 'FormConfiguratorTool' });
  }, []);

  const fCLiteConfig = !isFCEditFlow()
    ? {
        hidePrinterAlignment: true,
        hidePageTypeInformation: true,
        customRenderOptions: {
          hideActiveFieldToggle: true,
        },
      }
    : EMPTY_OBJECT;

  return (
    <FormConfiguratorTool
      reducerKey={BASE_REDUCER_KEY}
      hasViewOnlyPermission={hasViewOnlyFormConfigurator()}
      hasEContractingEditPermission={hasEContractingEditPermission()}
      hasEditGlobalFieldsPermission={hasEditGlobalFieldsPermission()}
      hasAddVisibilityTargettingPermission={hasAddVisibilityTargettingPermission()}
      hasEditTekionRestrictedFieldsPermission={hasEditTekionRestrictedFieldsPermission()}
      hasEditDPRestrictedFieldsPermission={hasEditDPRestrictedFieldsPermission()}
      config={{ isLockingEnabled: true, disableGlobalEditAccess: true, ...fCLiteConfig }}
      isSaveAsNewDisabled={!hasPublishFormPermission() || !hasCreateFormPermission()}
      isSaveDisabled={!hasPublishFormPermission() && !hasSaveFormPermission()}
      isFcLiteModeEnabled={!isFCEditFlow()}
      disableEditForFCLiteMode={!isFCLiteEditFlow()}
      isFCLiteViewOnly={isFCLiteViewFlow()}
      {...props}
    />
  );
};

const mapStateToProps = state => ({
  fniOptions: getFniOptions(state),
  dueBills: getDueBills(state),
  productsListFromProps: getFniOptions(state),
  vehicleTypes: getVehicleTypes(state),
  globalMetaData: getGlobalMetaData(state),
});

const mapDispatchToProps = {
  fetchDueBills,
  getFNIProducts,
  getViSettings,
  fetchSalesSetupInfo,
  fetchGlobalMetaData,
};

export default connect(mapStateToProps, mapDispatchToProps)(FormConfiguratorToolWrapper);
