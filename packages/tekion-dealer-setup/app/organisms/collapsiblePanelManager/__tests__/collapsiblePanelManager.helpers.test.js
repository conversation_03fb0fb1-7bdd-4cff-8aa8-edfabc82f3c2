import _noop from 'lodash/noop';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { DEFAULT_COLAPSIBLE_PANEL_MANAGER_PROPS } from 'constants/general.constants';
import { PANEL_TYPES } from '../collapsiblePanelManager.constants';
import {
  getDrawerToggleIconClass,
  createHeaderProps,
  createFilterProps,
  createCountProps,
  createSearchProps,
  createRefreshProps,
  createCollapsiblePanelProps,
} from '../collapsiblePanelManager.helpers';

describe('collapsiblePanelManager.helpers', () => {
  describe('getDrawerToggleIconClass', () => {
    it('should return correct icon class for LEFT panel when drawer is shown', () => {
      const result = getDrawerToggleIconClass(PANEL_TYPES.LEFT, true);
      expect(result).toBe('icon-arrow-left-circled');
    });

    it('should return correct icon class for LEFT panel when drawer is hidden', () => {
      const result = getDrawerToggleIconClass(PANEL_TYPES.LEFT, false);
      expect(result).toBe('icon-arrow-right-circled');
    });

    it('should return correct icon class for RIGHT panel when drawer is shown', () => {
      const result = getDrawerToggleIconClass(PANEL_TYPES.RIGHT, true);
      expect(result).toBe('icon-arrow-right-circled');
    });

    it('should return correct icon class for RIGHT panel when drawer is hidden', () => {
      const result = getDrawerToggleIconClass(PANEL_TYPES.RIGHT, false);
      expect(result).toBe('icon-arrow-left-circled');
    });
  });

  describe('createHeaderProps', () => {
    it('should return default props when no arguments provided', () => {
      const result = createHeaderProps();
      expect(result).toEqual({
        title: EMPTY_STRING,
        checked: false,
        onSelectAll: _noop,
        disabled: false,
        buttonLabel: EMPTY_STRING,
        onButtonClick: _noop,
        buttonDisabled: false,
        showCheckbox: false,
        showButton: false,
      });
    });

    it('should return custom props when provided', () => {
      const mockOnSelectAll = jest.fn();
      const mockOnButtonClick = jest.fn();
      const customProps = {
        title: 'Test Title',
        checked: true,
        onSelectAll: mockOnSelectAll,
        disabled: true,
        buttonLabel: 'Test Button',
        onButtonClick: mockOnButtonClick,
        buttonDisabled: true,
        showCheckbox: true,
        showButton: true,
      };

      const result = createHeaderProps(customProps);
      expect(result).toEqual(customProps);
    });

    it('should merge custom props with defaults', () => {
      const result = createHeaderProps({ title: 'Custom Title', checked: true });
      expect(result).toEqual({
        title: 'Custom Title',
        checked: true,
        onSelectAll: _noop,
        disabled: false,
        buttonLabel: EMPTY_STRING,
        onButtonClick: _noop,
        buttonDisabled: false,
        showCheckbox: false,
        showButton: false,
      });
    });
  });

  describe('createFilterProps', () => {
    it('should return default props when no arguments provided', () => {
      const result = createFilterProps();
      expect(result).toEqual({
        label: EMPTY_STRING,
        options: EMPTY_ARRAY,
        values: EMPTY_ARRAY,
        onChange: _noop,
        shouldSortOptions: true,
        disabled: false,
        className: EMPTY_STRING,
      });
    });

    it('should return custom props when provided', () => {
      const mockOnChange = jest.fn();
      const customProps = {
        label: 'Filter Label',
        options: ['option1', 'option2'],
        values: ['value1'],
        onChange: mockOnChange,
        shouldSortOptions: false,
        disabled: true,
        className: 'custom-class',
      };

      const result = createFilterProps(customProps);
      expect(result).toEqual(customProps);
    });
  });

  describe('createCountProps', () => {
    it('should return default props when no arguments provided', () => {
      const result = createCountProps();
      expect(result).toEqual({
        selectedCount: 0,
        totalCount: 0,
        showTotalCount: true,
        showSelectedCount: false,
      });
    });

    it('should return custom props when provided', () => {
      const customProps = {
        selectedCount: 5,
        totalCount: 10,
        showTotalCount: false,
        showSelectedCount: true,
      };

      const result = createCountProps(customProps);
      expect(result).toEqual(customProps);
    });
  });

  describe('createSearchProps', () => {
    it('should return default props when no arguments provided', () => {
      const result = createSearchProps();
      expect(result).toEqual({
        onSearch: _noop,
        searchableFieldsOptions: null,
        onChangeSearchField: _noop,
        searchField: EMPTY_STRING,
        placeholder: __('Search...'),
        className: EMPTY_STRING,
        isActive: false,
        value: EMPTY_STRING,
        onChange: _noop,
        inputWrapperClassName: EMPTY_STRING,
        inputFieldClassName: EMPTY_STRING,
        isFocused: false,
        dataTestConfig: EMPTY_OBJECT,
      });
    });

    it('should return custom props when provided', () => {
      const mockOnSearch = jest.fn();
      const mockOnChangeSearchField = jest.fn();
      const mockOnChange = jest.fn();
      const customProps = {
        onSearch: mockOnSearch,
        searchableFieldsOptions: ['field1', 'field2'],
        onChangeSearchField: mockOnChangeSearchField,
        searchField: 'name',
        placeholder: 'Custom placeholder',
        className: 'search-class',
        isActive: true,
        value: 'search term',
        onChange: mockOnChange,
        inputWrapperClassName: 'wrapper-class',
        inputFieldClassName: 'field-class',
        isFocused: true,
        dataTestConfig: { testId: 'search-input' },
      };

      const result = createSearchProps(customProps);
      expect(result).toEqual(customProps);
    });
  });

  describe('createRefreshProps', () => {
    it('should return default props when no arguments provided', () => {
      const result = createRefreshProps();
      expect(result).toEqual({
        disabled: false,
        onClick: _noop,
        className: EMPTY_STRING,
      });
    });

    it('should return custom props when provided', () => {
      const mockOnClick = jest.fn();
      const customProps = {
        disabled: true,
        onClick: mockOnClick,
        className: 'refresh-class',
      };

      const result = createRefreshProps(customProps);
      expect(result).toEqual(customProps);
    });
  });

  describe('createCollapsiblePanelProps', () => {
    it('should return default props when no arguments provided', () => {
      const result = createCollapsiblePanelProps();
      expect(result).toEqual({
        leftPanelManagerProps: DEFAULT_COLAPSIBLE_PANEL_MANAGER_PROPS,
        headerProps: EMPTY_OBJECT,
        filterProps: EMPTY_OBJECT,
        countProps: EMPTY_OBJECT,
        searchProps: EMPTY_OBJECT,
        refreshProps: EMPTY_OBJECT,
        isActive: true,
        onToggle: _noop,
        containerClass: EMPTY_STRING,
        containerHiddenClass: EMPTY_STRING,
        className: EMPTY_STRING,
        panelType: PANEL_TYPES.LEFT,
      });
    });

    it('should return custom props when provided', () => {
      const mockOnToggle = jest.fn();
      const customHeaderProps = { title: 'Custom Header' };
      const customFilterProps = { label: 'Custom Filter' };
      const customCountProps = { totalCount: 100 };
      const customSearchProps = { placeholder: 'Custom Search' };
      const customRefreshProps = { disabled: true };
      const customLeftPanelManagerProps = { showHeader: false };

      const customProps = {
        leftPanelManagerProps: customLeftPanelManagerProps,
        headerProps: customHeaderProps,
        filterProps: customFilterProps,
        countProps: customCountProps,
        searchProps: customSearchProps,
        refreshProps: customRefreshProps,
        isActive: false,
        onToggle: mockOnToggle,
        containerClass: 'custom-container',
        containerHiddenClass: 'custom-hidden',
        className: 'custom-class',
        panelType: PANEL_TYPES.RIGHT,
      };

      const result = createCollapsiblePanelProps(customProps);
      expect(result).toEqual(customProps);
    });

    it('should merge custom props with defaults', () => {
      const result = createCollapsiblePanelProps({
        isActive: false,
        className: 'custom-class',
      });

      expect(result).toEqual({
        leftPanelManagerProps: DEFAULT_COLAPSIBLE_PANEL_MANAGER_PROPS,
        headerProps: EMPTY_OBJECT,
        filterProps: EMPTY_OBJECT,
        countProps: EMPTY_OBJECT,
        searchProps: EMPTY_OBJECT,
        refreshProps: EMPTY_OBJECT,
        isActive: false,
        onToggle: _noop,
        containerClass: EMPTY_STRING,
        containerHiddenClass: EMPTY_STRING,
        className: 'custom-class',
        panelType: PANEL_TYPES.LEFT,
      });
    });
  });
});
