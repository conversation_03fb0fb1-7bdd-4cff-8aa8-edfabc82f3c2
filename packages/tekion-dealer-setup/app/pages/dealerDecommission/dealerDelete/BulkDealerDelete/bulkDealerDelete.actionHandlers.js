import _isEmpty from 'lodash/isEmpty';
import _has from 'lodash/has';
import _size from 'lodash/size';
import _filter from 'lodash/filter';
import _some from 'lodash/some';
import _map from 'lodash/map';
import _last from 'lodash/last';
import _get from 'lodash/get';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { DEFAULT_PAGE_SIZE } from '@tekion/tekion-base/constants/tableConstants';
import openWindowInNewTab from '@tekion/tekion-base/utils/openWindowInNewTab';

import { tget } from '@tekion/tekion-base/utils/general';
import FORM_ACTION_TYPES from '@tekion/tekion-components/organisms/FormBuilder/constants/actionTypes';
import { defaultOnChangeHandler } from '@tekion/tekion-components/src/connectors/withFormPageState/withFormPageState.helpers';
import TABLE_ACTION_TYPES from '@tekion/tekion-components/src/organisms/TableManager/constants/actionTypes';

import { DEALER_SETUP_BASE_PATH, FEATURES } from 'constants/general.constants';
import { BASE_PATHS } from 'pages/TenantAndDealerSetup/tenantAndDealerSetup.constants';
import {
  DEALER_DISABLE_FORM_FIELDS,
  CUSTOM_ACTIONS,
  COLAPSIBLE_PANEL_PAGE_SIZE,
} from 'pages/dealerDecommission/dealerDisable/dealerDisable.constants';
import {
  DELETE_CUSTOM_ACTIONS,
  FETCH_TENANTS_PAYLOAD,
  SELECTED_LIST_MAX_SIZE,
  TABS,
} from './bulkDealerDelete.constants';
import { fetchAllTenantsAction, fetchDealersByTenantIdAction } from '../../dealerDisable/dealerDisable.actions';
import { formatPayload, getDealerInfoFromDealerIds } from '../../dealerDisable/dealerDisable.helpers';
import {
  filterTapDealers,
  searchDealers,
  handlePageTokens,
  validateDealerSelection,
} from '../../dealerDecommision.helpers';
import {
  fetchEligibleDealers,
  fetchDecommissionHistory,
  bulkDecommissionDealersAction,
} from './bulkDealerDelete.actions';

const setInitialData = async ({ getState, setState }) => {
  setState({ isPageLoading: true });
  const { tenantsList = EMPTY_ARRAY, dispatch } = getState();
  if (_isEmpty(tenantsList)) fetchAllTenantsAction(dispatch, FETCH_TENANTS_PAYLOAD);

  const [eligibleDealersToDeleteResponse, decommissionedResponse] = await Promise.all([
    fetchEligibleDealers({ pageSize: COLAPSIBLE_PANEL_PAGE_SIZE }),
    fetchDecommissionHistory({ pageSize: DEFAULT_PAGE_SIZE }),
  ]);

  const { eligibleDealersToDelete = EMPTY_ARRAY } = eligibleDealersToDeleteResponse;
  const { decommissionedDealers = EMPTY_ARRAY, nextPageToken = 0, totalCount = 0 } = decommissionedResponse;

  setState({
    currentPage: 1,
    pageSize: DEFAULT_PAGE_SIZE,
    pageToken: null,
    nextPageToken,
    previousPageTokens: EMPTY_ARRAY,
    isPageLoading: false,
    selectedTab: TABS.SELECT_DEALERS,
    isListCheckBox: false,
    decommissionedDealers,
    decommissionedDealersTotalCount: totalCount,
    eligibleDealersToDelete,
    eligibleDealersToRender: eligibleDealersToDelete,
    filteredEligibleDealers: eligibleDealersToDelete,
    isEligibleDealersLoading: false,
    showRequestListDrawer: eligibleDealersToDelete?.length > 0,
  });
};

const handleTenantsChange = async ({ getState, setState, params = EMPTY_OBJECT }) => {
  const { tenantDealerMap = EMPTY_OBJECT, dispatch } = getState();
  const { id, value: updatedTenantId } = params;
  const updatedValues = {
    [id]: updatedTenantId,
    [DEALER_DISABLE_FORM_FIELDS.DEALER_ID]: EMPTY_ARRAY,
  };

  setState({ values: updatedValues });

  if (!_has(tenantDealerMap, updatedTenantId)) {
    setState({ isDealersForTenantLoading: true });
    await fetchDealersByTenantIdAction(dispatch, updatedTenantId);
    setState({ isDealersForTenantLoading: false });
  }
};

const handleSelectDealer = ({ getState, setState }) => {
  const { values: preValues, dealersToDelete: prevDealersToDelete, tenantDealerMap } = getState();
  const { [DEALER_DISABLE_FORM_FIELDS.TENANT_ID]: tenantId, [DEALER_DISABLE_FORM_FIELDS.DEALER_ID]: dealerIds } =
    preValues;

  const dealersForTenant = tget(tenantDealerMap, tenantId);
  const currSelectedDealers = getDealerInfoFromDealerIds({ dealerList: dealersForTenant, dealerIds });

  const { isValid, mergedList } = validateDealerSelection({
    currentList: prevDealersToDelete,
    newItems: currSelectedDealers,
    maxSize: SELECTED_LIST_MAX_SIZE,
  });

  if (!isValid) {
    return;
  }

  setState({
    values: EMPTY_OBJECT,
    dealersToDelete: mergedList,
  });
};

const ON_FIELD_CHANGE_HANDLERS = {
  [DEALER_DISABLE_FORM_FIELDS.TENANT_ID]: handleTenantsChange,
  [DEALER_DISABLE_FORM_FIELDS.SUBMIT_BUTTON]: handleSelectDealer,
};

const handleChange = ({ getState, setState, params = EMPTY_OBJECT }) => {
  const { id } = params;
  const onChange = ON_FIELD_CHANGE_HANDLERS[id] || defaultOnChangeHandler;

  onChange({ setState, getState, params });
};

const handleToggleDrawer = ({ getState, setState }) => {
  const { showRequestListDrawer } = getState();
  setState({ showRequestListDrawer: !showRequestListDrawer });
};

const handleTabChange = ({ setState, params }) => {
  const { tabKey } = params;
  setState({ selectedTab: tabKey });
};

const handleRemoveDealer = ({ setState, getState, params = EMPTY_OBJECT }) => {
  const { dealersToDelete: prevDealersToDelete } = getState();
  const { values, value } = params;

  if (value === 'ALL') {
    setState({ dealersToDelete: EMPTY_ARRAY, showRemoveAllModal: false });
    return;
  }

  const { id } = values;

  const updatedSelectedList = _filter(prevDealersToDelete, dealer => dealer?.id !== id);

  setState({ dealersToDelete: updatedSelectedList });
};

const handleOpenRemoveModal = ({ setState }) => {
  setState({ showRemoveAllModal: true });
};

const handleOpenDeleteModal = ({ setState }) => {
  setState({ showDeleteModal: true });
};

const handleModalClose = ({ setState }) => {
  setState({ showDeleteModal: false, showRemoveAllModal: false });
};

const handlePageUpdate = async ({ getState, setState, params = EMPTY_OBJECT }) => {
  const { page = 1, resultsPerPage = DEFAULT_PAGE_SIZE } = _get(params, 'value', EMPTY_OBJECT);

  const { previousPageTokens, currentPage, nextPageToken, pageSize, searchText, searchField } = getState();

  const { pageNo, prevPageTokens, currentPageToken } = handlePageTokens({
    page,
    currentPage,
    nextPageToken,
    previousPageTokens,
    prevPageSize: pageSize,
    currentPageSize: resultsPerPage,
  });

  const {
    decommissionedDealers = EMPTY_ARRAY,
    nextPageToken: currNextPageToken = 0,
    totalCount: decommissionedDealersTotalCount,
  } = await fetchDecommissionHistory({
    pageSize: resultsPerPage,
    nextPageToken: currentPageToken,
    [searchField]: searchText,
  });

  setState({
    currentPage: pageNo,
    pageSize: resultsPerPage,
    nextPageToken: currNextPageToken,
    previousPageTokens: prevPageTokens,
    decommissionedDealers,
    decommissionedDealersTotalCount,
    isHistoryDataLoading: false,
  });
};

const handleSelectDealersFromList = ({ getState, setState, params = EMPTY_OBJECT }) => {
  const { request, value } = params;
  const { selectedDealersFromList: prevSelectedDealersFromList, eligibleDealersToRender, isListCheckBox } = getState();
  if (_isEmpty(eligibleDealersToRender)) return;

  let updatedSelectedList = EMPTY_ARRAY;

  if (value === 'ALL') {
    updatedSelectedList = !isListCheckBox ? eligibleDealersToRender : EMPTY_ARRAY;
    setState({ isListCheckBox: !isListCheckBox });
  } else if (_some(prevSelectedDealersFromList, { dealerId: request?.dealerId })) {
    updatedSelectedList = _filter(prevSelectedDealersFromList, item => item.dealerId !== request?.dealerId);
  } else {
    updatedSelectedList = [...(prevSelectedDealersFromList || EMPTY_ARRAY), request];
  }
  setState({
    isListCheckBox: _size(updatedSelectedList) === _size(eligibleDealersToRender),
    selectedDealersFromList: updatedSelectedList,
  });
};

const handleAddSelectedDealersFromList = ({ setState, getState }) => {
  const { dealersToDelete: prevDealersToDelete, selectedDealersFromList } = getState();
  const formatedSelectedDealersFromList = _map(selectedDealersFromList, dealer => ({
    ...dealer,
    id: dealer.dealerId,
  }));

  const { isValid, mergedList } = validateDealerSelection({
    currentList: prevDealersToDelete,
    newItems: formatedSelectedDealersFromList,
    maxSize: SELECTED_LIST_MAX_SIZE,
  });

  if (!isValid) {
    return;
  }

  setState({
    dealersToDelete: mergedList,
    selectedDealersFromList: EMPTY_ARRAY,
    isListCheckBox: false,
  });
};

const handleDeleteDealers = async ({ getState, setState }) => {
  const { dealersToDelete } = getState();
  setState({ isDeleteLoading: true });

  const payload = formatPayload(dealersToDelete);
  const result = await bulkDecommissionDealersAction(payload);

  if (result) {
    setState({ dealersToDelete: EMPTY_ARRAY });
  }

  setState({ showDeleteModal: false, isDeleteLoading: false });
};

const handleRedirect = ({ params = EMPTY_OBJECT }) => {
  const { values = EMPTY_OBJECT } = params;
  const { tenantId = EMPTY_STRING, dealerId = EMPTY_STRING } = values;
  openWindowInNewTab(
    `/${DEALER_SETUP_BASE_PATH}/${FEATURES.DECOMMISSION}/${BASE_PATHS.ARC}/delete-dealer-setup/details/${tenantId}/${dealerId}`
  );
};

const handleSearchFieldChange = ({ setState, params = EMPTY_OBJECT }) =>
  setState({ searchField: _get(params, 'value', EMPTY_STRING) });

const handleSearchApply = async ({ getState, setState, params = EMPTY_OBJECT }) => {
  const {
    pageSize,
    nextPageToken,
    searchField,
    searchText: prevSearchText,
    currentPage,
    previousPageTokens,
  } = getState();
  const searchText = _get(params, 'value', EMPTY_STRING);

  setState({ isHistoryDataLoading: true });

  let currToken = nextPageToken;
  let currPage = currentPage;
  let prevTokens = [...previousPageTokens];

  if (prevSearchText !== searchText) {
    currToken = null;
    currPage = 1;
    prevTokens = [];
  }

  const {
    decommissionedDealers = EMPTY_ARRAY,
    nextPageToken: currNextPageToken = 0,
    totalCount: decommissionedDealersTotalCount,
  } = await fetchDecommissionHistory({
    pageSize,
    nextPageToken: currToken,
    [searchField]: searchText,
  });

  prevTokens.push(currNextPageToken);

  setState({
    decommissionedDealers,
    decommissionedDealersTotalCount,
    nextPageToken: currNextPageToken,
    currentPage: currPage,
    previousPageTokens: prevTokens,
    searchText,
    isHistoryDataLoading: false,
  });
};

const handleTableRefresh = async ({ getState, setState }) => {
  setState({ isHistoryDataLoading: true });

  const { pageSize, searchField, searchText, previousPageTokens } = getState();
  const currentPageToken = _last(previousPageTokens);
  const {
    decommissionedDealers = EMPTY_ARRAY,
    nextPageToken: currNextPageToken = 0,
    totalCount: decommissionedDealersTotalCount,
  } = await fetchDecommissionHistory({
    pageSize,
    nextPageToken: currentPageToken,
    [searchField]: searchText,
  });

  setState({
    decommissionedDealers,
    decommissionedDealersTotalCount,
    nextPageToken: currNextPageToken,
    isHistoryDataLoading: false,
  });
};
const handleRefreshEligibleDealers = async ({ setState, getState }) => {
  const { eligibleDealersSearchField, eligibleDealersSearchValue, selectedStatuses, selectedDealersFromList } =
    getState();
  setState({ isEligibleDealersLoading: true });

  const { eligibleDealersToDelete } = await fetchEligibleDealers({ pageSize: COLAPSIBLE_PANEL_PAGE_SIZE });

  const {
    statusFilteredDealers: filteredEligibleDealers,
    searchFilteredDealers: eligibleDealersToRender,
    groupedByStatus,
  } = filterTapDealers({
    dealers: eligibleDealersToDelete,
    selectedStatuses,
    searchField: eligibleDealersSearchField,
    searchValue: eligibleDealersSearchValue,
  });

  setState({
    eligibleDealersToDelete,
    groupedByStatus,
    filteredEligibleDealers,
    eligibleDealersToRender,
    isEligibleDealersLoading: false,
    isListCheckBox:
      _size(selectedDealersFromList) === _size(eligibleDealersToRender) && _size(eligibleDealersToRender) > 0,
  });
};

const handleUpdateEligibleDealersSearchField = ({ setState, params = EMPTY_OBJECT }) => {
  const { value: eligibleDealersSearchField } = params;
  setState({ eligibleDealersSearchField });
};

const handleEligibleDealersSearchApply = ({ setState, getState, params = EMPTY_OBJECT }) => {
  const { value: searchValue } = params;
  const { eligibleDealersSearchField: searchField, filteredEligibleDealers } = getState();

  if (searchValue === '' || !searchField || !searchValue) {
    setState({
      eligibleDealersToRender: filteredEligibleDealers,
      eligibleDealersSearchValue: searchValue,
      selectedDealersFromList: EMPTY_ARRAY,
      isListCheckBox: false,
    });
    return;
  }

  const eligibleDealersToRender = searchDealers({ dealers: filteredEligibleDealers, searchField, searchValue });

  setState({
    eligibleDealersToRender,
    selectedDealersFromList: EMPTY_ARRAY,
    isListCheckBox: false,
    eligibleDealersSearchValue: searchValue,
  });
};

const handleStatusFilter = ({ setState, getState, params = EMPTY_OBJECT }) => {
  const { selectedOption } = params;
  const {
    groupedByStatus: prevGroupedByStatus,
    eligibleDealersToDelete,
    eligibleDealersSearchValue,
    eligibleDealersSearchField,
  } = getState();

  setState({ selectedStatuses: selectedOption });

  const {
    statusFilteredDealers: filteredEligibleDealers,
    searchFilteredDealers: eligibleDealersToRender,
    groupedByStatus,
  } = filterTapDealers({
    dealers: eligibleDealersToDelete,
    selectedStatuses: selectedOption,
    searchField: eligibleDealersSearchField,
    searchValue: eligibleDealersSearchValue,
    groupedByStatus: prevGroupedByStatus,
  });

  setState({
    filteredEligibleDealers,
    eligibleDealersToRender,
    groupedByStatus,
    selectedDealersFromList: EMPTY_ARRAY,
    isListCheckBox: false,
  });
};

export default {
  [CUSTOM_ACTIONS.SET_INITIAL_DATA]: setInitialData,
  [FORM_ACTION_TYPES.ON_FIELD_CHANGE]: handleChange,
  [CUSTOM_ACTIONS.TOGGLE_DRAWER]: handleToggleDrawer,
  [CUSTOM_ACTIONS.TAB_CHANGE]: handleTabChange,
  [CUSTOM_ACTIONS.REMOVE_DEALER]: handleRemoveDealer,
  [CUSTOM_ACTIONS.REMOVE_DEALERS_MODAL]: handleOpenRemoveModal,
  [DELETE_CUSTOM_ACTIONS.DELETE_DEALER_MODAL]: handleOpenDeleteModal,
  [CUSTOM_ACTIONS.CLOSE_MODAL]: handleModalClose,
  [CUSTOM_ACTIONS.SELECT_TAP_REQUEST]: handleSelectDealersFromList,
  [CUSTOM_ACTIONS.ADD_TAP_DEALERS]: handleAddSelectedDealersFromList,
  [CUSTOM_ACTIONS.REFRESH_REQUESTS]: handleRefreshEligibleDealers,
  [CUSTOM_ACTIONS.UPDATE_SEARCH_FIELD]: handleUpdateEligibleDealersSearchField,
  [CUSTOM_ACTIONS.SEARCH_APPLY]: handleEligibleDealersSearchApply,
  [CUSTOM_ACTIONS.STATUS_FILTER_CHANGE]: handleStatusFilter,
  [DELETE_CUSTOM_ACTIONS.DELETE_DEALERS]: handleDeleteDealers,
  [DELETE_CUSTOM_ACTIONS.REDIRECT]: handleRedirect,
  [TABLE_ACTION_TYPES.TABLE_SEARCH_FIELD]: handleSearchFieldChange,
  [TABLE_ACTION_TYPES.TABLE_ITEMS_PAGE_UPDATE]: handlePageUpdate,
  [TABLE_ACTION_TYPES.TABLE_SEARCH]: handleSearchApply,
  [TABLE_ACTION_TYPES.TABLE_ITEMS_REFRESH]: handleTableRefresh,
};
