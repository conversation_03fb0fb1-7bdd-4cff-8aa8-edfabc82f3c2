import { mapProps } from 'recompose';

import { Date<PERSON><PERSON><PERSON>, Status<PERSON><PERSON><PERSON> } from '@tekion/tekion-components/molecules/CellRenderers';
import ButtonRenderer from '@tekion/tekion-components/src/molecules/CellRenderers/buttonRenderer';
import { Button } from '@tekion/tekion-components/atoms';
import makeCellRenderer from '@tekion/tekion-components/molecules/CellRenderers/makeCellRenderer';

import { DEALER_ID_COLUMN, TENANT_ID_COLUMN } from 'constants/columns.constants';
import { DEALER_SETUP_BASE_PATH, FEATURES, STATUS_VS_COLOR, STATUS_VS_LABEL } from 'constants/general.constants';
import { TABLE_COLUMN_WIDTH } from 'pages/dashboard/dashboard.constants';

const DateCellRenderer = makeCellRenderer(DateRenderer);

export const HEADER_PROPS = {
  label: __('Disable Dealer'),
  hasBack: true,
  goBackTo: `/${DEALER_SETUP_BASE_PATH}/${FEATURES.DECOMMISSION}`,
};

export const DELETE_CUSTOM_ACTIONS = {
  DELETE_DEALERS: 'DELETE_DEALERS',
  SELECT_ELIGIBLE_DEALERS: 'SELECT_ELIGIBLE_DEALERS',
  REDIRECT: 'REDIRECT',
  DELETE_DEALER_MODAL: 'DELETE_DEALER_MODAL',
};

export const TABS = {
  SELECT_DEALERS: 'SELECT_DEALERS',
  HISTORY: 'HISTORY',
};

export const TABLE_KEYS = {
  REDIRECT: 'redirect',
  CREATED_TIME: 'createdTime',
  MODIFIED_TIME: 'modifiedTime',
  STATUS: 'requestStatus',
};

export const REDIRECT_COLUMN = {
  Header: __('Redirect'),
  Cell: ButtonRenderer,
  getProps: ({ onAction }) => ({
    icon: 'icon-redirect',
    view: Button.VIEW.ICON,
    actionType: DELETE_CUSTOM_ACTIONS.REDIRECT,
    onAction,
  }),
  id: TABLE_KEYS.REDIRECT,
  width: TABLE_COLUMN_WIDTH.XSMALL,
};

export const STATUS_COLUMN = {
  Header: __('Status'),
  Cell: mapProps(({ value }) => ({
    data: value,
    labelMap: STATUS_VS_LABEL,
    colorMap: STATUS_VS_COLOR,
  }))(StatusRenderer),
  accessor: TABLE_KEYS.STATUS,
  id: TABLE_KEYS.STATUS,
};

export const CREATED_TIME_COLUMN = {
  Header: __('Created Time'),
  Cell: DateCellRenderer,
  accessor: TABLE_KEYS.CREATED_TIME,
  id: TABLE_KEYS.CREATED_TIME,
};

export const MODIFIED_TIME_COLUMN = {
  Header: __('Modified Time'),
  Cell: DateCellRenderer,
  accessor: TABLE_KEYS.MODIFIED_TIME,
  id: TABLE_KEYS.MODIFIED_TIME,
};

export const DECOMMISSIONED_COLUMNS = [
  TENANT_ID_COLUMN,
  DEALER_ID_COLUMN,
  STATUS_COLUMN,
  CREATED_TIME_COLUMN,
  MODIFIED_TIME_COLUMN,
  REDIRECT_COLUMN,
];

export const SELECTED_LIST_MAX_SIZE = 100;

export const FETCH_TENANTS_PAYLOAD = { limit: '1500' };

export const SELECT_ALL = 'ALL';

export const DISABLED_DATE = 'disabledDate';
