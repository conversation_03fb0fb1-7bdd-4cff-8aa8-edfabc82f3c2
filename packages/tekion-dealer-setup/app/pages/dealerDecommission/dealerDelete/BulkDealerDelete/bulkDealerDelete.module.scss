@use "tstyles/component.scss";

%font {
  font-family: component.$font-regular;
  font-size: component.$font-size-medium;
}

%flexContainer {
  @include component.flex($justify-content: flex-end, $align-items: center);
  margin-bottom: 0.8rem;
  margin-right: 1.6rem;
}

.label {
  @extend %font;
  color: component.$black;
  margin-bottom: 0.4rem;
}

.info {
  @extend %font;
}

.bodyContainer {
  @include component.flex();
}

.tableContainer {
  max-height: 60rem;
  height: 60rem;
  overflow-y: scroll;
}

.tabWrapper {
  :global(.ant-tabs-tab) {
    font-family: component.$font-medium;
    font-size: component.$font-size-large;
  }

  :global(.ant-tabs-bar.ant-tabs-top-bar) {
    background: transparent !important;
    padding: 0 2rem 0 2rem;
  }

  :global(.ant-tabs-content) {
    height: 100%;
  }
}

.tabPaneContent {
  width: 100%;
}

.tableWrapper {
  :global(.rt-tbody) {
    max-height: 45rem;
    height: 45rem;
    overflow-y: scroll;
  }
}

.headingWithGap {
  @include component.flex($align-items: center);
  gap: 2rem;
}
