import React, { useCallback, useContext, useEffect, useMemo } from 'react';

import PropTypes from 'prop-types';
import { compose } from 'recompose';
import { connect } from 'react-redux';

import _noop from 'lodash/noop';
import _size from 'lodash/size';
import _isEmpty from 'lodash/isEmpty';
import _map from 'lodash/map';

import { DEFAULT_PAGE_SIZE } from '@tekion/tekion-base/constants/tableConstants';
import { DEALER_SETUP } from '@tekion/tekion-base/constants/appServices';
import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import SaveComponent from '@tekion/tekion-components/src/molecules/SaveComponent';
import { AccessContext } from '@tekion/tekion-components/src/widgets/accessHelper';
import { ACCESS_KEYS } from '@tekion/tekion-components/src/widgets/accessHelper/constants/access.permission';
import { BaseTable } from '@tekion/tekion-components/src/molecules/table';
import Page from '@tekion/tekion-components/molecules/pageComponent/PageComponent';
import Heading from '@tekion/tekion-components/src/atoms/Heading';
import Loader from '@tekion/tekion-components/molecules/loader';
import Tabs from '@tekion/tekion-components/src/molecules/Tabs/Tabs';
import FormBuilder from '@tekion/tekion-components/organisms/FormBuilder';
import Modal from '@tekion/tekion-components/src/molecules/Modal';
import withActions from '@tekion/tekion-components/src/connectors/withActions';
import TableManager, { TABLE_TYPES } from '@tekion/tekion-components/src/organisms/TableManager';

import { FORM_SECTIONS } from 'pages/dealerDecommission/dealerDisable/dealerDisable.sections';
import TapRequestCard from 'pages/dealerDecommission/dealerDisable/components/tapRequestCard/TapRequestCard';
import { getDealerDisableFields } from 'pages/dealerDecommission/dealerDisable/dealerDisable.config';
import {
  TABLE_MANAGER_PROPS,
  TABLE_PROP_CONSTANTS,
  CUSTOM_ACTIONS,
  SEARCHABLE_OPTIONS,
  SEARCH_OPTION_IDS,
  FILTER_OPTIONS,
  DEFAULT_SEARCH_FIELD,
  COLAPSIBLE_PANEL_MANAGER_PROPS,
} from 'pages/dealerDecommission/dealerDisable/dealerDisable.constants';
import { SELECTED_LIST_TABLE_COLUMNS } from 'pages/dealerDecommission/dealerDisable/dealerDisable.columns';
import {
  validateUpdateCreateAccessForDecommision,
  getTableProps,
} from 'pages/dealerDecommission/dealerDecommision.helpers';
import CollapsiblePanelManager from 'organisms/collapsiblePanelManager';
import {
  createHeaderProps,
  createFilterProps,
  createCountProps,
  createSearchProps,
  createRefreshProps,
  createCollapsiblePanelProps,
} from 'organisms/collapsiblePanelManager/collapsiblePanelManager.helpers';
import ACTION_HANDLERS from './bulkDealerDelete.actionHandlers';
import {
  HEADER_PROPS,
  TABS,
  DELETE_CUSTOM_ACTIONS,
  DECOMMISSIONED_COLUMNS,
  SELECT_ALL,
  DISABLED_DATE,
  SELECTED_LIST_MAX_SIZE,
} from './bulkDealerDelete.constants';

import styles from './bulkDealerDelete.module.scss';

const BulkDealerDelete = props => {
  const {
    onAction,
    values,
    errors,
    tenantsList,
    tenantDealerMap,
    decommissionedDealers,
    decommissionedDealersTotalCount,
    selectedDealersFromList,
    dealersToDelete,
    selectedTab,
    showRequestListDrawer,
    showRemoveAllModal,
    showDeleteModal,
    isListCheckBox,
    isEligibleDealersLoading,
    eligibleDealersSearchField,
    eligibleDealersToRender,
    selectedStatuses,
    isPageLoading,
    isDealersForTenantLoading,
    isDeleteLoading,
    pageSize,
    currentPage,
    nextPageToken,
    searchField,
    isHistoryDataLoading,
  } = props;

  const accessContext = useContext(AccessContext);
  const [hasCreateAccess, hasUpdateAccess] = useMemo(
    () => validateUpdateCreateAccessForDecommision(ACCESS_KEYS.BLOCK_DEALER, accessContext),
    [accessContext]
  );

  const hasEditAccess = hasCreateAccess || hasUpdateAccess;

  const formFields = useMemo(
    () => getDealerDisableFields({ tenantsList, tenantDealerMap, values, hasEditAccess, isDealersForTenantLoading }),
    [tenantsList, tenantDealerMap, values, hasEditAccess, isDealersForTenantLoading]
  );

  const isDealersToDeleteEmpty = _isEmpty(dealersToDelete);

  const historyTableProps = useMemo(
    () =>
      getTableProps({
        totalNumberOfEntries: decommissionedDealersTotalCount,
        currentPage,
        pageSize,
        type: TABLE_TYPES.WITHOUT_CHECKBOX,
        keyField: 'id',
        loading: isHistoryDataLoading,
        showPagination: true,
        rowHeight: TABLE_PROP_CONSTANTS.ROW_HEIGHT,
        minRows: 0,
      }),
    [decommissionedDealersTotalCount, currentPage, pageSize, isHistoryDataLoading]
  );

  useEffect(() => {
    onAction({ type: CUSTOM_ACTIONS.SET_INITIAL_DATA });
  }, [onAction]);

  const handleTabChange = useCallback(
    tabKey => {
      onAction({ type: CUSTOM_ACTIONS.TAB_CHANGE, payload: { tabKey } });
    },
    [onAction]
  );

  const handleOpenRemoveModal = useCallback(() => {
    onAction({ type: CUSTOM_ACTIONS.REMOVE_DEALERS_MODAL });
  }, [onAction]);

  const handleOpenDeleteModal = useCallback(() => {
    onAction({ type: DELETE_CUSTOM_ACTIONS.DELETE_DEALER_MODAL });
  }, [onAction]);

  const closeModal = useCallback(() => {
    onAction({ type: CUSTOM_ACTIONS.CLOSE_MODAL });
  }, [onAction]);

  const handleRemoveAllDealers = useCallback(() => {
    onAction({ type: CUSTOM_ACTIONS.REMOVE_DEALER, payload: { value: SELECT_ALL } });
  }, [onAction]);

  const handleSelectAllEligibleDealers = useCallback(() => {
    onAction({ type: CUSTOM_ACTIONS.SELECT_TAP_REQUEST, payload: { value: SELECT_ALL } });
  }, [onAction]);

  const handleAddListSelectedDealers = useCallback(() => {
    onAction({ type: CUSTOM_ACTIONS.ADD_TAP_DEALERS });
  }, [onAction]);

  const handleDeleteDealers = useCallback(() => {
    onAction({ type: DELETE_CUSTOM_ACTIONS.DELETE_DEALERS });
  }, [onAction]);

  const handleRefreshEligibleDealers = useCallback(() => {
    onAction({ type: CUSTOM_ACTIONS.REFRESH_REQUESTS });
  }, [onAction]);

  const handleStatusFilterChange = useCallback(
    ({ value }) => {
      onAction({ type: CUSTOM_ACTIONS.STATUS_FILTER_CHANGE, payload: { selectedOption: value } });
    },
    [onAction]
  );

  const handleEligibleDealersSearchFieldChange = useCallback(
    value => {
      onAction({ type: CUSTOM_ACTIONS.UPDATE_SEARCH_FIELD, payload: { value } });
    },
    [onAction]
  );

  const handleEligibleDealersSearchApply = useCallback(
    value => {
      onAction({ type: CUSTOM_ACTIONS.SEARCH_APPLY, payload: { value } });
    },
    [onAction]
  );

  const filterOptions = useMemo(
    () => _map(FILTER_OPTIONS, option => ({ ...option, isDisabled: isEligibleDealersLoading })),
    [isEligibleDealersLoading]
  );

  const colapsiblePanelProps = useMemo(
    () =>
      createCollapsiblePanelProps({
        leftPanelManagerProps: COLAPSIBLE_PANEL_MANAGER_PROPS,
        headerProps: createHeaderProps({
          title: __('Dealers List'),
          checked: isListCheckBox,
          onSelectAll: handleSelectAllEligibleDealers,
          disabled: _isEmpty(eligibleDealersToRender) || !hasEditAccess || isEligibleDealersLoading,
          buttonLabel: __('Add'),
          onButtonClick: handleAddListSelectedDealers,
          buttonDisabled: _isEmpty(selectedDealersFromList) || !hasEditAccess,
          showCheckbox: true,
          showButton: true,
        }),
        filterProps: createFilterProps({
          label: __('Status:'),
          options: filterOptions,
          values: selectedStatuses,
          onChange: handleStatusFilterChange,
          shouldSortOptions: true,
        }),
        countProps: createCountProps({
          selectedCount: selectedDealersFromList.length,
          totalCount: eligibleDealersToRender.length,
          showTotalCount: true,
          showSelectedCount: true,
        }),
        searchProps: createSearchProps({
          onSearch: handleEligibleDealersSearchApply,
          searchableFieldsOptions: SEARCHABLE_OPTIONS,
          onChangeSearchField: handleEligibleDealersSearchFieldChange,
          searchField: eligibleDealersSearchField,
          placeholder: __('Search...'),
        }),
        refreshProps: createRefreshProps({
          disabled: isEligibleDealersLoading,
          onClick: handleRefreshEligibleDealers,
        }),
        isActive: showRequestListDrawer,
      }),
    [
      isEligibleDealersLoading,
      eligibleDealersToRender,
      isListCheckBox,
      filterOptions,
      handleSelectAllEligibleDealers,
      hasEditAccess,
      handleAddListSelectedDealers,
      selectedDealersFromList,
      selectedStatuses,
      handleStatusFilterChange,
      handleEligibleDealersSearchApply,
      handleEligibleDealersSearchFieldChange,
      eligibleDealersSearchField,
      handleRefreshEligibleDealers,
      showRequestListDrawer,
    ]
  );

  const renderConfirmationModal = () => {
    const actionText = showRemoveAllModal ? __('Remove') : __('Delete');
    const submitAction = showRemoveAllModal ? handleRemoveAllDealers : handleDeleteDealers;

    return (
      <Modal
        visible={showRemoveAllModal || showDeleteModal}
        title={__('{{actionText}} Dealers', { actionText })}
        submitBtnText={actionText}
        onCancel={closeModal}
        onSubmit={submitAction}
        primaryBtnDisabled={!hasEditAccess}
        destroyOnClose>
        <Heading size={5}>
          {__('Are you sure you want to {{actionText}} the selected dealers ?', { actionText })}
        </Heading>
      </Modal>
    );
  };

  const renderEligibleDealerCards = useCallback(() => {
    if (isEligibleDealersLoading) return <Loader />;
    if (_isEmpty(eligibleDealersToRender))
      return <div className={`full-width full-height flex flex-center ${styles.label}`}>{__('No rows found')}</div>;
    return (
      <>
        {_map(eligibleDealersToRender, item => (
          <TapRequestCard
            key={item.dealerId}
            disabled={!hasEditAccess}
            request={item}
            showAge
            ageKey={DISABLED_DATE}
            onAction={onAction}
            selectedList={selectedDealersFromList}
          />
        ))}
      </>
    );
  }, [onAction, hasEditAccess, isEligibleDealersLoading, eligibleDealersToRender, selectedDealersFromList]);

  if (isPageLoading) return <Loader />;

  return (
    <Page className="full-height full-width">
      <Page.Header {...HEADER_PROPS}>
        <Heading className="pl-2">{__('Decommission: Bulk Dealer Delete')}</Heading>
      </Page.Header>
      <Page.Body className={`full-height full-width ${styles.bodyContainer}`}>
        <CollapsiblePanelManager {...colapsiblePanelProps}>{renderEligibleDealerCards()}</CollapsiblePanelManager>
        <div className="full-height full-width overflow-hidden">
          <Tabs
            className={`full-width full-height ${styles.tabWrapper}`}
            activeKey={selectedTab}
            onChange={handleTabChange}>
            <Tabs.TabPane className={styles.tabPaneContent} tab={__('Select Dealers')} key={TABS.SELECT_DEALERS}>
              <div className="m-24">
                <div className="p-l-12">
                  <FormBuilder
                    onAction={onAction}
                    values={values}
                    fields={formFields}
                    sections={FORM_SECTIONS}
                    errors={errors}
                  />
                  <div>
                    <Heading className={`${styles.headingWithGap} p-l-12`} size={3}>
                      <div>{__('Selected Dealers')}</div>
                      <div className={styles.separator} />
                      <div className={styles.info}>
                        {__('{{dealersToDeleteCount}} Result(s)', { dealersToDeleteCount: _size(dealersToDelete) })}
                      </div>
                    </Heading>
                    <div className="full-height full-width p-12">
                      <BaseTable
                        className={styles.tableWrapper}
                        data={dealersToDelete}
                        totalNumberOfEntries={_size(dealersToDelete)}
                        rowHeight={TABLE_PROP_CONSTANTS.ROW_HEIGHT}
                        tableManagerProps={TABLE_MANAGER_PROPS}
                        columns={SELECTED_LIST_TABLE_COLUMNS}
                        pageSize={SELECTED_LIST_MAX_SIZE}
                        onAction={onAction}
                        showPagination={false}
                      />
                      <SaveComponent
                        isPrimaryDisabled={isDealersToDeleteEmpty}
                        isSecondaryDisabled={isDealersToDeleteEmpty}
                        primaryActionLoading={isDeleteLoading}
                        secondaryButtonLabel={__('Reset')}
                        primaryButtonLabel={__('Delete')}
                        onSecondaryAction={handleOpenRemoveModal}
                        onPrimaryAction={handleOpenDeleteModal}
                      />
                    </div>
                  </div>
                </div>
                {renderConfirmationModal()}
              </div>
            </Tabs.TabPane>
            <Tabs.TabPane className={styles.tabPaneContent} tab={__('History')} key={TABS.HISTORY}>
              <div className="m-24">
                <div className="p-l-12">
                  <div>
                    <div className="full-height full-width p-8">
                      <TableManager
                        tableProps={historyTableProps}
                        containerClassName={styles.tableContainer}
                        data={decommissionedDealers}
                        columns={DECOMMISSIONED_COLUMNS}
                        onAction={onAction}
                        tokenPagination
                        nextPageToken={nextPageToken}
                        searchField={searchField}
                        searchableFieldsOptions={SEARCHABLE_OPTIONS}
                        tableManagerProps={TABLE_MANAGER_PROPS}
                        tableClassName={styles.tableContainer}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </Tabs.TabPane>
          </Tabs>
        </div>
      </Page.Body>
    </Page>
  );
};

BulkDealerDelete.propTypes = {
  onAction: PropTypes.func,
  values: PropTypes.object,
  errors: PropTypes.object,
  tenantsList: PropTypes.array,
  tenantDealerMap: PropTypes.object,
  eligibleDealersToDelete: PropTypes.array,
  decommissionedDealers: PropTypes.array,
  decommissionedDealersTotalCount: PropTypes.number,
  selectedDealersFromList: PropTypes.array,
  dealersToDelete: PropTypes.array,
  selectedTab: PropTypes.string,
  showRequestListDrawer: PropTypes.bool,
  showRemoveAllModal: PropTypes.bool,
  showDeleteModal: PropTypes.bool,
  isListCheckBox: PropTypes.bool,
  isPageLoading: PropTypes.bool,
  isDealersForTenantLoading: PropTypes.bool,
  isDeleteLoading: PropTypes.bool,
  pageSize: PropTypes.number,
  currentPage: PropTypes.number,
  nextPageToken: PropTypes.string,
  searchField: PropTypes.string,
  isHistoryDataLoading: PropTypes.bool,
  eligibleDealersSearchField: PropTypes.string,
  eligibleDealersToRender: PropTypes.array,
  selectedStatuses: PropTypes.array,
  isEligibleDealersLoading: PropTypes.bool,
};

BulkDealerDelete.defaultProps = {
  onAction: _noop,
  values: EMPTY_OBJECT,
  errors: EMPTY_OBJECT,
  tenantsList: EMPTY_ARRAY,
  tenantDealerMap: EMPTY_OBJECT,
  eligibleDealersToDelete: EMPTY_ARRAY,
  decommissionedDealers: EMPTY_ARRAY,
  decommissionedDealersTotalCount: 0,
  selectedDealersFromList: EMPTY_ARRAY,
  dealersToDelete: EMPTY_ARRAY,
  selectedTab: TABS.SELECT_DEALERS,
  showRequestListDrawer: false,
  showRemoveAllModal: false,
  showDeleteModal: false,
  isListCheckBox: false,
  isPageLoading: false,
  isDealersForTenantLoading: false,
  isDeleteLoading: false,
  pageSize: DEFAULT_PAGE_SIZE,
  currentPage: 1,
  nextPageToken: null,
  searchField: SEARCH_OPTION_IDS.TENANT_ID,
  isHistoryDataLoading: false,
  eligibleDealersSearchField: SEARCH_OPTION_IDS.TENANT_ID,
  eligibleDealersToRender: EMPTY_ARRAY,
  selectedStatuses: EMPTY_ARRAY,
  isEligibleDealersLoading: false,
};

const mapStateToProps = ({ [DEALER_SETUP]: state }) => {
  const { globalData = EMPTY_OBJECT } = state;
  const { tenantsList = EMPTY_ARRAY, tenantDealerMap = EMPTY_OBJECT } = globalData;

  return {
    tenantsList,
    tenantDealerMap,
  };
};

const mapDispatchToProps = dispatch => ({
  dispatch,
});
export default compose(
  connect(mapStateToProps, mapDispatchToProps),
  withActions({ searchField: DEFAULT_SEARCH_FIELD, eligibleDealersSearchField: DEFAULT_SEARCH_FIELD }, ACTION_HANDLERS)
)(BulkDealerDelete);
