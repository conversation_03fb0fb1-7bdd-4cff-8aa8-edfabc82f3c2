import queryString from 'query-string';
import { defaultMemoize } from 'reselect';
import _castArray from 'lodash/castArray';
import _compact from 'lodash/compact';
import _flow from 'lodash/flow';
import _isEmpty from 'lodash/isEmpty';
import _map from 'lodash/map';
import _nthArg from 'lodash/nthArg';
import _partial from 'lodash/partial';
import _reduce from 'lodash/reduce';
import _uniq from 'lodash/uniq';
import _get from 'lodash/get';
import _head from 'lodash/head';
import _filter from 'lodash/filter';
import _find from 'lodash/find';
import _merge from 'lodash/merge';
import _pick from 'lodash/pick';
import _isNull from 'lodash/isNull';
import _isUndefined from 'lodash/isUndefined';
import _some from 'lodash/some';
import _isArray from 'lodash/isArray';
import _size from 'lodash/size';
import _flatten from 'lodash/flatten';
import _keys from 'lodash/keys';
import _forEach from 'lodash/forEach';
import _includes from 'lodash/includes';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING, NO_DATA } from '@tekion/tekion-base/app.constants';
import OPERATORS from '@tekion/tekion-base/constants/filterOperators';
import {
  AUDIT_ID,
  AUDIT_ID_LIBRARY,
  AUDIT_ID_LIBRARY_PACKAGE,
  AUDITS_LABEL,
  AUDIT_ID_FORM_ANNOTATION,
  AUDIT_ID_FORMS_LIBRARY_FORM,
  AUDIT_ID_FORMS_LIBRARY_FORM_SETUP,
  AUDIT_ID_FORM_ACTION,
  AUDIT_ID_FORMS_IN_PACKAGE,
  AUDIT_ID_PACKAGE_ACTION,
  AUDIT_ID_PACKAGE_LEVEL_FORMS,
  AUDIT_ID_PACKAGE_PUBLISH,
  FORM_STATE,
  MODULE_TARGETTING_TYPES,
} from '@tekion/tekion-base/constants/formConfigurator/constants';
import { getEnvironmentVariables } from '@tekion/tekion-base/helpers/envHelper';
import TEnvReader from '@tekion/tekion-base/readers/Env';
import { toMoment } from '@tekion/tekion-base/utils/dateUtils';
import { fetchUsers, fetchUserByIds, fetchAndSearchUsersByPersona } from 'tbusiness/actions/userActions';
import { getAuditFilters } from 'tbusiness/utils/formConfigurator/formConfigurator.utils';
import { FORMS_AUDITS_FILTER_TYPES } from '@tekion/tekion-widgets/src/organisms/formConfiguratorTool/FormConfiguratorTool.constants';
import { getLookupByKeys } from '@tekion/tekion-base/services/lookupService';
import { RESOURCE_TYPE } from '@tekion/tekion-base/bulkResolvers';
import { getLoggedInUserId } from '@tekion/tekion-base/utils/userUtils';
import { FORM_SOURCE } from 'tbase/constants/deal/formSetup';
import ACTION_TYPES from '../actionTypes/common';
import {
  DEFAULT_PAYLOAD_COUNTRY_STATES,
  FORM_FIELD,
  PACKAGE_FIELD,
  FORM_STATUS,
  DEALERS_ASSOCIATED_COLUMNS_IDS,
  ENVS,
  FORM_TYPES_FIELD_TYPE,
  DEALER_FORMS_TYPE,
} from '../constants/common';
import { FORM_LIBRARY_VIEW_TYPES, FORM_TYPE } from '../constants';
import { FILTER_IDS as DEALER_FORM_FILTER_IDS } from '../formsDetail/components/dealersAssociated/dealersAssociated.constants';
import { FILTER_IDS as PACKAGES_FILTER_IDS } from '../formsDetail/components/packagesAssociated/packagesAssociated.constants';
import FormManagementService from '../services/formManagement';
import OEMService from '../services/oem';
import { getFormattedFilters } from './table';
import { BUILD_ENV_MAP, BUILD_OPTIONS, ENV_CONFIG } from '../constants/envConfig';
import { getFormStatus } from '../dealershipFormsList/utils/dealershipFormsList.utils';
import { isAppHondaOem } from '../utils/formsLibrary.utils';

export const getActions = onAction => {
  const createAction = (type, payload) => onAction({ type, payload });
  return {
    showPublishModal: _partial(createAction, ACTION_TYPES.SHOW_PUBLISH_MODAL),
    hidePublishModal: _partial(createAction, ACTION_TYPES.HIDE_PUBLISH_MODAL),
    showVersionHistoryModal: _partial(createAction, ACTION_TYPES.SHOW_VERSION_HISTORY_MODAL),
    hideVersionHistoryModal: _partial(createAction, ACTION_TYPES.HIDE_VERSION_HISTORY_MODAL),
    downloadPdfAction: _partial(createAction, ACTION_TYPES.DOWNLOAD_PDF),
    showArchiveDealerSelection: _partial(createAction, ACTION_TYPES.SHOW_ARCHIVE_DEALER_SELECTION),
    showArchiveConfirmationDialog: _partial(createAction, ACTION_TYPES.SHOW_ARCHIVE_CONFIRMATION_DIALOG),
    hideArchiveConfirmationDialog: _partial(createAction, ACTION_TYPES.HIDE_ARCHIVE_CONFIRMATION_DIALOG),
    showCreateCopyDialog: _partial(createAction, ACTION_TYPES.SHOW_CREATE_COPY_DIALOG),
    hideCreateCopyDialog: _partial(createAction, ACTION_TYPES.HIDE_CREATE_COPY_DIALOG),
    sendFormForApproval: _partial(createAction, ACTION_TYPES.SEND_FOR_APPROVAL),
    showAuditLogs: _partial(createAction, ACTION_TYPES.SHOW_AUDIT_LOGS),
    hideAuditLogs: _partial(createAction, ACTION_TYPES.HIDE_AUDIT_LOGS),
    showDealersAssociatedModal: _partial(createAction, ACTION_TYPES.SHOW_DEALERS_ASSOCIATED_MODAL),
    hideDealersAssociatedModal: _partial(createAction, ACTION_TYPES.HIDE_DEALERS_ASSOCIATED_MODAL),
  };
};

const LOOKUP_API_PARAMS_ALL_USERS = {
  lookUpSearchApi: _flow(_nthArg(1), fetchUsers),
  lookupByKeysApi: _flow(_nthArg(1), fetchUserByIds),
};

const LOOKUP_API_PARAMS = {
  lookUpSearchApi: _flow(_nthArg(1), fetchAndSearchUsersByPersona),
  lookupByKeysApi: _flow(_nthArg(1), fetchUserByIds),
};

export const getUserLookupsParams = ({
  personas = EMPTY_ARRAY,
  additionalFilters = EMPTY_ARRAY,
  shouldAddPersonaFilter = true,
}) => ({
  ...(shouldAddPersonaFilter ? LOOKUP_API_PARAMS : LOOKUP_API_PARAMS_ALL_USERS),
  filters: [...(shouldAddPersonaFilter ? [{ values: personas, field: 'personas' }] : []), ...additionalFilters],
});

const convertArrayToMap = options => _reduce(options, (acc, ele) => ({ ...acc, [ele.code]: ele.name }), {});

export const getCountries = async env => {
  try {
    const payload = {
      env,
      request: DEFAULT_PAYLOAD_COUNTRY_STATES,
    };
    const response = await FormManagementService.fetchAllCountries(payload);
    return convertArrayToMap(response);
  } catch {
    return EMPTY_OBJECT;
  }
};

export const getStates = async (countries, env) => {
  try {
    const payload = {
      env,
      request: {
        ...DEFAULT_PAYLOAD_COUNTRY_STATES,
        country: countries ?? undefined,
      },
    };
    const response = await FormManagementService.fetchAllStates(payload);
    return convertArrayToMap(response);
  } catch {
    return EMPTY_OBJECT;
  }
};

export const getStateNames = defaultMemoize(async data => {
  const countries = _uniq(_compact(_map(data, ele => ele.country || ele.dealerSharedWith?.country)));
  if (_isEmpty(countries)) return EMPTY_OBJECT;
  const response = await FormManagementService.fetchStateNames(countries);
  return response;
});

export const getFormsDetailByKeyPayload = defaultMemoize(formKey => ({
  filters: [
    {
      field: FORM_FIELD.FORM_KEY,
      operator: OPERATORS.IN,
      values: [formKey],
    },
  ],
}));

export const getDealersAssocFilterPayloadForForms = ({ selectedFilters = EMPTY_ARRAY, id = EMPTY_ARRAY }) => {
  const formIds = _castArray(id);
  const filters = [
    ...selectedFilters,
    {
      type: DEALER_FORM_FILTER_IDS.FORM_ID,
      operator: OPERATORS.IN,
      values: formIds,
    },
    {
      type: DEALER_FORM_FILTER_IDS.ACTIVE,
      operator: OPERATORS.IN,
      values: [true],
    },
  ];
  return getFormattedFilters(filters);
};

export const getDealersAssocProdFilterPayload = ({ selectedFilters = EMPTY_ARRAY, id = EMPTY_ARRAY }) => {
  const formIds = _castArray(id);
  const env = getCurrentEnv();
  const filters = [
    ...selectedFilters,
    {
      type: DEALER_FORM_FILTER_IDS.FORM_ID,
      operator: OPERATORS.IN,
      values: formIds,
    },
    {
      type: DEALER_FORM_FILTER_IDS.ACTIVE,
      operator: OPERATORS.IN,
      values: [true],
    },
    {
      type: DEALER_FORM_FILTER_IDS.ENV,
      operator: OPERATORS.IN,
      values: [env],
    },
  ];
  return getFormattedFilters(filters);
};

export const getDealersAssocFilterPayloadForPackages = ({ selectedFilters = EMPTY_ARRAY, id = EMPTY_ARRAY }) => {
  const packageIds = _castArray(id);
  const filters = [
    ...selectedFilters,
    {
      type: PACKAGE_FIELD.PACKAGE_ID,
      operator: OPERATORS.IN,
      values: packageIds,
    },
  ];
  return getFormattedFilters(filters);
};

export const getPackageByNamePayload = packageName => ({
  filters: [
    {
      field: PACKAGE_FIELD.PACKAGE_NAME,
      operator: OPERATORS.IN,
      values: [packageName],
    },
  ],
});

export const getPackagesAssocFilterPayload = ({ selectedFilters = EMPTY_ARRAY, formKeys = EMPTY_ARRAY }) => {
  const formIds = _castArray(formKeys);
  const filters = [
    ...selectedFilters,
    {
      type: PACKAGES_FILTER_IDS.FORM_ID,
      operator: OPERATORS.IN,
      values: formIds,
    },
  ];
  return getFormattedFilters(filters);
};

export const getPackageFormsFilterPayload = ({ selectedFilters = EMPTY_ARRAY, formKeys = EMPTY_ARRAY }) => {
  const _formKeys = _castArray(formKeys);
  const filters = [
    ...selectedFilters,
    ...(!_isEmpty(_formKeys)
      ? [{ type: FORM_FIELD.FORM_KEY, values: _formKeys, operator: OPERATORS.IN }]
      : [{ type: FORM_FIELD.FORM_KEY, values: [null], operator: OPERATORS.IN }]),
  ];
  return getFormattedFilters(filters);
};

export const checkIfValidPackageName = async value => {
  const packageLists = await FormManagementService.searchPackageLists(getPackageByNamePayload(value));
  const isValidPackageName = !_get(packageLists, 'count');
  return isValidPackageName;
};

export const getEnvList = () => {
  const buildEnv = TEnvReader.buildEnv(getEnvironmentVariables());
  return BUILD_OPTIONS[buildEnv];
};

export const getCurrentEnv = () => {
  const buildEnv = TEnvReader.buildEnv(getEnvironmentVariables());
  return BUILD_ENV_MAP[buildEnv];
};

export const getDefaultEnvValue = () => {
  const defaultEnv = _head(getEnvList());
  if (defaultEnv) return defaultEnv.value;
  return EMPTY_STRING;
};

export const makeQueryParamsVsFormType = ({ formType, env, dealerId, tenantId, formKey, country }) =>
  formType === FORM_TYPE.DEALER_FORM
    ? encodeURI(queryString.stringify({ env, dealerId, tenantId, formType, formKey, country }))
    : encodeURI(queryString.stringify({ formType }));

export const getQueryParams = (location = EMPTY_OBJECT) => {
  const decodedQueryParams = decodeURI(location?.search);
  return queryString.parse(decodedQueryParams);
};

const ASSET_TYPES_LIST = {
  [FORM_LIBRARY_VIEW_TYPES.PACKAGES]: [
    AUDIT_ID_LIBRARY_PACKAGE,
    AUDIT_ID_PACKAGE_ACTION,
    AUDIT_ID_PACKAGE_LEVEL_FORMS,
    AUDIT_ID_PACKAGE_PUBLISH,
  ],
  [FORM_LIBRARY_VIEW_TYPES.FORMS]: [
    AUDIT_ID_FORMS_LIBRARY_FORM,
    AUDIT_ID_FORM_ANNOTATION,
    AUDIT_ID_FORMS_LIBRARY_FORM_SETUP,
    AUDIT_ID_FORM_ACTION,
    AUDIT_ID_FORMS_IN_PACKAGE,
  ],
  [FORM_LIBRARY_VIEW_TYPES.DEALER_FORMS]: [
    AUDIT_ID,
    AUDIT_ID_FORMS_LIBRARY_FORM,
    AUDIT_ID_FORM_ANNOTATION,
    AUDIT_ID_FORMS_LIBRARY_FORM_SETUP,
    AUDIT_ID_FORM_ACTION,
    AUDIT_ID_FORMS_IN_PACKAGE,
  ],
};

const handleAssetRequest = (viewType, assetId) =>
  _map(_get(ASSET_TYPES_LIST, viewType), assetType => ({
    assetType,
    assetId,
  }));

export const getAuditTabs = (auditLog, viewType = FORM_LIBRARY_VIEW_TYPES.FORMS) => {
  const { assetId } = auditLog;
  return [
    {
      id: viewType === FORM_LIBRARY_VIEW_TYPES.PACKAGES ? AUDIT_ID_LIBRARY_PACKAGE : AUDIT_ID_LIBRARY,
      label: AUDITS_LABEL,
      filterTypes:
        viewType === FORM_LIBRARY_VIEW_TYPES.PACKAGES ? EMPTY_ARRAY : getAuditFilters(FORMS_AUDITS_FILTER_TYPES),
      requests: handleAssetRequest(viewType, assetId),
    },
  ];
};

export const getPdfLibraryFormsKeys = formDetailsInPackages => {
  const { hits } = formDetailsInPackages;
  return _uniq(
    _map(
      _filter(hits, ({ pdfLibraryPdfKey }) => !!pdfLibraryPdfKey),
      ({ pdfLibraryPdfKey }) => pdfLibraryPdfKey
    )
  );
};

const getPDFMetadataForForm = (form, pdfLibraryMetadata) =>
  _find(pdfLibraryMetadata, metadata => _get(form, 'pdfLibraryPdfKey') === _get(metadata, 'pdfLibraryPdfKey')) ||
  EMPTY_OBJECT;

export const addPDFLibraryMetadataToForms = (formsResponse, pdfLibraryMetadata) => {
  const { hits: forms } = formsResponse;
  return {
    ...formsResponse,
    hits: _map(forms, form => _merge(form, getPDFMetadataForForm(form, pdfLibraryMetadata))),
  };
};

const getFormsInfoForPackage = (packageDetails, formsInfo) =>
  _map(_get(packageDetails, PACKAGE_FIELD.FORM_DETAILS, EMPTY_ARRAY), ({ formId }) =>
    _find(formsInfo, form => _get(form, 'formKey') === formId)
  );

const handleFormStatus = (form = EMPTY_OBJECT) =>
  toMoment().isAfter(_get(form, 'effectiveDate')) &&
  toMoment().isBefore(_get(form, 'expiryDate')) &&
  (_get(form, 'formState') === FORM_STATE.READY_TO_PUBLISH || _get(form, 'formState') === FORM_STATE.PUBLISHED)
    ? 'ACTIVE'
    : 'INACTIVE';

export const isFormInActive = form => handleFormStatus(form) === 'INACTIVE';

export const hasInActiveForms = formList => _some(formList, isFormInActive);

export const addFormsInfoToPackage = (packagesResponse, formsResponse) => {
  const { hits: packages } = packagesResponse;
  const { hits: formsInfo } = formsResponse;
  return {
    ...packagesResponse,
    hits: _map(_castArray(packages), packageDetails =>
      _merge(packageDetails, {
        formsInfo: getFormsInfoForPackage(packageDetails, formsInfo),
      })
    ),
  };
};

export const getFormSelectionStatusPayload = ({ formValues, globalMetaData }) => {
  const { COUNTRIES, STATES, FORM_TYPES, OEMS, TARGETED_MODULES } = FORM_FIELD;

  return {
    allCountriesSelected: _size(_get(formValues, COUNTRIES)) === _size(_get(globalMetaData, COUNTRIES)),
    allStatesSelected: _size(_get(formValues, STATES)) === _size(_flatten(_map(_get(globalMetaData, STATES), _keys))),
    allFormTypesSelected: _size(_get(formValues, FORM_TYPES)) === _size(_get(globalMetaData, FORM_TYPES)),
    allOemsSelected: _size(_get(formValues, OEMS)) === _size(_get(globalMetaData, OEMS)),
    allUsagesSelected: _size(_get(formValues, TARGETED_MODULES)) === _size(MODULE_TARGETTING_TYPES),
  };
};

export const getRefreshFormTagPayload = ({ formValues, globalMetaData }) => {
  const { LICENSED_BY, COUNTRIES, STATES, FORM_TYPES, OEMS, TARGETED_MODULES } = FORM_FIELD;
  return {
    ..._pick(formValues, [LICENSED_BY, COUNTRIES, STATES, FORM_TYPES, OEMS]),
    ...getFormSelectionStatusPayload({ formValues, globalMetaData }),
    usages: _get(formValues, TARGETED_MODULES),
  };
};

export const getDisplayNameFromResolvedUsers = (userId, resolvedUsers) => {
  let displayName;
  if (_isNull(userId)) {
    displayName = null;
  } else if (_isNull(resolvedUsers?.[userId]?.displayName) || _isUndefined(resolvedUsers?.[userId]?.displayName)) {
    displayName = userId;
  } else {
    displayName = resolvedUsers?.[userId]?.displayName;
  }
  return displayName;
};

export const getParsedFormList = formList =>
  _map(formList, form => ({
    ...form,
    status: FORM_STATUS[handleFormStatus(form)],
  }));

export const addPackageDataToForms = (packageDetails, formsResponse) => {
  const { hits: formsInfo } = formsResponse;
  return {
    ...formsResponse,
    hits: _map(formsInfo, form => {
      const packagesData = _isArray(packageDetails) ? _head(packageDetails) : packageDetails;
      const { addedBy, addedOn } =
        _find(
          _get(packagesData, PACKAGE_FIELD.FORM_DETAILS, EMPTY_ARRAY),
          packageData => _get(packageData, 'formId') === _get(form, 'formKey')
        ) || EMPTY_OBJECT;
      return {
        ...form,
        addedBy,
        addedOn,
      };
    }),
  };
};

export const isHigherEnvParamPassed = () => {
  const queryParams = new URLSearchParams(window.location.search);
  return queryParams.get('env') === 'higherEnv';
};

export const getHigherEnvKey = () => {
  const currentEnv = getDefaultEnvValue();
  if (currentEnv === ENVS.PROD || currentEnv === ENVS.PREPROD) {
    return ENV_CONFIG.PROD;
  }
  return ENV_CONFIG.STG;
};

export const getFieldValue = (payload, getState) => (field, original, value) => {
  if (field === DEALERS_ASSOCIATED_COLUMNS_IDS.STATUS) {
    const { id } = original;
    const { itemsToDisable, isRestore } = payload;
    const { dealersAssociatedModal } = getState();

    const { mappingData } = dealersAssociatedModal;

    const _item = _find(mappingData, item => item.mapId === id);

    const formStatus = getFormStatus({ ..._item });

    if (itemsToDisable.includes(id)) {
      return isRestore ? formStatus.label : __('Archived');
    }

    return isRestore ? __('Archived') : formStatus.label;
  }

  if (field === DEALERS_ASSOCIATED_COLUMNS_IDS.TENANT_NAME) {
    return _get(original, 'dealerSharedWith.tenantId');
  }
  return value;
};

export const getUserIdsToResolveFromResponse = (list = EMPTY_ARRAY) => {
  const userIds = [];
  _forEach(list, ({ modifiedBy, createdBy, addedBy }) => {
    userIds.push(createdBy);
    userIds.push(modifiedBy);
    userIds.push(addedBy);
  });
  const loggedInUserId = getLoggedInUserId();
  userIds.push(loggedInUserId);
  return _uniq(_compact(userIds));
};

export const getResolvedUsersResponseFromAPI = async (response = EMPTY_OBJECT) => {
  const { hits } = response;
  const userIdsToResolve = getUserIdsToResolveFromResponse(hits);
  const resolvedUsers = isAppHondaOem()
    ? await OEMService.getOEMUsersById(userIdsToResolve)
    : await getLookupByKeys(RESOURCE_TYPE.TENANT_USER_MINIMAL_V2, userIdsToResolve);
  const formsRespsonseWithResolvedUsers = [];

  _forEach(hits, item => {
    formsRespsonseWithResolvedUsers.push({
      ...item,
      modifiedByDisplayName: getDisplayNameFromResolvedUsers(item?.modifiedBy || item?.lastModifiedBy, resolvedUsers),
      createdByDisplayName: getDisplayNameFromResolvedUsers(item?.createdBy, resolvedUsers),
      publishedDisplayName: getDisplayNameFromResolvedUsers(item?.lastPublishedBy, resolvedUsers),
      addedByDisplayName: getDisplayNameFromResolvedUsers(item?.addedBy, resolvedUsers),
    });
  });

  return { ...response, hits: formsRespsonseWithResolvedUsers };
};

export const isTenantForm = form => _includes(_get(form, FORM_TYPES_FIELD_TYPE), DEALER_FORMS_TYPE);

export const getFormProgrammingType = form => {
  const formSource = _get(form, 'formSource');
  const formVersion = _get(form, 'version');

  const formCreatedInLibrary = formSource === FORM_SOURCE.FORM_LIBRARY;
  const formCreatedInDealership = formSource === FORM_SOURCE.FORM_PRO;

  if (formCreatedInLibrary && formVersion === 1 && !isTenantForm(form)) return FORM_TYPE.STANDARD;

  if (formCreatedInLibrary && formVersion > 1 && !isTenantForm(form)) return FORM_TYPE.CUSTOM;

  if (formCreatedInDealership || (formCreatedInLibrary && isTenantForm(form))) return FORM_TYPE.DEALERSHIP;

  return NO_DATA;
};
