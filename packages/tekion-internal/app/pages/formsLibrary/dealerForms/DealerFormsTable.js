import React, { useMemo, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import _noop from 'lodash/noop';
import _get from 'lodash/get';
import _startCase from 'lodash/startCase';

import { tget } from '@tekion/tekion-base/utils/general';
import { EMPTY_OBJECT, EMPTY_ARRAY, EMPTY_STRING, NO_DATA } from '@tekion/tekion-base/app.constants';
import MODULE_ASSET_TYPES from '@tekion/tekion-base/constants/moduleAssetTypes';
import Page from '@tekion/tekion-components/src/molecules/pageComponent';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import Loader from '@tekion/tekion-components/src/molecules/loader';
import TableManager, { TABLE_TYPES } from '@tekion/tekion-widgets/src/organisms/tableManager';
import { DEFAULT_FILTER_GROUP } from '@tekion/tekion-components/src/organisms/filterSection';

import { FORM_LIBRARY_VIEW_TYPES } from '../constants';
import ACTION_TYPES from './dealerFormsTable.actionTypes';
import { getColumnsByConfig, getPageInfo, getTableProps } from '../helpers/table';
import { getDealerFormsListConfig } from '../molecules/kebabAction/kebabAction.config';
import { TABLE_MANAGER_PROPS } from './dealerFormsTable.constants';
import { getSubHeaderRightActions, getFilterProps } from './dealerFormsTable.helpers';
import { getColumnConfig } from './dealerFormsTable.config';
import Header from './components/header';
import KebabAction from '../molecules/kebabAction';
import { dealerFormsListActionHandler } from '../molecules/kebabAction/kebabAction.helpers';
import { hasDealerFormsEdit } from '../permissions/formsLibrary.permissions';
import useFormPlatformMediaV3URL from '../hooks/useFormPlatformMediaV3URL';

import styles from './dealerFormsTable.module.scss';

const DealerFormsTable = ({
  contentHeight,
  onAction,
  payload,
  resizedProps,
  isLoading,
  total,
  actions,
  columns,
  columnMetaData,
  data,
  columnConfigurator,
  isFetchingDealerFormslist,
  commonActions,
  selectedFilters,
  selectedFilterGroup,
  searchText,
  sortDetails,
  navigate,
  selectedFormEnv,
  permissions,
  params,
  filterServices,
  ...restProps
}) => {
  useFormPlatformMediaV3URL();
  const { formKey } = params;
  const isDealerFormsEditMode = hasDealerFormsEdit(permissions);

  useEffect(() => {
    onAction({ type: ACTION_TYPES.HANDLE_DEALER_FORMS_PAGE_MOUNT, payload: { formKey } });
    actions.fetchMetaData(MODULE_ASSET_TYPES.DEALER_FORMS_LIST);
  }, EMPTY_ARRAY);

  const onSearchTextChange = useCallback(
    searchString => {
      const { rows } = tget(payload, 'request.pageInfo', EMPTY_OBJECT);

      onAction({
        type: ACTION_TYPES.DEALER_FORMS_SEARCH_TEXT,
        payload: {
          searchText: searchString,
          ...getPageInfo(1, rows),
        },
      });
    },
    [onAction, payload]
  );

  const tableProps = useMemo(() => {
    const { current, rows } = tget(payload, 'request.pageInfo', EMPTY_OBJECT);
    return {
      ...getTableProps({
        count: total,
        currentPage: current,
        itemsPerPage: rows,
        resizedProps,
        loading: isLoading,
        type: TABLE_TYPES.WITH_CHECKBOX,
      }),
    };
  }, [isLoading, payload, total, resizedProps]);

  const subHeaderProps = useMemo(
    () => ({
      rightHeaderClassName: styles.rightSubHeader,
      subHeaderRightActions: getSubHeaderRightActions({ handleSearchInputChange: onSearchTextChange, searchText }),
    }),
    [onSearchTextChange, searchText]
  );

  const getRowActionCell = useCallback(
    ({ original }) => (
      <KebabAction
        config={getDealerFormsListConfig(isDealerFormsEditMode)}
        onClick={dealerFormsListActionHandler}
        params={{
          formData: original,
          commonActions,
          selectedViewType: FORM_LIBRARY_VIEW_TYPES.DEALER_FORMS,
          navigate,
          formKey,
          selectedEnv: selectedFormEnv,
        }}
      />
    ),
    [commonActions, navigate, formKey, selectedFormEnv, isDealerFormsEditMode]
  );

  const columnConfiguratorConfig = useMemo(
    () => ({ ...columnConfigurator, Cell: getRowActionCell, className: 'p-0' }),
    [columnConfigurator, getRowActionCell]
  );

  const getPdfKey = rowInfo => <div className={styles.ellipses}>{_get(rowInfo, 'value')}</div>;

  const getPdfName = rowInfo => <div className={styles.ellipses}>{_get(rowInfo, 'value')}</div>;

  const getCreatedBy = rowInfo => _startCase(_get(rowInfo, 'original.createdDisplayName')) || NO_DATA;

  const getUpdatedBy = rowInfo => _startCase(_get(rowInfo, 'original.updatedDisplayName')) || NO_DATA;

  const getColumns = useCallback(
    () =>
      getColumnsByConfig({
        columns,
        columnMetaData,
        columnConfigurator: columnConfiguratorConfig,
        config: getColumnConfig({ getPdfKey, getPdfName, getCreatedBy, getUpdatedBy }),
      }),
    [columns, columnMetaData, columnConfiguratorConfig]
  );
  const filterProps = getFilterProps({ selectedFilters, selectedFilterGroup, filterServices });

  return (
    <PropertyControlledComponent controllerProperty={!isFetchingDealerFormslist} fallback={<Loader />}>
      <Page>
        <Header onAction={onAction} navigate={navigate} isDealerFormsEditMode={isDealerFormsEditMode} {...restProps} />
        <Page.Body className="full-width" style={{ height: contentHeight }}>
          <TableManager
            onAction={onAction}
            tableProps={tableProps}
            tableManagerProps={TABLE_MANAGER_PROPS}
            subHeaderProps={subHeaderProps}
            columns={getColumns()}
            data={data}
            filterProps={filterProps}
            sortDetails={sortDetails}
            isMultiSort
          />
        </Page.Body>
      </Page>
    </PropertyControlledComponent>
  );
};

DealerFormsTable.propTypes = {
  contentHeight: PropTypes.number,
  navigate: PropTypes.func.isRequired,
  payload: PropTypes.object.isRequired,
  resizedProps: PropTypes.object.isRequired,
  actions: PropTypes.object.isRequired,
  columnMetaData: PropTypes.object.isRequired,
  columns: PropTypes.array.isRequired,
  columnConfigurator: PropTypes.object.isRequired,
  data: PropTypes.object,
  onAction: PropTypes.func,
  isLoading: PropTypes.bool,
  total: PropTypes.number,
  isFetchingDealerFormslist: PropTypes.bool,
  commonActions: PropTypes.object,
  selectedFilters: PropTypes.array,
  selectedFilterGroup: PropTypes.string,
  searchText: PropTypes.string,
  sortDetails: PropTypes.object,
  selectedFormEnv: PropTypes.string,
  permissions: PropTypes.array,
};

DealerFormsTable.defaultProps = {
  contentHeight: 0,
  onAction: _noop,
  isLoading: false,
  total: 0,
  data: EMPTY_OBJECT,
  isFetchingDealerFormslist: true,
  commonActions: EMPTY_OBJECT,
  selectedFilters: EMPTY_ARRAY,
  selectedFilterGroup: DEFAULT_FILTER_GROUP,
  searchText: EMPTY_STRING,
  sortDetails: EMPTY_OBJECT,
  selectedFormEnv: EMPTY_STRING,
  permissions: EMPTY_ARRAY,
};

export default DealerFormsTable;
