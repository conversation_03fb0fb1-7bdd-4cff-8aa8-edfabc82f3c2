import _noop from 'lodash/noop';
import _head from 'lodash/head';
import _find from 'lodash/find';
import _isNil from 'lodash/isNil';
import _mapValues from 'lodash/mapValues';

import { tget } from '@tekion/tekion-base/utils/general';
import MODULE_ASSET_TYPES from '@tekion/tekion-base/constants/moduleAssetTypes';
import { DEFAULT_FILTER_BEHAVIOR } from '@tekion/tekion-components/src/organisms/filterSection/constants/filterSection.constants';
import SearchInputWithState from '@tekion/tekion-widgets/src/appServices/sales/molecules/searchInputWithState';

import { getFilters } from './dealerFormsTable.config';
import { DEALER_FORMS_FILTERS_ID } from './dealerFormsTable.constants';
import { PLATFORMS } from '../constants/common';
import { getDefaultEnvValue } from '../helpers/common';

export const getFilterTypes = () => {
  const FILTERS = getFilters();
  return {
    filterTypes: [
      FILTERS.ADDED_BY,
      FILTERS.MODIFIED_BY,
      FILTERS.MODIFIED_ON,
      FILTERS.ENV,
      FILTERS.MODIFIED,
      FILTERS.STATUS,
    ], // TODO: FILTERS.PLATFORM to be added once all platform available
    defaultFilterTypes: [DEALER_FORMS_FILTERS_ID.ENV], // TODO: DEALER_FORMS_FILTERS_ID.PLATFORM to be added once all platform available
  };
};

const getExtraStateUpdatesOnFiltersChange = state => {
  const { values: filterValues } = state;
  const newValues = _mapValues(filterValues, filterValue => {
    const { type, values } = filterValue;
    if (type === DEALER_FORMS_FILTERS_ID.ENV && _isNil(values)) {
      return {
        ...filterValue,
        values: [getDefaultEnvValue()],
      };
    }
    if (type === DEALER_FORMS_FILTERS_ID.PLATFORM && _isNil(values)) {
      return {
        ...filterValue,
        values: [PLATFORMS.ARC],
      };
    }
    return filterValue;
  });
  return { values: newValues };
};

export const getFilterProps = ({ selectedFilters, selectedFilterGroup }) => ({
  ...getFilterTypes(),
  assetType: MODULE_ASSET_TYPES.DEALER_FORMS_LIST,
  defaultFilterBehavior: DEFAULT_FILTER_BEHAVIOR.GENERAL,
  showDefaultFilter: true,
  appliedFilterGroup: selectedFilterGroup,
  selectedFilters,
  getExtraStateUpdatesOnFiltersChange,
  shouldCloseOnOutsideClick: true,
  shouldCloseMultiSortOnOutsideClick: true,
  shouldClearMultiSortOnReset: true,
});

export const getSubHeaderRightActions = ({ handleSearchInputChange, searchText }) => [
  {
    renderer: SearchInputWithState,
    renderOptions: {
      onSearchTextChange: handleSearchInputChange,
      onPressEnter: _noop,
      placeholder: __('Search Form'),
      shouldDebounce: true,
      searchText,
    },
  },
];

export const getSelectedEnv = selectedFilters => {
  const envFilter = _find(selectedFilters, ({ type }) => type === DEALER_FORMS_FILTERS_ID.ENV);
  const values = tget(envFilter, 'values');
  return _head(values);
};

export const checkIfEnvFilterSelected = selectedFilters => {
  const envValue = getSelectedEnv(selectedFilters);
  return _isNil(envValue);
};
