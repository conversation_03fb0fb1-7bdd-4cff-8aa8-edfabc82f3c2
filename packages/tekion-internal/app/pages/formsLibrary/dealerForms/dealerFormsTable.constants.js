import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { DEFAULT_FILTER_GROUP } from '@tekion/tekion-components/src/organisms/filterSection';
import { isAppHondaOem } from '../utils/formsLibrary.utils';

export const TABLE_MANAGER_PROPS = {
  isSearchFocused: false,
  showSearch: false,
  showFilter: true,
  showHeader: false,
  showSubHeader: true,
  showMultiSort: !isAppHondaOem(),
};

export const DEALER_FORMS_FILTERS_ID = {
  MODIFIED_BY: 'modifiedBy',
  MODIFIED_ON: 'modifiedTime',
  ADDED_BY: 'createdBy',
  ENV: 'ENV',
  PLATFORM: 'PLATFORM',
  STATUS: 'STATUS',
  ORIGINAL_FORM_KEY: 'originalFormKey',
  MODIFIED: 'modified',
  FORM_SOURCE: 'formSource',
};

export const COLUMN_IDS = {
  MODIFIED_ON: 'modifiedOn',
  MODIFIED_BY: 'modifiedBy',
  ADDED_BY: 'addedBy',
  ADDED_ON: 'addedOn',
  FORM_NAME: 'formName',
  DEALER_NAME: 'dealerName',
  MODIFIED: 'modified',
  PDF_NAME: 'pdfName',
  PDF_KEY: 'formKey',
  STATUS: 'status',
};

export const INITIAL_STATE = {
  selectedFilters: EMPTY_ARRAY,
  selectedFilterGroup: DEFAULT_FILTER_GROUP,
  sortDetails: EMPTY_OBJECT,
  searchText: EMPTY_STRING,
};

export const FILTER_OPTIONS = {
  [COLUMN_IDS.MODIFIED]: [
    { label: __('Yes'), value: true },
    { label: __('No'), value: false },
  ],
};

export const STATUS_FILTER_VALUES = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  ARCHIVED: 'ARCHIVED',
};

export const STATUS_FILTER_OPTIONS = [
  { label: __('Active'), value: STATUS_FILTER_VALUES.ACTIVE },
  { label: __('Inactive'), value: STATUS_FILTER_VALUES.INACTIVE },
  { label: __('Archived'), value: STATUS_FILTER_VALUES.ARCHIVED },
];

export const FORM_LIBRARY = 'FORM_LIBRARY';
