import _property from 'lodash/property';
import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { URL_TYPES } from '@tekion/tekion-base/constants/api';
import HTTP from '@tekion/tekion-base/services/apiService/httpClient';
import getDataFromResponse from '@tekion/tekion-base/utils/getDataFromResponse';
import { getAdditionalFormsLibraryHeaders } from '@tekion/tekion-business/src/utils/formConfigurator/formConfigurator.utils';
import { isAppHondaOem } from '../utils/formsLibrary.utils';

const getResponse = _property('data.data.data');

const BASE_URL = 'formpro-api/global/formmgmt';
const OEM_BASE_URL = '/oem-formpro/u';

export const ROUTES = {
  FETCH_TABLE_METADATA: `${BASE_URL}/metadata`,
  PUBLISH_FORMS: `${BASE_URL}/form/publish`,
  DEALER_VERSION_FORM: `${BASE_URL}/form`,
  GET_DEALERS: `${BASE_URL}/get-dealers`,
  GET_COUNTRIES: `${BASE_URL}/get-countries`,
  GET_STATES: `${BASE_URL}/get-states`,
  GET_COUNTRY_NAMES: `${BASE_URL}/get-country-names`,
  GET_STATE_NAMES: `${BASE_URL}/get-state-names`,
  SEARCH_PACKAGE_LIST: `${BASE_URL}/package/search`,
  CREATE_PACKAGE: `${BASE_URL}/package/create`,
  CREATE_LABELS: `${BASE_URL}/package/labels/create`,
  SEARCH_LABELS: `${BASE_URL}/package/labels/search`,
  FETCH_PACKAGE: `${BASE_URL}/package/fetch`,
  PUBLISH_PACKAGES: `${BASE_URL}/package/publish`,
  ADD_FORMS: `${BASE_URL}/package/addForms`,
  REMOVE_FORMS: `${BASE_URL}/package/removeForms`,
  UPDATE_PACKAGE: `${BASE_URL}/package/update`,
  DEALER_FORMS_LIST_META_DATA: `${BASE_URL}/metadata`,
  DEALER_FORMS_LIST: `${BASE_URL}/dealerforms/search`,
  DEALER_MODIFIED_FORM_COUNT: `${BASE_URL}/dealerforms/modifiedcount`,
  DEALER_MODIFIED_FORM_COUNT_FOR_PROD: `${BASE_URL}/dealerforms/lowerAndHigherEnv/modifiedCount`,
  DEALER_ASSOCIATED_PROD_COUNT: `${BASE_URL}/associatedDealerForms`,
  CREATE_NEW_GLOBAL_FORM: `${BASE_URL}/dealerforms/createdealerform`,
  FETCH_DEALER_FORM: `${BASE_URL}/dealerforms/fetch`,
  SEARCH_ARCHIVED_PACKAGES_LIST: `${BASE_URL}/archivedPackages/search`,
  ARCHIVE_PACKAGE: `${BASE_URL}/archive`,
  RESTORE_PACKAGE: `${BASE_URL}/restore`,
  FETCH_DEALERS: `${BASE_URL}/get-all-platform-dealers`,
  PACKAGE_ASSOCIATED_DEALERS: `${BASE_URL}/package/associated-dealers`,
  ASSOCIATED_DEALERS: `${BASE_URL}/associatedDealerDetails`,
};

export const OEM_ROUTES = {
  FETCH_TABLE_METADATA: `${OEM_BASE_URL}/metadata`,
  DEALER_MODIFIED_FORM_COUNT: `${OEM_BASE_URL}/dealerforms/modifiedcount`,
  DEALER_VERSION_FORM: `${OEM_BASE_URL}/form`,
  SEARCH_LABELS: `${OEM_BASE_URL}/package/labels/search`,
  CREATE_LABELS: `${OEM_BASE_URL}/package/labels/create`,
  ADD_FORMS: `${OEM_BASE_URL}/package/addForms`,
  SEARCH_PACKAGE_LIST: `${OEM_BASE_URL}/package/search`,
  CREATE_PACKAGE: `${OEM_BASE_URL}/package/create`,
  FETCH_PACKAGE: `${OEM_BASE_URL}/package/fetch`,
  PUBLISH_PACKAGES: `${OEM_BASE_URL}/package/publish`,
  REMOVE_FORMS: `${OEM_BASE_URL}/package/removeForms`,
  UPDATE_PACKAGE: `${OEM_BASE_URL}/package/update`,
  PACKAGE_ASSOCIATED_DEALERS: `${OEM_BASE_URL}/package/associated-dealers`,
  SEARCH_ARCHIVED_PACKAGES_LIST: `${OEM_BASE_URL}/archivedPackages/search`,
  ARCHIVE_PACKAGE: `${OEM_BASE_URL}/archive`,
  RESTORE_PACKAGE: `${OEM_BASE_URL}/restore`,
  DEALER_MODIFIED_FORM_COUNT_FOR_PROD: `${OEM_BASE_URL}/dealerforms/lowerAndHigherEnv/modifiedCount`,
  FETCH_DEALERS: `${OEM_BASE_URL}/get-all-platform-dealers`,
  GET_COUNTRIES: `${OEM_BASE_URL}/get-countries`,
  GET_STATES: `${OEM_BASE_URL}/get-states`,
  GET_COUNTRY_NAMES: `${OEM_BASE_URL}/get-country-names`,
  GET_STATE_NAMES: `${OEM_BASE_URL}/get-state-names`,
  PUBLISH_FORMS: `${OEM_BASE_URL}/form/publish`,
};

const getUrl = key => {
  const routes = isAppHondaOem() ? OEM_ROUTES : ROUTES;
  return routes[key] || ROUTES[key];
};

const headers = getAdditionalFormsLibraryHeaders();

const getModuleType = () => (isAppHondaOem() ? URL_TYPES.OEM_CDMS : URL_TYPES.FORM_PLATFORM);

const fetchMetadata = assetType =>
  HTTP.get(getModuleType(), `${getUrl('FETCH_TABLE_METADATA')}/${assetType}`, EMPTY_OBJECT, headers).then(
    getDataFromResponse
  );

const publishForms = payload => HTTP.post(getModuleType(), getUrl('PUBLISH_FORMS'), payload, EMPTY_OBJECT, headers);

const getDealerFormVersion = payload =>
  HTTP.post(getModuleType(), getUrl('DEALER_VERSION_FORM'), payload, EMPTY_OBJECT, headers).then(getDataFromResponse);

const getDealerAssocProdCount = payload =>
  HTTP.post(getModuleType(), getUrl('DEALER_ASSOCIATED_PROD_COUNT'), payload, EMPTY_OBJECT, headers).then(
    getDataFromResponse
  );

const fetchGlobalDealers = payload =>
  HTTP.post(getModuleType(), getUrl('FETCH_DEALERS'), payload, EMPTY_OBJECT, headers).then(getDataFromResponse);

const fetchAllCountries = payload =>
  HTTP.post(getModuleType(), getUrl('GET_COUNTRIES'), payload, EMPTY_OBJECT, headers).then(getResponse);

const fetchAllStates = payload =>
  HTTP.post(getModuleType(), getUrl('GET_STATES'), payload, EMPTY_OBJECT, headers).then(getResponse);

const fetchCountryNames = () =>
  HTTP.get(getModuleType(), getUrl('GET_COUNTRY_NAMES'), EMPTY_OBJECT, headers).then(getDataFromResponse);

const fetchStateNames = payload =>
  HTTP.post(getModuleType(), getUrl('GET_STATE_NAMES'), payload, EMPTY_OBJECT, headers).then(getDataFromResponse);

const searchPackageLists = payload =>
  HTTP.post(getModuleType(), getUrl('SEARCH_PACKAGE_LIST'), payload, EMPTY_OBJECT, headers).then(getDataFromResponse);

const createPackage = payload =>
  HTTP.post(getModuleType(), getUrl('CREATE_PACKAGE'), payload, EMPTY_OBJECT, headers).then(getDataFromResponse);

const createLabels = payload =>
  HTTP.post(getModuleType(), getUrl('CREATE_LABELS'), payload, EMPTY_OBJECT, headers).then(getDataFromResponse);

const searchLabelLists = payload =>
  HTTP.post(getModuleType(), getUrl('SEARCH_LABELS'), payload, EMPTY_OBJECT, headers).then(getDataFromResponse);

const fetchPackage = packageId =>
  HTTP.get(getModuleType(), `${getUrl('FETCH_PACKAGE')}/${packageId}`, EMPTY_OBJECT, headers).then(getDataFromResponse);

const publishPackages = payload =>
  HTTP.post(getModuleType(), getUrl('PUBLISH_PACKAGES'), payload, EMPTY_OBJECT, headers).then(getDataFromResponse);

const addForms = payload =>
  HTTP.post(getModuleType(), getUrl('ADD_FORMS'), payload, EMPTY_OBJECT, headers).then(getDataFromResponse);

const removeForms = payload =>
  HTTP.post(getModuleType(), getUrl('REMOVE_FORMS'), payload, EMPTY_OBJECT, headers).then(getDataFromResponse);

const renamePackage = payload =>
  HTTP.post(getModuleType(), `${getUrl('UPDATE_PACKAGE')}?activityType=RENAMED`, payload, EMPTY_OBJECT, headers).then(
    getDataFromResponse
  );

const fetchDealerFormslistMetaData = assetType =>
  HTTP.get(getModuleType(), `${getUrl('DEALER_FORMS_LIST_META_DATA')}/${assetType}`, EMPTY_OBJECT, headers).then(
    getDataFromResponse
  );

const searchDealerFormsList = payload =>
  HTTP.post(getModuleType(), getUrl('DEALER_FORMS_LIST'), payload, EMPTY_OBJECT, headers).then(getDataFromResponse);

const getDealerModifiedCount = payload =>
  HTTP.post(getModuleType(), getUrl('DEALER_MODIFIED_FORM_COUNT'), payload, EMPTY_OBJECT, headers).then(
    getDataFromResponse
  );

const getDealerModifiedCountForProd = payload =>
  HTTP.post(getModuleType(), getUrl('DEALER_MODIFIED_FORM_COUNT_FOR_PROD'), payload, EMPTY_OBJECT, headers).then(
    getDataFromResponse
  );

const createNewGlobalForm = payload =>
  HTTP.post(getModuleType(), getUrl('CREATE_NEW_GLOBAL_FORM'), payload, EMPTY_OBJECT, headers).then(
    getDataFromResponse
  );

const fetchDealerForm = payload =>
  HTTP.post(getModuleType(), getUrl('FETCH_DEALER_FORM'), payload, EMPTY_OBJECT, headers).then(getDataFromResponse);

const searchArchivedPackagesList = payload =>
  HTTP.post(getModuleType(), getUrl('SEARCH_ARCHIVED_PACKAGES_LIST'), payload, EMPTY_OBJECT, headers).then(
    getDataFromResponse
  );

const archivePackage = packageId =>
  HTTP.put(getModuleType(), `${getUrl('ARCHIVE_PACKAGE')}/${packageId}`, EMPTY_OBJECT, EMPTY_OBJECT, headers).then(
    getDataFromResponse
  );

const restoreArchivedPackage = packageId =>
  HTTP.put(getModuleType(), `${getUrl('RESTORE_PACKAGE')}/${packageId}`, EMPTY_OBJECT, EMPTY_OBJECT, headers).then(
    getDataFromResponse
  );

const searchPackageAssociatedDealers = payload =>
  HTTP.post(getModuleType(), getUrl('PACKAGE_ASSOCIATED_DEALERS'), payload, EMPTY_OBJECT, headers).then(
    getDataFromResponse
  );

const fetchAssociatedDealerships = payload =>
  HTTP.post(getModuleType(), getUrl('ASSOCIATED_DEALERS'), payload, EMPTY_OBJECT, headers).then(getDataFromResponse);

export default {
  fetchMetadata,
  publishForms,
  getDealerFormVersion,
  fetchGlobalDealers,
  fetchAllCountries,
  fetchAllStates,
  fetchCountryNames,
  fetchStateNames,
  searchPackageLists,
  createPackage,
  createLabels,
  searchLabelLists,
  fetchPackage,
  publishPackages,
  addForms,
  removeForms,
  renamePackage,
  fetchDealerFormslistMetaData,
  searchDealerFormsList,
  getDealerModifiedCount,
  createNewGlobalForm,
  fetchDealerForm,
  searchArchivedPackagesList,
  archivePackage,
  restoreArchivedPackage,
  searchPackageAssociatedDealers,
  getDealerAssocProdCount,
  getDealerModifiedCountForProd,
  fetchAssociatedDealerships,
};
