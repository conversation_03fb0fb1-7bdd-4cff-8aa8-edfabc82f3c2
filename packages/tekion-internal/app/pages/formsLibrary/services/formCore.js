import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import getDataFromResponse from '@tekion/tekion-base/utils/getDataFromResponse';
import HTTP from '@tekion/tekion-base/services/apiService/httpClient';
import { URL_TYPES } from '@tekion/tekion-base/constants/api';
import { tget } from '@tekion/tekion-base/utils/general';
import { getAdditionalFormsLibraryHeaders } from '@tekion/tekion-business/src/utils/formConfigurator/formConfigurator.utils';
import { isAppHondaOem } from '../utils/formsLibrary.utils';

const BASE_URL = '/formpro-api/global/formprocore';
const BASE_URL_OEM = '/oem-formpro/u';

export const ROUTES = {
  SEARCH_FORM_LIST: `${BASE_URL}/globalForm/search`,
  GLOBAL_FIELDS: `${BASE_URL}/global-fields`,
  GET_FORM_ANNOTATION_DETAILS: `${BASE_URL}/globalForm`,
  SAVE_FORM: `${BASE_URL}/globalForm`,
  SAVE_ANNOTATIONS: `${BASE_URL}/globalForm/annotations`,
  PRINTER_TYPES: `${BASE_URL}/printerTypes`,
  GET_CONFIGURATOR_METADATA: `${BASE_URL}/format/metadata`,
  GET_FORMS_METADATA: `${BASE_URL}/formMetaData`,
  UPDATE_FORM_DETAILS: `${BASE_URL}/updateGlobalForm`,
  GET_LEGAL_PROVIDERS: `${BASE_URL}/globalForm/legalProviders`,
  VALIDATE_PRINT_SEQUENCE: `${BASE_URL}/globalForm/maxPrintSequence/`,
  IS_VALID_PDF_NAME: `${BASE_URL}/globalForm`,
  IS_VALID_FORM_NAME: `${BASE_URL}/globalForm`,
  GET_VERSION_LIST: `${BASE_URL}/globalForm`,
  FETCH_VERSION_INFO: `${BASE_URL}/globalForm`,
  RESTORE_VERSION_FORM: `${BASE_URL}/globalForm/restore`,
  GET_FORM_LABELS: `${BASE_URL}/globalForms/details/labels`,
  NEW_PREVIEW_FIELDS: '/print/u/form/getDealerModifiedFormDetails',
  NEW_PREVIEW_URL: '/print/u/form/previewDealerModifiedForm',
  LEGAL_APPROVAL: `${BASE_URL}/legal-approval`,
  SEARCH_ARCHIVED_FORMS_LIST: `${BASE_URL}/archivedForm/search`,
  AUDIT_LOGS: `/audit/service/web/u/v2/audit/dwh`,
  ARCHIVE_FORM: `${BASE_URL}/form`,
  RESTORE_FORM: `${BASE_URL}/form`,
  ARCHIVE_MANY: `${BASE_URL}/archive-forms`,
  RESTORE_MANY: `${BASE_URL}/restore-forms`,
  GET_FILE_URL: '/formprocore/u/downloadFormPdf',
  COPY_FORM: `/formprocore/u/form/copy`,
  GET_MEDIAID_BY_DEALER_PDF: `/formprocore/u/getMediaIdByDealerPdfKey`,
  GET_GLOBAL_METADATA: '/formmgmt/u/metadata/fetch-global-metadata',
  REFRESH_FORM_TAG: `${BASE_URL}/generate/formID`,
  DEALERSHIP_FORM_LIST: `/formprocore/u/dealershipView/form/search`,
  CHECK_FORM_EXISTS: '/formpro-api/dealer/formprocore/formExists',
};

export const OEM_ROUTES = {
  SEARCH_FORM_LIST: `${BASE_URL_OEM}/form/search`,
  GLOBAL_FIELDS: `${BASE_URL_OEM}/global-fields`,
  GET_FORM_ANNOTATION_DETAILS: `${BASE_URL_OEM}`,
  SAVE_FORM: `${BASE_URL_OEM}/form`,
  SAVE_ANNOTATIONS: `${BASE_URL_OEM}/form/annotations`,
  PRINTER_TYPES: `${BASE_URL_OEM}/printerTypes`,
  GET_CONFIGURATOR_METADATA: `${BASE_URL_OEM}/format/metadata`,
  UPDATE_FORM_DETAILS: `${BASE_URL_OEM}/updateForm`,
  GET_LEGAL_PROVIDERS: `${BASE_URL_OEM}/legalProviders`,
  VALIDATE_PRINT_SEQUENCE: `${BASE_URL_OEM}/maxPrintSequence`,
  GET_FORM_LABELS: `${BASE_URL_OEM}/forms/details/labels`,
  GET_FORMS_METADATA: `${BASE_URL_OEM}/formMetaData`,
  GET_GLOBAL_METADATA: `${BASE_URL_OEM}/fetch-global-metadata`,
  LEGAL_APPROVAL: `${BASE_URL_OEM}/legal-approval`,
  GET_VERSION_LIST: `${BASE_URL_OEM}/form`,
  RESTORE_VERSION_FORM: `${BASE_URL_OEM}/form/restore`,
  FETCH_VERSION_INFO: `${BASE_URL_OEM}/form`,
  ARCHIVE_MANY: `${BASE_URL_OEM}/archive-forms`,
  RESTORE_MANY: `${BASE_URL_OEM}/restore-forms`,
  SEARCH_ARCHIVED_FORMS_LIST: `${BASE_URL_OEM}/archivedForm/search`,
  REFRESH_FORM_TAG: `${BASE_URL_OEM}/generate/formID`,
};

const getUrl = key => {
  const routes = isAppHondaOem() ? OEM_ROUTES : ROUTES;
  return routes[key] || ROUTES[key];
};

const headers = getAdditionalFormsLibraryHeaders();

const getModuleType = () => (isAppHondaOem() ? URL_TYPES.OEM_CDMS : URL_TYPES.FORM_PLATFORM);

const searchFormList = (payload = EMPTY_OBJECT) =>
  HTTP.post(getModuleType(), getUrl('SEARCH_FORM_LIST'), payload, EMPTY_OBJECT, headers).then(getDataFromResponse);

const fetchGlobalFields = () =>
  HTTP.get(getModuleType(), getUrl('GLOBAL_FIELDS'), EMPTY_OBJECT, headers).then(getDataFromResponse);

const getFormAnnotationDetails = (id, query) =>
  HTTP.get(getModuleType(), `${getUrl('GET_FORM_ANNOTATION_DETAILS')}/${id}/annotations`, query, headers).then(
    getDataFromResponse
  );

const saveForm = (id, payload = EMPTY_OBJECT, query) =>
  HTTP.put(getModuleType(), `${getUrl('SAVE_FORM')}/${id}`, payload, query, headers).then(getDataFromResponse);

const saveAnnotations = (payload = EMPTY_OBJECT) =>
  HTTP.post(getModuleType(), getUrl('SAVE_ANNOTATIONS'), payload, EMPTY_OBJECT, headers).then(getDataFromResponse);

const updateAnnotations = (id, payload = EMPTY_OBJECT, query) =>
  HTTP.put(getModuleType(), `${getUrl('SAVE_ANNOTATIONS')}/${id}`, payload, query, headers).then(getDataFromResponse);

const fetchPrinterTypes = () =>
  HTTP.get(getModuleType(), getUrl('PRINTER_TYPES'), EMPTY_OBJECT, headers).then(getDataFromResponse);

const getConfiguratorMetadata = () =>
  HTTP.get(getModuleType(), getUrl('GET_CONFIGURATOR_METADATA'), EMPTY_OBJECT, headers).then(getDataFromResponse);

const getFormsMetaData = () =>
  HTTP.get(getModuleType(), getUrl('GET_FORMS_METADATA'), EMPTY_OBJECT, headers).then(getDataFromResponse);

const updateFormDetails = (payload = EMPTY_OBJECT) =>
  HTTP.post(getModuleType(), getUrl('UPDATE_FORM_DETAILS'), payload, EMPTY_OBJECT, headers).then(getDataFromResponse);

const getLegalProviders = () =>
  HTTP.get(getModuleType(), getUrl('GET_LEGAL_PROVIDERS'), EMPTY_OBJECT, headers).then(getDataFromResponse);

const validatePrintSequence = (payload = EMPTY_OBJECT, query) =>
  HTTP.post(getModuleType(), getUrl('VALIDATE_PRINT_SEQUENCE'), payload, query, headers).then(getDataFromResponse);

const isValidPDFName = (pdfName, query) =>
  HTTP.get(getModuleType(), getUrl('IS_VALID_PDF_NAME'), { originalFileName: pdfName, ...query }, headers).then(
    getDataFromResponse
  );

const isValidFormName = (formName, query) =>
  HTTP.get(getModuleType(), getUrl('IS_VALID_FORM_NAME'), { formDisplayKey: formName, ...query }, headers).then(
    getDataFromResponse
  );

const getVersionInfoList = formPDFKey =>
  HTTP.get(getModuleType(), `${getUrl('GET_VERSION_LIST')}/${formPDFKey}/versions`, EMPTY_OBJECT, headers).then(
    getDataFromResponse
  );

const fetchVersionInfo = (dealerPDFKey, version) =>
  HTTP.get(
    getModuleType(),
    `${getUrl('FETCH_VERSION_INFO')}/${dealerPDFKey}/${version}/annotations`,
    EMPTY_OBJECT,
    headers
  ).then(getDataFromResponse);

const restoreFormVersion = (dealerPDFKey, targetVersion) =>
  HTTP.put(
    getModuleType(),
    `${getUrl('RESTORE_VERSION_FORM')}/${dealerPDFKey}/${targetVersion}`,
    EMPTY_OBJECT,
    EMPTY_OBJECT,
    headers
  ).then(getDataFromResponse);

const getFormLabels = query =>
  HTTP.get(getModuleType(), getUrl('GET_FORM_LABELS'), query, headers).then(getDataFromResponse);

const newPreviewFields = payload =>
  HTTP.post(URL_TYPES.CDMS, getUrl('NEW_PREVIEW_FIELDS'), payload).then(getDataFromResponse);

const newPreviewUrl = payload =>
  HTTP.post(URL_TYPES.CDMS, `${ROUTES.NEW_PREVIEW_URL}`, payload).then(getDataFromResponse);

const getFormComments = (id, query) =>
  HTTP.get(getModuleType(), `${getUrl('LEGAL_APPROVAL')}/${id}/review-comments`, query, headers).then(
    getDataFromResponse
  );

const sendFormForApproval = (id, payload, query) =>
  HTTP.put(getModuleType(), `${getUrl('LEGAL_APPROVAL')}/${id}/send-for-approval`, payload, query, headers).then(
    getDataFromResponse
  );

const approveForm = (id, payload) =>
  HTTP.post(getModuleType(), `${getUrl('LEGAL_APPROVAL')}/${id}/approve`, payload, EMPTY_OBJECT, headers).then(
    getDataFromResponse
  );

const sendFormForRevision = (id, payload) =>
  HTTP.post(getModuleType(), `${getUrl('LEGAL_APPROVAL')}/${id}/reject`, payload, EMPTY_OBJECT, headers).then(
    getDataFromResponse
  );

const searchArchivedFormsList = (payload = EMPTY_OBJECT) =>
  HTTP.post(getModuleType(), getUrl('SEARCH_ARCHIVED_FORMS_LIST'), payload, EMPTY_OBJECT, headers).then(
    getDataFromResponse
  );

const archiveForm = dealerPDFKey =>
  HTTP.delete(
    getModuleType(),
    `${ROUTES.ARCHIVE_FORM}/${dealerPDFKey}/archive`,
    EMPTY_OBJECT,
    EMPTY_OBJECT,
    headers
  ).then(getDataFromResponse);

const restoreArchivedForm = dealerPDFKey =>
  HTTP.put(getModuleType(), `${ROUTES.RESTORE_FORM}/${dealerPDFKey}/restore`, EMPTY_OBJECT, EMPTY_OBJECT, headers).then(
    getDataFromResponse
  );

const archiveForms = dealerPDFKeys =>
  HTTP.delete(getModuleType(), getUrl('ARCHIVE_MANY'), dealerPDFKeys, EMPTY_OBJECT, headers).then(getDataFromResponse);

const restoreArchivedForms = dealerPDFKeys =>
  HTTP.put(getModuleType(), getUrl('RESTORE_MANY'), dealerPDFKeys, EMPTY_OBJECT, headers).then(getDataFromResponse);

const fetchAuditLogs = payload =>
  HTTP.post(getModuleType(), `${ROUTES.AUDIT_LOGS}/bulk/details`, payload, EMPTY_OBJECT, headers).then(res => {
    const resultList = tget(res, 'data.resultList') || tget(res, 'data.data');
    const { nextToken, queryExecutionId } = tget(res, 'data', EMPTY_OBJECT);
    return { nextToken, resultList, queryExecutionId };
  });

const fetchAuditBeginningTimestamp = (assetType, assetId) =>
  HTTP.get(getModuleType(), `${ROUTES.AUDIT_LOGS}/${assetType}/${assetId}/startdate`, EMPTY_OBJECT, headers).then(res =>
    tget(res, 'data.logsStartDate')
  );

const getFileURLs = id =>
  HTTP.get(getModuleType(), `${ROUTES.GET_FILE_URL}/${id}`, EMPTY_OBJECT, headers).then(getDataFromResponse);

const copyForm = (payload = EMPTY_OBJECT) =>
  HTTP.post(getModuleType(), `${ROUTES.COPY_FORM}`, payload, EMPTY_OBJECT, headers).then(getDataFromResponse);
// Note: need ot call this in common.js in formsLibrary to get media id of the pdf
const getMediaIdByDealerPdfKey = dealerPDFKey =>
  HTTP.get(getModuleType(), `${ROUTES.GET_MEDIAID_BY_DEALER_PDF}/${dealerPDFKey}`, EMPTY_OBJECT, headers).then(
    getDataFromResponse
  );

const fetchGlobalMetadata = () =>
  HTTP.get(getModuleType(), getUrl('GET_GLOBAL_METADATA'), EMPTY_OBJECT, headers).then(getDataFromResponse);

const refreshFormTag = payload =>
  HTTP.post(getModuleType(), getUrl('REFRESH_FORM_TAG'), payload, EMPTY_OBJECT, headers).then(getDataFromResponse);

const searchDealershipFormList = payload => {
  const { dealerId: dealershipViewDealerId, tenantId: dealershipViewTenantId, apiPayload } = payload;
  const queryParams = {
    dealershipViewTenantId,
    dealershipViewDealerId,
  };
  return HTTP.post(getModuleType(), getUrl('DEALERSHIP_FORM_LIST'), apiPayload, queryParams, headers).then(
    getDataFromResponse
  );
};
const checkFormExists = formName =>
  HTTP.get(getModuleType(), `${ROUTES.CHECK_FORM_EXISTS}?formName=${formName}`, EMPTY_OBJECT, headers).then(
    getDataFromResponse
  );

export default {
  searchFormList,
  fetchGlobalFields,
  getFormAnnotationDetails,
  saveForm,
  saveAnnotations,
  updateAnnotations,
  fetchPrinterTypes,
  getConfiguratorMetadata,
  getFormsMetaData,
  updateFormDetails,
  getLegalProviders,
  validatePrintSequence,
  isValidPDFName,
  isValidFormName,
  getVersionInfoList,
  fetchVersionInfo,
  restoreFormVersion,
  getFormLabels,
  newPreviewFields,
  newPreviewUrl,
  getFormComments,
  sendFormForApproval,
  approveForm,
  sendFormForRevision,
  searchArchivedFormsList,
  archiveForm,
  restoreArchivedForm,
  fetchAuditLogs,
  fetchAuditBeginningTimestamp,
  getFileURLs,
  copyForm,
  getMediaIdByDealerPdfKey,
  fetchGlobalMetadata,
  refreshFormTag,
  searchDealershipFormList,
  archiveForms,
  restoreArchivedForms,
  checkFormExists,
};
