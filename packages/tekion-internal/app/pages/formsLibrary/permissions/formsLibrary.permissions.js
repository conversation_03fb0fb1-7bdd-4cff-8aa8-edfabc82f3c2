import { PERMISSIONS } from '@tekion/tekion-base/constants/permissions';
import permissionHelpers from '@tekion/tekion-components/src/widgets/permissionsHelper/helpers';

import { isAppHondaOem } from 'pages/formsLibrary/utils/formsLibrary.utils';

export const isFormArchivable = permissions =>
  isAppHondaOem() || permissionHelpers.validFor(permissions, [PERMISSIONS.SALES.DEALS.FORMS.FORM_ARCHIVE]);

export const hasFormsLibraryEdit = permissions =>
  isAppHondaOem() || permissionHelpers.validFor(permissions, [PERMISSIONS.SALES.DEALS.FORMS.FORMS_LIBRARY_EDIT]);

export const hasFormsPackagesEdit = permissions =>
  isAppHondaOem() || permissionHelpers.validFor(permissions, [PERMISSIONS.SALES.DEALS.FORMS.FORMS_PACKAGES_EDIT]);

export const hasDealerFormsView = permissions =>
  isAppHondaOem() || permissionHelpers.validFor(permissions, [PERMISSIONS.SALES.DEALS.FORMS.DEALER_FORMS_VIEW]);

export const hasDealerFormsEdit = permissions =>
  isAppHondaOem() || permissionHelpers.validFor(permissions, [PERMISSIONS.SALES.DEALS.FORMS.DEALER_FORMS_EDIT]);

export const hasLegalFormsApproval = permissions =>
  isAppHondaOem() || permissionHelpers.validFor(permissions, [PERMISSIONS.SALES.DEALS.FORMS.LEGAL_FORMS_APPROVAL]);

export const hasFormsLibraryDealershipView = permissions =>
  permissionHelpers.validFor(permissions, [PERMISSIONS.SALES.DEALS.FORMS.FORMS_LIBRARY_DEALERSHIP_VIEW]);
