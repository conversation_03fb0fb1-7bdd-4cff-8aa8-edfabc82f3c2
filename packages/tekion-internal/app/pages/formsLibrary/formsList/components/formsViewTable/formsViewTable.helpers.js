import { defaultMemoize } from 'reselect';
import _map from 'lodash/map';
import _forEach from 'lodash/forEach';
import _size from 'lodash/size';
import _partial from 'lodash/partial';
import _isEmpty from 'lodash/isEmpty';
import _find from 'lodash/find';
import _includes from 'lodash/includes';
import _get from 'lodash/get';
import _uniq from 'lodash/uniq';
import _compact from 'lodash/compact';
import _filter from 'lodash/filter';

import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import MODULE_ASSET_TYPES from '@tekion/tekion-base/constants/moduleAssetTypes';
import { getLookupByKeys } from '@tekion/tekion-base/services/lookupService';
import { RESOURCE_TYPE } from '@tekion/tekion-base/bulkResolvers/constants/resourceType';
import { FORM_STATE } from '@tekion/tekion-base/constants/formConfigurator/constants';
import { getLoggedInUserId } from '@tekion/tekion-base/utils/userUtils';
import Dropdown from '@tekion/tekion-components/src/molecules/DropDown';
import { DEFAULT_FILTER_BEHAVIOR } from '@tekion/tekion-components/src/organisms/filterSection/constants/filterSection.constants';

import { hasFormsLibraryEdit, hasFormsPackagesEdit } from 'pages/formsLibrary/permissions/formsLibrary.permissions';
import { isAppHondaOem } from 'pages/formsLibrary/utils/formsLibrary.utils';
import { FORMS_ACTION } from '../../../molecules/kebabAction/kebabAction.constants';
import {
  DISABLE_BULK_OPTIONS_FOR_FORM_STATE,
  FORMS_VIEW_BULK_ACTIONS,
  FORMS_VIEW_FILTER_IDS,
} from './formsViewTable.constants';
import { getFilters } from './formsViewTable.config';
import BulkActionButton from '../../../atoms/bulkActionButton';
import KebabActionButton from '../../../atoms/kebabActionButton';
import FormManagementService from '../../../services/formManagement';
import PDFLibraryService from '../../../services/pdfLibrary';
import OEMService from '../../../services/oem';
import ACTION_TYPES from './formsViewTable.actionTypes';
import { hasInActiveForms, isFormInActive } from '../../../helpers/common';
import { FORM_STATUS } from '../../../constants/status';

export const getActions = onAction => {
  const createAction = (type, payload) => onAction({ type, payload });
  return {
    showLegalPreviewModal: _partial(createAction, ACTION_TYPES.SHOW_LEGAL_PREVIEW_MODAL),
    hideLegalPreviewModal: _partial(createAction, ACTION_TYPES.HIDE_LEGAL_PREVIEW_MODAL),
    showLegalApprovalModal: _partial(createAction, ACTION_TYPES.SHOW_LEGAL_APPROVAL_MODAL),
    hideLegalApprovalModal: _partial(createAction, ACTION_TYPES.HIDE_LEGAL_APPROVAL_MODAL),
    approveSelectedForm: _partial(createAction, ACTION_TYPES.APPROVE_SELECTED_FORM),
    showRevisionCommentsModal: _partial(createAction, ACTION_TYPES.SHOW_REVISION_COMMENTS_MODAL),
    hideRevisionCommentsModal: _partial(createAction, ACTION_TYPES.HIDE_REVISION_COMMENTS_MODAL),
    saveRevisionComments: _partial(createAction, ACTION_TYPES.SAVE_REVISION_COMMENTS),
    handleFormArchive: _partial(createAction, ACTION_TYPES.ON_FORM_ARCHIVE),
    handleFormRestore: _partial(createAction, ACTION_TYPES.ON_FORM_RESTORE),
  };
};

export const getFilterTypes = defaultMemoize(({ globalMetaData, labelsInfo }) => {
  const filters = getFilters({ globalMetaData, labelsInfo });
  return {
    filterTypes: [
      filters[FORMS_VIEW_FILTER_IDS.CREATED_BY],
      filters[FORMS_VIEW_FILTER_IDS.CREATED_TIME],
      filters[FORMS_VIEW_FILTER_IDS.COUNTRIES],
      filters[FORMS_VIEW_FILTER_IDS.DEPARTMENT],
      filters[FORMS_VIEW_FILTER_IDS.EFFECTIVE_DATE],
      filters[FORMS_VIEW_FILTER_IDS.EXPIRY_DATE],
      filters[FORMS_VIEW_FILTER_IDS.FORM_TYPES],
      filters[FORMS_VIEW_FILTER_IDS.FORM_STATUS],
      filters[FORMS_VIEW_FILTER_IDS.FNI_PRODUCT_PROVIDER],
      filters[FORMS_VIEW_FILTER_IDS.LABELS],
      filters[FORMS_VIEW_FILTER_IDS.LENDER],
      filters[FORMS_VIEW_FILTER_IDS.MODIFIED_BY],
      filters[FORMS_VIEW_FILTER_IDS.OEMS],
      filters[FORMS_VIEW_FILTER_IDS.STATES],
      filters[FORMS_VIEW_FILTER_IDS.STATE_DEALER_ASSOCIATIONS],
      filters[FORMS_VIEW_FILTER_IDS.TARGETED_MODULES],
      filters[FORMS_VIEW_FILTER_IDS.LICENSED_FOR],
    ],
    defaultFilterTypes: [FORMS_VIEW_FILTER_IDS.FORM_STATUS],
  };
});

export const getFilterPropsConfig = (selectedFilterGroup, selectedFilters) => ({
  assetType: MODULE_ASSET_TYPES.FORMS_LIBRARY_VIEW,
  defaultFilterBehavior: DEFAULT_FILTER_BEHAVIOR.GENERAL,
  appliedFilterGroup: selectedFilterGroup,
  selectedFilters,
  showDefaultFilter: false,
  shouldCloseOnOutsideClick: true,
  shouldCloseMultiSortOnOutsideClick: true,
  shouldClearMultiSortOnReset: true,
});

export const getFilterProps = defaultMemoize(
  ({ selectedFilterGroup, selectedFilters, allDealershipUsers, globalMetaData, labelsInfo, filterServices }) => ({
    ...getFilterTypes({ allDealershipUsers, globalMetaData, labelsInfo }),
    ...getFilterPropsConfig(selectedFilterGroup, selectedFilters),
    ...filterServices,
  })
);

export const getSubHeaderRightActions = ({ bulkActionOverlay, kebabActionOverlay, selectedItems }) => [
  {
    renderer: Dropdown,
    renderOptions: {
      overlay: bulkActionOverlay,
      trigger: ['click'],
      placement: 'bottomRight',
      children: BulkActionButton,
      disabled: _size(selectedItems) <= 1,
    },
  },
  {
    renderer: Dropdown,
    renderOptions: {
      overlay: kebabActionOverlay,
      trigger: ['click'],
      placement: 'bottomLeft',
      children: KebabActionButton,
    },
  },
];

export const getUserIdsToResolveFromResponse = (list = EMPTY_ARRAY) => {
  const userIds = [];
  _forEach(list, ({ modifiedBy, createdBy }) => {
    userIds.push(createdBy);
    userIds.push(modifiedBy);
  });
  const loggedInUserId = getLoggedInUserId();
  userIds.push(loggedInUserId);
  return _uniq(_compact(userIds));
};

export const getModifiedCountPopulatedResponse = async (formsResponse = EMPTY_OBJECT) => {
  const { hits } = formsResponse;
  const payload = _map(hits, ({ originalFormKey, formKey }) => originalFormKey ?? formKey);
  const pdfLibraryFormsPayload = _uniq(
    _map(
      _filter(hits, ({ pdfLibraryPdfKey }) => !!pdfLibraryPdfKey),
      ({ pdfLibraryPdfKey }) => pdfLibraryPdfKey
    )
  );
  let pdfMetadata = [];
  if (!_isEmpty(pdfLibraryFormsPayload)) {
    pdfMetadata = await PDFLibraryService.getMultiplePDFMetadataFromPDFLibrary({ pdfKeys: pdfLibraryFormsPayload });
  }
  const modifiedCountRes = await FormManagementService.getDealerModifiedCount(payload);
  const { higherEnvCount: modifiedProdCountRes } = await FormManagementService.getDealerModifiedCountForProd(payload);
  const userIdsToResolve = getUserIdsToResolveFromResponse(hits);
  const resolvedUsers = isAppHondaOem()
    ? await OEMService.getOEMUsersById(userIdsToResolve)
    : await getLookupByKeys(RESOURCE_TYPE.TENANT_USER_MINIMAL_V2, userIdsToResolve);
  const formsRespsonseWithModifedCount = [];

  _forEach(hits, (form, index) => {
    const pdfMetadataForForm =
      _find(pdfMetadata, metadata => _get(form, 'pdfLibraryPdfKey') === _get(metadata, 'pdfLibraryPdfKey')) ||
      EMPTY_OBJECT;
    formsRespsonseWithModifedCount.push({
      ...form,
      ...pdfMetadataForForm,
      modifiedCount: modifiedCountRes[index]?.count,
      modifiedProdCount: modifiedProdCountRes[index]?.count,
      displayName: resolvedUsers?.[form?.createdBy]?.displayName,
    });
  });

  return { ...formsResponse, hits: formsRespsonseWithModifedCount };
};

export const getDisabledOptions = formStatus => {
  switch (formStatus) {
    case FORM_STATE.DRAFT:
      return [
        FORMS_ACTION.PUBLISH,
        FORMS_ACTION.VERSION_HISTORY,
        FORMS_ACTION.ADD_TO_PACKAGE,
        FORMS_ACTION.SEND_FOR_APPROVAL,
        FORMS_ACTION.ADD_TO_PACKAGE,
        FORMS_ACTION.AUDIT_LOG,
      ];

    case FORM_STATE.NEW:
    case FORM_STATE.UPDATE_REQUIRED:
      return [FORMS_ACTION.PUBLISH, FORMS_ACTION.ADD_TO_PACKAGE];

    case FORM_STATE.READY_TO_PUBLISH:
    case FORM_STATE.PUBLISHED:
      return [FORMS_ACTION.SEND_FOR_APPROVAL];

    default:
      return [FORMS_ACTION.PUBLISH, FORMS_ACTION.SEND_FOR_APPROVAL, FORMS_ACTION.ADD_TO_PACKAGE];
  }
};

export const getIsBulkActionDisabled = (actionKey, selectedForms, permissions) => {
  const disableOption = !_isEmpty(
    _find(selectedForms, form => _includes(DISABLE_BULK_OPTIONS_FOR_FORM_STATE, _get(form, 'formState')))
  );

  const hasEditForm = hasFormsLibraryEdit(permissions);
  const hasEditPackage = hasFormsPackagesEdit(permissions);
  switch (actionKey) {
    case FORMS_VIEW_BULK_ACTIONS.PUBLISH:
      return disableOption || !hasEditForm;
    case FORMS_VIEW_BULK_ACTIONS.ADD_TO_PACKAGE:
      return disableOption || !hasEditPackage || hasInActiveForms(selectedForms);

    case FORMS_VIEW_BULK_ACTIONS.COMPARE:
      return true;

    default:
      return true;
  }
};

export const getKebabMenuDisabledOptions = rowInfo => {
  const formStatus = _get(rowInfo, 'formState', FORM_STATUS.DRAFT);
  const disabledOptions = getDisabledOptions(formStatus);
  if (isFormInActive(rowInfo)) {
    disabledOptions.push(FORMS_ACTION.ADD_TO_PACKAGE);
  }
  return disabledOptions;
};
