import { defaultMemoize } from 'reselect';
import _noop from 'lodash/noop';
import _size from 'lodash/size';
import _forEach from 'lodash/forEach';
import _reduce from 'lodash/reduce';
import _map from 'lodash/map';
import _get from 'lodash/get';
import _uniq from 'lodash/uniq';

import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import MODULE_ASSET_TYPES from '@tekion/tekion-base/constants/moduleAssetTypes';
import { getLookupByKeys } from '@tekion/tekion-base/services/lookupService';
import { RESOURCE_TYPE } from '@tekion/tekion-base/bulkResolvers/constants/resourceType';
import Dropdown from '@tekion/tekion-components/src/molecules/DropDown';
import { DEFAULT_FILTER_BEHAVIOR } from '@tekion/tekion-components/src/organisms/filterSection/constants/filterSection.constants';
import SearchInputWithState from '@tekion/tekion-widgets/src/appServices/sales/molecules/searchInputWithState';

import { PACKAGE_FIELD } from 'pages/formsLibrary/constants/common';
import { getDisplayNameFromResolvedUsers } from 'pages/formsLibrary/helpers/common';
import { isAppHondaOem } from 'pages/formsLibrary/utils/formsLibrary.utils';
import { PACKAGES_VIEW_FILTER_IDS } from './packagesViewTable.constants';
import { FILTERS } from './packagesViewTable.config';
import BulkActionButton from '../../../atoms/bulkActionButton';
import KebabActionButton from '../../../atoms/kebabActionButton';
import OEMService from '../../../services/oem';
import { getUserIdsToResolveFromResponse } from '../formsViewTable/formsViewTable.helpers';

export const getFilterTypes = defaultMemoize(() => ({
  filterTypes: [FILTERS.PACKAGE_STATUS, FILTERS.PACKAGES_LABELS],
  defaultFilterTypes: [PACKAGES_VIEW_FILTER_IDS.PACKAGE_STATUS],
}));

export const getFilterPropsConfig = (selectedFilterGroup, selectedFilters) => ({
  assetType: MODULE_ASSET_TYPES.FORM_PACKAGE_LIST,
  defaultFilterBehavior: DEFAULT_FILTER_BEHAVIOR.GENERAL,
  appliedFilterGroup: selectedFilterGroup,
  selectedFilters,
  showDefaultFilter: false,
  shouldCloseOnOutsideClick: true,
  shouldCloseMultiSortOnOutsideClick: true,
  shouldClearMultiSortOnReset: true,
});

export const getFilterProps = defaultMemoize((selectedFilterGroup, selectedFilters, filterServices) => ({
  ...getFilterTypes(),
  ...getFilterPropsConfig(selectedFilterGroup, selectedFilters),
  ...filterServices,
}));

export const getSubHeaderRightActions = ({
  bulkActionOverlay,
  kebabActionOverlay,
  handleSearchInputChange,
  searchText,
  selectedItems,
}) => [
  {
    renderer: SearchInputWithState,
    renderOptions: {
      onSearchTextChange: handleSearchInputChange,
      onPressEnter: _noop,
      searchText,
      placeholder: __('Search Package'),
      shouldDebounce: true,
    },
  },
  {
    renderer: Dropdown,
    renderOptions: {
      overlay: bulkActionOverlay,
      trigger: ['click'],
      placement: 'bottomRight',
      children: BulkActionButton,
      disabled: _size(selectedItems) <= 1,
    },
  },
  {
    renderer: Dropdown,
    renderOptions: {
      overlay: kebabActionOverlay,
      trigger: ['click'],
      placement: 'bottomLeft',
      children: KebabActionButton,
    },
  },
];

export const getResolvedUsersResponse = async (formsResponse = EMPTY_OBJECT) => {
  const { hits } = formsResponse;
  const userIdsToResolve = getUserIdsToResolveFromResponse(hits);
  const resolvedUsers = isAppHondaOem()
    ? await OEMService.getOEMUsersById(userIdsToResolve)
    : await getLookupByKeys(RESOURCE_TYPE.TENANT_USER_MINIMAL_V2, userIdsToResolve);
  const formsRespsonseWithResolvedUsers = [];

  _forEach(hits, form => {
    formsRespsonseWithResolvedUsers.push({
      ...form,
      updatedDisplayName: getDisplayNameFromResolvedUsers(form?.modifiedBy || form?.lastModifiedBy, resolvedUsers),
      createdDisplayName: getDisplayNameFromResolvedUsers(form?.createdBy, resolvedUsers),
      publishedDisplayName: getDisplayNameFromResolvedUsers(form?.lastPublishedBy, resolvedUsers),
    });
  });

  return { ...formsResponse, hits: formsRespsonseWithResolvedUsers };
};

const getFormIdsInPackage = packageDetails =>
  _map(_get(packageDetails, PACKAGE_FIELD.FORM_DETAILS, EMPTY_ARRAY), ({ formId }) => formId);

export const getFormSearchPayloadFromPackages = packagesResponse => {
  const { hits: packages = [] } = packagesResponse;
  const formIds = _uniq(
    _reduce(packages, (packageFormIds, packageInfo) => [...packageFormIds, ...getFormIdsInPackage(packageInfo)], [])
  );
  return {
    pageInfo: {
      start: 0,
      rows: _size(formIds),
      current: 1,
    },
    filters: [
      {
        field: 'formKey',
        operator: 'IN',
        values: formIds,
      },
    ],
  };
};
