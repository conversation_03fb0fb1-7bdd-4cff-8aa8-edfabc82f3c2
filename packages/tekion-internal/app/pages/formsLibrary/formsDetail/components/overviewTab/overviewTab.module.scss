@import "tstyles/component.scss";
@import "tcomponents/organisms/FormBuilder/components/fieldLayout/fieldLayout.module.scss";

.associatedWrapper {
  @include flex($align-items: center, $justify-content: space-around);
  height: 7.2rem;
  border: $border-base-width $border-base-type $platinum;
  border-radius: 0.8rem;
}

@mixin icon {
  @include circle(4);
  @include flex($justify-content: center, $align-items: center);
  border: 0.4rem $border-base-type $lightGray;
}

.dealershipIcon {
  @include icon;
  background-color: $pastelGreen;
}

.packageIcon {
  @include icon;
  background-color: $anakiwa;
}

.labelText {
  font-size: $font-size-small;
}

.countText {
  font-size: $font-size-mlarge;
  font-weight: bold;
}

.viewAllLink {
  & > span {
    text-decoration: none;
  }
  &:disabled {
    cursor: not-allowed;
  }
}

.collapseWrapper {
  :global(.ant-collapse-borderless > .ant-collapse-item) {
    @include is-borderless;
  }

  :global(.ant-collapse-content > .ant-collapse-content-box) {
    @include is-paddingless;
  }

  :global(.ant-collapse-header) {
    &:hover {
      background: none;
    }
  }
}

.collapseBody {
  padding-left: 0 !important;
  border-left: none;
  margin-left: 0;
}

.arrowClass {
  padding-left: 0 !important;
}

.formClassName {
  & > div {
    padding-left: 0;
    padding-right: 0;
  }
}

.divider {
  border-right: $border-base-width $border-base-type $platinum;
  height: 4rem;
}

.iconContent {
  padding: 0 1rem;
}

.infoIcon {
  margin-left: -1.5rem;
  font-size: $font-normal;
}

.digiCertContainer {
  padding: 2.4rem;
}

.leftPaneWrapperViewOnly {
  opacity: 0.5;
  cursor: not-allowed;
  & > div {
    pointer-events: none;
  }
}

.dropDownClassName {
  margin-top: 0.8rem;
  width: 29.6rem;
  height: 3.2rem;
  max-width: 100%;
}

.subHeaderLabel {
  font-family: "Proxima-Nova-Regular";
  margin-top: -1.0rem;
}
