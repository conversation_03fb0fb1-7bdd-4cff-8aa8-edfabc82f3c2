import React from 'react';
import _get from 'lodash/get';

import { LICENSED_FOR_OPTIONS, ENTERPRISE_OPTIONS } from '@tekion/tekion-base/constants/formConfigurator/constants';
import { PRINTER_TYPES } from '@tekion/tekion-base/constants/formConfigurator/printers';
import { shouldBeAfter, shouldBeBefore } from '@tekion/tekion-base/utils/formValidators';
import standardFieldOptionMapper from '@tekion/tekion-base/utils/optionMappers/standardFieldMapper';
import { EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';

import IconAsBtn from '@tekion/tekion-components/src/atoms/iconAsBtn';
import Checkbox from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/checkbox';
import InputDatePickerField from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/inputDatePickerField';
import MultiSelectField from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/MultiSelectField';
import Select from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/select';
import TextInput from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/textInput';

import { DATE_TIME_FORMAT } from '@tekion/tekion-conversion-web';

import NumberInputField from '@tekion/tekion-widgets/src/fieldRenderers/numberInputField';
import {
  PRICE_TYPE_OPTIONS,
  SIGNING_TYPE_OPTIONS,
} from '@tekion/tekion-widgets/src/organisms/AddForm/AddForm.constants';
import { formattedStatesListResponse } from '@tekion/tekion-widgets/src/organisms/AddForm/AddForm.utils';
import DatePickerWrapper from '@tekion/tekion-widgets/src/organisms/AddForm/components/datePickerWrapper';
import NumberInputWithSignToggle from '@tekion/tekion-widgets/src/molecules/NumberInputWithSignToggle';
import COLORS from '@tekion/tekion-styles-next/scss/exports.scss';

import { ASSOCIATED_PANEL, COLLAPSE_PANEL_KEYS, OFFSET_X_RADIOS, OFFSET_Y_RADIOS } from './overviewTab.constants';
import { DEPARTMENTS_OPTIONS } from '../../../constants';
import { FORM_FIELD } from '../../../constants/common';
import { getFormCategoryRenderOptions, getPrinterTypesOptions } from '../../formsDetail.helpers';
import styles from './overviewTab.module.scss';

export const formSections = [
  {
    className: `full-width`,
    header: { label: __('Form Identification'), size: 3 },
    rows: [
      {
        columns: [FORM_FIELD.FORM_TAG],
      },
      {
        columns: [FORM_FIELD.CODE, FORM_FIELD.FORM_CATEGORY],
      },
      {
        columns: [FORM_FIELD.EFFECTIVE_DATE, FORM_FIELD.EXPIRY_DATE],
      },
      {
        columns: [FORM_FIELD.DEPARTMENT, FORM_FIELD.DOCUMENT_NAME],
      },
      {
        columns: [FORM_FIELD.DOCUMENT_ID, FORM_FIELD.FORM_NUMBER],
      },
      {
        columns: [FORM_FIELD.FORM_REVISION_DATE, FORM_FIELD.FLEET_ELIGIBILITY],
      },
      {
        columns: [FORM_FIELD.PRICE_TYPE, FORM_FIELD.COST],
      },
      {
        columns: [FORM_FIELD.STATES, FORM_FIELD.OEMS],
      },
      {
        columns: [FORM_FIELD.LENDER, FORM_FIELD.STATE_DEALER_ASSOCIATIONS],
      },
      {
        columns: [FORM_FIELD.FNI_PRODUCT_PROVIDER, FORM_FIELD.FORM_TYPES],
      },
    ],
  },
  {
    className: `full-width`,
    header: { label: __('Forms Printing'), size: 3 },
    rows: [
      {
        columns: [FORM_FIELD.PRINTER, FORM_FIELD.PRINT_SEQUENCE],
      },
      {
        columns: [FORM_FIELD.NUMBER_OF_COPIES, FORM_FIELD.DUPLEX],
      },
    ],
  },
  {
    className: `full-width`,
    header: { label: __('Print Offset (in mm)'), size: 3 },
    subHeader: { label: __('Update relative print position.'), className: styles.subHeaderLabel },
    rows: [
      {
        columns: [FORM_FIELD.OFFSET_X, FORM_FIELD.OFFSET_Y],
      },
    ],
  },
  {
    className: `full-width`,
    header: { label: __('Form License Fields'), size: 3 },
    rows: [
      {
        columns: [FORM_FIELD.LICENSED_BY, FORM_FIELD.LICENSED_PREFERED_NAME],
      },
      {
        columns: [FORM_FIELD.LICENSE_KEY, FORM_FIELD.LICENSED_FOR],
      },
      {
        columns: [FORM_FIELD.ENTERPRISE],
      },
    ],
  },
  {
    className: `full-width`,
    header: { label: __('Forms Signing'), size: 3 },
    rows: [
      {
        columns: [FORM_FIELD.MANDATORY_SIGNATURE_TYPE],
      },
    ],
  },
];

// TODO: Move to common Addform constants

export const getFormFields = ({
  printerType,
  formPrinterType,
  formCategory,
  effectiveDate,
  expiryDate,
  requiredFields,
  formCategories,
  printerTypes,
  legalProviders,
  isGlobalForm,
  isPDFLibraryForm,
  renderGroupLabel,
  globalMetaData,
  refreshFormTag,
}) => ({
  [FORM_FIELD.FORM_CATEGORY]: {
    renderer: Select,
    renderOptions: getFormCategoryRenderOptions(formCategories, formCategory),
  },
  [FORM_FIELD.CODE]: {
    renderer: TextInput,
    renderOptions: {
      label: __('Code'),
    },
  },

  [FORM_FIELD.EFFECTIVE_DATE]: {
    renderer: InputDatePickerField,
    renderOptions: {
      label: __('Effective Date'),
      dateFormat: DATE_TIME_FORMAT.BASE,
      validators: [shouldBeBefore(expiryDate)],
      required: requiredFields.includes(FORM_FIELD.EFFECTIVE_DATE),
    },
  },
  [FORM_FIELD.EXPIRY_DATE]: {
    renderer: InputDatePickerField,
    renderOptions: {
      label: __('Expiry Date'),
      dateFormat: DATE_TIME_FORMAT.BASE,
      disabledDate: current => current < effectiveDate,
      validators: [shouldBeAfter(effectiveDate)],
      required: requiredFields.includes(FORM_FIELD.EXPIRY_DATE),
    },
  },
  [FORM_FIELD.DEPARTMENT]: {
    renderer: Select,
    renderOptions: {
      label: __('Department'),
      options: DEPARTMENTS_OPTIONS,
      required: requiredFields.includes(FORM_FIELD.DEPARTMENT),
    },
  },
  [FORM_FIELD.PRINTER]: {
    renderer: Select,
    renderOptions: {
      label: __('Printer'),
      options: getPrinterTypesOptions(printerTypes, formPrinterType),
      size: 6,
      required: requiredFields.includes(FORM_FIELD.PRINTER),
      disabled: isPDFLibraryForm,
    },
  },
  [FORM_FIELD.PRINT_SEQUENCE]: {
    renderer: NumberInputField,
    renderOptions: {
      label: __('Print Order'),
      min: 0,
      triggerChangeOnBlur: false,
    },
  },
  [FORM_FIELD.NUMBER_OF_COPIES]: {
    renderer: NumberInputField,
    renderOptions: {
      label: __('Number of Copies'),
      min: 0,
      precision: 0,
    },
  },
  [FORM_FIELD.OFFSET_X]: {
    renderer: NumberInputWithSignToggle,
    renderOptions: {
      addonAfter: 'mm',
      radios: OFFSET_X_RADIOS,
      parser: value => {
        const parsed = value.replace(/[^\d.]/g, '').replace(/^(\d*\.\d*).*$/, '$1');
        return parsed;
      },
    },
  },
  [FORM_FIELD.OFFSET_Y]: {
    renderer: NumberInputWithSignToggle,
    renderOptions: {
      addonAfter: 'mm',
      radios: OFFSET_Y_RADIOS,
      parser: value => {
        const parsed = value.replace(/[^\d.]/g, '').replace(/^(\d*\.\d*).*$/, '$1');
        return parsed;
      },
    },
  },

  [FORM_FIELD.LICENSED_BY]: {
    renderer: Select,
    renderOptions: {
      label: __('Licensed By'),
      placeholder: __('Select License'),
      disabled: isPDFLibraryForm,
      options: standardFieldOptionMapper('FORM_LICENSE_PROVIDER', legalProviders),
    },
  },

  [FORM_FIELD.LICENSED_PREFERED_NAME]: {
    renderer: TextInput,
    renderOptions: {
      label: __('Form License Name'),
      disabled: isPDFLibraryForm,
    },
  },
  [FORM_FIELD.MANDATORY_SIGNATURE_TYPE]: {
    renderer: Select,
    renderOptions: {
      label: __('Mandatory Signing Type'),
      allowClear: true,
      options: SIGNING_TYPE_OPTIONS,
    },
  },
  [FORM_FIELD.LICENSE_KEY]: {
    renderer: TextInput,
    renderOptions: {
      label: __(' License Key'),
      disabled: isPDFLibraryForm,
    },
  },

  [FORM_FIELD.LICENSED_FOR]: {
    renderer: MultiSelectField,
    renderOptions: {
      label: __(' Licensed For'),
      options: LICENSED_FOR_OPTIONS,
      disabled: isPDFLibraryForm,
    },
  },

  [FORM_FIELD.DUPLEX]: {
    renderer: Checkbox,
    renderOptions: {
      label: __('Double Sided Print'),
      disabled: [PRINTER_TYPES.DOT_MATRIX, PRINTER_TYPES.LABEL].includes(printerType),
    },
  },
  [FORM_FIELD.FLEET_ELIGIBILITY]: {
    renderer: Checkbox,
    renderOptions: {
      label: __('Eligible for Fleet'),
      helpText: __('These are forms expected to be tracked at fleet level'),
      infoContentClassName: styles.iconContent,
      infoIconClassName: styles.infoIcon,
      disabled: isGlobalForm,
    },
  },
  [FORM_FIELD.PRICE_TYPE]: {
    renderer: Select,
    renderOptions: {
      label: __('Price Type'),
      placeholder: __('Select Price Type'),
      options: PRICE_TYPE_OPTIONS,
    },
  },
  [FORM_FIELD.COST]: {
    renderer: NumberInputField,
    renderOptions: {
      label: __('Cost'),
    },
  },
  [FORM_FIELD.FORM_REVISION_DATE]: {
    renderer: DatePickerWrapper,
    renderOptions: {
      label: __('Form Revision Date'),
    },
  },
  [FORM_FIELD.FORM_NUMBER]: {
    renderer: TextInput,
    renderOptions: {
      label: __('Form Number'),
    },
  },
  [FORM_FIELD.DOCUMENT_NAME]: {
    renderer: TextInput,
    renderOptions: {
      label: __('Document Name'),
    },
  },
  [FORM_FIELD.DOCUMENT_ID]: {
    renderer: TextInput,
    renderOptions: {
      label: __('Document ID'),
    },
  },
  [FORM_FIELD.STATES]: {
    renderer: MultiSelectField,
    renderOptions: {
      label: __('States'),
      options: formattedStatesListResponse(
        _get(globalMetaData, FORM_FIELD.COUNTRIES),
        _get(globalMetaData, FORM_FIELD.STATES)
      ),
      styles: base => ({
        ...base,
        ':not(:last-child)': {
          borderBottom: `0.1rem solid ${COLORS.platinum}`,
        },
      }),
      additionalOverrideProps: { overrideWithBaseSelectStyles: true },
      dropDownClassName: styles.dropDownClassName,
      formatGroupLabel: renderGroupLabel,
    },
  },
  [FORM_FIELD.OEMS]: {
    renderer: MultiSelectField,
    renderOptions: {
      label: __('OEM'),
      options: _get(globalMetaData, FORM_FIELD.OEMS, EMPTY_ARRAY),
      isWithSelectAll: true,
    },
  },
  [FORM_FIELD.LENDER]: {
    renderer: MultiSelectField,
    renderOptions: {
      label: __('Lender / Captive'),
      options: _get(globalMetaData, FORM_FIELD.LENDER, EMPTY_ARRAY),
      isWithSelectAll: true,
    },
  },
  [FORM_FIELD.STATE_DEALER_ASSOCIATIONS]: {
    renderer: MultiSelectField,
    renderOptions: {
      label: __('State Dealer Association'),
      options: _get(globalMetaData, FORM_FIELD.STATE_DEALER_ASSOCIATIONS, EMPTY_ARRAY),
      isWithSelectAll: true,
    },
  },
  [FORM_FIELD.FNI_PRODUCT_PROVIDER]: {
    renderer: MultiSelectField,
    renderOptions: {
      label: __('F&I Product Provider'),
      options: _get(globalMetaData, FORM_FIELD.FNI_PRODUCT_PROVIDER, EMPTY_ARRAY),
      isWithSelectAll: true,
    },
  },
  [FORM_FIELD.FORM_TYPES]: {
    renderer: MultiSelectField,
    renderOptions: {
      label: __('Form Type'),
      options: _get(globalMetaData, FORM_FIELD.FORM_TYPES, EMPTY_ARRAY),
      isWithSelectAll: true,
    },
  },
  [FORM_FIELD.FORM_TAG]: {
    renderer: TextInput,
    renderOptions: {
      label: __('Form Id'),
      addonBefore: <IconAsBtn onClick={refreshFormTag}>icon-refresh</IconAsBtn>,
      placeholder: '',
      readOnly: true,
    },
  },
  [FORM_FIELD.ENTERPRISE]: {
    renderer: MultiSelectField,
    renderOptions: {
      label: __('Enterprise'),
      options: ENTERPRISE_OPTIONS,
    },
  },
});

export const COLLAPSE_PANEL_CONFIG = [
  {
    header: __('Form Fields'),
    key: COLLAPSE_PANEL_KEYS.FORM_FIELDS,
  },
  {
    header: __('Required for eContracting'),
    key: COLLAPSE_PANEL_KEYS.E_CONTRACTING_FORM_FIELDS,
  },
];

export const ASSOCIATED_PANEL_CONFIG = {
  [ASSOCIATED_PANEL.DEALERS]: {
    iconClassName: styles.dealershipIcon,
    iconName: 'icon-dealership',
    label: __('Dealerships Associated'),
  },
  [ASSOCIATED_PANEL.PACKAGES]: {
    iconClassName: styles.packageIcon,
    iconName: 'icon-packages',
    label: __('Packages Associated'),
  },
};
