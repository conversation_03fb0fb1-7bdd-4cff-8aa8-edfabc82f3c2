import _find from 'lodash/find';
import _filter from 'lodash/filter';
import _get from 'lodash/get';
import _includes from 'lodash/includes';
import _isEmpty from 'lodash/isEmpty';
import _map from 'lodash/map';
import _toString from 'lodash/toString';
import _startCase from 'lodash/startCase';

import OPERATORS from '@tekion/tekion-base/constants/filterOperators';
import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { DEFAULT_FORM_SCALE } from '@tekion/tekion-base/constants/formConfigurator/constants';
import { isValidDate, isBefore, isAfter } from '@tekion/tekion-base/utils/dateUtils';
import { getLookupByKeys } from '@tekion/tekion-base/services/lookupService';
import { RESOURCE_TYPE } from '@tekion/tekion-base/bulkResolvers/constants/resourceType';
import { getSignedURLs } from 'tbusiness/services/mediaV3';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import { getAnnotationDetailsFromResponse } from '@tekion/tekion-widgets/src/organisms/formConfiguratorTool/FormConfiguratorTool.utils';
import { DATE_TIME_FORMAT } from '@tekion/tekion-conversion-web';

import { isAppHondaOem } from 'pages/formsLibrary/utils/formsLibrary.utils';
import { getUserIdsToResolveFromResponse } from '../formsList/components/formsViewTable/formsViewTable.helpers';
import { getUserOptionLabel } from '../formsList/formsList.helpers';

import ACTION_TYPES from './versionHistory.actionTypes';
import { DEFAULT_STATE } from './versionHistory.constants';
import { FILTER_IDS } from './components/filterFunnel/filterFunnel.constants';
import { getFormattedFilters } from '../helpers/table';
import FormCoreService from '../services/formCore';
import { ES_SYNC_DELAY } from '../constants';

const fetchVersionHistoryList = async ({ getState, setState, params = EMPTY_OBJECT }) => {
  const { getFormattedDateAndTime } = getState();
  const { versionHistoryFormKey } = params;
  setState({
    isLoadingVersionHistoryList: true,
  });

  try {
    const _versionListResponse = await FormCoreService.getVersionInfoList(versionHistoryFormKey);
    const _versionList = _get(_versionListResponse, 'formAnnotationDocumentVersionInfoList', EMPTY_ARRAY);
    const userIdsToResolve = getUserIdsToResolveFromResponse(_versionList);
    const resolvedUsers = isAppHondaOem()
      ? EMPTY_OBJECT
      : await getLookupByKeys(RESOURCE_TYPE.TENANT_USER_MINIMAL_V2, userIdsToResolve);
    const activeVersion = _find(_versionList, 'active');

    if (!_isEmpty(_versionList)) {
      const versionHistoryListData = _map(_versionList, formVersionInfo => {
        const { version, active, createdAt, createdBy } = formVersionInfo;

        return {
          ...formVersionInfo,
          version: _toString(version),
          isFormActive: active,
          lastModifiedTimestamp: createdAt,
          createdBy: _startCase(getUserOptionLabel(_get(resolvedUsers, createdBy))),
          authorId: createdBy, // TODO: Find if even required
          lastModifiedDate: isValidDate(createdAt)
            ? getFormattedDateAndTime({
                value: createdAt,
                formatType: DATE_TIME_FORMAT.DATE_ABBREVIATED_MONTH_YEAR_WITH_HOUR_MINUTE,
              })
            : __('Date not available'),
        };
      });
      setState({
        isLoadingVersionHistoryList: false,
        versionHistoryListData,
        versionHistoryListFormattedResponse: versionHistoryListData,
      });
      await handleFormVersionSelect({ setState, params: { ...activeVersion, versionHistoryFormKey } });
    } else {
      toaster(TOASTER_TYPE.ERROR, __('Something went wrong'));
    }
  } catch (error) {
    toaster(TOASTER_TYPE.ERROR, __('Something went wrong'));
  }
};

const updatePDFViewHeight = ({ setState, getState, params = EMPTY_OBJECT }) => {
  const { pageDimensions } = params;

  const { versionPreviewTotalPages, versionPreviewFormResponse } = getState();

  if (!_isEmpty(versionPreviewFormResponse)) {
    const { annotatedFields } = getAnnotationDetailsFromResponse(
      versionPreviewFormResponse,
      pageDimensions,
      versionPreviewTotalPages,
      DEFAULT_FORM_SCALE
    );

    setState({
      versionPreviewAnnotatedFields: annotatedFields,
      versionPreviewPageDimensions: pageDimensions,
      versionPreviewPDFLoaded: true,
    });
  } else {
    setState({ versionPreviewAnnotatedFields: [] });
  }
};

const updateTotalPages = ({ setState, params = EMPTY_OBJECT }) => {
  const { totalPages } = params;

  setState({
    versionPreviewTotalPages: totalPages,
  });
};

const handleFormVersionSelect = async ({ setState, params = EMPTY_OBJECT }) => {
  const { version, comment, versionHistoryFormKey } = params;

  setState({
    selectedFormVersion: _toString(version),
    selectedFormVersionComment: comment,
    isLoadingVersionPreviewPDF: true,
    versionPreviewFormData: EMPTY_OBJECT,
    versionPreviewTotalPages: 1,
    versionPreviewAnnotatedFields: EMPTY_ARRAY,
    versionPreviewPageDimensions: EMPTY_OBJECT,
  });

  const versionPreviewFormData = {};

  try {
    const versionPreviewFormResponse = await FormCoreService.fetchVersionInfo(versionHistoryFormKey, version);
    if (!_isEmpty(versionPreviewFormResponse)) {
      const mediaId = _get(versionPreviewFormResponse, 'mediaId');
      if (mediaId) {
        const signedUrlResponse = await getSignedURLs([mediaId]);
        const url = _get(signedUrlResponse, '0.normal.url');
        versionPreviewFormData.mediaId = mediaId;
        versionPreviewFormData.url = url;
        setState({ versionPreviewFormResponse });
      }
    } else {
      toaster(TOASTER_TYPE.ERROR, __('Something went wrong'));
    }
  } catch (error) {
    toaster(TOASTER_TYPE.ERROR, __('Something went wrong'));
  }

  setState({ isLoadingVersionPreviewPDF: false, versionPreviewFormData });
};

const handleApplyVersionListFilter = ({ setState, getState, params = EMPTY_OBJECT }) => {
  const { versionListAppliedFilters } = params;
  const { versionHistoryListFormattedResponse, selectedFormVersion } = getState();

  const formattedFilters = getFormattedFilters(versionListAppliedFilters); // NOTE: Use for API params
  if (_isEmpty(formattedFilters)) {
    setState({ versionHistoryListData: versionHistoryListFormattedResponse, versionListAppliedFilters });
    return;
  }

  const filteredVersionList = _filter(versionHistoryListFormattedResponse, version => {
    const { lastModifiedTimestamp, authorId } = version;
    const { values: dateFilterValue } = _find(versionListAppliedFilters, f => f.type === FILTER_IDS.DATE);
    const { operator: userFilterOperator, values: userFilterValue } = _find(
      versionListAppliedFilters,
      f => f.type === FILTER_IDS.USERS
    );

    if (
      !_isEmpty(dateFilterValue) &&
      !(
        isBefore(lastModifiedTimestamp, dateFilterValue[0], { inclusive: true }) &&
        isAfter(lastModifiedTimestamp, dateFilterValue[1], { inclusive: true })
      )
    ) {
      return false;
    }

    if (!_isEmpty(userFilterValue)) {
      if (userFilterOperator === OPERATORS.IN && !_includes(userFilterValue, authorId)) return false;
      if (userFilterOperator === OPERATORS.NIN && _includes(userFilterValue, authorId)) return false;
    }

    return true;
  });

  if (
    _includes(
      _map(filteredVersionList, ({ version }) => version),
      selectedFormVersion
    )
  ) {
    setState({ versionHistoryListData: filteredVersionList, versionListAppliedFilters });
  } else {
    setState({
      versionHistoryListData: filteredVersionList,
      versionListAppliedFilters,
      selectedFormVersion: EMPTY_STRING,
    });
  }
};

const resetState = ({ setState }) => {
  setState(DEFAULT_STATE);
};

const showRestoreConfirmationDialog = ({ setState, params = EMPTY_OBJECT }) => {
  const { formVersionSelectedToRestore } = params;

  setState({
    showRestoreConfirmationDialog: true,
    formVersionSelectedToRestore,
  });
};

const hideRestoreConfirmationDialog = ({ setState }) => {
  setState({
    showRestoreConfirmationDialog: false,
  });
};

const handleRestoreFormVersion = async ({ setState, getState, params = EMPTY_OBJECT }) => {
  const { versionHistoryFormKey, getUserInfoById } = params;
  const { formVersionSelectedToRestore, isVersionRestored } = getState();

  let flag = false;
  setState({ isLoadingFormVersionRestore: true });
  try {
    const response = await FormCoreService.restoreFormVersion(versionHistoryFormKey, formVersionSelectedToRestore);
    if (response) {
      toaster(TOASTER_TYPE.SUCCESS, __('Version Restored'));
      setTimeout(
        () => fetchVersionHistoryList({ getState, setState, params: { versionHistoryFormKey, getUserInfoById } }),
        ES_SYNC_DELAY
      );
      flag = true;
    } else toaster(TOASTER_TYPE.ERROR, __('Something went wrong'));
  } catch {
    toaster(TOASTER_TYPE.ERROR, __('Something went wrong'));
  }
  setState({ isLoadingFormVersionRestore: false, isVersionRestored: isVersionRestored || flag });
  hideRestoreConfirmationDialog({ setState });
};

const ACTION_HANDLERS = {
  [ACTION_TYPES.FETCH_VERSION_HISTORY_LIST]: fetchVersionHistoryList,
  [ACTION_TYPES.ON_VERSION_PREVIEW_PDF_PAGE_LOAD]: updatePDFViewHeight,
  [ACTION_TYPES.ON_VERSION_PREVIEW_PDF_LOAD]: updateTotalPages,
  [ACTION_TYPES.ON_FORM_VERSION_SELECT]: handleFormVersionSelect,
  [ACTION_TYPES.ON_APPLY_VERSION_LIST_FILTER]: handleApplyVersionListFilter,
  [ACTION_TYPES.RESET_STATE]: resetState,
  [ACTION_TYPES.SHOW_RESTORE_VERSION_MODAL]: showRestoreConfirmationDialog,
  [ACTION_TYPES.HIDE_RESTORE_VERSION_MODAL]: hideRestoreConfirmationDialog,
  [ACTION_TYPES.ON_FORM_VERSION_RESTORE]: handleRestoreFormVersion,
};

export default ACTION_HANDLERS;
