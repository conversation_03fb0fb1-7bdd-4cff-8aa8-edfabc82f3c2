import produce from 'immer';
import _set from 'lodash/set';
import _get from 'lodash/get';
import _map from 'lodash/map';
import _concat from 'lodash/concat';
import _filter from 'lodash/filter';
import _includes from 'lodash/includes';
import _isFunction from 'lodash/isFunction';
import _forEach from 'lodash/forEach';
import _find from 'lodash/find';
import _isEmpty from 'lodash/isEmpty';
import _keys from 'lodash/keys';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { tget } from '@tekion/tekion-base/utils/general';
import OPERATORS from '@tekion/tekion-base/constants/filterOperators';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import { downloadURI } from '@tekion/tekion-components/src/utils/downloadFile';
import { TABLE_TYPES } from '@tekion/tekion-widgets/src/organisms/tableManager';
import {
  showArchiveConfirmationDialog as showFormArchiveConfirmationDialog,
  hideArchiveConfirmationDialog as hideFormArchiveConfirmationDialog,
} from '@tekion/tekion-widgets/src/organisms/archivedFormsModal';

import { getEnvList, getFieldValue } from '../helpers/common';
import ACTION_TYPES from '../actionTypes/common';
import { DEALERS_ASSOCIATED_EXTRA_CONFIG, DEALERS_ASSOCIATED_EXTRA_COLUMNS, FILTER_FIELDS } from '../constants/common';
import FormCoreService from '../services/formCore';
import formManagement from '../services/formManagement';
import { createDealerForFormsPayload, getStatusFilterObject } from '../factory/dealerForms.factory';
import { getFilters } from '../dealerForms/dealerFormsTable.config';
import { FORM_STATUS } from '../constants/status';
import { FORM_LIBRARY } from '../dealerForms/dealerFormsTable.constants';

const showPublishModal = ({ setState, params = EMPTY_OBJECT }) => {
  const { entityData = EMPTY_OBJECT } = params;

  setState(
    produce(draft =>
      _set(draft, 'publishModal', {
        ...draft.publishModal,
        visible: true,
        entityData,
      })
    )
  );
};

const hidePublishModal = ({ setState }) => {
  setState(
    produce(draft =>
      _set(draft, 'publishModal', {
        ...draft.publishModal,
        visible: false,
        entityData: EMPTY_OBJECT,
      })
    )
  );
};

const showVersionHistoryModal = async ({ setState, params = EMPTY_OBJECT }) => {
  const { formKey, formName } = params;

  setState(
    produce(draft =>
      _set(draft, 'versionHistoryModal', {
        ...draft.versionHistoryModal,
        visible: true,
        versionHistoryFormName: formName,
        versionHistoryFormKey: formKey,
      })
    )
  );
};

const hideVersionHistoryModal = ({ setState }) => {
  setState(
    produce(draft =>
      _set(draft, 'versionHistoryModal', {
        ...draft.versionHistoryModal,
        visible: false,
        versionHistoryFormName: EMPTY_STRING,
        versionHistoryFormKey: EMPTY_STRING,
      })
    )
  );
};

const downloadPdfAction = async ({ params = EMPTY_OBJECT }) => {
  const { formKey } = params;

  try {
    if (formKey) {
      const signedUrlResponse = await FormCoreService.getFileURLs(formKey);
      const signedUrl = tget(signedUrlResponse, 'mediaIdResponse.normal.url', EMPTY_STRING);
      const formDisplayKey = tget(signedUrlResponse, 'formDisplayKey');
      downloadURI(signedUrl, formDisplayKey);
    }
  } catch {
    toaster(TOASTER_TYPE.ERROR, __('Failed to download'));
  }
};

const handleOnArchiveSubmit =
  ({ setState, payload }) =>
  async (itemsSelected = EMPTY_ARRAY) => {
    const { isRestore, mappingData, defaultPayload = EMPTY_ARRAY, onComplete } = payload;
    try {
      const requestPayload = [...defaultPayload];
      _forEach(itemsSelected, item => {
        const dealerForm = _find(mappingData, dealer => dealer.mapId === item);
        if (dealerForm) {
          const { formKey } = dealerForm;
          requestPayload.push(formKey);
        }
      });

      if (isRestore) {
        await FormCoreService.restoreArchivedForms(requestPayload);
      } else {
        await FormCoreService.archiveForms(requestPayload);
      }
      toaster(
        TOASTER_TYPE.SUCCESS,
        isRestore ? __('Successfully restored the forms.') : __('Successfully archived the forms.')
      );
      if (_isFunction(onComplete)) {
        onComplete();
      }
    } catch {
      toaster(TOASTER_TYPE.ERROR, isRestore ? __('Failed to restore') : __('Failed to archive'));
    } finally {
      hideDealersAssociatedModal({ setState });
    }
  };

const showArchiveConfirmationDialog = async ({ params = EMPTY_OBJECT }) => {
  const { key, name, modalViewType } = params;
  showFormArchiveConfirmationDialog({
    modalViewType,
    key,
    name,
  });
};

const getAssociatedArchiveFilter =
  (formKey, setState) =>
  async ({ filtersObj, selectedFilters: _selectedFilters }) => {
    const selectedFilters = _map(_selectedFilters, filter => ({
      ...filter,
      type: filter.type.replace('dealerSharedWith.', ''),
    }));
    const statusFilters = _filter(selectedFilters, ({ type }) => _includes([FILTER_FIELDS.STATUS], type));

    const filters = _filter(selectedFilters, ({ type }) => !_includes([FILTER_FIELDS.STATUS], type));
    const defaultSearchFilters = getDefaultSearchFilter(formKey);
    const applicableFilters = getStatusFilterObject(filters, defaultSearchFilters, statusFilters);
    const combinedResponse = await getCombinedSearchResponse(formKey, applicableFilters);

    setState(
      produce(draft =>
        _set(draft, 'dealersAssociatedModal', {
          ...draft.dealersAssociatedModal,
          mappingData: combinedResponse,
        })
      )
    );

    const dealerMap = combinedResponse.reduce((acc, item) => {
      if (_get(item, 'formState') !== FORM_STATUS.DRAFT) {
        const { env, dealerId } = item;
        if (!acc[dealerId]) {
          acc[dealerId] = [];
        }
        acc[dealerId].push(env);
      }
      return acc;
    }, {});

    const filtersWithoutStatus = _filter(filtersObj, item => item.field !== FILTER_FIELDS.STATUS);

    const andFilters = _map(_keys(dealerMap), key => {
      const dealerFilters = [
        {
          field: 'dealerSharedWith.dealerId',
          operator: 'IN',
          values: [key],
        },
        {
          field: 'env',
          operator: 'IN',
          values: dealerMap[key],
        },
      ];
      return [...dealerFilters, ...filtersWithoutStatus];
    });

    const orFilters = _map(andFilters, filter => ({
      operator: OPERATORS.BOOL,
      andFilters: filter,
    }));

    if (_isEmpty(andFilters)) {
      if (_isEmpty(combinedResponse) && !_isEmpty(selectedFilters)) {
        return _selectedFilters;
      }
      return filtersObj;
    }

    return [
      {
        operator: OPERATORS.BOOL,
        orFilters,
      },
    ];
  };

const getCombinedSearchResponse = async (originalFormKey, filters = EMPTY_ARRAY, filterDraft = true) => {
  const envs = getEnvList();
  const promises = _map(envs, env => {
    const { value } = env || EMPTY_OBJECT;
    const payload = createDealerForFormsPayload(originalFormKey, value, filters);
    return formManagement.searchDealerFormsList(payload);
  });
  const responses = await Promise.all(promises);
  let combinedResponse = _concat([], ..._map(responses, response => response.hits));

  if (filterDraft) {
    combinedResponse = _filter(combinedResponse, item => !_includes([FORM_STATUS.DRAFT], _get(item, 'formState', '')));
  }

  combinedResponse = _map(combinedResponse, item => ({ ...item, mapId: `${item.dealerId}-$-${item.env}` }));
  return combinedResponse;
};

const getDefaultSearchFilter = originalFormKey => [
  {
    field: 'originalFormKey',
    operator: 'IN',
    values: [originalFormKey],
  },
  { field: 'formSource', operator: 'IN', values: [FORM_LIBRARY] },
];

export const showArchiveDealerSelection = async ({ getState, setState, params = EMPTY_OBJECT }) => {
  const { key, originalFormKey, onComplete, isRestore = false } = params;
  const defaultFilter = getDefaultSearchFilter(originalFormKey);
  const combinedResponse = await getCombinedSearchResponse(originalFormKey, defaultFilter);
  const itemsToDisable = _map(
    _filter(combinedResponse, item => (isRestore ? !item.deleted : item.deleted)),
    item => item.mapId
  );
  const payload = {
    isRestore,
    itemsToDisable,
    mappingData: combinedResponse,
    defaultPayload: [key],
    onComplete,
  };
  setState(
    produce(draft =>
      _set(draft, 'dealersAssociatedModal', {
        ...draft.dealersAssociatedModal,
        mappingData: combinedResponse,
        modalConfig: {
          showFooter: true,
          submitBtnText: isRestore ? __('Restore') : __('Archive'),
          onModalSubmit: handleOnArchiveSubmit({ getState, setState, payload }),
        },
        tableConfig: {
          type: TABLE_TYPES.WITH_CHECKBOX,
          disabled: itemsToDisable,
          externalColumns: DEALERS_ASSOCIATED_EXTRA_COLUMNS,
          extraColumnConfig: DEALERS_ASSOCIATED_EXTRA_CONFIG(getFieldValue(payload, getState)),
          externalFilters: [_get(getFilters(), FILTER_FIELDS.STATUS)],
          getFiltersPayload: getAssociatedArchiveFilter(originalFormKey, setState),
        },
        visible: true,
        id: key,
        originalFormKey,
      })
    )
  );
};

const hideArchiveConfirmationDialog = () => {
  hideFormArchiveConfirmationDialog();
};

const showCreateCopyDialog = async ({ setState, params = EMPTY_OBJECT }) => {
  const { formKey, formName } = params;
  setState(
    produce(draft =>
      _set(draft, 'createCopyDialog', {
        ...draft.createCopyDialog,
        visible: true,
        formNameToCopy: formName,
        formKeytoCopy: formKey,
      })
    )
  );
};

const hideCreateCopyDialog = async ({ setState }) => {
  setState(produce(draft => _set(draft, 'createCopyDialog', {})));
};

const sendFormForApproval = async ({ params = EMPTY_OBJECT }) => {
  const { formKey, formName, callback } = params;
  try {
    await FormCoreService.sendFormForApproval(formKey);
    callback();
    toaster(TOASTER_TYPE.SUCCESS, __("'{{formName}}' sent for approval.", { formName }));
  } catch {
    toaster(TOASTER_TYPE.ERROR, __("Unable to send '{{formName}}' for approval.", { formName }));
  }
};

const showAuditLogs = ({ params = EMPTY_OBJECT, setState }) => {
  const { assetId, assetName } = params;
  setState(
    produce(draft =>
      _set(draft, 'auditLog', {
        ...draft.auditLog,
        visible: true,
        assetName,
        assetId,
      })
    )
  );
};

const hideAuditLogs = ({ setState }) => {
  setState(produce(draft => _set(draft, 'auditLog', {})));
};

const showDealersAssociatedModal = async ({ setState, params = EMPTY_OBJECT }) => {
  const {
    values: { packageId: id, publishedDealers },
    additional,
  } = params;
  const event = _get(additional, 'event');
  event?.stopPropagation();
  if (publishedDealers.length) {
    setState(
      produce(draft =>
        _set(draft, 'dealersAssociatedModal', {
          ...draft.dealersAssociatedModal,
          modalConfig: null,
          tableConfig: null,
          visible: true,
          id,
        })
      )
    );
  }
};

const hideDealersAssociatedModal = ({ setState }) => {
  setState(
    produce(draft =>
      _set(draft, 'dealersAssociatedModal', {
        ...draft.dealersAssociatedModal,
        modalConfig: null,
        tableConfig: null,
        visible: false,
        id: EMPTY_STRING,
      })
    )
  );
};

export default {
  [ACTION_TYPES.SHOW_PUBLISH_MODAL]: showPublishModal,
  [ACTION_TYPES.HIDE_PUBLISH_MODAL]: hidePublishModal,
  [ACTION_TYPES.SHOW_VERSION_HISTORY_MODAL]: showVersionHistoryModal,
  [ACTION_TYPES.HIDE_VERSION_HISTORY_MODAL]: hideVersionHistoryModal,
  [ACTION_TYPES.DOWNLOAD_PDF]: downloadPdfAction,
  [ACTION_TYPES.SHOW_ARCHIVE_DEALER_SELECTION]: showArchiveDealerSelection,
  [ACTION_TYPES.SHOW_ARCHIVE_CONFIRMATION_DIALOG]: showArchiveConfirmationDialog,
  [ACTION_TYPES.HIDE_ARCHIVE_CONFIRMATION_DIALOG]: hideArchiveConfirmationDialog,
  [ACTION_TYPES.SHOW_CREATE_COPY_DIALOG]: showCreateCopyDialog,
  [ACTION_TYPES.HIDE_CREATE_COPY_DIALOG]: hideCreateCopyDialog,
  [ACTION_TYPES.SEND_FOR_APPROVAL]: sendFormForApproval,
  [ACTION_TYPES.SHOW_AUDIT_LOGS]: showAuditLogs,
  [ACTION_TYPES.HIDE_AUDIT_LOGS]: hideAuditLogs,
  [ACTION_TYPES.SHOW_DEALERS_ASSOCIATED_MODAL]: showDealersAssociatedModal,
  [ACTION_TYPES.HIDE_DEALERS_ASSOCIATED_MODAL]: hideDealersAssociatedModal,
};
