export const FEDERATION_ENABLED_APPS = [
  'ro',
  'core',
  'root',
  'parts',
  'dse-v2',
  'leaderboard',
  'sales',
  'analytics',
  'report-manager',
  'templates',
  'crm',
  'forms',
  'oem-agent',
  'order-management',
  'payroll',
  'vi',
  'dealer-setup',
  'internal',
  'aec-dealer-setup',
  'fni-setup',
  'sales-setup',
  'foundation',
  'accounting',
  'dealer-onboarding',
  'tire-storage',
  'apc',
  'core-setups',
  'service-setups',
  'cashiering',
  'payments',
  'live-communication',
  'communication',
  'coupon-management',
  'fee-management',
  'esign',
  'oem-core',
  'service_reports',
  'analytics-portal',
  'employee-hours',
  'setup-management',
  'crm-lite',
  'internal-apps',
  'hardware-management',
  'business-portal',
  'concierge-setup',
  'tekion-paytek',
  'bulk-edit',
];

export const MODULE = 'module';
export const ENV = 'env';

export const ENV_OPTIONS = [
  {
    label: __('TST_ARM_EKS'),
    value: 'TST_ARM_EKS',
    userSpecificBuildEnabled: true,
  },
  {
    label: __('STAGE_ARM'),
    value: 'STAGE_ARM',
    userSpecificBuildEnabled: true,
  },
  {
    label: __('PREPROD'),
    value: 'PREPROD',
    userSpecificBuildEnabled: false,
  },
  {
    label: __('AZURE_EU_TST'),
    value: 'AZURE_EU_TST',
    userSpecificBuildEnabled: true,
  },
  {
    label: __('PREPROD_FR'),
    value: 'PREPROD_FR',
    userSpecificBuildEnabled: true,
  },
  {
    label: __('PREPROD_RRG'),
    value: 'PREPROD_RRG',
    userSpecificBuildEnabled: false,
  },
  {
    label: __('PROD'),
    value: 'PROD',
    userSpecificBuildEnabled: false,
  },
];

export const PROTECTED_ENV = ['PROD', 'PREPROD'];
