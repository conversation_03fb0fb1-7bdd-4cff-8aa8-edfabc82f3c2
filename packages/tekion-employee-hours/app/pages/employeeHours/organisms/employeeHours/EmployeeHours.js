/* eslint-disable import/order */
import React from 'react';
import PropTypes from 'prop-types';

// Lodash
import _noop from 'lodash/noop';

// Connectors
import withCustomSortTable from 'tcomponents/organisms/withCustomSortTable';

// Builder
import ConfigurableEntityMetadata from 'tbusiness/builders/ConfigurableEntityMetadata';

// Components
import TableManager from 'twidgets/organisms/tableManager';
import SubComponentTable from 'twidgets/appServices/accounting/organisms/table';
import ClockDetailsModal from './organisms/clockDetailsModal';

// Constants
import { DEFAULT_PAGE_SIZE } from 'tbase/constants/tableConstants';
import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import { HEADER_PROPS, TABLE_MANAGER_PROPS } from './constants/employeeHours.general';

// Hooks
import useTableConfig from './hooks/useTableConfig';

const TableComponent = withCustomSortTable(TableManager);

const EmployeeHours = ({
  columns,
  searchText,
  loading,
  sortDetails,
  selectedFilters,
  onAction,
  columnConfigurator,
  contentHeight,
  entityMetadata,
  employeeClockSummaries,
  employeeHoursSummary,
  otConfigurationsByCode,
  otConfigurations,
  timeAndAttendanceSettings,
  jobTitles,
  managersList,
  payFrequencies,
  roleList,
  currentPage,
  pageSize,
  isFetchingNextPage,
  isClockDetailsModalVisible,
  employeeHoursDetailsRow,
  employeeName,
}) => {
  const {
    tableProps,
    filterProps,
    employeeHoursRows,
    l3HeaderProps,
    subHeaderProps,
    getSubComponent,
    handleFormSubmit,
  } = useTableConfig({
    columns,
    searchText,
    loading,
    sortDetails,
    selectedFilters,
    onAction,
    columnConfigurator,
    contentHeight,
    entityMetadata,
    employeeClockSummaries,
    employeeHoursSummary,
    otConfigurationsByCode,
    l3HeaderProps,
    otConfigurations,
    timeAndAttendanceSettings,
    jobTitles,
    managersList,
    payFrequencies,
    roleList,
    currentPage,
    pageSize,
    isFetchingNextPage,
  });

  return (
    <>
      <TableComponent
        columns={columns}
        tableProps={tableProps}
        subHeaderProps={subHeaderProps}
        l3HeaderProps={l3HeaderProps}
        tableComponent={SubComponentTable}
        tableManagerProps={TABLE_MANAGER_PROPS}
        data={employeeHoursRows}
        headerProps={HEADER_PROPS}
        searchText={searchText}
        loading={loading}
        filterProps={filterProps}
        SubComponent={getSubComponent}
        sortDetails={sortDetails}
        selectedFilters={selectedFilters}
        onAction={onAction}
        shouldSetDefaultFilter
      />
      <ClockDetailsModal
        isVisible={isClockDetailsModalVisible}
        onAction={onAction}
        employeeHoursDetailsRow={employeeHoursDetailsRow}
        timeAndAttendanceSettings={timeAndAttendanceSettings}
        employeeName={employeeName}
        handleFormSubmit={handleFormSubmit}
      />
    </>
  );
};

EmployeeHours.propTypes = {
  columns: PropTypes.array,
  searchText: PropTypes.string,
  loading: PropTypes.bool,
  sortDetails: PropTypes.object,
  selectedFilters: PropTypes.array,
  onAction: PropTypes.func,
  columnConfigurator: PropTypes.object.isRequired,
  contentHeight: PropTypes.number.isRequired,
  entityMetadata: PropTypes.instanceOf(ConfigurableEntityMetadata),
  employeeClockSummaries: PropTypes.array,
  employeeHoursSummary: PropTypes.object,
  otConfigurationsByCode: PropTypes.object,
  otConfigurations: PropTypes.array,
  timeAndAttendanceSettings: PropTypes.object,
  jobTitles: PropTypes.array,
  managersList: PropTypes.array,
  payFrequencies: PropTypes.array,
  roleList: PropTypes.array,
  currentPage: PropTypes.number,
  pageSize: PropTypes.number,
  isFetchingNextPage: PropTypes.bool,
  isClockDetailsModalVisible: PropTypes.bool,
  employeeHoursDetailsRow: PropTypes.object,
  employeeName: PropTypes.string,
};

EmployeeHours.defaultProps = {
  columns: EMPTY_ARRAY,
  searchText: undefined,
  loading: false,
  sortDetails: EMPTY_OBJECT,
  selectedFilters: EMPTY_ARRAY,
  onAction: _noop,
  entityMetadata: undefined,
  employeeClockSummaries: EMPTY_ARRAY,
  employeeHoursSummary: EMPTY_OBJECT,
  otConfigurationsByCode: EMPTY_OBJECT,
  otConfigurations: EMPTY_ARRAY,
  timeAndAttendanceSettings: EMPTY_OBJECT,
  jobTitles: EMPTY_ARRAY,
  managersList: EMPTY_ARRAY,
  payFrequencies: EMPTY_ARRAY,
  roleList: EMPTY_ARRAY,
  currentPage: 0,
  pageSize: DEFAULT_PAGE_SIZE,
  isFetchingNextPage: false,
  isClockDetailsModalVisible: false,
  employeeName: '',
  employeeHoursDetailsRow: EMPTY_OBJECT,
};

export default EmployeeHours;
