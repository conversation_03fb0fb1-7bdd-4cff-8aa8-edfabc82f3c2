import useTableConfig from './useTableConfig';

// Helpers
import { getInitialFilterValues } from './helpers/useTableConfig.filterOptions';

// Constants
import { EMPLOYEE_STATUS_FILTER_CONFIG } from './constants/useTableConfig.filterProps';
import { ACTION_TYPE } from './constants/useTableConfig.actionType';

export { getInitialFilterValues, EMPLOYEE_STATUS_FILTER_CONFIG, ACTION_TYPE };

export default useTableConfig;
