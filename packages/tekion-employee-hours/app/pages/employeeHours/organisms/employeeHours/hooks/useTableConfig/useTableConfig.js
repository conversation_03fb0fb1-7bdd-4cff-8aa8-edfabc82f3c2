import { useCallback, useMemo } from 'react';

// Hooks
import { useTekionConversion } from '@tekion/tekion-conversion-web';

// Helpers
import createFilterProps from './helpers/useTableConfig.filterProps';
import makeTableProps from './helpers/useTableConfig.tableProps';
import { makeEmployeeHoursRows } from './helpers/useTableConfig.rows';
import createL3HeaderProps from './helpers/useTableConfig.l3HeaderProps';
import createSubHeaderProps from './helpers/useTableConfig.subHeaderProps';
import { getEmployeeHoursDetailsComponent } from './helpers/useTableConfig.employeeHoursDetailsComponent';

// Readers
import EmployeeHoursSummaryReader from '../../readers/EmployeeHoursSummary';

// Constants
import { ACTION_TYPES } from '../../organisms/clockDetailsModal';

const useTableConfig = ({
  loading,
  selectedFilters,
  columnConfigurator,
  contentHeight,
  entityMetadata,
  employeeClockSummaries,
  otConfigurationsByCode,
  employeeHoursSummary,
  otConfigurations,
  timeAndAttendanceSettings,
  jobTitles,
  managersList,
  payFrequencies,
  roleList,
  isDownloading,
  onAction,
  currentPage,
  pageSize,
  isFetchingNextPage,
}) => {
  const totalNumberOfEntries = EmployeeHoursSummaryReader.numberOfEmployees(employeeHoursSummary);
  const { getFormattedDateAndTime } = useTekionConversion();

  const tableProps = useMemo(
    () =>
      makeTableProps({
        loading,
        columnConfigurator,
        contentHeight,
        currentPage,
        pageSize,
        totalNumberOfEntries,
        isFetchingNextPage,
        employeeClockSummaries,
        onAction,
        getFormattedDateAndTime,
      }),
    [
      loading,
      columnConfigurator,
      contentHeight,
      currentPage,
      pageSize,
      isFetchingNextPage,
      totalNumberOfEntries,
      employeeClockSummaries,
      onAction,
      getFormattedDateAndTime,
    ]
  );

  const filterProps = useMemo(
    () =>
      createFilterProps({
        selectedFilters,
        entityMetadata,
        otConfigurations,
        timeAndAttendanceSettings,
        jobTitles,
        managersList,
        payFrequencies,
        roleList,
      }),
    [
      selectedFilters,
      entityMetadata,
      otConfigurations,
      timeAndAttendanceSettings,
      jobTitles,
      managersList,
      payFrequencies,
      roleList,
    ]
  );

  const employeeHoursRows = useMemo(
    () => makeEmployeeHoursRows(employeeClockSummaries, otConfigurationsByCode),
    [employeeClockSummaries, otConfigurationsByCode]
  );

  const l3HeaderProps = useMemo(() => createL3HeaderProps(employeeHoursSummary), [employeeHoursSummary]);

  const subHeaderProps = useMemo(
    () => createSubHeaderProps({ entityMetadata, isDownloading, onAction }),
    [entityMetadata, isDownloading, onAction]
  );

  const getSubComponent = useCallback(
    rowInfo => getEmployeeHoursDetailsComponent(rowInfo, timeAndAttendanceSettings),
    [timeAndAttendanceSettings]
  );

  const handleFormSubmit = useCallback(
    formValues =>
      onAction({
        type: ACTION_TYPES.HANDLE_CLOCK_DETAILS_FORM_SUBMIT,
        payload: { formValues },
      }),
    [onAction]
  );

  return {
    tableProps,
    filterProps,
    employeeHoursRows,
    l3HeaderProps,
    subHeaderProps,
    getSubComponent,
    handleFormSubmit,
  };
};

export default useTableConfig;
