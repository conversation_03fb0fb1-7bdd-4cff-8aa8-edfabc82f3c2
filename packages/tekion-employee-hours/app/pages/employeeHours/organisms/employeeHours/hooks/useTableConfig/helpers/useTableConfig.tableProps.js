// Lodash
import _size from 'lodash/size';
import _constant from 'lodash/constant';

// Styles
import variables from 'tstyles/exports.scss';

// Constants
import { DEFAULT_PAGE_SIZE } from 'tbase/constants/tableConstants';
import { L3_HEADER_HEIGHT, TABLE_FILTER_HEIGHT } from '../constants/useTableConfig.general';
import ROW_ACTIONS from '../constants/useTableConfig.rowActions';
import { ACTION_TYPE } from '../constants/useTableConfig.actionType';

// Helpers
import { getEmployeeName } from './useTableConfig.general';

const getTableHeight = contentHeight => contentHeight - L3_HEADER_HEIGHT - TABLE_FILTER_HEIGHT;

const getTdProps = () => ({
  cellStyle: { background: variables.white },
});

const handleRowActionClick = (onAction, getFormattedDateAndTime) => (actionType, row) => {
  onAction({
    type: ACTION_TYPE.ROW_ACTION_CLICK,
    payload: {
      actionType,
      getFormattedDateAndTime,
      employeeName: getEmployeeName(row),
    },
  });
};

const makeTableProps = ({
  loading,
  columnConfigurator,
  contentHeight,
  currentPage,
  totalNumberOfEntries = 0,
  pageSize = DEFAULT_PAGE_SIZE,
  isFetchingNextPage,
  employeeClockSummaries,
  onAction,
  getFormattedDateAndTime,
}) => ({
  loading,
  getTdProps,
  expandAllOnUpdate: false,
  totalNumberOfEntries,
  showActions: true,
  className: 'm-0',
  tableHeight: getTableHeight(contentHeight),
  getRowActions: _constant(ROW_ACTIONS),
  onRowActionClick: handleRowActionClick(onAction, getFormattedDateAndTime),
  columnConfigurator,
  hasInfiniteScroll: true,
  currentPage: currentPage + 1,
  pageSize,
  isFetchingNextPage,
  getIsAllDataFetched: () => totalNumberOfEntries === _size(employeeClockSummaries),
});

export default makeTableProps;
