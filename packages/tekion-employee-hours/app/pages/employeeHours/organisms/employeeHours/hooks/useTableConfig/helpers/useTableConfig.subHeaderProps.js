// Components
import BulkActions from 'twidgets/molecules/bulkActions';

// Constants
import { ACTION_TYPE } from '../constants/useTableConfig.actionType';
import { BULK_ACTION_OPTIONS, DOWNLOAD_ACTION, PRINT_ACTION } from '../constants/useTableConfig.general';

const onBulkActionClick = onAction => params => {
  onAction({ type: ACTION_TYPE.BULK_ACTION, payload: params });
};

const renderBulkActions = onAction => ({
  renderer: BulkActions,
  renderOptions: {
    options: BULK_ACTION_OPTIONS,
    onClick: onBulkActionClick(onAction),
  },
  action: ACTION_TYPE.BULK_ACTION,
});

const createSubHeaderRightActions = onAction => {
  const bulkActions = renderBulkActions(onAction);
  const subHeaderRightActions = [DOWNLOAD_ACTION, PRINT_ACTION, bulkActions];
  return subHeaderRightActions;
};

const createSubHeaderProps = onAction => {
  const subHeaderProps = {
    rightHeaderClassName: 'flex align-items-center',
    subHeaderRightActions: createSubHeaderRightActions(onAction),
  };

  return subHeaderProps;
};

export default createSubHeaderProps;
