import React, { useCallback } from 'react';
import _head from 'lodash/head';
import PropTypes from 'prop-types';

// Components
import Switch from 'tcomponents/molecules/Switch';

// Styles
import Style from './customSwitchFilter.module.scss';

const CustomSwitchFilter = ({ values, onChange, ...restProps }) => {
  const handleChange = useCallback(
    (value, eventDetails) => {
      onChange({
        value,
        eventDetails,
        option: value,
      });
    },
    [onChange]
  );
  return <Switch {...restProps} checked={_head(values)} className={Style.customSwitchFilter} onChange={handleChange} />;
};
CustomSwitchFilter.propTypes = {
  values: PropTypes.array,
  onChange: PropTypes.func.isRequired,
};

CustomSwitchFilter.defaultProps = {
  values: [false],
};

export default CustomSwitchFilter;
