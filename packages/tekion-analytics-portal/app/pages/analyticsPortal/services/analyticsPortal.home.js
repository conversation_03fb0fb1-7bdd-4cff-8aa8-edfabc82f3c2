import { EMPTY_OBJECT, EMPTY_STRING } from 'tbase/app.constants';
import { URL_TYPES } from 'tbase/constants/api';
import Http from 'tbase/services/apiService/httpClient';

export const sendCreateDashboardEvent = payload =>
  Http.put(URL_TYPES.CDMS, `analyticsv2/u/process/events/create-dashboard`, payload);

export const sendCreateReportEvent = payload =>
  Http.put(URL_TYPES.CDMS, `analyticsv2/u/process/events/create-report`, payload);

export const fetchDataSourceList = (payload = EMPTY_OBJECT) =>
  Http.get(URL_TYPES.CDMS, 'analyticsv2/u/security-settings/users/datasources', payload);

export const createDashboard = payload => Http.post(URL_TYPES.CDMS, `analyticsv2/u/bi-entities/dashboards`, payload);

export const getBiEntitites = payload => Http.post(URL_TYPES.CDMS, `analyticsv2/u/bi-entities/filter`, payload);

export const updateContentFavouriteStatus = (contentId, payload) =>
  Http.put(URL_TYPES.CDMS, `analyticsv2/u/bi-entities/${contentId}/favourites`, payload);

export const deleteContent = payload => Http.delete(URL_TYPES.CDMS, `/analyticsv2/u/bi-entities`, payload);

export const fetchContentPermissionConfig = (contentId = EMPTY_STRING, accessTypes = EMPTY_STRING) =>
  Http.get(URL_TYPES.CDMS, `analyticsv2/u/bi-entities/permissions/${contentId}?accessTypes=${accessTypes}`);

export const saveContentPermissionConfig = (contentId = EMPTY_STRING, payload = EMPTY_OBJECT) =>
  Http.put(URL_TYPES.CDMS, `analyticsv2/u/bi-entities/permissions/${contentId}/share`, payload);
