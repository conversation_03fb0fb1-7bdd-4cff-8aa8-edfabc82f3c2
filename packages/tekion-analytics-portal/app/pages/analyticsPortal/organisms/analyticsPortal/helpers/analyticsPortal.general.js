// Lodash
import _reduce from 'lodash/reduce';
import _includes from 'lodash/includes';

// Constants
import {
  ACTIONS_VS_PERMISSIONS_MAP,
  HIDDEN_ACTIONS,
  ANNOTATION_TYPE,
  CHART_TYPE,
  NOTES_INFO_REGEX,
  COPY_REPORT_ACTION_VS_PERMISSIONS_MAP,
  COPY_DASHBOARD_ACTION_VS_PERMISSIONS_MAP,
  TS_ERROR_TYPE,
} from '../constants/analyticsPortal.constants';
import { NOTES_ASSET_TYPE_LIST } from '../constants/analyticsPortal.notesAssetTypes';
import { REPORT } from '../analyticsPortalHome/constants/analyticsPortalHome.contentTypes';

const getPermissionControlledHiddenActions = permissions => (hiddenActions, linkedPermission, action) => {
  if (!_includes(permissions, linkedPermission)) hiddenActions.push(action);
  return hiddenActions;
};

const getHiddenCopyActions = (permissions, contentType) => {
  if (contentType === REPORT.id)
    return _reduce(COPY_REPORT_ACTION_VS_PERMISSIONS_MAP, getPermissionControlledHiddenActions(permissions), []);
  return _reduce(COPY_DASHBOARD_ACTION_VS_PERMISSIONS_MAP, getPermissionControlledHiddenActions(permissions), []);
};

export const getHiddenActions = (permissions, contentType) => {
  const permissionControlledHiddenActions = _reduce(
    ACTIONS_VS_PERMISSIONS_MAP,
    getPermissionControlledHiddenActions(permissions),
    []
  );
  const hiddenCopyActions = getHiddenCopyActions(permissions, contentType);
  const hiddenActions = [...permissionControlledHiddenActions, ...hiddenCopyActions, ...HIDDEN_ACTIONS];
  return hiddenActions;
};

export const isNoteCell = ({ displayMode, noteValue, noteMetaData }) =>
  displayMode === CHART_TYPE.TABLE_MODE && noteValue?.includes('🗒️') && noteMetaData?.[0] === ANNOTATION_TYPE.NOTES;

export const extractNoteContext = params => {
  const { event } = params;
  const eventData = event?.data;
  const displayMode = eventData?.embedAnswerData?.displayMode;

  const noteValue = eventData?.selectedPoints?.[0]?.selectedAttributes?.[0]?.value;

  const noteInfo = noteValue?.match(NOTES_INFO_REGEX);
  const noteMetaData = noteInfo?.[1]?.split('_');

  return {
    displayMode,
    noteValue,
    noteMetaData,
  };
};

export const handleNoteCellClick = ({ setState, params }) => {
  const { displayMode, noteValue, noteMetaData } = extractNoteContext(params);

  const isNoteCellClicked = isNoteCell({ displayMode, noteValue, noteMetaData });

  if (isNoteCellClicked) {
    const [, notesType = null, assetId = null, assetNumber = null] = noteMetaData || [];

    if (assetId && notesType && NOTES_ASSET_TYPE_LIST[notesType]) {
      const noteType = NOTES_ASSET_TYPE_LIST[notesType];
      setState({
        isNotesModalVisible: true,
        notesDetails: { noteType, assetId, assetNumber },
      });
    }
  }
};

export const checkShouldShowError = error => {
  if (!error) return false;

  if (error?.data?.errorType === TS_ERROR_TYPE.API) return false;

  if (error?.offlineWarning) return false;

  return true;
};
