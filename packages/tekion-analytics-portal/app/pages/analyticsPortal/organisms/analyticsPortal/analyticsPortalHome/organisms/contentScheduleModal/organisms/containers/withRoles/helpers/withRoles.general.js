// Services
import { fetchAllRoles } from 'tbusiness/services/rolesService';

// Utils
import getDataFromResponse from 'tbase/utils/getDataFromResponse';
import { toaster, TOASTER_TYPE } from 'tcomponents/organisms/NotificationWrapper';
import { getErrorMessage } from 'tbase/utils/errorUtils';

export const getAllRoles = async (setIsFetching, setAllRoles) => {
  try {
    const response = await fetchAllRoles();
    const roles = getDataFromResponse(response);
    setAllRoles(roles);
  } catch (error) {
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(error, __('Failed to fetch roles')));
  } finally {
    setIsFetching(false);
  }
};
