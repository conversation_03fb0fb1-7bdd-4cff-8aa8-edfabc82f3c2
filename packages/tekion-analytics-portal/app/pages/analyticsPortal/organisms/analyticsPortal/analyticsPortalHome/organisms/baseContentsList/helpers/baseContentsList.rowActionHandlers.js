import { DELETE_CONTENT, SCHEDULE_CONTENT, SHARE_CONTENT } from '../constants/baseContentsList.rowActions';

const handleShareContent = ({ params, setState }) => {
  const { content } = params;
  setState({ clickedContentItem: content, isShareModalVisible: true });
};

const handleDeleteContent = ({ params, setState }) => {
  const { content } = params;
  setState({ clickedContentItem: content, isDeleteModalVisible: true });
};

const handleScheduleContent = ({ params, setState }) => {
  const { content } = params;
  setState({ clickedContentItem: content, isReportSchedulerModalVisible: true });
};

export const ROW_ACTION_HANDLERS = {
  [SHARE_CONTENT.id]: handleShareContent,
  [DELETE_CONTENT.id]: handleDeleteContent,
  [SCHEDULE_CONTENT.id]: handleScheduleContent,
};
