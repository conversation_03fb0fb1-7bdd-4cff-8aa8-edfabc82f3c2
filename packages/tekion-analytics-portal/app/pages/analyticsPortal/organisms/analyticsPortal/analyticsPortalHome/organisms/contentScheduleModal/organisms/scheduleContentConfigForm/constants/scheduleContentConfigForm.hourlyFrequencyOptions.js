// Lodash
import _keyBy from 'lodash/keyBy';

// Utils
import standardFieldOptionMapper from 'tbase/utils/optionMappers/standardFieldMapper';

const FIRST = {
  name: __('1'),
  id: 1,
};

const SECOND = {
  name: __('2'),
  id: 2,
};

const THIRD = {
  name: __('3'),
  id: 3,
};

const FOURTH = {
  name: __('4'),
  id: 4,
};

const FIFTH = {
  name: __('5'),
  id: 5,
};

const SIXTH = {
  name: __('6'),
  id: 6,
};

const SEVENTH = {
  name: __('7'),
  id: 7,
};

const EIGHTH = {
  name: __('8'),
  id: 8,
};

const NINTH = {
  name: __('9'),
  id: 9,
};

const TENTH = {
  name: __('10'),
  id: 10,
};

const ELEVENTH = {
  name: __('11'),
  id: 11,
};

const TWELFTH = {
  name: __('12'),
  id: 12,
};

const THIRTEENTH = {
  name: __('13'),
  id: 13,
};

const FOURTEENTH = {
  name: __('14'),
  id: 14,
};

const FIFTEENTH = {
  name: __('15'),
  id: 15,
};

const SIXTEENTH = {
  name: __('16'),
  id: 16,
};

const SEVENTEENTH = {
  name: __('17'),
  id: 17,
};

const EIGHTEENTH = {
  name: __('18'),
  id: 18,
};

const NINETEENTH = {
  name: __('19'),
  id: 19,
};

const TWENTIETH = {
  name: __('20'),
  id: 20,
};

const TWENTY_FIRST = {
  name: __('21'),
  id: 21,
};

const TWENTY_SECOND = {
  name: __('22'),
  id: 22,
};

const TWENTY_THIRD = {
  name: __('23'),
  id: 23,
};

const HOURLY_FREQUENCY_TYPES = [
  FIRST,
  SECOND,
  THIRD,
  FOURTH,
  FIFTH,
  SIXTH,
  SEVENTH,
  EIGHTH,
  NINTH,
  TENTH,
  ELEVENTH,
  TWELFTH,
  THIRTEENTH,
  FOURTEENTH,
  FIFTEENTH,
  SIXTEENTH,
  SEVENTEENTH,
  EIGHTEENTH,
  NINETEENTH,
  TWENTIETH,
  TWENTY_FIRST,
  TWENTY_SECOND,
  TWENTY_THIRD,
];

const HOURLY_FREQUENCY_OPTIONS = standardFieldOptionMapper(undefined, HOURLY_FREQUENCY_TYPES);

export const HOURLY_FREQUENCY_TYPES_BY_ID = _keyBy(HOURLY_FREQUENCY_TYPES, 'id');

export default HOURLY_FREQUENCY_OPTIONS;
