// Utils
import { toMoment, getEndEpoch } from 'tbase/utils/dateUtils';

// reader
import baseContentsListReader from 'pages/analyticsPortal/organisms/analyticsPortal/analyticsPortalHome/readers/BaseContentsList';

// constants
import {
  DAILY,
  HOURLY,
  MONTHLY,
  WEEKLY,
} from '../../contentScheduleModal/organisms/scheduleContentConfigForm/constants/scheduleContentConfigForm.frequencyOptions';
import {
  OCCURENCES,
  SPECIFIC_DATE,
} from '../../contentScheduleModal/organisms/scheduleContentConfigForm/constants/scheduleContentConfigForm.endsOnTypeOptions';
import FIELD_IDS from '../../contentScheduleModal/organisms/scheduleContentConfigForm/constants/scheduleContentConfigForm.fieldIds';

const addHoursAndMinutesToDate = (startDate, startTime) => {
  const minutes = toMoment(startTime).minutes();
  const hour = toMoment(startTime).hour();
  return toMoment(startDate).set('hour', hour).set('minute', minutes).set('seconds', 0).set('millisecond', 0).valueOf();
};

const getStartDateWithStartTime = scheduleConfigFormValues => {
  const startDate = scheduleConfigFormValues?.[FIELD_IDS.START_DATE];
  const startTime = scheduleConfigFormValues?.[FIELD_IDS.START_TIME];
  const startDateTime = addHoursAndMinutesToDate(startDate, startTime);
  return startDateTime;
};

const getFrequencyConfig = ({
  frequency,
  hourlySchedule,
  dailySchedule,
  weeklySchedule = [],
  monthlySchedule = [],
}) => {
  switch (frequency) {
    case HOURLY.id:
      return { hourly: { value: String(hourlySchedule) } };

    case DAILY.id:
      return { daily: { value: dailySchedule ? 'EVERY_WEEK_DAY' : 'EVERY_DAY' } };

    case WEEKLY.id:
      return { weekly: { days: weeklySchedule } };

    case MONTHLY.id:
      return { monthly: { days: monthlySchedule } };

    default:
      return {};
  }
};

const getEndDateValue = endDate => getEndEpoch('day', endDate);

const getEndScheduleConfig = ({ endsOnType, endOccurences, endDate }) => {
  if (endsOnType === OCCURENCES.id) {
    return { endOccurences: { value: endOccurences } };
  }

  if (endsOnType === SPECIFIC_DATE.id && endDate) {
    return { endDateTime: getEndDateValue(endDate) };
  }

  return {};
};

const getRecipientConfig = ({ userIds = [], roleIds = [] }) => {
  const config = {};

  if (userIds.length > 0) {
    config.userIds = userIds;
  }

  if (roleIds.length > 0) {
    config.roleIds = roleIds;
  }

  return config;
};

export const getScheduleConfigsDTO = ({ clickedContentItem, scheduleConfigFormValues, timeZone }) => {
  const {
    scheduleName,
    customMessage = '',
    shareFormat = [],
    reportFormat,
    frequency,
    hourlySchedule,
    dailySchedule,
    weeklySchedule = [],
    monthlySchedule = [],
    endsOnType,
    endOccurences,
    endDate,
    userIds = [],
    recipientType,
    roleIds = [],
  } = scheduleConfigFormValues;

  const biEntityId = clickedContentItem?.id;
  const originType = baseContentsListReader.originType(clickedContentItem);
  const contentType = baseContentsListReader.contentType(clickedContentItem);

  const sendAsAttachment = shareFormat.includes('ATTACHMENT');
  const sendAsLink = shareFormat.includes('LINK');

  return {
    name: scheduleName,
    customMessage,
    exportFormat: reportFormat,
    sendAsAttachment,
    sendAsLink,
    startDateTime: getStartDateWithStartTime(scheduleConfigFormValues),
    frequency,
    timeZone,
    status: 'ACTIVE',
    biEntityId,
    originType,
    contentType,
    recipientType,
    endsOnType,
    ...getFrequencyConfig({ frequency, hourlySchedule, dailySchedule, weeklySchedule, monthlySchedule }),
    ...getEndScheduleConfig({ endsOnType, endOccurences, endDate }),
    ...getRecipientConfig({ userIds, roleIds }),
  };
};
