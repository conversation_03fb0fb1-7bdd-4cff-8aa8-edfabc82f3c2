/* eslint-disable import/order */
import React, { useCallback } from 'react';
import PropTypes from 'prop-types';

// lodash
import _noop from 'lodash/noop';

// components
import Modal from 'tcomponents/molecules/Modal';
import ScheduleContentConfigForm from './organisms/scheduleContentConfigForm';

// helper
import { triggerSubmit } from 'tcomponents/pages/formPage/utils/formAction';

// Constants
import { FORM_CONTEXT_ID } from './organisms/scheduleContentConfigForm/constants/scheduleContentConfigForm.general';
import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from 'tbase/app.constants';

function ContentScheduleModal(props) {
  const {
    title,
    submitBtnText,
    onAction,
    onParentAction,
    onCancel,
    onSubmit,
    clickedContentItem,
    isReportSchedulerModalVisible,
    initialValues,
    loading,
    dealerTimeZone,
  } = props;

  const handleSubmit = useCallback(() => {
    triggerSubmit(FORM_CONTEXT_ID);
  }, EMPTY_ARRAY);

  const handleFormSubmit = useCallback(
    formValues => {
      onSubmit(formValues);
    },
    [onSubmit]
  );

  return (
    <Modal
      visible={isReportSchedulerModalVisible}
      title={title}
      width={Modal.SIZES.L}
      minWidth={Modal.SIZES.L}
      centered
      destroyOnClose
      onCancel={onCancel}
      onSubmit={handleSubmit}
      keyboard={false}
      maskClosable={false}
      loading={loading}
      submitBtnText={submitBtnText}
      secondaryBtnText={__('Cancel')}>
      <ScheduleContentConfigForm
        onSubmit={handleFormSubmit}
        initialValues={initialValues}
        clickedContentItem={clickedContentItem}
        dealerTimeZone={dealerTimeZone}
      />
    </Modal>
  );
}

ContentScheduleModal.propTypes = {
  title: PropTypes.string,
  submitBtnText: PropTypes.string,
  onAction: PropTypes.func,
  onParentAction: PropTypes.func,
  onCancel: PropTypes.func,
  onSubmit: PropTypes.func,
  clickedContentItem: PropTypes.object,
  isReportSchedulerModalVisible: PropTypes.bool,
  loading: PropTypes.bool,
  dealerTimeZone: PropTypes.string,
  initialValues: PropTypes.object,
};

ContentScheduleModal.defaultProps = {
  title: EMPTY_STRING,
  submitBtnText: EMPTY_STRING,
  onAction: _noop,
  onParentAction: _noop,
  onCancel: _noop,
  onSubmit: _noop,
  clickedContentItem: EMPTY_OBJECT,
  isReportSchedulerModalVisible: false,
  loading: false,
  dealerTimeZone: EMPTY_STRING,
  initialValues: EMPTY_OBJECT,
};

export default ContentScheduleModal;
