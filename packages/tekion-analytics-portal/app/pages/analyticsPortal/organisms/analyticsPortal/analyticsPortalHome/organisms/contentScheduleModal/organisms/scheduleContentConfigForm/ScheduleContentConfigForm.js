/* eslint-disable import/order */
import React, { useContext } from 'react';
import PropTypes from 'prop-types';
import { compose } from 'recompose';

// lodash
import _noop from 'lodash/noop';

// Components
import { FormWithSubmission } from 'tcomponents/pages/formPage';

// Context
import { PermissionContext } from 'tcomponents/widgets/permissionsHelper';

// Containers
import withFormPageState from 'tcomponents/connectors/withFormPageState';
import withRoles from '../containers/withRoles';

// hooks
import useMemoizedCallback from 'tbase/customHooks/useMemoizedCallback';

// Helpers
import getFields from './helpers/scheduleContentConfigForm.getFields';
import getSections from './helpers/scheduleContentConfigForm.getSections';
import FIELD_CHANGE_HANDLERS from './helpers/scheduleContentConfigForm.onFieldChange';

// Constants
import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from 'tbase/app.constants';
import {
  FORM_CONTEXT_ID,
  FORM_CONFIG_DEPENDENT_KEYS,
  FORM_SECTION_DEPENDANT_KEYS,
} from './constants/scheduleContentConfigForm.general';

const FormWithState = withFormPageState()(FormWithSubmission);

const ScheduleContentConfigForm = props => {
  const { onSubmit, initialValues, clickedContentItem, allRoles, fieldsMetadataByDataSource, dealerTimeZone } = props;

  const memoizedGetFields = useMemoizedCallback(getFields);
  const memoizedGetSections = useMemoizedCallback(getSections);

  const permissions = useContext(PermissionContext);

  return (
    <FormWithState
      contextId={FORM_CONTEXT_ID}
      getFields={memoizedGetFields}
      getSections={memoizedGetSections}
      onSubmit={onSubmit}
      className="full-width"
      initialValues={initialValues}
      mapFieldToOnChange={FIELD_CHANGE_HANDLERS}
      formConfigDependantKeys={FORM_CONFIG_DEPENDENT_KEYS}
      formSectionDependantKeys={FORM_SECTION_DEPENDANT_KEYS}
      clickedContentItem={clickedContentItem}
      allRoles={allRoles}
      dealerTimeZone={dealerTimeZone}
      permissions={permissions}
      fieldsMetadataByDataSource={fieldsMetadataByDataSource}
    />
  );
};

ScheduleContentConfigForm.propTypes = {
  onSubmit: PropTypes.func,
  initialValues: PropTypes.object,
  allRoles: PropTypes.array,
  clickedContentItem: PropTypes.object,
  fieldsMetadataByDataSource: PropTypes.object,
  dealerTimeZone: PropTypes.string,
};

ScheduleContentConfigForm.defaultProps = {
  initialValues: EMPTY_OBJECT,
  onSubmit: _noop,
  allRoles: EMPTY_ARRAY,
  clickedContentItem: EMPTY_OBJECT,
  fieldsMetadataByDataSource: EMPTY_OBJECT,
  dealerTimeZone: EMPTY_STRING,
};

export default compose(withRoles)(ScheduleContentConfigForm);
