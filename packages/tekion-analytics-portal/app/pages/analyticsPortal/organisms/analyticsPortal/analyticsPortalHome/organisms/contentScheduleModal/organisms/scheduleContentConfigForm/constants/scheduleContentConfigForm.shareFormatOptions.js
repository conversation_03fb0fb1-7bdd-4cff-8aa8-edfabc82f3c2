import _keyBy from 'lodash/keyBy';

export const SEND_AS_LINK = {
  value: 'LINK',
  label: __('Send as link'),
};

export const SEND_AS_AN_ATTACHMENT = {
  value: 'ATTACHMENT',
  label: __('Send as an attachment'),
};

const SHARE_FORMAT_OPTIONS = [SEND_AS_LINK, SEND_AS_AN_ATTACHMENT];

export const SHARE_FORMAT_OPTIONS_BY_VALUE = _keyBy(SHARE_FORMAT_OPTIONS, 'value');

export default SHARE_FORMAT_OPTIONS;
