// Lodash
import _pick from 'lodash/pick';
import _noop from 'lodash/noop';

// Constants
import TABLE_ACTION_TYPES from 'tcomponents/organisms/TableManager/constants/actionTypes';
import { EMPTY_OBJECT } from 'tbase/app.constants';
import APP_SERVICES from 'tbase/constants/appServices';
import { ANALYTICS_PORTAL_ROUTES } from '../../../constants/analyticsPortalHome.general';
import ACTION_TYPES from '../constants/baseContentsList.actionTypes';
import { DASHBOARD } from '../../../constants/analyticsPortalHome.contentTypes';

// Helpers
import { deleteContentItem, getContentsList, toggleContentFavouriteStatus } from './baseContentsList.contentsList';
import { ROW_ACTION_HANDLERS } from './baseContentsList.rowActionHandlers';

// Readers
import baseContentsListReader from '../../../readers/BaseContentsList';

const handleFilterChange = ({ params, getState, setState }) => {
  const { searchText, sortDetails, selectedOriginType, isFavToggleButtonSelected, currentPage } = getState();
  const { value: selectedFilters } = params || EMPTY_OBJECT;
  setState({ selectedFilters });
  return getContentsList({
    selectedFilters,
    searchText,
    sortDetails,
    selectedOriginType,
    isFavToggleButtonSelected,
    currentPage,
    setState,
  });
};

const handleSortChange = ({ params, getState, setState }) => {
  const { searchText, selectedFilters, selectedOriginType, isFavToggleButtonSelected, currentPage } = getState();
  const { sortTypeMap, column } = params?.value || EMPTY_OBJECT;
  const keyToSort = column?.key;
  const sortDetails = _pick(sortTypeMap, keyToSort);
  setState({ sortDetails });
  return getContentsList({
    selectedFilters,
    searchText,
    sortDetails,
    selectedOriginType,
    isFavToggleButtonSelected,
    currentPage,
    setState,
  });
};

const handlePageUpdate = ({ params, getState, setState }) => {
  const { selectedFilters, searchText, sortDetails, selectedOriginType, isFavToggleButtonSelected } = getState();
  const { page } = params.value;
  setState({ currentPage: page });
  return getContentsList({
    selectedFilters,
    searchText,
    sortDetails,
    selectedOriginType,
    isFavToggleButtonSelected,
    currentPage: page,
    setState,
  });
};

const handleSearch = ({ params, getState, setState }) => {
  const { selectedFilters, sortDetails, selectedOriginType, isFavToggleButtonSelected, currentPage } = getState();
  const { value: searchText } = params;
  setState({ searchText });
  return getContentsList({
    selectedFilters,
    searchText,
    sortDetails,
    selectedOriginType,
    isFavToggleButtonSelected,
    currentPage,
    setState,
  });
};

const handleMyFavToggleClick = ({ getState, setState }) => {
  const { selectedFilters, searchText, sortDetails, selectedOriginType, isFavToggleButtonSelected, currentPage } =
    getState();
  setState({ isFavToggleButtonSelected: !isFavToggleButtonSelected });
  return getContentsList({
    selectedFilters,
    searchText,
    sortDetails,
    selectedOriginType,
    isFavToggleButtonSelected: !isFavToggleButtonSelected,
    currentPage,
    setState,
  });
};

const handleRowActionClick = ({ params, getState, setState }) => {
  const { rowActionType } = params;
  const actionHandler = ROW_ACTION_HANDLERS[rowActionType] || _noop;
  actionHandler({ params, getState, setState });
};

const handleToggleFavourite = ({ params, getState, setState }) => {
  const { selectedOriginType, contentsList, isFavToggleButtonSelected } = getState();
  const { row } = params;
  const { _original: rowData } = row;
  const favSelected = baseContentsListReader.isFavourite(rowData);
  return toggleContentFavouriteStatus({
    selectedOriginType,
    content: rowData,
    favouriteValue: !favSelected,
    contentsList,
    setState,
    isFavToggleButtonSelected,
  });
};

const handleTableItemsFetch = ({ getState, setState }) => {
  const { selectedFilters, searchText, sortDetails, selectedOriginType, isFavToggleButtonSelected, currentPage } =
    getState();
  return getContentsList({
    selectedFilters,
    searchText,
    sortDetails,
    selectedOriginType,
    isFavToggleButtonSelected,
    currentPage,
    setState,
  });
};

const handleCancelDeleteDialog = ({ setState }) => {
  setState({ clickedContentItem: EMPTY_OBJECT, isDeleteModalVisible: false });
};

const handleSubmitDeleteDialog = ({ getState, setState }) => {
  const { clickedContentItem, contentsList } = getState();
  setState({ isDeleteInProgress: true });
  return deleteContentItem({ clickedContentItem, setState, contentsList });
};

const handleRowClick = ({ params, getState }) => {
  const { navigate } = getState();
  const selectedContentItem = params?.value?.original;
  const referenceId = baseContentsListReader.referenceId(selectedContentItem);
  const contentType = baseContentsListReader.contentType(selectedContentItem);
  if (contentType === DASHBOARD.id)
    return navigate(`/${APP_SERVICES.ANALYTICS_PORTAL}/${ANALYTICS_PORTAL_ROUTES.DASHBOARD_PAGE}/${referenceId}`);
  return navigate(`/${APP_SERVICES.ANALYTICS_PORTAL}/${ANALYTICS_PORTAL_ROUTES.REPORT_PAGE}/${referenceId}`);
};

const handleCancelShareModal = ({ setState }) => {
  setState({ clickedContentItem: EMPTY_OBJECT, isShareModalVisible: false });
};

const handleCancelReportSchedulerModal = ({ setState }) => {
  setState({ clickedContentItem: EMPTY_OBJECT, isReportSchedulerModalVisible: false });
};

const ACTION_HANDLERS = {
  [TABLE_ACTION_TYPES.TABLE_ITEMS_SET_FILTER]: handleFilterChange,
  [TABLE_ACTION_TYPES.TABLE_ITEMS_SORT]: handleSortChange,
  [TABLE_ACTION_TYPES.TABLE_ITEMS_PAGE_UPDATE]: handlePageUpdate,
  [TABLE_ACTION_TYPES.TABLE_SEARCH]: handleSearch,
  [TABLE_ACTION_TYPES.TABLE_ITEMS_FETCH]: handleTableItemsFetch,
  [ACTION_TYPES.MY_FAV_TOGGLE_BUTTON_CLICK]: handleMyFavToggleClick,
  [ACTION_TYPES.ROW_ACTION_CLICK]: handleRowActionClick,
  [ACTION_TYPES.TOGGLE_FAVOURITE]: handleToggleFavourite,
  [ACTION_TYPES.CANCEL_DELETE_DIALOG]: handleCancelDeleteDialog,
  [ACTION_TYPES.SUBMIT_DELETE_DIALOG]: handleSubmitDeleteDialog,
  [ACTION_TYPES.CANCEL_SHARE_MODAL]: handleCancelShareModal,
  [ACTION_TYPES.CANCEL_REPORT_SCHEDULER_MODAL]: handleCancelReportSchedulerModal,
  [TABLE_ACTION_TYPES.TABLE_ITEM_CLICK]: handleRowClick,
};

export default ACTION_HANDLERS;
