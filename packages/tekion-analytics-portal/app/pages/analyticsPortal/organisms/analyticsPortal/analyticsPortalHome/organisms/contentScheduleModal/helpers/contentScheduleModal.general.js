import BASE_CONTENTS_LIST_ACTION_TYPES from 'pages/analyticsPortal/organisms/analyticsPortal/analyticsPortalHome/organisms/baseContentsList/constants/baseContentsList.actionTypes';

import ACTION_TYPES from 'pages/analyticsPortal/organisms/analyticsPortal/analyticsPortalHome/organisms/scheduleContentModal/constants/scheduleContentModal.actionTypes';

export const closeScheduleModal = onAction => () => {
  onAction({
    type: BASE_CONTENTS_LIST_ACTION_TYPES.CANCEL_REPORT_SCHEDULER_MODAL,
  });
};

export const submitNewScheduleReport =
  ({ onAction }) =>
  scheduleConfigFormValues => {
    console.log(scheduleConfigFormValues, ' -- scheduleConfigFormValues');
    // onAction({
    //   type: ACTION_TYPES.CREATE_NEW_SCHEDULE,
    //   payload: { scheduleConfigFormValues },
    // });
  };
