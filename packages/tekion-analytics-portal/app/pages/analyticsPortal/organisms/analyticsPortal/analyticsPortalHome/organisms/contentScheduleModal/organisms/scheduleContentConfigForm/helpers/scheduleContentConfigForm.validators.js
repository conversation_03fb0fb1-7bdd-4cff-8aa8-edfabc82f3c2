import * as DateUtils from 'tbase/utils/dateUtils';

// Constants
import FIELD_IDS from '../constants/scheduleContentConfigForm.fieldIds';

export const isEndDateAfterStartDate = (_fieldId, value, formValues) => {
  const startDate = formValues?.[FIELD_IDS.START_DATE];
  const endDate = DateUtils.toMoment(value);
  if (startDate && endDate) {
    if (DateUtils.isBefore(startDate, endDate, { unit: 'day' })) {
      return {
        isValid: false,
        message: __('End Date cannot be lesser than Start Date.'),
      };
    }
  }

  return { isValid: true };
};
