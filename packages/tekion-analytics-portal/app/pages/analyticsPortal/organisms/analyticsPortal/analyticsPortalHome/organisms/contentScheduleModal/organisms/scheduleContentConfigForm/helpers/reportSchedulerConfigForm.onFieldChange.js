// Lodash
import _omit from 'lodash/omit';

// Constants
import FIELD_IDS from '../constants/reportSchedulerConfigForm.fieldIds';
import { RECIPIENT_TYPE_FIELD_IDS_TO_OMIT_MAP } from '../constants/scheduleContentConfigForm.general';

// Helpers
import { isScheduledOnce } from './scheduleContentConfigForm.general';

const getUpdatedFormValuesOnFrequencyChange = (formValues, frequency) => ({
  ...formValues,
  [FIELD_IDS.FREQUENCY]: frequency,
  [FIELD_IDS.DAILY_SCHEDULE]: undefined,
  [FIELD_IDS.WEEKLY_SCHEDULE]: undefined,
  [FIELD_IDS.FREQUENCY_RECURRENCE_ON]: undefined,
  [FIELD_IDS.FREQUENCY_RECURRENCE_TYPE]: undefined,
  [FIELD_IDS.FREQUENCY_RECURRENCE_VALUE]: undefined,
  [FIELD_IDS.ENDS_ON_TYPE]: getFieldValueOnFrequencyChange({
    formValues,
    fieldId: FIELD_IDS.ENDS_ON_TYPE,
    frequency,
  }),
  [FIELD_IDS.END_DATE]: getFieldValueOnFrequencyChange({
    formValues,
    fieldId: FIELD_IDS.END_DATE,
    frequency,
  }),
  [FIELD_IDS.END_RECURRENCE]: getFieldValueOnFrequencyChange({
    formValues,
    fieldId: FIELD_IDS.END_RECURRENCE,
    frequency,
  }),
});

const getFieldValueOnFrequencyChange = ({ formValues, fieldId, frequency }) => {
  if (isScheduledOnce(frequency)) {
    return undefined;
  }
  return formValues[fieldId];
};
const handleFrequencyChange = ({ getState, setState, params }) => {
  const { values: formValues } = getState();
  const { value: frequency } = params;
  const updatedFormValues = getUpdatedFormValuesOnFrequencyChange(formValues, frequency);
  setState({
    values: updatedFormValues,
  });
};

const getUpdatedFormValuesOnEndsOnTypeChange = (formValues, endsOnType) => ({
  ...formValues,
  [FIELD_IDS.ENDS_ON_TYPE]: endsOnType,
  [FIELD_IDS.END_DATE]: undefined,
  [FIELD_IDS.END_RECURRENCE]: undefined,
});

const handleEndsOnTypeChange = ({ getState, setState, params }) => {
  const { values: formValues } = getState();
  const { value: endsOnType } = params;
  const updatedFormValues = getUpdatedFormValuesOnEndsOnTypeChange(formValues, endsOnType);
  setState({
    values: updatedFormValues,
  });
};

const getUpdatedFormValuesOnRecipientChange = (formValues, recipientType) => ({
  ...formValues,
  [FIELD_IDS.RECIPIENT_TYPE]: recipientType,
});

const getUpdatedErrorsOnRecipientTypeChange = (errors, recipientType) => {
  const recipientTypeFieldIdsToOmit = RECIPIENT_TYPE_FIELD_IDS_TO_OMIT_MAP[recipientType];
  const updatedErrors = _omit(errors, recipientTypeFieldIdsToOmit);
  return updatedErrors;
};

const handleRecipientTypeChange = ({ getState, setState, params }) => {
  const { values: formValues, errors } = getState();
  const { value: recipientType } = params;
  const updatedFormValues = getUpdatedFormValuesOnRecipientChange(formValues, recipientType);
  const updatedErrors = getUpdatedErrorsOnRecipientTypeChange(errors, recipientType);
  setState({
    values: updatedFormValues,
    errors: updatedErrors,
  });
};

const FIELD_CHANGE_HANDLERS = {
  // [FIELD_IDS.ENDS_ON_TYPE]: handleEndsOnTypeChange,
  // [FIELD_IDS.FREQUENCY]: handleFrequencyChange,
  // [FIELD_IDS.RECIPIENT_TYPE]: handleRecipientTypeChange,
};

export default FIELD_CHANGE_HANDLERS;
