// withRoles.js
import React, { useEffect, useState } from 'react';

// Components
import Loader from 'tcomponents/molecules/loader';

// helpers
import { getAllRoles } from './helpers/withRoles.general';

// constants
import { EMPTY_ARRAY } from 'tbase/app.constants';

function withRoles(WrappedComponent) {
  function WithRoles(props) {
    const [allRoles, setAllRoles] = useState([]);
    const [isFetching, setIsFetching] = useState(true);

    useEffect(() => {
      getAllRoles(setIsFetching, setAllRoles);
    }, EMPTY_ARRAY);

    if (isFetching) {
      return <Loader />;
    }

    return <WrappedComponent {...props} allRoles={allRoles} />;
  }

  return WithRoles;
}

export default withRoles;
