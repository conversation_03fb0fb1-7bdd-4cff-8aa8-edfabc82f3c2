import React, { useMemo } from 'react';
import PropTypes from 'prop-types';

// lodash
import _noop from 'lodash/noop';

// constants
import { EMPTY_OBJECT, EMPTY_STRING } from 'tbase/app.constants';
import { EDIT_SCHEDULE_CONTENT_MODAL_TITLE } from '../../constants/scheduleContentModal.general';

// helpers
import {
  closeScheduleModal,
  submitNewScheduleReport,
} from '../../../contentScheduleModal/helpers/contentScheduleModal.general';

// components
import ContentScheduleModal from '../../../contentScheduleModal';
import { getInitialValues } from './helpers/createScheduleContentModal.general';

const EditScheduleContentModal = props => {
  const {
    onAction,
    onParentAction,
    clickedContentItem,
    isReportSchedulerModalVisible,
    scheduleConfig,
    loading,
    dealerTimeZone,
  } = props;

  const handleCancel = useMemo(() => closeScheduleModal(onParentAction), [onParentAction]);
  const handleSubmit = useMemo(() => submitNewScheduleReport({ onAction }), [onAction]);

  const initialValues = useMemo(
    () => getInitialValues(scheduleConfig, clickedContentItem),
    [clickedContentItem, scheduleConfig]
  );

  return (
    <ContentScheduleModal
      title={EDIT_SCHEDULE_CONTENT_MODAL_TITLE}
      onAction={onAction}
      onParentAction={onParentAction}
      onCancel={handleCancel}
      onSubmit={handleSubmit}
      clickedContentItem={clickedContentItem}
      isReportSchedulerModalVisible={isReportSchedulerModalVisible}
      initialValues={initialValues}
      loading={loading}
      dealerTimeZone={dealerTimeZone}
      scheduleConfig={scheduleConfig}
    />
  );
};

EditScheduleContentModal.propTypes = {
  onAction: PropTypes.func,
  onParentAction: PropTypes.func,
  clickedContentItem: PropTypes.object,
  isReportSchedulerModalVisible: PropTypes.bool,
  scheduleConfig: PropTypes.object,
  loading: PropTypes.bool,
  dealerTimeZone: PropTypes.string,
};

EditScheduleContentModal.defaultProps = {
  onAction: _noop,
  onParentAction: _noop,
  clickedContentItem: EMPTY_OBJECT,
  isReportSchedulerModalVisible: false,
  scheduleConfig: EMPTY_OBJECT,
  loading: false,
  dealerTimeZone: EMPTY_STRING,
};

export default EditScheduleContentModal;
