import React from 'react';
import PropTypes from 'prop-types';

// lodash
import _noop from 'lodash/noop';

// constants
import { EMPTY_OBJECT } from 'tbase/app.constants';
import { EDIT_SCHEDULE_CONTENT_MODAL_TITLE } from '../../constants/scheduleContentModal.general';

// components
import ContentScheduleModal from '../../../contentScheduleModal';

const EditScheduleContentModal = props => {
  const { onAction, onParentAction, clickedContentItem, isReportSchedulerModalVisible } = props;

  return (
    <ContentScheduleModal
      title={EDIT_SCHEDULE_CONTENT_MODAL_TITLE}
      onAction={onAction}
      onParentAction={onParentAction}
      clickedContentItem={clickedContentItem}
      isReportSchedulerModalVisible={isReportSchedulerModalVisible}
    />
  );
};

EditScheduleContentModal.propTypes = {
  onAction: PropTypes.func,
  onParentAction: PropTypes.func,
  clickedContentItem: PropTypes.object,
  isReportSchedulerModalVisible: PropTypes.bool,
};

EditScheduleContentModal.defaultProps = {
  onAction: _noop,
  onParentAction: _noop,
  clickedContentItem: EMPTY_OBJECT,
  isReportSchedulerModalVisible: false,
};

export default EditScheduleContentModal;
