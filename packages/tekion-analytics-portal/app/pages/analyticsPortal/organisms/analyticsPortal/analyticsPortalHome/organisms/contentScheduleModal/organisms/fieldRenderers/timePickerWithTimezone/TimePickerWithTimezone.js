import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';

// Lodash
import _noop from 'lodash/noop';

// Components
import { Error } from 'tcomponents/organisms/FormBuilder';
import { TimePicker } from 'tcomponents/atoms';

// Constants
import actionTypes from 'tcomponents/organisms/FormBuilder/constants/actionTypes';
import { EMPTY_STRING } from 'tbase/app.constants';

// Styles
import styles from './timePickerWithTimezone.module.scss';

const TimePickerWithTimezone = props => {
  const { timePickerComponent: TimePickerComponent, fieldClassName, warning, dealerTimeZone, ...rest } = props;

  const handleChange = value => {
    const { id, onAction } = props;
    onAction({
      type: actionTypes.ON_FIELD_CHANGE,
      payload: {
        id,
        value,
      },
    });
  };

  return (
    <div className={fieldClassName}>
      <TimePickerComponent {...rest} className={styles.timePicker} onChange={handleChange} />
      <span className={styles.dealerTimezone}>{dealerTimeZone}</span>
      <Error key="warning" errorClassName={styles.errorClassName} error={warning} />
    </div>
  );
};

TimePickerWithTimezone.propTypes = {
  id: PropTypes.string.isRequired,
  onAction: PropTypes.func,
  timePickerComponent: PropTypes.elementType,
  fieldClassName: PropTypes.string,
  value: PropTypes.object,
  disabled: PropTypes.bool,
  warning: PropTypes.string,
  dealerTimeZone: PropTypes.string,
};

TimePickerWithTimezone.defaultProps = {
  onAction: _noop,
  timePickerComponent: TimePicker,
  fieldClassName: undefined,
  value: undefined,
  disabled: false,
  warning: undefined,
  dealerTimeZone: EMPTY_STRING,
};

export default TimePickerWithTimezone;
