import React, { useMemo } from 'react';
import PropTypes from 'prop-types';

// lodash
import _noop from 'lodash/noop';

// constants
import { EMPTY_OBJECT, EMPTY_STRING } from 'tbase/app.constants';
import { CREATE_SCHEDULE_CONTENT_MODAL_TITLE } from '../../constants/scheduleContentModal.general';

// helpers
import {
  closeScheduleModal,
  submitNewScheduleReport,
} from '../../../contentScheduleModal/helpers/contentScheduleModal.general';
import { getInitialValues } from './helpers/createScheduleContentModal.general';

// components
import ContentScheduleModal from '../../../contentScheduleModal';

const CreateScheduleContentModal = props => {
  const { onAction, onParentAction, clickedContentItem, isReportSchedulerModalVisible, loading, dealerTimeZone } =
    props;

  const handleCancel = useMemo(() => closeScheduleModal(onParentAction), [onParentAction]);
  const handleSubmit = useMemo(() => submitNewScheduleReport({ onAction }), [onAction]);

  const initialValues = useMemo(() => getInitialValues(clickedContentItem), [clickedContentItem]);

  return (
    <ContentScheduleModal
      title={CREATE_SCHEDULE_CONTENT_MODAL_TITLE}
      submitBtnText={__('Save')}
      onAction={onAction}
      onParentAction={onParentAction}
      onCancel={handleCancel}
      onSubmit={handleSubmit}
      clickedContentItem={clickedContentItem}
      isReportSchedulerModalVisible={isReportSchedulerModalVisible}
      initialValues={initialValues}
      loading={loading}
      dealerTimeZone={dealerTimeZone}
    />
  );
};

CreateScheduleContentModal.propTypes = {
  onAction: PropTypes.func,
  onParentAction: PropTypes.func,
  clickedContentItem: PropTypes.object,
  isReportSchedulerModalVisible: PropTypes.bool,
  loading: PropTypes.bool,
  dealerTimeZone: PropTypes.string,
};

CreateScheduleContentModal.defaultProps = {
  onAction: _noop,
  onParentAction: _noop,
  clickedContentItem: EMPTY_OBJECT,
  isReportSchedulerModalVisible: false,
  loading: false,
  dealerTimeZone: EMPTY_STRING,
};

export default CreateScheduleContentModal;
