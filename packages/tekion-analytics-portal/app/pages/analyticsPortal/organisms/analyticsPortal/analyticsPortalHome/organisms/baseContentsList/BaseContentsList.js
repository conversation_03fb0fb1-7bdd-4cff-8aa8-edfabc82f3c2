/* eslint-disable import/order */
import React, { useContext } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';

// Selectors
import { getLoginData } from 'tcomponents/pages/authentication/reducers/authentication.selectors';

// Lodash
import _size from 'lodash/size';

// Connectors
import withCustomSortTable from 'tcomponents/organisms/withCustomSortTable';

// context
import { PermissionContext } from 'tcomponents/widgets/permissionsHelper';

// components
import TableManager from 'twidgets/organisms/tableManager';
import DeleteConfirmationModal from '../../molecules/deleteConfirmationModal';
import ShareModal from '../shareModal';
import EnterpriseShareModal from '../enterpriseShareModal';
import ScheduleContentModal from '../scheduleContentModal';

// Constants
import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import { TABLE_MANAGER_PROPS } from './constants/baseContentsList.general';

// Hooks
import useTableProps from './hooks/useTableProps';

// Helpers
import { isEnterpriseWorkspaceEnabled } from 'pages/analyticsPortal/helpers/analyticsPortal.general';

const SortableTableManager = withCustomSortTable(TableManager);

function BaseContentsList(props) {
  const {
    columns,
    filterProps,
    tableManagerProps,
    contentList,
    onAction,
    searchText,
    sortDetails,
    isFetchingNextPage,
    currentPage,
    loading,
    totalCount,
    showActions,
    subHeaderProps,
    clickedContentItem,
    isDeleteModalVisible,
    isDeleteInProgress,
    isShareModalVisible,
    isReportSchedulerModalVisible,
    loginData,
  } = props;

  const permissions = useContext(PermissionContext);

  const tableProps = useTableProps({
    isFetchingNextPage,
    currentPage,
    loading,
    totalCount: _size(contentList),
    onAction,
    showActions,
    permissions,
    loginData,
  });

  const ShareComponent = isEnterpriseWorkspaceEnabled(loginData) ? EnterpriseShareModal : ShareModal;

  return (
    <>
      <SortableTableManager
        onAction={onAction}
        data={contentList}
        searchText={searchText}
        tableProps={tableProps}
        tableManagerProps={tableManagerProps}
        columns={columns}
        filterProps={filterProps}
        sortDetails={sortDetails}
        type="FIXED_COLUMN"
        subHeaderProps={subHeaderProps}
      />
      <DeleteConfirmationModal
        onAction={onAction}
        clickedContentItem={clickedContentItem}
        isDeleteModalVisible={isDeleteModalVisible}
        isDeleteInProgress={isDeleteInProgress}
      />
      <ShareComponent
        onParentAction={onAction}
        clickedContentItem={clickedContentItem}
        isShareModalVisible={isShareModalVisible}
        loginData={loginData}
      />
      <ScheduleContentModal
        onParentAction={onAction}
        clickedContentItem={clickedContentItem}
        isReportSchedulerModalVisible={isReportSchedulerModalVisible}
      />
    </>
  );
}

BaseContentsList.propTypes = {
  columns: PropTypes.array,
  filterProps: PropTypes.array,
  tableManagerProps: PropTypes.object,
  contentList: PropTypes.array,
  onAction: PropTypes.func.isRequired,
  searchText: PropTypes.string,
  sortDetails: PropTypes.object,
  isFetchingNextPage: PropTypes.bool,
  currentPage: PropTypes.number,
  loading: PropTypes.bool,
  totalCount: PropTypes.number,
  showActions: PropTypes.bool,
  clickedContentItem: PropTypes.object,
  isDeleteModalVisible: PropTypes.bool,
  isDeleteInProgress: PropTypes.bool,
  isShareModalVisible: PropTypes.bool,
  isReportSchedulerModalVisible: PropTypes.bool,
  loginData: PropTypes.object,
};

BaseContentsList.defaultProps = {
  columns: EMPTY_ARRAY,
  filterProps: EMPTY_ARRAY,
  tableManagerProps: TABLE_MANAGER_PROPS,
  contentList: EMPTY_ARRAY,
  searchText: undefined,
  sortDetails: EMPTY_OBJECT,
  isFetchingNextPage: false,
  currentPage: 0,
  loading: false,
  totalCount: 0,
  showActions: false,
  clickedContentItem: EMPTY_OBJECT,
  isDeleteModalVisible: false,
  isDeleteInProgress: false,
  isShareModalVisible: false,
  isReportSchedulerModalVisible: false,
  loginData: EMPTY_OBJECT,
};

const mapStateToProps = state => ({
  loginData: getLoginData(state),
});

export default connect(mapStateToProps)(BaseContentsList);
