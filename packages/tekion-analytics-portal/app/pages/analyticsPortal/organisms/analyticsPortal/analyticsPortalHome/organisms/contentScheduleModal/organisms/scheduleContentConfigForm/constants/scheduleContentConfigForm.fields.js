import React from 'react';

import _noop from 'lodash/noop';

// Components
import Select from 'tcomponents/organisms/FormBuilder/fieldRenderers/select';
import RadioField from 'tcomponents/organisms/FormBuilder/fieldRenderers/radio';
import NumberInputField from 'tcomponents/organisms/FormBuilder/fieldRenderers/numberInputField';
import InputDatePickerField from 'tcomponents/organisms/FormBuilder/fieldRenderers/inputDatePickerField';
import DaySelection from '@tekion/tekion-components/src/organisms/FormBuilder/fieldRenderers/daySelection';
import CheckboxGroupRenderer from 'tcomponents/organisms/FormBuilder/fieldRenderers/checkboxGroup';
import CheckBoxWrapper from 'tcomponents/atoms/checkboxGroup/';
import CheckBoxRenderer from 'tcomponents/organisms/FormBuilder/fieldRenderers/checkbox';
import TextInput from 'tcomponents/organisms/FormBuilder/fieldRenderers/textInput';
import TextArea from 'tcomponents/organisms/FormBuilder/fieldRenderers/textArea';
import MultiSelectField from 'tcomponents/organisms/FormBuilder/fieldRenderers/MultiSelectField';
import withSelectInput from '@tekion/tekion-components/src/organisms/FormBuilder/hoc/withSelectInput';
import AsyncMultiSelect from '@tekion/tekion-components/src/molecules/tableInputField/cellRenderers/AsyncMultiSelectCellRenderer';
import TimePickerWithTimezone from '../../fieldRenderers/timePickerWithTimezone';

// Utils
import { isRequiredRule } from 'tbase/utils/formValidators';

// Constants
import { RADIO_TYPES } from 'tcomponents/atoms/Radio';
import { RESOURCE_TYPE } from 'tbase/bulkResolvers/constants/resourceType';
import { DATE_TIME_FORMATS } from 'tbase/utils/dateUtils';
import REPORT_FORMAT_OPTIONS from './scheduleContentConfigForm.reportFormatOptions';
import RECIPIENT_TYPE_OPTIONS, { USER } from './scheduleContentConfigForm.recipientTypeOptions';
import SHARE_FORMAT_OPTIONS from './scheduleContentConfigForm.shareFormatOptions';
import FIELD_IDS from './scheduleContentConfigForm.fieldIds';
import FREQUENCY_OPTIONS from './scheduleContentConfigForm.frequencyOptions';
import ENDS_ON_TYPE_OPTIONS from './scheduleContentConfigForm.endsOnTypeOptions';
import ACTIVE_USER_FILTER from './scheduleContentConfigForm.activeUserFilter';
import HOURLY_FREQUENCY_OPTIONS from './scheduleContentConfigForm.hourlyFrequencyOptions';
import MONTHLY_FREQUENCY_OPTIONS from './scheduleContentConfigForm.monthlyFrequencyOptions';

// Helpers
import { isPastDate } from '../helpers/scheduleContentConfigForm.general';

// Validators
import { isEndDateAfterStartDate } from '../helpers/scheduleContentConfigForm.validators';

// Styles
import styles from '../scheduleContentConfigForm.module.scss';

const AsyncMultiSelectField = withSelectInput(AsyncMultiSelect);

const {
  SCHEDULE_NAME,
  ADD_CUSTOM_MESSAGE,
  CUSTOM_MESSAGE,
  SHARE_FORMAT,
  REPORT_FORMAT,
  START_DATE,
  START_TIME,
  FREQUENCY,
  HOURLY_SCHEDULE,
  DAILY_SCHEDULE,
  WEEKLY_SCHEDULE,
  MONTHLY_SCHEDULE,
  END_DATE,
  ENDS_ON_TYPE,
  END_OCCURENCES,
  RECIPIENT_TYPE,
  USERS,
  ROLES,
} = FIELD_IDS;

export const SCHEDULE_NAME_FIELD = {
  id: SCHEDULE_NAME,
  renderer: TextInput,
  renderOptions: {
    label: __('Schedule Name'),
    required: true,
    validators: [isRequiredRule],
    size: 6,
  },
};

export const ADD_CUSTOM_MESSAGE_FIELD = {
  id: ADD_CUSTOM_MESSAGE,
  renderer: CheckBoxRenderer,
  renderOptions: {
    checkboxLabel: __('Add Custom Message'),
  },
};

export const CUSTOM_MESSAGE_FIELD = {
  id: CUSTOM_MESSAGE,
  renderer: TextArea,
  renderOptions: {
    label: __('Custom Message'),
  },
};

export const SHARE_FORMAT_FIELD = {
  id: SHARE_FORMAT,
  renderer: CheckboxGroupRenderer,
  renderOptions: {
    type: CheckBoxWrapper,
    // defaultValue: SEND_AS_LINK.value,
    required: true,
    validators: [isRequiredRule],
    options: SHARE_FORMAT_OPTIONS,
    label: __('Format'),
    checkboxContainerClassName: styles.checkboxContainerClassName,
    checkboxClassName: styles.shareFormatCheckbox,
  },
};

export const REPORT_FORMAT_FIELD = {
  id: REPORT_FORMAT,
  renderer: Select,
  renderOptions: {
    label: __('Report format'),
    options: REPORT_FORMAT_OPTIONS,
    required: true,
    validators: [isRequiredRule],
    size: 4,
  },
};

export const START_DATE_FIELD = {
  id: START_DATE,
  renderer: InputDatePickerField,
  renderOptions: {
    label: __('Start Date'),
    required: true,
    validators: [isRequiredRule],
    disabledDate: isPastDate,
  },
};

export const START_TIME_FIELD = {
  id: START_TIME,
  renderer: TimePickerWithTimezone,
  renderOptions: {
    label: __('Schedule Time'),
    format: DATE_TIME_FORMATS.HOUR_MIN_FORMAT_WITHOUT_ZERO,
    minuteStep: 30,
    use12Hours: true,
    allowClear: false,
    inputReadOnly: true,
    required: true,
  },
};

export const FREQUENCY_FIELD = {
  id: FREQUENCY,
  renderer: Select,
  renderOptions: {
    label: __('Frequency'),
    options: FREQUENCY_OPTIONS,
    required: true,
    validators: [isRequiredRule],
    size: 6,
  },
};

export const HOURLY_SCHEDULE_FIELD = {
  id: HOURLY_SCHEDULE,
  renderer: Select,
  renderOptions: {
    label: __('Every'),
    options: HOURLY_FREQUENCY_OPTIONS,
    required: true,
    validators: [isRequiredRule],
    size: 6,
  },
};

export const DAILY_SCHEDULE_FIELD = {
  id: DAILY_SCHEDULE,
  renderer: CheckBoxRenderer,
  renderOptions: {
    checkboxLabel: __('Exclude Weekends'),
    fieldClassName: styles.defaultAggregation,
  },
};

export const WEEKLY_SCHEDULE_FIELD = {
  id: WEEKLY_SCHEDULE,
  renderer: DaySelection,
  renderOptions: {
    onSelect: _noop,
    size: 6,
    className: 'm-b-4',
    required: true,
    validators: [isRequiredRule],
  },
};

export const MONTHLY_SCHEDULE_FIELD = {
  id: MONTHLY_SCHEDULE,
  renderer: MultiSelectField,
  renderOptions: {
    label: __('Every'),
    size: 6,
    required: true,
    validators: [isRequiredRule],
    options: MONTHLY_FREQUENCY_OPTIONS,
  },
};

export const ENDS_ON_TYPE_FIELD = {
  id: ENDS_ON_TYPE,
  renderer: Select,
  renderOptions: {
    label: __('Ends'),
    options: ENDS_ON_TYPE_OPTIONS,
    required: true,
    validators: [isRequiredRule],
    size: 6,
  },
};

export const END_OCCURENCES_FIELD = {
  id: END_OCCURENCES,
  renderer: NumberInputField,
  renderOptions: {
    label: __('No. of Occureneces'),
    size: 6,
    shouldDisabledStepper: true,
    min: 0,
    required: true,
    validators: [isRequiredRule],
  },
};

export const END_DATE_FIELD = {
  id: END_DATE,
  renderer: InputDatePickerField,
  renderOptions: {
    label: __('Date'),
    size: 6,
    disabledDate: isPastDate,
    required: true,
    validators: [isRequiredRule, isEndDateAfterStartDate],
  },
};

export const RECIPIENT_TYPE_FIELD = {
  id: RECIPIENT_TYPE,
  renderer: RadioField,
  renderOptions: {
    type: RADIO_TYPES.RADIO,
    radios: RECIPIENT_TYPE_OPTIONS,
    defaultValue: USER.value,
  },
};

export const USERS_FIELD = {
  id: USERS,
  renderer: AsyncMultiSelectField,
  renderOptions: {
    label: __('Select Users'),
    placeholder: __('Select Users'),
    resourceType: RESOURCE_TYPE.TENANT_USER_MINIMAL_V2,
    shouldFetchOnValueChange: true,
    required: true,
    validators: [isRequiredRule],
    filters: [ACTIVE_USER_FILTER],
    dropDownClassName: true,
    size: 6,
  },
};

export const ROLES_FIELD = {
  id: ROLES,
  renderer: MultiSelectField,
  renderOptions: {
    label: __('Select Roles'),
    placeholder: __('Select Roles'),
    showSelectedValueLabels: true,
    required: true,
    validators: [isRequiredRule],
    size: 6,
  },
};
