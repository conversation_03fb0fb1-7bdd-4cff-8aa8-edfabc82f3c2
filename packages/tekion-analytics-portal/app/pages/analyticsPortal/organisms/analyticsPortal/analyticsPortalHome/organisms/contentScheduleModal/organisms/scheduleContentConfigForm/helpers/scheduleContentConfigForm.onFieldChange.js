// Constants
import { EMPTY_ARRAY } from 'tbase/app.constants';
import FIELD_IDS from '../constants/scheduleContentConfigForm.fieldIds';

const handleRecipientTypeChange = ({ getState, setState, params }) => {
  const { values: formValues } = getState();
  const { value: recipientType } = params;
  const updatedFormValues = {
    ...formValues,
    [FIELD_IDS.RECIPIENT_TYPE]: recipientType,
    [FIELD_IDS.ROLES]: EMPTY_ARRAY,
    [FIELD_IDS.USERS]: EMPTY_ARRAY,
  };
  setState({
    values: updatedFormValues,
  });
};

const handleRolesChange = ({ getState, setState, params }) => {
  const { values: formValues } = getState();
  const { value: roles } = params;
  console.log(roles, '--- selected roles ---');
  const updatedFormValues = {
    ...formValues,
    [FIELD_IDS.ROLES]: roles,
  };
  console.log(updatedFormValues, '--- updatedFormValues ---');
  console.log(formValues, '--- formValues ---');
  setState({
    values: updatedFormValues,
  });
};

const FIELD_CHANGE_HANDLERS = {
  [FIELD_IDS.RECIPIENT_TYPE]: handleRecipientTypeChange,
  [FIELD_IDS.ROLES]: handleRolesChange,
};

export default FIELD_CHANGE_HANDLERS;
