import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { compose } from 'recompose';

// lodash
import _noop from 'lodash/noop';

// Containers
import withActions from 'tcomponents/connectors/withActions';

// constants
import { EMPTY_OBJECT, EMPTY_STRING } from 'tbase/app.constants';
import { INITIAL_STATE } from './constants/scheduleContentModal.general';

// helpers
import ACTION_HANDLERS from './helpers/scheduleContentModal.actionHandlers';

// components
import CreateScheduleContentModal from './organisms/createScheduleContentModal';
import EditScheduleContentModal from './organisms/editScheduleContentModal';
import ACTION_TYPES from './constants/scheduleContentModal.actionTypes';

const ScheduleContentModal = props => {
  const {
    onAction,
    onParentAction,
    clickedContentItem,
    isReportSchedulerModalVisible,
    isEditMode,
    loading,
    dealerTimeZone,
  } = props;

  const ContentScheduleModal = isEditMode ? EditScheduleContentModal : CreateScheduleContentModal;

  useEffect(() => {
    onAction({ type: ACTION_TYPES.GET_DEALER_TIMEZONE });
  }, [onAction]);

  return (
    <ContentScheduleModal
      onParentAction={onParentAction}
      onAction={onAction}
      clickedContentItem={clickedContentItem}
      isReportSchedulerModalVisible={isReportSchedulerModalVisible}
      loading={loading}
      dealerTimeZone={dealerTimeZone}
    />
  );
};

ScheduleContentModal.propTypes = {
  onAction: PropTypes.func,
  onParentAction: PropTypes.func,
  clickedContentItem: PropTypes.object,
  isReportSchedulerModalVisible: PropTypes.bool,
  isEditMode: PropTypes.bool,
  loading: PropTypes.bool,
  dealerTimeZone: PropTypes.string,
};

ScheduleContentModal.defaultProps = {
  onAction: _noop,
  onParentAction: _noop,
  clickedContentItem: EMPTY_OBJECT,
  isReportSchedulerModalVisible: false,
  isEditMode: false,
  loading: false,
  dealerTimeZone: EMPTY_STRING,
};

export default compose(withActions(INITIAL_STATE, ACTION_HANDLERS))(ScheduleContentModal);
