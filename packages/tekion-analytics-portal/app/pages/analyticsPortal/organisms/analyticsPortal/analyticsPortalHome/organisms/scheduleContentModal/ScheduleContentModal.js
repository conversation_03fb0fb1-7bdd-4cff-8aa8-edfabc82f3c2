import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { compose } from 'recompose';

// lodash
import _noop from 'lodash/noop';

// Containers
import withActions from 'tcomponents/connectors/withActions';

// constants
import { EMPTY_OBJECT, EMPTY_STRING } from 'tbase/app.constants';
import { INITIAL_STATE } from './constants/scheduleContentModal.general';

// helpers
import ACTION_HANDLERS from './helpers/scheduleContentModal.actionHandlers';

// components
import CreateScheduleContentModal from './organisms/createScheduleContentModal';
import EditScheduleContentModal from './organisms/editScheduleContentModal';
import ACTION_TYPES from './constants/scheduleContentModal.actionTypes';

const ScheduleContentModal = props => {
  const {
    onAction,
    onParentAction,
    clickedContentItem,
    isReportSchedulerModalVisible,
    isEditMode,
    loading,
    dealerTimeZone,
    scheduleConfig,
  } = props;

  const ContentScheduleModal = !isEditMode ? EditScheduleContentModal : CreateScheduleContentModal;

  useEffect(() => {
    onAction({ type: ACTION_TYPES.GET_DEALER_TIMEZONE });
  }, [onAction]);

  const tmp = {
    recipientType: 'USER',
    scheduleName: 'nkjkj ',
    addCustomMessage: true,
    customMessage: 'deqc',
    shareFormat: ['LINK', 'ATTACHMENT'],
    reportFormat: 'PDF',
    // startDate: '2025-07-23T08:48:11.477Z',
    // startTime: '2025-07-23T20:30:23.000Z',
    frequency: 'MONTHLY',
    hourlySchedule: 2,
    endsOnType: 'SPECIFIC_DATE',
    userIds: ['001fffe0-5777-4262-bc6e-efac094df0d5'],
    weeklySchedule: ['SUNDAY', 'MONDAY'],
    monthlySchedule: [1, 2],
    endOccurences: 2,
    // endDate: '2025-07-30T08:52:00.368Z',
  };

  return (
    <ContentScheduleModal
      onParentAction={onParentAction}
      onAction={onAction}
      clickedContentItem={clickedContentItem}
      isReportSchedulerModalVisible={isReportSchedulerModalVisible}
      loading={loading}
      dealerTimeZone={dealerTimeZone}
      scheduleConfig={tmp}
    />
  );
};

ScheduleContentModal.propTypes = {
  onAction: PropTypes.func,
  onParentAction: PropTypes.func,
  clickedContentItem: PropTypes.object,
  isReportSchedulerModalVisible: PropTypes.bool,
  isEditMode: PropTypes.bool,
  loading: PropTypes.bool,
  dealerTimeZone: PropTypes.string,
  scheduleConfig: PropTypes.object,
};

ScheduleContentModal.defaultProps = {
  onAction: _noop,
  onParentAction: _noop,
  clickedContentItem: EMPTY_OBJECT,
  isReportSchedulerModalVisible: false,
  isEditMode: false,
  loading: false,
  dealerTimeZone: EMPTY_STRING,
  scheduleConfig: EMPTY_OBJECT,
};

export default compose(withActions(INITIAL_STATE, ACTION_HANDLERS))(ScheduleContentModal);
