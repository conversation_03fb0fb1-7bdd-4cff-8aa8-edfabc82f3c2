import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';

export const TABLE_MANAGER_PROPS = {
  showFilter: true,
  showHeader: false,
  showSubHeader: true,
  showSearchByField: false,
  showSearch: true,
};

export const INITIAL_STATE = {
  selectedFilters: EMPTY_ARRAY,
  sortDetails: EMPTY_OBJECT,
  searchText: undefined,
  isFavToggleButtonSelected: false,
  currentPage: 1,
  isLoading: false,
  contentsList: EMPTY_ARRAY,
  totalContentsCount: 0,
  clickedContentItem: EMPTY_OBJECT,
  isDeleteModalVisible: false,
  isDeleteInProgress: false,
  isShareModalVisible: false,
  isScheduleHistoryVisible: true,
};

export const BASE_CONTENTS_LIST_PAGE_SIZE = 50;
