/* eslint-disable import/order */
// Constants
import { EDIT } from 'pages/analyticsPortal/organisms/accessSettings/constants/accessSettings.accessTypes';
import { DELETE_CONTENT, SCHEDULE_CONTENT, SHARE_CONTENT } from '../constants/baseContentsList.rowActions';
import { PERMISSIONS } from 'tbase/constants/permissions';
import { DASHBOARD } from '../../../constants/analyticsPortalHome.contentTypes';

// Helpers
import { permissionValidations } from 'tcomponents/widgets/permissionsHelper';

// Reader
import baseContentsListReader from '../../../readers/BaseContentsList';
import {
  isCorporateWorkSpace,
  isEnterpriseWorkspaceEnabled,
} from 'pages/analyticsPortal/helpers/analyticsPortal.general';

const isDeleteDisabled = (permissions, content) => {
  let isDeletePermissionExisting = false;
  const contentType = baseContentsListReader.contentType(content);
  if (contentType === DASHBOARD.id) {
    isDeletePermissionExisting = permissionValidations.validatePermissions(permissions, {
      validForAny: [PERMISSIONS.ANALYTICS_PORTAL.ANALYTICS_PORTAL.DELETE_DASHBOARD],
    });
  } else {
    isDeletePermissionExisting = permissionValidations.validatePermissions(permissions, {
      validForAny: [PERMISSIONS.ANALYTICS_PORTAL.ANALYTICS_PORTAL.DELETE_REPORT],
    });
  }
  const accessType = baseContentsListReader.accessType(content);
  return accessType !== EDIT.id || !isDeletePermissionExisting;
};

const isShareDisabled = (permissions, loginData) => {
  const hasSharePermission = permissionValidations.validatePermissions(permissions, {
    validForAny: [PERMISSIONS.ANALYTICS_PORTAL.ANALYTICS_PORTAL.ENABLE_SHARE],
  });
  const enterpriseEnabled = isEnterpriseWorkspaceEnabled(loginData);
  if (!enterpriseEnabled) return !hasSharePermission;

  const corporateWorkspace = isCorporateWorkSpace(loginData);

  return !hasSharePermission || !corporateWorkspace;
};

const isReportSchedulerDisabled = permissions => {
  const hasSchedulePermission = permissionValidations.validatePermissions(permissions, {
    validForAny: [PERMISSIONS.ANALYTICS_PORTAL.ANALYTICS_PORTAL.ENABLE_SCHEDULE],
  });

  return !hasSchedulePermission;
};

const getBaseContentsListRowActions = ({ permissions, content, loginData }) => {
  const updatedShareAction = { ...SHARE_CONTENT, disabled: isShareDisabled(permissions, loginData) };
  const updatedDeleteAction = { ...DELETE_CONTENT, disabled: isDeleteDisabled(permissions, content) };
  const updatedReportSchedulerAction = {
    ...SCHEDULE_CONTENT,
    disabled: isReportSchedulerDisabled(permissions, content),
  };
  const baseContentsListRowActions = [updatedDeleteAction, updatedShareAction, updatedReportSchedulerAction];
  return baseContentsListRowActions;
};
export default getBaseContentsListRowActions;
