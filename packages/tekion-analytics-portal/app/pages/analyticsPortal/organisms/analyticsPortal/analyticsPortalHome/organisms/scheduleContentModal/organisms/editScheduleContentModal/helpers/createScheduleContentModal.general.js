import { compose } from 'recompose';

// Utils
import { toMoment } from 'tbase/utils/dateUtils';

import SCHEDULE_CONTENT_CONFIG_FORM_FIELD_IDS from 'pages/analyticsPortal/organisms/analyticsPortal/analyticsPortalHome/organisms/contentScheduleModal/organisms/scheduleContentConfigForm/constants/scheduleContentConfigForm.fieldIds';

import { DASHBOARD } from 'pages/analyticsPortal/organisms/analyticsPortal/analyticsPortalHome/constants/analyticsPortalHome.contentTypes';
import { USER } from '../../../../contentScheduleModal/organisms/scheduleContentConfigForm/constants/scheduleContentConfigForm.recipientTypeOptions';

const getStartDateTime = compose(toMoment, startDateTime);

export const getInitialValues = scheduleConfig => ({
  [SCHEDULE_CONTENT_CONFIG_FORM_FIELD_IDS.SCHEDULE_NAME]: scheduleConfig?.scheduleName,
  [SCHEDULE_CONTENT_CONFIG_FORM_FIELD_IDS.ADD_CUSTOM_MESSAGE]: scheduleConfig?.addCustomMessage,
  [SCHEDULE_CONTENT_CONFIG_FORM_FIELD_IDS.CUSTOM_MESSAGE]: scheduleConfig?.customMessage,
  [SCHEDULE_CONTENT_CONFIG_FORM_FIELD_IDS.SHARE_FORMAT]: scheduleConfig?.shareFormat,
  [SCHEDULE_CONTENT_CONFIG_FORM_FIELD_IDS.REPORT_FORMAT]: scheduleConfig?.reportFormat,
  [SCHEDULE_CONTENT_CONFIG_FORM_FIELD_IDS.START_DATE]: scheduleConfig?.startDate,
  [SCHEDULE_CONTENT_CONFIG_FORM_FIELD_IDS.START_TIME]: getStartDateTime(),
  [SCHEDULE_CONTENT_CONFIG_FORM_FIELD_IDS.FREQUENCY]: scheduleConfig?.frequency,
  [SCHEDULE_CONTENT_CONFIG_FORM_FIELD_IDS.HOURLY_SCHEDULE]: scheduleConfig?.hourlySchedule,
  [SCHEDULE_CONTENT_CONFIG_FORM_FIELD_IDS.DAILY_SCHEDULE]: scheduleConfig?.dailySchedule,
  [SCHEDULE_CONTENT_CONFIG_FORM_FIELD_IDS.WEEKLY_SCHEDULE]: scheduleConfig?.weeklySchedule,
  [SCHEDULE_CONTENT_CONFIG_FORM_FIELD_IDS.MONTHLY_SCHEDULE]: scheduleConfig?.monthlySchedule,
  [SCHEDULE_CONTENT_CONFIG_FORM_FIELD_IDS.ENDS_ON_TYPE]: scheduleConfig?.endsOnType,
  [SCHEDULE_CONTENT_CONFIG_FORM_FIELD_IDS.END_OCCURENCES]: scheduleConfig?.endOccurences,
  [SCHEDULE_CONTENT_CONFIG_FORM_FIELD_IDS.END_DATE]: scheduleConfig?.endDate,
  [SCHEDULE_CONTENT_CONFIG_FORM_FIELD_IDS.USERS]: scheduleConfig?.userIds ?? USER.value,
  [SCHEDULE_CONTENT_CONFIG_FORM_FIELD_IDS.ROLES]: scheduleConfig?.roleIds,
});

export const getModalTitle = contentType =>
  contentType === DASHBOARD.id ? __('Schedule Dashboard') : __('Schedule Report');
