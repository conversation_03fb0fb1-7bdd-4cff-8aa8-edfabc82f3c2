import React from 'react';
import _get from 'lodash/get';

// components
import { TextRenderer } from 'tcomponents/molecules/CellRenderers';
import StatusRenderer from 'tcomponents/molecules/CellRenderers/statusRenderer';

// utils
import { toMoment } from 'tbase/utils/dateUtils';

// constants
import scheduleRunHistoryTableReader from './scheduleRunHistoryModal.reader';
import { COLUMN_IDS } from './scheduleRunHistoryModal.general';

const DELIVERY_STATUS = {
  Header: __('Delivery Status'),
  accessor: scheduleRunHistoryTableReader.deliveryStatus,
  id: COLUMN_IDS.DELIVERY_STATUS,
  key: COLUMN_IDS.DELIVERY_STATUS,
  align: 'left',
  Cell: TextRenderer,
};

const FAILED_REASON = {
  Header: __('Failed Reason'),
  accessor: scheduleRunHistoryTableReader.failedReason,
  id: COLUMN_IDS.FAILED_REASON,
  key: COLUMN_IDS.FAILED_REASON,
  align: 'left',
  Cell: StatusRenderer,
};

const getDateTimeColumn = () => ({
  Header: __('Dates'),
  accessor: scheduleRunHistoryTableReader.dateTime,
  id: COLUMN_IDS.DATE_TIME,
  key: COLUMN_IDS.DATE_TIME,
  align: 'left',
  Cell: props => {
    const dateTime = _get(props, 'original.dateTime');

    if (!dateTime) {
      return <TextRenderer value="--" />;
    }

    const timestamp = typeof dateTime === 'string' ? parseInt(dateTime, 10) : dateTime;

    const momentObj = toMoment(timestamp);

    const formattedDateTime = momentObj.format('MMM DD, YYYY | h:mm A');

    return <TextRenderer value={formattedDateTime} />;
  },
});

const RUN_HISTORY_COLUMNS = [getDateTimeColumn(), DELIVERY_STATUS, FAILED_REASON];

export default RUN_HISTORY_COLUMNS;
