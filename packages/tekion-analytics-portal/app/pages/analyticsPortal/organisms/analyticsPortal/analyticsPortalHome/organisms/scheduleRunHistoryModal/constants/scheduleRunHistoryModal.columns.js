import React from 'react';
import _get from 'lodash/get';

// components
import { TextRenderer } from 'tcomponents/molecules/CellRenderers';

// constants
import scheduleRunHistoryTableReader from './scheduleRunHistoryModal.reader';
import { COLUMN_IDS } from './scheduleRunHistoryModal.general';

const DELIVERY_STATUS = {
  Header: __('Delivery Status'),
  accessor: scheduleRunHistoryTableReader.deliveryStatus,
  id: COLUMN_IDS.DELIVERY_STATUS,
  key: COLUMN_IDS.DELIVERY_STATUS,
  align: 'left',
  Cell: TextRenderer,
};

const FAILED_REASON = {
  Header: __('Failed Reason'),
  accessor: scheduleRunHistoryTableReader.failedReason,
  id: COLUMN_IDS.FAILED_REASON,
  key: COLUMN_IDS.FAILED_REASON,
  align: 'left',
  Cell: TextRenderer,
};

const getDateTimeColumn = () => ({
  Header: __('Dates'),
  accessor: scheduleRunHistoryTableReader.date,
  id: COLUMN_IDS.DATE_TIME,
  key: COLUMN_IDS.DATE_TIME,
  align: 'left',
  Cell: props => {
    console.log(props, ' --- props');
    const date = _get(props, 'original.dateTime');
    return <TextRenderer value={date} />;
  },
});

const RUN_HISTORY_COLUMNS = [getDateTimeColumn(), DELIVERY_STATUS, FAILED_REASON];

export default RUN_HISTORY_COLUMNS;
