import standardFieldOptionMapper from 'tbase/utils/optionMappers/standardFieldMapper';

export const NEVER = {
  name: __('Never'),
  id: 'NEVER',
};

export const SPECIFIC_DATE = {
  name: __('Specific Date'),
  id: 'SPECIFIC_DATE',
};

export const OCCURENCES = {
  name: __('Occurences'),
  id: 'OCCURENCES',
};

const ENDS_ON_TYPES = [NEVER, SPECIFIC_DATE, OCCURENCES];

const ENDS_ON_TYPE_OPTIONS = standardFieldOptionMapper(undefined, ENDS_ON_TYPES);

export default ENDS_ON_TYPE_OPTIONS;
