import baseContentsListReader from 'pages/analyticsPortal/organisms/analyticsPortal/analyticsPortalHome/readers/BaseContentsList';

// Services
import { fetchDealerInfo } from 'tbase/services/dealerService';
import { createContentSchedule } from 'pages/analyticsPortal/services/analyticsPortal.home';

// Reader
import dealerInfoReader from 'tbase/readers/DealerInfo';

import getDataFromResponse from 'tbase/utils/getDataFromResponse';
import { toaster, TOASTER_TYPE } from 'tcomponents/organisms/NotificationWrapper';
import { getErrorMessage } from 'tbase/utils/errorUtils';
import { getScheduleConfigsDTO } from './scheduleContentModal.request';

// constants
import PARENT_ACTION_TYPES from '../../baseContentsList/constants/baseContentsList.actionTypes';
import ACTION_TYPES from '../constants/scheduleContentModal.actionTypes';

const handleCreateNewSchedule = async ({ getState, setState, params }) => {
  const { clickedContentItem, dealerTimeZone, onParentAction } = getState();
  const contentId = baseContentsListReader.id(clickedContentItem);
  const contentType = baseContentsListReader.contentType(clickedContentItem);
  const { scheduleConfigFormValues } = params;

  const scheduleConfigDTO = getScheduleConfigsDTO({
    clickedContentItem,
    scheduleConfigFormValues,
    timeZone: dealerTimeZone,
  });

  try {
    setState({ loading: true });
    const response = await createContentSchedule(contentId, scheduleConfigDTO);
    const data = await getDataFromResponse(response);
    toaster(TOASTER_TYPE.SUCCESS, __('{{-contentType}} scheduled successfully.', { contentType }));
    onParentAction({ type: PARENT_ACTION_TYPES.CANCEL_REPORT_SCHEDULER_MODAL });
  } catch (error) {
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(error, __('Error in Creating Schedule.')));
  } finally {
    setState({ loading: false });
  }
};

const handleGetDealerTimezone = async ({ setState }) => {
  try {
    const response = await fetchDealerInfo();
    const dealerInfo = await getDataFromResponse(response);
    const dealerTimeZone = dealerInfoReader.timeZone(dealerInfo);
    setState({ dealerTimeZone });
  } catch (error) {
    toaster('error', __('Failed to fetch dealer information'));
  }
};

const ACTION_HANDLERS = {
  [ACTION_TYPES.CREATE_NEW_SCHEDULE]: handleCreateNewSchedule,
  [ACTION_TYPES.GET_DEALER_TIMEZONE]: handleGetDealerTimezone,
};

export default ACTION_HANDLERS;
