import React from 'react';
import PropTypes from 'prop-types';

const ScheduleRunHistoryHeader = props => {
  const { dateTime } = props;
  return (
    <div>
      <span>Schedule: </span>
      <IconWithText
        icon="icon-caution2"
        textContent={dateTime}
        // className={complianceIconClassName}
        iconClassName="mr-0"
      />
    </div>
  );
};

ScheduleRunHistoryHeader.propTypes = {
  dateTime: PropTypes.string,
};

ScheduleRunHistoryHeader.defaultProps = {
  dateTime: '',
};

export default ScheduleRunHistoryHeader;
