import React from 'react';
import PropTypes from 'prop-types';

// components
import IconWithText from 'tcomponents/atoms/IconWithText';

const ScheduleRunHistoryHeader = props => {
  const { time } = props;

  return (
    <div>
      <span>Schedule: </span>
      <IconWithText
        icon="icon-calendar"
        textContent={time}
        // className={complianceIconClassName}
        iconClassName="mr-0"
      />
      <IconWithText
        icon="icon-clock"
        textContent={time}
        // className={complianceIconClassName}
        iconClassName="mr-0"
      />
    </div>
  );
};

ScheduleRunHistoryHeader.propTypes = {
  dateTime: PropTypes.string,
};

ScheduleRunHistoryHeader.defaultProps = {
  dateTime: '',
};

export default ScheduleRunHistoryHeader;
