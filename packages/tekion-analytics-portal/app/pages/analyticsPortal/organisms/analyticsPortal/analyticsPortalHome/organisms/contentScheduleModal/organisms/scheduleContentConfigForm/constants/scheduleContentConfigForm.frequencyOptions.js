// Lodash
import _keyBy from 'lodash/keyBy';

// Utils
import standardFieldOptionMapper from 'tbase/utils/optionMappers/standardFieldMapper';

// Constants
import { SCHEDULE_CONFIG_FREQUENCY_KEYS } from 'pages/analyticsPortal/organisms/analyticsPortal/readers/ReportSchedulerConfig';

export const HOURLY = {
  name: __('Hourly'),
  id: 'HOURLY',
  key: SCHEDULE_CONFIG_FREQUENCY_KEYS.HOURLY,
};

export const DAILY = {
  name: __('Daily'),
  id: 'DAILY',
  key: SCHEDULE_CONFIG_FREQUENCY_KEYS.DAILY,
};

export const WEEKLY = {
  name: __('Weekly'),
  id: 'WEEKLY',
  key: SCHEDULE_CONFIG_FREQUENCY_KEYS.WEEKLY,
};

export const MONTHLY = {
  name: __('Monthly'),
  id: 'MONTHLY',
  key: SCHEDULE_CONFIG_FREQUENCY_KEYS.MONTHLY,
};

const FREQUENCY_TYPES = [HOURLY, DAILY, WEEKLY, MONTHLY];

const FREQUENCY_OPTIONS = standardFieldOptionMapper(undefined, FREQUENCY_TYPES);

export const FREQUENCY_TYPES_BY_ID = _keyBy(FREQUENCY_TYPES, 'id');

export default FREQUENCY_OPTIONS;
