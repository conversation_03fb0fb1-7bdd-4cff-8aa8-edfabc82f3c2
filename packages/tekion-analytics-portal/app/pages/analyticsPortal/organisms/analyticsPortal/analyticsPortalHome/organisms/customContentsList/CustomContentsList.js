import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import { compose } from 'recompose';

// Containers
import withActions from 'tcomponents/connectors/withActions';
import withSize from 'tcomponents/hoc/withSize';

// Components
import BaseContentsList from '../baseContentsList';

// Constants
import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import CUSTOM_CONTENTS_COLUMNS from './constants/customContentsList.columns';
import { TOGGLE_BUTTON_SECTION_HEIGHT } from '../../constants/analyticsPortalHome.general';
import { CUSTOM_CONTENT_LIST_INITIAL_STATE } from './constants/customContentsList.general';
import { TABLE_MANAGER_PROPS } from '../baseContentsList/constants/baseContentsList.general';

// Helpers
import ACTION_HANDLERS from './helpers/customContentsList.actionHandlers';
import { getFilterProps, getSubHeaderProps } from './helpers/customContentsList.general';
import { getScaleAdjustedSize } from 'tcomponents/utils/general';

function CustomContentsList(props) {
  const {
    onAction,
    selectedFilters,
    contentHeight,
    sortDetails,
    searchText,
    isFavToggleButtonSelected,
    currentPage,
    isLoading,
    contentsList,
    totalContentsCount,
    isDeleteModalVisible,
    clickedContentItem,
    isDeleteInProgress,
    isShareModalVisible,
    isReportSchedulerModalVisible,
    onParentAction,
    navigate,
  } = props;
  const filterProps = useMemo(() => getFilterProps(selectedFilters), [selectedFilters]);

  const subHeaderProps = useMemo(
    () => getSubHeaderProps({ isFavToggleButtonSelected, onAction, onParentAction, navigate }),
    [isFavToggleButtonSelected, onAction, onParentAction, navigate]
  );

  return (
    <div style={{ height: contentHeight - getScaleAdjustedSize(TOGGLE_BUTTON_SECTION_HEIGHT) }}>
      <BaseContentsList
        columns={CUSTOM_CONTENTS_COLUMNS}
        onAction={onAction}
        tableManagerProps={TABLE_MANAGER_PROPS}
        showActions
        filterProps={filterProps}
        contentList={contentsList}
        totalCount={totalContentsCount}
        subHeaderProps={subHeaderProps}
        sortDetails={sortDetails}
        searchText={searchText}
        currentPage={currentPage}
        loading={isLoading}
        isDeleteModalVisible={isDeleteModalVisible}
        clickedContentItem={clickedContentItem}
        isDeleteInProgress={isDeleteInProgress}
        isShareModalVisible={isShareModalVisible}
        isReportSchedulerModalVisible={isReportSchedulerModalVisible}
      />
    </div>
  );
}

CustomContentsList.propTypes = {
  onAction: PropTypes.func.isRequired,
  contentHeight: PropTypes.number.isRequired,
  sortDetails: PropTypes.object,
  searchText: PropTypes.string,
  isFavToggleButtonSelected: PropTypes.bool,
  currentPage: PropTypes.number,
  isLoading: PropTypes.bool,
  contentsList: PropTypes.array,
  totalContentsCount: PropTypes.number,
  isDeleteModalVisible: PropTypes.bool,
  clickedContentItem: PropTypes.object,
  isDeleteInProgress: PropTypes.bool,
  isShareModalVisible: PropTypes.bool,
  isReportSchedulerModalVisible: PropTypes.bool,
  onParentAction: PropTypes.func.isRequired,
  navigate: PropTypes.func.isRequired,
};

CustomContentsList.defaultProps = {
  sortDetails: EMPTY_OBJECT,
  searchText: undefined,
  isFavToggleButtonSelected: false,
  currentPage: 0,
  isLoading: false,
  contentsList: EMPTY_ARRAY,
  totalContentsCount: 0,
  isDeleteModalVisible: false,
  clickedContentItem: EMPTY_OBJECT,
  isDeleteInProgress: false,
  isShareModalVisible: false,
  isReportSchedulerModalVisible: false,
};

export default compose(
  withActions(CUSTOM_CONTENT_LIST_INITIAL_STATE, ACTION_HANDLERS),
  withSize({ hasPageHeader: 1, hasPageFooter: 1 })
)(CustomContentsList);
