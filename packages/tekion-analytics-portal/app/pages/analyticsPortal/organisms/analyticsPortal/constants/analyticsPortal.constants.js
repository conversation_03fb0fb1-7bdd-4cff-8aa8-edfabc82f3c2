import { Action } from '@thoughtspot/visual-embed-sdk/lib/src/react';
import { PERMISSIONS } from 'tbase/constants/permissions';

export const HIDDEN_ACTIONS = [
  Action.CopyLink,
  Action.TML,
  Action.ExportTML,
  Action.EditTML,
  Action.ImportTML,
  Action.ExportTML,
  Action.RequestVerification,
  Action.SyncToOtherApps,
  Action.SyncToSheets,
  Action.SyncToSlack,
  Action.SyncToTeams,
  // Action.QueryDetailsButtons,
  Action.Share,
  Action.RequestAccess,
  Action.LiveboardUsers,
  Action.SpotIQAnalyze,
  Action.AIHighlights,
  Action.AskAi,
  Action.CreateMonitor,
  Action.AddToFavorites,
];

export const ACTIONS_VS_PERMISSIONS_MAP = {
  [Action.CreateMonitor]: PERMISSIONS.ANALYTICS_PORTAL.ANALYTICS_PORTAL.CREATE_AND_MANAGE_ALERTS,
  [Action.Remove]: PERMISSIONS.ANALYTICS_PORTAL.ANALYTICS_PORTAL.DELETE_DASHBOARD,
  [Action.AnswerDelete]: PERMISSIONS.ANALYTICS_PORTAL.ANALYTICS_PORTAL.DELETE_REPORT,
  [Action.Download]: PERMISSIONS.ANALYTICS_PORTAL.ANALYTICS_PORTAL.EXPORT_DATA,
  [Action.DownloadAsPng]: PERMISSIONS.ANALYTICS_PORTAL.ANALYTICS_PORTAL.EXPORT_DATA,
  [Action.DownloadAsPdf]: PERMISSIONS.ANALYTICS_PORTAL.ANALYTICS_PORTAL.EXPORT_DATA,
  [Action.DownloadAsCsv]: PERMISSIONS.ANALYTICS_PORTAL.ANALYTICS_PORTAL.EXPORT_DATA,
  [Action.DownloadAsXlsx]: PERMISSIONS.ANALYTICS_PORTAL.ANALYTICS_PORTAL.EXPORT_DATA,
  [Action.Schedule]: PERMISSIONS.ANALYTICS_PORTAL.ANALYTICS_PORTAL.ENABLE_SCHEDULE,
  [Action.SchedulesList]: PERMISSIONS.ANALYTICS_PORTAL.ANALYTICS_PORTAL.ENABLE_SCHEDULE,
  [Action.EditScheduleHomepage]: PERMISSIONS.ANALYTICS_PORTAL.ANALYTICS_PORTAL.ENABLE_SCHEDULE,
  [Action.PauseScheduleHomepage]: PERMISSIONS.ANALYTICS_PORTAL.ANALYTICS_PORTAL.ENABLE_SCHEDULE,
  [Action.ViewScheduleRunHomepage]: PERMISSIONS.ANALYTICS_PORTAL.ANALYTICS_PORTAL.ENABLE_SCHEDULE,
  [Action.UnsubscribeScheduleHomepage]: PERMISSIONS.ANALYTICS_PORTAL.ANALYTICS_PORTAL.ENABLE_SCHEDULE,
  [Action.DeleteScheduleHomepage]: PERMISSIONS.ANALYTICS_PORTAL.ANALYTICS_PORTAL.ENABLE_SCHEDULE,
  [Action.Share]: PERMISSIONS.ANALYTICS_PORTAL.ANALYTICS_PORTAL.ENABLE_SHARE,
  [Action.ShareViz]: PERMISSIONS.ANALYTICS_PORTAL.ANALYTICS_PORTAL.ENABLE_SHARE,
};

export const COPY_DASHBOARD_ACTION_VS_PERMISSIONS_MAP = {
  [Action.MakeACopy]: PERMISSIONS.ANALYTICS_PORTAL.ANALYTICS_PORTAL.COPY_DASHBOARD,
  [Action.EditACopy]: PERMISSIONS.ANALYTICS_PORTAL.ANALYTICS_PORTAL.COPY_DASHBOARD,
  [Action.CopyAndEdit]: PERMISSIONS.ANALYTICS_PORTAL.ANALYTICS_PORTAL.COPY_DASHBOARD,
};

export const COPY_REPORT_ACTION_VS_PERMISSIONS_MAP = {
  [Action.MakeACopy]: PERMISSIONS.ANALYTICS_PORTAL.ANALYTICS_PORTAL.COPY_REPORT,
  [Action.EditACopy]: PERMISSIONS.ANALYTICS_PORTAL.ANALYTICS_PORTAL.COPY_REPORT,
  [Action.CopyAndEdit]: PERMISSIONS.ANALYTICS_PORTAL.ANALYTICS_PORTAL.COPY_REPORT,
};

export const NOTES_INFO_REGEX = /id=['"]([^'"]*)['"]/;

export const CHART_TYPE = {
  TABLE_MODE: 'TABLE_MODE',
};

export const ANNOTATION_TYPE = {
  NOTES: 'NOTES',
};

export const TS_ERROR_TYPE = {
  API: 'API',
};

export const TS_EVENT_TYPES = {
  ANSWER: 'ANSWER',
  LIVEBOARD: 'LIVEBOARD',
};

export const THOUGHTSPOT_EVENT_TYPE_MAP = {
  save: TS_EVENT_TYPES.ANSWER,
  createLiveboard: TS_EVENT_TYPES.LIVEBOARD,
  makeACopy: TS_EVENT_TYPES.LIVEBOARD,
};
