import _property from 'lodash/property';

export const SCHEDULE_CONFIG_FREQUENCY_KEYS = {
  HOURLY: 'hourly',
  DAILY: 'daily',
  WEEKLY: 'weekly',
  MONTHLY: 'monthly',
  QUARTERLY: 'quarterly',
  YEARLY: 'yearly',
};

export const SCHEDULE_CONFIG_KEYS = {
  status: 'status',
};

// ToDo: Needs to remove some properties.Will do once testing in done.
const id = _property('id');
const startTime = _property('startTime');
const endTime = _property('endTime');
const repeatConfig = _property('repeatConfig');
const repeatConfigType = _property('repeatConfig.type');
const repeatConfigFrequency = _property('repeatConfig.frequency');
const type = _property('type');
const reportFormatType = _property('reportFormatType');
const userGroups = _property('userGroups');
const emails = _property('emails');
const printers = _property('printers');
const createdBy = _property('createdBy');
const status = _property(SCHEDULE_CONFIG_KEYS.status);
const prevFireTime = _property('prevFireTime');
const userIds = _property('userIds');
const channel = _property('type');
const reportFormat = _property('reportFormatType');
const frequency = _property('frequency');
const startDateTime = _property('startDateTime');
const once = _property(SCHEDULE_CONFIG_FREQUENCY_KEYS.ONCE);
const daily = _property(SCHEDULE_CONFIG_FREQUENCY_KEYS.DAILY);
const weekly = _property(SCHEDULE_CONFIG_FREQUENCY_KEYS.WEEKLY);
const monthly = _property(SCHEDULE_CONFIG_FREQUENCY_KEYS.MONTHLY);
const quarterly = _property(SCHEDULE_CONFIG_FREQUENCY_KEYS.QUARTERLY);
const yearly = _property(SCHEDULE_CONFIG_FREQUENCY_KEYS.YEARLY);
const endConfig = _property('endConfig');
const shareFormat = _property('shareFormats');
const reportType = _property('reportType');
const isSchedulePasscodeValid = _property('schedulePasscodeValid');
const isSensitiveFieldEnabled = _property('sensitiveFieldEnabled');

const SchedulingConfigReader = {
  id,
  startTime,
  endTime,
  repeatConfig,
  repeatConfigType,
  repeatConfigFrequency,
  type,
  reportFormatType,
  userGroups,
  emails,
  status,
  printers,
  createdBy,
  prevFireTime,
  userIds,
  channel,
  reportFormat,
  frequency,
  startDateTime,
  once,
  daily,
  weekly,
  monthly,
  quarterly,
  yearly,
  endConfig,
  shareFormat,
  reportType,
  isSchedulePasscodeValid,
  isSensitiveFieldEnabled,
};

export default SchedulingConfigReader;
