import React, { useCallback, useContext, useMemo, useState } from 'react';
import { SearchEmbed, useEmbedRef, Page as TsPage } from '@thoughtspot/visual-embed-sdk/lib/src/react';
import PropTypes from 'prop-types';
import { useParams } from 'react-router-dom';
import { compose } from 'recompose';
// Lodash
import _noop from 'lodash/noop';

// Containers
import withActions from 'tcomponents/connectors/withActions';
import withAnalyticsPortalEnabled from 'pages/analyticsPortal/containers/withAnalyticsPortalEnabled';

// Context
import { PermissionContext } from 'tcomponents/widgets/permissionsHelper';

// Components
import Page from 'tcomponents/molecules/pageComponent';
import NotesModalContainer from '../../../molecules/notesModalContainer';
import ErrorScreen from '../errorScreen';

// Helpers
import ACTION_HANDLERS from './helpers/report.actionHandlers';
import {
  extractNoteContext,
  getHiddenActions,
  isNoteCell,
  checkShouldShowError,
} from '../helpers/analyticsPortal.general';

// constants
import { THOUGHTSPOT_EVENT_TYPE_MAP } from '../constants/analyticsPortal.constants';
import { INITIAL_STATE } from './constants/report.general';
import ACTION_TYPES from './constants/report.actionTypes';
import { EMPTY_OBJECT } from 'tbase/app.constants';
import { REPORT } from '../analyticsPortalHome/constants/analyticsPortalHome.contentTypes';

import styles from './report.module.scss';

function Report(props) {
  const { onAction, isNotesModalVisible, notesDetails } = props;
  const { noteType, assetNumber, assetId } = notesDetails ?? {};
  const [showError, setShowError] = useState(false);
  const { reportId } = useParams();

  const embedRef = useEmbedRef();
  const permissions = useContext(PermissionContext);
  const hiddenActions = useMemo(() => getHiddenActions(permissions, REPORT.id), [permissions]);

  const handleVizPointClick = useCallback(
    event => {
      const { displayMode, noteValue, noteMetaData } = extractNoteContext({ event });
      const isNoteCellClicked = isNoteCell({ displayMode, noteValue, noteMetaData });

      if (isNoteCellClicked) {
        onAction({
          type: ACTION_TYPES.OPEN_NOTES_MODAL,
          payload: {
            event,
          },
        });
      }
    },
    [onAction]
  );

  const handleNotesModelClose = useCallback(() => {
    onAction({
      type: ACTION_TYPES.CLOSE_NOTES_MODAL,
    });
  }, [onAction]);

  const handleError = useCallback(err => {
    if (checkShouldShowError(err)) {
      setShowError(true);
    }
  }, []);

  const handleAnswerSave = useCallback(
    event => {
      const { type, data } = event;

      onAction({
        type: ACTION_TYPES.EMBED_EVENT_PERSIST,
        payload: {
          type: THOUGHTSPOT_EVENT_TYPE_MAP[type],
          identifier: data?.answerId,
        },
      });
    },
    [onAction]
  );

  const handleCreateLiveboard = useCallback(
    event => {
      const { type, data } = event;
      onAction({
        type: ACTION_TYPES.EMBED_EVENT_PERSIST,
        payload: {
          type: THOUGHTSPOT_EVENT_TYPE_MAP[type] ?? type,
          identifier: data?.id,
        },
      });
    },
    [onAction]
  );

  return (
    <Page>
      <Page.Body>
        {isNotesModalVisible && (
          <NotesModalContainer assetId={assetId} assetType={noteType} handleClose={handleNotesModelClose} />
        )}
        {showError ? (
          <ErrorScreen />
        ) : (
          <SearchEmbed
            className={styles.embedContent}
            ref={embedRef}
            pageId={TsPage.Answers}
            answerId={reportId}
            hiddenActions={hiddenActions}
            disabledActionReason={__('Not Allowed')}
            showPrimaryNavbar={false}
            onVizPointClick={handleVizPointClick}
            locale="en-US"
            dataPanelV2
            onError={handleError}
            onSave={handleAnswerSave}
            onCreateLiveboard={handleCreateLiveboard}
            hideOrgSwitcher
          />
        )}
      </Page.Body>
    </Page>
  );
}

Report.propTypes = {
  onAction: PropTypes.func,
  isNotesModalVisible: PropTypes.bool,
  notesDetails: PropTypes.object,
};

Report.defaultProps = {
  onAction: _noop,
  isNotesModalVisible: false,
  notesDetails: EMPTY_OBJECT,
};

export default compose(withAnalyticsPortalEnabled, withActions(INITIAL_STATE, ACTION_HANDLERS))(Report);
