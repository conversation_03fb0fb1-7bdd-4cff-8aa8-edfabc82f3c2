/* eslint-disable import/order */
import React, { useCallback, useContext, useMemo, useState } from 'react';
import { LiveboardEmbed, useEmbedRef } from '@thoughtspot/visual-embed-sdk/lib/src/react';
import PropTypes from 'prop-types';
import { useParams } from 'react-router-dom';
import { compose } from 'recompose';
import ErrorScreen from '../errorScreen';

// Lodash
import _noop from 'lodash/noop';

// Containers
import withActions from 'tcomponents/connectors/withActions';
import withAnalyticsPortalEnabled from 'pages/analyticsPortal/containers/withAnalyticsPortalEnabled';

// Contexts
import { PermissionContext } from 'tcomponents/widgets/permissionsHelper';

// Components
import Page from 'tcomponents/molecules/pageComponent';
import NotesModalContainer from '../../../molecules/notesModalContainer';

// Helpers
import ACTION_HANDLERS from './helpers/liveboard.actionHandlers';
import {
  extractNoteContext,
  isNoteCell,
  getHiddenActions,
  checkShouldShowError,
} from '../helpers/analyticsPortal.general';

// constants
import { INITIAL_STATE } from './constants/liveboard.general';
import ACTION_TYPES from './constants/liveboard.actionTypes';
import { EMPTY_OBJECT } from 'tbase/app.constants';

import styles from './liveboard.module.scss';
import { TS_EVENT_TYPES } from '../constants/analyticsPortal.constants';

function Liveboard(props) {
  const [showError, setShowError] = useState(false);
  const { onAction, isNotesModalVisible, notesDetails } = props;
  const { liveboardId } = useParams();
  const { noteType, assetId } = notesDetails || {};

  const embedRef = useEmbedRef();

  const permissions = useContext(PermissionContext);
  const hiddenActions = useMemo(() => getHiddenActions(permissions), [permissions]);

  const handleVizPointClick = useCallback(
    event => {
      const { displayMode, noteValue, noteMetaData } = extractNoteContext({ event });
      const isNoteCellClicked = isNoteCell({ displayMode, noteValue, noteMetaData });

      if (isNoteCellClicked) {
        onAction({
          type: ACTION_TYPES.OPEN_NOTES_MODAL,
          payload: {
            event,
          },
        });
      }
    },
    [onAction]
  );

  const handleNotesModelClose = useCallback(() => {
    onAction({
      type: ACTION_TYPES.CLOSE_NOTES_MODAL,
    });
  }, [onAction]);

  const handleSave = useCallback(
    event => {
      const { data } = event;

      onAction({
        type: ACTION_TYPES.EMBED_EVENT_PERSIST,
        payload: {
          type: TS_EVENT_TYPES.LIVEBOARD,
          identifier: data?.liveboardId,
        },
      });
    },
    [onAction]
  );

  const handleCreateLiveboard = useCallback(
    event => {
      const { data } = event;

      onAction({
        type: ACTION_TYPES.EMBED_EVENT_PERSIST,
        payload: {
          type: TS_EVENT_TYPES.LIVEBOARD,
          identifier: data?.id,
        },
      });
    },
    [onAction]
  );

  const handleMakeACopy = useCallback(
    event => {
      const { data } = event;

      onAction({
        type: ACTION_TYPES.EMBED_EVENT_PERSIST,
        payload: {
          type: TS_EVENT_TYPES.LIVEBOARD,
          identifier: data?.newLiveboardId,
        },
      });
    },
    [onAction]
  );

  const handleError = useCallback(err => {
    if (checkShouldShowError(err)) {
      setShowError(true);
    }
  }, []);

  return (
    <Page>
      <Page.Body>
        {showError ? (
          <ErrorScreen />
        ) : (
          <>
            {isNotesModalVisible && (
              <NotesModalContainer assetId={assetId} assetType={noteType} handleClose={handleNotesModelClose} />
            )}
            <LiveboardEmbed
              className={styles.embedContent}
              ref={embedRef}
              disabledActionReason={__('Not Allowed')}
              onError={handleError}
              fullHeight
              liveboardId={liveboardId}
              showLiveboardTitle
              onVizPointClick={handleVizPointClick}
              locale="en-US"
              hiddenActions={hiddenActions}
              dataPanelV2
              onCreateLiveboard={handleCreateLiveboard}
              onSave={handleSave}
              onMakeACopy={handleMakeACopy}
              disableProfileAndHelp
              hideOrgSwitcher
            />
          </>
        )}
      </Page.Body>
    </Page>
  );
}

Liveboard.propTypes = {
  onAction: PropTypes.func,
  isNotesModalVisible: PropTypes.bool,
  notesDetails: PropTypes.object,
};

Liveboard.defaultProps = {
  onAction: _noop,
  isNotesModalVisible: false,
  notesDetails: EMPTY_OBJECT,
};

export default compose(withAnalyticsPortalEnabled, withActions(INITIAL_STATE, ACTION_HANDLERS))(Liveboard);
