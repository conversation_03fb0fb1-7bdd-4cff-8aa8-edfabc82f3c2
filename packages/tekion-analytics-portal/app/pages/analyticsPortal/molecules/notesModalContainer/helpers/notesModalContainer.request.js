// Lodash
import _isNil from 'lodash/isNil';
import _get from 'lodash/get';
import _head from 'lodash/head';
import _filter from 'lodash/filter';

// Resolvers
import resolver, { ENTITIES } from 'tbase/bulkResolvers';

// Utils
import TEnvReader from 'tbase/readers/Env';

// Services
import { addNoteV2, deleteNoteV2, fetchNoteV2, updateNoteV2 } from 'tbase/services/notesServices';
import { fetchUsersForNotesAction } from './notesModalContainer.actions';

// Actions
import { ACTION_TYPES } from '../constants/notesModalContainer.actionTypes';
import { createNoteObject, updateExistingNotes } from './notesModalContainer.general';
import { toaster, TOASTER_TYPE } from 'tcomponents/organisms/NotificationWrapper';
import { getErrorMessage } from 'tbase/utils/errorUtils';

export const fetchUsersForNotes = async (setUserList, setLoading, notes, userList) => {
  const resolvedUsersById = await fetchUsersForNotesAction(notes);
  setUserList({ ...userList, ...resolvedUsersById });
};

const onFetchNoteV2Success =
  ({ setNotes, setDisabled, setUserList, userList, isDisabled }) =>
  async newNotes => {
    const resolvedNotes = await resolver.getResolvedData(ENTITIES.NOTES, newNotes);
    setNotes(resolvedNotes);
    setDisabled(isDisabled);
    fetchUsersForNotes({ setUserList, notes: resolvedNotes, userList });
  };

const addNote = ({ noteToAdd, notes, setNotes, parentNotesUpdateHandler }) => {
  const updatedNotes = [...notes, noteToAdd];
  setNotes(updatedNotes);
  parentNotesUpdateHandler(updatedNotes, ACTION_TYPES.ADD_NOTE);
};

const updateNote = ({ updateNoteToAdd, notes, setNotes, parentNotesUpdateHandler }) => {
  const { id: noteId } = updateNoteToAdd;
  const updatedNotes = updateExistingNotes(notes, noteId, updateNoteToAdd);
  setNotes(updatedNotes);
  parentNotesUpdateHandler(updatedNotes, ACTION_TYPES.UPDATE_NOTE);
};

const deleteNote = ({ noteId, notes, setNotes, parentNotesUpdateHandler }) => {
  const updatedNotes = _filter(notes, note => note.id !== noteId);
  setNotes(updatedNotes);
  parentNotesUpdateHandler(updatedNotes, ACTION_TYPES.DELETE_NOTE);
};

export const handleGetNotes = async ({
  propagateAction,
  setNotes,
  setDisabled,
  setUserList,
  setLoading,
  assetId,
  assetType,
  userList,
  isDisabled,
}) => {
  if (!propagateAction) {
    if (_isNil(assetId) || _isNil(assetType)) {
      return;
    }

    try {
      setLoading(true);
      const newNotes = await fetchNoteV2({ assetType, assetId });
      await onFetchNoteV2Success({ setNotes, setDisabled, setUserList, userList, isDisabled })(newNotes);
    } catch (error) {
      toaster(TOASTER_TYPE.ERROR, getErrorMessage(error, __('Failed to fetch notes')));
      setNotes([]);
    } finally {
      setLoading(false);
    }
  }
};

export const handleAddNewNote = async ({
  note,
  assetType,
  assetId,
  notes,
  setNotes,
  parentNotesUpdateHandler,
  propagateAction,
}) => {
  const currentUser = TEnvReader.userInfo();
  const userId = _get(currentUser, 'id');
  const notesObj = { text: note, userId };
  if (propagateAction) {
    addNote({ noteToAdd: createNoteObject(notesObj), notes, setNotes, parentNotesUpdateHandler });
    return;
  }
  const noteToAdd = await addNoteV2({ assetType, assetId, text: note });
  const resolvedNoteToAdd = _head(await resolver.getResolvedData(ENTITIES.NOTES, noteToAdd));
  addNote({ noteToAdd: resolvedNoteToAdd, notes, setNotes, parentNotesUpdateHandler });
};

export const handleUpdateNote = async ({
  noteId,
  note,
  assetType,
  assetId,
  notes,
  setNotes,
  parentNotesUpdateHandler,
  propagateAction,
}) => {
  if (propagateAction) {
    updateNote({ noteId, note, notes, setNotes, parentNotesUpdateHandler });
    return;
  }
  const updatedNote = await updateNoteV2({ assetType, assetId, noteId, text: note });
  const resolvedUpdateNoteToAdd = _head(await resolver.getResolvedData(ENTITIES.NOTES, updatedNote));
  updateNote({ updateNoteToAdd: resolvedUpdateNoteToAdd, notes, setNotes, parentNotesUpdateHandler });
};

export const handleDeleteNote = async ({
  noteId,
  assetType,
  assetId,
  notes,
  setNotes,
  parentNotesUpdateHandler,
  propagateAction,
}) => {
  if (propagateAction) {
    deleteNote({ noteId, notes, setNotes, parentNotesUpdateHandler });
    return;
  }
  await deleteNoteV2({ assetType, assetId, noteId });
  deleteNote({ noteId, notes, setNotes, parentNotesUpdateHandler });
};

export const handleNotesModalOpen = ({ assetType, assetId, setNotes, isDisabled, setDisabled, propagateAction }) => {
  if (!propagateAction) {
    if (_isNil(assetId) || _isNil(assetType)) {
      return;
    }
    fetchNoteV2({ assetType, assetId }).then(newNotes => {
      setNotes(newNotes);
      setDisabled(isDisabled);
      fetchUsersForNotes();
    });
  }
};
