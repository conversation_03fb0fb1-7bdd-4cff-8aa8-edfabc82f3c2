import React, { useState, useEffect, useRef, useCallback } from 'react';
import PropTypes from 'prop-types';

import _get from 'lodash/get';
import _noop from 'lodash/noop';
import _isEmpty from 'lodash/isEmpty';

// Utils
import TEnvReader from 'tbase/readers/Env';

// Components
import { getSortedNotes } from 'tcomponents/utils/notes';

// Constants
import SIZES from 'tcomponents/molecules/Modal/constants/modal.sizes';
import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';

// Services
import NotesModal from '../NotesModal/NotesModal';

// Helpers
import {
  fetchUsersForNotes,
  handleAddNewNote,
  handleDeleteNote,
  handleGetNotes,
  handleNotesModalOpen,
  handleUpdateNote,
} from './helpers/notesModalContainer.request';

function NotesModalContainer(props) {
  const {
    isDisabled,
    containerStyle,
    assetId,
    assetType,
    parentNotesUpdateHandler,
    propagateAction,
    handleClose,
    ...rest
  } = props;

  const [notes, setNotes] = useState(EMPTY_ARRAY);
  const [userList, setUserList] = useState(EMPTY_ARRAY);
  const [disabled, setDisabled] = useState(isDisabled);
  const [loading, setLoading] = useState(false);
  const modalRef = useRef(null);

  const getNotes = useCallback(() => {
    handleGetNotes({
      propagateAction,
      setNotes,
      setDisabled,
      setUserList,
      setLoading,
      assetId,
      assetType,
      userList,
      isDisabled,
    });
  }, [propagateAction, setNotes, setDisabled, setUserList, assetId, assetType, userList, isDisabled]);

  useEffect(() => {
    if (_isEmpty(notes)) {
      getNotes();
    }
  }, EMPTY_ARRAY);

  useEffect(() => {
    if (!_isEmpty(notes)) {
      fetchUsersForNotes(setUserList, notes, userList);
    }
  }, [notes]);

  const onAddNewNote = useCallback(
    (_, note) => {
      handleAddNewNote({ note, assetType, assetId, notes, setNotes, parentNotesUpdateHandler, propagateAction });
    },
    [notes, assetType, assetId, parentNotesUpdateHandler, propagateAction]
  );

  const onUpdateNote = useCallback(
    (_, noteId, note) =>
      handleUpdateNote({
        noteId,
        note,
        assetType,
        assetId,
        notes,
        setNotes,
        parentNotesUpdateHandler,
        propagateAction,
      }),
    [assetType, assetId, notes, parentNotesUpdateHandler, propagateAction]
  );

  const onDeleteNote = useCallback(
    (_, noteId) =>
      handleDeleteNote({ noteId, assetType, assetId, notes, setNotes, parentNotesUpdateHandler, propagateAction }),
    [assetType, assetId, notes, parentNotesUpdateHandler, propagateAction]
  );

  const onNotesModalOpen = useCallback(() => {
    handleNotesModalOpen({ assetType, assetId, setNotes, isDisabled, setDisabled, propagateAction });
  }, [assetId, assetType, isDisabled, propagateAction]);

  const handleModalClose = useCallback(() => {
    handleClose();
  }, [handleClose]);

  return (
    <div className={containerStyle}>
      <NotesModal
        ref={modalRef}
        assetId={assetId}
        assetType={assetType}
        notes={notes}
        usersList={userList}
        loading={loading}
        modalWidth={SIZES.L}
        onCancel={_get(modalRef, 'current.hideModal')}
        handleModalClose={handleModalClose}
        handleAddNewNote={onAddNewNote}
        handleUpdateNote={onUpdateNote}
        handleDeleteNote={onDeleteNote}
        onOpen={onNotesModalOpen}
        currentUser={TEnvReader.userInfo()}
        hideSubmit
        {...rest}
        disabled={disabled}
        getSortedNotes={getSortedNotes}
      />
    </div>
  );
}

NotesModalContainer.propTypes = {
  assetType: PropTypes.string.isRequired,
  assetId: PropTypes.string.isRequired,
  isDisabled: PropTypes.bool,
  containerStyle: PropTypes.shape(),
  existingNotes: PropTypes.array,
  propagateAction: PropTypes.bool,
  parentNotesUpdateHandler: PropTypes.func,
  handleClose: PropTypes.func,
  userList: PropTypes.array,
};

NotesModalContainer.defaultProps = {
  isDisabled: false,
  containerStyle: EMPTY_OBJECT,
  existingNotes: EMPTY_ARRAY,
  propagateAction: false,
  parentNotesUpdateHandler: _noop,
  handleClose: _noop,
  userList: EMPTY_ARRAY,
};

export default NotesModalContainer;
