import React, { useMemo } from 'react';
import PropTypes from 'prop-types';

// Lodash
import _noop from 'lodash/noop';

// Constants
import { EMPTY_OBJECT } from 'tbase/app.constants';

// Components
import Heading from 'tcomponents/atoms/Heading';
import IconAsBtn from 'tcomponents/atoms/iconAsBtn';
import FontIcon from 'tcomponents/atoms/FontIcon';

// Helpers
import { getLastUpdatedDateAndTime } from 'twidgets/organisms/genericLogs';

// Styles
import styles from './emailOrPrintLogsHeaderRenderer.module.scss';

function EmailOrPrintLogsHeaderRenderer(props) {
  const { headerLabel, lastUpdatedTime, onRefresh, getFormattedDateAndTime } = props;

  const lastUpdatedDateAndTime = useMemo(
    () => getLastUpdatedDateAndTime(lastUpdatedTime, getFormattedDateAndTime),
    [lastUpdatedTime, getFormattedDateAndTime]
  );
  const { date, time } = lastUpdatedDateAndTime;

  return (
    <Heading size={4} className={`p-t-8 p-b-8 ${styles.header}`}>
      <div className={`p-0 ${styles.leftSectionHeader}`}>
        <FontIcon>icon-history</FontIcon>
        {headerLabel}
      </div>
      <div className={styles.rightSectionHeader}>
        <div className={styles.dateAndTime}>{__('Last Updated: {{date}} • {{time}}', { date, time })}</div>
        <IconAsBtn onClick={onRefresh} className={styles.refreshIcon}>
          icon-refresh2
        </IconAsBtn>
      </div>
    </Heading>
  );
}

EmailOrPrintLogsHeaderRenderer.propTypes = {
  lastUpdatedTime: PropTypes.object,
  onRefresh: PropTypes.func,
  headerLabel: PropTypes.string,
  getFormattedDateAndTime: PropTypes.func,
};

EmailOrPrintLogsHeaderRenderer.defaultProps = {
  lastUpdatedTime: EMPTY_OBJECT,
  onRefresh: _noop,
  headerLabel: __('Logs'),
  getFormattedDateAndTime: _noop,
};

export default React.memo(EmailOrPrintLogsHeaderRenderer);
