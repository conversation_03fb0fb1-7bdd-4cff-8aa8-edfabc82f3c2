@use "@tekion/tekion-styles-next/scss/colors.scss";
@use "@tekion/tekion-styles-next/scss/mixins/layout.scss";
@use "@tekion/tekion-styles-next/scss/mixins/typography.scss";
@use "@tekion/tekion-styles-next/scss/variables.scss";

.header {
  width: 39.4rem;
  height: 4rem;
  @include layout.flex($align-items: center, $justify-content: space-between);
}

.leftSectionHeader {
  @include layout.flex($align-items: flex-start);
  gap: 0.8rem;
}

.rightSectionHeader {
  @include layout.flex();
  gap: 0.8rem;
}

.dateAndTime {
  @include typography.heading5($font-family: variables.$font-regular, $color: colors.$atomic);
  letter-spacing: 0.002rem;
}

.refreshIcon {
  color: colors.$woodsmoke;
}
