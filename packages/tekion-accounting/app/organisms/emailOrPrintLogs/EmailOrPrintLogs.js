/* eslint-disable import/order */
import React from 'react';
import PropTypes from 'prop-types';

// Lodash
import _noop from 'lodash/noop';

// Components
import GenericLogs from 'twidgets/organisms/genericLogs';
import EmailOrPrintLogsRenderer from './molecules/emailOrPrintLogsRenderer';
import EmailOrPrintLogsHeaderRenderer from './molecules/emailOrPrintLogsHeaderRenderer';

// Styles
import styles from './emailOrPrintLogs.module.scss';

// Constants
import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import LOG_OPTIONS from './constants/emailOrPrintLogs.logOptions';
import {
  LOG_OPTION_VS_LOADING_MSG,
  LOG_OPTION_VS_ERROR_MSG,
  LOG_OPTION_VS_EMPTY_LOG_MSG,
} from './constants/emailOrPrintLogs.general';

function EmailOrPrintLogs(props) {
  const { lastUpdatedTime, onRefresh, logOption, isLoading, isError, remount, logs, getFormattedDateAndTime } = props;

  const logConfig = LOG_OPTIONS[logOption] || {};
  const { headerLabel } = logConfig;
  const logIcon = logConfig.icon;
  const loadingMessage = LOG_OPTION_VS_LOADING_MSG[logOption];
  const errorMessage = LOG_OPTION_VS_ERROR_MSG[logOption];
  const emptyMessage = LOG_OPTION_VS_EMPTY_LOG_MSG[logOption];

  return (
    <GenericLogs
      lastUpdatedTime={lastUpdatedTime}
      onRefresh={onRefresh}
      logOption={logOption}
      isLoading={isLoading}
      isError={isError}
      remount={remount}
      logs={logs}
      getFormattedDateAndTime={getFormattedDateAndTime}
      headerLabel={headerLabel}
      logIcon={logIcon}
      loadingMessage={loadingMessage}
      errorMessage={errorMessage}
      emptyMessage={emptyMessage}
      customRightSectionRenderer={EmailOrPrintLogsRenderer}
      customHeaderRenderer={EmailOrPrintLogsHeaderRenderer}
      separatorClass={styles.customSeparator}
    />
  );
}

EmailOrPrintLogs.propTypes = {
  lastUpdatedTime: PropTypes.object,
  onRefresh: PropTypes.func,
  logOption: PropTypes.string,
  isLoading: PropTypes.bool,
  isError: PropTypes.bool,
  remount: PropTypes.func,
  logs: PropTypes.arrayOf(
    PropTypes.shape({
      logMessage: PropTypes.string,
      formattedAmount: PropTypes.string,
      createdByUserName: PropTypes.string,
      formattedDate: PropTypes.string,
      formattedTime: PropTypes.string,
    })
  ),
  getFormattedDateAndTime: PropTypes.func,
};

EmailOrPrintLogs.defaultProps = {
  lastUpdatedTime: EMPTY_OBJECT,
  onRefresh: _noop,
  logOption: undefined,
  isLoading: true,
  isError: false,
  remount: _noop,
  logs: EMPTY_ARRAY,
  getFormattedDateAndTime: _noop,
};

export default EmailOrPrintLogs;
