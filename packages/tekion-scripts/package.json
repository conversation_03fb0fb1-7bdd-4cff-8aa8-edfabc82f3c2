{"name": "@tekion/tekion-scripts", "version": "2.1.0", "main": "index.js", "license": "", "bin": {"tekion-scripts": "./index.js"}, "scripts": {"release": "npm publish --registry https://tknon-registry.tekion.xyz/", "test": "tekion-test --override ./config/jest.override.js"}, "dependencies": {"@tekion/tekion-arc-aec-build-scripts": "1.1.27", "@tekion/tekion-build-essentials": "1.0.11", "@tekion/tekion-build-scripts": "1.2.2", "fs-extra": "^10.0.0", "jscodeshift": "0.15.0", "lodash": "4.17.21", "mime": "3.0.0", "path": "^0.12.7", "react-dev-utils": "^11.0.4", "recursive-readdir": "2.2.2"}}