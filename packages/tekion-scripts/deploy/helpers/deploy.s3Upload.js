/* eslint-disable no-console */
const fs = require('fs');
const path = require('path');
const { Upload } = require('@aws-sdk/lib-storage');
const _map = require('lodash/map');
const mime = require('mime/lite');
const { getMaxCacheAge } = require('../../helpers/environment');

/**
 * Gets file size in bytes
 * @param {string} [filePath] - Path to the file
 * @param {string|Buffer} [data] - Data buffer
 * @returns {number} File size in bytes, 0 if unable to determine
 */
const getFileSize = (filePath, data) => {
  try {
    if (filePath) {
      const stats = fs.statSync(filePath);
      return stats.size;
    }
    if (data) {
      return Buffer.isBuffer(data) ? data.length : Buffer.byteLength(data);
    }
    return 0;
  } catch (error) {
    return 0;
  }
};

/**
 * Determines MIME type for a file based on its extension
 *
 * @param {string} filePath - Path to the file
 * @returns {string} MIME type string (defaults to 'application/octet-stream' if unknown)
 */
const getMimeType = filePath => mime.getType(filePath) || 'application/octet-stream';

/**
 * Generates cache control header for S3 objects
 *
 * Sets aggressive caching with immutable flag for static assets.
 * Default cache age is 3 months (7862400 seconds) for optimal performance.
 *
 * @returns {string} Cache control header string
 */
const getCacheControl = () => {
  const maxCacheAge = getMaxCacheAge() || '7862400'; // Default to 3 months if not specified
  return `public, max-age=${maxCacheAge}, immutable`;
};

/**
 * Orchestrates retry upload process with comprehensive error handling
 *
 * When primary upload fails, this function attempts a fallback upload
 * and provides detailed error reporting including file size information
 * to help diagnose upload issues.
 *
 * @param {Object} options - Fallback upload options
 * @param {Object} options.awsClient - Configured AWS S3 client
 * @param {Object} options.params - S3 upload parameters
 * @param {Error} options.primaryError - Error from the primary upload attempt
 * @param {string} options.contentType - MIME type of the file
 * @param {string} options.fileSizeKB - File size in KB (pre-calculated)
 * @returns {Promise<Object>} Upload result with success status and metadata
 * @throws {Error} If both primary and fallback uploads fail
 */
const retryUpload = async ({ awsClient, params, primaryError, contentType, fileSizeKB }) => {
  const { Key: key } = params;
  console.log(`Attempting retry upload for ${key} (Content-Type: ${contentType}, size: ${fileSizeKB} KB)`);
  try {
    const command = new Upload({
      client: awsClient,
      params,
    });
    const result = await command.done();
    console.log(`✓ Retry upload successful: ${key} (Content-Type: ${contentType}, size: ${fileSizeKB} KB)`);
    return result;
  } catch (error) {
    console.error(`✗ Upload failed after one retry ${key} (Content-Type: ${contentType}, size: ${fileSizeKB} KB)`);
    console.error(`Primary error: ${primaryError.message}`);
    console.error(`Retry error: ${error.message}`);
    throw new Error(error);
  }
};

/**
 * Uploads file or data buffer to AWS S3 with error handling and fallback
 * @param {Object} awsClient - Configured AWS S3 client instance
 * @param {Object} uploadParams
 * @param {string} uploadParams.key - S3 object key where file will be stored
 * @param {string} [uploadParams.file] - Local file path to upload
 * @param {string|Buffer} [uploadParams.data] - Data buffer to upload
 * @param {string} uploadParams.bucket - Target S3 bucket name
 * @param {string} uploadParams.contentType - MIME type of the file
 * @param {string} uploadParams.cacheControl - Cache control header
 * @param {string} [uploadParams.contentEncoding] - Content encoding header
 * @returns {Promise<Object>} Upload result object
 */
const uploadToAws = async (awsClient, { key, file, bucket, contentType, cacheControl, contentEncoding, data }) => {
  if (!file && !data) {
    throw new Error('File path or data is required');
  }

  const fileSize = getFileSize(file, data);

  // Skip upload if file size is 0
  if (fileSize === 0) {
    console.log(`⚠️  Skipping upload for ${key}: File size is 0 KB`);
    return {
      success: false,
      skipped: true,
      reason: 'File size is 0 KB',
      Key: key,
      Location: `s3://${bucket}/${key}`,
    };
  }

  const fileSizeKB = (fileSize / 1024).toFixed(2);
  console.log(`Starting upload to S3: ${key} (Content-Type: ${contentType}, size: ${fileSizeKB} KB)`);

  const body = file ? fs.createReadStream(file) : Buffer.from(data);
  const params = {
    Key: key,
    Body: body,
    Bucket: bucket,
    ContentType: contentType,
    CacheControl: cacheControl,
  };

  if (contentEncoding) {
    params.ContentEncoding = contentEncoding;
  }

  try {
    const upload = new Upload({ client: awsClient, params });
    const result = await upload.done();
    console.log(`✓ Upload successful: ${key} (Content-Type: ${contentType}, size: ${fileSizeKB} KB)`);
    return result;
  } catch (error) {
    console.error(
      `✗ Primary upload failed for ${key} (Content-Type: ${contentType}, size: ${fileSizeKB} KB):`,
      error.message
    );
    console.log(`Retrying upload for ${key} (Content-Type: ${contentType}, size: ${fileSizeKB} KB)`);
    return retryUpload({ awsClient, params, primaryError: error, contentType, fileSizeKB });
  }
};

/**
 * Determines content encoding for compressed files
 * @param {string} filePath - Path to the file
 * @returns {string|undefined} Content encoding or undefined
 */
const getContentEncoding = filePath => {
  const fileName = path.basename(filePath);
  if (fileName.endsWith('.js.br') || fileName.endsWith('.css.br')) {
    return 'br';
  }
  return undefined;
};

/**
 * Creates configured upload function for individual files
 * @param {Object} config
 * @param {string} config.directoryPath - Base directory path containing files
 * @param {Object} config.awsClient - Configured AWS S3 client instance
 * @param {string} config.awsBucket - Target S3 bucket name
 * @param {Object} config.appConfig - Application configuration object
 * @param {Function} config.generateAwsKey - Function to generate S3 keys
 * @returns {Function} Configured upload function that accepts a file path
 */
const uploadSingleFile =
  ({ directoryPath, awsClient, awsBucket, appConfig, generateAwsKey }) =>
  async filePath => {
    const awsKey = generateAwsKey(directoryPath, filePath, appConfig);
    const contentType = getMimeType(filePath);
    const contentEncoding = getContentEncoding(filePath);
    const cacheControl = getCacheControl();

    const uploadParams = {
      key: awsKey,
      file: filePath,
      bucket: awsBucket,
      contentType,
      cacheControl,
    };

    if (contentEncoding) {
      uploadParams.contentEncoding = contentEncoding;
    }

    try {
      const result = await uploadToAws(awsClient, uploadParams);
      return { success: true, file: awsKey, result };
    } catch (error) {
      console.error(`✗ Failed to upload ${filePath}:`, error.message);
      return { success: false, file: awsKey, error: error.message };
    }
  };

/**
 * Creates array of upload promises for concurrent execution
 * @param {Object} config
 * @param {string[]} config.files - Array of file paths to upload
 * @param {string} config.directoryPath - Base directory path containing files
 * @param {Object} config.awsClient - Configured AWS S3 client instance
 * @param {string} config.awsBucket - Target S3 bucket name
 * @param {Object} config.appConfig - Application configuration object
 * @param {Function} config.generateAwsKey - Function to generate S3 keys
 * @returns {Promise[]} Array of upload promises
 */
const uploadAllFiles = ({ files, directoryPath, awsClient, awsBucket, appConfig, generateAwsKey }) =>
  _map(
    files,
    uploadSingleFile({
      directoryPath,
      awsClient,
      awsBucket,
      appConfig,
      generateAwsKey,
    })
  );

// Export the utility functions
module.exports = {
  uploadToAws,
  uploadAllFiles,
};
