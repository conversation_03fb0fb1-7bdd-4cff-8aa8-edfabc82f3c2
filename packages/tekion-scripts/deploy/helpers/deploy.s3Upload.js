/* eslint-disable no-console */
const fs = require('fs');
const path = require('path');
const { Upload } = require('@aws-sdk/lib-storage');
const _map = require('lodash/map');
const mime = require('mime/lite');
const { getMaxCacheAge } = require('../../helpers/environment');

/**
 * Determines MIME type for a file based on its extension
 *
 * @param {string} filePath - Path to the file
 * @returns {string} MIME type string (defaults to 'application/octet-stream' if unknown)
 */
const getMimeType = filePath => mime.getType(filePath) || 'application/octet-stream';

/**
 * Generates cache control header for S3 objects
 *
 * Sets aggressive caching with immutable flag for static assets.
 * Default cache age is 3 months (7862400 seconds) for optimal performance.
 *
 * @returns {string} Cache control header string
 */
const getCacheControl = () => {
  const maxCacheAge = getMaxCacheAge() || '7862400'; // Default to 3 months if not specified
  return `public, max-age=${maxCacheAge}, immutable`;
};

/**
 * Orchestrates retry upload process with comprehensive error handling
 *
 * When primary upload fails, this function attempts a fallback upload
 * and provides detailed error reporting including file size information
 * to help diagnose upload issues.
 *
 * @param {Object} options - Fallback upload options
 * @param {Object} options.awsClient - Configured AWS S3 client
 * @param {Object} options.params - S3 upload parameters
 * @param {Error} options.primaryError - Error from the primary upload attempt
 * @param {string} options.file - Path to the file being uploaded
 * @returns {Promise<Object>} Upload result with success status and metadata
 * @throws {Error} If both primary and fallback uploads fail
 */
const retryUpload = async ({ awsClient, params, primaryError, file }) => {
  const { Key: key } = params;
  console.log(`Attempting retry upload for ${key}`);
  try {
    const command = new Upload({
      awsClient,
      params,
    });
    const result = await command.done();
    return result;
  } catch (error) {
    // Gather file size information for debugging
    let fileSize = 0;
    try {
      const stats = await fs.promises.stat(file);
      fileSize = stats.size;
    } catch {
      // Ignore stat errors - file might not exist or be accessible
    }
    console.error(`✗ Upload failed after one retry ${key} (size: ${(fileSize / 1024).toFixed(2)} KB)`);
    console.error(`Primary error: ${primaryError.message}`);
    console.error(`Retry error: ${error.message}`);
    throw new Error(error);
  }
};

/**
 * Uploads file or data buffer to AWS S3 with error handling and fallback
 * @param {Object} awsClient - Configured AWS S3 client instance
 * @param {Object} uploadParams
 * @param {string} uploadParams.key - S3 object key where file will be stored
 * @param {string} [uploadParams.file] - Local file path to upload
 * @param {string|Buffer} [uploadParams.data] - Data buffer to upload
 * @param {string} uploadParams.bucket - Target S3 bucket name
 * @param {string} uploadParams.contentType - MIME type of the file
 * @param {string} uploadParams.cacheControl - Cache control header
 * @param {string} [uploadParams.contentEncoding] - Content encoding header
 * @returns {Promise<Object>} Upload result object
 */
const uploadToAws = async (awsClient, { key, file, bucket, contentType, cacheControl, contentEncoding, data }) => {
  if (!file && !data) {
    throw new Error('File path or data is required');
  }

  console.log(`Starting upload to S3: ${key}`);

  const body = file ? fs.createReadStream(file) : Buffer.from(data);
  const params = {
    Key: key,
    Body: body,
    Bucket: bucket,
    ContentType: contentType,
    CacheControl: cacheControl,
  };

  if (contentEncoding) {
    params.ContentEncoding = contentEncoding;
  }

  try {
    const upload = new Upload({ client: awsClient, params });
    const result = await upload.done();
    console.log(`✓ Upload successful: ${key}`);
    return result;
  } catch (error) {
    console.error(`✗ Primary upload failed for ${key}:`, error.message);
    console.log(`Retrying upload for ${key}`);
    return retryUpload({ awsClient, params, file, primaryError: error });
  }
};

/**
 * Determines content encoding for compressed files
 * @param {string} filePath - Path to the file
 * @returns {string|undefined} Content encoding or undefined
 */
const getContentEncoding = filePath => {
  const fileName = path.basename(filePath);
  if (fileName.endsWith('.js.br') || fileName.endsWith('.css.br')) {
    return 'br';
  }
  return undefined;
};

/**
 * Creates configured upload function for individual files
 * @param {Object} config
 * @param {string} config.directoryPath - Base directory path containing files
 * @param {Object} config.awsClient - Configured AWS S3 client instance
 * @param {string} config.awsBucket - Target S3 bucket name
 * @param {Object} config.appConfig - Application configuration object
 * @param {Function} config.generateAwsKey - Function to generate S3 keys
 * @returns {Function} Configured upload function that accepts a file path
 */
const uploadSingleFile =
  ({ directoryPath, awsClient, awsBucket, appConfig, generateAwsKey }) =>
  async filePath => {
    const awsKey = generateAwsKey(directoryPath, filePath, appConfig);
    const contentType = getMimeType(filePath);
    const contentEncoding = getContentEncoding(filePath);
    const cacheControl = getCacheControl();

    const uploadParams = {
      key: awsKey,
      file: filePath,
      bucket: awsBucket,
      contentType,
      cacheControl,
    };

    if (contentEncoding) {
      uploadParams.contentEncoding = contentEncoding;
    }

    try {
      const result = await uploadToAws(awsClient, uploadParams);
      return { success: true, file: awsKey, result };
    } catch (error) {
      console.error(`✗ Failed to upload ${filePath}:`, error.message);
      return { success: false, file: awsKey, error: error.message };
    }
  };

/**
 * Creates array of upload promises for concurrent execution
 * @param {Object} config
 * @param {string[]} config.files - Array of file paths to upload
 * @param {string} config.directoryPath - Base directory path containing files
 * @param {Object} config.awsClient - Configured AWS S3 client instance
 * @param {string} config.awsBucket - Target S3 bucket name
 * @param {Object} config.appConfig - Application configuration object
 * @param {Function} config.generateAwsKey - Function to generate S3 keys
 * @returns {Promise[]} Array of upload promises
 */
const uploadAllFiles = ({ files, directoryPath, awsClient, awsBucket, appConfig, generateAwsKey }) =>
  _map(
    files,
    uploadSingleFile({
      directoryPath,
      awsClient,
      awsBucket,
      appConfig,
      generateAwsKey,
    })
  );

// Export the utility functions
module.exports = {
  uploadToAws,
  uploadAllFiles,
};
