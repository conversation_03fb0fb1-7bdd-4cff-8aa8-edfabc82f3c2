/* eslint-disable no-console */
// Source code upload module for S3 deployment

const _filter = require('lodash/filter');
const _size = require('lodash/size');
const _forEach = require('lodash/forEach');

const { uploadAllFiles } = require('./deploy.s3Upload');
const { getAllFiles, generateAwsKey } = require('./deploy.file');

const SOURCE_MAP_FILE_REGEX = /.*\.chunk.js.map$/;

/**
 * Filters out source map files from the file list
 * Source maps are handled separately and should not be uploaded with regular assets
 *
 * @param {string[]} files - Array of file paths
 * @returns {string[]} Filtered array excluding source map files
 */
const getFilesWithoutSourceMaps = files => _filter(files, file => !SOURCE_MAP_FILE_REGEX.test(file));

/**
 * Prints upload summary with success/failure counts
 * @param {number} successful - Number of successful uploads
 * @param {Object[]} failedUploads - Array of failed upload result objects
 * @param {number} failed - Number of failed uploads
 */
const printUploadSummary = (successful, failedUploads, failed) => {
  console.log(`\nUpload completed: ${successful} successful, ${failed} failed`);

  if (failed > 0) {
    console.log('\nFailed uploads:');
    _forEach(failedUploads, r => {
      console.log(`  - ${r.file}: ${r.error}`);
    });
  }
};

/**
 * Main function to upload source code files to AWS S3
 *
 * This is the primary entry point for the source code upload process.
 * It orchestrates the entire upload workflow including:
 * - Directory scanning and file discovery
 * - Source map filtering
 * - Concurrent file uploads with progress tracking
 * - Comprehensive error handling and reporting
 *
 * @param {Object} config - Upload configuration
 * @param {Object} config.awsClient - Configured AWS S3 client instance
 * @param {string} config.directoryPath - Local directory path containing built files
 * @param {string} config.awsBucket - Target S3 bucket name
 * @param {Object} config.appConfig - Application configuration object
 * @returns {Promise<Object>} Upload summary with counts and detailed results
 * @returns {Promise<Object>} result.total - Total number of files processed
 * @returns {Promise<Object>} result.successful - Number of successful uploads
 * @returns {Promise<Object>} result.failed - Number of failed uploads
 * @returns {Promise<Object>} result.results - Array of individual upload results
 */
const uploadSourceCode = async ({ awsClient, directoryPath, awsBucket, appConfig }) => {
  console.log(`Starting upload from ${directoryPath} to AWS bucket: ${awsBucket}`);
  // Discover all files in the build directory recursively
  const allFiles = await getAllFiles(directoryPath);
  console.log(`Found ${allFiles.length} total files`);

  // Filter out source map files (handled separately)
  const filesWithoutSourceMaps = getFilesWithoutSourceMaps(allFiles);
  console.log(
    `Uploading ${filesWithoutSourceMaps.length} files (excluding ${allFiles.length - filesWithoutSourceMaps.length} source map files)`
  );

  // Create upload promises for concurrent processing
  const uploadPromises = uploadAllFiles({
    files: filesWithoutSourceMaps,
    directoryPath,
    awsClient,
    awsBucket,
    appConfig,
    generateAwsKey,
  });

  // Execute all uploads concurrently and wait for completion
  const results = await Promise.all(uploadPromises);

  // Analyze results and generate summary statistics
  const total = _size(results);
  const failedUploads = _filter(results, r => !r.success);
  const failed = _size(failedUploads);
  const successful = total - failed;

  // Display comprehensive upload summary
  printUploadSummary(successful, failedUploads, failed);

  if (failed > 0) {
    console.warn(`⚠️  ${failed} files failed to upload`);
    throw new Error(`Failed to upload ${failed} files`);
  } else {
    console.log('✅ All files uploaded successfully');
  }
};

module.exports = uploadSourceCode;
