import _keyBy from 'lodash/keyBy';
import _property from 'lodash/property';

import { PERSONAS } from '@tekion/tekion-base/constants/personas';
import OPERATORS from '@tekion/tekion-base/constants/filterOperators';

import { DYNAMIC_TAG_GROUPS } from '@tekion/tekion-widgets/src/appServices/communication/constants/templateBuilder/tag.constants';
import { DATE_TIME_FORMAT } from '@tekion/tekion-conversion-web';

export const CONTACT_COLLECTION_STATUS = {
  COMPLETE: 'COMPLETE',
  ASSIGNMENT_COMPLETED: 'ASSIGNMENT_COMPLETED',
  PENDING: 'PENDING',
  IN_PROGRESS: 'IN_PROGRESS',
};

export const ZIP_VALIDATOR_REGEX = /(^\d{5}(-\d{4})?$)|(^[ABCEGHJKLMNPRSTVXY]{1}\d{1}[A-Z]{1} *\d{1}[A-Z]{1}\d{1}$)/;
export const PARTIAL_ZIP_VALIDATOR_REGEX =
  /(^\d{0,5}$)|(^\d{5}(-\d{1,4})?$)|(^[ABCEGHJKLMNPRSTVXY]{1}$)|(^[ABCEGHJKLMNPRSTVXY]{1}\d{1}$)|(^[ABCEGHJKLMNPRSTVXY]{1}\d{1}[A-Z]{1}$)|(^[ABCEGHJKLMNPRSTVXY]{1}\d{1}[A-Z]{1} *\d{1}$)|(^[ABCEGHJKLMNPRSTVXY]{1}\d{1}[A-Z]{1} *\d{1}[A-Z]{1}$)|(^[ABCEGHJKLMNPRSTVXY]{1}\d{1}[A-Z]{1} *\d{1}[A-Z]{1}\d{1}$)/;

export const CAMPAIGN_OBJECTIVES = {
  SALES: 'sales',
  SERVICE: 'service',
};

export const SCRIPT_TYPES = {
  PHONE: 'PHONE_CALL',
  EMAIL: 'EMAIL',
  TEXT: 'TEXT',
};

export const CAMPAIGN_TYPES = {
  MANUAL: 'MANUAL',
  AUTOMATIC: 'AUTOMATIC',
};

export const CAMPAIGN_TYPE_LABLE = {
  [CAMPAIGN_TYPES.MANUAL]: __('Manual'),
  [CAMPAIGN_TYPES.AUTOMATIC]: __('Automatic'),
};

export const CAMPAIGN_TYPE_NAMES = {
  [CAMPAIGN_TYPES.MANUAL]: __('Manual Outbound Reach'),
  [CAMPAIGN_TYPES.AUTOMATIC]: __('Automatic'),
};

export const CAMPAIGN_OBJECTIVE_LABELS = {
  [CAMPAIGN_OBJECTIVES.SALES]: __('Sales'),
  [CAMPAIGN_OBJECTIVES.SERVICE]: __('Service'),
};

export const DAILY_LIMIT_EXHAUSTED_EMAIL = 'DAILY_LIMIT_EXHAUSTED_EMAIL';

const UNEXPECTED_ERROR_LABEL = __('Unexpected Error');

export const COMMUNICATION_TYPE = {
  PHONE: 'PHONE_CALL',
  EMAIL: 'EMAIL',
  TEXT: 'TEXT',
};

export const COMMUNICATION_FAILURE_REASONS = {
  CONFIG_ERROR: __(
    'There’s an issue with your configuration. Please review the details or contact support for assistance.'
  ),
  EMAIL_ADDRESS_MISSING: __('Email delivery failed because Email Address is not present'),
  EMAIL_CONTENT_MISSING: __('Email Message or Subject is missing, please update the template and try again'),
  EMAIL_TEMPLATE_ERROR: __('Email delivery failed because of an issue with Email template'),
  SENDER_EMAIL_INVALID: __('Email delivery failed because sender email is not valid'),
  EMAIL_ADDRESS_INVALID: __('Email delivery failed because Email Address is invalid'),
  NO_VALID_EMAIL_ADDRESS: __('Email delivery failed because Email Address is invalid'),
  ATTACHMENT_ERROR: __('Email delivery failed because of an issue with email attachment'),
  EMAIL_LIMIT_EXCEEDED: __('Daily Email limit has exhausted'),
  EMAIL_PROCESSING_FAILED: __('Failed to process the Email'),
  EMAIL_UNKNOWN_ERROR: __('Email delivery failed for unknown reason'),
  EMAIL_SERVICE_ERROR: __('Email delivery failed at Email service'),
  FILE_UPLOAD_ERROR: __(
    'There was an issue with your file upload or attachment. Please check the file size or try uploading the file again.'
  ),
  EMAIL_BOUNCE_ERROR: __('Email validation failed due to high chance of bounce.'),
  CAMPAIGN_LIMIT_ERROR: __('Daily campaign limit is reached for emails.'),
  OPTIN_UNKNOWN: __('Contact Opt-In status is unknown for this email category'),
  OPTED_OUT_ERROR: __('The contact has opted out of this category for Email communication'),
  CONTACT_OPTED_OUT: __('The contact has opted out of this category for Email communication'),
  [DAILY_LIMIT_EXHAUSTED_EMAIL]: __('Daily campaign limit is reached for emails'),
  DAILY_LIMIT_EXHAUSTED_TEXT: __('Failed to send text due to daily campaign text limit is consumed'),
  UNKNOWN_OPT_IN_STATUS: __('Contact Opt-In status is unknown for this email category.'),
  DND_ENABLED: __('Contact Do Not Disturb (DND) is enabled.'),
  EMAIL_ADDRESS_NOT_PRESENT: __('Email delivery failed because Email Address is not present.'),
  INVALID_EMAIL_ADDRESS: __('Email delivery failed because Email Address is invalid'),
  FAILED_TO_PROCESS_EMAIL: __('System failed to process the email.'),
  UNRESOLVED_TAGS_PRESENT: __('Mail has unresolved tags.'),
  COMPLAINT_FOUND: __('Contact marked the sender mail in the Complaint list.'),
  EMAIL_BOUNCED: __('Email validation failed due to high chance of bounce.'),
  EMAIL_REJECTED: __('Email content rejected due to sender policy'),
  MAIL_NOT_ATTEMPTED: __('Campaign ended - Mail not attempted for delivery.'),
  CONTACT_OPTED_OUT_TEXT: __('Contact Opted out of this text category.'),
  TWILIO_ERROR: __('Text delivery failed on Text messaging service'),
  INVALID_PHONE_NUMBER: __('Invalid phone number.'),
  PHONE_NOT_PRESENT: __('Phone number not present.'),
  TEMPLATE_RESOLUTION_FAILURE: __('Error in loading the email template.'),
  DEFAULT_SENDER_EMAIL_MISSING: __('Default sender email for campaign is not configured'),
  EMAIL_BOUNCED_PERMANENT: __('Email validation failed due to high chance of bounce'),
  UNKNOWN_TEXT_OPT_IN_STATUS: __('Contact Opt-In status is unknown for this text category.'),
  EMAIL_UNVERIFIED_SENDER: __('Sender email domain is not validated please contact support'),
  INVALID_RECEIVER_NUMBER: __('Recipients number is invalid'),
  INVALID_SENDER_NUMBER: __('Sender number is invalid'),
  TWILIO_BLACKLIST_ERROR: __("Customer's number is blacklisted"),
  PHONE_NUMBER_OF_TYPE_UNKNOWN_DESTINATION: __('The destination number is unknown and may no longer exist.'),
  PHONE_NUMBER_OF_TYPE_LANDLINE_OR_UNREACHABLE_CARRIER: __(
    'The destination number or carrier is unable to receive this message.'
  ),
  UNEXPECTED_ERROR: UNEXPECTED_ERROR_LABEL,
  UNEXPECTED_EMAIL_ERROR: __('Unexpected Email Error'),
  PHONE_NUMBER_OF_TYPE_LANDLINE: __('Landline phone number'),
  DND_ENABLED_FOR_PHONE_NUMBER: __('DND Enabled for phone number'),
  UNEXPECTED_TEXT_ERROR: __('Unexpected Text Error'),
  TWILIO_EXCEPTION: __('Text delivery failed on Twilio service'),
  EMAIL_STATUS_FETCH_TIMEOUT: __('Timeout while fetching email status'),
  TWILIO_BLACKLIST_EXCEPTION: __("Customer's number is blacklisted"),
  [COMMUNICATION_TYPE.PHONE]: {
    DND_ENABLED: __('Contact Do Not Disturb (DND) is enabled.'),
    INVALID_PHONE_NUMBER: __('Invalid phone number.'),
    PHONE_NOT_PRESENT: __('Phone number not present.'),
    INVALID_RECEIVER_NUMBER: __('Recipients number is invalid'),
    INVALID_SENDER_NUMBER: __('Sender number is invalid'),
    TWILIO_BLACKLIST_ERROR: __("Customer's number is blacklisted"),
    PHONE_NUMBER_OF_TYPE_UNKNOWN_DESTINATION: __('The destination number is unknown and may no longer exist.'),
    PHONE_NUMBER_OF_TYPE_LANDLINE_OR_UNREACHABLE_CARRIER: __(
      'The destination number or carrier is unable to receive this message.'
    ),
    UNEXPECTED_ERROR: UNEXPECTED_ERROR_LABEL,
    PHONE_NUMBER_OF_TYPE_LANDLINE: __('Landline phone number'),
    DND_ENABLED_FOR_PHONE_NUMBER: __('DND Enabled for phone number'),
    TWILIO_BLACKLIST_EXCEPTION: __("Customer's number is blacklisted"),
  },
  [COMMUNICATION_TYPE.EMAIL]: {
    CONFIG_ERROR: __(
      'There’s an issue with your configuration. Please review the details or contact support for assistance.'
    ),
    EMAIL_ADDRESS_MISSING: __('Email delivery failed because Email Address is not present'),
    EMAIL_CONTENT_MISSING: __('Email Message or Subject is missing, please update the template and try again'),
    EMAIL_TEMPLATE_ERROR: __('Email delivery failed because of an issue with Email template'),
    SENDER_EMAIL_INVALID: __('Email delivery failed because sender email is not valid'),
    EMAIL_ADDRESS_INVALID: __('Email delivery failed because Email Address is invalid'),
    NO_VALID_EMAIL_ADDRESS: __('Email delivery failed because Email Address is invalid'),
    ATTACHMENT_ERROR: __('Email delivery failed because of an issue with email attachment'),
    EMAIL_LIMIT_EXCEEDED: __('Daily Email limit has exhausted'),
    EMAIL_PROCESSING_FAILED: __('Failed to process the Email'),
    EMAIL_UNKNOWN_ERROR: __('Email delivery failed for unknown reason'),
    EMAIL_SERVICE_ERROR: __('Email delivery failed at Email service'),
    FILE_UPLOAD_ERROR: __(
      'There was an issue with your file upload or attachment. Please check the file size or try uploading the file again.'
    ),
    EMAIL_BOUNCE_ERROR: __('Email validation failed due to high chance of bounce.'),
    CAMPAIGN_LIMIT_ERROR: __('Daily campaign limit is reached for emails.'),
    OPTIN_UNKNOWN: __('Contact Opt-In status is unknown for this email category'),
    OPTED_OUT_ERROR: __('The contact has opted out of this category for Email communication'),
    CONTACT_OPTED_OUT: __('The contact has opted out of this category for Email communication'),
    [DAILY_LIMIT_EXHAUSTED_EMAIL]: __('Daily campaign limit is reached for emails'),
    UNKNOWN_OPT_IN_STATUS: __('Contact Opt-In status is unknown for this email category.'),
    DND_ENABLED: __('Contact Do Not Disturb (DND) is enabled.'),
    EMAIL_ADDRESS_NOT_PRESENT: __('Email delivery failed because Email Address is not present.'),
    INVALID_EMAIL_ADDRESS: __('Email delivery failed because Email Address is invalid'),
    FAILED_TO_PROCESS_EMAIL: __('System failed to process the email.'),
    UNRESOLVED_TAGS_PRESENT: __('Mail has unresolved tags.'),
    COMPLAINT_FOUND: __('Contact marked the sender mail in the Complaint list.'),
    EMAIL_BOUNCED: __('Email validation failed due to high chance of bounce.'),
    EMAIL_REJECTED: __('Email content rejected due to sender policy'),
    MAIL_NOT_ATTEMPTED: __('Campaign ended - Mail not attempted for delivery.'),
    TEMPLATE_RESOLUTION_FAILURE: __('Error in loading the email template.'),
    DEFAULT_SENDER_EMAIL_MISSING: __('Default sender email for campaign is not configured'),
    EMAIL_BOUNCED_PERMANENT: __('Email validation failed due to high chance of bounce'),
    EMAIL_UNVERIFIED_SENDER: __('Sender email domain is not validated please contact support'),
    UNEXPECTED_ERROR: UNEXPECTED_ERROR_LABEL,
    UNEXPECTED_EMAIL_ERROR: __('Unexpected Email Error'),
    EMAIL_STATUS_FETCH_TIMEOUT: __('Timeout while fetching email status'),
  },
  [COMMUNICATION_TYPE.TEXT]: {
    DAILY_LIMIT_EXHAUSTED_TEXT: __('Failed to send text due to daily campaign text limit is consumed'),
    DND_ENABLED: __('Contact Do Not Disturb (DND) is enabled.'),
    UNRESOLVED_TAGS_PRESENT: __('Text has unresolved tags.'),
    CONTACT_OPTED_OUT_TEXT: __('Contact Opted out of this text category.'),
    TWILIO_ERROR: __('Text delivery failed on Text messaging service'),
    INVALID_PHONE_NUMBER: __('Invalid phone number.'),
    PHONE_NOT_PRESENT: __('Phone number not present.'),
    UNKNOWN_TEXT_OPT_IN_STATUS: __('Contact Opt-In status is unknown for this text category.'),
    INVALID_RECEIVER_NUMBER: __('Recipients number is invalid'),
    INVALID_SENDER_NUMBER: __('Sender number is invalid'),
    TWILIO_BLACKLIST_ERROR: __("Customer's number is blacklisted"),
    PHONE_NUMBER_OF_TYPE_UNKNOWN_DESTINATION: __('The destination number is unknown and may no longer exist.'),
    PHONE_NUMBER_OF_TYPE_LANDLINE_OR_UNREACHABLE_CARRIER: __(
      'The destination number or carrier is unable to receive this message.'
    ),
    UNEXPECTED_ERROR: UNEXPECTED_ERROR_LABEL,
    PHONE_NUMBER_OF_TYPE_LANDLINE: __('Landline phone number'),
    DND_ENABLED_FOR_PHONE_NUMBER: __('DND Enabled for phone number'),
    UNEXPECTED_TEXT_ERROR: __('Unexpected Text Error'),
    TWILIO_EXCEPTION: __('Text delivery failed on Twilio service'),
    TWILIO_BLACKLIST_EXCEPTION: __("Customer's number is blacklisted"),
  },
};

/**
 *
 * @Note : For form fields using AdvancedAssignPersona.
 */
export const EMAIL_GROUPS = {
  SYSTEM_WIDE: 'SYSTEM_WIDE',
  ROLE: 'ROLE',
  INDIVIDUAL: 'INDIVIDUAL',
  INDIVIDUAL_MULTIDOMAIN: 'INDIVIDUAL_MULTIDOMAIN',
};

export const GROUP_LABEL = {
  INDIVIDUAL: 'individual',
  INDIVIDUAL_MULTIDOMAIN: 'individual',
  ROLE: 'ROLE',
  SYSTEM_WIDE: 'SYSTEM_WIDE',
};

export const EMAIL_GROUPS_WITH_USERID = [EMAIL_GROUPS.INDIVIDUAL, EMAIL_GROUPS.INDIVIDUAL_MULTIDOMAIN];

export const EMAIL_ROLE_TO_GROUP_LABEL_MAPPING = {
  [EMAIL_GROUPS.INDIVIDUAL]: GROUP_LABEL.INDIVIDUAL,
  [EMAIL_GROUPS.ROLE]: GROUP_LABEL.ROLE,
  [EMAIL_GROUPS.SYSTEM_WIDE]: GROUP_LABEL.SYSTEM_WIDE,
};

export const GROUP_LABEL_TO_EMAIL_ROLE_MAPPING = {
  [GROUP_LABEL.INDIVIDUAL]: EMAIL_GROUPS.INDIVIDUAL,
  [GROUP_LABEL.ROLE]: EMAIL_GROUPS.ROLE,
  [GROUP_LABEL.SYSTEM_WIDE]: EMAIL_GROUPS.SYSTEM_WIDE,
};

export const GROUP_LABELS = {
  SYSTEM_WIDE_EMAIL: {
    VALUE: EMAIL_GROUPS.SYSTEM_WIDE,
    LABEL: __('System Wide Email'),
  },
  ROLES: {
    VALUE: EMAIL_GROUPS.ROLE,
    LABEL: __('Roles'),
  },
};

export const PERSONAS_FOR_INDIVIDUAL_GROUP = [
  PERSONAS.BDC_MANAGER,
  PERSONAS.BDC_SPECIALIST,
  PERSONAS.SALES_MANAGER,
  PERSONAS.SALES_PERSON,
];

export const ROLES_OPTIONS = {
  SALES_PERSON: {
    VALUE: 'SALES_PERSON',
    LABEL: __('Sales Person'),
  },
  BDC: {
    VALUE: 'BDC',
    LABEL: __('BDC'),
  },
  SALES_MANAGER: {
    VALUE: 'SALES_MANAGER',
    LABEL: __('Sales Manager'),
  },
};

export const EMAIL_OPTIONS = [
  {
    groupLabel: GROUP_LABELS.SYSTEM_WIDE_EMAIL.LABEL,
    groupValue: GROUP_LABELS.SYSTEM_WIDE_EMAIL.VALUE,
  },
  {
    groupLabel: GROUP_LABELS.ROLES.LABEL,
    isMultiSelectable: true,
    options: [
      {
        displayName: ROLES_OPTIONS.SALES_PERSON.LABEL,
        value: ROLES_OPTIONS.SALES_PERSON.VALUE,
        id: ROLES_OPTIONS.SALES_PERSON.VALUE,
        groupValue: GROUP_LABELS.ROLES.VALUE,
      },
      {
        displayName: ROLES_OPTIONS.BDC.LABEL,
        value: ROLES_OPTIONS.BDC.VALUE,
        id: ROLES_OPTIONS.BDC.VALUE,
        groupValue: GROUP_LABELS.ROLES.VALUE,
      },
      {
        displayName: ROLES_OPTIONS.SALES_MANAGER.LABEL,
        value: ROLES_OPTIONS.SALES_MANAGER.VALUE,
        id: ROLES_OPTIONS.SALES_MANAGER.VALUE,
        groupValue: GROUP_LABELS.ROLES.VALUE,
      },
    ],
  },
];

export const INDIVIDUAL = 'individual';
export const REPLY_TO_EMAILS = 'replyToEmails';
export const ROLE = 'ROLE';
export const SENDER_EMAIL = 'senderEmail';
export const SENDER_MAIL_ID = 'senderMail';

export const ACTION_TYPES = {
  SET_IS_INBOUND_METADATA_LOADING: 'SET_IS_INBOUND_METADATA_LOADING',
  SET_INBOUND_METADATA: 'SET_INBOUND_METADATA',
  SET_USERS_OPTIONS: 'SET_USERS_OPTIONS',
  SET_FILTERS_OPTIONS_DATA: 'SET_FILTERS_OPTIONS_DATA',
};

const RESOLVE_ALL_THE_ERRORS_MSG = __('Resolve all the errors to proceed');

export const ERROR_MESSAGE = {
  EMAIL: RESOLVE_ALL_THE_ERRORS_MSG,
  EMAIL_SUBJECT: __('Subject or Mail Body is empty'),
  EMAIL_FOOTER: __('Footer is missing'),
  NO_TRIGGERS: __('Please create a trigger to move to next section'),
  NO_SCHEDULE: __('Day Schedule is missing in timeline. Please select day schedule to move further'),
  ORDER_NOT_VALID: __('Timeline schedule must be in ascending order. Please correct to continue'),
  NO_CONTACTS: __('Contacts list empty, please select contacts to move to next section'),
  PHONE_MANDTATORY: __('Phonescript is mandatory. Please create a phone script to move to next section'),
  EMAIL_MANDTATORY: __('Emailscript is mandatory. Please create a email script to move to next section'),
  TEXT_MANDTATORY: __('Textscript is mandatory. Please create a text script to move to next section'),
  AUTOMATED_TEXT: RESOLVE_ALL_THE_ERRORS_MSG,
  NO_DEFAULT_SENDER_EMAIL: __('Default sender email for campaign is not configured, please reach out to support'),
  RESOLVE_ALL_THE_ERRORS: RESOLVE_ALL_THE_ERRORS_MSG,
  EMAIL_SUBJECT_LENGTH_EXCEEDED: exceedLength =>
    __(
      'Allowed Subject Length for better deliverability is only {{exceedLength}} characters. Please reduce length to proceed further.',
      { exceedLength }
    ),
};

export const getRelativeDateFormat = getDateTimeFormatValue => ({
  sameDay(now) {
    const diff = this.diff(now, 'hours');
    if (diff < -1) {
      return `[${Math.abs(diff)} hours ago]`;
    }
    if (diff === -1) {
      return '[1 hour ago]';
    }
    return '[now]';
  },
  lastDay: '[Yesterday] LT',
  lastWeek: getDateTimeFormatValue(DATE_TIME_FORMAT.ABBREVIATED_DAY_DATE_ABBREVIATED_MONTH_YEAR_WITH_HOUR_MINUTE),
  sameElse: getDateTimeFormatValue(DATE_TIME_FORMAT.ABBREVIATED_DAY_DATE_ABBREVIATED_MONTH_YEAR_WITH_HOUR_MINUTE),
});

export const CAMPAIGN_SUPPORTED_TAGS = {
  [CAMPAIGN_TYPES.AUTOMATIC]: [
    DYNAMIC_TAG_GROUPS.BUYER,
    DYNAMIC_TAG_GROUPS.STORE,
    DYNAMIC_TAG_GROUPS.USER,
    DYNAMIC_TAG_GROUPS.VEHICLE_OF_INTEREST,
    DYNAMIC_TAG_GROUPS.CUSTOMER,
    DYNAMIC_TAG_GROUPS.APPOINTMENT,
    DYNAMIC_TAG_GROUPS.SERVICE_APPOINTMENT,
    DYNAMIC_TAG_GROUPS.CUSTOMER,
    DYNAMIC_TAG_GROUPS.VEHICLE,
    DYNAMIC_TAG_GROUPS.REPAIR_ORDER,
  ],
  [CAMPAIGN_TYPES.MANUAL]: [
    DYNAMIC_TAG_GROUPS.BUYER,
    DYNAMIC_TAG_GROUPS.STORE,
    DYNAMIC_TAG_GROUPS.USER,
    DYNAMIC_TAG_GROUPS.VEHICLE_OF_INTEREST,
    DYNAMIC_TAG_GROUPS.CUSTOMER,
    DYNAMIC_TAG_GROUPS.APPOINTMENT,
    DYNAMIC_TAG_GROUPS.SERVICE_APPOINTMENT,
    DYNAMIC_TAG_GROUPS.CUSTOMER,
    DYNAMIC_TAG_GROUPS.VEHICLE,
    DYNAMIC_TAG_GROUPS.REPAIR_ORDER,
  ],
};

export const BUSINESS_CUSTOMER_TYPE = '1';

export const CUSTOMER_TYPES = [__('Individual'), __('Business')];

export const CUSTOMER_PRIVACY_VALUES = {
  OPTED_OUT: 'OPTED_OUT',
  NOT_OPTED_IN: 'UNKNOWN',
  OPTED_IN: 'OPTED_IN',
};

export const CUSTOMER_PRIVACY_OPTIONS = [
  {
    label: __('Opted-Out'),
    value: CUSTOMER_PRIVACY_VALUES.OPTED_OUT,
    isDisabled: true,
    isSelected: true,
  },
  {
    label: __('Not Opted-In'),
    value: CUSTOMER_PRIVACY_VALUES.NOT_OPTED_IN,
  },
];

export const CAMPAIGN_MANAGEMENT_ACTIONS = {
  CAMPAIGNS_METADATA_REQUEST: 'CAMPAIGNS_METADATA_REQUEST',
  CAMPAIGNS_METADATA_SUCCESS: 'CAMPAIGNS_METADATA_SUCCESS',
  CAMPAIGNS_METADATA_FAILURE: 'CAMPAIGNS_METADATA_FAILURE',
  CAMPAIGNS_ADD_USERS_OPTIONS: 'CAMPAIGNS_ADD_USERS_OPTIONS',
};

export const MAX_RETRY_COUNT = 2;

export const FIELD_MAPPERS = {
  OEM_SITES: 'OEM_SITES',
};

export const CUSTOMER_PRIVACY_OPTIONS_BY_VALUE = _keyBy(CUSTOMER_PRIVACY_OPTIONS, 'value');

export const CAMPAIGN_COMM_TYPE = { communicationType: 'CAMPAIGN_EMAILS' };

export const DATA_IDENTIFIERS = {
  SYSTEM_WIDE_EMAIL: 'systemWideEmails',
  DOMAINS: 'domains',
  MULTI_DOMAIN_USER_DATA: 'multiDomainUsersData',
  HAS_CURATED_CAMPAIGN: 'hasCuratedCampaign',
};

export const MULTI_DOMAIN_USER_DATA_PAYLOAD = {
  typeVsValuesToSearchFor: {
    PERSONA: PERSONAS_FOR_INDIVIDUAL_GROUP,
  },
};

export const SITE_FILTER_ID = 'dealerOemSites.oemSites.siteId';

export const PREFERENCE_CATEGORY_MAPPER = {
  label: _property('categoryDisplayName'),
  value: _property('category'),
};

export const VALID = 'VALID';

export const DEFAULT_EMAIL_SUBJECT_LENGTH = 50;

export const MODULE_TYPES = {
  ALL: 'ALL',
  CAMPAIGN: 'CAMPAIGN',
};

export const VISIBLE_MODULES = [MODULE_TYPES.CAMPAIGN];
export const EMAIL_TYPES = {
  SENDER: 'sender',
  REPLY_TO: 'replyTo',
};

export const EMAIL_CONFIG_KEYS = {
  SENDER_EMAIL_CONFIG: 'senderMailConfigs',
  REPLY_TO_EMAIL_CONFIG: 'replyToMailConfigs',
};

export const FETCH_CURATED_CAMPAIGN_PAYLOAD = {
  sort: [],
  filters: [
    {
      field: 'campaignStatus',
      operator: OPERATORS.IN,
      values: ['CURATED'],
      key: 'campaignStatus',
    },
    {
      field: 'deleted',
      operator: 'IN',
      values: [false],
      key: 'deleted',
    },
  ],
  searchText: '',
  groupBy: [],
  includeFields: [],
  searchableFields: [],
  excludeFields: [],
  pageInfo: {
    start: 0,
    rows: 0,
  },
};

export const FIELD_KEYS = {
  ID: 'id',
};

export const FIELD_IDS = {
  NAME: 'name',
};
