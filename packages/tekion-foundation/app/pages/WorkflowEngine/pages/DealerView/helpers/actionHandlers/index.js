import ACTION_TYPES from '../../constants/dealerView.actionTypes';
import { stageActionHandlers } from './stageActionHandlers';
import { formActionHandlers } from './formActionHandlers';
import { nodeActionHandlers } from './nodeActionHandlers';
import { modalActionHandlers } from './modalActionHandlers';
import { initializationActionHandlers } from './initializationActionHandlers';

const ACTION_HANDLERS = {
  // Initialization
  [ACTION_TYPES.INIT_DEALER_SETUP]: initializationActionHandlers.handleInitDealerConfig,

  // Stage Management
  [ACTION_TYPES.CLOSE_DELETE_STAGE_MODAL]: stageActionHandlers.handleCloseDeleteStageModal,
  [ACTION_TYPES.CLOSE_STAGE_DRAWER]: stageActionHandlers.handleCloseStageDrawer,
  [ACTION_TYPES.OPEN_ADD_STAGE_DRAWER]: stageActionHandlers.openStageDrawer,
  [ACTION_TYPES.TOGGLE_STAGE_KEBAB_MENU_MODAL]: stageActionHandlers.toggleOpenStageOptionsModal,
  [ACTION_TYPES.HANDLE_CLICK_SAVE_STAGE]: stageActionHandlers.handleClickSaveStage,
  [ACTION_TYPES.OPEN_EDIT_STAGE_DRAWER]: stageActionHandlers.handleOpenEditStageDrawer,
  [ACTION_TYPES.TOGGLE_DELETE_STAGE_MODAL]: stageActionHandlers.handleToggleDeleteStageModal,
  [ACTION_TYPES.HANDLE_DELETE_STAGE]: stageActionHandlers.handleDeleteStage,
  [ACTION_TYPES.MOVE_STAGE]: stageActionHandlers.handleMoveStage,
  [ACTION_TYPES.HANDLE_STAGE_NAME_CHANGE]: stageActionHandlers.handleStageNameChange,

  // Form Management
  [ACTION_TYPES.CLOSE_DELETE_FORM_MODAL]: formActionHandlers.handleCloseDeleteFormModal,
  [ACTION_TYPES.HANDLE_CLICK_FORM_NAME]: formActionHandlers.handleClickStagesPanelFormName,
  [ACTION_TYPES.HANDLE_DELETE_FORM]: formActionHandlers.handleDeleteForm,
  [ACTION_TYPES.HANDLE_FORM_SELECTION]: formActionHandlers.handleSelectForm,
  [ACTION_TYPES.SORT_FORMS]: formActionHandlers.handleSortForms,
  [ACTION_TYPES.HANDLE_OPEN_DELETE_FORM_MODAL]: formActionHandlers.handleOpenDeleteStageDrawerForm,
  [ACTION_TYPES.INIT_ACTIONS_FOR_FORMS]: formActionHandlers.handleInitFormActions,
  [ACTION_TYPES.SET_IS_FORM_EDITED]: formActionHandlers.handleChangeIsFormEdited,
  [ACTION_TYPES.CHANGE_INACTIVE_AFTER_DAYS]: formActionHandlers.handleChangeInactiveLeadAfterDays,
  [ACTION_TYPES.SET_DRAFT_FORM_ACTIONS]: formActionHandlers.handleSetDraftFormActions,
  [ACTION_TYPES.UPDATE_DRAFT_FORM_ACTIONS]: formActionHandlers.handleUpdateDraftFormActions,

  // Node/Action Management
  [ACTION_TYPES.TOGGLE_DELETE_ACTION_MODAL]: nodeActionHandlers.handleToggleDeleteActionModal,
  [ACTION_TYPES.SORT_ACTION_NODE_CARDS]: nodeActionHandlers.handleSortActionNodes,
  [ACTION_TYPES.HANDLE_NODE_DUPLICATION]: nodeActionHandlers.handleNodeDuplication,
  [ACTION_TYPES.CANCEL_EDIT_ACTION_NODES]: nodeActionHandlers.handleCancelFormNodesEdit,
  [ACTION_TYPES.DELETE_NODE]: nodeActionHandlers.handleDeleteNode,
  [ACTION_TYPES.RE_INIT_FORMS_PER_DAYS]: nodeActionHandlers.handleReInitFormsEachDay,
  [ACTION_TYPES.PUBLISH_ACTION_NODES]: nodeActionHandlers.handlePublishActionNodes,
  [ACTION_TYPES.CLOSE_ACTION_DRAWER]: nodeActionHandlers.handleCloseActionDrawer,
  [ACTION_TYPES.OPEN_ADD_ACTION_DRAWER]: nodeActionHandlers.handleOpenAddActionDrawer,
  [ACTION_TYPES.OPEN_EDIT_ACTION_DRAWER]: nodeActionHandlers.handleOpenEditActionDrawer,
  [ACTION_TYPES.SAVE_NODE_DRAWER_DATA]: nodeActionHandlers.handleSaveNodeData,
  [ACTION_TYPES.TOGGLE_DUPLICATE_DAY_MODAL]: nodeActionHandlers.handleToggleDuplicateDayModal,
  [ACTION_TYPES.DUPLICATE_DAY_ACTIONS]: nodeActionHandlers.handleDuplicateDayActions,
  [ACTION_TYPES.DELETE_DAY_ACTIONS]: nodeActionHandlers.handleDeleteDayActions,
  [ACTION_TYPES.TOGGLE_DELETE_DAY_ACTIONS_MODAL]: nodeActionHandlers.handleToggleDeleteDayActionsModal,
  [ACTION_TYPES.TOGGLE_NODE_SELECTION]: nodeActionHandlers.handleToggleNodeSelection,
  [ACTION_TYPES.DUPLICATE_SELECTED_NODES]: nodeActionHandlers.handleBulkNodeDuplication,
  [ACTION_TYPES.CLEAR_SELECTED_NODES]: nodeActionHandlers.handleClearNodeSelection,
  [ACTION_TYPES.BULK_DELETE_SELECTED_NODES]: nodeActionHandlers.handleBulkDeleteSelectedNodes,
  [ACTION_TYPES.BULK_EDIT_DAY_NODES]: nodeActionHandlers.handleBulkEditDayNodes,
  [ACTION_TYPES.SET_SELECTED_NODES]: nodeActionHandlers.handleSelectAllNodes,

  // Modal Management & UI State
  [ACTION_TYPES.SHOW_CENTRAL_MODAL]: modalActionHandlers.handleShowCentralModal,
  [ACTION_TYPES.HIDE_CENTRAL_MODAL]: modalActionHandlers.handleHideCentralModal,
  [ACTION_TYPES.SET_CENTRAL_MODAL_LOADING]: modalActionHandlers.handleSetCentralModalLoading,
  [ACTION_TYPES.SET_MODAL_DETAILS]: modalActionHandlers.handleSetModalDetails,
  [ACTION_TYPES.HANDLE_ENTITY_ID_CHANGE]: modalActionHandlers.handleEntityIdChange,
};

export default ACTION_HANDLERS;

export {
  stageActionHandlers,
  formActionHandlers,
  nodeActionHandlers,
  modalActionHandlers,
  initializationActionHandlers,
};
