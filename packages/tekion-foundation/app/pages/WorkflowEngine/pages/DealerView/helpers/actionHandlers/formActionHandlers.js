import _get from 'lodash/get';
import _filter from 'lodash/filter';
import _isEmpty from 'lodash/isEmpty';

import isStringEmpty from '@tekion/tekion-base/utils/isStringEmpty';
import { uuid } from '@tekion/tekion-components/src/utils';
import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import FORM_FIELD_TYPES from '@tekion/tekion-widgets/src/organisms/ruleEngineWidgets/components/RuleEngineForms/formComponent/constants/formComponent.fieldTypes';

import { getDaysVsActionsMap } from '../dealerView.helpers';
import { fetchFormByFormId } from '../../actions/dealerView.actions';
import { DEFAULT_CENTRAL_MODAL_STATE } from '../../constants/dealerView.constants';
import { MODAL_TYPES } from '../../components/organisms/CentralModal/constants/centralModal.constants';

const handleCloseDeleteFormModal = ({ setState }) => {
  setState({
    centralModal: DEFAULT_CENTRAL_MODAL_STATE,
  });
};

const handleClickStagesPanelFormName = async ({ getState, setState, params = EMPTY_OBJECT }) => {
  const { formId = '' } = params;
  const { formDataById } = getState();

  setState({
    isFormLoading: true,
  });

  const fetchedGlobalForm = await fetchFormByFormId(formId);
  if (!_isEmpty(fetchedGlobalForm)) {
    setState({
      formDataById: { ...formDataById, [formId]: fetchedGlobalForm },
    });
  }
  setState({
    selectedFormId: formId,
    isFormLoading: false,
  });
};

const handleDeleteForm = ({ getState, setState, params = EMPTY_OBJECT }) => {
  const { selectedStageId, selectedFormsByStageId } = getState();
  const { formId } = params;

  const updatedFormIds = _filter(selectedFormsByStageId[selectedStageId], id => id !== formId);

  setState({
    selectedFormsByStageId: {
      ...selectedFormsByStageId,
      [selectedStageId]: updatedFormIds,
    },
    centralModal: DEFAULT_CENTRAL_MODAL_STATE,
  });
};

const handleSelectForm = ({ getState, setState, params = EMPTY_OBJECT }) => {
  const newFormId = _get(params, 'newFormId', '');
  const index = _get(params, 'index', '');

  const { selectedStageId, selectedFormsByStageId, stageDrawerMode } = getState();

  if (stageDrawerMode === 'EDIT') {
    const formIds = [...selectedFormsByStageId[selectedStageId]];

    if (index < formIds.length) {
      formIds[index] = newFormId;
    } else {
      formIds.push(newFormId);
    }

    setState({
      selectedFormsByStageId: {
        ...selectedFormsByStageId,
        [selectedStageId]: formIds,
      },
    });
  } else {
    const newStageId = selectedStageId || uuid();
    const formIds = [..._get(selectedFormsByStageId, newStageId, EMPTY_ARRAY)];

    if (index < formIds.length) {
      formIds[index] = newFormId;
    } else {
      formIds.push(newFormId);
    }

    setState({
      selectedStageId: newStageId,
      selectedFormsByStageId: {
        ...selectedFormsByStageId,
        [newStageId]: formIds,
      },
    });
  }
};

const handleSortForms = ({ setState, getState, params }) => {
  const { selectedStageId, selectedFormsByStageId } = getState();
  const { oldIndex, newIndex } = params;

  const currentFormIds = _get(selectedFormsByStageId, selectedStageId) || EMPTY_ARRAY;
  const reorderedFormIds = [...currentFormIds];

  const [movedForm] = reorderedFormIds.splice(oldIndex, 1);
  reorderedFormIds.splice(newIndex, 0, movedForm);

  setState({
    selectedFormsByStageId: {
      ...selectedFormsByStageId,
      [selectedStageId]: reorderedFormIds,
    },
  });
};

const handleOpenDeleteStageDrawerForm = ({ setState, params }) => {
  const formId = _get(params, 'formId', '');

  setState({
    formIdPendingDeletion: formId,
    centralModal: {
      isVisible: true,
      modalType: MODAL_TYPES.DELETE_FORM,
      modalData: { formId },
      isLoading: false,
    },
  });
};

const handleInitFormActions = async ({ setState, getState }) => {
  const { formDataById, selectedFormId, workflowActionsByFormId, actionScheduleByDay } = getState();
  const formData = formDataById[selectedFormId] || EMPTY_OBJECT;
  const allFetchedFormActions =
    _get(formData, [FORM_FIELD_TYPES.ACTION_NODE_CONFIGS, FORM_FIELD_TYPES.ACTIONS]) || EMPTY_ARRAY;

  const dayVsActionsMap = getDaysVsActionsMap({ formActions: allFetchedFormActions });

  setState({
    workflowActionsByFormId: { ...workflowActionsByFormId, [selectedFormId]: allFetchedFormActions },
    actionScheduleByDay: { ...actionScheduleByDay, [selectedFormId]: dayVsActionsMap },
  });
};

const handleChangeIsFormEdited = ({ getState, setState, params }) => {
  const { workflowActionsByFormId } = getState();
  const { isFormComponentEdited } = params;

  if (!isFormComponentEdited) {
    setState({
      pendingFormActionsByFormId: workflowActionsByFormId,
      hasUnsavedFormChanges: isFormComponentEdited,
    });
  } else {
    setState({
      hasUnsavedFormChanges: isFormComponentEdited,
    });
  }
};

const handleChangeInactiveLeadAfterDays = ({ getState, setState, params = EMPTY_OBJECT }) => {
  const { leadInactivityTimeoutByFormId } = getState();
  const { formId = '', updatedValue = null } = params;

  setState({
    leadInactivityTimeoutByFormId: {
      ...leadInactivityTimeoutByFormId,
      ...(!isStringEmpty(formId) && { [formId]: updatedValue }),
    },
  });
};

const handleSetDraftFormActions = ({ setState, params }) => {
  const { pendingFormActionsByFormId } = params;
  setState({ pendingFormActionsByFormId });
};

const handleUpdateDraftFormActions = ({ getState, setState, params }) => {
  const { formId, actions } = params;
  const { pendingFormActionsByFormId } = getState();
  setState({ pendingFormActionsByFormId: { ...pendingFormActionsByFormId, [formId]: actions } });
};

export const formActionHandlers = {
  handleCloseDeleteFormModal,
  handleClickStagesPanelFormName,
  handleDeleteForm,
  handleSelectForm,
  handleSortForms,
  handleOpenDeleteStageDrawerForm,
  handleInitFormActions,
  handleChangeIsFormEdited,
  handleChangeInactiveLeadAfterDays,
  handleSetDraftFormActions,
  handleUpdateDraftFormActions,
};
