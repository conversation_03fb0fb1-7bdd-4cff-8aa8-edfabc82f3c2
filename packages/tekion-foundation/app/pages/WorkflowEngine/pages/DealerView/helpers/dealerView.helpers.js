import _ from 'lodash';
import _map from 'lodash/map';
import _get from 'lodash/get';
import _set from 'lodash/set';
import _some from 'lodash/some';
import _find from 'lodash/find';
import _isNil from 'lodash/isNil';
import _sortBy from 'lodash/sortBy';
import _reduce from 'lodash/reduce';
import _filter from 'lodash/filter';
import _isArray from 'lodash/isArray';
import _isEmpty from 'lodash/isEmpty';
import _compact from 'lodash/compact';
import _groupBy from 'lodash/groupBy';
import _cloneDeep from 'lodash/cloneDeep';
import _mapValues from 'lodash/mapValues';
import _findIndex from 'lodash/findIndex';

import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import isStringEmpty from '@tekion/tekion-base/utils/isStringEmpty';
import { uuid } from '@tekion/tekion-components/src/utils';
import { ENTITY_DEF_IDS } from '@tekion/tekion-widgets/src/organisms/ruleEngineWidgets';
import { MILLISECONDS_IN_A_DAY } from '@tekion/tekion-widgets/src/organisms/ruleEngineWidgets/constants/ruleEngineForms.time';
import {
  ASSET_BASED_DELAY,
  FIXED_TIME_VALUE,
  IMMEDIATELY_VALUE,
  SPECIFIC_TIME,
} from '@tekion/tekion-widgets/src/organisms/ruleEngineWidgets/components/RuleEngineForms/createTask/createTask.generalConstants';
import FORM_FIELD_TYPES from '@tekion/tekion-widgets/src/organisms/ruleEngineWidgets/components/RuleEngineForms/formComponent/constants/formComponent.fieldTypes';
import EVENT_DELAY_FIELD_IDS from '@tekion/tekion-widgets/src/organisms/ruleEngineWidgets/components/RuleEngineForms/eventDelay/eventDelay.fieldTypes';
import { dataManager } from '@tekion/tekion-rule-engine';

import { DIRECTIONS, EVENT_HEADING, TITLE_HEADING, ACTION_DRAWER_MODES } from '../constants/dealerView.constants';
import { getFormsForStage, getJourneysFromDealerData, getStagesFromJourney } from '../readers/dealerView.reader';
import { EVENT_DELAY_DATA_FIELD_IDS } from '../../../components/modals/EventDelay/eventDelay.constants';

export const addFormToStage = (dealerWorkflowConfiguration, { stageId, addedForm }) => ({
  ...dealerWorkflowConfiguration,
  assetLifecycleJourney: _map(dealerWorkflowConfiguration.assetLifecycleJourney, journey => ({
    ...journey,
    assetLifecycleJourneyStages: _map(journey.assetLifecycleJourneyStages, stage =>
      stage.id === stageId
        ? {
            ...stage,
            formComponent: [...getFormsForStage(stage), addedForm],
          }
        : stage
    ),
  })),
});

export const addStageToJourney = (dealerWorkflowConfiguration, { journeyId, addedStage }) => ({
  ...dealerWorkflowConfiguration,
  assetLifecycleJourney: _map(dealerWorkflowConfiguration.assetLifecycleJourney, journey =>
    journey.id === journeyId
      ? {
          ...journey,
          assetLifecycleJourneyStages: [...getStagesFromJourney(journey), addedStage],
        }
      : journey
  ),
});

export const addStageIfNotExists = (dealerWorkflowConfiguration, stageName, formIds, selectedStageId) => {
  const stageId = selectedStageId || uuid();

  const formComponent = _map(formIds, id => ({ referencedFormId: id }));

  const newStage = {
    id: stageId,
    displayName: stageName,
    formComponent,
  };

  const updatedJourney = _map(getJourneysFromDealerData(dealerWorkflowConfiguration), journey => {
    const stages = getStagesFromJourney(journey);
    const existingStage = _find(stages, { id: stageId });

    return !existingStage ? { ...journey, assetLifecycleJourneyStages: [...stages, newStage] } : journey;
  });

  return { ...dealerWorkflowConfiguration, assetLifecycleJourney: updatedJourney };
};

export const deleteStageFromJourneys = (dealerWorkflowConfiguration, stageId) => {
  const updatedDealerData = _cloneDeep(dealerWorkflowConfiguration);

  updatedDealerData.assetLifecycleJourney = _map(getJourneysFromDealerData(updatedDealerData), journey => ({
    ...journey,
    assetLifecycleJourneyStages: _filter(getStagesFromJourney(journey), stage => stage.id !== stageId),
  }));

  return updatedDealerData;
};

export const getAssetLifecycleJourneyStages = dealerWorkflowConfiguration =>
  _get(dealerWorkflowConfiguration, 'assetLifecycleJourney[0].assetLifecycleJourneyStages', EMPTY_ARRAY);

export const updateDealerInputsStageConfig = ({
  dealerWorkflowConfiguration,
  selectedStageId,
  newFormOrder,
  newStageName,
}) => {
  const updatedDealerInputs = _cloneDeep(dealerWorkflowConfiguration);

  const journey = _get(updatedDealerInputs, 'assetLifecycleJourney[0]', EMPTY_ARRAY);
  const stages = getStagesFromJourney(journey);

  const targetStage = _find(stages, { id: selectedStageId });

  if (targetStage) {
    targetStage.formComponent = _compact(_map(newFormOrder, formId => formId && { referencedFormId: formId }));

    if (newStageName && newStageName !== targetStage.displayName) {
      targetStage.displayName = newStageName;
    }
  }

  return updatedDealerInputs;
};

export const getStageNameFromId = (dealerWorkflowConfiguration, stageId) => {
  const journey = _get(dealerWorkflowConfiguration, 'assetLifecycleJourney[0]', EMPTY_ARRAY);
  const stages = getStagesFromJourney(journey);

  const targetStage = _find(stages, { id: stageId });

  return _get(targetStage, 'displayName', '');
};

export const getFormNameFromId = (id, availableFormTemplates) => {
  if (isStringEmpty(id) || !_isArray(availableFormTemplates) || _isEmpty(availableFormTemplates)) return '';

  const form = _find(availableFormTemplates, { id }) || EMPTY_OBJECT;
  return !_isEmpty(form) || !_isNil(form) ? _get(form, 'formName', '') : '';
};

export const getFormOptions = availableFormTemplates => {
  if (!_isArray(availableFormTemplates)) return [];

  return _map(availableFormTemplates, form => ({
    label: form.formName,
    value: form.id,
    data: form,
  }));
};

export const getDayFromDelay = delayTime => Math.floor(delayTime / MILLISECONDS_IN_A_DAY);

export const getDayZeroHourFromDelay = delayTime => Math.floor(delayTime / (MILLISECONDS_IN_A_DAY / 24));

export const getDayZeroHourLabel = hour => {
  if (hour === 0) return __('Immediately');
  return __('After {{hour}} Hours', { hour });
};

export const getDelayTime = node => {
  const delayData = _get(node, [EVENT_DELAY_FIELD_IDS.EXECUTION_DELAY, 'userData', 'data'], EMPTY_OBJECT);
  const delayType = _get(delayData, EVENT_DELAY_DATA_FIELD_IDS.DELAY_TYPE, '');

  switch (delayType) {
    case SPECIFIC_TIME:
      return _get(
        delayData,
        [EVENT_DELAY_DATA_FIELD_IDS.EXECUTION_DELAY, EVENT_DELAY_DATA_FIELD_IDS.SPECIFIC_TIME_DELAY],
        0
      );
    case ASSET_BASED_DELAY:
      return _get(
        delayData,
        [EVENT_DELAY_DATA_FIELD_IDS.EXECUTION_DELAY, EVENT_DELAY_DATA_FIELD_IDS.ASSET_BASED_DELAY, 'rhs', 'value'],
        0
      );
    case FIXED_TIME_VALUE:
      return _get(
        delayData,
        [EVENT_DELAY_DATA_FIELD_IDS.EXECUTION_DELAY, EVENT_DELAY_DATA_FIELD_IDS.FIXED_TIME_DELAY],
        0
      );
    case IMMEDIATELY_VALUE:
    default:
      return 0;
  }
};

export const findNodeDataFromActions = ({ actions, nodeId, actionDrawerMode, entityId }) => {
  if (actionDrawerMode === ACTION_DRAWER_MODES.ADD_NODE) return dataManager.getTask(entityId);

  return _find(actions, { nodeId });
};

export const updateNodesInForm = (actions, formComponent) => {
  if (formComponent)
    return {
      ...formComponent,
      [FORM_FIELD_TYPES.ACTION_NODE_CONFIGS]: {
        [FORM_FIELD_TYPES.ALLOWED_ACTION_TYPES]: EMPTY_ARRAY,
        [FORM_FIELD_TYPES.ACTIONS]: actions,
      },
    };
  return {};
};

export const getFormConfigIdVsPermissionsMap = formsList =>
  _reduce(
    formsList,
    (acc, form) => {
      const formId = _get(form, 'id', '');
      const allowedActions = _get(
        form,
        [FORM_FIELD_TYPES.ACTION_NODE_CONFIGS, FORM_FIELD_TYPES.ALLOWED_ACTION_TYPES],
        EMPTY_ARRAY
      );

      const entityIdVsUnlockedFields = _reduce(
        _get(form, [FORM_FIELD_TYPES.ACTION_NODE_CONFIGS, FORM_FIELD_TYPES.ACTIONS], EMPTY_ARRAY),
        (result, action) => {
          const entityId = _get(action, ['uiMetadata', 'entityDefId'], '');
          return { ...result, [entityId]: _get(action, ['uiMetadata', 'unlockedFields'], EMPTY_ARRAY) };
        },
        {}
      );

      const defaultNodeData = _reduce(
        _get(form, [FORM_FIELD_TYPES.ACTION_NODE_CONFIGS, FORM_FIELD_TYPES.ACTIONS], EMPTY_ARRAY),
        (result, action) => {
          const entityId = _get(action, ['uiMetadata', 'entityDefId'], '');
          return { ...result, [entityId]: action };
        },
        {}
      );

      return { ...acc, [formId]: { allowedActions, entityIdVsUnlockedFields, defaultNodeData } };
    },
    EMPTY_OBJECT
  );

export const nodeList = workFlow => _get(workFlow, 'workflow.nodeList', EMPTY_OBJECT);

export const getDaysVsActionsMap = ({ formActions }) => {
  const dayVsActionsMap = _groupBy(formActions, node => getDayFromDelay(getDelayTime(node)));
  return _mapValues(dayVsActionsMap, nodes => _sortBy(nodes, getDelayTime));
};

export const ENTITY_DEF_VS_TITLE = {
  [ENTITY_DEF_IDS.CREATE_TASK]: { key: TITLE_HEADING, value: ['userData', 'data', 'body___title'] },
  [ENTITY_DEF_IDS.SEND_EMAIL]: { key: TITLE_HEADING, value: ['userData', 'data', 'body___subject'] },
  [ENTITY_DEF_IDS.SEND_TEXT]: { key: TITLE_HEADING, value: ['userData', 'data', 'actionTitle'] },
  [ENTITY_DEF_IDS.SEND_NOTIFICATION]: { key: EVENT_HEADING, value: ['uiMetadata', 'fields', 'body___eventType'] },
};

export const getOrderedActionsForForm = daysVsActionsMaps =>
  _(daysVsActionsMaps)
    .entries()
    .sortBy(([day]) => Number(day))
    .flatMap(([, actions]) => actions)
    .value();

export const moveStage = (dealerInputData, stageId, direction) => {
  const updatedDealerInput = _cloneDeep(dealerInputData);

  const assetLifecycleJourney = getJourneysFromDealerData(updatedDealerInput);
  const updatedJourney = _cloneDeep(assetLifecycleJourney);

  const journey = _find(updatedJourney, journey => _some(getStagesFromJourney(journey), { id: stageId }));
  if (!journey) return assetLifecycleJourney;

  const stages = getStagesFromJourney(journey);
  const index = _findIndex(stages, { id: stageId });
  const lastIndex = stages.length - 1;

  if (index === -1) return assetLifecycleJourney;

  const isFirst = index === 0;
  const isLast = index === lastIndex;

  if ((direction === DIRECTIONS.UP && isFirst) || (direction === DIRECTIONS.DOWN && isLast)) {
    return assetLifecycleJourney;
  }

  const targetIndex = direction === DIRECTIONS.UP ? index - 1 : index + 1;
  [stages[index], stages[targetIndex]] = [stages[targetIndex], stages[index]];

  _set(journey, 'assetLifecycleJourneyStages', stages);
  _set(updatedDealerInput, 'assetLifecycleJourney', updatedJourney);

  return updatedDealerInput;
};

export const getStageEdgeFlags = (dealerWorkflowConfiguration, stageId) => {
  let isFirstStage = false;
  let isLastStage = false;

  _some(_get(dealerWorkflowConfiguration, 'assetLifecycleJourney', []), journey => {
    const stages = _get(journey, 'assetLifecycleJourneyStages', []);
    const stageIndex = _findIndex(stages, { id: stageId });
    if (stageIndex !== -1) {
      isFirstStage = stageIndex === 0;
      isLastStage = stageIndex === stages.length - 1;
      return true;
    }
    return false;
  });

  return { isFirstStage, isLastStage };
};

export const getDuplicateActions = (dayActions, targetDay) =>
  _map(dayActions, action => ({
    ...action,
    nodeId: uuid(),
    [EVENT_DELAY_FIELD_IDS.EXECUTION_DELAY]: {
      ...action[EVENT_DELAY_FIELD_IDS.EXECUTION_DELAY],
      uiMetadata: {
        ...action[EVENT_DELAY_FIELD_IDS.EXECUTION_DELAY].uiMetadata,
        fields: {
          ...action[EVENT_DELAY_FIELD_IDS.EXECUTION_DELAY].uiMetadata.fields,
          [EVENT_DELAY_DATA_FIELD_IDS.DELAY_TYPE]: FIXED_TIME_VALUE,
          [EVENT_DELAY_DATA_FIELD_IDS.EXECUTION_DELAY]: {
            [EVENT_DELAY_DATA_FIELD_IDS.FIXED_TIME_DELAY]: {
              unit: 'DAYS',
              value: targetDay,
            },
          },
        },
      },
      userData: {
        ...action[EVENT_DELAY_FIELD_IDS.EXECUTION_DELAY].userData,
        data: {
          [EVENT_DELAY_DATA_FIELD_IDS.DELAY_TYPE]: FIXED_TIME_VALUE,
          [EVENT_DELAY_DATA_FIELD_IDS.EXECUTION_DELAY]: {
            [EVENT_DELAY_DATA_FIELD_IDS.FIXED_TIME_DELAY]: targetDay * MILLISECONDS_IN_A_DAY,
          },
        },
      },
    },
  }));
