import { MILLISECONDS_IN_A_DAY } from '@tekion/tekion-widgets/src/organisms/ruleEngineWidgets/constants/ruleEngineForms.time';
import { dataManager } from '@tekion/tekion-rule-engine';
import FORM_FIELD_TYPES from '@tekion/tekion-widgets/src/organisms/ruleEngineWidgets/components/RuleEngineForms/formComponent/constants/formComponent.fieldTypes';

import {
  moveStage,
  getStageEdgeFlags,
  addFormToStage,
  addStageToJourney,
  addStageIfNotExists,
  deleteStageFromJourneys,
  getAssetLifecycleJourneyStages,
  updateDealerInputsStageConfig,
  getStageNameFromId,
  getFormNameFromId,
  getFormOptions,
  getDayFromDelay,
  getDelayTime,
  getOrderedActionsForForm,
  getDayZeroHourFromDelay,
  getDayZeroHourLabel,
  findNodeDataFromActions,
  updateNodesInForm,
  getFormConfigIdVsPermissionsMap,
  getDaysVsActionsMap,
  getDuplicateActions,
  duplicateNodeInState,
  prepareBulkDuplicationParams,
  getAllNodeIds,
  removeNodesByIds,
  removeNodesFromPendingActions,
  moveNodesToDay,
} from '../dealerView.helpers';
import { DIRECTIONS } from '../../constants/dealerView.constants';

// Mock uuid function
jest.mock('@tekion/tekion-components/src/utils', () => ({
  uuid: jest.fn(() => 'mocked-uuid'),
}));
jest.mock('@tekion/tekion-widgets/src/organisms/ruleEngineWidgets/constants/ruleEngineForms.time', () => ({
  MILLISECONDS_IN_A_DAY: 86400000,
}));

describe('dealerWorkflowConfiguration utils', () => {
  describe('addFormToStage', () => {
    it('should add a form to the correct stage', () => {
      const dealerWorkflowConfiguration = {
        assetLifecycleJourney: [
          {
            assetLifecycleJourneyStages: [
              { id: 'stage1', formComponent: [] },
              { id: 'stage2', formComponent: [] },
            ],
          },
        ],
      };
      const addedForm = { id: 'form1' };
      const updated = addFormToStage(dealerWorkflowConfiguration, { stageId: 'stage1', addedForm });

      expect(updated.assetLifecycleJourney[0].assetLifecycleJourneyStages[0].formComponent).toContainEqual(addedForm);
      expect(updated.assetLifecycleJourney[0].assetLifecycleJourneyStages[1].formComponent).toHaveLength(0);
    });
  });

  describe('addStageToJourney', () => {
    it('should add a stage to the correct journey', () => {
      const dealerWorkflowConfiguration = {
        assetLifecycleJourney: [{ id: 'journey1', assetLifecycleJourneyStages: [] }],
      };
      const addedStage = { id: 'stage1', displayName: 'Stage 1' };
      const updated = addStageToJourney(dealerWorkflowConfiguration, { journeyId: 'journey1', addedStage });

      expect(updated.assetLifecycleJourney[0].assetLifecycleJourneyStages).toContainEqual(addedStage);
    });
  });

  describe('addStageIfNotExists', () => {
    it('should add a new stage if it does not exist', () => {
      const dealerWorkflowConfiguration = { assetLifecycleJourney: [{ assetLifecycleJourneyStages: [] }] };
      const formIds = ['form1', 'form2'];
      const updated = addStageIfNotExists(dealerWorkflowConfiguration, 'New Stage', formIds);

      const stages = updated.assetLifecycleJourney[0].assetLifecycleJourneyStages;
      expect(stages).toHaveLength(1);
      expect(stages[0].displayName).toBe('New Stage');
      expect(stages[0].formComponent.map(f => f.referencedFormId)).toEqual(formIds);
    });

    it('should not add a duplicate stage if stageId exists', () => {
      const existingStageId = 'stage1';
      const dealerWorkflowConfiguration = {
        assetLifecycleJourney: [
          { assetLifecycleJourneyStages: [{ id: existingStageId, displayName: 'Existing Stage' }] },
        ],
      };
      const updated = addStageIfNotExists(dealerWorkflowConfiguration, 'Another Stage', ['form1'], existingStageId);

      const stages = updated.assetLifecycleJourney[0].assetLifecycleJourneyStages;
      expect(stages).toHaveLength(1);
      expect(stages[0].displayName).toBe('Existing Stage');
    });
  });

  describe('deleteStageFromJourneys', () => {
    it('should delete the correct stage', () => {
      const dealerWorkflowConfiguration = {
        assetLifecycleJourney: [
          {
            assetLifecycleJourneyStages: [{ id: 'stage1' }, { id: 'stage2' }],
          },
        ],
      };
      const updated = deleteStageFromJourneys(dealerWorkflowConfiguration, 'stage1');

      const stages = updated.assetLifecycleJourney[0].assetLifecycleJourneyStages;
      expect(stages).toHaveLength(1);
      expect(stages[0].id).toBe('stage2');
    });
  });

  describe('getAssetLifecycleJourneyStages', () => {
    it('should return the stages array', () => {
      const dealerWorkflowConfiguration = {
        assetLifecycleJourney: [{ assetLifecycleJourneyStages: [{ id: 'stage1' }, { id: 'stage2' }] }],
      };
      const stages = getAssetLifecycleJourneyStages(dealerWorkflowConfiguration);

      expect(stages).toEqual([{ id: 'stage1' }, { id: 'stage2' }]);
    });

    it('should return empty array if not found', () => {
      expect(getAssetLifecycleJourneyStages({})).toEqual([]);
    });
  });

  describe('updateDealerInputsStageConfig', () => {
    it('should update the stage name and form order', () => {
      const dealerWorkflowConfiguration = {
        assetLifecycleJourney: [
          {
            assetLifecycleJourneyStages: [{ id: 'stage1', displayName: 'Old Name', formComponent: [] }],
          },
        ],
      };
      const availableFormTemplates = [
        { id: 'form1', formName: 'Form 1' },
        { id: 'form2', formName: 'Form 2' },
      ];
      const newFormOrder = ['form2', 'form1'];

      const updated = updateDealerInputsStageConfig({
        dealerWorkflowConfiguration,
        selectedStageId: 'stage1',
        newFormOrder,
        newStageName: 'New Name',
        availableFormTemplates,
      });

      const stage = updated.assetLifecycleJourney[0].assetLifecycleJourneyStages[0];
      expect(stage.displayName).toBe('New Name');
      expect(stage.formComponent.map(f => f.referencedFormId)).toEqual(newFormOrder);
    });
  });

  describe('getStageNameFromId', () => {
    it('should return the correct stage name', () => {
      const dealerWorkflowConfiguration = {
        assetLifecycleJourney: [
          {
            assetLifecycleJourneyStages: [
              { id: 'stage1', displayName: 'Stage 1' },
              { id: 'stage2', displayName: 'Stage 2' },
              { id: 'stage3', displayName: 'Stage 3' },
            ],
          },
        ],
      };
      const name = getStageNameFromId(dealerWorkflowConfiguration, 'stage1');
      expect(name).toBe('Stage 1');
    });

    it('should return empty string if not found', () => {
      const dealerWorkflowConfiguration = {
        assetLifecycleJourney: [
          {
            assetLifecycleJourneyStages: [
              { id: 'stage1', displayName: 'Stage 1' },
              { id: 'stage2', displayName: 'Stage 2' },
              { id: 'stage3', displayName: 'Stage 3' },
            ],
          },
        ],
      };

      expect(getStageNameFromId(dealerWorkflowConfiguration, 'stage4')).toBe('');
    });
  });

  describe('getFormNameFromId', () => {
    const availableFormTemplates = [
      { id: 'form1', formName: 'Form 1' },
      { id: 'form2', formName: 'Form 2' },
      { id: 'form3', formName: 'Form 3' },
    ];

    it('should return the correct form name', () => {
      expect(getFormNameFromId('form1', availableFormTemplates)).toBe('Form 1');
    });

    it('should return empty string for invalid id', () => {
      expect(getFormNameFromId('form4', availableFormTemplates)).toBe('');
    });
  });

  describe('getFormOptions', () => {
    it('should return mapped form options', () => {
      const availableFormTemplates = [{ id: 'form1', formName: 'Form 1' }];
      const options = getFormOptions(availableFormTemplates);

      expect(options).toEqual([{ label: 'Form 1', value: 'form1', data: availableFormTemplates[0] }]);
    });

    it('should return empty array for invalid input', () => {
      expect(getFormOptions(null)).toEqual([]);
    });
  });

  describe('getDayFromDelay', () => {
    it('should correctly calculate day from delay in ms', () => {
      const delayInMs = 2 * 24 * 60 * 60 * 1000; // 2 days
      expect(getDayFromDelay(delayInMs)).toBe(2);
    });
  });

  describe('getDelayTime', () => {
    it('should return correct delay time for different delay types', () => {
      const specificTimeNode = {
        executionDelay: {
          userData: {
            data: { body___delayType: 'SPECIFIC_TIME', body___executionDelay: { body___specificTimeDelay: 5000 } },
          },
        },
      };
      expect(getDelayTime(specificTimeNode)).toBe(5000);

      const assetBasedNode = {
        executionDelay: {
          userData: {
            data: {
              body___delayType: 'ASSET_BASED_DELAY',
              body___executionDelay: { body___assetBasedDelay: { rhs: { value: 10000 } } },
            },
          },
        },
      };
      expect(getDelayTime(assetBasedNode)).toBe(10000);

      const fixedTimeNode = {
        executionDelay: {
          userData: {
            data: { body___delayType: 'FIXED_TIME', body___executionDelay: { body___fixedTimeDelay: 15000 } },
          },
        },
      };
      expect(getDelayTime(fixedTimeNode)).toBe(15000);

      const immediatelyNode = { executionDelay: { userData: { data: { body___delayType: 'IMMEDIATELY' } } } };
      expect(getDelayTime(immediatelyNode)).toBe(0);
    });
  });

  describe('getOrderedActionsForForm', () => {
    it('should return actions sorted by day in ascending order', () => {
      const input = {
        2: [{ id: 4 }, { id: 5 }],
        0: [{ id: 1 }],
        1: [{ id: 2 }, { id: 3 }],
      };

      const expected = [{ id: 1 }, { id: 2 }, { id: 3 }, { id: 4 }, { id: 5 }];
      expect(getOrderedActionsForForm(input)).toEqual(expected);
    });

    it('should handle unordered string day keys and sort correctly', () => {
      const input = {
        10: [{ id: 3 }],
        1: [{ id: 1 }],
        5: [{ id: 2 }],
      };

      const expected = [{ id: 1 }, { id: 2 }, { id: 3 }];
      expect(getOrderedActionsForForm(input)).toEqual(expected);
    });

    it('should return an empty array for empty input object', () => {
      const input = {};
      const expected = [];
      expect(getOrderedActionsForForm(input)).toEqual(expected);
    });

    it('should skip empty action arrays but still sort valid ones', () => {
      const input = {
        1: [],
        2: [{ id: 1 }],
        3: [],
      };

      const expected = [{ id: 1 }];
      expect(getOrderedActionsForForm(input)).toEqual(expected);
    });
  });

  describe('getDayZeroHourFromDelay', () => {
    it('should return 0 for 0ms delay', () => {
      expect(getDayZeroHourFromDelay(0)).toBe(0);
    });

    it('should return 1 for 1 hour delay', () => {
      const oneHourInMs = MILLISECONDS_IN_A_DAY / 24;
      expect(getDayZeroHourFromDelay(oneHourInMs)).toBe(1);
    });

    it('should return 5 for 5 hour delay', () => {
      const fiveHoursInMs = 5 * (MILLISECONDS_IN_A_DAY / 24);
      expect(getDayZeroHourFromDelay(fiveHoursInMs)).toBe(5);
    });

    it('should floor the result for partial hours', () => {
      const twoAndHalfHourInMs = 2.5 * (MILLISECONDS_IN_A_DAY / 24);
      expect(getDayZeroHourFromDelay(twoAndHalfHourInMs)).toBe(2);
    });
  });

  describe('getDayZeroHourLabel', () => {
    it('should return "Immediately" if hour is 0', () => {
      expect(getDayZeroHourLabel(0)).toBe('Immediately');
    });

    it('should return "After 1 Hours" for hour = 1', () => {
      expect(getDayZeroHourLabel(1)).toBe('After 1 Hours');
    });

    it('should return "After 12 Hours" for hour = 12', () => {
      expect(getDayZeroHourLabel(12)).toBe('After 12 Hours');
    });
  });

  describe('findNodeDataFromActions', () => {
    const mockTask = { nodeId: 'n1', taskName: 'Test Task' };

    beforeEach(() => {
      jest.spyOn(dataManager, 'getTask').mockReturnValue(mockTask);
    });

    it('should return task from dataManager when mode is ADD_NODE', () => {
      const result = findNodeDataFromActions({
        actions: [],
        nodeId: 'n1',
        actionDrawerMode: 'ADD_NODE',
        entityId: 'e1',
      });
      expect(result).toEqual(mockTask);
      expect(dataManager.getTask).toHaveBeenCalledWith('e1');
    });

    it('should return matching node from actions when mode is not ADD_NODE', () => {
      const result = findNodeDataFromActions({
        actions: [{ nodeId: 'n1' }, { nodeId: 'n2' }],
        nodeId: 'n2',
        actionDrawerMode: 'EDIT_NODE',
        entityId: 'e1',
      });
      expect(result).toEqual({ nodeId: 'n2' });
    });
  });

  describe('updateNodesInForm', () => {
    it('should return updated formComponent with new actions', () => {
      const result = updateNodesInForm([{ nodeId: 1 }], { id: 'f1', allowedActionsAndActionNodesConfig: {} });
      expect(result).toEqual({
        id: 'f1',
        allowedActionsAndActionNodesConfig: {
          allowedActionTypes: [],
          actions: [{ nodeId: 1 }],
        },
      });
    });

    it('should return empty object if formComponent is falsy', () => {
      const result = updateNodesInForm([{ nodeId: 1 }], null);
      expect(result).toEqual({});
    });
  });

  describe('getFormConfigIdVsPermissionsMap', () => {
    it('should return map of formId to allowedActions, entityIdVsUnlockedFields, and defaultNodeData', () => {
      const forms = [
        {
          id: 'f1',
          [FORM_FIELD_TYPES.ACTION_NODE_CONFIGS]: {
            [FORM_FIELD_TYPES.ALLOWED_ACTION_TYPES]: ['t1'],
            [FORM_FIELD_TYPES.ACTIONS]: [
              {
                taskDefId: 't1',
                uiMetadata: {
                  entityDefId: 't1',
                  unlockedFields: ['field1'],
                },
              },
            ],
          },
        },
        {
          id: 'f2',
          [FORM_FIELD_TYPES.ACTION_NODE_CONFIGS]: {
            [FORM_FIELD_TYPES.ALLOWED_ACTION_TYPES]: ['t2', 't3'],
            [FORM_FIELD_TYPES.ACTIONS]: [
              {
                taskDefId: 't2',
                uiMetadata: {
                  entityDefId: 't2',
                  unlockedFields: ['field2', 'field3'],
                },
              },
              {
                taskDefId: 't3',
                uiMetadata: {
                  entityDefId: 't3',
                  unlockedFields: ['field4'],
                },
              },
            ],
          },
        },
      ];

      const result = getFormConfigIdVsPermissionsMap(forms);

      expect(result).toEqual({
        f1: {
          allowedActions: ['t1'],
          entityIdVsUnlockedFields: {
            t1: ['field1'],
          },
          defaultNodeData: {
            t1: {
              taskDefId: 't1',
              uiMetadata: {
                entityDefId: 't1',
                unlockedFields: ['field1'],
              },
            },
          },
        },
        f2: {
          allowedActions: ['t2', 't3'],
          entityIdVsUnlockedFields: {
            t2: ['field2', 'field3'],
            t3: ['field4'],
          },
          defaultNodeData: {
            t2: {
              taskDefId: 't2',
              uiMetadata: {
                entityDefId: 't2',
                unlockedFields: ['field2', 'field3'],
              },
            },
            t3: {
              taskDefId: 't3',
              uiMetadata: {
                entityDefId: 't3',
                unlockedFields: ['field4'],
              },
            },
          },
        },
      });
    });

    it('should default to empty arrays and objects if data is missing', () => {
      const forms = [{ id: 'f1' }];

      const result = getFormConfigIdVsPermissionsMap(forms);
      expect(result).toEqual({
        f1: {
          allowedActions: [],
          entityIdVsUnlockedFields: {},
          defaultNodeData: {},
        },
      });
    });

    it('should handle empty forms array', () => {
      const forms = [];
      const result = getFormConfigIdVsPermissionsMap(forms);
      expect(result).toEqual({});
    });

    it('should handle null/undefined forms', () => {
      expect(getFormConfigIdVsPermissionsMap(null)).toEqual({});
      expect(getFormConfigIdVsPermissionsMap(undefined)).toEqual({});
    });

    it('should handle forms with missing allowedActionsAndActionNodesConfig', () => {
      const forms = [
        { id: 'f1' },
        { id: 'f2', allowedActionsAndActionNodesConfig: null },
        { id: 'f3', allowedActionsAndActionNodesConfig: undefined },
      ];
      const result = getFormConfigIdVsPermissionsMap(forms);
      expect(result).toEqual({
        f1: {
          allowedActions: [],
          entityIdVsUnlockedFields: {},
          defaultNodeData: {},
        },
        f2: {
          allowedActions: [],
          entityIdVsUnlockedFields: {},
          defaultNodeData: {},
        },
        f3: {
          allowedActions: [],
          entityIdVsUnlockedFields: {},
          defaultNodeData: {},
        },
      });
    });

    it('should handle forms with missing allowedTaskDefList', () => {
      const forms = [
        {
          id: 'f1',
          allowedActionsAndActionNodesConfig: {
            actions: [
              {
                taskDefId: 't1',
                uiMetadata: { entityDefId: 't1', unlockedFields: ['field1'] },
              },
            ],
          },
        },
      ];
      const result = getFormConfigIdVsPermissionsMap(forms);
      expect(result).toEqual({
        f1: {
          allowedActions: [],
          entityIdVsUnlockedFields: {
            t1: ['field1'],
          },
          defaultNodeData: {
            t1: {
              taskDefId: 't1',
              uiMetadata: { entityDefId: 't1', unlockedFields: ['field1'] },
            },
          },
        },
      });
    });
  });

  describe('getDaysVsActionsMap', () => {
    it('should group actions by day and sort by delayTime', () => {
      const action1 = {
        id: 1,
        executionDelay: {
          userData: {
            data: {
              body___delayType: 'SPECIFIC_TIME',
              body___executionDelay: { body___specificTimeDelay: 43200000 },
            },
          },
        },
      };
      const action2 = {
        id: 2,
        executionDelay: { userData: { data: { body___delayType: 'IMMEDIATELY' } } },
      };
      const action3 = {
        id: 3,
        executionDelay: {
          userData: {
            data: { body___delayType: 'FIXED_TIME', body___executionDelay: { body___fixedTimeDelay: 86400000 } },
          },
        },
      };

      const actions = [action1, action2, action3];

      const result = getDaysVsActionsMap({ formActions: actions });

      expect(result).toEqual({
        0: [action2, action1],
        1: [action3],
      });
    });
  });

  describe('moveStage', () => {
    const mockDealerInput = {
      assetLifecycleJourney: [
        {
          id: 'journey-1',
          assetLifecycleJourneyStages: [{ id: 'stage-1' }, { id: 'stage-2' }, { id: 'stage-3' }],
        },
      ],
    };

    it('should move stage up when not first', () => {
      const result = moveStage(mockDealerInput, 'stage-2', DIRECTIONS.UP);
      const updatedStages = result.assetLifecycleJourney[0].assetLifecycleJourneyStages;
      expect(updatedStages[0].id).toBe('stage-2');
      expect(updatedStages[1].id).toBe('stage-1');
      expect(updatedStages[2].id).toBe('stage-3');
    });

    it('should move stage down when not last', () => {
      const result = moveStage(mockDealerInput, 'stage-2', DIRECTIONS.DOWN);
      const updatedStages = result.assetLifecycleJourney[0].assetLifecycleJourneyStages;
      expect(updatedStages[0].id).toBe('stage-1');
      expect(updatedStages[1].id).toBe('stage-3');
      expect(updatedStages[2].id).toBe('stage-2');
    });

    it('should return original journey if stage is already first and direction is UP', () => {
      const result = moveStage(mockDealerInput, 'stage-1', DIRECTIONS.UP);
      expect(result).toEqual(mockDealerInput.assetLifecycleJourney);
    });

    it('should return original journey if stage is already last and direction is DOWN', () => {
      const result = moveStage(mockDealerInput, 'stage-3', DIRECTIONS.DOWN);
      expect(result).toEqual(mockDealerInput.assetLifecycleJourney);
    });

    it('should return original journey if stageId not found', () => {
      const result = moveStage(mockDealerInput, 'stage-unknown', DIRECTIONS.UP);
      expect(result).toEqual(mockDealerInput.assetLifecycleJourney);
    });
  });

  describe('getStageEdgeFlags', () => {
    const dealerWorkflowConfiguration = {
      assetLifecycleJourney: [
        {
          assetLifecycleJourneyStages: [{ id: 'stage-1' }, { id: 'stage-2' }, { id: 'stage-3' }],
        },
      ],
    };

    it('should return isFirstStage=true and isLastStage=false for first stage', () => {
      const result = getStageEdgeFlags(dealerWorkflowConfiguration, 'stage-1');
      expect(result).toEqual({ isFirstStage: true, isLastStage: false });
    });

    it('should return isFirstStage=false and isLastStage=true for last stage', () => {
      const result = getStageEdgeFlags(dealerWorkflowConfiguration, 'stage-3');
      expect(result).toEqual({ isFirstStage: false, isLastStage: true });
    });

    it('should return isFirstStage=false and isLastStage=false for middle stage', () => {
      const result = getStageEdgeFlags(dealerWorkflowConfiguration, 'stage-2');
      expect(result).toEqual({ isFirstStage: false, isLastStage: false });
    });

    it('should return false for both flags if stage not found', () => {
      const result = getStageEdgeFlags(dealerWorkflowConfiguration, 'stage-unknown');
      expect(result).toEqual({ isFirstStage: false, isLastStage: false });
    });
  });

  describe('getDuplicateActions', () => {
    const EVENT_DELAY_FIELD_IDS = {
      EXECUTION_DELAY: 'executionDelay',
    };

    const EVENT_DELAY_DATA_FIELD_IDS = {
      DELAY_TYPE: 'body___delayType',
      EXECUTION_DELAY: 'body___executionDelay',
      FIXED_TIME_DELAY: 'body___fixedTimeDelay',
    };

    const FIXED_TIME_VALUE = 'FIXED_TIME';

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should duplicate actions with new nodeIds', () => {
      const dayActions = [
        {
          nodeId: 'original-node-1',
          name: 'Action 1',
          [EVENT_DELAY_FIELD_IDS.EXECUTION_DELAY]: {
            uiMetadata: {
              fields: {
                [EVENT_DELAY_DATA_FIELD_IDS.DELAY_TYPE]: 'IMMEDIATELY',
              },
            },
            userData: {
              data: {
                [EVENT_DELAY_DATA_FIELD_IDS.DELAY_TYPE]: 'IMMEDIATELY',
              },
            },
          },
        },
        {
          nodeId: 'original-node-2',
          name: 'Action 2',
          [EVENT_DELAY_FIELD_IDS.EXECUTION_DELAY]: {
            uiMetadata: {
              fields: {
                [EVENT_DELAY_DATA_FIELD_IDS.DELAY_TYPE]: 'SPECIFIC_TIME',
              },
            },
            userData: {
              data: {
                [EVENT_DELAY_DATA_FIELD_IDS.DELAY_TYPE]: 'SPECIFIC_TIME',
              },
            },
          },
        },
      ];

      const targetDay = 3;
      const result = getDuplicateActions(dayActions, targetDay);

      // Should return the same number of actions
      expect(result).toHaveLength(dayActions.length);

      // Each action should have a new nodeId
      result.forEach((action, index) => {
        expect(action.nodeId).not.toBe(dayActions[index].nodeId);
      });

      // Should preserve original action properties
      expect(result[0].name).toBe('Action 1');
      expect(result[1].name).toBe('Action 2');
    });

    it('should set delay type to FIXED_TIME for all duplicated actions', () => {
      const dayActions = [
        {
          nodeId: 'original-node-1',
          [EVENT_DELAY_FIELD_IDS.EXECUTION_DELAY]: {
            uiMetadata: {
              fields: {
                [EVENT_DELAY_DATA_FIELD_IDS.DELAY_TYPE]: 'IMMEDIATELY',
              },
            },
            userData: {
              data: {
                [EVENT_DELAY_DATA_FIELD_IDS.DELAY_TYPE]: 'IMMEDIATELY',
              },
            },
          },
        },
        {
          nodeId: 'original-node-2',
          [EVENT_DELAY_FIELD_IDS.EXECUTION_DELAY]: {
            uiMetadata: {
              fields: {
                [EVENT_DELAY_DATA_FIELD_IDS.DELAY_TYPE]: 'SPECIFIC_TIME',
              },
            },
            userData: {
              data: {
                [EVENT_DELAY_DATA_FIELD_IDS.DELAY_TYPE]: 'SPECIFIC_TIME',
              },
            },
          },
        },
      ];

      const targetDay = 5;
      const result = getDuplicateActions(dayActions, targetDay);

      // All actions should have FIXED_TIME delay type
      result.forEach(action => {
        expect(
          action[EVENT_DELAY_FIELD_IDS.EXECUTION_DELAY].uiMetadata.fields[EVENT_DELAY_DATA_FIELD_IDS.DELAY_TYPE]
        ).toBe(FIXED_TIME_VALUE);
        expect(action[EVENT_DELAY_FIELD_IDS.EXECUTION_DELAY].userData.data[EVENT_DELAY_DATA_FIELD_IDS.DELAY_TYPE]).toBe(
          FIXED_TIME_VALUE
        );
      });
    });

    it('should set fixed time delay to targetDay * MILLISECONDS_IN_A_DAY', () => {
      const dayActions = [
        {
          nodeId: 'original-node-1',
          [EVENT_DELAY_FIELD_IDS.EXECUTION_DELAY]: {
            uiMetadata: { fields: {} },
            userData: { data: {} },
          },
        },
      ];

      const targetDay = 7;
      const result = getDuplicateActions(dayActions, targetDay);

      // Should set the correct delay value in milliseconds
      expect(
        result[0][EVENT_DELAY_FIELD_IDS.EXECUTION_DELAY].userData.data[EVENT_DELAY_DATA_FIELD_IDS.EXECUTION_DELAY][
          EVENT_DELAY_DATA_FIELD_IDS.FIXED_TIME_DELAY
        ]
      ).toBe(targetDay * MILLISECONDS_IN_A_DAY);
    });

    it('should set fixed time delay with unit DAYS in uiMetadata', () => {
      const dayActions = [
        {
          nodeId: 'original-node-1',
          [EVENT_DELAY_FIELD_IDS.EXECUTION_DELAY]: {
            uiMetadata: { fields: {} },
            userData: { data: {} },
          },
        },
      ];

      const targetDay = 2;
      const result = getDuplicateActions(dayActions, targetDay);

      // Should set the correct unit and value in uiMetadata
      const fixedTimeDelay =
        result[0][EVENT_DELAY_FIELD_IDS.EXECUTION_DELAY].uiMetadata.fields[EVENT_DELAY_DATA_FIELD_IDS.EXECUTION_DELAY][
          EVENT_DELAY_DATA_FIELD_IDS.FIXED_TIME_DELAY
        ];
      expect(fixedTimeDelay.unit).toBe('DAYS');
      expect(fixedTimeDelay.value).toBe(targetDay);
    });

    it('should preserve other properties in executionDelay', () => {
      const dayActions = [
        {
          nodeId: 'original-node-1',
          [EVENT_DELAY_FIELD_IDS.EXECUTION_DELAY]: {
            uiMetadata: {
              fields: {},
              otherProp: 'test',
              anotherProp: 123,
            },
            userData: {
              data: {},
              customProp: true,
            },
          },
        },
      ];

      const targetDay = 1;
      const result = getDuplicateActions(dayActions, targetDay);

      // Should preserve other properties
      expect(result[0][EVENT_DELAY_FIELD_IDS.EXECUTION_DELAY].uiMetadata.otherProp).toBe('test');
      expect(result[0][EVENT_DELAY_FIELD_IDS.EXECUTION_DELAY].uiMetadata.anotherProp).toBe(123);
      expect(result[0][EVENT_DELAY_FIELD_IDS.EXECUTION_DELAY].userData.customProp).toBe(true);
    });

    it('should handle empty dayActions array', () => {
      const result = getDuplicateActions([], 1);
      expect(result).toEqual([]);
    });

    it('should handle targetDay = 0', () => {
      const dayActions = [
        {
          nodeId: 'original-node-1',
          [EVENT_DELAY_FIELD_IDS.EXECUTION_DELAY]: {
            uiMetadata: { fields: {} },
            userData: { data: {} },
          },
        },
      ];

      const targetDay = 0;
      const result = getDuplicateActions(dayActions, targetDay);

      // Should set the delay to 0 milliseconds
      expect(
        result[0][EVENT_DELAY_FIELD_IDS.EXECUTION_DELAY].userData.data[EVENT_DELAY_DATA_FIELD_IDS.EXECUTION_DELAY][
          EVENT_DELAY_DATA_FIELD_IDS.FIXED_TIME_DELAY
        ]
      ).toBe(0);

      // Should set the value to 0 in uiMetadata
      const fixedTimeDelay =
        result[0][EVENT_DELAY_FIELD_IDS.EXECUTION_DELAY].uiMetadata.fields[EVENT_DELAY_DATA_FIELD_IDS.EXECUTION_DELAY][
          EVENT_DELAY_DATA_FIELD_IDS.FIXED_TIME_DELAY
        ];
      expect(fixedTimeDelay.value).toBe(0);
    });
  });

  // Tests for optimized helper functions and utilities
  describe('Node Operation Helper Functions', () => {
    const mockActionScheduleByDay = {
      'form-1': {
        0: [
          { nodeId: 'node-1', name: 'Action 1' },
          { nodeId: 'node-2', name: 'Action 2' },
        ],
        1: [{ nodeId: 'node-3', name: 'Action 3' }],
      },
      'form-2': {
        0: [{ nodeId: 'node-4', name: 'Action 4' }],
        2: [
          { nodeId: 'node-5', name: 'Action 5' },
          { nodeId: 'node-6', name: 'Action 6' },
        ],
      },
    };

    const mockPendingFormActionsByFormId = {
      'form-1': [
        { nodeId: 'node-1', name: 'Pending Action 1' },
        { nodeId: 'node-2', name: 'Pending Action 2' },
      ],
      'form-2': [{ nodeId: 'node-4', name: 'Pending Action 4' }],
    };

    describe('getAllNodeIds', () => {
      it('should return all node IDs from actionScheduleByDay', () => {
        const result = getAllNodeIds(mockActionScheduleByDay);
        expect(result).toEqual(['node-1', 'node-2', 'node-3', 'node-4', 'node-5', 'node-6']);
      });

      it('should return empty array for empty actionScheduleByDay', () => {
        const result = getAllNodeIds({});
        expect(result).toEqual([]);
      });

      it('should handle undefined actionScheduleByDay', () => {
        const result = getAllNodeIds();
        expect(result).toEqual([]);
      });

      it('should skip nodes without nodeId', () => {
        const actionScheduleWithMissingIds = {
          'form-1': {
            0: [
              { nodeId: 'node-1', name: 'Action 1' },
              { name: 'Action without ID' },
              { nodeId: 'node-2', name: 'Action 2' },
            ],
          },
        };
        const result = getAllNodeIds(actionScheduleWithMissingIds);
        expect(result).toEqual(['node-1', 'node-2']);
      });
    });

    describe('removeNodesByIds', () => {
      it('should remove specified nodes from actionScheduleByDay', () => {
        const nodeIdsToRemove = ['node-2', 'node-5'];
        const result = removeNodesByIds(mockActionScheduleByDay, nodeIdsToRemove);

        expect(result['form-1']['0']).toEqual([{ nodeId: 'node-1', name: 'Action 1' }]);
        expect(result['form-1']['1']).toEqual([{ nodeId: 'node-3', name: 'Action 3' }]);
        expect(result['form-2']['0']).toEqual([{ nodeId: 'node-4', name: 'Action 4' }]);
        expect(result['form-2']['2']).toEqual([{ nodeId: 'node-6', name: 'Action 6' }]);
      });

      it('should return empty arrays when all nodes are removed', () => {
        const nodeIdsToRemove = ['node-1', 'node-2', 'node-3', 'node-4', 'node-5', 'node-6'];
        const result = removeNodesByIds(mockActionScheduleByDay, nodeIdsToRemove);

        expect(result['form-1']['0']).toEqual([]);
        expect(result['form-1']['1']).toEqual([]);
        expect(result['form-2']['0']).toEqual([]);
        expect(result['form-2']['2']).toEqual([]);
      });

      it('should handle empty nodeIdsToRemove array', () => {
        const result = removeNodesByIds(mockActionScheduleByDay, []);
        expect(result).toEqual(mockActionScheduleByDay);
      });

      it('should handle non-existent node IDs', () => {
        const nodeIdsToRemove = ['non-existent-1', 'non-existent-2'];
        const result = removeNodesByIds(mockActionScheduleByDay, nodeIdsToRemove);
        expect(result).toEqual(mockActionScheduleByDay);
      });
    });

    describe('removeNodesFromPendingActions', () => {
      it('should remove specified nodes from pendingFormActionsByFormId', () => {
        const nodeIdsToRemove = ['node-1', 'node-4'];
        const result = removeNodesFromPendingActions(mockPendingFormActionsByFormId, nodeIdsToRemove);

        expect(result['form-1']).toEqual([{ nodeId: 'node-2', name: 'Pending Action 2' }]);
        expect(result['form-2']).toEqual([]);
      });

      it('should return empty arrays when all nodes are removed', () => {
        const nodeIdsToRemove = ['node-1', 'node-2', 'node-4'];
        const result = removeNodesFromPendingActions(mockPendingFormActionsByFormId, nodeIdsToRemove);

        expect(result['form-1']).toEqual([]);
        expect(result['form-2']).toEqual([]);
      });

      it('should handle empty nodeIdsToRemove array', () => {
        const result = removeNodesFromPendingActions(mockPendingFormActionsByFormId, []);
        expect(result).toEqual(mockPendingFormActionsByFormId);
      });

      it('should handle non-existent node IDs', () => {
        const nodeIdsToRemove = ['non-existent-1', 'non-existent-2'];
        const result = removeNodesFromPendingActions(mockPendingFormActionsByFormId, nodeIdsToRemove);
        expect(result).toEqual(mockPendingFormActionsByFormId);
      });
    });

    describe('prepareBulkDuplicationParams', () => {
      it('should return node parameters for selected nodes', () => {
        const selectedNodeIds = ['node-1', 'node-5'];
        const result = prepareBulkDuplicationParams(selectedNodeIds, mockActionScheduleByDay);

        expect(result).toHaveLength(2);
        expect(result[0]).toEqual({
          day: '0',
          formId: 'form-1',
          index: 0,
          nodeData: { nodeId: 'node-1', name: 'Action 1' },
        });
        expect(result[1]).toEqual({
          day: '2',
          formId: 'form-2',
          index: 0,
          nodeData: { nodeId: 'node-5', name: 'Action 5' },
        });
      });

      it('should return empty array for empty selectedNodeIds', () => {
        const result = prepareBulkDuplicationParams([], mockActionScheduleByDay);
        expect(result).toEqual([]);
      });

      it('should return empty array for non-existent node IDs', () => {
        const selectedNodeIds = ['non-existent-1', 'non-existent-2'];
        const result = prepareBulkDuplicationParams(selectedNodeIds, mockActionScheduleByDay);
        expect(result).toEqual([]);
      });

      it('should handle undefined parameters', () => {
        const result = prepareBulkDuplicationParams();
        expect(result).toEqual([]);
      });

      it('should include correct index for nodes in same day', () => {
        const selectedNodeIds = ['node-1', 'node-2'];
        const result = prepareBulkDuplicationParams(selectedNodeIds, mockActionScheduleByDay);

        expect(result).toHaveLength(2);
        expect(result[0].index).toBe(0);
        expect(result[1].index).toBe(1);
      });
    });

    describe('moveNodesToDay', () => {
      it('should move specified nodes to target day', () => {
        const nodeIdsToMove = ['node-1', 'node-5'];
        const targetDay = '3';
        const result = moveNodesToDay(mockActionScheduleByDay, nodeIdsToMove, targetDay);

        // Original positions should be empty
        expect(result['form-1']['0']).toEqual([{ nodeId: 'node-2', name: 'Action 2' }]);
        expect(result['form-2']['2']).toEqual([{ nodeId: 'node-6', name: 'Action 6' }]);

        // Target day should contain moved nodes
        expect(result['form-1']['3']).toEqual([{ nodeId: 'node-1', name: 'Action 1' }]);
        expect(result['form-2']['3']).toEqual([{ nodeId: 'node-5', name: 'Action 5' }]);
      });

      it('should create target day arrays if they do not exist', () => {
        const nodeIdsToMove = ['node-1'];
        const targetDay = '10';
        const result = moveNodesToDay(mockActionScheduleByDay, nodeIdsToMove, targetDay);

        expect(result['form-1']['10']).toEqual([{ nodeId: 'node-1', name: 'Action 1' }]);
      });

      it('should handle empty nodeIdsToMove array', () => {
        const result = moveNodesToDay(mockActionScheduleByDay, [], '3');
        expect(result).toEqual(mockActionScheduleByDay);
      });

      it('should handle non-existent node IDs', () => {
        const nodeIdsToMove = ['non-existent-1', 'non-existent-2'];
        const targetDay = '3';
        const result = moveNodesToDay(mockActionScheduleByDay, nodeIdsToMove, targetDay);
        expect(result).toEqual(mockActionScheduleByDay);
      });

      it('should preserve other nodes in target day', () => {
        const actionScheduleWithTargetDay = {
          ...mockActionScheduleByDay,
          'form-1': {
            ...mockActionScheduleByDay['form-1'],
            3: [{ nodeId: 'existing-node', name: 'Existing Action' }],
          },
        };

        const nodeIdsToMove = ['node-1'];
        const targetDay = '3';
        const result = moveNodesToDay(actionScheduleWithTargetDay, nodeIdsToMove, targetDay);

        expect(result['form-1']['3']).toEqual([
          { nodeId: 'existing-node', name: 'Existing Action' },
          { nodeId: 'node-1', name: 'Action 1' },
        ]);
      });
    });

    describe('duplicateNodeInState', () => {
      const mockState = {
        actionScheduleByDay: mockActionScheduleByDay,
        pendingFormActionsByFormId: mockPendingFormActionsByFormId,
      };

      const nodeParams = {
        day: '0',
        formId: 'form-1',
        index: 0,
        nodeData: { nodeId: 'node-1', name: 'Action 1', taskType: 'CALL' },
      };

      beforeEach(() => {
        jest.clearAllMocks();
      });

      it('should duplicate node and insert after specified index', () => {
        const result = duplicateNodeInState(nodeParams, mockState);

        // Should have new node ID
        expect(result.newInsertedNode.nodeId).toBe('mocked-uuid');
        expect(result.newInsertedNode.name).toBe('Action 1');
        expect(result.newInsertedNode.taskType).toBe('CALL');

        // Should insert after index 0 (position 1)
        const updatedNodes = result.updatedActionScheduleByDay['form-1']['0'];
        expect(updatedNodes).toHaveLength(3);
        expect(updatedNodes[0]).toEqual({ nodeId: 'node-1', name: 'Action 1' });
        expect(updatedNodes[1]).toEqual({ nodeId: 'mocked-uuid', name: 'Action 1', taskType: 'CALL' });
        expect(updatedNodes[2]).toEqual({ nodeId: 'node-2', name: 'Action 2' });
      });

      it('should add duplicated node to pending actions', () => {
        const result = duplicateNodeInState(nodeParams, mockState);

        const updatedPendingActions = result.updatedPendingFormActionsByFormId['form-1'];
        expect(updatedPendingActions).toHaveLength(3);
        expect(updatedPendingActions[2]).toEqual({ nodeId: 'mocked-uuid', name: 'Action 1', taskType: 'CALL' });
      });

      it('should handle form with no existing pending actions', () => {
        const stateWithNoPending = {
          actionScheduleByDay: mockActionScheduleByDay,
          pendingFormActionsByFormId: {},
        };

        const result = duplicateNodeInState(nodeParams, stateWithNoPending);

        const updatedPendingActions = result.updatedPendingFormActionsByFormId['form-1'];
        expect(updatedPendingActions).toEqual([{ nodeId: 'mocked-uuid', name: 'Action 1', taskType: 'CALL' }]);
      });

      it('should preserve other forms and days unchanged', () => {
        const result = duplicateNodeInState(nodeParams, mockState);

        // Other forms should remain unchanged
        expect(result.updatedActionScheduleByDay['form-2']).toEqual(mockActionScheduleByDay['form-2']);
        expect(result.updatedPendingFormActionsByFormId['form-2']).toEqual(mockPendingFormActionsByFormId['form-2']);

        // Other days in same form should remain unchanged
        expect(result.updatedActionScheduleByDay['form-1']['1']).toEqual(mockActionScheduleByDay['form-1']['1']);
      });
    });
  });
});
