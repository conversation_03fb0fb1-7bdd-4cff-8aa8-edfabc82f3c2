import _get from 'lodash/get';
import _filter from 'lodash/filter';
import _isEmpty from 'lodash/isEmpty';
import _isEqual from 'lodash/isEqual';
import _has from 'lodash/has';
import _omit from 'lodash/omit';
import _xor from 'lodash/xor';
import _forEach from 'lodash/forEach';

import { uuid } from '@tekion/tekion-components/src/utils';
import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import { dataManager, getNewNode, getBranchingKeyFromNodeData } from '@tekion/tekion-rule-engine';
import { getConditionChild } from '@tekion/tekion-rule-engine/src/utils/core/helpers';
import FORM_FIELD_TYPES from '@tekion/tekion-widgets/src/organisms/ruleEngineWidgets/components/RuleEngineForms/formComponent/constants/formComponent.fieldTypes';

import { getLabel } from 'pages/WorkflowEngine/helpers/processAutomation.activityUtils';
import { getNodeStructureFromTask as getNodeStructure } from 'shared/ruleEngine/ruleEngine.utils';
import { fetchUiMetadataAndSetTemplateOption } from 'pages/WorkflowEngine/pages/workflowEditor/workflowEditor.helper';

import {
  getDelayTime,
  updateNodesInForm,
  getDaysVsActionsMap,
  getDuplicateActions,
  findNodeDataFromActions,
  getOrderedActionsForForm,
  duplicateNodeInState,
  prepareBulkDuplicationParams,
  getAllNodeIds,
  removeNodesByIds,
  removeNodesFromPendingActions,
  moveNodesToDay,
} from '../dealerView.helpers';
import { updateFormComponent } from '../../actions/dealerView.actions';
import { ACTION_DRAWER_MODES, DEFAULT_CENTRAL_MODAL_STATE } from '../../constants/dealerView.constants';
import { MODAL_TYPES } from '../../components/organisms/CentralModal/constants/centralModal.constants';

const handleToggleDeleteActionModal = ({ getState, setState, params = EMPTY_OBJECT }) => {
  const { centralModal, pendingFormActionsByFormId } = getState();
  const { day = '', index = '', nodeData = EMPTY_OBJECT, nodeId = '', formId = '' } = params;

  if (!centralModal.isVisible || centralModal.modalType !== MODAL_TYPES.DELETE_ACTION) {
    setState({
      selectedNodeDay: day,
      selectedNodeIndex: index,
      selectedNodeData: nodeData,
      selectedNodeId: nodeId,
      centralModal: {
        isVisible: true,
        modalType: MODAL_TYPES.DELETE_ACTION,
        modalData: { day, index, nodeData, nodeId, formId, pendingFormActionsByFormId },
        isLoading: false,
      },
    });
  } else {
    setState({
      selectedNodeDay: '',
      selectedNodeIndex: null,
      selectedNodeId: '',
      selectedNodeData: EMPTY_OBJECT,
      centralModal: DEFAULT_CENTRAL_MODAL_STATE,
    });
  }
};

const handleSortActionNodes = ({ setState, getState, params }) => {
  const {
    formId,
    oldIndex,
    newIndex,
    collection, // i.e. the day, example: 0, 1, 2, etc
  } = params;
  const { actionScheduleByDay } = getState();

  const dayVsActionsMap = _get(actionScheduleByDay, formId, EMPTY_ARRAY);
  if (getDelayTime(dayVsActionsMap[collection][oldIndex]) === getDelayTime(dayVsActionsMap[collection][newIndex])) {
    const currentActions = _get(dayVsActionsMap, collection, EMPTY_ARRAY);
    const reorderedActions = [...currentActions];

    const [movedNode] = reorderedActions.splice(oldIndex, 1);
    reorderedActions.splice(newIndex, 0, movedNode);

    setState({
      isSorted: oldIndex !== newIndex,
      actionScheduleByDay: {
        ...actionScheduleByDay,
        [formId]: { ...actionScheduleByDay[formId], [collection]: reorderedActions },
      },
    });
  }
};

const handleNodeDuplication = ({ getState, params, setState }) => {
  const { day, formId, index, nodeData } = params;
  const { actionScheduleByDay, pendingFormActionsByFormId } = getState();

  const { updatedActionScheduleByDay, updatedPendingFormActionsByFormId } = duplicateNodeInState(
    { day, formId, index, nodeData },
    { actionScheduleByDay, pendingFormActionsByFormId }
  );

  setState({
    actionScheduleByDay: updatedActionScheduleByDay,
    pendingFormActionsByFormId: updatedPendingFormActionsByFormId,
  });
};

const handleBulkNodeDuplication = ({ getState, setState }) => {
  const { actionScheduleByDay, selectedNodeIds, pendingFormActionsByFormId } = getState();
  const nodeParams = prepareBulkDuplicationParams(selectedNodeIds, actionScheduleByDay);

  let currentState = {
    actionScheduleByDay: { ...actionScheduleByDay },
    pendingFormActionsByFormId: { ...pendingFormActionsByFormId },
  };

  _forEach(nodeParams, nodeParam => {
    const { updatedActionScheduleByDay, updatedPendingFormActionsByFormId } = duplicateNodeInState(
      nodeParam,
      currentState
    );

    currentState = {
      actionScheduleByDay: updatedActionScheduleByDay,
      pendingFormActionsByFormId: updatedPendingFormActionsByFormId,
    };
  });

  setState({
    actionScheduleByDay: currentState.actionScheduleByDay,
    pendingFormActionsByFormId: currentState.pendingFormActionsByFormId,
    selectedNodeIds: EMPTY_ARRAY,
  });
};

const handleCancelFormNodesEdit = ({ getState, params, setState }) => {
  const { workflowActionsByFormId, actionScheduleByDay } = getState();
  const { formId } = params;

  const dayVsActionsMap = getDaysVsActionsMap({ formActions: workflowActionsByFormId[formId] });

  setState({
    isSorted: false,
    pendingFormActionsByFormId: {
      ...workflowActionsByFormId,
      [formId]: [...(_get(workflowActionsByFormId, formId) || EMPTY_ARRAY)],
    },
    actionScheduleByDay: { ...actionScheduleByDay, [formId]: { ...dayVsActionsMap } },
  });
};

const handleDeleteNode = ({ getState, setState, params }) => {
  const { formId } = params;
  const {
    selectedNodeDay,
    selectedNodeIndex,
    actionScheduleByDay,
    selectedNodeId,
    pendingFormActionsByFormId,
    selectedNodeIds,
  } = getState();

  const dayVsActionsMap = _get(actionScheduleByDay, formId, EMPTY_OBJECT);
  const reorderedActionNodes = [..._get(dayVsActionsMap, selectedNodeDay, EMPTY_ARRAY)];
  reorderedActionNodes.splice(selectedNodeIndex, 1);

  const actions = _get(pendingFormActionsByFormId, formId, EMPTY_ARRAY);
  const updatedDraftActions = _filter(actions, node => node?.nodeId !== selectedNodeId);

  // Remove the deleted node from selectedNodeIds if it was selected
  const updatedSelectedNodeIds = _filter(selectedNodeIds, nodeId => nodeId !== selectedNodeId);

  setState({
    actionScheduleByDay: {
      ...actionScheduleByDay,
      [formId]: { ...actionScheduleByDay[formId], [selectedNodeDay]: [...reorderedActionNodes] },
    },
    pendingFormActionsByFormId: {
      ...pendingFormActionsByFormId,
      [formId]: updatedDraftActions,
    },
    selectedNodeIds: updatedSelectedNodeIds,
    centralModal: DEFAULT_CENTRAL_MODAL_STATE,
  });
};

const handleReInitFormsEachDay = ({ getState, setState, params }) => {
  const { formId } = params;
  const { workflowActionsByFormId, actionScheduleByDay } = getState();

  const dayVsActionsMap = getDaysVsActionsMap({ formActions: workflowActionsByFormId[formId] });

  setState({
    actionScheduleByDay: { ...actionScheduleByDay, [formId]: { ...dayVsActionsMap } },
  });
};

const handlePublishActionNodes = async ({ setState, getState, params }) => {
  const { workflowActionsByFormId, formDataById, actionScheduleByDay } = getState();
  const { formId } = params;

  const actions = getOrderedActionsForForm(actionScheduleByDay[formId]);

  const areActionsDifferent = !_isEqual(
    actions,
    _get(formDataById, [formId, FORM_FIELD_TYPES.ACTION_NODE_CONFIGS, FORM_FIELD_TYPES.ACTIONS], EMPTY_ARRAY)
  );

  if (areActionsDifferent) {
    const updatedFormComponent = updateNodesInForm(actions, formDataById[formId]);

    const updatedGlobalForm = await updateFormComponent(updatedFormComponent); // put in global form

    setState({
      formDataById: { ...formDataById, [formId]: updatedGlobalForm },
    });
  }

  const dayVsActionsMap = getDaysVsActionsMap({ formActions: actions });

  setState({
    workflowActionsByFormId: {
      ...workflowActionsByFormId,
      [formId]: actions,
    },
    actionScheduleByDay: { ...actionScheduleByDay, [formId]: dayVsActionsMap },
    hasUnsavedFormChanges: false,
    isSorted: false,
  });
};

const handleCloseActionDrawer = ({ setState }) => {
  setState({
    entityId: '',
    actionDrawerMode: '',
    isWorkflowEditorOpen: false,
  });
};

const handleOpenAddActionDrawer = ({ setState }) => {
  const nodeId = uuid();

  setState({
    entityId: '',
    selectedNodeId: nodeId,
    actionDrawerMode: ACTION_DRAWER_MODES.ADD_NODE,
    isWorkflowEditorOpen: true,
  });
};

const handleOpenEditActionDrawer = ({ setState, params = EMPTY_OBJECT }) => {
  const { nodeId = '', entityDefId = '' } = params;

  setState({
    entityId: entityDefId,
    selectedNodeId: nodeId,
    actionDrawerMode: ACTION_DRAWER_MODES.EDIT_NODE,
    isWorkflowEditorOpen: true,
  });
};

export const handleAddActionNode = ({ params = EMPTY_OBJECT, getState }) => {
  const { pendingFormActionsByFormId = EMPTY_ARRAY } = getState();
  const { content = EMPTY_OBJECT, formId } = params;
  const { task } = content;

  const branchingKey = getBranchingKeyFromNodeData(task);

  const newNode = getNewNode({
    ...getNodeStructure(task),
    [branchingKey]: getConditionChild(task),
  });

  const updatedPendingFormActionsByFormId = {
    ...pendingFormActionsByFormId,
    [formId]: [
      ...(pendingFormActionsByFormId[formId] || EMPTY_ARRAY),
      { ...newNode, taskType: _get(newNode, ['uiMetadata', 'taskType']) },
    ],
  };

  return updatedPendingFormActionsByFormId;
};

export const handleUpdateActionNode = ({ getState, params = EMPTY_OBJECT }) => {
  const { selectedNodeId } = getState();
  const { node, formId, pendingFormActionsByFormId } = params;
  const nodeList = pendingFormActionsByFormId[formId];

  const updatedNodesList = nodeList.map(item => (item.nodeId === selectedNodeId ? node : item));

  const updatedPendingFormActionsByFormId = {
    ...pendingFormActionsByFormId,
    [formId]: updatedNodesList,
  };

  return updatedPendingFormActionsByFormId;
};

const handleSaveNodeData = ({ getState, setState, params }) => {
  const {
    actionDrawerMode,
    selectedNodeId,
    actionScheduleByDay,
    pendingFormActionsByFormId = EMPTY_OBJECT,
  } = getState();

  const {
    formId,
    entityId,
    childLabels,
    taskData: taskMetadata = EMPTY_OBJECT,
    formData = EMPTY_OBJECT,
    selectedTemplateOption = EMPTY_OBJECT,
    modalDetails = EMPTY_OBJECT,
  } = params;

  const selectedNode = findNodeDataFromActions({
    actions: pendingFormActionsByFormId[formId],
    nodeId: selectedNodeId,
    actionDrawerMode,
    entityId,
  });

  const oldFieldOptions = _get(modalDetails, 'uiMetadata.fieldOptions', EMPTY_ARRAY);
  const oldPropertiesByConditionTypes = _get(modalDetails, 'uiMetadata.optionsByConditionTypes', EMPTY_OBJECT);
  const taskData = _isEmpty(modalDetails) ? selectedNode : modalDetails;

  const uiMetadata = fetchUiMetadataAndSetTemplateOption({
    taskData: taskMetadata,
    modalDetails,
    formData,
    oldFieldOptions,
    oldPropertiesByConditionTypes,
    selectedTemplateOption,
    entityDefId: entityId,
  });

  let updatedPendingFormActionsByFormId = {};
  if (actionDrawerMode === ACTION_DRAWER_MODES.ADD_NODE) {
    const taskDetails = dataManager.getTask(entityId);

    const task = {
      ...taskData,
      ...formData,
      ...taskDetails,
      uiMetadata,
    };

    updatedPendingFormActionsByFormId = handleAddActionNode({
      getState,
      setState,
      params: {
        parentNode: selectedNode.node,
        content: {
          task,
          label: getLabel(
            _get(selectedNode, ['userData', 'data']),
            _get(dataManager, ['entityTaskData', entityId], EMPTY_OBJECT),
            _get(selectedNode, 'label', '')
          ),
          childLabels,
        },
        formId,
      },
    });
  } else if (actionDrawerMode === ACTION_DRAWER_MODES.EDIT_NODE) {
    const node = {
      ...selectedNode,
      ...taskData,
      ...formData,
      ...taskMetadata,
      label: getLabel(
        _get(selectedNode, ['userData', 'data']),
        _get(dataManager, ['entityTaskData', entityId], EMPTY_OBJECT),
        _get(taskMetadata, 'displayName', '')
      ),
      uiMetadata,
    };

    updatedPendingFormActionsByFormId = handleUpdateActionNode({
      getState,
      setState,
      params: {
        node,
        formId,
        entityId,
        pendingFormActionsByFormId,
      },
    });
  }

  const dayVsActionsMap = getDaysVsActionsMap({ formActions: updatedPendingFormActionsByFormId[formId] });

  setState({
    pendingFormActionsByFormId: updatedPendingFormActionsByFormId,
    actionScheduleByDay: { ...actionScheduleByDay, [formId]: dayVsActionsMap },
    isWorkflowEditorOpen: false,
  });
};

const handleDuplicateDayActions = ({ getState, setState, params }) => {
  const { formId, day, targetDay } = params;
  const { actionScheduleByDay, pendingFormActionsByFormId } = getState();

  const dayVsActionsMap = _get(actionScheduleByDay, formId, EMPTY_OBJECT);
  const dayActions = _get(dayVsActionsMap, day, EMPTY_ARRAY);

  if (_isEmpty(dayActions)) {
    setState({
      centralModal: DEFAULT_CENTRAL_MODAL_STATE,
    });
    return;
  }

  let duplicateToDay;
  if (targetDay >= 0 && targetDay !== day) {
    duplicateToDay = parseInt(targetDay, 10);
  }

  const duplicatedActions = getDuplicateActions(dayActions, targetDay);

  const updatedActionScheduleByDay = {
    ...actionScheduleByDay,
    [formId]: {
      ...actionScheduleByDay[formId],
      [duplicateToDay]: [
        ...(_get(actionScheduleByDay[formId], duplicateToDay, EMPTY_ARRAY) || EMPTY_ARRAY),
        ...duplicatedActions,
      ],
    },
  };

  const updatedFormActions = getOrderedActionsForForm(updatedActionScheduleByDay[formId]);
  const updatedPendingFormActionsByFormId = {
    ...pendingFormActionsByFormId,
    [formId]: updatedFormActions,
  };

  setState({
    actionScheduleByDay: updatedActionScheduleByDay,
    pendingFormActionsByFormId: updatedPendingFormActionsByFormId,
    centralModal: DEFAULT_CENTRAL_MODAL_STATE,
  });
};

const handleDeleteDayActions = ({ getState, setState, params }) => {
  const { formId, day } = params;
  const { actionScheduleByDay, pendingFormActionsByFormId } = getState();

  const dayVsActionsMap = _get(actionScheduleByDay, formId, EMPTY_OBJECT);

  if (!_has(dayVsActionsMap, day)) return;

  const updatedDayVsActionsMap = _omit(dayVsActionsMap, [day]);
  const updatedActionScheduleByDay = {
    ...actionScheduleByDay,
    [formId]: updatedDayVsActionsMap,
  };

  const updatedFormActions = getOrderedActionsForForm(updatedActionScheduleByDay[formId]);
  const updatedPendingFormActionsByFormId = {
    ...pendingFormActionsByFormId,
    [formId]: updatedFormActions,
  };

  setState({
    actionScheduleByDay: updatedActionScheduleByDay,
    pendingFormActionsByFormId: updatedPendingFormActionsByFormId,
    centralModal: DEFAULT_CENTRAL_MODAL_STATE,
  });
};

const handleToggleDuplicateDayModal = ({ getState, setState, params = EMPTY_OBJECT }) => {
  const { centralModal, isFormDayDuplicateModalOpen } = getState();
  const { day = '', formId = '' } = params;

  if (!centralModal.isVisible || centralModal.modalType !== MODAL_TYPES.DUPLICATE_DAY) {
    setState({
      isFormDayDuplicateModalOpen: !isFormDayDuplicateModalOpen,
      centralModal: {
        isVisible: true,
        modalType: MODAL_TYPES.DUPLICATE_DAY,
        modalData: { day, formId, isSubmitButtonDisabled: false, targetDay: null },
        isLoading: false,
      },
    });
  } else {
    setState({
      centralModal: DEFAULT_CENTRAL_MODAL_STATE,
    });
  }
};

const handleToggleDeleteDayActionsModal = ({ getState, setState, params = EMPTY_OBJECT }) => {
  const { centralModal, isFormDayDeleteModalOpen } = getState();
  const { day = '', formId = '' } = params;

  if (!centralModal.isVisible || centralModal.modalType !== MODAL_TYPES.DELETE_DAY) {
    setState({
      isFormDayDeleteModalOpen: !isFormDayDeleteModalOpen,
      centralModal: {
        isVisible: true,
        modalType: MODAL_TYPES.DELETE_DAY,
        modalData: { day, formId },
        isLoading: false,
      },
    });
  } else {
    setState({
      centralModal: DEFAULT_CENTRAL_MODAL_STATE,
    });
  }
};

const handleToggleNodeSelection = ({ getState, setState, params }) => {
  const { selectedNodeIds } = getState();
  const { nodeId } = params;

  setState({ selectedNodeIds: _xor(selectedNodeIds, [nodeId]) });
};

const handleClearNodeSelection = ({ setState }) => {
  setState({ selectedNodeIds: EMPTY_ARRAY });
};

const handleSelectAllNodes = ({ getState, setState }) => {
  const { actionScheduleByDay } = getState();
  const allNodeIds = getAllNodeIds(actionScheduleByDay);

  setState({ selectedNodeIds: allNodeIds });
};

const handleBulkDeleteSelectedNodes = ({ getState, setState }) => {
  const { actionScheduleByDay, pendingFormActionsByFormId, selectedNodeIds } = getState();
  const updatedActionScheduleByDay = removeNodesByIds(actionScheduleByDay, selectedNodeIds);
  const updatedPendingFormActionsByFormId = removeNodesFromPendingActions(pendingFormActionsByFormId, selectedNodeIds);

  setState({
    actionScheduleByDay: updatedActionScheduleByDay,
    pendingFormActionsByFormId: updatedPendingFormActionsByFormId,
    selectedNodeIds: EMPTY_ARRAY,
    centralModal: DEFAULT_CENTRAL_MODAL_STATE,
  });
};

const handleBulkEditDayNodes = ({ getState, setState, params }) => {
  const { actionScheduleByDay, selectedNodeIds, centralModal } = getState();
  const { selectedDay } = params;
  if (!selectedDay && selectedDay !== 0) {
    setState({ centralModal: { ...centralModal, hasError: true } });
    return;
  }
  const updatedActionScheduleByDay = moveNodesToDay(actionScheduleByDay, selectedNodeIds, selectedDay);

  setState({
    actionScheduleByDay: updatedActionScheduleByDay,
    selectedNodeIds: EMPTY_ARRAY,
    centralModal: DEFAULT_CENTRAL_MODAL_STATE,
  });
};

export const nodeActionHandlers = {
  handleToggleDeleteActionModal,
  handleSortActionNodes,
  handleNodeDuplication,
  handleBulkNodeDuplication,
  handleCancelFormNodesEdit,
  handleDeleteNode,
  handleReInitFormsEachDay,
  handlePublishActionNodes,
  handleCloseActionDrawer,
  handleOpenAddActionDrawer,
  handleOpenEditActionDrawer,
  handleAddActionNode,
  handleUpdateActionNode,
  handleSaveNodeData,
  handleDuplicateDayActions,
  handleDeleteDayActions,
  handleToggleDuplicateDayModal,
  handleToggleDeleteDayActionsModal,
  handleToggleNodeSelection,
  handleClearNodeSelection,
  handleSelectAllNodes,
  handleBulkDeleteSelectedNodes,
  handleBulkEditDayNodes,
};
