import { PRE_DEFINED_MODULES } from 'pages/WorkflowEngine/constants/ruleEngine.general';
import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

export const SALES_DEPARTMENT_ID = 'Sales';

export const DEFAULT_CENTRAL_MODAL_STATE = {
  isVisible: false,
  modalType: '',
  modalData: EMPTY_OBJECT,
  isLoading: false,
};

export const INITIAL_STATE = {
  availableFormTemplates: EMPTY_ARRAY,
  workflowNodeDataById: EMPTY_OBJECT,
  dealerWorkflowConfiguration: EMPTY_OBJECT,
  formDataById: EMPTY_OBJECT,
  dealerSettings: EMPTY_OBJECT,
  workflowActionsByFormId: EMPTY_OBJECT,
  formConfigurationById: EMPTY_OBJECT,
  actionScheduleByDay: EMPTY_OBJECT,
  selectedFormsByStageId: EMPTY_OBJECT,
  leadInactivityTimeoutByFormId: EMPTY_OBJECT,
  formPermissionsByConfigId: EMPTY_OBJECT,
  selectedModuleId: PRE_DEFINED_MODULES.CRM,
  departmentId: SALES_DEPARTMENT_ID,
  selectedNodeId: '',
  selectedFormId: '',
  selectedStageId: '',
  stageDrawerMode: '',
  actionDrawerMode: '',
  selectedNodeDay: '',
  selectedNodeIndex: null,
  selectedNodeData: EMPTY_OBJECT,
  isInitializingDealerView: true,
  hasUnsavedFormChanges: false,
  isStageCreationModalOpen: false,
  isStageOptionsMenuOpen: false,
  isFormDayDuplicateModalOpen: false,
  isFormDayDeleteModalOpen: false,
  isWorkflowEditorOpen: false,
  pendingFormActionsByFormId: EMPTY_OBJECT,
  isSorted: false,
  entityId: '',
  modalDetails: EMPTY_OBJECT,
  centralModal: DEFAULT_CENTRAL_MODAL_STATE,
};

const IMG_BASE_URL = 'https://com-tekioncloud-tap-assets.s3.us-west-1.amazonaws.com/assets/supportportal';

export const EMPTY_FORM_PLACEHOLDER_IMG_URL = `${IMG_BASE_URL}/empty_form_page_placeholder.svg`;

export const EMPTY_STAGE_PLACEHOLDER_IMG_URL = `${IMG_BASE_URL}/empty_stage_placeholder.svg`;

export const ADD_STAGE_BUTTON_TEXT = __('Add Stage');
export const DEALER_VIEW_HEADING = __('Dealer View Setup');
export const DEALER_WORKSPACE_HEADING_BY_MODULE = { [PRE_DEFINED_MODULES.CRM]: __('CRM Business Rules') };

export const NO_PERMISSIONS_MESSAGE = __('You do not have permission to access Dealer View Setup.');

export const STAGE_DRAWER_MODES = {
  EDIT: 'EDIT',
  ADD: 'ADD',
};

export const EVENT_HEADING = __('Event');
export const TITLE_HEADING = __('Title');
export const TEMPLATE_HEADING = __('Template');

export const ACTION_DRAWER_MODES = {
  ADD_NODE: 'ADD_NODE',
  EDIT_NODE: 'EDIT_NODE',
};

export const DIRECTIONS = {
  UP: 'UP',
  DOWN: 'DOWN',
};

export const SOLD_STAGE_ID = ['SOLD'];
export const APPOINTMENT_STAGE_IDS = ['APPOINTMENT_CREATED', 'UPDATED_WORKFLOW'];
