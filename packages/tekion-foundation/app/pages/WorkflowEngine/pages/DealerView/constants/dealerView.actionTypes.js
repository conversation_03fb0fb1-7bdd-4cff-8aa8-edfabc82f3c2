const INIT_DEALER_SETUP = 'INIT_DEALER_SETUP';
const HANDLE_ADD_FORM_SUBMIT = 'HANDLE_ADD_FORM_SUBMIT';
const HANDLE_CREATE_STAGE = 'HANDLE_CREATE_STAGE';
const CLOSE_DELETE_STAGE_MODAL = 'CLOSE_DELETE_STAGE_MODAL';
const CLOSE_DELETE_FORM_MODAL = 'CLOSE_DELETE_FORM_MODAL';
const CLOSE_STAGE_DRAWER = 'CLOSE_STAGE_DRAWER';
const OPEN_ADD_STAGE_DRAWER = 'OPEN_ADD_STAGE_DRAWER';
const TOGGLE_STAGE_KEBAB_MENU_MODAL = 'TOGGLE_STAGE_KEBAB_MENU_MODAL';
const HANDLE_CLICK_SAVE_STAGE = 'HANDLE_CLICK_SAVE_STAGE';
const OPEN_EDIT_STAGE_DRAWER = 'OPEN_EDIT_STAGE_DRAWER';
const TOGGLE_DELETE_STAGE_MODAL = 'TOGGLE_DELETE_STAGE_MODAL';
const TOGGLE_DELETE_FORM_MODAL = 'TOGGLE_DELETE_FORM_MODAL';
const HANDLE_DELETE_STAGE = 'HANDLE_DELETE_STAGE';
const HANDLE_DELETE_FORM = 'HANDLE_DELETE_FORM';
const HANDLE_CLICK_FORM_NAME = 'HANDLE_CLICK_FORM_NAME';
const HANDLE_FORM_SELECTION = 'HANDLE_FORM_SELECTION';
const HANDLE_STAGE_NAME_CHANGE = 'HANDLE_STAGE_NAME_CHANGE';
const HANDLE_OPEN_DELETE_FORM_MODAL = 'HANDLE_OPEN_DELETE_FORM_MODAL';
const SORT_FORMS = 'SORT_FORMS';
const SET_IS_FORM_EDITED = 'SET_IS_FORM_EDITED';
const INIT_ACTIONS_FOR_FORMS = 'INIT_ACTIONS_FOR_FORMS';
const HANDLE_NODE_DUPLICATION = 'HANDLE_NODE_DUPLICATION';
const DELETE_NODE = 'DELETE_NODE';
const SORT_ACTION_NODE_CARDS = 'SORT_ACTION_NODE_CARDS';
const PUBLISH_ACTION_NODES = 'PUBLISH_ACTION_NODES';
const CANCEL_EDIT_ACTION_NODES = 'CANCEL_EDIT_ACTION_NODES';
const RE_INIT_FORMS_PER_DAYS = 'RE_INIT_FORMS_PER_DAYS';
const OPEN_EDIT_ACTION_DRAWER = 'OPEN_EDIT_ACTION_DRAWER';
const SAVE_NODE_DRAWER_DATA = 'SAVE_NODE_DRAWER_DATA';
const OPEN_ADD_ACTION_DRAWER = 'OPEN_ADD_ACTION_DRAWER';
const CLOSE_ACTION_DRAWER = 'CLOSE_ACTION_DRAWER';
const TOGGLE_DELETE_ACTION_MODAL = 'TOGGLE_DELETE_ACTION_MODAL';
const TOGGLE_DUPLICATE_DAY_MODAL = 'TOGGLE_DUPLICATE_DAY_MODAL';
const TOGGLE_DELETE_DAY_ACTIONS_MODAL = 'TOGGLE_DELETE_DAY_ACTIONS_MODAL';
const DUPLICATE_DAY_ACTIONS = 'DUPLICATE_DAY_ACTIONS';
const DELETE_DAY_ACTIONS = 'DELETE_DAY_ACTIONS';
const MOVE_STAGE = 'MOVE_STAGE';
const CHANGE_INACTIVE_AFTER_DAYS = 'CHANGE_INACTIVE_AFTER_DAYS';

const SHOW_CENTRAL_MODAL = 'SHOW_CENTRAL_MODAL';
const HIDE_CENTRAL_MODAL = 'HIDE_CENTRAL_MODAL';
const SET_CENTRAL_MODAL_LOADING = 'SET_CENTRAL_MODAL_LOADING';

const SET_DRAFT_FORM_ACTIONS = 'SET_DRAFT_FORM_ACTIONS';
const UPDATE_DRAFT_FORM_ACTIONS = 'UPDATE_DRAFT_FORM_ACTIONS';
const SET_MODAL_DETAILS = 'SET_MODAL_DETAILS';
const HANDLE_ENTITY_ID_CHANGE = 'HANDLE_ENTITY_ID_CHANGE';

const ACTION_TYPES = {
  HANDLE_NODE_DUPLICATION,
  DELETE_NODE,
  INIT_DEALER_SETUP,
  HANDLE_ADD_FORM_SUBMIT,
  HANDLE_CREATE_STAGE,
  CLOSE_DELETE_STAGE_MODAL,
  CLOSE_DELETE_FORM_MODAL,
  CLOSE_STAGE_DRAWER,
  OPEN_ADD_STAGE_DRAWER,
  TOGGLE_STAGE_KEBAB_MENU_MODAL,
  HANDLE_CLICK_SAVE_STAGE,
  OPEN_EDIT_STAGE_DRAWER,
  TOGGLE_DELETE_STAGE_MODAL,
  TOGGLE_DELETE_FORM_MODAL,
  HANDLE_DELETE_STAGE,
  HANDLE_DELETE_FORM,
  HANDLE_CLICK_FORM_NAME,
  HANDLE_FORM_SELECTION,
  HANDLE_STAGE_NAME_CHANGE,
  SORT_FORMS,
  SET_IS_FORM_EDITED,
  HANDLE_OPEN_DELETE_FORM_MODAL,
  INIT_ACTIONS_FOR_FORMS,
  SORT_ACTION_NODE_CARDS,
  PUBLISH_ACTION_NODES,
  CANCEL_EDIT_ACTION_NODES,
  RE_INIT_FORMS_PER_DAYS,
  OPEN_EDIT_ACTION_DRAWER,
  SAVE_NODE_DRAWER_DATA,
  OPEN_ADD_ACTION_DRAWER,
  CLOSE_ACTION_DRAWER,
  TOGGLE_DELETE_ACTION_MODAL,
  TOGGLE_DUPLICATE_DAY_MODAL,
  DUPLICATE_DAY_ACTIONS,
  DELETE_DAY_ACTIONS,
  TOGGLE_DELETE_DAY_ACTIONS_MODAL,
  MOVE_STAGE,
  CHANGE_INACTIVE_AFTER_DAYS,
  SHOW_CENTRAL_MODAL,
  HIDE_CENTRAL_MODAL,
  SET_CENTRAL_MODAL_LOADING,
  SET_DRAFT_FORM_ACTIONS,
  UPDATE_DRAFT_FORM_ACTIONS,
  SET_MODAL_DETAILS,
  HANDLE_ENTITY_ID_CHANGE,
};

export default ACTION_TYPES;
