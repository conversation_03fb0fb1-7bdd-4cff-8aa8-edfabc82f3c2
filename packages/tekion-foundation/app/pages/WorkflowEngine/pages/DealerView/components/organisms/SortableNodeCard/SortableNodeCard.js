import React, { useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import _get from 'lodash/get';
import _noop from 'lodash/noop';
import _defaultsDeep from 'lodash/defaultsDeep';
import _isEmpty from 'lodash/isEmpty';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import isStringEmpty from '@tekion/tekion-base/utils/isStringEmpty';
import { dataManager } from '@tekion/tekion-rule-engine';
import Icon from '@tekion/tekion-components/src/atoms/FontIcon';
import IconAsBtn from '@tekion/tekion-components/src/atoms/iconAsBtn';

import { getEntityDefId, getExecutionDelayTimeFromNode } from 'shared/ruleEngine/ruleEngine.utils';

import SortableNodeContent from './SortableNodeContent';
import { ENTITY_DEF_IDS_VS_ICONS } from './SortableNodecards.constants';
import ACTION_TYPES from '../../../constants/dealerView.actionTypes';
import { DragIcon, SortableItem } from '../../atoms/Sortable/Sortable';
import { getColorForEntity, getFieldTitleAndValue } from './helpers/sortableNodecard.helpers';
import { getProcessDataFromWorkflow } from '../../../../../helpers/processAutomation.general';
import { getCardDataFromActivity } from '../../../../../helpers/processAutomation.activityUtils';

import styles from './sortableNodeCard.module.scss';

const SortableNodeCard = ({
  onAction,
  index,
  id,
  node,
  day,
  iconClassName = '',
  actionScheduleByDay,
  pendingFormActionsByFormId,
  selectedModuleId,
  containerClassName,
  formMetadata,
}) => {
  const entityDefId = useMemo(() => getEntityDefId(node), [node]);
  const defaultNodeData = _get(formMetadata, ['defaultNodeData', entityDefId], EMPTY_OBJECT);
  const nodeData = _defaultsDeep({}, node, defaultNodeData);

  const processData = useMemo(() => getProcessDataFromWorkflow(nodeData), [nodeData]);
  const { isDelayOrdered = true } = processData;

  const { label, description } = useMemo(
    () => getCardDataFromActivity(nodeData, dataManager.getTask(entityDefId), selectedModuleId),
    [nodeData, entityDefId, selectedModuleId]
  );

  const color = getColorForEntity(entityDefId);

  const onNodeAction = useCallback(
    type => {
      onAction({
        type: ACTION_TYPES[type],
        payload: {
          formId: id,
          index,
          day,
          actionScheduleByDay,
          nodeData,
          pendingFormActionsByFormId,
          entityDefId,
          nodeId: nodeData.nodeId,
        },
      });
    },
    [onAction, id, index, day, actionScheduleByDay, nodeData, pendingFormActionsByFormId, entityDefId]
  );

  const handleToggleDeleteModal = useCallback(
    () => onNodeAction(ACTION_TYPES.TOGGLE_DELETE_ACTION_MODAL),
    [onNodeAction]
  );

  const handleEdit = useCallback(() => onNodeAction(ACTION_TYPES.OPEN_EDIT_ACTION_DRAWER), [onNodeAction]);

  const handleDuplicate = useCallback(() => onNodeAction(ACTION_TYPES.HANDLE_NODE_DUPLICATION), [onNodeAction]);

  const assigneeDetails = getFieldTitleAndValue(nodeData, entityDefId);
  const isAssigneeValid = !_isEmpty(assigneeDetails) && !isStringEmpty(assigneeDetails.value);
  const { title: assigneeTitle, value: assigneeName } = assigneeDetails;

  return (
    <SortableItem
      sortable
      key={`item-${index}`}
      index={index}
      disabled={false}
      collection={isDelayOrdered ? getExecutionDelayTimeFromNode(nodeData) : null}>
      <div
        className={`${styles.card} ${containerClassName}`}
        style={{ borderLeftColor: color }}
        tabIndex="-1"
        role="button">
        <div className={styles.dragIconWrapper}>
          <DragIcon />
        </div>
        <div className={`${styles.activityIconContainer} ${iconClassName}`}>
          <Icon className={styles.entityIcon} style={{ backgroundColor: color }}>
            {ENTITY_DEF_IDS_VS_ICONS[entityDefId]}
          </Icon>
        </div>
        <SortableNodeContent
          label={label}
          description={description}
          entityDefId={entityDefId}
          isPreview={false}
          node={nodeData}
        />
        {isAssigneeValid && <div className={styles.receiverField}>{`${assigneeTitle} : ${assigneeName}`}</div>}
        <div className={styles.iconGroup}>
          <IconAsBtn containerClassName={styles.utilIcon} onClick={handleEdit}>
            icon-edit
          </IconAsBtn>
          <IconAsBtn containerClassName={styles.utilIcon} onClick={handleDuplicate}>
            icon-copy
          </IconAsBtn>
          <IconAsBtn containerClassName={styles.utilIcon} onClick={handleToggleDeleteModal}>
            icon-trash-thick
          </IconAsBtn>
        </div>
      </div>
    </SortableItem>
  );
};

SortableNodeCard.propTypes = {
  day: PropTypes.number,
  onAction: PropTypes.func,
  index: PropTypes.number,
  id: PropTypes.string,
  pendingFormActionsByFormId: PropTypes.object,
  node: PropTypes.object,
  iconClassName: PropTypes.string,
  containerClassName: PropTypes.string,
  selectedModuleId: PropTypes.string,
  formMetadata: PropTypes.object,
  actionScheduleByDay: PropTypes.object,
};

SortableNodeCard.defaultProps = {
  day: null,
  onAction: _noop,
  index: null,
  id: '',
  pendingFormActionsByFormId: EMPTY_OBJECT,
  node: EMPTY_OBJECT,
  iconClassName: '',
  containerClassName: '',
  selectedModuleId: '',
  formMetadata: EMPTY_OBJECT,
  actionScheduleByDay: EMPTY_OBJECT,
};

export default React.memo(SortableNodeCard);
