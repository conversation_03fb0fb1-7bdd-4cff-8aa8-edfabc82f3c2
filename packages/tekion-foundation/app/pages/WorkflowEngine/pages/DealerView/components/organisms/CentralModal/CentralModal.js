import React, { useCallback, useState } from 'react';
import _noop from 'lodash/noop';
import PropTypes from 'prop-types';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import Modal from '@tekion/tekion-components/src/molecules/Modal';
import Content from '@tekion/tekion-components/src/atoms/Content';

import { MODAL_TYPES, MODAL_CONFIGS } from './constants/centralModal.constants';

import styles from './centralModal.module.scss';

const CentralModal = props => {
  const { onAction, modalType, isVisible, modalData, isLoading, hasError } = props;
  const [additionalData, setAdditionalData] = useState(EMPTY_OBJECT);

  const config = MODAL_CONFIGS[modalType] || MODAL_CONFIGS[MODAL_TYPES.NONE];

  const handleClose = useCallback(() => {
    if (config.closeAction) {
      onAction({
        type: config.closeAction,
        payload: modalData,
      });
    }
  }, [onAction, config.closeAction, modalData]);

  const handleSubmit = useCallback(() => {
    const payload = { ...modalData, ...additionalData };
    if (config.submitAction) {
      onAction({
        type: config.submitAction,
        payload,
      });
    }
  }, [onAction, config.submitAction, modalData, additionalData]);

  const renderContent = useCallback(() => {
    if (config.customRenderer) {
      return config.customRenderer(modalData, setAdditionalData, hasError);
    }

    const bodyText = typeof config.bodyText === 'function' ? config.bodyText(modalData) : config.bodyText;

    return (
      <div className={styles.modalContent}>
        {bodyText && <Content>{bodyText}</Content>}
        {config.confirmationText && <Content>{config.confirmationText}</Content>}
      </div>
    );
  }, [config, modalData, hasError]);

  if (!modalType || modalType === MODAL_TYPES.NONE) {
    return null;
  }

  const submitButtonProps = {
    className: config.destructive ? styles.destructiveButton : styles.primaryButton,
  };

  return (
    <Modal
      visible={isVisible}
      destroyOnClose
      title={config.title}
      submitBtnText={config.submitButtonText}
      secondaryBtnText={config.secondaryBtnText || __('Cancel')}
      onCancel={handleClose}
      onSubmit={handleSubmit}
      okButtonProps={submitButtonProps}
      width={config.width || Modal.SIZES.SM}
      primaryBtnDisabled={modalData?.isSubmitButtonDisabled}
      loading={isLoading}>
      {renderContent()}
    </Modal>
  );
};

CentralModal.propTypes = {
  onAction: PropTypes.func,
  modalType: PropTypes.string,
  isVisible: PropTypes.bool,
  modalData: PropTypes.object,
  isLoading: PropTypes.bool,
  hasError: PropTypes.bool,
};

CentralModal.defaultProps = {
  onAction: _noop,
  modalType: MODAL_TYPES.NONE,
  isVisible: false,
  modalData: EMPTY_OBJECT,
  isLoading: false,
  hasError: false,
};

export default React.memo(CentralModal);
