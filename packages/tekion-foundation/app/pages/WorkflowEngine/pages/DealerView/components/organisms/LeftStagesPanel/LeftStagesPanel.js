import React, { useCallback, useMemo } from 'react';
import _noop from 'lodash/noop';
import PropTypes from 'prop-types';

import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import StageSection from './components/StageSection';
import AddStageDrawer from '../../../widgets/AddStage';
import ACTION_TYPES from '../../../constants/dealerView.actionTypes';
import { STAGE_DRAWER_MODES } from '../../../constants/dealerView.constants';
import { getAssetLifecycleJourneyStages } from '../../../helpers/dealerView.helpers';
import { STAGE_DRAWER_HEADER } from './constants/leftStagePanel.constants';

import styles from './leftStagesPanel.module.scss';

const LeftStagesPanel = props => {
  const {
    selectedStageId,
    onAction,
    stageName,
    formDataById,
    formIdPendingDeletion,
    selectedFormId,
    dealerWorkflowConfiguration,
    isStageCreationModalOpen,
    isStageOptionsMenuOpen,
    stageDrawerMode,
    selectedFormsByStageId,
    availableFormTemplates,
    formConfigurationById,
    isDealerViewSetup,
    isFormLoading,
  } = props;

  const toggleAddStageDrawer = useCallback(() => {
    onAction({
      type: ACTION_TYPES.OPEN_ADD_STAGE_DRAWER,
      payload: { stageDrawerMode: STAGE_DRAWER_MODES.ADD },
    });
  }, [onAction]);

  const renderAddStageDrawer = useCallback(
    () => (
      <AddStageDrawer
        onAction={onAction}
        header={STAGE_DRAWER_HEADER}
        stageName={stageName}
        formConfigurationById={formConfigurationById}
        formIdPendingDeletion={formIdPendingDeletion}
        toggleAddStageDrawer={toggleAddStageDrawer}
        isStageCreationModalOpen={isStageCreationModalOpen}
        selectedStageId={selectedStageId}
        dealerWorkflowConfiguration={dealerWorkflowConfiguration}
        selectedFormsByStageId={selectedFormsByStageId}
        stageDrawerMode={stageDrawerMode}
        availableFormTemplates={availableFormTemplates}
      />
    ),
    [
      onAction,
      stageName,
      formConfigurationById,
      formIdPendingDeletion,
      toggleAddStageDrawer,
      isStageCreationModalOpen,
      selectedStageId,
      dealerWorkflowConfiguration,
      selectedFormsByStageId,
      stageDrawerMode,
      availableFormTemplates,
    ]
  );

  const stages = useMemo(
    () => getAssetLifecycleJourneyStages(dealerWorkflowConfiguration),
    [dealerWorkflowConfiguration]
  );

  return (
    <div className={styles.sider}>
      <div className={styles.menu}>
        <StageSection
          onAction={onAction}
          handleAddStage={toggleAddStageDrawer}
          stages={stages}
          formConfigurationById={formConfigurationById}
          formDataById={formDataById}
          isStageOptionsMenuOpen={isStageOptionsMenuOpen}
          dealerWorkflowConfiguration={dealerWorkflowConfiguration}
          selectedFormId={selectedFormId}
          selectedStageId={selectedStageId}
          isStageCreationModalOpen={isStageCreationModalOpen}
          selectedFormsByStageId={selectedFormsByStageId}
          availableFormTemplates={availableFormTemplates}
          isDealerViewSetup={isDealerViewSetup}
          isFormLoading={isFormLoading}
        />
      </div>

      {renderAddStageDrawer()}
    </div>
  );
};

LeftStagesPanel.propTypes = {
  stageName: PropTypes.string,
  formIdPendingDeletion: PropTypes.string,
  onAction: PropTypes.func,
  selectedStageId: PropTypes.string,
  dealerWorkflowConfiguration: PropTypes.object,
  formConfigurationById: PropTypes.object,
  selectedFormId: PropTypes.string,
  formDataById: PropTypes.object,
  isStageCreationModalOpen: PropTypes.bool,
  isStageOptionsMenuOpen: PropTypes.bool,
  stageDrawerMode: PropTypes.string,
  selectedFormsByStageId: PropTypes.object,
  availableFormTemplates: PropTypes.array,
  isDealerViewSetup: PropTypes.bool,
  isFormLoading: PropTypes.bool,
};

LeftStagesPanel.defaultProps = {
  stageName: '',
  formIdPendingDeletion: '',
  onAction: _noop,
  selectedStageId: '',
  formDataById: EMPTY_OBJECT,
  dealerWorkflowConfiguration: EMPTY_OBJECT,
  formConfigurationById: EMPTY_OBJECT,
  selectedFormId: '',
  isStageCreationModalOpen: false,
  isStageOptionsMenuOpen: false,
  stageDrawerMode: '',
  selectedFormsByStageId: EMPTY_OBJECT,
  availableFormTemplates: EMPTY_ARRAY,
  isDealerViewSetup: false,
  isFormLoading: false,
};

export default React.memo(LeftStagesPanel);
