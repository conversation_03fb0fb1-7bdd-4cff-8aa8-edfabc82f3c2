@use "@tekion/tekion-styles-next/scss/component.scss" as c;

.cardContainer {
  min-width: 0;
  padding: 0.4rem 2.4rem;
}

.card {
  @include c.flex($align-items: center);
  background-color: c.$white;
  margin: 1.2rem 2.4rem;
  padding: 1.2rem 0;
  flex-wrap: wrap;
  height: 4.8rem;

  .dragIconWrapper {
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s;
  }

  &:hover {
    border: 0.1rem solid c.$denim;

    .dragIconWrapper {
      opacity: 1;
      pointer-events: auto;
    }

    .iconGroup {
      opacity: 1;
      pointer-events: auto;
    }
  }
}

.entityIcon {
  @include c.flex($justify-content: center, $align-items: center);
  height: 2.4rem;
  width: 2.4rem;
  border-radius: 50%;
  font-size: 1.4rem;
  color: c.$white;
}

.iconGroup {
  display: flex;
  gap: 0.4rem;
  margin-left: auto;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
}

.activityIconContainer {
  @include c.flex($justify-content: center, $align-items: center);
  height: 2.4rem;
  width: 2.4rem;
  border-radius: 50%;
  margin: auto 1rem;
}

.divider {
  margin: 0 1.2rem;
  border-right: c.$border-base-width c.$border-base-type c.$platinum;
  height: 1.6rem;
}

.eyeIcon {
  color: c.$ashGray;
}

.parentIconsContainer {
  @include c.flex();
  padding-right: 0.4rem;
  > div {
    padding: 0 0.4rem;
  }
}

.utilIcon {
  color: c.$atomic;
  margin: auto 0.8rem;
}

.dividerLeft {
  border-left: c.$border-base-width c.$border-base-type c.$platinum;
  line-height: 1.6rem;
}

.listKey {
  color: c.$atomic;
  margin-right: 0.4rem;
}

.listContainer {
  column-count: 2;
  margin-left: 4.4rem;
}

.cardContent {
  width: 24rem;
}

.contentContainer {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  margin-right: 8px;
}

.receiverField {
  display: flex;
  align-items: center;
  margin: 0 1.2rem 0 1.2rem;
}
