import React, { useCallback, useEffect, useMemo } from 'react';
import _get from 'lodash/get';
import _has from 'lodash/has';
import _noop from 'lodash/noop';
import _size from 'lodash/size';
import _find from 'lodash/find';
import _isEqual from 'lodash/isEqual';
import PropTypes from 'prop-types';

import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import isStringEmpty from '@tekion/tekion-base/utils/isStringEmpty';
import Page from '@tekion/tekion-components/src/molecules/pageComponent/PageComponent';
import { dataManager } from '@tekion/tekion-rule-engine';

import FormContainerDrawer from 'shared/ruleEngine/components/FormContainerDrawer';
import RenderNodes from './components/RenderNodes';
import SortableNodeCard from '../SortableNodeCard';
import EmptyNodePage from './components/EmptyNodePage';
import FormPageFooter from './components/FormPageFooter';
import FormPageHeader from './components/FormPageHeader';
import ACTION_TYPES from '../../../constants/dealerView.actionTypes';
import { ACTION_DRAWER_MODES } from '../../../constants/dealerView.constants';

import styles from './formPage.module.scss';

const FormPage = props => {
  const {
    onAction,
    id: formId,
    formDataById,
    hasUnsavedFormChanges,
    dealerWorkflowConfiguration,
    selectedNodeId,
    workflowActionsByFormId,
    availableFormTemplates,
    actionDrawerMode,
    selectedStageId,
    formConfigurationById,
    selectedModuleId,
    actionScheduleByDay,
    selectedNodeData,
    selectedNodeDay,
    selectedNodeIndex,
    isWorkflowEditorOpen,
    formPermissionsByConfigId,
    leadInactivityTimeoutByFormId,
    pendingFormActionsByFormId,
    isFormDayDeleteModalOpen,
    isFormDayDuplicateModalOpen,
    isSorted,
    entityId,
    modalDetails,
  } = props;

  useEffect(() => {
    if (!_has(pendingFormActionsByFormId, formId) || isSorted) {
      onAction({
        type: ACTION_TYPES.SET_DRAFT_FORM_ACTIONS,
        payload: { pendingFormActionsByFormId: workflowActionsByFormId },
      });
    } else {
      const isFormComponentEdited = !_isEqual(workflowActionsByFormId[formId], pendingFormActionsByFormId[formId]);
      onAction({
        type: ACTION_TYPES.SET_IS_FORM_EDITED,
        payload: { isFormComponentEdited },
      });
    }
  }, [pendingFormActionsByFormId, entityId, formId, workflowActionsByFormId, isSorted, onAction]);

  useEffect(() => {
    if (isWorkflowEditorOpen && !isStringEmpty(selectedNodeId)) {
      const action = _find(pendingFormActionsByFormId[formId], { nodeId: selectedNodeId });
      const userData = _get(action, 'userData', EMPTY_OBJECT);

      const modalDetailsObject = dataManager.getTask(entityId);

      onAction({
        type: ACTION_TYPES.SET_MODAL_DETAILS,
        payload: { modalDetails: { ...modalDetailsObject, userData } },
      });
    }
  }, [isWorkflowEditorOpen, selectedNodeId, actionDrawerMode, entityId, formId, pendingFormActionsByFormId, onAction]);

  const nodesList = useMemo(() => pendingFormActionsByFormId[formId], [pendingFormActionsByFormId, formId]);

  const dayVsActionsMap = useMemo(() => _get(actionScheduleByDay, formId, EMPTY_OBJECT), [formId, actionScheduleByDay]);

  const closeAddActionDrawer = useCallback(() => {
    onAction({
      type: ACTION_TYPES.CLOSE_ACTION_DRAWER,
    });
  }, [onAction]);

  const handleEntityIdChange = useCallback(
    entityId => {
      onAction({
        type: ACTION_TYPES.HANDLE_ENTITY_ID_CHANGE,
        payload: { entityId },
      });
    },
    [onAction]
  );

  const handleSaveNode = useCallback(
    payload =>
      onAction({
        type: ACTION_TYPES.SAVE_NODE_DRAWER_DATA,
        payload: {
          ...payload,
          entityId,
          taskData: dataManager.getTask(entityId),
          modalDetails,
          formId,
          pendingFormActionsByFormId,
        },
      }),
    [pendingFormActionsByFormId, entityId, formId, modalDetails, onAction]
  );

  const formMetadata = useMemo(
    () => _get(formPermissionsByConfigId, formId, EMPTY_OBJECT),
    [formPermissionsByConfigId, formId]
  );

  const renderNodeCard = useCallback(
    (node, index, day, restProps = EMPTY_OBJECT) => (
      <SortableNodeCard
        {...restProps}
        key={`item-${index}`}
        onAction={onAction}
        selectedNodeIndex={selectedNodeIndex}
        id={formId}
        index={index}
        day={day}
        node={node}
        selectedNodeDay={selectedNodeDay}
        entityId={entityId}
        pendingFormActionsByFormId={pendingFormActionsByFormId}
        selectedNodeData={selectedNodeData}
        dealerWorkflowConfiguration={dealerWorkflowConfiguration}
        rawData={node}
        dayVsActionsMap={dayVsActionsMap}
        actionScheduleByDay={actionScheduleByDay}
        actionDrawerMode={actionDrawerMode}
        selectedNodeId={selectedNodeId}
        selectedModuleId={selectedModuleId}
        formMetadata={formMetadata}
        isWorkflowEditorOpen={isWorkflowEditorOpen}
      />
    ),
    [
      onAction,
      selectedNodeIndex,
      formId,
      selectedNodeDay,
      entityId,
      pendingFormActionsByFormId,
      selectedNodeData,
      dealerWorkflowConfiguration,
      dayVsActionsMap,
      actionScheduleByDay,
      actionDrawerMode,
      selectedNodeId,
      selectedModuleId,
      formMetadata,
      isWorkflowEditorOpen,
    ]
  );

  const renderFormContainerDrawer = useMemo(
    () => (
      <FormContainerDrawer
        formId={formId}
        formDataById={formDataById}
        actionScheduleByDay={actionScheduleByDay}
        visible={isWorkflowEditorOpen}
        onClose={closeAddActionDrawer}
        onSubmit={handleSaveNode}
        onAction={onAction}
        entityId={entityId}
        handleEntityIdChange={handleEntityIdChange}
        values={modalDetails}
        modalDetails={modalDetails}
        actionDrawerMode={actionDrawerMode}
        pendingFormActionsByFormId={pendingFormActionsByFormId}
        selectedNodeId={selectedNodeId}
        selectedNodeDay={selectedNodeDay}
        formMetadata={formMetadata}
        loading={actionDrawerMode === ACTION_DRAWER_MODES.EDIT_NODE && isStringEmpty(entityId)}
      />
    ),
    [
      formId,
      formDataById,
      actionScheduleByDay,
      isWorkflowEditorOpen,
      closeAddActionDrawer,
      handleSaveNode,
      onAction,
      entityId,
      handleEntityIdChange,
      modalDetails,
      actionDrawerMode,
      pendingFormActionsByFormId,
      selectedNodeId,
      selectedNodeDay,
      formMetadata,
    ]
  );

  return (
    <Page>
      <Page.Header>
        <FormPageHeader
          formId={formId}
          onAction={onAction}
          selectedNodeId={selectedNodeId}
          availableFormTemplates={availableFormTemplates}
          actionDrawerMode={actionDrawerMode}
          isWorkflowEditorOpen={isWorkflowEditorOpen}
        />
      </Page.Header>

      <Page.Body>
        <div class={styles.formPageBodyContainer}>
          {!_size(nodesList) ? (
            <EmptyNodePage
              onAction={onAction}
              actionDrawerMode={actionDrawerMode}
              isWorkflowEditorOpen={isWorkflowEditorOpen}
            />
          ) : (
            <RenderNodes
              formId={formId}
              onAction={onAction}
              selectedStageId={selectedStageId}
              dayVsActionsMap={dayVsActionsMap}
              selectedNodeData={selectedNodeData}
              selectedNodeIndex={selectedNodeIndex}
              selectedNodeDay={selectedNodeDay}
              pendingFormActionsByFormId={pendingFormActionsByFormId}
              renderNodeCard={renderNodeCard}
              isFormDayDeleteModalOpen={isFormDayDeleteModalOpen}
              isFormDayDuplicateModalOpen={isFormDayDuplicateModalOpen}
            />
          )}
        </div>
      </Page.Body>
      <Page.Footer>
        <FormPageFooter
          formId={formId}
          formDataById={formDataById}
          onAction={onAction}
          isSorted={isSorted}
          formConfigurationById={formConfigurationById}
          hasUnsavedFormChanges={hasUnsavedFormChanges}
          workflowActionsByFormId={workflowActionsByFormId}
          actionScheduleByDay={actionScheduleByDay}
          pendingFormActionsByFormId={pendingFormActionsByFormId}
          leadInactivityTimeoutByFormId={leadInactivityTimeoutByFormId}
        />
      </Page.Footer>

      {isWorkflowEditorOpen && renderFormContainerDrawer}
    </Page>
  );
};

FormPage.propTypes = {
  onAction: PropTypes.func,
  id: PropTypes.string,
  actionDrawerMode: PropTypes.string,
  selectedNodeId: PropTypes.string,
  selectedModuleId: PropTypes.string,
  selectedStageId: PropTypes.string,
  workflowNodeDataById: PropTypes.object,
  dealerWorkflowConfiguration: PropTypes.object,
  workflowActionsByFormId: PropTypes.object,
  formDataById: PropTypes.object,
  formConfigurationById: PropTypes.object,
  actionScheduleByDay: PropTypes.object,
  formPermissionsByConfigId: PropTypes.object,
  availableFormTemplates: PropTypes.array,
  selectedNodeDay: PropTypes.string,
  selectedNodeIndex: PropTypes.number,
  selectedNodeData: PropTypes.object,
  leadInactivityTimeoutByFormId: PropTypes.object,
  hasUnsavedFormChanges: PropTypes.bool,
  isWorkflowEditorOpen: PropTypes.bool,
  pendingFormActionsByFormId: PropTypes.object,
  isFormDayDeleteModalOpen: PropTypes.bool,
  isFormDayDuplicateModalOpen: PropTypes.bool,
  isSorted: PropTypes.bool,
  entityId: PropTypes.string,
  modalDetails: PropTypes.object,
};

FormPage.defaultProps = {
  onAction: _noop,
  id: '',
  selectedNodeId: '',
  selectedModuleId: '',
  workflowNodeDataById: EMPTY_OBJECT,
  dealerWorkflowConfiguration: EMPTY_OBJECT,
  actionDrawerMode: '',
  selectedStageId: '',
  workflowActionsByFormId: EMPTY_OBJECT,
  formDataById: EMPTY_OBJECT,
  formConfigurationById: EMPTY_OBJECT,
  actionScheduleByDay: EMPTY_OBJECT,
  formPermissionsByConfigId: EMPTY_OBJECT,
  leadInactivityTimeoutByFormId: EMPTY_OBJECT,
  availableFormTemplates: EMPTY_ARRAY,
  hasUnsavedFormChanges: false,
  selectedNodeDay: '',
  selectedNodeIndex: null,
  selectedNodeData: EMPTY_OBJECT,
  isFormDayDeleteModalOpen: false,
  isFormDayDuplicateModalOpen: false,
  isWorkflowEditorOpen: false,
  pendingFormActionsByFormId: EMPTY_OBJECT,
  isSorted: false,
  entityId: '',
  modalDetails: EMPTY_OBJECT,
};

export default React.memo(FormPage);
