import React, { useCallback, useEffect } from 'react';
import PropTypes from 'prop-types';
import _noop from 'lodash/noop';

import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import isStringEmpty from '@tekion/tekion-base/utils/isStringEmpty';

import FormPage from '../FormPage';
import EmptyRightPanel from './components/EmptyRightPanel';
import ACTION_TYPES from '../../../constants/dealerView.actionTypes';

import styles from './rightSidePanel.module.scss';

const RightSidePanel = props => {
  const {
    selectedFormId,
    workflowActionsByFormId,
    onAction,
    workflowNodeDataById,
    dealerWorkflowConfiguration,
    selectedNodeId,
    availableFormTemplates,
    selectedModuleId,
    actionDrawerMode,
    formDataById,
    hasUnsavedFormChanges,
    selectedStageId,
    formConfigurationById,
    actionScheduleByDay,
    selectedNodeDay,
    selectedNodeData,
    selectedNodeIndex,
    isActionDeleteConfirmationOpen,
    isFormDayDeleteModalOpen,
    isFormDayDuplicateModalOpen,
    isWorkflowEditorOpen,
    formPermissionsByConfigId,
    leadInactivityTimeoutByFormId,
    pendingFormActionsByFormId,
    isSorted,
    entityId,
    modalDetails,
    selectedNodeIds,
    isFormLoading,
  } = props;

  useEffect(() => {
    if (!selectedFormId) return;

    const alreadyFetched = Object.prototype.hasOwnProperty.call(workflowActionsByFormId, selectedFormId);

    if (alreadyFetched) {
      onAction({
        type: ACTION_TYPES.RE_INIT_FORMS_PER_DAYS,
        payload: { formId: selectedFormId },
      });
    } else {
      onAction({
        type: ACTION_TYPES.INIT_ACTIONS_FOR_FORMS,
      });
    }
  }, [onAction, selectedFormId, workflowActionsByFormId]);

  const handleCreateStage = useCallback(
    () =>
      onAction({
        type: ACTION_TYPES.OPEN_ADD_STAGE_DRAWER,
      }),
    [onAction]
  );

  return (
    <div className={styles.rightPanelContainer}>
      {isStringEmpty(selectedFormId) ? (
        <EmptyRightPanel handleCreateStage={handleCreateStage} />
      ) : (
        <FormPage
          id={selectedFormId}
          dealerWorkflowConfiguration={dealerWorkflowConfiguration}
          onAction={onAction}
          hasUnsavedFormChanges={hasUnsavedFormChanges}
          selectedNodeId={selectedNodeId}
          workflowNodeDataById={workflowNodeDataById}
          workflowActionsByFormId={workflowActionsByFormId}
          availableFormTemplates={availableFormTemplates}
          actionDrawerMode={actionDrawerMode}
          selectedModuleId={selectedModuleId}
          formDataById={formDataById}
          selectedNodeData={selectedNodeData}
          selectedNodeIndex={selectedNodeIndex}
          selectedNodeDay={selectedNodeDay}
          selectedStageId={selectedStageId}
          isFormDayDeleteModalOpen={isFormDayDeleteModalOpen}
          isFormDayDuplicateModalOpen={isFormDayDuplicateModalOpen}
          isActionDeleteConfirmationOpen={isActionDeleteConfirmationOpen}
          formConfigurationById={formConfigurationById}
          actionScheduleByDay={actionScheduleByDay}
          leadInactivityTimeoutByFormId={leadInactivityTimeoutByFormId}
          formPermissionsByConfigId={formPermissionsByConfigId}
          isWorkflowEditorOpen={isWorkflowEditorOpen}
          pendingFormActionsByFormId={pendingFormActionsByFormId}
          isSorted={isSorted}
          entityId={entityId}
          modalDetails={modalDetails}
          selectedNodeIds={selectedNodeIds}
          isFormLoading={isFormLoading}
        />
      )}
    </div>
  );
};

RightSidePanel.propTypes = {
  onAction: PropTypes.func,
  workflowNodeDataById: PropTypes.object,
  dealerWorkflowConfiguration: PropTypes.object,
  actionDrawerMode: PropTypes.string,
  selectedNodeId: PropTypes.string,
  formConfigurationById: PropTypes.object,
  actionScheduleByDay: PropTypes.object,
  formDataById: PropTypes.object,
  formPermissionsByConfigId: PropTypes.object,
  selectedFormId: PropTypes.string,
  workflowActionsByFormId: PropTypes.object,
  availableFormTemplates: PropTypes.array,
  selectedModuleId: PropTypes.string,
  selectedNodeDay: PropTypes.string,
  selectedStageId: PropTypes.string,
  selectedNodeIndex: PropTypes.number,
  selectedNodeData: PropTypes.object,
  leadInactivityTimeoutByFormId: PropTypes.object,
  hasUnsavedFormChanges: PropTypes.bool,
  isActionDeleteConfirmationOpen: PropTypes.bool,
  isFormDayDeleteModalOpen: PropTypes.bool,
  isFormDayDuplicateModalOpen: PropTypes.bool,
  isWorkflowEditorOpen: PropTypes.bool,
  pendingFormActionsByFormId: PropTypes.object,
  isSorted: PropTypes.bool,
  isFormLoading: PropTypes.bool,
  entityId: PropTypes.string,
  modalDetails: PropTypes.object,
  selectedNodeIds: PropTypes.array,
};

RightSidePanel.defaultProps = {
  onAction: _noop,
  workflowNodeDataById: EMPTY_OBJECT,
  dealerWorkflowConfiguration: EMPTY_OBJECT,
  workflowActionsByFormId: EMPTY_OBJECT,
  actionScheduleByDay: EMPTY_OBJECT,
  formDataById: EMPTY_OBJECT,
  selectedStageId: '',
  formConfigurationById: EMPTY_OBJECT,
  formPermissionsByConfigId: EMPTY_OBJECT,
  selectedFormId: '',
  selectedNodeId: '',
  actionDrawerMode: '',
  selectedModuleId: '',
  availableFormTemplates: EMPTY_ARRAY,
  selectedNodeDay: '',
  selectedNodeIndex: null,
  selectedNodeData: EMPTY_OBJECT,
  leadInactivityTimeoutByFormId: EMPTY_OBJECT,
  hasUnsavedFormChanges: false,
  isActionDeleteConfirmationOpen: false,
  isFormDayDuplicateModalOpen: false,
  isFormDayDeleteModalOpen: false,
  isWorkflowEditorOpen: false,
  pendingFormActionsByFormId: EMPTY_OBJECT,
  isSorted: false,
  isFormLoading: false,
  entityId: '',
  modalDetails: EMPTY_OBJECT,
  selectedNodeIds: EMPTY_ARRAY,
};

export default React.memo(RightSidePanel);
