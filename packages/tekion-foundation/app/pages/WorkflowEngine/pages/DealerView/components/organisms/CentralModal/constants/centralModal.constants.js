import React from 'react';

import _size from 'lodash/size';

import ACTION_TYPES from '../../../../constants/dealerView.actionTypes';
import DuplicateDayModalContent from '../components/DuplicateDayModalContent';
import EditDayModalContent from '../components/EditDayModalContent';

/**
 * Modal Types - Enum for different modal configurations
 */
export const MODAL_TYPES = {
  NONE: 'NONE',
  DELETE_STAGE: 'DELETE_STAGE',
  DELETE_FORM: 'DELETE_FORM',
  DELETE_ACTION: 'DELETE_ACTION',
  CONFIRM_NAVIGATION: 'CONFIRM_NAVIGATION',
  SAVE_CHANGES: 'SAVE_CHANGES',
  DUPLICATE_DAY: 'DUPLICATE_DAY',
  DELETE_DAY: 'DELETE_DAY',
  BULK_DELETE_NODES: 'BULK_DELETE_NODES',
  BULK_EDIT_DAY: 'BULK_EDIT_DAY',
};

/**
 * Modal Configurations - Centralized configuration for all modal types
 *
 * Each configuration includes:
 * - title: Modal title text
 * - bodyText: Main message content
 * - confirmationText: Additional confirmation text (optional)
 * - submitButtonText: Text for the submit button
 * - closeAction: Action type to dispatch when modal is closed
 * - submitAction: Action type to dispatch when modal is submitted
 * - destructive: Whether this is a destructive action (affects button styling)
 * - width: Modal width (optional, defaults to SM)
 * - customRenderer: Custom content renderer function (optional)
 */

export const MODAL_CONFIGS = {
  [MODAL_TYPES.NONE]: {
    title: '',
    bodyText: '',
    submitButtonText: '',
    closeAction: null,
    submitAction: null,
    destructive: false,
  },

  [MODAL_TYPES.DELETE_STAGE]: {
    title: __('Delete Stage'),
    bodyText: __('Deleting this stage will also delete all associated forms. This action cannot be undone.'),
    confirmationText: __('Are you sure you want to proceed?'),
    submitButtonText: __('Delete'),
    closeAction: ACTION_TYPES.CLOSE_DELETE_STAGE_MODAL,
    submitAction: ACTION_TYPES.HANDLE_DELETE_STAGE,
    destructive: true,
  },

  [MODAL_TYPES.DELETE_FORM]: {
    title: __('Delete Form'),
    bodyText: __('This action cannot be undone.'),
    confirmationText: __('Are you sure you want to proceed?'),
    submitButtonText: __('Delete'),
    closeAction: ACTION_TYPES.CLOSE_DELETE_FORM_MODAL,
    submitAction: ACTION_TYPES.HANDLE_DELETE_FORM,
    destructive: true,
  },

  [MODAL_TYPES.DELETE_ACTION]: {
    title: __('Delete Action'),
    bodyText: __('Are you sure you want to delete task(s)?'),
    submitButtonText: __('Delete'),
    closeAction: ACTION_TYPES.TOGGLE_DELETE_ACTION_MODAL,
    submitAction: ACTION_TYPES.DELETE_NODE,
    destructive: true,
  },

  [MODAL_TYPES.DUPLICATE_DAY]: {
    title: __('Duplicate Day'),
    submitButtonText: __('Duplicate'),
    closeAction: ACTION_TYPES.TOGGLE_DUPLICATE_DAY_MODAL,
    submitAction: ACTION_TYPES.DUPLICATE_DAY_ACTIONS,
    destructive: false,
    customRenderer: modalData => <DuplicateDayModalContent modalData={modalData} />,
  },

  [MODAL_TYPES.DELETE_DAY]: {
    title: __('Delete Day'),
    bodyText: __('Are you sure you want to delete all task(s) for the current day?'),
    submitButtonText: __('Delete'),
    closeAction: ACTION_TYPES.TOGGLE_DELETE_DAY_ACTIONS_MODAL,
    submitAction: ACTION_TYPES.DELETE_DAY_ACTIONS,
    destructive: true,
  },

  [MODAL_TYPES.BULK_DELETE_NODES]: {
    title: __('Delete Action'),
    bodyText: modalData => {
      const count = _size(modalData?.selectedNodeIds) || 0;
      return __('This action cannot be undone. Are you sure you want to delete {{count}} {{taskLabel}}? ', {
        count,
        taskLabel: count === 1 ? __('task') : __('tasks'),
      });
    },
    submitButtonText: __('Yes, Delete'),
    secondaryBtnText: __('No, Cancel'),
    closeAction: ACTION_TYPES.HIDE_CENTRAL_MODAL,
    submitAction: ACTION_TYPES.BULK_DELETE_SELECTED_NODES,
    destructive: true,
  },

  [MODAL_TYPES.BULK_EDIT_DAY]: {
    title: __('Edit Day'),
    submitButtonText: __('Save'),
    closeAction: ACTION_TYPES.HIDE_CENTRAL_MODAL,
    submitAction: ACTION_TYPES.BULK_EDIT_DAY_NODES,
    destructive: false,
    customRenderer: (modalData, setAdditionalData, hasError) => (
      <EditDayModalContent modalData={modalData} setAdditionalData={setAdditionalData} hasError={hasError} />
    ),
  },
};

export const getModalConfig = modalType => MODAL_CONFIGS[modalType] || MODAL_CONFIGS[MODAL_TYPES.NONE];

export const createModalData = (type, data = {}) => ({
  modalType: type,
  ...data,
});

export const createShowModalAction = (modalType, modalData = {}) => ({
  type: ACTION_TYPES.SHOW_CENTRAL_MODAL,
  payload: {
    modalType,
    modalData,
  },
});

export const createHideModalAction = () => ({
  type: ACTION_TYPES.HIDE_CENTRAL_MODAL,
});
