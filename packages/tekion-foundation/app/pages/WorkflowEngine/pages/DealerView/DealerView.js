import React, { useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import _noop from 'lodash/noop';

import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import Heading from '@tekion/tekion-components/src/atoms/Heading';
import Loader from '@tekion/tekion-components/src/molecules/loader';
import withActions from '@tekion/tekion-components/src/connectors/withActions';
import Page from '@tekion/tekion-components/src/molecules/pageComponent/PageComponent';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import PermissionPlaceHolder from '@tekion/tekion-widgets/src/organisms/communications/atoms/permissionPlaceHolder';
import IconAsBtn from '@tekion/tekion-components/src/atoms/iconAsBtn';
import FontIcon, { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';

import LeftStagesPanel from './components/organisms/LeftStagesPanel';
import RightSidePanel from './components/organisms/RightSidePanel';
import CentralModal from './components/organisms/CentralModal';
import ACTION_HANDLERS from './helpers/actionHandlers';
import { checkPermissions } from '../ProcessAutomationList/helpers/processAutomationList.helpers';
import ACTION_TYPES from './constants/dealerView.actionTypes';
import LIST_ACTION_TYPES from '../ProcessAutomationList/constants/processAutomationList.actionTypes';

import {
  DEALER_VIEW_HEADING,
  NO_PERMISSIONS_MESSAGE,
  INITIAL_STATE,
  DEALER_WORKSPACE_HEADING_BY_MODULE,
} from './constants/dealerView.constants';

import styles from './dealerView.module.scss';

const DealerView = props => {
  const {
    onAction,
    navigate,
    isInitializingDealerView,
    stageName,
    formIdPendingDeletion,
    permissions,
    dealerWorkflowConfiguration,
    formDataById,
    workflowNodeDataById,
    hasUnsavedFormChanges,
    departmentId,
    selectedNodeId,
    selectedFormId,
    availableFormTemplates,
    stageDrawerMode,
    selectedStageId,
    workflowActionsByFormId,
    actionDrawerMode,
    selectedModuleId,
    formConfigurationById,
    redirectToListPage,
    isStageCreationModalOpen,
    isFormDayDuplicateModalOpen,
    isFormDayDeleteModalOpen,
    isStageOptionsMenuOpen,
    selectedNodeDay,
    selectedNodeIndex,
    selectedNodeData,
    selectedFormsByStageId,
    actionScheduleByDay,
    isWorkflowEditorOpen,
    formPermissionsByConfigId,
    isWorkflowEngineCentralViewEnabled,
    leadInactivityTimeoutByFormId,
    isDealerViewSetup,
    centralModal,
    pendingFormActionsByFormId,
    isSorted,
    entityId,
    modalDetails,
    parentOnAction,
  } = props;

  useEffect(() => {
    onAction({ type: ACTION_TYPES.INIT_DEALER_SETUP });
  }, [onAction]);

  const goBackHandler = useCallback(() => {
    redirectToListPage();
  }, [redirectToListPage]);

  const onModuleSelectorClick = useCallback(() => {
    parentOnAction({ type: LIST_ACTION_TYPES.OPEN_MODULE_DRAWER });
  }, [parentOnAction]);

  const renderHeader = useCallback(() => {
    const workspaceHeading = DEALER_WORKSPACE_HEADING_BY_MODULE[selectedModuleId];

    return (
      <Page.Header
        className={styles.pageHeader}
        navigate={navigate}
        hasBack={isDealerViewSetup}
        goBackHandler={goBackHandler}>
        <PropertyControlledComponent
          controllerProperty={!isDealerViewSetup}
          fallback={
            <Heading size={1} className={styles.pageHeading}>
              {DEALER_VIEW_HEADING}
            </Heading>
          }>
          <div className="d-flex align-items-center justify-content-between m-x-16 full-width">
            <div className={`d-flex align-items-center ${styles.headerLeftContent}`}>
              <IconAsBtn onClick={onModuleSelectorClick}>icon-hamburger-menu1</IconAsBtn>
              <Heading size={1}>{workspaceHeading}</Heading>
            </div>
            <div className={`d-flex align-items-center ${styles.headerRightContent}`}>
              <Heading size={3}>{__('Learn More')}</Heading>
              <FontIcon size={SIZES.L}>icon-unknown</FontIcon>
            </div>
          </div>
        </PropertyControlledComponent>
      </Page.Header>
    );
  }, [navigate, isDealerViewSetup, goBackHandler, onModuleSelectorClick, selectedModuleId]);

  if (!isWorkflowEngineCentralViewEnabled && isDealerViewSetup) {
    return <PermissionPlaceHolder label={NO_PERMISSIONS_MESSAGE} />;
  }

  return (
    <Page className={styles.page}>
      {renderHeader()}
      <PropertyControlledComponent controllerProperty={!isInitializingDealerView} fallback={<Loader />}>
        <PropertyControlledComponent
          controllerProperty={checkPermissions(permissions, selectedModuleId)}
          fallback={<PermissionPlaceHolder label={NO_PERMISSIONS_MESSAGE} />}>
          <Page.Body className="flex full-width full-height">
            <div className={`flex full-width full-height ${styles.panelContainer}`}>
              <LeftStagesPanel
                stageName={stageName}
                selectedStageId={selectedStageId}
                onAction={onAction}
                formDataById={formDataById}
                selectedFormId={selectedFormId}
                permissions={permissions}
                formConfigurationById={formConfigurationById}
                selectedFormsByStageId={selectedFormsByStageId}
                dealerWorkflowConfiguration={dealerWorkflowConfiguration}
                isStageCreationModalOpen={isStageCreationModalOpen}
                isStageOptionsMenuOpen={isStageOptionsMenuOpen}
                stageDrawerMode={stageDrawerMode}
                availableFormTemplates={availableFormTemplates}
                formIdPendingDeletion={formIdPendingDeletion}
                isDealerViewSetup={isDealerViewSetup}
              />
              <RightSidePanel
                selectedFormId={selectedFormId}
                onAction={onAction}
                selectedStageId={selectedStageId}
                dealerWorkflowConfiguration={dealerWorkflowConfiguration}
                workflowNodeDataById={workflowNodeDataById}
                hasUnsavedFormChanges={hasUnsavedFormChanges}
                selectedNodeId={selectedNodeId}
                formConfigurationById={formConfigurationById}
                isStageCreationModalOpen={isStageCreationModalOpen}
                isFormDayDuplicateModalOpen={isFormDayDuplicateModalOpen}
                isFormDayDeleteModalOpen={isFormDayDeleteModalOpen}
                workflowActionsByFormId={workflowActionsByFormId}
                availableFormTemplates={availableFormTemplates}
                selectedModuleId={selectedModuleId}
                departmentId={departmentId}
                actionDrawerMode={actionDrawerMode}
                formDataById={formDataById}
                selectedNodeData={selectedNodeData}
                selectedNodeIndex={selectedNodeIndex}
                selectedNodeDay={selectedNodeDay}
                actionScheduleByDay={actionScheduleByDay}
                leadInactivityTimeoutByFormId={leadInactivityTimeoutByFormId}
                formPermissionsByConfigId={formPermissionsByConfigId}
                isWorkflowEditorOpen={isWorkflowEditorOpen}
                pendingFormActionsByFormId={pendingFormActionsByFormId}
                isSorted={isSorted}
                entityId={entityId}
                modalDetails={modalDetails}
              />
            </div>
          </Page.Body>
        </PropertyControlledComponent>
      </PropertyControlledComponent>
      <CentralModal
        onAction={onAction}
        modalType={centralModal.modalType}
        isVisible={centralModal.isVisible}
        modalData={centralModal.modalData}
        isLoading={centralModal.isLoading}
      />
    </Page>
  );
};

DealerView.propTypes = {
  onAction: PropTypes.func,
  navigate: PropTypes.func,
  isInitializingDealerView: PropTypes.bool,
  stageName: PropTypes.string,
  formIdPendingDeletion: PropTypes.string,
  permissions: PropTypes.object,
  dealerWorkflowConfiguration: PropTypes.object,
  workflowNodeDataById: PropTypes.object,
  selectedNodeId: PropTypes.string,
  selectedFormId: PropTypes.string,
  redirectToListPage: PropTypes.func,
  departmentId: PropTypes.string,
  selectedModuleId: PropTypes.string,
  availableFormTemplates: PropTypes.array,
  workflowActionsByFormId: PropTypes.object,
  stageDrawerMode: PropTypes.string,
  actionDrawerMode: PropTypes.string,
  selectedStageId: PropTypes.string,
  selectedNodeDay: PropTypes.string,
  selectedNodeIndex: PropTypes.number,
  selectedNodeData: PropTypes.object,
  hasUnsavedFormChanges: PropTypes.bool,
  isStageCreationModalOpen: PropTypes.bool,
  isFormDayDuplicateModalOpen: PropTypes.bool,
  isFormDayDeleteModalOpen: PropTypes.bool,
  formConfigurationById: PropTypes.object,
  isStageOptionsMenuOpen: PropTypes.bool,
  selectedFormsByStageId: PropTypes.object,
  actionScheduleByDay: PropTypes.object,
  leadInactivityTimeoutByFormId: PropTypes.object,
  formDataById: PropTypes.object,
  isWorkflowEditorOpen: PropTypes.bool,
  formPermissionsByConfigId: PropTypes.object,
  isWorkflowEngineCentralViewEnabled: PropTypes.bool,
  isDealerViewSetup: PropTypes.bool,
  centralModal: PropTypes.object,
  pendingFormActionsByFormId: PropTypes.object,
  isSorted: PropTypes.bool,
  entityId: PropTypes.string,
  modalDetails: PropTypes.object,
  parentOnAction: PropTypes.func,
};

DealerView.defaultProps = {
  onAction: _noop,
  navigate: _noop,
  isInitializingDealerView: false,
  stageName: '',
  formIdPendingDeletion: '',
  redirectToListPage: _noop,
  permissions: EMPTY_OBJECT,
  dealerWorkflowConfiguration: EMPTY_OBJECT,
  workflowNodeDataById: EMPTY_OBJECT,
  availableFormTemplates: EMPTY_ARRAY,
  workflowActionsByFormId: EMPTY_OBJECT,
  formDataById: EMPTY_OBJECT,
  formPermissionsByConfigId: EMPTY_OBJECT,
  departmentId: '',
  selectedNodeId: '',
  selectedFormId: '',
  selectedStageId: '',
  stageDrawerMode: '',
  actionDrawerMode: '',
  selectedModuleId: '',
  selectedNodeDay: '',
  selectedNodeIndex: null,
  selectedNodeData: EMPTY_OBJECT,
  hasUnsavedFormChanges: false,
  isStageCreationModalOpen: false,
  isFormDayDuplicateModalOpen: false,
  isFormDayDeleteModalOpen: false,
  isStageOptionsMenuOpen: false,
  formConfigurationById: EMPTY_OBJECT,
  selectedFormsByStageId: EMPTY_OBJECT,
  actionScheduleByDay: EMPTY_OBJECT,
  leadInactivityTimeoutByFormId: EMPTY_OBJECT,
  isWorkflowEditorOpen: false,
  isWorkflowEngineCentralViewEnabled: false,
  isDealerViewSetup: false,
  centralModal: EMPTY_OBJECT,
  pendingFormActionsByFormId: EMPTY_OBJECT,
  isSorted: false,
  entityId: '',
  modalDetails: EMPTY_OBJECT,
  parentOnAction: _noop,
};

export default withActions(INITIAL_STATE, ACTION_HANDLERS)(DealerView);
